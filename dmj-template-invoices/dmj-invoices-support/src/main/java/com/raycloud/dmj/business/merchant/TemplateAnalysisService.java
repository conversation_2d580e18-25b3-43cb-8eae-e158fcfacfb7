package com.raycloud.dmj.business.merchant;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.deliver.DeliverPrintParams;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.EnumFieldValueName;
import com.raycloud.dmj.domain.pt.templatemapping.DeliverTemplateMapping;
import com.raycloud.dmj.domain.pt.enums.EnumInvoicesType;
import com.raycloud.dmj.domain.pt.enums.MerchantCodePrintSortEnum;
import com.raycloud.dmj.domain.pt.templatemapping.GetterTemplateMapping;
import com.raycloud.dmj.domain.pt.templatemapping.MerchantCodeTemplateMapping;
import com.raycloud.dmj.domain.pt.wlb.FieldValue;
import com.raycloud.dmj.domain.trades.PrintConfig;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeConfigUtils;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.getter.base.GetterPrintParams;
import com.raycloud.dmj.merchantCode.BO.DangkouReconcileTemplateBO;
import com.raycloud.dmj.merchantCode.BO.MerchantCodeTemplateBO;
import com.raycloud.dmj.merchantCode.BO.SendTradeTemplateBO;
import com.raycloud.dmj.merchantCode.base.MerchantCodePrintSource;
import com.raycloud.dmj.merchantCode.base.MerchantCodeSortField;
import com.raycloud.dmj.merchantCode.base.MerchantCodeSortTypeEnum;
import com.raycloud.dmj.merchantCode.util.MerchantPrintConfigUtils;
import com.raycloud.dmj.refund.BO.RefundTemplateBO;
import com.raycloud.dmj.repair.params.PrintRepairParams;
import com.raycloud.dmj.service.api.template.ITemplateAnalysisService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.helper.PrintTemplateHelper;
import com.raycloud.dmj.services.helper.wlb.TemplateHelper;
import com.raycloud.dmj.services.print.PrintHelperService;
import com.raycloud.dmj.services.print.utils.PrintMerchantCodeUtils;
import com.raycloud.dmj.services.pt.IUserDeliverTemplateService;
import com.raycloud.dmj.services.pt.IUserGetterTemplateService;
import com.raycloud.dmj.services.pt.IUserInvoicesTemplateService;
import com.raycloud.dmj.services.trades.IPrintConfigService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.template.base.TemplateFieldsDescription;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商家编码模板business
 *
 * @author: qingfeng.cxb
 * @create: 2020-06-23 20:37
 */
@Service("templateAnalysisService")
public class TemplateAnalysisService implements ITemplateAnalysisService {

    @Resource
    private IUserInvoicesTemplateService userInvoicesTemplateService;
    @Resource
    private IWmsService wmsService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    private IUserGetterTemplateService userGetterTemplateService;
    @Resource
    private IUserDeliverTemplateService userDeliverTemplateService;
    @Resource
    private PrintHelperService printHelperService;
    @Resource
    private IPrintConfigService printConfigService;

    /**
     * 默认排序
     */
    private static final Integer SORT_DEFAULT = 0;
    /**
     * 商家编码排序,拿货单\消退入库单是先主编码再规格编码
     * 发货单-出货单交叉结构:按货号排序
     */
    private static final Integer SORT_ITEM_OUTER_ID = 1;

    /**
     * 先商家编码再商品名称,拿货单是先主编码再规格备注
     * 发货单-出货单交叉结构:先按货号再按颜色排序
     */
    private static final Integer SORT_SYS_SKU_NAME = 2;

    /**
     * 拿货单-先主编码再规格名称
     */
    private static final Integer SORT_OUTER_ID_SKU_NAME = 3;

    /**
     * 拿货单-先商品备注再规格名称
     */
    private static final Integer SORT_ITEM_REMARK_SKU_NAME = 8;
    /**
     * 拿货单-先按货位编码再按主商家编码排序
     */
    private static final Integer SORT_ITEM_SECTION_CODE_ITEM_OUTER_ID = 9;

    /**
     * 拿货单-规格编码
     */
    private static final Integer SORT_SKU_OUTER_ID = 10;

    /**
     * 拿货单-规格备注
     */
    private static final Integer SORT_SKU_REMARK = 20;

    /**
     * 拿货单-商品货位编码
     */
    private static final Integer SORT_ITEM_SECTION_CODE = 21;

    /**
     * 拿货单-商品数量(降序),因为要降序所以特殊处理
     */
    private static final Integer SORT_DELIVER_NUM_ITEM_OUTER_ID = 11;

    /**
     * 拿货单目录-供应商编码排序
     */
    private static final Integer SORT_SUPPLIER_CODE = 12;

    /**
     * 发货单-拣货货位编码排序
     */
    private static final Integer PICK_SECITON = 16;

    /**
     * 发货单-商品简称排序
     */
    private static final Integer ITEM_SHORT_TITLE = 23;

    /**
     * 拿货单交叉结构-商家编码、颜色排序
     */
    private static final Integer SORT_OUTER_ID_PROP_COLOR = 13;

    /**
     * 消退入库单 上架货位排序 优先选良品上架货位参与排序
     */
    private static final Integer REFUND_SORT_SECTION_CODE = 2;

    private static final Map<Integer, String> repairSortMap = Maps.newHashMap();

    private static final Map<Integer, String> getterSortMap = Maps.newHashMap();

    private static final Map<Integer, String> deliverCustomSortMap = Maps.newHashMap();

    private static final Map<Integer, String> deliverCrossSortMap = Maps.newHashMap();

    static {
        // 目前这样写死,之后自定义排序
        repairSortMap.put(SORT_DEFAULT, "");
        repairSortMap.put(SORT_ITEM_OUTER_ID, "table_repair_item_code");
        repairSortMap.put(SORT_SYS_SKU_NAME, "table_repair_item_code,table_repair_item_name");
        // 排序字段
        getterSortMap.put(SORT_DEFAULT, "");
        getterSortMap.put(SORT_ITEM_OUTER_ID, "sortItemOuterId,sortSkuOuterId");
        getterSortMap.put(SORT_OUTER_ID_SKU_NAME, "sortItemOuterId,sortItemSpecification");
        getterSortMap.put(SORT_ITEM_REMARK_SKU_NAME, "sortItemRemark,sortItemSpecification");
        getterSortMap.put(SORT_SKU_OUTER_ID, "sortSkuOuterId");
        getterSortMap.put(SORT_SYS_SKU_NAME, "sortItemOuterId,sortSkuRemark");
        getterSortMap.put(SORT_SKU_REMARK, "sortSkuRemark");
        getterSortMap.put(SORT_ITEM_SECTION_CODE_ITEM_OUTER_ID, "item_section_code,sortItemOuterId,sortSkuOuterId");
        getterSortMap.put(SORT_ITEM_SECTION_CODE, "item_section_code");
        getterSortMap.put(SORT_DELIVER_NUM_ITEM_OUTER_ID, "sortNum,sortSkuOuterId,sortItemOuterId");
        getterSortMap.put(SORT_SUPPLIER_CODE, "supplierCode");
        getterSortMap.put(SORT_OUTER_ID_PROP_COLOR, "outer_id_of_item,prop_color");
        // 发货单排序
        deliverCustomSortMap.put(SORT_DEFAULT, "");
        deliverCustomSortMap.put(SORT_ITEM_OUTER_ID, "sortItemOuterId,sortSkuOuterId");
        deliverCustomSortMap.put(SORT_SYS_SKU_NAME, "sortItemOuterId,sortSkuRemark");
        deliverCustomSortMap.put(SORT_OUTER_ID_SKU_NAME, "sortItemOuterId,sortItemSpecification");
        deliverCustomSortMap.put(SORT_ITEM_REMARK_SKU_NAME, "sortItemRemark,sortItemSpecification");
        deliverCustomSortMap.put(SORT_SKU_OUTER_ID, "sortSkuOuterId");
        deliverCustomSortMap.put(PICK_SECITON, "sortSectionCode");
        deliverCustomSortMap.put(SORT_SKU_REMARK, "sortSkuRemark");
        deliverCustomSortMap.put(SORT_ITEM_SECTION_CODE, "item_section_num");
        deliverCustomSortMap.put(ITEM_SHORT_TITLE, "sortItemShortTitle");
        // 发货单-出货单(交叉结构)排序
        deliverCrossSortMap.put(SORT_DEFAULT, "");
        deliverCrossSortMap.put(SORT_ITEM_OUTER_ID, "outer_id_of_item");
        deliverCrossSortMap.put(SORT_SYS_SKU_NAME, "outer_id_of_item,table_prop_color_name");

    }

    /**
     * 解析模板
     */
    @Override
    public MerchantCodeTemplateBO analysisMerchantTemplate(Staff staff, Long templateId, String defaultFields) {
        return analysisMerchantTemplate(staff, templateId, defaultFields, null, null, null);
    }

    /**
     * 解析模板,有分隔模板ID
     */
    @Override
    public MerchantCodeTemplateBO analysisMerchantTemplate(Staff staff, Long templateId, String defaultFields, Long separateTemplateId, MerchantCodePrintSource source, String isPrint) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        UserInvoicesTemplate userInvoicesTemplate = getInvoicesTemplate(staff, templateId, EnumInvoicesType.MERCHANTCODE);
        //得到 模板设计的数据框的字段
        List<String> needValues = TemplateHelper.getNeedValues(userInvoicesTemplate.getFields());
        MerchantCodeTemplateBO merchantCodeTemplateBO = new MerchantCodeTemplateBO();
        merchantCodeTemplateBO.setUserInvoicesTemplate(userInvoicesTemplate);
        merchantCodeTemplateBO.setTemplateId(templateId);
        merchantCodeTemplateBO.setTemplateName(userInvoicesTemplate.getName());
        merchantCodeTemplateBO.setMerchantCodePrintSource(source);
        merchantCodeTemplateBO.setIsPrint(isPrint);
        merchantCodeTemplateBO.setFieldValues(userInvoicesTemplate.getFieldValues());
        if (separateTemplateId == null && (MerchantCodePrintSource.CAIGOU_MANAGE.equals(source) || MerchantCodePrintSource.PURCHASE_UNIQUE.equals(source))) {
            // 目前只有采购那边读配置,采购打商家编码和采购打唯一码
            PrintConfig printConfig = printConfigService.getPrintConfigBtStaff(staff);
            separateTemplateId = MerchantPrintConfigUtils.getSeparateTemplateId(printConfig);
        }
        if (separateTemplateId != null && separateTemplateId > 0) {
            // 获取打印分隔码所选的模板
            UserInvoicesTemplate separateInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, separateTemplateId, false, EnumInvoicesType.MERCHANTCODE);
            Assert.isTrue(separateInvoicesTemplate != null, "解析模板时未获取到打印分隔码的模板信息!");
            List<String> separateNeedValues = TemplateHelper.getNeedValues(separateInvoicesTemplate.getFields());
            if (CollectionUtils.isNotEmpty(separateNeedValues)) {
                // 合并并去重需要的值
                needValues.addAll(separateNeedValues);
                needValues = needValues.stream().distinct().collect(Collectors.toList());
            }
            merchantCodeTemplateBO.setSeparateTemplateId(separateTemplateId);
            merchantCodeTemplateBO.setSeparateInvoicesTemplate(separateInvoicesTemplate);
        }
        if (StringUtils.isNotBlank(defaultFields)) {
            for (String field : defaultFields.split(",")) {
                if (!needValues.contains(field)) {
                    needValues.add(field);
                }
            }
        }
        merchantCodeTemplateBO.setNeedValues(needValues);
        //得到排序方式 且 把排序需要用到的数据写到needValues里
        String sectionSort = PrintMerchantCodeUtils.handleGoodsSectionSort(userInvoicesTemplate);
        merchantCodeTemplateBO.setSectionSort(sectionSort);
        String printSortPicker = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.PRINT_SORT_PICKER.getValue()); //是否按拣选路径参与排序
        String suitSectionCodeSort = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.SUIT_ORDER_SECTION_CODE_SORT.getValue()); //是否套件按子件拣选货位参与排序
        String groupSectionCodeSort = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.GROUP_ORDER_SECTION_CODE_SORT.getValue()); //是否组合商品按子件商品货位参与排序
        String excludeTradeTagIds = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.EXCLUDE_TRADE_TAG_IDS.getValue()); //不打印指定订单标签
        String orderRemarkAddOuterId = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.ORDER_REMARK_ADD_OUTER_ID.getValue()); //不打印指定订单标签
        merchantCodeTemplateBO.setPrintSortPicker(printSortPicker);
        merchantCodeTemplateBO.setSuitSectionCodeSort(suitSectionCodeSort);
        merchantCodeTemplateBO.setGroupSectionCodeSort(groupSectionCodeSort);
        merchantCodeTemplateBO.setExcludeTradeTagIds(excludeTradeTagIds);
        merchantCodeTemplateBO.setOrderRemarkAddOuterId(orderRemarkAddOuterId);
        String printSortType = PrintMerchantCodeUtils.handlePrintSortType(userInvoicesTemplate);
        if (needValues.contains("trade_order_remark") && Objects.equals("1", orderRemarkAddOuterId) && !needValues.contains("outer_id")) {
            // 勾选订单商品备注,并且开了配置当订单商品备注为空时,打印商家编码信息,则需要商品信息
            needValues.add("outer_id");
        }
        if (MerchantCodeSortTypeEnum.NOT_SORT.getValue().equals(printSortType)) {
            //默认排序是按前端给的顺序排序  直接取 printItemMerchantBOS 的顺序
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.NOT_SORT);
        } else if (MerchantCodePrintSortEnum.ITEM.getValue().equals(printSortType)) {
            //仅按商品 要按商品的id排序 规格就是规格的id  直接取 printItemMerchantBOS 的顺序
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_ITEM.getValue().equals(printSortType)) {
            //先供应商名称再商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.GOODSSECTION.getValue().equals(printSortType)) {
            //货位排序
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.GOODSSECTION);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        } else if (MerchantCodeSortTypeEnum.ITEM_REMARK.getValue().equals(printSortType)) {
            //商品备注
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_REMARK);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_remark");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_REMARK);
        } else if (MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE.getValue().equals(printSortType)) {
            //商品简称
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "short_title");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SHORT_TITLE);
        } else if (MerchantCodeSortTypeEnum.ITEM_OUTER_ID.getValue().equals(printSortType)) {
            //商品商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SKU_REMARK.getValue().equals(printSortType)) {
            //规格备注
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_REMARK);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "remark");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.REMARK);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_ITEM_OUTERID.getValue().equals(printSortType)) {
            //先供应商名称 再供应商商家编码 再颜色 再尺码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_ITEM_OUTERID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "sort_supplier_item_outerId");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SORT_SUPPLIER_ITEM_OUTERID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.OUTER_PROPERTIES_NAME_OF_ITEM.getValue().equals(printSortType)) {
            //主商家编码再规格名称再别名
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTER_PROPERTIES_NAME_OF_ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "properties_name");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "properties_alias");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.PROPERTIES_NAME);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.PROPERTIES_ALIAS);
        } else if (MerchantCodeSortTypeEnum.SKU_OUTER_ID.getValue().equals(printSortType)) {
            //规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.GOODSSECTION_SKUOUTERID.getValue().equals(printSortType)) {
            //先货位再规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.GOODSSECTION_SKUOUTERID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_OUTERIDOFITEM.getValue().equals(printSortType)) {
            //先供应商名称再商品商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_OUTERIDOFITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.OUTERID_COLOR_OTHER.getValue().equals(printSortType)) {
            // 先主商品编码再颜色再尺寸
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTERID_COLOR_OTHER);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE_SKU_OUTER_ID.getValue().equals(printSortType)) {
            //先商品简称再规格编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE_SKU_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "short_title");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SHORT_TITLE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_CODE_OUTER_ID.getValue().equals(printSortType)) {
            //先供应商编码再规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_CODE_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_code");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_CODE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.OUTERID_OTHER_COLOR.getValue().equals(printSortType)) {
            // 先主商品编码再尺寸再颜色
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTERID_OTHER_COLOR);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.SKU_PROPERTIES_NAME_GOODSSECTION.getValue().equals(printSortType)) {
            // 先规格简称再货位编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_PROPERTIES_NAME_GOODSSECTION);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "sku_properties_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SKU_PROPERTIES_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        }
        //处理商品来源的数据
        Map<String, String> needItemFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needItemFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedItemFields(needItemFields);
        //商品库存数据
        Map<String, String> needStockFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemStockMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needStockFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedStockFields(needStockFields);
        //处理商品水洗唛来源的数据
        Map<String, String> needWashFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemWashMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needWashFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedWashFields(needWashFields);
        //处理商品货位来源的数据
        Map<String, String> needPositionFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemPositionMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPositionFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedPositionFields(needPositionFields);
        //处理商品供应商来源的数据
        Map<String, String> needSupplierFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemSupplierMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needSupplierFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedSupplierFields(needSupplierFields);
        //处理商品波次来源的数据
        Map<String, String> needWaveFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.waveMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needWaveFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedWaveFields(needWaveFields);
        //处理商品采购来源的数据
        Map<String, String> needPurchaseFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.purchaseMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPurchaseFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedPurchaseFields(needPurchaseFields);
        // 处理商品收货单来源的数据
        Map<String, String> needReceiptFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.receiptMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needReceiptFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedReceiptFields(needReceiptFields);
        //处理订单采购来源的数据
        Map<String, String> needTradeFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.tradeMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needTradeFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedTradeFields(needTradeFields);
        //处理商品其他的数据
        Map<String, String> needOtherFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.outherMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needOtherFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedOtherFields(needOtherFields);
        //处理唯一码的数据
        Map<String, String> needUniqueFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.uniqueMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needUniqueFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedUniqueFields(needUniqueFields);
        //处理售后信息
        Map<String, String> needAfterSaleFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.afterSaleMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needAfterSaleFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedAfterSaleFields(needAfterSaleFields);

        // 维修单
        Map<String, String> needRepairFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.repairMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needRepairFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedRepairFields(needRepairFields);

        //处理加工单来源的数据
        Map<String, String> needProcessingFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.processingMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needProcessingFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedProcessingFields(needProcessingFields);

        //处理快销信息
        Map<String, String> needFastStockFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.fastStockMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needFastStockFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedFastStockFields(needFastStockFields);

        //处理跨境信息
        Map<String, String> needForeignFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.foreignMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needForeignFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedForeignFields(needForeignFields);
        merchantCodeTemplateBO.setShowBtasCode(TradeConfigUtils.openBtasQualityTesting(tradeConfig));
        //处理组合属性
        if (needValues.contains("trade_unique_item_code") || needValues.contains("trade_unique_item_code_barcode") || needValues.contains("trade_unique_item_code_QRCode")) {
            //这几个
            needUniqueFields.put("trade_unique_code", MerchantCodeTemplateMapping.uniqueMapping.get("trade_unique_code"));
            needItemFields.put("outer_id", MerchantCodeTemplateMapping.itemMapping.get("outer_id"));
        }
        //处理组合属性 波次商品-拣选号-位置号
        if (needValues.contains("wave_barcode_position_code") || needValues.contains("wave_barcode_picking_position_code") || needValues.contains("wave_barcode_picking_position_qrcode")) {
            //这几个
            needWaveFields.put("wave_position_code", MerchantCodeTemplateMapping.waveMapping.get("wave_position_code"));
            needWaveFields.put("wave_picking_code", MerchantCodeTemplateMapping.waveMapping.get("wave_picking_code"));
            needItemFields.put("outer_id", MerchantCodeTemplateMapping.itemMapping.get("outer_id"));
        }
        //处理组合属性 波次商品唯一码
        if (needValues.contains("wave_item_position_unique_code") || needValues.contains("wave_item_position_unique_qrcode")) {
            //这几个
            needWaveFields.put("wave_item_unique_code", MerchantCodeTemplateMapping.waveMapping.get("wave_item_unique_code"));
            needItemFields.put("outer_id", MerchantCodeTemplateMapping.itemMapping.get("outer_id"));
        }
        return merchantCodeTemplateBO;
    }

    @Override
    public PrintRepairParams analysisInvoicesTemplate(Staff staff, Long templateId) {
        UserInvoicesTemplate userInvoicesTemplate = getInvoicesTemplate(staff, templateId, EnumInvoicesType.REPAIR_NEW);
        PrintRepairParams params = new PrintRepairParams();
        params.setUserInvoicesTemplate(userInvoicesTemplate);
        //得到 模板设计的数据框的字段
        List<String> needValues = TemplateHelper.getNeedValues(userInvoicesTemplate.getFields());
        params.setNeedValues(needValues);

        List<FieldValue> fieldValues = userInvoicesTemplate.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return params;
        }
        // 填充模板设计字段
        for (FieldValue fieldValue : fieldValues) {
            if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                String sortFields = repairSortMap.get(Integer.parseInt(fieldValue.getValue()));
                if (StringUtils.isNotBlank(sortFields)) {
                    params.setSortFields(Arrays.asList(sortFields.split(",")));
                }
            }
        }
        List<TemplateFieldsDescription> fieldsDescriptions = userInvoicesTemplate.getFieldsDescriptions();
        if (CollectionUtils.isNotEmpty(fieldsDescriptions)) {
            // 设置模板类型,方便后续使用
            params.setTemplateType(fieldsDescriptions.get(0).getTemplateType());
        }
        return params;
    }

    /**
     * 解析拿货单模板
     */
    @Override
    public GetterPrintParams analysisGetterTemplate(Staff staff, Long templateId, String sortType) {
        UserGetterTemplate userGetterTemplate;
        if (templateId != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, templateId);
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板,请确认模板是否已被删除!");

        GetterPrintParams params = new GetterPrintParams();
        params.setSortType(sortType);
        params.setUserInvoicesTemplate(userGetterTemplate);
        //得到 模板设计的数据框的字段
        List<String> needValues = TemplateHelper.getNeedValues(userGetterTemplate.getFields());
        params.setNeedValues(needValues);
        List<TemplateFieldsDescription> fieldsDescriptions = userGetterTemplate.getFieldsDescriptions();
        if (CollectionUtils.isNotEmpty(fieldsDescriptions)) {
            // 设置模板类型,方便后续使用,比如拿货单商品数量降序,针对这个要单独处理
            params.setTemplateType(fieldsDescriptions.get(0).getTemplateType());
        }
        //处理商品来源的数据
        Map<String, String> needItemFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : GetterTemplateMapping.itemMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needItemFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        //处理商品货位来源的数据
        Map<String, String> needPositionFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : GetterTemplateMapping.itemPositionMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPositionFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        //处理供应商相关数据
        Map<String, String> needSupplierFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : GetterTemplateMapping.supplierMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needSupplierFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        params.setNeedItemFields(needItemFields);
        params.setNeedPositionFields(needPositionFields);
        params.setNeedSupplierFields(needSupplierFields);
        List<FieldValue> fieldValues = userGetterTemplate.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return params;
        }
        // 填充模板设计字段
        for (FieldValue fieldValue : fieldValues) {
            if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                Integer orderType = Integer.parseInt(fieldValue.getValue());
                String sortFields = getterSortMap.get(orderType);
                if (StringUtils.isNotBlank(sortFields)) {
                    params.setSortFields(Arrays.asList(sortFields.split(",")));
                }
                if ((Objects.equals(9, orderType) || Objects.equals(21, orderType)) && !needValues.contains("item_section_code")) {
                    needValues.add("item_section_code");
                }
                if (Objects.equals(12, orderType) && !needValues.contains("supplierCode")) {
                    needValues.add("supplierCode");
                }
                if (!Arrays.asList(0, 9, 12, 21).contains(orderType)) {
                    // 保证查询商品信息
                    needValues.add("table_item_outer_id");
                }
            } else if (EnumFieldValueName.SUITE_TYPE.getValue().equals(fieldValue.getName())) {
                params.setSuitType(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.ITEM_PIC_SELECT.getValue().equals(fieldValue.getName())) {
                params.setItemPicSelect(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.PRINT_ITEM_TYPE.getValue().equals(fieldValue.getName())) {
                params.setPrintItemType(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.PRINT_GIFT_RULE.getValue().equals(fieldValue.getName())) {
                params.setPrintGiftRule(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.PRINT_GIFT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                params.setGiftSortType(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.SUIT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                params.setSuitSortType(Integer.parseInt(fieldValue.getValue()));
            } else if (EnumFieldValueName.SUPPLIER_SOURCE.getValue().equals(fieldValue.getName())) {
                params.setSupplierSource(fieldValue.getValue());
            } else if (EnumFieldValueName.PRINT_DIMENSION.getValue().equals(fieldValue.getName())) {
                params.setPrintDimension(fieldValue.getValue());
            }
        }
        return params;
    }

    @Override
    public DeliverPrintParams analysisDeliverTemplate(Staff staff, Long templateId, String isPrint) {
        DeliverPrintParams params = new DeliverPrintParams();
        UserDeliverTemplate userDeliverTemplate;
        if (templateId != null) {
            userDeliverTemplate = userDeliverTemplateService.userQuery(staff, templateId);
        } else {
            userDeliverTemplate = userDeliverTemplateService.userDefaultGet(staff);
        }
        if (userDeliverTemplate == null) {
            throw new IllegalArgumentException("当前用户找不到这个模板" + templateId);
        }
        boolean filterConsigned = printHelperService.getFilterConsigned(staff);
        boolean isDeliverCross = Objects.equals(9L, userDeliverTemplate.getSysTemplateId());
        List<String> needValues = TemplateHelper.getNeedValues(userDeliverTemplate.getFields());
        params.setUserDeliverTemplate(userDeliverTemplate);
        params.setNeedValues(needValues);
        params.setFilterConsigned(filterConsigned);
        List<TemplateFieldsDescription> fieldsDescriptions = userDeliverTemplate.getFieldsDescriptions();
        if (CollectionUtils.isNotEmpty(fieldsDescriptions)) {
            // 设置模板类型,方便后续使用,比如拿货单商品数量降序,针对这个要单独处理
            params.setTemplateType(fieldsDescriptions.get(0).getTemplateType());
        }
        if (isDeliverCross) {
            // 出货单默认添加主商家编码,添加商品信息
            needValues.add("outer_id_of_item");
        }
        List<FieldValue> fieldValues = userDeliverTemplate.getFieldValues();
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            // 填充模板设计字段
            for (FieldValue fieldValue : fieldValues) {
                if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName()) || EnumFieldValueName.SPECIFIC_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                    Integer orderType = Integer.parseInt(fieldValue.getValue());
                    String sortFields = isDeliverCross ? deliverCrossSortMap.get(orderType) : deliverCustomSortMap.get(orderType);
                    if (StringUtils.isNotBlank(sortFields)) {
                        params.setSortFields(Arrays.asList(sortFields.split(",")));
                        String[] split = sortFields.split(",");
                        if (split.length > 0) {
                            needValues.addAll(Arrays.asList(split));
                        }
                        if (sortFields.contains("sortSectionCode") || sortFields.contains("item_section_num")) {
                            params.setWmsConfig(wmsService.getConfig(staff));
                        }
                        if (sortFields.contains("sortSectionCode")) {
                            params.setPickGoodsRouteConfig(wmsService.getPickGoodsRouteConfig(staff));
                        }
                    }
                } else if (EnumFieldValueName.SUITE_TYPE.getValue().equals(fieldValue.getName())) {
                    params.setSuitType(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.ITEM_PIC_SELECT.getValue().equals(fieldValue.getName())) {
                    params.setItemPicSelect(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.PRINT_GIFT_RULE.getValue().equals(fieldValue.getName())) {
                    params.setPrintGiftRule(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.PRINT_GIFT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                    params.setGiftSortType(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.SUIT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                    params.setSuitSortType(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.PRINT_VIRTUAL_RULE.getValue().equals(fieldValue.getName())) {
                    params.setPrintVirtualRule(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.UN_NEED_DELIVER_PRINT.getValue().equals(fieldValue.getName())) {
                    params.setUnNeedDeliverPrint(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.IS_PRINT_SYS_ITEM_AFTER_MODIFY.getValue().equals(fieldValue.getName())) {
                    params.setIsPrintSysItemAfterModify(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.SELL_MEMO_SET.getValue().equals(fieldValue.getName())) {
                    params.setSellMemoSet(JSONObject.parseObject(fieldValue.getValue()));
                } else if (Objects.equals(9L, userDeliverTemplate.getSysTemplateId()) && EnumFieldValueName.MEASURES.getValue().equals(fieldValue.getName())) {
                    params.setMeasures(fieldValue.getValue());
                } else if (EnumFieldValueName.PRINT_TRADE_NUM.getValue().equals(fieldValue.getName())) {
                    params.setPrintTradeNum(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.TABLE_ITEM_MERGE.getValue().equals(fieldValue.getName())) {
                    params.setTableMerge(Integer.parseInt(fieldValue.getValue()));
                } else if (EnumFieldValueName.PRINT_SORT_PICKER.getValue().equals(fieldValue.getName())) {
                    params.setPrintSortPicker(fieldValue.getValue());
                } else if (EnumFieldValueName.PORD_MERGE.getValue().equals(fieldValue.getName())) {
                    params.setProdMerge(Integer.parseInt(fieldValue.getValue()));
                }
            }
        }
        //处理商品来源的数据
        Map<String, String> needItemFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : DeliverTemplateMapping.itemMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needItemFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        Map<String, String> needWaveFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : DeliverTemplateMapping.waveMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needWaveFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        //处理商品货位来源的数据
        Map<String, String> needPositionFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : DeliverTemplateMapping.itemPositionMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPositionFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        //处理订单相关数据
        Map<String, String> needTradeFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : DeliverTemplateMapping.tradeMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needTradeFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        //处理订单相关数据
        Map<String, String> needShopFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : DeliverTemplateMapping.shopMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needShopFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        params.setNeedItemFields(needItemFields);
        params.setNeedWaveFields(needWaveFields);
        params.setNeedPositionFields(needPositionFields);
        params.setNeedTradeFields(needTradeFields);
        params.setNeedShopFields(needShopFields);
        return params;
    }


    @Override
    public SendTradeTemplateBO analysisSendTradeTemplate(Staff staff, Long templateId) {
        UserInvoicesTemplate userInvoicesTemplate = getInvoicesTemplate(staff, templateId, EnumInvoicesType.SEND_TRADE);

        SendTradeTemplateBO sendTradeTemplateBO = new SendTradeTemplateBO();
        sendTradeTemplateBO.setUserInvoicesTemplate(userInvoicesTemplate);
        sendTradeTemplateBO.setNeedValues(TemplateHelper.getNeedValues(userInvoicesTemplate.getFields()));
        return sendTradeTemplateBO;
    }

    @Override
    public DangkouReconcileTemplateBO analysisDangkouReconcileTemplate(Staff staff, Long templateId) {
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null && templateId > 0) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.DANGKOU_STATEMENT);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.DANGKOU_STATEMENT);
        }
        Assert.isTrue(userInvoicesTemplate != null, "解析模板时未获取到模板信息!");

        // 排序类型
        Integer orderType = 1;
        List<FieldValue> fieldValues = userInvoicesTemplate.getFieldValues();
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            // 填充模板设计字段
            for (FieldValue fieldValue : fieldValues) {
                if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                    orderType = Integer.parseInt(fieldValue.getValue());
                }
            }
        }

        DangkouReconcileTemplateBO templateBO = new DangkouReconcileTemplateBO();
        templateBO.setUserInvoicesTemplate(userInvoicesTemplate);
        templateBO.setNeedValues(TemplateHelper.getNeedValues(userInvoicesTemplate.getFields()));
        templateBO.setOrderType(orderType);
        return templateBO;
    }

    @Override
    public MerchantCodeTemplateBO analysisSheinEnvTemplate(Staff staff, Long templateId, String defaultFields) {
        MerchantCodeTemplateBO merchantCodeTemplateBO = new MerchantCodeTemplateBO();
        UserInvoicesTemplate userInvoicesTemplate = getInvoicesTemplate(staff, templateId, EnumInvoicesType.SHEIN_MERCHANTCODE_ENV_LABEL);
        merchantCodeTemplateBO.setUserInvoicesTemplate(userInvoicesTemplate);
        merchantCodeTemplateBO.setFieldValues(userInvoicesTemplate.getFieldValues());
        merchantCodeTemplateBO.setTemplateName(userInvoicesTemplate.getName());
        //得到 模板设计的数据框的字段
        List<String> needValues = TemplateHelper.getNeedValues(userInvoicesTemplate.getFields());
        merchantCodeTemplateBO.setNeedValues(needValues);
        if (StringUtils.isNotBlank(defaultFields)) {
            for (String field : defaultFields.split(",")) {
                if (!needValues.contains(field)) {
                    needValues.add(field);
                }
            }
        }
        String printSortType = PrintMerchantCodeUtils.handlePrintSortType(userInvoicesTemplate);
        //得到排序方式 且 把排序需要用到的数据写到needValues里
        String sectionSort = PrintMerchantCodeUtils.handleGoodsSectionSort(userInvoicesTemplate);
        String printSortPicker = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.PRINT_SORT_PICKER.getValue()); //是否按拣选路径参与排序
        String groupByTrade = PrintTemplateHelper.getFieldValueByKey(userInvoicesTemplate.getFieldValues(), EnumFieldValueName.GROUP_BY_TRADE.getValue(), "1"); //是否按照订单分组,默认开启
        merchantCodeTemplateBO.setSectionSort(sectionSort);
        merchantCodeTemplateBO.setPrintSortPicker(printSortPicker);
        merchantCodeTemplateBO.setGroupByTrade(groupByTrade);
        if (MerchantCodeSortTypeEnum.NOT_SORT.getValue().equals(printSortType)) {
            //默认排序是按前端给的顺序排序  直接取 printItemMerchantBOS 的顺序
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.NOT_SORT);
        } else if (MerchantCodePrintSortEnum.ITEM.getValue().equals(printSortType)) {
            //仅按商品 要按商品的id排序 规格就是规格的id  直接取 printItemMerchantBOS 的顺序
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_ITEM.getValue().equals(printSortType)) {
            //先供应商名称再商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.GOODSSECTION.getValue().equals(printSortType)) {
            //货位排序
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.GOODSSECTION);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        } else if (MerchantCodeSortTypeEnum.ITEM_REMARK.getValue().equals(printSortType)) {
            //商品备注
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_REMARK);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_remark");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_REMARK);
        } else if (MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE.getValue().equals(printSortType)) {
            //商品简称
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "short_title");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SHORT_TITLE);
        } else if (MerchantCodeSortTypeEnum.ITEM_OUTER_ID.getValue().equals(printSortType)) {
            //商品商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SKU_REMARK.getValue().equals(printSortType)) {
            //规格备注
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_REMARK);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "remark");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.REMARK);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_ITEM_OUTERID.getValue().equals(printSortType)) {
            //先供应商名称 再供应商商家编码 再颜色 再尺码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_ITEM_OUTERID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "sort_supplier_item_outerId");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SORT_SUPPLIER_ITEM_OUTERID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.OUTER_PROPERTIES_NAME_OF_ITEM.getValue().equals(printSortType)) {
            //主商家编码再规格名称再别名
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTER_PROPERTIES_NAME_OF_ITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "properties_name");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "properties_alias");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.PROPERTIES_NAME);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.PROPERTIES_ALIAS);
        } else if (MerchantCodeSortTypeEnum.SKU_OUTER_ID.getValue().equals(printSortType)) {
            //规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.GOODSSECTION_SKUOUTERID.getValue().equals(printSortType)) {
            //先货位再规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.GOODSSECTION_SKUOUTERID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_OUTERIDOFITEM.getValue().equals(printSortType)) {
            //先供应商名称再商品商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_OUTERIDOFITEM);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.OUTERID_COLOR_OTHER.getValue().equals(printSortType)) {
            // 先主商品编码再颜色再尺寸
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTERID_COLOR_OTHER);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE_SKU_OUTER_ID.getValue().equals(printSortType)) {
            //先商品简称再规格编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.ITEM_SHORT_TITLE_SKU_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "short_title");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SHORT_TITLE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.SUPPLIER_CODE_OUTER_ID.getValue().equals(printSortType)) {
            //先供应商编码再规格商家编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SUPPLIER_CODE_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "supplier_code");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SUPPLIER_CODE);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
        } else if (MerchantCodeSortTypeEnum.OUTERID_OTHER_COLOR.getValue().equals(printSortType)) {
            // 先主商品编码再尺寸再颜色
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.OUTERID_OTHER_COLOR);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "item_outer_id");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.ITEM_OUTER_ID);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "other_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.OTHER_PROPERTY);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "color_property");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.COLOR_PROPERTY);
        } else if (MerchantCodeSortTypeEnum.SKU_PROPERTIES_NAME_GOODSSECTION.getValue().equals(printSortType)) {
            // 先规格简称再货位编码
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.SKU_PROPERTIES_NAME_GOODSSECTION);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "sku_properties_name");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.SKU_PROPERTIES_NAME);
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.GOODS_SECTION_CODE);
            if ("1".equals(sectionSort)) {
                //使用拣选货位排序
                PrintMerchantCodeUtils.needValuesAddField(needValues, "trade_unique_goods_section_code");
                PrintMerchantCodeUtils.needValuesAddField(needValues, "wave_picking_goods_section_code");
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.TRADE_UNIQUE_GOODS_SECTION_CODE);
                merchantCodeTemplateBO.getSortField().add(MerchantCodeSortField.WAVE_PICKING_GOODS_SECTION_CODE);
            }
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            merchantCodeTemplateBO.setWmsConfig(wmsConfig);
        }

        //处理商品来源的数据
        Map<String, String> needItemFields = new HashMap<>();
        //处理商品波次来源的数据
        Map<String, String> needWaveFields = new HashMap<>();
        //处理订单采购来源的数据
        Map<String, String> needTradeFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needItemFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedItemFields(needItemFields);

        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.waveMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needWaveFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedWaveFields(needWaveFields);

        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.tradeMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needTradeFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedTradeFields(needTradeFields);

        //商品库存数据
        Map<String, String> needStockFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemStockMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needStockFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedStockFields(needStockFields);
        //处理商品水洗唛来源的数据
        Map<String, String> needWashFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemWashMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needWashFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedWashFields(needWashFields);
        //处理商品货位来源的数据
        Map<String, String> needPositionFields = new HashMap<>();
        if (MerchantCodeSortTypeEnum.GOODSSECTION.getValue().equals(printSortType)) {
            PrintMerchantCodeUtils.needValuesAddField(needValues, "goods_section_code");
            merchantCodeTemplateBO.setPrintSortType(MerchantCodeSortTypeEnum.GOODSSECTION);
        }
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemPositionMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPositionFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedPositionFields(needPositionFields);
        //处理商品供应商来源的数据
        Map<String, String> needSupplierFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.itemSupplierMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needSupplierFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedSupplierFields(needSupplierFields);
        merchantCodeTemplateBO.setNeedWaveFields(needWaveFields);
        //处理商品采购来源的数据
        Map<String, String> needPurchaseFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.purchaseMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needPurchaseFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedPurchaseFields(needPurchaseFields);
        merchantCodeTemplateBO.setNeedTradeFields(needTradeFields);
        //处理商品其他的数据
        Map<String, String> needOtherFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.outherMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needOtherFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedOtherFields(needOtherFields);
        //处理跨境信息
        Map<String, String> needForeignFields = new HashMap<>();
        for (Map.Entry<String, String> itemEntry : MerchantCodeTemplateMapping.foreignMapping.entrySet()) {
            if (needValues.contains(itemEntry.getKey())) {
                needForeignFields.put(itemEntry.getKey(), itemEntry.getValue());
            }
        }
        merchantCodeTemplateBO.setNeedForeignFields(needForeignFields);

        HashMap<String, String> hashMap = new HashMap<>(1);
        merchantCodeTemplateBO.setNeedUniqueFields(hashMap);
        merchantCodeTemplateBO.setNeedProcessingFields(hashMap);
        merchantCodeTemplateBO.setNeedAfterSaleFields(hashMap);
        merchantCodeTemplateBO.setNeedFastStockFields(hashMap);
        merchantCodeTemplateBO.setNeedRepairFields(hashMap);

        if (!needWashFields.isEmpty()) {
            needItemFields.put("title", "title");
            needValues.add("title");
        }

        return merchantCodeTemplateBO;
    }

    @Override
    public RefundTemplateBO analysisRefundTemplate(Staff staff, Long templateId) {
        UserInvoicesTemplate userInvoicesTemplate;
        userInvoicesTemplate = getInvoicesTemplate(staff, templateId, EnumInvoicesType.REFUND_WAREHOUSE);
        RefundTemplateBO refundTemplateBO = new RefundTemplateBO();
        refundTemplateBO.setNeedValues(TemplateHelper.getNeedValues(userInvoicesTemplate.getFields()));
        refundTemplateBO.setUserInvoicesTemplate(userInvoicesTemplate);
        List<FieldValue> fieldValues = userInvoicesTemplate.getFieldValues();
        if(CollectionUtils.isNotEmpty(fieldValues)){
            for (FieldValue fieldValue : fieldValues) {
                if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                    refundTemplateBO.setPrintSortType(fieldValue.getValue());
                }
            }
        }

        return refundTemplateBO;
    }

    /**
     * 获取模板
     */
    @NotNull
    private UserInvoicesTemplate getInvoicesTemplate(Staff staff, Long templateId, EnumInvoicesType invoicesType) {
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null && templateId > 0) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, invoicesType);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, invoicesType);
        }
        Assert.isTrue(userInvoicesTemplate != null, "解析模板时未获取到模板信息!");
        return userInvoicesTemplate;
    }
}

package com.raycloud.dmj.services.print;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.raycloud.bizlogger.Logger;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.dms.domain.dto.DmsBaseDistributorInfoDto;
import com.raycloud.dmj.dms.request.DmsQueryDistributorInfoRequest;
import com.raycloud.dmj.dms.response.DmsDistributorInfoResponse;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PickGoodsRouteConfig;
import com.raycloud.dmj.domain.PickGoodsRouteConfigDetail;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.CustomSortRule;
import com.raycloud.dmj.domain.enums.AccountTypeEnum;
import com.raycloud.dmj.domain.enums.SaleOrderTypeEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.pt.IExpressTemplateBase;
import com.raycloud.dmj.domain.pt.PtConfigConst;
import com.raycloud.dmj.domain.pt.UserInvoicesTemplate;
import com.raycloud.dmj.domain.pt.enums.EnumFieldValueName;
import com.raycloud.dmj.domain.pt.enums.EnumInvoicesType;
import com.raycloud.dmj.domain.pt.enums.EnumSaleTradeGoodsType;
import com.raycloud.dmj.domain.pt.printbo.MergeSaleTradeBo;
import com.raycloud.dmj.domain.pt.printbo.SaleTradePrintBO;
import com.raycloud.dmj.domain.pt.printvo.SaleTradePrintVO;
import com.raycloud.dmj.domain.pt.wlb.FieldValue;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stalls.common.constants.SaleConstants;
import com.raycloud.dmj.domain.stalls.common.utils.DataFormatUtils;
import com.raycloud.dmj.domain.stalls.trade.vobj.PayInfo;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.params.AssoGoodsSectionSkuParams;
import com.raycloud.dmj.dto.stalls.order.response.SaleOrderFullResp;
import com.raycloud.dmj.dto.stalls.trade.response.SaleTradeFullResp;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.helper.*;
import com.raycloud.dmj.services.helper.wlb.TemplateHelper;
import com.raycloud.dmj.services.print.utils.ItemSupplierBridgeUtils;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.IOrderProductService;
import com.raycloud.dmj.services.trades.ITradePostPrintService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.CustomSortUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.PrintItemUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.secret_api.api.SecretRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.services.helper.wlb.TemplateHelper.checkPurchasePricePower;
import static com.raycloud.dmj.services.helper.wlb.TemplateHelper.checkSalePower;

/**
 * @author: 胡喻晶
 * @description: TODO 新接口打印销货单,数据组装服务
 * @date: 2024/7/15 4:39 PM
 */
@Service("printSaleTradePullRespService")
public class PrintSaleTradePullRespService {

    private static final String TRADE_FIELDS = "sid,discount_fee,short_id,template_id,template_type";
    private static final String ORDER_FIELDS = "id,sid,status,item_sys_id,sku_sys_id,num,enable_status,sys_status,type,combine_id,sys_consigned";

    private static final Logger logger = Logger.getLogger(PrintSaleTradePullRespService.class);
    @Resource
    PrintPageSearchService printPageSearchService;
    @Resource
    ITradePostPrintService tradePostPrintService;
    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    IStaffService staffService;
    @Resource
    SecretRequest secretRequest;
    @Resource
    IUserExpressTemplateService userTemplateService;
    @Resource
    IUserWlbExpressTemplateService wlbTemplateService;
    @Resource
    IItemServiceDubbo itemService;
    @Resource
    IWmsService wmsService;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IOrderProductService orderProductService;

    public List<SaleTradePrintVO> getFieldValues(Staff staff, List<SaleTradeFullResp> saleTradeFullResps, UserInvoicesTemplate template, Map<String, Integer> printNumMap, String mergePrint) {
        /*
          订单数据
          */
        Map<Long, Trade> tradeMap = new HashMap<>();
        List<SaleOrderFullResp> saleOrderFullResps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saleTradeFullResps)) {
            for (SaleTradeFullResp saleTradeFullResp : saleTradeFullResps) {
                if (CollectionUtils.isNotEmpty(saleTradeFullResp.getOrders())) {
                    saleOrderFullResps.addAll(saleTradeFullResp.getOrders());
                }
            }
        }
        // 考虑有拆单的情况,取order下的sid查订单,不然查不全,当中过滤退货数据
        List<Long> sids = saleOrderFullResps.stream().filter(t -> !SaleOrderTypeEnum.RETURN_GOODS.getType().toString().equals(t.getType()) && t.getSid() != null).map(SaleOrderFullResp::getSid).distinct().collect(Collectors.toList());
        if (!sids.isEmpty()) {
            tradeMap = tradePostPrintService.queryTradeBySidsForPrint(staff, sids, TRADE_FIELDS).stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (key1, key2) -> key2));
            List<String> needValues = TemplateHelper.getNeedValues(template.getFields());
            if (needValues.contains("express_template_name")) {
                setTemplateName(staff, tradeMap);
            }
            if (needValues.contains("item_batch_no") || needValues.contains("item_production_date") || needValues.contains("table_shelf_life") || needValues.contains("table_expire_date")) {
                // 获取子订单商品的批次\生产日期信息
                boolean isV2Version = Objects.equals(2, saleTradeFullResps.get(0).getStVersion());
                String dataSource = StringUtils.defaultString(PrintTemplateHelper.getFieldValueByKey(template.getFieldValues(), EnumFieldValueName.DATA_SOURCE.getValue()), "1");
                getOrderProduct(staff, tradeMap, isV2Version, dataSource);
            }
        }
        /*
         * 员工数据
         */
        Staff s = new Staff();
        s.setCompanyId(staff.getCompanyId());
        Map<Long, Staff> staffMap = staffService.queryStaffList(s).stream().collect(Collectors.toMap(Staff::getId, Function.identity(), (key1, key2) -> key2));
        /*
          获取到销货单的打印数据
         */
        List<SaleTradePrintVO> saleTradePrintVOS = buildPrintVos(staff, saleTradeFullResps, template, staffMap, tradeMap, mergePrint);//返回前端的打印数据
        for (SaleTradePrintVO printVO : saleTradePrintVOS) {
            if (null != printNumMap) {
                printVO.setPrintNum(printNumMap.get(printVO.getSaleTradeId()) == null ? 1 : printNumMap.get(printVO.getSaleTradeId()));
            } else {
                printVO.setPrintNum(1);
            }
        }
        //合单就直接返回数据
        if ("1".equals(mergePrint)) {
            return saleTradePrintVOS;
        }
        //排序
        List<SaleTradePrintVO> sortVOS = new ArrayList<>();
        Map<String, SaleTradePrintVO> saleTradePrintVOMap = saleTradePrintVOS.stream().collect(Collectors.toMap(SaleTradePrintVO::getSaleTradeId, v -> v, (v1, v2) -> v1));
        for (SaleTradeFullResp saleTradeFullResp : saleTradeFullResps) {
            SaleTradePrintVO saleTradePrintVO = saleTradePrintVOMap.get(saleTradeFullResp.getId().toString());
            if (saleTradePrintVO == null) {
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradeFullResp.getId() + ",未获取到打印数据!"));
                continue;
            }
            sortVOS.add(saleTradePrintVO);
        }
        return sortVOS;
    }

    /**
     * 组装整个销货单的打印数据
     */
    private List<SaleTradePrintVO> buildPrintVos(Staff staff, List<SaleTradeFullResp> saleTradeFullResps, UserInvoicesTemplate template, Map<Long, Staff> staffMap, Map<Long, Trade> tradeMap, String mergePrint) {
        List<FieldValue> fieldValues = template.getFieldValues();
        int sortType = 0;
        int dataSource = 1;
        int printDimension = 0;
        int earliestTimeTotalDebt = 0;
        int tableShowStatus = 0;
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            for (FieldValue fieldValue : fieldValues) {
                if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                    try {
                        sortType = Integer.parseInt(fieldValue.getValue());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化orderType失败：" + fieldValue.getValue()), e);
                    }
                } else if (EnumFieldValueName.DATA_SOURCE.getValue().equals(fieldValue.getName())) {
                    try {
                        dataSource = Integer.parseInt(fieldValue.getValue());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化dataSource失败：" + fieldValue.getValue()), e);
                    }
                } else if (EnumFieldValueName.PRINT_DIMENSION.getValue().equals(fieldValue.getName())) {
                    try {
                        printDimension = Integer.parseInt(fieldValue.getValue());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化printDimension失败：" + fieldValue.getValue()), e);
                    }
                } else if (EnumFieldValueName.EARLIEST_TIME_TOTAL_DEBT.getValue().equals(fieldValue.getName())) {
                    try {
                        earliestTimeTotalDebt = Integer.parseInt(fieldValue.getValue());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化earliestTimeTotalDebt失败：" + fieldValue.getValue()), e);
                    }
                } else if (EnumFieldValueName.TABLE_SHOW_STATUS.getValue().equals(fieldValue.getName())) {
                    try {
                        tableShowStatus = Integer.parseInt(fieldValue.getValue());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化tableShowStatust失败：" + fieldValue.getValue()), e);
                    }
                }
            }
        }
        /*
          2.1 将salaTrade order 转化出来并写入BOlist
          2.2 查询到所有的商品并转成map
         */
        Map<Long, Set<Long>> itemIdMap = new HashMap<>();
        List<SaleTradePrintBO> saleTradePrintBOs;
        boolean hasMergePrint = "1".equals(mergePrint);
        if (hasMergePrint) {
            saleTradePrintBOs = buildMergePrintBo(staff, saleTradeFullResps, dataSource, itemIdMap, staffMap, tradeMap, earliestTimeTotalDebt, tableShowStatus);
        } else {
            saleTradePrintBOs = buildPrintBo(saleTradeFullResps, dataSource, itemIdMap);
        }
        //获取商品map
        Map<Long, DmjItem> idToItem = new HashMap<>();
        Map<Long, DmjSku> idToSku = new HashMap<>();
        if (!itemIdMap.isEmpty()) {
            ItemSupplierBridgeUtils.getItemAndSkuMap(staff, itemIdMap, itemService, idToItem, idToSku);
        } else {
            // 说明是纯退货数据,并且表格数据来源设置为了"提交配货数据",这个数据来源不会组装退货商品,所以商品数据为空
            logger.debug(LogHelper.buildLog(staff, "销货单打印,未获取到商品id!!!"));
            throw new IllegalArgumentException("未获取到商品信息,请进入基础-打印模块-销货单-设计中,点击表格,设置中将表格数据来源选择为:以上两种数据匹配汇总,保存后重试!");
        }

        // 获取商品分类
        Map<String, String> itemCategoryIdNameMap = getItemCategoryIdNameMap(staff, idToItem);

        // 获取分销商信息,版本为2的才有分销商ID
        List<Long> distributorIds = saleTradeFullResps.stream().filter(t -> Objects.equals(2, t.getStVersion()) && t.getCustomerId() != null).map(SaleTradeFullResp::getCustomerId).collect(Collectors.toList());
        Map<Long, DmsBaseDistributorInfoDto> distributorMap = getDistributorMap(staff, distributorIds);

        //获取商品货位信息
        List<Long> warehouseIds = saleTradeFullResps.stream().map(SaleTradeFullResp::getOutWarehouseId).collect(Collectors.toList());
        Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap = getAssoGoodsSectionSkuMap(staff, warehouseIds, itemIdMap);

        // 获取子订单的批次\生产日期
        List<Order> orders = TradeUtils.getOrders4Trade(new ArrayList<>(tradeMap.values()));
        Map<String, List<OrderStockProduct>> orderProductMap = new HashMap<>();
        for (Order order : orders) {
            if (CollectionUtils.isNotEmpty(order.getOrderStockProducts())) {
                String key = order.getSid() + "_" + order.getItemSysId() + "_" + (order.getSkuSysId() == null || order.getSkuSysId() < 1 ? -1 : order.getSkuSysId());
                orderProductMap.computeIfAbsent(key, t -> new ArrayList<>()).addAll(order.getOrderStockProducts());
            }
        }
        /*
          3 组装数据
         */
        List<SaleTradePrintVO> saleTradePrintVOS = new ArrayList<>();
        for (SaleTradePrintBO saleTradePrintBO : saleTradePrintBOs) {
            saleTradePrintVOS.add(buildPrintVo(staff, saleTradePrintBO, idToItem, idToSku, template, sortType, dataSource, printDimension, staffMap, tradeMap, itemCategoryIdNameMap, distributorMap, assoGoodsSectionSkuMap, hasMergePrint, tableShowStatus, orderProductMap));
        }
        return saleTradePrintVOS;
    }

    /**
     * 组装整个销货单的打印数据
     */
    private List<SaleTradePrintBO> buildPrintBo(List<SaleTradeFullResp> saleTradeList, int dataSource, Map<Long, Set<Long>> itemIdMap) {
        List<SaleTradePrintBO> saleTradePrintBOs = new ArrayList<>();
        for (SaleTradeFullResp saleTradeFullResp : saleTradeList) {
            List<SaleOrderFullResp> planOrders = new ArrayList<>();
            List<SaleOrderFullResp> outOrders = new ArrayList<>();
            List<SaleOrderFullResp> returnOrders = new ArrayList<>();
            List<SaleOrderFullResp> exchangeOrders = new ArrayList<>();
            List<SaleOrderFullResp> reissueOrders = new ArrayList<>();
            List<SaleOrderFullResp> orders = saleTradeFullResp.getOrders();
            if (CollectionUtils.isNotEmpty(orders)) {
                for (SaleOrderFullResp order : orders) {
                    if (order.getNum() == null || order.getNum() <= 0) {
                        continue;
                    }
                    if (SaleOrderTypeEnum.OUT_GOODS.getType().toString().equals(order.getType())) {
                        outOrders.add(order);
                    } else if (SaleOrderTypeEnum.RETURN_GOODS.getType().toString().equals(order.getType())) {
                        order.setWorkOrderId(saleTradeFullResp.getWorkOrderId());
                        order.setWorkOrderStatus(saleTradeFullResp.getWorkOrderStatus());
                        if (Objects.equals(order.getGoodsType(), SaleConstants.AFTER_SALE_EXCHANGE_ORDER_TYPE)) {
                            exchangeOrders.add(order);
                        } else if (Objects.equals(order.getGoodsType(), SaleConstants.AFTER_SALE_REISSUE_ORDER_TYPE)) {
                            reissueOrders.add(order);
                        } else {
                            returnOrders.add(order);
                        }
                    } else if (SaleOrderTypeEnum.ALLOCATE_GOODS.getType().toString().equals(order.getType())) {
                        planOrders.add(order);
                    }
                }
            }
            if (dataSource == 0) {
                if (CollectionUtils.isNotEmpty(planOrders)) {
                    buildItemSearch(planOrders, itemIdMap);
                } else {
                    buildItemSearch(outOrders, itemIdMap);
                }
            } else if (dataSource == 1) {
                if (CollectionUtils.isNotEmpty(outOrders)) {
                    buildItemSearch(outOrders, itemIdMap);
                } else {
                    buildItemSearch(planOrders, itemIdMap);
                }
                buildItemSearch(exchangeOrders, itemIdMap);
                buildItemSearch(reissueOrders, itemIdMap);
                buildItemSearch(returnOrders, itemIdMap);
            } else {
                //两种数据汇总需要查提交配货数
                buildItemSearch(planOrders, itemIdMap);
                //有直接开单 都要查出货数 和 退货数
                buildItemSearch(outOrders, itemIdMap);
                buildItemSearch(exchangeOrders, itemIdMap);
                buildItemSearch(reissueOrders, itemIdMap);
                buildItemSearch(returnOrders, itemIdMap);
            }
            saleTradePrintBOs.add(new SaleTradePrintBO(saleTradeFullResp, planOrders, outOrders, returnOrders, exchangeOrders, reissueOrders));
        }
        return saleTradePrintBOs;
    }

    /**
     * 组装整个销货单的打印数据 合并模式
     */
    private List<SaleTradePrintBO> buildMergePrintBo(Staff staff, List<SaleTradeFullResp> saleTradeList, int dataSource, Map<Long, Set<Long>> itemIdMap, Map<Long, Staff> staffMap, Map<Long, Trade> tradeMap, int earliestTimeTotalDebt, int tableShowStatus) {
        List<SaleTradePrintBO> saleTradePrintBOs = new ArrayList<>();
        SaleTradeFullResp mergeSaleTrade = null;
        List<SaleOrderFullResp> allPlanOrders = new ArrayList<>();
        List<SaleOrderFullResp> allOutOrders = new ArrayList<>();
        List<SaleOrderFullResp> allReturnOrders = new ArrayList<>();
        List<SaleOrderFullResp> allExchangeOrders = new ArrayList<>();
        List<SaleOrderFullResp> allReissueOrders = new ArrayList<>();
        MergeSaleTradeBo mergeSaleTradeBo = new MergeSaleTradeBo();
        int exchangeItemNum = 0;//换货商品数量
        int reissueItemNum = 0;//补发商品数量
        BigDecimal exchangeTotalFee = new BigDecimal(0);//换货商品总金额
        BigDecimal reissueTotalFee = new BigDecimal(0);//补发商品总金额
        int outItemNum = 0;//出货商品总数 求和
        int returnItemNum = 0;//退货商品总数 求和
        int planAllocateGoodsCount = 0;//提交配货商品总数 求和
        BigDecimal outTotalFee = new BigDecimal(0);//出货商品出货金额 求和
        BigDecimal outTransportFee = new BigDecimal(0);//出货商品运费 求和
        BigDecimal returnTotalFee = new BigDecimal(0);//退货商品退款金额 求和
        BigDecimal saleTotalFee = new BigDecimal(0);//实际应收金额（含运费) 求和
        BigDecimal planAllocateGoodsTotalFee = new BigDecimal(0);//提交配货总金额 求和
        BigDecimal discountFee = new BigDecimal(0);//优惠金额 求和
        BigDecimal salePostFee = new BigDecimal(0);//应付金额  出库金额减退款 求和
        BigDecimal salePaymentFee = new BigDecimal(0);//支付金额 求和
        StringBuilder salePayMessage = new StringBuilder();
        StringBuilder salePaymentMsg = new StringBuilder();
        List<String> saleRemarkPic = new ArrayList<>(); //备注图片
        StringBuilder saleDeliveryTime = new StringBuilder(); //最晚发货时间
        Set<Long> waveIds = new HashSet<>();
        processLastTimeTotalDebt(mergeSaleTradeBo, saleTradeList, earliestTimeTotalDebt);
        // 判断所有拣选员是否是同一个人
        String pickerNames = null;
        boolean isPickerNames = true;
        // 判断所有出货店铺是否相同
        HashSet<String> sendShopNameSet = new HashSet<>();
        // 判断所有退货仓库是否相同
        Set<Long> returnWarehouseIdSet = new HashSet<>();
        // 判断所有出货仓库是否相同
        Set<Long> sendWarehouseIdSet = new HashSet<>();
        // 判断所有收货人是否相同
        HashSet<String> receiverSet = new HashSet<>();
        // 判断所有收货人手机号是否相同
        HashSet<String> receiverPhoneSet = new HashSet<>();
        // 判断所有收货地址是否相同
        HashSet<String> addressSet = new HashSet<>();
        /*
          合并数据
         */
        for (SaleTradeFullResp saleTrade : saleTradeList) {
            if (mergeSaleTrade == null) {
                mergeSaleTrade = saleTrade;
                pickerNames = saleTrade.getPickerNames();
            } else if (isPickerNames) {
                isPickerNames = StrUtil.isNotEmpty(pickerNames) && pickerNames.equals(saleTrade.getPickerNames());
            }
            // 组装不一样则不展示数据
            sendShopNameSet.add(saleTrade.getShopName());
            returnWarehouseIdSet.add(saleTrade.getRetWarehouseId());
            sendWarehouseIdSet.add(saleTrade.getOutWarehouseId());
            receiverSet.add(saleTrade.getReceiverName());
            receiverPhoneSet.add(saleTrade.getReceiverMobile());
            addressSet.add(saleTrade.getReceiverAddress());
            mergeSaleTradeBo.addid(saleTrade.getId());//销货单号
            mergeSaleTradeBo.addsid(saleTrade.getSid() + "");//这里别用tostring sid可能是null 出库单号
            mergeSaleTradeBo.addsysMakers(saleTrade.getSysMaker());//制单人
            mergeSaleTradeBo.addsubmitters(saleTrade.getSubmitter());//开单人
            mergeSaleTradeBo.addremark(saleTrade.getRemark());//备注
            Staff saleTradeSubmitter = staffMap.get(saleTrade.getSubmitterId());
            String saleTradeSubmitterPhone = saleTradeSubmitter == null ? "" : saleTradeSubmitter.getPhone();
            try {
                saleTradeSubmitterPhone = secretRequest.decode(saleTradeSubmitterPhone);
            } catch (Exception e) {
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTrade.getId() + ",开单人id:" + saleTrade.getSubmitterId() + ",开单人电话解密失败!"));
                saleTradeSubmitterPhone = "###";
            }
            mergeSaleTradeBo.addphones(saleTradeSubmitterPhone);//开单人手机号
            exchangeItemNum += saleTrade.getExchangeItemNum() == null ? 0 : saleTrade.getExchangeItemNum();
            reissueItemNum += saleTrade.getReissueItemNum() == null ? 0 : saleTrade.getReissueItemNum();
            exchangeTotalFee = exchangeTotalFee.add(getValue(saleTrade.getExchangeTotalFee(), 6));
            reissueTotalFee = reissueTotalFee.add(getValue(saleTrade.getReissueTotalFee(), 6));
            outItemNum += saleTrade.getOutItemNum() == null ? 0 : saleTrade.getOutItemNum();
            returnItemNum += saleTrade.getRetItemNum() == null ? 0 : saleTrade.getRetItemNum();
            planAllocateGoodsCount += saleTrade.getAllocateItemNum() == null ? 0 : saleTrade.getAllocateItemNum();
            outTotalFee = outTotalFee.add(getValue(saleTrade.getOutTotalFee(), 6));
            outTransportFee = outTransportFee.add(getValue(saleTrade.getTransportFee(), 6));
            returnTotalFee = returnTotalFee.add(getValue(saleTrade.getRetTotalFee(), 6));
            saleTotalFee = saleTotalFee.add(getValue(saleTrade.getPayment(), 6));
            planAllocateGoodsTotalFee = planAllocateGoodsTotalFee.add(getValue(saleTrade.getAllocateTotalFee(), 6));
            Trade trade = tradeMap.get(saleTrade.getSid());
            if (trade == null) {
                trade = new Trade();
            }
            discountFee = discountFee.add(getNum(trade.getDiscountFee(), 6));
            salePostFee = salePostFee.add(getValue(saleTrade.getOutTotalFee(), 6).subtract(getValue(saleTrade.getRetTotalFee(), 6)));
            String payInfoStr = saleTrade.getPayInfos();
            if (StringUtils.isNotBlank(payInfoStr)) {
                List<PayInfo> payInfos = JSONObject.parseArray(payInfoStr, PayInfo.class);
                if (CollectionUtils.isNotEmpty(payInfos)) {
                    salePaymentFee = salePaymentFee.add(payInfos.stream().map(PayInfo::getPayFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                    salePayMessage.append(buildPayMessage(saleTrade.getPayType(), payInfos)).append(";");
                    salePaymentMsg.append(buildSalePaymentMsg(payInfos)).append(",");
                }
            }

            List<SaleOrderFullResp> planOrders = new ArrayList<>();
            List<SaleOrderFullResp> outOrders = new ArrayList<>();
            List<SaleOrderFullResp> returnOrders = new ArrayList<>();
            List<SaleOrderFullResp> exchangeOrders = new ArrayList<>();
            List<SaleOrderFullResp> reissueOrders = new ArrayList<>();
            List<SaleOrderFullResp> orders = saleTrade.getOrders() == null ? new ArrayList<>() : saleTrade.getOrders();
            for (SaleOrderFullResp order : orders) {
                if (order.getNum() == null || order.getNum() <= 0) {
                    continue;
                }
                if (SaleOrderTypeEnum.OUT_GOODS.getType().toString().equals(order.getType())) {
                    outOrders.add(order);
                } else if (SaleOrderTypeEnum.RETURN_GOODS.getType().toString().equals(order.getType())) {
                    order.setWorkOrderId(saleTrade.getWorkOrderId());
                    order.setWorkOrderStatus(saleTrade.getWorkOrderStatus());
                    if (Objects.equals(order.getGoodsType(), SaleConstants.AFTER_SALE_EXCHANGE_ORDER_TYPE)) {
                        exchangeOrders.add(order);
                    } else if (Objects.equals(order.getGoodsType(), SaleConstants.AFTER_SALE_REISSUE_ORDER_TYPE)) {
                        reissueOrders.add(order);
                    } else {
                        returnOrders.add(order);
                    }
                } else if (SaleOrderTypeEnum.ALLOCATE_GOODS.getType().toString().equals(order.getType())) {
                    planOrders.add(order);
                }
            }
            allPlanOrders.addAll(planOrders);
            allOutOrders.addAll(outOrders);
            allReturnOrders.addAll(returnOrders);
            allExchangeOrders.addAll(exchangeOrders);
            allReissueOrders.addAll(reissueOrders);
            List<String> remarkPic = saleTrade.getFileRemarks();
            if (CollectionUtils.isNotEmpty(remarkPic)) {
                saleRemarkPic.addAll(remarkPic);
            }
            if (saleTrade.getDeliveryTime() != null) {
                saleDeliveryTime.append(DateUtil.format(saleTrade.getDeliveryTime(), "yyyy-MM-dd HH:mm:ss")).append(",");
            }
            if (saleTrade.getWaveId() != null) {
                waveIds.add(saleTrade.getWaveId());
            }
        }
        // 如果有拣选员不同则置为null
        if (!isPickerNames) {
            mergeSaleTrade.setPickerNames(null);
        }
        // 数据不一致则置为null
        if (mergeSaleTrade != null) {
            if (sendShopNameSet.size() != 1) mergeSaleTrade.setShopName(null);
            if (returnWarehouseIdSet.size() != 1) mergeSaleTrade.setRetWarehouseName(null);
            if (sendWarehouseIdSet.size() != 1) mergeSaleTrade.setOutWarehouseName(null);
            if (receiverSet.size() != 1) mergeSaleTrade.setReceiverName(null);
            if (receiverPhoneSet.size() != 1) mergeSaleTrade.setReceiverMobile(null);
            if (addressSet.size() != 1) mergeSaleTrade.setReceiverAddress(null);
        }
        if (dataSource == 0) {
            if (CollectionUtils.isNotEmpty(allPlanOrders)) {
                buildItemSearch(allPlanOrders, itemIdMap);
            } else {
                buildItemSearch(allOutOrders, itemIdMap);
            }
        } else if (dataSource == 1) {
            if (CollectionUtils.isNotEmpty(allOutOrders)) {
                buildItemSearch(allOutOrders, itemIdMap);
            } else {
                buildItemSearch(allPlanOrders, itemIdMap);
            }
            buildItemSearch(allReturnOrders, itemIdMap);
            buildItemSearch(allExchangeOrders, itemIdMap);
            buildItemSearch(allReissueOrders, itemIdMap);
        } else {
            //两种数据汇总需要查提交配货数
            buildItemSearch(allPlanOrders, itemIdMap);
            //有直接开单 都要查出货数 和 退货数
            buildItemSearch(allOutOrders, itemIdMap);
            buildItemSearch(allReturnOrders, itemIdMap);
            buildItemSearch(allExchangeOrders, itemIdMap);
            buildItemSearch(allReissueOrders, itemIdMap);
        }
        BigDecimal saleOutFee = outTotalFee.add(outTransportFee);//出货总金额（含运费）= 货款+运费
        mergeSaleTradeBo.setExchangeItemNum(exchangeItemNum);
        mergeSaleTradeBo.setExchangeTotalFee(exchangeTotalFee);
        mergeSaleTradeBo.setReissueItemNum(reissueItemNum);
        mergeSaleTradeBo.setReissueTotalFee(reissueTotalFee);
        mergeSaleTradeBo.setOutItemNum(outItemNum);
        mergeSaleTradeBo.setOutTotalFee(outTotalFee.toPlainString());
        mergeSaleTradeBo.setOutTransportFee(outTransportFee.toPlainString());
        mergeSaleTradeBo.setSaleOutFee(saleOutFee.toPlainString());
        mergeSaleTradeBo.setReturnItemNum(returnItemNum);
        mergeSaleTradeBo.setReturnTotalFee(returnTotalFee.toPlainString());
        mergeSaleTradeBo.setSaleTotalFee(saleTotalFee.toPlainString());
        mergeSaleTradeBo.setPlanAllocateGoodsCount(planAllocateGoodsCount);
        mergeSaleTradeBo.setPlanAllocateGoodsTotalFee(planAllocateGoodsTotalFee.toPlainString());
        mergeSaleTradeBo.setDiscountFee(discountFee.toPlainString());
        mergeSaleTradeBo.setSalePostFee(salePostFee.toPlainString());
        mergeSaleTradeBo.setSalePaymentFee(salePaymentFee.toPlainString());
        if (salePayMessage.toString().endsWith(";")) {
            salePayMessage.deleteCharAt(salePayMessage.length() - 1);
        }
        mergeSaleTradeBo.setSalePayMessage(salePayMessage.toString());
        if (salePaymentMsg.toString().endsWith(";")) {
            salePaymentMsg.deleteCharAt(salePaymentMsg.length() - 1);
        }
        mergeSaleTradeBo.setSalePaymentMsg(salePaymentMsg.toString());
        Map<String, List<SaleOrderFullResp>> planOrderMap = allPlanOrders.stream().collect(Collectors.groupingBy(t -> getOrderMergeKey(t.getItemSysId(), t.getSkuSysId(), t.getNumPerBox())));
        Map<String, List<SaleOrderFullResp>> outOrderMap = allOutOrders.stream().collect(Collectors.groupingBy(t -> getOrderMergeKey(t.getItemSysId(), t.getSkuSysId(), t.getNumPerBox())));
        Map<String, List<SaleOrderFullResp>> returnOrderMap = allReturnOrders.stream().collect(Collectors.groupingBy(t -> getOrderMergeKey(t.getItemSysId(), t.getSkuSysId(), t.getNumPerBox())));
        Map<String, List<SaleOrderFullResp>> exchangeOrderMap = allExchangeOrders.stream().collect(Collectors.groupingBy(t -> getOrderMergeKey(t.getItemSysId(), t.getSkuSysId(), null)));
        Map<String, List<SaleOrderFullResp>> reissueOrderMap = allReissueOrders.stream().collect(Collectors.groupingBy(t -> getOrderMergeKey(t.getItemSysId(), t.getSkuSysId(), null)));
        mergeSaleTradeBo.setPlanAllocateGoodsKindNum(planOrderMap.size());
        mergeSaleTradeBo.setOutItemKind(outOrderMap.size());
        mergeSaleTradeBo.setReturnItemKind(returnOrderMap.size());
        mergeSaleTradeBo.setExchangeItemKind(exchangeOrderMap.size());
        mergeSaleTradeBo.setReissueItemKind(reissueOrderMap.size());
        mergeSaleTradeBo.setSaleRemarkPic(saleRemarkPic);
        if (saleDeliveryTime.toString().endsWith(",")) {
            saleDeliveryTime.deleteCharAt(saleDeliveryTime.length() - 1);
        }
        mergeSaleTradeBo.setSaleDeliveryTime(saleDeliveryTime.toString());
        if (waveIds.size() == 1) {
            mergeSaleTradeBo.setWaveId(waveIds.toArray(new Long[0])[0]);
        }
        SaleTradePrintBO saleTradePrintBO = new SaleTradePrintBO();
        List<SaleOrderFullResp> mergePlanOrders = mergeOrder(allPlanOrders, tableShowStatus);
        List<SaleOrderFullResp> mergeOutOrders = mergeOrder(allOutOrders, tableShowStatus);
        List<SaleOrderFullResp> mergeReturnOrders = mergeOrder(allReturnOrders, tableShowStatus);
        List<SaleOrderFullResp> mergeExchangeOrders = mergeOrder(allExchangeOrders, tableShowStatus);
        List<SaleOrderFullResp> mergeReissueOrders = mergeOrder(allReissueOrders, tableShowStatus);
        saleTradePrintBO.setSaleTradeFullResp(mergeSaleTrade);
        saleTradePrintBO.setPlanSaleOrders(mergePlanOrders);
        saleTradePrintBO.setOutSaleOrders(mergeOutOrders);
        saleTradePrintBO.setReturnSaleOrders(mergeReturnOrders);
        saleTradePrintBO.setExchangeSaleOrders(mergeExchangeOrders);
        saleTradePrintBO.setReissueSaleOrders(mergeReissueOrders);
        saleTradePrintBO.setMergeSale(true);
        saleTradePrintBO.setMergeSaleTradeBo(mergeSaleTradeBo);
        saleTradePrintBOs.add(saleTradePrintBO);
        return saleTradePrintBOs;
    }

    /**
     * @param mergeOrder 合并后的
     * @param order      添加对象
     * @description 解析批发收银给的箱规、箱数、散装数 并进行累加
     * <AUTHOR>
     * @date 2024-04-17 19:50
     */
    private void accumulateTradeTable(SaleOrderFullResp mergeOrder, SaleOrderFullResp order) {
        Integer mergeNumOfBox = mergeOrder.getNumOfBox();
        Integer mergeBulkNum = mergeOrder.getBulkNum();
        Integer numOfBox = order.getNumOfBox();
        Integer bulkNum = order.getBulkNum();
        // 累加箱数
        int countNumOfBox = Optional.ofNullable(mergeNumOfBox).orElse(0) + Optional.ofNullable(numOfBox).orElse(0);
        // 累加散装数
        int countBulkNum = Optional.ofNullable(mergeBulkNum).orElse(0) + Optional.ofNullable(bulkNum).orElse(0);
        mergeOrder.setNumOfBox(countNumOfBox);
        mergeOrder.setBulkNum(countBulkNum);
    }

    /**
     * 封装单个销货单打印信息
     *
     * @param saleTradePrintBO bo
     * @param idToItem         商品map
     * @param idToSku          规格 map
     * @param sortType         排序方式
     * @param dataSource       数据来源
     * @param printDimension   打印维度 0-按sku打印,1-按款打印
     * @param hasMergePrint    是否合并打印
     * @param tableShowStatus  表格展示:0-商品维度,1-单据+商品维度
     */
    private SaleTradePrintVO buildPrintVo(Staff staff, SaleTradePrintBO saleTradePrintBO, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku,
                                          UserInvoicesTemplate template, Integer sortType, int dataSource, int printDimension, Map<Long, Staff> staffMap,
                                          Map<Long, Trade> tradeMap, Map<String, String> itemCategoryIdNameMap, Map<Long, DmsBaseDistributorInfoDto> distributorMap,
                                          Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint, int tableShowStatus,
                                          Map<String, List<OrderStockProduct>> orderProductMap) {
        SaleTradePrintVO saleTradePrintVo = new SaleTradePrintVO();
        SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
        Trade trade = tradeMap.get(saleTradeFullResp.getSid());
        trade = trade == null ? new Trade() : trade;
        /*
          1 封装基础数据
         */
        saleTradePrintVo.setSaleCustomerName(saleTradeFullResp.getCustomerName());//客户姓名
        saleTradePrintVo.setTitle(template.getTitle());//标题
        saleTradePrintVo.setSaleSendWarehouseName(saleTradeFullResp.getOutWarehouseName());//出货仓库
        saleTradePrintVo.setSaleSendShopName(saleTradeFullResp.getShopName());//出货店铺
        if (saleTradeFullResp.getPickGoodsType() != null) {
            saleTradePrintVo.setSalePickGoodsType(Integer.valueOf(1).equals(saleTradeFullResp.getPickGoodsType()) ? "自提" : "邮寄");//提货方式
        }
        saleTradePrintVo.setSaleReturnWarehouseName(StringUtils.isNotBlank(saleTradeFullResp.getRetWarehouseName()) ? saleTradeFullResp.getRetWarehouseName() : "");//退货仓库
        saleTradePrintVo.setSaleReceiver(saleTradeFullResp.getReceiverName());//收件人
        saleTradePrintVo.setSaleReceiverPhone(saleTradeFullResp.getReceiverMobile());//收货人电话

        StringBuilder saleAddress = new StringBuilder(StringUtils.defaultString(saleTradeFullResp.getReceiverState()))
                .append(saleTradeFullResp.getReceiverCity()).append(saleTradeFullResp.getReceiverDistrict())
                .append(saleTradeFullResp.getReceiverAddress());
        saleTradePrintVo.setSaleAddress(saleAddress.toString());//收货地址
        saleTradePrintVo.setSaleTradePrintTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));//打印时间
        saleTradePrintVo.setSaleTradeShareUrl((StringUtils.isBlank(saleTradeFullResp.getShareUrl()) || hasMergePrint) ? "" : saleTradeFullResp.getShareUrl());
        saleTradePrintVo.setSaleTradeIdBarCode(String.valueOf(saleTradeFullResp.getId()));//销货单条码
        saleTradePrintVo.setSaleShortId(trade.getShortId() == null ? "" : String.valueOf(trade.getShortId()));//内部单号
        saleTradePrintVo.setSaleAccountType(conversionAccountType(saleTradeFullResp.getAccountType()));//付款方式
        saleTradePrintVo.setSaleCalculateType(AccountTypeEnum.checkAccountType(saleTradeFullResp.getAccountType()));//结算方式
        saleTradePrintVo.setCreateTime(DateUtil.format(saleTradeFullResp.getCreated(), "yyyy-MM-dd HH:mm:ss"));//制单时间
        saleTradePrintVo.setTradeOutSid(saleTradeFullResp.getOutSid() != null ? saleTradeFullResp.getOutSid() : ""); // 快递单号
        saleTradePrintVo.setRepairWorkOrderId(saleTradeFullResp.getWorkOrderId() != null ? String.valueOf(saleTradeFullResp.getWorkOrderId()) : ""); // 售后工单号

        saleTradePrintVo.setExpressTemplateName(StringUtils.isNotBlank(trade.getTemplateName()) ? trade.getTemplateName() : "");//快递模板名称
        saleTradePrintVo.setSaleTradeSurplusFee(getValue(saleTradeFullResp.getSurplusFee(), 6).toString()); //销货单抹零金额
        saleTradePrintVo.setSaleTradeDepositAmount(getValue(saleTradeFullResp.getDeposit(), 6).toString()); //订金
        DmsBaseDistributorInfoDto distributorInfo = distributorMap.get(Objects.equals(saleTradeFullResp.getStVersion(), 2) ? saleTradeFullResp.getCustomerId() : null);
        if (distributorInfo != null) {
            saleTradePrintVo.setDistributorPhone(distributorInfo.getContactPhone()); // 分销商手机号码
            saleTradePrintVo.setDistributorTelephone(distributorInfo.getContactTelephone()); //分销商电话号码
            saleTradePrintVo.setDistributorAddress(buildDistributorAddress(distributorInfo));//分销商地址
            saleTradePrintVo.setDistributorContactName(distributorInfo.getContactName());//分销商联系人
            saleTradePrintVo.setDistributorSalesMan(distributorInfo.getSaleStaffName());//分销商业务员
            saleTradePrintVo.setSaleDistributorShortName(distributorInfo.getAliasName());//分销商简称
        }
        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
        boolean isV1Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() || EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue();
        if (saleTradePrintBO.isMergeSale()) {
            buildMergeMsg(saleTradePrintVo, saleTradePrintBO.getMergeSaleTradeBo());
            saleTradePrintVo.setSaleDistributorName(Objects.equals(saleTradeFullResp.getStVersion(), 2) ? saleTradeFullResp.getCustomerName() : "");//分销商名称
            saleTradePrintVo.setBalance(DataFormatUtils.formatStr(saleTradeFullResp.getBalance(), "0.00"));//分销商余额
            saleTradePrintVo.setCredit(DataFormatUtils.formatStr(saleTradeFullResp.getCredit(), "0.00"));//分销商授信额度
            saleTradePrintVo.setTotalBalance(DataFormatUtils.formatStr(saleTradeFullResp.getTotalBalance(), "0.00"));//分销商剩余总金额
        } else {
            buildSingleMsg(staff, saleTradePrintVo, saleTradeFullResp, trade, staffMap);
        }
        saleTradePrintVo.setPickerNames(saleTradeFullResp.getPickerNames());// 拣选员

        if (isV1Template) {
            // 交叉结构 V1、V2
            buildCrossItemProperties(saleTradePrintVo, staff, idToItem, idToSku, saleTradePrintBO, dataSource, sortType, template, hasMergePrint);
        } else {
            // 自定义模板数据组装 交叉结构V3(没有合计行) 表格数据saleShortId
            if (Integer.valueOf(1).equals(printDimension) || isV3Template) {
                //款维度
                if (Objects.equals(1, tableShowStatus)) {
                    saleTradePrintVo.setPrintTableVOS(buildMergeSaleTradeTableVosById(staff, idToItem, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
                } else {
                    saleTradePrintVo.setSaleTradeTableVos(buildMergeSaleTradeTableVos(staff, idToItem, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
                    // 计算箱数\销售合计
                    getTableVOSAfter(staff, saleTradePrintVo);
                }
            } else {
                //明细维度
                if (Objects.equals(1, tableShowStatus)) {
                    saleTradePrintVo.setPrintTableVOS(buildSingleSaleTradeTableVosById(staff, idToItem, idToSku, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
                } else {
                    saleTradePrintVo.setSaleTradeTableVos(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
                    // 计算箱数\销售合计
                    getTableVOSAfter(staff, saleTradePrintVo);
                }
            }
        }
        return saleTradePrintVo;
    }

    /**
     * 组装合并销货单数据
     */
    private void buildMergeMsg(SaleTradePrintVO saleTradePrintVo, MergeSaleTradeBo mergeSaleTradeBo) {
        saleTradePrintVo.setSaleTradeId(joinMsg(mergeSaleTradeBo.getIds()));//销货单id
        saleTradePrintVo.setSalePlanItemNum(mergeSaleTradeBo.getPlanAllocateGoodsCount() == null ? "" : String.valueOf(mergeSaleTradeBo.getPlanAllocateGoodsCount()));//提交配货商品总数
        saleTradePrintVo.setSalePlanItemKind(mergeSaleTradeBo.getPlanAllocateGoodsKindNum() == null ? "" : String.valueOf(mergeSaleTradeBo.getPlanAllocateGoodsKindNum()));//提交配货商品种类
        saleTradePrintVo.setSalePlanTotalFee(mergeSaleTradeBo.getPlanAllocateGoodsTotalFee());//提交配货商品金额
        saleTradePrintVo.setSaleOutItemNum(mergeSaleTradeBo.getOutItemNum() == null ? "" : String.valueOf(mergeSaleTradeBo.getOutItemNum()));//出货商品数
        saleTradePrintVo.setSaleOutItemKind(mergeSaleTradeBo.getOutItemKind() == null ? "" : String.valueOf(mergeSaleTradeBo.getOutItemKind()));//出货商品种类
        saleTradePrintVo.setSaleOutTotalFee(mergeSaleTradeBo.getOutTotalFee() == null ? "" : mergeSaleTradeBo.getOutTotalFee());//出货商品出货金额
        saleTradePrintVo.setSaleOutTransportFee(mergeSaleTradeBo.getOutTransportFee());//出货商品运费
        saleTradePrintVo.setSaleOutFee(mergeSaleTradeBo.getSaleOutFee());//出货总金额
        saleTradePrintVo.setSaleReturnItemNum(mergeSaleTradeBo.getReturnItemNum() == null ? "" : String.valueOf(mergeSaleTradeBo.getReturnItemNum()));//退货商品总数
        saleTradePrintVo.setSaleReturnItemKind(mergeSaleTradeBo.getOutItemKind() == null ? "" : String.valueOf(mergeSaleTradeBo.getOutItemKind()));//退货商品种类
        saleTradePrintVo.setSaleReturnTotalFee(mergeSaleTradeBo.getReturnTotalFee());//退货商品退款金额
        saleTradePrintVo.setSaleTotalFee(mergeSaleTradeBo.getSaleTotalFee());//实际应收金额
        saleTradePrintVo.setSalePayMessage(mergeSaleTradeBo.getSalePayMessage());//付款信息
        saleTradePrintVo.setSaleRemark(joinMsg(mergeSaleTradeBo.getRemarks()));//销货单备注
        saleTradePrintVo.setTradeSysMemo(joinMsg(mergeSaleTradeBo.getRemarks()));//销货单备注
        saleTradePrintVo.setSaleTradeCheckoutTime(mergeSaleTradeBo.getCheckoutTime());//开单时间
        saleTradePrintVo.setSaleTradeSid(joinMsg(mergeSaleTradeBo.getSids()));//出库单号
        saleTradePrintVo.setSaleTradeSysMaker(joinMsg(mergeSaleTradeBo.getSysMakers()));//制单人
        saleTradePrintVo.setSaleTradeSubmitter(joinMsg(mergeSaleTradeBo.getSubmitters()));//结账人
        saleTradePrintVo.setSaleLastTimeTotalDebt(mergeSaleTradeBo.getLastTimeTotalDebt());//上次累计欠款
        saleTradePrintVo.setSaleNowDebt(mergeSaleTradeBo.getNowDebt());//本次欠款
        saleTradePrintVo.setSaleTotalDebt(mergeSaleTradeBo.getTotalDebt());//累计欠款
        try {
            // 本次累计欠款 = 上次累计欠款 + 本次欠款
            saleTradePrintVo.setSaleNowTotalDebt(String.format("%.6f", new BigDecimal(mergeSaleTradeBo.getLastTimeTotalDebt()).add(new BigDecimal(mergeSaleTradeBo.getNowDebt()))));//上次累计欠款 + 本次欠款
        } catch (Exception e) {
            // 报错则走原逻辑
            saleTradePrintVo.setSaleNowTotalDebt(mergeSaleTradeBo.getNowTotalDebt());//上次累计欠款
            logger.error(String.format("组合销货单打印上次累计欠款和本次累计欠款转换失败：上次累计欠款 = %s, 本次欠款 = %s", mergeSaleTradeBo.getLastTimeTotalDebt(), mergeSaleTradeBo.getNowDebt()));
        }
//        saleTradePrintVo.setSaleNowTotalDebt(mergeSaleTradeBo.getNowTotalDebt());//本次累计欠款
        saleTradePrintVo.setSaleDiscountAmount(mergeSaleTradeBo.getDiscountFee());//优惠金额
        saleTradePrintVo.setSalePostFee(mergeSaleTradeBo.getSalePostFee());//出货商品总金额-退货商品总金额
        saleTradePrintVo.setSalePaymentFee(mergeSaleTradeBo.getSalePaymentFee());//支付宝支付+微信支付+现金支付的总和
        saleTradePrintVo.setSalePaymentMsg(mergeSaleTradeBo.getSalePaymentMsg());//自定义支付详情
        saleTradePrintVo.setSaleTradeSubmitterPhone(joinMsg(mergeSaleTradeBo.getPhones()));//开单人手机号
        saleTradePrintVo.setSaleRemarkPic(mergeSaleTradeBo.getSaleRemarkPic());//备注图片
        saleTradePrintVo.setSaleDeliveryTime(mergeSaleTradeBo.getSaleDeliveryTime());//最晚发货时间
        saleTradePrintVo.setSaleTradeWaveCode(mergeSaleTradeBo.getWaveId() == null ? "" : mergeSaleTradeBo.getWaveId().toString());//波次号
        saleTradePrintVo.setExchangeItemKind(mergeSaleTradeBo.getExchangeItemKind());//换货商品种类
        saleTradePrintVo.setExchangeItemNum(mergeSaleTradeBo.getExchangeItemNum()); //换货商品数量
        saleTradePrintVo.setExchangeTotalFee(mergeSaleTradeBo.getExchangeTotalFee()); //换货商品总金额
        saleTradePrintVo.setReissueItemKind(mergeSaleTradeBo.getReissueItemKind());//补发商品种类
        saleTradePrintVo.setReissueItemNum(mergeSaleTradeBo.getReissueItemNum()); //补发商品数量
        saleTradePrintVo.setReissueTotalFee(mergeSaleTradeBo.getReissueTotalFee()); //补发商品总金额
    }

    /**
     * 组装单个销货单数据
     */
    private void buildSingleMsg(Staff staff, SaleTradePrintVO saleTradePrintVo, SaleTradeFullResp saleTradeFullResp, Trade trade, Map<Long, Staff> staffMap) {
        saleTradePrintVo.setSaleTradeId(String.valueOf(saleTradeFullResp.getId()));//销货单id
        saleTradePrintVo.setSalePlanItemNum(saleTradeFullResp.getAllocateItemNum() != null ? saleTradeFullResp.getAllocateItemNum().toString() : "");//提交配货商品总数
        saleTradePrintVo.setSalePlanItemKind(saleTradeFullResp.getAllocateItemKind() != null ? saleTradeFullResp.getAllocateItemKind().toString() : "");//提交配货商品种类
        saleTradePrintVo.setSalePlanTotalFee(getValue(saleTradeFullResp.getAllocateTotalFee(), 6).toString());//提交配货商品金额
        saleTradePrintVo.setSaleOutItemNum(saleTradeFullResp.getOutItemNum() == null ? "" : saleTradeFullResp.getOutItemNum().toString());//出货商品数
        saleTradePrintVo.setSaleOutItemKind(saleTradeFullResp.getOutItemKind() == null ? "" : saleTradeFullResp.getOutItemKind().toString());//出货商品种类
        saleTradePrintVo.setSaleOutTotalFee(getValue(saleTradeFullResp.getOutTotalFee(), 6).toString());//出货商品出货金额
        saleTradePrintVo.setSaleOutTransportFee(getValue(saleTradeFullResp.getTransportFee(), 6).toString());//出货商品运费
        if (saleTradeFullResp.getOutTotalFee() != null || saleTradeFullResp.getTransportFee() != null) {
            //都为空不赋值
            BigDecimal outTotalFee = saleTradeFullResp.getOutTotalFee() == null ? new BigDecimal(0) : saleTradeFullResp.getOutTotalFee();
            BigDecimal outFee = outTotalFee.add(saleTradeFullResp.getTransportFee() == null ? new BigDecimal(0) : saleTradeFullResp.getTransportFee());
            saleTradePrintVo.setSaleOutFee(outFee.toString());//出货总金额
        }
        saleTradePrintVo.setSaleReturnItemNum(saleTradeFullResp.getRetItemNum() == null ? "" : saleTradeFullResp.getRetItemNum().toString());//退货商品总数
        saleTradePrintVo.setSaleReturnItemKind(saleTradeFullResp.getRetItemKind() == null ? "" : saleTradeFullResp.getRetItemKind().toString());//退货商品种类
        saleTradePrintVo.setSaleReturnTotalFee(getValue(saleTradeFullResp.getRetTotalFee(), 6).toString());//退货商品退款金额
        saleTradePrintVo.setSaleTotalFee(getValue(saleTradeFullResp.getPayment(), 6).toString());////实际应收金额
        saleTradePrintVo.setSaleRemark(saleTradeFullResp.getRemark());//销货单备注
        saleTradePrintVo.setSaleTradeCheckoutTime(saleTradeFullResp.getCheckoutTime() != null ? DateUtil.format(saleTradeFullResp.getCheckoutTime(), "yyyy-MM-dd HH:mm:ss") : "");//开单时间
        saleTradePrintVo.setSaleTradeSid(saleTradeFullResp.getSid() == null ? "" : String.valueOf(saleTradeFullResp.getSid()));//出库单号
        saleTradePrintVo.setSaleTradeSysMaker(saleTradeFullResp.getSysMaker());//制单人
        saleTradePrintVo.setSaleTradeSubmitter(saleTradeFullResp.getSubmitter());//结账人
        saleTradePrintVo.setTradeSysMemo(saleTradeFullResp.getRemark());//销货单备注
        logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradeFullResp.getId() + "本次欠款:" + saleTradeFullResp.getNowDebt() + ",上次累计欠款:" + saleTradeFullResp.getLastTimeTotalDebt() + ",累计欠款:" + saleTradeFullResp.getTotalDebt()));
        saleTradePrintVo.setSaleLastTimeTotalDebt(getValue(saleTradeFullResp.getLastTimeTotalDebt(), 6).toString());//上次累计欠款
        saleTradePrintVo.setSaleNowDebt(getValue(saleTradeFullResp.getNowDebt(), 6).toString());//本次欠款
        saleTradePrintVo.setSaleTotalDebt(getValue(saleTradeFullResp.getTotalDebt(), 6).toString());//累计欠款
        saleTradePrintVo.setSaleNowTotalDebt(getValue(saleTradeFullResp.getCurrentTotalDebt(), 6).toString());//本次累计欠款
        saleTradePrintVo.setSaleDiscountAmount(trade.getDiscountFee());//优惠金额
        saleTradePrintVo.setSalePostFee(String.valueOf(getValue(saleTradeFullResp.getOutTotalFee(), 6).subtract(getValue(saleTradeFullResp.getRetTotalFee(), 6))));//出货商品总金额-退货商品总金额
//        BigDecimal partSalePaymentFee = getNum(saleTrade.getAliPayFee()).add(getNum(saleTrade.getWxPayFee())).add(getNum(saleTrade.getCashFee()));
        String payInfoStr = saleTradeFullResp.getPayInfos();
        if (StringUtils.isNotBlank(payInfoStr)) {
            List<PayInfo> payInfos = JSONObject.parseArray(payInfoStr, PayInfo.class);
            if (CollectionUtils.isNotEmpty(payInfos)) {
                saleTradePrintVo.setSalePayMessage(buildPayMessage(saleTradeFullResp.getPayType(), payInfos));//付款信息
                BigDecimal salePaymentFee = payInfos.stream().map(PayInfo::getPayFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                saleTradePrintVo.setSalePaymentFee(salePaymentFee.toString());//支付宝支付+微信支付+现金支付+ 财务自定义支付方式[]支付的总和
                saleTradePrintVo.setSalePaymentMsg(buildSalePaymentMsg(payInfos));//自定义支付详情
            }
        }
        saleTradePrintVo.setBalance(DataFormatUtils.formatStr(saleTradeFullResp.getBalance(), "0.00"));//分销商余额
        saleTradePrintVo.setCredit(DataFormatUtils.formatStr(saleTradeFullResp.getCredit(), "0.00"));//分销商授信额度
        saleTradePrintVo.setTotalBalance(DataFormatUtils.formatStr(saleTradeFullResp.getTotalBalance(), "0.00"));//分销商剩余总金额
        saleTradePrintVo.setSaleDeliveryTime(DateUtil.format(saleTradeFullResp.getDeliveryTime(), "yyyy-MM-dd HH:mm:ss"));//最晚发货时间
        saleTradePrintVo.setSaleRemarkPic(saleTradeFullResp.getFileRemarks()); //图片备注
        saleTradePrintVo.setSaleTradeWaveCode(saleTradeFullResp.getWaveId() == null ? "" : saleTradeFullResp.getWaveId().toString());//波次号
        try {
            Staff saleTradeSubmitter = staffMap.get(saleTradeFullResp.getSubmitterId());
            String saleTradeSubmitterPhone = saleTradeSubmitter == null ? "" : saleTradeSubmitter.getPhone();
            saleTradePrintVo.setSaleTradeSubmitterPhone(secretRequest.decode(saleTradeSubmitterPhone));//开单人手机号
        } catch (Exception e) {
            logger.error(("电话号码转换出错"), e);
        }
        //表格数据saleShortId
        saleTradePrintVo.setSaleDistributorName(Objects.equals(saleTradeFullResp.getStVersion(), 2) ? saleTradeFullResp.getCustomerName() : "");//分销商名称
        saleTradePrintVo.setExchangeItemKind(saleTradeFullResp.getExchangeItemKind());;   //换货商品种类
        saleTradePrintVo.setExchangeItemNum(saleTradeFullResp.getExchangeItemNum());    //换货商品数量
        saleTradePrintVo.setExchangeTotalFee(saleTradeFullResp.getExchangeTotalFee());    //换货商品总金额
        saleTradePrintVo.setReissueItemKind(saleTradeFullResp.getReissueItemKind());  //补发商品种类
        saleTradePrintVo.setReissueItemNum(saleTradeFullResp.getReissueItemNum());    //补发商品数量
        saleTradePrintVo.setReissueTotalFee(saleTradeFullResp.getReissueTotalFee());    //补发商品总金额
    }

    private BigDecimal getNum(String num) {
        if (StringUtils.isEmpty(num) || Objects.equals("null", num)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(num).setScale(3, RoundingMode.HALF_UP);
    }

    private BigDecimal getNum(String num, Integer scale) {
        if (StringUtils.isEmpty(num) || Objects.equals("null", num)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(num).setScale(scale == null ? 3 : scale, RoundingMode.HALF_UP);
    }

    private BigDecimal getValue(BigDecimal value, Integer scale) {
        if (scale == null) {
            return ObjectUtils.defaultIfNull(value, BigDecimal.ZERO);
        }
        return ObjectUtils.defaultIfNull(value, BigDecimal.ZERO).setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 按款打印 我们走这个逻辑
     *
     * @param idToItem         idToItem
     * @param saleTradePrintBO saleTradePrintBO
     * @param dataSource       数据源 0 提交配货数据  1 实际出货数据 2 以上两种数据匹配汇总
     */
    private List<SaleTradePrintVO.SaleTradeTableVO> buildMergeSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, SaleTradePrintBO saleTradePrintBO, Integer dataSource,
                                                                                Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template,
                                                                                Map<Long, DmjSku> idToSku, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint,
                                                                                Map<String, List<OrderStockProduct>> orderProductMap) {
        //按款打印
        /*
          1 获取 根据sysItemId 分组的打印数据
         */
        Map<String, List<SaleOrderFullResp>> sysItemIdOrdersMap;//实际要打的数据
        Map<String, List<SaleOrderFullResp>> sysItemIdReferenceOrdersMap = new HashMap<>();//作为参照的数据
        Map<String, List<SaleOrderFullResp>> sysItemIdreturnOrdersMap = null;// 退货数据
        Map<String, List<SaleOrderFullResp>> sysItemIdExchangeOrdersMap = null;// 换货数据
        Map<String, List<SaleOrderFullResp>> sysItemIdReissueOrdersMap = null;// 补货数据
        boolean onlyOutOrder = false;
        if (dataSource == 0) {
            //提交配货数据 不许要参照
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getPlanSaleOrders())) {
                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanSaleOrders(), template);
            } else {
                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getOutSaleOrders(), template);
            }
        } else if (dataSource == 1) {
            //实际出货数据 提交配货数缺货数要参照提交数据
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutSaleOrders())) {
                // V2取销货单关联订单（未作废）的实际发货数量
                SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
                List<SaleOrderFullResp> outSaleOrders = saleTradePrintBO.getOutSaleOrders();
                if (saleTradeFullResp != null && Objects.equals(saleTradeFullResp.getStVersion(), 2)) {
                    outSaleOrders = saleTradePrintBO.getOutSaleOrders().stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
                }
                sysItemIdOrdersMap = orders2ItemIdMap(outSaleOrders, template);
            } else {
                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanSaleOrders(), template);
            }
            sysItemIdReferenceOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanSaleOrders(), template);
            if (sysItemIdReferenceOrdersMap.isEmpty()) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                onlyOutOrder = true;
            }
            sysItemIdreturnOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReturnSaleOrders(), template);
            sysItemIdExchangeOrdersMap = orders2ItemIdMap(saleTradePrintBO.getExchangeSaleOrders(), template);
            sysItemIdReissueOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReissueSaleOrders(), template);
        } else {
            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
            sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanSaleOrders(), template);
            sysItemIdReferenceOrdersMap = orders2ItemIdMap(saleTradePrintBO.getOutSaleOrders(), template);
            if (sysItemIdOrdersMap.isEmpty()) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                sysItemIdOrdersMap = sysItemIdReferenceOrdersMap;
                onlyOutOrder = true;
            }
            sysItemIdreturnOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReturnSaleOrders(), template);
            sysItemIdExchangeOrdersMap = orders2ItemIdMap(saleTradePrintBO.getExchangeSaleOrders(), template);
            sysItemIdReissueOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReissueSaleOrders(), template);
        }
        /*
          2 获取合并后的orders
         */
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildMergeSaleTradeTableVos(staff, sysItemIdOrdersMap,
                sysItemIdReferenceOrdersMap, idToItem, false, dataSource, saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder,
                itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        // 退货(换货)  ： 商品数量要显示为正数，isReturn=false
        if (sysItemIdExchangeOrdersMap != null && sysItemIdExchangeOrdersMap.size() > 0) {
            saleTradeTableVOS.addAll(buildMergeSaleTradeTableVos(staff, sysItemIdExchangeOrdersMap, sysItemIdReferenceOrdersMap, idToItem, false, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        // 退货(补货)  ： 商品数量要显示为正数，isReturn=false
        if (sysItemIdReissueOrdersMap != null && sysItemIdReissueOrdersMap.size() > 0) {
            saleTradeTableVOS.addAll(buildMergeSaleTradeTableVos(staff, sysItemIdReissueOrdersMap, sysItemIdReferenceOrdersMap, idToItem, false, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        // 退货
        if (sysItemIdreturnOrdersMap != null && !sysItemIdreturnOrdersMap.isEmpty()) {
            saleTradeTableVOS.addAll(buildMergeSaleTradeTableVos(staff, sysItemIdreturnOrdersMap, sysItemIdReferenceOrdersMap, idToItem, true, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        // 排序
        Long outWarehouseId = null;
        if (saleTradePrintBO.getSaleTradeFullResp() != null && StringUtils.isNotBlank(saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseName())) {
            outWarehouseId = saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseId(); //出货仓库一样才会有出货仓库名,这时候才塞仓库ID,便于取仓储的拣选路径配置
        }
        sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
        //填充序号
        fillTableNo(saleTradeTableVOS);
        return saleTradeTableVOS;
    }

    /**
     * 按款打印 我们走这个逻辑
     *
     * @param idToItem         idToItem
     * @param saleTradePrintBO saleTradePrintBO
     * @param dataSource       数据源 0 提交配货数据  1 实际出货数据 2 以上两种数据匹配汇总
     */
    private List<SaleTradePrintVO.SaleTradePrintTableVO> buildMergeSaleTradeTableVosById(Staff staff, Map<Long, DmjItem> idToItem, SaleTradePrintBO saleTradePrintBO, Integer dataSource,
                                                                                         Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template,
                                                                                         Map<Long, DmjSku> idToSku, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint,
                                                                                         Map<String, List<OrderStockProduct>> orderProductMap) {
        //按款打印
        /*
          1 获取 根据sysItemId 分组的打印数据
         */
        Map<String, List<SaleOrderFullResp>> sysItemIdOrdersMap;//实际要打的数据
        Map<String, List<SaleOrderFullResp>> sysItemIdReferenceOrdersMap;//作为参照的数据
        Map<String, List<SaleOrderFullResp>> sysItemIdreturnOrdersMap;// 退货
        Map<String, List<SaleOrderFullResp>> sysItemIdExchangeOrdersMap = null;// 换货数据
        Map<String, List<SaleOrderFullResp>> sysItemIdReissueOrdersMap = null;// 补货数据
        List<SaleOrderFullResp> orders;
        List<SaleOrderFullResp> referenceOrders = new ArrayList<>();
        List<SaleOrderFullResp> returnOrders = new ArrayList<>();
        List<SaleOrderFullResp> exchangeOrders = new ArrayList<>();
        List<SaleOrderFullResp> reissueOrders = new ArrayList<>();
        boolean onlyOutOrder = false;
        if (dataSource == 0) {
            //提交配货数据 不许要参照
            orders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                orders = saleTradePrintBO.getOutSaleOrders();
            }
        } else if (dataSource == 1) {
            //实际出货数据 提交配货数缺货数要参照提交数据
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutSaleOrders())) {
                SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
                orders = saleTradePrintBO.getOutSaleOrders();
                if (saleTradeFullResp != null && Objects.equals(saleTradeFullResp.getStVersion(), 2)) {
                    orders = saleTradePrintBO.getOutSaleOrders().stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
                }
            } else {
                orders = saleTradePrintBO.getPlanSaleOrders();
            }
            referenceOrders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(referenceOrders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        } else {
            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
            orders = saleTradePrintBO.getPlanSaleOrders();
            referenceOrders = saleTradePrintBO.getOutSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                orders = referenceOrders;
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        }
        List<SaleTradePrintVO.SaleTradePrintTableVO> printTableVOS = new ArrayList<>();
        Long outWarehouseId = null;
        if (saleTradePrintBO.getSaleTradeFullResp() != null && StringUtils.isNotBlank(saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseName())) {
            outWarehouseId = saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseId(); //出货仓库一样才会有出货仓库名,这时候才塞仓库ID,便于取仓储的拣选路径配置
        }
        /**
         * 组装表格
         */
        if (CollectionUtils.isNotEmpty(orders)) {
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            SaleOrderFullResp saleOrderFullResp = orders.get(0);
            Long sid = saleOrderFullResp.getSid();
            String sysStatus = saleOrderFullResp.getSysStatus();
            saleTradePrintTableVO.setId(sid == null || sid <= 0 ? "" : sid.toString());
            saleTradePrintTableVO.setStatus(Trade.SYS_STATUS_FINISHED.equals(sysStatus) ? "已出库" : "未出库");
            sysItemIdOrdersMap = orders2ItemIdMap(orders, template);
            sysItemIdReferenceOrdersMap = orders2ItemIdMap(referenceOrders, template);
            /*
              2 获取合并后的orders
             */
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildMergeSaleTradeTableVos(staff, sysItemIdOrdersMap,
                    sysItemIdReferenceOrdersMap, idToItem, false, dataSource, saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder,
                    itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            //填充序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        if (CollectionUtils.isNotEmpty(returnOrders)) {
            SaleOrderFullResp saleOrderFullResp = returnOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            sysItemIdreturnOrdersMap = orders2ItemIdMap(returnOrders, template);
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildMergeSaleTradeTableVos(staff, sysItemIdreturnOrdersMap, null, idToItem, true, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            //填充序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        if (CollectionUtils.isNotEmpty(exchangeOrders)) {
            SaleOrderFullResp saleOrderFullResp = exchangeOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            sysItemIdExchangeOrdersMap = orders2ItemIdMap(exchangeOrders, template);
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildMergeSaleTradeTableVos(staff, sysItemIdExchangeOrdersMap, null, idToItem, false, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            //填充序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        if (CollectionUtils.isNotEmpty(reissueOrders)) {
            SaleOrderFullResp saleOrderFullResp = reissueOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            sysItemIdReissueOrdersMap = orders2ItemIdMap(reissueOrders, template);
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildMergeSaleTradeTableVos(staff, sysItemIdReissueOrdersMap, null, idToItem, false, dataSource,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            //填充序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        return printTableVOS;
    }


    /**
     * 按款打印的组装数据
     *
     * @param sysItemIdOrdersMap          实际数据map
     * @param sysItemIdReferenceOrdersMap 参照数据map
     * @param idToItem                    idToItem
     * @param isReturn                    isReturn
     * @param dataSource                  dataSource
     */
    private List<SaleTradePrintVO.SaleTradeTableVO> buildMergeSaleTradeTableVos(Staff staff, Map<String, List<SaleOrderFullResp>> sysItemIdOrdersMap, Map<String, List<SaleOrderFullResp>> sysItemIdReferenceOrdersMap,
                                                                                Map<Long, DmjItem> idToItem, boolean isReturn, Integer dataSource,
                                                                                Integer saleTradeStatus, boolean onlyOutOrder, Map<String, String> itemCategoryIdNameMap,
                                                                                UserInvoicesTemplate template, Map<Long, DmjSku> idToSku, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint,
                                                                                Map<String, List<OrderStockProduct>> orderProductMap) {
        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>();
        for (Map.Entry<String, List<SaleOrderFullResp>> entry : sysItemIdOrdersMap.entrySet()) {
            SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO = new SaleTradePrintVO.SaleTradeTableVO();
            saleTradeTableVOS.add(saleTradeTableVO);
            List<SaleOrderFullResp> orders = entry.getValue();
            SaleOrderFullResp order_1 = !orders.isEmpty() ? orders.get(0) : null;
            long itemSysId = order_1 == null ? 0 : order_1.getItemSysId();
            DmjItem dmjItem = idToItem.get(itemSysId);
            if (Objects.isNull(dmjItem)) {
                throw new IllegalArgumentException(String.format("未获取到商品! 请确认商品是否删除或发生变更，系统商品id:%s，商品编码:%s", itemSysId, !orders.isEmpty() ? orders.get(0).getOuterId() : "按款打印没有查询到子订单数据"));
            }
            //组装基本商品信息 和 规格信息
            buildSomeFields(staff, saleTradeTableVO, dmjItem, null, itemCategoryIdNameMap);
            String discountPrice = "0";
            if (order_1 != null) {
                discountPrice = order_1.getPayment() == null ? "0" :
                        order_1.getPayment().divide(new BigDecimal(order_1.getNum()), 6, RoundingMode.HALF_UP).toString();
            }
            Double salePrice = dmjItem.getPriceOutput();
            //获取order中的第一个折扣率,默认100
            saleTradeTableVO.setTableItemDiscountRate(order_1 != null && order_1.getPrice() != null && salePrice != null && salePrice != 0 ? BigDecimal.valueOf((order_1.getPrice().doubleValue() / salePrice) * 100D).setScale(2, RoundingMode.HALF_UP).toString() : " ");
            //获取order中的第一个折后价
            saleTradeTableVO.setTableItemDiscountPrice(discountPrice);
            //获取order中的第一个单价
            saleTradeTableVO.setTableItemPrice(order_1 != null ? getValue(order_1.getPrice(), 6).toString() : "");//单价
            // 装箱要求
            saleTradeTableVO.setTablePackingAsk(order_1 != null ? order_1.getPackingAsk() : "");
            saleTradeTableVO.setOuterIdOfItem(dmjItem.getOuterId());//主商家编码
            int mergeItemNum = 0;//商品数量
            BigDecimal mergeTotalAmont = new BigDecimal(0);//总价
            Set<Long> assistUnitIdSet = new HashSet<>();
            Double assistUnitNum = 0D;
            String assistUnit = "";
            Integer numPerBox = null; // 箱规
            Long lackStockNum = 0L;
            List<OrderStockProduct> orderStockProducts = new ArrayList<>();
            for (SaleOrderFullResp saleOrderFullResp : orders) {
                int itemNum = saleOrderFullResp.getNum() == null ? 0 : isReturn ? -saleOrderFullResp.getNum() : saleOrderFullResp.getNum();
                //BigDecimal price = getValue(saleOrderFullResp.getPrice(), 6);
                //BigDecimal totalAmont = price.multiply(new BigDecimal(itemNum));
                mergeItemNum += itemNum;
                // 上游说不根据价格和数量计算了,直接取payment
                BigDecimal payment = getValue(saleOrderFullResp.getPayment(), 6);
                mergeTotalAmont = mergeTotalAmont.add(isReturn ? payment.negate() : payment);
                Long assistUnitId = saleOrderFullResp.getAssistUnitId(); //存在没单位但是有数量的情况,所以不判断空了
                assistUnitIdSet.add(assistUnitId);
                Double orderAssistUnitNum = saleOrderFullResp.getAssistUnitNum();
                String orderAssistUnit = saleOrderFullResp.getAssistUnit();
                if (orderAssistUnitNum != null) {
                    assistUnitNum += orderAssistUnitNum;
                }
                if (StringUtils.isBlank(assistUnit) && StringUtils.isNotBlank(orderAssistUnit)) {
                    assistUnit = orderAssistUnit;
                }
                // 获取箱规
                if (numPerBox == null) {
                    numPerBox = saleOrderFullResp.getNumPerBox();
                }
                if (MapUtils.isNotEmpty(orderProductMap)) {
                    Long skuSysId = saleOrderFullResp.getSkuSysId();
                    Long fullRespItemSysId = saleOrderFullResp.getItemSysId();
                    String itemProductKey = saleOrderFullResp.getSid() + "_" + fullRespItemSysId + "_" + (skuSysId == null || skuSysId < 1 ? -1 : skuSysId);
                    List<OrderStockProduct> stockProducts = orderProductMap.get(itemProductKey);
                    if (CollectionUtils.isNotEmpty(stockProducts)) {
                        orderStockProducts.addAll(stockProducts);
                    }
                    if (CollectionUtils.isNotEmpty(saleOrderFullResp.getMergeSids())) {
                        for (Long mergeSid : saleOrderFullResp.getMergeSids()) {
                            String mergeItemProductKey = mergeSid + "_" + fullRespItemSysId + "_" + (skuSysId == null || skuSysId < 1 ? -1 : skuSysId);
                            List<OrderStockProduct> mergeStockProducts = orderProductMap.get(mergeItemProductKey);
                            if (CollectionUtils.isNotEmpty(mergeStockProducts)) {
                                orderStockProducts.addAll(mergeStockProducts);
                            }
                        }
                    }
                }
                lackStockNum += saleOrderFullResp.getLackStockNum() == null ? 0L : saleOrderFullResp.getLackStockNum();
            }
            saleTradeTableVO.setTableTotalGetterNum(String.valueOf(mergeItemNum));//合计数量
            saleTradeTableVO.setTableItemNum(Integer.toString(mergeItemNum));//商品数量
            if (isReturn) {
                saleTradeTableVO.setReturnItemNum(String.valueOf(-mergeItemNum)); //退货数
                saleTradeTableVO.setReturnItemNumFee(mergeTotalAmont.negate().toString()); // 退货金额
            } else {
                saleTradeTableVO.setOutItemNum(String.valueOf(mergeItemNum)); //出货数
                saleTradeTableVO.setOutItemNumFee(String.valueOf(mergeTotalAmont.toString())); //出货金额
            }
            if (assistUnitIdSet.size() == 1) {
                saleTradeTableVO.setTableSaleAssistUnit(assistUnit); //销货单单据单位
                saleTradeTableVO.setTableSaleAssistUnitNum(Objects.equals(0D, assistUnitNum) ? "" : String.valueOf(assistUnitNum)); //销货单单据单位数量
            }
            // 销售总价合计
            buildTableSaleTotalAmount(staff, saleTradeTableVO);

            saleTradeTableVO.setTableTotalAmount(mergeTotalAmont.toString());//总价

            try {
                // 销售价优惠金额
                saleTradeTableVO.setTableDiscountAmount("***".equals(saleTradeTableVO.getTableSaleTotalAmount()) ? saleTradeTableVO.getTableSaleTotalAmount() :
                        new BigDecimal(saleTradeTableVO.getTableSaleTotalAmount()).subtract(mergeTotalAmont).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            } catch (NumberFormatException e) {
                saleTradeTableVO.setTableDiscountAmount("0");
                logger.error("销售价优惠金额 table_discount_amount 计算异常：", e);
            }
            // 根据箱规计算箱数、散装数
            saleTradeTableVO.setTableBoxnum(numPerBox == null ? 0 : numPerBox);
            int calculateNum = mergeItemNum < 0 ? -mergeItemNum : mergeItemNum;
            if (numPerBox != null && !Objects.equals(0, numPerBox)) {
                saleTradeTableVO.setTableNumOfBox(calculateNum / numPerBox); //商品箱数
                saleTradeTableVO.setTableRemainBulkNum(calculateNum % numPerBox); //剩余散装数
            } else {
                saleTradeTableVO.setTableRemainBulkNum(calculateNum); //剩余散装数
            }
            // 组装唯一码
            buildUniqueCodeStr(orders, saleTradeTableVO);

            // 组装批次\生产日期
            buildOrderProductMsg(orderStockProducts, saleTradeTableVO);

            // 商品重量
            saleTradeTableVO.setTableProductWeight(removeTrailingZeros(isReturn || dmjItem.getWeight() == null ? BigDecimal.ZERO : new BigDecimal(mergeItemNum).multiply(BigDecimal.valueOf(dmjItem.getWeight()))));

            // 销售类型
            String saleTradeGoodsType = EnumSaleTradeGoodsType.getName(order_1.getType(),order_1.getGoodsType());
            saleTradeTableVO.setTableSaleGoodsType(saleTradeGoodsType);

            // 开单缺货数
            saleTradeTableVO.setTableBillingInsufficientNum(String.valueOf(lackStockNum));

            if (!isReturn && !onlyOutOrder && !EnumSaleTradeGoodsType.EXCHANGE.getName().equals(saleTradeGoodsType)
                    && !EnumSaleTradeGoodsType.REISSUE.getName().equals(saleTradeGoodsType)) {
                //不是退货 且 不是直接开单的需要记录提交配货数和缺货数
                //退货没有提交数和缺货数
                if (dataSource == 0) {
                    saleTradeTableVO.setTablePlanNum(Integer.toString(mergeItemNum));//提交配货数取商品数
                } else {
                    List<SaleOrderFullResp> referenceOrders = sysItemIdReferenceOrdersMap.get(String.valueOf(itemSysId));
                    int referenceMergeItemNum = 0;
                    BigDecimal referencemergeTotalAmont = new BigDecimal(0);//参照总价
                    if (CollectionUtils.isNotEmpty(referenceOrders)) {
                        for (SaleOrderFullResp order : referenceOrders) {
                            int itemNum = order.getNum() == null ? 0 : order.getNum();
                            //BigDecimal price = getValue(order.getPrice(), 6);
                            //BigDecimal totalAmont = price.multiply(new BigDecimal(itemNum));
                            referenceMergeItemNum += itemNum;
                            // 上游说不根据价格和数量计算了,直接取payment
                            referencemergeTotalAmont = referencemergeTotalAmont.add(getValue(order.getPayment(), 6));
                        }
                    }
                    if (dataSource == 1) {
                        saleTradeTableVO.setTablePlanNum(Integer.toString(referenceMergeItemNum));//提交配货数取提交配货数
                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
                            saleTradeTableVO.setTableInsufficientNum(String.valueOf(referenceMergeItemNum - mergeItemNum));//缺货数是提交数减去出货数
                        }
                    } else {
                        saleTradeTableVO.setTableItemNum(Integer.toString(referenceMergeItemNum));//商品数量 要取出货的数量
                        saleTradeTableVO.setTableTotalAmount(referencemergeTotalAmont.toString());//总价 要取出货的总价
                        saleTradeTableVO.setTablePlanNum(Integer.toString(mergeItemNum));//提交配货数取提交配货数
                        saleTradeTableVO.setOutItemNum(Integer.toString(referenceMergeItemNum));// 出货的数量
                        saleTradeTableVO.setOutItemNumFee(referencemergeTotalAmont.toString());//出货的总价
                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
                            saleTradeTableVO.setTableInsufficientNum(String.valueOf(mergeItemNum - referenceMergeItemNum));//缺货数是提交数减去出货数
                        }
                    }
                }
            }
            if (isV3Template) {
                // V3颜色\尺寸等数据处理
                buildV3ColorAndOthers(staff, itemSysId, orders, idToSku, saleTradeTableVO, assoGoodsSectionSkuMap);
            }
        }
        return saleTradeTableVOS;
    }

    /**
     * @param saleTradeTableVO 打印VO
     * @param numPerBox        箱规
     * @param numOfBox         箱数
     * @param bulkNum          散装数
     * @param hasMergePrint    是否合并打印
     * @description 计算箱数、散装数
     * <AUTHOR>
     * @date 2024-04-18 16:30
     */
    private void computeBoxNum(SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, Integer numPerBox, Integer numOfBox, Integer bulkNum, Boolean hasMergePrint) {
        if (hasMergePrint && numPerBox != null && numOfBox != null && bulkNum != null && numPerBox != 0 && bulkNum >= numPerBox) {
            // 若散装数大于或等于箱规，散装数转为箱数，并累加
            numOfBox += bulkNum / numPerBox;
            bulkNum = bulkNum % numPerBox;
        }
        saleTradeTableVO.setTableBoxnum(numPerBox);
        saleTradeTableVO.setTableNumOfBox(numOfBox);
        saleTradeTableVO.setTableRemainBulkNum(bulkNum);
    }

    /**
     * 组装表格数据  明细维度
     */
    private List<SaleTradePrintVO.SaleTradeTableVO> buildSingleSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku, SaleTradePrintBO saleTradePrintBO,
                                                                                 Integer dataSource, Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template,
                                                                                 Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint, Map<String, List<OrderStockProduct>> orderProductMap) {
        //明细打印
        /*
          1 获取 根据sysItemId 分组的打印数据
         */
        List<SaleOrderFullResp> orders;//实际要打的数据
        // 退货
        List<SaleOrderFullResp> returnOrders = null;
        // 退货(换货)
        List<SaleOrderFullResp> exchangeOrders = null;
        // 退货(补发)
        List<SaleOrderFullResp> reissueOrders = null;
        Map<String, SaleOrderFullResp> skuReferenceOrdersMap = new HashMap<>();//作为参照的数据
        boolean onlyOutOrder = false;//是否直接开单
        if (dataSource == 0) {
            //提交配货数据 不许要参照
            orders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                orders = saleTradePrintBO.getOutSaleOrders();
            }
        } else if (dataSource == 1) {
            //实际出货数据 提交配货数缺货数要参照提交数据
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutSaleOrders())) {
                SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
                orders = saleTradePrintBO.getOutSaleOrders();
                if (saleTradeFullResp != null && Objects.equals(saleTradeFullResp.getStVersion(), 2)) {
                    orders = saleTradePrintBO.getOutSaleOrders().stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
                }
            } else {
                orders = saleTradePrintBO.getPlanSaleOrders();
            }
            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getPlanSaleOrders());
            if (skuReferenceOrdersMap.isEmpty()) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        } else {
            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
            orders = saleTradePrintBO.getPlanSaleOrders();
            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getOutSaleOrders());
            if (CollectionUtils.isEmpty(orders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                orders = saleTradePrintBO.getOutSaleOrders();
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        }
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, orders,
                skuReferenceOrdersMap, dataSource, false, saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        //退货(换货)  ： 商品数量要显示为正数，isReturn=false
        if (CollectionUtils.isNotEmpty(exchangeOrders)) {
            saleTradeTableVOS.addAll(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, exchangeOrders, skuReferenceOrdersMap, dataSource, false,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        //退货(补发)  ： 商品数量要显示为正数，isReturn=false
        if (CollectionUtils.isNotEmpty(reissueOrders)) {
            saleTradeTableVOS.addAll(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, reissueOrders, skuReferenceOrdersMap, dataSource, false,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        //退货 ： 退款全不是直接开单
        if (CollectionUtils.isNotEmpty(returnOrders)) {
            saleTradeTableVOS.addAll(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, returnOrders, skuReferenceOrdersMap, dataSource, true,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
        }
        // 排序
        Long outWarehouseId = null;
        if (saleTradePrintBO.getSaleTradeFullResp() != null && StringUtils.isNotBlank(saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseName())) {
            outWarehouseId = saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseId(); //出货仓库一样才会有出货仓库名,这时候才塞仓库ID,便于取仓储的拣选路径配置
        }
        sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);

        // 序号
        fillTableNo(saleTradeTableVOS);
        return saleTradeTableVOS;
    }

    /**
     * 组装表格数据  明细维度
     */
    private List<SaleTradePrintVO.SaleTradePrintTableVO> buildSingleSaleTradeTableVosById(Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku, SaleTradePrintBO saleTradePrintBO,
                                                                                          Integer dataSource, Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template,
                                                                                          Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint, Map<String, List<OrderStockProduct>> orderProductMap) {
        //明细打印
        // 1 获取 根据sysItemId 分组的打印数据
        List<SaleOrderFullResp> orders;//实际要打的数据
        Map<String, SaleOrderFullResp> skuReferenceOrdersMap;//作为参照的数据
        // 退货
        List<SaleOrderFullResp> returnOrders = null;
        // 退货(换货)
        List<SaleOrderFullResp> exchangeOrders = null;
        // 退货(补发)
        List<SaleOrderFullResp> reissueOrders = null;
        List<SaleOrderFullResp> referenceOrders = new ArrayList<>(); //参照数据
        boolean onlyOutOrder = false;//是否直接开单
        if (dataSource == 0) {
            //提交配货数据 不许要参照
            orders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                orders = saleTradePrintBO.getOutSaleOrders();
            }
        } else if (dataSource == 1) {
            //实际出货数据 提交配货数缺货数要参照提交数据
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutSaleOrders())) {
                SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
                orders = saleTradePrintBO.getOutSaleOrders();
                if (saleTradeFullResp != null && Objects.equals(saleTradeFullResp.getStVersion(), 2)) {
                    orders = saleTradePrintBO.getOutSaleOrders().stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
                }
            } else {
                orders = saleTradePrintBO.getPlanSaleOrders();
            }
            referenceOrders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(referenceOrders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        } else {
            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
            orders = saleTradePrintBO.getPlanSaleOrders();
            referenceOrders = saleTradePrintBO.getOutSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                orders = referenceOrders;
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
            exchangeOrders = saleTradePrintBO.getExchangeSaleOrders();
            reissueOrders = saleTradePrintBO.getReissueSaleOrders();
        }
        List<SaleTradePrintVO.SaleTradePrintTableVO> printTableVOS = new ArrayList<>();
        Long outWarehouseId = null;
        if (saleTradePrintBO.getSaleTradeFullResp() != null && StringUtils.isNotBlank(saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseName())) {
            outWarehouseId = saleTradePrintBO.getSaleTradeFullResp().getOutWarehouseId(); //出货仓库一样才会有出货仓库名,这时候才塞仓库ID,便于取仓储的拣选路径配置
        }
        if (CollectionUtils.isNotEmpty(orders)) {
            SaleOrderFullResp saleOrderFullResp = orders.get(0);
            String sysStatus = saleOrderFullResp.getSysStatus();
            Long sid = saleOrderFullResp.getSid();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(sid == null || sid < 0 ? "" : sid.toString());
            saleTradePrintTableVO.setStatus(Trade.SYS_STATUS_FINISHED.equals(sysStatus) ? "已出库" : "未出库");
            skuReferenceOrdersMap = orders2skuMap(referenceOrders);
            // 2 获取合并后的orders
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, orders,
                    skuReferenceOrdersMap, dataSource, false, saleTradePrintBO.getSaleTradeFullResp().getStatus(), onlyOutOrder, itemCategoryIdNameMap,
                    assoGoodsSectionSkuMap, hasMergePrint, orderProductMap));
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            // 序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        if (CollectionUtils.isNotEmpty(returnOrders)) {
            SaleOrderFullResp saleOrderFullResp = returnOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            //退款全不是直接开单
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildSingleSaleTradeTableVos(staff, idToItem, idToSku, returnOrders, null, dataSource, true,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            // 序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        /**
         * 换货
         */
        if (CollectionUtils.isNotEmpty(exchangeOrders)) {
            SaleOrderFullResp saleOrderFullResp = exchangeOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            //退款全不是直接开单
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildSingleSaleTradeTableVos(staff, idToItem, idToSku, exchangeOrders, null, dataSource, false,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            // 序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        /**
         * 补货
         */
        if (CollectionUtils.isNotEmpty(reissueOrders)) {
            SaleOrderFullResp saleOrderFullResp = reissueOrders.get(0);
            Integer workOrderStatus = saleOrderFullResp.getWorkOrderStatus();
            Long workOrderId = saleOrderFullResp.getWorkOrderId();
            SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO = new SaleTradePrintVO.SaleTradePrintTableVO();
            saleTradePrintTableVO.setId(workOrderId == null || workOrderId < 0 ? "" : workOrderId.toString());
            saleTradePrintTableVO.setStatus(Objects.equals(1, workOrderStatus) ? "已解决" : "未解决");
            //退款全不是直接开单
            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildSingleSaleTradeTableVos(staff, idToItem, idToSku, reissueOrders, null, dataSource, false,
                    saleTradePrintBO.getSaleTradeFullResp().getStatus(), false, itemCategoryIdNameMap, assoGoodsSectionSkuMap, hasMergePrint, orderProductMap);
            // 排序
            sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType, staff, outWarehouseId);
            // 序号
            fillTableNo(saleTradeTableVOS);
            saleTradePrintTableVO.setTableVOS(saleTradeTableVOS);
            // 计算箱数\销售合计\商品数量等合计字段
            getTableVOSAfterById(staff, saleTradePrintTableVO);
            printTableVOS.add(saleTradePrintTableVO);
        }
        return printTableVOS;
    }

    /**
     * 组装表格数据  明细维度
     */
    private void buildCrossItemProperties(SaleTradePrintVO saleTradePrintVo, Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku,
                                          SaleTradePrintBO saleTradePrintBO, Integer dataSource, Integer sortType, UserInvoicesTemplate template, Boolean hasMergePrint) {
        /*
          1 获取 根据sysItemId 分组的打印数据
         */
        List<SaleOrderFullResp> orders;//实际要打的数据
        Map<String, SaleOrderFullResp> skuReferenceOrdersMap = new HashMap<>();//作为参照的数据
        List<SaleOrderFullResp> returnOrders = null;//缺货数据
        boolean onlyOutOrder = false;//是否直接开单
        if (dataSource == 0) {
            //提交配货数据 不许要参照
            orders = saleTradePrintBO.getPlanSaleOrders();
            if (CollectionUtils.isEmpty(orders)) {
                orders = saleTradePrintBO.getOutSaleOrders();
            }
        } else if (dataSource == 1) {
            //实际出货数据 提交配货数缺货数要参照提交数据
            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutSaleOrders())) {
                // V2取销货单关联订单（未作废）的实际发货数量
                SaleTradeFullResp saleTradeFullResp = saleTradePrintBO.getSaleTradeFullResp();
                orders = saleTradePrintBO.getOutSaleOrders();
                if (saleTradeFullResp != null && Objects.equals(saleTradeFullResp.getStVersion(), 2)) {
                    orders = saleTradePrintBO.getOutSaleOrders().stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
                }
            } else {
                orders = saleTradePrintBO.getPlanSaleOrders();
            }
            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getPlanSaleOrders());
            if (skuReferenceOrdersMap.isEmpty()) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
        } else {
            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
            orders = saleTradePrintBO.getPlanSaleOrders();
            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getOutSaleOrders());
            if (CollectionUtils.isEmpty(orders)) {
                //没有提交配货数据视为直接开单 记录一下日志留下证据
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTradeFullResp().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
                orders = saleTradePrintBO.getOutSaleOrders();
                onlyOutOrder = true;
            }
            returnOrders = saleTradePrintBO.getReturnSaleOrders();
        }

        // 退货数据的价格和数量需要展示为负数
        if (CollectionUtils.isNotEmpty(returnOrders)) {
            for (SaleOrderFullResp saleOrderFullResp : returnOrders) {
                saleOrderFullResp.setPayment(saleOrderFullResp.getPayment() != null ? saleOrderFullResp.getPayment().negate() : getValue(null, 6).negate());           // 支付金额 = 单价*数量 - 优惠金额
                saleOrderFullResp.setNum(-saleOrderFullResp.getNum());                   // 数量
            }
            orders.addAll(returnOrders);
        }
        try {
            PrintSaleTradeTemplateHelper.buildNewCrossItemProperties(saleTradePrintVo, staff, idToItem, idToSku, orders, skuReferenceOrdersMap, dataSource, onlyOutOrder, template, hasMergePrint);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "获取销货单交叉数据异常 :" + e.getMessage()), e);
        }

    }

    /**
     * * 组装表格数据  明细维度
     *
     * @param idToItem              idToItem
     * @param idToSku               idToSku
     * @param orders                打印的数据源
     * @param skuReferenceOrdersMap 参照的map
     * @param dataSource            dataSource
     * @param isReturn              isReturn 是否是退货商品 退货商品 金额/数量 为负数
     * @param saleTradeStatus       销货单状态
     * @param onlyOutOrder          是否为直接开单
     */
    private List<SaleTradePrintVO.SaleTradeTableVO> buildSingleSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku, List<SaleOrderFullResp> orders,
                                                                                 Map<String, SaleOrderFullResp> skuReferenceOrdersMap, Integer dataSource,
                                                                                 boolean isReturn, Integer saleTradeStatus, boolean onlyOutOrder, Map<String, String> itemCategoryIdNameMap,
                                                                                 Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint, Map<String, List<OrderStockProduct>> orderProductMap) {
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>();
        Map<String, List<SaleOrderFullResp>> orderMap = orders.stream()
                .collect(Collectors.groupingBy(
                        order -> order.getItemSysId() + "_" + (order.getSkuSysId() == null || order.getSkuSysId() < 0 ? "0" : order.getSkuSysId()) + "_" + order.getPrice() + "_" + order.getNumPerBox(),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        for (List<SaleOrderFullResp> orderFullResps : orderMap.values()) {
            if (CollectionUtils.isEmpty(orderFullResps)) {
                continue;
            }
            SaleOrderFullResp saleOrderFullResp = orderFullResps.get(0);
            SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO = new SaleTradePrintVO.SaleTradeTableVO();
            saleTradeTableVOS.add(saleTradeTableVO);
            Long itemSysId = saleOrderFullResp.getItemSysId();
            Long skuSysId = saleOrderFullResp.getSkuSysId();
            DmjItem dmjItem = idToItem.get(itemSysId);
            DmjSku dmjSku = idToSku.get(skuSysId);
            if (dmjItem == null) {
                throw new IllegalArgumentException(String.format("未获取到商品! 请确认商品是否删除或发生变更，系统商品id:%s，商品编码:%s", itemSysId, saleOrderFullResp.getOuterId()));
            }
            if (dmjSku == null) {
                logger.debug(LogHelper.buildLog(staff, "商品id:" + itemSysId + "根据skuSysId:" + skuSysId + "未获取到商品规格!!!"));
            }
            String itemKey = itemSysId + "_" + (skuSysId == null || skuSysId < 0 ? "0" : skuSysId);
            //组装基本商品信息 和 规格信息
            buildSomeFields(staff, saleTradeTableVO, dmjItem, dmjSku, itemCategoryIdNameMap);
            //只能取order里的信息 合并时丢弃
            saleTradeTableVO.setTableItemPrice(getValue(saleOrderFullResp.getPrice(), 6).toString());//单价
            Double salePrice = dmjSku == null ? dmjItem.getPriceOutput() : dmjSku.getPriceOutput();
            //折扣率
            saleTradeTableVO.setTableItemDiscountRate(saleOrderFullResp.getPrice() != null && salePrice != null && salePrice != 0 ? BigDecimal.valueOf((saleOrderFullResp.getPrice().doubleValue() / salePrice) * 100D).setScale(2, RoundingMode.HALF_UP).toString() : " ");
            //折后价
            String discountPrice = saleOrderFullResp.getPayment() == null ? "0" : saleOrderFullResp.getPayment().divide(new BigDecimal(saleOrderFullResp.getNum()), 6, RoundingMode.HALF_UP).toString();
            saleTradeTableVO.setTableItemDiscountPrice(discountPrice);
            // 装箱要求
            saleTradeTableVO.setTablePackingAsk(saleOrderFullResp.getPackingAsk());
            saleTradeTableVO.setOuterIdOfItem(dmjItem.getOuterId());//主商家编码
            int itemTotalNum = 0; //商品数量
            StringBuilder tableSaleRemark = new StringBuilder(); //商品备注
            BigDecimal payment = new BigDecimal(0); //总价
            List<String> goodsSectionCodes = new ArrayList<>(); //商品货位
            Set<Long> assistUnitIdSet = new HashSet<>(); //单元
            Double assistUnitNum = 0D;
            String assistUnit = "";
            List<OrderStockProduct> orderStockProducts = new ArrayList<>(); //商品批次信息
            Long lackStockNum = 0L;
            for (SaleOrderFullResp order : orderFullResps) {
                String saleRemark = order.getSaleRemark();
                if (StringUtils.isNotBlank(saleRemark) && !tableSaleRemark.toString().contains(saleRemark)) {
                    tableSaleRemark.append(saleRemark).append(";");
                }
                //商品可合并信息
                int itemNum = order.getNum() == null ? 0 : isReturn ? -order.getNum() : order.getNum();
                itemTotalNum += itemNum;
                // 上游说不根据价格和数量计算了,直接取payment
                payment = payment.add(getValue(order.getPayment(), 6));
                // 拣选货位
                List<String> orderGoodsSectionCodes = order.getGoodsSectionCodes();
                if (CollectionUtils.isNotEmpty(orderGoodsSectionCodes)) {
                    goodsSectionCodes.addAll(orderGoodsSectionCodes);
                }
                Long assistUnitId = order.getAssistUnitId(); //存在没单位但是有数量的情况,所以不判断空了
                assistUnitIdSet.add(assistUnitId);
                Double orderAssistUnitNum = order.getAssistUnitNum();
                String orderAssistUnit = order.getAssistUnit();
                if (orderAssistUnitNum != null) {
                    assistUnitNum += orderAssistUnitNum;
                }
                if (StringUtils.isBlank(assistUnit) && StringUtils.isNotBlank(orderAssistUnit)) {
                    assistUnit = orderAssistUnit;
                }
                if (MapUtils.isNotEmpty(orderProductMap)) {
                    Long fullRespItemSysId = order.getItemSysId();
                    String itemProductKey = order.getSid() + "_" + fullRespItemSysId + "_" + (skuSysId == null || skuSysId < 1 ? -1 : skuSysId);
                    List<OrderStockProduct> stockProducts = orderProductMap.get(itemProductKey);
                    if (CollectionUtils.isNotEmpty(stockProducts)) {
                        orderStockProducts.addAll(stockProducts);
                    }
                    if (CollectionUtils.isNotEmpty(order.getMergeSids())) {
                        for (Long mergeSid : order.getMergeSids()) {
                            String mergeItemProductKey = mergeSid + "_" + fullRespItemSysId + "_" + (skuSysId == null || skuSysId < 1 ? -1 : skuSysId);
                            List<OrderStockProduct> mergeStockProducts = orderProductMap.get(mergeItemProductKey);
                            if (CollectionUtils.isNotEmpty(mergeStockProducts)) {
                                orderStockProducts.addAll(mergeStockProducts);
                            }
                        }
                    }
                }
                lackStockNum += order.getLackStockNum() == null ? 0L : order.getLackStockNum();
            }
            if (tableSaleRemark.length() > 0) {
                tableSaleRemark.deleteCharAt(tableSaleRemark.length() - 1);
            }
            saleTradeTableVO.setTableSaleRemark(String.valueOf(tableSaleRemark));
            saleTradeTableVO.setTableItemNum(Integer.toString(itemTotalNum));//商品数量
            saleTradeTableVO.setTableTotalGetterNum(Integer.toString(itemTotalNum));//合计数量
            // 销售总价合计
            buildTableSaleTotalAmount(staff, saleTradeTableVO);
            if (assistUnitIdSet.size() == 1) {
                saleTradeTableVO.setTableSaleAssistUnit(assistUnit); //销货单单据单位
                saleTradeTableVO.setTableSaleAssistUnitNum(Objects.equals(0D, assistUnitNum) ? "" : String.valueOf(assistUnitNum)); //销货单单据单位数量
            }
            saleTradeTableVO.setTableTotalAmount(isReturn ? payment.negate().toString() : payment.toString());//总价
            try {
                // 销售价优惠金额
                saleTradeTableVO.setTableDiscountAmount("***".equals(saleTradeTableVO.getTableSaleTotalAmount()) ? saleTradeTableVO.getTableSaleTotalAmount() :
                        new BigDecimal(saleTradeTableVO.getTableSaleTotalAmount()).subtract(isReturn ? payment.negate() : payment).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            } catch (NumberFormatException e) {
                saleTradeTableVO.setTableDiscountAmount("0");
                logger.error("销售价优惠金额 table_discount_amount 计算异常：", e);
            }
            if (assoGoodsSectionSkuMap.get(itemKey) != null) {
                saleTradeTableVO.setTableItemSectionCode(assoGoodsSectionSkuMap.get(itemKey).getGoodsSectionCode());//商品货位
            }
            if (CollectionUtils.isNotEmpty(goodsSectionCodes)) {
                // 处理拣选货位\货位数量
                buildPickingSectionMsg(goodsSectionCodes, saleTradeTableVO);
            }
            // 组装批发收银的数据
            int boxNum = saleOrderFullResp.getNumPerBox() == null ? 0 : saleOrderFullResp.getNumPerBox(); //箱数
            int calculateNum = itemTotalNum < 0 ? -itemTotalNum : itemTotalNum;
            saleTradeTableVO.setTableBoxnum(boxNum);
            if (!Objects.equals(0, boxNum)) {
                saleTradeTableVO.setTableNumOfBox(calculateNum / boxNum); //商品箱数
                saleTradeTableVO.setTableRemainBulkNum(calculateNum % boxNum); //剩余散装数
            } else {
                saleTradeTableVO.setTableRemainBulkNum(calculateNum); //剩余散装数
            }
            if (isReturn) {
                saleTradeTableVO.setReturnItemNum(String.valueOf(-itemTotalNum));
                saleTradeTableVO.setReturnItemNumFee(payment.toString());
            } else {
                saleTradeTableVO.setOutItemNum(String.valueOf(itemTotalNum));
                saleTradeTableVO.setOutItemNumFee(payment.toString());
            }
            // 组装唯一码
            buildUniqueCodeStr(orderFullResps, saleTradeTableVO);

            // 组装批次\生产日期
            buildOrderProductMsg(orderStockProducts, saleTradeTableVO);

            // 销售类型
            String saleTradeGoodsType = EnumSaleTradeGoodsType.getName(saleOrderFullResp.getType(),saleOrderFullResp.getGoodsType());
            saleTradeTableVO.setTableSaleGoodsType(saleTradeGoodsType);

            // 商品重量，sku维度，优先取sku
            saleTradeTableVO.setTableProductWeight(removeTrailingZeros(!isReturn && dmjSku != null && dmjSku.getWeight() != null ?
                    new BigDecimal(itemTotalNum).multiply(BigDecimal.valueOf(dmjSku.getWeight())) :
                    isReturn || dmjItem.getWeight() == null ? BigDecimal.ZERO : new BigDecimal(itemTotalNum).multiply(BigDecimal.valueOf(dmjItem.getWeight()))));

            // 开单缺货数
            saleTradeTableVO.setTableBillingInsufficientNum(String.valueOf(lackStockNum));

            if (!isReturn && !onlyOutOrder && !EnumSaleTradeGoodsType.EXCHANGE.getName().equals(saleTradeGoodsType)
                    && !EnumSaleTradeGoodsType.REISSUE.getName().equals(saleTradeGoodsType)) {
                //不是退货 且 不是直接开单的需要记录提交配货数和缺货数
                if (dataSource == 0) {
                    saleTradeTableVO.setTablePlanNum(Integer.toString(itemTotalNum));//提交配货数取商品数
                } else {
                    SaleOrderFullResp referenceOrder = skuReferenceOrdersMap.get(itemKey);
                    int referenceNum = referenceOrder == null || referenceOrder.getNum() == null ? 0 : referenceOrder.getNum();
                    // 上游说不根据价格和数量计算了,直接取payment
                    BigDecimal referenceTotalAmont = getValue(referenceOrder == null ? null : referenceOrder.getPayment(), 6);
                    if (dataSource == 1) {
                        saleTradeTableVO.setTablePlanNum(Integer.toString(referenceNum));//提交配货数取提交配货数
                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
                            saleTradeTableVO.setTableInsufficientNum(Integer.toString(referenceNum - itemTotalNum));//缺货数是提交数减去出货数
                        }
                    } else {
                        saleTradeTableVO.setTableItemNum(Integer.toString(referenceNum));//商品数量 取实际出货数量
                        saleTradeTableVO.setTableTotalAmount(referenceTotalAmont.toString());//总价 取实际出货总价
                        saleTradeTableVO.setTablePlanNum(Integer.toString(itemTotalNum));//提交配货数取提交配货数
                        saleTradeTableVO.setOutItemNum(Integer.toString(referenceNum));//商品数量 取实际出货数量
                        saleTradeTableVO.setOutItemNumFee(referenceTotalAmont.toString());//总价 取实际出货总价
                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
                            saleTradeTableVO.setTableInsufficientNum(Integer.toString(itemTotalNum - referenceNum));//缺货数是提交数减去出货数
                        }
                    }
                }
            }
        }
        return saleTradeTableVOS;
    }

    /**
     * @param saleTradeTableVO 打印vo
     * @param order            数据源
     * @param hasMergePrint    合并打印
     * @description 组装批发收银给的箱规、箱数、散装数
     * <AUTHOR>
     * @date 2024-04-17 19:50
     */
    private void assembleTradeTable(SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, SaleOrderFullResp order, Boolean hasMergePrint) {
        if (order == null) {
            return;
        }
        Integer numPerBox = order.getNumPerBox();
        Integer numOfBox = order.getNumOfBox();
        Integer bulkNum = order.getBulkNum();
        // 合并打印才处理是否计算
        if (hasMergePrint && numPerBox != null && numPerBox != 0 && bulkNum != 0 && bulkNum >= numPerBox) {
            numOfBox += bulkNum / numPerBox;
            bulkNum = bulkNum % numPerBox;
        }
        saleTradeTableVO.setTableBoxnum(numPerBox);
        saleTradeTableVO.setTableNumOfBox(numOfBox);
        saleTradeTableVO.setTableRemainBulkNum(bulkNum);
    }

    /**
     * 组装基本商品信息 和 规格信息
     */
    private void buildSomeFields(Staff staff, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, DmjItem dmjItem, DmjSku dmjSku, Map<String, String> itemCategoryIdNameMap) {
        //商品基本信息
        saleTradeTableVO.setTableItemPriceOutput(checkSalePower(staff) ? dmjItem.getPriceOutput() != null ? String.format("%.6f", dmjItem.getPriceOutput()) : "" : "***");//销售价
        saleTradeTableVO.setTableItemOutId(StringUtils.isBlank(dmjItem.getOuterId()) ? "" : dmjItem.getOuterId());//款号
        saleTradeTableVO.setTableItemTitle(StringUtils.isBlank(dmjItem.getTitle()) ? "" : dmjItem.getTitle());//商品名称
        saleTradeTableVO.setTableItemShortTitle(StringUtils.isBlank(dmjItem.getShortTitle()) ? "" : dmjItem.getShortTitle());//商品简称
        saleTradeTableVO.setTableItemRemark(StringUtils.isBlank(dmjItem.getRemark()) ? "" : dmjItem.getRemark());//商品备注
        saleTradeTableVO.setTableItemPic(StringUtils.isBlank(dmjItem.getPicPath()) ? "" : dmjItem.getPicPath());//商品图片
        saleTradeTableVO.setTableItemUnit(StringUtils.isBlank(dmjItem.getUnit()) ? "" : dmjItem.getUnit()); //商品计量单位
        // 商品分类
        saleTradeTableVO.setTableItemCategory(getItemCategory(dmjItem, itemCategoryIdNameMap));
        //商品类目
        saleTradeTableVO.setTableCatId(StringUtils.isBlank(dmjItem.getCatName()) ? "" : dmjItem.getCatName());
        // 商品条形码
        saleTradeTableVO.setTableItemBarcode(StringUtils.isBlank(dmjItem.getBarcode()) ? "" : dmjItem.getBarcode());
        // 规格条形码
        saleTradeTableVO.setTableSkuBarcode(dmjItem.getBarcode());
        //规格信息
        if (dmjSku != null) {
            String priceOutput = dmjSku.getPriceOutput() != null ? String.format("%.6f", dmjSku.getPriceOutput()) : "";
            saleTradeTableVO.setTableItemPriceOutput(checkSalePower(staff) ? priceOutput : "***");//销售价
            // 拆分商品数据 [第一、二、三、四属性]
            bulidSkuPropOther(dmjSku);

            JSONObject object = JSONObject.parseObject(dmjSku.getExtendFieldValues());
            if (object != null && StringUtils.isNotBlank(object.getString("marketPrice"))) {
                BigDecimal marketPrice = getNum(object.getString("marketPrice"), 6);
                saleTradeTableVO.setTableItemMarketPrice(checkPurchasePricePower(staff) ? marketPrice.toPlainString() : "***");//市场价
            }
            saleTradeTableVO.setTableSkuOuterId(StringUtils.isBlank(dmjSku.getSkuOuterId()) ? dmjSku.getItemOuterId() : dmjSku.getSkuOuterId());//规格商家编码
            // 和规格商家编码条码
            saleTradeTableVO.setTableSkuOuterIdJsBarCode(saleTradeTableVO.getTableSkuOuterId());
            saleTradeTableVO.setTableSkuName(StringUtils.isBlank(dmjSku.getPropertiesName()) ? "" : dmjSku.getPropertiesName());//规格名称
            saleTradeTableVO.setTableItemProperties(StringUtils.isBlank(dmjSku.getPropertiesAlias()) ? saleTradeTableVO.getTableItemProperties() : dmjSku.getPropertiesAlias());
            saleTradeTableVO.setTableSkuRemark(StringUtils.isBlank(dmjSku.getSkuRemark()) ? StringUtils.isBlank(dmjItem.getRemark()) ? "" : dmjItem.getRemark() : dmjSku.getSkuRemark());//规格备注
            saleTradeTableVO.setTableSkuPic((StringUtils.isBlank(dmjSku.getSkuPicPath()) || Objects.equals("/resources/css/build/images/no_pic.png", dmjSku.getSkuPicPath())) ? dmjItem.getPicPath() : dmjSku.getSkuPicPath());//规格图片
            saleTradeTableVO.setTableSkuUnit(StringUtils.isBlank(dmjSku.getUnit()) ? "" : dmjSku.getUnit()); //SKU计量单位
            // 商品成分
            saleTradeTableVO.setTableComponent(PrintMerchantCodeHelper.getItemComponentSplitLine(dmjSku.getSkuComponent()));
            // 颜色属性
            saleTradeTableVO.setTableColorProperty(dmjSku.getPropColor());
            // 其他属性
            saleTradeTableVO.setTableOtherProperty(dmjSku.getPropOtherFirst());
            // 规格条形码
            saleTradeTableVO.setTableSkuBarcode(StringUtils.isBlank(dmjSku.getBarcode()) ? dmjItem.getBarcode() : dmjSku.getBarcode());
        } else {
            JSONObject object = JSONObject.parseObject(dmjItem.getExtendFieldValues());
            if (object != null && StringUtils.isNotBlank(object.getString("marketPrice"))) {
                BigDecimal marketPrice = getNum(object.getString("marketPrice"), 6);
                saleTradeTableVO.setTableItemMarketPrice(checkPurchasePricePower(staff) ? marketPrice.toPlainString() : "***");//市场价
            }
            saleTradeTableVO.setTableItemProperties("");
            saleTradeTableVO.setTableSkuOuterId(dmjItem.getOuterId());//规格商家编码
            // 和规格商家编码条码
            saleTradeTableVO.setTableSkuOuterIdJsBarCode(dmjItem.getOuterId());
            saleTradeTableVO.setTableSkuName("");//规格名称
            saleTradeTableVO.setTableSkuRemark(StringUtils.isBlank(dmjItem.getRemark()) ? "" : dmjItem.getRemark());//规格备注
            saleTradeTableVO.setTableSkuPic(dmjItem.getPicPath());//无规格图片 取商品主图
            // 商品成分
            saleTradeTableVO.setTableComponent(PrintMerchantCodeHelper.getItemComponentSplitLine(dmjItem.getComponent()));
        }
    }

    /**
     * 装换 结账方式
     */
    private String conversionAccountType(int accountType) {
        switch (accountType) {
            case 1:
                return "现付";
            case 2:
                return "记账";
            case 3:
                return "部分收款";
            default:
                return "未知结账方式";
        }
    }

    /**
     * 组装付款信息
     */
    private String buildPayMessage(String payType, List<PayInfo> payInfos) {
        if (StringUtils.isBlank(payType)) {
            return "";
        }
        if (CollectionUtils.isEmpty(payInfos)) {
            return "";
        }
        StringBuilder payMessage = new StringBuilder();
        for (PayInfo payInfo : payInfos) {
            DecimalFormat decimalFormat = new DecimalFormat("#.######");
            String payFee = payInfo.getPayFee() == null ? "0" : decimalFormat.format(payInfo.getPayFee());
            payMessage.append(payInfo.getPayTypeName()).append(":").append(payFee);
            if (StringUtils.isNotBlank(payInfo.getSerialNumber())) {
                payMessage.append("流水号:").append(payInfo.getSerialNumber());
            }
            if (StringUtils.isNotBlank(payInfo.getPayName())) {
                payMessage.append("收款账号:").append(payInfo.getPayName());
            }
            payMessage.append(";");
        }

        if (payMessage.toString().endsWith(";")) {
            payMessage.deleteCharAt(payMessage.length() - 1);
        }
        return payMessage.toString();
    }

    /**
     * 填充序号
     */
    private void fillTableNo(List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS) {
        int num = 0;
        for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO : saleTradeTableVOS) {
            saleTradeTableVO.setTableNo(++num + "");
        }
    }

    /**
     * orders 根据商品id分组
     */
    private Map<String, SaleOrderFullResp> orders2skuMap(List<SaleOrderFullResp> saleOrderFullResps) {
        Map<String, SaleOrderFullResp> skuOrdersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(saleOrderFullResps)) {
            for (SaleOrderFullResp order : saleOrderFullResps) {
                String key = order.getItemSysId() + "_" + (order.getSkuSysId() == null || order.getSkuSysId() < 0 ? "0" : order.getSkuSysId());
                skuOrdersMap.put(key, order);
            }
        }
        return skuOrdersMap;
    }

    /**
     * orders 根据商品id分组
     */
    private Map<String, List<SaleOrderFullResp>> orders2ItemIdMap(List<SaleOrderFullResp> saleOrderFullResps, UserInvoicesTemplate template) {
        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
        Map<String, List<SaleOrderFullResp>> sysItemIdOrdersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(saleOrderFullResps)) {
            for (SaleOrderFullResp order : saleOrderFullResps) {
                Long itemSysId = order.getItemSysId();
                BigDecimal price = order.getPrice();
                String key;
                if (isV3Template) {
                    key = itemSysId + "_" + getValue(price, 6);
                } else {
                    key = itemSysId.toString();
                }
                List<SaleOrderFullResp> subOrders = sysItemIdOrdersMap.computeIfAbsent(key, k -> new ArrayList<>());
                subOrders.add(order);
            }
        }
        return sysItemIdOrdersMap;
    }

    /**
     * 表格 排序
     */
    private void sortSaleTradeTableVO(List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS, Integer sortType, String printSortPicker, Staff staff, Long outWarehouseId) {
        // 注意 用这种方式排序 用于排序的字段不能为null
        try {
            if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_OUT_ID_SKU_NAME.equals(sortType)) {
                //先款号再规格名称
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuName));
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_OUT_ID_SKU_OUT_ID.equals(sortType)) {
                //先款号再规格编码
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuOuterId));
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_REMARK_SKU_NAME.equals(sortType)) {
                //先主商品备注再规格名称
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemRemark).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuName));
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_SKU_OUT_ID.equals(sortType)) {
                //规格编码
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuOuterId));
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_SKU_REMARK.equals(sortType)) {
                //规格备注
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuRemark));
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_PICKING_SECTION_CODE.equals(sortType)) {
                WmsConfig wmsConfig = wmsService.getConfig(staff);
                // 按拣选货位编码排序
                if (Objects.equals("1", printSortPicker) && outWarehouseId != null) {
                    // 按拣选路径排序
                    PickGoodsRouteConfig pickGoodsRouteConfig = wmsService.getPickGoodsRouteConfig(staff);
                    List<Comparator<SaleTradePrintVO.SaleTradeTableVO>> comparators = Lists.newArrayListWithCapacity(3);
                    Optional<CustomSortRule> customSortRule = Optional.ofNullable(pickGoodsRouteConfig).map(PickGoodsRouteConfig::getDetails)
                            .flatMap(details -> details.stream().filter(d -> outWarehouseId.equals(d.getWarehouseId())).findFirst())
                            .map(PickGoodsRouteConfigDetail::getCustomSortRule);
                    if (customSortRule.isPresent()) {
                        final List<String> valuesInOrder = CustomSortUtils.getValuesInOrder(customSortRule.get());
                        if (CollectionUtils.isNotEmpty(valuesInOrder)) {
                            comparators.addAll(WmsUtils.getGoodsSectionSortComparators(wmsConfig, valuesInOrder, SaleTradePrintVO.SaleTradeTableVO::getSortSectionCode));
                        }
                    }
                    //add goodSectionCode sort
                    comparators.add((pre, next) -> WmsUtils.sortGoodsSectionCode(wmsConfig, pre.getSortSectionCode(), next.getSortSectionCode()));
                    saleTradeTableVOS.sort(Ordering.compound(comparators));
                } else {
                    saleTradeTableVOS.sort((o1, o2) -> WmsUtils.sortGoodsSectionCode(wmsConfig, o1.getSortSectionCode(), o2.getSortSectionCode()));
                }
            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_GOOD_SECTION_CODE.equals(sortType)) {
                WmsConfig wmsConfig = wmsService.getConfig(staff);
                saleTradeTableVOS.sort((o1, o2) -> WmsUtils.sortGoodsSectionCode(wmsConfig, o1.getTableItemSectionCode(), o2.getTableItemSectionCode()));
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("排序出错!" + e.getMessage(), e);
        }
    }

    private String buildSalePaymentMsg(List<PayInfo> payInfoList) {
        if (CollectionUtils.isEmpty(payInfoList)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        List<PayInfo> payInfos = payInfoList.stream().filter(x -> x.getPayFee() != null).collect(Collectors.toList());
        for (PayInfo payInfo : payInfos) {
            DecimalFormat decimalFormat = new DecimalFormat("#.######");
            String payFee = payInfo.getPayFee() == null ? "0" : decimalFormat.format(payInfo.getPayFee());
            if (payInfo.getPayType() == null) {
                continue;
            }
            if (payInfo.getPayType() == 1) {
                sb.append("现金支付(");
                sb.append(payFee);
                sb.append(")");
                sb.append(",");
            }
            if (payInfo.getPayType() == 2) {
                sb.append("支付宝支付(");
                sb.append(payFee);
                sb.append(")");
                sb.append(",");
            }
            if (payInfo.getPayType() == 3) {
                sb.append("微信支付(");
                sb.append(payFee);
                sb.append(")");
                sb.append(",");
            }
            if (payInfo.getPayType() == 5) {
                sb.append("余额支付(");
                sb.append(payFee);
                sb.append(")");
                sb.append(",");
            }
            if (payInfo.getPayType() == 9) {
                sb.append("在线支付(");
                sb.append(payFee);
                sb.append(")");
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 拼接数据
     */
    private String joinMsg(List msgs) {
        StringBuilder sb = new StringBuilder();
        for (Object msg : msgs) {
            if (msg != null) {
                sb.append(msg.toString()).append(",");
            }
        }
        if (sb.toString().endsWith(",")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 获取商品分类
     */
    private String getItemCategory(DmjItem dmjItem, Map<String, String> itemCategoryIdNameMap) {
        //查询商品分类数据
        if (null == dmjItem) {
            return PtConfigConst.UNDEFINE_CATEGORY;
        } else {
            String sellerCids = dmjItem.getSellerCids();
            //只有一级分类
            if (sellerCids == null || !sellerCids.contains(PtConfigConst.ENG_COMMA)) {
                String cName = itemCategoryIdNameMap.get(sellerCids);
                return StringUtils.isEmpty(cName) ? PtConfigConst.UNDEFINE_CATEGORY : cName;
            } else {
                //多个分类
                String[] cids = sellerCids.split(PtConfigConst.ENG_COMMA);
                List<String> cNameList = Lists.newArrayList();
                for (String cid : cids) {
                    String cName = itemCategoryIdNameMap.get(cid);
                    cNameList.add(StringUtils.isEmpty(cName) ? PtConfigConst.UNDEFINE_CATEGORY : cName);
                }
                return StringUtils.join(cNameList, PtConfigConst.ENG_COMMA);
            }
        }
    }

    /**
     * 获取分销商信息
     */
    private Map<Long, DmsBaseDistributorInfoDto> getDistributorMap(Staff staff, List<Long> distributorIds) {
        // 分销商信息
        Map<Long, DmsBaseDistributorInfoDto> distributorMap = new HashMap<>();
        if (!distributorIds.isEmpty()) {
            DmsQueryDistributorInfoRequest request = new DmsQueryDistributorInfoRequest();
            request.setDistributorCompanyIdList(distributorIds);
            request.setSupplierCompanyId(staff.getCompanyId());
            if (distributorIds.size() > 20) {
                request.setPage(new Page().setPageSize(distributorIds.size()));
            }
            DmsDistributorInfoResponse response = dmsTradeService.queryDistributorInfo(request);
            if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getDmsBaseDistributorInfoDtoList().getList())) {
                distributorMap = response.getDmsBaseDistributorInfoDtoList().getList().stream().collect(
                        Collectors.toMap(DmsBaseDistributorInfoDto::getDistributorCompanyId, p -> p, (v1, v2) -> v2));
            } else {
                logger.debug(LogHelper.buildLogHead(staff).append("获取分销商信息失败:").append(response.getErrorMsg()));
            }
        }
        return distributorMap;
    }

    /**
     * 获取商品分类
     */
    private Map<String, String> getItemCategoryIdNameMap(Staff staff, Map<Long, DmjItem> idToItem) {
        Map<String, String> itemCategoryIdNameMap = new HashMap<>();
        Set<Long> itemCategoryIdSet = Sets.newHashSet();
        idToItem.forEach((key, dmjItem) -> PrintItemUtils.handleItemCategory(dmjItem.getSellerCids(), itemCategoryIdSet, itemCategoryIdNameMap));
        List<Long> itemCategoryIdList = Lists.newArrayList(itemCategoryIdSet);
        PrintItemUtils.queryItemCategory(staff, itemCategoryIdList, itemCategoryIdNameMap, printPageSearchService);
        return itemCategoryIdNameMap;
    }

    /**
     * 拼接分销商
     */
    private String buildDistributorAddress(DmsBaseDistributorInfoDto distributorInfo) {
        String separator = " ";
        StringBuilder address = new StringBuilder();
        if (StringUtils.isNotBlank(distributorInfo.getContactProvince())) {
            address.append(distributorInfo.getContactProvince()).append(separator);
        }
        if (StringUtils.isNotBlank(distributorInfo.getContactCity())) {
            address.append(distributorInfo.getContactCity()).append(separator);
        }
        if (StringUtils.isNotBlank(distributorInfo.getContactDistrict())) {
            address.append(distributorInfo.getContactDistrict()).append(separator);
        }
        if (StringUtils.isNotBlank(distributorInfo.getContactDetailAddress())) {
            address.append(distributorInfo.getContactDetailAddress()).append(separator);
        }
        if (address.length() > 0) {
            return address.substring(0, address.length() - 1);
        }
        return address.toString();
    }

    //拆分商品数据 [第一、二、三、四属性]
    private void bulidSkuPropOther(DmjSku dmjSku) {
        if (dmjSku.getPropOther() == null) return;
        String[] propOthers = dmjSku.getPropOther().split(";");
        if (propOthers.length > 1) {
            dmjSku.setPropOtherFirst(propOthers[0]);
            dmjSku.setPropOtherSecond(propOthers[1]);
            if (propOthers.length > 2) {
                dmjSku.setPropOtherThird(propOthers[2]);
            }
        } else {
            dmjSku.setPropOtherFirst(dmjSku.getPropOther().replaceAll(";", ""));
        }
    }

    /**
     * 通过templateId和templateType查找template
     * 再给trade设置templateName
     */
    private void setTemplateName(Staff staff, Map<Long, Trade> tradeMap) {
        for (Map.Entry<Long, Trade> tradeEntry : tradeMap.entrySet()) {
            Trade trade = tradeEntry.getValue();
            Long templateId = trade.getTemplateId();
            if (templateId != null) {
                Integer templateType = trade.getTemplateType();
                IExpressTemplateBase template;
                if (0 == templateType) {
                    template = userTemplateService.userQuery(staff, templateId, false);
                } else {
                    template = wlbTemplateService.userQuery(staff, templateId, false);
                }
                if (template != null) {
                    trade.setTemplateName(template.getName());
                } else {
                    logger.debug(LogHelper.buildLog(staff, String.format("订单[%s]快递模板[%s,%s]找不到", trade.getSid(), trade.getTemplateId(), trade.getTemplateType())));
                }
            } else {
                logger.debug(LogHelper.buildLog(staff, String.format("订单[%s]的templateId为空", trade.getSid())));
            }
        }
    }

    private void buildItemSearch(List<SaleOrderFullResp> orders, Map<Long, Set<Long>> itemIdMap) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (SaleOrderFullResp order : orders) {
            if (order.getItemSysId() != null) {
                itemIdMap.computeIfAbsent(order.getItemSysId(), v -> new HashSet<>()).add(order.getSkuSysId() == null || order.getSkuSysId() < 0 ? 0 : order.getSkuSysId());
            }
        }
    }

    /**
     * 获取商品货位
     */
    private Map<String, AssoGoodsSectionSku> getAssoGoodsSectionSkuMap(Staff staff, List<Long> warehouseIds, Map<Long, Set<Long>> itemIdMap) {
        Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap = new HashMap<>();
        AssoGoodsSectionSkuParams skuParams = new AssoGoodsSectionSkuParams();
        List<Long> itemIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        for (Map.Entry<Long, Set<Long>> entry : itemIdMap.entrySet()) {
            itemIds.add(entry.getKey());
            skuIds.addAll(entry.getValue());
        }
        skuParams.setWarehouseIds(warehouseIds);
        skuParams.setSysItemIds(itemIds);
        skuParams.setSysSkuIds(skuIds);
        // 查询货位
        List<AssoGoodsSectionSku> skuList = wmsService.queryAssoGoodsSectionSkuList(staff, skuParams);

        if (CollectionUtils.isNotEmpty(skuList)) {
            //查询仓储的配置,读取货位编码显示配置
            WmsConfig config = wmsService.getConfig(staff);
            //根据显示配置显示,重新设值
            for (AssoGoodsSectionSku assoGoodsSectionSku : skuList) {
                assoGoodsSectionSku.setGoodsSectionCode(assoGoodsSectionSku.getGoodsSectionCode() == null ? "" : WmsUtils.encodeGsCode(config, assoGoodsSectionSku.getGoodsSectionCode()));
            }
            assoGoodsSectionSkuMap.putAll(skuList.stream().collect(Collectors.toMap(t -> t.getSysItemId() + "_" + (t.getSysSkuId() == null ? "0" : t.getSysSkuId()), sku -> sku, ((k1, k2) -> k1))));
        }
        return assoGoodsSectionSkuMap;
    }

    /**
     * 销售总价合计
     */
    private void buildTableSaleTotalAmount(Staff staff, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
        if (checkSalePower(staff)) {
            BigDecimal price = getNum(saleTradeTableVO.getTableItemPriceOutput(), 6);
            saleTradeTableVO.setTableSaleTotalAmount(price.multiply(new BigDecimal(saleTradeTableVO.getTableItemNum())).toString());
        } else {
            saleTradeTableVO.setTableSaleTotalAmount("***");
        }
    }

    /**
     * 销售总价合计
     */
    private void buildSaleTotalAmount(Staff staff, SaleTradePrintVO saleTradePrintVO) {
        BigDecimal total = new BigDecimal(0);
        if (checkSalePower(staff) && saleTradePrintVO.getSaleTradeTableVos() != null) {
            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO : saleTradePrintVO.getSaleTradeTableVos()) {
                total = total.add(new BigDecimal(saleTradeTableVO.getTableSaleTotalAmount()));
            }
            saleTradePrintVO.setSaleTotalAmount(total.toString());
        }
    }

    private void calculateBox(SaleTradePrintVO saleTradePrintVO, Boolean hasMergePrint) {
        int boxNum = 0;
        int remainBulkNum = 0;
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVos = saleTradePrintVO.getSaleTradeTableVos();
        if (CollectionUtils.isNotEmpty(saleTradeTableVos)) {
            Integer tableBoxnum = saleTradeTableVos.get(0).getTableBoxnum();
            // 所以箱规一样则计算箱数汇总
            boolean b = true;
            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVo : saleTradeTableVos) {
                boxNum += Optional.ofNullable(saleTradeTableVo.getTableNumOfBox()).orElse(0);
                remainBulkNum += Optional.ofNullable(saleTradeTableVo.getTableRemainBulkNum()).orElse(0);
                if (b && tableBoxnum == null) {
                    b = false;
                } else if (b) {
                    b = tableBoxnum.equals(saleTradeTableVo.getTableBoxnum());
                }
            }
            // 单独打印无需累加，若散装数大于或等于箱规，散装数转为箱数，并累加
            if (b && tableBoxnum != 0 && remainBulkNum != 0 && remainBulkNum >= tableBoxnum) {
                boxNum += remainBulkNum / tableBoxnum;
                remainBulkNum = remainBulkNum % tableBoxnum;
            }
        }
        if (Objects.equals(0, boxNum) && Objects.equals(0, remainBulkNum)) {
            saleTradePrintVO.setSaleOutBoxTotal("0箱");
        } else if (Objects.equals(0, remainBulkNum)) {
            saleTradePrintVO.setSaleOutBoxTotal(boxNum + "箱");
        } else {
            saleTradePrintVO.setSaleOutBoxTotal(boxNum + "箱+" + remainBulkNum);
        }
        saleTradePrintVO.setItemNumOfBoxTotal(Integer.toString(boxNum));
        saleTradePrintVO.setItemRemainBulkNumTotal(Integer.toString(remainBulkNum));
    }

    /**
     * 交叉结构V3数据的处理
     */
    private void buildV3ColorAndOthers(Staff staff, Long itemSysId, List<SaleOrderFullResp> orders, Map<Long, DmjSku> idToSku, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap) {
        Map<String, Map<String, Integer>> colorOtherPropNum = new HashMap<>();
        StringBuilder tableSaleRemark = new StringBuilder();
        for (SaleOrderFullResp order : orders) {
            Long skuSysId = order.getSkuSysId();
            DmjSku dmjSku = idToSku.get(skuSysId);
            String propOther = "";
            String propColor = "";
            if (dmjSku == null) {
                logger.debug(LogHelper.buildLog(staff, "商品id:" + itemSysId + "根据skuSysId:" + skuSysId + "未获取到商品规格!!!"));
            } else {
                propOther = StringUtils.isBlank(dmjSku.getPropOther()) ? "无" : dmjSku.getPropOther().replaceAll(";", "").toUpperCase();
                propColor = dmjSku.getPropColor();
                itemSysId = dmjSku.getSysItemId();
            }
            colorOtherPropNum.computeIfAbsent(propColor, k -> new HashMap<>());
            Map<String, Integer> otherPropNum = colorOtherPropNum.get(propColor);
            Integer otherNum = otherPropNum.getOrDefault(propOther, 0);
            otherPropNum.put(propOther, otherNum + order.getNum());
            String saleRemark = order.getSaleRemark();
            if (StringUtils.isNotBlank(saleRemark) && !tableSaleRemark.toString().contains(saleRemark)) {
                tableSaleRemark.append(saleRemark).append(";");
            }
        }
        if (tableSaleRemark.length() > 0) {
            tableSaleRemark.deleteCharAt(tableSaleRemark.length() - 1);
        }
        List<SaleTradePrintVO.SaleTradeTableDetailVO> tableDetailVOS = new ArrayList<>();
        Set<String> allOtherName = new HashSet<>();
        // V3新结构,每个商品一行,一行中有颜色尺寸的表格数据
        for (Map.Entry<String, Map<String, Integer>> colorEntry : colorOtherPropNum.entrySet()) {
            SaleTradePrintVO.SaleTradeTableDetailVO tableDetailVO = new SaleTradePrintVO.SaleTradeTableDetailVO();
            tableDetailVOS.add(tableDetailVO);
            String color = colorEntry.getKey();
            Map<String, Integer> otherNumMap = colorEntry.getValue();
            List<SaleTradePrintVO.TablePropOther> tablePropOthers = new ArrayList<>();
            for (Map.Entry<String, Integer> otherEntry : otherNumMap.entrySet()) {
                String otherName = otherEntry.getKey();
                SaleTradePrintVO.TablePropOther propOther = new SaleTradePrintVO.TablePropOther();
                tablePropOthers.add(propOther);
                propOther.setTablePropOtherName(otherName);
                propOther.setTablePropOtherNum(otherEntry.getValue() == null ? "0" : otherEntry.getValue().toString());
                allOtherName.add(otherName);
            }
            tableDetailVO.setTablePropColorName(color);
            tableDetailVO.setTablePropOthers(tablePropOthers);
        }
        List<String> otherPropNameList = new ArrayList<>(allOtherName);
        // 尺码排序
        PrintTemplateHelper.otherSort(otherPropNameList);
        // 默认颜色排序
        tableDetailVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableDetailVO::getTablePropColorName, Collator.getInstance(Locale.CHINA)));
        saleTradeTableVO.setSaleTradeTableDetailVos(tableDetailVOS); //颜色尺寸属性
        saleTradeTableVO.setTablePropOtherNames(otherPropNameList); //该商品下所有的尺寸
        String assoGoodSectionKey = itemSysId + "_0";
        if (assoGoodsSectionSkuMap.get(assoGoodSectionKey) != null) {
            saleTradeTableVO.setTableItemSectionCode(assoGoodsSectionSkuMap.get(assoGoodSectionKey).getGoodsSectionCode());//商品货位
        }
        saleTradeTableVO.setTableSaleRemark(String.valueOf(tableSaleRemark)); //销货单商品备注
    }

    /**
     * 排序
     */
    private void sortSaleTradeTableVOS(UserInvoicesTemplate template, List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS, Integer sortType, Staff staff, Long outWarehouseId) {
        Integer specificSortType = PrintTemplateHelper.getSpecificSortType(template.getFieldValues());
        // 按拣选路径参与排序
        String printSortPicker = StringUtils.defaultString(PrintTemplateHelper.getFieldValueByKey(template.getFieldValues(), EnumFieldValueName.PRINT_SORT_PICKER.getValue()), "0");
        if (EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue()) {
            // V3只有两种排序,默认排序和按商家编码
            if (specificSortType != null && StockConstants.ACCORDING_NUMBER_SORT == specificSortType) {
                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getOuterIdOfItem));
            }
        } else if (EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() ||
                EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue()) {
            // V1/V2有三种排序,默认排序,按货号排序,按货号再按颜色排序
            if (specificSortType != null) {
                if (StockConstants.ACCORDING_NUMBER_SORT == specificSortType) {
                    saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId));
                } else if (StockConstants.ACCORDING_NUMBER_COLOR_SORT == specificSortType) {
                    saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableColorProperty, Collator.getInstance(Locale.CHINA)));
                }
            }
        } else {
            // 非交叉结构的才走下面这个排序
            sortSaleTradeTableVO(saleTradeTableVOS, sortType, printSortPicker, staff, outWarehouseId);
        }
    }

    public void buildUniqueCodeStr(List<SaleOrderFullResp> saleOrderFullResps, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
        if (CollectionUtils.isEmpty(saleOrderFullResps)) {
            saleTradeTableVO.setTableUniqueCode("");
        }
        List<String> uniqueCodeList = new ArrayList<>();
        for (SaleOrderFullResp saleOrderFullResp : saleOrderFullResps) {
            List<String> uniqueCodes = saleOrderFullResp.getUniqueCodes();
            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
                uniqueCodeList.addAll(uniqueCodes);
            }
        }
        saleTradeTableVO.setTableUniqueCode(CollectionUtils.isNotEmpty(uniqueCodeList) ? Strings.join(",", uniqueCodeList) : "");
    }

    /**
     * 合并打印的时候,将唯一码\货位合并
     */
    public void mergeSomeData(SaleOrderFullResp mergeOrder, SaleOrderFullResp order) {
        // 合并唯一码
        List<String> uniqueCodeList = new ArrayList<>();
        List<String> mergeOrderUniqueCodes = mergeOrder.getUniqueCodes();
        if (CollectionUtils.isNotEmpty(mergeOrderUniqueCodes)) {
            uniqueCodeList.addAll(mergeOrderUniqueCodes);
        }
        List<String> uniqueCodes = order.getUniqueCodes();
        if (CollectionUtils.isNotEmpty(uniqueCodes)) {
            uniqueCodeList.addAll(uniqueCodes);
        }
        mergeOrder.setUniqueCodes(uniqueCodeList);
        // 合并货位
        List<String> allGoodsSectionCodes = new ArrayList<>();
        List<String> mergeGoodsSectionCodes = mergeOrder.getGoodsSectionCodes();
        if (CollectionUtils.isNotEmpty(mergeGoodsSectionCodes)) {
            allGoodsSectionCodes.addAll(mergeGoodsSectionCodes);
        }
        List<String> goodsSectionCodes = order.getGoodsSectionCodes();
        if (CollectionUtils.isNotEmpty(goodsSectionCodes)) {
            allGoodsSectionCodes.addAll(goodsSectionCodes);
        }
        mergeOrder.setGoodsSectionCodes(allGoodsSectionCodes);
    }

    /**
     * 处理上次累计欠款字段
     */
    private void processLastTimeTotalDebt(MergeSaleTradeBo mergeSaleTradeBo, List<SaleTradeFullResp> saleTradeFullResps, int earliestTimeTotalDebt) {
        SaleTradeFullResp saleTradeFullResp;
        if (earliestTimeTotalDebt == 1) {
            // 显示最早一个销货单
            saleTradeFullResp = saleTradeFullResps.stream().min(Comparator.comparing(SaleTradeFullResp::getCheckoutTime, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SaleTradeFullResp::getCreated, Comparator.nullsLast(Comparator.naturalOrder()))).orElse(null);
            if (saleTradeFullResp != null) {
                // 上次累计欠款取最早销货单，本次欠款取所有被选单据汇总数
                BigDecimal reduce = saleTradeFullResps.stream().map(SaleTradeFullResp::getNowDebt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                saleTradeFullResp.setNowDebt(reduce);
                // 累计欠款取最新的
                SaleTradeFullResp saleTradeMax = saleTradeFullResps.stream().max(Comparator.comparing(SaleTradeFullResp::getCheckoutTime, Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(SaleTradeFullResp::getCreated, Comparator.nullsFirst(Comparator.naturalOrder()))).orElse(null);
                saleTradeFullResp.setTotalDebt(saleTradeMax == null ? null : saleTradeMax.getTotalDebt());
            }
        } else {
            // 显示最后一个销货单
            saleTradeFullResp = saleTradeFullResps.stream().max(Comparator.comparing(SaleTradeFullResp::getCheckoutTime, Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(SaleTradeFullResp::getCreated, Comparator.nullsFirst(Comparator.naturalOrder()))).orElse(null);
        }
        if (saleTradeFullResp == null) {
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        mergeSaleTradeBo.setCheckoutTime(sdf.format(saleTradeFullResp.getCheckoutTime()));
        mergeSaleTradeBo.setLastTimeTotalDebt(DataFormatUtils.formatStr(saleTradeFullResp.getLastTimeTotalDebt(), "0.00"));
        mergeSaleTradeBo.setNowDebt(DataFormatUtils.formatStr(saleTradeFullResp.getNowDebt(), "0.00"));
        mergeSaleTradeBo.setTotalDebt(DataFormatUtils.formatStr(saleTradeFullResp.getTotalDebt(), "0.00"));
        mergeSaleTradeBo.setNowTotalDebt(DataFormatUtils.formatStr(saleTradeFullResp.getCurrentTotalDebt(), "0.00"));
    }


    private String getOrderMergeKey(Long itemSysId, Long skuSysId, Integer numPerBox) {
        return itemSysId + "" +
                (skuSysId == null || skuSysId < 1 ? "-1" : skuSysId) +
                // 拼接箱规
                (numPerBox == null ? "" : numPerBox);
    }

    /**
     * 合并列表明细  同一个规格 切 单价相同 数量累加 备注拼接
     */
    public List<SaleOrderFullResp> mergeOrder(List<SaleOrderFullResp> orders, int tableShowStatus) {
        Map<String, SaleOrderFullResp> mergeMap = new HashMap<>();
        Map<String, Set<Long>> assistUnitIdMap = new HashMap<>();
        for (SaleOrderFullResp saleOrderFullResp : orders) {
            String key = getOrderMergeKey(saleOrderFullResp.getItemSysId(), saleOrderFullResp.getSkuSysId(), saleOrderFullResp.getNumPerBox());
            // 模板设置表格展示是单据,那么相同商品合并的时候还要判断是不是同一个单据
            //KMERP-248292去掉这个逻辑
            //Long sid = saleOrderFullResp.getSid();
            //key = Objects.equals(1, tableShowStatus) ? key + "_" + (sid == null || sid <= 0 ? saleOrderFullResp.getWorkOrderId() == null ? "-1" : saleOrderFullResp.getWorkOrderId() : sid) : key;

            SaleOrderFullResp mergeOrder = mergeMap.get(key);
            assistUnitIdMap.computeIfAbsent(key, t -> new HashSet<>()).add(saleOrderFullResp.getAssistUnitId());
            if (mergeOrder == null) {
                mergeOrder = saleOrderFullResp;
                mergeMap.put(key, mergeOrder);
            } else {
                int itemNum = saleOrderFullResp.getNum() == null ? 0 : saleOrderFullResp.getNum();
                int mergeNum = (mergeOrder.getNum() == null ? 0 : mergeOrder.getNum()) + itemNum;
                mergeOrder.setNum(mergeNum);
                // 合并总金额,totalFee用payment就好了
//                BigDecimal totalFee = saleOrderFullResp.getTotalFee() != null ? saleOrderFullResp.getTotalFee() : BigDecimal.ZERO;
//                BigDecimal mergeTotalFee = mergeOrder.getTotalFee() != null ? mergeOrder.getTotalFee() : BigDecimal.ZERO;
//                mergeOrder.setTotalFee(mergeTotalFee.add(totalFee));
                // 合并支付金额
                BigDecimal payment = saleOrderFullResp.getPayment() != null ? saleOrderFullResp.getPayment() : BigDecimal.ZERO;
                BigDecimal mergePayment = mergeOrder.getPayment() != null ? mergeOrder.getPayment() : BigDecimal.ZERO;
                mergeOrder.setPayment(mergePayment.add(payment));
                String orderRemark = saleOrderFullResp.getSaleRemark() == null ? "" : saleOrderFullResp.getSaleRemark();
                String mergeRemark = mergeOrder.getSaleRemark() == null ? "" : mergeOrder.getSaleRemark();
                if (StringUtils.isNotBlank(orderRemark) && StringUtils.isNotBlank(mergeRemark)) {
                    mergeOrder.setSaleRemark(mergeRemark + "," + orderRemark);
                } else {
                    mergeOrder.setSaleRemark(StringUtils.isNotBlank(orderRemark) ? orderRemark : mergeRemark);
                }
                if (assistUnitIdMap.get(key) != null) {
                    if (assistUnitIdMap.get(key).size() == 1) {
                        Double orderAssistUnitNum = mergeOrder.getAssistUnitNum() == null ? 0D : mergeOrder.getAssistUnitNum();
                        Double assistUnitNum = saleOrderFullResp.getAssistUnitNum() == null ? orderAssistUnitNum : orderAssistUnitNum + saleOrderFullResp.getAssistUnitNum();
                        mergeOrder.setAssistUnitNum(assistUnitNum);
                    } else {
                        mergeOrder.setAssistUnitId(null);
                        mergeOrder.setAssistUnit("");
                        mergeOrder.setAssistUnitNum(null);
                    }
                }
                // 合并唯一码\货位
                mergeSomeData(mergeOrder, saleOrderFullResp);
                List<Long> mergeSids = mergeOrder.getMergeSids();
                if (CollectionUtils.isEmpty(mergeSids)) {
                    mergeSids = new ArrayList<>();
                    mergeOrder.setMergeSids(mergeSids);
                }
                mergeSids.add(saleOrderFullResp.getSid());
            }
        }
        return new ArrayList<>(mergeMap.values());
    }

    public void getTableVOSAfter(Staff staff, SaleTradePrintVO saleTradePrintVo) {
        int boxNum = 0;
        int remainBulkNum = 0;
        BigDecimal total = new BigDecimal(0);
        Boolean checkSalePower = checkSalePower(staff);
        int outItemNumTotal = 0; //出货商品总数
        int returnItemNumTotal = 0; //退货商品总数
        int exchangeItemNumTotal = 0; //换货商品总数
        int reissueItemNumTotal = 0; //补发商品总数
        BigDecimal exchangeItemNumTotalFee = new BigDecimal(0); //换货总金额
        BigDecimal reissueItemNumTotalFee = new BigDecimal(0); //补货总金额
        BigDecimal outItemNumTotalFee = new BigDecimal(0); //出货总金额
        BigDecimal returnItemNumTotalFee = new BigDecimal(0); //退货总金额
        BigDecimal itemDiscountTotalAmount = new BigDecimal(0); //商品销售价优惠总金额
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVos = saleTradePrintVo.getSaleTradeTableVos();
        BigDecimal totalShippingWeight = new BigDecimal(0); // 预估出货总重量
        BigDecimal itemSaleTotalAmount = new BigDecimal(0); // 商品销售价总金额(需正负值相抵,退货商品销售价金额为负数)
        if (CollectionUtils.isNotEmpty(saleTradeTableVos)) {
            Integer tableBoxnum = saleTradeTableVos.get(0).getTableBoxnum();
            // 所以箱规一样则计算箱数汇总
            boolean b = true;
            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVo : saleTradeTableVos) {
                BigDecimal priceOutput = checkSalePower(staff) ? new BigDecimal(saleTradeTableVo.getTableItemPriceOutput()) : BigDecimal.ZERO;
                total = checkSalePower ? total.add(new BigDecimal(saleTradeTableVo.getTableSaleTotalAmount())) : total;
                boxNum += Optional.ofNullable(saleTradeTableVo.getTableNumOfBox()).orElse(0);
                remainBulkNum += Optional.ofNullable(saleTradeTableVo.getTableRemainBulkNum()).orElse(0);
                if (b && tableBoxnum == null) {
                    b = false;
                } else if (b) {
                    b = tableBoxnum.equals(saleTradeTableVo.getTableBoxnum());
                }
                int outItemNum = StringUtils.isNotBlank(saleTradeTableVo.getOutItemNum()) ? Integer.parseInt(saleTradeTableVo.getOutItemNum()) : 0;
                if (EnumSaleTradeGoodsType.EXCHANGE.getName().equals(saleTradeTableVo.getTableSaleGoodsType())) {
                    // 换货
                    exchangeItemNumTotal += outItemNum;
                    exchangeItemNumTotalFee = exchangeItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                } else if (EnumSaleTradeGoodsType.REISSUE.getName().equals(saleTradeTableVo.getTableSaleGoodsType())) {
                    // 补发
                    reissueItemNumTotal += outItemNum;
                    reissueItemNumTotalFee = reissueItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                } else {
                    // 出货
                    outItemNumTotal += outItemNum;
                    outItemNumTotalFee = outItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                }
                itemSaleTotalAmount = itemSaleTotalAmount.add(priceOutput.multiply(BigDecimal.valueOf(outItemNum))); // 需要计算一次
                int returnItemNum = StringUtils.isNotBlank(saleTradeTableVo.getReturnItemNum()) ? Integer.parseInt(saleTradeTableVo.getReturnItemNum()) : 0;
                returnItemNumTotal += returnItemNum;
                returnItemNumTotalFee = returnItemNumTotalFee.add(getNum(saleTradeTableVo.getReturnItemNumFee(), 6));
                itemDiscountTotalAmount = checkSalePower ? itemDiscountTotalAmount.add(getNum(saleTradeTableVo.getTableDiscountAmount(), 6)) : itemDiscountTotalAmount;
                totalShippingWeight = totalShippingWeight.add(new BigDecimal(saleTradeTableVo.getTableProductWeight()));
                itemSaleTotalAmount = itemSaleTotalAmount.add(priceOutput.multiply(BigDecimal.valueOf(returnItemNum).negate()));
            }
            // 单独打印无需累加，若散装数大于或等于箱规，散装数转为箱数，并累加
            if (b && tableBoxnum != 0 && remainBulkNum != 0 && remainBulkNum >= tableBoxnum) {
                boxNum += remainBulkNum / tableBoxnum;
                remainBulkNum = remainBulkNum % tableBoxnum;
            }
        }
        if (Objects.equals(0, boxNum) && Objects.equals(0, remainBulkNum)) {
            saleTradePrintVo.setSaleOutBoxTotal("0箱");
        } else if (Objects.equals(0, remainBulkNum)) {
            saleTradePrintVo.setSaleOutBoxTotal(boxNum + "箱");
        } else {
            saleTradePrintVo.setSaleOutBoxTotal(boxNum + "箱+" + remainBulkNum);
        }
        // 换算箱汇总数\商品箱数合计\商品剩余散装数合计
        saleTradePrintVo.setItemNumOfBoxTotal(Integer.toString(boxNum));
        saleTradePrintVo.setItemRemainBulkNumTotal(Integer.toString(remainBulkNum));
        // 销售总价合计
        saleTradePrintVo.setSaleTotalAmount(checkSalePower ? total.toString() : BigDecimal.ZERO.toString());
        // 出货商品总数
        saleTradePrintVo.setOutItemNumTotal(String.valueOf(outItemNumTotal));
        // 退货商品总数
        saleTradePrintVo.setReturnItemNumTotal(String.valueOf(returnItemNumTotal));
        // 换货商品总数
        saleTradePrintVo.setExchangeItemNumTotal(String.valueOf(exchangeItemNumTotal));
        // 补货商品总数
        saleTradePrintVo.setReissueItemNumTotal(String.valueOf(reissueItemNumTotal));
        // 出货商品总金额
        saleTradePrintVo.setOutItemNumTotalFee(getValue(outItemNumTotalFee, 6).toString());
        // 退货商品总金额
        saleTradePrintVo.setReturnItemNumTotalFee(getValue(returnItemNumTotalFee, 6).toString());
        // 换货商品总金额
        saleTradePrintVo.setExchangeItemNumTotalFee(getValue(exchangeItemNumTotalFee, 6).toString());
        // 补货商品总金额
        saleTradePrintVo.setReissueItemNumTotalFee(getValue(reissueItemNumTotalFee, 6).toString());
        // 商品销售价优惠总金额
        saleTradePrintVo.setItemDiscountTotalAmount(checkSalePower ? itemDiscountTotalAmount.stripTrailingZeros().toPlainString() : BigDecimal.ZERO.toPlainString());
        // 预估出货重量总计
        saleTradePrintVo.setCalculatedTotalShippingWeight(removeTrailingZeros(totalShippingWeight));
        // 合计——商品销售价总金额
        saleTradePrintVo.setItemSaleTotalAmount(getValue(itemSaleTotalAmount, 6).toString());
        // 合计——商品总数量
        saleTradePrintVo.setItemTotalNum(String.valueOf(outItemNumTotal + returnItemNumTotal + exchangeItemNumTotal + reissueItemNumTotal));
        // 合计——商品总金额
        saleTradePrintVo.setItemTotalAmount(getValue(outItemNumTotalFee.add(returnItemNumTotalFee.negate()).add(exchangeItemNumTotalFee).add(reissueItemNumTotalFee),6).toString());
    }

    public void getTableVOSAfterById(Staff staff, SaleTradePrintVO.SaleTradePrintTableVO saleTradePrintTableVO) {
        int boxNum = 0; //箱数
        int remainBulkNum = 0; //散装数
        BigDecimal total = new BigDecimal(0); //销售总价合计
        int itemNum = 0; //商品数
        BigDecimal totalPayment = new BigDecimal(0); //出货商品出货金额
        Boolean checkSalePower = checkSalePower(staff);
        int outItemNumTotal = 0; //出货商品总数
        int returnItemNumTotal = 0; //退货商品总数
        BigDecimal outItemNumTotalFee = new BigDecimal(0); //出货总金额
        BigDecimal returnItemNumTotalFee = new BigDecimal(0); //退货总金额
        int exchangeItemNumTotal = 0; //换货商品总数
        int reissueItemNumTotal = 0; //补发商品总数
        BigDecimal exchangeItemNumTotalFee = new BigDecimal(0); //换货总金额
        BigDecimal reissueItemNumTotalFee = new BigDecimal(0); //补货总金额
        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVos = saleTradePrintTableVO.getTableVOS();
        if (CollectionUtils.isNotEmpty(saleTradeTableVos)) {
            Integer tableBoxnum = saleTradeTableVos.get(0).getTableBoxnum();
            // 所以箱规一样则计算箱数汇总
            boolean b = true;
            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVo : saleTradeTableVos) {
                String tableItemNum = saleTradeTableVo.getTableItemNum();
                itemNum += StringUtils.isNotBlank(tableItemNum) ? Integer.parseInt(tableItemNum) : 0;
                totalPayment = totalPayment.add(getNum(saleTradeTableVo.getTableTotalAmount(), 6));
                total = checkSalePower ? total.add(new BigDecimal(saleTradeTableVo.getTableSaleTotalAmount())) : total;
                boxNum += Optional.ofNullable(saleTradeTableVo.getTableNumOfBox()).orElse(0);
                remainBulkNum += Optional.ofNullable(saleTradeTableVo.getTableRemainBulkNum()).orElse(0);
                if (b && tableBoxnum == null) {
                    b = false;
                } else if (b) {
                    b = tableBoxnum.equals(saleTradeTableVo.getTableBoxnum());
                }
                // 退货
                String returnItemNum = saleTradeTableVo.getReturnItemNum();
                returnItemNumTotal += StringUtils.isNotBlank(returnItemNum) ? Integer.parseInt(returnItemNum) : 0;
                returnItemNumTotalFee = returnItemNumTotalFee.add(getNum(saleTradeTableVo.getReturnItemNumFee(), 6));


                String outItemNum = saleTradeTableVo.getOutItemNum();
                if (EnumSaleTradeGoodsType.EXCHANGE.getName().equals(saleTradeTableVo.getTableSaleGoodsType())) {
                    // 换货
                    exchangeItemNumTotal += StringUtils.isNotBlank(outItemNum) ? Integer.parseInt(outItemNum) : 0;
                    exchangeItemNumTotalFee = exchangeItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                } else if (EnumSaleTradeGoodsType.REISSUE.getName().equals(saleTradeTableVo.getTableSaleGoodsType())) {
                    // 补发
                    reissueItemNumTotal += StringUtils.isNotBlank(outItemNum) ? Integer.parseInt(outItemNum) : 0;
                    reissueItemNumTotalFee = reissueItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                } else {
                    // 出货
                    outItemNumTotal += StringUtils.isNotBlank(outItemNum) ? Integer.parseInt(outItemNum) : 0;
                    outItemNumTotalFee = outItemNumTotalFee.add(getNum(saleTradeTableVo.getOutItemNumFee(), 6));
                }
            }
            // 单独打印无需累加，若散装数大于或等于箱规，散装数转为箱数，并累加
            if (b && tableBoxnum != 0 && remainBulkNum != 0 && remainBulkNum >= tableBoxnum) {
                boxNum += remainBulkNum / tableBoxnum;
                remainBulkNum = remainBulkNum % tableBoxnum;
            }
        }
        if (Objects.equals(0, boxNum) && Objects.equals(0, remainBulkNum)) {
            saleTradePrintTableVO.setSaleOutBoxTotal("0箱");
        } else if (Objects.equals(0, remainBulkNum)) {
            saleTradePrintTableVO.setSaleOutBoxTotal(boxNum + "箱");
        } else {
            saleTradePrintTableVO.setSaleOutBoxTotal(boxNum + "箱+" + remainBulkNum);
        }
        // 计算换算箱汇总数\商品箱数合计\商品剩余散装数合计
        saleTradePrintTableVO.setItemNumOfBoxTotal(Integer.toString(boxNum));
        saleTradePrintTableVO.setItemRemainBulkNumTotal(Integer.toString(remainBulkNum));
        // 销售总价合计
        saleTradePrintTableVO.setSaleTotalAmount(checkSalePower ? total.toString() : BigDecimal.ZERO.toString());
        // 商品总数量
        saleTradePrintTableVO.setSaleOutItemNum(String.valueOf(itemNum));
        // 商品总金额
        saleTradePrintTableVO.setSaleOutTotalFee(getValue(totalPayment, 6).toString());
        // 出货商品总数
        saleTradePrintTableVO.setOutItemNumTotal(String.valueOf(outItemNumTotal));
        // 退货商品总数
        saleTradePrintTableVO.setReturnItemNumTotal(String.valueOf(returnItemNumTotal));
        // 出货商品总金额
        saleTradePrintTableVO.setOutItemNumTotalFee(getValue(outItemNumTotalFee, 6).toString());
        // 退货商品总金额
        saleTradePrintTableVO.setReturnItemNumTotalFee(getValue(returnItemNumTotalFee, 6).toString());
        // 换货商品总数
        saleTradePrintTableVO.setExchangeItemNumTotal(String.valueOf(exchangeItemNumTotal));
        // 补货商品总数
        saleTradePrintTableVO.setReissueItemNumTotal(String.valueOf(reissueItemNumTotal));
        // 换货商品总金额
        saleTradePrintTableVO.setExchangeItemNumTotalFee(getValue(exchangeItemNumTotalFee, 6).toString());
        // 补货商品总金额
        saleTradePrintTableVO.setReissueItemNumTotalFee(getValue(reissueItemNumTotalFee, 6).toString());

    }

    public void getOrderProduct(Staff staff, Map<Long, Trade> tradeMap, boolean isV2Version, String dataSource) {
        List<Long> allSids = tradeMap.values().stream().map(Trade::getSid).collect(Collectors.toList());
        List<Order> allOrders = tradeSearchService.querySomeFieldsBySids(staff, ORDER_FIELDS, new ArrayList<>(allSids));
        allOrders = OrderUtils.toTree(allOrders);
        if (CollectionUtils.isEmpty(allOrders)) {
            return;
        }
        if (Objects.equals("1", dataSource) && isV2Version) {
            // 批发收银V2打印实际出货数据,要过滤未发货数据只保留发货数据
            allOrders = allOrders.stream().filter(t -> TradeStatusUtils.isAfterSendGoods(t.getSysStatus())).collect(Collectors.toList());
        }
        Map<Long, List<Order>> allOrderMap = allOrders.stream().collect(Collectors.groupingBy(Order::getSid));
        orderProductService.fill(staff, allOrders);
        for (Trade trade : tradeMap.values()) {
            List<Order> orderList = allOrderMap.get(trade.getSid());
            TradeUtils.setOrders(trade, orderList, false);
        }
    }

    /**
     * 组装商品(销货单表格)的批次、生产日期，到期日期，保质期
     * @param orderStockProducts    仓储返回批次等信息
     * @param saleTradeTableVO      销货单打印VO
     */
    public void buildOrderProductMsg(List<OrderStockProduct> orderStockProducts, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
        if (CollectionUtils.isEmpty(orderStockProducts)) {
            return;
        }
        // 按批次号从小到大的顺序排序组装数据
        List<OrderStockProduct> orderStockProductList = orderStockProducts.stream().filter(t -> StringUtils.isNotBlank(t.getBatchNo()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrderStockProduct::getBatchNo))), ArrayList::new));
        StringJoiner batchNos = new StringJoiner(",");
        StringJoiner productionDates = new StringJoiner(",");
        StringJoiner expireDates = new StringJoiner(",");
        StringJoiner shelfLife = new StringJoiner(",");
        for (OrderStockProduct orderStockProduct : orderStockProductList) {
            batchNos.add(orderStockProduct.getBatchNo());
            if (orderStockProduct.getProductionDate() != null) {
                productionDates.add(DateUtils.date2Str(orderStockProduct.getProductionDate()));
            }
            if (orderStockProduct.getExpireDate() != null) {
                expireDates.add(DateUtils.date2Str(orderStockProduct.getExpireDate()));
            }
            if (orderStockProduct.getProductionDate() != null && orderStockProduct.getExpireDate() != null) {
                shelfLife.add(String.valueOf(DateUtils.diffDate(orderStockProduct.getExpireDate(), orderStockProduct.getProductionDate()) + 1));
            }
        }
        saleTradeTableVO.setItemBatchNo(batchNos.toString());
        saleTradeTableVO.setItemProductionDate(productionDates.toString());
        saleTradeTableVO.setTableExpireDate(expireDates.toString());
        saleTradeTableVO.setTableShelfLife(shelfLife.toString());
    }

    /**
     * 组装拣选货位\货位数量
     */
    public void buildPickingSectionMsg(List<String> goodsSectionCodes, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
        if (CollectionUtils.isEmpty(goodsSectionCodes)) {
            return;
        }
        Map<String, GoodsSectionOrderRecord> goodsSectionOrderRecordMap = new HashMap<>();
        for (String goodsSectionCode : goodsSectionCodes) {
            JSONObject sectionCodeObject = JSONObject.parseObject(goodsSectionCode);
            if (sectionCodeObject == null) {
                continue;
            }
            GoodsSectionOrderRecord sectionOrderRecord = new GoodsSectionOrderRecord();
            String sectionCode = sectionCodeObject.getString("sectionCode");
            Integer sectionNum = sectionCodeObject.getInteger("sectionNum");
            if (goodsSectionOrderRecordMap.containsKey(sectionCode)) {
                GoodsSectionOrderRecord orderRecord = goodsSectionOrderRecordMap.get(sectionCode);
                sectionNum += orderRecord.getGetNum();
            }
            sectionOrderRecord.setGoodsSectionCode(sectionCode);
            sectionOrderRecord.setGetNum(sectionNum);
            goodsSectionOrderRecordMap.put(sectionCode, sectionOrderRecord);
        }
        List<GoodsSectionOrderRecord> orderRecords = new ArrayList<>(goodsSectionOrderRecordMap.values());
        orderRecords.sort(Comparator.comparing(GoodsSectionOrderRecord::getGoodsSectionCode, Collator.getInstance(Locale.CHINA)));
        List<String> sectionCodes = new ArrayList<>();
        List<String> codeAndNumList = new ArrayList<>();
        for (GoodsSectionOrderRecord orderRecord : orderRecords) {
            String goodsSectionCode = orderRecord.getGoodsSectionCode();
            sectionCodes.add(goodsSectionCode);
            codeAndNumList.add(goodsSectionCode + "*" + orderRecord.getGetNum());
        }
        saleTradeTableVO.setTablePickSectionCode(Strings.join(",", sectionCodes)); //拣选货位
        saleTradeTableVO.setTablePickSectionCodeNum(Strings.join(",", codeAndNumList)); //货位数量
        saleTradeTableVO.setSortSectionCode(orderRecords.get(0).getGoodsSectionCode()); //排序拣选货位
    }

    /**
     * 格式化 BigDecimal，保留四位小数并去除末尾多余的零
     * @param number 需要格式化的 BigDecimal
     * @return 格式化后的字符串
     */
    private static String removeTrailingZeros(BigDecimal number) {
        // 保留4位小数，超出小数位舍弃
        number = number.setScale(4, RoundingMode.DOWN);
        // 去除末尾多余的零
        number = number.stripTrailingZeros();
        // 转换为字符串
        return number.toPlainString();
    }
}

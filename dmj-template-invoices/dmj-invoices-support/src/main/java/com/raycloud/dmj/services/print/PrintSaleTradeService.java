//package com.raycloud.dmj.services.print;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import com.raycloud.bizlogger.Logger;
//import com.raycloud.dmj.Strings;
//import com.raycloud.dmj.dms.domain.dto.DmsBaseDistributorInfoDto;
//import com.raycloud.dmj.dms.request.DmsQueryDistributorInfoRequest;
//import com.raycloud.dmj.dms.response.DmsDistributorInfoResponse;
//import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
//import com.raycloud.dmj.domain.Page;
//import com.raycloud.dmj.domain.TradeConstants;
//import com.raycloud.dmj.domain.account.Staff;
//import com.raycloud.dmj.domain.enums.AccountTypeEnum;
//import com.raycloud.dmj.domain.item.DmjItem;
//import com.raycloud.dmj.domain.pt.IExpressTemplateBase;
//import com.raycloud.dmj.domain.pt.PtConfigConst;
//import com.raycloud.dmj.domain.pt.UserInvoicesTemplate;
//import com.raycloud.dmj.domain.pt.enums.EnumFieldValueName;
//import com.raycloud.dmj.domain.pt.enums.EnumInvoicesType;
//import com.raycloud.dmj.domain.pt.printbo.MergeSaleTradeBo;
//import com.raycloud.dmj.domain.pt.printbo.SaleTradePrintBO;
//import com.raycloud.dmj.domain.pt.printvo.SaleTradePrintVO;
//import com.raycloud.dmj.domain.pt.wlb.FieldValue;
//import com.raycloud.dmj.domain.sku.DmjSku;
//import com.raycloud.dmj.domain.stalls.SaleOrder;
//import com.raycloud.dmj.domain.stalls.SaleTrade;
//import com.raycloud.dmj.domain.stalls.StallTrade;
//import com.raycloud.dmj.domain.stalls.order.vobj.OrderExtInfo;
//import com.raycloud.dmj.domain.stalls.order.vobj.PackageInfo;
//import com.raycloud.dmj.domain.stalls.trade.vobj.PayInfo;
//import com.raycloud.dmj.domain.stalls.trade.vobj.TradeExtInfo;
//import com.raycloud.dmj.domain.stalls.vo.PayTypeVo;
//import com.raycloud.dmj.domain.stock.StockConstants;
//import com.raycloud.dmj.domain.trades.Order;
//import com.raycloud.dmj.domain.trades.Trade;
//import com.raycloud.dmj.domain.trades.utils.TradeUtils;
//import com.raycloud.dmj.domain.wave.WaveUniqueCode;
//import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
//import com.raycloud.dmj.domain.wms.WmsConfig;
//import com.raycloud.dmj.domain.wms.params.AssoGoodsSectionSkuParams;
//import com.raycloud.dmj.services.account.IStaffService;
//import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
//import com.raycloud.dmj.services.helper.*;
//import com.raycloud.dmj.services.helper.wlb.TemplateHelper;
//import com.raycloud.dmj.services.print.utils.ItemSupplierBridgeUtils;
//import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
//import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
//import com.raycloud.dmj.services.trades.ITradePostPrintService;
//import com.raycloud.dmj.services.utils.LogHelper;
//import com.raycloud.dmj.services.wms.IWmsService;
//import com.raycloud.dmj.utils.PrintItemUtils;
//import com.raycloud.dmj.utils.wms.WmsUtils;
//import com.raycloud.dmj.web.utils.DateUtil;
//import com.raycloud.secret_api.api.SecretRequest;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.text.Collator;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//import static com.raycloud.dmj.services.helper.wlb.TemplateHelper.checkPurchasePricePower;
//import static com.raycloud.dmj.services.helper.wlb.TemplateHelper.checkSalePower;
//
///**
// * @author: qingfeng.cxb
// * @create: 2019-08-07 15:27
// */
//@Service("printSaleTradeService")
//public class PrintSaleTradeService {
//
//    private static final String TRADE_FIELDS = "sid,discount_fee,short_id,template_id,template_type";
//
//    private static final Logger logger = Logger.getLogger(PrintSaleTradeService.class);
//    @Resource
//    PrintPageSearchService printPageSearchService;
//    @Resource
//    ITradePostPrintService tradePostPrintService;
//    @Resource
//    IDmsTradeService dmsTradeService;
//    @Resource
//    IStaffService staffService;
//    @Resource
//    SecretRequest secretRequest;
//    @Resource
//    IUserExpressTemplateService userTemplateService;
//    @Resource
//    IUserWlbExpressTemplateService wlbTemplateService;
//    @Resource
//    IItemServiceDubbo itemService;
//    @Resource
//    IWmsService wmsService;
//
//    public List<SaleTradePrintVO> getFieldValues(Staff staff, List<SaleTrade> saleTradeList, UserInvoicesTemplate template, Map<String, Integer> printNumMap, String mergePrint) {
//        /**
//         * 订单数据
//         * */
//        Map<Long, Trade> tradeMap = new HashMap<>();
//        List<Long> sids = saleTradeList.stream().map(SaleTrade::getSid).collect(Collectors.toList());
//        if (sids.size() > 0) {
//            tradeMap = tradePostPrintService.queryTradeBySidsForPrint(staff, sids, TRADE_FIELDS).stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (key1, key2) -> key2));
//            List<String> needValues = TemplateHelper.getNeedValues(template.getFields());
//            if (needValues.contains("express_template_name")) {
//                setTemplateName(staff, tradeMap);
//            }
//        }
//        /**
//         * 员工数据
//         * */
//        Staff s = new Staff();
//        s.setCompanyId(staff.getCompanyId());
//        Map<Long, Staff> staffMap = staffService.queryStaffList(s).stream().collect(Collectors.toMap(Staff::getId, Function.identity(), (key1, key2) -> key2));
//        /**
//         * 获取到销货单的打印数据
//         */
//        List<SaleTradePrintVO> saleTradePrintVOS = buildPrintVos(staff, saleTradeList, template, staffMap, tradeMap, mergePrint);//返回前端的打印数据
//        for (SaleTradePrintVO printVO : saleTradePrintVOS) {
//            if (null != printNumMap) {
//                printVO.setPrintNum(printNumMap.get(printVO.getSaleTradeId()) == null ? 1 : printNumMap.get(printVO.getSaleTradeId()));
//            } else {
//                printVO.setPrintNum(1);
//            }
//        }
//        //合单就直接返回数据
//        if ("1".equals(mergePrint)) {
//            return saleTradePrintVOS;
//        }
//        //排序
//        List<SaleTradePrintVO> sortVOS = new ArrayList<>();
//        Map<String, SaleTradePrintVO> saleTradePrintVOMap = saleTradePrintVOS.stream().collect(Collectors.toMap(SaleTradePrintVO::getSaleTradeId, v -> v, (v1, v2) -> v1));
//        for (SaleTrade saleTrade : saleTradeList) {
//            SaleTradePrintVO saleTradePrintVO = saleTradePrintVOMap.get(saleTrade.getId().toString());
//            if (saleTradePrintVO == null) {
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTrade.getId() + ",未获取到打印数据!"));
//                continue;
//            }
//            sortVOS.add(saleTradePrintVO);
//        }
//        return sortVOS;
//    }
//
//    /**
//     * 组装整个销货单的打印数据
//     */
//    private List<SaleTradePrintVO> buildPrintVos(Staff staff, List<SaleTrade> saleTradeList, UserInvoicesTemplate template, Map<Long, Staff> staffMap, Map<Long, Trade> tradeMap, String mergePrint) {
//        List<FieldValue> fieldValues = template.getFieldValues();
//        int sortType = 0;
//        int dataSource = 1;
//        int printDimension = 0;
//        int earliestTimeTotalDebt = 0;
//        if (CollectionUtils.isNotEmpty(fieldValues)) {
//            for (FieldValue fieldValue : fieldValues) {
//                if (EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
//                    try {
//                        sortType = Integer.parseInt(fieldValue.getValue());
//                    } catch (Exception e) {
//                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化orderType失败：" + fieldValue.getValue()), e);
//                    }
//                } else if (EnumFieldValueName.DATA_SOURCE.getValue().equals(fieldValue.getName())) {
//                    try {
//                        dataSource = Integer.parseInt(fieldValue.getValue());
//                    } catch (Exception e) {
//                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化dataSource失败：" + fieldValue.getValue()), e);
//                    }
//                } else if (EnumFieldValueName.PRINT_DIMENSION.getValue().equals(fieldValue.getName())) {
//                    try {
//                        printDimension = Integer.parseInt(fieldValue.getValue());
//                    } catch (Exception e) {
//                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化printDimension失败：" + fieldValue.getValue()), e);
//                    }
//                } else if (EnumFieldValueName.EARLIEST_TIME_TOTAL_DEBT.getValue().equals(fieldValue.getName())) {
//                    try {
//                        earliestTimeTotalDebt = Integer.parseInt(fieldValue.getValue());
//                    } catch (Exception e) {
//                        logger.error(LogHelper.buildLog(staff, "获取打印数据转化earliestTimeTotalDebt失败：" + fieldValue.getValue()), e);
//                    }
//                }
//            }
//        }
//        /*
//          2.1 将salaTrade order 转化出来并写入BOlist
//          2.2 查询到所有的商品并转成map
//         */
//        Map<Long, Set<Long>> itemIdMap = new HashMap<>();
//        List<SaleTradePrintBO> saleTradePrintBOs;
//        boolean hasMergePrint = "1".equals(mergePrint);
//        if (hasMergePrint) {
//            saleTradePrintBOs = buildMergePrintBo(staff, saleTradeList, dataSource, itemIdMap, staffMap, tradeMap, earliestTimeTotalDebt);
//       } else {
//            saleTradePrintBOs = buildPrintBo(saleTradeList, dataSource, itemIdMap);
//        }
//        //获取商品map
//        Map<Long, DmjItem> idToItem = new HashMap<>();
//        Map<Long, DmjSku> idToSku = new HashMap<>();
//        if (itemIdMap.size() > 0) {
//            ItemSupplierBridgeUtils.getItemAndSkuMap(staff, itemIdMap, itemService, idToItem, idToSku);
//        } else {
//            logger.debug(LogHelper.buildLog(staff, "销货单打印,未获取到商品id!!!"));
//        }
//
//        // 获取商品分类
//        Map<String, String> itemCategoryIdNameMap = getItemCategoryIdNameMap(staff, idToItem);
//
//        // 获取分销商信息
//        Map<Long, DmsBaseDistributorInfoDto> distributorMap = getDistributorMap(staff, saleTradeList);
//
//        //获取商品货位信息
//        Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap = getAssoGoodsSectionSkuMap(staff, saleTradeList, itemIdMap);
//        /*
//          3 组装数据
//         */
//        List<SaleTradePrintVO> saleTradePrintVOS = new ArrayList<>();
//        for (SaleTradePrintBO saleTradePrintBO : saleTradePrintBOs) {
//            saleTradePrintVOS.add(buildPrintVo(staff, saleTradePrintBO, idToItem, idToSku, template, sortType, dataSource, printDimension, staffMap, tradeMap, itemCategoryIdNameMap, distributorMap, assoGoodsSectionSkuMap, hasMergePrint));
//        }
//        return saleTradePrintVOS;
//    }
//
//    /**
//     * 组装整个销货单的打印数据
//     */
//    private List<SaleTradePrintBO> buildPrintBo(List<SaleTrade> saleTradeList, int dataSource, Map<Long, Set<Long>> itemIdMap) {
//        List<SaleTradePrintBO> saleTradePrintBOs = new ArrayList<>();
//        for (SaleTrade saleTrade : saleTradeList) {
//            StallTrade stallTrade = saleTrade.getStallTrade();
//            List<Order> planOrders =
//                    (CollectionUtils.isNotEmpty(stallTrade.getAllocateOrders()) ? stallTrade.getAllocateOrders() : new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList());
//            List<Order> outOrders = (CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(stallTrade)) ? TradeUtils.getOrders4Trade(stallTrade) :
//                    new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList());
//            List<Order> returnOrders =
//                    (CollectionUtils.isNotEmpty(stallTrade.getReturnOrders()) ? stallTrade.getReturnOrders() : new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList());
//            if (dataSource == 0) {
//                if (CollectionUtils.isNotEmpty(planOrders)) {
//                    buildItemSearch(planOrders, itemIdMap);
//                } else {
//                    buildItemSearch(outOrders, itemIdMap);
//                }
//            } else if (dataSource == 1) {
//                if (CollectionUtils.isNotEmpty(outOrders)) {
//                    buildItemSearch(outOrders, itemIdMap);
//                } else {
//                    buildItemSearch(planOrders, itemIdMap);
//                }
//                buildItemSearch(returnOrders, itemIdMap);
//            } else {
//                //两种数据汇总需要查提交配货数
//                buildItemSearch(planOrders, itemIdMap);
//                //有直接开单 都要查出货数 和 退货数
//                buildItemSearch(outOrders, itemIdMap);
//                buildItemSearch(returnOrders, itemIdMap);
//            }
//            saleTradePrintBOs.add(new SaleTradePrintBO(saleTrade, planOrders, outOrders, returnOrders));
//        }
//        return saleTradePrintBOs;
//    }
//
//    /**
//     * 组装整个销货单的打印数据 合并模式
//     */
//    private List<SaleTradePrintBO> buildMergePrintBo(Staff staff, List<SaleTrade> saleTradeList, int dataSource, Map<Long, Set<Long>> itemIdMap, Map<Long, Staff> staffMap, Map<Long, Trade> tradeMap, int earliestTimeTotalDebt) {
//        List<SaleTradePrintBO> saleTradePrintBOs = new ArrayList<>();
//        SaleTrade mergeSaleTrade = null;
//        List<Order> allPlanOrders = new ArrayList<>();
//        List<Order> allOutOrders = new ArrayList<>();
//        List<Order> allReturnOrders = new ArrayList<>();
//        MergeSaleTradeBo mergeSaleTradeBo = new MergeSaleTradeBo();
//        int outItemNum = 0;//出货商品总数 求和
//        int returnItemNum = 0;//退货商品总数 求和
//        int planAllocateGoodsCount = 0;//提交配货商品总数 求和
//        BigDecimal outTotalFee = new BigDecimal(0);//出货商品出货金额 求和
//        BigDecimal outTransportFee = new BigDecimal(0);//出货商品运费 求和
//        BigDecimal returnTotalFee = new BigDecimal(0);//退货商品退款金额 求和
//        BigDecimal saleTotalFee = new BigDecimal(0);//实际应收金额（含运费) 求和
//        BigDecimal planAllocateGoodsTotalFee = new BigDecimal(0);//提交配货总金额 求和
//        BigDecimal discountFee = new BigDecimal(0);//优惠金额 求和
//        BigDecimal salePostFee = new BigDecimal(0);//应付金额  出库金额减退款 求和
//        BigDecimal salePaymentFee = new BigDecimal(0);//支付金额 求和
//        StringBuilder salePayMessage = new StringBuilder();
//        StringBuilder salePaymentMsg = new StringBuilder();
//        List<String> saleRemarkPic = new ArrayList<>(); //备注图片
//        StringBuilder saleDeliveryTime = new StringBuilder(); //最晚发货时间
//        Set<Long> waveIds = new HashSet<>();
//        processLastTimeTotalDebt(staff,mergeSaleTradeBo,saleTradeList,earliestTimeTotalDebt);
//        // 判断所有拣选员是否是同一个人
//        String pickerNames = null;
//        boolean isPickerNames = true;
//        // 判断所有出货店铺是否相同
//        HashSet<String> sendShopNameSet = new HashSet<>();
//        // 判断所有退货仓库是否相同
//        Set<Long> returnWarehouseIdSet = new HashSet<>();
//        // 判断所有出货仓库是否相同
//        Set<Long> sendWarehouseIdSet = new HashSet<>();
//        // 判断所有收货人是否相同
//        HashSet<String> receiverSet = new HashSet<>();
//        // 判断所有收货人手机号是否相同
//        HashSet<String> receiverPhoneSet = new HashSet<>();
//        // 判断所有收货地址是否相同
//        HashSet<String> addressSet = new HashSet<>();
//        /**
//         * 合并数据
//         */
//        for (SaleTrade saleTrade : saleTradeList) {
//            if (mergeSaleTrade == null) {
//                mergeSaleTrade = saleTrade;
//                pickerNames = saleTrade.getPickerNames();
//            }else if (isPickerNames){
//                isPickerNames = StrUtil.isNotEmpty(pickerNames) && pickerNames.equals(saleTrade.getPickerNames());
//            }
//            // 组装不一样则不展示数据
//            sendShopNameSet.add(saleTrade.getShopName());
//            returnWarehouseIdSet.add(saleTrade.getReturnWarehouseId());
//            sendWarehouseIdSet.add(saleTrade.getWarehouseId());
//            receiverSet.add(saleTrade.getReceiverName());
//            receiverPhoneSet.add(saleTrade.getReceiverMobile());
//            addressSet.add(saleTrade.getReceiverAddress());
//            mergeSaleTradeBo.addid(saleTrade.getId());//销货单号
//            mergeSaleTradeBo.addsid(saleTrade.getSid() + "");//这里别用tostring sid可能是null 出库单号
//            mergeSaleTradeBo.addsysMakers(saleTrade.getSysMaker());//制单人
//            mergeSaleTradeBo.addsubmitters(saleTrade.getSubmitter());//开单人
//            mergeSaleTradeBo.addremark(saleTrade.getRemark());//备注
//            Staff saleTradeSubmitter = staffMap.get(saleTrade.getSubmitterId());
//            String saleTradeSubmitterPhone = saleTradeSubmitter == null ? "" : saleTradeSubmitter.getPhone();
//            try {
//                saleTradeSubmitterPhone = secretRequest.decode(saleTradeSubmitterPhone);
//            } catch (Exception e) {
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTrade.getId() + ",开单人id:" + saleTrade.getSubmitterId() + ",开单人电话解密失败!"));
//                saleTradeSubmitterPhone = "###";
//            }
//            mergeSaleTradeBo.addphones(saleTradeSubmitterPhone);//开单人手机号
//            outItemNum += saleTrade.getOutItemNum() == null ? 0 : saleTrade.getOutItemNum();
//            returnItemNum += saleTrade.getReturnItemNum() == null ? 0 : saleTrade.getReturnItemNum();
//            planAllocateGoodsCount += saleTrade.getPlanAllocateGoodsCount() == null ? 0 : saleTrade.getPlanAllocateGoodsCount();
//            outTotalFee = outTotalFee.add(getNum(saleTrade.getOutTotalFee()));
//            outTransportFee = outTransportFee.add(getNum(saleTrade.getTransportFee()));
//            returnTotalFee = returnTotalFee.add(getNum(saleTrade.getReturnTotalFee()));
//            saleTotalFee = saleTotalFee.add(getNum(saleTrade.getPayment()));
//            planAllocateGoodsTotalFee = planAllocateGoodsTotalFee.add(getNum(saleTrade.getPlanAllocateGoodsTotalFee()));
//            Trade trade = tradeMap.get(saleTrade.getSid());
//            if (trade == null) {
//                trade = new Trade();
//            }
//            discountFee = discountFee.add(getNum(trade.getDiscountFee()));
//            salePostFee = salePostFee.add(getNum(saleTrade.getOutTotalFee()).subtract(getNum(saleTrade.getReturnTotalFee())));
//            if(CollectionUtils.isNotEmpty(saleTrade.getPayInfos())){
//                salePaymentFee = salePaymentFee.add(saleTrade.getPayInfos().stream().map(PayInfo::getPayFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
//            }
//            /*salePaymentFee = salePaymentFee.add(getNum(saleTrade.getAliPayFee()).add(getNum(saleTrade.getWxPayFee())).add(getNum(saleTrade.getCashFee())));
//            salePaymentFee = SaleTradeUtils.getAllPaidFee(saleTrade, salePaymentFee);*/
//
//            salePayMessage.append(buildPayMessage(saleTrade)).append(";");
//            salePaymentMsg.append(buildSalePaymentMsg(saleTrade)).append(",");
//            StallTrade stallTrade = saleTrade.getStallTrade();
//            allPlanOrders.addAll((CollectionUtils.isNotEmpty(stallTrade.getAllocateOrders()) ? stallTrade.getAllocateOrders() :
//                    new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList()));
//            allOutOrders.addAll((CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(stallTrade)) ? TradeUtils.getOrders4Trade(stallTrade) :
//                    new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList()));
//            allReturnOrders.addAll((CollectionUtils.isNotEmpty(stallTrade.getReturnOrders()) ? stallTrade.getReturnOrders() :
//                    new ArrayList<Order>()).stream().filter(o -> o.getNum() != null && o.getNum() > 0).collect(Collectors.toList()));
//            List<String> remarkPic = buildRemarkPic(saleTrade);
//            if (CollectionUtils.isNotEmpty(remarkPic)) {
//                saleRemarkPic.addAll(remarkPic);
//            }
//            if (saleTrade.getDeliveryTime() != null) {
//                saleDeliveryTime.append(DateUtil.format(saleTrade.getDeliveryTime(), "yyyy-MM-dd HH:mm:ss")).append(",");
//            }
//            if (saleTrade.getWaveId() != null) {
//                waveIds.add(saleTrade.getWaveId());
//            }
//        }
//        // 如果有拣选员不同则置为null
//        if (!isPickerNames) {
//            mergeSaleTrade.setPickerNames(null);
//        }
//        // 数据不一致则置为null
//        if (mergeSaleTrade != null) {
//            if(sendShopNameSet.size() != 1) mergeSaleTrade.setShopName(null);
//            if(returnWarehouseIdSet.size() != 1) mergeSaleTrade.setReturnWarehouseName(null);
//            if(sendWarehouseIdSet.size() != 1) mergeSaleTrade.setWarehouseName(null);
//            if(receiverSet.size() != 1) mergeSaleTrade.setReceiverName(null);
//            if(receiverPhoneSet.size() != 1) mergeSaleTrade.setReceiverMobile(null);
//            if(addressSet.size() != 1) mergeSaleTrade.setReceiverAddress(null);
//        }
//        if (dataSource == 0) {
//            if (CollectionUtils.isNotEmpty(allPlanOrders)) {
//                buildItemSearch(allPlanOrders, itemIdMap);
//            } else {
//                buildItemSearch(allOutOrders, itemIdMap);
//            }
//        } else if (dataSource == 1) {
//            if (CollectionUtils.isNotEmpty(allOutOrders)) {
//                buildItemSearch(allOutOrders, itemIdMap);
//            } else {
//                buildItemSearch(allPlanOrders, itemIdMap);
//            }
//            buildItemSearch(allReturnOrders, itemIdMap);
//        } else {
//            //两种数据汇总需要查提交配货数
//            buildItemSearch(allPlanOrders, itemIdMap);
//            //有直接开单 都要查出货数 和 退货数
//            buildItemSearch(allOutOrders, itemIdMap);
//            buildItemSearch(allReturnOrders, itemIdMap);
//        }
//        BigDecimal saleOutFee = outTotalFee.add(outTransportFee);//出货总金额（含运费）= 货款+运费
//
//        mergeSaleTradeBo.setOutItemNum(outItemNum);
//        mergeSaleTradeBo.setOutTotalFee(outTotalFee.toPlainString());
//        mergeSaleTradeBo.setOutTransportFee(outTransportFee.toPlainString());
//        mergeSaleTradeBo.setSaleOutFee(saleOutFee.toPlainString());
//        mergeSaleTradeBo.setReturnItemNum(returnItemNum);
//        mergeSaleTradeBo.setReturnTotalFee(returnTotalFee.toPlainString());
//        mergeSaleTradeBo.setSaleTotalFee(saleTotalFee.toPlainString());
//        mergeSaleTradeBo.setPlanAllocateGoodsCount(planAllocateGoodsCount);
//        mergeSaleTradeBo.setPlanAllocateGoodsTotalFee(planAllocateGoodsTotalFee.toPlainString());
//        mergeSaleTradeBo.setDiscountFee(discountFee.toPlainString());
//        mergeSaleTradeBo.setSalePostFee(salePostFee.toPlainString());
//        mergeSaleTradeBo.setSalePaymentFee(salePaymentFee.toPlainString());
//        if (salePayMessage.toString().endsWith(";")) {
//            salePayMessage.deleteCharAt(salePayMessage.length() - 1);
//        }
//        mergeSaleTradeBo.setSalePayMessage(salePayMessage.toString());
//        if (salePaymentMsg.toString().endsWith(";")) {
//            salePaymentMsg.deleteCharAt(salePaymentMsg.length() - 1);
//        }
//        mergeSaleTradeBo.setSalePaymentMsg(salePaymentMsg.toString());
//        Map<String, List<Order>> planOrderMap = allPlanOrders.stream().collect(Collectors.groupingBy(this::getOrderMergeKey));
//        Map<String, List<Order>> outOrderMap = allOutOrders.stream().collect(Collectors.groupingBy(this::getOrderMergeKey));
//        Map<String, List<Order>> returnOrderMap = allReturnOrders.stream().collect(Collectors.groupingBy(this::getOrderMergeKey));
//        mergeSaleTradeBo.setPlanAllocateGoodsKindNum(planOrderMap.size());
//        mergeSaleTradeBo.setOutItemKind(outOrderMap.size());
//        mergeSaleTradeBo.setReturnItemKind(returnOrderMap.size());
//        mergeSaleTradeBo.setSaleRemarkPic(saleRemarkPic);
//        if (saleDeliveryTime.toString().endsWith(",")) {
//            saleDeliveryTime.deleteCharAt(saleDeliveryTime.length() - 1);
//        }
//        mergeSaleTradeBo.setSaleDeliveryTime(saleDeliveryTime.toString());
//        if (waveIds.size() == 1) {
//            mergeSaleTradeBo.setWaveId(waveIds.toArray(new Long[0])[0]);
//        }
//        SaleTradePrintBO saleTradePrintBO = new SaleTradePrintBO();
//        List<Order> mergePlanOrders = mergeOrder(allPlanOrders);
//        List<Order> mergeOutOrders = mergeOrder(allOutOrders);
//        List<Order> mergeReturnOrders = mergeOrder(allReturnOrders);
//        saleTradePrintBO.setSaleTrade(mergeSaleTrade);
//        saleTradePrintBO.setPlanOrders(mergePlanOrders);
//        saleTradePrintBO.setOutOrders(mergeOutOrders);
//        saleTradePrintBO.setReturnOrders(mergeReturnOrders);
//        saleTradePrintBO.setMergeSale(true);
//        saleTradePrintBO.setMergeSaleTradeBo(mergeSaleTradeBo);
//        saleTradePrintBOs.add(saleTradePrintBO);
//        return saleTradePrintBOs;
//    }
//
//
//    /**
//     * @description  获取订单分组的key
//     *
//     * <AUTHOR>
//     * @date    2024-04-18 10:55
//     * @param	order
//     * @return  java.lang.String
//     */
//    private String getOrderMergeKey(Order order) {
//        // 获取箱规
//        PackageInfo packageInfo = analysisTradeTable(order);
//        return order.getItemSysId() + "" +
//                (order.getSkuSysId() == null ? "-1" : order.getSkuSysId()) +
//                // 拼接箱规
//                (packageInfo == null || packageInfo.getNumPerBox() == null ? "" : packageInfo.getNumPerBox());
//    }
//
//    /**
//     * 处理上次累计欠款字段
//     * @param staff
//     * @param mergeSaleTradeBo
//     * @param saleTradeList
//     * @param earliestTimeTotalDebt
//     */
//    private void processLastTimeTotalDebt(Staff staff, MergeSaleTradeBo mergeSaleTradeBo, List<SaleTrade> saleTradeList, int earliestTimeTotalDebt) {
//        SaleTrade saleTrade = null;
//        if (earliestTimeTotalDebt == 1) {
//            // 显示最早一个销货单
//            saleTrade = saleTradeList.stream().min(Comparator.comparing(SaleTrade::getCheckoutTime, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(SaleTrade::getCreated, Comparator.nullsLast(Comparator.naturalOrder()))).orElse(null);
//            if (saleTrade != null) {
//                // 上次累计欠款取最早销货单，本次欠款取所有被选单据汇总数
//                BigDecimal reduce = saleTradeList.stream().filter(trade -> StrUtil.isNotEmpty(trade.getNowDebt())).map(trade -> new BigDecimal(trade.getNowDebt())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                saleTrade.setNowDebt(String.format("%.2f", reduce));
//                // 累计欠款取最新的
//                SaleTrade saleTradeMax = saleTradeList.stream().max(Comparator.comparing(SaleTrade::getCheckoutTime, Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(SaleTrade::getCreated, Comparator.nullsFirst(Comparator.naturalOrder()))).orElse(null);
//                saleTrade.setTotalDebt(saleTradeMax.getTotalDebt());
//            }
//        } else {
//            // 显示最后一个销货单
//            saleTrade = saleTradeList.stream().max(Comparator.comparing(SaleTrade::getCheckoutTime, Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(SaleTrade::getCreated, Comparator.nullsFirst(Comparator.naturalOrder()))).orElse(null);
//        }
//
//        if (saleTrade == null) {
//            return;
//        }
//
////        String nowTotalDebt = String.valueOf(Double.parseDouble(StringUtils.isNotBlank(saleTrade.getLastTimeTotalDebt()) ? saleTrade.getLastTimeTotalDebt() : "0") + Double.parseDouble(StringUtils.isNotBlank(saleTrade.getNowDebt()) ? saleTrade.getNowDebt() : "0"));
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        mergeSaleTradeBo.setCheckoutTime(sdf.format(saleTrade.getCheckoutTime()));
//        mergeSaleTradeBo.setLastTimeTotalDebt(saleTrade.getLastTimeTotalDebt());
//        mergeSaleTradeBo.setNowDebt(saleTrade.getNowDebt());
//        mergeSaleTradeBo.setTotalDebt(saleTrade.getTotalDebt());
//        mergeSaleTradeBo.setNowTotalDebt(saleTrade.getCurrentTotalDebt());
//    }
//
//        /**
//         * 合并列表明细  同一个规格 切 单价相同 数量累加 备注拼接
//         */
//    public List<Order> mergeOrder(List<Order> orders) {
//        Map<String, Order> mergeMap = new HashMap<>();
//        Map<String, Set<Long>> assistUnitIdMap = new HashMap<>();
//        for (Order order : orders) {
////            Long sysItemId = order.getItemSysId();
////            Long sysSkuId = order.getSkuSysId() == null ? -1L : order.getSkuSysId();
////            String price = order.getPrice() == null ? "-1" : order.getPrice();
////            String key = sysItemId + "_" + sysSkuId + "_" + price;
//            String key = getOrderMergeKey(order);
//            Order mergeOrder = mergeMap.get(key);
//            if (order instanceof SaleOrder) {
//                SaleOrder saleOrder = (SaleOrder) order;
//                assistUnitIdMap.computeIfAbsent(key, t -> new HashSet<>()).add(saleOrder.getAssistUnitId());
//            }
//            if (mergeOrder == null) {
//                mergeOrder = order;
//                mergeMap.put(key, mergeOrder);
//            } else {
//                int itemNum = order.getNum() == null ? 0 : order.getNum();
//                int mergeNum = (mergeOrder.getNum() == null ? 0 : mergeOrder.getNum()) + itemNum;
//                mergeOrder.setNum(mergeNum);
//                // 合并总金额
//                BigDecimal totalFee = StringUtils.isNotEmpty(order.getTotalFee()) ? new BigDecimal(order.getTotalFee()) : BigDecimal.ZERO;
//                BigDecimal mergeTotalFee = StringUtils.isNotEmpty(mergeOrder.getTotalFee()) ? new BigDecimal(mergeOrder.getTotalFee()) : BigDecimal.ZERO;
//                mergeOrder.setTotalFee(mergeTotalFee.add(totalFee).toString());
//                // 合并支付金额
//                BigDecimal payment = StringUtils.isNotEmpty(order.getPayment()) ? new BigDecimal(order.getPayment()) : BigDecimal.ZERO;
//                BigDecimal mergePayment = StringUtils.isNotEmpty(mergeOrder.getPayment()) ? new BigDecimal(mergeOrder.getPayment()) : BigDecimal.ZERO;
//                mergeOrder.setPayment(mergePayment.add(payment).toString());
//                String orderRemark = order.getSaleRemark() == null ? "" : order.getSaleRemark();
//                String mergeRemark = mergeOrder.getSaleRemark() == null ? "" : mergeOrder.getSaleRemark();
//                if (StringUtils.isNotBlank(orderRemark) && StringUtils.isNotBlank(mergeRemark)) {
//                    mergeOrder.setSaleRemark(mergeRemark + "," + orderRemark);
//                } else {
//                    mergeOrder.setSaleRemark(StringUtils.isNotBlank(orderRemark) ? orderRemark : mergeRemark);
//                }
//                if (assistUnitIdMap.get(key) != null && order instanceof SaleOrder) {
//                    SaleOrder oldSaleOrder = (SaleOrder) mergeOrder;
//                    SaleOrder saleOrder = (SaleOrder) order;
//                    if (assistUnitIdMap.get(key).size() == 1) {
//                        Double orderAssistUnitNum = oldSaleOrder.getAssistUnitNum() == null ? 0D : oldSaleOrder.getAssistUnitNum();
//                        Double assistUnitNum = saleOrder.getAssistUnitNum() == null ? orderAssistUnitNum : orderAssistUnitNum + saleOrder.getAssistUnitNum();
//                        oldSaleOrder.setAssistUnitNum(assistUnitNum);
//                    } else {
//                        oldSaleOrder.setAssistUnitId(null);
//                        oldSaleOrder.setAssistUnit("");
//                        oldSaleOrder.setAssistUnitNum(null);
//                    }
//                }
//                // 合并箱数和散装数
//                accumulateTradeTable(mergeOrder, order);
//                // 合并唯一码
//                mergeUniqueCodes(mergeOrder, order);
//            }
//        }
//        return new ArrayList<>(mergeMap.values());
//    }
//
//    /**
//     * @description  解析批发收银给的箱规、箱数、散装数 并进行累加
//     *
//     * <AUTHOR>
//     * @date    2024-04-17 19:50
//     * @param   mergeOrder
//     * @param   order
//     * @return  void
//     */
//    private void accumulateTradeTable(Order mergeOrder, Order order) {
//        try {
//            String mergeExtInfoStr = ((SaleOrder) mergeOrder).getExtInfoStr();
//            String extInfoStr = ((SaleOrder) order).getExtInfoStr();
//            if (StrUtil.isEmpty(mergeExtInfoStr) || StrUtil.isEmpty(extInfoStr)) {
//                return ;
//            }
//            OrderExtInfo mergeExtInfo = JSONObject.parseObject(mergeExtInfoStr, OrderExtInfo.class);
//            if (mergeExtInfo == null || mergeExtInfo.getPackageInfo() == null) {
//                ((SaleOrder) mergeOrder).setExtInfoStr(extInfoStr);
//                return ;
//            }
//            OrderExtInfo extInfo = JSONObject.parseObject(extInfoStr, OrderExtInfo.class);
//            if (extInfo == null || extInfo.getPackageInfo() == null) {
//                return ;
//            }
//            PackageInfo mergePackageInfo = mergeExtInfo.getPackageInfo();
//            PackageInfo packageInfo = extInfo.getPackageInfo();
//            // 累加箱数
//            mergePackageInfo.setNumOfBox(Optional.ofNullable(mergePackageInfo.getNumOfBox()).orElse(0) +
//                    Optional.ofNullable(packageInfo.getNumOfBox()).orElse(0));
//            // 累加散装数
//            mergePackageInfo.setBulkNum(Optional.ofNullable(mergePackageInfo.getBulkNum()).orElse(0) +
//                    Optional.ofNullable(packageInfo.getBulkNum()).orElse(0));
//            // 还原成json
//            ((SaleOrder) mergeOrder).setExtInfoStr(JSONObject.toJSONString(mergeExtInfo));
//        }catch (Exception e) {
//            logger.error(String.format("合并打印，批发收银数据解析失败：%s - %s", JSONObject.toJSONString(mergeOrder), JSONObject.toJSONString(order)), e);
//        }
//    }
//    /**
//     * 封装单个销货单打印信息
//     *
//     * @param saleTradePrintBO bo
//     * @param idToItem         商品map
//     * @param idToSku          规格 map
//     * @param sortType         排序方式
//     * @param dataSource       数据来源
//     * @param hasMergePrint    是否合并打印
//     */
//    private SaleTradePrintVO buildPrintVo(Staff staff, SaleTradePrintBO saleTradePrintBO, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku,
//                                          UserInvoicesTemplate template, Integer sortType, int dataSource, int printDimension, Map<Long, Staff> staffMap,
//                                          Map<Long, Trade> tradeMap, Map<String, String> itemCategoryIdNameMap, Map<Long, DmsBaseDistributorInfoDto> distributorMap,
//                                          Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint) {
//        SaleTradePrintVO saleTradePrintVo = new SaleTradePrintVO();
//        SaleTrade saleTrade = saleTradePrintBO.getSaleTrade();
//        Trade trade = tradeMap.get(saleTrade.getSid());
//        trade = trade == null ? new Trade() : trade;
//        /*
//          1 封装基础数据
//         */
//        saleTradePrintVo.setSaleCustomerName(saleTrade.getCustomerName());//客户姓名
//        saleTradePrintVo.setTitle(template.getTitle());//标题
//        saleTradePrintVo.setSaleSendWarehouseName(saleTrade.getWarehouseName());//出货仓库
//        saleTradePrintVo.setSaleSendShopName(saleTrade.getShopName());//出货店铺
//        if (saleTrade.getPickGoodsType() != null) {
//            saleTradePrintVo.setSalePickGoodsType(Integer.valueOf(1).equals(saleTrade.getPickGoodsType()) ? "自提" : "邮寄");//提货方式
//        }
//        saleTradePrintVo.setSaleReturnWarehouseName(StringUtils.isNotBlank(saleTrade.getReturnWarehouseName()) ? saleTrade.getReturnWarehouseName() : "");//退货仓库
//        saleTradePrintVo.setSaleReceiver(saleTrade.getReceiverName());//收件人
//        saleTradePrintVo.setSaleReceiverPhone(saleTrade.getReceiverMobile());//收货人电话
//        StringBuilder saleAddress = new StringBuilder(StringUtils.defaultString(saleTrade.getReceiverState()))
//                .append(saleTrade.getReceiverCity()).append(saleTrade.getReceiverDistrict())
//                .append(saleTrade.getReceiverAddress());
//        saleTradePrintVo.setSaleAddress(saleAddress.toString());//收货地址
//        saleTradePrintVo.setSaleTradePrintTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));//打印时间
//        saleTradePrintVo.setSaleTradeIdBarCode(String.valueOf(saleTrade.getId()));//销货单条码
//        saleTradePrintVo.setSaleShortId(String.valueOf(trade.getShortId()));//内部单号
//        saleTradePrintVo.setSaleAccountType(conversionAccountType(saleTrade.getAccountType()));//付款方式
//        saleTradePrintVo.setSaleCalculateType(AccountTypeEnum.checkAccountType(saleTrade.getAccountType()));//结算方式
//        saleTradePrintVo.setCreateTime(DateUtil.format(saleTrade.getCreated(), "yyyy-MM-dd HH:mm:ss"));//制单时间
//        saleTradePrintVo.setTradeOutSid(saleTrade.getStallTrade() != null && saleTrade.getStallTrade().getOutSid() != null ? saleTrade.getStallTrade().getOutSid() : ""); // 快递单号
//        saleTradePrintVo.setRepairWorkOrderId(saleTrade.getWorkOrderId() != null ? String.valueOf(saleTrade.getWorkOrderId()) : ""); // 售后工单号
//
//        saleTradePrintVo.setExpressTemplateName(StringUtils.isNotBlank(trade.getTemplateName()) ? trade.getTemplateName() : "");//快递模板名称
//        saleTradePrintVo.setSaleTradeSurplusFee(saleTrade.getSurplusFee()); //销货单抹零金额
//        saleTradePrintVo.setSaleTradeDepositAmount(String.valueOf(saleTrade.getDeposit())); //订金
//        DmsBaseDistributorInfoDto distributorInfo = distributorMap.get(saleTrade.getDistributorId());
//        if (distributorInfo != null) {
//            saleTradePrintVo.setDistributorPhone(distributorInfo.getContactPhone()); // 分销商手机号码
//            saleTradePrintVo.setDistributorTelephone(distributorInfo.getContactTelephone()); //分销商电话号码
//            saleTradePrintVo.setDistributorAddress(buildDistributorAddress(distributorInfo));//分销商地址
//            saleTradePrintVo.setDistributorContactName(distributorInfo.getContactName());//分销商联系人
//            saleTradePrintVo.setDistributorSalesMan(distributorInfo.getSaleStaffName());//分销商业务员
//            saleTradePrintVo.setSaleDistributorShortName(distributorInfo.getAliasName());//分销商简称
//        }
//
//        if (saleTradePrintBO.isMergeSale()) {
//            buildMergeMsg(saleTradePrintVo, saleTradePrintBO.getMergeSaleTradeBo());
//            saleTradePrintVo.setSaleDistributorName(saleTrade.getDistributorName());//分销商名称
//            saleTradePrintVo.setBalance(saleTrade.getBalance());//分销商余额
//            saleTradePrintVo.setCredit(saleTrade.getCredit());//分销商授信额度
//            saleTradePrintVo.setTotalBalance(saleTrade.getTotalBalance());//分销商剩余总金额
//        } else {
//            buildSingleMsg(staff, saleTradePrintVo, saleTrade, trade, staffMap);
//        }
//        saleTradePrintVo.setPickerNames(saleTrade.getPickerNames());// 拣选员
//        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
//        boolean isV1Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() || EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue();
//
//        if (isV3Template) {
//            // 交叉模式V1/V2/V3数据
//            List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = buildMergeSaleTradeTableVos(staff, idToItem, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint);
//            saleTradePrintVo.setSaleTradeTableVos(saleTradeTableVOS);
//            // 销售总价合计
//            buildSaleTotalAmount(staff, saleTradePrintVo);
//            // 计算换算箱汇总数\商品箱数合计\商品剩余散装数合计
//            calculateBox(saleTradePrintVo, hasMergePrint);
//        } else if (isV1Template){
//            buildCrossItemProperties(saleTradePrintVo, staff, idToItem, idToSku, saleTradePrintBO, dataSource, sortType, template, hasMergePrint);
//        } else {
//            // 自定义模板数据组装
//            //表格数据saleShortId
//            if (Integer.valueOf(1).equals(printDimension)) {
//                //款维度
//                saleTradePrintVo.setSaleTradeTableVos(buildMergeSaleTradeTableVos(staff, idToItem, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint));
//            } else {
//                //明细维度
//                saleTradePrintVo.setSaleTradeTableVos(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, saleTradePrintBO, dataSource, sortType, itemCategoryIdNameMap, template, assoGoodsSectionSkuMap, hasMergePrint));
//            }
//            // 销售总价合计
//            buildSaleTotalAmount(staff, saleTradePrintVo);
//            // 计算换算箱汇总数\商品箱数合计\商品剩余散装数合计
//            calculateBox(saleTradePrintVo, hasMergePrint);
//        }
//        return saleTradePrintVo;
//    }
//
//    /**
//     * 组装合并销货单数据
//     */
//    private void buildMergeMsg(SaleTradePrintVO saleTradePrintVo, MergeSaleTradeBo mergeSaleTradeBo) {
//        saleTradePrintVo.setSaleTradeId(joinMsg(mergeSaleTradeBo.getIds()));//销货单id
//        saleTradePrintVo.setSalePlanItemNum(String.valueOf(mergeSaleTradeBo.getPlanAllocateGoodsCount()));//提交配货商品总数
//        saleTradePrintVo.setSalePlanItemKind(String.valueOf(mergeSaleTradeBo.getPlanAllocateGoodsKindNum()));//提交配货商品种类
//        saleTradePrintVo.setSalePlanTotalFee(mergeSaleTradeBo.getPlanAllocateGoodsTotalFee());//提交配货商品金额
//        saleTradePrintVo.setSaleOutItemNum(String.valueOf(mergeSaleTradeBo.getOutItemNum()));//出货商品数
//        saleTradePrintVo.setSaleOutItemKind(String.valueOf(mergeSaleTradeBo.getOutItemKind()));//出货商品种类
//        saleTradePrintVo.setSaleOutTotalFee(mergeSaleTradeBo.getOutTotalFee());//出货商品出货金额
//        saleTradePrintVo.setSaleOutTransportFee(mergeSaleTradeBo.getOutTransportFee());//出货商品运费
//        saleTradePrintVo.setSaleOutFee(mergeSaleTradeBo.getSaleOutFee());//出货总金额
//        saleTradePrintVo.setSaleReturnItemNum(String.valueOf(mergeSaleTradeBo.getReturnItemNum()));//退货商品总数
//        saleTradePrintVo.setSaleReturnItemKind(String.valueOf(mergeSaleTradeBo.getOutItemKind()));//退货商品种类
//        saleTradePrintVo.setSaleReturnTotalFee(mergeSaleTradeBo.getReturnTotalFee());//退货商品退款金额
//        saleTradePrintVo.setSaleTotalFee(mergeSaleTradeBo.getSaleTotalFee());//实际应收金额
//        saleTradePrintVo.setSalePayMessage(mergeSaleTradeBo.getSalePayMessage());//付款信息
//        saleTradePrintVo.setSaleRemark(joinMsg(mergeSaleTradeBo.getRemarks()));//销货单备注
//        saleTradePrintVo.setTradeSysMemo(joinMsg(mergeSaleTradeBo.getRemarks()));//销货单备注
//        saleTradePrintVo.setSaleTradeCheckoutTime(mergeSaleTradeBo.getCheckoutTime());//开单时间
//        saleTradePrintVo.setSaleTradeSid(joinMsg(mergeSaleTradeBo.getSids()));//出库单号
//        saleTradePrintVo.setSaleTradeSysMaker(joinMsg(mergeSaleTradeBo.getSysMakers()));//制单人
//        saleTradePrintVo.setSaleTradeSubmitter(joinMsg(mergeSaleTradeBo.getSubmitters()));//结账人
//        saleTradePrintVo.setSaleLastTimeTotalDebt(mergeSaleTradeBo.getLastTimeTotalDebt());//上次累计欠款
//        saleTradePrintVo.setSaleNowDebt(mergeSaleTradeBo.getNowDebt());//本次欠款
//        saleTradePrintVo.setSaleTotalDebt(mergeSaleTradeBo.getTotalDebt());//累计欠款
//        try{
//            // 本次累计欠款 = 上次累计欠款 + 本次欠款
//            saleTradePrintVo.setSaleNowTotalDebt(String.format("%.2f", new BigDecimal(mergeSaleTradeBo.getLastTimeTotalDebt()).add(new BigDecimal(mergeSaleTradeBo.getNowDebt()))));//上次累计欠款 + 本次欠款
//        }catch (Exception e) {
//            // 报错则走原逻辑
//            saleTradePrintVo.setSaleNowTotalDebt(mergeSaleTradeBo.getNowTotalDebt());//上次累计欠款
//            logger.error(String.format("组合销货单打印上次累计欠款和本次累计欠款转换失败：上次累计欠款 = %s, 本次欠款 = %s", mergeSaleTradeBo.getLastTimeTotalDebt(), mergeSaleTradeBo.getNowDebt()));
//        }
////        saleTradePrintVo.setSaleNowTotalDebt(mergeSaleTradeBo.getNowTotalDebt());//本次累计欠款
//        saleTradePrintVo.setSaleDiscountAmount(mergeSaleTradeBo.getDiscountFee());//优惠金额
//        saleTradePrintVo.setSalePostFee(mergeSaleTradeBo.getSalePostFee());//出货商品总金额-退货商品总金额
//        saleTradePrintVo.setSalePaymentFee(mergeSaleTradeBo.getSalePaymentFee());//支付宝支付+微信支付+现金支付的总和
//        saleTradePrintVo.setSalePaymentMsg(mergeSaleTradeBo.getSalePaymentMsg());//自定义支付详情
//        saleTradePrintVo.setSaleTradeSubmitterPhone(joinMsg(mergeSaleTradeBo.getPhones()));//开单人手机号
//        saleTradePrintVo.setSaleRemarkPic(mergeSaleTradeBo.getSaleRemarkPic());//备注图片
//        saleTradePrintVo.setSaleDeliveryTime(mergeSaleTradeBo.getSaleDeliveryTime());//最晚发货时间
//        saleTradePrintVo.setSaleTradeWaveCode(mergeSaleTradeBo.getWaveId() == null ? "" : mergeSaleTradeBo.getWaveId().toString());//波次号
//    }
//
//    /**
//     * 组装单个销货单数据
//     */
//    private void buildSingleMsg(Staff staff, SaleTradePrintVO saleTradePrintVo, SaleTrade saleTrade, Trade trade, Map<Long, Staff> staffMap) {
//        saleTradePrintVo.setSaleTradeId(String.valueOf(saleTrade.getId()));//销货单id
//        saleTradePrintVo.setSalePlanItemNum(saleTrade.getPlanAllocateGoodsCount() != null ? saleTrade.getPlanAllocateGoodsCount().toString() : "");//提交配货商品总数
//        saleTradePrintVo.setSalePlanItemKind(saleTrade.getPlanAllocateGoodsKindNum() != null ? saleTrade.getPlanAllocateGoodsKindNum().toString() : "");//提交配货商品种类
//        saleTradePrintVo.setSalePlanTotalFee(saleTrade.getPlanAllocateGoodsTotalFee());//提交配货商品金额
//        saleTradePrintVo.setSaleOutItemNum(saleTrade.getOutItemNum() == null ? "" : saleTrade.getOutItemNum().toString());//出货商品数
//        saleTradePrintVo.setSaleOutItemKind(saleTrade.getOutItemKind() == null ? "" : saleTrade.getOutItemKind().toString());//出货商品种类
//        saleTradePrintVo.setSaleOutTotalFee(saleTrade.getOutTotalFee());//出货商品出货金额
//        saleTradePrintVo.setSaleOutTransportFee(saleTrade.getTransportFee() == null ? "0.0" : saleTrade.getTransportFee());//出货商品运费
//        if (StringUtils.isNotBlank(saleTrade.getOutTotalFee()) || StringUtils.isNotBlank(saleTrade.getTransportFee())) {
//            //都为空不赋值
//            BigDecimal outTotalFee = new BigDecimal(StringUtils.isBlank(saleTrade.getOutTotalFee()) ? "0" : saleTrade.getOutTotalFee());
//            BigDecimal outFee = outTotalFee.add(new BigDecimal(StringUtils.isBlank(saleTrade.getTransportFee()) ? "0" : saleTrade.getTransportFee()));
//            saleTradePrintVo.setSaleOutFee(Double.toString(outFee.doubleValue()));//出货总金额
//        }
//        saleTradePrintVo.setSaleReturnItemNum(saleTrade.getReturnItemNum() == null ? "" : saleTrade.getReturnItemNum().toString());//退货商品总数
//        saleTradePrintVo.setSaleReturnItemKind(saleTrade.getReturnItemKind() == null ? "" : saleTrade.getReturnItemKind().toString());//退货商品种类
//        saleTradePrintVo.setSaleReturnTotalFee(saleTrade.getReturnTotalFee());//退货商品退款金额
//        saleTradePrintVo.setSaleTotalFee(saleTrade.getPayment());//实际应收金额
//        saleTradePrintVo.setSalePayMessage(buildPayMessage(saleTrade));//付款信息
//        saleTradePrintVo.setSaleRemark(saleTrade.getRemark());//销货单备注
//        saleTradePrintVo.setSaleTradeCheckoutTime(saleTrade.getCheckoutTime() != null ? DateUtil.format(saleTrade.getCheckoutTime(), "yyyy-MM-dd HH:mm:ss") : "");//开单时间
//        saleTradePrintVo.setSaleTradeSid(String.valueOf(saleTrade.getSid()));//出库单号
//        saleTradePrintVo.setSaleTradeSysMaker(saleTrade.getSysMaker());//制单人
//        saleTradePrintVo.setSaleTradeSubmitter(saleTrade.getSubmitter());//结账人
//        saleTradePrintVo.setTradeSysMemo(saleTrade.getRemark());//销货单备注
//        logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTrade.getId() + "本次欠款:" + saleTrade.getNowDebt() + ",上次累计欠款:" + saleTrade.getLastTimeTotalDebt() + ",累计欠款:" + saleTrade.getTotalDebt()));
//        saleTradePrintVo.setSaleLastTimeTotalDebt(saleTrade.getLastTimeTotalDebt());//上次累计欠款
//        saleTradePrintVo.setSaleNowDebt(saleTrade.getNowDebt());//本次欠款
//        saleTradePrintVo.setSaleTotalDebt(saleTrade.getTotalDebt());//累计欠款
//        saleTradePrintVo.setSaleNowTotalDebt(saleTrade.getCurrentTotalDebt());//本次累计欠款
//        saleTradePrintVo.setSaleDiscountAmount(trade.getDiscountFee());//优惠金额
//        saleTradePrintVo.setSalePostFee(String.valueOf(getNum(saleTrade.getOutTotalFee()).subtract(getNum(saleTrade.getReturnTotalFee()))));//出货商品总金额-退货商品总金额
////        BigDecimal partSalePaymentFee = getNum(saleTrade.getAliPayFee()).add(getNum(saleTrade.getWxPayFee())).add(getNum(saleTrade.getCashFee()));
//        if (CollectionUtils.isNotEmpty(saleTrade.getPayInfos())) {
//            BigDecimal salePaymentFee = saleTrade.getPayInfos().stream().map(PayInfo::getPayFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//            saleTradePrintVo.setSalePaymentFee(salePaymentFee.toString());//支付宝支付+微信支付+现金支付+ 财务自定义支付方式[]支付的总和
//        }
//        saleTradePrintVo.setSalePaymentMsg(buildSalePaymentMsg(saleTrade));//自定义支付详情
//        saleTradePrintVo.setBalance(saleTrade.getBalance());//分销商余额
//        saleTradePrintVo.setCredit(saleTrade.getCredit());//分销商授信额度
//        saleTradePrintVo.setTotalBalance(saleTrade.getTotalBalance());//分销商剩余总金额
//        saleTradePrintVo.setSaleDeliveryTime(DateUtil.format(saleTrade.getDeliveryTime(), "yyyy-MM-dd HH:mm:ss"));//最晚发货时间
//        saleTradePrintVo.setSaleRemarkPic(buildRemarkPic(saleTrade)); //图片备注
//        saleTradePrintVo.setSaleTradeWaveCode(saleTrade.getWaveId() == null ? "" : saleTrade.getWaveId().toString());//波次号
//        try {
//            Staff saleTradeSubmitter = staffMap.get(saleTrade.getSubmitterId());
//            String saleTradeSubmitterPhone = saleTradeSubmitter == null ? "" : saleTradeSubmitter.getPhone();
//            saleTradePrintVo.setSaleTradeSubmitterPhone(secretRequest.decode(saleTradeSubmitterPhone));//开单人手机号
//        } catch (Exception e) {
//            logger.error(("电话号码转换出错"), e);
//        }
//        //表格数据saleShortId
//        saleTradePrintVo.setSaleDistributorName(saleTrade.getDistributorName());//分销商名称
//
//    }
//
//    private BigDecimal getNum(String num) {
//        if (StringUtils.isEmpty(num) || Objects.equals("null", num)) {
//            return BigDecimal.ZERO;
//        }
//        return new BigDecimal(num).setScale(3);
//    }
//
//    /**
//     * 按款打印 我们走这个逻辑
//     *
//     * @param idToItem         idToItem
//     * @param saleTradePrintBO saleTradePrintBO
//     * @param dataSource       数据源 0 提交配货数据  1 实际出货数据 2 以上两种数据匹配汇总
//     */
//    private List<SaleTradePrintVO.SaleTradeTableVO> buildMergeSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, SaleTradePrintBO saleTradePrintBO, Integer dataSource,
//                                                                                Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template,
//                                                                                Map<Long, DmjSku> idToSku, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint) {
//        //按款打印
//        /**
//         * 1 获取 根据sysItemId 分组的打印数据
//         */
//        Map<String, List<Order>> sysItemIdOrdersMap;//实际要打的数据
//        Map<String, List<Order>> sysItemIdReferenceOrdersMap = new HashMap<>();//作为参照的数据
//        Map<String, List<Order>> sysItemIdreturnOrdersMap = null;//缺货数据
//        boolean onlyOutOrder = false;
//        if (dataSource == 0) {
//            //提交配货数据 不许要参照
//            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getPlanOrders())) {
//                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanOrders(), template, idToSku);
//            } else {
//                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getOutOrders(), template, idToSku);
//            }
//        } else if (dataSource == 1) {
//            //实际出货数据 提交配货数缺货数要参照提交数据
//            if (CollectionUtils.isNotEmpty(saleTradePrintBO.getOutOrders())) {
//                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getOutOrders(), template, idToSku);
//            } else {
//                sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanOrders(), template, idToSku);
//            }
//            sysItemIdReferenceOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanOrders(), template, idToSku);
//            if (sysItemIdReferenceOrdersMap.size() == 0) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                onlyOutOrder = true;
//            }
//            sysItemIdreturnOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReturnOrders(), template, idToSku);
//        } else {
//            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
//            sysItemIdOrdersMap = orders2ItemIdMap(saleTradePrintBO.getPlanOrders(), template, idToSku);
//            sysItemIdReferenceOrdersMap = orders2ItemIdMap(saleTradePrintBO.getOutOrders(), template, idToSku);
//            if (sysItemIdOrdersMap.size() == 0) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                sysItemIdOrdersMap = sysItemIdReferenceOrdersMap;
//                onlyOutOrder = true;
//            }
//            sysItemIdreturnOrdersMap = orders2ItemIdMap(saleTradePrintBO.getReturnOrders(), template, idToSku);
//        }
//        /**
//         * 2 获取合并后的orders
//         */
//        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildMergeSaleTradeTableVos(staff, sysItemIdOrdersMap,
//                sysItemIdReferenceOrdersMap, idToItem, false, dataSource, saleTradePrintBO.getSaleTrade().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint));
//        if (sysItemIdreturnOrdersMap != null && sysItemIdreturnOrdersMap.size() > 0) {
//            saleTradeTableVOS.addAll(buildMergeSaleTradeTableVos(staff, sysItemIdreturnOrdersMap, sysItemIdReferenceOrdersMap, idToItem, true, dataSource,
//                    saleTradePrintBO.getSaleTrade().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, idToSku, assoGoodsSectionSkuMap, hasMergePrint));
//        }
//
//        // 排序
//        sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType);
//        //填充序号
//        fillTableNo(saleTradeTableVOS);
//        return saleTradeTableVOS;
//    }
//
//    /**
//     * 按款打印的组装数据
//     *
//     * @param sysItemIdOrdersMap          实际数据map
//     * @param sysItemIdReferenceOrdersMap 参照数据map
//     * @param idToItem                    idToItem
//     * @param isReturn                    isReturn
//     * @param dataSource                  dataSource
//     */
//    private List<SaleTradePrintVO.SaleTradeTableVO> buildMergeSaleTradeTableVos(Staff staff, Map<String, List<Order>> sysItemIdOrdersMap, Map<String, List<Order>> sysItemIdReferenceOrdersMap,
//                                                                                Map<Long, DmjItem> idToItem, boolean isReturn, Integer dataSource,
//                                                                                Integer saleTradeStatus, boolean onlyOutOrder, Map<String, String> itemCategoryIdNameMap,
//                                                                                UserInvoicesTemplate template, Map<Long, DmjSku> idToSku, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint) {
//        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
//        boolean isV1Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() || EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue();
//        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>();
//        for (Map.Entry<String, List<Order>> entry : sysItemIdOrdersMap.entrySet()) {
//            SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO = new SaleTradePrintVO.SaleTradeTableVO();
//            saleTradeTableVOS.add(saleTradeTableVO);
//            List<Order> orders = entry.getValue();
//            Order order_1 = orders.size() > 0 ? orders.get(0) : null;
//            long itemSysId = order_1 == null ? 0 : order_1.getItemSysId();
//            DmjItem dmjItem = idToItem.get(itemSysId);
//            if (Objects.isNull(dmjItem)) {
//                throw new IllegalArgumentException(String.format("未获取到商品! 请确认商品是否删除或发生变更，系统商品id:%s，商品编码:%s", itemSysId, orders.size() > 0 ? orders.get(0).getSysOuterId() : "按款打印没有查询到子订单数据"));
//            }
//            //组装基本商品信息 和 规格信息
//            buildSomeFields(staff, saleTradeTableVO, dmjItem, null, itemCategoryIdNameMap);
//            String discountPrice = "0";
//            if (order_1 != null) {
//                discountPrice = StringUtils.isBlank(order_1.getPayment()) ? "0" :
//                        new BigDecimal(order_1.getPayment()).divide(new BigDecimal(order_1.getNum()), 2, RoundingMode.HALF_UP).toString();
//            }
//            Double salePrice = dmjItem.getPriceOutput();
//            //获取order中的第一个折扣率,默认100
//            saleTradeTableVO.setTableItemDiscountRate(order_1 != null && StringUtils.isNotBlank(order_1.getPrice()) && salePrice != 0 ? new BigDecimal((Double.parseDouble(order_1.getPrice()) / salePrice) * 100D).setScale(2, BigDecimal.ROUND_HALF_UP).toString() : " ");
//            //获取order中的第一个折后价
//            saleTradeTableVO.setTableItemDiscountPrice(discountPrice);
//            //获取order中的第一个单价
//            saleTradeTableVO.setTableItemPrice(order_1 != null ? order_1.getPrice() : "");//单价
//            // 装箱要求
//            saleTradeTableVO.setTablePackingAsk(PrintSaleTradeTemplateHelper.getPackingAsk(order_1));
//            saleTradeTableVO.setOuterIdOfItem(dmjItem.getOuterId());//主商家编码
//            int mergeItemNum = 0;//商品数量
//            BigDecimal mergeTotalAmont = new BigDecimal(0);//总价
//            Set<Long> assistUnitIdSet = new HashSet<>();
//            Double assistUnitNum = 0D;
//            String assistUnit = "";
//            Integer numPerBox = null; // 箱规
//            Integer numOfBox = null; // 箱数
//            Integer bulkNum = null; // 散装数
//            for (Order order : orders) {
//                int itemNum = order.getNum() == null ? 0 : isReturn ? -order.getNum() : order.getNum();
//                BigDecimal price = new BigDecimal(StringUtils.isBlank(order.getPrice()) ? "0" : order.getPrice());
//                BigDecimal totalAmont = price.multiply(new BigDecimal(itemNum));
//                mergeItemNum += itemNum;
//                mergeTotalAmont = mergeTotalAmont.add(totalAmont);
//                if (order instanceof SaleOrder) {
//                    SaleOrder saleOrder = (SaleOrder) order;
//                    Long assistUnitId = saleOrder.getAssistUnitId(); //存在没单位但是有数量的情况,所以不判断空了
//                    assistUnitIdSet.add(assistUnitId);
//                    Double orderAssistUnitNum = saleOrder.getAssistUnitNum();
//                    String orderAssistUnit = saleOrder.getAssistUnit();
//                    if (orderAssistUnitNum != null) {
//                        assistUnitNum += orderAssistUnitNum;
//                    }
//                    if (StringUtils.isBlank(assistUnit) && StringUtils.isNotBlank(orderAssistUnit)) {
//                        assistUnit = orderAssistUnit;
//                    }
//                }
//                // 解析获取批发收银数据
//                PackageInfo packageInfo = analysisTradeTable(order);
//                if (packageInfo != null) {
//                    // 获取箱规
//                    if (numPerBox == null && packageInfo.getNumPerBox() != null) {
//                        numPerBox = packageInfo.getNumPerBox();
//                    }
//                    // 累加箱数
//                    if (packageInfo.getNumOfBox() != null) {
//                        numOfBox = numOfBox == null ? packageInfo.getNumOfBox() : numOfBox + packageInfo.getNumOfBox();
//                    }
//                    // 累加散装数
//                    if (packageInfo.getBulkNum() != null) {
//                        bulkNum = bulkNum == null ? packageInfo.getBulkNum() : bulkNum + packageInfo.getBulkNum();
//                    }
//                }
//            }
//            saleTradeTableVO.setTableTotalGetterNum(String.valueOf(mergeItemNum));//合计数量
//            saleTradeTableVO.setTableItemNum(Integer.toString(mergeItemNum));//商品数量
//            if (assistUnitIdSet.size() == 1) {
//                saleTradeTableVO.setTableSaleAssistUnit(assistUnit); //销货单单据单位
//                saleTradeTableVO.setTableSaleAssistUnitNum(Objects.equals(0D, assistUnitNum) ? "" : String.valueOf(assistUnitNum)); //销货单单据单位数量
//            }
//            // 销售总价合计
//            buildTableSaleTotalAmount(staff, saleTradeTableVO);
//
//            saleTradeTableVO.setTableTotalAmount(mergeTotalAmont.toString());//总价
//            // 根据箱规计算箱数、散装数
//            computeBoxNum(saleTradeTableVO, numPerBox, numOfBox, bulkNum, hasMergePrint);
////            Integer boxNum = saleTradeTableVO.getTableBoxnum(); //箱规
////            if (boxNum != null && !Objects.equals(0, boxNum)) {
////                int calculateNum = mergeItemNum < 0 ? -mergeItemNum : mergeItemNum;
////                saleTradeTableVO.setTableNumOfBox(calculateNum / boxNum); //商品箱数
////                saleTradeTableVO.setTableRemainBulkNum(calculateNum % boxNum); //剩余散装数
////            }
//            // 组装唯一码
//            buildUniqueCodeStr(orders, saleTradeTableVO);
//
//            if (!isReturn && !onlyOutOrder) {
//                //不是退货 且 不是直接开单的需要记录提交配货数和缺货数
//                //退货没有提交数和缺货数
//                if (dataSource == 0) {
//                    saleTradeTableVO.setTablePlanNum(Integer.toString(mergeItemNum));//提交配货数取商品数
//                } else {
//                    List<Order> referenceOrders = sysItemIdReferenceOrdersMap.get(String.valueOf(itemSysId));
//                    int referenceMergeItemNum = 0;
//                    BigDecimal referencemergeTotalAmont = new BigDecimal(0);//参照总价
//                    if (CollectionUtils.isNotEmpty(referenceOrders)) {
//                        for (Order order : referenceOrders) {
//                            int itemNum = order.getNum() == null ? 0 : order.getNum();
//                            BigDecimal price = new BigDecimal(StringUtils.isBlank(order.getPrice()) ? "0" : order.getPrice());
//                            BigDecimal totalAmont = price.multiply(new BigDecimal(itemNum));
//                            referenceMergeItemNum += itemNum;
//                            referencemergeTotalAmont = referencemergeTotalAmont.add(totalAmont);
//                        }
//                    }
//                    if (dataSource == 1) {
//                        saleTradeTableVO.setTablePlanNum(Integer.toString(referenceMergeItemNum));//提交配货数取提交配货数
//                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
//                            saleTradeTableVO.setTableInsufficientNum(String.valueOf(referenceMergeItemNum - mergeItemNum));//缺货数是提交数减去出货数
//                        }
//                    } else {
//                        saleTradeTableVO.setTableItemNum(Integer.toString(referenceMergeItemNum));//商品数量 要取出货的数量
//                        saleTradeTableVO.setTableTotalAmount(referencemergeTotalAmont.toString());//总价 要取出货的总价
//                        saleTradeTableVO.setTablePlanNum(Integer.toString(mergeItemNum));//提交配货数取提交配货数
//                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
//                            saleTradeTableVO.setTableInsufficientNum(String.valueOf(mergeItemNum - referenceMergeItemNum));//缺货数是提交数减去出货数
//                        }
//                    }
//                }
//            }
//            if (isV3Template) {
//                // V3颜色\尺寸等数据处理
//                buildV3ColorAndOthers(staff, itemSysId, orders, idToSku, saleTradeTableVO, assoGoodsSectionSkuMap);
//            }
//        }
//        return saleTradeTableVOS;
//    }
//
//    /**
//     * @description  计算箱数、散装数
//     *
//     * <AUTHOR>
//     * @date    2024-04-18 16:30
//     * @param	saleTradeTableVO
//     * @param	numPerBox	箱规
//     * @param	numOfBox	箱数
//     * @param	bulkNum	    散装数
//     * @param	hasMergePrint	    是否合并打印
//     * @return  void
//    */
//    private void computeBoxNum(SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, Integer numPerBox, Integer numOfBox, Integer bulkNum, Boolean hasMergePrint) {
//        if (hasMergePrint && numPerBox != null && numOfBox != null && bulkNum != null && numPerBox != 0 && bulkNum >= numPerBox){
//            // 若散装数大于或等于箱规，散装数转为箱数，并累加
//            numOfBox += bulkNum / numPerBox;
//            bulkNum = bulkNum % numPerBox;
//        }
//        saleTradeTableVO.setTableBoxnum(numPerBox);
//        saleTradeTableVO.setTableNumOfBox(numOfBox);
//        saleTradeTableVO.setTableRemainBulkNum(bulkNum);
//    }
//
//    /**
//     * 组装表格数据  明细维度
//     */
//    private List<SaleTradePrintVO.SaleTradeTableVO> buildSingleSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku,
//                                                                                 SaleTradePrintBO saleTradePrintBO, Integer dataSource, Integer sortType, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint) {
//        //明细打印
//        /**
//         * 1 获取 根据sysItemId 分组的打印数据
//         */
//        List<Order> orders;//实际要打的数据
//        Map<String, Order> skuReferenceOrdersMap = new HashMap<>();//作为参照的数据
//        List<Order> returnOrders = null;//缺货数据
//        boolean onlyOutOrder = false;//是否直接开单
//        if (dataSource == 0) {
//            //提交配货数据 不许要参照
//            orders = saleTradePrintBO.getPlanOrders();
//            if (CollectionUtils.isEmpty(orders)) {
//                orders = saleTradePrintBO.getOutOrders();
//            }
//        } else if (dataSource == 1) {
//            //实际出货数据 提交配货数缺货数要参照提交数据
//            orders = saleTradePrintBO.getOutOrders();
//            if (CollectionUtils.isEmpty(orders)) {
//                orders = saleTradePrintBO.getPlanOrders();
//            }
//            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getPlanOrders());
//            if (skuReferenceOrdersMap.size() == 0) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                onlyOutOrder = true;
//            }
//            returnOrders = saleTradePrintBO.getReturnOrders();
//        } else {
//            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
//            orders = saleTradePrintBO.getPlanOrders();
//            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getOutOrders());
//            if (CollectionUtils.isEmpty(orders)) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                orders = saleTradePrintBO.getOutOrders();
//                onlyOutOrder = true;
//            }
//            returnOrders = saleTradePrintBO.getReturnOrders();
//        }
//        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, orders,
//                skuReferenceOrdersMap, dataSource, false, sortType, saleTradePrintBO.getSaleTrade().getStatus(), onlyOutOrder, itemCategoryIdNameMap, template, assoGoodsSectionSkuMap, hasMergePrint));
//        if (CollectionUtils.isNotEmpty(returnOrders)) {
//            //退款全不是直接开单
//            saleTradeTableVOS.addAll(buildSingleSaleTradeTableVos(staff, idToItem, idToSku, returnOrders, skuReferenceOrdersMap, dataSource, true, sortType,
//                    saleTradePrintBO.getSaleTrade().getStatus(), false, itemCategoryIdNameMap, template, assoGoodsSectionSkuMap, hasMergePrint));
//        }
//
//        // 排序
//        sortSaleTradeTableVOS(template, saleTradeTableVOS, sortType);
//
//        // 序号
//        fillTableNo(saleTradeTableVOS);
//        return saleTradeTableVOS;
//    }
//
//    /**
//     * 组装表格数据  明细维度
//     */
//    private void buildCrossItemProperties(SaleTradePrintVO saleTradePrintVo, Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku,
//                                          SaleTradePrintBO saleTradePrintBO, Integer dataSource, Integer sortType, UserInvoicesTemplate template, Boolean hasMergePrint) {
//        /**
//         * 1 获取 根据sysItemId 分组的打印数据
//         */
//        List<Order> orders;//实际要打的数据
//        Map<String, Order> skuReferenceOrdersMap = new HashMap<>();//作为参照的数据
//        List<Order> returnOrders = null;//缺货数据
//        boolean onlyOutOrder = false;//是否直接开单
//        if (dataSource == 0) {
//            //提交配货数据 不许要参照
//            orders = saleTradePrintBO.getPlanOrders();
//            if (CollectionUtils.isEmpty(orders)) {
//                orders = saleTradePrintBO.getOutOrders();
//            }
//        } else if (dataSource == 1) {
//            //实际出货数据 提交配货数缺货数要参照提交数据
//            orders = saleTradePrintBO.getOutOrders();
//            if (CollectionUtils.isEmpty(orders)) {
//                orders = saleTradePrintBO.getPlanOrders();
//            }
//            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getPlanOrders());
//            if (skuReferenceOrdersMap.size() == 0) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                onlyOutOrder = true;
//            }
//            returnOrders = saleTradePrintBO.getReturnOrders();
//        } else {
//            //两种数据匹配汇总 打印 提交数据  缺货需要参照 出库数据
//            orders = saleTradePrintBO.getPlanOrders();
//            skuReferenceOrdersMap = orders2skuMap(saleTradePrintBO.getOutOrders());
//            if (CollectionUtils.isEmpty(orders)) {
//                //没有提交配货数据视为直接开单 记录一下日志留下证据
//                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleTradePrintBO.getSaleTrade().getId() + "使用两种数据合并打印,没有提交配货数据!视为直接开单 打印取出货数据!"));
//                orders = saleTradePrintBO.getOutOrders();
//                onlyOutOrder = true;
//            }
//            returnOrders = saleTradePrintBO.getReturnOrders();
//        }
//
//        // 退货数据的价格和数量需要展示为负数
//        if (CollectionUtils.isNotEmpty(returnOrders)) {
//            for (Order order : returnOrders) {
//                order.setTotalFee("-" + order.getTotalFee());         // 总价 = 单价*数量
//                order.setPayment("-" + order.getPayment());           // 支付金额 = 单价*数量 - 优惠金额
//                order.setNum(-order.getNum());                   // 数量
//            }
//            orders.addAll(returnOrders);
//        }
//        try {
//            PrintSaleTradeTemplateHelper.buildCrossItemProperties(saleTradePrintVo, staff, idToItem, idToSku, orders, skuReferenceOrdersMap, dataSource, onlyOutOrder, template, hasMergePrint);
//        } catch (Exception e) {
//            logger.error(LogHelper.buildLog(staff, "获取销货单交叉数据异常 :" + e.getMessage()), e);
//        }
//
//    }
//
//    /**
//     * * 组装表格数据  明细维度
//     *
//     * @param idToItem              idToItem
//     * @param idToSku               idToSku
//     * @param orders                打印的数据源
//     * @param skuReferenceOrdersMap 参照的map
//     * @param dataSource            dataSource
//     * @param isReturn              isReturn
//     * @param sortType              sortType
//     * @param saleTradeStatus       销货单状态
//     * @param onlyOutOrder          是否为直接开单
//     */
//    private List<SaleTradePrintVO.SaleTradeTableVO> buildSingleSaleTradeTableVos(Staff staff, Map<Long, DmjItem> idToItem, Map<Long, DmjSku> idToSku, List<Order> orders,
//                                                                                 Map<String, Order> skuReferenceOrdersMap, Integer dataSource,
//                                                                                 boolean isReturn, Integer sortType, Integer saleTradeStatus,
//                                                                                 boolean onlyOutOrder, Map<String, String> itemCategoryIdNameMap, UserInvoicesTemplate template, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap, Boolean hasMergePrint) {
//        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS = new ArrayList<>();
//        for (Order order : orders) {
//            SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO = new SaleTradePrintVO.SaleTradeTableVO();
//            saleTradeTableVOS.add(saleTradeTableVO);
//            Long itemSysId = order.getItemSysId();
//            Long skuSysId = order.getSkuSysId();
//            DmjItem dmjItem = idToItem.get(itemSysId);
//            DmjSku dmjSku = idToSku.get(skuSysId);
//            if (dmjItem == null) {
//                throw new IllegalArgumentException(String.format("未获取到商品! 请确认商品是否删除或发生变更，系统商品id:%s，商品编码:%s", itemSysId, order.getSysOuterId()));
//            }
//            if (dmjSku == null) {
//                logger.debug(LogHelper.buildLog(staff, "商品id:" + itemSysId + "根据skuSysId:" + skuSysId + "未获取到商品规格!!!"));
//            }
//            //组装基本商品信息 和 规格信息
//            buildSomeFields(staff, saleTradeTableVO, dmjItem, dmjSku, itemCategoryIdNameMap);
//            //只能取order里的信息 合并时丢弃
//            saleTradeTableVO.setTableItemPrice(StringUtils.isBlank(order.getPrice()) ? "0" : order.getPrice());//单价
//            Double salePrice = dmjSku == null ? dmjItem.getPriceOutput() : dmjSku.getPriceOutput();
//            //折扣率
//            saleTradeTableVO.setTableItemDiscountRate(StringUtils.isNotBlank(order.getPrice()) && salePrice != null && salePrice != 0 ? new BigDecimal((Double.parseDouble(order.getPrice()) / salePrice) * 100D).setScale(2, BigDecimal.ROUND_HALF_UP).toString() : " ");
//            //折后价
//            String discountPrice = StringUtils.isBlank(order.getPayment()) ? "0" :
//                    new BigDecimal(order.getPayment()).divide(new BigDecimal(order.getNum()), 2, RoundingMode.HALF_UP).toString();
//            saleTradeTableVO.setTableItemDiscountPrice(discountPrice);
//            if (order instanceof SaleOrder) {
//                SaleOrder saleOrder = (SaleOrder) order;
//                saleTradeTableVO.setTableSaleRemark(StringUtils.isBlank(saleOrder.getSaleRemark()) ? "" : saleOrder.getSaleRemark());
//            }
//            //商品可合并信息
//            int itemNum = order.getNum() == null ? 0 : isReturn ? -order.getNum() : order.getNum();
//            saleTradeTableVO.setTableItemNum(Integer.toString(itemNum));//商品数量
//            // 装箱要求
//            saleTradeTableVO.setTablePackingAsk(PrintSaleTradeTemplateHelper.getPackingAsk(order));
//            // 销售总价合计
//            buildTableSaleTotalAmount(staff, saleTradeTableVO);
//            saleTradeTableVO.setTableTotalGetterNum(Integer.toString(itemNum));//合计数量
//            saleTradeTableVO.setOuterIdOfItem(dmjItem.getOuterId());//主商家编码
//            BigDecimal price = new BigDecimal(saleTradeTableVO.getTableItemPrice());
//            BigDecimal totalAmont = price.multiply(new BigDecimal(saleTradeTableVO.getTableItemNum()));
//            saleTradeTableVO.setTableTotalAmount(totalAmont.toString());//总价
//            String assoGoodSectionKey = itemSysId + "_" + (skuSysId == null || skuSysId < 0 ? "0" : skuSysId);
//            if (assoGoodsSectionSkuMap.get(assoGoodSectionKey) != null) {
//                saleTradeTableVO.setTableItemSectionCode(assoGoodsSectionSkuMap.get(assoGoodSectionKey).getGoodsSectionCode());//商品货位
//            }
//            if (CollectionUtils.isNotEmpty(order.getGoodsSectionCodes())) {
//                saleTradeTableVO.setTablePickSectionCode(Strings.join(",", order.getGoodsSectionCodes())); //拣选货位
//            }
//            if (order instanceof SaleOrder) {
//                // 设置销货单单据辅助单位\单位数量
//                SaleOrder saleOrder = (SaleOrder) order;
//                saleTradeTableVO.setTableSaleAssistUnit(saleOrder.getAssistUnit());
//                saleTradeTableVO.setTableSaleAssistUnitNum(saleOrder.getAssistUnitNum() != null ? String.valueOf(saleOrder.getAssistUnitNum()) : "");
//            }
//            // 组装批发收银的数据
//            assembleTradeTable(saleTradeTableVO, order, hasMergePrint);
////            Integer boxNum = saleTradeTableVO.getTableBoxnum(); //箱数
////            if (boxNum != null && !Objects.equals(0, boxNum)) {
////                int calculateNum = itemNum < 0 ? -itemNum : itemNum;
////                saleTradeTableVO.setTableNumOfBox(calculateNum / boxNum); //商品箱数
////                saleTradeTableVO.setTableRemainBulkNum(calculateNum % boxNum); //剩余散装数
////            }
//            // 组装唯一码
//            buildUniqueCodeStr(Collections.singletonList(order), saleTradeTableVO);
//
//            if (!isReturn && !onlyOutOrder) {
//                //不是退货 且 不是直接开单的需要记录提交配货数和缺货数
//                if (dataSource == 0) {
//                    saleTradeTableVO.setTablePlanNum(Integer.toString(itemNum));//提交配货数取商品数
//                } else {
//                    String key = itemSysId + "_" + (skuSysId == null || skuSysId < 0 ? "0" : skuSysId);
//                    Order referenceOrder = skuReferenceOrdersMap.get(key);
//                    int referenceNum = referenceOrder == null || referenceOrder.getNum() == null ? 0 : referenceOrder.getNum();
//                    BigDecimal referenceTotalAmont = new BigDecimal(saleTradeTableVO.getTableItemPrice()).multiply(new BigDecimal(referenceNum));
//                    if (dataSource == 1) {
//                        saleTradeTableVO.setTablePlanNum(Integer.toString(referenceNum));//提交配货数取提交配货数
//                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
//                            saleTradeTableVO.setTableInsufficientNum(Integer.toString(referenceNum - itemNum));//缺货数是提交数减去出货数
//                        }
//                    } else {
//                        saleTradeTableVO.setTableItemNum(Integer.toString(referenceNum));//商品数量 取实际出货数量
//                        saleTradeTableVO.setTableTotalAmount(referenceTotalAmont.toString());//总价 取实际出货总价
//                        saleTradeTableVO.setTablePlanNum(Integer.toString(itemNum));//提交配货数取提交配货数
//                        if (Integer.valueOf(2).equals(saleTradeStatus)) {
//                            saleTradeTableVO.setTableInsufficientNum(Integer.toString(itemNum - referenceNum));//缺货数是提交数减去出货数
//                        }
//                    }
//                }
//            }
//        }
//        return saleTradeTableVOS;
//    }
//
//    /**
//     * @description  组装批发收银给的箱规、箱数、散装数
//     *
//     * <AUTHOR>
//     * @date    2024-04-17 19:50
//     * @param   saleTradeTableVO
//     * @param   order
//     * @param   hasMergePrint
//     * @return  void
//     */
//    private void assembleTradeTable(SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, Order order, Boolean hasMergePrint) {
//        if (order == null) {
//            return;
//        }
//        try {
//            String extInfoStr = ((SaleOrder) order).getExtInfoStr();
//            if (StringUtils.isBlank(extInfoStr)) {
//                return;
//            }
//            OrderExtInfo extInfo = JSONObject.parseObject(extInfoStr, OrderExtInfo.class);
//            if (extInfo == null || extInfo.getPackageInfo() == null) {
//                return;
//            }
//            PackageInfo packageInfo = extInfo.getPackageInfo();
//
//            Integer numPerBox = packageInfo.getNumPerBox();
//            Integer numOfBox = packageInfo.getNumOfBox();
//            Integer bulkNum = packageInfo.getBulkNum();
//            // 合并打印才处理是否计算
//            if (hasMergePrint && numPerBox != null && numPerBox != 0 && bulkNum != 0 && bulkNum >= numPerBox) {
//                numOfBox += bulkNum / numPerBox;
//                bulkNum = bulkNum % numPerBox;
//            }
//            saleTradeTableVO.setTableBoxnum(numPerBox);
//            saleTradeTableVO.setTableNumOfBox(numOfBox);
//            saleTradeTableVO.setTableRemainBulkNum(bulkNum);
//        }catch (Exception e) {
//            logger.error(String.format("批发收银数据解析失败：%s", JSONObject.toJSONString(order)), e);
//        }
//    }
//
//    /**
//     * @description  解析批发收银给的箱规、箱数、散装数
//     *
//     * <AUTHOR>
//     * @date    2024-04-17 19:50
//     * @param   order
//     * @return  void
//     */
//    private PackageInfo analysisTradeTable(Order order) {
//        if (order == null) {
//            return null;
//        }
//        try {
//            String extInfoStr = ((SaleOrder) order).getExtInfoStr();
//            if (StrUtil.isBlank(extInfoStr)) {
//                return null;
//            }
//            OrderExtInfo extInfo = JSONObject.parseObject(extInfoStr, OrderExtInfo.class);
//            if (extInfo == null || extInfo.getPackageInfo() == null) {
//                return null;
//            }
//            return extInfo.getPackageInfo();
//        }catch (Exception e) {
//            logger.error(String.format("批发收银数据解析失败：%s", JSONObject.toJSONString(order)), e);
//        }
//        return null;
//    }
//
//    /**
//     * 组装基本商品信息 和 规格信息
//     */
//    private void buildSomeFields(Staff staff, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, DmjItem dmjItem, DmjSku dmjSku, Map<String, String> itemCategoryIdNameMap) {
//        //商品基本信息
//        saleTradeTableVO.setTableItemPriceOutput(checkSalePower(staff) ? dmjItem.getPriceOutput() != null ? dmjItem.getPriceOutput().toString() : "" : "***");//销售价
//        saleTradeTableVO.setTableItemOutId(StringUtils.isBlank(dmjItem.getOuterId()) ? "" : dmjItem.getOuterId());//款号
//        saleTradeTableVO.setTableItemTitle(StringUtils.isBlank(dmjItem.getTitle()) ? "" : dmjItem.getTitle());//商品名称
//        saleTradeTableVO.setTableItemShortTitle(StringUtils.isBlank(dmjItem.getShortTitle()) ? "" : dmjItem.getShortTitle());//商品简称
//        saleTradeTableVO.setTableItemRemark(StringUtils.isBlank(dmjItem.getRemark()) ? "" : dmjItem.getRemark());//商品备注
//        saleTradeTableVO.setTableItemPic(StringUtils.isBlank(dmjItem.getPicPath()) ? "" : dmjItem.getPicPath());//商品图片
//        saleTradeTableVO.setTableItemUnit(StringUtils.isBlank(dmjItem.getUnit()) ? "" : dmjItem.getUnit()); //商品计量单位
//        // 商品分类
//        saleTradeTableVO.setTableItemCategory(getItemCategory(dmjItem, itemCategoryIdNameMap));
//        //商品类目
//        saleTradeTableVO.setTableCatId(StringUtils.isBlank(dmjItem.getCatName()) ? "" : dmjItem.getCatName());
//        // 商品条形码
//        saleTradeTableVO.setTableItemBarcode(StringUtils.isBlank(dmjItem.getBarcode()) ? "" : dmjItem.getBarcode());
//        // 规格条形码
//        saleTradeTableVO.setTableSkuBarcode(dmjItem.getBarcode());
//        //规格信息
//        if (dmjSku != null) {
//            String priceOutput = dmjSku.getPriceOutput() != null ? dmjSku.getPriceOutput().toString() : "";
//            saleTradeTableVO.setTableItemPriceOutput(checkSalePower(staff) ? priceOutput : "***");//销售价
//            // 拆分商品数据 [第一、二、三、四属性]
//            bulidSkuPropOther(dmjSku);
//
//            JSONObject object = JSONObject.parseObject(dmjSku.getExtendFieldValues());
//            if (object != null) {
//                String marketPrice = object.getString("marketPrice");
//                saleTradeTableVO.setTableItemMarketPrice(checkPurchasePricePower(staff) ? marketPrice : "***");//市场价
//            }
//            saleTradeTableVO.setTableSkuOuterId(StringUtils.isBlank(dmjSku.getSkuOuterId()) ? dmjSku.getItemOuterId() : dmjSku.getSkuOuterId());//规格商家编码
//            // 和规格商家编码条码
//            saleTradeTableVO.setTableSkuOuterIdJsBarCode(saleTradeTableVO.getTableSkuOuterId());
//            saleTradeTableVO.setTableSkuName(StringUtils.isBlank(dmjSku.getPropertiesName()) ? "" : dmjSku.getPropertiesName());//规格名称
//            saleTradeTableVO.setTableItemProperties(StringUtils.isBlank(dmjSku.getPropertiesAlias()) ? saleTradeTableVO.getTableItemProperties() : dmjSku.getPropertiesAlias());
//            saleTradeTableVO.setTableSkuRemark(StringUtils.isBlank(dmjSku.getRemark()) ? "" : dmjSku.getRemark());//规格备注
//            saleTradeTableVO.setTableSkuPic((StringUtils.isBlank(dmjSku.getSkuPicPath()) || Objects.equals("/resources/css/build/images/no_pic.png", dmjSku.getSkuPicPath())) ? dmjItem.getPicPath() : dmjSku.getSkuPicPath());//规格图片
//            saleTradeTableVO.setTableSkuUnit(StringUtils.isBlank(dmjSku.getUnit()) ? "" : dmjSku.getUnit()); //SKU计量单位
//            // 商品成分
//            saleTradeTableVO.setTableComponent(PrintMerchantCodeHelper.getItemComponentSplitLine(dmjSku.getSkuComponent()));
//            // 箱规
////            saleTradeTableVO.setTableBoxnum(dmjSku.getBoxnum());
//            // 颜色属性
//            saleTradeTableVO.setTableColorProperty(dmjSku.getPropColor());
//            // 其他属性
//            saleTradeTableVO.setTableOtherProperty(dmjSku.getPropOtherFirst());
//            // 规格条形码
//            saleTradeTableVO.setTableSkuBarcode(StringUtils.isBlank(dmjSku.getBarcode()) ? dmjItem.getBarcode() : dmjSku.getBarcode());
//        } else {
//            JSONObject object = JSONObject.parseObject(dmjItem.getExtendFieldValues());
//            if (object != null) {
//                String marketPrice = object.getString("marketPrice");
//                saleTradeTableVO.setTableItemMarketPrice(checkPurchasePricePower(staff) ? marketPrice : "***");//市场价
//            }
//            saleTradeTableVO.setTableItemProperties("");
//            saleTradeTableVO.setTableSkuOuterId(dmjItem.getOuterId());//规格商家编码
//            // 和规格商家编码条码
//            saleTradeTableVO.setTableSkuOuterIdJsBarCode(dmjItem.getOuterId());
//            saleTradeTableVO.setTableSkuName("");//规格名称
//            saleTradeTableVO.setTableSkuRemark("");//规格备注
//            saleTradeTableVO.setTableSkuPic(dmjItem.getPicPath());//无规格图片 取商品主图
//            // 商品成分
//            saleTradeTableVO.setTableComponent(PrintMerchantCodeHelper.getItemComponentSplitLine(dmjItem.getComponent()));
//            // 箱规
////            saleTradeTableVO.setTableBoxnum(dmjItem.getBoxnum());
//        }
//    }
//
//    /**
//     * 装换 结账方式
//     */
//    private String conversionAccountType(int accountType) {
//        switch (accountType) {
//            case 1:
//                return "现付";
//            case 2:
//                return "记账";
//            case 3:
//                return "部分收款";
//            default:
//                return "未知结账方式";
//        }
//    }
//
//    /**
//     * 组装付款信息
//     */
//    private String buildPayMessage(SaleTrade saleTrade) {
//        if (StringUtils.isBlank(saleTrade.getPayType())) {
//            return "";
//        }
//        if (CollectionUtils.isEmpty(saleTrade.getPayInfos())) {
//            return "";
//        }
//        StringBuilder payMessage = new StringBuilder();
//        for (PayInfo payInfo : saleTrade.getPayInfos()) {
//            payMessage.append(payInfo.getPayTypeName()).append(":").append(payInfo.getPayFee() == null ? "0" : payInfo.getPayFee().toString());
//            if(StringUtils.isNotBlank(payInfo.getSerialNumber())){
//                payMessage.append("流水号:").append(payInfo.getSerialNumber());
//            }
//            if(StringUtils.isNotBlank(payInfo.getPayName())){
//                payMessage.append("收款账号:").append(payInfo.getPayName());
//            }
//            payMessage.append(";");
//        }
//
//        //String[] payTypes = saleTrade.getPayType().split(",");
//        //JSONObject receiveAccountNumber = StringUtils.isNotBlank(saleTrade.getReceiveAccountNumber()) ?
//        //        JSONObject.parseObject(saleTrade.getReceiveAccountNumber()) : null;
//        //JSONObject serialNumber = StringUtils.isNotBlank(saleTrade.getSerialNumber()) ? JSONObject.parseObject(saleTrade.getSerialNumber()) : null;
//        //for (String payType : payTypes) {
//        //    if ("1".equals(payType)) {
//        //        payMessage.append("现金收款:").append(StringUtils.isBlank(saleTrade.getCashFee()) ? "0" : saleTrade.getCashFee()).append(";");
//        //    } else if ("2".equals(payType) && receiveAccountNumber != null && serialNumber != null) {
//        //        payMessage.append("支付宝收款:").append(StringUtils.isBlank(saleTrade.getAliPayFee()) ? "0" : saleTrade.getAliPayFee()).append("流水号:").append(StringUtils.isBlank(serialNumber.getString("aliPayNumber")) ? "" : serialNumber.getString("aliPayNumber")).append("收款账号:").append(StringUtils.isBlank(receiveAccountNumber.getString("aliPayName")) ? "" : receiveAccountNumber.getString("aliPayName")).append(";");
//        //    }
//        //    if ("3".equals(payType) && receiveAccountNumber != null && serialNumber != null) {
//        //        payMessage.append("微信收款:").append(StringUtils.isBlank(saleTrade.getWxPayFee()) ? "0" : saleTrade.getWxPayFee()).append("流水号:").append(StringUtils.isBlank(serialNumber.getString("wxPayNumber")) ? "" : serialNumber.getString("wxPayNumber")).append("收款账号:").append(StringUtils.isBlank(receiveAccountNumber.getString("wxPayName")) ? "" : receiveAccountNumber.getString("wxPayName")).append(";");
//        //    }
//        //}
//
//        if (payMessage.toString().endsWith(";")) {
//            payMessage.deleteCharAt(payMessage.length() - 1);
//        }
//        return payMessage.toString();
//    }
//
//    /**
//     * 填充序号
//     */
//    private void fillTableNo(List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS) {
//        int num = 0;
//        for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO : saleTradeTableVOS) {
//            saleTradeTableVO.setTableNo(++num + "");
//        }
//    }
//
//    /**
//     * orders 根据商品id分组
//     */
//    private Map<String, Order> orders2skuMap(List<Order> orders) {
//        Map<String, Order> skuOrdersMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(orders)) {
//            for (Order order : orders) {
//                String key = order.getItemSysId() + "_" + (order.getSkuSysId() == null || order.getSkuSysId() < 0 ? "0" : order.getSkuSysId());
//                skuOrdersMap.put(key, order);
//            }
//        }
//        return skuOrdersMap;
//    }
//
//    /**
//     * orders 根据商品id分组
//     */
//    private Map<String, List<Order>> orders2ItemIdMap(List<Order> orders, UserInvoicesTemplate template, Map<Long, DmjSku> idToSku) {
//        boolean isV3Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue();
//        boolean isV1Template = EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() || EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue();
//        Map<String, List<Order>> sysItemIdOrdersMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(orders)) {
//            for (Order order : orders) {
//                Long itemSysId = order.getItemSysId();
//                String price = order.getPrice();
//                String key;
//                if (isV3Template) {
//                    key = itemSysId + "_" + price;
//                } else {
//                    key = itemSysId.toString();
//                }
//                List<Order> subOrders = sysItemIdOrdersMap.computeIfAbsent(key, k -> new ArrayList<>());
//                subOrders.add(order);
//            }
//        }
//        return sysItemIdOrdersMap;
//    }
//
//    /**
//     * 表格 排序
//     */
//    private void sortSaleTradeTableVO(List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS, Integer sortType) {
//        /**
//         * 注意 用这种方式排序 用于排序的字段不能为null
//         */
//        try {
//            if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_OUT_ID_SKU_NAME.equals(sortType)) {
//                //先款号再规格名称
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuName));
//            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_OUT_ID_SKU_OUT_ID.equals(sortType)) {
//                //先款号再规格编码
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuOuterId));
//            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_ITEM_REMARK_SKU_NAME.equals(sortType)) {
//                //先主商品备注再规格名称
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemRemark).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuName));
//            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_SKU_OUT_ID.equals(sortType)) {
//                //规格编码
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuOuterId));
//            } else if (SortUseInvoicesTemplateComparator.SALE_SORT_SKU_REMARK.equals(sortType)) {
//                //规格备注
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableSkuRemark));
//            }
//        } catch (Exception e) {
//            throw new IllegalArgumentException("排序出错!" + e.getMessage(), e);
//        }
//    }
//
//    private String buildSalePaymentMsg(SaleTrade saleTrade) {
//        if (saleTrade == null || CollectionUtils.isEmpty(saleTrade.getPayInfos())) {
//            return "";
//        }
//        StringBuilder sb = new StringBuilder();
//        List<PayInfo> payInfos = saleTrade.getPayInfos().stream().filter(x -> x.getPayFee() != null).collect(Collectors.toList());
//        for (PayInfo payInfo : payInfos) {
//            if (payInfo.getPayType() == 1) {
//                sb.append("现金支付(");
//                sb.append(payInfo.getPayFee().toPlainString());
//                sb.append(")");
//                sb.append(",");
//            }
//            if (payInfo.getPayType() == 2) {
//                sb.append("支付宝支付(");
//                sb.append(payInfo.getPayFee().toPlainString());
//                sb.append(")");
//                sb.append(",");
//            }
//            if (payInfo.getPayType() == 3) {
//                sb.append("微信支付(");
//                sb.append(payInfo.getPayFee().toPlainString());
//                sb.append(")");
//                sb.append(",");
//            }
//            if (payInfo.getPayType() == 5) {
//                sb.append("余额支付(");
//                sb.append(payInfo.getPayFee().toPlainString());
//                sb.append(")");
//                sb.append(",");
//            }
//            if (payInfo.getPayType() == 9) {
//                sb.append("在线支付(");
//                sb.append(payInfo.getPayFee().toPlainString());
//                sb.append(")");
//                sb.append(",");
//            }
//        }
//        if (sb.length() > 0) {
//            sb.deleteCharAt(sb.length() - 1);
//        }
//        return sb.toString();
//    }
//
//    /**
//     * 拼接数据
//     */
//    private String joinMsg(List msgs) {
//        StringBuilder sb = new StringBuilder();
//        for (Object msg : msgs) {
//            if (msg != null) {
//                sb.append(msg.toString()).append(",");
//            }
//        }
//        if (sb.toString().endsWith(",")) {
//            sb.deleteCharAt(sb.length() - 1);
//        }
//        return sb.toString();
//    }
//
//    /**
//     * 获取商品分类
//     */
//    private String getItemCategory(DmjItem dmjItem, Map<String, String> itemCategoryIdNameMap) {
//        //查询商品分类数据
//        if (null == dmjItem) {
//            return PtConfigConst.UNDEFINE_CATEGORY;
//        } else {
//            String sellerCids = dmjItem.getSellerCids();
//            //只有一级分类
//            if (sellerCids == null || !sellerCids.contains(PtConfigConst.ENG_COMMA)) {
//                String cName = itemCategoryIdNameMap.get(sellerCids);
//                return StringUtils.isEmpty(cName) ? PtConfigConst.UNDEFINE_CATEGORY : cName;
//            } else {
//                //多个分类
//                String[] cids = sellerCids.split(PtConfigConst.ENG_COMMA);
//                List<String> cNameList = Lists.newArrayList();
//                for (String cid : cids) {
//                    String cName = itemCategoryIdNameMap.get(cid);
//                    cNameList.add(StringUtils.isEmpty(cName) ? PtConfigConst.UNDEFINE_CATEGORY : cName);
//                }
//                return StringUtils.join(cNameList, PtConfigConst.ENG_COMMA);
//            }
//        }
//    }
//
//    /**
//     * 获取分销商信息
//     */
//    private Map<Long, DmsBaseDistributorInfoDto> getDistributorMap(Staff staff, List<SaleTrade> saleTradeList) {
//        // 分销商信息
//        Map<Long, DmsBaseDistributorInfoDto> distributorMap = new HashMap<>();
//        List<Long> distributorIds = saleTradeList.stream().map(SaleTrade::getDistributorId).filter(Objects::nonNull).collect(Collectors.toList());
//        if (distributorIds.size() > 0) {
//            DmsQueryDistributorInfoRequest request = new DmsQueryDistributorInfoRequest();
//            request.setDistributorCompanyIdList(distributorIds);
//            request.setSupplierCompanyId(staff.getCompanyId());
//            if (distributorIds.size() > 20) {
//                request.setPage(new Page().setPageSize(distributorIds.size()));
//            }
//            DmsDistributorInfoResponse response = dmsTradeService.queryDistributorInfo(request);
//            if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getDmsBaseDistributorInfoDtoList().getList())) {
//                distributorMap = response.getDmsBaseDistributorInfoDtoList().getList().stream().collect(
//                        Collectors.toMap(DmsBaseDistributorInfoDto::getDistributorCompanyId, p -> p, (v1, v2) -> v2));
//            } else {
//                logger.debug(LogHelper.buildLogHead(staff).append("获取分销商信息失败:").append(response.getErrorMsg()));
//            }
//        }
//        return distributorMap;
//    }
//
//    /**
//     * 获取商品分类
//     */
//    private Map<String, String> getItemCategoryIdNameMap(Staff staff, Map<Long, DmjItem> idToItem) {
//        Map<String, String> itemCategoryIdNameMap = new HashMap<>();
//        Set<Long> itemCategoryIdSet = Sets.newHashSet();
//        idToItem.forEach((key, dmjItem) -> PrintItemUtils.handleItemCategory(dmjItem.getSellerCids(), itemCategoryIdSet, itemCategoryIdNameMap));
//        List<Long> itemCategoryIdList = Lists.newArrayList(itemCategoryIdSet);
//        PrintItemUtils.queryItemCategory(staff, itemCategoryIdList, itemCategoryIdNameMap, printPageSearchService);
//        return itemCategoryIdNameMap;
//    }
//
//    /**
//     * 拼接分销商
//     */
//    private String buildDistributorAddress(DmsBaseDistributorInfoDto distributorInfo) {
//        String separator = " ";
//        StringBuilder address = new StringBuilder();
//        if (StringUtils.isNotBlank(distributorInfo.getContactProvince())) {
//            address.append(distributorInfo.getContactProvince()).append(separator);
//        }
//        if (StringUtils.isNotBlank(distributorInfo.getContactCity())) {
//            address.append(distributorInfo.getContactCity()).append(separator);
//        }
//        if (StringUtils.isNotBlank(distributorInfo.getContactDistrict())) {
//            address.append(distributorInfo.getContactDistrict()).append(separator);
//        }
//        if (StringUtils.isNotBlank(distributorInfo.getContactDetailAddress())) {
//            address.append(distributorInfo.getContactDetailAddress()).append(separator);
//        }
//        if (address.length() > 0) {
//            return address.substring(0, address.length() - 1);
//        }
//        return address.toString();
//    }
//
//    //拆分商品数据 [第一、二、三、四属性]
//    private void bulidSkuPropOther(DmjSku dmjSku) {
//        String[] propOthers = dmjSku.getPropOther().split(";");
//        if (propOthers.length > 1) {
//            dmjSku.setPropOtherFirst(propOthers[0]);
//            dmjSku.setPropOtherSecond(propOthers[1]);
//            if (propOthers.length > 2) {
//                dmjSku.setPropOtherThird(propOthers[2]);
//            }
//        } else {
//            dmjSku.setPropOtherFirst(dmjSku.getPropOther().replaceAll(";", ""));
//        }
//    }
//
//    /**
//     * 通过templateId和templateType查找template
//     * 再给trade设置templateName
//     */
//    private void setTemplateName(Staff staff, Map<Long, Trade> tradeMap) {
//        for (Map.Entry<Long, Trade> tradeEntry : tradeMap.entrySet()) {
//            Trade trade = tradeEntry.getValue();
//            Long templateId = trade.getTemplateId();
//            if (templateId != null) {
//                Integer templateType = trade.getTemplateType();
//                IExpressTemplateBase template = null;
//                if (0 == templateType) {
//                    template = userTemplateService.userQuery(staff, templateId, false);
//                } else {
//                    template = wlbTemplateService.userQuery(staff, templateId, false);
//                }
//                if (template != null) {
//                    trade.setTemplateName(template.getName());
//                } else {
//                    logger.debug(LogHelper.buildLog(staff, String.format("订单[%s]快递模板[%s,%s]找不到", trade.getSid(), trade.getTemplateId(), trade.getTemplateType())));
//                }
//            } else {
//                logger.debug(LogHelper.buildLog(staff, String.format("订单[%s]的templateId为空", trade.getSid())));
//            }
//        }
//    }
//
//    private void buildItemSearch(List<Order> orders, Map<Long, Set<Long>> itemIdMap) {
//        if (CollectionUtils.isEmpty(orders)) {
//            return;
//        }
//        for (Order order : orders) {
//            if (order.getItemSysId() != null) {
//                itemIdMap.computeIfAbsent(order.getItemSysId(), v -> new HashSet<>()).add(order.getSkuSysId() == null || order.getSkuSysId() < 0 ? 0 : order.getSkuSysId());
//            }
//        }
//    }
//
//    /**
//     * 获取商品货位
//     */
//    private Map<String, AssoGoodsSectionSku> getAssoGoodsSectionSkuMap(Staff staff, List<SaleTrade> saleTradeList, Map<Long, Set<Long>> itemIdMap) {
//        Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap = new HashMap<>();
//        AssoGoodsSectionSkuParams skuParams = new AssoGoodsSectionSkuParams();
//        List<Long> wareHouseIds = saleTradeList.stream().map(SaleTrade::getWarehouseId).collect(Collectors.toList());
//        List<Long> itemIds = new ArrayList<>();
//        List<Long> skuIds = new ArrayList<>();
//        for (Map.Entry<Long, Set<Long>> entry : itemIdMap.entrySet()) {
//            itemIds.add(entry.getKey());
//            skuIds.addAll(entry.getValue());
//        }
//        skuParams.setWarehouseIds(wareHouseIds);
//        skuParams.setSysItemIds(itemIds);
//        skuParams.setSysSkuIds(skuIds);
//        /**
//         * 查询货位
//         */
//        List<AssoGoodsSectionSku> skuList = wmsService.queryAssoGoodsSectionSkuList(staff, skuParams);
//
//        if (CollectionUtils.isNotEmpty(skuList)) {
//            //查询仓储的配置,读取货位编码显示配置
//            WmsConfig config = wmsService.getConfig(staff);
//            //根据显示配置显示,重新设值
//            for (AssoGoodsSectionSku assoGoodsSectionSku : skuList) {
//                assoGoodsSectionSku.setGoodsSectionCode(assoGoodsSectionSku.getGoodsSectionCode() == null ? "" : WmsUtils.encodeGsCode(config, assoGoodsSectionSku.getGoodsSectionCode()));
//            }
//            assoGoodsSectionSkuMap.putAll(skuList.stream().collect(Collectors.toMap(t -> t.getSysItemId() + "_" + (t.getSysSkuId() == null ? "0" : t.getSysSkuId()), sku -> sku, ((k1, k2) -> k1))));
//        }
//        return assoGoodsSectionSkuMap;
//    }
//
//    /**
//     * 销售总价合计
//     *
//     * @param staff
//     * @param saleTradeTableVO
//     */
//    private void buildTableSaleTotalAmount(Staff staff, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
//        if (checkSalePower(staff)) {
//            BigDecimal price = new BigDecimal(StringUtils.isBlank(saleTradeTableVO.getTableItemPriceOutput()) ? "0" : saleTradeTableVO.getTableItemPriceOutput());
//            saleTradeTableVO.setTableSaleTotalAmount(price.multiply(new BigDecimal(saleTradeTableVO.getTableItemNum())).toString());
//        } else {
//            saleTradeTableVO.setTableSaleTotalAmount("***");
//        }
//    }
//
//    /**
//     * 销售总价合计
//     *
//     * @param staff
//     * @param saleTradePrintVO
//     */
//    private void buildSaleTotalAmount(Staff staff, SaleTradePrintVO saleTradePrintVO) {
//        BigDecimal total = new BigDecimal(0);
//        if (checkSalePower(staff)) {
//            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO : saleTradePrintVO.getSaleTradeTableVos()) {
//                total = total.add(new BigDecimal(saleTradeTableVO.getTableSaleTotalAmount()));
//            }
//            saleTradePrintVO.setSaleTotalAmount(total.toString());
//        }
//    }
//
//    private void calculateBox(SaleTradePrintVO saleTradePrintVO, Boolean hasMergePrint) {
//        int boxNum = 0;
//        int remainBulkNum = 0;
//        List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVos = saleTradePrintVO.getSaleTradeTableVos();
//        if (CollectionUtils.isNotEmpty(saleTradeTableVos)) {
//            Integer tableBoxnum = saleTradeTableVos.get(0).getTableBoxnum();
//            // 所以箱规一样则计算箱数汇总
//            boolean b = true;
//            for (SaleTradePrintVO.SaleTradeTableVO saleTradeTableVo : saleTradeTableVos) {
//                boxNum += Optional.ofNullable(saleTradeTableVo.getTableNumOfBox()).orElse(0);
//                remainBulkNum += Optional.ofNullable(saleTradeTableVo.getTableRemainBulkNum()).orElse(0);
//                if (b && tableBoxnum == null) {
//                    b = false;
//                }else if (b) {
//                    b = tableBoxnum.equals(saleTradeTableVo.getTableBoxnum());
//                }
//            }
//            // 单独打印无需累加，若散装数大于或等于箱规，散装数转为箱数，并累加
//            if (hasMergePrint && b && tableBoxnum != 0 && remainBulkNum != 0 && remainBulkNum >= tableBoxnum){
//                boxNum += remainBulkNum / tableBoxnum;
//                remainBulkNum = remainBulkNum % tableBoxnum;
//            }
//        }
//        if (Objects.equals(0, boxNum) && Objects.equals(0, remainBulkNum)) {
//            saleTradePrintVO.setSaleOutBoxTotal("0箱");
//        } else if (Objects.equals(0, remainBulkNum)) {
//            saleTradePrintVO.setSaleOutBoxTotal(boxNum + "箱");
//        } else {
//            saleTradePrintVO.setSaleOutBoxTotal(boxNum + "箱+" + remainBulkNum);
//        }
//        saleTradePrintVO.setItemNumOfBoxTotal(Integer.toString(boxNum));
//        saleTradePrintVO.setItemRemainBulkNumTotal(Integer.toString(remainBulkNum));
//    }
//
//    /**
//     * 交叉结构V3数据的处理
//     */
//    private void buildV3ColorAndOthers(Staff staff, Long itemSysId, List<Order> orders, Map<Long, DmjSku> idToSku, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO, Map<String, AssoGoodsSectionSku> assoGoodsSectionSkuMap) {
//        Map<String, Map<String, Integer>> colorOtherPropNum = new HashMap<>();
//        StringBuilder tableSaleRemark = new StringBuilder();
//        for (Order order : orders) {
//            Long skuSysId = order.getSkuSysId();
//            DmjSku dmjSku = idToSku.get(skuSysId);
//            String propOther = "";
//            String propColor = "";
//            if (dmjSku == null) {
//                logger.debug(LogHelper.buildLog(staff, "商品id:" + itemSysId + "根据skuSysId:" + skuSysId + "未获取到商品规格!!!"));
//            } else {
//                propOther = StringUtils.isBlank(dmjSku.getPropOther()) ? "无" : dmjSku.getPropOther().replaceAll(";", "").toUpperCase();
//                propColor = dmjSku.getPropColor();
//                itemSysId = dmjSku.getSysItemId();
//            }
//            colorOtherPropNum.computeIfAbsent(propColor, k -> new HashMap<>());
//            Map<String, Integer> otherPropNum = colorOtherPropNum.get(propColor);
//            Integer otherNum = otherPropNum.getOrDefault(propOther, 0);
//            otherPropNum.put(propOther, otherNum + order.getNum());
//            String saleRemark = order.getSaleRemark();
//            if (StringUtils.isNotBlank(saleRemark) && !tableSaleRemark.toString().contains(saleRemark)) {
//                tableSaleRemark.append(saleRemark).append(";");
//            }
//        }
//        if (tableSaleRemark.length() > 0) {
//            tableSaleRemark.deleteCharAt(tableSaleRemark.length() - 1);
//        }
//        List<SaleTradePrintVO.SaleTradeTableDetailVO> tableDetailVOS = new ArrayList<>();
//        Set<String> allOtherName = new HashSet<>();
//        // V3新结构,每个商品一行,一行中有颜色尺寸的表格数据
//        for (Map.Entry<String, Map<String, Integer>> colorEntry : colorOtherPropNum.entrySet()) {
//            SaleTradePrintVO.SaleTradeTableDetailVO tableDetailVO = new SaleTradePrintVO.SaleTradeTableDetailVO();
//            tableDetailVOS.add(tableDetailVO);
//            String color = colorEntry.getKey();
//            Map<String, Integer> otherNumMap = colorEntry.getValue();
//            List<SaleTradePrintVO.TablePropOther> tablePropOthers = new ArrayList<>();
//            for (Map.Entry<String, Integer> otherEntry : otherNumMap.entrySet()) {
//                String otherName = otherEntry.getKey();
//                SaleTradePrintVO.TablePropOther propOther = new SaleTradePrintVO.TablePropOther();
//                tablePropOthers.add(propOther);
//                propOther.setTablePropOtherName(otherName);
//                propOther.setTablePropOtherNum(otherEntry.getValue() == null ? "0" : otherEntry.getValue().toString());
//                allOtherName.add(otherName);
//            }
//            tableDetailVO.setTablePropColorName(color);
//            tableDetailVO.setTablePropOthers(tablePropOthers);
//        }
//        List<String> otherPropNameList = new ArrayList<>(allOtherName);
//        // 尺码排序
//        PrintTemplateHelper.otherSort(otherPropNameList);
//        // 默认颜色排序
//        tableDetailVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableDetailVO::getTablePropColorName, Collator.getInstance(Locale.CHINA)));
//        saleTradeTableVO.setSaleTradeTableDetailVos(tableDetailVOS); //颜色尺寸属性
//        saleTradeTableVO.setTablePropOtherNames(otherPropNameList); //该商品下所有的尺寸
//        String assoGoodSectionKey = itemSysId + "_0";
//        if (assoGoodsSectionSkuMap.get(assoGoodSectionKey) != null) {
//            saleTradeTableVO.setTableItemSectionCode(assoGoodsSectionSkuMap.get(assoGoodSectionKey).getGoodsSectionCode());//商品货位
//        }
//        saleTradeTableVO.setTableSaleRemark(String.valueOf(tableSaleRemark)); //销货单商品备注
//    }
//
//    /**
//     * 排序
//     */
//    private void sortSaleTradeTableVOS(UserInvoicesTemplate template, List<SaleTradePrintVO.SaleTradeTableVO> saleTradeTableVOS, Integer sortType) {
//        Integer specificSortType = PrintTemplateHelper.getSpecificSortType(template.getFieldValues());
//        if (EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V3.getValue() == template.getSysTemplateId().intValue()) {
//            // V3只有两种排序,默认排序和按商家编码
//            if (specificSortType != null && StockConstants.ACCORDING_NUMBER_SORT == specificSortType) {
//                saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getOuterIdOfItem));
//            }
//        } else if (EnumInvoicesType.SALETRADE_CROSSSTRUCTURE.getValue() == template.getSysTemplateId().intValue() ||
//                EnumInvoicesType.SALETRADE_CROSSSTRUCTURE_V2.getValue() == template.getSysTemplateId().intValue()) {
//            // V1/V2有三种排序,默认排序,按货号排序,按货号再按颜色排序
//            if (specificSortType != null) {
//                if (StockConstants.ACCORDING_NUMBER_SORT == specificSortType) {
//                    saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId));
//                } else if (StockConstants.ACCORDING_NUMBER_COLOR_SORT == specificSortType) {
//                    saleTradeTableVOS.sort(Comparator.comparing(SaleTradePrintVO.SaleTradeTableVO::getTableItemOutId).thenComparing(SaleTradePrintVO.SaleTradeTableVO::getTableColorProperty, Collator.getInstance(Locale.CHINA)));
//                }
//            }
//        } else {
//            // 非交叉结构的才走下面这个排序
//            sortSaleTradeTableVO(saleTradeTableVOS, sortType);
//        }
//    }
//
//    private static List<String> buildRemarkPic(SaleTrade saleTrade) {
//        TradeExtInfo extInfo = saleTrade.getExtInfo();
//        String extInfoJson = saleTrade.getExtInfoJson();
//        if (extInfo == null && StringUtils.isNotBlank(extInfoJson)) {
//            extInfo = JSONObject.parseObject(extInfoJson, TradeExtInfo.class);
//        }
//        if (extInfo != null) {
//            return extInfo.getFileRemarks();
//        }
//        return null;
//    }
//
//    public void buildUniqueCodeStr(List<Order> orders, SaleTradePrintVO.SaleTradeTableVO saleTradeTableVO) {
//        if (CollectionUtils.isEmpty(orders)) {
//            saleTradeTableVO.setTableUniqueCode("");
//        }
//        List<String> uniqueCodeList = new ArrayList<>();
//        for (Order order : orders) {
//            try {
//                String extInfoStr = ((SaleOrder) order).getExtInfoStr();
//                if (StringUtils.isBlank(extInfoStr)) {
//                    return;
//                }
//                OrderExtInfo extInfo = JSONObject.parseObject(extInfoStr, OrderExtInfo.class);
//                if (extInfo == null) {
//                    return;
//                }
//                List<String> uniqueCodes = extInfo.getUniqueCodes();
//                if (CollectionUtils.isNotEmpty(uniqueCodes)) {
//                    uniqueCodeList.addAll(uniqueCodes);
//                }
//            } catch (Exception e) {
//                logger.error(String.format("批发收银数据解析失败：%s", JSONObject.toJSONString(order)), e);
//            }
//        }
//        saleTradeTableVO.setTableUniqueCode(CollectionUtils.isNotEmpty(uniqueCodeList) ? Strings.join(",", uniqueCodeList) : "");
//    }
//
//    /**
//     * 合并打印的时候,将唯一码合并
//     */
//    public void mergeUniqueCodes(Order mergeOrder, Order order) {
//        try {
//            List<String> uniqueCodeList = new ArrayList<>();
//            String mergeExtInfoStr = ((SaleOrder) mergeOrder).getExtInfoStr();
//            OrderExtInfo mergeExtInfo = StringUtils.isBlank(mergeExtInfoStr) ? new OrderExtInfo() : JSONObject.parseObject(mergeExtInfoStr, OrderExtInfo.class);
//            if (CollectionUtils.isNotEmpty(mergeExtInfo.getUniqueCodes())) {
//                uniqueCodeList.addAll(mergeExtInfo.getUniqueCodes());
//            }
//            String extInfoStr = ((SaleOrder) order).getExtInfoStr();
//            OrderExtInfo extInfo = StringUtils.isBlank(extInfoStr) ? new OrderExtInfo() : JSONObject.parseObject(extInfoStr, OrderExtInfo.class);
//            List<String> uniqueCodes = extInfo.getUniqueCodes();
//            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
//                uniqueCodeList.addAll(uniqueCodes);
//            }
//            mergeExtInfo.setUniqueCodes(uniqueCodeList);
//            // 塞回JSON,便于后续组装
//            ((SaleOrder) mergeOrder).setExtInfoStr(JSONObject.toJSONString(mergeExtInfo));
//        } catch (Exception e) {
//            logger.error(String.format("批发收银数据解析失败：%s", JSONObject.toJSONString(order)), e);
//        }
//    }
//}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.config.entity.AutoCancelAuditSellerMemoKeywords;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.erp.db.model.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交易配置
 *
 * <AUTHOR>
 */
@Table(name = "trade_config")
public class TradeConfig extends Model {
    private static final long serialVersionUID = 7929955515106826974L;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 是否开启发货单
     */
    private Integer openDeliverPrint;

    /**
     * 是否先打印快递单
     */
    private Integer firstPrintExpress;

    /**
     * 是否开启包裹称重
     */
    private Integer openPackageWeigh;

    /**
     * 是否开启包裹验货
     */
    private Integer openPackageExamine;

    /**
     * 是否开启发货
     */
    private Integer openConsign;

    /**
     * 是否开启识别码输入
     */
    private Integer openIdentCode;

    /**
     * 是否开启后置打印识别码输入
     */
    private Integer postOpenIdentCode;

    /**
     * 是否开启波次打印
     */
    private Integer openWave;

    /**
     * 是否开启后置打印
     */
    private Integer openPrintDelay;

    /**
     * 是否开启多品多件播种
     */
    private Integer openSeed;

    /**
     * 是否开启多品多件播种打印
     */
    private Integer openSeedPrint;

    /**
     * 播种位置号提示为排数
     */
    private Boolean openSeedRowPosition;

    /**
     * 开启波次后置打印后可以包装验货 默认关闭
     */
    private Boolean openWavePackage;

    /**
     * 单件波次拣选完成后，自动验货
     * 注：单件波次打印后，拣选完成后，自动验货
     */
    private Boolean singleWaveAutoPackage;

    /**
     * 后置--出单成功后自动验货(不持久化)
     */
    private Integer openPackAutoPost;

    /**
     * 波次批打--单件波次出单成功后自动验货(不持久化)
     */
    private Integer openPackAutoSingleWaveBatch;

    /**
     * 播种排数
     */
    private Integer seedRowNum;

    /**
     * 播种每排位置数
     */
    private Integer seedRowPositionNum;

    /**
     * 波次列表排序按照相同波次规则排序 0：关闭， 1：开启 默认0
     */
    private Integer waveSortRule;

    /**
     * 波次列表排序按照波次创建时间，0：正序，1：倒序，默认0
     */
    private Integer waveSortTime;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 默认按升序
     */
    private String orderName;

    /**
     * 是否开启自动上传发货 0:关闭 1:开启
     */
    private Integer openUploadConsign;

    /**
     * 自动上传发货的位置：1、打印 2、包装 3、称重 4、生成波次 5、审核
     */
    private Integer uploadConsignName;

    /**
     * 视图层展现订单时显示平台图片还是系统图片，默认系统部片
     * 1 平台图片， 其他（0）系统图片
     */
    private Integer orderImageSource;

    /**
     * 平台状态是否覆盖系统  0：不覆盖 1：覆盖
     */
    private Integer coverSysStatus;

    /**
     * 自动生成波次 0:不生成,1:生成
     */
    private Integer autoCreateWave;

    /**
     * 自动生成波次的时间间隔，最小间隔为10min
     */
    private Integer autoCreateWaveTimeInterval;

    /**
     * 最后一次自动生成波次时间
     */
    private Date lastAutoCreateWaveTime;

    /**
     * 是否开启波次编码打印新逻辑，默认否
     */
    private Integer openWaveCodePrintNew;

    /**
     * 订单修改的时候，金额计算是否开启联动 0：否 1：是
     */
    private Integer openLinkPayment;

    /**
     * 新增或修改订单时是否允许修改商品单价
     */
    private Integer openPriceUpdate;

    /**
     * 是否开启黑名单设置 0：未开启 1：开启
     */
    private Integer openBlack;
    /**
     * 是否同步待付款订单
     */
    private Integer syncWaitPay;
    /**
     * 是否开启订单财审功能 0：未开启 1：仅财审手工订单  2:财审全部订单
     */
    private Integer openFinanceAudit;

    /**
     * 是否开启自动赠品匹配 0：未开启 1：开启
     *
     * @return
     */
    private Integer autoGiftMatch;

    /**
     * 是否开启物流跟踪 0：未开启 1：开启
     */
    private Integer openLogisticsTracking;

    private String timeoutLimit;

    /**
     * 是否开启一单多包单号上传平台备注
     */
    private Integer openMultiOutsidUpload;

    /**
     * 生成波次时是否自动获取单号
     */
    private Boolean waveAutoGetWaybillCode;

    /**
     * 异常订单在第三方仓库出库后，ere也会自动出库
     * 1为开启，0为不开启，默认开启
     */
    private Integer excepTradeParty3SendGoods;

    /**
     * 财审选择店铺，以","分隔
     */
    private String financeUserIdsStr;

    /**
     * 是否开启重新上传发货：0:关闭 1:开启
     */
    private Integer openReUploadConsign;

    /**
     * 只有【是否开启重新上传发货】开启 才生效
     * 是否开启预发货的拆分订单重新上传发货：0:关闭 1:开启
     * https://gykj.yuque.com/entavv/xb9xi5/kmv29r
     */
    private Integer openSplitReUploadConsign;

    /**
     * 播种后置打印扫描类型 0：商家编码 1：商家编码+位置号
     */
    private Integer seedScanType;

    /**
     * 展示平台规格属性
     */
    private Integer showSkuProperties;

    /**
     * 是否开启包装验货支持扫包材
     */
    private Integer openScanItemPackma;

    /**
     * 是否开启订单优惠金额分摊，开启后
     * 1. 不同步待付款订单
     * 2. 订单的优惠金额按商品实付金额的比例分配到各商品（非已关闭）上
     */
    private Integer shareDiscount;
    /**
     * 订单同步时如果有部分退款关闭的商品且之前已匹配赠品，是否重新匹配赠品
     */
    private Integer rematchGift;

    /**
     * 是否开启包装验货语音提示
     */
    private Integer openPackVoiceHints;

    /**
     * 是否开启买家留言/卖家备注语音提示
     */
    private Integer openMessageVoiceHints;

    /**
     * 是否开启系统备注语音提示
     */
    private Integer openRemarkVoiceHints;

    /**
     * 是否开启赠品语音提示
     */
    private Integer openGiftVoiceHints;

    /**
     * 是否开启发票语音提示
     */
    private Integer openInvoiceVoiceHints;

    /**
     * 赠品活动有效期
     */
    private Integer giftDelayHour;

    /**
     * 智能合单异常订单是否合并 0：合并 1：不合并
     *
     * @deprecated 已经迁移到 TradeMergeConf mamFilterExcep
     */
    @Deprecated
    private Integer mamFilterExcep;

    /**
     * 智能合单拆单是否合并 0：不合并 1：合并
     *
     * @deprecated 已经迁移到 TradeMergeConf mamFilterSplit
     */
    @Deprecated
    private Integer mamFilterSplit;

    /**
     * 智能合单已经取消合单的订单是否合并  0：不合并 1：合并
     *
     * @deprecated 已经迁移到 TradeMergeConf mamFilterUndo
     */
    @Deprecated
    private Integer mamFilterUndo;

    /**
     * 取消审核时是否需要保留单号配置，目前默认值1+2+4+8=15
     * 0 取消审核时不保留运单号
     * 包含1,取消审核时,待打印订单保留运单号
     * 包含2,取消审核时,待包装订单保留运单号
     * 包含4,取消审核时,待称重订单保留运单号
     * 包含8,取消审核时,待发货订单保留运单号
     * 包含16,取消审核时,待财审订单保留运单号
     */
    private Integer unAuditWaybillCancel;

    /**
     * 是否开启备注异常
     */
    private Integer openMemoExp;

    /**
     * 是否开启强制包装验货
     */
    private Integer openForceTradePack;

    /**
     * 是否开启快递公司语音提示
     */
    private Integer openExpressCompanyVoiceHints;

    /**
     * 是否开启自动审核 0 不开启，1开启
     */
    private Integer openAutoAudit;

    /**
     * 是否开启快递智能匹配
     */
    private Integer openExpressMatch;

    /**
     * 结束波次时自动重新审核未完成的订单
     *
     * @see FinishWaveTradeAutoUnauditTypeEnum
     */
    private Integer finishWaveTradeAutoUnaudit;

    /**
     * 0 没有开启自动反审核
     * 1 平台改地址自动反审核
     * 2 平台改商品自动反审核
     * 4 改备注自动反审核
     * 8 平台商品退款自动反审核（非退款完成）
     * ......
     * 如果配置了多个，则为上面多个枚举的和
     */
    private Integer autoCancelAudit;

    /**
     * 开始缺货拆单时上传备注
     */
    private Integer openUploadSellerMemo;

    /**
     * 拼多多订单发货后（已发货/交易成功），是否允许修改快递模板
     * 1 允许 0 不允许 默认为 1
     */
    private Integer allowChangeTemplate;

    private Integer autoUnattainableNotApplyStock;

    private Integer expressWhetherFilterTrade;

    /**
     * 抖店物流探查功能
     */
    private Integer fxgLogisticsExploration;

    private Integer mergeTradeClearTemplate;

    /**
     * 是否开启回车直接打印  0:不开启   1:开启
     */
    private Boolean isPrintDirect;


    /**
     * 后置打印回车直接打印配置  0:全部打印   1:只打已检
     */
    private Integer postPrintDirectConfig;

    /**
     * 播种打印回车直接打印配置  0:全部打印   1:只打已播
     */
    private Integer seedPrintDirectConfig;

    /**
     * 开启打印单件波次 1:开启 0：关闭
     */
    private Integer openPrintSingleWave;

    /**
     * 开启生成波次时先匹配规则再配货
     * 0：关闭（默认） 1：开启
     */
    private Integer openRuleFirstThenAllocate;

    /**
     * 支持分段拣选的类型,PickingType
     */
    private String subSectionPickTypes;

    /**
     * 是否允许取消发货
     */
    private Integer cancelConsign;

    /**
     * 波次播种支持对所有未播订单（包含已打印和未打印）进行播种  0:不支持已打印,仅未打印  1:支持已打印
     */
    private Integer waveSeedType;

    /**
     * 只允许一单多件进入播种流程
     * 0：关闭（默认）1：开启
     */
    private Integer onlyTradeMultiSeed;

    /**
     * 开启仅缺货异常生成波次
     */
    private Integer openExcepTradeGenerateWave;

    /**
     * 自动创建拣货单 0:不创建,1:创建
     */
    private Integer autoCreateVipPick;

    public Integer getCancelConsign() {
        return cancelConsign;
    }

    public void setCancelConsign(Integer cancelConsign) {
        this.cancelConsign = cancelConsign;
    }

    /**
     * 智能审核时，先匹配模板
     */
    private Integer openAutoAuditMatchTemplate;

    public Integer getAllowChangeTemplate() {
        return allowChangeTemplate;
    }

    public void setAllowChangeTemplate(Integer allowChangeTemplate) {
        this.allowChangeTemplate = allowChangeTemplate;
    }

    /**
     * 重新审核时清除取消缺货发货标记
     */
    private Integer unAuditReplaceInsufficientCanceled;

    /**
     * 审核是否自动获取单号 1 是 0 否
     */
    private Integer auditAutoGetWaybillCode;
    /**
     * 跨境订单 审核是否自动获取单号 1 是 0 否
     */
    private Integer abroadAuditAutoGetWaybillCode;

    /**
     * 是否开启边检边播 0 不开启，1开启
     */
    private Integer openPrintInPicking;

    /**
     * 是否开启波次备注，如果开启则在点击"生成波次"，"批量生成波次"时需要先生成备注
     * 0 不开启，1开启，默认不开启
     */
    private Integer openWaveRemark;

    /**
     * 是否开启拣选中取消波次
     * 0 不开启，1开启，默认开启
     */
    private Integer openPickingCancel;

    /**
     * 播种拆分安排位置号
     * 默认0：新拆分的订单使用新位置号，原始订单使用老位置号
     * 1：新拆分的订单使用老位置号，原始订单使用新位置号
     */
    private Integer seedSplitAssignPositionNo;

    /**
     * 订单波次直接拣选进暂存区
     * 0：默认，不开启；1：开启
     */
    private Integer openDirectPickIntoWss;

    /**
     * 后置打印匹配订单顺序
     * 0:优先加急订单1:优先按订单承诺时间
     */
    private Integer postPrintMatchTradeSort;

    /**
     * 拆分发货上传的备注信息
     */
    private SplitConsignUploadData splitConsignUploadData;

    /**
     * 播种是否允许退款订单
     * 默认0，1：不允许
     */
    private Integer seedNotAllowRefund;

    public Integer getAuditAutoGetWaybillCode() {
        return auditAutoGetWaybillCode;
    }

    public void setAuditAutoGetWaybillCode(Integer auditAutoGetWaybillCode) {
        this.auditAutoGetWaybillCode = auditAutoGetWaybillCode;
    }

    /**
     * 推送消息相关配置，很多配置组成一个json串
     * openReissueChat—是否开启补发订单消息推送配置(0,1),
     * openChangeitemChat—是否开启换货订单消息推送配置(0,1),
     * openDelayChat——是否开启延迟订单推送配置(0,1)
     * days—天数（开启延迟订单推送配置才会有值）
     * isCoverWeight(理论重量是否覆盖实际重量)
     * itemExcepOpen-查询是否使用新的itemExcep字段
     * <p>
     * autoCancelAuditStatus
     * 平台信息变更时,哪些状态的订单自动反审核,多个取和
     * 1 待打印, 2 待包装, 4 待称重, 8 待发货, 16 待财审
     * autoWaitAuditUndoMerge 平台修改地址后，对待审核的合并订单自动取消合单（仅适用于合并订单，不适合拆合订单）
     * <p>
     * aiAuditOrder 智能审核顺序：payTime 订单付款时间降序 incluedTagIds 包含某些标签订单优先 mutilItemNum 多件订单优先 urgent 加急订单优先
     * aiAuditOrder 智能审核顺序：（beforePayTimeAndAfterTimeoutActionTime：现货订单按付款时间先排序，预售订单再按计划发货时间排序）（payTimeAndTimeoutActionTime：现货订单按付款时间、预售订单按计划发货时间混合排序）
     * incluedTagIds 当aiAuditOrder=incluedTagIds时，对应的标签值
     * openScalpNotApplyStock: 空包订单不锁定库存，0：不开启 1：开启 默认：0(及不开启这个配置)
     * openRefundpNotApplyStock：退款中商品不占用库存，0：不开启 1：开启 默认：0(及不开启这个配置)
     * preLock 库存预留方式，0：预留库存 1：不预留库存
     * auditAutoInsufficientCanceled  审核的时候，如果订单缺货，是否自动使用缺货发货 0：关闭 1：开启
     * auditIncluedInsufficientStock 审核的时候，缺货的订单也允许查询出来进行分配，只针对后置锁定。 0：关闭 1：开启
     * aiAuditOrderShops 智能审核店铺的优先级
     * openTradePay 1为开启 0为不开启 默认不开启
     * matchTagAfterGift 是否匹配完赠品之后在匹配标签 0否，1是
     * showPlatTitle 是否展示平台名称 0否，1是
     * openChatConfig 是否开启客服配置
     * matchTagAfterGift 是否匹配完赠品之后在匹配标签 0否，1是
     * showPlatTitle 是否展示平台名称 0否，1是
     * allowRemoveAllPlatItem: 允许删除所有平台商品, 0：不允许，1允许 默认：0
     * matchFreightZeroWeight  零重运费匹配是否开启 0否，1是
     * autoSuitTransformSingle 未发货订单首次同步进系统时，套件商品自动转为单品. 0:否 1：是 默认否
     * mergeAfterMatchGift 合单后重新计算赠品 0:否 1:是
     * mergeAfterMatchWarehouse 合单后重新匹配仓库 0:否 1:是
     * mergeAfterMatchExpress 合单后自动匹配快递模板 0:否 1:是
     * splitInsufficientAllowGift 缺货拆分时，不允许单独把赠品拆出去 0：允许 1：不允许 默认：0(及不开启这个配置)
     * packForceType 强制验货，订单配置 0：直接发货整笔订单 1：直接拆分订单，将订单拆分为已验货商品和未验货商品
     * packUploadInfo 强制验货，要上传的信息 skuSysId：规格商家编码。sysSkuPropertiesName：规格。forcePackNum：强制验货数量。staffName：验货员。packDate：操作时间。operType：操作类型
     * openPackUploadSellerMemo 强制验货上传备注配置 0：不上传备注信息。 1：上传备注信息，追加信息到原订单备注。2：上传备注信息，覆盖信息到原订单备注。
     * packSellerFlag 强制验货，旗帜配置
     * openCheckRepeatCod 是否开启判断重复货到付款订单
     * insufficientAnalyzeAudit 后置锁单：缺货分析时，分析成功的订单自动执行审核。 0:开启 1:关闭 默认：0（开启）
     * splitAutoAudit 后置锁单：订单按商品库存/缺货拆分时，拆分出去的正常有货并且审核过的订单，自动执行审核。 0:开启 1:关闭 默认：0（开启）
     * showMainSysOuterId 是否显示主商家编码
     * aiAuditExceptionOpen 包含指定异常时，订单也参与预留库存 0：关闭 1：开启（默认0）
     * aiAuditExceptionMsg  包含指定异常时，订单也参与预留库存，具体的异常信息
     * itemNumExcludeAfterSendGoods  计算商品数量的时候排除已发货后的商品
     * refundAutoSplit 商品发起退款，自动拆分订单
     * suitsSubOrderFeeShare 套件子商品金额分摊；0：按子商品分销价；1：按子商品移动加权成本价；2：子商品历史成本价
     * addressChangeAppliedUsers 抖音店铺订单，买家自主修改地址，需要商家审核
     * giftOrderSalePriceConfig 匹配赠品时，赠品的分销价读取配置，0-未开启，赠品分销价默认0；1-开启，取分销价管理设置的分销价
     * haveOrderExtInfo 订单是否包含order_ext信息 1-包含 0-不包含 默认 0
     * giftOrderSalePriceConfig 匹配赠品时，赠品的分销价读取配置，0-未开启，赠品分销价默认0；1-开启，取分销价管理设置的分销价
     * addressChangeAppliedUsers 抖音店铺订单，买家自主修改地址，需要商家审核
     * minutesAfterPaidOrderAreNotDisplayed 不显示已付款X分钟内的订单
     * queryOrderExtInfo 查询订单时，是否查询order_ext信息 1-查询
     * itemInactiveAutoSplit 商品标记停用，订单自动执行拆分 1-拆分 0-不拆封 默认为0
     * dewuPlatformDelayShipTime  毒（得物、云仓）平台延迟发货时间，单位（小时）
     * addressChangeAppliedUsers 抖音店铺订单，买家自主修改地址，需要商家审核
     * changePddLogisticsInfoExpress 更改拼多多物流信息自动重算快递
     * {@link com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum}
     * minutesAfterPaidOrderAreNotDisplayed 不显示已付款X分钟内的订单
     * giftOrderSalePriceConfig 匹配赠品时，赠品的分销价读取配置，0-未开启，赠品分销价默认0；1-开启，取分销价管理设置的分销价
     * jdWarehouseTradeSubStock 京东自营京东直发订单扣减库存
     * platformUpdateItemRematchGiveaway 平台改商品重新计算赠品
     * shopPresellMatchTag 淘宝天猫店铺预售标签匹配
     * showSupplierName 订单列表中显示商品的供应商名称
     * autoCreateVipPick 0:不自动生成唯品会拣货单,1:自动生成唯品会拣货单
     * openPostPrintMatchShopPriority 后置打印盲扫开启店铺优先发货 0-关闭 1-开启
     * postPrintMatchPriorityShops 后置打印盲扫优先发货指定店铺编号
     * moveStock 0:不平移,1:入库,2销退,3补货
     * wavePickIgnorePrintedErrorTrade 0:否1是
     * lazadaFblAutoSplit lazada二次销售或者fbl商品下载时自动拆分  1-自动拆分 0-不自动拆分 默认为1
     * tradeJitxFeedbackPlatformWarehouse  唯品会JITX业务自动反馈寻仓结果 0:关闭  1: 开启
     * grossCaculateType 毛利润计算公式 0:按实付计算 1: 按平台实付计算
     * expressReachable 自动匹配快递模板或手工选择快递模板时判断收件地址是否可达（不可达会自动标记快递异常） 0 否 1 是 默认为 1
     * orderCaigouNeedDecryptInfo 按单采购是否解密推送 0 否 1是 默认 null
     * tradeItemCostConfig 交易订单商品成本价取值 默认:0 0.商品档案成本价 1.历史成本价
     * openUpdateTradeCost 交易订单商品成本价更新 默认开启 1，0.关闭 1.开启
     */
    private String chatConfigs;

    private Map<String, Object> chatConfigMap;

    private Boolean useNewQuery;

    /**
     * 后置打印出单顺序类型
     */
    private Integer postPrintSortType;

    /**
     * 开启自动修改模板
     */
    private String autoUpdateTemplate;

    public String getAutoUpdateTemplate() {
        return autoUpdateTemplate;
    }

    public void setAutoUpdateTemplate(String autoUpdateTemplate) {
        this.autoUpdateTemplate = autoUpdateTemplate;
    }

    public Boolean getUseNewQuery() {
        return useNewQuery;
    }

    public void setUseNewQuery(Boolean useNewQuery) {
        this.useNewQuery = useNewQuery;
    }

    /**
     * 自动分摊
     */
    private Integer autoShare;

    public Integer getAutoShare() {
        return autoShare;
    }

    public void setAutoShare(Integer autoShare) {
        this.autoShare = autoShare;
    }

    /**
     * acPaymentShare
     */
    public Integer acPaymentShare;

    /**
     * 实时查看物流信息开关，0：关闭，1：开启
     */
    private Integer realtimeCheckLogistics;


    private Integer itemExcepOpen;

    public Integer getItemExcepOpen() {
        return itemExcepOpen;
    }

    public void setItemExcepOpen(Integer itemExcepOpen) {
        this.itemExcepOpen = itemExcepOpen;
    }

    /**
     * 开启订单波次播种时，播种人检测控制
     */
    private Integer openTradeWaveSeedSorter;

    /**
     * 开启波次唯一码
     */
    private Integer openWaveUniqueCode;

    private List<AiAuditOrderShop> aiAuditOrderShops;

    /**
     * 有打印次数的订单不进入波次
     */
    private Integer printedNotInWave;

    /**
     * 波次轮播时，分配位置号规则
     * 0：默认值：当前面的位置号释放后，即可被下一个订单占用
     * 1：当前面当位置号释放后，下一个订单依次往后进行播种，当所有位置号播完后，然后再占用出来的位置号
     */
    private Integer loopWaveAllocateNumRule;


    /**
     * 波次拣选时，相同货位的不同商品排序类型   0:订单位置号排序(原先)   1:商家编码排序
     */
    private Integer pickingSameSectionSortType;

    /**
     * -1盲扫配货，0：否（默认）；1：需要配货
     */
    private Integer negativeOneBlindScanAllocate;

    /**
     * 包装验货-波次批打,自动勾选已打订单:01:不勾/勾选
     */
    private Integer autoSelectPrintedTrade;

    /**
     * 拣选中是否可以安排拣选员，0：否（默认）；1：可以
     */
    private Integer pickingArrange;

    /**
     * 播种中是否可以安排播种员，0：否（默认）；1：可以
     */
    private Integer seedingArrange;
    /**
     * 播种拆分订单自动获取运单号 0否1是
     */
    private Integer seedSplitAutoGetWaybill;

    /**
     * “缺货审核”和“缺货发货”支持不自动打上：缺货发货标签
     */
    private Integer noAutoCheckSendInsufficient;

    private Boolean haveOrderExtInfo;

    private String tradeExtendConfig;

    private Map<String, Object> tradeExtendConfigMap;

    //ibatis iterate不支持map，所以需要转成list的形式
    private List<Object> tradeExtendConfigList;

    private Set<String> selfExcepSet;

    private Set<String> sysExcepSet;

    /**
     * @see TradeConfigEnum#ITEM_NUM_EXCLUDE_VIRTUAL
     */
    private Integer itemNumExcludeVirtual;

    private boolean isNewTemplateRule;

    /**
     * @see TradeConfigEnum#ITEM_NUM_EXCLUDE_NON_CONSIGN
     */
    private Integer itemNumExcludeNonConsign;


    public Integer getItemNumExcludeVirtual() {
        return itemNumExcludeVirtual;
    }

    public void setItemNumExcludeVirtual(Integer itemNumExcludeVirtual) {
        this.itemNumExcludeVirtual = itemNumExcludeVirtual;
    }

    public Integer getOpenExcepTradeGenerateWave() {
        return openExcepTradeGenerateWave;
    }

    public void setOpenExcepTradeGenerateWave(Integer openExcepTradeGenerateWave) {
        this.openExcepTradeGenerateWave = openExcepTradeGenerateWave;
    }

    public Integer getRealtimeCheckLogistics() {
        return realtimeCheckLogistics;
    }

    public void setRealtimeCheckLogistics(Integer realtimeCheckLogistics) {
        this.realtimeCheckLogistics = realtimeCheckLogistics;
    }

    public String getChatConfigs() {
        return chatConfigs;
    }

    public void setChatConfigs(String chatConfigs) {
        this.chatConfigs = chatConfigs;
        //清空一下缓存
        this.chatConfigMap = null;
    }

    public Map<String, Object> getChatConfigMap() {
        return chatConfigMap;
    }

    public void setChatConfigMap(Map<String, Object> chatConfigMap) {
        this.chatConfigMap = chatConfigMap;
    }

    public Integer getUnAuditReplaceInsufficientCanceled() {
        return unAuditReplaceInsufficientCanceled;
    }

    public void setUnAuditReplaceInsufficientCanceled(Integer unAuditReplaceInsufficientCanceled) {
        this.unAuditReplaceInsufficientCanceled = unAuditReplaceInsufficientCanceled;
    }

    public Integer getOpenUploadSellerMemo() {
        return openUploadSellerMemo;
    }

    public void setOpenUploadSellerMemo(Integer openUploadSellerMemo) {
        this.openUploadSellerMemo = openUploadSellerMemo;
    }

    public Integer getOpenMemoExp() {
        return openMemoExp;
    }

    public void setOpenMemoExp(Integer openMemoExp) {
        this.openMemoExp = openMemoExp;
    }

    public Integer getAutoCreateWave() {
        return autoCreateWave;
    }

    public void setAutoCreateWave(Integer autoCreateWave) {
        this.autoCreateWave = autoCreateWave;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getOpenDeliverPrint() {
        return openDeliverPrint;
    }

    public void setOpenDeliverPrint(Integer openDeliverPrint) {
        this.openDeliverPrint = openDeliverPrint;
    }

    public Integer getFirstPrintExpress() {
        return firstPrintExpress;
    }

    public void setFirstPrintExpress(Integer firstPrintExpress) {
        this.firstPrintExpress = firstPrintExpress;
    }

    public Integer getOpenPackageWeigh() {
        return openPackageWeigh;
    }

    public void setOpenPackageWeigh(Integer openPackageWeigh) {
        this.openPackageWeigh = openPackageWeigh;
    }

    public Integer getOpenPackageExamine() {
        return openPackageExamine;
    }

    public void setOpenPackageExamine(Integer openPackageExamine) {
        this.openPackageExamine = openPackageExamine;
    }

    public Integer getOpenConsign() {
        return openConsign;
    }

    public void setOpenConsign(Integer openConsign) {
        this.openConsign = openConsign;
    }

    public Integer getOpenIdentCode() {
        return openIdentCode;
    }

    public void setOpenIdentCode(Integer openIdentCode) {
        this.openIdentCode = openIdentCode;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getOpenWave() {
        return openWave;
    }

    public void setOpenWave(Integer openWave) {
        this.openWave = openWave;
    }

    public Integer getOpenPrintDelay() {
        return openPrintDelay;
    }

    public void setOpenPrintDelay(Integer openPrintDelay) {
        this.openPrintDelay = openPrintDelay;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public Integer getOpenUploadConsign() {
        return openUploadConsign;
    }

    public void setOpenUploadConsign(Integer openUploadConsign) {
        this.openUploadConsign = openUploadConsign;
    }

    public Integer getUploadConsignName() {
        return uploadConsignName;
    }

    public void setUploadConsignName(Integer uploadConsignName) {
        this.uploadConsignName = uploadConsignName;
    }


    public Integer getOrderImageSource() {
        return orderImageSource;
    }

    public void setOrderImageSource(Integer orderImageSource) {
        this.orderImageSource = orderImageSource;
    }

    public Integer getOpenSeed() {
        return openSeed;
    }

    public void setOpenSeed(Integer openSeed) {
        this.openSeed = openSeed;
    }

    public Integer getOpenSeedPrint() {
        return openSeedPrint;
    }

    public void setOpenSeedPrint(Integer openSeedPrint) {
        this.openSeedPrint = openSeedPrint;
    }

    /**
     * 为了处理缓存，先对为null的值做下处理
     *
     * @return
     */
    public Integer getCoverSysStatus() {
        return coverSysStatus;
    }

    public void setCoverSysStatus(Integer coverSysStatus) {
        this.coverSysStatus = coverSysStatus;
    }

    public Integer getSeedRowNum() {
        return seedRowNum;
    }

    public void setSeedRowNum(Integer seedRowNum) {
        this.seedRowNum = seedRowNum;
    }

    public Integer getSeedRowPositionNum() {
        return seedRowPositionNum;
    }

    public void setSeedRowPositionNum(Integer seedRowPositionNum) {
        this.seedRowPositionNum = seedRowPositionNum;
    }

    public Boolean getOpenSeedRowPosition() {
        return openSeedRowPosition;
    }

    public void setOpenSeedRowPosition(Boolean openSeedRowPosition) {
        this.openSeedRowPosition = openSeedRowPosition;
    }

    public Integer getOpenLinkPayment() {
        return openLinkPayment;
    }

    public void setOpenLinkPayment(Integer openLinkPayment) {
        this.openLinkPayment = openLinkPayment;
    }

    public Integer getOpenPriceUpdate() {
        return openPriceUpdate;
    }

    public void setOpenPriceUpdate(Integer openPriceUpdate) {
        this.openPriceUpdate = openPriceUpdate;
    }

    public Integer getOpenBlack() {
        return openBlack;
    }

    public void setOpenBlack(Integer openBlack) {
        this.openBlack = openBlack;
    }

    public Integer getSyncWaitPay() {
        return syncWaitPay;
    }

    public void setSyncWaitPay(Integer syncWaitPay) {
        this.syncWaitPay = syncWaitPay;
    }

    public Integer getOpenLogisticsTracking() {
        return openLogisticsTracking;
    }

    public void setOpenLogisticsTracking(Integer openLogisticsTracking) {
        this.openLogisticsTracking = openLogisticsTracking;
    }

    public Integer getOpenFinanceAudit() {
        return openFinanceAudit;
    }

    public void setOpenFinanceAudit(Integer openFinanceAudit) {
        this.openFinanceAudit = openFinanceAudit;
    }

    public Integer getAutoGiftMatch() {
        return autoGiftMatch;
    }

    public void setAutoGiftMatch(Integer autoGiftMatch) {
        this.autoGiftMatch = autoGiftMatch;
    }

    public Integer getWaveSortRule() {
        return waveSortRule;
    }

    public void setWaveSortRule(Integer waveSortRule) {
        this.waveSortRule = waveSortRule;
    }

    public Integer getWaveSortTime() {
        return waveSortTime;
    }

    public void setWaveSortTime(Integer waveSortTime) {
        this.waveSortTime = waveSortTime;
    }

    public String getTimeoutLimit() {
        return timeoutLimit;
    }

    public void setTimeoutLimit(String timeoutLimit) {
        this.timeoutLimit = timeoutLimit;
    }

    public Integer getOpenMultiOutsidUpload() {
        return openMultiOutsidUpload;
    }

    public void setOpenMultiOutsidUpload(Integer openMultiOutsidUpload) {
        this.openMultiOutsidUpload = openMultiOutsidUpload;
    }

    public Boolean getWaveAutoGetWaybillCode() {
        return waveAutoGetWaybillCode;
    }

    public void setWaveAutoGetWaybillCode(Boolean waveAutoGetWaybillCode) {
        this.waveAutoGetWaybillCode = waveAutoGetWaybillCode;
    }

    public Integer getExcepTradeParty3SendGoods() {
        return excepTradeParty3SendGoods;
    }

    public void setExcepTradeParty3SendGoods(Integer excepTradeParty3SendGoods) {
        this.excepTradeParty3SendGoods = excepTradeParty3SendGoods;
    }

    public String getFinanceUserIdsStr() {
        return financeUserIdsStr;
    }

    public void setFinanceUserIdsStr(String financeUserIdsStr) {
        this.financeUserIdsStr = financeUserIdsStr;
    }

    public Integer getOpenReUploadConsign() {
        return openReUploadConsign;
    }

    public void setOpenReUploadConsign(Integer openReUploadConsign) {
        this.openReUploadConsign = openReUploadConsign;
    }

    public Integer getOpenSplitReUploadConsign() {
        return openSplitReUploadConsign;
    }

    public void setOpenSplitReUploadConsign(Integer openSplitReUploadConsign) {
        this.openSplitReUploadConsign = openSplitReUploadConsign;
    }

    @Deprecated
    public Integer getSeedScanType() {
        return seedScanType;
    }

    public void setSeedScanType(Integer seedScanType) {
        this.seedScanType = seedScanType;
    }

    public Integer getShowSkuProperties() {
        return showSkuProperties;
    }

    public void setShowSkuProperties(Integer showSkuProperties) {
        this.showSkuProperties = showSkuProperties;
    }

    public Integer getOpenScanItemPackma() {
        return openScanItemPackma;
    }

    public void setOpenScanItemPackma(Integer openScanItemPackma) {
        this.openScanItemPackma = openScanItemPackma;
    }

    public Integer getShareDiscount() {
        return shareDiscount;
    }

    public void setShareDiscount(Integer shareDiscount) {
        this.shareDiscount = shareDiscount;
    }

    public Integer getPostOpenIdentCode() {
        return postOpenIdentCode;
    }

    public void setPostOpenIdentCode(Integer postOpenIdentCode) {
        this.postOpenIdentCode = postOpenIdentCode;
    }

    public Integer getRematchGift() {
        return rematchGift;
    }

    public void setRematchGift(Integer rematchGift) {
        this.rematchGift = rematchGift;
    }

    public Integer getOpenPackVoiceHints() {
        return openPackVoiceHints;
    }

    public void setOpenPackVoiceHints(Integer openPackVoiceHints) {
        this.openPackVoiceHints = openPackVoiceHints;
    }

    public Integer getOpenMessageVoiceHints() {
        return openMessageVoiceHints;
    }

    public void setOpenMessageVoiceHints(Integer openMessageVoiceHints) {
        this.openMessageVoiceHints = openMessageVoiceHints;
    }

    public Integer getOpenRemarkVoiceHints() {
        return openRemarkVoiceHints;
    }

    public void setOpenRemarkVoiceHints(Integer openRemarkVoiceHints) {
        this.openRemarkVoiceHints = openRemarkVoiceHints;
    }

    public Integer getOpenGiftVoiceHints() {
        return openGiftVoiceHints;
    }

    public void setOpenGiftVoiceHints(Integer openGiftVoiceHints) {
        this.openGiftVoiceHints = openGiftVoiceHints;
    }

    public Integer getOpenInvoiceVoiceHints() {
        return openInvoiceVoiceHints;
    }

    public void setOpenInvoiceVoiceHints(Integer openInvoiceVoiceHints) {
        this.openInvoiceVoiceHints = openInvoiceVoiceHints;
    }

    public Integer getGiftDelayHour() {
        return giftDelayHour;
    }

    public void setGiftDelayHour(Integer giftDelayHour) {
        this.giftDelayHour = giftDelayHour;
    }

    public Integer getMamFilterExcep() {
        return mamFilterExcep;
    }

    public void setMamFilterExcep(Integer mamFilterExcep) {
        this.mamFilterExcep = mamFilterExcep;
    }

    public Integer getMamFilterSplit() {
        return mamFilterSplit;
    }

    public void setMamFilterSplit(Integer mamFilterSplit) {
        this.mamFilterSplit = mamFilterSplit;
    }

    public Integer getMamFilterUndo() {
        return mamFilterUndo;
    }

    public void setMamFilterUndo(Integer mamFilterUndo) {
        this.mamFilterUndo = mamFilterUndo;
    }

    public Integer getUnAuditWaybillCancel() {
        return unAuditWaybillCancel;
    }

    public void setUnAuditWaybillCancel(Integer unAuditWaybillCancel) {
        this.unAuditWaybillCancel = unAuditWaybillCancel;
    }

    public Boolean getOpenWavePackage() {
        return openWavePackage;
    }

    public void setOpenWavePackage(Boolean openWavePackage) {
        this.openWavePackage = openWavePackage;
    }

    public Integer getOpenPackAutoPost() {
        return openPackAutoPost;
    }

    public void setOpenPackAutoPost(Integer openPackAutoPost) {
        this.openPackAutoPost = openPackAutoPost;
    }

    public Integer getOpenPackAutoSingleWaveBatch() {
        return openPackAutoSingleWaveBatch;
    }

    public void setOpenPackAutoSingleWaveBatch(Integer openPackAutoSingleWaveBatch) {
        this.openPackAutoSingleWaveBatch = openPackAutoSingleWaveBatch;
    }

    public Boolean getSingleWaveAutoPackage() {
        return singleWaveAutoPackage;
    }

    public void setSingleWaveAutoPackage(Boolean singleWaveAutoPackage) {
        this.singleWaveAutoPackage = singleWaveAutoPackage;
    }

    @Deprecated
    public Integer getFinishWaveTradeAutoUnaudit() {
        return finishWaveTradeAutoUnaudit;
    }

    public void setFinishWaveTradeAutoUnaudit(Integer finishWaveTradeAutoUnaudit) {
        this.finishWaveTradeAutoUnaudit = finishWaveTradeAutoUnaudit;
    }

    public Integer getOpenForceTradePack() {
        return openForceTradePack;
    }

    public void setOpenForceTradePack(Integer openForceTradePack) {
        this.openForceTradePack = openForceTradePack;
    }

    public Integer getOpenExpressCompanyVoiceHints() {
        return openExpressCompanyVoiceHints;
    }

    public void setOpenExpressCompanyVoiceHints(Integer openExpressCompanyVoiceHints) {
        this.openExpressCompanyVoiceHints = openExpressCompanyVoiceHints;
    }

    public Integer getOpenAutoAudit() {
        return openAutoAudit;
    }

    public void setOpenAutoAudit(Integer openAutoAudit) {
        this.openAutoAudit = openAutoAudit;
    }

    public Integer getOpenExpressMatch() {
        return openExpressMatch;
    }

    public void setOpenExpressMatch(Integer openExpressMatch) {
        this.openExpressMatch = openExpressMatch;
    }

    public Integer getAutoCancelAudit() {
        return autoCancelAudit;
    }

    public void setAutoCancelAudit(Integer autoCancelAudit) {
        this.autoCancelAudit = autoCancelAudit;
    }

    public boolean isCancelAuditIfAddressChanged() {
        return NumberUtils.has(autoCancelAudit, 1);
    }

    public boolean isCancelAuditIfItemChanged() {
        return NumberUtils.has(autoCancelAudit, 2);
    }

    public boolean isCancelAuditIfSellerMemoChanged(Set<String> sellerMemos, Set<Long> sellerFlags) {
        Object obj = this.get(TradeConfigEnum.AUTO_CANCEL_AUDIT_SELLER_MEMO_KEYWORDS.getConfigKey());
        String json = TradeConfigEnum.AUTO_CANCEL_AUDIT_SELLER_MEMO_KEYWORDS.getDefaultValue();
        if (obj != null) {
            json = obj.toString();
        }
        AutoCancelAuditSellerMemoKeywords autoCancelAuditSellerMemoKeywords = JSONObject.parseObject(json, AutoCancelAuditSellerMemoKeywords.class);
        return NumberUtils.has(autoCancelAudit, 4) && autoCancelAuditSellerMemoKeywords.canAutoCancelAudit(sellerMemos, sellerFlags);
    }

    public boolean isCancelAuditIfRefund() {
        return NumberUtils.has(autoCancelAudit, 8);
    }

    public boolean isCancelAuditIfPartRefund() {
        return autoCancelAudit != null && (autoCancelAudit | 16) - autoCancelAudit == 0;
    }

    public Integer getPostPrintDirectConfig() {
        return postPrintDirectConfig;
    }

    public void setPostPrintDirectConfig(Integer postPrintDirectConfig) {
        this.postPrintDirectConfig = postPrintDirectConfig;
    }

    public Integer getSeedPrintDirectConfig() {
        return seedPrintDirectConfig;
    }

    public void setSeedPrintDirectConfig(Integer seedPrintDirectConfig) {
        this.seedPrintDirectConfig = seedPrintDirectConfig;
    }

    public Boolean getIsPrintDirect() {
        return isPrintDirect;
    }

    public void setIsPrintDirect(Boolean isPrintDirect) {
        this.isPrintDirect = isPrintDirect;
    }

    public Integer getOpenPrintSingleWave() {
        return openPrintSingleWave;
    }

    public void setOpenPrintSingleWave(Integer openPrintSingleWave) {
        this.openPrintSingleWave = openPrintSingleWave;
    }

    public Integer getOpenAutoAuditMatchTemplate() {
        return openAutoAuditMatchTemplate;
    }

    public void setOpenAutoAuditMatchTemplate(Integer openAutoAuditMatchTemplate) {
        this.openAutoAuditMatchTemplate = openAutoAuditMatchTemplate;
    }

    @Deprecated
    public Integer getOpenPrintInPicking() {
        return openPrintInPicking;
    }

    public void setOpenPrintInPicking(Integer openPrintInPicking) {
        this.openPrintInPicking = openPrintInPicking;
    }

    @Deprecated
    public Integer getOpenTradeWaveSeedSorter() {
        return openTradeWaveSeedSorter;
    }

    public void setOpenTradeWaveSeedSorter(Integer openTradeWaveSeedSorter) {
        this.openTradeWaveSeedSorter = openTradeWaveSeedSorter;
    }

    @Deprecated
    public Integer getOpenWaveUniqueCode() {
        return openWaveUniqueCode;
    }

    public void setOpenWaveUniqueCode(Integer openWaveUniqueCode) {
        this.openWaveUniqueCode = openWaveUniqueCode;
    }

    @Deprecated
    public Integer getPrintedNotInWave() {
        return printedNotInWave;
    }

    public void setPrintedNotInWave(Integer printedNotInWave) {
        this.printedNotInWave = printedNotInWave;
    }

    @Deprecated
    public Integer getLoopWaveAllocateNumRule() {
        return loopWaveAllocateNumRule;
    }

    public void setLoopWaveAllocateNumRule(Integer loopWaveAllocateNumRule) {
        this.loopWaveAllocateNumRule = loopWaveAllocateNumRule;
    }

    public Integer getItemNumExcludeNonConsign() {
        return itemNumExcludeNonConsign;
    }

    public void setItemNumExcludeNonConsign(Integer itemNumExcludeNonConsign) {
        this.itemNumExcludeNonConsign = itemNumExcludeNonConsign;
    }


    public boolean isMatchTagAfterGift() {
        Object v = get("matchTagAfterGift");
        //默认走原先逻辑，先匹配赠品在匹配标签
        return v == null || (Integer.parseInt(v.toString())) == 1;
    }

    public boolean openAiAuditOrder(Staff staff) {
        if (staff.openAuditActiveStockRecord()) {
            //店铺优先级
            if (aiAuditOrderShops != null && aiAuditOrderShops.size() > 0) {
                return true;
            }
            String aiAuditOrderValue = (String) get(TradeExtendConfigsEnum.AI_AUDIT_ORDER.getKey());
            if (StringUtils.isEmpty(aiAuditOrderValue)) {
                aiAuditOrderValue = TradeExtendConfigsEnum.AI_AUDIT_ORDER_PAY_TIME.getKey();
            }
            //订单付款时间降序 || 多件订单优先 || 加急订单优先
            // || 现货订单按付款时间先排序，预售订单再按计划发货时间排序 || 现货订单按付款时间、预售订单按计划发货时间混合排序
            if (TradeExtendConfigsEnum.AI_AUDIT_ORDER_PAY_TIME.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeExtendConfigsEnum.AI_AUDIT_ORDER_MUTIL_ITEM_NUM.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeExtendConfigsEnum.AI_AUDIT_ORDER_URGENT.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeExtendConfigsEnum.AI_AUDIT_ORDER_BEFORE_PAY_TIME_AFTER_TIMEOUT_ACTION_TIME.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeExtendConfigsEnum.AI_AUDIT_ORDER_PAY_TIME_TIMEOUT_ACTION_TIME.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeExtendConfigsEnum.AI_AUDIT_ORDER_TIME_OUT_ACTION_TIME.getKey().equalsIgnoreCase(aiAuditOrderValue)
                    || TradeConfigEnum.AUDIT_MULTIPLE_CONDITION.getConfigKey().equalsIgnoreCase(aiAuditOrderValue)) {
                return true;
            }
            //包含某些标签订单优先
            if (TradeExtendConfigsEnum.AI_AUDIT_ORDER_INCLUED_TAG_IDS.getKey().equalsIgnoreCase(aiAuditOrderValue)) {
                try {
                    String[] incluedTagIdArr = ArrayUtils.toStringArray((String) get(TradeExtendConfigsEnum.INCLUED_TAG_IDS.getKey()));
                    if (incluedTagIdArr != null && incluedTagIdArr.length > 0) {
                        return true;
                    }
                } catch (Exception e) {

                }
            }
        }
        return false;
    }

    @Deprecated
    public Integer getWaveSeedType() {
        return waveSeedType;
    }

    public void setWaveSeedType(Integer waveSeedType) {
        this.waveSeedType = waveSeedType;
    }


    public boolean openNewPaymentShare() {
        Integer result = (Integer) get(TradeExtendConfigsEnum.NEW_PAYMENT_SHARE.getKey());
        return result != null && 1 == result.intValue();
    }

    public Integer getTbXsdUploadSet(){
        Integer result = (Integer) get(TradeConfigEnum.TB_XSD_UPLOAD_SET.getConfigKey());
        return result;
    }

    @Deprecated
    public Integer getOpenRuleFirstThenAllocate() {
        return openRuleFirstThenAllocate;
    }

    public void setOpenRuleFirstThenAllocate(Integer openRuleFirstThenAllocate) {
        this.openRuleFirstThenAllocate = openRuleFirstThenAllocate;
    }

    @Deprecated
    public String getSubSectionPickTypes() {
        return subSectionPickTypes;
    }

    public void setSubSectionPickTypes(String subSectionPickTypes) {
        this.subSectionPickTypes = subSectionPickTypes;
    }

    public String getAiAuditOrderValue(Staff staff) {
        String aiAuditOrderValue = "";
        if (staff.openAuditActiveStockRecord()) {
            aiAuditOrderValue = (String) get(TradeExtendConfigsEnum.AI_AUDIT_ORDER.getKey());
            if (StringUtils.isEmpty(aiAuditOrderValue)) {
                aiAuditOrderValue = TradeExtendConfigsEnum.AI_AUDIT_ORDER_PAY_TIME.getKey();
            }
        }
        return aiAuditOrderValue;
    }

    public boolean getPreLock(Staff staff) {
        // KMERP-262027: 默认不预留库存
        Object preLock = get(TradeExtendConfigsEnum.AI_AUDIT_ORDER_PRE_LOCK.getKey());
        return staff.openAuditActiveStockRecord() && Objects.nonNull(preLock) && !"1".equals(String.valueOf(preLock));
    }

    @Deprecated
    public Integer getOpenWaveRemark() {
        return openWaveRemark;
    }

    public void setOpenWaveRemark(Integer openWaveRemark) {
        this.openWaveRemark = openWaveRemark;
    }

    public Object get(String key) {
        if (this.chatConfigMap == null) {
            this.chatConfigMap = TradeConfigUtils.parseExtendConfig(this);
        }
        Object o = this.chatConfigMap.get(key);
        if (o != null && !"tradeJitxFeedbackPlatformWarehouse".equals(key) && !"grossCaculateType".equals(key)) {
            return o;
        }

        if (this.tradeExtendConfigMap == null) {
            this.tradeExtendConfigMap = TradeConfigUtils.parseExtendConfig(this.getTradeExtendConfig());
        }
        o = this.tradeExtendConfigMap.get(key);
        if (o != null) {
            return o;
        }

        TradeExtendConfigsEnum tradeExtendConfigsEnum = Arrays.stream(TradeExtendConfigsEnum.values())
                .filter(t -> t.getDefaultValue() != null && !"tradeJitxFeedbackPlatformWarehouse".equals(t.getKey()))
                .collect(Collectors.toMap(TradeExtendConfigsEnum::getKey, Function.identity()))
                .get(key);

        if (tradeExtendConfigsEnum != null) {
            return tradeExtendConfigsEnum.getDefaultValue();
        }

        NewTradeExtendConfigEnum newTradeExtendConfigsEnum = Arrays.stream(NewTradeExtendConfigEnum.values())
                .filter(t -> t.getDefaultValue() != null)
                .collect(Collectors.toMap(NewTradeExtendConfigEnum::getKey, Function.identity()))
                .get(key);

        if (newTradeExtendConfigsEnum != null) {
            return newTradeExtendConfigsEnum.getDefaultValue();
        }

        return null;
    }

    public int getInteger(String key) {
        Object o = get(key);
        return o == null ? 0 : Integer.parseInt(o.toString());
    }

    public int getIntegerDefaultOpen(String key) {
        Object o = get(key);
        return (o == null ? 1 : Integer.parseInt(o.toString()));
    }

    public int getInteger(NewTradeExtendConfigEnum key) {
        Object o = get(key.getKey());
        if (o == null) {
            o = key.getDefaultValue();
        }
        return Integer.parseInt(o.toString());
    }

    /**
     * 是否需要通过可达标记快递异常
     */
    public boolean needReachable() {
        Object o = get("expressReachable");
        return o == null || !"0".equals(o.toString());
    }

    /**
     * 按单采购是否需要解密
     * @return
     */
    public boolean orderCaigouNeedDecrypt(){
        return getInteger("orderCaigouNeedDecryptInfo") == 1;
    }

    public boolean isOpenExtendConfig(TradeExtendConfigsEnum tradeExtendConfigsEnum) {
        return getInteger(tradeExtendConfigsEnum.getKey()) == 1;
    }

    public boolean isOpenHistoryPriceConfig(TradeExtendConfigsEnum tradeExtendConfigsEnum) {
        return getInteger(tradeExtendConfigsEnum.getKey()) == 2;
    }

    public boolean isOpenExtendConfigDefaultOpen(TradeExtendConfigsEnum tradeExtendConfigsEnum) {
        return getIntegerDefaultOpen(tradeExtendConfigsEnum.getKey()) == 1 ? true : false;
    }

    public String getString(String key) {
        Object o = get(key);
        return o == null ? "" : String.valueOf(o);
    }

    @Deprecated
    public Integer getPostPrintSortType() {
        return postPrintSortType;
    }

    public void setPostPrintSortType(Integer postPrintSortType) {
        this.postPrintSortType = postPrintSortType;
    }

    /**
     * 开启订单导入时根据收件人信息和平台交易号判断是trade还是order 而不是依靠填没填收件人来判断
     *
     * @return
     */
    public boolean openNewTradeImport() {
        return getInteger(TradeExtendConfigsEnum.OPEN_NEW_TRADE_IMPORT.getKey()) == 1;
    }

    public enum FinishWaveTradeAutoUnauditTypeEnum {
        // 关闭
        CLOSE(0),

        // 所有未完成订单
        ALL(1),

        // 只针对正常未成单的订单
        ONLY_NORMAL(2),

        // 只针对有异常的订单
        ONLY_EXCEPT(3);

        FinishWaveTradeAutoUnauditTypeEnum(Integer value) {
            this.value = value;
        }

        private Integer value;

        public Integer getValue() {
            return value;
        }
    }


    public static boolean needAutoShare(TradeConfig tradeConfig) {
        //开启了新的实付金额分摊的，不再开启优惠金额分摊
        if (tradeConfig.getShareDiscount() != null && tradeConfig.getShareDiscount().intValue() == 1 && !openNewPaymentShare(tradeConfig)) {
            return true;
        }
        return false;
    }

    public static boolean openNewPaymentShare(TradeConfig tradeConfig) {
        Integer result = (Integer) tradeConfig.get(TradeExtendConfigsEnum.NEW_PAYMENT_SHARE.getKey());
        return result != null && 1 == result.intValue();
    }

    public int getOpenScalpNotApplyStock() {
        return getInteger(TradeExtendConfigsEnum.OPEN_SCALP_NOT_APPLY_STOCK.getKey());
    }

    public int getOpenRefundNotApplyStock() {
        return getInteger(TradeExtendConfigsEnum.OPEN_REFUND_NOT_APPLY_STOCK.getKey());
    }


    public Integer getShowPlatTitle() {
        return getInteger(TradeExtendConfigsEnum.SHOW_PLAT_TITLE.getKey());
    }


    public Integer getOpenChatConfig() {
        return getInteger(TradeExtendConfigsEnum.OPEN_CHAT_CONFIG.getKey());
    }

    public String getOpenCheckRepeatCod() {
        return getString(TradeExtendConfigsEnum.OPEN_CHECK_REPEAT_COD.getKey());
    }


    public List<AiAuditOrderShop> getAiAuditOrderShops() {
        return aiAuditOrderShops;
    }

    public void setAiAuditOrderShops(List<AiAuditOrderShop> aiAuditOrderShops) {
        this.aiAuditOrderShops = aiAuditOrderShops;
    }

    public List<AiAuditOrderShop> fillAiAuditOrderShops(Staff staff) {
        if (staff.openAuditActiveStockRecord()) {
            Object obj = get(TradeExtendConfigsEnum.AI_AUDIT_ORDER_SHOP.getKey());
            if (obj != null) {
                this.aiAuditOrderShops = JSON.parseArray(JSONObject.toJSONString(obj), AiAuditOrderShop.class);
            }
        }
        if (this.aiAuditOrderShops == null) {
            this.aiAuditOrderShops = new ArrayList<>();
        }
        return aiAuditOrderShops;
    }

    public int getOpenPartRefund(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.OPEN_PART_REFUND.getKey());
    }

    /**
     * 不覆盖模式，允许删除。覆盖模式，根据配置
     */
    public int getAllowRemoveItem() {
        return getInteger(TradeExtendConfigsEnum.ALLOW_REMOVE_ITEM.getKey());
    }

    /**
     * allowRemoveAllPlatItem: 允许删除所有平台商品, 0：不允许，1允许 默认：0
     */
    public int getAllowRemoveAllPlatItem() {
        return 0;
    }

    /**
     * 开启 订单导入时自动读取销售价
     * 客户销售价：https://gykj.yuque.com/entavv/xb9xi5/tsn1h0
     * 分销销售价：https://tb.raycloud.com/task/60409b9774c0072a0c892940
     */
    public boolean openImportAutoSetPayment() {
        return getInteger(TradeExtendConfigsEnum.OPEN_IMPORT_AUTO_SET_PAYMENT.getKey()) == 1;
    }

    /**
     * 开启 平台改备注自动重新匹配赠品
     * https://gykj.yuque.com/entavv/xb9xi5/abg21s
     */
    public boolean matchGiftAfterSellerMemoChanged() {
        return getInteger(TradeExtendConfigsEnum.MATCH_GIFT_AFTER_SELLER_MEMO_CHANGED.getKey()) == 1;
    }

    /**
     * 开启 三方仓订单允许获取快递单号
     * https://gykj.yuque.com/entavv/xb9xi5/dt2oep
     */
    public boolean allowParty3GetWaybill() {
        return getInteger(TradeExtendConfigsEnum.ALLOW_PARTY3_GET_WAYBILL.getKey()) == 1;
    }

    /**
     * 放心购订单是否允许脱敏匹配标签，因为放心购脱敏接口非常慢故有此配置，只可以tj开关
     */
    public boolean allowFxgMaskDataMatchTag() {
        return getInteger(TradeExtendConfigsEnum.ALLOW_FXG_MASK_DATA_MATCH_TAG.getKey()) == 1;
    }

    /**
     * 开启 其他erp发货订单支持重新发货配置（默认开启） https://gykj.yuque.com/entavv/xb9xi5/ebn48o
     */
    public boolean openReUploadOtherConsigned() {
        return getInteger(TradeExtendConfigsEnum.OPEN_RE_UPLOAD_OTHER_CONSIGNED.getKey()) == 1;
    }

    /**
     * 开启 订单列表根据商品信息查询订单时，排除订单中交易关闭的订单商品
     * @return
     */
    public boolean openQueryByOuterIdExcludeClosedTrade() {
        return getInteger(TradeExtendConfigsEnum.QUERY_BY_OUTER_ID_EXCLUDE_CLOSED_TRADE.getKey()) == 1;
    }

    @Deprecated
    public Integer getOnlyTradeMultiSeed() {
        return onlyTradeMultiSeed;
    }

    public void setOnlyTradeMultiSeed(Integer onlyTradeMultiSeed) {
        this.onlyTradeMultiSeed = onlyTradeMultiSeed;
    }

    public Integer getAutoCreateVipPick() {
        return autoCreateVipPick;
    }

    public void setAutoCreateVipPick(Integer autoCreateVipPick) {
        this.autoCreateVipPick = autoCreateVipPick;
    }

    @Deprecated
    public Integer getNegativeOneBlindScanAllocate() {
        return negativeOneBlindScanAllocate;
    }

    public void setNegativeOneBlindScanAllocate(Integer negativeOneBlindScanAllocate) {
        this.negativeOneBlindScanAllocate = negativeOneBlindScanAllocate;
    }

    public int getAutoSuitTransformSingle(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.AUTO_SUIT_TRANSFORM_SINGLE.getKey());
    }

    public boolean auditSuitTransformSingle() {
        return getInteger(TradeExtendConfigsEnum.AUDIT_SUIT_TRANSFORM_SINGLE.getKey()) == 1;
    }

    public int getMergeAfterMatchGift(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.MERGE_AFTER_MATCH_GIFT.getKey());
    }

    public int getMergeAfterMatchWarehouse(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.MERGE_AFTER_MATCH_WAREHOUSE.getKey());
    }

    public int getMergeAfterMatchExpress(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.MERGE_AFTER_MATCH_EXPRESS.getKey());
    }

    public int getUndoMergeAfterMatchGift(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.UNDO_MERGE_AFTER_MATCH_GIFT.getKey());
    }

    public int getUndoMergeAfterMatchExpress(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.UNDO_MERGE_AFTER_MATCH_EXPRESS.getKey());
    }

    public int getShowMainSysOuterId(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.SHOW_MAINSYSOUTER_ID.getKey());
    }

    public int splitInsufficientAllowGift() {
        return getInteger(TradeExtendConfigsEnum.SPLIT_INSUFFICIENT_ALLOW_GIFT.getKey());
    }

    @Deprecated
    public Integer getOpenPickingCancel() {
        return openPickingCancel;
    }

    public void setOpenPickingCancel(Integer openPickingCancel) {
        this.openPickingCancel = openPickingCancel;
    }

    @Deprecated
    public Integer getPickingSameSectionSortType() {
        return pickingSameSectionSortType;
    }

    public void setPickingSameSectionSortType(Integer pickingSameSectionSortType) {
        this.pickingSameSectionSortType = pickingSameSectionSortType;
    }

    public int getDistributionFlowControl(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.DISTRIBUTION_FLOW_CONTROL.getKey());
    }

    public int getPackForceType(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.PACK_FORCE_TYPE.getKey());
    }

    public int getOpenPackUploadSellerMemo(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.OPEN_PACK_UPLOAD_SELLER_MEMO.getKey());
    }

    public Set<String> getPackUploadInfo(Staff staff) {
        return ArrayUtils.toStringSet(getString(TradeExtendConfigsEnum.PACK_UPLOAD_INFO.getKey()));
    }

    public Long getPackSellerFlag(Staff staff) {
        String sellerFlag = getString(TradeExtendConfigsEnum.PACK_SELLER_FLAG.getKey());
        if (StringUtils.isEmpty(sellerFlag)) {
            return null;
        }
        return NumberUtils.str2Long(sellerFlag, -1L);
    }


    public Integer getAutoCreateWaveTimeInterval() {
        return autoCreateWaveTimeInterval;
    }

    public void setAutoCreateWaveTimeInterval(Integer autoCreateWaveTimeInterval) {
        this.autoCreateWaveTimeInterval = autoCreateWaveTimeInterval;
    }

    public Date getLastAutoCreateWaveTime() {
        return lastAutoCreateWaveTime;
    }

    public void setLastAutoCreateWaveTime(Date lastAutoCreateWaveTime) {
        this.lastAutoCreateWaveTime = lastAutoCreateWaveTime;
    }

    public int insufficientAnalyzeAudit() {
        return getInteger(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_AUDIT.getKey());
    }

    public int splitAutoAudit() {
        return getInteger(TradeExtendConfigsEnum.SPLIT_AUTO_AUDIT.getKey());
    }

    public Integer getAutoSelectPrintedTrade() {
        return autoSelectPrintedTrade;
    }

    public void setAutoSelectPrintedTrade(Integer autoSelectPrintedTrade) {
        this.autoSelectPrintedTrade = autoSelectPrintedTrade;
    }

    @Deprecated
    public Integer getPickingArrange() {
        return pickingArrange;
    }

    public void setPickingArrange(Integer pickingArrange) {
        this.pickingArrange = pickingArrange;
    }

    @Deprecated
    public Integer getSeedSplitAssignPositionNo() {
        return seedSplitAssignPositionNo;
    }

    public void setSeedSplitAssignPositionNo(Integer seedSplitAssignPositionNo) {
        this.seedSplitAssignPositionNo = seedSplitAssignPositionNo;
    }

    @Deprecated
    public Integer getSeedingArrange() {
        return seedingArrange;
    }

    public void setSeedingArrange(Integer seedingArrange) {
        this.seedingArrange = seedingArrange;
    }

    @Deprecated
    public Integer getOpenDirectPickIntoWss() {
        return openDirectPickIntoWss;
    }

    public void setOpenDirectPickIntoWss(Integer openDirectPickIntoWss) {
        this.openDirectPickIntoWss = openDirectPickIntoWss;
    }

    public Integer getOpenWaveCodePrintNew() {
        return openWaveCodePrintNew;
    }

    public void setOpenWaveCodePrintNew(Integer openWaveCodePrintNew) {
        this.openWaveCodePrintNew = openWaveCodePrintNew;
    }

    public Integer openAiAuditException() {
        return getInteger(TradeExtendConfigsEnum.AI_AUDIT_EXCEPTION_OPEN.getKey());
    }

    public String getAiAuditExceptionMsg() {
        return getString(TradeExtendConfigsEnum.AI_AUDIT_EXCEPTION_MSG.getKey());
    }

    public Boolean getMemoSortOpen() {
        return getInteger(TradeExtendConfigsEnum.MEMO_SORT_OPEN.getKey()) == 1 || Arrays.asList(StringUtils.defaultIfEmpty(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getSortCompanyIds(), "").split(",")).contains(this.getCompanyId().toString());
    }

    @Deprecated
    public Integer getPostPrintMatchTradeSort() {
        return postPrintMatchTradeSort;
    }

    public void setPostPrintMatchTradeSort(Integer postPrintMatchTradeSort) {
        this.postPrintMatchTradeSort = postPrintMatchTradeSort;
    }

    /**
     * 获取 后置打印盲扫开启店铺优先发货指定店铺
     *
     * @return
     */
    public List<Long> getPostPrintMatchPriorityShopList() {
        if (getInteger(TradeExtendConfigsEnum.OPEN_POST_PRINT_MATCH_SHOP_PRIORITY.getKey()) != 1) {
            return null;
        }
        return ArrayUtils.toLongList(getString(TradeExtendConfigsEnum.POST_PRINT_MATCH_PRIORITY_SHOPS.getKey()));
    }

    public Integer getNoAutoCheckSendInsufficient() {
        return noAutoCheckSendInsufficient;
    }

    @Deprecated
    public Integer getSeedNotAllowRefund() {
        return seedNotAllowRefund;
    }

    public void setSeedNotAllowRefund(Integer seedNotAllowRefund) {
        this.seedNotAllowRefund = seedNotAllowRefund;
    }

    public Integer getSeedSplitAutoGetWaybill() {
        return seedSplitAutoGetWaybill;
    }

    public void setSeedSplitAutoGetWaybill(Integer seedSplitAutoGetWaybill) {
        this.seedSplitAutoGetWaybill = seedSplitAutoGetWaybill;
    }


    public void setNoAutoCheckSendInsufficient(Integer noAutoCheckSendInsufficient) {
        this.noAutoCheckSendInsufficient = noAutoCheckSendInsufficient;
    }

    public boolean isItemNumExcludeAfterSendGoods() {
        return getInteger(TradeExtendConfigsEnum.ITEM_NUM_EXCLUDE_AFTER_SEND_GOODS.getKey()) == 1;
    }

    public boolean isRefundAutoSplit() {
        return getInteger(TradeExtendConfigsEnum.REFUND_AUTO_SPLIT.getKey()) == 1;
    }

    public boolean sysSourceSplitConsignUploadAll() {
        return getInteger(TradeExtendConfigsEnum.SYS_SOURCE_SPLIT_CONSIGN_UPLOAD_ALL.getKey()) == 1;
    }

    public Integer getGiftOrderSalePriceConfig() {
        return getInteger(TradeExtendConfigsEnum.GIFT_ORDER_SALE_PRICE_CONFIG.getKey());
    }

    public Boolean isHaveOrderExtInfo() {
        return getInteger(TradeExtendConfigsEnum.HAVE_ORDER_EXT_INFO.getKey()) == 1;
    }

    public boolean splitConsignUploadSellerMemo() {
        return getInteger(TradeExtendConfigsEnum.SPLIT_CONSIGN_UPLOAD_SELLER_MEMO.getKey()) == 1;
    }

    public boolean isItemInactiveAutoSplit() {
        return getInteger(TradeExtendConfigsEnum.ITEM_INACTIVE_AUTO_SPLIT.getKey()) == 1;
    }

    /**
     * “splitConsignUploadSellerMemoContent”:"sid=1|shortId=1|consignTime=yyyy-mmm-dd hh:mm:ss|express=1|outSid=1|outerId=1|consignNum=1|noConsignNum=1|flag=3"
     */
    public String splitConsignUploadSellerMemoContent() {
        return getString(TradeExtendConfigsEnum.SPLIT_CONSIGN_UPLOAD_SELLER_MEMO_CONTENT.getKey());
    }

    public String splitConsignUploadSellerMemoContentCustom() {
        return getString(TradeExtendConfigsEnum.SPLIT_CONSIGN_UPLOAD_SELLER_MEMO_CONTENT_CUSTOM.getKey());
    }

    public SplitConsignUploadData getSplitConsignUploadData() {
        if (splitConsignUploadData == null && splitConsignUploadSellerMemo()) {
            splitConsignUploadData = SplitConsignUploadData.buildSplitConsignUploadData(splitConsignUploadSellerMemoContent(), splitConsignUploadSellerMemoContentCustom());
        }
        return splitConsignUploadData;
    }

    public boolean isJdWarehouseTradeSubStock() {
        return getInteger(TradeExtendConfigsEnum.JD_WAREHOUSE_TRADE_SUB_STOCK.getKey()) == 1;
    }

    public boolean showSupplierName(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.SHOW_SUPPLIER_NAME.getKey()) == 1;
    }

    public boolean recalculationTag() {
        return getInteger(TradeExtendConfigsEnum.CHANGE_MEMO_RECALCULATION_TAG.getKey()) == 1;
    }

    public boolean showGoodsPurchaseLink(Staff staff) {
        return getInteger(TradeExtendConfigsEnum.SHOW_GOODS_PURCHASE_LINK.getKey()) == 1;
    }

    public boolean tradeConsignVerifyCustomerBalance() {
        return getInteger(TradeExtendConfigsEnum.TRADE_CONSIGN_VERIFY_CUSTOMER_BALANCE.getKey()) == 1;
    }

    public Integer getMoveStock() {
        return getInteger(TradeExtendConfigsEnum.MOVE_STOCK.getKey());
    }

    public Integer getWavePickIgnorePrintedErrorTrade() {
        return getInteger(TradeExtendConfigsEnum.WAVE_PICK_IGNORE_PRINTED_ERROR_TRADE.getKey());
    }

    public String getWaveAutoCreateTime() {
        String waveAutoCreateTime = getString(TradeExtendConfigsEnum.WAVE_AUTO_CREATE_TIME.getKey());
        return StringUtils.isEmpty(waveAutoCreateTime) ? "00:00" : waveAutoCreateTime;
    }

    public Integer getOpenSingleTradeSound() {
        return getInteger(TradeExtendConfigsEnum.OPEN_SINGLE_TRADE_SOUND.getKey());
    }

    public Integer getOpenWaveDingSound() {
        return getInteger(TradeExtendConfigsEnum.OPEN_WAVE_DING_SOUND.getKey());
    }

    /**
     * 是否开启异常重新匹配
     *
     * @return
     */
    public boolean isOpenTradeExceptionRemark() {
        String result = getString(TradeExtendConfigsEnum.TRADE_EXCEPTION_REMARK.getKey());
        if (StringUtils.isBlank(result)) {
            return false;
        }
        TradeExceptionRemarkDTO tradeExceptionRemarkDTO = null;
        try {
            tradeExceptionRemarkDTO = JSONObject.parseObject(result, TradeExceptionRemarkDTO.class);
        } catch (Exception e) {
            return false;
        }
        return Objects.nonNull(tradeExceptionRemarkDTO) && tradeExceptionRemarkDTO.isEnableStatus();
    }

    /**
     * 获取异常重新匹配对象
     *
     * @return
     */
    public TradeExceptionRemarkDTO getTradeExceptionRemark() {
        String result = getString(TradeExtendConfigsEnum.TRADE_EXCEPTION_REMARK.getKey());
        if (StringUtils.isBlank(result)) {
            return new TradeExceptionRemarkDTO();
        }
        try {
            return JSONObject.parseObject(result, TradeExceptionRemarkDTO.class);
        } catch (Exception e) {
            return new TradeExceptionRemarkDTO();
        }
    }

    public static class TradeExceptionRemarkDTO {

        public static final String CHANGE_RECIVER_ADDRESS_MESSAGE = "RECIVER_ADDRESS";

        /**
         * 是否启用
         */
        private boolean enableStatus;

        /**
         * 消息修改类型：
         *
         * @see CHANGE_RECIVER_ADDRESS_MESSAGE
         */
        private String changeMessage;

        /**
         * 店铺id
         */
        private List<Long> userIdList = new ArrayList<>();

        /**
         * 全选店铺
         * 1
         */
        private Integer checkAllShop = 0;

        /**
         * 0:覆盖，1:新增
         */
        private Integer remarkType;

        public boolean isEnableStatus() {
            return enableStatus;
        }

        public void setEnableStatus(boolean enableStatus) {
            this.enableStatus = enableStatus;
        }

        public String getChangeMessage() {
            return changeMessage;
        }

        public void setChangeMessage(String changeMessage) {
            this.changeMessage = changeMessage;
        }

        public List<Long> getUserIdList() {
            return userIdList;
        }

        public void setUserIdList(List<Long> userIdList) {
            this.userIdList = userIdList;
        }

        public Integer getRemarkType() {
            return remarkType;
        }

        public void setRemarkType(Integer remarkType) {
            this.remarkType = remarkType;
        }

        public Integer getCheckAllShop() {
            return checkAllShop;
        }

        public void setCheckAllShop(Integer checkAllShop) {
            this.checkAllShop = checkAllShop;
        }

        /**
         * 是否全选店铺
         */
        public boolean isCheckAllShop() {
            return Objects.equals(1, checkAllShop);
        }

    }

    public String getTradeExtendConfig() {
        return tradeExtendConfig;
    }

    public void setTradeExtendConfig(String tradeExtendConfig) {
        this.tradeExtendConfig = tradeExtendConfig;
    }

    public Map<String, Object> getTradeExtendConfigMap() {
        return tradeExtendConfigMap;
    }

    public void setTradeExtendConfigMap(Map<String, Object> tradeExtendConfigMap) {
        this.tradeExtendConfigMap = tradeExtendConfigMap;
    }

    public List<Object> getTradeExtendConfigList() {
        return tradeExtendConfigList;
    }

    public void setTradeExtendConfigList(List<Object> tradeExtendConfigList) {
        this.tradeExtendConfigList = tradeExtendConfigList;
    }


    /**
     * 批发收银V2，直接开单后订单审核通过并自动生成波次
     *
     * @return
     */
    public boolean isBillingIntoWaveV2() {
        return getInteger(TradeExtendConfigsEnum.ST_BILLING_INTO_WAVE_V2.getKey()) == 1;
    }


    public boolean isNewExport() {
        return getInteger(TradeExtendConfigsEnum.NEW_EXPORT.getKey()) != 2;
    }

    /**
     * 是否手动生成档口波次时同步对应订单备注
     *
     * @return
     */
    public boolean isManualCreateStallWaveSyncTradeSysMemo() {
        return getInteger(TradeExtendConfigsEnum.MANUAL_CREATE_STALL_WAVE_SYNC_TRADE_SYS_MEMO.getKey()) == 1;
    }

    /**
     * 是否开启通过关键字解析商品名称并匹配商品
     *
     * @return
     */
    public boolean isOpenItemKeywordParse() {
        String result = getString(TradeExtendConfigsEnum.ITEM_KEYWORD_PARSE.getKey());
        if (StringUtils.isBlank(result)) {
            return false;
        }
        ItemKeywordParseDTO itemKeywordParseDTO = null;
        try {
            itemKeywordParseDTO = JSONObject.parseObject(result, ItemKeywordParseDTO.class);
        } catch (Exception e) {
            return false;
        }
        return Objects.nonNull(itemKeywordParseDTO) && itemKeywordParseDTO.isEnableStatus();
    }

    /**
     * 获取商品名称解析对象
     *
     * @return
     */
    public ItemKeywordParseDTO getItemKeywordParse() {
        String result = getString(TradeExtendConfigsEnum.ITEM_KEYWORD_PARSE.getKey());
        if (StringUtils.isBlank(result)) {
            return null;
        }
        try {
            return JSONObject.parseObject(result, ItemKeywordParseDTO.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取不取消订单的指定异常
     *
     * @return
     */
    public Party3WareshoueOrderNotCancelOfExceDto getParty3WareshoueOrderNotCancelOfExceDto() {
        String result = getString(TradeExtendConfigsEnum.PARTY3_WAREHOUSE_ORDER_SPECIL_EXCE_NOT_CANCEL.getKey());
        if (StringUtils.isBlank(result)) {
            return null;
        }
        try {
            return JSONObject.parseObject(result, Party3WareshoueOrderNotCancelOfExceDto.class);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 商品关键字解析
     * https://gykj.yuque.com/entavv/xb9xi5/sab8k5
     */
    @Data
    public static class ItemKeywordParseDTO {
        /**
         * 启用状态
         */
        private boolean enableStatus;
        /**
         * 解析类型 0:从title解析 1:从skuPropertiesName解析
         */
        private Integer type = 0;
        /**
         * 起始关键字
         */
        private String keyword;
        /**
         * 结束关键字
         */
        private String endWord;
        /**
         * 组合比例起始关键字
         */
        private String numStartWord;
        /**
         * 组合比例结束关键字
         */
        private String numEndWord;
        /**
         * 店铺id
         */
        private List<Long> userIdList = new ArrayList<>();
        /**
         * 全选店铺
         * 1
         */
        private Integer checkAllShop = 0;
        /**
         * 复数解析
         */
        private Integer complex = 0;

        /**
         * 是否全选店铺
         */
        public boolean isCheckAllShop() {
            return Objects.equals(1, checkAllShop);
        }
    }

    public boolean openInsufficientAnalyzeExcludeExcep() {
        return getInteger(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP.getKey()) == 1;
    }

    public Set<String> getSysExcepSet() {
        if (sysExcepSet == null) {
            sysExcepSet = ArrayUtils.toStringSet(getString(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SYS.getKey()));
        }
        return sysExcepSet;
    }

    public Set<String> getSelfExcepSet() {
        if (selfExcepSet == null) {
            selfExcepSet = ArrayUtils.toStringSet(getString(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SELF.getKey()));
        }
        return selfExcepSet;
    }

    public boolean isOpenItemReplaceInsufficient() {
        return getInteger(TradeExtendConfigsEnum.ITEM_REPLACE_INSUFFICIENT.getKey()) == 1;
    }

    /**
     * 唯品会JITX业务自动反馈寻仓结果 0:关闭  1: 开启
     */
    public boolean allowJitxFeedbackPlatformWarehouse() {
        return getInteger(NewTradeExtendConfigEnum.TRADE_JITX_FEEDBACK_PLATFORM_WAREHOUSE.getKey()) == 1;
    }


    /**
     * 分配仓库时的拆分订单，进行异步重算标签控制开关 0:关闭  1: 开启
     */
    public boolean asyncReMatchTagFromWarehouseMatch() {
        return getInteger(NewTradeExtendConfigEnum.TRADE_ASYNC_RE_MATCH_TAG_FROM_WAREHOUSE_MATCH.getKey()) == 1;
    }


    public boolean isOpenItemReplaceMatchAlone() {
        Object o = get(TradeExtendConfigsEnum.ITEM_REPLACE_MATCH_ALONE.getKey());
        return o != null && Integer.parseInt(o.toString())== 1;
    }

    public Integer getAutoUnattainableNotApplyStock() {
        return autoUnattainableNotApplyStock;
    }

    public void setAutoUnattainableNotApplyStock(Integer autoUnattainableNotApplyStock) {
        this.autoUnattainableNotApplyStock = autoUnattainableNotApplyStock;
    }

    public boolean isNewTemplateRule() {
        return isNewTemplateRule;
    }

    public void setNewTemplateRule(boolean newTemplateRule) {
        isNewTemplateRule = newTemplateRule;
    }

    /**
     * 指定异常不取消发货单
     *
     * @link https://gykj.yuque.com/entavv/xb9xi5/qdvoii
     */
    @Data
    public static class Party3WareshoueOrderNotCancelOfExceDto implements Serializable {

        private static final long serialVersionUID = 7140825714760220367L;

        /**
         * 启用状态
         */
        private boolean enableStatus;
        /**
         * 异常ID
         */
        private List<String> exceptionIds;

        public Party3WareshoueOrderNotCancelOfExceDto(boolean enableStatus, List<String> exceptionIds) {
            this.enableStatus = enableStatus;
            this.exceptionIds = exceptionIds;
        }

        public Party3WareshoueOrderNotCancelOfExceDto() {
        }
    }

    public Integer getExpressWhetherFilterTrade() {
        return expressWhetherFilterTrade;
    }

    public void setExpressWhetherFilterTrade(Integer expressWhetherFilterTrade) {
        this.expressWhetherFilterTrade = expressWhetherFilterTrade;
    }
    public Integer getFxgLogisticsExploration() {
        return fxgLogisticsExploration;
    }

    public void setFxgLogisticsExploration(Integer fxgLogisticsExploration) {
        this.fxgLogisticsExploration = fxgLogisticsExploration;
    }

    public Integer getMergeTradeClearTemplate() {
        return mergeTradeClearTemplate;
    }

    public void setMergeTradeClearTemplate(Integer mergeTradeClearTemplate) {
        this.mergeTradeClearTemplate = mergeTradeClearTemplate;
    }

    public Integer getAbroadAuditAutoGetWaybillCode() {
        return abroadAuditAutoGetWaybillCode;
    }

    public void setAbroadAuditAutoGetWaybillCode(Integer abroadAuditAutoGetWaybillCode) {
        this.abroadAuditAutoGetWaybillCode = abroadAuditAutoGetWaybillCode;
    }

    public boolean isGiftRecalculateCashFlowEnabled() {
        return Objects.equals(1, getInteger(NewTradeExtendConfigEnum.FX_GIFT_RECALCULATE_CASH_FLOW));
    }

    private Integer speedPrintSendOfMultiCompany;

    public Integer getSpeedPrintSendOfMultiCompany() {
        return speedPrintSendOfMultiCompany;
    }

    public void setSpeedPrintSendOfMultiCompany(Integer speedPrintSendOfMultiCompany) {
        this.speedPrintSendOfMultiCompany = speedPrintSendOfMultiCompany;
    }
}

package com.raycloud.dmj.domain.logistics.request;


import com.raycloud.dmj.domain.trades.Trade;

public class SubLogisticsTraceParam {

    /**
     * 新快递ID
     */
    private Long expressId;
    /**
     * 新快递名称
     */
    private String expressName;
    /**
     * 新运单号
     */
    private String outSid;

    /**
     * 订单
     */
    private Trade trade;


    public SubLogisticsTraceParam(Long expressId, String expressName, String outSid, Trade trade) {
        this.expressId = expressId;
        this.expressName = expressName;
        this.outSid = outSid;
        this.trade = trade;
    }

    public Long getExpressId() {
        return expressId;
    }

    public void setExpressId(Long expressId) {
        this.expressId = expressId;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Trade getTrade() {
        return trade;
    }

    public void setTrade(Trade trade) {
        this.trade = trade;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }
}

package com.raycloud.dmj.domain.trades.report;

import java.io.Serializable;
import java.util.List;

public class TradeExtra implements Serializable {
    private static final long serialVersionUID = 3641004038362378919L;

    /**
     * 订单号
     */
    private Long sid;
    /**
     * 合单号
     */
    private Long mergeSid;
    /**
     * 历史成本价
     */
    private Double costSnapShot;
    /**
     * 原成本价
     */
    private Double costOrigin;


    private List<OrderExtra> orderExtraList;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getMergeSid() {
        return mergeSid;
    }

    public void setMergeSid(Long mergeSid) {
        this.mergeSid = mergeSid;
    }

    public Double getCostSnapShot() {
        return costSnapShot;
    }

    public void setCostSnapShot(Double costSnapShot) {
        this.costSnapShot = costSnapShot;
    }

    public Double getCostOrigin() {
        return costOrigin;
    }

    public void setCostOrigin(Double costOrigin) {
        this.costOrigin = costOrigin;
    }

    public List<OrderExtra> getOrderExtraList() {
        return orderExtraList;
    }

    public void setOrderExtraList(List<OrderExtra> orderExtraList) {
        this.orderExtraList = orderExtraList;
    }
}

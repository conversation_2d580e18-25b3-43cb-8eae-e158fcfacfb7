package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * 系统备注的修改日志
 * <AUTHOR>
 *
 */
@Table(name = "sys_memo_log", routerKey = "sysMemoLogDbNo")
public class SysMemoLog extends Model {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6459766459250898965L;

	/**
	 * 日志编号
	 */
	private Long id;
	
	/**
	 * 公司编号
	 */
	private Long companyId;
	
	/**
	 * 系统订单号
	 */
	private Long sid;
	
	/**
	 * 修改的备注内容
	 */
	private String memo;
	
	/**
	 * 修改的员工编号
	 */
	private Long staffId;
	
	/**
	 * 修改的员工名称
	 */
	private String staffName;
	
	/**
	 * 修改时间
	 */
	private Date created;
	
	/**
	 * 可用状态
	 */
	private Integer enableStatus;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getSid() {
		return sid;
	}

	public void setSid(Long sid) {
		this.sid = sid;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Long getStaffId() {
		return staffId;
	}

	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}

	public String getStaffName() {
		return staffName;
	}

	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Integer getEnableStatus() {
		return enableStatus;
	}

	public void setEnableStatus(Integer enableStatus) {
		this.enableStatus = enableStatus;
	}

}

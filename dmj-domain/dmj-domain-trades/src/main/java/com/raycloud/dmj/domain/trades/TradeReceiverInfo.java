package com.raycloud.dmj.domain.trades;


import java.io.Serializable;

public class TradeReceiverInfo implements Serializable {
    private static final long serialVersionUID = -3720251618901189823L;

    /**
     * 订单tid
     */
    private String tid;
    /**
     * 收件人ID
     */
    private String oaid;
    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件省份
     */
    private String receiverState;

    /**
     * 收件市
     */
    private String receiverCity;

    /**
     * 收件区县
     */
    private String receiverDistrict;

    /**
     * 收件详细地址
     */
    private String receiverAddress;

    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 收件人电话号码
     */
    private String receiverPhone;
    /**
     * oaid是否和tid当前的oaid匹配
     */
    private Boolean matched;
    /**
     * 买家昵称
     */
    private String buyerNick;

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverState() {
        return receiverState;
    }

    public void setReceiverState(String receiverState) {
        this.receiverState = receiverState;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public Boolean getMatched() {
        return matched;
    }

    public void setMatched(Boolean matched) {
        this.matched = matched;
    }
}

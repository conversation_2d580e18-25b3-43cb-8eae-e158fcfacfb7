package com.raycloud.dmj.domain.express;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeConfig;
import lombok.Data;

/**
 * @Auther mengfanguang
 * @Date 2024/6/14
 */

@Data
public class ExpressMatchContext {

    /**
     * staff信息
     */
    private Staff staff;

    /**
     * 配置
     */
    TradeConfig tradeConfig;

    /**
     * 是否过滤已有快递模版的
     */
    private boolean templateExistFilter;

    /**
     * 没匹配到是否清空快递模版
     */
    private Integer revert;


}
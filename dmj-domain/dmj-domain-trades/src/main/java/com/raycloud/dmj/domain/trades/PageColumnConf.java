package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;


/**
 * Created by ruanyg on 16/5/24.
 */
@Table(name = "page_column_conf", migratable = false)
public class PageColumnConf extends Model {

    private static final long serialVersionUID = 6964614229643741736L;

    //库存状态的列配置
    public static final long Stock_Status_Conf = 63;

    // 仓库库存列配置
    public static final long WAREHOUSE_STOCK_STATUS_CONF = 65;

    // 商品档案V2的列配置
    public static final long ITEM_RECORD_PAGE_ID = 84;

    /**
     * 列配置项ID
     */
    private Long id;

    /**
     * 列编码
     */
    private String colCode;

    /**
     * 列显示标题
     */
    private String colTitle;

    /**
     * 列所属页面ID
     */
    private Long pageId;

    /**
     * 是否为该页面默认显示的列
     */
    private Integer isDefault;

    /**
     * 列在当前页面的排序号
     */
    private Integer sortNo;

    /**
     * 列宽度
     */
    private Integer width;

    /**
     * 列其他配置
     */
    private String config;

    /**
     * 过滤配置
     */
    private String filterConfig;

    /**
     * 列适用的环境
     */
    private String env;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public String getColTitle() {
        return colTitle;
    }

    public void setColTitle(String colTitle) {
        this.colTitle = colTitle;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getFilterConfig() {
        return filterConfig;
    }

    public void setFilterConfig(String filterConfig) {
        this.filterConfig = filterConfig;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
}

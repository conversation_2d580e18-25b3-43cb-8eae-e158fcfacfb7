package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by ZuoYun on 2015/1/22.
 * Time: 15:14
 * Information:
 *
 */

public class TradeCanDeliverModel implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = -1L;

    Long taobaoId;
    String tid;
    Integer canDelivered;

    public static List<TradeCanDeliverModel> valueOf(List<TbTrade> tb) {
        List<TradeCanDeliverModel> models = new LinkedList<TradeCanDeliverModel>();
        for (TbTrade tbTrade : tb) {
            TradeCanDeliverModel model = new TradeCanDeliverModel();
            model.setTid(tbTrade.getTid());
            model.setTaobaoId(tbTrade.getTaobaoId());
            model.setCanDelivered(tbTrade.getCanDelivered());
            models.add(model);
        }
        return models;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Integer getCanDelivered() {
        return canDelivered;
    }

    public void setCanDelivered(Integer canDelivered) {
        this.canDelivered = canDelivered;
    }
}

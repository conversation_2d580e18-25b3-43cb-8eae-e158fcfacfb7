package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.payment.OrderPayment;
import com.raycloud.dmj.domain.trades.payment.SimpleOrderPayment;
import com.raycloud.dmj.domain.trades.payment.SimpleTradePayment;
import com.raycloud.dmj.domain.trades.payment.TradePayment;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import java.util.Map;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-09
 */
public class PaymentLogHelper {

    public static final String PAYMENT_LOG_SIGN = "[金额业务]";


    /**
     * 固定解析头部内容
     * @param companyId
     * @param staffId
     * @param staffName
     *
     * @see com.raycloud.dmj.web.log.Log4jESMessageFormat#handleStringMessage(Map, String)
     * @return
     */
    private static StringBuilder buildBaseHead(Long companyId,Long staffId,String staffName){
        return new StringBuilder("[").append(companyId == null?-1:companyId).append(",").append(staffId == null?-1:staffId).append(",")
                .append(staffName).append("] ");
    }

    private static StringBuilder buildBaseHead(Long companyId){
        if(null == companyId){
            return new StringBuilder("[]");
        }
        return buildBaseHead(companyId,null,null);
    }


    public static StringBuilder buildLogHead(User user){
        if(null == user){
            return buildBaseHead(null).append(PAYMENT_LOG_SIGN);
        }
        return buildBaseHead(user.getCompanyId(),user.getTaobaoId(),user.getNick()).append(PAYMENT_LOG_SIGN);
    }

    public static StringBuilder buildLogHead(Staff staff){
        if(null == staff){
            return buildBaseHead(null).append(PAYMENT_LOG_SIGN);
        }
        return buildBaseHead(staff.getCompanyId(),staff.getId(),staff.getName()).append(PAYMENT_LOG_SIGN);
    }

    public static StringBuilder buildLogHead(Long companyId){
        return buildBaseHead(companyId).append(PAYMENT_LOG_SIGN);
    }

    public static StringBuilder buildLogHead(Long companyId,Long sid){
        return buildBaseHead(companyId).append(PAYMENT_LOG_SIGN).append("[sid:").append(sid).append("]");
    }

    public static StringBuilder buildLogHead(Long companyId,String tid){
        return buildBaseHead(companyId).append(PAYMENT_LOG_SIGN).append("[tid:").append(tid).append("]");
    }

    public static StringBuilder buildLogHead(Long companyId,Long staffId,String staffName){
        return buildBaseHead(companyId, staffId, staffName).append(PAYMENT_LOG_SIGN);
    }

    public static StringBuilder buildLogHead(Staff staff,Long sid){
        return  buildLogHead(staff == null?null:staff.getCompanyId(),sid);
    }

    public static StringBuilder buildLogHead(User user,Long sid){
        return  buildLogHead(user == null?null:user.getCompanyId(),sid);
    }


    public static StringBuilder buildLogHead(Trade trade){
        if(null == trade){
            return buildBaseHead(null).append(PAYMENT_LOG_SIGN);
        }
        if (trade.getSid() != null) {
            return buildLogHead(trade.getCompanyId(),trade.getSid());
        }else {
            return buildLogHead(trade.getCompanyId(),trade.getTid());
        }
    }

    public static StringBuilder buildLog(Staff staff, String msg){
        return buildLogHead(staff).append(msg);
    }

    public static StringBuilder buildLog(User user, String msg){
        return buildLogHead(user).append(msg);
    }

    public static StringBuilder buildErrorLog(User user, Exception e, String msg){
        return buildLogHead(user).append(" ").append(msg).append(" ").append(e.getMessage());
    }
    public static StringBuilder buildErrorLog(Staff staff, Exception e, String msg){
        return buildLogHead(staff).append(" ").append(msg).append(" ").append(e.getMessage());
    }

    public static StringBuilder buildTradePaymentLog(Trade trade,String message){
        StringBuilder append = buildLogHead(trade);
        if (StringUtils.isNotBlank(message)) {
            append.append(message);
        }
        if (trade == null) {
            return append.append("trade:null");
        }
        buildeTradeLog(append, trade);
        return append;
    }


    public static StringBuilder buildTradePaymentLogs(Staff staff,List<Trade> trades,String message){
        StringBuilder append = buildLogHead(staff);
        append.append(buildTradePaymentLogs(trades,message));
        return append;
    }

    public static String buildTradePaymentLogs(List<Trade> trades,String message){
        StringBuilder append = new StringBuilder();
        if (StringUtils.isNotBlank(message)) {
            append.append(message);
        }
        if (CollectionUtils.isEmpty(trades)) {
            return append.append("trades:[]").toString();
        }
        append.append("trades:[");
        for (Trade trade : trades) {
            buildeTradeLog(append, trade);
            append.append(",");
        }
        append =append.deleteCharAt(append.length() - 1);
        append.append("]");

        return append.toString();
    }

    public static StringBuilder buildOrderPaymentLogs(Staff staff,List<Order> orders,String message){
        StringBuilder append = buildLogHead(staff);
        if (StringUtils.isNotBlank(message)) {
            append.append(message);
        }
        if (CollectionUtils.isEmpty(orders)) {
            return append.append("orders:[]");
        }

        append.append(",\"orders\":[");
        for (Order order : orders) {
            buildeOrderLog(append,order);
            append.append(",");
        }
        append =append.deleteCharAt(append.length() - 1);
        append.append("]");

        return append;
    }

    private static void buildeTradeLog(StringBuilder append, Trade trade) {
        append.append("{");
        SimpleTradePayment tradePayment = new SimpleTradePayment();
        copy(tradePayment, trade);
        append.append("\"trade\":").append(JSON.toJSONString(tradePayment));

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isNotEmpty(orders)) {
            append.append(",\"orders\":[");
            for (Order order : orders) {
                buildeOrderLog(append,order);
                append.append(",");
            }
            append =append.deleteCharAt(append.length() - 1);
            append.append("]");
        }

        append.append("}");
    }

    private static void buildeOrderLog(StringBuilder append, Order order) {
        append.append("{");
        SimpleOrderPayment orderPayment = new SimpleOrderPayment();
        copy(orderPayment, order);
        append.append("\"order\":").append(JSON.toJSONString(orderPayment));
        append.append("}");
    }


    private static void copy(Object dest, Object orig) {
        try {
            BeanUtils.copyProperties(dest, orig);
        } catch (Exception e) {
        }
    }
}

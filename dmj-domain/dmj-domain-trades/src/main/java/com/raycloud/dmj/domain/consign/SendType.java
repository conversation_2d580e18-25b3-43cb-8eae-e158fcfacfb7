package com.raycloud.dmj.domain.consign;

/**
 * 发货类型枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-13 19:41
 */
public enum SendType {

    DUMMY("线下无需物流发货", "线下无需物流发货"),

    OFFLINE("线下发货", "自己联系物流发货"),

    ONLINE("在线发货", "在线订单发货"),

    ONLINE_CONFIRM("确认发货", "确认发货通知"),

    RESEND("重新发货", "修改物流公司和运单号并重新发货"),

    DELAY("延时发货","延时发货"),

    AHEAD("定时预发货","定时预发货"),

    AHEAD_GX("供销定时预发货","供销定时预发货"),

    AHEAD_PAY_TIME("按付款时间定时预发货","按付款时间定时预发货"),

    UPLOAD("提前发货","提前发货"),

    //目前仅适用于网易严选订单二次上传 因为网易严选不支持拆单发货但支持一单多包，并且同一笔平台订单可以在三小时内多次上传单号最后合并到一起，故存在此种发货形式
    SEND_TWICE("二次上传","二次上传"),

    OFFLINE_SCAN("线下扫描发货", "线下扫描发货"),

    OFFLINE_MANUAL("线下手动发货", "线下手动发货"),

    MANUAL_UPLOAD("手动预发货", "手动预发货"),

    OFFLINE_PRINT("线下打印发货", "线下打印发货"),

    OFFLINE_DIRECT("线下直接发货", "线下直接发货"),

    OFFLINE_ONLY("线下仅发货", "线下仅发货"),

    BIC_BATCH("BIC批量绑码","BIC批量绑码"),

    BTAS_COMBINE_PARCEL("BTAS组包","BTAS组包"),

    MUL_PACK_UPLOAD("多包裹上传", "多包裹上传"),

    BORDER_AUTO_RESEND("跨境自动重试","跨境自动重试"),
    ;

    private final String name;

    private final String desc;

    SendType(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(String type){
        String result = "";
        switch (type){
            case "DUMMY": result = SendType.DUMMY.getDesc(); break;
            case "OFFLINE" : result = SendType.OFFLINE.getDesc(); break;
            case "ONLINE" : result = SendType.ONLINE.getDesc(); break;
            case "ONLINE_CONFIRM" : result = SendType.ONLINE_CONFIRM.getDesc(); break;
            case "RESEND" : result = SendType.RESEND.getDesc(); break;
            case "DELAY" : result = SendType.DELAY.getDesc(); break;
            case "AHEAD" : result = SendType.AHEAD.getDesc(); break;
            case "UPLOAD" : result = SendType.UPLOAD.getDesc(); break;
            case "OFFLINE_SCAN" : result = SendType.OFFLINE_SCAN.getDesc(); break;
            case "OFFLINE_MANUAL" : result = SendType.OFFLINE_MANUAL.getDesc(); break;
            case "OFFLINE_PRINT" : result = SendType.OFFLINE_PRINT.getDesc(); break;
            case "OFFLINE_ONLY" : result = SendType.OFFLINE_ONLY.getDesc(); break;
            case "OFFLINE_DIRECT" : result = SendType.OFFLINE_DIRECT.getDesc(); break;
            case "BIC_BATCH" : result = SendType.BIC_BATCH.getDesc(); break;
            case "BTAS_COMBINE_PARCEL" : result = SendType.BTAS_COMBINE_PARCEL.getDesc(); break;
            default: break;
        }

        return result;
    }
}

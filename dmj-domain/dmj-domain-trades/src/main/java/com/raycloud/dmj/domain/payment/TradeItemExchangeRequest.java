package com.raycloud.dmj.domain.payment;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description <pre>
 * 订单换商品 金额计算请求参数
 *
 * eg.
 *  原始订单
 *  ① id:1 sku:A  num：4   price：10
 *  ② id:2 sku:B  num：1   price：10
 *  ③ id:3 sku:C  num：1   price：10
 *
 *  按替换公式 A*2=D+E,A+B=F+G+H(F为套件=f1+f2) 替换后
 *
 *  ③ sku:C  num：1   price：10
 *
 *  ④ sku:D  num：1   price：5
 *  ⑤ sku:E  num：1   price：5
 *
 *  ⑥ sku:F  num：1   price：5
 *     套件单品f1  num：1
 *     套件单品f2  num：1
 *  ⑦ sku:G  num：1   price：10
 *  ⑧ sku:H  num：1   price：10
 *
 *  ⑨ sku:A  num：1   price：10
 *
 *  那么 对应传参中必需包含:
 *  mainTrade:{
 *      payment
 *      payAmount
 *      orders 应包含完整的orders ③④⑤⑥(⑥的suites带入)⑦⑧⑨
 *  }
 *  exChangeRel:[
 *      //对应A*2=D+E,
 *     {exchangeOrder:[① exchangeNum 2 ]     newOrders:[④,⑤] }
 *
 *      //对应A+B=F+G+H
 *         // 系统不存在多换多,实际上是前面的一一对应,最后一个做1换n或者n换1,因此这里实际上要拆成两个关系 A=F, B=G+H
 *         // 参见 com.raycloud.dmj.business.item.replace.TradeItemReplaceByFormulaBusiness#calculateMatchResult()
 *      {exchangeOrder:[① exchangeNum 1]     newOrders:[⑥] }
 *      {exchangeOrder:[② exchangeNum 1]     newOrders:[⑦,⑧] }
 * ]
 * leftOrders:[
 *      // 对应①原来num为4,换后剩下的1个A
 *     {key:1(①的id),value:⑨}
 * ]
 *
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-02-14
 */
@Data
public class TradeItemExchangeRequest {

    /**
     * 换商品后的完整trade(包含所有order 含没有换商品的order 且新换商品如果是套件 对应order.getSuits()必需传入)
     */
    private Trade mainTrade;

    /**
     * 新老商品映射关系 注意此参数中的newOrders必需与mainTrade中的order是同一实例
     */
    List<TradeItemExchangeRel> exChangeRel;


    public void addExChangeRel(TradeItemExchangeRel rel) {
        if (this.exChangeRel == null) {
            this.exChangeRel = new ArrayList<>();
        }
        this.exChangeRel.add(rel);
    }

    public void addLeftOrders(Long id, Order leftOrder) {
        if (leftOrders == null) {
            leftOrders = new HashMap<>();
        }
        leftOrders.put(id,leftOrder);
    }

    /**
     * <pre>
     * 数量未完全替换的商品Order映射
     * eg 原order [id:1 商品:A num:3],按公式 A*2=B+C 换商品后 剩余的num对应order2[商品:A num:1]
     * key:1  value:order2
     * 注意此参数中的order2必需与mainTrade中的order是同一实例
     * </pre>
     */
    Map<Long,Order> leftOrders;

    public String toPlainString(){
        StringBuilder sb = new StringBuilder("exChangeRel:[");
        if (exChangeRel != null) {
            for (TradeItemExchangeRel rel : exChangeRel) {
                sb.append(rel.toPlainString()).append(";");
            }
        }
        sb.append("] leftOrders:[");
        if (leftOrders != null) {
            for (Map.Entry<Long, Order> entry : leftOrders.entrySet()) {
                Order order = entry.getValue();
                sb.append("originId:").append(entry.getKey()).append(" item:").append(TradeItemExchangeRel.getOrderIdentity(order)).append(" leftNum:").append(order.getNum()).append(";");
            }
        }
        sb.append("]");
        return sb.toString();
    }

}

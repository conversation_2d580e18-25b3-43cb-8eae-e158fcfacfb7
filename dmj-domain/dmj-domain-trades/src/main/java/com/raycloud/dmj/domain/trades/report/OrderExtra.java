package com.raycloud.dmj.domain.trades.report;

import java.io.Serializable;

public class OrderExtra implements Serializable {
    private static final long serialVersionUID = 7373221578697904532L;
    /**
     * 订单号
     */
    private Long sid;
    /**
     * 子订单号
     */
    private Long oid;
    /**
     * 历史成本价
     */
    private Double costSnapShot;
    /**
     * 原成本价
     */
    private Double costOrigin;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public Double getCostSnapShot() {
        return costSnapShot;
    }

    public void setCostSnapShot(Double costSnapShot) {
        this.costSnapShot = costSnapShot;
    }

    public Double getCostOrigin() {
        return costOrigin;
    }

    public void setCostOrigin(Double costOrigin) {
        this.costOrigin = costOrigin;
    }
}

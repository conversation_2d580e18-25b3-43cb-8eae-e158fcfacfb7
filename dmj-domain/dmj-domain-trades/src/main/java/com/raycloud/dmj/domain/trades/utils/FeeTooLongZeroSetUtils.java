package com.raycloud.dmj.domain.trades.utils;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @Description
 */
public class FeeTooLongZeroSetUtils {
    /**
     * 对金额长度大于szie时，进行置零
     *
     * @param fee  金额
     * @param size 长度
     * @return
     */
    public static String feeOptimize(String fee, Integer size) {
        Double value = null;
        if (null != fee && fee.trim().length() > 0) {
            value = Double.valueOf(fee);
        } else {
            return fee;
        }
        fee = value != null ? String.format("%." + 2 + "f", value) : null;
        if (null != fee && fee.length() > size) {
            return "0.00";
        }
        return fee;
    }
}

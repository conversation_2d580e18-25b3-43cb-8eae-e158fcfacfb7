package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trades.fx.FxCashFlowTradeSyncData;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;

import java.util.*;

/**
 * Created by yangheng on 17/3/8.
 * 订单同步需要处理的数据
 */
public class TradeImportData {
    /**
     * 是否强制更新
     */
    public final boolean forceUpdate;

    public TradeConfig tradeConfig;

    public Set<Long> fxMergeTradeSidUnAuditSet = new HashSet<>();

    public Set<Long> fxSidCancelAuditSet = new HashSet<>();
    /**
     * 延迟同步的订单id
     */
    public Set<Long> fxSidDelaySyncSet = new HashSet<>();

    public Map<Long,Trade> notifyGxTradeMap = new HashMap<>();
    public Set<Long> gxTradeStatusChangeSidSet = new HashSet<>();

    public Set<Long> waitPayToWaitAuditMatchWarehouseSids = new HashSet<>();

    public Set<Long> gxCancelSidSet = new HashSet<>();
    public Set<Long> qimenCancelSidSet = new HashSet<>();

    public Set<Long> platformUpdateItemRematchGiftSidSet = new HashSet<>();



    public Set<Long> gxCancelAuditSidSet = new HashSet<>();

    public Set<Long> gxCancelConsignSidSet = new HashSet<>();

    public List<Trade> gxAddressChangeTrades = new ArrayList<>();

    public Set<Long> gxRecalculatePostFeeSidSet = new HashSet<>();

    public Set<Long> gxItemUpdateExceptIds = new HashSet<>();

    /**
     * 新增的trade
     */
    public List<Trade> insertTrades = new ArrayList<Trade>();

    /**
     * 修改的trade,需要继续进行后续business
     */
    public List<Trade> updateTrades = new ArrayList<>();


    /**
     * 合单的trade
     */
    public List<Trade> mergeTrades = new ArrayList<>();

    public Map<String, Set<Long>> sourceMergeSidsMap = new HashMap<>();

    public Map<Long, List<TbTrade>> mergeSidToTradeMap = new HashMap<>();

    /**
     * 要更新的所有trade
     */
    public List<Trade> updateAllTrades = new ArrayList<>();

    /**
     * 对应系统内拆单的trade
     */
    public Map<Long, String> splitSidTidMap = new HashMap<>();

    /**
     * 本次同步的订单
     */
    public List<Trade> trades;

    /**
     * 本次同步的原始平台订单
     */
    public List<Trade> originTrades = new ArrayList<>();

    /**
     * 本次同步，在系统中已经存在的订单，按tid分组
     */
    public Map<String, List<Trade>> existTradeMap = new HashMap<>();

    /**
     * 同步的时候，有换商品的子订单需要记录日志
     */
    public List<ItemChangeLog> itemChangeLogs = new ArrayList<>();

    /**
     * 同步的时候，地址有修改的订单要记录操作日志
     */
    public List<Trade> addressChangeTrades = new ArrayList<>();

    /**
     * 同步时需要自动反审核的订单sid
     */
    public Map<OpEnum, Set<Long>> opEnumAuditUndoSidMap = new HashMap<>();

    /**
     * 同步时需要重新匹配标签的系统订单
     */
    public Set<Long> sids2SysRecalculationTag = new HashSet<>();

    /**
     * 待审核的地址有变化的合单
     */
    public Map<Long, Set<Long>> waitAuditAddressChangeMergeSidSids = new HashMap<>();

    public TradeImportData(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    /**
     * 退款状态有变化的订单号
     */
    public Set<Long> refundChangeSids = new HashSet<>();
    /**
     *同步时需要删除的订单
     */
    public List<Order> needDeleteOrders = new ArrayList<>();

    /**
     * 分销同步数据容器
     */
    public FxCashFlowTradeSyncData fxTradeSyncData = new FxCashFlowTradeSyncData();

    /**
     * 同步可能需要自动拆分的订单
     */
    public Set<Long> refundAutoSplitSids = new HashSet<>();

    /**
     * 京东直发的订单
     */
    public List<Long> jdWarehouseTrades = new ArrayList<>();

    /**
     * shopee同步的订单
     */
    public Set<String> shopeeSyncTids = new HashSet<>();

    /**
     * shopee有更新过运单号的tid
     */
    public Set<String> shopeeOutSidChangeTids = new HashSet<>();

    /**
     * shopee有拆分状态的订单
     */
    public Set<String> shopeeSplitTids = new HashSet<>();

    /**
     * 第三方仓需要处理的订单
     */
    public List<Trade> party3WarehouseTrades = new ArrayList<>();

    /**
     * 已审核三方仓改地址的订单
     */
    public List<Trade> party3WarehouseAddressChangeTrades = new ArrayList<>();

    /**
     * 待审核订单修改收件人信息，重新计算异常和tag
     */
    public Set<Long> needRemarkException = new HashSet<>();

    /**
     * 同步后需要重新匹配赠品的订单
     */
    public Set<Long> needAutoReMatchGiftSids = new HashSet<>();

    /**
     * 补偿用踢出波次的订单
     */
    public Set<Long> reconsumeRemoveWaveSids = new HashSet<>();
    /**
     * 订单由待付款变为已付款
     */
    public Set<Long> waitPayToWaitAuditSids = new HashSet<>();

    /**
     * 待审核的地址变更的订单
     */
    public Set<Long> waitAuditAddressChangeSids = new HashSet<>();
    /**
     * 平台地址映射
     */
    public Map<String,Trade> platAddressMap=new HashMap<>();
    /**
     * oid 和orderid的对应
     */
    public Map<Long,Set<Long>> oidOrderIdMap=new HashMap<>();

    /**
     * 需要重算的拆分手工单
     */
    public Set<Trade> split2RematchTrades = new HashSet<>();


    /**
     * 奇门供销交易关闭的订单
     */
    public Set<Long> qimenFxRefundTrades = new HashSet<>();

    /**
     * 奇门供销部分关闭的订单
     */
    public Set<Long> qimenFxPartRefundTrades = new HashSet<>();



    /**
     *
     * @param trade
     */
    public void addAddressChangeTrade(Trade trade) {
        Trade update = new TbTrade();
        if (    Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus())||
                Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())||
                Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus())||
                Trade.SYS_STATUS_WAIT_MANUAL_AUDIT.equals(trade.getSysStatus())) {
            needRemarkException.add(trade.getSid());
        }
        if (TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
            waitAuditAddressChangeSids.add(trade.getSid());
        }
        update.setSid(trade.getSid());
        update.setOutSid(trade.getOutSid());
        update.setTemplateId(trade.getTemplateId());
        update.setTemplateType(trade.getTemplateType());
        addressChangeTrades.add(update);
    }

}

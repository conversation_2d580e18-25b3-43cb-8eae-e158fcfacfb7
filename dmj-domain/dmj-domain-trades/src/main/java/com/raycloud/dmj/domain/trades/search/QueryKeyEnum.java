package com.raycloud.dmj.domain.trades.search;


import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-08-07
 */
public enum QueryKeyEnum {

    SID("sid","系统单号",Long.class),

    SHORT_ID("short_id","内部单号",Long.class),

    MERGE_SID("merge_sid","合单主单号",Long.class,-1L),

    SPLIT_SID("split_sid","拆单原单号",Long.class,-1L),

    TID("tid","平台单号",String.class),

    OUT_SID("out_sid","运单号",String.class,""),

    WAVE_ID("wave_id","波次号",Long.class,0L),

    ;

    private String field,name;
    private Class type;

    /**
     * 一般为数据库默认值 按默认值查询数据库庞大 会有OOM风险
     */
    private List<Object> forbiddenValues = new ArrayList<>();
    QueryKeyEnum(String field, String name,Class type) {
        this.field = field;
        this.name = name;
        this.type = type;
        forbiddenValues = Collections.emptyList();
    }

    QueryKeyEnum(String field, String name,Class type,Object ... forbiddens) {
        this.field = field;
        this.name = name;
        this.type = type;
        if (forbiddens != null) {
            forbiddenValues = Arrays.asList(forbiddens);
        }else {
            forbiddenValues = Collections.emptyList();
        }
    }

    public static QueryKeyEnum getByField(String field){
        if (StringUtils.isBlank(field)) {
            return null;
        }
        field = field.trim().toLowerCase();
        for (QueryKeyEnum value : QueryKeyEnum.values()) {
            if (Objects.equals(value.field,field) || Objects.equals("t." +value.field, field) ) {
                return value;
            }
        }
        return null;
    }

    public String getField() {
        return field;
    }

    public String getName() {
        return name;
    }

    public Class getType() {
        return type;
    }

    public List<Object> getForbiddenValues() {
        return forbiddenValues;
    }
}

package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Orderable;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNotBlank;


/**
 * @Description <pre>
 * 金额检查
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-21
 */
public class PaymentChecker {


    private static final Logger logger = Logger.getLogger(PaymentChecker.class);


    public static JSONObject checkTradePayment(Trade trade,Trade updated){
        JSONObject json = new JSONObject(true);

        //售后订单不校验
        if (TradeUtils.isReissueOrChangeitem(trade)) {
            return null;
        }

        if (updated == null || updated.getPayment() != null) {
            appendErrorIfNull(json,"payment",trade.getPayment());
            appendErrorIfLessThanZero(json,"payment",trade.getPayment());
            appendErrorIfScale(json,"payment",MathUtils.toBigDecimalWithOutHalfup(trade.getPayment()));
        }
        if (updated == null || updated.getPayAmount() != null) {
            //供销订单没有payAmount
            if (!TradeUtils.isGxTrade(trade) && trade.getSid() != null) {
                appendErrorIfNull(json,"payAmount",trade.getPayAmount());
                appendErrorIfLessThanZero(json,"payAmount",trade.getPayAmount());
                appendErrorIfScale(json,"payAmount",MathUtils.toBigDecimalWithOutHalfup(trade.getPayAmount()));
            }
        }
        if (updated == null || updated.getTotalFee() != null) {
            appendErrorIfNull(json,"totalFee",trade.getTotalFee());
            appendErrorIfLessThanZero(json,"totalFee",trade.getTotalFee());
            appendErrorIfScale(json,"totalFee",MathUtils.toBigDecimalWithOutHalfup(trade.getTotalFee()));
        }
        if (updated == null || updated.getDiscountFee() != null) {
            appendErrorIfNull(json,"discountFee",trade.getDiscountFee());
            appendErrorIfScale(json,"discountFee",MathUtils.toBigDecimalWithOutHalfup(trade.getDiscountFee()));
        }
        if (updated == null || updated.getCost() != null) {
            appendErrorIfScale(json,"cost",MathUtils.toBigDecimalWithOutHalfup(trade.getCost()));
        }
        if (updated == null || updated.getSaleFee() != null) {
            appendErrorIfScale(json,"saleFee",MathUtils.toBigDecimalWithOutHalfup(trade.getSaleFee()));
        }

        BigDecimal totalFee = MathUtils.toBigDecimal(trade.getTotalFee());
        BigDecimal payment = MathUtils.toBigDecimal(trade.getPayment());
        BigDecimal payAmount = MathUtils.toBigDecimal(trade.getPayAmount());
        BigDecimal discountFee = MathUtils.toBigDecimal(trade.getDiscountFee());
        BigDecimal cost = MathUtils.toBigDecimal(trade.getCost());
        BigDecimal saleFee = MathUtils.toBigDecimal(trade.getSaleFee());

        List<Order> orders4Trade = getOrders4Trade(trade);
        if (CollectionUtils.isNotEmpty(orders4Trade)) {
            List<BigDecimal> orderTotalFees = new ArrayList<>();
            List<BigDecimal> orderPayAmounts = new ArrayList<>();
            List<BigDecimal> orderDiscountFees = new ArrayList<>();
            List<BigDecimal> orderCosts = new ArrayList<>();
            List<BigDecimal> orderSaleFees = new ArrayList<>();

            boolean orderTotalFeesChanged = false;
            boolean orderPayAmountsChanged = false;
            boolean orderDiscountFeesChanged = false;
            boolean orderCostsChanged = false;
            boolean orderSaleFeesChanged = false;


            Map<Long,Order> updatedOrders = new HashMap<>();
            if (updated != null ) {
                List<Order> orders = getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders)) {
                    updatedOrders = orders.stream().collect(Collectors.toMap(x-> x.getId(),x ->x));
                }
            }

            JSONArray array = new JSONArray();
            boolean containsMergeOrder = false;
            for (Order order : orders4Trade) {
                Order updateOrder = updatedOrders.get(order.getId());
                JSONObject jsonObject = checkOrderPayment(order,updated != null,updateOrder,TradeUtils.isGxTrade(trade));
                if (jsonObject != null) {
                    array.add(jsonObject);
                }
                if (order.getCombineId() != null && order.getCombineId() != 0L) {
                    continue;
                }
                orderCosts.add(MathUtils.multiply(order.getCost(),order.getNum()));
                //过滤掉合单进来的商品
                if (!Objects.equals(order.getSid(),trade.getSid())) {
                    containsMergeOrder = true;
                    continue;
                }
                orderTotalFees.add(MathUtils.toBigDecimal(order.getTotalFee()));
                orderPayAmounts.add(MathUtils.toBigDecimal(order.getPayAmount()));
                orderDiscountFees.add(MathUtils.toBigDecimal(order.getDiscountFee()));
                orderSaleFees.add(MathUtils.toBigDecimal(order.getSaleFee()));

                orderTotalFeesChanged = orderTotalFeesChanged || (updated == null || (updateOrder != null && updateOrder.getTotalFee() != null));
                orderPayAmountsChanged = orderPayAmountsChanged || (updated == null || (updateOrder != null && updateOrder.getPayAmount() != null));
                orderDiscountFeesChanged = orderDiscountFeesChanged || (updated == null || (updateOrder != null && updateOrder.getDiscountFee() != null));
                orderCostsChanged = orderCostsChanged || (updated == null || (updateOrder != null && updateOrder.getCost() != null));
                orderSaleFeesChanged = orderSaleFeesChanged || (updated == null || (updateOrder != null && updateOrder.getSaleFee() != null));

            }
            if (array.size() > 0) {
                json.put("orders",array);
            }

            boolean totalFeeChanged = updated == null || updated.getTotalFee() != null;
            boolean discountFeeChanged = updated == null || updated.getDiscountFee() != null;
            boolean paymentChanged = updated == null || updated.getPayment() != null;
            boolean payAmountChanged = updated == null || updated.getPayAmount() != null;
            boolean costChanged = updated == null || updated.getCost() != null;
            boolean saleFeeChanged = updated == null || updated.getCost() != null;
            boolean postFeeChanged = updated == null || updated.getPostFee() != null;
            boolean taxFeeChanged = updated == null || updated.getTaxFee() != null;



            if (totalFeeChanged || orderTotalFeesChanged){
                appendErrorIfSumNotEquals(json,"totalFee",totalFee,totalFeeChanged,"orderTotalFees",orderTotalFees,orderTotalFeesChanged);
            }
            if (payAmountChanged|| orderPayAmountsChanged){
                if (!TradeUtils.isGxTrade(trade)) {
                    appendErrorIfSumNotEquals(json,"payAmount",payAmount,payAmountChanged,"orderPayAmounts",orderPayAmounts,orderPayAmountsChanged);
                }
            }

            if (costChanged || orderCostsChanged){
                if (!(TradeUtils.isMerge(trade) && Objects.equals(trade.getSid(),trade.getMergeSid())) || containsMergeOrder){
                    appendErrorIfSumNotEquals(json,"cost",cost,costChanged,"orderCosts",orderCosts,orderCostsChanged);
                }
            }

            if (saleFeeChanged || orderSaleFeesChanged ){
                appendErrorIfSumNotEquals(json,"saleFee",saleFee,saleFeeChanged,"orderSaleFees",orderSaleFees,orderSaleFeesChanged);
            }


            if (totalFeeChanged || orderDiscountFeesChanged || postFeeChanged|| taxFeeChanged || paymentChanged || discountFeeChanged){

                BigDecimal sum = MathUtils.add(orderDiscountFees.toArray(new BigDecimal[0]));
                //订单优惠 = 订单应付  - ∑商品优惠+ 运费 + 税费 - 订单实付
                BigDecimal subtract = new BigDecimalWrapper(totalFee).subtract(sum).add(trade.getPostFee()).add(trade.getTaxFee()).subtract(payment).get();
                if (!MathUtils.equals(subtract,discountFee)) {
                    appendError(json,"discountFee",
                            "discountFee:",discountFee,changeSign(discountFeeChanged),
                            "≠totalFee:", totalFee,changeSign(totalFeeChanged),"-orderDiscountFees:", sum,changeSign(orderDiscountFeesChanged),"(",StringUtils.join(orderDiscountFees,"+"),")",
                            "+postFee:", trade.getPostFee(),changeSign(postFeeChanged),"+taxFee:", trade.getTaxFee(),changeSign(taxFeeChanged),"-payment:", payment,changeSign(paymentChanged),"=",subtract);
                }
            }

        }


        if (json.keySet().size()>0) {
            json.put("sid",trade.getSid());
            json.put("companyId",trade.getCompanyId());
            json.put("tid",trade.getTid());
            json.put("source",trade.getSource());
            if (isNotBlank(trade.getSubSource())) {
                json.put("subSource",trade.getSubSource());
            }
            return json;
        }
        return null;
    }

    private static List<Order> getOrders4Trade(Trade trade){
        if (trade instanceof Orderable) {
            return TradeUtils.getOrders4Trade(trade);
        }
        return null;
    }

    public static JSONObject checkOrderPayment(Order order,boolean update,Order updated,boolean gxTrade){
        JSONObject json = new JSONObject();

        if (!update || (updated !=null && updated.getPayment() != null)) {
            appendErrorIfNull(json,"payment",order.getPayment());
            appendErrorIfLessThanZero(json,"payment",order.getPayment());
            appendErrorIfScale(json,"payment",MathUtils.toBigDecimalWithOutHalfup(order.getPayment()));
        }
        if (!update || (updated !=null && updated.getPayAmount() != null)) {
            if (!gxTrade  && order.getSid() != null) {
                appendErrorIfNull(json,"payAmount",order.getPayAmount());
                appendErrorIfLessThanZero(json,"payAmount",order.getPayAmount());
                appendErrorIfScale(json,"payAmount",MathUtils.toBigDecimalWithOutHalfup(order.getPayAmount()));
            }
        }
        if (!update || (updated !=null && updated.getTotalFee() != null)) {
            appendErrorIfNull(json,"totalFee",order.getTotalFee());
            appendErrorIfLessThanZero(json,"totalFee",order.getTotalFee());
            appendErrorIfScale(json,"totalFee",MathUtils.toBigDecimalWithOutHalfup(order.getTotalFee()));
        }
        if (!update || (updated !=null && updated.getNum() != null)) {
            appendErrorIfNull(json,"num",order.getNum());
        }
        if (!update || (updated !=null && updated.getPrice() != null)) {
            appendErrorIfNull(json,"price",order.getPrice());
        }
        if (!update || (updated !=null && updated.getDivideOrderFee() != null)) {
            appendErrorIfScale(json,"divideOrderFee",MathUtils.toBigDecimalWithOutHalfup(order.getDivideOrderFee()));
        }
        if (!update || (updated !=null && updated.getDiscountFee() != null)) {
            appendErrorIfScale(json,"discountFee",MathUtils.toBigDecimalWithOutHalfup(order.getDiscountFee()));
        }


        BigDecimal totalFee = MathUtils.toBigDecimal(order.getTotalFee());
        BigDecimal payment = MathUtils.toBigDecimal(order.getPayment());
        BigDecimal payAmount = MathUtils.toBigDecimal(order.getPayAmount());
        BigDecimal divideOrderFee = MathUtils.toBigDecimal(order.getDivideOrderFee());
        BigDecimal discountFee = MathUtils.toBigDecimal(order.getDiscountFee());
        BigDecimal saleFee = MathUtils.toBigDecimal(order.getSaleFee());



        boolean totalFeeChanged = !update || (updated !=null && updated.getTotalFee() != null);
        boolean numChanged = !update || (updated !=null && updated.getNum() != null);
        boolean priceFeeChanged = !update || (updated !=null && updated.getPrice() != null);

        boolean discountFeeChanged = !update || (updated !=null && updated.getDiscountFee() != null);
        boolean paymentChanged = !update || (updated !=null && updated.getPayment() != null);

        boolean saleFeeChanged = !update || (updated !=null && updated.getSaleFee() != null);
        boolean salePriceChanged = !update || (updated !=null && updated.getSalePrice() != null);

        boolean payAmountChanged = !update || (updated !=null && updated.getPayAmount() != null);
        boolean divideOrderFeeChanged = !update || (updated !=null && updated.getDivideOrderFee() != null);


        if (totalFeeChanged || priceFeeChanged  || numChanged) {
            //totalFee = price*num
            BigDecimal multiply = MathUtils.multiply(order.getPrice(), order.getNum());
            if (!MathUtils.equals(totalFee,multiply)) {
                appendError(json,"totalFee","totalFee:", order.getTotalFee(),changeSign(totalFeeChanged),
                        "≠price:" ,order.getPrice(),changeSign(priceFeeChanged),"*num:",order.getNum(),changeSign(numChanged),"=",multiply);
            }
        }

        if (discountFeeChanged || paymentChanged  || totalFeeChanged) {
            //discountFee = totalFee - payment
            BigDecimal subtract = MathUtils.subtract(totalFee, payment);
            if (!MathUtils.equals(subtract,discountFee)) {
                appendError(json,"discountFee",
                        "discountFee:", order.getDiscountFee(),changeSign(discountFeeChanged),
                        "≠totalFee:",order.getTotalFee(),changeSign(totalFeeChanged),"-payment:",order.getPayment(),changeSign(paymentChanged),"=",subtract);
            }
        }

        if (saleFeeChanged || salePriceChanged || numChanged) {
            //saleFee = salePrice * num
            BigDecimal multiply2 = MathUtils.multiply(order.getSalePrice(),order.getNum());
            if (!MathUtils.equals(multiply2,saleFee)) {
                appendError(json,"saleFee",
                        "saleFee:",order.getSaleFee(),changeSign(saleFeeChanged),
                        "≠salePrice:",order.getSalePrice(),changeSign(salePriceChanged),"*num:",order.getNum(),changeSign(numChanged),"=",multiply2);
            }
        }

        if (paymentChanged || payAmountChanged || divideOrderFeeChanged) {
            if (CollectionUtils.isNotEmpty(order.getSuits())) {
                checkSuites(order, json, payment, payAmount, divideOrderFee,gxTrade);
            }
        }

        if (json.keySet().size()>0) {
            if (isNotBlank(order.getSysOuterId())) {
                json.put("商家编码",order.getSysOuterId());
            }else {
                json.put("平台商家编码", isNotBlank(order.getOuterSkuId())?order.getOuterSkuId():order.getOuterIid());
            }
            json.put("id",order.getId());
            return json;
        }
        return null;
    }

    private static String changeSign(boolean changed){
        return  changed?"[C]":"";
    }

    private static void checkSuites(Order order, JSONObject json, BigDecimal payment, BigDecimal payAmount, BigDecimal divideOrderFee,boolean gxTrade) {
        JSONArray array = new JSONArray();

        List<BigDecimal> suiteTotalPayment = new ArrayList<>();
        List<BigDecimal> suiteTotalPayAmount = new ArrayList<>();
        List<BigDecimal> suiteTotalDivideOrderFee = new ArrayList<>();

        for (Order suit : order.getSuits()) {
            JSONObject s = checkOrderPayment(suit,false,null,gxTrade);
            if (s != null) {
                array.add(s);
            }
            suiteTotalPayment.add(MathUtils.toBigDecimal(suit.getPayment()));
            suiteTotalPayAmount.add(MathUtils.toBigDecimal(suit.getPayAmount()));
            suiteTotalDivideOrderFee.add(MathUtils.toBigDecimal(suit.getDivideOrderFee()));
        }

        appendErrorIfSumNotEquals(json,"payment",payment,false,"suiteTotalPayment",suiteTotalPayment,false);
        appendErrorIfSumNotEquals(json,"payAmount",payAmount,false,"suiteTotalPayAmount",suiteTotalPayAmount,false);
        //appendErrorIfSumNotEquals(json,"divideOrderFee",divideOrderFee,false,"suiteTotalDivideOrderFee",suiteTotalDivideOrderFee,false);

        if (array.size() > 0) {
            json.put("suits",array);
        }
    }




    private static void appendErrorIfLessThanZero(JSONObject json,String field,String value){
        BigDecimal b = StringUtils.isBlank(value) ? BigDecimal.ZERO:new BigDecimal(value);
        if (b.compareTo(BigDecimal.ZERO) < 0) {
            appendError(json,field,value);
        }
    }

    private static void appendErrorIfNull(JSONObject json,String field,Object value){
        if (Objects.isNull(value)) {
            appendError(json,field,"为空");
        }
    }


    private static void appendErrorIfScale(JSONObject json,String field,BigDecimal value){
        //商品那边 成本价存的是3位小数
        //历史原因部分数据是处理为3位小数
        if (Objects.nonNull(value) && value.scale() > getMaxScale(field)) {
            appendError(json,field,value+" 精度超出");
        }
    }

    private static void appendError(JSONObject json,String field,Object ... error){
        json.put(field,StringUtils.join(error));
    }

    private static void appendErrorIfSumNotEquals(JSONObject json, String field, BigDecimal total,boolean changed, String targetField, List<BigDecimal> nums,boolean subsChanged){
        BigDecimal sum = MathUtils.add(nums.toArray(new BigDecimal[0]));
        if (!MathUtils.equals(sum,total)) {
            appendError(json,field,field,":",total,changeSign(changed),"≠",targetField,":", sum,changeSign(subsChanged),"(",StringUtils.join(nums,"+"),")");
        }
    }





    /**
     * 仅数据有效性判断 并尝试修复
     * @param trade
     * @return
     */
    public static JSONObject checkTradeNumbers(Trade trade,boolean tryRepair){
        //售后订单不校验
        if (TradeUtils.isReissueOrChangeitem(trade)) {
            return null;
        }

        Long companyId = trade.getCompanyId();

        JSONObject json = new JSONObject();

        String repair = appendErrorIfIllegal(companyId,json, "totalFee", trade.getTotalFee(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setTotalFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "payment", trade.getPayment(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setPayment(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "DiscountFee", trade.getDiscountFee(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setDiscountFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "payAmount", trade.getPayAmount(),tryRepair,true,true);
        if (isNotBlank(repair)) {
            trade.setPayAmount(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "AcPayment", trade.getAcPayment(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setAcPayment(repair);
        }

        Double doub = appendErrorIfIllegal(companyId,json, "cost", trade.getCost(),tryRepair,true);
        if (doub != null) {
            trade.setCost(doub);
        }
        repair = appendErrorIfIllegal(companyId,json, "saleFee", trade.getSaleFee(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setSaleFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "PostFee", trade.getPostFee(),tryRepair);
        if (isNotBlank(repair)) {
            trade.setPostFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "ActualPostFee", trade.getActualPostFee(),tryRepair,true,false);
        if (isNotBlank(repair)) {
            trade.setActualPostFee(repair);
        }
        doub = appendErrorIfIllegal(companyId,json, "theoryPostFee", trade.getTheoryPostFee(),tryRepair,false);
        if (doub != null) {
            trade.setTheoryPostFee(doub);
        }

        repair = appendErrorIfIllegal(companyId,json, "GrossProfit", trade.getGrossProfit() == null? null:String.valueOf(trade.getGrossProfit()),tryRepair);
        if (isNotBlank(repair)) {
            trade.setGrossProfit(Double.parseDouble(repair));
        }

        //getOrders4Trade接收的参数类型是Trade 里边做了个强转Orderable 但Trade本身并没有实现Orderable -_-!
        if(trade instanceof  Orderable){
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orders4Trade)) {
                JSONArray array = new JSONArray();
                for (Order order : orders4Trade) {
                    JSONObject jsonObject = checkOrderNumbers(order,tryRepair);
                    if (jsonObject != null) {
                        array.add(jsonObject);
                    }
                }
                if (array.size() > 0) {
                    json.put("orders", array);
                }
            }
        }

        if (json.keySet().size()>0) {
            json.put("tid",trade.getTid());
            json.put("userId",trade.getUserId());
            return json;
        }
        return null;
    }

    /**
     * 仅数据有效性判断 并尝试修复
     * @param trade
     * @return
     */
    public static JSONObject checkOrderNumbers(Order order,boolean tryRepair){
        JSONObject json = new JSONObject();
        Long companyId = order.getCompanyId();
        String repair = appendErrorIfIllegal(companyId,json, "totalFee", order.getTotalFee(),tryRepair);
        if (isNotBlank(repair)) {
            order.setTotalFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "Price", order.getPrice(),tryRepair);
        if (isNotBlank(repair)) {
            order.setPrice(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "payment", order.getPayment(),tryRepair);
        if (isNotBlank(repair)) {
            order.setPayment(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "DiscountFee", order.getDiscountFee(),tryRepair);
        if (isNotBlank(repair)) {
            order.setDiscountFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "payAmount", order.getPayAmount(),tryRepair,true,true);
        if (isNotBlank(repair)) {
            order.setPayAmount(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "AcPayment", order.getAcPayment(),tryRepair);
        if (isNotBlank(repair)) {
            order.setAcPayment(repair);
        }
        Double doub = appendErrorIfIllegal(companyId,json, "costPrice", order.getCost(),tryRepair,true);
        if (doub != null) {
            order.setCost(doub);
        }
        repair = appendErrorIfIllegal(companyId,json, "saleFee", order.getSaleFee(),tryRepair);
        if (isNotBlank(repair)) {
            order.setSaleFee(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "SalePrice", order.getSalePrice(),tryRepair);
        if (isNotBlank(repair)) {
            order.setSalePrice(repair);
        }
        repair = appendErrorIfIllegal(companyId,json, "DivideOrderFee", order.getDivideOrderFee(),tryRepair);
        if (isNotBlank(repair)) {
            order.setDivideOrderFee(repair);
        }

        List<Order> suits = order.getSuits();
        if (CollectionUtils.isNotEmpty(suits)) {
            JSONArray array = new JSONArray();
            for (Order suit : suits) {
                JSONObject jsonObject = checkOrderNumbers(suit,tryRepair);
                if (jsonObject != null) {
                    array.add(jsonObject);
                }
            }
            if (array.size() > 0) {
                json.put("suits", array);
            }
        }
        if (json.keySet().size()>0) {
            json.put("id",order.getId());
            json.put("oid",order.getOid() != null ? order.getOid():order.getSoid());
            json.put("OuterIid",order.getOuterIid());
            return json;
        }
        return null;

    }


    private static String appendErrorIfIllegal(Long companyId,JSONObject json,String field,String value,boolean tryRepair){
        return appendErrorIfIllegal(companyId,json,field,value,tryRepair,false,true);
    }

    private static int getMaxScale(String field){
        return 6;
    }

    /**
     *
     * @param json
     * @param field
     * @param value
     * @param maxLen
     * @param tryRepair
     * @param toZeroIfErr 当数据无法修复时 是否强制设为0,主要考虑订单同步时先入库, 但实际运费等其他业务操作更新字段,则不应强制设为0,而应抛出异常让业务员有感知并修正数据
     * @return
     */
    private static String appendErrorIfIllegal(Long companyId,JSONObject json,String field,String value,boolean tryRepair,boolean allowNull,boolean toZeroIfErr){
        int maxLen = 20;
        if (ConfigHolder.PAYMENT_CALCULATE_CONFIG.useMaxLength16(companyId)) {
            maxLen = 16;
        }
        if (Objects.isNull(value)) {
            return null;
        }
        PaymentFixUtils.FixResult<String> fixResult = PaymentFixUtils.fixNumString(value, maxLen, getMaxScale(field), tryRepair,allowNull, toZeroIfErr);
        if (StringUtils.isNotBlank(fixResult.getMessage())) {
            JSONObject object = new JSONObject();
            object.put("msg",fixResult.getMessage());
            object.put("origin",fixResult.getOrigin());
            json.put(field,object);
        }
        return fixResult.getFixed();
    }

    /**
     *
     * @param json
     * @param field
     * @param value
     * @param maxLen 这里指整数位的长度 比如数据库定义字段为 double(12,3),则整数位长度为9
     * @param tryRepair
     * @param toZeroIfErr 当数据无法修复时 是否强制设为0,主要考虑订单同步时先入库, 但实际运费等其他业务操作更新字段,则不应强制设为0,而应抛出异常让业务员有感知并修正数据
     * @return
     */
    private static Double appendErrorIfIllegal(Long companyId,JSONObject json,String field,Double value,boolean tryRepair,boolean toZeroIfErr){
        int maxLen = 20;
        if (Objects.isNull(value)) {
            return null;
        }
        PaymentFixUtils.FixResult<Double> fixResult = PaymentFixUtils.fixNumDouble(value, maxLen, getMaxScale(field), tryRepair, toZeroIfErr);
        if (StringUtils.isNotBlank(fixResult.getMessage())) {
            JSONObject object = new JSONObject();
            object.put("msg",fixResult.getMessage());
            object.put("origin",fixResult.getOrigin());
            json.put(field,object);
        }
        return fixResult.getFixed();
    }



    public static String checkTradeCost(Trade trade){
        JSONObject json = new JSONObject();
        Double doub = appendErrorIfIllegal(trade.getCompanyId(),json, "cost", trade.getCost(),true,true);
        if (doub != null) {
            trade.setCost(doub);
        }
        return json.getString("cost");
    }

}

package com.raycloud.dmj.domain.trades.statusenum;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: qingfeng.cxb
 * @create: 2019-10-19 16:00
 */
public class RefundStatusEnum {

    public static final String REFUND_WAIT_SELLER_AGREE = "WAIT_SELLER_AGREE";//待卖家同意退款

    public static final String REFUND_WAIT_BUYER_RETURN_GOODS = "WAIT_BUYER_RETURN_GOODS";//待买家退货

    public static final String REFUND_WAIT_SELLER_CONFIRM_GOODS = "WAIT_SELLER_CONFIRM_GOODS";//待卖家确认收货

    public static final String REFUND_SELLER_REFUSE_BUYER = "SELLER_REFUSE_BUYER";//卖家拒绝退款

    public static final String REFUND_CLOSED = "CLOSED";//退款关闭

    public static final String REFUND_SUCCESS = "SUCCESS";//退款成功

    public static final String NO_REFUND = "NO_REFUND";//未退款

    /**
     * 这个是系统的一个退款状态，当平台订单的状态为待发货时，买家申请了退款，卖家是不能直接拒绝退款，只能强制发货，所以这个时候需要把退款状态设置为这个状态
     */
    public static final String REFUND_SELLER_CONTINUE_CONSIGN = "SELLER_CONTINUE_CONSIGN";

    public static final Map<String, String> refundStatusMsgMap = new HashMap<>();
    public static final Map<Long, String> isRefundMsgMap = new HashMap<>();


    static {
        refundStatusMsgMap.put(REFUND_WAIT_SELLER_AGREE, "待卖家同意退款");
        refundStatusMsgMap.put(REFUND_WAIT_BUYER_RETURN_GOODS, "待买家退货");
        refundStatusMsgMap.put(REFUND_WAIT_SELLER_CONFIRM_GOODS, "待卖家确认收货");
        refundStatusMsgMap.put(REFUND_SELLER_REFUSE_BUYER, "卖家拒绝退款");
        refundStatusMsgMap.put(REFUND_CLOSED, "退款关闭");
        refundStatusMsgMap.put(REFUND_SUCCESS, "退款成功");
        refundStatusMsgMap.put(null, "未退款");
        refundStatusMsgMap.put(NO_REFUND, "未退款");
        refundStatusMsgMap.put(REFUND_SELLER_CONTINUE_CONSIGN, "待发货订单退款");
        isRefundMsgMap.put(null, "无子订单退款中!");
        isRefundMsgMap.put(0L, "无子订单退款中!");
        isRefundMsgMap.put(1L, "有子订单退款中!");
    }
}

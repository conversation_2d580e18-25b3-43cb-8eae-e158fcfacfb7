package com.raycloud.dmj.domain.trades;

import java.util.Date;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_05_25 09:52
 */
public class FastSysMemo {

    private Long id;
    private String message;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Date getUsageTime() {
        return usageTime;
    }

    public void setUsageTime(Date usageTime) {
        this.usageTime = usageTime;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    private Long companyId;
    private Date usageTime;
    private Long accountId;


}

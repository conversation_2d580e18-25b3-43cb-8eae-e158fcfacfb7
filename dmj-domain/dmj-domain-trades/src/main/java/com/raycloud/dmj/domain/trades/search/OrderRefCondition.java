package com.raycloud.dmj.domain.trades.search;

import lombok.Data;


/**
 * @Description <pre>
 *   order相关的查询条件
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-20
 */
@Data
public class OrderRefCondition {


    /**
     * 系统主商品Id
     */
    private Long[] SysItemIds;


    /**
     * 系统规格
     */
    private Long[] SysSkuIds;


    /**
     * 系统主商家编码
     */
    private String[] mainOuterIds;


    /**
     * 系统商品类目ID
     */
    private Long cid;


    /**
     * 0 模糊查询  1 精确查询
     */
    private int queryType;

    /**
     * 系统商品名称(已匹配) 或 平台商品名称(未匹配)
     */
    private String itemTitle;

    /**
     * 系统商品简称
     */
    private String shortTitle;

    /**
     * 系统商品备注
     */
    private String itemRemark;

    /**
     * 系统规格备注
     */
    private String skuRemark;

    /**
     * 系统规格属性
     */
    private String skuProps;

    /**
     * 系统规格别名
     */
    private String skuPropAlias;

    //商品唯一码
    private String[] uniqueCodes;

    /**
     * 无需发货 0-否、1-是
     */
    private Integer nonConsign;

    /**
     * 商品识别码
     */
    private String identCode;

    /**
     * 系统商家编码
     */
    private String[] sysOuterIds;

    /**
     * 库存状态
     */
    private String[] stockStatus;


    /**
     * 系统商家编码(已匹配) 或 平台商家编码(未匹配 outerIid or outer_sku_id)
     */
    private String[] outerIds;

    /**
     * 商家编码附属拓展条件 类型 对应前端条件参数中的 itemType
     * 0(or null) 无限制
     * 1 仅查单品 (这里实际是指普通商品 而非套件下的单品)
     * 2 仅查套件
     * 3 仅查组合装
     * 4 仅查缺货商品
     */
    private Integer outerIdRefType;

    /**
     * 平台商家编码(outerIid or outer_sku_id)
     */
    private String[] platformOuterIds;


    private Integer[] types;

    /**
     * 平台主商家编码
     */
    private String[] outerIids;

    /**
     * 平台名称
     */
    private String platFormTitle;


    /**
     * 平台商品ID
     */
    private String[] numIids;

    /**
     * 平台skuid
     */
    private String skuIds[];

    /**
     * 平台属性规格
     */
    private String platSkuProp;


    /**
     * 这个是前端独立的一个查询条件 用于过滤平台或系统Sku编码  全模糊查询
     */
    private String skuOuterId;


    /**
     * order拓展条件
     */
    private OrderAdditionalConditionEnum[] additionals;





}

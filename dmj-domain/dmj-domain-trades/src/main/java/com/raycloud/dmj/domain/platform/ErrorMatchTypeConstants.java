package com.raycloud.dmj.domain.platform;

/**
 * @description
 * <AUTHOR>
 * @date 2023/3/23 15:47
 */
public class ErrorMatchTypeConstants {

    public static final String MAPPING_CACHE_KEY_SUFFIX = "_plat_error_mapping_cache";

    /**
     * 根据 code 和 sub_code 进行匹配，适用于错误码规范，sub_code 可以唯一对应一个错误描述的平台
     */
    public static final String SUB_CODE = "1";

    /**
     * 根据 code 和 sub_code 筛选，最后按 sub_msg 进行匹配，适用于 sub_code 不能唯一对应一个错误描述，需要额外根据子错误信息进行匹配的平台
     */
    public static final String SUB_CODE_MSG = "2";

    /**
     * 根据 code 进行匹配，适用于没有子错误码和子错误信息(sub_code和sub_msg)，但是 code 可以唯一对应一个错误描述的平台
     */
    public static final String CODE = "3";

    /**
     * 根据 code 进行筛选，再用 msg 进行匹配，适用于没有子错误码和子错误信息(sub_code和sub_msg)，且 code 不能唯一对应一个错误描述的平台
     */
    public static final String CODE_MSG = "4";
}

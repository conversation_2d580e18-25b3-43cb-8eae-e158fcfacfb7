package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.user.User;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class TradeQimenUtils {
    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 构建接口响应结果
     * @param success
     * @param errorCode
     * @param errorMsg
     * @return
     */
    public static JSONObject buildResultObject(boolean success, Object errorCode, String errorMsg,JSONObject logisticResult) {
        JSONObject result = new JSONObject();
        result.put("ret", success);
        result.put("retCode", errorCode);
        result.put("retMsg", errorMsg);
        result.put("logisticResult",logisticResult);
        return result;
    }

    public static Integer getProcessCode(TbTrade tbTrade, TradeConfig tradeConfig){
        String sysStatus = TradeStatusUtils.convertSysStatus(tbTrade, tradeConfig);
        if (Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus)) {
            return 40;
        }
        if (Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(sysStatus)) {
            return 60;
        }
        if (Trade.SYS_STATUS_WAIT_PACKAGE.equals(sysStatus)) {
            return 90;
        }
        if (Trade.SYS_STATUS_WAIT_WEIGHT.equals(sysStatus) || Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(sysStatus)) {
            return 80;
        }
        return 1;
    }


    public static String checkConsignTime(Warehouse warehouse, LocalDateTime payTime){
        String payTimeDate = payTime.getYear() + "-" + payTime.getMonthValue() + "-" + payTime.getDayOfMonth();
        try {
            //如果仓库没有维护接单和发货时间，那么默认是取付款时间+48小时
            if (StringUtils.isBlank(warehouse.getLastPayTime()) || StringUtils.isBlank(warehouse.getLastDeliveryTime())) {
                return defaultDate(payTime);
            }
            String lastPayTime = warehouse.getLastPayTime();
            String lastDeliveryTime = warehouse.getLastDeliveryTime();
            int payHour = payTime.getHour();
            int payMinute = payTime.getMinute();

            String[] lastPayTimeStringarray = lastPayTime.split(":");
            int lastPayHour = Integer.parseInt(lastPayTimeStringarray[0]);
            int lastPayMinutes = lastPayTimeStringarray[1] != null ? Integer.parseInt(lastPayTimeStringarray[1]) : 0;

            String[] lastDeliveryTimeStringarray = lastDeliveryTime.split(":");
            int lastDeliveryHour = Integer.parseInt(lastDeliveryTimeStringarray[0]);
            int lastDeliverMinute = lastDeliveryTimeStringarray[1] != null ? Integer.parseInt(lastDeliveryTimeStringarray[1]) : 0;


            String lastDelivery = (lastDeliveryHour == 0 ? "00" :lastDeliveryHour) + ":" +(lastDeliverMinute== 0 ? "00" : lastDeliverMinute) + ":" + "00";
            //在付款时间范围内取最晚发货时间
            if (inclueLastPayTime(lastPayHour,payHour,lastPayMinutes,payMinute)) {
                return payTimeDate + " "  + lastDelivery;
            } else {
                String format = payTime.plusDays(1).format(dateFormatter);
                return format + " " + lastDelivery;
            }
        } catch (Exception e) {
            return defaultDate(payTime);
        }
    }

    private static boolean inclueLastPayTime(int lastPayHour,int payHour,int lastPayMinutes,int payMinute){
        if (lastPayHour - payHour > 0) {
            return true;
        }
        if (lastPayHour - payHour == 0) {
            if (lastPayMinutes - payMinute >= 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 默认最晚时间,48小时
     * @param payTime
     * @return
     */
    private static String defaultDate(LocalDateTime payTime){
        return payTime.plusHours(48).format(dateTimeFormatter);
    }


}

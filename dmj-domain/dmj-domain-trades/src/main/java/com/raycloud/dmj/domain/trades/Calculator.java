package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.PaymentLogBuilder;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.log4j.Logger;

import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.log4j.Logger;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-07-05 16:33
 */
public class Calculator {

    protected final Logger logger = Logger.getLogger(this.getClass());

    /**
     * 合单后的主单计算时是否需要查询隐藏订单下的所有子订单
     */
    private boolean queryMerge = true;

    /**
     * 是否计算订单商品数量与商品种类数量 itemNum,itemKindNum
     */
    private boolean calculateNum = true;

    /**
     * 是否计算订单异常状态 isExcep, isRefund
     */
    private boolean calculateExcep = true;

    /**
     * 是否计算订单净重 netWeight
     */
    private boolean calculateNetWeight = true;

    /**
     * 是否计算订单成本 cost
     */
    private boolean calculateCost = true;

    /**
     * 是否计算订单库存状态 stockStatus
     */
    private boolean calculateStock = true;

    /**
     * 是否计算订单体积 volume
     */
    private boolean calculateVolume = true;

    /**
     * 是否计算统一后的订单平台状态 unifiedStatus
     */
    private boolean calculatePlatStatus = true;

    /**
     * 是否计算订单的系统商家编码
     */
    private boolean calculateSysOuterId;

    private boolean calculatePayment = false;

    /**
     * 是否计算订单的分销金额
     */
    private boolean calculateSaleFee = true;

    /**
     * 是否计算source
     */
    private boolean calculateSource = false;

    /**
     * 是否计算SysStatus
     */
    private boolean calculateSysStatus = false;

    /**
     * 是否需要去查询数据库，校验数据的准确
     */
    private boolean queryDb = true;

    /**
     *订单实付金额变化
     */
    @Deprecated
    private boolean calculateTradePaymentChange;

    /**
     *订单实付金额不变
     */
    @Deprecated
    private boolean calculateTradePaymentConstant;

    /**
     * 是否开启金额联动
     */
    private boolean openPaymentLink;

    /**
     * (2, "平台订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     * (3, "系统订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     * (4, "系统订单、平台订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     */
    private Integer calculateOrderPayment;

    private TradeConfig tradeConfig;

    private String tradeSource;

    public Integer getCalculateOrderPayment() {
        return calculateOrderPayment;
    }

    public void setCalculateOrderPayment(Integer calculateOrderPayment) {
        this.calculateOrderPayment = calculateOrderPayment;
    }

    public Calculator(TradeConfig tradeConfig) {
        this.tradeConfig = tradeConfig;
    }

    public boolean isQueryMerge() {
        return queryMerge;
    }

    public Calculator setQueryMerge(boolean queryMerge) {
        this.queryMerge = queryMerge;
        return this;
    }

    public boolean isCalculateNum() {
        return calculateNum;
    }

    public Calculator setCalculateNum(boolean calculateNum) {
        this.calculateNum = calculateNum;
        return this;
    }

    public boolean isCalculateExcep() {
        return calculateExcep;
    }

    public Calculator setCalculateExcep(boolean calculateExcep) {
        this.calculateExcep = calculateExcep;
        return this;
    }

    public boolean isCalculateNetWeight() {
        return calculateNetWeight;
    }

    public Calculator setCalculateNetWeight(boolean calculateNetWeight) {
        this.calculateNetWeight = calculateNetWeight;
        return this;
    }

    public boolean isCalculateCost() {
        return calculateCost;
    }

    public Calculator setCalculateCost(boolean calculateCost) {
        this.calculateCost = calculateCost;
        return this;
    }

    public boolean isCalculateStock() {
        return calculateStock;
    }

    public Calculator setCalculateStock(boolean calculateStock) {
        this.calculateStock = calculateStock;
        return this;
    }

    public boolean isCalculateVolume() {
        return calculateVolume;
    }

    public Calculator setCalculateVolume(boolean calculateVolume) {
        this.calculateVolume = calculateVolume;
        return this;
    }

    public boolean isCalculatePlatStatus() {
        return calculatePlatStatus;
    }

    public Calculator setCalculatePlatStatus(boolean calculatePlatStatus) {
        this.calculatePlatStatus = calculatePlatStatus;
        return this;
    }


    public boolean isCalculateSysOuterId() {
        return calculateSysOuterId;
    }

    public Calculator setCalculateSysOuterId(boolean calculateSysOuterId) {
        this.calculateSysOuterId = calculateSysOuterId;
        return this;
    }

    public boolean isCalculatePayment() {
        return calculatePayment;
    }

    public Calculator setCalculatePayment(boolean calculatePayment) {
        this.calculatePayment = calculatePayment;
        return this;
    }

    public boolean isCalculateSaleFee() {
        return calculateSaleFee;
    }

    public void setCalculateSaleFee(boolean calculateSaleFee) {
        this.calculateSaleFee = calculateSaleFee;
    }

    public boolean isCalculateSource() {
        return calculateSource;
    }

    public Calculator setCalculateSource(boolean calculateSource) {
        this.calculateSource = calculateSource;
        return this;
    }

    public boolean isCalculateSysStatus() {
        return calculateSysStatus;
    }

    public Calculator setCalculateSysStatus(boolean calculateSysStatus) {
        this.calculateSysStatus = calculateSysStatus;
        return this;
    }

    public boolean isQueryDb() {
        return queryDb;
    }

    public Calculator setQueryDb(boolean queryDb) {
        this.queryDb = queryDb;
        return this;
    }

    public boolean isCalculateTradePaymentChange() {
        return calculateTradePaymentChange;
    }

    public Calculator setCalculateTradePaymentChange(boolean calculateTradePaymentChange) {
        this.calculateTradePaymentChange = calculateTradePaymentChange;
        return this;
    }

    public boolean isCalculateTradePaymentConstant() {
        return calculateTradePaymentConstant;
    }

    public Calculator setCalculateTradePaymentConstant(boolean calculateTradePaymentConstant) {
        this.calculateTradePaymentConstant = calculateTradePaymentConstant;
        return this;
    }

    public TradeConfig getTradeConfig() {
        return tradeConfig;
    }

    public void setTradeConfig(TradeConfig tradeConfig) {
        this.tradeConfig = tradeConfig;
    }

    public void calculate(Staff staff, Trade trade,boolean enableLog) {
        if (isCalculateCost()) {
            trade.setCost(TradeUtils.calculateCost(trade, true));
        }
        if (isCalculateVolume()) {
            trade.setVolume(TradeUtils.calculateVolume(trade));
        }
        if (isCalculateNum()) {
            TradeUtils.resetTradeItemNum(trade, tradeConfig);
        }
        if (isCalculateNetWeight()) {
            trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
        }
        if (isCalculateStock()) {
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
        }
        if (isCalculateExcep()) {
            TradeUtils.setTradeExcep(staff,trade);
        }
        if (isCalculatePlatStatus()) {
            trade.setUnifiedStatus(TradeStatusUtils.getUnifiedStatus(trade.getStatus()));
        }
        if (isCalculateSysOuterId()) {
            trade.setSysOuterId(TradeUtils.calculateOuterId(staff, trade));
        }
        if (isCalculatePayment()) {
            PaymentUtils.calculateUpdateTrade(trade);
        }
        if (isCalculateSaleFee() && !CommonConstants.PLAT_FORM_TYPE_FX.equals(trade.getSource())) {
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
        }
        if (isCalculateSource()) {
            trade.setSource(TradeUtils.getTradeSource(trade));
        }
        if (isCalculateSysStatus()) {
            trade.setSysStatus(TradeStatusUtils.getTradeSysStatus(trade));
        }
        //订单实付金额更改
        if (isCalculateTradePaymentChange()) {
            PaymentUtils.calculateUpdateTradeTotalFeeAndDiscountFee(trade, getCalculateOrderPayment(),enableLog);
        }
        //订单实付金额不变
        if (isCalculateTradePaymentConstant()) {
            PaymentUtils.calculateUpdateTradeTotalFeeAndDiscountFee(trade, getCalculateOrderPayment(),enableLog);
        }
        //开启订单金额联动
        if (isOpenPaymentLink()) {
            PaymentUtils.calculateUpdateTradeTotalFeeAndDiscountFee(trade, getCalculateOrderPayment(),enableLog);
        }
        /**
         * 店加 现货单的平台状态永远是已发货
         * 秦丝  未开启在erp发货订单  已发货
         */
        if(TradeUtils.isDianPlusXHTrade(trade) || TradeUtils.isQinsiNotConsignTrade(trade)){
            trade.setUnifiedStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
        }
    }

    /**
     * 通过 calculate(Trade trade)计算之后，获取更新信息
     *
     * @param trade
     * @return
     */
    public Trade getCalculateUpdate(Staff staff, Trade trade) {
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade);
        updateTrade.setSid(trade.getSid());
        updateTrade.setTagIds(trade.getTagIds());
        updateTrade.setOperations(trade.getOperations());
        if (isCalculateCost()) {
            updateTrade.setCost(trade.getCost());
        }

        if (isCalculateVolume()) {
            updateTrade.setVolume(trade.getVolume());
        }

        if (isCalculateNum()) {
            updateTrade.setItemNum(trade.getItemNum());
            updateTrade.setItemKindNum(trade.getItemKindNum());
        }

        if (isCalculateNetWeight()) {
            updateTrade.setNetWeight(trade.getNetWeight());
        }

        if (isCalculateStock()) {
         //   updateTrade.setStockStatus(trade.getStockStatus());
            updateTrade.setInsufficientNum(trade.getInsufficientNum());
            updateTrade.setInsufficientRate(trade.getInsufficientRate());
            TradeExceptUtils.setStockStatus(staff,updateTrade,trade.getStockStatus());
        }

        if (isCalculateExcep()) {
            updateTrade.setIsExcep(trade.getIsExcep());
            TradeExceptUtils.updateExcept(staff,updateTrade, ExceptEnum.HALT,TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.HALT));
        }

        if (isCalculatePlatStatus()) {
            updateTrade.setUnifiedStatus(trade.getUnifiedStatus());
        }


        PaymentLogBuilder logBuilder = new PaymentLogBuilder(trade).append("Calculator.getCalculateUpdate ");

        if (isCalculatePayment()) {
            updateTrade.setPayment(trade.getPayment());
            updateTrade.setTotalFee(trade.getTotalFee());
            logBuilder.append("calculatePayment",calculatePayment)
                    .appendChanged("payment",updateTrade.getPayment(),trade.getPayment())
                    .appendChanged("totalFee",updateTrade.getTotalFee(),trade.getTotalFee());
        }

        if (isCalculateSaleFee()) {
            updateTrade.setSaleFee(trade.getSaleFee());
            logBuilder.append("calculateSaleFee",calculateSaleFee)
                    .appendChanged("saleFee",updateTrade.getSaleFee(),trade.getSaleFee());
        }

        //订单实付金额更改
        if (isCalculateTradePaymentChange()) {
            updateTrade.setPayment(trade.getPayment());
            updateTrade.setTotalFee(trade.getTotalFee());
            logBuilder.append("calculateTradePaymentChange",calculateTradePaymentChange)
                    .appendChanged("payment",updateTrade.getPayment(),trade.getPayment())
                    .appendChanged("totalFee",updateTrade.getTotalFee(),trade.getTotalFee());
        }
        //订单实付金额不变
        if (isCalculateTradePaymentConstant()) {
            updateTrade.setDiscountFee(trade.getDiscountFee());
            updateTrade.setTotalFee(trade.getTotalFee());
            logBuilder.append("calculateTradePaymentConstant",calculateTradePaymentConstant)
                    .appendChanged("discountFee",updateTrade.getDiscountFee(),trade.getDiscountFee())
                    .appendChanged("totalFee",updateTrade.getTotalFee(),trade.getTotalFee());
        }

        //开启金额联动配置
        if (isOpenPaymentLink()) {
            updateTrade.setDiscountFee(trade.getDiscountFee());
            updateTrade.setTotalFee(trade.getTotalFee());
            updateTrade.setPayment(trade.getPayment());

            logBuilder.append("openPaymentLink",openPaymentLink)
                    .appendChanged("discountFee",updateTrade.getDiscountFee(),trade.getDiscountFee())
                    .appendChanged("totalFee",updateTrade.getTotalFee(),trade.getTotalFee())
                    .appendChanged("payment",updateTrade.getPayment(),trade.getPayment());
        }

        if (logger.isDebugEnabled()) {
            logger.debug(logBuilder.toString());
        }

        return updateTrade;
    }


    public void calculate(Staff staff, Trade trade, Trade toUpdate,boolean enableLog) {
        if (toUpdate == null) {
            calculate(staff, trade,enableLog);
            return;
        }
        Double originValue;
        if (isCalculateCost()) {
            originValue = trade.getCost();
            trade.setCost(TradeUtils.calculateCost(trade));
            if (originValue == null || originValue - trade.getCost() != 0) {//有变化才更新
                toUpdate.setCost(trade.getCost());
            }
        }
        if (isCalculateVolume()) {
            originValue = trade.getVolume();
            trade.setVolume(TradeUtils.calculateVolume(trade));
            if (originValue == null || originValue - trade.getVolume() != 0) {
                toUpdate.setVolume(trade.getVolume());
            }
        }
        if (isCalculateNum()) {
            Integer originNum = trade.getItemNum();
            Integer originKindNum = trade.getItemKindNum();
            TradeUtils.resetTradeItemNum(trade, tradeConfig);
            if (originNum == null || originNum - trade.getItemNum() != 0) {
                toUpdate.setItemNum(trade.getItemNum());
            }
            if (originKindNum == null || originKindNum - trade.getItemKindNum() != 0) {
                toUpdate.setItemKindNum(trade.getItemKindNum());
            }
        }
        if (isCalculateNetWeight()) {
            originValue = trade.getNetWeight();
            trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
            if (originValue == null || originValue - trade.getNetWeight() != 0) {
                toUpdate.setNetWeight(trade.getNetWeight());
            }
        }
        if (isCalculateStock()) {
            String originStockStatus = trade.getStockStatus();
            Integer oldInsufficientNum = trade.getInsufficientNum();
            Double oldInsufficientRate = trade.getInsufficientRate();
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
            if (oldInsufficientNum - trade.getInsufficientNum() != 0) {
                toUpdate.setInsufficientNum(trade.getInsufficientNum());
            }
            if (oldInsufficientRate - trade.getInsufficientRate() != 0) {
                toUpdate.setInsufficientRate(trade.getInsufficientRate());
            }
            if (!trade.getStockStatus().equals(originStockStatus)) {
                // toUpdate.setStockStatus(trade.getStockStatus());
                TradeExceptUtils.setStockStatus(staff,toUpdate,trade.getStockStatus());
            }
        }
        if (isCalculateExcep()) {
            TradeUtils.setTradeExcep(staff,trade, toUpdate);
        }
        if (isCalculatePlatStatus()) {
            trade.setUnifiedStatus(TradeStatusUtils.getUnifiedStatus(trade.getStatus()));
            toUpdate.setUnifiedStatus(trade.getUnifiedStatus());
        }
        if (isCalculateSysOuterId()) {
            trade.setSysOuterId(TradeUtils.calculateOuterId(staff, trade));
            toUpdate.setSysOuterId(trade.getSysOuterId());
        }
    }

    public static void calculate4Match(Staff staff, Trade trade, TradeConfig config) {
        trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
        trade.setCost(TradeUtils.calculateCost(trade));
        trade.setVolume(TradeUtils.calculateVolume(trade));
        if (!CommonConstants.PLAT_FORM_TYPE_FX.equals(trade.getSource())) {
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
        }
        TradeStockUtils.resetTradeStockStatus(staff, trade, config);
        TradeUtils.setTradeExcep(staff,trade);
        PaymentUtils.calculateTrade(trade);
    }

    public String getTradeSource() {
        return tradeSource;
    }

    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    public boolean isOpenPaymentLink() {
        return openPaymentLink;
    }

    public void setOpenPaymentLink(boolean openPaymentLink) {
        this.openPaymentLink = openPaymentLink;
    }
}

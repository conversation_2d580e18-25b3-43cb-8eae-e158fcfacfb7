package com.raycloud.dmj.domain.trades.utils;


import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 4/15/15. Time: 11:16 AM Information:
 */

public class DmjSkuUtils {

  public static String[] formatProprotyNameArray(String propertiesName){
    if (StringUtils.isEmpty(propertiesName)) return  new String[]{};

    String[] wapper= StringUtils.split(propertiesName, ";");
    for (int i = 0; i <wapper.length ; i++) {
      String element=wapper[i];
      String[] _element=StringUtils.split(element, ":");
      if (_element.length==0) return new String[]{};
      else if (_element.length==2) element=_element[1];
      else{
        List<String> newElement=new LinkedList<String>();
        int left=_element.length/2;
        newElement.addAll(Arrays.asList(_element).subList(left, _element.length));
        element=StringUtils.join(newElement,":");
      }
      wapper[i]=element;
    }
    return wapper;
  }

  public static String formatProprotyName(String propertiesName,String spilit){
    return StringUtils.join(formatProprotyNameArray(propertiesName),spilit);
  }

}

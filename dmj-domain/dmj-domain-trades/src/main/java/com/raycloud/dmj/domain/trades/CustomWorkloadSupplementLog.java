package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> xubin
 * @desc:   自定义绩效日志表
 * @date : 6/7/22  1:54 PM
 */
@Table(name = "custom_workload_supplement_log", routerKey = "customWorkloadSupplementDbNo")
@Data
public class CustomWorkloadSupplementLog extends Model implements Serializable{


    private static final long serialVersionUID = 1271108186456695422L;

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 订单号
     */
    private Long sid;


    /**
     * 单据类型 1 订单 2 加工单
     */
    private Integer busyType;


    /**
     * 单据号
     */
    private String busyCode;


    /**
     * 自定义Id
     */
    private Long customId;

    /**
     * 自定义名称
     */
    private String customName;

    /**
     * 商品数
     */
    private Integer itemNum;

    /**
     * 商品数
     */
    private Integer itemKindNum;

    /**
     * 扫描类型
     */
    private Integer scanType;

    /**
     * 绩效员工id
     */
    private Long staffId;

    /**
     * 绩效员工名称
     */
    private String staffName;

    /**
     * 补入人员id
     */
    private Long createId;

    /**
     * 补入人员名称
     */
    private String createName;

    /**
     * 有效标记 0-无效 1-有效
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;
}

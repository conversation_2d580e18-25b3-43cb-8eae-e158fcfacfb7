package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

public class TradeQueryNoFilterParams implements Serializable {
    private static final long serialVersionUID = 5431521212236302262L;

    private Boolean showDetail;

    private Boolean needFill;

    private Boolean needFillExt;

    public Boolean getShowDetail() {
        return showDetail;
    }

    public void setShowDetail(Boolean showDetail) {
        this.showDetail = showDetail;
    }

    public Boolean getNeedFill() {
        return needFill;
    }

    public void setNeedFill(Boolean needFill) {
        this.needFill = needFill;
    }

    public Boolean getNeedFillExt() {
        return needFillExt;
    }

    public void setNeedFillExt(Boolean needFillExt) {
        this.needFillExt = needFillExt;
    }
}

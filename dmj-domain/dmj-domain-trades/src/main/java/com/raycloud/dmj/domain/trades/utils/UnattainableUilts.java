package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/5/17 7:53 下午
 * @Description:
 */
public class UnattainableUilts {
    public static final String eventName = "trade.excep.unattainable";

    /**
     * 快递异常不锁库存
     *
     * @param trade
     * @return
     */
    public static Boolean isUnattainableNotApplyStockTrade(Staff staff,Trade trade, TradeConfigNew tradeConfigNew) {
        if (Objects.equals(tradeConfigNew.getConfigValue(), "1") && isAutoMatchUnattainable(staff,trade)) {
            return true;
        }
        return false;
    }


    public static Boolean isAutoMatchUnattainableTrade(Staff staff,TradeConfig tradeConfig, Trade trade) {
        if (tradeConfig != null && isAutoMatchUnattainable(staff,trade) && Objects.equals(tradeConfig.getAutoUnattainableNotApplyStock(), 1)) {
            return true;
        }
        return false;
    }

    public static Boolean isAutoMatchUnattainable(Staff staff,Trade trade) {
       /* Integer unattainable = trade.getUnattainable();
        return unattainable != null && unattainable == 1;*/
        return TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNATTAINABLE) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, ExceptEnum.UNATTAINABLE);
    }


    public static void setOrderAutoUnattainable(Staff staff,Trade trade, Order order) {
        order.setAutoUnattainable(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)?1:0);
        order.setExceptData(trade.getExceptData());
        List<Order> suits = order.getSuits();
        if (CollectionUtils.isNotEmpty(suits)) {
            for (Order suit : suits) {
                suit.setAutoUnattainable(order.getAutoUnattainable());
                suit.setExceptData(order.getExceptData());
            }
        }
    }

    public static void setOrderListAutoUnattainable(Staff staff,Trade trade, List<Order> orders) {
        for (Order order : orders) {
            setOrderAutoUnattainable(staff,trade, order);
        }
    }

    public static Boolean isAutoMatchUnattainableOrder(Order order, Integer autoUnattainableNotApplyStock) {
        if ( Objects.equals(autoUnattainableNotApplyStock, 1) && Objects.equals(order.getAutoUnattainable(), 1)) {
            return true;
        }
        return false;
    }

    public static boolean isAutoMatchUnattainableOrder(Order order, int autoUnattainableNotApplyStock) {
        return autoUnattainableNotApplyStock == 1 && Objects.equals(order.getAutoUnattainable(), 1);
    }

    public static void setTradeListAutoUnattainable(Staff staff,List<Trade> trades) {
        handleMergeTradeUnattainable(staff,trades);
        for (Trade trade : trades) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            setOrderListAutoUnattainable(staff,trade, orders4Trade);
        }
    }


    /**
     * 合单任意一单快递异常，其他子单均快递异常
     *
     * @param trades
     */
    public static <T extends Trade> void handleMergeTradeUnattainable(Staff staff,List<T> trades) {
        Map<Long, List<Trade>> collect = trades.stream().filter(e -> e.getMergeSid() > 0).collect(Collectors.groupingBy(Trade::getMergeSid));
        if (MapUtils.isEmpty(collect)) {
            return;
        }
        Set<Long> sids = collect.keySet();
        List<Long> unattainableSids = new ArrayList<>();
        for (Long sid : sids) {
            List<Trade> tradeList = collect.get(sid);
            if (tradeList.size() <= 1) {
                continue;
            }
            long count = tradeList.stream().filter(trade -> TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)).count();
            // count 大于0 合单中存在快递异常的
            if (count > 0) {
                List<Long> sidList = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
                unattainableSids.addAll(sidList);
            }
        }
        for (Trade trade : trades) {
            if (!TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE) && unattainableSids.contains(trade.getSid())) {
                TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.UNATTAINABLE,1L);
            }
        }
    }

    public static void setOrderUnattainableAndScalping(Staff staff,Trade trade, Order order) {
        setOrderAutoUnattainable(staff,trade, order);
        order.setScalping(trade.getScalping());
        List<Order> suits = order.getSuits();
        if (CollectionUtils.isNotEmpty(suits)) {
            for (Order suit : suits) {
                suit.setScalping(order.getScalping());
            }
        }
    }


    public static <T extends Trade> List<String> buildLog(Staff staff, List<T> trades) {
        Map<Long, List<Trade>> mergeTrades = trades.stream().filter(e -> e.getMergeSid() > 0).collect(Collectors.groupingBy(Trade::getMergeSid));
        List<Trade> tradeList = trades.stream().filter(e -> e.getMergeSid() < 0).collect(Collectors.toList());
        List<String> logs = new ArrayList<>();
        for (Trade trade : tradeList) {
            StringBuilder sb = new StringBuilder();
            sb.append(",sid=").append(trade.getSid()).append(",scalping=").append(trade.getScalping()).append(",isRefund=").append(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.REFUNDING));
            sb.append(",autoUnattainable=").append(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)?1:0);
            logs.add(sb.toString());
        }
        Set<Map.Entry<Long, List<Trade>>> entries = mergeTrades.entrySet();
        for (Map.Entry<Long, List<Trade>> entry : entries) {
            List<Trade> value = entry.getValue();
            StringBuilder sb = new StringBuilder();
            for (Trade trade : value) {
                sb.append(",sid=").append(trade.getSid()).append(",scalping=").append(trade.getScalping()).append(",isRefund=").append(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.REFUNDING));
                sb.append(",autoUnattainable=").append(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)?1:0);
            }
            logs.add(sb.toString());
        }
        return logs;
    }
}

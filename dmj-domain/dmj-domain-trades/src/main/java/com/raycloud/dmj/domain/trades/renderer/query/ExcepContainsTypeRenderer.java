package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class ExcepContainsTypeRenderer implements IFieldValueRenderer {

    /**
     * 1：仅包含，2：排除，3：同时包含； 4：包含
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,1)) {
            return "仅包含";
        }
        if (Objects.equals(originValue,2)) {
            return "排除";
        }
        if (Objects.equals(originValue,3)) {
            return "同时包含";
        }
        if (Objects.equals(originValue,4)) {
            return "包含";
        }
        return String.valueOf(originValue);
    }
}

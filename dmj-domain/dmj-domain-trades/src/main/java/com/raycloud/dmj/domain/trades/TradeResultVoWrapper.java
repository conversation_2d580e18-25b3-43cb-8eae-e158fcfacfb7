package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by windy26205 on 18/11/23.
 */
public class TradeResultVoWrapper implements Serializable{

    private static final long serialVersionUID = 1L;

    private List<TradeResultVo> errorResult;

    private List<TradeResultVo> successResult;

    public List<TradeResultVo> getErrorResult() {
        return errorResult;
    }

    public void setErrorResult(List<TradeResultVo> errorResult) {
        this.errorResult = errorResult;
    }

    public List<TradeResultVo> getSuccessResult() {
        return successResult;
    }

    public void setSuccessResult(List<TradeResultVo> successResult) {
        this.successResult = successResult;
    }

    public TradeResultVoWrapper() {

    }


    public TradeResultVoWrapper(List<TradeResultVo> errorResult, List<TradeResultVo> successResult) {
        this.errorResult = errorResult;
        this.successResult = successResult;
    }

    public static TradeResultVoWrapper models2Vo(Collection<TradeResult> list) {
        if(null == list || list.size() <=0){
            return new TradeResultVoWrapper();
        }else{
            List<TradeResultVo> successList = new ArrayList<>();
            List<TradeResultVo> errorList = new ArrayList<>();
            TradeResultVoWrapper tradeResultVoWrapper = new TradeResultVoWrapper(errorList,successList);
            for(TradeResult tradeResult : list){
                if(tradeResult.getSuccess() != null && tradeResult.getSuccess()){
                    successList.add(TradeResultVo.model2Vo(tradeResult));
                }else{
                    errorList.add(TradeResultVo.model2Vo(tradeResult));
                }
            }
            return tradeResultVoWrapper;
        }
    }
}

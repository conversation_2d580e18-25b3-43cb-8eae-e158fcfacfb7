package com.raycloud.dmj.domain.trades.params;


import com.raycloud.dmj.domain.Page;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 线下组包大包管理查询
* @Date 14:56 2022/4/28
**/
public class OfflineCombineParcelRequest implements Serializable {

    private Long combineParcelId;

    private String combineParcelIds;

    private Integer status;

    private String parcelTrackingNo;

    private Long startTime;

    private Long endTime;

    private Date createStartTime;

    private Date createEndTime;

    private Page page;

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getParcelTrackingNo() {
        return parcelTrackingNo;
    }

    public void setParcelTrackingNo(String parcelTrackingNo) {
        this.parcelTrackingNo = parcelTrackingNo;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Date getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(Date createStartTime) {
        this.createStartTime = createStartTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getCombineParcelIds() {
        return combineParcelIds;
    }

    public void setCombineParcelIds(String combineParcelIds) {
        this.combineParcelIds = combineParcelIds;
    }
}

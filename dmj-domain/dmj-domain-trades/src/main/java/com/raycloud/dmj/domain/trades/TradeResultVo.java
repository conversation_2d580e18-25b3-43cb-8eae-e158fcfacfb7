package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-12-20 12:06
 */
public class TradeResultVo implements Serializable {

    private static final long serialVersionUID = 4635811651836100665L;

    private String sid;

    private String mergeSid;

    private Boolean success;

    private String errorMsg;

    private String sysStatus;

    private String destId;

    private Integer isHandlerMemo;

    private Integer isHandlerMessage;

    public Integer getIsHandlerMemo() {
        return isHandlerMemo;
    }

    public void setIsHandlerMemo(Integer isHandlerMemo) {
        this.isHandlerMemo = isHandlerMemo;
    }

    public Integer getIsHandlerMessage() {
        return isHandlerMessage;
    }

    public void setIsHandlerMessage(Integer isHandlerMessage) {
        this.isHandlerMessage = isHandlerMessage;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getMergeSid() {
        return mergeSid;
    }

    public void setMergeSid(String mergeSid) {
        this.mergeSid = mergeSid;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getDestId() {
        return destId;
    }

    public void setDestId(String destId) {
        this.destId = destId;
    }

    public static TradeResultVo model2Vo(TradeResult result) {
        TradeResultVo vo = new TradeResultVo();
        vo.setSid(result.getSid().toString());
        if(result.getDestId() != null){
            vo.setDestId(String.valueOf(result.getDestId()));
        }
        if (result.getMergeSid() != null) {
            vo.setMergeSid(result.getMergeSid().toString());
        }
        vo.setSuccess(result.getSuccess());
        vo.setErrorMsg(result.getErrorMsg());
        vo.setSysStatus(result.getSysStatus());
        vo.setIsHandlerMemo(result.getIsHandlerMemo());
        vo.setIsHandlerMessage(result.getIsHandlerMessage());
        return vo;
    }

    public static List<TradeResultVo> models2VoList(Collection<TradeResult> list) {
        List<TradeResultVo> voList = new ArrayList<>(list.size());
        for (TradeResult model : list) {
            voList.add(model2Vo(model));
        }
        return voList;
    }
}

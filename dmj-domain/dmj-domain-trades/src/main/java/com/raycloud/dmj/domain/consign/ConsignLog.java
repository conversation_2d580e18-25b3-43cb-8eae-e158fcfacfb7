package com.raycloud.dmj.domain.consign;


import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.ConsignRecord;
import com.raycloud.dmj.domain.user.User;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统发货日志类
 * 每笔订单系统发货完成后，会创建一个该类的发货日志输出到kibana中，供发货统计使用
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-16 14:11
 */
public class ConsignLog implements Serializable {

    private static final long serialVersionUID = 1930628448981738876L;

    /**
     * 用于标记上传发货失败日志打印, 不提供对外set方法
     */
    private String logName = "上传发货失败日志";

    /**
     * 系统订单号
     */
    private Long sid;
    /**
     * 平台订单号
     */
    private String tid;
    /**
     * 发货时使用的快递单号
     */
    private String outSid;
    /**
     * 快递模版ID_快递模版类型
     */
    private String template;
    /**
     * 发货（上传）时间
     */
    private Date consignTime;
    /**
     * 发货类型
     */
    private String type;
    /**
     * 发货、上传标志： 1 系统发货，20 上传发货失败，21 上传发货成功
     */
    private Integer flag;
    /**
     * 平台上传耗时
     */
    private Long took;
    /**
     * 平台上传失败的错误信息
     */
    private String error;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 公司名
     */
    private String companyName;
    /**
     * 操作员ID
     */
    private Long staffId;
    /**
     * 操作员姓名
     */
    private String staffName;
    /**
     * 店铺系统ID
     */
    private Long userId;
    /**
     * 店铺平台ID
     */
    private Long taobaoId;
    /**
     * 店铺昵称
     */
    private String nick;
    /**
     * 订单所属平台
     */
    private String platform;

    /**
     * 本次发货的orderIds
     */
    private String orderIds;

    private Integer tradeType;

    private String source;

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Long getTook() {
        return took;
    }

    public void setTook(Long took) {
        this.took = took;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(String orderIds) {
        this.orderIds = orderIds;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public static ConsignLog buildConsignLog(Staff staff, ConsignRecord record, Long took) {
        ConsignLog log = new ConsignLog();
        log.setSid(record.getSid());
        log.setTid(record.getTid());
        log.setOutSid(record.getOutSid());
        log.setTemplate(buildTemplate(record.getTemplateId(), record.getTemplateType(), record.getTemplateName()));
        log.setConsignTime(record.getConsigned());
        log.setType(record.getConsignType());
        log.setFlag(record.getIsError() != null && record.getIsError() == 0 ? 1 : 0);
        log.setError(record.getErrorDesc());
        log.setSource(record.getSource());
        log.setTook(took);
        log.setCompanyId(staff.getCompanyId());
        log.setCompanyName(staff.getCompanyName());
        log.setTradeType(record.getTradeType());
        if (staff.getCompany() != null) {
            log.setCompanyName(staff.getCompany().getName());
        }
        log.setStaffId(staff.getId());
        log.setStaffName(staff.getName());
        log.setUserId(record.getUserId());
        log.setPlatform(record.getSource());
        log.setOrderIds(record.getOrderIds());
        if (record.getUserId() != null && record.getUserId() > 0) {
            User user = staff.getUserByUserId(record.getUserId());
            if (user != null) {
                log.setTaobaoId(user.getTaobaoId());
                log.setNick(user.getNick());
                if (log.getPlatform() == null) {
                    log.setPlatform(user.getSource());
                }
            }
        }
        return log;
    }

    public static ConsignLog buildUploadLog(User user, ConsignRecord record, Long took) {
        ConsignLog log = new ConsignLog();
        log.setSid(record.getSid());
        log.setTid(record.getTid());
        log.setOutSid(record.getOutSid());
        log.setTemplate(buildTemplate(record.getTemplateId(), record.getTemplateType(), record.getTemplateName()));
        log.setConsignTime(record.getUploadTime());
        log.setType(record.getConsignType());
        log.setFlag(record.getIsError() != null && record.getIsError() - 1 == 0 ? 20 : 21);
        log.setTook(took);
        log.setSource(user.getSource());
        log.setError(record.getErrorDesc());
        log.setCompanyId(user.getCompanyId());
        if (user.getStaff() != null) {
            log.setCompanyName(user.getStaff().getCompanyName());
            if (user.getStaff().getCompany() != null) {
                log.setCompanyName(user.getStaff().getCompany().getName());
            }
            log.setStaffId(user.getStaff().getId());
            log.setStaffName(user.getStaff().getName());
        }
        log.setUserId(user.getId());
        log.setTaobaoId(user.getTaobaoId());
        log.setNick(user.getNick());
        log.setPlatform(record.getSource() != null ? record.getSource() : user.getSource());
        return log;
    }

    private static String buildTemplate(Long templateId, Integer templateType, String templateName) {
        StringBuilder buf = new StringBuilder();
        buf.append(templateId);
        if (templateType != null) {
            buf.append("_").append(templateType);
        }
        if (templateName != null) {
            buf.append("_").append(templateName);
        }
        return buf.toString();
    }


    /**
     * 必须实现toString方法  否则日志打印为空
     * @return
     */
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConsignLog{");
        sb.append("logName='").append(logName).append('\'');
        sb.append(", sid=").append(sid);
        sb.append(", tid='").append(tid).append('\'');
        sb.append(", outSid='").append(outSid).append('\'');
        sb.append(", template='").append(template).append('\'');
        sb.append(", consignTime=").append(consignTime);
        sb.append(", type='").append(type).append('\'');
        sb.append(", flag=").append(flag);
        sb.append(", took=").append(took);
        sb.append(", error='").append(error).append('\'');
        sb.append(", companyId=").append(companyId);
        sb.append(", companyName='").append(companyName).append('\'');
        sb.append(", staffId=").append(staffId);
        sb.append(", source=").append(source);
        sb.append(", staffName='").append(staffName).append('\'');
        sb.append(", userId=").append(userId);
        sb.append(", taobaoId=").append(taobaoId);
        sb.append(", nick='").append(nick).append('\'');
        sb.append(", platform='").append(platform).append('\'');
        sb.append('}');
        return sb.toString();
    }
}

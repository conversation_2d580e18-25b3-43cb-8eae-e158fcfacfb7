package com.raycloud.dmj.domain.trades;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by windy26205 on 19/10/31.
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExtJson {

    String proName();

    boolean isExtJsonPro() default false;


}

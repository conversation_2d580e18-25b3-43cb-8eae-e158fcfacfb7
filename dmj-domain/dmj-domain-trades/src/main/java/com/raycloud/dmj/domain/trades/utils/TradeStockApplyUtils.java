package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.except.enums.ExceptEnum;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/12/9 3:37 下午
 * @Description:
 */
public class TradeStockApplyUtils {


    public static boolean needNotApplyStock(Staff staff, Order order, Integer openScalpNotApplyStock, Integer openRefundNotApplyStock, Integer autoUnattainableNotApplyStock) {
        boolean notApplyStock = ((order.getItemSysId() != null && order.getItemSysId() > 0) || !OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED))
                && (order.isVirtual() || order.ifNonConsign() || OrderUtils.isFxOrMixOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order) || scalpNotApplyStock(order, openScalpNotApplyStock) || refundNotApplyStock(order, openRefundNotApplyStock) || OrderUtils.hasFblOrder(order)
                || UnattainableUilts.isAutoMatchUnattainableOrder(order, autoUnattainableNotApplyStock));
        //组合装、加工商品 的单品不申请库存
        if (!notApplyStock) {
            notApplyStock = order.getType() != null && order.getCombineId() != null && order.getCombineId() > 0 && (order.getType() == Order.TypeOfGroupOrder || order.getType() == Order.TypeOfProcessOrder);
        }
        if (notApplyStock) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return notApplyStock;
            }
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setStockNum(order.getNum());
        }
        return notApplyStock;
    }

    private static boolean scalpNotApplyStock(Order order, Integer openScalpNotApplyStock) {
        return openScalpNotApplyStock != null && order.getScalping() != null && order.getScalping() == 1 && openScalpNotApplyStock == 1;
    }


    private static boolean refundNotApplyStock(Order order, Integer openRefundNotApplyStock) {
        return openRefundNotApplyStock != null && RefundUtils.isRefundOrder(order) && openRefundNotApplyStock == 1;
    }
}

package com.raycloud.dmj.domain.trades.search;

import com.raycloud.dmj.domain.Page;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>(杨保平)
 * @Date: 2022/6/29 5:54 下午
 * @Version 1.0
 * @Email <EMAIL>
 */
@Data
@Builder
public class ItemQueryParams {

    /**
     * 商品查询参数
     */
    private List<ItemParams> itemParams;
    /**
     * 是否匹配
     */
    private boolean match;
    /**
     * 查询字段
     */
    private List<String> field;
    private Page page;
}

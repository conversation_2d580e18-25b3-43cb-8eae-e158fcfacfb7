package com.raycloud.dmj.domain.consign;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * ConsignCacheArgs
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
public class ConsignCacheRetryArgs implements Serializable {
    /**
     * 是否异常发货
     */
    private Boolean exceptConsign;

    /**
     * 发货类型
     */
    private String consignType;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 无需物流发货发货类型
     */
    private Integer dummyType;

    /**
     * 无需物流发货-配送人员
     */
    private String noLogisticsName;

    /**
     * 联系电话
     */
    private String noLogisticsTel;

    /**
     * 当前操作人id
     */
    private Long staffId;


    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (object == null || getClass() != object.getClass()) return false;
        ConsignCacheRetryArgs that = (ConsignCacheRetryArgs) object;
        return Objects.equals(exceptConsign, that.exceptConsign)
                && Objects.equals(consignType, that.consignType)
                && Objects.equals(clientIp, that.clientIp)
                && Objects.equals(dummyType, that.dummyType)
                && Objects.equals(noLogisticsName, that.noLogisticsName)
                && Objects.equals(noLogisticsTel, that.noLogisticsTel)
                && Objects.equals(staffId, that.staffId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(exceptConsign, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, staffId);
    }
}

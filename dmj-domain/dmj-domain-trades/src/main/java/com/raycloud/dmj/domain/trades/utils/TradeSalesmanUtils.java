package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeSalesman;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/6
 */
public class TradeSalesmanUtils {

    public static List<TradeSalesman> convertSalesmanList(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return Collections.emptyList();
        }
        List<TradeSalesman> tradeSalesmanList = new ArrayList<>();
        for (Trade trade : trades) {
            TradeSalesman tradeSalesman = new TradeSalesman();
            tradeSalesman.setSid(trade.getSid());
            tradeSalesman.setSalesmanId(trade.getSalesmanId());
            tradeSalesman.setEnableStatus(1);
            tradeSalesmanList.add(tradeSalesman);
        }
        return tradeSalesmanList;
    }

    public static List<TradeSalesman> buildSalesmanList(Staff staff, List<Trade> insertTradeList, List<Trade> deleteTradeList) {
        List<TradeSalesman> tradeSalesmanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(insertTradeList)) {
            for (Trade trade : insertTradeList) {
                if(trade.getSalesmanId() != null && trade.getSalesmanId() > 0){
                    TradeSalesman tradeSalesman = new TradeSalesman();
                    tradeSalesman.setSid(trade.getSid());
                    // 没有匹配上或者没有填业务员ID的时候 默认取当前登陆用户的ID
                    tradeSalesman.setSalesmanId(trade.getSalesmanId());
                    tradeSalesman.setEnableStatus(1);
                    tradeSalesmanList.add(tradeSalesman);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(deleteTradeList)) {
            deleteTradeList.forEach(trade -> {
                TradeSalesman tradeSalesman = new TradeSalesman();
                tradeSalesman.setSid(trade.getSid());
                tradeSalesman.setSalesmanId(trade.getSalesmanId());
                tradeSalesman.setEnableStatus(0);
                tradeSalesmanList.add(tradeSalesman);
            });
        }
        return tradeSalesmanList;
    }

    public static TradeSalesman buildSalesman(Staff staff, Trade trade) {
        TradeSalesman tradeSalesman = new TradeSalesman();
        tradeSalesman.setSid(trade.getSid());
        tradeSalesman.setSalesmanId(NumberUtils.nvlInteger(trade.getSalesmanId()) > 0 ? trade.getSalesmanId() : staff.getId());
        tradeSalesman.setEnableStatus(1);
        return tradeSalesman;
    }

    public static List<TradeSalesman> buildSalesmanList(Staff staff, Collection<Trade> trades) {
        List<TradeSalesman> tradeSalesmanList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                tradeSalesmanList.add(buildSalesman(staff, trade));
            }
        }
        return tradeSalesmanList;
    }
}

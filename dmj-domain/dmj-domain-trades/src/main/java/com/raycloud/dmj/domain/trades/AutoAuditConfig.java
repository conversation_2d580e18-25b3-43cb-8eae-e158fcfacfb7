package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 自动审核配置
 * <AUTHOR>
 *
 */
@Table(name = "auto_audit_config")
@Setter
@Getter
public class AutoAuditConfig extends TradeRule {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4421284826644310379L;
	
	/**
	 * 是否为或关系
	 */
	private Integer isAny = 0;

	/**
	 * 付款时间多少分钟之前的订单进行自动审核
	 */
	private Integer beforePayTime;

	/**
	 * 审核的时间段-开始时间
	 * */
	private String excludeStart;

	/**
	 * 审核的时间段-结束时间
	 * */
	private String excludeEnd;

	private List<SpelCondition> conditions;
	
	private String userIdStr;
	private List<Long> userIds;
	
	/**
	 * 过滤条件描述
	 */
	private String conditionDesc;
	
	/**
	 * 店铺名称
	 */
	private String shopNames;

	private Integer index;

	/**
	 * 是否审核系统订单 1为审核  0为不审核 默认为0
	 */
	private Integer auditSysTrade;


	private String brandIds;
    private String supplierIds;

    private String templateIds;

    /**
     * 商品品牌Name
     */
    private  List<Brand> brandList;

    /**
     * 供销商Name
     */
    private List<Supplier>  supplierList;

    private List<Map> templateList;

    /**
     * 0，正常店铺规则 1，分销店铺规则
     */
    private Integer ruleType;

	/**
	 * 重新审核订单
	 */
	private Integer allowReAutoAudit;

	/**
	 * 仓库编码，多个以逗号分割
	 */
	private String warehouseIds;

	/**
	 * 仓库信息
	 */
	private List<Warehouse> warehouseList;

	public List<SpelCondition> getConditions() {
		if(null == conditions)
			conditions = new ArrayList<>();
		return conditions;
	}

	public List<Long> getUserIds() {
		if(null == userIds)
			userIds = new ArrayList<>();
		return userIds;
	}

}

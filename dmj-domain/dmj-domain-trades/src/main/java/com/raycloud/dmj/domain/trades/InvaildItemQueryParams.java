package com.raycloud.dmj.domain.trades;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_08_10 10:13
 */
public class InvaildItemQueryParams {

    private String invaildUserIds;
    private String invaildQueryType;
    private String invaildQueryText;
    private Integer platform=1;
    private Integer isAccurate=1;

    public String getInvaildUserIds() {
        return invaildUserIds;
    }

    public void setInvaildUserIds(String invaildUserIds) {
        this.invaildUserIds = invaildUserIds;
    }

    public String getInvaildQueryType() {
        return invaildQueryType;
    }

    public void setInvaildQueryType(String invaildQueryType) {
        this.invaildQueryType = invaildQueryType;
    }

    public String getInvaildQueryText() {
        return invaildQueryText;
    }

    public void setInvaildQueryText(String invaildQueryText) {
        this.invaildQueryText = invaildQueryText;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getIsAccurate() {
        return isAccurate;
    }

    public void setIsAccurate(Integer isAccurate) {
        this.isAccurate = isAccurate;
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-05-14 19:08
 * @Description 拆分发货上传的数据
 */
public class SplitConsignUploadData implements Serializable {


    /**
     * 系统单号是否上传
     */
    private boolean sid;

    /**
     * 内部订单号是否上传
     */
    private boolean shortId;

    /**
     * 系统发货时间是否上传 格式： yyyy-mm-dd hh:mm:ss | yyyy-mm-dd | mm-dd
     */
    private String consignTime;

    /**
     * 快递公司是否上传
     */
    private boolean express;

    /**
     * 快递单号是否上传
     */
    private boolean outSid;

    /**
     * 商家编码是否上传
     */
    private boolean outerId;

    /**
     * 发货数量（本次发货的数量）是否上传
     */
    private boolean consignNum;

    /**
     * 剩余发货数量（拆分订单对应的原平台订单下所有商品的未发货数量） 是否上传
     */
    private boolean noConsignNum;

    /**
     * 旗帜
     */
    private Integer flag;

    /**
     * 自定义内容
     */
    private String custom;

    public boolean isSid() {
        return sid;
    }

    public void setSid(boolean sid) {
        this.sid = sid;
    }

    public boolean isShortId() {
        return shortId;
    }

    public void setShortId(boolean shortId) {
        this.shortId = shortId;
    }

    public String getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(String consignTime) {
        this.consignTime = consignTime;
    }

    public boolean isExpress() {
        return express;
    }

    public void setExpress(boolean express) {
        this.express = express;
    }

    public boolean isOutSid() {
        return outSid;
    }

    public void setOutSid(boolean outSid) {
        this.outSid = outSid;
    }

    public boolean isOuterId() {
        return outerId;
    }

    public void setOuterId(boolean outerId) {
        this.outerId = outerId;
    }

    public boolean isConsignNum() {
        return consignNum;
    }

    public void setConsignNum(boolean consignNum) {
        this.consignNum = consignNum;
    }

    public boolean isNoConsignNum() {
        return noConsignNum;
    }

    public void setNoConsignNum(boolean noConsignNum) {
        this.noConsignNum = noConsignNum;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom;
    }

    /**
     * @param uploadContent "sid=1|shortId=1|consignTime=yyyy-mmm-dd hh:mm:ss|express=1|outSid=1|outerId=1|consignNum=1|noConsignNum=1|flag=3
     */
    public static SplitConsignUploadData buildSplitConsignUploadData(String uploadContent, String customContent) {
        SplitConsignUploadData uploadData = new SplitConsignUploadData();
        uploadContent = StringUtils.trimToEmpty(uploadContent);
        customContent = StringUtils.trimToEmpty(customContent);
        if (StringUtils.isNotEmpty(uploadContent)) {
            String[] contentArr = uploadContent.split("\\|");
            if (contentArr.length > 0) {
                for (String content : contentArr) {
                    parseContent(uploadData, StringUtils.trimToEmpty(content), StringUtils.trimToEmpty(customContent));
                }
            }
        }
        return uploadData;
    }

    private static void parseContent(SplitConsignUploadData uploadData, String content, String customContent) {
        if (StringUtils.isNotEmpty(content)) {
            String[] kv = content.split("=");
            String k = StringUtils.trimToEmpty(kv[0]);
            String v = StringUtils.trimToEmpty(kv[1]);
            boolean vB = "1".equals(v);
            if ("sid".equals(k)) {
                uploadData.setSid(vB);
            } else if ("shortId".equals(k)) {
                uploadData.setShortId(vB);
            } else if ("consignTime".equals(k)) {
                uploadData.setConsignTime(v.replace("mm","MM"));
            } else if ("express".equals(k)) {
                uploadData.setExpress(vB);
            } else if ("outSid".equals(k)) {
                uploadData.setOutSid(vB);
            } else if ("outerId".equals(k)) {
                uploadData.setOuterId(vB);
            } else if ("consignNum".equals(k)) {
                uploadData.setConsignNum(vB);
            } else if ("noConsignNum".equals(k)) {
                uploadData.setNoConsignNum(vB);
            } else if ("flag".equals(k)) {
                uploadData.setFlag(NumberUtils.str2Int(v, null));
            } else if("splitConsignUploadSellerMemoContentCustomCheck".equalsIgnoreCase(k) && "1".equals(v) && StringUtils.isNotEmpty(customContent)){
                uploadData.setCustom(customContent);
            }
        }
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.util.Date;

/**
 * 汇率表
 * <AUTHOR>
 *
 */
@Table(name = "exchange_rate")
@Data
public class ExchangeRate extends Model {

	/**
	 *
	 */
	private static final long serialVersionUID = 6459766459246898565L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 要换算的单位
	 */
	private String from;

	/**
	 * 换算后的单位,有多个，json字符串格式
	 */
	private String toInJson;

	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 修改时间
	 */
	private Date updated;
	
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Author: chenchaochao
 * @Date: 2020/9/1 11:17 上午
 * https://open.pinduoduo.com/application/document/browse?idStr=368907B19D976F88
 */
public class PddCommonUtils {

    static char SEP_PHONE = '$';
    static char SEP_ID = '#';
    static char SEP_NORMAL = '~';
    static Pattern BASE64_PATTERN = Pattern.compile("^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$");

    public static boolean isEncryptData(String data) {
        if (null == data || data.length() < 44) {
            return false;
        }
        if (data.charAt(0) != data.charAt(data.length() - 1)) {
            return false;
        }
        char separator = data.charAt(0);
        String[] dataArray = StringUtils.split(data, separator);
        if (dataArray.length < 2
                || !StringUtils.isNumeric(dataArray[dataArray.length - 1])) {
            return false;
        }
        if (separator == SEP_PHONE || separator == SEP_ID) {
            if (dataArray.length == 3) {
                if (data.charAt(data.length() - 2) == separator) {
                    return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                            BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                            dataArray[1].length() >= 44;
                } else {
                    return BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                            dataArray[1].length() >= 44;
                }
            }else if (dataArray.length == 2) {
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        dataArray[0].length() >= 44;
            }else {
                return false;
            }
        }
        if (separator == SEP_NORMAL) {
            if (data.charAt(data.length() - 2) == separator) {
                if (dataArray.length != 3) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[0].length() >= 44;
            } else {
                if (dataArray.length != 2) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        dataArray[0].length() >= 44;
            }
        }
        return false;
    }

    public static boolean isPddUserTrade(Staff staff, Trade trade) {
        if (staff == null || trade == null) {
            return false;
        }
        Map<Long, User> userIdMap = staff.getUserIdMap();
        if (userIdMap == null || trade.getUserId() == null) {
            return false;
        }
        User user = userIdMap.get(trade.getUserId());
        return user != null && CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource());
    }


    public static boolean isEncryptTrade(Trade trade){
        if(isEncryptData(trade.getReceiverAddress()) || isEncryptData(trade.getReceiverName()) || isEncryptData(trade.getReceiverMobile())){
            return true;
        }
        return false;
    }

}

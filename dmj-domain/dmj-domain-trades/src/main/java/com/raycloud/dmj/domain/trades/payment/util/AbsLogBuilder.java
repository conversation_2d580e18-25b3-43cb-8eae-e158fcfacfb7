package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.ESMessage;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.payment.exception.StackRecordException;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.*;

import java.util.*;
import java.util.function.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-09-06
 */
public abstract class AbsLogBuilder<T extends AbsLogBuilder>{

    private StringBuilder bodybuilder;

    private StringBuilder headbuilder;

    private Map<Object,List<Object>> group;

    protected ESMessage message;

    protected boolean changed = false;

    /**
     * @deprecated 不建议直接使用bodybuilder添加内容
     */
    @Deprecated
    public StringBuilder getBuilder() {
        return bodybuilder;
    }

    protected abstract String getBusinessSign();

    /**
     * 子类可重写此方法以使用自己的message类型
     * @return
     */
    protected ESMessage createESMessage(){
        return new ESMessage();
    }

    protected void init(Long companyId, Long staffId, String staffName) {
        message = createESMessage();
        message.setCompanyId(String.valueOf(companyId ==null?-1: companyId));
        message.setStaffId(String.valueOf(staffId ==null?-1: staffId));
        message.setStaffName(staffName);

        headbuilder =  new StringBuilder();
        bodybuilder = new StringBuilder();
        group = new HashMap<>();
    }

    /**
     * 固定解析头部内容
     * @param companyId
     * @param staffId
     * @param staffName
     *
     * @see com.raycloud.dmj.web.log.NewLog4jESMessageFormat#handleESMessage(Map, ESMessage)
     * @return
     */
    private void buildBaseHead(Long companyId,Long staffId,String staffName){
        init(companyId, staffId, staffName);
        String businessSign = getBusinessSign();
        if (businessSign != null) {
            appendHead(businessSign);
            changed = false;
        }
    }

    public AbsLogBuilder(User user) {
        if (user == null) {
            buildBaseHead(-1L,null,null);
        }else {
            Staff staff = user.getStaff();
            if (staff != null) {
                buildBaseHead(staff.getCompanyId(),staff.getId(),staff.getName());
            }else {
                buildBaseHead(user.getCompanyId(),user.getId(),"[店铺]:"+user.getNick());
            }
        }
    }

    public AbsLogBuilder(Staff staff) {
        if (staff == null) {
            buildBaseHead(-1L,null,null);
        }else {
            buildBaseHead(staff.getCompanyId(),staff.getId(),staff.getName());
        }
    }

    private AbsLogBuilder() {
    }

    public AbsLogBuilder(Long companyId) {
        buildBaseHead(companyId,null,null);
    }

    public AbsLogBuilder(Long companyId,Long staffId,String staffName) {
        buildBaseHead(companyId,staffId,staffName);
    }

    /**
     * 添加自定义附加内容到日志头 比如关联Id号
     * @param o
     * @return
     */
    public T appendHead(Object o){
        headbuilder.append("[").append(o == null?"null":o.toString()).append("]");
        changed = true;
        return (T)this;
    }

    /**
     * 添加订单信息到日志头
     * @param o
     * @return
     */
    public  T tradeInHead(Trade trade){
        if (trade == null) {
            headbuilder.append("[trade:null]");
        }else if (trade.getSid() == null) {
            headbuilder.append("[tid:").append(trade.getTid()).append("]");
        }else {
            headbuilder.append("[sid:").append(trade.getSid()).append("]");
        }
        changed = true;
        return (T)this;
    }

    public T append(Object o){
        bodybuilder.append(o == null?"null":o.toString());
        changed = true;
        return (T)this;
    }

    public T appendIf(BooleanSupplier supplier, Object o){
        if(supplier.getAsBoolean()){
            return append(o);
        }
        return (T)this;
    }

    public T appendIf(BooleanSupplier supplier, Object key, Object value){
        if(supplier.getAsBoolean()){
            return append(key,value);
        }
        return (T)this;
    }

    public T appendJSon(Object obj){
        if (obj == null) {
            append("null");
        }else {
            append(JSON.toJSONString(obj));
        }
        return (T)this;
    }

    public T appendJSon(Object obj,String ... fields){
        if (obj == null) {
            append("null");
        }else if (fields == null ) {
            append(JSON.toJSONString(obj));
        }else {
            append(JSON.toJSONString(obj,new SimplePropertyPreFilter(fields)));
        }
        return (T)this;
    }

    public T appendJSonWithSerializeFilter(Object obj, SerializeFilter... filters){
        if (obj == null) {
            append("null");
        }else if (filters == null ) {
            append(JSON.toJSONString(obj));
        }else {
            append(JSON.toJSONString(obj,filters));
        }
        return (T)this;
    }

    public  T appendTrade(Trade trade){
        if (trade == null) {
            append("[trade:null]");
        }else if (trade.getSid() == null) {
            append("[tid:").append(trade.getTid()).append("]");
        }else {
            append("[sid:").append(trade.getSid()).append("]");
        }
        return (T)this;
    }

    public T appendTrade(List<Trade> trades){
        if (CollectionUtils.isEmpty(trades)) {
            append("[trades:empty]");
        }else if (trades.get(0).getSid() == null) {
            append("[tids:").append(Arrays.toString(TradeUtils.toTids(trades))).append("]");
        }else {
            append("[sids:").append(Arrays.toString(TradeUtils.toSids(trades))).append("]");
        }
        return (T)this;
    }

    public T append(Object key, Object value){
        append(String.valueOf(key)).append(":").append(value).append(",");
        return (T)this;
    }

    public  <E> T appendArray(Collection<E> list){
        appendArray(null,list,10,null);
        return (T)this;
    }

    public <R, E> T appendArray(Collection<E> list,int maxSize,Function<E, R> func){
        appendArray(null,list,maxSize,func);
        return (T)this;
    }

    public  <E> T appendArray(Object key, Collection<E> list){
        appendArray(key,list,10,null);
        return (T)this;
    }

    /**
     * 打印集合中指定个数的内容
     * @param key
     * @param list
     * @param maxSize
     * @param func
     * @return
     * @param <R>
     * @param <E>
     */
    public <R, E> T appendArray(Object key, Collection<E> list,int maxSize,Function<E, R> func){
        if (key != null) {
            append(String.valueOf(key)).append(":");
        }
        if (CollectionUtils.isEmpty(list)) {
            append(list == null? "null":"[]");
            return (T)this;
        }
        Iterator<E> iterator = list.iterator();
        int count = 0;
        startArray();
        while (iterator.hasNext()){
            if (count >= maxSize) {
                append("'... "+(list.size()-maxSize)+" more'");
                break;
            }
            E next = iterator.next();
            if (func == null) {
                append(next).append(",");
            }else{
                R apply = func.apply(next);
                append(apply).append(",");
            }
            count++;
        }
        endArray();
        return (T)this;
    }

    public  <E> T group(Object key, Object value){
        group.computeIfAbsent(key,x->new ArrayList<>()).add(value);
        this.changed = true;
        return (T)this;
    }

    public T appendError(String key, Throwable e){
        append(key).append(":").append(e.getMessage() == null? e.getClass().getSimpleName():e.getMessage()).append(",");
        return (T)this;
    }

    public T format(String format, Object... ar) {
        this.append(String.format(format,ar));
        return (T)this;
    }

    public T startObject() {
        append("{");
        return (T)this;
    }

    public T endObject() {
        delLastComma();
        append("},");
        return (T)this;
    }

    public T startArray() {
        append("[");
        return (T)this;
    }

    public T endArray() {
        delLastComma();
        append("],");
        return (T)this;
    }

    public T delLastComma() {
        if (bodybuilder != null && bodybuilder.length() > 0) {
            if (bodybuilder.charAt(bodybuilder.length() - 1) == ',') {
                bodybuilder.deleteCharAt(bodybuilder.length() - 1);
            }
        }
        return (T)this;
    }


    /**
     *
     * @return
     */
    @Deprecated
    public String build(){
        delLastComma();
        return new StringBuilder("[").append(message.getCompanyId()).append(",").append(message.getStaffId()).append(",")
                .append(message.getStaffName()).append("]").append(headbuilder.toString()).append(bodybuilder.toString()).toString();
    }

    @Override
    public String toString() {
        return build();
    }

    /** 单个es日志记录最大长度 */
    public static final int MAX_LENGTH_FOR_LOG = 1030;
    /** 单个业务日志最多的ES记录数 */
    public static final int MAX_COUNT_FOR_LOG = 3;


    protected void print(org.apache.log4j.Logger logger, org.apache.log4j.Priority level, Throwable e){
        if (!logger.isEnabledFor(level)) {
            clean();
            return;
        }
        if (!changed) {
            clean();
            return;
        }
        delLastComma();
        String msg = headbuilder.toString() + getBodyMessage();
        message.setMessage(msg);
        if (e != null) {
            logger.log(level,message,StackTracesUtils.filterStackTraces(e));
        }else{
            logger.log(level,message);
        }
    }

    public void clean() {
        group = null;
        message = null;
        bodybuilder = null;
        headbuilder = null;
        timeRecords = null;
    }


    private String getBodyMessage(){
        if (group != null && !group.isEmpty()) {
            for (Map.Entry<Object, List<Object>> entry : group.entrySet()) {
                bodybuilder.append(entry.getKey()).append(":");
                appendArray(entry.getValue());
            }
        }
        return bodybuilder.toString();
    }

    /**
     * 日志打印
     * @param logger
     * @param level
     */
    public void print(org.apache.log4j.Logger logger, org.apache.log4j.Priority level){
        print(logger,level,null);
    }

    public void printDebug(org.apache.log4j.Logger logger){
        print(logger,Priority.DEBUG);
    }

    public void printInfo(org.apache.log4j.Logger logger){
        print(logger,Priority.INFO);
    }

    public void printWarn(org.apache.log4j.Logger logger){
        print(logger,Priority.WARN);
    }

    /**
     * warn日志 并打印当前的堆栈信息
     * @param logger
     */
    public void printStackWarn(org.apache.log4j.Logger logger){
        Throwable e = new StackRecordException();
        print(logger,Priority.WARN,e);
    }

    /**
     * 打印异常,自动过滤掉异常信息中无意义的堆栈信息后打印
     * @param logger
     * @param level
     */
    public void printWarn(org.apache.log4j.Logger logger, Throwable e){
        print(logger,Priority.WARN,e);
    }

    /**
     * 打印异常,自动过滤掉异常信息中无意义的堆栈信息后打印
     * @param logger
     * @param level
     */
    public void printError(org.apache.log4j.Logger logger, Throwable e){
        print(logger,Priority.ERROR,e);
    }

    public void multiPrintDebug(org.apache.log4j.Logger logger){
        multiPrint(logger,Priority.DEBUG);
    }

    public void multiPrintInfo(org.apache.log4j.Logger logger){
        multiPrint(logger,Priority.INFO);
    }

    /**
     * <pre>
     * 日志打印 自动将过长日志拆分为多个打印 总打印次数不超过 <code>MAX_COUNT_FOR_LOG</code>
     *
     *  如非必要 不要使用这个方法!
     *  如非必要 不要使用这个方法!
     *  如非必要 不要使用这个方法!
     * </pre>
     * @param logger
     * @param level
     */
    public void multiPrint(org.apache.log4j.Logger logger, org.apache.log4j.Priority level){
        multiPrint(logger,level,null);
    }

    public void multiPrint(org.apache.log4j.Logger logger, org.apache.log4j.Priority level, Throwable e){
        if (!logger.isEnabledFor(level)) {
            clean();
            return;
        }
        if (!changed) {
            clean();
            return;
        }
        List<String> list = splitLog();
        for (int i = 0; i < list.size(); i++) {
            ESMessage msg = message;
            if (i != 0) {
                msg = createESMessage();
                msg.setCompanyId(message.getCompanyId());
                msg.setStaffId(message.getStaffId());
                msg.setStaffName(message.getStaffName());
            }
            String s = list.get(i);
            msg.setMessage(s);
            if (i == 0 && e != null) {
                logger.log(level,message,StackTracesUtils.filterStackTraces(e));
            }else{
                logger.log(level,msg);
            }
        }
    }

    public List<String> splitLog() {
        return splitLog(false);
    }

    public List<String> splitLog(boolean full) {
        List<String> result = new ArrayList<>();
        delLastComma();
        String body = getBodyMessage();
        String header = headbuilder.toString();
        //base的内容不写入message字段 因此不占长度
        int h = header.length();

        if (body.length() > MAX_LENGTH_FOR_LOG - h) {

            int maxBodyLen = MAX_LENGTH_FOR_LOG - h - 3;

            List<String> list = splitString(body, maxBodyLen);
            if (full){
                for (int i = 0; i < list.size(); i++) {
                    result.add(header+"["+i+"]"+list.get(i));
                }
            }else{
                for (int i = 0; i < list.size(); i++) {
                    if (i < MAX_COUNT_FOR_LOG - 1) {
                        result.add(header+"["+i+"]"+list.get(i));
                    }else if (i == MAX_COUNT_FOR_LOG - 1) {
                        String sub = list.get(i);
                        if (list.size() > 3) {
                            sub = (sub.substring(0,sub.length() - 3) + "...");
                        }
                        result.add(header+"["+i+"]"+sub);
                    }else{
                        break;
                    }
                }
            }
        }else {
            result.add(header+body);
        }
        return result;
    }

    private List<String> splitString(String input, int length) {
        List<String> result = new ArrayList<>();
        int index = 0;
        while (index < input.length()) {
            result.add(input.substring(index, Math.min(index + length, input.length())));
            index += length;
        }
        return result;
    }



    public T startWatch() {
        this.changed = false;
        return (T)this;
    }

    /**
     * 从日志构造器生成(或最近一处显示调用startWatch()的地方)开始,到当前日志内容是否发生变更 用于判定当前日志是否包含需要输出的内容
     * @return
     */
    public boolean isChanged(){
        return changed;
    }

    //以下用于耗时统计支持
    List<TimeRecord> timeRecords;

    Map<String,MutiTimeRecord> mutiTimeRecordMap;
    Long baseTime;
    Long startTime;

    Long tooklmt = 1L;

    public T startTimer() {
        startTime = System.currentTimeMillis();
        baseTime = startTime;
        timeRecords = new ArrayList<>();
        mutiTimeRecordMap = new HashMap<>();
        return (T)this;
    }

    /**
     * 小于这个数值的耗时将不会被记录
     * @param tooklmt
     * @return
     */
    public T setBaseTooklmt(Long tooklmt) {
        this.tooklmt = tooklmt;
        return (T)this;
    }

    public T reBaseTimer() {
        if (timeRecords == null) {
            startTimer();
        }else {
            baseTime = System.currentTimeMillis();
        }
        return (T)this;
    }

    /**
     * 从上一次recordTimer操作 或上一次使用reBaseTimer()开始计时
     * 到当前的耗时记录
     * @param message
     * @return
     */
    public T recordTimer(String message) {
        return recordTimer(message,tooklmt);
    }


    /**
     * 从上一次recordTimer操作 或上一次使用reBaseTimer()开始计时
     * 到当前的耗时记录
     * @param message
     * @return
     */
    public T recordTimer(String message,Long tooklmt) {
        if (timeRecords == null) {
            startTimer();
        }
        long timeMillis = System.currentTimeMillis();
        if (tooklmt != null && timeMillis - baseTime < tooklmt) {
            baseTime = timeMillis;
            return (T)this;
        }
        timeRecords.add(new SingleTimeRecord(baseTime,timeMillis,message));
        baseTime = timeMillis;
        return (T)this;
    }


    /**
     * 从上一次recordTimer操作 或上一次使用reBaseTimer()开始计时
     * 到当前的耗时记录 相同message的耗时将会被汇总到一起
     * @param message
     * @return
     */
    public T recordMutiTimer(String message) {
        if (timeRecords == null) {
            startTimer();
        }
        if (mutiTimeRecordMap == null) {
            mutiTimeRecordMap = new HashMap<>();
        }
        long timeMillis = System.currentTimeMillis();
        if (!mutiTimeRecordMap.containsKey(message)) {
            MutiTimeRecord timeRecord = new MutiTimeRecord(baseTime, timeMillis, message);
            timeRecords.add(timeRecord);
            mutiTimeRecordMap.put(message,timeRecord);
        }else {
            mutiTimeRecordMap.get(message).add(baseTime, timeMillis);
        }
        baseTime = timeMillis;
        return (T)this;
    }

    public T appendTook(long tooklmt) {
        if (startTime == null) {
            return (T)this;
        }
        Long total = System.currentTimeMillis() - startTime;
        if (total > tooklmt) {
            message.setTotalTook(total);
            message.setTook(total);
            if (timeRecords != null && timeRecords.size() > 0) {
                StringBuilder s = new StringBuilder().append("[");
                for (TimeRecord timeRecord : timeRecords) {
                    s.append(timeRecord.getMsg()).append(",");
                }
                s = s.deleteCharAt(s.lastIndexOf(","));
                s.append("]");
                message.setTookDetails(s.toString());
            }
            append("[total:"+total +"ms]");
        }
        return (T)this;
    }

    static class SingleTimeRecord implements TimeRecord{
        Long took;
        String msg;

        public SingleTimeRecord(Long baseTime, Long curTime, String msg) {
            this.took = curTime -baseTime ;
            this.msg = msg;
        }

        @Override
        public String getMsg() {
            return "{\"" + msg + "\":" + getTook() + "}";
        }

        @Override
        public Long getTook() {
            return took;
        }
    }

    static class MutiTimeRecord implements TimeRecord{
        List<Long> tooks = new ArrayList<>();
        String msg;

        public MutiTimeRecord(Long baseTime, Long curTime, String msg) {
            this.msg = msg;
            add(baseTime,curTime);
        }

        public void add(Long baseTime, Long curTime){
            this.tooks.add(curTime -baseTime);
        }

        @Override
        public String getMsg() {
            Long max = tooks.stream().mapToLong(Long::longValue).max().getAsLong();
            Long avg = ((Double)tooks.stream().mapToLong(Long::longValue).average().getAsDouble()).longValue() ;
            return "{\"" + msg + "\":" + getTook() + "[\"max:" + max + " avg:" + avg +" cnt:" + tooks.size() +"\"]}";
        }

        @Override
        public Long getTook() {
            return tooks.stream().mapToLong(Long::longValue).sum();
        }
    }

    static interface TimeRecord{
        String getMsg();

        Long getTook();
    }


    /**
     * 这个实例啥都不会做 只是为了使用方减少为空判定
     */
    private static AbsLogBuilder nvlInstance =  new AbsLogBuilder<AbsLogBuilder>(){

        @Override
        protected String getBusinessSign() {
            return null;
        }

        @Override
        public AbsLogBuilder appendHead(Object o) {
            return this;
        }

        @Override
        public AbsLogBuilder append(Object o) {
            return this;
        }

        @Override
        public void print(Logger logger, Priority level) {
            super.clean();
            return;
        }

        @Override
        public void multiPrint(Logger logger, Priority level) {
            super.clean();
            return;
        }

        @Override
        public AbsLogBuilder recordTimer(String message) {
            return this;
        }

        @Override
        public AbsLogBuilder recordMutiTimer(String message) {
            return this;
        }

        @Override
        public boolean isChanged() {
            return false;
        }

        @Override
        public AbsLogBuilder  group(Object key, Object value) {
            return this;
        }

        @Override
        public AbsLogBuilder startWatch() {
            return this;
        }

        @Override
        public AbsLogBuilder startTimer() {
            return this;
        }

        @Override
        public AbsLogBuilder setBaseTooklmt(Long tooklmt) {
            return this;
        }

        @Override
        public AbsLogBuilder reBaseTimer() {
            return this;
        }

        @Override
        public AbsLogBuilder recordTimer(String message, Long tooklmt) {
            return this;
        }

        @Override
        public AbsLogBuilder appendTook(long tooklmt) {
            return this;
        }

        @Override
        public AbsLogBuilder tradeInHead(Trade trade) {
            return this;
        }

        @Override
        public AbsLogBuilder appendIf(BooleanSupplier supplier, Object o) {
            return this;
        }

        @Override
        public AbsLogBuilder appendIf(BooleanSupplier supplier, Object key, Object value) {
            return this;
        }

        @Override
        public AbsLogBuilder appendJSon(Object obj) {
            return this;
        }

        @Override
        public AbsLogBuilder appendJSon(Object obj, String... fields) {
            return this;
        }

        @Override
        public AbsLogBuilder appendJSonWithSerializeFilter(Object obj, SerializeFilter... filters) {
            return this;
        }

        @Override
        public AbsLogBuilder appendTrade(Trade trade) {
            return this;
        }

        @Override
        public AbsLogBuilder appendTrade(List<Trade> trades) {
            return this;
        }

        @Override
        public AbsLogBuilder append(Object key, Object value) {
            return this;
        }

        @Override
        public <E> AbsLogBuilder appendArray(Collection<E> list) {
            return this;
        }

        @Override
        public <R, E> AbsLogBuilder appendArray(Collection<E> list, int maxSize, Function<E, R> func) {
            return this;
        }

        @Override
        public <E> AbsLogBuilder appendArray(Object key, Collection<E> list) {
            return this;
        }

        @Override
        public <R, E> AbsLogBuilder appendArray(Object key, Collection<E> list, int maxSize, Function<E, R> func) {
            return this;
        }

        @Override
        public AbsLogBuilder appendError(String key, Throwable e) {
            return this;
        }

        @Override
        public AbsLogBuilder format(String format, Object... ar) {
            return this;
        }

        @Override
        public AbsLogBuilder startObject() {
            return this;
        }

        @Override
        public AbsLogBuilder endObject() {
            return this;
        }

        @Override
        public AbsLogBuilder startArray() {
            return this;
        }

        @Override
        public AbsLogBuilder endArray() {
            return this;
        }

        @Override
        public AbsLogBuilder delLastComma() {
            return this;
        }
    };

    /**
     * 这个实例啥都不会做 只是为了使用方减少为空判定
     */
    public static AbsLogBuilder getNvlInstance() {
        return nvlInstance;
    }
}

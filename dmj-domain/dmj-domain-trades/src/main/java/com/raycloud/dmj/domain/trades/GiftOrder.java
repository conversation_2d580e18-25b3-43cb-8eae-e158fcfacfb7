package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 16/9/23.
 */
public class GiftOrder implements Serializable {


    private static final long serialVersionUID = -1725747927026772576L;
    /**
     * 对应的order的id
     */
    private Long id;
    /**
     * 赠品所属的订单
     */
    private Long sid;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 赠品所属的促销活动
     */
    private Long giftPromotionId;

    /**
     * 赠品所属的规则
     */
    private Long giftPromotionRuleId;

    /**
     * 赠品所属的促销名称
     */
    private String giftPromotionName;

    /**
     * 赠品所属的规则名称
     */
    private String giftPromotionRuleName;

    /**
     * 赠品所属id
     */
    private Long giftGiveItemId;

    /**
     * 匹配到的系统商品ID
     */
    private Long itemSysId;

    /**
     * 匹配到的系统规格ID
     */
    private Long skuSysId;

    /**
     * 赠品数量
     */
    private Integer num;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 赠品是否参与拣选
     */
    private Integer isPick;

    private String outerId;

    private String matchOrderIds;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getGiftPromotionId() {
        return giftPromotionId;
    }

    public void setGiftPromotionId(Long giftPromotionId) {
        this.giftPromotionId = giftPromotionId;
    }

    public Long getGiftPromotionRuleId() {
        return giftPromotionRuleId;
    }

    public void setGiftPromotionRuleId(Long giftPromotionRuleId) {
        this.giftPromotionRuleId = giftPromotionRuleId;
    }

    public Long getGiftGiveItemId() {
        return giftGiveItemId;
    }

    public void setGiftGiveItemId(Long giftGiveItemId) {
        this.giftGiveItemId = giftGiveItemId;
    }

    public Long getItemSysId() {
        return itemSysId;
    }

    public void setItemSysId(Long itemSysId) {
        this.itemSysId = itemSysId;
    }

    public Long getSkuSysId() {
        return skuSysId;
    }

    public void setSkuSysId(Long skuSysId) {
        this.skuSysId = skuSysId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public Integer getIsPick() {
        return isPick;
    }

    public void setIsPick(Integer isPick) {
        this.isPick = isPick;
    }

    public String getGiftPromotionRuleName() {
        return giftPromotionRuleName;
    }

    public void setGiftPromotionRuleName(String giftPromotionRuleName) {
        this.giftPromotionRuleName = giftPromotionRuleName;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getGiftPromotionName() {
        return giftPromotionName;
    }

    public void setGiftPromotionName(String giftPromotionName) {
        this.giftPromotionName = giftPromotionName;
    }

    public String getMatchOrderIds() {
        return matchOrderIds;
    }

    public void setMatchOrderIds(String matchOrderIds) {
        this.matchOrderIds = matchOrderIds;
    }
}

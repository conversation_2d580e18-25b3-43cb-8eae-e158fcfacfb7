package com.raycloud.dmj.domain.trades.params;

import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description <pre>
 * 指定查询订单时需要补充的内容
 * </pre>
 * <AUTHOR>
 * @Date 2024-06-13
 */
public class OrderAssembleParams<T extends OrderAssembleParams> implements Serializable {

    private boolean containSuitSingle = false,includeDelete = false;


    /**
     * 指定要查询的order字段 没有指定则查询所有
     */
    private List<String> queryOrderFields;




    public boolean isContainSuitSingle() {
        return containSuitSingle;
    }

    public T setContainSuitSingle(boolean containSuitSingle) {
        this.containSuitSingle = containSuitSingle;
        return (T)this;
    }

    public List<String> getQueryOrderFields() {
        return queryOrderFields;
    }

    public boolean isIncludeDelete() {
        return includeDelete;
    }

    public T setIncludeDelete(boolean includeDelete) {
        this.includeDelete = includeDelete;
        return (T)this;
    }

    public T setQueryOrderFields(List<String> queryOrderFields) {
        if (CollectionUtils.isNotEmpty(queryOrderFields)) {
            addQueryField(queryOrderFields.toArray(new String[0]));
        }
        return (T)this;
    }

    public T addQueryField(String ... queryField) {
        if (queryOrderFields == null) {
            //保证一些基本字段都会被查询到
            this.addBaseField();
        }
        for (String s : queryField) {
            addField(s);
        }
        return (T)this;
    }


    private void  addField(String field){
        if (queryOrderFields == null) {
            this.queryOrderFields = new ArrayList<>();
        }
        field = field.toLowerCase().trim();
        if (!queryOrderFields.contains(field)) {
            this.queryOrderFields.add(field);
        }
    }

    /**
     * 添加id 归属 状态 类型 从属关系等一些基本字段
     * 强烈建所有指定字段查询的业务 都添加一下基本字段
     * @return
     */
    public T  addBaseField(){
        addField("company_id");
        addField("sid");
        addField("tid");
        addField("source");
        addField("id");
        addField("oid");
        addField("soid");
        addField("belong_sid");

        addField("source");
        addField("is_presell");
        addField("type");

        addField("combine_id");

        addField("sys_outer_id");
        addField("sku_sys_id");
        addField("item_sys_id");
        addField("num_iid");
        addField("outer_sku_id");
        addField("is_virtual");

        addQueryField("item_Changed");
        addQueryField("sys_Item_Changed");

        addQueryField("num");

        addQueryField("volume");
        addQueryField("net_weight");

        addQueryField("sys_status");
        addQueryField("status");
        addQueryField("refund_id");
        addQueryField("refund_status");
        addQueryField("stock_status");
        addQueryField("is_pick");

        addField("sys_Consigned");
        addField("non_consign");

        addField("dest_id");

        addQueryField("enable_status");
        addQueryField("is_cancel");
        addQueryField("pic_path");

        addQueryField("insufficient_canceled");

        return (T)this;
    }


    /**
     * 添加金额相关字段
     * @return
     */
    public T  addPaymentField(){
        addQueryField("cost", "price","num","total_fee",
                "payment", "pay_amount","discount_fee","ac_payment","divide_order_fee",
                "sale_price","sale_fee");
        return (T)this;
    }

    /**
     * 仅查询order表信息
     * @return
     */
    public static OrderAssembleParams justOrder(){
        return new OrderAssembleParams();
    }

    /**
     * 查询order表信息 以及套件对应的子商品信息
     * @return
     */
    public static OrderAssembleParams orderWithSuitSingle(){
        return new OrderAssembleParams().setContainSuitSingle(true);
    }

    /**
     * 添加一些按字段查询时建议的字段 建议业务在此基础上增减需要的字段
     * @param params
     */
    public static void  addRecommendQueryFields(OrderAssembleParams params){
        params.addBaseField();
    }
}

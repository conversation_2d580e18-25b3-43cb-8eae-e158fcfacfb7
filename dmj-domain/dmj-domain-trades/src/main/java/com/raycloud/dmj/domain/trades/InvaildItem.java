package com.raycloud.dmj.domain.trades;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_08_09 11:25
 */
public class InvaildItem {//sku_sys_id
    public static final Integer UN_DOWNLOAD=0;
    public static final Integer UN_MATCH=1;
    public static final Integer MATCH_ING=2;

    private Integer status=UN_DOWNLOAD;//从商品来
    private Long userId;
    private Long taobaoId;
    private Long sid;
    private String title;
    private String sysTitle;
    private String source;
    private String subSource="";
    private Long belongSid;
    private String numIid;
    private String skuId;
    private String outIid;
    private String picPath;
    private String skuPropertiesName;
    private String sysSkuPropertiesName;
    private String sysSkuPropertiesAlias;
    private String shopName;
    private Integer count;
    private String shopFlag;
    private Integer tradeCount;
    private Integer itemSpecial;
    private Long sysItemId;
    private Long sysSkuId;

    private String newItemSkuId;
    private String skuOuterId;
    private String sysPicPath;
    private String itemOuterId;

    private InvaildItemSysItemInfo itemSystem;
    private String outerSkuId;//平台skuOuterId平台商家编码。

    //统计订单下的无效商品总数
    private Integer num;

    public String getShopSource(){
        return source;
    }

    public String getSkuPropertiesName() {
        return skuPropertiesName;
    }

    public void setSkuPropertiesName(String skuPropertiesName) {
        this.skuPropertiesName = skuPropertiesName;
    }

    public String getSysSkuPropertiesName() {
        return sysSkuPropertiesName;
    }

    public void setSysSkuPropertiesName(String sysSkuPropertiesName) {
        this.sysSkuPropertiesName = sysSkuPropertiesName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopFlag() {
        return shopFlag;
    }

    public void setShopFlag(String shopFlag) {
        this.shopFlag = shopFlag;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSysTitle() {
        return sysTitle;
    }

    public void setSysTitle(String sysTitle) {
        this.sysTitle = sysTitle;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getBelongSid() {
        return belongSid;
    }

    public void setBelongSid(Long belongSid) {
        this.belongSid = belongSid;
    }

    public String getNumIid() {
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOutIid() {
        return outIid;
    }

    public void setOutIid(String outIid) {
        this.outIid = outIid;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public Integer getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Integer tradeCount) {
        this.tradeCount = tradeCount;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        if (subSource==null){return;}
        this.subSource = subSource;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getNewItemSkuId() {
        return newItemSkuId;
    }

    public void setNewItemSkuId(String newItemSkuId) {
        this.newItemSkuId = newItemSkuId;
    }

    public Integer getItemSpecial() {
        return itemSpecial;
    }

    public void setItemSpecial(Integer itemSpecial) {
        this.itemSpecial = itemSpecial;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public String getSysSkuPropertiesAlias() {
        return sysSkuPropertiesAlias;
    }

    public void setSysSkuPropertiesAlias(String sysSkuPropertiesAlias) {
        this.sysSkuPropertiesAlias = sysSkuPropertiesAlias;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSysPicPath(String sysPicPath) {
        this.sysPicPath = sysPicPath;
    }

    public String getSysPicPath() {
        return sysPicPath;
    }

    public void setItemOuterId(String itemOuterId) {
        this.itemOuterId = itemOuterId;
    }

    public String getItemOuterId() {
        return itemOuterId;
    }

    public InvaildItemSysItemInfo getItemSystem() {
        return itemSystem;
    }

    public void setItemSystem(InvaildItemSysItemInfo itemSystem) {
        this.itemSystem = itemSystem;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public boolean isUnMatchItem(){
        return this.getSysItemId() != null && this.getSysItemId() !=-1 && this.getStatus().equals(UN_MATCH);
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public static class InvaildItemSysItemInfo{
        private Long sysItemId;
        private String picPath;
        private String title;
        private String type;
        private Integer typeTag;

        public String getPicPath() {
            return picPath;
        }

        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Long getSysItemId() {
            return sysItemId;
        }

        public void setSysItemId(Long sysItemId) {
            this.sysItemId = sysItemId;
        }

        public Integer getTypeTag() {
            return typeTag;
        }

        public void setTypeTag(Integer typeTag) {
            this.typeTag = typeTag;
        }
    }
}

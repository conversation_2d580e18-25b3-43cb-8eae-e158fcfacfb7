package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.enums.TradeInspectErrorEnum;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @Author: ruanyaguang
 * @Date : 2017/5/22
 * @Info : 订单体检结果
 */
public class TradeInspectResult implements Serializable {
    private static final long serialVersionUID = 142759361475847045L;

    private final Map<Long, Set<TradeInspectErrorEnum>> sidInspectErrorMap;

    public TradeInspectResult() {
        sidInspectErrorMap = new HashMap<Long, Set<TradeInspectErrorEnum>>();
    }

    public void addSidInspectError(Long sid, TradeInspectErrorEnum error) {
        if (sidInspectErrorMap.containsKey(sid)) {
            sidInspectErrorMap.get(sid).add(error);
        } else {
            Set<TradeInspectErrorEnum> set = new HashSet<TradeInspectErrorEnum>(1);
            set.add(error);
            sidInspectErrorMap.put(sid, set);
        }
    }

    public void addSidInspectErrors(Long sid, Set<TradeInspectErrorEnum> errors) {
        if (sidInspectErrorMap.containsKey(sid)) {
            sidInspectErrorMap.get(sid).addAll(errors);
        } else {
            sidInspectErrorMap.put(sid, errors);
        }
    }

    public Map<Long, Set<TradeInspectErrorEnum>> getSidInspectErrorMap() {
        return sidInspectErrorMap;
    }
}

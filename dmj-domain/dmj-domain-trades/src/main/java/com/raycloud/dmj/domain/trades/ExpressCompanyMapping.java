package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 物流映射关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@Table(name = "express_company_mapping", migratable = false)
public class ExpressCompanyMapping extends Model {


    private static final long serialVersionUID = 5872785361193467649L;
    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 平台
     */
    private String source;

    /**
     * 平台发货需要的参数类型：1:id 2:code 3:name
     */
    private String matchType;

    /**
     * 平台物流公司id
     */
    private String platformBizId;

    /**
     * 平台物流公司编码
     */
    private String platformBizCode;

    /**
     * 平台物流公司名称
     */
    private String platformBizName;

    /**
     * erp系统的物流公司ID
     */
    private Long relativeExpressCompanyId;

    /**
     * 系统物流公司编码
     */
    private String sysBizCode;

    /**
     * 系统物流公司名称
     */
    private String sysBizName;

    /**
     * 启用状态，1：启用，0未启用，默认为0
     */
    private Boolean active;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 数据状态 1：有效 0：无效 默认为1
     */
    private Boolean enableStatus;


    /**
     * 售后平台物流公司编码
     */
    private String asAliasPlatformBizCode;

}

package com.raycloud.dmj.domain.rematch.enums;

import lombok.*;

/**
 * @Auther mengfanguang
 * @Date 2022/10/19
 */
@Getter
@AllArgsConstructor
public enum EventEnum {

    /**
     * 修改商品
     */
    EVENT_CHANGE_ITEM("修改商品事件"),

    EVENT_CREATE_SYS_TRADE("创建手工单"),

    EVENT_CREATE_AFTER_SALE_TRADE("创建换货补发单"),

    EVENT_TRADE_COPY("复制订单"),

    EVENT_EXCEL_IMPORT_TRADE("EXCEL导入订单"),

    EVENT_AFTER_SALE_CREATE_TRADE("售后创建订单"),

    EVENT_TRADE_MATCH_ITEM("未匹配商品的订单重新匹配到商品"),

    EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG("修改卖家备注或者旗帜"),
    EVENT_TRADE_CHANGE_SHIPPING_ADDRESS("修改收件人信息"),

    EVENT_TRADE_MERGE_AFTER("合单后"),

    EVENT_TRADE_MERGE_UNDO_AFTER("取消合单后"),

    EVENT_TRADE_SPLIT_AFTER("拆单后"),

    EVENT_TRADE_SPLIT_UNDO_AFTER("取消拆单后"),


    EVENT_TRADE_CHANGE_WAREHOUSE_AFTER("修改仓库后"),

    EVENT_TRADE_MERGE_TRADE_LABEL("新版标签拆分后"),

    EVENT_TRADE_SYNC("订单同步"),

    EVENT_TRADE_BE_WAIT_AUDIT("系统状态由待付款到待审核"),

    EVENT_TRADE_CANCEL_RISK("订单取消风控"),

    EVENT_CHANGE_EXPRESS("修改快递事件"),
    ;

    /**
     * 说明
     */
    private final String msg;

}
package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.TradeUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 17/3/27.
 * 修改订单使用`
 */
public class ModifyData {
    /**
     * 新增的子订单
     */
    public List<Order> inserts = new ArrayList<>();

    /**
     * 要删除的子订单
     */
    public List<Order> deletes = new ArrayList<>();

    /**
     * 要更新的子订单,包括基本信息的更新以及商品数量的变化
     */
    public List<Order> updates = new ArrayList<>();
    /**
     * 要更换仓库的子订单
     */
    public List<Order> warehouseChanges = new ArrayList<>();
    /**
     * 要更换商品的子订单
     */
    public List<Order> itemChanges = new ArrayList<>();
    /**
     * 更换商品数量的子订单(商品本身没有更换)
     */
    public List<Order> numChanges = new ArrayList<>();

    public List<Trade> updateTrades = new ArrayList<>();

    /**
     * 合单集合
     */
    public List<Trade> mergeTrades = new ArrayList<>();

    /**
     * 要更新报关信息的子订单
     */
    public List<Order> declareInfoModifieds = new ArrayList<>();

    public List<Order> singleOrders = new ArrayList<>();

    public List<Order> resumeStocks = new ArrayList<>();
    public List<Order> applyOrders = new ArrayList<>();

    /**
     * 记录套件转单品的原始订单
     */
    public List<Order> originOrders = new ArrayList<>();

    public List<OrderExt> orderExts = new ArrayList<>();

    public List<OrderModifyLog> orderModifyLogs = new ArrayList<>();

    public Map<Long,OrderExt> orderExtMap=new HashMap<>();
    /**
     * 获取更新的子订单
     *
     * @return
     */
    public List<Order> getUpdateOrders() {
        List<Order> updateOrders = new ArrayList<>();
        if (deletes != null && deletes.size() > 0) {
            for (Order order : deletes) {
                Order updateOrder = createUpdateOrder(order);
                updateOrder.setEnableStatus(0);
                updateOrders.add(updateOrder);
                List<Order> suits = order.getSuits();
                if (suits == null || suits.size() == 0) {
                    continue;
                }
                for (Order suit : suits) {
                    Order updateSuit = createUpdateOrder(suit);
                    updateSuit.setEnableStatus(0);
                    updateOrders.add(updateSuit);
                }
            }
        }

        if (updates != null && updates.size() > 0) {
            for (Order order : updates) {
                updateOrders.add(order);
                List<Order> suits = order.getSuits();
                if (suits != null && suits.size() > 0) {
                    updateOrders.addAll(suits);
                }
            }
        }

        return updateOrders;
    }

    private Order createUpdateOrder(Order order) {
        Order updateOrder = new TbOrder();
        updateOrder.setId(order.getId());
        return updateOrder;
    }

    public String getChangeItemLog() {
        if (itemChanges.size() == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        String splitChar = "";
        sb.append("[");
        for (Order order : itemChanges) {
            sb.append(splitChar).append(order.getSysOuterId()).append("*").append(order.getNum());
            splitChar = ",";
        }
        return sb.append("]").toString();
    }

    public void addOrderExt(OrderExt orderExt) {
        OrderExt temp = new OrderExt();
        temp.setId(orderExt.getId());
        temp.setSid(orderExt.getSid());
        temp.setTid(orderExt.getTid());
        temp.setCompanyId(orderExt.getCompanyId());
        temp.setEnableStatus(orderExt.getEnableStatus());
        temp.setAuthorName(orderExt.getAuthorName());
        temp.setAuthorId(orderExt.getAuthorId());
        temp.setAuthorNo(orderExt.getAuthorNo());
        temp.setOrderRemark(orderExt.getOrderRemark());
        temp.setCooperationNoJitx(orderExt.getCooperationNoJitx());
        temp.setCustomization(orderExt.getCustomization());
        temp.setPromiseAcceptTime(orderExt.getPromiseAcceptTime());
        this.orderExts.add(temp);
    }

    public void addOrderExtOnlyOrderRemark(OrderExt orderExt) {
        OrderExt temp = new OrderExt();
        temp.setId(orderExt.getId());
        temp.setSid(orderExt.getSid());
        temp.setTid(orderExt.getTid());
        temp.setCompanyId(orderExt.getCompanyId());
        temp.setOrderRemark(orderExt.getOrderRemark());
        this.orderExts.add(temp);
    }

    public void addMergeTrade(Trade trade) {
        if (TradeUtils.isMerge(trade)) {
            mergeTrades.add(trade);
        }
    }

    public boolean isChange() {
        return !inserts.isEmpty() || !deletes.isEmpty() || !updates.isEmpty() || !warehouseChanges.isEmpty() || !itemChanges.isEmpty() || !numChanges.isEmpty() || !updateTrades.isEmpty();
    }

    public OrderExt getUpdateOrderExt(OrderExt orderExt) {
        OrderExt updateExt = orderExtMap.get(orderExt.getId());
        if (updateExt == null) {
            updateExt = new OrderExt();
            updateExt.setId(orderExt.getId());
            updateExt.setSid(orderExt.getSid());
            updateExt.setTid(orderExt.getTid());
            updateExt.setCompanyId(orderExt.getCompanyId());
            updateExt.setCustomization(orderExt.getCustomization());
            orderExtMap.put(orderExt.getId(), updateExt);
        }
        return updateExt;
    }

}

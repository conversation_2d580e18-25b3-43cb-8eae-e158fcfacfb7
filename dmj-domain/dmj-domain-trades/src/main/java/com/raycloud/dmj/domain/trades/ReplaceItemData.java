package com.raycloud.dmj.domain.trades;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/05/26 09:23 PM
 * 订单换商品使用`
 */
public class ReplaceItemData {

    private TradeConfig tradeConfig;

    /**
     * 订单失败原因
     * key:sid, value:String 错误原因
     */
    private Map<String, String> error = new HashMap<>();

    /**
     * 原始的订单
     * key:sid value:Trade
     */
    private Map<Long, Trade> originTradeMap = new HashMap<>();

    /**
     * 新的订单列表
     */
    private List<Trade> updateTradeList = new ArrayList<>();

    /**
     * 更新的订单
     * key:sid value:Trade
     */
    private Map<Long, Trade> updateTradeMap = new HashMap<>();

    /**
     * 要删除的子订单
     */
    private List<Order> deleteOrders = new ArrayList<>();

    /**
     * 要更换商品的子订单
     * key:sid, value:Order
     */
    private Map<Long, List<Order>> deleteOrderMap = new HashMap<>();

    /**
     * 要更换商品的子订单
     */
    private List<Order> itemChanges = new ArrayList<>();

    /**
     * 要更换商品的子订单
     * key:sid, value:Order
     */
    private Map<Long, Order> itemChangesOrderMap = new HashMap<>();

    /**
     * 更换商品数量的子订单(商品本身没有更换)
     */
    private List<Order> numChanges = new ArrayList<>();

    /**
     * 要更换数量的子订单
     * key:sid, value:Order
     */
    private Map<Long, Order> numChangesOrderMap = new HashMap<>();

    public ReplaceItemData(TradeConfig tradeConfig) {
        this.tradeConfig = tradeConfig;
    }

    public TradeConfig getTradeConfig() {
        return tradeConfig;
    }

    public void setTradeConfig(TradeConfig tradeConfig) {
        this.tradeConfig = tradeConfig;
    }

    public Map<String, String> getError() {
        return error;
    }

    public void setError(Map<String, String> error) {
        this.error = error;
    }

    public Map<Long, Trade> getOriginTradeMap() {
        return originTradeMap;
    }

    public void setOriginTradeMap(Map<Long, Trade> originTradeMap) {
        this.originTradeMap = originTradeMap;
    }

    public List<Trade> getUpdateTradeList() {
        return updateTradeList;
    }

    public void setUpdateTradeList(List<Trade> updateTradeList) {
        this.updateTradeList = updateTradeList;
    }

    public Map<Long, Trade> getUpdateTradeMap() {
        return updateTradeMap;
    }

    public void setUpdateTradeMap(Map<Long, Trade> updateTradeMap) {
        this.updateTradeMap = updateTradeMap;
    }

    public List<Order> getDeleteOrders() {
        return deleteOrders;
    }

    public void setDeleteOrders(List<Order> deleteOrders) {
        this.deleteOrders = deleteOrders;
    }

    public Map<Long, List<Order>> getDeleteOrderMap() {
        return deleteOrderMap;
    }

    public void setDeleteOrderMap(Map<Long, List<Order>> deleteOrderMap) {
        this.deleteOrderMap = deleteOrderMap;
    }

    public List<Order> getItemChanges() {
        return itemChanges;
    }

    public void setItemChanges(List<Order> itemChanges) {
        this.itemChanges = itemChanges;
    }

    public Map<Long, Order> getItemChangesOrderMap() {
        return itemChangesOrderMap;
    }

    public void setItemChangesOrderMap(Map<Long, Order> itemChangesOrderMap) {
        this.itemChangesOrderMap = itemChangesOrderMap;
    }

    public List<Order> getNumChanges() {
        return numChanges;
    }

    public void setNumChanges(List<Order> numChanges) {
        this.numChanges = numChanges;
    }

    public Map<Long, Order> getNumChangesOrderMap() {
        return numChangesOrderMap;
    }

    public void setNumChangesOrderMap(Map<Long, Order> numChangesOrderMap) {
        this.numChangesOrderMap = numChangesOrderMap;
    }

    public void addItemChanges(Trade newTrade, Trade origin, Order order){
        this.itemChanges.add(order);
        this.itemChangesOrderMap.put(order.getSid(), order);
        this.updateTradeMap.put(newTrade.getSid(), newTrade);
        this.originTradeMap.put(newTrade.getSid(), origin);
    }

    public void addNumChanges(Trade newTrade, Trade origin, Order order){
        this.numChanges.add(order);
        this.numChangesOrderMap.put(order.getSid(), order);
        this.updateTradeMap.put(newTrade.getSid(), newTrade);
        this.originTradeMap.put(newTrade.getSid(), origin);
    }

    public void addDeleteChanges(Trade newTrade, Trade origin, Order order){
        this.deleteOrders.add(order);
        this.deleteOrderMap.computeIfAbsent(order.getSid(), t -> new ArrayList<>()).add(order);
        this.updateTradeMap.put(newTrade.getSid(), newTrade);
        this.originTradeMap.put(newTrade.getSid(), origin);
    }

    /**
     * 是否能进行后续业务，有正确的业务数据
     * @return
     */
    public boolean hasData(){
        if (originTradeMap.isEmpty()){
            return false;
        }
        if (updateTradeMap.isEmpty()){
            return false;
        }
        this.updateTradeList = new ArrayList<>(updateTradeMap.values());
        return true;
    }

    public void clear(){
        this.error.clear();
        this.originTradeMap.clear();
        this.updateTradeList.clear();
        this.updateTradeMap.clear();
        this.deleteOrders.clear();
        this.deleteOrderMap.clear();
        this.itemChanges.clear();
        this.itemChangesOrderMap.clear();
        this.numChanges.clear();
        this.numChangesOrderMap.clear();
    }
}

package com.raycloud.dmj.domain.trades.payment.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-10
 */
public  class BigDecimalWrapper {

    BigDecimal curr = null;

    public BigDecimalWrapper(BigDecimal a) {
        if (a == null) {
            this.curr = new BigDecimal("0.0");
        }else {
            this.curr = a;
        }
    }

    public BigDecimalWrapper(String curr) {
        this.curr = MathUtils.toBigDecimal(curr);
    }

    public BigDecimalWrapper(Double curr) {
        this.curr = MathUtils.toBigDecimal(curr);
    }

    public BigDecimalWrapper() {
        this.curr = BigDecimal.ZERO;
    }

    public BigDecimalWrapper add(BigDecimal a){
        curr = MathUtils.add(curr,a);
        return this;
    }

    public BigDecimalWrapper add(String a){
        if (StringUtils.isNotBlank(a)) {
            curr = MathUtils.add(curr,MathUtils.toBigDecimal(a));
        }
        return this;
    }

    public BigDecimalWrapper add(Double a){
        if (a != null) {
            curr = MathUtils.add(curr,MathUtils.toBigDecimal(a));
        }
        return this;
    }

    /**
     * 加上给定数值的乘积 + (a*b)
     * @param a
     * @return
     */
    public BigDecimalWrapper addMulti(Double a,Integer b){
        BigDecimal multiply = MathUtils.multiply(a, b);
        curr = MathUtils.add(curr,multiply);
        return this;
    }

    /**
     * 加上给定数值的乘积 + (a*b)
     * @param a
     * @return
     */
    public BigDecimalWrapper addMulti(String a,Integer b){
        BigDecimal multiply = MathUtils.multiply(a, b);
        curr = MathUtils.add(curr,multiply);
        return this;
    }

    public BigDecimalWrapper subtract(BigDecimal a){
        curr = MathUtils.subtract(curr,a);
        return this;
    }

    public BigDecimalWrapper subtract(String a){
        if (StringUtils.isNotBlank(a)) {
            curr = MathUtils.subtract(curr,MathUtils.toBigDecimal(a));
        }
        return this;
    }

    public BigDecimalWrapper subtract(Double a){
        if (a != null) {
            curr = MathUtils.subtract(curr,MathUtils.toBigDecimal(a));
        }
        return this;
    }

    /**
     * 减去给定数值的乘积  - (a*b)
     * @param a
     * @return
     */
    public BigDecimalWrapper subMulti(Double a,Integer b){
        BigDecimal multiply = MathUtils.multiply(a, b);
        curr = MathUtils.subtract(curr,multiply);
        return this;
    }

    /**
     * 减去给定数值的乘积 - (a*b)
     * @param a
     * @return
     */
    public BigDecimalWrapper subMulti(String a,Integer b){
        BigDecimal multiply = MathUtils.multiply(a, b);
        curr = MathUtils.subtract(curr,multiply);
        return this;
    }

    public BigDecimalWrapper multiply(BigDecimal a){
        curr = MathUtils.multiply(curr,a);
        return this;
    }

    public BigDecimalWrapper multiply(Double a){
        curr = MathUtils.multiply(curr,MathUtils.toBigDecimal(a));
        return this;
    }


    public BigDecimalWrapper multiply(Integer a){
        curr = MathUtils.multiply(curr,a);
        return this;
    }

    public BigDecimalWrapper divide(BigDecimal a){
        curr = MathUtils.divide(curr,9,a);
        return this;
    }

    public BigDecimalWrapper divide(Integer a){
        curr = MathUtils.divide(curr,9,MathUtils.toBigDecimal(a));
        return this;
    }


    @Override
    public String toString() {
        return getString();
    }

    public  String getString(){
        return MathUtils.toString(curr);
    }

    public  double getDouble(){
        return MathUtils.toDouble(curr);
    }

    public BigDecimal get() {
        return MathUtils.scaleUp(curr);
    }


    public BigDecimal get(int scale) {
        return MathUtils.scaleUp(curr,scale);
    }

    public  String getString(int scale){
        return MathUtils.scaleUp(curr,scale).toPlainString();
    }

    public  double getDouble(int scale){
        return MathUtils.scaleUp(curr,scale).doubleValue();
    }

    public  boolean equals( BigDecimal b){
        return MathUtils.equals(curr, b);
    }

    public  boolean equalsTo( String b){
        return equals(MathUtils.toBigDecimal(b));
    }

    public  boolean equalsTo( Double b){
        return equals(MathUtils.toBigDecimal(b));
    }

    public  boolean isZero(){
        return MathUtils.equalsZero(curr);
    }

    public  boolean greatThanZero(){
        return MathUtils.greatThan(curr,BigDecimal.ZERO);
    }

}

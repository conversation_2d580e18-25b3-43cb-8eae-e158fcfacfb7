package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class ExpressCompanyRenderer implements IFieldValueRenderer {

    /**
     * 快递公司编号：格式 id,id,id,  0:无需物流发货，-2 未设置快递
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "无需物流发货";
        }
        if (Objects.equals(originValue,-2)) {
            return "未设置快递";
        }
        return String.valueOf(originValue);
    }
}

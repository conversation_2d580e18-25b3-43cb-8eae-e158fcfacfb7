package com.raycloud.dmj.domain.trades.tradepay;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/3/24 8:27 下午
 */
public enum TradePayType {
    TYPE_CASH(1, "现金"),
    TYPE_ALIPAY(2, "支付宝"),
    TYPE_WECHAT(3, "微信"),
    TYPE_BANK(4, "银行转账"),
    TYPE_SURPLUS(5, "余额"),
    TYPE_PRE_PAY(6, "预支付"),
    TYPE_OTHER(99, "微信");

    private int value;

    private String name;

    TradePayType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static TradePayType parseOfValue(int value) {
        for (TradePayType source : TradePayType.values()) {
            if (source.value == value) {
                return source;
            }
        }

        return null;
    }

    public static TradePayType parseOfName(String name){
        for (TradePayType source : TradePayType.values()) {
            if (source.name.equals(name)) {
                return source;
            }
        }
        return null;
    }
}

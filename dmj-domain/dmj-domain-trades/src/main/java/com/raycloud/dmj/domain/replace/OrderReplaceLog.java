package com.raycloud.dmj.domain.replace;

import com.raycloud.dmj.domain.enums.OrderReplaceTypeEnum;
import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Description OrderReplaceLog
 * @Date 2021/12/15 11:46
 * @Created 杨恒
 */
@Getter
@Setter
public class OrderReplaceLog extends Model {
    private static final long serialVersionUID = 3563570279892807807L;

    private Long id;

    private Long companyId;

    private Long sid;

    private String tid;

    private Long oid;

    /**
     * 操作类型 {@link OrderReplaceTypeEnum}
     */
    private Integer opType;

    /**
     * 原子订单id
     */
    private Long oldOrderId;

    /**
     * 原子订单系统商品id
     */
    private Long oldSysItemId;

    /**
     * 原子订单系统规格id
     */
    private Long oldSysSkuId;

    /**
     * 原子订单商品平台id
     */
    private String oldNumIid;

    /**
     * 原子订单规格平台id
     */
    private String oldSkuId;

    /**
     * 原子订单数量（用来替换的数量）
     */
    private Integer oldNum;

    /**
     * 原子订单source
     */
    private String oldSource;

    /**
     * 新子订单id
     */
    private Long newOrderId;

    /**
     * 新子订单商品id
     */
    private Long newSysItemId;

    /**
     * 新子订单规格id
     */
    private Long newSysSkuId;

    /**
     * 新子订单平台商品id
     */
    private String newNumIid;

    /**
     * 新子订单平台规格id
     */
    private String newSkuId;

    /**
     * 新子订单数量（被替换后的商品的数量）
     */
    private Integer newNum;

    /**
     * 新子订单source
     */
    private String newSource;

    /**
     * 记录一些说明信息
     */
    private String content;

    /**
     * 创建时间
     */
    private Date created;

    private Integer enableStatus;
}

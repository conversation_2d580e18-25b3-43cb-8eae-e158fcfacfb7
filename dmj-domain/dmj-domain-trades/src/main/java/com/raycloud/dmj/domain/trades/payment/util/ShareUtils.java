package com.raycloud.dmj.domain.trades.payment.util;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.trades.payment.exception.MoneyShareException;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description <pre>
 *  金额拆分工具
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-02
 */
public class ShareUtils {


    /**
     * 按指定分摊因数 对金额进行分摊处理 尾差记录到分摊因数不为0的最后一个对象上
     * @param ratios  分摊因数  按 ShareParam.ratio / ∑(ShareParam.ratio) 进行分摊
     * @param totalToShare 指定需要分摊的金额 与结果中shared字段顺序一致
     * @return
     * @param <T>
     * @throws MoneyShareException
     */
    public static <T> List<ShareResult<T>> share(Staff staff, List<ShareParam<T>> ratios, Double ... totalToShare) throws MoneyShareException {
        BigDecimal[] totals = new  BigDecimal[totalToShare.length];
        for (int i = 0; i < totalToShare.length; i++) {
            totals[i] = MathUtils.toBigDecimal(totalToShare[i]);
        }
        return share(staff,ratios, totals);
    }

    /**
     * 按指定分摊因数 对金额进行分摊处理 尾差记录到分摊因数不为0的最后一个对象上
     * @param ratios  分摊因数  按 ShareParam.ratio / ∑(ShareParam.ratio) 进行分摊
     * @param totalToShare 指定需要分摊的金额 与结果中shared字段顺序一致
     * @return
     * @param <T>
     * @throws MoneyShareException
     */
    public static <T> List<ShareResult<T>> share(Staff staff,List<ShareParam<T>> ratios, String ... totalToShare) throws MoneyShareException {
        BigDecimal[] totals = new  BigDecimal[totalToShare.length];
        for (int i = 0; i < totalToShare.length; i++) {
            totals[i] = MathUtils.toBigDecimal(totalToShare[i]);
        }
        return share(staff,ratios, totals);
    }


    /**
     * 按指定分摊因数 对金额进行分摊处理 尾差记录到分摊因数不为0的最后一个对象上
     * @param ratios  分摊因数  按 ShareParam.ratio / ∑(ShareParam.ratio) 进行分摊
     * @param totalToShare 指定需要分摊的金额 与结果中shared字段顺序一致
     * @return
     * @param <T>
     * @throws MoneyShareException
     */
    public static <T> List<ShareResult<T>> share(Staff staff,List<ShareParam<T>> ratios, BigDecimal ... totalToShare) throws MoneyShareException {
        if (CollectionUtils.isEmpty(ratios)) {
            return Collections.emptyList();
        }


        if (ratios.size() == 1) {
            ShareParam<T> param = ratios.get(0);
            ShareResult<T> result = new ShareResult<T>();
            result.setTarget(param.target);
            result.setShared(totalToShare);
            result.setRatio(param.ratio);
            result.setSum(param.ratio);
            result.setDesc("单个继承");
            return Arrays.asList(result);
        }

        BigDecimalWrapper wrapper = new BigDecimalWrapper(BigDecimal.ZERO);
        for (ShareParam<T> ratio : ratios) {
            wrapper.add(ratio.getRatio());
        }
        BigDecimal sum =wrapper.get();
        if (MathUtils.equalsZero(sum)) {
            throw new MoneyShareException("分摊除数0 无法分摊");
        }

        boolean allZero = true;
        List<ShareResult<T>> results = new ArrayList<>();
        List<ShareParam<T>> needCalculate = new ArrayList<>();

        BigDecimal zero = new BigDecimal("0.00");
        BigDecimal[] zeros = new BigDecimal[totalToShare.length];
        for (int i = 0; i < totalToShare.length; i++) {
            zeros[i] = zero;
        }

        for (ShareParam<T> ratio : ratios) {
            if (ratio.getRatio() == null) {
                ratio.setRatio(BigDecimal.ZERO);
            }
            if (MathUtils.equalsZero(ratio.getRatio())) {
                //分摊因数为0的 不参与分摊计算 相应金额直接设置为0 防止尾差记录到因数为0的对象上;
                ShareResult<T> result = new ShareResult<T>();
                result.setTarget(ratio.target);
                result.setShared(zeros);
                result.setRatio(ratio.ratio);
                result.setDesc("0.00/"+sum);
                results.add(result);
            }else {
                allZero =false;
                needCalculate.add(ratio);
            }
        }

        if (allZero) {
            throw new MoneyShareException("所有分摊比例都为0 无法分摊");
        }

        int counter = 0;
        BigDecimal[] alreadyShareds = new BigDecimal[totalToShare.length];
        for (ShareParam<T> param : needCalculate) {
            BigDecimal[] shared = new BigDecimal[totalToShare.length];
            ShareResult<T> result = new ShareResult<T>();
            result.setTarget(param.target);
            result.setShared(shared);
            result.setRatio(param.ratio);
            result.setSum(sum);
            //最后一笔算尾差
            if (counter == needCalculate.size() - 1) {
                for (int i = 0; i < totalToShare.length; i++) {
                    shared[i] = MathUtils.subtract(totalToShare[i],alreadyShareds[i]);
                }
                result.setDesc("剩余值");
            }else{
                for (int i = 0; i < totalToShare.length; i++) {
                    BigDecimal toShare = totalToShare[i];
                    if (MathUtils.equalsZero(toShare)) {
                        shared[i] = BigDecimal.ZERO;
                    }else{

                        BigDecimal divide = toShare.multiply(param.ratio).divide(sum,8,BigDecimal.ROUND_HALF_UP);
                        BigDecimal decimal = null;
                        if(ConfigHolder.PAYMENT_CALCULATE_CONFIG.useGlobalScaleWhenAllocation(staff.getCompanyId())){
                            decimal = MathUtils.scaleUp(staff.getCompanyId(),divide);
                        }else {
                            decimal = MathUtils.scaleUp(divide);
                        }
                        if (alreadyShareds[i] == null) {
                            alreadyShareds[i] = BigDecimal.ZERO;
                        }
                        //如果总的已分摊金额已经大于分摊金额(因为进位),则后续的金额都设置为0
                        if ((toShare.compareTo(BigDecimal.ZERO) > 0 && toShare.subtract(alreadyShareds[i]).compareTo(decimal) < 0) ||
                                (toShare.compareTo(BigDecimal.ZERO) < 0 && toShare.subtract(alreadyShareds[i]).compareTo(decimal) > 0)
                        ) {
                            shared[i] = toShare.subtract(alreadyShareds[i]);
                            alreadyShareds[i] = toShare;
                            result.setDesc("剩余不足");
                        }else {
                            alreadyShareds[i] = alreadyShareds[i].add(decimal);
                            shared[i] = decimal;
                            result.setDesc(param.ratio+"/"+sum);
                        }
                    }
                }
            }
            counter++;

            results.add(result);
        }
        return  results;
    }


    @Data
    public static class ShareParam<T>{

        private T target;

        private BigDecimal ratio;

        public ShareParam(T target, BigDecimal ratio) {
            this.target = target;
            this.ratio = ratio;
        }
    }

    @Data
    public static class ShareResult<T>{

        private T target;

        private BigDecimal ratio;

        private BigDecimal sum;

        private BigDecimal[] shared;

        private String desc;

    }
}

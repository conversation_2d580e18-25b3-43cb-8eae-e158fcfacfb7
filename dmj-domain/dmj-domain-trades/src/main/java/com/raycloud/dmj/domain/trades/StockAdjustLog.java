package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 17/5/16.
 * 库存调整日志
 */
@Table(name = "stock_adjust_log", routerKey = "stockAdjustLogDbNo")
public class StockAdjustLog extends Model {

    private static final long serialVersionUID = 5815371904989723549L;
    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 子订单编号
     */
    private Long orderId;

    /**
     * 创建时间
     */
    private Date insertTime;

    /**
     * 是否可用（是否处理成功） 0：成功 1：失败
     */
    private Integer enableStatus;

    private Integer total;


    public StockAdjustLog(){

    }

    public StockAdjustLog(Long companyId,Long orderId,Integer enableStatus){
        this.companyId = companyId;
        this.orderId = orderId;
        this.enableStatus = enableStatus;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

}

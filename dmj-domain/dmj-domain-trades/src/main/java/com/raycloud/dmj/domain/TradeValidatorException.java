package com.raycloud.dmj.domain;

/**
 * 订单校验异常
 * 异常来源 @link com.raycloud.dmj.domain.TradeValidator
 *
 * <AUTHOR>
 * @Date 2019-10-25
 **/
public class TradeValidatorException extends RuntimeException {

    /**
     * 波次异常类型
     */
    public static final String EXCEPTION_TYPE = "TRADE_VALIDATOR";

    private Integer errCode;

    private String errMsg;

    public TradeValidatorException(Exception e) {
        super(e);
    }

    public TradeValidatorException(String msg) {
        super(msg);
    }

    public TradeValidatorException(String msg, Exception e) {
        super(msg, e);
    }

    public TradeValidatorException(Integer errCode, String errMsg) {
        super(errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public Integer getErrCode() {
        return this.errCode;
    }

    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return this.errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}

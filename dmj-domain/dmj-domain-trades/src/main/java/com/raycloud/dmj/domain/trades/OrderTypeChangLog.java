package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 16:39:59
 */
public class OrderTypeChangLog extends Model {

    private static final long serialVersionUID = 6981801630355558184L;

    private Long id;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 系统子订单号
     */
    private Long orderId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 系统商家编码，如果是商品则为商品的商家编码，如果为SKU则为SKU的商家编码
     */
    private String sysOuterId;

    /**
     * 系统商品ID
     */
    private Long itemSysId;

    /**
     * 规格商品ID
     */
    private Long skuSysId;

    /**
     * 商品类型：2 套件商品，combine_id=0是套件自身，combine_id>0是套件单品
     */
    private Integer type;

    /**
     * 套件关系ID，套件单品对应套件自身的orderId
     */
    private Long combineId;

    /**
     * 创建时间
     */
    private Date created;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getSysOuterId() {
        return sysOuterId;
    }

    public void setSysOuterId(String sysOuterId) {
        this.sysOuterId = sysOuterId;
    }

    public Long getItemSysId() {
        return itemSysId;
    }

    public void setItemSysId(Long itemSysId) {
        this.itemSysId = itemSysId;
    }

    public Long getSkuSysId() {
        return skuSysId;
    }

    public void setSkuSysId(Long skuSysId) {
        this.skuSysId = skuSysId;
    }

    public Long getCombineId() {
        return combineId;
    }

    public void setCombineId(Long combineId) {
        this.combineId = combineId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }
}

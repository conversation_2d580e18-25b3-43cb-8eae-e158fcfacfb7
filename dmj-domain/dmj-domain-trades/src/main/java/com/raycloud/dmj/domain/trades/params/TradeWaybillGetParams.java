package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.WaybillGetOperationEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 自动获取单号参数
 *
 * @author: qingfeng.cxb
 * @create: 2019-05-27 20:45
 */
public class TradeWaybillGetParams implements Serializable {

    private static final long serialVersionUID = 2983127080963750962L;

    /**
     * 公司
     */
    private Staff staff;

    /**
     * ip
     */
    private String ip;

    /**
     * 是否需要上传发货
     */
    private Boolean needSend;

    /**
     * 订单编号串
     */
    private List<Long> sids;

    /**
     * 触发操作
     */
    private WaybillGetOperationEnum operation;

    /**
     * 波次号
     */
    private List<Long> waveIds;

    private ProgressEnum progress;

    public Staff getStaff() {
        return staff;
    }

    public TradeWaybillGetParams setStaff(Staff staff) {
        this.staff = staff;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public TradeWaybillGetParams setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public List<Long> getSids() {
        return sids;
    }

    public TradeWaybillGetParams setSids(List<Long> sids) {
        this.sids = sids;
        return this;
    }

    public Boolean getNeedSend() {
        return needSend;
    }

    public TradeWaybillGetParams setNeedSend(Boolean needSend) {
        this.needSend = needSend;
        return this;
    }

    public WaybillGetOperationEnum getOperation() {
        return operation;
    }

    public TradeWaybillGetParams setOperation(WaybillGetOperationEnum operation) {
        this.operation = operation;
        return this;
    }

    public List<Long> getWaveIds() {
        return waveIds;
    }

    public TradeWaybillGetParams setWaveIds(List<Long> waveIds) {
        this.waveIds = waveIds;
        return this;
    }

    public ProgressEnum getProgress() {
        return progress;
    }

    public TradeWaybillGetParams setProgress(ProgressEnum progress) {
        this.progress = progress;
        return this;
    }
}

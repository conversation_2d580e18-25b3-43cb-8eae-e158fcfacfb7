package com.raycloud.dmj.domain.trades.tradepay.params;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chen<PERSON><PERSON><PERSON>
 * @Date: 2020/3/20 2:43 下午
 */
public class TradePayGetParams implements Serializable {

    private static final long serialVersionUID = 5728178760186369719L;
    /**
     * 系统单号
     */
    private Long sid;
    /**
     * 系统单号s
     */
    private List<Long> sids;
    /**
     * 支付单id
     */
    private String payId;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }
}

package com.raycloud.dmj.domain.trades;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class DigestCorrelationInfo implements Serializable {
    private static final long serialVersionUID = 8300347044037886297L;

    public static final Integer ADDRESS = 1;
    public static final Integer CHINESE_NAME = 2;
    public static final Integer MOBILE_PHONE = 3;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 密文
     */
    private String cipherText;
    /**
     * 明文
     */
    private String plainText;
    /**
     * 脱敏
     */
    private String maskText;
    /**
     * 是否支持索引
     */
    private Boolean supportIndex;
    /**
     * 类型；1：Address，2：ChineseName，3：MobilePhone
     */
    private Integer dataType;

    public DigestCorrelationInfo() {
    }

    public DigestCorrelationInfo(String orderId, String cipherText) {
        this.orderId = orderId;
        this.cipherText = cipherText;
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;

/**
 * 交易日志查询参数
 *
 * <AUTHOR>
 */
public class TradeTraceQueryParams implements Serializable {
    private static final long serialVersionUID = 5796798817620171573L;
    /**
     * 系统订单号
     * 多个以逗号隔开
     */
    private String sids;

    /**
     * 起始时间
     */
    private Long operateTimeStart;

    /**
     * 截止时间
     */
    private Long operateTimeEnd;

    /**
     * 操作人
     * 多个以逗号隔开
     */
    private String operators;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 操作备注
     */
    private String content;
    /**
     * 分页
     */
    private Page page;

    /**
     * ERP的Trade流水短号，系统生成，公司维度唯一
     * 多个以逗号隔开
     */
    private String  shortIds;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getShortIds() {
        return shortIds;
    }

    public void setShortIds(String shortIds) {
        this.shortIds = shortIds;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public Long getOperateTimeStart() {
        return operateTimeStart;
    }

    public void setOperateTimeStart(Long operateTimeStart) {
        this.operateTimeStart = operateTimeStart;
    }

    public Long getOperateTimeEnd() {
        return operateTimeEnd;
    }

    public void setOperateTimeEnd(Long operateTimeEnd) {
        this.operateTimeEnd = operateTimeEnd;
    }

    public String getOperators() {
        return operators;
    }

    public void setOperators(String operator) {
        this.operators = operator;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}

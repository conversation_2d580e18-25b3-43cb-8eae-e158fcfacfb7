package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * 发货记录，保存发货中和发货失败的记录
 *
 * <AUTHOR>
 */
@Table(name = "consign_record", routerKey = "tradeDbNo")
public class ConsignRecord extends Model {

    private static final long serialVersionUID = -2805030325724730140L;

    /**
     * 公司编号
     */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 系统订单号
     */
    @Column(name = "sid", type = Types.BIGINT, key = true)
    private Long sid;

    /**
     * 平台订单号
     */
    @Column(name = "tid", type = Types.VARCHAR)
    private String tid;

    /**
     * 本次发货时参与发货的子订单ID,以逗号分割. 一笔订单发货时,有些子订单处于已发货或已发货之后的状态这些子订单不再参与发货
     */
    private String orderIds;

    /**
     * 订单发货时间
     */
    @Column(name = "consigned", type = Types.TIMESTAMP)
    private Date consigned;

    /**
     * 最近一次上传时间
     */
    @Column(name = "upload_time", type = Types.TIMESTAMP)
    private Date uploadTime;

    /**
     * 运单号
     */
    @Column(name = "out_sid", type = Types.VARCHAR)
    private String outSid;

    /**
     * 订单来自哪个平台
     */
    private String source;

    /**
     * 订单快递模版ID
     */
    @Column(name = "template_id", type = Types.BIGINT)
    private Long templateId;

    /**
     * 订单快递模版类型，0表示普通，1表示电子面单
     */
    @Column(name = "template_type", type = Types.TINYINT)
    private Integer templateType;

    /**
     * 当时的快递模版名称
     */
    @Column(name = "template_name", type = Types.VARCHAR)
    private String templateName;

    /**
     * 发货类型 OFFLINE ONLINE DUMMY
     */
    @Column(name = "consign_type", type = Types.VARCHAR)
    private String consignType;

    /**
     * 当前记录创建时间
     */
    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    /**
     * 0 正常
     * 1 上传异常
     * 2 延迟上传
     * 3 上传失败（上传中被过滤了，需要日志单独记录但不需要标记异常的）
     */
    @Column(name = "is_error", type = Types.TINYINT)
    private Integer isError;

    /**
     * 上传异常
     */
    private Integer isUploadError;

    /**
     * 上传失败的异常信息
     */
    @Column(name = "error_desc", type = Types.VARCHAR)
    private String errorDesc;

    @Column(name = "enable_status", type = Types.TINYINT)
    private Integer enableStatus;

    /**
     * 订单所属店铺的用户ID
     */
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id", type = Types.BIGINT)
    private Long warehouseId;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name", type = Types.VARCHAR)
    private String warehouseName;

    /**
     * 发货时用户的订购类型，与company.orderType保持一致
     */
    @Column(name = "buy_type", type = Types.TINYINT)
    private Integer buyType;

    /**
     * 订单付款时间快照
     */
    @Column(name = "trade_pay", type = Types.TIMESTAMP)
    private Date tradePay;

    /**
     * 合单的订单设置主单的sid
     */
    @Column(name = "merge_sid", type = Types.BIGINT)
    private Long mergeSid;

    /**
     * 是否是供销订单
     */
    private Integer tradeType;

    /**
     * 物流状态
     *
     * @return
     */
    private String logisticsStatus;

    /**
     * 物流异常类型
     *
     * @return
     */
    private String logisticsExceptType;

    /**
     * 物流云订阅状态  默认0 未订阅  1已订阅 2订阅失败
     *
     * @return
     */
    private Integer wlySubscribeFlag;

    /**
     * 物流最近更新时间
     *
     * @return
     */
    private Date logisticsModified;

    /**
     * 空单标记,前端显示用
     *
     * @return
     */
    private Integer scalping;

    /**
     * 重试时间
     */
    @Column(name = "retry_time", type = Types.TIMESTAMP)
    private Date retryTime;

    /**
     * 重试次数
     */
    @Column(name = "retry_count", type = Types.TINYINT)
    private Integer retryCount;

    /**
     * 错误类型
     * 其实就是把所有平台报错、系统报错做了一个归类，相同类型的报错给以统一的描述方便筛查
     * 因为需要支持用户自定义没有做成Enum类型
     */
    @Column(name = "error_type", type = Types.BIGINT)
    private Long errorType;

    @Column(name = "is_cut", type = Types.TINYINT)
    private Integer isCut;

    /**
     * 错误类型对应的中文描述（非持久化字段）
     */
    private String errorTypeDesc;

    /**
     * 各平台状态映射后的统一状态, 用于前端展示（KMERP-134401）
     */
    private String unifiedStatus;

    /**
     * 快递公司id
     */
    private Long logisticsCompanyId;

    /**
     * 快递公司id
     */
    private String logisticsCompanyName;

    /**
     * 是否通过新版上传服务发货上传
     */
    private boolean uploadByService;

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public ConsignRecord setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public Long getSid() {
        return sid;
    }

    public ConsignRecord setSid(Long sid) {
        this.sid = sid;
        return this;
    }

    public String getTid() {
        return tid;
    }

    public ConsignRecord setTid(String tid) {
        this.tid = tid;
        return this;
    }

    public String getOrderIds() {
        return orderIds;
    }

    public ConsignRecord setOrderIds(String orderIds) {
        this.orderIds = orderIds;
        return this;
    }

    public Date getConsigned() {
        return consigned;
    }

    public ConsignRecord setConsigned(Date consigned) {
        this.consigned = consigned;
        return this;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public ConsignRecord setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
        return this;
    }

    public String getOutSid() {
        return outSid;
    }

    public ConsignRecord setOutSid(String outSid) {
        this.outSid = outSid;
        return this;
    }

    public String getSource() {
        return source;
    }

    public ConsignRecord setSource(String source) {
        this.source = source;
        return this;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public ConsignRecord setTemplateId(Long templateId) {
        this.templateId = templateId;
        return this;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public ConsignRecord setTemplateType(Integer templateType) {
        this.templateType = templateType;
        return this;
    }

    public String getTemplateName() {
        return templateName;
    }

    public ConsignRecord setTemplateName(String templateName) {
        this.templateName = templateName;
        return this;
    }

    public String getConsignType() {
        return consignType;
    }

    public ConsignRecord setConsignType(String consignType) {
        this.consignType = consignType;
        return this;
    }

    public Date getCreated() {
        return created;
    }

    public ConsignRecord setCreated(Date created) {
        this.created = created;
        return this;
    }

    public Integer getIsError() {
        return isError;
    }

    public ConsignRecord setIsError(Integer isError) {
        this.isError = isError;
        return this;
    }

    public Integer getIsUploadError() {
        return isUploadError;
    }

    public void setIsUploadError(Integer isUploadError) {
        this.isUploadError = isUploadError;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public ConsignRecord setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
        return this;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public ConsignRecord setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public ConsignRecord setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public ConsignRecord setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public ConsignRecord setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        return this;
    }

    public Integer getBuyType() {
        return buyType;
    }

    public ConsignRecord setBuyType(Integer buyType) {
        this.buyType = buyType;
        return this;
    }

    public Date getTradePay() {
        return tradePay;
    }

    public ConsignRecord setTradePay(Date tradePay) {
        this.tradePay = tradePay;
        return this;
    }

    public Long getMergeSid() {
        return mergeSid;
    }

    public ConsignRecord setMergeSid(Long mergeSid) {
        this.mergeSid = mergeSid;
        return this;
    }

    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public Integer getWlySubscribeFlag() {
        return wlySubscribeFlag;
    }

    public void setWlySubscribeFlag(Integer wlySubscribeFlag) {
        this.wlySubscribeFlag = wlySubscribeFlag;
    }

    public Date getLogisticsModified() {
        return logisticsModified;
    }

    public void setLogisticsModified(Date logisticsModified) {
        this.logisticsModified = logisticsModified;
    }

    public Integer getScalping() {
        return scalping;
    }

    public void setScalping(Integer scalping) {
        this.scalping = scalping;
    }

    public Date getRetryTime() {
        return retryTime;
    }

    public void setRetryTime(Date retryTime) {
        this.retryTime = retryTime;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Long getErrorType() {
        return errorType;
    }

    public void setErrorType(Long errorType) {
        this.errorType = errorType;
    }

    public Integer getIsCut() {
        return isCut;
    }

    public void setIsCut(Integer isCut) {
        this.isCut = isCut;
    }

    public String getErrorTypeDesc() {
        return errorTypeDesc;
    }

    public void setErrorTypeDesc(String errorTypeDesc) {
        this.errorTypeDesc = errorTypeDesc;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public boolean isUploadByService() {
        return uploadByService;
    }

    public void setUploadByService(boolean uploadByService) {
        this.uploadByService = uploadByService;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/1/11
 * @Description
 */
public class WipeEmojiUtils {
    /**
     * 去除emoji表情
     * @param str
     * @return
     */
    public static String wipeEmoji(String str){
        if (StringUtils.isBlank(str)){
            return str;
        }
        final int LAST_BMP = 0xFFFF;
        StringBuilder sb = new StringBuilder(str.length());
        //count=表示非emoji，count=1表示为emoji
        Integer count = 0;
        for (int i = 0; i < str.length(); i++) {
            int codePoint = str.codePointAt(i);
            if (codePoint < LAST_BMP && count == 0) {
                sb.appendCodePoint(codePoint);
            } else {
                if (codePoint < LAST_BMP && count ==1){
                    count = 0;
                }else {
                    count = 1;
                }
                continue;
            }
        }
        return sb.toString();
    }
}

package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class MemoQueryRenderer implements IFieldValueRenderer {

    /**
     * 留言备注标注
     * 0 无买家留言和卖家备注
     * 1 有买家留言
     * 2 有卖家备注
     * 3 有买家留言或卖家备注
     * 4 有未处理留言或备注
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        int type = (Integer)originValue;
        switch (type){
            case 0:return "无买家留言和卖家备注";
            case 1:return "有买家留言";
            case 2:return "有卖家备注";
            case 3:return "有买家留言或卖家备注";
            case 4:return "有未处理留言或备注";

            default: return String.valueOf(type);
        }
    }
}

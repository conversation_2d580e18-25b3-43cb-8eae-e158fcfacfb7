package com.raycloud.dmj.domain.replace;

import com.raycloud.dmj.domain.enums.ItemReplaceStockEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @Description itemStock
 * @Date 2021/12/14 16:32
 * @Created 杨恒
 */
@Getter
@Setter
public class ReplaceItemStock {

    private Long warehouseId;

    private Long sysItemId;

    private Long sysSkuId;

    private String itemKey;

    /**
     * @see ItemReplaceStockEnum#AVAILABLE_STOCK
     */
    private Long availableStock;

    /**
     * @see ItemReplaceStockEnum#AVAILABLE_STOCK_SUM
     */
    private Long availableStockSum;

    /**
     * @see ItemReplaceStockEnum#VIRTUAL_STOCK
     */
    private Long virtualStock;

    /**
     * defective_stock  次品库存
     */
    private Long defectiveStock = 0L;

    List<ReplaceItemBatchStock> replaceItemBatchStocks;

    /**
     * 原商品已使用库存
     * 用于判断待更换商品不缺货不执行更换，缺货执行更换 自动执行的时候判断库存
     */
    private Long originItemUseStock = 0L;

    public ReplaceItemStock() {

    }

    public ReplaceItemStock(Long warehouseId, Long sysItemId, Long sysSkuId, String itemKey, Long availableStock, Long availableStockSum, Long virtualStock, Long defectiveStock) {
        this.warehouseId = warehouseId;
        this.sysItemId = sysItemId;
        this.sysSkuId = sysSkuId;
        this.itemKey = itemKey;
        this.availableStock = availableStock;
        this.availableStockSum = availableStockSum;
        this.virtualStock = virtualStock;
        this.defectiveStock = defectiveStock;
    }

    @Data
    public static class ReplaceItemBatchStock {
        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 生产日期
         */
        private Date productionDate;


        /**
         * 入库时间
         */
        private Date receiptTime;

        /**
         * 到期时间
         */
        private Date expireTime;

        /**
         * 库存数量
         * 查询业务中，数量为批次库存数量
         */
        private Long num = 0L;


        /**
         * 库存可用数量
         */
        private Long availableStock = 0L;


        /**
         * 已锁定的批次库存数
         */
        private Long locked = 0L;

        /**
         * 是否还有剩余库存
         */
        private boolean hasResidualInventory;

        private Long replaceItemId;

        public boolean isHasResidualInventory() {
            return num > locked;
        }

        public Long getAvailableStock() {
            return num - locked;
        }

    }
}

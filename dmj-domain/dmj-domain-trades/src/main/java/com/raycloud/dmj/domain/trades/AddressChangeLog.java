package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 17/3/10.
 * 订单同步的时候，对非覆盖模式下地址发生变化的记录
 */
@Table(name = "address_change_log", routerKey = "addressChangeLogDbNo")
public class AddressChangeLog extends Model {

    private static final long serialVersionUID = -8713240943164837245L;
    /**
     * 日志主键
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 上一次订单在系统中的收件地址
     */
    private String preAddress;

    /**
     * 当前订单在系统中的收件地址
     */
    private String curAddress;

    /**
     * 新增时间
     */
    private Date insertTime;

    /**
     * 是否可用
     */
    private Integer enableStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getPreAddress() {
        return preAddress;
    }

    public void setPreAddress(String preAddress) {
        this.preAddress = preAddress;
    }

    public String getCurAddress() {
        return curAddress;
    }

    public void setCurAddress(String curAddress) {
        this.curAddress = curAddress;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public static AddressChangeLog trade2Log(Staff staff, Trade existTrade, Trade toUpdate) {
        AddressChangeLog log = new AddressChangeLog();
        log.setCompanyId(staff.getCompanyId());
        log.setSid(existTrade.getSid());
        log.setPreAddress(encodeAddress(existTrade));
        log.setCurAddress(encodeAddress(toUpdate));
        log.setDbNo(staff.getDbInfo().getAddressChangeLogDbNo());
        return log;
    }

    public static String encodeAddress(Trade trade) {
        String address = new StringBuilder().append(StringUtils.trimToEmpty(trade.getReceiverName()))
                .append(StringUtils.trimToEmpty(trade.getReceiverPhone())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverMobile())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverZip())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverState())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverCity())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverDistrict())).append("_")
                .append(StringUtils.trimToEmpty(trade.getReceiverAddress())).toString();
        return TradeUtils.removeUtf8mb4(address);
    }

}

package com.raycloud.dmj.domain.logistics.request;

import java.io.Serializable;

/**
 * 订阅物流请求参数
 */
public class SubscribeLogisticsRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 订单Id
     */
    private Long sid;
    /**
     * 运单号
     */
    private String outSid;
    /**
     * 快递ID
     */
    private Long expressId;
    /**
     * 快递名称
     */
    private String expressName;


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Long getExpressId() {
        return expressId;
    }

    public void setExpressId(Long expressId) {
        this.expressId = expressId;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }
}

package com.raycloud.dmj.domain.trades;

import org.apache.commons.lang3.StringUtils;

/**
 * ConfigLog
 *
 * <AUTHOR>
 * @Date 2019-06-18
 * @Time 20:42
 */
public class ConfigLog {
    private StringBuilder log = new StringBuilder();

    public StringBuilder getLog() {
        return log;
    }

    public ConfigLog append(String title, Object newValue, Object oldValue){
        if (StringUtils.isBlank(title)) {
            return this;
        }
        String s = String.valueOf(newValue).equals("null") ? "" : String.valueOf(newValue);
        String s1 = String.valueOf(oldValue).equals("null") ? "" : String.valueOf(oldValue);
        if (!s.equals(s1)) {
            getLog().append(getLog().length() > 0 ? "; " : "").append(title).append(":").append(s1).append("->").append(s);
        }
        return this;
    }
}

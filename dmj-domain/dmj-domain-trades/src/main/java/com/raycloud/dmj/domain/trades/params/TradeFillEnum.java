package com.raycloud.dmj.domain.trades.params;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-11-21
 */
public enum TradeFillEnum {


    TRADE_DISTRIBUTOR_FILL("com.raycloud.dmj.services.trades.fill.TradeDistributorFill","平台经销分销信息"),
    TRADE_EXCEPT_FILL("com.raycloud.dmj.services.trades.fill.TradeExceptFill","订单异常填充"),
    TRADE_EXT_FILL("com.raycloud.dmj.services.trades.fill.TradeExtFill","tradeExt字段填充"),
    TRADE_INVOICE_FILL("com.raycloud.dmj.services.trades.fill.TradeInvoiceFill","订单发票信息填充"),
    TRADE_ORIGIN_SOURCE_FILL("com.raycloud.dmj.services.trades.fill.TradeOriginSourceFill","拆单原始source填充"),
    TRADE_PLATFORM_RECOMMENDATION_FILL("com.raycloud.dmj.services.trades.fill.TradePlatformRecommendationFill","平台仓配建议信息填充"),
    TRADE_SALESMAN_FILL("com.raycloud.dmj.services.trades.fill.TradeSalesmanFill","业务员信息填充"),
    TRADE_SUPPLIER_FILL("com.raycloud.dmj.services.trades.fill.TradeSupplierFill","供应商信息填充"),
    TRADE_TAG_FILL("com.raycloud.dmj.services.trades.fill.TradeTagFill","订单标签信息填充"),
    TRADE_TYPE_FILL("com.raycloud.dmj.services.trades.fill.TradeTypeFill","订单tradeTypeMap字段填充"),
    TRADE_USER_SOURCE_FILL("com.raycloud.dmj.services.trades.fill.TradeUserSourceFill","对应店铺source填充"),

    ORDER_EXCEPT_FILL("com.raycloud.dmj.services.trades.fill.OrderExceptFill","商品异常填充"),
    TRADE_ORDER_CAIGOU_FILL("com.raycloud.dmj.services.trades.fill.TradeOrderCaigouFill","商品采购信息填充"),
    TRADE_ORDER_PRODUCT_FILL("com.raycloud.dmj.services.trades.fill.TradeOrderProductFill","商品批次信息填充"),
    TRADE_ORDER_SYNC_ITEM_TAG_FILL("com.raycloud.dmj.services.trades.fill.TradeOrderSyncItemTagFill","商品标签填充"),

    ;

    String clazz;

    String desc;

    TradeFillEnum(String clazz, String desc) {
        this.clazz = clazz;
        this.desc = desc;
    }

    public String getClazz() {
        return clazz;
    }

    public String getDesc() {
        return desc;
    }
}

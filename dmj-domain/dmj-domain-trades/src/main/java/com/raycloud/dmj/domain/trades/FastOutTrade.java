package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 快速出库
 * <AUTHOR>
 * @date 2018/6/21 20:33
 */
public class FastOutTrade extends TbTrade implements Serializable {

    public static final String PAYMENT_TYPE_CASH = "cash";

    public static final String PAYMENT_TYPE_BOOK = "book";

    public static final String PAYMENT_TYPE_PART = "part";

    /**
     * 出库类型 现出
     */
    public static final String OUT_TYPE_NOW = "now";

    /**
     * 出库类型 自提
     */
    public static final String OUT_TYPE_SELF = "self";

    /**
     * 出库类型 邮寄
     */
    public static final String OUT_TYPE_POST = "post";

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 支付类型 cash现付 book记账 part部分收款
     */
    private String paymentType;

    /**
     * 支付方式 cash现金 card刷卡 alipay支付宝 wechatpay微信支付
     */
    private String paymentMethod;

    /**
     * 出库类型 now现出 self自提 post邮寄
     */
    private String outType;

    /**
     * 已付金额
     */
    private String paidFee;

    private Long staffId;

    private String staffName;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getOutType() {
        return outType;
    }

    public void setOutType(String outType) {
        this.outType = outType;
    }

    public String getPaidFee() {
        return paidFee;
    }

    public void setPaidFee(String paidFee) {
        this.paidFee = paidFee;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }
}

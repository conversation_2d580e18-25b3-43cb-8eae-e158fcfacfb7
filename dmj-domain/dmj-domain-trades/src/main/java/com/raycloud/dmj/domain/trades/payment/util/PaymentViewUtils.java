package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-06-12
 */
public class PaymentViewUtils {

    private  static final Logger logger = Logger.getLogger(PaymentViewUtils.class);

    public static JSONObject getTradePaymentView(Staff staff, TbTrade tbTrade, List<Trade> subTrades, Integer grossProfitConfig){
        try {
            JSONObject trade = getTradeJson(tbTrade,subTrades,grossProfitConfig);
            JSONObject jsonObject = PaymentChecker.checkTradePayment(tbTrade,null);
            if (jsonObject != null) {
                trade.put("错误信息",jsonObject);
            }

            boolean merge = TradeUtils.isMerge(tbTrade) && Objects.equals(tbTrade.getSid(), tbTrade.getMergeSid());
            if (merge) {
                JSONArray array = new JSONArray();
                trade.put("合单子订单",array);
                if (CollectionUtils.isNotEmpty(subTrades)) {
                    for (Trade subTrade : subTrades) {
                        JSONObject sub = getTradeJson((TbTrade) subTrade,null,grossProfitConfig);
                        JSONObject err = PaymentChecker.checkTradePayment(subTrade,null);
                        if (err != null) {
                            sub.put("错误信息",err);
                        }
                        array.add(sub);
                    }
                }
            }

            return trade;
        }catch (Throwable e){
            new PaymentLogBuilder(staff).appendTrade(tbTrade).append("getTradePaymentView 转换错误 ").printError(logger,e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sid",tbTrade == null? null:tbTrade.getSid());
            jsonObject.put("错误信息",e.getMessage());
            return jsonObject;
        }
    }

    private static JSONObject getTradeJson(TbTrade tbTrade, List<Trade> subTrades,Integer grossProfitConfig) {
        JSONObject trade = new JSONObject(true);

        boolean isMainTrade = TradeUtils.isMerge(tbTrade) && Objects.equals(tbTrade.getSid(), tbTrade.getMergeSid());

        List<Order> list = TradeUtils.getOrders4Trade(tbTrade);
        BigDecimalWrapper orderDivideOrderFees = new BigDecimalWrapper();
        BigDecimalWrapper orderDiscounts = new BigDecimalWrapper();
        BigDecimalWrapper orderTotals = new BigDecimalWrapper();

        for (Order tbOrder : list) {
            if (tbOrder.getCombineId() != null && tbOrder.getCombineId() > 0L) {
                continue;
            }
            if (tbOrder.getEnableStatus() != null && tbOrder.getEnableStatus() == 0){
                continue;
            }
            if (Objects.equals(tbOrder.getSid(), tbTrade.getSid())) {
                orderDiscounts.add(tbOrder.getDiscountFee());
                orderTotals.add(tbOrder.getTotalFee());
            }

            if (Objects.equals(tbOrder.getSid(), tbTrade.getSid()) || isMainTrade) {
                orderDivideOrderFees.add(tbOrder.getDivideOrderFee());
            }
        }


        trade.put("基本信息","sid:" + tbTrade.getSid() + " tid:" + tbTrade.getTid()+ " userId:" + tbTrade.getUserId()+ " source:" + tbTrade.getSource()+ " subSource:"
                + tbTrade.getSubSource()+ " sysStatus:" + tbTrade.getSysStatus() + " status:" + tbTrade.getStatus());
        trade.put("金额信息",(isMainTrade?"[main]":"")+"应付金额(payment):"+tbTrade.getPayment()+" 买家已付金额(payAmount):"+tbTrade.getPayAmount()+ " 成本:"+tbTrade.getCost()+ " 分销金额:"+tbTrade.getSaleFee());
        trade.put("订单优惠","订单优惠["+tbTrade.getDiscountFee()+"]= 商品总金额["+tbTrade.getTotalFee()+"(合计值:"+orderTotals.getString()+")] - ∑商品优惠["+orderDiscounts.getString()+"] + 税费["+tbTrade.getTaxFee()+"] + 运费["+tbTrade.getPostFee()+"] - 应付金额["+tbTrade.getPayment()+"]");


        if (grossProfitConfig != null) {
            boolean byDivideOrderFee = Objects.equals(grossProfitConfig, 1);
            StringBuilder postFee = new StringBuilder(String.valueOf(tbTrade.getPostFee()));
            StringBuilder payment = new StringBuilder(String.valueOf(tbTrade.getPayment()));

            boolean check = true;
            if (isMainTrade) {
                if (CollectionUtils.isNotEmpty(subTrades)) {
                    for (Trade subTrade : subTrades) {
                        postFee.append("+").append(subTrade.getPostFee());
                        payment.append("+").append(subTrade.getPayment());
                    }
                }else {
                    trade.put("订单毛利","订单毛利["+tbTrade.getGrossProfit()+"]:子订单缺失无法检查 ");
                    check = false;
                }
            }

            BigDecimalWrapper cost = new BigDecimalWrapper();
            for (Order tbOrder : list) {
                if (tbOrder.getCombineId() != null && tbOrder.getCombineId() > 0L) {
                    continue;
                }
                if (tbOrder.getEnableStatus() != null && tbOrder.getEnableStatus() == 0){
                    continue;
                }
                cost.addMulti(tbOrder.getCost(),tbOrder.getNum());
            }

            if (check) {
                //订单毛利 = (∑divideOrderFee + ∑trade运费) - 主单cost - 主单PackmaCost - 主单actualPostFee
                //       or   ∑tradePayment               - 主单cost - 主单PackmaCost - 主单actualPostFee

                String pay =  byDivideOrderFee?
                        ("∑商品平台实付(divideOrderFee)["+orderDivideOrderFees.getString()+"] + ∑运费["+postFee.toString()+"]"  ):
                        ("∑订单应付金额["+payment.toString() + "]");

                String post = tbTrade.getActualPostFee() == null?
                        ("理论运费(实际运费为空以理论运费计算)["+tbTrade.getTheoryPostFee()+"]"):
                        ("实际运费["+tbTrade.getActualPostFee()+"]");

                trade.put("订单毛利","订单毛利["+tbTrade.getGrossProfit()+"]= " + pay + " - 成本["+tbTrade.getCost()+"(合计值:"+cost.getString()+")] - 包材费用["+tbTrade.getPackmaCost()+"] - " + post);
            }

        }else {
            trade.put("订单毛利","订单毛利["+tbTrade.getGrossProfit()+"]:grossProfitConfig 缺失无法检查 ");
        }


        JSONArray orderArray = new JSONArray();
        Map<Order,JSONObject> orders = new LinkedHashMap<>();
        Map<Long,List<JSONObject>> suits = new HashMap<>();

        for (Order tbOrder : list) {
            if (tbOrder.getCombineId() != null && tbOrder.getCombineId() > 0L) {
                JSONObject order = getOrderJson(tbTrade, tbOrder);
                suits.computeIfAbsent(tbOrder.getCombineId(),x->new ArrayList<>()).add(order);
            }
            if (tbOrder.getEnableStatus() != null && tbOrder.getEnableStatus() == 0){
                continue;
            }
            if (tbOrder.getSid() != null && !Objects.equals(tbOrder.getSid(), tbTrade.getSid())) {
                continue;
            }
            JSONObject order = getOrderJson(tbTrade, tbOrder);
            orders.put(tbOrder,order);
        }

        for (Map.Entry<Order, JSONObject> entry : orders.entrySet()) {
            orderArray.add(entry.getValue());
            Long id = entry.getKey().getId();
            if (suits.containsKey(id)) {
                for (JSONObject jsonObject : suits.get(id)) {
                    orderArray.add(jsonObject);
                }
            }
        }
        trade.put("商品信息",orderArray);

        return trade;
    }

    private static JSONObject getOrderJson(TbTrade tbTrade, Order tbOrder) {
        JSONObject order = new JSONObject(true);
        String sysOuterId = tbOrder.getSysOuterId();
        if (StringUtils.isBlank(sysOuterId)) {
            sysOuterId = tbOrder.getOuterId();
            if (StringUtils.isBlank(sysOuterId)) {
                sysOuterId = tbOrder.getOuterIid();
            }
            sysOuterId= "[平]" + sysOuterId;
        }
        StringBuilder s = new StringBuilder("["+ sysOuterId +"]");
        s.append(" 商品金额:").append(tbOrder.getPrice()).append("*").append(tbOrder.getNum()).append("=").append(tbOrder.getTotalFee());
        s.append(" 成交金额(payment):").append(tbOrder.getPayment());
        if (!MathUtils.equalsZero(tbOrder.getDiscountFee())) {
            s.append(" 商品优惠:").append(tbOrder.getDiscountFee());
        }
        s.append(" 买家已付金额(payAmount):").append(tbOrder.getPayAmount());
        s.append(" 平台实付(divideOrderFee):").append(tbOrder.getDivideOrderFee());
        s.append(" 分销价格:").append(tbOrder.getSalePrice());
        s.append(" 成本:").append(tbOrder.getCost()).append("*").append(tbOrder.getNum()).append("=").append(MathUtils.toString(MathUtils.multiply(tbOrder.getCost(), tbOrder.getNum())));

        if (!Objects.equals(tbTrade.getSid(), tbOrder.getSid())) {
            s.append(" 子订单sid:").append(tbOrder.getSid());
        }

        if (tbOrder.getCombineId() != null && tbOrder.getCombineId() > 0L) {
            order.put(" [子商品]"+ tbOrder.getId(),s.toString());
        }else {
            order.put(String.valueOf(tbOrder.getId()),s.toString());
        }

        return order;
    }
}

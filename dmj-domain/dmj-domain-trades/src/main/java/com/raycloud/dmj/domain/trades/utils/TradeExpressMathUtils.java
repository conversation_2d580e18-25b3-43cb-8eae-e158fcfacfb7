package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.express.ExpressSmartMatch;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.trades.MessageMemo;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/3/3 5:25 下午
 * @Description:
 */
public class TradeExpressMathUtils {

    /**
     * 获取订单指定物流信息
     *
     * @param tradeExt
     * @return List<erpExpressId> erp物流公司id
     */
    public static List<String> getTradePointExpressIds(TradeExt tradeExt, String source, Map<String, List<String>> expressInfoMap, boolean fxgSpecifyLogistics) {
        List<String> pointExpress = new ArrayList<>();
        if (tradeExt != null && StringUtils.isNotBlank(tradeExt.getLogisticsCode())) {
            String pointExpressKey = tradeExt.getLogisticsCode();
            pointExpress = Arrays.asList(pointExpressKey.split(","));
            if (Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_PDD)
                    || Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_POISON)
                    || Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_QIMEN)
                    || fxgSpecifyLogistics) {
                List<String> expressIds = new ArrayList<>();
                for (String pointCode : pointExpress) {
                    List<String> list = expressInfoMap.get(pointCode);
                    if (CollectionUtils.isNotEmpty(list)) {
                        expressIds.addAll(list);
                    }
                }
                pointExpress = expressIds;
                if (CollectionUtils.isEmpty(pointExpress)) {
                    Logs.ifDebug(
                            String.format("快递匹配失败,指定物流匹配失败 companyId=%s，sid=%s，logisticsCode=%s，expressInfoMap=%s",
                                    tradeExt.getCompanyId(), tradeExt.getSid(), pointExpressKey, expressInfoMap));
                }
            }
        }
        return pointExpress;
    }


    public static boolean isMatchTmAppointExpress(Staff staff, Trade trade, ExpressSmartMatch expressSmartMatch, Map<String, List<String>> expressInfoMap, Map<Long, UserLogisticsCompany> idAndLogisticsCompanyMap, boolean useNewTemplate) {
        //需要交易提供方法 是否天猫指定物流
        if (StringUtils.isEmpty(trade.getTagIds())) {
            return true;
        }
        if (!trade.getTagIds().contains("1000000785")) {
            return true;
        }
        //订单的code
        String logisticsCode = trade.getTradeExt().getLogisticsCode();
        if (StringUtils.isBlank(logisticsCode) || "noCode".equals(logisticsCode)) {
            List<MessageMemo> messageMemos = trade.getMessageMemos();
            if (CollectionUtils.isEmpty(messageMemos)) {
                return true;
            }
            for (MessageMemo messageMemo : messageMemos) {
                if (StringUtils.isNotBlank(messageMemo.getLogisticsCode()) && !"noCode".equals(logisticsCode)) {
                    logisticsCode = messageMemo.getLogisticsCode();
                    break;
                }
            }
        }
        if (StringUtils.isBlank(logisticsCode) || "noCode".equals(logisticsCode)) {
            return true;
        }
        // 快递公司
        if (useNewTemplate) {
            UserLogisticsCompany userLogisticsCompany = idAndLogisticsCompanyMap.get(expressSmartMatch.getLogisticsCompanyId());
            if (Objects.isNull(userLogisticsCompany)) {
                return false;
            }
            return userLogisticsCompany.getCpCode().contains(logisticsCode);
        } else {
            List<String> expressIds = expressInfoMap.get(logisticsCode);
            if (CollectionUtils.isEmpty(expressIds)) {
                return false;
            }
            return expressIds.contains(String.valueOf(expressSmartMatch.getExpressId()));
        }
    }

    ;

    /**
     * 获取订单指定物流信息Code
     *
     * @param tradeExt
     * @return List<erpExpressId> erp物流公司id
     */
    public static List<String> getTradePointExpressCodes(TradeExt tradeExt, String source, boolean fxgSpecifyLogistics) {
        List<String> pointExpressCode = new ArrayList<>();
        if (tradeExt != null && StringUtils.isNotBlank(tradeExt.getLogisticsCode())) {
            if (Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_PDD)
                    || Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_POISON)
                    || Objects.equals(source, CommonConstants.PLAT_FORM_TYPE_QIMEN)
                    || fxgSpecifyLogistics) {
                pointExpressCode = Arrays.asList(tradeExt.getLogisticsCode().split(","));
            }
        }
        return pointExpressCode;
    }
}

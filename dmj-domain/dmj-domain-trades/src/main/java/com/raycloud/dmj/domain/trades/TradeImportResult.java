package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.TradeSyncExceptContext;
import com.raycloud.dmj.except.enums.ExceptEnum;
import lombok.*;

import java.io.Serializable;
import java.util.*;

/**
 * 订单导入结果对象
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class TradeImportResult implements Serializable {

    private static final long serialVersionUID = -7658136642060987505L;

    public static final String RESULT_TYPE_PARTIAL_SUCCESS = "partial_success";

    private SplitNumContextData contextData;

    /**
     * 新增的订单统计数据
     */
    private Map<String, Integer> inserts = new HashMap<>();

    /**
     * 更新的订单统计数据
     */
    private Map<String, Integer> updates = new HashMap<>();

    private List<Trade> resumes = new ArrayList<>();

    /**
     * 本次同步的店铺ID
     */
    private Long userId;
    /**
     * 本次同步的是哪个平台
     */
    private String platform;
    /**
     * 本次同步的订单中最早下单的那个订单的下单时间与本次同步的订单插入DB的时间之差,单位秒
     */
    private Long delay;
    /**
     * 延迟最长的那个订单的系统订单号
     */
    private Long sid;
    /**
     * 本次同步的订单总数
     */
    private long total;
    /**
     * 本次同步更新的订单总数
     */
    private long updateTotal;
    /**
     * 本次同步插入的订单总数
     */
    private long insertTotal;

    /**
     * 同步订单是否发生异常
     */
    private boolean error;
    /**
     * 异常错误信息
     */
    private String errorMsg;


    /**
     * 导入是否完成
     */
    private boolean complete = false;

    /**
     * 本次下载是否来自订单同步
     */
    private boolean sync;

    /**
     * 本次同步结束后，下一次订单同步的游标时间
     */
    private Date end;

    /**
     * 当前同步开始时间
     */
    private Long start;

    /**
     * 第一页同步订单请求平台消耗的时间
     */
    private Long firstTook;
    /**
     * 记录本次操作类型
     */
    private TradeOperateEnum tradeOperateEnum;

    private boolean forceUpdate = false;

    private boolean updateCache = true;

    /**
     * 第几次同步一整天订单
     */
    private int fullSyncCount;

    /**
     * 本次同步结果类型
     * 主要用于PDD部分订单成功
     */
    private String resultType;

    /**
     * 是否新增链路
     */
    private boolean isInsertChain;

    /**
     * 是否自定义同步（tj，backend接口同步）
     */
    private boolean isCustomSync;

    public List<DmsSupplierInfoDto> dmsSupplierInfoDtos;
    /**
     * 指定物流变更的单的sid
     */
    public Set<Long> logisticsChangeSids=new TreeSet<>();

    public Map<Long, List<TbTrade>> mergeSidToTradeMap = new HashMap<>();

    /**
     * 是否忽略自动合单,默认不忽略
     */

    private boolean ignoreAutoMerge = false;

    private boolean ignoreAutoAudit = false;

    private transient TradeConfig tradeConfig;

    private Map<String, TradeConfigNew> tradeConfigMap;

    private transient TradeUserConfig tradeUserConfig;

    public void setSupplyInfo(List<DmsSupplierInfoDto> dmsSupplierInfoDtos) {
        this.dmsSupplierInfoDtos = dmsSupplierInfoDtos;
    }

    public boolean isAgency() {
        return dmsSupplierInfoDtos != null && !dmsSupplierInfoDtos.isEmpty();
    }

    /**
     * 同步时需要自动反审核的订单sid，由于在链路中不传TradeImportData，故放在result中并且不序列化
     */
    public transient Map<OpEnum, Set<Long>> opEnumAuditUndoSidMap = new HashMap<>();

    public Set<Long> fxSidCancelAuditSet = new HashSet<>();

    public Set<Long> fxSidDelaySyncSet = new HashSet<>();

    public Set<Long> gxTradeStatusChangeSidSet = new HashSet<>();

    public Map<Long, Trade> notifyGxTradeMap = new HashMap<>();

    public Set<Long> gxCancelAuditSidSet = new HashSet<>();

    public Set<Long> needRematchGiftSidSet = new HashSet<>();

    /**
     * 平台修改商品重新匹配赠品
     */
    public Set<Long> platformUpdateItemRematchGiftSidSet = new HashSet<>();

    public Set<Long> bicTradeInitExpressSidSet = new HashSet<>();

    /**
     * 需要退款还流水的供销订单
     */
    public List<Trade> refundGxTrades = new ArrayList<>();

    public Set<Long> gxRecalculatePostFeeSidSet = new HashSet<>();
    /**
     * 需要退款还流水第一次插入的供销订单
     */
    public List<Trade> refundGxInsertTrades = new ArrayList<>();

    public Set<Long> waitPayToWaitAuditMatchWarehouseSids = new HashSet<>();

    // 非待审核
    public Set<Long> needRematchWarehouseSidSet = new HashSet<>();

    // 待审核
    public Set<Long> needRematchWarehouseSidNormalSet = new HashSet<>();

    public Map<EventEnum, Set<Long>> event2needRematchSidSet = new HashMap<>();

    // 非待审核
    public Set<Long> needRematchExpressSidSet = new HashSet<>();

    // 待审核
    public Set<Long> needRematchExpressNormalSet = new HashSet<>();


    public Map<EventEnum, Set<Long>> rematchBusinessEnumListMap= new HashMap<>();


    //修改地址后需要重算的。
    public Set<Long> addressChangeNeedRematchSids = new HashSet<>();


    /**
     * 需要重新匹配标签的订单
     */
    public Set<Long> needRematchTagSidSet = new HashSet<>();

    /**
     * 需要直接审核的订单号
     */
    public Set<Long> needAuditSidSet = new HashSet<>();

    /**
     * 同步时需要部分关闭的订单，由于在链路中不传TradeImportData，故放在result中并且不序列化
     */
    public transient List<Trade> cancelPartRefund = new ArrayList<>();

    /**
     * 商品匹配时，匹配到停用的商品order列表，用于后续根据配置进行停用拆分、标记
     */
    public List<Order> itemInactiveOrders = new ArrayList<>();
    /**
     * 商品匹配时，匹配到下架的商品order列表，用于后续根据配置进行标记
     */
    public List<Order> itemShelfOffOrders = new ArrayList<>();

    public transient Map<Long, List<OrderModifyLog>> orderIdLogsMap;

    /**
     * 商品变动的操作记录，所有的类型
     */
    public transient Map<Long,Map<Integer, List<OrderModifyLog>>> orderIdLogsModifyTpeMap;

    /**
     * 插入enable_status=0的子订单，第一次同步套件转单品
     */
    public transient List<Order> insertDeletes = new ArrayList<>();

    /**
     * 更新enable_status=0的子订单，第二次同步套件转单品（待付款变成待审核）
     */
    public transient List<Order> updateDeletes = new ArrayList<>();

    /**
     * 待审核订单修改收件人信息，重新计算异常和tag
     */
    public Set<Long> needRemarkException = new HashSet<>();

    /**
     * 需要审核且发货
     */
    public Set<Long> auditConsignSids = new HashSet<>();
    /**
     * 运单号变化
     */
    public List<InfoChangeLog>  outSidsInfoChangeLogs=new ArrayList<>();

    /**
     * 合单取消合单
     */
    public Map<TradeMergeEnum, Map<Long,Set<Long>>> undoMergeSids=new HashMap<>();
    /**
     * 合单的异常的需要重新处理的sid
     */
    public Set<Long> exceptMergeSids=new HashSet<>();

    /**
     * 子母单交易关闭需要回收单号
     */
    public List<Trade> recyclingOutSidTrades = new ArrayList<>();

    private boolean ignoreCache = false;

    /**
     * 是否可以更新订单金额   和 TradeSyncConfigUtils.canUpdatePaymentUserIds  一起控制
     */
    private boolean canUpdatePayment = false;
    /**
     * 同步链路中系统order透传的原始order
     */
    public Map<Long,Order> sysOriginOrderMap=new HashMap<>();
    /**
     * 同步链路中，平台原始order与oid的映射
     */
    public Map<Long,Order> platOriginOrderOidMap=new HashMap<>();

    /**
     * 订单同步时的额外参数
     */
    private TradeSyncContext tradeSyncContext;
    /**
     * 订单同步时异常相关的上下文
     */
    private TradeSyncExceptContext tradeSyncExceptContext;

    /**
     * oid 和orderid的对应
     */
    public Map<Long,Set<Long>> oidOrderIdMap=new HashMap<>();


    public Map<Trade,String> itemChangeLogMap=new HashMap<>();


    /**
     * 异常变更sid
     */
    public Map<ExceptEnum,Map<Long,Set<Long>>> exceptChangeMap=new HashMap<>();
    /**
     * 订单交易关闭的商品orderId
     */
    public Set<Order> closeOrders=new HashSet<>();

    public boolean openPlatformToCommodity=false;
    /**
     * 做过套件转单品或者组合商品转单品的oid
     * 套件转单品后做过拆单，新的系统单不会有套件转单品记录，要通过oid判断
     */
    public Set<Long> suit2SingleOids=new HashSet<>();
    /**
     * 奇门供销交易关闭订单
     */
    public transient List<Long> qimenFxRefundSids = new ArrayList<>();

    /**
     * 奇门供销部分退款订单
     */
    public transient List<Long> qimenFxPartRefundSids = new ArrayList<>();

    /**
     * 供销修改商品集合
     */
    public Set<Long> gxItemUpdateExceptIds = new HashSet<>();

    /**
     * 单品转套件信息
     */
    public SingleToSuitData singleToSuitData = new SingleToSuitData();

    public Map<Integer,Set<String>> platStatusChangetidMap=new HashMap<>();
    /**
     * 平台修改数量异常的sid
     */
    public Set<Long> platModifyItemNumExceptSids=new HashSet<>();

    /**
     * 部分order 更新为其他erp发货
     */
    public Set<Long> sidsOfPartOrderOtherERPConsigned = new HashSet<>();
    /**
     * 重新匹配到商品的sid
     */
    public Set<Long> rematchedItemSids = new HashSet<>();


    /**
     * 整单交易关闭或其他erp发货取消运单号
     */
    public Set<Trade> changeStatusCancelOutSidTrades = new HashSet<>();

    public Long getUserId() {
        return userId;
    }
	public SplitNumContextData getContextData() {
		return contextData;
	}

	public void setContextData(SplitNumContextData contextData) {
		this.contextData = contextData;
	}



    public TradeImportResult setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public TradeImportResult setPlatform(String platform) {
        this.platform = platform;
        return this;
    }

    public TradeImportResult setDelay(Long delay) {
        this.delay = delay;
        return this;
    }

    public TradeImportResult setSid(Long sid) {
        this.sid = sid;
        return this;
    }

    public TradeImportResult setTotal(long total) {
        this.total = total;
        return this;
    }

    public TradeImportResult setInsertTotal(long insertTotal) {
        this.insertTotal = insertTotal;
        return this;
    }

    public TradeImportResult setInserts(Map<String, Integer> inserts) {
        this.inserts = inserts;
        return this;
    }

    public TradeImportResult setUpdates(Map<String, Integer> updates) {
        this.updates = updates;
        return this;
    }

    public boolean getError() {
        return error;
    }

    public TradeImportResult setError(boolean error) {
        this.error = error;
        return this;
    }

    public TradeImportResult setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }

    public TradeImportResult setResumes(List<Trade> resumes) {
        this.resumes = resumes;
        return this;
    }

    @Override
    public String toString() {
        return "error:" + getError() +
                ", errorMsg:" + getErrorMsg() +
                ", delay:" + getDelay() +
                ", total:" + getTotal() +
                ", inserts:" + getInsertTotal() +
                ", updates:" + getUpdateTotal() +
                ", sid:" + getSid() +
                ", resultType:" + getResultType();

    }

    public void count(List<Trade> trades, boolean insert) {
        Map<String, Integer> map = insert ? getInserts() : getUpdates();
        for (Trade trade : trades) {
            map.compute(trade.getStatus(), (k, count) -> (count == null ? 1 : count + 1));
            if (insert) {
                setInsertTotal(getInsertTotal() + 1);
            } else {
                setUpdateTotal(getUpdateTotal() + 1);
            }
        }
        setTotal(getTotal() + trades.size());
    }

    public void addErrMsg(String errMsg) {
        if (errMsg == null || (errMsg = errMsg.trim()).isEmpty()) {
            return;
        }
        if (getErrorMsg() == null || getErrorMsg().trim().isEmpty()) {
            setErrorMsg(errMsg);
        } else {
            setErrorMsg(getErrorMsg() + "\n" + errMsg);
        }
    }

    /**
     * 同步出错的链路
     * 格式 class:errMsg
     */
    private String errChain;

    public void initFirstTook(){
        if (getFirstTook() != null || getStart() == null){
            return;
        }
        setFirstTook(System.currentTimeMillis() - start);
    }

    /** 分销配置 */
    public DmsDistributorConfigDto dmsDistributorConfigDto = null;

    /**同步链路是否已经走过 itemMatchBusiness*/
    public boolean itemMatched = false;

    /**
     * 加锁失败的tid
     */
    public Set<String> lockFailTids = new HashSet<>();

}

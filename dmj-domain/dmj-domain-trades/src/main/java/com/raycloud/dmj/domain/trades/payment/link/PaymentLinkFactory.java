package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description:
 * @author: pxh
 * @create: 2021-07-29 19:22
 **/
public class PaymentLinkFactory {

    private static final Map<OpenLinkConfigEnum, IPaymentLinkStrategy> PAYMENT_LINK_STRATEGY_FACTORY_MAP = new ConcurrentHashMap<>();

    static {
        PAYMENT_LINK_STRATEGY_FACTORY_MAP.put(OpenLinkConfigEnum.ORDER_PAYMENT_NO_LINK_CHANGE_TRADE_PAYMENT, new PaymentNoLinkStrategy());
        PAYMENT_LINK_STRATEGY_FACTORY_MAP.put(OpenLinkConfigEnum.ORDER_AND_TRADE_PAYMENT_CHANGE_LINK, new PaymentLinkStrategy());
        PAYMENT_LINK_STRATEGY_FACTORY_MAP.put(OpenLinkConfigEnum.PLATFORM_TRADE_PAYMENT_NO_LINK, new PlatformOrderPaymentNoLinkStrategy());
        PAYMENT_LINK_STRATEGY_FACTORY_MAP.put(OpenLinkConfigEnum.SYSTEM_TRADE_PAYMENT_NO_LINK, new SystemOrderPaymentNoLinkStrategy());
        PAYMENT_LINK_STRATEGY_FACTORY_MAP.put(OpenLinkConfigEnum.ALL_TRADE_PAYMENT_NO_LINK, new AllOrderPaymentNoLinkStrategy());
    }

    /***
     *
     * @author: pxh
     * @description:
     * @date: 2021/9/6 9:51 上午
     * @param: paymentLinkReqDTO
     * @return: com.raycloud.dmj.domain.trades.payment.link.PaymentLinkRespDTO
     */
    public static PaymentLinkRespDTO getPaymentLinkDto(PaymentLinkReqDTO paymentLinkReqDTO) {
        if(paymentLinkReqDTO == null){
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if(paymentLinkReqDTO.getOrder() == null){
            throw new IllegalArgumentException("order不能为空");
        }
        if(paymentLinkReqDTO.getOpenLinkConfig() == null){
            throw new IllegalArgumentException("金额联动策略不能为空");
        }
        IPaymentLinkStrategy paymentLinkStrategy = PAYMENT_LINK_STRATEGY_FACTORY_MAP.get(paymentLinkReqDTO.getOpenLinkConfig());
        if(paymentLinkStrategy == null){
            throw new IllegalArgumentException("未实现指定的金额联动策略");
        }
        return paymentLinkStrategy.getPaymentLinkDto(paymentLinkReqDTO);
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

/**
 * SeparateWeightMachineConf
 *  员工 与称重台配置
 * <AUTHOR>
 * @Date 2019/9/17
 * @Time 15:52
 */
public class SeparateWeightMachineConf implements Serializable {
    private static final long serialVersionUID = -4185655617952289088L;
    /**
     * 员工id
     */
    private Long staffId;
    /**
     * 员工名称
     */
    private String staffName;
    /**
     * 称重台名称
     */
    private String name;

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}

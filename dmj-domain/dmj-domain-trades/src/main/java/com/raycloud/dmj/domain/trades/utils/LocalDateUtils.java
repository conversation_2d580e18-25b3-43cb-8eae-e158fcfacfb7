package com.raycloud.dmj.domain.trades.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @description: LocalDate工具类
 * @author: chunri
 * @create: 2021-03-12 17:11
 **/
public class LocalDateUtils {

    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 获取当天结束时间 2021-03-12 23:59:59
     */
    public static LocalDateTime getTodayEndTime(){
        LocalDate currentDay = LocalDate.now();
        return LocalDateTime.of(currentDay, LocalTime.MAX);
    }

    /**
     * 获取当天开始时间 2021-03-12 00:00:00
     */
    public static LocalDateTime getTodayStartTime(){
        LocalDate currentDay = LocalDate.now();
        return LocalDateTime.of(currentDay, LocalTime.MIN);
    }

    /**
     * Date转换为LocalDateTime
     */
    public static LocalDateTime convertDateToLDT(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * LocalDateTime转换为Date
     */
    public static Date convertLDTToDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当天开始时间 2021-03-12 00：00：00
     */
    public static LocalDateTime getTodayBeginTime(){
        LocalDate currentDay = LocalDate.now();
        return LocalDateTime.of(currentDay, LocalTime.MIN);
    }

    public static Long convertLDTToDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toEpochSecond(ZoneOffset.of("+8"));
    }



    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.of("GMT+8")).toLocalDateTime();
        return YYYY_MM_DD_FORMATTER.format(localDateTime);
    }

    public static Date parseToDate(String dataStr,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date parse;
        try {
            parse = sdf.parse(dataStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return parse;
    }

    /**
     * 判断当前分钟是否是2的倍数
     * KMERP-158385
     */
    public static boolean isMinuteMultipleOfTen() {
        LocalTime now = LocalTime.now();
        int minutes = now.getMinute();
        // 检查分钟是否是2的倍数
        return minutes % 2 == 0;
    }

}

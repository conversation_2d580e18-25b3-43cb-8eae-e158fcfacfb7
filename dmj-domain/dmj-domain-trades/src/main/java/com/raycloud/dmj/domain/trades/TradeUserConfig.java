package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @discription: 订单承诺时间配置
 * <AUTHOR>
 * @date: 2020/03/16 14:49
 */
@Table(name = "table_user_config")
@Setter
@Getter
public class TradeUserConfig extends TradeRule {

    private static final long serialVersionUID = -798743551607809477L;

    public Long userId;

    public Long companyId;

    public Integer estConsignTime;

    public Integer enableStatus;

    /**
     * 店铺名称，不需要持久化
     */
    private String shopName;

    /**
     * 店铺平台类型，不需要持久化
     */
    private String source;

    private String sourceName;

}

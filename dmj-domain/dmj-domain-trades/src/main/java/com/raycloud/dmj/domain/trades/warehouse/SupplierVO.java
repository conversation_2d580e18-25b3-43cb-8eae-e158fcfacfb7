package com.raycloud.dmj.domain.trades.warehouse;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21 10:14
 */
@Data
public class SupplierVO implements Serializable {
    /**
     * 供应商编号
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 合作状态，1表示合作中，2表示停止合作
     */
    private Integer status;
}

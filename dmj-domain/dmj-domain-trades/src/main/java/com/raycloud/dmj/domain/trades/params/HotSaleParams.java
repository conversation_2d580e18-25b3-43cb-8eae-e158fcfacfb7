package com.raycloud.dmj.domain.trades.params;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: men
 * @Date: 2021-08-12 14:38
 * 爆款商品列表查询条件
 */
@Data
@Accessors(chain = true)
public class HotSaleParams implements Serializable {

    private static final long serialVersionUID = 3306869244493323717L;

    /**
     * 是否忽略权限，默认false
     * 生成商品标签使用
     */
    private boolean ignorePower;

    /**
     * 仓库id
     */
    private List<Long> warehouseIds;

    /**
     * 最小可用库存数
     */
    private Integer minAvailableStock;

    /**
     * 最大可用库存数
     */
    private Integer maxAvailableStock;

    /**
     * 指定规格商家编码
     */
    private List<String> skuOuterIds;

    /**
     * 是否有可用库存
     * 【1：是 0：否】
     */
    private Integer isExistStock;

    /**
     * 是否启用库存维度
     * 【1：是 0：否】
     * 开启后爆款数据按 商品+仓库+模版信息分组
     */
    private Integer isMatchWarehouse;

    /**
     * 是否过滤阈值
     * 【1：是 0：否】
     */
    private Integer isThreshold;

    /**
     * 付款时间起始范围
     */
    private Date startPayTime;

    /**
     * 付款时间结束范围
     */
    private Date endPayTime;

    /**
     * 规格商家编码查询key
     */
    private List<String> skuOuterKey;

    /**
     * 店铺id
     */
    private List<Long> userIds;

    /**
     * 快递模版
     */
    private List<Long> expressTemplateIds;

    /**
     * 快递公司名称
     */
    private List<Long> logisticsCompanyIds;

    /**
     * 是否是快递公司名称白名单 默认false
     */
    private boolean printTemplateIntegrate;

    /**
     * 快递平台
     */
    private List<Integer> wlbTemplateTypeIds;

    /**
     * 拦截的订单标签id
     */
    private List<String> interceptIds;

    /**
     * 爆款打印是否排除免验 0-否 1-是
     */
    private Integer excludeNotCheck;

    private Boolean checkIsPickWhiteList;

    /**
     * 列表排序key
     *
     * * 枚举 HotSaleListSortEnum
     * * 正数正序,负数倒叙
     */
    private List<Integer> sortList;

    /**
     * 是否生成爆款码总阈值(商品标签页面才生成)
     */
    private Long hotSaleCodeThreshold;

    /**
     * 生成订单唯一码单件订单根据商品聚合
     */
    Map<String, Long> tradeNumMap;

    /**
     * 1:不参与生成唯一码的爆款商品 2:生成外采唯一码的爆款商品
     */
    private Integer hotSaleType;

    public enum HotSaleListSortEnum{

        ITEM_SHEL_SORT(1,"货位排序（多货位取第一个货位值）"),
        TRADE_NUM_SORT(2,"按订单数量排序"),
        ASS_GOODS_STOCK_SORT(3,"按货位库存数排序");

        @Getter
        private Integer key;
        private String msg;


        HotSaleListSortEnum(Integer key,String msg) {
            this.key = key;
            this.msg = msg;
        }
    }
}

package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 用于扩展的打印配置类
 */
public class ExtensiblePrintConfig implements Serializable {
    private static final long serialVersionUID = -6381601718237030248L;
    //请务必记住这个类里所有的字段都需要写 最好不要提供无参的构造方法
    /**
     * 云打印快递模板影藏停用模板 0显示 1 隐藏
     */
    private Integer concealDisableTempltate;

    /**
     * 快递公司隐藏停用 0显示 1 隐藏
     */
    private Integer concealDisableLogisticsCompany;

    /**
     * 商家编码模板 0显示 1 隐藏
     */
    private Integer merchantCodeDisableTemplate;

    /**
     * 采购单退货模板 0显示 1 隐藏
     */
    private Integer purchaseReturnDisableTemplate;

    /**
     * 捡货模板 0显示 1 隐藏
     */
    private Integer pickerGoodsDisableTemplate;

    /**
     * 拿货模板 0显示 1 隐藏
     */
    private Integer getterGoodsDisableTemplate;

    /**
     * 货位模板 0显示 1 隐藏
     */
    private Integer goodsSectionDisableTemplate;
    /**
     * 发货单
     */
    private Integer deliverDisableTemplate;
    /**
     * 装箱清单
     */
    private Integer fetchDisableTemplate;
    /**
     * 采购单
     */
    private Integer purchaseDisableTemplate;
    /**
     * 收货单
     */
    private Integer receiptDisableTemplate;
    /**
     * 箱柜码
     */
    private Integer boxCodeDisableTemplate;
    /**
     * 箱码
     */
    private Integer boxLabelDisableTemplate;
    /**
     * 吊牌
     */
    private Integer tagsDisableTemplate;
    /**
     * 销货单
     */
    private Integer salesSlipDisableTemplate;
    /**
     * 调拨单
     */
    private Integer allocateOrderDisableTemplate;
    /**
     * 其他出入库单
     */
    private Integer outboundDisableTemplate;
    /**
     * 收款单
     */
    private Integer receivablesDisableTemplate;
    /**
     * 加工单
     */
    private Integer processingOrderDisableTemplate;
    /**
     * 预约入库单
     */
    private Integer preinDisableTemplate;
    /**
     * 送货单
     */
    private Integer sendTradeDisableTemplate;
    /**
     * 唯一码外链打印商家编码使用的模板
     */
    private Long uniqueMerchantTemplateId;
    /**
     * 采购单外链打印商家编码使用的模板
     */
    private Long purchaseMerchantTemplateId;
    /**
     * 采购单外链打印采购单的模板
     */
    private Long purchasePrintTemplateId;
    /**
     * 采购单外链打印送货单的模板
     */
    private Long sendTradeTemplateId;
    /**
     * 唯一码 多件打印文案
     */
    private String uniqueMultiText;
    /**
     * 唯一码 单件打印文案
     */
    private String uniqueSingleText;
    /**
     * 唯一码 备货打印文案
     */
    private String uniqueBackupText;

    /**
     * 放心购已停用模板隐藏
     */
    private Integer fxgWlbtype;

    /**
     * 菜鸟已停用模板隐藏
     */
    private Integer caiNiaoWlbtype;

    /**
     * 京东已停用模板隐藏
     */
    private Integer jdWlbtype;

    /**
     * 拼多多已停用模板隐藏
     */
    private Integer pddWlbtype;

    /**
     * 分隔码打印供应商信息
     */
    private String separatePrintSupplierInfo;

    /**
     * 唯一码按照单多备规则排序,0--不区分单多,1--单多备,2--多单备
     */
    private Integer uniqueCodeSortType;

    /**
     * 唯一码标签分组 0--不进行分组,1--按供应商分组,2--先按供应商分组再按唯一码类型分组
     */
    private Integer uniqueSeparateType;

    /**
     * 标签来源 0--全部,1--档口,2--家里
     */
    private Integer source;

    /**
     * 不同供应商之间打出空白标签,0--关闭,1--开启
     */
    private Integer needSeparate;

    /**
     * 急标/补标/新标优先级排序
     * 0 不排序 1 排序
     */
    private String urgentTagSort;

    /**
     * 剩余时间排序
     * 0 不排序 1 排序
     */
    private String remainTimeSort;

    /**
     * 自选打印时间类型
     * 0：同系统时间；1：唯一码生成时间
     */
    private Integer customPrintTimeSet;

    /**
     * 编码模板ID,到货清点打印标签的模板ID
     */
    private Long uniqueTemplateId;

    /**
     * 到货清点打印标签的打印机名称
     */
    private String uniquePrinterName;

    /**
     * 按供应商档案列表序号顺序排序
     * 0不排序  1排序
     */
    private Integer supplierSort;

    /**
     * 维修单
     */
    private Integer repairDisableTemplate;

    /**
     * 编码模板ID,到货清点爆款码的模板ID
     */
    private Long hotSaleTemplateId;

    /**
     * 到货清点爆款码的打印机名称
     */
    private String hotSalePrinterName;

    public Integer getDeliverDisableTemplate() {
        return deliverDisableTemplate;
    }

    public void setDeliverDisableTemplate(Integer deliverDisableTemplate) {
        this.deliverDisableTemplate = deliverDisableTemplate;
    }

    public Integer getFetchDisableTemplate() {
        return fetchDisableTemplate;
    }

    public void setFetchDisableTemplate(Integer fetchDisableTemplate) {
        this.fetchDisableTemplate = fetchDisableTemplate;
    }

    public Integer getPurchaseDisableTemplate() {
        return purchaseDisableTemplate;
    }

    public void setPurchaseDisableTemplate(Integer purchaseDisableTemplate) {
        this.purchaseDisableTemplate = purchaseDisableTemplate;
    }

    public Integer getReceiptDisableTemplate() {
        return receiptDisableTemplate;
    }

    public void setReceiptDisableTemplate(Integer receiptDisableTemplate) {
        this.receiptDisableTemplate = receiptDisableTemplate;
    }

    public Integer getBoxCodeDisableTemplate() {
        return boxCodeDisableTemplate;
    }

    public void setBoxCodeDisableTemplate(Integer boxCodeDisableTemplate) {
        this.boxCodeDisableTemplate = boxCodeDisableTemplate;
    }

    public Integer getBoxLabelDisableTemplate() {
        return boxLabelDisableTemplate;
    }

    public void setBoxLabelDisableTemplate(Integer boxLabelDisableTemplate) {
        this.boxLabelDisableTemplate = boxLabelDisableTemplate;
    }

    public Integer getTagsDisableTemplate() {
        return tagsDisableTemplate;
    }

    public void setTagsDisableTemplate(Integer tagsDisableTemplate) {
        this.tagsDisableTemplate = tagsDisableTemplate;
    }

    public Integer getSalesSlipDisableTemplate() {
        return salesSlipDisableTemplate;
    }

    public void setSalesSlipDisableTemplate(Integer salesSlipDisableTemplate) {
        this.salesSlipDisableTemplate = salesSlipDisableTemplate;
    }

    public Integer getAllocateOrderDisableTemplate() {
        return allocateOrderDisableTemplate;
    }

    public void setAllocateOrderDisableTemplate(Integer allocateOrderDisableTemplate) {
        this.allocateOrderDisableTemplate = allocateOrderDisableTemplate;
    }

    public Integer getOutboundDisableTemplate() {
        return outboundDisableTemplate;
    }

    public void setOutboundDisableTemplate(Integer outboundDisableTemplate) {
        this.outboundDisableTemplate = outboundDisableTemplate;
    }

    public Integer getReceivablesDisableTemplate() {
        return receivablesDisableTemplate;
    }

    public void setReceivablesDisableTemplate(Integer receivablesDisableTemplate) {
        this.receivablesDisableTemplate = receivablesDisableTemplate;
    }

    public Integer getProcessingOrderDisableTemplate() {
        return processingOrderDisableTemplate;
    }

    public void setProcessingOrderDisableTemplate(Integer processingOrderDisableTemplate) {
        this.processingOrderDisableTemplate = processingOrderDisableTemplate;
    }

    public Integer getMerchantCodeDisableTemplate() {
        return merchantCodeDisableTemplate;
    }

    public void setMerchantCodeDisableTemplate(Integer merchantCodeDisableTemplate) {
        this.merchantCodeDisableTemplate = merchantCodeDisableTemplate;
    }

    public Integer getPurchaseReturnDisableTemplate() {
        return purchaseReturnDisableTemplate;
    }

    public void setPurchaseReturnDisableTemplate(Integer purchaseReturnDisableTemplate) {
        this.purchaseReturnDisableTemplate = purchaseReturnDisableTemplate;
    }

    public Integer getPickerGoodsDisableTemplate() {
        return pickerGoodsDisableTemplate;
    }

    public void setPickerGoodsDisableTemplate(Integer pickerGoodsDisableTemplate) {
        this.pickerGoodsDisableTemplate = pickerGoodsDisableTemplate;
    }

    public Integer getGetterGoodsDisableTemplate() {
        return getterGoodsDisableTemplate;
    }

    public void setGetterGoodsDisableTemplate(Integer getterGoodsDisableTemplate) {
        this.getterGoodsDisableTemplate = getterGoodsDisableTemplate;
    }

    public Integer getGoodsSectionDisableTemplate() {
        return goodsSectionDisableTemplate;
    }

    public void setGoodsSectionDisableTemplate(Integer goodsSectionDisableTemplate) {
        this.goodsSectionDisableTemplate = goodsSectionDisableTemplate;
    }

    public Integer getConcealDisableTempltate() {
        return concealDisableTempltate;
    }

    public Long getPurchaseMerchantTemplateId() {
        return purchaseMerchantTemplateId;
    }

    public void setPurchaseMerchantTemplateId(Long purchaseMerchantTemplateId) {
        this.purchaseMerchantTemplateId = purchaseMerchantTemplateId;
    }

    public void setConcealDisableTempltate(Integer concealDisableTempltate) {
        this.concealDisableTempltate = concealDisableTempltate;
    }

    public Long getUniqueMerchantTemplateId() {
        return uniqueMerchantTemplateId;
    }

    public void setUniqueMerchantTemplateId(Long uniqueMerchantTemplateId) {
        this.uniqueMerchantTemplateId = uniqueMerchantTemplateId;
    }

    public Long getPurchasePrintTemplateId() {
        return purchasePrintTemplateId;
    }

    public void setPurchasePrintTemplateId(Long purchasePrintTemplateId) {
        this.purchasePrintTemplateId = purchasePrintTemplateId;
    }

    public Long getSendTradeTemplateId() {
        return sendTradeTemplateId;
    }

    public void setSendTradeTemplateId(Long sendTradeTemplateId) {
        this.sendTradeTemplateId = sendTradeTemplateId;
    }

    public String getUniqueMultiText() {
        return uniqueMultiText;
    }

    public void setUniqueMultiText(String uniqueMultiText) {
        this.uniqueMultiText = uniqueMultiText;
    }

    public String getUniqueSingleText() {
        return uniqueSingleText;
    }

    public void setUniqueSingleText(String uniqueSingleText) {
        this.uniqueSingleText = uniqueSingleText;
    }

    public String getUniqueBackupText() {
        return uniqueBackupText;
    }

    public void setUniqueBackupText(String uniqueBackupText) {
        this.uniqueBackupText = uniqueBackupText;
    }

    public Integer getFxgWlbtype() {
        return fxgWlbtype;
    }

    public void setFxgWlbtype(Integer fxgWlbtype) {
        this.fxgWlbtype = fxgWlbtype;
    }

    public Integer getCaiNiaoWlbtype() {
        return caiNiaoWlbtype;
    }

    public void setCaiNiaoWlbtype(Integer caiNiaoWlbtype) {
        this.caiNiaoWlbtype = caiNiaoWlbtype;
    }

    public Integer getJdWlbtype() {
        return jdWlbtype;
    }

    public void setJdWlbtype(Integer jdWlbtype) {
        this.jdWlbtype = jdWlbtype;
    }

    public Integer getPddWlbtype() {
        return pddWlbtype;
    }

    public void setPddWlbtype(Integer pddWlbtype) {
        this.pddWlbtype = pddWlbtype;
    }

    public Integer getPreinDisableTemplate() {
        return preinDisableTemplate;
    }

    public void setPreinDisableTemplate(Integer preinDisableTemplate) {
        this.preinDisableTemplate = preinDisableTemplate;
    }

    public Integer getSendTradeDisableTemplate() {
        return sendTradeDisableTemplate;
    }

    public void setSendTradeDisableTemplate(Integer sendTradeDisableTemplate) {
        this.sendTradeDisableTemplate = sendTradeDisableTemplate;
    }

    public Integer getConcealDisableLogisticsCompany() {
        return concealDisableLogisticsCompany;
    }

    public void setConcealDisableLogisticsCompany(Integer concealDisableLogisticsCompany) {
        this.concealDisableLogisticsCompany = concealDisableLogisticsCompany;
    }

    public String getSeparatePrintSupplierInfo() {
        return separatePrintSupplierInfo;
    }

    public void setSeparatePrintSupplierInfo(String separatePrintSupplierInfo) {
        this.separatePrintSupplierInfo = separatePrintSupplierInfo;
    }

    public Integer getUniqueCodeSortType() {
        return uniqueCodeSortType == null ? 0 : uniqueCodeSortType;
    }

    public void setUniqueCodeSortType(Integer uniqueCodeSortType) {
        this.uniqueCodeSortType = uniqueCodeSortType;
    }

    public ExtensiblePrintConfig() {
    }

    public Integer getUniqueSeparateType() {
        return uniqueSeparateType;
    }

    public void setUniqueSeparateType(Integer uniqueSeparateType) {
        this.uniqueSeparateType = uniqueSeparateType;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getNeedSeparate() {
        return needSeparate;
    }

    public void setNeedSeparate(Integer needSeparate) {
        this.needSeparate = needSeparate;
    }

    public String getUrgentTagSort() {
        return urgentTagSort;
    }

    public void setUrgentTagSort(String urgentTagSort) {
        this.urgentTagSort = urgentTagSort;
    }

    public String getRemainTimeSort() {
        return remainTimeSort;
    }

    public void setRemainTimeSort(String remainTimeSort) {
        this.remainTimeSort = remainTimeSort;
    }

    public Integer getCustomPrintTimeSet() {
        return customPrintTimeSet;
    }

    public void setCustomPrintTimeSet(Integer customPrintTimeSet) {
        this.customPrintTimeSet = customPrintTimeSet;
    }

    public Long getUniqueTemplateId() {
        return uniqueTemplateId;
    }

    public void setUniqueTemplateId(Long uniqueTemplateId) {
        this.uniqueTemplateId = uniqueTemplateId;
    }

    public String getUniquePrinterName() {
        return uniquePrinterName;
    }

    public void setUniquePrinterName(String uniquePrinterName) {
        this.uniquePrinterName = uniquePrinterName;
    }

    public Integer getSupplierSort() {
        return supplierSort;
    }

    public void setSupplierSort(Integer supplierSort) {
        this.supplierSort = supplierSort;
    }

    public Integer getRepairDisableTemplate() {
        return repairDisableTemplate;
    }

    public void setRepairDisableTemplate(Integer repairDisableTemplate) {
        this.repairDisableTemplate = repairDisableTemplate;
    }

    public Long getHotSaleTemplateId() {
        return hotSaleTemplateId;
    }

    public void setHotSaleTemplateId(Long hotSaleTemplateId) {
        this.hotSaleTemplateId = hotSaleTemplateId;
    }

    public String getHotSalePrinterName() {
        return hotSalePrinterName;
    }

    public void setHotSalePrinterName(String hotSalePrinterName) {
        this.hotSalePrinterName = hotSalePrinterName;
    }
}
package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-01-11-13:25
 */
public class ConsignRecordWithTrade extends Trade implements Serializable {

    private static final long serialVersionUID = 3511000748025079005L;

    /**
     * 物流云订阅状态  默认0 未订阅  1已订阅 2订阅失败
     * @return
     */
    private Integer wlySubscribeFlag;

    /**
     * 物流状态
     * @return
     */
    private String logisticsStatus;

    /**
     * 物流最近更新时间
     * @return
     */
    private Date logisticsModified;

    /**
     * 订单发货时间
     */
    private Date consigned;

    /**
     * 快递公司code  不持久化
     */
    private String cpCode;

    public Integer getWlySubscribeFlag() {
        return wlySubscribeFlag;
    }

    public void setWlySubscribeFlag(Integer wlySubscribeFlag) {
        this.wlySubscribeFlag = wlySubscribeFlag;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public Date getLogisticsModified() {
        return logisticsModified;
    }

    public void setLogisticsModified(Date logisticsModified) {
        this.logisticsModified = logisticsModified;
    }

    public Date getConsigned() {
        return consigned;
    }

    public void setConsigned(Date consigned) {
        this.consigned = consigned;
    }
}

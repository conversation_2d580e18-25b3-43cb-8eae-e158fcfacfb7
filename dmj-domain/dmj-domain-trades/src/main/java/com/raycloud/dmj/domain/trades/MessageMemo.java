package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.io.Serializable;
import java.util.*;

/**
 * 这个只做订单的 buyerMessage、sellerMemo和flag的封装对象，不做持久化
 *
 * <AUTHOR>
 */
public class MessageMemo implements Serializable {

    private static final Logger logger = Logger.getLogger(MessageMemo.class);
    /**
     *
     */
    private static final long serialVersionUID = 4572101133362270494L;

    private Long sid;

    private String tid;

    private Long shortId;

    private String buyerMessage;

    public String getAcPayment() {
        return acPayment;
    }

    public void setAcPayment(String acPayment) {
        this.acPayment = acPayment;
    }

    private String sellerMemo;

    private Long sellerFlag;

    private String sysMemo;

    private String payment;

    private String platformDiscountFee;

    private String totalFee;

    private String discountFee;

    private String postFee;

    private String acPayment;

    /**
     * 理论运费
     */
    private Double theoryPostFee;

    private String adjustFee;

    /**
     * 支付详情
     */
    private String paymentDisplay;

    /**
     * 总共优惠的金额
     */
    private String totalDiscountFee;

    private Date created;

    private Date payTime;

    private Date consignTime;

    private Date ptConsignTime;

    private String taxFee;

    private String saleFee;

    private String grossProfit;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMessage;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMemo;

    /**
     * 是否标识订单地址修改，1标识
     */
    private Integer addressChanged;

    /**
     * 是否标识订单为合单主单，1标识
     */
    private Integer isMergeMain;

    private String unifiedStatus;

    private Integer sysConsigned;

    private Integer isUpload;

    private Long userId;

    private String sysStatus;

    private String actualPostFee;

    /**
     * 天猫物流升级
     */
    private String tmallAsdpBizType;

    /**
     * 天猫送货上门
     */
    private String tmallAsdpAds;

    private String logisticsCode;

    private String storeName;

    /**
     * 加运费发某快递公司  SF-顺丰
     */
    private String increaseFreightExpress;

    /**
     * 加运费发某快递公司费用
     *  比如：顺丰加价服务费
     */
    private String increaseFreightFee;

    /**
     * 社区团购  团id
     */
    private String communityGroupId;

    /**
     * 社区团购  团身份
     */
    private String communityGroupRole;

    /**
     * 阿里健康的平台订单号，ERP系统的平台订单号其实是发货单号
     */
    private Long aliHealthMainId;

    private String payAmount;

    private List<Order> orderList;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 业务员名称
     */
    private String salesmanName;

    /**
     * 重点检查
     */
    private Boolean qualityCheck;

    /**
     * 优先发货
     *
     */
    private Boolean priorityDelivery;


    /**
     * "1"(一换质检),"2"(二换质检),"4"(退换货重拍同商品),"8"(商品品相敏感)
     */
    private String qualityCheckType;

    /**
     * "newcarton_package"(全新纸箱包装发货),"gift_package"(礼盒包装发货), "carton_package"(纸箱包装发货)
     */
    private String actionCode;

    /**
     * "1"(普通订单),"2"(原订单),"3"(一换订单),"4"(二换订单)
     */
    private String orderLabel;

    /**
     * tradeExt.extraFields.checkItemsInfos 是jsonStr
     *
     * 检查内容
     * 问题描述 problemDesc 图片地址imageList 视频地址videoList  订单标签orderlabel
     */
    private Object checkItemsInfos;

    private Integer isPresell;

    private String type;

    /**
     * 分销原始平台单号
     */
    private String fxPlatformTid;

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    /**
     * 旗帜备注信息
     */
    private String sellerFlagTag;

    public String getSellerFlagTag() {
        return sellerFlagTag;
    }

    public void setSellerFlagTag(String sellerFlagTag) {
        this.sellerFlagTag = sellerFlagTag;
    }

    /**
     * 订单来自哪个平台，例如 tb(淘宝或者天猫平台),jd(京东平台),sys(系统手工订单)
     */
    private String source;


    private String subSource;

    /**
     * 经分销id 团长id
     */
    private String distributorId;

    /**
     * 经分销昵称 团长昵称 活动名称
     */
    private String distributorName;

    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public Boolean getQualityCheck() {
        return qualityCheck;
    }

    public void setQualityCheck(Boolean qualityCheck) {
        this.qualityCheck = qualityCheck;
    }

    public Boolean getPriorityDelivery() {
        return priorityDelivery;
    }

    public void setPriorityDelivery(Boolean priorityDelivery) {
        this.priorityDelivery = priorityDelivery;
    }

    public String getQualityCheckType() {
        return qualityCheckType;
    }

    public void setQualityCheckType(String qualityCheckType) {
        this.qualityCheckType = qualityCheckType;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getOrderLabel() {
        return orderLabel;
    }

    public void setOrderLabel(String orderLabel) {
        this.orderLabel = orderLabel;
    }

    public Object getCheckItemsInfos() {
        return checkItemsInfos;
    }

    public void setCheckItemsInfos(Object checkItemsInfos) {
        this.checkItemsInfos = checkItemsInfos;
    }

    /**
     * 图片备注 https://tb.raycloud.com/task/63281b894d3a0a001db56736
     */
    private List<String> tradePictureMemoUris;

    /**
     * 订单成本
     */
    private String visualCost;

    public String getVisualCost() {
        return visualCost;
    }

    public void setVisualCost(String visualCost) {
        this.visualCost = visualCost;
    }

    public String getSelfBuiltDepositAmount() {
        return selfBuiltDepositAmount;
    }

    public void setSelfBuiltDepositAmount(String selfBuiltDepositAmount) {
        this.selfBuiltDepositAmount = selfBuiltDepositAmount;
    }

    public String getSelfBuiltPaymentReceivable() {
        return selfBuiltPaymentReceivable;
    }

    public void setSelfBuiltPaymentReceivable(String selfBuiltPaymentReceivable) {
        this.selfBuiltPaymentReceivable = selfBuiltPaymentReceivable;
    }

    /**
     * 定金
     */
    String selfBuiltDepositAmount;

    /**
     * 代收金额
     */
    String selfBuiltPaymentReceivable;

    public List<String> getTradePictureMemoUris() {
        return tradePictureMemoUris;
    }

    public void setTradePictureMemoUris(List<String> tradePictureMemoUris) {
        this.tradePictureMemoUris = tradePictureMemoUris;
    }

    public List<Order> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<Order> orderList) {
        this.orderList = orderList;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getTmallAsdpBizType() {
        return tmallAsdpBizType;
    }

    public void setTmallAsdpBizType(String tmallAsdpBizType) {
        this.tmallAsdpBizType = tmallAsdpBizType;
    }

    public String getTmallAsdpAds() {
        return tmallAsdpAds;
    }

    public void setTmallAsdpAds(String tmallAsdpAds) {
        this.tmallAsdpAds = tmallAsdpAds;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getIsHandlerMessage() {
        return isHandlerMessage;
    }

    public void setIsHandlerMessage(Integer isHandlerMessage) {
        this.isHandlerMessage = isHandlerMessage;
    }

    public Integer getIsHandlerMemo() {
        return isHandlerMemo;
    }

    public void setIsHandlerMemo(Integer isHandlerMemo) {
        this.isHandlerMemo = isHandlerMemo;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public Long getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(Long sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getPostFee() {
        return postFee;
    }

    public void setPostFee(String postFee) {
        this.postFee = postFee;
    }

    public String getAdjustFee() {
        return adjustFee;
    }

    public void setAdjustFee(String adjustFee) {
        this.adjustFee = adjustFee;
    }

    public String getPaymentDisplay() {
        return paymentDisplay;
    }

    public void setPaymentDisplay(String paymentDisplay) {
        this.paymentDisplay = paymentDisplay;
    }

    public String getTotalDiscountFee() {
        return totalDiscountFee;
    }

    public void setTotalDiscountFee(String totalDiscountFee) {
        this.totalDiscountFee = totalDiscountFee;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public Date getPtConsignTime() {
        return ptConsignTime;
    }

    public void setPtConsignTime(Date ptConsignTime) {
        this.ptConsignTime = ptConsignTime;
    }

    public String getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(String taxFee) {
        this.taxFee = taxFee;
    }

    public String getSaleFee() {
        return saleFee;
    }

    public void setSaleFee(String saleFee) {
        this.saleFee = saleFee;
    }

    public String getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(String grossProfit) {
        this.grossProfit = grossProfit;
    }

    public Integer getAddressChanged() {
        return addressChanged;
    }

    public void setAddressChanged(Integer addressChanged) {
        this.addressChanged = addressChanged;
    }

    public Integer getIsMergeMain() {
        return isMergeMain;
    }

    public void setIsMergeMain(Integer isMergeMain) {
        this.isMergeMain = isMergeMain;
    }

    public Double getTheoryPostFee() {
        return theoryPostFee;
    }

    public void setTheoryPostFee(Double theoryPostFee) {
        this.theoryPostFee = theoryPostFee;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public Integer getSysConsigned() {
        return sysConsigned;
    }

    public void setSysConsigned(Integer sysConsigned) {
        this.sysConsigned = sysConsigned;
    }

    public Integer getIsUpload() {
        return isUpload;
    }

    public void setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getActualPostFee() {
        return actualPostFee;
    }

    public void setActualPostFee(String actualPostFee) {
        this.actualPostFee = actualPostFee;
    }

    public String getIncreaseFreightExpress() {
        return increaseFreightExpress;
    }

    public void setIncreaseFreightExpress(String increaseFreightExpress) {
        this.increaseFreightExpress = increaseFreightExpress;
    }

    public String getIncreaseFreightFee() {
        return increaseFreightFee;
    }

    public void setIncreaseFreightFee(String increaseFreightFee) {
        this.increaseFreightFee = increaseFreightFee;
    }

    public String getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(String communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public String getCommunityGroupRole() {
        return communityGroupRole;
    }

    public void setCommunityGroupRole(String communityGroupRole) {
        this.communityGroupRole = communityGroupRole;
    }

    public Long getAliHealthMainId() {
        return aliHealthMainId;
    }

    public void setAliHealthMainId(Long aliHealthMainId) {
        this.aliHealthMainId = aliHealthMainId;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public Long getSalesmanId() {
        return salesmanId;
    }

    public void setSalesmanId(Long salesmanId) {
        this.salesmanId = salesmanId;
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    public String getFxPlatformTid() {
        return fxPlatformTid;
    }

    public void setFxPlatformTid(String fxPlatformTid) {
        this.fxPlatformTid = fxPlatformTid;
    }

    public String getPlatformDiscountFee() {
        return platformDiscountFee;
    }

    public void setPlatformDiscountFee(String platformDiscountFee) {
        this.platformDiscountFee = platformDiscountFee;
    }

    public static MessageMemo valueOf(Staff staff, Trade trade) {
        MessageMemo t = new MessageMemo();
        t.setSid(trade.getSid());
        t.setTid(trade.getTid());
        t.setShortId(trade.getShortId());
        t.setSource(trade.getSource());
        t.setSubSource(trade.getSubSource());
        t.setBuyerMessage(trade.getBuyerMessage());
        t.setSellerMemo(trade.getSellerMemo());
        t.setSellerFlag(trade.getSellerFlag());
        t.setSysMemo(trade.getSysMemo());
        t.setCreated(trade.getCreated());
        t.setPayTime(trade.getPayTime());
        t.setConsignTime(trade.getConsignTime());
        t.setPayment(trade.getPayment());
        t.setTotalFee(trade.getTotalFee());
        t.setPostFee(trade.getPostFee());
        t.setTheoryPostFee(trade.getTheoryPostFee());
        t.setActualPostFee(trade.getActualPostFee());
        t.setDiscountFee(trade.getDiscountFee());
        t.setAdjustFee(trade.getAdjustFee());
        t.setTaxFee(trade.getTaxFee());
        t.setSaleFee(trade.getSaleFee());
        t.setGrossProfit(trade.getGrossProfit()!=null?String.valueOf(trade.getGrossProfit()):String.valueOf(TradeUtils.calculateGrossProfitNotMerge(trade)));
        t.setIsHandlerMemo(trade.getIsHandlerMemo());
        t.setIsHandlerMessage(trade.getIsHandlerMessage());
        t.setPtConsignTime(trade.getPtConsignTime());
        t.setSysConsigned(trade.getSysConsigned());
        t.setIsUpload(trade.getIsUpload());
        t.setUnifiedStatus(trade.getUnifiedStatus());
        t.setAcPayment(trade.getAcPayment());
        t.setPayAmount(trade.getPayAmount());
        t.setUserId(trade.getUserId());
        t.setSysStatus(trade.getSysStatus());
        t.setPayAmount(trade.getPayAmount());
        t.setIsPresell(trade.getIsPresell());
        t.setPlatformDiscountFee(trade.getPlatformDiscountFee());
        t.setType(trade.getType());
        t.setOrderList(CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade)) ? new ArrayList<>() : TradeUtils.getOrders4Trade(trade));
        if (Objects.nonNull(trade.getTradeExt())) {
            TradeExt tradeExt = trade.getTradeExt();
            t.setLogisticsCode(tradeExt.getLogisticsCode());
            t.setStoreName(tradeExt.getStoreName());
            t.setTmallAsdpAds(tradeExt.getTmallAsdpAds());
            t.setTmallAsdpBizType(tradeExt.getTmallAsdpBizType());
            initExtraFieldsMap(tradeExt);
            t.setSellerFlagTag(TradeExtUtils.getSellerFlagTag(tradeExt));
            fillTradeModelByExtraField(staff,tradeExt, tradeExt.getExtraFieldsMap(), t, trade);
        }
        if(Objects.nonNull(trade.getTradeDistributor())){
            TradeDistributor distributor = trade.getTradeDistributor();
            t.setDistributorId(distributor.getDistributorId());
            t.setDistributorName(distributor.getDistributorName());
        }
        t.setSalesmanId(trade.getSalesmanId());
        t.setSalesmanName(trade.getSalesmanName());
        t.setVisualCost(MathUtils.toScaleString(staff.getCompanyId(),TradeUtils.calculateCost(trade)));
        return t;
    }

    private static void initExtraFieldsMap(TradeExt tradeExt){
        if(tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields())) {
            return;
        }
        if(tradeExt.getExtraFieldsMap() == null || tradeExt.getExtraFieldsMap().isEmpty()){
            try {
                Map extraFieldsMap = JSONObject.parseObject(tradeExt.getExtraFields(), Map.class);
                tradeExt.setExtraFieldsMap(extraFieldsMap);
            } catch (Exception e){
                logger.warn(String.format("订单%s的extraField转换Map异常，extraField：%s", tradeExt.getTid(), tradeExt.getExtraFields()));
            }
        }
    }

    /**
     * tradeExt表的extraField字段填充到MessageMemo
     * @param tradeExt
     * @param extraFieldsMap
     * @param memo
     */
    private static void fillTradeModelByExtraField(Staff staff,TradeExt tradeExt, Map<String, Object> extraFieldsMap, MessageMemo memo,Trade trade){
        if(memo == null || tradeExt == null || extraFieldsMap == null || extraFieldsMap.isEmpty()){
            return;
        }
        handleVipjit(extraFieldsMap,memo,trade);
        memo.setIncreaseFreightExpress((String)extraFieldsMap.get("increaseFreightExpress"));
        memo.setIncreaseFreightFee((String)extraFieldsMap.get("increaseFreightFee"));
        memo.setCommunityGroupId((String)extraFieldsMap.get("communityGroupId"));
        memo.setCommunityGroupRole((String)extraFieldsMap.get("communityGroupRole"));
        memo.setAliHealthMainId((Long)extraFieldsMap.get("alihealthMainId"));
        memo.setSelfBuiltDepositAmount((String) extraFieldsMap.get("selfBuiltDepositAmount"));
        memo.setSelfBuiltPaymentReceivable((String) extraFieldsMap.get("selfBuiltPaymentReceivable"));
        handleFxTrade(extraFieldsMap, memo, trade);

        Object tradePictureMemoUris = extraFieldsMap.get("tradePictureMemoUris");
        if (Objects.isNull(tradePictureMemoUris)){
            return;
        }
        if (tradePictureMemoUris instanceof List){
            try {
                List<String> tempList = (List<String>) tradePictureMemoUris;
                memo.setTradePictureMemoUris(tempList);
            }catch (RuntimeException e){
                logger.warn(String.format("公司:%s,订单sid:%s,tradePictureMemoUris转换异常,tradePictureMemoUris:%s",staff.getCompanyId(),tradeExt.getSid(),JSONObject.toJSONString(tradePictureMemoUris)));
            }
        }else {
            logger.warn(String.format("公司:%s,订单sid:%s,tradePictureMemoUris不是List,tradePictureMemoUris:%s",staff.getCompanyId(),tradeExt.getSid(),JSONObject.toJSONString(tradePictureMemoUris)));
        }

    }

    private static void handleFxTrade(Map<String, Object> extraFieldsMap, MessageMemo memo, Trade trade) {
        if (!TradeUtils.isFxOrMixTrade(trade)) {
            return;
        }
        memo.setFxTotalPrice((String) extraFieldsMap.get("fxTotalPrice"));//分销价
        memo.setFxCommission((String) (extraFieldsMap.get("fxCommission")));//佣金
        memo.setFxCost((PaymentUtils.calculateFxTotalCost(trade)));//分销总成本
    }

    /**
     * 处理唯品会订单-优先发货/重点检查标识
     *
     * @param extraFieldsMap
     * @param memo
     * @param trade
     */
    private static void handleVipjit(Map<String, Object> extraFieldsMap, MessageMemo memo, Trade trade) {
        if(memo == null || trade == null || extraFieldsMap == null || extraFieldsMap.isEmpty()){
            return;
        }
        if(!CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource())){
            return;
        }
        try{
            Integer qualityCheck = (Integer) extraFieldsMap.get("quality_check");
            memo.setQualityCheck(Objects.equals(qualityCheck,1));
            Integer priorityDelivery = (Integer) extraFieldsMap.get("priority_delivery");
            memo.setPriorityDelivery(Objects.equals(priorityDelivery,1));
            memo.setQualityCheckType((String) extraFieldsMap.get("qualityCheckType"));
            memo.setActionCode((String) extraFieldsMap.get("actionCode"));
            memo.setOrderLabel((String) extraFieldsMap.get("orderLabel"));
            String checkItemsInfos = (String) extraFieldsMap.get("checkItemsInfos");
            if(StringUtils.isNotBlank(checkItemsInfos)){
                try {
                    JSONArray checkItemsInfosObj = JSONObject.parseArray(checkItemsInfos);
                    memo.setCheckItemsInfos(checkItemsInfosObj);
                } catch (Exception e){
                    logger.warn(String.format("订单sid=%s的extraField.checkItemsInfos转换JSONObject异常，checkItemsInfos：%s", trade.getSid(),checkItemsInfos),e);
                }
            }
        }catch (Exception e){
            logger.warn(String.format("订单sid=%s的extraField获取值失败，extraFields=%s", trade.getSid(),JSONObject.toJSONString(extraFieldsMap)),e);
        }
    }

    /**
     * 分销金额 分销单才有
     */
    String fxTotalPrice;

    /**
     * 佣金 分销单才有
     */
    String fxCommission;
    /**
     * 分销成本 分销单才有
     */
    String fxCost;

    public String getFxTotalPrice() {
        return fxTotalPrice;
    }

    public void setFxTotalPrice(String fxTotalPrice) {
        this.fxTotalPrice = fxTotalPrice;
    }

    public String getFxCommission() {
        return fxCommission;
    }

    public void setFxCommission(String fxCommission) {
        this.fxCommission = fxCommission;
    }

    public String getFxCost() {
        return fxCost;
    }

    public void setFxCost(String fxCost) {
        this.fxCost = fxCost;
    }
}

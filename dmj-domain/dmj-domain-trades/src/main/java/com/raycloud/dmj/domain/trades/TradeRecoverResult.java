package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: ruanyaguang
 * @Date : 2017/5/25
 * @Info : 订单修复结果
 */
public class TradeRecoverResult implements Serializable {
    private static final long serialVersionUID = 3892719955466611244L;

    // 成功修复的订单
    private List<Trade> successRecoverTrades;

    public List<Trade> getSuccessRecoverTrades() {
        return successRecoverTrades;
    }

    public void setSuccessRecoverTrades(List<Trade> successRecoverTrades) {
        this.successRecoverTrades = successRecoverTrades;
    }
}

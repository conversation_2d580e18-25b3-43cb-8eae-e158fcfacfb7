package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.*;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2023/8/8 2:58 下午
 * @Description:
 */
public class OrderExtUtils {
    /**
     *  判断退款数量是否有变动
     * @param cur
     * @param origin
     * @return
     */
    public static boolean equalsRefundOrderNum(OrderExt cur, OrderExt origin) {
        int refundedCnt = cur.getRefundedCnt();
        int refundingCnt = cur.getRefundingCnt();
        int originRefundedCnt = origin.getRefundedCnt();
        int originRefundingCnt = origin.getRefundingCnt();
        return refundedCnt == originRefundedCnt && originRefundingCnt == refundingCnt;
    }

    /**
     *  复制order的orderExt
     * @param staff
     * @param order
     * @return
     */
    public static OrderExt initOrderExt(Staff staff, Order order){
        OrderExt originOrderExt = order.getOrderExt();
        if(originOrderExt==null){
            return null;
        }
        OrderExt orderExt=new OrderExt();
        orderExt.setTid(order.getTid());
        orderExt.setSid(order.getSid());
        orderExt.setCompanyId(originOrderExt.getCompanyId());
        orderExt.setEnableStatus(originOrderExt.getEnableStatus());
        orderExt.setCustomization(originOrderExt.getCustomization());
        orderExt.setSupplierName(originOrderExt.getSupplierName());
        orderExt.setSupplierItemOuterId(originOrderExt.getSupplierItemOuterId());
        orderExt.setCooperationNoJitx(originOrderExt.getCooperationNoJitx());
        orderExt.setAuthorId(originOrderExt.getAuthorId());
        orderExt.setAuthorName(originOrderExt.getAuthorName());
        orderExt.setOrderRemark(originOrderExt.getOrderRemark());
        orderExt.setPromiseAcceptTime(originOrderExt.getPromiseAcceptTime());
        orderExt.setExtraFields(originOrderExt.getExtraFields());
        return orderExt;
    }


    public static List<OrderExt> getOrderExts(List<Order> orders) {
        List<OrderExt> orderExts = new ArrayList<>();
        for (Order order : orders) {
            if (order.getOrderExt() != null) {
                orderExts.add(order.getOrderExt());
            }
        }
        return orderExts;
    }


    public static boolean isRefundNumOrderExt(OrderExt orderExt){
        if (orderExt != null && StringUtils.isNotBlank(orderExt.getCustomization())) {
            int skuCnt = orderExt.getSkuCnt();
            int refundingCnt = orderExt.getRefundingCnt();
            int refundedCnt = orderExt.getRefundedCnt();
            return refundingCnt != 0 || refundedCnt != 0 || skuCnt != 0;
        }
        return false;
    }

    public static boolean isRefundNumExceptOrderExt(OrderExt orderExt) {
        if (orderExt != null && StringUtils.isNotBlank(orderExt.getCustomization())) {
            int skuCnt = orderExt.getSkuCnt();
            int refundingCnt = orderExt.getRefundingCnt();
            int refundedCnt = orderExt.getRefundedCnt();
            return (refundingCnt != 0 || refundedCnt != 0 || skuCnt != 0) && (refundingCnt + refundedCnt != skuCnt && (refundedCnt + refundingCnt) != 0)
            ;
        }
        return false;
    }

    /**
     * 添加指定的key-value到orderExt的extraField里
     * 只支持添加不存在的key
     * @param orderExt
     * @param key
     * @param value
     * @return
     */
    public static OrderExt setExtraFieldValue(OrderExt orderExt, String key, Object value) {
        if (orderExt == null || key == null || value == null) {
            return orderExt;
        }

        String extraFields = orderExt.getExtraFields();
        List<Object> extraFieldsList = orderExt.getExtraFieldsList();
        Map<String, Object> extraFieldsMap = orderExt.getExtraFieldsMap();

        if (StringUtils.isEmpty(extraFields)) {
            Map<String, Object> extraFields4Map = Maps.newHashMapWithExpectedSize(2);
            extraFields4Map.put(key,value);
            orderExt.setExtraFields(JSONObject.toJSONString(extraFields4Map));
            orderExt.setExtraFieldsMap(JSONObject.parseObject(extraFields, Map.class));
        } else {
            if (MapUtils.isEmpty(extraFieldsMap)) {
                extraFieldsMap = JSONObject.parseObject(extraFields, Map.class);
                orderExt.setExtraFieldsMap(extraFieldsMap);
            }
            extraFieldsMap.put(key, value);
            orderExt.setExtraFields(JSONObject.toJSONString(extraFieldsMap));
        }

        if (CollectionUtils.isEmpty(extraFieldsList)) {
            extraFieldsList = new ArrayList<>();
        }
        extraFieldsList.add("$." + key);
        extraFieldsList.add(value);
        orderExt.setExtraFieldsList(extraFieldsList);

        return orderExt;
    }

    /**
     * 根据key获取extraFieldMap里的值
     * @param orderExt
     * @param key
     * @return
     */
    public static Object getExtraFieldValue(OrderExt orderExt, String key) {
        if (orderExt == null || StringUtils.isBlank(orderExt.getExtraFields()) || StringUtils.isBlank(key)) {
            return null;
        }
        return orderExt.get(key);
    }

    public static String getExtraFieldValueStr(OrderExt orderExt, String key) {
        Object result = getExtraFieldValue(orderExt, key);
        if (result == null) {
            return "";
        }
        return String.valueOf(result);
    }

    public static Map<String, Object> parseExtraFields(OrderExt orderExt){
        if (orderExt != null){
            try {
                if (StringUtils.isNotEmpty(orderExt.getExtraFields())){
                    return JSON.parseObject(orderExt.getExtraFields(), Map.class);
                }
            }catch (Exception e){
                Logs.error("转换orderExt中扩展配置出错，jsonStr:" + orderExt.getExtraFields(), e);
            }
        }
        return new HashMap<>();
    }

    public static Map<String, Object> parseCustomization(OrderExt orderExt){
        if (orderExt != null){
            try {
                if (StringUtils.isNotEmpty(orderExt.getCustomization())){
                    return JSON.parseObject(orderExt.getCustomization(), new TypeReference<Map<String, Object>>() {});
                }
            }catch (Exception e){
                Logs.error("转换orderExt中订单定制信息出错，jsonStr:" + orderExt.getCustomization(), e);
            }
        }
        return new HashMap<>();
    }

}

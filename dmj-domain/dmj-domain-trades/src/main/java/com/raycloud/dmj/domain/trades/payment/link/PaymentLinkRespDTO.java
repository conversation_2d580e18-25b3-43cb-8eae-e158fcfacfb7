package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OrderOpeartEnum;
import lombok.Data;

/**
 * @description:
 * @author: pxh
 * @create: 2021-07-29 17:49
 **/
@Data
public class PaymentLinkRespDTO {

    /**
     * 优惠金额
     */
    private double discountFee;

    /**
     * 应付金额
     */
    private double totalFee;

    /**
     * 实付金额
     */
    private double payment;

    /**
     * trade级别优惠金额
     */
    private double tradeDiscountFee;

    private OrderOpeartEnum orderOpeart;
}

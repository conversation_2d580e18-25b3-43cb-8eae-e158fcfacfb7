package com.raycloud.dmj.domain.deliver;

import java.io.Serializable;

/**
 * 提供给基础组那边发货单二维码扫描数据的vo
 *
 * @author: qingfeng.cxb
 * @create: 2019-05-14 15:37
 */
public class DeliverVo implements Serializable {
    private static final long serialVersionUID = 37806579499137348L;
    /**
     * 规格商品图片路径
     */
    private String skuPicUrl;
    /**
     * 商品名称
     */
    private String itemTitle;
    /**
     * 规格名称
     */
    private String skuTitle;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 实付金额
     */
    private String payment;
    /**
     * 商品ID
     */
    private Long sysItemId;
    /**
     * 规格ID
     */
    private Long sysSkuId;

    public String getSkuPicUrl() {
        return skuPicUrl;
    }

    public void setSkuPicUrl(String skuPicUrl) {
        this.skuPicUrl = skuPicUrl;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public String getSkuTitle() {
        return skuTitle;
    }

    public void setSkuTitle(String skuTitle) {
        this.skuTitle = skuTitle;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }
}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.enums.TradeDistributePatternEnum;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 交易o2o表
 */
@Table(name = "trade_o2o")
public class TradeO2o extends Model {

    private static final long serialVersionUID = -6773040567150197546L;

    private Long sid;

    private Long companyId;

    private Long userId;

    private String tid;

    /**
     * o2o订单平台状态
     */
    @Column(name = "status")
    private String status;

    /**
     * o2o订单系统状态
     */
    @Column(name = "sys_status")
    private String sysStatus;

    /**
     * 订单配送方式
     *
     * @see TradeDistributePatternEnum
     */
    @Column(name = "distribute_pattern")
    private String distributePattern;

    /**
     * 配送员电话
     */
    @Column(name = "distribute_mobile")
    private String distributeMobile;

    /**
     * 配送员姓名
     */
    @Column(name = "distribute_name")
    private String distributeName;

    /**
     * 配送异常编码
     */
    @Column(name = "distribute_exception_code")
    private String distributeExceptionCode;

    /**
     * 配送异常编码描述
     */
    private String distributeExceptionDesc;

    /**
     * 软删除字段
     */
    @Column(name = "enable_status")
    private Integer enableStatus;

    /**
     * 扩充字段
     * 后续需要添加的字段都可以放在这个字段里，json格式
     */
    @Column(name = "extra_fields")
    private String extraFields;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 平台流水号
     */
    private String daySeq;

    /**
     * 将map中的数据以key1,value1,key2,value2...的顺序放入到list中，方便sql拼接
     */
    @JSONField(serialize = false)
    private List<Object> extraFieldsList;

    /**
     * 需要移除的key集合
     */
    @JSONField(serialize = false)
    private List<String> removeExtraFieldsList;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getDistributePattern() {
        return distributePattern;
    }

    public void setDistributePattern(String distributePattern) {
        this.distributePattern = distributePattern;
    }

    public String getDistributeMobile() {
        return distributeMobile;
    }

    public void setDistributeMobile(String distributeMobile) {
        this.distributeMobile = distributeMobile;
    }

    public String getDistributeName() {
        return distributeName;
    }

    public void setDistributeName(String distributeName) {
        this.distributeName = distributeName;
    }

    public String getDistributeExceptionCode() {
        return distributeExceptionCode;
    }

    public void setDistributeExceptionCode(String distributeExceptionCode) {
        this.distributeExceptionCode = distributeExceptionCode;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(String extraFields) {
        this.extraFields = extraFields;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public List<Object> getExtraFieldsList() {
        return extraFieldsList;
    }

    public void setExtraFieldsList(List<Object> extraFieldsList) {
        this.extraFieldsList = extraFieldsList;
    }

    public List<String> getRemoveExtraFieldsList() {
        return removeExtraFieldsList;
    }

    public void setRemoveExtraFieldsList(List<String> removeExtraFieldsList) {
        this.removeExtraFieldsList = removeExtraFieldsList;
    }

    public String getDistributeExceptionDesc() {
        return distributeExceptionDesc;
    }

    public void setDistributeExceptionDesc(String distributeExceptionDesc) {
        this.distributeExceptionDesc = distributeExceptionDesc;
    }


    public String getDaySeq() {
        return daySeq;
    }

    public void setDaySeq(String daySeq) {
        this.daySeq = daySeq;
    }

    /**
     *
     * 不落库
     * 减少频繁json转map
     */
    @JSONField(serialize = false)
    private Map<String, Object> extraFieldsMap;

    public Map<String, Object> getExtraFieldsMap() {
        if (extraFieldsMap == null){
            extraFieldsMap = parseExtraFields(this);
        }
        return extraFieldsMap;
    }

    public void setExtraFieldsMap(Map<String, Object> extraFieldsMap) {
        this.extraFieldsMap = extraFieldsMap;
    }

    /**
     * 获取额外属性
     * @return
     */
    @JSONField(serialize = false)
    public Object get(String key){
        if (extraFieldsMap == null){
            extraFieldsMap = parseExtraFields(this);
        }
        return extraFieldsMap.get(key);
    }


    public static Map<String, Object> parseExtraFields(TradeO2o tradeO2o){
        if (tradeO2o != null){
            try {
                if (StringUtils.isNotEmpty(tradeO2o.getExtraFields())){
                    return JSON.parseObject(tradeO2o.getExtraFields(), Map.class);
                }
            }catch (Exception e){
                Logs.error("转换tradeO2o中扩展配置出错，jsonStr:" + tradeO2o.getExtraFields(), e);
            }
        }
        return new HashMap<>();
    }


    /**
     * 添加指定的key-value到tradeExt的extraField里
     * 只支持添加不存在的key
     *
     * @param tradeO2o
     * @param key
     * @param value
     * @return
     */
    public static TradeO2o setExtraFieldValue(TradeO2o tradeO2o, String key, Object value) {
        if (tradeO2o == null || key == null || value == null) {
            return tradeO2o;
        }

        String extraFields = tradeO2o.getExtraFields();
        List<Object> extraFieldsList = tradeO2o.getExtraFieldsList();
        Map<String, Object> extraFieldsMap = tradeO2o.getExtraFieldsMap();

        if (StringUtils.isEmpty(extraFields)) {
            Map<String, Object> extraFields4Map = Maps.newHashMapWithExpectedSize(2);
            extraFields4Map.put(key,value);
            tradeO2o.setExtraFields(JSONObject.toJSONString(extraFields4Map));
            tradeO2o.setExtraFieldsMap(JSONObject.parseObject(extraFields, Map.class));
        } else {
            if (MapUtils.isEmpty(extraFieldsMap)) {
                extraFieldsMap = JSONObject.parseObject(extraFields, Map.class);
                tradeO2o.setExtraFieldsMap(extraFieldsMap);
            }
            extraFieldsMap.put(key, value);
            tradeO2o.setExtraFields(JSONObject.toJSONString(extraFieldsMap));
        }

        if (CollectionUtils.isEmpty(extraFieldsList)) {
            extraFieldsList = new ArrayList<>();
        }
        extraFieldsList.add("$." + key);
        extraFieldsList.add(value);
        tradeO2o.setExtraFieldsList(extraFieldsList);

        return tradeO2o;
    }
}
package com.raycloud.dmj.domain.consign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/20 11:10
 * @Description 发货事件数据
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsignEventData implements Serializable {

    private static final long serialVersionUID = 7578213427026661634L;

    /**
     * 系统订单号
     */
    private List<Long> sids;

    /**
     * 是否异常发货
     */
    private Boolean exceptConsign;

    /**
     * 发货类型
     */
    private String consignType;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 无需物流发货发货类型
     */
    private Integer dummyType;

    /**
     * 无需物流发货-配送人员
     */
    private String noLogisticsName;

    /**
     * 联系电话
     */
    private String noLogisticsTel;

    /**
     * 不需要执行上传的Sid
     */
    private Set<Long> parallelUploadSids;

    /**
     * 当前操作人id
     */
    private Long staffId;

    /**
     * 上下文id
     */
    private Long clueId;

}

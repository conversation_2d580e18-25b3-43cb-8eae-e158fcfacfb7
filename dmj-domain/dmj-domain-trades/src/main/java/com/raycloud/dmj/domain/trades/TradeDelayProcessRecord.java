package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.util.Date;

/**
 * 订单延迟处理记录表
 */
@Data
@Table(name = "trade_delay_process_record")
public class TradeDelayProcessRecord extends Model {
    private static final long serialVersionUID = -5557282718984348563L;
    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long userId;

    /**
     * 订单来源平台
     */
    private String source;

    /**
     * 订单系统ID
     */
    private Long sid;

    /**
     * 业务类型：halt：挂起
     */
    private String businessType;

    /**
     * 发生的时间
     */
    private Date occurTime;

    /**
     * 时间单位 1：分 2：小时 3：天 4：月 5：年
     */
    private Integer occurTimeUnit;

    /**
     * 延迟的数值，如果单位为分，1就表示1分钟
     */
    private Integer occurTimeDelayNum;

    /**
     * 失效时间，=occur+延迟的时间
     */
    private Date expTime;

    /**
     * 处理状态，0：未处理，1：处理成功 2：处理失败，默认为0
     */
    private Integer processStatus;

    /**
     * 扩展属性
     */
    private String extend;

    /**
     * 数据状态 1：有效 0：无效
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;
}
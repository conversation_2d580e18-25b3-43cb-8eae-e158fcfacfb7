package com.raycloud.dmj.domain.trades;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.raycloud.erp.db.model.Model;

import java.util.List;

/**
 * 列配置规则
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ColumnConfRule extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 列配置规则ID
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 规则模版名称
     */
    private String columnRuleName;

    /**
     * 设置值
     */
    private String columnConfig;

    /**
     * 所属模块名称
     */
    private String module;

    /**
     * 所属模块编码 对应 ModuleEnum
     */
    private Integer moduleId;

    /**
     * 列所属页面ID
     */
    private Long pageId;

    /**
     * 状态
     */
    private Integer enableStatus;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 列配置信息
     */
    private List<ColumnConf> columnConfList;
}


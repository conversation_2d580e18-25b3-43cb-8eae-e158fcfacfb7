package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class ExcludeOrderTypeRenderer implements IFieldValueRenderer {

    /**
     * 排除商品类型
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "普通商品";
        }
        if (Objects.equals(originValue,1)) {
            return "赠品";
        }
        if (Objects.equals(originValue,2)) {
            return "套件商品";
        }
        if (Objects.equals(originValue,3)) {
            return "组合商品";
        }
        if (Objects.equals(originValue,4)) {
            return "加工商品";
        }
        return String.valueOf(originValue);
    }
}

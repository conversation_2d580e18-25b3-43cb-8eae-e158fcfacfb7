package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Table;

import java.util.Date;

@Table(name = "order", routerKey = "orderDbNo")
public class TbOrder extends Order {

    /**
     *
     */
    private static final long serialVersionUID = 1391807073326154500L;
    
    /**
     * item_meal_id
     */
    private Long itemMealId;
    /**
     * is_oversold
     */
    private Long isOversold;
    /**
     * is_service_order
     */
    private Long isServiceOrder;
    /**
     * item_meal_name
     */
    private String itemMealName;
    /**
     * seller_nick
     */
    private String sellerNick;
    /**
     * buyer_nick
     */
    private String buyerNick;
    /**
     * timeout_action_time
     */
    private Date timeoutActionTime;
    /**
     * buyer_rate
     */
    private Long buyerRate;
    /**
     * seller_rate
     */
    private Long sellerRate;
    /**
     * seller_type
     */
    private String sellerType;
    /**
     * cid
     */
    private Long cid;
    
    private String ydId;

    /**
     * 不持久化数据库,用于接收外部系统商品id
     */
    private String erpItemId;

    /**
     * 定制信息
     */
    private String customization;

    /**
     * @return itemMealId item_meal_id
     */
    public Long getItemMealId() {
        return itemMealId;
    }

    /**
     * @param itemMealId item_meal_id
     */
    public TbOrder setItemMealId(Long itemMealId) {
        this.itemMealId = itemMealId;
        return this;
    }

    /**
     * @return isOversold is_oversold
     */
    public Long getIsOversold() {
        return isOversold;
    }

    /**
     * @param isOversold is_oversold
     */
    public TbOrder setIsOversold(Long isOversold) {
        this.isOversold = isOversold;
        return this;
    }

    /**
     * @return isServiceOrder is_service_order
     */
    public Long getIsServiceOrder() {
        return isServiceOrder;
    }

    /**
     * @param isServiceOrder is_service_order
     */
    public TbOrder setIsServiceOrder(Long isServiceOrder) {
        this.isServiceOrder = isServiceOrder;
        return this;
    }

    /**
     * @return itemMealName item_meal_name
     */
    public String getItemMealName() {
        return itemMealName;
    }

    /**
     * @param itemMealName item_meal_name
     */
    public TbOrder setItemMealName(String itemMealName) {
        this.itemMealName = itemMealName;
        return this;
    }

    /**
     * @return sellerNick seller_nick
     */
    public String getSellerNick() {
        return sellerNick;
    }

    /**
     * @param sellerNick seller_nick
     */
    public TbOrder setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
        return this;
    }

    /**
     * @return buyerNick buyer_nick
     */
    public String getBuyerNick() {
        return buyerNick;
    }

    /**
     * @param buyerNick buyer_nick
     */
    public TbOrder setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
        return this;
    }

    /**
     * @return timeoutActionTime timeout_action_time
     */
    public Date getTimeoutActionTime() {
        return timeoutActionTime;
    }

    /**
     * @param timeoutActionTime timeout_action_time
     */
    public TbOrder setTimeoutActionTime(Date timeoutActionTime) {
        this.timeoutActionTime = timeoutActionTime;
        return this;
    }

    /**
     * @return buyerRate buyer_rate
     */
    public Long getBuyerRate() {
        return buyerRate;
    }

    /**
     * @param buyerRate buyer_rate
     */
    public TbOrder setBuyerRate(Long buyerRate) {
        this.buyerRate = buyerRate;
        return this;
    }

    /**
     * @return sellerRate seller_rate
     */
    public Long getSellerRate() {
        return sellerRate;
    }

    /**
     * @param sellerRate seller_rate
     */
    public TbOrder setSellerRate(Long sellerRate) {
        this.sellerRate = sellerRate;
        return this;
    }

    /**
     * @return sellerType seller_type
     */
    public String getSellerType() {
        return sellerType;
    }

    /**
     * @param sellerType seller_type
     */
    public TbOrder setSellerType(String sellerType) {
        this.sellerType = sellerType;
        return this;
    }

    /**
     * @return cid cid
     */
    public Long getCid() {
        return cid;
    }

    /**
     * @param cid cid
     */
    public TbOrder setCid(Long cid) {
        this.cid = cid;
        return this;
    }

	public String getYdId() {
		return ydId;
	}

	public TbOrder setYdId(String ydId) {
		this.ydId = ydId;
        return this;
	}

    public String getErpItemId() {
        return erpItemId;
    }

    public void setErpItemId(String erpItemId) {
        this.erpItemId = erpItemId;
    }

    public String getCustomization() {
        return customization;
    }

    public void setCustomization(String customization) {
        this.customization = customization;
    }
}
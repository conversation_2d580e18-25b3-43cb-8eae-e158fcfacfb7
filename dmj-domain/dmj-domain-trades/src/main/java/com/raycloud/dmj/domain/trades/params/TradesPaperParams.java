package com.raycloud.dmj.domain.trades.params;

import java.io.Serializable;

/**
 * 拣选领单业务params
 *
 * <AUTHOR>
 * @date 2019/12/18
 */
public class TradesPaperParams implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long waveId;

    private Long staffId;

    private String staffName;

    /**
     * 是否强制分配
     * 默认：false，需要检查
     */
    private Boolean forceAssign;

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Boolean getForceAssign() {
        return forceAssign;
    }

    public void setForceAssign(Boolean forceAssign) {
        this.forceAssign = forceAssign;
    }
}

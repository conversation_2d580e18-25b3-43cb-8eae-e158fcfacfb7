package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trades.TradeConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TradeConfigUtils
 *
 * <AUTHOR>
 * @Date 2019-06-18
 * @Time 17:50
 */
@Deprecated
public class TradeConfigUtils {

    /**
     * 订单同步时候匹配
     */
    public static final int SYS_PRESELL_GIFT_MATCH_CONFIG_ONE = 1;

    /**
     * 解锁时候匹配
     */
    public static final int SYS_PRESELL_GIFT_MATCH_CONFIG_TWO = 2;


    public static boolean parseExtendConfigWithResult(Map<String, Object> result, TradeConfig tradeConfig) {

        if (tradeConfig != null && result != null) {
            try {
                result.putAll(parseExtendConfig(tradeConfig.getChatConfigs()));
            } catch (Exception e) {
                Logs.error("转换tradeConfig中扩展配置出错，jsonStr:" + tradeConfig.getChatConfigs(), e);
                return false;
            }
        }
        return true;
    }

    public static Map<String, Object> parseExtendConfig(TradeConfig tradeConfig) {
        if (tradeConfig != null) {
            try {
                return parseExtendConfig(tradeConfig.getChatConfigs());
            } catch (Exception e) {
                Logs.error("转换tradeConfig中扩展配置出错，jsonStr:" + tradeConfig.getChatConfigs(), e);
            }
        }
        return new HashMap<>();
    }


    public static Map<String, Object> parseExtendConfig(String tradeConfigStr) {
        if (StringUtils.isNotBlank(tradeConfigStr)) {
            Map<String, Object> map = JSON.parseObject(tradeConfigStr, Map.class);
            return map == null ? new HashMap<>() : map;
        }
        return new HashMap<>();
    }

    /**
     * 导出时是否不加密
     *
     * @param tradeConfig
     * @return
     */
    public static boolean tradeExportNotEncrypt(TradeConfig tradeConfig) {
        if (tradeConfig != null && StringUtils.isNotBlank(tradeConfig.getString(TradeExtendConfigsEnum.TRADE_DISCLAIMER_START.getKey()))) {
            Date now = new Date();
            Date start = DateUtils.str2Date(tradeConfig.getString(TradeExtendConfigsEnum.TRADE_DISCLAIMER_START.getKey()), now);
            Date end = DateUtils.str2Date(tradeConfig.getString(TradeExtendConfigsEnum.TRADE_DISCLAIMER_END.getKey()), now);
            return now.after(start) && now.before(end);
        }
        return false;
    }

    /**
     * 判断当前是否开启bic质检流程配置
     */
    public static boolean openBicQualityTesting(TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return false;
        }

        Object open = tradeConfig.get(TradeExtendConfigsEnum.OPEN_BIC_QUALITY_TESTING.getKey());
        if (open == null || (Integer) open != 1) {
            return false;
        }

        Object openShop = tradeConfig.get(TradeExtendConfigsEnum.OPEN_BIC_QUALITY_TESTING_SHOP.getKey());
        if (openShop == null || StringUtils.isEmpty((String) openShop)) {
            return false;
        }

        return true;
    }

    /**
     * 判断当前是否开启BTAS质检流程配置
     */
    public static boolean openBtasQualityTesting(TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return false;
        }

        Object open = tradeConfig.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING.getKey());
        if (open == null || (Integer) open != 1) {
            return false;
        }

        return true;
    }

    /**
     * 仅判断当前店铺是否开启BTAS质检流程配置
     */
    public static boolean openBtasQualityTestingShopOnly(TradeConfig tradeConfig,Long userId){
        if (userId==null){
            return false;
        }
        String userIds = (String) tradeConfig.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING_SHOP.getKey());
        String[] userArr = userIds.split(",");
        for (String _userId : userArr) {
            if (_userId.equals(userId.toString())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否开启BTAS订单保价功能
     * @param tradeConfig
     * @return
     */
    public static boolean openBtasInsuredPrice(TradeConfig tradeConfig){
        if (tradeConfig == null) {
            return false;
        }

        Object open = tradeConfig.get(TradeExtendConfigsEnum.BTAS_INSURED_PRICE.getKey());
        if (open == null || (Integer) open != 1) {
            return false;
        }

        return true;
    }

    /**
     * 检验BTAS订单保价配置类型
     * @param userId
     * @return 0：未开启，1：开启未匹配店铺，2：开启且成功匹配勾选店铺
     */
    public static int getInsuredPriceType(TradeConfig tradeConfig, Long userId){
        if (!openBtasInsuredPrice(tradeConfig)){
            return 0;
        }
        Object shops = tradeConfig.get(TradeExtendConfigsEnum.BTAS_INSURED_SHOP_IDS.getKey());
        if (StringUtils.isEmpty((String)shops)){
            return 2;
        }
        JSONArray jsonArray = JSONArray.parseArray((String)shops);
        if (jsonArray.isEmpty()){
            return 2;
        }
        return jsonArray.toString().contains(userId + "") ? 2 : 1;
    }

    /**
     * 判断当前是否开启订单存在指定异常时自动反审核配置
     */
    public static boolean openExistExcepAutoUndoAudit(TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return false;
        }
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        if (StringUtils.isEmpty(tradeExtendConfig)) {
            return false;
        }

        //获取配置中设置的订单状态
        Object tradeStatus = tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_STATUS.getKey());
        int status = tradeStatus != null ? (Integer) tradeStatus : 0;

        //获取配置中设置的订单异常
        Object tradeExceptions = tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_EXCEPTION.getKey());
        List<String> exceptions = tradeExceptions != null ? Arrays.asList(((String) tradeExceptions).split(",")) : new ArrayList<>(0);

        if (status == 0 || exceptions.size() == 0) {
            return false;
        }

        return true;
    }

    /**
     * 判断当前是否开启订单存在指定异常时自动反审核配置
     */
    public static Integer getSysPresellGiftMatchConfig(TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return (Integer) NewTradeExtendConfigEnum.SYS_PRESELL_GIFT_MATCH_CONFIG.getDefaultValue();
        }
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        Map<String, Object> tradeExtendConfigMap = (Map<String, Object>) JSONObject.parse(tradeExtendConfig);

        if (tradeExtendConfigMap == null || tradeExtendConfigMap.size() == 0) {
            return (Integer) NewTradeExtendConfigEnum.SYS_PRESELL_GIFT_MATCH_CONFIG.getDefaultValue();
        }

        Object sysPresellGiftMatchConfig = tradeExtendConfigMap.get(NewTradeExtendConfigEnum.SYS_PRESELL_GIFT_MATCH_CONFIG.getKey());
        return sysPresellGiftMatchConfig != null ? (Integer) sysPresellGiftMatchConfig : (Integer) NewTradeExtendConfigEnum.SYS_PRESELL_GIFT_MATCH_CONFIG.getDefaultValue();
    }

    /**
     * 只有【是否开启重新上传发货】开启 才生效
     * 是否开启预发货的拆分订单重新上传发货：
     */
    public static boolean isOpenSplitReUploadConsign(TradeConfig tradeConfig) {
        return tradeConfig != null && tradeConfig.getOpenReUploadConsign() != null && tradeConfig.getOpenReUploadConsign() == 1 && tradeConfig.getOpenSplitReUploadConsign() != null && tradeConfig.getOpenSplitReUploadConsign() == 1;
    }

    /**
     * 判断当前是否开启改地址自动匹配赠品
     */
    public static boolean isOpenAddressChangeRematchGift(TradeConfig tradeConfig) {
        Object value = getTradeExtendConfigValue(tradeConfig, NewTradeExtendConfigEnum.ADDRESS_CHANGE_REMATCH_GIVEAWAY.getKey());
        return value != null && ((Integer) value == 1);
    }

    /**
     * 判断是否同步天猫分销订单
     */
    public static boolean isSyncPlatformFx(TradeConfig tradeConfig) {
        Object value = getTradeExtendConfigValue(tradeConfig, NewTradeExtendConfigEnum.SYNC_PLATFORM_FX_CONFIG.getKey());
        return value != null && ((Integer) value == 1);
    }




    private static Object getTradeExtendConfigValue(TradeConfig tradeConfig, String key) {
        if (tradeConfig == null) {
            return null;
        }
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        Map<String, Object> tradeExtendConfigMap = parseExtendConfig(tradeExtendConfig);
        return tradeExtendConfigMap.get(key);
    }

    public static List<String> getExceptionsByConfig(TradeConfig tradeConfig, NewTradeExtendConfigEnum newTradeExtendConfigEnum) {
        List<String> exceptions = Lists.newArrayList();
        if (tradeConfig == null) {
            return exceptions;
        }
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        Map<String, Object> tradeExtendConfigMap = (Map<String, Object>) JSONObject.parse(tradeExtendConfig);

        if (tradeExtendConfigMap == null || tradeExtendConfigMap.size() == 0) {
            return exceptions;
        }

        //获取配置中设置的订单异常
        Object tradeExceptions = tradeExtendConfigMap.get(newTradeExtendConfigEnum.getKey());
        exceptions = tradeExceptions != null ? Arrays.asList(((String) tradeExceptions).split(",")) : new ArrayList<>(0);

        return exceptions.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 判断当前是否开启订单自动拆分
     */
    public static boolean isOpenTradeAutoSplit(TradeConfig tradeConfig) {
        Object value = getTradeExtendConfigValue(tradeConfig, NewTradeExtendConfigEnum.TRADE_AUTO_SPLIT.getKey());
        return value != null && ((Integer) value == 1);
    }

    /**
     * 将tradeConfig转为新的配置方式
     */
    public static Map<String, String> tradeConfig2ConfigMap(Staff staff, TradeConfig tradeConfig) {
        Map<String, Object> chatConfigMap = StringUtils.isNotEmpty(tradeConfig.getChatConfigs()) ? JSONObject.parseObject(tradeConfig.getChatConfigs()) : new HashMap<>();
        Map<String, Object> extendConfigMap = StringUtils.isNotEmpty(tradeConfig.getTradeExtendConfig()) ? JSONObject.parseObject(tradeConfig.getTradeExtendConfig()) : new HashMap<>();
        Map<String, Object> configMap = JSONObject.parseObject(JSON.toJSONString(tradeConfig));
        configMap.remove("chatConfigs");
        configMap.remove("chatConfigMap");
        configMap.remove("tradeExtendConfig");
        configMap.remove("tradeExtendConfigMap");
        configMap.putAll(chatConfigMap);
        configMap.putAll(extendConfigMap);

        Map<String, String> tradeConfigNewMap = new HashMap<>();
        for (String key : configMap.keySet()) {
            Object value = configMap.get(key);
            if (value == null) {
                continue;
            }

            if (value instanceof String) {
                tradeConfigNewMap.put(key, (String) value);
            } else if (value instanceof Boolean) {
                //原来的boolean值配置 true 1 | false 0
                Boolean bool = (Boolean) value;
                tradeConfigNewMap.put(key, bool ? "1" : "0");
            } else if (value instanceof Date) {
                Date date = (Date) value;
                tradeConfigNewMap.put(key, DateUtils.datetime2Str(date));
            } else if (value instanceof Number) {
                tradeConfigNewMap.put(key, value.toString());
            } else {
                //处理JSONObject JSONArray 类型
                tradeConfigNewMap.put(key, JSON.toJSONString(value));
            }
        }

        return tradeConfigNewMap;
    }

    /**
     * 商品档案金额占比发生变化时，待审核订单金额重新分摊
     *
     * @param tradeConfig
     * @return
     */
    public static boolean isOpenWaitAuditSuitSingleRatioShareConfig(TradeConfig tradeConfig) {
        return tradeConfig.getInteger(TradeExtendConfigsEnum.OPEN_WAIT_AUDIT_SUIT_SINGLE_RATIO_SHARE.getKey()) == 1;
    }

    /**
     * 套件/组合装/加工商品按金额占比进行分摊
     *
     * @param tradeConfig
     * @return
     */
    public static boolean isOpenSuitSingleRatioShareConfig(TradeConfig tradeConfig) {
        return tradeConfig.getInteger(TradeExtendConfigsEnum.OPEN_SUIT_SINGLE_RATIO_SHARE.getKey()) == 1;
    }

}

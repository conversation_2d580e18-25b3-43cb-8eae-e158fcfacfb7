package com.raycloud.dmj.domain.constant;

/**
 * 订单仓储事件
 * <AUTHOR>
 *
 */
public class TradeWmsEvents {
    
    /**
     * 审核、自动审核
     */
    public static final String EVENT_AUDIT = "wms.trade.audit";

    /**
     * 反审核、已审核之后的交易关闭(退款成功)、已审核的作废等
     */
    public static final String EVENT_UN_AUDIT = "wms.trade.unaudit";

    /**
     * 新反审核
     */
    public static final String EVENT_UN_AUDIT_NEW = "wms2.trade.unaudit";
    
    /**
     * 发货
     */
    public static final String EVENT_CONSUME = "wms.trade.consume";

    /**
     * 新仓储发货
     */
    public static final String EVENT_CONSUME_NEW = "wms2.trade.consume";

    /**
     * 撤销发货
     */
    public static final String EVENT_CANCEL_CONSIGN = "wms.trade.cancel.consign";

    /**
     * 新撤销发货
     */
    public static final String EVENT_CANCEL_CONSIGN_NEW = "wms2.trade.cancel.consign";


    /**
     * 库存锁定调整
     */
    public static final String EVENT_WMS_TRADE_RECORD_ADJUST = "wms.trade.record.adjust";

    /**
     * 货位锁定调整
     */
    public static final String EVENT_WMS_TRADE_SECTION_RECORD_ADJUST = "wms.trade.section.record.adjust";

    /**
     * 货位锁定调整+库存锁定调整
     */
    public static final String EVENT_WMS_TRADE_SECTION_STOCK_RECORD_ADJUST = "wms.trade.section.stock.record.adjust";


    /**
     * 反审核重试
     */
    public static final String UNAUDIT_RETRY = "unAuditRetry";


    /**
     * 订单撤销发货时间（最近一次审核时间、发货时间）
     */
    public static final String UNAUDIT_CONSIGN_TIME = "unaudit_consign_time";

}

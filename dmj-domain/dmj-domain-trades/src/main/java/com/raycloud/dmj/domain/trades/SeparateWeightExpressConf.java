package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * SeparateWeightExpressConf
 *
 * <AUTHOR>
 * @Date 2019/9/17
 * @Time 15:51
 */
public class SeparateWeightExpressConf implements Serializable {
    private static final long serialVersionUID = 7338725291139771409L;

    private Long expressId;

    private String expressName;

    /**
     * 快递公司ID
     */
    private Long logisticsCompanyId;

    /**
     * 快递公司名称
     */
    private String logisticsCompanyName;

    private String channel;

    public Long getExpressId() {
        return expressId;
    }

    public void setExpressId(Long expressId) {
        this.expressId = expressId;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}

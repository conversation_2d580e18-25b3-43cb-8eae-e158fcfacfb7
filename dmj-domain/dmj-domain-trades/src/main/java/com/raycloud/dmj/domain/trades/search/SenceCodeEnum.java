package com.raycloud.dmj.domain.trades.search;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-05
 */
public enum SenceCodeEnum {


    /**
     * /trade/sellermemoflag/update
     */
    UPD_SELLER_MEMO("批量更新卖家备注"),

    /**
     * /trade/after/batch/print/record
     * /trade/before/batch/print/record
     */
    BATCH_PRINT("批量打印"),

    /**
     * /trade/audit
     */
    AUDIT("订单审核"),

    /**
     * /trade/change/item
     * /trade/platform/save
     */
    CHANGE_ITEM("订单修改商品"),

    /**
     * /trade/save
     */
    SAVE("新建订单"),

    /**
     * /trade/warehouse/change
     */
    UPD_WAREHOUSE("修改仓库"),


    /**
     * /trade/consign/audited
     * /trade/consign
     */
    CONSIGN("订单发货"),

    /**
     * /trade/exception/cancel
     * /trade/except/update
     */
    UPD_EXCEPTION("修改异常"),

    /**
     * /trade/tag/addOrCover
     */
    ADD_COVER_TAG("新增或覆盖选中订单的标签"),


    COMMON("通用场景"),

    ;


    SenceCodeEnum(String desc) {
        this.desc = desc;
    }

    String desc;


    public String getDesc() {
        return desc;
    }
}

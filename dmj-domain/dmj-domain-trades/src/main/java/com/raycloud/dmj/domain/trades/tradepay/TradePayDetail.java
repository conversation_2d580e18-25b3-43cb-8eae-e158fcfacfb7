package com.raycloud.dmj.domain.trades.tradepay;

import com.google.common.collect.Lists;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: chenchaochao
 * @Date: 2020/3/25 3:42 下午
 */
public class TradePayDetail implements Serializable {
    private static final long serialVersionUID = -1056202681305733268L;
    /**
     * 订单实收金额
     */
    private BigDecimal tradePurchaseAmount = new BigDecimal(0.0);
    /**
     * 订单支付信息
     */
    private List<TradePayVO> tradePays = Lists.newArrayList();
    /**
     * 平台支付金额
     */
    private BigDecimal platformPaymentAmount = new BigDecimal(0.0);
    /**
     * 手工支付金额
     */
    private BigDecimal manualPaymentAmount = new BigDecimal(0.0);

    public BigDecimal getTradePurchaseAmount() {
        return tradePurchaseAmount;
    }

    public void setTradePurchaseAmount(BigDecimal tradePurchaseAmount) {
        this.tradePurchaseAmount = tradePurchaseAmount;
    }

    public List<TradePayVO> getTradePays() {
        return tradePays;
    }

    public void setTradePays(List<TradePayVO> tradePays) {
        this.tradePays = tradePays;
    }

    public BigDecimal getPlatformPaymentAmount() {
        return platformPaymentAmount;
    }

    public void setPlatformPaymentAmount(BigDecimal platformPaymentAmount) {
        this.platformPaymentAmount = platformPaymentAmount;
    }

    public BigDecimal getManualPaymentAmount() {
        return manualPaymentAmount;
    }

    public void setManualPaymentAmount(BigDecimal manualPaymentAmount) {
        this.manualPaymentAmount = manualPaymentAmount;
    }
}

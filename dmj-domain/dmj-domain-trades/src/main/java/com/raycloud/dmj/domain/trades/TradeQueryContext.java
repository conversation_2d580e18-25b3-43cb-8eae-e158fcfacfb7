package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-09-10
 */
public class TradeQueryContext {

    /**
     * 使用过的优化方案
     */
    private List<String> optimizations;


    private List<String> suggests;


    /**
     * 包含的平台source，上面加的字段太多了，后面加的平台可以加到这里
     */
    private Set<String> sources;


    /**
     * 最终组建的sql中所包含的userId 按平台分组
     */
    private Map<String,List<Long>> userSourceMap = null;


    /**
     * 原始条件中的店铺
     */
    private Long[] orignUserIds;;

    /**
     * 原始条件中的系统状态
     */
    private String[] orignSysStatus;

    public List<String> getOptimizations() {
        return optimizations;
    }

    public void setOptimizations(List<String> optimizations) {
        this.optimizations = optimizations;
    }

    public void addOptimizations(String optimization) {
        if (this.optimizations == null) {
            optimizations = new ArrayList<>();
        }
        optimizations.add(optimization);
    }

    public void addSuggests(String suggest) {
        if (this.suggests == null) {
            suggests = new ArrayList<>();
        }
        suggests.add(suggest);
    }

    public String getSuggestString(Long took,boolean timeout) {
        if (CollectionUtils.isEmpty(suggests)) {
            return null;
        }
        StringBuilder s = new StringBuilder();
        if (timeout) {
            s.append("当前查询超时");
        }else {
            if (took != null) {
                s.append("当前查询总耗时"+(took/1000)+"秒");
            }else {
                s.append("当前查询总耗时超过了20秒");
            }

        }
        s.append(" 建议通过以下方式调整您的查询条件以提高查询速度：<br/>");
        for (int i = 0; i < suggests.size(); i++) {
            s.append("\t").append(i+1).append(". ").append(suggests.get(i)).append("<br/>");
        }
        return s.toString();
    }

    private AbsLogBuilder logBuilder;

    public AbsLogBuilder getLogBuilder() {
        return logBuilder == null? AbsLogBuilder.getNvlInstance():logBuilder;
    }

    public void setLogBuilder(AbsLogBuilder logBuilder) {
        this.logBuilder = logBuilder;
    }

    private String clientType;

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }


    private TradeQueryParams params;

    public TradeQueryParams getParams() {
        return params;
    }

    public void setParams(TradeQueryParams params) {
        this.params = params;
    }

    public Set<String> getSources() {
        return sources;
    }

    public void setSources(Set<String> sources) {
        this.sources = sources;
    }

    public Map<String, List<Long>> getUserSourceMap() {
        return userSourceMap;
    }

    public void setUserSourceMap(Map<String, List<Long>> userSourceMap) {
        this.userSourceMap = userSourceMap;
    }

    public boolean existAnySource(String ... arr){
        if (sources == null || arr == null) {
            return false;
        }
        for (String source : arr) {
            if (sources.contains(source)) {
                return true;
            }
        }
        return false;
    }

    public void addUserSourceMap(String source,Long userId) {
        if (this.userSourceMap ==null) {
            userSourceMap = new HashMap<>();
        }
        userSourceMap.computeIfAbsent(source,x->new ArrayList<>()).add(userId);
    }

    public boolean hasTradeExtQuery() {
        return (params.getReceiverAddress() != null && !params.getReceiverAddress().trim().isEmpty()) ||
                (params.getReceiverMobile() != null && !params.getReceiverMobile().trim().isEmpty()) ||
                (params.getReceiverName() != null && !params.getReceiverName().trim().isEmpty()) ||
                (params.getBuyerNick() != null && params.getBuyerNick().length > 0) ||
                (params.getLogisticsCode() != null && !params.getReceiverName().trim().isEmpty());
    }

    public boolean hasTradeExtSearch() {
        return (params.getCooperationNo() != null && !params.getCooperationNo().trim().isEmpty()) ||
                StringUtils.isNotBlank(params.getBicUniqueCode()) ||
                StringUtils.isNotBlank(params.getLogisticsCode()) || params.getWrapperDescriptionFlag() != null ||
                (params.getFxgDfMallMaskIds() != null && !params.getFxgDfMallMaskIds().trim().isEmpty())
                || null != params.getSmartItemNumStart() || null != params.getSmartItemNumEnd();
    }

    public boolean hasConsignRecordQuery() {
        return params.getUploadErrorType()!=null && params.getUploadErrorType().length>0;
    }


    /**
     * 是否查询合单后隐藏的订单,目前只支持唯一性查询条件(sid,tid,outSid)搜索
     */
    public boolean isUniqueQuery() {
        return (params.getSid() != null && params.getSid().length > 0) ||
                (params.getTid() != null && params.getTid().length > 0) ||
                (params.getMixKey() != null && !params.getMixKey().trim().isEmpty()) ||
                (params.getOutSids() != null && params.getOutSids().length > 0) ||
                (params.getShortId() != null && params.getShortId().length > 0) ||
                (StringUtils.isNotBlank(params.getReceiverMobile())) ||
                CollectionUtils.isNotEmpty(params.getUniqueCodes());
    }
    public boolean hasItemQueryOrigin() {
        return (params.getItemTitle() != null && !params.getItemTitle().trim().isEmpty()) ||
                (params.getPlatFormTitle() != null && !params.getPlatFormTitle().trim().isEmpty()) ||
                (params.getShortTitle() != null && !params.getShortTitle().trim().isEmpty()) ||
                (params.getOuterId() != null && !params.getOuterId().trim().isEmpty()) ||
                (params.getUniqueCode() != null && !params.getUniqueCode().trim().isEmpty()) ||
                (params.getSkuProp() != null && !params.getSkuProp().trim().isEmpty()) ||
                (params.getPlatSkuProp() != null && !params.getPlatSkuProp().trim().isEmpty()) ||
                (params.getItemRemark() != null && !params.getItemRemark().trim().isEmpty()) ||
                (params.getSkuRemark() != null && !params.getSkuRemark().trim().isEmpty()) ||
                (params.getSkuPropAlias() != null && !params.getSkuPropAlias().trim().isEmpty()) ||
                (params.getCid() != null && params.getCid() > 0) ||
                (params.getCid() != null && params.getCid() == -1) ||
                (params.getIdentCode() != null && !params.getIdentCode().trim().isEmpty()) ||
                (params.getSysOuterIds() != null && params.getSysOuterIds().length > 0) ||
                (params.getSysItemIds() != null && params.getSysItemIds().length > 0) ||
                (params.getSysSkuIds() != null && params.getSysSkuIds().length > 0) ||
                (params.getOuterIdAndSysItemIds() != null && params.getOuterIdAndSysItemIds().length > 0) ||
                (params.getOuterIdAndSysSkuIds() != null && params.getOuterIdAndSysSkuIds().length > 0) ||
                (ArrayUtils.isNotEmpty(params.getItemPops()) && params.getItemPops().length > 0)||
                (ArrayUtils.isNotEmpty(params.getNumIid()) && params.getNumIid().length > 0)||
                (ArrayUtils.isNotEmpty(params.getOuterIid()) && params.getOuterIid().length > 0)||
                Objects.equals(1,params.getItemContainNonConsign()) ||
                (ArrayUtils.isNotEmpty(params.getSkuId()) && params.getSkuId().length > 0);
    }

    public boolean hasItemQuery() {
        return isHasUseJoinOrder() && ((params.getItemTitle() != null && !params.getItemTitle().trim().isEmpty()) ||
                (params.getPlatFormTitle() != null && !params.getPlatFormTitle().trim().isEmpty()) ||
                (params.getShortTitle() != null && !params.getShortTitle().trim().isEmpty()) ||
                (params.getOuterId() != null && !params.getOuterId().trim().isEmpty()) ||
                (params.getUniqueCode() != null && !params.getUniqueCode().trim().isEmpty()) ||
                (params.getSkuProp() != null && !params.getSkuProp().trim().isEmpty()) ||
                (params.getPlatSkuProp() != null && !params.getPlatSkuProp().trim().isEmpty()) ||
                (params.getItemRemark() != null && !params.getItemRemark().trim().isEmpty()) ||
                (params.getSkuRemark() != null && !params.getSkuRemark().trim().isEmpty()) ||
                (params.getSkuPropAlias() != null && !params.getSkuPropAlias().trim().isEmpty()) ||
//                (contains(STATUS_EXCEP_ITEM_CHANGED, params.getExceptionStatus())) ||
                (params.getCid() != null && params.getCid() > 0) ||
                (params.getCid() != null && params.getCid() == -1) ||
                (params.getIdentCode() != null && !params.getIdentCode().trim().isEmpty()) ||
                (params.getSysOuterIds() != null && params.getSysOuterIds().length > 0) ||
                (params.getSysItemIds() != null && params.getSysItemIds().length > 0) ||
                (params.getSysSkuIds() != null && params.getSysSkuIds().length > 0) ||
                (params.getOuterIdAndSysItemIds() != null && params.getOuterIdAndSysItemIds().length > 0) ||
                (params.getOuterIdAndSysSkuIds() != null && params.getOuterIdAndSysSkuIds().length > 0) ||
                (ArrayUtils.isNotEmpty(params.getItemPops()) && params.getItemPops().length > 0 && !(ArrayUtils.contains(params.getItemPops(), Order.TypeOfGiftOrder) && ArrayUtils.contains(params.getItemPops(), Order.TypeOfNoGiftOrder)))||
                (ArrayUtils.isNotEmpty(params.getNumIid()) && params.getNumIid().length > 0)||
                (ArrayUtils.isNotEmpty(params.getOuterIid()) && params.getOuterIid().length > 0)||
                Objects.equals(1,params.getItemContainNonConsign()) ||
                (ArrayUtils.isNotEmpty(params.getSkuId()) && params.getSkuId().length > 0) ||
                (params.getSkuOuterId() != null && params.getSkuOuterId().length() > 0) ||
                (params.getOriginPlatformOuterId() != null && params.getOriginPlatformOuterId().length() > 0));
    }

    /**
     * 开启过滤交易关闭的查询条件
     * @return
     */
    public boolean hasExcludeClosedTradeQuery() {
        //只有前端发起的请求，才会排除部分商品关闭的订单
        if (!params.isAllowExcludeClosedTrade()){
            return false;
        }
        //queryId的过滤, 订单查询、订单处理、订单管理、作废订单、订单打印、订单打印v2这些页面，才允许该操作
        if (null == params.getQueryId() || !(params.getQueryId() == 8 || params.getQueryId() == 62 || params.getQueryId() == 20
                || params.getQueryId() == 21 || params.getQueryId() == 22 || params.getQueryId() == 23 || params.getQueryId() == 60
                || params.getQueryId() == 24 || params.getQueryId() == 25 || params.getQueryId() == 26 || params.getQueryId() == 27 || params.getQueryId() == 28 || params.getQueryId() == 77)){
            return false;
        }
        /**
         * 商品关键字查询：主商家编码、系统规格、商品名称、商品简称、商品备注、商品编码、规格备注、规格别名、平台规格、平台名称、平台商品Id、平台SkuId
         * 商品信息查询：包含商家编码、排除商家编码
         * 区间筛选：商品种类、商品数量、商品净重、包裹重量、订单缺货数、订单金额
         */
        return (params.getMainOuterId() != null && !params.getMainOuterId().trim().isEmpty()) ||
                (params.getSkuProp() != null && !params.getSkuProp().trim().isEmpty()) ||
                (params.getItemTitle() != null && !params.getItemTitle().trim().isEmpty()) ||
                (params.getShortTitle() != null && !params.getShortTitle().trim().isEmpty()) ||
                (params.getItemRemark() != null && !params.getItemRemark().trim().isEmpty()) ||
                (params.getOuterId() != null && !params.getOuterId().trim().isEmpty()) ||
                (params.getSkuRemark() != null && !params.getSkuRemark().trim().isEmpty()) ||
                (params.getSkuPropAlias() != null && !params.getSkuPropAlias().trim().isEmpty()) ||
                (params.getPlatSkuProp() != null && !params.getPlatSkuProp().trim().isEmpty()) ||
                (params.getPlatFormTitle() != null && !params.getPlatFormTitle().trim().isEmpty()) ||
                ArrayUtils.isNotEmpty(params.getNumIid()) || ArrayUtils.isNotEmpty(params.getSkuId()) ||
                ArrayUtils.isNotEmpty(params.getOuterIid()) ||
                hasItemQuery4TimeProcess() || null != params.getItemKindStart() || null != params.getItemKindEnd() ||
                null != params.getItemCountStart() || null != params.getItemCountEnd() || null != params.getNetWeightStart() || null != params.getNetWeightEnd() ||
                null != params.getWeightStart() || null != params.getWeightEnd() || null != params.getInsufficientNumStart() || null != params.getInsufficientNumEnd() ||
                null != params.getPaymentLowerLimit() || null != params.getPaymentUpperLimit();
    }

    public boolean hasOrderCaculateQuery() {
        return (params.getDiscountFeeUpperLimit() != null || params.getDiscountFeeLowerLimit() != null);
    }

    public boolean needMergeSysStatus() {
        //以下情况使用前端传递的系统状态，不用模板系统状态覆盖
        // 1. 非订单管理页面
        // 2. 订单管理页面
        // 2.1. 根据sid,tid,outSid查询
        // 2.2. 查询已发货、已完成、已关闭订单
        // 2.3. 根据卖家昵称查询
        // 2.4. 根据发货时间查询
        return params.getQueryId() == null || params.getQueryId() - 62 != 0 ||
                (!isUniqueQuery() && (params.getBuyerNick() == null || params.getBuyerNick().length == 0) &&
                        !("consign_time".equals(params.getTimeType())||"end_time".equals(params.getTimeType())) &&
                        !Strings.contains(Trade.SYS_STATUS_SELLER_SEND_GOODS, params.getSysStatus()) &&
                        !Strings.contains(Trade.SYS_STATUS_FINISHED, params.getSysStatus()) &&
                        !Strings.contains(Trade.SYS_STATUS_CLOSED, params.getSysStatus()));
    }

    public boolean hasItemQuery4TimeProcess() {
        return  ArrayUtils.isNotEmpty(params.getOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getOuterIdAndSysSkuIds())||
                ArrayUtils.isNotEmpty(params.getOnlyOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getOnlyOuterIdAndSysSkuIds())||
                ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysSkuIds())
                ;
    }

    public boolean hasItemWholeQuery4TimeProcess() {
        return (params.getItemTitle() != null && !params.getItemTitle().trim().isEmpty()) ||
                (params.getOuterId() != null && !params.getOuterId().trim().isEmpty()) ||
                (params.getItemRemark() != null && !params.getItemRemark().trim().isEmpty()) ||
                (params.getSkuRemark() != null && !params.getSkuRemark().trim().isEmpty()) ||
                (params.getSkuPropAlias() != null && !params.getSkuPropAlias().trim().isEmpty()) ||
                (params.getPlatSkuProp() != null && !params.getPlatSkuProp().trim().isEmpty()) ||
                (params.getPlatFormTitle() != null && !params.getPlatFormTitle().trim().isEmpty()) ||
                (params.getShortTitle() != null && !params.getShortTitle().trim().isEmpty()) ||
                (params.getSkuProp() != null && !params.getSkuProp().trim().isEmpty()) ||
                (params.getMainOuterId() != null && !params.getMainOuterId().trim().isEmpty()) ||
                ArrayUtils.isNotEmpty(params.getNumIid())||
                ArrayUtils.isNotEmpty(params.getOuterIid())||
                ArrayUtils.isNotEmpty(params.getSkuId())||
                ArrayUtils.isNotEmpty(params.getOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getOuterIdAndSysSkuIds())||
                ArrayUtils.isNotEmpty(params.getOnlyOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getOnlyOuterIdAndSysSkuIds())||
                ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysItemIds())||
                ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysSkuIds())
                ;
    }

    public boolean hasSysStatusOrder() {
        return  "sysStatus".equals(params.getSort().getField());
    }
    public boolean hasPaymentOrder() {
        return  "payment".equals(params.getSort().getField());
    }


    private boolean hasUseJoinOrder = true;

    public boolean isHasUseJoinOrder() {
        return hasUseJoinOrder;
    }

    public void setHasUseJoinOrder(boolean hasUseJoinOrder) {
        this.hasUseJoinOrder = hasUseJoinOrder;
    }

    public boolean isHaveCancel(){
        return isUniqueQuery() && params.getQueryId() != null && (params.getQueryId() - 62 == 0);
    }

    private boolean isLeftJoinOrderExt = false;

    public boolean isLeftJoinOrderExt() {
        return isLeftJoinOrderExt;
    }

    public void setLeftJoinOrderExt(boolean leftJoinOrderExt) {
        isLeftJoinOrderExt = leftJoinOrderExt;
    }

    /**
     * 是否开启分销商权限鉴权
     */
    private boolean openDistributorAuth = false;

    /**
     * 是否开启业务员权限鉴权
     */
    private boolean openSalesmanAuth = false;

    public boolean isOpenDistributorAuth() {
        return openDistributorAuth;
    }

    public void setOpenDistributorAuth(boolean openDistributorAuth) {
        this.openDistributorAuth = openDistributorAuth;
    }

    public boolean isOpenSalesmanAuth() {
        return openSalesmanAuth;
    }

    public void setOpenSalesmanAuth(boolean openSalesmanAuth) {
        this.openSalesmanAuth = openSalesmanAuth;
    }


    /**
     * 运单号关联的一单多包系统订单号
     */
    private Set<Long> outSidsRelSidSet;

    public Set<Long> getOutSidsRelSidSet() {
        return outSidsRelSidSet;
    }
    public void setOutSidsRelSidSet(Set<Long> outSidsRelSidSet) {
        this.outSidsRelSidSet = outSidsRelSidSet;
    }


    /**
     * 导出任务主键
     */
    private Long taskId;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long[] getOrignUserIds() {
        return orignUserIds;
    }

    public void setOrignUserIds(Long[] orignUserIds) {
        this.orignUserIds = orignUserIds;
    }

    public boolean hasOrignUserIds(){
        return orignUserIds !=null && orignUserIds.length > 0;
    }

    public String[] getOrignSysStatus() {
        return orignSysStatus;
    }

    public void setOrignSysStatus(String[] orignSysStatus) {
        this.orignSysStatus = orignSysStatus;
    }

    public boolean hasOrignSysStatus(){
        return orignSysStatus !=null && orignSysStatus.length > 0;
    }
}

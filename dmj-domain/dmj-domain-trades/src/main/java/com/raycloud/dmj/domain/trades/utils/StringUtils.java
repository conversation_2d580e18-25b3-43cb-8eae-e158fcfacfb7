package com.raycloud.dmj.domain.trades.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by CXW on 2016/12/4.
 */
public final class StringUtils {
    private static final Pattern UTF8MB4_PATTERN = Pattern.compile("\\\\u([0-9a-zA-Z]{4})\\\\u([0-9a-zA-Z]{4})");

    public static final boolean contentEquals(String s, String t) {
        String v1 = s == null ? "" : s;
        String v2 = t == null ? "" : t;
        return v1.equals(v2);
    }

    /**
     * 替换utf8mb字符为uxxxxuxxxx格式
     *
     * @param str
     * @return
     */
    public static String replaceUtf8mb4(String str) {
        if (null == str) {
            return str;
        }
        final int LAST_BMP = 0xFFFF;
        StringBuilder sb = new StringBuilder(str.length());
        for (int i = 0; i < str.length(); i++) {
            int codePoint = str.codePointAt(i);
            if (codePoint < LAST_BMP) {
                sb.appendCodePoint(codePoint);
            } else {
                if (i < str.length() - 1) {
                    sb.append("\\u").append(Integer.toHexString(str.charAt(i)))
                            .append("\\u").append(Integer.toHexString(str.charAt(i + 1)));
                    i++;
                } else {
                    // 逻辑到这里说明存在尾部单字符utf8mb，直接去除
                }
            }
        }
        return sb.toString();
    }

    public static boolean isAllOriEmoji(String str) {
        try {
            String emojiInfo = replaceUtf8mb4(str);
            return isAllEmoji(emojiInfo);
        } catch (Exception e) {
            return false;
        }

    }

    public static String regainUtf8mb4(String str) {
        if (null == str) {
            return str;
        }
        Matcher matcher = UTF8MB4_PATTERN.matcher(str);
        StringBuffer sb = new StringBuffer(str.length());
        while (matcher.find()) {
            matcher.appendReplacement(sb, new String(new char[]{(char) Integer.parseInt(matcher.group(1), 16),
                    (char) Integer.parseInt(matcher.group(2), 16)}));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String nvl(String str) {
        if (str == null) return "";
        return str;
    }

    public static Long nvl(Long val) {
        if (val == null) return 0L;
        return val;
    }

    public static Integer nvl(Integer val) {
        if (val == null) return 0;
        return val;
    }

    public static Double nvl(Double val) {
        if (val == null) return 0D;
        return val;
    }

    public static boolean isAllEmoji(String info) {
        try {
            String remainStr = info.replace(" ", "");
            if (remainStr.startsWith("\\u")) {
                while (remainStr.length() > 0) {
                    int lastIndex = remainStr.lastIndexOf("\\u");
                    if (lastIndex == remainStr.length() - 6) {
                        remainStr = remainStr.substring(0, lastIndex);
                    } else {
                        return false;
                    }
                }
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            return false;
        }
    }

    private static Pattern linePattern = Pattern.compile("_(\\w)");

    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(replaceUtf8mb4("😊"));

    }

    /**
     * 把原始字符串分割成指定长度的字符串列表
     *
     * @param inputString
     *            原始字符串
     * @param length
     *            指定长度
     * @return
     */
    public static List<String> getStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size += 1;
        }
        return getStrList(inputString, length, size);
    }

    /**
     * 把原始字符串分割成指定长度的字符串列表
     *
     * @param inputString
     *            原始字符串
     * @param length
     *            指定长度
     * @param size
     *            指定列表大小
     * @return
     */
    public static List<String> getStrList(String inputString, int length,
                                          int size) {
        List<String> list = new ArrayList<String>();
        for (int index = 0; index < size; index++) {
            String childStr = substring(inputString, index * length,
                    (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    /**
     * 分割字符串，如果开始位置大于字符串长度，返回空
     *
     * @param str
     *            原始字符串
     * @param f
     *            开始位置
     * @param t
     *            结束位置
     * @return
     */
    public static String substring(String str, int f, int t) {
        if (f > str.length())
            return null;
        if (t > str.length()) {
            return str.substring(f, str.length());
        } else {
            return str.substring(f, t);
        }
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.vo.PrinterConfigVO;
import com.raycloud.erp.db.model.Model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class PrintAutomationConfig extends Model implements Serializable {
    private static final long serialVersionUID = 1761407842886332260L;

    private Long id;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 员工id
     */
    private Long staffId;
    /**
     * 页面id
     */
    private Integer pageId;
    /**
     * 打印机配置
     */
    private String printerConfig;
    /**
     * 自动打印配置
     */
    private String autoPrintConfig;

    private Date created;

    private Date modified;

    private List<PrinterConfigVO> printers;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Integer getPageId() {
        return pageId;
    }

    public void setPageId(Integer pageId) {
        this.pageId = pageId;
    }

    public String getPrinterConfig() {
        return printerConfig;
    }

    public void setPrinterConfig(String printerConfig) {
        this.printerConfig = printerConfig;
    }

    public String getAutoPrintConfig() {
        return autoPrintConfig;
    }

    public void setAutoPrintConfig(String autoPrintConfig) {
        this.autoPrintConfig = autoPrintConfig;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public List<PrinterConfigVO> getPrinters() {
        return printers;
    }

    public void setPrinters(List<PrinterConfigVO> printers) {
        this.printers = printers;
    }

}

package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 发货统计，统计发货中和发货失败的
 * <AUTHOR>
 *
 */
public class ConsignRecordStatistics implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4297697741168473689L;

	private Integer isError;
	
	private Integer count;

	public Integer getIsError() {
		return isError;
	}

	public void setIsError(Integer isError) {
		this.isError = isError;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * 订单上传记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Table(name = "trade_upload_record", routerKey = "tradeDbNo")
public class TradeUploadRecord extends Model {

    private static final long serialVersionUID = -2805030325724730140L;

    /** id */
    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id ;

    /** 系统订单号 */
    @Column(name = "sid", type = Types.BIGINT)
    private Long sid ;

    /** 平台订单号 */
    @Column(name = "tid", type = Types.VARCHAR)
    private String tid ;

    /** 平台子单号 */
    @Column(name = "oids", type = Types.VARCHAR)
    private String oids ;

    /** 公司ID */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId ;

    /** 运单号 */
    @Column(name = "out_sid", type = Types.VARCHAR)
    private String outSid ;

    /** 物流模板ID */
    @Column(name = "template_id", type = Types.BIGINT)
    private Long templateId ;

    /** 物流模板类型 */
    @Column(name = "template_type", type = Types.TINYINT)
    private Integer templateType ;

    /**
     * 发货类型
     *  com.raycloud.dmj.domain.consign.SendType
     */
    @Column(name = "send_type", type = Types.VARCHAR)
    private String sendType;

    /** 上传类型 */
    @Column(name = "upload_type", type = Types.TINYINT)
    private Integer uploadType ;

    /** 上传状态 (1-成功、0-失败) */
    @Column(name = "upload_status", type = Types.TINYINT)
    private Integer uploadStatus ;

    /** 上传时间 */
    @Column(name = "upload_time", type = Types.TIMESTAMP)
    private Date uploadTime ;

    /** 错误信息 */
    @Column(name = "msg", type = Types.VARCHAR)
    private String msg;

    /** 链路ID */
    @Column(name = "clue_id", type = Types.VARCHAR)
    private String clueId ;

    /** 扩展字段 */
    @Column(name = "ext", type = Types.VARCHAR)
    private String ext ;


    public Long getId(){
        return this.id;
    }

    public void setId(Long id){
        this.id=id;
    }

    public Long getSid(){
        return this.sid;
    }

    public void setSid(Long sid){
        this.sid=sid;
    }

    public String getTid(){
        return this.tid;
    }

    public void setTid(String tid){
        this.tid=tid;
    }

    public String getOids(){
        return this.oids;
    }

    public void setOids(String oids){
        this.oids=oids;
    }

    public Long getCompanyId(){
        return this.companyId;
    }

    public void setCompanyId(Long companyId){
        this.companyId=companyId;
    }

    public String getOutSid(){
        return this.outSid;
    }

    public void setOutSid(String outSid){
        this.outSid=outSid;
    }

    public Long getTemplateId(){
        return this.templateId;
    }

    public void setTemplateId(Long templateId){
        this.templateId=templateId;
    }

    public Integer getTemplateType(){
        return this.templateType;
    }

    public void setTemplateType(Integer templateType){
        this.templateType=templateType;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public Integer getUploadType(){
        return this.uploadType;
    }

    public void setUploadType(Integer uploadType){
        this.uploadType=uploadType;
    }

    public Integer getUploadStatus(){
        return this.uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus){
        this.uploadStatus=uploadStatus;
    }

    public Date getUploadTime(){
        return this.uploadTime;
    }

    public void setUploadTime(Date uploadTime){
        this.uploadTime=uploadTime;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getClueId(){
        return this.clueId;
    }

    public void setClueId(String clueId){
        this.clueId=clueId;
    }

    public String getExt(){
        return this.ext;
    }

    public void setExt(String ext){
        this.ext=ext;
    }
}

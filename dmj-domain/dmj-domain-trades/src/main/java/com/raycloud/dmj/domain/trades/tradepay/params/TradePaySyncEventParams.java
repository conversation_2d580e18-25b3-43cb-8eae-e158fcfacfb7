package com.raycloud.dmj.domain.trades.tradepay.params;

import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.tradepay.TradePayVO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenchaochao
 * @Date: 2020/3/23 7:44 下午
 */

public class TradePaySyncEventParams implements Serializable {

    private static final long serialVersionUID = 6177133061580811993L;
    /**
     * 事物编号
     */
    private Long txnId;

    private List<TradePayVO> tradePays;
    /**
     * 来源
     */
    private SourceEnum source;

    /**
     * 操作类型
     */
    private HandleTypeEnum handleTypeEnum;

    /**
     */
    private Map<Long, List<TbTrade>> tbTradeMaps;


    public static enum SourceEnum {
        SOURCE_TB("TB", "淘宝"),
        SOURCE_1688("1688", "1688");

        private String value;

        private String name;

        SourceEnum(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        public static SourceEnum parseOfValue(String value) {
            for (SourceEnum source : SourceEnum.values()) {
                if (source.value.equals(value)) {
                    return source;
                }
            }
            return null;
        }
    }

    public static enum HandleTypeEnum {
        SOURCE_SYNC("sync", "订单同步操作"),
        SOURCE_SPLIT("split", "拆单操作");

        private String value;

        private String name;

        HandleTypeEnum(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        public static HandleTypeEnum parseOfValue(String value) {
            for (HandleTypeEnum source : HandleTypeEnum.values()) {
                if (source.value.equals(value)) {
                    return source;
                }
            }
            return null;
        }
    }


    public Long getTxnId() {
        return txnId;
    }

    public void setTxnId(Long txnId) {
        this.txnId = txnId;
    }

    public SourceEnum getSource() {
        return source;
    }

    public void setSource(SourceEnum source) {
        this.source = source;
    }

    public HandleTypeEnum getHandleTypeEnum() {
        return handleTypeEnum;
    }

    public void setHandleTypeEnum(HandleTypeEnum handleTypeEnum) {
        this.handleTypeEnum = handleTypeEnum;
    }

    public List<TradePayVO> getTradePays() {
        return tradePays;
    }

    public void setTradePays(List<TradePayVO> tradePays) {
        this.tradePays = tradePays;
    }

    public Map<Long, List<TbTrade>> getTbTradeMaps() {
        return tbTradeMaps;
    }

    public void setTbTradeMaps(Map<Long, List<TbTrade>> tbTradeMaps) {
        this.tbTradeMaps = tbTradeMaps;
    }
}

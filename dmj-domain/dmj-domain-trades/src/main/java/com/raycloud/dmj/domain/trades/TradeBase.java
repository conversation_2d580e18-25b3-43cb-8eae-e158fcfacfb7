package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Description 订单模型基类
 * @Date 2022/2/21 11:37
 * @Created 杨恒
 */
@Setter
@Getter
public abstract class TradeBase extends Model {

    private static final long serialVersionUID = -4095423603681921833L;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    private Integer enableStatus;

    private Integer oldEnableStatus;

    /**
     * 是否有数据更新
     */
    private boolean update;

}

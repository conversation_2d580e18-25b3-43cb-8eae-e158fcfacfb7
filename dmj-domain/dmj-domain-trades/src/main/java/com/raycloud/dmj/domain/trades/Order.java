package com.raycloud.dmj.domain.trades;

import com.google.common.collect.Sets;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.enums.OrderOpeartEnum;
import com.raycloud.dmj.domain.trade.orderitemtag.OrderItemTag;
import com.raycloud.dmj.domain.trade.type.OrderType;
import com.raycloud.dmj.domain.trades.payment.link.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.vo.OrderUnitVO;
import com.raycloud.dmj.domain.trades.vo.WaveAssembleInfo;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.erp.db.model.Column;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.*;

/**
 * ERP的子订单领域，所有平台的业务领域都应该要继承这个类
 *
 * <AUTHOR>
 */
public class Order extends OrderAggregation {

    private static final long serialVersionUID = -5237783229703216767L;

    public static final String REFUND_WAIT_SELLER_AGREE = "WAIT_SELLER_AGREE";//待卖家同意退款

    public static final String REFUND_WAIT_BUYER_RETURN_GOODS = "WAIT_BUYER_RETURN_GOODS";//待买家退货

    public static final String REFUND_WAIT_SELLER_CONFIRM_GOODS = "WAIT_SELLER_CONFIRM_GOODS";//待卖家确认收货

    public static final String REFUND_SELLER_REFUSE_BUYER = "SELLER_REFUSE_BUYER";//卖家拒绝退款

    public static final String REFUND_CLOSED = "CLOSED";//退款关闭

    public static final String REFUND_SUCCESS = "SUCCESS";//退款成功

    public static final String NO_REFUND = "NO_REFUND";//未退款

    /**
     * 这个是系统的一个退款状态，当平台订单的状态为待发货时，买家申请了退款，卖家是不能直接拒绝退款，只能强制发货，所以这个时候需要把退款状态设置为这个状态
     */
    public static final String REFUND_SELLER_CONTINUE_CONSIGN = "SELLER_CONTINUE_CONSIGN";

    public final static int TypeOfNormal = 0;
    public final static int TypeOfGiftOrder = 1;
    public final static int TypeOfCombineOrder = 2;
    public final static int TypeOfGroupOrder = 3;//组合装商品
    public final static int TypeOfProcessOrder = 4;//加工商品

    public final static int TypeOfNoGiftOrder = 6;//不含赠品

    //商品停用
    public final static int ITEM_SHUTOFF = 1 << 4;
    public static final Integer SHOP_PRESELL = 4;
    //唯一码下架
    public final static int ITEM_UNIQUE_CODE_OFFSHELF = 1 << 5;
    //商品类型转换异常
    public final static int ITEM_PROCESS = 1 << 6;
    //供销改商品
    public final static int GX_ITEM_UPDATE = 1 << 24;

    public final static String PART_REFUND_END_STR = "@part_refund@";

    /**
     * 订单所属店铺用户ID
     */
    private Long userId;

    /**
     * 原先属于哪个订单
     */
    private Long sourceSid;

    /**
     * 订单来源 tb,tm,jd,sys
     */
    @Column(name = "source", type = Types.VARCHAR)
    private String source;

    /**
     * 是否为预售订单 0:正常订单，1：是预售；2预售转正常；3：系统预售
     */
    @Column(name = "is_presell", type = Types.TINYINT)
    private Integer isPresell;
    /**
     * 商品简称
     */
    @Column(name = "short_title", type = Types.VARCHAR)
    private String shortTitle;
    /**
     * 0 已删除 1正常
     */
    @Column(name = "enable_status", type = Types.TINYINT)
    private Integer enableStatus = 1;
    /**
     * 订单系统状态
     */
    @Column(name = "sys_status", type = Types.VARCHAR)
    private String sysStatus;
    /**
     * 旧的系统状态，不做持久化，一般用于订单同步更新时的比较
     */
    private String oldSysStatus;

    /**
     * 是否取消库存缺货异常（INSUFFICIENT,EMPTY,EXCEP）：0 否，1 是
     */
    @Column(name = "insufficient_canceled", type = Types.TINYINT)
    private Integer insufficientCanceled;
    /**
     * 商品对应关系是否改动: 0 否，1 是
     */
    @Column(name = "relation_changed", type = Types.TINYINT)
    private Integer relationChanged;
    /**
     * 当前子订单的库存状态,如库存正常(NORMAL),库存不足(INSUFFICIENT)
     */
    @Column(name = "stock_status", type = Types.VARCHAR)
    private String stockStatus;

    /**
     * 之前子订单的库存状态,如库存正常(NORMAL),库存不足(INSUFFICIENT)
     */
    private String oldStockStatus;

    private Date oldPtConsignTime;

    public Date getOldPtConsignTime() {
        return oldPtConsignTime;
    }

    public void setOldPtConsignTime(Date oldPtConsignTime) {
        this.oldPtConsignTime = oldPtConsignTime;
    }

    /**
     * 分配的库存数量
     */
    @Column(name = "stock_num", type = Types.INTEGER)
    private Integer stockNum;
    /**
     * 在作库存操作之前的库存分配数
     */
    private Integer oldStockNum;

    /**
     * 缺货数量=num-stockNum（不需要持久化）
     */
    private Integer diffStockNum;

    /**
     * 匹配到的系统商品ID
     */
    @Column(name = "item_sys_id", type = Types.BIGINT)
    private Long itemSysId;
    /**
     * 匹配到的系统规格ID
     */
    @Column(name = "sku_sys_id", type = Types.BIGINT)
    private Long skuSysId;
    /**
     * 修改套件明细前系统商品ID
     */
    private Long oldItemSysId;
    /**
     * 修改套件明细前系统规格ID
     */
    private Long oldSkuSysId;
    /**
     * 修改套件明细前商家编码
     */
    private String oldSysOuterId;
    /**
     * outer_iid
     */
    @Column(name = "outer_iid", type = Types.VARCHAR)
    private String outerIid;
    /**
     * 商家编码，不做持久化，仅仅只是为了系统订单添加商品使用
     */
    private String outerId;
    /**
     * outer_sku_id
     */
    @Column(name = "outer_sku_id", type = Types.VARCHAR)
    private String outerSkuId;

    /**
     * 系统主商家编码，暂不做持久化
     */
    private String sysItemOuterId;

    /**
     * 商品标题
     */
    @Column(name = "title", type = Types.VARCHAR)
    private String title;

    /**
     * 系统商品数量单位
     */
    @Column(name = "unit", type = Types.VARCHAR)
    private String unit;
    /**
     * 系统规格计量单位
     */
    @Column(name = "sku_unit", type = Types.VARCHAR)
    private String skuUnit;
    /**
     * 子订单商品数量
     */
    @Column(name = "num", type = Types.INTEGER)
    private Integer num;
    /**
     * 更新之前的商品数量，不做持久化，用于订单调整商品数量的业务
     */
    private Integer oldNum;
    /**
     * 订单数量，不做持久化
     */
    private Integer tradeNum;

    /**
     * 商品详情：不做持久化
     */
    private List<TradeHotItemDetail> itemDetails;
    /**
     * 规格属性名
     */
    @Column(name = "sku_properties_name", type = Types.VARCHAR)
    private String skuPropertiesName;
    /**
     * 子订单退款状态
     */
    @Column(name = "refund_status", type = Types.VARCHAR)
    private String refundStatus;
    private String oldRefundStatus;
    /**
     * 下单时间
     */
    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;
    /**
     * 子订单平台的修改时间
     */
    @Column(name = "modified", type = Types.TIMESTAMP)
    private Date modified;
    /**
     * 平台退款编号
     */
    @Column(name = "refund_id", type = Types.VARCHAR)
    private String refundId;
    /**
     * 子订单平台状态
     */
    @Column(name = "status", type = Types.VARCHAR)
    private String status;
    /**
     * 子订单旧的平台状态，不做持久化，一般用于订单同步更新时的判断比较
     */
    private String oldStatus;

    /**
     * 子订单中单个商品净重
     */
    @Column(name = "net_weight", type = Types.DOUBLE)
    private Double netWeight;
    /**
     * 单个商品体积
     */
    @Column(name = "volume", type = Types.DOUBLE)
    private Double volume;
    /**
     * 平台商品图片链接
     */
    @Column(name = "pic_path", type = Types.VARCHAR)
    private String picPath;
    /**
     * 系统商品图片链接
     */
    @Column(name = "sys_pic_path", type = Types.VARCHAR)
    private String sysPicPath;
    /**
     * 子订单的交易结束时间
     */
    @Column(name = "end_time", type = Types.TIMESTAMP)
    private Date endTime;
    /**
     * 子订单发货时间
     */
    @Column(name = "consign_time", type = Types.TIMESTAMP)
    private Date consignTime;

    /**
     * 平台发货时间
     */
    @Column(name = "pt_consign_time", type = Types.TIMESTAMP)
    private Date ptConsignTime;

    /**
     * 是否在系统发货 1 是，0 否
     */
    @Column(name = "sys_consigned", type = Types.TINYINT)
    private Integer sysConsigned;

    /**
     * 包裹，抖音特有
     */
    private Parcel parcel;

    /**
     * 订单店铺的平台ID
     */
    @Column(name = "taobao_id", type = Types.BIGINT)
    private Long taobaoId;

    /**
     * 付款时间
     */
    @Column(name = "pay_time", type = Types.TIMESTAMP)
    private Date payTime;
    /**
     * 数据更新时间
     */
    @Column(name = "upd_time", type = Types.TIMESTAMP)
    private Date updTime;

    /**
     * 平台商品数字编号
     */
    @Column(name = "num_iid", type = Types.VARCHAR)
    private String numIid;
    /**
     * 平台规格编号
     */
    @Column(name = "sku_id", type = Types.VARCHAR)
    private String skuId;
    /**
     * 是否为拍下减库存还是付款减库存，1表示付款减库存，0表示拍下减库存, 目前只用于淘宝
     */
    @Column(name = "sub_stock", type = Types.TINYINT)
    private Integer subStock;
    /**
     * 系统商家编码，如果是商品则为商品的商家编码，如果为SKU则为SKU的商家编码
     */
    @Column(name = "sys_outer_id", type = Types.VARCHAR)
    private String sysOuterId;

    /**
     * 系统SKU的规格属性名称
     */
    @Column(name = "sys_sku_properties_name", type = Types.VARCHAR)
    private String sysSkuPropertiesName;
    /**
     * 系统SKU规格属性别名
     */
    @Column(name = "sys_sku_properties_alias", type = Types.VARCHAR)
    private String sysSkuPropertiesAlias;
    /**
     * 系统规商品备注
     */
    @Column(name = "sys_item_remark", type = Types.VARCHAR)
    private String sysItemRemark;
    /**
     * 系统规格备注
     */
    @Column(name = "sys_sku_remark", type = Types.VARCHAR)
    private String sysSkuRemark;
    /**
     * 系统商品的标题
     */
    @Column(name = "sys_title", type = Types.VARCHAR)
    private String sysTitle;
    /**
     * 系统商品类目ID,以逗号分隔
     */
    @Column(name = "cids", type = Types.VARCHAR)
    private String cids;

    /**
     * 系统商品类目名
     */
    private String sellerCatName;
    /**
     * 用于订单同步时记录平台商品(规格)是否变更过 0:未变 1：有变
     */
    @Column(name = "item_changed", type = Types.TINYINT)
    private Integer itemChanged;
    /**
     * 标志系统商品是否更换过
     */
    @Column(name = "sys_item_changed", type = Types.TINYINT)
    private Integer sysItemChanged;

    /**
     * 如果是套件单品,则该字段的值为其所属的套件子订单的id,否则为0
     */
    @Column(name = "combine_id", type = Types.BIGINT)
    private Long combineId;

    /**
     * 赠品的order数量
     */
    @Column(name = "gift_num", type = Types.INTEGER)
    private Integer giftNum;
    /**
     * 强制验货数量
     */
    @Column(name = "force_pack_num", type = Types.BIGINT)
    private Integer forcePackNum;

    /**
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     *
     */
    @Column(name = "type", type = Types.INTEGER)
    private Integer type;

    /**
     * 子订单类型
     */
    private Map<String, OrderType> orderTypeMap;

    /**
     * order 商品标签对应关系
     * 对应表 order_item_tag
     * 对应的填充bean TradeOrderItemTagFill
     */
    private List<OrderItemTag> orderItemTags;

    /**
     * 商品标签名称
     * 用于前端展示
     * 这里由系统配置 订单列表中显示商品的商品标签 控制是否填充值
     * 有配置控制 所以走商品dubbo查询标签
     * 对应的填充bean TradeOrderSyncItemTagFill
     */
    private List<String> orderItemTagNames;

    /**
     * 3: 按sku拆分
     * 4：按数量拆分
     */
    private Integer splitType;

    /**
     * <p>
     * 目前使用场景(以后可继续增加):
     * 1. 订单修改时判断该子订单(商品)是否可修改
     * 2. 订单同步时判断该子订单(商品)是否可修改
     * 3. 订单发货时判断该子订单(商品)是否需要消费库存
     * true 可修改, false 不可修改, 默认值为true
     * </p>
     */
    private boolean operable = true;

    /**
     * 覆盖模式下已上传订单不覆盖状态,但平台部分退款成功，得把相应子订单关闭
     */
    private boolean canBeUpdated = true;
    /**
     * 子订单商品库存操作时的仓库ID,要保证与所属trade中的warehouseId一致
     */
    private Long warehouseId;

    /**
     * 换仓库之前的仓库ID,不做持久化
     */
    private Long oldWarehouseId;

    /**
     * 识别码
     */
    @Column(name = "ident_code", type = Types.VARCHAR)
    private String identCode;
    /**
     * 非首次订单同步时,平台子订单所对应的系统中已存在的平台子订单
     */
    private Order origin;

    /**
     * 对应的平台订单
     */
    private Order platformOrder;

    /**
     * 套件商品下的单品集合,只针对套件而言
     */
    private List<Order> suits;
    /**
     * 套件商品下单品的数量总和,只针对套件而言
     */
    private Integer suitItemCount;

    /**
     * 匹配系统商品时设置的标志
     * <p>
     * 0.默认值
     * 1.一个子订单在匹配系统商品时若匹配到了套件,则flag设为1,新产生的所有单品子订单flag也设为1
     * 2.系统换商品时,若换成套件,则当前子订单及新产生的套件子订单flag设为1
     * 3.系统新增或修改订单,添加的商品若是套件,则该子订单及其下的套件子订单的flag设为1
     * 其它: 临时定义
     * </p>
     */
    private int flag;

    /**
     * 使用分销的status覆盖供销的status，默认用sys_status覆盖status
     */
    private boolean coverGxWithFxStatus;

    private List<SplitUploadRecord> splitUploadRecords;

    private SplitUploadRecord insertSplitUploadRecord;

    private Order needUploadOrder;

    private Integer autoUnattainable;

    public boolean isNeedRematchItem() {
        return needRematchItem;
    }

    public boolean isNeedReAppStock() {
        return needReAppStock;
    }

    public void setNeedReAppStock(boolean needReAppStock) {
        this.needReAppStock = needReAppStock;
    }

    public void setNeedRematchItem(boolean needRematchItem) {
        this.needRematchItem = needRematchItem;
    }

    private boolean needRematchItem;

    private boolean needReAppStock;

    /**
     * 是否已经根据商家编码重新计算num
     */
    private Boolean hasNumReset;

    public Boolean getHasNumReset() {
        return hasNumReset;
    }

    public void setHasNumReset(Boolean hasNumReset) {
        this.hasNumReset = hasNumReset;
    }

    public boolean isCoverGxWithFxStatus() {
        return coverGxWithFxStatus;
    }

    public void setCoverGxWithFxStatus(boolean coverGxWithFxStatus) {
        this.coverGxWithFxStatus = coverGxWithFxStatus;
    }

    /**
     * 子订单平台编号（由于某些平台的子订单号为字符类型，需要用此字段保存，后续可将oid修改为String类型）
     */
    @Column(name = "soid", type = Types.VARCHAR)
    private String soid;
    /**
     * 是否虚拟商品，虚拟商品不需要申请库存，发货时也不需要消费库存、归还库存
     */
    @Column(name = "is_virtual", type = Types.TINYINT)
    private Integer isVirtual;

    /**
     * 混合拆分的时候，记录前端传过来的拆分数量，不做持久化
     */
    private Integer splitNum;
    /**
     * 是否作废，未发货的子订单与所属的trade保持一致
     */
    @Column(name = "is_cancel", type = Types.TINYINT)
    private Integer isCancel;

    /**
     * 预计发货时间
     */
    @Column(name = "estimate_con_time", type = Types.TIMESTAMP)
    private Date estimateConTime;

    /**
     * 无需发货 0-否、1-是
     */
    @Column(name = "non_consign", type = Types.TINYINT)
    private Integer nonConsign;

    /**
     * 合单对应的主单，不做持久化
     */
    private Long mergeSid;

    /**
     * 该order属于真实的系统id
     */
    private Long belongSid;

    /**
     * 自定义赠品类型，暂定0 原有逻辑，1手工新增赠品, 2:平台赠品套转单
     */
    private Long customGiftType;

    /**
     * 刷单，取trade中，不持久化
     */
    private Integer scalping;

    /**
     * 当子订单是赠品的时候（giftNum>0），判断赠品是否参与拣选
     * 增加（是否参与验货）属性判断
     * 0：不拣选不验货；1：拣选不验货；2：拣选验货；3：验货不拣选
     */
    private Integer isPick;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 对应的播种信息
     */
    private TradeWaveSortingDetail waveSortingDetail;

    /**
     * 子订单异常信息
     */
    private Set<String> exceptions;

    /**
     * 批量修改商品时保存平台商家编码
     */
    private String platformOuterId;

    /**
     * 新增的order
     */
    private boolean insert;

   private OrderOpeartEnum orderOpeart;

    public String getPlatformOuterId() {
        return platformOuterId;
    }

    public void setPlatformOuterId(String platformOuterId) {
        this.platformOuterId = platformOuterId;
    }


    private String goodsSectionNumStr;

    /**
     * 商品货位编码
     */
    private List<String> goodsSectionCodes;

    /**
     * 是否保持之前的缺货库存状态，在开启后置锁定时候使用
     */
    private boolean keepInsufficientStockStatus = false;
    /**
     * 订单转化类型 0表示正常订单，1表示分销系统
     */
    @Column(name = "convert_type", type = Types.INTEGER)
    private Integer convertType;

    /**
     * 1、订单来源于哪里 分销系统里记为分销商id（companyId）order不一定需要
     * 2、按数量拆分，从哪个子订单中拆分出去的 不持久化到数据库
     */
    private Long sourceId;

    /**
     * 指定的的orderId
     */
    private Long newOrderId;

    public Long getNewOrderId() {
        return newOrderId;
    }

    public void setNewOrderId(Long newOrderId) {
        this.newOrderId = newOrderId;
    }

    /**
     * 从哪个套件拆分出来的
     */
    private Long fromOriginSuitSelfId;

    /**
     * 品牌（不持久化）
     */
    private List<Long> brandIds;
    /**
     * 供应商（不持久化）
     */
    private List<Long> supplierIds;

    private List<Long> sellerCids;

    private String mainOuterId;

    private String FxSoid;

    /**
     * 关联的唯一码，不持久化
     */
    private List<String> uniqueCodes;

    /**
     * 是否使用截取的outerId去匹配
     */
    private Boolean useOuterIdMatch;

    /**
     * 商品的箱规范
     */
    private Integer boxNum;

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public String getOldOuterIid() {
        return oldOuterIid;
    }

    public void setOldOuterIid(String oldOuterIid) {
        this.oldOuterIid = oldOuterIid;
    }

    public String getOldOuterSkuId() {
        return oldOuterSkuId;
    }

    public void setOldOuterSkuId(String oldOuterSkuId) {
        this.oldOuterSkuId = oldOuterSkuId;
    }

    /**
     * 截取以前的outerId
     */

    private String oldOuterIid;

    private String oldOuterSkuId;

    public Boolean getUseOuterIdMatch() {
        return useOuterIdMatch;
    }

    public void setUseOuterIdMatch(Boolean useOuterIdMatch) {
        this.useOuterIdMatch = useOuterIdMatch;
    }

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    @Column(name = "dest_id", type = Types.BIGINT)
    private Long destId;

    /**
     * 订单归属于哪里 分销系统里记为供销商name（不持久化）
     */
    private String destName;

    /**
     * 订单属于soure or dest ,0表示正常订单，1表示source，2表示dest
     */
    @Column(name = "belong_type", type = Types.INTEGER)
    private Integer belongType;

    /**
     * 供销商公司id
     */
    private Long supplierCompanyId;

    /**
     *  sku.outerId/item.outerId
     */
    private String supplierOuterId;

    /**
     * 供销的分销价
     */
    private Double supplierPrice;

    public Double getSupplierPrice() {
        return supplierPrice;
    }

    public void setSupplierPrice(Double supplierPrice) {
        this.supplierPrice = supplierPrice;
    }

    public Long getSupplierCompanyId() {
        return supplierCompanyId;
    }

    public void setSupplierCompanyId(Long supplierCompanyId) {
        this.supplierCompanyId = supplierCompanyId;
    }

    public String getSupplierOuterId() {
        return supplierOuterId;
    }

    public void setSupplierOuterId(String supplierOuterId) {
        this.supplierOuterId = supplierOuterId;
    }

    /**
     * 是否是取消分销属性触发的订单同步，如果是1，则不需要匹配供应商，因为是取消分销属性
     */
    private Integer isCancelDistributorAttribute;


    public Integer getIsCancelDistributorAttribute() {
        return isCancelDistributorAttribute;
    }

    public void setIsCancelDistributorAttribute(Integer isCancelDistributorAttribute) {
        this.isCancelDistributorAttribute = isCancelDistributorAttribute;
    }

    public Integer getConvertType() {
        return convertType;
    }

    public void setConvertType(Integer convertType) {
        this.convertType = convertType;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getFromOriginSuitSelfId() {
        return fromOriginSuitSelfId;
    }

    public void setFromOriginSuitSelfId(Long fromOriginSuitSelfId) {
        this.fromOriginSuitSelfId = fromOriginSuitSelfId;
    }

    public Integer getBelongType() {
        return belongType;
    }

    public void setBelongType(Integer belongType) {
        this.belongType = belongType;
    }

    public Long getDestId() {
        return destId;
    }

    public void setDestId(Long destId) {
        this.destId = destId;
    }

    public String getDestName() {
        return destName;
    }

    public void setDestName(String destName) {
        this.destName = destName;
    }
    /**
     * 二进制标志位，从低位到高位依次表示
     * 2^0 已取消换商品异常（反审核时会取消该标记）
     */
    private Long v;
    /**
     * 记录在业务中改order做了那些操作
     */
    private Map<OpEnum, String> operations = new HashMap<>();

    /**
     * 平台实付(平台分摊之后的实付金额) http://doc.raycloud.com/pages/viewpage.action?pageId=30544685
     */
    private String divideOrderFee;

    /**
     * 前N有礼活动id
     */
    private String osActivityId;

    /**
     * 前N有礼赠品id
     */
    private String osFgItemId;

    /**
     * 前N有礼赠品数量
     */
    private String osGiftCount;

    /**
     * 前N有礼中奖名次，获得奖品的订单才会有该字段
     */
    private String osSortNum;

    /**
     * 是否需要上传发货信息，非持久化
     */
    private boolean needUploadConsign;

    /**
     * 是否系统触发过上传发货
     */
    private boolean sysTriggerUpload;

    /**
     * 分销订单是否系统触发过上传发货
     */
    private boolean fxSysTriggerUpload;

    /**
     * 新的不需要落库的属性可以加到这里面，防止类爆炸
     */
    private OrderExt orderExt;

    /**
     * 销货单备注信息（批发收银，无需落库）
     */
    private String saleRemark;

    /**
     * 商品状态
     */
    private Integer itemActiveStatus;
    /**
     * 商品上下架状态 0 下架 1 上架
     */
    private Integer itemGoodsStatus;

    public Integer getItemGoodsStatus() {
        return itemGoodsStatus;
    }

    public void setItemGoodsStatus(Integer itemGoodsStatus) {
        this.itemGoodsStatus = itemGoodsStatus;
    }

    /**
     * 可用库存，非持久化
     */
    private long availableStock;

    /**
     * 预售是否锁库存 0：是；1：否
     */
    private int preSellLockStock;

    private Boolean isDaixiao;

    private String oldPayment;

    /**
     * 订单买家留言 打印用
     */
    private String tradeBuyerMessage;

    /**
     * 订单卖家备注 打印用
     */
    private String tradeSellerMemo;

    /**
     * 免拣免验标记
     */
    private Integer pickCheckFlag;

    /**
     * 已进行过替换标识
     * 不持久化，作为单次任务执行标记
     */
    private boolean alreadyReplace;

    public String getOldPayment() {
        return oldPayment;
    }

    public void setOldPayment(String oldPayment) {
        this.oldPayment = oldPayment;
    }

    public Boolean getIsDaixiao() {
        return this.isDaixiao;
    }

    public void setIsDaixiao(Boolean isDaixiao) {
        this.isDaixiao = isDaixiao;
    }

    public long getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(long availableStock) {
        this.availableStock = availableStock;
    }

    /**
     * 供应商分类 不持久化
     */
    private List<Supplier> supplierList = new ArrayList<>();

    public List<Supplier> getSupplierList() {
        return supplierList;
    }

    public void setSupplierList(List<Supplier> supplierList) {
        this.supplierList = supplierList;
    }

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 平台赠品对应的主商品id,不持久化
     */
    private List<String> platformGiftMasterItemIds;

    /**
     * 采购单号
     */
    private String caigouCode;

    /**
     * 套件商家编码  不做持久化
     */
    private String itemSuitOuterId;
    /**
     * 套件商家编码备注  不做持久化
     */
    private String itemSuitRemark;

    /**
     * 货位不参与拣选
     */
    private Integer gsNotPick;

    /**
     * combineId对应Order的名称 不做持久化
     */
    private String combineSysTitle;

    private String combinePayment;

    /**
     * 供分销关系中，对应分销订单Order的id，不做持久化
     * order对应关系： 分销 id = 供销的oid
     */
    private Long fxOrderId;

    /**
     * 展示明细，以套件维度排序专属字段，套件商品id
     */
    private Long suitSortSysId;
    private Long suitSortSkuId;

    public String getCaigouCode() {
        return caigouCode;
    }

    public void setCaigouCode(String caigouCode) {
        this.caigouCode = caigouCode;
    }

    public List<String> getPlatformGiftMasterItemIds() {
        return platformGiftMasterItemIds;
    }

    public void setPlatformGiftMasterItemIds(List<String> platformGiftMasterItemIds) {
        this.platformGiftMasterItemIds = platformGiftMasterItemIds;
    }

    /**
     * 生产信息（生产批次、日期）
     */
    private List<OrderProduct> orderProducts;
    /**
     * 生产日期信息
     */
    private String productDateInfo;
    /**
     * 批次信息
     */
    private String batchNoInfo;

    private Integer periodCast;//有效期

    public Integer getPeriodCast() {
        return periodCast;
    }

    public void setPeriodCast(Integer periodCast) {
        this.periodCast = periodCast;
    }

    /**
     * 生产信息（新版的生产批次、日期）
     */
    private List<OrderStockProduct> orderStockProducts;

    /**
     * 辅助单位
     */
    private List<OrderUnitVO> assistUnitList;

    /**
     * 供销快麦通商品id
     */
    private String supplierBaseItemId;

    /**
     * 供销快麦通sku-id
     */
    private String supplierSkuId;

    /**
     * 组合装下的单品集合,只针对组合装而言
     */
    private List<Order> groups;

    private boolean isPeriodItem;

    /**
     * 系统商品的一级类目
     */
    private List<Long> firstCat;

    /**
     * 订单行缺货数量，不持久化
     */
    private Integer insufficientNum;

    public Integer getInsufficientNum() {
        return insufficientNum;
    }

    public void setInsufficientNum(Integer insufficientNum) {
        this.insufficientNum = insufficientNum;
    }

    public List<OrderUnitVO> getAssistUnitList() {
        return assistUnitList;
    }

    public void setAssistUnitList(List<OrderUnitVO> assistUnitList) {
        this.assistUnitList = assistUnitList;
    }

    /**
     * 业务操作标志--不落库。
     *
     */
    private Set<OpVEnum> opVEnumSet;

    /**
     * 业务操作标志--不落库。 值是String给前端用，暂时不考虑json方式传opVEnumSet。实现后可以去掉此字段。
     *
     */
    private Set<String> opVEnumCodeSet;


    public Set<String> getOpVEnumCodeSet() {
        return opVEnumCodeSet;
    }

    public void setOpVEnumCodeSet(Set<String> opVEnumCodeSet) {
        this.opVEnumCodeSet = opVEnumCodeSet;
        if(CollectionUtils.isNotEmpty(opVEnumCodeSet)){
            if(CollectionUtils.isEmpty(opVEnumSet)){
                opVEnumSet = Sets.newHashSetWithExpectedSize(4);
            }
            opVEnumCodeSet.forEach(v->{
                opVEnumSet.add(OpVEnum.valueOfCode(v));
            });
        }
    }

    public Set<OpVEnum> getOpVEnumSet() {
        return opVEnumSet;
    }

    public void setOpVEnumSet(Set<OpVEnum> opVEnumSet) {
        this.opVEnumSet = opVEnumSet;
    }

    public void addOpV(OpVEnum opVEnum) {
        if(opVEnum ==null){
            return;
        }
        if(CollectionUtils.isEmpty(opVEnumSet)){
            opVEnumSet = Sets.newHashSetWithExpectedSize(4);
        }
        opVEnumSet.add(opVEnum);
    }

    public void removeOpV(OpVEnum opVEnum) {
        if(CollectionUtils.isEmpty(opVEnumSet) || opVEnum ==null){
            return;
        }
        opVEnumSet.remove(opVEnum);
    }
    /**
     *
     * @return
     */
    public boolean hasOpV(OpVEnum opVEnum) {
        return CollectionUtils.isNotEmpty(opVEnumSet) && opVEnumSet.contains(opVEnum);
    }

    /**
     * 1.手工添加；2.手工换货；999.临时标识
     */
    private Integer fromTo;

    public Integer getFromTo() {
        return fromTo;
    }

    public void setFromTo(Integer fromTo) {
        this.fromTo = fromTo;
    }

    /**
     * 波次填充信息，不再新增新的关于波次的字段
     */
    private WaveAssembleInfo waveAssembleInfo;

    public String getBarcode() {
		return barcode;
	}

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getSplitNum() {
        return splitNum;
    }

    public void setSplitNum(Integer splitNum) {
        this.splitNum = splitNum;
    }

    public String getIdentCode() {
        return identCode;
    }

    public void setIdentCode(String identCode) {
        this.identCode = identCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public Integer getInsufficientCanceled() {
        return insufficientCanceled;
    }

    public void setInsufficientCanceled(Integer insufficientCanceled) {
        this.insufficientCanceled = insufficientCanceled;
    }

    public Integer getRelationChanged() {
        return relationChanged;
    }

    public void setRelationChanged(Integer relationChanged) {
        this.relationChanged = relationChanged;
    }

    public String getStockStatus() {
        return stockStatus;
    }

    public void setStockStatus(String stockStatus) {
        this.stockStatus = stockStatus;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    public Integer getOldStockNum() {
        return oldStockNum;
    }

    public void setOldStockNum(Integer oldStockNum) {
        this.oldStockNum = oldStockNum;
    }

    public Integer getDiffStockNum() {
        return diffStockNum;
    }

    public void setDiffStockNum(Integer diffStockNum) {
        this.diffStockNum = diffStockNum;
    }

    public Long getItemSysId() {
        return itemSysId;
    }

    public void setItemSysId(Long itemSysId) {
        this.itemSysId = itemSysId;
    }

    public Long getSkuSysId() {
        return skuSysId;
    }

    public void setSkuSysId(Long skuSysId) {
        this.skuSysId = skuSysId;
    }

    public String getOuterIid() {
        return outerIid;
    }

    public void setOuterIid(String outerIid) {
        this.outerIid = outerIid;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public double getPriceDouble() {
        try {
            return getPrice() == null || "".equals(getPrice()) ? 0 : Double.valueOf(getPrice());
        } catch (Exception e) {
            return 0;
        }
    }

    public double getDiscountedPrice(){
        try {
            return getOldPrice() != null ? NumberUtils.str2Double(getOldPrice()) : NumberUtils.formatDouble(NumberUtils.str2Double(getPayment()) / getNum(), 3);
        } catch (Exception e) {
            return 0;
        }
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSkuUnit() {
        return skuUnit;
    }

    public Order setSkuUnit(String skuUnit) {
        this.skuUnit = skuUnit;
        return this;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getSkuPropertiesName() {
        return skuPropertiesName;
    }

    public void setSkuPropertiesName(String skuPropertiesName) {
        this.skuPropertiesName = skuPropertiesName;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getOldRefundStatus() {
        return oldRefundStatus;
    }

    public void setOldRefundStatus(String oldRefundStatus) {
        this.oldRefundStatus = oldRefundStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPicPath() {
        return picPath;
    }
    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public String getSysPicPath() {
        return sysPicPath;
    }

    public void setSysPicPath(String sysPicPath) {
        this.sysPicPath = sysPicPath;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public Integer getSysConsigned() {
        return sysConsigned;
    }

    public void setSysConsigned(Integer sysConsigned) {
        this.sysConsigned = sysConsigned;
    }

    public Long getSourceSid() {
        return sourceSid;
    }

    public void setSourceSid(Long sourceSid) {
        this.sourceSid = sourceSid;
    }

    public Parcel getParcel() {
        return parcel;
    }

    public void setParcel(Parcel parcel) {
        this.parcel = parcel;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getUpdTime() {
        return updTime;
    }

    public void setUpdTime(Date updTime) {
        this.updTime = updTime;
    }

    public String getNumIid() {
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Integer getSubStock() {
        return subStock;
    }

    public void setSubStock(Integer subStock) {
        this.subStock = subStock;
    }

    public String getOldSysStatus() {
        return oldSysStatus;
    }

    public void setOldSysStatus(String oldSysStatus) {
        this.oldSysStatus = oldSysStatus;
    }

    public String getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getSysOuterId() {
        return sysOuterId;
    }

    public void setSysOuterId(String sysOuterId) {
        this.sysOuterId = sysOuterId;
    }

    public String getSysSkuPropertiesName() {
        return sysSkuPropertiesName;
    }

    public void setSysSkuPropertiesName(String sysSkuPropertiesName) {
        this.sysSkuPropertiesName = sysSkuPropertiesName;
    }

    public String getSysSkuPropertiesAlias() {
        return sysSkuPropertiesAlias;
    }

    public void setSysSkuPropertiesAlias(String sysSkuPropertiesAlias) {
        this.sysSkuPropertiesAlias = sysSkuPropertiesAlias;
    }

    public String getSysItemRemark() {
        return sysItemRemark;
    }

    public void setSysItemRemark(String sysItemRemark) {
        this.sysItemRemark = sysItemRemark;
    }

    public String getSysSkuRemark() {
        return sysSkuRemark;
    }

    public void setSysSkuRemark(String sysSkuRemark) {
        this.sysSkuRemark = sysSkuRemark;
    }

    public String getSysTitle() {
        return sysTitle;
    }

    public void setSysTitle(String sysTitle) {
        this.sysTitle = sysTitle;
    }

    public String getCids() {
        return cids;
    }

    public void setCids(String cids) {
        this.cids = cids;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getOldNum() {
        return oldNum;
    }

    public void setOldNum(Integer oldNum) {
        this.oldNum = oldNum;
    }

    public Long getCombineId() {
        return combineId;
    }

    public void setCombineId(Long combineId) {
        this.combineId = combineId;
    }

    public Integer getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(Integer giftNum) {
        this.giftNum = giftNum;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Integer getItemChanged() {
        return itemChanged;
    }

    public void setItemChanged(Integer itemChanged) {
        this.itemChanged = itemChanged;
    }

    public Integer getSysItemChanged() {
        return sysItemChanged;
    }

    public Order setSysItemChanged(Integer sysItemChanged) {
        this.sysItemChanged = sysItemChanged;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Map<String, OrderType> getOrderTypeMap() {
        return orderTypeMap;
    }

    public void setOrderTypeMap(Map<String, OrderType> orderTypeMap) {
        this.orderTypeMap = orderTypeMap;
    }

    public Integer getSplitType() {
        return splitType;
    }

    public void setSplitType(Integer splitType) {
        this.splitType = splitType;
    }

    public boolean isOperable() {
        return operable;
    }

    public void setOperable(boolean operable) {
        this.operable = operable;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOldWarehouseId() {
        return oldWarehouseId;
    }

    public void setOldWarehouseId(Long oldWarehouseId) {
        this.oldWarehouseId = oldWarehouseId;
    }

    public Order getOrigin() {
        return origin;
    }

    public void setOrigin(Order origin) {
        this.origin = origin;
    }

    public Order getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(Order platformOrder) {
        this.platformOrder = platformOrder;
    }

    public List<Order> getSuits() {
        return suits;
    }

    public void setSuits(List<Order> suits) {
        this.suits = suits;
    }

    public Integer getSuitItemCount() {
        return suitItemCount == null ? 0 : suitItemCount;
    }

    public void setSuitItemCount(Integer suitItemCount) {
        this.suitItemCount = suitItemCount;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getOldStockStatus() {
        return oldStockStatus;
    }

    public void setOldStockStatus(String oldStockStatus) {
        this.oldStockStatus = oldStockStatus;
    }

    public String getSoid() {
        return soid;
    }

    public void setSoid(String soid) {
        this.soid = soid;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
    }

    public Long getOldItemSysId() {
        return oldItemSysId;
    }

    public void setOldItemSysId(Long oldItemSysId) {
        this.oldItemSysId = oldItemSysId;
    }

    public Long getOldSkuSysId() {
        return oldSkuSysId;
    }

    public void setOldSkuSysId(Long oldSkuSysId) {
        this.oldSkuSysId = oldSkuSysId;
    }

    public String getOldSysOuterId() {
        return oldSysOuterId;
    }

    public void setOldSysOuterId(String oldSysOuterId) {
        this.oldSysOuterId = oldSysOuterId;
    }


    public String getSysItemOuterId() {
        return sysItemOuterId;
    }

    public void setSysItemOuterId(String sysItemOuterId) {
        this.sysItemOuterId = sysItemOuterId;
    }

    /**
     * 是否是预售订单
     *
     * @return
     */
    public boolean isPresellOrder() {
        if (this.isPresell != null && (this.getIsPresell() == 1 || this.getIsPresell() == 3)) {
            return true;
        }
        return false;
    }

    /**
     * 预售订单转正常订单
     * @return
     */
    public Boolean presell2Normal(){
        if (Objects.nonNull(getIsPresell()) && 2 - getIsPresell() == 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断子订单是否可编辑修改
     *
     * @return
     */
    public boolean canUpdate() {
        if (TradeStatusUtils.getSysStatusWeight(sysStatus) > TradeConstants.WEIGHT_WAIT_AUDIT) {
            return false;
        }
        return isOperable();
    }

    /**
     * 判断一个子订单是否为套件子订单下的单品或套件子订单本身,套件子订单及其下面的单品type=2,但单品的enableStatus=2
     *
     * @param single true 判断是否为单品, false 判断是否为套件子订单本身
     * @return
     */
    public boolean isSuit(boolean single) {
        int orderType = getType() == null ? 0 : getType();
        long combineId = getCombineId() == null ? 0 : getCombineId();
        return orderType == TypeOfCombineOrder && (single ? combineId > 0 : combineId == 0);
    }

    /**
     * 判断一个子订单是否为组合商品下的单品或组合商品本身
     *
     * @param single true 判断是否为单品, false 判断是否为组合商品订单本身
     */
    public boolean isGroup(boolean single) {
        int orderType = getType() == null ? 0 : getType();
        long combineId = getCombineId() == null ? 0 : getCombineId();
        return orderType == TypeOfGroupOrder && (single ? combineId > 0 : combineId == 0);
    }

    /**
     * 判断一个子订单是否为套件
     *
     * @return
     */
    public boolean isSuit() {
        return getType() != null && getType() == TypeOfCombineOrder;
    }

    /**
     * 是否组合
     */
    public boolean isGroup(){
        return getType() == null ? false : getType() == TypeOfGroupOrder;
    }

    /**
     * 是否加工
     */
    public boolean isProcess(){
        return getType() == null ? false : getType() == TypeOfProcessOrder;
    }
    /**
     * 判断一个子订单是否为加工商品下的单品或加工商品本身
     *
     * @param single true 判断是否为单品, false 判断是否为加工商品订单本身
     */
    public boolean isProcess(boolean single) {
        int orderType = getType() == null ? 0 : getType();
        long combineId = getCombineId() == null ? 0 : getCombineId();
        return orderType == TypeOfProcessOrder && (single ? combineId > 0 : combineId == 0);
    }

    public Long getBelongSid() {
        return belongSid;
    }

    public void setBelongSid(Long belongSid) {
        this.belongSid = belongSid;
    }

    /**
     * 判断一个套件是单品还是套件本身
     *
     * @return
     */

    public boolean isSuitSingle() {
        return getType() != null && getType() == TypeOfCombineOrder && getCombineId() != null && getCombineId() > 0;
    }

    public boolean isVirtual() {
        return getIsVirtual() != null && getIsVirtual() - 1 == 0;
    }

    public Long getMergeSid() {
        return mergeSid;
    }

    public void setMergeSid(Long mergeSid) {
        this.mergeSid = mergeSid;
    }

    public boolean isCanBeUpdated() {
        return canBeUpdated;
    }

    public void setCanBeUpdated(boolean canBeUpdated) {
        this.canBeUpdated = canBeUpdated;
    }

    public Long getCustomGiftType() {
        return customGiftType;
    }

    public void setCustomGiftType(Long customGiftType) {
        this.customGiftType = customGiftType;
    }

    public Integer getScalping() {
        return scalping;
    }

    public void setScalping(Integer scalping) {
        this.scalping = scalping;
    }

    public Integer getIsPick() {
        return isPick;
    }

    public void setIsPick(Integer isPick) {
        this.isPick = isPick;
    }

    public TradeWaveSortingDetail getWaveSortingDetail() {
        return waveSortingDetail;
    }

    public void setWaveSortingDetail(TradeWaveSortingDetail waveSortingDetail) {
        this.waveSortingDetail = waveSortingDetail;
    }

    public Set<String> getExceptions() {
        return exceptions;
    }

    public void setExceptions(Set<String> exceptions) {
        this.exceptions = exceptions;
    }

    public Date getPtConsignTime() {
        return ptConsignTime;
    }

    public void setPtConsignTime(Date ptConsignTime) {
        this.ptConsignTime = ptConsignTime;
    }

    public String getGoodsSectionNumStr() {
        return goodsSectionNumStr;
    }

    public void setGoodsSectionNumStr(String goodsSectionNumStr) {
        this.goodsSectionNumStr = goodsSectionNumStr;
    }

    public List<String> getGoodsSectionCodes() {
        return goodsSectionCodes;
    }

    public void setGoodsSectionCodes(List<String> goodsSectionCodes) {
        this.goodsSectionCodes = goodsSectionCodes;
    }

    public boolean getKeepInsufficientStockStatus() {
        return keepInsufficientStockStatus;
    }

    public void setKeepInsufficientStockStatus(boolean keepInsufficientStockStatus) {
        this.keepInsufficientStockStatus = keepInsufficientStockStatus;
    }

    public Date getEstimateConTime() {
        return estimateConTime;
    }

    public void setEstimateConTime(Date estimateConTime) {
        this.estimateConTime = estimateConTime;
    }

    public Integer getNonConsign() {
        return nonConsign;
    }

    public void setNonConsign(Integer nonConsign) {
        this.nonConsign = nonConsign;
    }

    public boolean ifNonConsign() {
        return Objects.equals(getNonConsign(), 1);
    }

    public Long getV() {
        return v;
    }

    public void setV(Long v) {
        this.v = v;
    }

    /**
     * @param v
     * @return
     */
    public boolean addV(int v) {
        if (this.v != null) {
            if ((this.v | v) != this.v) {
                this.v |= v;
                return true;
            }
            return false;
        }
        this.v = (long) v;
        return true;
    }

    public boolean removeV(int v) {
        if (this.v != null && (this.v | v) - this.v == 0) {
            this.v = this.v - v;
            return true;
        }
        return false;
    }

    /**
     * 是否取消过平台换商品异常
     * @return
     */
    public boolean isItemChangeCancelled() {
        return v != null && (v | 1) - v == 0;
    }

    /**
     *  套件修改异常交易目前不实用这个方法
     * @return
     */
    @Deprecated
    public boolean isSuiteChanged() {
        return v != null && (v | (1 << 3)) - v == 0;
    }

    public Map<OpEnum, String> getOperations() {
        return operations;
    }

    public void setOperations(Map<OpEnum, String> operations) {
        this.operations = operations;
    }

    public Integer getForcePackNum() {
        return forcePackNum;
    }

    public void setForcePackNum(Integer forcePackNum) {
        this.forcePackNum = forcePackNum;
    }

    public List<Long> getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(List<Long> brandIds) {
        this.brandIds = brandIds;
    }

    public List<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(List<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    /**
     * 赠品是否参与拣选
     * @return
     */
    public boolean needPicking(){
        return isPick != null && (isPick == 1 || isPick == 2);
    }

    /**
     * 赠品是否参与验货
     * @return
     */
    public boolean needVerify(){
        return isPick != null && (isPick == 2 || isPick == 3);
    }

    public String getDivideOrderFee() {
        return divideOrderFee;
    }

    public void setDivideOrderFee(String divideOrderFee) {
        this.divideOrderFee = divideOrderFee;
    }

    /**
     * 是否是平台纯商品
     * @return
     */
    public boolean isPfPureItem(){
        if(StringUtils.isEmpty(this.skuId) && StringUtils.isNotEmpty(this.numIid)){
            return true;
        }

        return false;
    }

    public String getPfItemOrSkuOuterIid(){
        //部分平台没有numIid和skuId，直接取值outerIid
        if(isPfPureItem() || (StringUtils.isNotEmpty(this.getOuterIid()) && StringUtils.isEmpty(this.getOuterSkuId()))){
            return this.getOuterIid();
        }

        return this.getOuterSkuId();
    }


    public boolean isInsert() {
        return insert;
    }

    public void setInsert(boolean insert) {
        this.insert = insert;
    }

    public boolean isGift(){
        return (getGiftNum() != null && getGiftNum() > 0) || (customGiftType != null && customGiftType == 1);
    }

    public boolean isPlatformGift(){
        return getGiftNum() != null && getGiftNum() > 0 && !CommonConstants.PLAT_FORM_TYPE_SYS.equalsIgnoreCase(getSource());
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }
    public String getOsActivityId() {
        return osActivityId;
    }

    public void setOsActivityId(String osActivityId) {
        this.osActivityId = osActivityId;
    }

    public String getOsFgItemId() {
        return osFgItemId;
    }

    public void setOsFgItemId(String osFgItemId) {
        this.osFgItemId = osFgItemId;
    }

    public String getOsGiftCount() {
        return osGiftCount;
    }

    public void setOsGiftCount(String osGiftCount) {
        this.osGiftCount = osGiftCount;
    }

    public String getOsSortNum() {
        return osSortNum;
    }

    public void setOsSortNum(String osSortNum) {
        this.osSortNum = osSortNum;
    }

    public String getFxSoid() {
        return FxSoid;
    }

    public void setFxSoid(String fxSoid) {
        FxSoid = fxSoid;
    }

    public boolean isNeedUploadConsign() {
        return needUploadConsign;
    }

    public void setNeedUploadConsign(boolean needUploadConsign) {
        this.needUploadConsign = needUploadConsign;
    }

    public boolean isSysTriggerUpload() {
        return sysTriggerUpload;
    }

    public void setSysTriggerUpload(boolean sysTriggerUpload) {
        this.sysTriggerUpload = sysTriggerUpload;
    }

    public boolean isFxSysTriggerUpload() {
        return fxSysTriggerUpload;
    }

    public void setFxSysTriggerUpload(boolean fxSysTriggerUpload) {
        this.fxSysTriggerUpload = fxSysTriggerUpload;
    }

    public List<SplitUploadRecord> getSplitUploadRecords() {
        return splitUploadRecords;
    }

    public void setSplitUploadRecords(List<SplitUploadRecord> splitUploadRecords) {
        this.splitUploadRecords = splitUploadRecords;
    }

    public SplitUploadRecord getInsertSplitUploadRecord() {
        return insertSplitUploadRecord;
    }

    public void setInsertSplitUploadRecord(SplitUploadRecord insertSplitUploadRecord) {
        this.insertSplitUploadRecord = insertSplitUploadRecord;
    }

    public Order getNeedUploadOrder() {
        return needUploadOrder;
    }

    public void setNeedUploadOrder(Order needUploadOrder) {
        this.needUploadOrder = needUploadOrder;
    }

    public String getSaleRemark() {
        return saleRemark;
    }

    public void setSaleRemark(String saleRemark) {
        this.saleRemark = saleRemark;
    }

    public List<String> getUniqueCodes() {
        return uniqueCodes;
    }

    public OrderExt getOrderExt() {
        return orderExt;
    }

    public void setOrderExt(OrderExt orderExt) {
        this.orderExt = orderExt;
    }

    /**
     * @return 是否平台预售
     */
    public boolean isPlatformPresell() {
        return Objects.nonNull(getIsPresell()) && getIsPresell() - 4 == 0;
    }

    /**
     * @return 是否全款预售
     */
    public boolean isFullPresell() {
        return Objects.nonNull(getIsPresell()) && getIsPresell() - 5 == 0;
    }

    public boolean isCoverTradePresell(){
        if (getIsPresell() == null) return true;
        if ((getIsPresell() - 4 == 0) || (getIsPresell() - 5 == 0)) {
            return false;
        }
        return true;
    }

    public void setUniqueCodes(List<String> uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    @Override
    public String toString(){
        StringBuilder sb =new StringBuilder("{");
        sb.append("id:").append(getId());
        sb.append(", oid:").append(getOid());
        sb.append(", sid:").append(getSid());
        sb.append(", tid:").append(getTid());
        sb.append(", itemSysId:").append(itemSysId);
        sb.append(", skuSysId:").append(skuSysId);
        sb.append(", num:").append(num);
        sb.append(", warehouseId:").append(warehouseId);
        sb.append(", supplierIds:").append(supplierIds);
        sb.append(", itemActiveStatus:").append(itemActiveStatus);
        if(!Objects.isNull(orderExt)){
            sb.append(", orderExt:").append(orderExt.toString());
        }
        sb.append(", price:").append(getPrice());
        sb.append(", totalFee:").append(getTotalFee());
        sb.append(", discountFee:").append(getDiscountFee());
        sb.append(", payment:").append(getPayment());
        sb.append(", volume:").append(volume);
        sb.append(", giftNum:").append(getGiftNum());
        sb.append("}");
        return sb.toString();
    }


    public Integer getTradeNum() {
        return tradeNum;
    }

    public void setTradeNum(Integer tradeNum) {
        this.tradeNum = tradeNum;
    }

    public List<TradeHotItemDetail> getItemDetails() {
        return itemDetails;
    }

    public void setItemDetails(List<TradeHotItemDetail> itemDetails) {
        this.itemDetails = itemDetails;
    }

    public String getTidOidKey(){
        return this.getTid()+"_"+this.getOid();
    }

    public Integer getItemActiveStatus() {
        return itemActiveStatus;
    }

    public void setItemActiveStatus(Integer itemActiveStatus) {
        this.itemActiveStatus = itemActiveStatus;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public OrderOpeartEnum getOrderOpeart() {
        return orderOpeart;
    }

    public void setOrderOpeart(OrderOpeartEnum orderOpeart) {
        this.orderOpeart = orderOpeart;
    }

    public int getPreSellLockStock() {
        return preSellLockStock;
    }

    public void setPreSellLockStock(int preSellLockStock) {
        this.preSellLockStock = preSellLockStock;
    }

    /**
     * 平台 estimateConTime字段是否不为空，仅限淘宝、天猫
     */
    private boolean existEstimateConTime;

    public boolean isExistEstimateConTime() {
        return existEstimateConTime;
    }

    public void setExistEstimateConTime(boolean existEstimateConTime) {
        this.existEstimateConTime = existEstimateConTime;
    }


    public Integer getAutoUnattainable() {
        return autoUnattainable;
    }

    public void setAutoUnattainable(Integer autoUnattainable) {
        this.autoUnattainable = autoUnattainable;
    }

    public List<OrderProduct> getOrderProducts() {
        return orderProducts;
    }

    public void setOrderProducts(List<OrderProduct> orderProducts) {
        this.orderProducts = orderProducts;
    }

    public void addOrderProducts(OrderProduct orderProduct) {
        if (CollectionUtils.isEmpty(this.orderProducts)) {
            this.orderProducts = new ArrayList<>();
        }
        this.orderProducts.add(orderProduct);
    }

    public String getProductDateInfo() {
        return productDateInfo;
    }

    public void setProductDateInfo(String productDateInfo) {
        this.productDateInfo = productDateInfo;
    }

    public String getBatchNoInfo() {
        return batchNoInfo;
    }

    public void setBatchNoInfo(String batchNoInfo) {
        this.batchNoInfo = batchNoInfo;
    }

    public String getSellerCatName() {
        return sellerCatName;
    }

    public void setSellerCatName(String sellerCatName) {
        this.sellerCatName = sellerCatName;
    }

    public List<OrderItemTag> getOrderItemTags() {
        return orderItemTags;
    }

    public void setOrderItemTags(List<OrderItemTag> orderItemTags) {
        this.orderItemTags = orderItemTags;
    }

    public List<String> getOrderItemTagNames() {
        return orderItemTagNames;
    }

    public void setOrderItemTagNames(List<String> orderItemTagNames) {
        this.orderItemTagNames = orderItemTagNames;
    }

    public String getTradeBuyerMessage() {
        return tradeBuyerMessage;
    }

    public void setTradeBuyerMessage(String tradeBuyerMessage) {
        this.tradeBuyerMessage = tradeBuyerMessage;
    }

    public String getTradeSellerMemo() {
        return tradeSellerMemo;
    }

    public void setTradeSellerMemo(String tradeSellerMemo) {
        this.tradeSellerMemo = tradeSellerMemo;
    }
    public Integer getPickCheckFlag() {
        return pickCheckFlag;
    }

    public void setPickCheckFlag(Integer pickCheckFlag) {
        this.pickCheckFlag = pickCheckFlag;
    }

    public String getItemSuitOuterId() {
        return itemSuitOuterId;
    }

    public void setItemSuitOuterId(String itemSuitOuterId) {
        this.itemSuitOuterId = itemSuitOuterId;
    }
    public List<OrderStockProduct> getOrderStockProducts() {
        return orderStockProducts;
    }

    public void setOrderStockProducts(List<OrderStockProduct> orderStockProducts) {
        this.orderStockProducts = orderStockProducts;
    }

    /**
     * <pre>
     *     是否强制覆盖payment
     *     在 商品金额不联动 配置下,订单实付金额不随商品变动而改变,但是前端可以手工进行修改 这种场景下需要强制覆盖为前端传入的payment值
     *     不持久化到数据库
     * </pre>
     * @see PaymentNoLinkStrategy#insertOrder(PaymentLinkReqDTO)
     */
    private Boolean forceUpdPayment;

    public Boolean getForceUpdPayment() {
        return forceUpdPayment;
    }

    public void setForceUpdPayment(Boolean forceUpdPayment) {
        this.forceUpdPayment = forceUpdPayment;
    }

    public String getItemSuitRemark() {
        return itemSuitRemark;
    }

    public void setItemSuitRemark(String itemSuitRemark) {
        this.itemSuitRemark = itemSuitRemark;
    }
    public Integer getGsNotPick() {
        return gsNotPick;
    }

    public void setGsNotPick(Integer gsNotPick) {
        this.gsNotPick = gsNotPick;
    }

    public String getCombineSysTitle() {
        return combineSysTitle;
    }

    public void setCombineSysTitle(String combineSysTitle) {
        this.combineSysTitle = combineSysTitle;
    }

    public String getCombinePayment() {
        return combinePayment;
    }

    public void setCombinePayment(String combinePayment) {
        this.combinePayment = combinePayment;
    }

    public boolean isAlreadyReplace() {
        return alreadyReplace;
    }

    public void setAlreadyReplace(boolean alreadyReplace) {
        this.alreadyReplace = alreadyReplace;
    }

    public String getSupplierBaseItemId() {
        return supplierBaseItemId;
    }

    public void setSupplierBaseItemId(String supplierBaseItemId) {
        this.supplierBaseItemId = supplierBaseItemId;
    }

    public String getSupplierSkuId() {
        return supplierSkuId;
    }

    public void setSupplierSkuId(String supplierSkuId) {
        this.supplierSkuId = supplierSkuId;
    }

    public List<Order> getGroups() {
        return groups;
    }

    public void setGroups(List<Order> groups) {
        this.groups = groups;
    }

    public Long getFxOrderId() {
        return fxOrderId;
    }

    public void setFxOrderId(Long fxOrderId) {
        this.fxOrderId = fxOrderId;
    }

    public boolean isPeriodItem() {
        return isPeriodItem;
    }

    public void setPeriodItem(boolean isPeriodItem) {
        isPeriodItem = isPeriodItem;
    }

    public Long getSuitSortSysId() {
        return suitSortSysId;
    }

    public void setSuitSortSysId(Long suitSortSysId) {
        this.suitSortSysId = suitSortSysId;
    }

    public Long getSuitSortSkuId() {
        return suitSortSkuId;
    }

    public void setSuitSortSkuId(Long suitSortSkuId) {
        this.suitSortSkuId = suitSortSkuId;
    }

    public List<Long> getFirstCat() {
        return firstCat;
    }

    public void setFirstCat(List<Long> firstCat) {
        this.firstCat = firstCat;
    }

    public List<Long> getSellerCids() {
        return sellerCids;
    }

    public void setSellerCids(List<Long> sellerCids) {
        this.sellerCids = sellerCids;
    }

    public WaveAssembleInfo getWaveAssembleInfo() {
        return waveAssembleInfo;
    }

    public void setWaveAssembleInfo(WaveAssembleInfo waveAssembleInfo) {
        this.waveAssembleInfo = waveAssembleInfo;
    }
}

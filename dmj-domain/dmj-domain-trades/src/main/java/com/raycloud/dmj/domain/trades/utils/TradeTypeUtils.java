package com.raycloud.dmj.domain.trades.utils;

import com.google.common.base.Objects;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.type.TradeType;
import com.raycloud.dmj.domain.trade.type.TradeTypeNewEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.TradeTypeConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * TradeTypeUtils
 * 订单类型处理工具类
 *
 * <AUTHOR>
 * @Date 2018/11/20
 * @Time 17:17
 */
public class TradeTypeUtils {

    private static List<String> TRADE_MERGE_WHITE_TYPES;

    private TradeTypeUtils() {
    }

    static {
        TRADE_MERGE_WHITE_TYPES = Arrays.asList("step");
    }

    /**
     * 获取订单在合并时的type
     *      TRADE_MERGE_WHITE_TYPES中的订单类型，只要在待审核情况下认为是普通订单
     * @param trade
     * @return
     */
    public static String getMergeTradeType(Trade trade,boolean searchFinishAudit,boolean mergePresellNorlMal) {
        if (trade == null) {
            return "";
        }
        String tradeType = StringUtils.stripToEmpty(trade.getType());
        return ((searchFinishAudit
                ? (TradeStatusUtils.isWaitAudit(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()))
                : (TradeStatusUtils.isWaitAudit(trade.getSysStatus())))
                && mergePresellNorlMal) ? "fixed" : tradeType;
    }

    /**
     * 是否换货类型订单
     *
     * @param type
     * @return
     */
    public static boolean isExchangeTradeType(String type) {
        return Objects.equal(type, "changeitem");
    }

    /**
     * 是否补发类型订单
     *
     * @param type
     * @return
     */
    public static boolean isReissueTradeType(String type) {
        return Objects.equal(type, "reissue");
    }

    /**
     * 是否线下类型订单
     *
     * @param source
     * @return
     */
    public static boolean isOfflineTradeType(String source) {
        return Objects.equal(source, "sys");
    }


    /**
     * 是抖店BTAS单
     * @param trade
     * @return
     */
    public static boolean isFxgBtasTrade(Trade trade){
        return Objects.equal(trade.getSubSource(), CommonConstants.PLAT_FORM_TYPE_BTAS);
    }

    /**
     * 是否为WMS BTAS订单
     * @param trade
     * @return
     */
    public static boolean isWmsBtasTrade(Trade trade){
        return Objects.equal(trade.getType(), CommonConstants.PLAT_FORM_TYPE_BTAS);
    }

    public static List<String> getTradeType(Trade trade) {
        List<String> result = new ArrayList<>();
        if (trade == null || trade.getType() == null) {
            result.add("");
            return result;
        }
        //BTAS订单直接返回类型
        if(isFxgBtasTrade(trade)){
            result.add("BTAS订单");
            return result;
        }
        switch (trade.getType()) {
            case "purchase_out":
                result.add("采退");
                break;
            case "reissue":
                result.add("补发");
                break;
            case "changeitem":
                result.add("换货");
                break;
            case "fenxiao":
                result.add("分销");
                break;
            case "cod":
                result.add("货到");
                break;
            case "jd-1":
                result.add("货到");
                break;
            case "ta":
                result.add("信保");
                break;
            case "wholesale":
                result.add("在线");
                break;
            case "ws":
                result.add("大额批发");
                break;
            case "yp":
                result.add("普通拿样");
                break;
            case "yf":
                result.add("一分钱拿样");
                break;
            case "fs":
                result.add("倒批(限时折扣)");
                break;
            case "cz":
                result.add("加工定制");
                break;
            case "ag":
                result.add("协议采购");
                break;
            case "hp":
                result.add("伙拼");
                break;
            case "supply":
                result.add("供销");
                break;
            case "factory":
                result.add("淘工厂");
                break;
            case "quick":
                result.add("快订");
                break;
            case "xiangpin":
                result.add("享拼");
                break;
            case "f2f":
                result.add("当面现付");
                break;
            case "cyfw":
                result.add("存样服务");
                break;
            case "sp":
                result.add("阿里代销");
                break;
            case "wg":
                result.add("微供");
                break;
            case "sd":
                result.add("经销");
                break;
            case "tmall_i18n":
                result.add("天猫国际");
                break;
            case "overseas_warehouse":
                result.add("海外仓");
                break;
            case "dangkou":
                result.add("档口");
                break;
            case  "step":
                result.add("平台预售");
                break;
            case "shopPresell":
                result.add("店铺预售");
                break;
        }

        if ("sys".equals(trade.getSource())) {
            result.add("系统订单");
        } else {
            result.add("平台订单");
        }
        if (trade.getIsPresell() != null && trade.getIsPresell() > 0 && !"shopPresell".equals(trade.getType()) && !"step".equals(trade.getType())) {
            result.add("预售");
        }
        if (TradeUtils.isMerge(trade)) {
            result.add("合单");
        }
        if (TradeUtils.isSplit(trade)) {
            result.add("拆单");
        }
        if (trade.getIsUrgent() != null && trade.getIsUrgent() == 1) {
            result.add("加急");
        }
        if (trade.getScalping() != null && trade.getScalping() == 1) {
            result.add("空单");
        }
//        if (trade.getCheckManualMergeCount() != null && trade.getCheckManualMergeCount() == 1) {
//            result.add("合单标记");
//        }
        if (trade.getIsStore() != null && trade.getIsStore() == 1) {
            result.add("门店订单");
        }
        if (trade.getIsTmallDelivery() != null && trade.getIsTmallDelivery() == 1) {
            result.add("天猫直送");
        }
        if ("jd_warehouse".equals(trade.getSubSource())) {
            result.add("京东仓直发");
        }
        if (TradeTypeConstants.POISION_NOMORL_SPOT_TRADE_TYPE.equals(trade.getType()) && CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource())) {
            result.add("得物普通现货");
        }
        if (TradeTypeConstants.POISION_FAST_SPOT_TRADE_TYPE.equals(trade.getType()) && CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource())) {
            result.add("得物极速现货");
        }
        if (TradeTypeConstants.POISON_BRAND_DELIVER_TRADE_TYPE.equals(trade.getType()) && CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource())) {
            result.add("得物品牌直发");
        }
        if (TradeUtils.isTbDfTrade(trade) || CommonConstants.PLAT_FORM_TYPE_TAO_BAO_DF.equals(trade.getType())){
            result.add("淘宝厂商代发");
        }
        if (TradeTypeConstants.TMGJZY_POST_GATE_DECLARE.equals(trade.getType()) && CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            result.add("天猫国际直邮");
        }
        return result;
    }

}

package com.raycloud.dmj.domain.platform.bo;

import com.raycloud.dmj.domain.platform.ErpPlatformErrorMapping;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * <AUTHOR>
 * @date 2023/3/23 11:33
 */
@Data
public class ErpPlatformErrorMappingBO implements Serializable {

    private static final long serialVersionUID = 8167061257609381065L;

    private Long id;
    /**
     * 平台来源
     * */
    private String source;

    /**
     * 是否有效
     */
    private Integer enableStatus;

    /**
     * 映射类型
     */
    private String matchType;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 平台错误码
     */
    private String platCode;

    /**
     * 平台错误信息
     */
    private String platMsg;

    /**
     * 平台子错误码
     */
    private String platSubCode;

    /**
     * 平台子错误信息
     */
    private String platSubMsg;

    /**
     * 系统错误码
     */
    private String sysCode;

    /**
     * 系统错误信息
     */
    private String sysMsg;

    /**
     * 缓存的key
     */
    private String cacheKey;

    public ErpPlatformErrorMapping toErpPlatformErrorMapping(){
        ErpPlatformErrorMapping mapping = new ErpPlatformErrorMapping();
        mapping.setId(id);
        mapping.setPlatCode(platCode);
        mapping.setPlatMsg(platMsg);
        mapping.setPlatSubCode(platSubCode);
        mapping.setPlatSubMsg(platSubMsg);
        mapping.setMatchType(matchType);
        mapping.setSysCode(sysCode);
        mapping.setSysMsg(sysMsg);
        mapping.setSource(source);
        mapping.setBizType(bizType);
        mapping.setEnableStatus(enableStatus);
        return mapping;
    }
}

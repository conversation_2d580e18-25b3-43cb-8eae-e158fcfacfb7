package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * Created by yang<PERSON><PERSON> on 17/7/20.
 */
@Table(name = "order_pack_split", routerKey = "orderPackSplitDbNo")
public class OrderPackSplit extends Model {

    private static final long serialVersionUID = 2917301001546673063L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 装箱编号
     */
    private Long packId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 子订单编号
     */
    private Long orderId;

    /**
     * 子订单数量
     */
    private Integer num;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 是否可用
     */
    private Integer enableStatus;

    /**
     *
     */
    private Order order;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getPackId() {
        return packId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }
}

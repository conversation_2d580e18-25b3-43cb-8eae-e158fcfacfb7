package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.*;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.audit.TradeAuditUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.DiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderUtils {

    static Map<String, String> URL_MAP = new HashMap<>();

    static List<ExceptEnum> orderExceptEnums = new ArrayList<>();

    static {
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_SYS, "");
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_JD, "http://item.jd.com/%s.html");
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_TAO_BAO, "https://item.taobao.com/item.htm?id=%s");//淘宝
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO, "https://detail.tmall.com/item.htm?id=%s");//天猫
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_1688, "https://detail.1688.com/offer/%s.html");
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_1688_C2M, "https://detail.1688.com/offer/%s.html");
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_PDD, "https://mobile.yangkeduo.com/goods2.html?goods_id=%s");//pdd
        URL_MAP.put(CommonConstants.PLAT_FORM_TYPE_SMT, "https://www.aliexpress.com/item/%s.html");//速卖通


        orderExceptEnums.add(ExceptEnum.RELATION_CHANGED);
        orderExceptEnums.add(ExceptEnum.UNALLOCATED);
        orderExceptEnums.add(ExceptEnum.INSUFFICIENT);
        orderExceptEnums.add(ExceptEnum.SUITE_CHANGE);
        orderExceptEnums.add(ExceptEnum.ITEM_SHUTOFF);
        orderExceptEnums.add(ExceptEnum.UNIQUE_CODE_OFFSHELF);
        orderExceptEnums.add(ExceptEnum.ITEM_PROCESS);
    }

    public static Set<Integer> parseExcept(Staff staff, Order order) {
        Set<Integer> except = new HashSet<Integer>();
        if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ORDER_LOST_SYS_STATUS)) {
            except.add((int) ExceptEnum.ORDER_LOST_SYS_STATUS.getOldExceptEnum().getOldIdx());
        }
        if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {
            if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ITEM_CHANGED)
                    && !TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                except.add((int) ExceptEnum.ITEM_CHANGED.getOldExceptEnum().getOldIdx());
            }
            for (ExceptEnum exceptEnum : orderExceptEnums) {
                if (OrderExceptUtils.isContainsExcept(staff, order, exceptEnum)) {
                    except.add((int) exceptEnum.getOldExceptEnum().getOldIdx());
                }
            }
        }
        if (OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.REFUNDING)) {
            except.add((int) ExceptEnum.REFUNDING.getOldExceptEnum().getOldIdx());
        }
        return except;
    }

    /**
     * 按照order.id为key，转换为map
     *
     * @param orders
     * @return
     */
    public static <T extends Order> Map<Long, Order> toMap(List<T> orders) {
        Map<Long, Order> map = new HashMap<Long, Order>(orders.size(), 1);
        for (Order order : orders) {
            map.put(order.getId(), order);
        }
        return map;
    }

    public static <T extends Order> Map<Long, Order> toMapByOid(List<T> orders) {
        Map<Long, Order> map = new HashMap<Long, Order>(orders.size(), 1);
        for (Order order : orders) {
            map.put(order.getOid(), order);
        }
        return map;
    }

    public static <T extends Order> Map<Long, Order> toMapById(List<T> orders) {
        Map<Long, Order> map = new HashMap<>(orders.size(), 1);
        if (CollectionUtils.isNotEmpty(orders)) {
            orders.forEach(order -> map.put(order.getId(), order));
        }
        return map;
    }

    public static <T extends Order> Map<String, List<Order>> toMapByTid(List<T> orders) {
        Map<String, List<Order>> map = new HashMap<>();
        if (orders != null && orders.size() > 0) {
            orders.forEach(order -> map.computeIfAbsent(order.getTid(), k -> new ArrayList<>()).add(order));
        }
        return map;
    }

    public static <T extends Order> Map<Long, List<Order>> toMapBySid(List<T> orders) {
        Map<Long, List<Order>> map = new HashMap<>();
        if (orders != null && orders.size() > 0) {
            for (Order order : orders) {
                map.computeIfAbsent(order.getSid(), k -> new ArrayList<>(6)).add(order);
            }
        }
        return map;
    }

    public static <T extends Order> Map<Long, List<Order>> toMapByBelongSid(List<T> orders) {
        Map<Long, List<Order>> map = new HashMap<>();
        if (orders != null && orders.size() > 0) {
            for (Order order : orders) {
                map.computeIfAbsent(order.getBelongSid(), k -> new ArrayList<>(6)).add(order);
            }
        }
        return map;
    }

    public static Map<Long, Order> toFullOrderMap(List<Order> orders) {
        Map<Long, Order> orderMap = new HashMap<Long, Order>(orders.size() + 1);
        for (Order order : orders) {
            orderMap.put(order.getId(), order);
            if (order.getSuits() != null) {
                order.getSuits().forEach(suit -> {
                    suit.setExceptData(order.getExceptData());
                    suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                });
                for (Order son : order.getSuits()) {
                    orderMap.put(son.getId(), son);
                }
            }
        }
        return orderMap;
    }

    public static List<Order> toOrders(List<TbOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }
        List<Order> list = new ArrayList<>(orders.size());
        if (orders.size() > 0) {
            list.addAll(orders);
        }
        return list;
    }

    /**
     * 取有效的子订单（套件取子订单，加工和组合同普通商品取本身）
     * 可以参与拣货的子订单
     *
     * @param orders
     * @return
     */
    public static List<Order> toEffectiveOrders(List<Order> orders) {
        List<Order> orderList = new ArrayList<>();
        for (Order order : orders) {
            if (order.isSuit(false)) {
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    order.getSuits().forEach(suit -> {
                        suit.setExceptData(order.getExceptData());
                        suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                    });
                    orderList.addAll(order.getSuits());
                }
            } else {
                orderList.add(order);
            }
        }
        return orderList;
    }

    public static List<Order> toSuitSelfOrders(List<Order> orders) {
        List<Order> suitSelfOrderList = new ArrayList<>();
        for (Order order : orders) {
            if (order.isSuit(false)) {
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    suitSelfOrderList.add(order);
                }
            }
        }
        return suitSelfOrderList;
    }

    /**
     * 取有套件本身和子订单（套件取套件本身和子订单，加工和组合同普通商品取本身）
     *
     * @param orders
     * @return
     */
    public static List<Order> toFullSuitOrders(List<Order> orders) {
        List<Order> orderList = new ArrayList<>();
        for (Order order : orders) {
            if (order.isSuit(false)) {
                orderList.add(order);
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    order.getSuits().forEach(suit -> {
                        suit.setExceptData(order.getExceptData());
                        suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                    });
                    orderList.addAll(order.getSuits());
                }
            } else {
                orderList.add(order);
            }
        }
        return orderList;
    }

    /**
     * 取有效的子订单（套件根据suitMode判断是否取子订单还是本身，加工和组合同普通商品取本身）
     * 可以有效子订单
     */
    public static List<Order> toEffectiveOrders(List<Order> orders, boolean suitMode) {
        List<Order> orderList = new ArrayList<>();
        for (Order order : orders) {
            if (order.isSuit(false)) {
                if (suitMode) {
                    orderList.add(order);
                } else {
                    if (CollectionUtils.isNotEmpty(order.getSuits())) {
                        orderList.addAll(order.getSuits());
                    }
                }
            } else {
                orderList.add(order);
            }
        }
        return orderList;
    }

    /**
     * @param orders
     * @param needRemoveSuitSelf 是否剔除套件子订单本身
     * @return
     */
    public static List<Order> toFullOrderList(List<Order> orders, boolean needRemoveSuitSelf) {
        List<Order> orderList = new ArrayList<>();
        for (Order order : orders) {
            List<Order> suits = order.getSuits();
            if (suits == null || suits.size() == 0) {
                orderList.add(order);
            } else {
                if (!needRemoveSuitSelf || !order.isSuit()) {
                    orderList.add(order);
                }
                for (Order suit : suits) {
                    if (suit.getWarehouseId() == null) {
                        suit.setWarehouseId(order.getWarehouseId());
                    }
                    suit.setExceptData(order.getExceptData());
                    suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                }
                orderList.addAll(suits);
            }
        }
        return orderList;
    }

    /**
     * @param order
     * @param needRemoveSuitSelf 是否剔除套件子订单本身
     * @return
     */
    public static List<Order> toFullOrderList(Order order, boolean needRemoveSuitSelf) {
        List<Order> orderList = new ArrayList<Order>();
        if (order.isSuit(false)) {
            if (!needRemoveSuitSelf) {
                orderList.add(order);
            }
            if (order.getSuits() != null) {
                order.getSuits().forEach(suit -> {
                    suit.setExceptData(order.getExceptData());
                    suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                });
                orderList.addAll(order.getSuits());
            }
        }
        return orderList;
    }

    /**
     * 将套件子订单下的单品子订单归属到对应的套件子订单下,生成至多两层的树形结构
     *
     * @param orders
     * @return
     */
    public static <T extends Order> List<Order> toTree(List<T> orders) {
        List<Order> tree = new ArrayList<>();
        if (orders == null || orders.size() == 0) {
            return tree;
        }
        Map<Long, Order> orderMap = toMap(orders);
        for (Order order : orders) {
            //隐藏的订单不处理
            if (order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getCombineId() != null && order.getCombineId() > 0) {
                Order parent = orderMap.get(order.getCombineId());
                if (parent != null) {
                    if (parent.getSuits() == null) {
                        parent.setSuits(new ArrayList<Order>(5));
                    }
                    parent.getSuits().add(order);
                }
            } else {
                tree.add(order);
            }
        }
        return tree;
    }

    /**
     * 提取orders中的ids
     *
     * @param orders
     * @return
     */
    public static <T extends Order> Long[] toIds(List<T> orders) {
        Set<Long> ids = new LinkedHashSet<Long>(orders.size());
        for (T o : orders) {
            ids.add(o.getId());
        }
        return ids.toArray(new Long[ids.size()]);
    }

    /**
     * 提取trades中所有order的id
     * by Jacky LIU 2019-03-12
     *
     * @param trades
     * @return
     */
    public static List<Long> toIdsWithTrades(List<Trade> trades) {
        List<Long> orderIds = new ArrayList<Long>(trades.size());
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                orderIds.add(order.getId());
            }
        }
        return orderIds;
    }

    /**
     * 提取orders中的ids
     *
     * @param orders
     * @return
     */
    public static <T extends Order> List<Long> toIdList(List<T> orders) {
        List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orders)) {
            for (T o : orders) {
                ids.add(o.getId());
            }
        }
        return ids;
    }

    public static <T extends Order> List<Long> toIdList(List<T> orders, boolean containsSuiteSingle) {
        List<Long> ids = new ArrayList<Long>(orders.size());
        for (Order order : orders) {
            ids.add(order.getId());
            if (containsSuiteSingle && order.getSuits() != null) {
                for (Order son : order.getSuits()) {
                    ids.add(son.getId());
                }
            }
        }
        return ids;
    }

    /**
     * 提取orders中的sids，并去掉重复的sid
     *
     * @param orders
     * @return
     */
    public static <T extends Order> Long[] toSids(List<T> orders) {
        Set<Long> sids = new HashSet<Long>();
        for (T order : orders) {
            sids.add(order.getSid());
        }
        return sids.toArray(new Long[sids.size()]);
    }

    public static <T extends Order> List<Long> toSidList(List<T> orders) {
        List<Long> sids = new ArrayList<Long>(orders.size());
        for (T order : orders) {
            if (!sids.contains(order.getSid())) {
                sids.add(order.getSid());
            }
        }
        return sids;
    }

    /**
     * 提取orders中的tids，并去掉重复的tid
     *
     * @param orders
     * @return
     */
    public static <T extends Order> String[] toTids(List<T> orders) {
        Set<String> tids = new HashSet<String>();
        for (T order : orders) {
            tids.add(order.getTid());
        }
        return tids.toArray(new String[tids.size()]);
    }

    public static <T extends Order> List<String> toTidList(List<T> orders) {
        Set<String> tidList = new HashSet<String>();
        for (T order : orders) {
            tidList.add(order.getTid());
        }
        return new ArrayList<String>(tidList);
    }

    public static <T extends Order> List<Long> toOidList(List<T> orders) {
        List<Long> oidList = new ArrayList<Long>(orders.size());
        for (Order order : orders) {
            oidList.add(order.getOid());
        }
        return oidList;
    }

    /**
     * 提取orders中的oids，并去掉重复的oid
     *
     * @param orders
     * @return
     */
    public static <T extends Order> Long[] toOids(List<T> orders) {
        if (null == orders || orders.size() == 0) {
            return new Long[0];
        }
        Set<Long> oids = new HashSet<Long>(orders.size());
        for (T order : orders) {
            if (order.getOid() != null) {
                oids.add(order.getOid());
            }
        }
        return oids.toArray(new Long[oids.size()]);
    }


    public static boolean iseBeforeSendGoods(Order order) {
        return TradeStatusUtils.iseBeforeSendGoods(order.getSysStatus());
    }


    public static boolean isAfterSendGoods(Order order) {
        return TradeStatusUtils.isAfterSendGoods(order.getSysStatus());
    }

    /**
     * 是否所有子订单 系统状态是否在发货之后
     */
    public static boolean isAllAfterSendGoods(List<Order> orders) {
        for (Order order : orders) {
            if (!isAfterSendGoods(order)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否任一子订单 系统状态是否在发货之后
     */
    public static boolean isAnyAfterSendGoods(List<Order> orders) {
        for (Order order : orders) {
            if (isAfterSendGoods(order)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取一笔子订单所对应的系统中已存在的原始子订单,订单同步时用
     * <p>
     * 订单同步时,对于非首次同步的订单,每一个子订单在系统中都有唯一一个已存在的子订单与之对应,
     * 对于套件商品,该子订单对应的是系统中已存在的套件子订单本身,不包含单品子订单,单品子订单在套件子订单本身的suits中
     * </p>
     *
     * @param trade 系统中已存在的原始订单
     * @param order 欲更新的子订单
     * @return
     */
    public static Order getOriginOrder(Order order, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (order.getId() != null) {
            for (Order o : orders) {
                if (order.getId() - o.getId() == 0) {
                    return o;
                }
            }
        } else if (order.getOid() != null) {//淘宝等平台子订单有oid,京东平台没有
            for (Order o : orders) {
                if (order.getSource().equals(o.getSource()) && o.getOid() - order.getOid() == 0) {
                    return o;
                }
            }
        } else {//有些平台子订单没有oid,如京东平台
            String key = buildKey(order);
            for (Order o : orders) {
                if (order.getSource().equals(o.getSource()) && buildKey(o).equals(key)) {
                    order.setOid(o.getOid());
                    return o;
                }
            }
        }
        return null;
    }

    private static String buildKey(Order order) {
        return new StringBuilder(order.getTid()).append("_").append(order.getOid() == null ? "0" : order.getOid()).append("_").append(order.getNumIid()).append("_").append(order.getSkuId() == null ? "0" : order.getSkuId().trim()).toString();
    }

    /**
     * 判断子订单商品是否变更,主要在订单同步时使用
     *
     * @param order  新同步时的子订单
     * @param origin 原先已同步时的子订单
     * @return boolean
     */
    public static boolean isItemChanged(Order order, Order origin) {
        return !StringUtils.contentEquals(order.getNumIid(), origin.getNumIid()) || !StringUtils.contentEquals(order.getSkuId(), origin.getSkuId());
    }

    public static boolean isFxItemChanged(Order order, Order origin) {
        boolean changed = false;
        if (OrderUtils.isGxOrder(origin)) {
            // 供销只需要比较 ## 前面的是否一致 因为ItemMatchService.splitItemIds() 有骚操作
            String outerSkuId = org.apache.commons.lang3.StringUtils.defaultString(order.getOuterSkuId());
            String originOuterSkuId = org.apache.commons.lang3.StringUtils.defaultString(origin.getOuterSkuId());
            if (outerSkuId.contains("##")) {
                outerSkuId = org.apache.commons.lang3.StringUtils.split(outerSkuId, "##")[0];
            }
            if (originOuterSkuId.contains("##")) {
                originOuterSkuId = org.apache.commons.lang3.StringUtils.split(originOuterSkuId, "##")[0];
            }
            changed = !StringUtils.contentEquals(outerSkuId, originOuterSkuId) || !StringUtils.contentEquals(order.getOuterIid(), origin.getOuterIid());
        }
        return changed;
    }

    /**
     * 判断订单洗系统商品是否修改过,主要在修改订单时使用
     *
     * @param order  要修改的子订单
     * @param origin 原始子订单
     * @return
     */
    public static boolean isSysItemChanged(Order order, Order origin) {
        if (order.getItemSysId() - origin.getItemSysId() != 0) {
            return true;
        }
        if (order.getSkuSysId() == null || order.getSkuSysId() <= 0) {
            return origin.getSkuSysId() != null && origin.getSkuSysId() > 0;
        }
        return order.getSkuSysId() - origin.getSkuSysId() != 0;
    }

    /**
     * 判断子订单中的平台商品是否为指定的商品
     *
     * @param order
     * @param sysItemId
     * @param sysSkuId
     * @return
     */
    public static boolean sysItemEquals(Order order, Long sysItemId, Long sysSkuId) {
        boolean equals = false;
        if (sysItemId != null && sysItemId > 0) {
            equals = sysItemId - order.getItemSysId() == 0;
        }
        if (sysSkuId != null && sysSkuId > 0 && order.getSkuSysId() != null) {
            equals = sysSkuId - order.getSkuSysId() == 0;
        }
        return equals;
    }

    public static String getSysRemark(Order order) {
        if (order == null) {
            return "";
        }
        if (order.getSkuSysId() == null) {
            return "";
        }
        return order.getSkuSysId() <= 0 || org.apache.commons.lang3.StringUtils.isEmpty(order.getSysSkuRemark()) ?
                order.getSysItemRemark() : order.getSysSkuRemark();
    }

    @Deprecated
    public static String buildKey(Long sysItemId, Long sysSkuId) {
        return TradeItemUtils.getItemKey(sysItemId, sysSkuId);
    }

    public static String getSnapshotUrl(Order order) {
        String source = order.getSource();
        if (!URL_MAP.containsKey(source)) {
            return "";
        }
        String url = URL_MAP.get(source);
        String id = "";
        if (CommonConstants.PLAT_FORM_TYPE_JD.equals(source)) {
            id = org.apache.commons.lang3.StringUtils.isNotEmpty(order.getSkuId()) ? order.getSkuId() : order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source)) {//淘宝
            id = order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)) {//天猫
            id = order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_1688.equals(source)) {
            id = order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(source)) {
            id = order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(source)) {
            id = order.getNumIid();
        } else if (CommonConstants.PLAT_FORM_TYPE_SMT.equals(source)) {
            id = order.getNumIid();
        }
        return String.format(url, id);
    }

    public static <T extends Order> List<Long> getSysSkuIds(List<T> orders) {
        List<Long> sysSkuIds = new ArrayList<Long>(orders.size());
        for (Order order : orders) {
            if (order.getSkuSysId() != null && order.getSkuSysId() > 0 && !sysSkuIds.contains(order.getSkuSysId())) {
                sysSkuIds.add(order.getSkuSysId());
            }
        }
        return sysSkuIds;
    }

    public static List<Long> getSysItemIds(List<Order> orders) {
        List<Long> sysItemIds = new ArrayList<Long>(orders.size());
        for (Order order : orders) {
            if (order.getItemSysId() != null && order.getItemSysId() > 0 && !sysItemIds.contains(order.getItemSysId())) {
                sysItemIds.add(order.getItemSysId());
            }
        }
        return sysItemIds;
    }

    public static void addItemMatchLog(Order order) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOperations().get(OpEnum.ITEM_RELATION_BIND_FAILURE))) {
            return;
        }
        if (order.isGift() && !order.isPlatformGift()) {
            return;
        }
        if (order.getItemSysId() != null && order.getItemSysId() > 0) {
            StringBuilder s = new StringBuilder();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOuterSkuId())) {
                s.append(order.getOuterSkuId());
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOuterIid())) {
                s.append(order.getOuterIid());
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSkuPropertiesName())) {
                s.append(order.getSkuPropertiesName());
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getTitle())) {
                s.append(order.getTitle());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSysOuterId())) {
                if (s.length() > 0) {
                    s.append("->");
                }
                s.append(order.getSysOuterId());
                setSuitTrace(s, order);
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSysSkuPropertiesName())) {
                if (s.length() > 0) {
                    s.append("->");
                }
                s.append(order.getSysSkuPropertiesName());
                setSuitTrace(s, order);
            }
            if (s.length() > 0) {
                order.getOperations().put(OpEnum.ITEM_RELATION_BIND_SUCCESS, s.toString());
            }
        }
    }

    public static String addItemUnmatchLog(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        StringBuilder buf = new StringBuilder();
        List<String> unmatchOrderValues = Lists.newArrayList();
        boolean matchByOuterId = false;
        for (Order order : orders) {
            if (org.apache.commons.lang3.StringUtils.isBlank(order.getOperations().get(OpEnum.ITEM_RELATION_BIND_FAILURE))) {
                continue;
            }

            if (order.isGift() && !order.isPlatformGift()) {
                continue;
            }

            if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOperations().get(OpEnum.ITEM_MATCH_BY_OUTERID))) {
                unmatchOrderValues.add(order.getOperations().get(OpEnum.ITEM_MATCH_BY_OUTERID));
                matchByOuterId = true;
            } else {
                unmatchOrderValues.add(order.getOperations().get(OpEnum.ITEM_MATCH_BY_RELATION));
            }

            if (org.apache.commons.lang3.StringUtils.isBlank(buf)) {
                buf.append(order.getOperations().get(OpEnum.ITEM_RELATION_BIND_FAILURE));
            } else {
                buf.append("," + order.getOperations().get(OpEnum.ITEM_RELATION_BIND_FAILURE));
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(buf.toString())) {
            String valueStr = unmatchOrderValues.stream().collect(Collectors.joining(","));
            buf.append(" ");
            if (matchByOuterId) {
                buf.append(OpEnum.ITEM_MATCH_BY_OUTERID.getName()).append("失败,").append("商家编码[").append(valueStr).append("]");
            } else {
                buf.append(OpEnum.ITEM_MATCH_BY_RELATION.getName()).append("失败,").append("平台id[").append(valueStr).append("]");
            }
        }

        return buf.toString();
    }

    private static void setSuitTrace(StringBuilder s, Order order) {
        if (order.getSuits() != null && !order.getSuits().isEmpty()) {
            s.append("(");
            for (int i = 0; i < order.getSuits().size(); i++) {
                if (i > 0) {
                    s.append("+");
                }
                Order son = order.getSuits().get(i);
                s.append(son.getNum() / order.getNum()).append("*").append(son.getSysOuterId());
            }
            s.append(")");
        }
    }

    public static String getSysOuterIdLog(Order order) {
        StringBuilder s = new StringBuilder();
        s.append(order.getSysOuterId());
        if (order.getSuits() != null && !order.getSuits().isEmpty()) {
            s.append("(");
            for (int i = 0; i < order.getSuits().size(); i++) {
                if (i > 0) {
                    s.append("+");
                }
                s.append(order.getSuits().get(i).getNum()).append("*").append(order.getSuits().get(i).getSysOuterId());
            }
            s.append(")");
        }
        return s.toString();
    }

    /**
     * 是否包含标志位
     *
     * @param o
     * @param v
     * @return
     */
    public static boolean isContainV(Order o, int v) {
        return o != null && o.getV() != null && (o.getV() | v) - o.getV() == 0;
    }

    /**
     * 是否是分销类型订单(包含供销订单/分销订单)
     *
     * @return
     */
    public static boolean isFxSource(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1;
    }


    /**
     * 是否是分销订单(仅包含分销订单)
     *
     * @return
     */
    public static boolean isFxOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1 && order.getBelongType() != null && order.getBelongType() == 1;
    }


    /**
     * 是否是供销订单(仅包含供销订单)
     *
     * @return
     */
    public static boolean isGxOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1 && order.getBelongType() != null && order.getBelongType() == 2;
    }

    /**
     * 是否是供销且分销订单(仅包含供销且分销订单)
     *
     * @return
     */
    public static boolean isGxAndFxOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1 && order.getBelongType() != null && order.getBelongType() == 3;
    }

    /**
     * 是否是分销或分销且供销订单(仅包含分销或分销且供销订单)
     *
     * @return
     */
    public static boolean isFxOrMixOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1 && order.getBelongType() != null && (order.getBelongType() == 1 || order.getBelongType() == 3);
    }

    /**
     * 是否是供销或分销且供销订单(仅包含供销或分销且供销订单)
     *
     * @return
     */
    public static boolean isGxOrMixOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 1 && order.getBelongType() != null && (order.getBelongType() == 2 || order.getBelongType() == 3);
    }

    /**
     * 是否是平台分销订单
     *
     * @return
     */
    public static boolean isPlatformFxOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 2 && order.getBelongType() != null && order.getBelongType() == 1;
    }

    public static boolean isAlibabaFxRoleOrder(Order order){
        return  isContainV(order, OrderConstant.V_IF_1688_FX_ROLE);
    }

    /**
     * 是否是奇门分销订单
     *
     * @return
     */
    public static boolean isQimenFxOrder(Order order) {
        return order.getConvertType() != null && order.getConvertType() == 3 && order.getBelongType() != null && order.getBelongType() == 2;
    }

    /**
     * 是否需要匹配destId的order
     *
     * @return
     */
    public static boolean needDestIdMatchOrder(Order order) {
        return (order.getIsCancelDistributorAttribute() == null || order.getIsCancelDistributorAttribute() == 0)
                && (Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus())) //待付款和待审核
                && (org.apache.commons.lang3.StringUtils.isEmpty(order.getRefundStatus()) || Order.NO_REFUND.equals(order.getRefundStatus()) || Order.REFUND_CLOSED.equals(order.getRefundStatus())) //没有退款状态订单
                && ((order.getDestId() == null || order.getDestId() <= 0) || OrderUtils.isGxOrder(order));
    }

    /**
     * 分销小站的
     * 1.直接解析： outerSkuId,outerIid
     * 2.用getFxTrueOuterId(order）兜底
     *
     * @param trade
     * @param order
     * @return
     */
    public static String getFxTrueOuterId(Trade trade, Order order) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSupplierOuterId())) {
            return order.getSupplierOuterId();
        }
        if (org.apache.commons.lang3.StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_FXXZ, trade.getSubSource())) {
            String outerId = getOuterId(order, org.apache.commons.lang3.StringUtils.defaultIfBlank(order.getOuterSkuId(), order.getOuterIid()));
            if (org.apache.commons.lang3.StringUtils.isBlank(outerId)) {
                return getFxTrueOuterId(order);
            }
            return outerId;
        }
        return getFxTrueOuterId(order);
    }

    public static String getFxTrueOuterId(Order order) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSupplierOuterId())) {
            return order.getSupplierOuterId();
        }
        if (order.getOrderExt() != null && org.apache.commons.lang3.StringUtils.isNotBlank(order.getOrderExt().getSupplierItemOuterId())) {
            return order.getOrderExt().getSupplierItemOuterId();
        } else if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            return getOuterId(order, order.getSysOuterId());
        } else {
            String f;
            if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getSysOuterId())) {
                f = order.getSysOuterId();
            } else {
                f = order.getOuterSkuId() != null ? order.getOuterSkuId() : order.getOuterIid();
            }
            return getOuterId(order, f);
        }
    }

    public static String getOuterId(Order order, String outerId) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(outerId) && isFxSource(order)) {
            int index = outerId.indexOf("##") > 0 ? outerId.indexOf("##") : outerId.indexOf("$$");
            if (index > 0 && index < outerId.length() - 2) {
                return outerId.substring(0, index);
            } else {
                return outerId;
            }
        } else {
            return outerId;
        }
    }

    public static String buildItemKey(Long sysItemId, Long sysSkuId) {
        sysSkuId = Optional.ofNullable(sysSkuId).orElse(0L);
        if (sysSkuId < 0) {
            sysSkuId = 0L;
        }
        return sysItemId + "_" + sysSkuId;
    }

    /**
     * 商品是否为其他ERP发货
     *
     * @param order
     * @return
     */
    public static boolean isOtherErpConsigned(Order order) {
        return TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && !Objects.equals(order.getSysConsigned(), 1);
    }

    /**
     * 是否为系统发货前退款
     *
     * @param order
     * @return
     */
    public static boolean isRefundedBeforeSysConsign(Order order) {
        return Order.REFUND_SUCCESS.equals(order.getRefundStatus()) && !Objects.equals(order.getSysConsigned(), 1);
    }

    public static boolean isAllGift(List<Order> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }

        for (Order order : orderList) {
            if (!order.isGift()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否全部为虚拟商品
     */
    public static boolean isAllVirtual(List<Order> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }

        for (Order order : orderList) {
            if (!order.isVirtual()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否是lazada FBL订单和二次销售的订单
     *
     * @param order
     * @return boolean
     * <AUTHOR>
     * @date 2022/3/29 上午10:47
     */
    public static boolean hasFblOrder(Order order) {
        if (!Objects.equals(CommonConstants.PLAT_FORM_TYPE_LAZADA, order.getSource())) {
            return false;
        }
        if (Objects.isNull(order.getOrderExt()) || Objects.isNull(order.getOrderExt().getCustomization())) {
            return false;
        }
        JSONObject customization = JSON.parseObject(order.getOrderExt().getCustomization());
        Integer isFbl = customization.getInteger("isFbl");
        Integer isReroute = customization.getInteger("isReroute");
        //lazada FBL订单和二次销售的订单不扣库存
        if (Objects.equals(1, isFbl) || Objects.equals(1, isReroute)) {
            return true;
        }
        return false;
    }

    public static Integer getSfFreeShipping(Order order) {
        if (!(Objects.equals(CommonConstants.PLAT_FORM_TYPE_FXG, order.getSource()) || Objects.equals(CommonConstants.PLAT_FORM_TYPE_SYS, order.getSource()))) {
            return null;
        }
        if (Objects.isNull(order.getOrderExt()) || Objects.isNull(order.getOrderExt().getCustomization())) {
            return null;
        }
        JSONObject customization = JSON.parseObject(order.getOrderExt().getCustomization());
        if (customization == null) {
            return null;
        }
        Integer sfFreeShipping = customization.getInteger("sf_free_shipping");
        if (Objects.equals(1, sfFreeShipping)) {
            return 1;
        }
        return null;
    }


    public static Integer getOriNum(Order order) {
        if (!(Objects.equals(CommonConstants.PLAT_FORM_TYPE_SHEIN, order.getSource()) || Objects.equals(CommonConstants.PLAT_FORM_TYPE_SYS, order.getSource()))) {
            return null;
        }
        if (Objects.isNull(order.getOrderExt()) || Objects.isNull(order.getOrderExt().getCustomization())) {
            return null;
        }
        JSONObject customization = JSON.parseObject(order.getOrderExt().getCustomization());
        if (customization == null) {
            return null;
        }
        try {
            return customization.getInteger("orderNum");
        } catch (Exception e) {
            return null;
        }
    }

    public static List<String> getBtasOrderCode(Order order) {
        if (order == null) {
            return Lists.newLinkedList();
        }
        return getBtasOrderCodeByOrderExt(order.getOrderExt());
    }

    public static List<String> getBtasOrderCodeByOrderExt(OrderExt orderExt) {
        if (orderExt == null || Strings.isNullOrEmpty(orderExt.getCustomization())) {
            return Lists.newLinkedList();
        }
        String customization = orderExt.getCustomization();
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(customization);
        } catch (Exception e) {
            return Lists.newLinkedList();
        }
        if (jsonObject == null) {
            return Lists.newArrayList();
        }
        JSONArray orderList = jsonObject.getJSONArray(CommonConstants.BTAS_ORDER_CODE);
        if (orderList == null) {
            return Lists.newArrayList();
        }
        List<String> result = orderList.toJavaList(String.class);
        return result;
    }

    public static void setBtasOrderCode(Order order, List<String> btasOrderCode) {
        if (order == null || order.getOrderExt() == null || CollectionUtils.isEmpty(btasOrderCode)) {
            return;
        }
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(order.getOrderExt().getCustomization());
        } catch (Exception e) {
            return;
        }
        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }
        jsonObject.put(CommonConstants.BTAS_ORDER_CODE, btasOrderCode);
        order.getOrderExt().setCustomization(JSONObject.toJSONString(jsonObject));
    }

    public static List<AbstractMap.SimpleEntry> getBtasPictureQualityResult(Order order) {
        List<AbstractMap.SimpleEntry> result = new ArrayList<>();
        if (order == null || order.getOrderExt() == null || Strings.isNullOrEmpty(order.getOrderExt().getCustomization())) {
            return result;
        }
        String customization = order.getOrderExt().getCustomization();
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(customization);
        } catch (Exception e) {
            return result;
        }
        if (jsonObject == null) {
            return result;
        }
        List<String> btasOrderCode = getBtasOrderCode(order);
        if (CollectionUtils.isEmpty(btasOrderCode)) {
            return result;
        }
        Map<String, String> resultMap = jsonObject.getObject(CommonConstants.BTAS_PICTURE_QUALITY_RESULT, Map.class);
        Map<String, String> objectMap = jsonObject.getObject(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, Map.class);
        if (MapUtils.isEmpty(resultMap)) {
            resultMap = new HashMap<>();
        }
        for (String code : btasOrderCode) {
            if (MapUtils.isNotEmpty(objectMap)) {
                String objState = objectMap.get(code);
                if (!Strings.isNullOrEmpty(objState) && BtasPictureQualityResultEnum.SELLER_SEND.descAndCode.equals(objState)) {
                    result.add(new AbstractMap.SimpleEntry<String, String>(code, BtasPictureQualityResultEnum.SELLER_SEND.descAndCode));
                    continue;
                }
            }
            String state = resultMap.get(code);
            if (Strings.isNullOrEmpty(state)) {
                result.add(new AbstractMap.SimpleEntry<String, String>(code, BtasPictureQualityResultEnum.UNDEFINED.descAndCode));
            } else if (state.equals(BtasPictureQualityResultEnum.SUCCESS.descAndCode)) {
                result.add(new AbstractMap.SimpleEntry<String, String>(code, BtasPictureQualityResultEnum.SUCCESS.descAndCode));
            } else if (state.equals(BtasPictureQualityResultEnum.FAIL.descAndCode)) {
                result.add(new AbstractMap.SimpleEntry<String, String>(code, BtasPictureQualityResultEnum.FAIL.descAndCode));
            } else {
                result.add(new AbstractMap.SimpleEntry<String, String>(code, BtasPictureQualityResultEnum.UNDEFINED.descAndCode));
            }
        }
        return result;
    }

    /**
     * 合并【数据库已有的】【接口拉取的】质检结果
     */
    public static void mergeBtasQualityResult(OrderExt dbOrderExt, OrderExt currentOrderExt) {
        String dbCustomization = dbOrderExt.getCustomization();
        JSONObject dbCustomizationJsonObj = JSONObject.parseObject(dbCustomization);
        if (dbCustomizationJsonObj == null) {
            dbCustomizationJsonObj = new JSONObject();
        }
        String customization = currentOrderExt.getCustomization();
        JSONObject currentCustomizationJsonObj = JSONObject.parseObject(customization);
        if (currentCustomizationJsonObj == null) {
            return;
        }
        //质检结果拿最新的，订单码不存在时也需要更新
        dbCustomizationJsonObj.put(CommonConstants.BTAS_PICTURE_QUALITY_RESULT, currentCustomizationJsonObj.get(CommonConstants.BTAS_PICTURE_QUALITY_RESULT));
        if (!dbCustomizationJsonObj.containsKey(CommonConstants.BTAS_ORDER_CODE) && currentCustomizationJsonObj.containsKey(CommonConstants.BTAS_ORDER_CODE)) {
            dbCustomizationJsonObj.put(CommonConstants.BTAS_ORDER_CODE, currentCustomizationJsonObj.get(CommonConstants.BTAS_ORDER_CODE));
        }
        currentOrderExt.setCustomization(dbCustomizationJsonObj.toJSONString());
    }

    public static void setBtasPictureQualityResult(Order order, String code, BtasPictureQualityResultEnum type) {
        if (Objects.isNull(type)) {
            throw new IllegalArgumentException("type不能未空！");
        }
        if (Objects.isNull(order)) {
            throw new IllegalArgumentException("order不能未空!");
        }
        if (Objects.isNull(order.getOrderExt())) {
            throw new IllegalArgumentException("order.orderExt不能未空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(order.getOrderExt().getCustomization());
        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }
        Map<String, String> resultMap = jsonObject.getObject(CommonConstants.BTAS_PICTURE_QUALITY_RESULT, Map.class);
        if (MapUtils.isEmpty(resultMap)) {
            resultMap = new HashMap<>();
        }
        resultMap.put(code, type.descAndCode);
        jsonObject.remove(CommonConstants.BTAS_PICTURE_QUALITY_RESULT);
        jsonObject.put(CommonConstants.BTAS_PICTURE_QUALITY_RESULT, resultMap);
        //如果是商家已送检，另外再拿个字段再存起来一份，避免字段覆盖
        if (BtasPictureQualityResultEnum.SELLER_SEND.equals(type)) {
            Map<String, String> objectQualityMap = jsonObject.getObject(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, Map.class);
            if (MapUtils.isEmpty(objectQualityMap)) {
                objectQualityMap = new HashMap<>();
            }
            objectQualityMap.put(code, type.descAndCode);
            jsonObject.remove(CommonConstants.BTAS_OBJECT_QUALITY_STATUS);
            jsonObject.put(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, objectQualityMap);
        }
        //如果质检成功，或者质检失败，需要检查，这个订单码是不是商家已送检了，如果是，则将商家已送检去掉
        if (BtasPictureQualityResultEnum.SUCCESS.equals(type) || BtasPictureQualityResultEnum.FAIL.equals(type)) {
            Map<String, String> objectQualityMap = jsonObject.getObject(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, Map.class);
            if (!MapUtils.isEmpty(objectQualityMap)) {
                objectQualityMap.remove(code);
            }
        }
        order.getOrderExt().setCustomization(JSONObject.toJSONString(jsonObject));
    }

    public enum BtasPictureQualityResultEnum {
        UNDEFINED(0, "待质检"), SUCCESS(1, "质检成功"), FAIL(2, "质检失败"), SELLER_SEND(3, "商家已送检");
        public int platformCode = 0;
        public String descAndCode = "";

        BtasPictureQualityResultEnum(int code, String descAndCode) {
            this.descAndCode = descAndCode;
        }
    }

    public static void fillIsPick(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        toFullOrderList(orders, false).parallelStream().filter(order -> !order.isGift()).forEach(OrderUtils::fillIsPick);
    }

    public static void fillIsPick(Order order) {
        if (Objects.isNull(order)) {
            return;
        }

        //赠品不根据商品标签填充isPick
        if (order.isGift()) {
            return;
        }

        if (!checkIsPickWhiteList(order.getCompanyId())) {
            return;
        }

        if (CollectionUtils.isEmpty(order.getOrderItemTags())) {
            order.setIsPick(2);
            return;
        }
        // 1000000003  免验商品
        boolean isLaisserPasser = order.getOrderItemTags().parallelStream()
                .filter(tag -> Objects.nonNull(tag) && tag.getEnableStatus() == 1)
                .anyMatch(tag -> tag.getTypeId() == 0 && tag.getItemTagId() == 1000000003L);
        //1000000004 免拣商品
        boolean isExemptChoose = order.getOrderItemTags().parallelStream()
                .filter(tag -> Objects.nonNull(tag) && tag.getEnableStatus() == 1)
                .anyMatch(tag -> tag.getTypeId() == 0 && tag.getItemTagId() == 1000000004L);

        // 0：不拣选不验货；1：拣选不验货；2：拣选验货；3：验货不拣选
        if (isLaisserPasser && isExemptChoose) {
            order.setIsPick(0);
            return;
        }
        if (isLaisserPasser) {
            order.setIsPick(1);
            return;
        }
        if (isExemptChoose) {
            order.setIsPick(3);
            return;
        }
        order.setIsPick(2);
    }

    public static Boolean checkIsPickWhiteList(Staff staff) {
        if (staff == null) {
            return false;
        }
        return checkIsPickWhiteList(staff.getCompanyId());
    }

    public static Boolean checkIsPickWhiteList(Long companyId) {
        TradeItemIsPickConfig config = ConfigHolder.TRADE_ITEM_IS_PICK_CONFIG;
        return config != null && (DiamondUtils.hasValue(config.getOpenCompanyIds(), companyId) || DiamondUtils.hasValue(config.getOpenCompanyIds(), DiamondUtils.ALL_COMPANY_ID));
    }

    /**
     * 是否免拣
     */
    public static boolean notPick(Order order) {
        boolean notPick = order.getIsPick() == 0 || order.getIsPick() == 3;
        boolean giftNotPick = order.getGiftNum() != null && order.getGiftNum() > 0 && notPick;
        return BooleanUtils.isTrue(checkIsPickWhiteList(order.getCompanyId())) ? notPick : giftNotPick;
    }

    /**
     * 是否免验
     */
    public static boolean notCheck(Order order) {
        boolean notCheck = order.getIsPick() == 0 || order.getIsPick() == 1;
        boolean giftNotCheck = order.getGiftNum() != null && order.getGiftNum() > 0 && notCheck;
        return BooleanUtils.isTrue(checkIsPickWhiteList(order.getCompanyId())) ? notCheck : giftNotCheck;
    }

    /**
     * 是否免拣免验
     */
    public static boolean notPickNotCheck(Order order) {
        boolean notPickCheck = order.getIsPick() == 0;
        boolean giftNotPickCheck = order.getGiftNum() != null && order.getGiftNum() > 0 && notPickCheck;
        return BooleanUtils.isTrue(checkIsPickWhiteList(order.getCompanyId())) ? notPickCheck : giftNotPickCheck;
    }

    /**
     * 免拣免验标记
     */
    public static Integer buildPickCheckFlag(Order order) {
        if (order.getGiftNum() != null && order.getGiftNum() > 0) {
            order.setPickCheckFlag(order.getIsPick());
        } else {
            order.setPickCheckFlag(2);
        }
        if (BooleanUtils.isTrue(checkIsPickWhiteList(order.getCompanyId()))) {
            order.setPickCheckFlag(order.getIsPick());
        }
        return order.getPickCheckFlag();
    }

    /**
     * 是否是退款数量的单
     *
     * @param order
     * @return
     */
    public static <T extends Order> boolean isRefundNum(T order) {
        return OrderExtUtils.isRefundNumOrderExt(order.getOrderExt());
    }
    //

    public static boolean isSysGift(Order order) {
        if (Objects.isNull(order)) {
            return false;
        }

        boolean gift = order.isGift();
        if (!gift) {
            return false;
        }
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            return true;
        }
        return false;
    }

    /**
     * 是否虚拟商品/无需发货
     * 仓内作业不需要再考虑
     * https://gykj.yuque.com/entavv/xb9xi5/trm9ebhy2b99agss
     */
    public static boolean isVirtualOrNonConsign(Order order) {
        return isNonConsign(order) || order.isVirtual();
    }

    /**
     * 是否无需发货
     * 仓内作业不需要再考虑
     */
    public static boolean isNonConsign(Order order) {
        return BooleanUtils.isTrue(order.ifNonConsign());
    }


    /**
     * 订单同步链路中获取系统单的origin
     *
     * @param staff
     * @param order
     * @param tir
     * @return
     */
    public static Order getSyncOriginOrder(Staff staff, Order order, TradeImportResult tir) {
        if (Objects.equals(order.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)) {
            // 系统单的origin数据从sysOriginOrderMap 取,自带的origin 不是系统查出的数据
            Order origin = tir.sysOriginOrderMap.get(order.getId());
            if (origin == null) {
                Logs.debug(String.format("系统单 id=%s sid=%s oid=%s 的origin数据不对 ", order.getId(), order.getSid(), order.getOid()));
            }
            return origin;
        }
        return order.getOrigin();
    }

    /**
     * 兼容其他模块的，后期会去掉，其他模块后期不要使用
     *
     * @param order
     * @return
     */
    @Deprecated
    public static Set<Integer> parseExcept(Order order) {
        return parseExcept(new Staff().setCompanyId(order.getCompanyId()), order);
    }

    /**
     * 获取平台原始商家编码
     *
     * @param order
     * @return
     */
    public static String getPlatOuterId(Order order) {
        String platOuterId = null;
        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            platOuterId = order.getOuterSkuId();
            if (org.apache.commons.lang3.StringUtils.isBlank(platOuterId)) {
                platOuterId = order.getOuterId();
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(platOuterId)) {
                platOuterId = order.getOuterIid();
            }
        }
        return platOuterId;
    }


    public static List<Order> toOrderList(List<TbOrder> tbOrders) {
        return new ArrayList<>(tbOrders);
    }

    public static void emptyStockByWarehouseChange(Staff staff, TradeConfigContext configContext, Trade trade) {
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            if(TradeStatusUtils.isWaitSellerSend(order.getSysStatus())){
                TradeAuditUtils.fillOrderStockMsg(trade, order);
                if (order.isSuit(false)) {
                    for (Order suit : order.getSuits()) {
                        TradeAuditUtils.fillOrderStockMsg(trade, suit);
                        if(TradeStockUtils.needApplyStock(configContext, suit)){
                            OrderExceptUtils.setStockStatus(staff, suit, Trade.STOCK_STATUS_EMPTY);
                        }
                    }
                } else {
                    if(TradeStockUtils.needApplyStock(configContext, order)){
                        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
                    }
                }
            }
        }
    }

    /**
     * 判断一个子订单是否为指定的类型（套件、组合、加工）
     *
     * @param orderType order的type
     * @param combineId order的combineId
     * @param targeType 目标类型 Order#TypeOfCombineOrder、Order#TypeOfGroupOrder、Order#TypeOfProcessOrder
     * @param single    是否单品
     */
    public static boolean orderIsTargeType(Integer orderType, Long combineId, int targeType, boolean single) {
        if (orderType == null) {
            orderType = 0;
        }
        if (combineId == null) {
            combineId = 0L;
        }
        return orderType == targeType && (single ? combineId > 0 : combineId == 0);
    }


    public static int getOrderType(Order order) {
        if (notPick(order)) return 1;
        if (isNonConsign(order)) return 2;
        if (order.isVirtual()) return 3;
        return 0;
    }

    /**
     * 是否淘宝有效的部分退款  （如果 未退款，退款关闭，商家拒绝  则返回false）
     * @param order
     * @return
     */
    public static boolean isTbValidPartRefund(Order order){
        // 每个订单只处理自己的异常，所以系统单也需要处理
        if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(order.getSource()) || CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(order.getSource())) {
            if(order.getOrderExt() == null){
                return false;
            }
            if(Order.NO_REFUND.equals(order.getRefundStatus())
                || Order.REFUND_CLOSED.equals(order.getRefundStatus())
                || Order.REFUND_SELLER_REFUSE_BUYER.equals(order.getRefundStatus())
            ){
                return false;
            }
            if("1".equals((String) order.getOrderExt().get("partRefund"))){
                return true;
            }
        }
        return false;
    }

}

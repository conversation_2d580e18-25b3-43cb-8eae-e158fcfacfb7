package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.trades.Order;

/**
 * Created by CXW on 16/6/7.
 */
public class OrderCopier<S extends Order, T extends Order> implements AttrCopier<S, T> {

    @Override
    public T copy(S source, T target) {
        target.setEnableStatus(source.getEnableStatus());
        target.setCompanyId(source.getCompanyId());
        target.setSource(source.getSource());
        target.setUserId(source.getUserId());
        target.setWarehouseId(source.getWarehouseId());
        target.setTaobaoId(source.getTaobaoId());
        target.setSid(source.getSid());
        target.setOldSid(source.getOldSid());
        target.setTid(source.getTid());
        target.setId(source.getId());
        target.setOldId(source.getOldId());
        target.setOid(source.getOid());
        target.setSoid(source.getSoid());
        target.setStatus(source.getStatus());
        target.setOldStatus(source.getOldStatus());
        target.setSysStatus(source.getSysStatus());
        target.setOldSysStatus(source.getOldSysStatus());
        target.setRefundId(source.getRefundId());
        target.setRefundStatus(source.getRefundStatus());
        target.setStockStatus(source.getStockStatus());
        target.setStockNum(source.getStockNum());
        target.setOldStockNum(source.getOldStockNum());
        target.setSubStock(source.getSubStock());
        target.setNum(source.getNum());
        target.setOldNum(source.getOldNum());
        target.setUnit(source.getUnit());
        target.setNumIid(source.getNumIid());
        target.setSkuId(source.getSkuId());
        target.setItemSysId(source.getItemSysId());
        target.setSkuSysId(source.getSkuSysId());
        target.setOuterIid(source.getOuterIid());
        target.setOuterId(source.getOuterId());
        target.setOuterSkuId(source.getOuterSkuId());
        target.setSysOuterId(source.getSysOuterId());
        target.setSkuPropertiesName(source.getSkuPropertiesName());
        target.setSysSkuPropertiesName(source.getSysSkuPropertiesName());
        target.setSysSkuPropertiesAlias(source.getSysSkuPropertiesAlias());
        target.setTitle(source.getTitle());
        target.setSysTitle(source.getSysTitle());
        target.setShortTitle(source.getShortTitle());
        target.setPrice(source.getPrice());
        target.setPayment(source.getPayment());
        target.setPayAmount(source.getPayAmount());
        target.setTotalFee(source.getTotalFee());
        target.setDiscountFee(source.getDiscountFee());
        target.setAdjustFee(source.getAdjustFee());
        target.setCost(source.getCost());
        target.setSaleFee(source.getSaleFee());
        target.setSalePrice(source.getSalePrice());
        target.setNetWeight(source.getNetWeight());
        target.setPicPath(source.getPicPath());
        target.setCreated(source.getCreated());
        target.setPayTime(source.getPayTime());
        target.setConsignTime(source.getConsignTime());
        target.setPtConsignTime(source.getPtConsignTime());
        target.setEndTime(source.getEndTime());
        target.setModified(source.getModified());
        target.setUpdTime(source.getUpdTime());
        target.setGiftNum(source.getGiftNum());
        target.setType(source.getType());
        target.setSysPicPath(source.getSysPicPath());
        target.setSysSkuRemark(source.getSysSkuRemark());
        target.setSysItemRemark(source.getSysItemRemark());
        target.setUnit(source.getUnit());
        target.setSkuUnit(source.getSkuUnit());
        target.setCombineId(source.getCombineId());
        target.setItemChanged(source.getItemChanged());
        target.setWarehouseId(source.getWarehouseId());
        target.setIdentCode(source.getIdentCode());
        target.setOperable(source.isOperable());
        target.setIsVirtual(source.getIsVirtual());
        target.setNonConsign(source.getNonConsign());
        target.setVolume(source.getVolume());
        target.setDiscountRate(source.getDiscountRate());
        target.setCustomGiftType(source.getCustomGiftType());
        target.setBelongType(source.getBelongType());
        target.setDestId(source.getDestId());
        target.setConvertType(source.getConvertType());
        target.setSourceId(source.getSourceId());
        target.setV(source.getV());
        target.setRelationChanged(source.getRelationChanged());
        target.setScalping(source.getScalping());
        target.setBelongSid(source.getBelongSid());
        target.setEstimateConTime(source.getEstimateConTime());
        target.setSysConsigned(source.getSysConsigned());
        target.setCids(source.getCids());
        target.setNeedMarkItemChanged(source.isNeedMarkItemChanged());
        target.setOrigin(source);
        if (source.isPlatformPresell() || source.isFullPresell()) {
            target.setIsPresell(source.getIsPresell());
        }
        target.setIsPick(source.getIsPick());
        // 原始订单的异常
        if (source.getExceptData() != null && !source.isKeepOldExceptData()) {
            target.setExceptData(source.getExceptData().copier());
        }else{
            // 保持原对象不变
            target.setExceptData(source.getExceptData());
        }
        return target;
    }
}

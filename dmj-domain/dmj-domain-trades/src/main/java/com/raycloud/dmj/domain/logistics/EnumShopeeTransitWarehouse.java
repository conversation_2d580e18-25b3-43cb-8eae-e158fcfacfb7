package com.raycloud.dmj.domain.logistics;

public enum EnumShopeeTransitWarehouse {

    TWS01("TWS01", "Shopee深圳仓", "Shopee Shenzhen Transit Warehouse"),
    ECP01("ECP01", "Shopee万色仓 (包含：上海仓/泉州仓)", "Shopee Wanse Transit Warehouse (Including: Shanghai/Yiwu/Quanzhou)"),
    TWS02("TWS02", "Shopee香港仓", "Shopee Hong Kong Transit Warehouse"),
    TWS03("TWS03", "Shopee韩国仓", "Shopee Korea Transit Warehouse"),
    TWS04("TWS04", "Shopee台湾仓", "Shopee Taiwan Transit Warehouse"),
    TWS05("TWS05", "Shopee印尼仓", "Shopee Indonesia Transit Warehouse"),
    TWS06("TWS06", "Shopee马来仓", "Shopee Malaysia Transit Warehouse"),
    LGSFM40("LGS-FM40", "Lazada东莞仓", "Lazada 东莞 Transit Warehouse"),
    LGSFM41("LGS-FM41", "Lazada义乌仓", "Lazada yiwu Transit Warehouse"),
    LGSFM48("LGS-FM48", "Lazada泉州仓", "Lazada quanzhou Transit Warehouse"),
    TWS07("TWS07", "Shopee日本仓", "Shopee Japan Transit Warehouse"),
    ECP04("ECP04", "Shopee义乌仓", "Shopee Wanse Transit Warehouse (Including: yiwu"),
    ECP03("ECP03", "Shopee福建仓", "Shopee Wanse Transit Warehouse (Including: yiwu"),
    ECP01_SHANGHAI("ECP01_SHANGHAI", "万色仓-福建", "Shopee Wanse Transit Warehouse (Including: yiwu"),
    ECP01_QUANZHOU("ECP01_QUANZHOU", "万色仓-泉州", "Shopee Wanse Transit Warehouse (Including: yiwu"),
    ECP01_YIWU("ECP01_YIWU", "万色仓-义乌", "Shopee Wanse Transit Warehouse (Including: yiwu");
    private String warehouseId;

    private String warehouseNameCN;

    private String warehouseNameEN;

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseNameCN() {
        return warehouseNameCN;
    }

    public void setWarehouseNameCN(String warehouseNameCN) {
        this.warehouseNameCN = warehouseNameCN;
    }

    public String getWarehouseNameEN() {
        return warehouseNameEN;
    }

    public void setWarehouseNameEN(String warehouseNameEN) {
        this.warehouseNameEN = warehouseNameEN;
    }

    EnumShopeeTransitWarehouse(String warehouseId, String warehouseNameCN, String warehouseNameEN) {
        this.warehouseId = warehouseId;
        this.warehouseNameCN = warehouseNameCN;
        this.warehouseNameEN = warehouseNameEN;
    }

    public static String getWarehouseNameCNById(String warehouseId, String warehouseAddress) {
        for (EnumShopeeTransitWarehouse value : EnumShopeeTransitWarehouse.values()) {
            if(value.warehouseId.equals(warehouseId)) {
                if("ECP01".equals(warehouseId)) {
                    if(warehouseAddress.contains("Shanghai")) {
                        return "Shopee上海仓";
                    }
                    if(warehouseAddress.contains("Yiwu")) {
                        return "Shopee义乌仓";
                    }
                    if(warehouseAddress.contains("Quanzhou")) {
                        return "Shopee泉州仓";
                    }
                }
                return value.warehouseNameCN;
            }
        }
        return null;
    }
}

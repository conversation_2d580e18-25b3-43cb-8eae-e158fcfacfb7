package com.raycloud.dmj.domain.performance;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * Created by windy26205 on 19/10/18.
 */
@Table(name = "trade_pick", routerKey = "tradeDbNo")
public class TradePick extends Model implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

   // @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 订单系统Id
     */
    private Long sid;

    /**
     * 领单人Id
     */
 //   @Column(name = "staff_id", type = Types.BIGINT)
    private Long staffId;

 //   @Column(name = "staff_name", type = Types.VARCHAR)
    private String staffName;

    /**
     * 更新事件
     */
  //  @Column(name = "modify_time", type = Types.TIMESTAMP)
    private Timestamp modifyTime;

    /**
     * 领单时间
     */
  //  @Column(name = "picked_time", type = Types.TIMESTAMP)
    private Timestamp pickedTime;

   // @Column(name="wave_id", type = Types.BIGINT)
    private Long waveId;

    /**
     * 信息json展示
     */
    private String infoJson;

    public String getInfoJson() {
        return infoJson;
    }

    public void setInfoJson(String infoJson) {
        this.infoJson = infoJson;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Timestamp getPickedTime() {
        return pickedTime;
    }

    public void setPickedTime(Timestamp pickedTime) {
        this.pickedTime = pickedTime;
    }



    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }
}

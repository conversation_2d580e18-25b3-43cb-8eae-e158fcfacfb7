package com.raycloud.dmj.domain.trades.search;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description <pre>
 *  原则上 这里只能包含trade本表内的原始数据
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-31
 */
@Data
public class BaseTrade implements Serializable {

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 店铺id
     */
    private Long userId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 运单号
     */
    private String outSid;

    private String Source;

    private String subSource;


    private String sysStatus;
    /**
     * 订单的平台状态
     */
    private String status;

    /**
     * 各平台状态映射后的统一状态
     */
    private String unifiedStatus;

    /**
     * 库存状态
     */
    private String stockStatus;

    /**
     * 订单转化类型 0表示正常订单，1表示分销系统 2表示平台分销订单
     */
    private Integer convertType;

    /**
     * 订单属于soure or dest ,0表示正常订单，1表示source，2表示dest
     */
    private Integer belongType;

    /**
     * 订单来源于哪里 分销系统里记为分销商id（companyId）
     */
    private Long sourceId;

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    private Long destId;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 是否为预售订单：0:正常订单，1：系统预售（预售规则没有开启自动识别）；2预售转正常；3：系统预售(预售规则开启了自动识别)
     */
    private Integer isPresell;


    private Integer isExcep;

    /**
     * 是否发货上传到平台
     */
    private Integer isUpload;

    /**
     * 是否在系统发货 1 是，0 否,2其他ERP发货
     */
    private Integer sysConsigned;

    /**
     * 是否退款订单
     */
    private Integer isRefund;

    /**
     * 是否挂起订单
     */
    private Integer isHalt;

    /**
     * 是否作废
     */
    private Integer isCancel;


    private Integer enableStatus;

    /**
     * 订单是否打包
     */
    private Integer isPackage;

    /**
     * 是否称重
     */
    private Integer isWeigh;


    private Date payTime;

    /**
     * 如果为合单。则对应合单后主单的sid。否则为0
     */
    private Long mergeSid;

    /**
     * 如果为拆单。则对应拆单的主单的sid。否则为0
     */
    private Long splitSid;

    /**
     * 波次id
     */
    private Long waveId;

    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 数据库最后更新时间
     */
    private Date updTime;

    /**
     * 发货时间
     */
    private Date consignTime;

    /**
     * 快递单打印时间
     */
    private Date expressPrintTime;

}

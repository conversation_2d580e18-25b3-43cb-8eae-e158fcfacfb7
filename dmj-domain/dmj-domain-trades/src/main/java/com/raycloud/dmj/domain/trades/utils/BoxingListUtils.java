package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.BoxingListItem;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ruanyg on 16/12/5.
 */
public class BoxingListUtils {

    public static final String BOX_NO_PREFIX = "BN";

    public static void setBn(List<BoxingListItem> boxingListItems) {
        Integer maxBn = 0;
        List<BoxingListItem> blmNeedBn = new ArrayList<BoxingListItem>();
        for(BoxingListItem item : boxingListItems) {
            if(StringUtils.isBlank(item.getBoxNo())) {
                blmNeedBn.add(item);
            } else {
                Integer bn = Integer.parseInt(item.getBoxNo().substring(2));
                if(bn > maxBn) {
                    maxBn = bn;
                }
            }
        }
        setBn(blmNeedBn, maxBn);
    }

    private static void setBn(List<BoxingListItem> blmNeedBn, Integer maxBn) {
        for(BoxingListItem item : blmNeedBn) {
            item.setBoxNo(BOX_NO_PREFIX + String.format("%03d", ++maxBn));
        }
    }
}

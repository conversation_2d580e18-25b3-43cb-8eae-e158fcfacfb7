package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

public class CalculateResult implements Serializable {

    private static final long serialVersionUID = -7171243248796330540L;

    /**
     * 订单的sid
     */
    private Long sid;

    /**
     * 金额
     */
    private String count;

    /**
     * 无法计算费用原因
     */
    private String reason;

    /**
     * 是否错误
     */
    private boolean isError;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public boolean isError() {
        return isError;
    }

    public void setError(boolean isError) {
        this.isError = isError;
    }

    public static CalculateResult buildResult(Long sid, String count) {
        CalculateResult result = new CalculateResult();
        result.setSid(sid);
        result.setCount(count);
        result.setError(false);
        return result;
    }

    public static CalculateResult buildErrorResult(Long sid, String reason) {
        CalculateResult result = new CalculateResult();
        result.setSid(sid);
        result.setReason(reason);
        result.setError(true);
        return result;
    }
}

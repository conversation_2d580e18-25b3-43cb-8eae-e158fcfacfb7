package com.raycloud.dmj.domain.platform.response;

import com.raycloud.dmj.domain.platform.ErpPlatformErrorMapping;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description 分页查询结果，含总数
 * <AUTHOR>
 * @date 2023/5/9 10:03
 */
@Data
public class ErrorMappingQueryPageResponse implements Serializable {
    private static final long serialVersionUID = 8451714319073555082L;

    private Long total;

    private List<ErpPlatformErrorMapping> rows;
}

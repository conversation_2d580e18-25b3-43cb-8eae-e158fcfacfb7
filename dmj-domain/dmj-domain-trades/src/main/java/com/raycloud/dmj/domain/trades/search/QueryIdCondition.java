package com.raycloud.dmj.domain.trades.search;

import com.raycloud.dmj.domain.trades.TradeQueryParams;

/**
 * @Description <pre>
 *  queryId 映射的查询条件
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-06
 */
public class QueryIdCondition {

    /**
     * 查询基本卡盘
     * @see com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext
     */
    private Long queryId;

    /**
     * 是否异常订单
     */
    private Integer isExcep;

    /**
     * 是否作废订单 0未作废, 1已作废
     */
    private Integer isCancel;

    private String[] sysStatus;

    /**
     * 是否需要检查店铺用户是否激活（是否需要过滤掉店铺已停用的订单）
     */
    private Boolean checkActive;

    /**
     * 是否查询预售锁定订单
     * 1 只查询预售锁定订单 isPresell=1
     * 0 不查询预售锁定订单（查询非预售订单、预售解锁订单）isPresell != 1
     * 2 只查询预售解锁订单 isPresell = 2
     * 3 查询系统预售订单 isPresell = 3
     * null 都查询
     */
    private Integer isPresell;

    /**
     * 订单是否分配了快递公司
     */
    private Boolean containExpress;


    /**
     * 出库单查询 1 出库单查询， 0 非出库单查询，null 都查询
     * null 出库单非出库单都查询
     */
    private Integer isOutstock;


    /**
     * 快递单打印状态，0未打印，1已打印
     */
    private Integer expressStatus;

    /**
     * 异常状态 (对应系统异常中的英文名称)
     * @see TradeQueryParams#SYS_EXCEP_SETS
     */
    private String[] exceptionStatus;

    /**
     * 是否挂起订单
     */
    private Integer isHalt;

    public Integer getIsExcep() {
        return isExcep;
    }

    public QueryIdCondition setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
        return this;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public QueryIdCondition setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
        return this;
    }

    public String[] getSysStatus() {
        return sysStatus;
    }

    public QueryIdCondition setSysStatus(String ... sysStatus) {
        this.sysStatus = sysStatus;
        return this;
    }

    public Boolean getCheckActive() {
        return checkActive;
    }

    public QueryIdCondition setCheckActive(Boolean checkActive) {
        this.checkActive = checkActive;
        return this;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public QueryIdCondition setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
        return this;
    }

    public Boolean getContainExpress() {
        return containExpress;
    }

    public QueryIdCondition setContainExpress(Boolean containExpress) {
        this.containExpress = containExpress;
        return this;
    }

    public Integer getIsOutstock() {
        return isOutstock;
    }

    public QueryIdCondition setIsOutstock(Integer isOutstock) {
        this.isOutstock = isOutstock;
        return this;
    }

    public Integer getExpressStatus() {
        return expressStatus;
    }

    public QueryIdCondition setExpressStatus(Integer expressStatus) {
        this.expressStatus = expressStatus;
        return this;
    }

    public String[] getExceptionStatus() {
        return exceptionStatus;
    }

    public QueryIdCondition setExceptionStatus(String ... exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
        return this;
    }

    public Integer getIsHalt() {
        return isHalt;
    }

    public QueryIdCondition setIsHalt(Integer isHalt) {
        this.isHalt = isHalt;
        return this;
    }

    public Long getQueryId() {
        return queryId;
    }

    public QueryIdCondition setQueryId(Long queryId) {
        this.queryId = queryId;
        return this;
    }
}

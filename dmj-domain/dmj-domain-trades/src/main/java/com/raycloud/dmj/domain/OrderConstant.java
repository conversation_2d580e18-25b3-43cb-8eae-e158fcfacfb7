package com.raycloud.dmj.domain;

/**
 * <AUTHOR>
 */
public interface OrderConstant {

    /**
     * 1 商品变更
     * 2 未知
     * 4 未知
     * ItemChanged
     */
     int V_CANCEL_ADDRESS_CHANGE_EXCEPT = 1;



    /**
     *
     * com.raycloud.dmj.domain.trades.Order#isSuiteChanged()
     * 8 套件修改异常交易目前使用这个方法
     */

    int V_IF_SUITE_CHANGED = 1 << 3;


    /**
     *
     * 8已经被用，从第四位开始。
     *
     * 1048576 商品快麦通代发
     */
    int V_KMT_DF = 1 << 20;

    /**
     * 2097152 供销商品执行过修改
     */
    int V_GX_ITEM_EDIT = 1 << 21;

    /**
     * 4194304 供销商品执行过修改,且后续业务不需要关联修改分销订单
     */
    int V_GX_ITEM_EDIT_NOT_REL_FX = 1 << 22;

    /**
     *8388608 是否1688分销小店分销角色
     */
    int V_IF_1688_FX_ROLE = 1 << 23;

    //供销改商品
    int GX_ITEM_UPDATE = 1 << 24;
}

package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * @program: erp-core
 * @description:
 * @author: fuchen
 * @create: 2020-04-23 10:20
 **/
public class OrderInfoVo implements Serializable, Comparable<OrderInfoVo> {

    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 日期
     */
    private String date;
    /**
     * 充值单量
     */
    private Long topupOrderAmount;
    /**
     * 消耗单量
     */
    private Long consumeOrderAmount;
    /**
     * 剩余单量
     */
    private Long remainOrderAmount;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Long getTopupOrderAmount() {
        return topupOrderAmount;
    }

    public void setTopupOrderAmount(Long topupOrderAmount) {
        this.topupOrderAmount = topupOrderAmount;
    }

    public Long getConsumeOrderAmount() {
        return consumeOrderAmount;
    }

    public void setConsumeOrderAmount(Long consumeOrderAmount) {
        this.consumeOrderAmount = consumeOrderAmount;
    }

    public Long getRemainOrderAmount() {
        return remainOrderAmount;
    }

    public void setRemainOrderAmount(Long remainOrderAmount) {
        this.remainOrderAmount = remainOrderAmount;
    }

    @Override
    public int compareTo(OrderInfoVo o) {
        int res = this.getDate().compareTo(o.getDate());
        if (res > 0) {
            return 1;
        } else if (res < 0) {
            return -1;
        } else {
            return 0;
        }
    }
}

package com.raycloud.dmj.domain.trades.tradepay;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chen<PERSON><PERSON><PERSON>
 * @Date: 2020/3/19 2:33 下午
 */
public class TradePayVO implements Serializable {

    private static final long serialVersionUID = 5271441529131647594L;

    /**
     * @mbg.generated
     * 表字段: id
     */
    private Long id;

    /**
     * @mbg.generated
     * 公司id
     * 表字段: company_id
     */
    private Long companyId;

    /**
     * @mbg.generated
     * 店铺id
     * 表字段: user_id
     */
    private Long userId;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * @mbg.generated
     * 内部id
     * 表字段: sid
     */
    private Long sid;

    /**
     * 内部单号
     */
    private Long shortId;

    /**
     * @mbg.generated
     * 平台订单id
     * 表字段: tid
     */
    private String tid;

    /**
     * @mbg.generated
     * 支付单号
     * 表字段: pay_id
     */
    private String payId;

    /**
     * @mbg.generated
     * 支付单状态，1:待审核，2:已生效，3已作废
     * 表字段: order_status
     */
    private Integer orderStatus;

    /**
     * @mbg.generated
     * 来源 1为手工创建，2为平台创建
     * 表字段: source
     */
    private Integer source;

    /**
     * @mbg.generated
     * 支付金额
     * 表字段: pay_fee
     */
    private String payFee;

    /**
     * @mbg.generated
     * 支付方式
     * 表字段: pay_type
     */
    private Integer payType;

    /**
     * @mbg.generated
     * 支付日期
     * 表字段: pay_date
     */
    private Date payDate;
    /**
     * 订单支付时间
     */
    private Date payTime;

    /**
     * @mbg.generated
     * 收款账号
     * 表字段: account_no
     */
    private String accountNo;

    /**
     * @mbg.generated
     * 买家账号
     * 表字段: buyer_account_no
     */
    private String buyerAccountNo;

    /**
     * @mbg.generated
     * 操作人
     * 表字段: operator
     */
    private String operator;
    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * @mbg.generated
     * 创建时间
     * 表字段: created
     */
    private Date created;

    /**
     * @mbg.generated
     * 修改时间
     * 表字段: modified
     */
    private Date modified;

    /**
     * 支付类型
     */
    private String payTypeName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getPayFee() {
        return payFee;
    }

    public void setPayFee(String payFee) {
        this.payFee = payFee;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBuyerAccountNo() {
        return buyerAccountNo;
    }

    public void setBuyerAccountNo(String buyerAccountNo) {
        this.buyerAccountNo = buyerAccountNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

}

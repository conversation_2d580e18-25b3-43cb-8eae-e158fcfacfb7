package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.util.Date;

/**
 * 拆单发货记录表
 * <AUTHOR>
 *
 */
@Table(name = "split_order_consign_record")
@Data
public class SplitOrderConsignRecord extends Model {

	/**
	 *
	 */
	private static final long serialVersionUID = 6459766459250898565L;

	/**
	 * 主键id
	 */
	private Long id;
	
	/**
	 * 平台子订单编号
	 */
	private Long oid;

	/**
	 * 平台订单号
	 */
	private String tid;
	
	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 修改时间
	 */
	private Date updated;
	
}

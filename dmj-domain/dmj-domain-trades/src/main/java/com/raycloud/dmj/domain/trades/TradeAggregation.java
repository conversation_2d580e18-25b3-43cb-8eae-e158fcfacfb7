package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.history.TradeHistory;
import com.raycloud.dmj.domain.trade.invoice.TradeInvoice;
import com.raycloud.dmj.domain.trade.label.TradeLabel;
import com.raycloud.dmj.domain.trade.address.TradeAddress;
import com.raycloud.dmj.domain.trades.payment.TradePayment;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.domain.TradeExcept;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description 订单模型的聚合（重构过程中的中间类）
 * @Date 2022/2/21 11:46
 * @Created 杨恒
 */
@Setter
@Getter
public abstract class TradeAggregation extends TradeBase {

    private static final long serialVersionUID = -8271110960718753346L;

    //************************************************* TradeAddress start *************************************************
    /**
     * 订单地址模型
     */
    private TradeAddress tradeAddress;

    private String receiverCountry;

    private String receiverState;

    private String receiverCity;

    private String receiverDistrict;

    private String receiverStreet;

    private String receiverDoorplate;

    private String receiverAddress;

    private String receiverZip;

    private Integer addressType;

    private String receiverName;

    private String receiverMobile;

    private String mobileTail;

    private String receiverPhone;

    private String addressMd5;

    private String buyerNick;

    private String oriBuyerNick;

    private String openUid;

    private String receiverNameIndex;

    private String receiverMobileIndex;

    private String receiverAddressIndex;

    private String desensitizationReceiverName;

    private String desensitizationReceiverMobile;

    private String desensitizationReceiverAddress;
    //************************************************* TradeAddress end *************************************************


    //************************************************* TradePayment start *************************************************
    /**
     * 订单金额模型
     */
    private TradePayment tradePayment;

    private String payment;

    private String acPayment;

    private String oldAcPayment;

    /**
     * 订单差额=系统实付金额-平台实收金额 区间值
     */
    private Double paymentDiff;

    /**
     * TradePayment#cost
     */
    private Double cost;

    private String totalFee;

    private String discountFee;

    private Double theoryPostFee;

    private String postFee;

    private String actualPostFee;

    private String taxFee;

    private String adjustFee;

    private String saleFee;

    private String salePrice;

    private Double packmaCost;

    private Double tradePurchaseAmount;

    private Double platformPaymentAmount;

    private Double manualPaymentAmount;

    private Double grossProfit;

    private String payAmount;

    private String platformDiscountFee;

    private String grossProfitRate;

    private String selfBuiltDepositAmount;
    private String selfBuiltPaymentReceivable;

    //************************************************* TradePayment end *************************************************

    //************************************************* TradeExcept start *************************************************

    @Deprecated
    private Integer isExcep;
    @Deprecated
    private Integer oldIsExcep;
    // 自定异常id
    @Deprecated
    private String exceptIds;
    @Deprecated
    private String oldExceptIds;
    @Deprecated
    private Long excep;
    @Deprecated
    private List<String> exceptNames;
    @Deprecated
    private Integer itemExcep; // erp-core
    @Deprecated
    private Integer riskExcep;//风控异常 erp-core

    /**
     * pdd缺货处理
     */
    @Deprecated
    private Integer pddStockOut; // erp-core

    /**
     * 二进制标志字段,从低位到高位依次表示
     * 2^0 = 1. 已取消平台换地址异常（反审核时会取消该标记）
     * 2^1 = 2. 地址已处理
     * 2^2 = 4. 上传失败标记
     * 2^3 = 8. 已取消单价异常标签
     * 2^5 = 32 快卖通现场自提
     * 2^13 = 8192 快卖通小程序
     */
    @Deprecated
    private Long v;//erp-core


    // 挂起
    @Deprecated
    private Integer isHalt;
    // 退款 (多平台需要用到，后面可以不持久化到数据库，但是需要透传到交易)
    private Long isRefund;
    @Deprecated
    private Set<String> exceptions;
    @Deprecated
    private String exceptMemo;
    // 地址变更异常
    @Deprecated
    private Integer addressChanged;
    // 黑名单
    @Deprecated
    private Integer blackBuyerNick;
    // 卖家 旗帜，备注变更 0 未修改，1：修改过，2：留言备注异常
    @Deprecated
    private Integer sellerMemoUpdate;
    // 信息缺失
    @Deprecated
    private Integer isLostMsg;
    // 快递不可达
    @Deprecated
    private Integer unattainable;

    /**
     *  trade 对象复制时必须先复制这两个对象
     */
    /**
     * 订单异常数据，包含增删改的异常id信息，计算异常的时候，传递给异常服务
     */
    @JSONField(serialize = false)
    private ExceptData exceptData;

    /**
     * 合单的所有异常,(非合单只包含自己的) 订单的异常数据,计算异常的时候，传递给异常服务 ExceptData 寸的是个trade的exceptData 对象，都会改变
     */
    @JSONField(serialize = false)
    private Map<Long, ExceptData> subTradeExceptDatas;

    private Integer autoSignExcept=0;


    private Map<OpEnum, Set<String>> operationSetLogs = new LinkedHashMap<OpEnum, Set<String>>();
    /**
     * 合单旧的异常保存容器
     */
    private Set<String> subTradeOldExceptEnglishes=new HashSet<>();


    //************************************************* TradeExcept end *************************************************

    /**
     * 订单发票信息
     */
    private TradeInvoice tradeInvoice;


    /**
     * trade 非系统赠品的商品数量
     */
    private Integer oldNotSysGiftNum;
    /**
     * 返回复制的ExceptData 对象
     *
     * @return
     */
    @JSONField(serialize = false)
    public ExceptData getExceptDataCopier() {
        if (this.getExceptData() == null) {
            return null;
        }
        return this.getExceptData().copier();
    }

    /**
     * 返回复制的OrderExceptDatas对象
     *
     * @return
     */
    @JSONField(serialize = false)
    public Map<Long, ExceptData> getSubExceptDatasCopier(){
        if(this.getSubTradeExceptDatas()==null){
            return new ConcurrentHashMap<>();
        }
        Map<Long,ExceptData> copier=new ConcurrentHashMap<>();
        Set<Map.Entry<Long, ExceptData>> entries = this.getSubTradeExceptDatas().entrySet();
        for(Map.Entry<Long, ExceptData> entry:entries){
            if (entry.getKey() == null || entry.getValue() == null) {
                continue;
            }
            copier.put(entry.getKey(), entry.getValue().copier());
        }
        return copier;
    }

    @Deprecated
    public void addV(int v) {
        this.v = this.v != null ? (this.v | v) : (long) v;
    }
    @Deprecated
    public void removeV(int v) {
        if (this.v != null && (this.v | v) - this.v == 0) {
            this.v = this.v - v;
        }
    }
    @Deprecated
    public boolean isAddressChangeCancelled() {
        return v != null && (v | 1) - v == 0;
    }

    @Deprecated
    public Long getV() {
        return v;
    }
    @Deprecated
    public void setV(Long v) {
        this.v = v;
    }


    public boolean isAddressHandled() {
        return v != null && (v | 2) - v == 0;
    }

    public boolean getIfKmtDf() {
        return v != null && (v | 16) - v == 0;
    }

    public boolean getIfKmtSelfPick() {
        return v != null && (v | 32) - v == 0;
    }

    /**
     * 是否自动匹配业务员
     *
     * @return
     */
    public boolean getIfAutoMatchSalesman() {
        return v != null && (v | TradeConstants.V_IF_AUTO_MATCH_SALESMAN) - v == 0;
    }

    /**
     * 是否在tradeUpdate中更行异常，默认更新
     */
    private boolean autoUpdateExcept=true;
    private Long oldIsRefund;

    private Integer oldIsLostMsg;

    /**
     * 订单部分信息变化的记录
     */
    private List<InfoChangeLog> infoChangeLogs;

    private TradeSalesman tradeSalesman;

    private TradeHistory tradeHistory;

    /**
     * 标签
     */
    @Deprecated
    private Map<Long, TradeLabel> tradeLabelMap;

    public Map<Long, TradeLabel> getTradeLabelMap() {
        if (tradeLabelMap == null) {
            tradeLabelMap = new HashMap<>();
        }
        return tradeLabelMap;
    }

    /**
     * 订单标签
     */
    private List<TradeLabel> tradeLabels;
    /**
     * 订单异常
     */
    private List<TradeExcept> tradeExcepts;

}

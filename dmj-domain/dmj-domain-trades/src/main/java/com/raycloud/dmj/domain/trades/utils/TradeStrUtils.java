package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单内字符串处理工具类
 *
 * @author: qingfeng.cxb
 * @create: 2020-05-27 17:19
 */
public class TradeStrUtils {


    /**
     * 过滤订单收货人的相关信息
     */
    public static void filterTradesSpecialCharacters(List<Trade> trades) {
        for (Trade trade : trades) {
            filterTradeSpecialCharacters(trade);
        }
    }

    /**
     * 过滤订单收货人的相关信息
     */
    public static void filterTradeSpecialCharacters(Trade trade) {
        //若过滤后姓名长度小于2个汉字，则自动在内容后方填充“表情”两个字
        String name = replaceAllUtf8mb4(trade.getReceiverName(), true);
        trade.setReceiverName(name);
        // 若收件详细地址中有表情等特殊字符：自动将详细地址中的特殊字符过滤。
        trade.setReceiverAddress(replaceAllUtf8mb4(trade.getReceiverAddress(), false));
        // 为电话和固化过滤空格
        trade.setReceiverMobile(trade.getReceiverMobile() == null ? trade.getReceiverMobile() : trade.getReceiverMobile().replace(" ", ""));
        trade.setReceiverPhone(trade.getReceiverPhone() == null ? trade.getReceiverPhone() : trade.getReceiverPhone().replace(" ", ""));
    }

    /**
     * 找出字符串里超过两个字节的字符
     */
    public static List<String> findUtf8mb4List(String str) {
        List<String> result = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isEmpty(str)) {
            return result;
        }
        final int LAST_BMP = 0xFFFF;
        for (int i = 0; i < str.length(); i++) {
            if (str.codePointAt(i) >= LAST_BMP) {
                result.add(new StringBuilder().appendCodePoint(str.codePointAt(i)).toString());
            }
        }
        return result;
    }

    /**
     * 处理字符串里的表情
     */
    public static String replaceAllUtf8mb4(String str, boolean needFill) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(str)) {
            return str;
        }
        List<String> utf8mb4 = findUtf8mb4List(str);
        if (CollectionUtils.isNotEmpty(utf8mb4)) {
            for (String s : utf8mb4) {
                str = str.replaceAll(s, "");
            }
            if (needFill && str.length() < 2) {
                str = str + "表情";
            }
        }
        return str;
    }
}

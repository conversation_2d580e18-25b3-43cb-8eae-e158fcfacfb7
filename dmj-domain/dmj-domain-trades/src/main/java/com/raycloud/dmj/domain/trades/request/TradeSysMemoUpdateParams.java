package com.raycloud.dmj.domain.trades.request;

import com.raycloud.dmj.domain.trades.TradeControllerParams;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_06_02 11:21
 */
@Setter
@Getter
public class TradeSysMemoUpdateParams implements Serializable {

    private static final long serialVersionUID = 8119921917316649524L;

    TradeControllerParams params = null;

    String sids = null;

    String updateSysMemo = null;

    String fastSysMemoId = null;
    /**
     * 0:通过sids进行更新
     * 1:通过params进行全量更新
     */
    Integer updateType = 0;

    /**
     * 供销订单修改，是否通知分销订单
     */
    Boolean notifyFx;
}

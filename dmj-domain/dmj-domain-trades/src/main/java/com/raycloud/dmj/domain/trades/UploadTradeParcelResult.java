package com.raycloud.dmj.domain.trades;

import java.util.ArrayList;
import java.util.List;

public class UploadTradeParcelResult {

    private List<TradeParcel> successList = new ArrayList<>();
    private List<TradeParcel> failList = new ArrayList<>();
    private String successMessage ;
    public List<TradeParcel> getSuccessList() {
        return successList;
    }

    public void setSuccessList(List<TradeParcel> successList) {
        this.successList = successList;
    }

    public List<TradeParcel> getFailList() {
        return failList;
    }

    public void setFailList(List<TradeParcel> failList) {
        this.failList = failList;
    }

    public String getSuccessMessage() {
        return successMessage;
    }

    public void setSuccessMessage(String successMessage) {
        this.successMessage = successMessage;
    }
}

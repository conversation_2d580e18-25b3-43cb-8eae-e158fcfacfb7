package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;

public class JdCommonUtils {
    public static boolean isJdUserTrade(Staff staff, Trade trade){
        User user = staff.getUserIdMap().get(trade.getUserId());
        if(user!=null && CommonConstants.PLAT_FORM_TYPE_JD.equals(user.getSource())){
            return true;
        }
        return false;
    }
}

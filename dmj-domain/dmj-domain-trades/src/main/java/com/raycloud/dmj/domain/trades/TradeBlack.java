package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * Created by yang<PERSON><PERSON> on 17/6/27.
 * 黑名单配置
 */
@Table(name = "trade_black", routerKey = "tradeBlackDbNo")
public class TradeBlack extends Model {

    private static final long serialVersionUID = -3275055834445142681L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 黑名单
     */
    private String buyerNick;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 是否删除
     */
    private Integer enableStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

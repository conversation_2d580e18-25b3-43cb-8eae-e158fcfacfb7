package com.raycloud.dmj.domain.aoxiang;

import java.io.Serializable;

/**
 * 翱象仓作业事件监听传递消息实体类
 */
public class AxMessage implements Serializable {

    private static final long serialVersionUID = -6001502720146583253L;

    private Long sid;

    private Long expressCompanyId;

    private String outSid;

    public AxMessage() {
    }

    public AxMessage(Long sid, Long expressCompanyId, String outSid) {
        this.sid = sid;
        this.expressCompanyId = expressCompanyId;
        this.outSid = outSid;
    }

    @Override
    public String toString() {
        return "AxMessage{" +
                "sid=" + sid +
                ", expressCompanyId=" + expressCompanyId +
                ", outSid='" + outSid + '\'' +
                '}';
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getExpressCompanyId() {
        return expressCompanyId;
    }

    public void setExpressCompanyId(Long expressCompanyId) {
        this.expressCompanyId = expressCompanyId;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

}

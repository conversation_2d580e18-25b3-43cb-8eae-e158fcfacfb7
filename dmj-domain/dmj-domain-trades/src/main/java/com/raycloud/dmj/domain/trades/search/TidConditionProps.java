package com.raycloud.dmj.domain.trades.search;

/**
 * @Description <pre>
 * tid条件查询 拓展信息
 * </pre>
 * <AUTHOR>
 * @Date 2025-06-13
 */
public enum TidConditionProps {

    /** tid in (A) */
    STRICT(0,"仅查指定tid本身(默认)"),

    /**
     * <pre>
     * 售后产生的订单 tid为 {原平台单号}-{数字}
     * 复制产生的订单 tid为 {原平台单号}-{随机码}
     * tid in (A) OR tid like 'A-%'
     * </pre>
     */
    REF_TRADES(1,"查询关联的售后订单及复制订单,不能超1000个tid"),

    /**
     * <pre>
     * 对应奇门的上游原始平台单号 或供销单对应分销单的tid
     * 会先转换为系统的tid再进行查询,转换未成功的仍按普通tid查询
     *
     * 假设一组tid条件 (A,B),其中A对应的是奇门单tidA1,B为正常系统TID,且同时指定了REF_TRADES
     * tid in (A1,B) OR tid like 'B-%'
     * </pre>
     */
    SOURCE_TID(2,"查询关联上游平台单号"),
    ;


    private int key;
    private String desc;

    TidConditionProps(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}

package com.raycloud.dmj.domain.trades;


import com.raycloud.dmj.domain.account.Staff;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeHotItemContext implements Serializable {
    private static final long serialVersionUID = -4090334596303688973L;

    private Staff staff;

    private List<TradeHotItem> tradeHotItems;

    private List<TbOrder> orders;

    /**
     * 爆款类型
     */
    private Integer hotItemType;

}

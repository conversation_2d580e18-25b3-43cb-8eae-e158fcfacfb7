package com.raycloud.dmj.domain.trades.request;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
public class TradeUpdateTimeoutActionTimeRequest implements Serializable {

    private static final long serialVersionUID = -4594176970183071985L;

    private Set<Long> sids;

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    private String timeoutActionTime;

    /**
     * -1 提前
     * 1 延后
     */
    private int beforeOrAfter = 0;

    /**
     * 0 - 366
     */
    private int days = 0;

    /**
     * 0 - 24
     */
    private int hours = 0;

    public void check() {
        boolean checked = this.getSids() != null && !this.getSids().isEmpty();
        checked = checked && Objects.equals(this.beforeOrAfter, -1) || Objects.equals(this.beforeOrAfter, 0) || Objects.equals(this.beforeOrAfter, 1);
        checked = checked && this.days >= 0 && this.days < 366;
        checked = checked && this.hours >= 0 && this.hours < 24;
        if (this.timeoutActionTime == null) {
            checked = checked && (this.beforeOrAfter == -1 || this.beforeOrAfter == 1);
            checked = checked && (this.days != 0 || this.hours != 0);
        }
        Preconditions.checkArgument(checked, "请求数据非法");
    }
}

package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 扫码验货扫码信息
 */
public class TradePackScanInfo implements Serializable {

    private static final long serialVersionUID = 4696426338558981179L;

    private Long orderId;

    /**
     * 扫码验货的编码
     */
    private String scanCode;

    /**
     * 扫码的类型
     * 1-普通商家编码
     * 2-唯一码/三段码
     * 3-多码
     * 99-强制验货
     */
    private Integer codeType;

    private Integer num;
    /**
     * 扫描后商家编码，后置打印时用的
     */
    private String outerId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getScanCode() {
        return scanCode;
    }

    public void setScanCode(String scanCode) {
        this.scanCode = scanCode;
    }

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public enum ScanCodeType {
        NORMAL(1),
        UNIQUE_CODE(2),
        MULTI_CODE(3),;

        private Integer type;

        ScanCodeType(Integer type) {
            this.type = type;
        }

        public Integer getType() {
            return type;
        }
    }

    public static TradePackScanInfo buildTradePackScanInfo(String outerId, Long orderId, Integer num, Integer codeType, String scanCode) {
        TradePackScanInfo tradePackScanInfo = new TradePackScanInfo();
        tradePackScanInfo.setCodeType(codeType);
        tradePackScanInfo.setNum(num);
        tradePackScanInfo.setOuterId(outerId);
        tradePackScanInfo.setOrderId(orderId);
        tradePackScanInfo.setScanCode(scanCode);
        return tradePackScanInfo;
    }
}

package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.trades.Order;

import com.raycloud.dmj.domain.trades.payment.util.PaymentLogBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

/**
 * @description: 商品金额不联动;改商品时，trade实付金额不变；增/删商品时，trade实付金额跟着变化（优惠金额不变）
 * @author: pxh
 * @create: 2021-07-29 17:59
 **/
public class PaymentNoLinkStrategy implements IPaymentLinkStrategy {

    protected final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public PaymentLinkRespDTO insertOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        Order order = paymentLinkReqDTO.getOrder();

        if (order.getForceUpdPayment() !=null && order.getForceUpdPayment()) {
            if (logger.isDebugEnabled()) {
                PaymentLogBuilder logBuilder = new PaymentLogBuilder(order.getCompanyId(),order.getSid())
                        .append("开启商品金额不联动配置").append("forceUpdPayment",order.getForceUpdPayment()).append("商品[").appendOrderIdentify(order).append("] 实付金额取传入值 payment:", order.getPayment());
                logger.debug(logBuilder.toString());
            }
        }else {
            if (StringUtils.isNotBlank(order.getOriginPayment())) {
                String payment = order.getPayment();
                order.setPayment(order.getOriginPayment());
                if (logger.isDebugEnabled()) {
                    PaymentLogBuilder logBuilder = new PaymentLogBuilder(order.getCompanyId(),order.getSid())
                            .append("开启商品金额不联动配置,商品[").appendOrderIdentify(order).appendChanged(" 还原为原实付金额 payment", payment, order.getPayment());
                    logger.debug(logBuilder.toString());
                }
            }
        }
        return commonPaymentLink(order);
    }


    /**
     * 改商品时，trade实付金额不变
     *
     * @param: order
     * @return:
     */
    @Override
    public PaymentLinkRespDTO updateOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return insertOrder(paymentLinkReqDTO);
    }

    @Override
    public PaymentLinkRespDTO deleteOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return insertOrder(paymentLinkReqDTO);
    }

    @Override
    public boolean support(OpenLinkConfigEnum openLinkConfigEnum) {
        return OpenLinkConfigEnum.ORDER_PAYMENT_NO_LINK_CHANGE_TRADE_PAYMENT.equals(openLinkConfigEnum);
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.enums.UploadRecordMsgEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> HuangFuJun
 * @Date : 2022/12/1 5:21 PM
 * @From : erp-core
 */
public class UploadRecordUtils {

    public static Object getMsgByKey(String key, String msg) {
        Map<String, Object> map = new HashMap<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(msg)) {
            try {
                map = JSON.parseObject(msg, Map.class);
            } catch (Exception e) {
                Logs.error("转换uploadRecord中扩展配置出错，msg:" + msg, e);
            }
        }
        return map.get(key);
    }

    public static String updateMsg(String key, Object value, String msg) {
        Map<String, Object> map = null;
        if (StringUtils.isBlank(msg)) {
            map = new HashMap<>();
        } else {
            map = JSON.parseObject(msg, Map.class);
        }
        map.put(key, value);
        msg = JSONObject.toJSONString(map);

        return msg;
    }

    /**
     * 更新CLUE_ID
     * @param value 线索ID
     * @param msg record 拓展字段 msg
     * @return
     */
    public static String updateClueId(Object value, String msg) {
        return UploadRecordUtils.updateMsg(UploadRecordMsgEnum.CLUE_ID.getKey(), value, msg);
    }
}

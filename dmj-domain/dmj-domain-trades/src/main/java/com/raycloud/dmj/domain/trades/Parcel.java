package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 快递包裹
 *
 * <AUTHOR>
 */
@Table(name = "parcel")
public class Parcel extends Model {

    private static final long serialVersionUID = 5591512599705564279L;

    private Long id;

    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    private String source;

    @Column(name = "order_id", type = Types.BIGINT)
    private Long orderId;

    private Long sid;

    private String tid;

    private String oid;

    @Column(name = "pack_id", type = Types.VARCHAR)
    private String packId;

    private String detail;

    private Date created;

    private Date updated;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPackId() {
        return packId;
    }

    public void setPackId(String packId) {
        this.packId = packId;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    public static Parcel build(User user, Order order) {
        return build(user, order, null);
    }
    public static Parcel build(User user, Order order, String packId) {
        Parcel parcel = order.getParcel();
        if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())){
            if(null == parcel) {//放心购有判断 order.getParcel
                return null;
            }
        }
        if(parcel == null){
            parcel = new Parcel();
        }
        parcel.setCompanyId(user.getCompanyId());
        parcel.setUserId(user.getId());
        parcel.setSource(user.getSource());
        parcel.setOrderId(order.getId());
        parcel.setSid(order.getSid());
        parcel.setTid(order.getTid());
        parcel.setOid(order.getOid().toString());
        if(StringUtils.isNotBlank(packId)){
            parcel.setPackId(packId);
        }
        return parcel;
    }

    public static List<Parcel> buildList(User user, List<Order> orderList){
        return buildList(user, orderList, null);
    }
    public static List<Parcel> buildList(User user, List<Order> orderList, String packId) {
        List<Parcel> parcelList = new ArrayList<>(orderList.size());
        orderList.forEach(order -> {
            Parcel build = build(user, order, packId);
            if (null != build) {
                parcelList.add(build);
            }
        });
        return parcelList;
    }

    @Override
    public String toString() {
        return "Parcel{" +
                "id=" + id +
                ", companyId=" + companyId +
                ", userId=" + userId +
                ", source='" + source + '\'' +
                ", orderId=" + orderId +
                ", sid=" + sid +
                ", tid='" + tid + '\'' +
                ", oid='" + oid + '\'' +
                ", packId='" + packId + '\'' +
                ", detail='" + detail + '\'' +
                ", created=" + created +
                ", updated=" + updated +
                '}';
    }
}

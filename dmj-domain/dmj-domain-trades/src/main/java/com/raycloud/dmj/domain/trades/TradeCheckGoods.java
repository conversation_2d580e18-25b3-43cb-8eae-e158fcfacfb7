package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单验货登记表
 *
 * <AUTHOR>
 * @date 2018/11/07 10:32
 */
@Table(name = "trade_check_goods")
public class TradeCheckGoods extends Model implements Serializable {

    /**
     * 验货登记类型 拣选号
     */
    public static final String CHECK_TYPE_PICKING_CODE = "PICKING_CODE";

    /**
     * 验货登记类型 运单号
     */
    public static final String CHECK_TYPE_OUTSID = "OUTSID";

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 类型 1-一单多包
     */
    private Integer type;

    /**
     * 验货人员
     */
    private Long staffId;

    /**
     * 验货人员
     */
    private String staffName;

    /**
     * 验货类型
     */
    private String checkType;

    /**
     * 有效标记
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 商品种类数
     */
    private Integer itemKindNum;

    /**
     * 商品数
     */
    private Integer itemNum;

    /**
     * 订单商品明细
     */
    private String itemSkuInfo;

    /**
     * 订单商品属性
     */
    private String itemSkuLabel;

    /**
     * 订单波次Id
     */
    private Long waveId;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return this.companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return this.sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getStaffId() {
        return this.staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return this.staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getCheckType() {
        return this.checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public Integer getEnableStatus() {
        return this.enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return this.created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return this.modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(Integer itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public Integer getItemNum() {
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public String getItemSkuInfo() {
        return itemSkuInfo;
    }

    public void setItemSkuInfo(String itemSkuInfo) {
        this.itemSkuInfo = itemSkuInfo;
    }

    public String getItemSkuLabel() {
        return itemSkuLabel;
    }

    public void setItemSkuLabel(String itemSkuLabel) {
        this.itemSkuLabel = itemSkuLabel;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }
}

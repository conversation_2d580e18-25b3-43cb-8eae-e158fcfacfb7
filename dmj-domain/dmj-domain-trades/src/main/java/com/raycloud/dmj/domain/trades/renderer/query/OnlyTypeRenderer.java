package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class OnlyTypeRenderer implements IFieldValueRenderer {

    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "仅包含-所有";
        }
        if (Objects.equals(originValue,1)) {
            return "仅包含-任意";
        }
        if (Objects.equals(originValue,2)) {
            return "同时包含";
        }
        return String.valueOf(originValue);
    }
}

package com.raycloud.dmj.domain.trades.utils;

import org.apache.commons.beanutils.BeanComparator;
import org.apache.commons.collections.ComparatorUtils;
import org.apache.commons.collections.comparators.ComparableComparator;

import java.util.*;

/**
 * Created by yangheng on 17/4/18.
 * 排序的工具类
 */
public class SortUtils {
    /**
     * 根据fieldName对list排序
     *
     * @param list      需要排序的集合
     * @param fieldName 根据对象的哪个关键字排序
     * @param asc       是否升序
     */
    public static <T> void sort(List<T> list, String fieldName, boolean asc) {
        if (list == null || list.size() <= 1) {
            return;
        }
        Comparator<T> mycmp = ComparableComparator.getInstance();
        mycmp = ComparatorUtils.nullLowComparator(mycmp); // 允许为null，不过业务上T一般不会为null
        if (!asc) {
            mycmp = ComparatorUtils.reversedComparator(mycmp); // 逆序
        }
        Collections.sort(list, new BeanComparator(fieldName, mycmp));
    }

    public static <T> void deleteDuplicate(List<T> list) {
        Map<T, Integer> map = new LinkedHashMap<T, Integer>(list.size());
        Iterator<T> iterator = list.iterator();
        while (iterator.hasNext()) {
            T t = iterator.next();
            Integer c = map.get(t);
            if (c != null) {//重复的元素
                iterator.remove();
            }
            map.put(t, 1);
        }
    }
}

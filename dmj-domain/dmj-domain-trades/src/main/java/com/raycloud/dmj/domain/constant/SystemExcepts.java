package com.raycloud.dmj.domain.constant;

import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.except.constant.ExceptConstantOld;
import com.raycloud.dmj.except.enums.ExceptEnum;

import java.util.*;

import static com.raycloud.dmj.domain.tag.TradeTag.*;

/**
 * @description: SystemExcepts
 * @author: pureyang
 * @create: 2021/06/16 16:35
 */
public class SystemExcepts {

    public static final TradeTag EXCEPT_PARTY3_WAREHOUSE = buildSystemExcept(2000000000L, "三方仓操作异常");
    public static final TradeTag EXCEPT_PARTY3_WAREHOUSE_UNCREATE = buildSystemExcept(2000000001L, "三方仓取消发货单异常");
    public static final TradeTag EXCEPT_PARTY3_WAREHOUSE_RECREATE = buildSystemExcept(2000000002L, "三方仓重建发货单异常");
    public static final TradeTag EXCEPT_PARTY3_WAREHOUSE_CREATE = buildSystemExcept(2000000003L, "三方仓创建发货单异常");
    public static final TradeTag EXCEPT_PARTY3_WAREHOUSE_UNATTAINABLE = buildSystemExcept(2000000004L, "三方仓快递停发异常");
    public static final TradeTag EXCEPT_QIMEN_TRADE_INTERCEPT_SUCCESS=buildSystemExcept(2000000005L, "奇门订单拦截成功");
    public static final TradeTag EXCEPT_QIMEN_TRADE_INTERCEPT_FAILED=buildSystemExcept(2000000006L, "奇门订单拦截失败");
    public static final TradeTag EXCEPT_UNIQUE_CODE_CLOSE = buildSystemExcept(3000000001L, "唯一码扫描关闭异常");
    public static final TradeTag SHIP_BOX_SPLIT_ORDER = buildSystemExcept(3000000002L, "未装箱部分发货订单");
    public static final TradeTag UNATTAINABLE = buildSystemExcept(ExceptEnum.UNATTAINABLE.getId(), ExceptEnum.UNATTAINABLE.getChinese());
    public static final TradeTag INSUFFICIENT = buildSystemExcept(ExceptEnum.INSUFFICIENT.getId(), ExceptEnum.INSUFFICIENT.getChinese());
    /**
     * 分销暂停发货
     */
    public static final TradeTag FX_STOP_DELIVER = buildSystemExcept(ExceptEnum.FX_STOP_DELIVER_EXCEPT.getOldExceptEnum().getOldIdx(), ExceptEnum.FX_STOP_DELIVER_EXCEPT.getChinese());

    //  public static final TradeTag FINANCE_REJECT_EXCEPT = buildSystemExcept(ExceptEnum.FINANCE_REJECT_EXCEPT.getId(), ExceptEnum.FINANCE_REJECT_EXCEPT.getChinese(), ExceptEnum.FINANCE_REJECT_EXCEPT.getChinese());


    public static final Map<Long, TradeTag> SYSTEM_EXCEPT_MAP = new LinkedHashMap<>();


    static {
        SYSTEM_EXCEPT_MAP.put(EXCEPT_PARTY3_WAREHOUSE_UNCREATE.getId(), EXCEPT_PARTY3_WAREHOUSE_UNCREATE);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_PARTY3_WAREHOUSE_RECREATE.getId(), EXCEPT_PARTY3_WAREHOUSE_RECREATE);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_PARTY3_WAREHOUSE_CREATE.getId(), EXCEPT_PARTY3_WAREHOUSE_CREATE);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_UNIQUE_CODE_CLOSE.getId(), EXCEPT_UNIQUE_CODE_CLOSE);
        SYSTEM_EXCEPT_MAP.put(SHIP_BOX_SPLIT_ORDER.getId(), SHIP_BOX_SPLIT_ORDER);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_PARTY3_WAREHOUSE_UNATTAINABLE.getId(), EXCEPT_PARTY3_WAREHOUSE_UNATTAINABLE);
        SYSTEM_EXCEPT_MAP.put(UNATTAINABLE.getId(), UNATTAINABLE);
      //  SYSTEM_EXCEPT_MAP.put(FINANCE_REJECT_EXCEPT.getId(), FINANCE_REJECT_EXCEPT);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_QIMEN_TRADE_INTERCEPT_SUCCESS.getId(), EXCEPT_QIMEN_TRADE_INTERCEPT_SUCCESS);
        SYSTEM_EXCEPT_MAP.put(EXCEPT_QIMEN_TRADE_INTERCEPT_FAILED.getId(), EXCEPT_QIMEN_TRADE_INTERCEPT_FAILED);
        SYSTEM_EXCEPT_MAP.put(INSUFFICIENT.getId(), INSUFFICIENT);
        SYSTEM_EXCEPT_MAP.put(FX_STOP_DELIVER.getId(), FX_STOP_DELIVER);

    }

    public static List<TradeTag> getSystemExcepts() {
        return new ArrayList<>(SYSTEM_EXCEPT_MAP.values());
    }

    public static Map<Long,TradeTag> getSystemExceptMap(){
        return new HashMap<>(SYSTEM_EXCEPT_MAP);
    }

    public static Map<Long,TradeTag> getSystemExceptMap(Integer ignoreUnattainable,Integer contailInsufficient){
        Map<Long, TradeTag> map = getSystemExceptMap();
        // 默认不反回
        map.remove(SystemExcepts.INSUFFICIENT.getId());
        map.remove(SystemExcepts.UNATTAINABLE.getId());
        if(Objects.equals(1,ignoreUnattainable)){
            map.put(SystemExcepts.UNATTAINABLE.getId(),UNATTAINABLE);
        }
        if(Objects.equals(1,contailInsufficient)){
            map.put(SystemExcepts.INSUFFICIENT.getId(),INSUFFICIENT);
        }
        return map;
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 桉数量拆单上下文
 *
 * <AUTHOR>
 */
public class SplitNumContextData {

    private String[] oids;

    private String[] tids;

    private Map<Long, Order> originPlatOrderMapByOid = new HashMap<>();

    private List<Order> currentOrderList = new ArrayList<>();

    private Map<String, List<Order>> orderMapGroupByOid = new HashMap<>();

    private Map<Long, List<Order>> suitSubOrderMapGroupByOrderId = new HashMap<>();

    private Map<Long, Order> originSuitSelfOrderByOrderId = new HashMap<>();

    private Map<String, List<SplitUploadRecord>> splitUploadRecordMapGroupByOid = new HashMap<>();

    private List<SplitUploadRecord> existSplitUploadRecordList = new ArrayList<>();

    private List<SplitUploadRecord> currentSplitUploadRecordList = new ArrayList<>();

    private List<SplitUploadRecord> needInsertSplitUploadRecordList = new ArrayList<>();

    private List<SplitUploadRecord> needUpdateSplitUploadRecordList = new ArrayList<>();

    private Set<Long> suitOrderUploadSet = new HashSet<>();

    public Map<Long, Order> getOriginPlatOrderMapByOid() {
        return originPlatOrderMapByOid;
    }

    public String[] getTids() {
        return tids;
    }

    public void setTids(String[] tids) {
        this.tids = tids;
    }

    public String[] getOids() {
        return oids;
    }

    public void setOids(String[] oids) {
        this.oids = oids;
    }

    public Map<Long, Order> getOriginSuitSelfOrderByOrderId() {
        return originSuitSelfOrderByOrderId;
    }

    public List<Order> getCurrentOrderList() {
        return currentOrderList;
    }

    public void setCurrentOrderList(List<Order> currentOrderList) {
        this.currentOrderList = currentOrderList;
    }

    public Map<String, List<Order>> getOrderMapGroupByOid() {
        return orderMapGroupByOid;
    }

    public Map<Long, List<Order>> getSuitSubOrderMapGroupByOrderId() {
        return suitSubOrderMapGroupByOrderId;
    }

    public Map<String, List<SplitUploadRecord>> getSplitUploadRecordMapGroupByOid() {
        return splitUploadRecordMapGroupByOid;
    }

    public List<SplitUploadRecord> getExistSplitUploadRecordList() {
        return existSplitUploadRecordList;
    }

    public void setExistSplitUploadRecordList(List<SplitUploadRecord> existSplitUploadRecordList) {
        this.existSplitUploadRecordList = existSplitUploadRecordList;
    }

    public List<SplitUploadRecord> getCurrentSplitUploadRecordList() {
        return currentSplitUploadRecordList;
    }

    public void setCurrentSplitUploadRecordList(List<SplitUploadRecord> currentSplitUploadRecordList) {
        this.currentSplitUploadRecordList = currentSplitUploadRecordList;
    }

    public List<SplitUploadRecord> getNeedInsertSplitUploadRecordList() {
        return needInsertSplitUploadRecordList;
    }

    public List<SplitUploadRecord> getNeedUpdateSplitUploadRecordList() {
        return needUpdateSplitUploadRecordList;
    }

    public Set<Long> getSuitOrderUploadSet() {
        return suitOrderUploadSet;
    }

    public void setSuitOrderUploadSet(Set<Long> suitOrderUploadSet) {
        this.suitOrderUploadSet = suitOrderUploadSet;
    }

    public Map<String, List<SplitUploadRecord>> groupSplitUploadRecordMapByOid() {
        Map<String, List<SplitUploadRecord>> groupSplitUploadRecordMap = new HashMap<>();
        existSplitUploadRecordList.forEach(splitUploadRecord -> groupSplitUploadRecordMap.computeIfAbsent(splitUploadRecord.getOid(), o -> new ArrayList<>()).add(splitUploadRecord));
        return groupSplitUploadRecordMap;
    }

    public SplitUploadRecord getRelateSplitUploadRecord(User user, Trade trade) {
        for (SplitUploadRecord splitUploadRecord : existSplitUploadRecordList) {
            if (!SplitUploadRecord.NOT_UPLOAD.equals(splitUploadRecord.getUploadStatus())) {
                for (Order order : TradeUtils.getOrders4Trade(trade)) {
                    if (order.getId().equals(splitUploadRecord.getOrderId())) {
                        String packId = splitUploadRecord.getPackId();
                        if (StringUtils.isBlank(packId)) {
                            continue;
                        }
                        return splitUploadRecord;
                    }
                }
            }
        }
        return null;
    }

}

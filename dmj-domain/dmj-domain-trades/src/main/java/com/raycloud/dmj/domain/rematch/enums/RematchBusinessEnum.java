package com.raycloud.dmj.domain.rematch.enums;

import lombok.*;

/**
 * @Auther mengfanguang
 * @Date 2023/4/27
 */
@Getter
@AllArgsConstructor
public enum RematchBusinessEnum {
    AUTO_EXCEPTION_NOT_REMATCH_BUSINESS(10, "重算（有异常不进行重算）"),
    AUTO_FLAG_MEMO_NOT_REMATCH_BUSINESS(11, "卖家备注/订单旗帜变更重算关键字设置"),
    AUTO_LABEL_MERGE_REMATCH_BUSINESS(20, "重算-（合单以后得新版标签重算）"),
    AUTO_LABEL_SPLIT_REMATCH_BUSINESS(30, "重算-（拆单以后的新版标签重算）"),
    AUTO_MARK_REMATCH_BUSINESS(30, "重算（自动标记规则）"),
    // TODO 注意：“重算-（合单以后得新版标签重算）”和“重算（自动标记规则）”必须放在“重算（赠品规则）”前面，涉及到”系统设置-交易配置-设置订单下载时，执行赠品规则和自动标记规则的顺序“的执行顺序,
    AUTO_GIFT_REMATCH_BUSINESS(40, "重算（赠品规则）"),
    AUTO_MATCH_RULE_REMATCH_BUSINESS(50, "重算 (自动化匹配规则)"),
    AUTO_WAREHOUSE_REMATCH_BUSINESS(60, "重算（自动匹配仓库）"),
    AUTO_EXPRESS_REMATCH_BUSINESS(70, "重算（自动匹配快递）"),
    AUTO_ITEM_REPLACE_REMATCH_BUSINESS(80, "重算（按商品搭配规则替换商品）"),
    AUTO_PRE_SELL_REMATCH_BUSINESS(90, "重算（预售规则）"),
    AUTO_MERGE_TRADE_REMATCH_BUSINESS(100, "重算（合单规则）"),
    AUTO_SPLIT_TRADE_REMATCH_BUSINESS(110, "重算（拆单规则）"),
    AUTO_SUIT_SWITCH_REMATCH_BUSINESS(120, "重算（套件转商品）"),
    AUTO_ORDER_STOCK_PRODUCT_BUSINESS(130, "重算批次"),
    AUTO_EXCEPT_MATCH_BUSINESS(150, "异常重算(退款商品数量异常)"),
    AUTO_AUDIT_MATCH_BUSINESS(160, "匹配审单规则"),

    AUTO_EXPRESS_THEORY_FREIGHT_BUSINESS(190, "重算理论运费"),

    AUTO_SYSTEM_LABEL_MATCH_BUSINESS(980, "重算（单价异常标签）"),
    AUTO_SALE_REMATCH_BUSINESS(990, "档口订单重算相关属性"),
    AUTO_FX_REMATCH_BUSINESS(1000, "分销订单重算相关属性"),
    ;

    /**
     * 操作顺序
     */
    private final Integer sortValue;

    /**
     * 说明
     */
    private final String msg;
}
package com.raycloud.dmj.domain.channel;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.ObjectParser;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * 星盘渠道参数，位于淘宝淘宝Trade结构中（omnichannel_param或omni_param）
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-29 16:05
 */
public class OrderChannel implements Serializable {

    private static final long serialVersionUID = 9126421168920748284L;

    /**
     * 订单的子订单号（对应order中的oid）
     */
    private String subOrderCode;
    /**
     * 该子订单是否曾经分单给门店过， 1 是， 0 否
     */
    private Integer everStoreAllocated;
    /**
     * 订单分担后的状态
     */
    private String orderState;
    /**
     * 订单收货类型，包含门店发货和门店自提
     */
    private String orderType;
    /**
     * 门店接单超时时间
     */
    private String acceptExpirationTime;
    /**
     * 全渠道星盘内部的分单编码
     */
    private String allocationCode;
    /**
     * 分单门店的外部编码
     */
    private String targetStoreOuterId;
    /**
     * 分单后的结果，用于区分分单到门店仓还是电商仓。WAREHOUSE表示电商仓，STORE表示门店仓
     */
    private String targetType;
    /**
     * 分单后对应的门店或电商的ID
     */
    private String targetCode;

    public String getSubOrderCode() {
        return subOrderCode;
    }

    public void setSubOrderCode(String subOrderCode) {
        this.subOrderCode = subOrderCode;
    }

    public Integer getEverStoreAllocated() {
        return everStoreAllocated;
    }

    public void setEverStoreAllocated(Integer everStoreAllocated) {
        this.everStoreAllocated = everStoreAllocated;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getAcceptExpirationTime() {
        return acceptExpirationTime;
    }

    public void setAcceptExpirationTime(String acceptExpirationTime) {
        this.acceptExpirationTime = acceptExpirationTime;
    }

    public String getAllocationCode() {
        return allocationCode;
    }

    public void setAllocationCode(String allocationCode) {
        this.allocationCode = allocationCode;
    }

    public String getTargetStoreOuterId() {
        return targetStoreOuterId;
    }

    public void setTargetStoreOuterId(String targetStoreOuterId) {
        this.targetStoreOuterId = targetStoreOuterId;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    /**
     * 解析淘宝订单中的渠道参数
     * @param channels 格式 everStoreAllocated:1,orderState:X_SHOP_HANDLED,targetType:STORE;everStoreAllocated:1,orderState:X_SHOP_HANDLED,targetType:STORE
     * @return List<OrderChannel>
     */
    public static List<OrderChannel> parseChannelParam(String channels) {
        if (channels == null || (channels = channels.trim()).isEmpty()) {
            return null;
        }
        StringTokenizer t = new StringTokenizer(channels, ";");
        List<OrderChannel> list = new ArrayList<OrderChannel>(t.countTokens());
        ObjectParser<OrderChannel> parser = new ObjectParser<OrderChannel>(OrderChannel.class);
        while (t.hasMoreTokens()) {
            boolean hasProp = false;
            JSONObject object = new JSONObject();
            String channel = t.nextToken();
            String[] kvs = channel.split(",");
            for (String kv : kvs) {
                int i = kv.indexOf(":");
                if (i > 0) {
                    String key = kv.substring(0, i);
                    String value = null;
                    if (i < kv.length() - 1) {
                        value = kv.substring(i + 1);
                    }
                    object.put(key, value);
                    hasProp = true;
                }
            }
            if (hasProp) {
                list.add(parser.json2Object(object));
            }
        }
        return list;
    }
}

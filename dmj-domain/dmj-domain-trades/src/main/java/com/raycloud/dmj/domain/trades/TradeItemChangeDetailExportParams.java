package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.trades.utils.DateSplitUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TradeItemChangeDetailExportParams extends ExportParams implements Serializable {

    private static final long serialVersionUID = -6021777234733132632L;

    public static final String EXCEL_NAME = "更换结果分析";

    private HashSet<Long> distanceTable;

    private TradeItemChangeQueryParams params;

    private String exportFields;

    private Integer maxSize;

    private Date originStart;

    private Date originEnd;

    private Long totalCount = 0L;

    private RuntimeIdSnapshot runtime;

    private Page page;

    public static TradeItemChangeDetailExportParams build(TradeItemChangeQueryParams params, String exportFields, Integer maxSize) {
        TradeItemChangeDetailExportParams exportParams = new TradeItemChangeDetailExportParams();
        exportParams.setParams(params);
        exportParams.setExportFields(exportFields);
        exportParams.setMaxSize(maxSize);
        exportParams.setPage(params.getPage());
        Integer exportTimeInterval = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getExportTimeInterval();
        if (exportTimeInterval == null || exportTimeInterval < 0){
            exportTimeInterval = 24;
        }
        List<TimeSlice> timeSlices = DateSplitUtils.dateSplit(params.getOperateTimeStart(), params.getOperateTimeEnd() , exportTimeInterval);
        exportParams.setTimeSlices(timeSlices);
        return exportParams;
    }


    public TradeItemChangeQueryParams getParams() {
        return params;
    }

    public void setParams(TradeItemChangeQueryParams params) {
        this.params = params;
    }

    public String getExportFields() {
        return exportFields;
    }

    public void setExportFields(String exportFields) {
        this.exportFields = exportFields;
    }

    public Integer getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }

    public Date getOriginStart() {
        return originStart;
    }

    public void setOriginStart(Date originStart) {
        this.originStart = originStart;
    }

    public Date getOriginEnd() {
        return originEnd;
    }

    public void setOriginEnd(Date originEnd) {
        this.originEnd = originEnd;
    }

    public HashSet<Long> getDistanceTable() {
        return distanceTable;
    }

    public void setDistanceTable(HashSet<Long> distanceTable) {
        this.distanceTable = distanceTable;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public RuntimeIdSnapshot getRuntime() {
        return runtime;
    }

    public void setRuntime(RuntimeIdSnapshot runtime) {
        this.runtime = runtime;
    }

    public int getCurrentTask() {
        return this.getStatus();
    }

    public void setCurrentTask(int currentTask) {
        setStatus(currentTask);
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}

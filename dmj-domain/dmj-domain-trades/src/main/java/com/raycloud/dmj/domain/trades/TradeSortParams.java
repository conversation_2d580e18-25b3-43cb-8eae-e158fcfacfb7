package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TradeSortParams implements Serializable {
    private static final long serialVersionUID = 1480779984306574769L;
    private String itemKindNum;

    private List<OrderSortParams> orders;

    public String getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(String itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public List<OrderSortParams> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderSortParams> orders) {
        this.orders = orders;
    }

    public static TradeSortParams build(Trade trade) {
        return build(trade, TradeUtils.getOrders4Trade(trade));

    }

    public static TradeSortParams build(Trade trade, List<Order> orders) {
        TradeSortParams params = new TradeSortParams();
        params.setItemKindNum(String.format("%03d", trade.getItemKindNum()));
        Map<String, OrderSortParams> map = new HashMap<>();
        for (Order order : orders) {
            String outerId = StringUtils.defaultIfEmpty(order.getSysOuterId(), order.getOuterIid());
            OrderSortParams orderSortParam = map.getOrDefault(outerId, new OrderSortParams());
            orderSortParam.setNum(orderSortParam.getNum() == null ? order.getNum() : (order.getNum() + orderSortParam.getNum()));
            orderSortParam.setOuterId(StringUtils.defaultIfEmpty(order.getSysOuterId(), order.getOuterIid()));
            map.put(outerId, orderSortParam);
        }
        params.setOrders(new ArrayList<>(map.values()));
        return params;

    }

    @Override
    public String toString() {
        int sum = 0;
        for (OrderSortParams order : this.orders) {
            if (order.getNum() != null) {
                sum += order.getNum();
            }
        }
//        return this.itemKindNum +
//                "^" + this.orders.stream().sorted(Comparator.comparing(OrderSortParams::getOuterId)).map(OrderSortParams::getOuterId).collect(Collectors.joining(",")) +
//                "^" + String.format("%03d", sum) + "," + this.orders.stream().sorted(Comparator.comparing(OrderSortParams::getOuterId)).map(o -> (o.getNum() == null ? "000" : String.format("%03d",o.getNum()))).collect(Collectors.joining(","));
        // https://gykj.yuque.com/entavv/xb9xi5/fykbd511we6icg8h 2022-12-15 修改排序值规则  商家编码1，商家编码2^商品总数，编码1的数量，编码2的数量
        return this.orders.stream().sorted(Comparator.comparing(OrderSortParams::getOuterId)).map(OrderSortParams::getOuterId).collect(Collectors.joining(",")) +
                "^" + String.format("%03d", sum) + "," + this.orders.stream().sorted(Comparator.comparing(OrderSortParams::getOuterId)).map(o -> (o.getNum() == null ? "000" : String.format("%03d", o.getNum()))).collect(Collectors.joining(","));

    }

    static class OrderSortParams implements Serializable {

        private static final long serialVersionUID = -5236600801281893607L;
        private String outerId;
        private Integer num;
        public String getOuterId() {
            if (StringUtils.isEmpty(outerId)){
                //排序到最后
                outerId = "zzzzzz";
            }
            return outerId;
        }

        public void setOuterId(String outerId) {
            this.outerId = outerId;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }
    }
}

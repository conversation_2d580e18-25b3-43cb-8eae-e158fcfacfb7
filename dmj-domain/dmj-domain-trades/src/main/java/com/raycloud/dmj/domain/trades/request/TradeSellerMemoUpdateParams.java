package com.raycloud.dmj.domain.trades.request;

import com.raycloud.dmj.domain.trades.TradeControllerParams;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_06_02 16:57
 */
public class TradeSellerMemoUpdateParams {
    Boolean append = null;

    /**
     * 备注标记  0：未处理 1：已处理
     */
    Integer memoType = 0;

    public Integer getMemoType() {
        return memoType;
    }

    public void setMemoType(Integer memoType) {
        this.memoType = memoType;
    }

    public Boolean getAppend() {
        return append;
    }

    public void setAppend(Boolean append) {
        this.append = append;
    }
}

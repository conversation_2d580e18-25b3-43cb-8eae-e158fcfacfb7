package com.raycloud.dmj.domain.trades.search;

/**
 * @Description <pre>
 * 运单号条件查询 拓展信息
 *
 * 如果一单多包拆分为子母单
 * 主单: sid: s1  outSid: os1
 * 包裹子单:     sid: s11  outSid: os11
 *              sid: s12  outSid: os12
 *  STRICT:
 *    传入 os1   返回 s1
 *    传入 os11  返回 null
 *
 *  MAIN_AND_SELF:
 *    传入 os1   返回 s1
 *    传入 os11  返回 s1,s11
 *
 *  ALL_REFS:
 *    传入 os1   返回 s1,s11,s12
 *    传入 os11  返回 s1,s11,s12
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-06-13
 */
public enum OutSidConditionProps {

    STRICT(0,"仅查询主运单号(trade表outSid),不考虑多包裹"),

    MAIN_AND_SELF(1,"考虑多包裹,如果子母单拆单,返回主单及传入outSid条件对应的子订单"),

    ALL_REFS(2,"考虑多包裹,如果子母单拆单,返回主单及下属的所有子订单"),
    ;


    private int key;
    private String desc;

    OutSidConditionProps(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}

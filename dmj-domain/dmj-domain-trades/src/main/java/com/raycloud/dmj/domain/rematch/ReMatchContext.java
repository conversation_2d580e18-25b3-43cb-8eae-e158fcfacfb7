package com.raycloud.dmj.domain.rematch;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import lombok.Builder;
import lombok.Data;

import java.util.*;

/**
 * @Auther mengfanguang
 * @Date 2022/10/18
 * @Desc 重算上下文
 */
@Data
@Builder
public class ReMatchContext {

    /**
     * 重算行为来源
     */
    private OpEnum op;

    /**
     * 事件
     */
    private EventEnum event;

    /**
     * staff对象
     */
    private Staff staff;

    /**
     * tradeConfig
     */
    private TradeConfig tradeConfig;

    /**
     * 当前操作的订单
     */
    private List<Trade> originTrades;

    /**
     * 因为异常移除的订单
     */
    private List<Trade> exceptRemoveTrades;

    /**
     * 需要重算的订单（originTrades = exceptRemoveTrades + needRematchTrades)
     */
    private List<Trade> needRematchTrades;

    /**
     * 源需要重算的订单   可能存在某些重算业务需要过滤订单,但是不能影响其他重算业务场景, 声明一个临时集合,以便后续重算使用
     */
    private List<Trade> originNeedRematchTrades;


    /**
     * 需要重算自动标记规则的订单（如果开了已审核重算，这里要加上已审核的）
     */
    private List<Trade> needRematchMarkTrades;


    /**
     * 记录日志的订单 （needRematchTrades 匹配前， progressLogTrades匹配后）
     */
    private List<Trade> progressLogTrades;

    /**
     * 要删除的sids
     */
    private List<Long> removerSids;

    /**
     * 是否需要重算仓库（默认是true，订单导入的重算要看传进来的）
     */
    private List<Long> needRematchWarehouseSids;

    /**
     * TJ白名单配置
     */
    private Map<Feature, Boolean> featureConfigMap;


}
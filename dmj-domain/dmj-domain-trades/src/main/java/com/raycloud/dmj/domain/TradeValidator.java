package com.raycloud.dmj.domain;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.except.utils.ExceptUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 2019-06-03 21:45
 */
public class TradeValidator implements Serializable {
    private static final long serialVersionUID = -4790480115148170011L;

    /**
     * 订单异常错误码
     */
    private int code;
    /**
     * 订单异常错误信息
     */
    private String message;
    /**
     * 订单有异常时是否抛出异常
     */
    private boolean throwExceptionIfError = true;
    /**
     * 是否检查店铺权限
     */
    private boolean checkUserPrivilege = true;
    /**
     * 是否检查店铺是否停用
     */
    private boolean checkUserActive = true;
    /**
     * 检查作废
     */
    private boolean checkCancel = true;
    /**
     * 是否挂起异常
     */
    private boolean checkHalt = true;
    /**
     * 是否检查退款异常
     */
    private boolean checkRefund = true;
    /**
     * 是否平台换地址异常
     */
    private boolean checkAddressChanged = true;
    /**
     * 是否平台卖家备注更新异常
     */
    private boolean checkSellerMemoChanged = true;
    /**
     * 是否检查平台换商品异常
     */
    private boolean checkItemChanged = true;
    /**
     * 是否检查黑名单异常
     */
    private boolean checkBlackBuyerNick = true;
    /**
     * 是否检查库存异常
     */
    private boolean checkStockStatus = true;
    /**
     * 是否忽略缺货异常
     * 发货前称重、批量称重、包装验货需要忽略缺货异常
     */
    private boolean ignoreInsufficient;
    /**
     * 是否检查自定义异常
     */
    private boolean checkCustomExcept = true;
    /**
     * 是否检查其他异常
     */
    private boolean checkExcept = true;
    /**
     * 是否检查部分关闭异常
     */
    private boolean checkPartRefund = true;

    /**
     * 该订单是否需要取消缺货异常
     */
    private boolean cancelInsufficient;

    /**
     * 是否检查快递异常
     */
    private boolean checkExpressException = true;
    /**
     * 是否检查风控异常
     */
    private boolean checkRiskException = true;
    /**
     * 是否检查发货异常
     */
    private boolean checkDeliverException = true;
    /**
     * 是否检查上传异常
     */
    private boolean checkUploadException = false;
    /**
     * 是否检查套件异常
     */
    private boolean checkSuitException = true;

    /**
     * 是否检查上传异常
     */
    private boolean checkProcessException = true;

    private boolean checkAddressRule = false;

    private boolean supportSellerSend = false;

    private String cpCode;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     * 是否检查发货后退款异常
     */
    private boolean checkAfterSendGoodsRefundException = false;

    public boolean isCheckAfterSendGoodsRefundException() {
        return checkAfterSendGoodsRefundException;
    }

    public void setCheckAfterSendGoodsRefundException(boolean checkAfterSendGoodsRefundException) {
        this.checkAfterSendGoodsRefundException = checkAfterSendGoodsRefundException;
    }

    public boolean isCheckAddressRule() {
        return checkAddressRule;
    }

    public void setCheckAddressRule(boolean checkAddressRule) {
        this.checkAddressRule = checkAddressRule;
    }

    public boolean isSupportSellerSend() {
        return supportSellerSend;
    }

    public void setSupportSellerSend(boolean supportSellerSend) {
        this.supportSellerSend = supportSellerSend;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isThrowExceptionIfError() {
        return throwExceptionIfError;
    }

    public void setThrowExceptionIfError(boolean throwExceptionIfError) {
        this.throwExceptionIfError = throwExceptionIfError;
    }

    public boolean isCheckUserPrivilege() {
        return checkUserPrivilege;
    }

    public void setCheckUserPrivilege(boolean checkUserPrivilege) {
        this.checkUserPrivilege = checkUserPrivilege;
    }

    public boolean isCheckUserActive() {
        return checkUserActive;
    }

    public void setCheckUserActive(boolean checkUserActive) {
        this.checkUserActive = checkUserActive;
    }

    public boolean isCheckCancel() {
        return checkCancel;
    }

    public void setCheckCancel(boolean checkCancel) {
        this.checkCancel = checkCancel;
    }

    public boolean isCheckHalt() {
        return checkHalt;
    }

    public void setCheckHalt(boolean checkHalt) {
        this.checkHalt = checkHalt;
    }

    public boolean isCheckRefund() {
        return checkRefund;
    }

    public void setCheckRefund(boolean checkRefund) {
        this.checkRefund = checkRefund;
    }

    public boolean isCheckAddressChanged() {
        return checkAddressChanged;
    }

    public void setCheckAddressChanged(boolean checkAddressChanged) {
        this.checkAddressChanged = checkAddressChanged;
    }

    public boolean isCheckSellerMemoChanged() {
        return checkSellerMemoChanged;
    }

    public void setCheckSellerMemoChanged(boolean checkSellerMemoChanged) {
        this.checkSellerMemoChanged = checkSellerMemoChanged;
    }

    public boolean isCheckItemChanged() {
        return checkItemChanged;
    }

    public void setCheckItemChanged(boolean checkItemChanged) {
        this.checkItemChanged = checkItemChanged;
    }

    public boolean isCheckBlackBuyerNick() {
        return checkBlackBuyerNick;
    }

    public void setCheckBlackBuyerNick(boolean checkBlackBuyerNick) {
        this.checkBlackBuyerNick = checkBlackBuyerNick;
    }

    public boolean isCheckStockStatus() {
        return checkStockStatus;
    }

    public void setCheckStockStatus(boolean checkStockStatus) {
        this.checkStockStatus = checkStockStatus;
    }

    public boolean isIgnoreInsufficient() {
        return ignoreInsufficient;
    }

    public void setIgnoreInsufficient(boolean ignoreInsufficient) {
        this.ignoreInsufficient = ignoreInsufficient;
    }

    public boolean isCheckCustomExcept() {
        return checkCustomExcept;
    }

    public void setCheckCustomExcept(boolean checkCustomExcept) {
        this.checkCustomExcept = checkCustomExcept;
    }

    public boolean isCheckExcept() {
        return checkExcept;
    }

    public void setCheckExcept(boolean checkExcept) {
        this.checkExcept = checkExcept;
    }

    public boolean isCheckPartRefund() {
        return checkPartRefund;
    }

    public void setCheckPartRefund(boolean checkPartRefund) {
        this.checkPartRefund = checkPartRefund;
    }

    public boolean isCancelInsufficient() {
        return cancelInsufficient;
    }

    public void setCancelInsufficient(boolean cancelInsufficient) {
        this.cancelInsufficient = cancelInsufficient;
    }

    public boolean isCheckExpressException() {
        return checkExpressException;
    }

    public void setCheckExpressException(boolean checkExpressException) {
        this.checkExpressException = checkExpressException;
    }

    public boolean isCheckRiskException() {
        return checkRiskException;
    }

    public void setCheckRiskException(boolean checkRiskException) {
        this.checkRiskException = checkRiskException;
    }

    public boolean isCheckDeliverException() {
        return checkDeliverException;
    }

    public void setCheckDeliverException(boolean checkDeliverException) {
        this.checkDeliverException = checkDeliverException;
    }

    public boolean isCheckUploadException() {
        return checkUploadException;
    }

    public void setCheckUploadException(boolean checkUploadException) {
        this.checkUploadException = checkUploadException;
    }

    public boolean isCheckSuitException() {
        return checkSuitException;
    }

    public void setCheckSuitException(boolean checkSuitException) {
        this.checkSuitException = checkSuitException;
    }

    public boolean isCheckProcessException() {
        return checkProcessException;
    }

    public void setCheckProcessException(boolean checkProcessException) {
        this.checkProcessException = checkProcessException;
    }

    public void setError(Error error) {
        setError(error.getCode(), error.getMessage());
    }

    public void setError(int code, String message) {
        setCode(code);
        setMessage(message);
    }

    public boolean hasError() {
        return getCode() != 0 || StringUtils.isNotBlank(getMessage());
    }


    public void checkExcept(Staff staff, Trade trade) {
        if (isCheckCancel() && (trade.getIsCancel() != null && trade.getIsCancel() - 1 == 0)) {
            setError(Error.CANCELLED);
        } else if (isCheckHalt() && checkTradeExcept(staff, trade, ExceptEnum.HALT)) {
            setError(Error.HALT);
        } else if (isCheckRefund() && checkTradeExcept(staff, trade, ExceptEnum.REFUNDING)) {
            setError(Error.REFUND);
        } else if (isCheckAddressChanged() && checkTradeExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED)) {
            setError(Error.ADDRESS_CHANGED);
        } else if (isCheckSellerMemoChanged() && checkTradeExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE)) {
            setError(Error.SELLER_MEMO_CHANGED);
        } else if (isCheckBlackBuyerNick() && checkTradeExcept(staff, trade, ExceptEnum.BLACK_NICK)) {
            setError(Error.BLACK_BUYER_NICK);
        } else if (isCheckCustomExcept() && checkTradeExcept(staff, trade, ExceptEnum.CUSTOM)) {
            setError(Error.CUSTOM_EXCEPTION);
        } else if (isCheckPartRefund() && checkTradeExcept(staff, trade, ExceptEnum.PART_REFUND)) {
            setError(Error.PART_REFUND);
        } else if (isCheckExpressException() && checkTradeExcept(staff, trade, ExceptEnum.UNATTAINABLE)) {
            setError(Error.EXPRESS_EXCEPTION);
        } else if (isCheckRiskException() && checkTradeExcept(staff, trade, ExceptEnum.RISK)) {
            setError(Error.RISK_EXCEPTION);
        } else if (isCheckDeliverException() && checkTradeExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT)) {
            setError(Error.DELIVER_EXCEPTION);
        } else if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
            setError(Error.SYS_STATUS_CLOSED);
        } else if (isCheckUploadException() && checkTradeExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT)) {
            setError(Error.UPLOAD_EXCEPTION);
        } else if (isCheckSuitException() && checkTradeExcept(staff, trade, ExceptEnum.SUITE_CHANGE)) {
            setError(Error.SUIT_EXCEPTION);
        } else if (isCheckProcessException() && checkTradeExcept(staff, trade, ExceptEnum.ITEM_PROCESS)) {
            setError(Error.SUIT_EXCEPTION);
        } else {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {
                    if (isCheckItemChanged() && checkOrderExcept(staff, trade, order, ExceptEnum.ITEM_CHANGED)) {
                        setError(Error.ITEM_CHANGED);
                    } else if (isCheckRefund() && RefundUtils.isRefundOrder(order)) {
                        setError(Error.REFUND);
                    } else if (isCheckStockStatus()) {
                        if (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                            if (order.getInsufficientCanceled() != null && order.getInsufficientCanceled() - 1 != 0) {
                                if (isIgnoreInsufficient()) {
                                    order.setInsufficientCanceled(1);
                                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.INSUFFICIENT, 0L);
                                    setCancelInsufficient(true);
                                } else {
                                    setError(Error.INSUFFICIENT);
                                }
                            }
                        } else if (checkOrderExcept(staff, trade, order, ExceptEnum.RELATION_CHANGED)) {
                            setError(Error.RELATION_CHANGED);
                        } else if (order.getItemSysId() != null && order.getItemSysId() <= 0) {
                            setError(Error.UNALLOCATED);
                        } else if (!Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
                            setError(Error.STOCK_ABNORMAL);
                        }
                    }
                } else {
                    if (isCheckAfterSendGoodsRefundException() && RefundUtils.isRefundOrder(order)) {
                        setError(Error.REFUND);
                    }
                }
                if (hasError()) {//只要有错误就结束
                    break;
                }
            }
            TradeExceptUtils.resetTradeExceptByOrder(staff, trade, ExceptEnum.INSUFFICIENT);
        }
        //又取消缺货异常的子订单时需要重新计算订单异常,这样才能知道是否还有其他异常
        if (isCancelInsufficient()) {
            TradeUtils.setTradeExcep(staff, trade);
        }

        //上面的异常都没有时,如果订单需要检查其他异常并且有其他异常
        if (!hasError() && isCheckExcept() && trade.getIsExcep() != null && trade.getIsExcep() - 1 == 0) {
            setError(Error.EXCEPTION);
        }
        //检查订单有异常时是否需要抛出异常
        checkThrowError();
    }

    private boolean checkTradeExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        if (exceptEnum == ExceptEnum.CUSTOM) {
            return TradeExceptUtils.isContainCustomExcept(staff, trade) || TradeExceptUtils.isSubTradeContainCustomExcept(staff, trade);
        } else {
            // 子单的异常判断
            return TradeExceptUtils.isContainExcept(staff, trade, exceptEnum) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, exceptEnum);

        }
    }


    private boolean checkOrderExcept(Staff staff, Trade trade, Order order, ExceptEnum exceptEnum) {
        if (!TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, -1L)) {
            return OrderExceptUtils.isContainsExcept(staff, order, exceptEnum);
        }
        if (trade.getSid() - order.getSid() == 0) {
            // 合单可能包含非自身的order
            return OrderExceptUtils.isContainsExcept(staff, order, exceptEnum);
        } else {
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            if (subTradeExceptDatas != null && subTradeExceptDatas.get(order.getSid()) != null) {
                return ExceptUtils.isContainsExcept(subTradeExceptDatas.get(order.getSid()), exceptEnum.getId(), order.getId());
            }
        }
        return false;
    }

    public void check(Staff staff, Trade trade) {
        if (trade == null) {
            setError(Error.NOT_FOUND);
        } else if (staff != null && !(trade.isOutstock() || TradeUtils.isGxOrMixTrade(trade)) && !trade.isPenetrateTrade()) {
            //穿透单不校验权限
            User user = staff.getUserByUserId(trade.getUserId());
            if (user == null) {
                if (isCheckUserPrivilege()) {
                    setError(Error.NO_USER_PRIVILEGE);
                }
            } else if (isCheckUserActive() && user.getActive() == 0) {
                setError(Error.USER_DISABLED);
            }
        }
        checkThrowError();
        if (!hasError()) {
            checkExcept(staff,trade);
        }
    }

    public void checkThrowError() {
        if (isThrowExceptionIfError() && hasError()) {
            throw new IllegalArgumentException(StringUtils.isNotBlank(getMessage()) ? getMessage() : String.valueOf(getCode()));
        }
    }

    public void reset() {
        setCode(0);
        setMessage(null);
        setCancelInsufficient(false);
    }

    public enum Error {

        EXCEPTION(10001, "异常订单"),
        CANCELLED(10002, "订单已作废"),
        HALT(10003, "订单已挂起"),
        REFUND(10004, "订单退款中"),
        ADDRESS_CHANGED(10005, "平台地址有更新"),
        SELLER_MEMO_CHANGED(10006, "平台卖家备注有更新"),
        ITEM_CHANGED(10007, "平台商品有更新"),
        BLACK_BUYER_NICK(10008, "黑名单订单"),
        CUSTOM_EXCEPTION(10009, "自定义异常订单"),
        STOCK_ABNORMAL(10010, "库存状态异常"),
        RELATION_CHANGED(10011, "商品对应关系改动"),
        UNALLOCATED(10012, "商品未匹配"),
        INSUFFICIENT(10013, "订单缺货"),
        PART_REFUND(10014, "部分关闭"),
        EXPRESS_EXCEPTION(10015, "快递异常"),
        RISK_EXCEPTION(10016, "风控异常"),
        DELIVER_EXCEPTION(10017, "发货异常"),
        UPLOAD_EXCEPTION(10018, "上传异常"),
        SUIT_EXCEPTION(10019, "套件异常"),

        NOT_FOUND(20001, "没找到订单"),
        NO_USER_PRIVILEGE(20002, "无店铺权限"),
        USER_DISABLED(20003, "店铺已停用"),

        SYS_STATUS_ERROR(30001, "系统状态有误"),

        ITEM_PROCESS_EXCEPTION(30011, "普通商品转加工"),


        SYS_STATUS_CLOSED(300016,"订单已关闭"),
        PENETRATE_NOT_TEMPLATE(30009,"穿透单未选择模板!"),
        ;

        private int code;
        private String message;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        Error(int code, String message) {
            this.code = code;
            this.message = message;
        }
    }
}

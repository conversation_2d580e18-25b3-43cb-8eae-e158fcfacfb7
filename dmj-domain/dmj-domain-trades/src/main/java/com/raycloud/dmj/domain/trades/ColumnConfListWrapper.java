package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ruanyg on 16/6/1.
 */
public class ColumnConfListWrapper implements Serializable {

    private List<ColumnConf> columnConfList;

    private Boolean isDefault;

    public ColumnConfListWrapper() {
    }

    public ColumnConfListWrapper(List<ColumnConf> columnConfList, Boolean isDefault) {
        this.columnConfList = columnConfList;
        this.isDefault = isDefault;
    }

    public List<ColumnConf> getColumnConfList() {
        return columnConfList;
    }

    public void setColumnConfList(List<ColumnConf> columnConfList) {
        this.columnConfList = columnConfList;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
}

package com.raycloud.dmj.domain;

import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 交易常量类
 * Description:
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>@maijia.com
 * on 2016/12/13 0013-上午 10:56.
 */
public abstract class TradeConstants {

    public static final String YES = "1";

    public static final String NO = "0";


    public static final String AUDIT_DEFAULT_END = "23:59";

    // 默认时间 2000-01-01 00:00:00
    @Deprecated
    public static final long INIT_DATE_TIME = TradeTimeUtils.INIT_DATE_TIME;
    @Deprecated
    public static final Date INIT_DATE = TradeTimeUtils.INIT_DATE;
    @Deprecated
    public static final String INIT_DATE_STR = TradeTimeUtils.INIT_DATE_STR;

    /**
     * 分销订单匹配供销商的方式：0:不勾选 快麦通商品和erp分销策略
     */
    public static final String DMS_TRADE_MATCH_FX_ATTR_ALL = "0";

    /**
     * 分销订单匹配供销商的方式：1:通过快麦通的商品匹配供销商
     */
    public static final String DMS_TRADE_MATCH_FX_ATTR_KMT = "1";

    /**
     * 分销订单匹配供销商的方式：2:通过ERP的分销策略规则匹配供销商
     */
    public static final String DMS_TRADE_MATCH_FX_ATTR_TRADE_RULE = "2";

    /**
     * 供销取消异常
     */
    public static final String GX_TRADE_CANCEL_EXCEPT = "GX_TRADE_CANCEL_EXCEPT";
    /**
     * 普通出库单类型
     */
    public static final String TYPE_TRADE_OUT = "trade_out";

    /**
     * 客户订单类型
     */
    public static final String TYPE_TRADE_CUSTOMER = "customer";


    /**
     * 换货订单类型
     */
    public static final String TYPE_TRADE_EXCHANGE = "changeitem";

    /**
     * 补发订单类型
     */
    public static final String TYPE_TRADE_REISSUE = "reissue";
    /**
     * 维修单转订单
     */
    public static final String TYPE_TRADE_REPAIR = "repair";

    /**
     * 档口订单类型
     */
    public static final String TYPE_TRADE_DANGKOU = "dangkou";


    /**
     * 是否由系统自动上传的：0：非自动上传 1：自动上传成功 2：自动上传失败
     */
    public static final Integer CONSIGN_UPLOAD_NO = 0;
    public static final Integer CONSIGN_UPLOAD_SUCCESS = 1;
    public static final Integer CONSIGN_UPLOAD_FAIL = 2;

    /**
     * trade扩展字段V
     * 二进制标志字段,从低位到高位依次表示
     * 2^0 = 1. 已取消平台换地址异常（反审核时会取消该标记）
     * 2^1 = 2. 地址已处理
     * 2^2 = 4. 上传失败标记
     * 2^3 = 8. 已取消单价异常标签
     */
    public static final int TRADE_V_CANCEL_ADDRESS_CHANGE = 1;
    public static final int TRADE_V_ADDRESS_PROCESS = 1 << 1;
    public static final int TRADE_V_UPLOAD_EXCEPTION = 1 << 2;
    public static final int TRADE_V_CANCEL_PRICE_TAG = 1 << 3;

    //============================ 订单系统状态权重 ============================//

    /**
     * 待付款权重，这个最低
     */
    public static final int WEIGHT_WAIT_BUYER_PAY = 1;

    /**
     * 待审核权重
     */
    public static final int WEIGHT_WAIT_AUDIT = 2;

    /**
     * 待财核权重
     */
    public static final int WEIGHT_WAIT_FINANCE_AUDIT = 2;

    /**
     * 待审核权重
     */
    public static final int WEIGHT_WAIT_MANUAL_AUDIT = 2;

    /**
     * 审核完成权重
     */
    public static final int WEIGHT_FINISHED_AUDIT = 3;

    /**
     * 已发货权重
     */
    public static final int WEIGHT_SELLER_SEND_GOODS = 4;

    /**
     * 交易完成权重
     */
    public static final int WEIGHT_FINISHED = 5;

    /**
     * 交易关闭权重
     */
    public static final int WEIGHT_CLOSED = 6;


    //============================ 库存状态的权重 ============================//

    /**
     * 商品对应关系修改的权重
     */
    public static final int WEIGHT_STOCK_RELATION_MODIFIED = 1;
    /**
     * 库存未分配的权重
     */
    public static final int WEIGHT_STOCK_UNALLOCATED = 2;
    /**
     * 空值库存的权重
     */
    public static final int WEIGHT_STOCK_EMPTY = 3;
    /**
     * 库存不足的权重
     */
    public static final int WEIGHT_STOCK_INSUFFICIENT = 4;
    /**
     * 库存正常的权重
     */
    public static final int WEIGHT_STOCK_NORMAL = 5;



    //============================ 仓库状态枚举 ============================//
    /**
     * 仓库状态正常
     */
    public static final String WAREHOUSE_STATUS_NORMAL = "NORMAL";

    /**
     * 换商品
     */
    public static final String WAREHOUSE_STATUS_EXCHANGE_GOODS = "EXCHANGE_GOODS";

    /**
     * 验货提报
     */
    public static final String WAREHOUSE_STATUS_EXAMINE_GOODS_REPORT = "EXAMINE_GOODS_REPORT";

    /**
     * 换快递
     */
    public static final String WAREHOUSE_STATUS_EXCHANGE_EXPRESS = "EXCHANGE_EXPRESS";



    //============================ 合单、拆单常量 ============================//

    /**
     * 使用TradeMergeSplitEnum对应的枚举值替代
     */
    @Deprecated
    public static final int MERGE_TYPE_NORMAL = TradeMergeEnum.MERGE_NORMAL.getDbType();

    /**
     * 使用TradeMergeSplitEnum对应的枚举值替代
     */
    @Deprecated
    public static final int MERGE_TYPE_AUTO = TradeMergeEnum.MERGE_AUTO.getDbType();

    /**
     * 使用TradeMergeSplitEnum对应的枚举值替代
     */
    @Deprecated
    public static final int SPLIT_TYPE_NORMAL = TradeSplitEnum.SPLIT_NORMAL.getDbType();

    @Deprecated
    public static final int SPLIT_TYPE_MANUAL = TradeSplitEnum.SPLIT_SKU.getDbType();

    /**
     * 订单同步方式
     *  0 RDS增量同步（jdp_modified），
     *  1 API增量同步（modified），（非陶系平台的api同步方式）
     *  2 API全量同步（created）
     */
    public static final int SYNC_INCR_RDS = 0;
    public static final int SYNC_INCR_API = 1;
    public static final int SYNC_FULL_API = 2;
    public static final int SYNC_MIX = 3;

    //正常订单
    public static final int NORMAL_CONVERT_TYPE = 0;
    //系统分销订单(convertType)
    public static final int FX_SYS_CONVERT_TYPE = 1;
    //平台分销订单
    public static final int FX_PLAT_CONVERT_TYPE = 2;
    public static final int NORMAL_BELONG_TYPE = 0;
    public static final int FX_SYS_BELONG_TYPE = 1;
    public static final int GX_SYS_BELONG_TYPE = 2;
    public static final int GX_FX_SYS_BELONG_TYPE = 3;


    /*************************** 订单异常索引表 **************************/
    public static final int IDX_HALT = 0;                       // 挂起异常
    public static final int IDX_REFUNDING = 1;                  // 退款处理中异常（发货后退款处理中订单不算异常）
    public static final int IDX_INSUFFICIENT = 2;               // 缺货异常（包含EMPTY、EXCEP以及没有缺货发货的INSUFFICIENT）
    public static final int IDX_UNALLOCATED = 3;                // 商品未匹配异常
    public static final int IDX_RELATION_CHANGED = 4;           // 商品对应关系改动异常
    public static final int IDX_ITEM_CHANGED = 5;               // 平台换商品异常（已审核情况下）
    public static final int IDX_ADDRESS_CHANGED = 6;            // 平台改地址异常
    public static final int IDX_SELLER_MEMO_UPDATE = 7;         // 平台改备注异常
    public static final int IDX_PART_REFUND = 8;                // 部分退款异常
    public static final int IDX_CUSTOM_EXCEPT = 9;              // 包含自定义异常
    public static final int IDX_TRADE_LOST_SYS_STATUS = 10;     // trade缺少系统状态异常
    public static final int IDX_ORDER_LOST_SYS_STATUS = 11;     // order缺少系统状态异常
    public static final int IDX_LOST_MSG = 12;                  // 缺少必要信息异常
    public static final int IDX_BLACK_NICK = 13;                // 黑名单异常
    public static final int IDX_SUITE_CHANGE = 14;              // 套件变更异常
    public static final int IDX_PDD_RISK = 15;                  // 拼多多风控异常
    public static final int IDX_DELIVER_EXCEPT = 16;            // 系统发货异常
    public static final int IDX_UNATTAINABLE = 17;              // 快递停发异常
    public static final int IDX_LOST_ITEM = 22;                 // 缺少商品异常
    public static final int IDX_FX_AMBIGUITY = 18;            // 不明确供应商异常
    public static final int IDX_FX_WAITPAY = 19;            // 分销待付款异常
    public static final int IDX_FX_UNAUDIT = 20;            // 分销反审核异常
    public static final int IDX_FX_REPULSE = 21;            // 供销商打回异常
    public static final int IDX_COD_REPEAT = 25;                //货到付款订单重复异常
    public static final int IDX_STOCK_OUT = 23;                 // 缺货已处理异常
    public static final int IDX_WATING_RETURN_WMS = 26;         // 等待退货入仓
    public static final int IDX_WAIT_MERGE = 27;                //等待合并异常
    public static final int IDX_ITEM_SHUTOFF = 28;              // 停用
    public static final int IDX_TMCS_STOCK_OUT = 29;            //天猫超市缺货回告
    public static final int IDX_TMGJZY_STOCK_OUT = 30;            //天猫超市缺货回告
    public static final int IDX_PLATFROM_WAREHOUSE_MATCH = 32;              // 平台仓
    public static final int IDX_UNIQUE_CODE_OFFSHELF = 31;            //唯一码下架
    public static final int IDX_PLATFORM_FX_SPLIT_EXCEPT = 34;              // 平台分销待拆分
    public static final int IDX_ITEM_PROCESS = 35;              // 普通商品转加工
    public static final int IDX_POISON_NOT_MATCH_EXPRESS_TEMPLATE = 36;              // 得物直发物流模板匹配
    public static final int IDX_VIP_COOPERATION_CODE_NOT_MATCH = 37;

    //订单异常值
    public static final int NO_EXCEPT = 0;//无异常
    public static final int RELATION_CHANGED = 1;//商品对应关系改动
    public static final int INSUFFICIENT = 1 << 1;//缺货
    public static final int UNALLOCATED = 1 << 2;//商品为匹配
    public static final int ITEM_CHANGED = 1 << 3;//平台换商品
    public static final int PART_REFUND = 1 << 4;//部分退款
    public static final int ADDRESS_CHANGED = 1 << 5;//平台地址更换
    public static final int SELLER_MEMO_UPDATE = 1 << 6;//平台地址更换
    public static final int BLACK_NICK = 1 << 7;//黑名单
    public static final int LOST_MESSAGE = 1 << 8;//信息丢失
    public static final int PDD_RISKEXCEP = 1 << 9;//pdd风控异常
    public static final int DELIVER_EXCEPT = 1 << 10;//发货异常
    public static final int PDD_STOCK_OUT = 1 << 11;//pdd缺货已处理异常
    public static final int SUITE_QUANTITY_CHANGE = 1 << 12;//套件更改异常(添加或者删除，不包括修改)
    public static final int ITEM_PROCESS_EXCEP = 1 << 19;//普通商品转加工商品异常
    public static final int COD_REPEAT = 1 << 20;//货到付款重复订单
    public static final int WATING_RETURN_WMS_EXCEPT = 1 << 21;//等待退货入仓异常,售后生成的换货补发订单标记的系统异常
    public static final int WAIT_MERGE = 1 << 22;//等待合并
    public static final int ITEM_SHUTOFF = 1 << 23;// 商品停用
    /*public static final long OVERTIME = 1 << 24; //即将超时（预计发货时间）移到系统标签里去了*/
    public static final long TMCS_STOCK_OUT = 1 << 25; //天猫超市缺货异常
    public static final long TMGJZY_STOCK_OUT = 1 << 26; //天猫国际直营缺货异常
    public static final int  PLATFORM_WAREHOUSE_MAPPING_EXCEPTION = 1 << 28;//天猫物流升级平台仓映射异常
    public static final int  SMTQTG_UN_CONFIRM_EXCEPTION = 1 << 29;//速卖通全托管待接单异常
    public static final int UNIQUE_CODE_OFFSHELF = 1 << 27; //唯一码下架
    public static final long  PLATFORM_FX_SPLIT_EXCEPT = 1L << 34;//平台分销待拆分
    public static final long  POISON_NOT_MATCH_EXPRESS_TEMPLATE = 1L << 35;//得物直发订单物流模板匹配失败异常

    public static final long FX_AMBIGUITY = 1 << 13;//不明确供应商异常
    public static final long FX_WAITPAY = 1 << 14;//分销待付款异常
    public static final long FX_UNAUDIT = 1 << 15;//分销反审核异常
    public static final long FX_REPULSE = 1 << 16;//供销商打回异常

    public static final long VIP_COOPERATION_CODE_NOT_MATCH = 1L << 37; // 唯品会未匹配常态合作码

    public static final Map<String,String> excepToIntMap = new HashMap<>();

    public static final Map<String,String> intToExcepMap = new HashMap<>();

    public static final Map<String,String> excepToChineseMap =new HashMap<>();

    public static final Map<String,String> JDVC_GXD_CARRIER_MAP = new HashMap<>();


    static {//做成enum更好
        excepToIntMap.put("EXCEP_HALT","1");
        excepToIntMap.put("EXCEP_REFUND","2");
        excepToIntMap.put("EXCEP_ITEM_UNALLOCATED","3");
        excepToIntMap.put("EXCEP_ITEM_RELATION_MODIFIED","4");
        excepToIntMap.put("EXCEP_STOCK_INSUFFICIENT","5");
        excepToIntMap.put("EX_CHANGE_ADDRESS","6");
        excepToIntMap.put("EX_CHANGE_ITEM","7");
        excepToIntMap.put("EX_UPDATED_SELLERMEMO","8");
        excepToIntMap.put("EX_BLACK","9");
        excepToIntMap.put("EX_LOST_MSG","10");
        excepToIntMap.put("EX_UNATTAINABLE","11");
        excepToIntMap.put("EX_PART_REFUND","12");
        excepToIntMap.put("EX_RISK_ORDER","13");
        excepToIntMap.put("EX_DELIVER","14");
        excepToIntMap.put("EX_UPLOAD_DELIVER","15");
        excepToIntMap.put("EX_SUITE_QUANTITY_CHANGE","16");
        excepToIntMap.put("EX_COD_REPEAT","17");
        excepToIntMap.put("EX_STOCK_OUT","18");
        excepToIntMap.put("EX_WAITING_RETURN_WMS","19");
        excepToIntMap.put("EX_WAIT_MERGE","20");
        excepToIntMap.put("EX_ITEM_SHUTOFF","21");
        excepToIntMap.put("PLATFORM_WAREHOUSE_MAPPING_EXCEPTION","22");
        excepToIntMap.put("EX_UNIQUE_CODE_OFFSHELF","23");

        intToExcepMap.put("1","EXCEP_HALT");
        intToExcepMap.put("2","EXCEP_REFUND");
        intToExcepMap.put("3","EXCEP_ITEM_UNALLOCATED");
        intToExcepMap.put("4","EXCEP_ITEM_RELATION_MODIFIED");
        intToExcepMap.put("5","EXCEP_STOCK_INSUFFICIENT");
        intToExcepMap.put("6","EX_CHANGE_ADDRESS");
        intToExcepMap.put("7","EX_CHANGE_ITEM");
        intToExcepMap.put("8","EX_UPDATED_SELLERMEMO");
        intToExcepMap.put("9","EX_BLACK");
        intToExcepMap.put("10","EX_LOST_MSG");
        intToExcepMap.put("11","EX_UNATTAINABLE");
        intToExcepMap.put("12","EX_PART_REFUND");
        intToExcepMap.put("13","EX_RISK_ORDER");
        intToExcepMap.put("14","EX_DELIVER");
        intToExcepMap.put("15","EX_UPLOAD_DELIVER");
        intToExcepMap.put("16","EX_SUITE_QUANTITY_CHANGE");
        intToExcepMap.put("17","EX_COD_REPEAT");
        intToExcepMap.put("18","EX_STOCK_OUT");
        intToExcepMap.put("19","EX_WAITING_RETURN_WMS");
        intToExcepMap.put("20","EX_WAIT_MERGE");
        intToExcepMap.put("21","EX_ITEM_SHUTOFF");
        intToExcepMap.put("22","PLATFORM_WAREHOUSE_MAPPING_EXCEPTION");
        intToExcepMap.put("23","EX_UNIQUE_CODE_OFFSHELF");

        excepToChineseMap.put("EXCEP_HALT","挂起订单");
        excepToChineseMap.put("EXCEP_REFUND","退款订单");
        excepToChineseMap.put("EXCEP_ITEM_UNALLOCATED","商品未匹配");
        excepToChineseMap.put("EXCEP_ITEM_RELATION_MODIFIED","对应关系改动");
        excepToChineseMap.put("EXCEP_STOCK_INSUFFICIENT","库存不足");
        excepToChineseMap.put("EX_CHANGE_ADDRESS","平台更换地址");
        excepToChineseMap.put("EX_CHANGE_ITEM","平台更换商品");
        excepToChineseMap.put("EX_UPDATED_SELLERMEMO","平台修改备注");
        excepToChineseMap.put("EX_BLACK","黑名单");
        excepToChineseMap.put("EX_LOST_MSG","订单信息缺失");
        excepToChineseMap.put("EX_UNATTAINABLE","快递异常");
        excepToChineseMap.put("EX_PART_REFUND","部分关闭");
        excepToChineseMap.put("EX_RISK_ORDER","风控订单");
        excepToChineseMap.put("EX_DELIVER","发货异常");
        excepToChineseMap.put("EX_UPLOAD_DELIVER","上传异常");
        excepToChineseMap.put("EX_SUITE_QUANTITY_CHANGE","套件信息修改");
        excepToChineseMap.put("EX_COD_REPEAT","重复货到付款订单");
        excepToChineseMap.put("EX_STOCK_OUT","已缺货处理");
        excepToChineseMap.put("EX_WAITING_RETURN_WMS","等待退货入仓");
        excepToChineseMap.put("EX_WAIT_MERGE","等待合并");
        excepToChineseMap.put("EX_ITEM_SHUTOFF","商品停用");
        excepToChineseMap.put("PLATFORM_WAREHOUSE_MAPPING_EXCEPTION","平台仓未匹配");
        excepToChineseMap.put("EX_UNIQUE_CODE_OFFSHELF","商品下架");

        JDVC_GXD_CARRIER_MAP.put("3046","德邦快递");
        JDVC_GXD_CARRIER_MAP.put("2087","京东快递");
        JDVC_GXD_CARRIER_MAP.put("773574","京东快运");
        JDVC_GXD_CARRIER_MAP.put("4832","安能物流");
        JDVC_GXD_CARRIER_MAP.put("599866","跨越速运");
    }

    /**
     * 异常状态：挂起
     */
    public static final String STATUS_EXCEP_HALT = "EXCEP_HALT";

    /**
     * 黑名单异常
     */
    public static final String STATUS_EXCEP_BLACK = "EX_BLACK";

    /**
     * 异常状态：退款订单
     */
    public static final String STATUS_EXCEP_REFUND = "EXCEP_REFUND";
    /**
     * 异常状态：商品未匹配异常
     */
    public static final String STATUS_EXCEP_ITEM_UNALLOCATED = "EXCEP_ITEM_UNALLOCATED";
    /**
     * 异常状态：系统商品对应关系修改
     */
    public static final String STATUS_EXCEP_ITEM_RELATION_MODIFIED = "EXCEP_ITEM_RELATION_MODIFIED";
    /**
     * 异常状态：库存不足
     */
    public static final String STATUS_EXCEP_STOCK_INSUFFICIENT = "EXCEP_STOCK_INSUFFICIENT";
    /**
     * 平台变更地址
     */
    public static final String STATUS_EXCEP_ADDRESS_CHANGED = "EX_CHANGE_ADDRESS";
    /**
     * 平台分销商品待拆分
     */
    public static final String STATUS_EXCEP_PLATFORM_FX_SPLIT_EXCEPT = "EX_PLATFORM_FX_SPLIT_EXCEPT";
    /**
     * 平台更换商品
     */
    public static final String STATUS_EXCEP_ITEM_CHANGED = "EX_CHANGE_ITEM";
    /**
     * 平台修改备注异常
     */
    public static final String STATUS_EXCEP_SELLERMEMO_UPDATED = "EX_UPDATED_SELLERMEMO";
    /**
     * 部分退款异常
     */
    public static final String STATUS_EXCEP_PART_REFUND = "EX_PART_REFUND";
    /**
     * 快递异常
     */
    public static final String STATUS_EXCEP_UNATTAINABLE = "EX_UNATTAINABLE";
    /**
     * 风控异常订单
     */
    public static final String STATUS_EXCEP_RISK_ORDER = "EX_RISK_ORDER";

    /**
     * 地址、手机/固话、收件人、商品信息缺失的异常
     */
    public static final String STATUS_EXCEP_LOST_MSG = "EX_LOST_MSG";
    /**
     * pdd缺货已处理异常
     */
    public static final String STATUS_EXCEP_STOCK_OUT = "EX_STOCK_OUT";
    /**
     * 商品停用异常
     */
    public static final String STATUS_EXCEP_ITEM_SHUTOFF = "EX_ITEM_SHUTOFF";
    /**
     * 供分销补发单同步导入
     */
    public static final String GX_TRADE_REISSUE_SYNC_IMPORT = "GX.TRADE.REISSUE.SYNC.IMPORT";

    /**
     * 天猫物流升级平台仓映射异常
     */
    public static final String STATUS_PLATFORM_WAREHOUSE_MAPPING_EXCEPTION = "PLATFORM_WAREHOUSE_MAPPING_EXCEPTION";
    /**
     * 退货入仓
     */
    public static final String EX_WAITING_RETURN_WMS="EX_WAITING_RETURN_WMS";



    /**
     * 1024 分销强推分销商品未匹配
     */
    public static final int V_IF_FX_FORCE_PUSH_UNALLOCATED = 1 << 10;


    public static final long PT_REFUND = 1 << 1;

    public static final long PT_ITEM_CHANGE = 1 << 2;

    public static final long PT_ADDRESS_CHANGE = 1 << 3;

    public static final long PT_SELLER_MEMO_CHANGE = 1 << 4;

    public static final long PT_PART_CONSIGN = 1 << 5;

    public static final long PT_CONSIGN = 1 << 6;

    public static final long PT_PART_SUCCESS = 1 << 7;

    public static final long PT_SUCCESS = 1 << 8;

    public static final long PT_PART_CLOSED =  1 << 9;

    public static final long PT_CLOSED =  1 << 10;

    public static final String FXG_SUFFIX = "A";

    /**
     * 快递助手
     */
    public static final String TRADE_USER_KDZS= "快递助手";

    /**
     * 供销修改订单商品
     */
    public static final String TRADE_ITEM_EDIT_FROM_GX = "TRADE_ITEM_EDIT_FROM_GX";

    /**
     * 1 已取消平台换地址异常（反审核时会取消该标记）
     */
    public static final int V_CANCEL_ADDRESS_CHANGE_EXCEPT = 1;

    /**
     * 2 地址已处理
     */
    public static final int V_ADDRESS_PROCESSED = 1 << 1;

    /**
     * 4 上传失败标记
     */
    public static final int V_UPLOAD_EXCEPT = 1 << 2;

    /**
     * 8 已取消单价异常标签
     */
    public static final int V_CANCEL_PRICE_EXCEPT = 1 << 3;

    /**
     * 16 快麦通代发标记
     */
    public static final int V_IF_KMT_DF = 1 << 4;

    /**
     * 32 快卖通现成场自提
     */
    public static final int V_IF_KMT_SELF_PICK = 1 << 5;

    /**
     * 64 分销强推
     */
    public static final int V_IF_FX_FORCE_PUSH = 1 << 6;

    /**
     * 128 奇门分销（奇门供销单新增分销属性）
     */
    public static final int V_IF_FX_FROM_QIMEN = 1 << 7;

    /**
     * 256 供销或分销且供销订单（来源于奇门分销单推送）
     */
    public static final int V_IF_MIX_FROM_QIMEN = 1 << 8;

    /**
     * 512 快递助手
     */
    public static final int V_IF_KDZS = 1 << 9;


    /**
     * 2048 是否多级供销
     */
    public static final int V_IF_MULTI_GX = 1 << 11;


    /**
     * 4096 是否供销修改过商品
     */
    public static final int V_IF_GX_EDIT_TRADE_ITEM = 1 << 12;

    /**
     * 8192 是否匹配业务员 目前仅快麦通小程序
     */
    public static final int V_IF_AUTO_MATCH_SALESMAN = 1 << 13;

    /**
     * 是否
     */
    public static final int V_IF_SAVE_INVOICES = 1 << 14;

    /**
     * 奇门待分销商付款订单
     */
    public static final int V_QIMEN_FX_WAIT_PAY = 1 << 15;

    /**
     * 65536 复制订单
     */
    public static final int V_TRADE_COPY = 1 << 16;

    /**
     * 分销是否修改过商品
     */
    public static final int V_IF_FX_EDIT_TRADE_ITEM = 1 << 21;


    /**
     * 131072 奇门供销&流水类型为预扣
     */
    public static final int V_TRADE_QIMEN_CASH_FLOW = 1 << 17;


    /**
     * 262144 是否分销指定快递模板
     */
    public static final int V_IF_FX_APPOINT_TEMPLATE_ID = 1 << 18;

    /**
     * 524288 分销价异常
     */
    public static final int V_TRADE_FX_PRICE_EXCEPT = 1 << 19;


    /**
     * 是否追加过平台备注
     */
    public static final int V_FX_APPEND_SELLER_MEMO = 1 << 20;

    /**
     * 4194304 供销商品执行过修改,且后续业务不需要关联修改分销订单
     */
    public static final int V_GX_ITEM_EDIT_NOT_REL_FX = 1 << 22;

    /**
     *8388608 是否1688分销小店分销角色
     */
    public static final int V_IF_1688_FX_ROLE = 1 << 23;




}


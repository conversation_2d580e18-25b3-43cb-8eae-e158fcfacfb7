package com.raycloud.dmj.domain.trades.warehouse;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-10-10
 * @description 跨境平台仓库表
 */
@Data
@Accessors(chain = true)
@Table(name = "cross_border_warehouses", migratable = false)
public class CrossBorderWarehouses extends Model {
    private static final long serialVersionUID = 469049237992710586L;
    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 平台
     */
    private String source;

    /**
     * 平台仓库编码
     */
    private String code;

    /**
     * 平台仓库名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 数据状态 1：有效 0：无效 默认为1
     */
    private Boolean enableStatus;

    private String extInfo;
}

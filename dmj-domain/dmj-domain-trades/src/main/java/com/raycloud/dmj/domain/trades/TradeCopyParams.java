package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.request.SecurityEventTrackingBatchOrderRequest;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class TradeCopyParams implements Serializable {

    private static final long serialVersionUID = -2071130092802321039L;

    /**
     * 要复制的订单号
     */
    private Long sid;

    /**
     * 批量复制的订单号
     */
    private Long[] sids;

    /**
     * 是否强制复制（忽略库存不足）
     */
    private boolean force;

    /**
     * 是否复制商品备注
     */
    private boolean copyOrderExtOrderRemark;

    /**
     * 复制数量
     */
    private Integer copyNum;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 复制时新增加的orders
     * https://gykj.yuque.com/entavv/xb9xi5/xnk9tp
     */
    private List<Order> orders;

    private SecurityEventTrackingBatchOrderRequest fxgSecurityRequest;

    /**
     * 复制时失败的订单
     * https://gykj.yuque.com/entavv/xb9xi5/cke0or
     */
    private Map<Long, String> failSid;

    /**
     * 复制新建过滤交易关闭的商品
     */
    private boolean copyFilterClosedOrders;

    public boolean isCopyFilterClosedOrders() {
        return copyFilterClosedOrders;
    }
    public void setCopyFilterClosedOrders(boolean copyFilterClosedOrders) {
        this.copyFilterClosedOrders = copyFilterClosedOrders;
    }
    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long[] getSids() {
        return sids;
    }

    public void setSids(Long[] sids) {
        this.sids = sids;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public boolean isCopyOrderExtOrderRemark() {
        return copyOrderExtOrderRemark;
    }

    public void setCopyOrderExtOrderRemark(boolean copyOrderExtOrderRemark) {
        this.copyOrderExtOrderRemark = copyOrderExtOrderRemark;
    }

    public Integer getCopyNum() {
        return copyNum;
    }

    public void setCopyNum(Integer copyNum) {
        this.copyNum = copyNum;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public SecurityEventTrackingBatchOrderRequest getFxgSecurityRequest() {
        return fxgSecurityRequest;
    }

    public void setFxgSecurityRequest(SecurityEventTrackingBatchOrderRequest fxgSecurityRequest) {
        this.fxgSecurityRequest = fxgSecurityRequest;
    }

    public Map<Long, String> getFailSid() {
        return failSid;
    }

    public void setFailSid(Map<Long, String> failSid) {
        this.failSid = failSid;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private Long sid;
        private Long[] sids;
        private boolean force;
        private Integer copyNum;
        private Long userId;
        private List<Order> orders;

        private Builder() {
        }

        public Builder sid(Long sid) {
            this.sid = sid;
            return this;
        }

        public Builder sids(Long[] sids) {
            this.sids = sids;
            return this;
        }

        public Builder force(boolean force) {
            this.force = force;
            return this;
        }

        public Builder copyNum(Integer copyNum) {
            this.copyNum = copyNum;
            return this;
        }

        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public Builder orders(List<Order> orders) {
            this.orders = orders;
            return this;
        }

        public TradeCopyParams build() {
            TradeCopyParams tradeCopyParams = new TradeCopyParams();
            tradeCopyParams.setSid(sid);
            tradeCopyParams.setSids(sids);
            tradeCopyParams.setForce(force);
            tradeCopyParams.setCopyNum(copyNum);
            tradeCopyParams.setUserId(userId);
            tradeCopyParams.setOrders(orders);
            return tradeCopyParams;
        }
    }
}

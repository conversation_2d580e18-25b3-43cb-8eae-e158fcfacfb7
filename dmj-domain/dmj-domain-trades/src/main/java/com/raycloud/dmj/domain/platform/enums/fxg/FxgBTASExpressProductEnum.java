package com.raycloud.dmj.domain.platform.enums.fxg;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 抖店BTAS质检订单，二段快递产品枚举
 */
public enum FxgBTASExpressProductEnum {

    /**
     * 顺丰快递
     */
    SF_HIGH(0, "1", "顺丰特快", "顺丰特快"),

    SF_STANDARD(1, "2", "顺丰标快", "顺丰标快"),

    SF_TODAY(3, "3", "顺丰即日", "顺丰即日"),

    SF_CSB_HIGH(4, "4", "便利封/袋(特快)", "顺丰便利封/袋(特快)"),

    SF_SO_D(5, "5", "顺丰特惠D", "顺丰特惠D"),

    SF_AIR(6, "6", "顺丰空配", "顺丰空配"),

    SF_HEAVY(7, "7", "重货包裹", "顺丰重货包裹"),

    SF_STANDARD_LTT(8, "8", "标准零担", "顺丰标准零担"),

    SF_BAG_HIGH(9, "9", "特快包裹", "顺丰特快包裹"),

    SF_CT_STANDARD(10, "10", "冷运标快", "顺丰冷运标快"),

    SF_WXJ(11, "11", "顺丰微⼩件", "顺丰微⼩件"),

    SF_SO_SC(12, "12", "特惠专配", "顺丰特惠专配"),

    SF_BT_DD(13, "13", "⼤票直送", "顺丰⼤票直送"),

    SF_JW_ZD(14, "14", "精温专递", "顺丰精温专递"),

    SF_BAG_LY(15, "15", "陆运包裹", "顺丰陆运包裹"),

    SF_JW_ZD_YBL(16, "16", "精温专递(样本陆)", "顺丰精温专递(样本陆)"),

    SF_JXQZ_YS(17, "17", "极效前置-预售", "顺丰极效前置-预售"),

    SF_CZ_TP(18, "18", "纯重特配", "顺丰纯重特配"),

    SF_EC_STANDARD(19, "19", "电商标快", "顺丰电商标快"),

    SF_RC_DB(20, "20", "⼊仓电标", "顺丰⼊仓电标"),

    SF_TC_DB(21, "21", "填舱电标", "顺丰填舱电标"),

    /**
     * 京东快递
     */
    JD_SX_SO(25, "25", "⽣鲜特惠", "京东⽣鲜特惠"),

    JD_SX_HIGH(26, "26", "⽣鲜特快", "京东⽣鲜特快"),

    JD_THS(27, "27", "特惠送", "京东特惠送"),

    JD_TKS(28, "28", "特快送", "京东特快送"),

    JD_BAG_SO(29, "29", "特惠包裹", "京东特惠包裹"),

    JD_HSD(30, "30", "函速达", "京东函速达"),

    JD_XJ_SO(31, "31", "特惠⼩件", "京东特惠⼩件"),

    JD_EC_SO(32, "32", "电商特惠", "京东电商特惠"),

    JD_LL_ZS(33, "33", "冷链专送", "京东冷链专送"),

    JD_YY_ZS(34, "34", "医药专送", "京东医药专送"),

    /**
     * EMS快递
     */
    EMS_HIGH(40, "40", "特快包裹", "ems特快包裹"),

    /**
     * 德邦物流
     */
    DB_TZKJ(45, "45", "特准快件", "德邦特准快件"),

    DB_ZBRH(46, "46", "重包⼊户", "德邦重包⼊户"),

    DB_BZKD(47, "47", "标准快递", "德邦标准快递"),

    DB_DJKD_360(48, "48", "⼤件快递360", "德邦⼤件快递360"),

    DB_TKZD(49, "49", "特快专递", "德邦特快专递"),

    DB_WXJ_SO(50, "50", "微⼩件特惠", "德邦微⼩件特惠"),

    DB_ZBTH(51, "51", "重包特惠", "德邦重包特惠"),

    /**
     * 邮政国内
     */
    YZGN_YZ_BAG(55, "55", "邮政包裹", "邮政包裹"),

    ;

    FxgBTASExpressProductEnum(Integer key, String platform, String platName, String name) {
        this.key = key;
        this.platform = platform;
        this.platName = platName;
        this.name = name;
    }

    /**
     * ERP内的标识
     */
    private Integer key;

    /**
     * 质检站与抖店平台约定的标识
     */
    private String platform;

    /**
     * 快递产品名称
     */
    private String platName;

    /**
     * ERP内展示的快递产品名称
     */
    private String name;

    public static final Map<Integer, FxgBTASExpressProductEnum> fxgBTASExpressProductEnumMap = new HashMap<>();

    static {
        for (FxgBTASExpressProductEnum fxgBTASExpressProductEnum : values()){
            fxgBTASExpressProductEnumMap.put(fxgBTASExpressProductEnum.getKey(), fxgBTASExpressProductEnum);
        }
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatName() {
        return platName;
    }

    public void setPlatName(String platName) {
        this.platName = platName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static FxgBTASExpressProductEnum getByKey(Integer key){
        FxgBTASExpressProductEnum fxgBTASExpressProductEnum = fxgBTASExpressProductEnumMap.get(key);
        if (Objects.isNull(fxgBTASExpressProductEnum)){
            return SF_HIGH;
        }
        return fxgBTASExpressProductEnum;
    }

}

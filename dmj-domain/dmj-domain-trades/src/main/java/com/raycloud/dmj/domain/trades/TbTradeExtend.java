package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Table;

import java.io.Serializable;
import java.sql.Types;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON>(王超)
 * @Date: 2019/6/29 1:05 PM
 * @Version 1.0
 * @Email <EMAIL>
 * 交易扩展表
 */
@Table(name = "trade_ext")
public class TbTradeExtend implements Serializable {

    /**
     * 序列化id
     */
    private static final long serialVersionUID = -5565516102246326004L;


    /**
     * 客户id
     */
    @Column(name = "customer_id", type = Types.BIGINT)
    private Long customerId;

    /**
     * 提货方式 1-自提 2-快递邮寄
     */
    @Column(name = "pick_goods_type", type = Types.INTEGER)
    private Integer pickGoodsType;

    @Column(name = "created", type = Types.DATE)
    private Date created;

    @Column(name = "modified", type = Types.DATE)
    private Date modified;

    /**
     * 订单id
     */
    private Long sid;

    /**
     * 客户名称
     */
    @Column(name = "customer_name", type = Types.VARCHAR)
    private String customerName;

    private Long companyId;


    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Integer getPickGoodsType() {
        return pickGoodsType;
    }

    public void setPickGoodsType(Integer pickGoodsType) {
        this.pickGoodsType = pickGoodsType;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}

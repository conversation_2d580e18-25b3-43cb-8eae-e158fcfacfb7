package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
* @description
* <AUTHOR>
* @date 2022/10/25 10:13
*/
public class CooperationExceptUtils {

    private static final Logger logger = Logger.getLogger(CooperationExceptUtils.class);

    /**
     * 获取店铺配置的常态合作编码
     *
     * @param user
     * @return
     */
    @Deprecated
    public static List<String> getCooperationNoList(User user) {
        if (user.getUserConf() == null){
            return null;
        }
        Map<String, Object> sellerInfo = user.getUserConf().getSellerInfo();
        if (null == sellerInfo) {
            return null;
        }
        if (!sellerInfo.containsKey("cooperationNo")) {
            return null;
        }
        String cooperationNo = String.valueOf(sellerInfo.get("cooperationNo"));
        if (org.apache.commons.lang3.StringUtils.isBlank(cooperationNo)){
            return null;
        }
        return Arrays.asList(StringUtils.split(cooperationNo, ","));
    }

    /**
     * 判断原单是否有常态合作码异常
     * @param originTrade
     */
    @Deprecated
    public static boolean hasCooperationNoExcept(Staff staff,Trade originTrade){
        if (originTrade == null || originTrade.getExcep() == null){
            return false;
        }
        return TradeExceptUtils.isContainExcept(staff,originTrade, ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH);
    }

    /**
     * 添加常态合作码异常
     * @param trade
     */
    @Deprecated
    public static boolean addCooperationNoExcept(Staff staff ,Trade trade) {
        if (trade == null){
            return false;
        }
        Trade originTrade = trade.getOrigin();

        // 有合作码异常直接返回
        if (hasCooperationNoExcept(staff,originTrade)){
            return false;
        }
        TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH,1L);
        // 无原单为新进入系统订单
       /* if (originTrade != null) {
            TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH,1L);
            if (trade.getExcep() == null) {
                trade.setExcep(originTrade.getExcep() | TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH);
            } else {
                trade.setExcep(trade.getExcep() | TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH);
            }
        } else {
            trade.setExcep(trade.getExcep() == null ? TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH : (trade.getExcep() | TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH));
        }*/
        // 重新计算异常
        computeTradeExcept(staff,trade);
        return true;
    }

    /**
     * 移除常态合作码异常(订单同步)
     * @param trade
     */
    @Deprecated
    public static boolean removeCooperationNoExcept(Staff staff,Trade trade) {
        if (trade == null) {
            return false;
        }
        Trade originTrade = trade.getOrigin();

        // 无合作码异常直接返回
        if (!hasCooperationNoExcept(staff,originTrade)){
            return false;
        }
        TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH,0L);
       /* if (trade.getExcep() == null) {
            trade.setExcep(originTrade.getExcep() - TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH);
        } else {
            trade.setExcep(trade.getExcep() - TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH);
        }*/
        // 重新计算订单异常
        computeTradeExcept(staff,trade);
        return true;
    }

    /**
     * 移除常态合作码异常（页面处理)
     */
    @Deprecated
    public static boolean removeCooperationNoExceptOrigin(Staff staff,Trade originTrade){
        if (originTrade == null) {
            return false;
        }

        if(!hasCooperationNoExcept(staff,originTrade)){
            return false;
        }
        TradeExceptUtils.updateExcept(staff,originTrade,ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH,0L);
        return true;
      /*  if (originTrade.getExcep() != null) {
            originTrade.setExcep(originTrade.getExcep() - TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH);
            computeTradeExcept(staff,originTrade);
            return true;
        }else {
            return false;
        }*/
    }

    /**
     * 重新计算订单异常
     */
    @Deprecated
    public static void computeTradeExcept(Staff staff, Trade trade) {
        Integer oldExcept = trade.getIsExcep();
        TradeUtils.setTradeExcep(staff,trade);
        if (logger.isDebugEnabled()) {
            logger.debug(String.format("订单[userId=%s,sid=%s]异常状态计算[%s -> %s]", trade.getUserId(), trade.getSid(), oldExcept, trade.getIsExcep()));
        }
    }

    /**
     * 是否使用常态合作码异常处理
     *
     * @param user
     * @return
     */
    public static boolean isUseCooperationNoExceptHandler(User user) {
        if (user == null || user.getStaff() == null || user.getStaff().getCompanyId() == null) {
            return false;
        }
        return ConfigHolder.VIP_ORDER_SYNC_CONFIG.isAllUseCooperationNoExceptHandler() || ConfigHolder.VIP_ORDER_SYNC_CONFIG.getCooperationNoExceptCompanyIdSet().contains(user.getStaff().getCompanyId().toString());
    }

}

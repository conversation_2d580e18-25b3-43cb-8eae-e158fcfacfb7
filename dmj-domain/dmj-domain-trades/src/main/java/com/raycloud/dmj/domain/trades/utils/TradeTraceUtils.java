package com.raycloud.dmj.domain.trades.utils;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.*;

/**
 * TradeTraceUtils
 *
 * <AUTHOR>
 * @Date 2018/11/24
 * @Time 17:19
 */
public class TradeTraceUtils {

    public static List<Trade> simplifyWithOrders(Collection<Trade> trades, boolean withOrders){
        List<Trade> list = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            list.add(simplifyWithOrders(trade, withOrders));
        }
        return list;
    }

    public static Trade simplifyWithOrders(Trade trade, boolean withOrders){
        Trade tempTrade = new TbTrade();
        tempTrade.setSid(trade.getSid());
        tempTrade.setUserId(trade.getUserId());
        tempTrade.setSource(trade.getSource());
        tempTrade.setSourceId(trade.getSourceId());
        tempTrade.setDestId(trade.getDestId());
        tempTrade.setConvertType(trade.getConvertType());
        tempTrade.setBelongType(trade.getBelongType());
        tempTrade.setV(trade.getV());
        tempTrade.setOpVEnumSet(trade.getOpVEnumSet());
        tempTrade.setSubSource(trade.getSubSource());
        tempTrade.setPayTime(trade.getPayTime());
        tempTrade.setOperations(trade.getOperations());
        tempTrade.setTid(trade.getTid());
        tempTrade.setIsExcep(trade.getIsExcep());
        tempTrade.setTagIds(trade.getTagIds());
        tempTrade.setExceptIds(trade.getExceptIds());
        tempTrade.setCreated(trade.getCreated());
        tempTrade.setMergeSid(trade.getMergeSid());
        tempTrade.setMergeType(trade.getMergeType());
        tempTrade.setSplitSid(trade.getSplitSid());
        tempTrade.setSplitType(trade.getSplitType());
        tempTrade.setStatus(trade.getStatus());
        tempTrade.setSysStatus(trade.getSysStatus());
        tempTrade.setPayment(trade.getPayment());
        tempTrade.setDiscountFee(trade.getDiscountFee());
        tempTrade.setItemNum(trade.getItemNum());
        tempTrade.setItemKindNum(trade.getItemKindNum());
        tempTrade.setItemExcep(trade.getItemExcep());
        tempTrade.setPayAmount(trade.getPayAmount());
        tempTrade.setExceptData(trade.getExceptData());
        if(withOrders){
            TradeUtils.getOrders4Trade(trade).forEach(order -> {
                simpifyOrder(order);
            });
        }

        // TODO fixme add new fields
        return tempTrade;
    }

    public static List<Trade> simplify(Collection<Trade> trades) {
        return simplifyWithOrders(trades, false);
    }

    public static Trade simplify(Trade trade) {
        return simplifyWithOrders(trade, false);
    }

    public static Trade simplifyWithTrace(Trade trade, String content){
        Trade tempTrade = simplify(trade);
        tempTrade.setTradeTrace(content);
        return tempTrade;
    }

    static TbOrder simpifyOrder(Order order){
        TbOrder newOrder = new TbOrder();
        newOrder.setId(order.getId());
        newOrder.setNumIid(order.getNumIid());
        newOrder.setSkuId(order.getSkuId());
        newOrder.setOuterIid(order.getOuterIid());
        newOrder.setOuterSkuId(order.getOuterSkuId());
        newOrder.setSysOuterId(order.getSysOuterId());
        newOrder.setItemSysId(order.getItemSysId());
        newOrder.setSkuSysId(order.getSkuSysId());
        newOrder.setSysStatus(order.getSysStatus());
        newOrder.setStatus(order.getStatus());
        newOrder.setPayment(order.getPayment());
        newOrder.setNum(order.getNum());
        newOrder.setStockNum(order.getStockNum());
        newOrder.setStockStatus(order.getStockStatus());
        newOrder.setRefundStatus(order.getRefundStatus());
        newOrder.setRefundId(order.getRefundId());
        newOrder.setPayAmount(order.getPayAmount());
        newOrder.setExceptData(order.getExceptData());
        return newOrder;
    }

    /**
     *
     * @param staff
     * @param sid
     * @param taobaoId
     * @param action
     * @param content
     * @return
     * @deprecated
     */
    public static TradeTrace createTradeTrace(Staff staff, Long sid, Long taobaoId, String action, String content) {
        return TradeTrace.builder()
                .action(action)
                .created(new Date())
                .operateTime(new Date())
                .operator(staff.getName())
                .companyId(staff.getCompanyId())
                .sid(sid)
                .taobaoId(taobaoId)
                .content(StringUtils.isEmpty(content) ? action : content)
                .build();
    }

    /**
     *
     * @param sid
     * @param content
     * @return
     * @deprecated 请使用 {@link #simplifyWithTrace(Trade, String)}
     */
    public static Trade createTrade(Long sid, String content) {
        Trade trade = new Trade();
        trade.setSid(sid);
        trade.setTradeTrace(content);
        return trade;
    }

    /**
     *
     * @param sids
     * @param content
     * @return
     * @deprecated
     */
    public static List<Trade> createTradeS(List<Long> sids, String content) {
        if (CollectionUtils.isNotEmpty(sids)) {
            List<Trade> result = new ArrayList<Trade>();
            for (Long sid : sids) {
                result.add(createTrade(sid, content));
            }
            return result;
        } else {
            return Lists.newArrayList();
        }
    }

    /**
     * 构建完整的tradeTrace轨迹
     * @param companyId
     * @param trade
     * @param action
     * @param operator
     * @param matchTime
     * @param content
     * @return
     */
    public static TradeTrace createTradeTraceWithTrade(Long companyId, Trade trade, String action, String operator, Date matchTime, String content){
        return createTradeTraceWithTrade(companyId, trade, action, operator, matchTime, content, false);
    }

    /**
     * 构建完整的tradeTrace轨迹
     * @param companyId
     * @param trade
     * @param action
     * @param operator
     * @param matchTime
     * @param content
     * @param recordOrder 是否需要记录order的信息
     * @return
     */
    public static TradeTrace createTradeTraceWithTrade(Long companyId, Trade trade, String action, String operator, Date matchTime, String content, boolean recordOrder){
        TradeTrace trace = new TradeTrace();
        trace.setCompanyId(companyId);
        trace.setSid(trade.getSid());
        trace.setAction(action);
        trace.setOperator(operator);
        trace.setOperateTime(matchTime);
        trace.setContent(StringUtils.isEmpty(content) ? action : content);
        trace.setEnableStatus(1);
        trace.setTid(trade.getTid());
        trace.setTaobaoId(trade.getTaobaoId());
        trace.setCreated(new Date());
        // 设置额外属性
        putExtProperties(trace, trade, recordOrder);
        return trace;
    }

    /**
     * 构建完整的tradeTrace轨迹
     * @param staff
     * @param trade
     * @param action
     * @param operator
     * @param matchTime
     * @param content
     * @return
     */
    public static TradeTrace createTradeTraceWithTrade(Staff staff, Trade trade, String action, String operator, Date matchTime, String content){
        return createTradeTraceWithTrade(staff.getCompanyId(), trade, action, operator, matchTime, content);
    }

    /**
     * 构建完整的tradeTrace轨迹
     * @param staff
     * @param trade
     * @param action
     * @param operator
     * @param matchTime
     * @param content
     * @return
     */
    public static TradeTrace createTradeTraceWithTrade(Staff staff, Trade trade, String action, String operator, Date matchTime, String content, boolean recordOrder){
        return createTradeTraceWithTrade(staff.getCompanyId(), trade, action, operator, matchTime, content, recordOrder);
    }

    static TradeTrace putExtProperties(TradeTrace tradeTrace, Trade trade, boolean recordOrder){
        putProperty2Trace(tradeTrace, TradeTraceAttrs.TID, trade.getTid());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.USER_ID, trade.getUserId());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SOURCE, trade.getSource());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SUB_SOURCE, trade.getSubSource());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.STATUS, trade.getStatus());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SYS_STATUS, trade.getSysStatus());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.CREATED, trade.getCreated());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.MODIFIED, trade.getModified());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.PAY_TIME, trade.getPayTime());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.IS_EXCEP, trade.getIsExcep());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.TAG_IDS, trade.getTagIds());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.EXCEPT_IDS, trade.getExceptIds());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.MERGE_SID, trade.getMergeSid());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.MERGE_TYPE, trade.getMergeType());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SPLIT_SID, trade.getSplitSid());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SPLIT_TYPE, trade.getSplitType());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.PAYMENT, trade.getPayment());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.DISCOUNT_FEE, trade.getDiscountFee());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ITEM_NUM, trade.getItemNum());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ITEM_KIND_NUM, trade.getItemKindNum());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ITEM_EXCEP, trade.getItemExcep());
        putProperty2Trace(tradeTrace, TradeTraceAttrs.PAY_AMOUNT, trade.getPayAmount());
        if(trade.getIfKmtDf()){
            putProperty2Trace(tradeTrace, TradeTraceAttrs.IF_KMT_DF, 1);
        }
        if(trade.getV() != null && (trade.getV() | TradeConstants.V_IF_KDZS) - trade.getV() == 0){
            putProperty2Trace(tradeTrace, TradeTraceAttrs.IF_KDZS, 1);
            if(trade.getSourceId()!=null && trade.getSourceId()>0L){
                putProperty2Trace(tradeTrace, TradeTraceAttrs.SOURCE_ID, trade.getSourceId());
            }
        }
        if(TradeUtils.isFxTrade(trade)){
            putProperty2Trace(tradeTrace, TradeTraceAttrs.IF_FX, 1);
        }
        if(TradeUtils.isFxSource(trade)){
            putProperty2Trace(tradeTrace, TradeTraceAttrs.DEST_ID, trade.getDestId());
        }
        if(TradeUtils.isGxAndFxTrade(trade)){
            putProperty2Trace(tradeTrace, TradeTraceAttrs.IF_GX_AND_FX, 1);
        }
        if(recordOrder){
            try {
                putOrderProperty2Trace(tradeTrace, TradeUtils.getOrders4Trade(trade));
            }catch(Exception e){
                Logger.getLogger(TradeTraceUtils.class).error(e.getMessage(), e);
            }
        }


        return calculateDelay(tradeTrace);
    }

    static TradeTrace putOrderProperty2Trace(TradeTrace tradeTrace, List<Order> orders){
        if(null == orders || orders.size() == 0){
            return tradeTrace;
        }

        List<String> orderIds = new ArrayList<>(orders.size());
        List<String> tbItemIds = new ArrayList<>(orders.size());
        List<String> tbSkuIds = new ArrayList<>(orders.size());
        List<String> outerIds = new ArrayList<>(orders.size());
        List<String> outerSkuIds = new ArrayList<>(orders.size());
        List<String> sysOuterIds = new ArrayList<>(orders.size());
        List<String> sysItemIds = new ArrayList<>(orders.size());
        List<String> sysSkuIds = new ArrayList<>(orders.size());
        List<String> sysStatusList = new ArrayList<>(orders.size());
        List<String> statusList = new ArrayList<>(orders.size());
        List<String> orderPayments = new ArrayList<>(orders.size());
        List<Integer> itemNums = new ArrayList<>(orders.size());
        List<Integer> stockNums = new ArrayList<>(orders.size());
        List<String> stockStatus = new ArrayList<>(orders.size());
        List<String> refundStatus = new ArrayList<>(orders.size());
        List<String> refundIds = new ArrayList<>(orders.size());
        List<String> orderPayAmounts = new ArrayList<>(orders.size());

        orders.forEach(order -> {
            orderIds.add(order.getId() + "");
            tbItemIds.add(order.getNumIid());
            tbSkuIds.add(order.getSkuId());
            outerIds.add(order.getOuterIid());
            outerSkuIds.add(order.getOuterSkuId());
            sysOuterIds.add(order.getSysOuterId());
            sysItemIds.add(order.getItemSysId() + "");
            sysSkuIds.add(order.getSkuSysId() + "");
            sysStatusList.add(order.getSysStatus());
            statusList.add(order.getStatus());
            orderPayments.add(order.getPayment());
            itemNums.add(order.getNum());
            stockNums.add(order.getStockNum());
            stockStatus.add(order.getStockStatus());
            refundStatus.add(order.getRefundStatus());
            refundIds.add(order.getRefundId());
            orderPayAmounts.add(order.getPayAmount());
        });

        putProperty2Trace(tradeTrace, TradeTraceAttrs.ORDER_IDS, StringUtils.join(orderIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.TB_ITEM_IDS, StringUtils.join(tbItemIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.TB_SKU_IDS, StringUtils.join(tbSkuIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.OUTER_IDS, StringUtils.join(outerIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.OUTER_SKU_IDS, StringUtils.join(outerSkuIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SYS_OUTER_IDS, StringUtils.join(sysOuterIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SYS_ITEM_IDS, StringUtils.join(sysItemIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.SYS_SKU_IDS, StringUtils.join(sysSkuIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ORDER_SYS_STATUS, StringUtils.join(sysStatusList, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ORDER_STATUS, StringUtils.join(statusList, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ORDER_PAYMENTS, StringUtils.join(orderPayments, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ITEM_NUMS, StringUtils.join(itemNums, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.STOCK_NUMS, StringUtils.join(stockNums, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.STOCK_STATUS, StringUtils.join(stockStatus, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.REFUND_STATUS, StringUtils.join(refundStatus, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.REFUND_IDS, StringUtils.join(refundIds, ","));
        putProperty2Trace(tradeTrace, TradeTraceAttrs.ORDER_PAY_AMOUNTS, StringUtils.join(orderPayAmounts, ","));
        return tradeTrace;
    }

    static TradeTrace putProperty2Trace(TradeTrace tradeTrace, String key, Object value){
        if(null == value){
            return tradeTrace;
        }
        if(value instanceof String && StringUtils.isEmpty((String)value)){
            return tradeTrace;
        }
        tradeTrace.getExtProperties().put(key, value);
        return tradeTrace;
    }

    public static List<TradeTrace> createTradeTraceWithTrades(Staff staff, List<Trade> trades, String action, String operator, Date matchTime, String content){
        List<TradeTrace> tradeTraces = new ArrayList<>(trades.size() + 1);
        for (Trade trade : trades) {
            tradeTraces.add(createTradeTraceWithTrade(staff, trade, action, operator, matchTime, content));
        }
        return tradeTraces;
    }

    /**
     * 构建订单日志
     *
     * @param companyId companyId
     * @param sid       sid
     * @param action    操作名
     * @param operator  操作人
     * @param matchTime 操作时间
     * @param content   操作详情
     * @deprecated 请使用 {@link #createTradeTraceWithTrade(Staff, Trade, String, String, Date, String)}
     */
    public static TradeTrace createTradeTrace(Long companyId, Long sid, String action, String operator, Date matchTime, String content) {
        TradeTrace trace = new TradeTrace();
        trace.setCompanyId(companyId);
        trace.setSid(sid);
        trace.setAction(action);
        trace.setOperator(operator);
        trace.setOperateTime(matchTime);
        trace.setContent(content);
        trace.setEnableStatus(1);
        return trace;
    }

    /**
     * 构建订单日志
     *
     * @param companyId companyId
     * @param sids      sids
     * @param action    操作名
     * @param operator  操作人
     * @param matchTime 操作时间
     * @param content   操作详情
     * @deprecated 请使用 {@link #createTradeTraceWithTrades(Staff, List, String, String, Date, String)}
     */
    public static List<TradeTrace> createTradeTrace(Long companyId, List<Long> sids, String action, String operator, Date matchTime, String content) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        for (Long sid : sids) {
            tradeTraces.add(createTradeTrace(companyId, sid, action, operator, matchTime, content));
        }
        return tradeTraces;
    }

    /**
     * 计算订单同步延迟字段，当系统状态为WAIT_SELLER_SEND_GOODS时，并且触发的tradeTrade.action为订单同步或者同步修改订单平台状态，则进行计算
     * @param tradeTrace
     * @return
     */
    public static TradeTrace calculateDelay(TradeTrace tradeTrace){
        if(tradeTrace == null || StringUtils.isEmpty(tradeTrace.getAction()) || tradeTrace.getExtProperties().size() == 0
                || !tradeTrace.getExtProperties().containsKey(TradeTraceAttrs.SYS_STATUS)
                || !analyzeSysStatus((String)tradeTrace.getExtProperties().get(TradeTraceAttrs.SYS_STATUS))
            || !tradeTrace.getExtProperties().containsKey(TradeTraceAttrs.PAY_TIME) || tradeTrace.getOperateTime() == null){
            return tradeTrace;
        }

        if(tradeTrace.getAction().equals(OpEnum.TRADE_SYNC.getName())){
            // 计算订单同步时的延迟
            Date payTime = getDateAttr(tradeTrace, TradeTraceAttrs.PAY_TIME);
            if (Objects.nonNull(payTime) && payTime.getTime() > TradeTimeUtils.INIT_DATE_TIME) {
                tradeTrace.putLongProperty(TradeTraceAttrs.DELAY, tradeTrace.getOperateTime().getTime() - payTime.getTime());
                return tradeTrace;
            }
            if(!tradeTrace.getExtProperties().containsKey(TradeTraceAttrs.CREATED)){
                return tradeTrace;
            }
            tradeTrace.putLongProperty(TradeTraceAttrs.DELAY, tradeTrace.getOperateTime().getTime() - getDateAttr(tradeTrace, TradeTraceAttrs.CREATED).getTime());
            return tradeTrace;
        }

        if(tradeTrace.getAction().contains(OpEnum.TRADE_SYNC_UPDATED.getName()) && tradeTrace.getExtProperties().containsKey(TradeTraceAttrs.MODIFIED)){
            tradeTrace.putLongProperty(TradeTraceAttrs.DELAY, tradeTrace.getOperateTime().getTime() - getDateAttr(tradeTrace, TradeTraceAttrs.MODIFIED).getTime());
            return tradeTrace;
        }

        return tradeTrace;
    }

    static boolean analyzeSysStatus(String sysStatus){
        return StringUtils.equals(sysStatus, Trade.SYS_STATUS_FINISHED_AUDIT) ||  StringUtils.equals(sysStatus, Trade.SYS_STATUS_WAIT_AUDIT)
                || StringUtils.equals(sysStatus, Trade.SYS_STATUS_WAIT_MANUAL_AUDIT) || StringUtils.equals(sysStatus, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT);
    }

    static Date getDateAttr(TradeTrace tradeTrace, String key){
        return (Date)tradeTrace.getExtProperties().get(key);
    }
}

package com.raycloud.dmj.domain.trades.params;


import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.user.AbroadAddress;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 线下组包查询订单数据请求参数
 */
public class OfflineParcelTradeRequest implements Serializable {

    /**
     * 淘宝Id 列表
     */
    private List<Long> taobaoId;

    /**
     * 系统单号列表
     */
    private List<Long> sids;

    /**
     * 平台单号列表
     */
    private List<String> tids;

    /**
     * 运单号列表
     */
    private List<String> outSids;

    /**
     * 内部单号列表
     */
    private List<String> shortId;

    /**
     * 查询时间类型 ：pay_time 付款时间 ,consign_time 发货时间
     */
    private String timeType;

    private Long startTime;

    private Long endTime;
    /**
     * 发货开始时间
     */
    private Date consignStartTime;
    /**
     * 发货结束时间
     */
    private Date consignEndTime;
    /**
     * 付款开始时间
     */
    private Date payStartTime;
    /**
     * 付款结束时间
     */
    private Date payEndTime;
    /**
     * 系统状态列表
     */
    private List<String> sysStatus;

    /**
     * 平台列表
     */
    private List<String> source;

    /**
     * 专门给btas加的字段。
     */
    private int btas=0;

    /**
     * 波次号
     */
    private List<String> waveIds;

    /**
     * 平台状态
     */
    private String unifiedStatus;

    /**
     * 仓库Id
     */
    private Long warehouseId;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 大包单号
     */
    private Long combineParcelId;

    /**
     * 大包单号列表
     */
    private List<Long> combineParcelIds;

    /**
     * 小包id
     */
    private List<Long> parcelIds;

    /**
     * 物流单号或者大包单号
     */
    private String parcelTrackingNo;

    /**
     * 1:邮寄，2:无需物流
     */
    private Integer btasConsignType;

    /**
     * BTAS大包质检配置: 快递产品
     * 0：顺丰特快
     * 1：顺丰标快
     */
    private Integer btasExpressProduct;

    private Page page;

    /**
     * 收件人地址
     */
    private AbroadAddress receiveAddress;

    private Boolean containExcept;

    public List<Long> getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(List<Long> taobaoId) {
        this.taobaoId = taobaoId;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public List<String> getTids() {
        return tids;
    }

    public void setTids(List<String> tids) {
        this.tids = tids;
    }

    public List<String> getOutSids() {
        return outSids;
    }

    public void setOutSids(List<String> outSids) {
        this.outSids = outSids;
    }

    public List<String> getShortId() {
        return shortId;
    }

    public void setShortId(List<String> shortId) {
        this.shortId = shortId;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Date getConsignStartTime() {
        return consignStartTime;
    }

    public void setConsignStartTime(Date consignStartTime) {
        this.consignStartTime = consignStartTime;
    }

    public Date getConsignEndTime() {
        return consignEndTime;
    }

    public void setConsignEndTime(Date consignEndTime) {
        this.consignEndTime = consignEndTime;
    }

    public List<String> getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(List<String> sysStatus) {
        this.sysStatus = sysStatus;
    }

    public List<String> getSource() {
        return source;
    }

    public void setSource(List<String> source) {
        this.source = source;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getPayStartTime() {
        return payStartTime;
    }

    public void setPayStartTime(Date payStartTime) {
        this.payStartTime = payStartTime;
    }

    public Date getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(Date payEndTime) {
        this.payEndTime = payEndTime;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public List<Long> getParcelIds() {
        return parcelIds;
    }

    public void setParcelIds(List<Long> parcelIds) {
        this.parcelIds = parcelIds;
    }

    public List<Long> getCombineParcelIds() {
        return combineParcelIds;
    }

    public void setCombineParcelIds(List<Long> combineParcelIds) {
        this.combineParcelIds = combineParcelIds;
    }

    public String getParcelTrackingNo() {
        return parcelTrackingNo;
    }

    public void setParcelTrackingNo(String parcelTrackingNo) {
        this.parcelTrackingNo = parcelTrackingNo;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public AbroadAddress getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(AbroadAddress receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public List<String> getWaveIds() {
        return waveIds;
    }

    public void setWaveIds(List<String> waveIds) {
        this.waveIds = waveIds;
    }

    public int getBtas() {
        return btas;
    }

    public void setBtas(int btas) {
        this.btas = btas;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public Integer getBtasConsignType() {
        return btasConsignType;
    }

    public void setBtasConsignType(Integer btasConsignType) {
        this.btasConsignType = btasConsignType;
    }

    public Integer getBtasExpressProduct() {
        return btasExpressProduct;
    }

    public void setBtasExpressProduct(Integer btasExpressProduct) {
        this.btasExpressProduct = btasExpressProduct;
    }

    public Boolean getContainExcept() {
        return containExcept;
    }

    public void setContainExcept(Boolean containExcept) {
        this.containExcept = containExcept;
    }
}

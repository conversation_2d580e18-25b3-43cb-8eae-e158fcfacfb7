package com.raycloud.dmj.domain.payment;

import lombok.Data;

import java.util.List;

/**
 * @Description <pre>
 * 前端 修改商品页面 进行换商品操作时 对应金额计算回填
 * </pre>
 * <AUTHOR>
 * @Date 2023-02-14
 */
@Data
public class TradeItemExchangeProCalculateResponse {


    private Long sid;


    List<OrderPayment> targets;


    @Data
    public static class OrderPayment{

        /**
         * 匹配到的系统商品ID
         */
        private Long itemSysId;
        /**
         * 匹配到的系统规格ID
         */
        private Long skuSysId;

        /**
         * 系统商家编码，如果是商品则为商品的商家编码，如果为SKU则为SKU的商家编码
         */
        private String sysOuterId;

        /**
         * 子订单平台编
         */
        private Long oid;


        /**
         * 实付金额, 公式: 实付金额 = 应付金额 - 优惠金额
         */
        private String payment;

        /**
         * 不变的实付金额, 进来后不变
         */
        private String acPayment;

        /**
         * 商品销售价
         */
        private String price;

        /**
         * 子订单商品数量
         */
        private Integer num;

        /**
         * 优惠金额
         */
        private String discountFee;

        /**
         * 实付金额
         */
        private Double payAmount;

        /**
         * 分销金额
         */
        private String saleFee;
        /**
         * 分销价格
         */
        private String salePrice;


    }

}

package com.raycloud.dmj.domain.constant;

import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系统标签常量类
 */
public interface SystemTags {

    /**
     * 全局系统标签集合
     */
    // erp-aftersale 引用
    Map<Long, TradeTag> SYSTEM_TAG_MAP = new ConcurrentHashMap<>();

    /* ------------------------------------------------------------------------------------------------------------- */
    TradeTag TAG_CANCEL_INSUFFICIENT = TradeSystemLabelEnum.TAG_CANCEL_INSUFFICIENT.getSystemTag();
    TradeTag TAG_CANCEL_CONSIGN = TradeSystemLabelEnum.TAG_CANCEL_CONSIGN.getSystemTag();
    TradeTag TAG_TRADE_PAY_INCONSISTENT_AMOUNT = TradeSystemLabelEnum.TAG_TRADE_PAY_INCONSISTENT_AMOUNT.getSystemTag();
    // erp-pt 引用
    TradeTag TAG_MULTI_PACKS = TradeSystemLabelEnum.TAG_MULTI_PACKS.getSystemTag();
    TradeTag TAG_JD_JPS = TradeSystemLabelEnum.TAG_JD_JPS.getSystemTag();
    TradeTag TAG_EXCHANGE = TradeSystemLabelEnum.TAG_EXCHANGE.getSystemTag();
    TradeTag TAG_REISSUE = TradeSystemLabelEnum.TAG_REISSUE.getSystemTag();
    TradeTag TAG_FORCE_PACK_SPLIT = TradeSystemLabelEnum.TAG_FORCE_PACK_SPLIT.getSystemTag();
    TradeTag TAG_IMPORT_DELIVER = TradeSystemLabelEnum.TAG_IMPORT_DELIVER.getSystemTag();
    TradeTag TAG_BEFORE_DELIVER_AFTER_SALE = TradeSystemLabelEnum.TAG_BEFORE_DELIVER_AFTER_SALE.getSystemTag();
    TradeTag TAG_AFTER_DELIVER_AFTER_SALE = TradeSystemLabelEnum.TAG_AFTER_DELIVER_AFTER_SALE.getSystemTag();
    TradeTag TAG_ITEM_PRICE_EXCEPTION = TradeSystemLabelEnum.TAG_ITEM_PRICE_EXCEPTION.getSystemTag();
    TradeTag TAG_OS_ACTIVITY = TradeSystemLabelEnum.TAG_OS_ACTIVITY.getSystemTag();
    TradeTag TAG_UNIQUE_OFF_SHELF = TradeSystemLabelEnum.TAG_UNIQUE_OFF_SHELF.getSystemTag();
    TradeTag TAG_OS_ACTIVITY_GIFT = TradeSystemLabelEnum.TAG_OS_ACTIVITY_GIFT.getSystemTag();
    TradeTag TAG_TO_BE_SEARCHED = TradeSystemLabelEnum.TAG_TO_BE_SEARCHED.getSystemTag();
    TradeTag TAG_EXPEDITED_SHIPMENTS = TradeSystemLabelEnum.TAG_EXPEDITED_SHIPMENTS.getSystemTag();
    TradeTag TAG_SALE_TRDE_PRE_ALLOCATE_GOODS = TradeSystemLabelEnum.TAG_SALE_TRDE_PRE_ALLOCATE_GOODS.getSystemTag();
    TradeTag TAG_OVERTIME = TradeSystemLabelEnum.TAG_OVERTIME.getSystemTag();
    TradeTag TAG_PARTY3_TEMPLATE_CHANGED = TradeSystemLabelEnum.TAG_PARTY3_TEMPLATE_CHANGED.getSystemTag();
    TradeTag TAG_SELF_PICK = TradeSystemLabelEnum.TAG_SELF_PICK.getSystemTag();
    TradeTag TAG_OFFLINE_PAY = TradeSystemLabelEnum.TAG_OFFLINE_PAY.getSystemTag();
    TradeTag TAG_TRADE_ITEM_REPLACE_EXCEPTION = TradeSystemLabelEnum.TAG_TRADE_ITEM_REPLACE_EXCEPTION.getSystemTag();
    // erp-aftersale 引用
    TradeTag TAG_PRE_UPLOAD_CONSIGN = TradeSystemLabelEnum.TAG_PRE_UPLOAD_CONSIGN.getSystemTag();
    TradeTag TAG_CHANGE_ITEM = TradeSystemLabelEnum.TAG_CHANGE_ITEM.getSystemTag();
    TradeTag TAG_ORDER_NUM_EXCEPTION = TradeSystemLabelEnum.TAG_ORDER_NUM_EXCEPTION.getSystemTag();
    TradeTag ITEM_REPLACE_SUCCESS = TradeSystemLabelEnum.ITEM_REPLACE_SUCCESS.getSystemTag();
    TradeTag SUIT_TO_SINGLE = TradeSystemLabelEnum.SUIT_TO_SINGLE.getSystemTag();
    // erp-pt 引用
    TradeTag TAG_HOME_DELIVERY = TradeSystemLabelEnum.TAG_HOME_DELIVERY.getSystemTag();
    // erp-pt 引用
    TradeTag TAG_YXD_HOME_DELIVERY = TradeSystemLabelEnum.TAG_YXD_HOME_DELIVERY.getSystemTag();
    TradeTag TAG_OPEN_IN_FESTIVAL = TradeSystemLabelEnum.TAG_OPEN_IN_FESTIVAL.getSystemTag();
    TradeTag TAG_FAST_MOVING_ORDER = TradeSystemLabelEnum.TAG_FAST_MOVING_ORDER.getSystemTag();
    TradeTag TAG_FAST_IN_FAST_OUT = TradeSystemLabelEnum.TAG_FAST_IN_FAST_OUT.getSystemTag();
    TradeTag AFTER_SALE_CAN_CONSIGN = TradeSystemLabelEnum.AFTER_SALE_CAN_CONSIGN.getSystemTag();
    TradeTag TAG_DELAY_CONSIGN = TradeSystemLabelEnum.TAG_DELAY_CONSIGN.getSystemTag();
    TradeTag TAG_NO_TRACE_DELIVERY = TradeSystemLabelEnum.TAG_NO_TRACE_DELIVERY.getSystemTag();
    TradeTag TAG_SAME_CITY_DISTRIBUTION = TradeSystemLabelEnum.TAG_SAME_CITY_DISTRIBUTION.getSystemTag();
    TradeTag TAG_HAS_SUBSIDY_POSTAGE = TradeSystemLabelEnum.TAG_HAS_SUBSIDY_POSTAGE.getSystemTag();
    TradeTag TAG_TMGJZY_BUSINESS_WAREHOUSE_BUSINESS = TradeSystemLabelEnum.TAG_TMGJZY_BUSINESS_WAREHOUSE_BUSINESS.getSystemTag();
    TradeTag TAG_TMGJZY_BUSINESS_WAREHOUSE_SELF = TradeSystemLabelEnum.TAG_TMGJZY_BUSINESS_WAREHOUSE_SELF.getSystemTag();
    TradeTag TAG_TMGJZY_BUSINESS_WAREHOUSE_CAINIAO = TradeSystemLabelEnum.TAG_TMGJZY_BUSINESS_WAREHOUSE_CAINIAO.getSystemTag();
    TradeTag TAG_HAS_SF_EXPRESS_SERVICE = TradeSystemLabelEnum.TAG_HAS_SF_EXPRESS_SERVICE.getSystemTag();
    TradeTag TAG_COMMUNITY_GROUP = TradeSystemLabelEnum.TAG_COMMUNITY_GROUP.getSystemTag();
    TradeTag TAG_DY_AUTO_LZ = TradeSystemLabelEnum.TAG_DY_AUTO_LZ.getSystemTag();
    TradeTag TAG_TIKTOK_SEND_BY_SELLER = TradeSystemLabelEnum.TAG_TIKTOK_SEND_BY_SELLER.getSystemTag();
    TradeTag TAG_TRADE_COPY = TradeSystemLabelEnum.TAG_TRADE_COPY.getSystemTag();
    TradeTag TAG_SYSTEM_AUTO_CREATE_ITEM = TradeSystemLabelEnum.TAG_SYSTEM_AUTO_CREATE_ITEM.getSystemTag();
    TradeTag TAG_INVALID_CHANGE_ITEM = TradeSystemLabelEnum.TAG_INVALID_CHANGE_ITEM.getSystemTag();
    TradeTag TAG_HAS_CREATE_CAIGOU_ORDER = TradeSystemLabelEnum.TAG_HAS_CREATE_CAIGOU_ORDER.getSystemTag();
    TradeTag TAG_PURCHASE_BASE_ON_SALES = TradeSystemLabelEnum.TAG_PURCHASE_BASE_ON_SALES.getSystemTag();
    TradeTag TAG_PDD_SHIP_HOLD = TradeSystemLabelEnum.TAG_PDD_SHIP_HOLD.getSystemTag();
    TradeTag CAINIAO_WAREHOUSE = TradeSystemLabelEnum.CAINIAO_WAREHOUSE.getSystemTag();
    TradeTag CAINIAO_WAREHOUSE_ZF = TradeSystemLabelEnum.CAINIAO_WAREHOUSE_ZF.getSystemTag();
    TradeTag TAG_TB_SX_BRD = TradeSystemLabelEnum.TAG_TB_SX_BRD.getSystemTag();
    TradeTag TAG_WX_XY_HF = TradeSystemLabelEnum.TAG_WX_XY_HF.getSystemTag();
    TradeTag TAG_WX_BIC = TradeSystemLabelEnum.TAG_WX_BIC.getSystemTag();
    TradeTag TAG_POISON_PERFORMANCE_TYPE_3 = TradeSystemLabelEnum.TAG_POISON_PERFORMANCE_TYPE_3.getSystemTag();
    TradeTag TAG_KUAISHOU_DF = TradeSystemLabelEnum.TAG_KUAISHOU_DF.getSystemTag();
    TradeTag TAG_KS_FAST_CONSIGN = TradeSystemLabelEnum.TAG_KS_FAST_CONSIGN.getSystemTag();
    TradeTag TAG_FX_FUND_NOT_ENOUGH = TradeSystemLabelEnum.TAG_FX_FUND_NOT_ENOUGH.getSystemTag();
    // erp-fms 引用
    TradeTag TAG_NEED_INVOICE = TradeSystemLabelEnum.TAG_NEED_INVOICE.getSystemTag();
    // erp-fms 引用
    TradeTag TAG_ALREADY_INVOICE = TradeSystemLabelEnum.TAG_ALREADY_INVOICE.getSystemTag();
    TradeTag AX_DELIVERY = TradeSystemLabelEnum.AX_DELIVERY.getSystemTag();
    TradeTag AX_NEXT_DAY = TradeSystemLabelEnum.AX_NEXT_DAY.getSystemTag();
    TradeTag AX_24_HOURS = TradeSystemLabelEnum.AX_24_HOURS.getSystemTag();
    TradeTag AX_TODAY = TradeSystemLabelEnum.AX_TODAY.getSystemTag();
    TradeTag AX_OFFICIAL_LOGISTICS = TradeSystemLabelEnum.AX_OFFICIAL_LOGISTICS.getSystemTag();
    TradeTag AX_XINJIANG_CONSOLIDATION = TradeSystemLabelEnum.AX_XINJIANG_CONSOLIDATION.getSystemTag();
    TradeTag AX_PROMISES_HOME_DELIVERY = TradeSystemLabelEnum.AX_PROMISES_HOME_DELIVERY.getSystemTag();
    TradeTag AX_SHIPPING_CONSOLIDATION = TradeSystemLabelEnum.AX_SHIPPING_CONSOLIDATION.getSystemTag();
    // erp-pt 引用
    TradeTag SPLIT_THE_PARENT_ORDER = TradeSystemLabelEnum.SPLIT_THE_PARENT_ORDER.getSystemTag();
    // erp-pt 引用
    TradeTag SPLIT_THE_CHILD_ORDER = TradeSystemLabelEnum.SPLIT_THE_CHILD_ORDER.getSystemTag();
    // erp-pt 引用
    TradeTag VIPSOV_UPDATE_ORDER = TradeSystemLabelEnum.VIPSOV_UPDATE_ORDER.getSystemTag();
    // erp-pt 引用
    TradeTag SPLIT_THE_PARENT_ORDER_DIFF_NUM = TradeSystemLabelEnum.SPLIT_THE_PARENT_ORDER_DIFF_NUM.getSystemTag();
    // erp-pt 引用
    TradeTag SPLIT_THE_CHILD_ORDER_DIFF_NUM = TradeSystemLabelEnum.SPLIT_THE_CHILD_ORDER_DIFF_NUM.getSystemTag();
    TradeTag TAG_FX_POSSIBLE_LOSS = TradeSystemLabelEnum.TAG_FX_POSSIBLE_LOSS.getSystemTag();
    TradeTag TAG_PARTY3_TMSCANCELED = TradeSystemLabelEnum.TAG_PARTY3_TMSCANCELED.getSystemTag();
    TradeTag TAG_PARTY3_WMSCANCELED = TradeSystemLabelEnum.TAG_PARTY3_WMSCANCELED.getSystemTag();
    TradeTag TAG_PARTY3_TMSCANCELFAILED = TradeSystemLabelEnum.TAG_PARTY3_TMSCANCELFAILED.getSystemTag();
    TradeTag TAG_PARTY3_CANCELEDING = TradeSystemLabelEnum.TAG_PARTY3_CANCELEDING.getSystemTag();
    TradeTag TAG_AUDIT_UNDO = TradeSystemLabelEnum.TAG_AUDIT_UNDO.getSystemTag();
    TradeTag TAG_UPDATE_TIMEOUT = TradeSystemLabelEnum.TAG_UPDATE_TIMEOUT.getSystemTag();
    // erp-pt 引用
    TradeTag TAG_JDCV_SELF_SETTLE = TradeSystemLabelEnum.TAG_JDCV_SELF_SETTLE.getSystemTag();
    TradeTag TAG_KTT_HELP_SALE = TradeSystemLabelEnum.TAG_KTT_HELP_SALE.getSystemTag();
    // erp-pt 引用
    TradeTag TAG_SF_FREE_SHIPPING = TradeSystemLabelEnum.TAG_SF_FREE_SHIPPING.getSystemTag();
    TradeTag TRANSIT_MERGE = TradeSystemLabelEnum.TRANSIT_MERGE.getSystemTag();
    TradeTag NFGOOD_HELP_SELL = TradeSystemLabelEnum.NFGOOD_HELP_SELL.getSystemTag();
    TradeTag TAG_TRADE_IN_STATE_SUBSIDIES = TradeSystemLabelEnum.TAG_TRADE_IN_STATE_SUBSIDIES.getSystemTag();
    TradeTag AX_OPTIONAL_EXPRESS = TradeSystemLabelEnum.AX_OPTIONAL_EXPRESS.getSystemTag();
    TradeTag TAG_CROSS_BORDER_PRINT_WAVES = TradeSystemLabelEnum.TAG_CROSS_BORDER_PRINT_WAVES.getSystemTag();
    TradeTag TAG_FX_FORCE_PUSH = TradeSystemLabelEnum.TAG_FX_FORCE_PUSH.getSystemTag();
    TradeTag HAS_URGED_SUPPLIER_TO_SHIP = TradeSystemLabelEnum.HAS_URGED_SUPPLIER_TO_SHIP.getSystemTag();
    TradeTag DISTRIBUTOR_URGING_SHIPMENT = TradeSystemLabelEnum.DISTRIBUTOR_URGING_SHIPMENT.getSystemTag();
    TradeTag TAG_SHOPEE_FUL_FILLED = TradeSystemLabelEnum.TAG_SHOPEE_FUL_FILLED.getSystemTag();
    TradeTag TAG_WECHAT_VIDEO_GIFT_TRADE = TradeSystemLabelEnum.TAG_WECHAT_VIDEO_GIFT_TRADE.getSystemTag();
    TradeTag TAG_PLATFORM_DISTRIBUTION = TradeSystemLabelEnum.TAG_PLATFORM_DISTRIBUTION.getSystemTag();
    TradeTag TAG_CLEARANCE_TRADE = TradeSystemLabelEnum.TAG_CLEARANCE_TRADE.getSystemTag();
    // erp-storage 引用
    TradeTag TAG_STATE_SUBSIDY_3C = TradeSystemLabelEnum.TAG_STATE_SUBSIDY_3C.getSystemTag();
    TradeTag TAG_1688_CUSTOM_SERVICE = TradeSystemLabelEnum.TAG_1688_CUSTOM_SERVICE.getSystemTag();
    TradeTag TAG_YUN_ZHU_SHOU = TradeSystemLabelEnum.TAG_YUN_ZHU_SHOU.getSystemTag();

}

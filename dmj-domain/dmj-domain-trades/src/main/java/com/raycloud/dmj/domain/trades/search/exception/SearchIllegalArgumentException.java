package com.raycloud.dmj.domain.trades.search.exception;

/**
 * 查询参数异常
 */
public class SearchIllegalArgumentException extends IllegalArgumentException{

    public SearchIllegalArgumentException() {
    }

    public SearchIllegalArgumentException(String message) {
        super(message);
    }

    public SearchIllegalArgumentException(String message, Throwable cause) {
        super(message, cause);
    }

    public SearchIllegalArgumentException(Throwable cause) {
        super(cause);
    }
}

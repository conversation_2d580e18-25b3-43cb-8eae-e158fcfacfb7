package com.raycloud.dmj.domain.trades;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <EMAIL>
 * @date 2022/7/18 19:40
 */
@Data
@Builder
public class TradeOrderGroupSummaryParams implements Serializable {
    private static final long serialVersionUID = 8622601709497338098L;
    /**
     * 1.过滤有异常、待付款、已发货、交易成功、交易关闭、待供销商发货的订单
     * 2.不过滤，根据勾选/查询结果的订单进行汇总
     */
    Integer filterId;

    /**
     * 查询套件库存
     * 套件商品按套件统计：true
     * 套件商品按套件单品明细统计：false
     */
    Boolean querySuitStock;

    /**
     * 展示仓库
     */
    String warehouseIds;

    /**
     * 排序字段
     */
    String sortFields;

    /**
     * 排序规则
     */
    String orderBy;
}

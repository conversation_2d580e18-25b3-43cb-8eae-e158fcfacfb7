package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: men
 * @Date: 2021-07-15 20:03
 * 订单包装验货记录
 */
@Table(name = "trade_item_pack_log" ,routerKey = "wavePickerDbNo")
@Data
public class TradeItemPackLog extends Model{

    private static final long serialVersionUID = 5799927940822956575L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    private Long sid;

    private Long orderId;

    /**
     * 运单号
     */
    private Long outSid;

    /**
     * 波次号
     */
    private Long waveId;

    /**
     * skuID
     */
    private Long sysSkuId;

    /**
     * itemID
     */
    private Long sysItemId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 验货数量
     */
    private Integer packNum;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 是否启用 （ 1 启用 0 作废）
     */
    private Integer enableStatus;

    /**
     * 扫描的唯一码
     */
    private String uniqueCodes;

    /**
     * 是否重新验货 不持久化
     */
    private Boolean reexamine;

    public Integer getEnableStatus() {
        return enableStatus==null?1:enableStatus;
    }

}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.raycloud.dmj.domain.utils.CommonConstants.*;

public class TradeSysDigestUtils {

    private static final Logger logger = Logger.getLogger(OrderUtils.class);

    //系统自脱敏。
    public static List<String> ERP_SELF_SUPPORT_PLATFORM = Arrays.asList(
            CommonConstants.PLAT_FORM_TYPE_XHS
            ,CommonConstants.PLAT_FORM_TYPE_YYJK
            ,CommonConstants.PLAT_FORM_TYPE_HEMAOS
    );

    /**
     * 平台和系统手工单走系统脱敏
     */
    public static List<String> SYS_SELF_SUPPORT_PLATFORM = Arrays.asList(
            CommonConstants.PLAT_FORM_TYPE_HEMAOS
    );

    public static void batchSensitive(Staff staff,List<Trade> trade){
        if (CollectionUtils.isEmpty(trade)) {
            return;
        }
        if(staff.getUserIdMap() == null){
            staff.fillUserMap();
        }
        Map<Long, User> userIdMap = staff.getUserIdMap();
        for (Trade currentTrade : trade) {
            //自己是这个平台的，或者店铺是这个平台的。
            if (ERP_SELF_SUPPORT_PLATFORM.contains(currentTrade.getSource())
                    ) {
                sensitive(currentTrade);
                continue;
            }
            User user = userIdMap.get(currentTrade.getUserId());
            if (user ==null) {
                if (Objects.equals(100000000L, currentTrade.getUserId())){
                    continue;
                }
                logger.warn(String.format("订单脱敏失败：userId:%s,sid:%s",currentTrade.getUserId(),currentTrade.getSid()));
                continue;
            }
            if(PLAT_FORM_TYPE_SYS.equals(currentTrade.getSource()) && ERP_SELF_SUPPORT_PLATFORM.contains(user.getSource())){
                sensitive(currentTrade);
            }
            //临时处理系统复制的AKC订单
            if(PLAT_FORM_TYPE_SYS.equals(currentTrade.getSource()) && PLAT_FORM_TYPE_AKC.equals(user.getSource())){
                sensitive(currentTrade);
            }
        }
    }

    private static void sensitive(Trade trade){
        if (isEncrypt(trade)) {
            return;
        }
        trade.setReceiverName(toReceiverName(trade.getReceiverName()));
        trade.setBuyerNick(toReceiverName(trade.getBuyerNick()));

        trade.setReceiverAddress("*****");

        trade.setReceiverMobile(toPhone(trade.getReceiverMobile()));
        trade.setReceiverPhone(toPhone(trade.getReceiverPhone()));
    }

    public static String toPhone(String receiverPhone){
        if (receiverPhone==null) {
            return null;
        }
        if (11 == receiverPhone.length()) {
            return receiverPhone.substring(0,3)+"****"+receiverPhone.substring(receiverPhone.length()-3,receiverPhone.length());
        }else {
            return "****";
        }
    }

    public static String toReceiverName(String receiverName){
        if (receiverName!=null){
            switch (receiverName.length()){
                case 0:
                case 1:
                    return "***";
                case 2:
                    return receiverName.substring(0,1)+"*";
                default:
                    return receiverName.substring(0,1)+"**"+receiverName.substring(receiverName.length()-1,receiverName.length());
            }
        }else {
            return null;
        }
    }

    public static boolean isEncrypt(Trade trade) {
        //临时处理系统复制的AKC订单
        if(PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && PLAT_FORM_TYPE_AKC.equals(trade.getSubSource())){
            return false;
        }
        if(SYS_SELF_SUPPORT_PLATFORM.contains(trade.getSource())
                || SYS_SELF_SUPPORT_PLATFORM.contains(trade.getUserSource())){
            return false;
        }
        if (TradeUtils.ifEncrypt(trade.getReceiverName()) || TradeUtils.ifEncrypt(trade.getReceiverMobile()) || TradeUtils.ifEncrypt(trade.getReceiverAddress())) {
            return true;
        }
        return false;
    }

    public static void sensitiveAll(Trade trade){
        trade.setReceiverName(toReceiverName(trade.getReceiverName()));
        trade.setBuyerNick(toReceiverName(trade.getBuyerNick()));
        trade.setReceiverAddress("*****");
        trade.setReceiverMobile(toPhone(trade.getReceiverMobile()));
        trade.setReceiverPhone(toPhone(trade.getReceiverPhone()));
    }

    public static void batchSysAkcSensitive(Staff staff,List<Trade> trade){
        if (CollectionUtils.isEmpty(trade)) {
            return;
        }
        Map<Long, User> userIdMap = staff.getUserIdMap();
        if(Objects.isNull(userIdMap)){
            return;
        }
        for (Trade currentTrade : trade) {
            if(SYS_SELF_SUPPORT_PLATFORM.contains(currentTrade.getSource())
                    || SYS_SELF_SUPPORT_PLATFORM.contains(currentTrade.getUserSource())){
                sensitive(currentTrade);
                continue;
            }
            //自己是这个平台的，或者店铺是这个平台的。
            User user = userIdMap.get(currentTrade.getUserId());
            if (user ==null) {
                continue;
            }
            //临时处理系统复制的AKC订单
            if(PLAT_FORM_TYPE_SYS.equals(currentTrade.getSource()) && PLAT_FORM_TYPE_AKC.equals(user.getSource())){
                sensitive(currentTrade);
            }
        }
    }
}

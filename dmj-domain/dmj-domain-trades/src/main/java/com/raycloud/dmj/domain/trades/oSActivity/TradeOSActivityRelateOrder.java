package com.raycloud.dmj.domain.trades.oSActivity;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * 前N有礼-关联订单
 * Created by huangfuhua on 2020-08-24.
 */
@Table(name = "trade_os_activity_relate_order", routerKey = "tradeOSActivityRelateOrderDbNo")
public class TradeOSActivityRelateOrder extends Model {

    private static final long serialVersionUID = -3701065780958034035L;

    /**
     * 公司信息
     */
    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id;
    @Column(name = "activity_id", type = Types.VARCHAR)
    private String activityId;
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    /**
     * 订单信息
     */
    @Column(name = "sid", type = Types.BIGINT)
    private Long sid;
    @Column(name = "oid", type = Types.BIGINT)
    private Long oid;

    /**
     * 订单入库时间
     */
    @Column(name = "import_time", type = Types.TIMESTAMP)
    private Date importTime;
    /**
     * 是否解除挂起
     */
    @Column(name = "is_cancel_halt", type = Types.INTEGER)
    private Integer isCancelHalt;
    @Column(name = "gift_item_id", type = Types.VARCHAR)
    private String giftItemId;
    @Column(name = "gift_count", type = Types.INTEGER)
    private Integer giftCount;

    @Column(name = "enable_status", type = Types.INTEGER)
    private Integer enableStatus;

    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    @Column(name = "modified", type = Types.TIMESTAMP)
    private Date modified;

    public static TradeOSActivityRelateOrder init(Staff staff, User user, String activityId, Order order) {
        TradeOSActivityRelateOrder relateOrder = new TradeOSActivityRelateOrder();
        relateOrder.setActivityId(activityId);
        relateOrder.setUserId(user.getId());
        relateOrder.setCompanyId(staff.getCompanyId());
        relateOrder.setSid(order.getSid());
        relateOrder.setOid(order.getOid());
        relateOrder.setGiftItemId(order.getOsFgItemId());
        relateOrder.setGiftCount(order.getOsGiftCount() == null ? 0 : Integer.valueOf(order.getOsGiftCount()));
        relateOrder.setImportTime(new Date());
        relateOrder.setIsCancelHalt(0);
        return relateOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Integer getIsCancelHalt() {
        return isCancelHalt;
    }

    public void setIsCancelHalt(Integer isCancelHalt) {
        this.isCancelHalt = isCancelHalt;
    }

    public void setGiftItemId(String giftItemId) {
        this.giftItemId = giftItemId;
    }

    public String getGiftItemId() {
        return giftItemId;
    }

    public void setGiftCount(Integer giftCount) {
        this.giftCount = giftCount;
    }

    public Integer getGiftCount() {
        return giftCount;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}

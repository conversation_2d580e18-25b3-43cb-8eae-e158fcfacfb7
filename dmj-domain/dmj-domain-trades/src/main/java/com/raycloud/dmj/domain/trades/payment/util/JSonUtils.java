package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-06-06
 */
public class JSonUtils {

    public static <T extends JSON> T  filterFields(T o, List<String> fields){
        return filterFields(o, fields, Collections.emptyList());
    }

    public static <T extends JSON> T  filterFields(T o, List<String> fields, List<String> expectFields){
        JSON json = filterFields(o, true,false, fields, expectFields == null?Collections.emptyList():expectFields);
        if (json != null) {
            return (T)json;
        }
        return null;
    }

    private static JSON filterFields(Object o, boolean root,boolean all, List<String> fields, List<String> expectFields){
        if (o == null) {
            return null;
        }
        if (o instanceof JSONObject) {
            JSONObject json = (JSONObject) o;
            JSONObject object = new JSONObject();
            for (String key : json.keySet()) {
                Object value = json.get(key);
                if (expectFields.contains(key)) {
                    continue;
                }
                if (fields.contains(key) || all) {
                    if (value instanceof JSON) {
                        if (Objects.equals(fields.indexOf(key),fields.size() -1)) {
                            object.put(key,value);
                        }else {
                            JSON ch = filterFields(value,false,true, fields,expectFields);
                            if (ch != null) {
                                object.put(key,ch);
                            }else{
                                JSONObject chs = new JSONObject();
                                chs.put(fields.get(fields.indexOf(key) + 1), null);
                                object.put(key, chs);
                            }
                        }
                    }else {
                        object.put(key,value);
                    }
                    continue;
                }
                if (value instanceof JSON) {
                    JSON ch = filterFields(value,false,all, fields,expectFields);
                    if (ch != null) {
                        object.put(key,ch);
                    }
                }
            }
            if (object.size() == 0 && !root) {
                return null;
            }
            return object;
        }else if(o instanceof JSONArray){
            JSONArray json =  (JSONArray) o;
            JSONArray array = new JSONArray();
            for (Object value : json) {
                if (value instanceof JSON) {
                    JSON ch = filterFields(value,false,all, fields,expectFields);
                    if (ch != null) {
                        array.add(ch);
                    }
                }
            }
            if (array.size() == 0 && !root) {
                return null;
            }
            return array;
        }
        return null;
    }

    public static void main(String[] args) {
        String json = "{\n" +
                "    \"a\": 1,\n" +
                "    \"b\": 1,\n" +
                "    \"c\": [\n" +
                "        {\n" +
                "            \"a\": 1,\n" +
                "            \"b\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"d\": {\n" +
                "        \"a\": 1,\n" +
                "        \"b\": 1\n" +
                "    },\n" +
                "}";
        JSONObject jsonObject = filterFields(JSON.parseObject(json), Arrays.asList("a", "d"),Arrays.asList("b"));

        System.out.println(jsonObject.toJSONString());
    }
}

package com.raycloud.dmj.domain.trades.oSActivity;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * 前N有礼-活动信息表
 * Created by huangfuhua on 2020-08-24.
 */
@Table(name = "trade_os_activity_info" , routerKey = "tradeOSActivityInfoDbNo")
public class TradeOSActivityInfo extends Model {

    private static final long serialVersionUID = 6209440221425865839L;

    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id;
    @Column(name = "activity_id", type = Types.VARCHAR)
    private String activityId;
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    @Column(name = "begin_time", type = Types.TIMESTAMP)
    private Date beginTime;

    /**
     * 是否开奖
     */
    @Column(name = "is_award", type = Types.INTEGER)
    private Integer isAward;
    /**
     * 是否发奖完毕
     */
    @Column(name = "is_finish_award", type = Types.INTEGER)
    private Integer isFinishAward;

    @Column(name = "enable_status", type = Types.INTEGER)
    private Integer enableStatus;

    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    @Column(name = "modified", type = Types.TIMESTAMP)
    private Date modified;

    public static TradeOSActivityInfo init(Staff staff, User user, String activityId) {
        TradeOSActivityInfo activityInfo = new TradeOSActivityInfo();
        activityInfo.setActivityId(activityId);
        activityInfo.setCompanyId(staff.getCompanyId());
        activityInfo.setUserId(user.getId());
        activityInfo.setBeginTime(new Date());
        activityInfo.setIsAward(0);
        activityInfo.setIsFinishAward(0);
        activityInfo.setEnableStatus(1);
        activityInfo.setCreated(new Date());
        activityInfo.setModified(new Date());
        return activityInfo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Integer getIsAward() {
        return isAward;
    }

    public void setIsAward(Integer isAward) {
        this.isAward = isAward;
    }

    public Integer getIsFinishAward() {
        return isFinishAward;
    }

    public void setIsFinishAward(Integer isFinishAward) {
        this.isFinishAward = isFinishAward;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}

package com.raycloud.dmj.domain.deliver;

import java.io.Serializable;
import java.util.List;

/**
 * deliversVo
 * 发货单二维码的vo组合
 *
 * @author: qingfeng.cxb
 * @create: 2019-05-14 15:37
 */
public class DeliversVo implements Serializable {
    private static final long serialVersionUID = -1919885022296735509L;
    /**
     * 发货单vo集合
     */
    private List<DeliverVo> deliverVoList;

    /**
     * 数量
     */
    private Integer total;

    /**
     * 订单编号
     */
    private String sid;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public List<DeliverVo> getDeliverVoList() {
        return deliverVoList;
    }

    public void setDeliverVoList(List<DeliverVo> deliverVoList) {
        this.deliverVoList = deliverVoList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}


package com.raycloud.dmj.domain.logistics.response;

import java.io.Serializable;

public class BaseLogisticsResponse implements Serializable {

    private static final long serialVersionUID = -7137774715755470976L;

    private Integer code;

    private String message;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static BaseLogisticsResponse error(String message) {
        BaseLogisticsResponse response = new BaseLogisticsResponse();
        response.setCode(500);
        response.setMessage(message);
        return response;
    }

    public static BaseLogisticsResponse success() {
        BaseLogisticsResponse response = new BaseLogisticsResponse();
        response.setCode(0);
        return response;
    }
}

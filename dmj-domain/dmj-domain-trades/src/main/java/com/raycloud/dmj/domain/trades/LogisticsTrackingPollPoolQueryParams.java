package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;

import java.util.Date;
import java.util.List;

/**
 * 运单号物流跟踪轮询池查询参数类
 */
public class LogisticsTrackingPollPoolQueryParams extends AbstractTemplateQueryParam {

    private static final long serialVersionUID = -1754507262375874325L;
    /**
     * 系统订单号
     */
    private Long sid;
    /**
     * 多个系统订单号
     */
    private String sidStr;

    private Long companyId;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 发货时使用的快递单号
     */
    private String outSid;

    /**
     * 店铺用户ID
     */
    private String userId;

    private List<Long> userIds;

    /**
     * 订单所在仓库ID
     */
    private Long warehouseId;

    /**
     * 查询时间类型
     */
    private String timeType;

    /**
     * 查询起始时间戳
     */
    private Long startTime;
    private Date startDate;

    /**
     * 查询结束时间戳
     */
    private Long endTime;
    private Date endDate;

    /**
     * 分页对象
     */
    private Page page;

    /**
     * 排序对象
     */
    private Sort sort;

    /**
     * 物流异常类型
     *
     * @return
     */
    private String logisticsExceptType;

    /**
     * 物流状态
     *
     * @return
     */
    private String logisticsStatus;

    private Long[] sids;

    private String[] tids;

    private String[] outSids;

    private Integer tradeDbNo;

    private Integer dbNo;

    private Integer enableStatus;

    /**
     * 1 需要关注
     * 0 不关注
     */
    private Integer needTracking;

    /**
     * 1 需要关注
     * 0 不关注
     */
    private List<Integer> needTrackingList;
    /**
     * 追踪时间 根据 物流状态 和用户设置的时间计算出来
     * 时间先于当前时间的才需要追踪
     */
    private Date calculatedTime;

    public String getSidStr() {
        return sidStr;
    }

    public void setSidStr(String sidStr) {
        this.sidStr = sidStr;
    }

    public String[] getTids() {
        return tids;
    }

    public void setTids(String[] tids) {
        this.tids = tids;
    }

    public String[] getOutSids() {
        return outSids;
    }

    public void setOutSids(String[] outSids) {
        this.outSids = outSids;
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public Integer getTradeDbNo() {
        return tradeDbNo;
    }

    public void setTradeDbNo(Integer tradeDbNo) {
        this.tradeDbNo = tradeDbNo;
    }

    public Long[] getSids() {
        return sids;
    }

    public void setSids(Long[] sids) {
        this.sids = sids;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public String getUserId() {
        return userId;
    }

    public LogisticsTrackingPollPoolQueryParams setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public LogisticsTrackingPollPoolQueryParams setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public LogisticsTrackingPollPoolQueryParams setStartTime(Long startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getStartDate() {
        if (startDate == null && (startTime != null && startTime > 0)) {
            startDate = new Date(startTime);
        }
        return startDate;
    }

    public LogisticsTrackingPollPoolQueryParams setStartDate(Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public Long getEndTime() {
        return endTime;
    }

    public LogisticsTrackingPollPoolQueryParams setEndTime(Long endTime) {
        this.endTime = endTime;
        return this;
    }

    public Date getEndDate() {
        if (endDate == null && (endTime != null && endTime > 0)) {
            endDate = new Date(endTime);
        }
        return endDate;
    }

    public LogisticsTrackingPollPoolQueryParams setEndDate(Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public Page getPage() {
        return page;
    }

    public LogisticsTrackingPollPoolQueryParams setPage(Page page) {
        this.page = page;
        return this;
    }

    public Sort getSort() {
        return sort;
    }

    public LogisticsTrackingPollPoolQueryParams setSort(Sort sort) {
        this.sort = sort;
        return this;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Integer getNeedTracking() {
        return needTracking;
    }

    public void setNeedTracking(Integer needTracking) {
        this.needTracking = needTracking;
    }

    public List<Integer> getNeedTrackingList() {
        return needTrackingList;
    }

    public void setNeedTrackingList(List<Integer> needTrackingList) {
        this.needTrackingList = needTrackingList;
    }

    public Date getCalculatedTime() {
        return calculatedTime;
    }

    public void setCalculatedTime(Date calculatedTime) {
        this.calculatedTime = calculatedTime;
    }
}

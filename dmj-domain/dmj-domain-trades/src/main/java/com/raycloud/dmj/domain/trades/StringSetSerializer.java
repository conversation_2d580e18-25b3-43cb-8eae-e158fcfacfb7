package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Set;


/**
 *
 * 避免浏览器的long类型溢出
 * Set<Long> sids = Sets.newHashSet(1L,2L,3L,4630852787560892L);   -->  ["1","2","3","4630852787560892"]
 *
 * <AUTHOR>
 */
public  class StringSetSerializer implements ObjectSerializer {
        @Override
        public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
            SerializeWriter out = serializer.out;
            Set<Long> set = (Set<Long>) object;
            if (set == null) {
                out.writeNull();
                return;
            }
            out.write('[');
            int index = 0;
            int size  = set.size();
            for (Long value : set) {
                out.writeString(String.valueOf(value));
                if(++index < size){
                    out.write(',');
                }
            }
            out.write(']');
        }
    }
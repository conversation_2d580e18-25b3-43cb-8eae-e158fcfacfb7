package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.PrintAsyncEventEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 打印异步事件处理
 *
 * @Date 2021/7/12
 * <AUTHOR>
 */
@Builder
@Data
public class PrintAsyncEventParams implements Serializable {

    private Staff staff;

    /**
     * 打印异步事件类型
     * @see PrintAsyncEventEnum
     */
    private Integer eventType;

    /**
     * 系统单号
     */
    private List<Long> sids;

}

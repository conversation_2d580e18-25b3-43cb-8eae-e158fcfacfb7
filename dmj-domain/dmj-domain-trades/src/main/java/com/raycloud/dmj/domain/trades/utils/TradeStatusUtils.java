package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ChangeItemBatchSource;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.ptException.DispatchedPtException;
import com.raycloud.dmj.ptException.PddCheckPtException;
import com.raycloud.dmj.ptException.SplitCannelCheckPtException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单状态计算处理工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-06 14:11
 */
public final class TradeStatusUtils {

    private TradeStatusUtils() {
    }

    private static final Map<String, Integer> SYS_STATUS_MAP = new HashMap<String, Integer>(10, 1);
    private static final Map<String, Integer> STATUS_MAP = new HashMap<String, Integer>(6, 1);
    private static Map<String, String> CH_STATUS_MAP = new HashMap<String, String>();
    private static Map<String, String> VIEW_CH_STATUS_MAP = new HashMap<String, String>();
    public  static Map<String, String> FINISHED_AUDIT_VIEW_STATUS_MAP = new HashMap<String, String>();
    private static final String UN_TB_WAIT_SELLER_SEND_GOODS = "UN_TB_WAIT_SELLER_SEND_GOODS";

    static {
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, 1);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_AUDIT, 3);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, 5);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_MANUAL_AUDIT, 7);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED_AUDIT, 9);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, 11);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED, 13);
        SYS_STATUS_MAP.put(Trade.SYS_STATUS_CLOSED, 15);

        STATUS_MAP.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, 1);
        STATUS_MAP.put(Trade.SYS_STATUS_WAIT_SEND_GOODS, 3);
        STATUS_MAP.put(TradeStatus.TB_SELLER_CONSIGNED_PART, 9);
        STATUS_MAP.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, 11);
        STATUS_MAP.put(Trade.SYS_STATUS_FINISHED, 13);
        STATUS_MAP.put(Trade.SYS_STATUS_CLOSED, 15);

        CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, "待付款");
        CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_SEND_GOODS, "待发货");
        CH_STATUS_MAP.put(TradeStatus.TB_SELLER_CONSIGNED_PART, "部分发货");
        CH_STATUS_MAP.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, "已发货");
        CH_STATUS_MAP.put(Trade.SYS_STATUS_CLOSED, "交易关闭");
        CH_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED, "交易成功");


        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, "待付款");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, "已发货");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_CLOSED, "交易关闭");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED, "交易成功");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_AUDIT, "待审核");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, "待财审");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT, "待打印发货单");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS, "待供销商发货");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT, "待打印快递单");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_WEIGHT, "待称重");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_PACKAGE, "待包装");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_SEND_GOODS, "待发货");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED_AUDIT, "审核完成");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_CANCEL, "交易作废");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_MANUAL_AUDIT, "待审核");

        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_WEIGHT, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_PACKAGE, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_SEND_GOODS, Trade.SYS_STATUS_FINISHED_AUDIT);
        FINISHED_AUDIT_VIEW_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
    }

    public static Integer getSysStatus(String sysStatus) {
        return sysStatus != null ? SYS_STATUS_MAP.get(sysStatus) : null;
    }

    public static List<String> getAllSysStatus() {
        return new ArrayList<>(SYS_STATUS_MAP.keySet());
    }


    public static Integer getStatus(String status) {
        return status != null ? STATUS_MAP.get(status) : null;
    }

    public static String getChStatus(String status, boolean isUnified) {
        if (isUnified) {
            return CH_STATUS_MAP.get(status);
        }
        return CH_STATUS_MAP.get(getUnifiedStatus(status));
    }

    /**
     * 获取各平台状态对应的统一状态
     *
     * @param status
     * @return
     */
    public static String getUnifiedStatus(String status) {
        //系统订单没有平台状态
        if (status == null || status.isEmpty()) {
            return null;
        }
        //部分发货
        // TODO 2018/11/19 ruanyaguang: 这里最好交由多平台实现
        if (TradeStatus.TB_SELLER_CONSIGNED_PART.equals(status) || "wd_split_deliver".equals(status)) {
            return TradeStatus.TB_SELLER_CONSIGNED_PART;
        }
        String sysStatus = TradeStatus.getSysStatus(status, null);
        //待发货
        if (isWaitAudit(sysStatus) || isWaitFinanceAudit(sysStatus) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_MANUAL_AUDIT.equals(sysStatus) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(sysStatus) || Trade.SYS_STATUS_WAIT_DELIVERY_PRINT.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_PACKAGE.equals(sysStatus) || Trade.SYS_STATUS_WAIT_WEIGHT.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(sysStatus)) {
            return Trade.SYS_STATUS_WAIT_SEND_GOODS;
        }
        //其他状态
        return sysStatus;
    }

    public static String convertSysStatus(Trade trade, TradeConfig tradeConfig) {
        return convertSysStatus(trade, TradeConfigContext.builder()
                .openDeliverPrint(tradeConfig.getOpenDeliverPrint())
                .openPackage(tradeConfig.getOpenPackageExamine())
                .openWeigh(tradeConfig.getOpenPackageWeigh())
                .build());
    }

    /**
     * 转换系统状态，这里主要转换已审核的订单状态
     */
    public static String convertSysStatus(Trade trade, TradeConfigContext context) {
        //出库单不验证店铺
        if (trade.isOutstock()) {
            return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) ? Trade.SYS_STATUS_WAIT_SEND_GOODS : trade.getSysStatus();
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
            return Trade.SYS_STATUS_CANCEL;
        }
        if (TradeUtils.isPlatformFxTrade(trade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
            return Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS;
        }
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return trade.getSysStatus();
        }
        // 无需快递的类型，直接就是待发货状态
        if (trade.getTemplateId() != null && trade.getTemplateId() == 0L && !TradeUtils.isFxOrMixTrade(trade)) {
            return Trade.SYS_STATUS_WAIT_SEND_GOODS;
        }
        if ((TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isAlibabaFxRoleTrade(trade)) && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS;
        }
        if (getPrintStatus(trade.getExpressPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_EXPRESS_PRINT;
        }
        if (context.getOpenDeliverPrint() == 1 && getPrintStatus(trade.getDeliverPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_DELIVERY_PRINT;
        }
        if (context.getOpenPackage() == 1 && !getIs(trade.getIsPackage())) {
            return Trade.SYS_STATUS_WAIT_PACKAGE;
        }
        if (context.getOpenWeigh() == 1 && !getIs(trade.getIsWeigh())) {
            return Trade.SYS_STATUS_WAIT_WEIGHT;
        }
        return Trade.SYS_STATUS_WAIT_SEND_GOODS;
    }

    /**
     * 转换系统状态，这里主要转换已审核的订单状态
     *
     * @param trade
     * @return
     */
    public static String convertChSysStatus(Trade trade, TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return trade.getSysStatus();
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
            return "交易作废";
        }
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus())) {
                return "待付款";
            }
            if (isWaitAudit(trade.getSysStatus())) {
                if (TradeUtils.isPlatformFxTrade(trade)){
                    return "待供销商发货";
                }
                return "待审核";
            }
            if (isWaitFinanceAudit(trade.getSysStatus())) {
                return "待财审";
            }
            if (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())) {
                return "交易完成";
            }
            if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
                return "交易关闭";
            }
            if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus())) {
                return "卖家已发货";
            }
        }
        // 无需快递的类型，直接就是待发货状态
        if (trade.getTemplateId() != null && trade.getTemplateId() == 0L) {
            return "待发货";
        }
        if (getPrintStatus(trade.getExpressPrintTime()) == 0 && !TradeUtils.isFxTrade(trade)) {
            return "待打印快递单";
        }

        if (getPrintStatus(trade.getExpressPrintTime()) == 0 && TradeUtils.isFxOrMixTrade(trade)) {
            return "待供销商发货";
        }
        if (tradeConfig.getOpenDeliverPrint() == 1 && getPrintStatus(trade.getDeliverPrintTime()) == 0) {
            return "待打印发货单";
        }
        if (tradeConfig.getOpenPackageExamine() == 1 && !getIs(trade.getIsPackage())) {
            return "待包装";
        }
        if (tradeConfig.getOpenPackageWeigh() == 1 && !getIs(trade.getIsWeigh())) {
            return "待称重";
        }
        return "待发货";
    }

    /**
     * 转换子订单系统状态
     *
     * @param order
     * @return
     */
    public static String convertOrderSysStatus(Order order) {
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus())) {
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus())) {
                return "待付款";
            }
            if (isWaitAudit(order.getSysStatus())) {
                return "待审核";
            }
            if (Trade.SYS_STATUS_FINISHED.equals(order.getSysStatus())) {
                return "交易完成";
            }
            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                return "交易关闭";
            }
            if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(order.getSysStatus())) {
                return "卖家已发货";
            }
        }
        return "待发货";
    }

    public static Integer getPrintStatus(Date printDate) {
        if (null == printDate) {
            return 0;
        }
        return printDate.after(TradeTimeUtils.INIT_DATE) ? 1 : 0;
    }


    /**
     * 组装日志
     */
    public static void checkUpdateTemplate(Map<String, List<String>> errorMsg, boolean needExcept, boolean isNewTemplateRule) {
        if (errorMsg.size() != 0) {
//            if (CollectionUtils.isEmpty(tradeList)) {
//                throw new IllegalArgumentException("您选择的订单均不允许修改模板!");
//            }
            String msg = appendErrorMsg(errorMsg, isNewTemplateRule);
            if (needExcept) {
                throw new IllegalArgumentException(msg);
            }
        }
    }

    public static String appendErrorMsg(Map<String, List<String>> errorMsg, boolean isNewTemplateRule) {
        StringBuilder msg = new StringBuilder("选中订单中");
        if (CollectionUtils.isNotEmpty(errorMsg.get("wave"))) {
            msg.append("有").append(errorMsg.get("wave").size()).append("笔订单已经进入波次;订单系统编号:").append(errorMsg.get("wave").toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(UN_TB_WAIT_SELLER_SEND_GOODS))) {
            msg.append("有").append(errorMsg.get(UN_TB_WAIT_SELLER_SEND_GOODS).size()).append("笔订单平台状态为非待发货状态;订单系统编号:").append(errorMsg.get(UN_TB_WAIT_SELLER_SEND_GOODS).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_WAIT_BUYER_PAY))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_WAIT_BUYER_PAY).size()).append("笔订单为待付款订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_WAIT_BUYER_PAY).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_WAIT_PACKAGE))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_WAIT_PACKAGE).size()).append("笔订单为待包装订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_WAIT_PACKAGE).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_WAIT_WEIGHT))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_WAIT_WEIGHT).size()).append("笔订单为待称重订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_WAIT_WEIGHT).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_WAIT_SEND_GOODS))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_WAIT_SEND_GOODS).size()).append("笔订单为待发货订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_WAIT_SEND_GOODS).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_SELLER_SEND_GOODS))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_SELLER_SEND_GOODS).size()).append("笔订单为已发货订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_SELLER_SEND_GOODS).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_FINISHED))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_FINISHED).size()).append("笔订单为交易成功订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_FINISHED).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(Trade.SYS_STATUS_CLOSED))) {
            msg.append("有").append(errorMsg.get(Trade.SYS_STATUS_CLOSED).size()).append("笔订单为交易关闭订单;订单系统编号:").append(errorMsg.get(Trade.SYS_STATUS_CLOSED).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPJITX))) {
            msg.append("有").append(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPJITX).size()).append("笔订单为交易关闭订单;订单系统编号:").append(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPJITX).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPSOV))) {
            msg.append("有").append(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPSOV).size()).append("笔订单为唯品会修改运单号订单;订单系统编号:").append(errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPSOV).toString());
        }
        if (CollectionUtils.isNotEmpty(errorMsg.get("splitGet"))) {
            msg.append("有").append(errorMsg.get("splitGet").size()).append("笔订单为拆单一单多包取号订单;订单系统编号:").append(errorMsg.get("splitGet").toString());
        }
        msg.append(String.format("请筛选后再进行修改%s!", isNewTemplateRule ? "快递公司" : "模板"));
        return msg.toString();
    }

    /**
     * 针对pdd订单的一个换模板换单号的校验...个人觉得很奇怪的一个需求
     */
    public static void checkUpdateTemplatePdd(List<Trade> tradeList) {
        List<String> errorSids = new ArrayList<>();
        for (Trade trade : tradeList) {
            if ("pdd".equals(trade.getSource())) {
                if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())) {
                    errorSids.add(trade.getSid().toString());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorSids)) {
            StringBuilder msg =
                    new StringBuilder("您设置了拼多多订单发货后（已发货/交易成功），不允许修改快递模板!以下订单不允许更改模板与单号:").append(errorSids);
            throw new IllegalArgumentException(msg.toString());
        }
    }

    /**
     * 模板变更时过滤订单
     */
    public static List<Trade> templateChangeFilter(List<Trade> tradeList, boolean allowChangeTemplate) {
        List<Trade> filterTrade = new ArrayList<>();
        if (CollectionUtils.isEmpty(tradeList)) {
            return filterTrade;
        }
        for (Trade trade : tradeList) {
            if (allowChangeTemplate && "pdd".equals(trade.getSource()) && (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()))) {
                //pdd已发货过滤
                continue;
            }
            if (TradeUtils.isPoisonBrandDeliverTrade(trade)) {
                //得物直发
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())) {
                //jitx 和 虾皮
                continue;
            }


            filterTrade.add(trade);
        }
        return filterTrade;
    }

    public static void cancelTradeCheck(List<Trade> tradeList, String checkSplitMain) {
        List<Long> haveChildSplitTagList = tradeList.stream()
                .filter(TradeUtils::haveAllChildSplitTag)
                .map(Trade::getSid)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(haveChildSplitTagList)) {
            throw new IllegalArgumentException("该单号为拆分订单获取子母单，只能取消母单号！" + haveChildSplitTagList);
        }

        if (StringUtils.isNotEmpty(checkSplitMain) && "1".equals(checkSplitMain)) {
            List<Long> haveMainSplitTagList = tradeList.stream()
                    .filter(TradeUtils::haveAllMainSplitTag)
                    .map(Trade::getSid)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(haveMainSplitTagList)) {
                throw new SplitCannelCheckPtException("订单" + haveMainSplitTagList + "存在拆分子母单，请确认是否需要清空已获取的快递单号(关联的子母单都会清空）");
            }
        }

        // 小时达O2O订单不能回收单号
        List<Trade> o2oFilterTrades = tradeList.stream().filter(PlatformUtils::isO2ODefaultTradeTemplate).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(o2oFilterTrades)) {
            throw new IllegalArgumentException("O2O（即时配送）订单虚拟运单号不支持回收");
        }
    }

    /**
     * 校验是否为唯品会jitx订单,得物直发订单
     */
    public static void updateTemplateCheckJitx(List<Trade> tradeList, String operation) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        //得物直发订单类型校验
        updateTemplateCheckPoison(tradeList, operation);

        List<Long> jitxTradeSid =
                tradeList.stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(t.getSubSource())).map(Trade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(jitxTradeSid)) {
            throw new IllegalArgumentException("唯品会JITX订单:" + JSONObject.toJSONString(jitxTradeSid) + ",由平台指定发货快递公司和分配快递单号，不能" + operation + "!");
        }
        List<Trade> officialPickUp1688Trades = tradeList.stream().filter(PlatformUtils::is1688OfficialPickUp).collect(Collectors.toList());
        if ("取消单号".equals(operation)) {
            List<Long> shopeeTradeSid =
                    tradeList.stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(t.getSource())).map(Trade::getSid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shopeeTradeSid)) {
                throw new IllegalArgumentException("shopee订单:" + JSONObject.toJSONString(jitxTradeSid) + ",不允许操作:" + operation);
            }
            if (CollectionUtils.isNotEmpty(officialPickUp1688Trades)) {
                throw new IllegalArgumentException("1688官方物流提货订单:" + JSONObject.toJSONString(officialPickUp1688Trades.stream().map(Trade::getSid).collect(Collectors.toList())) + "，由平台指定发货快递公司和分配快递单号，不能取消单号！");
            }
        }
        if ("修改快递模板".equals(operation) || "修改快递公司".equals(operation) || "清空模板".equals(operation)) {
            if (CollectionUtils.isNotEmpty(officialPickUp1688Trades)) {
                throw new IllegalArgumentException("1688官方物流提货订单:" + JSONObject.toJSONString(officialPickUp1688Trades.stream().map(Trade::getSid).collect(Collectors.toList())) + "，由平台指定发货快递公司和分配快递单号，不能修改快递公司！");
            }
        }
        List<Trade> isCancelTradeList = tradeList.stream()
                .filter(trade -> Objects.nonNull(trade.getIsCancel()) && trade.getIsCancel().equals(1))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(isCancelTradeList)) {
            throw new IllegalArgumentException("作废订单:" + JSONObject.toJSONString(jitxTradeSid) + ",不允许操作:" + operation);
        }
    }

    public static void updateTemplateCheckTmAppoint(List<Trade> tradeList, UserWlbExpressTemplate userWlbExpressTemplate, String operation) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Trade> tmAppointTrades = tradeList.stream().filter(trade -> trade.getTagIds().contains("1000000785")).collect(Collectors.toList());
        for (Trade trade : tmAppointTrades) {
            String logisticsCode = trade.getTradeExt().getLogisticsCode();
            if (StringUtils.isBlank(logisticsCode) || "noCode".equals(logisticsCode)) {
                List<MessageMemo> messageMemos = trade.getMessageMemos();
                if (CollectionUtils.isEmpty(messageMemos)) {
                    continue;
                }
                for (MessageMemo messageMemo : messageMemos) {
                    if (StringUtils.isBlank(messageMemo.getLogisticsCode()) || "noCode".equals(messageMemo.getLogisticsCode())) {
                        continue;
                    }
                    logisticsCode = messageMemo.getLogisticsCode();
                    break;
                }
            }
            if (StringUtils.isBlank(logisticsCode) || "noCode".equals(logisticsCode)) {
                continue;
            }
            if (!userWlbExpressTemplate.getCpCode().contains(logisticsCode)) {
                throw new IllegalArgumentException("当前订单在平台对消费者做出了发顺丰的服务承诺，建议按照平台要求进行择配，否则存在电子面单无法打印以及赔付的风险。");
            }
        }
    }


    public static void updateTemplateCheckKuaiShou(List<Trade> tradeList, UserWlbExpressTemplate userWlbExpressTemplate, String operation) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Trade> ksAppointTrades = tradeList.stream().filter(trade -> CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource()) && trade.getTagIds().contains("1000000180")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ksAppointTrades) && !Long.valueOf(505L).equals(userWlbExpressTemplate.getExpressId())) {
            throw new IllegalArgumentException("当前订单为顺丰包邮订单必须使用顺丰速运发货，未使用顺丰速运发货会产生罚款，根据系统内现有的匹配规则未匹配上顺丰速运快递，请手动选择快递模板或者重新设置快递匹配规则后重新匹配");
        }
    }

    /***
     * @DESCRIPTION: 得物直发订单类型校验
     * @Date: 2022/2/8 9:40 上午
     */
    public static void updateTemplateCheckPoison(List<Trade> tradeList, String operation) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        //过滤得物直发订单
        List<Long> posionTradeSid = tradeList.stream()
                .filter(t -> TradeUtils.isPoisonBrandDeliverTrade(t) && !TradeUtils.isPoisonSpecifyLogistics(t))
                .map(Trade::getSid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(posionTradeSid)) {
            throw new IllegalArgumentException("得物直发订单:" + JSONObject.toJSONString(posionTradeSid) + ",不允许操作:" + operation);
        }
        if ("取消单号".equals(operation)) {
            if (CollectionUtils.isNotEmpty(posionTradeSid)) {
                throw new IllegalArgumentException("得物直发订单:" + JSONObject.toJSONString(posionTradeSid) + ",不允许操作:" + operation);
            }
        }
    }

    /**
     * 已经上传的校验 换模板同时获取单号的校验
     */
    public static List<Long> checkUpTemplateOutsid(List<Trade> tradeList, String content, Long templateId, boolean needThrowException) {
//        List<Trade> needChangeTemplateT = new ArrayList<>();
//        for (Trade trade : tradeList) {
//            if (!templateId.equals(trade.getTemplateId())) {
//                needChangeTemplateT.add(trade);
//            }
//        }
        List<Long> uploadConsign = new ArrayList<>();//已经上传 的订单
        if (CollectionUtils.isNotEmpty(tradeList)) {
            uploadConsign = checkUpTemplateOutsid(tradeList, content, needThrowException);
        }
        return uploadConsign;
    }

    /**
     * pdd已经上传的校验 智能处理时用 直接不让用
     */
    public static void checkUpTemplateOutsid(List<Trade> tradeList, String content) {
        List<Long> uploadConsign = checkUpTemplateOutsid(tradeList, content, false);
        if (CollectionUtils.isNotEmpty(uploadConsign)) {
            tradeList.removeIf(t -> uploadConsign.contains(t.getSid()));
            throw new PddCheckPtException("风险提示！拼多多预发货订单更换模版或取消单号可能会造成订单无物流信息被罚款！以下订单已经上传发货:" + JSONObject.toJSONString(uploadConsign) + ",不允许进行:" + content + "操作!");
        }
    }

    public static void batchCannelCheckUp(List<Trade> tradeList, boolean skipDispatched) {
        if (skipDispatched) {
            return;
        }
        List<Long> goodsSidList = tradeList.stream()
                .filter(trade -> TradeStatusUtils.isAfterSendGoods(trade.getStatus()) || TradeStatusUtils.isAfterSendGoods(trade.getUnifiedStatus()) || TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()))
                .map(Trade::getSid)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(goodsSidList)) {

            String msg = "<div style=\"word-break:break-all;\">" +
                    "<div style=\"font-size: 24px;color: red;\">风险提示！！！</div>" +
                    "<span>预发货订单更换单号可能会被平台</span><span style=\"font-size:20px;color:red\">罚款!!!</span><br>" +
                    "<span>已上传发货订单:<span style=\"color:red\">共" + goodsSidList.size() + "个</span></span><br>" +
                    "<span>确认要</span><span style=\"font-size:20px;color:red\">强制执行</span>" +
                    "<span>:" + "取消单号" + "操作?</span>" +
                    "</div>";

            throw new DispatchedPtException(msg);
        }
    }

    /**
     * 所以平台订单通用校验
     */
    public static List<Long> checkUpTemplateOutsid(List<Trade> tradeList, String content, boolean needThrowException) {
        List<Long> uploadConsign = new ArrayList<>();//已经上传 的订单
        for (Trade trade : tradeList) {
            //pdd的订单 有单号 已上传平台 不允许修改
            // 改为通用
            if (StringUtils.isNotBlank(trade.getOutSid()) && Integer.valueOf(1).equals(trade.getIsUpload())) {
                uploadConsign.add(trade.getSid());
            }
        }
        if (needThrowException && CollectionUtils.isNotEmpty(uploadConsign)) {
            String msg = "<div style=\"word-break:break-all;\">" +
                    "<div style=\"font-size: 24px;color: red;\">风险提示！！！</div>" +
                    "<span>预发货订单更换单号可能会被平台</span><span style=\"font-size:20px;color:red\">罚款!!!</span><br>" +
                    "<span>已上传发货订单:" + JSONObject.toJSONString(uploadConsign) + "</span><br>" +
                    "<span>确认要</span><span style=\"font-size:20px;color:red\">强制执行</span>" +
                    "<span>:" + content + "操作?</span>" +
                    "</div>";

            throw new PddCheckPtException(msg);
        }
        return uploadConsign;
    }

    public static String getFormatRemindMessage(String prefix, String content, List<Long> remindSids) {
        String msg = "<div style=\"word-break:break-all;\">" +
                "<div style=\"font-size: 24px;color: red;\">风险提示！！！</div>" +
                "<span>" + prefix + "</span><span style=\"font-size:20px;color:red\">罚款!!!</span><br>" +
                "<span>已上传发货订单:" + JSONObject.toJSONString(remindSids) + "</span><br>" +
                "<span>确认要</span><span style=\"font-size:20px;color:red\">强制执行</span>" +
                "<span>:" + content + "操作?</span>" +
                "</div>";
        return msg;
    }

    /**
     * 校验订单
     */
    public static List<Trade> checkUpdateTemplate(List<Trade> tradeList, TradeConfig tradeConfig) {
        return checkUpdateTemplate(tradeList, null, tradeConfig, false);
    }

    /**
     * 校验订单
     */
    public static List<Trade> checkUpdateTemplate(List<Trade> tradeList, Long templateId, TradeConfig tradeConfig) {
        Integer needExcept = Optional.ofNullable(tradeConfig.getExpressWhetherFilterTrade()).orElse(0);
        return checkUpdateTemplate(tradeList, templateId, tradeConfig, needExcept == 0);
    }

    /**
     * 校验订单 根据快递维度
     */
    public static List<Trade> checkUpdateLogisticsCompany(List<Trade> tradeList, TradeConfig tradeConfig) {
        Integer needExcept = Optional.ofNullable(tradeConfig.getExpressWhetherFilterTrade()).orElse(0);
        return checkUpdateTemplate(tradeList, null, tradeConfig, needExcept == 0);
    }

    /**
     * 校验订单
     */
    public static List<Trade> checkUpdateTemplate(List<Trade> tradeList, Long templateId, TradeConfig tradeConfig, boolean needExcept) {
        List<Trade> allowTrades = new ArrayList<>();
        Map<String, List<String>> errorMsg = new HashMap<>();
        findAllowTrades(tradeList, templateId, tradeConfig, allowTrades, errorMsg);
        checkUpdateTemplate(errorMsg, needExcept, tradeConfig.isNewTemplateRule());
        return allowTrades;
    }

    /**
     * 校验订单Wave
     */
    public static List<Trade> checkWaveUpdateTemplate(List<Trade> tradeList, Long templateId, TradeConfig tradeConfig) {
        List<Trade> allowTrades = new ArrayList<>();
        Map<String, List<String>> errorMsg = new HashMap<>();
        findWaveAllowTrades(tradeList, templateId, tradeConfig, allowTrades, errorMsg);
        checkUpdateTemplate(errorMsg, true, tradeConfig.isNewTemplateRule());
        return allowTrades;
    }

    /**
     * 波次更换模板允许订单
     * 1、系统状态待打印
     * 2、平台状态待发货
     */
    private static void findWaveAllowTrades(List<Trade> tradeList, Long templateId, TradeConfig tradeConfig, List<Trade> allowTrades, Map<String, List<String>> errorMsg) {
        for (Trade trade : tradeList) {
            if (trade.getTemplateId() != null && trade.getTemplateId().equals(templateId)) {
                allowTrades.add(trade);
                continue;
            }
            String sysStatus = convertSysStatus(trade, tradeConfig);
            if (!Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(sysStatus)) {
                errorMsg.computeIfAbsent(sysStatus, k -> Lists.newArrayList()).add(trade.getSid().toString());
            } else if (StringUtils.isNotEmpty(trade.getUnifiedStatus()) && !(Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getUnifiedStatus()) || Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(trade.getUnifiedStatus()))) {
                errorMsg.computeIfAbsent(UN_TB_WAIT_SELLER_SEND_GOODS, k -> Lists.newArrayList()).add(trade.getSid().toString());
            }
            allowTrades.add(trade);
        }
    }

    private static void findAllowTrades(List<Trade> tradeList, Long templateId, TradeConfig tradeConfig, List<Trade> allowTrades, Map<String, List<String>> errorMsg) {
        for (Trade trade : tradeList) {
            if (trade.getTemplateId() != null && trade.getTemplateId().equals(templateId)) {
                allowTrades.add(trade);
                continue;
            }
            String sysStatus = convertSysStatus(trade, tradeConfig);
            if (checkLongNotEmpty(trade.getWaveId())) {
                //已经进入波次
                List<String> errorSids = errorMsg.get("wave");
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put("wave", errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {
                //待付款
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_WAIT_BUYER_PAY);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_WAIT_PACKAGE.equals(sysStatus)) {
                //待包装
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_WAIT_PACKAGE);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_WAIT_PACKAGE, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_WAIT_WEIGHT.equals(sysStatus)) {
                //待称重
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_WAIT_WEIGHT);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_WAIT_WEIGHT, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(sysStatus)) {
                //待发货
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_WAIT_SEND_GOODS);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_WAIT_SEND_GOODS, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)) {
                //已发货
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_FINISHED.equals(sysStatus)) {
                //交易成功
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_FINISHED);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_FINISHED, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (Trade.SYS_STATUS_CLOSED.equals(sysStatus)) {
                //交易关闭
                List<String> errorSids = errorMsg.get(Trade.SYS_STATUS_CLOSED);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(Trade.SYS_STATUS_CLOSED, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
                //vipjitx订单
                List<String> errorSids = errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPJITX);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(CommonConstants.PLAT_FORM_TYPE_VIPJITX, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (TradeUtils.haveMPUpdateTag(trade)) {
                List<String> errorSids = errorMsg.get(CommonConstants.PLAT_FORM_TYPE_VIPSOV);
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put(CommonConstants.PLAT_FORM_TYPE_VIPSOV, errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else if (TradeUtils.haveAllSplitGetTag(trade)) {
                List<String> errorSids = errorMsg.get("splitGet");
                if (CollectionUtils.isEmpty(errorSids)) {
                    errorSids = new ArrayList<>();
                    errorMsg.put("splitGet", errorSids);
                }
                errorSids.add(trade.getSid().toString());
            } else {
                allowTrades.add(trade);
            }
        }
    }

    /**
     * 校验订单是否允许修改模板
     *
     * @param trade 订单
     * @return 是否允许修改
     */
    public static boolean checkUpdateTemplate(Trade trade) {
        String sysStatus = trade.getSysStatus();
        //是否打印 1 已打 0 未打
        Integer isPrint = getPrintStatus(trade.getExpressPrintTime());
        //未打印 且未进入波次订单 且 不是待付款订单  则表示是 待审核或 待打印订单
        if (isPrint.equals(0) && !checkLongNotEmpty(trade.getWaveId()) && !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {
            return true;
        }
        return false;
    }

    private static boolean checkLongNotEmpty(Long value) {
        return value != null && value > 0L;
    }

    private static boolean getIs(Integer is) {
        return !(is == null || is == 0);
    }

    /**
     * 判断一个状态是否为订单系统状态
     *
     * @param sysStatus
     * @return
     */
    public static boolean isSysStatus(String sysStatus) {
        return Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus) || Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus) ||
                Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(sysStatus) || Trade.SYS_STATUS_WAIT_MANUAL_AUDIT.equals(sysStatus) ||
                Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus) || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) ||
                Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus);
    }

    public static boolean iseBeforeSendGoods(String sysStatus) {
        return !Trade.SYS_STATUS_FINISHED.equals(sysStatus) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus);
    }

    /**
     * 判断订单系统状态是否在发货之后
     *
     * @param sysStatus 订单系统状态
     * @return boolean
     */
    public static boolean isAfterSendGoods(String sysStatus) {
        return Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus) || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus);
    }

    public static List<String> AFTER_SEND_GOODS_STATUS = Arrays.asList(Trade.SYS_STATUS_FINISHED,Trade.SYS_STATUS_CLOSED,Trade.SYS_STATUS_SELLER_SEND_GOODS);

    /**
     * 判断订单系统状态是否在打印之后
     * WAIT_PACKAGE,WAIT_WEIGHT,WAIT_SEND_GOODS,SELLER_SEND_GOODS,FINISHED
     *
     * @param sysStatus 订单系统状态
     * @return boolean
     */
    public static boolean isAfterPrint(String sysStatus) {
        return Trade.SYS_STATUS_WAIT_PACKAGE.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_WEIGHT.equals(sysStatus)
                || Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(sysStatus)
                || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)
                || Trade.SYS_STATUS_FINISHED.equals(sysStatus);
    }

    public static List<String> AFTER_PRINT_SYS_STATUS = Arrays.asList(Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_WAIT_SEND_GOODS,
            Trade.SYS_STATUS_FINISHED,Trade.SYS_STATUS_CLOSED,Trade.SYS_STATUS_SELLER_SEND_GOODS);

    /**
     * 判断一个系统状态是否为待发货（目前包括待审核与已审核）
     *
     * @param sysStatus
     */
    public static boolean isWaitSellerSend(String sysStatus) {
        return isWaitAudit(sysStatus) || isWaitFinanceAudit(sysStatus) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus);
    }

    /**
     * 判断一个系统状态是否为待审核
     *
     * @param sysStatus
     */
    public static boolean isWaitAudit(String sysStatus) {
        return Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus);
    }

    /**
     * 判断一个系统状态是否为待待财审
     *
     * @param sysStatus
     */
    public static boolean isWaitFinanceAudit(String sysStatus) {
        return Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(sysStatus);
    }

    /**
     * 判断一个系统状态是否为待付款
     *
     * @param sysStatus
     */
    public static boolean isWaitPay(String sysStatus) {
        return Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus);
    }

    /**
     * 根据trade的子订单集合的状态计算订单的sysStatus状态
     *
     * @param trade
     * @return String
     */
    public static String getTradeSysStatus(Trade trade) {
        return getTradeSysStatus(trade, TradeUtils.getOrders4Trade(trade));
    }

    /**
     * 根据平台子订单的状态，获取主订单的状态
     *
     * @param platOrders
     * @return
     */
    public static String getTradeSysStatus(Trade trade, List<Order> platOrders) {
        String sysStatus = getTradeSysStatus(platOrders);
        if (StringUtils.isNotEmpty(sysStatus)) {
            return sysStatus;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(trade.getStatus())) {
            sysStatus = TradeStatus.getSysStatus(trade.getStatus(), trade.getSysStatus());
            if (getSysStatusWeight(sysStatus) > getSysStatusWeight(trade.getSysStatus())) {
                return sysStatus;
            }
        }
        return trade.getSysStatus();
    }

    public static String getTradeSysStatus(List<Order> platOrders) {
        List<String> platOrderSysStatuses = platOrders.stream()
                .filter(order -> !Objects.equals(order.getEnableStatus(), 0))
                .map(Order::getSysStatus)
                .collect(Collectors.toList());
        return getTradeSysStatusByOrderSysStatuses(platOrderSysStatuses);
    }

    public static String getTradeSysStatusByOrderSysStatuses(List<String> orderSysStatuses) {
        boolean[] flag = {false, false, false, false, false, false, false};
        for (String sysStatus : orderSysStatuses) {
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus) && !flag[0]) {
                flag[0] = true;
            } else if (isWaitAudit(sysStatus) && !flag[1]) {
                flag[1] = true;
            } else if (isWaitFinanceAudit(sysStatus) && !flag[2]) {
                flag[2] = true;
            } else if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus) && !flag[2]) {
                flag[3] = true;
            } else if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) && !flag[3]) {
                flag[4] = true;
            } else if (Trade.SYS_STATUS_FINISHED.equals(sysStatus) && !flag[4]) {
                flag[5] = true;
            } else if (Trade.SYS_STATUS_CLOSED.equals(sysStatus) && !flag[5]) {
                flag[6] = true;
            }
        }
        if (flag[0]) {
            return Trade.SYS_STATUS_WAIT_BUYER_PAY;//待付款
        } else if (flag[1]) {
            return Trade.SYS_STATUS_WAIT_AUDIT;//待审核
        } else if (flag[2]) {
            return Trade.SYS_STATUS_WAIT_FINANCE_AUDIT;
        } else if (flag[3]) {
            return Trade.SYS_STATUS_FINISHED_AUDIT;//审核完成
        } else if (flag[4]) {
            return Trade.SYS_STATUS_SELLER_SEND_GOODS;//卖家已发货
        } else if (flag[5]) {
            return Trade.SYS_STATUS_FINISHED;//交易完成
        } else if (flag[6]) {
            return Trade.SYS_STATUS_CLOSED;//交易关闭
        }
        return null;
    }

    /**
     * 获取订单系统状态的权重,如果未匹配到，则返回10000
     *
     * @param sysStatus 系统状态
     * @return int
     */
    public static int getSysStatusWeight(String sysStatus) {
        if (sysStatus == null) {
            return -1;
        } else if (Trade.SYS_STATUS_CLOSED.equals(sysStatus)) {
            return TradeConstants.WEIGHT_CLOSED;
        } else if (Trade.SYS_STATUS_FINISHED.equals(sysStatus)) {
            return TradeConstants.WEIGHT_FINISHED;
        } else if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)) {
            return TradeConstants.WEIGHT_SELLER_SEND_GOODS;
        } else if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus)) {
            return TradeConstants.WEIGHT_FINISHED_AUDIT;
        } else if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {
            return TradeConstants.WEIGHT_WAIT_BUYER_PAY;
        } else if (Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus)) {
            return TradeConstants.WEIGHT_WAIT_AUDIT;
        } else if (Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(sysStatus)) {
            return TradeConstants.WEIGHT_WAIT_FINANCE_AUDIT;
        } else if (Trade.SYS_STATUS_WAIT_MANUAL_AUDIT.equals(sysStatus)) {
            return TradeConstants.WEIGHT_WAIT_MANUAL_AUDIT;
        }
        return 10000;
    }

    /**
     * 比较两个系统状态的优先级
     *
     * @param systatus1
     * @param systatus2
     * @return int
     */
    public static int compareSystatus(String systatus1, String systatus2) {
        return getSysStatusWeight(systatus1) - getSysStatusWeight(systatus2);
    }


    public static boolean equalsSysStatusView(Trade trade, TradeConfig tradeConfg, String validSysStatus) {
        return convertSysStatusToView(trade, tradeConfg).equals(validSysStatus);
    }


    /**
     * 转换系统状态给视图展现
     *
     * @param trade
     * @return
     */
    public static String convertSysStatusToView(Trade trade, TradeConfig tradeConfig) {
        //出库单不验证店铺
        if (trade.isOutstock()) {
            return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) ? Trade.SYS_STATUS_WAIT_SEND_GOODS : trade.getSysStatus();
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
            return Trade.SYS_STATUS_CANCEL;
        }
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return trade.getSysStatus();
        }
        // 无需快递的类型，直接就是待发货状态
        if (trade.getTemplateId() != null && trade.getTemplateId() == 0L) {
            return Trade.SYS_STATUS_WAIT_SEND_GOODS;
        }
        if (getPrintStatus(trade.getExpressPrintTime()) == 0 && !TradeUtils.isFxTrade(trade)) {
            return Trade.SYS_STATUS_WAIT_EXPRESS_PRINT;
        }
        if (getPrintStatus(trade.getExpressPrintTime()) == 0 && (TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isAlibabaFxRoleTrade(trade))) {
            return Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS;
        }
        if (tradeConfig.getOpenDeliverPrint() == 1 && getPrintStatus(trade.getDeliverPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_DELIVERY_PRINT;
        }
        if (tradeConfig.getOpenPackageExamine() == 1 && !getIs(trade.getIsPackage())) {
            return Trade.SYS_STATUS_WAIT_PACKAGE;
        }
        if (tradeConfig.getOpenPackageWeigh() == 1 && !getIs(trade.getIsWeigh())) {
            return Trade.SYS_STATUS_WAIT_WEIGHT;
        }
        return Trade.SYS_STATUS_WAIT_SEND_GOODS;
    }

    public static String getChSysStatusView(String sysStatus) {
        return VIEW_CH_STATUS_MAP.get(sysStatus);
    }

    public static boolean validSysStatus(Trade trade, TradeConfig tradeConfig, Set<String> validSysStatusSet) {
        if (validSysStatusSet == null || validSysStatusSet.size() <= 0) {
            return true;
        }

        String tradeSysStatusView = convertSysStatusToView(trade, tradeConfig);
        boolean valid = validSysStatusSet.contains(tradeSysStatusView);
        if (!valid &&
                validSysStatusSet.contains(Trade.SYS_STATUS_FINISHED_AUDIT) &&
                (tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_PACKAGE) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_WEIGHT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_SEND_GOODS))) {
            valid = true;
        }
        return valid;
    }

    public static void validSysStatus(Trade trade, TradeConfig tradeConfig, Set<String> validSysStatusSet, String optMsg) {
        if (!validSysStatus(trade, tradeConfig, validSysStatusSet)) {
            throw new IllegalArgumentException(String.format("订单%s系统状态为%s,无法%s", trade.getSid(), getChSysStatusView(convertSysStatusToView(trade, tradeConfig)), optMsg));
        }
    }

    public static void validSysStatus(List<Trade> tradeList, TradeConfig tradeConfig, String[] validSysStatusArray, String optMsg) {
        if (validSysStatusArray == null || validSysStatusArray.length <= 0) {
            return;
        }
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        Set<String> validSysStatusSet = new HashSet<String>();

        for (String validStatus : validSysStatusArray) {
            validSysStatusSet.add(validStatus);
        }

        for (Trade trade : tradeList) {
            validSysStatus(trade, tradeConfig, validSysStatusSet, optMsg);
        }
    }

    public static boolean isWaitPrint(Trade trade) {
        return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && getPrintStatus(trade.getExpressPrintTime()) == 0;
    }

    public static boolean isWaitPack(Trade trade, TradeConfig tc) {
        return isWaitPack(trade, tc.getOpenPackageExamine());
    }

    public static boolean isWaitPack(Trade trade, int openPackageExamine) {
        return openPackageExamine == 1 && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && getPrintStatus(trade.getExpressPrintTime()) == 1 && trade.getIsPackage() == 0;
    }

    public static boolean isWaitWeigh(Trade trade, TradeConfig tc) {
        return isWaitWeigh(trade, tc.getOpenPackageExamine(), tc.getOpenPackageWeigh());
    }

    public static boolean isWaitWeigh(Trade trade, int openPackageExamine, int openPackageWeigh) {
        return openPackageWeigh == 1 && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && getPrintStatus(trade.getExpressPrintTime()) == 1 && trade.getIsWeigh() == 0 && (openPackageExamine == 0 || (openPackageExamine == 1 && trade.getIsPackage() == 1));
    }

    public static boolean isWaitConsign(Trade trade, TradeConfig tc) {
        return isWaitConsign(trade, tc.getOpenPackageExamine(), tc.getOpenPackageWeigh());
    }

    public static boolean isWaitConsign(Trade trade, int openPackageExamine, int openPackageWeigh) {
        boolean isOpenPack = openPackageExamine == 1;
        boolean isOpenWeight = openPackageWeigh == 1;
        boolean isPrinted = Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && getPrintStatus(trade.getExpressPrintTime()) == 1;
        if (isOpenPack && isOpenWeight) {
            return isPrinted && trade.getIsPackage() == 1 && trade.getIsWeigh() == 1;
        } else if (isOpenPack) {
            return isPrinted && trade.getIsPackage() == 1;
        } else if (isOpenWeight) {
            return isPrinted && trade.getIsWeigh() == 1;
        }
        return isPrinted;
    }

    public static boolean isFinishAuditButNotPrint(Trade trade) {
        return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && (trade.getExpressPrintTime() != null && trade.getExpressPrintTime().compareTo(TradeTimeUtils.INIT_DATE) == 0);
    }

    public static boolean ifAutoCancelAudit(Trade trade, TradeConfig tc) {
        Object o = tc.get("autoCancelAuditStatus");
        if (o != null) {
            return ifAutoCancelAudit(trade, (int) o, tc.getOpenPackageExamine(), tc.getOpenPackageWeigh());
        }
        //兼容以前老数据，只有待打印订单才自动反审核
        return isWaitPrint(trade);
    }

    public static boolean ifAutoCancelAudit(Trade trade, Integer autoCancelAuditStatus, Integer openPackageExamine, Integer openPackageWeigh) {
        if (autoCancelAuditStatus == null || autoCancelAuditStatus == 0) {
            return false;
        }
        return (NumberUtils.has(autoCancelAuditStatus, 1) && isWaitPrint(trade))
                || (NumberUtils.has(autoCancelAuditStatus, 2) && isWaitPack(trade, openPackageExamine))
                || (NumberUtils.has(autoCancelAuditStatus, 4) && isWaitWeigh(trade, openPackageExamine, openPackageWeigh))
                || (NumberUtils.has(autoCancelAuditStatus, 8) && isWaitConsign(trade, openPackageExamine, openPackageWeigh))
                || (NumberUtils.has(autoCancelAuditStatus, 16) && isWaitFinanceAudit(trade.getSysStatus()));
    }

    /**
     * 返回订单状态的中文
     *
     * @return
     */
    public static String getSysStatusToString(String sysStatus) {
        String str = new String("");
        switch (sysStatus) {
            /**
             * 待付款
             */
            case Trade.SYS_STATUS_WAIT_BUYER_PAY: {
                str = new String("待付款");
                break;
            }

            /**
             * 待审核
             */
            case Trade.SYS_STATUS_WAIT_AUDIT: {
                str = new String("待审核");
                break;
            }

            /**
             * 等待财审
             */
            case Trade.SYS_STATUS_WAIT_FINANCE_AUDIT: {
                str = new String("待财审");
                break;
            }
            /**
             * 等待人工审核，一般由系统的自动审核触发，如果订单在自动审核的过程中被拒绝了，那么状态将转变为人工审核
             */
            case Trade.SYS_STATUS_WAIT_MANUAL_AUDIT: {
                str = new String("待人工审核");
                break;
            }
            /**
             * 审核完成
             */
            case Trade.SYS_STATUS_FINISHED_AUDIT: {
                str = new String("审核完成");
                break;
            }

            /**
             * 待打印快递单，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
             */
            case Trade.SYS_STATUS_WAIT_EXPRESS_PRINT: {
                str = new String("待打印快递单");
                break;
            }

            /**
             * 待打印发货单，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
             */
            case Trade.SYS_STATUS_WAIT_DELIVERY_PRINT: {
                str = new String("待打印发货单");
                break;
            }

            /**
             * 待打包，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
             */
            case Trade.SYS_STATUS_WAIT_PACKAGE: {
                str = new String("待打包");
                break;
            }

            /**
             * 待称重，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
             */
            case Trade.SYS_STATUS_WAIT_WEIGHT: {
                str = new String("待称重");
                break;
            }

            /**
             * 待发货，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
             */
            case Trade.SYS_STATUS_WAIT_SEND_GOODS: {
                str = new String("待发货");
                break;
            }

            /**
             * 卖家已发货
             */
            case Trade.SYS_STATUS_SELLER_SEND_GOODS: {
                str = new String("卖家已发货");
                break;
            }

            /**
             * 交易完成
             */
            case Trade.SYS_STATUS_FINISHED: {
                str = new String("交易完成");
                break;
            }

            /**
             * 交易关闭
             */
            case Trade.SYS_STATUS_CLOSED: {
                str = new String("交易关闭");
                break;
            }

            /**
             * 交易作废
             */
            case Trade.SYS_STATUS_CANCEL: {
                str = new String("交易作废");
                break;
            }

        }

        return str;
    }

    /**
     * @param trade
     * @return java.lang.Boolean
     * <AUTHOR>
     * @description 是否待付款变更为待审核
     * @date 2021/5/12 3:48 下午
     */
    public static Boolean waitPay2WaitAudit(Trade trade) {
        return Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus());
    }

    /**
     * 检查订单是否允许按比例换商品
     * 需要满足以下条件
     * 订单已审核
     * 正常订单状态(包括缺货异常)
     * 未进入波次
     * 未打印
     * 未生成唯一码
     *
     * @return true- 允许，false-不允许
     */
    public static boolean checkTradeCanChangeItemByRatio(Staff staff, Trade trade, ChangeItemBatchSource source, TradeConfig tradeConfig) {
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(trade.getSysStatus())) {
            //非已审核
            return false;
        }

        try {
            List<String> exceptionsByConfig = TradeConfigUtils.getExceptionsByConfig(tradeConfig, NewTradeExtendConfigEnum.CHANGE_ITEM_EXCEPTION);
            //状态有异常
            if (trade.getIsExcep() != null && trade.getIsExcep() > 0) {
                if (CollectionUtils.isNotEmpty(exceptionsByConfig)) {
                    List<String> systemExceptions = TradeExceptionUtils.analyze(null, trade).stream().collect(Collectors.toList());
                    Set<String> customExceptionArr = TradeExceptUtils.getCustomExceptIds(staff, trade).stream().map(String::valueOf).collect(Collectors.toSet());
                    for (String excep : systemExceptions) {
                        if (!exceptionsByConfig.contains(excep)) {
                            return false;
                        }
                    }
                    for (String excep : customExceptionArr) {
                        if (!exceptionsByConfig.contains(excep)) {
                            return false;
                        }
                    }
                } else {
                    return false;
                }
            }
        } catch (Exception e) {
            Logs.error(String.format("判断异常出错[sid=%s]", trade.getSid()));
        }


        int isPrint = getPrintStatus(trade.getExpressPrintTime());
        if (isPrint == 1) {
            //已打印
            return false;
        }

        if (source == null || Objects.equals(ChangeItemBatchSource.BY_RATIO.getValue(), source.getValue())) {
            if (checkInWave(trade.getWaveId())) {
                //已进入波次，生成唯一码的前提是进入波次，所以不需要单独判断是否生成唯一码
                return false;
            }
        }

        return true;
    }

    private static boolean checkInWave(Long waveId) {
        return waveId != null && !waveId.equals(0L);
    }


    /**
     * 转换系统状态，这里主要转换已审核的订单状态
     *
     * @param trade
     * @return
     */
    public static String convertSysStatus(Trade trade, Integer openDeliverPrint, Integer openPackageExamine, Integer openPackageWeight) {
        //出库单不验证店铺
        if (trade.isOutstock()) {
            return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) ? Trade.SYS_STATUS_WAIT_SEND_GOODS : trade.getSysStatus();
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
            return Trade.SYS_STATUS_CANCEL;
        }
        if (TradeUtils.isPlatformFxTrade(trade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
            return Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS;
        }
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return trade.getSysStatus();
        }
        // 无需快递的类型，直接就是待发货状态
        if (trade.getTemplateId() != null && trade.getTemplateId() == 0L && !TradeUtils.isFxOrMixTrade(trade)) {
            return Trade.SYS_STATUS_WAIT_SEND_GOODS;
        }
        if ((TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isAlibabaFxRoleTrade(trade))&& Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS;
        }
        if (getPrintStatus(trade.getExpressPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_EXPRESS_PRINT;
        }
        if (openDeliverPrint != null && openDeliverPrint == 1 && getPrintStatus(trade.getDeliverPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_DELIVERY_PRINT;
        }
        if (openPackageExamine != null && openPackageExamine == 1 && !getIs(trade.getIsPackage())) {
            return Trade.SYS_STATUS_WAIT_PACKAGE;
        }
        if (openPackageWeight != null && openPackageWeight == 1 && !getIs(trade.getIsWeigh())) {
            return Trade.SYS_STATUS_WAIT_WEIGHT;
        }
        return Trade.SYS_STATUS_WAIT_SEND_GOODS;
    }
}

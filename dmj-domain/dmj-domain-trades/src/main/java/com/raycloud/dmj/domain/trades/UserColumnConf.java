package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * Created by ruanyg on 16/5/24.
 */
@Table(name = "user_column_conf")
public class UserColumnConf extends Model {

    private static final long serialVersionUID = 8668102748618018340L;

    /**
     * 卖家公司ID
     */
    private Long companyId;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 列所属页面ID
     */
    private Long pageId;

    /**
     * 列ID
     */
    private Long colId;

    /**
     * 是否可见: 1可见, 0不可见
     */
    private Integer visible;

    /**
     * 列在当前页面的排序号
     */
    private Integer sortNo;

    /**
     * 列宽度
     */
    private Integer width;


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}

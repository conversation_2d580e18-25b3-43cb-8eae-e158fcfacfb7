package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/21 16:35
 */
@Data
@NoArgsConstructor
public class OrderHotItem extends Model {

    private Long id;
    private String digest;
    private Long companyId;
    private Long sid;
    private String title;
    private String numIid;
    private String skuId;
    private String outerIid;
    private String outerSkuId;
    private String skuPropertiesName;
    private Long itemSysId;
    private Long skuSysId;
    private String sysOuterId;
    private String sysSkuPropertiesName;
    private String sysTitle;
    private String shortTitle;
    private String sysSkuPropertiesAlias;
    private String sysItemRemark;
    private String sysSkuRemark;
    private Integer isVirtual;
    private Integer type;
    private Integer isGift;
    private String cids;
    private Date createTime;
    private Date updTime;
    private Long combineId;

    public static OrderHotItem valueOf(Order order, Long id) {
        OrderHotItem hotItem = new OrderHotItem();
        hotItem.setId(id);
        hotItem.setSid(order.getSid());
        hotItem.setCompanyId(order.getCompanyId());
        hotItem.setTitle(order.getTitle());
        hotItem.setNumIid(order.getNumIid());
        hotItem.setSkuId(order.getSkuId());
        hotItem.setOuterIid(order.getOuterIid());
        hotItem.setOuterSkuId(order.getOuterSkuId());
        hotItem.setSkuPropertiesName(order.getSkuPropertiesName());
        hotItem.setItemSysId(order.getItemSysId());
        hotItem.setSkuSysId(order.getSkuSysId());
        hotItem.setSysOuterId(order.getSysOuterId());
        hotItem.setSysSkuPropertiesName(order.getSysSkuPropertiesName());
        hotItem.setSysTitle(order.getSysTitle());
        hotItem.setShortTitle(order.getShortTitle());
        hotItem.setSysSkuPropertiesAlias(order.getSysSkuPropertiesAlias());
        hotItem.setSysItemRemark(order.getSysItemRemark());
        hotItem.setSysSkuRemark(order.getSysSkuRemark());
        hotItem.setIsVirtual(order.getIsVirtual());
        hotItem.setType(order.getType());
        hotItem.setIsGift(order.getGiftNum() != null && order.getGiftNum() > 0 ? 1 : 0);
        hotItem.setCids(order.getCids());
        hotItem.setCombineId(order.getCombineId());
        return hotItem;
    }

    public static String hash(OrderHotItem item) {
        return item.getTitle() +
                item.getNumIid() +
                item.getSkuId() +
                item.getOuterIid() +
                item.getOuterSkuId() +
                item.getSkuPropertiesName() +
                item.getItemSysId() +
                item.getSkuSysId() +
                item.getSysOuterId() +
                item.getSysSkuPropertiesName() +
                item.getSysTitle() +
                item.getShortTitle() +
                item.getSysSkuPropertiesAlias() +
                item.getSysItemRemark() +
                item.getSysSkuRemark() +
                item.getIsVirtual() +
                item.getType() +
                item.getIsGift();
    }
}

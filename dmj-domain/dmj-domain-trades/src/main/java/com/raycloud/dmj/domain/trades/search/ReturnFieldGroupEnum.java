package com.raycloud.dmj.domain.trades.search;

import java.util.Objects;

/**
 * @Description <pre>
 * COMMON 场景下 前端指定需要填充的内容
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-12
 */
public enum ReturnFieldGroupEnum {

    FULL_ORDER(1,"完整Order"),
    RECEIVER(2,"收件人信息"),

    LOGISTICS(3,"物流信息"),

    EXCEPTION(4,"异常信息"),
        ;

    int key;
    String desc;

    ReturnFieldGroupEnum(int key,String desc){
        this.desc= desc;
        this.key= key;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static ReturnFieldGroupEnum getByKey(Integer key){
        if (key == null) {
            return null;
        }
        for (ReturnFieldGroupEnum value : ReturnFieldGroupEnum.values()) {
            if (Objects.equals(value.key,key)) {
                return value;
            }
        }
        return null;
    }

}

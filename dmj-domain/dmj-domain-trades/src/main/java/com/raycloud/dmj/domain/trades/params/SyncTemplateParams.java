package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 同步模板参数
 *
 * @Date 2021/7/12
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncTemplateParams implements Serializable {

    private Staff staff;

}

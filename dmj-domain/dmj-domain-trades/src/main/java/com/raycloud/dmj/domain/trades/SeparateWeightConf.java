package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.List;

/**
 * SeparateWeightConf
 *
 * <AUTHOR>
 * @Date 2019/9/17
 * @Time 10:53
 */
@Table(name = "separate_weight_conf")
public class SeparateWeightConf extends Model {
    private static final long serialVersionUID = 6259005643131111873L;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     *  接入方式
     *  后台 BACKEND
     *  端口 PORT
     */
    private String accessType;

    /**
     *  accessType时候必填数据，对接端口
     */
    private String port;

    /**
     * 称重类型
     *  0 或 null: 发货前称重
     *  1 发货后称重
     *  2 发货前重复称重
     *  3 发货后重复称重
     */
    private Long weightKind;

    private String expressChannelConfStr;

    /**
     * 称重快递通道配置
     */
    private String expressChannelConfList;

    private String machineConfStr;

    /**
     * 开启重量校验 0关闭 1按重量 2按百分比
     */
    private Integer openWeightCheck;

    /**
     * 重量上浮
     */
    private Double upperLimit;

    /**
     * 重量下浮
     */
    private Double lowerLimit;

    /**
     * 重量百分比上浮
     */
    private Double percentUpperLimit;

    /**
     * 重量百分比下浮
     */
    private Double percentLowerLimit;

    private List<SeparateWeightExpressConf> expressChannelConfs;

    private List<SeparateWeightMachineConf> machineConfs;


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getAccessType() {
        return accessType;
    }

    public void setAccessType(String accessType) {
        this.accessType = accessType;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public Long getWeightKind() {
        return weightKind;
    }

    public void setWeightKind(Long weightKind) {
        this.weightKind = weightKind;
    }

    public String getExpressChannelConfStr() {
        return expressChannelConfStr;
    }

    public void setExpressChannelConfStr(String expressChannelConfStr) {
        this.expressChannelConfStr = expressChannelConfStr;
    }

    public String getExpressChannelConfList() {
        return expressChannelConfList;
    }

    public void setExpressChannelConfList(String expressChannelConfList) {
        this.expressChannelConfList = expressChannelConfList;
    }

    public String getMachineConfStr() {
        return machineConfStr;
    }

    public void setMachineConfStr(String machineConfStr) {
        this.machineConfStr = machineConfStr;
    }

    public List<SeparateWeightExpressConf> getExpressChannelConfs() {
        return expressChannelConfs;
    }

    public void setExpressChannelConfs(List<SeparateWeightExpressConf> expressChannelConfs) {
        this.expressChannelConfs = expressChannelConfs;
    }

    public List<SeparateWeightMachineConf> getMachineConfs() {
        return machineConfs;
    }

    public void setMachineConfs(List<SeparateWeightMachineConf> machineConfs) {
        this.machineConfs = machineConfs;
    }

    public Integer getOpenWeightCheck() {
        return openWeightCheck;
    }

    public void setOpenWeightCheck(Integer openWeightCheck) {
        this.openWeightCheck = openWeightCheck;
    }

    public Double getUpperLimit() {
        return upperLimit;
    }

    public void setUpperLimit(Double upperLimit) {
        this.upperLimit = upperLimit;
    }

    public Double getLowerLimit() {
        return lowerLimit;
    }

    public void setLowerLimit(Double lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    public Double getPercentUpperLimit() {
        return percentUpperLimit;
    }

    public void setPercentUpperLimit(Double percentUpperLimit) {
        this.percentUpperLimit = percentUpperLimit;
    }

    public Double getPercentLowerLimit() {
        return percentLowerLimit;
    }

    public void setPercentLowerLimit(Double percentLowerLimit) {
        this.percentLowerLimit = percentLowerLimit;
    }
}

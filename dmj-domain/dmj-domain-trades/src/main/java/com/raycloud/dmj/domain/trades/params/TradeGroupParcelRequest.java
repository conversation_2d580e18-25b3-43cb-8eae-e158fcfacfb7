package com.raycloud.dmj.domain.trades.params;

import lombok.Data;

import java.util.Date;

/**
 * 批量拆分小包请求体
 */
@Data
public class TradeGroupParcelRequest {
    /**
     * 订单id
     */
    private String sid;
    /**
     * 订单列表集合
     */
    private String sids;

    /**
     * 所属店铺
     */
    private Long taobaoId;
    /**
     * 所属店铺id
     */
    private String userId;
    /**
     * 快递模板
     */
    private Long templateId;
    /**
     * 快递模板名称
     */
    private String templateName;
    /**
     * 发货仓库id
     */
    private String consignWarehouseId;

    /**
     * 中转仓库名称
     */
    private String transferWarehouseName;

    /**
     * 收集类型
     */
    private Integer gatherType;

    /**
     * 平台来源
     */
    private String source;
    /**
     * 发货开始时间
     */
    private Long startTime;
    /**
     * 发货结束时间
     */
    private Long endTime;
    /**
     * 发货开始时间
     */
    private Date consignStartTime;
    /**
     * 发货结束时间
     */
    private Date consignEndTime;

    /**
     * 大包生成数量
     */
    private Integer packageNum;

    /**
     * 物流渠道商
     */
    private String shippingCarrier;
    /**
     * 平台ID
     */
    private String tid;
    /**
     * 运单号
     */
    private String outSid;

    /**
     * 内部单号
     */
    private String shortId;

    /**
     * 是否erp发货 1是 2否
     */
    private Integer sysConsigned;

    /**
     * 预约交货方式（0：大包预约，1：批次预约））
     */
    private Integer appointmentType;

    /**
     * 物流公司CODE
     * com.raycloud.dmj.merchantCode.base.SumaitongBigbagEnum
     */
    private String expressCode;

    /**
     * 物流单号
     */
    private String trackingNo;

    /**
     * 中转仓地址ID
     */
    private String abroadAddressId;
    /**
     * 是否预发货
     */
    private String tagIds;

    /**
     * tiktok 卖家昵称
     */
    private String tiktokNick;

    /**
     * tiktok 站点分组
     * 1.SG(新加坡)/MY(马来西亚
     * 2.TH(泰国)/PH(菲律宾)/VN(越南)
     */
    private Integer tiktokSite;


}

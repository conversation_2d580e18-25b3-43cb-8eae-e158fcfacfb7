package com.raycloud.dmj.domain.trades.warehouse;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.pt.WarehouseAllocate;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.vo.SysItemSkuVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21 10:14
 */
@Data
public class WarehouseMatchVo implements Serializable {

    private String id;

    private String warehouseId;

    private String ruleName;

    private String areaStr;

    private String shopStr;

    private Integer isOpen;

    private Integer minItemNum;

    private Integer maxItemNum;

    private Integer minItemKindNum;

    private Integer maxItemKindNum;

    private Double minWeight;

    private Double maxWeight;

    private Double minVolume;

    private Double maxVolume;

    private Integer isSplit;

    private String priority;

    private JSONObject tradeType;

    private Integer hasItem;

    private List<SysItemSkuVo> itemSkuList;

    private Integer ItemCount;

    private Integer isMatchItemNum;

    private List<SpelCondition> conditions;

    private String  sourceIds;

    private Integer splitType;

    private String splitKeys;

    /**
     * 商品品牌
     */
    private String  brandIds;

    /**
     * 供销商
     */
    private String  supplierIds;

    /**
     * 供应商分类合集，逗号分隔开
     */
    private String supplierCats;

    /**
     * 自动拆分类型 0-不拆封 1-供应商 默认0
     * 由于上面的splitType是专门针对指定商品的类型，故新加字段
     */
    private Integer autoSplitType;

    /**
     * 商品品牌Name
     */
    private List<BrandVO>  brandList;

    /**
     * 供销商Name
     */
    private List<SupplierVO>  supplierList;


    private String  fxTaobaoIds;

    private Integer matchItemAction;

    private String tagIds;

    private String tagOperator;

    private Integer itemMatchType;

    private Integer supplierIncludeStragery;

    private Integer ignoreGift;

    private String secondUserStr;

    /**
     * 标记的标签
     */
    private String markTagIds;
    /**
     * 标记的异常
     */
    private String markExceptIds;

    /**
     * 自动化匹配规则（老的规则转新的规则使用）
     */
    private TradeRule tradeRule;

    public static JSONObject getTradeType(String tradeType) {
        JSONObject jo = new JSONObject();
        if (tradeType == null || tradeType.equals("")) {
            return jo;
        }
        String[] split = tradeType.split(",");
        for (String s : split) {
            for (Map.Entry<String, String> entry : WarehouseAllocate.tradeTypeMapping.entrySet()) {
                if (entry.getValue().equals(s)) {
                    jo.put(entry.getKey(),"1");
                }
            }
        }
        return jo;
    }
}

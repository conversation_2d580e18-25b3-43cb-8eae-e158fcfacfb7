package com.raycloud.dmj.domain.trades.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: chenchaochao
 * @Date: 2020/9/17 12:18 下午
 */
public class StringCommonUtils {

    /**
     * 表情正则
     */
    private static final String EMOJI_REGX = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]|[\ue000-\uf8ff]";

    public static String subString(String str, String newStr) {
        int count = 0;
        if (str == null || newStr == null) return str;
        int i = 0;
        while (str.indexOf(newStr, i) >= 0) {
            count++;
            i = str.indexOf(newStr, i) + newStr.length();
            if (count == 2) {
                return str.substring(0, i - 1);
            }
        }
        return str;
    }

    /**
     * 根据某个符号首尾第一次拿到结果
     * @param str
     * @param subStr
     * @return
     */
    public static String splitString(String str, String subStr) {
        if(StringUtils.isBlank(str) || StringUtils.isBlank(subStr)){
            return StringUtils.EMPTY;
        }
        int start = str.indexOf(subStr);
        if (start == -1) {
            return StringUtils.EMPTY;
        }
        int end = str.indexOf(subStr, start + 1);
        if (end == -1){
            return StringUtils.EMPTY;
        }
        return str.substring(start + 1, end);
    }

    /**
     * 根据某个符号截取返回数组
     * @param str
     * @param subStr
     * @return
     */
    public static List<String> splitStringReturnList(String str, String subStr) {
        List<String> strings = Lists.newArrayList();
        if (StringUtils.isBlank(str) || StringUtils.isBlank(subStr)) {
            return strings;
        }
        str = str.replaceAll("＃", "#");
        while (str.indexOf(subStr) != -1) {
            int start = str.indexOf(subStr);
            if (start == -1) {
                return strings;
            }
            int end = str.indexOf(subStr, start + 1);
            if (end == -1){
                return strings;
            }
            strings.add(str.substring(start + 1, end));
            str = str.substring(end+1);
        }
        return strings;
    }


    public static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(EMOJI_REGX, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                return emojiMatcher.replaceAll("");
            }
        }
        return source;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: ruanyaguang
 * @Date : 2018/10/22
 * @Info :
 */
public class PageUtils {
    public static Page createPage(Integer pageNo, Integer pageSize, Integer defaultPageNo, Integer defaultPageSize) {
        if (null == pageNo) {
            pageNo = null != defaultPageNo ? defaultPageNo : Page.DEFAULT_PAGE_NUM;
        }
        if (null == pageSize) {
            pageSize = null != defaultPageSize ? defaultPageSize : Page.DEFAULT_PAGE_SIZE;
        }
        return new Page(pageNo, pageSize);
    }

    public static Page createPage(Integer pageNo, Integer pageSize) {
        return createPage(pageNo, pageSize, 1, 10);
    }

    public static <T> List<T> subList(List<T> list, Page page) {
        int startRow = (page.getPageNo() - 1) * page.getPageSize();
        int endRow = page.getPageNo() * page.getPageSize();

        List<T> subList = null;
        if (list.size() > startRow) {
            subList = list.subList(startRow, Math.min(endRow, list.size()));
        } else {
            subList = new ArrayList<>(0);
        }
        return subList;
    }
}
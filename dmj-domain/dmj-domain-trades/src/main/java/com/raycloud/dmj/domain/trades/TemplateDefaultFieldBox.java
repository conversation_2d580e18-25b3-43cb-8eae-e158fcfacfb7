package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * Date    2019-07-25
 */
public class TemplateDefaultFieldBox implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * company_id
     */
    private Long companyId;
    /**
     * 文本框内容包括设计内容
     */
    private String fieldBoxValue;
    /**
     * 后文字 冗余的供查询用
     */
    private String fieldBoxAfter;
    /**
     * 前文字 和后文字一样
     */
    private String fieldBoxBefore;
    /**
     * 所述模板分类 0 商家编码 1 吊牌 2 快递单 需求来了再扩展
     */
    private Integer fieldBoxType;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除
     */
    private Integer enableStatus;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return companyId company_id
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * @param companyId company_id
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * @return fieldBoxValue 文本框内容包括设计内容
     */
    public String getFieldBoxValue() {
        return fieldBoxValue;
    }

    /**
     * @param fieldBoxValue 文本框内容包括设计内容
     */
    public void setFieldBoxValue(String fieldBoxValue) {
        this.fieldBoxValue = fieldBoxValue;
    }

    /**
     * @return fieldBoxAfter 后文字 冗余的供查询用
     */
    public String getFieldBoxAfter() {
        return fieldBoxAfter;
    }

    /**
     * @param fieldBoxAfter 后文字 冗余的供查询用
     */
    public void setFieldBoxAfter(String fieldBoxAfter) {
        this.fieldBoxAfter = fieldBoxAfter;
    }

    /**
     * @return fieldBoxBefore 前文字 和后文字一样
     */
    public String getFieldBoxBefore() {
        return fieldBoxBefore;
    }

    /**
     * @param fieldBoxBefore 前文字 和后文字一样
     */
    public void setFieldBoxBefore(String fieldBoxBefore) {
        this.fieldBoxBefore = fieldBoxBefore;
    }

    /**
     * @return fieldBoxType 所述模板分类 0 商家编码 1 吊牌 2 快递单 需求来了再扩展
     */
    public Integer getFieldBoxType() {
        return fieldBoxType;
    }

    /**
     * @param fieldBoxType 所述模板分类 0 商家编码 1 吊牌 2 快递单 需求来了再扩展
     */
    public void setFieldBoxType(Integer fieldBoxType) {
        this.fieldBoxType = fieldBoxType;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

}
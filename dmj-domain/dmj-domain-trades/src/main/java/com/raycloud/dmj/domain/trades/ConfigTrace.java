package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.util.Date;

/**
 * @Description 配置操作记录
 * @Date 2021/8/4 3:25 下午
 * @Created 杨恒
 */
@Table(name = "config_trace")
@Data
public class ConfigTrace extends Model {

    private static final long serialVersionUID = -392389839637433361L;

    private Long id;

    private Long companyId;

    /**
     * 配置id
     */
    private Long configId;

    /**
     * 配置类型
     */
    private Integer configType;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作员
     */
    private String staffName;

    private Date created;

    private Integer enableStatus;

    private String clueId;

}

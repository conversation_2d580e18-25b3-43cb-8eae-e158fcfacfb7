package com.raycloud.dmj.domain.trades.search;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Data;

/**
 * @Description <pre>
 *    使用游标方式查询数据  该模式下自动指定时间范围条件对应字段进行排序(默认为最后更新时间倒序),并返回对应游标,
 *    调用方查询下一页数据时需将对应游标再次传入
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-05
 */
@Data
public class TradeCursorQueryRequest extends TradeQueryRequest{

    public TradeCursorQueryRequest(Long companyId, Long staffId) {
        super(companyId, staffId);
        setTimeType(TimeTypeEnum.UPD_TIME);
    }

    public TradeCursorQueryRequest(Staff staff) {
        super(staff);
        setTimeType(TimeTypeEnum.UPD_TIME);
    }

    public TradeCursorQueryRequest() {
        setTimeType(TimeTypeEnum.UPD_TIME);
    }

    private String cursor;

    private int pageSize = 200;

    /**
     * 系统将自动按 TimeTypeEnum 指定字段及sid 进行排序
     * 是否是降序排序，默认使用降序排序
     */
    private String order = "desc";

    @Override
    public void setSort(Sort sort) {
        if (sort != null) {
            throw new IllegalArgumentException("不支持自定义排序");
        }
        super.setSort(sort);
    }

    @Override
    public void setPage(Page page) {
        if (page != null) {
            throw new IllegalArgumentException("不支持分页查询");
        }
        super.setPage(page);
    }
}

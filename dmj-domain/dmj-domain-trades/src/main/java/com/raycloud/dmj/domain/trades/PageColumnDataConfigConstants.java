package com.raycloud.dmj.domain.trades;

import org.apache.commons.collections.BidiMap;
import org.apache.commons.collections.bidimap.DualHashBidiMap;

public class PageColumnDataConfigConstants {
    //包装验货订单信息配置列
    public static final long PACK_ORDER_COLUMN_PAGE_ID = 34;

    //后置打印订单信息配置列
    public static final long POST_ORDER_COLUMN_PAGE_ID = 35;

    //包装验货订单信息配置列 (老data表)
    public static final long PACK_ORDER_DATA_PAGE_ID = 38;

    //后置打印订单信息配置列 (老data表)
    public static final long POST_ORDER_DATA_PAGE_ID = 39;

    //波次列表，波次打印 订单列表页面
    public static final long WAVE_ORDER_PAGE_ID = 82;

    public static final int DEFAULT_WIDTH = 150;

    public static final BidiMap pageIdMap = new DualHashBidiMap();

    public static final BidiMap columnIdMap = new DualHashBidiMap();


    static {
        pageIdMap.put(PACK_ORDER_COLUMN_PAGE_ID, PACK_ORDER_DATA_PAGE_ID);
        pageIdMap.put(POST_ORDER_COLUMN_PAGE_ID, POST_ORDER_DATA_PAGE_ID);

        columnIdMap.put(51227L, 1L);
        columnIdMap.put(51228L, 26L);
        columnIdMap.put(51229L, 2L);
        columnIdMap.put(51230L, 11L);
        columnIdMap.put(51231L, 3L);
        columnIdMap.put(51232L, 4L);
        columnIdMap.put(51233L, 5L);
        columnIdMap.put(51234L, 6L);
        columnIdMap.put(51235L, 7L);
        columnIdMap.put(51236L, 8L);
        columnIdMap.put(51237L, 9L);
        columnIdMap.put(51238L, 24L);
        columnIdMap.put(51239L, 10L);
        columnIdMap.put(51240L, 36L);
        columnIdMap.put(51241L, 37L);

        columnIdMap.put(51254L, 12L);
        columnIdMap.put(51243L, 13L);
        columnIdMap.put(51244L, 14L);
        columnIdMap.put(51255L, 15L);
        columnIdMap.put(51256L, 16L);
        columnIdMap.put(51245L, 27L);
        columnIdMap.put(51253L, 17L);
        columnIdMap.put(51246L, 18L);
        columnIdMap.put(51249L, 19L);
        columnIdMap.put(51242L, 20L);
        columnIdMap.put(51248L, 25L);
        columnIdMap.put(51252L, 21L);
        columnIdMap.put(51250L, 22L);
        columnIdMap.put(51247L, 23L);
        columnIdMap.put(51251L, 38L);
    }
}

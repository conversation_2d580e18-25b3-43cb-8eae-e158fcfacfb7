package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.area.Area;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.orderitemtag.OrderItemTag;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trade.rule.AddressUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.bo.SecondUserBO;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.vo.GovSubsidyInfo;
import com.raycloud.dmj.domain.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.*;

import static com.raycloud.dmj.domain.constant.SystemTags.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-05-16 10:59
 */
public class TradeMatchUtils {

    public static boolean isOnlyMatchCod(Trade trade, Integer isOnlyMatchCod) {
        if (isOnlyMatchCod == null || isOnlyMatchCod == 0) {
            //匹配全部类型订单
            return true;
        }
        //只匹配货到付款
        return "cod".equalsIgnoreCase(trade.getType()) || "jd-1".equals(trade.getType());
    }

    //地址关键字匹配
    public static boolean isAddressWordMatched(Trade trade, Integer isContains, String addressWord) {
        if (StringUtils.isEmpty(addressWord) || StringUtils.isEmpty(addressWord.replace(",", ""))) {
            return true;
        }
        String tradeAddress = nvl(trade.getReceiverCountry()) +
                nvl(trade.getReceiverState()) +
                nvl(trade.getReceiverCity()) +
                nvl(trade.getReceiverDistrict()) +
                nvl(trade.getReceiverAddress());
        boolean contains = isContains != null && isContains == 1;
        StringTokenizer tokenizer = new StringTokenizer(addressWord, ",");
        boolean containsAddress = false;
        while (tokenizer.hasMoreTokens()) {
            String address = tokenizer.nextToken();
            if (!containsAddress) {
                containsAddress = tradeAddress.contains(address);
            }
        }
        return contains == containsAddress;
    }

    public static boolean isMessageMemoMatched(Trade trade, String sellerMemo, String buyerMessage) {
        if ((sellerMemo == null || (sellerMemo = sellerMemo.trim()).isEmpty()) && (buyerMessage == null || (buyerMessage = buyerMessage.trim()).isEmpty())) {
            return true;//没有设置留言备注，不作留言备注校验
        }
        if (sellerMemo != null && !sellerMemo.isEmpty()) {
            if (trade.getSellerMemo() != null && trade.getSellerMemo().contains(sellerMemo)) {
                return true;
            }
        }
        if (buyerMessage != null && !buyerMessage.isEmpty()) {
            if (trade.getBuyerMessage() != null && trade.getBuyerMessage().contains(buyerMessage)) {
                return true;
            }
        }
        return false;
    }

    private static String nvl(String str) {
        return org.apache.commons.lang3.StringUtils.isEmpty(str) ? "" : str;
    }

    public static boolean isAreaMatched(Trade trade, List<Area> areas) {
        return AddressUtils.match(trade, areas);
    }

    public static boolean isUserMatched(Long userId, String userIds) {
        if (userIds == null || (userIds = userIds.trim()).isEmpty()) {//没有添加店铺时，不作店铺校验
            return true;
        }
        return ("," + userIds).contains("," + userId);
    }

    public static boolean isExpressMatched(Staff staff, Trade trade, String configExpress) {
        if (StringUtils.isBlank(configExpress)) {
            return true;
        }
        Set<String> configExpressSet = ArrayUtils.toStringSet(configExpress);
        for (String express : configExpressSet) {
            if (StringUtils.contains(express, "_")) {//模板匹配
                if (StringUtils.equals(express, trade.getTemplateType() + "_" + trade.getTemplateId())) {
                    return true;
                }
            } else if (StringUtils.equals(express, trade.getLogisticsCompanyId() + "")) {//快递公司匹配
                return true;
            }
        }
        return false;
    }

    /**
     * doc：http://doc.raycloud.com/pages/viewpage.action?pageId=30541094
     */
    public static boolean isItemMatched(Trade trade, WarehouseAllocate rule) {
        List<SysItemSku> itemSkuList = rule.getItemSkuList();

        //0 包含,1仅包含,2有且仅有(只有有且仅有才支持指定商品数量)
        //Integer action = rule.getMatchItemAction();
        List<Order> orders = filterOrders(TradeUtils.getOrders4Trade(trade));
        if (orders.size() == 0) {
            return false;
        }

        /**
         * 分仓策略商品匹配类型，0-商品本身（默认0） 1-套件下单品
         * https://gykj.yuque.com/entavv/xb9xi5/hcutqz
         */
        if(!Objects.isNull(rule.getItemMatchType()) && rule.getItemMatchType().equals(1)){
            //分配仓库新增复选框“套件按照子商品来匹配” 勾选了该配置，若订单中包含套件商品，匹配仓库时先按照套件下的单品来进行仓库匹配的判断
            List<Order> suitOrders = orders.stream().filter(Order::isSuit).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(suitOrders)){
                List<Order> allSingleOrders = Lists.newArrayList();
                for(Order suitOrder : suitOrders){
                    allSingleOrders.addAll(suitOrder.getSuits());
                }
                //先根据套件下单品做匹配，单品匹配不上时，再根据套件本身做匹配。 任何异常的场景（比如suitOrders为空等）都用默认的匹配方式来匹配
                if(CollectionUtils.isNotEmpty(allSingleOrders)){
                    List<Order> commonOrders = orders.stream().filter(o -> !o.isSuit()).collect(Collectors.toList());
                    //除了单品本身外，还需要把订单的非套件order加入到匹配列表
                    if(CollectionUtils.isNotEmpty(commonOrders)){
                        allSingleOrders.addAll(commonOrders);
                    }
                    boolean isSingleMatched = doItemMatch(allSingleOrders,itemSkuList,rule);
                    if (rule.getMatchItemAction().equals(4)){
                        return isSingleMatched;
                    }
                    if(isSingleMatched){   //当单品匹配上时，返回匹配成功。
                        return true;
                    }else {     //单品未匹配上，再继续以套件本身做匹配。
                        return doItemMatch(orders,itemSkuList,rule);
                    }
                } else {
                    return doItemMatch(orders,itemSkuList,rule);
                }
            } else {
                return doItemMatch(orders,itemSkuList,rule);
            }
        } else {
            return doItemMatch(orders,itemSkuList,rule);
        }
    }

    private static boolean doItemMatch(List<Order> orders,List<SysItemSku> itemSkuList,WarehouseAllocate rule){
        //0 包含,1仅包含,2有且仅有(只有有且仅有才支持指定商品数量)
        Integer action = rule.getMatchItemAction();
        if (action == 0) {//包含
            return isMatchWarehouseByItem(orders,rule, itemSkuList);
        } else if (action == 1) {//仅包含
            return onlyMatchItem(orders, rule, itemSkuList);
        } else if (action == 2) {//有且仅有
            Integer isMatchItemNum = rule.getIsMatchItemNum();
            if (isMatchItemNum == null) {
                isMatchItemNum = 0;
            }
            return isMatchByItemNum(orders,rule, itemSkuList, isMatchItemNum);
        } else if (action == 3) {//指定商品拆单
            return isMatchByAssignItem(rule,orders, itemSkuList);
        } else if (action == 4) {// 不包含
            return isMatchNone(orders,rule,itemSkuList);
        }
        return false;
    }


    /**
     * 过滤待审核或者待付款的子订单
     */
    private static List<Order> filterOrders(List<Order> orders) {
        List<Order> filter = new ArrayList<>();
        for (Order order : orders) {
            if (TradeStatusUtils.isWaitAudit(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) {
                filter.add(order);
            }
        }
        return filter;
    }

    /**
     * 仓库匹配商品规则仅包含(订单商品必须为配置商品组合的子集，举例说明：若商品配置为A+B，则订单只有包含商品A，或只包含商品B，或只包含商品A+B，才能匹配该条例外设置，如果订单商品包含A+B+C，则匹配不上)
     * itemSkuList=根据订单中的商品在规则表查询出来的结果集
     */
    private static boolean onlyMatchItem(List<Order> orders, WarehouseAllocate rule, List<SysItemSku> itemSkuList) {
        if (!rule.isHasPresellItem()) {
            //没有设置商品
            return true;
        }
        //如果根据订单中的商品没有查到商品
        if (CollectionUtils.isEmpty(itemSkuList)) {
            return false;
        }
        List<String> orderItems = new ArrayList<>(), ruleItems = new ArrayList<>();
        for (Order order : orders) {
            //商品未匹配或非待审核非已审核非待付款，交易关闭的直接跳过
            if (!(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            orderItems.add(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()));
        }

        for (SysItemSku itemSku : itemSkuList) {
            ruleItems.add(TradeItemUtils.getItemKey(itemSku.getSysItemId(), itemSku.getSysSkuId()));
        }

        //如果不相当，说明根据订单中的商品不完全在规则表中

        for (String orderItem : orderItems) {
            if (!ruleItems.contains(orderItem)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 根据商品进行匹配，有一个商品满足就匹配成功
     */
    public static boolean isMatchWarehouseByItem(List<Order> orders,WarehouseAllocate rule, List<SysItemSku> itemSkuList) {
        if (!rule.isHasPresellItem()) {
            return true;
        }
        //配置了商品，但是根据order 没有在配置中查询到商品
        if (CollectionUtils.isEmpty(itemSkuList)) {
            return false;
        }
        return isMatchByItemBase(orders,itemSkuList);
    }

    /**
     * 根据商品进行匹配，有一个商品满足就匹配成功
     */
    public static boolean isMatchByItem(List<Order> orders, List<SysItemSku> itemSkuList) {
        //快递匹配使用原先的逻辑
        if (CollectionUtils.isEmpty(itemSkuList)) {
            return true;
        }
        return isMatchByItemBase(orders,itemSkuList);
    }

    public static boolean isMatchByItemBase(List<Order> orders, List<SysItemSku> itemSkuList){
        for (Order order : orders) {
            //商品未匹配或非待审核非已审核非待付款，交易关闭的直接跳过
            if (!(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            if (isItemMatched(order, itemSkuList)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据商品进行匹配，不包含商品就匹配成功
     */
    public static boolean isMatchNone(List<Order> orders, WarehouseAllocate rule, List<SysItemSku> itemSkuList) {
        //没有设置商品或者根据订单中的商品在规则中没有查询到
        if (!rule.isHasPresellItem() || CollectionUtils.isEmpty(itemSkuList)) {
            return true;
        }
        for (Order order : orders) {
            //商品未匹配或非待审核非已审核非待付款，交易关闭的直接跳过
            if (!(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            if (isItemMatched(order, itemSkuList)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 根据商品种类，商品数量进行匹配，都成功才成功
     * 开启指定数量，匹配逻辑为订单商品+数量必须和设置的商品+数量一致，则才可以满足匹配规则，举例说明：订单商品Ax1+Bx1，商品配置为Ax1+Bx1，则可以匹配该条例外设置，否则都无法匹配
     * 未开启指定数量，匹配逻辑为订单上必须和设置的商品一致，才可以匹配规则，举例说明：订单商品A+B，商品配置A+B，则可以匹配该条例外设置，
     */
    private static boolean isMatchByItemNum(List<Order> orders, WarehouseAllocate rule, List<SysItemSku> itemSkuList,Integer isMatchItemNum) {
        if (!rule.isHasPresellItem()) {
            return true;
        }
        if (CollectionUtils.isEmpty(itemSkuList)) {
            return false;
        }
        Map<String, Integer> orderMap = groupOrdersByItemKey(orders);

        Map<String, Integer> sysItemSkuMap = itemSkuList.stream().collect(Collectors.toMap(k -> TradeItemUtils.getItemKey(k.getSysItemId(), k.getSysSkuId()), SysItemSku::getNum));

        Set<String> orderKeys = orderMap.keySet();
        Set<String> sysItemSkuKeys = sysItemSkuMap.keySet();
        if (orderKeys.size() != sysItemSkuKeys.size()) {
            return false;
        }

        for (String orderKey : orderKeys) {
            //校验商品种类是否和规则中的一摸一样（必须一摸一样）
            if (!sysItemSkuKeys.contains(orderKey)) {
                return false;
            }
            //有且仅有 且指定了商品数量,还要校验一下商品数量属否满足
            if (isMatchItemNum == 1) {
                //种类匹配上然后匹配商品数量
                if (orderMap.get(orderKey) - sysItemSkuMap.get(orderKey) != 0) {
                    return false;
                }
            }
        }
        return true;
    }


    public static boolean containKeyWords(String[] keyWords ,String name){
        for (String keyWord : keyWords) {
            if (StringUtils.contains(name, keyWord)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 指定商品拆单 http://doc.raycloud.com/pages/viewpage.action?pageId=30544298
     */
    private static boolean isMatchByAssignItem(WarehouseAllocate rule,List<Order> orders, List<SysItemSku> itemSkuList) {
        Map<String, Integer> orderMap = groupOrdersByItemKey(orders);
        if (rule.getSplitType() == 0) {
            if (CollectionUtils.isEmpty(itemSkuList)) {
                return true;
            }
            for (SysItemSku itemSku : itemSkuList) {
                Integer num = orderMap.get(TradeItemUtils.getItemKey(itemSku.getSysItemId(), itemSku.getSysSkuId()));
                if (num == null || num < itemSku.getNum()) {
                    return false;
                }
            }
            return true;
        } else  {
            //平台主商品名称包含设置的关键字
            if (StringUtils.isBlank(rule.getSplitKeys())) {
                return true;
            }
            String[] keyWords = StringUtils.split(rule.getSplitKeys(), ",");

            for (Order order : orders) {
                //商品未匹配或非待审核非已审核非待付款，交易关闭的直接跳过
                if (!(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                    continue;
                }
                //String key = rule.getSplitType() == 1 ? order.getTitle() : order.getSkuPropertiesName();
                String key = getOrderCalculateSplitString(rule.getSplitType(),order);
                if (containKeyWords(keyWords,key)) {
                    return true;
                }

            }
            return false;
        }
    }

    private static String getOrderCalculateSplitString(Integer splitType, Order order) {
        switch (splitType) {
            case 1 :
                return order.getTitle();
            case 2:
                return order.getSkuPropertiesName();
            case 3:
                return StringUtils.isNotEmpty(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterId();
            case 4:
                return order.getSysOuterId();
            default:
                return "";
        }
    }

    private static Map<String, Integer> groupOrdersByItemKey(List<Order> orders) {
        Map<String, Integer> orderMap = new HashMap<>();
        for (Order order : orders) {
            //商品未匹配或非待审核非已审核非待付款，交易关闭的直接跳过
            if (!(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            String key = TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId());
            orderMap.put(key, orderMap.containsKey(key) ? orderMap.get(key) + order.getNum() : order.getNum());
        }
        return orderMap;
    }

    public static boolean isItemMatched(Order order, List<SysItemSku> itemSkuList) {
        Long sysItemId = order.getItemSysId();
        Long sysSkuId = order.getSkuSysId();
        Integer num = order.getNum();
        if (itemSkuList == null) {
            return true;
        }
        sysSkuId = sysSkuId == null || sysSkuId < 0 ? 0 : sysSkuId;
        Set<SysItemSku> itemSkuSet = new HashSet<>(itemSkuList);
        SysItemSku targetItemSku = new SysItemSku(sysItemId, sysSkuId, num);
        if (sysSkuId == 0L) {
            //来源的order中可能会有-1的情况，兼容sysSkuId为-1
            SysItemSku itemSku = new SysItemSku(sysItemId, -1L, num);
            return itemSkuSet.contains(targetItemSku) || itemSkuList.contains(itemSku);
        }
        return itemSkuSet.contains(targetItemSku);
    }

    /**
     * 判断订单商品数量与商品种类数量是否在规则的范围内
     */
    public static boolean isNumMatched(Trade trade, WarehouseAllocate rule) {
        return isIntValueMatched(trade.getItemNum(), rule.getMinItemNum(), rule.getMaxItemNum()) && isIntValueMatched(trade.getItemKindNum(), rule.getMinItemKindNum(), rule.getMaxItemKindNum());
    }

    public static boolean isIntValueMatched(Integer v, Integer min, Integer max) {
        if (v == null) {
            return true;
        }
        boolean matched = true;
        if (min != null && min > 0) {
            matched = v >= min;
        }
        if (matched && max != null && max > 0) {
            matched = v <= max;
        }
        return matched;
    }

    public static boolean isDoubleValueMatched(Double v, Double min, Double max) {
        if (v == null) {
            return true;
        }
        boolean matched = true;
        if (min != null && min > 0) {
            matched = v >= min;
        }
        if (matched && max != null && max > 0) {
            matched = v <= max;
        }
        return matched;
    }

    /**
     * 判断订单类型是否与规则设置的一致
     */
    public static boolean isTypeMatched(Trade trade, WarehouseAllocate rule) {
        boolean matched = true;
        if (StringUtils.isNotBlank(rule.getTradeType())) {
            matched = false;
            if ("overseas_warehouse".equals(trade.getType()) || ("jd_warehouse".equals(trade.getSubSource()) || "jd_warehouse".equals(trade.getType()))||
                    CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
                return (StringUtils.isNotBlank(trade.getType()) && rule.getTradeType().contains(trade.getType())) || rule.getTradeType().contains(trade.getSubSource()) || rule.getTradeType().equals(trade.getSubSource());
            }
        }
        return matched;
    }


    /**
     * 校验品牌供应商
     * http://doc.raycloud.com/pages/viewpage.action?pageId=30541843
     * @param trade
     * @param rule
     * @return
     */
    public static boolean isMatchBrand(Trade trade, WarehouseAllocate rule) {
        if (StringUtils.isBlank(rule.getBrandIds())) {
            return true;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            List<Long> brands = order.getBrandIds();
            if (CollectionUtils.isEmpty(brands)) {
                return false;
            }
            Long brand = brands.get(0);
            if (!isMatchedIds(brand,rule.getBrandIds())) {
                return false;
            }
        }
        return true;

    }

    public static boolean isMatchSupplier(Trade trade,WarehouseAllocate rule) {
        // 默认是0 ，包含
        Integer supplierIncludeStragery = Optional.ofNullable(rule.getSupplierIncludeStragery()).orElse(0);
        if(!Objects.equals(supplierIncludeStragery,0)){
            /**
             *   产品规则更改
             *   走仅包含供应商逻辑
             *   https://gykj.yuque.com/entavv/xb9xi5/lq964c
             */
            return  isOnlyIncludeSupplier(trade, rule);
        }
        // 默认策略，supplierIncludeStragery 为0 走原来的包含供应商逻辑
        return defaultIsMatchSupplier(trade, rule);

    }

    /**
     * 校验店铺，含二级店铺*
     * @param trade
     * @param secondUserStr
     * @param userIds
     * @return
     */
    public static boolean isMatchUser(Trade trade, String secondUserStr, String userIds) {
        // 两个都为空就是没有设置店铺规则，不校验，直接返回true
        if ((StringUtils.isEmpty(secondUserStr) || secondUserStr.equals("{}")) && StringUtils.isEmpty(userIds)) {
            return true;
        }
        Long userId = trade.getUserId();
        // 二级店铺校验
        if (StringUtils.isNotEmpty(secondUserStr) && Objects.nonNull(userId)) {
            JSONObject jsonObject = JSONObject.parseObject(secondUserStr);
            String userIdStr = String.valueOf(userId);
            String subSource = trade.getSubSource();
            String sellerNick = trade.getSellerNick();
            if (jsonObject.containsKey(userIdStr)) {
                List<SecondUserBO> secondUserBOS = jsonObject.getJSONArray(userIdStr).toJavaList(SecondUserBO.class);
                for (SecondUserBO secondUserBO : secondUserBOS) {
                    if (secondUserBO.getPlatformCode().equals(subSource) && secondUserBO.getShopName().equals(sellerNick)) {
                        return true;
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(userIds)) {
            return isMatchedIds(userId, userIds);
        } else {
            return false;
        }
    }

    /**
     * 校验 分销商/供销商/店铺id是否满足
     * @param id
     * @param ids
     * @return
     */
    public static boolean isMatchedIds(Long id, String ids) {
        if (ids == null || (ids = ids.trim()).isEmpty()) {
            return true;
        }
        Set<Long> userSet = new HashSet<>();
        String[] idArray = ids.split(",");
        for (String strId : idArray) {
            if (StringUtils.isNotEmpty(strId) && strId.trim().matches("^-?\\d+$")) {
                userSet.add(Long.parseLong(strId.trim()));
            }
        }
        return userSet.contains(id);
    }

    /**
     * 校验 规则指定物流信息
     * @param trade
     * @param pointExpressId 快递规则匹配中指定的erp物流公司Id
     * @param pointExpressCode 快递规则匹配中指定的erp物流公司code
     * @return
     */
    public static boolean isPointExpress(Staff staff, Trade trade, String pointExpressId, String pointExpressCode, Long expressId, Map<String, List<String>> expressInfoMap, Map<Long, TradeExt> tradeExtMap, Map<Long, ExpressCompany> expressCompanyMap) {
        boolean fxgSpecifyLogistics = TradeUtils.isFxgSpecifyLogistics(staff, trade);
        if (fxgSpecifyLogistics) {
            pointExpressId = expressCompanyMap.get(expressId).getCode();
        }
        if (StringUtils.isBlank(pointExpressId) && StringUtils.isBlank(pointExpressCode) ) {
            return true;
        }
        Logs.ifDebug(String.format("userId=%s,sid=%s,pointExpressId=%s,pointExpressCode=%s,tradeExt=%s ", trade.getUserId(), trade.getSid(), pointExpressId, pointExpressCode, tradeExtMap.get(trade.getSid())));
        // 拼多多的指定物流的单，从map 获取tradeExt
        TradeExt tradeExt = tradeExtMap.get(trade.getSid()) != null
                && (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_PDD) || TradeUtils.isPoisonBrandDeliverTrade(trade) || fxgSpecifyLogistics)
                ? tradeExtMap.get(trade.getSid()) : trade.getTradeExt();
        String logisticsCode = tradeExt != null ? tradeExt.getLogisticsCode() : null;
        if (StringUtils.isBlank(logisticsCode)) {
            Logs.ifDebug(String.format("1.userId=%s,sid=%s设置了指定物流 pointExpressId=%s,pointExpressCode=%s,但订单非指定物流单logisticsCode=%s ", trade.getUserId(), trade.getSid(), pointExpressId, pointExpressCode, logisticsCode));
            return false;
        }
        if ("noCode".equalsIgnoreCase(logisticsCode)) {
            Logs.ifDebug(String.format("2.userId=%s,sid=%s设置了指定物流 pointExpressId=%s，pointExpressCode=%s，但订单非指定物流单logisticsCode=%s ", trade.getUserId(), trade.getSid(), pointExpressId, pointExpressCode, logisticsCode));
            return false;
        }
        // 放心购指定物流的先直接匹配一次,放心购指定物流的奇门订单直接存的系统内coCode，不用转换
        if (fxgSpecifyLogistics && tradeExt != null && StringUtils.isNotBlank(tradeExt.getLogisticsCode()) &&
                Arrays.asList(tradeExt.getLogisticsCode().split(",")).contains(pointExpressId)) {
            return true;
        }

        //获取订单内指定物流ID
        List<String> tradePointExpressIds = TradeExpressMathUtils.getTradePointExpressIds(tradeExt, TradeUtils.getSourceByTrade(trade), expressInfoMap, fxgSpecifyLogistics);
        if (StringUtils.isNotBlank(pointExpressId) && CollectionUtils.isNotEmpty(tradePointExpressIds)) {
            List<String> rulePointExpressKey = Arrays.asList(pointExpressId.split(","));
            for (String ruleCode : rulePointExpressKey) {
                if (tradePointExpressIds.contains(ruleCode)) {
                    return true;
                }
            }
            Logs.ifDebug(String.format("userId=%s,sid=%s，设置的指定物流和logisticsCode 不匹配 pointExpressId=%s ，tradePointExpressKey=%s ", trade.getUserId(), trade.getSid(), pointExpressId, tradePointExpressIds));
        }

        List<String> tradePointExpressCodes = TradeExpressMathUtils.getTradePointExpressCodes(tradeExt, TradeUtils.getSourceByTrade(trade), fxgSpecifyLogistics);
        if (StringUtils.isNotBlank(pointExpressCode) && CollectionUtils.isNotEmpty(tradePointExpressCodes)) {
            //规则指定code
            List<String> rulePointExpressKey = Arrays.asList(pointExpressCode.split(","));
            for (String ruleCode : rulePointExpressKey) {
                if (tradePointExpressCodes.contains(ruleCode)) {
                    return true;
                }
            }
            Logs.ifDebug(String.format("userId=%s,sid=%s，设置的指定物流和logisticsCode 不匹配 pointExpressCode=%s ，tradePointExpressKey=%s ", trade.getUserId(), trade.getSid(), pointExpressCode, tradePointExpressIds));
        }

        Logs.ifDebug(String.format("userId=%s,sid=%s，logisticsCode=%s 从expressInfoMap 未找到对应的物流信息 ", trade.getUserId(), trade.getSid(), logisticsCode));
        return StringUtils.isBlank(pointExpressId) && StringUtils.isBlank(pointExpressCode) ;
    }

    /**
     * 校验 规则指定物流信息
     *
     * @param trade
     * @param pointExpress
     * @return
     */
    public static boolean isPointExpress(Trade trade, String pointExpress, Map<String, List<String>> expressInfoMap, Collection<String> supportPlatformNames) {
        //获取订单内指定物流code
        List<String> tradePointExpressKey = TradeUtils.getTradePointExpress(trade, expressInfoMap, supportPlatformNames);
        if (StringUtils.isNotBlank(pointExpress) && tradePointExpressKey.size() > 0) {
            //规则指定code
            String[] rulePointExpressKey = pointExpress.split(",");
            for (String ruleCode : rulePointExpressKey) {
                if (tradePointExpressKey.contains(ruleCode)) {
                    return true;
                }
            }
        } else {
            return true;
        }
        return false;
    }

    /**
     * 校验标签id是否满足
     *
     * @param tagIds            订单标签id
     * @param warehouseAllocate 规则中勾选的标签id
     * @return
     */
    public static boolean isMatchedTagIds(String tagIds, WarehouseAllocate warehouseAllocate) {
        //如果规则中没有勾选标签，不对订单标签作匹配
        String tagOperator = warehouseAllocate.getTagOperator();
        if (StringUtils.isEmpty(tagOperator) && StringUtils.isEmpty(warehouseAllocate.getTagIds())) {
            return true;
        }
        // 原来的只有包含
        if (StringUtils.isEmpty(tagOperator)) {
            if (StringUtils.isBlank(tagIds)) {
                return false;
            }
            Set<String> tagIdSet = Stream.of(tagIds.split(",")).collect(Collectors.toSet());
            Set<String> idList = ArrayUtils.toStringSet(warehouseAllocate.getTagIds());
            for (String id : idList) {
                if (tagIdSet.contains(id)) {
                    return true;
                }
            }
            return false;
        }
        //无标签
        if (SpelCondition.OPERATOR_NOT.equals(tagOperator)) {
            return StringUtils.isEmpty(tagIds);
        }
        List<String> tagIdList = ArrayUtils.toStringList(tagIds);
        //指定标签
        if (SpelCondition.OPERATOR_ASSIGN.equals(tagOperator)) {
            return assign(tagIdList, warehouseAllocate.getTagIds());
        }
        //指定排除标签
        if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(tagOperator)) {
            return exclude(tagIdList, warehouseAllocate.getTagIds());
        }
        //组合
        if (SpelCondition.OPERATOR_ASSEMBLE.equals(tagOperator)) {
            String value = warehouseAllocate.getTagIds();
            if (StringUtils.isBlank(value) || !value.contains("@")) {
                return false;
            }
            String[] split = value.split("@");
            return assign(tagIdList, split[0]) && exclude(tagIdList, split[1]);
        }
        return false;
    }


    /**
     * 指定
     *
     * @return
     */
    public static boolean assign(List<String> tagIds, String ruleTagIds) {
        if (StringUtils.isEmpty(ruleTagIds)) {
            return Boolean.TRUE;
        }
        if (CollectionUtils.isEmpty(tagIds)) {
            return Boolean.FALSE;
        }
//        String[] tagTds = ArrayUtils.toStringArray(ruleTagIds);
        Set<String> idList = ArrayUtils.toStringSet(ruleTagIds);

        for (String tagId : idList) {
            if (tagIds.contains(tagId)) {
                return Boolean.TRUE;
            }
        }
        return false;
    }

    /**
     * 排除
     *
     * @return
     */
    public static boolean exclude(List<String> tagIds, String ruleTagIds) {
        if (StringUtils.isEmpty(ruleTagIds) || CollectionUtils.isEmpty(tagIds)) {
            return Boolean.TRUE;
        }
//        String[] tagTds = ArrayUtils.toStringArray(ruleTagIds);
        Set<String> idList = ArrayUtils.toStringSet(ruleTagIds);

        for (String tagId : idList) {
            if (tagIds.contains(tagId)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }





    public static  Boolean defaultIsMatchSupplier(Trade trade,WarehouseAllocate rule){
        /**
         * https://gykj.yuque.com/entavv/xb9xi5/gkgisy
         * 产品更改规则，无论是否勾选按供应商拆分，只要订单中有一个商品的供应商在配置的供应商里面，就算匹配。
         */
        if (StringUtils.isBlank(rule.getSupplierIds())) {
            return true;
        }
        //规则"无供应商"校验
        if ("-1".equals(rule.getSupplierIds())) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                List<Long> supplierIds = order.getSupplierIds();
                if (CollectionUtils.isNotEmpty(supplierIds)) {
                    Logs.ifDebug(String.format("规则[ruleId=%s]无供应商校验不通过，订单sid=%s,orderId=%s商品包含供应商%s",rule.getId(),trade.getSid(),order.getId(),supplierIds));
                    return false;
                }
            }
            return true;
        }

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            List<Long> supplierIds = order.getSupplierIds();
            if (CollectionUtils.isNotEmpty(supplierIds)) {
                Long supplier = supplierIds.get(0);
                if (isMatchedIds(supplier,rule.getSupplierIds())) {
                    return true;
                }
            }else if(rule.getSupplierIds().contains("-1")){
                return true;
            }
        }
        return false;
    }


    public static  Boolean isOnlyIncludeSupplier(Trade trade,WarehouseAllocate rule){
        String supplierId = rule.getSupplierIds();
        if (StringUtils.isBlank(supplierId)) {
            return true;
        }
        //规则"无供应商"校验
        if ("-1".equals(supplierId)) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                List<Long> supplierIds = order.getSupplierIds();
                if (CollectionUtils.isNotEmpty(supplierIds)) {
                    Logs.ifDebug(String.format("仅包含供应商，规则[ruleId=%s]无供应商校验不通过，订单sid=%s,orderId=%s商品包含供应商%s",rule.getId(),trade.getSid(),order.getId(),supplierIds));
                    return false;
                }
            }
            return true;
        }


        String[] split = supplierId.split(",");
        Set<String> supplierIdList = new HashSet<>();
        for(int i=0;i<split.length;i++){
            String trim = split[i].trim();
            if(StringUtils.isNotBlank(trim)){
                supplierIdList.add(trim);
            }
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            List<Long> supplierIds = order.getSupplierIds();
            if (CollectionUtils.isNotEmpty(supplierIds)) {
                Long supplier = supplierIds.get(0);
                // 仅包含，存在配置以外的供应商，不符合规则
                if(supplier==null||!supplierIdList.contains(supplier.toString())){
                    Logs.ifDebug(String.format("%s 供应商不符合仅包含供应商策略，规则[ruleId=%s]无供应商校验不通过，订单sid=%s,orderId=%s商品仅包含供应商%s",supplier,rule.getId(),trade.getSid(),order.getId(),supplierIds));
                    return false;
                }
            }else{
                // 商品不存在供应商
                Logs.ifDebug(String.format("商品不存在供应商,供应商不符合仅包含供应商策略，规则[ruleId=%s]无供应商校验不通过，订单sid=%s,orderId=%s",rule.getId(),trade.getSid(),order.getId()));
                return false;
            }
        }

        return true;

    }
    /**
     * 商品未匹配或非待审核非已审核非待付款，交易关闭状态检查
     * @param order
     * @return
     */
    public static boolean check(Order order) {
        return !(TradeStatusUtils.isWaitAudit(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus()) || TradeStatusUtils.isWaitPay(order.getSysStatus())) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus());
    }



    public static boolean isMatchedItemTagIds(Staff Staff, Trade trade, WarehouseAllocate rule) {
        List<SpelCondition> conditions = rule.getConditions();
        if (conditions == null) {
            conditions = new ArrayList<>();
        }
        // 先判断无标签
        List<SpelCondition> collect = conditions.stream().filter(spelCondition -> SpelCondition.FIELD_ITEM_TAG_IDS.equals(spelCondition.getField())).filter(spelCondition -> Objects.equals(SpelCondition.OPERATOR_NOT, spelCondition.getOperator())).collect(Collectors.toList());
        List<Long> itemTagIds = getItemTagIds(trade, rule);
        if (CollectionUtils.isNotEmpty(collect)) {
            // 无标签
            return CollectionUtils.isEmpty(itemTagIds);
        }
        collect = conditions.stream().filter(spelCondition -> SpelCondition.FIELD_ITEM_TAG_IDS.equals(spelCondition.getField())).filter(spelCondition->Objects.equals(SpelCondition.OPERATOR_ASSEMBLE,spelCondition.getOperator())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return true;
        }

        for (SpelCondition spelCondition : collect) {
            String value = spelCondition.getValue();
            if (StringUtils.isBlank(value)) {
                Logs.debug(String.format("%s %s %s包含且排除规则不能为空 %s", trade.getSid(),rule.getId(),rule.getRuleName(),value));
                return false;
            }
            String[] conditionItemTagIdArr = value.split("@");
            if (conditionItemTagIdArr.length > 2) {
                Logs.debug(String.format("%s %s %s 包含且排除规则不对 %s", trade.getSid(),rule.getId(),rule.getRuleName(),value));
                continue;
            }
            List<Long> containsItemTagIds = StringUtils.isNotBlank(conditionItemTagIdArr[0]) ? Arrays.stream(conditionItemTagIdArr[0].split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            List<Long> exludeItemTagIds = StringUtils.isNotBlank(conditionItemTagIdArr[1]) ? Arrays.stream(conditionItemTagIdArr[1].split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            boolean matched = assign(itemTagIds, containsItemTagIds) && exclude(itemTagIds, exludeItemTagIds);
            if (!matched) {
                return false;
            }
        }
        return true;
    }


    public static List<Long> getItemTagIds(Trade trade,WarehouseAllocate rule){

        boolean calculateSuit = !Objects.isNull(rule.getItemMatchType()) && rule.getItemMatchType().equals(1);
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        List<Long> itemTags = new ArrayList<>();
        for (Order order : orders4Trade) {
            Logs.debug(String.format("%s 匹配规则%s %s orderId=%s calculateSuit=%s suits=%s",trade.getSid(),rule.getRuleName(),rule.getId(),rule.getRuleName(),order.getId(),CollectionUtils.isNotEmpty(order.getSuits())));
            if (calculateSuit && CollectionUtils.isNotEmpty(order.getSuits())) {
                List<Order> suits = order.getSuits();
                for (Order suit : suits) {
                    getItemTagIds(suit, itemTags);
                }
            } else {
                getItemTagIds(order, itemTags);
            }
        }
        return itemTags;
    }

    private static void getItemTagIds(Order order,List<Long> itemTags){
        List<OrderItemTag> orderItemTagList = order.getOrderItemTags();
        if(CollectionUtils.isNotEmpty(orderItemTagList)){
            List<Long> itemTagIds = orderItemTagList.stream().filter(orderItemTag -> Objects.equals(1, orderItemTag.getEnableStatus())).map(OrderItemTag::getItemTagId).collect(Collectors.toList());
            itemTags.addAll(itemTagIds);
        }
    }

    /**
     * 指定
     *
     * @return
     */
    public static boolean assign(List<Long> tagIds, List<Long> ruleTagIds) {
        if (CollectionUtils.isEmpty(ruleTagIds)) {
            return Boolean.TRUE;
        }
        if (CollectionUtils.isEmpty(tagIds)) {
            return Boolean.FALSE;
        }
        for (Long tagId : ruleTagIds) {
            if (tagIds.contains(tagId)) {
                return Boolean.TRUE;
            }
        }
        return false;
    }

    /**
     * 排除
     *
     * @return
     */
    public static boolean exclude(List<Long> tagIds, List<Long> ruleTagIds) {
        if (CollectionUtils.isEmpty(ruleTagIds) || CollectionUtils.isEmpty(tagIds)) {
            return Boolean.TRUE;
        }
        for (Long tagId : ruleTagIds) {
            if (tagIds.contains(tagId)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    public static boolean isMatchedGovSubsidySubShopId(Staff Staff, Trade trade, WarehouseAllocate rule, boolean feature) {

        List<SpelCondition> conditionList = rule.getConditions();
        if (conditionList == null) {
            conditionList = new ArrayList<>();
        }

        Map<String, List<SpelCondition>> conditionMap = conditionList.stream()
                .collect(Collectors.groupingBy(SpelCondition::getField));

        List<SpelCondition> govSubsidySubShopIdConditionList = conditionMap.computeIfAbsent(SpelCondition.FIELD_GOV_SUBSIDY_SUB_SHOP_ID, k -> new ArrayList<>());

        // 是否存在国补订单分店ID条件
        boolean isGovSubsidyRule = conditionMap.containsKey(SpelCondition.FIELD_GOV_SUBSIDY_SUB_SHOP_ID) && CollectionUtils.isNotEmpty(govSubsidySubShopIdConditionList);

        TradeExt tradeExt = trade.getTradeExt();
        GovSubsidyInfo govSubsidyInfo = TradeExtUtils.parseGovSubsidyInfo(tradeExt);
        // 是否是存在分店ID的国补订单
        boolean isGovSubsidyTrade = PlatformUtils.isGovSubsidy3COrder(tradeExt) && Objects.nonNull(govSubsidyInfo) && StringUtils.isNotBlank(govSubsidyInfo.getSubShopId());

        if (!isGovSubsidyTrade) {
            // 如果 非国补订单 规则存在该条件, 则不匹配
            // 如果 非国补订单 规则不存在该条件, 则视为匹配
            return !isGovSubsidyRule;
        }else {
            if (!isGovSubsidyRule) {
                // 如果 存在分店ID的国补订单 规则不存在该条件, 则不匹配
                return !feature;
            }
            String subShopId = govSubsidyInfo.getSubShopId();
            // 理论上list.size<=1
            for (SpelCondition condition : govSubsidySubShopIdConditionList) {
                Set<String> ruleSubShopIdSet = ArrayUtils.toStringSet(condition.getValue());
                if (ruleSubShopIdSet.contains(subShopId)) {
                    // 如果规则分店ID包含订单分店ID 则视为匹配
                    return true;
                }
            }
            return false;
        }
    }

    public static boolean isWarehouseMatched(Trade trade, String warehouseIds) {
        Set<String> configExpressSet = ArrayUtils.toStringSet(warehouseIds);
        if (CollectionUtils.isEmpty(configExpressSet) || trade.getWarehouseId() == null){
            return true;
        }
        return trade.getWarehouseId() != null && configExpressSet.contains(trade.getWarehouseId().toString());
    }
}

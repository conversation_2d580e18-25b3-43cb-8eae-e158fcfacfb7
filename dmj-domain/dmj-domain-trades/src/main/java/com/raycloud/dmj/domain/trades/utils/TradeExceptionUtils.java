package com.raycloud.dmj.domain.trades.utils;

import com.google.common.base.Objects;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExcStatus;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * Description:
 *
 * @version Version 1.0
 * <AUTHOR>
 * @Copyright 2016 Git Inc. All rights reserved.
 * @CreateDate on 2016.11.03
 * @Company 杭州光云科技有限公司
 */
public abstract class TradeExceptionUtils {

    /**
     * 店铺停用异常状态
     */
    public static final String EX_USER_UNACTIVE = "EX_USER_UNACTIVE";

    /**
     * 订单挂起
     */
    public static final String EX_HALT = "EX_HALT";

    /**
     * 换地址
     */
    public static final String EX_CHANGE_ADDRESS = "EX_CHANGE_ADDRESS";

    /**
     * 退款
     */
    public static final String EX_REFUND = "EX_REFUND";

    /**
     * 部分退款
     */
    public static final String EX_PART_REFUND = "EX_PART_REFUND";

    /**
     * 商品未匹配异常状态
     */
    public static final String EX_UNALLOCATED = "EX_UNALLOCATED";

    /**
     * 库存不足异常状态
     */
    public static final String EX_INSUFFICIENT = "EX_INSUFFICIENT";

    /**
     * 商品对应关系修改异常状态
     */
    public static final String EX_RELATION_MODIFIED = "EX_RELATION_MODIFIED";
    public static final String EX_AUTO_DELETE = "EX_AUTO_DELETE";

    /**
     * 换商品的异常状态
     */
    public static final String EX_CHANGE_ITEM = "EX_CHANGE_ITEM";

    /**
     * 风控异常订单
     */
    public static final String EX_RISK_ORDER = "EX_RISK_ORDER";

    /**
     * 套件修改异常
     */
    public static final String EX_SUITE_CHANGE = "EX_SUITE_QUANTITY_CHANGE";

    /**
     * 普通商品转加工
     */
    public static final String EX_ITEM_PROCESS_EXCEP = "EX_PROCESS_CHANGE";


    /**
     * 货单付款重复订单异常
     */
    public static final String EX_COD_REPEAT = "EX_COD_REPEAT";


    public static final String EX_UPDATED_SELLERMEMO = "EX_UPDATED_SELLERMEMO";

    public static final String EX_STATUS_EXCEP_UNATTAINABLE = "EX_UNATTAINABLE";

    /**
     * 黑名单的异常状态
     */
    public static final String EX_BLACK = "EX_BLACK";
    /**
     * 发货异常
     */
    public static final String EX_DELIVER = "EX_DELIVER";
    /**
     * 上传发货异常
     */
    public static final String EX_UPLOAD_DELIVER = "EX_UPLOAD_DELIVER";
    /**
     * pdd缺货已处理异常
     */
    public static final String EX_STOCK_OUT = "EX_STOCK_OUT";


    /**
     * 发货异常
     */
    public static final String EX_WAITING_RETURN_WMS = "EX_WAITING_RETURN_WMS";
    /**
     * 不明确供销商
     */
    public static final String EX_AMBIGUITY_FX = "EX_AMBIGUITY_FX";
    /**
     * 分销商反审核
     */
    public static final String EX_UNAUDIT_FX = "EX_UNAUDIT_FX";
    /**
     * 分销商未付款
     */
    public static final String EX_WAITEPAY_FX = "EX_WAITEPAY_FX";
    /**
     * 供销商打回
     */
    public static final String EX_REPULSE_FX = "EX_REPULSE_FX";
    /**
     * 外仓商品待拆分
     */
    public static final String EX_PLATFORM_FX_SPLIT = "EX_PLATFORM_FX_SPLIT";
    /**
     * 等待合并异常
     */
    public static final String EX_WAIT_MERGE = "EX_WAIT_MERGE";
    /**
     * 商品停用异常
     */
    public static final String EX_ITEM_SHUTOFF = "EX_ITEM_SHUTOFF";
    /**
     * 天猫超时缺货回告异常
     */
    public static final String EX_TMCS_STOCK_OUT = "EX_TMCS_STOCK_OUT";

    /**
     * 唯一码下架异常
     */
    public static final String EX_UNIQUE_CODE_OFFSHELF = "EX_UNIQUE_CODE_OFFSHELF";
    /**
     * 天猫国际直营缺货回告异常
     */
    public static final String EX_TMGJZY_STOCK_OUT = "EX_TMGJZY_STOCK_OUT";

    /**
     * 得物直发物流模板匹配异常
     */
    public static final String EX_POISON_NOT_MATCH_EXPRESS_TEMPLATE = "EX_POISON_NOT_MATCH_EXPRESS_TEMPLATE";

    /**
     * 唯品会未匹配常态合作码异常
     */
    public static final String EX_VIP_COOPERATION_CODE_NOT_MATCH = "EX_VIP_COOPERATION_CODE_NOT_MATCH";

    static List<ExceptEnum> viewTradeExceptEnums = new ArrayList<>();

    static List<ExceptEnum> viewOrderExceptEnums = new ArrayList<>();

    static List<ExceptEnum> viewAnalyzeExceptEnums = new ArrayList<>();

    static {
        viewTradeExceptEnums.add(ExceptEnum.PART_REFUND);
        viewTradeExceptEnums.add(ExceptEnum.RISK);
        viewTradeExceptEnums.add(ExceptEnum.DELIVER_EXCEPT);
        viewTradeExceptEnums.add(ExceptEnum.UPLOAD_EXCEPT);
        viewTradeExceptEnums.add(ExceptEnum.SUITE_CHANGE);
        viewTradeExceptEnums.add(ExceptEnum.ITEM_PROCESS);
        viewTradeExceptEnums.add(ExceptEnum.FX_AMBIGUITY);
        viewTradeExceptEnums.add(ExceptEnum.FX_REPULSE);
        viewTradeExceptEnums.add(ExceptEnum.FX_WAITPAY);
        viewTradeExceptEnums.add(ExceptEnum.FX_UNAUDIT);
        viewTradeExceptEnums.add(ExceptEnum.PLATFORM_FX_SPLIT_EXCEPT);
        viewTradeExceptEnums.add(ExceptEnum.TMCS_STOCK_OUT);
        viewTradeExceptEnums.add(ExceptEnum.TMGJZY_STOCK_OUT);
        viewTradeExceptEnums.add(ExceptEnum.POISON_NOT_MATCH_EXPRESS_TEMPLATE);
        viewTradeExceptEnums.add(ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH);
        viewTradeExceptEnums.add(ExceptEnum.COD_REPEAT);
        viewTradeExceptEnums.add(ExceptEnum.PDD_STOCK_OUT);
        viewTradeExceptEnums.add(ExceptEnum.WAITING_RETURN_WMS);
        viewTradeExceptEnums.add(ExceptEnum.ITEM_CHANGED);
        viewTradeExceptEnums.add(ExceptEnum.ADDRESS_CHANGED);
        viewTradeExceptEnums.add(ExceptEnum.SELLER_MEMO_UPDATE);
        viewTradeExceptEnums.add(ExceptEnum.INSUFFICIENT);
        viewTradeExceptEnums.add(ExceptEnum.ONLINE_LOCK);
        viewTradeExceptEnums.add(ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        viewTradeExceptEnums.add(ExceptEnum.SELLER_MEMO_UPDATE);
        viewTradeExceptEnums.add(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT);
        viewTradeExceptEnums.add(ExceptEnum.CAI_GOU_TRADE_EXCEPT);


        viewOrderExceptEnums.add(ExceptEnum.REFUNDING);
        viewOrderExceptEnums.add(ExceptEnum.UNALLOCATED);
        viewOrderExceptEnums.add(ExceptEnum.RELATION_CHANGED);
        viewOrderExceptEnums.add(ExceptEnum.INSUFFICIENT);
        viewOrderExceptEnums.add(ExceptEnum.ITEM_CHANGED);
        viewOrderExceptEnums.add(ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        viewOrderExceptEnums.add(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT);

        viewAnalyzeExceptEnums.add(ExceptEnum.HALT);
        viewAnalyzeExceptEnums.add(ExceptEnum.ADDRESS_CHANGED);
        viewAnalyzeExceptEnums.add(ExceptEnum.BLACK_NICK);
        viewAnalyzeExceptEnums.add(ExceptEnum.SELLER_MEMO_UPDATE);
        viewAnalyzeExceptEnums.add(ExceptEnum.UNATTAINABLE);
    }


    /**
     * 处理订单异常信息，
     *
     * @param staff
     * @param trades
     * @param large2small:异常信息是否按权重从大到小排序：true：从大到小；false：从小到大 如果null:则不需要按权重排序，乱序
     */
    public static void analyzeException(Staff staff, List<Trade> trades, Boolean large2small) {
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                analyzeException(staff, trade, large2small);
            }
        }
    }

    /**
     * 处理订单异常信息，汇总Order异常信息
     *
     * @param staff
     * @param trade
     * @param large2small:异常信息是否按权重从大到小排序：true：从大到小；false：从小到大 如果null:则不需要按权重排序，乱序
     */
    public static void analyzeException(Staff staff, Trade trade, final Boolean large2small) {
        if (trade == null) {
            return;
        }
        int initialCapacity = 16;
        if (TradeExcStatus.values().length > 12) {
            //避免HashSet扩容(resize)
            initialCapacity = TradeExcStatus.values().length * 4 / 3 + 2;
        }
        Set<TradeExcStatus> excStauses = null;
        if (null == large2small) {
            excStauses = new HashSet<TradeExcStatus>(initialCapacity);
        } else {
            //按权重从大到小排序
            excStauses = new TreeSet<TradeExcStatus>(
                    new Comparator<TradeExcStatus>() {
                        @Override
                        public int compare(TradeExcStatus o1, TradeExcStatus o2) {
                            if (large2small) {
                                return o2.getWeight() - o1.getWeight();
                            } else {
                                return o1.getWeight() - o2.getWeight();
                            }

                        }
                    }
            );
        }

        trade.setExcStauses(excStauses);

        //订单独有异常：店铺停用，订单废弃，订单挂起
        //店铺停用
        if (TradeExceptUtils.isUserUnActive(staff, trade)) {
            excStauses.add(TradeExcStatus.ES_USER_UNACTIVE);
        }
        //如果订单已经发货，则为正常订单
        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
//            if (!excStauses.isEmpty()) {
//                trade.setIsExcep(1);
//            }
            return;
        }

        //订单取消
        if (trade.getIsCancel() != null && 1 == trade.getIsCancel()) {
            excStauses.add(TradeExcStatus.ES_CANCEL);
        }
        //订单挂起
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT)) {
            excStauses.add(TradeExcStatus.ES_HALT);
        }

        //Order 独有订单异常：退款 库存(改，匹,缺)
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Order order : orders) {
                //订单退款
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.REFUNDING)) {
                    excStauses.add(TradeExcStatus.ES_REFUND);
                }
                //商品未匹配
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
                    excStauses.add(TradeExcStatus.ES_UNALLOCATED);
                }
                //商品关系更改
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)) {
                    excStauses.add(TradeExcStatus.ES_ITEM_RELATION_MODIFIED);
                }
                //订单缺货
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT)) {
                    excStauses.add(TradeExcStatus.ES_STOCK_INSUFFICIENT);
                }
            }
        }
    }

    /**
     * 分析主订单
     *
     * @param trade
     * @return
     */
    public static Set<String> analyzeExcept(Staff staff, Trade trade) {
        Set<String> exceptions = new HashSet<>();
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.REFUNDING)) {
            exceptions.add(EX_REFUND);
        }
        //如果订单已经发货，则只检查店铺是否停用
        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
            return exceptions;
        }
        for (ExceptEnum exceptEnum : viewAnalyzeExceptEnums) {
            if (TradeExceptUtils.isContainExcept(staff, trade, exceptEnum) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, exceptEnum)) {
                exceptions.add(exceptEnum.getOldExceptEnum().getEnglish());
            }
        }

        return exceptions;
    }


    /**
     * 分析子订单的异常状态，参照订单查询异常处理逻辑来修改
     *
     * @param order
     * @return
     */
    public static Set<String> analyze(Staff staff, Order order) {
        Set<String> exceptions = new HashSet<String>();
        if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.REFUNDING)) {
            exceptions.add(ExceptEnum.REFUNDING.getOldExceptEnum().getEnglish());
        }
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
            return exceptions;
        }
        for (ExceptEnum exceptEnum : viewOrderExceptEnums) {
            if (OrderExceptUtils.isContainsExcept(staff, order, exceptEnum) || OrderExceptUtils.isSubOrderContainExcept(staff, order, exceptEnum)) {
                exceptions.add(exceptEnum.getOldExceptEnum().getEnglish());
            }
        }

        return exceptions;
    }

    /**
     * 分析主订单&子订单异常信息， 参照订单查询异常处理逻辑来修改
     *
     * @param trade
     * @return
     */
    public static Set<String> analyze(Staff staff, Trade trade) {
        Set<String> exceptions = analyzeExcept(staff, trade);
        if (TradeExceptUtils.isUserUnActive(staff, trade)) {
            exceptions.add(EX_USER_UNACTIVE);
        }
        for (ExceptEnum exceptEnum : viewTradeExceptEnums) {
            if (TradeUtils.isAfterSendGoods(trade) && ExceptEnum.afterSendHideExceptExceptEnums.contains(exceptEnum)) {
                continue;
            }
            if (!TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId()) && exceptEnum == ExceptEnum.INSUFFICIENT) {
                continue;
            }
            if (TradeExceptUtils.isContainExcept(staff, trade, exceptEnum) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, exceptEnum)) {
                exceptions.add(exceptEnum.getOldExceptEnum().getEnglish());
            }
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Order order : orders) {
                Set<String> orderExceptions = analyze(staff, order);
                if (CollectionUtils.isEmpty(orderExceptions)) {
                    continue;
                }
                for (String exception : orderExceptions) {
                    if (!EX_INSUFFICIENT.equals(exception)) {
                        exceptions.add(exception);
                    } else if (order.getInsufficientCanceled() == null || order.getInsufficientCanceled() == 0) {//缺货且没有取消缺货异常的情况下前端展示为缺，否则显示正常
                        exceptions.add(exception);
                    }
                }
                order.setExceptions(orderExceptions);
            }
        }
        trade.setExceptions(exceptions);
        return exceptions;
    }

    /**
     * 仅缺货异常
     */
    public static boolean onlyInsufficientExcept(Staff staff, Trade trade) {
        if (!Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES)) {
            return false;
        }
        return TradeExceptUtils.isOnlyContainsExcept(staff, trade, ExceptEnum.INSUFFICIENT);
    }

    /**
     * 包含上传发货异常
     *
     * @param trade
     * @return
     */
    public static boolean hasUploadExcept(Staff staff, Trade trade) {
        boolean b = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT);
        return b;
    }
}

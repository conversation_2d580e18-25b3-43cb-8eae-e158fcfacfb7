package com.raycloud.dmj.domain.trades;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.enums.OrderModifyLogTypeEnum.*;

/**
 * SingleToSuitData
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
public class SingleToSuitData implements Serializable  {

    /**
     * 保存平台原始的订单商品信息
     */
    private Map<String/*tid*/, List<Order/*平台原始信息*/>> originTidOrdersMap = new HashMap<>();

    /**
     * 单转套信息
     */
    private Map<Long/*sid*/, Map<Long/*转换后套件的Id*/, Set<Long/*转换前单品的Id*/>>> singleToSuitMap = new HashMap<>();

    /**
     * 单转套之前的单品信息
     */
    private Map<Long/*单品的id*/, Order/*单品信息*/> beforeSingleMap = new HashMap<>();

    /**
     * 按数量拆单信息
     */
    private Map<String/*tid*/, List<OrderModifyLog>/*按数量拆单信息*/> splitNumMap = new HashMap<>();

    /**
     * 原始单品存在异常的信息
     */
    private Map<String/*tid*/, Map<Long/*orderId*/, OrderModifyLog/*原始单品异常信息*/>> originSingleExceptMap = new HashMap<>();

    /**
     * 订单上传记录
     */
    private Map<String, Set<Long>> uploadTid2oids;

    /**
     * 需要标记异常的 trade -> oids
     */
    private Map<Trade, Set<Long>/*oid*/> markExceptOids = new HashMap<>();

    /**
     * 需要移除异常的 trade -> oids
     */
    private Map<Trade, Set<Long>/*oid*/> removeExceptOids = new HashMap<>();

    /**
     * oid > 标记异常的原因
     */
    private Map<Long/*oid*/, Set<String>/*标记异常的原因*/> oid2markExceptActions = new HashMap<>();




    /**
     * 填充单品转套件信息
     */
    public void initTradeSingleToSuitInfo(List<OrderModifyLog> singleToSuitLogs) {
        for (OrderModifyLog log : singleToSuitLogs) {
            if (Objects.equals(SPLIT_NUM.getType(), log.getModifyType())) {
                splitNumMap.computeIfAbsent(log.getTid(), k -> Lists.newArrayList()).add(log);
                continue;
            }
            if (Objects.equals(SINGLE_TO_SUIT_EXIST_EXCEPT.getType(), log.getModifyType())) {
                originSingleExceptMap.computeIfAbsent(log.getTid(), k -> Maps.newHashMap()).put(log.getOrderId(), log);
                continue;
            }
            if (!Objects.equals(SINGLE_TO_SUIT.getType(), log.getModifyType())) {
                continue;
            }
            if (StringUtils.isBlank(log.getContent())) {
                continue;
            }
            // 单转套前：对应的单品orderId
            Set<Long> singleOrderIds = Arrays.stream(log.getContent().split(",")).map(Long::valueOf).collect(Collectors.toSet());
            // 单转套后：对应的套件orderId
            Long suitOrderId = log.getOrderId();
            Long sid = log.getSid();

            Map<Long, Set<Long>> map = singleToSuitMap.get(sid);
            if (Objects.isNull(map)) {
                map = Maps.newHashMap();
                singleToSuitMap.put(sid, map);
            }
            map.put(suitOrderId, singleOrderIds);
        }
    }


    public void clear() {
        originTidOrdersMap.clear();
        singleToSuitMap.clear();
        beforeSingleMap.clear();
        splitNumMap.clear();
        originSingleExceptMap.clear();
        uploadTid2oids = null;
        markExceptOids.clear();
        removeExceptOids.clear();
        oid2markExceptActions.clear();
    }
}

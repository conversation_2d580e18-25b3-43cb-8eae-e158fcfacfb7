package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;
import java.util.Objects;

/**
 * 小包实体
 */
@Table(name = "trade_parcel", routerKey = "tradeParcelDbNo")
public class TradeParcel extends Model {

    private static final long serialVersionUID = -7022698737559392437L;

    //待上传
    public static final int UPLOAD_STATUS_TO_UPLOAD = 1;

    //已上传
    public static final int UPLOAD_STATUS_UPLOADED = 2;

    //已取消
    public static final int UPLOAD_STATUS_CANCELLED = 3;

    //上传失败
    public static final int UPLOAD_STATUS_FAIL = 4;

    /**
     * id
     */
    private Long id;

    /**
     * 	公司id
     */
    private Long companyId;

    /**
     * 店铺id
     */
    private Long taobaoId;

    /**
     * 大包组包id 默认为0
     */
    private Long combineParcelId;

    /**
     * 物流渠道
     */
    private String shippingCarrier;

    /**
     * 物流ID
     */
    private Integer logisticId;

    /**
     * 交接单的小包编码ID
     */
    private String LPOrderCode;

    /**
     * 系统单号
     */
    private Long sid;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 新重量
     */
    private Double netWeight;

    /**
     * 体积
     */
    private Double volume;

    /**
     * 状态
     */
    private Integer status;

    /**
     *  上传状态 1未上传 2已上传 3已取消 4上传失败
     */
    private Integer uploadStatus;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * created
     */
    private Date created;

    /**
     * modified
     */
    private Date modified;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 原因
     */
    private String reason;

    /**
     * 发货时间
     */
    private Date consignTime;

    /**
     * 平台
     */
    private String source;

    private String subSource;

    /**
     * 中转仓
     */
    private String transferWarehouseName;

    /**
     * 中转仓Id
     */
    private String transferWarehouseId;

    /**
     * 中转仓地址
     */
    private String warehouseAddress;
    /**
     * 发货仓库Id
     */
    private String consignWarehouseId;

    /**
     * 发货仓库名称
     */
    private String consignWarehouseName;

    /**
     * smt中转仓
     */
    private String storeName;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 系统状态
     */
    private String sysStatus;

    /**
     * 平台状态
     */
    private String unifiedStatus;

    /**
     * 波次号
     */
    private Long waveId;

    /**
     * 是否退款
     */
    private Long isRefund;

    /**
     * 是否交易作废
     */
    private Long isCancel;

    /**
     * 是否上传成功。
     */
    private Boolean uploadSuccess;

    /**
     * 交付方式
     */
    private String deliveryType;

    /**
     * 速卖通异常小包信息
     */
    private String exceptionStatus;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getLogisticId() {
        return logisticId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public void setLogisticId(Integer logisticId) {
        this.logisticId = logisticId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TradeParcel that = (TradeParcel) o;
        return Objects.equals(id, that.id) && Objects.equals(companyId, that.companyId) && Objects.equals(taobaoId, that.taobaoId) && Objects.equals(combineParcelId, that.combineParcelId) && Objects.equals(shippingCarrier, that.shippingCarrier) && Objects.equals(logisticId, that.logisticId) && Objects.equals(sid, that.sid) && Objects.equals(tid, that.tid) && Objects.equals(outSid, that.outSid) && Objects.equals(weight, that.weight) && Objects.equals(netWeight, that.netWeight) && Objects.equals(volume, that.volume) && Objects.equals(status, that.status) && Objects.equals(uploadStatus, that.uploadStatus) && Objects.equals(enableStatus, that.enableStatus) && Objects.equals(created, that.created) && Objects.equals(modified, that.modified) && Objects.equals(shopName, that.shopName) && Objects.equals(reason, that.reason) && Objects.equals(consignTime, that.consignTime) && Objects.equals(source, that.source);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, companyId, taobaoId, combineParcelId, shippingCarrier, logisticId, sid, tid, outSid, weight, netWeight, volume, status, uploadStatus, enableStatus, created, modified, shopName, reason, consignTime, source);
    }

    public String getLPOrderCode() {
        return LPOrderCode;
    }

    public void setLPOrderCode(String LPOrderCode) {
        this.LPOrderCode = LPOrderCode;
    }

    public String getTransferWarehouseName() {
        return transferWarehouseName;
    }

    public void setTransferWarehouseName(String transferWarehouseName) {
        this.transferWarehouseName = transferWarehouseName;
    }

    public String getConsignWarehouseName() {
        return consignWarehouseName;
    }

    public void setConsignWarehouseName(String consignWarehouseName) {
        this.consignWarehouseName = consignWarehouseName;
    }

    public String getTransferWarehouseId() {
        return transferWarehouseId;
    }

    public void setTransferWarehouseId(String transferWarehouseId) {
        this.transferWarehouseId = transferWarehouseId;
    }

    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getConsignWarehouseId() {
        return consignWarehouseId;
    }

    public void setConsignWarehouseId(String consignWarehouseId) {
        this.consignWarehouseId = consignWarehouseId;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Long getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Long isRefund) {
        this.isRefund = isRefund;
    }

    public Long getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Long isCancel) {
        this.isCancel = isCancel;
    }

    public Boolean getUploadSuccess() {
        return uploadSuccess;
    }

    public void setUploadSuccess(Boolean uploadSuccess) {
        this.uploadSuccess = uploadSuccess;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getExceptionStatus() {
        return exceptionStatus;
    }

    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }
}

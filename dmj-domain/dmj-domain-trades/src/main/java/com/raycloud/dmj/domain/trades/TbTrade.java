package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Table;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Table(name = "trade", routerKey = "tradeDbNo")
public class TbTrade extends Trade implements Orderable<TbOrder> {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * num
     */
    private Integer num;
    /**
     * num_iid
     */
    private Long numIid;
    /**
     * title
     */
    private String title;

    /**
     * seller_cod_fee
     */
    private String sellerCodFee;
    /**
     * point_fee
     */
    private Integer pointFee;
    /**
     * has_post_fee
     */
    private Long hasPostFee;
    /**
     * is_lgtype
     */
    private Long isLgtype;
    /**
     * is_brand_sale
     */
    private Long isBrandSale;
    /**
     * is_force_wlb
     */
    private Long isForceWlb;
    /**
     * alipay_id
     */
    private Long alipayId;
    /**
     * alipay_no
     */
    private String alipayNo;
    /**
     * has_yfx
     */
    private Long hasYfx;
    /**
     * yfx_fee
     */
    private String yfxFee;
    /**
     * yfx_id
     */
    private String yfxId;
    /**
     * shipping_type
     */
    private String shippingType;
    /**
     * buyer_cod_fee
     */
    private String buyerCodFee;
    /**
     * express_agency_fee
     */
    private String expressAgencyFee;
    /**
     * buyer_obtain_point_fee
     */
    private Long buyerObtainPointFee;
    /**
     * cod_fee
     */
    private String codFee;
    /**
     * cod_status
     */
    private String codStatus;
    /**
     * can_rate
     */
    private Long canRate;
    /**
     * commission_fee
     */
    private String commissionFee;
    /**
     * trade_memo
     */
    private String tradeMemo;

    /**
     * seller_rate
     */
    private Long sellerRate;
    /**
     * buyer_rate
     */
    private Long buyerRate;
    /**
     * real_point_fee
     */
    private Long realPointFee;
    /**
     * seller_alipay_no
     */
    private String sellerAlipayNo;
    /**
     * seller_mobile
     */
    private String sellerMobile;
    /**
     * seller_phone
     */
    private String sellerPhone;
    /**
     * seller_name
     */
    private String sellerName;
    /**
     * seller_email
     */
    private String sellerEmail;
    /**
     * available_confirm_fee
     */
    private String availableConfirmFee;
    /**
     * received_payment
     */
    private String receivedPayment;


    /**
     * 分阶段付款的订单状态（例如万人团订单等），目前有三返回状态 FRONT_NOPAID_FINAL_NOPAID(定金未付尾款未付)，FRONT_PAID_FINAL_NOPAID(定金已付尾款未付)，FRONT_PAID_FINAL_PAID(定金和尾款都付)
     */
    private String stepTradeStatus;
    /**
     * 分阶段付款的已付金额（万人团订单已付金额）
     */
    private BigDecimal stepPaidFee;


    /**
     * 运单编号，这个字段将作为主次店铺的运单编号和运单公司的关联,1表示此订单的店铺使用绑定了的主店铺的的快递模板
     */
    private String ydId;

    /**
     * 子订单列表
     */
    protected List<TbOrder> orders;

    /**
     * @return num num
     */
    public Integer getNum() {
        return num;
    }

    /**
     * @param num num
     */
    public TbTrade setNum(Integer num) {
        this.num = num;
        return this;
    }

    /**
     * @return numIid num_iid
     */
    public Long getNumIid() {
        return numIid;
    }

    /**
     * @param numIid num_iid
     */
    public TbTrade setNumIid(Long numIid) {
        this.numIid = numIid;
        return this;
    }

    /**
     * @return title title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title title
     */
    public TbTrade setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * @return sellerCodFee seller_cod_fee
     */
    public String getSellerCodFee() {
        return sellerCodFee;
    }

    /**
     * @param sellerCodFee seller_cod_fee
     */
    public TbTrade setSellerCodFee(String sellerCodFee) {
        this.sellerCodFee = sellerCodFee;
        return this;
    }

    /**
     * @return pointFee point_fee
     */
    public Integer getPointFee() {
        return pointFee;
    }

    /**
     * @param pointFee point_fee
     */
    public TbTrade setPointFee(Integer pointFee) {
        this.pointFee = pointFee;
        return this;
    }

    /**
     * @return hasPostFee has_post_fee
     */
    public Long getHasPostFee() {
        return hasPostFee;
    }

    /**
     * @param hasPostFee has_post_fee
     */
    public TbTrade setHasPostFee(Long hasPostFee) {
        this.hasPostFee = hasPostFee;
        return this;
    }

    /**
     * @return isLgtype is_lgtype
     */
    public Long getIsLgtype() {
        return isLgtype;
    }

    /**
     * @param isLgtype is_lgtype
     */
    public TbTrade setIsLgtype(Long isLgtype) {
        this.isLgtype = isLgtype;
        return this;
    }

    /**
     * @return isBrandSale is_brand_sale
     */
    public Long getIsBrandSale() {
        return isBrandSale;
    }

    /**
     * @param isBrandSale is_brand_sale
     */
    public TbTrade setIsBrandSale(Long isBrandSale) {
        this.isBrandSale = isBrandSale;
        return this;
    }

    /**
     * @return isForceWlb is_force_wlb
     */
    public Long getIsForceWlb() {
        return isForceWlb;
    }

    /**
     * @param isForceWlb is_force_wlb
     */
    public TbTrade setIsForceWlb(Long isForceWlb) {
        this.isForceWlb = isForceWlb;
        return this;
    }
    
    /**
     * @return alipayId alipay_id
     */
    public Long getAlipayId() {
        return alipayId;
    }

    /**
     * @param alipayId alipay_id
     */
    public TbTrade setAlipayId(Long alipayId) {
        this.alipayId = alipayId;
        return this;
    }

    /**
     * @return alipayNo alipay_no
     */
    public String getAlipayNo() {
        return alipayNo;
    }

    /**
     * @param alipayNo alipay_no
     */
    public TbTrade setAlipayNo(String alipayNo) {
        this.alipayNo = alipayNo;
        return this;
    }

    /**
     * @return hasYfx has_yfx
     */
    public Long getHasYfx() {
        return hasYfx;
    }

    /**
     * @param hasYfx has_yfx
     */
    public TbTrade setHasYfx(Long hasYfx) {
        this.hasYfx = hasYfx;
        return this;
    }

    /**
     * @return yfxFee yfx_fee
     */
    public String getYfxFee() {
        return yfxFee;
    }

    /**
     * @param yfxFee yfx_fee
     */
    public TbTrade setYfxFee(String yfxFee) {
        this.yfxFee = yfxFee;
        return this;
    }

    /**
     * @return yfxId yfx_id
     */
    public String getYfxId() {
        return yfxId;
    }

    /**
     * @param yfxId yfx_id
     */
    public TbTrade setYfxId(String yfxId) {
        this.yfxId = yfxId;
        return this;
    }

    /**
     * @return shippingType shipping_type
     */
    public String getShippingType() {
        return shippingType;
    }

    /**
     * @param shippingType shipping_type
     */
    public TbTrade setShippingType(String shippingType) {
        this.shippingType = shippingType;
        return this;
    }

    /**
     * @return buyerCodFee buyer_cod_fee
     */
    public String getBuyerCodFee() {
        return buyerCodFee;
    }

    /**
     * @param buyerCodFee buyer_cod_fee
     */
    public TbTrade setBuyerCodFee(String buyerCodFee) {
        this.buyerCodFee = buyerCodFee;
        return this;
    }

    /**
     * @return expressAgencyFee express_agency_fee
     */
    public String getExpressAgencyFee() {
        return expressAgencyFee;
    }

    /**
     * @param expressAgencyFee express_agency_fee
     */
    public TbTrade setExpressAgencyFee(String expressAgencyFee) {
        this.expressAgencyFee = expressAgencyFee;
        return this;
    }

    /**
     * @return buyerObtainPointFee buyer_obtain_point_fee
     */
    public Long getBuyerObtainPointFee() {
        return buyerObtainPointFee;
    }

    /**
     * @param buyerObtainPointFee buyer_obtain_point_fee
     */
    public TbTrade setBuyerObtainPointFee(Long buyerObtainPointFee) {
        this.buyerObtainPointFee = buyerObtainPointFee;
        return this;
    }

    /**
     * @return codFee cod_fee
     */
    public String getCodFee() {
        return codFee;
    }

    /**
     * @param codFee cod_fee
     */
    public TbTrade setCodFee(String codFee) {
        this.codFee = codFee;
        return this;
    }

    /**
     * @return codStatus cod_status
     */
    public String getCodStatus() {
        return codStatus;
    }

    /**
     * @param codStatus cod_status
     */
    public TbTrade setCodStatus(String codStatus) {
        this.codStatus = codStatus;
        return this;
    }

    /**
     * @return canRate can_rate
     */
    public Long getCanRate() {
        return canRate;
    }

    /**
     * @param canRate can_rate
     */
    public TbTrade setCanRate(Long canRate) {
        this.canRate = canRate;
        return this;
    }

    /**
     * @return commissionFee commission_fee
     */
    public String getCommissionFee() {
        return commissionFee;
    }

    /**
     * @param commissionFee commission_fee
     */
    public TbTrade setCommissionFee(String commissionFee) {
        this.commissionFee = commissionFee;
        return this;
    }

    /**
     * @return tradeMemo trade_memo
     */
    public String getTradeMemo() {
        return tradeMemo;
    }

    /**
     * @param tradeMemo trade_memo
     */
    public TbTrade setTradeMemo(String tradeMemo) {
        this.tradeMemo = tradeMemo;
        return this;
    }

    /**
     * @return sellerRate seller_rate
     */
    public Long getSellerRate() {
        return sellerRate;
    }

    /**
     * @param sellerRate seller_rate
     */
    public TbTrade setSellerRate(Long sellerRate) {
        this.sellerRate = sellerRate;
        return this;
    }

    /**
     * @return buyerRate buyer_rate
     */
    public Long getBuyerRate() {
        return buyerRate;
    }

    /**
     * @param buyerRate buyer_rate
     */
    public TbTrade setBuyerRate(Long buyerRate) {
        this.buyerRate = buyerRate;
        return this;
    }

    /**
     * @return realPointFee real_point_fee
     */
    public Long getRealPointFee() {
        return realPointFee;
    }

    /**
     * @param realPointFee real_point_fee
     */
    public TbTrade setRealPointFee(Long realPointFee) {
        this.realPointFee = realPointFee;
        return this;
    }

    /**
     * @return sellerAlipayNo seller_alipay_no
     */
    public String getSellerAlipayNo() {
        return sellerAlipayNo;
    }

    /**
     * @param sellerAlipayNo seller_alipay_no
     */
    public TbTrade setSellerAlipayNo(String sellerAlipayNo) {
        this.sellerAlipayNo = sellerAlipayNo;
        return this;
    }

    /**
     * @return sellerMobile seller_mobile
     */
    public String getSellerMobile() {
        return sellerMobile;
    }

    /**
     * @param sellerMobile seller_mobile
     */
    public TbTrade setSellerMobile(String sellerMobile) {
        this.sellerMobile = sellerMobile;
        return this;
    }

    /**
     * @return sellerPhone seller_phone
     */
    public String getSellerPhone() {
        return sellerPhone;
    }

    /**
     * @param sellerPhone seller_phone
     */
    public TbTrade setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone;
        return this;
    }

    /**
     * @return sellerName seller_name
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     * @param sellerName seller_name
     */
    public TbTrade setSellerName(String sellerName) {
        this.sellerName = sellerName;
        return this;
    }

    /**
     * @return sellerEmail seller_email
     */
    public String getSellerEmail() {
        return sellerEmail;
    }

    /**
     * @param sellerEmail seller_email
     */
    public TbTrade setSellerEmail(String sellerEmail) {
        this.sellerEmail = sellerEmail;
        return this;
    }

    /**
     * @return availableConfirmFee available_confirm_fee
     */
    public String getAvailableConfirmFee() {
        return availableConfirmFee;
    }

    /**
     * @param availableConfirmFee available_confirm_fee
     */
    public TbTrade setAvailableConfirmFee(String availableConfirmFee) {
        this.availableConfirmFee = availableConfirmFee;
        return this;
    }

    /**
     * @return receivedPayment received_payment
     */
    public String getReceivedPayment() {
        return receivedPayment;
    }

    /**
     * @param receivedPayment received_payment
     */
    public TbTrade setReceivedPayment(String receivedPayment) {
        this.receivedPayment = receivedPayment;
        return this;
    }

    /**
     * @return stepTradeStatus 分阶段付款的订单状态（例如万人团订单等），目前有三返回状态 FRONT_NOPAID_FINAL_NOPAID(定金未付尾款未付)，FRONT_PAID_FINAL_NOPAID(定金已付尾款未付)，FRONT_PAID_FINAL_PAID(定金和尾款都付)
     */
    public String getStepTradeStatus() {
        return stepTradeStatus;
    }

    /**
     * @param stepTradeStatus 分阶段付款的订单状态（例如万人团订单等），目前有三返回状态 FRONT_NOPAID_FINAL_NOPAID(定金未付尾款未付)，FRONT_PAID_FINAL_NOPAID(定金已付尾款未付)，FRONT_PAID_FINAL_PAID(定金和尾款都付)
     */
    public TbTrade setStepTradeStatus(String stepTradeStatus) {
        this.stepTradeStatus = stepTradeStatus;
        return this;
    }

    /**
     * @return stepPaidFee 分阶段付款的已付金额（万人团订单已付金额）
     */
    public BigDecimal getStepPaidFee() {
        return stepPaidFee;
    }

    /**
     * @param stepPaidFee 分阶段付款的已付金额（万人团订单已付金额）
     */
    public TbTrade setStepPaidFee(BigDecimal stepPaidFee) {
        this.stepPaidFee = stepPaidFee;
        return this;
    }

    public String getYdId() {
        return ydId;
    }

    public TbTrade setYdId(String ydId) {
        this.ydId = ydId;
        return this;
    }

	@Override
	public void setOrders(List<TbOrder> orders) {
		this.orders = orders;
	}

	@Override
	public List<TbOrder> getOrders() {
		if(null == this.orders) {
            this.orders = new ArrayList<TbOrder>();
        }
		return this.orders;
	}
}
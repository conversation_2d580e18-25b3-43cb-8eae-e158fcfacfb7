package com.raycloud.dmj.domain.trades;


import com.raycloud.dmj.domain.trades.bo.ExpressCompanyMappingBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DESCRIPTION
 * @create 2022/1/11 2:44 下午
 */
public class ExpressCompanyMappingBatchDTO {
    List<ExpressCompanyMappingBO> list;

    private Map<String, String> map;
    /**
     * 传入的数据是list还是map
     */
    private String createType;
    private String source;
    private String matchType;

    public String getCreateType() {
        return createType;
    }

    public void setCreateType(String createType) {
        this.createType = createType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }

    public List<ExpressCompanyMappingBO> getList() {
        return list;
    }

    public void setList(List<ExpressCompanyMappingBO> list) {
        this.list = list;
    }
}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Data
public class TradeHotItem implements Serializable {


    private static final long serialVersionUID = 6011690066306864793L;
    /**
     * 爆款类型
     *
     * 1：全部
     * 2：单件爆款
     * 3：单sku爆款
     * 4：组团
     * 5：单spu爆款
     */
    private  Integer hotItemType;

    /**
     * 
     * 编码
     */
    private  String  hotItemOuterId;


    /**
    *  订单数量
    */
    private  Integer tradeNum;


    /**
    * 订单sids集合
    */
    @JSONField(serializeUsing = StringSetSerializer.class)
    private  Set<Long> sids;


}
package com.raycloud.dmj.domain.trades.utils;

import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 对于部分特殊地址的订单进行处理
 * @create 2021-12-23
 */
public class TradeAddressUtil {

    /**
     * 县级行政区的特殊地址
     */
    private static final List<String> COUNTY_LEVEL_ADMINISTRATIVE_REGION_AREA = Arrays.asList("省直辖县级行政区划", "自治区直辖县级行政区划");

    /**
     * 直辖市的地址
     */
    private static final List<String> MUNICIPALITY_AREA = Arrays.asList("北京", "上海", "天津", "重庆");

    /**
     * 不设区县的地级市名单
     */
    public static final List<String> NO_DISTRICTS_CITY = Arrays.asList("东莞市", "中山市", "儋州市", "嘉峪关市");

    /**
     * 直辖县名单
     */
    public static final List<String> MUNICIPALITY_COUNTY = Arrays.asList("济源市", "仙桃市", "潜江市", "天门市","神农架林区",
            "五指山市","文昌市","琼海市","万宁市","东方市","定安县","屯昌县","澄迈县","临高县","琼中黎族苗族自治县","保亭黎族苗族自治县",
            "白沙黎族自治县","昌江黎族自治县","乐东黎族自治县","陵水黎族自治县","石河子市","阿拉尔市","图木舒克市","五家渠市","北屯市",
            "铁门关市","双河市","可克达拉市","昆玉市","胡杨河市","新星市");


    /**
     * 东莞市特殊区域映射关系
     */
    public static Map<String, String> DG_DISTRICTS_MAP = Maps.newHashMap();
    static {
        DG_DISTRICTS_MAP.put("南城街道", "南城区");
        DG_DISTRICTS_MAP.put("东城街道", "东城区");
        DG_DISTRICTS_MAP.put("万江街道", "万江区");
        DG_DISTRICTS_MAP.put("莞城街道", "莞城区");
    }

    /**
     * 兼容特殊地址的订单匹配
     */
    public static void compatibleSpecialAddress(Trade trade) {
        processAddressOfCountyLevelAdministrativeRegion(trade);
        processAddressOfMunicipality(trade);
        processAddressOrSpecial(trade);
    }

    /**
     * 处理特殊地址的兼容 https://gykj.yuque.com/entavv/xb9xi5/ulw622/
     */
    public static void processAddressOrSpecial(Trade trade) {
        if ("江苏省".equals(trade.getReceiverState()) && "苏州市".equals(trade.getReceiverCity()) && "工业园区".equals(trade.getReceiverDistrict())) {
            trade.setReceiverDistrict("苏州工业园区");
        }
    }


    /**
     * 对于二级地址（市）为《省直辖县级行政区划、自治区直辖县级行政区划》的特殊订单，把其三级地址（区县）替换为二级地址（市）
     * ex: 河南省-省直辖县级行政区划-济源市 ----> 河南省-济源市-null
     */
    public static void processAddressOfCountyLevelAdministrativeRegion(Trade trade) {
        if (Objects.isNull(trade)) {
            return;
        }
        if (COUNTY_LEVEL_ADMINISTRATIVE_REGION_AREA.contains(trade.getReceiverCity())) {
            trade.setReceiverCity(trade.getReceiverDistrict());
            //trade.setReceiverDistrict("");
        }
    }


    /**
     * 对于地址是直辖市的订单进行特殊处理
     */
    public static void processAddressOfMunicipality(Trade trade) {
        if (Objects.isNull(trade) || StringUtils.isBlank(trade.getReceiverState()) || StringUtils.isBlank(trade.getReceiverCity())) {
            return;
        }
        for (String specialArea : MUNICIPALITY_AREA) {
            if (trade.getReceiverState().contains(specialArea)) {
                if (trade.getReceiverCity().contains("市辖区")) {
                    trade.setReceiverCity(trade.getReceiverState());
                } else if (trade.getReceiverCity().endsWith("区")) {
                    trade.setReceiverDistrict(trade.getReceiverCity());
                    trade.setReceiverCity(trade.getReceiverState());
                }
                return;
            }
        }
    }

    /**
     * 对于二级地址（市）为《市辖区》的特殊订单，把其一级地址（省）替换为二级地址（市）
     * ex: 北京市-市辖区-朝阳区 ----> 北京市-北京市-朝阳区
     * 对于二级地址（市）为《区》结尾的特殊订单，把其一级地址（省）替换为二级地址（市）,二级地址（市）替换为三级地址（区县）
     * ex: 北京市-朝阳区-xx镇  ---> 北京市-北京市-朝阳区
     */
    private static void processAddress(Trade trade) {
        if (trade.getReceiverCity().contains("市辖区")) {
            trade.setReceiverCity(trade.getReceiverState());
        } else if (trade.getReceiverCity().endsWith("区")) {
            trade.setReceiverDistrict(trade.getReceiverCity());
            trade.setReceiverCity(trade.getReceiverState());
        }
    }


    /**
     * 处理不设区县的地级市名单
     * <p>
     * 参考文档：https://gykj.yuque.com/docs/share/7f577658-659f-442e-9502-66bf202a3d17?#CdZq
     * 同步tb同步下来是广东省-东莞市-null（但是前端是随机选择了一个）-东城街道， dy同步下来是广东省-东莞市-东莞市-东城街道；
     *      tb处理后是广东省-东莞市-东城街道-东城街道，                     dy处理后是广东省-东莞市-东城街道-东城街道；
     * </p>
     * @param trade
     */
    public static void processNoDistrictsCityAddress(Trade trade) {
        if (Objects.isNull(trade)) {
            return;
        }
        if (NO_DISTRICTS_CITY.contains(trade.getReceiverCity())) {
            if (StringUtils.isNotBlank(trade.getReceiverStreet())) {//排除掉空格字符串
                if (DG_DISTRICTS_MAP.containsKey(trade.getReceiverStreet())) {
                    trade.setReceiverDistrict(DG_DISTRICTS_MAP.get(trade.getReceiverStreet()));
                } else {
                    trade.setReceiverDistrict(trade.getReceiverStreet());
                }
            }
        }
    }

    /**
     * 处理直辖县名单
     * <p>
     * 参考文档：https://gykj.yuque.com/docs/share/7f577658-659f-442e-9502-66bf202a3d17?#CdZq
     * 海南省-白沙黎族自治县-国营白沙农场 我是详细地址；
     * 现状：同步tb同步下来是海南省-白沙黎族自治县-null（但是前端是随机选择了一个）-国营白沙农场，dy同步下来是海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场；pdd同步下来是海南省-其他省直辖县-白沙黎族自治县；
     * 问题：快递规则都匹配不上；比如tb前端选择不上
     * 期望效果：tb处理后海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场，                    dy处理后是海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场；pdd处理后是海南省-白沙黎族自治县-白沙黎族自治县；
     * </p>
     * @param trade
     */
    public static void processMunicipalityCountyAddress(Trade trade) {
        if (Objects.isNull(trade)) {
            return;
        }
        if (COUNTY_LEVEL_ADMINISTRATIVE_REGION_AREA.contains(trade.getReceiverCity())) {
            trade.setReceiverCity(trade.getReceiverDistrict());
        } else {
            if (MUNICIPALITY_COUNTY.contains(trade.getReceiverCity())) {
                if (StringUtils.isNotBlank(trade.getReceiverStreet())) {//排除掉空格字符串
                    trade.setReceiverDistrict(trade.getReceiverStreet());
                }
            }
        }
    }

    /**
     * 回滚处理逻辑入口方法；
     * @param trade
     */
    public static void rollbackCompatibleSpecialAddress(Trade trade) {
        rollbackProcessNoDistrictsCityAddress(trade);
        rollbackProcessMunicipalityCountyAddress(trade);
    }

    /**
     * 回滚-处理不设区县的地级市名单
     * <p>
     * 同步tb同步下来是广东省-东莞市-null（但是前端是随机选择了一个）-东城街道， dy同步下来是广东省-东莞市-东莞市-东城街道；
     * tb处理后是广东省-东莞市-东城街道-东城街道，                          dy处理后是广东省-东莞市-东城街道-东城街道；
     * </p>
     * @param trade
     */
    public static void rollbackProcessNoDistrictsCityAddress(Trade trade) {
        if (Objects.isNull(trade)) {
            return;
        }
        if (NO_DISTRICTS_CITY.contains(trade.getReceiverCity())) {
            if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_1688.equals(trade.getSource())) {
                if (StringUtils.isNotEmpty(trade.getReceiverStreet())) {
                    trade.setReceiverDistrict("");
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())) {
                trade.setReceiverDistrict(trade.getReceiverCity());
            }
        }
    }


    /**
     * 回滚-处理直辖县名单
     * <p>
     * 海南省-白沙黎族自治县-国营白沙农场 我是详细地址；
     * 现状：同步tb同步下来是海南省-白沙黎族自治县-null（但是前端是随机选择了一个）-国营白沙农场，dy同步下来是海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场；pdd同步下来是海南省-其他省直辖县-白沙黎族自治县；
     * 问题：快递规则都匹配不上；比如tb前端选择不上
     * 期望效果：tb处理后海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场，                    dy处理后是海南省-白沙黎族自治县-白沙黎族自治县-国营白沙农场；pdd处理后是海南省-白沙黎族自治县-白沙黎族自治县；
     * </p>
     * @param trade
     */
    public static void rollbackProcessMunicipalityCountyAddress(Trade trade) {
        if (Objects.isNull(trade)) {
            return;
        }
        if (MUNICIPALITY_COUNTY.contains(trade.getReceiverCity())) {
            if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_1688.equals(trade.getSource())) {
                trade.setReceiverDistrict("");
            }
            if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())) {
                if ("海南省".contains(trade.getReceiverState())) {
                    trade.setReceiverDistrict(trade.getReceiverCity());
                    trade.setReceiverCity("省直辖县级行政区划");
                }
                if ("新疆维吾尔自治区".contains(trade.getReceiverState())) {
                    trade.setReceiverDistrict(trade.getReceiverCity());
                    trade.setReceiverCity("自治区直辖县级行政区划");
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())) {
                trade.setReceiverDistrict(trade.getReceiverCity());
            }
        }
    }


    /**
     * 判断是否包含特殊地址
     * @param trade
     * @return
     */
    public static Boolean isSpecialAddress(Trade trade){
        if (TradeUtils.isTbTrade(trade.getSource())
                || StringUtils.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_1688)
                || StringUtils.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_FXG)) {
            if (TradeAddressUtil.NO_DISTRICTS_CITY.contains(trade.getReceiverCity()) || TradeAddressUtil.MUNICIPALITY_COUNTY.contains(trade.getReceiverCity())) {
                return true;
            }
        }
        return false;
    }
}

package com.raycloud.dmj.domain.trades;

import lombok.Data;

import java.io.Serializable;

@Data
public class TradeParcelBTASDetail implements Serializable {

    private static final long serialVersionUID = 3982171064176802624L;
    /**
     * 系统单号
     */
    private Long sid;

    /**
     * 订单码
     */
    private String orderCode;

    /**
     * 商家编码
     */
    private String outId;

    /**
     * 是否异常
     * 0：无异常
     * 1：异常
     */
    private Integer isExcept;
}

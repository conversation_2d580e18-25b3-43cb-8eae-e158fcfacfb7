package com.raycloud.dmj.domain.trades;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TradeHotItems implements Serializable {

    private static final long serialVersionUID = 6176622439388339413L;

    /**
     * 总订单数
     */
    private Long total;


    /**
     * 是否开启爆款打印V2 0：未开启，1：已开启
     */
    private Integer openHotItemPrintV2;

    /**
     * 可能订单数大于5W，可能没有权限
     */
    private Boolean ifNeedHotItem = true;



    private List<TradeHotItem> list;

}

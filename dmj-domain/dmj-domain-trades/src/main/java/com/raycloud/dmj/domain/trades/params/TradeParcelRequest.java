package com.raycloud.dmj.domain.trades.params;


public class TradeParcelRequest {

    private Long taobaoId;

    private String shippingCarrier;

    private String[] shippingCarriers;

    private String sid;

    private Integer uploadStatus;

    private Long combineParcelId;

    private String tid;

    private String outSid;

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String[] getShippingCarriers() {
        return shippingCarriers;
    }

    public void setShippingCarriers(String[] shippingCarriers) {
        this.shippingCarriers = shippingCarriers;
    }
}

package com.raycloud.dmj.domain.trades.search;

import com.raycloud.dmj.domain.trades.Order;

/**
 * @Description <pre>
 *   order表附加条件 注意如果这里定义的是exclude 对应sql的表别名应该是 o2
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-20
 */
public enum OrderAdditionalConditionEnum {

    //order类型相关
    TYPE_NORMAL(Order.TypeOfNormal,"普通商品"," o.type = " + Order.TypeOfNormal),
    TYPE_COMBINE(Order.TypeOfCombineOrder,"套件商品"," o.type = " + Order.TypeOfCombineOrder),
    TYPE_GROUP(Order.TypeOfGroupOrder,"组合装商品"," o.type = " + Order.TypeOfGroupOrder),
    TYPE_PROCESS(Order.TypeOfProcessOrder,"加工商品"," o.type = " + Order.TypeOfProcessOrder),

    TYPE_NO_PROCESS(7,true,"不含加工商品"," o.type = " + Order.TypeOfProcessOrder),

    /**
     *  这是一个特殊的处理 前端界面查询指定类型的order时 需要去除本身是虚拟商品的
     *  比如一个商品 即是虚拟品也是套件
     *  但是按TYPE_COMBINE查询时要排除掉,因为这个要归类到虚拟商品那个选项中去
     * */
    TYPE_NORMAL_IGNORE_VIRTUAL(10,"普通商品(非虚拟)","(o.type = " + Order.TypeOfNormal +" and  o.is_virtual=0) "),
    TYPE_COMBINE_IGNORE_VIRTUAL(20,"套件商品(非虚拟)","(o.type = " + Order.TypeOfCombineOrder +" and  o.is_virtual=0) "),
    TYPE_GROUP_IGNORE_VIRTUAL(30,"组合装商品(非虚拟)","(o.type = " + Order.TypeOfGroupOrder +" and  o.is_virtual=0) "),
    TYPE_PROCESS_IGNORE_VIRTUAL(40,"加工商品(非虚拟)","(o.type = " + Order.TypeOfProcessOrder +" and  o.is_virtual=0) "),



    //状态相关
    STATUS_INSUFFICIENT(100,"缺货"," o.stock_status ='INSUFFICIENT'"),




    //商品属性相关
    PROP_VIRTUAL(200,"虚拟商品"," ( o.is_virtual = 1 and o.enable_status=1 )"),
    PROP_GIFT(Order.TypeOfGiftOrder,"赠品"," ( o.gift_num > 0 and o.enable_status=1 )"),
    PROP_NO_GIFT(Order.TypeOfNoGiftOrder,true,"不含赠品","( o2.gift_num > 0 and o2.enable_status=1 ) "),


    ;

    private int code;

    private boolean exclude =false;
    private String desc;
    private String sql;

    OrderAdditionalConditionEnum(int code, String desc, String sql) {
        this.code = code;
        this.desc = desc;
        this.sql = sql;
    }

    OrderAdditionalConditionEnum(int code, boolean exclud, String desc, String sql) {
        this.code = code;
        this.exclude = exclud;
        this.desc = desc;
        this.sql = sql;
    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getSql() {
        return sql;
    }

    public boolean isExclude() {
        return exclude;
    }
}

package com.raycloud.dmj.domain.trades.params;

import java.io.Serializable;
import java.util.List;

public class PageColumnConfUpdateParam implements Serializable {
    private static final long serialVersionUID = 3192463413830995312L;
    /**
     * 列配置项ID
     */
    private Long id;

    /**
     * 列编码
     */
    private String colCode;

    /**
     * 列显示标题
     */
    private String colTitle;

    /**
     * 列所属页面ID
     */
    private Long pageId;

    /**
     * 是否为该页面默认显示的列
     */
    private Integer isDefault;

    /**
     * 列在当前页面的排序号
     */
    private Integer sortNo;

    /**
     * 列宽度
     */
    private Integer width;
    
    /**
     * 环境配置
     */
    private String env;
    
    /**
     * 分组id
     */
    private Long groupId;

    private String toEnv;
    private List<String>fromEnvs;
    private String companyIds;
    private String token;
    private String dbNo;

    public String getDbNo() {
        return dbNo;
    }

    public void setDbNo(String dbNo) {
        this.dbNo = dbNo;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
    public String getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(String companyIds) {
        this.companyIds = companyIds;
    }

    public String getToEnv() {
        return toEnv;
    }

    public void setToEnv(String toEnv) {
        this.toEnv = toEnv;
    }

    public List<String> getFromEnvs() {
        return fromEnvs;
    }

    public void setFromEnvs(List<String> fromEnvs) {
        this.fromEnvs = fromEnvs;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public String getColTitle() {
        return colTitle;
    }

    public void setColTitle(String colTitle) {
        this.colTitle = colTitle;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

	public String getEnv() {
		return env;
	}

	public void setEnv(String env) {
		this.env = env;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
    
}

package com.raycloud.dmj.domain;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @program: erp-core
 * @description: Created by luzhehen
 * Created on 2022/4/27 10:38 上午
 * Copyright(c)2022  版权所有
 */
public interface TradeConstant {

    /**
     * 特殊省份
     */
    enum DistrictAndCounty {
        INNER_MONGOLIA("内蒙","内蒙"),
        GUANGXI("广西","广西"),
        SICHUAN("四川","四川"),
        TIBET("西藏","西藏"),
        XINJIANG("新疆","新疆"),
        NINGXIA("宁夏","宁夏"),
        HAINAN("海南","海南"),
        ;

        private String key;
        private String label;

        public String getKey() {
            return key;
        }

        public String getLabel() {
            return label;
        }

        DistrictAndCounty(String key, String label) {
            this.key = key;
            this.label = label;
        }

        final static Map<String, DistrictAndCounty> ENUM_MAP = new HashMap<>();

        static {
            Iterator<DistrictAndCounty> iterable = Arrays.stream(values()).iterator();
            while (iterable.hasNext()) {
                DistrictAndCounty type = iterable.next();
                ENUM_MAP.put(type.key, type);
            }
        }

        /**
         * 判断值是否满足枚举中的value
         */
        public static boolean validation(String key) {
            return ENUM_MAP.containsKey(key);
        }
        
        /**
         * 模糊匹配是否存在当前key
        */
        public static boolean vagueDoesItExist(String key) {
            for (Map.Entry<String, DistrictAndCounty> entry : ENUM_MAP.entrySet()) {
                if(key.contains(entry.getKey())){
                    return true;
                }
            }
            return false;
        }

        public static DistrictAndCounty getValue(String key) {
            return ENUM_MAP.get(key);
        }

    }

}

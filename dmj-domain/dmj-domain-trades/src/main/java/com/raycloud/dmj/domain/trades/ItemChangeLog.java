package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 17/3/10.
 * 订单同步的时候，对非覆盖模式下换商品的子订单的记录
 */
@Table(name = "item_change_log", routerKey = "itemChangeLogDbNo")
public class ItemChangeLog extends Model {

    private static final long serialVersionUID = -306433457689167984L;
    /**
     * 日志主键
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 系统子订单号
     */
    private Long orderId;

    /**
     * 上一次子订单的平台商品数字编号
     */
    private String preNumIid;

    /**
     * 上一次订单的平台规格编号
     */
    private String preSkuId;

    /**
     * 当前子订单的平台商品数字编号
     */
    private String curNumIid;

    /**
     * 当前订单的平台规格编号
     */
    private String curSkuId;

    /**
     * 新增时间
     */
    private Date insertTime;

    /**
     * 是否可用
     */
    private Integer enableStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getPreNumIid() {
        return preNumIid;
    }

    public void setPreNumIid(String preNumIid) {
        this.preNumIid = preNumIid;
    }

    public String getPreSkuId() {
        return preSkuId;
    }

    public void setPreSkuId(String preSkuId) {
        this.preSkuId = preSkuId;
    }

    public String getCurNumIid() {
        return curNumIid;
    }

    public void setCurNumIid(String curNumIid) {
        this.curNumIid = curNumIid;
    }

    public String getCurSkuId() {
        return curSkuId;
    }

    public void setCurSkuId(String curSkuId) {
        this.curSkuId = curSkuId;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

}

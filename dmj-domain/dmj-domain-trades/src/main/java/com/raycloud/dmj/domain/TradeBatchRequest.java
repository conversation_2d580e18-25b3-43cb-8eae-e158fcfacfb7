package com.raycloud.dmj.domain;

import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.item.OrderChangeItemRecord;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.request.SecurityEventTrackingBatchOrderRequest;
import com.raycloud.dmj.domain.trade.except.ExceptHandlerDto;
import com.raycloud.dmj.domain.trades.*;

import java.util.List;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @created 2020-07-01 23:10
 */
public class TradeBatchRequest {

    private TradeControllerParams params;

    private TradeQueryParams queryParams;

    private ProgressEnum progressEnum;

    private ProgressData progress;

    private AtomicInteger countCurrent;
    private AtomicInteger sucNum;

    private List<String> systemExceptions;

    private List<Long> customExceptions;

    private TradeUpdateLogisticsParams tradeUpdateLogisticsParams;

    private Integer coverException;
    private Integer isCancelSmallRefund;

    /**
     * 请求查询分页大小 默认5W
     */
    private Integer pageSize = 50000;
    /**
     * 是否复制商品备注
     */
    private boolean copyOrderExtOrderRemark;

    /**
     * 以关联码维度记录商品更换细节
     */
    private List<TradeItemChangeDetail> itemChangeDetailList;

    private ConcurrentMap<String, TradeItemChangeDetail> itemChangeDetailMap;

    /**
     * 商品更换记录表
     */
    private List<OrderChangeItemRecord> orderChangeItemRecordList;

    private List<Trade> tradeList;

    private SecurityEventTrackingBatchOrderRequest fxgSecurityRequest;

    /**
     * 订单数据来源，true:页面勾选；false:条件查询
     */
    private Boolean pageSelect = false;

    public SecurityEventTrackingBatchOrderRequest getFxgSecurityRequest() {
        return fxgSecurityRequest;
    }

    public void setFxgSecurityRequest(SecurityEventTrackingBatchOrderRequest fxgSecurityRequest) {
        this.fxgSecurityRequest = fxgSecurityRequest;
    }

    public TradeQueryParams getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(TradeQueryParams queryParams) {
        this.queryParams = queryParams;
    }

    public TradeControllerParams getParams() {
        return params;
    }

    public void setParams(TradeControllerParams params) {
        this.params = params;
    }

    public ProgressEnum getProgressEnum() {
        return progressEnum;
    }

    public void setProgressEnum(ProgressEnum progressEnum) {
        this.progressEnum = progressEnum;
    }

    public ProgressData getProgress() {
        return progress;
    }

    public void setProgress(ProgressData progress) {
        this.progress = progress;
    }

    public List<String> getSystemExceptions() {
        return systemExceptions;
    }

    public void setSystemExceptions(List<String> systemExceptions) {
        this.systemExceptions = systemExceptions;
    }

    public List<Long> getCustomExceptions() {
        return customExceptions;
    }

    public void setCustomExceptions(List<Long> customExceptions) {
        this.customExceptions = customExceptions;
    }

    public TradeUpdateLogisticsParams getTradeUpdateLogisticsParams() {
        return tradeUpdateLogisticsParams;
    }

    public void setTradeUpdateLogisticsParams(TradeUpdateLogisticsParams tradeUpdateLogisticsParams) {
        this.tradeUpdateLogisticsParams = tradeUpdateLogisticsParams;
    }

    public Integer getCoverException() {
        return coverException;
    }

    public void setCoverException(Integer coverException) {
        this.coverException = coverException;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<TradeItemChangeDetail> getItemChangeDetailList() {
        return itemChangeDetailList;
    }

    public void setItemChangeDetailList(List<TradeItemChangeDetail> itemChangeDetailList) {
        this.itemChangeDetailList = itemChangeDetailList;
    }

    public ConcurrentMap<String, TradeItemChangeDetail> getItemChangeDetailMap() {
        return itemChangeDetailMap;
    }

    public void setItemChangeDetailMap(ConcurrentMap<String, TradeItemChangeDetail> itemChangeDetailMap) {
        this.itemChangeDetailMap = itemChangeDetailMap;
    }

    public List<OrderChangeItemRecord> getOrderChangeItemRecordList() {
        return orderChangeItemRecordList;
    }

    public void setOrderChangeItemRecordList(List<OrderChangeItemRecord> orderChangeItemRecordList) {
        this.orderChangeItemRecordList = orderChangeItemRecordList;
    }

    public List<Trade> getTradeList() {
        return tradeList;
    }

    public void setTradeList(List<Trade> tradeList) {
        this.tradeList = tradeList;
    }

    public AtomicInteger getCountCurrent() {
        return countCurrent;
    }

    public void setCountCurrent(AtomicInteger countCurrent) {
        this.countCurrent = countCurrent;
    }

    public AtomicInteger getSucNum() {
        return sucNum;
    }

    public void setSucNum(AtomicInteger sucNum) {
        this.sucNum = sucNum;
    }

    public Boolean getPageSelect() {
        return pageSelect;
    }

    public void setPageSelect(Boolean pageSelect) {
        this.pageSelect = pageSelect;
    }

    public boolean isCopyOrderExtOrderRemark() {
        return copyOrderExtOrderRemark;
    }

    public void setCopyOrderExtOrderRemark(boolean copyOrderExtOrderRemark) {
        this.copyOrderExtOrderRemark = copyOrderExtOrderRemark;
    }

    public Integer getIsCancelSmallRefund() {
        return isCancelSmallRefund;
    }

    public void setIsCancelSmallRefund(Integer isCancelSmallRefund) {
        this.isCancelSmallRefund = isCancelSmallRefund;
    }
}

package com.raycloud.dmj.domain.payment;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import lombok.Data;

import java.util.List;

/**
 * @Description <pre>
 * 订单换商品 金额计算请求
 * </pre>
 * <AUTHOR>
 * @Date 2023-02-14
 */
@Data
public class TradeItemExchangeResponse {


    /**
     * 需更新商品
     */
    List<Order> changedOrders;

    /**
     * 需更新订单 对于合单 相应的金额分摊计算都是在原订单范围内进行的 因此这里可能会有多个待更新的trade
     */
    List<Trade> changedTrades;
}

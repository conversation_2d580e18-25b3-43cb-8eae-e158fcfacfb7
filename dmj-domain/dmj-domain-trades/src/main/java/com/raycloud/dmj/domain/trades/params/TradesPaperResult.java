package com.raycloud.dmj.domain.trades.params;

import java.io.Serializable;

/**
 * 拣选领单业务result
 *
 * <AUTHOR>
 * @date 2019/12/20
 */
public class TradesPaperResult implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 纸质拣选，提醒错误
     */
    public static final String PAPER_ERROR_CODE = "PAPER_ERROR_CODE";

    /**
     * 纸质拣选，更改拣选员
     */
    public static final String PAPER_MODIFY_PICKER = "PAPER_MODIFY_PICKER";

    /**
     * 纸质拣选，重新领取任务
     */
    public static final String PAPER_AGAIN_PICKER = "PAPER_AGAIN_PICKER";

    /**
     * 纸质拣选，更改并完成拣选
     */
    public static final String PAPER_MODIFY_COMPLETE_PICKER = "PAPER_MODIFY_COMPLETE_PICKER";

    private Boolean success;

    private String code;

    private String message;

    public TradesPaperResult(boolean success) {
        this.success = success;
    }

    public TradesPaperResult(boolean success, String code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }

    public static TradesPaperResult buildSuccess() {
        return new TradesPaperResult(Boolean.TRUE);
    }

    public static TradesPaperResult buildError(String code, String message) {
        return new TradesPaperResult(Boolean.FALSE, code, message);
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}

package com.raycloud.dmj.domain.trades.params;


import com.raycloud.dmj.domain.user.AbroadAddress;

import java.io.Serializable;

public class TradeCombineParcelRequest implements Serializable {

    private String sids;

    private Long taobaoId;

    private String combineParcelIds;

    private Long combineParcelId;

    private String parcelIds;

    private Integer status;

    private Integer uploadStatus;

    private String combineParcelTrackingNo;

    private String platformBatchNo;

    private String sid;

    private String tid;

    private Long templateId;

    private String parcelTrackingNo;

    private String source;

    /**
     * tiktok 自寄仓库code
     */
    private String dropOffCode;

    public String getCombineParcelIds() {
        return combineParcelIds;
    }

    public void setCombineParcelIds(String combineParcelIds) {
        this.combineParcelIds = combineParcelIds;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getParcelIds() {
        return parcelIds;
    }

    public void setParcelIds(String parcelIds) {
        this.parcelIds = parcelIds;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public String getCombineParcelTrackingNo() {
        return combineParcelTrackingNo;
    }

    public void setCombineParcelTrackingNo(String combineParcelTrackingNo) {
        this.combineParcelTrackingNo = combineParcelTrackingNo;
    }

    public String getPlatformBatchNo() {
        return platformBatchNo;
    }

    public void setPlatformBatchNo(String platformBatchNo) {
        this.platformBatchNo = platformBatchNo;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getParcelTrackingNo() {
        return parcelTrackingNo;
    }

    public void setParcelTrackingNo(String parcelTrackingNo) {
        this.parcelTrackingNo = parcelTrackingNo;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDropOffCode() {
        return dropOffCode;
    }

    public void setDropOffCode(String dropOffCode) {
        this.dropOffCode = dropOffCode;
    }
}

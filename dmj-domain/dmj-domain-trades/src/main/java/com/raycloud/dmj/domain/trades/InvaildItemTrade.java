package com.raycloud.dmj.domain.trades;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_08_16 09:35
 */
public class InvaildItemTrade {

    private Long sid;
    private Long shortId;
    private String tid;
    private String sysStatus;
    private Long insufficientNum;
    private Double insufficientRate;
    private Long itemNum=null;
    private Long itemKindNum;
    private Long count;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sys_status) {
        this.sysStatus = sys_status;
    }

    public Long getInsufficientNum() {
        return insufficientNum;
    }

    public void setInsufficientNum(Long insufficientNum) {
        this.insufficientNum = insufficientNum;
    }

    public Double getInsufficientRate() {
        return insufficientRate;
    }

    public void setInsufficientRate(Double insufficientRate) {
        this.insufficientRate = insufficientRate;
    }

    public Long getItemNum() {
        return itemNum;
    }

    public void setItemNum(Long itemNum) {
        this.itemNum = itemNum;
    }

    public Long getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(Long itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }


    public InvaildItemTrade copyIfItemNumExist(Long newitemNum) {
        if (this.itemNum == null) {
            this.itemNum = newitemNum;
            return this;
        }
        InvaildItemTrade newInvalidItemTrade = new InvaildItemTrade();
        newInvalidItemTrade.sid = this.sid;
        newInvalidItemTrade.shortId = this.shortId;
        newInvalidItemTrade.tid = this.tid;
        newInvalidItemTrade.sysStatus = this.sysStatus;
        newInvalidItemTrade.insufficientNum = this.insufficientNum;
        newInvalidItemTrade.insufficientRate = this.insufficientRate;
        newInvalidItemTrade.itemNum = this.itemNum;
        newInvalidItemTrade.itemKindNum = this.itemKindNum;
        newInvalidItemTrade.count = this.count;
        newInvalidItemTrade.itemNum = newitemNum;
        return newInvalidItemTrade;
    }
}

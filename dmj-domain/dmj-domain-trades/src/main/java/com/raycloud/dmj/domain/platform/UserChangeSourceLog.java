package com.raycloud.dmj.domain.platform;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserChangeSourceLog implements Serializable {
    private static final long serialVersionUID = 5959521034154755887L;

    private Long id;

    private Long userId;

    private Long companyId;

    private String oldSource;

    private String newSource;

    /**
     * 配置生效时间
     */
    private Date changeTime;

    private Byte enableStatus;

    private Date created;

    private Date modified;
}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.Page;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_07_28 15:46
 */
@Table(name = "runtime_id_snapshot")
public class RuntimeIdSnapshot extends Model {

    public static final Integer TRADE_ORDER_DOWNLOAD_EXCEL = 1; //订单导出
    public static final Integer TRADE_ORDER_INFO_DOWNLOAD_EXCEL = 2; //订单详情导出
    public static final int OUT_ID_BATCH_SIZE=500;

    private static final int FILE_MAX_PAGE = 200;//将每个excel的大小调到10万
//    private static final int FILE_MAX_PAGE = 2;//将每个excel的大小调到1千，给测试用
    private Long id;
    private Integer type;
    private Long companyId;
    private Long staffId;
    private Long startTimeMillis;
    private boolean multiSnapshot=false;
    private int currentSnapshot=0;
    private boolean batch=false;
    private boolean finalBatch = false;

    /**
     * 表中没有下面四个字段，
     * useLastTimeSequence：是否使用sequence过滤，用来取代limit
     * lastTimeSequence: 上一次的最大sequence
     * currentSequence: 本次查询的最大sequence
     * tradeCount: 本次导出的总trade数。
     * */

    public static final int STATUS_INIT = 0;
    public static final int LOSE_PAGE_EXCEPTION = 1;
    public static final int STATIS_SNAPSHOTFINISH = 2;
    private long tradeCount = 0L;
    private volatile int status=STATUS_INIT;
    private int pageCount=0;
    private int pageSize=0;
    private int fileCount;
    private int currentFile = 1;
    private volatile AtomicInteger currentPagePoint =new AtomicInteger(1);
    private SnapshotWorker worker;
    private boolean needCutFile=false;

    private volatile BitSet bitSet = new BitSet();
    private ArrayList<Integer> losePage = new ArrayList<Integer>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getStartTime() {
        return startTimeMillis;
    }

    public void setStartTime(Long startTimeMillis) {
        this.startTimeMillis = startTimeMillis;
    }

    public long getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Long tradeCount) {
        if (!multiSnapshot){
            this.tradeCount +=tradeCount;
        }else {
            this.tradeCount = tradeCount;
        }
    }

    @Transient
    @JSONField(serialize = false)
    public Page nextPage() {
        if (needCutFile){
            needCutFile = false;
            currentFile++;
            return null;
        }
        int currentPage = currentPagePoint.getAndIncrement();//当前页从1开始
        if (currentPage<=pageCount) {//当前页<=最大页面则进入判断
            int i = currentPage - 1;
            long start = System.currentTimeMillis();
            while (!bitSet.get(i)) {
                if (System.currentTimeMillis()-start>=1000*60*5) {//5分钟等待时间
                    status = LOSE_PAGE_EXCEPTION;
                    worker.interruptedWorker();
                    return null;
                }
                if (status==STATIS_SNAPSHOTFINISH&&currentPage>pageCount){
                    worker.interruptedWorker();
                    return null;
                }
            }
            if (currentPage%FILE_MAX_PAGE==0){
                needCutFile = true;//下一次返回为空，切断
            }
            Page page = new Page();
            page.setPageNo(currentPage);
            page.setPageSize(pageSize);
            return page;
        }else {
            worker.interruptedWorker();
            return null;
        }
    }

    public BitSet getBitSet(){
        return bitSet;
    }

    public int getCurrentPage(){
        int i = currentPagePoint.get();
        return i;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void finishPage(int pageNo){
        bitSet.set(pageNo,true);
    }

    public void finishSnapshot() {
        if (status==STATUS_INIT){
            status = STATIS_SNAPSHOTFINISH;
        }
        for (int i = 0; i < pageCount; i++) {
            if (!bitSet.get(i)) {
                losePage.add(i);
                status = LOSE_PAGE_EXCEPTION;
            }
        }
    }

    public ArrayList<Integer> getLosepage(){
        return losePage;
    }

    //反正以最后一次为准。
    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
        this.fileCount = this.pageCount / FILE_MAX_PAGE+1;
        if (this.pageCount%FILE_MAX_PAGE==0) {
            this.fileCount--;
        }
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public void setSnapShotWorker(SnapshotWorker worker){this.worker = worker;}

    public SnapshotWorker getSnapshotWorker(){
        return this.worker;
    }

    public int getFileCount() {
        return fileCount;
    }

    public int getCurrentFile() {
        return currentFile;
    }

    public String getFileSuffix(){
        return "("+currentFile+"-"+fileCount+")";
    }

    public int getWorkNo() {
        return currentSnapshot;
    }

    public boolean isBatch() {
        return batch;
    }

    public void setBatch(boolean batch) {
        this.batch = batch;
    }

    public boolean finalBatch() {
        return finalBatch;
    }

    public void setFinalBatch(boolean finalBatch){
        this.finalBatch = finalBatch;
    }

    public interface SnapshotWorker<T>{
        //通知快照建立者关闭资源。
        void interruptedWorker();

        void addId(T t);
    }
}

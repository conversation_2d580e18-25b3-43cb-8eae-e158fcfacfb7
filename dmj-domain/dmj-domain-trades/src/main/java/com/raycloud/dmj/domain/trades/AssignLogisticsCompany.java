package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Timestamp;

/**
 * 指定物流公司表
 */
@Table(name = "assign_logistics_company")
public class AssignLogisticsCompany extends Model {
    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 物流公司代号
     */
    private String logisticsCode;

    /**
     * 创建时间
     */
    private Timestamp created;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public Timestamp getCreated() {
        return created;
    }

    public void setCreated(Timestamp created) {
        this.created = created;
    }
}

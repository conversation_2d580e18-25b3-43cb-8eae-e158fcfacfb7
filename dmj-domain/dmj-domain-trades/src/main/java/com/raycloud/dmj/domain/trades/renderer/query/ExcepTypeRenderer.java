package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class ExcepTypeRenderer implements IFieldValueRenderer {

    /**
     * 是否过滤异常订单 0为非异常，1为异常，null为全部
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return "全部";
        }
        if (Objects.equals(originValue,0)) {
            return "非异常";
        }
        if (Objects.equals(originValue,1)) {
            return "异常";
        }
        return String.valueOf(originValue);
    }
}

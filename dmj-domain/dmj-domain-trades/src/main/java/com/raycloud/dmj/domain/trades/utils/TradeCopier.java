package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.trade.except.TradeExceptOldUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.domain.utils.CommonConstants;

/**
 * 订单复制工具,注意这里不会复制子订单
 * Created by CXW on 16/6/7.
 */
public class TradeCopier<S extends Trade, T extends Trade> implements AttrCopier<S, T> {

    @Override
    public T copy(S source, T target) {

        target.setEnableStatus(source.getEnableStatus());
        target.setCompanyId(source.getCompanyId());
        if(TradeUtils.isFxTrade(source)){
            target.setBelongType(source.getBelongType());
            target.setDestId(source.getDestId());
            target.setConvertType(source.getConvertType());
            target.setSourceId(source.getSourceId());
        } else if(CommonConstants.PLAT_FORM_TYPE_SHULIANTONG.equals(source.getSource())
                || CommonConstants.PLAT_FORM_TYPE_FX.equals(source.getSource())
                || CommonConstants.PLAT_FORM_TYPE_FXG_GX.equals(source.getSource())){
            target.setSourceId(source.getSourceId());
        }
        target.setSource(source.getSource());
        target.setUserId(source.getUserId());
        target.setTaobaoId(source.getTaobaoId());
        target.setSid(source.getSid());
        target.setTid(source.getTid());
        target.setMergeType(source.getMergeType());
        target.setMergeSid(source.getMergeSid());
        target.setSplitType(source.getSplitType());
        target.setSplitSid(source.getSplitSid());
        target.setStatus(source.getStatus());
        target.setSysStatus(source.getSysStatus());
        target.setStockStatus(source.getStockStatus());
        target.setTotalFee(source.getTotalFee());
        target.setPayment(source.getPayment());
        target.setPayAmount(source.getPayAmount());
        target.setAcPayment(source.getAcPayment());
        target.setPostFee(source.getPostFee());
        target.setAdjustFee(source.getAdjustFee());
        target.setDiscountFee(source.getDiscountFee());
        target.setWarehouseId(source.getWarehouseId());
        target.setWarehouseName(source.getWarehouseName());
        target.setReceiverAddress(source.getReceiverAddress());
        target.setAddressType(source.getAddressType());
        target.setReceiverCity(source.getReceiverCity());
        target.setReceiverDistrict(source.getReceiverDistrict());
        target.setReceiverStreet(source.getReceiverStreet());
        target.setReceiverMobile(source.getReceiverMobile());
        target.setMobileTail(source.getMobileTail());
        target.setReceiverName(source.getReceiverName());
        target.setReceiverPhone(source.getReceiverPhone());
        target.setReceiverState(source.getReceiverState());
        target.setReceiverZip(source.getReceiverZip());
        target.setSellerFlag(source.getSellerFlag());
        target.setSellerFlagString(source.getSellerFlagString());
        target.setSellerMemo(source.getSellerMemo());
        target.setSellerNick(source.getSellerNick());
        target.setBuyerNick(source.getBuyerNick());
        target.setOpenUid(source.getOpenUid());
        target.setBuyerMessage(source.getBuyerMessage());
        target.setSysMemo(source.getSysMemo());
        target.setAuditTime(source.getAuditTime());
        target.setExpressCompanyId(source.getExpressCompanyId());
        target.setTemplateId(source.getTemplateId());
        target.setTemplateName(source.getTemplateName());
        target.setTemplateType(source.getTemplateType());
        target.setLogisticsCompanyId(source.getLogisticsCompanyId());
        target.setCanDelivered(source.getCanDelivered());
        target.setOutSid(source.getOutSid());
        target.setExpressPrintTime(source.getExpressPrintTime());
        target.setDeliverPrintTime(source.getDeliverPrintTime());
        target.setAssemblyPrintTime(source.getAssemblyPrintTime());
        target.setIsPackage(source.getIsPackage());
        target.setIsWeigh(source.getIsWeigh());
        target.setWeight(source.getWeight());
        target.setActualPostFee(source.getActualPostFee());
        target.setConsignTime(source.getConsignTime());
        target.setPtConsignTime(source.getPtConsignTime());
        target.setCanConfirmSend(source.getCanConfirmSend());
        target.setCreated(source.getCreated());
        target.setPayTime(source.getPayTime());
        target.setModified(source.getModified());
        target.setUpdTime(source.getUpdTime());
        target.setEndTime(source.getEndTime());
        target.setCost(source.getCost());
        target.setSaleFee(source.getSaleFee());
        target.setType(source.getType());
        target.setIsCancel(source.getIsCancel());
        target.setIsExcep(source.getIsExcep());
        target.setIsHalt(source.getIsHalt());
        target.setIsUrgent(source.getIsUrgent());
        target.setIsRefund(source.getIsRefund());
        target.setIsPresell(source.getIsPresell());
        target.setInvoiceName(source.getInvoiceName());
        target.setInvoiceRemark(source.getInvoiceRemark());
        target.setInvoiceFormat(source.getInvoiceFormat());
        target.setNeedInvoice(source.getNeedInvoice());
        target.setBuyerTaxNo(source.getBuyerTaxNo());
        target.setScalping(source.getScalping());
        target.setVipPickNo(source.getVipPickNo());
        target.setVipStorageNo(source.getVipStorageNo());
        target.setAddressMd5(source.getAddressMd5());
        target.setForceUpdateAddress(source.getForceUpdateAddress());
        target.setTaxFee(source.getTaxFee());
        target.setSubSource(source.getSubSource());
        target.setReceiverCountry(source.getReceiverCountry());
        target.setTagIds(source.getTagIds());
        target.setExceptIds(source.getExceptIds());
        target.setManualMarkIds(source.getManualMarkIds());
        target.setIsUpload(source.getIsUpload());
        target.setExceptMemo(source.getExceptMemo());
        target.setPrintCount(source.getPrintCount());
        target.setFxSysStatus(source.getFxSysStatus());
        target.setV(source.getV());
        target.setTheoryPostFee(source.getTheoryPostFee());
        target.setTimeoutActionTime(source.getTimeoutActionTime());
        target.setIsJz(source.getIsJz());
        target.setSysConsigned(source.getSysConsigned());
        target.setTradeTypeMap(source.getTradeTypeMap());
        // 放在最后
        TradeBuilderUtils.tradeCopierNewExcept(target,source);
        return target;
    }
}

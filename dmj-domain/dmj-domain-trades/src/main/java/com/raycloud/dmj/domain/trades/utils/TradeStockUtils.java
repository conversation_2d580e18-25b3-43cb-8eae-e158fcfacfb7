package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.OrderPlatExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.except.enums.ExceptEnum;

import java.util.*;

/**
 * Created by CXW on 16/8/17.
 */
public class TradeStockUtils {

    /**
     * 创建调用库存系统的订单，只设置基本数据，不包括订单
     *
     * @param trade
     * @return
     */
    private static final Map<String, Integer> STOCK_STATUS_MAP = new HashMap<>();

    static {
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_UNALLOCATED, 1);
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_EMPTY, 3);
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_EXCEP, 5);
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_INSUFFICIENT, 7);
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_RELATION_MODIFIED, 9);
        STOCK_STATUS_MAP.put(Trade.STOCK_STATUS_NORMAL, 11);
    }

    public static Integer getStockStatus(String stockStatus) {
        return stockStatus != null ? STOCK_STATUS_MAP.get(stockStatus) : null;
    }

    public static Order createStockOrder(Staff staff, Order order) {
        Order o = OrderBuilderUtils.builderUpdateOrder(order);
        o.setCompanyId(order.getCompanyId());
        o.setId(order.getId());
        o.setSource(order.getSource());
        o.setOid(order.getOid());
        o.setSid(order.getSid());
        o.setTid(order.getTid());
        o.setUserId(order.getUserId());
        o.setStatus(order.getStatus());
        o.setSysStatus(order.getSysStatus());
        o.setOldSysStatus(order.getOldSysStatus());
        // o.setStockStatus(order.getStockStatus());
        OrderExceptUtils.setStockStatus(staff, o, order.getStockStatus());
        o.setStockNum(order.getStockNum());
        o.setNum(order.getNum());
        o.setOldNum(order.getOldNum());
        o.setItemSysId(order.getItemSysId());
        o.setSkuSysId(order.getSkuSysId());
        o.setNumIid(order.getNumIid());
        o.setSkuId(order.getSkuId());
        o.setWarehouseId(order.getWarehouseId());
        o.setCombineId(order.getCombineId());
        o.setType(order.getType());
        o.setSysOuterId(order.getSysOuterId());
        o.setIsVirtual(order.getIsVirtual());
        o.setInsufficientCanceled(order.getInsufficientCanceled());
        o.setSubStock(order.getSubStock());
        //奇门那边用到的数据
        o.setTitle(order.getTitle());
        o.setCreated(order.getCreated());
        o.setPayment(order.getPayment());
        o.setPrice(order.getPrice());
        o.setPayTime(order.getPayTime());
        //分销用到的数据
        o.setBelongType(order.getBelongType());
        o.setDestId(order.getDestId());
        o.setSourceId(order.getSourceId());
        o.setConvertType(order.getConvertType());
        return o;
    }

    /**
     * 计算套件子订单本身库存状态,非套件子订单不作任何处理
     *
     * @param order
     */
    public static void handleSuitOrderStock(Staff staff, Order order, boolean fillRealStockNum, TradeConfig tradeConfig) {
        TradeConfigContext context = TradeConfigContext.builder()
                .splitAutoAudit(tradeConfig.splitAutoAudit())
                .openScalpNotApplyStock(tradeConfig.getOpenScalpNotApplyStock())
                .openRefundNotApplyStock(tradeConfig.getOpenRefundNotApplyStock())
                .autoUnattainableNotApplyStock(tradeConfig.getAutoUnattainableNotApplyStock())
                .build();
        handleSuitOrderStock(staff, context, order, fillRealStockNum);
    }

    public static void handleSuitOrderStock(Staff staff, TradeConfigContext context, Order order, boolean fillRealStockNum) {
        if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
            return;
        }
        List<Order> suits = order.getSuits();
        if (suits != null && !suits.isEmpty()) {
            int minStockNum = getSuitStockNum(staff, order, suits);
            order.setOldStockNum(order.getStockNum());
            order.setOldStockStatus(order.getStockStatus());
            order.setStockNum(minStockNum);
            OrderExceptUtils.setStockStatus(staff, order, getStockStatus(staff, context, suits, true, fillRealStockNum));
        }
    }

    public static int getSuitStockNum(Staff staff, Order order, List<Order> suits) {
        int minStockNum = order.getNum();
        for (Order son : suits) {
            // KMERP-265558 跨境平台交易关闭的商品num可能会被置为0，导致算数异常
            if (Trade.SYS_STATUS_CLOSED.equals(son.getSysStatus())) {
                continue;
            }
            son.setExceptData(order.getExceptData());
            // 这里子商品继承父商品的ExceptData 对象，exceptData 是同一个对象
            OrderExceptUtils.updateExceptOrder(staff, son, ExceptEnum.RELATION_CHANGED, 0L);
            // son.setRelationChanged(0);
            int stockNum = (son.getStockNum() != null ? son.getStockNum() : 0) / (son.getNum() / order.getNum());
            if (minStockNum > stockNum) {
                minStockNum = stockNum;
            }
        }
        return minStockNum;
    }

    public static boolean resetTradeStockStatus(Staff staff, TradeConfigContext configContext, Trade trade, List<Order> orders, boolean handleAuditActiveStockRecord) {
        int[] a = new int[]{0, 0, 0, 0, 0, 0};
        int insufficientNum = 0;
        int validItemNum = 0;
        for (Order order : orders) {
            if (configContext.getOpenScalpNotApplyStock() == 1 && order.getScalping() == null) {
                order.setScalping(trade.getScalping());
            }
            UnattainableUilts.setOrderAutoUnattainable(staff, trade, order);
            insufficientNum += _handleStockStatus(staff, order, a, handleAuditActiveStockRecord, configContext);
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                validItemNum += order.getNum();
            }
        }
        trade.setValidItemNum(validItemNum);
        trade.setInsufficientNum(insufficientNum);
        trade.setInsufficientRate(validItemNum > 0 ? Double.parseDouble(String.format("%.2f", insufficientNum * 100.0D / validItemNum)) : 0D);
        if (trade.getOldStockStatus() == null) {
            trade.setOldStockStatus(trade.getStockStatus());
        }
        String stockStatus = _getStockStatus(a, false);
        TradeExceptUtils.setStockStatus(staff, trade, stockStatus);
        return a[0] > 0;
    }


    public static boolean resetTradeStockStatus(Staff staff, Trade trade, TradeConfig tradeConfig) {
        return resetTradeStockStatus(staff, trade, TradeUtils.getOrders4Trade(trade), true, tradeConfig);
    }

    @Deprecated
    public static boolean resetTradeStockStatus(Staff staff, Trade trade, List<Order> orders, boolean handleAuditActiveStockRecord, TradeConfig tradeConfig) {
        return resetTradeStockStatus(staff, trade, orders, handleAuditActiveStockRecord,
                tradeConfig.getOpenScalpNotApplyStock(),
                tradeConfig.splitAutoAudit(),
                tradeConfig.getOpenRefundNotApplyStock(),
                tradeConfig.getAutoUnattainableNotApplyStock()
        );
    }

    @Deprecated
    public static boolean resetTradeStockStatus(Staff staff, Trade trade, List<Order> orders,
                                                boolean handleAuditActiveStockRecord,
                                                int openScalpNotApplyStock,
                                                int splitAutoAudit,
                                                int openRefundNotApplyStock,
                                                int autoUnattainableNotApplyStock) {
        return resetTradeStockStatus(staff, trade, orders, handleAuditActiveStockRecord, TradeConfigContext.builder()
                .openScalpNotApplyStock(openScalpNotApplyStock)
                .splitAutoAudit(splitAutoAudit)
                .openRefundNotApplyStock(openRefundNotApplyStock)
                .autoUnattainableNotApplyStock(autoUnattainableNotApplyStock)
                .build());
    }

    public static boolean resetTradeStockStatus(Staff staff, Trade trade, List<Order> orders, boolean handleAuditActiveStockRecord, TradeConfigContext context) {
        int[] a = new int[]{0, 0, 0, 0, 0, 0};
        int insufficientNum = 0;
        int validItemNum = 0;
        for (Order order : orders) {
            if (context.getOpenScalpNotApplyStock() == 1 && order.getScalping() == null) {
                order.setScalping(trade.getScalping());
            }
            UnattainableUilts.setOrderAutoUnattainable(staff, trade, order);
            insufficientNum += _handleStockStatus(staff, order, a, handleAuditActiveStockRecord, context);
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                validItemNum += order.getNum();
            }
        }
        trade.setValidItemNum(validItemNum);
        trade.setInsufficientNum(insufficientNum);
        trade.setInsufficientRate(validItemNum > 0 ? Double.parseDouble(String.format("%.2f", insufficientNum * 100.0D / validItemNum)) : 0D);
        if (trade.getOldStockStatus() == null) {
            trade.setOldStockStatus(trade.getStockStatus());
        }
        String stockStatus = _getStockStatus(a, false);
        TradeExceptUtils.setStockStatus(staff, trade, stockStatus);
        return a[0] > 0;
    }

    public static String getStockStatus(Staff staff, List<Order> orders, boolean needEmptyOrExcep, boolean fillRealStockNum) {
        int[] a = new int[]{0, 0, 0, 0, 0, 0};
        TradeConfigContext context = TradeConfigContext.builder()
                .openScalpNotApplyStock(0)
                .splitAutoAudit(0)
                .openRefundNotApplyStock(0)
                .autoUnattainableNotApplyStock(0)
                .build();
        for (Order order : orders) {
            _handleStockStatus(staff, order, a, !fillRealStockNum, context);
        }
        return _getStockStatus(a, needEmptyOrExcep);
    }

    public static String getStockStatus(Staff staff, List<Order> orders, boolean needEmptyOrExcep, boolean fillRealStockNum, TradeConfig tradeConfig) {
        TradeConfigContext context = TradeConfigContext.builder()
                .splitAutoAudit(tradeConfig.splitAutoAudit())
                .openScalpNotApplyStock(tradeConfig.getOpenScalpNotApplyStock())
                .openRefundNotApplyStock(tradeConfig.getOpenRefundNotApplyStock())
                .autoUnattainableNotApplyStock(tradeConfig.getAutoUnattainableNotApplyStock())
                .build();
        return getStockStatus(staff, context, orders, needEmptyOrExcep, fillRealStockNum);
    }

    public static String getStockStatus(Staff staff, TradeConfigContext context, List<Order> orders, boolean needEmptyOrExcep, boolean fillRealStockNum) {
        int[] a = new int[]{0, 0, 0, 0, 0, 0};
        for (Order order : orders) {
            _handleStockStatus(staff, order, a, !fillRealStockNum, context);
        }
        return _getStockStatus(a, needEmptyOrExcep);
    }

    /**
     * 比较两个库存状态的权重值
     */
    public static int compareStockStatus(String stockStatus1, String stockStatus2) {
        return stockStatus2Weight(stockStatus1) - stockStatus2Weight(stockStatus2);
    }

    /**
     * 库存状态对应的整数值
     */
    private static int stockStatus2Weight(String stockStatus) {
        if (Trade.STOCK_STATUS_RELATION_MODIFIED.equals(stockStatus)) {
            return TradeConstants.WEIGHT_STOCK_RELATION_MODIFIED;
        } else if (Trade.STOCK_STATUS_UNALLOCATED.equals(stockStatus)) {
            return TradeConstants.WEIGHT_STOCK_UNALLOCATED;
        } else if (Trade.STOCK_STATUS_EMPTY.equals(stockStatus)) {
            return TradeConstants.WEIGHT_STOCK_EMPTY;
        } else if (Trade.STOCK_STATUS_INSUFFICIENT.equals(stockStatus)) {
            return TradeConstants.WEIGHT_STOCK_INSUFFICIENT;
        } else if (Trade.STOCK_STATUS_NORMAL.equals(stockStatus)) {
            return TradeConstants.WEIGHT_STOCK_NORMAL;
        } else {
            return 10000;
        }
    }

    public static String getStockStatusStr(long stockNum, long applyNum) {
        return applyNum - stockNum == 0 ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_INSUFFICIENT;
    }

    private static int _handleStockStatus(Staff staff, Order order, int[] a, boolean handleAuditActiveStockRecord, TradeConfigContext context) {
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) || (order.getEnableStatus() != null && order.getEnableStatus() == 0)) {
            return 0;
        }
        //如果是后置锁定，且商品已匹配，且订单状态为待审核/待财神
        if (order.getKeepInsufficientStockStatus()) {
            if (context.getSplitAutoAudit() == 0) {
                order.setStockNum(0);
            }
            OrderExceptUtils.setStockStatus(staff, order, getStockStatusStr(order.getNum(), order.getStockNum()));
        } else if (handleAuditActiveStockRecord && staff.openAuditActiveStockRecord()
                && !Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus())
                && !OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)
                && (Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(order.getSysStatus()))) {
            // 默认0  走原来的逻辑
            if (order.getPreSellLockStock() == 0) {
                order.setStockNum(order.getNum());
                OrderExceptUtils.setStockStatus(staff, order, getStockStatusStr(order.getNum(), order.getStockNum()));
            }
        } else if (order.getStockNum() != null && Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
            OrderExceptUtils.setStockStatus(staff, order, getStockStatusStr(order.getNum(), order.getStockNum()));
        } else if (order.getType() != null && (order.getType() == Order.TypeOfGroupOrder && order.getType() == Order.TypeOfProcessOrder) && order.getCombineId() > 0) {
            order.setStockNum(order.getNum());
            OrderExceptUtils.setStockStatus(staff, order, getStockStatusStr(order.getNum(), order.getStockNum()));
        }

        needNotApplyStock(staff, context, order);
        if (!OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
            OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.UNALLOCATED, 0L);
        }
        if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)) {//商品对应关系改动, 这个需要优先计算
            a[4]++;
            if (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {//缺货
                a[2]++;
            }
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        } else if (Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus())) {
            a[0]++;
            return order.getNum();
        } else if (Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
            a[1]++;
            if (!isEqualStockNum(order)) return order.getDiffStockNum();
        } else if (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
            if (order.getInsufficientCanceled() != null && order.getInsufficientCanceled() - 1 == 0) {//如果取消了缺货异常，算作正常
                a[1]++;
            } else {
                a[2]++;
            }
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        } else if (OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
            a[3]++;
            return order.getNum();
        } else {
            a[5]++;
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        }
        return 0;
    }

    /**
     * 新的方法
     * com.raycloud.dmj.domain.trades.utils.TradeStockApplyUtils#needNotApplyStock(com.raycloud.dmj.domain.trades.Order, java.lang.Integer, java.lang.Integer, java.lang.Integer)
     */
    @Deprecated
    public static boolean needNotApplyStock(Staff staff, Order order, TradeConfig tradeConfig) {
        if (tradeConfig != null) {
            return needNotApplyStock(staff, order, tradeConfig.getOpenScalpNotApplyStock(), tradeConfig.getOpenRefundNotApplyStock(), tradeConfig.getAutoUnattainableNotApplyStock());
        }
        boolean notApplyStock = !OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)
                && (order.isVirtual()
                || order.ifNonConsign() || OrderUtils.isFxOrMixOrder(order)
                || OrderUtils.isPlatformFxOrder(order)
                || OrderUtils.isAlibabaFxRoleOrder(order)
                || OrderUtils.hasFblOrder(order)
        );
        //组合装、加工商品 的单品不申请库存
        if (!notApplyStock) {
            notApplyStock = order.getType() != null && order.getCombineId() != null && order.getCombineId() > 0 && (order.getType() == Order.TypeOfGroupOrder || order.getType() == Order.TypeOfProcessOrder);
        }
        if (notApplyStock) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return notApplyStock;
            }
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setStockNum(order.getNum());
        }
        return notApplyStock;
    }

    public static boolean needNotApplyStock(Staff staff, Order order, int openScalpNotApplyStock, int openRefundNotApplyStock, int autoUnattainableNotApplyStock) {
        return needNotApplyStock(staff, TradeConfigContext.builder()
                        .openScalpNotApplyStock(openScalpNotApplyStock)
                        .openRefundNotApplyStock(openRefundNotApplyStock)
                        .autoUnattainableNotApplyStock(autoUnattainableNotApplyStock)
                        .build(),
                order);
    }

    public static boolean needNotApplyStock(Staff staff, TradeConfigContext context, Order order) {
        boolean notApplyStock = !OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)
                && (order.isVirtual()
                || order.ifNonConsign()
                || OrderUtils.isFxOrMixOrder(order)
                || OrderUtils.isPlatformFxOrder(order)
                || OrderUtils.isAlibabaFxRoleOrder(order)
                || scalpNotApplyStock(order, context.getOpenScalpNotApplyStock())
                || refundNotApplyStock(order, context.getOpenRefundNotApplyStock())
                || UnattainableUilts.isAutoMatchUnattainableOrder(order, context.getAutoUnattainableNotApplyStock())
                || OrderUtils.hasFblOrder(order)
        );
        //组合装、加工商品 的单品不申请库存
        if (!notApplyStock) {
            notApplyStock = order.getType() != null && order.getCombineId() != null && order.getCombineId() > 0 && (order.getType() == Order.TypeOfGroupOrder || order.getType() == Order.TypeOfProcessOrder);
        }
        if (notApplyStock) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return notApplyStock;
            }
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setStockNum(order.getNum());
        }
        return notApplyStock;
    }

    /**
     * 是否需要申请库存
     */
    public static boolean needApplyStock(TradeConfigContext context, Order order) {
        boolean needApplyStock = needApplyGroupOrProcessSuitStock(context, order)
                && ((order.getType() != Order.TypeOfGroupOrder && order.getType() != Order.TypeOfProcessOrder) || order.getCombineId() <= 0)//不是 组合装或者加工商品下面的单品
                ;
        //这里不计算库存异常，异常相关的在各自业务中去算
        if (!needApplyStock) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return needApplyStock;
            }
            order.setStockNum(order.getNum());
        }
        return needApplyStock;
    }

    /**
     * 后置审核时，组合装/加工商品的单品是否需要申请库存
     */
    public static boolean needApplyGroupOrProcessSuitStock(TradeConfigContext context, Order order) {
        return order.getItemSysId() > 0 //商品是匹配的
                && !order.isVirtual() //不是 虚拟商品
                && !order.ifNonConsign() // 不是 无需发货
                && !OrderUtils.isFxOrMixOrder(order) // 不是 分销或分销供销订单
                && !OrderUtils.isPlatformFxOrder(order) // 不是 平台分销订单
                && !OrderUtils.isAlibabaFxRoleOrder(order) // 不是 1688分销订单
                && (order.getScalping() != 1 || context.getOpenScalpNotApplyStock() != 1) // 非空包订单 或者 未开启空包订单不锁定库存
                && (!RefundUtils.isRefundOrder(order) || context.getOpenRefundNotApplyStock() != 1) // 非退款中订单 或者 未开启退款中商品不占用库存
                && (order.getAutoUnattainable() != 1 || context.getAutoUnattainableNotApplyStock() != 1) // 非快递异常订单 或者 未开启快递异常不锁库存
                && !OrderUtils.hasFblOrder(order);
    }

    private static boolean scalpNotApplyStock(Order order, int openScalpNotApplyStock) {
        return order.getScalping() != null && order.getScalping() == 1 && openScalpNotApplyStock == 1;
    }

    private static boolean refundNotApplyStock(Order order, int openRefundNotApplyStock) {
        return RefundUtils.isRefundOrder(order) && openRefundNotApplyStock == 1;
    }

    private static String _getStockStatus(int[] a, boolean needEmptyOrExcep) {
        if (needEmptyOrExcep) {
            if (a[0] > 0) {
                return Trade.STOCK_STATUS_EMPTY;
            } else if (a[5] > 0) {
                return Trade.STOCK_STATUS_EXCEP;
            }
        }
        if (a[4] > 0) {
            return Trade.STOCK_STATUS_RELATION_MODIFIED;
        } else if (a[3] > 0) {
            return Trade.STOCK_STATUS_UNALLOCATED;
        } else if (a[2] > 0) {
            return Trade.STOCK_STATUS_INSUFFICIENT;
        } else if (a[0] > 0 || a[5] > 0) {
            return Trade.STOCK_STATUS_EXCEP;
        } else {
            return Trade.STOCK_STATUS_NORMAL;
        }
    }

    @Deprecated
    public static void handleVirtualItemStock(Staff staff, Order order) {
        if (order.isVirtual() || order.ifNonConsign() || OrderUtils.isFxOrMixOrder(order)) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return;
            }
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setStockNum(order.getNum());
        }
    }

    private static boolean isEqualStockNum(Order order) {
        if (order.getStockNum() == null || order.getDiffStockNum() == null) return true;
        int diffStockNum = order.getNum() - order.getStockNum();
        if (diffStockNum == order.getDiffStockNum()) {
            return true;
        }
        return false;
    }

    public static boolean checkIsCancelAndIsScalpingAndIsPresell(Staff staff, Trade trade) {
        return checkIsCancel(staff, trade) || checkIsScalping(staff, trade) || checkIsPresell(staff, trade);
    }

    /**
     * 作废订单
     */
    public static boolean checkIsCancel(Staff staff, Trade trade) {
        return trade.getIsCancel() != null && trade.getIsCancel() == 1;
    }

    /**
     * 刷单订单
     */
    public static boolean checkIsScalping(Staff staff, Trade trade) {
        return trade.getScalping() != null && trade.getScalping() == 1;
    }

    /**
     * 预售订单
     *
     * @param staff
     * @param trade
     * @return
     */
    public static boolean checkIsPresell(Staff staff, Trade trade) {
        return trade.isPresellTrade();
    }

    public static boolean checkIsPresellAndIsUnallocatedAndIsUnallocated(Staff staff, Order order) {
        return checkIsPresell(staff, order) || checkIsUnallocated(staff, order) || checkIsFxOrder(staff, order);
    }

    public static boolean checkIsPresell(Staff staff, Order order) {
        return order.getIsPresell() != null && order.isPresellOrder();
    }

    public static boolean checkIsFxOrder(Staff staff, Order order) {
        return OrderUtils.isFxOrMixOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order);
    }

    public static boolean checkIsUnallocated(Staff staff, Order order) {
        return order.getItemSysId() == null || order.getItemSysId() <= 1;
    }

    public static void resetTradeStockStatus(Staff staff, List<Trade> trades, TradeConfig tradeConfig) {
        for (Trade trade : trades) {
            resetTradeStockStatus(staff, trade, TradeUtils.getOrders4Trade(trade), true, tradeConfig);
        }
    }

}

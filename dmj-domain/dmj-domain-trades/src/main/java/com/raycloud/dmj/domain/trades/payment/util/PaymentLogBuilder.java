package com.raycloud.dmj.domain.trades.payment.util;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-17
 */
public class PaymentLogBuilder extends AbsLogBuilder<PaymentLogBuilder>{

    @Override
    protected String getBusinessSign() {
        return LogBusinessEnum.PAYMENT.getSign();
    }

    public PaymentLogBuilder(Staff staff) {
        super(staff);
    }


    public PaymentLogBuilder(Long companyId) {
        super(companyId);
    }

    public PaymentLogBuilder(Long companyId,Long sid) {
        super(companyId);
        appendHead("sid:"+sid);
    }

    public PaymentLogBuilder(Long companyId,String tid) {
        super(companyId);
        appendHead("tid:"+tid);
    }


    public PaymentLogBuilder(Long companyId,Long staffId,String staffName) {
        super(companyId,staffId,staffName);
    }

    public PaymentLogBuilder(User user) {
        super(user);
    }

    public PaymentLogBuilder(Trade trade) {
        super(trade.getCompanyId());
        if (Objects.nonNull(trade.getSid())) {
            appendHead("sid:"+trade.getSid());
        }else{
            appendHead("tid:"+trade.getTid());
        }
    }

    public PaymentLogBuilder(Staff staff,Long sid) {
        super(staff);
        appendHead("sid:"+sid);
    }

    public PaymentLogBuilder(User user,Long sid) {
        super(user);
        appendHead("sid:"+sid);
    }

    public PaymentLogBuilder appendSid(Long sid){
        append("[sid:").append(sid).append("]");
        return this;
    }

    public PaymentLogBuilder appendTradeIdentify(Trade trade){
        this.append("sid",trade.getSid());
        return this;
    }

    public PaymentLogBuilder appendOrderIdentify(Order order){
        if (StringUtils.isNotBlank(order.getSysOuterId())) {
            this.append(" 商家编码",order.getSysOuterId());
        }else {
            this.append(" 平台商家编码",StringUtils.isNotBlank(order.getOuterSkuId())?order.getOuterSkuId():order.getOuterIid());
        }this.append("oid",order.getOid() == null? order.getSoid():order.getOid());
        return this;
    }

    public PaymentLogBuilder appendChanged(String key,Object orign,Object news){
        append(key).append(":").append(orign).append("->").append(news).append(",");
        return this;
    }

    public PaymentLogBuilder appendSource(String key,Object value,Object source){
        append(key).append(":").append(value).append("=").append(source).append(",");
        return this;
    }


    public PaymentLogBuilder add() {
        delLastComma();
        append(" + ");
        return this;
    }

    public PaymentLogBuilder sub() {
        delLastComma();
        append(" - ");
        return this;
    }

    public PaymentLogBuilder muti() {
        delLastComma();
        append(" * ");
        return this;
    }

    public PaymentLogBuilder div() {
        delLastComma();
        append(" / ");
        return this;
    }

    public PaymentLogBuilder eq() {
        delLastComma();
        append(" = ");
        return this;
    }

    public static boolean isPrintMoneyDetailLog(Long companyId){
        IPaymentSwitcher instance = IPaymentSwitcher.getInstance();
        if (instance == null) {
            return false;
        }
        return instance.isPrintMoneyDetailLog(companyId);
    }

}

package com.raycloud.dmj.domain.trades.search.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.trades.SortConfig;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeControllerParams;
import com.raycloud.dmj.domain.trades.TradeQueryItemTagIdsParams;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.TradeTypeNewParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.raycloud.dmj.Strings.getAsDouble;
import static com.raycloud.dmj.Strings.getAsIntArray;
import static com.raycloud.dmj.Strings.getAsLongArray;
import static com.raycloud.dmj.Strings.getAsLongList;
import static com.raycloud.dmj.Strings.getAsStringArray;
import static com.raycloud.dmj.Strings.getAsStringList;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-04-28
 */
public class ControllerParamConverter {

    /**
     * 将前端用户输入的参数转化为TradeQueryParams对象
     *
     * @param params
     */
    public static List<TradeQueryParams> copyParamsBatch(List<TradeControllerParams> params){
        if (CollectionUtils.isEmpty(params))
            return null;
        List<TradeQueryParams> result = new ArrayList<TradeQueryParams>();
        for (TradeControllerParams param : params) {
            param.setIfCustom(true);
            result.add(copyParams(param));
        }
        return result;

    }

    /**
     * 将前端用户输入的参数转化为TradeQueryParams对象
     *
     * @param params
     */
    public static TradeQueryParams copyParams(TradeControllerParams params) {
        TradeQueryParams query = new TradeQueryParams();
        query.setId(params.getCustomId());
        query.setQueryId(params.getQueryId());
        query.setDirectQueryType(params.getDirectQueryType());
        query.setUserIds(getAsLongArray(params.getUserId(), ",", true));
        query.getContext().setOrignUserIds(query.getUserIds());
        query.setAllowUnPrintPack(params.getAllowUnPrintPack());
        query.setSourceIds(getAsLongArray(params.getSourceId(), ",", true));
        query.setDestIds(getAsLongArray(params.getDestId(), ",", true));

        query.setWarehouseIds(getAsLongArray(params.getWarehouseId(), ",", true));
        query.setSortOrder(params.getSortOrder());

        query.setPageSelect(StringUtils.isNotBlank(params.getSids()));
        query.setSid(getAsLongArray(StringUtils.isNotBlank(params.getSid()) ? params.getSid() : params.getSids(), ",", true));
        query.setShortId(getAsLongArray(params.getShortId(), ",", true));
        query.setTid(getAsStringArray(params.getTid(), ",", true));
        if(params.getBatch()!=null && params.getBatch()==1){
            query.setOutSids(getAsStringArray(params.getOutSid(), "\n", true));
        }else {
            query.setOutSids(getAsStringArray(params.getOutSid(), ",", true));
        }
        query.setMixKey(params.getMixKey());
        query.setMixMemo(params.getMixMemo());
        query.setUniqueCodes(getAsStringList(params.getUniqueCodes(), ",", true));

        query.setTimeType(params.getTimeType());
        query.setIsCancel(params.getIsCancel());
        query.setStartTime(params.getStartTime() != null ? new Date(params.getStartTime()) : null);
        query.setEndTime(params.getEndTime() != null ? new Date(params.getEndTime()) : null);
        query.setLogisticsWarningStartTime(params.getLogisticsWarningStartTime() != null ? new Date(params.getLogisticsWarningStartTime()) : null);
        query.setLogisticsWarningEndTime(params.getLogisticsWarningEndTime() != null ? new Date(params.getLogisticsWarningEndTime()) : null);

        query.setReceiverArea(params.getReceiverArea());
        query.setAddressType(params.getAddressType());
        query.setItemCountStart(params.getItemCountStart());
        query.setItemCountEnd(params.getItemCountEnd());
        query.setItemKindStart(params.getItemKindStart());
        query.setItemKindEnd(params.getItemKindEnd());
        query.setNetWeightStart(getAsDouble(params.getNetWeightStart(), true));
        query.setNetWeightEnd(getAsDouble(params.getNetWeightEnd(), true));
        query.setWeightStart(getAsDouble(params.getWeightStart(), true));
        query.setWeightEnd(getAsDouble(params.getWeightEnd(), true));
        query.setExcludeOrderTypes(Strings.getAsStringArray(params.getExcludeOrderTypes(), ",", true));

        query.setPaymentUpperLimit(getAsDouble(params.getPaymentUpperLimit(), true));
        query.setPaymentLowerLimit(getAsDouble(params.getPaymentLowerLimit(), true));
        query.setCostUpperLimit(getAsDouble(params.getCostUpperLimit(), true));
        query.setCostLowerLimit(getAsDouble(params.getCostLowerLimit(), true));
        query.setDiscountRateUpperLimit(getAsDouble(params.getDiscountRateUpperLimit(), true));
        query.setDiscountRateLowerLimit(getAsDouble(params.getDiscountRateLowerLimit(), true));
        query.setPostFeeUpperLimit(getAsDouble(params.getPostFeeUpperLimit(), true));
        query.setPostFeeLowerLimit(getAsDouble(params.getPostFeeLowerLimit(), true));
        query.setDiscountFeeLowerLimit(getAsDouble(params.getDiscountFeeLowerLimit(), true));
        query.setDiscountFeeUpperLimit(getAsDouble(params.getDiscountFeeUpperLimit(), true));
        query.setTheoryPostFeeUpperLimit(getAsDouble(params.getTheoryPostFeeUpperLimit(), true));
        query.setTheoryPostFeeLowerLimit(getAsDouble(params.getTheoryPostFeeLowerLimit(), true));

        query.setInsufficientNumStart(params.getInsufficientNumStart());
        query.setInsufficientNumEnd(params.getInsufficientNumEnd());
        query.setInsufficientRate(Strings.getAsIntList(params.getInsufficientRate(), ",", true));

        query.setBuyerNick(getAsStringArray(params.getBuyerNick(), ",", true));
        query.setOpenUid(getAsStringArray(params.getOpenUid(), ",", true));
        query.setCustomerNick(getAsStringArray(params.getCustomerNick(), ",", true));
        query.setSellerFlags(getAsIntArray(params.getSellerFlag(), ",", true));
        query.setContainMemo(params.getContainMemo());
        query.setMemoKeyWord(params.getMemoKeyWord());
        query.setSellerMemoUpdate(params.getSellerMemoUpdate());

        query.setTradeType(params.getTradeType());
        query.setExcludeTradeType(params.getExcludeTradeType());
        query.setType(params.getType());
        query.setTradeTypeNewParams(params.getTradeTypeNewParamsStr() == null || params.getTradeTypeNewParamsStr().isEmpty() ?
                null : JSONObject.parseObject(params.getTradeTypeNewParamsStr(), TradeTypeNewParams.class));
        query.setContainOrExclude(params.getContainOrExclude());
        query.setSysStatus(getAsStringArray(params.getSysStatus(), ",", true));
        query.getContext().setOrignSysStatus(query.getSysStatus());
        query.setO2oSysStatus(getAsStringArray(params.getO2oSysStatus(), ",", true));
        query.setStatusList(getAsStringArray(params.getStatus(), ",", true));
        query.setPlatformStatus(getAsStringArray(params.getPlatformStatus(), ",", true));
        query.setExceptionStatus(getAsStringArray(params.getExceptionStatus(), ",", true));
        query.setExcludeExceptionStatus(getAsStringArray(params.getExcludeExceptionStatus(), ",", true));
        query.setExcludeExceptIds(getAsStringArray(params.getExcludeExceptIds(), ",", true));

        query.setQueryType(params.getQueryType());
        query.setSkuProp(params.getSkuProp());
        query.setPlatSkuProp(params.getPlatSkuProp());
        query.setItemOrder(params.getItemOrder());
        query.setExcludeSysOuterIds(Strings.getAsStringArray(params.getExcludeSysOuterIds(), ",", true));

        query.setConsignType(params.getConsignType());
        query.setExpress(getAsStringArray(params.getExpress(), ",", true));
        query.setCanDelivered(params.getCanDelivered());
        query.setHasInvoice(params.getHasInvoice());
        query.setKey(params.getKey()).setText(params.getText());
        query.setItemType(params.getItemType());
        query.setCid(params.getCid());
        query.setFields(transeform2DbFields(params.getSelectionFields()));
        query.setTagIds(getAsStringArray(params.getTagIds(), ",", true));
        query.setOnlyTagIds(getAsStringArray(params.getOnlyTagIds(), ",", true));
        query.setTaobaoIds(getAsStringArray(params.getTaobaoIds(), ",", true));
        query.setExcludeTagIds(getAsStringArray(params.getExcludeTagIds(), ",", true));
        query.setExceptIds(getAsStringArray(params.getExceptIds(), ",", true));
        query.setInWave(params.getInWave());
        query.setWaveId(params.getWaveId());
        query.setWaveShortId(params.getWaveShortId());
        query.setTradeFrom(getAsStringArray(params.getTradeFrom(), ",", true));
        query.setExpressStatus(params.getExpressStatus());
        query.setDeliverStatus(params.getDeliverStatus());
        query.setExcludeSellerFlags(getAsIntArray(params.getExcludeSellerFlags(), ",", true));
        query.setIsSysConsigned(params.getIsSysConsigned());
        query.setLogisticsExceptType(params.getLogisticsExceptType());
        query.setNeedTracking(params.getNeedTracking());
        query.setUniqueCodeSplitQuery(params.getUniqueCodeSplitQuery());
        String key = params.getKey() != null ? params.getKey().trim() : null;
        String text = params.getText() != null ? params.getText().trim() : null;

        processKeyQuery(query, key, text);
        if(!(Objects.equals(query.getItemType(),5) && "outerId".equals(key))){
            query.setSysOuterIds(Strings.getAsStringArray(params.getOuterId(), ",", true));
        }
        query.setSkuOuterId(params.getSkuOuterId());
        query.setOuterIdAndSysItemIds(Strings.getAsStringArray(params.getOuterIdAndSysItemIds(), ",", true));
        query.setOuterIdAndSysSkuIds(Strings.getAsStringArray(params.getOuterIdAndSysSkuIds(), ",", true));
        query.setExcludeOuterIdAndSysItemIds(Strings.getAsStringArray(params.getExcludeOuterIdAndSysItemIds(), ",", true));
        query.setExcludeOuterIdAndSysSkuIds(Strings.getAsStringArray(params.getExcludeOuterIdAndSysSkuIds(), ",", true));
        query.setOnlyOuterIdAndSysItemIds(Strings.getAsStringArray(params.getOnlyOuterIdAndSysItemIds(), ",", true));
        query.setOnlyOuterIdAndSysSkuIds(Strings.getAsStringArray(params.getOnlyOuterIdAndSysSkuIds(), ",",true));
        query.setOnlyOuterIdAndSysSkuIdType(params.getOnlyOuterIdAndSysSkuIdType());

        if (StringUtils.isNotBlank(params.getBuyerMessage())) {
            query.setBuyerMessage(params.getBuyerMessage());
        }
        if (StringUtils.isNotBlank(params.getSellerMemo())) {
            query.setSellerMemo(params.getSellerMemo());
        }
        if (StringUtils.isNotBlank(params.getSysMemo())) {
            query.setSysMemo(params.getSysMemo());
        }
        query.setHasBuyerMessage(params.getHasBuyerMessage());
        query.setHasSellerMemo(params.getHasSellerMemo());
        query.setHasSysMemo(params.getHasSysMemo());
        query.setHasMemo(params.getHasMemo());
        query.setMemos(params.getMemos());
        query.setIsExcep(params.getIsExcep());
        handleTimeoutActionTime(query, params);
        if(StringUtils.isNotEmpty(params.getDeliveryTime())){
            query.setDeliveryTimes(getAsIntArray(params.getDeliveryTime(), ",", true));
        }
        if(StringUtils.isNotEmpty(params.getSignTime())){
            query.setSignTimes(getAsIntArray(params.getSignTime(), ",", true));
        }

        query.setQueryOrder(!(params.getQueryOrder() != null && params.getQueryOrder() == 0));
        query.setIfSensitive(params.getIfSensitive());

        /**
         * 自定义查询条件前端model转param
         */
        if (query.getId() == null) {
            query.setId(params.getId());
        }
        query.setName(params.getName());
        query.setCompanyId(params.getCompanyId());
        query.setEnableStatus(params.getEnableStatus());
        query.setContent(params.getContent());
        query.setStaffId(params.getStaffId());
        query.setGroupId(params.getGroupId());
        query.setIfGroup(params.getIfGroup());
        query.setContainOutsid(params.getContainOutSid());
        query.setDaysOfCreateStart(params.getDaysOfCreateStart());
        query.setDaysOfCreateEnd(params.getDaysOfCreateEnd());
        query.setDaysOfPaymentStart(params.getDaysOfPaymentStart());
        query.setDaysOfPaymentEnd(params.getDaysOfPaymentEnd());
        query.setDaysSelection(params.getDaysSelection());
        query.setOnlyContain(params.getOnlyContain());
        query.setPrintCountStart(params.getPrintCountStart());
        query.setPrintCountEnd(params.getPrintCountEnd());
        query.setMergeNumStart(params.getMergeNumStart());
        query.setMergeNumEnd(params.getMergeNumEnd());
        query.setReacquire(params.getReacquire());

        //如果有单独输入商品标题，取单独输入的
        if(StringUtils.isNotEmpty(params.getItemTitle())){
            query.setItemTitle(params.getItemTitle());
        }

        //如果有单独输入商品标题，取单独输入的
        if(StringUtils.isNotEmpty(params.getPlatFormTitle())){
            query.setItemTitle(params.getPlatFormTitle());
        }

        query.setCommonIds(getAsLongList(params.getExpressCompanyIds(),",",true));

        //指定查询
        if((params.getAssignTrade() != null && params.getAssignTrade() == 1) && ASSIGN_TRADE_KEYS.contains(params.getKey()) && StringUtils.isNotBlank(params.getText())){
            query.setAssignTrade(true);
        } else {
            query.setAssignTrade(false);
        }
        query.setSource(params.getSource());

        query.setStockRegionTypes(getAsIntArray(params.getStockRegionTypeStr(), ",", true));
        query.setIgnoreBeforeDate(params.isIgnoreBeforeDate());
        query.setManualAutoMergeQueryAll(params.getManualAutoMergeQueryAll());
        query.setContainInsufficient(params.isContainInsufficient());
        query.setLogisticsCode(params.getLogisticsCode());
        query.setWrapperDescriptionFlag(params.getWrapperDescriptionFlag());

        //设置商品属性
        query.setItemPops(getAsIntArray(params.getItemPopsStr(), ",", true));
        query.setItemContainNonConsign(params.getItemContainNonConsign());
        query.setQueryPlatform(params.getQueryPlatform());
        query.setMinutesAfterPaidOrderAreNotDisplayed(params.getMinutesAfterPaidOrderAreNotDisplayed());
        query.setSupplierIds(Strings.getAsLongList(params.getSupplierIds(), ",", true));
        query.setBatch(params.getBatch());
        query.setCombineParcelId(params.getCombineParcelId());
        query.setRemainTimeHours(params.getRemainTimeHours());

        // 毛利润查询区间
        query.setMinGrossProfit(params.getMinGrossProfit());
        query.setMaxGrossProfit(params.getMaxGrossProfit());

        query.setMinGrossProfitRate(params.getMinGrossProfitRate());
        query.setMaxGrossProfitRate(params.getMaxGrossProfitRate());

        //上传异常类型 仅用于订单跟踪界面
        query.setUploadErrorType(getAsLongArray(params.getUploadErrorType(), ",", true));
        query.setReceiverStreets(Strings.getAsStringArray(params.getReceiverStreets(), ",", true));
        query.setFxgDfMallMaskIds(params.getFxgDfMallMaskIds());
        //是否勾选无异常
        query.setTickExcep(params.getTickExcep());

        //商品标签查询
        query.setTradeQueryItemTagIdsParams(StringUtils.isNotEmpty(params.getItemTagIdsParamsStr()) ? JSONObject.parseObject(params.getItemTagIdsParamsStr(), TradeQueryItemTagIdsParams.class) : null);

        //订单智能单品数量
        query.setSmartItemNumStart(params.getSmartItemNumStart());
        query.setSmartItemNumEnd(params.getSmartItemNumEnd());
        //初始化排序数据 来源于前端的
        query.initSortConfig(params.getSortConfig());
        //如果是勾选了无异常，并且是同时包含某个异常状态，直接返回空
        if (params.getTickExcep() - 1 == 0 && (null != params.getOnlyContain() && params.getOnlyContain() - 3 == 0)
                && (StringUtils.isNotEmpty(params.getExceptionStatus()) || StringUtils.isNotEmpty(params.getExceptIds()))) {
            //直接返回null 改为设置sid为-1，反正-1的sid 也查不出来数据；
            query.setSid(-1L);
        }
        query.setLogisticsCompanyIds(params.getLogisticsCompanyIds());
        //订单所属平台查询
        query.setSubSource(params.getSubSource());
        //支持排除订单来源
        query.setExcludeSource(params.getExcludeSource());
        //卖家昵称
        if (StringUtils.isNotEmpty(params.getSellerNicks())) {
            query.setSellerNicks(params.getSellerNicks());
        }
        //二级店铺subSource查询
        if (StringUtils.isNotEmpty(params.getSubSources())) {
            query.setSubSources(params.getSubSources());
        }
        query.setSecondUserId(params.getSecondUserId());

        //0代表没有波次，1代表有波次，大于1的数数字=具体波次
        if (params.getWaveId() !=null || params.getWaveShortId() !=null) {
            if (Objects.equals(params.getQueryId(),25L) || Objects.equals(params.getQueryId(),26L) || Objects.equals(params.getQueryId(),28L) || Objects.equals(params.getQueryId(),77L)) {
                if (Objects.equals(params.getWaveId(),0L) || Objects.equals(params.getWaveShortId(),0L) ) {
                    params.setInWave(false);
                    params.setWaveId(null);
                    params.setWaveShortId(null);
                }else if (Objects.equals(params.getWaveId(),1L) || Objects.equals(params.getWaveShortId(),1L) ) {
                    params.setInWave(true);
                    params.setWaveId(null);
                    params.setWaveShortId(null);
                }
            }
        }
        query.setPrintStatus(params.getPrintStatus());
        query.setPrintBoxMark(params.getPrintBoxMark());
        query.setOrderRefundStatus(params.getOrderRefundStatus());
        //支持匹配/排除出仓单
        query.setContainOrExcludeVipStorageNo(params.getContainOrExcludeVipStorageNo());
        query.setShippingOrderStatus(Strings.getAsStringList(params.getShippingOrderStatus(), ",", true));
        query.setShippingOrderNumber(Strings.getAsStringList(params.getShippingOrderNumber(), ",", true));
        query.setDeliveryBatchCodes(Strings.getAsStringList(params.getDeliveryBatchCodes(), ",", true));
        query.setItemPrintStatus(params.getItemPrintStatus());
        query.setAgedProductCode(params.getAgedProductCode());
        query.setRapidPrintQueryIds(params.getRapidPrintQueryIds());
        query.setFulfillPickupOrderCode(params.getFulfillPickupOrderCode());
        query.setPackageStatus(Strings.getAsIntArray(params.getPackageStatus(), ",", true));
        return query;
    }

    /**
     * 单独查询时需要走大表的查询key
     */
    public static final List<String> ASSIGN_TRADE_KEYS = Arrays.asList("receiverName","outerId","receiverMobile","receiveCode");

    public static void processKeyQuery(TradeQueryParams query, String key, String text){
        if (key != null && !key.isEmpty()) {
            if(Objects.equals(query.getItemType(),5) && "outerId".equals(key)){
                query.setSysOuterIds(Strings.getAsStringArray(text, ",", true));
                return;
            }
            if ("outerId".equals(key)) {//根据商家编码搜索
                query.setOuterId(text);
            } else if ("itemTitle".equals(key)) {//商品名称
                query.setItemTitle(text);
            } else if ("mainOuterId".equals(key)) {//主商家编码
                query.setMainOuterId(text);
            } else if ("platFormTitle".equals(key)) {//根据平台商品标题搜索
                query.setPlatFormTitle(text);
            } else if ("shortTitle".equals(key)) {// 商品简称
                query.setShortTitle(text);
            } else if ("skuProps".equals(key)) {//根据系统规格搜索
                query.setSkuProp(text);
            } else if ("platSkuProps".equals(key)) {//根据平台规格搜索
                query.setPlatSkuProp(text);
            }  else if ("itemRemark".equals(key)) {// 商品备注
                query.setItemRemark(text);
            } else if ("skuRemark".equals(key)) {// 规则备注
                query.setSkuRemark(text);
            } else if ("skuPropAlias".equals(key)) {// 规格别名
                query.setSkuPropAlias(text);
            } else if ("identCode".equals(key)) {
                query.setIdentCode(text);
            } else if ("sysMemo".equals(key)) {
                query.setSysMemo(text);
            } else if ("buyerMessage".equals(key)) {
                query.setBuyerMessage(text);
            } else if ("sellerMemo".equals(key)) {
                query.setSellerMemo(text);
            } else if ("receiverName".equals(key)) {
                query.setReceiverName(text);
            } else if ("receiverMobile".equals(key)) {
                query.setReceiverMobile(text);
            } else if ("mobileTail".equals(key)) {
                query.setMobileTail(text);
            } else if ("receiverPhone".equals(key)) {
                query.setReceiverPhone(text);
            } else if ("receiverAddress".equals(key)) {
                query.setReceiverAddress(text);
            } else if ("poNo".equals(key)) {
                query.setPoNo(text);
            } else if ("vipPickNo".equals(key)) {
                query.setVipPickNo(text);
            } else if ("vipStorageNo".equals(key)) {
                query.setVipStorageNo(text);
            } else if ("buyerNick".equals(key)) {
                query.setBuyerNick(getAsStringArray(text, ",", true));
            } else if ("uniqueCode".equals(key)) {
                query.setUniqueCode(text);
            } else if ("numIid".equals(key)){// 平台商品
                query.setNumIid(getAsStringArray(text, ",", true));
            } else if ("outerIid".equals(key)) {// 平台主商家编码
                query.setOuterIid(getAsStringArray(text, ",", true));
            } else if ("receiverState".equals(key)){
                query.setReceiverState(text);
            } else if ("receiverCity".equals(key)){
                query.setReceiverCity(text);
            } else if ("receiverDistrict".equals(key)){
                query.setReceiverDistrict(text);
            } else if ("skuId".equals(key)) {
                query.setSkuId(getAsStringArray(text, ",", true));
            } else if ("excludeOuterId".equals(key)){
                query.setExcludeOuterId(text);
            } else if ("cooperationNo".equals(key)) {
                query.setCooperationNo(text);
            } else if ("cooperationNoJitx".equals(key)){
                query.setCooperationNoJitx(text);
            } else if ("bicUniqueCode".equals(key)) {
                query.setBicUniqueCode(text);
            } else if ("authorId".equals(key)) {
                query.setAuthorId(text);
            } else if ("authorName".equals(key)) {
                query.setAuthorName(text);
            } else if ("shippingCarrier".equals(key)) {
                query.setShippingCarrier(text);
            } else if ("uniqueCodeSplitQuery".equals(key)) {
                query.setUniqueCode(text);
            } else if ("btasPictureQualityResult".equals(key)){
                query.setBtasPictureQualityResult(text);
            } else if ("btasOrderCode".equals(key)){
                query.setBtasOrderCode(text);
            } else if ("salesmanName".equals(key)) {
                query.setSalesmanName(text);
            } else if ("caigouCode".equals(key)) {
                query.setCaigouCode(text);
            } else if ("distributorId".equals(key)){
                query.setDistributorId(text);
            } else if ("distributorName".equals(key)){
                query.setDistributorName(text);
            } else if ("originPlatformOuterId".equals(key)){
                query.setOriginPlatformOuterId(text);
            } else if ("receiveCode".equals(key)){
                query.setReceiveCode(text);
            } else if ("activityName".equals(key)){
                query.setActivityName(text);
            }
        }
    }

    public static final Map<String, String> modelFields2DBFields = new HashMap<String, String>();
    public static final Map<Long,List<String>> QUERYID_REQUIRED_FIELDS = Maps.newHashMapWithExpectedSize(8);

    static {
        SortConfig.SUPPORT_SORT_FIELD_LIST.forEach(f->{
            modelFields2DBFields.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,f),"t."+f);
        });
        QUERYID_REQUIRED_FIELDS.put(-1L,Arrays.asList("sid","merge_sid"));
        QUERYID_REQUIRED_FIELDS.put(21L,Arrays.asList("is_excep","user_id"));
        QUERYID_REQUIRED_FIELDS.put(23L,Arrays.asList("is_excep","user_id"));
        QUERYID_REQUIRED_FIELDS.put(26L,Arrays.asList("is_excep","user_id"));
    }


    private static String transeform2DbFields(String modelFields) {
        if (org.apache.commons.lang3.StringUtils.isBlank(modelFields)) {
            return "";
        }
        StringBuilder dbFieldsBuild = new StringBuilder();
        for (String field : org.apache.commons.lang3.StringUtils.split(modelFields, ",")) {
            if (null == modelFields2DBFields.get(field)) {
                continue;
            }
            dbFieldsBuild.append(modelFields2DBFields.get(field)).append(",");
        }
        return dbFieldsBuild.substring(0, dbFieldsBuild.length() - 1);
    }

    private static void handleTimeoutActionTime(TradeQueryParams query, TradeControllerParams params) {
        //支持剩余时间区间下限 KMERP-147486
        if (Objects.equals(params.getTimeoutActionTimeType(), 0) && params.getTimeoutActionTimeAfter() != null) {
            query.setTimeoutActionTimeAfter(params.getTimeoutActionTimeAfter());
        }
        if (params.getTimeoutActionTimeBefore() == null && params.getTimeoutActionTimeType() == null) {
            return;
        }
        if(params.getTimeoutActionTimeBefore() == null && params.getTimeoutActionTimeType() != null){
            query.setTimeoutActionTimeType(params.getTimeoutActionTimeType());
            return;
        }
        query.setTimeoutActionTimeType(params.getTimeoutActionTimeType());
        if(params.getTimeoutActionTimeBefore() == -999999999){//-999999999是与前端约定好的值，一般不会有人用这个值
            query.setTimeoutActionTime(TradeQueryParams.TIMEOUTACTIONTIMEBEFORE_NULL);
        }else if(params.getTimeoutActionTimeBefore() != -888888888){
            Date TimeoutActionTime = DateUtils.addHours(new Date(), params.getTimeoutActionTimeBefore());
            query.setTimeoutActionTime(TimeoutActionTime);
        }

        if (query.getQueryId() == null || query.getQueryId() != 8) {
            return;
        }
        // 自定义查询条件 不用根据是否超时去设置状态
        if(BooleanUtils.isTrue(params.getIfCustom())){
            return;
        }
        Set<String> statusSet = new HashSet<String>();
        statusSet.add(TbTrade.SYS_STATUS_WAIT_AUDIT);
        statusSet.add(TbTrade.SYS_STATUS_FINISHED_AUDIT);

        Set<String> filterStatusSet = new HashSet<String>();
        String[] statusList = query.getStatusList();
        if (statusList != null) {
            for (String status:statusList) {
                if (statusSet.contains(status)) {
                    filterStatusSet.add(status);
                }
            }
        }
        if (filterStatusSet.size() == 0) {
            query.setSysStatus(statusSet.toArray(new String[0]));
        } else {
            query.setSysStatus(filterStatusSet.toArray(new String[0]));
        }

    }
}

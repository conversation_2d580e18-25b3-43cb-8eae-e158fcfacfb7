package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

/**
 * @Author: chen<PERSON><PERSON><PERSON>
 * @Date: 2021/1/22 10:52 上午
 */
public class TradeGetRequest implements Serializable {
    private static final long serialVersionUID = 9087747477051926555L;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * staff
     */
    private Staff staff;

    /**
     * 是否需要查询order信息
     */
    private boolean queryOrders;



    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public boolean isQueryOrders() {
        return queryOrders;
    }

    public void setQueryOrders(boolean queryOrders) {
        this.queryOrders = queryOrders;
    }

}

package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Types;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * erp 业务动作跟踪记录 DO+DTO+pojo多用途对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Table(name = "trade_trace", routerKey = "tradeTraceDbNo")
public class TradeTrace extends Model {

    public static final String DOWNLOAD_TRADE = "同步下载订单";

    private static final long serialVersionUID = -3585579845261031431L;

    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id;

    /**
     * 卖家的淘宝编号
     */
    @Column(name = "taobao_id", type = Types.BIGINT)
    private Long taobaoId;

    /**
     * 公司编号
     */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 订单编号
     */
    private String tid;

    /**
     * 系统单号
     */
    @Column(name = "sid", type = Types.BIGINT)
    private Long sid;

    /**
     * 操作名称
     */
    @Column(name = "action", type = Types.VARCHAR)
    private String action;

    /**
     * 操作员名称
     */
    @Column(name = "operator", type = Types.VARCHAR)
    private String operator;

    /**
     * 操作时间
     */
    @Column(name = "operate_time", type = Types.TIMESTAMP)
    private Date operateTime;

    /**
     * 操作内容
     */
    @Column(name = "content", type = Types.VARCHAR)
    private String content;

    /**
     * 数据创建时间
     */
    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    @Column(name = "enable_status", type = Types.TINYINT)
    private Integer enableStatus;

    /**
     * 扩展属性
     */
    private final Map<String, Object> extProperties = new HashMap<>();

    public void putStringProperty(String key, String value) {
        this.extProperties.put(key, value);
    }

    public void putLongProperty(String key, Long value) {
        this.extProperties.put(key, value);
    }

    public TradeTrace withEventName(String eventName) {
        this.extProperties.put("event", eventName);
        return this;
    }
}

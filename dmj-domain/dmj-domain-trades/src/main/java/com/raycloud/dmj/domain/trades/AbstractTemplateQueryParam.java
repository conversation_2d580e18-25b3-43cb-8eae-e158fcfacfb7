package com.raycloud.dmj.domain.trades;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-03-06-16:38
 */
public abstract class AbstractTemplateQueryParam implements Serializable {

    private static final long serialVersionUID = 2429741390970146336L;
    /**
     * 普通快递模版ID集合
     */
    protected List<Long> commonTemplateIds;

    /**
     * 电子快递模版ID集合
     */
    protected List<Long> wlbTemplateIds;
    /**
     * 跨境模版
     */
    protected List<Long> kjTemplateIds;
    /**
     * 查询的快递模版类型，根据commonTemplateIds与wlbTemplateIds综合计算得到， "01" 既查询普通模版也查询电子模版， "0" 只查询普通模版，"1" 只查询电子模版
     */
    protected String templateType;

    /**
     * 快递公司列表
     */
    private String logisticsCompanyIds;

    /**
     * 快递公司id列表
     */
    private List<Long> logisticsCompanyIdList;

    public List<Long> getCommonTemplateIds() {
        return commonTemplateIds;
    }

    public void setCommonTemplateIds(List<Long> commonTemplateIds) {
        this.commonTemplateIds = commonTemplateIds;
    }

    public List<Long> getWlbTemplateIds() {
        return wlbTemplateIds;
    }

    public void setWlbTemplateIds(List<Long> wlbTemplateIds) {
        this.wlbTemplateIds = wlbTemplateIds;
    }

    public List<Long> getKjTemplateIds() {
        return kjTemplateIds;
    }

    public void setKjTemplateIds(List<Long> kjTemplateIds) {
        this.kjTemplateIds = kjTemplateIds;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getTemplateType() {
        if (templateType == null) {
            if (getCommonTemplateIds() != null && !getCommonTemplateIds().isEmpty() && getWlbTemplateIds() != null && !getWlbTemplateIds().isEmpty() && getKjTemplateIds() != null && !getKjTemplateIds().isEmpty()) {
                templateType = "012";
            } else if (getCommonTemplateIds() != null && !getCommonTemplateIds().isEmpty() && getWlbTemplateIds() != null && !getWlbTemplateIds().isEmpty()) {
                templateType = "01";
            } else if (getCommonTemplateIds() != null && !getCommonTemplateIds().isEmpty() && getKjTemplateIds() != null && !getKjTemplateIds().isEmpty()) {
                templateType = "02";
            } else if (getWlbTemplateIds() != null && !getWlbTemplateIds().isEmpty() && getKjTemplateIds() != null && !getKjTemplateIds().isEmpty()) {
                templateType = "12";
            } else if (getCommonTemplateIds() != null && !getCommonTemplateIds().isEmpty()) {
                templateType = "0";
            } else if (getWlbTemplateIds() != null && !getWlbTemplateIds().isEmpty()) {
                templateType = "1";
            } else if (getKjTemplateIds() != null && !getKjTemplateIds().isEmpty()) {
                templateType = "2";
            }
        }
        return templateType;
    }

    private void addTemplateId(Long templateId, Integer templateType) {
        if (templateType != null && templateType - 1 == 0) {//电子面单
            if (getWlbTemplateIds() == null) {
                setWlbTemplateIds(new ArrayList<Long>());
            }
            getWlbTemplateIds().add(templateId);
        } else if (templateType != null && templateType - 2 == 0) {//跨境模版
            if (getKjTemplateIds() == null) {
                setKjTemplateIds(new ArrayList<Long>());
            }
            getKjTemplateIds().add(templateId);
        } else {//普通模版
            if (getCommonTemplateIds() == null) {
                setCommonTemplateIds(new ArrayList<Long>());
            }
            getCommonTemplateIds().add(templateId);
        }
    }

    public void express2TemplateIds(String express) {
        if (express == null || (express = express.trim()).isEmpty()) {
            return;
        }
        String[] idTypeArr = express.split(",");
        for (int i = 0; i < idTypeArr.length; i++) {
            String[] idType = idTypeArr[i].trim().split("_");
            if (idType.length > 1) {
                addTemplateId(Long.parseLong(idType[0]), Integer.parseInt(idType[1]));
            } else {
                addTemplateId(Long.parseLong(idType[0]), 0);
            }
        }
    }

    public String getLogisticsCompanyIds() {
        return logisticsCompanyIds;
    }

    public void setLogisticsCompanyIds(String logisticsCompanyIds) {
        this.logisticsCompanyIds = logisticsCompanyIds;
        if (StringUtils.isNotBlank(logisticsCompanyIds)) {
            logisticsCompanyIdList = new ArrayList<>();
            for (String s : logisticsCompanyIds.split(",")) {
                logisticsCompanyIdList.add(Long.valueOf(s));
            }
        }
    }

    public List<Long> getLogisticsCompanyIdList() {
        return logisticsCompanyIdList;
    }

    public void setLogisticsCompanyIdList(List<Long> logisticsCompanyIdList) {
        this.logisticsCompanyIdList = logisticsCompanyIdList;
    }
}

package com.raycloud.dmj.domain.trades.tradepay;

/**
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/3/19 5:40 下午
 */
public enum TradePayStatus {
    STATUS_WAIT_CHECK(1, "待审核"),
    STATUS_SUCESS(2, "已生效"),
    STATUS_CANCEL(3, "已作废");

    private int value;

    private String name;

    TradePayStatus(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static TradePayStatus parseOfValue(int value) {
        for (TradePayStatus source : TradePayStatus.values()) {
            if (source.value == value) {
                return source;
            }
        }

        return null;
    }
}

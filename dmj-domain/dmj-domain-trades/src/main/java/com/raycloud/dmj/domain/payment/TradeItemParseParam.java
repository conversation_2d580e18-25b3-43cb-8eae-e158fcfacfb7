package com.raycloud.dmj.domain.payment;

import com.raycloud.dmj.domain.trades.Order;
import lombok.Data;

import java.util.List;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-05-09
 */
@Data
public class TradeItemParseParam {

    // 换前商品金额信息
    private  Long  sid;

    private  String  payment;

    private  String  payAmount;

    private String divideOrderFee;

    private String platformDiscountFee;

    // 换后商品订单 调用方需保证 price num cost,suits 字段准确
    private List<Order> orders;
}

package com.raycloud.dmj.domain.trades;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class PresellItemQueryParams {
    private List<Long> deleteIds;
    /**
     * 商品信息
     */
    private List<PresellItem> items;
    /**
     * 规则Id
     */
    private Long ruleId;


    private String ruleIds;


    private Long versionId;
    /**
     * 规则类型
     */
    private Integer ruleType;
    /**
     * 删除的商品
     */
    private List<PresellItem> deleteItems;
    /**
     * 商品类型
     */
    private Integer itemType;

    private Integer pageNo = 1;

    private Integer pageSize = 10;

}

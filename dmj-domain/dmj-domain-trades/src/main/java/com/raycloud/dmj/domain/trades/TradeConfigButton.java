package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;


/**
 * <AUTHOR>
 * @program: erp-core
 * @description: 按钮配置
 * Created by luz<PERSON><PERSON>
 * Created on 2022/8/4 2:00 下午
 * Copyright(c)2022  版权所有
 */
@Table(name = "trade_config_button")
public class TradeConfigButton extends Model{

    private static final long serialVersionUID = -967599848169652736L;

    /**
     * 逻辑主键
     */
    private Long id;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 用户ID
     */
    private Long staffId;

    /**
     * 按钮配置详情
     */
    private String configVal;

    /**
     * 逻辑主键
     */
    private Date created;

    /**
     * 逻辑主键
     */
    private Long configType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getConfigVal() {
        return configVal;
    }

    public void setConfigVal(String configVal) {
        this.configVal = configVal;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Long getConfigType() {
        return configType;
    }

    public void setConfigType(Long configType) {
        this.configType = configType;
    }

}

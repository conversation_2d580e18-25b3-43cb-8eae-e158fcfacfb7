package com.raycloud.dmj.domain.trades;

/**
 * TradeTrace Vo对象
 *
 * <AUTHOR>
 */
public class TradeTraceVo {
    /**
     * 系统单号
     */
    private Long sid;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 操作备注
     */
    private String content;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * ERP的Trade流水短号，系统生成，公司维度唯一
     */
    private Long  shortId;

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

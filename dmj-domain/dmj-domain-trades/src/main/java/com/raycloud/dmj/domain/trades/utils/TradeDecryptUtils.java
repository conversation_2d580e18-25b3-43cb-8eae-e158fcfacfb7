package com.raycloud.dmj.domain.trades.utils;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;

import java.util.List;

public class TradeDecryptUtils {
    /**
     * 支持页面根据手机号/收件人查询的平台
     */
    private static final List<String> supportPageSearchUserList = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO,CommonConstants.PLAT_FORM_TYPE_TIAN_MAO,CommonConstants.PLAT_FORM_TYPE_FX,CommonConstants.PLAT_FORM_TYPE_1688,CommonConstants.PLAT_FORM_TYPE_1688_C2M);
    /**
     * 支持页面根据手机号后四位查询
     */
    private static final List<String> supportLastFourPhoneUserList = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO,CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);

    /**
     * 是否支持页面手机号/收件人查询
     * @param user
     * @return
     */
    public static boolean supportPageSearchUsers(User user){
        return supportPageSearchUserList.contains(user.getSource());
    }

    /**
     * 是否支持页面手机号/收件人查询
     * @param user
     * @return
     */
    public static boolean supportPageSearchUsers4All(User user){
        boolean taoSeries = null != user && TradeDecryptUtils.supportPageSearchUsers(user) && null != user.getUserConf().getConfAttrInfoIfEmpty().get("openOaid");
        boolean fxgSupport = null != user && CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource());
        boolean pddSupport = null != user && CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource());
        boolean sphSupport = null != user && CommonConstants.PLAT_FORM_TYPE_WXSPH.equals(user.getSource());
        return  taoSeries || fxgSupport || sphSupport || pddSupport;
    }

    /**
     * 是否支持手机号后四位查询(有些平台不支持比如1688c2m)
     * @param user
     * @return
     */
    public static boolean supportLastFourPhoneSearch(User user){
        return supportLastFourPhoneUserList.contains(user.getSource());
    }
}

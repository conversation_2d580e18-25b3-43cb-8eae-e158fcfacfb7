package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.erp.db.model.Model;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by windy26205 on 19/11/18.
 */
public class TradePerformanceOptLog extends Model implements Serializable{

    public static int Audit_Type = 4;

    public static int Pack_Type = 5;

    public static int Weight_Type = 6;

    public static int Consign_Type = 1;

    public static int Re_Consign_Type = 2;

    public static int Cancel_Consign_Type = 3;

    public static int Finance_Audit_Type = 7;


    private static final long serialVersionUID = 1L;

    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 操作人Id
     */
    private Long staffId;

    /**
     * 操作人姓名
     */
    private String staffName;

    /**
     * 操作订单Sid
     */
    private Long sid;

    /**
     * 波次号id
     */
    private Long waveId;


    /**
     *创建时间
     */
    private Timestamp created;

    /**
     * 更新时间
     */
    private Timestamp modified;

    /**
     * 是否有效
     */
    private Integer enableStatus;

    /**
     * 信息json展示
     */

    private String infoJson;

    /**
     * 操作类型
     */
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Timestamp getCreated() {
        return created;
    }

    public void setCreated(Timestamp created) {
        this.created = created;
    }

    public Timestamp getModified() {
        return modified;
    }

    public void setModified(Timestamp modified) {
        this.modified = modified;
    }

    public String getInfoJson() {
        return infoJson;
    }

    public void setInfoJson(String infoJson) {
        this.infoJson = infoJson;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }




}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.trades.TradeExt;

/**
 * @Author: chenchaochao
 * @Date: 2020/10/26 3:42 下午
 */
public class TradeExtCopier implements AttrCopier<TradeExt, TradeExt> {
    @Override
    public TradeExt copy(TradeExt tradeExt, TradeExt target) {
        target.setSid(tradeExt.getSid());
        target.setCompanyId(tradeExt.getCompanyId());
        target.setUserId(tradeExt.getUserId());
        target.setTid(tradeExt.getTid());
        target.setMallMaskId(tradeExt.getMallMaskId());
        target.setMallMaskName(tradeExt.getMallMaskName());
        target.setStatus(tradeExt.getStatus());
        target.setReceiverNameIndex(tradeExt.getReceiverNameIndex());
        target.setReceiverMobileIndex(tradeExt.getReceiverMobileIndex());
        target.setReceiverAddressIndex(tradeExt.getReceiverAddressIndex());
        target.setDesensitizationReceiverName(tradeExt.getDesensitizationReceiverName());
        target.setDesensitizationReceiverMobile(tradeExt.getDesensitizationReceiverMobile());
        target.setDesensitizationReceiverAddress(tradeExt.getDesensitizationReceiverAddress());
        target.setLogisticsCode(tradeExt.getLogisticsCode());
        target.setWrapperDescription(tradeExt.getWrapperDescription());
        target.setShippingCarrier(tradeExt.getShippingCarrier());
        target.setTransitWarehouseId(tradeExt.getTransitWarehouseId());
        target.setLogisticId(tradeExt.getLogisticId());
        target.setCurrency(tradeExt.getCurrency());
        target.setWarehouseAddress(tradeExt.getWarehouseAddress());
        target.setCreated(tradeExt.getCreated());
        target.setAutoUnHook(tradeExt.getAutoUnHook());
        target.setStoreName(tradeExt.getStoreName());
        target.setAutoUnHookTime(tradeExt.getAutoUnHookTime());
        target.setCooperationNo(tradeExt.getCooperationNo());
        target.setSellSite(tradeExt.getSellSite());
        target.setBicUniqueCode(tradeExt.getBicUniqueCode());
        target.setForderId(tradeExt.getForderId());
        target.setPackageNumber(tradeExt.getPackageNumber());
        target.setCombineParcelId(tradeExt.getCombineParcelId());
        target.setTmallAsdpBizType(tradeExt.getTmallAsdpBizType());
        target.setTmallAsdpAds(tradeExt.getTmallAsdpAds());
        target.setDeliveryType(tradeExt.getDeliveryType());
        target.setShipType(tradeExt.getShipType());
        target.setRedpackFee(tradeExt.getRedpackFee());
        target.setRedpackPlatformFee(tradeExt.getRedpackPlatformFee());
        target.setRedpackTalentFee(tradeExt.getRedpackTalentFee());
        target.setLogisticsRecords(tradeExt.getLogisticsRecords());
        target.setExtraFields(tradeExt.getExtraFields());
        target.setPromiseDeliveryTime(tradeExt.getPromiseDeliveryTime());
        return target;
    }
}

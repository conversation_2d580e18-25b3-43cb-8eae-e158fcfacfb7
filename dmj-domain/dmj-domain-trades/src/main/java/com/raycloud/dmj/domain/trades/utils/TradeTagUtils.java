package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.enums.SellerFlagEnum;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class TradeTagUtils {

    /**
     * 检查该订单是否存在指定tag，订单或标签为null时返回false
     *
     * @param trade 订单
     * @param tag   指定标签
     */
    public static boolean checkIfExistTag(Trade trade, TradeTag tag) {
        if (Objects.isNull(trade) || Objects.isNull(tag)) {
            return false;
        }
        Set<Long> tagIds = Strings.getAsLongSet(trade.getTagIds(), ",", false);
        return tagIds.contains(tag.getId());
    }

    /**
     * 当订单不包含此tag时添加指定tag
     *
     * @param trade 订单
     * @param tag   指定标签
     * @return true: 不存在该标签，成功添加  false: 入参为null或已存在该标签
     */
    public static boolean addIfNotExistTag(Trade trade, TradeTag tag) {
        if (Objects.isNull(trade) || Objects.isNull(tag)) {
            return false;
        }
        Set<Long> tagIds = Strings.getAsLongSet(trade.getTagIds(), ",", false);
        if (tagIds.contains(tag.getId())) {
            return false;
        }
        tagIds.add(tag.getId());
        trade.setTagIds(Strings.join(",", tagIds));
        return true;
    }

    /**
     * 将target订单的tag合并到source订单中{@link Trade#getTagIds()}
     *
     * @param source 合并源订单，合并后tagIds包含target中的tag
     * @param target 被合并订单，将该订单的tag合并到source中，此订单不变
     */
    public static void mergeTargetTagToSourceTag(Trade source, Trade target) {
        if (Objects.isNull(source) || Objects.isNull(target)) {
            return;
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(target.getTagIds())) {
            return;
        }
        Set<Long> targetTagIds = Strings.getAsLongSet(target.getTagIds(), ",", false);
        Set<Long> sourceTagIds = StringUtils.isEmpty(source.getTagIds()) ? new HashSet<>(targetTagIds.size()) : Strings.getAsLongSet(source.getTagIds(), ",", false);
        sourceTagIds.addAll(targetTagIds);
        source.setTagIds(Strings.join(",", sourceTagIds));
    }

    public static String getFlagColor(Long flag){
        return SellerFlagEnum.getMsg(flag);
    }

    public static String getMemoContent(String memo){
        if(memo==null){
            return "无";
        }else{
            return memo;
        }
    }


    public static List<String> getTagNameList(Trade singleTrade, String excludeTradeTagIds) {
        if (CollectionUtils.isEmpty(singleTrade.getTags())) {
            return new ArrayList<>();
        }
        List<TradeTag> tags = singleTrade.getTags();
        if (StringUtils.isNotBlank(excludeTradeTagIds)) {
            List<String> excludeTradeTagIdList = Arrays.asList(excludeTradeTagIds.split(","));
            tags = tags.stream().filter(tag -> !excludeTradeTagIdList.contains(tag.getId().toString())).collect(Collectors.toList());
        }
        return tags.stream().map(TradeTag::getTagName).collect(Collectors.toList());
    }


    public static String getExceptNames(Set<Long> exceptIds, Map<Long, String> exceptNameMap) {
        if (CollectionUtils.isNotEmpty(exceptIds) && MapUtils.isNotEmpty(exceptNameMap)) {
            return exceptIds.stream().map(id -> Optional.ofNullable(exceptNameMap.get(id)).orElse( "" + id)).collect(Collectors.joining(","));
        }
        return CollectionUtils.isNotEmpty(exceptIds) ? exceptIds.stream().map(String::valueOf).collect(Collectors.joining(",")) : "";
    }

}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

public class TradeLog implements Serializable {
    private static final long serialVersionUID = -1L;

    private String logBiz;

    private JSONObject log;

    public TradeLog() {
    }

    public TradeLog(Staff staff) {
        if (staff != null) {
            JSONObject log = new JSONObject();
            log.put("clueId", staff.getClueId());
            log.put("companyId", staff.getClueId());
            log.put("id", staff.getId());
            log.put("name", staff.getName());
            log.put("companyName", staff.getCompanyName());
            this.log = log;
        }
    }

    public String getLogBiz() {
        return logBiz;
    }

    public void setLogBiz(String logBiz) {
        this.logBiz = logBiz;
    }

    public JSONObject getLog() {
        if (log ==null){
            log =new JSONObject();
        }
        return log;
    }

    public void setLog(JSONObject log) {
        this.log = log;
    }

    public void appendLog(JSONObject log) {
        if (log != null){
            for (String s : log.keySet()) {
                getLog().put(s, log.get(s));
            }
        }
    }
}

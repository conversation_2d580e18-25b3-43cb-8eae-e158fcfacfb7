package com.raycloud.dmj.domain.trades.payment.util;

import com.raycloud.dmj.domain.account.Staff;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description <pre>
 *  堆栈信息精简工具
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-09
 */
public class StackTracesUtils {

    private static final Logger logger = Logger.getLogger(StackTracesUtils.class);

    public static void recordStackTraces(Staff staff,String message){
        Exception e;
        if (StringUtils.isNotBlank(message)) {
            e = new Exception(message);
        }else {
            e = new Exception("用于调用链统计 仅测试环境允许 正式环境请删除对当前方法的调用");
        }
        filterStackTraces(e);
        logger.warn(PaymentLogHelper.buildLogHead(staff).append(e.getMessage()).toString(),e);
    }


    public static Throwable filterStackTraces(Throwable e){
        return filterStackTraces(e,0);
    }

    public static Throwable filterStackTraces(Throwable e,int begin){
        if (e == null) {
            return null;
        }
        StackTraceElement[] stackTraces = e.getStackTrace();
        List<StackTraceElement> filtered = new ArrayList<>();

        for (int i = begin; i < stackTraces.length; i++) {
            StackTraceElement stackTraceElement = stackTraces[i];
            //第一个堆栈不做过滤
            if (i == begin) {
                filtered.add(stackTraceElement);
            }else {
                String line = stackTraceElement.getClassName();
                boolean filted = false;
                for (String proFix : filters) {
                    if (line.contains(proFix)) {
                        filted = true;
                        for (String wProFix : whiteList) {
                            if (line.contains(wProFix)) {
                                filted = false;
                                break;
                            }
                        }
                        if (filted) {
                            break;
                        }
                    }
                }
                if (!filted) {
                    filtered.add(stackTraceElement);
                }
            }
        }

        e.setStackTrace(filtered.toArray(new StackTraceElement[0]));
        Throwable cause = e.getCause();
        if (cause != null) {
            filterStackTraces(cause);
        }
        return e;
    }

    static List<String> filters = Arrays.asList(
            "org.springframework.cglib",
            "org.springframework.aop",
            "org.springframework.transaction.interceptor",
            "org.springframework.web",

            "sun.reflect",
            "java.lang.reflect",
            "java.lang.Thread",
            "java.util.stream",
            "java.util.concurrent",

            "org.apache.catalina",
            "org.apache.coyote",
            "org.apache.tomcat",
            "javax.servlet.http.HttpServlet",

            "com.alibaba.dubbo",

            "$$FastClassBySpringCGLIB$$",
            "$$EnhancerBySpringCGLIB$$",

            "com.netflix.hystrix",
            "rx.internal.operators",
            "rx.observers",
            "rx.Observable",

            "com.raycloud.dmj.services.inner.LockProxyBuiness",
            "com.raycloud.dmj.services.impl.OldLockService",
            "com.raycloud.dmj.aspect.LockRecordAspect",
            "com.raycloud.dmj.services.utils.LogKit",
            "com.raycloud.ec"
    );

    static List<String> whiteList = Arrays.asList(
            "org.springframework.transaction.interceptor.TransactionAspectSupport",
            "com.alibaba.dubbo.monitor.support.MonitorFilter",
            "com.raycloud.ec.api.EventListenerTask"
    );

}

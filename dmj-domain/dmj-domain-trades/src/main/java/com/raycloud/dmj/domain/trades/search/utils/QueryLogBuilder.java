package com.raycloud.dmj.domain.trades.search.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.user.User;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-01-03
 */
public class QueryLogBuilder extends AbsLogBuilder<QueryLogBuilder> {

    public QueryLogBuilder(User user) {
        super(user);
    }

    public QueryLogBuilder(Staff staff) {
        super(staff);
    }

    public QueryLogBuilder(Long companyId) {
        super(companyId);
    }

    public QueryLogBuilder(Long companyId, Long staffId, String staffName) {
        super(companyId, staffId, staffName);
    }

    @Override
    protected String getBusinessSign() {
        return LogBusinessEnum.QUERY.getSign();
    }
}

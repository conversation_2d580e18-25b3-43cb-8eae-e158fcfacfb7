package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.*;

import java.util.*;

import static com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum.*;

public class TradeConsolidateInfoUtils {

    public static final List<Long> PDD_CONSOLIDATE_LABELS = new ArrayList<Long>() {{
        add(TAG_1000000036.getId());
        add(TAG_1000000056.getId());
        add(TAG_1000000057.getId());
        add(TAG_PDD_JY_HONGKONG.getId());
        add(TAG_PDD_JY_XINJIANG.getId());
        add(TAG_PDD_JY_HSK.getId());
        add(TAG_PDD_JY_XIZANG.getId());
        add(TAG_JAPAN_CONSOLIDATED_SHIPPING.getId());
        add(TAG_TAIWAN_CONSOLIDATED_SHIPPING.getId());
        add(TAG_KOREA_CONSOLIDATED_SHIPPING.getId());
        add(TAG_SINGAPORE_CONSOLIDATED_SHIPPING.getId());
        add(TAG_MALAYSIA_CONSOLIDATED_SHIPPING.getId());
        add(TAG_THAILAND_CONSOLIDATED_SHIPPING.getId());
        add(TAG_PINDUODUO_GANSU_TRANSIT.getId());
        add(TAG_PINDUODUO_INNER_MONGOLIA_TRANSIT.getId());
        add(TAG_PINDUODUO_NINGXIA_TRANSIT.getId());
        add(TAG_PINDUODUO_QINGHAI_TRANSIT.getId());

    }};

    /**
     * 判断是否是拼多多集运订单
     */
    public static boolean isPddConsolidatedTrade(Trade trade) {
        if (!CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())
                && !CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())
                && !(CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSubSource()))
        ) {
            return false;
        }

        if (
                PlatformUtils.isTradeExtContainsKey(trade, "isConsolidateType", "1")
                        || PlatformUtils.isTradeExtContainsKey(trade, "consolidate_hmt", "1")
                        || PlatformUtils.isTradeExtContainsKey(trade, "consolidate_overseas", "1")
        ) {
            return true;
        }

        Set<Long> tagIdList = ArrayUtils.toLongSet(trade.getTagIds());
        List<Long> intersection = CollectionsUtils.intersection(tagIdList, PDD_CONSOLIDATE_LABELS);
        if (!intersection.isEmpty()) {
            return true;
        }
        return false;
    }

}

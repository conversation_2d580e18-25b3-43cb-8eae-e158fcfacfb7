package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

public class SpiteReportDTO implements Serializable {
    private static final long serialVersionUID = -4967651615439679853L;
    /**
     * 问题描述
     */
    private String memo;
    /**
     * 订单商品状态[1:未发货/已收回, 2:已发货/已收回, 3:已发货/未收回]
     */
    private Integer status;
    /**
     * 上报类型1:大量拍下不付款,2:下单疑似因纠纷报复我),3:差价/运费/赠品链接单拍),4:疑似通过凑单拆单套利、刷券套利),
     * 5:黄牛经销商批量下单),6:存在多次下单后无故拒收行为),7:地址不详/不合理且联系不到客户),8:在本店铺存在多次异常退换货行为)
     */
    private Integer reason;
    /**
     * 订单编号集合/服务单号集合（恶意退换货上报独有字段）
     */
    private String theOthers;
    /**
     * 售后联系人手机号
     */
    private String relationPhone;
    /**
     * 售后联系人姓名
     */
    private String relationName;
    /**
     * 文件全名称
     */
    private String fileName;
    /**
     * 证据文件数组
     */
    private byte[] fileByte;

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getReason() {
        return reason;
    }

    public void setReason(Integer reason) {
        this.reason = reason;
    }

    public String getTheOthers() {
        return theOthers;
    }

    public void setTheOthers(String theOthers) {
        this.theOthers = theOthers;
    }

    public String getRelationPhone() {
        return relationPhone;
    }

    public void setRelationPhone(String relationPhone) {
        this.relationPhone = relationPhone;
    }

    public String getRelationName() {
        return relationName;
    }

    public void setRelationName(String relationName) {
        this.relationName = relationName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public byte[] getFileByte() {
        return fileByte;
    }

    public void setFileByte(byte[] fileByte) {
        this.fileByte = fileByte;
    }
}

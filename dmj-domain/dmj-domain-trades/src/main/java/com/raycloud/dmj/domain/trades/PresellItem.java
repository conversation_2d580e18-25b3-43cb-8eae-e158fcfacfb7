package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.item.SkuInfo;
import com.raycloud.erp.db.model.Table;
import lombok.*;

import java.util.Date;

/**
 * Description: 各个自动化规则中 商品明细
 *
 * @version Version 1.0
 * <AUTHOR>
 * @Copyright 2016 Git Inc. All rights reserved.
 * @CreateDate on 2016.11.21
 * @Company 杭州光云科技有限公司
 */
@Getter
@Setter
@NoArgsConstructor
@Table(name = "presell_item", routerKey = "preSellItemDbNo")
public class PresellItem extends SkuInfo {

    private static final long serialVersionUID = 2300528090305292610L;

    /**
     * 平台商品id
     */
    private String platformItemId;

    /**
     * 历史记录版本id
     */
    private Long versionId;

    private String staffName;
    /**
     * 平台商品skuId
     */
    private String platformSkuId;

    /**
     * 商品类型
     */
    private Integer itemType;

    private Long id;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 预售规则ID
     */
    private Long ruleId;

    /**
     *  规则类型
     *
     */
    private Integer ruleType;

    /**
     * 规则扩展类型(可扩展使用)  ruleType == RULE_TYPE_EXPRESSMATCH 时：0：排除 1：包含(默认)
     */
    private Integer extType = 1;

    private Integer maximum;

    private Integer minimum;

    private Date created;
}

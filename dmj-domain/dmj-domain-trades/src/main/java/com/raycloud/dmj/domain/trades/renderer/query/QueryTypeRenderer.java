package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class QueryTypeRenderer implements IFieldValueRenderer {

    /**
     * text字段搜索方式，1 精准搜索，  其它 模糊搜索,默认模糊搜索
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,1)) {
            return "精准搜索";
        }
        return "模糊搜索";
    }
}

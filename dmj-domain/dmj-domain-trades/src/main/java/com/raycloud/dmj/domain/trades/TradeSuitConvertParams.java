package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/6/10 1:43 下午
 */
public class TradeSuitConvertParams implements Serializable {

    private static final long serialVersionUID = 1650831351239354117L;

    /**
     * 1:套件转实体商品 2：实体商品转套件
     */
    private Integer suitsConvertType;
    /**
     * 套件转实体商品-忽略库存 1
     */
    private Integer allowableStockOut;

    public Integer getSuitsConvertType() {
        return suitsConvertType;
    }

    public void setSuitsConvertType(Integer suitsConvertType) {
        this.suitsConvertType = suitsConvertType;
    }

    public Integer getAllowableStockOut() {
        return allowableStockOut;
    }

    public void setAllowableStockOut(Integer allowableStockOut) {
        this.allowableStockOut = allowableStockOut;
    }
}

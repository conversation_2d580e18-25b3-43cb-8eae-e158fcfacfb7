package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Date 2022/12/9
 * <AUTHOR>
 */
public class TradeConvertUtils {

    /**
     * 构建简单的Trade类
     */
    public static List<Trade> buildSimpleTrades(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return trades;
        }

        List<Trade> tradeList = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            Trade source = new Trade();
            source.setSid(trade.getSid());
            source.setOutSid(trade.getOutSid());
            tradeList.add(source);
        }
        return tradeList;
    }

}

package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.enums.TradeExtraFieldEnum;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.vo.GovSubsidyInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: chenchaochao
 * @Date: 2020/10/26 2:17 下午
 */
public class TradeExtUtils {

    public static Map<Long, TradeExt> tradeExt2Map(List<TradeExt> tradeExts) {
        Map<Long, TradeExt> tradeExtMap = new HashMap<>();
        for (TradeExt tradeExt : tradeExts) {
            tradeExtMap.put(tradeExt.getSid(), tradeExt);
        }
        return tradeExtMap;
    }

    /**
     * 添加指定的key-value到tradeExt的extraField里
     * 只支持添加不存在的key
     */
    public static TradeExt setExtraFieldValue(TradeExt tradeExt, String key, Object value) {
        if (tradeExt == null || key == null || value == null) {
            return tradeExt;
        }

        String extraFields = tradeExt.getExtraFields();
        List<Object> extraFieldsList = tradeExt.getExtraFieldsList();
        Map<String, Object> extraFieldsMap = tradeExt.getExtraFieldsMap();

        if (StringUtils.isEmpty(extraFields)) {
            Map<String, Object> extraFields4Map = Maps.newHashMapWithExpectedSize(2);
            extraFields4Map.put(key,value);
            tradeExt.setExtraFields(JSONObject.toJSONString(extraFields4Map));
            tradeExt.setExtraFieldsMap(JSONObject.parseObject(extraFields, Map.class));
        } else {
            if (MapUtils.isEmpty(extraFieldsMap)) {
                extraFieldsMap = JSONObject.parseObject(extraFields, Map.class);
                tradeExt.setExtraFieldsMap(extraFieldsMap);
            }
            extraFieldsMap.put(key, value);
            tradeExt.setExtraFields(JSONObject.toJSONString(extraFieldsMap));
        }

        if (CollectionUtils.isEmpty(extraFieldsList)) {
            extraFieldsList = new ArrayList<>();
        }
        extraFieldsList.add("$." + key);
        extraFieldsList.add(value);
        tradeExt.setExtraFieldsList(extraFieldsList);

        return tradeExt;
    }

    /**
     * 根据key获取extraFieldMap里的值
     * @param tradeExt
     * @param key
     * @return
     */
    public static Object getExtraFieldValue(TradeExt tradeExt, String key) {
        if (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields()) || StringUtils.isBlank(key)) {
            return null;
        }
        return tradeExt.get(key);
    }

    /**
     * 转成字符串
     */
    public static String getExtraFieldValueStr(TradeExt tradeExt, String key) {
        Object result = getExtraFieldValue(tradeExt, key);
        if(result == null){
            return "";
        }
        return String.valueOf(result);
    }

    /**
     * BTAS订单获取保价金额
     * @param tradeExt
     * @return
     */
    public static Double getInsuranceCost(TradeExt tradeExt){
        Object o = getExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURED_PRICE.getField());
        if (o instanceof Integer){
            return ((Integer) o).doubleValue();
        }else if (o instanceof String){
            return Double.valueOf((String) o);
        }else if (o instanceof BigDecimal) {
            return ((BigDecimal) o).doubleValue();
        }else if (o instanceof Float){
            return ((Float) o).doubleValue();
        } else {
            return (Double)o;
        }
    }

    /**
     * BTAS订单获取保价匹配类型：0：未开启，1：开启未匹配店铺，2：开启且成功匹配店铺
     * @param tradeExt
     * @return
     */
    public static Integer getInsuredPriceType(TradeExt tradeExt){
        Object o = getExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURANCE_COST_TYPE.getField());
        if (null == o){
            return 0;
        }
        return (Integer) o;
    }

    /**
     * 卖家旗帜标签（暂只支持淘宝、天猫）
     * @param tradeExt
     * @return
     */
    public static String getSellerFlagTag(TradeExt tradeExt){
        Object o = getExtraFieldValue(tradeExt, TradeExtraFieldEnum.SELLER_FLAG_TAG.getField());
        if (null == o){
            return "";
        }
        return (String) o;
    }

    public static Map<String, Object> parseExtraFields(TradeExt tradeExt){
        if (tradeExt != null){
            try {
                if (StringUtils.isNotEmpty(tradeExt.getExtraFields())){
                    return JSON.parseObject(tradeExt.getExtraFields(), Map.class);
                }
            }catch (Exception e){
                Logs.error("转换tradeExt中扩展配置出错，jsonStr:" + tradeExt.getExtraFields(), e);
            }
        }
        return new HashMap<>();
    }

    /**
     * 订单保价相加处理
     * @param insuranceCostL
     * @param insuranceCostR
     * @return
     */
    public static Double countInsuranceCost(Double insuranceCostL, Double insuranceCostR){
        if (insuranceCostL == null && insuranceCostR == null){
            return null;
        }
        if (insuranceCostL != null && insuranceCostR != null){
            BigDecimal b1 = new BigDecimal(Double.toString(insuranceCostL));
            BigDecimal b2 = new BigDecimal(Double.toString(insuranceCostR));
            return b1.add(b2).doubleValue();
        }
        if (insuranceCostL == null){
            return insuranceCostR;
        }
        return insuranceCostL;
    }

    /**
     * 订单保价相减处理
     * @param insuranceCostL
     * @param insuranceCostR
     * @return
     */
    public static Double subInsuranceCost(Double insuranceCostL, Double insuranceCostR){
        if (insuranceCostL == null && insuranceCostR == null){
            return null;
        }
        if (insuranceCostL != null && insuranceCostR != null){
            if (insuranceCostR > insuranceCostL){
                return 0.00D;
            }
            BigDecimal b1 = new BigDecimal(Double.toString(insuranceCostL));
            BigDecimal b2 = new BigDecimal(Double.toString(insuranceCostR));
            return b1.subtract(b2).doubleValue();
        }
        if (insuranceCostL == null){
            return null;
        }
        return insuranceCostL;
    }

    public static String getPlatformWarehouseId(TradeExt tradeExt) {
        return (String) getExtraFieldValue(tradeExt, TradeExtraFieldEnum.STORE_ID.getField());
    }

    /**
     * 解析国补3C信息
     * @param tradeExt
     * @return
     */
    public static GovSubsidyInfo parseGovSubsidyInfo(TradeExt tradeExt){
        if (tradeExt == null || StringUtils.isEmpty(tradeExt.getExtraFields())) {
            return null;
        }

        JSONObject extraFields = JSON.parseObject(tradeExt.getExtraFields());
        String govSubsidyInfoStr = extraFields.getString(TradeExtraFieldEnum.GOV_SUBSIDY_INFO.getField());

        if (StringUtils.isNotEmpty(govSubsidyInfoStr)) {
            JSONObject govSubsidyInfoJson = JSON.parseObject(govSubsidyInfoStr);
            return JSON.toJavaObject(govSubsidyInfoJson, GovSubsidyInfo.class);
        }

        return null;
    }

    /**
     * 设置国补3C信息
     * @param tradeExt
     * @return
     */
    public static void setGovSubsidyInfo(TradeExt tradeExt, GovSubsidyInfo govSubsidyInfo) {
        if (tradeExt != null && govSubsidyInfo != null){
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.GOV_SUBSIDY_INFO.getField(), JSON.toJSONString(govSubsidyInfo));
        }
    }

    public static String getLogisticsCode(TradeExt tradeExt) {
        String logisticsCode = StringUtils.trimToEmpty(tradeExt.getLogisticsCode());
        if ("noCode".equals(logisticsCode) || "null".equals(logisticsCode)) {
            logisticsCode = "";
        }
        return logisticsCode;
    }

    public static TradeExt getOrInitTradeExt(TbTrade tbTrade) {
        if(tbTrade.getTradeExt() != null){
            return tbTrade.getTradeExt();
        }
        TradeExt tradeExt = new TradeExt();
        tradeExt.setTid(tbTrade.getTid());
        tradeExt.setCompanyId(tbTrade.getCompanyId());
        tradeExt.setUserId(tbTrade.getUserId());
        tbTrade.setTradeExt(tradeExt);
        return tradeExt;
    }


    public static String getSelfBuiltDepositAmount(TradeExt tradeExt) {
        if (tradeExt != null) {
            Object extraFieldValue = TradeExtUtils.getExtraFieldValue(tradeExt, TradeExtraFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT.getField());
            return extraFieldValue == null ? null : extraFieldValue.toString();
        }
        return null;
    }

    public static String getSelfBuiltPaymentReceivable(TradeExt tradeExt) {
        if (tradeExt != null) {
            Object extraFieldValue = TradeExtUtils.getExtraFieldValue(tradeExt, TradeExtraFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE.getField());
            return extraFieldValue == null ? null : extraFieldValue.toString();
        }
        return null;
    }

    public static boolean ifSelfBuiltDepositAmount(TradeExt tradeExt) {
        return tradeExt != null && (Objects.nonNull(tradeExt.get(TradeExtraFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT.getField())) || Objects.nonNull(tradeExt.get(TradeExtraFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE.getField())));
    }
}

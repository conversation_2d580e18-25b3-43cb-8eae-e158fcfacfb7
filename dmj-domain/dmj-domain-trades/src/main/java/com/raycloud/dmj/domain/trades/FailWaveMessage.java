package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 创建时间：2022/1/16 11:05 上午
 * 创 建 人：sanglin
 */
public class FailWaveMessage implements Serializable {

    private static final long serialVersionUID = 1304271248371480919L;
    /**
     *  订单号
     */
    private Long sid;

    private String outSid;
    /**
     *  错误信息
     */
    private String failMessage;

    /**
     *  位置号
     */
    private String PositionCode;

    private Integer errorCode;

    private Long waveId;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }

    public String getPositionCode() {
        return PositionCode;
    }

    public void setPositionCode(String positionCode) {
        PositionCode = positionCode;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }
}

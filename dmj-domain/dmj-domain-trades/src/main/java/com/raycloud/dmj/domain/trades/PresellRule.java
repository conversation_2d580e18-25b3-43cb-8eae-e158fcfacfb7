package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trade.presell.PresellAutoIdentifyTypeEnum;
import com.raycloud.dmj.domain.trade.presell.PresellAutoSplitTypeEnum;
import com.raycloud.dmj.domain.trade.presell.PresellAutoUnlockEnum;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Description: 预售规则
 *
 * @version Version 1.0
 * <AUTHOR>
 * @Copyright 2016 Git Inc. All rights reserved.
 * @CreateDate on 2016.11.21
 * @Company 杭州光云科技有限公司
 */
@Table(name = "presell_rule")
@Setter
@Getter
public class PresellRule extends TradeRule {

    private static final long serialVersionUID = -4266880815938622066L;

    /**
     * 预售开始时间
     */
    private Date startTime;
    private Long startTimeStamp;//非持久化字段
    /**
     * 预售结束时间
     */
    private Date endTime;
    private Long endTimeStamp;//非持久化字段
    /**
     * 预售是否锁库存 0不锁库存 1锁库存
     */
    private Integer isLockStock;
    /**
     * 预售解锁方式
     *
     * @see PresellAutoUnlockEnum
     */
    private Integer autoUnlock;
    /**
     * 按时间自动解锁时设置的时间（autoUnlock=0前提下）
     */
    private Date unlockTime;
    private Long unlockTimeStamp;//非持久化字段
    /**
     * 按时间自动解锁时设置的订单付款时间之后多少个小时解锁（autoUnlock=0前提下），即 payTime + delayHours >= now时解锁
     */
    private Integer delayHours;
    /**
     * 按时间自动解锁时当库存满足时是否忽略设置的时间强制解锁
     */
    private Integer unlockIfStockNormal;
    /**
     * 参与该预售规则的店铺，多个用逗号分隔
     */
    private String userIds;
    private String shopNames;//非持久化字段

    /**
     * 是否开启预售拆分
     */
    private Integer isSplit;

    /**
     * 平台商品链接
     */
    private String platformItemUrl;
    /**
     * 平台规格关键字
     */
    private String skuPropertiesName;

    private Integer enableStatus;

    /**
     * 自动识别类型
     *
     * @see PresellAutoIdentifyTypeEnum
     */
    private Integer autoIdentifyType;

    /**
     * 自动识别商品关键字
     */
    private String autoIdentifyPropertiesName;

    /**
     * 商品自动拆分类型
     *
     * @see PresellAutoSplitTypeEnum
     */
    private Integer autoSplitType;

    private List<PresellItem> items = new ArrayList<>();//避免迭代出现空指针

    /**
     * 最小应付金额
     */
    private String minPayment;

    /**
     * 最大应付金额
     */
    private String maxPayment;

    /**
     * 是否锁库存
     */
    public Boolean isLockStock() {
        return Objects.nonNull(isLockStock) && isLockStock == 1;
    }

    /**
     * 是否需要处理预售规则的应付金额条件
     */
    public Boolean needProcessPayment(){
        return StringUtils.isNotBlank(minPayment) ||StringUtils.isNotBlank(maxPayment);
    }
    /**
     * 当库存满足时，忽略时间按库存解锁
     */
    public Boolean isUnlockIfStockNormal(){
        return Objects.nonNull(unlockIfStockNormal) && unlockIfStockNormal == 1;
    }
}

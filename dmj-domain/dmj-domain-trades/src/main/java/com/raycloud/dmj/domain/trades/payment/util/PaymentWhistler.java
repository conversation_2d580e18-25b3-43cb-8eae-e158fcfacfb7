package com.raycloud.dmj.domain.trades.payment.util;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;


/**
 * @Description <pre>
 * 金额相关预警日志
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-21
 */
public class PaymentWhistler {


    /**
     * <pre>
     * 强校验模式直接抛出异常 适合测试环境及早发现问题
     * 弱校验模式不抛出异常 记录日志并尝试修复数据 适合线上环境
     * </pre>
     */
    public static final boolean CHECK_HARD_MODEL =  false;

    private static final Logger logger = Logger.getLogger(PaymentWhistler.class);


    public static void checkTradeNumbers(Staff staff,Trade trade){
        if (trade == null) {
            return;
        }
        JSONObject error = null;
        try {
            error = PaymentChecker.checkTradeNumbers(trade, !CHECK_HARD_MODEL);
        }catch (Exception e){
            logger.error(PaymentLogHelper.buildLogHead(trade.getCompanyId(),trade.getSid())
                    .append("checkTradeNumbers error:").append(StringUtils.isNotBlank(e.getMessage())? e.getMessage(): e.getClass().getName()).toString(),e);
        }
        if (error != null) {
            logger.warn( new PaymentLogBuilder(staff,trade.getSid()).append("[金额非法]").append(error.toJSONString()).toString());
            if (CHECK_HARD_MODEL) {
                throw new IllegalArgumentException("订单数值非法:"+error.toJSONString() );
            }
        }
    }

    public static void checkOrderNumbers(Staff staff,Order order){
        if (order == null) {
            return;
        }
        JSONObject error = null;
        try {
            error = PaymentChecker.checkOrderNumbers(order, !CHECK_HARD_MODEL);
        }catch (Exception e){
            logger.error(PaymentLogHelper.buildLogHead(order.getCompanyId(),order.getSid())
                    .append("checkOrderNumbers error:").append(StringUtils.isNotBlank(e.getMessage())? e.getMessage(): e.getClass().getName()).toString(),e);
        }
        if (error != null) {
            logger.warn( new PaymentLogBuilder(staff,order.getSid()).append("[金额非法]").append(error.toJSONString()).toString());
            if (CHECK_HARD_MODEL) {
                throw new IllegalArgumentException("订单数值非法:"+error.toJSONString());
            }
        }
    }



}

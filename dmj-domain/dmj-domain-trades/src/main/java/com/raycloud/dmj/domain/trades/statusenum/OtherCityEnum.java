package com.raycloud.dmj.domain.trades.statusenum;

import org.apache.commons.lang3.StringUtils;

/**
 * 其他特殊的地区字符比较
 */
public enum OtherCityEnum {

    MUNICIPAL_DISTRICTS("市辖区");

    OtherCityEnum(String name) {
        this.name = name;
    }

    private final String name;

    public String getName() {
        return this.name;
    }

    public static boolean doEquals(String name) {
        if (!StringUtils.isNotBlank(name)) {
            return false;
        }
        for (OtherCityEnum value : OtherCityEnum.values()) {
            if (value.getName().startsWith(name) || name.startsWith(value.getName())) {
                return true;
            }
        }
        return false;
    }
}

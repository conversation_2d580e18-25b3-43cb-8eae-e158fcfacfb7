package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_04_08 11:33
 */
@Table(name="trade_fast_memo")
public class TradeFastMemo extends Model {
    private Long id;
    private String memo;
    private Long companyId;
    private Integer descOrder;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getDescOrder() {
        return descOrder;
    }

    public void setDescOrder(Integer descOrder) {
        this.descOrder = descOrder;
    }
}

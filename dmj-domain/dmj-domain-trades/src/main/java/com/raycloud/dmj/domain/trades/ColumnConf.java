package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.erp.db.model.Model;

import java.util.List;

/**
 * Created by ruanyg on 16/5/24.
 */
public class ColumnConf extends Model {

    private static final long serialVersionUID = -535628316343525052L;
    private Long pageId;
    /**
     * 列编码
     */
    private String field;

    /**
     * 列显示标题
     */
    private String title;

    /**
     * 是否可见: 1可见, 0不可见
     */
    private Integer visible;

    /**
     * 列ID
     */
    private Long colId;

    /**
     * 列宽度
     */
    private Integer width;

    /**
     * 列额外配置
     */
    private JSONObject config;

    /**
     * 列适用的环境
     */
    private String env;

    /**
     * 该列配置是否开启了对应的数据权限
     * （目前只针对 成本价/销售价/批发价/进货价/市场价/历史成本价/其他价格123）
     * false - 开启 ； true - 未开启
     */
    private Boolean disabled;


    /**
     * 列在当前页面的排序号
     */
    private Integer sortNo;

    /**
     * 配置 json格式""
     */
    private String filterConfig;

    private List<ColumnConf> subColumnConf;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public JSONObject getConfig() {
        return config;
    }

    public void setConfig(JSONObject config) {
        this.config = config;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getFilterConfig() {
        return filterConfig;
    }

    public void setFilterConfig(String filterConfig) {
        this.filterConfig = filterConfig;
    }

    public List<ColumnConf> getSubColumnConf() {
        return subColumnConf;
    }

    public void setSubColumnConf(List<ColumnConf> subColumnConf) {
        this.subColumnConf = subColumnConf;
    }
}

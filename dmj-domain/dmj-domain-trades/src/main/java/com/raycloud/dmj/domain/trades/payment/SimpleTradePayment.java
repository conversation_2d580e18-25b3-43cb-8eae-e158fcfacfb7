package com.raycloud.dmj.domain.trades.payment;

import lombok.Data;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-08-01
 */
@Data
public class SimpleTradePayment {

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 实付金额
     */
    private String payment;

    /**
     * 平台实付（不变的实付金额）
     */
    private String acPayment;

    /**
     * 订单总成本价,所有商品总成本价之和
     */
    private Double cost;

    /**
     * 总额
     */
    private String totalFee;

    /**
     * 优惠
     */
    private String discountFee;

    /**
     * 理论运费
     */
    private Double theoryPostFee;

    /**
     * 邮费
     */
    private String postFee;

    /**
     * 实际的物流费用，通过weight重量、收货地址，运费模板等字段运算出来
     */
    private String actualPostFee;

    /**
     * 分销金额
     */
    private String saleFee;

    /**
     * sale_price 修复bug 不参与计算
     * TODO 这个实际取的是 saleFee 不清楚原因
     */
    private String salePrice;

    /**
     * 包材成本
     */
    private Double packmaCost;

    /**
     * 毛利润
     */
    private Double grossProfit;

    /**
     * 实付金额
     */
    private String payAmount;
}

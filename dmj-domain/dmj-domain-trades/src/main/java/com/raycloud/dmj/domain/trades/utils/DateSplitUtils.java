package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.TimeSlice;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class DateSplitUtils {

    /**
     * 时间切片
     *
     * @param start       开始时间
     * @param end         结束时间
     * @param appendHours 间隔时间 单位：小时 如果小于0 切分结果会做一个倒序排序
     * @return
     */
    public static List<TimeSlice> dateSplit(Long start, Long end, Integer appendHours) {
        return dateSplit(new Date(start), new Date(end), appendHours);
    }

    /**
     * 时间切片
     *
     * @param start       开始时间
     * @param end         结束时间
     * @param appendHours 间隔时间 单位：小时 如果小于0 切分结果会做一个倒序排序
     * @return
     */
    public static List<TimeSlice> dateSplit(Date start, Date end, Integer appendHours) {
        if (appendHours == null) {
            appendHours = 24;
        }
//TODO 提供默认值
//        if (start == null){
//            start = DateUtils.addMonths(new Date(), -3);
//        }
        if (start != null && end == null){
            end = new Date();
        }
        int sortValue = appendHours > 0 ? 1 : -1;
        List<TimeSlice> timeSlices = splitAsc(start, end, sortValue * appendHours);
        return (sortValue > 0 && timeSlices.size() > 1) ? timeSlices : timeSlices.stream().sorted((a, b) -> b.getStart().compareTo(a.getStart())).collect(Collectors.toList());
    }

    /**
     * 切割时间，按照间隔进行切割
     * @param start
     * @param end
     * @param interval 单位毫秒
     * @return
     */
    public static List<TimeSlice> splitDate(Date start, Date end, Long interval){
        List<TimeSlice> dateSplits = new ArrayList<>();
        Date lastStart = start;
        Date lastEnd = org.apache.commons.lang3.time.DateUtils.addMilliseconds(start, interval.intValue());
        while(lastEnd.getTime() <= end.getTime()){
            TimeSlice dateShard = new TimeSlice();
            dateShard.setStart(lastStart);
            dateShard.setEnd(lastEnd);
            dateSplits.add(dateShard);
            lastStart = lastEnd;
            lastEnd = org.apache.commons.lang3.time.DateUtils.addMilliseconds(lastStart, interval.intValue());
        }
        if(lastEnd.getTime() > end.getTime() && lastStart.getTime() < end.getTime()){
            TimeSlice dateShard = new TimeSlice();
            dateShard.setStart(lastStart);
            dateShard.setEnd(end);
            dateSplits.add(dateShard);
        }

        /*for (int i = 1; i < dateSplits.size(); i++) {
            TimeSlice timeSlice = dateSplits.get(i);
            timeSlice.setStart(addOneSecond(timeSlice.getStart()));
        }*/
        return dateSplits;
    }



    private static List<TimeSlice> splitAsc(Date start, Date end, Integer appendHours) {
        List<TimeSlice> dateSplits = new ArrayList<>();
        if (start == null ){
            dateSplits.add(new TimeSlice(start, end));
            return dateSplits;
        }
        TimeSlice param = new TimeSlice();
        param.setStart(start);
        while (true) {
            param.setStart(start);
            Date tempEndTime = addHours(start, appendHours);
            if (tempEndTime.getTime() >= end.getTime()) {
                tempEndTime = end;
            }
            param.setEnd(tempEndTime);

            dateSplits.add(new TimeSlice(param.getStart(), param.getEnd()));
            start = addHours(start, appendHours);
            if (start.getTime()/1000 >= end.getTime()/1000) {
                break;
            }
            if (param.getEnd().getTime()/1000 >= end.getTime()/1000) {
                break;
            }
        }
        /*for (int i = 1; i < dateSplits.size(); i++) {
            TimeSlice timeSlice = dateSplits.get(i);
            timeSlice.setStart(addOneSecond(timeSlice.getStart()));
        }*/
        return dateSplits;
    }

    private static Date addHours(Date date, int hours) {
        return add(date, Calendar.HOUR_OF_DAY, hours);
    }

    private static Date addOneSecond(Date date) {
        return add(date, Calendar.SECOND, 1);
    }

    private static Date add(final Date date, final int calendarField, final int amount) {
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }
}


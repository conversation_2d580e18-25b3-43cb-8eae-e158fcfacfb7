package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import lombok.Data;

/**
 * @description:
 * @author: pxh
 * @create: 2021-07-30 15:42
 **/
@Data
public class PaymentLinkReqDTO {

    private Order order;

    private OpenLinkConfigEnum openLinkConfig;

    private Trade trade;

    private String tradeSource;
}

package com.raycloud.dmj.domain.trades;

import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/4/7
 * @description 地址解析结果合集
 */
public class ExcelResult implements Serializable {
    private static final long serialVersionUID = -711503548446733575L;

    /**
     * 是否解析完成
     */
    private boolean complete;

    /**
     * 总条目
     */
    private int totalNum;

    /**
     * 当前已解析的条目
     */
    private int currNum;

    /**
     * 解析成功的条目
     */
    private int rightNum;

    /**
     * 解析失败的条目
     */
    private int errorNum;

    /**
     * 成功解析的地址列表
     */
    private List<ExcelAddress> addressList = new ArrayList<>();

    /**
     * 失败行的错误信息
     */
    private List<ErrorData> errorDataList;

    public void addAddress(ExcelAddress address) {
        if (address == null || CollectionUtils.isEmpty(addressList)) {
            addressList = new ArrayList<>();
        }
        addressList.add(address);
    }

    public void addError(ErrorData errorData) {
        if (errorData == null || CollectionUtils.isEmpty(errorDataList)) {
            errorDataList = new ArrayList<>();
        }
        errorDataList.add(errorData);
    }

    public boolean isComplete() {
        return complete;
    }

    public void setComplete(boolean complete) {
        this.complete = complete;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public int getCurrNum() {
        return currNum;
    }

    public void setCurrNum(int currNum) {
        this.currNum = currNum;
    }

    public int getRightNum() {
        return rightNum;
    }

    public void setRightNum(int rightNum) {
        this.rightNum = rightNum;
    }

    public int getErrorNum() {
        return errorNum;
    }

    public void setErrorNum(int errorNum) {
        this.errorNum = errorNum;
    }

    public List<ExcelAddress> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<ExcelAddress> addressList) {
        this.addressList = addressList;
    }

    public List<ErrorData> getErrorDataList() {
        return errorDataList;
    }

    public void setErrorDataList(List<ErrorData> errorDataList) {
        this.errorDataList = errorDataList;
    }


    public static class ErrorData extends ExcelAddress implements Serializable {
        private static final long serialVersionUID = -8933934388346626805L;
        private int col;
        private String error;

        public ErrorData(int col, String error) {
            this.col = col;
            this.error = error;
        }

        public ErrorData(int col, String error, String state, String city, String district) {
            this(col, error);
            super.state = state;
            super.city = city;
            super.district = district;
        }

        public ErrorData(int col, String error, String state, String city, String district,String street) {
            this(col, error);
            super.state = state;
            super.city = city;
            super.district = district;
            super.street = street;
        }

        public int getCol() {
            return col;
        }

        public void setCol(int col) {
            this.col = col;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }
    }

    public static class ExcelAddress implements Serializable {
        private static final long serialVersionUID = 8494379884745450722L;
        private String state;
        private String city;
        private String district;
        private String street;
        private int col;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getDistrict() {
            return district;
        }

        public void setDistrict(String district) {
            this.district = district;
        }

        public String getStreet() {
            return street;
        }

        public void setStreet(String street) {
            this.street = street;
        }

        public int getCol() {
            return col;
        }

        public void setCol(int col) {
            this.col = col;
        }
    }
}
package com.raycloud.dmj.domain.platform.enums;

import com.raycloud.dmj.domain.utils.CommonConstants;

import java.util.HashMap;
import java.util.Map;

import static com.raycloud.dmj.domain.platform.ErrorMatchTypeConstants.*;

/**
 * @description 平台错误信息映射枚举
 * <AUTHOR>
 * @date 2023/3/23 15:33
 */
public enum PlatformErrorMappingEnum {

    /***/
    FXG(CommonConstants.PLAT_FORM_TYPE_FXG, SUB_CODE, CommonConstants.PLAT_FORM_TYPE_FXG + MAPPING_CACHE_KEY_SUFFIX),
    WWXX(CommonConstants.PLAT_FORM_TYPE_WWXX, CODE, CommonConstants.PLAT_FORM_TYPE_WWXX + MAPPING_CACHE_KEY_SUFFIX),
    ALIBABA_C2M(CommonConstants.PLAT_FORM_TYPE_1688_C2M, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_1688_C2M + MAPPING_CACHE_KEY_SUFFIX),
    WXSPH(CommonConstants.PLAT_FORM_TYPE_WXSPH, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_WXSPH + MAPPING_CACHE_KEY_SUFFIX),
    PDD(CommonConstants.PLAT_FORM_TYPE_PDD, SUB_CODE, CommonConstants.PLAT_FORM_TYPE_PDD + MAPPING_CACHE_KEY_SUFFIX),
    ALIBABA(CommonConstants.PLAT_FORM_TYPE_1688, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_1688 + MAPPING_CACHE_KEY_SUFFIX),
    ALIBABA_FX(CommonConstants.PLAT_FORM_TYPE_1688_FX, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_1688_FX + MAPPING_CACHE_KEY_SUFFIX),
    AKC(CommonConstants.PLAT_FORM_TYPE_AKC, CODE, CommonConstants.PLAT_FORM_TYPE_AKC + MAPPING_CACHE_KEY_SUFFIX),
    WD(CommonConstants.PLAT_FORM_TYPE_WD, CODE, CommonConstants.PLAT_FORM_TYPE_WD + MAPPING_CACHE_KEY_SUFFIX),
    THH(CommonConstants.PLATFROM_TYPE_THH, CODE, CommonConstants.PLATFROM_TYPE_THH + MAPPING_CACHE_KEY_SUFFIX),
    YZ(CommonConstants.PLAT_FORM_TYPE_YZ, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_YZ + MAPPING_CACHE_KEY_SUFFIX),
    XHS(CommonConstants.PLAT_FORM_TYPE_XHS, CODE, CommonConstants.PLAT_FORM_TYPE_XHS + MAPPING_CACHE_KEY_SUFFIX),
    YIDINGHUO(CommonConstants.PLAT_FORM_TYPE_YIDINGHUO, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_YIDINGHUO + MAPPING_CACHE_KEY_SUFFIX),
    YIDINGHUO2(CommonConstants.PLAT_FORM_TYPE_YIDINGHUO2, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_YIDINGHUO2 + MAPPING_CACHE_KEY_SUFFIX),
    WXXD(CommonConstants.PLAT_FORM_TYPE_WXXD, CODE_MSG, CommonConstants.PLAT_FORM_TYPE_WXXD + MAPPING_CACHE_KEY_SUFFIX),
    ;

    PlatformErrorMappingEnum(String source, String matchType, String cacheKey){
        this.source = source;
        this.matchType = matchType;
        this.cacheKey = cacheKey;
    }

    private static final Map<String, PlatformErrorMappingEnum> SOURCE_ENUM_MAP = new HashMap<>(PlatformErrorMappingEnum.values().length, 1);

    static {
        PlatformErrorMappingEnum[] values = PlatformErrorMappingEnum.values();
        for (PlatformErrorMappingEnum value : values) {
            SOURCE_ENUM_MAP.put(value.getSource(), value);
        }
    }


    private String source;

    /**
     * todo 考虑一下动态可配，不然要改只能改代码发布
     */
    private String matchType;

    private String cacheKey;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }

    public static PlatformErrorMappingEnum getBySource(String source){
        if (source == null) {
            return null;
        }
        return SOURCE_ENUM_MAP.get(source);
    }

}

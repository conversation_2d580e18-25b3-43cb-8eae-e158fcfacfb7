package com.raycloud.dmj.domain.trades.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数值与字符串转换工具
 * Created by CXW on 16/6/16.
 */
public class NumberUtils {

    public static Long str2Long(String value){
        return str2Long(value, 0L);
    }

    public static Long str2Long(String value, Long defaultValue){
        try {
            return value != null && value.trim().length() > 0 ? Long.valueOf(value) : 0L;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer str2Int(String value){
        return str2Int(value,0);
    }

    public static Integer str2Int(String value, Integer defaultValue){
        try {
            return value != null && value.trim().length() > 0 ? Integer.valueOf(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Double str2Double(String value){
        return str2Double(value,  0.00D);
    }

    public static Double str2Double(String value, Double defaultValue){
        try {
            return value != null && value.trim().length() > 0 ? Double.parseDouble(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static BigDecimal str2Decimal(String value){
        try {
            return value != null && value.trim().length() > 0 ? new BigDecimal(value)  : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static BigDecimal str2Decimal(String value,String defaultValue){
        try {
            return value != null && value.trim().length() > 0 ? new BigDecimal(value)  : new BigDecimal(defaultValue);
        } catch (Exception e) {
            return new BigDecimal(defaultValue);
        }
    }

    public static String decimal2Str(BigDecimal value, Integer precision){
        if (precision == null) {
            precision = 2;
        }
        return value.setScale(precision, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static String long2Str(Long value){
        return long2Str(value, "");
    }

    public static String long2Str(Long value, String defaultValue){
        return value != null ? value.toString() : defaultValue;
    }

    public static String int2Str(Integer value){
        return value != null ? value.toString() : "";
    }

    public static String double2Str(Double value){
        return double2Str(value,2);
    }

    public static String double2Str(Double value, Integer precision){
        return value != null ? (precision == null || precision < 0 ? value.toString() : decimal2Str(new BigDecimal(value),precision)) : "";
    }

    public static Long bool2Long(Boolean value){
        return value == null || !value ? 0L : 1L;
    }

    public static Integer long2Int(Long value){
        return value != null ? value.intValue() : 0;
    }

    public static double nvlDouble(Double num) {
        return nvlDouble(num, 0D);
    }

    public static double nvlDouble(Double num, Double defaultValue) {
        return num == null ? defaultValue : num;
    }

    public static int nvlInteger(Integer num){
        return nvlInteger(num, 0);
    }

    public static int nvlInteger(Integer num, Integer defaultValue){
        return num == null ? defaultValue : num;
    }

    public static long nvlLong(Long num, Long defaultValue){
        return num == null ? defaultValue : num;
    }

    public static long nvlInteger(Long num){
        return nvlInteger(num, 0L);
    }

    public static long nvlInteger(Long num, Long defaultValue){
        return num == null ? defaultValue : num;
    }

    public static double formatDouble(Double v, int scale) {
        return v != null ? new BigDecimal(v).setScale(scale, RoundingMode.HALF_UP).doubleValue() : 0;
    }

    public static Long Integer2Long(Integer value){
        return value == null ?  0L : Long.valueOf(value);
    }

    public static Long negative2Zero(Long num) {
        if (null == num || num < 0) {
            return 0L;
        }
        return num;
    }

    public static Integer negative2Zero(Integer num) {
        if (null == num || num < 0) {
            return 0;
        }
        return num;
    }

    public static Double negative2Zero(Double num) {
        if (null == num || num < 0) {
            return 0D;
        }
        return num;
    }

    public static boolean isEquals(Integer i1, Integer i2){
        return nvlInteger(i1, 0) - nvlInteger(i2, 0) == 0;
    }

    public static boolean isEquals(Long l1, Long l2){
        return negative2Zero(l1) - negative2Zero(l2) == 0;
    }

    public static boolean isEquals(Double d1, Double d2){
        return formatDouble(d1,3) - formatDouble(d2,3) == 0;
    }

    /**
     * 判断v分解成2的幂次方和后,是否包含h
     * @param v
     * @param h 2的幂次方
     * @return boolean
     */
    public static boolean has(Integer v, int h) {
        return v != null && (v | h) == v;
    }

    public static boolean has(Long v, int h) {
        return v != null && (v | h) - v == 0;
    }

    public static BigDecimal nvl(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }

    /**
     * 金额转换保留六位小数点
     * @param v
     * @return
     */
    public static BigDecimal scale(BigDecimal v) {
        return scale(v, 6);
    }

    public static BigDecimal scale(BigDecimal v, int newScale) {
        return v == null ? null : v.setScale(newScale, RoundingMode.HALF_UP);
    }

    public static Integer add(Integer... values) {
        int result = 0;
        for (Integer v : values) {
            result += nvlInteger(v);
        }
        return result;
    }

    public static Integer subtract(Integer... values) {
        int result = nvlInteger(values[0]);
        for (int i = 1; i < values.length; i++) {
            result -= nvlInteger(values[i]);
        }
        return result;
    }

    public static Long add(Long... values) {
        long result = 0;
        for (Long v : values) {
            result += nvlInteger(v);
        }
        return result;
    }

    public static String add(String... values) {
        BigDecimal result = BigDecimal.ZERO;
        for (String v : values) {
            result = result.add(str2Decimal(v, "0"));
        }
        return decimal2Str(result, 2);
    }

    public static String subtract(String... values) {
        BigDecimal result = str2Decimal(values[0], "0");
        for (int i = 1; i < values.length; i++) {
            result = result.subtract(str2Decimal(values[i], "0"));
        }
        return decimal2Str(result, 2);
    }

    public static BigDecimal add(BigDecimal... values) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal v : values) {
            result = result.add(nvl(v));
        }
        return result;
    }

    public static BigDecimal subtract(BigDecimal... values) {
        BigDecimal result = nvl(values[0]);
        for (int i = 1; i < values.length; i++) {
            result = result.subtract(nvl(values[i]));
        }
        return result;
    }

    public static boolean isGreaterThan(Double weight, Double value) {
        try {
            weight = Double.parseDouble(weight.toString());
            value = Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            throw new NumberFormatException("非数字无法转换");
        }
        if (weight == value) {
            return false;
        }
        return weight > value;
    }
    public static BigDecimal strToDecimalElseThrow(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? new BigDecimal(value)  : null;
        } catch (Exception e) {
            throw new NumberFormatException("非数字无法转换");
        }
    }
}

package com.raycloud.dmj.domain.platform;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description 平台错误信息映射关系表
 * <AUTHOR>
 * @date 2023/3/22 17:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "erp_platform_error_mapping", migratable = false)
public class ErpPlatformErrorMapping extends Model {

    private static final long serialVersionUID = -1759918648086426235L;

    /**主键id 自增*/
    private Long id;

    /**
     * 平台来源
     * */
    private String source;

    /**
     * 是否有效
     */
    private Integer enableStatus;

    /**
     * 映射类型
     */
    private String matchType;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 平台错误码
     */
    private String platCode;

    /**
     * 平台错误信息
     */
    private String platMsg;

    /**
     * 平台子错误码
     */
    private String platSubCode;

    /**
     * 平台子错误信息
     */
    private String platSubMsg;

    /**
     * 系统错误码
     */
    private String sysCode;

    /**
     * 系统错误信息
     */
    private String sysMsg;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date modified;
}

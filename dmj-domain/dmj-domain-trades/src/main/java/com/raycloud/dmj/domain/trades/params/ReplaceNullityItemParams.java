package com.raycloud.dmj.domain.trades.params;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReplaceNullityItemParams implements Serializable {

    private static final long serialVersionUID = -5229323736807100222L;
    List<Item> originItems;

    List<Item> replaceItems;

    @Data
    public static class Item implements Serializable {
        private static final long serialVersionUID = 409762693612182009L;
        String itemId;
        String skuId;
        Long sysItemId;
        Long sysSkuId;
        Integer num;
    }

}

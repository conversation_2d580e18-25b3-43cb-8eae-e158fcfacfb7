package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.search.utils.ControllerParamConverter;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;

import static com.raycloud.dmj.Strings.*;

/**
 * 订单查询参数
 *
 * <AUTHOR>
 */
@Table(name = "trade_query_params")
public class TradeQueryParams extends Model {
    private static final long serialVersionUID = 1048432840546830641L;

    public static final Date TIMEOUTACTIONTIMEBEFORE_NULL = new Date(946656000000L);

    public static final Date  TIMESTAMP_MIN_DATE = new Date(-28800000L);

    /**
     * 导出来源
     */
    public String exportSource;

    /**
     * 订单导出Excel来源标识
     */
    public static String TRADE_EXPORT = "/trade/exportTrades";

    /**
     * 系统查询状态：作废订单
     */
    public static final String STATUS_CANCEL = "CANCEL";
    /**
     * 异常状态：挂起
     */
    public static final String STATUS_EXCEP_HALT = "EXCEP_HALT";

    /**
     * 黑名单异常
     */
    public static final String STATUS_EXCEP_BLACK = "EX_BLACK";

    /**
     * 异常状态：退款订单
     */
    public static final String STATUS_EXCEP_REFUND = "EXCEP_REFUND";
    /**
     * 异常状态：商品未匹配异常
     */
    public static final String STATUS_EXCEP_ITEM_UNALLOCATED = "EXCEP_ITEM_UNALLOCATED";
    /**
     * 异常状态：系统商品对应关系修改
     */
    public static final String STATUS_EXCEP_ITEM_RELATION_MODIFIED = "EXCEP_ITEM_RELATION_MODIFIED";
    /**
     * 异常状态：库存不足
     */
    public static final String STATUS_EXCEP_STOCK_INSUFFICIENT = "EXCEP_STOCK_INSUFFICIENT";
    /**
     * 平台变更地址
     */
    public static final String STATUS_EXCEP_ADDRESS_CHANGED = "EX_CHANGE_ADDRESS";
    /**
     * 平台更换商品
     */
    public static final String STATUS_EXCEP_ITEM_CHANGED = "EX_CHANGE_ITEM";
    /**
     * 平台修改备注异常
     */
    public static final String STATUS_EXCEP_SELLERMEMO_UPDATED = "EX_UPDATED_SELLERMEMO";
    /**
     * 部分退款异常
     */
    public static final String STATUS_EXCEP_PART_REFUND = "EX_PART_REFUND";
    /**
     * 快递异常
     */
    public static final String STATUS_EXCEP_UNATTAINABLE = "EX_UNATTAINABLE";
    /**
     * 风控异常订单
     */
    public static final String STATUS_EXCEP_RISK_ORDER = "EX_RISK_ORDER";

    /**
     * 不明确供销商
     */
    public static final String STATUS_EXCEP_AMBIGUITY_FX = "EX_AMBIGUITY_FX";
    /**
     * 分销商反审核
     */
    public static final String STATUS_EXCEP_UNAUDIT_FX = "EX_UNAUDIT_FX";
    /**
     * 分销商未付款
     */
    public static final String STATUS_EXCEP_WAITEPAY_FX= "EX_WAITEPAY_FX";
    /**
     * 供销商打回
     */
    public static final String STATUS_EXCEP_REPULSE_FX= "EX_REPULSE_FX";

    /**
     * 外仓商品待拆分
     */
    public static final String STATUS_EXCEP_PLATFORM_FX_SPLIT = "EX_PLATFORM_FX_SPLIT";

    /**
     * 地址、手机/固话、收件人、商品信息缺失的异常
     */
    public static final String STATUS_EXCEP_LOST_MSG = "EX_LOST_MSG";

    /**
     * 发货异常
     */
    public static final String STATUS_EXCEP_DELIVER = "EX_DELIVER";
    /**
     * 上传发货异常
     */
    public static final String STATUS_EXCEP_UPLOAD_DELIVER = "EX_UPLOAD_DELIVER";


    /**
     * 等待退货入仓异常
     */
    public static final String STATUS_WAITING_RETURN_WMS = "EX_WAITING_RETURN_WMS";
    /**


     /**
     * 套件数量修改异常(曾加或者删除不包含修改)
     */
    public static final String STATUS_EXCEP_SUITE_QUANTITY_CHANGE = "EX_SUITE_QUANTITY_CHANGE";
    /**
     * 普通商品转加工
     */
    public static final String STATUS_EXCEP_PROCESS_CHANGE = "EX_PROCESS_CHANGE";
    /**
     * 套件数量修改异常(曾加或者删除不包含修改)
     */
    public static final String STATUS_EXCEP_COD_REPEAT = "EX_COD_REPEAT";
    /**
     * 缺货已处理异常(pdd订单)
     */
    public static final  String STATUS_EXCEP_STOCK_OUT = "EX_STOCK_OUT";
    /**
     * 等待合并异常
     */
    public static final String STATUS_EXCEP_WAIT_MERGE = "EX_WAIT_MERGE";
    /**
     * 商品停用异常
     */
    public static final String STATUS_EXCEP_ITEM_SHUTOFF = "EX_ITEM_SHUTOFF";
    /**
     * 天猫超市缺货回告异常
     */
    public static final String STATUS_EXCEP_TMCS_STOCK_OUT = "EX_TMCS_STOCK_OUT";

    /**
     * 天猫国际直营缺货回告异常
     */
    public static final String STATUS_EXCEP_TMGJZY_STOCK_OUT = "EX_TMGJZY_STOCK_OUT";
    /**
     * 唯一码下架异常
     */
    public static final String STATUS_EXCEP_UNIQUE_CODE_OFFSHELF = "EX_UNIQUE_CODE_OFFSHELF";

    /**
     * 平台仓未匹配异常
     */
    public static final String STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING = "EX_PLATFORM_WAREHOUSE_MAPPING";

    /**
     * 得物直发-物流模板未匹配
     */
    public static final String STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE = "EX_POISON_NOT_MATCH_EXPRESS_TEMPLATE";

    /**
     * 唯品会未匹配常态合作编码
     */
    public static final String STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH = "EX_VIP_COOPERATION_CODE_NOT_MATCH";

    /**
     * 速卖通全托管 未接单
     */
    public static final String STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION = "EX_SMTQTG_UN_CONFIRM_EXCEPTION";

    /**
     * 系统异常集合
     */
    public static final Set<String>  SYS_EXCEP_SETS = Sets.newHashSet(TradeQueryParams.STATUS_EXCEP_HALT,
            TradeQueryParams.STATUS_EXCEP_REFUND,TradeQueryParams.STATUS_EXCEP_BLACK,TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED,
            TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED,TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT,
            TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED,TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED,
            TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED,TradeQueryParams.STATUS_EXCEP_PART_REFUND,
            TradeQueryParams.STATUS_EXCEP_UNATTAINABLE,TradeQueryParams.STATUS_EXCEP_RISK_ORDER,
            TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX, TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX,
            TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX,TradeQueryParams.STATUS_EXCEP_REPULSE_FX,
            TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT,TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE,
            TradeQueryParams.STATUS_EXCEP_LOST_MSG,TradeQueryParams.STATUS_EXCEP_DELIVER,
            TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER, TradeQueryParams.STATUS_WAITING_RETURN_WMS,
            TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE, TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE,
            TradeQueryParams.STATUS_EXCEP_COD_REPEAT, TradeQueryParams.STATUS_EXCEP_STOCK_OUT,
            TradeQueryParams.STATUS_EXCEP_WAIT_MERGE, TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF,
            TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF, TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT,
            TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT, TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING,
            TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH
            ,TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION
    );

    @Override
    @JSONField(serialize = false)
    public String getTable() {
        return super.getTable();
    }

    /**
     * 持久化时的唯一编号
     */
    private Long id;
    /**
     * 这个用于展现自定义查询参数的标题，不用于搜索
     */
    private String name;
    /**
     * 查询的条件的JSON内容，这个是将TradeQueryParams本身自定义的参数json序列化到这个content中，然后保存到数据库
     */
    private String content;
    /**
     * 员工编号，用于存储员工自定义的条件
     */
    private Long staffId;
    /**
     * 公司编号
     */
    private Long companyId;
    /**
     * 业务查询编号(卡盘ID)
     */
    private Long queryId;
    /**
     * 分组号
     */
    @JSONField(serialize = false)
    private Long groupId;
    /**
     * 是否分组
     */
    @JSONField(serialize = false)
    private Integer ifGroup;

    /**
     * 排序值
     */
    private Integer sortOrder;
    /**
     * 0 已被删除的订单，1 正常订单，2 合单后被隐藏的订单，3 分销商审核后的分销订单
     */
    private Integer enableStatus;
    private Date created;
    private Date modified;

    /**
     * 唯品会po单
     */
    private String poNo;

    private String[] poNos;
    /**
     * 唯品会出仓单关联sid
     */
    private Long[] vipSids;

    private String vipPickNo;

    private String vipStorageNo;

    /**
     * null 查询所有 0查询自有库 1查询三方库
     */
    private Integer warehouseType;

    /**
     * 使用新的itemExcepOpen
     */
    private Integer itemExcepOpen;

    /**
     * 1：仅包含，2：排除，3：同时包含； 4：包含
     */
    private Integer onlyContain;

    /**
     * 仅做前端订单查询、订单操作查询：
     * 是否勾选无异常订单    0.未勾选   1.勾选
     */
    private Integer tickExcep = 0;

    public Integer getTickExcep() {
        return tickExcep;
    }

    public void setTickExcep(Integer tickExcep) {
        this.tickExcep = tickExcep;
    }

    private Integer ignoreFilter;


    /**
     * 是否需要脱敏 空或者true 需要
     */
    private Boolean ifSensitive;

    public Boolean getIfSensitive() {
        return ifSensitive;
    }

    public void setIfSensitive(Boolean ifSensitive) {
        this.ifSensitive = ifSensitive;
    }

    /**
     * 是否检查商品
     */
    private boolean checkItem = true;
    /**
     * 物流异常类型
     *
     * @return
     */
    private String logisticsExceptType;
    /**
     * 关注状态： 1已关注  0未关注
     *
     * @return
     */
    private String needTracking;

    /**
     * 是否校验快递禁发地址规则
     */
    private boolean checkAddressRule = false;

    public boolean isCheckAddressRule() {
        return checkAddressRule;
    }

    public void setCheckAddressRule(boolean checkAddressRule) {
        this.checkAddressRule = checkAddressRule;
    }

    /**
     * 是否需要执行tradeFill的逻辑
     */
    private boolean needFill = true;


    public boolean isUndoMergeFill() {
        return undoMergeFill;
    }

    public TradeQueryParams setUndoMergeFill(boolean undoMergeFill) {
        this.undoMergeFill = undoMergeFill;
        return this;
    }

    /**
     * 是否需要执行fill(取消合单这里单独设置为false)
     */
    private boolean undoMergeFill = true;

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public String getNeedTracking() {
        return needTracking;
    }

    public void setNeedTracking(String needTracking) {
        this.needTracking = needTracking;
    }

    /**
     * 订单来源于哪里 分销系统里记为分销商id（companyId）
     */
    private String sourceId;

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    private String destId;

    /**
     * 订单导出用来截断后续插入避免分批插入时页偏移
     */
    private Date inserted = null;

    /**
     * 播报商品类目 0-否 1-是
     */
    private Integer categoryVoice;

    /**
     * 预售订单解锁范围
     * QUERY_ORDER：处理查询结果
     * ALL_ORDER：处理所有未解锁的预售订单
     */
    private UnlockPresellTradeRangeEnum unlockPresellTradeRange;

    /**
     * 预售订单解锁条件
     * <p>
     * <p>
     * ("SATISFY_STOCK_UNLOCK", "忽略计划发货时间，预售商品满足库存就解锁")
     * ("IGNORE_STOCK_DELIVERY_TIME_UNLOCK", "忽略库存，预售商品计划发货时间到期就解锁")
     * ("SATISFY_STOCK_DELIVERY_TIME_UNLOCK", "预售商品满足库存且假话发货时间到期解锁")
     */
    private UnlockPresellConditionEnum unlockPresellCondition;

    /**
     * 预售订单解锁顺序
     * ("DELIVERY_TIME_AND_PAY_TIME", "先按计划时间发货排序，计划发货时间相同再按付款时间依次解锁")
     * ("PAY_TIME", "仅按付款时间依次解锁")
     */
    private UnlockPresellSortEnum unlockPresellSort;

    /**
     * 解锁拆分
     * ("SPLIT_INCOMPATIBLE_ORDER_AND_UNLOCK_SATISFY_ORDER", "拆分不符解锁条件的商品，仅解锁符合条件的商品"),
     * ("SPLIT_INCOMPATIBLE_ORDER_AND_LOCK_ORDER", "拆分不符解锁条件的商品，所有商品均不执行解锁"),
     * ("UNSPLIT_AND_LOCK_AND_APPLY_STOCK", "不拆分，不解锁，但在智能解锁时为不满的解锁条件的商品预留库存"),
     */
    private UnlockPresellSplitEnum unlockPresellSplit;

    /**
     * 库存扣除
     * ("INCLUDE_NORMAL_ORDER_STOCK", "库存不会扣除未审核的正常订单占用"),
     * ("EXCLUDE_NORMAL_ORDER_STOCK", "库存扣除未审核的正常订单占用后再给预售订单解锁"),
     */
    private UnlockPresellStockEnum unlockPresellStock;

    /**
     * 导出时，是否对运单号做分批处理。
     * 1：是
     * 0：否
     * */
    private Integer batch = 0;

    private Integer useHasNext;

    /**
     * 查询待包装支持一单多包
     */
    private Boolean supportMultiPackPrint;

    /**
     * 查询待包装支持已发货
     */
    private Boolean supportPackSellerSend;

    public Integer getUseHasNext() {
        return useHasNext;
    }

    public void setUseHasNext(Integer useHasNext) {
        this.useHasNext = useHasNext;
    }

    public Boolean getSupportMultiPackPrint() {
        return supportMultiPackPrint;
    }

    public void setSupportMultiPackPrint(Boolean supportMultiPackPrint) {
        this.supportMultiPackPrint = supportMultiPackPrint;
    }

    public Boolean getSupportPackSellerSend() {
        return supportPackSellerSend;
    }

    public void setSupportPackSellerSend(Boolean supportPackSellerSend) {
        this.supportPackSellerSend = supportPackSellerSend;
    }

    public void setBatch(Integer batch){
        if (batch==null){
            this.batch = 0;
        } else {
            this.batch = batch;
        };
    }

    public Integer getBatch(){
        return this.batch;
    }

    public UnlockPresellStockEnum getUnlockPresellStock() {
        return unlockPresellStock;
    }

    public void setUnlockPresellStock(UnlockPresellStockEnum unlockPresellStock) {
        this.unlockPresellStock = unlockPresellStock;
    }

    public UnlockPresellTradeRangeEnum getUnlockPresellTradeRange() {
        return unlockPresellTradeRange;
    }

    public UnlockPresellSplitEnum getUnlockPresellSplit() {
        return unlockPresellSplit;
    }

    public void setUnlockPresellSplit(UnlockPresellSplitEnum unlockPresellSplit) {
        this.unlockPresellSplit = unlockPresellSplit;
    }

    public void setUnlockPresellTradeRange(UnlockPresellTradeRangeEnum unlockPresellTradeRange) {
        this.unlockPresellTradeRange = unlockPresellTradeRange;
    }

    public UnlockPresellConditionEnum getUnlockPresellCondition() {
        return unlockPresellCondition;
    }

    public void setUnlockPresellCondition(UnlockPresellConditionEnum unlockPresellCondition) {
        this.unlockPresellCondition = unlockPresellCondition;
    }

    public UnlockPresellSortEnum getUnlockPresellSort() {
        return unlockPresellSort;
    }

    public void setUnlockPresellSort(UnlockPresellSortEnum unlockPresellSort) {
        this.unlockPresellSort = unlockPresellSort;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getDestId() {
        return destId;
    }

    public void setDestId(String destId) {
        this.destId = destId;
    }

    public Integer getIgnoreFilter() {
        return ignoreFilter;
    }

    public TradeQueryParams setIgnoreFilter(Integer ignoreFilter) {
        this.ignoreFilter = ignoreFilter;
        return this;
    }

    public Integer getOnlyContain() {
        return onlyContain;
    }

    public void setOnlyContain(Integer onlyContain) {
        this.onlyContain = onlyContain;
    }

    public Integer getItemExcepOpen() {
        return itemExcepOpen;
    }

    public void setItemExcepOpen(Integer itemExcepOpen) {
        this.itemExcepOpen = itemExcepOpen;
    }

    public Long getId() {
        return id;
    }

    public TradeQueryParams setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public TradeQueryParams setName(String name) {
        this.name = name;
        return this;
    }

    public String getContent() {
        return content;
    }

    public TradeQueryParams setContent(String content) {
        this.content = content;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public TradeQueryParams setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public TradeQueryParams setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public Long getQueryId() {
        return queryId;
    }

    public TradeQueryParams setQueryId(Long queryId) {
        this.queryId = queryId;
        return this;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getIfGroup() {
        return ifGroup;
    }

    public void setIfGroup(Integer ifGroup) {
        this.ifGroup = ifGroup;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public TradeQueryParams setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
        return this;
    }

    public Date getCreated() {
        return created;
    }

    public TradeQueryParams setCreated(Date created) {
        this.created = created;
        return this;
    }

    public Date getModified() {
        return modified;
    }

    public TradeQueryParams setModified(Date modified) {
        this.modified = modified;
        return this;
    }

    public String getPoNo() {
        return poNo;
    }

    public TradeQueryParams setPoNo(String poNo) {
        this.poNo = poNo;
        return this;
    }

    public String[] getPoNos() {
        return poNos;
    }

    public void setPoNos(String[] poNos) {
        this.poNos = poNos;
    }

    public Long[] getVipSids() {
        return vipSids;
    }

    public void setVipSids(Long[] vipSids) {
        this.vipSids = vipSids;
    }

    public String getVipPickNo() {
        return vipPickNo;
    }

    public TradeQueryParams setVipPickNo(String vipPickNo) {
        this.vipPickNo = vipPickNo;
        return this;
    }

    public String getVipStorageNo() {
        return vipStorageNo;
    }

    public TradeQueryParams setVipStorageNo(String vipStorageNo) {
        this.vipStorageNo = vipStorageNo;
        return this;
    }

    //============================================== 非持久化字段，只作查询参数使用 ==============================================

    private Long[] userIds;
    private String[] taobaoIds;

    private Long[] sourceIds;
    private Long[] destIds;

    private Long[] warehouseIds;

    private Long[] sid;
    private Long[] shortId;
    private String[] tid;
    private String[] outSids;

    private String mixKey;
    private String sids;

    private String timeType;
    private Date startTime;
    private Date endTime;
    private Date logisticsWarningStartTime;
    private Date logisticsWarningEndTime;
    private Date deliveryStartTime;
    private Date deliveryEndTime;


    /**
     * 订单来源
     */
    private String []tradeFrom;
    /**
     * 承诺时间
     */
    private Date timeoutActionTime;

    /**
     * 承诺时间在多久之后,仅timeoutActionTimeType=0下生效
     */
    private Integer timeoutActionTimeAfter;

    /**
     * 承诺时间查询Type 0：默认，1-过滤已超时
     */
    private Integer timeoutActionTimeType;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人手机
     */
    private String receiverMobile;

    /**
     * 收件人姓名（适用于jd，pdd 加密搜索）
     */
    private String receiverNameL;
    /**
     * 收件人手机 （适用于jd，pdd 加密搜索）
     */
    private String receiverMobileL;
    /**
     * 收件人固话 （适用于jd，pdd 加密搜索）
     */
    private String receiverPhoneL;
    /**
     * 手机后四位尾号
     */
    private String mobileTail;
    /**
     * 收件人固话
     */
    private String receiverPhone;
    /**
     * 收件的省市区 P1@C11$D111$D112@C12$D121@C13,P2@C21,P3，其中P代表省，C代表市，D代表区县
     * 格式 @前面是省，@后面是市，$后面是区县
     */
    private String receiverArea;
    /**
     * 收件地址
     */
    private String receiverAddress;
    /**
     * 收件人省份
     */
    private String receiverState;
    /**
     * 收件人市
     */
    private String receiverCity;
    /**
     * 收件人区
     */
    private String receiverDistrict;

    /**
     * 收件人街道
     */
    private String[] receiverStreets;

    private Integer addressType;

    private Integer itemCountStart;
    private Integer itemCountEnd;
    private Integer itemKindStart;
    private Integer itemKindEnd;
    private Double netWeightStart;
    private Double netWeightEnd;
    private Double weightStart;
    private Double weightEnd;
    /**
     * 订单实付查询下限值
     */
    private Double paymentUpperLimit;
    /**
     * 订单实付查询上限值
     */
    private Double paymentLowerLimit;
    /**
     * 运费金额上限值
     */
    private Double postFeeUpperLimit;
    /**
     * 运费金额下限值
     */
    private Double postFeeLowerLimit;


    /**
     * 理论运费金额上限值
     */
    private Double theoryPostFeeUpperLimit;
    /**
     * 理论运费金额下限值
     */
    private Double theoryPostFeeLowerLimit;


    /**
     * 优惠金额上限值
     */
    private Double discountFeeUpperLimit;
    /**
     * 优惠金额下限值
     */
    private Double discountFeeLowerLimit;
    /**
     * 缺货数量下限
     */
    private Integer insufficientNumStart;
    /**
     * 缺货数量上限
     */
    private Integer insufficientNumEnd;

    /**
     * 缺货比例 0 0%, 1 100%, 2 0%~100%
     */
    private List<Integer> insufficientRate;

    private Integer hasSysMemo;
    /**
     * 系统备注
     */
    private String sysMemo;

    private Integer hasMemo;
    /**
     * 便签
     */
    private String memos;

    /**
     * 买家昵称／旺旺号
     */
    private String[] buyerNick;
    /**
     * 买家ID
     */
    private String[] openUid;
    /**
     * 客户昵称
     * 将会合并到buyerNick 和 tradeType 查询条件中
     */
    private String[] customerNick;
    /**
     * 是否有买家留言 0-没有 1-有  2-有未处理留言 3-无留言+已处理留言
     */
    private Integer hasBuyerMessage;
    /**
     * 买家留言
     */
    private String buyerMessage;
    /**
     * 是否有买家备注  0-没有 1-有  2-有未处理备注 3-无备注+已处理备注
     */
    private Integer hasSellerMemo;
    /**
     * 卖家备注
     */
    private String sellerMemo;
    /**
     * 卖家旗帜, 红、黄、绿、蓝、紫 分别对应 1、2、3、4、5，0表示灰色，也就是未设置旗帜
     */
    private Integer[] sellerFlags;
    /**
     * 留言备注标注
     * 0 无买家留言和卖家备注
     * 1 有买家留言
     * 2 有卖家备注
     * 3 有买家留言或卖家备注
     * 4 有未处理留言或备注
     */
    private Integer containMemo;
    /**留言关键字,当牌价留言或者备注存在此关键字时命中*/
    private String memoKeyWord;
    private String tradeType;

    /**
     * 对应订单上的原始type字段
     */
    private String type;
    private String excludeTradeType;
    private Integer containOrExclude;
    private String[] sysStatus;
    private String[] o2oSysStatus;
    private String[] statusList;
    private String[] exceptionStatus;
    private String[] stockStatus;
    /**
     * 平台真实的状态
     */
    private String[] platformStatus;
    private String[] status;

    /**
     * 新版订单类型查询
     */
    private TradeTypeNewParams tradeTypeNewParams;

    /***
     * <pre>
     *     不要被字段命名骗了 这个是同时包含 不是仅包含
     *     不要被字段命名骗了 这个是同时包含 不是仅包含
     *     不要被字段命名骗了 这个是同时包含 不是仅包含
     * </pre>
     */
    private String[] onlyTagIds;
    /***
     * 查询指定标签
     */
    private String[] tagIds;
    /**
     * 排除标签
     * */
    private String[] excludeTagIds;
    /**
     * 排除旗帜
     * */
    private Integer[] excludeSellerFlags;

    /**
     * 查询异常标签
     * */
    private String[] exceptIds;

    /**
     * 查询排除自定义异常标签
     * */
    private String[] excludeExceptIds;

    /**
     * 查询排除异常标签
     * */
    private String[] excludeExceptionStatus;

    /**
     * 卖家备注是否有修改
     * 空值：查询全部
     * 0：无修改
     * 1：有修改
     */
    private Integer sellerMemoUpdate;

    /**
     * 系统属性规格
     */
    private String skuProp;
    /**
     * 平台属性规格
     */
    private String platSkuProp;
    /**
     * 商品（规格）商家编码
     */
    private String outerId;

    /**
     * 源平台商家编码
     */
    private String originPlatformOuterId;
    /**
     * 唯一码
     */
    private String uniqueCode;

    private List<String> uniqueCodes;

    /**
     * 系统商家编码
     */
    private String[] sysOuterIds;
    private String[] excludeSysOuterIds;

    private Long[] sysItemIds;
    private Long[] sysSkuIds;

    private String[] outerIdAndSysItemIds;
    private String[] outerIdAndSysSkuIds;
    private String[] onlyOuterIdAndSysItemIds;
    private String[] onlyOuterIdAndSysSkuIds;
    private String[] excludeOuterIdAndSysItemIds;
    private String[] excludeOuterIdAndSysSkuIds;
    /**
     *  0仅包含-所有，1仅包含-任意，2同时包含
     *  兼容原来的值，默认不传也是仅包含-所有，也就是0
     *
     */
    private Integer onlyOuterIdAndSysSkuIdType;

    public Integer getOnlyOuterIdAndSysSkuIdType() {
        return onlyOuterIdAndSysSkuIdType;
    }

    public void setOnlyOuterIdAndSysSkuIdType(Integer onlyOuterIdAndSysSkuIdType) {
        this.onlyOuterIdAndSysSkuIdType = onlyOuterIdAndSysSkuIdType;
    }

    public Integer getTimeoutActionTimeType() {
        return timeoutActionTimeType;
    }

    public void setTimeoutActionTimeType(Integer timeoutActionTimeType) {
        this.timeoutActionTimeType = timeoutActionTimeType;
    }

    /**
     * 排除商品类型
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     */
    private String[] excludeOrderTypes;

    /**
     * 商品标题
     */
    private String itemTitle;

    /**
     * 平台商品标题
     */
    private String platFormTitle;
    /**
     * 商品简称
     */
    private String shortTitle;
    /**
     * 商品备注
     */
    private String itemRemark;
    /**
     * 规格备注
     */
    private String skuRemark;
    /**
     * 规格别名
     */
    private String skuPropAlias;
    /**
     * 商品识别码
     */
    private String identCode;
    /**
     * 系统商品类目ID
     */
    private Long cid;
    /**
     * skuProp、outerId、itemTitle、platFormTitle itemRemark、skuRemark、skuPropAlias等字段搜索方式，1 精准搜索，  其它 模糊搜索
     */
    private Integer queryType;

    /**
     * 直接根据sid/tid搜索  0：sid  1:tid
     */
    private Integer directQueryType;


    private String itemOrder;
    /**
     * 订单是否分配了快递公司
     */
    private Boolean containExpress;
    /**
     * 订单是否分配了运单号
     */
    private Boolean containOutsid;
    /**
     * 查询待发货订单时指定的发货方式，相关枚举值参见SendGoodTag.TYPE
     */
    private String consignType;
    /**
     * 快递模版id_快递模版type
     */
    private String[] express;
    /**
     * 快递是否可达标志: 0 不可达，1 可达
     */
    private Integer canDelivered;
    /**
     * 根据哪个字段查询
     */
    private String key;
    /**
     * 查询的字段的值
     */
    private String text;
    /**
     * 当key字段对应传入outerId时，启用该字段，0 查询所以，1 仅查询包含单品的订单，2 仅查询包含套件商品订单 ,3 仅查询包含组合商品
     * 5 仅包含-用系统编码查询
     */
    private Integer itemType = 0;

    /**
     * 0/null- 按照套件统计；1-按照套件明细统计
     */
    private Integer miniHotItem;

    /**
     * 爆款打印查询方式 0/null-按商品，1-按款
     */
    private Integer hotItemType;
    /**
     * 波次id
     */
    private Long waveId;

    /**
     * 波次短码。
     */
    private Long waveShortId;
    /**
     * 是否挂起订单
     */
    private Integer isHalt;
    /**
     * 是否异常订单
     */
    private Integer isExcep;


    /**
     * 检查异常，默认为true
     */
    private boolean checkExcep = true;
    /**
     * 是否称重
     */
    private Integer isWeigh;
    /**
     * 是否退款订单
     */
    private Integer isRefund;
    /**
     * 是否作废订单 0未作废, 1已作废
     */
    private Integer isCancel;
    /**
     * 是否加急订单 1加急订单
     */
    private Integer isUrgent;
    /**
     * 快递单打印状态，0未打印，1已打印
     */
    private Integer expressStatus;
    /**
     * 发货单打印状态，0未打印，1已打印
     */
    private Integer deliverStatus;
    /**
     * 拿货单打印状态，0未打印，1已打印
     */
    private Integer assemblyStatus;
    /**
     * 出库单查询 1 出库单查询， 0 非出库单查询，null 都查询
     * null 出库单非出库单都查询
     */
    private Integer isOutstock;
    /**
     * 是否查询预售锁定订单
     * 1 只查询预售锁定订单 isPresell=1
     * 0 不查询预售锁定订单（查询非预售订单、预售解锁订单）isPresell != 1
     * 2 只查询预售解锁订单 isPresell = 2
     * 3 查询系统预售订单 isPresell = 3
     * null 都查询
     */
    private Integer isPresell;
    /**
     * 预售规则ID
     */
    private Long presellRuleId;
    /**
     * 是否有发票
     */
    private Integer hasInvoice;
    /**
     * 分页对象
     */
    private Page page;
    /**
     * 排序对象
     */
    private Sort sort;
    /**
     * 查询哪些字段，默认查询所有字段，多个以逗号分隔
     * solr查询设置的是属性名对应的solr字段名
     * db查询设置的是属性名对应的列名
     */
    private String fields;

    /**
     * 是否需要检查店铺用户是否激活（是否需要过滤掉店铺已停用的订单）
     */
    private Boolean checkActive;
    /**
     * 是否需要查询出大的结果集
     * 默认查询是1000条
     * 设置这个为true的话那么可以查询最多1000条
     */
    private Boolean queryLargeResult;
    /**
     * 0或null 查询订单和总数（默认值）
     * 1 只查询订单不查询总数
     * 2 只查询总数不查询订单
     * 3 表示将符合条件的订单的sid全部插入运行时表中
     * 4 表示将运行时表的sid leftjoin trade查出。
     * 5 订单导出只生成sql并写入任务
     */
    private Integer queryFlag;
    /**
     * 默认为空，当queryFlag=3,4时分别使用
     * */
    private RuntimeIdSnapshot runtimeIdSnapshot =null;
    /**
     * 是否需要查询子订单: true或null 是，false 否
     */
    private Boolean queryOrder;

    /**
     * 前端是否需要返回order信息,用于优化返回内容大小使用
     */
    private Integer needOrder;
    /**
     * 子订单是套件时是否同时查询单品
     */
    private Boolean containsSuitSingle;

    /**
     * 是否在波次中
     */
    private Boolean inWave;

    /**
     * 查询对应订单时，是否先忽略一些状态的校验，留给后面业务自己校验（当前只在待称重查询中使用，因为此时要根据订单抛出具体的不能称重的原因）
     */
    private Boolean validateLater;

    /**
     * 付款天数
     */
    private Integer daysOfPaymentStart;

    private Integer daysOfPaymentEnd;

    /**
     * 下单天数
     */
    private Integer daysOfCreateStart;

    private Integer daysOfCreateEnd;

    /**
     * 勾选条件 1-付款天数 2-下单天数
     */
    private Integer daysSelection;

    /**
     * 承诺发货时间
     */
    private Integer[] deliveryTimes;

    /**
     * 承诺到达时间
     */
    private Integer[] signTimes;

    private Integer printCountStart;

    private Integer printCountEnd;

    /**
     * 合单数最小值
     */
    private Integer mergeNumStart;

    /**
     * 合单数最大值
     */
    private Integer mergeNumEnd;


    public Boolean getReacquire() {
        return reacquire;
    }

    public void setReacquire(Boolean reacquire) {
        this.reacquire = reacquire;
    }

    private Boolean reacquire;


    private String[] numIid;
    /**
     * 平台主商家编码
     */
    private String[] outerIid;
    private String[] skuId;
    /**
     * 普通模版id
     */
    private List<Long> commonIds;

    /**
     * 电子模版id
     */
    private List<Long> cloudIds;

    /**
     * 是否指定查询大表 true:是 false:否
     */
    private Boolean assignTrade = false;

    private String source ;

    private String subSource ;

    /**
     * 支持排除订单来源
     */
    private String excludeSource;

    /**
     * 订单分配的库区类型
     */
    private Integer[] stockRegionTypes;

    /**
     * 是否是 EPR 发货，1：ERP 发货， 0：非 ERP 发货
     * 具体参考 http://doc.raycloud.com/pages/viewpage.action?pageId=30015960
     */
    private Integer isSysConsigned;

    /**
     * 是否允许pgl查询
     */
    private boolean allowedPgl;

    /**
     * 是否允许排除有商品交易关闭的订单
     */
    private boolean allowExcludeClosedTrade = false;

    /**
     * 是否可以中断查询，只需要第一次查询出来的信息
     * 这个参数实际上是说 查库后要不要做填充动作 不是不查库了
     * 这个参数实际上是说 查库后要不要做填充动作 不是不查库了
     * 这个参数实际上是说 查库后要不要做填充动作 不是不查库了
     */
    private boolean breakQuery = false;

    /**
     * 自定义的where，直接拼接到sql的最后，不需要解析
     */
    private String customWhere;

    private boolean containInsufficient = false;

    /**
     * 是否是拼多多脱敏数据
     */
    private boolean pddMask = false;

    /**
     * 对于有系统脱敏的订单需要系统脱敏。默认是空
     * sysMask:0 默认，前端未指定值(不需要脱敏)。 1 需要脱敏处理。 2 不需要脱敏处理。
     * */
    private int sysMask=0;

    /**
     * 需要排除的系统商家编码，仅支持单个
     */
    private String excludeOuterId;

    // 查询待包装订单使用
    private Integer allowUnPrintPack;

    /**
     * 前端传的主商家编码字段
     */
    private String mainOuterId;

    /**
     * 针对淘宝订单的收件人查询和手机号查询
     */
    private Integer queryPlatform;

    private String[] orTids;

    /**
     * 是否需要order唯一码
     */
    private boolean needUniqueCode;

    /**
     * 毛利润区间
     */
    private Double minGrossProfit;

    private Double maxGrossProfit;

    private Double minGrossProfitRate;
    private Double maxGrossProfitRate;

    /**
     * 上传异常类型
     */
    private Long[] uploadErrorType;

    /**
     * 批量验货首单订单号
     */
    private Long firstSid;

    private boolean needTemplateName;

    /**
     * 三合一备注： 买家，卖家，系统
     */
    private String mixMemo;

    /**
     * 附加条件： 商品包含无需发货
     */
    private Integer itemContainNonConsign;

    public Integer getItemContainNonConsign() {
        return itemContainNonConsign;
    }

    public void setItemContainNonConsign(Integer itemContainNonConsign) {
        this.itemContainNonConsign = itemContainNonConsign;
    }

    public String getMixMemo() {
        return mixMemo;
    }

    public void setMixMemo(String mixMemo) {
        this.mixMemo = mixMemo;
    }

    public Integer getDirectQueryType() {
        return directQueryType;
    }

    public void setDirectQueryType(Integer directQueryType) {
        this.directQueryType = directQueryType;
    }

    public String[] getReceiverStreets() {
        return receiverStreets;
    }

    public void setReceiverStreets(String[] receiverStreets) {
        this.receiverStreets = receiverStreets;
    }

    public boolean isNeedUniqueCode() {
        return needUniqueCode;
    }

    public void setNeedUniqueCode(boolean needUniqueCode) {
        this.needUniqueCode = needUniqueCode;
    }

    /**
     * 已付款时间，在这个时间后面的订单不被查询出来
     */
    private Integer minutesAfterPaidOrderAreNotDisplayed;

    private List<Long> supplierIds;

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }


    public boolean isContainInsufficient() {
        return containInsufficient;
    }

    public void setContainInsufficient(boolean containInsufficient) {
        this.containInsufficient = containInsufficient;
    }

    public String getPlatFormTitle() {
        return platFormTitle;
    }

    public void setPlatFormTitle(String platFormTitle) {
        this.platFormTitle = platFormTitle;
    }

    /**
     * 忽略订单暂存时间 默认为true
     */
    private boolean ignoreBeforeDate = true;

    /**
     * 客户端ip
     */
    private String clientIp;

    public boolean isIgnoreBeforeDate() {
        return ignoreBeforeDate;
    }

    public void setIgnoreBeforeDate(boolean ignoreBeforeDate) {
        this.ignoreBeforeDate = ignoreBeforeDate;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    /**
     *  0:智能合并所有符合条件的订单 1:智能合并搜索结果中符合条件的订单
     */
    private Integer manualAutoMergeQueryAll;

    private String logisticsCode;

    private Integer wrapperDescriptionFlag;

    /**
     * JIT常态合作码
     */
    private String cooperationNo;

    /**
     * JITX常态合作编码
     */
    private String cooperationNoJitx;

    public String getCooperationNoJitx() {
        return cooperationNoJitx;
    }

    public void setCooperationNoJitx(String cooperationNoJitx) {
        this.cooperationNoJitx = cooperationNoJitx;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public boolean isAllowedPgl() {
        return allowedPgl;
    }

    public TradeQueryParams setAllowedPgl(boolean allowedPgl) {
        this.allowedPgl = allowedPgl;
        return this;
    }

    public boolean isAllowExcludeClosedTrade() {
        return allowExcludeClosedTrade;
    }

    public void setAllowExcludeClosedTrade(boolean allowExcludeClosedTrade) {
        this.allowExcludeClosedTrade = allowExcludeClosedTrade;
    }

    /**
     * 抖音BIC订单唯一码
     */
    private String bicUniqueCode;

    public String getBicUniqueCode() {
        return bicUniqueCode;
    }

    public void setBicUniqueCode(String bicUniqueCode) {
        this.bicUniqueCode = bicUniqueCode;
    }

    private String btasPictureQualityResult;

    public String getBtasPictureQualityResult() {
        return btasPictureQualityResult;
    }

    public void setBtasPictureQualityResult(String btasPictureQualityResult) {
        this.btasPictureQualityResult = btasPictureQualityResult;
    }

    /**
     * BTAS订单码
     */
    private String btasOrderCode;

    public String getBtasOrderCode() {
        return btasOrderCode;
    }

    public void setBtasOrderCode(String btasOrderCode) {
        this.btasOrderCode = btasOrderCode;
    }

    /**
     * 业务员名称
     */
    private String salesmanName;

    private String caigouCode;

    private String authorId;

    private String authorName;

    /**
     * 暂存区类型
     */
    private String wssName;

    /**
     * 快手经分销id 团长id
     */
    private String distributorId;

    /**
     * 快手经分销昵称 团长昵称
     */
    private String distributorName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 剩余时间
     */
    private Integer remainTimeHours;

    /**
     * 唯一码(关联查询拆分订单)
     */
    private String uniqueCodeSplitQuery;

    /**
     * 将sysStatus按照tradeconfig转换为视图可见的状态值
     *      如待打印存储在其他字段，此时则转换为sysStatus=waitPrint
     *      注意：需要查询全部字段才能使用
     */
    private boolean convertSysStatus;

    private TradeQueryItemTagIdsParams tradeQueryItemTagIdsParams;

    /**
     * 快递公司Ids ,拼接
     */
    private String logisticsCompanyIds;


    /**
     * 卖家昵称
     */
    private String sellerNicks;

    public String getSellerNicks() {
        return sellerNicks;
    }

    public void setSellerNicks(String sellerNicks) {
        this.sellerNicks = sellerNicks;
    }

    /**
     * 二级店铺查询,其他场景使用subSource
     */
    private String subSources;

    public String getSubSources() {
        return subSources;
    }

    public void setSubSources(String subSources) {
        this.subSources = subSources;
    }

    public String getSecondUserId() {
        return secondUserId;
    }

    public void setSecondUserId(String secondUserId) {
        this.secondUserId = secondUserId;
    }

    /**
     *  二级店铺id
     */
    private String secondUserId;

    public String getOrderRefundStatus() {
        return orderRefundStatus;
    }

    public void setOrderRefundStatus(String orderRefundStatus) {
        this.orderRefundStatus = orderRefundStatus;
    }

    /**
     * 订单商品退款状态查询
     */
    private String orderRefundStatus;


    public String getLogisticsCompanyIds() {
        return logisticsCompanyIds;
    }

    public void setLogisticsCompanyIds(String logisticsCompanyIds) {
        this.logisticsCompanyIds = logisticsCompanyIds;
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }


    public String getCaigouCode() {
        return caigouCode;
    }

    public void setCaigouCode(String caigouCode) {
        this.caigouCode = caigouCode;
    }

    public boolean isConvertSysStatus() {
        return convertSysStatus;
    }

    public TradeQueryParams setConvertSysStatus(boolean convertSysStatus) {
        this.convertSysStatus = convertSysStatus;
        return this;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    private boolean allowQueryUnboundUniqueCode = false;

    /**
     * 订单列表查询->商品属性 0-4为order中的type属性
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     * 5 虚拟商品 使用Order中的is_virtual进行判断
     * 7 非加工商品
     */
    private Integer[] itemPops;

    public Integer[] getItemPops() {
        return itemPops;
    }

    public void setItemPops(Integer[] itemPops) {
        this.itemPops = itemPops;
    }
    public Boolean isAssignTrade() {
        return assignTrade;
    }

    public void setAssignTrade(Boolean assignTrade) {
        this.assignTrade = assignTrade;
    }

    public List<Long> getCommonIds() {
        return commonIds;
    }

    public void setCommonIds(List<Long> commonIds) {
        this.commonIds = commonIds;
    }

    public String[] getOuterIid() {
        return outerIid;
    }

    public void setOuterIid(String[] outerIid) {
        this.outerIid = outerIid;
    }

    public String[] getNumIid() {
        return numIid;
    }

    public void setNumIid(Object numIid) {
        if (numIid==null){
            this.numIid=null;
            return;
        }
        if (numIid instanceof String){
            this.numIid = new String[]{(String) numIid};
        }else if (numIid instanceof String[]){
            this.numIid = (String[])numIid;
        }else if (numIid instanceof JSONArray){
            this.numIid = ((JSONArray) numIid).toArray(new String[]{});
        }else {
            throw new RuntimeException("numIid need String or String[] but args is "+numIid.getClass());
        }
    }

    public List<Long> getCloudIds() {
        return cloudIds;
    }

    public void setCloudIds(List<Long> cloudIds) {
        this.cloudIds = cloudIds;
    }

    public Integer[] getExcludeSellerFlags() {
        return excludeSellerFlags;
    }

    public void setExcludeSellerFlags(Integer[] excludeSellerFlags) {
        this.excludeSellerFlags = excludeSellerFlags;
    }

    public String[] getExcludeTagIds() {
        return excludeTagIds;
    }

    public void setExcludeTagIds(String[] excludeTagIds) {
        this.excludeTagIds = excludeTagIds;
    }

    /**
     * 是否显示系统商品主商家编码（除了配置项控制外，开放平台需要显示）
     */
    private boolean showSysItemOuterId = false;

    public boolean isShowSysItemOuterId() {
        return showSysItemOuterId;
    }

    public void setShowSysItemOuterId(boolean showSysItemOuterId) {
        this.showSysItemOuterId = showSysItemOuterId;
    }

    /**
     *
     */
    private Boolean useNewQuery;



    /**
     * 数据来源是否来自页面勾选
     */
    private boolean pageSelect = false;

    public Boolean isUseNewQuery() {
        return useNewQuery;
    }

    public void setUseNewQuery(Boolean useNewQuery) {
        this.useNewQuery = useNewQuery;
    }

    public String[] getTaobaoIds() {
        if (taobaoIds != null && taobaoIds.length == 1 && (taobaoIds[0] + 1).equals("0")) {
            return null;
        }
        return taobaoIds;
    }

    public TradeQueryParams setTaobaoIds(String... taobaoIds) {
        this.taobaoIds = taobaoIds;
        return this;
    }


    public Long[] getUserIds() {
        if (userIds != null && userIds.length == 1 && userIds[0] + 1 == 0) {
            return null;
        }
        return userIds;
    }

    public TradeQueryParams setUserIds(Long... userIds) {
        this.userIds = userIds;
        return this;
    }

    public Long[] getSourceIds() {
        if (sourceIds != null && sourceIds.length == 1 && sourceIds[0] + 1 == 0) {
            return null;
        }
        return sourceIds;
    }

    public void setSourceIds(Long[] sourceIds) {
        this.sourceIds = sourceIds;
    }

    public Long[] getDestIds() {
        if (destIds != null && destIds.length == 1 && destIds[0] + 1 == 0) {
            return null;
        }
        return destIds;
    }

    public void setDestIds(Long[] destIds) {
        this.destIds = destIds;
    }

    public Long[] getWarehouseIds() {
        if (warehouseIds != null && warehouseIds.length == 1 && warehouseIds[0] + 1 == 0) {
            return null;
        }
        return warehouseIds;
    }

    public TradeQueryParams setWarehouseIds(Long... warehouseIds) {
        this.warehouseIds = warehouseIds;
        return this;
    }


    public Long[] getSid() {
        return sid;
    }

    public TradeQueryParams setSid(Long... sid) {
        this.sid = sid;
        return this;
    }

    public Long[] getShortId() {
        return shortId;
    }

    public TradeQueryParams setShortId(Long... shortId) {
        this.shortId = shortId;
        return this;
    }

    public String[] getTid() {
        return tid;
    }

    public TradeQueryParams setTid(String... tid) {
        this.tid = tid;
        return this;
    }

    public String[] getOutSids() {
        return outSids;
    }

    public TradeQueryParams setOutSids(String[] outSids) {
        this.outSids = outSids;
        return this;
    }

    public String getMixKey() {
        return mixKey;
    }

    public TradeQueryParams setMixKey(String mixKey) {
        this.mixKey = mixKey;
        return this;
    }

    public String getTimeType() {
        return timeType;
    }

    public TradeQueryParams setTimeType(String timeType) {
        this.timeType = timeType;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public TradeQueryParams setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public TradeQueryParams setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }


    public Date getTimeoutActionTime() {
        return timeoutActionTime;
    }

    public TradeQueryParams setTimeoutActionTime(Date timeoutActionTime) {
        this.timeoutActionTime = timeoutActionTime;
        return this;
    }
    public String[] getTradeFrom() {
        return tradeFrom;
    }

    public void setTradeFrom(String[] tradeFrom) {
        this.tradeFrom = tradeFrom;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getMobileTail() {
        return mobileTail;
    }

    public void setMobileTail(String mobileTail) {
        this.mobileTail = mobileTail;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }


    public String getReceiverNameL() {
        return receiverNameL;
    }

    public void setReceiverNameL(String receiverNameL) {
        this.receiverNameL = receiverNameL;
    }

    public String getReceiverMobileL() {
        return receiverMobileL;
    }

    public void setReceiverMobileL(String receiverMobileL) {
        this.receiverMobileL = receiverMobileL;
    }

    public String getReceiverPhoneL() {
        return receiverPhoneL;
    }

    public void setReceiverPhoneL(String receiverPhoneL) {
        this.receiverPhoneL = receiverPhoneL;
    }

    public String getReceiverArea() {
        return receiverArea;
    }

    public TradeQueryParams setReceiverArea(String receiverArea) {
        this.receiverArea = receiverArea;
        return this;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public TradeQueryParams setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
        return this;
    }

    public Integer getAddressType() {
        return addressType;
    }

    public TradeQueryParams setAddressType(Integer addressType) {
        this.addressType = addressType;
        return this;
    }

    public Integer getItemCountStart() {
        return itemCountStart;
    }

    public TradeQueryParams setItemCountStart(Integer itemCountStart) {
        this.itemCountStart = itemCountStart;
        return this;
    }

    public Integer getItemCountEnd() {
        return itemCountEnd;
    }

    public TradeQueryParams setItemCountEnd(Integer itemCountEnd) {
        this.itemCountEnd = itemCountEnd;
        return this;
    }

    public Integer getItemKindStart() {
        return itemKindStart;
    }

    public TradeQueryParams setItemKindStart(Integer itemKindStart) {
        this.itemKindStart = itemKindStart;
        return this;
    }

    public Integer getItemKindEnd() {
        return itemKindEnd;
    }

    public TradeQueryParams setItemKindEnd(Integer itemKindEnd) {
        this.itemKindEnd = itemKindEnd;
        return this;
    }

    public Double getNetWeightStart() {
        return netWeightStart;
    }

    public TradeQueryParams setNetWeightStart(Double netWeightStart) {
        this.netWeightStart = netWeightStart;
        return this;
    }

    public Double getNetWeightEnd() {
        return netWeightEnd;
    }

    public TradeQueryParams setNetWeightEnd(Double netWeightEnd) {
        this.netWeightEnd = netWeightEnd;
        return this;
    }

    public Double getWeightStart() {
        return weightStart;
    }

    public TradeQueryParams setWeightStart(Double weightStart) {
        this.weightStart = weightStart;
        return this;
    }

    public Double getWeightEnd() {
        return weightEnd;
    }

    public TradeQueryParams setWeightEnd(Double weightEnd) {
        this.weightEnd = weightEnd;
        return this;
    }

    public Integer getInsufficientNumStart() {
        return insufficientNumStart;
    }

    public void setInsufficientNumStart(Integer insufficientNumStart) {
        this.insufficientNumStart = insufficientNumStart;
    }

    public Integer getInsufficientNumEnd() {
        return insufficientNumEnd;
    }

    public void setInsufficientNumEnd(Integer insufficientNumEnd) {
        this.insufficientNumEnd = insufficientNumEnd;
    }

    public List<Integer> getInsufficientRate() {
        return insufficientRate;
    }

    public void setInsufficientRate(List<Integer> insufficientRate) {
        this.insufficientRate = insufficientRate;
    }

    public Integer getHasSysMemo() {
        return hasSysMemo;
    }

    public void setHasSysMemo(Integer hasSysMemo) {
        this.hasSysMemo = hasSysMemo;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public String[] getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String... buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String[] getOpenUid() {
        return openUid;
    }

    public void setOpenUid(String[] openUid) {
        this.openUid = openUid;
    }

    public String[] getCustomerNick() {
        return customerNick;
    }

    public void setCustomerNick(String[] customerNick) {
        this.customerNick = customerNick;
    }

    public Integer getHasBuyerMessage() {
        return hasBuyerMessage;
    }

    public void setHasBuyerMessage(Integer hasBuyerMessage) {
        this.hasBuyerMessage = hasBuyerMessage;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public TradeQueryParams setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
        return this;
    }

    public Integer getHasSellerMemo() {
        return hasSellerMemo;
    }

    public void setHasSellerMemo(Integer hasSellerMemo) {
        this.hasSellerMemo = hasSellerMemo;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public TradeQueryParams setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
        return this;
    }

    public Integer[] getSellerFlags() {
        return sellerFlags;
    }

    public TradeQueryParams setSellerFlags(Integer... sellerFlags) {
        this.sellerFlags = sellerFlags;
        return this;
    }

    public Integer getContainMemo() {
        return containMemo;
    }

    public TradeQueryParams setContainMemo(Integer containMemo) {
        this.containMemo = containMemo;
        return this;
    }

    public String getMemoKeyWord() {
        return memoKeyWord;
    }

    public void setMemoKeyWord(String memoKeyWord) {
        this.memoKeyWord = memoKeyWord;
    }

    public Integer getSellerMemoUpdate() {
        return sellerMemoUpdate;
    }

    public TradeQueryParams setSellerMemoUpdate(Integer sellerMemoUpdate) {
        this.sellerMemoUpdate = sellerMemoUpdate;
        return this;
    }

    public String getTradeType() {
        return tradeType;
    }

    public TradeQueryParams setTradeType(String tradeType) {
        this.tradeType = tradeType;
        return this;
    }

    public String getExcludeTradeType() {
        return excludeTradeType;
    }

    public TradeQueryParams setExcludeTradeType(String excludeTradeType) {
        this.excludeTradeType = excludeTradeType;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getContainOrExclude() {
        return containOrExclude;
    }

    public void setContainOrExclude(Integer containOrExclude) {
        this.containOrExclude = containOrExclude;
    }


    public String[] getO2oSysStatus() {
        return o2oSysStatus;
    }

    public void setO2oSysStatus(String[] o2oSysStatus) {
        this.o2oSysStatus = o2oSysStatus;
    }

    public String[] getSysStatus() {
        return sysStatus;
    }

    public TradeQueryParams setSysStatus(String... sysStatus) {
        this.sysStatus = sysStatus;
        return this;
    }

    public String[] getStatusList() {
        return statusList;
    }

    public TradeQueryParams setStatusList(String[] statusList) {
        this.statusList = statusList;
        return this;
    }

    public String[] getExceptionStatus() {
        return exceptionStatus;
    }

    public TradeQueryParams setExceptionStatus(String... exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
        return this;
    }

    public String[] getStockStatus() {
        return stockStatus;
    }

    public TradeQueryParams setStockStatus(String... stockStatus) {
        this.stockStatus = stockStatus;
        return this;
    }

    public String[] getPlatformStatus() {
        return platformStatus;
    }

    public TradeQueryParams setPlatformStatus(String... platformStatus) {
        this.platformStatus = platformStatus;
        return this;
    }

    public String getSkuProp() {
        return skuProp;
    }

    public TradeQueryParams setSkuProp(String skuProp) {
        this.skuProp = skuProp;
        return this;
    }

    public String getPlatSkuProp() {
        return platSkuProp;
    }

    public TradeQueryParams setPlatSkuProp(String platSkuProp) {
        this.platSkuProp = platSkuProp;
        return this;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOriginPlatformOuterId() {
        return originPlatformOuterId;
    }

    public void setOriginPlatformOuterId(String originPlatformOuterId) {
        this.originPlatformOuterId = originPlatformOuterId;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public List<String> getUniqueCodes() {
        return uniqueCodes;
    }

    public void setUniqueCodes(List<String> uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    public String[] getSysOuterIds() {
        return sysOuterIds;
    }

    public void setSysOuterIds(String[] sysOuterIds) {
        this.sysOuterIds = sysOuterIds;
    }

    public String[] getExcludeSysOuterIds() {
        return excludeSysOuterIds;
    }

    public void setExcludeSysOuterIds(String... excludeSysOuterIds) {
        this.excludeSysOuterIds = excludeSysOuterIds;
    }

    public Long[] getSysItemIds() {
        return sysItemIds;
    }

    public void setSysItemIds(Long[] sysItemIds) {
        this.sysItemIds = sysItemIds;
    }

    public Long[] getSysSkuIds() {
        return sysSkuIds;
    }

    public void setSysSkuIds(Long[] sysSkuIds) {
        this.sysSkuIds = sysSkuIds;
    }

    public String[] getOuterIdAndSysItemIds() {
        return outerIdAndSysItemIds;
    }

    public void setOuterIdAndSysItemIds(String[] outerIdAndSysItemIds) {
        this.outerIdAndSysItemIds = outerIdAndSysItemIds;
    }

    public String[] getOuterIdAndSysSkuIds() {
        return outerIdAndSysSkuIds;
    }

    public void setOuterIdAndSysSkuIds(String[] outerIdAndSysSkuIds) {
        this.outerIdAndSysSkuIds = outerIdAndSysSkuIds;
    }

    public String[] getExcludeOuterIdAndSysItemIds() {
        return excludeOuterIdAndSysItemIds;
    }

    public void setExcludeOuterIdAndSysItemIds(String[] excludeOuterIdAndSysItemIds) {
        this.excludeOuterIdAndSysItemIds = excludeOuterIdAndSysItemIds;
    }

    public String[] getExcludeOuterIdAndSysSkuIds() {
        return excludeOuterIdAndSysSkuIds;
    }

    public void setExcludeOuterIdAndSysSkuIds(String[] excludeOuterIdAndSysSkuIds) {
        this.excludeOuterIdAndSysSkuIds = excludeOuterIdAndSysSkuIds;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getItemRemark() {
        return itemRemark;
    }

    public void setItemRemark(String itemRemark) {
        this.itemRemark = itemRemark;
    }

    public String getSkuRemark() {
        return skuRemark;
    }

    public void setSkuRemark(String skuRemark) {
        this.skuRemark = skuRemark;
    }

    public String getSkuPropAlias() {
        return skuPropAlias;
    }

    public void setSkuPropAlias(String skuPropAlias) {
        this.skuPropAlias = skuPropAlias;
    }

    public String getIdentCode() {
        return identCode;
    }

    public void setIdentCode(String identCode) {
        this.identCode = identCode;
    }

    public Long getCid() {
        return cid;
    }

    public TradeQueryParams setCid(Long cid) {
        this.cid = cid;
        return this;
    }

    public Integer getQueryType() {
        if (queryType == null) {
            return 0;
        }
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public String getItemOrder() {
        return itemOrder;
    }

    public TradeQueryParams setItemOrder(String itemOrder) {
        this.itemOrder = itemOrder;
        return this;
    }

    public Boolean getContainExpress() {
        return containExpress;
    }

    public TradeQueryParams setContainExpress(Boolean containExpress) {
        this.containExpress = containExpress;
        return this;
    }

    public Boolean getContainOutsid() {
        return containOutsid;
    }

    public void setContainOutsid(Boolean containOutsid) {
        this.containOutsid = containOutsid;
    }

    public String getConsignType() {
        return consignType;
    }

    public TradeQueryParams setConsignType(String consignType) {
        this.consignType = consignType;
        return this;
    }

    public String[] getExpress() {
        return express;
    }

    public TradeQueryParams setExpress(String[] express) {
        this.express = express;
        return this;
    }

    public Integer getCanDelivered() {
        return canDelivered;
    }

    public TradeQueryParams setCanDelivered(Integer canDelivered) {
        this.canDelivered = canDelivered;
        return this;
    }

    public String getKey() {
        return key;
    }

    public TradeQueryParams setKey(String key) {
        this.key = key;
        return this;
    }

    public String getText() {
        return text;
    }

    public TradeQueryParams setText(String text) {
        this.text = text;
        return this;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public Integer getMiniHotItem() {
        return miniHotItem;
    }

    public void setMiniHotItem(Integer miniHotItem) {
        this.miniHotItem = miniHotItem;
    }

    public Integer getHotItemType() {
        return hotItemType;
    }

    public void setHotItemType(Integer hotItemType) {
        this.hotItemType = hotItemType;
    }

    public Long getWaveId() {
        return waveId;
    }

    public TradeQueryParams setWaveId(Long waveId) {
        this.waveId = waveId;
        return this;
    }

    public Long getWaveShortId(){ return waveShortId; }

    public TradeQueryParams setWaveShortId(Long waveShortId){ this.waveShortId = waveShortId;return this;}

    public Integer getIsHalt() {
        return isHalt;
    }

    public TradeQueryParams setIsHalt(Integer isHalt) {
        this.isHalt = isHalt;
        return this;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public TradeQueryParams setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
        return this;
    }

    public Integer getIsWeigh() {
        return isWeigh;
    }

    public TradeQueryParams setIsWeigh(Integer isWeigh) {
        this.isWeigh = isWeigh;
        return this;
    }

    public Integer getIsRefund() {
        return isRefund;
    }

    public TradeQueryParams setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
        return this;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public TradeQueryParams setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
        return this;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public TradeQueryParams setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
        return this;
    }

    public Integer getExpressStatus() {
        return expressStatus;
    }

    public TradeQueryParams setExpressStatus(Integer expressStatus) {
        this.expressStatus = expressStatus;
        return this;
    }

    public Integer getDeliverStatus() {
        return deliverStatus;
    }

    public TradeQueryParams setDeliverStatus(Integer deliverStatus) {
        this.deliverStatus = deliverStatus;
        return this;
    }

    public Integer getAssemblyStatus() {
        return assemblyStatus;
    }

    public TradeQueryParams setAssemblyStatus(Integer assemblyStatus) {
        this.assemblyStatus = assemblyStatus;
        return this;
    }

    public Integer getIsOutstock() {
        return isOutstock;
    }

    public TradeQueryParams setIsOutstock(Integer isOutstock) {
        this.isOutstock = isOutstock;
        return this;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public TradeQueryParams setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
        return this;
    }

    public Long getPresellRuleId() {
        return presellRuleId;
    }

    public TradeQueryParams setPresellRuleId(Long presellRuleId) {
        this.presellRuleId = presellRuleId;
        return this;
    }

    public Integer getHasInvoice() {
        return hasInvoice;
    }

    public TradeQueryParams setHasInvoice(Integer hasInvoice) {
        this.hasInvoice = hasInvoice;
        return this;
    }

    public Page getPage() {
        return page;
    }

    public TradeQueryParams setPage(Page page) {
        this.page = page;
        return this;
    }

    public Sort getSort() {
        return sort;
    }

    public TradeQueryParams setSort(Sort sort) {
        this.sort = sort;
        this.initSortConfig(null);
        return this;
    }

    public String getFields() {
        return fields;
    }

    /**
     * @param fields
     * @return
     */
    public TradeQueryParams setFields(String fields) {
        this.fields = doSetFields(fields,this.getQueryId());
        return this;
    }

    /**
     * 待审核，待打印，异常查询需要把 sid,merge_sid,is_excep,user_id 查询出来
     * 其他queryId 最少要有：sid,merge_sid
     *
     * @param fields
     * @return
     */
    public String doSetFields(String fields,Long queryId) {
        if(this.getQueryId()==null ||StringUtils.isBlank(fields) || StringUtils.contains(fields,"*")){
            return fields;
        }
        List<String> list = Strings.getAsStringList(fields, ",", true);
        boolean added = addField(list,queryId);
        if (added) {
            fields = String.join(",", list);
        }
        return fields;
    }

    private boolean addField(List<String> list,Long queryId){
        boolean added = false;
        List<String> needFieldList = Lists.newArrayList(ControllerParamConverter.QUERYID_REQUIRED_FIELDS.getOrDefault(-1L,new ArrayList<>(0)));
        needFieldList.addAll(ControllerParamConverter.QUERYID_REQUIRED_FIELDS.getOrDefault(queryId,new ArrayList<>(0)));
        for (String string : needFieldList) {
            boolean contains = false;
            for(String s: list){
                if(StringUtils.isBlank(s)){
                    continue;
                }
                if(StringUtils.endsWithIgnoreCase(s,string)){
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                list.add("t."+string);
                added = true;
            }
        }

        return added;
    }

    public Boolean getCheckActive() {
        return checkActive;
    }

    public TradeQueryParams setCheckActive(Boolean checkActive) {
        this.checkActive = checkActive;
        return this;
    }

    public Boolean getQueryLargeResult() {
        return queryLargeResult;
    }

    public TradeQueryParams setQueryLargeResult(Boolean queryLargeResult) {
        this.queryLargeResult = queryLargeResult;
        return this;
    }

    public Integer getQueryFlag() {
        return queryFlag;
    }

    public TradeQueryParams setQueryFlag(Integer queryFlag) {
        this.queryFlag = queryFlag;
        return this;
    }

    public Boolean getQueryOrder() {
        return queryOrder;
    }

    public Boolean getContainsSuitSingle() {
        return containsSuitSingle;
    }

    public void setContainsSuitSingle(Boolean containsSuitSingle) {
        this.containsSuitSingle = containsSuitSingle;
    }

    public Boolean getInWave() {
        return inWave;
    }

    public TradeQueryParams setInWave(Boolean inWave) {
        this.inWave = inWave;
        return this;
    }

    public TradeQueryParams setQueryOrder(Boolean queryOrder) {
        this.queryOrder = queryOrder;
        return this;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public TradeQueryParams setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
        return this;
    }

    public Double getPaymentUpperLimit() {
        return paymentUpperLimit;
    }

    public TradeQueryParams setPaymentUpperLimit(Double paymentUpperLimit) {
        this.paymentUpperLimit = paymentUpperLimit;
        return this;
    }

    public Double getPaymentLowerLimit() {
        return paymentLowerLimit;
    }

    public TradeQueryParams setPaymentLowerLimit(Double paymentLowerLimit) {
        this.paymentLowerLimit = paymentLowerLimit;
        return this;
    }

    public Double getPostFeeUpperLimit() {
        return postFeeUpperLimit;
    }

    public void setPostFeeUpperLimit(Double postFeeUpperLimit) {
        this.postFeeUpperLimit = postFeeUpperLimit;
    }

    public Double getPostFeeLowerLimit() {
        return postFeeLowerLimit;
    }

    public void setPostFeeLowerLimit(Double postFeeLowerLimit) {
        this.postFeeLowerLimit = postFeeLowerLimit;
    }

    public String[] getStatus() {
        return status;
    }

    public void setStatus(String[] status) {
        this.status = status;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public String[] getTagIds() {
        return tagIds;
    }

    public TradeQueryParams setTagIds(String[] tagIds) {
        this.tagIds = tagIds;
        return this;
    }

    public String[] getOnlyTagIds() {
        return onlyTagIds;
    }

    public void setOnlyTagIds(String[] onlyTagIds) {
        this.onlyTagIds = onlyTagIds;
    }

    public Integer getNeedOrder() {
        return needOrder;
    }

    public void setNeedOrder(Integer needOrder) {
        this.needOrder = needOrder;
    }
    public String[] getExceptIds() {
        return exceptIds;
    }

    public TradeQueryParams setExceptIds(String[] exceptIds) {
        this.exceptIds = exceptIds;
        return this;
    }

    public Boolean getValidateLater() {
        return validateLater;
    }

    public TradeQueryParams setValidateLater(Boolean validateLater) {
        this.validateLater = validateLater;
        return this;
    }

    public Integer getDaysOfPaymentStart() {
        return daysOfPaymentStart;
    }

    public void setDaysOfPaymentStart(Integer daysOfPaymentStart) {
        this.daysOfPaymentStart = daysOfPaymentStart;
    }

    public Integer getDaysOfPaymentEnd() {
        return daysOfPaymentEnd;
    }

    public void setDaysOfPaymentEnd(Integer daysOfPaymentEnd) {
        this.daysOfPaymentEnd = daysOfPaymentEnd;
    }

    public Integer getDaysOfCreateStart() {
        return daysOfCreateStart;
    }

    public void setDaysOfCreateStart(Integer daysOfCreateStart) {
        this.daysOfCreateStart = daysOfCreateStart;
    }

    public Integer getDaysOfCreateEnd() {
        return daysOfCreateEnd;
    }

    public void setDaysOfCreateEnd(Integer daysOfCreateEnd) {
        this.daysOfCreateEnd = daysOfCreateEnd;
    }

    public Integer getDaysSelection() {
        return daysSelection;
    }

    public void setDaysSelection(Integer daysSelection) {
        this.daysSelection = daysSelection;
    }

    public Integer[] getDeliveryTimes() {
        return deliveryTimes;
    }

    public void setDeliveryTimes(Integer[] deliveryTimes) {
        this.deliveryTimes = deliveryTimes;
    }

    public Integer[] getSignTimes() {
        return signTimes;
    }

    public void setSignTimes(Integer[] signTimes) {
        this.signTimes = signTimes;
    }

    public Integer getIsSysConsigned() {
        return isSysConsigned;
    }

    public void setIsSysConsigned(Integer isSysConsigned) {
        this.isSysConsigned = isSysConsigned;
    }

    public Integer[] getStockRegionTypes() {
        return stockRegionTypes;
    }

    public void setStockRegionTypes(Integer[] stockRegionTypes) {
        this.stockRegionTypes = stockRegionTypes;
    }

    public Integer getManualAutoMergeQueryAll() {
        return manualAutoMergeQueryAll;
    }

    public void setManualAutoMergeQueryAll(Integer manualAutoMergeQueryAll) {
        this.manualAutoMergeQueryAll = manualAutoMergeQueryAll;
    }

    public String getReceiverState() {
        return receiverState;
    }

    public void setReceiverState(String receiverState) {
        this.receiverState = receiverState;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public Integer getHasMemo() {
        return hasMemo;
    }

    public void setHasMemo(Integer hasMemo) {
        this.hasMemo = hasMemo;
    }

    public String getMemos() {
        return memos;
    }

    public void setMemos(String memos) {
        this.memos = memos;
    }

    public String[] getExcludeOrderTypes() {
        return excludeOrderTypes;
    }

    public void setExcludeOrderTypes(String[] excludeOrderTypes) {
        this.excludeOrderTypes = excludeOrderTypes;
    }

    public Integer getQueryPlatform() {
        return queryPlatform;
    }

    public void setQueryPlatform(Integer queryPlatform) {
        this.queryPlatform = queryPlatform;
    }

    public Integer getCategoryVoice() {
        return categoryVoice;
    }

    public void setCategoryVoice(Integer categoryVoice) {
        this.categoryVoice = categoryVoice;
    }


    public boolean isCheckExcep() {
        return checkExcep;
    }

    public TradeQueryParams setCheckExcep(boolean checkExcep) {
        this.checkExcep = checkExcep;
        return this;
    }

    public Date getLogisticsWarningStartTime() {
        return logisticsWarningStartTime;
    }

    public void setLogisticsWarningStartTime(Date logisticsWarningStartTime) {
        this.logisticsWarningStartTime = logisticsWarningStartTime;
    }

    public Date getLogisticsWarningEndTime() {
        return logisticsWarningEndTime;
    }

    public void setLogisticsWarningEndTime(Date logisticsWarningEndTime) {
        this.logisticsWarningEndTime = logisticsWarningEndTime;
    }

    public Date getDeliveryStartTime() {
        return deliveryStartTime;
    }

    public void setDeliveryStartTime(Date deliveryStartTime) {
        this.deliveryStartTime = deliveryStartTime;
    }

    public Date getDeliveryEndTime() {
        return deliveryEndTime;
    }

    public void setDeliveryEndTime(Date deliveryEndTime) {
        this.deliveryEndTime = deliveryEndTime;
    }

    public boolean isPageSelect() {
        return pageSelect;
    }

    public void setPageSelect(boolean pageSelect) {
        this.pageSelect = pageSelect;
    }

    //合并代码时注意与ControllerParamConverter做比对
    public static TradeQueryParams copyParams(TradeControllerParams params) {
        return ControllerParamConverter.copyParams(params);
    }

    public Integer getPrintCountStart() {
        return printCountStart;
    }

    public void setPrintCountStart(Integer printCountStart) {
        this.printCountStart = printCountStart;
    }

    public Integer getPrintCountEnd() {
        return printCountEnd;
    }

    public void setPrintCountEnd(Integer printCountEnd) {
        this.printCountEnd = printCountEnd;
    }

    public Integer getMergeNumStart() {
        return mergeNumStart;
    }

    public void setMergeNumStart(Integer mergeNumStart) {
        this.mergeNumStart = mergeNumStart;
    }

    public Integer getMergeNumEnd() {
        return mergeNumEnd;
    }

    public void setMergeNumEnd(Integer mergeNumEnd) {
        this.mergeNumEnd = mergeNumEnd;
    }

    public String getSource() {
        return source;
    }

    public TradeQueryParams setSource(String source) {
        this.source = source;
        return this;
    }

    public String getSubSource() {
        return subSource;
    }

    public TradeQueryParams setSubSource(String subSource) {
        this.subSource = subSource;
        return this;
    }

    public boolean isCheckItem() {
        return checkItem;
    }

    public TradeQueryParams setCheckItem(boolean checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public boolean isBreakQuery() {
        return breakQuery;
    }

    public TradeQueryParams setBreakQuery(boolean breakQuery) {
        this.breakQuery = breakQuery;
        return this;
    }

    public String getCustomWhere() {
        return customWhere;
    }

    public TradeQueryParams setCustomWhere(String customWhere) {
        this.customWhere = customWhere;
        return this;
    }

    public String[] getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Object skuId) {
        if (skuId==null){
            this.skuId = null;
            return;
        }
        if (skuId instanceof String){
            this.skuId = new String[]{(String)skuId};
        }else if(skuId instanceof String[]){
            this.skuId = (String[])skuId;
        }else if (skuId instanceof JSONArray){
            this.skuId = ((JSONArray) skuId).toArray(new String[]{});
        }else {
            throw new RuntimeException("skuId need String or String[] but args is "+numIid.getClass());
        }
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public Integer getWrapperDescriptionFlag() {
        return wrapperDescriptionFlag;
    }

    public void setWrapperDescriptionFlag(Integer wrapperDescriptionFlag) {
        this.wrapperDescriptionFlag = wrapperDescriptionFlag;
    }

    public boolean isPddMask() {
        return pddMask;
    }

    public TradeQueryParams setPddMask(boolean pddMask) {
        this.pddMask = pddMask;
        return this;
    }

    public String getExcludeOuterId() {
        return excludeOuterId;
    }

    public void setExcludeOuterId(String excludeOuterId) {
        this.excludeOuterId = excludeOuterId;
    }

    public String getExportSource() {
        return exportSource;
    }

    public void setExportSource(String exportSource) {
        this.exportSource = exportSource;
    }
    public Boolean isExcludedNormalOrderStock() {
        return Objects.nonNull(getUnlockPresellStock()) && UnlockPresellStockEnum.EXCLUDE_NORMAL_ORDER_STOCK.equals(getUnlockPresellStock());
    }

    public String[] getOrTids() {
        return orTids;
    }

    public void setOrTids(String[] orTids) {
        this.orTids = orTids;
    }

    public Integer getMinutesAfterPaidOrderAreNotDisplayed() {
        return minutesAfterPaidOrderAreNotDisplayed;
    }

    public void setMinutesAfterPaidOrderAreNotDisplayed(Integer minutesAfterPaidOrderAreNotDisplayed) {
        this.minutesAfterPaidOrderAreNotDisplayed = minutesAfterPaidOrderAreNotDisplayed;
    }

    public Date getInserted() {
        return inserted;
    }

    public void setInserted(Date inserted) {
        this.inserted = inserted;
    }


    public String[] getOnlyOuterIdAndSysItemIds() {
        return onlyOuterIdAndSysItemIds;
    }

    public void setOnlyOuterIdAndSysItemIds(String[] onlyOuterIdAndSysItemIds) {
        this.onlyOuterIdAndSysItemIds = onlyOuterIdAndSysItemIds;
    }

    public String[] getOnlyOuterIdAndSysSkuIds() {
        return onlyOuterIdAndSysSkuIds;
    }

    public void setOnlyOuterIdAndSysSkuIds(String[] onlyOuterIdAndSysSkuIds) {
        this.onlyOuterIdAndSysSkuIds = onlyOuterIdAndSysSkuIds;
    }

    public RuntimeIdSnapshot getRuntimeIdSnapshot() {
        return runtimeIdSnapshot;
    }

    public void setRuntimeIdSnapshot(RuntimeIdSnapshot runtimeIdSnapshot) {
        this.runtimeIdSnapshot = runtimeIdSnapshot;
    }

    public List<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(List<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public Double getDiscountFeeUpperLimit() {
        return discountFeeUpperLimit;
    }

    public void setDiscountFeeUpperLimit(Double discountFeeUpperLimit) {
        this.discountFeeUpperLimit = discountFeeUpperLimit;
    }

    public Double getDiscountFeeLowerLimit() {
        return discountFeeLowerLimit;
    }

    public void setDiscountFeeLowerLimit(Double discountFeeLowerLimit) {
        this.discountFeeLowerLimit = discountFeeLowerLimit;
    }


    private String shippingCarrier;

    private Long combineParcelId;

    /**
     * 放心购商家代发店铺id
     * 对应trade_ext mallMaskId
     */
    private String fxgDfMallMaskIds;

    private Boolean hasSqlGroupBy;

    public Boolean getHasSqlGroupBy() {
        return hasSqlGroupBy;
    }

    public TradeQueryParams setHasSqlGroupBy(Boolean hasSqlGroupBy) {
        this.hasSqlGroupBy = hasSqlGroupBy;
        return this;
    }

    /**
     * 是否脱敏 1：脱敏，其他值不脱敏
     */
    private String sensitive;

    public String getSensitive() {
        return sensitive;
    }

    public void setSensitive(String sensitive) {
        this.sensitive = sensitive;
    }

    /**
     * 参数重置，后端可能一个params多次查询
     */
    public void reset(){
        getContext().setHasUseJoinOrder(true);
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public boolean isAllowQueryUnboundUniqueCode() {
        return allowQueryUnboundUniqueCode;
    }

    public void setAllowQueryUnboundUniqueCode(boolean allowQueryUnboundUniqueCode) {
        this.allowQueryUnboundUniqueCode = allowQueryUnboundUniqueCode;
    }
    public Integer getRemainTimeHours() {
        return remainTimeHours;
    }

    public void setRemainTimeHours(Integer remainTimeHours) {
        this.remainTimeHours = remainTimeHours;
    }
    public Double getMinGrossProfit() {
        return minGrossProfit;
    }

    public void setMinGrossProfit(Double minGrossProfit) {
        this.minGrossProfit = minGrossProfit;
    }

    public Double getMaxGrossProfit() {
        return maxGrossProfit;
    }

    public void setMaxGrossProfit(Double maxGrossProfit) {
        this.maxGrossProfit = maxGrossProfit;
    }

    public String getUniqueCodeSplitQuery() {
        return uniqueCodeSplitQuery;
    }

    public void setUniqueCodeSplitQuery(String uniqueCodeSplitQuery) {
        this.uniqueCodeSplitQuery = uniqueCodeSplitQuery;
    }

    public Long[] getUploadErrorType() {
        return uploadErrorType;
    }

    public void setUploadErrorType(Long[] uploadErrorType) {
        this.uploadErrorType = uploadErrorType;
    }


    public String getFxgDfMallMaskIds() {
        return fxgDfMallMaskIds;
    }

    public void setFxgDfMallMaskIds(String fxgDfMallMaskIds) {
        this.fxgDfMallMaskIds = fxgDfMallMaskIds;
    }


    public String[] getExcludeExceptIds() {
        return excludeExceptIds;
    }

    public void setExcludeExceptIds(String[] excludeExceptIds) {
        this.excludeExceptIds = excludeExceptIds;
    }

    public String[] getExcludeExceptionStatus() {
        return excludeExceptionStatus;
    }

    public void setExcludeExceptionStatus(String[] excludeExceptionStatus) {
        this.excludeExceptionStatus = excludeExceptionStatus;
    }

    public int getSysMask() {
        return sysMask;
    }

    public TradeQueryParams setSysMask(int needSysSensitive) {
        this.sysMask = needSysSensitive;
        return this;
    }

    public TradeTypeNewParams getTradeTypeNewParams() {
        return tradeTypeNewParams;
    }

    public void setTradeTypeNewParams(TradeTypeNewParams tradeTypeNewParams) {
        this.tradeTypeNewParams = tradeTypeNewParams;
    }


    public TradeQueryItemTagIdsParams getTradeQueryItemTagIdsParams() {
        return tradeQueryItemTagIdsParams;
    }

    public void setTradeQueryItemTagIdsParams(TradeQueryItemTagIdsParams tradeQueryItemTagIdsParams) {
        this.tradeQueryItemTagIdsParams = tradeQueryItemTagIdsParams;
    }

    public boolean isNeedFill() {
        return needFill;
    }

    public TradeQueryParams setNeedFill(boolean needFill) {
        this.needFill = needFill;
        return this;
    }

    public Long getFirstSid() {
        return firstSid;
    }

    public void setFirstSid(Long firstSid) {
        this.firstSid = firstSid;
    }

    /**
     * 订单智能单品数量下限
     */
    private Integer smartItemNumStart;
    /**
     * 订单智能单品数量上线
     */
    private Integer smartItemNumEnd;

    public Integer getSmartItemNumStart() {
        return smartItemNumStart;
    }

    public void setSmartItemNumStart(Integer smartItemNumStart) {
        this.smartItemNumStart = smartItemNumStart;
    }

    public Integer getSmartItemNumEnd() {
        return smartItemNumEnd;
    }

    public void setSmartItemNumEnd(Integer smartItemNumEnd) {
        this.smartItemNumEnd = smartItemNumEnd;
    }


    private List<SortConfig> sortConfigs;

    public List<SortConfig> getSortConfigs() {
        return sortConfigs;
    }
    public void initSortConfig(String str){
        if(StringUtils.startsWith(StringUtils.trimToEmpty(str),"[") &&
                StringUtils.endsWith(StringUtils.trimToEmpty(str),"]") ) {
            this.setSortConfigs(JSON.parseArray(str, SortConfig.class));
        }
    }

    public void setSortConfigs(List<SortConfig> sortConfigs) {
        this.sortConfigs = sortConfigs;
    }

    public boolean isNeedTemplateName() {
        return needTemplateName;
    }

    public void setNeedTemplateName(boolean needTemplateName) {
        this.needTemplateName = needTemplateName;
    }

    public String getWssName() {
        return wssName;
    }

    public void setWssName(String wssName) {
        this.wssName = wssName;
    }

    public Integer getAllowUnPrintPack() {
        return allowUnPrintPack;
    }

    public void setAllowUnPrintPack(Integer allowUnPrintPack) {
        this.allowUnPrintPack = allowUnPrintPack;
    }


    public String getExcludeSource() {
        return excludeSource;
    }

    public void setExcludeSource(String excludeSource) {
        this.excludeSource = excludeSource;
    }

    public Integer getTimeoutActionTimeAfter() {
        return timeoutActionTimeAfter;
    }

    public void setTimeoutActionTimeAfter(Integer timeoutActionTimeAfter) {
        this.timeoutActionTimeAfter = timeoutActionTimeAfter;
    }



    //========跨境业务支持 start ==============

    /**
     * 是否打印箱唛
     * 1-已打印,0-未打印
     */
    private Integer printBoxMark;

    public Integer getPrintBoxMark() {
        return printBoxMark;
    }

    public void setPrintBoxMark(Integer printBoxMark) {
        this.printBoxMark = printBoxMark;
    }

    /**
     * 发货单状态
     */
    private List<String> shippingOrderStatus;

    public List<String> getShippingOrderStatus() {
        return shippingOrderStatus;
    }

    public void setShippingOrderStatus(List<String> shippingOrderStatus) {
        this.shippingOrderStatus = shippingOrderStatus;
    }

    /**
     * 发货单号/ASN单号/入库单号
     */
    private List<String> shippingOrderNumber;

    public List<String> getShippingOrderNumber() {
        return shippingOrderNumber;
    }

    public void setShippingOrderNumber(List<String> shippingOrderNumber) {
        this.shippingOrderNumber = shippingOrderNumber;
    }

    /**
     *  发货批次号
     */
    private List<String> deliveryBatchCodes;

    public List<String> getDeliveryBatchCodes() {
        return deliveryBatchCodes;
    }

    public void setDeliveryBatchCodes(List<String> deliveryBatchCodes) {
        this.deliveryBatchCodes = deliveryBatchCodes;
    }

    //========跨境业务支持 start ==============

    @JSONField(serialize = false)
    private transient TradeQueryContext context = null;

    public TradeQueryContext getContext() {
        if (context == null) {
            context = new TradeQueryContext();
            context.setParams(this);
        }
        return context;
    }

    /**
     * 排除的 sids
     */
    private Long[] excludeSids;

    public Long[] getExcludeSids() {
        return excludeSids;
    }

    public void setExcludeSids(Long[] excludeSidIds) {
        this.excludeSids = excludeSidIds;
    }

    /** 淘宝/天猫收货码 */
    private String receiveCode;

    public String getReceiveCode() {
        return receiveCode;
    }

    public void setReceiveCode(String receiveCode) {
        this.receiveCode = receiveCode;
    }

    /**
     * 是否已经送打
     * 1-已送打,2-未送打
     * 为空或其他不处理
     */
    private Integer printStatus;

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    public Integer getContainOrExcludeVipStorageNo() {
        return containOrExcludeVipStorageNo;
    }

    public void setContainOrExcludeVipStorageNo(Integer containOrExcludeVipStorageNo) {
        this.containOrExcludeVipStorageNo = containOrExcludeVipStorageNo;
    }

    Integer containOrExcludeVipStorageNo;




    /**
     * 商品编码打印状态 1.已打印 0.未打印
     */
    private Integer itemPrintStatus;

    public Integer getItemPrintStatus() {
        return itemPrintStatus;
    }

    public void setItemPrintStatus(Integer itemPrintStatus) {
        this.itemPrintStatus = itemPrintStatus;
    }

    public Double getTheoryPostFeeUpperLimit() {
        return theoryPostFeeUpperLimit;
    }

    public void setTheoryPostFeeUpperLimit(Double theoryPostFeeUpperLimit) {
        this.theoryPostFeeUpperLimit = theoryPostFeeUpperLimit;
    }

    public Double getTheoryPostFeeLowerLimit() {
        return theoryPostFeeLowerLimit;
    }

    public void setTheoryPostFeeLowerLimit(Double theoryPostFeeLowerLimit) {
        this.theoryPostFeeLowerLimit = theoryPostFeeLowerLimit;
    }


    public Boolean fillItemQuantity = true;

    public Boolean isFillItemQuantity() {
        return fillItemQuantity;
    }

    public TradeQueryParams setFillItemQuantity(boolean fillItemQuantity) {
        this.fillItemQuantity = fillItemQuantity;
        return this;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }


    public Double getDiscountRateLowerLimit() {
        return discountRateLowerLimit;
    }

    public void setDiscountRateLowerLimit(Double discountRateLowerLimit) {
        this.discountRateLowerLimit = discountRateLowerLimit;
    }

    public Double getDiscountRateUpperLimit() {
        return discountRateUpperLimit;
    }

    public void setDiscountRateUpperLimit(Double discountRateUpperLimit) {
        this.discountRateUpperLimit = discountRateUpperLimit;
    }

    /**
     * 折扣率上限查询
     */
    private Double discountRateUpperLimit;
    /**
     * 折扣率下限查询
     */
    private Double discountRateLowerLimit;



    /**
     * 仅查询和返回原始订单数据(仅返回数据库Trade表中获取到的内容) 跳过任何的数据填充
     */
    private boolean pureTrade = false;

    public boolean isPureTrade() {
        return pureTrade;
    }

    public void setPureTrade(boolean pureTrade) {
        this.pureTrade = pureTrade;
    }

    private boolean transToMainSid;

    public boolean isTransToMainSid() {
        return transToMainSid;
    }

    public void setTransToMainSid(boolean transToMainSid) {
        this.transToMainSid = transToMainSid;
    }


    //时效产品
    private String agedProductCode;

    public String getAgedProductCode() {
        return agedProductCode;
    }

    public void setAgedProductCode(String agedProductCode) {
        this.agedProductCode = agedProductCode;
    }
    /**
     * 极速打单发货 二级查询queryID
     */
    public String rapidPrintQueryIds;

    public String getRapidPrintQueryIds() {
        return rapidPrintQueryIds;
    }

    public void setRapidPrintQueryIds(String rapidPrintQueryIds) {
        this.rapidPrintQueryIds = rapidPrintQueryIds;
    }

    private String skuOuterId;

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    private Boolean debug;

    public Boolean getDebug() {
        return debug;
    }

    public void setDebug(Boolean debug) {
        this.debug = debug;
    }

    public Double getMinGrossProfitRate() {
        return minGrossProfitRate;
    }

    public void setMinGrossProfitRate(Double minGrossProfitRate) {
        this.minGrossProfitRate = minGrossProfitRate;
    }

    public Double getMaxGrossProfitRate() {
        return maxGrossProfitRate;
    }

    public void setMaxGrossProfitRate(Double maxGrossProfitRate) {
        this.maxGrossProfitRate = maxGrossProfitRate;
    }

    /**
     * 服务商揽收单号
     */
    private String fulfillPickupOrderCode;

    public String getFulfillPickupOrderCode() {
        return fulfillPickupOrderCode;
    }

    public void setFulfillPickupOrderCode(String fulfillPickupOrderCode) {
        this.fulfillPickupOrderCode = fulfillPickupOrderCode;
    }

    public Double getCostUpperLimit() {
        return costUpperLimit;
    }

    public void setCostUpperLimit(Double costUpperLimit) {
        this.costUpperLimit = costUpperLimit;
    }

    public Double getCostLowerLimit() {
        return costLowerLimit;
    }

    public void setCostLowerLimit(Double costLowerLimit) {
        this.costLowerLimit = costLowerLimit;
    }

    /**
     * 成本上限值
     */
    private Double costUpperLimit;
    /**
     * 成本下限值
     */
    private Double costLowerLimit;

    /**
     * 打包状态
     * 1：已发未验 2：已发已验
     */
    private Integer[] packageStatus;

    public Integer[] getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(Integer[] packageStatus) {
        this.packageStatus = packageStatus;
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-11-16 10:09
 * @Description 发货上传记录
 */
@Table(name = "consign_record", routerKey = "tradeDbNo")
public class UploadRecord extends Model {

    private static final long serialVersionUID = 9019450819726035116L;

    /**
     * 公司编号
     */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 子订单系统编号
     */
    @Column(name = "order_id", type = Types.BIGINT, key = true)
    private Long orderId;

    /**
     * 系统订单号
     */
    @Column(name = "sid", type = Types.BIGINT, key = true)
    private Long sid;

    /**
     * 平台订单号
     */
    @Column(name = "tid", type = Types.VARCHAR)
    private String tid;

    /**
     * 子订单平台编号
     */
    @Column(name = "oid", type = Types.BIGINT)
    private Long oid;

    /**
     * 上传状态，1 成功  0 失败（默认1）
     */
    @Column(name = "upload_status", type = Types.TINYINT)
    private Integer uploadStatus;

    /**
     * 上传次数
     */
    @Column(name = "upload_count", type = Types.TINYINT)
    private Integer uploadCount;

    /**
     * 创建时间
     */
    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    /**
     * 0 被删除 1正常 2开放平台触发
     */
    @Column(name = "enable_status", type = Types.VARCHAR)
    private Integer enableStatus;

    /**
     * 订单快递模版ID
     */
    private Long templateId;

    /**
     * 订单快递模版ID
     */
    private Integer templateType;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 拓展字段 {"clueId": 507356600060416434}
     */
    private String msg;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getUploadCount() {
        return uploadCount;
    }

    public void setUploadCount(Integer uploadCount) {
        this.uploadCount = uploadCount;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}

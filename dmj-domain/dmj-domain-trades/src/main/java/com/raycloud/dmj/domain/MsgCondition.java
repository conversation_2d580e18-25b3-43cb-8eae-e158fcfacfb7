package com.raycloud.dmj.domain;

import com.raycloud.dmj.domain.user.User;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @program dmj-aftersale-dubbo
 * @create 2021-06-23 14:56
 * @description 订单导入时临时存储消息，用于延迟消费
 **/
@Data
@ToString
public class MsgCondition implements Serializable {

    public static final String CACHE_KEY = "trade-pdd-chat-orderPromise-";

    public static final Integer EXPIRE_TIME = 5 * 60 * 60;

    private User user;

    private String tid;

    private Long promiseId;

    private Integer retryTimes;

    public MsgCondition(User user, String tid, Long promiseId, Integer retryTimes) {
        if(user == null){
            throw new IllegalArgumentException("user不能为空");
        }
        if(tid == null){
            throw new IllegalArgumentException("tid不能为空");
        }
        if(promiseId == null){
            throw new IllegalArgumentException("promiseId不能为空");
        }
        this.user = user;
        this.tid = tid;
        this.promiseId = promiseId;
        this.retryTimes = retryTimes;
    }

    public MsgCondition(User user, String tid, Long promiseId) {
        this(user, tid, promiseId, 2);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MsgCondition that = (MsgCondition) o;
        return Objects.equals(tid, that.tid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(user.getId(), tid, promiseId);
    }
}
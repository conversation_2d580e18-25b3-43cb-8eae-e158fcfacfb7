package com.raycloud.dmj.domain.trades.tradepay.params;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chencha<PERSON><PERSON>
 * @Date: 2020/3/22 2:39 下午
 */
public class TradePayUpdateParams implements Serializable {
    private static final long serialVersionUID = -562351860343683499L;
    /**
     * 支付单状态
     */
    private Integer orderStatus;

    /**
     * 需要修改的id
     */
    private List<Long> ids;

    /**
     * 系统单号
     */
    private List<Long> sids;

    /**
     * 更新系统单号
     */
    private Long updateSid;

    /**
     * 支付类型
     */
    private Integer payType;


    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public Long getUpdateSid() {
        return updateSid;
    }

    public void setUpdateSid(Long updateSid) {
        this.updateSid = updateSid;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }
}

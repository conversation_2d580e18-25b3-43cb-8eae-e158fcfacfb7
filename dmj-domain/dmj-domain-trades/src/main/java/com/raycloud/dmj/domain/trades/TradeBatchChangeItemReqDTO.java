package com.raycloud.dmj.domain.trades;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @description: 批量修改商品
 * @author: peng<PERSON><PERSON><PERSON>
 * @create: 2021-03-29 17:13
 */
@Data
public class TradeBatchChangeItemReqDTO {

    private TradeControllerParams queryParams;

    private List<Order> orderFrontList;

    private TradeBatchChangeItemParams itemParams;

    public void initKMTBindParameter(){
        if (!Integer.valueOf(1).equals(itemParams.getKMFBind())){
            return;
        }
        if (orderFrontList.size() != 1){
            throw new IllegalArgumentException("快卖通只支持绑定一个商品!");
        }
        //只查询供销、奇门订单、1688分销小站
        queryParams.setTradeType("34,48,56,73");
        //待审核
        queryParams.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
        //平台商家编码、numIid、skuId都为空是，需要传入sid
        if((!Objects.equals("outerId", queryParams.getKey()) && !Objects.equals("numIid", queryParams.getKey()) && !Objects.equals("skuId", queryParams.getKey())) || StringUtils.isBlank(queryParams.getText())){
            //没有商家编码的参数,校验系统单号参数
            if (StringUtils.isBlank(queryParams.getSids())){
                throw new IllegalArgumentException("绑定系统商品失败:前端参数传递异常,查询参数(queryParams)缺少商家编码信息!");
            }
        }
        //转换key处理
        if (Objects.equals("outerId", queryParams.getKey())){
            queryParams.setKey("originPlatformOuterId");
        }
        //更新金额
        itemParams.setUpdatePayment(1);
        //预设payment，后续后重新计算payment
        orderFrontList.forEach(t ->{
            t.setPayment(t.getPrice());
        });
        //校验itemParams
        if (StringUtils.isBlank(itemParams.getOriginPlatformNumiId()) && StringUtils.isBlank(itemParams.getOriginPlatformSkuId())){
            throw new IllegalArgumentException("绑定系统商品失败:前端参数传递异常,替换参数(itemParams)缺少源平台商品Id(numIid,skuId)信息!");
        }
    }
}

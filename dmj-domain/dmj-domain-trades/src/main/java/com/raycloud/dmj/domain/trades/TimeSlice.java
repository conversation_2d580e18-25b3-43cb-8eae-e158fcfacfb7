package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.DateUtils;

import java.io.Serializable;
import java.util.Date;

public class TimeSlice implements Serializable {

    private static final long serialVersionUID = 1406946620563801547L;

    private Date start;

    private Date end;

    public TimeSlice(Date start, Date end) {
        this.start = start;
        this.end = end;
    }

    public TimeSlice() {
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }


    @Override
    public String toString() {
        return "TimeSlice{" +
                "start=" + DateUtils.datetime2Str(start) +
                ", end=" +  DateUtils.datetime2Str(end) +
                '}';
    }
}

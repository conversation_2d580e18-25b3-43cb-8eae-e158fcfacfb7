package com.raycloud.dmj.domain.trades;

public class TradeUpdateLogisticsParams {

    //模版id
    private String templateId;

    //查询id
    private String queryId;

    //判断是否是拼多多
    private String checkPdd;

    //批量修改快递的模版类型 0或null-其他 1-普通面单 2-京东手工单
    private Integer templateType;

    //是否保留单号 1 保留 0 回收
    private String cancelOutsid;

    //是否需要获取单号
    private String needGetWaybill;

    // 快递公司id
    private Long logisticsCompanyId;

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public String getNeedGetWaybill() {
        return needGetWaybill;
    }

    public void setNeedGetWaybill(String needGetWaybill) {
        this.needGetWaybill = needGetWaybill;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getQueryId() {
        return queryId;
    }

    public void setQueryId(String queryId) {
        this.queryId = queryId;
    }

    public String getCheckPdd() {
        return checkPdd;
    }

    public void setCheckPdd(String checkPdd) {
        this.checkPdd = checkPdd;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getCancelOutsid() {
        return cancelOutsid;
    }

    public void setCancelOutsid(String cancelOutsid) {
        this.cancelOutsid = cancelOutsid;
    }
}

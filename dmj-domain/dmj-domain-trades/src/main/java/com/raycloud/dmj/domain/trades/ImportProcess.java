package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * 出库单导入进度
 * Created by ya<PERSON><PERSON><PERSON> on 16/9/12.
 */
public class ImportProcess implements Serializable {

    private boolean isComplete;

    private int currNum;

    private int totalNum;

    private long totalCount;

    private int rightNum;

    private int errorNum;

    private Long userId;

    private List<String> errors;

    private Object result;

    private List<String> warnningMsgs;

    public boolean isComplete() {
        return isComplete;
    }

    public void setIsComplete(boolean isComplete) {
        this.isComplete = isComplete;
    }

    public int getCurrNum() {
        return currNum;
    }

    public void setCurrNum(int currNum) {
        this.currNum = currNum;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public int getRightNum() {
        return rightNum;
    }

    public void setRightNum(int rightNum) {
        this.rightNum = rightNum;
    }

    public int getErrorNum() {
        return errorNum;
    }

    public void setErrorNum(int errorNum) {
        this.errorNum = errorNum;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<String> getWarnningMsgs() {
        return warnningMsgs;
    }

    public void setWarnningMsgs(List<String> warnningMsgs) {
        this.warnningMsgs = warnningMsgs;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.link.PaymentLinkFactory;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.payment.link.PaymentLinkReqDTO;
import com.raycloud.dmj.domain.trades.payment.link.PaymentLinkRespDTO;
import com.raycloud.dmj.domain.trades.payment.util.PaymentLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.PaymentLogHelper;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.sun.org.apache.bcel.internal.generic.NEW;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.raycloud.dmj.domain.utils.CommonConstants.*;

/**
 * Created by yangheng on 16/11/28.
 * 关于订单金额计算的工具类：
 * 修改订单计算公式：
 * 1、订单实付 = 订单应付 + 运费 - 所有优惠
 * 2、订单应付 = 所有子订单应付之和
 * 3、所有优惠 = 所有子订单优惠之和 + 订单级别优惠
 * 4、子订单实付 = 子订单数量*子订单单价 - 子订单优惠
 */
public class PaymentUtils {

    private static final Logger logger = Logger.getLogger(PaymentUtils.class);

    private static final String START_PAYMENT_FORMATTER = PaymentLogHelper.PAYMENT_LOG_SIGN + "开始计算金额联动 tid:[{%s}] , sid:[{%s}] , tradePayment:[{%s}] , tradeDiscountFee:[{%s}]";
    public static final String END_PAYMENT_FORMATTER = PaymentLogHelper.PAYMENT_LOG_SIGN + "结束计算金额联动 tid:[{%s}] , sid:[{%s}] , tradePayment:[{%s}] , tradeDiscountFee:[{%s}], tradePayAmount:[{%s}] ,  " +
            "ordersTotalFee:[{%s}] ,ordersTotalPayment:[{%s}],  ordersTotalDiscountFee:[{%s}], ordersTotalPayAmount:[{%s}]";

    private static final Double MAX_DISCOUNT_RATE = 9999.99D;

    /**
     * 计算并设置子订单应付金额,实付金额,优惠金额信息
     *
     * @param order
     */
    public static void calculateOrder(Order order) {
        if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
            return;
        }
        //商品数量 = 原数量 - 赠品数量
        int num = NumberUtils.nvlInteger(order.getNum()) - NumberUtils.nvlInteger(order.getGiftNum());
        if (num < 0) {
            num = 0;
        }
        BigDecimalWrapper total = new BigDecimalWrapper(order.getPrice()).multiply(num);
        order.setTotalFee(total.getString());//应付金额
        order.setDiscountFee(total.subtract(order.getPayment()).getString());//优惠金额
        order.setDiscountRate(PaymentUtils.calculateDiscountRate(order));//折扣率
    }

    public static Double calculateDiscountRate(Order order) {
        if (order == null) {
            return null;
        }
        int num = NumberUtils.nvlInteger(order.getNum()) - NumberUtils.nvlInteger(order.getGiftNum());
        if (num < 0) {
            num = 0;
        }
        double price = NumberUtils.str2Double(order.getPrice());
        if (num == 0 || price == 0D) {
            return 0D;
        }
        double discountRate = new BigDecimalWrapper(order.getPayment()).divide(num).divide(new BigDecimalWrapper(price).get()).getDouble();
        return discountRate <= MAX_DISCOUNT_RATE ? discountRate : MAX_DISCOUNT_RATE;
    }


    public static void autoShareTrade(Trade trade) {
        //   calculateTrade(trade);//先强行算一波，理论上可以不算
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        double totalPayment = 0.00D;
        double tradeDiscountFee = NumberUtils.str2Double(trade.getDiscountFee());
        for (Order order : orderList) {
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            totalPayment += NumberUtils.str2Double(order.getPayment());
        }

        double hasSharedDiscountFee = 0.00D;
        for (int i = 0; i < orderList.size() - 1; i++) {
            Order order = orderList.get(i);
            double shareDiscountFee = autoShareOrder(order, totalPayment, tradeDiscountFee);
            hasSharedDiscountFee += shareDiscountFee;
        }
        double remainDiscountFee = tradeDiscountFee - hasSharedDiscountFee;
        Order order = orderList.get(orderList.size() - 1);
        autoShareOrder(order, remainDiscountFee);
        trade.setDiscountFee("0.00");

    }

    public static double autoShareOrder(Order order, double totalPayment, double tradeDiscountFee) {
        double orderPayment = NumberUtils.str2Double(order.getPayment());
        double shareDiscountFee = 0.00D;
        if (totalPayment > 0.001D) {
            shareDiscountFee = (orderPayment / totalPayment * tradeDiscountFee);
        }
        autoShareOrder(order, shareDiscountFee);
        return shareDiscountFee;
    }

    private static double autoShareOrder(Order order, double shareDiscountFee) {
        BigDecimalWrapper orderDiscountFee = new BigDecimalWrapper(order.getDiscountFee());
        BigDecimalWrapper orderTotalFee = new BigDecimalWrapper(order.getTotalFee());
        orderDiscountFee.add(shareDiscountFee);
        order.setDiscountFee(orderDiscountFee.getString());
        order.setPayment(orderTotalFee.subtract(order.getDiscountFee()).getString());
        return shareDiscountFee;
    }

    public static void calculateTrade(Trade trade, boolean needAutoShare) {
//        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
//            trade.setPayment("0.00");
//            return;
//        }
        calculateTrade(trade);
        if (needAutoShare) {
            autoShareTrade(trade);
        }
    }


    /**
     * 订单金额计算
     *
     * @param trade
     */
    public static void calculateTrade(Trade trade) {
//        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
//            trade.setPayment("0.00");
//            return;
//        }
        calculateTrade(trade, TradeUtils.getOrders4Trade(trade));
    }

    public static void calculateTrade(Trade trade, List<Order> orders) {
        String originPayment = trade.getPayment();
        //所有子订单应付金额
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        //所有子订单优惠
        BigDecimalWrapper ordersDiscountFee = new BigDecimalWrapper();
        //所有子订单买家已付金额
        BigDecimalWrapper ordersPayAmount = new BigDecimalWrapper();
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        //所有子订单成本
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            calculateOrder(order);
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            ordersTotalFee.add(order.getTotalFee());
            ordersDiscountFee.add(order.getDiscountFee());
            ordersPayAmount.add(order.getPayAmount());
        }
        //订单应付 = 所有子订单应付之和
        trade.setTotalFee(ordersTotalFee.getString());
        //订单实付 = 订单应付 + 运费 - 订单优惠 - 所有子订单优惠 + 税费
        BigDecimalWrapper payment = new BigDecimalWrapper(trade.getTotalFee()).add(trade.getPostFee()).subtract(trade.getDiscountFee()).subtract(ordersDiscountFee.get()).add(trade.getTaxFee());
        if (PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId()) && !MathUtils.equals(payment.get(), MathUtils.toBigDecimal(trade.getPayment()))) {
            PaymentLogBuilder append = new PaymentLogBuilder(trade).append("重算订单实付 payment:").append(originPayment).append("->").append(payment.getString()).eq()
                    .append("totalFee", trade.getTotalFee()).add().append("postFee", trade.getPostFee()).add().append("taxFee", trade.getTaxFee())
                    .sub().append("ordersDiscountFee", ordersDiscountFee.getString()).sub().append("tradeDiscountFee", trade.getDiscountFee());
            logger.debug(append.toString());
        }
        trade.setPayment(payment.getString());
        //订单买家已付金额
        trade.setPayAmount(ordersPayAmount.getString());
    }

    public static void calculateTradeDiscountFee(Trade trade, List<Order> orders) {
        //所有子订单应付金额
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        //所有子订单优惠
        BigDecimalWrapper ordersDiscountFee = new BigDecimalWrapper();
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            calculateOrder(order);
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            ordersTotalFee.add(order.getTotalFee());
            ordersDiscountFee.add(order.getDiscountFee());
        }
        //订单应付 = 所有子订单应付之和
        String ordersTotalFeeString = ordersTotalFee.getString();
        trade.setTotalFee(ordersTotalFeeString);
        //订单实付 = 订单应付 + 运费 - 订单优惠 - 所有子订单优惠 + 税费
        BigDecimal tradeDiscountFee = ordersTotalFee.add(trade.getPostFee()).subtract(trade.getPayment()).subtract(ordersDiscountFee.get()).add(trade.getTaxFee()).get();

        if (!MathUtils.equals(tradeDiscountFee, MathUtils.toBigDecimal(trade.getDiscountFee()))) {
            trade.setDiscountFee(MathUtils.toString(tradeDiscountFee));
            if (PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
                logger.info(PaymentLogHelper.buildLogHead(trade).append(String.format("重新计算trade优惠金额[%s] : tradeDiscountFee(%s) = ordersTotalFee(%s) + tradePostFee(%s) - tradePayment(%s) - ordersDiscountFee(%s) + tradeTaxFee(%s)",
                        trade.getSid(), trade.getDiscountFee(), ordersTotalFeeString,trade.getPostFee(),trade.getPayment(),ordersDiscountFee.getString(),trade.getTaxFee())));
            }
        }
    }


    public static BigDecimal getOrderTotalPayment(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        BigDecimal payment = new BigDecimal(0.00D);
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        for (Order order : orders) {
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            //排除套件子订单
            if (order.getCombineId() != null && order.getCombineId() != 0) {
                continue;
            }
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            payment = payment.add(NumberUtils.str2Decimal(order.getPayment(), "0.00"));
        }
        return payment;
    }

    /**
     *  商品成交总额
     * @param trade
     * @param containMergeTrade ture:包含合单 false:不包含
     * @return
     */
    public static BigDecimal totalPayment(Trade trade, boolean containMergeTrade) {
        BigDecimalWrapper payment = new BigDecimalWrapper();
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        if (CollectionUtils.isEmpty(orders)) {
            return payment.get();
        }
        for (Order order : orders) {
            if (order.getEnableStatus() == null || order.getEnableStatus() == 0) {
                continue;
            }
            if (!containMergeTrade && (order.getSid() != null && !trade.getSid().equals(order.getSid()))) {
                continue;
            }
            payment.add(order.getPayment());
        }
        return payment.get();
    }

    public static BigDecimal getOrderTotalPayAmount(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        BigDecimalWrapper payAmount = new BigDecimalWrapper();
        for (Order order : orders) {
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            //排除套件子订单
            if (order.getCombineId() != null && order.getCombineId() != 0) {
                continue;
            }
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            payAmount.add(order.getPayAmount());
        }
        return payAmount.get();
    }

    /**
     * 修改订单的时候计算并设置订单应付金额,优惠金额信息
     *
     * @param trade
     */
    public static void calculateUpdateTrade(Trade trade) {
//        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
//            trade.setPayment("0.00");
//            return;
//        }
        //原订单
        Trade originTrade = trade.getOrigin();
        if (originTrade == null) {
            originTrade = trade;
        }
        //原订单优惠
        String originTradeDiscountFee = originTrade.getDiscountFee();
        //原订单运费
        String originTradePostFee = originTrade.getPostFee();
        //原订单税费
        String originTradeTaxFee = originTrade.getTaxFee();
        //原订单买家已付金额
        String originTradePayAmount = originTrade.getPayAmount();

        //新的子订单
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        //所有子订单应付
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        //所有子订单的优惠
        BigDecimalWrapper ordersTotalDiscountFee = new BigDecimalWrapper();
        //double orderPayAmount =0.00D;
        //所有子订单的分销价
        //double ordersTotalSaleFee = 0.00D;
        //计算子订单的优惠
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            PaymentUtils.calculateOrder(order);
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            ordersTotalFee.add(order.getTotalFee());
            ordersTotalDiscountFee.add(order.getDiscountFee());
            //orderPayAmount += NumberUtils.str2Double(order.getPayAmount());
            //ordersTotalSaleFee += NumberUtils.str2Double(order.getSaleFee());
        }
        //设置总成本价
        trade.setCost(TradeUtils.calculateCost(trade));
        //设置总分销价 KMERP-41330 分销价支持分摊，原逻辑不要了。
        //trade.setSaleFee(String.format("%.2f", ordersTotalSaleFee));
        //订单应付
        String ordersTotalFeeString = ordersTotalFee.getString();
        trade.setTotalFee(ordersTotalFeeString);
        //订单实付 = 订单应付 + 运费 - 订单优惠 - 所有子订单优惠 + 税费
        String payment = ordersTotalFee.add(originTradePostFee).subtract(originTradeDiscountFee).subtract(ordersTotalDiscountFee.get()).add(originTradeTaxFee).getString();

        if (logger.isDebugEnabled() && PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
            String log = new PaymentLogBuilder(trade).append("payment", payment).eq()
                    .append("ordersTotalFee", ordersTotalFeeString).add().append("originTradePostFee", originTradePostFee).add().append("originTradeTaxFee", originTradeTaxFee)
                    .sub().append("originTradeDiscountFee", originTradeDiscountFee).sub().append("ordersTotalDiscountFee", ordersTotalDiscountFee.getString()).toString();
            logger.debug(log);
        }
        trade.setPayment(payment);

        if (PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
            JSONObject tradePaymentView = PaymentViewUtils.getTradePaymentView(new Staff().setCompanyId(trade.getCompanyId()), (TbTrade) trade, null, 0);
            new PaymentLogBuilder(trade).append("金额重算后快照(毛利默认以应付金额计算):").append(tradePaymentView.toJSONString()).printDebug(logger);
        }

    }

    private static boolean validateMutiIds(Long companyId,Long sid,List<Order> orders){
        if (orders == null) {
            return false;
        }
        Map<Long,Integer> orderIdCounter = new HashMap();

        PaymentLogBuilder builder = new PaymentLogBuilder(companyId).append("存在重复的orderId:").startWatch();
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getSid() != null && !sid.equals(order.getSid())) {
                continue;//合单情况，只计算本身order
            }
            Long id = order.getId();
            if (id != null) {
                if (orderIdCounter.containsKey(id)) {
                    orderIdCounter.put(id,orderIdCounter.get(id) + 1);
                }else {
                    orderIdCounter.put(id,1);
                }
            }
        }
        for (Map.Entry<Long, Integer> entry : orderIdCounter.entrySet()) {
            Long id = entry.getKey();
            Integer count = entry.getValue();
            if (count > 1) {
                builder.append(id,count);
            }
        }
        builder.printWarn(logger);
        return builder.isChanged();
    }

    /**
     * 修改运费的时候需要重新计算实付金额
     * 实付金额 = 实付金额 - 原运费 + 新运费
     *
     * @param trade
     * @param postFee
     */
    public static void calculatePostFee(Trade trade, String postFee) {
        //实付金额 = 实付金额 - 原运费 + 新运费
        BigDecimalWrapper newPayment = new BigDecimalWrapper(trade.getPayment()).subtract(trade.getPostFee()).add(postFee);
        trade.setPayment(newPayment.getString());
        trade.setPostFee(postFee);
    }

    /**
     * 用于前端展示，获取订单
     *
     * @param trade
     * @return
     */
    public static String paymentDisplay(Trade trade) {
//        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
//            return "0.00";
//        }

        //实付金额
        String payment = MathUtils.toScaleString(trade.getCompanyId(), trade.getPayment());
        //应付金额
        String totalFee = MathUtils.toScaleString(trade.getCompanyId(), trade.getTotalFee());
        //商品成交总额
        String totalPayment = MathUtils.toScaleString(trade.getCompanyId(), totalPayment(trade, false));
        //运费
        String postFee = MathUtils.toScaleString(trade.getCompanyId(), trade.getPostFee());
        //优惠
        String discountFee = MathUtils.toScaleString(trade.getCompanyId(), totalDiscountFee(trade, false));//订单优惠
        //税费
        String taxFee = MathUtils.toScaleString(trade.getCompanyId(), trade.getTaxFee());
        //return new StringBuilder(payment).append(" = ").append(totalFee).append(" + ").append(postFee).append(" + ").append(taxFee).append(" - ").append(discountFee).toString();
        //应付金额=商品成交总额+运费+税费-订单优惠金额（不包含商品优惠）
        return new StringBuilder(payment).append(" = ").append(totalPayment).append(" + ").append(postFee).append(" + ").append(taxFee).append(" - ").append(discountFee).toString();
    }

    /**
     * 用于前端展示
     * 总优惠额 = trade优惠额 + order优惠额
     *
     * @param trade
     * @return
     */
    public static String totalDiscountFee(Trade trade) {
        return totalDiscountFee(trade, true);
    }

    /**
     * 用于前端展示
     * 优惠金额
     * @param trade 订单
     * @param containOrder 是否包含商品优惠
     * @return
     */
    public static String totalDiscountFee(Trade trade, boolean containOrder) {
        BigDecimalWrapper totalDiscountFee = new BigDecimalWrapper();
        //合单计算总优惠值时需要加上所有原trade的优惠
        if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
            for (MessageMemo memo : trade.getMessageMemos()) {
                String tradeDiscountFee = memo.getDiscountFee();
                totalDiscountFee.add(tradeDiscountFee);
            }
        } else {
            String tradeDiscountFee = trade.getDiscountFee();
            totalDiscountFee.add(tradeDiscountFee);
        }
        if (containOrder) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);

            for (Order order : orders) {
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
                if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                    continue;
                }
                String orderDiscountFee = order.getDiscountFee();
                totalDiscountFee.add(orderDiscountFee);
            }
        }
        return totalDiscountFee.getString();
    }

    public static String getDisplayDouble(String num) {
        return StringUtils.isBlank(num) ? "0.00" : num;
    }

    /**
     * 校验订单金额是否正确
     *
     * @param order
     * @return
     */
    public static boolean checkOrder(Order order, boolean handle) {
        return checkOrder(order);
    }

    /**
     * 校验子订单金额是否正确
     *
     * @param order
     * @return
     */
    public static boolean checkOrder(Order order) {
        boolean correct = true;

        BigDecimal totalFee = MathUtils.toBigDecimal(order.getTotalFee());
        BigDecimal discountFee = MathUtils.toBigDecimal(order.getDiscountFee());
        BigDecimal payment = MathUtils.toBigDecimal(order.getPayment());
        //数量
        int num = order.getNum();
        //计算的应付 = 单价 * 数量
        BigDecimal countTotalFee = MathUtils.multiply(order.getPrice(), order.getNum());

        //校验应付
        if (!MathUtils.equals(countTotalFee, totalFee)) {
            totalFee = countTotalFee;
            order.setTotalFee(MathUtils.toString(countTotalFee));
            correct = false;
        }
        //计算的优惠 = 应付 - 单价
        BigDecimal countDiscountFee = MathUtils.subtract(totalFee, payment);
        if (!MathUtils.equals(countDiscountFee, discountFee) || order.getDiscountFee() == null) {
            order.setDiscountFee(MathUtils.toString(countDiscountFee));
            correct = false;
        }

        return correct;
    }

    /**
     * 校验子订单金额是否正确(批量)
     *
     * @param orders
     * @param handle 是否自动校正数据
     * @param <T>
     * @return
     */
    public static <T extends Order> boolean checkOrders(List<T> orders, boolean handle) {
        boolean correct = true;

        for (Order order : orders) {
            correct &= checkOrder(order, handle);
        }

        return correct;
    }

    /**
     * 校验订单金额是否正确
     *
     * @param trade
     * @return
     */
    public static boolean checkTrade(Trade trade, boolean handle, boolean enableLog) {
        return checkTrade(trade, enableLog);
    }

    /**
     * 校验订单金额是否正确
     *
     * @param trade
     * @return
     */
    public static boolean checkTrade(Trade trade) {
        return checkTrade(trade, true);
    }

    /**
     * 校验订单金额是否正确
     *
     * @param trade
     * @return
     */
    public static boolean checkTrade(Trade trade, boolean enableLog) {
        // 默认为正确
        boolean correct = true;

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        //所有子订单应付金额
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        //所有子订单优惠
        BigDecimalWrapper ordersDiscountFee = new BigDecimalWrapper();
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        for (Order order : orders) {
            correct &= checkOrder(order);
//            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
//                continue;
//            }
            ordersTotalFee.add(order.getTotalFee());
            ordersDiscountFee.add(order.getDiscountFee());
        }
        //校验主单应付
        if (!ordersTotalFee.equalsTo(trade.getTotalFee())) {
            trade.setTotalFee(ordersTotalFee.getString());
            correct = false;
        }
        //计算的主单优惠
        BigDecimal countDiscountFee = ordersTotalFee.add(trade.getPostFee()).add(trade.getTaxFee()).subtract(trade.getPayment()).subtract(ordersDiscountFee.get()).get();
        //校验主单优惠
        if (!MathUtils.equals(countDiscountFee, MathUtils.toBigDecimal(trade.getDiscountFee())) || trade.getDiscountFee() == null ) {
            if (logger.isDebugEnabled() && enableLog) {
                String log = new PaymentLogBuilder(trade).appendHead(trade.getSource()).appendChanged("矫正订单discountFee ", trade.getDiscountFee(), countDiscountFee).append("countDiscountFee", countDiscountFee).eq()
                        .append("totalFee", trade.getTotalFee()).add().append("postFee", trade.getPostFee()).add().append("taxFee", trade.getTaxFee())
                        .sub().append("payment", trade.getPayment()).sub().append("ordersDiscountFee", ordersDiscountFee.getString()).toString();
                logger.debug(log);
            }
            trade.setDiscountFee(MathUtils.toString(countDiscountFee));
            correct = false;
        }

        return correct;
    }

    /**
     * 校验订单金额是否正确(批量)
     *
     * @param trades
     * @param handle 是否自动校正数据
     * @param <T>
     * @return
     */
    public static <T extends Trade> boolean checkTrades(List<T> trades, boolean handle, boolean log) {
        boolean correct = true;

        for (Trade trade : trades) {
            correct &= checkTrade(trade, handle, log);
        }

        return correct;
    }

    public static String calculateSplitPaymentByPercentage(String payment, int originNum, int num) {
        return new BigDecimalWrapper(payment).multiply(num).divide(originNum).getString();
    }

    public static String calculateSplitPayAmountByPercentage(String payAmount, int originNum, int num) {
        return new BigDecimalWrapper(payAmount).multiply(num).divide(originNum).getString();
    }

    /**
     * @param: trade
     * @param: noLinkType (2, "平台订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     *                   (3, "系统订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     *                   (4, "系统订单、平台订单金额不联动，增/删商品时，订单（trade）实付金额不变"),
     *                   (0, "改商品时，商品实付金额不变；增/删商品时，订单实付金额跟着变化"),
     * @author: pxh
     * @description:
     * @date 2021/5/11 6:33 下午
     */
    public static void calculateUpdateTradeTotalFeeAndDiscountFee(Trade trade, Integer noLinkType, boolean enableLog) {
        if (Objects.isNull(noLinkType)) {
            return;
        }
        if (trade.isSkipPaymentLink()) {
            return;
        }
        if (logger.isDebugEnabled() && PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
            logger.debug(String.format(START_PAYMENT_FORMATTER, trade.getTid(), trade.getSid(), trade.getPayment(), trade.getDiscountFee()));
        }

        OpenLinkConfigEnum openLinkConfig = OpenLinkConfigEnum.getOpenLinkConfigEnum(noLinkType);
        Trade originTrade = trade.getOrigin();
        if (originTrade == null) {
            originTrade = trade;
        }

        double originTradeDiscountFee = getOriginTradeDiscountFee(trade, openLinkConfig, originTrade);
        //原订单运费
        double originTradePostFee = MathUtils.toDouble(originTrade.getPostFee());
        //原订单税费
        double originTradeTaxFee = MathUtils.toDouble(originTrade.getTaxFee());
        //新的子订单
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        //所有子订单应付
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        //子订单的优惠
        BigDecimalWrapper ordersTotalDiscountFee = new BigDecimalWrapper();
        //子订单的实付
        BigDecimalWrapper ordersTotalPayment = new BigDecimalWrapper();
        //子订单的实付
        BigDecimalWrapper ordersTotalPayAmount = new BigDecimalWrapper();
        //所有子订单的分销价
        BigDecimalWrapper ordersTotalSaleFee = new BigDecimalWrapper();
        PaymentLinkReqDTO paymentLinkReqDTO = new PaymentLinkReqDTO();
        validateMutiIds(trade.getCompanyId(),trade.getSid(),orders);
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }

            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                PaymentUtils.calculateOrder(order);
                continue;//合单情况，只计算本身order
            }


            if (Objects.nonNull(order.getOrderOpeart())) {
                paymentLinkReqDTO.setOrder(order);
                paymentLinkReqDTO.setOpenLinkConfig(openLinkConfig);
                paymentLinkReqDTO.setTradeSource(trade.getSource());
                PaymentLinkRespDTO paymentLinkRespDto = PaymentLinkFactory.getPaymentLinkDto(paymentLinkReqDTO);
                ordersTotalDiscountFee.add(paymentLinkRespDto.getDiscountFee());
            } else {
                //对于没有做任何操作的订单 也需要将其优惠金额添加到order总优惠中去
                ordersTotalDiscountFee.add(order.getDiscountFee());
            }

            if (order.getSid() != null && trade.getSid().equals(order.getSid())) {
                ordersTotalSaleFee.add(order.getSaleFee());
            }
            ordersTotalFee.add(order.getTotalFee());
            ordersTotalPayment.add(order.getPayment());
            ordersTotalPayAmount.add(order.getPayAmount());
        }

        //设置总成本价
        trade.setCost(TradeUtils.calculateCost(trade));
        //设置分销金额
        trade.setSaleFee(ordersTotalSaleFee.getString());
        //订单应付
        trade.setTotalFee(ordersTotalFee.getString());

        PaymentLinkConvertDTO paymentLinkConvertDTO = PaymentLinkConvertDTO.builder()
                .tradeDiscountFee(originTradeDiscountFee)
                .originTradePostFee(originTradePostFee)
                .originTradeTaxFee(originTradeTaxFee)
                .ordersTotalDiscountFee(ordersTotalDiscountFee.getDouble())
                .ordersTotalFee(ordersTotalFee.getDouble())
                .build();


        calculationPayment(paymentLinkConvertDTO, openLinkConfig, trade, enableLog);
        if (logger.isDebugEnabled() && PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
            logger.debug(String.format(END_PAYMENT_FORMATTER, trade.getTid(), trade.getSid(), trade.getPayment(), trade.getDiscountFee(), trade.getPayAmount(),
                    ordersTotalFee, ordersTotalPayment, ordersTotalDiscountFee, ordersTotalPayAmount));
        }


    }

    /**
     * @author: pxh
     * @description: 获取源订单优惠金额
     * @date: 2021/9/8 9:40 下午
     * @param: trade
     * @param: openLinkConfig
     * @param: originTrade
     * @return: double
     */
    private static double getOriginTradeDiscountFee(Trade trade, OpenLinkConfigEnum openLinkConfig, Trade originTrade) {
        //原订单优惠
        double originTradeDiscountFee = MathUtils.toDouble(originTrade.getDiscountFee());
        if (OpenLinkConfigEnum.SYSTEM_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig) && PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            originTradeDiscountFee = MathUtils.toDouble(trade.getDiscountFee());
        }
        if (OpenLinkConfigEnum.PLATFORM_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig) && !PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            originTradeDiscountFee = MathUtils.toDouble(trade.getDiscountFee());
        }
        if (OpenLinkConfigEnum.ALL_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig)) {
            originTradeDiscountFee = MathUtils.toDouble(trade.getDiscountFee());
        }
        return originTradeDiscountFee;
    }

    private static void calculationPayment(PaymentLinkConvertDTO paymentLinkConvertDTO, OpenLinkConfigEnum openLinkConfig, Trade trade, boolean enableLog) {
        if (enableLog) {
            logger.info(new PaymentLogBuilder(trade).append("订单金额联动配置", openLinkConfig == null ? "null" : openLinkConfig.getDesc()).append("paymentLinkConvertDTO", JSON.toJSONString(paymentLinkConvertDTO)).toString());
        }

        //系统订单、平台订单金额不联动，增/删商品时，订单（trade）实付金额不变
        if (OpenLinkConfigEnum.ALL_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig)) {
            setTradePayment(paymentLinkConvertDTO, trade, enableLog);
            return;
        }

        //系统订单金额不联动，增/删商品时，订单（trade）实付金额不变
        if (OpenLinkConfigEnum.SYSTEM_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig)
                && PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            setTradePayment(paymentLinkConvertDTO, trade, enableLog);
            return;
        }

        //平台订单金额不联动，增/删商品时，订单（trade）实付金额不变
        if (OpenLinkConfigEnum.PLATFORM_TRADE_PAYMENT_NO_LINK.equals(openLinkConfig)
                && !PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            setTradePayment(paymentLinkConvertDTO, trade, enableLog);
            return;
        }

        //订单实付 = 订单应付 + 运费 - 订单优惠 - 所有子订单优惠 + 税费
        BigDecimalWrapper payment = new BigDecimalWrapper();
        payment.add(paymentLinkConvertDTO.getOrdersTotalFee()).add(paymentLinkConvertDTO.getOriginTradePostFee()).add(paymentLinkConvertDTO.getOriginTradeTaxFee())
                .subtract(paymentLinkConvertDTO.getTradeDiscountFee()).subtract(paymentLinkConvertDTO.getOrdersTotalDiscountFee());

        trade.setPayment(payment.getString());

        //double countDiscountFee = NumberUtils.str2Double(NumberUtils.double2Str(paymentLinkConvertDTO.getOrdersTotalFee()
        //        + paymentLinkConvertDTO.getOriginTradePostFee() + paymentLinkConvertDTO.getOriginTradeTaxFee() - payment - paymentLinkConvertDTO.getOrdersTotalDiscountFee()));
        trade.setDiscountFee(MathUtils.toString(paymentLinkConvertDTO.getTradeDiscountFee()));
    }

    private static void setTradePayment(PaymentLinkConvertDTO paymentLinkConvertDTO, Trade trade, boolean enableLog) {
        trade.setPayment(trade.getPayment());
        trade.setDiscountFee(MathUtils.toString(paymentLinkConvertDTO.getTradeDiscountFee()));

        BigDecimal discountFee = MathUtils.toBigDecimal(trade.getDiscountFee());
        //订单优惠 = 订单应付 + 运费 + 税费 -  订单实付- 所有子订单优惠
        BigDecimal countDiscountFee = new BigDecimalWrapper(paymentLinkConvertDTO.getOrdersTotalFee()).add(paymentLinkConvertDTO.getOriginTradePostFee()).add(paymentLinkConvertDTO.getOriginTradeTaxFee()).subtract(trade.getPayment()).subtract(paymentLinkConvertDTO.getOrdersTotalDiscountFee()).get();
        if (!MathUtils.equals(discountFee, countDiscountFee)) {
            if (logger.isDebugEnabled() && enableLog) {
                String log = new PaymentLogBuilder(trade).appendChanged("discountFee ", discountFee, countDiscountFee).append("discountFee", countDiscountFee).eq()
                        .append("OrdersTotalFee", paymentLinkConvertDTO.getOrdersTotalFee()).add().append("OriginTradePostFee", paymentLinkConvertDTO.getOriginTradePostFee()).add().append("OriginTradeTaxFee", paymentLinkConvertDTO.getOriginTradeTaxFee())
                        .sub().append("payment", trade.getPayment()).sub().append("OrdersTotalDiscountFee", paymentLinkConvertDTO.getOrdersTotalDiscountFee()).toString();
                logger.debug(log);
            }
            trade.setDiscountFee(MathUtils.toString(countDiscountFee));
        }
    }

    private static final Pattern pattern = Pattern.compile("-?[0-9]+(\\.[0-9]+)?");

    /**
     * 匹配是否为数字
     */
    public static boolean isNumeric(String str) {
        // 该正则表达式可以匹配所有的数字 包括负数
        String numberStr;
        try {
            numberStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            return false;
        }

        Matcher isNum = pattern.matcher(numberStr);
        return isNum.matches();
    }

    /**
     * 根据配置(【天猫淘宝】平台订单商品金额分摊是否开启)初始化实付金额 https://gykj.yuque.com/entavv/xb9xi5/yxpg6x7azy4plsl1
     *
     * @param openPlatformDivideOrderFee 【天猫淘宝】平台订单商品金额分摊是否开启
     */
    public static void init(Trade trade, List<Order> orders, boolean openPlatformDivideOrderFee, boolean force) {
        try {
            //仅淘宝,天猫订单
            if (openPlatformDivideOrderFee && (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource()))) {
                StringBuilder msg = new StringBuilder();
                for (Order order : orders) {
                    Trade originTrade = trade.getOrigin();
                    String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
                    if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {
                        //待付款
                        if (StringUtils.isNotEmpty(order.getTotalFee())) {
                            if (!NumberUtils.str2Double(order.getPayment()).equals(NumberUtils.str2Double(order.getTotalFee()))) {
                                String oldPayment = order.getPayment();
                                order.setPayment(order.getTotalFee());
                                msg.append(String.format("order id:{%s} sysStatus:%s payment:{%s}->{%s}", order.getId(), sysStatus, oldPayment, order.getPayment()));
                            }
                        }
                    } else if (force || (originTrade != null && Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(originTrade.getSysStatus()) &&
                            !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus))) {
                        //待付款->已付款
                        if (StringUtils.isNotEmpty(order.getDivideOrderFee())) {
                            if (!NumberUtils.str2Double(order.getPayment()).equals(NumberUtils.str2Double(order.getDivideOrderFee()))) {
                                String oldPayment = order.getPayment();
                                order.setPayment(order.getDivideOrderFee());
                                msg.append(String.format("order id:{%s} sysStatus:%s payment:{%s}->{%s}", order.getId(), sysStatus, oldPayment, order.getPayment()));
                            }
                        }
                    }
                    PaymentUtils.calculateOrder(order);
                }
                if (msg.length() > 0 && PaymentLogBuilder.isPrintMoneyDetailLog(trade.getCompanyId())) {
                    logger.debug(String.format("开启【天猫淘宝】平台订单商品金额分摊 sid:%s ", trade.getSid()) + "orders:{" + msg + "}");
                }
                PaymentUtils.calculateTradeDiscountFee(trade, orders);
            }
        } catch (Exception ex) {
            logger.error(String.format("开启【天猫淘宝】平台订单商品金额分摊失败 sid:%s tid:%s", trade.getSid(), trade.getTid()), ex);
        }
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class PaymentLinkConvertDTO {
        private double tradeDiscountFee;
        private double originTradePostFee;
        private double originTradeTaxFee;
        private double ordersTotalFee;
        private double ordersTotalDiscountFee;
    }


    /**
     * 计算订单标准金额
     * @param trade
     * @return
     */
    public static String calculateTradeTotalFee(Trade trade) {
        if (trade == null) {
            return "0.00";
        }
        //所有子订单应付金额
        BigDecimalWrapper ordersTotalFee = new BigDecimalWrapper();
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        //所有子订单成本
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;
            }
            //商品数量 = 原数量 - 赠品数量
            int num = NumberUtils.nvlInteger(order.getNum()) - NumberUtils.nvlInteger(order.getGiftNum());
            if (num < 0) {
                num = 0;
            }
            ordersTotalFee.addMulti(order.getPrice(), num);
        }
        return ordersTotalFee.getString();
    }


    /**
     * 根据配置获取毛利润计算公式
     *
     * @param trade
     * @param tradeConfig
     * @return
     */
    public static String generateGrossProfitDisplay(Trade trade, TradeConfig tradeConfig) {
        try {
            if (trade == null) {
                return null;
            }
            Integer scale = IPaymentSwitcher.getInstance().getScale(trade.getCompanyId());
            int calculateGrossProfitConfig = tradeConfig == null ? 0 : tradeConfig.getInteger(NewTradeExtendConfigEnum.GROSS_CACULATE_TYPE.getKey());
            BigDecimal payment = NumberUtils.str2Decimal(trade.getPayment(), "0");
            BigDecimal postFee = NumberUtils.str2Decimal(trade.getPostFee(), "0");
            //合单主单
            if (TradeUtils.isMerge(trade) && trade.getMergeSid() - trade.getSid() == 0) {
                if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                    BigDecimal totalPayment = BigDecimal.ZERO;
                    BigDecimal totalPostFee = BigDecimal.ZERO;
                    for (MessageMemo messageMemo : trade.getMessageMemos()) {
                        if (calculateGrossProfitConfig == 1 && null != messageMemo.getPostFee()) {
                            totalPostFee = totalPostFee.add(NumberUtils.str2Decimal(messageMemo.getPostFee(), "0"));
                        } else if (calculateGrossProfitConfig == 0 && null != messageMemo.getPayment()) {
                            totalPayment = totalPayment.add(NumberUtils.str2Decimal(messageMemo.getPayment(), "0"));
                        }
                    }
                    if (calculateGrossProfitConfig == 1) {
                        //如果合单,取所有子单运费合计
                        postFee = totalPostFee;
                    } else if (calculateGrossProfitConfig == 0) {
                        //如果合单,取所有子单应付合计
                        payment = totalPayment;
                    }
                }
            }
            StringBuilder display = new StringBuilder("(");
            if (calculateGrossProfitConfig == 1) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders)) {
                    BigDecimal divideOrderFee = BigDecimal.ZERO;
                    for (Order order : orders) {
                        divideOrderFee = divideOrderFee.add(StringUtils.isNoneBlank(order.getDivideOrderFee()) ? NumberUtils.str2Decimal(order.getDivideOrderFee(), "0") : BigDecimal.ZERO);
                    }
                    display.append(decimalFormatMoney(divideOrderFee, scale == null ? 2 : scale))
                            .append("+")
                            .append(decimalFormatMoney(postFee, scale == null ? 2 : scale));
                } else {
                    display.append("0")
                            .append("+")
                            .append(decimalFormatMoney(postFee, scale == null ? 2 : scale));
                }
            } else if (calculateGrossProfitConfig == 0) {
                display.append(decimalFormatMoney(payment, scale == null ? 2 : scale));
            }
            String actualPostFee = trade.getActualPostFee();
            if (actualPostFee == null) {
                actualPostFee = MathUtils.toString(trade.getTheoryPostFee());
            }
            return display.append("-")
                    .append(trade.getCost() != null ? decimalFormatMoney(BigDecimal.valueOf(trade.getCost()), scale == null ? 3 : scale) : "0")
                    .append("-")
                    .append(decimalFormatMoney(NumberUtils.str2Decimal(actualPostFee, "0"), scale == null ? 2 : scale))
                    .append("-")
                    .append(trade.getPackmaCost() != null ? decimalFormatMoney(BigDecimal.valueOf(trade.getPackmaCost()), scale == null ? 2 : scale) : "0")
                    .append(")").toString();
        } catch (Exception ex) {
            if (logger.isDebugEnabled()) {
                logger.error(String.format("获取毛利润计算公式错误 sid:%s", trade.getSid()), ex);
            }
            //go continue
            return null;
        }
    }

    /**
     * 判断是否整数,金额格式化输出
     * null=》0
     * 0=》0
     * -100.00=》-100
     * 000=》0
     * 0.00=》0
     * 0.0001=》0.00
     * 0.01=》0.01
     *
     * @param s
     * @return
     */
    public static String decimalFormatMoney(BigDecimal s, int scale) {
        if (s == null) {
            return "0";
        }
        return isInteger(s) ? String.valueOf(s.longValue()) : s.scale() > scale ? s.setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() : s.stripTrailingZeros().toPlainString();
    }

    /**
     * 判断BigDecimal是否是整数<br>
     * 支持10进制
     *
     * @param s BigDecimal
     * @return 是否为整数
     */
    public static boolean isInteger(BigDecimal s) {
        return s != null && (s.signum() == 0 || s.scale() <= 0 || s.stripTrailingZeros().scale() <= 0);
    }

    public static Double calculateQimenFxAmount(Trade trade) {
        return calculateQimenFxAmount(trade, true, true);
    }

    public static Double calculateQimenFxAmount(Trade trade, boolean filterClose, boolean filterSysConsign) {
        if (trade == null) {
            return 0D;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        BigDecimalWrapper payment = new BigDecimalWrapper();
        for (Order order : orders) {
            if (order.getEnableStatus() == null || order.getEnableStatus() == 0) {
                continue;
            }
            if (filterClose && Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            //其他ERP发货的，系统发货前退款成功的
            if (filterSysConsign && (OrderUtils.isOtherErpConsigned(order) || OrderUtils.isRefundedBeforeSysConsign(order))) {
                continue;
            }
            payment.add(order.getPayment());
        }
        BigDecimal postFee = new BigDecimal(StringUtils.defaultIfEmpty(trade.getPostFee(), "0"));
        return new BigDecimalWrapper(payment.get()).add(postFee).getDouble();
    }


    /**
     * 计算分销总成本
     *
     * @param trade
     * @return
     */
    public static String calculateFxTotalCost(Trade trade) {
        if (!(TradeUtils.isGxOrMixTrade(trade) || TradeUtils.isFxOrMixTrade(trade))) {
            return null;
        }
        if (TradeUtils.isGxOrMixTrade(trade)) {
            return new BigDecimalWrapper(trade.getTotalFee())
                    .add(MathUtils.toString(new BigDecimalWrapper(trade.getDiscountFee()).get().negate()))
                    .add(new BigDecimalWrapper(trade.getPostFee()).get())
                    .getString();
        } else if (TradeUtils.isFxOrMixTrade(trade) && trade.getTradeExt() != null) {
            return new BigDecimalWrapper(Optional.ofNullable(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "fxTotalPrice")).orElse("0.00").toString())
                    .add(Optional.ofNullable(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "fxCommission")).orElse("0.00").toString())
                    .add(new BigDecimalWrapper(trade.getActualPostFee()).get())
                    .getString();
        }
        return null;
    }

    /**
     * 计算补贴
     * @param trade
     * @return
     */
    public static String calculateTradePlatformDiscountFee(Trade trade) {
        if (trade == null) {
            return "0.00";
        }
        double ordersPlatformDiscountFee = 0.00D;
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getSid() != null && !trade.getSid().equals(order.getSid())) {
                continue;
            }
            ordersPlatformDiscountFee += NumberUtils.str2Double(order.getPlatformDiscountFee());
        }
        return NumberUtils.double2Str(ordersPlatformDiscountFee);
    }

    /**
     * 计算毛利率 订单毛利润率=订单毛利润/订单应付金额
     *
     * @param staff
     * @param grossProfit
     * @param payment
     * @return
     */
    public static String calculateGrossProfitRate(Staff staff, double grossProfit, double payment) {
        if (MathUtils.equalsZero(payment) || MathUtils.equalsZero(grossProfit)) {
            return "0.00";
        }
        return MathUtils.toScaleString(staff.getCompanyId(),
                new BigDecimalWrapper(grossProfit)
                        .divide(new BigDecimalWrapper(payment).get()).get());
    }
}

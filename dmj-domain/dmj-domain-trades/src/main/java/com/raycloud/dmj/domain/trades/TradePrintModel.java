package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.channel.OrderChannel;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;

import java.io.Serializable;
import java.sql.Types;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * @Description:打印model
 * @author:zhangzhaowu
 * @time:May 30, 2019 7:22:57 PM
 */
public class TradePrintModel implements Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 系统订单号
     */
    private Long sid;
    
    /**
     * 快递模板编号
     */
    private Long templateId;

	public Long getSid() {
		return sid;
	}

	public void setSid(Long sid) {
		this.sid = sid;
	}

	public Long getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Long templateId) {
		this.templateId = templateId;
	}
    
}

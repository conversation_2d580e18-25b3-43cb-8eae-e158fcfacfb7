package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.utils.WipeEmojiUtils;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Created by windy26205 on 19/3/13.
 */
@Table(name = "info_change_log", routerKey = "itemChangeLogDbNo")
@Setter
@Getter
public class InfoChangeLog extends Model {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long companyId;

    private Long sid;

    private String tid;

    private String preValue;

    private String curValue;

    private Date insertTime;

    private int infoType;//1表示sellerMemo,2表示sellerFlag,3标识addressChange,4 表示运单号
    /**
     * 是否成功 0:失败  1：成功
     */
    private int success;

    public InfoChangeLog() {
    }

    public InfoChangeLog(Long companyId, Long sid, String preValue, String curValue, int infoType, String tid) {
        this.sid = sid;
        this.preValue = WipeEmojiUtils.wipeEmoji(preValue);
        this.curValue = WipeEmojiUtils.wipeEmoji(curValue);
        this.companyId = companyId;
        this.infoType = infoType;
        this.tid = tid;
    }
}

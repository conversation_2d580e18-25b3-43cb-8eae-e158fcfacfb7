package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * <AUTHOR>
 * @created 2019-01-05 16:42
 */
@Table(name = "trade_stat", routerKey = "tradeDbNo")
public class TradeStat extends Model {

    /**
     * 系统订单号
     */
    private Long sid;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 店铺ID
     */
    private Long userId;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 订单类型
     */
    private String type;
    /**
     * 是否预售订单
     */
    private Integer isPresell;
    /**
     * 系统状态
     */
    private String sysStatus;
    /**
     * 地址是否有变更
     */
    private Integer addressChanged;
    /**
     * 备注是否有变更
     */
    private Integer sellerMemoChanged;
    /**
     * 是否挂起订单
     */
    private Integer isHalt;
    /**
     * 是否异常
     */
    private Integer isExcep;
    /**
     * 是否作废
     */
    private Integer isCancel;
    /**
     * 是否黑名单订单
     */
    private Integer blackBuyerNick;
    /**
     * 是否库存不足
     */
    private Integer insufficient;
    /**
     * 是否商品为匹配
     */
    private Integer unmatched;
    /**
     * 是否商品对应关系改动
     */
    private Integer relationChanged;
    /**
     * 是否退款订单
     */
    private Integer isRefund;
    /**
     * 是否平台更换商品
     */
    private Integer itemChanged;
    /**
     * 订单删除状态
     */
    private Integer enableStatus;

    /**
     * 快递停发异常
     */
    private Integer unattainable;

    /**
     * 地址、手机/固话、收件人、商品信息缺失的订单
     */
    private Integer isLostMsg;

/*    *//**
     * 风控订单
     *//*
    private Integer isRisk;*/

    private Integer deliverExcep;

    private Integer uploadExcep;
    /**
     * 套件信息修改异常
     */
    private Integer suiteChange;


    /**
     * 分销订单-----不明确供应商异常
     */
    private Integer ambiguityeExcep;
    /**
     * 分销订单-----分销待付款异常
     */
    private Integer waitPayExcep;
    /**
     * 分销订单-----分销反审核异常
     */
    private Integer unAuditExcep;
    /**
     * 分销订单-----供销商打回
     */
    private Integer repulseExcep;
    /**
     * 平台分销订单----外仓商品待拆分
     */
    private Integer platformFxSplitExcep;
    /**
     * 等待合并异常
     */
    private Integer waitMerge;
    /**
     * 商品停用
     */
    private Integer itemShutoff;
    /**
     * 商品类型转换异常
     */
    private Integer itemProcessExcep;
    /**
     * 唯一码下架
     */
    private Integer itemShelfOff;

    public Integer getRepulseExcep() {
        return repulseExcep;
    }

    public void setRepulseExcep(Integer repulseExcep) {
        this.repulseExcep = repulseExcep;
    }

    public Integer getAmbiguityeExcep() {
        return ambiguityeExcep;
    }

    public void setAmbiguityeExcep(Integer ambiguityeExcep) {
        this.ambiguityeExcep = ambiguityeExcep;
    }

    public Integer getWaitPayExcep() {
        return waitPayExcep;
    }

    public void setWaitPayExcep(Integer waitPayExcep) {
        this.waitPayExcep = waitPayExcep;
    }

    public Integer getUnAuditExcep() {
        return unAuditExcep;
    }

    public void setUnAuditExcep(Integer unAuditExcep) {
        this.unAuditExcep = unAuditExcep;
    }

    /**
     * 售后生成的换货补发订单等待退货入仓的异常
     */
    private Integer waitingReturnWmsExcep;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public Integer getAddressChanged() {
        return addressChanged;
    }

    public void setAddressChanged(Integer addressChanged) {
        this.addressChanged = addressChanged;
    }

    public Integer getSellerMemoChanged() {
        return sellerMemoChanged;
    }

    public void setSellerMemoChanged(Integer sellerMemoChanged) {
        this.sellerMemoChanged = sellerMemoChanged;
    }

    public Integer getIsHalt() {
        return isHalt;
    }

    public void setIsHalt(Integer isHalt) {
        this.isHalt = isHalt;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public void setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
    }

    public Integer getBlackBuyerNick() {
        return blackBuyerNick;
    }

    public void setBlackBuyerNick(Integer blackBuyerNick) {
        this.blackBuyerNick = blackBuyerNick;
    }

    public Integer getInsufficient() {
        return insufficient;
    }

    public void setInsufficient(Integer insufficient) {
        this.insufficient = insufficient;
    }

    public Integer getUnmatched() {
        return unmatched;
    }

    public void setUnmatched(Integer unmatched) {
        this.unmatched = unmatched;
    }

    public Integer getRelationChanged() {
        return relationChanged;
    }

    public void setRelationChanged(Integer relationChanged) {
        this.relationChanged = relationChanged;
    }

    public Integer getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
    }

    public Integer getItemChanged() {
        return itemChanged;
    }

    public void setItemChanged(Integer itemChanged) {
        this.itemChanged = itemChanged;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Integer getUnattainable() {
        return unattainable;
    }

    public void setUnattainable(Integer unattainable) {
        this.unattainable = unattainable;
    }

    public Integer getIsLostMsg() {
        return isLostMsg;
    }

    public void setIsLostMsg(Integer isLostMsg) {
        this.isLostMsg = isLostMsg;
    }

/*    public Integer getIsRisk() {
        return isRisk;
    }

    public void setIsRisk(Integer isRisk) {
        this.isRisk = isRisk;
    }*/

    public Integer getDeliverExcep() {
        return deliverExcep;
    }

    public void setDeliverExcep(Integer deliverExcep) {
        this.deliverExcep = deliverExcep;
    }

    public Integer getUploadExcep() {
        return uploadExcep;
    }

    public void setUploadExcep(Integer uploadExcep) {
        this.uploadExcep = uploadExcep;
    }

    public Integer getSuiteChange() {
        return suiteChange;
    }

    public void setSuiteChange(Integer suiteChange) {
        this.suiteChange = suiteChange;
    }

    public Integer getWaitingReturnWmsExcep() {
        return waitingReturnWmsExcep;
    }

    public void setWaitingReturnWmsExcep(Integer waitingReturnWmsExcep) {
        this.waitingReturnWmsExcep = waitingReturnWmsExcep;
    }

    public Integer getWaitMerge() {
        return waitMerge;
    }

    public void setWaitMerge(Integer waitMerge) {
        this.waitMerge = waitMerge;
    }

    public Integer getItemShutoff() {
        return itemShutoff;
    }

    public void setItemShutoff(Integer itemShutoff) {
        this.itemShutoff = itemShutoff;
    }

    public Integer getItemShelfOff() {
        return itemShelfOff;
    }

    public void setItemShelfOff(Integer itemShelfOff) {
        this.itemShelfOff = itemShelfOff;
    }

    public Integer getPlatformFxSplitExcep() {
        return platformFxSplitExcep;
    }

    public void setPlatformFxSplitExcep(Integer platformFxSplitExcep) {
        this.platformFxSplitExcep = platformFxSplitExcep;
    }

    public Integer getItemProcessExcep() {
        return itemProcessExcep;
    }

    public void setItemProcessExcep(Integer itemProcessExcep) {
        this.itemProcessExcep = itemProcessExcep;
    }
}

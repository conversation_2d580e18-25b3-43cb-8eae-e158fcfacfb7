package com.raycloud.dmj.domain.trades;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

/**
 * 存放校验配置时 多个配置校验使用到的公共参数
 * 第一次使用到时做填充处理 后续复用
 */
@Getter
@Setter
public class TradeConfigCheckContext {

    /**
     * 公司当前的所有店铺user
     * 校验配置时 按系统状态和当前所有店铺userId查询订单
     */
    private Set<Long> currAllUserIds;

    private TradeConfig tradeConfig;

    /**
     * 延迟发货上传配置的userId
     */
    private List<Long> delayConsignUserIds;
}

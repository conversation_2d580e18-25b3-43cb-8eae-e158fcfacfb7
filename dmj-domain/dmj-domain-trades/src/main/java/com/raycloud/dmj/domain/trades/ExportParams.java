package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ExportParams implements Serializable {

    public static final int ININT_STATUS = 0;
    public static final int RUN_STATUS = 1;
    public static final int BREAK_CURRENT_STATUS = 2;

    private static final long serialVersionUID = -3942066332482778848L;

    private List<TimeSlice> timeSlices;

    private Page lastPage;

    private boolean firstFilter = true;

    //为分阶段运行状态而设立的值
    private int status = 0;

    public List<TimeSlice> getTimeSlices() {
        if (timeSlices == null){
            return new ArrayList<>();
        }
        return timeSlices;
    }

    public void setTimeSlices(List<TimeSlice> timeSlices) {
        this.timeSlices = timeSlices;
    }

    public Page getLastPage() {
        return lastPage;
    }

    public void setLastPage(Page lastPage) {
        this.lastPage = lastPage;
    }

    public boolean isFirstFilter() {
        return firstFilter;
    }

    public void setFirstFilter(boolean firstFilter) {
        this.firstFilter = firstFilter;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}

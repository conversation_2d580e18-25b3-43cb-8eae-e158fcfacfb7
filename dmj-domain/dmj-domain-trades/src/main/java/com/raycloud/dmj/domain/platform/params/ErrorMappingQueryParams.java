package com.raycloud.dmj.domain.platform.params;

import lombok.Data;

import java.io.Serializable;

/**
 * @description 平台错误信息映射查询参数
 * <AUTHOR>
 * @date 2023/5/5 15:03
 */
@Data
public class ErrorMappingQueryParams implements Serializable {
    private static final long serialVersionUID = -7002083992962854251L;

    /**
     * 分页起始索引
     */
    private Integer offset;

    /**
     * 分页大小pageSize
     */
    private Integer limit;

    /**
     * 当前页码，从1开始
     */
    private Integer pageNo;

    /**
     * 平台来源
     */
    private String source;

    /**
     * 平台错误码
     */
    private String platCode;

    /**
     * 平台错误信息
     */
    private String platMsg;

    /**
     * 平台子错误码
     */
    private String platSubCode;

    /**
     * 平台子错误信息
     */
    private String platSubMsg;

    /**
     * 匹配类型
     */
    private String matchType;

    private String sysCode;

    private String sysMsg;
}

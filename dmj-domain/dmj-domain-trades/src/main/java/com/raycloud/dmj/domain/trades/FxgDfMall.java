package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;
import java.util.Objects;

/**
 * 不要被名字迷惑，现在是代发商家店铺，不仅仅是放心购
 *
 * 代发商家店铺
 */
@Table(name = "fxg_df_mall")
public class FxgDfMall extends Model {

    private static final long serialVersionUID = 7184169956449590610L;

    private Long id;

    /**
     * 来源
     */
    private String tradeSource;

    /**
     * 子来源
     */
    private String subSource;


    private Long companyId;

    private Long taobaoId;

    /**
     * 放心购商家代发店铺id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mallMaskId;

    /**
     * 放心购商家代发店铺自定义名称
     */
    private String mallMaskName;

    /**
     * 逻辑标识
     * 1
     * 0 删除
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public FxgDfMall() {
    }

    public FxgDfMall(Long companyId,String tradeSource,String subSource, Long mallMaskId, String mallMaskName) {
        this.companyId = companyId;
        this.tradeSource = tradeSource;
        this.subSource = subSource;
        this.mallMaskId = mallMaskId;
        this.mallMaskName = mallMaskName;
    }

    public FxgDfMall(Long companyId, Long taobaoId, Long mallMaskId, String mallMaskName) {
        this.companyId = companyId;
        this.taobaoId = taobaoId;
        this.mallMaskId = mallMaskId;
        this.mallMaskName = mallMaskName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        FxgDfMall fxgDfMall = (FxgDfMall) o;
        return Objects.equals(id, fxgDfMall.id) &&
                Objects.equals(companyId, fxgDfMall.companyId) &&
                Objects.equals(taobaoId, fxgDfMall.taobaoId) &&
                Objects.equals(mallMaskId, fxgDfMall.mallMaskId) &&
                Objects.equals(tradeSource, fxgDfMall.tradeSource) &&
                Objects.equals(subSource, fxgDfMall.subSource) &&
                Objects.equals(mallMaskName, fxgDfMall.mallMaskName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, companyId, tradeSource,subSource,taobaoId,mallMaskId, mallMaskName);
    }

    /**
     * getter setter
     */
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Long getMallMaskId() {
        return mallMaskId;
    }

    public void setMallMaskId(Long mallMaskId) {
        this.mallMaskId = mallMaskId;
    }

    public String getMallMaskName() {
        return mallMaskName;
    }

    public void setMallMaskName(String mallMaskName) {
        this.mallMaskName = mallMaskName;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTradeSource() {
        return tradeSource;
    }

    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }
}

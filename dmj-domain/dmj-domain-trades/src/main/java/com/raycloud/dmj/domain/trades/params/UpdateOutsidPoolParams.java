package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;
import java.util.Map;

/**
 * @author: qingfeng.cxb
 * @create: 2020-04-29 17:38
 */
public class UpdateOutsidPoolParams implements Serializable {
    private static final long serialVersionUID = -8768950974749542071L;
    /**
     * 公司
     */
    private Staff staff;
    /**
     * ip
     */
    private String ip;

    /**
     * 处理之后的订单号和运单号map
     */
    private Map<Long, String> sidOutsidMap;

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Map<Long, String> getSidOutsidMap() {
        return sidOutsidMap;
    }

    public void setSidOutsidMap(Map<Long, String> sidOutsidMap) {
        this.sidOutsidMap = sidOutsidMap;
    }
}

package com.raycloud.dmj.domain.trades.search.exception;

/**
 * <AUTHOR>
 *
 * 查询超时异常
 */
public class SearchTimeoutException extends RuntimeException{

    public SearchTimeoutException() {
    }

    public SearchTimeoutException(String message) {
        super(message);
    }

    public SearchTimeoutException(String message, Throwable cause) {
        super(message, cause);
    }

    public SearchTimeoutException(Throwable cause) {
        super(cause);
    }
}
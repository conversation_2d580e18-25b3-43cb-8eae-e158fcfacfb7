package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * pc端验货上传所用的Vo类
 *
 * @author: qingfeng.cxb
 * @create: 2019-04-11 10:08
 */
public class CheckGoodsTradeVo implements Serializable {

    private static final long serialVersionUID = 4532974752184094348L;
    /**
     * 订单ID
     */
    private Long sid;

    /**
     * 快递单号
     */
    private String outSid;

    /**
     * 商品数
     */
    private Integer itemCount;

    /**
     * 商品种类数
     */
    private Integer itemKindCount;

    /**
     * 同运单号下交易关闭订单
     */
    private List<Long> closeSids;

    /**
     * 订单短号
     */
    private Long shortId;

    private String shipperName;

    private String shipperId;
    /**
     * 验货登记补发单异常
     */
    private List<ReplenishTrades> checkReplenishMsg;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public Integer getItemKindCount() {
        return itemKindCount;
    }

    public void setItemKindCount(Integer itemKindCount) {
        this.itemKindCount = itemKindCount;
    }

    public List<Long> getCloseSids() {
        return closeSids;
    }

    public void setCloseSids(List<Long> closeSids) {
        this.closeSids = closeSids;
    }

    public List<ReplenishTrades> getCheckReplenishMsg() {
        return checkReplenishMsg;
    }

    public void setCheckReplenishMsg(List<ReplenishTrades> checkReplenishMsg) {
        this.checkReplenishMsg = checkReplenishMsg;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }
}
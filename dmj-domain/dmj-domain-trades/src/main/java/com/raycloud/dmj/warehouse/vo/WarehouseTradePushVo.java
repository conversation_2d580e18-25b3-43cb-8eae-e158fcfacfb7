package com.raycloud.dmj.warehouse.vo;

import java.util.Date;

/**
 * 订单推送结果
 * Created by guzy on 17/11/21.
 */
public class WarehouseTradePushVo {

    /**
     * 推送平台名称
     */
    private String sourceName;

    private Long sid;

    private String source;

    private String shopSource;

    private String shopFlag;

    private String sysStatus;

    private String receiverAddress;

    private String receiverCity;

    private String receiverDistrict;

    private String warehouseName;

    /**
     * 平台是否已发货
     */
   // private Boolean sentGoods;

    /**
     * 平台状态中文名
     */
    private String chStatus;

    /**
     * 操作
     */
    private String op;

    /**
     * 状态
     */
    private String status;

    /**
     * 说明
     */
    private String msg;

    /**
     * 推送时间
     */
    private Date pushTime;

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getChStatus() {
        return chStatus;
    }

    public void setChStatus(String chStatus) {
        this.chStatus = chStatus;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getShopSource() {
        return shopSource;
    }

    public void setShopSource(String shopSource) {
        this.shopSource = shopSource;
    }

    public String getShopFlag() {
        return shopFlag;
    }

    public void setShopFlag(String shopFlag) {
        this.shopFlag = shopFlag;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

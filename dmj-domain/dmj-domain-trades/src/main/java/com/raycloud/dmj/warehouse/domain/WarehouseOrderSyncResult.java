package com.raycloud.dmj.warehouse.domain;

import java.util.Date;

/**
 * 第三方仓库
 * Created by guzy on 17/11/17.
 */
public class WarehouseOrderSyncResult {

    public final static String OP_CREATE="create",OP_CANCEL="cancel";

    public final static Integer STATUS_ING=1,STATUS_FAIL=2;

    private Long id;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 单据id
     */
    private Long sysId;

    private String msg;

    private Long companyId;

    private Date updTime;

    /**
     * 1 执行中
     * 2 失败
     */
    private Integer status;

    private String customerId;

    private String op;

    private Integer dbNo;

    public String getOpName(){
        if(OP_CREATE.equalsIgnoreCase(op)){
            return "推送订单";
        }else if(OP_CANCEL.equalsIgnoreCase(op)){
            return "取消订单";
        }
        return "";
    }

    public String getStatusName(){
        if(status.equals(STATUS_ING)){
            return "推送中";
        }else{
            return "推送失败";
        }
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Date getUpdTime() {
        return updTime;
    }

    public void setUpdTime(Date updTime) {
        this.updTime = updTime;
    }

    public Integer getStatus() {
        return status;
    }


    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}

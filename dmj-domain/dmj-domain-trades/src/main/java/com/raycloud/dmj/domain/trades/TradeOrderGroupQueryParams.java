package com.raycloud.dmj.domain.trades;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品汇总查询  https://gykj.yuque.com/entavv/xb9xi5/go5kwv#IQppA
 *
 * <AUTHOR> <EMAIL>
 * @date 2022/7/15 10:36
 */
@Data
@AllArgsConstructor
public class TradeOrderGroupQueryParams implements Serializable {

    private static final long serialVersionUID = -4876162548073834038L;
    /**
     * 订单查询参数
     */
    TradeQueryParams tradeQueryParams;

    /**
     * 订单分组参数
     */
    TradeOrderGroupSummaryParams groupSummaryParams;
}

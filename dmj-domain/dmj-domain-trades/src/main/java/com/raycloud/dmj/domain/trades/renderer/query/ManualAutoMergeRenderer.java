package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class ManualAutoMergeRenderer implements IFieldValueRenderer {

    /**
     * 0:智能合并所有符合条件的订单 1:智能合并搜索结果中符合条件的订单
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "智能合并所有符合条件的订单";
        }
        if (Objects.equals(originValue,1)) {
            return "智能合并搜索结果中符合条件的订单";
        }
        return String.valueOf(originValue);
    }
}

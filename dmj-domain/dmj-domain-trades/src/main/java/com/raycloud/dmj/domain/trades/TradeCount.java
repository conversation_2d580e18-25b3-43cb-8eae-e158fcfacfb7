package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.except.domain.TradeExceptCount;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 订单统计数据，提供各个状态下的卖家的订单数量
 * <AUTHOR>
 *
 */
public class TradeCount implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8878333181012292121L;
	/**
	 * 异常订单数
	 */
	private Long excepNum;
    /**
     * 退款中的订单数
     */
	private Long refundNum;
	/**
	 * 待付款的订单数量
	 */
	private Long waitPayNum;
	/**
	 * 待审核订单数
	 */
	private Long waitAuditNum;
    /**
     * 待财审订单数
     */
	private Long waitFinanceAudit;
    /**
     * 已审核订单数
     */
	private Long finishAuditNum;
    /**
     * 已审核未分配快递的订单数
     */
	private Long waitSetTemplateNum;
    /**
     * 已设置快递模版待打印快递单的订单数
     */
	private Long waitPrintNum;
    /**
     * 已打印快递单的订单数
     */
	private Long finishPrintNum;
    /**
     * 待包装订单数
     */
	private Long waitPackNum;
    /**
     * 可拆分装箱数
     */
	private Long packSplitableNum;
    /**
     * 包装是否开启识别码,查询待包装订单数量时使用
     */
	private Integer openIdentCode;
    /**
     * 待称重订单数
     */
	private Long waitWeighNum;
    /**
     * 待发货订单数
     */
    private Long waitConsignNum;
    /**
     * 已发货订单数
     */
    private Long finishConsignNum;
    /**
     * 已完成订单数
     */
    private Long finishedNum;
    /**
     * 出库单：待出库单数
     */
    private Long waitOutNum;
    /**
     * 出库单：已出库单数
     */
    private Long finishOutNum;
    /**
     * 预售锁定订单数
     */
    private Long presellNum;
    /**
     * 待处理售后工单数
     */
    private Long pendingWorkOrderNum;
    /**
     * 上传失败订单数
     */
    private Long uploadFailNum;
    /**
     * 当前查询的订单总数
     */
    private Long num;

    /**
     * 第三方仓库推送失败数量
     */
    private Long party3PushFailNum;

    /**
     * 待发货订单数量（整合页面用）
     */
    private Long unConsignedNum;

    private Long haltNum;

    private Long insufficientNum;

    private Long unmatchedNum;

    private Long relationChangedNum;

    private Long itemChangedNum;

    private Long addressChangedNum;

    private Long blackBuyerNickNum;

    private Long sellerMemoChangedNum;

    private Long isLostMsgNum;

    private Long partRefundNum;

    private Long itemProcessNum;

    private Long sellerSendNoWeightNum;

    private Long riskNum;

    private Long caiGouNum;

    private Long waitDestSendNum;

    private Long smallRefundExceptNum;

    public Long getPartRefundNum() {
        return partRefundNum;
    }

    public void setPartRefundNum(Long partRefundNum) {
        this.partRefundNum = partRefundNum;
    }

    /**
     * 系统异常
     */
    private List<TradeExceptCount> sysExceptCountList;

    /**
     *
     */
    private Boolean ifCalcAllSysExcept;

    public List<TradeExceptCount> getSysExceptCountList() {
        return sysExceptCountList;
    }

    public void setSysExceptCountList(List<TradeExceptCount> sysExceptCountList) {
        this.sysExceptCountList = sysExceptCountList;
    }

    public Boolean getIfCalcAllSysExcept() {
        return ifCalcAllSysExcept;
    }

    public void setIfCalcAllSysExcept(Boolean ifCalcAllSysExcept) {
        this.ifCalcAllSysExcept = ifCalcAllSysExcept;
    }

    /**
     * 自定义异常数量
     */
    private Map<String, Long> exceptTagMap;


    /**
     * 标签数量
     */
    private Map<String, Integer> labelMap;

    private Map<String,Integer> o2oMap;

    public Map<String, Integer> getO2oMap() {
        return o2oMap;
    }

    public void setO2oMap(Map<String, Integer> o2oMap) {
        this.o2oMap = o2oMap;
    }

    public Map<String, Integer> getLabelMap() {
        return labelMap;
    }

    public void setLabelMap(Map<String, Integer> labelMap) {
        this.labelMap = labelMap;
    }


    /**
     * 快递停发订数量
     */
    private Long unattainableNum;
    /**
     * 未处理备注数
     */
    private Long unCheckMemoNum;
    /**
     * 未处理留言数
     */
    private Long unCheckMessageNum;

    /**
     * 未处理留言或备注数
     */
    private Long unCheckTotalNum;

    /**
     * 已处理留言或没有留言的数量
     */
    private Long CheckedMessageAndNoneCount;

    /**
     * 已处理备注或没有备注的数量
     */
    private Long CheckedMemoAndNoneCount;

    private Long deliverExcepNum;

    private Long uploadExcepNum;

    private Long uploadingNum;
    /**
     * 等待合并
     */
    private Long waitMergeNum;

    /**
     * 等待退货入仓数
     */
    private Long waitingReturnWmsNum;

    private Long ambiguityeExcepNum;

    private Long repulseExcepNum;

    /**
     * 商品停用
     */
    private Long itemShutoffNum;
    private Long itemUniqueShelfoffNum;
    /**
     *  财审拒绝
     */
    private Long financeRejectUnm;

    /**
     * 待供销商发货数量
     */
    private Long waitDestSendGoodsNum;

    /**
     * 自发待发货
     */
    private Long waitSelfSendGoodsNum;

    /**
     * 单号回收异常
     */
    private Long outSidRecoveryFailNum=0L;

    /**
     * 取消拦截失败数量
     */
    private Long cancelInterceptFailNum;

    /**
     * 发货失败数量
     */
    private Long shipmentFailNum;

    /**
     * 发货创建失败数量
     */
    private Long createShipmentOrderFailNum;

    /**
     * 自发订单待审核数量(非代销 -- tradeType != 70 )
     */
    private Long waitSelfAuditGoodsNum;

    /**
     * 代发订单待推送数量(代发 -- 分销且存在供销商 tradeType = 70)
     */
    private Long waitDfAuditGoodsNum;

    private Long poJiaExceptNum;

    public Long getWaitSelfSendGoodsNum() {
        return waitSelfSendGoodsNum;
    }

    public void setWaitSelfSendGoodsNum(Long waitSelfSendGoodsNum) {
        this.waitSelfSendGoodsNum = waitSelfSendGoodsNum;
    }

    public Long getWaitDestSendGoodsNum() {
        return waitDestSendGoodsNum;
    }

    public void setWaitDestSendGoodsNum(Long waitDestSendGoodsNum) {
        this.waitDestSendGoodsNum = waitDestSendGoodsNum;
    }

    public Long getItemUniqueShelfoffNum() {
        return itemUniqueShelfoffNum;
    }

    public void setItemUniqueShelfoffNum(Long itemUniqueShelfoffNum) {
        this.itemUniqueShelfoffNum = itemUniqueShelfoffNum;
    }

    /**
     * 套件信息修改异常
     */
    private Long suiteChangeNum;

    /**
     * 是否异常
     */
    private Integer isExcep;

    /**
     *  退款数量异常
     */
    private Long refundNumExceptNum;

    private Long partPayExceptNum;

    private Long gxItemChangeNum;

    private Long platModifyExchangeItemExcept;

    /**
     * 待接单 (速卖通待接单
     */
    private Long unConfirmNum = 0L;

    /**
     * 线上锁定数量
     */
    private Long onlineLockNum;

    public Integer getIsExcep() {
        return isExcep;
    }

    public void setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
    }

    public Long getRepulseExcepNum() {
        return repulseExcepNum;
    }

    public void setRepulseExcepNum(Long repulseExcepNum) {
        this.repulseExcepNum = repulseExcepNum;
    }

    public Long getAmbiguityeExcepNum() {
        return ambiguityeExcepNum;
    }

    public void setAmbiguityeExcepNum(Long ambiguityeExcepNum) {
        this.ambiguityeExcepNum = ambiguityeExcepNum;
    }


    public Long getUploadingNum() {
        return uploadingNum;
    }

    public void setUploadingNum(Long uploadingNum) {
        this.uploadingNum = uploadingNum;
    }

    public Long getUnConsignedNum() {
        return unConsignedNum;
    }

    public void setUnConsignedNum(Long unConsignedNum) {
        this.unConsignedNum = unConsignedNum;
    }

    public Long getParty3PushFailNum() {
        return party3PushFailNum;
    }

    public void setParty3PushFailNum(Long party3PushFailNum) {
        this.party3PushFailNum = party3PushFailNum;
    }

    public Long getPendingWorkOrderNum() {
        return pendingWorkOrderNum;
    }

    public void setPendingWorkOrderNum(Long pendingWorkOrderNum) {
        this.pendingWorkOrderNum = pendingWorkOrderNum;
    }

    public Long getUploadFailNum() {
        return uploadFailNum;
    }

    public void setUploadFailNum(Long uploadFailNum) {
        this.uploadFailNum = uploadFailNum;
    }

    public Long getExcepNum() {
        return excepNum;
    }

    public TradeCount setExcepNum(Long excepNum) {
        this.excepNum = excepNum;
        return this;
    }

    public Long getRefundNum() {
        return refundNum;
    }

    public TradeCount setRefundNum(Long refundNum) {
        this.refundNum = refundNum;
        return this;
    }

    public Long getWaitPayNum() {
        return waitPayNum;
    }

    public TradeCount setWaitPayNum(Long waitPayNum) {
        this.waitPayNum = waitPayNum;
        return this;
    }

    public Long getWaitAuditNum() {
        return waitAuditNum;
    }

    public TradeCount setWaitAuditNum(Long waitAuditNum) {
        this.waitAuditNum = waitAuditNum;
        return this;
    }

    public Long getWaitFinanceAudit() {
        return waitFinanceAudit;
    }

    public TradeCount setWaitFinanceAudit(Long waitFinanceAudit) {
        this.waitFinanceAudit = waitFinanceAudit;
        return this;
    }

    public Long getFinishAuditNum() {
        return finishAuditNum;
    }

    public TradeCount setFinishAuditNum(Long finishAuditNum) {
        this.finishAuditNum = finishAuditNum;
        return this;
    }

    public Long getWaitSetTemplateNum() {
        return waitSetTemplateNum;
    }

    public TradeCount setWaitSetTemplateNum(Long waitSetTemplateNum) {
        this.waitSetTemplateNum = waitSetTemplateNum;
        return this;
    }

    public Long getWaitPrintNum() {
        return waitPrintNum;
    }

    public TradeCount setWaitPrintNum(Long waitPrintNum) {
        this.waitPrintNum = waitPrintNum;
        return this;
    }

    public Long getFinishPrintNum() {
        return finishPrintNum;
    }

    public TradeCount setFinishPrintNum(Long finishPrintNum) {
        this.finishPrintNum = finishPrintNum;
        return this;
    }

    public Long getWaitPackNum() {
        return waitPackNum;
    }

    public TradeCount setWaitPackNum(Long waitPackNum) {
        this.waitPackNum = waitPackNum;
        return this;
    }

    public Integer getOpenIdentCode() {
        return openIdentCode;
    }

    public TradeCount setOpenIdentCode(Integer openIdentCode) {
        this.openIdentCode = openIdentCode;
        return this;
    }

    public Long getWaitWeighNum() {
        return waitWeighNum;
    }

    public TradeCount setWaitWeighNum(Long waitWeighNum) {
        this.waitWeighNum = waitWeighNum;
        return this;
    }

    public Long getWaitConsignNum() {
        return waitConsignNum;
    }

    public TradeCount setWaitConsignNum(Long waitConsignNum) {
        this.waitConsignNum = waitConsignNum;
        return this;
    }

    public Long getFinishConsignNum() {
        return finishConsignNum;
    }

    public TradeCount setFinishConsignNum(Long finishConsignNum) {
        this.finishConsignNum = finishConsignNum;
        return this;
    }

    public Long getFinishedNum() {
        return finishedNum;
    }

    public TradeCount setFinishedNum(Long finishedNum) {
        this.finishedNum = finishedNum;
        return this;
    }

    public Long getWaitOutNum() {
        return waitOutNum;
    }

    public TradeCount setWaitOutNum(Long waitOutNum) {
        this.waitOutNum = waitOutNum;
        return this;
    }

    public Long getFinishOutNum() {
        return finishOutNum;
    }

    public TradeCount setFinishOutNum(Long finishOutNum) {
        this.finishOutNum = finishOutNum;
        return this;
    }

    public Long getPresellNum() {
        return presellNum;
    }

    public TradeCount setPresellNum(Long presellNum) {
        this.presellNum = presellNum;
        return this;
    }

    public Long getNum() {
        return num;
    }

    public TradeCount setNum(Long num) {
        this.num = num;
        return this;
    }

    private List<String> sysStatusList;

    /**
     * 供销商缺货
     */
    private Integer statDestStockInsufficient;

    public Integer getStatDestStockInsufficient() {
        return statDestStockInsufficient;
    }

    public void setStatDestStockInsufficient(Integer statDestStockInsufficient) {
        this.statDestStockInsufficient = statDestStockInsufficient;
    }

    public List<String> getSysStatusList() {
        return sysStatusList;
    }

    public void setSysStatusList(List<String> sysStatusList) {
        this.sysStatusList = sysStatusList;
    }

    public Long getPackSplitableNum() {
        return packSplitableNum;
    }

    public void setPackSplitableNum(Long packSplitableNum) {
        this.packSplitableNum = packSplitableNum;
    }

    public Long getHaltNum() {
        return haltNum;
    }

    public void setHaltNum(Long haltNum) {
        this.haltNum = haltNum;
    }

    public Long getInsufficientNum() {
        return insufficientNum;
    }

    public void setInsufficientNum(Long insufficientNum) {
        this.insufficientNum = insufficientNum;
    }

    public Long getUnmatchedNum() {
        return unmatchedNum;
    }

    public void setUnmatchedNum(Long unmatchedNum) {
        this.unmatchedNum = unmatchedNum;
    }

    public Long getRelationChangedNum() {
        return relationChangedNum;
    }

    public void setRelationChangedNum(Long relationChangedNum) {
        this.relationChangedNum = relationChangedNum;
    }

    public Long getItemChangedNum() {
        return itemChangedNum;
    }

    public void setItemChangedNum(Long itemChangedNum) {
        this.itemChangedNum = itemChangedNum;
    }

    public Long getAddressChangedNum() {
        return addressChangedNum;
    }

    public void setAddressChangedNum(Long addressChangedNum) {
        this.addressChangedNum = addressChangedNum;
    }

    public Long getBlackBuyerNickNum() {
        return blackBuyerNickNum;
    }

    public void setBlackBuyerNickNum(Long blackBuyerNickNum) {
        this.blackBuyerNickNum = blackBuyerNickNum;
    }

    public Long getSellerMemoChangedNum() {
        return sellerMemoChangedNum;
    }

    public void setSellerMemoChangedNum(Long sellerMemoChangedNum) {
        this.sellerMemoChangedNum = sellerMemoChangedNum;
    }

    public Map<String, Long> getExceptTagMap() {
        return exceptTagMap;
    }

    public void setExceptTagMap(Map<String, Long> exceptTagMap) {
        this.exceptTagMap = exceptTagMap;
    }

    public Long getUnattainableNum() {
        return unattainableNum;
    }

    public void setUnattainableNum(Long unattainableNum) {
        this.unattainableNum = unattainableNum;
    }

    public Long getIsLostMsgNum() {
        return isLostMsgNum;
    }

    public void setIsLostMsgNum(Long isLostMsgNum) {
        this.isLostMsgNum = isLostMsgNum;
    }

    public Long getUnCheckMemoNum() {
        return unCheckMemoNum;
    }

    public void setUnCheckMemoNum(Long unCheckMemoNum) {
        this.unCheckMemoNum = unCheckMemoNum;
    }

    public Long getUnCheckMessageNum() {
        return unCheckMessageNum;
    }

    public void setUnCheckMessageNum(Long unCheckMessageNum) {
        this.unCheckMessageNum = unCheckMessageNum;
    }

    public Long getUnCheckTotalNum() {
        return unCheckTotalNum;
    }

    public void setUnCheckTotalNum(Long unCheckTotalNum) {
        this.unCheckTotalNum = unCheckTotalNum;
    }

    public Long getDeliverExcepNum() {
        return deliverExcepNum;
    }

    public void setDeliverExcepNum(Long deliverExcepNum) {
        this.deliverExcepNum = deliverExcepNum;
    }

    public Long getUploadExcepNum() {
        return uploadExcepNum;
    }

    public void setUploadExcepNum(Long uploadExcepNum) {
        this.uploadExcepNum = uploadExcepNum;
    }

    public Long getSellerSendNoWeightNum() {
        return sellerSendNoWeightNum;
    }

    public void setSellerSendNoWeightNum(Long sellerSendNoWeightNum) {
        this.sellerSendNoWeightNum = sellerSendNoWeightNum;
    }

    public Long getWaitingReturnWmsNum() {
        return waitingReturnWmsNum;
    }

    public void setWaitingReturnWmsNum(Long waitingReturnWmsNum) {
        this.waitingReturnWmsNum = waitingReturnWmsNum;
    }

    public Long getWaitMergeNum() {
        return waitMergeNum;
    }

    public void setWaitMergeNum(Long waitMergeNum) {
        this.waitMergeNum = waitMergeNum;
    }

    public Long getItemShutoffNum() {
        return itemShutoffNum;
    }

    public void setItemShutoffNum(Long itemShutoffNum) {
        this.itemShutoffNum = itemShutoffNum;
    }

    public Long getCheckedMessageAndNoneCount() {
        return CheckedMessageAndNoneCount;
    }

    public void setCheckedMessageAndNoneCount(Long checkedMessageAndNoneCount) {
        CheckedMessageAndNoneCount = checkedMessageAndNoneCount;
    }

    public Long getCheckedMemoAndNoneCount() {
        return CheckedMemoAndNoneCount;
    }

    public void setCheckedMemoAndNoneCount(Long checkedMemoAndNoneCount) {
        CheckedMemoAndNoneCount = checkedMemoAndNoneCount;
    }

    public Long getSuiteChangeNum() {
        return suiteChangeNum;
    }

    public void setSuiteChangeNum(Long suiteChangeNum) {
        this.suiteChangeNum = suiteChangeNum;
    }

    public Long getItemProcessNum() {
        return itemProcessNum;
    }

    public void setItemProcessNum(Long itemProcessNum) {
        this.itemProcessNum = itemProcessNum;
    }

    public Long getFinanceRejectUnm() {
        return financeRejectUnm;
    }

    public void setFinanceRejectUnm(Long financeRejectUnm) {
        this.financeRejectUnm = financeRejectUnm;
    }

    public Long getOutSidRecoveryFailNum() {
        return outSidRecoveryFailNum;
    }

    public void setOutSidRecoveryFailNum(Long outSidRecoveryFailNum) {
        this.outSidRecoveryFailNum = outSidRecoveryFailNum;
    }

    public Long getCancelInterceptFailNum() {
        return cancelInterceptFailNum;
    }

    public void setCancelInterceptFailNum(Long cancelInterceptFailNum) {
        this.cancelInterceptFailNum = cancelInterceptFailNum;
    }

    public Long getShipmentFailNum() {
        return shipmentFailNum;
    }

    public void setShipmentFailNum(Long shipmentFailNum) {
        this.shipmentFailNum = shipmentFailNum;
    }

    public Long getCreateShipmentOrderFailNum() {
        return createShipmentOrderFailNum;
    }

    public void setCreateShipmentOrderFailNum(Long createShipmentOrderFailNum) {
        this.createShipmentOrderFailNum = createShipmentOrderFailNum;
    }

    public Long getRefundNumExceptNum() {
        return refundNumExceptNum;
    }

    public void setRefundNumExceptNum(Long refundNumExceptNum) {
        this.refundNumExceptNum = refundNumExceptNum;
    }



    public Long getPartPayExceptNum() {
        return partPayExceptNum;
    }

    public void setPartPayExceptNum(Long partPayExceptNum) {
        this.partPayExceptNum = partPayExceptNum;
    }

    public Long getWaitSelfAuditGoodsNum() {
        return waitSelfAuditGoodsNum;
    }

    public void setWaitSelfAuditGoodsNum(Long waitSelfAuditGoodsNum) {
        this.waitSelfAuditGoodsNum = waitSelfAuditGoodsNum;
    }

    public Long getWaitDfAuditGoodsNum() {
        return waitDfAuditGoodsNum;
    }

    public void setWaitDfAuditGoodsNum(Long waitDfAuditGoodsNum) {
        this.waitDfAuditGoodsNum = waitDfAuditGoodsNum;
    }

    public Long getUnConfirmNum() {
        return unConfirmNum;
    }

    public void setUnConfirmNum(Long unConfirmNum) {
        this.unConfirmNum = unConfirmNum;
    }

    public Long getUploadDeliveryExceptNum() {
        return uploadDeliveryExceptNum;
    }

    public void setUploadDeliveryExceptNum(Long uploadDeliveryExceptNum) {
        this.uploadDeliveryExceptNum = uploadDeliveryExceptNum;
    }

    /**
     * 上传/发货异常统计订单数
     */
    private Long uploadDeliveryExceptNum;

    public Long getRiskNum() {
        return riskNum;
    }

    public void setRiskNum(Long riskNum) {
        this.riskNum = riskNum;
    }

    public Long getOnlineLockNum() {
        return onlineLockNum;
    }

    public void setOnlineLockNum(Long onlineLockNum) {
        this.onlineLockNum = onlineLockNum;
    }


    public Long getGxItemChangeNum() {
        return gxItemChangeNum;
    }

    public void setGxItemChangeNum(Long gxItemChangeNum) {
        this.gxItemChangeNum = gxItemChangeNum;
    }

    public Long getPlatModifyExchangeItemExcept() {
        return platModifyExchangeItemExcept;
    }

    public void setPlatModifyExchangeItemExcept(Long platModifyExchangeItemExcept) {
        this.platModifyExchangeItemExcept = platModifyExchangeItemExcept;
    }


    public Long getCaiGouNum() {
        return caiGouNum;
    }

    public void setCaiGouNum(Long caiGouNum) {
        this.caiGouNum = caiGouNum;
    }

    public Long getWaitDestSendNum() {
        return waitDestSendNum;
    }

    public void setWaitDestSendNum(Long waitDestSendNum) {
        this.waitDestSendNum = waitDestSendNum;
    }


    public Long getPoJiaExceptNum() {
        return poJiaExceptNum;
    }

    public void setPoJiaExceptNum(Long poJiaExceptNum) {
        this.poJiaExceptNum = poJiaExceptNum;
    }

    public Long getSmallRefundExceptNum() {
        return smallRefundExceptNum;
    }

    public void setSmallRefundExceptNum(Long smallRefundExceptNum) {
        this.smallRefundExceptNum = smallRefundExceptNum;
    }
}

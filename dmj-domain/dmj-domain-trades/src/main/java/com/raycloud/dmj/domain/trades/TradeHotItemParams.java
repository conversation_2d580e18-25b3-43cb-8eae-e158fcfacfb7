package com.raycloud.dmj.domain.trades;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */

@Data
public class TradeHotItemParams implements Serializable {
    private static final long serialVersionUID = 4995609468803732441L;

    /**
     * 爆款类型
     *
     * 1：全部
     * 2：单件爆款
     * 3：单sku爆款
     * 4：组团
     * 5：单spu爆款
     */
    private Integer hotItemType;


    /**
     * 0/null-按照套件统计；1-按照套件明细统计
     */
    private Integer miniHotItem;


    /**
     * 仓库
     */
    private Long warehouseId;

    /**
     * 是否强制刷新
     * 0:否 1：是
     */
    private Integer forceRefresh;

    /**
     * 系统状态
     */
    private String sysStatus;

}

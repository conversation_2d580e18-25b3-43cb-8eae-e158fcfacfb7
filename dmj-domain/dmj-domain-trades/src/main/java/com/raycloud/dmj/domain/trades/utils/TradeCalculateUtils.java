package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Description: trade 属性计算的工具类 例如：重量 ，金额，体积等
 * Author: jinghe
 * Date: 2024/10/25 3:18 下午
 */
public class TradeCalculateUtils {


    /**
     * 计算重量
     *
     * @param trade
     * @return
     */
    public static double calculateTradeNetWeight(Trade trade) {
        return calculateTradeNetWeight(trade, TradeUtils.getOrders4Trade(trade));
    }

    public static double calculateTradeNetWeight(Trade trade, List<Order> orders) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getNetWeight() != null) {
            return trade.getOrigin().getNetWeight();
        }
        boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
        double netWeight = 0.0;
        for (Order order : orders) {
            if (!filterOrderCalNetWeight(order, isAfterSendGoods)) {
                netWeight += (order.getNetWeight() != null ? order.getNetWeight() * order.getNum() : 0.0);
            }
        }
        return NumberUtils.formatDouble(netWeight, 4);
    }


    public static boolean filterOrderCalNetWeight(Order order, boolean tradeIsAfterSendGoods) {
        return (order.getEnableStatus() != null && order.getEnableStatus() == 0)
                || (order.getItemSysId() == null || order.getItemSysId() <= 0 || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) || (order.getCombineId() != null && order.getCombineId() > 0))
                || (!tradeIsAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus()));
    }


    /**
     * 计算成本
     *
     * @param trade
     * @return
     */
    public static double calculateCost(Trade trade) {
        return calculateCost(trade, TradeUtils.getOrders4Trade(trade));
    }

    public static double calculateCost(Trade trade, List<Order> orders) {
        return calculateCost(trade, orders, false);
    }

    public static double calculateCost(Trade trade, boolean isFilterEnbaleStatus) {
        return calculateCost(trade, TradeUtils.getOrders4Trade(trade), isFilterEnbaleStatus);
    }

    public static double calculateCost(Trade trade, List<Order> orders, boolean isFilterEnbaleStatus) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getCost() != null) {
            return trade.getOrigin().getCost();
        }
        List<Order> filted = filterOrders4Cost(trade, orders, isFilterEnbaleStatus, false);
        BigDecimalWrapper totalCost = new BigDecimalWrapper();
        for (Order order : filted) {
            totalCost.add(MathUtils.multiply(order.getCost(), order.getNum()));
        }
        return totalCost.getDouble();
    }

    public static List<Order> filterOrders4Cost(Trade trade, List<Order> orders, boolean isFilterEnbaleStatus, boolean isFilterMerge) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<Order> filted = new ArrayList<>();
        for (Order order : orders) {
            if (isFilterEnbaleStatus && order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if ((order.getCombineId() != null && order.getCombineId() > 0)) {
                continue;
            }
            if (isFilterMerge && !Objects.equals(trade.getSid(), order.getSid())) {
                continue;
            }
            //https://gykj.yuque.com/entavv/xb9xi5/fw6zrx 1688订单待付款订单，卖家部分关闭商品，付款后，订单成本不统计交易关闭商品的成本。
            if (TradeUtils.is1688CancelOrder(trade, order)) {
                //1688开始是待付款、后面变成交易关闭的子订单，不计算在订单成本里面去。 待付款状态下的子订单取消，不计算在订单成本里面去。
                continue;
            }
            filted.add(order);
        }
        return filted;
    }


    /**
     * 计算体积
     *
     * @param trade
     * @return
     */
    public static double calculateVolume(Trade trade) {
        return calculateVolume(trade, TradeUtils.getOrders4Trade(trade));
    }

    public static double calculateVolume(Trade trade, List<Order> orders) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getVolume() != null) {
            return trade.getOrigin().getVolume();
        }
        boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
        double volume = 0.0D;
        for (Order order : orders) {
            if (!filterOrderCalVolume(order, isAfterSendGoods)) {
                volume += order.getVolume() * order.getNum();
            }
        }
        return volume;
    }

    public static boolean filterOrderCalVolume(Order order, boolean tradeIsAfterSendGoods) {
        return (order.getEnableStatus() != null && order.getEnableStatus() == 0)
                || order.getVolume() == null
                || (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) || (order.getCombineId() != null && order.getCombineId() > 0))
                || (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && (order.getSysConsigned() != null && order.getSysConsigned() - 1 != 0))
                || (!tradeIsAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus()));
    }


    /**
     * 计算销售价
     * @param trade
     * @return
     */
    public static String calculateTradeSaleFee(Trade trade) {
        return calculateTradeSaleFee(trade, TradeUtils.getOrders4Trade(trade));
    }


    public static String calculateTradeSaleFee(Trade trade, List<Order> orders) {
        BigDecimalWrapper totalSaleFee = new BigDecimalWrapper();
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getItemSysId() == null || order.getItemSysId() <= 0 || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) || (order.getCombineId() != null && order.getCombineId() > 0)) {
                continue;
            }
            //KMERP-56828 针对合单订单修改，不是主单下面的order，分销金额不计算在该主单下面。
            if (Objects.nonNull(order.getSid()) && !order.getSid().equals(trade.getSid())) {
                continue;
            }
            totalSaleFee.add(order.getSaleFee());
        }
        return totalSaleFee.getString();
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.tag.TradeTagRule;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenchaochao
 * @Date: 2020/7/28 5:56 下午
 */
public class TradeTagMatchVo implements Serializable {

    private static final long serialVersionUID = 9173576786842521415L;
    private List<TbTrade> tradeList;

    private Map<TradeTagRule, String> expr2Rules;

    public List<TbTrade> getTradeList() {
        return tradeList;
    }

    public void setTradeList(List<TbTrade> tradeList) {
        this.tradeList = tradeList;
    }

    public Map<TradeTagRule, String> getExpr2Rules() {
        return expr2Rules;
    }

    public void setExpr2Rules(Map<TradeTagRule, String> expr2Rules) {
        this.expr2Rules = expr2Rules;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 字符串模糊处理工具类
 * @date 2017-06-09-14:26
 */
public class StringBlurUtil {

    /**
     * 不模糊开始的nonBlurCharCount个字符
     * @param needBlurStr
     * @param nonBlurCharCount
     * @return
     */
    public static String nonBlurStart(String needBlurStr, int nonBlurCharCount) {
        if (null == needBlurStr || needBlurStr.trim().length() == 0) {
            return needBlurStr;
        }
        if (nonBlurCharCount < 0 ) {
            nonBlurCharCount = 0;
        }
        if (nonBlurCharCount > needBlurStr.length()) {
            nonBlurCharCount = needBlurStr.length();
        }
        char[] chars = needBlurStr.toCharArray();
        char[] blurChars = new char[nonBlurCharCount + 5];
        for (int i = 0; i < blurChars.length; i++) {
            blurChars[i] = '*';
        }
        System.arraycopy(chars ,0 , blurChars, 0, nonBlurCharCount);
        return String.valueOf(blurChars);
    }

    public static void main(String[] args) {
        String s = "1";
        System.out.println(s);
        System.out.println(blurByIndex(s, 4, 8));
    }

    /**
     * 模糊开始的blurCharCount个字符
     * @param needBlurStr
     * @param blurCharCount
     * @return
     */
    public static String blurStart(String needBlurStr, int blurCharCount) {
        if (null == needBlurStr || needBlurStr.trim().length() == 0) {
            return needBlurStr;
        }
        if (blurCharCount < 0 ) {
            blurCharCount = 0;
        }
        if (blurCharCount > needBlurStr.length()) {
            blurCharCount = needBlurStr.length();
        }
        char[] chars = needBlurStr.toCharArray();
        for (int i = 0; i < blurCharCount; i++) {
            chars[i] = '*';
        }
        return String.valueOf(chars);
    }

    /**
     * 不模糊结尾的nonBlurCharCount个字符
     * @param needBlurStr
     * @param nonBlurCharCount
     * @return
     */
    public static String nonBlurEnd(String needBlurStr, int nonBlurCharCount) {
        if (null == needBlurStr || needBlurStr.trim().length() == 0) {
            return needBlurStr;
        }
        if (nonBlurCharCount < 0 ) {
            nonBlurCharCount = 0;
        }
        if (nonBlurCharCount > needBlurStr.length()) {
            nonBlurCharCount = needBlurStr.length();
        }
        char[] chars = needBlurStr.toCharArray();
        for (int i = chars.length - 1 - nonBlurCharCount; i > -1; i--) {
            chars[i] = '*';
        }
        return String.valueOf(chars);
    }

    /**
     * 模糊结尾的nonBlurCharCount个字符
     * @param needBlurStr
     * @param blurCharCount
     * @return
     */
    public static String blurEnd(String needBlurStr, int blurCharCount) {
        if (null == needBlurStr || needBlurStr.trim().length() == 0) {
            return needBlurStr;
        }
        if (blurCharCount < 0 ) {
            blurCharCount = 0;
        }
        if (blurCharCount > needBlurStr.length()) {
            blurCharCount = needBlurStr.length();
        }
        char[] chars = needBlurStr.toCharArray();
        for (int i = chars.length - blurCharCount; i < chars.length; i++) {
            chars[i] = '*';
        }
        return String.valueOf(chars);
    }

    /**
     * 按照start end 做模糊
     * @param needBlurStr
     * @return
     */
    public static String blurByIndex(String needBlurStr, int start, int end) {
        if (null == needBlurStr || needBlurStr.trim().length() == 0) {
            return needBlurStr;
        }
        if (start < 0 ) {
            start = 0;
        }
        char[] chars = needBlurStr.toCharArray();
        if (end < 0 || end > chars.length) {
            end = chars.length + 1;
        }

        for (int i = start; i < end; i++) {
            chars[i - 1] = '*';
        }
        return String.valueOf(chars);
    }


    /**
     * 保留 前begin长度 和后面end长度的内容 其他替换为*
     *
     * commonBlur("张",1,1,"*")             --> 张*
     * commonBlur("张三",1,1,"*")           --> 张*
     * commonBlur("王二小",1,1,"*")         --> 王*小
     * commonBlur("王二小",1,4,"*")         --> 王*小
     * commonBlur("王二小",4,0,"*")         --> 王二*
     *
     *
     * commonBlur("12345678900",0,4,"*") --> *8900
     * commonBlur("12345678900",4,0,"*") --> 1234*
     * commonBlur("12322223333",3,4,"****") --> 123****3333
     *
     *
     * @param input         要脱敏的字符串
     * @param startVisible  开始不脱敏的字符个数
     * @param endVisible    结束不脱敏的字符个数
     * @return
     */
    public static String commonBlur(String data, int begin, int end,String fill) {
        if (StringUtils.isBlank(data) || (begin<=0 && end <= 0)) {
            return data;
        }
        if (begin < 0) {
            begin = 0;
        }
        if (end < 0) {
            end = 0;
        }
        if (data.length() <= begin && begin > 0) {
            return ( data.length() == 1 ? data : data.substring(0,data.length() -1) )+ fill;
        }
        String header = begin > 0 ? data.substring(0,begin) : "";
        if (end <= 0) {
            return header + fill;
        }

        int left =  data.length() - begin;
        if (end >= left) {
            end = left -1;
        }
        String tail = end > 0 ? data.substring(data.length() - end) : "";

        return header + fill + tail;
    }
}

package com.raycloud.dmj.domain.consign;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.ClueIdUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.utils.TradeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/20 13:35
 * @Description
 */
public abstract class ConsignCacheUtils {

    public static List<ConsignCache> buildCache(Staff staff, List<Long> sids, boolean exceptConsign, String consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel) {
        ConsignEventData consignEventData = ConsignEventData.builder()
                .exceptConsign(exceptConsign)
                .clientIp(clientIp)
                .consignType(consignType)
                .dummyType(dummyType)
                .noLogisticsName(noLogisticsName)
                .noLogisticsTel(noLogisticsTel)
                .staffId(staff.getId())
                .clueId(TradeUtils.getClueId())
                .build();
        String args = JSON.toJSONString(consignEventData);
        List<ConsignCache> consignCaches = new ArrayList<>();
        for (Long sid : sids) {
            ConsignCache consignCache = new ConsignCache();
            consignCache.setCompanyId(staff.getCompanyId());
            consignCache.setSid(sid);
            consignCache.setArgs(args);
            consignCaches.add(consignCache);
        }
        return consignCaches;
    }

    public static List<ConsignEventData> parseConsignCache(Staff staff, List<ConsignCache> consignCaches) {
        Map<ConsignCacheRetryArgs, Set<Long>> map = new HashMap<>();
        for (ConsignCache consignCache : consignCaches) {
            if (StringUtils.isNotEmpty(consignCache.getArgs())) {
                map.computeIfAbsent(JSON.parseObject(consignCache.getArgs(), ConsignCacheRetryArgs.class), k -> new HashSet<>()).add(consignCache.getSid());
            }
        }
        List<ConsignEventData> result = new ArrayList<>();
        if (map.size() > 0) {
            for (Map.Entry<ConsignCacheRetryArgs, Set<Long>> entry : map.entrySet()) {
                ConsignEventData consignEventData = ConsignEventData.builder()
                        .exceptConsign(entry.getKey().getExceptConsign())
                        .consignType(entry.getKey().getConsignType())
                        .clientIp(entry.getKey().getClientIp())
                        .dummyType(entry.getKey().getDummyType())
                        .noLogisticsName(entry.getKey().getNoLogisticsName())
                        .noLogisticsTel(entry.getKey().getNoLogisticsTel())
                        .staffId(entry.getKey().getStaffId())
                        .sids(new ArrayList<>(entry.getValue()))
                        .clueId(ClueIdUtils.getClueId(staff))
                        .build();
                result.add(consignEventData);
            }
        }
        return result;
    }

}

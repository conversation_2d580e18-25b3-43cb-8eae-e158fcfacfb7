package com.raycloud.dmj.domain.trades.params;

import java.io.Serializable;

/***
 * @DESCRIPTION: 拣选领单
 * <AUTHOR>
 * @params:
 * @return:
 * @Date: 2021/3/18 9:05 下午 
 * @Modified By:  
 */
public class TradePickingParams implements Serializable {


    private static final long serialVersionUID = -5444259354333072232L;

    private String value;

    /**
     * sid:系统订单号,shortId:平台订单号,outSid:运单号
     */
    private String pickParamsName;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPickParamsName() {
        return pickParamsName;
    }

    public void setPickParamsName(String pickParamsName) {
        this.pickParamsName = pickParamsName;
    }
}

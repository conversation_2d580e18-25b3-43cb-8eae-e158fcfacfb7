package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 订单物流跟踪信息
 * <AUTHOR>
 * @since 16/3/28
 */
public class TradeTransitStepInfo implements Serializable {

	/**
	 * 指明当前节点揽收、派送，签收。
	 */
	private String action;
	/**
	 * 状态发生的时间
	 */
	private String statusTime;
	/**
	 * 状态描述
	 */
	private String statusDesc;

	public String getAction() {
		return action;
	}

	@JSONField(name = "Remark")
	public void setAction(String action) {
		this.action = action;
	}

	public String getStatusTime() {
		return statusTime;
	}

	@JSONField(name = "AcceptTime")
	public void setStatusTime(String statusTime) {
		this.statusTime = statusTime;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	@JSONField(name = "AcceptStation")
	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}
}

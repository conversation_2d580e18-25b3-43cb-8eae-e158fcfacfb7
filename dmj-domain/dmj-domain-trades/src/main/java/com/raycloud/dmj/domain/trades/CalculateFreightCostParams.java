package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

/**
 * @Author: ruanyaguang
 * @Date : 2018/9/25
 * @Info :
 */
public class CalculateFreightCostParams implements Serializable {
    private Staff staff;

    private Long sid;

    private String weight;

    private Trade  currentTrade;

    public static class Builder {
        private Staff staff;

        private Long sid;

        private String weight;

        private Trade  currentTrade;

        public CalculateFreightCostParams build() {
            CalculateFreightCostParams params = new CalculateFreightCostParams();
            params.staff = staff;
            params.sid = sid;
            params.weight = weight;
            params.currentTrade = currentTrade;
            return params;
        }

        public Builder staff(Staff staff) {
            this.staff = staff;
            return this;
        }

        public Builder sid(Long sid) {
            this.sid = sid;
            return this;
        }

        public Builder weight(String weight) {
            this.weight = weight;
            return this;
        }

        public Builder currentTrade(Trade trade) {
            this.currentTrade = trade;
            return this;
        }
    }

    public Staff getStaff() {
        return staff;
    }

    public Long getSid() {
        return sid;
    }

    public String getWeight() {
        return weight;
    }

    public Trade getCurrentTrade() {
        return currentTrade;
    }

    private CalculateFreightCostParams() {

    }
}
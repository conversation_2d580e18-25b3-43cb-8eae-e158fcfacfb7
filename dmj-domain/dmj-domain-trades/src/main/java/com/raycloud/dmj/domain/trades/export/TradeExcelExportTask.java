package com.raycloud.dmj.domain.trades.export;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.Logs;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Table(name = "trade_export_task")
public class TradeExcelExportTask extends Model {
    public static final Integer IS_BATCH = 1;        //批量导出标识


    public static final Integer TASK_TYPE_TRADE = 1;        //订单导出
    public static final Integer TASK_TYPE_TRADE_ORDER = 2;        //订单明细导出


    public static final Integer TASK_PREPARE = 1;        //任务准备中
    public static final Integer TASK_READY = 2;        //任务就绪
    public static final Integer TASK_RUNNING = 3;        //任务执行中
    public static final Integer TASK_FAIL = 4;        //任务运行失败
    public static final Integer TASK_SUCCESS = 5;     //任务运行成功

    //归档订单导出标识
    public static final String TASK_EXPORT_3MONTH = "exportThreeMonthAgo";
    public static final String EXPORT_QUERY_PARAMS = "exportQueryParams";
    public static final String OPEN_EXPORT_NEW_SQL = "openExportNewSQL";
    public static final String EXPORT_OPEN_PGL = "exportOpenPgl";

    private Long id ;
    private Long companyId;
    private Long staffId;
    /**
     * 导出类型 1订单导出 2明细导出
     */
    private Integer exportType;
    /**
     * 导出订单总数
     */
    private Long tradeCount;
    /**
     * 任务唯一Key
     */
    private String taskKey;

    /**
     * 子任务的key
     */
    private List<String> subTaskKey;

    /**
     * 插入时间
     */
    private Long insertTime;
    /**
     * 导出状态 1任务准备 2任务就绪 3导出中 4已完成 5已失败
     */
    private Integer exportStatus;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 下载中心任务主键ID
     */
    private Long downloadCenterId;
    /**
     * 导出文件请求参数
     */
    private String fileDownloadParams;
    /**
     * 导出文件构建的sql
     */
    private String fileDownloadSql;
    /**
     * 查询ID
     */
    private Long queryId;
    /**
     * 任务所属环境
     */
    private String envProfile;

    /**
     * 导出任务参数配置
     */
    private String taskParams;

    /**
     * 导出数量较小的sid列表
     */
    private String taskSidList;

    /**
     * taskParams字段缓存
     * 减少JSON转换
     */
    private Map<String, Object> taskParamsMap;

    /**
     * TJ白名单（不持久化）
     * 是否开启导出订单图片
     * 白名单内的客户，导出订单文件时，最多支持导出10张图片
     */
    private boolean openDownloadImages;

    /**
     * 导出流程上下文(不持久化)
     */
    private Object tradeExportContext;

    public String buildRedisKey(){
        String taskKey = new StringBuilder().append(this.getCompanyId()).append("_").append(this.getStaffId()).append("_").append(this.getExportType()).append("_").append(queryId).append("_").append(this.getDownloadCenterId()).toString();
        if (taskKey.length() > 50){
            taskKey = taskKey.substring(0, 50);
        }
        return taskKey;
    }

    /**
     * 是否是归档订单导出
     * @return
     */
    public boolean export3Month(){
        if (null != taskParams && taskParams.contains(TASK_EXPORT_3MONTH)){
            return true;
        }
        return false;
    }

    /**
     * 是否是归档订单导出 --使用冷库查询
     * @return
     */
    public boolean export3MonthByColdData(){
        if (null != taskParams && taskParams.contains(EXPORT_QUERY_PARAMS)){
            return true;
        }
        return false;
    }

    public boolean allowPgl(){
        Object allowPgl = getTaskParamsByKey(EXPORT_OPEN_PGL);
        return Objects.nonNull(allowPgl) && (boolean) allowPgl;
    }

    public Object getTaskParamsByKey(String key){
        if (Objects.isNull(taskParamsMap)){
            taskParamsMap = parseTaskParams();
        }
        return taskParamsMap.get(key);
    }

    private Map<String, Object> parseTaskParams(){
        try {
            if (Objects.nonNull(taskParams)){
                return JSON.parseObject(taskParams, Map.class);
            }
        }catch (Exception e){
            Logs.error("转换TradeExcelExportTask中扩展配置出错，jsonStr:" + taskParams, e);
        }
        return new HashMap<>();
    }

    public boolean openExportNewSQL(){
        Object openExportNewSQL = getTaskParamsByKey(OPEN_EXPORT_NEW_SQL);
        return Objects.nonNull(openExportNewSQL) && (boolean) openExportNewSQL;
    }

    public List<Long> getSelectedSids(){
        if (StringUtils.isBlank(taskParams)){
            return Collections.emptyList();
        }
        JSONObject object = JSON.parseObject(taskParams);
        if(!StringUtils.equals(object.getString("selectedExport"),"1")){
            return Collections.emptyList();
        }
        return Strings.getAsLongList(object.getString("selectedSids"),",",false);
    }

    public TradeExcelExportTask clearContext(){
        this.tradeExportContext = null;
        return this;
    }
}

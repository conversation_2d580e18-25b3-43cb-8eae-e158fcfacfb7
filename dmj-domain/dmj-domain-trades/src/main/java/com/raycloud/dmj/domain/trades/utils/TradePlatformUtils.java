package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: chenchaochao
 * @Date: 2020/12/8 4:27 下午
 */
public class TradePlatformUtils {

    /**
     * 是否存在source的订单
     * @param trades
     * @param source
     * @return
     */
    public static boolean isExsitPlatTrade(List<? extends Trade> trades, String source, String subsource) {
        if (StringUtils.isBlank(source) || StringUtils.isBlank(subsource)) return false;
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                if (source.equals(trade.getSource()) && subsource.equals(trade.getSubSource())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否存在有该source下的店铺订单
     * @param trades
     * @param source
     * @return
     */
    public static boolean isExsitPlatUserTrade(Staff staff, List<Trade> trades, String source){
        if (StringUtils.isBlank(source)) return false;
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                User user = staff.getUserByUserId(trade.getUserId());
                if (source.equals(user.getSource())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否存在该source的订单
     * @param trades
     * @param source
     * @return
     */
    public static boolean isExsitSourceTrade(List<? extends Trade> trades, String source) {
        if (StringUtils.isBlank(source)) return false;
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                if (source.equals(trade.getSource())) {
                    return true;
                }
            }
        }
        return false;
    }



    public static boolean platformsMatch(Staff staff, Trade trade, String ... platForms) {
        for(String platForm:platForms){
            boolean b = TradeUtils.platformMatch(staff, trade, platForm);
            if(b){
                return true;
            }
        }
        return false;
    }

    public static boolean platformSourceMatch(Staff staff, Trade trade, String ... sources) {
        for(String source:sources){
             if(Objects.equals(trade.getSource(),source)){
                 return true;
             }
        }
        return false;
    }

    public static boolean platformMatchMd5NotNull(Staff staff, Trade trade,List<String> sources) {
        for(String source:sources){
            if(Objects.equals(trade.getSource(),source)&&StringUtils.isNotBlank(trade.getAddressMd5())){
                return true;
            }
        }
        return false;
    }

    public static String getTradeUserSource(Staff staff, Trade trade) {
        if(!TradeUtils.isSplit(trade)){
            return trade.getSource();
        }
        if (!Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)) {
            return trade.getSource();
        }
        if (StringUtils.isNotBlank(trade.getSplitSource())) {
            return trade.getSplitSource();
        }
        User user = staff.getUserByUserId(trade.getUserId());
        if(user==null){
            return trade.getSource();
        }
        return user.getSource();
    }

    public static boolean platformMatchMd5NotNull(Staff staff, String source, boolean noNullAddressMd5, List<String> sources) {
        for (String element : sources) {
            if (Objects.equals(element, source) && noNullAddressMd5) {
                return true;
            }
        }
        return false;
    }
}

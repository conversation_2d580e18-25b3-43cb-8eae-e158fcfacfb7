package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * 快递公司
 *
 * <AUTHOR>
 */
@Table(name = "express_company" , migratable = false)
public class ExpressCompany extends Model {

    private static final long serialVersionUID = -3820085833116893270L;

    /**
     * 快递公司编号
     */
    private long id;

    /**
     * 快递公司名称
     */
    private String name;

    /**
     * 快递公司代码
     */
    private String code;
    /**
     * 类型
     */
    private Integer cpType;

    /**
     * 快递公司发货代码
     */
    private String consignCode;

    /**
     * 自定义快递公司名称
     */
    private String customName;

    /**
     * 对应的京东快递公司的id
     */
    private Long expressCompanyJdId;

    /**
     * 0表示tb、1表示jd
     */
    private Integer source;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date modified;

    /**
     * 数据的可用状态，0表示不可用，1表示可用
     */
    private Integer enableStatus;

    /**
     * 是否是跨境渠道 不存入数据库
     */
    private Integer isKuaj;

    private String jdCpCode;

    private Integer shopeeLogisticId;

    private Integer isDefault;

    public long getId() {
        return id;
    }

    public ExpressCompany setId(long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public ExpressCompany setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ExpressCompany setCode(String code) {
        this.code = code;
        return this;
    }

    public String getConsignCode() {
        return consignCode;
    }

    public void setConsignCode(String consignCode) {
        this.consignCode = consignCode;
    }

    public Date getCreated() {
        return created;
    }

    public ExpressCompany setCreated(Date created) {
        this.created = created;
        return this;
    }

    public Date getModified() {
        return modified;
    }

    public ExpressCompany setModified(Date modified) {
        this.modified = modified;
        return this;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public ExpressCompany setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
        return this;
    }

    public Integer getShopeeLogisticId() {
        return shopeeLogisticId;
    }

    public void setShopeeLogisticId(Integer shopeeLogisticId) {
        this.shopeeLogisticId = shopeeLogisticId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ExpressCompany that = (ExpressCompany) o;

        if (id != that.id) return false;
        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (code != null ? code.hashCode() : 0);
        return result;
    }

    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public Long getExpressCompanyJdId() {
        return expressCompanyJdId;
    }

    public void setExpressCompanyJdId(Long expressCompanyJdId) {
        this.expressCompanyJdId = expressCompanyJdId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getIsKuaj() {
        if (isKuaj == null){
            return 0;
        }
        return isKuaj;
    }

    public void setIsKuaj(Integer isKuaj) {
        this.isKuaj = isKuaj;
    }

    public String getJdCpCode() {
        return jdCpCode;
    }

    public void setJdCpCode(String jdCpCode) {
        this.jdCpCode = jdCpCode;
    }

    public Integer getCpType() {
        return cpType;
    }

    public void setCpType(Integer cpType) {
        this.cpType = cpType;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}

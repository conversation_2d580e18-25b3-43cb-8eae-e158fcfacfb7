package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * TradeSeparateWeightParams
 *
 * <AUTHOR>
 * @Date 2019-09-10
 * @Time 22:51
 */
public class TradeSeparateWeightParams implements Serializable {
    private static final long serialVersionUID = -5431541437236302262L;

    /**
     * 运单号
     */
    private String ticketsNum;
    /**
     * 重量
     */
    private Double weight;
    /**
     * 体积
     */
    private Double volume;
    /**
     * 操作称重台名称
     */
    private String workConsole;
    /**
     * 目的地
     */
    private String destination;

    /**
     * 交易订单 trade(默认), 采退单 caigouReturn
     */
    private String orderType;

    public String getTicketsNum() {
        return ticketsNum;
    }

    public void setTicketsNum(String ticketsNum) {
        this.ticketsNum = ticketsNum;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public String getWorkConsole() {
        return workConsole;
    }

    public void setWorkConsole(String workConsole) {
        this.workConsole = workConsole;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
}

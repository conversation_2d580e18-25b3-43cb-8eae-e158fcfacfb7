package com.raycloud.dmj.domain.trades.request;

import com.raycloud.dmj.domain.trades.Trade;

import java.io.Serializable;

/**
 * 快麦开放平台更改地址 request
 */
public class ModifyUnAuditTradeReceiveAddressRequest implements Serializable {

    private static final long serialVersionUID = 3908295154075783034L;

    /**
     * 快麦系统订单号 必填
     */
    private Long sid;

    /**
     * 收货人姓名 非必填
     */
    private String receiverName;

    /**
     * 收货人固定电话 手机号码或电话号码至少填写一个
     */
    private String receiverMobile;

    /**
     * 收货人手机号码 手机号码或电话号码至少填写一个
     */
    private String receiverPhone;

    /**
     * 收件邮编
     */
    private String receiverZip;

    /**
     * 省
     */
    private String receiverState;

    /**
     * 市
     */
    private String receiverCity;

    /**
     * 区
     */
    private String receiverDistrict;

    /**
     * 详细地址 必填  长度 <= 228
     */
    private String receiverAddress;

    public Trade convertToTrade(Trade origin) {
        Trade trade = new Trade();

        trade.setSid(this.sid);
        trade.setType(origin.getType());
        trade.setReceiverName(this.receiverName == null ? origin.getReceiverName() : this.receiverName);
        trade.setReceiverMobile(this.receiverMobile == null ? origin.getReceiverMobile() : this.receiverMobile);
        trade.setReceiverPhone(this.receiverPhone == null ? origin.getReceiverPhone() : this.receiverPhone);
        trade.setReceiverZip(this.receiverZip == null ? origin.getReceiverZip() : this.receiverZip);
        trade.setReceiverState(this.receiverState == null ? origin.getReceiverState() : this.receiverState);
        trade.setReceiverCity(this.receiverCity == null ? origin.getReceiverCity() : this.receiverCity);
        trade.setReceiverDistrict(this.receiverDistrict == null ? origin.getReceiverDistrict() : this.receiverDistrict);
        trade.setReceiverAddress(this.receiverAddress == null ? origin.getReceiverAddress() : this.receiverAddress);

        return trade;
    }


    /**
     * getter setter
     */
    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverZip() {
        return receiverZip;
    }

    public void setReceiverZip(String receiverZip) {
        this.receiverZip = receiverZip;
    }

    public String getReceiverState() {
        return receiverState;
    }

    public void setReceiverState(String receiverState) {
        this.receiverState = receiverState;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

}

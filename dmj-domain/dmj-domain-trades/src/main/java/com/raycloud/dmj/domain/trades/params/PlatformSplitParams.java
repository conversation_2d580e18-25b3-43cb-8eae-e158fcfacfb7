package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.trades.Order;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 平台拆单参数
 * <AUTHOR>
 * @Date 2022/1/16 4:33 下午
 */
public class PlatformSplitParams implements Serializable {
    private static final long serialVersionUID = -5132812991371247589L;

    private Long sid;

    private List<List<Order>> splitOrders;


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public List<List<Order>> getSplitOrders() {
        return splitOrders;
    }

    public void setSplitOrders(List<List<Order>> splitOrders) {
        this.splitOrders = splitOrders;
    }

}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: ruanyaguang
 * @Date : 2018/9/19
 * @Info : 订单称重参数
 */
public class TradeWeighParams implements Serializable {
    private Staff staff;

    private Map<Long, Trade> sidTradeMap;

    private String clientIp;

    private String outerId;

    public static class Builder {
        private Staff staff;

        private String sids;

        private String weight;

        private String cost;

        private String clientIp;

        private String outerId;

        private String actualVolume;

        private String actualLengthWidthAndHeight;

        public TradeWeighParams build() {
            TradeWeighParams params = new TradeWeighParams();
            if(StringUtils.isEmpty(sids)){
                throw new IllegalArgumentException("请输入sids参数");
            }
            if(StringUtils.isEmpty(weight)){
                throw new IllegalArgumentException("请输入weight参数");
            }
            if(StringUtils.isEmpty(cost)){
                throw new IllegalArgumentException("请输入cost参数");
            }

            Long[] sidArray = ArrayUtils.toLongArray(sids);
            String[] weighArray = ArrayUtils.toStringArray(weight);
            String[] costArray = ArrayUtils.toStringArray(cost);
            String[] actualVolumeArray = ArrayUtils.toStringArray(actualVolume);
            String[] lengthWeightAndHeightArray = ArrayUtils.toStringArray(actualLengthWidthAndHeight);
            if(sidArray.length != weighArray.length){
                throw new IllegalArgumentException("sids和weigh的数组长度必须要一致");
            }
            if(sidArray.length != costArray.length){
                throw new IllegalArgumentException("sids和cost的数组长度必须要一致");
            }

            Map<Long, Trade> sidTradeMap = new HashMap<Long, Trade>();
            for (int i = 0; i < sidArray.length; i++) {
                Trade trade = new TbTrade();
                trade.setSid(sidArray[i]);
                trade.setWeight(Double.parseDouble(weighArray[i].trim()));
                trade.setActualPostFee(costArray[i]);
                if (actualVolumeArray != null && i < actualVolumeArray.length) {
                    trade.setActualVolume(actualVolumeArray[i]);
                }
                if (lengthWeightAndHeightArray != null && i < lengthWeightAndHeightArray.length) {
                    trade.setActualLengthWidthAndHeight(lengthWeightAndHeightArray[i]);
                }
                sidTradeMap.put(sidArray[i], trade);
            }
            params.staff = staff;
            params.sidTradeMap = sidTradeMap;
            params.clientIp = clientIp;
            params.outerId = outerId;

            return params;
        }

        public Builder staff(Staff staff) {
            this.staff = staff;
            return this;
        }

        public Builder sids(String sids) {
            this.sids = sids;
            return this;
        }

        public Builder weight(String weight) {
            this.weight = weight;
            return this;
        }

        public Builder cost(String cost) {
            this.cost = cost;
            return this;
        }

        public Builder clientIp(String clientIp) {
            this.clientIp = clientIp;
            return this;
        }

        public Builder outerId(String outerId) {
            this.outerId = outerId;
            return this;
        }

        public Builder actualVolume(String actualVolume) {
            this.actualVolume = actualVolume;
            return this;
        }

        public Builder actualLengthWidthAndHeight(String actualLengthWidthAndHeight) {
            this.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
            return this;
        }
    }

    public Staff getStaff() {
        return staff;
    }

    public Map<Long, Trade> getSidTradeMap() {
        return sidTradeMap;
    }

    public String getClientIp() {
        return clientIp;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }
}
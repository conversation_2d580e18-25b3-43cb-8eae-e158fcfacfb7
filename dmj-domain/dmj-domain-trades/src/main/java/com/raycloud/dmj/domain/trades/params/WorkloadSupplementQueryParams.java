package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.trades.Trade;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> xubin
 * @desc:  补录绩效入参
 * @date : 12/20/21  10:18 AM
 */
@Data
public class WorkloadSupplementQueryParams implements Serializable{


    private static final long serialVersionUID = 5373739179089438213L;

    /**
     * 订单号
     */
    private Long sid;

    /**
     * 加工单号
     */
    private String stockProductOrderCode;

    /**
     * 内部单号
     */
    private Long shortId;

    /**
     * 是否可重复扫描
     */
    private boolean repeatable;

    /**
     * 是否强制
     */
    private boolean force;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 拣选号
     */
    private String pickingCode;

    /**
     * 波次号
     */
    private Long waveId;

    /**
     * 首波次号
     */
    private Long firstWaveId;

    private boolean finishByWaveFirstAndEnd;

    /**
     * 绩效员工id
     */
    private Long staffId;

    /**
     * 绩效员工名称
     */
    private String staffName;

    /**
     * 补入人员id
     */
    private Long createId;

    /**
     * 补入人员名称
     */
    private String createName;

    /**
     * 扫描类型 0-系统订单号 1-拣选号 2-快递单号 3-波次号 4-内部单号 5-扫单按波 6-加工单
     */
    private Integer checkGoodsType;

    /**
     * tab来源 1-包装验货 2-拣选领单 3-自定义 4-验货登记
     */
    private Integer source;


    /**
     * 终端来源 1PC 2PDA 3APP
     */
    private Integer terminalSource;

    /**
     * 是否完成,false则返回可补录订单数,并不执行补录
     */
    private boolean complete;

    private String clientIP;

    // 完成补录的入参

    /**
     * 自定义Id
     */
    private Long customId;

    /**
     * 自定义名称
     */
    private String customName;

    private List<Long> sids;

    private List<Long> waveIds;

    /**
     * 是否区间补录
     */
    private boolean section;

    /**
     * 是否是查询接口 (区分查询和完成接口)
     */
    private boolean query;

    /**
     * 需要返回具体结果
     */
    private Boolean needSupplementResult;

    /**
     * 踢出波次的sids
     */
    private List<Long> waveNotInSids;

    /**
     * 被过滤的trades
     */
    private List<Trade> filterTrades;

    List<Long> existSupplementLogSidList;

    List<Long> existCheckGoodsSidList;

    Set<Long> existTradeTraceSids;

    /**
     * 绩效员工ids
     */
    private List<Long> staffIds;
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description PackWaveResult
 * @Date 2021/7/27 2:49 下午
 * @Created 杨恒
 */
public class PackWaveResult implements Serializable {

    private static final long serialVersionUID = -4943098890084516409L;

    /**
     * 波次号
     */
    private Long waveId;

    private Long combineParcelId;

    /**
     * 波次验货成功sids
     */
    private List<String> successSids;
    /**
     * 波次验货成功主单sids
     */
    private List<Long> successMainSids;

    /**
     * 波次验货失败sids
     */
    private Map<String, String> failSidsMap;

    /**
     * 波次历史验货成功数
     */
    private Integer packedCount;

    /**
     * 波次内订单数
     */
    private Integer total;

    /**
     *  验货失败返回信息集合
     */
    private List<FailWaveMessage> failWaveMessages;


    public List<FailWaveMessage> getFailWaveMessages() {
        return failWaveMessages;
    }

    public void setFailWaveMessages(List<FailWaveMessage> failWaveMessages) {
        this.failWaveMessages = failWaveMessages;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public List<String> getSuccessSids() {
        return successSids;
    }

    public void setSuccessSids(List<String> successSids) {
        this.successSids = successSids;
    }

    public List<Long> getSuccessMainSids() {
        return successMainSids;
    }

    public void setSuccessMainSids(List<Long> successMainSids) {
        this.successMainSids = successMainSids;
    }

    public Map<String, String> getFailSidsMap() {
        return failSidsMap;
    }

    public void setFailSidsMap(Map<String, String> failSidsMap) {
        this.failSidsMap = failSidsMap;
    }


    public Integer getPackedCount() {
        return packedCount;
    }

    public void setPackedCount(Integer packedCount) {
        this.packedCount = packedCount;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public static PackWaveResult build(Long waveId, List<Trade> trades, Integer packedCount, Integer total) {
        PackWaveResult result = new PackWaveResult();
        result.setWaveId(waveId);
        result.setTotal(total);
        result.setPackedCount(packedCount);

        if (!CollectionUtils.isEmpty(trades)) {
            List<String> successList = new ArrayList<>();
            Map<String, String> failMap = new HashMap<>();
            for (Trade trade : trades) {
                if (trade.getIsPackage().equals(CommonConstants.JUDGE_YES)) {
                    successList.add(trade.getSid().toString());
                } else {
                    failMap.putIfAbsent(trade.getSid().toString(), trade.getExceptMemo());
                }
            }
            result.setSuccessSids(successList);
            result.setPackedCount(result.getPackedCount() + successList.size());
            result.setFailSidsMap(failMap);
        }
        return result;
    }
}

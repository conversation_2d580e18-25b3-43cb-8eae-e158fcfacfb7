package com.raycloud.dmj.domain.trades.search;

import java.io.Serializable;

/**
 * @Description <pre>
 *  支持时间范围查询
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-31
 */
public enum TimeTypeEnum implements Serializable {

    PAY_TIME("pay_time","支付时间"),

    CONSIGN_TIME("consign_time","系统发货时间"),

    EXPRESS_PRINT_TIME("express_print_time","打印时间"),

    CREATED("created","创建时间"),

    UPD_TIME("upd_time","更新时间"),

    ;

    private String field,name;

    TimeTypeEnum(String field, String name) {
        this.field = field;
        this.name = name;
    }

    public String getField() {
        return field;
    }

    public String getName() {
        return name;
    }
}

package com.raycloud.dmj.domain.trades.oSActivity;

import java.util.List;

/**
 * Created by huangfuhua on 2020-08-25.
 */
public class TradeOSActivityCondition {
    private Long companyId;
    private Long userId;
    private String activityId;
    private List<String> activityIdList;
    private Long sid;
    private Long oid;
    private List<Long> sidList;
    private List<Long> oidList;
    private Integer isFinishAward;
    private Integer dbNo;
    private Integer isAward;
    private Integer isCancelHalt;
    private Integer dbKey;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public List<String> getActivityIdList() {
        return activityIdList;
    }

    public void setActivityIdList(List<String> activityIdList) {
        this.activityIdList = activityIdList;
    }

    public void setSidList(List<Long> sidList) {
        this.sidList = sidList;
    }

    public List<Long> getSidList() {
        return sidList;
    }

    public void setOidList(List<Long> oidList) {
        this.oidList = oidList;
    }

    public List<Long> getOidList() {
        return oidList;
    }

    public void setIsFinishAward(Integer isFinishAward) {
        this.isFinishAward = isFinishAward;
    }

    public Integer getIsFinishAward() {
        return isFinishAward;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setIsAward(Integer isAward) {
        this.isAward = isAward;
    }

    public Integer getIsAward() {
        return isAward;
    }

    public void setIsCancelHalt(Integer isCancelHalt) {
        this.isCancelHalt = isCancelHalt;
    }

    public Integer getIsCancelHalt() {
        return isCancelHalt;
    }

    public void setDbKey(Integer dbKey) {
        this.dbKey = dbKey;
    }

    public Integer getDbKey() {
        return dbKey;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * Created by windy26205 on 18/11/30.
 * 专门给验证用，功能已经冗余到TradeStatusUtil里
 */
public class TradeStatusValidUtils {

    private static Map<String,String> VIEW_CH_STATUS_MAP = new HashMap<String, String>();

    static{
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_BUYER_PAY, "待付款");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_SELLER_SEND_GOODS, "已发货");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_CLOSED, "交易关闭");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED, "交易成功");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_AUDIT,"待审核");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT,"待财审");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT,"待打印发货单");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT,"待打印快递单");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_WEIGHT,"待称重");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_PACKAGE,"待包装");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_SEND_GOODS,"待发货");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_FINISHED_AUDIT,"审核完成");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_CANCEL,"交易作废");
        VIEW_CH_STATUS_MAP.put(Trade.SYS_STATUS_WAIT_MANUAL_AUDIT,"待审核");
    }

    public static void validSysStatus(Trade trade,TradeConfig tradeConfig,Set<String> validSysStatusSet,String optMsg){
        if(validSysStatusSet == null || validSysStatusSet.size() <=0){
            return;
        }

        String tradeSysStatusView = convertSysStatusToView(trade,tradeConfig);
        boolean valid = validSysStatusSet.contains(tradeSysStatusView);
        if(!valid &&
                validSysStatusSet.contains(Trade.SYS_STATUS_FINISHED_AUDIT) &&
                (tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_PACKAGE) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_WEIGHT) ||
                        tradeSysStatusView.equals(Trade.SYS_STATUS_WAIT_SEND_GOODS))){
            valid = true;
        }
        if(!valid){
            throw new IllegalArgumentException(String.format("订单%s系统状态为%s,无法%s", trade.getSid(), getChSysStatusView(tradeSysStatusView), optMsg));
        }
    }

    public static void validSysStatus(List<Trade> tradeList,TradeConfig tradeConfig,String[] validSysStatusArray,String optMsg){
        if(validSysStatusArray == null || validSysStatusArray.length <=0){
            return;
        }
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        Set<String> validSysStatusSet =new HashSet<String>();

        for(String validStatus : validSysStatusArray){
            validSysStatusSet.add(validStatus);
        }

        for(Trade trade : tradeList){
            validSysStatus(trade,tradeConfig,validSysStatusSet,optMsg);
        }
    }

    /**
     * 转换系统状态给视图展现
     *
     * @param trade
     * @return
     */
    public static String convertSysStatusToView(Trade trade, TradeConfig tradeConfig) {
        //出库单不验证店铺
        if (trade.isOutstock()) {
            return Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) ? Trade.SYS_STATUS_WAIT_SEND_GOODS : trade.getSysStatus();
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
            return Trade.SYS_STATUS_CANCEL;
        }
        if (!Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return trade.getSysStatus();
        }
        // 无需快递的类型，直接就是待发货状态
        if (trade.getTemplateId() != null && trade.getTemplateId() == 0L) {
            return Trade.SYS_STATUS_WAIT_SEND_GOODS;
        }
        if (TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_EXPRESS_PRINT;
        }
        if (tradeConfig.getOpenDeliverPrint() == 1 && TradeStatusUtils.getPrintStatus(trade.getDeliverPrintTime()) == 0) {
            return Trade.SYS_STATUS_WAIT_DELIVERY_PRINT;
        }
        if (tradeConfig.getOpenPackageExamine() == 1 && !getIs(trade.getIsPackage())) {
            return Trade.SYS_STATUS_WAIT_PACKAGE;
        }
        if (tradeConfig.getOpenPackageWeigh() == 1 && !getIs(trade.getIsWeigh())) {
            return Trade.SYS_STATUS_WAIT_WEIGHT;
        }
        return Trade.SYS_STATUS_WAIT_SEND_GOODS;
    }

    private static boolean getIs(Integer is){
        return !(is == null||is == 0);
    }

    public static String getChSysStatusView(String sysStatus){
        String chSysStatus =  VIEW_CH_STATUS_MAP.get(sysStatus);
        //Assert.isTrue(chSysStatus == null ,String.format("%s不是有效的系统状态",sysStatus));
        return chSysStatus;
    }

}

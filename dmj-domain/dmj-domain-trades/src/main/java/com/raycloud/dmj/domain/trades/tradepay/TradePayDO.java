package com.raycloud.dmj.domain.trades.tradepay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: ccc
 * @mbg.generated
 * 表名: trade_pay
 * @date 2020/03/20
 */
public class TradePayDO implements Serializable {
    private static final long serialVersionUID = -6107769006442259808L;
    /**
     * @mbg.generated
     * 表字段: id
     */
    private Long id;

    /**
     * @mbg.generated
     * 公司id
     * 表字段: company_id
     */
    private Long companyId;

    /**
     * @mbg.generated
     * 店铺id
     * 表字段: user_id
     */
    private Long userId;

    /**
     * @mbg.generated
     * 系统单号
     * 表字段: sid
     */
    private Long sid;

    /**
     * 内部单号
     */
    private Long shortId;

    /**
     * @mbg.generated
     * 平台订单id
     * 表字段: tid
     */
    private String tid;

    /**
     * @mbg.generated
     * 支付单号
     * 表字段: pay_id
     */
    private String payId;

    /**
     * @mbg.generated
     * 支付单状态，1:待审核，2:已生效，3已作废
     * 表字段: order_status
     */
    private Integer orderStatus;

    /**
     * @mbg.generated
     * 来源 1为手工创建，2为平台创建
     * 表字段: source
     */
    private Integer source;

    /**
     * @mbg.generated
     * 支付金额
     * 表字段: pay_fee
     */
    private Double payFee;

    /**
     * @mbg.generated
     * 支付方式
     * 表字段: pay_type
     */
    private Integer payType;

    /**
     * @mbg.generated
     * 支付日期
     * 表字段: pay_date
     */
    private Date payDate;
    /**
     * 订单付款时间
     */
    private Date payTime;

    /**
     * @mbg.generated
     * 收款账号
     * 表字段: account_no
     */
    private String accountNo;

    /**
     * @mbg.generated
     * 买家账号
     * 表字段: buyer_account_no
     */
    private String buyerAccountNo;
    /**
     * 同步标志
     */
    private Integer syncFlag;
    /**
     * 是否有效
     */
    private Integer enableStatus;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * @mbg.generated
     * 创建时间
     * 表字段: created
     */
    private Date created;

    /**
     * @mbg.generated
     * 修改时间
     * 表字段: modified
     */
    private Date modified;
    /**
     * 分表id
     */
    private Integer tableId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Double getPayFee() {
        return payFee;
    }

    public void setPayFee(Double payFee) {
        this.payFee = payFee;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBuyerAccountNo() {
        return buyerAccountNo;
    }

    public void setBuyerAccountNo(String buyerAccountNo) {
        this.buyerAccountNo = buyerAccountNo;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }
}
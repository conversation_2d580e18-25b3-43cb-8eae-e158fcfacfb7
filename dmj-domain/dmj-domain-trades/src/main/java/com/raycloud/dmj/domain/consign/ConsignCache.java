package com.raycloud.dmj.domain.consign;

import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/20 09:50
 * @Description
 */
@Setter
@Getter
public class ConsignCache extends Model{

    private static final long serialVersionUID = 3445857599612003105L;

    private Long id;

    private Long companyId;

    private Long sid;

    private String args;

    private Date created;

}

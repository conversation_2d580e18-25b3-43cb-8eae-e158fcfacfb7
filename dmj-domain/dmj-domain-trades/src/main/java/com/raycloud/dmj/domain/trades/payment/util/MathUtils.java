package com.raycloud.dmj.domain.trades.payment.util;

import com.raycloud.dmj.domain.Query;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.math.BigDecimal;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-03-10
 */
public  class MathUtils {

    private static final Logger logger = Logger.getLogger(MathUtils.class);

    public static boolean equals(BigDecimal a, BigDecimal b){
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        if (b == null) {
            b = BigDecimal.ZERO;
        }
        return a.compareTo(b) == 0;
    }

    public static boolean equals(String a, String b){
        return equals(toBigDecimal(a),toBigDecimal(b));
    }

    public static boolean lessThan(BigDecimal a, BigDecimal b){
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        if (b == null) {
            b = BigDecimal.ZERO;
        }
        return a.compareTo(b) < 0;
    }

    public static boolean greatThan(BigDecimal a, BigDecimal b){
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        if (b == null) {
            b = BigDecimal.ZERO;
        }
        return a.compareTo(b) > 0;
    }

    public static boolean equalsOrGreatThan(BigDecimal a, BigDecimal b){
        return !lessThan(a,b);
    }

    public static boolean equalsZero(BigDecimal a){
        return equals(a,BigDecimal.ZERO);
    }

    public static boolean equalsZero(Double a){
        return equals(toBigDecimal(a),BigDecimal.ZERO);
    }

    public static boolean equalsZero(String a){
        return equals(toBigDecimal(a),BigDecimal.ZERO);
    }


    public static boolean equalsOrLessThanZero(String a){
        return equals(toBigDecimal(a),BigDecimal.ZERO);
    }

    public static boolean equalsOrLessThanZero(BigDecimal a){
        return equals(a,BigDecimal.ZERO) || lessThan(a,BigDecimal.ZERO);
    }


    public static BigDecimal scaleUp(BigDecimal a){
        if (a == null) {
            return null;
        }
        //历史数据有3位的 不处理
        if (a.scale() > 6) {
            a= a.setScale(6,BigDecimal.ROUND_HALF_UP);
        }
        return a;
    }

    public static BigDecimal scaleUp(BigDecimal a,Integer scale){
        if (a == null) {
            return null;
        }
        if (scale == null) {
            //历史数据有3位的 不处理
            if (a.scale() > 3) {
                a= a.setScale(3,BigDecimal.ROUND_HALF_UP);
            }
        }else {
            if (a.scale() > scale) {
                a= a.setScale(scale,BigDecimal.ROUND_HALF_UP);
            }
        }
        return a;
    }

    public static Double scaleUp(Double a,int scale){
        if (a == null) {
            return 0.0;
        }
        BigDecimal decimal = new BigDecimal(a);
        decimal = scaleUp(decimal,scale);
        return decimal.doubleValue();
    }

    public static BigDecimal add(BigDecimal ... as){
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal a : as) {
            if (a != null) {
                sum= sum.add(a);
            }
        }
        return scaleUp(sum);
    }

    public static BigDecimal add(String ... as){
        BigDecimal sum = BigDecimal.ZERO;
        for (String a : as) {
            if (a != null) {
                sum = sum.add(toBigDecimal(a));
            }
        }
        return scaleUp(sum);
    }

    public static Double add(Double ... as){
        Double sum = 0.0;
        for (Double a : as) {
            if (a != null) {
                sum= sum + a;
            }
        }
        return sum;
    }

    public static BigDecimal subtract(BigDecimal tag, BigDecimal ... as){
        if (tag == null) {
            tag = BigDecimal.ZERO;
        }
        for (BigDecimal a : as) {
            if (a != null) {
                tag= tag.subtract(a);
            }
        }
        return scaleUp(tag);
    }

    public static BigDecimal subtract(String tag, String ... as){
        BigDecimal t = toBigDecimal(tag);
        for (String a : as) {
            if (a != null) {
                t= t.subtract(toBigDecimal(a));
            }
        }
        return scaleUp(t);
    }

    public static BigDecimal multiply(BigDecimal ... as){
        BigDecimal sum = BigDecimal.ONE;
        for (BigDecimal a : as) {
            if (a != null) {
                sum= sum.multiply(a);
            }
        }
        return scaleUp(sum);
    }

    public static BigDecimal multiply(String a,Integer b){
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal multiply = toBigDecimal(a).multiply(new BigDecimal(b));
        return scaleUp(multiply);
    }

    public static BigDecimal multiply(BigDecimal a,Integer b){
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal multiply = a.multiply(new BigDecimal(b));
        return scaleUp(multiply);
    }

    public static BigDecimal multiply(Double a,Integer b){
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal multiply = toBigDecimal(a).multiply(new BigDecimal(b));
        return scaleUp(multiply);
    }

    public static BigDecimal multiply(Integer ... as){
        BigDecimal sum = BigDecimal.ONE;
        for (Integer a : as) {
            if (a != null) {
                sum= sum.multiply(new BigDecimal(a));
            }
        }
        return scaleUp(sum);
    }

    public static BigDecimal divide(BigDecimal tag, BigDecimal ... as){
        return divide(tag,2,as);
    }

    public static BigDecimal divide(BigDecimal tag,int scale, BigDecimal ... as){
        if (tag == null) {
            tag = BigDecimal.ZERO;
        }
        for (BigDecimal a : as) {
            if (equalsZero(a)) {
                throw new RuntimeException("除数不能为0");
            }
            tag= tag.divide(a,scale,BigDecimal.ROUND_HALF_UP);
        }
        return scaleUp(tag,scale);
    }

    public static String toString(BigDecimal a){
        if (a == null) {
            return "0.00";
        }
        a =  scaleUp(a);
        return a.toPlainString();
    }

    public static String toString(BigDecimal a,int scale){
        if (a == null) {
            return "0.00";
        }
        a =  scaleUp(a,scale);
        return a.toPlainString();
    }


    public static String toString(Double a){
        if (a == null) {
            return "0.00";
        }
        return toString(toBigDecimal(a));
    }

    public static String toString(Double a,int scale){
        if (a == null) {
            return "0.00";
        }
        BigDecimal decimal = new BigDecimal(a);
        decimal = scaleUp(decimal,scale);
        return decimal.toPlainString();
    }

    public static double toDouble(BigDecimal a){
        if (a == null) {
            return 0.00d;
        }
        a =  scaleUp(a);
        return a.doubleValue();
    }

    public static double toDouble(String a){
        if (a == null) {
            return 0.00d;
        }
        return toDouble(toBigDecimal(a));
    }

    public static double toDouble(String a,int scale){
        if (a == null) {
            return 0.00d;
        }
        BigDecimal decimal = new BigDecimal(a);
        decimal = scaleUp(decimal,scale);
        return decimal.doubleValue();
    }

    public static BigDecimal toBigDecimal(String a){
        return toBigDecimal(a,null);
    }

    public static BigDecimal toBigDecimal(String a,String field){
        if (StringUtils.isBlank(a)) {
            return new BigDecimal("0.00");
        }
        BigDecimal b = null;
        try {
            b = new BigDecimal(a);
        }catch (Exception e){
            //更明确区分null和字符串"null"
            if (a.equalsIgnoreCase("null")) {
                a = "\""+a+"\"";
            }
            if (field != null) {
                throw new NumberFormatException(field + "非法:"+a);
            }else{
                throw new NumberFormatException("数值非法:"+a);
            }
        }
        b = scaleUp(b);
        return b;
    }

    public static BigDecimal toBigDecimal(Double a){
        if (a == null) {
            return new BigDecimal("0.00");
        }
        BigDecimal b = BigDecimal.valueOf(a);
        b = scaleUp(b);
        return b;
    }

    public static BigDecimal toBigDecimal(Integer a){
        if (a == null) {
            return new BigDecimal("0.00");
        }
        BigDecimal b = BigDecimal.valueOf(a);
        return b;
    }

    public static BigDecimal toBigDecimalWithOutHalfup(String a){
        if (StringUtils.isBlank(a)) {
            return new BigDecimal("0.00");
        }
        BigDecimal b = null;
        try {
            b = new BigDecimal(a);
        }catch (Exception e){
            //更明确区分null和字符串"null"
            if (a.equalsIgnoreCase("null")) {
                a = "\""+a+"\"";
            }
            throw new NumberFormatException("数值转换错误:"+a);
        }
        return b;
    }

    public static BigDecimal toBigDecimalWithOutHalfup(Double a){
        if (a == null) {
            return new BigDecimal("0.00");
        }
        String s = String.valueOf(a);
        return toBigDecimalWithOutHalfup(s);
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static String toScaleString(Long companyId,BigDecimal a){
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        a = scaleUp(companyId,a);
        return a.toPlainString();
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static String toScaleString(Long companyId,String a){
        if (a == null) {
            a = "0";
        }
        BigDecimal b = toBigDecimal(a);
        return toScaleString(companyId,b);
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static String toScaleString(Long companyId,Double a){
        if (a == null) {
            a = 0.0;
        }
        BigDecimal b = BigDecimal.valueOf(a);
        return toScaleString(companyId,b);
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static double toScaleDouble(Long companyId,BigDecimal a){
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        a = scaleUp(companyId,a);
        return a.doubleValue();
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static double toScaleDouble(Long companyId,Double a){
        if (a == null) {
            a = 0.0;
        }
        BigDecimal b = BigDecimal.valueOf(a);
        return toScaleDouble(companyId,b);
    }

    /**
     * 转换为按公司配置的精度
     * @param companyId
     * @param a
     * @return
     */
    public static double toScaleDouble(Long companyId,String a){
        if (a == null) {
            a = "0";
        }
        BigDecimal b = toBigDecimal(a);
        return toScaleDouble(companyId,b);
    }

    /**
     * 转换为按公司配置的精度
     * @param a
     * @param companyId
     * @return
     */
    public static BigDecimal scaleUp(Long companyId,BigDecimal a){
        Integer scale = null;
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        IPaymentSwitcher switcher = IPaymentSwitcher.getInstance();
        if (switcher == null) {
            new PaymentLogBuilder(companyId).append("未获取到IPaymentSwitcher实例 使用系统默认精度").printWarn(logger);
        }else {
            scale = switcher.getScale(companyId);
        }
        if (scale == null) {
            scale = 3;
        }
        return a.setScale(scale,BigDecimal.ROUND_HALF_UP);
    }

}

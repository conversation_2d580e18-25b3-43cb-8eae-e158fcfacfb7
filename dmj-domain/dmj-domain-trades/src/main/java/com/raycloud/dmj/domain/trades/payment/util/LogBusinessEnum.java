package com.raycloud.dmj.domain.trades.payment.util;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-08-16
 */
public enum LogBusinessEnum {

    TRADE_TEMPLATE_MATCH("快递匹配"),
    PAYMENT("金额业务"),
    QUERY("查询业务"),
    FX("分销业务"),
    POST_FEE("运费计算"),
    WEIGH("称重业务"),
    TRADE_FILL("TradeFill"),
    TRADE_FILTER("TradeFilter"),


    TRADE_IMPORT_CONVERT("订单同步:数据转换"),
    TRADE_IMPORT("订单同步"),
    TRADE_IMPORT_AFTER("订单同步:后置处理"),

    DECRYPT("订单解密"),

    CONSIGN("订单发货"),

    ;

    private String sign;

    LogBusinessEnum(String sign) {
        this.sign = sign;
    }

    public String getSign() {
        return sign;
    }
}

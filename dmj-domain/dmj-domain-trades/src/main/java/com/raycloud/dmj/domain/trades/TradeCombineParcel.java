package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.raycloud.dmj.domain.trades.vo.SubbagDetailVo;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

import static com.raycloud.dmj.domain.utils.CommonConstants.PLAT_FORM_TYPE_FXG;
import static com.raycloud.dmj.domain.utils.CommonConstants.PLAT_FORM_TYPE_QIMEN;

/**
 * 组包大包
 */
@Table(name = "trade_combine_parcel", routerKey = "tradeCombineParcelDbNo")
public class TradeCombineParcel extends Model{

    private static final long serialVersionUID = 2772258169115128998L;

    //待出库
    public static final int STATUS_TO_OUTBOUND = 1;
    //已出库
    public static final int STATUS_OUTBOUNDED = 2;
    //已取消
    public static final int STATUS_CANCELLED = 3;

    //未上传
    public static final int UPLOAD_STATUS_TO_UPLOAD = 1;
    //部分已上传
    public static final int UPLOAD_STATUS_PART_UPLOADED = 2;
    //全部已上传
    public static final int UPLOAD_STATUS_UPLOADED = 3;
    //已取消
    public static final int UPLOAD_STATUS_CANCELLED = 4;

    public static final String APPOINTMENT_BIGBAG="bigbag";
    public static final String APPOINTMENT_BATCH="batch";

    public enum ExtraField{
        UPLOADED_TRACKING_NO("uploadedTrackingNo"),
        BTAS_EXPRESS_PRODUCT("btasExpressProduct");
        ExtraField(String name){
            this.name = name;
        }
        private String name;
        public String getName(){
            return this.name;
        }
    }
    /**
     * 主键id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 店铺id
     */
    private Long taobaoId;

    /**
     * 平台来源
     */
    private String source;
    /**
     * 平台来源 中文名称
     */
    private String sourceName;

    /**
     * 平台揽货批次号
     */
    private String platformBatchNo;
    /**
     * 平台大包交接单id
     */
    private String platformOrderId;
    /**
     * 平台大包id
     */
    private String platformContentId;
    /**
     * 物流单号
     */
    private String trackingNo;

    /**
     * 发货仓库id
     */
    private Long consignWarehouseId;

    /**
     * 发货仓库名称
     */
    private String consignWarehouseName;

    /**
     * 中专仓库id
     */
    private String transferWarehouseId;

    /**
     * 中专仓库名称
     */
    private String transferWarehouseName;

    /**
     * 中专仓库地址
     */
    private String transferWarehouseAddress;

    /**
     * 小包数量
     */
    private Integer parcelNum;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 净重
     */
    private Double netWeight;

    /**
     * 体积
     */
    private Double volume;

    /**
     * 状态 1 待出库 2已出库 3已取消
     */
    private Integer status;

    /**
     * 上传状态 1未上传 2部分已上传 3全部已上传 4已取消
     */
    private Integer uploadStatus;

    /**
     * 启用状态 0 未启用 1启用
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * template_id
     */
    private Long templateId;

    /**
     * template_Name
     */
    private String templateName;

    /**
     * 收集类型 3 cainiao_pickup(菜鸟揽收)、2 self_post(自寄)、 4 self_send(自送)
     */
    private Integer gatherType;

    /**
     * 1-仓库地址
     */
    private Integer gatherAddressType;

    /**
     * 收集时间
     */
    private Date gatherTime;
    /**
     * 小包列表
     */
    private List<TradeParcel> parcels;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 平台状态
     */
    private String platformStatus;


    /**
     * 最大组包数量
     */
    private Integer maxParcelNum;
    /**
     * 0线上组包 1 线下组包
     */
    private Integer combineParcelType;

    /**
     * BTAS大包质检配置: 快递产品
     * 0：顺丰特快
     * 1：顺丰标快
     */
    private Integer btasExpressProduct;

    /**
     * 扩展字段， 线上组包，
     */
    private String extraFields;

    /**
     * 0：不能上传，1：上传平台，2：继续上传平台，3：重新上传平台
     */
    private Integer opType;

    /**
     * 预约交货方式（0：大包预约，1：批次预约)
     */
    private Integer appointmentType;

    /**
     * 大包最大数量
     */
    private Integer maxCombineParcelNum;
    /**
     * 加包数量
     */
    private Integer addCombineParcelNum;

    /**
     * 速卖通加包详情列表
     */
    private List<SubbagDetailVo> subbagDetailList;

    /**
     * 子包订单id
     */
    private String subbagId;

    /***
     * 集货点编码
     */
    private String pickUpCode;

    /**
     * 订单分组key
     */
    private String tradeKey;

    /**
     * tiktok组包平台返回url
     */
    private String platformUrl;

    public String getTradeKey() {
        return tradeKey;
    }

    public void setTradeKey(String tradeKey) {
        this.tradeKey = tradeKey;
    }

    public String getPlatformUrl() {
        return platformUrl;
    }

    public void setPlatformUrl(String platformUrl) {
        this.platformUrl = platformUrl;
    }

    public String getPickUpCode() {
        return pickUpCode;
    }

    public void setPickUpCode(String pickUpCode) {
        this.pickUpCode = pickUpCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPlatformBatchNo() {
        return platformBatchNo;
    }

    public void setPlatformBatchNo(String platformBatchNo) {
        this.platformBatchNo = platformBatchNo;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public Long getConsignWarehouseId() {
        return consignWarehouseId;
    }

    public void setConsignWarehouseId(Long consignWarehouseId) {
        this.consignWarehouseId = consignWarehouseId;
    }

    public String getConsignWarehouseName() {
        return consignWarehouseName;
    }

    public void setConsignWarehouseName(String consignWarehouseName) {
        this.consignWarehouseName = consignWarehouseName;
    }

    public String getTransferWarehouseId() {
        return transferWarehouseId;
    }

    public void setTransferWarehouseId(String transferWarehouseId) {
        this.transferWarehouseId = transferWarehouseId;
    }

    public String getTransferWarehouseName() {
        return transferWarehouseName;
    }

    public void setTransferWarehouseName(String transferWarehouseName) {
        this.transferWarehouseName = transferWarehouseName;
    }

    public Integer getParcelNum() {
        return parcelNum;
    }

    public void setParcelNum(Integer parcelNum) {
        this.parcelNum = parcelNum;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public List<TradeParcel> getParcels() {
        return parcels;
    }

    public void setParcels(List<TradeParcel> parcels) {
        this.parcels = parcels;
    }

    public Integer getGatherType() {
        return gatherType;
    }

    public void setGatherType(Integer gatherType) {
        this.gatherType = gatherType;
    }

    public Integer getGatherAddressType() {
        return gatherAddressType;
    }

    public void setGatherAddressType(Integer gatherAddressType) {
        this.gatherAddressType = gatherAddressType;
    }

    public Date getGatherTime() {
        return gatherTime;
    }

    public void setGatherTime(Date gatherTime) {
        this.gatherTime = gatherTime;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getTransferWarehouseAddress() {
        return transferWarehouseAddress;
    }

    public void setTransferWarehouseAddress(String transferWarehouseAddress) {
        this.transferWarehouseAddress = transferWarehouseAddress;
    }

    public Integer getOpType() {
        return opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }
    public enum LazadaEnumGatherType {
        //lazada 自送 和 自送到集货点
        CAINIAO_PICKUP(3, "cainiao_pickup", "菜鸟上门揽收",1),

        SELF_SEND(7, "self_send", "自行寄送", 7),
        PICKUP_COLLECTION(8, "pickup_collection", "自送到集货点", 8),
        LAZADA_SELF_POST(9, "self_post", "自行配送",9);


        private Integer type;
        private String code;
        private String name;
        private Integer gatherType;

        LazadaEnumGatherType(Integer type, String code, String name, Integer gatherType) {
            this.type = type;
            this.code = code;
            this.name = name;
            this.gatherType = gatherType;
        }
        public static LazadaEnumGatherType getEnumLazadaGatherType(Integer type) {
            for (LazadaEnumGatherType value : LazadaEnumGatherType.values()) {
                if (value.gatherType.equals(type)) {
                    return value;
                }
            }
            return CAINIAO_PICKUP;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getGatherType() {
            return gatherType;
        }

        public void setGatherType(Integer gatherType) {
            this.gatherType = gatherType;
        }
    }
    public enum EnumGatherType {
        PICK_UP(1, "pickup", "上门揽收",1),
        DROP_OFF(2, "dropoff", "快递寄送",3),
        CAINIAO_PICKUP(3, "cainiao_pickup", "菜鸟上门揽收",1),
        SELF_POST(4, "self_post", "自行配送",2),

        //btas无需物流，btas邮寄发货
        DUMMY(5,"dummy","无需物流",0),
        YOUJI(6,"youji","邮寄发货",0),

        //lazada 自送 和 自送到集货点
        SELF_SEND(7, "self_send", "自行寄送", 7),
        PICKUP_COLLECTION(8, "pickup_collection", "自送到集货点", 8),
        LAZADA_SELF_POST(9, "self_post", "自行配送",9);


        private Integer type;
        private String code;
        private String name;
        private Integer gatherType;

        EnumGatherType(Integer type, String code, String name, Integer gatherType) {
            this.type = type;
            this.code = code;
            this.name = name;
            this.gatherType = gatherType;
        }

        public static String getCodeByType(Integer type) {
            for (EnumGatherType value : EnumGatherType.values()) {
                if(value.type.equals(type)) {
                    return value.code;
                }
            }
            return null;
        }

        public static EnumGatherType getEnumGatherType(Integer type) {
            for (EnumGatherType value : EnumGatherType.values()) {
                if (value.type.equals(type)) {
                    return value;
                }
            }
            return null;
        }
        public static EnumGatherType getEnumLazadaGatherType(Integer type) {
            for (EnumGatherType value : EnumGatherType.values()) {
                if (value.gatherType.equals(type)) {
                    return value;
                }
            }
            return CAINIAO_PICKUP;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getGatherType() {
            return gatherType;
        }

        public void setGatherType(Integer gatherType) {
            this.gatherType = gatherType;
        }
    }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    public String getPlatformContentId() {
        return platformContentId;
    }

    public void setPlatformContentId(String platformContentId) {
        this.platformContentId = platformContentId;
    }

    public String getPlatformStatus() {
        return platformStatus;
    }

    public void setPlatformStatus(String platformStatus) {
        this.platformStatus = platformStatus;
    }

    public Integer getMaxParcelNum() {
        return maxParcelNum;
    }

    public void setMaxParcelNum(Integer maxParcelNum) {
        this.maxParcelNum = maxParcelNum;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getCombineParcelType() {
        return combineParcelType;
    }

    public void setCombineParcelType(Integer combineParcelType) {
        this.combineParcelType = combineParcelType;
    }

    public Integer getBtasExpressProduct() {
        return btasExpressProduct;
    }

    public void setBtasExpressProduct(Integer btasExpressProduct) {
        this.btasExpressProduct = btasExpressProduct;
    }

    public String getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(String extraFields) {
        this.extraFields = extraFields;
    }

    public void putExtraFields(ExtraField extraField ,String uploadedTrackingNo){
        if (Strings.isNullOrEmpty(this.getExtraFields())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(extraField.name,uploadedTrackingNo);
            this.setExtraFields(jsonObject.toJSONString());
        }else {
            JSONObject jsonObject = JSON.parseObject(this.getExtraFields());
            jsonObject.remove(extraField.name);
            jsonObject.put(extraField.name,uploadedTrackingNo);
            this.setExtraFields(jsonObject.toJSONString());
        }
    }

    public String pullExtraFields(ExtraField extraField){
        if (Strings.isNullOrEmpty(this.getExtraFields())) {
            return null;
        }else {
            JSONObject jsonObject = JSON.parseObject(this.getExtraFields());
            return jsonObject.getString(extraField.name);
        }
    }

    public void parseBtasFromExtraFields(){
        if (StringUtils.isNotBlank(this.getExtraFields())) {
            JSONObject jsonObject = JSON.parseObject(this.getExtraFields());
            this.btasExpressProduct = jsonObject.getInteger(ExtraField.BTAS_EXPRESS_PRODUCT.getName());
        }
    }

    /**
     * 大包是否为BTAS质检组包
     * @return
     */
    public boolean isBtasParcel(){
        return PLAT_FORM_TYPE_FXG.equals(this.source) || PLAT_FORM_TYPE_QIMEN.equals(this.source);
    }

    public boolean isWmsBtasParcel(){
        return PLAT_FORM_TYPE_QIMEN.equals(this.source);
    }

    public Integer getAppointmentType() {
        return appointmentType;
    }

    public void setAppointmentType(Integer appointmentType) {
        this.appointmentType = appointmentType;
    }

    public Integer getMaxCombineParcelNum() {
        return maxCombineParcelNum;
    }

    public void setMaxCombineParcelNum(Integer maxCombineParcelNum) {
        this.maxCombineParcelNum = maxCombineParcelNum;
    }

    public Integer getAddCombineParcelNum() {
        return addCombineParcelNum;
    }

    public void setAddCombineParcelNum(Integer addCombineParcelNum) {
        this.addCombineParcelNum = addCombineParcelNum;
    }

    public List<SubbagDetailVo> getSubbagDetailList() {
        return subbagDetailList;
    }

    public void setSubbagDetailList(List<SubbagDetailVo> subbagDetailList) {
        this.subbagDetailList = subbagDetailList;
    }

    public String getSubbagId() {
        return subbagId;
    }

    public void setSubbagId(String subbagId) {
        this.subbagId = subbagId;
    }
}

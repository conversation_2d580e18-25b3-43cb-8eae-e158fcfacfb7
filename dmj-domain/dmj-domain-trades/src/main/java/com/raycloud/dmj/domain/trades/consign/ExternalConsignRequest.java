package com.raycloud.dmj.domain.trades.consign;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 供开放平台调用
 * https://gykj.yuque.com/docs/share/e0a64d68-ad8e-4da7-8576-fd7e7be71a3c?#
 * 如果没有填写运单号，那么就是系统内发货上传
 * 如果填写了运单号，那么认为是系统外仅上传
 */
public class ExternalConsignRequest implements Serializable {
    private static final long serialVersionUID = 5898810223938711972L;

    /**
     * 系统订单号（如果同时传sids与tid，优先sids）
     */
    private Long[] sids;
    /**
     * 平台订单号
     */
    private String tid;
    /**
     * 发货类型
     */
    private String consignType;
    /**
     * erp内部的快递公司编码
     */
    private String expressCode;
    /**
     * 运单号
     */
    private String outSid;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 订单来自哪个平台，例如 tb(淘宝或者天猫平台),jd(京东平台),sys(系统手工订单)
     */
    private String source;

    /**
     * 1.只走系统发货不上传平台； 2 就是默认的发货流程
     */
    private String operateType;

    public Long[] getSids() {
        return sids;
    }

    public void setSids(Long[] sids) {
        this.sids = sids;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getConsignType() {
        return consignType;
    }

    public void setConsignType(String consignType) {
        this.consignType = consignType;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    /**
     * 如果参数中带有快递信息则认为是直接上传
     */
    public boolean isOnlyUpload(){
        return StringUtils.isNotEmpty(expressCode) && StringUtils.isNotEmpty(outSid);
    }
}

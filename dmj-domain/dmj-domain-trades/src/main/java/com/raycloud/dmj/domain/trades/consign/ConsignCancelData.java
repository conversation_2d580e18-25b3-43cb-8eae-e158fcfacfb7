package com.raycloud.dmj.domain.trades.consign;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeResultData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther mengfanguang
 * @Date 2023/7/10
 */
public class ConsignCancelData extends TradeResultData {
    /**
     * 库存未消费  （存在 stock_order_record enable_status = 1 的情况）
     */
    public Map<Long, String> notConsumeErrors = new HashMap<>();
    /**
     * 交易关闭的单
     */
    public List<Trade> closeTrades=new ArrayList<>();

    public boolean closeTradeCancelConsign;

    public List<Long> excuteCloseTradeCancelConsignSidList;
}
package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.trades.ExtJson;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by windy26205 on 19/10/31.
 */
public class ExtJsonUtil {

    private static Map<Class,ExtJsonConverInfo> extJsonConverInfoMap = new ConcurrentHashMap<>();



    public static void exactExtJson(Object object) {
        try {
            Class classType = object.getClass();
            ExtJsonUtil.ExtJsonConverInfo extJsonConverInfo = ExtJsonUtil.getkeyProMap(classType);
            if (extJsonConverInfo != null) {
                Field field = classType.getField(extJsonConverInfo.getJsonPro());
                if (field != null) {
                    field.setAccessible(true);
                    String extJsonStr = (String) field.get(object);
                    if (extJsonStr != null) {
                        Map<String, Object> extJsonMap = (Map) JSONObject.parse(extJsonStr);
                        Map<String, String> keyProMap = extJsonConverInfo.getKeyProMap();
                        for (Map.Entry<String, String> entry : keyProMap.entrySet()) {
                            String key = entry.getKey();
                            String fieldName = entry.getValue();
                            Object fieldValue = extJsonMap.get(key);
                            if (fieldValue != null) {
                                Field setField = classType.getField(fieldName);
                                if (setField != null) {
                                    setField.setAccessible(true);
                                    setField.set(object, fieldValue);
                                }
                            }
                        }
                    }
                }
            }
        } catch (NoSuchFieldException e) {
            //
        } catch (IllegalAccessException e) {
            //
        }
    }


    /**
     *
     * 得到Class类的extJson字段和其他字段的转化关系
     * @param classType
     * @return
     */
    public static ExtJsonConverInfo getkeyProMap(Class classType) {
        ExtJsonConverInfo extJsonConverInfo = extJsonConverInfoMap.get(classType);
        if(extJsonConverInfo == null) {
            Map<String,String> keyProsMap = new HashMap<>();
            Field[] fields = classType.getFields();
            String jsonPro = null;
            for(int i=0;i<fields.length;i++) {
                Field field = fields[i];
                ExtJson[] extJsonAnnations = field.getAnnotationsByType(ExtJson.class);
                if(extJsonAnnations != null && extJsonAnnations.length > 0){
                    ExtJson extJsonAnnation = extJsonAnnations[0];
                    String key = extJsonAnnation.proName();
                    boolean isExtJsonPro = extJsonAnnation.isExtJsonPro();
                    if(isExtJsonPro){
                        //存在多个isExtJsonPro暂时以后面的为准
                        Class jsonProType = field.getType();
                        if(jsonProType.equals(String.class)) {
                            jsonPro = field.getName();
                        }
                    }else {
                        keyProsMap.put(key,field.getName());
                    }
                }
            }
            if(jsonPro != null && keyProsMap.size() > 0){
                extJsonConverInfo = new ExtJsonConverInfo(jsonPro,keyProsMap);
                extJsonConverInfoMap.put(classType,extJsonConverInfo);
            }
        }
        return extJsonConverInfo;
    }



    public static class ExtJsonConverInfo {

        String jsonPro;

        Map<String,String> keyProMap;

        public String getJsonPro() {
            return jsonPro;
        }

        public void setJsonPro(String jsonPro) {
            this.jsonPro = jsonPro;
        }

        public Map<String, String> getKeyProMap() {
            return keyProMap;
        }

        public void setKeyProMap(Map<String, String> keyProMap) {
            this.keyProMap = keyProMap;
        }

        public ExtJsonConverInfo(String jsonPro, Map<String, String> keyProMap) {
            this.jsonPro = jsonPro;
            this.keyProMap = keyProMap;
        }
    }
}

package com.raycloud.dmj.domain.trades;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台交易状态枚举以及与系统交易状态的映射关系类
 * <AUTHOR>
 * @since 16/3/9
 */
public final class TradeStatus {

	//================================ 淘宝平台交易状态枚举 ================================

	//没有创建支付宝交易
	public static final String TB_TRADE_NO_CREATE_PAY = "TRADE_NO_CREATE_PAY";
	//国际信用卡支付付款确认中
	public static final String TB_PAY_PENDING = "PAY_PENDING";
	//0元购合约中
	public static final String TB_WAIT_PRE_AUTH_CONFIRM = "WAIT_PRE_AUTH_CONFIRM";
	//等待买家付款
	public static final String TB_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
	//等待卖家发货,即:买家已付款
	public static final String TB_WAIT_SELLER_SEND_GOODS = "WAIT_SELLER_SEND_GOODS";
	//卖家部分发货
	public static final String TB_SELLER_CONSIGNED_PART = "SELLER_CONSIGNED_PART";
	//等待买家确认收货,即:卖家已发货
	public static final String TB_WAIT_BUYER_CONFIRM_GOODS = "WAIT_BUYER_CONFIRM_GOODS";
	//买家已签收,货到付款专用
	public static final String TB_TRADE_BUYER_SIGNED = "TRADE_BUYER_SIGNED";
	//付款以后用户退款成功，交易自动关闭
	public static final String TB_TRADE_CLOSED = "TRADE_CLOSED";
	//付款以前，卖家或买家主动关闭交易
	public static final String TB_TRADE_CLOSED_BY_TAOBAO = "TRADE_CLOSED_BY_TAOBAO";
	//交易完成
	public static final String TB_TRADE_FINISHED = "TRADE_FINISHED";
	//拼团中订单
	public static final String PAID_FORBID_CONSIGN = "PAID_FORBID_CONSIGN";

	public static int getStatusWeight(String status){
		TradeStatus object = statusMap.get(status);
		if (object != null) {
			return object.getWeight();
		}
		return 10000;
	}

	/**
	 * 根据平台状态获取对应的系统状态
	 * @param status 平台状态字符串
	 * @param sysStatus 默认的系统状态,当没有对应的系统状态时,返回该值
	 * @return 系统状态字符串
	 */
	public static String getSysStatus(String status, String sysStatus) {
		String result = sysStatus;
		TradeStatus object = statusMap.get(status);
		if (object != null) {
			result = object.getSysStatus();
		}
		return result;
	}

	/**
	 * 平台订单状态与系统订单状态的映射
	 */
	private static final Map<String, TradeStatus> statusMap = new HashMap<String, TradeStatus>();

	/**
	 * 权重值请按规律设置
	 */
	static {
		handleTbTradeStatus();
	}

	static void handleTbTradeStatus(){
		statusMap.put(TB_TRADE_NO_CREATE_PAY, new TradeStatus(TB_TRADE_NO_CREATE_PAY, Trade.SYS_STATUS_WAIT_BUYER_PAY, 1));
		statusMap.put(TB_WAIT_BUYER_PAY, new TradeStatus(TB_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_BUYER_PAY, 3));
		statusMap.put(TB_PAY_PENDING, new TradeStatus(TB_PAY_PENDING, Trade.SYS_STATUS_WAIT_BUYER_PAY, 5));
		statusMap.put(TB_WAIT_PRE_AUTH_CONFIRM, new TradeStatus(TB_WAIT_PRE_AUTH_CONFIRM, Trade.SYS_STATUS_WAIT_BUYER_PAY, 7));
		statusMap.put(TB_WAIT_SELLER_SEND_GOODS, new TradeStatus(TB_WAIT_SELLER_SEND_GOODS, Trade.SYS_STATUS_WAIT_AUDIT, 101));
		statusMap.put(PAID_FORBID_CONSIGN, new TradeStatus(PAID_FORBID_CONSIGN, Trade.SYS_STATUS_WAIT_AUDIT, 101));
		statusMap.put(TB_SELLER_CONSIGNED_PART, new TradeStatus(TB_SELLER_CONSIGNED_PART, Trade.SYS_STATUS_WAIT_AUDIT, 103));
		statusMap.put(TB_WAIT_BUYER_CONFIRM_GOODS, new TradeStatus(TB_WAIT_BUYER_CONFIRM_GOODS, Trade.SYS_STATUS_SELLER_SEND_GOODS , 201));
		//已完成,已关闭
		statusMap.put(TB_TRADE_BUYER_SIGNED, new TradeStatus(TB_TRADE_BUYER_SIGNED, Trade.SYS_STATUS_FINISHED, 401));
		statusMap.put(TB_TRADE_FINISHED, new TradeStatus(TB_TRADE_FINISHED, Trade.SYS_STATUS_FINISHED , 401));
		statusMap.put(TB_TRADE_CLOSED_BY_TAOBAO, new TradeStatus(TB_TRADE_CLOSED_BY_TAOBAO, Trade.SYS_STATUS_CLOSED, 501));
		statusMap.put(TB_TRADE_CLOSED, new TradeStatus(TB_TRADE_CLOSED, Trade.SYS_STATUS_CLOSED, 501));
	}


	public static TradeStatus  getTradeStatus(String status){
		return statusMap.get(status);
	}
	/**
	 * 加载其他平台的status映射数据
	 * @param map
	 */
	public static void loadPlatformStatus(Map<String, TradeStatus> map){
		if(null == map || map.size() == 0) {
			return;
		}
		statusMap.putAll(map);
	}

	/**
	 * 平台订单状态
	 */
	private String status;
	/**
	 * 平台状态对应的系统状态
	 */
	private String sysStatus;
	/**
	 * 平台订单状态权重,用于同步订单时判断已存在的订单是否需要更新,同步后的权重大于同步前的权重需要更新更新订单
	 */
	private Integer weight;

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSysStatus() {
		return sysStatus;
	}

	public void setSysStatus(String sysStatus) {
		this.sysStatus = sysStatus;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public TradeStatus(String status, String sysStatus, Integer weight){
		this.status = status;
		this.sysStatus = sysStatus;
		this.weight = weight;
	}


	public static List<String> getNotConsignStatusList() {
		List<String> statusList = new ArrayList<>();
		if(statusMap != null) {
			for(Map.Entry<String,TradeStatus> entry : statusMap.entrySet()) {
				TradeStatus tradeStatus = entry.getValue();
				if(tradeStatus.getSysStatus() != null && (tradeStatus.getSysStatus().equals(Trade.SYS_STATUS_SELLER_SEND_GOODS) || tradeStatus.getSysStatus().equals(Trade.SYS_STATUS_FINISHED) || tradeStatus.getSysStatus().equals(Trade.SYS_STATUS_CLOSED))) {
					//已发货或者交易成功或者交易失败
				} else {
					statusList.add(entry.getKey());
				}
			}
		}
		return statusList;
	}

	@Override
	public String toString() {
		return status + "->" + sysStatus;
	}
}

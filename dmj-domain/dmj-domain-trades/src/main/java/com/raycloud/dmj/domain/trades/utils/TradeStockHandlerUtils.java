package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.except.enums.ExceptEnum;

import java.util.List;
import java.util.Objects;


/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/12/15 11:56 上午
 * @Description:
 */
public class TradeStockHandlerUtils {

    public static boolean resetTradeStockStatus(Staff staff, final Trade trade, final List<Order> orders, boolean handleAuditActiveStockRecord,
                                                Integer openScalpNotApplyStock, Integer openRefundNotApplyStock, Integer autoUnattainableNotApplyStock, Integer splitAutoAudit) {
        int[] a = new int[]{0, 0, 0, 0, 0, 0};
        int insufficientNum = 0;
        int validItemNum = 0;
        for (Order order : orders) {
            if (openScalpNotApplyStock == 1 && order.getScalping() == null) {
                order.setScalping(trade.getScalping());
            }
            UnattainableUilts.setOrderAutoUnattainable(staff,trade, order);
            insufficientNum += _handleStockStatus(staff, order, a, handleAuditActiveStockRecord, openScalpNotApplyStock, openRefundNotApplyStock, autoUnattainableNotApplyStock, splitAutoAudit);
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                validItemNum += order.getNum();
            }
           // OrderExceptUtils.calculateUnallocatedOrInsufficient(staff,order);
        }
        trade.setValidItemNum(validItemNum);
        trade.setInsufficientNum(insufficientNum);
        trade.setInsufficientRate(validItemNum > 0 ? Double.valueOf(String.format("%.2f", insufficientNum * 100.0D / validItemNum)) : 0D);
        if (trade.getOldStockStatus() == null) {
            trade.setOldStockStatus(trade.getStockStatus());
        }
       // trade.setStockStatus(_getStockStatus(a, false));
        TradeExceptUtils.setStockStatus(staff,trade,_getStockStatus(a, false));
        return a[0] > 0;
    }


    private static int _handleStockStatus(Staff staff, Order order, int[] a, boolean handleAuditActiveStockRecord, Integer openScalpNotApplyStock, Integer openRefundNotApplyStock, Integer autoUnattainableNotApplyStock, Integer splitAutoAudit) {
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) || (order.getEnableStatus() != null && order.getEnableStatus() == 0)) {
            return 0;
        }
        //如果是后置锁定，且商品已匹配，且订单状态为待审核/待财神
        if (order.getKeepInsufficientStockStatus()) {
            if (Objects.equals(splitAutoAudit, 0)) {
                order.setStockNum(0);
                OrderExceptUtils.setStockStatus(staff,order,getStockStatusStr(order.getNum(), order.getStockNum()));
            } else {
                OrderExceptUtils.setStockStatus(staff,order,getStockStatusStr(order.getNum(), order.getStockNum()));
            }
        } else if (handleAuditActiveStockRecord && staff.openAuditActiveStockRecord()
                && !Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus())
                && !OrderExceptUtils.isContainsExcept(staff,order,ExceptEnum.UNALLOCATED)
                && (Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(order.getSysStatus()))) {
            // 默认0  走原来的逻辑
            if (order.getPreSellLockStock() == 0) {
                order.setStockNum(order.getNum());
                OrderExceptUtils.setStockStatus(staff,order,getStockStatusStr(order.getNum(), order.getStockNum()));
            }
        } else if (order.getStockNum() != null && Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
            OrderExceptUtils.setStockStatus(staff,order,getStockStatusStr(order.getNum(), order.getStockNum()));
        } else if (order.getType() != null && (order.getType() == Order.TypeOfGroupOrder && order.getType() == Order.TypeOfProcessOrder) && order.getCombineId() > 0) {
            order.setStockNum(order.getNum());
            OrderExceptUtils.setStockStatus(staff,order,getStockStatusStr(order.getNum(), order.getStockNum()));
        }
        TradeStockApplyUtils.needNotApplyStock(staff,order, openScalpNotApplyStock, openRefundNotApplyStock, autoUnattainableNotApplyStock);

        if (OrderExceptUtils.isContainsExcept(staff,order, ExceptEnum.RELATION_CHANGED)) {//商品对应关系改动, 这个需要优先计算
            a[4]++;
            if (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {//缺货
                a[2]++;
            }
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        } else if (Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus())) {
            a[0]++;
            return order.getNum();
        } else if (Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
            a[1]++;
            if (!isEqualStockNum(order)) return order.getDiffStockNum();
        } else if (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
            if (order.getInsufficientCanceled() != null && order.getInsufficientCanceled() - 1 == 0) {//如果取消了缺货异常，算作正常
                a[1]++;
            } else {
                a[2]++;
            }
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        } else if (OrderExceptUtils.isContainsExcept(staff,order,ExceptEnum.UNALLOCATED)) {
            a[3]++;
            return order.getNum();
        } else {
            a[5]++;
            return order.getStockNum() != null ? order.getNum() - order.getStockNum() : order.getNum();
        }
        return 0;
    }


    public static String getStockStatusStr(long stockNum, long applyNum) {
        return applyNum - stockNum == 0 ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_INSUFFICIENT;
    }

    private static boolean isEqualStockNum(Order order) {
        if (order.getStockNum() == null || order.getDiffStockNum() == null) return true;
        int diffStockNum = order.getNum() - order.getStockNum();
        if (diffStockNum == order.getDiffStockNum()) {
            return true;
        }
        return false;
    }

    private static String _getStockStatus(int[] a, boolean needEmptyOrExcep) {
        if (needEmptyOrExcep) {
            if (a[0] > 0) {
                return Trade.STOCK_STATUS_EMPTY;
            } else if (a[5] > 0) {
                return Trade.STOCK_STATUS_EXCEP;
            }
        }
        if (a[4] > 0) {
            return Trade.STOCK_STATUS_RELATION_MODIFIED;
        } else if (a[3] > 0) {
            return Trade.STOCK_STATUS_UNALLOCATED;
        } else if (a[2] > 0) {
            return Trade.STOCK_STATUS_INSUFFICIENT;
        } else if (a[0] > 0 || a[5] > 0) {
            return Trade.STOCK_STATUS_EXCEP;
        } else {
            return Trade.STOCK_STATUS_NORMAL;
        }
    }
}

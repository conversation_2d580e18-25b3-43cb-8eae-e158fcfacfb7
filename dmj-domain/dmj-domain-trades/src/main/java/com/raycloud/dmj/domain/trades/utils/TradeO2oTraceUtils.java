package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.TradeLogisticsStatusEnum;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


public class TradeO2oTraceUtils {

    public static boolean isTradeO2oStatusChange(Trade trade) {
        if (Objects.isNull(trade.getTradeO2o()) || !PlatformUtils.isTradeO2o(trade)) {
            return false;
        }

        if (Objects.isNull(trade.getOriginTradeO2o())) {
            return false;
        }

        return !Objects.equals(trade.getOriginTradeO2o().getSysStatus(), trade.getTradeO2o().getSysStatus());
    }

    public static String builderTradeO2oStatusChangeContent(Trade trade) {
        String content = OpEnum.TRADE_O2O_STATUS_CHANGE.getName() + ": ";
        if (Objects.nonNull(trade.getOriginTradeO2o())) {
            content = content + TradeLogisticsStatusEnum.getNameByCode(trade.getOriginTradeO2o().getSysStatus());
        }
        if (Objects.nonNull(trade.getTradeO2o())) {
            content = content + "->" + TradeLogisticsStatusEnum.getNameByCode(trade.getTradeO2o().getSysStatus());
        }
        return joinTradeO2oDistribute(content, trade);
    }

    public static boolean isTradeO2oException(Trade trade) {
        if (Objects.isNull(trade.getTradeO2o()) || !PlatformUtils.isTradeO2o(trade)) {
            return false;
        }
        if (Objects.isNull(trade.getTradeO2o().getDistributeExceptionCode())) {
            return false;
        }
        return Objects.isNull(trade.getOriginTradeO2o()) || !Objects.equals(trade.getOriginTradeO2o().getDistributeExceptionCode(), trade.getTradeO2o().getDistributeExceptionCode());
    }

    public static String builderTradeO2oExceptionContent(Trade trade) {
        String content = OpEnum.TRADE_O2O_EXCEPTION.getName() + ": " + trade.getTradeO2o().getDistributeExceptionDesc();
        return joinTradeO2oDistribute(content, trade);
    }

    public static String builderTradeO2oAcceptContent(Trade trade, String msg) {
        String content = OpEnum.TRADE_O2O_ACCEPT.getName() + ": " + msg;
        return joinTradeO2oDistribute(content, trade);
    }

    public static String builderTradeO2oPickContent(Trade trade, String msg) {
        String content = OpEnum.TRADE_O2O_PICK.getName() + ": " + msg;
        return joinTradeO2oDistribute(content, trade);
    }

    public static String joinTradeO2oDistribute(String content, Trade trade) {
        if (Objects.isNull(trade.getTradeO2o())) {
            return content;
        }

        if (StringUtils.isNotBlank(trade.getTradeO2o().getDistributeName())) {
            content = content + " " + "骑手姓名：" + trade.getTradeO2o().getDistributeName();
        }
        if (StringUtils.isNotBlank(trade.getTradeO2o().getDistributeMobile())) {
            content = content + " " + "骑手电话：" + trade.getTradeO2o().getDistributeMobile();
        }
        return content;
    }

    public static void main(String[] args) {
        Trade trade = builderTrade();
        System.out.println(builderTradeO2oAcceptContent(trade, "成功"));
        System.out.println(builderTradeO2oPickContent(trade, "成功"));
//        trade.getTradeO2o().setDistributeName("配送牛人");
        trade.getTradeO2o().setDistributeMobile("13812345678");
        System.out.println(builderTradeO2oStatusChangeContent(trade));
        System.out.println(builderTradeO2oExceptionContent(trade));
    }

    public static Trade builderTrade() {
        Trade trade = new Trade();
        TradeO2o tradeO2o = new TradeO2o();
        tradeO2o.setSysStatus(TradeLogisticsStatusEnum.RIDER_PICKED_UP.getCode());
        tradeO2o.setDistributeExceptionCode("1");
        tradeO2o.setDistributeExceptionDesc("发配送超过30分钟没有骑手抢单");
        trade.setTradeO2o(tradeO2o);
        TradeExt tradeExt = new TradeExt();
        Map<String, Object> extraFieldsMap = new HashMap<>();
        extraFieldsMap.put("o2oTrade", 1);
        tradeExt.setExtraFields(JSON.toJSONString(extraFieldsMap));
        trade.setTradeExt(tradeExt);
        TradeO2o originTradeO2o = new TradeO2o();
        originTradeO2o.setSysStatus(TradeLogisticsStatusEnum.WAIT_MERCHANT_ACCEPTANCE.getCode());
        trade.setOriginTradeO2o(originTradeO2o);
        return trade;
    }
}

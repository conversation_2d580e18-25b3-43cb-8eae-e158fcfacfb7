package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 对应淘宝平台 仓配建议信息
 */
public class TbRecommendation implements Serializable {

    private static final long serialVersionUID = 1680744578702618469L;

    /**
     * 子单物流发货信息
     */
    private List<LogisticsInfo> logisticsInfos;

    /**
     * 物流服务承诺
     */
    private LogisticsAgreement logisticsAgreement;

    /**
     * 收货省市区街道
     * 为了比较是否变更
     */
    private String receiveProvinceCityDistrictTown;

    public static class LogisticsInfo implements Serializable {
        private static final long serialVersionUID = -1134763314894966605L;

        /**
         * 交易主单号
         */
        private Long tradeId;

        /**
         * 交易子单号
         */
        private Long subTradeId;

        /**
         * 建议仓类型
         * ● 0：子单无仓建议；
         * ● 1：子单有仓建议，erp可按需参考，默认可按照无仓建议执行；
         * ● 2：子单有仓建议，erp需按建议择仓。
         */
        private String bizSdType;

        /**
         * 订单推荐配送类型
         * 0：子单无配建议；ERP按照自己的逻辑进行择配
         * 1：子单有推荐配list，erp可按需参考
         * 2：子单有推荐配list，erp必须在推荐配list中选择配品牌
         * 3：子单有禁用配list，erp需要过滤配品牌
         */
        private String bizDeliveryCode;

        /**
         * 订单推荐配送类型
         * 0：子单无配建议；ERP按照自己的逻辑进行择配
         * 1：子单有推荐配list，erp可按需参考
         * 2：子单有推荐配list，erp必须在推荐配list中选择配品牌
         * 3：子单有禁用配list，erp需要过滤配品牌
         */
        private Long bizDeliveryType;

        /**
         * 发货仓erp编码
         */
        private String bizStoreCode;

        /**
         * 翱象决策的发货地
         * 国家省市区街道
         */
        private String sendCountry;
        private String sendState;
        private String sendCity;
        private String sendDistrict;
        private String sendTown;

        /**
         * 翱翔决策的发货地最小地址编码（开放平台地址库）
         * taobao.areas.get
         */
        private String sendDivisionCode;

        /**
         * 快递黑白名单（列表）
         */
        private String blackDeliveryCps;
        private String whiteDeliveryCps;

        /**
         * 未使用配建议报错
         */
        private String unusedDeliveryErrorMsg;

        /**
         * 未使用仓建议报错
         */
        private String unusedWarehouseErrorMsg;

        /**
         * 使用禁止配报错
         */
        private String usedBlackDeliveryErrorMsg;

        /**
         * 承诺/最晚揽收时间
         */
        private String promiseCollectTime;

        /**
         * 承诺/最晚出库时间
         */
        private String promiseOutboundTime;

        /**
         * 判断 子单有仓建议，erp需按建议择仓
         */
        public boolean isWarehouseRecommendation() {
            return Objects.equals("2", this.bizSdType);
        }

        /**
         * 判断 子单配建议，erp需按建议择配
         */
        public boolean isExpressRecommendation() {
            return sendState != null || sendCity != null ||
                    sendDistrict != null || sendTown != null;
        }

        public LogisticsInfo() {
        }

        public Long getTradeId() {
            return tradeId;
        }

        public void setTradeId(Long tradeId) {
            this.tradeId = tradeId;
        }

        public Long getSubTradeId() {
            return subTradeId;
        }

        public void setSubTradeId(Long subTradeId) {
            this.subTradeId = subTradeId;
        }

        public String getBizSdType() {
            return bizSdType;
        }

        public void setBizSdType(String bizSdType) {
            this.bizSdType = bizSdType;
        }

        public String getBizStoreCode() {
            return bizStoreCode;
        }

        public void setBizStoreCode(String bizStoreCode) {
            this.bizStoreCode = bizStoreCode;
        }

        public String getSendCountry() {
            return sendCountry;
        }

        public void setSendCountry(String sendCountry) {
            this.sendCountry = sendCountry;
        }

        public String getSendState() {
            return sendState;
        }

        public void setSendState(String sendState) {
            this.sendState = sendState;
        }

        public String getSendCity() {
            return sendCity;
        }

        public void setSendCity(String sendCity) {
            this.sendCity = sendCity;
        }

        public String getSendDistrict() {
            return sendDistrict;
        }

        public void setSendDistrict(String sendDistrict) {
            this.sendDistrict = sendDistrict;
        }

        public String getSendTown() {
            return sendTown;
        }

        public void setSendTown(String sendTown) {
            this.sendTown = sendTown;
        }

        public String getSendDivisionCode() {
            return sendDivisionCode;
        }

        public void setSendDivisionCode(String sendDivisionCode) {
            this.sendDivisionCode = sendDivisionCode;
        }

        public String getBlackDeliveryCps() {
            return blackDeliveryCps;
        }

        public void setBlackDeliveryCps(String blackDeliveryCps) {
            this.blackDeliveryCps = blackDeliveryCps;
        }

        public String getWhiteDeliveryCps() {
            return whiteDeliveryCps;
        }

        public void setWhiteDeliveryCps(String whiteDeliveryCps) {
            this.whiteDeliveryCps = whiteDeliveryCps;
        }

        public String getBizDeliveryCode() {
            return bizDeliveryCode;
        }

        public void setBizDeliveryCode(String bizDeliveryCode) {
            this.bizDeliveryCode = bizDeliveryCode;
        }

        public String getUnusedDeliveryErrorMsg() {
            return unusedDeliveryErrorMsg;
        }

        public void setUnusedDeliveryErrorMsg(String unusedDeliveryErrorMsg) {
            this.unusedDeliveryErrorMsg = unusedDeliveryErrorMsg;
        }

        public String getUnusedWarehouseErrorMsg() {
            return unusedWarehouseErrorMsg;
        }

        public void setUnusedWarehouseErrorMsg(String unusedWarehouseErrorMsg) {
            this.unusedWarehouseErrorMsg = unusedWarehouseErrorMsg;
        }

        public String getUsedBlackDeliveryErrorMsg() {
            return usedBlackDeliveryErrorMsg;
        }

        public void setUsedBlackDeliveryErrorMsg(String usedBlackDeliveryErrorMsg) {
            this.usedBlackDeliveryErrorMsg = usedBlackDeliveryErrorMsg;
        }

        public String getPromiseCollectTime() {
            return promiseCollectTime;
        }

        public void setPromiseCollectTime(String promiseCollectTime) {
            this.promiseCollectTime = promiseCollectTime;
        }

        public String getPromiseOutboundTime() {
            return promiseOutboundTime;
        }

        public void setPromiseOutboundTime(String promiseOutboundTime) {
            this.promiseOutboundTime = promiseOutboundTime;
        }

        public Long getBizDeliveryType() {
            return bizDeliveryType;
        }

        public void setBizDeliveryType(Long bizDeliveryType) {
            this.bizDeliveryType = bizDeliveryType;
        }
    }

    public static class LogisticsAgreement implements Serializable {
        private static final long serialVersionUID = 5682773345180307315L;

        /**
         * 物流信息，多个值时用英文逗号隔开
         */
        private String asdpAds;

        /**
         * value=logistics_upgrade 为天猫物流升级订单
         * value=aox 为翱象订单
         */
        private String asdpBizType;

        /**
         * 服务承诺/异常文案
         */
        private String logisticsServiceMsg;

        /**
         * 承诺/最晚送达时间
         */
        private String promiseSignTime;

        /**
         * 计划送达时间
         */
        private String signTime;

        public String getAsdpAds() {
            return asdpAds;
        }

        public void setAsdpAds(String asdpAds) {
            this.asdpAds = asdpAds;
        }

        public String getAsdpBizType() {
            return asdpBizType;
        }

        public void setAsdpBizType(String asdpBizType) {
            this.asdpBizType = asdpBizType;
        }

        public String getLogisticsServiceMsg() {
            return logisticsServiceMsg;
        }

        public void setLogisticsServiceMsg(String logisticsServiceMsg) {
            this.logisticsServiceMsg = logisticsServiceMsg;
        }

        public String getPromiseSignTime() {
            return promiseSignTime;
        }

        public void setPromiseSignTime(String promiseSignTime) {
            this.promiseSignTime = promiseSignTime;
        }

        public String getSignTime() {
            return signTime;
        }

        public void setSignTime(String signTime) {
            this.signTime = signTime;
        }
    }


    public TbRecommendation() {
    }

    public List<LogisticsInfo> getLogisticsInfos() {
        return logisticsInfos;
    }

    public void setLogisticsInfos(List<LogisticsInfo> logisticsInfos) {
        this.logisticsInfos = logisticsInfos;
    }

    public String getReceiveProvinceCityDistrictTown() {
        return receiveProvinceCityDistrictTown;
    }

    public void setReceiveProvinceCityDistrictTown(String receiveProvinceCityDistrictTown) {
        this.receiveProvinceCityDistrictTown = receiveProvinceCityDistrictTown;
    }

    public LogisticsAgreement getLogisticsAgreement() {
        return logisticsAgreement;
    }

    public void setLogisticsAgreement(LogisticsAgreement logisticsAgreement) {
        this.logisticsAgreement = logisticsAgreement;
    }
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;

import java.util.Date;

/**
 * 交易子账号配置
 * @Description:TODO
 * @author:z<PERSON><PERSON><PERSON><PERSON>
 * @time:Sep 12, 2019 2:00:47 PM
 */
public class TradeStaffConfig extends Model {
	
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long companyId;

    private Long staffId;

    private Date created;
    
    private Date modified;
    
    
    /**
     * 领取拣选任务同时完成拣选
     */
    private Integer openFinishPickOnClaimTask;

	/**
	 * 后置允许扫描多件波次
	 */
	private Integer postAllowMultiWave;

	/**
	 * 是否开启强制包装验货
	 */
	private Integer openForceTradePack;
	/**
	 * 包装后知否直接打印
	 */
	private Integer printAfterPack;

    /**
     * 是否开启验货后自动登记
     */
    private Integer openForceRegisterFinish;

	/**
	 * 开启备货区优先拣选
	 */
	private Integer openBackRegionPickFirst;

	/**
	 * 单个sku生成一个波次
	 */
	private Integer checkedWaveSameSku;

	/**
	 * 波次分组，每个波次最大订单数
	 */
	private Integer checkedWaveMaxNumPer;
	/**
	 * 波次列表商品明细配置
	 * @see WaveItemInfo.ItemShowConf
	 */
	private String waveListItemShowConf;

	/**
	 * 相同快递
	 */
	private Integer expressEq;

	/**
	 * json 格式配置信息
	 */
	private String configs;

	/**
	 * 是否强制验货登记
	 */
	private Integer forceRegister;
	/**
	 * 开启箱规码扫描
	 */
	private Integer openBoxCodeScan;

	/**
	 * 开启按套件本身验货
	 */
	private Integer openSuitselfPack;

	private Integer logisticsCompanyEq;

	/**
	 * 开启未打印包装验货
	 */
	private Integer allowUnprintPack;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getStaffId() {
		return staffId;
	}

	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}

	public Date getModified() {
		return modified;
	}

	public void setModified(Date modified) {
		this.modified = modified;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Integer getOpenFinishPickOnClaimTask() {
		return openFinishPickOnClaimTask;
	}

	public void setOpenFinishPickOnClaimTask(Integer openFinishPickOnClaimTask) {
		this.openFinishPickOnClaimTask = openFinishPickOnClaimTask;
	}

	public Integer getPostAllowMultiWave() {
		return postAllowMultiWave;
	}

	public void setPostAllowMultiWave(Integer postAllowMultiWave) {
		this.postAllowMultiWave = postAllowMultiWave;
	}

	public Integer getOpenForceTradePack() {
		return openForceTradePack;
	}

	public void setOpenForceTradePack(Integer openForceTradePack) {
		this.openForceTradePack = openForceTradePack;
	}
	public Integer getPrintAfterPack() {
		return printAfterPack;
	}

	public void setPrintAfterPack(Integer printAfterPack) {
		this.printAfterPack = printAfterPack;
	}

    public Integer getOpenForceRegisterFinish() {
        return openForceRegisterFinish;
    }

    public void setOpenForceRegisterFinish(Integer openForceRegisterFinish) {
        this.openForceRegisterFinish = openForceRegisterFinish;
    }

	public Integer getOpenBackRegionPickFirst() {
		return openBackRegionPickFirst;
	}

	public void setOpenBackRegionPickFirst(Integer openBackRegionPickFirst) {
		this.openBackRegionPickFirst = openBackRegionPickFirst;
	}

	public Integer getCheckedWaveSameSku() {
		return checkedWaveSameSku;
	}

	public void setCheckedWaveSameSku(Integer checkedWaveSameSku) {
		this.checkedWaveSameSku = checkedWaveSameSku;
	}

	public Integer getCheckedWaveMaxNumPer() {
		return checkedWaveMaxNumPer;
	}

	public void setCheckedWaveMaxNumPer(Integer checkedWaveMaxNumPer) {
		this.checkedWaveMaxNumPer = checkedWaveMaxNumPer;
	}

	public Integer getExpressEq() {
		return expressEq;
	}

	public void setExpressEq(Integer expressEq) {
		this.expressEq = expressEq;
	}

	public String getConfigs() {
		return configs;
	}

	public void setConfigs(String configs) {
		this.configs = configs;
	}

	public String getWaveListItemShowConf() {
		return waveListItemShowConf;
	}

	public void setWaveListItemShowConf(String waveListItemShowConf) {
		this.waveListItemShowConf = waveListItemShowConf;
	}

	public Integer getForceRegister() {
		return forceRegister;
	}

	public void setForceRegister(Integer forceRegister) {
		this.forceRegister = forceRegister;
	}

	public Integer getOpenBoxCodeScan() {
		return openBoxCodeScan;
	}

	public void setOpenBoxCodeScan(Integer openBoxCodeScan) {
		this.openBoxCodeScan = openBoxCodeScan;
	}

	public Integer getOpenSuitselfPack() {
		return openSuitselfPack;
	}

	public void setOpenSuitselfPack(Integer openSuitselfPack) {
		this.openSuitselfPack = openSuitselfPack;
	}

	public Integer getLogisticsCompanyEq() {
		return logisticsCompanyEq;
	}

	public void setLogisticsCompanyEq(Integer logisticsCompanyEq) {
		this.logisticsCompanyEq = logisticsCompanyEq;
	}

	public Integer getAllowUnprintPack() {
		return allowUnprintPack;
	}

	public void setAllowUnprintPack(Integer allowUnprintPack) {
		this.allowUnprintPack = allowUnprintPack;
	}
}

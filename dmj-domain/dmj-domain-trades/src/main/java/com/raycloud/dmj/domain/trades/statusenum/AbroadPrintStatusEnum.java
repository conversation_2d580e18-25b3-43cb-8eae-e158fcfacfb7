package com.raycloud.dmj.domain.trades.statusenum;


/**
 * @description: 跨境打印任务状态
 * @author: <PERSON>haojian<PERSON>
 * @time: 2024/11/19 14:43
 */
public enum AbroadPrintStatusEnum {

    /**
     * 未消费
     */
    PENDING("PENDING", "未消费"),

    /**
     * 消费中
     */
    IN_PROGRESS("IN_PROGRESS", "消费中"),

    /**
     * 消费失败
     */
    FAILED("FAILED", "消费失败"),

    /**
     * 消费成功
     */
    COMPLETED("COMPLETED", "消费成功");

    private final String code;
    private final String description;

    AbroadPrintStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取任务状态的代码
     *
     * @return 状态代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取任务状态的描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取对应的任务状态枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回 null
     */
    public static AbroadPrintStatusEnum fromCode(String code) {
        for (AbroadPrintStatusEnum status : AbroadPrintStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}

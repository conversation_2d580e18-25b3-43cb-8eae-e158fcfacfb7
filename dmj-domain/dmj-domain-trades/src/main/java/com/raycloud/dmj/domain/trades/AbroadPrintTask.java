package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * @description:
 * @author: <PERSON>haojianbo
 * @time: 2024/11/8 10:18
 */


@Table(name = "abroad_print_tasks")
public class AbroadPrintTask extends Model {


    /**
     * 任务ID，用于唯一标识一个任务
     */
    private Long id;

    /**
     * 任务的SID，用于跟踪任务的特定标识符
     */
    private Long sid;

    /**
     * 公司ID，标识任务所属的公司
     */
    private Long companyId;

    /**
     * 重试次数，记录任务已经尝试执行的次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数，定义任务可以尝试执行的最大次数
     */
    private Integer maxRetries;

    /**
     * 任务状态 (PENDING-未消费, IN_PROGRESS-消费中, FAILED-消费失败, COMPLETED-消费成功)',
     */
    private String taskStatus;

    /**
     * 最后尝试时间，记录最后一次尝试执行任务的时间
     */
    private Date lastAttemptTime;

    /**
     * 失败原因，记录任务执行失败的原因
     */
    private String failureReason;

    /**
     * 创建时间，记录任务被创建的时间
     */
    private Date created;

    /**
     * 更新时间，记录任务信息最后一次被更新的时间
     */
    private Date updated;

    /**
     * 平台URL，任务执行相关平台的地址
     */
    private String platformUrl;

    /**
     * 运单号，与任务相关的运单标识
     */
    private String waybillNumber;

    /**
     * 版本号，记录任务信息的版本，用于并发控制
     */
    private Integer versions;

    /**
     * 来源，描述任务的来源
     */
    private String source;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetries() {
        if (maxRetries == null) {
            return 3;
        }
        return maxRetries;
    }

    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Date getLastAttemptTime() {
        return lastAttemptTime;
    }

    public void setLastAttemptTime(Date lastAttemptTime) {
        this.lastAttemptTime = lastAttemptTime;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    public String getPlatformUrl() {
        return platformUrl;
    }

    public void setPlatformUrl(String platformUrl) {
        this.platformUrl = platformUrl;
    }

    public String getWaybillNumber() {
        return waybillNumber;
    }

    public void setWaybillNumber(String waybillNumber) {
        this.waybillNumber = waybillNumber;
    }

    public Integer getVersions() {
        return versions;
    }

    public void setVersions(Integer versions) {
        this.versions = versions;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}

package com.raycloud.dmj.domain;

import com.raycloud.dmj.warehouse.domain.AssTradeSnInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/17
 */
@Data
public class Party3TradeConsignDTO implements Serializable {
    private static final long serialVersionUID = -7235419421983713262L;

    /**
     * 创建时间
     */
    private Date created;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 系统单号
     */
    private Long sid;
    /**
     * 快递单号
     */
    private String outSid;
    /**
     * 模板ID
     */
    private Long templateId;
    /**
     * 模板类型
     */
    private Integer templateType;
    /**
     * 包裹重量
     */
    private Double weight;
    /**
     * 卖家备注
     */
    private String sellerMemo;

    /**
     * 快递公司ID
     */
    private Long logisticsCompanyId;

    /**
     * 拓展属性
     */
    private String extendProps;


    private Map<String, AssTradeSnInfo> assTradeSnInfoMap;

    /**
     * 包裹编码
     */
    private String packageCode;
}

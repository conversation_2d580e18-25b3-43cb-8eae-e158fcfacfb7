package com.raycloud.dmj.domain.logistics;

import com.raycloud.dmj.domain.utils.CommonConstants;

public enum EnumCombineParcelNum {

    SHOPEE(CommonConstants.PLAT_FORM_TYPE_SHOPEE, 10000),
    LAZADA(CommonConstants.PLAT_FORM_TYPE_LAZADA, 1000),
    SMT(CommonConstants.PLAT_FORM_TYPE_SMT, 200),
    SMT_BIGBAG(CommonConstants.PLAT_FORM_TYPE_SMT, 1000),
    SMT_BATCH(CommonConstants.PLAT_FORM_TYPE_SMT, 5000),
    SMT_MAX_SUBBAG(CommonConstants.PLAT_FORM_TYPE_SMT, 50),
    ;


    EnumCombineParcelNum(String source, Integer maxNum) {
        this.source = source;
        this.maxNum = maxNum;
    }

    private String source;

    private Integer maxNum;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getMaxNum() {
        return maxNum;
    }

    public void setMaxNum(Integer maxNum) {
        this.maxNum = maxNum;
    }

    public static Integer getCombineParcelNum(String source) {
        for (EnumCombineParcelNum value : EnumCombineParcelNum.values()) {
            if (value.source.equals(source)) {

                return value.maxNum;
            }
        }
        return 0;
    }

}

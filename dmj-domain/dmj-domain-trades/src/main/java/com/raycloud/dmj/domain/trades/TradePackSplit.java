package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;
import java.util.List;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 17/7/20.
 * 打包拆分
 */
@Table(name = "trade_pack_split", routerKey = "tradePackSplitDbNo")
public class TradePackSplit extends Model {

    private static final long serialVersionUID = 4553313729594957188L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 快递模板编号
     */
    private Long templateId;

    /**
     * 快递模版类型
     */
    private Integer templateType;

    /**
     * 模板名称,不做持久化处理
     */
    private String templateName;

    /**
     * 快递公司的编号，不做持久化处理
     */
    private Long expressCompanyId;

    /**
     * 快递公司的代码
     */
    private String expressCode;

    /**
     * 订单使用的模板是否为手工填写单号模板
     */
    private Integer isManulfill;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 是否可用
     */
    private Integer enableStatus;

    /**
     * 装箱里面详细的商品信息
     */
    private List<OrderPackSplit> orderPackSplits;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Long getExpressCompanyId() {
        return expressCompanyId;
    }

    public void setExpressCompanyId(Long expressCompanyId) {
        this.expressCompanyId = expressCompanyId;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public Integer getIsManulfill() {
        return isManulfill;
    }

    public void setIsManulfill(Integer isManulfill) {
        this.isManulfill = isManulfill;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public List<OrderPackSplit> getOrderPackSplits() {
        return orderPackSplits;
    }

    public void setOrderPackSplits(List<OrderPackSplit> orderPackSplits) {
        this.orderPackSplits = orderPackSplits;
    }
}

package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/1/22 11:01 上午
 */
public class DangkouSidsGetRequest implements Serializable {
    private static final long serialVersionUID = 4263812552666187248L;

    /**
     * 发货开始时间
     */
    private Date startDate;

    /**
     * 发货结束时间
     */
    private Date endDate;

    private Staff staff;

    /**
     * 是否需要返回total,否则返回-1
     */
    private boolean needTotal;

    private Page page;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public boolean isNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(boolean needTotal) {
        this.needTotal = needTotal;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}

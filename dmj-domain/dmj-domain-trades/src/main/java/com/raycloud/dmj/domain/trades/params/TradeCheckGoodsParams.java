package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> xubin
 * @desc:  验货登记接口入参
 * @date : 12/20/21  10:18 AM
 */
public class TradeCheckGoodsParams implements Serializable{

    private static final long serialVersionUID = 4495879074616689784L;

    /**
     * 订单
     */
    private List<Long> sids;

    private boolean repeatable;

    /**
     * 完成是否直接发货
     */
    private boolean consign;

    /**
     * 是否强制验货
     */
    private boolean force;

    /**
     * 运单号
     */
    private String outSid;

    private String clientIP;

    /**
     * 绩效员工
     */
    private Long staffId;

    /**
     * 绩效员工名称
     */
    private String staffName;

    /**
     * 首单号
     */
    private String firstOutSid;

    /**
     * 尾单号
     */
    private String lastOutSid;

    /**
     * 是否直接完成
     */
    private boolean complete;

    /**
     * 是否校验售后补发单
     */
    private boolean checkReissue;

    /**
     * 穿透面单 公司Id-sid
     */
    private String pierceThroughCode;

    /**
     * 分销公司名称
     */
    private String distributorCompanyName;

    private Long sid;

    private Long shortSid;

    private Integer checkGoodsType;

    /**
     * 是否展示一单多包扫描单号
     */
    private boolean showMultiPacksScanOutSid;

    private List<TradeCheckGoodsTradeInfo> tradeInfos;

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public boolean isRepeatable() {
        return repeatable;
    }

    public void setRepeatable(boolean repeatable) {
        this.repeatable = repeatable;
    }

    public boolean isConsign() {
        return consign;
    }

    public void setConsign(boolean consign) {
        this.consign = consign;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getFirstOutSid() {
        return firstOutSid;
    }

    public void setFirstOutSid(String firstOutSid) {
        this.firstOutSid = firstOutSid;
    }

    public String getLastOutSid() {
        return lastOutSid;
    }

    public void setLastOutSid(String lastOutSid) {
        this.lastOutSid = lastOutSid;
    }

    public boolean isComplete() {
        return complete;
    }

    public void setComplete(boolean complete) {
        this.complete = complete;
    }

    public boolean isCheckReissue() {
        return checkReissue;
    }

    public void setCheckReissue(boolean checkReissue) {
        this.checkReissue = checkReissue;
    }

    public String getPierceThroughCode() {
        return pierceThroughCode;
    }

    public void setPierceThroughCode(String pierceThroughCode) {
        this.pierceThroughCode = pierceThroughCode;
    }

    public String getDistributorCompanyName() {
        return distributorCompanyName;
    }

    public void setDistributorCompanyName(String distributorCompanyName) {
        this.distributorCompanyName = distributorCompanyName;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getShortSid() {
        return shortSid;
    }

    public void setShortSid(Long shortSid) {
        this.shortSid = shortSid;
    }

    public Integer getCheckGoodsType() {
        return checkGoodsType;
    }

    public void setCheckGoodsType(Integer checkGoodsType) {
        this.checkGoodsType = checkGoodsType;
    }

    public boolean isShowMultiPacksScanOutSid() {
        return showMultiPacksScanOutSid;
    }

    public void setShowMultiPacksScanOutSid(boolean showMultiPacksScanOutSid) {
        this.showMultiPacksScanOutSid = showMultiPacksScanOutSid;
    }

    public List<TradeCheckGoodsTradeInfo> getTradeInfos() {
        return tradeInfos;
    }

    public void setTradeInfos(List<TradeCheckGoodsTradeInfo> tradeInfos) {
        this.tradeInfos = tradeInfos;
    }
}

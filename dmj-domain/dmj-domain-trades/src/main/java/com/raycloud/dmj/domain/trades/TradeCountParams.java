package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

/**
 * @Author: ruanyaguang
 * @Date : 2018/9/19
 * @Info : 订单数量统计参数
 */
public class TradeCountParams implements Serializable {
    private static final long serialVersionUID = 1597132585591793130L;

    private Staff staff;

    private TradeControllerParams queryParams;

    private String queryIds;

    private Boolean ignoreAuth;

    public static class Builder {
        private Staff staff;

        private TradeControllerParams queryParams;

        private String queryIds;

        private Boolean ignoreAuth;

        public TradeCountParams build() {
            TradeCountParams params = new TradeCountParams();
            params.staff = staff;
            params.queryParams = queryParams;
            params.queryIds = queryIds;
            params.ignoreAuth = ignoreAuth;
            return params;
        }

        public Builder staff(Staff staff) {
            this.staff = staff;
            return this;
        }

        public Builder queryParams(TradeControllerParams queryParams) {
            this.queryParams = queryParams;
            return this;
        }

        public Builder queryIds(String queryIds) {
            this.queryIds = queryIds;
            return this;
        }

        public Builder ignoreAuth(Boolean ignoreAuth) {
            this.ignoreAuth = ignoreAuth;
            return this;
        }

    }

    public Staff getStaff() {
        return staff;
    }

    public TradeControllerParams getQueryParams() {
        return queryParams;
    }

    public String getQueryIds() {
        return queryIds;
    }

    public Boolean getIgnoreAuth() {
        return ignoreAuth;
    }
}
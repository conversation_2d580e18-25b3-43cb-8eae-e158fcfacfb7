package com.raycloud.dmj.domain.trades.params;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class TradeListRequest implements Serializable {

    private Long taobaoId;

    private String shippingCarrier;

    private String[] shippingCarriers;

    private String sids;

    private Long combineParcelId;

    private String tids;

    private String outSids;

    private Long startTime;

    private Long endTime;

    private Date consignStartTime;

    private Date consignEndTime;

    private String shortIds;

    private String sysStatus;

    private String source;

    private String userId;

    /**
     * 是否erp发货 1是 2否
     */
    private Integer sysConsigned;

    private Long warehouseId;
    /**
     * 揽收仓
     */
    private String takeWarehouse;

    /**
     * 配送方式：1 上门揽收 2自行配送 3快递自寄
     */
    private String deliveryType;

    /**
     * 当前用户的店铺权限列表
     */
    private List<Long> userList;

    /**
     * 订单标签 1000000022L 预发货
     */
    private String tagIds;

    /**
     * tiktok可以支持的国家国家
     */
    private List<String> receiverCountry;

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public String[] getShippingCarriers() {
        return shippingCarriers;
    }

    public void setShippingCarriers(String[] shippingCarriers) {
        this.shippingCarriers = shippingCarriers;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getTids() {
        return tids;
    }

    public void setTids(String tids) {
        this.tids = tids;
    }

    public String getOutSids() {
        return outSids;
    }

    public void setOutSids(String outSids) {
        this.outSids = outSids;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Date getConsignStartTime() {
        return consignStartTime;
    }

    public void setConsignStartTime(Date consignStartTime) {
        this.consignStartTime = consignStartTime;
    }

    public Date getConsignEndTime() {
        return consignEndTime;
    }

    public void setConsignEndTime(Date consignEndTime) {
        this.consignEndTime = consignEndTime;
    }

    public String getShortIds() {
        return shortIds;
    }

    public void setShortIds(String shortIds) {
        this.shortIds = shortIds;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getSysConsigned() {
        return sysConsigned;
    }

    public void setSysConsigned(Integer sysConsigned) {
        this.sysConsigned = sysConsigned;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getTakeWarehouse() {
        return takeWarehouse;
    }

    public void setTakeWarehouse(String takeWarehouse) {
        this.takeWarehouse = takeWarehouse;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public List<Long> getUserList() {
        return userList;
    }

    public void setUserList(List<Long> userList) {
        this.userList = userList;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public List<String> getReceiverCountry() {
        return receiverCountry;
    }

    public void setReceiverCountry(List<String> receiverCountry) {
        this.receiverCountry = receiverCountry;
    }
}

package com.raycloud.dmj.domain.trades.warehouse;

import lombok.*;

@Getter
@Setter
public class MatchResult {

    private Long ruleId;

    private boolean success;

    private String msg;

    public static MatchResult success() {
        MatchResult result = new MatchResult();
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    public static MatchResult fail(String msg) {
        MatchResult result = new MatchResult();
        result.setSuccess(Boolean.FALSE);
        result.setMsg(msg);
        return result;
    }
}

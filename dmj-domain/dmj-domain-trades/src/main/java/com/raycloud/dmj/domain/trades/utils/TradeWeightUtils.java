package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;

import java.util.Objects;

/**
 * TradeWeightUtils
 *
 * <AUTHOR>
 * @Date 2019/9/17
 * @Time 21:39
 */
public class TradeWeightUtils {

    public static boolean isBeforeConsignWeight(Integer kind) {
        return null == kind || 0 == kind || 2 == kind || 4 == kind;//4为批量称重，批量称重只有发货前称重
    }

    public static boolean isAfterConsignWeight(Integer kind) {
        return  kind != null && (kind == 1 || kind == 3);
    }


    public static boolean isReWeight(Integer kind) {
        return kind != null && (kind == 2 || kind == 3);
    }

    public static boolean supportPackNew(Integer kind){
        return (kind != null && kind == 0) || isReWeight(kind);
    }

    /**
     * 判断合单非主单,隐藏单
     *
     * @param trade
     * @return
     */
    public static boolean isMergeHiddenTrade(Trade trade) {
        return TradeUtils.isMerge(trade)
                && trade.getMergeSid() != null
                && trade.getSid() != null
                && trade.getMergeSid() - trade.getSid() != 0;
    }

    public static boolean isNeedCoverActualVolume(Trade trade) {
        return trade != null && trade.getVolume() != null && !MathUtils.equalsZero(trade.getVolume()) && (trade.getIsWeigh() == null || trade.getIsWeigh() != null && trade.getIsWeigh() == 0) && ifVolumeChanged(trade);
    }

    public static boolean isNeedCoverActualVolume(Trade trade, TradeConfig tradeConfig) {
        return isNeedCoverActualVolume(trade) && tradeConfig != null
                && Objects.equals(1, tradeConfig.getInteger("isCoverWeight"));
    }

    public static boolean ifVolumeChanged(Trade trade) {
        return trade != null
                && trade.getVolume() != null
                && (trade.getTradeExt() == null ||
                (trade.getTradeExt() != null && (trade.getTradeExt().get("actualVolume") == null || (!MathUtils.equals(MathUtils.toString(trade.getVolume()), Objects.toString(trade.getTradeExt().get("actualVolume")))))));
    }
}

package com.raycloud.dmj.domain.trades;


import java.io.Serializable;
import java.util.List;

public class UserDataConfigResponse implements Serializable {

    private List<UserDataConfigDTO> userDataConfigs;

    private Boolean isDefault;

    public List<UserDataConfigDTO> getUserDataConfigs() {
        return userDataConfigs;
    }

    public void setUserDataConfigs(List<UserDataConfigDTO> userDataConfigs) {
        this.userDataConfigs = userDataConfigs;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }
}

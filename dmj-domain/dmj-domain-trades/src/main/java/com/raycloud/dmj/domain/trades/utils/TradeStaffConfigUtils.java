package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeStaffConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * TradeConfigUtils
 *
 * <AUTHOR>
 * @Date 2019-06-18
 * @Time 17:50
 */
public class TradeStaffConfigUtils {

    public static boolean parseExtendConfigWithResult(Map<String, Object> result, TradeStaffConfig tradeStaffConfig) {

        if (tradeStaffConfig != null && result != null) {
            try {
                result.putAll(parseExtendConfig(tradeStaffConfig.getConfigs()));
            } catch (Exception e) {
                Logs.error("转换tradeStaffConfig中扩展配置出错，jsonStr:" + tradeStaffConfig.getConfigs(), e);
                return false;
            }
        }
        return true;
    }

    public static Map<String, Object> parseExtendConfig(TradeStaffConfig tradeStaffConfig) {
        if (tradeStaffConfig != null) {
            try {
                return parseExtendConfig(tradeStaffConfig.getConfigs());
            } catch (Exception e) {
                Logs.error("转换tradeStaffConfig中扩展配置出错，jsonStr:" + tradeStaffConfig.getConfigs(), e);
            }
        }
        return new HashMap<>();
    }


    public static Map<String, Object> parseExtendConfig(String configStr) {
        if (StringUtils.isNotBlank(configStr)) {
            Map<String, Object> map = JSON.parseObject(configStr, Map.class);
            return map == null ? new HashMap<>() : map;
        }
        return new HashMap<>();
    }

    public static Integer getTradeCountDateRange(String countDateRangeName, TradeStaffConfig tradeStaffConfig) {
        try {
            Object days = parseExtendConfig(tradeStaffConfig).get(countDateRangeName);
            if (Objects.isNull(days)) {
                return null;
            }
            return Integer.valueOf(days.toString());
        } catch (Exception e) {
            Logs.error("转换tradeStaffConfig中扩展配置出错，jsonStr:" + tradeStaffConfig.getConfigs(), e);
        }
        return null;
    }


    public static Map<String,Object> getStaffDefaultConfigByKeys(String keys, TradeStaffConfig tradeStaffConfig) {
        Map<String,Object>  map = new HashMap<>();
        try {
            if (StringUtils.isBlank(keys)){
                return map;
            }
            String[] keyArr = keys.split(",");
            Map<String, Object> extendConfigMap = parseExtendConfig(tradeStaffConfig);
            for (String key : keyArr) {
                map.put(key,extendConfigMap.get(key));
            }
        } catch (Exception e) {
            Logs.error("转换tradeStaffConfig中扩展配置出错，jsonStr:" + tradeStaffConfig.getConfigs(), e);
        }
        return map;
    }
}

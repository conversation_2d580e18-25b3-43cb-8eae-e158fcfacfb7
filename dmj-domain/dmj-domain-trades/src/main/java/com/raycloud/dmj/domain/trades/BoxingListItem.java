package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ruanyg on 16/12/5.
 */
public class BoxingListItem implements Serializable {

    private static final long serialVersionUID = -6940426418035168760L;

    private String boxNo;

    private List<BoxingListProduct> products;

    private Integer isPrint;

    public String getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(String boxNo) {
        this.boxNo = boxNo;
    }

    public List<BoxingListProduct> getProducts() {
        return products;
    }

    public void setProducts(List<BoxingListProduct> products) {
        this.products = products;
    }

    public Integer getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Integer isPrint) {
        this.isPrint = isPrint;
    }
}

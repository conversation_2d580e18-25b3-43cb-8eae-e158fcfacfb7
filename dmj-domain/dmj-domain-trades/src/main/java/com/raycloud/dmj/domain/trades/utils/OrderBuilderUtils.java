package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2023/7/31 3:10 下午
 * @Description:
 */
public class OrderBuilderUtils {


    /**
     * 根据order来更新数据库的时候，需要新生成一个更新的order，强烈建议通过这种方式来获取新的order对象
     *
     * @param oldOrder 老的子订单
     * @return 待更新的子订单
     */
    public  static TbOrder builderUpdateOrder(Order oldOrder) {
        TbOrder newOrder = new TbOrder();
        newOrder.setId(oldOrder.getId());
        newOrder.setExceptData(oldOrder.getExceptData());
        newOrder.setSubTradeExceptDatas(oldOrder.getSubTradeExceptDatas());
        newOrder.setInsufficientCanceled(oldOrder.getInsufficientCanceled());
        newOrder.setOrigin(oldOrder);
        return newOrder;
    }

    public static TbOrder builderUpdateOrderWithV(Order oldOrder) {
        TbOrder newOrder = builderUpdateOrder(oldOrder);
        newOrder.setV(oldOrder.getV());
        return newOrder;
    }
}

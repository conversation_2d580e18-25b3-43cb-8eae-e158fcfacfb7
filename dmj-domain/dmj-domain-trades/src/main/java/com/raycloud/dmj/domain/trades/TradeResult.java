package com.raycloud.dmj.domain.trades;

import lombok.*;

import java.io.Serializable;

/**
 * 订单操作结果视图类
 * <AUTHOR>
 * @version 1.0
 * @date 2017-12-19 19:09
 */
@Setter
@Getter
public class TradeResult implements Serializable {

    private static final long serialVersionUID = 2246677833964460189L;

    private Long sid;

    private Long shortId;

    private String tid;

    private Long mergeSid;

    private Long destId;

    private Boolean success;

    private String errorMsg;

    private String subErrorMsg;

    private String sysStatus;

    private Integer isHandlerMemo;

    private Integer isHandlerMessage;

}

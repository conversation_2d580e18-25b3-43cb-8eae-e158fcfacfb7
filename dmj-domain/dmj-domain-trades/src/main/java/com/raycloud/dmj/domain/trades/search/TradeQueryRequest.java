package com.raycloud.dmj.domain.trades.search;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-31
 */
@Data
public class TradeQueryRequest extends QueryIdCondition implements Serializable {


    public TradeQueryRequest(Long companyId, Long staffId) {
        this.companyId = companyId;
        this.staffId = staffId;
    }

    public TradeQueryRequest(Staff staff) {
        this.companyId = staff.getCompanyId();
        this.staffId = staff.getId();
    }

    public TradeQueryRequest() {
    }

    /**
     * 公司编号
     */
    private Long companyId;


    /**
     * 员工编号
     */
    private Long staffId;


    /**
     * 是否忽略店铺权限
     */
    private Boolean ignoreUserPrivilege =false;


    /**
     * 店铺Id
     */
    private Long[] userIds;


    /**
     * 平台店铺Id
     */
    private Long[] taobaoIds;


    /**
     * 是否忽略仓库权限
     */
    private Boolean ignoreWareHousePrivilege =false;


    /**
     * 仓库Id
     */
    private Long[] wareHouseIds;

    /**
     * 仓库类型 0 自有仓 1 三方仓 2 门店仓库
     * @see com.raycloud.dmj.domain.basis.Warehouse.TYPE_OWN
     * @see com.raycloud.dmj.domain.basis.Warehouse.TYPE_EXTRA
     * @see com.raycloud.dmj.domain.basis.Warehouse.TYPE_STORE
     */
    private Integer[] warehouseTypes;


    /**
     * 系统单号
     */
    private Long[] sids;
    /**
     * 平台单号
     */
    private String[] tids;

    /**
     * 平台单号条件拓展
     */
    private TidConditionProps[] tidProps;
    /**
     * 运单号
     */
    private String[] outSids;

    /**
     * 是否包含运单号
     */
    private Boolean hasOutSid;

    /**
     * 运单号条件拓展
     */
    private OutSidConditionProps outSidProps;


    /**
     * 最大系统单号 仅查询 sid > maxSid 的数据
     */
    private Long maxSid;

    /**
     * 最小系统单号 仅查询 sid < minSid 的数据
     */
    private Long minSid;


    /**
     * 时间范围条件
     */
    private TimeTypeEnum timeType;
    private Date startTime;
    private Date endTime;


    /**
     * 是否已发货
     */
    private Boolean isConsigned;

    /**
     * 是否系统发货 否则为其他erp发货
     */
    private Boolean sysConsigned;


    /**
     * 是否包含已经合单的子订单数据(enableStatus = 2 的数据)
     */
    private Boolean containsMerged=false;

    /**
     * 是否包含已取消的订单
     */
    private Boolean containsCanceled =false;



    /**
     * 订单来源
     */
    private String[] sources;


    /**
     * (供分销业务)分销商公司Id
     */
    private Long[] distributorIds;


    /**
     * (供分销业务)分销商店铺Id
     */
    private Long[] distributorUserIds;


    /**
     * 收件人手机号 最大支持10个
     */
    private String[] receiverMobiles;

    /**
     * 订单类型
     */
    private Integer[] tradeTypes;

    /***
     * 查询指定标签
     */
    private String[] tagIds;
    /**
     * 排除标签
     * */
    private String[] excludeTagIds;


    /**
     * 自定义异常ID
     */
    private String[] exceptIds;

    /**
     * 异常状态条件查询类型
     * 1：仅包含，2：排除，3：同时包含； 4：包含
     */
    private Integer excptQryType;


    /**
     *  order相关的查询条件
     */
    private OrderRefCondition orderRefCondition;


    /**
     * 默认按指定的timeType字段排序
     */
    private Sort sort;

    /**
     * 不分页默认查询2000条
     */
    private Page page;

    /**
     * 强制查询主库
     * 从库数据同步存在一定时间差异,如果对这个时间差非常敏感则可指定查询主库
     * 一般业务都不需要
     */
    private Boolean forceMaster;





}

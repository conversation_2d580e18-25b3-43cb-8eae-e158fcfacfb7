package com.raycloud.dmj.domain.platform.bo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserChangeSourceLogBo implements Serializable {
    private static final long serialVersionUID = 400303416809049032L;

    private Long id;

    private Long userId;

    private Long companyId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;

    private String oldSource;

    private String newSource;

    private Byte enableStatus;
}

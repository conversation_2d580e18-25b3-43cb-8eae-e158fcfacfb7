package com.raycloud.dmj.warehouse.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 快麦WMS 库存异动信息
 * <AUTHOR>
 * @Date 2023/10/20
 */
@Data
public class KmWmsStockChangeInfo implements Serializable {

    private static final long serialVersionUID = 6653612097855415137L;

    /**
     * 单据号
     */
    private String orderCode;
    /**
     * 库存变动类型（销退入库=XTRK 盘点=QTRK&QTCK）
     */
    private String orderType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 商品信息
     */
    private List<Item> items;

    /**
     * 货主id
     */
    private String shipperId;

    /**
     * 物流编码
     */
    private String logisticsCode;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 快递单号
     */
    private String expressCode;

    @Data
    public static class Item implements Serializable {
        private static final long serialVersionUID = -8509851680614893762L;
        /**
         * 商家编码
         */
        private String itemCode;
        /**
         * 库存类型  ZP=正品,CC=次品
         */
        private String inventoryType;

        /**
         * 数量
         */
        private Long quantity;
        /**
         * 生产日期
         */
        private String productDate;
        /**
         * 批次
         */
        private String produceCode;

        /**
         * 盘点后总库存数
         */
        private Long totalQty;

        /**
         * 商品Id
         */
        private Long sysItemId;

        /**
         * 规格Id
         */
        private Long sysSkuId;

    }


}

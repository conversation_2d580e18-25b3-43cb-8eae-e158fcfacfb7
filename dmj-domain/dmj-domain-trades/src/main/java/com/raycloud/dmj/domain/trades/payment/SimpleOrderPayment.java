package com.raycloud.dmj.domain.trades.payment;

import lombok.Data;


@Data
public class SimpleOrderPayment {


    /**
     * 子订单系统编号
     */
    private Long id;

    /**
     * 所属主订单的系统编号
     */
    private Long sid;

    /**
     * 实付金额, 公式: 实付金额 = 应付金额 - 优惠金额
     */
    private String payment;


    /**
     * 商品成本价 cost*num 该商品总成本价
     */
    private Double cost;

    /**
     * 商品销售价
     */
    private String price;

    /**
     * 应付金额, 公式: 应付金额 = 商品数量 * 商品单价 + 手工调整价
     */
    private String totalFee;

    /**
     * 优惠金额
     */
    private String discountFee;


    /**
     * 分销金额
     */
    private String saleFee;
    /**
     * 分销价格
     */
    private String salePrice;

    /**
     * 实付金额
     */
    private Double payAmount;

    /**
     * 子订单商品数量
     */
    private Integer num;

    /**
     * 系统商家编码，如果是商品则为商品的商家编码，如果为SKU则为SKU的商家编码
     */
    private  String sysOuterId;

}

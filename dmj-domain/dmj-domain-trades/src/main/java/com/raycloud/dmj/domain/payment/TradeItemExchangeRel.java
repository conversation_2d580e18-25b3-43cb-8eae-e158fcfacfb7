package com.raycloud.dmj.domain.payment;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.erp.db.model.Column;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description <pre>
 * 订单换商品 新老order映射关系
 * </pre>
 * <AUTHOR>
 * @Date 2023-02-14
 */
@Data
public class TradeItemExchangeRel {

    /**
     * 最终记录到新商品上的平台编码
     * (多换一的情况下 新商品记录第一个原商品的oid)
     */
    private Long oid;

    /**
     * 最终记录到新商品上的平台编码
     */
    private String soid;

    /**
     * 被换商品信息
     */
    List<ExchangOrder> exchangOrders;

    public void addExchangeOrder(ExchangOrder exchangeOrder) {
        if (exchangOrders == null) {
            exchangOrders = new ArrayList<>();
        }
        this.exchangOrders.add(exchangeOrder);
    }

    public void addNewOrders(Order newOrder) {
        if (newOrders == null) {
            newOrders = new ArrayList<>();
        }
        this.newOrders.add(newOrder);
    }

    /**
     * 换后订单
     */
    List<Order> newOrders;


    @Data
    public static class ExchangOrder {

        public ExchangOrder() {
        }

        /**
         * 商品全部被替换
         * @param originOrder
         */
        public ExchangOrder(Order originOrder) {
            this.setOid(originOrder.getOid());
            this.setSoid(originOrder.getSoid());
            this.setId(originOrder.getId());
            this.setSid(originOrder.getSid());
            this.setSysOuterId(originOrder.getSysOuterId());
            this.setItemSysId(originOrder.getItemSysId());
            this.setSkuSysId(originOrder.getSkuSysId());
            this.setPayment(originOrder.getPayment());
            this.setDivideOrderFee(originOrder.getDivideOrderFee());
            this.setPlatformDiscountFee(originOrder.getPlatformDiscountFee());
            this.setNum(originOrder.getNum());
            this.setExchangeNum(originOrder.getNum());
        }

        public ExchangOrder(Order originOrder,int exchangeNum) {
            this.setOid(originOrder.getOid());
            this.setSoid(originOrder.getSoid());
            this.setId(originOrder.getId());
            this.setSid(originOrder.getSid());
            this.setSysOuterId(originOrder.getSysOuterId());
            this.setItemSysId(originOrder.getItemSysId());
            this.setSkuSysId(originOrder.getSkuSysId());
            this.setPayment(originOrder.getPayment());
            this.setDivideOrderFee(originOrder.getDivideOrderFee());
            this.setPlatformDiscountFee(originOrder.getPlatformDiscountFee());
            this.setNum(originOrder.getNum());
            this.setExchangeNum(exchangeNum);
        }

        /**
         * order平台编
         */
        private Long oid;

        /**
         * order平台编
         */
        private String soid;

        /**
         * order对应Id
         */
        private Long id;

        /**
         * order对应sid
         */
        private Long sid;

        /**
         * order商品系统编码
         */
        private String sysOuterId;

        /**
         * 匹配到的系统商品ID
         */
        private Long itemSysId;
        /**
         * 匹配到的系统规格ID
         */
        private Long skuSysId;

        /**
         * order换前原始payment
         */
        private String payment;

        /**
         * order换前原始DivideOrderFee
         */
        private String divideOrderFee;

        /**
         * order换前原始商品数量
         */
        private int num;

        /**
         * 被换商品数量 基于公式 有可能只会换掉指定数量的商品
         */
        private int exchangeNum;

        /**
         * order换前原始platformDiscountFee
         */
        private String platformDiscountFee;
    }

    public String toPlainString() {
        StringBuilder builder = new StringBuilder();
        builder.append("oid:").append(StringUtils.isNotBlank(getSoid())?getSoid():getOid()).append(" 替换关系: ");

        if (CollectionUtils.isNotEmpty(exchangOrders)) {
            for (ExchangOrder exchangOrder : exchangOrders) {
                builder.append(getOrderIdentity(exchangOrder)).append("[oid:").append(StringUtils.isNotBlank(exchangOrder.getSoid())?exchangOrder.getSoid():exchangOrder.getOid()).append("]").append("*").append(exchangOrder.exchangeNum).append(" +");
            }
            builder.deleteCharAt(builder.lastIndexOf("+")).append(" =");
        }else {
            builder.append(exchangOrders == null?"null":"[]").append(" =");
        }

        if (CollectionUtils.isNotEmpty(newOrders)) {
            for (Order order : newOrders) {
                builder.append(getOrderIdentity(order)).append("*").append(order.getNum()).append(" +");
            }
            builder.deleteCharAt(builder.lastIndexOf("+"));
        }else{
            builder.append(newOrders == null?"null":"[]");
        }

        return builder.toString();
    }

    public static String getOrderIdentity(ExchangOrder order){
        if (StringUtils.isNotBlank(order.getSysOuterId())) {
            return order.getSysOuterId();
        }
        Long skuSysId = order.getSkuSysId();
        if (skuSysId != null && skuSysId != -1) {
            return skuSysId +"[sku]";
        }
        return order.getItemSysId() +"[item]";
    }

    public static String getOrderIdentity(Order order){
        if (StringUtils.isNotBlank(order.getSysOuterId())) {
            return order.getSysOuterId();
        }
        Long skuSysId = order.getSkuSysId();
        if (skuSysId != null && skuSysId != -1) {
            return skuSysId +"[sku]";
        }
        return order.getItemSysId() +"[item]";
    }

    @Override
    public String toString() {
        return toPlainString();
    }
}

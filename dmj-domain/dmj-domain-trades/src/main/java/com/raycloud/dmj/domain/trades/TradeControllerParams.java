package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raycloud.dmj.domain.SortExcepLevel;
import com.raycloud.dmj.domain.enums.*;
import org.apache.commons.lang3.StringUtils;
import com.raycloud.dmj.domain.renderer.FieldRendererUtils;
import com.raycloud.dmj.domain.renderer.Int2BooleanRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;
import com.raycloud.dmj.domain.trades.renderer.query.*;

import java.io.Serializable;
import java.util.List;

/**
 * Created by yangheng on 16/7/11.
 */
public class TradeControllerParams implements Serializable {

    private static final long serialVersionUID = 1650831358989354117L;

    /**
     * 自定义查询编号
     */
    private Long id;
    /**
     * 这个用于展现自定义查询参数的标题，不用于搜索
     */
    private String name;
    /**
     * 查询的条件的JSON内容，这个是将TradeQueryParams本身自定义的参数json序列化到这个content中，然后保存到数据库
     */
    private String content;
    /**
     * 员工编号，用于存储员工自定义的条件
     */
    @FieldRenderer("员工编号")
    private Long staffId;
    /**
     * 公司编号
     */
    @FieldRenderer("公司编号")
    private Long companyId;
    /**
     * 0 已被删除的订单，1 正常订单，2 合单后被隐藏的订单，3 分销商审核后的分销订单
     */
    @FieldRenderer("删除状态")
    private Integer enableStatus;

    /**
     * 是否用户自定义查询
     */
    @FieldRenderer("是否用户自定义查询")
    private Boolean ifCustom;
    /**
     * 用户自定义查询ID
     */
    @FieldRenderer("自定义查询")
    private Long customId;
    /**
     * 查询的卡盘ID,具体枚举值参见SystemTradeQueryParamsContext
     */
    @FieldRenderer("卡盘ID")
    private Long queryId;
    /**
     * 排序值
     */
    @FieldRenderer("排序值")
    private Integer sortOrder;
    /**
     * 分组号
     */
    @FieldRenderer("分组号")
    private Long groupId;
    /**
     * 是否是分组0：非，1：是
     */
    @FieldRenderer(value = "是否是分组",rendererClass = Int2BooleanRenderer.class)
    private Integer ifGroup;
    /**
     * 系统订单号
     */
    @FieldRenderer("系统单号")
    private String sid;
    /**
     * 订单短号
     */
    @FieldRenderer("订单短号")
    private String shortId;
    /**
     * 平台交易号
     */
    @FieldRenderer("平台交易号")
    private String tid;

    /**
     * 高亮平台交易号，只用来处理高亮逻辑
     */
    @FieldRenderer(ignore = true)
    private String highlightTid;
    /**
     * 快递单号
     */
    @FieldRenderer("快递单号")
    private String outSid;
    /**
     * 根据店铺用户查询订单， 多个以逗号分隔
     */
    @FieldRenderer("店铺用户")
    private String userId;

    /**
     * 淘宝Id,如果是供销订单就代表对应的分销订单的UserId
     */
    @FieldRenderer("淘宝Id")
    private String taobaoId;

    /**
     *  淘宝Id,如果是供销订单就代表对应的分销订单的UserId，以,拼接
     */
    @FieldRenderer("淘宝Id")
    private String taobaoIds;

    /**
     * 仓库Id， 多个以逗号分隔
     */
    @FieldRenderer("仓库Id")
    private String warehouseId;
    /**
     * 查询的时间类型：created: 下单时间, pay_time: 付款时间, consign_time: 发货时间 audit_time:审核时间
     */
    @FieldRenderer("时间类型")
    private String timeType;
    /**
     * 起始时间
     */
    @FieldRenderer("起始时间")
    private Long startTime;
    /**
     * 截止时间
     */
    @FieldRenderer("截止时间")
    private Long endTime;
    /**
     * 未发货物流预警：起始时间
     */
    @FieldRenderer("未发货物流预警起始时间")
    private Long logisticsWarningStartTime;
    /**
     * 未发货物流预警：截止时间
     */
    @FieldRenderer("未发货物流预警截止时间")
    private Long logisticsWarningEndTime;
    /**
     * 买家昵称/旺旺
     */
    @FieldRenderer("买家昵称/旺旺")
    private String buyerNick;

    /**
     * openUid
     */
    @FieldRenderer("openUid")
    private String openUid;
    /**
     * 客户昵称
     * 将会转化为buyerNick=customerNick + tradeType=21 的查询条件
     */
    @FieldRenderer("客户昵称")
    private String customerNick;
    /**
     * 订单类型，多个以逗号分割
     * 1 货到付款订单（type="code","jd-1"）
     * 3 平台订单（source != "sys"）
     * 4 手工订单（source = "sys"）
     * 5 分销订单（分销商审核后推送给供应商的订单,fxCompanyId>0）
     * 6 预售订单（isPresell = 1）
     * 7 合单（mergeSplitType = 2,4）
     * 8 拆单（mergeSplitType = 5,6）
     * 9 加急订单（isUrgent=1）
     * 10 刷单（空单，scalping=1）
     * 11 有合单提示的订单(checkManualMergeCount=1)
     *
     * @see com.raycloud.dmj.domain.trades.search.TradeQueryTypeEnum
     */
    @FieldRenderer("订单类型")
    private String tradeType;
    @FieldRenderer("排除订单类型")
    private String excludeTradeType;


    /**
     * 对应订单上的原始type字段
     */
    @FieldRenderer("订单类型")
    private String type;

    /**
     * 新版订单类型查询
     */
    @FieldRenderer("新版订单类型")
    private String tradeTypeNewParamsStr;
    /**
     * 订单类型，包含或者排除
     */
    @FieldRenderer("订单类型包含/排除")
    private Integer containOrExclude;
    /**
     * 订单异常状态，多个以逗号分割
     * 具体值参见 TradeQueryParams类中的异常状态常量定义
     */
    @FieldRenderer("异常状态")
    private String exceptionStatus;
    /**
     * 排除订单异常状态，多个以逗号分割
     * 具体值参见 TradeQueryParams类中的异常状态常量定义
     */
    @FieldRenderer("排除异常状态")
    private String excludeExceptionStatus;
    /**
     * 订单系统状态，多个以逗号分隔
     * 具体值参见 Trade类中的系统状态常量定义
     */
    @FieldRenderer("系统状态")
    private String sysStatus;

    @FieldRenderer("o2o系统状态")
    private String o2oSysStatus;
    /**
     * 各平台统一后的平台状态
     */
    @FieldRenderer("平台状态")
    private String status;
    /**
     * 各平台真实的状态
     */
    private String platformStatus;
    /**
     * 收件的省市区 P1@C11$D111$D112@C12$D121@C13,P2@C21,P3，其中P代表省，C代表市，D代表区县
     * 格式 @前面是省，@后面是市，$后面是区县
     */
    @FieldRenderer("收件省市区")
    private String receiverArea;

    /**
     * 收件的街道
     */
    @FieldRenderer(value = "收件街道" )
    private String receiverStreets;
    /**
     * 收件地址类型
     * 0 城区地址
     * 1 非城区地址（详细地址中包含镇、乡、村、左旗、右旗等关键字，因此可能存在误差）
     */
    @FieldRenderer(value="收件地址类型", rendererClass = AddressTypeRenderer.class)
    private Integer addressType;
    /**
     * 留言备注标注
     * 0 无买家留言和卖家备注
     * 1 有买家留言
     * 2 有卖家备注
     * 3 有买家留言或卖家备注
     * 4 有未处理留言或备注
     */
    @FieldRenderer(value="留言备注标注", rendererClass = MemoQueryRenderer.class)
    private Integer containMemo;

    @FieldRenderer(value = "是否有买家留言",rendererClass = Int2BooleanRenderer.class)
    private Integer hasBuyerMessage;

    @FieldRenderer("买家留言")
    private String buyerMessage;

    @FieldRenderer(value="是否有卖家备注",rendererClass = Int2BooleanRenderer.class)
    private Integer hasSellerMemo;

    @FieldRenderer("卖家备注")
    private String sellerMemo;

    @FieldRenderer(value = "是否有系统备注",rendererClass = Int2BooleanRenderer.class)
    private Integer hasSysMemo;

    @FieldRenderer("系统备注")
    private String sysMemo;

    @FieldRenderer(value="是否有系统备注",rendererClass = Int2BooleanRenderer.class)
    private Integer hasMemo;

    @FieldRenderer("便签")
    private String memos;


    @FieldRenderer("留言关键字")
    private String memoKeyWord;

    /**
     * 卖家备注是否有修改
     * 空值：查询全部
     * 0：无修改
     * 1：有修改
     */
    @FieldRenderer(value="卖家备注是否有修改",rendererClass = Int2BooleanRenderer.class)
    private Integer sellerMemoUpdate;

    /**
     * 卖家旗帜, 红、黄、绿、蓝、紫 分别对应 1、2、3、4、5，0表示灰色，也就是未设置旗帜
     */
    @FieldRenderer("卖家旗帜")
    private String sellerFlag;
    /**
     * 快递是否可达标志: 0 不可达，1 可达
     */
    @FieldRenderer(value="快递是否可达",rendererClass = Int2BooleanRenderer.class)
    private Integer canDelivered;
    /**
     * 快递模版ID，多个以逗号分隔
     * 格式：templateId_templateType,templateId_templateType
     * 其中：templateType 0 普通模版，1 电子模版，当是普通模版时_templateType可以省略
     */
    @FieldRenderer("快递模版")
    private String express;
    /**
     * 查询待包装、待称重、待发货订单时的系统订单号或快递单号
     */
    @FieldRenderer("待包装/待称重/待发货订单的系统单号或快递单号")
    private String mixKey;

    /**
     * 三合一备注： 买家，卖家，系统
     */
    @FieldRenderer("三合一备注")
    private String mixMemo;

    /**
     * 查询待发货订单时指定的发货方式，相关枚举值参见SendGoodTag.TYPE
     */
    @FieldRenderer(value="发货方式")
    private String consignType;
    /**
     * 商品数量最小值
     */
    @FieldRenderer(value="商品数量最小值")
    private Integer itemCountStart;
    /**
     * 商品数量最大值
     */
    @FieldRenderer(value="商品数量最大值")
    private Integer itemCountEnd;
    /**
     * 商品种类数量最小值
     */
    @FieldRenderer(value="商品种类最小值")
    private Integer itemKindStart;
    /**
     * 商品种类数量最大值
     */
    @FieldRenderer(value="商品种类最大值")
    private Integer itemKindEnd;
    /**
     * 订单净重最小值
     */
    @FieldRenderer(value="订单净重最小值")
    private String netWeightStart;
    /**
     * 订单净重最大值
     */
    @FieldRenderer(value="订单净重最大值")
    private String netWeightEnd;


    /**
     * 包裹重量最小值
     */
    @FieldRenderer(value="包裹重量最小值")
    private String weightStart;
    /**
     * 包裹重量最大值
     */
    @FieldRenderer(value="包裹重量最大值")
    private String weightEnd;
    /**
     * 系统订单商品规格名称
     */
    @FieldRenderer(value="系统规格名称")
    private String skuProp;
    /**
     * 平台订单商品规格名称
     */
    @FieldRenderer(value="平台规格名称")
    private String platSkuProp;
    /**
     * 系统商家编码,多个以逗号分隔
     */
    @FieldRenderer(value="系统商家编码")
    private String outerId;
    /**
     * 需要排除的商品商家编码
     */
    @FieldRenderer(value="排除系统商家编码")
    private String excludeSysOuterIds;
    /**
     * outerId和系统商品id的json体,多个以逗号分隔
     */
    @FieldRenderer(value="outerId/系统商品id")
    private String outerIdAndSysItemIds;
    /**
     * outerId和系统商品sku的json体,多个以逗号分隔
     */
    @FieldRenderer(value="outerId/系统商品sku")
    private String outerIdAndSysSkuIds;
    /**
     * 需要排除的outerId和商品id的json体
     */
    @FieldRenderer(value="排除outerId/系统商品id")
    private String excludeOuterIdAndSysItemIds;
    /**
     * 需要排除的outerId和商品sku的json体
     */
    @FieldRenderer(value="排除outerId/系统商品sku")
    private String excludeOuterIdAndSysSkuIds;

    /**
     * 仅包含 outerId和系统商品id的json体,多个以逗号分隔
     */
    @FieldRenderer(value="仅包含outerId/系统商品id")
    private String onlyOuterIdAndSysItemIds;
    /**
     * 仅包含 outerId和系统商品sku的json体,多个以逗号分隔
     */
    @FieldRenderer(value="仅包含outerId/系统商品sku")
    private String onlyOuterIdAndSysSkuIds;

    /**
     *  0仅包含-所有，1仅包含-任意，2同时包含
     *  兼容原来的值，默认不传也是仅包含-所有，也就是0
     *
     */
    @FieldRenderer(value="仅包含类型",rendererClass = OnlyTypeRenderer.class)
    private Integer onlyOuterIdAndSysSkuIdType;

    /**
     * 规格商家编码
     */
    private String skuOuterId;

    public Integer getOnlyOuterIdAndSysSkuIdType() {
        return onlyOuterIdAndSysSkuIdType;
    }

    public void setOnlyOuterIdAndSysSkuIdType(Integer onlyOuterIdAndSysSkuIdType) {
        this.onlyOuterIdAndSysSkuIdType = onlyOuterIdAndSysSkuIdType;
    }

    public String getReceiverStreets() {
        return receiverStreets;
    }

    public void setReceiverStreets(String receiverStreets) {
        this.receiverStreets = receiverStreets;
    }

    public String getStaffIds() {
        return staffIds;
    }

    public void setStaffIds(String staffIds) {
        this.staffIds = staffIds;
    }

    public String getOnlyOuterIdAndSysItemIds() {
        return onlyOuterIdAndSysItemIds;
    }

    public void setOnlyOuterIdAndSysItemIds(String onlyOuterIdAndSysItemIds) {
        this.onlyOuterIdAndSysItemIds = onlyOuterIdAndSysItemIds;
    }

    public String getOnlyOuterIdAndSysSkuIds() {
        return onlyOuterIdAndSysSkuIds;
    }

    public void setOnlyOuterIdAndSysSkuIds(String onlyOuterIdAndSysSkuIds) {
        this.onlyOuterIdAndSysSkuIds = onlyOuterIdAndSysSkuIds;
    }

    public Boolean getIfCustom() {
        return ifCustom;
    }

    public void setIfCustom(Boolean ifCustom) {
        this.ifCustom = ifCustom;
    }

    /**
     * 订单商品排序
     * single: 订单商品种类最少的排在前面，并且按照订单第一个商品的系统商家编码进行排序；
     * multi: 订单商品种类最多的排在前面
     */
    @FieldRenderer("商品排序")
    private String itemOrder;
    /**
     * 是否高亮显示搜索结果
     */
    @FieldRenderer(ignore = true)
    private Boolean highlight;


    /**
     * 根据哪个字段查询
     */
    @FieldRenderer("查询字段")
    private String key;
    /**
     * 查询的字段的值
     */
    @FieldRenderer("查询字段值")
    private String text;
    /**
     * 系统商品类目ID
     */
    @FieldRenderer("商品类目")
    private Long cid;
    /**
     * text字段搜索方式，1 精准搜索，  其它 模糊搜索,默认模糊搜索
     */
    @FieldRenderer(value = "查询字段方式",rendererClass = QueryTypeRenderer.class)
    private Integer queryType = 0;

    /**
     * 直接根据sid/tid搜索  0：sid  1:tid
     */
    private Integer directQueryType;

    /**
     * 当key字段对应传入outerId时，启用该字段，0 查询所以，1 仅查询包含单品的订单，2 仅查询包含套件商品订单
     */
    @FieldRenderer(value = "outerId查询方式",rendererClass = OuterIdTypeRenderer.class)
    private Integer itemType = 0;

    @FieldRenderer(value = "按照套件明细统计")
    private Integer miniHotItem;

    @FieldRenderer(value = "爆款打印查询方式")
    private Integer hotItemType;

    /**
     * 是否查询子订单
     */
    @FieldRenderer(value = "查询子订单",rendererClass = Int2BooleanRenderer.class)
    private Integer queryOrder;

    /**
     * 是否有发票
     */
    @FieldRenderer(value = "是否有发票",rendererClass = Int2BooleanRenderer.class)
    private Integer hasInvoice;

    /**
     * 承诺时间在多久之前,-99999999表示无承诺时间（承诺时间为空）
     */
    @FieldRenderer(value = "承诺时间在多久之前")
    private Integer timeoutActionTimeBefore;

    /**
     * 承诺时间在多久之后,仅timeoutActionTimeType=0下生效
     */
    private Integer timeoutActionTimeAfter;

    /**
     * 承诺时间查询Type 0：默认，1-过滤已超时，2-已超时
     */
    private Integer timeoutActionTimeType;
    /**
     * 缺货比例,多个值以逗号分割 0 0%, 1 100%, 2 0%~100%
     */
    @FieldRenderer(value = "缺货比例")
    private String insufficientRate;
    /**
     * 缺货数量下限
     */
    @FieldRenderer(value = "缺货数量下限")
    private Integer insufficientNumStart;
    /**
     * 缺货数量上线
     */
    @FieldRenderer(value = "缺货数量上限")
    private Integer insufficientNumEnd;
    /**
     * 订单实付查询上限值
     */
    @FieldRenderer(value = "订单实付上限")
    private String paymentUpperLimit;
    /**
     * 订单实付查询下限值
     */
    @FieldRenderer(value = "订单实付下限")
    private String paymentLowerLimit;
    /**
     * 运费金额上限值
     */
    @FieldRenderer(value = "运费上限")
    private String postFeeUpperLimit;
    /**
     * 运费金额下限值
     */
    @FieldRenderer(value = "运费下限")
    private String postFeeLowerLimit;

    /**
     * 理论运费金额上限值
     */
    @FieldRenderer(value = "理论运费上限")
    private String theoryPostFeeUpperLimit;
    /**
     * 理论运费金额下限值
     */
    @FieldRenderer(value = "理论运费下限")
    private String theoryPostFeeLowerLimit;

    /**
     * 需要查询返回前端的字段
     */
    @FieldRenderer(value = "需要查询字段",ignore = true)
    private String selectionFields;


    /**
     * 是否过滤异常订单 0为非异常，1为异常，null为全部
     */
    @FieldRenderer(value = "异常过滤",rendererClass = ExcepTypeRenderer.class)
    private Integer isExcep;

    /**
     * 是否是 EPR 发货，1：ERP 发货， 0：非 ERP 发货
     * 具体参考 http://doc.raycloud.com/pages/viewpage.action?pageId=30015960
     */
    @FieldRenderer(value = "EPR发货",rendererClass = Int2BooleanRenderer.class)
    private Integer isSysConsigned;

    public Integer getTimeoutActionTimeType() {
        return timeoutActionTimeType;
    }

    public void setTimeoutActionTimeType(Integer timeoutActionTimeType) {
        this.timeoutActionTimeType = timeoutActionTimeType;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public void setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
    }

    /**
     * 同时包含标签id，以,拼接
     */
    private String onlyTagIds;
    /**
     * 标签id，以,拼接
     */
    @FieldRenderer(value = "标签id")
    private String tagIds;

    /**
     * 排除标签
     */
    @FieldRenderer(value = "排除标签")
    private String excludeTagIds;
    /**
     * 排除旗帜
     */
    @FieldRenderer(value = "排除旗帜")
    private String excludeSellerFlags;
    /**
     * 自定义异常ID,多个以逗号分割
     */
    @FieldRenderer(value = "自定义异常")
    private String exceptIds;
    /**
     * 排除自定义异常ID,多个以逗号分割
     */
    @FieldRenderer(value = "排除自定义异常ID")
    private String excludeExceptIds;

    /**
     * 转异常后，已审核的订单自动反审核
     */
    @FieldRenderer(value = "转异常后自动反审核")
    private Boolean autoUnaudit;

    /**
     * 是否在波次中
     */
    @FieldRenderer(value = "是否在波次中")
    private Boolean inWave;

    /**
     * 波次号
     */
    @FieldRenderer(value = "波次号")
    private Long waveId;
    /**
     * 波次短号
     */
    @FieldRenderer(value = "波次短号")
    private Long waveShortId;

    /**
     * 订单来源
     */
    @FieldRenderer(value = "订单来源")
    private String tradeFrom;

    @FieldRenderer(value = "已分配运单号")
    private Boolean containOutSid;

    /**
     * 付款天数
     */
    @FieldRenderer(value = "付款天数最小值")
    private Integer daysOfPaymentStart;

    @FieldRenderer(value = "付款天数最大值")
    private Integer daysOfPaymentEnd;

    /**
     * 下单天数
     */
    @FieldRenderer(value = "下单天数最小值")
    private Integer daysOfCreateStart;

    @FieldRenderer(value = "下单天数最大值")
    private Integer daysOfCreateEnd;

    /**
     * 勾选条件 1-付款天数 2-下单天数
     */
    @FieldRenderer(value = "天数类型",rendererClass = DaySelectTypeRenderer.class)
    private Integer daysSelection;


    public String getExcludeTagIds() {
        return excludeTagIds;
    }

    public void setExcludeTagIds(String excludeTagIds) {
        this.excludeTagIds = excludeTagIds;
    }


    /**
     * 快递单打印状态，0未打印，1已打印
     */
    @FieldRenderer(value = "快递单是否已打印",rendererClass = Int2BooleanRenderer.class)
    private Integer expressStatus;
    /**
     * 发货单打印状态，0未打印，1已打印
     */
    @FieldRenderer(value = "发货单是否已打印",rendererClass = Int2BooleanRenderer.class)
    private Integer deliverStatus;

    /**
     * 承诺发货时间
     */
    @FieldRenderer(value = "承诺发货时间")
    private String deliveryTime;

    /**
     * 承诺到达时间
     */
    @FieldRenderer(value = "承诺到达时间")
    private String signTime;

    @FieldRenderer(value = "商品标题")
    private String itemTitle;


    /**
     * 1：仅包含，2：排除，3：同时包含； 4：包含
     */
    @FieldRenderer(value = "异常条件",rendererClass = ExcepContainsTypeRenderer.class)
    private Integer onlyContain;

    /**
     * 仅做前端订单查询、订单操作查询：
     * 是否勾选无异常订单    0.未勾选   1.勾选
     */
    @FieldRenderer(value = "是否勾选无异常订单",rendererClass = Int2BooleanRenderer.class)
    private Integer tickExcep = 0;

    public Integer getTickExcep() {
        return tickExcep;
    }

    public void setTickExcep(Integer tickExcep) {
        this.tickExcep = tickExcep;
    }

    /**
     * 快递公司编号：格式 id,id,id,  0:无需物流发货，-2 未设置快递
     */
    @FieldRenderer(value = "快递公司编号",rendererClass = ExpressCompanyRenderer.class)
    private String expressCompanyIds;

    /**
     * 指定查询trade表，1：是 other：否
     */
    @FieldRenderer(value = "指定查询trade表",rendererClass = Int2BooleanRenderer.class)
    private Integer assignTrade;

    @FieldRenderer(value = "订单来源")
    private String source;

    /**
     * 订单分配的库区类型
     */
    @FieldRenderer(value = "库区类型")
    private String stockRegionTypeStr;

    /**
     * 打印次数最小值
     */
    @FieldRenderer(value = "打印次数最小值")
    private Integer printCountStart;

    /**
     * 打印次数最大值
     */
    @FieldRenderer(value = "打印次数最大值")
    private Integer printCountEnd;

    /**
     * 合单数最小值
     */
    @FieldRenderer(value = "合单数最小值")
    private Integer mergeNumStart;

    /**
     * 合单数最大值
     */
    @FieldRenderer(value = "合单数最大值")
    private Integer mergeNumEnd;

    public Integer getMergeNumStart() {
        return mergeNumStart;
    }

    public void setMergeNumStart(Integer mergeNumStart) {
        this.mergeNumStart = mergeNumStart;
    }

    public Integer getMergeNumEnd() {
        return mergeNumEnd;
    }

    public void setMergeNumEnd(Integer mergeNumEnd) {
        this.mergeNumEnd = mergeNumEnd;
    }

    /**
     * 平台商品标题
     */
    @FieldRenderer(value = "平台商品标题")
    private String platFormTitle;

    /**
     * 忽略订单暂存时间 默认为true
     */
    @FieldRenderer(value = "忽略暂存时间")
    private boolean ignoreBeforeDate = true;
    /**
     * 查询订单包含缺货订单，默认false
     */
    @FieldRenderer(value = "包含缺货订单")
    private boolean containInsufficient = false;

    /**
     *  0:智能合并所有符合条件的订单 1:智能合并搜索结果中符合条件的订单
     */
    @FieldRenderer(value = "智能合单",rendererClass = ManualAutoMergeRenderer.class)
    private Integer manualAutoMergeQueryAll;

    /**
     * 物流异常类型
     * @return
     */
    @FieldRenderer(value = "物流异常类型")
    private String logisticsExceptType;
    /**
     * 关注状态： 1已关注  0未关注
     * @return
     */
    @FieldRenderer(value = "是否已关注",rendererClass = Int2BooleanRenderer.class)
    private String needTracking;

    @FieldRenderer(value = "是否指定包材",rendererClass = Int2BooleanRenderer.class)
    private Integer wrapperDescriptionFlag;

    /**
     * 已付款时间，在这个时间后面的订单不被查询出来
     */
    @FieldRenderer(value = "已付款时长",ignoreValue = "0")
    private Integer minutesAfterPaidOrderAreNotDisplayed;

    /**
     * 排除商品类型
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     */
    @FieldRenderer(value = "排除商品类型",rendererClass = ExcludeOrderTypeRenderer.class)
    private String excludeOrderTypes;

    /**
     * 是否查询order信息 匹配日志记录
     */
    @FieldRenderer(value = "是否查询order信息",ignore = true)
    private Boolean queryOrders;
    /**
     * 订单号搜索 支持多个
     */
    @FieldRenderer(value = "系统单号")
    private String sids;
    /**
     * 规格商家编码搜索 支持多个
     */
    @FieldRenderer(value = "规格商家编码")
    private String outerSkuIds;
    /**
     * 模版搜索 支持多个
     */
    @FieldRenderer(value = "模版")
    private String templateIds;
    /**
     * 员工编号搜索 支持多个
     */
    @FieldRenderer(value = "员工编号")
    private String staffIds;
    private String frontOutSids;
    private String frontTemplateId;

    @FieldRenderer(value = "异常Id")
    private String frontExceptIds;
    private String frontRemark;
    private Integer frontCoverRemark;
    private String ip;
    private List<Order> orders;


    @FieldRenderer(value = "大包组包Id")
    private Long combineParcelId;

    /**
     * 供应商ids
     */
    @FieldRenderer(value = "供应商Id")
    private String supplierIds;

    /**
     * 优惠金额上限值
     */
    @FieldRenderer(value = "优惠金额上限")
    private String discountFeeUpperLimit;
    /**
     * 优惠金额下限值
     */
    @FieldRenderer(value = "优惠金额下限")
    private String discountFeeLowerLimit;
    /**
     * 订单唯一码查询
     */
    @FieldRenderer(value = "唯一码")
    private String uniqueCodes;

    /**
     * 放心购商家代发店铺id
     * 对应trade_ext mallMaskId
     */
    @FieldRenderer(value = "代发店铺id")
    private String fxgDfMallMaskIds;

    /**
     * 毛利润区间
     */
    @FieldRenderer(value = "毛利润下限")
    private Double minGrossProfit;
    @FieldRenderer(value = "毛利润上限")
    private Double maxGrossProfit;

    /**
     * 根据上传异常类型查询订单，多个以逗号隔开
     */
    @FieldRenderer(value = "上传异常类型")
    private String uploadErrorType;


    @FieldRenderer(ignore = true)
    private Integer useHasNext;

    public Integer getUseHasNext() {
        return useHasNext;
    }

    public void setUseHasNext(Integer useHasNext) {
        this.useHasNext = useHasNext;
    }

    @FieldRenderer(ignore = true)
    private  Integer queryFlag;

    public Integer getQueryFlag() {
        return queryFlag;
    }

    public void setQueryFlag(Integer queryFlag) {
        this.queryFlag = queryFlag;
    }
    private String sortConfig;


    public String getSortConfig() {
        return sortConfig;
    }

    public void setSortConfig(String sortConfig) {
        this.sortConfig = sortConfig;
    }

    public String getDiscountFeeUpperLimit() {
        return discountFeeUpperLimit;
    }

    public void setDiscountFeeUpperLimit(String discountFeeUpperLimit) {
        this.discountFeeUpperLimit = discountFeeUpperLimit;
    }

    public String getDiscountFeeLowerLimit() {
        return discountFeeLowerLimit;
    }

    public void setDiscountFeeLowerLimit(String discountFeeLowerLimit) {
        this.discountFeeLowerLimit = discountFeeLowerLimit;
    }
    //转异常前是否先取消异常 0：否；1：是
    private String cancelExceptionBeforeTurningException;

    //转异常前需要取消的异常
    private String cancelExceptionIds;

    public String getCancelExceptionBeforeTurningException() {
        return cancelExceptionBeforeTurningException;
    }

    public void setCancelExceptionBeforeTurningException(String cancelExceptionBeforeTurningException) {
        this.cancelExceptionBeforeTurningException = cancelExceptionBeforeTurningException;
    }

    public String getCancelExceptionIds() {
        return cancelExceptionIds;
    }

    public void setCancelExceptionIds(String cancelExceptionIds) {
        this.cancelExceptionIds = cancelExceptionIds;
    }


    public String getExcludeOrderTypes() {
        return excludeOrderTypes;
    }

    public void setExcludeOrderTypes(String excludeOrderTypes) {
        this.excludeOrderTypes = excludeOrderTypes;
    }


    /**
     * 前端传的主商家编码字段
     */
    @FieldRenderer(value = "主商家编码")
    private String mainOuterId;
    /**
     * 针对淘宝订单的收件人查询和手机号查询
     */
    @FieldRenderer(ignore = true)
    private Integer queryPlatform = 0;

    /**
     * 唯一码(关联查询拆分订单)
     */
    @FieldRenderer(value = "唯一码(关联拆单)")
    private String uniqueCodeSplitQuery;

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }


    @FieldRenderer(value = "物流编号")
    private String logisticsCode;

    /**
     * 预售订单解锁范围
     * QUERY_ORDER：处理查询结果
     * ALL_ORDER：处理所有未解锁的预售订单
     */
    @FieldRenderer(value = "预售订单解锁范围")
    private UnlockPresellTradeRangeEnum unlockPresellTradeRange;

    /**
     * 预售订单解锁条件
     * <p>
     * <p>
     * ("SATISFY_STOCK_UNLOCK", "忽略计划发货时间，预售商品满足库存就解锁")
     * ("IGNORE_STOCK_DELIVERY_TIME_UNLOCK", "忽略库存，预售商品计划发货时间到期就解锁")
     * ("SATISFY_STOCK_DELIVERY_TIME_UNLOCK", "预售商品满足库存且假话发货时间到期解锁")
     */
    @FieldRenderer(value = "预售订单解锁条件")
    private UnlockPresellConditionEnum unlockPresellCondition;

    /**
     * 预售订单解锁顺序
     * ("DELIVERY_TIME_AND_PAY_TIME", "先按计划时间发货排序，计划发货时间相同再按付款时间依次解锁")
     * ("PAY_TIME", "仅按付款时间依次解锁")
     */
    @FieldRenderer(value = "预售订单解锁顺序")
    private UnlockPresellSortEnum unlockPresellSort;

    /**
     * 解锁拆分
     * ("SPLIT_INCOMPATIBLE_ORDER_AND_UNLOCK_SATISFY_ORDER", "拆分不符解锁条件的商品，仅解锁符合条件的商品"),
     * ("SPLIT_INCOMPATIBLE_ORDER_AND_LOCK_ORDER", "拆分不符解锁条件的商品，所有商品均不执行解锁"),
     * ("UNSPLIT_AND_LOCK_AND_APPLY_STOCK", "不拆分，不解锁，但在智能解锁时为不满的解锁条件的商品预留库存"),
     */
    @FieldRenderer(value = "预售订单解锁拆分")
    private UnlockPresellSplitEnum unlockPresellSplit;

    /**
     * 库存扣除
     * ("INCLUDE_NORMAL_ORDER_STOCK", "库存不会扣除未审核的正常订单占用"),
     * ("EXCLUDE_NORMAL_ORDER_STOCK", "库存扣除未审核的正常订单占用后再给预售订单解锁"),
     */
    @FieldRenderer(value = "预售订单库存扣除")
    private UnlockPresellStockEnum unlockPresellStock;

    /**
     * 剩余时间
     */
    @FieldRenderer(value = "剩余时间")
    private Integer remainTimeHours;

    /**
     *  0:执行所有已开启的标记规则，1:执行指定标记的规则
     */
    private Integer matchExcepWay;
    /**
     *  规则Id,matchExcepWay 为1时，不能为空
     */
    private String ruleIds;

    private String itemTagIdsParamsStr;

    /**
     * KMERP-117313: 反审核的订单有已经生成唯一码的，需要二次强提醒
     *  0 - 订单数据唯一码校验
     *  1 - 忽略已生成唯一码订单，继续执行反审核
     *  2 - 全部订单继续执行反审核
     */
    private Integer unAuditUniqueCodeTradeExecType;

    private String uploadParams;

    private Integer needDummyUpload;

    private String tradeSource;

    private Integer progressType;
    /**
     * 智能分仓勾选类型
     * splitWarehouseType
     * 1 （按规则分仓
     * 0  （按库存分仓，整单库存不满足自动拆单
     * 2 （ 按库存分仓，整单库存不满足，不拆单
     * 3  （先规则后库存, 拆单
     * 4  （先规则后库存，不拆单
     */
    @FieldRenderer(value = "智能分仓类型",rendererClass = SplitWarehouseTypeRenderer.class)
    private Integer splitWarehouseType = -1;

    public String getCopyUserId() {
        return copyUserId;
    }

    public void setCopyUserId(String copyUserId) {
        this.copyUserId = copyUserId;
    }

    /**
     * 订单复制新建的userId
     */
    private String copyUserId;

    /**
     * 是否作废订单 0未作废, 1已作废
     */
    private Integer isCancel;

    public Integer getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
    }
    // 查询待包装订单使用
    private Integer allowUnPrintPack;

    public Integer getAllowUnPrintPack() {
        return allowUnPrintPack;
    }

    public void setAllowUnPrintPack(Integer allowUnPrintPack) {
        this.allowUnPrintPack = allowUnPrintPack;
    }

    public Integer getSplitWarehouseType() {
        return splitWarehouseType;
    }

    public void setSplitWarehouseType(Integer splitWarehouseType) {
        this.splitWarehouseType = splitWarehouseType;
    }

    public String getTemplateIds() {
        return templateIds;
    }

    public void setTemplateIds(String templateIds) {
        this.templateIds = templateIds;
    }

    public UnlockPresellStockEnum getUnlockPresellStock() {
        return unlockPresellStock;
    }

    public void setUnlockPresellStock(UnlockPresellStockEnum unlockPresellStock) {
        this.unlockPresellStock = unlockPresellStock;
    }

    public UnlockPresellSplitEnum getUnlockPresellSplit() {
        return unlockPresellSplit;
    }

    public void setUnlockPresellSplit(UnlockPresellSplitEnum unlockPresellSplit) {
        this.unlockPresellSplit = unlockPresellSplit;
    }

    public UnlockPresellTradeRangeEnum getUnlockPresellTradeRange() {
        return unlockPresellTradeRange;
    }

    public void setUnlockPresellTradeRange(UnlockPresellTradeRangeEnum unlockPresellTradeRange) {
        this.unlockPresellTradeRange = unlockPresellTradeRange;
    }

    public UnlockPresellConditionEnum getUnlockPresellCondition() {
        return unlockPresellCondition;
    }

    public void setUnlockPresellCondition(UnlockPresellConditionEnum unlockPresellCondition) {
        this.unlockPresellCondition = unlockPresellCondition;
    }

    public UnlockPresellSortEnum getUnlockPresellSort() {
        return unlockPresellSort;
    }

    public void setUnlockPresellSort(UnlockPresellSortEnum unlockPresellSort) {
        this.unlockPresellSort = unlockPresellSort;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }

    public String getFrontOutSids() {
        return frontOutSids;
    }

    public void setFrontOutSids(String frontOutSids) {
        this.frontOutSids = frontOutSids;
    }

    public String getFrontTemplateId() {
        return frontTemplateId;
    }

    public void setFrontTemplateId(String frontTemplateId) {
        this.frontTemplateId = frontTemplateId;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public String getFrontExceptIds() {
        return frontExceptIds;
    }

    public void setFrontExceptIds(String frontExceptIds) {
        this.frontExceptIds = frontExceptIds;
    }

    public String getFrontRemark() {
        return frontRemark;
    }

    public void setFrontRemark(String frontRemark) {
        this.frontRemark = frontRemark;
    }

    public Integer getFrontCoverRemark() {
        return frontCoverRemark;
    }

    public void setFrontCoverRemark(Integer frontCoverRemark) {
        this.frontCoverRemark = frontCoverRemark;
    }

    /**
     * 0代表页面勾选勾选订单
     * 1代表查询结果
     */
    private String userSearch;

    public String getUserSearch() {
        return userSearch;
    }

    public void setUserSearch(String userSearch) {
        this.userSearch = userSearch;
    }

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public Integer getAssignTrade() {
        return assignTrade;
    }

    public void setAssignTrade(Integer assignTrade) {
        this.assignTrade = assignTrade;
    }

    public String getExpressCompanyIds() {
        return expressCompanyIds;
    }

    public void setExpressCompanyIds(String expressCompanyIds) {
        this.expressCompanyIds = expressCompanyIds;
    }



    /**
     * 订单来源于哪里 分销系统里记为分销商id（companyId）
     */
    @FieldRenderer(value = "供分销来源")
    private String sourceId;

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    @FieldRenderer(value = "供分销归属")
    private String destId;


    private String ifChangeTradeDmsAttr;

    /**
     * 交易列表查询-商品属性
     */
    @FieldRenderer(value = "商品属性")
    private String itemPopsStr;

    /**
     * 附加条件： 商品包含无需发货
     */
    private Integer itemContainNonConsign;

    /**
     * 是否是运单号较多的批量导出。如果是则为1
     * */
    private Integer batch;

    /**
     * 快递公司Ids ,拼接
     */
    private String logisticsCompanyIds;

    /**
     * 订单所属平台,多个用逗号分割
     */
    private String subSource;

    /**
     * 排除订单来源,多个用逗号分割
     */
    private String excludeSource;

    /**
     * 批次打印记录序号,起始序号
     */
    private Integer startIndex;

    /**
     * 批次打印记录序号,结束序号
     */
    private Integer endIndex;


    @FieldRenderer(value = "是否继续查询")
    private Boolean isReacquire = false;

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    /**
     * 卖家昵称
     */
    private String sellerNicks;

    public String getSellerNicks() {
        return sellerNicks;
    }

    public void setSellerNicks(String sellerNicks) {
        this.sellerNicks = sellerNicks;
    }

    /**
     * 二级店铺平台来源对应trade#subSource,用于前端区分查询来源
     */
    private String subSources;

    private String sensitive;

    public String getSensitive() {
        return sensitive;
    }

    public void setSensitive(String sensitive) {
        this.sensitive = sensitive;
    }

    public String getSubSources() {
        return subSources;
    }

    public void setSubSources(String subSources) {
        this.subSources = subSources;
    }


    /**
     * 二级店铺id
     */
    private String secondUserId;

    public String getSecondUserId() {
        return secondUserId;
    }

    public void setSecondUserId(String secondUserId) {
        this.secondUserId = secondUserId;
    }


    public String getOrderRefundStatus() {
        return orderRefundStatus;
    }

    public void setOrderRefundStatus(String orderRefundStatus) {
        this.orderRefundStatus = orderRefundStatus;
    }

    /**
     * 商品退款状态
     */
    private String orderRefundStatus;

    /**
     * 是否需要脱敏 空或者true 需要
     */
    @FieldRenderer(value = "是否需要脱敏")
    private Boolean ifSensitive;

    public Boolean getIfSensitive() {
        return ifSensitive;
    }

    public void setIfSensitive(Boolean ifSensitive) {
        this.ifSensitive = ifSensitive;
    }

    public Integer getBatch() {
        return batch;
    }

    public void setBatch(Integer batch) {
        this.batch = batch;
    }

    public String getItemPopsStr() {
        return itemPopsStr;
    }

    public void setItemPopsStr(String itemPopsStr) {
        this.itemPopsStr = itemPopsStr;
    }

    public Integer getItemContainNonConsign() {
        return itemContainNonConsign;
    }

    public void setItemContainNonConsign(Integer itemContainNonConsign) {
        this.itemContainNonConsign = itemContainNonConsign;
    }

    public String getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(String taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getDestId() {
        return destId;
    }

    public void setDestId(String destId) {
        this.destId = destId;
    }

    public Integer getOnlyContain() {
        return onlyContain;
    }

    public void setOnlyContain(Integer onlyContain) {
        this.onlyContain = onlyContain;
    }

    public String getExcludeSellerFlags() {
        return excludeSellerFlags;
    }

    public void setExcludeSellerFlags(String excludeSellerFlags) {
        this.excludeSellerFlags = excludeSellerFlags;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getSelectionFields() {
        return selectionFields;
    }

    public void setSelectionFields(String selectionFields) {
        this.selectionFields = selectionFields;
    }

    public Long getCustomId() {
        return customId;
    }

    public void setCustomId(Long customId) {
        this.customId = customId;
    }

    public Long getQueryId() {
        return queryId;
    }

    public void setQueryId(Long queryId) {
        this.queryId = queryId;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getShortId() {
        return shortId;
    }

    public void setShortId(String shortId) {
        this.shortId = shortId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getOpenUid() {
        return openUid;
    }

    public void setOpenUid(String openUid) {
        this.openUid = openUid;
    }

    public String getCustomerNick() {
        return customerNick;
    }

    public void setCustomerNick(String customerNick) {
        this.customerNick = customerNick;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getExcludeTradeType() {
        return excludeTradeType;
    }

    public void setExcludeTradeType(String excludeTradeType) {
        this.excludeTradeType = excludeTradeType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getExceptionStatus() {
        return exceptionStatus;
    }

    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }


    public String getO2oSysStatus() {
        return o2oSysStatus;
    }

    public void setO2oSysStatus(String o2oSysStatus) {
        this.o2oSysStatus = o2oSysStatus;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPlatformStatus() {
        return platformStatus;
    }

    public void setPlatformStatus(String platformStatus) {
        this.platformStatus = platformStatus;
    }

    public String getReceiverArea() {
        return receiverArea;
    }

    public void setReceiverArea(String receiverArea) {
        this.receiverArea = receiverArea;
    }

    public Integer getAddressType() {
        return addressType;
    }

    public void setAddressType(Integer addressType) {
        this.addressType = addressType;
    }

    public Integer getContainMemo() {
        return containMemo;
    }

    public void setContainMemo(Integer containMemo) {
        this.containMemo = containMemo;
    }

    public Integer getHasBuyerMessage() {
        return hasBuyerMessage;
    }

    public void setHasBuyerMessage(Integer hasBuyerMessage) {
        this.hasBuyerMessage = hasBuyerMessage;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public Integer getHasSellerMemo() {
        return hasSellerMemo;
    }

    public void setHasSellerMemo(Integer hasSellerMemo) {
        this.hasSellerMemo = hasSellerMemo;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public Integer getSellerMemoUpdate() {
        return sellerMemoUpdate;
    }

    public void setSellerMemoUpdate(Integer sellerMemoUpdate) {
        this.sellerMemoUpdate = sellerMemoUpdate;
    }

    public String getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(String sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public Integer getHasSysMemo() {
        return hasSysMemo;
    }

    public void setHasSysMemo(Integer hasSysMemo) {
        this.hasSysMemo = hasSysMemo;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public Integer getCanDelivered() {
        return canDelivered;
    }

    public void setCanDelivered(Integer canDelivered) {
        this.canDelivered = canDelivered;
    }

    public String getExpress() {
        return express;
    }

    public void setExpress(String express) {
        this.express = express;
    }

    public String getMixKey() {
        return mixKey;
    }

    public void setMixKey(String mixKey) {
        this.mixKey = mixKey;
    }

    public String getMixMemo() {
        return mixMemo;
    }

    public void setMixMemo(String mixMemo) {
        this.mixMemo = mixMemo;
    }

    public String getConsignType() {
        return consignType;
    }

    public void setConsignType(String consignType) {
        this.consignType = consignType;
    }

    public Integer getItemCountStart() {
        return itemCountStart;
    }

    public void setItemCountStart(Integer itemCountStart) {
        this.itemCountStart = itemCountStart;
    }

    public Integer getItemCountEnd() {
        return itemCountEnd;
    }

    public void setItemCountEnd(Integer itemCountEnd) {
        this.itemCountEnd = itemCountEnd;
    }

    public Integer getItemKindStart() {
        return itemKindStart;
    }

    public void setItemKindStart(Integer itemKindStart) {
        this.itemKindStart = itemKindStart;
    }

    public Integer getItemKindEnd() {
        return itemKindEnd;
    }

    public void setItemKindEnd(Integer itemKindEnd) {
        this.itemKindEnd = itemKindEnd;
    }

    public String getNetWeightStart() {
        return netWeightStart;
    }

    public void setNetWeightStart(String netWeightStart) {
        this.netWeightStart = netWeightStart;
    }

    public String getNetWeightEnd() {
        return netWeightEnd;
    }

    public void setNetWeightEnd(String netWeightEnd) {
        this.netWeightEnd = netWeightEnd;
    }

    public String getWeightStart() {
        return weightStart;
    }

    public void setWeightStart(String weightStart) {
        this.weightStart = weightStart;
    }

    public String getWeightEnd() {
        return weightEnd;
    }

    public void setWeightEnd(String weightEnd) {
        this.weightEnd = weightEnd;
    }

    public String getSkuProp() {
        return skuProp;
    }

    public void setSkuProp(String skuProp) {
        this.skuProp = skuProp;
    }

    public String getPlatSkuProp() {
        return platSkuProp;
    }

    public void setPlatSkuProp(String platSkuProp) {
        this.platSkuProp = platSkuProp;
    }

    public String getItemOrder() {
        return itemOrder;
    }

    public void setItemOrder(String itemOrder) {
        this.itemOrder = itemOrder;
    }

    public Boolean getHighlight() {
        return highlight;
    }

    public void setHighlight(Boolean highlight) {
        this.highlight = highlight;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public Integer getMiniHotItem() {
        return miniHotItem;
    }

    public void setMiniHotItem(Integer miniHotItem) {
        this.miniHotItem = miniHotItem;
    }

    public Integer getHotItemType() {
        return hotItemType;
    }

    public void setHotItemType(Integer hotItemType) {
        this.hotItemType = hotItemType;
    }

    public Integer getQueryOrder() {
        return queryOrder;
    }

    public void setQueryOrder(Integer queryOrder) {
        this.queryOrder = queryOrder;
    }

    public Integer getHasInvoice() {
        return hasInvoice;
    }

    public void setHasInvoice(Integer hasInvoice) {
        this.hasInvoice = hasInvoice;
    }

    public Integer getTimeoutActionTimeBefore() {
        return timeoutActionTimeBefore;
    }

    public void setTimeoutActionTimeBefore(Integer timeoutActionTimeBefore) {
        this.timeoutActionTimeBefore = timeoutActionTimeBefore;
    }

    public String getInsufficientRate() {
        return insufficientRate;
    }

    public void setInsufficientRate(String insufficientRate) {
        this.insufficientRate = insufficientRate;
    }

    public Integer getInsufficientNumStart() {
        return insufficientNumStart;
    }

    public void setInsufficientNumStart(Integer insufficientNumStart) {
        this.insufficientNumStart = insufficientNumStart;
    }

    public Integer getInsufficientNumEnd() {
        return insufficientNumEnd;
    }

    public void setInsufficientNumEnd(Integer insufficientNumEnd) {
        this.insufficientNumEnd = insufficientNumEnd;
    }

    public String getPaymentUpperLimit() {
        return paymentUpperLimit;
    }

    public void setPaymentUpperLimit(String paymentUpperLimit) {
        this.paymentUpperLimit = paymentUpperLimit;
    }

    public String getPaymentLowerLimit() {
        return paymentLowerLimit;
    }

    public void setPaymentLowerLimit(String paymentLowerLimit) {
        this.paymentLowerLimit = paymentLowerLimit;
    }

    public String getPostFeeUpperLimit() {
        return postFeeUpperLimit;
    }

    public void setPostFeeUpperLimit(String postFeeUpperLimit) {
        this.postFeeUpperLimit = postFeeUpperLimit;
    }

    public String getPostFeeLowerLimit() {
        return postFeeLowerLimit;
    }

    public void setPostFeeLowerLimit(String postFeeLowerLimit) {
        this.postFeeLowerLimit = postFeeLowerLimit;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public String getOnlyTagIds() {
        return onlyTagIds;
    }

    public void setOnlyTagIds(String onlyTagIds) {
        this.onlyTagIds = onlyTagIds;
    }

    public String getTaobaoIds() {
        return taobaoIds;
    }

    public void setTaobaoIds(String taobaoIds) {
        this.taobaoIds = taobaoIds;
    }

    public String getExceptIds() {
        return exceptIds;
    }

    public void setExceptIds(String exceptIds) {
        this.exceptIds = exceptIds;
    }

    public Boolean getInWave() {
        return inWave;
    }

    public void setInWave(Boolean inWave) {
        this.inWave = inWave;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Long getWaveShortId() {
        return waveShortId;
    }

    public void setWaveShortId(Long waveShortId) {
        this.waveShortId = waveShortId;
    }

    public String getTradeFrom() {
        return tradeFrom;
    }

    public void setTradeFrom(String tradeFrom) {
        this.tradeFrom = tradeFrom;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getExcludeSysOuterIds() {
        return excludeSysOuterIds;
    }

    public void setExcludeSysOuterIds(String excludeSysOuterIds) {
        this.excludeSysOuterIds = excludeSysOuterIds;
    }

    public String getOuterIdAndSysItemIds() {
        return outerIdAndSysItemIds;
    }

    public void setOuterIdAndSysItemIds(String outerIdAndSysItemIds) {
        this.outerIdAndSysItemIds = outerIdAndSysItemIds;
    }

    public String getOuterIdAndSysSkuIds() {
        return outerIdAndSysSkuIds;
    }

    public void setOuterIdAndSysSkuIds(String outerIdAndSysSkuIds) {
        this.outerIdAndSysSkuIds = outerIdAndSysSkuIds;
    }

    public String getExcludeOuterIdAndSysItemIds() {
        return excludeOuterIdAndSysItemIds;
    }

    public void setExcludeOuterIdAndSysItemIds(String excludeOuterIdAndSysItemIds) {
        this.excludeOuterIdAndSysItemIds = excludeOuterIdAndSysItemIds;
    }

    public String getExcludeOuterIdAndSysSkuIds() {
        return excludeOuterIdAndSysSkuIds;
    }

    public void setExcludeOuterIdAndSysSkuIds(String excludeOuterIdAndSysSkuIds) {
        this.excludeOuterIdAndSysSkuIds = excludeOuterIdAndSysSkuIds;
    }

    public Boolean getContainOutSid() {
        return containOutSid;
    }

    public void setContainOutSid(Boolean containOutSid) {
        this.containOutSid = containOutSid;
    }

    public Integer getDaysOfPaymentStart() {
        return daysOfPaymentStart;
    }

    public void setDaysOfPaymentStart(Integer daysOfPaymentStart) {
        this.daysOfPaymentStart = daysOfPaymentStart;
    }

    public Integer getDaysOfPaymentEnd() {
        return daysOfPaymentEnd;
    }

    public void setDaysOfPaymentEnd(Integer daysOfPaymentEnd) {
        this.daysOfPaymentEnd = daysOfPaymentEnd;
    }

    public Integer getDaysOfCreateStart() {
        return daysOfCreateStart;
    }

    public void setDaysOfCreateStart(Integer daysOfCreateStart) {
        this.daysOfCreateStart = daysOfCreateStart;
    }

    public Integer getDaysOfCreateEnd() {
        return daysOfCreateEnd;
    }

    public void setDaysOfCreateEnd(Integer daysOfCreateEnd) {
        this.daysOfCreateEnd = daysOfCreateEnd;
    }

    public Integer getDaysSelection() {
        return daysSelection;
    }

    public void setDaysSelection(Integer daysSelection) {
        this.daysSelection = daysSelection;
    }

    public Integer getExpressStatus() {
        return expressStatus;
    }

    public void setExpressStatus(Integer expressStatus) {
        this.expressStatus = expressStatus;
    }

    public Integer getDeliverStatus() {
        return deliverStatus;
    }

    public void setDeliverStatus(Integer deliverStatus) {
        this.deliverStatus = deliverStatus;
    }

    public String getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(String deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public String getPlatFormTitle() {
        return platFormTitle;
    }

    public void setPlatFormTitle(String platFormTitle) {
        this.platFormTitle = platFormTitle;
    }

    public Integer getPrintCountStart() {
        return printCountStart;
    }

    public void setPrintCountStart(Integer printCountStart) {
        this.printCountStart = printCountStart;
    }

    public Integer getPrintCountEnd() {
        return printCountEnd;
    }

    public void setPrintCountEnd(Integer printCountEnd) {
        this.printCountEnd = printCountEnd;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getIsSysConsigned() {
        return isSysConsigned;
    }

    public void setIsSysConsigned(Integer isSysConsigned) {
        this.isSysConsigned = isSysConsigned;
    }

    public String getStockRegionTypeStr() {
        return stockRegionTypeStr;
    }

    public void setStockRegionTypeStr(String stockRegionTypeStr) {
        this.stockRegionTypeStr = stockRegionTypeStr;
    }

    public Integer getContainOrExclude() {
        return containOrExclude;
    }

    public void setContainOrExclude(Integer containOrExclude) {
        this.containOrExclude = containOrExclude;
    }

    public boolean isIgnoreBeforeDate() {
        return ignoreBeforeDate;
    }

    public void setIgnoreBeforeDate(boolean ignoreBeforeDate) {
        this.ignoreBeforeDate = ignoreBeforeDate;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getIfGroup() {
        return ifGroup;
    }

    public void setIfGroup(Integer ifGroup) {
        this.ifGroup = ifGroup;
    }

    public Boolean getAutoUnaudit() {
        return autoUnaudit;
    }

    public void setAutoUnaudit(Boolean autoUnaudit) {
        this.autoUnaudit = autoUnaudit;
    }

    public Integer getManualAutoMergeQueryAll() {
        return manualAutoMergeQueryAll;
    }

    public void setManualAutoMergeQueryAll(Integer manualAutoMergeQueryAll) {
        this.manualAutoMergeQueryAll = manualAutoMergeQueryAll;
    }

    public Integer getHasMemo() {
        return hasMemo;
    }

    public void setHasMemo(Integer hasMemo) {
        this.hasMemo = hasMemo;
    }

    public String getMemos() {
        return memos;
    }

    public String getMemoKeyWord() {
        return memoKeyWord;
    }

    public void setMemoKeyWord(String memoKeyWord) {
        this.memoKeyWord = memoKeyWord;
    }

    public void setMemos(String memos) {
        this.memos = memos;
    }

    public boolean isContainInsufficient() {
        return containInsufficient;
    }

    public void setContainInsufficient(boolean containInsufficient) {
        this.containInsufficient = containInsufficient;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public Integer getWrapperDescriptionFlag() {
        return wrapperDescriptionFlag;
    }

    public void setWrapperDescriptionFlag(Integer wrapperDescriptionFlag) {
        this.wrapperDescriptionFlag = wrapperDescriptionFlag;
    }

    public Integer getQueryPlatform() {
        return queryPlatform;
    }

    public void setQueryPlatform(Integer queryPlatform) {
        this.queryPlatform = queryPlatform;
    }

    public String getNeedTracking() {
        return needTracking;
    }

    public void setNeedTracking(String needTracking) {
        this.needTracking = needTracking;
    }

    public Integer getMinutesAfterPaidOrderAreNotDisplayed() {
        return minutesAfterPaidOrderAreNotDisplayed;
    }

    public void setMinutesAfterPaidOrderAreNotDisplayed(Integer minutesAfterPaidOrderAreNotDisplayed) {
        this.minutesAfterPaidOrderAreNotDisplayed = minutesAfterPaidOrderAreNotDisplayed;
    }

    public String getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(String supplierIds) {
        this.supplierIds = supplierIds;
    }


    /**
     * 是否开启转异常前先取消异常
     *
     * @return
     */
    public boolean isCancelExceptionBeforeTurningException() {
        return "1".equals(cancelExceptionBeforeTurningException);
    }

    public String getIfChangeTradeDmsAttr() {
        return ifChangeTradeDmsAttr;
    }

    public void setIfChangeTradeDmsAttr(String ifChangeTradeDmsAttr) {
        this.ifChangeTradeDmsAttr = ifChangeTradeDmsAttr;
    }

    public Boolean getQueryOrders() {
        return queryOrders;
    }

    public void setQueryOrders(Boolean queryOrders) {
        this.queryOrders = queryOrders;
    }

    public String getOuterSkuIds() {
        return outerSkuIds;
    }

    public void setOuterSkuIds(String outerSkuIds) {
        this.outerSkuIds = outerSkuIds;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getUniqueCodes() {
        return uniqueCodes;
    }

    public void setUniqueCodes(String uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    public Integer getRemainTimeHours() {
        return remainTimeHours;
    }

    public void setRemainTimeHours(Integer remainTimeHours) {
        this.remainTimeHours = remainTimeHours;
    }



    public Double getMinGrossProfit() {
        return minGrossProfit;
    }

    public void setMinGrossProfit(Double minGrossProfit) {
        this.minGrossProfit = minGrossProfit;
    }

    public Double getMaxGrossProfit() {
        return maxGrossProfit;
    }

    public void setMaxGrossProfit(Double maxGrossProfit) {
        this.maxGrossProfit = maxGrossProfit;
    }

    public String getUniqueCodeSplitQuery() {
        return uniqueCodeSplitQuery;
    }

    public void setUniqueCodeSplitQuery(String uniqueCodeSplitQuery) {
        this.uniqueCodeSplitQuery = uniqueCodeSplitQuery;
    }

    public String getUploadErrorType() {
        return uploadErrorType;
    }

    public void setUploadErrorType(String uploadErrorType) {
        this.uploadErrorType = uploadErrorType;
    }

    public String getFxgDfMallMaskIds() {
        return fxgDfMallMaskIds;
    }

    public void setFxgDfMallMaskIds(String fxgDfMallMaskIds) {
        this.fxgDfMallMaskIds = fxgDfMallMaskIds;
    }


    public Long getLogisticsWarningStartTime() {
        return logisticsWarningStartTime;
    }

    public void setLogisticsWarningStartTime(Long logisticsWarningStartTime) {
        this.logisticsWarningStartTime = logisticsWarningStartTime;
    }

    public Long getLogisticsWarningEndTime() {
        return logisticsWarningEndTime;
    }

    public void setLogisticsWarningEndTime(Long logisticsWarningEndTime) {
        this.logisticsWarningEndTime = logisticsWarningEndTime;
    }

    public String getExcludeExceptionStatus() {
        return excludeExceptionStatus;
    }

    public void setExcludeExceptionStatus(String excludeExceptionStatus) {
        this.excludeExceptionStatus = excludeExceptionStatus;
    }

    public String getExcludeExceptIds() {
        return excludeExceptIds;
    }

    public void setExcludeExceptIds(String excludeExceptIds) {
        this.excludeExceptIds = excludeExceptIds;
    }
    public Integer getMatchExcepWay() {
        return matchExcepWay;
    }

    public void setMatchExcepWay(Integer matchExcepWay) {
        this.matchExcepWay = matchExcepWay;
    }

    public String getRuleIds() {
        return ruleIds;
    }

    public void setRuleIds(String ruleIds) {
        this.ruleIds = ruleIds;
    }

    public String getTradeTypeNewParamsStr() {
        return tradeTypeNewParamsStr;
    }

    public void setTradeTypeNewParamsStr(String tradeTypeNewParamsStr) {
        this.tradeTypeNewParamsStr = tradeTypeNewParamsStr;
    }


    public String getItemTagIdsParamsStr() {
        return itemTagIdsParamsStr;
    }

    public void setItemTagIdsParamsStr(String itemTagIdsParamsStr) {
        this.itemTagIdsParamsStr = itemTagIdsParamsStr;
    }

    /**
     * 订单智能单品数量下限
     */
    @FieldRenderer(value = "订单智能单品下限")
    private Integer smartItemNumStart;
    /**
     * 订单智能单品数量上线
     */
    @FieldRenderer(value = "订单智能单品上限")
    private Integer smartItemNumEnd;

    public Integer getSmartItemNumStart() {
        return smartItemNumStart;
    }

    public void setSmartItemNumStart(Integer smartItemNumStart) {
        this.smartItemNumStart = smartItemNumStart;
    }

    public Integer getSmartItemNumEnd() {
        return smartItemNumEnd;
    }

    public void setSmartItemNumEnd(Integer smartItemNumEnd) {
        this.smartItemNumEnd = smartItemNumEnd;
    }

    public String getHighlightTid() {
        return highlightTid;
    }

    public void setHighlightTid(String highlightTid) {
        this.highlightTid = highlightTid;
    }

    public Integer getUnAuditUniqueCodeTradeExecType() {
        return unAuditUniqueCodeTradeExecType;
    }

    public void setUnAuditUniqueCodeTradeExecType(Integer unAuditUniqueCodeTradeExecType) {
        this.unAuditUniqueCodeTradeExecType = unAuditUniqueCodeTradeExecType;
    }

    public String getLogisticsCompanyIds() {
        return logisticsCompanyIds;
    }

    public void setLogisticsCompanyIds(String logisticsCompanyIds) {
        this.logisticsCompanyIds = logisticsCompanyIds;
    }

    public Integer getDirectQueryType() {
        return directQueryType;
    }

    public void setDirectQueryType(Integer directQueryType) {
        this.directQueryType = directQueryType;
    }

    public String toPlainString(){
        return FieldRendererUtils.render(this);
    }


    public String getExcludeSource() {
        return excludeSource;
    }

    public void setExcludeSource(String excludeSource) {
        this.excludeSource = excludeSource;
    }

    public Integer getTimeoutActionTimeAfter() {
        return timeoutActionTimeAfter;
    }

    public void setTimeoutActionTimeAfter(Integer timeoutActionTimeAfter) {
        this.timeoutActionTimeAfter = timeoutActionTimeAfter;
    }
    /**
     * 是否已经送打
     * 1-已送打,2-未送打
     * 为空或其他不处理
     */
    private Integer printStatus;


    /**
     * 请求发起的端 PC app
     */
    private String client;

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getEndIndex() {
        return endIndex;
    }

    public void setEndIndex(Integer endIndex) {
        this.endIndex = endIndex;
    }
    @FieldRenderer(ignore = true)
    private Integer debug;

    public Integer getDebug() {
        return debug;
    }

    public void setDebug(Integer debug) {
        this.debug = debug;
    }
    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }
    /**
     * 跨境 是否打印箱唛
     * 1-已打印,0-未打印
     */
    private Integer printBoxMark;

    public Integer getPrintBoxMark() {
        return printBoxMark;
    }

    public void setPrintBoxMark(Integer printBoxMark) {
        this.printBoxMark = printBoxMark;
    }


    public Integer getContainOrExcludeVipStorageNo() {
        return containOrExcludeVipStorageNo;
    }

    public void setContainOrExcludeVipStorageNo(Integer containOrExcludeVipStorageNo) {
        this.containOrExcludeVipStorageNo = containOrExcludeVipStorageNo;
    }

    Integer containOrExcludeVipStorageNo;
    public String getShippingOrderStatus() {
        return shippingOrderStatus;
    }

    public void setShippingOrderStatus(String shippingOrderStatus) {
        this.shippingOrderStatus = shippingOrderStatus;
    }

    /**
     * 跨境 发货单状态
     */
    private String shippingOrderStatus;

    public String getShippingOrderNumber() {
        return shippingOrderNumber;
    }

    public void setShippingOrderNumber(String shippingOrderNumber) {
        this.shippingOrderNumber = shippingOrderNumber;
    }

    /**
     * 跨境 发货单号
     */
    private String shippingOrderNumber;

    /**
     * 跨境 发货批次号
     */
    private String deliveryBatchCodes;

    public String getDeliveryBatchCodes() {
        return deliveryBatchCodes;
    }

    public void setDeliveryBatchCodes(String deliveryBatchCodes) {
        this.deliveryBatchCodes = deliveryBatchCodes;
    }
    public Boolean getReacquire() {
        return isReacquire;
    }

    public void setReacquire(Boolean reacquire) {
        this.isReacquire = reacquire;
    }


    /**
     * 商品编码打印状态 1.已打印 0.未打印
     */
    private Integer itemPrintStatus;

    public Integer getItemPrintStatus() {
        return itemPrintStatus;
    }

    public void setItemPrintStatus(Integer itemPrintStatus) {
        this.itemPrintStatus = itemPrintStatus;
    }


    public String getTheoryPostFeeUpperLimit() {
        return theoryPostFeeUpperLimit;
    }

    public void setTheoryPostFeeUpperLimit(String theoryPostFeeUpperLimit) {
        this.theoryPostFeeUpperLimit = theoryPostFeeUpperLimit;
    }

    public String getTheoryPostFeeLowerLimit() {
        return theoryPostFeeLowerLimit;
    }

    public void setTheoryPostFeeLowerLimit(String theoryPostFeeLowerLimit) {
        this.theoryPostFeeLowerLimit = theoryPostFeeLowerLimit;
    }
    public String getDiscountRateUpperLimit() {
        return discountRateUpperLimit;
    }

    public void setDiscountRateUpperLimit(String discountRateUpperLimit) {
        this.discountRateUpperLimit = discountRateUpperLimit;
    }

    public String getDiscountRateLowerLimit() {
        return discountRateLowerLimit;
    }

    public void setDiscountRateLowerLimit(String discountRateLowerLimit) {
        this.discountRateLowerLimit = discountRateLowerLimit;
    }

    /**
     * 折扣率查询上限值
     */
    private String discountRateUpperLimit;
    /**
     * 折扣率查询下限值
     */
    private String discountRateLowerLimit;

    public Integer getNeedDummyUpload() {
        return needDummyUpload;
    }

    public void setNeedDummyUpload(Integer needDummyUpload) {
        this.needDummyUpload = needDummyUpload;
    }

    public String getUploadParams() {
        return uploadParams;
    }

    public void setUploadParams(String uploadParams) {
        this.uploadParams = uploadParams;
    }

    public String getTradeSource() {
        return tradeSource;
    }

    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    public Integer getProgressType() {
        return progressType;
    }

    public void setProgressType(Integer progressType) {
        this.progressType = progressType;
    }

    //时效产品
    private String agedProductCode;

    public String getAgedProductCode() {
        return agedProductCode;
    }

    public void setAgedProductCode(String agedProductCode) {
        this.agedProductCode = agedProductCode;
    }

    /**
     * 极速打单发货 二级查询queryID
     */
    public String rapidPrintQueryIds;

    public String getRapidPrintQueryIds() {
        return rapidPrintQueryIds;
    }

    public void setRapidPrintQueryIds(String rapidPrintQueryIds) {
        this.rapidPrintQueryIds = rapidPrintQueryIds;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }
    public Double getMinGrossProfitRate() {
        return minGrossProfitRate;
    }

    public void setMinGrossProfitRate(Double minGrossProfitRate) {
        this.minGrossProfitRate = minGrossProfitRate;
    }

    public Double getMaxGrossProfitRate() {
        return maxGrossProfitRate;
    }

    public void setMaxGrossProfitRate(Double maxGrossProfitRate) {
        this.maxGrossProfitRate = maxGrossProfitRate;
    }

    /**
     * 毛利率区间
     */
    @FieldRenderer(value = "毛利率下限")
    private Double minGrossProfitRate;
    @FieldRenderer(value = "毛利率上限")
    private Double maxGrossProfitRate;

    /**
     * 服务商揽收单号
     */
    private String fulfillPickupOrderCode;

    public String getFulfillPickupOrderCode() {
        return fulfillPickupOrderCode;
    }

    public void setFulfillPickupOrderCode(String fulfillPickupOrderCode) {
        this.fulfillPickupOrderCode = fulfillPickupOrderCode;
    }

    private Boolean copyFilterClosedOrders;

    public Boolean getCopyFilterClosedOrders() {
        return copyFilterClosedOrders;
    }
    public void setCopyFilterClosedOrders(Boolean copyFilterClosedOrders) {
        this.copyFilterClosedOrders = copyFilterClosedOrders;
    }

    public String getCostUpperLimit() {
        return costUpperLimit;
    }

    public void setCostUpperLimit(String costUpperLimit) {
        this.costUpperLimit = costUpperLimit;
    }

    public String getCostLowerLimit() {
        return costLowerLimit;
    }

    public void setCostLowerLimit(String costLowerLimit) {
        this.costLowerLimit = costLowerLimit;
    }

    /**
     * 成本上限值
     */
    @FieldRenderer(value = "成本上限值")
    private String costUpperLimit;
    /**
     * 成本下限值
     */
    @FieldRenderer(value = "成本下限值")
    private String costLowerLimit;

    /**
     * 打包状态
     * 1：已发未验 2：已发已验
     */
    private String packageStatus;

    public String getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(String packageStatus) {
        this.packageStatus = packageStatus;
    }

}

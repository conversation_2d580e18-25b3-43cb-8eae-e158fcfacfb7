package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * 订单报关信息
 *
 * <AUTHOR>
 * @date 2022/6/21 下午12:01
 */
@Table(name = "trade_declare")
public class TradeDeclare extends Model {

    private static final long serialVersionUID = 7262464561151464391L;

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 系统订单id
     */
    private Long sid;

    /**
     * 平台子订单id
     */
    private Long oid;

    /**
     * 系统子订单id
     */
    private Long orderId;

    /**
     * 商品图片
     */
    private String picPath;

    /**
     * 商品编码
     */
    private String outerSkuId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 中文报关名称
     */
    private String declareZh;

    /**
     * 英文报关名称
     */
    private String declareEn;

    /**
     * 报关价格
     */
    private Double price;

    /**
     * 报关重量
     */
    private Double weight;

    /**
     * 报关数量
     */
    private Integer num;

    /**
     * 海关编码
     */
    private String hsCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDeclareZh() {
        return declareZh;
    }

    public void setDeclareZh(String declareZh) {
        this.declareZh = declareZh;
    }

    public String getDeclareEn() {
        return declareEn;
    }

    public void setDeclareEn(String declareEn) {
        this.declareEn = declareEn;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

}

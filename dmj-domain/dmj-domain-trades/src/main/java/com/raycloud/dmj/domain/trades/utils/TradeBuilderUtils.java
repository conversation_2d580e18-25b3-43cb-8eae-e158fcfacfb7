package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trade.except.TradeExceptOldUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.except.domain.ExceptData;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2023/4/19 3:48 下午
 * @Description:  trade 对象构建工具类
 */
public class TradeBuilderUtils {


    /**
     * 复制订单必要的属性
     * @param target
     * @param origin
     */
    private static void tradeBasePropCopier(Trade target,Trade origin){
        target.setSid(origin.getSid());
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(origin);
        //业务中可能构建多次更新对象 tradeExceptData的MergeSid 始终取最新的
        tradeExceptData.setMergeSid(origin.getMergeSid());
        target.setCompanyId(origin.getCompanyId());
        if(origin.getOrigin()!=null){
            target.setOrigin(origin.getOrigin());
        }else{
            target.setOrigin(origin);
        }
        target.setSubTradeExceptDatas(origin.getSubTradeExceptDatas());
        target.setExceptData(tradeExceptData);
    }

    /**
     * 复制订单必要的属性
     * @param target
     * @param origin
     */
    public static void tradeCommonPropCopier(Trade target,Trade origin){
        if(target==null|| origin==null){
            return;
        }
        tradeBasePropCopier(target,origin);
        tradeCopierExcept(target,origin);

    }

    /**
     * 链路透传中 复制trade 对象 时，必须复制的公共属性
     * @param origin
     * @return
     */
    public static Trade builderUpdateTrade(Trade origin){
        Trade target= new TbTrade();
        tradeCommonPropCopier(target,origin);
        return target;
    }

    /**
     * withOldExcept:false 只透传新的异常属性，itemExcep，excep,v不透传，避免更新多余的字段,用于非itemExcep，excep,v 异常的更新
     * @param origin
     * @param withOldExcept
     * @return
     */
    public static Trade builderUpdateTrade(Trade origin, boolean withOldExcept) {
        if (withOldExcept) {
            return builderUpdateTrade(origin);
        }
        return builderUpdateTrade(origin, false, false, false, false);
    }

    /**
     *  复制原trade属性
     * @param origin
     * @param updateItemExcep
     * @param updateExcep
     * @param updateExceptIds
     * @param updateV
     * @return
     */
    public static Trade builderUpdateTrade(Trade origin, boolean updateItemExcep, boolean updateExcep, boolean updateExceptIds, Boolean updateV) {
        Trade trade = builderUpdateTrade(origin);
        if (!updateItemExcep) {
            trade.setItemExcep(null);
        }
        if (!updateExcep) {
            trade.setExcep(null);
        }
        if (!updateExceptIds) {
            trade.setExceptIds(null);
        }
        if (!updateV) {
            trade.setV(null);
        }
        return trade;
    }

    /**
     *  只更新exceptIds 字段
     * @param origin
     * @return
     */
    public static Trade builderUpdateExceptIds(Trade origin){
        return builderUpdateTrade(origin,false,false,true,false);
    }


    /**
     *  只更新itemExcep 字段
     * @param origin
     * @return
     */
    public static Trade builderUpdateItemExcep(Trade origin){
        return builderUpdateTrade(origin,true,false,false,false);
    }

    /**
     * 只更新excep
     * @param origin
     * @return
     */
    public static Trade builderUpdateExcep(Trade origin){
        return builderUpdateTrade(origin,false,true,false,false);
    }

    /**
     *  只更新V字段
     * @param origin
     * @return
     */
    public static Trade builderUpdateV(Trade origin){
        return builderUpdateTrade(origin,false,false,false,true);
    }


    /**
     *
     * @param target
     * @param origin
     */
    public static void tradeCopierExcept(Trade target,Trade origin){
      /*  target.setExceptData(origin.getExceptData());
        target.setSubTradeExceptDatas(origin.getSubTradeExceptDatas());*/
        target.setItemExcep(origin.getItemExcep());
        target.setV(origin.getV());
        target.setExcep(origin.getExcep());
        target.setExceptIds(origin.getExceptIds());
        target.setOldExceptIds(origin.getOldExceptIds());
    }


    /**
     *  复制异常信息，创建新的对象
     * @param target
     * @param origin
     */
    public static void tradeCopierNewExcept(Trade target,Trade origin){
        target.setSid(origin.getSid());
        target.setExceptData(TradeExceptUtils.getTradeExceptData(origin).copier());
        target.setSubTradeExceptDatas(origin.getSubExceptDatasCopier());
        Map<Long, ExceptData> subTradeExceptDatas = target.getSubTradeExceptDatas();
        if (subTradeExceptDatas == null) {
            subTradeExceptDatas = new ConcurrentHashMap<>();
        }
        if (origin.getSid() != null) {
            subTradeExceptDatas.put(origin.getSid(), TradeExceptUtils.getTradeExceptData(target));
        }
        target.setSubTradeExceptDatas(subTradeExceptDatas);
        target.setItemExcep(origin.getItemExcep());
        target.setV(origin.getV());
        target.setExcep(origin.getExcep());
        target.setExceptIds(origin.getExceptIds());
    }



}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.pt.FreightCost;

import java.io.Serializable;

/**
 * @Author: ruanyaguang
 * @Date : 2018/9/25
 * @Info :
 */
public class CalculateFreightCostResult implements Serializable {
    private Long sid;
    
    private String tid;

    private FreightCost cost;

    public CalculateFreightCostResult(Long sid, FreightCost cost) {
        this.sid = sid;
        this.cost = cost;
    }
    
    public CalculateFreightCostResult(String tid, FreightCost cost) {
        super();
        this.tid = tid;
        this.cost = cost;
    }

    public Long getSid() {
        return sid;
    }

    public FreightCost getCost() {
        return cost;
    }
    
    public String getTid() {
        return tid;
    }

}
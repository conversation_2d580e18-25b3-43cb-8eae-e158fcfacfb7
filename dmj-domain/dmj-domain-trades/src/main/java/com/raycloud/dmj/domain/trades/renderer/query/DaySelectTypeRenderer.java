package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class DaySelectTypeRenderer implements IFieldValueRenderer {

    /**
     * 勾选条件 1-付款天数 2-下单天数
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,1)) {
            return "付款天数";
        }
        if (Objects.equals(originValue,2)) {
            return "下单天数";
        }
        return String.valueOf(originValue);
    }
}

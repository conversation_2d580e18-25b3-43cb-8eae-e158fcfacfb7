package com.raycloud.dmj.domain.trades;

import lombok.Data;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-19 16:30:23
 */
@Data
public class ChangeOrderCodeReqDTO {

    private TradeControllerParams queryParams;

    private String originSysOuterId;

    private Order orderFront;

    public void validate() {
        Assert.isTrue(queryParams != null,"queryParams 不能为空");
        Assert.notNull(originSysOuterId, "请输入原系统商家编码");
        Assert.notNull(orderFront, "orderFront 不能为空");
        Assert.notNull(orderFront.getOuterId(), "请输入新商家编码");
    }
}

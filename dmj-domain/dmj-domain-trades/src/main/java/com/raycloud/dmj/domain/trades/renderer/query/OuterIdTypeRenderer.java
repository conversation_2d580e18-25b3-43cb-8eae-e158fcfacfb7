package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class OuterIdTypeRenderer implements IFieldValueRenderer {

    /**
     * 当key字段对应传入outerId时，启用该字段，0 查询所以，1 仅查询包含单品的订单，2 仅查询包含套件商品订单
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "查询所有";
        }
        if (Objects.equals(originValue,1)) {
            return "仅查询包含单品的订单";
        }
        if (Objects.equals(originValue,2)) {
            return "仅查询包含套件商品订单";
        }
        return String.valueOf(originValue);
    }
}

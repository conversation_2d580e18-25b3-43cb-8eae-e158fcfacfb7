package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.trades.Order;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenchaochao
 * @Date: 2021/7/8 10:52 上午
 */
public class ShopeeSplitParams implements Serializable {
    private static final long serialVersionUID = -5132812001371247589L;

    private Long sid;

    private List<List<Order>> splitOrders;


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public List<List<Order>> getSplitOrders() {
        return splitOrders;
    }

    public void setSplitOrders(List<List<Order>> splitOrders) {
        this.splitOrders = splitOrders;
    }

}

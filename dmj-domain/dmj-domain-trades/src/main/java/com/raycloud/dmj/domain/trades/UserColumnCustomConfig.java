package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * UserColumnConfConfig
 *
 * <AUTHOR>
 * @Date 2018/12/7
 * @Time 11:10
 */
@Table(name = "user_column_custom_config")
public class UserColumnCustomConfig extends Model {

    private static final long serialVersionUID = -2242822721548955166L;
    /**
     * 卖家公司ID
     */
    private Long companyId;

    /**
     * 列所属页面ID
     */
    private Long pageId;

    /**
     * 列ID
     */
    private Long colId;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 配置
     */
    private String config;

    public UserColumnCustomConfig() {
    }


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }
}

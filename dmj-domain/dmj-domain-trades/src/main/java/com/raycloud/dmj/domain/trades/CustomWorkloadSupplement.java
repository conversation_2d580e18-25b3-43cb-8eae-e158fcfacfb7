package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> xubin
 * @desc:   自定义工作量补入
 * @date : 6/7/22  1:54 PM
 */
@Table(name = "custom_workload_supplement")
@Data
public class CustomWorkloadSupplement extends Model implements Serializable{

    private static final long serialVersionUID = 3204573357873315220L;

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 名称
     */
    private String name;


    /**
     * 单据类型 1 订单 2加工单
     */
    private Integer orderType;

    /**
     * 商品类型 1 成品 2 原料
     */
    private Integer goodsType;


    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 操作人id
     */
    private Long staffId;

    /**
     * 操作人名称
     */
    private String staffName;

    private Integer enableStatus;

    /**
     * 是否展示tab
     */
    private Integer isShow;
}

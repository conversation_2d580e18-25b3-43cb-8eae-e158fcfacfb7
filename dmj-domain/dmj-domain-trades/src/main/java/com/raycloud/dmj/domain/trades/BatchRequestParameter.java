package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.user.User;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class BatchRequestParameter implements Serializable {

    private static final long serialVersionUID = -2140071679233002537L;

    private List<User> user;
    /**
     * 收件人手机号
     */
    private String mobile;
    /**
     * 收件人固话
     */
    private String phone;
    /**
     * 收件人名
     */
    private String name;
    /**
     * 查询订单开始时间
     */
    private Date startTime;
    /**
     * 查询订单结束时间
     */
    private Date endTime;
}

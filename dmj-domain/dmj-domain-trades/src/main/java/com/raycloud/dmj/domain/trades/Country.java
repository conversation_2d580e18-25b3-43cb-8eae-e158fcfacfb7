package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * @Author: ruanyaguang
 * @Date : 2017/11/10
 */
@Table(name = "country", migratable = false)
public class Country extends Model {
    private static final long serialVersionUID = -8786025969344192578L;

    private String id;
    private String enName;
    private String cnName;
    private String isoName;
    private String phone;
    private String zone;
    private String chargeArea;
    private String chargeStandard;
    private String ydfId;
    private String chargeAreaPingyou;
    private String sortingCode;
    private String chargeAreaGuahao;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getIsoName() {
        return isoName;
    }

    public void setIsoName(String isoName) {
        this.isoName = isoName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getChargeArea() {
        return chargeArea;
    }

    public void setChargeArea(String chargeArea) {
        this.chargeArea = chargeArea;
    }

    public String getChargeStandard() {
        return chargeStandard;
    }

    public void setChargeStandard(String chargeStandard) {
        this.chargeStandard = chargeStandard;
    }

    public String getYdfId() {
        return ydfId;
    }

    public void setYdfId(String ydfId) {
        this.ydfId = ydfId;
    }

    public String getChargeAreaPingyou() {
        return chargeAreaPingyou;
    }

    public void setChargeAreaPingyou(String chargeAreaPingyou) {
        this.chargeAreaPingyou = chargeAreaPingyou;
    }

    public String getSortingCode() {
        return sortingCode;
    }

    public void setSortingCode(String sortingCode) {
        this.sortingCode = sortingCode;
    }

    public String getChargeAreaGuahao() {
        return chargeAreaGuahao;
    }

    public void setChargeAreaGuahao(String chargeAreaGuahao) {
        this.chargeAreaGuahao = chargeAreaGuahao;
    }
}

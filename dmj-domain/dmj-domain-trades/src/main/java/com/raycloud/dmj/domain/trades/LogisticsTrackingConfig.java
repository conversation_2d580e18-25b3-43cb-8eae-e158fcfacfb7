package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-01-11-9:00
 */
public class LogisticsTrackingConfig implements Serializable {


    private static final long serialVersionUID = -3135679489984011571L;

    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    private Date created;

    private Date modified;

    private Integer enableStatus;

    /**
     * 未揽收时限 单位 h
     */
    private Integer gatheredConfirmDeadline;

    /**
     * 物流未更新时限 单位 h
     */
    private Integer logisticsModifyDeadline;

    /**
     * 揽收后无物流信息超时时限 单位 h
     */
    private Integer noTransformDeadline;
    /**
     * 分拨超时
     */
    private Integer alloCatDeadline;
    /**
     * 节点超时配置 json
     */
    private String nodeDeadlineStr;
    /**
     * 节点超时配置
     */
    private NodeDeadlineSet nodeDeadlineSet;

    /**
     * 节点超时配置 属性命名无奈
     */
    public static class NodeDeadlineSet implements Serializable {
        private static final long serialVersionUID = 8684902611454347709L;
        /**
         * 江浙沪皖同区域超时
         */
        @JSONField(name = "JZHWDeadline")
        private Integer JZHWDeadline;
        /**
         * 京津冀同区域
         */
        @JSONField(name = "JJJDeadline")
        private Integer JJJDeadline;
        /**
         * 同一省份
         */
        @JSONField(name = "TYSFDeadline")
        private Integer TYSFDeadline;
        /**
         * 不同省份
         */
        @JSONField(name = "BTSFNodeDeadline")
        private Integer BTSFNodeDeadline;
        /**
         * 新疆西藏内蒙停留超时
         */
        @JSONField(name = "XXNDeadline")
        private Integer XXNDeadline;

        public Integer getJZHWDeadline() {
            return JZHWDeadline;
        }

        public void setJZHWDeadline(Integer JZHWDeadline) {
            this.JZHWDeadline = JZHWDeadline;
        }

        public Integer getJJJDeadline() {
            return JJJDeadline;
        }

        public void setJJJDeadline(Integer JJJDeadline) {
            this.JJJDeadline = JJJDeadline;
        }

        public Integer getTYSFDeadline() {
            return TYSFDeadline;
        }

        public void setTYSFDeadline(Integer TYSFDeadline) {
            this.TYSFDeadline = TYSFDeadline;
        }

        public Integer getBTSFNodeDeadline() {
            return BTSFNodeDeadline;
        }

        public void setBTSFNodeDeadline(Integer BTSFNodeDeadline) {
            this.BTSFNodeDeadline = BTSFNodeDeadline;
        }

        public Integer getXXNDeadline() {
            return XXNDeadline;
        }

        public void setXXNDeadline(Integer XXNDeadline) {
            this.XXNDeadline = XXNDeadline;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Integer getGatheredConfirmDeadline() {
        return gatheredConfirmDeadline;
    }

    public void setGatheredConfirmDeadline(Integer gatheredConfirmDeadline) {
        this.gatheredConfirmDeadline = gatheredConfirmDeadline;
    }

    public Integer getLogisticsModifyDeadline() {
        return logisticsModifyDeadline;
    }

    public void setLogisticsModifyDeadline(Integer logisticsModifyDeadline) {
        this.logisticsModifyDeadline = logisticsModifyDeadline;
    }

    public Integer getNoTransformDeadline() {
        return noTransformDeadline;
    }

    public void setNoTransformDeadline(Integer noTransformDeadline) {
        this.noTransformDeadline = noTransformDeadline;
    }

    public Integer getAlloCatDeadline() {
        return alloCatDeadline;
    }

    public void setAlloCatDeadline(Integer alloCatDeadline) {
        this.alloCatDeadline = alloCatDeadline;
    }

    public String getNodeDeadlineStr() {
        return nodeDeadlineStr;
    }

    public void setNodeDeadlineStr(String nodeDeadlineStr) {
        this.nodeDeadlineStr = nodeDeadlineStr;
    }

    public LogisticsTrackingConfig.NodeDeadlineSet getNodeDeadlineSet() {
        if (nodeDeadlineSet == null) {
            initNodeSet();
        }
        return nodeDeadlineSet;
    }

    public void setNodeDeadlineSet(LogisticsTrackingConfig.NodeDeadlineSet nodeDeadlineSet) {
        this.nodeDeadlineSet = nodeDeadlineSet;
        initNodeSetStr();
    }

    /**
     * 初始化节点设置对象
     */
    public void initNodeSet() {
        if (StringUtils.isNotBlank(nodeDeadlineStr)) {
            nodeDeadlineSet = JSONObject.parseObject(nodeDeadlineStr, NodeDeadlineSet.class);
        } else {
            nodeDeadlineSet = new NodeDeadlineSet();
        }
    }

    /**
     * 初始化节点设置json
     */
    public void initNodeSetStr() {
        NodeDeadlineSet nodeSet;
        if (StringUtils.isNotBlank(nodeDeadlineStr)) {
            nodeSet = JSONObject.parseObject(nodeDeadlineStr, NodeDeadlineSet.class);
        } else {
            nodeSet = new NodeDeadlineSet();
        }
        if (nodeDeadlineSet != null) {
            parseNodeSet(nodeSet, nodeDeadlineSet);
        }
        nodeDeadlineStr = JSONObject.toJSONString(nodeSet);
    }

    /**
     * 赋值节点设置
     */
    public static void parseNodeSet(NodeDeadlineSet target, NodeDeadlineSet source) {
        if (source.getJZHWDeadline() != null) {
            target.setJZHWDeadline(source.getJZHWDeadline());
        }
        if (source.getJJJDeadline() != null) {
            target.setJJJDeadline(source.getJJJDeadline());
        }
        if (source.getTYSFDeadline() != null) {
            target.setTYSFDeadline(source.getTYSFDeadline());
        }
        if (source.getBTSFNodeDeadline() != null) {
            target.setBTSFNodeDeadline(source.getBTSFNodeDeadline());
        }
        if (source.getXXNDeadline() != null) {
            target.setXXNDeadline(source.getXXNDeadline());
        }
    }
}

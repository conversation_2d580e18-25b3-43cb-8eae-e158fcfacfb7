package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.BusinessNodeEnum;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.user.User;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-09-27
 */
public class PostFeeLogBuilder extends AbsLogBuilder<PostFeeLogBuilder> {

    public PostFeeLogBuilder(User user) {
        super(user);
    }

    public PostFeeLogBuilder(Staff staff) {
        super(staff);
    }

    public PostFeeLogBuilder(Long companyId) {
        super(companyId);
    }

    public PostFeeLogBuilder(Long companyId, Long staffId, String staffName) {
        super(companyId, staffId, staffName);
    }

    public PostFeeLogBuilder appendNode(BusinessNodeEnum businessNode){
        append("计算环节",businessNode == null?"null":businessNode.getNodeName());
        return this;
    }

    @Override
    protected String getBusinessSign() {
        return LogBusinessEnum.POST_FEE.getSign();
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.domain.trades.request.TradeTimeoutContext;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: chenchaochao
 * @Date: 2020/7/2 5:01 下午
 */
public class TradeEsConTimeUtils {

    /**
     * 合单，天猫时效承诺取最小值,如果是放心购的订单取trade维度的最小值
     * mainTrade在生成后和mergeTrades其中一个是同一个内存数据
     * 计算合单的承诺时间
     * mainTrade 可能不能包涵order mergeTrades 一定包含mainTrade 也包含order
     */
    public static <T extends Trade> void handleTimeoutActionTimeWhenMerge(TradeTimeoutContext ctx, Trade mainTrade, List<T> mergeTrades) {
        // 计算每个单的承诺时间
        for (Trade trade : mergeTrades) {
            handleTimeoutActionTime(trade, TradeUtils.getOrders4Trade(trade), false, ctx != null && ctx.isTimeoutFromSysProduct());
        }
        // 合单去最小的承诺时间
        Date minTradeTimeoutActionTime = getMinTradeTimeoutActionTime(mergeTrades);
        if (minTradeTimeoutActionTime != null) {
            if (mainTrade.getTimeoutActionTime() != null) {
                Logs.debug(String.format("sid=%s 合单最小承诺时间：trade：%s,merge_sid:%s ,order:%s", mainTrade.getSid(), mainTrade.getMergeSid(), mainTrade.getTimeoutActionTime(), minTradeTimeoutActionTime));
            }
            mainTrade.setTimeoutActionTime(minTradeTimeoutActionTime);
        }
    }


    /**
     * 计算单个单的承若发货时间
     *
     * @param trade
     */
    public static void handleTimeoutActionTime(TradeTimeoutContext ctx, Trade trade) {
        handleTimeoutActionTime(trade, TradeUtils.getOrders4Trade(trade), false, ctx != null && ctx.isTimeoutFromSysProduct());
    }

    public static void handleTimeoutActionTime(TradeTimeoutContext ctx, Trade trade, boolean tradeFlag) {
        handleTimeoutActionTime(trade, TradeUtils.getOrders4Trade(trade), tradeFlag, ctx != null && ctx.isTimeoutFromSysProduct());
    }


    public static Date getTimeoutActionTime(TradeTimeoutContext ctx, Trade trade, List<Order> orders) {
        handleTimeoutActionTime(trade, orders, false, ctx != null && ctx.isTimeoutFromSysProduct());
        return trade.getTimeoutActionTime();
    }

    /**
     * 计算一批订单的承诺时间 包含合单拆单
     *
     * @param trades
     */
    public static <T extends Trade> void handleTimeoutActionTime(TradeTimeoutContext ctx, List<T> trades) {
        if (ctx != null && ctx.isTimeoutFromSysProduct()) {
            Set<Trade> recalculated = new HashSet<>();
            trades.stream().
                    filter(t -> Objects.nonNull(t.getMergeSid())).
                    collect(Collectors.groupingBy(Trade::getMergeSid)).
                    forEach((mergeSid, ts) -> {
                        if (ts.isEmpty()) {
                            return;
                        }
                        Trade mainTrade = ts.stream().filter(t -> Objects.equals(t.getMergeSid(), t.getSid())).findFirst().orElse(null);
                        Date min = mainTrade != null ? mainTrade.getTimeoutActionTime() : null;
                        for (Trade t : ts) {
                            Date recalculation = recalculation(t, ctx.isTimeoutFromSysProduct());
                            t.setTimeoutActionTime(recalculation);
                            recalculated.add(t);
                            if (min == null || DateUtils.isAfter(min, recalculation)) {
                                min = recalculation;
                            }
                        }
                        if (mainTrade != null) {
                            mainTrade.setTimeoutActionTime(min);
                        }
                    });
            trades.stream().
                    filter(t -> Objects.nonNull(t.getSplitSid())).
                    collect(Collectors.groupingBy(Trade::getSplitSid)).
                    forEach((splitSid, ts) -> {
                        if (ts.isEmpty()) {
                            return;
                        }
                        Trade origin = ts.stream().filter(t -> Objects.equals(t.getSplitSid(), t.getSid())).findFirst().orElse(null);
                        if (origin != null && origin.getTimeoutActionTime() != null) {
                            for (Trade t : ts) {
                                t.setTimeoutActionTime(origin.getTimeoutActionTime());
                                recalculated.add(t);
                            }
                        }
                    });
            for (Trade t : trades) {
                if (!recalculated.contains(t)) {
                    Date recalculation = recalculation(t, ctx.isTimeoutFromSysProduct());
                    t.setTimeoutActionTime(recalculation);
                }
            }
            return;
        }
        Map<Long, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : trades) {
            tradeMap.computeIfAbsent(trade.getMergeSid() > 0 ? trade.getMergeSid() : trade.getSid(), k -> new ArrayList<>()).add(trade);
        }
        Set<Map.Entry<Long, List<Trade>>> entries = tradeMap.entrySet();
        for (Map.Entry<Long, List<Trade>> entry : entries) {
            List<Trade> tradeList = entry.getValue();
            if (CollectionUtils.isEmpty(tradeList)) {
                continue;
            }
            for (Trade trade : tradeList) {
                handleTimeoutActionTime(trade, TradeUtils.getOrders4Trade(trade), false, ctx != null && ctx.isTimeoutFromSysProduct());
            }
            // 非合单
            if (tradeList.size() == 1) {
                continue;
            }
            // 合单
            List<Trade> collect = tradeList.stream().filter(trade -> Objects.equals(trade.getSid(), trade.getMergeSid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }
            // 主单
            Trade mainTrade = collect.get(0);
            handleTimeoutActionTimeWhenMerge(ctx, mainTrade, tradeList);

        }
    }

    /**
     * 设置拆单或者取消拆单时的承诺时间
     * 放心购的拆单和取消拆单都以trade维度的承诺时间为准
     * 计算单个trade的承诺时间
     * @param trade
     * @param orders
     * @param tradeFlag 是否需要根据trade计算
     * @param timeoutFromSysProduct 白名单控制 order承诺发货时间取系统商品自定义字段 发货周期 根据付款时间计算
     */
    public static void handleTimeoutActionTime(Trade trade, List<Order> orders, boolean tradeFlag, boolean timeoutFromSysProduct) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        if (timeoutFromSysProduct) {
            Date recalculation = recalculation(trade, timeoutFromSysProduct);
            trade.setTimeoutActionTime(recalculation);
            return;
        }
        // 放心购单独处理
        if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())) {
            handleFxgTimeoutActionTime(trade, orders);
            return;
        }
        Date newTimeoutActionTime = null;
        if (tradeFlag) {
            newTimeoutActionTime = trade.getTimeoutActionTime();
        }
        dealTradeTimeoutActionTimeWhitOrders(trade, orders, newTimeoutActionTime);
    }


    private static void dealTradeTimeoutActionTimeWhitOrders(Trade trade, List<Order> orders, Date newTimeoutActionTime) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (Order order : orders) {
            if (isAfterConsignedStatus(order)) {
                continue;
            }
            if (order.isPlatformGift() && (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource()))) {
                continue;
            }
            if (newTimeoutActionTime == null || (order.getEstimateConTime() != null && newTimeoutActionTime.after(order.getEstimateConTime()))) {
                newTimeoutActionTime = order.getEstimateConTime();
            }
        }
        if (newTimeoutActionTime != null) {
            trade.setTimeoutActionTime(newTimeoutActionTime);
        }
    }

    private static boolean isAfterConsignedStatus(Order order) {
        String sysStatus = order.getSysStatus();
        if (!Objects.equals(order.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)) {
            sysStatus = TradeStatus.getSysStatus(order.getStatus(), order.getSysStatus());
        }
        return TradeStatusUtils.isAfterSendGoods(sysStatus);
    }

    /**
     *
     * @param orders
     * @param excludeAfterConsignedStatus 是否排除发货后的order
     * @return
     */
    private static Date getMaxTimeoutActionTime(List<Order> orders, boolean excludeAfterConsignedStatus) {
        Stream<Order> orderStream = orders.stream()
                .filter(order -> Objects.nonNull(order.getEstimateConTime()) && !Objects.equals(order.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS));
        if (excludeAfterConsignedStatus) {
            orderStream = orderStream.filter(order -> !isAfterConsignedStatus(order));
        }
        return orderStream.sorted(Comparator.comparing(Order::getEstimateConTime).reversed())
                .map(Order::getEstimateConTime)
                .findFirst().orElse(null);
    }


    private static boolean hasFullPresellOrder(List<Order> orders) {
        return Optional.of(orders.stream().filter(order -> !isAfterConsignedStatus(order)).anyMatch(Order::isFullPresell)).orElse(false);
    }


    private static Date getMinTimeoutActionTime(List<Order> orders) {
        return orders.stream()
                .filter(order -> Objects.nonNull(order.getEstimateConTime()) && !isAfterConsignedStatus(order) && !Objects.equals(order.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS))
                .sorted(Comparator.comparing(Order::getEstimateConTime))
                .map(Order::getEstimateConTime)
                .findFirst().orElse(null);
    }

    //抖店拆单订单包含未发货预售商品取商品承诺时间最大值，否则取承诺时间最小值
    private static void handleFxgTimeoutActionTime(Trade trade, List<Order> orders) {
        if (isManualUpdate(trade)) {
            return;
        }
        if (CollectionUtils.isEmpty(orders) || !CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())) {
            return;
        }
        Date maxTimeoutActionTime = null;
        if (isOrderAllAfterConsignedStatus(orders)) {
            //全部为发货后的商品，取最长的
            maxTimeoutActionTime = getMaxTimeoutActionTime(orders, false);
        } else {
            // 拆单并且全部为现货取最短的，其他情况都取最长的
            maxTimeoutActionTime = !hasFullPresellOrder(orders) && TradeUtils.isSplit(trade) ? getMinTimeoutActionTime(orders) : getMaxTimeoutActionTime(orders, true);
        }
        if (maxTimeoutActionTime != null) {
            if (trade.getTimeoutActionTime() != null && maxTimeoutActionTime.after(trade.getTimeoutActionTime())) {
                Logs.ifDebug(String.format("sid=%s 放心购承诺时间：merge_sid：%s ,trade.timeoutActionTime:%s,order:%s", trade.getSid(), trade.getMergeSid(), trade.getTimeoutActionTime(), maxTimeoutActionTime));
            }
            trade.setTimeoutActionTime(maxTimeoutActionTime);
        }
    }

    /**
     *  获取最小的承诺时间
     * @param trades
     * @return
     */
    private static <T extends Trade> Date getMinTradeTimeoutActionTime(List<T> trades) {
        // 默认时间 2000-01-01 00:00:00
        Date def = new Date(946656000000L);
        return trades.stream()
                .filter(trade -> Objects.nonNull(trade.getTimeoutActionTime()) && trade.getTimeoutActionTime().after(def) && !TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(trade.getStatus(), trade.getSysStatus())))
                .sorted(Comparator.comparing(Trade::getTimeoutActionTime))
                .map(Trade::getTimeoutActionTime)
                .findFirst().orElse(null);
    }

    /**
     * 全部都为发货后
     */
    private static boolean isOrderAllAfterConsignedStatus(List<Order> orders) {
        return orders.stream().allMatch(order -> isAfterConsignedStatus(order));
    }

    /**
     * 根据系统商品自定义字段 发货周期
     * 承诺时间取值逻辑
     * 单个sid 取order中最晚的时间
     * 多个sid 取sid中最早的
     */
    public static Date recalculation(Trade trade, boolean timeoutFromSysProduct) {
        if (isManualUpdate(trade)) {
            return trade.getTimeoutActionTime();
        }
        if (timeoutFromSysProduct && TradeUtils.isSplit(trade)) {
            return trade.getTimeoutActionTime();
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        Map<Long, List<Order>> groups = orders.stream().collect(Collectors.groupingBy(Order::getSid));
        List<Date> timeoutActionTimes = new ArrayList<>(groups.size());
        for (Long sid : groups.keySet()) {
            List<Order> group = groups.get(sid);
            boolean allIsConsignedOrRefund = group.stream().allMatch(o -> TradeStatusUtils.isAfterSendGoods(o.getSysStatus()) || Order.REFUND_SUCCESS.equals(o.getRefundStatus()));
            if (allIsConsignedOrRefund) {
                timeoutActionTimes.add(group.stream().map(Order::getEstimateConTime).filter(Objects::nonNull).max(Date::compareTo).orElse(null));
            } else {
                timeoutActionTimes.add(group.stream().
                        filter(o -> !TradeStatusUtils.isAfterSendGoods(o.getSysStatus()) && !Order.REFUND_SUCCESS.equals(o.getRefundStatus())).
                        map(Order::getEstimateConTime).
                        filter(Objects::nonNull).
                        max(Date::compareTo).
                        orElse(null)
                );
            }
        }
        return timeoutActionTimes.stream().filter(Objects::nonNull).min(Date::compareTo).orElse(null);
    }

    /**
     * 用户开了白名单 手动修改了承诺时间的 不做重算
     */
    static boolean isManualUpdate(Trade trade) {
        return TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_UPDATE_TIMEOUT);
    }
}

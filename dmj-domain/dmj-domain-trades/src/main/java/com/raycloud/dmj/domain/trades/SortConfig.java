package com.raycloud.dmj.domain.trades;

import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Data
@Builder
public class SortConfig<T> implements Serializable {
    private static final long serialVersionUID = 5569522509707443991L;

    public static final Set<String> TIME_TYPE_SETS = Sets.newHashSet("created","pay_time","modified",
            "end_time","audit_time","timeout_action_time","upd_time","delivery_time","collect_time","sign_time",
            "express_print_time","deliver_print_time","assembly_print_time","pt_consign_time","consign_time","inserted");

    public static final String SINGLE_DATA_TYPE = "single";

    public static final String SORT_TYPE_STRING_2_LONG = "string_2_long";
    public static final String SORT_TYPE_STRING_2_DOUBLE = "string_2_Double";
    public static final String SORT_TYPE_USE_GBK = "useGBK";

    public static final List<String> SUPPORT_SORT_FIELD_LIST = Splitter.on(",").omitEmptyStrings().trimResults().splitToList("sid,short_id,wave_id,user_id,taobao_id,company_id,source,sub_source,tid,status,unified_status,type,price,discount_fee," +
            "total_fee,created,pay_time,modified,end_time,buyer_message,buyer_nick,adjust_fee,tax_fee,trade_from,seller_nick," +
            "payment,pay_amount,post_fee,seller_memo,seller_memo_update,seller_flag,receiver_name,receiver_country,receiver_state," +
            "receiver_city,receiver_district,receiver_address,address_type,receiver_zip,receiver_mobile,mobile_tail," +
            "receiver_phone,consign_time,sys_consigned,timeout_action_time,out_sid,upd_time,enable_status,can_confirm_send," +
            "express_print_time,deliver_print_time,assembly_print_time,check_manual_merge_count,template_id,template_type,is_excep," +
            "is_lost_msg,is_halt,is_refund,is_package,is_weigh,is_presell,can_delivered,warehouse_id,warehouse_name,sys_status," +
            "sys_memo,is_cancel,is_urgent,audit_time,stock_status,insufficient_num,insufficient_rate,weight,actual_post_fee," +
            "invoice_kind,invoice_remark,need_invoice,invoice_format,invoice_name,invoice_type,buyer_tax_no,is_auto_audit," +
            "net_weight,volume,cost,sale_price,scalping,is_upload,inserted,boxing_list,presell_rule_id,po_nos,vip_pick_no," +
            "vip_storage_no,item_num,item_kind_num,address_changed,merge_type,merge_sid,split_type,split_sid,address_md5," +
            "is_store,black_buyer_nick,sys_outer_id,is_jz,is_smart,tag_ids,packma_cost,three_pl_timing,is_tmall_delivery," +
            "except_ids,is_deduct,is_sendmessage,except_memo,is_handler_message,is_handler_memo,pick_goods_type,timing_promise," +
            "promise_service,delivery_time,collect_time,sign_time,es_time,item_excep,pt_consign_time,unattainable,print_count," +
            "handled,v,theory_post_fee,dest_id,source_id,convert_type,belong_type,excep,stock_region_type,ac_payment,merge_md5," +
            "receiver_street,manual_mark_ids,gross_profit,open_uid,logistics_company_id");
    public static final List<String> SUPPORT_SORT_TYPE_LIST = Lists.newArrayList("desc","asc");
    /**
     * 字段  filed做检验 不允许包含特殊字符 除开_其他一律不支持
     */
    private String field;
    /**
     * desc OR asc
     */
    private String order;

    /**
     * 排序方式
     * single 单页排序
     * 其他值  数据库排序
     */
    private String dataType;
    /**
     * string2Long
     * string2double
     * useGBK
     */
    private String sortType;
    /**
     * 索引排序值
     */
    private int index;

    private Function<String, Function<T, Object>> function;

    private Comparator comparator;

    public String getSql() {
        //DB 排序先支持到trade级别的，order级别的sql需要整改查询逻辑之后才能支持
        String sql = "";
        if (SINGLE_DATA_TYPE.equals(this.dataType)) {
            return sql;
        }
        if(StringUtils.isBlank(this.field) ||
                StringUtils.isBlank(this.order) ||
                !SUPPORT_SORT_FIELD_LIST.contains(this.field.toLowerCase(Locale.ROOT))||
                !SUPPORT_SORT_TYPE_LIST.contains(this.order.toLowerCase(Locale.ROOT))){
            return sql;
        }
        if (this.sortType == null) {
            sql = normal(this.field, this.order);
        } else {
            switch (this.sortType) {
                case SORT_TYPE_STRING_2_LONG:
                case SORT_TYPE_STRING_2_DOUBLE:
                    sql = str2num(this.field, this.order);
                    break;
                case SORT_TYPE_USE_GBK:
                    sql = sql + nullFirst(this.field, this.order) + "," + useGBK(this.field, this.order);
                    break;
                default:
                    sql = normal(this.field, this.order);
                    break;
            }
        }

        return sql;
    }

    /**
     * 自定义值排序
     * 定义规则，按照给定的值顺序排序
     */
//    private String cumtomVlaueSort(String field, String order) {
//    }
    private String str2num(String field, String order) {
        return "  CAST(t.`" + field + "`) AS DECIMAL(15, 2)) " + order.toUpperCase();
    }

    private String normal(String field, String order) {
        return "t.`" + field + "` " + order.toUpperCase();
    }

    private String useGBK(String field, String order) {
        return " CONVERT( t.`" + field + "` USING 'GBK') " + order.toUpperCase();
    }

    private String nullFirst(String field, String order) {
        return "(t.`" + field + "` is null OR t.`" + field + "` = '' ) " + order.toUpperCase();
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.CommonConstants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/29 13:45
 */
public class TradeKjUtils {

    public static final Set<String> KJ_SOURCE_LIST = new HashSet<>(
            Arrays.asList(CommonConstants.PLAT_FORM_TYPE_TEMU,
                    CommonConstants.PLAT_FORM_TYPE_SMTQTG,
                    CommonConstants.PLAT_FORM_TYPE_SHEIN,
                    CommonConstants.PLAT_FORM_TYPE_TIKTOK_QTG,
                    CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG)
    );

    public static final Set<String> KJ_NEED_ORDEREXT_SOURCE_LIST = new HashSet<>(
            Arrays.asList(
            CommonConstants.PLAT_FORM_TYPE_SHEIN)
    );

    public static String getSource(Trade trade) {
        return getSource(trade.getSource(), trade.getSubSource());
    }

    public static String getSource(String source, String subSource) {
        if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_SYS, source)
                && KJ_SOURCE_LIST.contains(subSource)
                && (Objects.equals(subSource, CommonConstants.PLAT_FORM_TYPE_TIKTOK_QTG) ||
                Objects.equals(subSource, CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG)
                || Objects.equals(subSource, CommonConstants.PLAT_FORM_TYPE_SMTQTG))) {
            return subSource;
        }
        return source;
    }
}

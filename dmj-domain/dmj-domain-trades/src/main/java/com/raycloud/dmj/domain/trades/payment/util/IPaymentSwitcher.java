package com.raycloud.dmj.domain.trades.payment.util;

import org.apache.log4j.Logger;

/**
 * @Description <pre>
 *  金额计算中存在一些通过配置进行计算的内容 但是底层domain包是无法获取到配置对象的,
 *  因此这里定义好接口 在上层PaymentCalculateSupports初始化时反向将接口实现注入到这里的instance,以达到读取配置的目的
 *
 * </pre>
 * @see com.raycloud.dmj.business.payment.support.PaymentCalculateSupports#afterPropertiesSet()
 * <AUTHOR>
 * @Date 2024-03-06
 */
public abstract class IPaymentSwitcher {

    private final Logger logger = Logger.getLogger(this.getClass());

    public static IPaymentSwitcher instance = null;

    /**
     * <pre>
     * 是否打印金额流转的明细日志
     *   商品 payAmount 分摊日志
     *   套件内金额分摊日志
     *   成本价获取日志
     * </pre>
     * @param companyId
     * @return
     */
    public abstract boolean isPrintMoneyDetailLog(Long companyId);

    public Integer getScale(Long companyId){
        new PaymentLogBuilder(companyId).append("使用默认精度位").printWarn(logger);
        return 2;
    }

    public static IPaymentSwitcher getInstance() {
        return instance;
    }
}

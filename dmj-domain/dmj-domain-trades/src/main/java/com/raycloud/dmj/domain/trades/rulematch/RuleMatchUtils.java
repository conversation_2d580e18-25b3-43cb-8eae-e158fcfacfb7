package com.raycloud.dmj.domain.trades.rulematch;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.pt.enums.SellerFlagKeyEnum;
import com.raycloud.dmj.domain.pt.express.ExpressSmartMatch;
import com.raycloud.dmj.domain.tag.TradeTagRule;
import com.raycloud.dmj.domain.trade.audit.AuditContext;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.orderitemtag.OrderItemTag;
import com.raycloud.dmj.domain.trade.rulematch.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther mengfanguang
 * @Date 2022/10/14
 */
public class RuleMatchUtils {

    public static final String VALUE_ONE = "1";

    public static final String VALUE_ZERO = "0";

    public static List<AiRule> transferAiRule(List<AutoAuditConfig> autoAuditConfigs, Boolean ignoreBeforeDate) {
        List<AiRule> aiRuleList = new ArrayList<>();
        for (AutoAuditConfig autoAuditConfig : autoAuditConfigs) {
            aiRuleList.add(transferAiRule(autoAuditConfig, ignoreBeforeDate));
        }
        return aiRuleList;
    }

    public static List<AiRule> transferAiRule(List<AutoAuditConfig> autoAuditConfigs, AuditContext auditContext) {
        List<AiRule> aiRuleList = new ArrayList<>();
        for (AutoAuditConfig autoAuditConfig : autoAuditConfigs) {
            aiRuleList.add(transferAiRule(autoAuditConfig, auditContext));
        }
        return aiRuleList;
    }

    public static AiRule transferAiRule(AutoAuditConfig autoAuditConfig, Boolean ignoreBeforeDate) {
        AiRule aiRule = new AiRule();
        aiRule.setId(autoAuditConfig.getId());
        aiRule.setUserIds(autoAuditConfig.getUserIds());
        aiRule.setRuleType(autoAuditConfig.getRuleType());
        aiRule.setExcludeStart(autoAuditConfig.getExcludeStart());
        aiRule.setExcludeEnd(autoAuditConfig.getExcludeEnd());
        aiRule.setBeforePayTime(autoAuditConfig.getBeforePayTime());
        aiRule.setIgnoreBeforeDate(ignoreBeforeDate);
        if (Objects.nonNull(autoAuditConfig.getAuditSysTrade()) && VALUE_ONE.equals(autoAuditConfig.getAuditSysTrade().toString())) {
            aiRule.setAuditSysTrade(Boolean.TRUE);
        }
        if (Objects.nonNull(autoAuditConfig.getAllowReAutoAudit()) && VALUE_ONE.equals(autoAuditConfig.getAllowReAutoAudit().toString())) {
            aiRule.setAllowReAutoAudit(Boolean.TRUE);
        }
        transConditions(autoAuditConfig.getConditions(), aiRule);
        return aiRule;
    }

    public static AiRule transferAiRule(AutoAuditConfig autoAuditConfig, AuditContext auditContext) {
        AiRule aiRule = transferAiRule(autoAuditConfig, auditContext.isIgnoreBeforeDate());
        aiRule.setOpEnum(auditContext.getOpEnum());
        aiRule.setItemNumExcludeVirtual(Objects.nonNull(auditContext.getTradeConfig()) ? auditContext.getTradeConfig().getItemNumExcludeVirtual() : 0);
        aiRule.setItemNumExcludeNonConsign(Objects.nonNull(auditContext.getTradeConfig()) ? auditContext.getTradeConfig().getItemNumExcludeNonConsign() : 0);
        aiRule.setItemNumExcludeAfterSendGoods(Objects.nonNull(auditContext.getTradeConfig()) && auditContext.getTradeConfig().isItemNumExcludeAfterSendGoods());
        return aiRule;
    }

    /**
     * conditions 转 AiRule的 (通用的）
     */
    public static void transConditions(List<SpelCondition> conditions, AiRule aiRule) {
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }
        for (SpelCondition condition : conditions) {
            String field = condition.getField();
            String operator = condition.getOperator();
            String value = condition.getValue();
            switch (field) {
                case SpelCondition.FIELD_USER_ID:                             //店铺
                    aiRule.setUserIds(ArrayUtils.toLongList(value));
                    break;
                case SpelCondition.FIELD_MATCH_QIMEN_SOURCES:
                    aiRule.setQimenSources(transCondition(condition));
                case SpelCondition.FIELD_ORDER_BRAND_IDS:                     //品牌
                    if (SpelCondition.OPERATOR_ASSIGN.equals(operator) && StringUtils.isNotBlank(value)) {
                        aiRule.setBrandIds(new HashSet<>(Arrays.asList(value.split(","))));
                    }
                    break;
                case SpelCondition.FIELD_ORDER_SUPPLIER_IDS:                     //供应商
                    if (SpelCondition.OPERATOR_ASSIGN.equals(operator) && StringUtils.isNotBlank(value)) {
                        aiRule.setSupplierIds(new HashSet<>(Arrays.asList(value.split(","))));
                    }
                    break;
                case SpelCondition.SUPPLIER_CATEGORY_MATCH:                     //供应商分类
                    aiRule.setSupplierCategory(transCondition(condition));
                    break;
                case SpelCondition.FIELD_MERGE_PROMPT_NOT_AUTO_AUDIT:          //是否有合单提示的订单不自动审核
                    if (SpelCondition.OPERATOR_EQUAL.equals(operator) && VALUE_ONE.equals(value)) {
                        aiRule.setMergePromptNotAutoAudit(VALUE_ONE);
                    } else {
                        aiRule.setMergePromptNotAutoAudit(VALUE_ZERO);
                    }
                    break;
                case SpelCondition.FIELD_TEMPLATE_IDS:                         //快递模版
                    if (SpelCondition.OPERATOR_CONTAIN.equals(operator) && StringUtils.isNotBlank(value)) {
                        aiRule.setExpressTemplates(new ArrayList<>(Arrays.asList(value.split(","))));
                    }
                    break;
                case SpelCondition.FIELD_LOGISTICS_COMPANY_IDS:
                    if (SpelCondition.OPERATOR_CONTAIN.equals(operator) && StringUtils.isNotBlank(value)) {
                        aiRule.setLogisticsCompanyIds(new ArrayList<>(Arrays.asList(value.split(","))));
                    }
                    break;
                case SpelCondition.FIELD_HANDLER_MESSAGE_OR_NOT:               //买家无留意或已处理
                case SpelCondition.FIELD_MESSAGE:                              //买家留言
                    aiRule.setBuyerMessageDetails(transCondition(condition));
                    break;
                case SpelCondition.FIELD_MEMO:
                case SpelCondition.FIELD_HANDLER_MEMO_OR_NOT:                  //卖家无备注或已处理
                    aiRule.setSellerMemoDetails(transCondition(condition));    //卖家备注
                    break;
                case SpelCondition.FIELD_FLAG:
                    aiRule.setSellerFlags(transCondition(condition));          //订单旗帜
                    break;
                case SpelCondition.FIELD_PAYMENT:                              //实付金额
                    aiRule.setPayment(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_INVOICE_NAME:                         //发票
                    aiRule.setInvoice(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_ISVIRTUAL_STR:                  //虚拟商品
                    aiRule.setOrderIsVirtual(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ITEM_NUM:                             //商品数量
                    aiRule.setItemNum(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_ITEM_KIND_NUM:                        //商品种类
                    aiRule.setItemKindNum(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_VOLUME:                               //体积
                    aiRule.setVolume(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_NET_WEIGHT:                           //重量
                    aiRule.setNetWeight(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_SYS_OUTER_IDS:                  //商家编码 or  系统商品-按sku-按款
                    aiRule.setSysOuterIds(transCondition(condition));
                    if (SpelCondition.OPERATOR_ASSIGN.equals(condition.getOperator()) || SpelCondition.OPERATOR_ONLY_ASSIGN.equals(condition.getOperator())) {
                        aiRule.getSysItemSkuIds().add(transSysItemSkuIdsCondition(condition));
                    } else {
                        aiRule.getExcludeSysItemSkuIds().add(transSysItemSkuIdsCondition(condition));
                    }
                    break;
                case SpelCondition.FIELD_GIFT:                                 //赠品
                    aiRule.setGift(transCondition(condition));
                    break;
                case SpelCondition.FIELD_TRADE_TAG:                            //标签
                case SpelCondition.TRADE_TAG_IDS:                              //标签（快递）
                    aiRule.setTag(transCondition(condition));
                    break;
                case SpelCondition.FIELD_TRADE_AREA:                           //地址
                    aiRule.setArea(transCondition(condition));
                    break;
                case SpelCondition.FIELD_DEST_SEND_NOT_AUTO_AUDIT:
                    aiRule.setDestSendNotAutoAudit(transCondition(condition));
                    break;
                case SpelCondition.FIELD_SELF_SEND_NOT_AUTO_AUDIT:
                    aiRule.setSelfSendNotAutoAudit(transCondition(condition));
                    break;
                case SpelCondition.FIELD_WAREHOUSE_IDS:                        //仓库编码
                    if (SpelCondition.OPERATOR_ASSIGN.equals(operator) && StringUtils.isNotBlank(value)) {
                        aiRule.setWarehouseIds(new HashSet<>(Arrays.asList(value.split(","))));
                    }
                    break;
                case SpelCondition.FIELD_CHECK_ITEM_SALE_PRICE_LOWER_LIMIT:     // 商品成交金额单价低于商品售价下限
                    aiRule.setCheckItemSalePriceLowerLimit("true".equalsIgnoreCase(value));
                    break;
                case SpelCondition.FIELD_POST_FEE:                                //订单运费
                    aiRule.setPostFee(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_ITEM_TAG_IDS:                            //商品标签 （快递新增的）
                    aiRule.setItemTag(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ITEM_MAX_LENGTH:                            //商品标签 （快递新增的）
                    aiRule.setItemMaxLength(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_BUYER_NICK:                              //买家昵称
                    aiRule.setBuyerNickDetail(transCondition(condition));
                    break;
                case SpelCondition.EXCLUDE_TIME:
                    aiRule.setExpressExcludeTime(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_TIMEOUT_ACTION_TIME:                   //发货时间
                    aiRule.setTimeoutActionTime(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_RECEIVER_NAME:                         //收件人
                    aiRule.setReceiverName(transCondition(condition));
                    break;
                case SpelCondition.FIELD_AUTHOR_NAME:                          //达人名称
                    aiRule.setAuthorName(transCondition(condition));
                    break;
                case SpelCondition.FIELD_AUTHOR_ID:                           //达人Id
                    aiRule.setAuthorId(transCondition(condition));
                    break;
                case SpelCondition.FIELD_FXG_DF_MALL_MASK_IDS:                //代发
                    aiRule.setFxgDfMall(transCondition(condition));
                    break;
                case SpelCondition.FIELD_RECEIVER_MOBILE:                     //手机号
                    aiRule.setReceiverMobile(transCondition(condition));
                    break;
                case SpelCondition.FIELD_TRADE_FROM:                          //订单来源
                    aiRule.setTradeFrom(transCondition(condition));
                    break;
                case SpelCondition.FIELD_TRADE_TYPE:                          //订单类型
                    aiRule.setTradeTypeDetail(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_CONTAIN_SUPPLIER_IDS:          //包含其中一个供应商id
                    aiRule.setSupplierIds(new HashSet<>(Arrays.asList(value.split(","))));
                    break;

                //商品相关的条件
                case SpelCondition.FIELD_ORDER_SKU_NUM:                           //单品sku数量
                    aiRule.setSingleSkuNum(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_ALL_SKU_NUM:                       //所有单品sku数量
                    aiRule.setAllSkuNum(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_SKU_AMOUNT:                        //单品sku实付金额
                    aiRule.setSingleSkuAmount(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_SKU_PRICE:                               //单品sku单价
                    aiRule.setSingleSkuPrice(transRangCondition(condition));
                    break;

                case SpelCondition.FIELD_SYSTEM_SKU_ID_KEYWORD:         //系统商品  规格商家编码关键词  和 系统商品 是 or 的关系
                    if (SpelCondition.OPERATOR_ASSIGN.equals(condition.getOperator()) || SpelCondition.OPERATOR_ONLY_ASSIGN.equals(condition.getOperator())) {
                        aiRule.getSysSkuIdKeywords().add(transCondition(condition));
                    } else {
                        aiRule.getExcludeSysSkuIdKeywords().add(transCondition(condition));
                    }
                    break;
                //平台商品条件
                case SpelCondition.FIELD_ORDER_PLATFORM_URL:           // 平台商品 商品链接    平台商品条件都是and关系
                    aiRule.getPlatformUrls().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ORDER_SKU_PROPERTIES_NAME:    // 平台商品 规格关键字
                    aiRule.getSkuPropertiesNames().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_PLATFORM_ITEM_NAME_KEYWORD:   // 平台商品 商品名称关键字
                    aiRule.getPlatformItemNameKeywords().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_KEYWORD_INTERCEPT_MATCH:   // 平台商品 商品名称关键字 截取匹配开关
                    aiRule.setKeywordInterceptMatch(Objects.equals("true", value));
                    break;
                case SpelCondition.FIELD_KEYWORD_INTERCEPT_CHAR:   // 平台商品 商品名称关键字 指定截取字符
                    aiRule.setKeywordInterceptChar(value);
                    break;
                case SpelCondition.FIELD_KEYWORD_INTERCEPT_REVERSE_NUM:   // 平台商品 商品名称关键字 逆向截取指定字符位数
                    aiRule.setKeywordInterceptReverseNum(Integer.valueOf(value));
                    break;
                case SpelCondition.FIELD_PLAT_SKU_OUTER_ID_KEY://平台商品 主商家编码关键字
                    aiRule.getPlatformOuterIidKeywords().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_PLATFORM_OUTER_SKU_ID_KEYWORD:// 平台商品 平台规格商家编码关键字
                    aiRule.getPlatformOuterSkuIdKeywords().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_PLATFORM_ITEM_NUM_I_ID:       // 平台商品 平台商品id
                    aiRule.getPlatformNumIids().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_PLATFORM_ITEM_SKU_ID:         // 平台商品 平台规格商家编码id
                    aiRule.getPlatformSkuIds().add(transCondition(condition));
                    break;
                case SpelCondition.FIELD_GROSS_PROFIT:                         //毛利润
                    aiRule.setGrossProfit(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_GROSS_PROFIT_RATE:                    //毛利率
                    aiRule.setGrossProfitRate(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_DISCOUNT_RATE:
                    aiRule.setDiscountRate(transRangCondition(condition));
                    break;
                case SpelCondition.FIELD_FX_COMPANY_IDS:                       //分销商
                    aiRule.setFxCompanyId(transCondition(condition));
                    break;
                case SpelCondition.FIELD_FX_USER_IDS:                       //分销商店铺
                    aiRule.setFxUserId(transCondition(condition));
                    break;
                case SpelCondition.FIELD_COOPERATION_NO:                       //常态合作码
                    aiRule.setCooperationNo(transCondition(condition));
                    break;
                case SpelCondition.FIELD_ADDRESS_WORD:                        //地址关键字
                    aiRule.setAddressWord(transCondition(condition));
                    break;
                case SpelCondition.FIELD_DIFFERENT_ADDRESS:                     //双地址
                    condition.setValue(condition.getDoubleAddressKeywords());
                    aiRule.setDoubleAddress(transCondition(condition));
                    break;
                case SpelCondition.FIELD_RECEIVER_ADDRESS:                        //地址
                    aiRule.setReceiverAddress(transCondition(condition));
                    break;
                case SpelCondition.FIELD_SHOP_NAME:                               //奇门店铺
                    aiRule.setShopName(transCondition(condition));
                case SpelCondition.MEMO_PRECEDENCE:                               // 是否留言备注优先
                    aiRule.setMemoPrecedence(StringUtils.isNotEmpty(value) && value.equals("1"));
                    break;
                case SpelCondition.FIELD_SALES_PRICE_COEFFICIENT:// 销售价系数
                    aiRule.setSalesPriceCoefficient(transRangCondition(condition));
                    break;
            }
        }
    }

    /**
     * spelCondition 转 AiRuleOperateDetail
     * 操作类型转换
     */
    private static AiRuleOperateDetail transCondition(SpelCondition spelCondition) {
        AiRuleOperateDetail aiRuleOperateDetail = new AiRuleOperateDetail();
        aiRuleOperateDetail.setField(spelCondition.getField());
        aiRuleOperateDetail.setOperator(spelCondition.getOperator());
        aiRuleOperateDetail.setValue(spelCondition.getValue());
        return aiRuleOperateDetail;
    }

    /**
     * spelCondition 转 AiRuleRangeDetail
     * 范围类型转换
     */
    private static AiRuleRangeDetail transRangCondition(SpelCondition spelCondition) {
        AiRuleRangeDetail aiRuleRangeDetail = new AiRuleRangeDetail();
        aiRuleRangeDetail.setField(spelCondition.getField());
        aiRuleRangeDetail.setOperator(spelCondition.getOperator());
        aiRuleRangeDetail.setMin(spelCondition.getMin());
        aiRuleRangeDetail.setMax(spelCondition.getMax());
        aiRuleRangeDetail.setFlag(spelCondition.getFlag());
        return aiRuleRangeDetail;
    }


    /**
     * 标签规则转换
     */
    public static List<AiRule> transferLabelAiRule(Map<TradeTagRule, String> expr2Rules, TradeConfig tradeConfig) {
        List<AiRule> aiRules = Lists.newArrayList();
        for (Map.Entry<TradeTagRule, String> entry : expr2Rules.entrySet()) {
            TradeTagRule rule = entry.getKey();
            //判断规则是否在指定时间内，如果不在不验证
            if (rule.getConditions() == null && rule.getConditions().isEmpty()) {
                continue;
            }
            AiRule aiRule = new AiRule();
            if (Objects.nonNull(rule.getTagsStartDate())) {
                aiRule.setExcludeStart(String.valueOf(rule.getTagsStartDate().getTime()));
            }
            if (Objects.nonNull(rule.getTagsEndDate())) {
                aiRule.setExcludeEnd(String.valueOf(rule.getTagsEndDate().getTime()));
            }
            aiRule.setSuitToSingleItem(rule.getSuitToSingleItem());
            aiRule.setItemNumExcludeVirtual(tradeConfig.getItemNumExcludeVirtual());
            aiRule.setItemNumExcludeNonConsign(tradeConfig.getItemNumExcludeNonConsign());
            aiRule.setItemNumExcludeAfterSendGoods(tradeConfig.isItemNumExcludeAfterSendGoods());
            transConditions(rule.getConditions(), aiRule);
            aiRule.setId(rule.getId());
            aiRule.setRuleName(rule.getName());
            aiRules.add(aiRule);
        }
        return aiRules;
    }

    /**
     * 标签-系统商品-按sku-按款转换-按分类
     *
     * @param spelCondition (spel条件)
     */
    private static AiRuleOperateDetail transSysItemSkuIdsCondition(SpelCondition spelCondition) {
        AiRuleOperateDetail aiRuleOperateDetail = new AiRuleOperateDetail();
        if (Objects.nonNull(spelCondition.getFlag()) && "sku".equals(spelCondition.getFlag())) {
            aiRuleOperateDetail.setField("sku");
        } else if (Objects.nonNull(spelCondition.getFlag()) && "cat".equals(spelCondition.getFlag())) {
            aiRuleOperateDetail.setField("cat");
        } else {
            aiRuleOperateDetail.setField("item");
        }
        aiRuleOperateDetail.setOperator(spelCondition.getOperator());
        aiRuleOperateDetail.setValue(spelCondition.getValue());
        return aiRuleOperateDetail;
    }

    /**
     * 标签转换（Trade->AiTrade）
     */
    public static List<AiTrade> transferAiLabelTrades(List<Trade> trades, Map<Long, List<Trade>> mergeTrades) {
        List<AiTrade> aiTrades = new ArrayList<>();
        for (Trade trade : trades) {
            aiTrades.add(transferAiTrade(trade, mergeTrades));
        }
        return aiTrades;
    }

    public static AiTrade transferAiTrade(Trade trade) {
        return transferAiTrade(trade, Collections.emptyMap());
    }

    /**
     * trade 转 AiTrade
     */
    public static AiTrade transferAiTrade(Trade trade, Map<Long, List<Trade>> mergeTrades) {
        List<Order> originOrders = TradeUtils.getOrders4Trade(trade);
        AiTrade aiTrade = new AiTrade();
        aiTrade.setSid(trade.getSid());
        aiTrade.setTid(trade.getTid());
        aiTrade.setUserId(trade.getUserId());
        aiTrade.setConvertType(trade.getConvertType());
        aiTrade.setBelongType(trade.getBelongType());
        aiTrade.setDestId(trade.getDestId());
        //供应商和品牌要取order的
        aiTrade.setBrands(new HashSet<>());
        aiTrade.setSupplierIds(new HashSet<>());
        aiTrade.setItemTagSet(new HashSet<>());
        aiTrade.setOuterIdSet(new HashSet<>());
        aiTrade.setOuterSkuIdSet(new HashSet<>());
        aiTrade.setUnAllocatedTrade(false);
        if (CollectionUtils.isNotEmpty(originOrders)) {
            for (Order order : originOrders) {
                if (CollectionUtils.isNotEmpty(order.getBrandIds())) {
                    aiTrade.getBrands().addAll(order.getBrandIds().stream().map(Objects::toString).collect(Collectors.toSet()));
                }
                if (CollectionUtils.isNotEmpty(order.getSupplierIds())) {

                    aiTrade.getSupplierIds().addAll(order.getSupplierIds().stream().map(Objects::toString).collect(Collectors.toSet()));
                }
                if (CollectionUtils.isNotEmpty(order.getOrderItemTags())) {
                    aiTrade.getItemTagSet().addAll(order.getOrderItemTags().stream()
                            .filter(orderItemTag -> Objects.equals(1, orderItemTag.getEnableStatus()))
                            .map(OrderItemTag::getItemTagId).collect(Collectors.toList()));
                }
                if (!Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                    aiTrade.getOuterIdSet().add(order.getSysOuterId());
                    aiTrade.getOuterSkuIdSet().add(order.getOuterSkuId());
                }
                //商品未匹配
                if ((order.getItemSysId() != null && order.getItemSysId() <= 0) || Trade.STOCK_STATUS_UNALLOCATED.equals(order.getStockStatus())) {
                    aiTrade.setUnAllocatedTrade(true);
                }
                if (Objects.nonNull(order.getOrderExt()) && Objects.nonNull(order.getOrderExt().getAuthorName())) {
                    aiTrade.getAuthorNames().add(order.getOrderExt().getAuthorName());
                }

                if (Objects.nonNull(order.getOrderExt()) && Objects.nonNull(order.getOrderExt().getAuthorId())) {
                    aiTrade.getAuthorIds().add(order.getOrderExt().getAuthorId());
                }

                if (Objects.nonNull(order.getOrderExt()) && StringUtils.isNotEmpty(order.getOrderExt().getAuthorNo())) {
                    aiTrade.getAuthorNos().add(order.getOrderExt().getAuthorNo());
                }

            }
            aiTrade.setOrders(originOrders);
        }
        aiTrade.setSource(trade.getSource());
        aiTrade.setSplitSid(trade.getSplitSid());
        aiTrade.setSplitSource(trade.getSplitSource());
        aiTrade.setSubSource(trade.getSubSource());
        aiTrade.setType(trade.getType());
        if (Objects.nonNull(trade.getTradeExt())) {
            aiTrade.setLogisticsCode(trade.getTradeExt().getLogisticsCode());
        }
        aiTrade.setIsAutoAudit(trade.getIsAutoAudit());
        aiTrade.setPayTime(trade.getPayTime());
        aiTrade.setCheckManualMergeCount(trade.getCheckManualMergeCount());
        aiTrade.setTemplateId(trade.getTemplateId());
        aiTrade.setTemplateType(trade.getTemplateType());
        aiTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());

        // 留言/备注/旗帜
        Set<String> buyerMessageSet = new HashSet<>();
        Set<String> sellerMemoSet = new HashSet<>();
        Set<Long> sellerFlagSet = new HashSet<>();

        if (mergeTrades.containsKey(trade.getSid())) {
            // 合单
            for (Trade mergeTrade : mergeTrades.get(trade.getSid())) {
                buyerMessageSet.add(mergeTrade.getBuyerMessage());
                sellerMemoSet.add(mergeTrade.getSellerMemo());
                // 旗帜
                if (Objects.isNull(mergeTrade.getSellerFlag())) {
                    sellerFlagSet.add(0L);
                } else {
                    sellerFlagSet.add(mergeTrade.getSellerFlag());
                }
            }
        }else {
            // 非合单
            buyerMessageSet.add(trade.getBuyerMessage());
            sellerMemoSet.add(trade.getSellerMemo());
            // 旗帜
            if (Objects.isNull(trade.getSellerFlag())) {
                sellerFlagSet.add(0L);
            } else {
                sellerFlagSet.add(trade.getSellerFlag());
            }
        }
        aiTrade.setBuyerMessage(trade.getBuyerMessage());
        aiTrade.setBuyerMessageSet(buyerMessageSet);
        aiTrade.setSellerMemo(trade.getSellerMemo());
        aiTrade.setSellerMemoSet(sellerMemoSet);
        if (Objects.isNull(trade.getSellerFlag())) {
            aiTrade.setSellerFlag(0L);
        } else {
            aiTrade.setSellerFlag(trade.getSellerFlag());
        }
        aiTrade.setSellerFlagSet(sellerFlagSet);
        aiTrade.setIsHandlerMessage(trade.getIsHandlerMessage());
        aiTrade.setIsHandlerMemo(trade.getIsHandlerMemo());

        //todo 订单实付金额，有疑问是不是这个金额
        aiTrade.setTradePayment(NumberUtils.str2Double(trade.getPayment()));
        aiTrade.setInvoiceName(trade.getInvoiceName());
        aiTrade.setOrderIsVirtualStr(transVirtualStr(trade));

        aiTrade.setItemNum(trade.getItemNum());
        aiTrade.setItemKindNum(trade.getItemKindNum());
        aiTrade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
        aiTrade.setVolume(trade.getVolume());
        aiTrade.setSysItemSkuIds(transSysItemSkuIds(trade));
        aiTrade.setContainGift(trade.getContainGift());
        aiTrade.setContainGift(CollectionUtils.isNotEmpty(TradeUtils.getGiftOrders4Trade(Collections.singletonList(trade))) ? Boolean.TRUE : Boolean.FALSE);
        aiTrade.setTagIds(StringUtils.isNotBlank(trade.getTagIds()) ? new ArrayList<>(Arrays.asList(trade.getTagIds().split(","))) : new ArrayList<>());
        aiTrade.setReceiverState(trade.getReceiverState());
        aiTrade.setReceiverCity(trade.getReceiverCity());
        aiTrade.setReceiverDistrict(trade.getReceiverDistrict());
        aiTrade.setReceiverStreet(trade.getReceiverStreet());
        aiTrade.setAuditTime(trade.getAuditTime());


        aiTrade.setSubSource(trade.getSubSource());
        aiTrade.setReceiverCountry(trade.getReceiverCountry());
        aiTrade.setReceiverAddress(trade.getReceiverAddress());

        aiTrade.setSourceId(trade.getSourceId());
        aiTrade.setTaobaoId(trade.getTaobaoId());

        aiTrade.setTmallDesignatedStoreName(PlatformUtils.isTmallDesignatedStoreName(trade));
        aiTrade.setPlatformWarehouse(trade.getTradeExt() == null ? "" : trade.getTradeExt().getStoreName());


        aiTrade.setType(trade.getType());
        aiTrade.setIsPresell(trade.getIsPresell());
        aiTrade.setSplitType(trade.getSplitType());
        aiTrade.setMergeType(trade.getMergeType());
        aiTrade.setIsUrgent(trade.getIsUrgent());
        aiTrade.setScalping(trade.getScalping());
        aiTrade.setIsStore(trade.getIsStore());
        aiTrade.setIsTmallDelivery(trade.getIsTmallDelivery());
        aiTrade.setBelongType(trade.getBelongType());
        aiTrade.setConvertType(trade.getConvertType());
        aiTrade.setSource(trade.getSource());
        aiTrade.setSubSource(trade.getSubSource());
        aiTrade.setCheckManualMergeCount(trade.getCheckManualMergeCount());
        aiTrade.setTradeTypeMap(trade.getTradeTypeMap());
        aiTrade.setWarehouseId(trade.getWarehouseId());

        //标签
        aiTrade.setBuyerNick(trade.getBuyerNick());
        aiTrade.setOpenUid(trade.getOpenUid());
        aiTrade.setTimeoutActionTime(trade.getTimeoutActionTime());
        aiTrade.setReceiverName(trade.getReceiverName());
        aiTrade.setReceiverMobile(trade.getReceiverMobile());
        aiTrade.setTradeFrom(trade.getTradeFrom());
        aiTrade.setExceptData(TradeExceptUtils.getTradeExceptData(trade).copier());
        aiTrade.setSubTradeExceptDatas(trade.getSubTradeExceptDatas());
        aiTrade.setExcep(trade.getExcep());
        aiTrade.setItemExcep(trade.getItemExcep());
        if (trade instanceof TbTrade) {
            aiTrade.setSellerNick(trade.getSellerNick());
        }
        aiTrade.setPostFee(StringUtils.isNoneBlank(trade.getPostFee()) ? Double.valueOf(trade.getPostFee()) : null);
        aiTrade.setGrossProfit(trade.getGrossProfit() != null && !Objects.equals(trade.getGrossProfit(), 0D) ? trade.getGrossProfit() : TradeUtils.calculateGrossProfit(trade));
        aiTrade.setGrossProfitRate((trade.getGrossProfit() != null && StringUtils.isNotEmpty(StringUtils.trimToEmpty(trade.getPayment())) && NumberUtils.str2Double(trade.getPayment()) != 0) ? (trade.getGrossProfit() / NumberUtils.str2Double(trade.getPayment())) * 100 : (TradeUtils.calculateGrossProfit(trade) / NumberUtils.str2Double(trade.getPayment())) * 100);
        aiTrade.setCooperationNo((trade.getTradeExt() == null ? "" : trade.getTradeExt().getCooperationNo()));
        aiTrade.setFxgMallMaskIds(trade.getTradeExt() == null ? null : new HashSet<>(Lists.newArrayList(trade.getTradeExt().getMallMaskId())));
        aiTrade.setTradeExt(trade.getTradeExt());
        return aiTrade;
    }


    /**
     * 转化系统商品Id;
     */
    private static Set<String> transSysItemSkuIds(Trade trade) {
        List<Order> originOrders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(originOrders)) {
            return new HashSet<>();
        }
        Set<String> SysItemSkuIds = new HashSet<>();
        for (Order order : originOrders) {
            if(order.getItemSysId() != null && order.getItemSysId() > 0){
                SysItemSkuIds.add(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()));
            }
        }
        return SysItemSkuIds;
    }


    /**
     * 订单虚拟商品字符串
     */
    private static String transVirtualStr(Trade trade) {
        StringBuilder orderIsVirtualStr = new StringBuilder();
        List<Order> originOrders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(originOrders)) {
            return orderIsVirtualStr.toString();
        }
        for (Order order : originOrders) {
            orderIsVirtualStr.append(order.getIsVirtual()).append(",");

        }
        return orderIsVirtualStr.toString();
    }


    /**
     * 快递规则转换
     */
    public static List<AiRule> transExpressRules(List<ExpressSmartMatch> expr2Rules, TradeConfig tradeConfig, boolean isNewTemplate) {
        List<AiRule> aiRules = Lists.newArrayList();
        boolean isFreightPrecedence = expr2Rules.stream().anyMatch(rule -> rule.getFreightPrecedence() != null && rule.getFreightPrecedence() == 1);
        for (ExpressSmartMatch expressSmartMatch : expr2Rules) {
            AiRule aiRule = new AiRule();
            aiRule.setId(expressSmartMatch.getId());
            aiRule.setNewTemplateRule(isNewTemplate);
            aiRule.setCompanyId(expressSmartMatch.getCompanyId());
            aiRule.setLogisticsCompanyId(expressSmartMatch.getLogisticsCompanyId());
            aiRule.setTemplateId(expressSmartMatch.getTemplateId());
            aiRule.setTemplateType(expressSmartMatch.getTemplateType());
            aiRule.setRuleName(expressSmartMatch.getRuleName());
            aiRule.setPercent(expressSmartMatch.getPercent());
            //上限
            aiRule.setUpperLimit(expressSmartMatch.getUpperLimit());
            aiRule.setFreightPrecedence(isFreightPrecedence);
            aiRule.setPlatformMatchItemAction(expressSmartMatch.getPlatformMatchItemAction());
            aiRule.setMatchItemAction(expressSmartMatch.getMatchItemAction());
            aiRule.setSecondUserStr(expressSmartMatch.getSecondUserStr());
            aiRule.setExpressUserIds(expressSmartMatch.getUserIds());
            aiRule.setExpressId(expressSmartMatch.getExpressId());
            if (StringUtils.isNotEmpty(expressSmartMatch.getSourceIds())) {
                aiRule.setSourceIdSet(Arrays.stream(expressSmartMatch.getSourceIds().split(",")).map(String::trim).collect(Collectors.toSet()));
            }
            if (StringUtils.isNotEmpty(expressSmartMatch.getDestIds())) {
                aiRule.setDestIdSet(Arrays.stream(expressSmartMatch.getDestIds().split(",")).map(String::trim).collect(Collectors.toSet()));
            }
            aiRule.setIsOnlyMatchCod(expressSmartMatch.getIsOnlyMatchCod());
            aiRule.setNotSendRuleDTO(expressSmartMatch.getNotSendRuleDTO());
            aiRule.setCanSendRuleDTO(expressSmartMatch.getCanSendRuleDTO());
            aiRule.setPointExpressId(expressSmartMatch.getPointExpressId());
            aiRule.setAddressKey(expressSmartMatch.getAddressKey());
            aiRule.setStateBinderStr(expressSmartMatch.getStateBinderStr());
            aiRule.setSellerFlags(tradeExpressSellerFlag(expressSmartMatch.getSellerFlagKey(), expressSmartMatch.getSellerFlagsMatch()));
            aiRule.setSellerMemoDetails(transExpressCondition(SpelCondition.OPERATOR_CONTAIN, expressSmartMatch.getSellerMemo()));
            aiRule.setAddressWord(transExpressCondition((1 == expressSmartMatch.getAddressKey()) ? SpelCondition.OPERATOR_CONTAIN : SpelCondition.OPERATOR_NOT_CONTAIN, expressSmartMatch.getAddressWord()));
            aiRule.setBuyerMessageDetails(transExpressCondition(SpelCondition.OPERATOR_CONTAIN, expressSmartMatch.getBuyerMessage()));
            aiRule.setNetWeight(transExpressRangeCondition(expressSmartMatch.getMinWeight(), expressSmartMatch.getMaxWeight()));
            aiRule.setVolume(transExpressRangeCondition(expressSmartMatch.getMinVolume(), expressSmartMatch.getMaxVolume()));
            aiRule.setItemKindNum(transExpressRangeIntegerCondition(expressSmartMatch.getMinItemKindNum(), expressSmartMatch.getMaxItemKindNum()));
            aiRule.setItemNum(transExpressRangeIntegerCondition(expressSmartMatch.getMinItemNum(), expressSmartMatch.getMaxItemNum()));
            aiRule.setTradeTypeDetail(transExpressCondition(SpelCondition.OPERATOR_CONTAIN, expressSmartMatch.getTradeTypes()));
            aiRule.setPayment(transExpressRangeCondition(expressSmartMatch.getMinPayment(), expressSmartMatch.getMaxPayment()));
            if (!StringUtils.isBlank(expressSmartMatch.getFxTaobaoIds())) {
                aiRule.setFxTaobaoIdSet(Arrays.stream(expressSmartMatch.getFxTaobaoIds().split(",")).map(String::trim).collect(Collectors.toSet()));
            }
            aiRule.setPoisonExpressCode(expressSmartMatch.getPoisonExpressCode());
            aiRule.setPointExpressId(expressSmartMatch.getPointExpressId());
            aiRule.setSuitToSingleItem(expressSmartMatch.getSuitItemMatchType());
            aiRule.setExcludeAreas(expressSmartMatch.getExcludeAreas());
            aiRule.setItemNumExcludeVirtual(tradeConfig.getItemNumExcludeVirtual());
            aiRule.setItemNumExcludeAfterSendGoods(tradeConfig.isItemNumExcludeAfterSendGoods());
            aiRule.setPoisonExpressCode(expressSmartMatch.getPoisonExpressCode());
            aiRule.setPointExpressId(expressSmartMatch.getPointExpressId());
            aiRule.setItemNumExcludeNonConsign(tradeConfig.getItemNumExcludeNonConsign());
            List<SpelCondition> conditions = expressSmartMatch.getConditions();
            transConditions(conditions, aiRule);
            aiRules.add(aiRule);
        }
        return aiRules;
    }



    /**
     * spelCondition 转 AiRuleOperateDetail
     * 操作类型转换
     */
    private static AiRuleOperateDetail transExpressCondition(String operator, String value) {
        if (StringUtils.isEmpty(operator)|| StringUtils.isEmpty(value)) {
            return null;
        }
        AiRuleOperateDetail aiRuleOperateDetail = new AiRuleOperateDetail();
        aiRuleOperateDetail.setOperator(operator);
        aiRuleOperateDetail.setValue(value);
        return aiRuleOperateDetail;
    }

    private static AiRuleRangeDetail transExpressRangeIntegerCondition(Integer min, Integer max) {
        if (min != null && min == 0 && max != null && max ==0) {
            return null;
        }
        AiRuleRangeDetail aiRuleRangeDetail = new AiRuleRangeDetail();
        aiRuleRangeDetail.setMin(String.valueOf(min));
        aiRuleRangeDetail.setMax(String.valueOf(max));
        return aiRuleRangeDetail;
    }

    /**
     * spelCondition 转 AiRuleOperateDetail
     * 操作类型转换
     */
    private static AiRuleRangeDetail transExpressRangeCondition(Double min, Double max) {
        if (min != null && min == 0 && max != null && max ==0) {
            return null;
        }
        AiRuleRangeDetail aiRuleRangeDetail = new AiRuleRangeDetail();
        aiRuleRangeDetail.setMin(String.valueOf(min));
        aiRuleRangeDetail.setMax(String.valueOf(max));
        return aiRuleRangeDetail;
    }


    /**
     * spelCondition 转 AiRuleOperateDetail
     * 操作类型转换
     */
    private static AiRuleOperateDetail tradeExpressSellerFlag(Integer key, String value) {
        if (null == key || SellerFlagKeyEnum.NONE.getValue().equals(key) || StringUtils.isBlank(value)) {
            return null;
        }
        AiRuleOperateDetail aiRuleOperateDetail = new AiRuleOperateDetail();
        aiRuleOperateDetail.setField("sellerFlag");
        if (key == 1) {
            aiRuleOperateDetail.setOperator(SpelCondition.OPERATOR_CONTAIN);
        } else {
            aiRuleOperateDetail.setOperator(SpelCondition.OPERATOR_NOT_CONTAIN);
        }
        //无旗帜存的-1，订单是0。兼容。
        if (value.contains("-1")) {
            value = value + ",0";
        }
        aiRuleOperateDetail.setValue(value);
        return aiRuleOperateDetail;
    }





    /**
     *
     */
    public static List<AiTrade> transferAiExpressTrades(List<Trade> trades) {
        List<AiTrade> aiTrades = new ArrayList<>();
        for (Trade trade : trades) {
            AiTrade aiTrade = transferAiTrade(trade);
            aiTrade.setTmallDesignatedStoreName(trade.isTmallDesignatedLogistics());
            aiTrade.setBackTemplateList(trade.getBackTemplateList());
            aiTrade.setTradeExt(trade.getTradeExt());
            aiTrade.setAxExpressMatch(trade.getPlatformRecommendation() != null);
            aiTrades.add(aiTrade);
        }
        return aiTrades;
    }


}

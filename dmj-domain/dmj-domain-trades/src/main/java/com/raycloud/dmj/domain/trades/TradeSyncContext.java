package com.raycloud.dmj.domain.trades;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2023/11/13 2:27 下午
 * @Description:
 */
@Deprecated
@Data
public class TradeSyncContext implements Serializable {
    private static final long serialVersionUID = 3649431195630756787L;
    boolean isManualHandlerPlatItemChangeExcept = false;
    boolean onlyHandlerOne2one = true;
}

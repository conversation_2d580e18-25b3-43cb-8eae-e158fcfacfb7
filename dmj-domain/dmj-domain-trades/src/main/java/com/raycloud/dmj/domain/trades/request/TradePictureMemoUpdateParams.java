package com.raycloud.dmj.domain.trades.request;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2022_09_21 16:02
 */
public class TradePictureMemoUpdateParams implements Serializable {
    private Long sid;
    private Long saleTradeId;//批发收银-销货单单号
    private List<String> tradePictureMemoUris;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public List<String> getTradePictureMemoUris() {
        return tradePictureMemoUris;
    }

    public void setTradePictureMemoUris(List<String> tradePictureMemoUris) {
        this.tradePictureMemoUris = tradePictureMemoUris;
    }

    public Long getSaleTradeId() {
        return saleTradeId;
    }

    public void setSaleTradeId(Long saleTradeId) {
        this.saleTradeId = saleTradeId;
    }
}

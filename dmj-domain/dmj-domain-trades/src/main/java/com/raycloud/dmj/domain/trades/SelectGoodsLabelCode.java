package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * @description: 跨境全托管商品标签码
 * @author: zhaojianbo
 * @time: 2023/5/22 16:17
 */
public class SelectGoodsLabelCode implements Serializable {

    private Long bgProdSkcId;
    private Long bgProdSkuId;
    private String clothesSpec;
    private Boolean isClothes;
    private Long labelCode;
    private String madeIn;
    private String spec;

    public Long getBgProdSkcId() {
        return bgProdSkcId;
    }

    public void setBgProdSkcId(Long bgProdSkcId) {
        this.bgProdSkcId = bgProdSkcId;
    }

    public Long getBgProdSkuId() {
        return bgProdSkuId;
    }

    public void setBgProdSkuId(Long bgProdSkuId) {
        this.bgProdSkuId = bgProdSkuId;
    }

    public String getClothesSpec() {
        return clothesSpec;
    }

    public void setClothesSpec(String clothesSpec) {
        this.clothesSpec = clothesSpec;
    }

    public Boolean getClothes() {
        return isClothes;
    }

    public void setClothes(Boolean clothes) {
        isClothes = clothes;
    }

    public Long getLabelCode() {
        return labelCode;
    }

    public void setLabelCode(Long labelCode) {
        this.labelCode = labelCode;
    }

    public String getMadeIn() {
        return madeIn;
    }

    public void setMadeIn(String madeIn) {
        this.madeIn = madeIn;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }
}

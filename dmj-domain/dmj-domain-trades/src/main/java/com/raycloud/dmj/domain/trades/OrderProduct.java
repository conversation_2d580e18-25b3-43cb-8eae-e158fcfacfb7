package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderProduct extends Model {

    private static final long serialVersionUID = 4351161983713644152L;

    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 子订单id
     */
    private Long orderId;

    /**
     * 生产批次
     */
    private String batchNo;

    /**
     * 生产日期
     */

    private Date productTime;

    /**
     * 生产数量
     */
    private Integer num;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    private String msg;
}

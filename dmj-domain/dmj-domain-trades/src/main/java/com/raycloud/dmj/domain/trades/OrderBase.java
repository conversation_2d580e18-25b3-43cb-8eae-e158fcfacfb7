package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 子订单模型基类
 * @Date 2022/2/21 14:25
 * @Created 杨恒
 */
@Setter
@Getter
public abstract class OrderBase extends Model {

    private static final long serialVersionUID = -5418788369025220480L;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 子订单系统编号
     */
    private Long id;

    /**
     * 原子订单系统编号
     */
    private Long oldId;

    /**
     * 子订单平台编
     */
    private Long oid;

    /**
     * 所属主订单的系统编号
     */
    private Long sid;

    /**
     * 原系统单号（拆单使用）
     */
    private Long oldSid;

    /**
     * 所属订单的平台编号
     */
    private String tid;


}

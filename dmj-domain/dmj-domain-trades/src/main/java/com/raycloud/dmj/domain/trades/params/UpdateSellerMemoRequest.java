package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_07_19 17:47
 */
public class UpdateSellerMemoRequest implements Serializable {
    public static final long version = 202107019L;

    public Staff staff;
    public String memo;
    public Long flag;
    public boolean append;
    public boolean handleMergeTrade=true;//是否处理(上传备注)合单里面的其他订单。默认处理
    public Long[] sids;
    /**
     * 奇门助手单、供销单是否同步给分销商
     */
    public Boolean notifyFx;
}

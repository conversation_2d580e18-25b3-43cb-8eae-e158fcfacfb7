package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 上传发货记录查询参数类
 * <AUTHOR>
 * @version 1.0
 * @date 2017-04-28 10:24
 */
public class ConsignRecordQueryParams extends AbstractTemplateQueryParam {

    private static final long serialVersionUID = -9144769348376684270L;
    /**
     * 需要查询的字段
     */
    private String fields;
    /**
     * 系统订单号
     */
    private String sid;
	/**
	 * 多个系统订单号
	 */
	private String sids;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 发货时使用的快递单号
     */
    private String outSid;

    /**
     * 店铺用户ID
     */
    private String userId;

    /**
     * 订单所在仓库ID
     */
    private String warehouseId;

    /**
     * 查询时间类型
     */
    private String timeType;

    /**
     * 查询起始时间戳
     */
    private Long startTime;
    private Date startDate;

    /**
     * 查询结束时间戳
     */
    private Long endTime;
    private Date endDate;

    /**
     * null 查询所有，1 查询上传出错的， 0 查询上传成功的
     */
    private Integer isError;

    /**
     * "deductDetail" 查询按单付费发货扣费明细
     */
    private String queryFlag;

    /**
     * 分页对象
     */
    private Page page;

    /**
     * 排序对象
     */
    private Sort sort;

    /**
     * 物流异常类型
     * @return
     */
    private String logisticsExceptType;

    /**
     * 物流状态
     * @return
     */
    private String logisticsStatus;

    private Date tradePay;

    private Long companyId;

    /**
     * 错误说明信息
     */
    private String errorDesc;

    /**
     * 上传异常类型id,多个以逗号隔开
     */
    private String errorType;

    /**
     * 是否截单
     */
    private Integer isCut;

    /**
     * 平台状态, 多个状态以逗号分割
     */
    private String status;


	public String getSids() {
		return sids;
	}

	public void setSids(String sids) {
		this.sids = sids;
	}

	public String getFields() {
        return fields;
    }

    public ConsignRecordQueryParams setFields(String fields) {
        this.fields = fields;
        return this;
    }


    public String getSid() {
        return sid;
    }

    public ConsignRecordQueryParams setSid(String sid) {
        this.sid = sid;
        return this;
    }

    public String getTid() {
        return tid;
    }

    public ConsignRecordQueryParams setTid(String tid) {
        this.tid = tid;
        return this;
    }

    public String getOutSid() {
        return outSid;
    }

    public ConsignRecordQueryParams setOutSid(String outSid) {
        this.outSid = outSid;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public ConsignRecordQueryParams setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public ConsignRecordQueryParams setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getTimeType() {
        return timeType;
    }

    public ConsignRecordQueryParams setTimeType(String timeType) {
        this.timeType = timeType;
        return this;
    }

    public Long getStartTime() {
        return startTime;
    }

    public ConsignRecordQueryParams setStartTime(Long startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getStartDate() {
        if (startDate == null && (startTime != null && startTime > 0)) {
            startDate = new Date(startTime);
        }
        return startDate;
    }

    public ConsignRecordQueryParams setStartDate(Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public Long getEndTime() {
        return endTime;
    }

    public ConsignRecordQueryParams setEndTime(Long endTime) {
        this.endTime = endTime;
        return this;
    }

    public Date getEndDate() {
        if (endDate == null && (endTime != null && endTime > 0)) {
            endDate = new Date(endTime);
        }
        return endDate;
    }

    public ConsignRecordQueryParams setEndDate(Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public Integer getIsError() {
        return isError;
    }

    public ConsignRecordQueryParams setIsError(Integer isError) {
        this.isError = isError;
        return this;
    }

    public String getQueryFlag() {
        return queryFlag;
    }

    public ConsignRecordQueryParams setQueryFlag(String queryFlag) {
        this.queryFlag = queryFlag;
        return this;
    }

    public Page getPage() {
        return page;
    }

    public ConsignRecordQueryParams setPage(Page page) {
        this.page = page;
        return this;
    }

    public Sort getSort() {
        return sort;
    }

    public ConsignRecordQueryParams setSort(Sort sort) {
        this.sort = sort;
        return this;
    }


    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public Date getTradePay() {
        return tradePay;
    }

    public void setTradePay(Date tradePay) {
        this.tradePay = tradePay;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public Integer getIsCut() {
        return isCut;
    }

    public void setIsCut(Integer isCut) {
        this.isCut = isCut;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

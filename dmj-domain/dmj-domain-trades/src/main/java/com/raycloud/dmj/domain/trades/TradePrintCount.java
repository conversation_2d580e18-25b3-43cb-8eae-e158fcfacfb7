package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

/**
 * 有关订单打印的数量统计
 * <AUTHOR>
 *
 */
public class TradePrintCount implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8598845175466757597L;

	/**
	 * 快递单打印的数量
	 */
	private Long countOfPringExpress;
	
	/**
	 * 发货单打印的数量
	 */
	private Long countOfPringDeliver;
	
	/**
	 * 剩余未打快递单数量
	 */
	private Long countOfUnprintExpress;
	
	/**
	 * 剩余未打发货单数量
	 */
	private Long countOfUnprintDeliver;
	
	/**
	 * 未发货的订单数量
	 */
	private Long countOfUndeliver;
	
	/**
	 * 时间间隔，例如1表示近一天
	 */
	private Long dayInterval;

	public Long getCountOfPringExpress() {
		return countOfPringExpress;
	}

	public TradePrintCount setCountOfPringExpress(Long countOfPringExpress) {
		this.countOfPringExpress = countOfPringExpress;
		return this;
	}

	public Long getCountOfPringDeliver() {
		return countOfPringDeliver;
	}

	public TradePrintCount setCountOfPringDeliver(Long countOfPringDeliver) {
		this.countOfPringDeliver = countOfPringDeliver;
		return this;
	}

	public Long getCountOfUnprintExpress() {
		return countOfUnprintExpress;
	}

	public TradePrintCount setCountOfUnprintExpress(Long countOfUnprintExpress) {
		this.countOfUnprintExpress = countOfUnprintExpress;
		return this;
	}

	public Long getCountOfUnprintDeliver() {
		return countOfUnprintDeliver;
	}

	public TradePrintCount setCountOfUnprintDeliver(Long countOfUnprintDeliver) {
		this.countOfUnprintDeliver = countOfUnprintDeliver;
		return this;
	}

	public Long getCountOfUndeliver() {
		return countOfUndeliver;
	}

	public TradePrintCount setCountOfUndeliver(Long countOfUndeliver) {
		this.countOfUndeliver = countOfUndeliver;
		return this;
	}

	public Long getDayInterval() {
		return dayInterval;
	}

	public TradePrintCount setDayInterval(Long dayInterval) {
		this.dayInterval = dayInterval;
		return this;
	}
}

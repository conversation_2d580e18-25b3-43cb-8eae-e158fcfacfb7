package com.raycloud.dmj.domain.trades;

import java.util.*;

import com.google.common.collect.Lists;

import java.io.Serializable;

/**
 * 采购快速收货打印配置
 *
 * <AUTHOR>
 * Date    2019-06-26
 */
public class PurchasePrintConfig implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;
    
    public static List<Integer> completePrintList = Lists.newArrayList(1,2,4,8);

    /**
     * id
     */
    private Long id;
    /**
     * company_id
     */
    private Long companyId;
    /**
     * staff_id
     */
    private Long staffId;
    /**
     * created
     */
    private Date created;
    /**
     * modified
     */
    private Date modified;
    /**
     * 1 正常 0 删除
     */
    private Integer enableStatus;
    /**
     * 保存收货数时打印设置  0 不打印 1 打印商家编码 2 打印吊牌 3 打印商家编码和吊牌
     */
    private Integer saveNumberPrint;
    /**
     * 完成时打印设置  0001 (1:打印商家编码) 0010(2:打印吊牌) 0100(4:打印箱码) 1000(8:打印装箱清单) 保存时用 或运算符(|) 计算好保存  查询时用 与运算符(&) 计算,得知是否勾选了
     */
    private Integer completePrint;
    
    /**
     * 完成时打印设置(字符串 ,分隔) 1:打印商家编码 2:打印吊牌 4:打印箱码 8:打印装箱清单
     */
    private String completePrintStr;
    
    /**
     * 商家编码模板id
     */
    private Long outerTemplateId;
    /**
     * 商家编码打印机id
     */
    private Long outerPrinterId;
    /**
     * 商家编码打印机名称
     */
    private String outerPrinterName;
    /**
     * 吊牌模板id
     */
    private Long tagTemplateId;
    /**
     * 吊牌打印机id
     */
    private Long tagPrinterId;
    /**
     * 吊牌打印机名称
     */
    private String tagPrinterName;
    
    /**
     * 打印机配置
     */
    private String printerConfig;

    /**
     * 添加商品弹窗确认时 1 、勾选 0、不勾选
     */
    private Integer dialogConfirm;

    /**
     * 录入商品后：0、不勾选,1、打印编码,2、打印吊牌,3、打印编码和吊牌
     */
    private Integer saveItemPrint;

    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return companyId company_id
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * @param companyId company_id
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * @return staffId staff_id
     */
    public Long getStaffId() {
        return staffId;
    }

    /**
     * @param staffId staff_id
     */
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    /**
     * @return created created
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created created
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified modified
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified modified
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 1 正常 0 删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 1 正常 0 删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return saveNumberPrint 保存收货数时打印设置  0 不打印 1 打印商家编码 2 打印吊牌 3 打印商家编码和吊牌
     */
    public Integer getSaveNumberPrint() {
        return saveNumberPrint;
    }

    /**
     * @param saveNumberPrint 保存收货数时打印设置  0 不打印 1 打印商家编码 2 打印吊牌 3 打印商家编码和吊牌
     */
    public void setSaveNumberPrint(Integer saveNumberPrint) {
        this.saveNumberPrint = saveNumberPrint;
    }

    /**
     * @return completePrint 完成时打印设置  0 不打印 1 打印商家编码 2 打印吊牌 3 打印商家编码和吊牌
     */
    public Integer getCompletePrint() {
        return completePrint;
    }

    /**
     * @param completePrint 完成时打印设置  0 不打印 1 打印商家编码 2 打印吊牌 3 打印商家编码和吊牌
     */
    public void setCompletePrint(Integer completePrint) {
        this.completePrint = completePrint;
    }

    public String getCompletePrintStr() {
        return completePrintStr;
    }

    public void setCompletePrintStr(String completePrintStr) {
        this.completePrintStr = completePrintStr;
    }

    /**
     * @return outerTemplateId 商家编码模板id
     */
    public Long getOuterTemplateId() {
        return outerTemplateId;
    }

    /**
     * @param outerTemplateId 商家编码模板id
     */
    public void setOuterTemplateId(Long outerTemplateId) {
        this.outerTemplateId = outerTemplateId;
    }

    /**
     * @return outerPrinterId 商家编码打印机id
     */
    public Long getOuterPrinterId() {
        return outerPrinterId;
    }

    /**
     * @param outerPrinterId 商家编码打印机id
     */
    public void setOuterPrinterId(Long outerPrinterId) {
        this.outerPrinterId = outerPrinterId;
    }

    /**
     * @return outerPrinterName 商家编码打印机名称
     */
    public String getOuterPrinterName() {
        return outerPrinterName;
    }

    /**
     * @param outerPrinterName 商家编码打印机名称
     */
    public void setOuterPrinterName(String outerPrinterName) {
        this.outerPrinterName = outerPrinterName;
    }

    /**
     * @return tagTemplateId 吊牌模板id
     */
    public Long getTagTemplateId() {
        return tagTemplateId;
    }

    /**
     * @param tagTemplateId 吊牌模板id
     */
    public void setTagTemplateId(Long tagTemplateId) {
        this.tagTemplateId = tagTemplateId;
    }

    /**
     * @return tagPrinterId 吊牌打印机id
     */
    public Long getTagPrinterId() {
        return tagPrinterId;
    }

    /**
     * @param tagPrinterId 吊牌打印机id
     */
    public void setTagPrinterId(Long tagPrinterId) {
        this.tagPrinterId = tagPrinterId;
    }

    /**
     * @return tagPrinterName 吊牌打印机名称
     */
    public String getTagPrinterName() {
        return tagPrinterName;
    }

    /**
     * @param tagPrinterName 吊牌打印机名称
     */
    public void setTagPrinterName(String tagPrinterName) {
        this.tagPrinterName = tagPrinterName;
    }

    public String getPrinterConfig() {
        return printerConfig;
    }

    public void setPrinterConfig(String printerConfig) {
        this.printerConfig = printerConfig;
    }

    public Integer getDialogConfirm() {
        return dialogConfirm;
    }

    public void setDialogConfirm(Integer dialogConfirm) {
        this.dialogConfirm = dialogConfirm;
    }

    public Integer getSaveItemPrint() {
        return saveItemPrint;
    }

    public void setSaveItemPrint(Integer saveItemPrint) {
        this.saveItemPrint = saveItemPrint;
    }
}
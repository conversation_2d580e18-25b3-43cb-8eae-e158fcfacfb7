package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.enums.OrderOpeartEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import org.apache.log4j.Logger;

import java.util.Objects;

/**
 * @description:
 * @author: pxh
 * @create: 2021-07-29 17:47
 **/
public interface IPaymentLinkStrategy {

    Logger LOGGER = Logger.getLogger(IPaymentLinkStrategy.class);

    String DISCOUNT_FEE_NEGATIVE_FORMATTER = "当前订单优惠金额为负数,id:[{%s}] , tid:[{%s}] , sid:[{%s}] , price:[{%s}] , payment:[{%s}] , totalFee:[{%s}] , discountFee:[{%s}]";

    PaymentLinkRespDTO insertOrder(PaymentLinkReqDTO paymentLinkReqDTO);

    PaymentLinkRespDTO updateOrder(PaymentLinkReqDTO paymentLinkReqDTO);

    PaymentLinkRespDTO deleteOrder(PaymentLinkReqDTO paymentLinkReqDTO);

    default PaymentLinkRespDTO getPaymentLinkDto(PaymentLinkReqDTO paymentLinkReqDTO) {
        if(paymentLinkReqDTO.getOrder() == null){
            throw new IllegalArgumentException("order不能为空");
        }
        if(paymentLinkReqDTO.getOpenLinkConfig() == null){
            throw new IllegalArgumentException("金额联动策略不能为空");
        }
        if(paymentLinkReqDTO.getOrder().getOrderOpeart() == null){
            throw new IllegalArgumentException("订单操作类型不能为空");
        }
        OrderOpeartEnum orderOpeart = paymentLinkReqDTO.getOrder().getOrderOpeart();
        if (OrderOpeartEnum.DELETE.equals(orderOpeart)) {
            return deleteOrder(paymentLinkReqDTO);
        }
        if (OrderOpeartEnum.UPDATE.equals(orderOpeart)) {
            return updateOrder(paymentLinkReqDTO);
        }
        if (OrderOpeartEnum.INSERT.equals(orderOpeart)) {
            return insertOrder(paymentLinkReqDTO);
        }
        throw new IllegalArgumentException("未实现金额联动相关服务 orderOpeart:[{" + orderOpeart.name() + "}]");
    }

    boolean support(OpenLinkConfigEnum openLinkConfigEnum);

    /**
     * 金额联动
     *
     * @param: order
     * @return:
     */
    default PaymentLinkRespDTO commonPaymentLink(Order order) {
        PaymentUtils.calculateOrder(order);
        double payment = NumberUtils.str2Double(order.getPayment());
        double totalFee = NumberUtils.str2Double(order.getTotalFee());
        double discountFee = totalFee - payment;
        PaymentLinkRespDTO paymentLInkRespDto = new PaymentLinkRespDTO();
        paymentLInkRespDto.setTotalFee(totalFee);
        paymentLInkRespDto.setPayment(payment);
        paymentLInkRespDto.setDiscountFee(discountFee);
        return paymentLInkRespDto;
    }

    /**
     * 金额不联动
     *
     * @param: order
     * @return:
     */
    default PaymentLinkRespDTO noPaymentLink(Order order) {
        PaymentUtils.calculateOrder(order);
        PaymentLinkRespDTO paymentLInkRespDto = new PaymentLinkRespDTO();
        paymentLInkRespDto.setTotalFee(NumberUtils.str2Double(order.getTotalFee()));
        paymentLInkRespDto.setPayment(NumberUtils.str2Double(order.getPayment()));
        paymentLInkRespDto.setDiscountFee(NumberUtils.str2Double(order.getDiscountFee()));
        return paymentLInkRespDto;
    }

    default PaymentLinkRespDTO deleteOrderPayment(PaymentLinkReqDTO reqDTO) {
        PaymentLinkRespDTO paymentLInkRespDto = new PaymentLinkRespDTO();
        paymentLInkRespDto.setTotalFee(0);
        paymentLInkRespDto.setPayment(0);
        paymentLInkRespDto.setDiscountFee(0);
        return paymentLInkRespDto;
    }
}

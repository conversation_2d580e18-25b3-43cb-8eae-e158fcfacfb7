package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.user.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 更新面单打印数据参数
 *
 * @Date 2021/7/12
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefreshPrintDataParams implements Serializable {

    private Staff staff;

    private User user;

    /**
     * 系统单号
     */
    private List<Long> sids;

    /**
     * 面单号
     */
    private List<String> outSids;

    /**
     * 系统物流商编码
     */
    private String cpCode;

    /**
     * 电子面单类型
     */
    private Integer wlbTemplateType;

    /**
     * 物流商id
     */
    private Long expressId;

    private UserWlbExpressTemplate userWlbExpressTemplate;

    public RefreshPrintDataParams(Staff staff, User user, List<Long> sids, String cpCode, Integer wlbTemplateType) {
        this.staff = staff;
        this.user = user;
        this.sids = sids;
        this.cpCode = cpCode;
        this.wlbTemplateType = wlbTemplateType;
    }

    public RefreshPrintDataParams(Staff staff, User user, String cpCode, Integer wlbTemplateType, List<String> outSids) {
        this.staff = staff;
        this.user = user;
        this.outSids = outSids;
        this.cpCode = cpCode;
        this.wlbTemplateType = wlbTemplateType;
    }

    public RefreshPrintDataParams(Staff staff, User user, String cpCode, Integer wlbTemplateType, List<String> outSids, Long expressId) {
        this.staff = staff;
        this.user = user;
        this.sids = sids;
        this.outSids = outSids;
        this.cpCode = cpCode;
        this.wlbTemplateType = wlbTemplateType;
        this.expressId = expressId;
    }

    public RefreshPrintDataParams(Staff staff, User user, List<String> outSids, UserWlbExpressTemplate userWlbExpressTemplate) {
        this.staff = staff;
        this.user = user;
        this.outSids = outSids;
        this.userWlbExpressTemplate = userWlbExpressTemplate;
    }
}

package com.raycloud.dmj.domain.trades;

/**
 * Description:
 *
 * @version Version 1.0
 * <AUTHOR>
 * @Copyright 2016 Git Inc. All rights reserved.
 * @CreateDate on 2016.11.03
 * @Company 杭州光云科技有限公司
 * 后期使用 @link com.raycloud.dmj.except.enums.TradeExcStatus
 */
@Deprecated
public enum TradeExcStatus {

    ES_USER_UNACTIVE("店铺停用", 100),//停
    ES_CANCEL("订单作废", 90), //
    ES_HALT("订单挂起", 70),//挂
//    ES_CHANGE("换货", 60), 暂不属于异常 无需提示
    ES_REFUND("退款", 60),//退
    ES_UNALLOCATED("商品未匹配", 50),//匹
    ES_ITEM_HALT("商品停用", 40),//
    ES_ITEM_RELATION_MODIFIED("商品对应关系修改", 30),//改
    ES_STOCK_INSUFFICIENT("缺货", 20);//缺


    private String explain;//订单状态说明
    private Integer weight;//异常权重,用与排序

    private TradeExcStatus(String explain, Integer weight) {
        this.explain = explain;
        this.weight = weight;
    }

    public Integer getWeight() {
        return weight;
    }
}

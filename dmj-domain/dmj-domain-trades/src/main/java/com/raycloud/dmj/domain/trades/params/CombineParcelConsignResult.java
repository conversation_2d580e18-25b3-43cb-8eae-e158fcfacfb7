package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.utils.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2022_06_20 13:39
 */
public class CombineParcelConsignResult implements Serializable {
    private List<Long> removeParcelIds;
    private Boolean success=true;
    private String errorMsg;
    private List<CombineParcelConsignResultVo> errorList;
    private boolean progressEnable=false;

    public List<Long> getRemoveParcelIds() {
        return removeParcelIds;
    }

    public void setRemoveParcelIds(List<Long> removeParcelIds) {
        this.removeParcelIds = removeParcelIds;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public List<CombineParcelConsignResultVo> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<CombineParcelConsignResultVo> errorList) {
        this.errorList = errorList;
    }

    public boolean isProgressEnable() {
        return progressEnable;
    }

    public void setProgressEnable(boolean progressEnable) {
        this.progressEnable = progressEnable;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public void computeSuccessStatus(){
        this.setSuccess((this.getErrorList()==null || this.getErrorList().isEmpty()) && StringUtils.isBlank(this.getErrorMsg()));
    }

    public static class CombineParcelConsignResultVo implements Serializable {
        private Long id;
        private Long sid;
        private String errorMsg;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getSid() {
            return sid;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }
    }
}


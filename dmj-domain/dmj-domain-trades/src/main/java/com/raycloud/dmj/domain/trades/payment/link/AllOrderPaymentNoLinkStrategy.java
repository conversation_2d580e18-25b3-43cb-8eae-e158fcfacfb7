package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;

/**
 * @description: 平台订单金额不联动，增/删/改商品时，trade实付金额不变
 * @author: pxh
 * @create: 2021-07-29 17:59
 **/
public class AllOrderPaymentNoLinkStrategy implements IPaymentLinkStrategy {

    @Override
    public PaymentLinkRespDTO insertOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return noPaymentLink(paymentLinkReqDTO.getOrder());
    }

    @Override
    public PaymentLinkRespDTO updateOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return commonPaymentLink(paymentLinkReqDTO.getOrder());
    }

    @Override
    public PaymentLinkRespDTO deleteOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return insertOrder(paymentLinkReqDTO);
    }

    @Override
    public boolean support(OpenLinkConfigEnum openLinkConfigEnum) {
        return OpenLinkConfigEnum.ALL_TRADE_PAYMENT_NO_LINK.equals(openLinkConfigEnum);
    }
}

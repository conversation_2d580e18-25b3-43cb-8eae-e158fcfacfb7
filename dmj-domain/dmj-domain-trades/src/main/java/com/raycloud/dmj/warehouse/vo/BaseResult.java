package com.raycloud.dmj.warehouse.vo;

import com.raycloud.dmj.warehouse.domain.ReqResBody;

import java.io.Serializable;

/**
 * Created by guzy on 17/9/18.
 */
public class BaseResult extends ReqResBody implements Serializable {

    private String party3Id;

    private Boolean success;

    private String msg;
    
    private boolean ignoreError;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public String getParty3Id() {
        return party3Id;
    }

    public void setParty3Id(String party3Id) {
        this.party3Id = party3Id;
    }

    public boolean isIgnoreError() {
        return ignoreError;
    }

    public void setIgnoreError(boolean ignoreError) {
        this.ignoreError = ignoreError;
    }
}

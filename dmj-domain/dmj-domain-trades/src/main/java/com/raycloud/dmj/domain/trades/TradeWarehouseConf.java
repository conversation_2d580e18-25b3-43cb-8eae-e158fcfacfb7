package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * 订单的仓库分配配置
 * <AUTHOR>
 *
 */
@Table(name = "trade_warehouse_conf")
public class TradeWarehouseConf extends Model {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2498968305240360185L;

	/**
	 * 唯一编号
	 */
	private Long id;
	
	/**
	 * 公司编号
	 */
	private Long companyId;
	
	/**
	 * 仓库编号
	 */
	private Long wid;
	
	/**
	 * 地区
	 */
	private String areas;
	
	/**
	 * 地区数组，由areas解析过来
	 */
	private String[] areaArray;
	
	/**
	 * 用户编号，所选店铺的平台用户的编号列表
	 */
	private String userIds;
	
	/**
	 * 用户编号数组，由userIds解析过来
	 */
	private Long[] userIdArray;
	
	/**
	 * 所选店铺的名称列表
	 */
	private String shopNames;
	
	/**
	 * 是否开启
	 */
	private Integer open;
	
	private Date created;
	
	private Date modified;
	
	private Integer enableStatus;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getWid() {
		return wid;
	}

	public void setWid(Long wid) {
		this.wid = wid;
	}

	public String getAreas() {
		return areas;
	}

	public void setAreas(String areas) {
		this.areas = areas;
	}

	public String getUserIds() {
		return userIds;
	}

	public void setUserIds(String userIds) {
		this.userIds = userIds;
	}

	public String getShopNames() {
		return shopNames;
	}

	public void setShopNames(String shopNames) {
		this.shopNames = shopNames;
	}

	public Integer getOpen() {
		return open;
	}

	public void setOpen(Integer open) {
		this.open = open;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getModified() {
		return modified;
	}

	public void setModified(Date modified) {
		this.modified = modified;
	}

	public Integer getEnableStatus() {
		return enableStatus;
	}

	public void setEnableStatus(Integer enableStatus) {
		this.enableStatus = enableStatus;
	}

	public String[] getAreaArray() {
		return areaArray;
	}

	public void setAreaArray(String[] areaArray) {
		this.areaArray = areaArray;
	}

	public Long[] getUserIdArray() {
		return userIdArray;
	}

	public void setUserIdArray(Long[] userIdArray) {
		this.userIdArray = userIdArray;
	}
}

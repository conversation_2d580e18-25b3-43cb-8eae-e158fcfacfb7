package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Table;

import java.util.Date;

/**
 * <AUTHOR>
 * @created 2018-10-08 10:11
 */
@Table(name = "trade_index", routerKey = "tradeDbNo")
public class TradeIndex {

    private Long sid;
    private Long companyId;
    private Long userId;
    private Long warehouseId;
    private Integer isExcep;
    private Integer isCancel;
    private String type;
    private Integer sysStatus;
    private Date created;
    private Date payTime;
    private Date expressPrintTime;
    private Date consignTime;
    private Integer enableStatus;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public void setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(Integer sysStatus) {
        this.sysStatus = sysStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getExpressPrintTime() {
        return expressPrintTime;
    }

    public void setExpressPrintTime(Date expressPrintTime) {
        this.expressPrintTime = expressPrintTime;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }
}

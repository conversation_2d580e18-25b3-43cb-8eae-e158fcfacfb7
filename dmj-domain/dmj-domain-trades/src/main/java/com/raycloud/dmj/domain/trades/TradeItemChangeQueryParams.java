package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/1/16 上午11:36
 **/
public class TradeItemChangeQueryParams implements Serializable {

    /**
     * 订单导出Excel来源标识
     */
    public static String TRADE_EXPORT = "/trade/exportItemChangeDetail";

    /**
     * 导出来源
     */
    private String exportSource;

    private Date operateTimeStart;

    private Date operateTimeEnd;

    private String outerId;

    private String skuOuterId;

    private Page page = new Page();

    public String getExportSource() {
        return exportSource;
    }

    public void setExportSource(String exportSource) {
        this.exportSource = exportSource;
    }

    public Date getOperateTimeStart() {
        return operateTimeStart;
    }

    public void setOperateTimeStart(Date operateTimeStart) {
        this.operateTimeStart = operateTimeStart;
    }

    public Date getOperateTimeEnd() {
        return operateTimeEnd;
    }

    public void setOperateTimeEnd(Date operateTimeEnd) {
        this.operateTimeEnd = operateTimeEnd;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}

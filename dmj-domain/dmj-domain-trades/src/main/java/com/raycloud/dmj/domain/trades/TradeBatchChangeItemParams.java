
package com.raycloud.dmj.domain.trades;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * @description:
 * @author: peng<PERSON><PERSON>ui
 * @create: 2021-03-22 14:04
 */
@Data
public class TradeBatchChangeItemParams implements Serializable {

    private static final long serialVersionUID = -5277592559454139734L;

    /**
     * 系统商家编码，如果是商品则为商品的商家编码，如果为SKU则为SKU的商家编码
     */
    private String originSysOuterId;

    /**
     * 源平台商家编码
     */
    private String originPlatformOuterId;

    /**
     *
     */
    private String originPlatformNumiId;

    /**
     * 源平台skuId
     */
    private String originPlatformSkuId;

    /**
     * 金额是否支持修改 0：不支持；1：支持
     */
    private Integer updatePayment;

    /**
     * 数量是否支持修改 0：不支持；1：支持
     */
    private Integer updateNum;

    /**
     * 0或者null：erp换商品
     * 1：快卖通供销绑定商品
     */
    private Integer KMFBind;

    /**
     * 1-供销修改商品 0或者null非供销修改商品
     */
    private Integer gxChangeItem;


    public void paramCheck() {
        if (StringUtils.isBlank(getOriginSysOuterId())
                && StringUtils.isBlank(getOriginPlatformOuterId())
                && StringUtils.isBlank(getOriginPlatformSkuId())) {
            throw new IllegalArgumentException("请输入原商家编码/平台编码/平台skuid");
        }
    }

    public Boolean isUpdatePayment() {
        return Objects.nonNull(updatePayment) && updatePayment - 1 == 0;
    }

    public Boolean isUpdateNum() {
        return Objects.nonNull(updateNum) && updateNum - 1 == 0;
    }

}

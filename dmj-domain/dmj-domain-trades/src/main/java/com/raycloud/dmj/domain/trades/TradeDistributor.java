package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 交易扩展表  平台经销分销信息  团长
 */
@Table(name = "trade_distributor")
@Setter
@Getter
public class TradeDistributor extends Model {
    private static final long serialVersionUID = -3992734250148275601L;
    private Long sid;

    private Long companyId;

    private Long userId;

    private String tid;

    private String distributorId;

    private String distributorName;

    private Date created;

}
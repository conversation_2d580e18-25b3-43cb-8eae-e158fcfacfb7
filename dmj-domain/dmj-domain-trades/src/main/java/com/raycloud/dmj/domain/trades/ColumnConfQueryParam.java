package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022-11-24 17:24
 * @description：
 * @modified By：
 * @version: $
 */
public class ColumnConfQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long pageId;

    private String  updateVersion;

    private String  tabVersion;

    private List<Long> pageIds;

    private List<String> envList;


    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getUpdateVersion() {
        return updateVersion;
    }

    public void setUpdateVersion(String updateVersion) {
        this.updateVersion = updateVersion;
    }

    public String getTabVersion() {
        return tabVersion;
    }

    public void setTabVersion(String tabVersion) {
        this.tabVersion = tabVersion;
    }

    public List<Long> getPageIds() {
        return pageIds;
    }

    public void setPageIds(List<Long> pageIds) {
        this.pageIds = pageIds;
    }

    public List<String> getEnvList() {
        return envList;
    }

    public void setEnvList(List<String> envList) {
        this.envList = envList;
    }

}

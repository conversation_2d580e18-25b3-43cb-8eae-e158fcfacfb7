package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.trades.TradeConfig;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description <pre>
 * 指定查询订单时需要补充的内容
 * . queryFields                指定要查询的具体字段 不指定则查所有
 *                                 强烈建议指定 目前trade表已经有145+的字段,真的需要这么多内容么?
 *                                 建议在addRecommendQueryFields指定字段的基础上增减字段
 * . queryOrder  queryOrderExt  是否查询对应order记录 还可以通过指定OrderAssembleParams来明确order需要查询的内容
 * . queryMerge  containConsign 是否查询隐藏的合单信息 当containConsign为true时已发货的合单子订单也会查询
 *   mergeStyle                 0: 合单子订单作为一个独立的trade进行返回
 *                              1: 合单子订单隐藏 并将对应的order放入合单主单的orders中
 * . queryExt                   是否补充ext表数据
 * . filter                     是否走ITradeFilter填充,目前包含
 *                                 1 DefaultTemplateFilter 单据模板名称 快递公司名称 以及仓库名称填充
 *                                 2 DecodeFieldFilter 系统加密数据解密处理
 *                                 3 TagNameFilter 标签及异常名填充
 *                                 强烈建议通过 excludeFilters 参数指定不需要的填充逻辑
 * . fill                       是否走ITradeFill填充,目前已有十几个填充实现
 *                                 强烈建议通过 includeFills,excludeFills 参数指定具体需要的填充逻辑
 *                                 建议通过addSuggestExcludeFills排除一些不推荐走的fill服务
 * . sensitive                  是否脱敏 如果脱敏则oriReceiver相关字段保留原始信息 receiver返回脱敏信息
 * </pre>
 * <AUTHOR>
 * @Date 2024-06-13
 */
public class TradeAssembleParams<T extends TradeAssembleParams> implements Serializable {

    /**
     * 合单子订单作为一个独立订单返回
     */
    public static final int MERGE_STYLE_INDEPENDENT = 0;

    /**
     * 合单子订单不返回，其orders挂在对应合单主订单上返回，合单子单信息体现在messageMemos字段上
     */
    public static final int MERGE_STYLE_HIDDEN_SUB = 1;

    private boolean queryOrder = false ,queryOrderExt = false ,
                    queryMerge = false ,  containConsign = true,
                    queryExt = false ,
                    filter = false, fill = false,
                    fillTradePay = false;

    private boolean sensitive =false;

    /**
     *  转换系统状态，这里主要转换已审核的订单状态
     */
    private boolean convertSysStatus =false;


    /**
     *  是否填充快递模板对应信息
     */
    private boolean fillTemplate =false;

    /**
     * <pre>
     *     MERGE_STYLE_INDEPENDENT: 合单子订单作为一个独立的trade进行返回
     *     MERGE_STYLE_HIDDEN_SUB : 合单子订单隐藏 并将对应的order放入合单主单的orders中
     *     仅queryMerge = true时生效
     * </pre>
     */
    private int mergeStyle =0;

    /**
     * 指定要查询的字段 没有指定则查询所有
     * @warn 需指定表别名,一般情况下为 t.;
     *       如果要走填充逻辑 需确认填充所依赖的字段存在于这个列表中
     */
    private List<String> queryFields;

    private OrderAssembleParams orderAssembleParams;


    /**
     * 指定不需要填充的内容对应的className
     * @see com.raycloud.dmj.services.trades.fill.ITradeFill
     */
    private List<String> excludeFills;

    /**
     * 指定需要填充的内容对应的className 不指定则走所有填充
     * @see com.raycloud.dmj.services.trades.fill.ITradeFill
     */
    private List<String> includeFills;

    /**
     * 指定不需要填充的内容对应的className
     * @see com.raycloud.dmj.services.trades.filter.ITradeFilter
     */
    private List<String> excludeFilters;


    /**
     * 这个参数由数据填充时系统自动赋值 不需要外部调用方传值
     */
    private TradeConfig tradeConfig;


    /**
     * 仅查询trade表信息
     * @return
     */
    public static TradeAssembleParams justTrade(){
        return new TradeAssembleParams();
    }

    /**
     * 仅查询trade表和 ext表信息
     * @return
     */
    public static TradeAssembleParams tradeWithExt(){
        return new TradeAssembleParams().setQueryExt(true);
    }

    /**
     * 仅查询trade表和order数据
     * @return
     */
    public static TradeAssembleParams tradeWithOrder(){
        return new TradeAssembleParams().setQueryOrder(true);
    }

    /**
     * 仅查询trade表和order数据 并考虑合单
     * @return
     */
    public static TradeAssembleParams mergeTradeWithOrder(){
        return new TradeAssembleParams().setQueryOrder(true).setQueryMerge(true);
    }

    /**
     * 仅查询trade表和order数据及ext数据
     * @return
     */
    public static TradeAssembleParams tradeWithOrderAndExt(){
        return new TradeAssembleParams().setQueryExt(true).setQueryOrder(true).setQueryOrderExt(true);
    }

    /**
     * 查询全部信息
     * @return
     */
    public static TradeAssembleParams fullData(){
        TradeAssembleParams tradeAssembleParams = new TradeAssembleParams()
                .setQueryOrder(true).setQueryOrderExt(true).setQueryExt(true).setQueryMerge(true)
                .setContainConsign(true);

        tradeAssembleParams.setOrderAssembleParams(OrderAssembleParams.orderWithSuitSingle());
        return tradeAssembleParams;
    }

    /**
     * 查询全部信息 并走填充逻辑
     * @return
     */
    public static TradeAssembleParams fullDataWithFill(){
        TradeAssembleParams tradeAssembleParams = new TradeAssembleParams()
                .setQueryOrder(true).setQueryOrderExt(true).setQueryExt(true).setQueryMerge(true)
                .setContainConsign(true).setFill(true).setFilter(true).setMergeStyle(MERGE_STYLE_HIDDEN_SUB);

        tradeAssembleParams.setOrderAssembleParams(OrderAssembleParams.orderWithSuitSingle());
        return tradeAssembleParams;
    }

    /**
     * 添加排除一些不建议走的fill服务
     * @param params
     */
    public static void  addSuggestExcludeFills(TradeAssembleParams params){
        params.addSuggestExcludeFills();
    }


    /**
     * 添加一些按字段查询时建议的字段 建议业务在此基础上增减需要的字段
     * @param params
     */
    public static void  addRecommendQueryFields(TradeAssembleParams params){
        params.addBaseField();

    }

    public boolean isFilter() {
        return filter;
    }

    public T setFilter(boolean filter) {
        this.filter = filter;
        return (T)this;
    }

    public List<String> getExcludeFilters() {
        return excludeFilters;
    }

    public void setExcludeFilters(List<String> excludeFilters) {
        this.excludeFilters = excludeFilters;
    }

    public T addExcludeFilter(String excludeFilter) {
        if (this.excludeFilters == null) {
            excludeFilters = new ArrayList<>();
        }
        excludeFilters.add(excludeFilter);
        return (T)this;
    }

    public boolean isFill() {
        return fill;
    }

    public T setFill(boolean fill) {
        this.fill = fill;
        return (T)this;
    }

    public boolean isContainConsign() {
        return containConsign;
    }

    public T setContainConsign(boolean containConsign) {
        this.containConsign = containConsign;
        return (T)this;
    }

    public boolean isQueryExt() {
        return queryExt;
    }

    public T setQueryExt(boolean queryExt) {
        this.queryExt = queryExt;
        return (T)this;
    }

    public boolean isQueryMerge() {
        return queryMerge;
    }

    public T setQueryMerge(boolean queryMerge) {
        this.queryMerge = queryMerge;
        return (T)this;
    }

    public int getMergeStyle() {
        return mergeStyle;
    }

    public T setMergeStyle(int mergeStyle) {
        this.mergeStyle = mergeStyle;
        return (T)this;
    }

    public List<String> getQueryFields() {
        return queryFields;
    }

    /**
     * 指定要查询的字段 没有指定则查询所有
     * @warn <pre>
     *     需指定表别名,一般情况下为 t.;
     *     如果要走填充逻辑 需确认填充所依赖的字段存在于这个列表中
     *     必需含有字段 t.sid,t.merge_sid,t.tid,t.companyId
     * </pre>
     */
    public T setQueryFields(List<String> queryFields) {
        if (CollectionUtils.isNotEmpty(queryFields)) {
            addQueryField(queryFields.toArray(new String[0]));
        }
        return (T)this;
    }

    public T addQueryField(String ... queryField) {
        if (queryFields == null) {
            //保证一些基本字段都会被查询到
            this.addBaseField();
        }
        for (String s : queryField) {
            addField(s);
        }
        return (T)this;
    }

    private void  addField(String field){
        if (queryFields == null) {
            this.queryFields = new ArrayList<>();
        }
        field = field.toLowerCase().trim();
        if (!queryFields.contains(field)) {
            this.queryFields.add(field);
        }
    }

    /**
     * 添加订单id 归属 状态 类型 从属关系等一些基本字段
     * 强烈建所有指定字段查询的业务 都添加一下基本字段
     * @return
     */
    public T  addBaseField(){
        addField("t.sid");
        addField("t.tid");
        addField("t.out_sid");

        addField("t.company_Id");
        addField("t.user_Id");
        addField("t.source");
        addField("t.sub_source");

        addQueryField("t.merge_sid");
        addQueryField("t.merge_type");
        addQueryField("t.split_sid");
        addQueryField("t.split_type");

        addField("t.status");
        addField("t.sys_status");
        addField("t.unified_status");
        addField("t.stock_status");

        addField("t.convert_type");
        addField("t.belong_type");
        addField("t.type");
        addField("t.is_presell");

        addField("t.is_excep");
        addField("t.item_excep");
        addField("t.except_ids");

        addField("t.is_upload");
        addField("t.sys_Consigned");
        addField("t.is_refund");
        addField("t.is_halt");
        addField("t.is_weigh");
        addField("t.is_package");

        addField("t.is_cancel");
        addField("t.enable_status");

        addField("t.dest_id");
        addField("t.source_id");

        addField("t.wave_id");
        addField("t.warehouse_id");

        addQueryField("t.v");
        addQueryField("t.tag_ids");

        //下面是非核心字段 但是会导致查询报错的字段
        addQueryField("t.seller_memo_update");


        return (T)this;
    }


    public T  addExceptRefField(){
        addQueryField("t.is_excep");
        addQueryField("t.excep");
        addQueryField("t.is_lost_msg");
        addQueryField("t.black_buyer_nick");
        addQueryField("t.seller_memo_update");
        addQueryField("t.unattainable");
        addQueryField("t.address_changed");

        //没有这些字段 返回前端会被标记异常  com.raycloud.dmj.domain.trades.utils.TradeUtils.getExceptionRemark
        addQueryField("t.payment");
        addQueryField("t.receiver_name");
        addQueryField("t.receiver_mobile");
        addQueryField("t.receiver_address");
        addQueryField("t.receiver_phone");
        addQueryField("t.receiver_name");
        return (T)this;
    }



    /**
     * 添加快递信息相关字段
     * 如果需要模板名称 快递公司名称等内容 需要走对应Filter
     * @see com.raycloud.dmj.services.filter.support.DefaultTemplateFilter
     * @return
     */
    public T  addLogisticsField(){
        addQueryField("t.out_sid", "t.logistics_company_id","t.template_id","t.template_type");
        return (T)this;
    }

    /**
     * 添加金额相关字段
     * @return
     */
    public T  addPaymentField(){
        addQueryField("t.cost", "t.packma_cost","t.discount_fee","t.total_fee","t.adjust_fee","t.tax_fee","t.post_fee",
                "t.payment","t.pay_amount","t.actual_post_fee","t.theory_post_fee","t.ac_payment");
        return (T)this;
    }

    public T  addFinanceBaseField(){
        addQueryField(
                "t.sid","t.user_id","t.taobao_id","t.company_id","t.tid","t.source","t.out_sid","t.consign_time","t.cost","t.post_fee","t.item_num",
                "t.weight","t.net_weight","t.warehouse_id","t.template_id","t.template_type","t.receiver_state","t.receiver_city","t.receiver_district","t.receiver_address","t.actual_post_fee",
                "t.merge_type","t.merge_sid","t.split_type","t.split_sid","t.three_pl_timing","t.is_tmall_delivery","t.theory_post_fee","t.gross_profit","t.logistics_company_id","t.scalping",
                "t.sys_status","t.payment","t.sys_memo","t.seller_memo","t.buyer_message"
        );
        return (T)this;
    }

    public boolean isQueryOrder() {
        return queryOrder;
    }

    public T setQueryOrder(boolean queryOrder) {
        this.queryOrder = queryOrder;
        return (T)this;
    }

    public boolean isQueryOrderExt() {
        return queryOrderExt;
    }

    public T setQueryOrderExt(boolean queryOrderExt) {
        this.queryOrderExt = queryOrderExt;
        return (T)this;
    }

    public OrderAssembleParams getOrderAssembleParams() {
        return orderAssembleParams;
    }

    public T setOrderAssembleParams(OrderAssembleParams orderAssembleParams) {
        this.orderAssembleParams = orderAssembleParams;
        return (T)this;
    }

    public List<String> getExcludeFills() {
        return excludeFills;
    }

    /**
     * @deprecated  请使用带TradeFillEnum的同名方法
     */
    @Deprecated
    public T setExcludeFills(List<String> excludeFills) {
        this.excludeFills = excludeFills;
        return (T)this;
    }

    /**
     * @deprecated  请使用带TradeFillEnum的同名方法
     */
    @Deprecated
    public T addExcludeFills(String ... tradeFills) {
        if (tradeFills != null) {
            if (this.excludeFills == null) {
                this.excludeFills = new ArrayList<>();
            }
            for (String excluedFill : tradeFills) {
                this.excludeFills.add(excluedFill);
            }
        }
        return (T)this;
    }

    /**
     * @deprecated  请使用带TradeFillEnum的同名方法
     */
    @Deprecated
    public T removeExcludeFills(String ... tradeFills) {
        if (this.excludeFills == null) {
            return (T)this;
        }
        for (String excluedFill : tradeFills) {
            if (this.excludeFills.contains(excluedFill)) {
                excludeFills.remove(excluedFill);
            }
        }
        return (T)this;
    }


    public T setExcludeFillEnums(List<TradeFillEnum> excludeFills) {
        if (CollectionUtils.isNotEmpty(excludeFills)) {
            this.excludeFills = excludeFills.stream().map(x->x.getClazz()).collect(Collectors.toList());
        }
        return (T)this;
    }

    public T addExcludeFills(TradeFillEnum ... tradeFills) {
        if (tradeFills != null) {
            if (this.excludeFills == null) {
                this.excludeFills = new ArrayList<>();
            }
            for (TradeFillEnum excluedFill : tradeFills) {
                this.excludeFills.add(excluedFill.getClazz());
            }
        }
        return (T)this;
    }

    public T removeExcludeFills(TradeFillEnum ... tradeFills) {
        if (this.excludeFills == null) {
            return (T)this;
        }
        for (TradeFillEnum excluedFill : tradeFills) {
            if (this.excludeFills.contains(excluedFill.getClazz())) {
                excludeFills.remove(excluedFill.getClazz());
            }
        }
        return (T)this;
    }

    /**
     * 添加排除一些不建议走的fill服务
     * @param params
     */
    public T addSuggestExcludeFills(){
        addExcludeFills(TradeFillEnum.TRADE_SALESMAN_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_SUPPLIER_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_DISTRIBUTOR_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_INVOICE_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_PLATFORM_RECOMMENDATION_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_ORDER_SYNC_ITEM_TAG_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_ORDER_PRODUCT_FILL.getClazz());
        addExcludeFills(TradeFillEnum.TRADE_EXT_FILL.getClazz());
        return (T)this;
    }

    public List<String> getIncludeFills() {
        return includeFills;
    }

    /**
     * @deprecated  请使用带TradeFillEnum的同名方法
     */
    @Deprecated
    public T setIncludeFills(List<String> includeFills) {
        this.includeFills = includeFills;
        return (T)this;
    }

    /**
     * @deprecated  请使用带TradeFillEnum的同名方法
     */
    @Deprecated
    public T addIncludeFills(String incluedFill) {
        if (this.includeFills == null) {
            includeFills = new ArrayList<>();
        }
        includeFills.add(incluedFill);
        return (T)this;
    }

    public T setIncludeFillEnums(List<TradeFillEnum> includeFills) {
        if (CollectionUtils.isNotEmpty(includeFills)) {
            this.includeFills = includeFills.stream().map(x->x.getClazz()).collect(Collectors.toList());
        }
        return (T)this;
    }

    public T addIncludeFills(TradeFillEnum incluedFill) {
        if (this.includeFills == null) {
            includeFills = new ArrayList<>();
        }
        includeFills.add(incluedFill.getClazz());
        return (T)this;
    }

    public boolean isSensitive() {
        return sensitive;
    }

    public T setSensitive(boolean sensitive) {
        this.sensitive = sensitive;
        return (T)this;
    }

    public boolean isConvertSysStatus() {
        return convertSysStatus;
    }

    public void setConvertSysStatus(boolean convertSysStatus) {
        this.convertSysStatus = convertSysStatus;
    }

    public boolean isFillTemplate() {
        return fillTemplate;
    }

    public void setFillTemplate(boolean fillTemplate) {
        this.fillTemplate = fillTemplate;
    }

    public TradeConfig getTradeConfig() {
        return tradeConfig;
    }

    public void setTradeConfig(TradeConfig tradeConfig) {
        this.tradeConfig = tradeConfig;
    }
    /**
     * 填充支付单信息
     * @return
     */
    public boolean isFillTradePay() {
        return fillTradePay;
    }

    /**
     * 填充支付单信息
     * @param fillTradePay
     */
    public T setFillTradePay(boolean fillTradePay) {
        this.fillTradePay = fillTradePay;
        return (T)this;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @since 16/1/19
 */
public class DateUtils {


    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");

    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static String format(Date date, String format) {
        return date != null ? new SimpleDateFormat(format).format(date) : "";
    }

    public static String date2Str(Date date) {
        return format(date, "yyyy-MM-dd");
    }

    public static String datetime2Str(Date date) {
        return format(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static String datetime2Str(Date date, boolean nullInitDate) {
        if (nullInitDate && Objects.equals(date, TradeTimeUtils.INIT_DATE)) {
            return "";
        }
        return format(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将日期字符串转换为日期对象,日期字符串的分隔符支持"-",如2016-10-20 12:00:00,日期和时间之前已空格分割
     *
     * @param source
     * @return
     */
    public static Date str2Date(String source, Date defaultDate) {
        return str2Date(source, defaultDate, "-");
    }

    public static Date str2Date(String source, Date defaultDate, String splitChar) {
        if (StringUtils.isBlank(source)) {
            return defaultDate;
        }
        try {
            String[] dt = source.split("\\s+");
            String[] da = dt[0].split(splitChar);
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.YEAR, Integer.parseInt(da[0]));
            calendar.set(Calendar.MONTH, Integer.parseInt(da[1]) - 1);
            calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(da[2]));
            if (dt.length == 2) {
                String[] ta = dt[1].split(":");
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(ta[0]));
                if (ta.length >= 2) {
                    calendar.set(Calendar.MINUTE, Integer.parseInt(ta[1]));
                } else {
                    calendar.set(Calendar.MINUTE, 0);
                }
                if (ta.length == 3) {
                    calendar.set(Calendar.SECOND, Integer.parseInt(ta[2]));
                } else {
                    calendar.set(Calendar.SECOND, 0);
                }
            } else {
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
            }
            //这里必须设置毫秒为0   否则精度不准  如果是直接落库  有时会多一秒
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        } catch (Exception e) {
            return defaultDate;
        }
    }

    public static int differentHoursByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600));
    }

    public static int differentSecondsByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / (1000));
    }

    public static int differentMinutesByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / (1000 * 60));
    }

    public static Date str2Date(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH点mm分ss秒");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf4 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat sdf5 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat sdf6 = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            try {
                return sdf2.parse(str);
            } catch (ParseException e2) {
                try {
                    return sdf3.parse(str);
                } catch (ParseException e3) {
                    try {
                        return sdf4.parse(str);
                    } catch (ParseException e4) {
                        try {
                            return sdf5.parse(str);
                        } catch (ParseException e5) {
                            try {
                                return sdf6.parse(str);
                            } catch (ParseException e6) {
                                return null;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 日期转换
     *
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static Date str2DaTe(int year, int month, int day) {
        LocalDate localDate = LocalDate.of(year, month, day);
        ZonedDateTime zdt = localDate.atStartOfDay(ZONE_ID);
        return Date.from(zdt.toInstant());
    }

    /**
     * 日期格式化
     *
     * @param str
     * @return
     */
    public static Date str2Date_yyyy_MM_dd(String str) {
        YYYY_MM_DD_FORMATTER.parse(str);
        LocalDate localDate = LocalDate.parse(str, YYYY_MM_DD_FORMATTER);
        ZonedDateTime zdt = localDate.atStartOfDay(ZONE_ID);
        return Date.from(zdt.toInstant());
    }

    /**
     * @param str
     * @param dateTimeFormatter
     * @return
     */
    public static Date str2DateTimeFormatter(String str, DateTimeFormatter dateTimeFormatter) {
        YYYY_MM_DD_FORMATTER.parse(str);
        LocalDate localDate = LocalDate.parse(str, YYYY_MM_DD_FORMATTER);
        ZonedDateTime zdt = localDate.atStartOfDay(ZONE_ID);
        return Date.from(zdt.toInstant());
    }

    public static Date beginOfDay(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date  date = simpleDateFormat.parse(str);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        } catch (ParseException var3) {
            return null;
        }

    }

    /**
     * 比较时间，判断date1时间是否在date2时间之后
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isAfter(Date date1, Date date2) {
        if (Objects.isNull(date1) || Objects.isNull(date2)) {
            return false;
        }
        return date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
                .isAfter(date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
    }


    public static Date getDayStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date getDayEndDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 24);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static boolean equalsDate(Date date1, Date date2) {
        if (date1 == null) {
            date1 = TradeTimeUtils.INIT_DATE;
        }
        if (date2 == null) {
            date2 = TradeTimeUtils.INIT_DATE;
        }
        return date1.equals(date2);
    }

    public static Date addDate(Date date, Integer day) {
        if (date == null || day == null) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(getMillis(date) + ((long) day) * 24 * 3600 * 1000);
        return c.getTime();
    }

    public static Date minusDate(Date date, Integer day) {
        if (date == null || day == null) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(getMillis(date) - ((long) day) * 24 * 3600 * 1000);
        return c.getTime();
    }

    /**
     * @param date 日期
     * @param date1 日期
     * @return 返回date - date1 相减后的日期
     */
    public static int diffDate(Date date, Date date1) {
        if (date == null || date1 == null) return 0;
        return (int) ((getMillis(date) - getMillis(date1)) / (24 * 3600 * 1000));
    }

    /**
     * @param date  日期
     * @return 返回毫秒
     */
    public static long getMillis(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getTimeInMillis();
    }

    public static Integer diffDateDefaultNull(Date date, Date date1) {
        if (date == null || date1 == null) return null;
        return (int) ((getMillis(date) - getMillis(date1)) / (24 * 3600 * 1000));
    }

    public static Integer diffSecondDefaultNull(Date date, Date date1) {
        if (date == null || date1 == null) return null;
        return (int) ((getMillis(date) - getMillis(date1)) / (1000));
    }

    /**
     * 毫秒
     * @param date
     * @param date1
     * @return
     */
    public static int diffDate(Long date, Long date1) {
        if (date == null || date1 == null) return 0;
        return (int) ((date - date1) / (24 * 3600 * 1000));
    }
}

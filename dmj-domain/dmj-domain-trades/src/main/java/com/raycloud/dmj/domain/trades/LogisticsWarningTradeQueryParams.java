package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/7 7:28 PM
 * @description
 */
public class LogisticsWarningTradeQueryParams implements Serializable {

    private static final long serialVersionUID = 3292860076721778603L;

    /**
     * 物流预警状态
     * 0:发货即将超时
     * 1:揽收即将超时
     * 2:揽收更新即将超时
     * 3:物流更新即将超时
     */
    private Integer warningType;

    /**
     * 子预警类型档位   1，2，3，4, 5
     */
    private Integer subWarningType;

    /**
     * sid列表
     */
    private List<Long> sids;

    /**
     * 快递单号列表
     */
    private List<String> outSids;

    /**
     * 平台单号列表
     */
    private List<String> tids;

    /**
     * 物流公司编码
     */
    private String cpCode;

    /**
     * 系统状态
     */
    private List<String> sysStatus;

    /**
     * 平台状态
     */
    private List<String> status;

    /**
     * 发货仓ID
     */
    private List<Long> warehouseIds;

    /**
     * 订单店铺ID，供销商要记录分销店铺
     */
    private List<Long> shopIds;

    /**
     * 支付时间
     */
    private Date startPayDate;

    private Date endPayDate;

    /**
     * 查询超时预警的时间
     */
    private Date startTime;

    private Date endTime;

    private Page page;

    private Set<String> sources;

    private String searchKey;

    public Integer getWarningType() {
        return warningType;
    }

    public void setWarningType(Integer warningType) {
        this.warningType = warningType;
    }

    public Integer getSubWarningType() {
        return subWarningType;
    }

    public void setSubWarningType(Integer subWarningType) {
        this.subWarningType = subWarningType;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public List<String> getOutSids() {
        return outSids;
    }

    public void setOutSids(List<String> outSids) {
        this.outSids = outSids;
    }

    public List<String> getTids() {
        return tids;
    }

    public void setTids(List<String> tids) {
        this.tids = tids;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public List<String> getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(List<String> sysStatus) {
        this.sysStatus = sysStatus;
    }

    public List<String> getStatus() {
        return status;
    }

    public void setStatus(List<String> status) {
        this.status = status;
    }

    public List<Long> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(List<Long> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public List<Long> getShopIds() {
        return shopIds;
    }

    public void setShopIds(List<Long> shopIds) {
        this.shopIds = shopIds;
    }

    public Date getStartPayDate() {
        return startPayDate;
    }

    public void setStartPayDate(Date startPayDate) {
        this.startPayDate = startPayDate;
    }

    public Date getEndPayDate() {
        return endPayDate;
    }

    public void setEndPayDate(Date endPayDate) {
        this.endPayDate = endPayDate;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Set<String> getSources() {
        return sources;
    }

    public void setSources(Set<String> sources) {
        this.sources = sources;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }
}

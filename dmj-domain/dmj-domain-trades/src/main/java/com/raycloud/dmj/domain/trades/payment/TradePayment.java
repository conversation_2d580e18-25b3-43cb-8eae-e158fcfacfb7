package com.raycloud.dmj.domain.trades.payment;

import com.raycloud.dmj.domain.trades.TradeBase;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description TradePayment
 * @Date 2022/2/21 13:34
 * @Created 杨恒
 */
@Table(name = "trade_payment", routerKey = "tradeDbNo")
@Setter
@Getter
public class TradePayment extends TradeBase {

    private static final long serialVersionUID = -6437791095688749548L;

    /**
     * 实付金额
     */
    private String payment;

    /**
     * 平台实付（不变的实付金额）
     */
    private String acPayment;

    /**
     * 订单总成本价,所有商品总成本价之和
     */
    private Double cost;

    /**
     * 总额
     */
    private String totalFee;

    /**
     * 优惠
     */
    private String discountFee;

    /**
     * 理论运费
     */
    private Double theoryPostFee;

    /**
     * 邮费
     */
    private String postFee;

    /**
     * 实际的物流费用，通过weight重量、收货地址，运费模板等字段运算出来
     */
    private String actualPostFee;

    /**
     * 税费
     */
    private String taxFee;

    /**
     * 手工调整订单价格
     */
    private String adjustFee;

    /**
     * 分销金额
     */
    private String saleFee;

    /**
     * sale_price 修复bug 不参与计算
     * TODO 这个实际取的是 saleFee 不清楚原因
     */
    private String salePrice;

    /**
     * 包材成本
     */
    private Double packmaCost;

    /**
     * 订单实收金额
     */
    private Double tradePurchaseAmount;

    /**
     * 平台支付金额
     */
    private Double platformPaymentAmount;

    /**
     * 手工支付金额
     */
    private Double manualPaymentAmount;

    /**
     * 毛利润
     */
    private Double grossProfit;

    /**
     * 实付金额
     */
    private String payAmount;
}

package com.raycloud.dmj.domain.trades;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther mengfanguang
 * @Date 2023/7/10
 */
public class TradeResultData {

    public TradeConfig tradeConfig;
    /**
     * 处理成功的订单
     */
    public List<Trade> trades = new ArrayList<>();
    /**
     * 需要操作的order
     */
    public List<Order> orders = new ArrayList<>();
    /**
     * 本次处理的合单
     */
    public Map<Long, List<Trade>> mergeTradeMap = new HashMap<>();

    public Map<Long, List<Trade>> mergeTradeMapCopy = new HashMap<>();

    /**
     * 处理失败的订单号及错误信息
     */
    public Map<String, String> errors = new HashMap<>();

}
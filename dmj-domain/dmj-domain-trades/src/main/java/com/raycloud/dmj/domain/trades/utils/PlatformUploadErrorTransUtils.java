package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.consign.TradeUploadErrorMapperConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 上传错误翻译
 * <AUTHOR> HuangFuJun
 * @Date : 2023/4/19 11:21 AM
 * @From : erp-core
 */
public class PlatformUploadErrorTransUtils {

    /** 异常消息转换 */
    public static String transform(String source, String platMsg) {
        if (StringUtils.isBlank(source) || StringUtils.isBlank(platMsg)) {
            return platMsg;
        }

        TradeUploadErrorMapperConfig config = ConfigHolder.TRADE_UPLOAD_ERROR_MAPPER_CONFIG;
        if (Objects.isNull(config) || CollectionUtils.isEmpty(config.getErrTransMappers())) {
            return platMsg;
        }

        for (TradeUploadErrorMapperConfig.ErrorTransMapper mapper : config.getErrTransMappers()) {
            if (match(source, platMsg, mapper)) {
                return StringUtils.isNotBlank(mapper.getTransMsg()) ? mapper.getTransMsg() :
                        Optional.ofNullable(config.getErrCodeMapper()).map(m -> m.get(mapper.getTransCode())).orElse(platMsg);
            }
        }
        return platMsg;
    }

    private static boolean match(String source, String platError, TradeUploadErrorMapperConfig.ErrorTransMapper mapper) {
        if (!Objects.equals(source, mapper.getSource())) {
            return false;
        }
        return mapper.getPlatErrors().stream().anyMatch(platError::contains);
    }
}

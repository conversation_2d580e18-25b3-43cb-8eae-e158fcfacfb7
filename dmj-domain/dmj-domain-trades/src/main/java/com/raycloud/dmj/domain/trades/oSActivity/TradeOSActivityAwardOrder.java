package com.raycloud.dmj.domain.trades.oSActivity;

import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.sql.Types;
import java.util.Date;

/**
 * Created by huangfuhua on 2020-08-24.
 */
@Table(name = "trade_os_activity_award_order", routerKey = "tradeOSActivityAwardOrderDbNo")
public class TradeOSActivityAwardOrder extends Model {

    private static final long serialVersionUID = 5285672507075118414L;

    /**
     * 公司信息
     */
    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id;
    @Column(name = "activity_id", type = Types.VARCHAR)
    private String activityId;
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    /**
     * 订单信息
     */
    @Column(name = "sid", type = Types.BIGINT)
    private Long sid;
    @Column(name = "oid", type = Types.BIGINT)
    private Long oid;

    /**
     * 买家信息-接口返回
     */
    @Column(name = "buyer_id", type = Types.BIGINT)
    private Long buyerId;
    @Column(name = "buyer_nick", type = Types.VARCHAR)
    private String buyerNick;
    /**
     * 是否中奖
     */
    @Column(name = "is_award", type = Types.INTEGER)
    private Integer isAward;
    /**
     * 是否完成发奖
     */
    @Column(name = "is_finish_award", type = Types.INTEGER)
    private Integer isFinishAward;
    /**
     * 付款时间
     */
    @Column(name = "pay_time", type = Types.TIMESTAMP)
    private Date payTime;
    /**
     * 排序码
     */
    @Column(name = "sort_code", type = Types.VARCHAR)
    private String sortCode;
    /**
     * 排序序号
     */
    @Column(name = "sort_num", type = Types.BIGINT)
    private Long sortNum;

    @Column(name = "enable_status", type = Types.INTEGER)
    private Integer enableStatus;

    @Column(name = "created", type = Types.TIMESTAMP)
    private Date created;

    @Column(name = "modified", type = Types.TIMESTAMP)
    private Date modified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public Integer getIsAward() {
        return isAward;
    }

    public void setIsAward(Integer isAward) {
        this.isAward = isAward;
    }

    public Integer getIsFinishAward() {
        return isFinishAward;
    }

    public void setIsFinishAward(Integer isFinishAward) {
        this.isFinishAward = isFinishAward;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getSortCode() {
        return sortCode;
    }

    public void setSortCode(String sortCode) {
        this.sortCode = sortCode;
    }

    public Long getSortNum() {
        return sortNum;
    }

    public void setSortNum(Long sortNum) {
        this.sortNum = sortNum;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}

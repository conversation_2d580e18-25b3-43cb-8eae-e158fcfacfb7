package com.raycloud.dmj.domain.payment;


import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import com.raycloud.dmj.domain.trade.item.Gx2FxChangeItemData;

import java.util.List;

/**
 * @Description <pre>
 *  订单商品修改请求参数
 * </pre>
 * <AUTHOR>
 * @Date 2023-01-28
 */
public class TradeItemChangeRequest {

    public TradeItemChangeRequest() {
    }

    public TradeItemChangeRequest(Trade trade) {
        if (trade != null) {
            sid = trade.getSid();
            payAmount = trade.getPayAmount();
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orders4Trade)) {
                List<ItemChange> orders = new ArrayList<>();
                for (Order order : orders4Trade) {
                    orders.add(new ItemChange(order));
                }
                this.orders = orders;
            }

        }
    }

    private Long sid;

    private Trade trade;

    private Gx2FxChangeItemData gx2FxChangeItemData = new Gx2FxChangeItemData();

    private List<ItemChange> orders;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    /**
     * 买家已付金额 系统订单 这个字段支持手工修改
     */
    private String payAmount;

    /**
     * 是否由前端传参来修改订单商品供应商，不持久化，前端传参
     */
    private Boolean saveOrderSupplier;


    /**
     * 修改商品后是否需要执行商品搭配规则
     */
    private Boolean rematchItemReplace;

    private Integer isImport;

    private Integer sameGoodFillType;

    private Integer importType;

    public Integer getIsImport() {
        return isImport;
    }

    public void setIsImport(Integer isImport) {
        this.isImport = isImport;
    }

    public Integer getSameGoodFillType() {
        return sameGoodFillType;
    }

    public void setSameGoodFillType(Integer sameGoodFillType) {
        this.sameGoodFillType = sameGoodFillType;
    }

    public Integer getImportType() {
        return importType;
    }

    public void setImportType(Integer importType) {
        this.importType = importType;
    }

    private String selfBuiltDepositAmount;
    private String selfBuiltPaymentReceivable;


    public Gx2FxChangeItemData getGx2FxChangeItemData() {
        return gx2FxChangeItemData;
    }

    public void setGx2FxChangeItemData(Gx2FxChangeItemData gx2FxChangeItemData) {
        this.gx2FxChangeItemData = gx2FxChangeItemData;
    }

    public Trade getTrade() {
        return trade;
    }

    public void setTrade(Trade trade) {
        this.trade = trade;
    }

    public List<ItemChange> getOrders() {
        return orders;
    }

    public void setOrders(List<ItemChange> orders) {
        this.orders = orders;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public Boolean getSaveOrderSupplier() {
        return saveOrderSupplier;
    }

    public void setSaveOrderSupplier(Boolean saveOrderSupplier) {
        this.saveOrderSupplier = saveOrderSupplier;
    }

    public String getSelfBuiltDepositAmount() {
        return selfBuiltDepositAmount;
    }

    public void setSelfBuiltDepositAmount(String selfBuiltDepositAmount) {
        this.selfBuiltDepositAmount = selfBuiltDepositAmount;
    }

    public String getSelfBuiltPaymentReceivable() {
        return selfBuiltPaymentReceivable;
    }

    public void setSelfBuiltPaymentReceivable(String selfBuiltPaymentReceivable) {
        this.selfBuiltPaymentReceivable = selfBuiltPaymentReceivable;
    }

    public Boolean getRematchItemReplace() {
        return rematchItemReplace;
    }

    public void setRematchItemReplace(Boolean rematchItemReplace) {
        this.rematchItemReplace = rematchItemReplace;
    }

    public static class ItemChange{

        public ItemChange() {
        }

        public ItemChange(Order order) {
            if (order != null) {
                this.setId(order.getId());
                this.setNum(order.getNum());
                this.setPrice(order.getPrice());
                this.setPayment(order.getPayment());
                this.setDivideOrderFee(order.getDivideOrderFee());
                this.setTitle(order.getTitle());
                this.setItemSysId(order.getItemSysId());
                this.setSkuSysId(order.getSkuSysId());
                this.setPicPath(order.getPicPath());
                this.setIsVirtual(order.getIsVirtual());
                this.setOuterId(order.getOuterId());
                this.setOuterSkuId(order.getOuterSkuId());
                this.setSid(order.getSid());
                this.setTid(order.getTid());
                this.setOid(order.getOid());
                this.setCustomGiftType(order.getCustomGiftType());
                this.setIsPick(order.getIsPick());
                this.setStatus(order.getStatus());
                this.setNewOrderId(order.getNewOrderId());
                this.setSupplierIds(order.getSupplierIds());

                if (order.getOrderExt() != null) {
                    ItemExt ext = new ItemExt();
                    ext.setOrderRemark(order.getOrderExt().getOrderRemark());
                    this.setOrderExt(ext);
                }
            }
        }

        /**
         * 子订单系统编号 新增商品则为空
         */
        private Long id;

        /**
         * 子订单商品数量
         */
        private Integer num;

        /**
         * 商品价格
         */
        private String price;

        /**
         * 实付金额
         */
        private String payment;


        /**
         * 平台实付金额
         */
        private String divideOrderFee;


        private ItemExt orderExt;


        //==== 对于新增商品 以下内容必填 =========

        /**
         * 商品标题
         */
        private String title;

        /**
         * 匹配到的系统商品ID
         */
        private Long itemSysId;

        /**
         * 匹配到的系统规格ID
         */
        private Long skuSysId;

        /**
         * 平台商品图片链接
         */
        private String picPath;

        /**
         * 是否虚拟商品，虚拟商品不需要申请库存，发货时也不需要消费库存、归还库存
         */
        private Integer isVirtual;

        /**
         * 商家编码，不做持久化，仅仅只是为了系统订单添加商品使用
         */
        private String outerId;

        /**
         * outer_sku_id
         */
        private String outerSkuId;


        /**
         * 前端执行了换商品的情况下 新增的order有可能属于合单子单
         */
        private Long sid;
        private String tid;
        /**
         * 子订单平台编
         */
        private Long oid;


        /**
         * 自定义赠品类型，暂定0 原有逻辑，1手工新增赠品
         */
        private Long customGiftType;

        /**
         * 当子订单是赠品的时候（giftNum>0），判断赠品是否参与拣选
         * 增加（是否参与验货）属性判断
         * 0：不拣选不验货；1：拣选不验货；2：拣选验货；3：验货不拣选
         */
        private Integer isPick;



        private String status;

        /**
         * 商品供应商
         */
        private List<Long> supplierIds;

        /**
         * 指定的的orderId
         */
        private Long newOrderId;

        public Long getNewOrderId() {
            return newOrderId;
        }

        public void setNewOrderId(Long newOrderId) {
            this.newOrderId = newOrderId;
        }

        //==== 对于新增商品 以上内容必填 =========


        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getPayment() {
            return payment;
        }

        public void setPayment(String payment) {
            this.payment = payment;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public Long getItemSysId() {
            return itemSysId;
        }

        public void setItemSysId(Long itemSysId) {
            this.itemSysId = itemSysId;
        }

        public Long getSkuSysId() {
            return skuSysId;
        }

        public void setSkuSysId(Long skuSysId) {
            this.skuSysId = skuSysId;
        }

        public String getPicPath() {
            return picPath;
        }

        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }

        public Integer getIsVirtual() {
            return isVirtual;
        }

        public void setIsVirtual(Integer isVirtual) {
            this.isVirtual = isVirtual;
        }

        public String getOuterId() {
            return outerId;
        }

        public void setOuterId(String outerId) {
            this.outerId = outerId;
        }

        public String getOuterSkuId() {
            return outerSkuId;
        }

        public void setOuterSkuId(String outerSkuId) {
            this.outerSkuId = outerSkuId;
        }

        public ItemExt getOrderExt() {
            return orderExt;
        }

        public void setOrderExt(ItemExt orderExt) {
            this.orderExt = orderExt;
        }

        public Long getSid() {
            return sid;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        public Long getCustomGiftType() {
            return customGiftType;
        }

        public void setCustomGiftType(Long customGiftType) {
            this.customGiftType = customGiftType;
        }

        public Integer getIsPick() {
            return isPick;
        }

        public void setIsPick(Integer isPick) {
            this.isPick = isPick;
        }

        public String getDivideOrderFee() {
            return divideOrderFee;
        }

        public void setDivideOrderFee(String divideOrderFee) {
            this.divideOrderFee = divideOrderFee;
        }

        public Long getOid() {
            return oid;
        }

        public void setOid(Long oid) {
            this.oid = oid;
        }


        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public List<Long> getSupplierIds() {
            return supplierIds;
        }

        public void setSupplierIds(List<Long> supplierIds) {
            this.supplierIds = supplierIds;
        }
    }

    public static class ItemExt{

        private String orderRemark;

        public String getOrderRemark() {
            return orderRemark;
        }

        public void setOrderRemark(String orderRemark) {
            this.orderRemark = orderRemark;
        }
    }
}

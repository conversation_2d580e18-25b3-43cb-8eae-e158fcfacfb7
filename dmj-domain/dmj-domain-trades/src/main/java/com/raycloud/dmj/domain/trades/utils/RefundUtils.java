package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/4/15.
 */
public class RefundUtils {

    /**
     *
     * 判断子订单是否退款类型
     * 子订单的refundStatus为以下状态则不是退款类型
     *      {@link Order#REFUND_CLOSED},
     *      {@link Order#REFUND_SUCCESS},
     *      {@link Order#REFUND_SELLER_CONTINUE_CONSIGN},
     *      {@link Order#REFUND_SELLER_REFUSE_BUYER},
     *      {@link Order#NO_REFUND}，
     *
     * @param order
     * @return
     */
    public static boolean isRefundOrder(Order order){
        return isRefundOrder(order.getRefundStatus());
    }

    public static boolean isRefundOrder(String refundStatus){
        return !(StringUtils.isEmpty(refundStatus) ||
                Order.NO_REFUND.equals(refundStatus) ||
                Order.REFUND_CLOSED.equals(refundStatus) ||
                Order.REFUND_SUCCESS.equals(refundStatus) ||
                Order.REFUND_SELLER_CONTINUE_CONSIGN.equals(refundStatus) ||
                Order.REFUND_SELLER_REFUSE_BUYER.equals(refundStatus));
    }

    /**
     * 发货前退款中
     * @param order
     * @return
     */
    public static boolean isBeforeSendGoodsRefundingOrder(Order order){
        if(Objects.isNull(order)){
            return false;
        }
        String refundStatus = order.getRefundStatus();
        if(StringUtils.isBlank(refundStatus)){
            return false;
        }
        return (Order.REFUND_WAIT_SELLER_AGREE.equals(refundStatus) ||
                Order.REFUND_WAIT_BUYER_RETURN_GOODS.equals(refundStatus) ||
                Order.REFUND_WAIT_SELLER_CONFIRM_GOODS.equals(refundStatus)
                ) && !OrderUtils.isAfterSendGoods(order);
    }

    public static boolean isBeforeSendGoodsRefundingOrder(Trade trade){
        if(Objects.isNull(trade)){
            return false;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for(Order order : orders){
            if(isBeforeSendGoodsRefundingOrder(order)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 退款成功
     */
    public static boolean isRefundOrderSuccess(Order order){
        return Order.REFUND_SUCCESS.equals(order.getRefundStatus());
    }

    /**
     * 判断订单是否为退款订单
     * @param trade
     * @return
     */
    public static boolean containRefundOrder(Trade trade){
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for(Order order : orders){
            if(isRefundOrder(order)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否能将退款状态改为 {@link Order#REFUND_SELLER_CONTINUE_CONSIGN}
     * @param oldRefundStatus
     * @return
     */
    public static boolean canUpdateContinueConsign(String oldRefundStatus){
        return Order.REFUND_WAIT_SELLER_AGREE.equals(oldRefundStatus);
    }

    /**
     * 是否拒绝退款的状态
     * @param refundStatus
     * @return
     */
    public static boolean isRefuseRefund(String refundStatus){
        return Order.REFUND_SELLER_CONTINUE_CONSIGN.equals(refundStatus) || Order.REFUND_SELLER_REFUSE_BUYER.equals(refundStatus) || Order.REFUND_CLOSED.equals(refundStatus);
    }

    /**
     * 展示退款状态
     * @param order
     * @return
     */
    public static String orderRefundMessage(Order order) {
        if (StringUtils.isNotBlank(order.getRefundStatus())) {
            if (Order.REFUND_WAIT_SELLER_AGREE.equals(order.getRefundStatus()) || Order.REFUND_WAIT_BUYER_RETURN_GOODS.equals(order.getRefundStatus())
            || Order.REFUND_WAIT_SELLER_CONFIRM_GOODS.equals(order.getRefundStatus())) {
                //退款中
                return (OrderUtils.isAfterSendGoods(order) && Objects.equals(order.getSysConsigned(),1))?"系统发货后退款中":"系统发货前退款中";
            } else if (Order.REFUND_SUCCESS.equals(order.getRefundStatus())) {
                //退款成功
                return (null != order.getSysConsigned() && 1 == order.getSysConsigned())?"系统发货后退款成功":"系统发货前退款成功";
            } else if (Order.REFUND_CLOSED.equals(order.getRefundStatus())) {
                //退款关闭
                return "退款关闭";
            }
        }
        return "正常";

    }

    /**
     * 判断一个订单的平台子订单是不是全部交易完成后退款成功
     */
    public static boolean isFinishedAfterRefund(Trade trade){
        if(CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) || !Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())){
            return false;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if(orders == null || orders.size() == 0){
            return false;
        }
        for(Order order : orders){
            if(!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) && !Order.REFUND_SUCCESS.equals(order.getRefundStatus())){
                return false;
            }
        }
        return true;
    }

    public static String getRefundStatus(Order order) {
        if (order == null) {
            return Order.NO_REFUND;
        }
        String refundStatus = StringUtils.trimToEmpty(order.getRefundStatus());
        if (StringUtils.isEmpty(refundStatus)) {
            refundStatus = Order.NO_REFUND;
        }
        return refundStatus;
    }

}

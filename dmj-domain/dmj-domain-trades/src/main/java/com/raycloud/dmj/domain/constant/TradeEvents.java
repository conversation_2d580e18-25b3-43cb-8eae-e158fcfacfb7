package com.raycloud.dmj.domain.constant;

/**
 * <AUTHOR>
 * @created 2020-03-09 19:30
 */
public class TradeEvents {

    private TradeEvents() {}

    /**
     * 订单标签双写
     */
    public static final String TRADE_LABEL_DOUBLE_WRITE = "trade.label.double.write";
    public static final String TRADE_LABEL_BACKUP = "trade.label.backup";
    public static final String TRADE_TRACE_ADD = "trade.trace.add";
    public static final String TRADE_CONSIGN = "trade.consign";
    public static final String TRADE_CONSIGN_CACHE = "trade.consign.cache";
    public static final String SERVER_TRADE_CONSIGN = "server.trade.consign";
    public static final String TRADE_CONSIGN_UPLOAD = "trade.consign.upload";

    public static final String TRADE_CONSIGN_UPLOAD_DIRECT = "trade.consign.upload.direct";
    public static final String SERVER_TRADE_CONSIGN_UPLOAD = "server.trade.consign.upload";
    public static final String TRADE_STOCK_CONSUME = "trade.stock.consume";
    public static final String TRADE_STOCK_RESUME = "trade.stock.resume";
    public static final String TRADE_DOWNLOAD = "trade.download";
    public static final String TRADE_ITEM_MATCH = "trade.item.match";
    public static final String TRADE_FX_DOWNLOAD = "trade.fx.download";
    public static final String TRADE_GX_CANCEL = "trade.gx.cancel";
    public static final String TRADE_FX_AUDIT = "trade.fx.audit";
    public static final String TRADE_IMPORT_AFTER = "trade.import.after";


    public static final String TRADE_ALIBABA_FX_ADD = "trade.alibaba.fx.add";
    public static final String TRADE_ALIBABA_FX_CANCEL = "trade.alibaba.fx.cancel";
    public static final String TRADE_ALIBABA_FX_CONFIRMGOODS = "trade.alibaba.fx.confirmGoods";
    public static final String TRADE_ALIBABA_FX_TRADE_MODIFY = "trade.alibaba.fx.tradeModify";
    public static final String TRADE_ALIBABA_FX_ORDER_MODIFY = "trade.alibaba.fx.orderModify";
    public static final String TRADE_GX_CANCEL_PART_REFUND = "trade.gx.cancel.part.refund";
    public static final String TRADE_FX_EXCEP_SYNC = "trade.fx.excep.sync";
    public static final String TRADE_FX_TAG_POSSIBLE_LOSS = "trade.fx.tagPossibleLoss";
    public static final String TRADE_FX_CANCEL_ATTRIBUTE = "trade.fx.cancelAttribute";
    public static final String TRADE_FX_CHANGE_ATTRIBUTE = "trade.fx.changeAttribute";
    public static final String TRADE_FX_REMATCH_ATTRIBUTE = "trade.fx.rematchAttribute";
    public static final String TRADE_FX_CANCEL = "trade.fx.cancel";
    public static final String TRADE_QIMEN_CANCEL = "trade.qimen.cancel";
    public static final String TRADE_FX_UNAUDIT = "trade.fx.unaudit";
    public static final String TRADE_FX_MERGE_UNAUDIT = "trade.fx.merge.unaudit";
    public static final String TRADE_FX_EXPRESS_CASHFLOW = "trade.fx.express.cashFlow";
    public static final String TRADE_GX_CASH_FLOWER = "trade.gx.cashFlower";
    public static final String TRADE_GX_RESUME_FLOWER = "trade.gx.resumeFlower";
    public static final String TRADE_FX_CONSIGN = "trade.fx.consign";
    public static final String TRADE_GX_STOCK_INSUFFICIENT = "trade.gx.stock.insufficient";
    public static final String TRADE_GX_ITEM_CHANGE = "trade.gx.item.change";
    public static final String TRADE_GX_IMPORT_FORCEPUSH = "trade.gx.import.forcePush";
    public static final String TRADE_GX_ITEM_AUTO_BIND = "trade.gx.item.auto.bind";
    public static final String GX_REFRESH_PRICE_BATCH = "gx.refresh.price.batch";
    public static final String TRADE_GX_SPLIT_BATCH = "trade.gx.split.batch";
    public static final String TRADE_FX_SYNC_PRICE = "trade.fx.sync.price";
    public static final String TRADE_GX_SYNC_MULTI_PACK = "trade.gx.sync.multiPack";
    public static final String TRADE_FX_UPLOAD_RESEND = "trade.fx.upload.resend";
    public static final String TRADE_GX_ITEM_CHANGE_RECALCULATE = "trade.gx.item.change.reCalculate";
    public static final String TRADE_FX_MEMO = "trade.fx.memo";

    public static final String TRADE_FX_URGE_DELIVERY="trade.distributor.urgeDelivery";
    public static final String TRADE_FX_URGE_DELIVERY_TRACE="trade.distributor.urgeDelivery.trace";
    public static final String TRADE_TOPUP_DEHUCTION = "trade.topup.deduction";

    public static final String CHANGE_ITEM_BY_RATIO = "trade.changeItemByRatio";
    public static final String CANCEL_CHANGE_ITEM_BY_BATCH_ID = "trade.cancelChangeItemByBatchId";
    public static final String OPERATE_CHANGE_LOG = "trade.operateChangeLog";
    public static final String ITEM_CHANGE_DETAIL = "trade.itemChangeDetail";

    public static final String EXPORT_ORDER_CHANGE_ITEM_RECORD = "export.orderChangeItemRecord";

    //库存仓储一致性处理事件
    public static final String TRADE_STOCK_WMS_WHOLE_CONSUME = "trade.stock.wms.whole.consume";

    public static final String WAVE_UNIQUE_CODE_RECEIVE = "wave.unique.code.receive";

    public static final String SHOPEE_SYNC_PRINT_DATA_INIT = "shopee.sync.print.data.init";

    public static final String TRADE_DECRYPT_LOG_UPLOAD = "decrypt.log.pass.back";

    public static final String TRADE_SMTQTG_FEEDBACK_CONFIRM = "trade.smtqtg.feedback.confirm";

    public static final String TRADE_JITX_FEEDBACK_PLATFORM_WAREHOUSE = "trade.jitx.feedback.platform.warehouse";

    public static final String TRADE_POISON_FEEDBACK_PLATFORM_WAREHOUSE = "trade.poison.feedback.platform.warehouse";

    public static final String TRADE_POISON_CHANGE_PLATFORM_WAREHOUSE = "trade.poison.change.platform.warehouse";

    public static final String TRADE_TAG_WAREHOUSE_MATCH = "trade.tag.warehouse.match";

    public static final String TRADE_BACKEND_SYNC_FULLAPI = "trade.backend.sync.fullapi";

    public static final String UPDATE_SELLER_MEMO_FLAG = "trade.updateSellerMemoFlag";


    public static final String AFTER_UPDATE_SELLER_MEMO_FLAG_ASYNC = "after.trade.updateSellerMemoFlag.async";

    public static final String TRADE_QIMEN_FX_CASH_FLOW = "trade.qimen.fx.cash.flow";

    public static final String TRADE_UPDATE_WEIGHT_AND_VOLUME = "trade.updateWeightAndVolume";

    public static final String TRADE_RECALC_POST_FEE = "trade.recalc.postfee";

    public static final String TRADE_FX_SYNC = "trade.fx.sync";


    public static final String TRADE_UPDATE_ACTUAL_VOLUME = "trade.updateActualVolume";

    public static final String TRADE_WAREHOUSE_CHANGE_REMATCH_ORDER_PRODUCT = "warehouse.change.rematch.orderProduct";
    public static final String TRADE_FX_COMMISSION_UPLOAD = "trade.fx.commission.upload";

    public static final String ORDER_HOT_ITEM_SYNC = "order.hot.item.sync";

}

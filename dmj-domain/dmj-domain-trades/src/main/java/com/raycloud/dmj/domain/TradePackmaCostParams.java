package com.raycloud.dmj.domain;

import java.io.Serializable;

public class TradePackmaCostParams implements Serializable {
    private Long sid;
    private Double packmaCost;
    private Double packmaWeight;
    private Double packmaVolume;
    /**
     * 订单包材更新场景
     *
     */
    private String scene;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Double getPackmaCost() {
        return packmaCost;
    }

    public void setPackmaCost(Double packmaCost) {
        this.packmaCost = packmaCost;
    }

    public Double getPackmaWeight() {
        return packmaWeight;
    }

    public void setPackmaWeight(Double packmaWeight) {
        this.packmaWeight = packmaWeight;
    }

    public Double getPackmaVolume() {
        return packmaVolume;
    }

    public void setPackmaVolume(Double packmaVolume) {
        this.packmaVolume = packmaVolume;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}

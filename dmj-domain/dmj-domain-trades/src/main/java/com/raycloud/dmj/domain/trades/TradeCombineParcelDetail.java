package com.raycloud.dmj.domain.trades;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TradeCombineParcelDetail implements Serializable {
    private static final long serialVersionUID = 4642628715993155874L;

    private Integer tradeNum = 0;

    private Integer orderNum = 0;

    private List<TradeParcelBTASDetail> parcelDetails;

    /**
     * 组包状态 1 待出库 2已出库 3已取消
     */
    private Integer status;

}

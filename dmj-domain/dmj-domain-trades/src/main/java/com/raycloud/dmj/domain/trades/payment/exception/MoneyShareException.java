package com.raycloud.dmj.domain.trades.payment.exception;

/**
 * @Description <pre>
 * 金额分摊异常
 * </pre>
 * <AUTHOR>
 * @Date 2023-02-22
 */
public class MoneyShareException extends RuntimeException{

    public MoneyShareException() {
    }

    public MoneyShareException(String message) {
        super(message);
    }

    public MoneyShareException(String message, Throwable cause) {
        super(message, cause);
    }

    public MoneyShareException(Throwable cause) {
        super(cause);
    }

    public MoneyShareException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

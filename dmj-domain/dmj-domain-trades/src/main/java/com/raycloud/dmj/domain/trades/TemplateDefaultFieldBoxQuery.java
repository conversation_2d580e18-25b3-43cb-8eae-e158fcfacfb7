package com.raycloud.dmj.domain.trades;

import java.util.*;

/**
 * TemplateDefaultFieldBox  Query
 *
 * <AUTHOR>
 */
public class TemplateDefaultFieldBoxQuery {

    private static final long serialVersionUID = -1L;

    /**==============================批量查询、更新、删除时的Where条件设置==================================**/
    /**
     * id
     **/
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * company_id
     **/
    private Long companyId;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * 文本框内容包括设计内容
     **/
    private String fieldBoxValue;

    public String getFieldBoxValue() {
        return fieldBoxValue;
    }

    public void setFieldBoxValue(String fieldBoxValue) {
        this.fieldBoxValue = fieldBoxValue;
    }

    /**
     * 后文字 冗余的供查询用
     **/
    private String fieldBoxAfter;

    public String getFieldBoxAfter() {
        return fieldBoxAfter;
    }

    public void setFieldBoxAfter(String fieldBoxAfter) {
        this.fieldBoxAfter = fieldBoxAfter;
    }

    /**
     * 前文字 和后文字一样
     **/
    private String fieldBoxBefore;

    public String getFieldBoxBefore() {
        return fieldBoxBefore;
    }

    public void setFieldBoxBefore(String fieldBoxBefore) {
        this.fieldBoxBefore = fieldBoxBefore;
    }

    /**
     * 所述模板分类 0 商家编码 1 吊牌 2 快递单 需求来了再扩展
     **/
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 创建时间
     **/
    private Date createdStart;

    public Date getCreatedStart() {
        return createdStart;
    }

    public void setCreatedStart(Date created) {
        this.createdStart = created;
    }

    private Date createdEnd;

    public Date getCreatedEnd() {
        return createdEnd;
    }

    public void setCreatedEnd(Date created) {
        this.createdEnd = created;
    }

    private Date createdEqual;

    public Date getCreatedEqual() {
        return createdEqual;
    }

    public void setCreatedEqual(Date created) {
        this.createdEqual = created;
    }

    /**
     * 修改时间
     **/
    private Date modifiedStart;

    public Date getModifiedStart() {
        return modifiedStart;
    }

    public void setModifiedStart(Date modified) {
        this.modifiedStart = modified;
    }

    private Date modifiedEnd;

    public Date getModifiedEnd() {
        return modifiedEnd;
    }

    public void setModifiedEnd(Date modified) {
        this.modifiedEnd = modified;
    }

    private Date modifiedEqual;

    public Date getModifiedEqual() {
        return modifiedEqual;
    }

    public void setModifiedEqual(Date modified) {
        this.modifiedEqual = modified;
    }

    /**
     * 逻辑删除
     **/
    private Integer enableStatus;

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * ==============================批量查询时的Order条件顺序设置==================================
     **/
    public class OrderField {
        public OrderField(String fieldName, String order) {
            super();
            this.fieldName = fieldName;
            this.order = order;
        }

        private String fieldName;
        private String order;

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getOrder() {
            return order;
        }

        public void setOrder(String order) {
            this.order = order;
        }
    }

    /**==============================批量查询时的Order条件顺序设置==================================**/
    /**
     * 排序列表字段
     **/
    private List<OrderField> orderFields = new ArrayList<OrderField>();

    /**
     * 设置排序按属性：id
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyId(boolean isAsc) {
        orderFields.add(new OrderField("id", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：company_id
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyCompanyId(boolean isAsc) {
        orderFields.add(new OrderField("company_id", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：文本框内容包括设计内容
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyFieldBoxValue(boolean isAsc) {
        orderFields.add(new OrderField("field_box_value", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：后文字 冗余的供查询用
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyFieldBoxAfter(boolean isAsc) {
        orderFields.add(new OrderField("field_box_after", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：前文字 和后文字一样
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyFieldBoxBefore(boolean isAsc) {
        orderFields.add(new OrderField("field_box_before", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：所述模板分类 0 商家编码 1 吊牌 2 快递单 需求来了再扩展
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyType(boolean isAsc) {
        orderFields.add(new OrderField("type", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：创建时间
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyCreated(boolean isAsc) {
        orderFields.add(new OrderField("created", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：修改时间
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyModified(boolean isAsc) {
        orderFields.add(new OrderField("modified", isAsc ? "ASC" : "DESC"));
    }

    /**
     * 设置排序按属性：逻辑删除
     *
     * @param isAsc 是否升序，否则为降序
     */
    public void orderbyEnableStatus(boolean isAsc) {
        orderFields.add(new OrderField("enable_status", isAsc ? "ASC" : "DESC"));
    }

    private String fields;
    /**
     * 提供自定义字段使用
     */
    private static Map<String, String> fieldMap;

    private static Map<String, String> getFieldSet() {
        if (fieldMap == null) {
            fieldMap = new HashMap<String, String>();
            fieldMap.put("id", "id");
            fieldMap.put("company_id", "companyId");
            fieldMap.put("field_box_value", "fieldBoxValue");
            fieldMap.put("field_box_after", "fieldBoxAfter");
            fieldMap.put("field_box_before", "fieldBoxBefore");
            fieldMap.put("type", "type");
            fieldMap.put("created", "created");
            fieldMap.put("modified", "modified");
            fieldMap.put("enable_status", "enableStatus");
        }
        return fieldMap;
    }

    public String getFields() {
        return this.fields;
    }

    public void setFields(String fields) {
        String[] array = fields.split(",");
        StringBuilder buffer = new StringBuilder();
        for (String field : array) {
            if (getFieldSet().containsKey(field)) {
                buffer.append(field).append(" as ").append(getFieldSet().get(field)).append(" ,");
            }
            if (getFieldSet().containsKey("`" + field + "`")) {
                buffer.append("`" + field + "`").append(" as ").append(getFieldSet().get(field)).append(" ,");
            }
        }
        if (buffer.length() != 0) {
            this.fields = buffer.substring(0, buffer.length() - 1);
        } else {
            this.fields = " 1 ";//没有一个参数可能会报错
        }
    }
}

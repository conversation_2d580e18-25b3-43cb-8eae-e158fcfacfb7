package com.raycloud.dmj.domain.trades.payment.util;

import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description <pre>
 * 对于前端传入的金额范围条件 因前端是做了四舍五入的,因此需要转换为四舍五入前的真实范围 再进行数据过滤
 *
 * 对于前端展示数值n，其对应的进位范围为
 * n>0
 * n-0.005 ~ n+0.004999
 * n=0
 * -0.004999 ~ +0.004999
 * n<0
 * n-0.004999 ~ n+0.005
 *
 * eg. 0.01 其对应数值范围    0.005 ~ 0.014999
 *     -0.01 其对应数值范围   -0.014999 ~ -0.005
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-29
 */
public class PaymentRangeUtils {

    private static final Logger logger = Logger.getLogger(PaymentRangeUtils.class);

   private static Map<Integer,BigDecimal> FOUR_NINE_MAP = new HashMap<>();
    private static Map<Integer,BigDecimal> FIVE_MAP = new HashMap<>();

   static {
       FOUR_NINE_MAP.put(1,new BigDecimal(0.049999));
       FOUR_NINE_MAP.put(2,new BigDecimal(0.004999));
       FOUR_NINE_MAP.put(3,new BigDecimal(0.000499));
       FOUR_NINE_MAP.put(4,new BigDecimal(0.000049));

       FIVE_MAP.put(1,new BigDecimal(0.05));
       FIVE_MAP.put(2,new BigDecimal(0.005));
       FIVE_MAP.put(3,new BigDecimal(0.0005));
       FIVE_MAP.put(4,new BigDecimal(0.00005));
   }


    public static BigDecimal[] getRange(Long companyId,BigDecimal a,BigDecimal b){
        if (a == null && b == null) {
            throw new RuntimeException("数值范围不能都为空");
        }
        BigDecimal[] ax = null;
        if (a !=null) {
            ax = getRange(companyId,a);
        }
        BigDecimal[] bx = null;
        if (b != null) {
            bx = getRange(companyId,b);
        }
        return new BigDecimal[]{
                ax==null?null:ax[0],
                bx==null?null:bx.length == 1?bx[0]:bx[1]
        };
    }

    public static BigDecimal[] getRange(Long companyId,BigDecimal a){
        //这里要获取配置
        Integer scale = null;
        IPaymentSwitcher switcher = IPaymentSwitcher.getInstance();
        if (switcher == null) {
            new PaymentLogBuilder(companyId).append("未获取到IPaymentSwitcher实例 使用系统默认精度").printWarn(logger);
        }else {
            scale = switcher.getScale(companyId);
        }

        //没有配置精度的企业,直接按原始值过滤即可
        if (scale == null) {
            return new BigDecimal[]{a};
        }

        a = a.setScale(scale,BigDecimal.ROUND_HALF_UP);
        int zero = a.compareTo(BigDecimal.ZERO);

        if (zero == 0) {
            return new BigDecimal[]{
                    FOUR_NINE_MAP.get(scale).negate().setScale(6,BigDecimal.ROUND_HALF_UP),
                    FOUR_NINE_MAP.get(scale).setScale(6,BigDecimal.ROUND_HALF_UP)
            };
        }
        if (zero > 0) {
            return new BigDecimal[]{
                    a.subtract(FIVE_MAP.get(scale)).setScale(6,BigDecimal.ROUND_HALF_UP),
                    a.add(FOUR_NINE_MAP.get(scale)).setScale(6,BigDecimal.ROUND_HALF_UP)
            };
        }
        return new BigDecimal[]{
                a.subtract(FOUR_NINE_MAP.get(scale)).setScale(6,BigDecimal.ROUND_HALF_UP),
                a.add(FIVE_MAP.get(scale)).setScale(6,BigDecimal.ROUND_HALF_UP)
        };
    }

    public static void main(String[] args) {
        test(0.01);
        test(0.00);
        test(-0.01);
        test(1);
        test(1.5);
        test(1.234);
    }

    public static void test(double s) {
        //这里要获取配置
        int scale = 2;
        BigDecimal[] range = getRange(-1L, new BigDecimal(s));
        System.out.println("输入:"+s);
        System.out.println("输出:"+range[0].toPlainString() +" " + range[1].toPlainString());
        System.out.println("校验:"+range[0].setScale(scale,BigDecimal.ROUND_HALF_UP) + " " +range[1].setScale(scale,BigDecimal.ROUND_HALF_UP));
    }
}

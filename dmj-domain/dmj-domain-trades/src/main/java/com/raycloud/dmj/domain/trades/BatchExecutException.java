package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * 批处理执行异常，当执行批处理操作时，记录批处理的失败的对象和原因，并抛出异常
 *
 * <AUTHOR>
 */
public class BatchExecutException extends RuntimeException {

    /**
     *
     */
    private static final long serialVersionUID = 2761315536772346216L;

    public final static String TRADE_REFUND = "20101";
    public final static String TRADE_HALT = "20102";
    public final static String TRADE_EXCEP = "20103";
    public final static String TRADE_CLOSED = "20100";
    public final static String TRADE_NOT_AUDIT = "20104";
    public final static String TRADE_STOCK_NOT_ALLOCATED = "20105";
    public final static String TRADE_STOCK_INSUFFICIENT = "20106";
    public final static String TRADE_TEMPLATE_EMPTY = "20107";

    private final List<ExceptionReport> reports;

    private final List<ExceptionReport> successsReports;

    public BatchExecutException(String Msg, List<ExceptionReport> reports, List<ExceptionReport> successsReports) {
        super(Msg);
        this.reports = reports;
        this.successsReports = successsReports;
    }

    public List<ExceptionReport> getReports() {
        return reports;
    }

    public List<ExceptionReport> getSuccesssReports() {
        return successsReports;
    }

    /**
     * 异常报告
     *
     * <AUTHOR>
     */
    public static class ExceptionReport implements Serializable {

        /**
         *
         */
        private static final long serialVersionUID = 8256256510136162877L;

        private String sid;
        
        private String tid;

        private String id;

        private Long taobaoId;

        private String errorMsg;

        private String subMsg;

        private String errorCode;


        public ExceptionReport() {
        }

        public String getTid() {
            return tid;
        }

        public ExceptionReport setTid(String tid) {
        	if(null != tid)
        		this.tid = tid.toString();
            return this;
        }

        public Long getTaobaoId() {
            return taobaoId;
        }

        public ExceptionReport setTaobaoId(Long taobaoId) {
            this.taobaoId = taobaoId;
            return this;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public ExceptionReport setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
            return this;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public ExceptionReport setErrorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }


        public String getSubMsg() {
            return subMsg;
        }

        public ExceptionReport setSubMsg(String subMsg) {
            this.subMsg = subMsg;
            return this;
        }

        public String getId() {
            return id;
        }

        public ExceptionReport setId(Long id) {
        	if(null != id)
        		this.id = id.toString();
            return this;
        }

        public String getSid() {
			return sid;
		}

		public ExceptionReport setSid(String sid) {
			this.sid = sid;
            return this;
		}

		@Override
        public String toString() {
            return "{" +
                    "sid='" + sid + '\'' +
                    "tid='" + tid + '\'' +
                    ", id='" + id + '\'' +
                    ", taobaoId=" + taobaoId +
                    ", errorMsg='" + errorMsg + '\'' +
                    ", subMsg='" + subMsg + '\'' +
                    ", errorCode='" + errorCode + '\'' +
                    '}';
        }
    }
}

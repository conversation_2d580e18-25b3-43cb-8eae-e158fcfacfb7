package com.raycloud.dmj.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-22 14:43
 */
public class ObjectParser<T> {

    private Class<T> type;

    public ObjectParser(Class<T> type) {
        this.type = type;
    }

    public T string2Object(String jsonObject) {
        return JSON.parseObject(jsonObject, type);
    }

    public T json2Object(JSONObject object) {
        return JSON.toJavaObject(object, type);
    }

    public List<T> string2List(String jsonArray) {
        return JSON.parseArray(jsonArray, type);
    }

    public List<T> json2List(JSONArray array) {
        List<T> list = new ArrayList<T>(array.size());
        for (Object object : array) {
            list.add(json2Object((JSONObject) object));
        }
        return list;
    }
}

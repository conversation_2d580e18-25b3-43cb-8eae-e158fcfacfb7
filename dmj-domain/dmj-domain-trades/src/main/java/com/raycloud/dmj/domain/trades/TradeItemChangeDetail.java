package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/12/16 下午4:22
 **/
public class TradeItemChangeDetail extends Model implements Serializable {

    private static final long serialVersionUID = -5088950795633074019L;

    private Long id;

    private Long companyId;

    private Long batchId;

    /**
     * 操作人
     */
    private String operator;

    private String sids;

    private String tids;

    private String oids;

    private List<Long> sidsList;

    private List<String> tidsList;

    private List<Long> oidsList;

    /**
     * 主商家编码
     */
    private String outerId;

    /**
     * 更换前系统规格id
     */
    private Long oldSysSkuId;

    /**
     * 更换后系统规格id
     */
    private Long newSysSkuId;

    /**
     * 换商品前的规格商家编码
     */
    private String oldSkuOuterId;

    /**
     * 换商品后的规格商家编码
     */
    private String newSkuOuterId;

    /**
     * 关联码
     */
    private String relatingCode;

    /**
     * 外采+工厂 商品总数
     */
    private Integer total = 0;

    /**
     * 换商品前的外采商品数量
     */
    private Integer oldOutsideNum = 0;


    /**
     * 更换后实际的外采商品数量
     */
    private Integer actualOutsideNum = 0;

    /**
     * 需要更换的商品数量
     */
    private Integer needChangeItemNum = 0;

    /**
     * 实际更换成功的商品数量
     */
    private Integer actualChangeSucNum = 0;

    /**
     * 实际更换失败的商品数量
     */
    private Integer actualChangeFailNum = 0;

    /**
     * 外采商品原本的比例
     */
    private Integer oldRatio = 0;

    /**
     * 应调整为的比例
     */
    private Integer shouldRatio = 0;

    /**
     * 调整后实际的比例
     */
    private Integer actualRatio = 0;

    /**
     * 统计信息
     */
    private String statistics;

    /**
     * 用于json字段存储
     */
    private List<String> statisticsList;

    private Date created;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSids() {
        return sids;
    }

    public void setSids(String sids) {
        this.sids = sids;
    }

    public String getTids() {
        return tids;
    }

    public void setTids(String tids) {
        this.tids = tids;
    }

    public String getOids() {
        return oids;
    }

    public void setOids(String oids) {
        this.oids = oids;
    }

    public List<Long> getSidsList() {
        return sidsList;
    }

    public void setSidsList(List<Long> sidsList) {
        this.sidsList = sidsList;
    }

    public List<String> getTidsList() {
        return tidsList;
    }

    public void setTidsList(List<String> tidsList) {
        this.tidsList = tidsList;
    }

    public List<Long> getOidsList() {
        return oidsList;
    }

    public void setOidsList(List<Long> oidsList) {
        this.oidsList = oidsList;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Long getOldSysSkuId() {
        return oldSysSkuId;
    }

    public void setOldSysSkuId(Long oldSysSkuId) {
        this.oldSysSkuId = oldSysSkuId;
    }

    public Long getNewSysSkuId() {
        return newSysSkuId;
    }

    public void setNewSysSkuId(Long newSysSkuId) {
        this.newSysSkuId = newSysSkuId;
    }

    public String getOldSkuOuterId() {
        return oldSkuOuterId;
    }

    public void setOldSkuOuterId(String oldSkuOuterId) {
        this.oldSkuOuterId = oldSkuOuterId;
    }

    public String getNewSkuOuterId() {
        return newSkuOuterId;
    }

    public void setNewSkuOuterId(String newSkuOuterId) {
        this.newSkuOuterId = newSkuOuterId;
    }

    public String getRelatingCode() {
        return relatingCode;
    }

    public void setRelatingCode(String relatingCode) {
        this.relatingCode = relatingCode;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getOldOutsideNum() {
        return oldOutsideNum;
    }

    public void setOldOutsideNum(Integer oldOutsideNum) {
        this.oldOutsideNum = oldOutsideNum;
    }

    public Integer getActualOutsideNum() {
        return actualOutsideNum;
    }

    public void setActualOutsideNum(Integer actualOutsideNum) {
        this.actualOutsideNum = actualOutsideNum;
    }

    public Integer getNeedChangeItemNum() {
        return needChangeItemNum;
    }

    public void setNeedChangeItemNum(Integer needChangeItemNum) {
        this.needChangeItemNum = needChangeItemNum;
    }

    public Integer getActualChangeSucNum() {
        return actualChangeSucNum;
    }

    public void setActualChangeSucNum(Integer actualChangeSucNum) {
        this.actualChangeSucNum = actualChangeSucNum;
    }

    public Integer getActualChangeFailNum() {
        return actualChangeFailNum;
    }

    public void setActualChangeFailNum(Integer actualChangeFailNum) {
        this.actualChangeFailNum = actualChangeFailNum;
    }

    public Integer getOldRatio() {
        return oldRatio;
    }

    public void setOldRatio(Integer oldRatio) {
        this.oldRatio = oldRatio;
    }

    public Integer getShouldRatio() {
        return shouldRatio;
    }

    public void setShouldRatio(Integer shouldRatio) {
        this.shouldRatio = shouldRatio;
    }

    public Integer getActualRatio() {
        return actualRatio;
    }

    public void setActualRatio(Integer actualRatio) {
        this.actualRatio = actualRatio;
    }

    public String getStatistics() {
        return statistics;
    }

    public void setStatistics(String statistics) {
        this.statistics = statistics;
    }

    public List<String> getStatisticsList() {
        return statisticsList;
    }

    public void setStatisticsList(List<String> statisticsList) {
        this.statisticsList = statisticsList;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }
}

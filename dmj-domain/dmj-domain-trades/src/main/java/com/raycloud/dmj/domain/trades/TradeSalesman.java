package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Description 业务员
 * <AUTHOR>
 * @Date 2022/9/1
 */
@Setter
@Getter
public class TradeSalesman extends Model {


    private static final long serialVersionUID = -5285094040727136296L;

    private Long id;

    private Date created;

    private Date modified;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 订单ID
     */
    private Long sid;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 有效 0 无效 1有效
     */
    private Integer enableStatus;
}

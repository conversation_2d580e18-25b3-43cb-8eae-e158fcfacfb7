package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * 交易商品标签查询params
 */
public class TradeQueryItemTagIdsParams implements Serializable {

    private static final long serialVersionUID = -8575623308175303221L;

    /**
     * 包含
     */
    private List<Long> containItemTagIds;

    /**
     * 排除
     */
    private List<Long> notContainItemTagIds;

    public List<Long> getContainItemTagIds() {
        return containItemTagIds;
    }

    public void setContainItemTagIds(List<Long> containItemTagIds) {
        this.containItemTagIds = containItemTagIds;
    }

    public List<Long> getNotContainItemTagIds() {
        return notContainItemTagIds;
    }

    public void setNotContainItemTagIds(List<Long> notContainItemTagIds) {
        this.notContainItemTagIds = notContainItemTagIds;
    }

}

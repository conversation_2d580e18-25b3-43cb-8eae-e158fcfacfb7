package com.raycloud.dmj.domain.trades.tradepay.params;

import com.raycloud.dmj.domain.Page;

import java.io.Serializable;

/**
 * @Author: chenchaochao
 * @Date: 2020/3/20 2:43 下午
 */
public class TradePayQueryParams extends Page implements Serializable {
    private static final long serialVersionUID = 5934868784584704701L;

    public static final String FIELD_PAY_DATE = "payDate";

    public static final String FIELD_PAY_TIME = "payTime";

    /**
     * 系统单号
     */
    private Long sid;
    /**
     * 内部单号
     */
    private Long shortId;
    /**
     * 店铺Id,多选
     */
    private String userId;
    /**
     * 平台交易号
     */
    private String tid;
    /**
     * 开始日期
     */
    private String startTime;
    /**
     * 结束日期
     */
    private String endTime;
    /**
     * 买家账号
     */
    private String buyerAccountNo;
    /**
     * 收款账号
     */
    private String accountNo;
    /**
     * 支付单状态
     */
    private Integer orderStatus;
    /**
     * 支付方式
     */
    private Integer payType;
    /**
     * 支付单id
     */
    private String payId;
    /**
     * payDate:支付日期 payTime:付款时间
     */
    private String field;


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBuyerAccountNo() {
        return buyerAccountNo;
    }

    public void setBuyerAccountNo(String buyerAccountNo) {
        this.buyerAccountNo = buyerAccountNo;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }
}

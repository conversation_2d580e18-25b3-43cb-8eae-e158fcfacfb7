package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.trades.Order;

/**
 * @description:金额联动，增/删/改商品时，实付金额（order）、订单实付金额（trade）跟着变化
 * @author: pxh
 * @create: 2021-07-29 17:59
 **/
public class PaymentLinkStrategy implements IPaymentLinkStrategy {

    /**
     * 订单金额联动
     *
     * @param paymentLinkReqDTO
     * @return
     */
    @Override
    public PaymentLinkRespDTO insertOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        Order order = paymentLinkReqDTO.getOrder();
        return commonPaymentLink(order);
    }

    @Override
    public PaymentLinkRespDTO updateOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return insertOrder(paymentLinkReqDTO);
    }

    @Override
    public PaymentLinkRespDTO deleteOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        return insertOrder(paymentLinkReqDTO);
    }

    @Override
    public boolean support(OpenLinkConfigEnum openLinkConfigEnum) {
        return OpenLinkConfigEnum.ORDER_AND_TRADE_PAYMENT_CHANGE_LINK.equals(openLinkConfigEnum);
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trade.except.TradeExceptViewUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

public class TradeExceptionStatusUtils extends TradeExceptionUtils {

    /**
     * 异常状态转中文
     * @param exceptions
     * @return
     */
    public static String getExceptionToString (Set<String> exceptions) {
        if(CollectionUtils.isEmpty(exceptions)) {
            return "";
        }
        return TradeExceptViewUtils.english2Chinese(exceptions);
    }
}

package com.raycloud.dmj.domain.logistics;

public enum EnumIcbuProductFeatureType {

    COMMON_GOODS(0, "general","普货"),
    WITH_BATTERY(1, "battery","电池"),
    TORT(2, "general","普货"),
    MAGNETIC_BELT(3, "general","普货"),
    NON_LIQUID_COSMETIC(4, "cream","膏状"),
    LIQUID_COSMETIC(5, "aquiform","水状"),
    LIQUID_NON_COSMETIC(6, "liquor","液体"),
    POWDER(7, "stive","粉末"),
    PASTE(8, "cream","膏状");

    private Integer productFeatureId;

    private String productFeatureCode;

    private String productFeatureName;

    EnumIcbuProductFeatureType() {
    }

    EnumIcbuProductFeatureType(Integer productFeatureId, String productFeatureCode, String productFeatureName) {
        this.productFeatureId = productFeatureId;
        this.productFeatureCode = productFeatureCode;
        this.productFeatureName = productFeatureName;
    }

    public Integer getProductFeatureId() {
        return productFeatureId;
    }

    public void setProductFeatureId(Integer productFeatureId) {
        this.productFeatureId = productFeatureId;
    }

    public String getProductFeatureCode() {
        return productFeatureCode;
    }

    public void setProductFeatureCode(String productFeatureCode) {
        this.productFeatureCode = productFeatureCode;
    }

    public String getProductFeatureName() {
        return productFeatureName;
    }

    public void setProductFeatureName(String productFeatureName) {
        this.productFeatureName = productFeatureName;
    }

    public static EnumIcbuProductFeatureType getEnumIcbuProductFeatureType(Integer productFeatureId) {
        for (EnumIcbuProductFeatureType value : EnumIcbuProductFeatureType.values()) {
                if(value.productFeatureId.equals(productFeatureId)) {
                    return value;
                }
            }
        return COMMON_GOODS;
    }
}

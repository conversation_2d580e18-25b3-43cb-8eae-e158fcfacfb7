package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * Date    2019-05-17
 */
public class PrintConfig implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * copmany_id
     */
    private Long copmanyId;
    /**
     * 逻辑删除 1 未删除 0 已删除
     */
    private Integer enableStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 排序方式 6 仅按商品款号 8 仅按商品名称 5 仅按商品简称 4 仅按商品备注 7 仅按规格备注 21 先按供应商再按商品（款号+颜色+尺寸 22 先按供应商再按商品最后按付款时间
     */
    private Integer merchantCodeSort;
    /**
     * 单件显示内容
     */
    private String singleRemark;
    /**
     * 多件显示内容
     */
    private String muchRemark;

    /**
     * 模版id保存
     */
    private String analysisTemplateId;

    /**
     * 用于扩展的配置字段
     */
    private String extensibleConfigJson;

    private ExtensiblePrintConfig extensiblePrintConfig;

    /**
     * 前端配置
     */
    private String frontConfig;
    /**
     * 分库号
     */
    private Integer dbKey;

    /**
     * 翱象 已经上传到平台的cpCode 也就是支持启用了的
     */
    private String aoxiangCpCode;

    /**
     * 自动打印模版和打印机设置(根据账号维度staffId，是一个json数组)
     */
    private String autoPrinterSettings;

    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return copmanyId copmany_id
     */
    public Long getCopmanyId() {
        return copmanyId;
    }

    /**
     * @param copmanyId copmany_id
     */
    public void setCopmanyId(Long copmanyId) {
        this.copmanyId = copmanyId;
    }

    /**
     * @return enableStatus 逻辑删除 1 未删除 0 已删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除 1 未删除 0 已删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return merchantCodeSort 排序方式 6 仅按商品款号 8 仅按商品名称 5 仅按商品简称 4 仅按商品备注 7 仅按规格备注 21 先按供应商再按商品（款号+颜色+尺寸 22 先按供应商再按商品最后按付款时间
     */
    public Integer getMerchantCodeSort() {
        return merchantCodeSort;
    }

    /**
     * @param merchantCodeSort 排序方式 6 仅按商品款号 8 仅按商品名称 5 仅按商品简称 4 仅按商品备注 7 仅按规格备注 21 先按供应商再按商品（款号+颜色+尺寸 22 先按供应商再按商品最后按付款时间
     */
    public void setMerchantCodeSort(Integer merchantCodeSort) {
        this.merchantCodeSort = merchantCodeSort;
    }

    /**
     * @return singleRemark 单件显示内容
     */
    public String getSingleRemark() {
        return singleRemark;
    }

    /**
     * @param singleRemark 单件显示内容
     */
    public void setSingleRemark(String singleRemark) {
        this.singleRemark = singleRemark;
    }

    /**
     * @return muchRemark 多件显示内容
     */
    public String getMuchRemark() {
        return muchRemark;
    }

    /**
     * @param muchRemark 多件显示内容
     */
    public void setMuchRemark(String muchRemark) {
        this.muchRemark = muchRemark;
    }

    /**
     * @return extensibleComfigJson 用于扩展的配置字段
     */
    public String getExtensibleConfigJson() {
        return extensibleConfigJson;
    }

    /**
     * @param extensibleConfigJson 用于扩展的配置字段
     */
    public void setExtensibleConfigJson(String extensibleConfigJson) {
        this.extensibleConfigJson = extensibleConfigJson;
        if (StringUtils.isNotBlank(extensibleConfigJson)) {
            try {
                ExtensiblePrintConfig extensiblePrintConfig = JSON.parseObject(extensibleConfigJson, ExtensiblePrintConfig.class);
                this.setExtensiblePrintConfig(extensiblePrintConfig);
            } catch (Exception e) {
                Logs.error(String.format("json parse err ,json 【%s】, errMsg 【%s】", extensibleConfigJson, e.getMessage()), e);
            }
        }
    }

    public ExtensiblePrintConfig getExtensiblePrintConfig() {
        return extensiblePrintConfig;
    }

    public void setExtensiblePrintConfig(ExtensiblePrintConfig extensiblePrintConfig) {
        this.extensiblePrintConfig = extensiblePrintConfig;
        if (extensiblePrintConfig != null) {
            this.extensibleConfigJson = JSONObject.toJSONString(extensiblePrintConfig);
        }
    }

    public Integer getDbKey() {
        return dbKey;
    }

    public void setDbKey(Integer dbKey) {
        this.dbKey = dbKey;
    }

    public String getFrontConfig() {
        return frontConfig;
    }

    public void setFrontConfig(String frontConfig) {
        this.frontConfig = frontConfig;
    }

    public String getAnalysisTemplateId() {
        return analysisTemplateId;
    }

    public void setAnalysisTemplateId(String analysisTemplateId) {
        this.analysisTemplateId = analysisTemplateId;
    }

    public String getAoxiangCpCode() {
        return aoxiangCpCode;
    }

    public void setAoxiangCpCode(String aoxiangCpCode) {
        this.aoxiangCpCode = aoxiangCpCode;
    }

    public String getAutoPrinterSettings() {
        return autoPrinterSettings;
    }

    public void setAutoPrinterSettings(String autoPrinterSettings) {
        this.autoPrinterSettings = autoPrinterSettings;
    }

}
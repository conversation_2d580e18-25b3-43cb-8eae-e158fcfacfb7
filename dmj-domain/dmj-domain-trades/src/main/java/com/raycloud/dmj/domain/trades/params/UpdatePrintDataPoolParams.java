package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.user.User;

import java.io.Serializable;
import java.util.List;

public class UpdatePrintDataPoolParams implements Serializable {
    private static final long serialVersionUID = 7525827158718528589L;

    private Staff staff;

    private User user;

    private List<Long> sids;

    private UserWlbExpressTemplate userWlbExpressTemplate;

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public UserWlbExpressTemplate getUserWlbExpressTemplate() {
        return userWlbExpressTemplate;
    }

    public void setUserWlbExpressTemplate(UserWlbExpressTemplate userWlbExpressTemplate) {
        this.userWlbExpressTemplate = userWlbExpressTemplate;
    }
}

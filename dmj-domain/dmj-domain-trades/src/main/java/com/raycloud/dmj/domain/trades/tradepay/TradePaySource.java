package com.raycloud.dmj.domain.trades.tradepay;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/3/19 2:48 下午
 */
public enum TradePaySource {
    SOURCE_PLATFORM(2, "平台创建"),
    SOURCE_CUSTOM(1, "手工创建");

    private int value;

    private String name;

    TradePaySource(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static TradePaySource parseOfValue(int value) {
        for (TradePaySource source : TradePaySource.values()) {
            if (source.value == value) {
                return source;
            }
        }

        return null;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;

public class UserCommonUtils {
    /**
     * 判断用户是否是天猫超市/天猫国际直营/天猫优品
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isTmcsUserTrade(Staff staff, Trade trade){
        User user = staff.getUserIdMap().get(trade.getUserId());
        if(user!=null && CommonConstants.PLAT_FORM_TYPE_TMCS.equals(user.getSource())){
            return true;
        }
        if(user!=null && CommonConstants.PLAT_FORM_TYPE_TMYP.equals(user.getSource())){
            return true;
        }
        if(user!=null && CommonConstants.PLAT_FORM_TYPE_TMGJZY.equals(user.getSource())){
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否是腾讯云
     *
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isTxyUserTrade(Staff staff, Trade trade) {
        User user = staff.getUserIdMap().get(trade.getUserId());
        if (user != null && CommonConstants.PLAT_FORM_TYPE_TXY.equals(user.getSource())) {
            return true;
        }
        return false;
    }
}

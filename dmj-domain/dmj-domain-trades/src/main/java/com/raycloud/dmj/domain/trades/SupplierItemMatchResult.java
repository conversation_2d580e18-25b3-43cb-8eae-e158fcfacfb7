package com.raycloud.dmj.domain.trades;

import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierItemMatchResult implements Serializable {

    /**
     * 是否匹配成功
     */
    private Boolean matchSuccess;

    /**
     * 匹配失败的msg
     */
    private String errorMsg;

    /**
     * 供销商公司id
     */
    private Long supplierCompanyId;

    /**
     * isSku=true -> sku.outerId
     * isSku=false -> item.outerId
     */
    private String outerId;

    private Boolean ifSku;

    /**
     * 商品的分销价
     */
    private Double fxPrice;

    private Long supplierSysItemId;

    private Long supplierSysSkuId;

    public static SupplierItemMatchResult success(Long supplierCompanyId, String outerId, Boolean ifSku, Double fxPrice, Long supplierSysItemId, Long supplierSysSkuId) {
        SupplierItemMatchResult result = new SupplierItemMatchResult();
        result.matchSuccess = Boolean.TRUE;
        result.supplierCompanyId = supplierCompanyId;
        result.outerId = outerId;
        result.ifSku = ifSku;
        result.fxPrice = fxPrice;
        result.supplierSysItemId = supplierSysItemId;
        result.supplierSysSkuId = supplierSysSkuId;
        return result;
    }

    public static SupplierItemMatchResult fail(String errorMsg) {
        SupplierItemMatchResult result = new SupplierItemMatchResult();
        result.matchSuccess = Boolean.FALSE;
        result.errorMsg = errorMsg;
        return result;
    }

    private SupplierItemMatchResult() {
    }
}

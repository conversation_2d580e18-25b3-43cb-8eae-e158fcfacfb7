package com.raycloud.dmj.domain;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.domain.trades.utils.DateUtils;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by CXW on 2016/12/5.
 */
public class Query implements Cloneable{

    /**
     * 公共的where条件语句
     */
    private StringBuilder q = new StringBuilder();

    /**
     * order by排序语句
     */
    private String sort;

    /**
     * order by排序语句
     */
    private Sort sortTrade;

    /**
     * count 字段
     */
    private String countField;

    /**
     * group by part
     */
    private String groupByPart;

    /**
     * 是否保存sql
     */
    private boolean needSaveSql = false;

    /**
     * 完整的sql
     */
    private String wholeSql;

    /**
     * 与q中的'?'对应的参数列表
     */
    private List<Object> args = new ArrayList<Object>();

    private String tradeTable = "trade";
    private String orderTable = "order";
    private String tradeExtTable = "trade_ext";
    private String orderExtTable = "order_ext";
    private String tradeExceptTable = "trade_except";

    /**
     * 在查询的query中是否存在主键或者hash索引的查询
     * Jacky LIU 2019-12-03
     */
    private boolean containHashKey = false;

    private boolean allowedPgl = false;

    private boolean onlyContains = false;

    private boolean tradeAddress = false;

    private boolean orderSupplier = false;

    private boolean tradeSalesman = false;

    private boolean orderCaigou = false;

    /**
     * 查询订单表时强制引用索引
     */
    private String tradeIndex;

    /**
     * 查询订单表统计时强制引用索引
     */
    private String tradeCountIndex;

    /**
     * 是否允许查询订单表统计时强制引用索引
     */
    private boolean allowTradeCountIndex = true;

    public boolean isAllowTradeCountIndex() {
        return allowTradeCountIndex;
    }

    public void setAllowTradeCountIndex(boolean allowTradeCountIndex) {
        this.allowTradeCountIndex = allowTradeCountIndex;
    }

    public String getTradeCountIndex() {
        return tradeCountIndex;
    }

    public void setTradeCountIndex(String tradeCountIndex) {
        this.tradeCountIndex = tradeCountIndex;
    }

    private Map<String,String> paramBeforeWhere = new HashMap<>(4);

    public boolean isAllowedPgl() {
        return allowedPgl;
    }

    public void setAllowedPgl(boolean allowedPgl) {
        this.allowedPgl = allowedPgl;
    }

    public Sort getSortTrade() {
        return sortTrade;
    }

    public void setSortTrade(Sort sortTrade) {
        this.sortTrade = sortTrade;
    }

    public boolean isContainHashKey() {
        return containHashKey;
    }

    public void setContainHashKey(boolean containHashKey) {
        this.containHashKey = containHashKey;
    }
    /**
     * 唯一码表
     */
    private String uniqueCodeTable;

    /**
     * 终止查询标记
     */
    private boolean stopQuery = false;

    private String stopReason = null;

    public boolean isStopQuery() {
        return stopQuery;
    }

    public void setStopQuery(boolean stopQuery) {
        this.stopQuery = stopQuery;
    }

    public String getStopReason() {
        return stopReason;
    }

    public void setStopReason(String stopReason) {
        this.stopReason = stopReason;
    }

    public StringBuilder getQ() {
        return q;
    }

    public Query setQ(StringBuilder q) {
        this.q = q;
        return this;
    }

    public String getSort() {
        return sort;
    }

    public Query setSort(String sort) {
        this.sort = sort;
        return this;
    }

    public String getCountField() {
        return countField;
    }

    public void setCountField(String countField) {
        this.countField = countField;
    }

    public String getGroupByPart() {
        return groupByPart;
    }

    public void setGroupByPart(String groupByPart) {
        this.groupByPart = groupByPart;
    }

    public boolean isNeedSaveSql() {
        return needSaveSql;
    }

    public void setNeedSaveSql(boolean needSaveSql) {
        this.needSaveSql = needSaveSql;
    }

    public String getWholeSql() {
        return wholeSql;
    }

    public void setWholeSql(String wholeSql) {
        this.wholeSql = wholeSql;
    }

    public List<Object> getArgs() {
        return args;
    }

    public Query setArgs(List<Object> args) {
        this.args = args;
        return this;
    }

    public String getTradeTable() {
        return tradeTable;
    }

    public void setTradeTable(String tradeTable) {
        this.tradeTable = tradeTable;
    }

    public String getOrderTable() {
        return orderTable;
    }

    public void setOrderTable(String orderTable) {
        this.orderTable = orderTable;
    }

    public String getTradeExtTable() {
        return tradeExtTable;
    }

    public void setTradeExtTable(String tradeExtTable) {
        this.tradeExtTable = tradeExtTable;
    }

    public String getOrderExtTable() {
        return orderExtTable;
    }

    public void setOrderExtTable(String orderExtTable) {
        this.orderExtTable = orderExtTable;
    }

    public String getUniqueCodeTable() {
        return uniqueCodeTable;
    }

    public void setUniqueCodeTable(String uniqueCodeTable) {
        this.uniqueCodeTable = uniqueCodeTable;
    }

    public Map<String, String> getParamBeforeWhere() {
        return paramBeforeWhere;
    }

    public void setParamBeforeWhere(Map<String, String> paramBeforeWhere) {
        this.paramBeforeWhere = paramBeforeWhere;
    }

    public boolean isOnlyContains() {
        return onlyContains;
    }

    public void setOnlyContains(boolean onlyContains) {
        this.onlyContains = onlyContains;
    }

    public boolean isTradeAddress() {
        return tradeAddress;
    }

    public void setTradeAddress(boolean tradeAddress) {
        this.tradeAddress = tradeAddress;
    }

    public boolean isOrderSupplier() {
        return orderSupplier;
    }

    public void setOrderSupplier(boolean orderSupplier) {
        this.orderSupplier = orderSupplier;
    }

    public boolean isTradeSalesman() {
        return tradeSalesman;
    }

    public void setTradeSalesman(boolean tradeSalesman) {
        this.tradeSalesman = tradeSalesman;
    }

    public boolean isOrderCaigou() {
        return orderCaigou;
    }

    public void setOrderCaigou(boolean orderCaigou) {
        this.orderCaigou = orderCaigou;
    }

    public String getTradeIndex() {
        return tradeIndex;
    }

    public void setTradeIndex(String tradeIndex) {
        this.tradeIndex = tradeIndex;
    }

    public <T> Query append(T v){
        getQ().append(v);
        return this;
    }

    public <T> Query append(T v, boolean append) {
        return append ? append(v) : this;
    }

    public <T> Query add(T value) {
        if (value instanceof Collection) {
            Collection<?> list = (Collection<?>) value;
            getArgs().addAll(list);
        } else if (value != null && value.getClass().isArray()) {
            int length = Array.getLength(value);
            for (int i = 0; i < length; i++) {
                getArgs().add(Array.get(value, i));
            }
        } else {
            getArgs().add(value);
        }
        return this;
    }

    public <T> Query add(T value, boolean add) {
        return add ? add(value) : this;
    }

    public Query and() {
        return conjunct(" AND ", getQ().length() > 0);
    }

    public Query or(){
        return conjunct(" OR ", getQ().length() > 0);
    }

    public <T> Query conjunct(T w, boolean append) {
        if (append) {
            getQ().append(w);
        }
        return this;
    }

    public static StringBuilder and(StringBuilder q) {
        return conjunct(q, " AND ", q.length() > 0);
    }

    public static StringBuilder or(StringBuilder q){
        return conjunct(q, " OR ", q.length() > 0);
    }

    public static <T> StringBuilder conjunct(StringBuilder q, T w, boolean append){
        if (append) {
            q.append(w);
        }
        return q;
    }

    public StringBuilder buildSql(String query) {
        StringBuilder sql = new StringBuilder(query);
        if (getQ().length() > 0) {
            sql.append(" WHERE ").append(getQ());
        }
        if (getSort() != null) {
            sql.append(" ORDER BY ").append(getSort());
        }
        return sql;
    }

    @Override
    public String toString() {
        StringBuilder buf = new StringBuilder();
        int i = 0, fromIndex = 0, index;
        while ((index = getQ().indexOf("?", fromIndex)) > -1 && i < getArgs().size()) {
            buf.append(getQ().substring(fromIndex, index));
            Object arg = getArgs().get(i);
            if (arg instanceof String) {
                buf.append("'").append(arg).append("'");
            } else if (arg instanceof Date) {
                buf.append("'").append(DateUtils.datetime2Str((Date) arg)).append("'");
            } else {
                buf.append(arg);
            }
            fromIndex = index + 1;
            i++;
        }
        if (i == getArgs().size()) {
            buf.append(getQ().substring(fromIndex));
            if (getSort() != null) {
                buf.append(" ORDER BY ").append(getSort());
            }
            return buf.toString();
        } else {
            return "where: " + getQ() + "\nargs: " + getArgs();
        }
    }

    public String getTradeExceptTable() {
        return tradeExceptTable;
    }

    public void setTradeExceptTable(String tradeExceptTable) {
        this.tradeExceptTable = tradeExceptTable;
    }

    @Override
    public Query clone() throws CloneNotSupportedException {
        Query query = (Query)super.clone();
        query.setQ(new StringBuilder(this.q.toString()));
        // args进行复制
        List<Object> args = new ArrayList<>(this.args.size());
        args.addAll(this.args);
        query.setArgs(args);
        return query;
    }

    public boolean allowPgl(){
        return !isContainHashKey() && isAllowedPgl() && "trade".equalsIgnoreCase(getTradeTable());
    }
}

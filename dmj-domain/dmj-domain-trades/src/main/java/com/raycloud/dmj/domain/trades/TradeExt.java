package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.shopee.*;
import com.raycloud.dmj.domain.shopee.Package;
import com.raycloud.dmj.domain.trade.address.TradeAddress;
import com.raycloud.dmj.domain.trades.utils.TradeExtUtils;
import com.raycloud.erp.db.model.*;

import java.util.*;

/**
 * 交易扩展表
 */
@Table(name = "trade_ext")
public class TradeExt extends Model {
    private static final long serialVersionUID = -5969144406650840882L;
    private Long sid;

    private Long companyId;

    private Long userId;

    private String tid;

    private Long mallMaskId;

    /**
     * pdd:代发店铺名称
     * shopee:首公里追踪号
     */
    private String mallMaskName;

    private Integer status;

    /**
     * @deprecated
     * @see TradeAddress#receiverNameIndex
     */
    private String receiverNameIndex;
    /**
     * @deprecated
     * @see TradeAddress#receiverNameIndex
     */
    private String receiverMobileIndex;

    //暂时不使用
    private String receiverPhoneIndex;
    /**
     * @deprecated
     * @see TradeAddress#receiverAddressIndex
     */
    private String receiverAddressIndex;
    /**
     * @deprecated
     * @see TradeAddress#desensitizationReceiverName
     */
    private String desensitizationReceiverName;
    /**
     * @deprecated
     * @see TradeAddress#desensitizationReceiverMobile
     */
    private String desensitizationReceiverMobile;
    /**
     * @deprecated
     * @see TradeAddress#desensitizationReceiverAddress
     */
    private String desensitizationReceiverAddress;

    /**
     * 指定物流
     */
    private String logisticsCode;

    /**
     * 指定包材
     */
    private String wrapperDescription;

    /**
     * shopee:物流渠道
     */
    private String shippingCarrier;

    /**
     * shopee:中转仓
     */
    private String transitWarehouseId;

    /**
     * shopee:物流id
     */
    private Long logisticId;

    /**
     * shopee:币种
     */
    private String currency;

    /**
     * shopee:仓库地址信息
     */
    private String warehouseAddress;

    /**
     * shopee: 拆分的id
     */
    private String forderId;

    /**
     * 拆分包裹Id
     */
    private String packageNumber;

    private List<Forder> forders;

    private List<Package> packages;

    private Date created;

    /**
     * 自动解挂时间
     */
    private Integer autoUnHook;

    private Long combineParcelId;

    /**
     * 仓库信息
     */
    private String storeName;

    /**
     * 自动解挂的时间点
     */
    private Date autoUnHookTime;

    /**
     * 订单唯一码，目前是个抖音质检订单用的
     */
    private String bicUniqueCode;
    /**
     * 送检方式
     * 0：自行送货
     * 1：邮寄
     * 2：不需要送货到质检机构
     */
    private Integer deliveryType;

    /**
     * 出仓方式
     * 1：一单一包裹
     * 2：一单多包裹
     * 3：混订单
     */
    private Integer shipType;

    /**
     * PDD
     * 集运类型 0代表香港集运，1代表新疆集运
     */
    private Integer consolidateType;

    private String shopeeOutSid;

    /**
     * 常态合作码
     */
    private String cooperationNo;

    /**
     * 送货仓库
     */
    private String sellSite;

    /**
     * 红包优惠金额
     */
    private String redpackFee;
    /**
     * 平台红包优惠金额
     */
    private String redpackPlatformFee;
    /**
     * 达人红包优惠金额
     */
    private String redpackTalentFee;

    /**
     * 承诺送达时间
     */
    private Date promiseDeliveryTime;
    /**
     * 扩充字段
     * 后续需要添加的字段都可以放在这个字段里，json格式
     */
    private String extraFields;

    /**
     * 将map中的数据以key1,value1,key2,value2...的顺序放入到list中，方便sql拼接
     */
    private List<Object> extraFieldsList;

    /**
     * 扩充字段缓存
     * 减少频繁json转map
     */
    private Map<String, Object> extraFieldsMap;

    /**
     * 天猫物流升级
     */
    private String tmallAsdpBizType;

    /**
     * 天猫物流服务 编码字段  @see com.raycloud.dmj.domain.enums.TmallAsdpAdsTypeEnum
     */
    private String tmallAsdpAds;

    /**
     * 跨境物流订单完善信息
     */
    private String logisticsRecords;
    /**
     * 拼多多暂停发货标记 1:有暂停发货标记 0:无标记
     */
    private Integer shipHold;

    /**
     * 需要移除的key集合
     */
    private List<String> removeExtraFieldsList;

    public List<String> getRemoveExtraFieldsList() {
        return removeExtraFieldsList;
    }

    public void setRemoveExtraFieldsList(List<String> removeExtraFieldsList) {
        this.removeExtraFieldsList = removeExtraFieldsList;
    }

    public Integer getShipHold() {
        return shipHold;
    }

    public void setShipHold(Integer shipHold) {
        this.shipHold = shipHold;
    }
    public String getRedpackFee() {
        return redpackFee;
    }

    public void setRedpackFee(String redpackFee) {
        this.redpackFee = redpackFee;
    }

    public String getRedpackPlatformFee() {
        return redpackPlatformFee;
    }

    public void setRedpackPlatformFee(String redpackPlatformFee) {
        this.redpackPlatformFee = redpackPlatformFee;
    }

    public String getRedpackTalentFee() {
        return redpackTalentFee;
    }

    public void setRedpackTalentFee(String redpackTalentFee) {
        this.redpackTalentFee = redpackTalentFee;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getMallMaskId() {
        return mallMaskId;
    }

    public void setMallMaskId(Long mallMaskId) {
        this.mallMaskId = mallMaskId;
    }

    public String getMallMaskName() {
        return mallMaskName;
    }

    public void setMallMaskName(String mallMaskName) {
        this.mallMaskName = mallMaskName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getReceiverNameIndex() {
        return receiverNameIndex;
    }

    public void setReceiverNameIndex(String receiverNameIndex) {
        this.receiverNameIndex = receiverNameIndex;
    }

    public void setReceiverMobileIndex(String receiverMobileIndex) {
        this.receiverMobileIndex = receiverMobileIndex;
    }

    public String getReceiverAddressIndex() {
        return receiverAddressIndex;
    }

    public void setReceiverAddressIndex(String receiverAddressIndex) {
        this.receiverAddressIndex = receiverAddressIndex;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReceiverMobileIndex() {
        return receiverMobileIndex;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public String getWrapperDescription() {
        return wrapperDescription;
    }

    public void setWrapperDescription(String wrapperDescription) {
        this.wrapperDescription = wrapperDescription;
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public Integer getConsolidateType() {
        return consolidateType;
    }

    public void setConsolidateType(Integer consolidateType) {
        this.consolidateType = consolidateType;
    }

    public Integer getAutoUnHook() {
        return autoUnHook;
    }

    public void setAutoUnHook(Integer autoUnHook) {
        this.autoUnHook = autoUnHook;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public String getTransitWarehouseId() {
        return transitWarehouseId;
    }

    public void setTransitWarehouseId(String transitWarehouseId) {
        this.transitWarehouseId = transitWarehouseId;
    }

    public Long getLogisticId() {
        return logisticId;
    }

    public void setLogisticId(Long logisticId) {
        this.logisticId = logisticId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Date getAutoUnHookTime() {
        return autoUnHookTime;
    }

    public void setAutoUnHookTime(Date autoUnHookTime) {
        this.autoUnHookTime = autoUnHookTime;
    }

    public String getBicUniqueCode() {
        return bicUniqueCode;
    }

    public void setBicUniqueCode(String bicUniqueCode) {
        this.bicUniqueCode = bicUniqueCode;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getShipType() {
        return shipType;
    }

    public void setShipType(Integer shipType) {
        this.shipType = shipType;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress;
    }

    public List<Forder> getForders() {
        return forders;
    }

    public void setForders(List<Forder> forders) {
        this.forders = forders;
    }

    public String getForderId() {
        return forderId;
    }

    public void setForderId(String forderId) {
        this.forderId = forderId;
    }

    public String getPackageNumber() {
        return packageNumber;
    }

    public void setPackageNumber(String packageNumber) {
        this.packageNumber = packageNumber;
    }

    public List<Package> getPackages() {
        return packages;
    }

    public void setPackages(List<Package> packages) {
        this.packages = packages;
    }

    public String getShopeeOutSid() {
        return shopeeOutSid;
    }

    public void setShopeeOutSid(String shopeeOutSid) {
        this.shopeeOutSid = shopeeOutSid;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public String getSellSite() {
        return sellSite;
    }

    public void setSellSite(String sellSite) {
        this.sellSite = sellSite;
    }

    public String getTmallAsdpBizType() {
        return tmallAsdpBizType;
    }

    public void setTmallAsdpBizType(String tmallAsdpBizType) {
        this.tmallAsdpBizType = tmallAsdpBizType;
    }

    public String getTmallAsdpAds() {
        return tmallAsdpAds;
    }

    public void setTmallAsdpAds(String tmallAsdpAds) {
        this.tmallAsdpAds = tmallAsdpAds;
    }


    public String getDesensitizationReceiverName() {
        return desensitizationReceiverName;
    }

    public void setDesensitizationReceiverName(String desensitizationReceiverName) {
        this.desensitizationReceiverName = desensitizationReceiverName;
    }

    public String getDesensitizationReceiverMobile() {
        return desensitizationReceiverMobile;
    }

    public void setDesensitizationReceiverMobile(String desensitizationReceiverMobile) {
        this.desensitizationReceiverMobile = desensitizationReceiverMobile;
    }

    public String getDesensitizationReceiverAddress() {
        return desensitizationReceiverAddress;
    }

    public void setDesensitizationReceiverAddress(String desensitizationReceiverAddress) {
        this.desensitizationReceiverAddress = desensitizationReceiverAddress;
    }

    public String getReceiverPhoneIndex() {
        return receiverPhoneIndex;
    }

    public void setReceiverPhoneIndex(String receiverPhoneIndex) {
        this.receiverPhoneIndex = receiverPhoneIndex;
    }

    public String getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(String extraFields) {
        this.extraFields = extraFields;
    }

    public List<Object> getExtraFieldsList() {
        return extraFieldsList;
    }

    public void setExtraFieldsList(List<Object> extraFieldsList) {
        this.extraFieldsList = extraFieldsList;
    }

    public Map<String, Object> getExtraFieldsMap() {
        return extraFieldsMap;
    }

    public void setExtraFieldsMap(Map<String, Object> extraFieldsMap) {
        this.extraFieldsMap = extraFieldsMap;
    }

    public String getLogisticsRecords() {
        return logisticsRecords;
    }

    public void setLogisticsRecords(String logisticsRecords) {
        this.logisticsRecords = logisticsRecords;
    }

    /**
     * 获取额外属性
     * @return
     */
    public Object get(String key){
        if (extraFieldsMap == null){
            extraFieldsMap = TradeExtUtils.parseExtraFields(this);
        }
        return extraFieldsMap.get(key);
    }

    public void set(String key, Object value) {
        if (extraFieldsMap == null){
            extraFieldsMap = TradeExtUtils.parseExtraFields(this);
        }
        extraFieldsMap.put(key, value);
        this.extraFields = JSONObject.toJSONString(extraFieldsMap);
    }

    /**
     * 智能计算订单单品数量
     */
    private Integer itemQuantity;

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public Date getPromiseDeliveryTime() {
        return promiseDeliveryTime;
    }

    public void setPromiseDeliveryTime(Date promiseDeliveryTime) {
        this.promiseDeliveryTime = promiseDeliveryTime;
    }
}
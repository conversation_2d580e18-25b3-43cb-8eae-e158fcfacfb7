package com.raycloud.dmj.domain.trades;

import com.google.common.collect.Sets;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.channel.OrderChannel;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.IExpressTemplateBase;
import com.raycloud.dmj.domain.pt.express.WaybillUsingRule;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.label.TradeLabel;
import com.raycloud.dmj.domain.trade.merge.TradeMerge;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.type.TradeType;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.vo.WaveAssembleInfo;
import com.raycloud.dmj.domain.user.Address;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.erp.db.model.Column;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Types;
import java.util.*;

/**
 * ERP的订单领域，所有平台的业务领域
 *
 * <AUTHOR>
 */
@SuppressWarnings("JavadocReference")
//@EqualsAndHashCode(callSuper=true)
public class Trade extends TradeAggregation {

    public static final Integer SUIT_TO_SINGLE_VALUE = 1;

    private static final long serialVersionUID = -210827514234904523L;

    //========================= 系统状态枚举 =======================//
    /**
     * 待付款
     */
    public static final String SYS_STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";

    /**
     * 待审核
     */
    public static final String SYS_STATUS_WAIT_AUDIT = "WAIT_AUDIT";

    /**
     * 等待财审
     */
    public static final String SYS_STATUS_WAIT_FINANCE_AUDIT = "WAIT_FINANCE_AUDIT";

    /**
     * 等待人工审核，一般由系统的自动审核触发，如果订单在自动审核的过程中被拒绝了，那么状态将转变为人工审核
     */
    public static final String SYS_STATUS_WAIT_MANUAL_AUDIT = "WAIT_MANUAL_AUDIT";

    /**
     * 审核完成
     */
    public static final String SYS_STATUS_FINISHED_AUDIT = "FINISHED_AUDIT";

    /**
     * 待打印快递单，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_EXPRESS_PRINT = "WAIT_EXPRESS_PRINT";

    /**
     * 待打印发货单，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_DELIVERY_PRINT = "WAIT_DELIVERY_PRINT";

    /**
     * 待打包，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_PACKAGE = "WAIT_PACKAGE";

    /**
     * 待称重，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_WEIGHT = "WAIT_WEIGHT";

    /**
     * 待发货，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_SEND_GOODS = "WAIT_SEND_GOODS";

    /**
     * 待供销商发货，这个状态和FINISHED_AUDIT一样，这个状态在视图上进行转变
     */
    public static final String SYS_STATUS_WAIT_DEST_SEND_GOODS = "WAIT_DEST_SEND_GOODS";

    /**
     * 卖家已发货
     */
    public static final String SYS_STATUS_SELLER_SEND_GOODS = "SELLER_SEND_GOODS";

    /**
     * 交易完成
     */
    public static final String SYS_STATUS_FINISHED = "FINISHED";

    /**
     * 交易关闭
     */
    public static final String SYS_STATUS_CLOSED = "CLOSED";

    /**
     * 交易作废
     */
    public static final String SYS_STATUS_CANCEL = "CANCEL";

    //========================= 库存状态枚举 =======================//
    /**
     * 库存空值，未进行过匹配操作
     */
    public static final String STOCK_STATUS_EMPTY = StockStatusEnum.STOCK_STATUS_EMPTY.stockStatus;

    /**
     * 库存正常
     */
    public static final String STOCK_STATUS_NORMAL = StockStatusEnum.STOCK_STATUS_NORMAL.stockStatus;

    /**
     * 商品未分配
     */
    public static final String STOCK_STATUS_UNALLOCATED = StockStatusEnum.STOCK_STATUS_UNALLOCATED.stockStatus;

    /**
     * 库存不足
     */
    public static final String STOCK_STATUS_INSUFFICIENT = StockStatusEnum.STOCK_STATUS_INSUFFICIENT.stockStatus;

    /**
     * 商品对应关系修改
     */
    public static final String STOCK_STATUS_RELATION_MODIFIED = StockStatusEnum.STOCK_STATUS_RELATION_MODIFIED.stockStatus;

    /**
     * 库存不足
     */
    public static final String STOCK_STATUS_EXCEP = StockStatusEnum.STOCK_STATUS_EXCEP.stockStatus;

    /**
     * 店铺用户编号
     */
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    /**
     * 平台的卖家账户
     */
    @Column(name = "taobao_id", type = Types.BIGINT)
    private Long taobaoId;

    /**
     * 订单来自哪个平台，例如 tb(淘宝或者天猫平台),jd(京东平台),sys(系统手工订单)
     */
    @Column(name = "source", type = Types.VARCHAR)
    private String source;

    private String oldSource;

    /**
     * 订单所属店铺的source
     */
    private String userSource;

    /**
     * 拆分后原始source，拆分订单的时候使用到
     */
    private String splitSource;

    /**
     * 订单短号
     */
    private Long shortId;

    /**
     * 需要解析tid的时候存解析前的tid，不做持久化
     */
    private String oldTid;

    /**
     * 扫描手动拆单个数时 ,做记录
     */
    @Column(name = "check_manual_merge_count", type = Types.INTEGER)
    private Integer checkManualMergeCount;

    /**
     * 订单类型
     */
    @Column(name = "type", type = Types.VARCHAR)
    private String type;

    private Integer tradeType;

    private Map<String, TradeType> tradeTypeMap;

    /**
     * 订单的平台状态
     */
    @Column(name = "status", type = Types.VARCHAR)
    private String status;

    /**
     * 各平台状态映射后的统一状态
     */
    @Column(name = "unified_status", type = Types.VARCHAR)
    private String unifiedStatus;

    private String jdPaymentExcludePost;

    public String getJdPaymentExcludePost() {
        return jdPaymentExcludePost;
    }

    public void setJdPaymentExcludePost(String jdPaymentExcludePost) {
        this.jdPaymentExcludePost = jdPaymentExcludePost;
    }

    /**
     * 平台之前的订单状态，不持久，一般用于订单同步用
     */
    private String oldStatus;

    /**
     * 订单的系统状态，平台订单的系统状态由所有平台子订单综合计算得出
     */
    @Column(name = "sys_status", type = Types.VARCHAR)
    private String sysStatus;

    /**
     * 订单系统状态的中文说明
     */
    private String chSysStatus;

    /**
     * 更新之前的系统状态，不做持久化，一般用于订单同步
     */
    private String oldSysStatus;

    /**
     * 库存状态
     */
    @Column(name = "stock_status", type = Types.VARCHAR)
    private String stockStatus;

    /**
     * 做库存更新之前的库存状态，不做持久化
     */
    private String oldStockStatus;

    /**
     * 当前有效的商品数,部分发货的订单只包含尚未发货的商品数（非持久化字段）
     */
    private Integer validItemNum;
    /**
     * 订单缺货数量（所有子订单缺货数量之和）
     */
    @Column(name = "insufficient_num", type = Types.INTEGER)
    private Integer insufficientNum;

    /**
     * 缺货比例
     */
    private Double insufficientRate;

    /**
     * 付款时间
     */
    @Column(name = "pay_time", type = Types.TIMESTAMP)
    private Date payTime;

    /**
     * 发货时间
     */
    @Column(name = "consign_time", type = Types.TIMESTAMP)
    private Date consignTime;

    /**
     * 平台发货时间
     */
    @Column(name = "pt_consign_time", type = Types.TIMESTAMP)
    private Date ptConsignTime;

    /**
     * 发货类型，非持久化字段
     */
    private SendType consignType;

    /**
     * 是否在系统发货 1 是，0 否,2其他ERP发货
     */
    @Column(name = "sys_consigned", type = Types.TINYINT)
    private Integer sysConsigned;

    /**
     * 完结时间
     */
    @Column(name = "end_time", type = Types.TIMESTAMP)
    private Date endTime;

    /**
     * 数据库最后更新时间
     */
    @Column(name = "upd_time", type = Types.TIMESTAMP)
    private Date updTime;

    /**
     * 运单号
     */
    private String outSid;
    /**
     * 老的运单号
     */
    private String oldOutSid;

    /**
     * 快递是否可送达，0表示不可送达，1表示可送达，默认为1，需要持久化
     */
    @Column(name = "can_delivered", type = Types.TINYINT)
    private Integer canDelivered;

    /**
     * 是否可确认发货或者取消发货，0表示不能确认或者取消，1表示可用。这个字段一般在使用了
     * 在线发货功能的时候，使用到。在线发货如果没有填写运单号，应该要将这个字段设置为1
     */
    @Column(name = "can_confirm_send", type = Types.TINYINT)
    private Integer canConfirmSend;

    /**
     * 订单使用的模板是否为手工填写单号模板
     */
    private Integer isManulfill;

    /**
     * 快递模板编号
     */
    @Column(name = "template_id", type = Types.BIGINT)
    private Long templateId;

    /**
     * 快递公司id
     */
    @Column(name = "logistics_company_id", type = Types.BIGINT)
    private Long logisticsCompanyId;

    /**
     * 快递公司名称，不做持久化处理
     */
    private String logisticsCompanyName;

    /**
     * 快递模版类型，0表示普通快递，1表示电子面单快递，默认为0
     */
    @Column(name = "template_type", type = Types.TINYINT)
    private Integer templateType;

    /**
     * 模板名称,不做持久化处理
     */
    private String templateName;

    /**
     * 输入参数的模板名称,不做持久化处理
     */
    private String originTemplateName;

    /**
     * 备选快递模板，不做持久化
     */
    private List<IExpressTemplateBase> backTemplateList;

    /**
     * 标签名称，不做持久化处理
     */
    private List<String> tagName;
    /**
     * 标签列表
     */
    private List<TradeTag> tags;

    /**
     * 快递公司的编号，不做持久化处理
     */
    private Long expressCompanyId;

    /**
     * 快递公司的编码，不做持久化处理
     */
    private String expressCode;

    /**
     * 快递公司的名称，不做持久化处理
     */
    private String expressName;

    /**
     * 订单转化类型 0表示正常订单，1表示分销系统 2表示平台分销订单
     */
    @Column(name = "convert_type", type = Types.INTEGER)
    private Integer convertType;

    /**
     * 订单来源于哪里 分销系统里记为分销商id（companyId）
     */
    @Column(name = "source_id", type = Types.BIGINT)
    private Long sourceId;

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    @Column(name = "dest_id", type = Types.BIGINT)
    private Long destId;

    /**
     * 订单来源于哪里 分销系统里记为分销商name（不持久化）
     */
    private String sourceName;

    /**
     * 订单归属于哪里 分销系统里记为供销商nname（不持久化）
     */
    private String destName;

    /**
     * 供销商的简称
     */
    private String destAliasName;

    /**
     * 需要反审核的分销/供销订单
     */
    private boolean fxNeedUnaudit;
    /**
     * 一单多包单号数量（不持久化）
     */
    private Integer waybillCount;

    public Integer getWaybillCount() {
        return waybillCount;
    }

    public void setWaybillCount(Integer waybillCount) {
        this.waybillCount = waybillCount;
    }

    public String getSourceAliasName() {
        return sourceAliasName;
    }

    public void setSourceAliasName(String sourceAliasName) {
        this.sourceAliasName = sourceAliasName;
    }

    public String getDestAliasName() {
        return destAliasName;
    }

    public void setDestAliasName(String destAliasName) {
        this.destAliasName = destAliasName;
    }

    /**
     * 分销商简称
     */
    private String sourceAliasName;

    /**
     * 业务操作标志--不落库。
     *
     */
    private Set<OpVEnum> opVEnumSet;

    /**
     * 业务操作标志--不落库。 值是String给前端用，暂时不考虑json方式传opVEnumSet。实现后可以去掉此字段。
     *
     */
    private Set<String> opVEnumCodeSet;


    public Set<String> getOpVEnumCodeSet() {
        return opVEnumCodeSet;
    }

    public void setOpVEnumCodeSet(Set<String> opVEnumCodeSet) {
        this.opVEnumCodeSet = opVEnumCodeSet;
        if(CollectionUtils.isNotEmpty(opVEnumCodeSet)){
            if(CollectionUtils.isEmpty(opVEnumSet)){
                opVEnumSet = Sets.newHashSetWithExpectedSize(4);
            }
            opVEnumCodeSet.forEach(v->{
                opVEnumSet.add(OpVEnum.valueOfCode(v));
            });
        }
    }

    public Set<OpVEnum> getOpVEnumSet() {
        return opVEnumSet;
    }

    public void setOpVEnumSet(Set<OpVEnum> opVEnumSet) {
        this.opVEnumSet = opVEnumSet;
    }

    public void addOpV(OpVEnum opVEnum) {
        if(opVEnum ==null){
            return;
        }
        if(CollectionUtils.isEmpty(opVEnumSet)){
            opVEnumSet = Sets.newHashSetWithExpectedSize(4);
        }
        opVEnumSet.add(opVEnum);
    }

    public void removeOpV(OpVEnum opVEnum) {
        if(CollectionUtils.isEmpty(opVEnumSet) || opVEnum ==null){
           return;
        }
        opVEnumSet.remove(opVEnum);
    }
    /**
     *
     * @return
     */
    public boolean hasOpV(OpVEnum opVEnum) {
        return CollectionUtils.isNotEmpty(opVEnumSet) && opVEnumSet.contains(opVEnum);
    }
    /**
     *
     */
    private String fxSysStatus;

    public String getOldTid() {
        return oldTid;
    }

    public void setOldTid(String oldTid) {
        this.oldTid = oldTid;
    }

    public boolean isFxCancelConsign() {
        return fxCancelConsign;
    }

    public void setFxCancelConsign(boolean fxCancelConsign) {
        this.fxCancelConsign = fxCancelConsign;
    }

    /**
     * 分销订单是否撤销发货
     */
    private boolean fxCancelConsign;

    /**
     * 是否是分销订单重推过来的（供销订单用）
     */
    private boolean isFxReAudit;

    /**
     * 是否分销强制推单到供销商
     */
    private boolean ifFxForcePushTrade;

    /**
     * 分销商支付方式，目前仅用于档口订单excel导入
     */
    private String fxPayType;

    /**
     * 分销原始平台单号
     */
    private String fxPlatformTid;

    /**
     * 处方药未审核状态（不持久化）
     */
    private String rxAuditStatus;
    /**
     * 是否疫情登记的订单。0=未登记，1=已登记（不持久化）
     */
    private String drugRegister;
    /**
     * 用来存放平台备注，不做持久化
     */
    private String platformSellerMemo;
    /**
     * 用来存放平台旗帜，不做持久化
     */
    private Long platformFlag;

    /**
     * 系统商品类目名,以逗号分隔,所有order里面的sellerCatName汇总
     */
    private String sellerCatNameAll;

    /**
     * 是否已经物理删除，只有批量打印记录详情查询接口会用到
     */
    private boolean isDeleted = false;

    /**
     * 订单所属平台,不做持久化
     */
    private String subSourceName;

    public String getSubSourceName() {
        return subSourceName;
    }

    public void setSubSourceName(String subSourceName) {
        this.subSourceName = subSourceName;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

    public String getSellerCatNameAll() {
        return sellerCatNameAll;
    }

    public void setSellerCatNameAll(String sellerCatNameAll) {
        this.sellerCatNameAll = sellerCatNameAll;
    }

    public String getDrugRegister() {
        return drugRegister;
    }

    public void setDrugRegister(String drugRegister) {
        this.drugRegister = drugRegister;
    }

    public String getRxAuditStatus() {
        return rxAuditStatus;
    }

    public void setRxAuditStatus(String rxAuditStatus) {
        this.rxAuditStatus = rxAuditStatus;
    }

    public String getFxPayType() {
        return fxPayType;
    }

    public void setFxPayType(String fxPayType) {
        this.fxPayType = fxPayType;
    }

    public String getFxPlatformTid() {
        return fxPlatformTid;
    }

    public void setFxPlatformTid(String fxPlatformTid) {
        this.fxPlatformTid = fxPlatformTid;
    }

    public boolean isFxReAudit() {
        return isFxReAudit;
    }

    public void setIsFxReAudit(boolean isFxReAudit) {
        this.isFxReAudit = isFxReAudit;
    }

    public String getFxSysStatus() {
        return fxSysStatus;
    }

    public void setFxSysStatus(String fxSysStatus) {
        this.fxSysStatus = fxSysStatus;
    }

    public boolean isFxNeedUnaudit() {
        return fxNeedUnaudit;
    }

    public void setFxNeedUnaudit(boolean fxNeedUnaudit) {
        this.fxNeedUnaudit = fxNeedUnaudit;
    }

    /**
     * 订单属于soure or dest ,0表示正常订单，1表示source，2表示dest
     */
    @Column(name = "belong_type", type = Types.INTEGER)
    private Integer belongType;

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getDestName() {
        return destName;
    }

    public void setDestName(String destName) {
        this.destName = destName;
    }

    public Integer getConvertType() {
        return convertType;
    }

    public void setConvertType(Integer convertType) {
        this.convertType = convertType;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getDestId() {
        return destId;
    }

    public void setDestId(Long destId) {
        this.destId = destId;
    }

    public Integer getBelongType() {
        return belongType;
    }

    public void setBelongType(Integer belongType) {
        this.belongType = belongType;
    }

    /**
     * 掩码操作包装起来它不香吗？它不香吗？它不香吗？
     * 《面向对象》的原则： 抽象、封装、继承、多态
     *
     * @param bits
     * @return
     */
    public boolean hasBitExcep(long bits) {
        return (getExcep() & bits) == bits;
    }

    /**
     * 快递单打印时间
     */
    @Column(name = "express_print_time", type = Types.TIMESTAMP)
    private Date expressPrintTime;

    /**
     * 发货单打印时间
     */
    @Column(name = "deliver_print_time", type = Types.TIMESTAMP)
    private Date deliverPrintTime;

    /**
     * 配货单打印时间
     */
    @Column(name = "assembly_print_time", type = Types.TIMESTAMP)
    private Date assemblyPrintTime;
    /**
     * 首次订单同步时匹配到的仓库规则id,非持久化字段
     */
    private Long warehouseMatchRuleId;
    /**
     * 仓库编号
     */
    @Column(name = "warehouse_id", type = Types.BIGINT)
    private Long warehouseId;
    /**
     * 换仓库之前的仓库ID,不做持久化
     */
    private Long oldWarehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 审核时间
     */
    @Column(name = "audit_time", type = Types.TIMESTAMP)
    private Date auditTime;

    /**
     * 自动审核字段： 0：未审核 1：自动审核 2：人工审核 3：反审核
     */
    @Column(name = "is_auto_audit", type = Types.TINYINT)
    private Integer isAutoAudit;

    /**
     * 同运单号下交易关闭订单
     */
    private List<Long> closeSids;

    /**
     * 分销订单id集合
     */
    private List<Long> fxSids;

    private Map<Long, TradeResult> errors;

    /**
     * 平台订单上的买家地区
     */
    @Deprecated
    private String buyerArea;

    /**
     * 平台订单上的买家邮箱
     */
    @Deprecated
    private String buyerEmail;

    /**
     * 是否有买家留言
     */
    private Boolean hasBuyerMessage;

    /**
     * 平台订单上的买家留言
     */
    @Column(name = "buyer_message", type = Types.VARCHAR)
    private String buyerMessage;

    /**
     * 买家支付宝交易号
     */
    @Deprecated
    private String buyerAlipayNo;

    /**
     * 平台上的卖家旗帜
     */
    @Column(name = "seller_flag", type = Types.TINYINT)
    private Long sellerFlag;

    /**
     * 平台上的卖家备注
     */
    @Column(name = "seller_memo", type = Types.VARCHAR)
    private String sellerMemo;

    /**
     * seller_nick
     */
    @Column(name = "seller_nick", type = Types.VARCHAR)
    private String sellerNick;

    /**
     * 订单系统备注
     */
    @Column(name = "sys_memo", type = Types.VARCHAR)
    private String sysMemo;

    /**
     * 旧订单系统备注 （不做存储只为操作日志记录）
     */
    private String oldSysMemo;

    /**
     * 发票种类（ 1 电子发票 2 纸质发票 ）
     */
    @Column(name = "invoice_kind", type = Types.VARCHAR)
    private String invoiceKind;

    /**
     * 发票抬头
     */
    @Column(name = "invoice_name", type = Types.VARCHAR)
    private String invoiceName;

    /**
     * 发票类型
     */
    @Column(name = "invoice_type", type = Types.VARCHAR)
    private String invoiceType;

    /**
     * 发票备注
     */
    @Column(name = "invoice_remark", type = Types.VARCHAR)
    private String invoiceRemark;

    /**
     * 需要开具发票 1开启，0关闭
     */
    @Column(name = "need_invoice", type = Types.TINYINT)
    private Integer needInvoice;

    /**
     * 发票类型 1普通 2增值
     */
    @Column(name = "invoice_format", type = Types.TINYINT)
    private Integer invoiceFormat;

    /**
     * 买家税号
     */
    @Column(name = "buyer_tax_no", type = Types.VARCHAR)
    private String buyerTaxNo;

    /**
     * 3PL有时效订单标，值true 或者 false
     */
    @Column(name = "three_pl_timing", type = Types.TINYINT)
    private Integer threePlTiming;

    /**
     * 天猫直送，值true 或者 false
     */
    @Column(name = "is_tmall_delivery", type = Types.TINYINT)
    private Integer isTmallDelivery;


    /**
     * 订单来源，例如手机端或者PC端
     */
    @Column(name = "trade_from", type = Types.VARCHAR)
    private String tradeFrom;

    /**
     * 重量
     */
    @Column(name = "weight", type = Types.DOUBLE)
    private Double weight;

    /**
     * 订单净重，由子订单综合计算得出，参照Order#netWeight
     */
    @Column(name = "net_weight", type = Types.DOUBLE)
    private Double netWeight;

    /**
     * 订单体积
     */
    @Column(name = "volume", type = Types.DOUBLE)
    private Double volume;

    /**
     * 订单包裹实际体积
     */
    private String actualVolume;

    /**
     * 订单包裹实际长宽高
     */
    private String actualLengthWidthAndHeight;

    /**
     * 订单下的商品数量
     */
    @Column(name = "item_num", type = Types.INTEGER)
    private Integer itemNum;

    /**
     * 波次计算使用
     */
    private Integer oldItemNum;

    /**
     * 订单下的商品种类数
     */
    @Column(name = "item_kind_num", type = Types.INTEGER)
    private Integer itemKindNum;

    /**
     * 波次计算使用
     */
    private Integer oldItemKindNum;

    /**
     * 商品单品种类, 最小粒度
     */
    private Integer singleItemKindNum;

    /**
     * 是否门店分单 0 否，1 是
     */
    @Column(name = "is_store", type = Types.TINYINT)
    private Integer isStore;

    /**
     * 是否刷单 1 是, 0 否
     */
    @Column(name = "scalping", type = Types.TINYINT)
    private Integer scalping;

    /**
     * 是否为作废订单
     */
    @Column(name = "is_cancel", type = Types.TINYINT)
    private Integer isCancel;

    /**
     * 订单是否打包
     */
    @Column(name = "is_package", type = Types.TINYINT)
    private Integer isPackage;

    /**
     * (旧)订单是否打包
     */
    private Integer oldIsPackage;

    /**
     * 是否称重
     */
    @Column(name = "is_weigh", type = Types.TINYINT)
    private Integer isWeigh;

    /**
     * 是否是加急订单
     */
    @Column(name = "is_urgent", type = Types.TINYINT)
    private Integer isUrgent;

    /**
     * 字段描述
     * 是否为预售订单：0:正常订单，1：系统预售（预售规则没有开启自动识别）；2预售转正常；3：系统预售(预售规则开启了自动识别)
     */
    @Column(name = "is_presell", type = Types.TINYINT)
    private Integer isPresell;

    /**
     * 预售规则ID
     */
    @Column(name = "presell_rule_id", type = Types.BIGINT)
    private Long presellRuleId;

    private PresellRule presellRule;

    /**
     * 平台timeout_action_time
     */
    @Column(name = "timeout_action_time", type = Types.TIMESTAMP)
    private Date timeoutActionTime;

    /**
     * 波次id
     */
    @Column(name = "wave_id", type = Types.BIGINT)
    private Long waveId;

    /**
     * 波次短号
     */
    private Long waveShortId;

    /**
     * 波次填充信息，不再新增新的关于波次的字段
     */
    private WaveAssembleInfo waveAssembleInfo;

    /**
     * 位置号
     */
    private Long positionNo;

    /**
     * 是否发货上传到平台
     */
    @Column(name = "is_upload", type = Types.TINYINT)
    @Deprecated //这个字段即将废弃，请使用 sysTriggerUpload
    private Integer isUpload;

    /**
     * 是否系统触发过上传
     */
    private boolean sysTriggerUpload;

    /**
     * 装箱清单明细json串
     */
    @Column(name = "boxing_list", type = Types.VARCHAR)
    private String boxingList;

    /**
     * 唯品会po单号
     */
    @Column(name = "po_nos", type = Types.VARCHAR)
    private String poNos;

    /**
     * 唯品会拣货单号
     */
    @Column(name = "vip_pick_no", type = Types.VARCHAR)
    private String vipPickNo;

    /**
     * 唯品会出仓单号
     */
    @Column(name = "vip_storage_no", type = Types.VARCHAR)
    private String vipStorageNo;

    /**
     * 新建订单库存不足时是否强制生成
     */
    private boolean force;


    /**
     * 订单打印次数
     */
    @Column(name = "print_count", type = Types.INTEGER)
    private Integer printCount;

    /**
     * 库区类型
     *
     * @see com.raycloud.dmj.domain.wms.enums.StockRegionTypeEnum
     */
    private Integer stockRegionType;


    /**
     * 掩码操作包装起来它不香吗？
     * 《面向对象》的原则： 抽象、封装、继承、多态
     *
     * @param bits
     * @return
     */
    public boolean hasBitItemExcep(int bits) {
        return (getItemExcep() & bits) == bits;
    }

    /**
     * 只更新子订单，这个不做存储，只做逻辑分析
     */
    private boolean isSplitTrade = false;
    /**
     * （星盘）分单渠道信息系
     */
    private List<OrderChannel> channels;

    /**
     * 赠品子订单
     */
    protected List<GiftOrder> giftOrders;

    /**
     * 订单异常信息
     */
    private Set<TradeExcStatus> excStauses;

    /**
     * 再次同步订单时,欲更新的平台订单所对应的系统中已存在的订单,目前只在订单同步时使用
     */
    private Trade origin;

    /**
     * 是否需要重算cost，volume，netWeight,默认需要重算
     */
    private boolean needRecal = true;

    public boolean isNeedRecal() {
        return needRecal;
    }

    public void setNeedRecal(boolean needRecal) {
        this.needRecal = needRecal;
    }

    /**
     * 当只有合单的订单，这个属性才会被创建出来
     */
    private List<MessageMemo> messageMemos;

    /**
     * 合单数据
     */
    private TradeMerge tradeMerge;

    /**
     * 合单类型
     *
     * @see com.raycloud.dmj.domain.enums.TradeMergeEnum
     */
    @Column(name = "merge_type", type = Types.INTEGER)
    private Integer mergeType;

    /**
     * 如果merge_type等于1或2。则对应合单后主单的sid。否则为0
     */
    private Long mergeSid;

    /**
     * 老的merge_sid
     */
    private Long oldMergeSid;

    /**
     * 已合并的数量（不持久化）
     */
    private Integer mergeCount;

    /**
     * 拆单类型
     *
     * @see TradeSplitEnum
     */
    @Column(name = "split_type", type = Types.INTEGER)
    private Integer splitType;

    /**
     * 如果split_type等于1。则对应拆单的主单的sid。否则为0
     */
    private Long splitSid;

    /**
     * 老的split_sid
     */
    private Long oldSplitSid;

    /**
     * 库存调剂类型 1：被调剂的订单 2：接受调剂的订单
     */
    private Integer swapStockType;

    /**
     * 订单更新前是否是分销或分销且供销，不持久化到数据库
     */
    private Boolean oldIsFxOrMix;

    /**
     * 老得运费金额，不持久化到数据库
     */
    private String oldPostFee;

    public Boolean getOldIsFxOrMix() {
        return oldIsFxOrMix;
    }

    public void setOldIsFxOrMix(Boolean oldIsFxOrMix) {
        this.oldIsFxOrMix = oldIsFxOrMix;
    }

    public String getOldPostFee() {
        return oldPostFee;
    }

    public void setOldPostFee(String oldPostFee) {
        this.oldPostFee = oldPostFee;
    }

    /**
     * 经过分销处理后，要从列表中删除
     */
    private boolean removeAfterFxPre;

    public boolean isRemoveAfterFxPre() {
        return removeAfterFxPre;
    }

    public void setRemoveAfterFxPre(boolean removeAfterFxPre) {
        this.removeAfterFxPre = removeAfterFxPre;
    }

    /**
     * 订单下第一个匹配系统商品的子订单的系统商家编码，若都没有匹配，则直接取第一个商品的平台商家编码
     */
    @Column(name = "sys_outer_id", type = Types.VARCHAR)
    private String sysOuterId;

    /**
     * 波次计算使用
     */
    private String oldSysOuterId;

    /**
     * 商品主商家编码 计算波次中订单信息,用于波次分组
     */
    private Long itemSysId;

    /**
     *
     */
    private Long lastItemSysId;

    /**
     * 拆分装箱
     */
    private List<TradePackSplit> tradePackSplits;

    /**
     * 是否智选物流订单
     */
    @Column(name = "is_smart", type = Types.TINYINT)
    private Integer isSmart;

    /**
     * 0：非家装类型 1：家装类型
     */
    @Column(name = "is_jz", type = Types.TINYINT)
    private Integer isJz;

    /**
     * 该订单是否扣过费(打印快递单或系统发货时设置为1)
     */
    private Integer isDeduct;

    /**
     * 次来源地 {@link com.raycloud.dmj.domain.user.User#subSource}
     * jd 保存订单来源
     */
    @Column(name = "sub_source", type = Types.VARCHAR)
    private String subSource;

    /**
     * 是否含有套件
     */
    private Boolean hasSuit;

    /**
     * 所在货位
     */
    private List<String> goodsSectionCodes;

    /**
     * 商品对应的货位
     */
    private Map<String, List<String>> itemGoodsSectionCodes;

    /**
     * 所有的供应商id
     */
    private Set<Long> supplierIds;
    /**
     * 所有的商品品牌
     */
    private Set<String> brands;
    /**
     * 所有的商品分类 （套件下单品）
     */
    private Set<Long> sellerCids;
    /**
     * 所有的商品分类 (套件本身)
     */
    private Set<Long> suitSellerCids;
    /**
     * 所有的规格分类 （套件下单品）
     */
    private Set<Long> skuSellerCids;
    /**
     * 所有的规格分类 (套件本身)
     */
    private Set<Long> skuSuitSellerCids;
    /**
     * 所有的商品类目
     */
    private Set<String> catIds;
    /**
     * 所有的商品标签
     */
    private Set<Long> itemTagIds;

    /**
     * 所在库区
     */
    private String sectionAreaCodes;

    /**
     * 排序后的库区编码
     */
    private String sortedSectionAreaCodes;

    /**
     * 标签id,以,分隔
     */
    @Column(name = "tag_ids", type = Types.VARCHAR)
    private String tagIds;

    private String oldTagIds;



    /**
     * 手动添加的标签或异常备份放到这里，用于判断原字段里的哪些是手动添加的，多个逗号分隔，id_1为标签，id_2为自定义异常
     */
    @Column(name = "manual_mark_ids", type = Types.VARCHAR)
    private String manualMarkIds;

    /**
     * 是否包含赠品
     */
    private Boolean containGift;

    /**
     * 订单操作记录 不持久化到数据库,后续不用在为了打操作记录而去加trade中字段
     */
    private String tradeTrace;

    /**
     * 后置打印、播种打印质检登记内容,并入到订单操作记录
     */
    private String checkContent;

    /**
     * 不计算到波次的原因
     */
    private String notInWaveReason;
    /**
     * 不匹配到波次的原因（规则）
     */
    private String notMatchWaveReason;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 货主Id（展示字段）
     */
    private String shipperId;

    /**
     * 货主名称（展示字段）
     */
    private String shipperName;

    /**
     * 货主名称（展示字段）
     */
    private String shipper;

    /**
     * 是否发送消息给快麦客服
     */
    @Column(name = "is_sendmessage", type = Types.TINYINT)
    private Integer isSendmessage;
    /**
     * /**
     * 提货类型 1-自提 2-快递邮寄
     */
    @Column(name = "pick_goods_type", type = Types.TINYINT)
    private Integer pickGoodsType;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    @Column(name = "is_handler_message", type = Types.TINYINT)
    private Integer isHandlerMessage;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    @Column(name = "is_handler_memo", type = Types.TINYINT)
    private Integer isHandlerMemo;

    /**
     * 供销订单流水号
     */
    private Long flowNumber;
    /**
     * 非虚拟商品的商品数量
     */
    private Integer notIsVirtualItemNum;

    private Double tradeWeight;

    public Long getFlowNumber() {
        return flowNumber;
    }

    public void setFlowNumber(Long flowNumber) {
        this.flowNumber = flowNumber;
    }

    public Integer getIsHandlerMessage() {
        return isHandlerMessage;
    }

    public void setIsHandlerMessage(Integer isHandlerMessage) {
        this.isHandlerMessage = isHandlerMessage;
    }

    public Integer getIsHandlerMemo() {
        return isHandlerMemo;
    }

    public void setIsHandlerMemo(Integer isHandlerMemo) {
        this.isHandlerMemo = isHandlerMemo;
    }

    /**
     * 服务身份标识
     */
    private String timingPromise;

    /**
     * 承诺服务类型，会有多个服务值，以英文半角逗号","切割，其中 tmallpromise.arrival.timing 代表到货承诺时效 tmallpromise.consign.timing代表发货承诺时效
     */
    private String promiseService;

    /**
     * 最晚发货时间，日期，格式2019-04-12 16:00:00 时间等同于最晚揽收时间；
     */
    private Date deliveryTime;

    /**
     * 最晚揽收时间，日期，格式2019-04-12 16:00:00 因发货以有物流揽收记录为准，因此发货和到货订单都会基于该字段进行发货的判责；
     */
    private Date collectTime;

    /**
     * 最晚签收时间，日期，格式2019-04-12 16:00:00 到货订单会依据该字段进行到货的判责；
     */
    private Date signTime;

    /**
     * 相对到达时间，单位为天，0当日达，1次日达，2三日达，3四日达，4五日达
     */
    private Integer esTime;

    /**
     * 智能审核的时候是否需要激活
     */
    private boolean needActive = true;


    /**
     * 如果该订单是合单后的主单，该字段记录该主单下隐藏的订单
     */
    private List<Trade> mergeList;

    private Boolean needUploadTrade;

    /**
     * 是否需要上传整笔订单 tradeConfig.sysSourceSplitConsignUploadAll()=true 会使用
     */
    private boolean needUploadTradeAll;

    /**
     * 拆分的系统单触发整笔上传时候，对应的平台子订单 tradeConfig.sysSourceSplitConsignUploadAll()=true 会使用
     */
    private List<Order> uploadPlatSplitOrders;

    private Map<OpEnum, String> operations = new LinkedHashMap<>();
    /**
     * 所有的order都被上传过滤
     */
    private Boolean needPreUpload;

    public Boolean getNeedPreUpload() {
        return needPreUpload;
    }

    public void setNeedPreUpload(Boolean needPreUpload) {
        this.needPreUpload = needPreUpload;
    }

    /**
     * 是否使用截取的outerId去匹配
     */
    private Boolean useOuterIdMatch;

    /**
     * 记录原始订单的sid
     */
    private Long sourceSid;
    /**
     * 京东京品试订单标示
     */
    private String isJps;

    /**
     * 订单是否新增
     */
    private boolean insert;

    private Integer isAddressSplitGet;

    private Integer isSplitGet;

    public Integer getIsAddressSplitGet() {
        return isAddressSplitGet;
    }

    public void setIsAddressSplitGet(Integer isAddressSplitGet) {
        this.isAddressSplitGet = isAddressSplitGet;
    }

    public Integer getIsSplitGet() {
        return isSplitGet;
    }

    public void setIsSplitGet(Integer isSplitGet) {
        this.isSplitGet = isSplitGet;
    }
    public String getIsJps() {
        return isJps;
    }

    public void setIsJps(String isJps) {
        this.isJps = isJps;
    }

    /**
     * 扩展表
     */
    private TradeExt tradeExt;

    /**
     * 扩展表  平台经销分销信息
     */
    private TradeDistributor tradeDistributor;

    /**
     * 如果是供销订单，这个值代表对应的分销订单
     * 如果是分销订单，这个值代表对应的供销订单
     */
    private Trade sourceTrade;
    /**
     * 供销订单用
     */
    private Integer wlbTemplateType;

    /**
     * 供销订单使用,分销作废来源
     * 0-取消分销属性
     * 1-分销订单作废
     * 2-打回供销订单，自动作废!
     * 4-流水消费失败，自动作废供销订单
     * 6-供销混合拆分，自动作废
     * 7-分销订单更改供销商，自动作废供销订单
     * 8-分销订单发生退款或者其他ERP发货，供销订单找不到对应的商品，所以供销单作废，重新生成供销订单，避免发错货。
     * 9-供销执行了商品搭配规则/套转单，分销部分退款，供销订单作废，并重新生成供销单
     * 10-商品信息变更，自动作废订单
     * 99-供销缺货拆分，自动作废
     */
    private Integer cancelFrom;

    /**
     * 分销订单是否在系统上传（供销订单用）
     */
    private Integer fxIsUpload;

    /**
     * 匹配规则用，用逗号分开
     */
    private String sellerFlagString;

    /**
     * 匹配规则用，用逗号分开
     */
    private String sellerMemoString;

    /**
     * 是否是取消分销属性触发的订单同步，如果是1，则不需要匹配供应商，因为是取消分销属性
     */
    private Integer isCancelDistributorAttribute;

    /**
     * 平台原始数据json
     */
    private String platformSourceJson;

    private boolean fxSplit;
    private String fxSource;
    private Long guid;

    private Boolean isDaixiao;

    private Long finalDestCompanyId;


    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }


    /**
     * 订单O2o
     */
    private TradeO2o tradeO2o;

    /**
     * 数据库中的订单O2o
     */
    private TradeO2o originTradeO2o;

    public TradeO2o getTradeO2o() {
        return tradeO2o;
    }

    public void setTradeO2o(TradeO2o tradeO2o) {
        this.tradeO2o = tradeO2o;
    }

    public TradeO2o getOriginTradeO2o() {
        return originTradeO2o;
    }

    public void setOriginTradeO2o(TradeO2o originTradeO2o) {
        this.originTradeO2o = originTradeO2o;
    }

    public Boolean getIsDaixiao() {
        return this.isDaixiao;
    }

    public void setIsDaixiao(Boolean isDaixiao) {
        this.isDaixiao = isDaixiao;
    }

    public Long getFinalDestCompanyId() {
        return finalDestCompanyId;
    }

    public void setFinalDestCompanyId(Long finalDestCompanyId) {
        this.finalDestCompanyId = finalDestCompanyId;
    }

    public Long getGuid() {
        return guid;
    }

    public void setGuid(Long guid) {
        this.guid = guid;
    }

    public List<Long> getFxSids() {
        return fxSids;
    }

    public void setFxSids(List<Long> fxSids) {
        this.fxSids = fxSids;
    }

    public Map<Long, TradeResult> getErrors() {
        return errors;
    }

    public void setErrors(Map<Long, TradeResult> errors) {
        this.errors = errors;
    }

    /**
     * 智能审核规则匹配结果
     */
    private Integer auditMatchRule;

    /**
     * 车型memo
     */
    private String carAttrMemo;

    private String maskReceiverName;

    private String maskReceiverAddress;


    /**
     * 原始收件人手机
     */
    private String oriReceiverMobile;

    /**
     * 原始收件人姓名
     */
    private String oriReceiverName;

    /**
     * 原始收件人详细地址
     */
    private String oriReceiverAddress;

    private Boolean needCoverAddress = false;

    /**
     * 淘宝订单解密类型
     */
    private String decryptCode;

    /**
     * 延迟返货时间
     */
    private Integer delayShipTime;

    /**
     * 快递匹配规则id（非持久化字段）
     */
    private Long templateMatchRuleId;


    /**
     * 需要删除配货记录订单
     */
    private Boolean needRemoveAllocateGoods;

    /**
     * 是否处理合单  /trade/platform/save
     */
    private boolean processMergeTrade = true;

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    /**
     * 不是在波次具体原因
     */
    private LinkedHashMap<String, String> notInWaveSpecificReasonMap;

    public boolean isProcessMergeTrade() {
        return processMergeTrade;
    }

    public void setProcessMergeTrade(boolean processMergeTrade) {
        this.processMergeTrade = processMergeTrade;
    }

    public Integer getDelayShipTime() {
        return delayShipTime;
    }

    public void setDelayShipTime(Integer delayShipTime) {
        this.delayShipTime = delayShipTime;
    }

    /**
     * 毒（得物、云仓）平台是否延迟发货
     * 不要用这个方法业务判断不要放在Entity里
     * @return
     */
    @Deprecated
    public boolean isDelayShip() {
        return Objects.nonNull(getDelayShipTime()) && getDelayShipTime() > 0 && getIsHalt() == 1;
    }

    private Boolean splitUp;

    /**
     * 合单后订单优惠金额，不包括包括了子订单的优惠和店铺优惠
     */
    private String mergeDiscountFee;

    public String getMergeDiscountFee() {
        return mergeDiscountFee;
    }

    public void setMergeDiscountFee(String mergeDiscountFee) {
        this.mergeDiscountFee = mergeDiscountFee;
    }

    /**
     * 是否平台指定仓库匹配
     */
    private boolean platformDesignatedWarehouseMatch;

    /**
     * 店铺简称,不持久化
     */
    private String shopShortTitle;

    /**
     * 是否是根据配置转换得来的重新发货（上传发货需要，非持久化字段）
     */
    private Integer isAutoReUpload;

    /**
     * 上传异常类型描述（非持久化字段，仅用于返回前端）
     */
    private String uploadErrorTypeDesc;

    /**
     * 希音平台原始数据 json（非持久化字段，仅用于生成希音的PO单（或者拣货单））
     * 用在订单池的上游 保存希音订单接口返回的数据，从订单池获取数据的时候，根据这个字段，生成PO单（或者拣货单）列表数据
     *
     * @return
     */
    private String sheinOriginalJson;
    /**
     * 商品信息
     */
    private String itemInfo;
    /**
     * 验货登记异常
     */
    private String registerError;
    /**
     * 免检免验订单验货完成
     */
    private Boolean finishNoCheckPack;

    /**
     * 免检免验订单验货完成报错信息
     */
    private String finishNoCheckPackErrorMsg;
    /**
     * 验货登记补货单异常
     */
    private ReplenishTrades bindReplenish;

    /**
     * 商品信息
     */
    private List<Order> itemInfoList;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 业务员名称
     */
    private String salesmanName;

    /**
     * 是否为解除快递异常订单 是的话 要发一下事件 不持久化
     */
    private Integer removeUnattainable;

    /**
     * 批次打印记录的批次号
     */
    private String batchNo;
    /**
     * 批次打印记录里的打印序号
     */
    private Integer seq;

    private PlatformRecommendation platformRecommendation;

    private String itemNameInfo;

    /**
     * 规格名称
     */
    private String skuNameInfo;

    /**
     * 是否穿透面单
     */
    private boolean penetrateTrade;

    private String sortGoodsCode;
    /**
     * 匹配的运单数量规则，不做持久化*
     */
    private WaybillUsingRule waybillUsingRule;


    public String getSortGoodsCode() {
        return sortGoodsCode;
    }

    public void setSortGoodsCode(String sortGoodsCode) {
        this.sortGoodsCode = sortGoodsCode;
    }

    public WaybillUsingRule getWaybillUsingRule() {
        return waybillUsingRule;
    }

    public void setWaybillUsingRule(WaybillUsingRule waybillUsingRule) {
        this.waybillUsingRule = waybillUsingRule;
    }

    public boolean isPenetrateTrade() {
        return penetrateTrade;
    }

    public void setPenetrateTrade(boolean penetrateTrade) {
        this.penetrateTrade = penetrateTrade;
    }

    private String mainSplitGetKey;

    public String getMainSplitGetKey() {
        return mainSplitGetKey;
    }

    public void setMainSplitGetKey(String mainSplitGetKey) {
        this.mainSplitGetKey = mainSplitGetKey;
    }

    /**
     * 奇门子店铺发货地址*
     */
    private Address qiMenAddress;

    public Address getQiMenAddress() {
        return qiMenAddress;
    }

    public void setQiMenAddress(Address qiMenAddress) {
        this.qiMenAddress = qiMenAddress;
    }

    public String getItemNameInfo() {
        return itemNameInfo;
    }

    public void setItemNameInfo(String itemNameInfo) {
        this.itemNameInfo = itemNameInfo;
    }

    /**
     * 商家编码数量
     */
    private List<Pair<String, Integer>> sysOutId2NumList;

    private Map<String, PlatformRecommendation> tidToPr;

    public boolean isSkipPaymentLink() {
        return skipPaymentLink;
    }

    public void setSkipPaymentLink(boolean skipPaymentLink) {
        this.skipPaymentLink = skipPaymentLink;
    }

    /**
     * 跳过金额联动计算
     */
    private boolean skipPaymentLink;

    public Long getSalesmanId() {
        return salesmanId;
    }

    public void setSalesmanId(Long salesmanId) {
        this.salesmanId = salesmanId;
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }

    /**
     * 快递模版匹配结果的策略， 不持久化
     */
    private String expressMatchStrategy;

    /**
     * 供销订单，运费规则名称，不持久化
     */
    private String gxFreightRuleName;

    /**
     * 是否强制更新地址，不持久化
     * 针对下游无法判断地址是否更新的情形，由上游指定更新
     */
    private boolean forceUpdateAddress;

    /**
     * 是否已经填充了供销了 打印用
     */
    private Boolean coverGx;

    public Boolean getCoverGx() {
        return coverGx;
    }

    public void setCoverGx(Boolean coverGx) {
        this.coverGx = coverGx;
    }

    /**
     * 是否修改模板,不持久化
     */
    private Boolean modifyTemplate;

    /**
     * 老模板ID
     */
    private Long oldTemplateId;

    /**
     * 老模板类型
     */
    private Integer oldTemplateType;

    /**
     * 快递公司id
     */
    private Long oldLogisticsCompanyId;

    /**
     * 旧数据的地址Md5，用于兼容临界数据判断，不持久化
     */
    private String oldAddressMd5;

    /**
     * 供销订单，运费
     */
    private String gxPostFee;

    public String getGxPostFee() {
        return gxPostFee;
    }

    public void setGxPostFee(String gxPostFee) {
        this.gxPostFee = gxPostFee;
    }

    public String getGxFreightRuleName() {
        return gxFreightRuleName;
    }

    public void setGxFreightRuleName(String gxFreightRuleName) {
        this.gxFreightRuleName = gxFreightRuleName;
    }

    /**
     *毛利润展示公式
     */
    private String grossProfitDisplay;

    public String getGrossProfitDisplay() {
        return grossProfitDisplay;
    }

    public void setGrossProfitDisplay(String grossProfitDisplay) {
        this.grossProfitDisplay = grossProfitDisplay;
    }

    public boolean isPlatformDesignatedWarehouseMatch() {
        return platformDesignatedWarehouseMatch;
    }

    public void setPlatformDesignatedWarehouseMatch(boolean platformDesignatedWarehouseMatch) {
        this.platformDesignatedWarehouseMatch = platformDesignatedWarehouseMatch;
    }

    public String getOldTagIds() {
        return oldTagIds;
    }

    public void setOldTagIds(String oldTagIds) {
        this.oldTagIds = oldTagIds;
    }

    public String getSellerFlagString() {
        return sellerFlagString;
    }

    public void setSellerFlagString(String sellerFlagString) {
        this.sellerFlagString = sellerFlagString;
    }

    public String getFxSource() {
        return fxSource;
    }

    public void setFxSource(String fxSource) {
        this.fxSource = fxSource;
    }

    public Integer getFxIsUpload() {
        return fxIsUpload;
    }

    public boolean isFxSplit() {
        return fxSplit;
    }

    public void setFxSplit(boolean fxSplit) {
        this.fxSplit = fxSplit;
    }

    public void setFxIsUpload(Integer fxIsUpload) {
        this.fxIsUpload = fxIsUpload;
    }

    public Integer getCancelFrom() {
        return cancelFrom;
    }

    public void setCancelFrom(Integer cancelFrom) {
        this.cancelFrom = cancelFrom;
    }

    public Trade getSourceTrade() {
        return sourceTrade;
    }

    public void setSourceTrade(Trade sourceTrade) {
        this.sourceTrade = sourceTrade;
    }

    public Integer getWlbTemplateType() {
        return wlbTemplateType;
    }

    public void setWlbTemplateType(Integer wlbTemplateType) {
        this.wlbTemplateType = wlbTemplateType;
    }

    public Boolean getNeedUploadTrade() {
        return needUploadTrade;
    }

    public void setNeedUploadTrade(Boolean needUploadTrade) {
        this.needUploadTrade = needUploadTrade;
    }

    public boolean getNeedUploadTradeAll() {
        return needUploadTradeAll;
    }

    public void setNeedUploadTradeAll(boolean needUploadTradeAll) {
        this.needUploadTradeAll = needUploadTradeAll;
    }

    public List<Order> getUploadPlatSplitOrders() {
        return uploadPlatSplitOrders;
    }

    public void setUploadPlatSplitOrders(List<Order> uploadPlatSplitOrders) {
        this.uploadPlatSplitOrders = uploadPlatSplitOrders;
    }

    public Integer getIsSendmessage() {
        return isSendmessage;
    }

    public void setIsSendmessage(Integer isSendmessage) {
        this.isSendmessage = isSendmessage;
    }

    public TradeMerge getTradeMerge() {
        return tradeMerge;
    }

    public void setTradeMerge(TradeMerge tradeMerge) {
        this.tradeMerge = tradeMerge;
    }

    public Integer getMergeType() {
        return mergeType;
    }

    public void setMergeType(Integer mergeType) {
        this.mergeType = mergeType;
    }

    public Long getMergeSid() {
        return mergeSid;
    }

    public void setMergeSid(Long mergeSid) {
        this.mergeSid = mergeSid;
    }

    public Long getOldMergeSid() {
        return oldMergeSid;
    }

    public void setOldMergeSid(Long oldMergeSid) {
        this.oldMergeSid = oldMergeSid;
    }

    public Integer getMergeCount() {
        return mergeCount;
    }

    public void setMergeCount(Integer mergeCount) {
        this.mergeCount = mergeCount;
    }

    public Integer getSplitType() {
        return splitType;
    }

    public void setSplitType(Integer splitType) {
        this.splitType = splitType;
    }

    public Long getSplitSid() {
        return splitSid;
    }

    public void setSplitSid(Long splitSid) {
        this.splitSid = splitSid;
    }

    public Long getOldSplitSid() {
        return oldSplitSid;
    }

    public void setOldSplitSid(Long oldSplitSid) {
        this.oldSplitSid = oldSplitSid;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getOldSource() {
        return oldSource;
    }

    public void setOldSource(String oldSource) {
        this.oldSource = oldSource;
    }

    public String getUserSource() {
        return userSource;
    }

    public void setUserSource(String userSource) {
        this.userSource = userSource;
    }

    public String getSplitSource() {
        return splitSource;
    }

    public void setSplitSource(String splitSource) {
        this.splitSource = splitSource;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public Integer getCheckManualMergeCount() {
        return checkManualMergeCount;
    }

    public void setCheckManualMergeCount(Integer checkManualMergeCount) {
        this.checkManualMergeCount = checkManualMergeCount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public Map<String, TradeType> getTradeTypeMap() {
        return tradeTypeMap;
    }

    public void setTradeTypeMap(Map<String, TradeType> tradeTypeMap) {
        this.tradeTypeMap = tradeTypeMap;
    }

    public String getPlatformSourceJson() {
        return platformSourceJson;
    }

    public void setPlatformSourceJson(String platformSourceJson) {
        this.platformSourceJson = platformSourceJson;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public String getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public String getChSysStatus() {
        return chSysStatus;
    }

    public void setChSysStatus(String chSysStatus) {
        this.chSysStatus = chSysStatus;
    }

    public String getOldSysStatus() {
        return oldSysStatus;
    }

    public void setOldSysStatus(String oldSysStatus) {
        this.oldSysStatus = oldSysStatus;
    }

    public String getStockStatus() {
        return stockStatus;
    }

    public void setStockStatus(String stockStatus) {
        this.stockStatus = stockStatus;
    }

    public String getOldStockStatus() {
        return oldStockStatus;
    }

    public void setOldStockStatus(String oldStockStatus) {
        this.oldStockStatus = oldStockStatus;
    }

    public Integer getValidItemNum() {
        return validItemNum;
    }

    public void setValidItemNum(Integer validItemNum) {
        this.validItemNum = validItemNum;
    }

    public Integer getInsufficientNum() {
        return insufficientNum;
    }

    public void setInsufficientNum(Integer insufficientNum) {
        this.insufficientNum = insufficientNum;
    }

    public Double getInsufficientRate() {
        return insufficientRate;
    }

    public void setInsufficientRate(Double insufficientRate) {
        this.insufficientRate = insufficientRate;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public SendType getConsignType() {
        return consignType;
    }

    public void setConsignType(SendType consignType) {
        this.consignType = consignType;
    }

    public Integer getSysConsigned() {
        return sysConsigned;
    }

    public void setSysConsigned(Integer sysConsigned) {
        this.sysConsigned = sysConsigned;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getUpdTime() {
        return updTime;
    }

    public void setUpdTime(Date updTime) {
        this.updTime = updTime;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getOldOutSid() {
        return oldOutSid;
    }

    public void setOldOutSid(String oldOutSid) {
        this.oldOutSid = oldOutSid;
    }

    public Integer getCanDelivered() {
        return canDelivered;
    }

    public void setCanDelivered(Integer canDelivered) {
        this.canDelivered = canDelivered;
    }

    public Integer getCanConfirmSend() {
        return canConfirmSend;
    }

    public void setCanConfirmSend(Integer canConfirmSend) {
        this.canConfirmSend = canConfirmSend;
    }

    public Integer getIsManulfill() {
        return isManulfill;
    }

    public void setIsManulfill(Integer isManulfill) {
        this.isManulfill = isManulfill;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getOriginTemplateName() {
        return originTemplateName;
    }

    public void setOriginTemplateName(String originTemplateName) {
        this.originTemplateName = originTemplateName;
    }

    public List<IExpressTemplateBase> getBackTemplateList() {
        return backTemplateList;
    }

    public void setBackTemplateList(List<IExpressTemplateBase> backTemplateList) {
        this.backTemplateList = backTemplateList;
    }

    public Long getExpressCompanyId() {
        return expressCompanyId;
    }

    public void setExpressCompanyId(Long expressCompanyId) {
        this.expressCompanyId = expressCompanyId;
    }


    public Date getExpressPrintTime() {
        return expressPrintTime;
    }

    public void setExpressPrintTime(Date expressPrintTime) {
        this.expressPrintTime = expressPrintTime;
    }

    public Date getDeliverPrintTime() {
        return deliverPrintTime;
    }

    public void setDeliverPrintTime(Date deliverPrintTime) {
        this.deliverPrintTime = deliverPrintTime;
    }

    public Date getAssemblyPrintTime() {
        return assemblyPrintTime;
    }

    public void setAssemblyPrintTime(Date assemblyPrintTime) {
        this.assemblyPrintTime = assemblyPrintTime;
    }

    public Long getWarehouseMatchRuleId() {
        return warehouseMatchRuleId;
    }

    public void setWarehouseMatchRuleId(Long warehouseMatchRuleId) {
        this.warehouseMatchRuleId = warehouseMatchRuleId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getOldWarehouseId() {
        return oldWarehouseId;
    }

    public void setOldWarehouseId(Long oldWarehouseId) {
        this.oldWarehouseId = oldWarehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getIsAutoAudit() {
        return isAutoAudit;
    }

    public void setIsAutoAudit(Integer isAutoAudit) {
        this.isAutoAudit = isAutoAudit;
    }

    @Deprecated
    public String getBuyerArea() {
        return buyerArea;
    }

    @Deprecated
    public void setBuyerArea(String buyerArea) {
        this.buyerArea = buyerArea;
    }

    @Deprecated
    public String getBuyerEmail() {
        return buyerEmail;
    }

    @Deprecated
    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }

    public Boolean getHasBuyerMessage() {
        return hasBuyerMessage;
    }

    public void setHasBuyerMessage(Boolean hasBuyerMessage) {
        this.hasBuyerMessage = hasBuyerMessage;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getBuyerAlipayNo() {
        return buyerAlipayNo;
    }

    public void setBuyerAlipayNo(String buyerAlipayNo) {
        this.buyerAlipayNo = buyerAlipayNo;
    }

    public Long getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(Long sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public String getOldSysMemo() {
        return oldSysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public void setOldSysMemo(String oldSysMemo) {
        this.oldSysMemo = oldSysMemo;
    }

    public String getInvoiceKind() {
        return invoiceKind;
    }

    public void setInvoiceKind(String invoiceKind) {
        this.invoiceKind = invoiceKind;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
    }

    public Integer getNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(Integer needInvoice) {
        this.needInvoice = needInvoice;
    }

    public Integer getInvoiceFormat() {
        return invoiceFormat;
    }

    public void setInvoiceFormat(Integer invoiceFormat) {
        this.invoiceFormat = invoiceFormat;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getBuyerTaxNo() {
        return buyerTaxNo;
    }

    public void setBuyerTaxNo(String buyerTaxNo) {
        this.buyerTaxNo = buyerTaxNo;
    }


    public String getTradeFrom() {
        return tradeFrom;
    }

    public void setTradeFrom(String tradeFrom) {
        this.tradeFrom = tradeFrom;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public Double getVolume() {
        return volume;
    }

    public Trade setVolume(Double volume) {
        this.volume = volume;
        return this;
    }

    public Integer getItemNum() {
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public Integer getOldItemNum() {
        return oldItemNum;
    }

    public void setOldItemNum(Integer oldItemNum) {
        this.oldItemNum = oldItemNum;
    }

    public Integer getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(Integer itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public Integer getOldItemKindNum() {
        return oldItemKindNum;
    }

    public void setOldItemKindNum(Integer oldItemKindNum) {
        this.oldItemKindNum = oldItemKindNum;
    }

    public Integer getIsStore() {
        return isStore;
    }

    public void setIsStore(Integer isStore) {
        this.isStore = isStore;
    }

    public Integer getScalping() {
        return scalping;
    }

    public void setScalping(Integer scalping) {
        this.scalping = scalping;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
    }

    public Integer getIsPackage() {
        return isPackage;
    }

    public void setIsPackage(Integer isPackage) {
        this.isPackage = isPackage;
    }

    public Integer getOldIsPackage() {
        if (oldIsPackage == null) {
            return isPackage;
        }
        return oldIsPackage;
    }

    public void setOldIsPackage(Integer oldIsPackage) {
        this.oldIsPackage = oldIsPackage;
    }

    public Integer getIsWeigh() {
        return isWeigh;
    }

    public void setIsWeigh(Integer isWeigh) {
        this.isWeigh = isWeigh;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public Long getPresellRuleId() {
        return presellRuleId;
    }

    public void setPresellRuleId(Long presellRuleId) {
        this.presellRuleId = presellRuleId;
    }

    public PresellRule getPresellRule() {
        return presellRule;
    }

    public void setPresellRule(PresellRule presellRule) {
        this.presellRule = presellRule;
    }

    /**
     * 是否是预售订单
     */
    public boolean isPresellTrade() {
        return this.isPresell != null && (this.getIsPresell() == 1 || this.getIsPresell() == 3);
    }

    public boolean presell2Normal() {
        if (Objects.nonNull(this.isPresell) && 2 - getIsPresell() == 0) {
            return true;
        }
        return false;
    }

    public Date getTimeoutActionTime() {
        return timeoutActionTime;
    }

    public void setTimeoutActionTime(Date timeoutActionTime) {
        this.timeoutActionTime = timeoutActionTime;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Long getWaveShortId() {
        return waveShortId;
    }

    public void setWaveShortId(Long waveShortId) {
        this.waveShortId = waveShortId;
    }

    public Long getPositionNo() {
        return positionNo;
    }

    public void setPositionNo(Long positionNo) {
        this.positionNo = positionNo;
    }

    public Integer getIsUpload() {
        return isUpload;
    }

    public void setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
    }

    public boolean isSysTriggerUpload() {
        return sysTriggerUpload;
    }

    public void setSysTriggerUpload(boolean sysTriggerUpload) {
        this.sysTriggerUpload = sysTriggerUpload;
    }

    public String getBoxingList() {
        return boxingList;
    }

    public void setBoxingList(String boxingList) {
        this.boxingList = boxingList;
    }

    public String getPoNos() {
        return poNos;
    }

    public void setPoNos(String poNos) {
        this.poNos = poNos;
    }

    public String getVipPickNo() {
        return vipPickNo;
    }

    public void setVipPickNo(String vipPickNo) {
        this.vipPickNo = vipPickNo;
    }

    public String getVipStorageNo() {
        return vipStorageNo;
    }

    public void setVipStorageNo(String vipStorageNo) {
        this.vipStorageNo = vipStorageNo;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public boolean isSplitTrade() {
        return isSplitTrade;
    }

    public void setSplitTrade(boolean splitTrade) {
        isSplitTrade = splitTrade;
    }

    public List<OrderChannel> getChannels() {
        return channels;
    }

    public void setChannels(List<OrderChannel> channels) {
        this.channels = channels;
    }

    public List<GiftOrder> getGiftOrders() {
        return giftOrders;
    }

    public void setGiftOrders(List<GiftOrder> giftOrders) {
        this.giftOrders = giftOrders;
    }

    public Set<TradeExcStatus> getExcStauses() {
        return excStauses;
    }

    public void setExcStauses(Set<TradeExcStatus> excStauses) {
        this.excStauses = excStauses;
    }

    public Trade getOrigin() {
        return origin;
    }

    public void setOrigin(Trade origin) {
        this.origin = origin;
    }

    public List<MessageMemo> getMessageMemos() {
        return messageMemos;
    }

    public void setMessageMemos(List<MessageMemo> messageMemos) {
        this.messageMemos = messageMemos;
    }

    public String toString() {
        return new StringBuilder("{sid:").append(getSid()).append(", tid:").append(getTid()).append(", sysStatus:").append(sysStatus).append(", stockStatus:").append(stockStatus).append(", status:").append(status).append(", presellRule:").append(presellRule).append("}").toString();
    }

    /**
     * 财务审核错误信息
     */
    private String financeAuditErrorMessage;

    public String getFinanceAuditErrorMessage() {
        return financeAuditErrorMessage;
    }

    public void setFinanceAuditErrorMessage(String financeAuditErrorMessage) {
        this.financeAuditErrorMessage = financeAuditErrorMessage;
    }

    /**
     * 判断订单是否为出库单
     *
     * @return
     */
    public boolean isOutstock() {
        return getType() != null && !"".equals(getType()) && getType().startsWith(TradeConstants.TYPE_TRADE_OUT);
    }

    /**
     * 判断订单是否为客户订单
     *
     * @return
     */
    public boolean isCustomerTrade() {
        return getType() != null && !"".equals(getType()) && getType().endsWith(TradeConstants.TYPE_TRADE_CUSTOMER);
    }

    /**
     * 判断订单是否为出库单
     * <p>
     * 判断订单是否为档口订单
     *
     * @return
     */
    public boolean isDangkouTrade() {
        return getType() != null && !"".equals(getType()) && getType().startsWith(TradeConstants.TYPE_TRADE_DANGKOU);
    }

    public Integer getSwapStockType() {
        return swapStockType;
    }

    public void setSwapStockType(Integer swapStockType) {
        this.swapStockType = swapStockType;
    }

    public String getSysOuterId() {
        return sysOuterId;
    }

    public void setSysOuterId(String sysOuterId) {
        this.sysOuterId = sysOuterId;
    }

    public List<TradePackSplit> getTradePackSplits() {
        return tradePackSplits;
    }

    public void setTradePackSplits(List<TradePackSplit> tradePackSplits) {
        this.tradePackSplits = tradePackSplits;
    }

    public Integer getIsSmart() {
        return isSmart;
    }

    public void setIsSmart(Integer isSmart) {
        this.isSmart = isSmart;
    }

    public Integer getIsJz() {
        return isJz;
    }

    public void setIsJz(Integer isJz) {
        this.isJz = isJz;
    }

    public Integer getIsDeduct() {
        return isDeduct;
    }

    public void setIsDeduct(Integer isDeduct) {
        this.isDeduct = isDeduct;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    public Boolean getHasSuit() {
        return hasSuit;
    }

    public void setHasSuit(Boolean hasSuit) {
        this.hasSuit = hasSuit;
    }

    public List<String> getGoodsSectionCodes() {
        return goodsSectionCodes;
    }

    public void setGoodsSectionCodes(List<String> goodsSectionCodes) {
        this.goodsSectionCodes = goodsSectionCodes;
    }

    public Set<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(Set<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public String getSectionAreaCodes() {
        return sectionAreaCodes;
    }

    public void setSectionAreaCodes(String sectionAreaCodes) {
        this.sectionAreaCodes = sectionAreaCodes;
    }

    public String getSortedSectionAreaCodes() {
        return sortedSectionAreaCodes;
    }

    public void setSortedSectionAreaCodes(String sortedSectionAreaCodes) {
        this.sortedSectionAreaCodes = sortedSectionAreaCodes;
    }

    public List<String> getTagName() {
        return tagName;
    }

    public void setTagName(List<String> tagName) {
        this.tagName = tagName;
    }

    public List<TradeTag> getTags() {
        return tags;
    }

    public void setTags(List<TradeTag> tags) {
        this.tags = tags;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public String getManualMarkIds() {
        return manualMarkIds;
    }

    public void setManualMarkIds(String manualMarkIds) {
        this.manualMarkIds = manualMarkIds;
    }



    public String getSalePrice() {
        return this.getSaleFee();
    }

    public void setSalePrice(String salePrice) {
        this.setSaleFee(salePrice);
        super.setSalePrice(salePrice);
    }

    public Integer getThreePlTiming() {
        return threePlTiming;
    }

    public void setThreePlTiming(Integer threePlTiming) {
        this.threePlTiming = threePlTiming;
    }

    public Integer getIsTmallDelivery() {
        return isTmallDelivery;
    }

    public void setIsTmallDelivery(Integer isTmallDelivery) {
        this.isTmallDelivery = isTmallDelivery;
    }

    public Boolean getContainGift() {
        return containGift;
    }

    public void setContainGift(Boolean containGift) {
        this.containGift = containGift;
    }

    public String getTradeTrace() {
        return tradeTrace;
    }

    public void setTradeTrace(String tradeTrace) {
        this.tradeTrace = tradeTrace;
    }

    public String getCheckContent() {
        return checkContent;
    }

    public void setCheckContent(String checkContent) {
        this.checkContent = checkContent;
    }

    public String getNotInWaveReason() {
        return notInWaveReason;
    }

    public void setNotInWaveReason(String notInWaveReason) {
        this.notInWaveReason = notInWaveReason;
    }

    public Integer getSingleItemKindNum() {
        return singleItemKindNum;
    }

    public void setSingleItemKindNum(Integer singleItemKindNum) {
        this.singleItemKindNum = singleItemKindNum;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getPickGoodsType() {
        return pickGoodsType;
    }

    public void setPickGoodsType(Integer pickGoodsType) {
        this.pickGoodsType = pickGoodsType;
    }

    public String getTimingPromise() {
        return timingPromise;
    }

    public void setTimingPromise(String timingPromise) {
        this.timingPromise = timingPromise;
    }

    public String getPromiseService() {
        return promiseService;
    }

    public void setPromiseService(String promiseService) {
        this.promiseService = promiseService;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Integer getEsTime() {
        return esTime;
    }

    public void setEsTime(Integer esTime) {
        this.esTime = esTime;
    }

    public Date getPtConsignTime() {
        return ptConsignTime;
    }

    public void setPtConsignTime(Date ptConsignTime) {
        this.ptConsignTime = ptConsignTime;
    }

    public Integer getPrintCount() {
        return printCount;
    }

    public void setPrintCount(Integer printCount) {
        this.printCount = printCount;
    }

    public List<Trade> getMergeList() {
        return mergeList;
    }

    public void setMergeList(List<Trade> mergeList) {
        this.mergeList = mergeList;
    }

    public boolean getIfFxForcePushTrade() {
        return  (getV() != null && (getV() | 64) - getV() == 0) || ifFxForcePushTrade;
    }

    public boolean getIfFxWaitPay() {
        return  (getV() != null && (getV() | TradeConstants.V_QIMEN_FX_WAIT_PAY) - getV() == 0) ;
    }

    public void setIfFxForcePushTrade(boolean ifFxForcePushTrade) {
        this.ifFxForcePushTrade = ifFxForcePushTrade;
    }

    public Long getSourceSid() {
        return sourceSid;
    }

    public void setSourceSid(Long sourceSid) {
        this.sourceSid = sourceSid;
    }

    public Integer getStockRegionType() {
        return stockRegionType;
    }

    public void setStockRegionType(Integer stockRegionType) {
        this.stockRegionType = stockRegionType;
    }

    public boolean getNeedActive() {
        return needActive;
    }

    public void setNeedActive(boolean needActive) {
        this.needActive = needActive;
    }

    public Map<OpEnum, String> getOperations() {
        return operations;
    }

    public void setOperations(Map<OpEnum, String> operations) {
        this.operations = operations;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }

    public TradeExt getTradeExt() {
        return tradeExt;
    }

    public void setTradeExt(TradeExt tradeExt) {
        this.tradeExt = tradeExt;
    }

    public TradeDistributor getTradeDistributor() {
        return tradeDistributor;
    }

    public void setTradeDistributor(TradeDistributor tradeDistributor) {
        this.tradeDistributor = tradeDistributor;
    }

    public boolean isInsert() {
        return insert;
    }

    public void setInsert(boolean insert) {
        this.insert = insert;
    }


    public Integer getIsCancelDistributorAttribute() {
        return isCancelDistributorAttribute;
    }

    public void setIsCancelDistributorAttribute(Integer isCancelDistributorAttribute) {
        this.isCancelDistributorAttribute = isCancelDistributorAttribute;
    }

    public Integer getAuditMatchRule() {
        return auditMatchRule;
    }

    public void setAuditMatchRule(Integer auditMatchRule) {
        this.auditMatchRule = auditMatchRule;
    }

    public String getNotMatchWaveReason() {
        return notMatchWaveReason;
    }

    public void setNotMatchWaveReason(String notMatchWaveReason) {
        this.notMatchWaveReason = notMatchWaveReason;
    }

    public String getCarAttrMemo() {
        return carAttrMemo;
    }

    public void setCarAttrMemo(String carAttrMemo) {
        this.carAttrMemo = carAttrMemo;
    }

    public Map<String, List<String>> getItemGoodsSectionCodes() {
        return itemGoodsSectionCodes;
    }

    public void setItemGoodsSectionCodes(Map<String, List<String>> itemGoodsSectionCodes) {
        this.itemGoodsSectionCodes = itemGoodsSectionCodes;
    }

    public Boolean getNeedCoverAddress() {
        return needCoverAddress;
    }

    public void setNeedCoverAddress(Boolean needCoverAddress) {
        this.needCoverAddress = needCoverAddress;
    }

    public String getMaskReceiverName() {
        return maskReceiverName;
    }

    public void setMaskReceiverName(String maskReceiverName) {
        this.maskReceiverName = maskReceiverName;
    }

    public String getMaskReceiverAddress() {
        return maskReceiverAddress;
    }

    public void setMaskReceiverAddress(String maskReceiverAddress) {
        this.maskReceiverAddress = maskReceiverAddress;
    }

    public Set<String> getBrands() {
        return brands;
    }

    public void setBrands(Set<String> brands) {
        this.brands = brands;
    }

    public Set<Long> getSellerCids() {
        return sellerCids;
    }

    public void setSellerCids(Set<Long> sellerCids) {
        this.sellerCids = sellerCids;
    }

    public Set<Long> getSuitSellerCids() {
        return suitSellerCids;
    }

    public void setSuitSellerCids(Set<Long> suitSellerCids) {
        this.suitSellerCids = suitSellerCids;
    }

    public String getSellerMemoString() {
        return sellerMemoString;
    }

    public void setSellerMemoString(String sellerMemoString) {
        this.sellerMemoString = sellerMemoString;
    }

    public String getShopShortTitle() {
        return shopShortTitle;
    }

    public void setShopShortTitle(String shopShortTitle) {
        this.shopShortTitle = shopShortTitle;
    }

    public Set<String> getCatIds() {
        return catIds;
    }

    public void setCatIds(Set<String> catIds) {
        this.catIds = catIds;
    }

    public Set<Long> getItemTagIds() {
        return itemTagIds;
    }

    public void setItemTagIds(Set<Long> itemTagIds) {
        this.itemTagIds = itemTagIds;
    }

    /**
     * 是否天猫平台预售订单
     *
     * @return
     */
    public boolean isTmallOrTaoBaoPlatformPresell() {
        return (CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(getSource()) || CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(getSource()))
                && CommonConstants.SHOP_PRESELL_TAG.equals(getType());
    }

    public String getOldSysOuterId() {
        return oldSysOuterId;
    }

    public void setOldSysOuterId(String oldSysOuterId) {
        this.oldSysOuterId = oldSysOuterId;
    }

    public String getDecryptCode() {
        return decryptCode;
    }

    public void setDecryptCode(String decryptCode) {
        this.decryptCode = decryptCode;
    }

    /**
     * 是否天猫物流升级并指定物流的订单
     * @see PlatformUtils#isTmallDesignatedLogistics(Trade)
     */
    @Deprecated
    public boolean isTmallDesignatedLogistics() {
        return PlatformUtils.isTmallDesignatedLogistics(this);
    }

    public Boolean getSplitUp() {
        return splitUp;
    }

    public void setSplitUp(Boolean splitUp) {
        this.splitUp = splitUp;
    }

    /**
     * 是否天猫物流升级订单
     * @see PlatformUtils#isTmallLogisticsUpgrade(Trade)
     */
    @Deprecated
    public boolean isTmallLogisticsUpgrade() {
        return PlatformUtils.isTmallLogisticsUpgrade(this);
    }

    public boolean isTmallAsdpAds(TmallAsdpAdsTypeEnum asdpAdsTypeEnum) {
        if (asdpAdsTypeEnum == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        if (!(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(getSource())
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(getSource()))) {
            return false;
        }
        if (Objects.isNull(tradeExt)) {
            return false;
        }
        return asdpAdsTypeEnum.equals(TmallAsdpAdsTypeEnum.getAsdpAdsByCode(tradeExt.getTmallAsdpAds()));
    }

    /**
     * 是否天猫物流升级并指定仓库的订单
     * @see PlatformUtils#isTmallDesignatedStoreName(Trade)
     */
    public boolean isTmallDesignatedStoreName() {
        return PlatformUtils.isTmallDesignatedStoreName(this);
    }

    public Long getItemSysId() {
        return itemSysId;
    }

    public void setItemSysId(Long itemSysId) {
        this.itemSysId = itemSysId;
    }

    public Long getTemplateMatchRuleId() {
        return templateMatchRuleId;
    }

    public void setTemplateMatchRuleId(Long templateMatchRuleId) {
        this.templateMatchRuleId = templateMatchRuleId;
    }

    public List<Long> getCloseSids() {
        return closeSids;
    }

    public void setCloseSids(List<Long> closeSids) {
        this.closeSids = closeSids;
    }

    //淘宝、天猫店铺预售，订单更新时需要用到，根据该字段判断是否店铺预售订单
    private boolean isExistEstimateConTime;

    public boolean isExistEstimateConTime() {
        return isExistEstimateConTime;
    }

    public void setExistEstimateConTime(boolean existEstimateConTime) {
        isExistEstimateConTime = existEstimateConTime;
    }

    public Integer getIsAutoReUpload() {
        return isAutoReUpload;
    }

    public void setIsAutoReUpload(Integer isAutoReUpload) {
        this.isAutoReUpload = isAutoReUpload;
    }

    public Boolean getNeedRemoveAllocateGoods() {
        return needRemoveAllocateGoods;
    }

    public void setNeedRemoveAllocateGoods(Boolean needRemoveAllocateGoods) {
        this.needRemoveAllocateGoods = needRemoveAllocateGoods;
    }

    public String getUploadErrorTypeDesc() {
        return uploadErrorTypeDesc;
    }

    public void setUploadErrorTypeDesc(String uploadErrorTypeDesc) {
        this.uploadErrorTypeDesc = uploadErrorTypeDesc;
    }

    public String getSheinOriginalJson() {
        return sheinOriginalJson;
    }

    public void setSheinOriginalJson(String sheinOriginalJson) {
        this.sheinOriginalJson = sheinOriginalJson;
    }

    /**
     * 平台原始订单信息
     */
    private Object platfromTrade;

    public Object getPlatfromTrade() {
        return platfromTrade;
    }

    public void setPlatfromTrade(Object platfromTrade) {
        this.platfromTrade = platfromTrade;
    }

    public String getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(String itemInfo) {
        this.itemInfo = itemInfo;
    }

    public String getPlatformSellerMemo() {
        return platformSellerMemo;
    }

    public void setPlatformSellerMemo(String platformSellerMemo) {
        this.platformSellerMemo = platformSellerMemo;
    }

    public Long getPlatformFlag() {
        return platformFlag;
    }

    public void setPlatformFlag(Long platformFlag) {
        this.platformFlag = platformFlag;
    }

    public String getRegisterError() {
        return registerError;
    }

    public void setRegisterError(String registerError) {
        this.registerError = registerError;
    }

    public Boolean isFinishNoCheckPack() {
        return finishNoCheckPack;
    }

    public void setFinishNoCheckPack(Boolean finishNoCheckPack) {
        this.finishNoCheckPack = finishNoCheckPack;
    }

    public String getFinishNoCheckPackErrorMsg() {
        return finishNoCheckPackErrorMsg;
    }

    public void setFinishNoCheckPackErrorMsg(String finishNoCheckPackErrorMsg) {
        this.finishNoCheckPackErrorMsg = finishNoCheckPackErrorMsg;
    }

    public String getOriReceiverMobile() {
        return oriReceiverMobile;
    }

    public void setOriReceiverMobile(String oriReceiverMobile) {
        this.oriReceiverMobile = oriReceiverMobile;
    }

    public String getOriReceiverName() {
        return oriReceiverName;
    }

    public void setOriReceiverName(String oriReceiverName) {
        this.oriReceiverName = oriReceiverName;
    }

    public String getOriReceiverAddress() {
        return oriReceiverAddress;
    }

    public void setOriReceiverAddress(String oriReceiverAddress) {
        this.oriReceiverAddress = oriReceiverAddress;
    }

    /**
     * 智能计算订单单品数量
     */
    private Integer itemQuantity = 0;

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }


    public List<Order> getItemInfoList() {
        return itemInfoList;
    }

    public void setItemInfoList(List<Order> itemInfoList) {
        this.itemInfoList = itemInfoList;
    }

    public ReplenishTrades getBindReplenish() {
        return bindReplenish;
    }

    public void setBindReplenish(ReplenishTrades bindReplenish) {
        this.bindReplenish = bindReplenish;
    }

    public Integer getNotIsVirtualItemNum() {
        return notIsVirtualItemNum;
    }

    public void setNotIsVirtualItemNum(Integer notIsVirtualItemNum) {
        this.notIsVirtualItemNum = notIsVirtualItemNum;
    }

    public Integer getRemoveUnattainable() {
        return removeUnattainable;
    }

    public void setRemoveUnattainable(Integer removeUnattainable) {
        this.removeUnattainable = removeUnattainable;
    }

    public String getExpressMatchStrategy() {
        return expressMatchStrategy;
    }

    public void setExpressMatchStrategy(String expressMatchStrategy) {
        this.expressMatchStrategy = expressMatchStrategy;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public PlatformRecommendation getPlatformRecommendation() {
        return platformRecommendation;
    }

    public void setPlatformRecommendation(PlatformRecommendation platformRecommendation) {
        this.platformRecommendation = platformRecommendation;
    }


    /**
     * 强制更新卖家旗帜
     */
    private boolean ifForceUpdateSellerFlag;

    public boolean isIfForceUpdateSellerFlag() {
        return ifForceUpdateSellerFlag;
    }

    public void setIfForceUpdateSellerFlag(boolean ifForceUpdateSellerFlag) {
        this.ifForceUpdateSellerFlag = ifForceUpdateSellerFlag;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public LinkedHashMap<String, String> getNotInWaveSpecificReasonMap() {
        return notInWaveSpecificReasonMap;
    }

    public void setNotInWaveSpecificReasonMap(LinkedHashMap<String, String> notInWaveSpecificReasonMap) {
        this.notInWaveSpecificReasonMap = notInWaveSpecificReasonMap;
    }

    public List<Pair<String, Integer>> getSysOutId2NumList() {
        return sysOutId2NumList;
    }

    public void setSysOutId2NumList(List<Pair<String, Integer>> sysOutId2NumList) {
        this.sysOutId2NumList = sysOutId2NumList;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public Map<String, PlatformRecommendation> getTidToPr() {
        return tidToPr;
    }

    public void setTidToPr(Map<String, PlatformRecommendation> tidToPr) {
        this.tidToPr = tidToPr;
    }

    public boolean getForceUpdateAddress() {
        return forceUpdateAddress;
    }

    public void setForceUpdateAddress(boolean forceUpdateAddress) {
        this.forceUpdateAddress = forceUpdateAddress;
    }

    public Set<Long> getSkuSellerCids() {
        return skuSellerCids;
    }

    public void setSkuSellerCids(Set<Long> skuSellerCids) {
        this.skuSellerCids = skuSellerCids;
    }

    public Set<Long> getSkuSuitSellerCids() {
        return skuSuitSellerCids;
    }

    public void setSkuSuitSellerCids(Set<Long> skuSuitSellerCids) {
        this.skuSuitSellerCids = skuSuitSellerCids;
    }

    public WaveAssembleInfo getWaveAssembleInfo() {
        return waveAssembleInfo;
    }

    public void setWaveAssembleInfo(WaveAssembleInfo waveAssembleInfo) {
        this.waveAssembleInfo = waveAssembleInfo;
    }

    public Boolean getModifyTemplate() {
        return modifyTemplate;
    }

    public void setModifyTemplate(Boolean modifyTemplate) {
        this.modifyTemplate = modifyTemplate;
    }

    public Long getOldTemplateId() {
        return oldTemplateId;
    }

    public void setOldTemplateId(Long oldTemplateId) {
        this.oldTemplateId = oldTemplateId;
    }

    public Integer getOldTemplateType() {
        return oldTemplateType;
    }

    public void setOldTemplateType(Integer oldTemplateType) {
        this.oldTemplateType = oldTemplateType;
    }

    public Long getOldLogisticsCompanyId() {
        return oldLogisticsCompanyId;
    }

    public void setOldLogisticsCompanyId(Long oldLogisticsCompanyId) {
        this.oldLogisticsCompanyId = oldLogisticsCompanyId;
    }

    public String getOldAddressMd5() {
        return oldAddressMd5;
    }

    public void setOldAddressMd5(String oldAddressMd5) {
        this.oldAddressMd5 = oldAddressMd5;
    }

    public String getSkuNameInfo() {
        return skuNameInfo;
    }

    public void setSkuNameInfo(String skuNameInfo) {
        this.skuNameInfo = skuNameInfo;
    }

    public String getQimenShopShortName() {
        return qimenShopShortName;
    }

    public void setQimenShopShortName(String qimenShopShortName) {
        this.qimenShopShortName = qimenShopShortName;
    }

    /**
     * 奇门子店铺简称 不持久化 区别:shopShortTitle
     */
    String qimenShopShortName;

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }


    public Double getTradeWeight() {
        return tradeWeight;
    }

    public void setTradeWeight(Double tradeWeight) {
        this.tradeWeight = tradeWeight;
    }

    /**
     * 是否由前端传参来修改订单商品供应商，不持久化，前端传参
     */
    private Boolean saveOrderSupplier;

    public Boolean getSaveOrderSupplier() {
        return saveOrderSupplier;
    }

    public void setSaveOrderSupplier(Boolean saveOrderSupplier) {
        this.saveOrderSupplier = saveOrderSupplier;
    }

    public String getActualVolume() {
        return actualVolume;
    }

    public void setActualVolume(String actualVolume) {
        this.actualVolume = actualVolume;
    }

    public String getActualLengthWidthAndHeight() {
        return actualLengthWidthAndHeight;
    }

    public void setActualLengthWidthAndHeight(String actualLengthWidthAndHeight) {
        this.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
    }

    /**
     * 新增商品是否需要重新执行商品搭配规则，不持久化
     */
    private Boolean rematchItemReplace;

    public Boolean getRematchItemReplace() {
        return rematchItemReplace;
    }

    public void setRematchItemReplace(Boolean rematchItemReplace) {
        this.rematchItemReplace = rematchItemReplace;
    }

    public Long getLastItemSysId() {
        return lastItemSysId;
    }

    public void setLastItemSysId(Long lastItemSysId) {
        this.lastItemSysId = lastItemSysId;
    }

}

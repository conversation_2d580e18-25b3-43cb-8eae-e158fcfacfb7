package com.raycloud.dmj.domain.trades;

import java.io.Serializable;

public class UserDataConfigDTO implements Serializable {

    /**
     * 数据项编码
     */
    private String field;

    /**
     * 数据项显示标题
     */
    private String title;

    /**
     * 数据项ID
     */
    private Long dataId;

    /**
     * 是否可见: 1可见, 0不可见
     */
    private Integer visible;

    /**
     * 数据项在当前页面的排序号
     */
    private Integer sortNo;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }
}

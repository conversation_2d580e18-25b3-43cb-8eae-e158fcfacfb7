package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.trade.history.OrderHistory;
import com.raycloud.dmj.domain.trade.order.OrderCaigou;
import com.raycloud.dmj.domain.trades.payment.OrderPayment;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.domain.OrderExcept;
import com.raycloud.dmj.stock.oms.OmsStockRecord;
import lombok.*;

import java.util.*;

/**
 * @Description 子订单模型的聚合（重构过程中的中间类）
 * @Date 2022/2/21 14:27
 * @Created 杨恒
 */
@Setter
@Getter
public abstract class OrderAggregation extends OrderBase {

    private static final long serialVersionUID = -5028698066274854062L;

    //************************************************* OrderPayment start *************************************************
    /**
     * 子订单金额模型
     */
    private OrderPayment orderPayment;

    private String payment;

    private String originPayment;

    private Double paymentDouble;

    private String acPayment;

    private String oldAcPayment;

    private Double cost;

    private String price;

    private String oldPrice;

    private String totalFee;

    private String discountFee;

    private Double discountRate;

    private String adjustFee;

    private String saleFee;

    private String salePrice;

    private String payAmount;

    private String platformDiscountFee;

    //************************************************* OrderPayment end *************************************************

    //************************************************* OrderExcept start *************************************************

   // private Long v;
   /* private Integer itemChanged;
    private Integer relationChanged;
    private String stockStatus;
    private String oldStockStatus;*/

    //************************************************* OrderExcept end *************************************************
    /**
     *  order 的exceptData 对像与trade的是同一个对像
     */
    @JSONField(serialize = false)
    private ExceptData exceptData;


    @JSONField(serialize = false)
    private Map<Long, ExceptData> subTradeExceptDatas;

    /**
     * com.raycloud.dmj.business.sync.ItemChangeBusiness#checkItemChange
     * 订单同步时，判断商品是否改动标识，不持久化数据库,默认是不需要
     */
    private boolean needMarkItemChanged=false;

    private boolean syncItemChanged=false;
    /**
     * com.raycloud.dmj.domain.trades.utils.OrderCopier 时 exceptData 对象是否需要创建新的对象
     * false 复制新的ExceptData对象,true 保存老的对象 默认是true
     */
    private boolean keepOldExceptData=true;
    /**
     * 临时保存order的相关异常，新增的订单
     */
    private Set<Long> orderExceptIds;
    /***
     * 平台运单号,对应其他erp发货的商品的运单号
     *  需要同步设置 orderPlatOutSidSupportSources  对应的source
     */
    private Set<String> platOutSids;


    /**
     * Order操作记录
     */
    private Map<Integer, OrderModifyLog> modifyTypeLogMap;

    private OrderCaigou orderCaigou;

    private OmsStockRecord omsStockRecord;

    private OrderHistory orderHistory;

    /**
     * 订单商品异常
     */
    private List<OrderExcept> orderExcepts;


}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;

import java.util.ArrayList;
import java.util.Collections;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UserUtils {

    /**
     * 初始化一个较为轻量级的user
     *  TODO 浅拷贝，只处理了user 和staff 其他未处理
     *  user中staff的users和userMap只存在当前user
     * @param origin
     * @return
     */
    public static User initSingleUser(User origin) {
        User dest = copyUser(origin);
        Staff staff = copyStaff(origin.getStaff());
        staff.setUsers(new ArrayList<>(Collections.singletonList(dest)));
        staff.setUserIdMap(staff.getUsers().stream().collect(Collectors.toMap(User::getId, Function.identity(), (o, n) -> n)));
        dest.setStaff(staff);
        return dest;
    }

    public static User copyUser(User origin) {
        User dest = new User();
        dest.setActive(origin.getActive());
        dest.setAssociationId(origin.getAssociationId());
        dest.setAuthIdentify(origin.getAuthIdentify());
        dest.setAutoDownCopy(origin.getAutoDownCopy());
        dest.setBindShops(origin.getBindShops());
        dest.setBindTime(origin.getBindTime());
//        dest.setBindUsers(origin.getBindUsers());
        dest.setBuyType(origin.getBuyType());
        dest.setCompanyId(origin.getCompanyId());
        dest.setConf(origin.getConf());
        dest.setCreated(origin.getCreated());
        dest.setDeadline(origin.getDeadline());
        dest.setEnableStatus(origin.getEnableStatus());
        dest.setId(origin.getId());
        dest.setInvalidSession(origin.getInvalidSession());
        dest.setIsBarCode(origin.getIsBarCode());
        dest.setIsManualTaobaoId(origin.getIsManualTaobaoId());
        dest.setItemCodeType(origin.getItemCodeType());
        dest.setItemSpecial(origin.getItemSpecial());
        dest.setLastAuthTime(origin.getLastAuthTime());
        dest.setLastImportItemTime(origin.getLastImportItemTime());
        dest.setLastImportTradeTime(origin.getLastImportTradeTime());
        dest.setLastLoginDate(origin.getLastLoginDate());
        dest.setLevel(origin.getLevel());
        dest.setLogisticsOuterId(origin.getLogisticsOuterId());
        dest.setMasterUser(origin.getMasterUser());
        dest.setMasterUserData(origin.getMasterUserData());
        dest.setModified(origin.getModified());
        dest.setNewSync(origin.getNewSync());
        dest.setNick(origin.getNick());
        dest.setQiMenShops(origin.getQiMenShops());
        dest.setSellerLevel(origin.getSellerLevel());
        dest.setSessionId(origin.getSessionId());
        dest.setSid(origin.getSid());
        dest.setSlaveUserData(origin.getSlaveUserData());
        dest.setSource(origin.getSource());

        dest.setSubSource(origin.getSubSource());
        dest.setSubStockType(origin.getSubStockType());
        dest.setSubUser(origin.getSubUser());
        dest.setTaobaoId(origin.getTaobaoId());
        dest.setThirdPlatformId(origin.getThirdPlatformId());
        dest.setTradeSyncStatus(origin.getTradeSyncStatus());
        dest.setTwdaoClientId(origin.getTwdaoClientId());
        dest.setTwdaoToken(origin.getTwdaoToken());
        dest.setUrlParams(origin.getUrlParams());
        dest.setUseXingpan(origin.getUseXingpan());
        dest.setUserConf(origin.getUserConf());
        dest.setWarehouseCode(origin.getWarehouseCode());
        return dest;

    }

    private static Staff copyStaff(Staff origin) {
        Staff dest = new Staff();
        dest.setAccountId(origin.getAccountId());
        dest.setAccountName(origin.getAccountName());
        dest.setAddress(origin.getAddress());
        dest.setAllowLogin(origin.getAllowLogin());
        dest.setAllowPlatformLogin(origin.getAllowPlatformLogin());
        dest.setBirthday(origin.getBirthday());
        dest.setBrandGroup(origin.getBrandGroup());
        dest.setChildrenList(origin.getChildrenList());
        dest.setClueId(origin.getClueId());
        dest.setCompany(origin.getCompany());
        dest.setCompanyId(origin.getCompanyId());
        dest.setCompanyName(origin.getCompanyName());
        dest.setConfig(origin.getConfig());
        dest.setCreated(origin.getCreated());
        dest.setCustomerGroup(origin.getCustomerGroup());
        dest.setDataPrivilegeSetting(origin.getDataPrivilegeSetting());
        dest.setDegrees(origin.getDegrees());
        dest.setDepartment(origin.getDepartment());
        dest.setDepth(origin.getDepth());
        dest.setDistributorGroup(origin.getDistributorGroup());
        dest.setEmail(origin.getEmail());
        dest.setEnableStatus(origin.getEnableStatus());
        dest.setEncodePhone(origin.getEncodePhone());
        dest.setEnvStatus(origin.getEnvStatus());
        dest.setExtraPrivilegeSettings(origin.getExtraPrivilegeSettings());
        dest.setFollowErpShopPrivileges(origin.getFollowErpShopPrivileges());
        dest.setId(origin.getId());
        dest.setIsGray(origin.getIsGray());
        dest.setIsSingleLogin(origin.getIsSingleLogin());
        dest.setIsUpdatePwd(origin.getIsUpdatePwd());
        dest.setIsVague(origin.getIsVague());
        dest.setItemClassifyGroup(origin.getItemClassifyGroup());
        dest.setJob(origin.getJob());
        dest.setJobNum(origin.getJobNum());
        dest.setLastLoginDate(origin.getLastLoginDate());
        dest.setMenus(origin.getMenus());
        dest.setModified(origin.getModified());
        dest.setName(origin.getName());
        dest.setOrderId(origin.getOrderId());
        dest.setParentId(origin.getParentId());
        dest.setPhone(origin.getPhone());
        dest.setPhoneWithStar(origin.getPhoneWithStar());
        dest.setPowerDataPrivilegeSettings(origin.getPowerDataPrivilegeSettings());
        dest.setPrivilegeSetting(origin.getPrivilegeSetting());
        dest.setQq(origin.getQq());
        dest.setRegularEmployeeType(origin.getRegularEmployeeType());
        dest.setRemark(origin.getRemark());
        dest.setSex(origin.getSex());
        dest.setShadowToken(origin.getShadowToken());
        dest.setSource(origin.getSource());
        dest.setStaffConf(origin.getStaffConf());
        dest.setStaffGroup(origin.getStaffGroup());
        dest.setSupplierCategory(origin.getSupplierCategory());
        dest.setSupplierGroup(origin.getSupplierGroup());
        dest.setSupplyPrivilegeSetting(origin.getSupplyPrivilegeSetting());
        dest.setThinkTankShopPrivilege(origin.getThinkTankShopPrivilege());
        dest.setTwdaoClientId(origin.getTwdaoClientId());
        dest.setTwdaoToken(origin.getTwdaoToken());
        dest.setUseSupplyChain(origin.getUseSupplyChain());
        dest.setUserGroup(origin.getUserGroup());
//        dest.setUserIdMap(origin.getUserIdMap());
        dest.setUserRoleBrandGroupPrivilegeSettings(origin.getUserRoleBrandGroupPrivilegeSettings());
        dest.setUserRoleCustomerGroupPrivilegeSettings(origin.getUserRoleCustomerGroupPrivilegeSettings());
        dest.setUserRoleDataPrivilegeSettings(origin.getUserRoleDataPrivilegeSettings());
        dest.setUserRoleDistributorGroupPrivilegeSettings(origin.getUserRoleDistributorGroupPrivilegeSettings());
        dest.setUserRoleFollowErpShopPrivileges(origin.getUserRoleFollowErpShopPrivileges());
        dest.setUserRoleItemClassifyGroupPrivilegeSettings(origin.getUserRoleItemClassifyGroupPrivilegeSettings());
        dest.setUserRoleList(origin.getUserRoleList());
        dest.setUserRolePrivilegeSettings(origin.getUserRolePrivilegeSettings());
        dest.setUserRoleShopPrivilegeSettings(origin.getUserRoleShopPrivilegeSettings());
        dest.setUserRoleStaffPrivilegeSettings(origin.getUserRoleStaffPrivilegeSettings());
        dest.setUserRoleSupplierCategoryPrivilegeSettings(origin.getUserRoleSupplierCategoryPrivilegeSettings());
        dest.setUserRoleSupplierPrivilegeSettings(origin.getUserRoleSupplierPrivilegeSettings());
        dest.setUserRoleSupplyPrivilegeSetting(origin.getUserRoleSupplyPrivilegeSetting());
        dest.setUserRoleThinkTankShopPrivilege(origin.getUserRoleThinkTankShopPrivilege());
        dest.setUserRoleVirtualWarehousePrivilegeSettings(origin.getUserRoleVirtualWarehousePrivilegeSettings());
        dest.setUserRoleWarehousePrivilegeSettings(origin.getUserRoleWarehousePrivilegeSettings());
        dest.setUserRoleWaveGroupPrivilegeSettings(origin.getUserRoleWaveGroupPrivilegeSettings());
//        dest.setUsers(origin.getUsers());
        dest.setVirtualWarehouse(origin.getVirtualWarehouse());
        dest.setWarehouseExtraGroup(origin.getWarehouseExtraGroup());
        dest.setWarehouseGroup(origin.getWarehouseGroup());
        dest.setWarehouseStoreGroup(origin.getWarehouseStoreGroup());
        dest.setWaveGroup(origin.getWaveGroup());
        return dest;
    }
}
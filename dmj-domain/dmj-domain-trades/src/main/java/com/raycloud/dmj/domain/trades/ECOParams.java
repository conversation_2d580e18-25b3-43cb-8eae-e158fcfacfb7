package com.raycloud.dmj.domain.trades;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/1
 * @description
 */
public class ECOParams implements Serializable {

    private static final long serialVersionUID = 6455680972134126831L;

    private Long userId;

    private Boolean isTopToken;

    private String secToken;

    private Boolean isGx;

    private String tid;

    public void verify() {
        if (userId == null) {
            throw new IllegalArgumentException("用户不能为空");
        }
        if (isTopToken != null && isTopToken) {
            if (StringUtils.isEmpty(secToken)) {
                throw new IllegalArgumentException("参数校验失败");
            }
        } else {
            isTopToken = false;
        }
        if (isGx != null && isGx) {
            // 针对订单解密的时候，需要校验是否传入tid
            if (isTopToken && StringUtils.isEmpty(tid)) {
                throw new IllegalArgumentException("供销订单需要传入平台单号");
            }
        } else {
            isGx = false;
        }
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Boolean getTopToken() {
        return isTopToken;
    }

    public void setIsTopToken(Boolean topToken) {
        isTopToken = topToken;
    }

    public String getSecToken() {
        return secToken;
    }

    public void setSecToken(String secToken) {
        this.secToken = secToken;
    }

    public Boolean getGx() {
        return isGx;
    }

    public void setIsGx(Boolean gx) {
        isGx = gx;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}

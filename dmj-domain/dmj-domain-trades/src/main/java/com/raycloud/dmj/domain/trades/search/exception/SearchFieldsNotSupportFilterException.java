package com.raycloud.dmj.domain.trades.search.exception;

/**
 * <AUTHOR>
 *
 * 查询字段不支持过滤
 */
public class SearchFieldsNotSupportFilterException extends RuntimeException{

    public SearchFieldsNotSupportFilterException(Long queryId,String ...fields) {
        super(String.format("当前queryId：%s,所需要的过滤字段不存在: %s",queryId,fields !=null?String.join(",",fields):""));
    }

    public SearchFieldsNotSupportFilterException(Throwable cause) {
        super(cause);
    }
}
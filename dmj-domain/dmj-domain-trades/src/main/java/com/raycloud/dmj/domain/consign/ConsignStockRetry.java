package com.raycloud.dmj.domain.consign;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * 发货时库存消费或归还出错，订单临时记录表
 * <AUTHOR>
 * @version 1.0
 * @date 2017-08-24 18:35
 */
@Table(name = "consign_stock_retry")
public class ConsignStockRetry extends Model {

    private static final long serialVersionUID = -5324447436525344493L;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 重试次数
     */
    private Integer num;


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

}

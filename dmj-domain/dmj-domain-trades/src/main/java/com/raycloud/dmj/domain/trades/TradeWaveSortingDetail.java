package com.raycloud.dmj.domain.trades;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @description: 播种信息，交易使用的WaveSortingDetail
 * @author: chunri
 * @create: 2022-08-09 14:53
 **/
@Getter
@Setter
public class TradeWaveSortingDetail {

    private Long id;

    private Long companyId;

    /**
     * 分拣Id
     */
    private Long sortingId;

    private Long waveId;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * order类型：0单品 1套件
     */
    private Integer suitType;

    /**
     * 如果是套件单品,则该字段的值为其所属的套件子订单的id,否则为0
     */
    private Long combineId;

    private String title;

    private String shortTitle;

    private String propertiesName;

    private String propertiesAlias;

    private String picPath;

    private Long orderId;

    private Long sysItemId;

    private Long sysSkuId;

    /**
     * 商品数量
     */
    private Integer itemNum;

    /**
     * 分拣明细所在订单的该商品总数（合单或套件中某种商品的明细会有多条）
     */
    private Integer totalNum;

    /**
     * 已匹配数量
     */
    private Integer matchedNum;

    /**
     * 已拣数量
     */
    private Integer pickedNum;

    private Integer stockNum;

    /**
     * 缺货数
     */
    private Integer shortageNum;

    /**
     * 匹配状态， 0：未匹配   1：匹配中   2：匹配完成
     */
    private Integer matchedStatus;

    /**
     * 箱规json
     */
    private String itemBoxJson;

    private Long sid;

    /**
     * 订单匹配 true表示匹配完成
     */
    private Boolean tradeMatched;

    private Integer printStatus;

    private Long positionNo;

    private Integer enableStatus;

    private String goodsSectionStr;

    private Date modified;

    private Boolean qualityType;

    /**
     * 变更的播种数
     */
    private Integer changeMatchedNum;

    /**
     * 拣选员ids，同种商品可能是多个拣选员
     */
    private String pickerIds;

    /**
     * 拣选员名称
     */
    private String pickerNames;


    /**
     * 播种结束时间
     */
    private Date seedEndTime;


    /**
     * 播种时间列表
     */
    private List<Date> seedTimeList;

    /**
     * 唯一码
     */
    private String uniqueCode;

    /**
     * @see Order#getIsPick()
     * 0：不拣选不验货
     * 1：拣选不验货
     * 2：拣选验货
     * 3：不拣选验货
     */
    private Integer giftPickAndCheck;

    /**
     * 商品条形码
     */
    private String multiCode;

    private Long sourceOrderId;

    /**
     * 不播种原因，默认0：参与播种； 大于0：不参与播种
     * 1：退款商品
     */
    private Integer notSeedReason;

    /**
     * 质检员
     */
    private String checker;

    /**
     * @see Order#getPicPath()
     */
    private String platformPicPath;

    /**
     * 异常信息
     */
    private String exceptionMsg;

    private String remark;

    /**
     * 主商家编码
     */
    private String mainOuterId;


    /**
     * 已打印订单商品数量
     */
    private Integer printItemNum;
    /**
     * 商品销售价
     */
    private Double itemPrice;


}

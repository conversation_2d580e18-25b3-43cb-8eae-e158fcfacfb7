package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.trades.spel.OverseasLogisticRuleSpelCondition;
import com.raycloud.erp.db.model.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 境外物流规则
 * <AUTHOR>
 */
@Table(name = "overseas_logistic_rule")
@Data
public class OverseasLogisticRule implements Serializable {
    private static final long serialVersionUID = 6459712859250898565L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 优先级 数字越小，优先级越高
     */
    private Integer priority;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 公司物流商id
     */
    private Long providerId;

    /**
     * 渠道模版id
     */
    private Long templateId;

    /**
     * 渠道模版名称，冗余字段，用于模糊搜索
     */
    private String templateName;

    /**
     * 规则条件，以json数组字符串格式持久化
     */
    private String conditionsDesc;

    /**
     * 开启状态：0-关闭，1-启用
     */
    private Integer activeStatus;

    /**
     * 存在状态：0-删除，1-开启
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date updated;

    /**
     * 规则条件，不持久化
     */
    private List<OverseasLogisticRuleSpelCondition> conditions;

    /**
     * 规则内容，不持久化
     */
    private String conditionContent;

    private Long logId;
}

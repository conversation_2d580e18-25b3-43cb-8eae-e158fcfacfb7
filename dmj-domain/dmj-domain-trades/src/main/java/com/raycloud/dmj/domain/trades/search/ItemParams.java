package com.raycloud.dmj.domain.trades.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>(杨保平)
 * @Date: 2022/6/29 5:55 下午
 * @Version 1.0
 * @Email <EMAIL>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemParams<T> {

    /**
     * num_iid 平台商品ID
     * sku_id 平台商品规格
     */
    private String field;
    private String name;
    private List<T> values;
}

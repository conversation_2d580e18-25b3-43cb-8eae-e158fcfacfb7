package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * 订单类型 TradeTypeNew 查询参数
 * 对应TradeTypeNewEnum
 */
public class TradeTypeNewParams implements Serializable {

    private static final long serialVersionUID = 1105349491391218972L;
    /**
     * 包含
     */
    private List<Long> containTypeIds;

    /**
     * 排除
     */
    private List<Long> notContainTypeIds;

    /**
     * constructor
     */
    public TradeTypeNewParams(List<Long> containTypeIds, List<Long> notContainTypeIds) {
        this.containTypeIds = containTypeIds;
        this.notContainTypeIds = notContainTypeIds;
    }

    public List<Long> getContainTypeIds() {
        return containTypeIds;
    }

    public void setContainTypeIds(List<Long> containTypeIds) {
        this.containTypeIds = containTypeIds;
    }

    public List<Long> getNotContainTypeIds() {
        return notContainTypeIds;
    }

    public void setNotContainTypeIds(List<Long> notContainTypeIds) {
        this.notContainTypeIds = notContainTypeIds;
    }

}

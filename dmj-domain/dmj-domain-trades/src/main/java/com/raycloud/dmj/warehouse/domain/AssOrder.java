package com.raycloud.dmj.warehouse.domain;

import com.raycloud.dmj.warehouse.vo.BaseResult;

import java.util.Date;

/**
 * 单据关联表
 * Created by guzy on 16/8/29.
 */
public class AssOrder extends BaseResult implements Cloneable {

    private static final long serialVersionUID = -8887065283988645847L;

    private Long id;

    private Long warehouseId;

    /**
     * erp中的单据id，分别对应订单sid、采购单id、销退入库单id
     */
    private Long sysId;


    private Long companyId;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 第三方id
     */
    private String party3Id;

    /**
     * 分表号
     */
    private Integer dbNo;

    /**
     * 日志内容记录
     */
    private String content;

    private String customerId;

    private Date created;

    private Date modified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Override
    public String getParty3Id() {
        return party3Id;
    }

    @Override
    public void setParty3Id(String party3Id) {
        this.party3Id = party3Id;
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public AssOrder clone() throws CloneNotSupportedException {
        return (AssOrder) super.clone();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    @Override
    public String toString() {
        return "AssOrder{" +
                "id=" + id +
                ", warehouseId=" + warehouseId +
                ", sysId=" + sysId +
                ", companyId=" + companyId +
                ", orderType='" + orderType + '\'' +
                ", party3Id='" + party3Id + '\'' +
                ", customerId='" + customerId + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}

package com.raycloud.dmj.domain.logistics.response;

import com.raycloud.dmj.domain.trades.TradeLogisticsTrace;

import java.util.List;

/**
 * @Date 2021/12/11
 * <AUTHOR>
 */
public class QueryTraceLogisticsResponse extends BaseLogisticsResponse {

    private List<TradeLogisticsTrace> data;

    public List<TradeLogisticsTrace> getData() {
        return data;
    }

    public void setData(List<TradeLogisticsTrace> data) {
        this.data = data;
    }

}

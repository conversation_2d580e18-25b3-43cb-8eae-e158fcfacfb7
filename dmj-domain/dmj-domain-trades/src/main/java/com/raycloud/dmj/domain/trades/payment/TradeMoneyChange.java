package com.raycloud.dmj.domain.trades.payment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TradeMoneyChange {
    private Long companyId;
    private Long sid;
    private String tid;
    private Date created;
    private String payment;
    private Double cost;
    private String discountFee;
    private String totalFee;
    private String salePrice;
    private String saleFee;
    private List<OrderMoneyChange> orderMoneyChangeList = new ArrayList<>();

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(String salePrice) {
        this.salePrice = salePrice;
    }

    public String getSaleFee() {
        return saleFee;
    }

    public void setSaleFee(String saleFee) {
        this.saleFee = saleFee;
    }

    public List<OrderMoneyChange> getOrderMoneyChangeList() {
        return orderMoneyChangeList;
    }

    public void setOrderMoneyChangeList(List<OrderMoneyChange> orderMoneyChangeList) {
        this.orderMoneyChangeList = orderMoneyChangeList;
    }
}

package com.raycloud.dmj.domain.trades.payment;

import com.raycloud.dmj.domain.trades.OrderBase;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description OrderPayment
 * @Date 2022/2/21 14:23
 * @Created 杨恒
 */
@Table(name = "order_payment", routerKey = "orderDbNo")
@Setter
@Getter
public class OrderPayment extends OrderBase {

    private static final long serialVersionUID = 7702843211727281642L;

    /**
     * 实付金额, 公式: 实付金额 = 应付金额 - 优惠金额
     */
    private String payment;

    /**
     * 不变的实付金额, 进来后不变
     */
    private String acPayment;

    /**
     * 商品成本价 cost*num 该商品总成本价
     */
    private Double cost;

    /**
     * 商品销售价
     */
    private String price;

    /**
     * 应付金额, 公式: 应付金额 = 商品数量 * 商品单价 + 手工调整价
     */
    private String totalFee;

    /**
     * 优惠金额
     */
    private String discountFee;

    /**
     * 折扣率
     */
    private Double discountRate;

    /**
     * 手工调整金额
     */
    private String adjustFee;

    /**
     * 分销金额
     */
    private String saleFee;
    /**
     * 分销价格
     */
    private String salePrice;

    /**
     * 实付金额
     */
    private Double payAmount;

}

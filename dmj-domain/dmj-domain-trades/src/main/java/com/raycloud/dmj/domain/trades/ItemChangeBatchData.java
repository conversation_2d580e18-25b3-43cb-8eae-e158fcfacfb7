package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.LinkedListMultimap;
import com.raycloud.dmj.domain.account.Staff;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.payment.TradeItemExchangeRel;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.utils.OrderBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trade.item.ItemReplaceBaseData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;

public class ItemChangeBatchData extends ItemReplaceBaseData {


    public List<TradeTag> tags;
    /**
     * 换商品数据--修改前
     */
    public Order orderFront;

    /**
     * 是否更新数量
     */
    public Integer updateNum;

    /**
     * 是否更新金额
     */
    public Integer updatePayment;

    /**
     * 是否更新平台实付
     */
    public Integer updateDivideOrderFee;

    /**
     * 是否是快卖通商品绑定
     */
    public Integer KMFBind;

    /**
     * 是否供销修改商品
     */
    public Integer gxChangeItem;

    /**
     * 要更换商品的子订单--修改后商品
     */
    public List<Order> changeItemOrders = new ArrayList<>();

    /**
     * 新增的子订单，换成套件的时候，套件下的单品需要新增
     */
    public List<Order> insertOrders = new ArrayList<>();

    /**
     * 修改的子订单，包括套件被换的时候需要删除下面的单品
     */
    public List<Order> updateOrders = new ArrayList<>();

    /**
     * 修改订单
     */
    public List<Trade> updateTrades = new ArrayList<>();

    /**
     * 合单的订单
     */
    public List<Trade> mergeTrades = new ArrayList<>();
    /**
     * 成功的sid,key-sid,value-错误信息
     */
    public List<Long> successSids = new ArrayList<>();
    /**
     * 失败的订单key-sid,value-错误信息
     */
    public Map<String, String> errorSids = new HashMap<>();

    /**
     * 批量修改商品--修改后的商品
     */
    public List<Order> batchChangeItemOrder = new ArrayList<>();

    /**
     * 批量替换商品入参
     */
    public TradeBatchChangeItemParams itemParams;

    /**
     * 金额重算映射
     */
    public Map<Long, List<TradeItemExchangeRel>> exchangeRelMap = Maps.newHashMap();
    /**
     * 金额重算后需要修改的订单
     */
    public List<Trade> changedTrades = Lists.newArrayList();


    //===============================================================================================================

    /**
     * 旧商品还库存
     */
    public List<Order> resumes = new ArrayList<>();

    public Map<String, Order> origin2ChangeMap = new HashMap<>();
    /**
     * key oldId
     * val newId
     */
    @JSONField(deserialize = false,serialize = false)
    public LinkedListMultimap<Long, Long> fxIdMap = LinkedListMultimap.create();

    public ItemChangeBatchData(Order orderFront, Integer updateNum, Integer updatePayment) {
        this.orderFront = orderFront;
        this.updateNum = Objects.nonNull(updateNum) ? updateNum : 0;
        this.updatePayment = Objects.nonNull(updatePayment) ? updatePayment : 0;
    }

    public ItemChangeBatchData(List<Order> batchChangeItemOrder, TradeBatchChangeItemParams itemParams) {
        this.itemParams = itemParams;
        this.batchChangeItemOrder = batchChangeItemOrder;
        this.updatePayment = Objects.nonNull(itemParams.getUpdatePayment()) ? itemParams.getUpdatePayment() : 0;
        this.updateNum = Objects.nonNull(itemParams.getUpdateNum()) ? itemParams.getUpdateNum() : 0;
        this.updateDivideOrderFee = Objects.nonNull(itemParams.getUpdateNum()) ? itemParams.getUpdateNum() : 0;
        this.KMFBind = Objects.nonNull(itemParams.getKMFBind()) ? itemParams.getKMFBind() : 0;
        this.gxChangeItem = Objects.nonNull(itemParams.getGxChangeItem()) ? itemParams.getGxChangeItem() : 0;
    }

    public ItemChangeBatchData() {
    }

    public void addUpdateOrder(Staff staff, Order order) {
        Order updateOrder = _addUpdateOrder(order);
        updateOrder.setWarehouseId(order.getWarehouseId());
        updateOrder.setItemSysId(order.getItemSysId());
        updateOrder.setSkuSysId(order.getSkuSysId());
        updateOrder.setIsVirtual(order.getIsVirtual());
        updateOrder.setNonConsign(order.getNonConsign());
        updateOrder.setType(order.getType());
        OrderExceptUtils.setStockStatus(staff,updateOrder,order.getStockStatus());
        updateOrders.add(updateOrder);
        updateOrder.setSysOuterId(order.getSysOuterId());
        updateOrder.setSid(order.getSid());
        resumes.add(updateOrder);
        //套件单品
        List<Order> suits = order.getSuits();
        if (suits == null || suits.size() == 0) {
            return;
        }

        for (Order suit : suits) {
            Order suitOrder = _addUpdateOrder(suit);
            suitOrder.setWarehouseId(order.getWarehouseId());
            suitOrder.setItemSysId(suit.getItemSysId());
            suitOrder.setSkuSysId(suit.getSkuSysId());
            suitOrder.setNonConsign(suit.getNonConsign());
            updateOrders.add(suitOrder);

            suit.setWarehouseId(order.getWarehouseId());
        }
        updateOrder.setSuits(suits);
        //清除原套件单品
        order.setSuits(null);
    }

    public Order _addUpdateOrder(Order order) {
        Order updateOrder = OrderBuilderUtils.builderUpdateOrder(order);
        updateOrder.setId(order.getId());
        updateOrder.setSid(order.getSid());
        updateOrder.setEnableStatus(0);
        return updateOrder;
    }

    public List<Trade> getUpdateTrades(Staff staff) {
        List<Trade> list = new ArrayList<>(updateTrades.size());
        for (Trade updateTrade : updateTrades) {
            Trade trade = TradeBuilderUtils.builderUpdateTrade(updateTrade,false);
            trade.setSid(updateTrade.getSid());
            //trade.setStockStatus(updateTrade.getStockStatus());
            trade.setInsufficientNum(updateTrade.getInsufficientNum());
            trade.setInsufficientRate(updateTrade.getInsufficientRate());
            trade.setIsExcep(updateTrade.getIsExcep());
            TradeExceptUtils.setStockStatus(staff,trade,updateTrade.getStockStatus());
            trade.setNetWeight(updateTrade.getNetWeight());

            trade.setPayment(updateTrade.getPayment());
            trade.setDiscountFee(updateTrade.getDiscountFee());
            trade.setTotalFee(updateTrade.getTotalFee());
            //换商品重新更新成本价取值
            trade.setCost(updateTrade.getCost());

            trade.setItemKindNum(updateTrade.getItemKindNum());
            trade.setItemNum(updateTrade.getItemNum());
            trade.setSaleFee(updateTrade.getSaleFee());
            trade.setTagIds(updateTrade.getTagIds());
            trade.setMergeSid(updateTrade.getMergeSid());

            list.add(trade);
        }
        return list;
    }

    /**
     *
     * 前提：先有分销订单调用此方法
     * fx.id -->gx.oid
     *
     * 供销修改商品
     * 1. 如果是分销订单，设置map
     * 2. 如果是供销订单，根据oid获取id
     *
     * @param order
     * @param newId
     * @return
     */
    public Long getId(Order order,Long newId){
        if(!ifGxChangeItem()){
            return newId;
        }
        if(OrderUtils.isFxOrder(order)){
            fxIdMap.put(order.getId(),newId);
            return newId;
        }
        if(OrderUtils.isGxOrMixOrder(order)){
            Order originOrder = order.getOrigin()!=null ? order.getOrigin():order;
            List<Long> ids = fxIdMap.get(originOrder.getOid());
            if(CollectionUtils.isNotEmpty(ids)){
                Long id = ids.get(0);
                fxIdMap.remove(originOrder.getOid(),id);
                return id;
            }
        }

        return newId;
    }


    public void resetList4Fx(){
        errorSids = new HashMap<>();
        insertOrders = new ArrayList<>();
        updateOrders = new ArrayList<>();
        updateTrades = new ArrayList<>();
        resumes =  new ArrayList<>();
        mergeTrades = new ArrayList<>();
        successSids = new ArrayList<>();
        exchangeRelMap = Maps.newHashMap();
    }

    public boolean allowGxTrade(){
        return Integer.valueOf(1).equals(KMFBind);
    }

    public boolean ifGxChangeItem(){
        return Integer.valueOf(1).equals(gxChangeItem);
    }

    public void addOrigin2ChangeMap(Trade originTrade, Order origin, Order changeItemOrder){
        if(ifGxChangeItem() && OrderUtils.isGxOrder(origin)){
            origin2ChangeMap.put(origin.getOid()+"", changeItemOrder);
        }
        if (!allowGxTrade()){
            return;
        }
        if (TradeUtils.isQimenFxSource(originTrade)){
            if (!origin2ChangeMap.containsKey("qiMenKey")){
                origin2ChangeMap.put(origin.getNumIid() + "_" + origin.getSkuId(), changeItemOrder);
                origin2ChangeMap.put("qiMenKey", changeItemOrder);
            }
        }else {
            if (!origin2ChangeMap.containsKey("gxKey")){
                origin2ChangeMap.put(origin.getSid() + "_" + origin.getOid(), changeItemOrder);
                origin2ChangeMap.put("gxKey", changeItemOrder);
            }
        }
    }
}

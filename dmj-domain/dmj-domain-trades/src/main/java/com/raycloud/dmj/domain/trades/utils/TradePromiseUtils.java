package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-07-23 11:03
 * @Description TradePromiseUtils
 */
public class TradePromiseUtils {
    /**
     * tmallPromise 代表天猫时效承诺
     */
    public static final String TM_PROMISE = "tmallPromise";
    /**
     * 发货承诺时效
     */
    public static final String TM_PROMISE_CONSIGN = "tmallpromise.consign.timing";
    /**
     * 到货承诺时效
     */
    public static final String TM_PROMISE_ARRIVAL = "tmallpromise.arrival.timing";

    /**
     * @param trade
     * @return 是否为天猫时效订单
     */
    public static boolean isTmallPromise(Trade trade) {
        return isTmallPromise(trade.getTimingPromise(), trade.getPromiseService());
    }

    /**
     * @param timingPromise  服务身份标识 tmallPromised 代表天猫时效承诺
     * @param promiseService 承诺服务类型，会有多个服务值，以英文半角逗号","切割，其中 tmallpromise.arrival.timing 代表到货承诺时效 tmallpromise.consign.timing代表发货承诺时效
     * @return 是否为天猫时效订单
     */
    private static boolean isTmallPromise(String timingPromise, String promiseService) {
        return TM_PROMISE.equalsIgnoreCase(timingPromise) && StringUtils.isNotEmpty(promiseService) && (promiseService.contains(TM_PROMISE_CONSIGN) || promiseService.contains(TM_PROMISE_ARRIVAL));
    }

    public static boolean isPromiseConsign(Trade trade){
        return TM_PROMISE.equalsIgnoreCase(trade.getTimingPromise()) && StringUtils.isNotEmpty(trade.getPromiseService()) && trade.getPromiseService().contains(TM_PROMISE_CONSIGN);
    }

    public static boolean isPromiseArrival(Trade trade){
        return TM_PROMISE.equalsIgnoreCase(trade.getTimingPromise()) && StringUtils.isNotEmpty(trade.getPromiseService()) && trade.getPromiseService().contains(TM_PROMISE_ARRIVAL);
    }

    /**
     * @param deliveryTime 最晚发货时间，日期，格式2019-04-12 16:00:00 时间等同于最晚揽收时间；
     * @param payTime      付款时间
     * @return 发货承诺时效服务类型
     */
    public static String getPromiseConsignType(Date deliveryTime, Date payTime) {
        if(deliveryTime == null || deliveryTime.getTime() == TradeTimeUtils.INIT_DATE_TIME){
            return "";
        }
        return payTime == null || !DateUtils.isSameDay(deliveryTime, payTime) ? "24h发货" : "当日发货";
    }

    /**
     * @param esTime 相对到达时间，单位为天，0当日达，1次日达，2三日达，3四日达，4五日达s
     * @return 到货承诺时效服务类型
     */
    public static String getPromiseArrivalType(Integer esTime) {
        if (esTime == null || esTime < 0) {
            return "";
        }
        switch (esTime) {
            case 0:
                return "当日达";
            case 1:
                return "次日达";
            case 2:
                return "三日达";
            case 3:
                return "四日达";
            case 4:
                return "五日达";
            default:
                return "大于5日达";
        }
    }

    /**
     * @param promiseService 承诺服务类型，会有多个服务值，以英文半角逗号","切割，其中 tmallpromise.arrival.timing 代表到货承诺时效 tmallpromise.consign.timing代表发货承诺时效
     * @param deliveryTime   最晚发货时间，日期，格式2019-04-12 16:00:00 时间等同于最晚揽收时间；
     * @param payTime        付款时间
     * @param esTime         相对到达时间，单位为天，0当日达，1次日达，2三日达，3四日达，4五日达
     * @return 天猫时效 具体描述
     */
    public static String getPromiseMsg(String promiseService, Date deliveryTime, Date payTime, Integer esTime) {
        if (StringUtils.isEmpty(promiseService)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        //发货承诺时效
        if (promiseService.contains(TM_PROMISE_CONSIGN)) {
            sb.append("天猫时效·").append(getPromiseConsignType(deliveryTime, payTime));
        }
        //到货承诺时效
        if (promiseService.contains(TM_PROMISE_ARRIVAL)) {
            if(sb.length()>0){
                sb.append(",");
            }
            sb.append("天猫时效·").append(getPromiseArrivalType(esTime));
        }
        return sb.toString();
    }

    /**
     * 合单，天猫时效承诺取最小值
     */
    public static <T extends Trade> void merge(Trade mainTrade, List<T> mergeTrades) {
        boolean hasPromise = false;
        String timingPromise = "";
        StringBuilder promiseService = new StringBuilder();
        Date deliveryTime = TradeTimeUtils.INIT_DATE;
        Date collectTime = TradeTimeUtils.INIT_DATE;
        Date signTime = TradeTimeUtils.INIT_DATE;
        int esTime = -1;
        Date minPromiseDeliveryTime = null;

        for (Trade trade : mergeTrades) {
            if (!(isTmallPromise(trade) || trade.isTmallLogisticsUpgrade())) {
                continue;
            }
            hasPromise = true;
            if (StringUtils.isEmpty(timingPromise)) {
                timingPromise = trade.getTimingPromise();
            }
            if (StringUtils.isNotBlank(trade.getPromiseService()) && trade.getPromiseService().contains(TM_PROMISE_CONSIGN) && promiseService.indexOf(TM_PROMISE_CONSIGN) == -1) {
                if (promiseService.length() > 0) {
                    promiseService.append(",");
                }
                promiseService.append(TM_PROMISE_CONSIGN);
            }

            if (StringUtils.isNotBlank(trade.getPromiseService()) && trade.getPromiseService().contains(TM_PROMISE_ARRIVAL) && promiseService.indexOf(TM_PROMISE_ARRIVAL) == -1) {
                if (promiseService.length() > 0) {
                    promiseService.append(",");
                }
                promiseService.append(TM_PROMISE_ARRIVAL);
            }

            deliveryTime = getMinDate(deliveryTime, trade.getDeliveryTime());
            collectTime = getMinDate(collectTime, trade.getCollectTime());
            signTime = getMinDate(signTime, trade.getSignTime());
            esTime = getMinInt(esTime, trade.getEsTime());
            TradeExt tradeExt = trade.getTradeExt();
            if (null!=tradeExt){
                minPromiseDeliveryTime = getMinDate(minPromiseDeliveryTime,tradeExt.getPromiseDeliveryTime());
            }

        }

        if (!hasPromise) {
            return;
        }
        mainTrade.setTimingPromise(timingPromise);
        mainTrade.setPromiseService(promiseService.toString());
        mainTrade.setDeliveryTime(deliveryTime);
        mainTrade.setCollectTime(collectTime);
        mainTrade.setSignTime(signTime);
        mainTrade.setEsTime(esTime);

        TradeExt tradeExt = mainTrade.getTradeExt();
        if (null!=tradeExt){
            tradeExt.setPromiseDeliveryTime(minPromiseDeliveryTime);
        }
    }

    private static Date getMinDate(Date d1, Date d2) {
        if (d1 == null) {
            d1 = TradeTimeUtils.INIT_DATE;
        }
        if (d2 == null) {
            d2 = TradeTimeUtils.INIT_DATE;
        }

        long t1 = d1.getTime();
        if (t1 == TradeTimeUtils.INIT_DATE_TIME) {
            return d2;
        }

        long t2 = d2.getTime();
        if (t2 == TradeTimeUtils.INIT_DATE_TIME) {
            return d1;
        }
        return t1 <= t2 ? d1 : d2;
    }

    private static int getMinInt(int i1, Integer i2) {
        if (i2 == null || i2 == -1) {
            return i1;
        }
        if (i1 == -1) {
            return i2;
        }
        return Math.min(i1, i2);
    }
}

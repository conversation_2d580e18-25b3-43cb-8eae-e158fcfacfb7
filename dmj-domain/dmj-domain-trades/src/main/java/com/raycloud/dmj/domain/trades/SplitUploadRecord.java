package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.user.User;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Types;
import java.util.*;

/**
 * <AUTHOR>
 */
@Table(name = "split_upload_record")
public class SplitUploadRecord extends Model {

    /**
     * 激活状态
     */
    public static final Integer ENABLE = 1;

    /**
     * 未上传状态
     */
    public static final Integer NOT_UPLOAD = 0;

    /**
     * 已上传（系统）
     */
    public static final Integer SYS_UPLOAD = 1;

    /**
     * 已上传（平台）
     */
    public static final Integer PLAT_UPLOAD = 2;

    private static final long serialVersionUID = 8433474656312787093L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 公司id
     */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 店铺id
     */
    @Column(name = "user_id", type = Types.BIGINT)
    private Long userId;

    /**
     * 类型
     */
    private String source;

    /**
     * 子订单id
     */
    @Column(name = "order_id", type = Types.BIGINT)
    private Long orderId;

    /**
     * 系统id
     */
    private Long sid;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 平台id
     */
    private String tid;

    /**
     * 平台子订单id
     */
    private String oid;

    /**
     * 平台子订单商品项ids
     */
    @Column(name = "item_ids", type = Types.VARCHAR)
    private String itemIds;

    /**
     * 包裹id
     */
    @Column(name = "pack_id", type = Types.VARCHAR)
    private String packId;

    /**
     * 发货数量
     */
    private Integer num;

    /**
     * 发货状态
     * 0: 未发货
     * 1：系统发货
     * 2：平台发货
     */
    @Column(name = "upload_status", type = Types.INTEGER)
    private Integer uploadStatus;

    /**
     * 激活状态
     */
    @Column(name = "enable_status", type = Types.INTEGER)
    private Integer enableStatus;

    @Column(name = "logistics_time", type = Types.TIMESTAMP)
    private Date logisticsTime;

    /**
     * 是否匹配到了order，如果没有匹配到order，说明是平台发货了
     */
    private boolean isMatched;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getItemIds() {
        return itemIds;
    }

    public void setItemIds(String itemIds) {
        this.itemIds = itemIds;
    }

    public String getPackId() {
        return packId;
    }

    public void setPackId(String packId) {
        this.packId = packId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getLogisticsTime() {
        return logisticsTime;
    }

    public void setLogisticsTime(Date logisticsTime) {
        this.logisticsTime = logisticsTime;
    }

    public boolean isMatched() {
        return isMatched;
    }

    public void setMatched(boolean matched) {
        isMatched = matched;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SplitUploadRecord that = (SplitUploadRecord) o;
        return Objects.equals(oid, that.oid) &&
                Objects.equals(itemIds, that.itemIds);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyId, userId, source, orderId, sid, tid, oid, itemIds, packId, num, uploadStatus, enableStatus, logisticsTime, created, updated);
    }

    public static List<SplitUploadRecord> fillRecord(User user, List<Order> orderList) {
        return fillRecord(user, orderList, null);
    }

    public static List<SplitUploadRecord> fillRecord(User user, List<Order> orderList, Integer uploadStatus) {
        Set<SplitUploadRecord> splitUploadRecordList = new TreeSet<>(new Comparator<SplitUploadRecord>() {
            @Override
            public int compare(SplitUploadRecord o1, SplitUploadRecord o2) {
                return (o1.getPackId() + o1.getItemIds()).compareTo(o2.getPackId() + o2.getItemIds());
            }
        });
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>(splitUploadRecordList);
        }
        for (Order order : orderList) {
            if (CollectionUtils.isEmpty(order.getSplitUploadRecords())) {
                continue;
            }
            order.getSplitUploadRecords().forEach(splitUploadRecord -> {
                splitUploadRecord.setCompanyId(user.getCompanyId());
                splitUploadRecord.setUserId(user.getId());
                splitUploadRecord.setSource(user.getSource());
                splitUploadRecord.setSid(order.getSid());
                splitUploadRecord.setTid(order.getTid());
                splitUploadRecord.setOid(order.getOid().toString());
                if (null != uploadStatus) {
                    splitUploadRecord.setUploadStatus(uploadStatus);
                }
                splitUploadRecord.setEnableStatus(ENABLE);
            });
            splitUploadRecordList.addAll(order.getSplitUploadRecords());
        }
        return new ArrayList<>(splitUploadRecordList);
    }

    public static SplitUploadRecord buildSysUploadRecord(User user, Order order, String itemIds, Integer uploadNum, String outSid) {
        SplitUploadRecord splitUploadRecord = new SplitUploadRecord();
        splitUploadRecord.setCompanyId(user.getCompanyId());
        splitUploadRecord.setUserId(user.getId());
        splitUploadRecord.setSource(user.getSource());
        splitUploadRecord.setSid(order.getSid());
        splitUploadRecord.setTid(order.getTid());
        splitUploadRecord.setOid(order.getOid().toString());
        splitUploadRecord.setOrderId(order.getId());
        splitUploadRecord.setItemIds(itemIds);
        splitUploadRecord.setNum(uploadNum);
        splitUploadRecord.setUploadStatus(SYS_UPLOAD);
        splitUploadRecord.setOutSid(outSid);
        return splitUploadRecord;
    }
}

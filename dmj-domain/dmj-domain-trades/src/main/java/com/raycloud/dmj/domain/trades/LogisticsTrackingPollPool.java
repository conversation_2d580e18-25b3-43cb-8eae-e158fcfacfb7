package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Table;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-01-31-18:04
 */
@Table(name = "logistics_tracking_poll_pool", routerKey = "logisticsTrackingPollPoolDbNo")
public class LogisticsTrackingPollPool extends Trade implements Orderable<Order> {
    private static final long serialVersionUID = -1457431008977672674L;

    public LogisticsTrackingPollPool() {
    }

    public LogisticsTrackingPollPool(Long sid, Long companyId, String source, Long userId, String outSid, Date consignTime, String logisticsStatus) {
        this.sid = sid;
        this.companyId = companyId;
        this.source = source;
        this.userId = userId;
        this.outSid = outSid;
        this.consignTime = consignTime;
        this.logisticsStatus = logisticsStatus;
    }

    /**
     * 未揽收
     */
    public static final String WAIT_GATHERED = "WAIT_GATHERED";

    /**
     * 已揽收
     */
    public static final String GATHERED = "GATHERED";

    /**
     * 已签收
     */
    public static final String SIGNED = "SIGNED";

    /**
     * 揽收超时
     */
    public static final String GATHERED_CONFIRM_TIMEOUT = "GATHERED_CONFIRM_TIMEOUT";

    /**
     * 物流信息更新超时
     */
    public static final String LOGISTICS_MODIFY_TIMEOUT = "LOGISTICS_MODIFY_TIMEOUT";

    /**
     * 揽收后无物流信息超时
     */
    public static final String LOGISTICS_NOTRANSFORM_TIMEOUT = "LOGISTICS_NOTRANSFORM_TIMEOUT";
    /**
     * 分拨中心停留超时
     */
    public static final String LOGISTICS_ALLOCAT_TIMEOUT = "LOGISTICS_ALLOCAT_TIMEOUT";
    /**
     * 节点停留超时
     */
    public static final String LOGISTICS_NODE_TIMEOUT = "LOGISTICS_NODE_TIMEOUT";

    private Long sid;

    private Long companyId;

    private String source;

    private Long userId;

    private String outSid;

    private Date consignTime;

    /**
     * 0表示物流信息订阅失败， 1表示物流信息已订阅
     */
    private String logisticsStatus;

    private String logisticsExceptType;
    /**
     * 物流最近更新时间
     */
    private Date logisticsModified;

    private Date modified;

    /**
     * 快递公司code  不持久化
     */
    private String cpCode;

    /**
     * 物流记录条数
     */
    private Integer logisticsTraceCount;

    /**
     * 最近物流记录
     */
    private String lastLogisticsTrace;

    /**
     * 实际获取到的单号的userId
     */
    private Long branchUserId;

    /**
     * 是否需要跟踪
     */
    private Integer needTracking;

    /**
     * 追踪时间 根据 物流状态 和用户设置的时间计算出来
     * 时间先于当前时间的才需要追踪
     */
    private Date calculatedTime;
    /**
     * 物流跟踪版本
     * 0：老版本物流跟踪
     * 1：新版本物流跟踪，订阅服务走物流预警
     */
    private Integer version;

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    @Override
    public Date getConsignTime() {
        return consignTime;
    }

    @Override
    public void setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
    }

    public String getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public String getLogisticsExceptType() {
        return logisticsExceptType;
    }

    public void setLogisticsExceptType(String logisticsExceptType) {
        this.logisticsExceptType = logisticsExceptType;
    }

    public Date getLogisticsModified() {
        return logisticsModified;
    }

    public void setLogisticsModified(Date logisticsModified) {
        this.logisticsModified = logisticsModified;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getExceptTypeName() {
        if (GATHERED_CONFIRM_TIMEOUT.equals(logisticsExceptType)) {
            return "超时未揽收";
        } else if (LOGISTICS_MODIFY_TIMEOUT.equals(logisticsExceptType)) {
            return "超时物流未更新";
        } else if (LOGISTICS_NOTRANSFORM_TIMEOUT.equals(logisticsExceptType)) {
            return "超时揽收后无物流";
        } else if (LOGISTICS_ALLOCAT_TIMEOUT.equals(logisticsExceptType)) {
            return "分拨中心停留超时";
        } else if (LOGISTICS_NODE_TIMEOUT.equals(logisticsExceptType)) {
            return "节点停留超时";
        }
        return "";
    }

    public Integer getLogisticsTraceCount() {
        return logisticsTraceCount;
    }

    public void setLogisticsTraceCount(Integer logisticsTraceCount) {
        this.logisticsTraceCount = logisticsTraceCount;
    }

    public String getLastLogisticsTrace() {
        return lastLogisticsTrace;
    }

    public void setLastLogisticsTrace(String lastLogisticsTrace) {
        this.lastLogisticsTrace = lastLogisticsTrace;
    }

    public Long getBranchUserId() {
        return branchUserId;
    }

    public void setBranchUserId(Long branchUserId) {
        this.branchUserId = branchUserId;
    }

    public Integer getNeedTracking() {
        return needTracking;
    }

    public void setNeedTracking(Integer needTracking) {
        this.needTracking = needTracking;
    }

    public Date getCalculatedTime() {
        return calculatedTime;
    }

    public void setCalculatedTime(Date calculatedTime) {
        this.calculatedTime = calculatedTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public void setOrders(List<Order> orders) {

    }

    @Override
    public List<Order> getOrders() {
        return new ArrayList<>();
    }
}

package com.raycloud.dmj.domain.trades.renderer.query;

import com.raycloud.dmj.domain.renderer.IFieldValueRenderer;
import com.raycloud.dmj.domain.renderer.annotation.FieldRenderer;

import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2023-07-17
 */
public class SplitWarehouseTypeRenderer implements IFieldValueRenderer {

    /**
     * 智能分仓勾选类型
     * splitWarehouseType
     * 1 （按规则分仓
     * 0  （按库存分仓，整单库存不满足自动拆单
     * 2 （ 按库存分仓，整单库存不满足，不拆单
     * 3  （先规则后库存, 拆单
     * 4  （先规则后库存，不拆单
     */
    @Override
    public String getRenderedValue(Object target, String fieldName, FieldRenderer annotation, Object originValue) {
        if (originValue == null) {
            return null;
        }
        if (Objects.equals(originValue,0)) {
            return "按库存分仓，整单库存不满足自动拆单";
        }
        if (Objects.equals(originValue,1)) {
            return "按规则分仓";
        }
        if (Objects.equals(originValue,2)) {
            return "按库存分仓，整单库存不满足，不拆单";
        }
        if (Objects.equals(originValue,3)) {
            return "先规则后库存, 拆单";
        }
        if (Objects.equals(originValue,4)) {
            return "先规则后库存，不拆单";
        }
        if (Objects.equals(originValue,-1)) {
            return null;
        }
        return String.valueOf(originValue);
    }
}

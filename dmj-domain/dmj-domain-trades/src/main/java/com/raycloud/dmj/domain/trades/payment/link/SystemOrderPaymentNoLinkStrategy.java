package com.raycloud.dmj.domain.trades.payment.link;

import com.raycloud.dmj.domain.enums.OpenLinkConfigEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 系统订单金额不联动，增/删/改商品时，trade实付金额不变，平台订单实付金额trade变化
 * @author: pxh
 * @create: 2021-07-29 17:59
 **/
public class SystemOrderPaymentNoLinkStrategy implements IPaymentLinkStrategy {

    @Override
    public PaymentLinkRespDTO insertOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        if(StringUtils.isBlank(paymentLinkReqDTO.getTradeSource())){
            throw new IllegalArgumentException("trade source不能为空");
        }
        Order order = paymentLinkReqDTO.getOrder();
        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(paymentLinkReqDTO.getTradeSource())) {
            return commonPaymentLink(order);
        }
        return noPaymentLink(paymentLinkReqDTO.getOrder());
    }

    @Override
    public PaymentLinkRespDTO updateOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        Order order = paymentLinkReqDTO.getOrder();
        return commonPaymentLink(order);
    }

    @Override
    public PaymentLinkRespDTO deleteOrder(PaymentLinkReqDTO paymentLinkReqDTO) {
        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(paymentLinkReqDTO.getTradeSource())) {
            Order order = paymentLinkReqDTO.getOrder();
            return commonPaymentLink(order);
        }
        return deleteOrderPayment(paymentLinkReqDTO);    }

    @Override
    public boolean support(OpenLinkConfigEnum openLinkConfigEnum) {
        return OpenLinkConfigEnum.SYSTEM_TRADE_PAYMENT_NO_LINK.equals(openLinkConfigEnum);
    }
}

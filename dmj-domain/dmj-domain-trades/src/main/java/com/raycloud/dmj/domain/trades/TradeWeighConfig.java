package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

/**
 * 订单称重配置类
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/9/21.
 */
@Table(name = "trade_weigh_config")
public class TradeWeighConfig extends Model {

    private static final long serialVersionUID = -3812250105414395407L;

    //称重偏差类型
    public static String WEIGH_TYPE_WEIGHT = "weight";      //按重量

    private Long companyId;

    private Boolean isOpen;

    private String weighType;

    private String upperLimit;

    private String lowerLimit;

    public TradeWeighConfig(){

    }

    public TradeWeighConfig(Boolean isOpen, String weighType, String upperLimit, String lowerLimit) {
        this.isOpen = isOpen;
        this.weighType = weighType;
        this.upperLimit = upperLimit;
        this.lowerLimit = lowerLimit;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Boolean getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Boolean isOpen) {
        this.isOpen = isOpen;
    }

    public String getWeighType() {
        return weighType;
    }

    public void setWeighType(String weighType) {
        this.weighType = weighType;
    }

    public String getUpperLimit() {
        return upperLimit;
    }

    public void setUpperLimit(String upperLimit) {
        this.upperLimit = upperLimit;
    }

    public String getLowerLimit() {
        return lowerLimit;
    }

    public void setLowerLimit(String lowerLimit) {
        this.lowerLimit = lowerLimit;
    }
}

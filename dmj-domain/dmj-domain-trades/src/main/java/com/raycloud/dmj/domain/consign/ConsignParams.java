package com.raycloud.dmj.domain.consign;

import com.raycloud.dmj.domain.trades.TradeCombineParcel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * ConsignParams
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConsignParams implements Serializable {

    private Long[] sids;

    private SendType consignType;

    private String clientIp;

    private boolean exceptionConsign;

    private Integer dymmyType;

    private String noLogisticsName;

    private String noLogisticsTel;

    private boolean isFilterPartyWarehouse;

    private String operateType;

    private TradeCombineParcel parcel;

    private Set<Long> parallelUploadSids;
}

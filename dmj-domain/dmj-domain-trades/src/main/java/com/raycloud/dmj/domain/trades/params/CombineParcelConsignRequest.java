package com.raycloud.dmj.domain.trades.params;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2022_06_20 11:53
 */
public class CombineParcelConsignRequest implements Serializable {
    //1:上传平台按钮,2:继续上传平台,3:重新上传平台
    private Integer type;
    private String combineParcelIds;
    //1:邮寄，2:无需物流
    private Integer btasConsignType;

    private boolean progressEnable=false;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCombineParcelIds() {
        return combineParcelIds;
    }

    public void setCombineParcelIds(String combineParcelIds) {
        this.combineParcelIds = combineParcelIds;
    }

    public Integer getBtasConsignType() {
        return btasConsignType;
    }

    public void setBtasConsignType(Integer btasConsignType) {
        this.btasConsignType = btasConsignType;
    }

    public boolean isEvent() {
        return progressEnable;
    }

    public void setEvent(boolean event) {
        this.progressEnable = event;
    }
}

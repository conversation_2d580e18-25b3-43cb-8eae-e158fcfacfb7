package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Parcel;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;

/**
 * description:
 * date: 2020/12/9
 *
 * @author: yiyuanyuan
 */
public class TradeSysConsignUtils {

    /**
     * 未发货
     */
    public static final int NO_CONSIGNED = 0;

    /**
     * erp发货
     */
    public static final int ERP_CONSIGNED = 1;

    /**
     * 其他erp发货
     */
    public static final int OTHER_ERP_CONSIGNED = 2;

    private static List<String> NO_DELIVERY_TIME_PLATS = new ArrayList<>();

    static {
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_AKC);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_JINRIBAOTUAN);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_JD_VC);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_ALIHEALTH);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_YYJK);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_FANKE);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_1889);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_SHEIN);
        NO_DELIVERY_TIME_PLATS.add(CommonConstants.PLAT_FORM_TYPE_XIANYU);
    }


    /**
     * 其他erp发货取值逻辑为：
     *  1. 所有order的sysConsigned都为2
     *  2. 商品状态为0+2时，如果状态为0的商品全为交易关闭，则也是其他erp发货 (KMERP-139563)
     *
     * @param trades
     */
    public static void handleSysConsigned(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        for (Trade trade : trades) {
            int nonConsignClosedNum = 0;
            int sysConsignedNum = 0;
            List<Order> orders4Trades = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders4Trades) {
                if (isOtherErpConsigned(order)) {
                    order.setSysConsigned(OTHER_ERP_CONSIGNED);
                    //其他ERP发货时，设置order的发货时间
                    if (null != trade.getConsignTime() && trade.getConsignTime().after(TradeTimeUtils.INIT_DATE)) {
                        order.setConsignTime(trade.getConsignTime());
                        //套件未转单品，将订单的发货时间塞给套件中的order的发货时间
                        if (null != order.getCombineId() && 0L == order.getCombineId() && 2 == order.getType()
                                && 1 == order.getEnableStatus() && order.getSuits() != null) {
                            for (Order suitOrder : order.getSuits()) {
                                suitOrder.setConsignTime(trade.getConsignTime());
                            }
                        }
                    } else if(Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) && isEmptyTime(trade.getConsignTime())){
                        order.setConsignTime(isEmptyTime(trade.getConsignTime()) ? trade.getPtConsignTime() : trade.getConsignTime());
                        if(isEmptyTime(order.getConsignTime())){
                            order.setConsignTime(isEmptyTime(trade.getModified()) ? trade.getUpdTime() : trade.getModified());
                        }
                    }
                    sysConsignedNum++;
                    if (order.getSuits() != null) {
                        order.getSuits().forEach(suit -> suit.setSysConsigned(OTHER_ERP_CONSIGNED));
                    }
                }

                // KMERP-139563: 商品状态为0+2时，如果状态为0的商品全为交易关闭，则也是其他erp发货
                if (Objects.equals(Optional.ofNullable(order.getSysConsigned()).orElse(0), 0) &&
                        Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                    nonConsignClosedNum++;
                }
            }
            if (sysConsignedNum > 0 && (sysConsignedNum + nonConsignClosedNum) == orders4Trades.size()) {
                trade.setSysConsigned(OTHER_ERP_CONSIGNED);
                //处理整笔订单已经完全发货,但是平台没有返回发货时间的平台订单
                handlerNoConsignTimeTrade(trade);
            }
        }
    }

    /**
     * 处理其他erp发货 的发货时间
     * @param trade
     */
    public static void handlerNoConsignTimeTrade(Trade trade) {
        if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) && isEmptyTime(trade.getConsignTime())) {
//平台没有发货时间.这里取当前同步到已发货状态时的系统时间作为平台发货时间
            if (NO_DELIVERY_TIME_PLATS.contains(trade.getSource())) {
                Date currentDate = new Date();
                Date payTime = trade.getPayTime() != null && trade.getPayTime().after(TradeTimeUtils.INIT_DATE) ? trade.getPayTime() : null;
                if(payTime == null && trade.getCreated() != null && trade.getCreated().after(TradeTimeUtils.INIT_DATE)){
                    payTime = trade.getCreated();
                }
                if(payTime != null){
                    Date newDate = DateUtils.addDays(payTime, 48);
                    if(currentDate.after(newDate)){
                        currentDate = newDate;
                    }
                }
                trade.setConsignTime(currentDate);
                trade.setPtConsignTime(currentDate);
                for (Order order : TradeUtils.getOrders4Trade(trade)) {
                    order.setConsignTime(currentDate);
                    order.setPtConsignTime(currentDate);
                    if(CollectionUtils.isNotEmpty(order.getSuits())){
                        for (Order suit : order.getSuits()) {
                            suit.setConsignTime(currentDate);
                            suit.setPtConsignTime(currentDate);
                        }
                    }
                }
            }
        }
    }

    public static boolean isEmptyTime(Date date) {
        if (null == date) {
            return true;
        }
        return date.after(TradeTimeUtils.INIT_DATE) ? false : true;
    }

    public static void handleOrderSysConsigned(List<Order> orders,List<String> sysOutSids) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (Order order : orders) {
            if (isOtherErpConsigned(order)||isFxgAdvanceOrder(order,sysOutSids)) {
                order.setSysConsigned(OTHER_ERP_CONSIGNED);
                if (order.getSuits() != null) {
                    order.getSuits().forEach(suit -> suit.setSysConsigned(OTHER_ERP_CONSIGNED));
                }
            }
        }
    }

    public static boolean isFxgAdvanceOrder(Order order,List<String> sysOutSids){
        if(!CommonConstants.PLAT_FORM_TYPE_FXG.equals(order.getSource())){
            return false;
        }
        Parcel parcel = order.getParcel();
        if(parcel==null){
            return false;
        }
        JSONObject detail = JSONObject.parseObject(parcel.getDetail());
        if(detail!=null){
            String tracking_no = detail.getString("tracking_no");
            if(tracking_no!=null&&!sysOutSids.contains(tracking_no)){
                return true;
            }
        }
        return false;
    }

    public static boolean isOtherErpConsigned(Order order) {
        //发货前更新为交易关闭
        if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) && !(Trade.SYS_STATUS_FINISHED.equals(order.getOldSysStatus()) || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(order.getOldSysStatus()))) {
            return false;
        }
        //这里没有直接等于2是兼容历史状态0，0表示了未发货及其他erp发货
        int sysConsigned = order.getSysConsigned() == null ? 0 : order.getSysConsigned();
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && sysConsigned != ERP_CONSIGNED) {
            return true;
        }
        return false;
    }

    public static boolean isOtherErpConsigned(Staff staff, Trade trade) {
        return Objects.equals(trade.getSysConsigned(), OTHER_ERP_CONSIGNED);
    }

    public static void handleOrderSysConsigned(Order order) {
        if (isOtherErpConsigned(order)) {
            order.setSysConsigned(OTHER_ERP_CONSIGNED);
            if (order.getSuits() != null) {
                order.getSuits().forEach(suit -> suit.setSysConsigned(OTHER_ERP_CONSIGNED));
            }
        }
    }
}

package com.raycloud.dmj.domain.logistics;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenchaochao
 * @Date: 2020/5/21 1:42 下午
 */
public class LogisticsTrajectory implements Serializable {

    private static final long serialVersionUID = 6594966721079776570L;

    private Long id;
    /**
     * 运单号
     */
    private String trackingNumber;
    /**
     *
     */
    private Integer shippingId;

    private String statusDesc;

    private String action;

    private Date time;

    private String desc;

    private Date created;

    private Integer tableId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Integer getShippingId() {
        return shippingId;
    }

    public void setShippingId(Integer shippingId) {
        this.shippingId = shippingId;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.consign.UploadServiceWhitelistConfig;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.diamond.consign.UploadServiceWhitelistConfig.ALL;


/**
 * UploadServiceWhitelistUtil
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/11/18 09:38
 */
public class UploadServiceWhitelistUtils {


    public static List<Trade> distributeUploadServiceTrades(Staff staff, Integer type, List<Trade> trades) {
        Set<String> sources = uploadServiceSupportSources(staff, type);
        return trades.stream().filter(t -> sources.contains(getPlatSource(t,staff))).collect(Collectors.toList());
    }

    public static boolean isDistributeUploadServiceTrade(Staff staff, Integer type, Trade trade) {
        Set<String> sources = uploadServiceSupportSources(staff, type);
        return sources.contains(getPlatSource(trade,staff));
    }

    /**
     * @param type ALL: 发货上传+多包裹发货上传、 1: 发货上传、 2: 多包裹发货上传
     * @return 发货上传服务支持的平台
     */
    public static Set<String> uploadServiceSupportSources(Staff staff, Integer type) {
        Long companyId = staff.getCompanyId();
        List<UploadServiceWhitelistConfig.Whitelist> whitelists = ConfigHolder.UPLOAD_SERVICE_WHITELIST_CONFIG.getWhitelists();
        if (CollectionUtils.isEmpty(whitelists)) {
            return Sets.newHashSet();
        }

        return whitelists.stream()
                .filter(config -> CollectionUtils.containsAny(config.getCompanyIds(), Lists.newArrayList(ALL, String.valueOf(companyId)))) // 公司
                .filter(config -> config.getExcludeCompanyIds()==null||!CollectionUtils.containsAny(config.getExcludeCompanyIds(), Lists.newArrayList(ALL, String.valueOf(companyId)))) // 排除的公司
                .filter(config -> Objects.equals(ALL, config.getType()) || Objects.equals(String.valueOf(type), config.getType())) // 上传类型
                .map(UploadServiceWhitelistConfig.Whitelist::getSource)
                .map(source -> Arrays.asList(source.split(",")))
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toSet());
    }


    private static String getPlatSource(Trade trade,Staff staff) {
        User user = staff.getUserByUserId(trade.getUserId());
        if(user!=null){
            return user.getSource();
        }
        return CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) ? trade.getSplitSource() : trade.getSource();
    }

}

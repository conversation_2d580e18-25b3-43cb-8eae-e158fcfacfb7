package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台 仓配建议信息
 * 对应淘宝单的 logistics_infos
 */
@Table(name = "platform_recommendation")
public class PlatformRecommendation extends Model implements Serializable {

    private static final long serialVersionUID = -6893404758926620193L;

    private Long id;

    private Long companyId;

    private String tid;

    /**
     * enum: PlatformRecommendationType
     */
    private String type;

    /**
     * 平台建议 json
     * 淘宝翱象对应 TbRecommendation
     */
    private String recommendation;

    private Date created;

    private Date modified;

    private Integer enableStatus;

    public TbRecommendation getTbRecommendation() {
        if (recommendation == null || recommendation.isEmpty()) {
            return null;
        }

        return JSONObject.parseObject(recommendation, TbRecommendation.class);
    }

    public PlatformRecommendation() {
    }

    public PlatformRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public PlatformRecommendation(Long companyId, String tid, String recommendation) {
        this.companyId = companyId;
        this.tid = tid;
        this.recommendation = recommendation;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRecommendation() {
        return recommendation;
    }

    public void setRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

}

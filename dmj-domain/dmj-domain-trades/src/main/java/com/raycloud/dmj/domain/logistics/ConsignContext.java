package com.raycloud.dmj.domain.logistics;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeControllerParams;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicLong;

public class ConsignContext implements Serializable {
    private static final long serialVersionUID = 1L;
    public ConsignContext(){}

    public ConsignContext(Staff staff, String clientIp, SendType sendType){
        this.staff = staff;
        this.clientIp = clientIp;
        this.sendType = sendType;
    }

    public ConsignContext(Staff staff, TradeControllerParams queryParams, String clientIp, SendType sendType, ProgressData progressData){
        this.staff = staff;
        this.queryParams = queryParams;
        this.clientIp = clientIp;
        this.sendType = sendType;
        this.progressData = progressData;
    }

    public ConsignContext(Staff staff, String clientIp, SendType sendType, Integer dymmyType, String noLogisticsName, String noLogisticsTel, Map<String, Integer> paramMap){
        this.staff = staff;
        this.clientIp = clientIp;
        this.sendType = sendType;
        this.dymmyType = dymmyType;
        this.noLogisticsName = noLogisticsName;
        this.noLogisticsTel = noLogisticsTel;
        this.paramMap = paramMap;
    }


    public TradeControllerParams queryParams;
    public AtomicLong sucNum = new AtomicLong(0);
    public Staff staff;
    public String clientIp;
    public SendType sendType;
    public ProgressData progressData;
    public Map<Long, Warehouse> warehouses = new ConcurrentHashMap<>();
    public TradeConfig tradeConfig;
    public Map<Long, List<String>> errorMessage = new ConcurrentHashMap<>();
    public Integer dymmyType;
    public String noLogisticsName;
    public String noLogisticsTel;
    public AtomicLong countAll = new AtomicLong(0);
    public Map<String, Integer> paramMap;
    public Set<String> uniqueCodeItem = new CopyOnWriteArraySet<>();
    public AtomicLong errorNum = new AtomicLong(0);
    public boolean needValid = true;
}

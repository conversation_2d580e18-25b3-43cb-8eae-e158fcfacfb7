package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.Sort;

import java.util.List;

/**
 * 通用订单集合
 * <AUTHOR>
 *
 */
public class Trades extends PageListBase<Trade> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3192626617043656746L;

	private Boolean hasNext;

	public Boolean getHasNext() {
		return hasNext;
	}

	public void setHasNext(Boolean hasNext) {
		this.hasNext = hasNext;
	}


	public static Trades createTrades(List<Trade> list, Long total, Page page, Sort sort){
		Trades trades = new Trades();
		trades.setList(list);
		trades.setTotal(total);
		trades.setPage(page);
		trades.setSort(sort);
		return trades;
	}
}

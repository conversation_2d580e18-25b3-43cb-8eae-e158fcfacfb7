package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.trades.OrderExt;

/**
 * @ClassName OrderExtCopier
 * @Description OrderExtCopier
 * <AUTHOR>
 * @Date 2024/3/12
 * @Version 1.0
 */
public abstract class OrderExtCopier {

    public static OrderExt copy(OrderExt orderExt) {
        OrderExt copy = new OrderExt();
        copy.setId(orderExt.getId());
        copy.setSid(orderExt.getSid());
        copy.setTid(orderExt.getTid());
        copy.setCompanyId(orderExt.getCompanyId());
        copy.setEnableStatus(orderExt.getEnableStatus());
        copy.setCustomization(orderExt.getCustomization());
        copy.setSupplierName(orderExt.getSupplierName());
        copy.setSupplierItemOuterId(orderExt.getSupplierItemOuterId());
        copy.setCooperationNoJitx(orderExt.getCooperationNoJitx());
        copy.setAuthorId(orderExt.getAuthorId());
        copy.setAuthorNo(orderExt.getAuthorNo());
        copy.setAuthorName(orderExt.getAuthorName());
        copy.setOrderRemark(orderExt.getOrderRemark());
        copy.setPromiseAcceptTime(orderExt.getPromiseAcceptTime());
        copy.setRefundingCnt(orderExt.getRefundedCnt());
        copy.setRefundedCnt(orderExt.getRefundedCnt());
        copy.setSkuCnt(orderExt.getSkuCnt());
        return copy;
    }
}

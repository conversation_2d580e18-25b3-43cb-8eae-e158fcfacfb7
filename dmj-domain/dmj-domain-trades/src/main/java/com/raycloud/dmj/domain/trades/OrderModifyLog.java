package com.raycloud.dmj.domain.trades;

import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-07-21 15:54
 * @Description 套件转单品/套件修改单品 等 明细记录
 */
@Table(name = "order_modify_log", routerKey = "orderModifyLogDbNo")
@ToString
@Setter
@Getter
public class OrderModifyLog extends Model {

    private static final long serialVersionUID = 393497694641423682L;

    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 系统订单号
     */
    private Long sid;

    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 系统子订单号
     */
    private Long orderId;

    /**
     * 平台子订单号
     */
    private Long oid;

    /**
     * 修改类型
     */
    private Integer modifyType;

    /**
     * 修改内容
     */
    private String content;

    /**
     * 新增时间
     */
    private Date insertTime;

    private Integer enableStatus;

    private Long originOrderId;

    private OrderModifyLogContent contentInfo;

}

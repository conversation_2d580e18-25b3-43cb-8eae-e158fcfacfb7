package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TradeWeightParams implements Serializable {

    private static final long serialVersionUID = 5796797652320171573L;

    private Staff staff;

    /**
     * 订单号
     */
    private Long sid;

    /**
     * 部分明确不需要重新查询订单的场景下 优先取这个已有值
     */
    private Trade trade;

    private AbsLogBuilder logBuilder;

    /**
     * 重量
     */
    private String weight;

    /**
     * 费用
     */
    private String cost;

    /**
     * 称重类型 0123:发货前称重/发货后称重/发货前重复称重/发货后重复称重
     */
    private Integer kind;

    /**
     * 请求ip
     */
    private String clientIp;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 商品标识包材编码
     */
    private String outerId;

    /**
     * 称重扫描类型 123:系统单号/内部单号/快递单号
     */
    private Integer scanType;

    private String packmaOuterIds;

    private Staff packStaff;

    /**
     * 实际体积
     */
    private String actualVolume;

    /**
     * 实际长宽高
     */
    private String actualLengthWidthAndHeight;

    private boolean checked = false;

    private boolean asyncCalcFreight;

    public static class Builder {
        private Staff staff;

        private Long sid;

        private String weight;

        private String cost;

        private Integer kind;

        private String clientIp;

        private String outSid;

        private String outerId;

        private AbsLogBuilder logBuilder;

        private String actualVolume;

        private String actualLengthWidthAndHeight;

        /**
         * 部分明确不需要重新查询订单的场景下 优先取这个已有值
         */
        private Trade trade;

        private String packmaOuterIds;

        private Staff packStaff;

        private boolean asyncCalcFreight;

        public TradeWeightParams build() {
            TradeWeightParams params = new TradeWeightParams();
            if(sid == null){
                throw new IllegalArgumentException("请输入sids参数");
            }
            if(StringUtils.isEmpty(weight)){
                throw new IllegalArgumentException("请输入weight参数");
            }
            params.staff = staff;
            params.sid = sid;
            params.weight = weight;
            params.cost = cost;
            params.kind = kind;
            params.clientIp = clientIp;
            params.outSid = outSid;
            params.outerId = outerId;
            params.trade = trade;
            params.logBuilder = logBuilder;
            params.packmaOuterIds = packmaOuterIds;
            params.packStaff = packStaff;
            params.actualVolume = actualVolume;
            params.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
            params.asyncCalcFreight = asyncCalcFreight;
            return params;
        }

        public Builder staff(Staff staff) {
            this.staff = staff;
            return this;
        }

        public Builder sid(Long sid) {
            this.sid = sid;
            return this;
        }

        public Builder weight(String weight) {
            this.weight = weight;
            return this;
        }

        public Builder cost(String cost) {
            this.cost = cost;
            return this;
        }

        public Builder kind(Integer kind) {
            this.kind = kind;
            return this;
        }

        public Builder clientIp(String clientIp) {
            this.clientIp = clientIp;
            return this;
        }

        public Builder outSid(String outSid) {
            this.outSid = outSid;
            return this;
        }

        public Builder trade(Trade trade) {
            this.trade = trade;
            return this;
        }

        public Builder logBuilder(AbsLogBuilder logBuilder) {
            this.logBuilder = logBuilder;
            return this;
        }

        public Builder packmaOuterIds(String packmaOuterIds) {
            this.packmaOuterIds = packmaOuterIds;
            return this;
        }

        public Builder packStaff(Staff packStaff) {
            this.packStaff = packStaff;
            return this;
        }

        public Builder actualVolume(String actualVolume) {
            this.actualVolume = actualVolume;
            return this;
        }

        public Builder actualLengthWidthAndHeight(String actualLengthWidthAndHeight) {
            this.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
            return this;
        }

        public Builder asyncCalcFreight(boolean asyncCalc) {
            this.asyncCalcFreight = asyncCalc;
            return this;
        }
    }

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public Integer getKind() {
        return kind;
    }

    public void setKind(Integer kind) {
        this.kind = kind;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getScanType() {
        return scanType;
    }

    public void setScanType(Integer scanType) {
        this.scanType = scanType;
    }

    public Trade getTrade() {
        return trade;
    }

    public void setTrade(Trade trade) {
        this.trade = trade;
    }

    public AbsLogBuilder getLogBuilder() {
        return logBuilder;
    }

    public void setLogBuilder(AbsLogBuilder logBuilder) {
        this.logBuilder = logBuilder;
    }

    public String getPackmaOuterIds() {
        return packmaOuterIds;
    }

    public void setPackmaOuterIds(String packmaOuterIds) {
        this.packmaOuterIds = packmaOuterIds;
    }

    public Staff getPackStaff() {
        return packStaff;
    }

    public void setPackStaff(Staff packStaff) {
        this.packStaff = packStaff;
    }

    public String getActualVolume() {
        return actualVolume;
    }

    public void setActualVolume(String actualVolume) {
        this.actualVolume = actualVolume;
    }

    public String getActualLengthWidthAndHeight() {
        return actualLengthWidthAndHeight;
    }

    public void setActualLengthWidthAndHeight(String actualLengthWidthAndHeight) {
        this.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean isAsyncCalcFreight() {
        return asyncCalcFreight;
    }

    public void setAsyncCalcFreight(boolean asyncCalcFreight) {
        this.asyncCalcFreight = asyncCalcFreight;
    }
}
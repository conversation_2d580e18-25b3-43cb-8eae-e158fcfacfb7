package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.List;

/**
 * 订单物流跟踪信息封装
 *
 * <AUTHOR>
 * @since 16/3/28
 */
public class TradeLogisticsTrace implements Serializable {
    /**
     * 物流公司名
     */
    private String companyName;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 状态
     */
    private String status;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 物流详情
     */
    private List<TradeTransitStepInfo> traceList;

    /**
     * url
     */
    private String url;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public List<TradeTransitStepInfo> getTraceList() {
        return traceList;
    }

    public void setTraceList(List<TradeTransitStepInfo> traceList) {
        this.traceList = traceList;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
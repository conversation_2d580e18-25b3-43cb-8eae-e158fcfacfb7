package com.raycloud.dmj.domain.trades.utils;

import com.alibaba.fastjson.*;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.consign.SupportAppendUploadConfig;
import com.raycloud.dmj.domain.diamond.trade.FakeOrderConfig;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum;
import com.raycloud.dmj.domain.trade.rulematch.AiTrade;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.TradeConstants.*;
import static com.raycloud.dmj.domain.trade.common.TradeTimeUtils.*;
import static com.raycloud.dmj.domain.utils.CommonConstants.*;

/**
 * 有关 {@link Trade}的订单工具类
 *
 * <AUTHOR>
 */
public class TradeUtils {

    public static char SEPARATOR_TILDE = '~';

    public static char SEPARATOR_POUND = '#';

    public static char SEPARATOR_BUCK = '$';

    public static String PREFIX_VIP_SOV = "SV";

    public static String PREFIX_WD = "WD-SECRET:";

    private static final Integer ENABLE_STATUS = 1;

    public static final List<String> PDD_TEME_CREATED_STATUS = Lists.newArrayList("pddtemu_0_2", "pddtemu_1_2", "pddtemu_2_2", "pddtemu_3_2", "pddtemu_5_2", "pddtemu_6_2", "pddtemu_7_2", "pddtemu_8_2", "pddtemu_9_2");

    //卖家备注异常生效时间
    public static Timestamp SellerMemoExceptionBeginTime = Timestamp.valueOf("2018-11-24 12:00:00");

    private static final char[] chars = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    private static final Logger logger = Logger.getLogger(TradeUtils.class);

    private static final Pattern BASE64_PATTERN = Pattern.compile("^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$");

    private static final String TRADE_TYPE_REISSUE = "reissue";//补发单type
    private static final String TRADE_TYPE_CHANGEITEM = "changeitem";//换货单type

    //半日生鲜达
    private final static String FRESH_HALF_DAY = "fresh_half_day";
    //菜鸟仓自流转
    private final static String CAINIAO_WAREHOUSE = "cainiao_warehouse";
    //菜鸟仓自发
    private final static String CAINIAO_WAREHOUSE_ZF = "cainiao_zf";

    public static final Set<String> needTradeExtSource = new HashSet<>();

    private static final Set<ExceptEnum> parseExceptEnums = new HashSet<>();
    private static final Set<String> ignoreLostMsgPlatforms = new HashSet<>();

    private static final List<String> platformFxSourceList = Arrays.asList(
            PLAT_FORM_TYPE_HAOSHIQI,
            PLAT_FORM_TYPE_1889,
            PLAT_FORM_TYPE_1688,
            PLAT_FORM_TYPE_QIMEN,
            PLAT_FORM_TYPE_XIAOMANG,
            PLAT_FORM_TYPE_JD,
            PLAT_FORM_TYPE_JD_VC
    );

    static {
        initNeedTradeExtSource();
        initParseExceptEnums();
        initIgnoreLostMsgPlatforms();
    }

    private static void initIgnoreLostMsgPlatforms() {
        // 不需要校验信息缺失的平台
        ignoreLostMsgPlatforms.add(PLAT_FORM_TYPE_TEMU);
        ignoreLostMsgPlatforms.add(PLAT_FORM_TYPE_TIK_TOK_QTG);
    }

    private static void initNeedTradeExtSource() {
        needTradeExtSource.add(PLAT_FORM_TYPE_PDD);
        needTradeExtSource.add(PLAT_FORM_TYPE_FXG);
        needTradeExtSource.add(PLAT_FORM_TYPE_FXG_DF);
        needTradeExtSource.add(PLAT_FORM_TYPE_JD);
        needTradeExtSource.add(PLAT_FORM_TYPE_TIAN_MAO);
        needTradeExtSource.add(PLAT_FORM_TYPE_TAO_BAO);
        needTradeExtSource.add(PLAT_FORM_TYPE_SUMAITONG);
        needTradeExtSource.add(PLAT_FORM_TYPE_SN);
        needTradeExtSource.add(PLAT_FORM_TYPE_SYS);
        needTradeExtSource.add(PLAT_FORM_TYPE_HAOSHIQI);
        needTradeExtSource.add(PLAT_FORM_TYPE_QIMEN);
        needTradeExtSource.add(PLAT_FORM_TYPE_1889);
        needTradeExtSource.add(PLAT_FORM_TYPE_NEWFX);
        needTradeExtSource.add(PLAT_FORM_TYPE_LAZADA);
        needTradeExtSource.add(PLAT_FORM_TYPE_ALIHEALTH);
        needTradeExtSource.add(PLAT_FORM_TYPE_QINSI);
        needTradeExtSource.add(PLAT_FORM_TYPE_TMCS);
        needTradeExtSource.add(PLAT_FORM_TYPE_TMGJZY);
        needTradeExtSource.add(PLAT_FORM_TYPE_TMYP);
        needTradeExtSource.add(PLAT_FORM_TYPE_TAO_BAO_DF);
        needTradeExtSource.add(PLAT_FORM_TYPE_TXY);
        needTradeExtSource.add(PLAT_FORM_TYPE_POISON);
        needTradeExtSource.add(PLAT_FORM_TYPE_AKC);
        needTradeExtSource.add(PLAT_FORM_TYPE_EASTBUY);
        needTradeExtSource.add(PLAT_FORM_TYPE_YYJK);
    }


    private static void initParseExceptEnums() {
        parseExceptEnums.add(ExceptEnum.HALT);
        parseExceptEnums.add(ExceptEnum.BLACK_NICK);
        parseExceptEnums.add(ExceptEnum.SELLER_MEMO_UPDATE);
        parseExceptEnums.add(ExceptEnum.UNATTAINABLE);
        parseExceptEnums.add(ExceptEnum.PART_REFUND);
        parseExceptEnums.add(ExceptEnum.RISK);
        parseExceptEnums.add(ExceptEnum.SUITE_CHANGE);
        parseExceptEnums.add(ExceptEnum.UNIQUE_CODE_OFFSHELF);
        parseExceptEnums.add(ExceptEnum.COD_REPEAT);
        parseExceptEnums.add(ExceptEnum.DELIVER_EXCEPT);
        parseExceptEnums.add(ExceptEnum.PDD_STOCK_OUT);
        parseExceptEnums.add(ExceptEnum.WAITING_RETURN_WMS);
        parseExceptEnums.add(ExceptEnum.WAIT_MERGE);
        parseExceptEnums.add(ExceptEnum.ITEM_PROCESS);
        parseExceptEnums.add(ExceptEnum.FX_AMBIGUITY);
        parseExceptEnums.add(ExceptEnum.FX_WAITPAY);
        parseExceptEnums.add(ExceptEnum.FX_UNAUDIT);
        parseExceptEnums.add(ExceptEnum.FX_REPULSE);
        parseExceptEnums.add(ExceptEnum.PLATFORM_FX_SPLIT_EXCEPT);
        parseExceptEnums.add(ExceptEnum.TMCS_STOCK_OUT);
        parseExceptEnums.add(ExceptEnum.TMGJZY_STOCK_OUT);
        parseExceptEnums.add(ExceptEnum.POISON_NOT_MATCH_EXPRESS_TEMPLATE);
        parseExceptEnums.add(ExceptEnum.PLATFORM_WAREHOUSE_MATCH);
        parseExceptEnums.add(ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH);
        parseExceptEnums.add(ExceptEnum.OUTSID_RECOVERY_FAIL);
        parseExceptEnums.add(ExceptEnum.REFUND_ITEM_NUM_EXCEPT);
        parseExceptEnums.add(ExceptEnum.PART_PAY_EXCEPT);
        parseExceptEnums.add(ExceptEnum.ONLINE_LOCK);
        parseExceptEnums.add(ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        parseExceptEnums.add(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT);
        parseExceptEnums.add(ExceptEnum.CAI_GOU_TRADE_EXCEPT);
        parseExceptEnums.add(ExceptEnum.SMTQTG_UN_CONFIRM);
        parseExceptEnums.add(ExceptEnum.PO_JIA_EXCEPT);
    }

    private static final List<String> SUPPORT_NON_OUT_SID_PLATS = Lists.newArrayList(PLAT_FORM_TYPE_SHEIN, PLAT_FORM_TYPE_SMTQTG, PLAT_FORM_TYPE_TEMU, PLAT_FORM_TYPE_SHOPEE_QTG, PLAT_FORM_TYPE_TIKTOK_QTG,PLAT_FORM_TYPE_JOOM,PLAT_FORM_TYPE_OZON);

    public static boolean isSupportNonOutSid(Trade trade) {
        return SUPPORT_NON_OUT_SID_PLATS.contains(trade.getSource()) || SUPPORT_NON_OUT_SID_PLATS.contains(trade.getSplitSource());
    }

    public static final String randomUUID(int length) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < length; i++) {
            buf.append(chars[RandomUtils.nextInt(0, chars.length)]);
        }
        return buf.toString();
    }

    /**
     * 模糊订单中敏感信息
     *
     * @param trades
     */
    public static void blurTrades(List<Trade> trades) {
        for (Trade trade : trades) {
            blurTrade(trade);
        }
    }

    /**
     * 模糊订单中敏感信息
     *
     * @param trade
     */
    public static void blurTrade(Trade trade) {
        trade.setReceiverAddress(StringBlurUtil.nonBlurStart(trade.getReceiverAddress(), 4));
        trade.setReceiverName(StringBlurUtil.nonBlurStart(trade.getReceiverName(), 1));
        trade.setBuyerNick(StringBlurUtil.nonBlurStart(trade.getBuyerNick(), 2));
        trade.setReceiverMobile(StringBlurUtil.nonBlurEnd(trade.getReceiverMobile(), 4));
        trade.setReceiverPhone(StringBlurUtil.nonBlurEnd(trade.getReceiverPhone(), 4));
    }


    public static void blurFullTrades(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        for (Trade trade : trades) {
            trade.setReceiverAddress("****");
            trade.setReceiverName("****");
            trade.setBuyerNick("****");
            trade.setReceiverMobile("****");
            trade.setReceiverPhone("****");
        }
    }



    public static boolean ifSensitived(String data) {
        if (StringUtils.isBlank(data)) {
            return false;
        }
        if (data.contains("*")) {
            return true;
        }
        return false;
    }

    public static boolean configHolderIsNull(Staff staff) {
        if (ConfigHolder.GLOBAL_CONFIG == null || ConfigHolder.GLOBAL_CONFIG.getModuleSwitch() == null || ConfigHolder.GLOBAL_CONFIG
                .getModuleSwitch().getTrade() == null || ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getSortCompanyIds() == null
                || staff == null || staff.getCompanyId() == null) {
            return true;
        }
        return false;
    }

    public static void blurTradePlatformExport(Trade trade, Staff staff) {
        if (platformMatch(staff, trade, PLAT_FORM_TYPE_PDD) || platformMatch(staff, trade, PLAT_FORM_TYPE_JD)) {
            trade.setReceiverAddress("****");
            trade.setReceiverName("****");
            trade.setReceiverMobile("****");
            trade.setReceiverPhone("****");
            trade.setBuyerNick("****");
        }
    }

    public static boolean platformMatch(Staff staff, Trade trade, String... platForms) {
        for (String platForm : platForms) {
            if (StringUtils.equals(trade.getSource(), platForm)) {
                return true;
            }
            if (StringUtils.equals(trade.getSubSource(), platForm)) {
                return true;
            }
            if (staff.getUserIdMap() != null) {
                User user = staff.getUserIdMap().get("newfx".equalsIgnoreCase(trade.getSource()) ? trade.getTaobaoId() : trade.getUserId());
                if (user != null && StringUtils.equals(user.getSource(), platForm)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 将套件下的单品提取出来直接放在trade下
     *
     * @return
     */
    public static List<Trade> toFullTrades(List<Trade> trades, boolean needRemoveSuitSelf) {
        for (Trade trade : trades) {
            TradeUtils.setOrders(trade, OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(trade), needRemoveSuitSelf));
        }
        return trades;
    }

    /**
     * 将套件下的单品提取出来直接放在trade下
     *
     * @return
     */
    public static List<Trade> toFullTradesOnlySuit(List<Trade> trades, boolean needRemoveSuitSelf) {
        for (Trade trade : trades) {
            List<Order> orderList = new ArrayList<>();
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                List<Order> suits = order.getSuits();
                if (CollectionUtils.isNotEmpty(suits) && order.isSuit()) {
                    if (!needRemoveSuitSelf) {
                        orderList.add(order);
                    }
                    for (Order suit : suits) {
                        suit.setItemSuitRemark(StringUtils.isNotBlank(OrderUtils.getSysRemark(suit)) ? OrderUtils.getSysRemark(suit) : OrderUtils.getSysRemark(order));
                        suit.setItemSuitOuterId(order.getSysOuterId());
                        //把套件的附表信息写到套件明细中
                        suit.setOrderExt(order.getOrderExt());
                        //需要把系统修改商品的状态写到套件明细里
                        suit.setSysItemChanged(order.getSysItemChanged());
                        // 保存套件商品信息，有大用
                        suit.setSuitSortSkuId(order.getSkuSysId() == null || order.getSkuSysId() <= 0 ? null : order.getSkuSysId());
                        suit.setSuitSortSysId(order.getItemSysId() == null || order.getItemSysId() <= 0 ? null : order.getItemSysId());
                        orderList.add(suit);
                    }
                } else {
                    orderList.add(order);
                }
            }
            TradeUtils.setOrders(trade, orderList);
        }
        return trades;
    }

    /**
     * 过滤出合单
     *
     * @return
     */
    public static List<Trade> filterMergeTrades(Staff staff, List<Trade> trades) {
        ArrayList<Trade> mergeTrades = new ArrayList<>();
        if (trades == null || trades.isEmpty()) {
            return mergeTrades;
        }
        for (Trade trade : trades) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                mergeTrades.add(trade);
            }
        }
        return mergeTrades;
    }

    public static List<Trade> filterMergeSubTrades(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> mergeSubTrades = new ArrayList<>();
        for (Trade trade : trades) {
            if (isMergeSub(staff, trade)) {
                mergeSubTrades.add(trade);
            }
        }
        return mergeSubTrades;
    }

    /**
     * 过滤出非合单 以及 合单中的主单
     */
    public static List<Trade> filterUnMergeOrMergeMainTrades(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> unMergeOrMergeMainTrades = new ArrayList<>();
        for (Trade trade : trades) {
            if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) || (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMergeMain(staff, trade))) {
                unMergeOrMergeMainTrades.add(trade);
            }
        }
        return unMergeOrMergeMainTrades;
    }


    /**
     * 合单分组
     *
     * @return
     */
    public static <T extends Trade> Map<Long, List<Trade>> groupByMergeSid(List<T> trades) {
        Map<Long, List<Trade>> sid2MergeTrades = new HashMap<>();
        if (trades == null || trades.isEmpty()) {
            return sid2MergeTrades;
        }
        for (Trade trade : trades) {
            sid2MergeTrades.computeIfAbsent(trade.getMergeSid(), k -> new ArrayList<>()).add(trade);
        }
        return sid2MergeTrades;
    }

    /**
     * 按店铺分组
     *
     * @return
     */
    public static Map<Long, List<Trade>> groupByUserId(List<Trade> trades) {
        Map<Long, List<Trade>> userId2Trades = new HashMap<Long, List<Trade>>();
        if (trades == null || trades.size() == 0) {
            return userId2Trades;
        }
        for (Trade trade : trades) {
            List<Trade> userTrades = userId2Trades.get(trade.getUserId());
            if (userTrades == null) {
                userTrades = new ArrayList<Trade>();
                userId2Trades.put(trade.getUserId(), userTrades);
            }
            userTrades.add(trade);
        }
        return userId2Trades;
    }

    /**
     * 按DestId分组
     *
     * @return
     */
    public static Map<Long, List<Trade>> groupByDestId(List<Trade> trades) {
        Map<Long, List<Trade>> userId2Trades = new HashMap<Long, List<Trade>>();
        if (trades == null || trades.size() == 0) {
            return userId2Trades;
        }
        for (Trade trade : trades) {
            List<Trade> destTrades = userId2Trades.get(trade.getDestId());
            if (destTrades == null) {
                destTrades = new ArrayList<Trade>();
                userId2Trades.put(trade.getDestId(), destTrades);
            }
            destTrades.add(trade);
        }
        return userId2Trades;
    }


    /**
     * 按SourceId分组
     *
     * @return
     */
    public static Map<Long, List<Trade>> groupBySourceId(List<Trade> trades) {
        Map<Long, List<Trade>> userId2Trades = new HashMap<Long, List<Trade>>();
        if (trades == null || trades.size() == 0) {
            return userId2Trades;
        }
        for (Trade trade : trades) {
            List<Trade> destTrades = userId2Trades.get(trade.getSourceId());
            if (destTrades == null) {
                destTrades = new ArrayList<Trade>();
                userId2Trades.put(trade.getSourceId(), destTrades);
            }
            destTrades.add(trade);
        }
        return userId2Trades;
    }


    /**
     * 按仓库分组
     */
    public static Map<Long, List<Trade>> groupByWarehouseId(List<Trade> trades) {
        Map<Long, List<Trade>> warehouseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                warehouseMap.computeIfAbsent(NumberUtils.nvlLong(trade.getWarehouseId(), -1L), k -> new ArrayList<>()).add(trade);
            }
        }
        return warehouseMap;
    }


    /**
     * 按照userId对订单归类
     *
     * @param trades
     * @return
     */
    public static <T extends Trade> Map<Long, List<Trade>> toMapByUserId(List<T> trades) {
        Map<Long, List<Trade>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                map.computeIfAbsent(trade.getUserId(), k -> new ArrayList<>()).add(trade);
            }
        }
        return map;
    }

    public static <T extends Trade> Map<Long, Trade> toMapBySid(List<T> trades) {
        Map<Long, Trade> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (T trade : trades) {
                map.put(trade.getSid(), trade);
            }
        }
        return map;
    }

    public static <T extends Trade> Map<String, Trade> toMapByTid(List<T> trades) {
        Map<String, Trade> map = new HashMap<>(trades.size(), 1);
        if (trades != null && trades.size() > 0) {
            trades.forEach(trade -> map.put(trade.getTid(), trade));
        }
        return map;
    }

    /**
     * {@link TbTrade}集合转换为 {@link Trade}集合
     *
     * @param trades
     * @return
     */
    public static List<Trade> toTrades(List<TbTrade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> list = new ArrayList<>(trades.size());
        if (trades.size() > 0) {
            list.addAll(trades);
        }
        return list;
    }

    public static List<Trade> childToTrades(List<? extends Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> list = new ArrayList<>(trades.size());
        if (trades.size() > 0) {
            list.addAll(trades);
        }
        return list;
    }

    public static <T extends Trade> List<Trade> toTrades(T... array) {
        List<Trade> trades = new ArrayList<>(array.length);
        trades.addAll(Arrays.asList(array));
        return trades;
    }


    public static void setOrders(Trade trade, Trade origin) {
        getOrders4Trade(trade).addAll(getOrders4Trade(origin));
    }

    @SuppressWarnings("unchecked")
    public static void setOrders(Trade trade, List<Order> orders) {
        if (orders != null) {
            for (Order order : orders) {
                order.setExceptData(TradeExceptUtils.getTradeExceptData(trade));
            }
            ((Orderable<Order>) trade).setOrders(orders);
        }
    }

    public static void setOrders(Trade trade, List<Order> orders, boolean fillExcept) {
        if (orders != null) {
            for (Order order : orders) {
                if (fillExcept) {
                    order.setExceptData(TradeExceptUtils.getTradeExceptData(trade));
                }
            }
            ((Orderable<Order>) trade).setOrders(orders);
        }
    }

    public static <T extends Trade> Long[] toSids(Collection<T> trades) {
        Long[] sids = new Long[trades.size()];
        int index = 0;
        for (Trade trade : trades) {
            sids[index++] = trade.getSid();
        }
        return sids;
    }

    public static <T extends Trade> String toSidsStr(Collection<T> trades) {
        return trades.stream().map(Trade::getSid).map(String::valueOf).collect(Collectors.joining(","));
    }

    public static <T extends Trade> String[] toOutSids(List<T> trades) {
        return trades.stream().filter(t -> StringUtils.isNotBlank(t.getOutSid())).map(Trade::getOutSid).toArray(String[]::new);
    }

    public static <T extends Trade> List<Long> toSid(T trade) {
        List<Long> sid = new ArrayList<Long>();
        sid.add(trade.getSid());
        return sid;
    }

    public static <T extends Trade> Long[] toSids(T trade) {
        Long[] sids = new Long[1];
        sids[0] = trade.getSid();
        return sids;
    }

    public static <T extends Trade> String[] toTids(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new String[0];
        }
        Set<String> tids = new HashSet<String>(trades.size());
        for (Trade trade : trades) {
            tids.add(trade.getTid());
        }
        return tids.toArray(new String[tids.size()]);
    }

    /**
     * 通过Order获取Tids，用于合单订单获取
     *
     * @return
     */
    public static <T extends Trade> String[] toTidsFromOrders(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new String[0];
        }
        Set<String> tids = new HashSet<String>(trades.size());
        for (Trade trade : trades) {
            tids.add(trade.getTid());
            List<Order> orders = getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orders)) {
                orders.forEach(t -> tids.add(t.getTid()));
            }
        }
        return tids.toArray(new String[0]);
    }

    public static <T extends Trade> List<String> toTidList(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<String> tids = new ArrayList<String>(trades.size());
        for (Trade trade : trades) {
            tids.add(trade.getTid());
        }
        return tids;
    }

    public static <T extends Trade> Set<String> toTidSet(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new HashSet<>();
        }
        Set<String> tids = new HashSet<>(trades.size());
        for (Trade trade : trades) {
            tids.add(trade.getTid());
        }
        return tids;
    }

    public static <T extends Trade> List<Long> toSidList(Collection<T> trades) {
        List<Long> sidList = new ArrayList<>();
        if (trades != null && trades.size() > 0) {
            for (Trade trade : trades) {
                sidList.add(trade.getSid());
            }
        }
        return sidList;
    }

    public static <T extends Trade> Set<Long> toSidSet(List<T> trades) {
        Set<Long> sidList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                sidList.add(trade.getSid());
            }
        }
        return sidList;
    }

    public static <T extends Trade> List<Long> toMergeSidList(List<T> trades) {
        List<Long> sidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                sidList.add(trade.getMergeSid());
            }
        }
        return sidList;
    }

    public static <T extends Trade> Set<Long> toMergeSidSet(List<T> trades) {
        Set<Long> sidList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                sidList.add(trade.getMergeSid());
            }
        }
        return sidList;
    }

    public static <T extends Trade> List<Long> toSplitSidList(List<T> trades) {
        List<Long> sidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                sidList.add(trade.getSplitSid());
            }
        }
        return sidList;
    }

    public static <T extends Trade> Set<Long> toSplitSidSet(List<T> trades) {
        Set<Long> sidList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                sidList.add(trade.getSplitSid());
            }
        }
        return sidList;
    }


    public static List<Order> getOrders4Trade(List<Trade> trades) {
        List<Order> orders = new ArrayList<Order>();
        for (Trade trade : trades) {
            orders.addAll(getOrders4Trade(trade));
        }
        return orders;
    }

    public static List<Order> getGiftOrders4Trade(List<Trade> trades) {
        List<Order> giftOrders = new ArrayList<>();
        List<Order> allOrders = getOrders4Trade(trades);
        if (CollectionUtils.isEmpty(allOrders)) {
            return giftOrders;
        }

        for (Order order : allOrders) {
            if (order.isGift()) {
                giftOrders.add(order);
            }
        }
        return giftOrders;
    }

    public static List<GiftOrder> getGiftOrders(List<Trade> trades) {
        List<GiftOrder> giftOrders = new ArrayList<GiftOrder>();
        for (Trade trade : trades) {
            List<GiftOrder> giftOrdersTemp = trade.getGiftOrders();
            if (CollectionUtils.isNotEmpty(giftOrdersTemp)) {
                giftOrders.addAll(giftOrdersTemp);
            }
        }
        return giftOrders;
    }

    @SuppressWarnings("unchecked")
    public static List<Order> getOrders4Trade(Trade trade) {
        List<Order> orders = ((Orderable<Order>) trade).getOrders();
        for (Order order : orders) {
            TradeExceptUtils.fillOrderExceptData(trade, order, true);
        }
        return orders;
    }

    public static List<Order> getOrders4Trade(Trade trade, boolean fillExcept) {
        List<Order> orders = ((Orderable<Order>) trade).getOrders();
        for (Order order : orders) {
            TradeExceptUtils.fillOrderExceptData(trade, order, fillExcept);
        }
        return orders;
    }

    /**
     * 得到 非退款的子订单
     */
    @SuppressWarnings("unchecked")
    public static List<Order> getUnRefundOrders4Trade(Trade trade) {
        List<Order> orders = new ArrayList<>();
        for (Order order : ((Orderable<Order>) trade).getOrders()) {
            if (Order.REFUND_WAIT_SELLER_AGREE.equals(order.getStatus()) || Order.REFUND_WAIT_BUYER_RETURN_GOODS.equals(order.getStatus()) || Order.REFUND_SELLER_REFUSE_BUYER.equals(order.getStatus()) || Order.REFUND_SUCCESS.equals(order.getStatus())) {
                continue;
            }
            orders.add(order);
        }
        return orders;
    }


    /**
     * 重新计算订单平台类型
     * 若子订单全部是系统子订单,则该笔订单为系统订单,否则保持原source不变
     *
     * @param trade
     */
    public static String getTradeSource(Trade trade) {
        if(TradeUtils.isGxOrMixTrade(trade)){
            return trade.getSource();
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (!PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                return order.getSource();
            }
        }
        return orders.isEmpty() ? trade.getSource() : PLAT_FORM_TYPE_SYS;
    }


    public static TbTrade initTradeUserInfo(TbTrade trade, User user) {
        trade.setSource(user.getSource());
        trade.setCompanyId(user.getCompanyId());
        trade.setUserId(user.getId());
        trade.setTaobaoId(user.getTaobaoId());
        trade.setSubSource(user.getSubSource());
        return trade;
    }

    public static TradeExt initTradeExtUserInfo(TradeExt tradeExt, User user) {
        tradeExt.setCompanyId(user.getCompanyId());
        tradeExt.setUserId(user.getId());
        return tradeExt;
    }

    /**
     * 判断对应的订单状态是否为合单
     *
     * @param trade
     * @return
     */
    @Deprecated
    public static boolean isMerge(Trade trade) {
        Staff staff = new Staff();
        staff.setCompanyId(trade.getCompanyId());
        return com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade);
    }

    /**
     * 判断对应订单是否为合单子单
     */
    public static boolean isMergeSub(Staff staff, Trade trade) {
        if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            return false;
        }
        return !Objects.equals(trade.getMergeSid(), trade.getSid());
    }


    /**
     * 是否是分销类型订单(包含供销订单/分销订单)
     *
     * @return
     */
    public static boolean isFxSource(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1;
    }

    /**
     * 是否是普通类型订单
     *
     * @return
     */
    public static boolean isNorMalSource(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 0;
    }

    /**
     * 非出库单+非供分销+非补发/换货订单
     *
     * @return
     */
    public static boolean isCommonSource(Trade trade) {
        return (trade.getConvertType() == null || trade.getConvertType() == 0) && !TRADE_TYPE_REISSUE.equals(trade.getType()) && !TRADE_TYPE_CHANGEITEM.equals(trade.getType()) && !trade.isOutstock();
    }

    /**
     * 是否包含
     *
     * @param t
     * @param v
     * @return
     */
    public static boolean isContainV(Trade t, int v) {
        return t != null && t.getV() != null && (t.getV() | v) - t.getV() == 0;
    }

    /**
     * 是否包含标志位
     *
     * @param t
     * @param v
     * @return
     * @see com.raycloud.dmj.domain.TradeConstants
     */
    public static boolean ifContainV(Trade t, int v) {
        return t != null && t.getV() != null && (t.getV() | v) - t.getV() == 0;
    }

    public static boolean ifContainV(Long originV, int v) {
        return originV != null && (originV | v) - originV == 0;
    }


    public static boolean ifAllowEditTradeItem(Trade trade, String sysStatus) {
        if (TradeUtils.isFxTrade(trade) && Objects.equals(MDC.get(TRADE_ITEM_EDIT_FROM_GX), "1")) {
            return (Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus) || Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus));
        }
        return Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus);
    }

    /**
     * 供销拆单子单不需要打上待付款异常
     *
     * @param t
     * @return
     */
    public static boolean ifNeedAddWaitPayExcept(Trade t){
        return !(TradeUtils.isGxOrMixTrade(t) && TradeUtils.isSplit(t) && !Objects.equals(t.getSid(),t.getSplitSid()));
    }


    /**
     * 是否是分销订单(仅包含分销订单)
     *
     * @return
     */
    public static boolean isFxTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getSourceId() != null && trade.getBelongType() != null && trade.getBelongType() == 1;
    }


    /**
     * 是否是供销订单(仅包含供销订单)
     *
     * @return
     */
    public static boolean isGxTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getDestId() != null && trade.getBelongType() != null && trade.getBelongType() == 2;
    }

    /**
     * 是否奇门Jitx订单
     */
    public static boolean isQimenJitxTrade(Trade trade) {
        return PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource());
    }

    /**
     * 是否包含供销订单
     *
     * @return
     */
    public static boolean hasGxTrade(Collection<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                if (isGxTrade(trade)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否是供销且分销订单(仅包含供销且分销订单)
     *
     * @return
     */
    public static boolean isGxAndFxTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getSourceId() != null && trade.getDestId() != null && trade.getBelongType() != null && trade.getBelongType() == 3;
    }

    /**
     * 是否是分销或分销且供销订单(仅包含分销或分销且供销订单)
     *
     * @return
     */
    public static boolean isFxOrMixTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getSourceId() != null && trade.getBelongType() != null && (trade.getBelongType() == 1 || (trade.getBelongType() == 3 && trade.getDestId() != null));
    }

    /**
     * 是否是供销或分销且供销订单(仅包含供销或分销且供销订单)
     *
     * @return
     */
    public static boolean isGxOrMixTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getDestId() != null && trade.getBelongType() != null && (trade.getBelongType() == 2 || (trade.getBelongType() == 3 && trade.getSourceId() != null));
    }

    /**
     * 分销指定供销的快递模板，是否修改了模版
     *  1.分销没有指定返回false
     *
     * 分销的快递模板和原始的供销的快递模板不一样就是修改了。
     *
     * @param trade
     * @return
     */
    public static boolean isFxUpdateTemplate(Trade trade){
        if(trade == null || trade.getSourceTrade() == null || trade.getOrigin() == null){
            return false;
        }
        Trade sourceTrade = trade.getSourceTrade();
        Trade originTrade = trade.getOrigin();

        if(sourceTrade.getTemplateId() == null || sourceTrade.getTemplateId() <= 0){
            return false;
        }

        if(originTrade.getTemplateId() != null && originTrade.getTemplateId() > 0 && sourceTrade.getTemplateId().equals(originTrade.getTemplateId())){
            return false;
        }


        return true;

    }

    /**
     * 校验供分销的层级，是否正确
     * 1级：分销 - 供销
     * 2级：分销 - 分销且供销 - 供销
     * 3级：分销 - 分销且供销 - 分销且供销 - 供销
     *
     * @param level 层级
     * @return true：正确
     */
    public static boolean checkFxAndGxLevel(int level) {
        if (level >= 4) {
            logger.warn("供分销层级最大不超过3级！");
            return false;
        }
        return true;
    }

    /**
     * 是否pddtemu订单（因为不存在拆单等情况，所以不考虑手共拆分平台单问题）
     *
     * @param trade
     * @return
     */
    public static boolean isPddtemuTrade(Trade trade) {
        return trade.getSource() != null && trade.getSource().equals(PLAT_FORM_TYPE_TEMU);
    }

    /**
     * 是否pddtemu订单且已取消或者已退货
     *
     * @param trade
     * @return
     */
    public static boolean isPddTemuTradeCreatedStatus(Trade trade) {
        return trade.getSource() != null && trade.getSource().equals(PLAT_FORM_TYPE_TEMU) && (PDD_TEME_CREATED_STATUS.contains(trade.getStatus()));
    }


    /**
     * 是否pddtemu
     *
     * @param trade
     * @return pddtemu_0_2, pddtemu_1_2, pddtemu_2_2, pddtemu_3_2, pddtemu_5_2, pddtemu_6_2, pddtemu_7_2, pddtemu_8_2, pddtemu_9_2,
     * -->待创建：pddtemu_0_0,pddtemu_1_0
     * -->创建中：pddtemu_0_1,pddtemu_1_1
     */
    public static boolean isPddtemuTradeCancelOrRefund(Trade trade) {
        return trade.getSource() != null && trade.getSource().equals(PLAT_FORM_TYPE_TEMU) && (trade.getStatus().equals("4") || trade.getStatus().equals("5"));
    }


    /**
     * 是否是平台分销订单(包含全部代发和混合单)
     *
     * @return
     */
    public static boolean isPlatformFxSource(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 2;
    }


    /**
     * 是否是奇门供销订单
     *
     * @return
     */
    public static boolean isQimenFxSource(Trade trade) {
        return isQimenFxSource(trade, true) || getIfQimenMix(trade);
    }


    /**
     * 是否抖超订单
     *
     * @param trade
     * @return
     */
    public static boolean isFxgcsSource(Trade trade) {
        return PLAT_FORM_TYPE_FXG_CS.equals(trade.getSource());
    }

    public static boolean isQimenFxSource(Trade trade, boolean containMix) {
        boolean isQiMenGxSource = trade.getConvertType() != null && trade.getConvertType() == 3 && trade.getBelongType() != null && trade.getBelongType() == 2;
        if (containMix) {
            return isQiMenGxSource || isQimenMixSource(trade);
        }
        return isQiMenGxSource;
    }

    public static boolean isQimenMixSource(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 1 && trade.getBelongType() != null && trade.getBelongType() == 1 && getIfQimenMix(trade);
    }

    public static boolean getIfQimenMix(Trade trade) {
        return trade.getV() != null && (trade.getV() | TradeConstants.V_IF_FX_FROM_QIMEN) - trade.getV() == 0;
    }

    public static boolean getIfGxTradeFromQimen(Trade trade) {
        return trade.getV() != null && (trade.getV() | TradeConstants.V_IF_MIX_FROM_QIMEN) - trade.getV() == 0;
    }

    /**
     * 奇门订单，是否走预扣模式
     *
     * @param trade
     * @return
     */
    public static boolean isQimenPayLock(Trade trade) {
        return trade.getV() != null && (trade.getV() | TradeConstants.V_TRADE_QIMEN_CASH_FLOW) - trade.getV() == 0;
    }

    public static Long getDistributorCompanyId(Trade trade) {
        if (isQimenMixSource(trade)) {
            return trade.getCompanyId();
        }
        return trade.getSourceId();
    }

    public static boolean isQimenAndNotFx(Trade trade) {
        boolean isQiMenGxSource = trade.getConvertType() != null && trade.getConvertType() == 3 && trade.getBelongType() != null && trade.getBelongType() == 2;
        return ((trade.getV() != null && (trade.getV() | 128) - trade.getV() == 0) || isQiMenGxSource) && !isFxTrade(trade);
    }

    public static void setSourceId(Trade trade, Long sourceId) {
        boolean isQiMenGxSource = trade.getConvertType() != null && trade.getConvertType() == 3 && trade.getBelongType() != null && trade.getBelongType() == 2;
        if (isQiMenGxSource || getIfQimenMix(trade)) {
            return;
        }
        trade.setSourceId(sourceId);
    }

    /**
     * 不需要匹配系统分销属性。
     * 平台分销 || 档口 || 1688分销小站
     *
     * @param trade
     * @return
     */
    public static boolean notNeedMatchFxAttr(Trade trade) {
        return (TradeUtils.isPlatformFxSource(trade) || TradeUtils.isDangKou(trade) || TradeUtils.isAlibabaFxTrade(trade) || TradeUtils.ifContainV(trade, V_IF_1688_FX_ROLE));
    }

    /**
     * 奇门供销。通过订单状态，获取当前流水的执行节点
     * 先通过平台状态，在根据系统状态
     *
     * @param trade 需要限制为 奇门供销单
     * @return FxCashFlowModEnum
     */
    public static FxCashFlowModEnum getQiMenCashFlowType(Trade trade) {
        if (TradeStatusUtils.isAfterSendGoods(trade.getUnifiedStatus())) {
            return FxCashFlowModEnum.LOCK_CONSIGN;
        }
        //平台部分发货时，order只要存在平台未发货的，就表示还没有平台上传
        if (TradeStatus.TB_SELLER_CONSIGNED_PART.equals(trade.getUnifiedStatus())) {
            boolean upload = true;
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isEmpty(orders)) {
                upload = false;
            }
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                upload = upload && TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), null));
            }
            if (upload) {
                return FxCashFlowModEnum.LOCK_CONSIGN;
            }
        }
        if (TradeUtils.isAfterSendGoods(trade)) {
            return FxCashFlowModEnum.LOCK_CONSIGN;
        }
        if (Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus())) {
            return FxCashFlowModEnum.LOCK_ADD;
        }
        if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            return FxCashFlowModEnum.LOCK_AUDIT;
        }
        return FxCashFlowModEnum.LOCK_ADD;
    }

    /**
     * 是得物云仓订单
     *
     * @return
     */
    public static boolean isPoisonCloudWarehouseTrade(Trade trade) {
        return TradeTypeConstants.POISION_CLOUD_WAREHOUSE_TRADE_TYPE.equals(trade.getType()) && PLAT_FORM_TYPE_POISON.equals(trade.getSource());
    }


    /**
     * 是否是生鲜半日达订单
     *
     * @return
     */
    public static boolean isFreshHalfDayTrade(Trade trade) {
        return (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) && FRESH_HALF_DAY.equals(trade.getSubSource());
    }

    /**
     * 是否是菜鸟仓自流转
     *
     * @return
     */
    public static boolean isCainiaoWarehouseTrade(Trade trade) {
        return (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) && CAINIAO_WAREHOUSE.equals(trade.getSubSource())
                || PlatformUtils.is1688HyperLinkShipTrade(trade);
    }

    /**
     * 是否是菜鸟仓自发仓
     *
     * @return
     */
    public static boolean isCainiaoWarehouseZfTrade(Trade trade) {
        return (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) && CAINIAO_WAREHOUSE_ZF.equals(trade.getSubSource());
    }

    /**
     * 菜鸟仓 自流转-自发 转换
     *
     * @return
     */
    public static void cainiaoWarehouseConvert(Trade trade) {
        if (!CAINIAO_WAREHOUSE.equals(trade.getSubSource()) && !CAINIAO_WAREHOUSE_ZF.equals(trade.getSubSource())) {
            return;
        }
        if (CAINIAO_WAREHOUSE.equals(trade.getSubSource())) {
            trade.setSubSource(CAINIAO_WAREHOUSE_ZF);
            return;
        }
        if (CAINIAO_WAREHOUSE_ZF.equals(trade.getSubSource())) {
            trade.setSubSource(CAINIAO_WAREHOUSE);
            return;
        }
    }


    /**
     * 是否是平台分销订单（只包含全部代发）
     *
     * @return
     */
    public static boolean isPlatformFxTrade(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 2 && trade.getBelongType() != null && trade.getBelongType() == 1;
    }

    /**
     * 是否是平台分销订单(只包含混合单)
     *
     * @return
     */
    public static boolean isPlatformFxNormalTradeMix(Trade trade) {
        return trade.getConvertType() != null && trade.getConvertType() == 2 && trade.getBelongType() != null && trade.getBelongType() == 3;
    }

    /**
     * 分销店铺展示名称（涉及：供销订单店铺名展示、分销店铺列表展示）
     * 优先级为：对外展示名称 > 简称 > 点店名
     *
     * @param shop
     * @return
     */
    public static String getFxShopShowName(Shop shop) {
        if (StringUtils.isNotEmpty(shop.getExternalName())) {
            return shop.getExternalName();
        } else {
            if (Integer.valueOf(1).equals(shop.getSimpleTitleConfig())) {
                return StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle());
            } else {
                return shop.getTitle();
            }
        }
    }


    public static String getHandlerSource(Trade trade) {//得到
        if (isGxTrade(trade)) {
            return PLAT_FORM_TYPE_NEWFX;
        } else {
            return trade.getSource();
        }
    }


    public static String getPlatSource(Trade trade) {//得到
        if (isGxTrade(trade)) {
            String removePre = trade.getSource().substring(PLAT_FORM_TYPE_NEWFX.length());
            if (StringUtils.isNotEmpty(removePre)) {
                removePre = removePre.substring(1);
            }
            return removePre;
        } else {
            return trade.getSource();
        }
    }


    /**
     * 判断对应的订单状态是否为拆单
     *
     * @param trade
     * @return
     */
    public static boolean isSplit(Trade trade) {
        return (trade.getSplitSid() != null && trade.getSplitSid() > 0) || (trade.getSplitType() != null && TradeSplitEnum.SPLIT_SKU.getDbType() - trade.getSplitType() == 0);
    }

    /**
     * 判断对应订单是否为拆单子单
     */
    public static boolean isSplitSub(Trade trade) {
        if (!isSplit(trade)) {
            return false;
        }
        return !Objects.equals(trade.getSid(), trade.getSplitSid());
    }

    /**
     * 判断对应的订单状态是否为礼物订单
     */
    public static boolean isPresent(Trade trade) {
        return Objects.nonNull(trade.getTradeExt())
                && Objects.equals(1, trade.getTradeExt().get("isPresent"));
    }

    /**
     * 是拆单并且平台支持追加上传运单号(包括拆分的手工单)
     *
     * @param trade
     * @return
     */
    public static boolean isSupportAppendUpload(Staff staff, Trade trade) {

        if (PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) || PLAT_FORM_TYPE_QIMEN.equals(trade.getSplitSource()) || PLAT_FORM_TYPE_QIMEN.equals(trade.getSubSource())) {
            return true;
        }


        if (isSupportNonOutSid(trade)) {
            return true;
        }


        if (isSupportNonOutSid(trade)) {
            return true;
        }

        List<SupportAppendUploadConfig> configs = TradeConsignConfigDiamondUtils.getSupportAppendUploadConfigs();
        if (isSplit(trade) && CollectionUtils.isNotEmpty(configs)) {
            Map<String, SupportAppendUploadConfig> map = configs.stream().collect(Collectors.toMap(SupportAppendUploadConfig::getSource, a -> a));
            String source = getSource(staff, trade);
            if (map.keySet().contains(source)) {
                if (StringUtils.isNotBlank(map.get(source).getSubSource())) {
                    if (map.get(source).getSubSource().equals(trade.getSubSource())) {
                        return true;
                    } else {
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 支持全部运单号一次性全部上传的平台
     *
     * @param trade
     * @return
     */
    public static boolean isSupportOnceUpload(Staff staff, Trade trade) {
        List<String> configs = TradeConsignConfigDiamondUtils.getSupportOnceUploadConfigs();
        String source = getSource(staff, trade);
        if (isSplit(trade) && CollectionUtils.isNotEmpty(configs) && configs.contains(source)) {
            return true;
        }
        return false;
    }

    public static boolean isPresell(Trade trade) {
        return trade.getIsPresell() != null && trade.getIsPresell() - 1 == 0;
    }

    /**
     * 快递单是否打印
     */
    public static boolean isPrint(Trade trade) {
        return trade.getExpressPrintTime() != null && trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE);
    }

    /**
     * 是否为系统预售订单
     *
     * @param trade
     * @return
     */
    public static boolean isSysPresell(Trade trade) {
        return trade.getIsPresell() != null && trade.getIsPresell().equals(3);
    }

    public static boolean isAfterSendGoods(Trade trade) {
        return TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
    }

    public static String getTradeSysStatus(Trade trade) {
        return TradeStatusUtils.getTradeSysStatus(trade);
    }

    /**
     * 根据sid组装order与trade,并返回trade集合
     *
     * @param trades
     * @param orders
     * @return
     */
    public static <T extends Trade, O extends Order> List<Trade> assemblyBySid(List<T> trades, List<O> orders) {
        List<Trade> tradeList = new ArrayList<Trade>(trades.size());
        Map<Long, List<Order>> ordersBySid = OrderUtils.toMapBySid(OrderUtils.toTree(orders));
        for (Trade trade : trades) {
            List<Order> list = ordersBySid.get(trade.getSid());
            if (CollectionUtils.isNotEmpty(list)) {
                for (Order order : list) {
                    order.setExceptData(trade.getExceptData());
                }
                TradeUtils.getOrders4Trade(trade).addAll(list);
            }
            tradeList.add(trade);
        }
        return tradeList;
    }

    /**
     * 根据sid组装order与trade,并返回trade集合
     *
     * @param trades
     * @param orders
     * @return
     */
    public static <T extends Trade, O extends Order> Map<String, List<Trade>> assemblyBySid2Map(List<T> trades, List<O> orders) {
        //如果有拆单的话，一个tid可能对应多个trade
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        Map<Long, List<Order>> orderMap = OrderUtils.toMapBySid(orders);
        for (Trade trade : trades) {
            tradeMap.computeIfAbsent(trade.getTid(), k -> new ArrayList<>()).add(trade);
            List<Order> orderList = orderMap.get(trade.getSid());
            if (orderList == null) {
                continue;
            }
            setOrders(trade, OrderUtils.toTree(orderList));
        }
        return tradeMap;
    }

    /**
     * 根据tid组装order与trade,并返回trade集合
     * <p>
     * 对于拆单,由于多个trade共用一个tid
     * </p>
     *
     * @param trades
     * @param orders
     * @return
     */
    public static <T extends Trade, O extends Order> List<Trade> assemblyByTid(List<T> trades, List<O> orders) {
        Map<String, Trade> tradeMap = toMapByTid(trades);
        Map<Long, Order> orderMap = OrderUtils.toMap(orders);
        for (O order : orders) {
            if (order.isSuit(true)) {
                Order parent = orderMap.get(order.getCombineId());
                if (parent != null && parent.getSid() - order.getSid() == 0) {
                    if (parent.getSuits() == null) {
                        parent.setSuits(new ArrayList<Order>(5));
                    }
                    parent.getSuits().add(order);
                    continue;
                }
            }
            Trade trade = tradeMap.get(order.getTid());
            if (trade != null) {
                getOrders4Trade(trade).add(order);
            }
        }
        return new ArrayList<Trade>(tradeMap.values());
    }

    public static Set<Integer> parseExcept(Staff staff, Trade trade) {
        Set<Integer> except = new HashSet<Integer>();
        parseExcept(staff, trade, except);
        return except;
    }

    private static void parseExcept(Staff staff, Trade trade, Set<Integer> except) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (StringUtils.isBlank(trade.getSysStatus())) {
            except.add(IDX_TRADE_LOST_SYS_STATUS);
        }
        if (orders.isEmpty()) {
            except.add(IDX_LOST_ITEM);
        }
        boolean isWaitSellerSend = TradeStatusUtils.isWaitSellerSend(trade.getSysStatus());
        if (isWaitSellerSend) {//已发货、交易成功、交易关闭的订单不计算异常
            if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED)
                    && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())
                    || TradeStatusUtils.isWaitFinanceAudit(trade.getSysStatus())
                    || com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) || isSplit(trade))) {
                except.add(IDX_ADDRESS_CHANGED);
            }
            if ((trade.getType() == null || (!trade.getType().startsWith(TYPE_TRADE_OUT)
                    && !trade.getType().startsWith(TYPE_TRADE_DANGKOU)))
                    && !ignoreLostMsgPlatforms.contains(TradePlatformUtils.getTradeUserSource(staff, trade))
                    && isLostMsg(trade, orders)
                    && !("fds".equals(trade.getSubSource()) || PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource()))) {
                except.add(IDX_LOST_MSG);
            }
            if (TradeExceptUtils.isContainCustomExcept(staff, trade)) {//包含自定义异常的
                except.add(IDX_CUSTOM_EXCEPT);
            }
            for (ExceptEnum exceptEnum : parseExceptEnums) {
                if (TradeExceptUtils.isContainExcept(staff, trade, exceptEnum)) {
                    except.add((int) exceptEnum.getOldExceptEnum().getOldIdx());
                }
            }

        }
        for (Order order : orders) {
            //已删除的不计算
            if (order.getEnableStatus() == 0) {
                continue;
            }
            Set<Integer> exceptSet = OrderUtils.parseExcept(staff, order);
            if (isWaitSellerSend) {
                except.addAll(exceptSet);
            } else if (exceptSet.contains(IDX_REFUNDING)) {
                except.add(IDX_REFUNDING);
            }
        }
    }

    /**
     * 地址、手机/固话、收件人、商品信息缺失的订单
     */
    @Deprecated
    private static boolean isLostMsg(Trade trade, List<Order> orders) {
        return StringUtils.isBlank(trade.getPayment()) || // 实付金额缺失
                StringUtils.isBlank(trade.getReceiverName()) || // 收件人缺失
                StringUtils.isBlank(trade.getReceiverAddress()) || //收货地址缺失
                (StringUtils.isBlank(trade.getReceiverMobile()) && StringUtils.isBlank(trade.getReceiverPhone()));
    }

    /**
     * 第一个元素为异常状态值,大于0表示异常订单，第二个元素为退款状态值，大于0表示退款订单
     *
     * @param trade
     * @return
     */
    public static int getItemExcep(Staff staff, Trade trade, List<Trade> mergeList) {
        int itemExcep = TradeConstants.NO_EXCEPT;
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)) {
                    itemExcep |= TradeConstants.RELATION_CHANGED;
                }
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
                    itemExcep |= TradeConstants.UNALLOCATED;
                } else if ((order.getInsufficientCanceled() != null && order.getInsufficientCanceled() == 0) && (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus()))) {
                    itemExcep |= TradeConstants.INSUFFICIENT;
                }
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ITEM_CHANGED)) {
                    itemExcep |= TradeConstants.ITEM_CHANGED;
                }
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ITEM_SHUTOFF)) {
                    itemExcep |= TradeConstants.ITEM_SHUTOFF;
                }
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNIQUE_CODE_OFFSHELF)) {
                    itemExcep |= TradeConstants.UNIQUE_CODE_OFFSHELF;
                }
            }
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_REFUND)) {
            itemExcep |= TradeConstants.PART_REFUND;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
            itemExcep |= TradeConstants.PDD_RISKEXCEP;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED)) {
            itemExcep |= TradeConstants.ADDRESS_CHANGED;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_AMBIGUITY)) {
            itemExcep |= TradeConstants.FX_AMBIGUITY;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_WAITPAY)) {
            itemExcep |= TradeConstants.FX_WAITPAY;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_UNAUDIT)) {
            itemExcep |= TradeConstants.FX_UNAUDIT;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_REPULSE)) {
            itemExcep |= TradeConstants.FX_REPULSE;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PLATFORM_FX_SPLIT_EXCEPT)) {
            itemExcep |= TradeConstants.PLATFORM_FX_SPLIT_EXCEPT;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE)) {
            itemExcep |= TradeConstants.SELLER_MEMO_UPDATE;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.BLACK_NICK)) {
            itemExcep |= TradeConstants.BLACK_NICK;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.LOST_MSG)) {
            itemExcep |= TradeConstants.LOST_MESSAGE;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT)) {
            itemExcep |= TradeConstants.DELIVER_EXCEPT;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNIQUE_CODE_OFFSHELF)) {
            itemExcep |= TradeConstants.UNIQUE_CODE_OFFSHELF;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.SUITE_CHANGE)) {
            itemExcep |= TradeConstants.SUITE_QUANTITY_CHANGE;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ITEM_PROCESS)) {
            itemExcep |= TradeConstants.ITEM_PROCESS_EXCEP;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.COD_REPEAT)) {
            itemExcep |= TradeConstants.COD_REPEAT;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PDD_STOCK_OUT)) {
            itemExcep |= TradeConstants.PDD_STOCK_OUT;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAITING_RETURN_WMS)) {
            itemExcep |= TradeConstants.WATING_RETURN_WMS_EXCEPT;
        }

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAIT_MERGE)) {
            itemExcep |= TradeConstants.WAIT_MERGE;
        }

        if (mergeList != null && mergeList.size() > 0) {
            for (Trade mergeTrade : mergeList) {

                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED)) {
                    itemExcep |= TradeConstants.ADDRESS_CHANGED;
                }

                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE)) {
                    itemExcep |= TradeConstants.SELLER_MEMO_UPDATE;
                }

                if (TradeExceptUtils.isContainExcept(staff, mergeTrade, ExceptEnum.BLACK_NICK)) {
                    itemExcep |= TradeConstants.BLACK_NICK;
                }

                if (TradeExceptUtils.isContainExcept(staff, mergeTrade, ExceptEnum.LOST_MSG)) {
                    itemExcep |= TradeConstants.LOST_MESSAGE;
                }
            }
        }
        return itemExcep;
    }

    public static void setTradeExcep(Staff staff, Trade trade) {
        setTradeExcep(staff, trade, true);
    }

    public static void setTradeExcep(Staff staff, Trade trade, boolean checkLostMsg) {
        Set<Integer> except = parseExcept(staff, trade);
        if (!checkLostMsg) {
            except.remove(IDX_LOST_MSG);
            except.remove(IDX_LOST_ITEM);
        }
        if (TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
            if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId())) {
                trade.setIsExcep(TradeExceptUtils.getSubTradeIsExcept(staff, trade));
            } else {
                trade.setIsExcep(except.isEmpty() ? 0 : 1);
            }
        } else {
            trade.setIsExcep(0);
        }

        //  trade.setIsLostMsg(except.contains(IDX_LOST_MSG) || except.contains(IDX_LOST_ITEM) ? 1 : 0);
        long isLostMsg = except.contains(IDX_LOST_MSG) || except.contains(IDX_LOST_ITEM) ? 1L : 0L;
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.LOST_MSG, isLostMsg);
        // 合单时trade的order 包含了子单的order，但其实主单是没有退款异常，新版的未开起白名单的才重新计算退款异常
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId())) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orders4Trade)) {
                boolean b = orders4Trade.stream().filter(order -> Objects.equals(order.getSid(), trade.getSid())).anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.REFUNDING));
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.REFUNDING, b);
                if (b) {
                    except.add(IDX_REFUNDING);
                }
            }
        } else {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.REFUNDING, except.contains(IDX_REFUNDING));
        }
        // to do
        if (except.contains(IDX_REFUNDING)) {
            trade.setIsExcep(1);
        } else if (TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
            if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId())) {
                int isExcep = 0;
                if (except.size() == 1 && except.contains(ExceptEnum.UPLOAD_EXCEPT.getOldExceptEnum().getEnglish())) {
                    isExcep = 0;
                } else if (TradeExceptUtils.getSubTradeIsExcept(staff, trade) == 1 || CollectionUtils.isNotEmpty(except)) {
                    isExcep = 1;
                }
                trade.setIsExcep(isExcep);
            } else {
                trade.setIsExcep(except.isEmpty() ? 0 : 1);
            }
        } else {
            trade.setIsExcep(0);
        }
    }

    public static int setTradeExcep(Staff staff, Trade trade, Trade toUpdate) {
        int originIsExcep = trade.getIsExcep();
        boolean originRefund = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.REFUNDING);
        //  Integer isLostMsg = trade.getIsLostMsg();
        boolean originContainLostMsg = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.LOST_MSG);
        setTradeExcep(staff, trade);
        int c = 0;
        if (originIsExcep - trade.getIsExcep() != 0) {
            toUpdate.setIsExcep(trade.getIsExcep());
            c++;
        }
        boolean curRefund = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.REFUNDING);
        if (originRefund != curRefund) {
            // toUpdate.setIsRefund(trade.getIsRefund());
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.REFUNDING, curRefund);
            c++;
        }
        boolean currentContainLostMsg = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.LOST_MSG);
        if (currentContainLostMsg != originContainLostMsg) {
            // toUpdate.setIsLostMsg(trade.getIsLostMsg());
            TradeExceptUtils.updateExcept(staff, toUpdate, ExceptEnum.LOST_MSG, currentContainLostMsg);
            c++;
        }
        return c;
    }

    /**
     * 判断一笔订单是否为异常订单
     *
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isExcep(Staff staff, Trade trade) {
        // 已完成，已关闭订单不算异常订单
        if (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()) || Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
            return false;
        }
        Set<Integer> except = parseExcept(staff, trade);
        if (!except.isEmpty()) {//异常订单
            return true;
        }
        return isDisableUser(staff, trade);
    }

    public static boolean isDisableUser(Staff staff, Trade trade) {
        if (staff != null) {
            User user = null;
            if (staff.getUserIdMap() != null) {
                user = staff.getUserIdMap().get(trade.getUserId());
            }
            if (user == null) {
                user = staff.getUserByUserId(trade.getUserId());
            }
            if (user != null && user.getActive() != null && user.getActive() == 0) {//店铺停用的订单是异常订单，但isExcep不用设置为1
                return true;
            }
        }
        return false;
    }

    public static boolean isTbTrade(String source) {
        return PLAT_FORM_TYPE_TAO_BAO.equals(source) || PLAT_FORM_TYPE_TIAN_MAO.equals(source);
    }

    /**
     * 判断是否是微店订单
     *
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isWdUserTrade(Staff staff, Trade trade) {
        User user = staff.getUserIdMap().get(trade.getUserId());
        return user != null && PLAT_FORM_TYPE_WD.equals(user.getSource());
    }

    public static boolean isAKCUserTrade(Staff staff, Trade trade) {
        User user = staff.getUserIdMap().get(trade.getUserId());
        return user != null && PLAT_FORM_TYPE_AKC.equals(user.getSource());
    }

    public static boolean isTbCMTrade(String source) {
        return PLAT_FORM_TYPE_1688_C2M.equals(source);
    }

    public static boolean isThisPlatSysTrade(Staff staff, Trade trade, String source) {
        if (staff.getUserIdMap() == null) {
            staff.fillUserMap();
        }
        User user = staff.getUserIdMap().get(trade.getUserId());
        return user != null && PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && Objects.equals(source, user.getSource());
    }

    public static boolean isTbXsdTrade(Trade trade) {
        return PLAT_FORM_TYPE_TAO_BAO_XSD.equals(trade.getSource()) ;
    }

    /**
     * 淘宝小时达 b2c订单
     * @param trade
     * @return
     */
    public static boolean isTbXsdB2cTrade(Trade trade) {
        return (CommonConstants.PLAT_FORM_TYPE_TAO_BAO_XSD.equals(trade.getSource())
                ||("sys".equals(trade.getSource()) && CommonConstants.PLAT_FORM_TYPE_TAO_BAO_XSD.equals(trade.getSubSource())))
                && "1".equals(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "isB2c"));
    }

    /**
     * 判断是否为换货补发订单
     *
     * @param trade
     * @return
     */
    public static boolean isReissueOrChangeitem(Trade trade) {
        return trade != null && (Objects.equals(TYPE_TRADE_EXCHANGE, trade.getType()) || Objects.equals(TYPE_TRADE_REISSUE, trade.getType()));
    }

    /**
     * 判断是否为售后来的订单
     */
    public static boolean isAfterSaleTrade(Trade trade) {
        return trade != null &&
                (TYPE_TRADE_REISSUE.equals(trade.getType()) || TYPE_TRADE_EXCHANGE.equals(trade.getType()) || TYPE_TRADE_REPAIR.equals(trade.getType()));
    }

    /**
     * 是否指定source的复制订单
     */
    public static boolean isSourceCopyTrade(Trade trade, String source) {
        if (trade == null || StringUtils.isEmpty(source)) {
            return false;
        }
        TradeExt tradeExt = trade.getTradeExt();
        if (tradeExt == null) {
            return false;
        }
        JSONObject extraJson = JSON.parseObject(tradeExt.getExtraFields());
        if (extraJson == null) {
            return false;
        }
        return source.equals(extraJson.getString("source"));
    }

    /**
     * 判断是否为天猫国际直营商家仓菜鸟配标签订单，1000000116为商家仓菜鸟配id
     *
     * @param trade
     * @return
     */
    public static boolean isTMGJZYCaiNiao(Trade trade) {
        return trade != null && PLAT_FORM_TYPE_TMGJZY.equals(trade.getSource())
                && Arrays.stream(trade.getTagIds().split(",")).collect(Collectors.toSet()).contains("1000000116");
    }

    /**
     * 是否为快手代发加密订单
     *
     * @param trade
     * @return
     */
    public static boolean isKuaiShouDFEncryption(Trade trade) {
        return trade != null && PLAT_FORM_TYPE_KUAISHOU_DF.equals(trade.getSource())
                && StringUtils.isNotBlank(trade.getAddressMd5());
    }


    /**
     * 判断是否为京东加密订单
     *
     * @return
     */
    public static boolean isJDEncryption(Trade trade) {
        return (StringUtils.isNotBlank(trade.getReceiverPhone()) && trade.getReceiverPhone().contains("*"))
                || (StringUtils.isNotBlank(trade.getReceiverMobile()) && trade.getReceiverMobile().contains("*"));
    }

    /**
     * 判断是否为放心购加密订单
     *
     * @param trade
     * @return
     */
    public static boolean isFXGEncryption(Trade trade) {
        if (ifEncrypt(trade.getReceiverName()) || ifEncrypt(trade.getReceiverMobile()) || ifEncrypt(trade.getReceiverAddress())) {
            return true;
        }
        return false;
    }

    public static boolean ifEncrypt(String data) {
        if (StringUtils.isBlank(data) || data.length() < 4) {
            return false;
        }
        if ((StringUtils.startsWith(data, String.valueOf(SEPARATOR_POUND)) && StringUtils.endsWith(data, String.valueOf(SEPARATOR_POUND)))
                || (StringUtils.startsWith(data, String.valueOf(SEPARATOR_BUCK)) && StringUtils.endsWith(data, String.valueOf(SEPARATOR_BUCK)))
                || (StringUtils.startsWith(data, String.valueOf(SEPARATOR_TILDE)) && StringUtils.endsWith(data, String.valueOf(SEPARATOR_TILDE)))) {
            return true;
        }
        if (data.startsWith(PREFIX_WD)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为拼多多加密订单
     *
     * @param trade
     * @return
     */
    public static boolean isPDDEncryption(Trade trade) {
        return PddCommonUtils.isEncryptData(trade.getReceiverAddress()) || PddCommonUtils.isEncryptData(trade.getReceiverMobile()) || PddCommonUtils.isEncryptData(trade.getReceiverName());
    }

    public static List<String> getExceptionRemark(Staff staff, Trade trade) {
        //去掉 PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource()) 判断
        //唯品会 按数量拆单 source为 sys
        if (PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())
                || (PLAT_FORM_TYPE_PDD.equals(trade.getSource()) && PLAT_FORM_TYPE_FDS.equals(trade.getSubSource()))) {
            return new ArrayList<String>(0);
        }
        List<String> exceptionRemark = new ArrayList<>();
        if (StringUtils.isBlank(trade.getSysStatus())) {//缺少系统状态可能是因为平台同步订单时没有平台状态导致计算出来的系统状态为空
            exceptionRemark.add("没有系统状态");
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
            if (StringUtils.isBlank(trade.getPayment())) {
                exceptionRemark.add("没有实付金额");
            }
            if (StringUtils.isBlank(trade.getReceiverName()) && !ignoreLostMsgPlatforms.contains(TradePlatformUtils.getTradeUserSource(staff, trade))) {
                exceptionRemark.add("没有收件人姓名");
            }
            if (StringUtils.isBlank(trade.getReceiverAddress()) && !ignoreLostMsgPlatforms.contains(TradePlatformUtils.getTradeUserSource(staff, trade))) {
                exceptionRemark.add("没有收件地址");
            }
            if (StringUtils.isBlank(trade.getReceiverPhone()) && StringUtils.isBlank(trade.getReceiverMobile()) && !ignoreLostMsgPlatforms.contains(TradePlatformUtils.getTradeUserSource(staff, trade))) {
                exceptionRemark.add("没有收件人手机或电话");
            }
            if (orders.isEmpty() && TradeExceptUtils.isContainExcept(new Staff(), trade, ExceptEnum.LOST_MSG)) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    exceptionRemark.add("合单存在某个订单没有商品信息");
                } else {
                    exceptionRemark.add("没有商品");
                }
            }
        }
        int i = 1;
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (StringUtils.isBlank(order.getSysStatus())) {
                exceptionRemark.add("第" + i + "个商品没有系统状态");
            }
            if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {
                if ((PLAT_FORM_TYPE_TAO_BAO.equals(order.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(order.getSource())) && StringUtils.isBlank(order.getNumIid())) {
                    exceptionRemark.add("第" + i + "个商品没有平台商品ID");
                }
            }
            i++;
        }
        return exceptionRemark;
    }

    /**
     * 计算订单的总净重值，根据order.netWeight计算，如果netWeight为空，那么不会计算这个order
     *
     * @param trade
     * @return
     */
    @Deprecated
    public static double calculateTradeNetWeight(Trade trade) {
        return TradeCalculateUtils.calculateTradeNetWeight(trade);
    }

    public static boolean filterOrderCalNetWeight(Order order, boolean tradeIsAfterSendGoods) {
        return (order.getEnableStatus() != null && order.getEnableStatus() == 0)
                || (order.getItemSysId() == null || order.getItemSysId() <= 0 || (order.getCombineId() != null && order.getCombineId() > 0))
                || (!tradeIsAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus()));
    }

    public static double calculateTradeNetWeightWithPackma(Trade trade) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getNetWeight() != null) {
            return trade.getOrigin().getNetWeight();
        }
        TradeExt tradeExt = trade.getTradeExt();
        Object packmaWeight = TradeExtUtils.getExtraFieldValue(tradeExt, TradeExtraFieldEnum.PACKMA_WEIGHT.getField());
        if (packmaWeight == null) {
            return calculateTradeNetWeight(trade);
        }
        double packmaWeightDouble = Double.parseDouble(packmaWeight.toString());
        if (!MathUtils.equalsZero(packmaWeightDouble)) {
            return calculateTradeNetWeight(trade) + packmaWeightDouble;
        }
        return calculateTradeNetWeight(trade);
    }


    public static Double calculateTradeNetWeightByRule(Trade trade) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getNetWeight() != null) {
            return trade.getOrigin().getNetWeight();
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
        double netWeight = 0.0;
        for (Order order : orders) {
            if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if (order.getItemSysId() <= 0 || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                continue;
            }
            if (isAfterSendGoods || !TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                netWeight += (order.getNetWeight() != null ? order.getNetWeight() * order.getNum() : 0.0);
            }
        }
        return MathUtils.scaleUp(netWeight, 4);
    }

    @Deprecated
    public static double calculateCost(Trade trade) {
        return TradeCalculateUtils.calculateCost(trade);
    }

    @Deprecated
    public static double calculateCost(Trade trade, boolean isFilterEnbaleStatus) {
        return TradeCalculateUtils.calculateCost(trade, isFilterEnbaleStatus);
    }

    /**
     * @param trade
     * @param orders
     * @param isFilterEnbaleStatus
     * @return
     */
    public static List<Order> filterOrders4Cost(Trade trade, List<Order> orders, boolean isFilterEnbaleStatus, boolean isFilterMerge) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<Order> filted = new ArrayList<>();
        for (Order order : orders) {
            if (isFilterEnbaleStatus && order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                continue;
            }
            if ((order.getCombineId() != null && order.getCombineId() > 0)) {
                continue;
            }
            if (order.getItemSysId() <= 0) {
                continue;
            }
            if (isFilterMerge && !Objects.equals(trade.getSid(), order.getSid())) {
                continue;
            }
            //https://gykj.yuque.com/entavv/xb9xi5/fw6zrx 1688订单待付款订单，卖家部分关闭商品，付款后，订单成本不统计交易关闭商品的成本。
            if (is1688CancelOrder(trade, order)) {
                //1688开始是待付款、后面变成交易关闭的子订单，不计算在订单成本里面去。 待付款状态下的子订单取消，不计算在订单成本里面去。
                continue;
            }
            filted.add(order);
        }
        return filted;
    }


    public static boolean is1688CancelOrder(Trade trade, Order order) {
        if (!PLAT_FORM_TYPE_1688.equals(trade.getSource())) {
            return false;
        }

        //1688开始是待付款、后面变成交易关闭的子订单，不计算在订单成本里面去。
        //1688待付款状态下的子订单取消，不计算在订单成本里面去。
        if ((!Objects.isNull(order.getOrigin()) && Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getOrigin().getSysStatus()) && Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()))
                || ("cancel".equals(order.getStatus()) && Order.NO_REFUND.equals(order.getRefundStatus()))) {
            logger.debug(String.format("order[id=%s,sid=%s,payment=%s,cost=%s]不参与trade成本计算【在待付款的状态下被取消】", order.getId(), order.getSid(), order.getPayment(), order.getCost()));
            return true;
        }

        return false;
    }

    public static double calculateGrossProfitNotMerge(Trade trade) {
        double payment = calculateGrossProfitByDivideOrderFee(trade);
        Double cost = trade.getCost() != null ? trade.getCost() : 0D;
        Double packmaCost = trade.getPackmaCost() != null ? trade.getPackmaCost() : 0D;
        Double postFee = StringUtils.isNoneBlank(trade.getActualPostFee()) ? Double.valueOf(trade.getActualPostFee()) : 0D;
        return new BigDecimalWrapper(payment).subtract(cost).subtract(packmaCost).subtract(postFee).getDouble();
    }

    private static double calculateGrossProfitByDivideOrderFee(Trade trade) {
        return StringUtils.isNoneBlank(trade.getPayment()) ? Double.valueOf(trade.getPayment()) : 0D;
    }

    public static double calculatePaymentDiffNotMerge(Trade trade) {
        Double payment = StringUtils.isNoneBlank(trade.getPayment()) ? Double.valueOf(trade.getPayment()) : 0D;
        Double acPayment = StringUtils.isNoneBlank(trade.getAcPayment()) ? Double.valueOf(trade.getAcPayment()) : 0D;
        return new BigDecimal(payment - acPayment).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double calculateGrossProfitMerge(Trade trade, List<MessageMemo> messageMemos) {
        Double payment = 0D;
        Double cost = trade.getCost() != null ? trade.getCost() : 0D;
        Double packmaCost = trade.getPackmaCost() != null ? trade.getPackmaCost() : 0D;
        Double postFee = StringUtils.isNoneBlank(trade.getActualPostFee()) ? Double.valueOf(trade.getActualPostFee()) : 0D;
        for (MessageMemo memo : messageMemos) {
            payment += calculateGrossProfitMessageMemo(memo);
        }
        return new BigDecimal(payment - cost - packmaCost - postFee).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double calculateGrossProfitMessageMemo(MessageMemo messageMemo) {
        return StringUtils.isNoneBlank(messageMemo.getPayment()) ? Double.valueOf(messageMemo.getPayment()) : 0D;
    }

    public static double calculatePaymentDiffMerge(Trade trade, List<MessageMemo> messageMemos) {
        Double payment = 0D;
        Double acPayment = 0D;
        for (MessageMemo memo : messageMemos) {
            payment += NumberUtils.str2Double(memo.getPayment());
            acPayment += NumberUtils.str2Double(memo.getAcPayment());
        }
        return new BigDecimal(payment - acPayment).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double calculateGrossProfit(Trade trade) {
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        return CollectionUtils.isNotEmpty(messageMemos) ? calculateGrossProfitMerge(trade, messageMemos) : calculateGrossProfitNotMerge(trade);
    }

    public static double calculatePaymentDiff(Trade trade) {
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        return CollectionUtils.isNotEmpty(messageMemos) ? calculatePaymentDiffMerge(trade, messageMemos) : calculatePaymentDiffNotMerge(trade);
    }

    @Deprecated
    public static double calculateVolume(Trade trade) {
        return TradeCalculateUtils.calculateVolume(trade);
    }

    public static boolean filterOrderCalVolume(Order order, boolean tradeIsAfterSendGoods) {
        return (order.getEnableStatus() != null && order.getEnableStatus() == 0)
                || order.getVolume() == null
                || ((order.getCombineId() != null && order.getCombineId() > 0))
                || (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && (order.getSysConsigned() != null && order.getSysConsigned() - 1 != 0))
                || (!tradeIsAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus()));
    }

    public static double calculateVolumeWithPackma(Trade trade) {
        if (!trade.isNeedRecal() && trade.getOrigin() != null && trade.getOrigin().getVolume() != null) {
            return trade.getOrigin().getVolume();
        }
        TradeExt tradeExt = trade.getTradeExt();
        Object packmaVolume = TradeExtUtils.getExtraFieldValue(tradeExt, TradeExtraFieldEnum.PACKMA_VOLUME.getField());
        if (packmaVolume == null) {
            return calculateVolume(trade);
        }
        double packmaVolumeDouble = Double.parseDouble(packmaVolume.toString());
        if (MathUtils.equalsZero(packmaVolumeDouble)) {
            return calculateVolume(trade);
        }
        return packmaVolumeDouble;
    }


    /**
     * 订单是否需要重算cost，volumn，netWeight，现在只在同步处调用
     *
     * @param trade
     * @return
     */
    public static boolean needRecal(Trade trade) {
        Trade originTrade = trade.getOrigin();
        if (originTrade != null && trade.getSysStatus() != null && TradeUtils.isAfterSendGoods(originTrade)) {
            trade.setNeedRecal(false);
            originTrade.setNeedRecal(false);
        }
//        originTrade.setNeedRecal(true);
//        trade.setNeedRecal(true);
        return trade.isNeedRecal();
    }

    /**
     * 获取订单下第一个待发货的子订单的系统商家编码
     *
     * @param trade
     * @return String
     */
    public static String calculateOuterId(Staff staff, Trade trade) {
//        if (Arrays.asList(StringUtils.defaultIfEmpty(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getSortCompanyIds(),"").split(",")).contains(staff.getCompanyId().toString())) {
        TradeUtils.fillTradeOfSort(trade);
        return trade.getSysOuterId();
//        } else {
//            List<Order> orders = TradeUtils.getOrders4Trade(trade);
//            for (Order order : orders) {
//                if (order.getItemSysId() > 0 && TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {//第一个待发货切匹配了系统商品的子订单
//                    return order.getSysOuterId();
//                }
//            }
//        }
//        return null;
    }

    /**
     * 将double值转换为字符串，保留4位小数位
     *
     * @param d
     * @return
     */
    public static String doubleToString(double d) {
        String v = new BigDecimal(d).setScale(4, BigDecimal.ROUND_HALF_EVEN).toString();
        // 裁剪小数点后多余的0，例如1.0000改为1.0
        return catRightZero(v, 1);
    }

    /**
     * 裁剪小数字符串右边多余的0
     *
     * @param v
     * @param stay
     * @return
     */
    public static String catRightZero(String v, int stay) {
        if (!v.contains("."))
            return v;

        if (stay < 0)
            stay = 0;

        String[] array = v.split("\\.");
        if (array.length != 2)
            return v;

        String rightDecimal = array[1];
        if (StringUtils.isEmpty(rightDecimal))
            return v;

        if (rightDecimal.length() <= stay)
            return v;

        int endIndex = rightDecimal.length();
        for (; endIndex > stay; endIndex--) {
            if (rightDecimal.charAt(endIndex - 1) != '0')
                break;
        }

        if (endIndex == 0)
            return array[0];

        return new StringBuilder(array[0]).append(".").append(rightDecimal.substring(0, endIndex)).toString();
    }

    /**
     * 判断trade是否匹配到了赠品,并且由待付款变为待审核,并且是拍下减库存
     *
     * @param trade
     * @return
     */
    public static boolean isSubTradeHasGift(Trade trade) {

        List<GiftOrder> giftOrders = trade.getGiftOrders();
        if (CollectionUtils.isEmpty(giftOrders)) {
            return false;//没有匹配到赠品
        }

        if (!Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) || !TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
            return false;//不是由待付款变为待审核
        }

        //是否是拍下减库存
        return isSubTrade(trade);
    }

    /**
     * 判断订单是否是拍下减库存的
     *
     * @param trade
     * @return
     */
    public static boolean isSubTrade(Trade trade) {
        //是否是拍下减库存
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (order.getSubStock() != null && order.getSubStock() == 0) {
                return true;//只要有一个是拍下减库存，则认为订单就是拍下减库存
            }
        }
        return false;
    }

    /**
     * 获取买家昵称(自动合单及标记可合单的时候使用)
     *
     * @param trades
     * @return
     */
    public static List<String> getBuyerNicks(List<Trade> trades) {
        List<String> buyerNicks = new ArrayList<String>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                String buyerNick = trade.getBuyerNick();
                if (buyerNicks.contains(buyerNick) || StringUtils.isEmpty(buyerNick)) {
                    continue;
                }
                buyerNicks.add(buyerNick);
            }
        }
        return buyerNicks;
    }

    /**
     * 判断传入的订单列表是否使用的是不同的物流编号发货，
     *
     * @return 返回使用不同outSid的订单列表
     */
    public static List<TbTrade> isDiffLogistics(List<TbTrade> mergeTbTrades) {
        if (CollectionUtils.isEmpty(mergeTbTrades)) {
            return new ArrayList<TbTrade>();
        }
        List<TbTrade> result = new ArrayList<TbTrade>(mergeTbTrades.size());
        Set<String> outSids = new HashSet<String>(mergeTbTrades.size());
        for (TbTrade mergeTrade : mergeTbTrades) {
            if (StringUtils.isEmpty(mergeTrade.getOutSid())) {
                result.add(mergeTrade);
            } else if (!outSids.contains(mergeTrade.getOutSid())) {
                result.add(mergeTrade);
                outSids.add(mergeTrade.getOutSid());
            }
        }
        return result;
    }

    /**
     * 对tradeList根据sids进行排序
     *
     * @param sids
     * @param tradeList
     */
    public static List<Trade> sortTradesBySids(Long[] sids, List<Trade> tradeList) {
        if (sids == null || sids.length <= 0 || CollectionUtils.isEmpty(tradeList)) {
            return tradeList;
        }
        Map<Long, Trade> tradeMap = new HashMap<Long, Trade>(tradeList.size(), 1);
        for (Trade trade : tradeList) {
            tradeMap.put(trade.getSid(), trade);
        }

        List<Trade> result = new ArrayList<Trade>(tradeList.size());
        for (Long sid : sids) {
            Trade trade = tradeMap.get(sid);
            if (trade == null) {
                continue;
            }
            result.add(trade);
        }

        return result;
    }

    /**
     * 新的排序方法，兼容合单数据
     * 将tradeList中的顺序根据sidList的顺序排序
     *
     * @param sidList
     * @param tradeList
     * @return
     */
    public static void sortTradesBySidsNew(List<Long> sidList, List<Trade> tradeList) {
        tradeList.sort((t1, t2) -> {
            int index1 = sidList.indexOf(t1.getSid());
            if (index1 == -1 && CollectionUtils.isNotEmpty(t1.getMessageMemos())) {
                MessageMemo messageMemo = t1.getMessageMemos().stream()
                        .filter(memo -> sidList.contains(memo.getSid()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(messageMemo)) {
                    index1 = sidList.indexOf(messageMemo.getSid());
                }
            }

            int index2 = sidList.indexOf(t2.getSid());
            if (index2 == -1 && CollectionUtils.isNotEmpty(t2.getMessageMemos())) {
                MessageMemo messageMemo = t2.getMessageMemos().stream()
                        .filter(memo -> sidList.contains(memo.getSid()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(messageMemo)) {
                    index2 = sidList.indexOf(messageMemo.getSid());
                }
            }

            if (index1 != -1) {
                index1 = tradeList.size() - index1;
            }

            if (index2 != -1) {
                index2 = tradeList.size() - index2;
            }
            return index2 - index1;
        });
    }

    public static Long[] getSids(PrintTradeLog log) {
        List<Long> list = new ArrayList<Long>();
        for (PrintTradeLogDetail detail : log.getDetails()) {
            list.add(detail.getSid());
        }
        Long[] sids = new Long[list.size()];
        return list.toArray(sids);
    }

    public static String[] getMultiPrintOutsid(MultiPacksPrintTradeLog log) {
        List<String> list = new ArrayList<String>();
        if (null != log && CollectionUtils.isNotEmpty(log.getDetails())) {
            for (MultiPacksPrintTradeLogDetail detail : log.getDetails()) {
                list.add(detail.getOutSid());
            }
        }
        String[] outsids = new String[list.size()];
        return list.toArray(outsids);
    }

    /**
     * 计算订单应付(合单的时候需要合并)
     *
     * @param trade
     * @return
     */
    public static String getTotalFee(Trade trade) {
        if (!TradeUtils.isMerge(trade)) {
            return trade.getTotalFee();
        }
        List<MessageMemo> memos = trade.getMessageMemos();
        BigDecimalWrapper mergeTotalFee = new BigDecimalWrapper();
        if (memos != null) {
            for (MessageMemo memo : memos) {
                if (memo == null || memo.getTotalFee() == null) {
                    continue;
                }
                try {
                    mergeTotalFee.add(memo.getTotalFee());
                } catch (Exception e) {
                }
            }
        }
        return mergeTotalFee.getString();
    }

    /**
     * 计算订单实付(合单的时候需要合并)
     *
     * @param trade
     * @return
     */
    public static String getPayment(Trade trade) {
        if (!TradeUtils.isMerge(trade)) {
            return trade.getPayment();
        }
        List<MessageMemo> memos = trade.getMessageMemos();
        BigDecimalWrapper mergePayment = new BigDecimalWrapper();
        if (memos != null) {
            for (MessageMemo memo : memos) {
                try {
                    mergePayment.add(memo.getPayment());
                } catch (Exception e) {
                }
            }
        }
        return mergePayment.getString();
    }

    /**
     * 计算订单买家已付金额(合单的时候需要合并)
     *
     * @param trade
     * @return
     */
    public static String getPayAmount(Staff staff, Trade trade) {
        if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            return trade.getPayAmount() == null ? "0" : trade.getPayAmount();
        }
        List<MessageMemo> memos = trade.getMessageMemos();
        BigDecimalWrapper mergePayAmount = new BigDecimalWrapper();
        if (memos != null) {
            for (MessageMemo memo : memos) {
                try {
                    mergePayAmount.add(memo.getPayAmount());
                } catch (Exception e) {
                }
            }
        }
        return mergePayAmount.getString();
    }

    public static String getOrderPrice(Trade trade) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade, false);
        // 初始化为0，避免空指针
        BigDecimal totalSum = BigDecimal.ZERO;
        for (Order order : orders4Trade) {
            String price = order.getPrice();
            if (StringUtils.isEmpty(price)) {
                continue;
            }
            // 将字符串价格转换为BigDecimal，确保精度
            BigDecimal priceDecimal = new BigDecimal(order.getPrice());
            // 将整数数量转换为BigDecimal
            BigDecimal numDecimal = new BigDecimal(order.getNum());
            // 相乘得到总金额
            BigDecimal totalAmount = priceDecimal.multiply(numDecimal);

            // 累加到总金额
            totalSum = totalSum.add(totalAmount);
        }
        return new BigDecimalWrapper(totalSum).getString();
    }


    /**
     * 平台上的卖家备注(合单的时候需要合并)
     *
     * @param trade
     * @return
     */
    public static String getSellerMemo(Trade trade) {
        return getSellerMemo(trade, ",");
    }

    /**
     * 合单的情况下，备注取所有的订单备注
     *
     * @param trade 订单
     * @return
     */
    public static String getSellerMemo(Trade trade, String delimiter) {
        if (!TradeUtils.isMerge(trade)) {
            return trade.getSellerMemo();
        }
        String delimiteres = StringUtils.isEmpty(delimiter) ? ";" : delimiter;
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        if (CollectionUtils.isEmpty(messageMemos)) {
            return trade.getSellerMemo();
        } else {
            return messageMemos.stream().map(m -> StringUtils.trimToEmpty(m.getSellerMemo())).filter(StringUtils::isNotBlank).collect(Collectors.joining(delimiteres));
        }
    }

    /**
     * 合单的情况下，备注取所有的系统订单
     *
     * @param trade 订单
     * @return
     */
    public static String getSysMemo(Staff staff, Trade trade) {
        boolean isAliHealth = TradeUtils.platformMatch(staff, trade, PLAT_FORM_TYPE_ALIHEALTH);
        if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            if (isAliHealth) {
                return parseSysMemo(trade, null);
            }
            return trade.getSysMemo();
        }
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        if (CollectionUtils.isEmpty(messageMemos)) {
            if (isAliHealth) {
                return parseSysMemo(trade, null);
            }
            return trade.getSysMemo();
        } else {
            if (isAliHealth) {
                return messageMemos.stream().map(m -> parseSysMemo(null, m)).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            }
            return messageMemos.stream().map(m -> StringUtils.trimToEmpty(m.getSysMemo())).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        }
    }


    /**
     * 阿里健康平台，导出系统备注的特殊处理
     *
     * @param trade       订单信息
     * @param messageMemo 拆单的备注信息
     * @return
     */
    public static String parseSysMemo(Trade trade, MessageMemo messageMemo) {
        if (null != trade) {
            if (null != trade.getTradeExt()) {
                Long alihealthMainId = (Long) trade.getTradeExt().get("alihealthMainId");
                String sysMemo = StringUtils.trimToEmpty(trade.getSysMemo());
                if (null == alihealthMainId) {
                    return sysMemo;
                }
                StringBuilder stringBuilder = new StringBuilder();
                if (StringUtils.isNotBlank(sysMemo)) {
                    stringBuilder.append(sysMemo).append(";");
                }
                stringBuilder.append("平台订单号:").append(alihealthMainId);
                return stringBuilder.toString();
            }
            return StringUtils.trimToEmpty(trade.getSysMemo());
        }
        if (null != messageMemo) {
            if (null != messageMemo.getAliHealthMainId()) {
                String sysMemo = StringUtils.trimToEmpty(messageMemo.getSysMemo());
                StringBuilder stringBuilder = new StringBuilder();
                if (StringUtils.isNotBlank(sysMemo)) {
                    stringBuilder.append(sysMemo).append(";");
                }
                stringBuilder.append("平台订单号:").append(messageMemo.getAliHealthMainId());
                return stringBuilder.toString();
            }
            return StringUtils.trimToEmpty(messageMemo.getSysMemo());
        }
        return "";
    }

    /**
     * 平台订单上的买家留言(合单的时候需要合并)
     *
     * @param trade
     * @return
     */
    public static String getBuyerMessage(Staff staff, Trade trade) {
        if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            return trade.getBuyerMessage();
        }
        StringBuilder sb = new StringBuilder();
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        if (messageMemos != null) {
            for (MessageMemo messageMemo : messageMemos) {
                if (StringUtils.isNotEmpty(StringUtils.trimToEmpty(messageMemo.getBuyerMessage()))) {
                    sb.append(",").append(StringUtils.trimToEmpty(messageMemo.getBuyerMessage()));
                }
            }
        }
        if (sb.length() > 0) {
            return sb.substring(1);
        } else {
            return trade.getBuyerMessage();
        }
    }

    /**
     * 计算地址类型 null 未知，1 非城区，0城区
     */
    public static void resetAddressType(Trade trade) {
        String address = trade.getReceiverStreet();
        if (StringUtils.isBlank(address)) {
            address = trade.getReceiverAddress();
        }
        Integer addressType = (address != null && !address.isEmpty()) ? (BacklandUtils.getFirstBacklandKey(address).isEmpty() ? 0 : 1) : null;
        if (addressType != null) {
            trade.setAddressType(addressType);
        }
    }

    public static int[] calculateItemNum(List<Order> orders, TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            return calculateItemNum(orders, TradeConfigContext.builder()
                    .itemNumExcludeAfterSendGoods(false)
                    .itemNumExcludeVirtual(0)
                    .itemNumExcludeNonConsign(0)
                    .build());
        } else {
            return calculateItemNum(orders, TradeConfigContext.builder()
                    .itemNumExcludeAfterSendGoods(tradeConfig.isItemNumExcludeAfterSendGoods())
                    .itemNumExcludeVirtual(tradeConfig.getItemNumExcludeVirtual())
                    .itemNumExcludeNonConsign(tradeConfig.getItemNumExcludeNonConsign())
                    .build());
        }
    }

    public static int[] calculateItemNum(List<Order> orders, boolean itemNumExcludeAfterSendGoods, int itemNumExcludeVirtual, Integer itemNumExcludeNonConsign) {
        return calculateItemNum(orders, TradeConfigContext.builder()
                .itemNumExcludeAfterSendGoods(itemNumExcludeAfterSendGoods)
                .itemNumExcludeVirtual(itemNumExcludeVirtual)
                .itemNumExcludeNonConsign(itemNumExcludeNonConsign)
                .build());
    }

    public static int[] calculateItemNum(List<Order> orders, TradeConfigContext context) {
        int itemNum = 0;
        Map<String, Integer> map = new HashMap<>();
        //增加单品种类
        Map<String, Integer> singleItemKindMap = new HashMap<>();
        for (Order order : orders) {
            if (filterOrderItemNum(context, order)) {
                continue;
            }
            itemNum += order.getNum();
            String key = TradeItemUtils.getItemKey(order);
            if (!map.containsKey(key)) {
                map.put(key, 1);
            }

            if (!Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                List<Order> suitsList = order.getSuits();
                if (!CollectionUtils.isEmpty(suitsList)) {
                    for (Order o : suitsList) {
                        key = TradeItemUtils.getItemKey(o.getItemSysId(), o.getSkuSysId());
                        singleItemKindMap.put(key, singleItemKindMap.containsKey(key) ? singleItemKindMap.get(key) + 1 : 1);
                    }
                } else {
                    singleItemKindMap.put(key, singleItemKindMap.containsKey(key) ? singleItemKindMap.get(key) + 1 : 1);
                }
            }
        }
        return new int[]{map.size(), itemNum, singleItemKindMap.size()};
    }

    /**
     * 计算数量的时候是否需要过滤order
     */
    public static boolean filterOrderItemNum(Order order, boolean itemNumExcludeAfterSendGoods, int itemNumExcludeVirtual, Integer itemNumExcludeNonConsign) {
        return filterOrderItemNum(TradeConfigContext.builder()
                        .itemNumExcludeAfterSendGoods(itemNumExcludeAfterSendGoods)
                        .itemNumExcludeVirtual(itemNumExcludeVirtual)
                        .itemNumExcludeNonConsign(itemNumExcludeNonConsign)
                        .build(),
                order);
    }

    public static boolean filterOrderItemNum(TradeConfigContext context, Order order) {
        return (order.getEnableStatus() != null && order.getEnableStatus() == 0) //删除的不统计
                || (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && context.isItemNumExcludeAfterSendGoods()) //已发货后的不统计
                || (order.isVirtual() && context.getItemNumExcludeVirtual() == 1) //虚拟商品不统计
                || (order.ifNonConsign() && Objects.equals(context.getItemNumExcludeNonConsign(), 1)) //无需发货商品不统计
                ;
    }

    @Deprecated
    public static void resetTradeItemNum(Trade trade) {
        resetTradeItemNum(trade, TradeConfigContext.builder()
                .itemNumExcludeAfterSendGoods(false)
                .itemNumExcludeVirtual(0)
                .itemNumExcludeNonConsign(0)
                .build());
    }

    @Deprecated
    public static void resetTradeItemNum(Trade trade, TradeConfig tradeConfig) {
        resetTradeItemNum(trade, TradeConfigContext.builder()
                .itemNumExcludeAfterSendGoods(tradeConfig.isItemNumExcludeAfterSendGoods())
                .itemNumExcludeVirtual(tradeConfig.getItemNumExcludeVirtual())
                .itemNumExcludeNonConsign(tradeConfig.getItemNumExcludeNonConsign())
                .build());
    }

    public static void resetTradeItemNum(Trade trade, List<Order> orders, TradeConfig tradeConfig) {
        int[] numArr = calculateItemNum(orders, tradeConfig);
        trade.setItemKindNum(numArr[0]);
        trade.setItemNum(numArr[1]);
        trade.setSingleItemKindNum(numArr[2]);
    }

    public static void resetTradeItemNum(Trade trade, TradeConfigContext context) {
        int[] numArr = calculateItemNum(TradeUtils.getOrders4Trade(trade), context);
        trade.setItemKindNum(numArr[0]);
        trade.setItemNum(numArr[1]);
        trade.setSingleItemKindNum(numArr[2]);
    }

    public static void resetTradeItemNum(Trade trade,List<Order> orders, TradeConfigContext context) {
        int[] numArr = calculateItemNum(orders, context);
        trade.setItemKindNum(numArr[0]);
        trade.setItemNum(numArr[1]);
        trade.setSingleItemKindNum(numArr[2]);
    }

    public static boolean isEquals(String s1, String s2) {
        return StringUtils.equals(StringUtils.stripToEmpty(s1), StringUtils.stripToEmpty(s2));
    }

    public static boolean isEqualsSellerFlag(Long l1, Long l2) {
        //小于0 为无旗帜
        l1 = (l1 == null || l1 < 0) ? null : l1;
        l2 = (l2 == null || l2 < 0) ? null : l2;
        return (nvl2Default(l1) - nvl2Default(l2) == 0) && ((l1 == null) == (l2 == null));
    }

    public static boolean isEqualsSellerFlagByPdd(Long l1, Long l2) {
        //KMERP-213454 历史数据兼容。历史数据同步至ERP，系统旗帜已经为-1（无旗帜），不要强制去修改为0（灰色），避免因为旗帜变动触发反审核。
        l1 = (l1 == null || l1 < 0) ? null : l1;
        l2 = (l2 == null || l2 < 0) ? null : l2;
        return (nvl2Default(l1) - nvl2Default(l2) == 0);
    }

    public static Long nvl2Default(Long L) {
        return L == null ? 0 : L;
    }

    public static Trade findMainMergeTrade(List<? extends Trade> trades) {
        for (Trade trade : trades) {
            if (trade.getSid().equals(trade.getMergeSid())) {
                return trade;
            }
        }
        return null;
    }

    public static String calculateTradeSaleFee(Trade trade) {
        return TradeCalculateUtils.calculateTradeSaleFee(trade);
    }

    /**
     * 1.快麦通处理所有订单，非快麦通只处理原来是分销的订单
     * 2.根据order计算trade的分销属性和供销商不明确异常
     *
     * @param trades
     */
    public static void recalculateDistributorAttributeFromOrder2Trade(Staff staff, List<Trade> trades, boolean ifForce) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        trades.stream().filter(t1 -> !TradeUtils.notNeedMatchFxAttr(t1) && (ifForce || staff.getCompany().isKmt() || TradeUtils.isFxTrade(t1))).forEach(t -> {
            t.setOldIsFxOrMix(TradeUtils.isFxOrMixTrade(t));
            // 包含0的
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(t);
            if (CollectionUtils.isEmpty(orders4Trade)) {
                return;
            }
            orders4Trade.stream().filter(o -> Objects.isNull(o.getDestId())).forEach(o1 -> o1.setDestId(0L));
            Set<Long> destIdSet = orders4Trade.stream().map(Order::getDestId).collect(Collectors.toSet());
            if (destIdSet.size() == 1 && destIdSet.contains(0L)) {
                if (TradeUtils.isGxOrMixTrade(t)) {
                    return;
                }
                if (staff.getCompany().isKmt()) {
                    TradeUtils.setSourceId(t, 0L);
                    t.setBelongType(0);
                    t.setConvertType(0);
                    t.setDestId(0L);
                    t.removeV(V_IF_KMT_DF);
                    // 去掉供销商不明确异常
                    TradeExceptUtils.updateExcept(staff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                    t.setIsExcep(TradeExceptUtils.isExcept(staff, t) ? 1 : 0);
                } else {
                    if (!ifForce) {
                        // 打上供销商不明确异常，拆单的时候需要打上。
                        TradeExceptUtils.updateExcept(staff, t, ExceptEnum.FX_AMBIGUITY, 1L);
                        t.setIsExcep(1);
                    }
                }
                // 只有一个供应商且公司id不是本公司就是分销
            } else if (destIdSet.size() == 1 && !destIdSet.contains(0L) && !destIdSet.contains(t.getCompanyId())) {

                // 全部都是分销，订单取商品的供销商
                TradeExceptUtils.updateExcept(staff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                t.setIsExcep(TradeExceptUtils.isExcept(staff, t) ? 1 : 0);
                if (TradeUtils.isGxOrMixTrade(t)) {
                    t.setBelongType(3);
                } else {
                    TradeUtils.setSourceId(t, t.getCompanyId());
                    t.setBelongType(1);
                }
                t.setConvertType(1);
                t.setDestId(orders4Trade.get(0).getDestId());
            } else if (destIdSet.size() > 1) {// 剩下的就是多供销
                if (staff.getCompany().isKmt()) {
                    if (TradeUtils.isGxOrMixTrade(t)) {
                        return;
                    }
                    TradeExceptUtils.updateExcept(staff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                    t.setIsExcep(TradeExceptUtils.isExcept(staff, t) ? 1 : 0);
                    t.setBelongType(0);
                    t.setConvertType(0);
                    TradeUtils.setSourceId(t, 0L);
                    t.setDestId(0L);
                    t.removeV(V_IF_KMT_DF);
                } else {
                    // 打上供销商不明确异常
                    TradeExceptUtils.updateExcept(staff, t, ExceptEnum.FX_AMBIGUITY, 1L);
                    t.setIsExcep(1);
                    t.setDestId(0L);
                }
            }
        });

        // 奇门订单把convertType和belongType重置成原来的的值
        trades.stream().filter(TradeUtils::isQimenAndNotFx).forEach(t -> {
            t.setConvertType(3);
            t.setBelongType(2);
            TradeUtils.getOrders4Trade(t).forEach(o -> {
                o.setConvertType(3);
                o.setBelongType(2);
            });
        });

        // to do 最好异常和非异常业务分离，上游传递一个是否比标记异常的标记过来，下游在业务外层去标记
       /* for(Trade trade:trades){
           // 根据上游的标记判断
            if (false) {
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 0L);
                trade.setIsExcep(TradeExceptUtils.isExcept(staff, trade) ? 1 : 0);
            } else {
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 1L);
            }
        }*/

    }

    /**
     * 1.快麦通处理所有订单，非快麦通只处理原来是分销的订单
     * 2.根据order计算trade的分销属性和供销商不明确异常
     *
     * @param trades
     */
    public static void recalculateDistributorAttributeFromOrder2Trade(Staff staff, List<Trade> trades) {
        recalculateDistributorAttributeFromOrder2Trade(staff, trades, false);
    }

    public static void setMobileTail(Trade trade) {
        if (trade.getMobileTail() == null && trade.getReceiverMobile() != null) {//淘宝天猫在前面解密的时候已经设置
            String mobile = trade.getReceiverMobile().trim();
            if (mobile.length() < 16) {//超过16是加密串
                trade.setMobileTail(mobile.length() >= 4 ? mobile.substring(mobile.length() - 4) : mobile);
            }
        }
    }

    public static boolean hasOutSid(Trade trade) {
        return trade != null && trade.getOutSid() != null && !"".equals(trade.getOutSid()) && !"N/A".equals(trade.getOutSid());
    }


    private static String sellerFlag(Long sellerFlag) {
        if (sellerFlag == null) {
            return "";
        }
        int flag = sellerFlag.intValue();
        switch (flag) {
            case 0:
                return "无旗帜";
            case 1:
                return "红色旗帜";
            case 2:
                return "黄色旗帜";
            case 3:
                return "绿色旗帜";
            case 4:
                return "蓝色旗帜";
            case 5:
                return "紫色旗帜";
            default:
                return "";
        }
    }


    public static Boolean hasNonSellerFlag(Integer[] excludeSellerFlag) {
        Boolean flag = false;
        for (Integer sellerFlag : excludeSellerFlag) {
            if (Integer.valueOf(-1).equals(sellerFlag)) {
                flag = true;
                break;
            }
        }
        return flag;
    }


    public static String getSellerFlag(Staff staff, Trade trade) {
        if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            return sellerFlag(trade.getSellerFlag());
        }
        StringBuilder sb = new StringBuilder();
        List<MessageMemo> messageMemos = trade.getMessageMemos();
        if (messageMemos != null) {
            for (MessageMemo messageMemo : messageMemos) {
                if (messageMemo.getSellerFlag() != null) {
                    sb.append(",").append(sellerFlag(messageMemo.getSellerFlag()));
                }
            }
        }
        if (sb.length() > 0) {
            return sb.substring(1);
        } else {
            return sellerFlag(trade.getSellerFlag());
        }
    }

    public static boolean isInsufficient(Trade trade) {
        for (Order order : getOrders4Trade(trade)) {
            if (null == order.getStockNum() || (order.getStockNum() < order.getNum())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 去除MD5加密时的 超过两个字节的字符
     */
    public static String removeUtf8mb4(String str) {
        if (null == str) {
            return str;
        }
        final int LAST_BMP = 0xFFFF;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            int codePoint = str.codePointAt(i);
            if (codePoint < LAST_BMP) {
                sb.appendCodePoint(codePoint);
            }
        }
        return sb.toString();
    }

    public static void setTimeoutActionTime(Trade trade, TradeConfig config, TradeUserConfig tradeUserConfig) {
        if (TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_UPDATE_TIMEOUT)) {
            return;
        }
        String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
        boolean isTianMao = PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource());
        boolean isTaoBao = PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource());
        boolean isWxSph = PLAT_FORM_TYPE_WXSPH.equals(trade.getSource());
        //开启承诺发货时间配置
        boolean openEstConsignTime = Objects.nonNull(config) && config.isOpenExtendConfig(TradeExtendConfigsEnum.OPEN_EST_CONSIGN_TIME);
        //承诺发货时时间配置可用
        boolean estConsignTimeIsNotNull = Objects.nonNull(tradeUserConfig) && ENABLE_STATUS.equals(tradeUserConfig.getEnableStatus()) && Objects.nonNull(tradeUserConfig.getEstConsignTime());
        //开启承若发货时间，并且当前店铺承若时间配置已开启
        if (openEstConsignTime && estConsignTimeIsNotNull) {
            // 天猫特殊处理（https://tb.raycloud.com/task/60a5dd1b933f542a1abca590），
            if (isTianMao || isWxSph || isTaoBao) {
                for (Order order : TradeUtils.getOrders4Trade(trade)) {
                    String estimateConTimeStr = time2Str(order.getEstimateConTime());
                    String payTimeStr = time2Str(order.getPayTime());
                    if(StringUtils.isBlank(estimateConTimeStr) && StringUtils.isNotBlank(payTimeStr) && !PLAT_FORM_TYPE_SYS.equals(order.getSource())){
                        Date estimateConTime = DateUtils.addDays(order.getPayTime(), tradeUserConfig.getEstConsignTime());
                        order.setEstimateConTime(estimateConTime);
                        if (CollectionUtils.isNotEmpty(order.getSuits())) {
                            order.getSuits().forEach(suit -> suit.setEstimateConTime(estimateConTime));
                        }
                        if (order.getOrigin() != null) {
                            String oldEstimateConTimeStr = time2Str(order.getOrigin().getEstimateConTime());
                            String newEstimateConTimeStr = time2Str(order.getEstimateConTime());
                            if (!StringUtils.equals(oldEstimateConTimeStr, newEstimateConTimeStr)) {
                                Logs.debug(String.format("order 承诺时间被覆盖 sid=%s orderId=%s oldEstimateConTime=%s newEstimateConTime=%s ", order.getSid(), order.getId(), oldEstimateConTimeStr, newEstimateConTimeStr));
                            }
                        }
                    }
                }
            }
            //除天猫外 、系统状态为待付款的order都不填充承诺发货时间
            if (TradeStatusUtils.isWaitPay(sysStatus) || Objects.isNull(trade.getPayTime())) {
                return;
            }
            //trade的承若发货时间不为空order以trade的承若发货时间为准
            if (Objects.nonNull(trade.getTimeoutActionTime())) {
                setOrderTimeoutActionTime(trade);
                return;
            }

            Date newTimeoutActionTime = DateUtils.addDays(trade.getPayTime(), tradeUserConfig.getEstConsignTime());
            trade.setTimeoutActionTime(newTimeoutActionTime);
            if (trade.getOrigin() != null) {
                String oldTimeoutActionTimeStr = time2Str(trade.getOrigin().getTimeoutActionTime());
                String newTimeoutActionTimeStr = time2Str(trade.getTimeoutActionTime());
                if (!StringUtils.equals(oldTimeoutActionTimeStr, newTimeoutActionTimeStr)) {
                    Logs.debug(String.format("trade 承诺时间被覆盖 sid=%s  oldEstimateConTime=%s newEstimateConTime=%s ", trade.getSid(), oldTimeoutActionTimeStr, newTimeoutActionTimeStr));
                }
            }
            setOrderTimeoutActionTime(trade);
            return;
        }
        //只有天猫，或不是待付款的淘宝订单，在未开启承若发货时间，或当前店铺承若时间配置未开启时设置兜底时间为48小时
        if (!isTianMao && !(isTaoBao && !TradeStatusUtils.isWaitPay(sysStatus))) {
            return;
        }

        if (Objects.isNull(trade.getPayTime())) {
            return;
        }
        StringBuilder log = new StringBuilder();
        if (Objects.isNull(trade.getTimeoutActionTime())) {
            Date date = DateUtils.addDays(trade.getPayTime(), 2);
            trade.setTimeoutActionTime(date);
            log.append(String.format("sid =%s trade TimeoutActionTime 不存在 默认取付款时间推后两天=%s", trade.getSid(), date));
        }
        //天猫淘宝计划发货时间各算各的(https://tb.raycloud.com/task/63647249d7a78c001df7fb3c)
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            String estimateConTimeStr = time2Str(order.getEstimateConTime());
            String payTimeStr = time2Str(order.getPayTime());
            if (StringUtils.isBlank(estimateConTimeStr) && StringUtils.isNotBlank(payTimeStr) && !PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                Date date = DateUtils.addDays(order.getPayTime(), 2);
                order.setEstimateConTime(date);
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    order.getSuits().forEach(suit -> suit.setEstimateConTime(date));
                }
                log.append(String.format("orderId =%s sid=%s order estimateConTime 不存在 默认取付款时间推后两天=%s", order.getId(), order.getSid(), time2Str(date)));
            }
        }
        if (log.length() > 0) {
            Logs.debug(String.format("companyId=%s msg=%s", trade.getCompanyId(), log));
        }
    }

    private static void setOrderTimeoutActionTime(Trade trade) {
        List<Order> orders = getOrders4Trade(trade);
        for (Order order : orders) {
            if (!PLAT_FORM_TYPE_SYS.equals(order.getSource()) && StringUtils.isBlank(time2Str(order.getEstimateConTime()))) {
                order.setEstimateConTime(trade.getTimeoutActionTime());
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    order.getSuits().forEach(suit -> suit.setEstimateConTime(trade.getTimeoutActionTime()));
                }
            }
        }
    }

    /**
     * 解析识别码
     */
    public static List<Order> parseIdentCodes(String sid, String orderIdIdentCodes) {
        if (StringUtils.isBlank(orderIdIdentCodes)) {
            return null;
        }
        JSONArray array = JSONArray.parseArray(orderIdIdentCodes);
        int size = array.size();
        List<Order> orders = new ArrayList<Order>(size);
        for (int i = 0; i < size; i++) {
            JSONObject object = array.getJSONObject(i);
            String orderId = object.getString("orderId");
            String idCodeStr = object.getString("idCode");
            Integer forcePackNum = object.getInteger("forcePackNum");
            StringBuilder identCode = new StringBuilder();
            if (StringUtils.isNotBlank(idCodeStr)) {
                String[] idCodeArr = idCodeStr.split(",");
                for (String ic : idCodeArr) {
                    if (StringUtils.isEmpty(ic)) {
                        continue;
                    }
                    identCode.append(",").append(StringUtils.trimToEmpty(ic));
                }
            }
            orders.add(identCode2Order(Long.parseLong(orderId), identCode.length() == 0 ? "" : identCode.toString().substring(1), forcePackNum));
        }
        return orders;
    }

    public static Order identCode2Order(Long orderId, String identCode, Integer forcePackNum) {
        Order udateOrder = new TbOrder();
        udateOrder.setId(orderId);
        if (StringUtils.isNotBlank(identCode)) {
            udateOrder.setIdentCode(identCode);
        }
        udateOrder.setForcePackNum(forcePackNum);
        return udateOrder;
    }

    /**
     * 解析识别码
     */
    public static List<TradePackScanInfo> parsePackScanInfos(String sid, String orderScanInfos) {
        if (StringUtils.isBlank(orderScanInfos)) {
            return null;
        }
        JSONArray array = JSONArray.parseArray(orderScanInfos);
        int size = array.size();
        List<TradePackScanInfo> tradePackScanInfos = new ArrayList<TradePackScanInfo>(size);
        for (int i = 0; i < size; i++) {
            JSONObject object = array.getJSONObject(i);
            if (!Objects.isNull(object)) {
                String orderId = object.getString("orderId");
                String scanCode = object.getString("scanCode");
                Integer codeType = !Objects.isNull(object.getInteger("codeType")) ? object.getInteger("codeType") : 0;
                Integer num = !Objects.isNull(object.getInteger("num")) ? object.getInteger("num") : 0;

                TradePackScanInfo packScanInfo = new TradePackScanInfo();
                packScanInfo.setOrderId(Long.parseLong(orderId));
                packScanInfo.setScanCode(scanCode);
                packScanInfo.setCodeType(codeType);
                packScanInfo.setNum(num);

                tradePackScanInfos.add(packScanInfo);
            }
        }
        return tradePackScanInfos;
    }

    public static boolean isPddEncrypt(String data) {
        char SEP_PHONE = '$';
        char SEP_ID = '#';
        char SEP_NORMAL = '~';
        Pattern BASE64_PATTERN = Pattern.compile("^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$");
        if (null == data || data.length() < 44) {
            return false;
        }
        if (data.charAt(0) != data.charAt(data.length() - 1)) {
            return false;
        }
        char separator = data.charAt(0);
        String[] dataArray = StringUtils.split(data, separator);
        if (dataArray.length < 2
                || !StringUtils.isNumeric(dataArray[dataArray.length - 1])) {
            return false;
        }
        if (separator == SEP_PHONE || separator == SEP_ID) {
            if (dataArray.length != 3) {
                return false;
            }
            if (data.charAt(data.length() - 2) == separator) {
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[1].length() >= 44;
            } else {
                return BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[1].length() >= 44;
            }
        }
        if (separator == SEP_NORMAL) {
            if (data.charAt(data.length() - 2) == separator) {
                if (dataArray.length != 3) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[0].length() >= 44;
            } else {
                if (dataArray.length != 2) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        dataArray[0].length() >= 44;
            }
        }
        return false;
    }

    public static void toTree(List<Trade> trades, boolean removeHide) {
        Map<Long, List<Trade>> mergeMap = new HashMap<Long, List<Trade>>();
        trades.stream().filter(t -> t.getMergeSid() > 0).forEach(t -> mergeMap.computeIfAbsent(t.getMergeSid(), s -> new ArrayList<Trade>()).add(t));
        mergeMap.forEach((mergeSid, mergeTrades) -> {
            for (Trade trade : mergeTrades) {
                if (trade.getMergeSid() - mergeSid == 0) {
                    mergeTrades.remove(trade);
                    trade.setMergeList(mergeTrades);
                    if (removeHide) {
                        trades.removeAll(mergeTrades);
                    }
                    break;
                }
            }
        });
    }


    public static boolean isFxAndNoAMBIGUITYExcepTrade(Staff staff, Trade trade) {//没有不确定供销商异常的订单
        return isFxSource(trade) && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_AMBIGUITY);
    }


    /**
     * 分销订单设置库存为正常
     *
     * @param trade
     */
    public static void applyStockForFxTrade(Staff staff, Trade trade) {
        if(!(TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isContainV(trade, V_IF_1688_FX_ROLE))){
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        for (Order order : orderList) {
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setStockNum(order.getNum());
            if (order.getSuits() != null) {
                for (Order suit : order.getSuits()) {
                    OrderExceptUtils.setStockStatus(staff, suit, Trade.STOCK_STATUS_NORMAL);
                    suit.setStockNum(order.getNum());
                }
            }
        }
        TradeExceptUtils.setStockStatus(staff, trade, Trade.STOCK_STATUS_NORMAL);

    }

    /**
     * 是否由分销商反审核异常
     *
     * @param trade
     * @return
     */
    public static boolean isUnAuditExcep(Staff staff, Trade trade) {
        if (isGxOrMixTrade(trade) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_UNAUDIT)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否为商品未匹配订单
     *
     * @param trade
     * @return
     */
    public static boolean isUnAllocatedTrade(Staff staff, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            //商品未匹配
            if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是作废订单
     *
     * @param trade
     * @return
     */
    public static boolean isCancel(Trade trade) {
        return trade.getIsCancel() == 1;
    }


    public static String getSourceByTrade(Trade fxTrade) {
        String source;
        if (TradeUtils.isGxTrade(fxTrade)) {
            Trade sourceTrade = fxTrade.getSourceTrade();
            if (sourceTrade != null) {
                source = sourceTrade.getSource();
            } else {
                source = fxTrade.getSource();
            }
        } else {
            source = fxTrade.getSource();
        }
        return source;
    }

    public static Long getUserIdByTrade(Trade fxTrade) {
        Long userId;
        if (TradeUtils.isGxTrade(fxTrade)) {
            Trade sourceTrade = fxTrade.getSourceTrade();
            if (sourceTrade != null) {
                userId = sourceTrade.getUserId();
            } else {
                userId = fxTrade.getUserId();
            }
        } else {
            userId = fxTrade.getUserId();
        }
        return userId;
    }


    public static void replaceUtf8mb4(List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        for (Trade updateTrade : updateTrades) {
            replaceUtf8mb4(updateTrade);
        }
    }

    public static void replaceUtf8mb4(Trade trade) {
        if (trade == null) {
            return;
        }
        if (StringUtils.isNotEmpty(trade.getReceiverAddress())) {
            trade.setReceiverAddress(com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(trade.getReceiverAddress()));
        }
        if (StringUtils.isNotEmpty(trade.getBuyerMessage())) {
            trade.setBuyerMessage(com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(trade.getBuyerMessage()));
        }
        if (StringUtils.isNotEmpty(trade.getSellerMemo())) {
            trade.setSellerMemo(com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(trade.getSellerMemo()));
        }
        if (StringUtils.isNotEmpty(trade.getReceiverName())) {
            trade.setReceiverName(com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(trade.getReceiverName()));
        }
        if (StringUtils.isNotEmpty(trade.getBuyerNick())) {
            trade.setBuyerNick(com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(trade.getBuyerNick()));
        }
    }

    public static String fillTradeOfSort(Trade trade) {
        if (trade != null) {
            String sortString = TradeSortParams.build(trade).toString();
            trade.setSysOuterId(StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString);
            return StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString;
        }
        return null;
    }

    public static String fillTradeOfSort(Trade trade, TradeConfig tradeConfig) {
        if (trade != null) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            //虚拟商品不统计
            if (tradeConfig != null && tradeConfig.getItemNumExcludeVirtual() == 1) {
                orders = orders.stream().filter(o -> !o.isVirtual()).collect(Collectors.toList());
            }
            //无需发货商品不统计
            if (tradeConfig != null && Objects.equals(tradeConfig.getItemNumExcludeNonConsign(), 1)) {
                orders = orders.stream().filter(o -> !o.ifNonConsign()).collect(Collectors.toList());
            }
            String sortString = TradeSortParams.build(trade, orders).toString();
            trade.setSysOuterId(StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString);
            return StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString;
        }
        return null;
    }

    public static String fillOrderOfSort(Trade trade, List<Order> orders, TradeConfig tradeConfig) {
        if (CollectionUtils.isNotEmpty(orders)) {
            //虚拟商品不统计
            if (tradeConfig != null && tradeConfig.getItemNumExcludeVirtual() == 1) {
                orders = orders.stream().filter(o -> !o.isVirtual()).collect(Collectors.toList());
            }
            //无需发货商品不统计
            if (tradeConfig != null && Objects.equals(tradeConfig.getItemNumExcludeNonConsign(), 1)) {
                orders = orders.stream().filter(o -> !o.ifNonConsign()).collect(Collectors.toList());
            }
            String sortString = TradeSortParams.build(trade, orders).toString();
            trade.setSysOuterId(StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString);
            return StringUtils.length(sortString) > 128 ? sortString.substring(0, 128) : sortString;
        }
        return null;
    }

    //    填充订单的排序字段
    public static void fillTradeOfSort(List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade var : trades) {
                fillTradeOfSort(var);
            }
        }
    }

    // 填充订单的排序字段
    public static void fillTradeOfSort(List<Trade> trades, TradeConfig tradeConfig) {
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade var : trades) {
                fillTradeOfSort(var, tradeConfig);
            }
        }
    }


    public static boolean havePlatformTrade(List<Trade> trades, String platform) {
        for (Trade trade : trades) {
            if (platform.equals(trade.getSource())) return true;
        }
        return false;
    }


    /**
     * 校验用户是否有订单店铺权限，代码迁移
     *
     * @param staff  注意staff必须是getStaff得到的，传getLightStaff会获取不到user
     * @param trades
     */
    public static void checkTradeShops(Staff staff, List<Trade> trades) {
        StringBuilder noActiveShopSids = new StringBuilder();
        for (Trade trade : trades) {
            //供销订单不校验店铺权限
            if (Objects.equals(trade.getUserId(), 100000000L)) {
                continue;
            }
            //出库单没有店铺,不校验
            if (!trade.isOutstock()) {
                User user = staff.getUserByUserId(trade.getUserId());
                if (user == null || user.getActive() == null || user.getActive() == JUDGE_NO) {
                    noActiveShopSids.append(" [").append(trade.getSid()).append("]");
                }
            }
        }
        if (StringUtils.isNotEmpty(noActiveShopSids.toString())) {
            throw new IllegalArgumentException(String.format("没有该订单%s 所属店铺的权限，请联系管理员进行开通!", noActiveShopSids.toString()));
        }
    }


    public static List<Trade> buildSimpleTrade(List<Trade> tradeList) {
        List<Trade> simpleTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(tradeList)) {
            return simpleTrades;
        }
        for (Trade trade : tradeList) {
            Trade simple = new Trade();
            simple.setSid(trade.getSid());
            simple.setTid(trade.getTid());
            simple.setCompanyId(trade.getCompanyId());
            simple.setSourceId(trade.getSourceId());
            simple.setDestId(trade.getDestId());
            simple.setBelongType(trade.getBelongType());
            simple.setConvertType(trade.getConvertType());
            simple.setTemplateType(trade.getTemplateType());
            simple.setTemplateId(trade.getTemplateId());
            simple.setOutSid(trade.getOutSid());
            simple.setSource(trade.getSource());
            simple.setSubSource(trade.getSubSource());
            simpleTrades.add(simple);

        }
        return simpleTrades;
    }

    /**
     * 过滤出来供销订单
     *
     * @param trades
     * @param <T>
     * @return
     */
    public static <T extends Trade> List<Trade> filterGxTrades(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        return trades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
    }

    /**
     * 过滤出来分销订单
     *
     * @param trades
     * @param <T>
     * @return
     */
    public static <T extends Trade> List<Trade> filterFxTrades(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        return trades.stream().filter(TradeUtils::isFxTrade).collect(Collectors.toList());
    }

    /**
     * 更新订单timeoutAction字段值
     */
//    public static <T extends Trade> List<T> resetCalculationTimeoutActionTime(List<T> tradeList, List<Order> updateOrderList) {
//        if (CollectionUtils.isEmpty(updateOrderList)) {
//            updateOrderList = new ArrayList<>();
//        }
//        Map<String, List<Long>> orderMap = updateOrderList.stream().collect(Collectors.groupingBy(Order::getTid,Collectors.mapping(Order::getSid,Collectors.toList())));
//        tradeList.stream().forEach(trade -> {
//            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
//            if (CollectionUtils.isEmpty(orderList)) {
//                return;
//            }
//
//            final List<Long> updateOrderSid = new ArrayList<>();
//            if (Objects.nonNull(orderMap.get(trade.getTid()))) {
//                updateOrderSid.addAll(orderMap.get(trade.getTid()));
//            }
//
//            Optional<Order> orderOptional = orderList.stream().filter(order -> {
//                return !updateOrderSid.contains(order.getSid()) && Objects.nonNull(order.getEstimateConTime());
//            }).sorted(Comparator.comparing(Order::getEstimateConTime)).findFirst();
//            if (orderOptional.isPresent()) {
//                trade.setTimeoutActionTime(orderOptional.get().getEstimateConTime());
//            }
//        });
//        return tradeList;
//    }
//
   /* public static <T extends Trade> List<T> resetCalculationTimeoutActionTime(List<T> tradeList) {
        return resetCalculationTimeoutActionTime(tradeList,new ArrayList<>());
    }*/

    /**
     * 将有单号的订单组装一个回收单号需要的字段的对象
     */
    public static Map<Long, Trade> buildHaveOutsidTradeMap(List<Trade> trades) {
        Map<Long, Trade> someFieldsTradeMap = new HashMap<>();//存处理前的订单数据 的部分字段 匹配后回收单号用
        for (Trade trade : trades) {
            if (StringUtils.isNotBlank(trade.getOutSid()) && trade.getTemplateId() != null && trade.getTemplateId() > 0) {
                someFieldsTradeMap.put(trade.getSid(), buildHaveOutsidTrade(trade));
            }
        }
        return someFieldsTradeMap;
    }

    /**
     * 组装回收单号需要的部分字段trade
     */
    public static Trade buildHaveOutsidTrade(Trade trade) {
        Trade someFieldsTrade = new TbTrade();
        someFieldsTrade.setSid(trade.getSid());
        someFieldsTrade.setOutSid(trade.getOutSid());
        someFieldsTrade.setWarehouseId(trade.getWarehouseId());
        someFieldsTrade.setTemplateType(trade.getTemplateType());
        someFieldsTrade.setTemplateId(trade.getTemplateId());
        someFieldsTrade.setTagIds(trade.getTagIds());
        return someFieldsTrade;
    }

    /**
     * 将有快递模板的订单组装一个回收单号需要的字段的对象
     */
    public static Map<Long, Trade> buildHaveTemplateIdTradeMap(List<Trade> trades) {
        Map<Long, Trade> someFieldsTradeMap = new HashMap<>();//存处理前的订单数据 的部分字段
        for (Trade trade : trades) {
            if (trade.getTemplateId() != null && trade.getTemplateId() > 0) {
                Trade someFieldsTrade = new TbTrade();
                someFieldsTrade.setSid(trade.getSid());
                someFieldsTrade.setOutSid(trade.getOutSid());
                someFieldsTrade.setWarehouseId(trade.getWarehouseId());
                someFieldsTrade.setTemplateType(trade.getTemplateType());
                someFieldsTrade.setTemplateId(trade.getTemplateId());
                someFieldsTradeMap.put(someFieldsTrade.getSid(), someFieldsTrade);
            }
        }
        return someFieldsTradeMap;
    }

    public static void tradeValidator(Staff staff, Trade trade, TradeConfig tradeConfig, boolean matchedAfter) {
        if (trade == null) {
            return;
        }
        int fastFail = tradeConfig.getInteger(TradeExtendConfigsEnum.WAVE_SEED_FAST_FAIL.getKey());
        if ((BooleanUtils.toBoolean(fastFail) && matchedAfter)
                || (!BooleanUtils.toBoolean(fastFail) && !matchedAfter)) {
            return;
        }
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.check(staff, trade);
        if (validator.hasError() && TradeValidator.Error.INSUFFICIENT.getCode() != validator.getCode()) {
            throw new TradeValidatorException(validator.getCode(), validator.getMessage());
        }
    }

    /**
     * 是否猫超订单
     */
    public static boolean isMCTrade(Trade trade) {
        return "tmcs".equals(trade.getSource());
    }

    /**
     * 是否档口订单
     */
    public static Boolean isDangKou(Trade trade) {
        return StringUtils.isNotBlank(trade.getType()) && trade.getType().startsWith("dangkou");
    }


    public static boolean isFxgBicTrade(Trade trade, TradeConfig config) {
        if (trade == null) {
            return false;
        }

        if (!PLAT_FORM_TYPE_FXG.equals(trade.getSource())) {
            return false;
        }

        if (!PLAT_FORM_TYPE_BIC.equals(trade.getSubSource())) {
            return false;
        }

        if (TradeConfigUtils.openBicQualityTesting(config)) {
            return isFxgBicTrade(trade, ArrayUtils.toLongSet((String) config.get(TradeExtendConfigsEnum.OPEN_BIC_QUALITY_TESTING_SHOP.getKey())));
        }

        return false;
    }

    public static boolean isFxgBicTrade(Trade trade, Set<Long> userIds) {
        if (trade == null || CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        return PLAT_FORM_TYPE_FXG.equals(trade.getSource()) && PLAT_FORM_TYPE_BIC.equals(trade.getSubSource()) && userIds.contains(trade.getUserId());
    }

    /**
     * 是否微信视频号质检订单
     *
     * @param t
     * @return
     */
    public static boolean isWxSphBicTrade(Trade t) {
        if (Objects.nonNull(t)
                && StringUtils.isNotBlank(t.getTagIds())
                && (t.getTagIds().contains(String.valueOf(SystemTags.TAG_WX_BIC.getId())))) {
            return true;
        }
        return t != null && PLAT_FORM_TYPE_WXSPH.equals(t.getSource())
                && (TradeTypeConstants.WXSPH_BIC.equals(t.getType()) || TradeTypeConstants.WXSPH_BIC.equals(t.getSubSource()));
    }

    /**
     * 是否块团团自提并且已核销订单
     */
    public static boolean isKttSelfAndCancelledTrade(Trade t) {
        if (Objects.isNull(t) || Objects.isNull(t.getTradeExt())) {
            return false;
        }

        //物流方式 0-无需物流 10-普通快递 20-自提 30-同城配送
        String logisticsType = (String) TradeExtUtils.getExtraFieldValue(t.getTradeExt(), "logisticsType");
        //核销状态 0-未核销 1-已核销 2-部分核销
        String verificationStatus = (String) TradeExtUtils.getExtraFieldValue(t.getTradeExt(), "verificationStatus");
        return Objects.equals("20", logisticsType) && Objects.equals("1", verificationStatus);
    }

    public static boolean needTradeDistributor(Trade t) {
        if (PLAT_FORM_TYPE_KUAI_SHOU.equals(t.getSource()) && "platform_cost_per_sale".equals(t.getType())) {
            return true;
        } else if (PLAT_FORM_TYPE_KTT.equals(t.getSource())) {//快团团团长业务增加活动名称
            return true;
        } else if (PLAT_FORM_TYPE_YZ.equals(t.getSource())) {//有赞分销商业务增加分销商名称
            return true;
        }
        return false;
    }

    /**
     * 是否需要计算供销运费
     * 供销或供销且分销 并且 不是现场自提
     *
     * @param t
     * @return
     */
    public static boolean needCalcGxPostFee(Trade t) {
        return isGxOrMixTrade(t) && !(t != null && t.getV() != null && (t.getV() | V_IF_KMT_SELF_PICK) - t.getV() == 0);
    }

    /**
     * 判断当前订单是否需要查询ext数据
     */
    public static boolean needTradeExt(Trade t) {
        return needTradeExt(t.getSource(), t.getSubSource()) || (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(t) && t.getTradeExt() == null);
    }

    public static boolean needTradeExt(String source, String subSource) {
        //通用的source判断放needTradeExtSource
        if (needTradeExtSource.contains(source)) {
            return true;
        } else if (PLAT_FORM_TYPE_JD.equals(subSource)
                || "jd_warehouse".equals(subSource)
                || "fds".equals(subSource)
                || PLAT_FORM_TYPE_AKC.equals(subSource)
        ) {
            return true;
        } else if (PLAT_FORM_TYPE_VIPJIT.equals(source) && PLAT_FORM_TYPE_VIPJIT.equals(subSource)) {
            return true;
        } else if (PLAT_FORM_TYPE_XIAOMANG.equals(source) && (
                PLAT_FORM_TYPE_FXG.equals(subSource)
                        || PLAT_FORM_TYPE_KUAI_SHOU.equals(subSource)
                        || PLAT_FORM_TYPE_PDD.equals(subSource)
        )) {
            return true;
        }
        return false;
    }

    /**
     * 根据sid把trade和ext数据组装起来
     *
     * @param trades
     * @param tradeExts
     */
    public static <T extends Trade> List<T> assemblyBySidForExt(List<T> trades, List<TradeExt> tradeExts) {
        if (CollectionUtils.isEmpty(trades) || CollectionUtils.isEmpty(tradeExts)) return trades;
        Map<Long, TradeExt> tradeExtMap = tradeExts.stream().collect(Collectors.toMap(TradeExt::getSid, TradeExt -> TradeExt));
        for (Trade t : trades) {
            t.setTradeExt(tradeExtMap.get(t.getSid()));
        }
        return trades;
    }

    /**
     * 根据sid把order和orderExt数据组装起来
     *
     * @param orders
     * @param orderExts
     */
    public static <O extends Order, E extends OrderExt> void assemblyBySidForOrderExt(List<O> orders, List<E> orderExts) {
        if (CollectionUtils.isEmpty(orders) || CollectionUtils.isEmpty(orderExts)) return;
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, Order -> Order));
        for (OrderExt e : orderExts) {
            Order order = orderMap.get(e.getId());
            if (order != null) {
                e.setSid(order.getSid());
                orderMap.get(e.getId()).setOrderExt(e);
            }
        }
    }


    public static boolean isFxgBicTrade(Trade trade) {
        return PLAT_FORM_TYPE_FXG.equals(trade.getSource()) && PLAT_FORM_TYPE_BIC.equals(trade.getSubSource()) && PddFzUtils.isWhiteFxgCompany(trade.getCompanyId());
    }


    /**
     * 京东送货上门新的标签。
     *
     * @param trade
     * @return
     */
    public static boolean isJdSHSM(Trade trade) {
        return PLAT_FORM_TYPE_JD.equals(trade.getSource()) && trade.getTagIds().contains(TradeSystemLabelEnum.TAG_1000000008.getId().toString());
    }

    /**
     * 京东中小件送货上门*
     *
     * @param trade
     * @return
     */
    public static boolean isJdZXJSHSMTrade(Trade trade) {
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000001496");
    }

    public static boolean needJdSHSMTrade(Trade trade) {
        return StringUtils.isNotBlank(trade.getTagIds()) && (isJdZXJSHSMTrade(trade) || isJdSHSM(trade));
    }

    /**
     * 获取订单指定物流信息
     *
     * @param trade
     * @return
     */
    public static List<String> getTradePointExpress(Trade trade, Map<String, List<String>> expressInfoMap) {
        List<String> pointExpress = new ArrayList<>();
        if (trade.getTradeExt() != null && StringUtils.isNotBlank(trade.getTradeExt().getLogisticsCode())) {
            String pointExpressKey = trade.getTradeExt().getLogisticsCode();
            pointExpress = Arrays.asList(pointExpressKey.split(","));
            if (PLAT_FORM_TYPE_PDD.equals(getSourceByTrade(trade))) {
                List<String> expressIds = new ArrayList<>();
                for (String pointCode : pointExpress) {
                    List<String> list = expressInfoMap.get(pointCode);
                    if (CollectionUtils.isNotEmpty(list))
                        expressIds.addAll(list);
                }
                pointExpress = expressIds;
                if (CollectionUtils.isEmpty(pointExpress)) {
                    Logs.ifDebug(
                            String.format("快递匹配失败,指定物流匹配失败 companyId=%s，sid=%s，logisticsCode=%s，expressInfoMap=%s",
                                    trade.getCompanyId(), trade.getSid(), pointExpressKey, expressInfoMap));
                }
            }
        }
        return pointExpress;
    }

    /**
     * 获取订单指定物流信息
     *
     * @param trade
     * @return
     */
    public static List<String> getTradePointExpress(Trade trade, Map<String, List<String>> expressInfoMap, Collection<String> supportPlatformNames) {
        List<String> pointExpress = new ArrayList<>();
        if (trade.getTradeExt() != null && StringUtils.isNotBlank(trade.getTradeExt().getLogisticsCode())) {
            String pointExpressKey = trade.getTradeExt().getLogisticsCode();
            pointExpress = Arrays.asList(pointExpressKey.split(","));
            if (supportPlatformNames.contains(getSourceByTrade(trade))) {
                List<String> expressIds = new ArrayList<>();
                for (String pointCode : pointExpress) {
                    List<String> list = new ArrayList<>();
                    if (expressInfoMap.get(pointCode) != null)
                        list = expressInfoMap.get(pointCode).stream().map(x -> x + "").collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(list))
                        expressIds.addAll(list);
                }
                pointExpress = expressIds;
            }
        }
        return pointExpress;
    }

    /**
     * 是否偏远中转订单
     * 1000000199	fxg	偏远直邮
     * 1000000205	fxg	新疆中转(抖音)
     * 1000000206	fxg	西藏中转(抖音)
     *
     * @param trade
     * @return
     */
    public static boolean isRemoteTransferTrade(Trade trade) {
        return StringUtils.isNotBlank(trade.getTagIds()) && (trade.getTagIds().contains("1000000205") || trade.getTagIds().contains("1000000206"));
    }

    /**
     * 是否偏远中转订单
     * 1000000199	fxg	偏远直邮
     * 1000000205	fxg	新疆中转(抖音)
     * 1000000206	fxg	西藏中转(抖音)
     *
     * @param trade
     * @return
     */
    public static boolean isRemoteTransferTrade(AiTrade trade) {
        return CollectionUtils.isNotEmpty(trade.getTagIds()) && (trade.getTagIds().contains("1000000205") || trade.getTagIds().contains("1000000206"));
    }

    /**
     * @param trade
     * @return boolean
     * @description: 标签id：1000001493 = 京东偏远集运
     * @author: tanyi
     * @date: 2024-08-29 11:57
     */
    public static boolean isRemoteJDTrade(Trade trade) {
        return StringUtils.isNotEmpty(trade.getTagIds()) && trade.getTagIds().contains("1000001493");
    }

    /**
     * @param trade
     * @return boolean
     * @description: 标签id：1000001493 = 京东偏远集运
     * @author: tanyi
     * @date: 2024-08-29 11:57
     */
    public static boolean isRemoteJDTrade(AiTrade trade) {
        return CollectionUtils.isNotEmpty(trade.getTagIds()) && trade.getTagIds().contains("1000001493");
    }

    /**
     * @Description: 使用正则表达式，取出""中的商家编码
     * @Author: DuanYue
     */
    public static String getOuterId(String outerIdAndSysItemIds) {
        if (StringUtils.isEmpty(outerIdAndSysItemIds)) {
            return "";
        }
        List outerIdList = new ArrayList<>();
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile("\"(.*?)\"");
        Matcher matcher = pattern.matcher(outerIdAndSysItemIds);
        while (matcher.find()) {
            outerIdList.add(matcher.group());
        }
        return outerIdList.toString();
    }


    /**
     * 根据条件筛选商家编码日志
     *
     * @param params
     * @return
     */
    public static String handleExcludeQueryParams(TradeControllerParams params) {
        if (StringUtils.isNotBlank(params.getExcludeOuterIdAndSysSkuIds())) {
            if (StringUtils.isNotBlank(params.getExcludeOuterIdAndSysItemIds())) {
                return "排除任意规格商家编码，规格商家编码如下：" + TradeUtils.getOuterId(params.getExcludeOuterIdAndSysItemIds() + TradeUtils.getOuterId(params.getExcludeOuterIdAndSysSkuIds()));
            } else {
                return "排除任意规格商家编码，规格商家编码如下：" + TradeUtils.getOuterId(params.getExcludeOuterIdAndSysSkuIds());
            }
        } else if (StringUtils.isNotBlank(params.getExcludeOuterIdAndSysItemIds())) {
            return "排除任意主商家编码，系统商家编码如下: " + TradeUtils.getOuterId(params.getExcludeOuterIdAndSysItemIds());
        } else {
            return "";
        }
    }

    /**
     * 筛选出满足系统配置中状态和异常都符合条件的订单
     * 发货中状态以值来区分：待打印 1 待包装 2  待称重 4  待发货 8
     * 如果页面上勾选了多个，则传入相加的值即可。如：传入的值为3，则表示待打印和待包装的订单
     *
     * @param tradeList
     * @param tradeConfig
     * @return
     */
    public static List<Trade> satisfyStatusAndExcep(Staff staff, List<Trade> tradeList, TradeConfig tradeConfig) {
        List<Trade> updateList = new ArrayList<>(tradeList);
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        if (StringUtils.isEmpty(tradeExtendConfig)) {
            return new ArrayList<>();
        }

        //获取配置中设置的订单状态
        Object tradeStatus = tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_STATUS.getKey());
        Integer status = (Integer) Optional.ofNullable(tradeStatus).orElse(0);
        if (Objects.equals(status, 0)) {
            return new ArrayList<>(0);
        }
        //获取配置中设置的订单异常
        String tradeExceptions = (String) tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_EXCEPTION.getKey());
        if (StringUtils.isBlank(tradeExceptions)) {
            return new ArrayList<>(0);
        }
        List<String> exceptions = Arrays.stream(tradeExceptions.split(",")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(exceptions)) {
            return new ArrayList<>(0);
        }

        updateList = updateList.stream().filter(trade -> filterByExcep(staff, trade, tradeConfig, status, exceptions)).collect(Collectors.toList());

        return updateList;
    }

    /**
     * @param staff
     * @param trade
     * @param tradeConfig
     * @param status      配置的状态 {@link TradeDeliveringStatusEnum}
     * @param exceptions  配置的异常
     * @return
     */
    private static boolean filterByExcep(Staff staff, Trade trade, TradeConfig tradeConfig, int status, List<String> exceptions) {
        String tempStatus = TradeStatusUtils.convertSysStatus(trade, tradeConfig);
        TradeDeliveringStatusEnum tradeDeliveringStatusEnum = TradeDeliveringStatusEnum.tradeDeliveringStatusEnumMap.get(tempStatus);
        if (tradeDeliveringStatusEnum == null) {
            return false;
        }
        //如果订单的状态或异常不在配置范围内，说明该订单不符合条件
        if ((tradeDeliveringStatusEnum.getValue() & status) == 0) {
            return false;
        }
        //通过analyze方法计算得到的系统异常,TradeExceptionUtils.analyze 获取的是Ex_开头的与界面相同的映射
        Set<String> tradeExceptEnglish = TradeExceptViewUtils.getTradeExceptEnglish(staff, trade);
        Set<Long> customExceptIds = TradeExceptUtils.getCustomExceptIds(staff, trade);
        if (CollectionUtils.isNotEmpty(customExceptIds)) {
            tradeExceptEnglish.addAll(customExceptIds.stream().map(String::valueOf).collect(Collectors.toSet()));
        }
        boolean b = exceptions.stream().anyMatch(except -> tradeExceptEnglish.contains(except));
        return b;
    }

    public static boolean platformContain(Staff staff, Trade trade, List<String> platform) {
        if (platform.contains(trade.getSource())) {
            return true;
        } else if (PLAT_FORM_TYPE_1688.equals(trade.getSource()) && platform.contains(trade.getSubSource())) { // 1688分销
            return true;
        } else if (PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (null != user && platform.contains(user.getSource())) {
                return true;
            } else {
                return false;
            }
        } else {
            if (staff.getUserIdMap() == null) {
                return false;
            }
            User user = staff.getUserIdMap().get("newfx".equalsIgnoreCase(trade.getSource()) ? trade.getTaobaoId() : trade.getUserId());
            if (user != null && platform.contains(user.getSource())) {
                return true;
            }
            return false;
        }
    }


    /**
     * 获取订单原始来源
     *
     * @param staff
     * @param trade
     * @return
     */
    public static String getOrgiPlatform(Staff staff, Trade trade) {
        if (PLAT_FORM_TYPE_SYS.equals(trade.getSource()) || StringUtils.isBlank(trade.getSource())) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (null != user) {
                return user.getSource();
            }
        } else if (PLAT_FORM_TYPE_NEW_FX.equalsIgnoreCase(trade.getSource())) {
            TradeExt tradeExt = trade.getTradeExt();
            if (tradeExt != null) {
                Object source = TradeExtUtils.getExtraFieldValue(tradeExt, "source");
                if (source != null) {
                    return String.valueOf(source);
                }
            }
            if (staff.getUserIdMap() == null) {
                return null;
            }
            User user = staff.getUserIdMap().get(trade.getTaobaoId());
            if (user != null) {
                return user.getSource();
            }
        }
        return trade.getSource();
    }

    /**
     * 获取平台原始的PlatformId
     * 售后和供销订单 需要根据 extFields->platformId进行解密，"-"后面的需求去掉
     *
     * @param trade
     * @return
     */
    public static String getTradeOrgiPlatformId(Trade trade) {
        TradeExt tradeExt = trade.getTradeExt();
        if (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields())) {
            if (StringUtils.contains(trade.getTid(), "-")) {
                return trade.getTid().split("-")[0];
            }
            return trade.getTid();
        } else {
            JSONObject jsonObject = JSON.parseObject(tradeExt.getExtraFields());
            String platformId = jsonObject.getString("platformId");
            if (StringUtils.isBlank(platformId)) {
                if (StringUtils.contains(trade.getTid(), "-")) {
                    return trade.getTid().split("-")[0];
                }
                return trade.getTid();
            }

            return platformId;
        }
    }

    public static boolean platformContainExcludeFx(Staff staff, Trade trade, List<String> platform) {
        if (platform.contains(trade.getSource())) {
            return true;
        } else if (PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (null != user && platform.contains(user.getSource())) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 根据条件筛选商家编码日志
     */
    public static String handleQueryParams(TradeControllerParams params) {
        if (StringUtils.isNotBlank(params.getOuterIdAndSysSkuIds())) {
            if (StringUtils.isNotBlank(params.getOuterIdAndSysItemIds())) {
                return "包含任意规格商家编码，规格商家编码如下：" + TradeUtils.getOuterId(params.getOuterIdAndSysSkuIds() + TradeUtils.getOuterId(params.getOuterIdAndSysItemIds()));
            } else {
                return "包含任意规格商家编码，规格商家编码如下：" + TradeUtils.getOuterId(params.getOuterIdAndSysSkuIds());
            }
        } else if (StringUtils.isNotBlank(params.getOuterIdAndSysItemIds())) {
            return "包含任意主商家编码，系统商家编码如下: " + TradeUtils.getOuterId(params.getOuterIdAndSysItemIds());
        } else if (StringUtils.isNotBlank(params.getOnlyOuterIdAndSysSkuIds())) {
            if (StringUtils.isNotBlank(params.getOnlyOuterIdAndSysItemIds())) {
                return "仅包含规格商家编码，规格商家编码如下: " + TradeUtils.getOuterId(params.getOnlyOuterIdAndSysSkuIds() + TradeUtils.getOuterId(params.getOnlyOuterIdAndSysItemIds()));
            } else {
                return "仅包含规格商家编码，规格商家编码如下: " + TradeUtils.getOuterId(params.getOnlyOuterIdAndSysSkuIds());
            }
        } else if (StringUtils.isNotBlank(params.getOnlyOuterIdAndSysItemIds())) {
            return "仅包含主商家编码，系统商家编码如下: " + TradeUtils.getOuterId(params.getOnlyOuterIdAndSysItemIds());
        } else {
            return "";
        }
    }


    /**
     * 检查订单是否为挂起和平台修改地址异常
     */
    public static boolean checkTradeIsHaltAndModifyAddress(Staff staff, Trade trade) {
        if (trade == null) {
            return false;
        }
        boolean containHaltExcept = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT);
        boolean containAddressChangedExcept = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED);
        if (containHaltExcept && containAddressChangedExcept
                && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())
                || TradeStatusUtils.isWaitFinanceAudit(trade.getSysStatus()) || com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) || isSplit(trade))) {
            return true;
        }
        return false;
    }

    /**
     * 判断订单是否为已发货订单且只含有退款异常
     */
    public static boolean checkTradeContainRefundOrder(Staff staff, Trade trade) {
        if (trade == null) {
            return false;
        }

        if (staff != null) {
            User user = null;
            if (staff.getUserIdMap() != null) {
                user = staff.getUserIdMap().get(trade.getUserId());
            }
            if (user == null) {
                user = staff.getUserByUserId(trade.getUserId());
            }
            //店铺停用的订单是异常订单
            if (user != null && Objects.equals(user.getActive(), VALUE_NO)) {
                return false;
            }
        }
        Set<Integer> exceptions = parseExcept(staff, trade);
        if (Objects.equals(exceptions.size(), JUDGE_YES) && exceptions.contains(IDX_REFUNDING)) {
            return true;
        }

        return false;
    }


    // 计算一批合单的利润
    public static double caculateMainTradeGrossProfit(List<Trade> trades) {
        double payment = 0D;
        double cost = 0L;
        double packmaCost = 0L;
        double postFee = 0L;
        for (Trade trade : trades) {
            payment += calculateGrossProfitByDivideOrderFee(trade);
            //payment += NumberUtils.str2Double(trade.getPayment());
            // 主单成本已经将隐藏单的成本加进去了，无需再累加
            if (Objects.equals(trade.getSid(), trade.getMergeSid())) {
                cost = trade.getCost() != null ? trade.getCost() : 0D;
                packmaCost = trade.getPackmaCost() != null ? trade.getPackmaCost() : 0D;
                postFee = StringUtils.isNoneBlank(trade.getActualPostFee()) ? Double.valueOf(trade.getActualPostFee()) : 0D;
            }
        }
        double grossProfit = new BigDecimal(payment - cost - packmaCost - postFee).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        return grossProfit;
    }

    /**
     * 快递公司维度匹配老模板 手工单推供销订单需要 取 ext里的shopSource
     */
    public static String getSourceOnLogistics(Staff staff, Trade trade) {
        if (isGxTrade(trade)) {
            if (trade.getTradeExt() != null && StringUtils.isNotEmpty(trade.getTradeExt().getExtraFields())) {
                JSONObject extFields = JSONObject.parseObject(trade.getTradeExt().getExtraFields());
                String source = extFields.getString("source");
                if (StringUtils.isNotBlank(source) && !PLAT_FORM_TYPE_SYS.equals(source)) {
                    return source;
                }
                String shopSource = extFields.getString("shopSource");
                if (StringUtils.isNotBlank(shopSource) && !PLAT_FORM_TYPE_SYS.equals(shopSource)) {
                    return shopSource;
                }
            }
            return trade.getSubSource();
        }
        // 小时达匹配模版需要特殊判断，返回fxg_xsd，小时达订单走getTradePlatformSource()方法会返回fxg,任务KMERP-227411做的为了支持抖音小时达走抖音取号
        if (isFxgXsdSource(trade)) {
            return trade.getSource();
        }
        return getTradePlatformSource(staff, trade);
    }

    /**
     * 根据trade获取平台source
     *
     * @param staff
     * @param trade
     * @return
     */
    public static String getTradePlatformSource(Staff staff, Trade trade) {
        if (trade == null || trade.getSource() == null) {
            return "";
        }
        if (isVipSovTrade(trade)) {
            return PLAT_FORM_TYPE_VIPSOV;
        }
        // 供销订单及多级分销订单source
        if (isGxTrade(trade) && StringUtils.isNotBlank(trade.getSubSource())) {
            if (trade.getTradeExt() != null && StringUtils.isNotEmpty(trade.getTradeExt().getExtraFields())) {
                String source = JSONObject.parseObject(trade.getTradeExt().getExtraFields()).getString("source");
                if (StringUtils.isNotBlank(source) && !PLAT_FORM_TYPE_SYS.equals(source)) {
                    return source;
                }
            }
            return trade.getSubSource();
        }
        if (PLAT_FORM_TYPE_AKC.equals(trade.getSource())) {
            return trade.getSource();
        }
        // 奇门放心购供销订单、放心购小时达返回放心购类型
        if (isQiMenFxgGxTrade(trade)) {
            return PLAT_FORM_TYPE_FXG;
        }
        // 拼多多厂商代发返回拼多多类型
        if (Objects.equals(trade.getSubSource(), CommonConstants.PLAT_FORM_TYPE_FDS)) {
            return CommonConstants.PLAT_FORM_TYPE_PDD;
        }
        String source = StringUtils.isBlank(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_OTHER.equals(trade.getSubSource()) ? trade.getSource() : trade.getSubSource();
        if (StringUtils.isNotBlank(trade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            // 非系统订单 代表是平台分销 奇门 供分销订单
            return source;
        }
        //下面手工单处理
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(source) && (StringUtils.isBlank(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_OTHER.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSubSource()))) {
            //纯手工单取店铺的source 快手例外
            User user = staff.getUserByUserId(trade.getUserId());
            if (user != null) {
                source = CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(user.getSource()) ? source : user.getSource();
            }
        }
        //fxg 不允许非平台订单取号 特殊处理 不是密文 就返回sys
        if (CommonConstants.FXG_SOURCE_LIST.contains(source)) {
            source = isFXGEncryption(trade) ? source : CommonConstants.PLAT_FORM_TYPE_SYS;
        }
        return source;
    }

    /**
     * 是否1688分销订单-订单收件人信息是明文，1688进行了加密的订单
     *
     * @return
     */
    public static boolean is1688FxEncryptTrade(Trade trade) {
        // ，判定是1688订单
        if (trade != null && StringUtils.isNotBlank(trade.getSubSource())) {
            if (trade.getTradeExt() != null && StringUtils.isNotEmpty(trade.getTradeExt().getExtraFields())) {
                JSONObject extraFields = JSONObject.parseObject(trade.getTradeExt().getExtraFields());
                String fromEncryptOrder = extraFields.getString("fromEncryptOrder");
                if ("false".equals(fromEncryptOrder)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isNeteaseAndFxgTrade(Trade trade) {
        return PLAT_FORM_TYPE_NETEASE.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG.equals(trade.getSubSource());
    }

    /**
     * 是否好时期平台来源为放心购的订单*
     *
     * @return
     */
    public static boolean isHSQAndFXGTrade(Trade trade) {
        return PLAT_FORM_TYPE_HAOSHIQI.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG.equals(trade.getSubSource());
    }

    public static boolean isSLTAndFXGTrade(Trade trade) {
        return PLAT_FORM_TYPE_SHULIANTONG.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG.equals(trade.getSubSource());
    }

    public static boolean isSLTAndPDDTrade(Trade trade) {
        return PLAT_FORM_TYPE_SHULIANTONG.equals(trade.getSource()) && PLAT_FORM_TYPE_PDD.equals(trade.getSubSource());
    }

    public static boolean isSLTAndKSTrade(Trade trade) {
        return PLAT_FORM_TYPE_SHULIANTONG.equals(trade.getSource()) && PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSubSource());
    }

    /**
     * jd 平台的分销订单
     */
    public static boolean isJDFXTrade(Trade trade) {
        return (PLAT_FORM_TYPE_JD.equals(trade.getSource()) || PLAT_FORM_TYPE_JD_VC.equals(trade.getSource())) && StringUtils.isNotBlank(trade.getSubSource());
    }

    /**
     * https://gykj.yuque.com/entavv/xb9xi5/ex1yalhfeuswd6qs
     * 平台分销
     * source是platformFxSourceList这些，subSource有值 且不是和source一样来判断的。还有source不等于sys
     */
    public static boolean isPlatformFx(Trade trade) {
        return trade != null && platformFxSourceList.contains(trade.getSource()) && StringUtils.isNotBlank(trade.getSubSource()) && !StringUtils.equals(trade.getSource(), trade.getSubSource());
    }

    /**
     * jd 平台的fxg分销订单
     */
    public static boolean isJD4FXGTrade(Trade trade) {
        return (PLAT_FORM_TYPE_JD.equals(trade.getSource()) || PLAT_FORM_TYPE_JD_VC.equals(trade.getSource())) && PLAT_FORM_TYPE_FXG.equals(trade.getSubSource());
    }

    /**
     * 是否奇门放心购供销订单*
     *
     * @param trade
     * @return
     */
    public static boolean isQiMenFxgGxTrade(Trade trade) {
        return PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG_GX.equals(trade.getSubSource());
    }

    public static boolean isSNAndFXGTrade(Trade trade) {
        return PLAT_FORM_TYPE_SN.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG.equals(trade.getSubSource());
    }

    public static boolean isSNAndFXGXsdTrade(Trade trade) {
        return PLAT_FORM_TYPE_SN.equals(trade.getSource()) && PLAT_FORM_TYPE_FXG_XSD.equals(trade.getSubSource());
    }

    /**
     * 奇门fxg*
     *
     * @param trade
     * @return
     */
    public static boolean isQiMenAndFXGTrade(Trade trade) {
        return (QIMEN_DISTRIBUTOR.equals(trade.getSource()) || PLAT_FORM_TYPE_SYS.equals(trade.getSource())) && ConvertQiMenSourceEnum.isFxg(trade.getSubSource());
    }

    /**
     * 走放心购取号的其他平台*
     *
     * @param trade
     * @return
     */
    public static boolean otherPlatformToFXG(Trade trade) {
        return isSNAndFXGTrade(trade)
                //|| isQiMenFxgGxTrade(trade)
                || isHSQAndFXGTrade(trade)
                || isSLTAndFXGTrade(trade)
                || checkXiaomangSource(trade, PLAT_FORM_TYPE_FXG)
                || isQiMenAndFXGTrade(trade)
                || isFxgcsSource(trade)
                || isNeteaseAndFxgTrade(trade)
                || isFxgXsdSource(trade);
    }

    public static boolean isQiMenAndYzTrade(Trade trade) {
        return QIMEN_DISTRIBUTOR.equals(trade.getSource()) && PLAT_FORM_TYPE_YZ.equals(trade.getSubSource());
    }

    /**
     * @param trade
     * @return boolean
     * @description: 是否为抖音即时零售
     * @author: tanyi
     * @date: 2025-02-24 11:13
     */
    private static boolean isFxgXsdSource(Trade trade) {
        return PLAT_FORM_TYPE_FXG_XSD.equals(trade.getSource());
    }
    /**
     * 获取订单source，手工单获取User的source
     *
     * @param staff
     * @param trade
     * @return
     */
    public static String getSource(Staff staff, Trade trade) {
        if (trade == null) {
            return "";
        }
        if (PLAT_FORM_TYPE_SHULIANTONG.equals(trade.getSource())) {
            return StringUtils.isEmpty(trade.getSubSource()) ? trade.getSource() : trade.getSubSource();
        }
        // 得到1688分销平台source
        if (PLAT_FORM_TYPE_1688.equals(trade.getSource()) && StringUtils.isNotBlank(trade.getSubSource())) {
            return ConvertAlibabaSourceEnum.getPlatformSource(trade.getSubSource());
        }
        if (isVipSovTrade(trade)) {
            return PLAT_FORM_TYPE_VIPSOV;
        }
        //转换奇门的subSource为系统source
        if (PLAT_FORM_TYPE_QIMEN.equals(trade.getSource())) {
            return ConvertQiMenSourceEnum.getPlatformSourceByQiMenSource(trade.getSubSource());
        }
        if (PLAT_FORM_TYPE_XIAOMANG.equals(trade.getSource()) || PLAT_FORM_TYPE_HAOSHIQI.equals(trade.getSource())) {
            return trade.getSubSource();
        }
        // 供销订单及多级分销订单source
        if (isGxTrade(trade) && StringUtils.isNotBlank(trade.getSubSource())) {
            if (trade.getTradeExt() != null && StringUtils.isNotEmpty(trade.getTradeExt().getExtraFields())) {
                String source = JSONObject.parseObject(trade.getTradeExt().getExtraFields()).getString("source");
                if (StringUtils.isNotBlank(source) && !PLAT_FORM_TYPE_SYS.equals(source)) {
                    return source;
                }
            }
            return trade.getSubSource();
        }
        if (StringUtils.isNotBlank(trade.getSource()) && !PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            return trade.getSource();
        }
        if (trade.getUserId() != null) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (user != null) {
                // 1688分销手工单
                if (PLAT_FORM_TYPE_1688.equals(user.getSource()) && StringUtils.isNotBlank(trade.getSubSource())) {
                    return ConvertAlibabaSourceEnum.getPlatformSource(trade.getSubSource());
                }
                return user.getSource();
            }
        }
        return "";
    }

    /**
     * 判断订单是否为唯品会sov订单
     * 判断条件：source不为vipjit且不为vipjitx 并且 subsource为vipsov
     *
     * @param trade
     * @return
     */
    public static boolean isVipSovTrade(Trade trade) {
        if (trade == null) {
            return false;
        }

        if (PLAT_FORM_TYPE_VIPJIT.equalsIgnoreCase(trade.getSource())) {
            if (StringUtils.isEmpty(trade.getSubSource()) || PLAT_FORM_TYPE_VIPSOV.equalsIgnoreCase(trade.getSubSource())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 不需要回收单号的订单类型*
     * @param trade
     * @return
     */
    public static boolean notCannelWaybillTrade(Trade trade) {
        if (trade == null) {
            return true;
        }
        return isVipJitTrade(trade) || isVipJitxTrade(trade) || isPoisonBrandDeliverTrade(trade);
    }

    public static boolean isVipJitTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        return PLAT_FORM_TYPE_VIPJIT.equalsIgnoreCase(trade.getSubSource());
    }

    public static boolean isVipJitxTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        return PLAT_FORM_TYPE_VIPJIT.equalsIgnoreCase(trade.getSource()) && PLAT_FORM_TYPE_VIPJITX.equalsIgnoreCase(trade.getSubSource());
    }

    /**
     * 卖家旺旺是否使用openUid
     *
     * @param source
     * @return
     */
    public static boolean needReturnOpenUidWithBuyerNick(String source) {
        if (PLAT_FORM_TYPE_TAO_BAO.equals(source)
                || PLAT_FORM_TYPE_TIAN_MAO.equals(source)
                || PLAT_FORM_TYPE_TAO_BAO_TJB.equals(source)
                || PLAT_FORM_TYPE_1688.equals(source)) {
            return true;
        }
        return false;
    }

    /**
     * 判断订单是否为快手订单
     * 判断条件：source kuaishou 或者 subsource为kuaishou
     *
     * @param trade
     * @return
     */
    public static boolean isKuaishouTrade(Trade trade) {
        if (trade == null) {
            return false;
        }

        if (PLAT_FORM_TYPE_KUAI_SHOU.equalsIgnoreCase(trade.getSource()) || PLAT_FORM_TYPE_KUAI_SHOU.equalsIgnoreCase(trade.getSubSource())) {
            return true;
        }

        return false;
    }

    public static Trade createSellerMemoFlagTrade(Trade origin) {
        Trade update = new TbTrade();
        update.setSid(origin.getSid());
        update.setTid(origin.getTid());
        update.setSellerMemo(origin.getSellerMemo());
        update.setSellerFlag(origin.getSellerFlag());
        return update;
    }

    /**
     * 是否同步时对卖家备注进行了修改
     *
     * @param trade
     * @return
     */
    public static boolean isUpdateSellerMemo(Trade trade) {
        if (trade == null || trade.getOrigin() == null) {
            return false;
        }

        Trade origin = trade.getOrigin();

        if (Objects.isNull(origin.getSellerFlag())) {
            origin.setSellerFlag(0L);
        }

        if (Objects.isNull(trade.getSellerFlag())) {
            trade.setSellerFlag(0L);
        }

        if (trade.getSellerMemo() != null && !StringUtils.trimToEmpty(trade.getSellerMemo()).equals(StringUtils.trimToEmpty(origin.getSellerMemo()))) {
            return true;
        }

        if (!TradeUtils.isEqualsSellerFlag(origin.getSellerFlag(), trade.getSellerFlag())) {
            //天猫国际、淘工厂、微信小店（微信视频号）的不同步sellerFlag
            if (PLAT_FORM_TYPE_WXSPH.equals(trade.getSource()) || PLAT_FORM_TYPE_TMGJZY.equals(trade.getSource()) || PLAT_FORM_TYPE_1688_C2M.equals(trade.getSource())) {
                return false;
            }
            return true;
        }
//

        return false;
    }

    /**
     * 是否同步时，系统修改过备注，还原系统修改的备注
     *
     * @param trade
     * @return
     */
    public static boolean notUpdateSellerMemo(Trade trade) {
        if (null == trade || null == trade.getOrigin()) {
            return false;
        }

        Trade origin = trade.getOrigin();

        if (null != trade.getSellerMemo() && StringUtils.trimToEmpty(trade.getSellerMemo()).equals(StringUtils
                .trimToEmpty(origin.getSellerMemo())) && StringUtils.isNotBlank(trade.getPlatformSellerMemo())) {
            return true;
        }

        if (null != trade.getSellerFlag() && TradeUtils.isEqualsSellerFlag(origin.getSellerFlag(), trade.getSellerFlag()) && null != trade.getPlatformFlag()) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否需要匹配跨境物流规则
     *
     * @param trade
     * @return
     */
    public static boolean needMatchOverseasLogisticRule(Trade trade) {
        if (trade == null) {
            return false;
        }
        return needMatchOverseasLogisticRule(trade.getSource());
    }

    /**
     * 判断是否需要匹配跨境物流规则
     *
     * @param source
     * @return
     */
    public static boolean needMatchOverseasLogisticRule(String source) {
        if (PLAT_FORM_TYPE_SHOPEE.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_LAZADA.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_AMAZON.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_ALIBABA_ICBU.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_TIKTOK.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_JOOM.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_OZON.equalsIgnoreCase(source) ||
                PLAT_FORM_TYPE_SMT.equalsIgnoreCase(source)) {
            return true;
        }
        return false;
    }

    /**
     * 根据order重算trade的承诺时间,需要过滤掉已经退款以及发货后的状态的
     */
    public static void resetTimeOutTimeAction(Trade trade) {
        Optional<Date> optional = TradeUtils.getOrders4Trade(trade).stream()
                .filter(order ->
                        Objects.nonNull(order.getEstimateConTime())
                                && !OrderUtils.isAfterSendGoods(order)
                                && !Order.REFUND_SUCCESS.equals(order.getRefundStatus()))
                .sorted(Comparator.comparing(Order::getEstimateConTime))
                .map(Order::getEstimateConTime).findFirst();
        optional.ifPresent(trade::setTimeoutActionTime);
    }

    /**
     * 放心购 || 拼多多厂家代发 || 淘宝厂商代发
     *
     * @param trade
     * @return
     */
    public static boolean isDfSource(Trade trade) {
        if (null == trade) {
            return false;
        }
        String tradeSource = trade.getSource();
        return PLAT_FORM_TYPE_FXG_DF.equals(tradeSource) ||
                PLAT_FORM_TYPE_TAO_BAO_DF.equals(tradeSource) ||
                PLAT_FORM_TYPE_KUAISHOU_DF.equals(tradeSource) ||
                PLAT_FORM_TYPE_TAO_BAO_DF.equals(trade.getType()) ||
                (Objects.equals(tradeSource, PLAT_FORM_TYPE_PDD) &&
                        Objects.equals(trade.getSubSource(), PLAT_FORM_TYPE_FDS)
                );

    }

    /**
     * 是否为某个平台店铺的订单，包含手工单
     */
    public static boolean isPlatformSourceTrade(Staff staff, Trade trade, String platformSource) {
        if (platformSource.equals(trade.getSource())) {
            return true;
        }
        if (PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (user == null) {
                return false;
            } else {
                return platformSource.equals(user.getSource());
            }
        }
        return false;
    }

    /**
     * 获取订单中的warehouseId
     *
     * @param tradeList
     * @return
     */
    public static Long getWareHouseIdByTrades(List<Trade> tradeList) {
        // 判断是否得物订单，得物订单直接返回 -1
        List<Trade> poisonTrades = tradeList.stream()
                .filter(trade -> TradeUtils.isPoisonBrandDeliverTrade(trade)
                        && (Objects.isNull(trade.getTradeExt()) || StringUtils.isBlank(trade.getTradeExt().getLogisticsCode())))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(poisonTrades)) {
            return -1L;
        }

        List<Long> warehouseIds = tradeList.stream().map(Trade::getWarehouseId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseIds) || warehouseIds.size() != 1) {
            throw new IllegalArgumentException("订单里的仓库Id为空或存在多个！");
        }
        return warehouseIds.get(0);
    }

    /**
     * packmaOuterIds 转化为 商家编码*数量 格式
     *
     * @param sids
     * @param packmaOuterIds
     * @return
     */
    public static Map<Long, String> handlePackmaOuterIds(String sids, String packmaOuterIds) {
        try {
            if (StringUtils.isBlank(packmaOuterIds)) {
                return null;
            }
            HashMap<Long, String> map = new HashMap<>(1);
            JSONArray array = JSONArray.parseArray(packmaOuterIds);
            int size = array.size();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < size; i++) {
                JSONObject object = array.getJSONObject(i);
                builder.append(object.getString("outerId")).append("*").append(object.getInteger("amount")).append("，");
            }
            if (builder.length() > 0) {
                builder.deleteCharAt(builder.length() - 1);
            }
            for (Long sid : ArrayUtils.toLongArray(sids)) {
                map.put(sid, builder.toString());
            }
            return map;
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean isTbDfTrade(Trade trade) {
        return PLAT_FORM_TYPE_TAO_BAO_DF.equals(trade.getSource());
    }

    public static List<TbTrade> toTbTrades(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<TbTrade> list = new ArrayList<>(trades.size());
        if (trades != null && trades.size() > 0) {
            for (Trade trade : trades) {
                list.add((TbTrade) trade);
            }
        }
        return list;
    }


    /**
     * 买家信息是否需要脱敏
     *
     * @param trade
     * @return
     */
    public static Boolean buyerNeedEncode(Trade trade) {
        if (trade == null) {
            return false;
        }
        if (Objects.equals(PLAT_FORM_TYPE_YZ, trade.getSource())
                || Objects.equals(PLAT_FORM_TYPE_DD, trade.getSource())
                || Objects.equals(PLATFROM_TYPE_THH, trade.getSource())
                || Objects.equals(PLAT_FORM_TYPE_SN, trade.getSource())
                || Objects.equals(PLAT_FORM_TYPE_VIPJIT, trade.getSource())
                || Objects.equals(PLAT_FORM_TYPE_VIPJITX, trade.getSource())
                || Objects.equals(PLAT_FORM_TYPE_ALIHEALTH, trade.getSource())) {
            return true;
        }
        return false;
    }

    /**
     * 是否是得物直发类型订单
     */
    public static boolean isPoisonBrandDeliverTrade(Trade trade) {
        return (PLAT_FORM_TYPE_POISON.equals(trade.getSource()) && TradeTypeConstants.isPoisonBrandDeliverType(trade.getType()))
                || (PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && ConvertQiMenSourceEnum.DWZF.getQimenSource().equalsIgnoreCase(trade.getSubSource()))
                || (PLAT_FORM_TYPE_NEWFX.equals(trade.getSource()) && PLAT_FORM_TYPE_POISON.equalsIgnoreCase(trade.getSubSource()));
    }

    public static boolean isSheinDeliverTrade(Trade trade) {
        return PLAT_FORM_TYPE_SHEIN.equals(trade.getSource());
    }

    public static boolean isSmtqtgDeliverTrade(Trade trade) {
        return PLAT_FORM_TYPE_SMTQTG.equals(trade.getSource());
    }

    public static boolean isJpsTrade(Trade trade) {
        return Objects.equals(trade.getIsJps(), "1");
    }

    /**
     * 拆单一单多包获取主单*
     *
     * @param tradeList
     * @return
     */
    public static Trade getSplitMain(List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return null;
        }
        // 判断母单标签
        List<Trade> mainTradeList = tradeList.stream()
                .filter(TradeUtils::haveMainSplitTag)
                .collect(Collectors.toList());

        if (mainTradeList.size() == 1) {
            return mainTradeList.get(0);
        }

        // 没有标签的进行选举
        return tradeList.stream()
                .min(Comparator.comparingLong(Trade::getSid))
                .get();
    }

    /**
     * 这里要判断是否有"拆单子母单-母单"标签*
     *
     * @param trade
     * @return
     */
    public static boolean haveMainSplitTag(Trade trade) {
        // 这里要判断是否有"拆单子母单-母单"标签
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000503");
    }

    public static boolean haveAllMainSplitTag(Trade trade) {
        // 这里要判断是否有"拆单子母单-母单"标签
        return haveMainSplitTag(trade) || haveMainAddressSplitTag(trade);
    }

    public static boolean haveMainAddressSplitTag(Trade trade) {
        // 这里要判断是否有"拆单子母单-母单"标签
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000505");
    }

    /**
     * 是否含有拆单一单多包子单标签*
     *
     * @param trade
     * @return
     */
    public static boolean haveChildSplitTag(Trade trade) {
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000504");
    }

    public static boolean haveAllChildSplitTag(Trade trade) {
        return haveChildSplitTag(trade) || haveChildAddressSplitTag(trade);
    }

    public static boolean haveChildAddressSplitTag(Trade trade) {
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000506");
    }

    public static boolean haveSplitGetTag(Trade trade) {
        return haveMainSplitTag(trade) || haveChildSplitTag(trade);
    }

    public static boolean haveAllSplitGetTag(Trade trade) {
        return haveSplitGetTag(trade) || haveAddressSplitGetTag(trade);
    }

    public static boolean haveAddressSplitGetTag(Trade trade) {
        return haveMainAddressSplitTag(trade) || haveChildAddressSplitTag(trade);
    }

    /**
     * 是否有唯品会Mp订单发货后修改单号标签*
     *
     * @param trade
     * @return
     */
    public static boolean haveMPUpdateTag(Trade trade) {
        // 这里要判断是否有"拆单子母单-母单"标签
        return StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000515");
    }

    /**
     * 得物品牌直发且普通履约订单
     *
     * @param trade
     * @return
     */
    public static boolean isPoisonBrandDeliverPerformanceTrade(Trade trade) {
        if (!isPoisonBrandDeliverTrade(trade)) {
            return false;
        }
        // TradeExt.logisticsCode 不为空则为 2-商家指定物流 @see com.raycloud.dmj.services.poison.trades.PoisonBrandDeliverTradeCopy.dealAppointExpressTypeTrade
        // return Objects.isNull(trade.getTradeExt()) || StringUtils.isBlank(trade.getTradeExt().getLogisticsCode());

        // KMERP-139568: 得物直发履约模式判断 performance_type: 1-普通履约, 2-商家指定物流, 3-多仓发货-商家指定发货仓
        return Optional.ofNullable(trade.getTradeExt())
                .map(TradeExt::getExtraFields)
                .filter(StringUtils::isNotBlank)
                .map(JSONObject::parseObject)
                .map(json -> json.getString("performance_type"))
                .filter(type -> Objects.equals("1", type))
                .isPresent();
    }

    public static boolean isSystemTrade(Trade trade) {
        if (Objects.equals(trade.getSource(), PLAT_FORM_TYPE_SYS) && StringUtils.isEmpty(trade.getAddressMd5()) && !trade.getReceiverMobile().contains("*")) {
            return true;
        }
        return false;
    }

    public static boolean isKsDfTrade(Trade trade) {
        return PLAT_FORM_TYPE_KUAISHOU_DF.equals(trade.getSource());
    }

    /**
     * Jitx补寄订单*
     * @param trade
     * @return
     */
    public static boolean isJitxRemailTrade(Trade trade) {
        return PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource()) && PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())
                && Objects.nonNull(trade.getType()) && trade.getType().equals("5");
    }

    public static boolean isSmtqtgTrade(Trade trade) {
        return PLAT_FORM_TYPE_SMTQTG.equals(trade.getSource());
    }


    /**
     * 是否是得物直发指定物流类型订单
     */
    public static boolean isPoisonSpecifyLogistics(Trade trade) {
        if (!isPoisonBrandDeliverTrade(trade)) {
            return false;
        }
        // performance_type != 1 就认定为指定物流
        return Optional.ofNullable(trade.getTradeExt())
                .map(TradeExt::getExtraFields)
                .filter(StringUtils::isNotBlank)
                .map(fields -> JSONObject.parseObject(fields).getString("performance_type"))
                .filter(type -> Objects.equals("2", type))
                .isPresent();
    }

    /**
     * 放心购指定物流订单*
     *
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isFxgSpecifyLogistics(Staff staff, Trade trade) {
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSubSource())) {
            User userByUserId = staff.getUserByUserId(trade.getUserId());
            if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(userByUserId.getSource()) || CommonConstants.PLAT_FORM_TYPE_FXG_CS.equals(userByUserId.getSource())) {
                return false;
            }
        }
        if (PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) || PLAT_FORM_TYPE_FXG_CS.equals(trade.getSource())) {
            //所有奇门订单都不判断指定物流
            return false;
        }
        TradeExt tradeExt = trade.getTradeExt();
        if (Objects.isNull(tradeExt)) {
            return false;
        }
        //先取主单 没有就取子单的补偿一下
        String logisticsCode = tradeExt.getLogisticsCode();
        if (StringUtils.isBlank(logisticsCode)) {
            List<MessageMemo> messageMemos = trade.getMessageMemos();
            if (CollectionUtils.isEmpty(messageMemos)) {
                //为空 也没有合单明细
                return false;
            }
            for (MessageMemo messageMemo : messageMemos) {
                if (StringUtils.isNotBlank(messageMemo.getLogisticsCode())) {
                    logisticsCode = messageMemo.getLogisticsCode();
                    break;
                }
            }
        }
        String tradePlatformSource = getTradePlatformSource(staff, trade);
        return PLAT_FORM_TYPE_FXG.equals(tradePlatformSource)
                && StringUtils.isNotBlank(logisticsCode)
                && !"noCode".equals(logisticsCode);
    }

    /**
     * 是否pdd上门揽件
     *
     * @return
     */
    public static boolean isPddZF(Trade trade) {
        return PLAT_FORM_TYPE_PDD.equals(trade.getSource()) && StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains("1000000128");
    }


    /**
     * 放心购订单Tid的去A与补A处理
     *
     * @param tidList
     * @return
     */
    public static List<String> getMoreTidForFxg(List<String> tidList) {
        if (CollectionUtils.isEmpty(tidList)) {
            return new ArrayList<>();
        }
        Set<String> tidSet = new HashSet<>(tidList.size());
        for (String tid : tidList) {
            tidSet.add(tid);
            if (tid.endsWith(TradeConstants.FXG_SUFFIX)) {
                tidSet.add(tid.substring(0, tid.length() - 1));//去A
            } else {
                tidSet.add(tid + TradeConstants.FXG_SUFFIX);//加A
            }
        }
        return new ArrayList<>(tidSet);
    }

    /**
     * 判断小芒订单的实际平台
     * subSource 不能给null
     */
    public static boolean checkXiaomangSource(Trade trade, String subSource) {
        return PLAT_FORM_TYPE_XIAOMANG.equals(trade.getSource()) && subSource != null && subSource.equals(trade.getSubSource());
    }

    /**
     * 判断1889订单的实际平台
     * subSource 不能给null
     */
    public static boolean check1889Source(Trade trade, String subSource) {
        return PLAT_FORM_TYPE_1889.equals(trade.getSource()) && subSource != null && subSource.equals(trade.getSubSource());
    }

    /**
     * 是否为发货前订单
     */
    public static boolean isSendBefore(Trade trade) {
        boolean ptNotConsign = true;
        if (Objects.nonNull(trade.getUnifiedStatus())) {
            ptNotConsign = Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getUnifiedStatus()) || Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(trade.getUnifiedStatus());
        }
        //平台未发货             系统未发货
        return ptNotConsign && TradeStatusUtils.isWaitSellerSend(trade.getSysStatus());
    }

    /**
     * 获取系统赠品的数量
     *
     * @param staff
     * @param trade
     * @return
     */
    public static Integer getSysGiftNum(Staff staff, Trade trade) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        Integer reduce = orders4Trade.stream().filter(order -> order.isGift() && PLAT_FORM_TYPE_SYS.equalsIgnoreCase(order.getSource())).map(Order::getNum).reduce(0, (a, b) -> a + b);
        return reduce;
    }


    /**
     * trade集合合单分组
     *
     * @param staff
     * @param trades
     * @return
     */
    public static Map<Long, List<Trade>> trades2MergeMap(Staff staff, List<Trade> trades) {
        Map<Long, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : trades) {
            tradeMap.computeIfAbsent(trade.getMergeSid() > 0 ? trade.getMergeSid() : trade.getSid(), k -> new ArrayList<>()).add(trade);
        }
        return tradeMap;
    }

    /**
     * 获取分销订单所属平台
     *
     * @param trade
     * @return
     * @see com.raycloud.dmj.domain.user.Shop#getSourceName()
     */
    public static String getSubSourceName(Trade trade) {
        if (trade == null) {
            return null;
        }
        String subSourceName = null;
        if (StringUtils.isNotEmpty(trade.getSubSource())) {
            //非供销
            subSourceName = PlatformSourceConstants.getSourceName(trade.getSubSource());
        }
        return subSourceName;
    }

    /**
     * 系统拆单且是放心购平台全款预售订单
     *
     * @param dbTrade       数据库订单
     * @param platformTrade 平台订单
     * @return
     */
    public static boolean isFxgSysSplitAndPlatformFullPresell(Trade dbTrade, Trade platformTrade) {
        return TradeUtils.isSplit(dbTrade) && PLAT_FORM_TYPE_FXG.equals(platformTrade.getSource()) && StringUtils.isNotBlank(platformTrade.getType()) && FULL_PRESELL_TAG.equals(platformTrade.getType());
    }

    /**
     * 填充默认昵称
     */
    public static void fillDefaultBuyerNick(List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        tradeList.forEach(trade -> {
            if (StringUtils.isBlank(trade.getBuyerNick()) && StringUtils.isNotBlank(trade.getReceiverMobile())) {
                trade.setBuyerNick(trade.getReceiverMobile());
            } else if (StringUtils.isBlank(trade.getBuyerNick()) && StringUtils.isNotBlank(trade.getReceiverName())) {
                trade.setBuyerNick(trade.getReceiverName());
            }
        });
    }

    /**
     * 是否补发订单
     *
     * @param trade
     * @return
     */
    public static boolean isReissue(Trade trade) {
        return StringUtils.isNotEmpty(trade.getType())
                && TradeConstants.TYPE_TRADE_REISSUE.equals(trade.getType());
    }

    /**
     * 是否为店加现货发货订单
     *
     * @param trade
     * @return
     */
    public static boolean isDianPlusXHTrade(Trade trade) {
        return trade != null && PLAT_FORM_TYPE_DIANPLUS.equals(trade.getSource())
                && TradeTypeConstants.DIANPLUS_XH_TRADE_TYPE.equals(trade.getType());
    }

    /**
     * 判断是否是跨境订单可换模板
     *
     * @param trade
     * @return
     */
    public static boolean isAbroadTrade(Trade trade) {
        List<String> abroadSource = new ArrayList<>();
        abroadSource.add(PLAT_FORM_TYPE_SHOPEE);
        abroadSource.add(PLAT_FORM_TYPE_LAZADA);
        abroadSource.add(PLAT_FORM_TYPE_SMT);
        if (abroadSource.contains(trade.getSource())) {
            return true;
        }
        return false;
    }

    /**
     * 是否1688分销小站
     *
     * @param trade
     * @return
     */
    public static boolean isAlibabaFxTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        if (PLAT_FORM_TYPE_1688_FX.equals(trade.getSource())) {
            return true;
        }
        return false;
    }
    public static boolean isAlibabaFxRoleTrade(Trade trade){
        return  ifContainV(trade, V_IF_1688_FX_ROLE);
    }

    public static boolean isAlibabaAeTrade(Trade trade) {
        if (trade == null || !PLAT_FORM_TYPE_1688.equals(trade.getSource()) || trade.getTradeExt() == null) {
            return false;
        }
        try {
            return Optional.ofNullable(trade.getTradeExt().getExtraFields())
                    .filter(StringUtils::isNotBlank)
                    .map(JSONObject::parseObject)
                    .map(json -> json.getString("aeTrade"))
                    .filter(type -> Objects.equals("Y", type))
                    .isPresent();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 打印测判断是否合单，
     * 组包订单，售后工单，采退单等无MergeSid
     *
     * @param trade
     * @return
     */
    public static boolean printIsMerge(Trade trade) {
        return trade.getMergeSid() != null && trade.getMergeSid() > 0;
    }

    /**
     * 秦丝 不在erp发货订单
     *
     * @param trade
     * @return
     */
    public static boolean isQinsiNotConsignTrade(Trade trade) {
        return trade != null && PLAT_FORM_TYPE_QINSI.equals(trade.getSource())
                && TradeTypeConstants.QINSI_NOT_CONSIGN_TRADE_TYPE.equals(trade.getType());
    }

    public static String getTidOrPlatformId(Trade trade) {
        TradeExt tradeExt = trade.getTradeExt();
        if (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields())) {
            if (StringUtils.contains(trade.getTid(), "-")) {
                return trade.getTid().split("-")[0];
            }
            return trade.getTid();
        } else {
//            JSONObject jsonObject = JSON.parseObject(tradeExt.getExtraFields());
//            String platformId = jsonObject.getString("platformId");
            String platformId = String.valueOf(TradeExtUtils.getExtraFieldValue(tradeExt, "platformId"));
            if (StringUtils.isBlank(platformId) || "null".equals(platformId)) {
                if (StringUtils.contains(trade.getTid(), "-")) {
                    return trade.getTid().split("-")[0];
                }
                return trade.getTid();
            }
            return platformId;
        }
    }

    /**
     * 买家指定物流是否发生变更
     *
     * @param trade
     * @return
     */
    public static boolean hasLogisticsCodeChange(Trade trade) {
        Optional<String> oldLogistics = Optional.ofNullable(trade.getTradeExt()).map(TradeExt::getLogisticsCode);
        Optional<String> newLogistics = Optional.ofNullable(trade.getOrigin()).map(Trade::getTradeExt).map(TradeExt::getLogisticsCode);

        return oldLogistics.isPresent() && newLogistics.isPresent() && !oldLogistics.equals(newLogistics);
    }

    /**
     * 获取奇门供销订单
     *
     * @param trades
     * @return
     */
    public static List<Trade> getQimenFxTrades(List<Trade> trades) {
        List<Trade> qimenFxTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return qimenFxTrades;
        }
        return trades.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
    }

    /**
     * 是否是震坤行直发类型订单
     */
    public static boolean isZhenkunhangDirectTrade(Trade trade) {
        return (PLAT_FORM_TYPE_ZHENKUNHANG.equals(trade.getSource()) && TradeTypeConstants.ZHENKUNHANG_DIRECT_TRADE_TYPE.equals(trade.getType()));
    }

    /**
     * 是否是震坤行代管代发类型订单
     */
    public static boolean isZhenkunhangLogisticsTrade(Trade trade) {
        return (PLAT_FORM_TYPE_ZHENKUNHANG.equals(trade.getSource()) && TradeTypeConstants.ZHENKUNHANG_LOGISTICS_TRADE_TYPE.equals(trade.getType()));
    }

    /**
     * 花城农夫帮卖订单
     */
    public static boolean isNfgoodHelpSellTrade(Trade trade) {
        return TradeTypeEnum.NFGOOD_HELP_SELL.getTypeToConfirm().apply(trade);
    }


    /**
     * 是否快团团帮卖订单
     *
     * @param trade
     * @return
     */
    public static boolean isKttHelpSaleTrade(Trade trade) {
        if (trade == null) {
            return false;
        }

        if (!PLAT_FORM_TYPE_KTT.equals(trade.getSource())) {
            return false;
        }

        if ("helpSale".equals(trade.getType())) {
            return true;
        }

        return false;
    }

    /**
     * 是否礼品订单
     *
     * @param trade
     * @return
     */
    public static boolean isPresentTrade(Trade trade) {

        return Optional.ofNullable(trade.getTradeExt())
                .map(TradeExt::getExtraFields)
                .filter(StringUtils::isNotBlank)
                .map(fields -> JSONObject.parseObject(fields).getString("isPresent"))
                .filter(type -> Objects.equals("1", type))
                .isPresent();
    }

    /**
     * 是否淘宝礼品订单
     *
     * @param trade
     * @return
     */
    public static boolean isTbPresentTrade(Trade trade) {
        if (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            return isPresentTrade(trade);
        }
        return false;
    }

    /**
     * 快手是否能合并订单
     */
    public static boolean isKuaishouMerge(Trade trade) {
        if (PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource())) {
            if (Objects.nonNull(trade.getTradeExt())) {
                Integer mergeDeliveryType = (Integer) trade.getTradeExt().get("mergeDeliveryType");
                if (Objects.nonNull(mergeDeliveryType)) {
                    return !Objects.equals(1, mergeDeliveryType);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 是否淘宝清仓订单
     * https://open.taobao.com/doc.htm?docId=121945&docType=1
     * 1）字段名称：qn_distr
     * 2）字段值含义：“1”代表是闲鱼渠道的订单，后续可能扩充新的渠道“2”、“3”。想要区分订单是否为清仓订单，只需要判断字段值是否为空，不为空就代表这笔订单是清仓订单
     *
     * @param trade
     * @return
     */
    public static boolean isTbQingCangTrade(Trade trade) {
        if (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            return Optional.ofNullable(trade.getTradeExt())
                    .map(TradeExt::getExtraFields)
                    .filter(StringUtils::isNotBlank)
                    .map(fields -> JSONObject.parseObject(fields).getString("qnDistr"))
                    .filter(qnDistr -> StringUtils.isNotBlank(qnDistr))
                    .isPresent();
        }
        return false;
    }

    /**
     * 淘宝  红包支付订单
     *
     * @param trade
     * @return
     */
    public static boolean isTbRedPacketTrade(Trade trade) {
        if (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            return Optional.ofNullable(trade.getTradeExt())
                    .map(TradeExt::getExtraFields)
                    .filter(StringUtils::isNotBlank)
                    .map(fields -> JSONObject.parseObject(fields).getLong("redPacketFee"))
                    .filter(tmallCouponFee -> tmallCouponFee != null && tmallCouponFee > 0)
                    .isPresent();
        }
        return false;
    }

    /**
     * 判断订单是不是京东京配订单
     *
     * @param trade
     * @return
     */
    public static boolean isJdJpTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        if (PLAT_FORM_TYPE_JD_GXPT.equals(trade.getSource()) || PLAT_FORM_TYPE_JD.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_JD_VC.equals(trade.getSource())) {
            return (StringUtils.isNotBlank(trade.getSubSource()) && trade.getSubSource().contains("jd_jp")) ||
                    (StringUtils.isNotBlank(trade.getType()) && trade.getType().contains("jd_jp"));
        }
        return false;
    }


    /**
     * 判断订单是不是京东京配自提订单
     *
     * @param trade
     * @return
     */
    public static boolean isJdJpZtTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        if (PLAT_FORM_TYPE_JD.equals(trade.getSource())) {
            return StringUtils.isNotBlank(trade.getSubSource()) && trade.getSubSource().contains("jd_jpzt");
        }
        return false;
    }

    /**
     * 京东鉴定仓订单
     * @param trade
     * @return
     */
    public static boolean isJdIdentifyStoreTrade(Trade trade) {
        if (trade == null) {
            return false;
        }
        return PLAT_FORM_TYPE_JD.equals(trade.getSource()) && PLAT_FORM_TYPE_JD_IDENTIFY_STORE.equals(trade.getSubSource());
    }

    /**
     * 淘宝 是否需要校验 sn  imei码
     *
     * @param trade
     * @return
     */
    public static boolean isTbNeedGovSnCheck(Trade trade) {
        if (PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            return StringUtils.isNotBlank((String) TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "govSnCheck"));
        }
        return false;
    }

    /**
     * 是否京东线上锁定订单
     * @param trade
     * @return
     */
    public static boolean isJdOnlineLockTrade(Trade trade) {
        if(PLAT_FORM_TYPE_JD.equals(trade.getSource())){
            Object onlineLock = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "online_lock");
            return Objects.equals("1", String.valueOf(onlineLock));
        }
        return false;
    }

    /**
     *   京东订单  由线上锁定 变为 非锁定
     * @param exsitTrade
     * @param toUpdateTrade
     * @return
     */
    public static boolean isJdOnlineLockTradeToUnlock(Trade exsitTrade, Trade toUpdateTrade) {
        if(exsitTrade == null || toUpdateTrade == null){
            return false;
        }
        return isJdOnlineLockTrade(exsitTrade) && (isJdOnlineLockTrade(toUpdateTrade) == false);
    }

    /**
     * 如果京东数据库中的金额为0  变为非0  则金额更新（京东的金额有时存在延迟）
     * @param exsitTrade
     * @param toUpdateTrade
     * @return
     */
    public static boolean isJdDbTradePaymentZero(Trade exsitTrade, Trade toUpdateTrade) {
        if(exsitTrade == null || toUpdateTrade == null || !PLAT_FORM_TYPE_JD.equals(exsitTrade.getSource())){
            return false;
        }
        return NumberUtils.str2Double(exsitTrade.getPayment()) == 0.00d && NumberUtils.str2Double(toUpdateTrade.getPayment()) > 0.00;
    }

    public static boolean isJdDbOrderPaymentZero(Order exsitOrder, Order toUpdateOrder) {
        if(exsitOrder == null || toUpdateOrder == null || !PLAT_FORM_TYPE_JD.equals(exsitOrder.getSource())){
            return false;
        }
        return NumberUtils.str2Double(exsitOrder.getPayment()) == 0.00d && NumberUtils.str2Double(toUpdateOrder.getPayment()) > 0.00;
    }

    public static void main(String[] args) {
        System.out.println(NumberUtils.str2Double("0") == 0.00d && NumberUtils.str2Double("0.01") > 0.00);
    }

    /**
     * 是否为刷单订单
     * 场景1. 买家留言或卖家备注存在 fakeOrderConfig.sellerMemoBuyerMessageKey 字符
     * 场景2. 订单下所有的商品 分别 都满足 单价*数量 < fakeOrderConfig.orderPaymentBenchmark
     * 场景3. 订单下所有的order 对应的 pic_path符合配置 判定为刷单
     */
    public static boolean isFakeOrder(Trade trade) {
        if (ConfigHolder.TRADE_BUSINESS_CONFIG == null) {
            return false;
        }
        FakeOrderConfig fakeOrderConfig = ConfigHolder.TRADE_BUSINESS_CONFIG.getFakeOrderConfig();
        if (fakeOrderConfig == null) {
            return false;
        }
        // skipCompanyIds
        Set<Long> skipCompanyIds = fakeOrderConfig.getSkipCompanyIds();
        Long tradeCompanyId = trade.getCompanyId();
        if (skipCompanyIds != null && tradeCompanyId != null && skipCompanyIds.contains(tradeCompanyId)) {
            return false;
        }

        // limitSource
        Set<String> limitSource = fakeOrderConfig.getLimitSource();
        String source = trade.getSource();
        if (source == null || source.isEmpty()) {
            return false;
        }
        if (limitSource != null && !limitSource.isEmpty() && !limitSource.contains(trade.getSource())) {
            return false;
        }

        // 买家留言卖家备注
        Set<String> sellerBuyerKeys = fakeOrderConfig.getSellerBuyerKeys();
        String sellerMemo = trade.getSellerMemo(), buyerMessage = trade.getBuyerMessage();
        if (sellerMemo != null && sellerBuyerKeys != null && sellerBuyerKeys.stream().anyMatch(sellerMemo::contains)) {
            return true;
        }
        if (buyerMessage != null && sellerBuyerKeys != null && sellerBuyerKeys.stream().anyMatch(buyerMessage::contains)) {
            return true;
        }

        List<Order> orders = getOrders4Trade(trade);
        if (orders == null || orders.isEmpty()) {
            return false;
        }

        // order pic_path
        Map<Long, Set<String>> companyIdToOrderPicPath = fakeOrderConfig.getCompanyIdToOrderPicPath();
        Set<String> picPaths;
        if (tradeCompanyId != null &&
                companyIdToOrderPicPath != null &&
                !companyIdToOrderPicPath.isEmpty() &&
                (picPaths = companyIdToOrderPicPath.get(tradeCompanyId)) != null &&
                orders.stream().allMatch(o -> {
                    String picPath = o.getPicPath();
                    return picPath != null && !picPath.isEmpty() && picPaths.contains(picPath);
                })
        ) {
            return true;
        }

        // order payment
        String benchmarkStr = fakeOrderConfig.getOrderPaymentBenchmark();
        if (benchmarkStr == null || benchmarkStr.isEmpty()) {
            return false;
        }
        BigDecimal benchmarkBD = new BigDecimal(benchmarkStr);
        if (benchmarkBD.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        return orders.stream().allMatch(o -> {
            String payment = o.getPayment();
            if (payment == null || payment.isEmpty()) {
                return true;
            }
            BigDecimal paymentBD = new BigDecimal(payment);
            return paymentBD.compareTo(benchmarkBD) < 0;
        });
    }
    /**
     *  天猫淘宝国补订单实际未享受国补订单
     * @param trade
     * @return
     */
    public static boolean isGovSubsidyUnShare(Trade trade) {
        if(PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())){
            return "1".equals(String.valueOf(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "govSubsidyUnShare")));
        }
        return false;
    }

    public static boolean isQimenSellerMemoEquals(String s1, String s2) {
        return StringUtils.equals(cleanMemo(s1), cleanMemo(s2));
    }

    private static String cleanMemo(String s) {
        if (s == null) {
            return "";
        }
        String pattern = "(" +
                Pattern.quote("【卖家留言】:") + "|" +
                Pattern.quote("【备注】:") + "|" +
                Pattern.quote(";") +
                ")";
        return s.replaceAll(pattern, "");
    }
}

package com.raycloud.dmj.domain.trades;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 作废订单结果
 * <AUTHOR>
 * @Date 2023/3/23 3:21 下午
 */
@Getter
@Setter
public class CancelResult  implements Serializable {
    private static final long serialVersionUID = 998946025528881929L;

    private List<Trade> successList = new ArrayList<>();
    private List<String> errorList = new ArrayList<>();
    private Map<String,String> errorMap = new HashMap<>();
    private boolean ifDmsTradeGxEditNotRelFx;
}

package com.raycloud.dmj.domain.trades;

import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.trades.utils.DateSplitUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

public class TradeExportParams extends ExportParams implements Serializable {
    public static final int PUSH_SID_TO_DB = ExportParams.ININT_STATUS;
    public static final int PULL_TRADE_FROM_DB = ExportParams.RUN_STATUS;
    private static final long serialVersionUID = 7672090734565640086L;
    private HashSet<Long> distanceTable;

    private TradeQueryParams params;

    private String exportFields;

    private Integer maxSize;

    private Date originStart;

    private Date originEnd;

    private boolean needCopyTrade;

    //用于解决数据集不稳定的问题
    private Date inserted;

    private Long totalCount = 0L;

    private RuntimeIdSnapshot runtime;

    private Boolean isNewExport;

    private List<List<Long>> exportSidsPage;

    private boolean exportRelation;
    /**
     * 导出一单多包各包裹的实际重量和实付运费
     */
    private boolean exportMultiPackActualInfo;

    public TradeExportParams(){
        inserted = new Date();
    }

    public static TradeExportParams build(TradeQueryParams params, String exportFields, Integer maxSize, boolean needCopyTrade) {
        return build(params, exportFields, maxSize, needCopyTrade, false);
    }

    public static TradeExportParams build(TradeQueryParams params, String exportFields, Integer maxSize, boolean needCopyTrade, boolean exportRelation) {
        TradeExportParams tradeExportParams = new TradeExportParams();
        Sort sort = params.getSort();
        String timeType = params.getTimeType();
        tradeExportParams.setOriginStart(params.getStartTime());
        tradeExportParams.setOriginEnd(params.getEndTime());
        tradeExportParams.setExportRelation(exportRelation);
        if (params.getContext().isUniqueQuery() || (params.getOutSids() != null && params.getOutSids().length > 0) || ("mobileTail".equals(params.getKey()) && StringUtils.isNotBlank(params.getText()))) {
            params.setStartTime(null);
        }
        int sortValue = 1;
        if (sort != null) {
            sort.setField(StringUtils.isNotEmpty(timeType) ? timeType : "pay_time");
            sortValue = "ASC".equalsIgnoreCase(sort.getOrder()) ? 1 : -1;
        }
        tradeExportParams.setParams(params);
        tradeExportParams.setExportFields(exportFields);
        tradeExportParams.setMaxSize(maxSize);
        tradeExportParams.setNeedCopyTrade(needCopyTrade);
        Integer exportTimeInterval = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getExportTimeInterval();
        if (exportTimeInterval == null || exportTimeInterval < 0){
            exportTimeInterval = 24;
        }
        List<TimeSlice> timeSlices = DateSplitUtils.dateSplit(params.getStartTime(), params.getEndTime() , exportTimeInterval * sortValue);
        tradeExportParams.setTimeSlices(timeSlices);
        return tradeExportParams;
    }


    public TradeQueryParams getParams() {
        return params;
    }

    public void setParams(TradeQueryParams params) {
        this.params = params;
        this.params.setInserted(inserted);
    }

    public String getExportFields() {
        return exportFields;
    }

    public void setExportFields(String exportFields) {
        this.exportFields = exportFields;
    }

    public Integer getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }

    public boolean isNeedCopyTrade() {
        return needCopyTrade;
    }

    public void setNeedCopyTrade(boolean needCopyTrade) {
        this.needCopyTrade = needCopyTrade;
    }

    public Date getOriginStart() {
        return originStart;
    }

    public void setOriginStart(Date originStart) {
        this.originStart = originStart;
    }

    public Date getOriginEnd() {
        return originEnd;
    }

    public void setOriginEnd(Date originEnd) {
        this.originEnd = originEnd;
    }

    public HashSet<Long> getDistanceTable() {
        return distanceTable;
    }

    public void setDistanceTable(HashSet<Long> distanceTable) {
        this.distanceTable = distanceTable;
    }

    public Date getInserted() {
        return inserted;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public RuntimeIdSnapshot getRuntime() {
        return runtime;
    }

    public void setRuntime(RuntimeIdSnapshot runtime) {
        this.runtime = runtime;
    }

    public int getCurrentTask() {
        return this.getStatus();
    }

    public void setCurrentTask(int currentTask) {
        setStatus(currentTask);
    }

    public Boolean getNewExport() {
        return isNewExport;
    }

    public void setNewExport(Boolean newExport) {
        isNewExport = newExport;
    }

    public List<List<Long>> getExportSidsPage() {
        return exportSidsPage;
    }

    public void setExportSidsPage(List<List<Long>> exportSidsPage) {
        this.exportSidsPage = exportSidsPage;
    }

    public boolean isExportRelation() {
        return exportRelation;
    }

    public void setExportRelation(boolean exportRelation) {
        this.exportRelation = exportRelation;
    }

    public boolean isExportMultiPackActualInfo() {
        return exportMultiPackActualInfo;
    }

    public void setExportMultiPackActualInfo(boolean exportMultiPackActualInfo) {
        this.exportMultiPackActualInfo = exportMultiPackActualInfo;
    }
}

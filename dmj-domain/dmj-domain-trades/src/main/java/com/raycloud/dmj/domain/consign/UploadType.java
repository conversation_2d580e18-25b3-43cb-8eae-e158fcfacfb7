package com.raycloud.dmj.domain.consign;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * 上传类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public enum UploadType {
    /**
     * 上传运单号
     */
    UPLOAD(1, "上传运单号"),
    /** 修改运单号 */
    RE_UPLOAD(2, "修改运单号"),
    /** 上传额外运单号 (通过多包裹接口) */
    MUL_PACK_UPLOAD(3, "上传额外运单号"),
    /** 上传无需物流发货信息 */
    DUMMY(4, "无需物流发货"),
    ;

    @Getter
    private final int key;
    @Getter
    private final String desc;

    UploadType(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    private static final Map<Integer, UploadType> KEY_TYPE_MAP = Maps.newHashMap();
    static {
        for (UploadType type : UploadType.values()) {
            KEY_TYPE_MAP.put(type.getKey(), type);
        }
    }
    public static UploadType formByKey(int key) {
        return KEY_TYPE_MAP.get(key);
    }
}

package com.raycloud.dmj.domain.trades.params;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TradePackScanInfo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xubin
 * @desc:  包装验货参数
 * @date : 2/21/22  10:26 AM
 */
public class TradePackParams implements Serializable {

    private static final long serialVersionUID = -5485849495669004099L;

    public static final Integer SOURCE_PC = 1;

    public static final Integer SOURCE_PDA = 2;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 订单号
     */
    private Long[] sids;

    /**
     * 快递单号
     */
    private String outSid;

    /**
     * 子订单
     */
    private List<Order> orders;

    /**
     * 扫描信息
     */
    private List<TradePackScanInfo> packScanInfos;

    /**
     * 扫描唯一码
     */
    private List<String> uniqueCodes;

    /**
     * 是否后置绑定
     */
    private boolean isBind;

    /**
     * ip
     */
    private String clientIp;

    /**
     * 是否支持强制
     */
    private boolean canForce;

    /**
     * 包装验货时 订单对应的包材信息
     */
    private Map<Long, String> packmaOuterIds;

    private String packmaOuterIdStr;

    /**
     * 是否是强制验货
     */
    private boolean force;

    private boolean suitPack;

    /**
     * 校验交易关闭状态,默认不校验
     */
    private boolean validClosedStatus;

    /**
     * 验货拆单
     */
    private boolean packSplit;

    private Long packSplitPrintSid;

    /**
     * 是否跳过校验店铺权限
     */
    private boolean skipValidStaffUser;

    /**
     * 是否自动包装验货
     */
    private boolean autoPack;

    /**
     * 来源 1/null-pc 2-pda
     */
    private Integer source;

    /**
     * 是否开启包装验货完自动收货 0-否 1-是
     */
    private Integer openPackAutoReceiveGoods;

    /**
     * 包材修改来源
     */
    private Integer packMaOptSource;


    public Integer getOpenPackAutoReceiveGoods() {
        return openPackAutoReceiveGoods;
    }

    public void setOpenPackAutoReceiveGoods(Integer openPackAutoReceiveGoods) {
        this.openPackAutoReceiveGoods = openPackAutoReceiveGoods;
    }

    public boolean isSuitPack() {
        return suitPack;
    }

    public void setSuitPack(boolean suitPack) {
        this.suitPack = suitPack;
    }


    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long[] getSids() {
        return sids;
    }

    public void setSids(Long[] sids) {
        this.sids = sids;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public List<TradePackScanInfo> getPackScanInfos() {
        return packScanInfos;
    }

    public void setPackScanInfos(List<TradePackScanInfo> packScanInfos) {
        this.packScanInfos = packScanInfos;
    }

    public List<String> getUniqueCodes() {
        return uniqueCodes;
    }

    public void setUniqueCodes(List<String> uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    public boolean isBind() {
        return isBind;
    }

    public void setBind(boolean bind) {
        isBind = bind;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public boolean isCanForce() {
        return canForce;
    }

    public void setCanForce(boolean canForce) {
        this.canForce = canForce;
    }

    public Map<Long, String> getPackmaOuterIds() {
        return packmaOuterIds;
    }

    public void setPackmaOuterIds(Map<Long, String> packmaOuterIds) {
        this.packmaOuterIds = packmaOuterIds;
    }

    public String getPackmaOuterIdStr() {
        return packmaOuterIdStr;
    }

    public void setPackmaOuterIdStr(String packmaOuterIdStr) {
        this.packmaOuterIdStr = packmaOuterIdStr;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public boolean isSkipValidStaffUser() {
        return skipValidStaffUser;
    }

    public void setSkipValidStaffUser(boolean skipValidStaffUser) {
        this.skipValidStaffUser = skipValidStaffUser;
    }

    public boolean isValidClosedStatus() {
        return validClosedStatus;
    }

    public void setValidClosedStatus(boolean validClosedStatus) {
        this.validClosedStatus = validClosedStatus;
    }

    public boolean isPackSplit() {
        return packSplit;
    }

    public void setPackSplit(boolean packSplit) {
        this.packSplit = packSplit;
    }

    public Long getPackSplitPrintSid() {
        return packSplitPrintSid;
    }

    public void setPackSplitPrintSid(Long packSplitPrintSid) {
        this.packSplitPrintSid = packSplitPrintSid;
    }

    public boolean isAutoPack() {
        return autoPack;
    }

    public void setAutoPack(boolean autoPack) {
        this.autoPack = autoPack;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getPackMaOptSource() {
        return packMaOptSource;
    }

    public void setPackMaOptSource(Integer packMaOptSource) {
        this.packMaOptSource = packMaOptSource;
    }


    public static class Builder {

        private Long staffId;

        private Long[] sids;

        private String outSid;

        private List<Order> orders;

        private List<TradePackScanInfo> packScanInfos;

        private List<String> uniqueCodes;

        private boolean isBind;

        private String clientIp;

        private boolean canForce;

        private Map<Long, String> packmaOuterIds;

        /**
         * 是否自动包装验货
         */
        private boolean autoPack;

        /**
         * 来源 1/null-pc 2-pda
         */
        private Integer source;

        private String packmaOuterIdStr;

        /**
         * 包材修改来源
         */
        private Integer packMaOptSource;

        public Builder staffId(Long staffId) {
            this.staffId = staffId;
            return this;
        }

        public Builder sids(Long[] sids) {
            this.sids = sids;
            return this;
        }

        public Builder outSid(String outSid) {
            this.outSid = outSid;
            return this;
        }

        public Builder orders(List<Order> orders) {
            this.orders = orders;
            return this;
        }

        public Builder packScanInfos(List<TradePackScanInfo> packScanInfos) {
            this.packScanInfos = packScanInfos;
            return this;
        }

        public Builder uniqueCodes(List<String> uniqueCodes) {
            this.uniqueCodes = uniqueCodes;
            return this;
        }

        public Builder isBind(boolean isBind) {
            this.isBind = isBind;
            return this;
        }

        public Builder clientIp(String clientIp) {
            this.clientIp = clientIp;
            return this;
        }

        public Builder canForce(boolean canForce) {
            this.canForce = canForce;
            return this;
        }

        public Builder packmaOuterIds(Map<Long, String> packmaOuterIds) {
            this.packmaOuterIds = packmaOuterIds;
            return this;
        }

        public Builder autoPack(boolean autoPack) {
            this.autoPack = autoPack;
            return this;
        }

        public Builder packmaOuterIdStr(String packmaOuterIdStr) {
            this.packmaOuterIdStr = packmaOuterIdStr;
            return this;
        }

        public Builder packMaOptSource(Integer packMaOptSource) {
            this.packMaOptSource = packMaOptSource;
            return this;
        }

        public Long getStaffId() {
            return staffId;
        }

        public void setStaffId(Long staffId) {
            this.staffId = staffId;
        }

        public Long[] getSids() {
            return sids;
        }

        public void setSids(Long[] sids) {
            this.sids = sids;
        }

        public String getOutSid() {
            return outSid;
        }

        public void setOutSid(String outSid) {
            this.outSid = outSid;
        }

        public List<Order> getOrders() {
            return orders;
        }

        public void setOrders(List<Order> orders) {
            this.orders = orders;
        }

        public List<TradePackScanInfo> getPackScanInfos() {
            return packScanInfos;
        }

        public void setPackScanInfos(List<TradePackScanInfo> packScanInfos) {
            this.packScanInfos = packScanInfos;
        }

        public List<String> getUniqueCodes() {
            return uniqueCodes;
        }

        public void setUniqueCodes(List<String> uniqueCodes) {
            this.uniqueCodes = uniqueCodes;
        }

        public boolean isBind() {
            return isBind;
        }

        public void setBind(boolean bind) {
            isBind = bind;
        }

        public String getClientIp() {
            return clientIp;
        }

        public void setClientIp(String clientIp) {
            this.clientIp = clientIp;
        }

        public boolean isCanForce() {
            return canForce;
        }

        public void setCanForce(boolean canForce) {
            this.canForce = canForce;
        }

        public Map<Long, String> getPackmaOuterIds() {
            return packmaOuterIds;
        }

        public void setPackmaOuterIds(Map<Long, String> packmaOuterIds) {
            this.packmaOuterIds = packmaOuterIds;
        }


        public Builder source(Integer source) {
            this.source = source;
            return this;
        }

        public String getPackmaOuterIdStr() {
            return packmaOuterIdStr;
        }

        public void setPackmaOuterIdStr(String packmaOuterIdStr) {
            this.packmaOuterIdStr = packmaOuterIdStr;
        }

        public TradePackParams builder() {
            TradePackParams params = new TradePackParams();
            params.staffId = staffId;
            params.sids = sids;
            params.orders = orders;
            params.outSid = outSid;
            params.uniqueCodes = uniqueCodes;
            params.isBind = isBind;
            params.clientIp = clientIp;
            params.packScanInfos = packScanInfos;
            params.canForce = canForce;
            params.packmaOuterIds = packmaOuterIds;
            params.autoPack = autoPack;
            params.source = source;
            params.packmaOuterIdStr = packmaOuterIdStr;
            params.packMaOptSource = packMaOptSource;
            return params;
        }
    }
}

package com.raycloud.dmj.domain.trades.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.except.enums.ExceptEnum;

import java.util.List;

/**
 * Description:库存判断
 *
 * @version Version 1.0
 * <AUTHOR>
 * @Copyright 2016 Git Inc. All rights reserved.
 * @CreateDate on 2016.11.03
 * @Company 杭州光云科技有限公司
 */
public class StockStatusUtils {

    /**
     * 分析子订单的库存异常状态
     *
     * @param order
     * @return：
     */
    public static boolean isStockExpOrder(Staff staff, Order order) {
        //发货后 不查询订单异常状态
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
            return false;
        }
        if (OrderExceptUtils.isContainsExcept(staff,order, ExceptEnum.INSUFFICIENT)) {
            return true;
        }
        return false;
    }

    /**
     * 判断订单是否为库存异常订单
     *
     * @param trade
     * @return
     */
    public static boolean containStockExpOrder(Staff staff,Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (isStockExpOrder(staff,order)) {
                return true;
            }
        }
        return false;
    }

}

package com.raycloud.dmj.domain.trades;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.trades.utils.OrderExtUtils;
import com.raycloud.dmj.domain.utils.JsonUtils;
import com.raycloud.erp.db.model.*;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Table(name = "order", routerKey = "orderDbNo")
public class OrderExt extends Model {

    private static final long serialVersionUID = 5827572175004802843L;

    private Long id;

    /**
     * 所属主订单的系统编号
     */
    @Column(name = "sid", type = Types.BIGINT)
    private Long sid;

    /**
     * 所属订单的平台编号
     */
    @Column(name = "tid", type = Types.VARCHAR)
    private String tid;

    /**
     * 公司编号
     */
    @Column(name = "company_id", type = Types.BIGINT)
    private Long companyId;

    /**
     * 0 已删除 1正常
     */
    @Column(name = "enable_status", type = Types.TINYINT)
    private Integer enableStatus = 1;

    /**
     * 订单定制信息
     */
    private String customization;

    /**
     * 扩充字段缓存
     * 减少频繁json转map
     */
    private Map<String, Object> customizationMap;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商商家编码
     */
    private String supplierItemOuterId;

    /**
     * JITX常态合作编码
     */
    private String cooperationNoJitx;

    /**
     * 主播ID   即将废弃  新数据会双写到 authorNo
     */
    @Deprecated
    private Long authorId;

    /**
     * 主播ID
     */
    private String authorNo;

    /**
     * 主播名称
     */
    private String authorName;

    /**
     * 商品级别备注
     */
    private String orderRemark;

    /**
     * 承诺揽收时间
     */
    private Date promiseAcceptTime;

    /**
     * 扩充字段
     * 后续需要添加的字段都可以放在这个字段里，json格式
     */
    private String extraFields;
    /**
     * 将map中的数据以key1,value1,key2,value2...的顺序放入到list中，方便sql拼接
     * 不持久化数据库
     */
    private List<Object> extraFieldsList;

    /**
     * 扩充字段缓存
     * 减少频繁json转map 不持久化数据库
     */
    private Map<String, Object> extraFieldsMap;
    /**
     * 正在售后/退款中的数量,不持久化数据库
     */
    @JSONField(serialize = false)
    private int refundingCnt;
    /**
     * 完成售后/退款的数量,不持久化数据库
     */
    @JSONField(serialize = false)
    private int refundedCnt;
    /**
     *  总数量，不持久化数据库
     */
    @JSONField(serialize = false)
    private int skuCnt;
    private boolean isNew;
    /**
     * 赠品对应的主品order_id
     */
    @JSONField(serialize = false)
    private String masterOrderIds;

    @JSONField(serialize = false)
    private String lastRefundId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getCustomization() {
        return customization;
    }

    public void setCustomization(String customization) {
        this.customization = customization;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierItemOuterId() {
        return supplierItemOuterId;
    }

    public void setSupplierItemOuterId(String supplierItemOuterId) {
        this.supplierItemOuterId = supplierItemOuterId;
    }

    public String getCooperationNoJitx() {
        return cooperationNoJitx;
    }

    public void setCooperationNoJitx(String cooperationNoJitx) {
        this.cooperationNoJitx = cooperationNoJitx;
    }

    public Long getAuthorId() {
        return authorId;
    }

    /**
     * 本字段将废弃  请使用新字段  authorNo
     * @param authorId
     */
    @Deprecated
    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    public String getAuthorNo() {
        return authorNo;
    }

    public void setAuthorNo(String authorNo) {
        this.authorNo = authorNo;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    public Date getPromiseAcceptTime() {
        return promiseAcceptTime;
    }

    public void setPromiseAcceptTime(Date promiseAcceptTime) {
        this.promiseAcceptTime = promiseAcceptTime;
    }

    @JSONField(serialize = false)
    public int getRefundingCnt() {
        return (int) getCustomizationOrDefault("refundingCnt", 0);
    }

    @JSONField(serialize = false)
    public int getRefundedCnt() {
        return (int) getCustomizationOrDefault("refundedCnt", 0);
    }

    @JSONField(serialize = false)
    public int getSkuCnt() {
        return (int) getCustomizationOrDefault("skuCnt", 0);
    }

    @JSONField(serialize = false)
    public Integer getFirstRecordOrderNum(){
        return (Integer) getCustomization("firstRecordOrderNum");
    }
    @JSONField(serialize = false)
    public Integer getSyncPlatOrderNum() {
        return (Integer) getCustomization("orderNum");
    }

    public void setFirstRecordOrderNum(Integer firstRecordOrderNum) {
        setCustomization("firstRecordOrderNum", firstRecordOrderNum);
    }


    public void setRefundingCnt(int refundingCnt) {
        this.refundingCnt = refundingCnt;
    }

    public void setRefundedCnt(int refundedCnt) {
        this.refundedCnt = refundedCnt;
    }

    public void setSkuCnt(int skuCnt) {
        this.skuCnt = skuCnt;
    }


    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }


    @JSONField(serialize = false)
    public Set<Long> getMasterOrderIds() {
        Object masterOrderIds = getCustomization("masterOrderIds");
        if (Objects.nonNull(masterOrderIds)) {
            String masterOrderIdsStr = (String) masterOrderIds;
            if (StringUtils.isNotBlank(masterOrderIdsStr)) {
                return Arrays.stream(masterOrderIdsStr.split(",")).map(Long::valueOf).collect(Collectors.toSet());
            }
        }
        return null;
    }

    public String getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(String extraFields) {
        this.extraFields = extraFields;
    }

    public List<Object> getExtraFieldsList() {
        return extraFieldsList;
    }

    public void setExtraFieldsList(List<Object> extraFieldsList) {
        this.extraFieldsList = extraFieldsList;
    }

    public Map<String, Object> getExtraFieldsMap() {
        return extraFieldsMap;
    }

    public void setExtraFieldsMap(Map<String, Object> extraFieldsMap) {
        this.extraFieldsMap = extraFieldsMap;
    }

    /**
     * 获取额外属性
     * @return
     */
    public Object get(String key){
        if (extraFieldsMap == null){
            extraFieldsMap = OrderExtUtils.parseExtraFields(this);
        }
        return extraFieldsMap.get(key);
    }

    public void set(String key, Object value) {
        if (extraFieldsMap == null){
            extraFieldsMap = OrderExtUtils.parseExtraFields(this);
        }
        extraFieldsMap.put(key, value);
        this.extraFields = JSONObject.toJSONString(extraFieldsMap);
    }

    public void setCustomization(String key, Object value) {
        if (customizationMap == null){
            customizationMap = OrderExtUtils.parseCustomization(this);
        }
        customizationMap.put(key, value);
        this.customization = JSONObject.toJSONString(customizationMap);
    }

    public Object getCustomization(String key){
        if (customizationMap == null){
            customizationMap = OrderExtUtils.parseCustomization(this);
        }
        return customizationMap.get(key);
    }

    public String getCustomizationStr(String key) {
        Object result = getCustomization(key);
        if (result == null) {
            return "";
        }
        return result.toString();
    }

    public Object getCustomizationOrDefault(String key, Object defaultValue){
        if (customizationMap == null){
            customizationMap = OrderExtUtils.parseCustomization(this);
        }
        return customizationMap.getOrDefault(key, defaultValue);
    }
    @JSONField(serialize = false)
    public String getLastRefundId() {
        return (String) getCustomization("lastRefundId");
    }

    public void setLastRefundId(String refundId) {
        setCustomization("lastRefundId", refundId);
    }

    @Override
    public String toString() {
        return "OrderExt{" +
                "customization='" + customization + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", supplierItemOuterId='" + supplierItemOuterId + '\'' +
                ", cooperationNoJitx='" + cooperationNoJitx + '\'' +
                ", authorId=" + authorId +
                ", authorNo=" + authorNo +
                ", authorName='" + authorName + '\'' +
                ", orderRemark='" + orderRemark + '\'' +
                ", promiseAcceptTime=" + promiseAcceptTime + '\'' +
                ", extraFields=" + extraFields +
                '}';
    }
}

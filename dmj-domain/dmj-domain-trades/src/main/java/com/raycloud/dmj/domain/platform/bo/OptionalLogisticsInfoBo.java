package com.raycloud.dmj.domain.platform.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2024/6/13 10:07
 */
@Data
public class OptionalLogisticsInfoBo implements Serializable {
    private static final long serialVersionUID = 1777276820203088772L;
    private List<LogisticsDetail> logisticDetails;

    @Data
    public static class LogisticsDetail implements Serializable {
        private static final long serialVersionUID = 3784498255817593622L;
        private String logisticsName;
        private String logisticsCode;
    }
}

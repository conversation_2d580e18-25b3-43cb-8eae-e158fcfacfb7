package com.raycloud.dmj.domain.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 交易金额处理参数类
 * <AUTHOR>
 * @date 2024/8/30 16:16
 */
@Data
public class TradeAmountExpandParam implements Serializable {
    private static final long serialVersionUID = -3475934301496824750L;

    /**
     * 平台商品金额→订单商品金额
     */
    private BigDecimal platformGoodsAmount;

    /**
     * 平台改价金额
     */
    private BigDecimal platformAlterAmount;

    /**
     * 订单应收金额
     */
    private BigDecimal receiveAmount;

    /**
     * 平台商品优惠
     */

    private BigDecimal platformGoodsDiscountFee;

    /**
     * 平台优惠
     */
    private BigDecimal platformDiscountFee;

    /**
     * 商家优惠→商家商品优惠
     */
    private BigDecimal sellerGoodsDiscountFee;

    /**
     * 商家优惠→商家商品优惠
     */
    private BigDecimal sellerDiscountFee;

    /**
     * 平台实付金额→订单实付金额
     */
    private BigDecimal payment;

    /**
     * 平台税费
     */
    private BigDecimal taxAmount;
    /**
     * 三方优惠
     */
    private BigDecimal thirdDiscountFee;
    /**
     * 单据明细中的优惠合计
     */
    private BigDecimal orderDiscountFee;

    public void setPlatformGoodsDiscountFee(BigDecimal platformGoodsDiscountFee) {
        this.platformGoodsDiscountFee = platformGoodsDiscountFee;
        sumPlatformDiscountFee();
    }

    /**
     * 平台其他优惠
     */
    private BigDecimal platformOtherDiscountFee;

    public void setPlatformOtherDiscountFee(BigDecimal platformOtherDiscountFee) {
        this.platformOtherDiscountFee = platformOtherDiscountFee;
        sumPlatformDiscountFee();
    }

    public void sumPlatformDiscountFee() {
        this.platformDiscountFee = add(this.platformGoodsDiscountFee, this.platformOtherDiscountFee);
    }

    public void sumSellerDiscountFee() {
        this.sellerDiscountFee = add(this.sellerGoodsDiscountFee, this.sellerOtherDiscountFee);
    }

    public void setSellerGoodsDiscountFee(BigDecimal sellerGoodsDiscountFee) {
        this.sellerGoodsDiscountFee = sellerGoodsDiscountFee;
        sumSellerDiscountFee();
    }

    public void setSellerOtherDiscountFee(BigDecimal sellerOtherDiscountFee) {
        this.sellerOtherDiscountFee = sellerOtherDiscountFee;
        sumSellerDiscountFee();
    }

    /**
     * 商家其他优惠
     */
    private BigDecimal sellerOtherDiscountFee;
    /**
     * 平台运费
     */
    private BigDecimal platformPostFee;
    /**
     * 给平台的开票金额
     */
    private BigDecimal platformInvoiceAmount;
    /**
     * 买家开票金额
     */
    private BigDecimal consumerInvoiceAmount;


    /**
     * 计算应付 实收金额
     */
    public void computeChargeAmount() {
        computeChargeAmount(false);
    }

    /**
     *计算 应收 和 买家实付
     * @param isSubAlter 是否减改价金额还是加改进金额 true减差价
     */
    public void computeChargeAmount(boolean isSubAlter) {
        //订单商品金额+订单改价金额+运费+税费-商家承担优惠-商家其他优惠=订单应收金额
        BigDecimal fee = add(getPlatformGoodsAmount(), getTaxAmount(), getPlatformPostFee());
        if (isSubAlter) {
            fee = sub(fee, getPlatformAlterAmount());
        } else {
            fee = add(fee, getPlatformAlterAmount());
        }
        fee = sub(fee, getSellerGoodsDiscountFee(), getSellerOtherDiscountFee());
        this.receiveAmount = fee;
        //订单商品金额+订单改价金额+运费+税费-商家商品优惠-商家其他优惠-平台商品优惠-平台其他优惠-三方其他优惠=订单实付金额
        this.payment = sub(fee, getPlatformGoodsDiscountFee(), getPlatformOtherDiscountFee(), getThirdDiscountFee());
    }
    @JSONField(serialize = false)
    public String getPrint() {
        return "订单商品金额" + "=" + this.getPlatformGoodsAmount() +
                System.lineSeparator() +
                "订单改价金额" + "=" + this.getPlatformAlterAmount() +
                "\n订单应收金额" + "=" + this.getReceiveAmount() +
                "\n订单实付金额" + "=" + this.getPayment() +
                "\n平台运费" + "=" + this.getPlatformPostFee() +
                "\n平台税费" + "=" + this.getTaxAmount() +
                "\n平台商品优惠" + "=" + this.getPlatformGoodsDiscountFee() +
                "\n商家商品优惠" + "=" + this.getSellerGoodsDiscountFee() +
                "\n平台其他优惠" + "=" + this.getPlatformOtherDiscountFee() +
                "\n商家其他优惠" + "=" + this.getSellerOtherDiscountFee() +
                "\n三方其他优惠" + "=" + this.getThirdDiscountFee() +
                "\n平台优惠" + "=" + this.getPlatformDiscountFee() +
                "\n商家优惠" + "=" + this.getSellerDiscountFee() +
                "\n用户开票金额" + "=" + this.getConsumerInvoiceAmount() +
                "\n平台开票金额" + "=" + this.getPlatformInvoiceAmount();

    }

    public static BigDecimal add(BigDecimal... values) {
        if (ArrayUtils.isEmpty(values)) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = Objects.isNull(values[0]) ? BigDecimal.ZERO : values[0];
        for(int i = 1; i < values.length; ++i) {
            BigDecimal value = values[i];
            if (null != value) {
                result = result.add(value);
            }
        }

        return result;

    }

    public static BigDecimal sub(BigDecimal... values) {
        if (ArrayUtils.isEmpty(values)) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = Objects.isNull(values[0]) ? BigDecimal.ZERO : values[0];
        for(int i = 1; i < values.length; ++i) {
            BigDecimal value = values[i];
            if (null != value) {
                result = result.subtract(value);
            }
        }
        return result;
    }

}

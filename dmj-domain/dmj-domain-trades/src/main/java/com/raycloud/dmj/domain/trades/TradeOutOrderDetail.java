package com.raycloud.dmj.domain.trades;

import java.io.Serializable;
import java.util.Date;

/**
 * 出库单导入对象
 * Created by ya<PERSON><PERSON><PERSON> on 16/9/12.
 */
public class TradeOutOrderDetail implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 4197859160954659842L;

    /**
     * 采购订单编号
     */
    private Long id;

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 采购订单编号
     */
    private Long oid;

    /**
     * 商品系统编码
     */
    private Long sysItemId;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 规格名称
     */
    private String propertiesName;

    /**
     * 规格的系统编码，如果skuSysId不为空，那么表示此商品为规格
     */
    private Long sysSkuId;

    /**
     * 商品的numIid
     */
    private Long numIid;

    /**
     * 淘宝的skuId
     */
    private Long skuId;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品数量数量
     */
    private Long importNum;

    /**
     * 已到数量
     */
    private Long arrivedQuantity;

    /**
     * 单价
     */
    private Double price;

    /**
     * 采购单价（成本价）
     */
    private Double priceImport;

    /**
     * 总金额
     */
    private Double totalAmount;

    /**
     * 折扣率，例如100表示 1%
     */
    private Long discountRate;

    /**
     * 折后单价
     */
    private Long discountPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 添加者编号
     */
    private Long createrId;

    /**
     * 添加者姓名
     */
    private String createrName;

    /**
     * 添加日期
     */
    private Date created;

    /**
     * 修改日期
     */
    private Date modified;

    /**
     * 状态，请参考CgOrder中的订单状态
     */
    private String status;

    private Integer enableStatus;

    /**
     * 规格备注
     */
    private String skuRemark;

    /**
     * 是否纯商品
     */
    private Integer isSku;

    /**
     * 图片路径
     */
    private String picPath;

    /**
     * 规格属性别名
     */
    private String propertiesAlias;

    /**
     * 参考售价
     */
    private Double priceOutput;

    /**
     * 商品类型
     * "0"普通 "1"套件 纯商品  "2"
     */
    private String type;

    /**
     * 
     */
    private Double weight;

    /**
     * 库存可用数
     */
    private Long availableInStock;

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getPriceOutput() {
        return priceOutput;
    }

    public void setPriceOutput(Double priceOutput) {
        this.priceOutput = priceOutput;
    }

    public String getPropertiesAlias() {
        return propertiesAlias;
    }

    public void setPropertiesAlias(String propertiesAlias) {
        this.propertiesAlias = propertiesAlias;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public Integer getIsSku() {
        return isSku;
    }

    public void setIsSku(Integer isSku) {
        this.isSku = isSku;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public Long getNumIid() {
        return numIid;
    }

    public void setNumIid(Long numIid) {
        this.numIid = numIid;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getImportNum() {
        return importNum;
    }

    public void setImportNum(Long importNum) {
        this.importNum = importNum;
    }

    public Long getArrivedQuantity() {
        return arrivedQuantity;
    }

    public void setArrivedQuantity(Long arrivedQuantity) {
        this.arrivedQuantity = arrivedQuantity;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getPriceImport() {
        return priceImport;
    }

    public void setPriceImport(Double priceImport) {
        this.priceImport = priceImport;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Long discountRate) {
        this.discountRate = discountRate;
    }

    public Long getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Long discountPrice) {
        this.discountPrice = discountPrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreaterId() {
        return createrId;
    }

    public void setCreaterId(Long createrId) {
        this.createrId = createrId;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getSkuRemark() {
        return skuRemark;
    }

    public void setSkuRemark(String skuRemark) {
        this.skuRemark = skuRemark;
    }

    public Long getAvailableInStock() {
        return availableInStock;
    }

    public void setAvailableInStock(Long availableInStock) {
        this.availableInStock = availableInStock;
    }
}

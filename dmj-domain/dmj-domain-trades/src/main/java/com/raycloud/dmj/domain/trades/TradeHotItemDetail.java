package com.raycloud.dmj.domain.trades;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeHotItemDetail implements Serializable {
    private static final long serialVersionUID = -1316382933256274101L;


    /**
     * 系统编码
     */
    private String sysOuterId;


    /**
     * 订单数量
     */
    private Integer tradeNum;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 系统商品的标题
     */
    private String sysTitle;

    /**
     * 系统SKU的规格属性名称
     */
    private String sysSkuPropertiesName;
    /**
     * 系统SKU规格属性别名
     */
    private String sysSkuPropertiesAlias;



}

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.raycloud.dmj</groupId>
		<artifactId>dmj-domain</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>dmj-domain-trades</artifactId>
	<name>DMJ Domain Trades</name>
	<description>有关订单的领域模型</description>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-domain-basis</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-dms-domain</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-domain-snapshot</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-domain-trade-except</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-domain-items-stock-product</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-domain-stock-oms</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
    </dependencies>
</project>

package com.raycloud.dmj.controller.pt;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.business.pt.FeatureSetBusiness;
import com.raycloud.dmj.business.pt.common.PtLockBusiness;
import com.raycloud.dmj.business.trade.GxTradeModifyTemplateLogService;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.pt.FieldSettings;
import com.raycloud.dmj.domain.pt.enums.EnumWlbType;
import com.raycloud.dmj.domain.pt.enums.ObtainWayEnum;
import com.raycloud.dmj.domain.pt.enums.WlbTemplateTypeEnum;
import com.raycloud.dmj.domain.pt.model.UserWlbExpressTemplateName;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.model.waybill.get.WlbRequestGet;
import com.raycloud.dmj.domain.pt.wlb.FieldHeadText;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplates;
import com.raycloud.dmj.domain.pt.wlb.WlbUserAddParams;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.services.ILockCallback;
import com.raycloud.dmj.services.UserAkcExpressTemplateQueryService;
import com.raycloud.dmj.services.helper.UniformCloudTemplateUtils;
import com.raycloud.dmj.services.helper.wlb.AkcCacheUtil;
import com.raycloud.dmj.services.helper.wlb.WayBillCompanyHelper;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.print.PrintHelperService;
import com.raycloud.dmj.services.print.utils.TemplateUtils;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.pt.model.SingleOrBatchGetParam;
import com.raycloud.dmj.services.pt.model.WlbStatusResult;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.pt.utils.SendWayBillEcUtils;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.model.TradeTraceParam;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.dmj.web.utils.OpLogHelper;
import com.raycloud.dmj.web.utils.SaveTradeTraceHelper;
import com.raycloud.ec.api.IEventCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.waybill.common.enums.AllocateGetPathEnum.SINGLE_OR_BATCH_GET;

@Slf4j
@Controller
public class UserAkcExpressTemplateController extends Sessionable {

    @Resource
    protected IOpLogService opLogService;

    @Resource
    private IUserAkcExpressTemplateService userAkcExpressTemplateService;

    @Resource
    private IExpressTemplateService expressTemplateService;

    @Resource
    private UserAkcExpressTemplateQueryService userAkcExpressTemplateQueryService;

    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    private ITradeTraceService tradeTraceService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private IWaybillGetTradeGroupService waybillGetTradeGroupService;

    @Resource(name = "userWlbExpressCompanyAdapterService")
    private IUserWlbExpressCompanyService userWlbExpressCompanyAdapterService;

    @Resource
    private FeatureSetBusiness featureSetBusiness;

    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;

    @Resource
    private GxTradeModifyTemplateLogService gxTradeModifyTemplateLogService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private PtLockBusiness ptLockBusiness;
    @Resource
    private IProgressService progressService;

    @Resource
    private IWaybillUsingRuleService waybillUsingRuleService;

    @Resource
    private IExpressCompanyService expressCompanyService;

    @RequestMapping(value = "/pt/akc/user/sync/cloud", method = RequestMethod.POST)
    @ResponseBody
    public Object userSyncCloud(String api_name) throws Exception {
        Integer wlbType = EnumWlbType.AKC.getValue();
        Staff staff = getStaff();

        List<User> needSyncUsers = userAkcExpressTemplateService.getNeedSyncUsers(staff);
        if (CollectionUtils.isEmpty(needSyncUsers)) {
            return Status.buildFailStatus();
        }
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.AKC_SYNC), "当前正在进行同步操作,请稍后再试！");
        new Thread(() -> userAkcExpressTemplateService.userSyncShops(staff, needSyncUsers))
                .start();

        // 记录日志
        OpLogHelper.recodeOpLog(opLogService, request, staff, "wlbDefault", wlbType.toString(),
                "同步爱库存云打印模板", JSON.toJSONString(wlbType));
        return Status.buildSuccessStatus();
    }


    /**
     * 查询爱库存模板列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/pt/akc/user/list", method = RequestMethod.GET)
    @ResponseBody
    public Object userList(Integer pageNo, Integer pageSize, Long warehouseId, String api_name) throws SessionException {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page page = new Page();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        Staff staff = getStaff();
        UserWlbExpressTemplates templates = userAkcExpressTemplateService.userList(staff, page, WlbTemplateTypeEnum.AKC.getValue());
        expressTemplateService.getExpressWarehouseBranchBind(staff, warehouseId, templates);
        return templates;
    }

    /**
     * 根据id获取爱库存模板
     *
     * @param id 爱库存模板的编号
     * @return
     */
    @RequestMapping(value = "/pt/akc/user/query", method = RequestMethod.GET)
    @ResponseBody
    public Object userQuery(Long id, Boolean templateList, String api_name) throws SessionException {
        Assert.isTrue(null != id && id > 0, "请传入id");
        if (templateList == null) {
            templateList = false;
        }
        Staff staff = getStaff();
        UserWlbExpressTemplate userWlbExpressTemplate = userAkcExpressTemplateService.userQuery(staff, id, templateList);
        if (userWlbExpressTemplate == null) {
            throw new IllegalArgumentException("找不到该模板" + id);
        }
        if (StringUtils.isEmpty(userWlbExpressTemplate.getCloudCustomAreaConfig())) {
            UniformCloudTemplateUtils.calculateCustomArea(staff, userWlbExpressTemplate, cache);
        }
        List<FieldHeadText> fieldHeadTexts = userWlbExpressTemplate.getFieldHeadTexts();
        List<FieldSettings> fieldSettingses = userWlbExpressTemplate.getFieldSettings();
        if (fieldHeadTexts != null && fieldHeadTexts.size() > 0 && fieldSettingses != null && fieldSettingses.size() > 0) {
            Map<Integer, List<FieldSettings>> fieldSettingMap = new HashMap<Integer, List<FieldSettings>>();
            for (FieldSettings fieldSettings : fieldSettingses) {
                List<FieldSettings> fieldSettingsList = fieldSettingMap.get(fieldSettings.getTitleId());
                if (fieldSettingsList == null) {
                    fieldSettingsList = new ArrayList<FieldSettings>();
                }
                fieldSettingsList.add(fieldSettings);

                fieldSettingMap.put(fieldSettings.getTitleId(), fieldSettingsList);
            }
            for (FieldHeadText fieldHeadText : fieldHeadTexts) {
                Integer titleId = fieldHeadText.getId();
                fieldHeadText.setFieldSettingsList(fieldSettingMap.get(titleId));
            }
        }

        if (WlbTemplateTypeEnum.DIVIDE_AKC.getValue().equals(userWlbExpressTemplate.getWlbTemplateType())) {
            User shareUser = userService.queryById(userWlbExpressTemplate.getTaobaoId());
            Staff shareStaff = staffService.queryDefaultStaffByCompanyId(shareUser.getCompanyId());
            shareStaff.setUsers(Collections.singletonList(shareUser));
            userWlbExpressTemplate.setAkcCustomListVOS(userAkcExpressTemplateService.userCustomTemplateQuery(shareStaff, userWlbExpressTemplate.getExpressId()));
        } else {
            userWlbExpressTemplate.setAkcCustomListVOS(userAkcExpressTemplateService.userCustomTemplateQuery(staff, userWlbExpressTemplate.getExpressId()));
        }
        // 爱库存默认自定义区域
        userWlbExpressTemplate.setCloudCustomAreaConfig(String.format("[{\"height\":50,\"width\":%s}]", userWlbExpressTemplate.getWidthCustom()));

        return userWlbExpressTemplate;
    }

    /**
     * 保存当前设置
     *
     * @param userWlbExpressTemplate
     * @return
     */
    @RequestMapping(value = "/pt/akc/update", method = RequestMethod.POST)
    @ResponseBody
    public Object update(UserWlbExpressTemplate userWlbExpressTemplate, String api_name) throws SessionException {
        Assert.notNull(userWlbExpressTemplate, "请传入要保存的信息");
        Assert.isTrue(null != userWlbExpressTemplate.getId() && userWlbExpressTemplate.getId() > 0, "请传入模板id");
        Staff staff = getStaff();

        UserWlbExpressTemplate originTemplate = userAkcExpressTemplateService.userQuery(staff, userWlbExpressTemplate.getId(), false);
        Assert.notNull(originTemplate, "找不到对应模板");

        userWlbExpressTemplate.setUpdateStatus(1);
        userWlbExpressTemplate.setExpressId(originTemplate.getExpressId());
        userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);

        // 查询需要修改的模板列表
        if (Objects.equals(originTemplate.getObtainWay(), ObtainWayEnum.SYNC.getValue())) {
            List<UserWlbExpressTemplate> updateTemplateList = userAkcExpressTemplateQueryService.listSyncTemplateByExpressId(staff, null, originTemplate.getExpressId(), originTemplate.getCloudTemplatePaperType(), originTemplate.getWlbTemplateType());
            if (CollectionUtils.isNotEmpty(updateTemplateList)) {
                updateTemplateList.stream()
                        .map(UserWlbExpressTemplate::getId)
                        .filter(id -> !Objects.equals(id, originTemplate.getId()))
                        .forEach(templateId -> {
                            userWlbExpressTemplate.setId(templateId);
                            userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);
                        });
            }
        }

        StringBuilder sb = new StringBuilder();
        sb.append("爱库存模板【");
        sb.append(originTemplate.getName());
        sb.append("】更新设计");
        OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcDefault", originTemplate.getId().toString(),
                sb.toString(), JSON.toJSONString(userWlbExpressTemplate));

        // 记录保存模板变化的数据
        String modifyLog = TemplateUtils.buildModifyLog(userWlbExpressTemplate, originTemplate);
        if (StringUtils.isNotBlank(modifyLog)) {
            OpLogHelper.recodeOpLog(opLogService, request, staff, "updateTemplate", originTemplate.getId().toString(), "修改模板信息【" + modifyLog + "】", JSON.toJSONString(userWlbExpressTemplate));
        }
        return Status.buildSuccessStatus();
    }

    /**
     * 修改用户模板名称
     *
     * @param id   用户模板id
     * @param name 新的模板名称
     * @return
     */
    @RequestMapping(value = "/pt/akc/user/name/edit", method = RequestMethod.POST)
    @ResponseBody
    public Object userNameEdit(Long id, String name, String api_name) throws SessionException {
        Assert.isTrue(null != id && id > 0, "请传入id");
        Assert.hasText(name, "请传入name");

        Staff staff = getStaff();
        UserWlbExpressTemplate originTemplate = userAkcExpressTemplateService.userQuery(staff, id, false);
        Assert.notNull(originTemplate, "找不到对应模板");

        List<UserWlbExpressTemplateName> userWlbExpressTemplateNameList = userAkcExpressTemplateService.getAkcTemplateName(staff, originTemplate.getWlbTemplateType());
        for (UserWlbExpressTemplateName userWlbExpressTemplateName : userWlbExpressTemplateNameList) {
            if (name.equals(userWlbExpressTemplateName.getTemplateName()) && !id.equals(userWlbExpressTemplateName.getId())) {
                throw new IllegalArgumentException("模板名称重复，请重新输入!");
            }
        }

        UserWlbExpressTemplate userWlbExpressTemplate = new UserWlbExpressTemplate();
        userWlbExpressTemplate.setId(id);
        userWlbExpressTemplate.setName(name);
        userWlbExpressTemplate.setExpressId(originTemplate.getExpressId());
        userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);

        AkcCacheUtil.cleanForEachWlbTemplateList(staff, userAkcExpressTemplateService, cache);

        StringBuilder sb = new StringBuilder();
        sb.append("将爱库存模板名【");
        sb.append(originTemplate.getName());
        sb.append("】修改为【");
        sb.append(name);
        sb.append("】");
        OpLogHelper.recodeOpLog(opLogService, request, staff, "userNameEdit", id.toString(),
                sb.toString(), JSON.toJSONString(id));
        return Status.buildSuccessStatus();
    }

    /**
     * 锁定打印机
     * 存储格式:
     * {
     * "主帐号淘宝Id": "锁定打印机名称",
     * "子账号淘宝Id": "锁定打印机名称"
     * }
     *
     * @param templateId
     * @param printerName
     * @param api_name
     * @return
     * @throws SessionException
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/pt/akc/user/lockprinter", method = RequestMethod.POST)
    @ResponseBody
    public Object lockPrinter(Long templateId, String printerName, String api_name) throws SessionException {
        Assert.isTrue(null != templateId && templateId > 0, "请传入templateId");

        Staff staff = getStaff();
        UserWlbExpressTemplate wlbExpressTemplate = userAkcExpressTemplateService.userQueryWithCache(staff, templateId, false);
        Assert.notNull(wlbExpressTemplate, "找不到该id的模板信息");

        String printNameJSON = wlbExpressTemplate.getPrinterName();
        Map<String, String> map = null;
        if (!StringUtils.isEmpty(printNameJSON)) {
            try {
                map = JSON.parseObject(printNameJSON, Map.class);
            } catch (Exception e) {
                log.error("锁定打印机转化json出错:{}", printNameJSON, e);
            }
        }
        if (map == null) {
            map = new HashMap();
        }
        map.put(String.valueOf(staff.getId()), printerName);

        UserWlbExpressTemplate userWlbExpressTemplate = new UserWlbExpressTemplate();
        userWlbExpressTemplate.setId(templateId);
        userWlbExpressTemplate.setPrinterName(JSON.toJSONString(map));
        userWlbExpressTemplate.setExpressId(wlbExpressTemplate.getExpressId());

        userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);
        userAkcExpressTemplateService.cleanCache(staff, templateId);

        StringBuilder sb = new StringBuilder();
        sb.append("爱库存模板【");
        sb.append(wlbExpressTemplate.getName());
        if (StringUtils.isEmpty(printerName)) {
            sb.append("】解锁打印机");
        } else {
            sb.append("】锁定打印机【");
            sb.append(printerName);
            sb.append("】");
        }
        OpLogHelper.recodeOpLog(opLogService, request, staff, "userLockPrint", templateId.toString(),
                sb.toString(), JSON.toJSONString(printerName));
        return Status.buildSuccessStatus();
    }

    /**
     * 得到该用户的电子面单模板名称
     *
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/pt/akc/user/templateName", method = RequestMethod.GET)
    @ResponseBody
    public Object getWlbTemplateName(String api_name) throws SessionException {
        return userAkcExpressTemplateService.getAkcTemplateName(getStaff(), WlbTemplateTypeEnum.AKC.getValue());
    }

    /**
     * 添加爱库存模板
     *
     * @return
     */
    @RequestMapping(value = "/pt/akc/user/add", method = RequestMethod.POST)
    @ResponseBody
    public Object userAdd(WlbUserAddParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        params.setWlbType(EnumWlbType.AKC.getValue());
        params.setWlbTemplateType(WlbTemplateTypeEnum.AKC.getValue());
        Long templateId = userAkcExpressTemplateService.addUserWlbExpressTemplate(staff, params);
        OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcDefault", params.getWlbType().toString(),
                "新增爱库存模板", JSON.toJSONString(params.getWlbType()));
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("templateId", templateId);
        return result;
    }


    /**
     * 删除添加的爱库存模板
     *
     * @return
     */
    @RequestMapping(value = "/pt/akc/user/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteAddTemplate(Long templateId, String api_name) throws Exception {
        Integer wlbType = EnumWlbType.AKC.getValue();
        Staff staff = getStaff();
        userAkcExpressTemplateService.deleteAddTemplate(staff, templateId);
        OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcDefault", wlbType.toString(),
                "删除爱库存模板", JSON.toJSONString(wlbType));
        return Status.buildSuccessStatus();
    }

    /**
     * 获取爱库存运单号
     *
     * @param sids       必须 系统订单号
     * @param templateId 必须 快递模板编号
     * @param checkPdd   是否需要校验上传的爱库存 订单
     */
    @RequestMapping(value = "/trade/logistics/akc/waybill/get", method = RequestMethod.POST)
    @ResponseBody
    public Object wayBillGet(final Long templateId, final String sids, final String queryId, final String checkPdd,
                             String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请传入sids");
        Assert.isTrue(null != templateId && 0 != templateId, "请传入templateId");

        Long[] sidArr = null;
        try {
            sidArr = ArrayUtils.toLongArray(sids);
        } catch (Exception e) {
            throw new IllegalArgumentException("sids里面的数字有非法字符，需要为整数!");
        }

        final Staff staff = getStaff();
        final Long[] fSidArr = sidArr;

        // 模板数据
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateId, false);
        Assert.notNull(userWlbExpressTemplate, "模板数据不存在");

        ILockCallback<Object> lockCallback = () -> {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            List<Trade> tradeList = PrintHelperService.checkTradeTemplate(templateId, queryId, tradeConfig, tradeSearchService.queryBySids(staff, false, fSidArr));
            Map<Long, String> sidAndErrorStrMap = new HashMap<>();
            tradeList = waybillUsingRuleService.qiMenGetWaybillRuleCheck(staff, null, tradeList, userWlbExpressTemplateService, expressCompanyService, sidAndErrorStrMap, tradeTraceService, templateId);

            WlbStatus noTradeWlbStatus = WlbStatus.buildSuccessStatus();
            if (CollectionUtils.isEmpty(tradeList)) {
                if (MapUtils.isNotEmpty(sidAndErrorStrMap)) {
                    noTradeWlbStatus = WlbStatus.buildFailStatus();
                    List<WlbResult> wlbResultList = new ArrayList<>();
                    for (Map.Entry<Long, String> longStringEntry : sidAndErrorStrMap.entrySet()) {
                        WlbResult wlbResult = new WlbResult();
                        wlbResult.setSid(String.valueOf(longStringEntry.getKey()));
                        wlbResult.setErrorMsg(longStringEntry.getValue());
                        wlbResultList.add(wlbResult);
                    }
                    noTradeWlbStatus.setErrorResult(wlbResultList);
                }
                return noTradeWlbStatus;
            }
            final Long[] tradeSid = tradeList.stream().map(Trade::getSid).toArray(Long[]::new);
            Long warehouseId = TradeUtils.getWareHouseIdByTrades(tradeList);
            WlbRequestGet wlbRequestGet = new WlbRequestGet(warehouseId, templateId, tradeSid);
            wlbRequestGet.setWlbType(userWlbExpressTemplate.getWlbType());
            wlbRequestGet.setClientIp(IpUtils.getClientIP(request));
            List<Long> uploadConsignAkc = TradeStatusUtils.checkUpTemplateOutsid(tradeList, "修改模板", templateId, "1".equals(checkPdd));

            // 获取电子面单
            WlbStatus waybillCode = waybillGetTradeGroupService.getWlbWaybillCodeGroupByUserId(staff, userWlbExpressTemplate, wlbRequestGet, userWlbExpressCompanyAdapterService);
            SaveTradeTraceHelper.saveTradeTrace(TradeTraceParam.builder()
                    .staff(staff)
                    .waybillCode(waybillCode)
                    .tradeList(tradeList)
                    .templateId(templateId)
                    .templateName(userWlbExpressTemplate.getName())
                    .tradeTraceService(tradeTraceService)
                    .uploadConsignPdd(uploadConsignAkc)
                    .featureSetBusiness(featureSetBusiness)
                    .userLogisticsCompanyService(userLogisticsCompanyService)
                    .build());
            // 记录日志
            String content = "【获取单号】系统订单号：" + sids + "，使用模板【" + userWlbExpressTemplate.getName()
                    + "】一共获取了" + tradeSid.length + "个单号。操作人：" + staff.getAccountName();
            OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcWaybillBatchGet", sids, content, null);
            //记录换快递模板 用于供销订单资金流水重算
            gxTradeModifyTemplateLogService.saveTemplateModifyLog(staff, waybillCode, tradeList, templateId);
            // 爱库存运单生命周期
            SendWayBillEcUtils.buildAndSend(staff, this,
                    tradeList.stream()
                            .filter(trade -> Objects.nonNull(trade.getWaybillUsingRule()))
                            .collect(Collectors.toMap(Trade::getSid, Trade::getWaybillUsingRule, (t1, t2) -> t1)),
                    new SingleOrBatchGetParam(wlbRequestGet),
                    new WlbStatusResult((WlbStatus) waybillCode),
                    SINGLE_OR_BATCH_GET,
                    eventCenter);
            return WayBillCompanyHelper.initSysWaybillWithResult(staff, waybillCode, tradeList, templateId, tradeSearchService, tradeSid, sidAndErrorStrMap);
        };

        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArr), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "获取单号时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }

}

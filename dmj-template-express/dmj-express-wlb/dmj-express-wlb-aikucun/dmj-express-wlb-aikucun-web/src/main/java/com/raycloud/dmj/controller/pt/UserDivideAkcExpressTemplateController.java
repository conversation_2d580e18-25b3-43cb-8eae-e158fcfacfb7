package com.raycloud.dmj.controller.pt;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.pt.FieldSettings;
import com.raycloud.dmj.domain.pt.enums.EnumWlbType;
import com.raycloud.dmj.domain.pt.enums.ObtainWayEnum;
import com.raycloud.dmj.domain.pt.enums.WlbTemplateTypeEnum;
import com.raycloud.dmj.domain.pt.model.UserWlbExpressTemplateName;
import com.raycloud.dmj.domain.pt.wlb.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.UserAkcExpressTemplateQueryService;
import com.raycloud.dmj.services.helper.UniformCloudTemplateUtils;
import com.raycloud.dmj.services.helper.wlb.AkcCacheUtil;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.print.utils.TemplateUtils;
import com.raycloud.dmj.services.pt.IExpressTemplateService;
import com.raycloud.dmj.services.pt.IUserAkcExpressTemplateService;
import com.raycloud.dmj.services.pt.IUserDivideAkcExpressTemplateService;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.utils.OpLogHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Controller
public class UserDivideAkcExpressTemplateController extends Sessionable {

    @Resource
    protected IOpLogService opLogService;
    @Resource
    private IUserAkcExpressTemplateService userAkcExpressTemplateService;
    @Resource
    private IUserDivideAkcExpressTemplateService userDivideAkcExpressTemplateService;
    @Resource
    private IExpressTemplateService expressTemplateService;
    @Resource
    private UserAkcExpressTemplateQueryService userAkcExpressTemplateQueryService;
    @Resource
    private IProgressService progressService;


    /**
     * 同步已开通的爱库存打印模板
     *
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/sync", method = RequestMethod.POST)
    @ResponseBody
    public Object userSyncCloud(String cpCode, String api_name) throws Exception {
        Staff staff = getStaff();
        DivideSyncBO divideSyncBO = userDivideAkcExpressTemplateService.getDivideSyncBO(staff, cpCode);
        if (CollectionUtils.isEmpty(divideSyncBO.getUserIds())) {
            return Status.buildFailStatus();
        }
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.AKC_DIVIDE_SYNC), "当前正在进行同步操作,请稍后再试！");
        // 同步打印模板
        new Thread(() -> userDivideAkcExpressTemplateService.syncDivideTemplate(staff, cpCode, divideSyncBO))
                .start();
        // 记录日志
        OpLogHelper.recodeOpLog(opLogService, request, staff, "wlbDefault", String.valueOf(EnumWlbType.DIVIDE_AKC.getValue()),
                "同步爱库存分单云打印模板", String.valueOf(EnumWlbType.DIVIDE_AKC.getValue()));

        return Status.buildSuccessStatus();
    }


    /**
     * 查询爱库存模板列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/list", method = RequestMethod.GET)
    @ResponseBody
    public Object userList(Integer pageNo, Integer pageSize, Long warehouseId, String api_name) throws SessionException {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page page = new Page();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        Staff staff = getStaff();
        UserWlbExpressTemplates templates = userAkcExpressTemplateService.userList(staff, page, WlbTemplateTypeEnum.DIVIDE_AKC.getValue());
        expressTemplateService.getExpressWarehouseBranchBind(staff, warehouseId, templates);
        return templates;
    }

    /**
     * 根据id获取爱库存模板
     *
     * @param id 爱库存模板的编号
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/query", method = RequestMethod.GET)
    @ResponseBody
    public Object userQuery(Long id, Boolean templateList, String api_name) throws SessionException {
        Assert.isTrue(null != id && id > 0, "请传入id");
        if (templateList == null) {
            templateList = false;
        }
        Staff staff = getStaff();
        UserWlbExpressTemplate userWlbExpressTemplate = userAkcExpressTemplateService.userQuery(staff, id, templateList);
        if (userWlbExpressTemplate == null) {
            throw new IllegalArgumentException("找不到该模板" + id);
        }
        if (StringUtils.isEmpty(userWlbExpressTemplate.getCloudCustomAreaConfig())) {
            UniformCloudTemplateUtils.calculateCustomArea(staff, userWlbExpressTemplate, cache);
        }
        List<FieldHeadText> fieldHeadTexts = userWlbExpressTemplate.getFieldHeadTexts();
        List<FieldSettings> fieldSettingses = userWlbExpressTemplate.getFieldSettings();
        if (fieldHeadTexts != null && fieldHeadTexts.size() > 0 && fieldSettingses != null && fieldSettingses.size() > 0) {
            Map<Integer, List<FieldSettings>> fieldSettingMap = new HashMap<Integer, List<FieldSettings>>();
            for (FieldSettings fieldSettings : fieldSettingses) {
                List<FieldSettings> fieldSettingsList = fieldSettingMap.get(fieldSettings.getTitleId());
                if (fieldSettingsList == null) {
                    fieldSettingsList = new ArrayList<FieldSettings>();
                }
                fieldSettingsList.add(fieldSettings);

                fieldSettingMap.put(fieldSettings.getTitleId(), fieldSettingsList);
            }
            for (FieldHeadText fieldHeadText : fieldHeadTexts) {
                Integer titleId = fieldHeadText.getId();
                fieldHeadText.setFieldSettingsList(fieldSettingMap.get(titleId));
            }
        }

        // 查询分享者的自定义模板数据
        User shareUser = userService.queryById(userWlbExpressTemplate.getTaobaoId());
        Staff shareStaff = staffService.queryDefaultStaffByCompanyId(shareUser.getCompanyId());
        shareStaff.setUsers(Collections.singletonList(shareUser));
        userWlbExpressTemplate.setAkcCustomListVOS(userAkcExpressTemplateService.userCustomTemplateQuery(shareStaff, userWlbExpressTemplate.getExpressId()));

        // 爱库存默认自定义区域
        userWlbExpressTemplate.setCloudCustomAreaConfig(String.format("[{\"height\":50,\"width\":%s}]", userWlbExpressTemplate.getWidthCustom()));

        return userWlbExpressTemplate;
    }

    /**
     * 修改用户模板名称
     *
     * @param id   用户模板id
     * @param name 新的模板名称
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/name/edit", method = RequestMethod.POST)
    @ResponseBody
    public Object userNameEdit(Long id, String name, String api_name) throws SessionException {
        Assert.isTrue(null != id && id > 0, "请传入id");
        Assert.hasText(name, "请传入name");

        Staff staff = getStaff();
        UserWlbExpressTemplate originTemplate = userAkcExpressTemplateService.userQuery(staff, id, false);
        Assert.notNull(originTemplate, "找不到对应模板");

        List<UserWlbExpressTemplateName> userWlbExpressTemplateNameList = userAkcExpressTemplateService.getAkcTemplateName(staff, originTemplate.getWlbTemplateType());
        for (UserWlbExpressTemplateName userWlbExpressTemplateName : userWlbExpressTemplateNameList) {
            if (name.equals(userWlbExpressTemplateName.getTemplateName()) && !id.equals(userWlbExpressTemplateName.getId())) {
                throw new IllegalArgumentException("模板名称重复，请重新输入!");
            }
        }

        UserWlbExpressTemplate userWlbExpressTemplate = new UserWlbExpressTemplate();
        userWlbExpressTemplate.setId(id);
        userWlbExpressTemplate.setName(name);
        userWlbExpressTemplate.setExpressId(originTemplate.getExpressId());
        userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);

        AkcCacheUtil.cleanForEachWlbTemplateList(staff, userAkcExpressTemplateService, cache);

        StringBuilder sb = new StringBuilder();
        sb.append("将爱库存共享模板名【");
        sb.append(originTemplate.getName());
        sb.append("】修改为【");
        sb.append(name);
        sb.append("】");
        OpLogHelper.recodeOpLog(opLogService, request, staff, "userNameEdit", id.toString(),
                sb.toString(), JSON.toJSONString(id));
        return Status.buildSuccessStatus();
    }

    /**
     * 锁定打印机
     * 存储格式:
     * {
     * "主帐号淘宝Id": "锁定打印机名称",
     * "子账号淘宝Id": "锁定打印机名称"
     * }
     *
     * @param templateId
     * @param printerName
     * @param api_name
     * @return
     * @throws SessionException
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/pt/divide/akc/user/lockprinter", method = RequestMethod.POST)
    @ResponseBody
    public Object lockPrinter(Long templateId, String printerName, String api_name) throws SessionException {
        Assert.isTrue(null != templateId && templateId > 0, "请传入templateId");

        Staff staff = getStaff();
        UserWlbExpressTemplate wlbExpressTemplate = userAkcExpressTemplateService.userQueryWithCache(staff, templateId, false);
        Assert.notNull(wlbExpressTemplate, "找不到该id的模板信息");

        String printNameJSON = wlbExpressTemplate.getPrinterName();
        Map<String, String> map = null;
        if (!StringUtils.isEmpty(printNameJSON)) {
            try {
                map = JSON.parseObject(printNameJSON, Map.class);
            } catch (Exception e) {
                log.error("锁定打印机转化json出错:{}", printNameJSON, e);
            }
        }
        if (map == null) {
            map = new HashMap();
        }
        map.put(String.valueOf(staff.getId()), printerName);

        UserWlbExpressTemplate userWlbExpressTemplate = new UserWlbExpressTemplate();
        userWlbExpressTemplate.setId(templateId);
        userWlbExpressTemplate.setPrinterName(JSON.toJSONString(map));
        userWlbExpressTemplate.setExpressId(wlbExpressTemplate.getExpressId());

        userAkcExpressTemplateService.update(staff, userWlbExpressTemplate);
        userAkcExpressTemplateService.cleanCache(staff, templateId);

        StringBuilder sb = new StringBuilder();
        sb.append("爱库存共享模板【");
        sb.append(wlbExpressTemplate.getName());
        if (StringUtils.isEmpty(printerName)) {
            sb.append("】解锁打印机");
        } else {
            sb.append("】锁定打印机【");
            sb.append(printerName);
            sb.append("】");
        }
        OpLogHelper.recodeOpLog(opLogService, request, staff, "userLockPrint", templateId.toString(),
                sb.toString(), JSON.toJSONString(printerName));
        return Status.buildSuccessStatus();
    }

    /**
     * 得到该用户的电子面单模板名称
     *
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/pt/divide/akc/user/templateName", method = RequestMethod.GET)
    @ResponseBody
    public Object getWlbTemplateName(String api_name) throws SessionException {
        return userAkcExpressTemplateService.getAkcTemplateName(getStaff(), WlbTemplateTypeEnum.DIVIDE_AKC.getValue());
    }

    /**
     * 添加爱库存模板
     *
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/add", method = RequestMethod.POST)
    @ResponseBody
    public Object userAdd(WlbUserAddParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        params.setWlbType(EnumWlbType.DIVIDE_AKC.getValue());
        params.setWlbTemplateType(WlbTemplateTypeEnum.DIVIDE_AKC.getValue());
        Long templateId = userAkcExpressTemplateService.addUserWlbExpressTemplate(staff, params);
        OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcDefault", params.getWlbType().toString(),
                "新增爱库存共享模板", JSON.toJSONString(params.getWlbType()));
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("templateId", templateId);
        return result;
    }


    /**
     * 删除添加的爱库存模板
     *
     * @return
     */
    @RequestMapping(value = "/pt/divide/akc/user/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteAddTemplate(Long templateId, String api_name) throws Exception {
        Integer wlbType = EnumWlbType.DIVIDE_AKC.getValue();
        Staff staff = getStaff();
        userAkcExpressTemplateService.deleteAddTemplate(staff, templateId);
        OpLogHelper.recodeOpLog(opLogService, request, staff, "AkcDefault", wlbType.toString(),
                "删除爱库存共享模板", JSON.toJSONString(wlbType));
        return Status.buildSuccessStatus();
    }
}

package com.raycloud.dmj.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.logistics.LogisticsWarningBusiness;
import com.raycloud.dmj.business.pt.FeatureSetBusiness;
import com.raycloud.dmj.business.pt.common.PtLockBusiness;
import com.raycloud.dmj.business.pt.template.business.UserLogisticsCompanyBusiness;
import com.raycloud.dmj.business.pt.template.impl.UserLogisticsCompanyTemplateService;
import com.raycloud.dmj.business.pt.waybill.PrinterConfigBusinessService;
import com.raycloud.dmj.business.pt.waybill.WaybillTradeQueryBusinessService;
import com.raycloud.dmj.business.pt.waybill.impl.PrintCheckService;
import com.raycloud.dmj.business.trade.GxTradeModifyTemplateLogService;
import com.raycloud.dmj.business.trade.TradeFilterBusiness;
import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.business.wave.TradeWavePrintBusiness;
import com.raycloud.dmj.business.wave.WaveHelpBusiness;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.pt.bo.MultiPacksOutSidDetail;
import com.raycloud.dmj.business.wlb.PostPrintWlbBusiness;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessCondition;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.annotation.AccessShields;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.caigou.*;
import com.raycloud.dmj.domain.enums.AiPackmaOpEnum;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.WaveTypeEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.*;
import com.raycloud.dmj.domain.pt.log.*;
import com.raycloud.dmj.domain.pt.model.UserTemplateLockPrintModel;
import com.raycloud.dmj.domain.pt.model.print.EndPrintTimeRequest;
import com.raycloud.dmj.domain.pt.model.print.FailPrintParam;
import com.raycloud.dmj.domain.pt.model.print.IPrintRequest;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.model.waybill.get.WlbRequestGet;
import com.raycloud.dmj.domain.pt.params.AsynJobPrintParams;
import com.raycloud.dmj.domain.pt.params.MultiPacksPrintTradeLogDetailParams;
import com.raycloud.dmj.domain.pt.params.PrintReceivablesDataParam;
import com.raycloud.dmj.domain.pt.params.SaveTemplateParams;
import com.raycloud.dmj.domain.pt.params.TradeRepeatPrintTraceParams;
import com.raycloud.dmj.domain.pt.printvo.*;
import com.raycloud.dmj.domain.pt.rest.request.waybill.WaybillValidRequest;
import com.raycloud.dmj.domain.pt.rest.response.waybill.*;
import com.raycloud.dmj.domain.pt.wlb.PrintInvoicesDataParam;
import com.raycloud.dmj.domain.pt.wlb.PrintMerchantCodeDataParam;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.vo.WaveAutoPrintVO;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.MapUtils;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.ReceiveUniqueCodeParams;
import com.raycloud.dmj.domain.wave.model.TradePostPrintContext;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import com.raycloud.dmj.domain.wave.model.WaveTradeQueryParams;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.TradePostPrintContextUtils;
import com.raycloud.dmj.domain.wave.utils.WaveTraceUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.AllocateTask;
import com.raycloud.dmj.domain.wms.PickingType;
import com.raycloud.dmj.domain.wms.WarehousePositionConfig;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.dto.stalls.trade.response.SaleTradeFullResp;
import com.raycloud.dmj.express.api.IExpressTemplateConfigService;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.log.PrintESLog;
import com.raycloud.dmj.log.PrintLogType;
import com.raycloud.dmj.merchantCode.params.PrintDataGetParams;
import com.raycloud.dmj.getter.TradePurchaseOrderParams;
import com.raycloud.dmj.model.pt.PrintReceiptDataParams;
import com.raycloud.dmj.model.pt.VerifyReceiptPrintResp;
import com.raycloud.dmj.model.pt.VipDeliveryPrintParams;
import com.raycloud.dmj.param.FxgPrintDataParam;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.ptException.ForceRecyclingException;
import com.raycloud.dmj.ptException.HaveToPrintPtException;
import com.raycloud.dmj.services.ILockCallback;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.caigou.IPurchaseService;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.domain.ItemTraceBillTypeEnum;
import com.raycloud.dmj.services.domain.WaveItemTraceActionModulePathEnum;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.IUniqueCodeServiceDubbo;
import com.raycloud.dmj.services.helper.*;
import com.raycloud.dmj.services.helper.wlb.TemplateHelper;
import com.raycloud.dmj.services.helper.wlb.WayBillCancelParamHelper;
import com.raycloud.dmj.services.helper.wlb.WayBillCompanyHelper;
import com.raycloud.dmj.services.helper.wlb.WlbExpressHelper;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.print.*;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.pt.helper.status.impl.AllocateGetPathImpl;
import com.raycloud.dmj.services.pt.jd.IUserLogisticsCompaniesJdService;
import com.raycloud.dmj.services.pt.log.PickerPrintLogService;
import com.raycloud.dmj.services.pt.model.*;
import com.raycloud.dmj.services.pt.model.getter.AcquireGetterDataParams;
import com.raycloud.dmj.services.pt.model.print.FieldValuesRequest;
import com.raycloud.dmj.services.pt.model.print.FieldValuesResponse;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.pt.support.PrintDataShowService;
import com.raycloud.dmj.services.pt.support.UserWlbExpressSumaitongService;
import com.raycloud.dmj.services.pt.support.UserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.utils.PtStringUtils;
import com.raycloud.dmj.services.pt.utils.PtTradeAndOrderUtils;
import com.raycloud.dmj.services.pt.utils.SendWayBillEcUtils;
import com.raycloud.dmj.services.pt.wireless.IBaseWirelessPrintService;
import com.raycloud.dmj.services.pt.wireless.constant.WirelessPrintConstant;
import com.raycloud.dmj.services.pt.wlb.waybill.util.WaybillServiceUtil;
import com.raycloud.dmj.services.pt.wlb.waybill.processor.impl.DefaultWayBillPostProcessor;
import com.raycloud.dmj.services.repair.IRepairOrderService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trade.recommendation.IPlatformRecommendationService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.support.PrintConfigService;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;
import com.raycloud.dmj.services.trades.support.wave.business.PostPrintUniqueCodeBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.TradeWavePackBusiness;
import com.raycloud.dmj.services.trades.support.wave.dubbo.UniqueCodeServiceDubbo;
import com.raycloud.dmj.services.trades.support.wave.efficient.EfficientEnum;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.dmj.services.utils.WavePickUtils;
import com.raycloud.dmj.services.vip.logistics.model.PrintLabelDataResponse;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.CompanyUtils;
import com.raycloud.dmj.utils.FxgExpressTemplateUtil;
import com.raycloud.dmj.utils.FxgXsdExpressTemplateUtil;
import com.raycloud.dmj.utils.WaveItemTraceLogHelper;
import com.raycloud.dmj.utils.WaveItemTracePTLogHelper;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.waybill.common.context.PtWaybillPathContext;
import com.raycloud.dmj.waybill.common.enums.PrintPathEnum;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.interceptors.RequestBodyParamsUtils;
import com.raycloud.dmj.web.model.SysItemSkuVo;
import com.raycloud.dmj.web.model.TradeCanDeliverModel;
import com.raycloud.dmj.web.model.TradeTraceParam;
import com.raycloud.dmj.web.model.caigou.PreinOrderParams;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import com.raycloud.dmj.web.model.wms.WarehousePositionConfigParams;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.dmj.web.utils.OpLogHelper;
import com.raycloud.dmj.web.utils.SaveTradeTraceHelper;
import com.raycloud.dmj.wlb.PostPrintRequest;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import com.raycloud.dmj.services.items.shipper.IShipperDubbo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.waybill.common.enums.AllocateGetPathEnum.POST_GET;
import static com.raycloud.dmj.waybill.common.enums.PrintPathEnum.*;
import static com.raycloud.dmj.waybill.common.enums.RecycledPathEnum.MATCH_COURIER_RECYCLED;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> huangjie
 * @Date : 2016/11/28 11:37
 * @Info :
 */
@Controller
@RequestMapping("/trade")
@Scope("prototype")
@LogTag(value = "trade", enableArgs = "false", enableResponse = "false")
public class TradePrintController extends Sessionable {
    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private IUserInvoicesTemplateService userInvoicesTemplateService;
    @Resource
    IUserExpressTemplateService userExpressTemplateService;
    @Resource
    IExpressCompanyService expressCompanyService;
    @Resource
    UserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    IUserWlbExpressTemplateCloudService userWlbExpressTemplateCloudService;
    @Resource
    IUserJdAlphaExpressTemplateService userJdAlphaExpressTemplateService;
    @Resource
    IUserDeliverTemplateService userDeliverTemplateService;
    @Resource
    IUserPickerTemplateService userPickerTemplateService;
    @Resource
    IUserGetterTemplateService userGetterTemplateService;
    @Resource
    ISysTradeService sysTradeService;
    @Resource
    IPrintService printAdapterService;
    @Resource
    IExpressService expressService;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource(name = "userWlbExpressCompanyAdapterService")
    private IUserWlbExpressCompanyService userWlbExpressCompanyAdapterService;
    @Resource
    IPrintTradelogService printTradeLogService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IWaveConfigService waveConfigService;
    @Resource
    IWarehouseTemplateService warehouseTemplateService;
    @Resource
    IUserLogisticsCompaniesJdService userLogisticsCompaniesJdService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    ILargeGetterPrintService printLargeGetterService;
    @Resource
    IPrintCaigouAsGetterService printCaigouAsGetterService;
    @Resource
    IPrintShouhuoAsGetterService printShouhuoAsGetterService;
    @Resource(name = "printWaveGetterService")
    IPrintGetterService printWaveGetterService;
    @Resource
    IBoxingListPrintService boxingListPrintService;
    @Resource
    IPrintLargePickerService printLargePickerService;
    @Resource
    IPrintLargePickerService printLargePickerVipService;
    @Resource
    IOpLogService opLogService;
    @Resource
    IPrintDataSourceService printDataSourceService;
    @Resource
    CloudSmartMatchHelper cloudSmartMatchHelper;
    @Resource
    private ITradePostPrintService tradePostPrintService;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;
    @Resource(name = "tradeMultiWavesPostPrintService")
    private ITradePostPrintService tradeMultiWavesPostPrintService;
    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;
    @Resource
    private IWmsService wmsService;
    @Resource
    ITradeWaveService tradeWaveService;
    @Resource
    Configurable config;
    @Resource
    PtLockBusiness ptLockBusiness;
    @Resource
    IPrintDataShowService printDataShowService;
    @Autowired
    PrintDataShowService pds;
    @Resource
    IRepairOrderService repairOrderService;
    @Resource
    PrintHelperService printHelperService;
    @Resource
    IOutSidPoolService outSidPoolService;
    @Resource
    IWaveSortingService waveSortingService;
    @Resource
    IWaveTraceService waveTraceService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    PrintVipTradeService printVipTradeService;
    @Autowired
    ITradeService tradeService;
    @Resource
    PrintConfigService printConfigService;
    @Resource
    PickerPrintLogService pickerPrintLogService;
    @Resource
    ITradeStaffConfigService tradeStaffConfigService;
    @Resource
    MerchantCodeGroupService merchantCodeGroupService;
    @Resource
    PrintAllocateService printAllocateService;
    @Resource
    private IMultiPrintConfigService multiPrintConfigService;
    @Resource
    IStaffService staffService;
    @Resource
    private PrintBoxLabelService printBoxLabelService;
    @Resource
    private IItemTraceService itemTraceService;
    @Resource
    private ISalesWirelessPrintService salesWirelessPrintService;
    @Resource
    private StaffAssembleBusiness staffAssembleBusiness;
    @Resource
    private PrintOutboundService printOutboundService;
    @Resource
    PrintPageSearchService printPageSearchService;
    @Resource
    private IPrintDataSaveJobService printDataSaveJobService;
    @Resource
    private GxTradeModifyTemplateLogService gxTradeModifyTemplateLogService;
    @Resource
    private PrintReceivablesService printReceivablesService;
    @Resource
    private PrintProcessingService printProcessingService;
    @Resource
    private TradeWavePackBusiness tradeWavePackBusiness;
    @Resource
    private IBoxCodeWirelessPrintService boxCodeWirelessPrintService;
    @Resource
    private ICache cache;
    @Resource
    IProgressService progressService;
    @Resource
    private IPurchaseService purchaseService;
    @Resource(name = "wirelessPrintAdapterService")
    private IBaseWirelessPrintService baseWirelessPrintService;
    @Resource
    private PrintPreinService printPreinService;
    @Resource
    private FxBusiness fxBusiness;
    @Resource
    private FeatureSetBusiness featureSetBusiness;
    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;
    @Resource
    TradeWavePrintBusiness tradeWavePrintBusiness;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource
    private IPlatformRecommendationService platformRecommendationService;
    @Resource
    private IWaybillUsingRuleService waybillUsingRuleService;
    @Resource
    private PrintSaleTradePullRespService printSaleTradePullRespService;
    @Resource
    UserLogisticsCompanyTemplateService userLogisticsCompanyTemplateService;
    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    DefaultWayBillPostProcessor defaultWayBillPostProcessor;

    //缓存锁的key
    private static final String CACHE_PRINT_LOCK_KEY_ = "cache_print_lock_key_";
    /**
     * 编码打印数量 cache key
     */
    private static final String MERCHANT_NUM_CACHE_KEY = "merchant_num_cache_key";

    /**
     * 拣货单 异步保存 1 不异步 默认异步
     */
    private static final String PICKER_ASYN_SAVE_CACHE_KEY = "picker_asyn_save";

    @Resource
    private IOrderUniqueCodeService orderUniqueCodeService;
    @Resource
    private IOrderUniqueCodeExamineService orderUniqueCodeExamineService;
    @Resource
    ILogisticsProviderService logisticsProviderService;
    @Resource
    LogisticsWarningBusiness logisticsWarningBusiness;
    @Resource
    UserWlbExpressSumaitongService userWlbExpressSumaitongService;
    @Resource
    private PrintCheckService printCheckService;

    @Resource
    private IWaveAutoPrintService waveAutoPrintService;
    @Resource
    private IPrintDataService printDataService;
    @Resource
    IExpressTemplateConfigService expressTemplateConfigService;
    @Resource
    private IOutSidRecyclePoolService outSidRecyclePoolService;

    @Resource
    private AllocateGetPathImpl allocateGetPath;
    @Resource
    private PostPrintUniqueCodeBusiness postPrintUniqueCodeBusiness;
    @Resource
    private IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    private ITradeConfigNewService tradeConfigNewService;

    @Resource
    IWaybillGetTradeGroupService waybillGetTradeGroupService;
    @Resource
    private IIndexDubboService indexDubboService;
    @Resource
    private PostPrintWlbBusiness postPrintWlbBusiness;
    @Resource
    private IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;
    @Resource
    private IFastConsignPrintService fastConsignPrintService;
    @Resource
    private IUniqueCodeServiceDubbo uniqueCodeServiceDubbo;

    @Resource
    ITradePtService tradePtService;

    /**
     * 262144 是否分销指定快递模板
     */
    public static final int V_IF_FX_APPOINT_TEMPLATE_ID = 1 << 18;

    @Autowired(required = false)
    private IShipperDubbo iShipperDubbo;
    @Resource
    private IItemServiceDubbo itemService;
    /**
     * 获取采购单的打印数据
     * @throws Exception
     */
    @RequestMapping(value = "/print/caigou/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printCaigouData(Long templateId, String ids, Boolean onlyShowPrinterList, Boolean mandatory, Boolean onlyPrintReceived, String api_name, String mergePrint) throws Exception {

        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        param.setOnlyPrintReceived(onlyPrintReceived);
        final Long[] idArray = ArrayUtils.toLongArray(ids);
        final Staff staff = getStaff();

        return new AbstractInvoicesPrintData() {

            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryByPurchaseOrderIds(staff, Arrays.asList(idArray));
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getMergePrintData(param, staff, ids, onlyShowPrinterList, mandatory, 1, EnumInvoicesType.CAIGOU, EnumTemplateKind.CAIGOU, "采购单",
                userInvoicesTemplateService, printAdapterService, printConfigService, "caigou", mergePrint);
    }

    /**
     * 获取采退单打印数据
     */
    @RequestMapping(value = "print/caigouReturn/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printCaigouPrintData(Long templateId, String ids, Boolean onlyShowPrinterList, Boolean mandatory,
                                       String api_name, String mergePrint) throws Exception {
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        Assert.isTrue(StringUtils.isNotBlank(ids), "入参错误,采退单id不能为空!请刷新后重新选择单据打印!");
        final Long[] idArray = ArrayUtils.toLongArray(ids);
        final Staff staff = getStaff();

        return new AbstractInvoicesPrintData() {
            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryByPurchaseReturnIds(staff, Arrays.asList(idArray));
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }
        }.getMergePrintData(param, staff, ids, onlyShowPrinterList, mandatory, 1, EnumInvoicesType.CAIGOURETURN,
                EnumTemplateKind.CAIGOURETURN, "采购退货单", userInvoicesTemplateService, printAdapterService, printConfigService, "caigou", mergePrint);
    }

    /**
     * 获取销货单单打印数据
     */
    @RequestMapping(value = "print/saleTrade/data", method = RequestMethod.POST)
    @ResponseBody
    public Object saleTradePrintData(Long templateId, String ids, Boolean onlyShowPrinterList, String api_name, String mergePrint) throws Exception {
        Assert.notNull(ids, "请传入销货单id");
        List<Long> saleIds = new ArrayList<>();
        Map<String, Integer> printNumMap = new HashMap<>();
        if (ids.contains("_")) {
            List<String> tempIds = Strings.getAsStringList(ids, ",", true);
            for (String tempId : tempIds) {
                String[] tempArray = StringUtils.split(tempId, "_");
                saleIds.add(Long.parseLong(tempArray[0]));
                printNumMap.put(tempArray[0], Integer.valueOf(tempArray[1]));
            }
        } else {
            saleIds.addAll(Strings.getAsLongList(ids, ",", true));
        }
        final Staff staff = getStaff();

        List<SaleTradeFullResp> saleTradeFullResps = printPageSearchService.getSaleTradeFullResps(staff, saleIds, null);
        if (CollectionUtils.isEmpty(saleTradeFullResps)) {
            throw new IllegalArgumentException("未查询到销货单!!!销货单号:" + ids);
        }
        Map<Long, SaleTradeFullResp> saleTradeFullRespMap = saleTradeFullResps.stream().collect(Collectors.toMap(SaleTradeFullResp::getId, t -> t, (t1, t2) -> t1));
        List<SaleTradeFullResp> sortTrades = new ArrayList<>();
        Long customerId = null;
        Integer pickGoodsType = null;
        for (Long saleId : saleIds) {
            SaleTradeFullResp saleTradeFullResp = saleTradeFullRespMap.get(saleId);
            if (saleTradeFullResp == null) {
                logger.debug(LogHelper.buildLog(staff, "销货单号:" + saleId + ",为查询到销货单!"));
                continue;
            }
            // 合并打印：后端兜底校验 分销商 和 提货方式 是否一致
            if ("1".equals(mergePrint)) {
                // 分销商
                if (customerId == null) customerId = saleTradeFullResp.getCustomerId();
                if (customerId != null && !customerId.equals(saleTradeFullResp.getCustomerId()))
                    throw new IllegalArgumentException(String.format("分销商不一致!!!分销商：%s", saleTradeFullResp.getCustomerName()));
                // 提货方式
                if (pickGoodsType == null) pickGoodsType = saleTradeFullResp.getPickGoodsType();
                if (pickGoodsType != null && !pickGoodsType.equals(saleTradeFullResp.getPickGoodsType()))
                    throw new IllegalArgumentException(String.format("提货类型不一致!!!提货类型：%s", Integer.valueOf(1).equals(pickGoodsType) ? "自提" : "邮寄"));
            }
            sortTrades.add(saleTradeFullResp);
        }
        return getJsonForPrint(templateId, onlyShowPrinterList, mergePrint, printNumMap, staff, sortTrades);
    }


    /**
     * 获取销货单单打印数据,by sid
     */
    @RequestMapping(value = "print/saleTrade/dataFromTrade", method = RequestMethod.POST)
    @ResponseBody
    public Object saleTradePrintData(Long templateId, Boolean onlyShowPrinterList, String api_name, String sidStr) throws Exception {
        Assert.notNull(sidStr, "请传入sidStr");
        String mergePrint = "0";//不是合并打印
        Map<String, Integer> printNumMap = new HashMap<>();
        final Staff staff = getStaff();
        Long sid = Long.valueOf(sidStr);
        List<SaleTradeFullResp> saleTradeFullResps = printPageSearchService.getSaleTradeFullResps(staff, null, Lists.newArrayList(sid));
        if (CollectionUtils.isEmpty(saleTradeFullResps)) {
            throw new IllegalArgumentException(" 未查询到销货单!!! Sid号:" + sid);
        }
        saleTradeFullResps.get(0).setSid(Long.valueOf(sidStr));//拆单的销货单打印的时候,将拆单的sid写进销货单,方便之后查这笔订单的相关信息
        return getJsonForPrint(templateId, onlyShowPrinterList, mergePrint, printNumMap, staff, saleTradeFullResps);
    }


    private JSONObject getJsonForPrint(Long templateId, Boolean onlyShowPrinterList, String mergePrint, Map<String, Integer> printNumMap, Staff staff, List<SaleTradeFullResp> saleTradeFullResps) {
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.SALETRADE);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.SALETRADE);
        }
        Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板：" + templateId);
        JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList) {
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.SALETRADE, true);
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        }
        List<SaleTradePrintVO> saleTradePrintVOS = printSaleTradePullRespService.getFieldValues(staff, saleTradeFullResps, userInvoicesTemplate, printNumMap, mergePrint);
        result.put("fieldValues", saleTradePrintVOS);
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 销货单打印结束, 更新打印时间, by ids
     */
    @RequestMapping(value = "print/saleTrade/end", method = RequestMethod.POST)
    @ResponseBody
    public Object printSaleTradeEnd(String ids, String templateName, String api_name) throws SessionException {
        Long[] idArr = ArrayUtils.toLongArray(ids);
        if (idArr == null || idArr.length == 0) {
            logger.info("打印完成的销货单为空");
            return successResponse();
        }
        final Staff staff = getStaff();
        printPageSearchService.endPrintTime(staff, Arrays.asList(idArr), templateName);
        return successResponse();
    }


    /**
     * 获取收款单打印数据
     *
     * <AUTHOR>
     * @date 2021/4/13
     */
    @RequestMapping(value = "print/receivables/data", method = RequestMethod.POST)
    @ResponseBody
    public Object receivablesPrintData(@RequestBody PrintReceivablesDataParam param, String api_name) throws Exception {
        // 获取模板数据
        final Staff staff = getStaff();
        UserInvoicesTemplate userInvoicesTemplate = null;
        Long templateId = param.getTemplateId();
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.RECEIVABLES);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.RECEIVABLES);
        }

        JSONObject result = new JSONObject();
        // 查询收款单数据，填充属性值
        result.put("fieldValues", printReceivablesService.getFieldValues(staff, param));
        // 用户模板数据
        result.put("template", userInvoicesTemplate);
        // 所有用户模板数据
        if (com.raycloud.dmj.utils.DataUtils.defaultIfNull(param.getOnlyShowPrinterList(), false)) {
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff,
                    userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.RECEIVABLES)));
        }
        return result;
    }

    /**
     * 获取波次的订单sid
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/wave/sid", method = RequestMethod.POST)
    @ResponseBody
    public Object printWaveSid(String waveIdStr) throws Exception {
        Assert.notNull(waveIdStr, "请传入订单查询参数");
        Staff staff = getStaff();
        List<Trade> trades = Lists.newArrayList();
        trades = getTradesByWaveId(staff, waveIdStr);

        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("未查询到波次订单!!!waveIdStr:" + waveIdStr);
        }
        Long[] sidArray = TradeUtils.toSids(trades);
        JSONObject result = new JSONObject();

        result.put("sids", sidArray);
        return result;
    }


    /**
     * 获取加工单打印数据
     *
     * @param templateId  打印模板id
     * @param orderIdList 收款单id数组，多个以","分割
     * @return
     * <AUTHOR>
     * @date 2021/9/27
     */
    @RequestMapping(value = "print/processing/data", method = RequestMethod.POST)
    @ResponseBody
    public Object stockProcessingPrintData(Long templateId, String orderIdList, Integer processType, String api_name, String isPrint) throws Exception {
        // 获取模板数据
        final Staff staff = getStaff();
        UserInvoicesTemplate userInvoicesTemplate = null;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.PROCESSING);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.PROCESSING);
        }
        JSONObject result = new JSONObject();
        // 根据类型判断是仓内加工还是库存加工  0：仓内加工 1：库存加工已被废弃（KMERP-137030）
        if (processType == 0) {
            // 查询收款单数据，填充属性值
            result.put("fieldValues", printProcessingService.getStockFieldValues(staff, orderIdList, userInvoicesTemplate, isPrint));
        } else {
            result.put("fieldValues", printProcessingService.getWarehouseFieldValues(staff, orderIdList, userInvoicesTemplate, isPrint));
        }

        // 用户模板数据
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 获取波次唯品会订单的打印数据
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/vip/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printVipDeliveryData(String mixBillNo, Integer billType, String boxCode) throws Exception {
        Assert.notNull(mixBillNo, "请传入订单查询参数");
        Assert.notNull(billType, "请传入订单查询参数");
        Staff staff = getStaff();
        List<Trade> trades = Lists.newArrayList();
        if (Integer.valueOf(1).equals(billType)) {
            trades = getTradesBySid(staff, mixBillNo);
        } else if (Integer.valueOf(2).equals(billType)) {
            trades = getTradesByShortId(staff, mixBillNo);
        } else if (Integer.valueOf(4).equals(billType)) {
            trades = getTradesByWaveId(staff, mixBillNo);
        }

        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("未查询到唯品会出库单!!!");
        }
        Long[] sidArray = TradeUtils.toSids(trades);
        List<VipTradePrintVO> printVOS = printVipTradeService.getFieldValuesByDeliverySids(staff, sidArray, null);

        if (CollectionUtils.isEmpty(printVOS)) {
            logger.error(LogHelper.buildLog(staff, "未查询到唯品会出仓单打印信息，请确认是否绑定出仓单 mixBillNo:" + mixBillNo + " ,billType:" + billType));
            throw new IllegalArgumentException("未查询到唯品会出仓单打印信息，请确认是否绑定出仓单");
        }
        printVOS.forEach(vo -> vo.setBoxCode(boxCode));
        JSONObject result = new JSONObject();
        result.put("fieldValues", printVOS);
        result.put("template", null);
        return result;
    }

    public List<Trade> getTradesByWaveId(Staff staff, String waveIdStr) {
        List<Long> waveIds = Lists.newArrayList();
        waveIds.addAll(Arrays.asList(Strings.getAsLongArray(waveIdStr, ",", true)));

        if (logger.isDebugEnabled() && 1 == config.getInteger(PtConfigConst.PT_DETAIL_LOG_SWITCH, 0)) {
            logger.debug(LogHelper.buildLog(staff, String.format("printVipDeliveryData.waveIds:%s", waveIds)));
        }

        return tradeWaveService.queryTradesByWaveIds(staff, waveIds);
    }

    public List<Trade> getTradesBySid(Staff staff, String sids) {
        // 0. 参数检查
        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        final List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sidArray));
        Assert.isTrue(trades.size() == sidArray.length, "查询的订单数量和传入的订单数量不一致");
        return trades;
    }

    public List<Trade> getTradesByShortId(Staff staff, String shortIds) {
        // 0. 参数检查
        final Long[] sidArray = ArrayUtils.toLongArray(shortIds);
        final List<Trade> trades = tradeSearchService.queryByShortId(staff, false, sidArray);
        Assert.isTrue(trades.size() == sidArray.length, "查询的订单数量和传入的订单数量不一致");

        return trades;
    }

    /**
     * 获取波次唯品会订单的打印数据
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/vip/data/multi", method = RequestMethod.POST)
    @ResponseBody
    public Object printVipDeliveryDataMulti(@RequestBody List<VipDeliveryPrintParams> params) throws Exception {
        Assert.notEmpty(params, "请传入订单查询参数");
        params.forEach(param -> {
            Assert.notNull(param.getSids(), "请传入订单查询参数");
            Assert.notNull(param.getBoxCode(), "请传入订单查询参数");
        });
        Staff staff = getStaff();

        JSONObject result = new JSONObject();
        String sids = params.stream().map(VipDeliveryPrintParams::getSids).collect(Collectors.joining(","));
        List<VipTradePrintVO> printVOS = printVipTradeService.getFieldValuesByDeliverySids(staff, ArrayUtils.toLongArray(sids), null);
        List<VipTradePrintVO> vipTradePrintVOS = params.stream().map(param -> {
            List<VipTradePrintVO> vos = printVOS.stream()
                    .filter(vo -> Arrays.asList(StringUtils.split(vo.getSids(), ",")).contains(StringUtils.split(vo.getSids(), ",")[0])).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vos)) {
                logger.error(LogHelper.buildLog(staff, "未查询到唯品会出仓单打印信息，请确认是否绑定出仓单 sids:" + param.getSids()));
                throw new IllegalArgumentException("未查询到唯品会出仓单打印信息，请确认是否绑定出仓单");
            }
            printVOS.forEach(vo -> vo.setBoxCode(param.getBoxCode()));
            return printVOS.get(0);
        }).collect(Collectors.toList());
        result.put("fieldValues", vipTradePrintVOS);
        result.put("template", null);
        return result;
    }

    @RequestMapping(value = "/print/jit/label/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printJitLabelData(@RequestBody List<VipDeliveryPrintParams> params) throws Exception {
        Assert.notEmpty(params, "请传入订单查询参数");
        params.forEach(param -> {
            Assert.notNull(param.getSids(), "请传入订单查询参数");
            Assert.notNull(param.getBoxCode(), "请传入订单查询参数");
        });
        Staff staff = getStaff();

        JSONObject result = new JSONObject();
        List<PrintLabelDataResponse> printLabelDataResponse = new ArrayList<>();
        for (VipDeliveryPrintParams param : params) {
            Long[] sids = ArrayUtils.toLongArray(param.getSids());
            printLabelDataResponse.addAll(printVipTradeService.printJitLabelData(staff, param.getBoxCode(), sids));
        }
        result.put("fieldValues", printLabelDataResponse);
        result.put("template", null);
        return result;
    }

    @RequestMapping(value = "/print/single/jit/label/data", method = RequestMethod.POST)
    @ResponseBody
    public Object singlePrintJitLabelData(String mixBillNo, Integer billType, String boxCode) throws Exception {
        Assert.notNull(mixBillNo, "请传入订单查询参数");
        Assert.notNull(billType, "请传入订单查询参数");
        Staff staff = getStaff();
        List<Trade> trades = Lists.newArrayList();
        if (Integer.valueOf(1).equals(billType)) {
            trades = getTradesBySid(staff, mixBillNo);
        } else if (Integer.valueOf(2).equals(billType)) {
            trades = getTradesByShortId(staff, mixBillNo);
        } else if (Integer.valueOf(4).equals(billType)) {
            trades = getTradesByWaveId(staff, mixBillNo);
        }

        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("未查询到唯品会出库单!!!");
        }
        Long[] sids = TradeUtils.toSids(trades);
        List<PrintLabelDataResponse> printLabelDataResponseList = printVipTradeService.printJitLabelData(staff, boxCode, sids);
        JSONObject result = new JSONObject();
        result.put("fieldValues", printLabelDataResponseList);
        result.put("template", null);
        return result;
    }

    /**
     * 获唯品会出库单打印数据
     */
    @RequestMapping(value = "print/vipDelivery/data", method = RequestMethod.POST)
    @ResponseBody
    public Object vipDeliveryPrintData(Long templateId, String sids, String api_name) throws Exception {
        // 0. 参数检查
        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        final Staff staff = getStaff();
        final List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sidArray));
        Assert.isTrue(trades.size() == sidArray.length, "查询的订单数量和传入的订单数量不一致");

        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("未查询到唯品会出库单!!!订单单号:" + sids);
        }
        JSONObject result = new JSONObject();
        List<VipTradePrintVO> printVOS = printVipTradeService.getFieldValues(staff, sidArray, null);

        result.put("fieldValues", printVOS);
        result.put("template", null);
        return result;
    }

    /**
     * 获取调拨单打印数据
     */
    @RequestMapping(value = "print/allocate/data", method = RequestMethod.POST)
    @ResponseBody
    public Object allocatePrintData(Long templateId, String ids, Boolean onlyShowPrinterList, String api_name) throws Exception {

        final Long[] idArray = ArrayUtils.toLongArray(ids);
        final Staff staff = getStaff();
        List<AllocateTask> allocateTasks = printPageSearchService.queryTaskDetailByIds(staff, Arrays.asList(idArray));
        if (CollectionUtils.isEmpty(allocateTasks)) {
            throw new IllegalArgumentException("调拨单【" + ids + "】不存在！");
        }
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.ALLOCATE);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.ALLOCATE);
        }
        Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板：" + templateId);
        JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList) {
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.ALLOCATE, true);
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        }
        List<AllocatePrintVO> allocatePrintVOS = printAllocateService.getFieldValues(staff, allocateTasks, userInvoicesTemplate);
        result.put("fieldValues", allocatePrintVOS);
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 获取出入库单打印数据
     */
    @RequestMapping(value = "print/outbound/data", method = RequestMethod.POST)
    @ResponseBody
    public Object OutboundPrintData(Long templateId, Boolean onlyShowPrinterList, String api_name, Integer queryType, String codes) throws Exception {
        OtherInOutPrintQueryParams otherInOutPrintQueryParams = new OtherInOutPrintQueryParams();
        otherInOutPrintQueryParams.setQueryType(queryType);
        otherInOutPrintQueryParams.setCodes(Arrays.asList(codes.split(",")));
        final Staff staff = getStaff();
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.OUTBOUND);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.OUTBOUND);
        }
        Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板：" + templateId);
        JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList) {
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.OUTBOUND, true);
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        }
        List<OutboundPrintVO> outboundPrintVOS = printOutboundService.getFieldValues(staff, otherInOutPrintQueryParams, userInvoicesTemplate);
        result.put("fieldValues", outboundPrintVOS);
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 获取采购单的打印数据（以拿货单格式返回）
     *
     * @param ids
     * @param templateId
     * @param api_name
     * @return
     * @throws Exception 已过期，请使用{@link com.raycloud.dmj.controller.getter.GetterPrintController#getGetterData(com.raycloud.dmj.getter.GetterPrintDataQueryParams, java.lang.String)}
     */
    @RequestMapping(value = "/print/caigouasgetter/data", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Object printCaigouAsGetterData(String ids, Long templateId, String sortType, String api_name) throws Exception {
        Assert.hasText(ids, "请传入采购单编号");
        Long[] idArray = ArrayUtils.toLongArray(ids);
        Staff staff = getStaff();
        List<PurchaseOrder> purchaseOrders = printPageSearchService.queryByPurchaseOrderIds(staff, Arrays.asList(idArray));
        UserGetterTemplate userGetterTemplate = null;
        if (templateId != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, templateId);
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板");
        List<GetterSheetEntry> sheetEntries = printCaigouAsGetterService.getFieldValues(staff, purchaseOrders, userGetterTemplate, sortType);
        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("sheetEntries", sheetEntries);
        result.put("template", userGetterTemplate);
        UserInvoicesTemplate userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.MERCHANTCODE);
        Assert.notNull(userInvoicesTemplate, "当前用户找不到编码打印模板");
        result.put("merchantCodeTemplate", userInvoicesTemplate);
        UserGetterTemplates userGetterTemplates = userGetterTemplateService.getListAll(staff, 1);
        result.put("userGetterTemplates", TemplateHelper.getUserGetterTemplateList(staff, userGetterTemplates));
//        result.put("templateLockPrintList", TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate));
        UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.MERCHANTCODE, true);
        result.put("merchantCodeTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
//        List<UserDeliverTemplate> userDeliverTemplates = userDeliverTemplateService.listUserDeliverTemplates(staff);
//        result.put("deliverTemplates", TemplateHelper.listUserTemplates(staff, userDeliverTemplates));
        return result;
    }

    /**
     * 返回拿货单数据（根据已售采购建议）
     *
     * @return
     * @throws Exception 已过期，请使用{@link com.raycloud.dmj.controller.getter.GetterPrintController#getGetterData(com.raycloud.dmj.getter.GetterPrintDataQueryParams, java.lang.String)}
     */
    @RequestMapping(value = "/print/caigouassoldsuggest/data", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Object printCaigouAsSoldSuggestData(@RequestBody TradePurchaseOrderParams data) throws Exception {
        Staff staff = getStaff();
        List<PurchaseOrder> purchaseOrders = printPageSearchService.getGoodsBySalePurchaseOrder(staff, data.getPurchaseOrder(), data.getParams(), data.getSupplier(), data.getSuggests(), data.getCalculationType(), data.getCalculationNumber());
        UserGetterTemplate userGetterTemplate = null;
        if (data.getTemplateId() != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, data.getTemplateId());
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板");
        List<GetterSheetEntry> sheetEntries = printCaigouAsGetterService.getFieldValues(staff, purchaseOrders, userGetterTemplate, data.getSortType());
        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("sheetEntries", sheetEntries);
        result.put("template", userGetterTemplate);
        UserInvoicesTemplate userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.MERCHANTCODE);
        Assert.notNull(userInvoicesTemplate, "当前用户找不到编码打印模板");
        result.put("merchantCodeTemplate", userInvoicesTemplate);
        UserGetterTemplates userGetterTemplates = userGetterTemplateService.getListAll(staff, 1);
        result.put("userGetterTemplates", TemplateHelper.getUserGetterTemplateList(staff, userGetterTemplates));
        UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.MERCHANTCODE);
        result.put("merchantCodeTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        return result;
    }

    /**
     * 获取收货单的打印数据（以拿货单格式返回）
     *
     * @param ids
     * @param templateId
     * @param api_name
     * @return
     * @throws Exception 已过期，请使用{@link com.raycloud.dmj.controller.getter.GetterPrintController#getGetterData(com.raycloud.dmj.getter.GetterPrintDataQueryParams, java.lang.String)}
     */
    @RequestMapping(value = "/print/shouhuoasgetter/data", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Object printShouhuoAsGetterData(String ids, Long templateId, Long warehouseId, String api_name) throws Exception {
        Assert.hasText(ids, "请传入收货单编号");
        Long[] idArray = ArrayUtils.toLongArray(ids);
        Staff staff = getStaff();
        List<WarehouseEntry> warehouseEntries = printPageSearchService.queryByWarehouseEntryId(staff, Arrays.asList(idArray));
        UserGetterTemplate userGetterTemplate = null;
        if (templateId != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, templateId);
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板");
        List<GetterSheetEntry> sheetEntries = printShouhuoAsGetterService.getFieldValues(staff, warehouseEntries, userGetterTemplate, warehouseId);
        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("sheetEntries", sheetEntries);
        result.put("template", userGetterTemplate);
        UserInvoicesTemplate userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.MERCHANTCODE);
        Assert.notNull(userInvoicesTemplate, "当前用户找不到编码打印模板");
        result.put("merchantCodeTemplate", userInvoicesTemplate);
        UserGetterTemplates userGetterTemplates = userGetterTemplateService.getListAll(staff, 1);
        result.put("userGetterTemplates", TemplateHelper.getUserGetterTemplateList(staff, userGetterTemplates));
//        result.put("templateLockPrintList", TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate));
        UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.MERCHANTCODE, true);
        result.put("merchantCodeTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
//        List<UserDeliverTemplate> userDeliverTemplates = userDeliverTemplateService.listUserDeliverTemplates(staff);
//        result.put("deliverTemplates", TemplateHelper.listUserTemplates(staff, userDeliverTemplates));
        return result;
    }

    /**
     * 获取波次拿货单的打印数据
     *
     * @param params
     * @param templateId
     * @param api_name
     * @return
     * @throws Exception 已过期，请使用{@link com.raycloud.dmj.controller.getter.GetterPrintController#getGetterData(com.raycloud.dmj.getter.GetterPrintDataQueryParams, java.lang.String)}
     */
    @RequestMapping(value = "/print/wavegetter/data", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Object printPosGetterData(WaveFilterParams params, Long templateId, String api_name) throws Exception {
        Assert.notNull(params, "请传入波次查询参数");
        Staff staff = getStaff();
        String waveIdStr = params.getWaveIdStr();
        List<Long> waveIds = new ArrayList<>();

        if (StringUtils.isBlank(waveIdStr)) {
            Page page = new Page();
            page.setPageSize(999999999);

            // 查询出的波次不需要包含播种完成的，所以如果没有distributionStatus，则设置为0,1,2,3，否则去除其中的4
            String distributionStatus = params.getDistributionStatus();
            if (StringUtils.isNotBlank(distributionStatus)) {
                Set<String> distributionStatusSet = new HashSet<>(Arrays.asList(StringUtils.split(distributionStatus, ",")));
                distributionStatusSet.remove("4");
            } else {
                params.setDistributionStatus("0,1,2,3");
            }

            PageListBase<Wave> wavePageListBase = tradeWaveService.queryCreatedWaves(staff, params, page);
            List<Wave> waves = wavePageListBase.getList();
            for (Wave wave : waves) {
                waveIds.add(wave.getId());
            }
        } else {
            waveIds.addAll(Arrays.asList(Strings.getAsLongArray(waveIdStr, ",", true)));
        }

        if (logger.isDebugEnabled() && 1 == config.getInteger(PtConfigConst.PT_DETAIL_LOG_SWITCH, 0)) {
            logger.debug(LogHelper.buildLog(staff, String.format("printPosGetterData.waveIds:%s", waveIds)));
        }

        UserGetterTemplate userGetterTemplate = null;
        if (templateId != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, templateId);
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板");
        AcquireGetterDataParams acquireGetterDataParams = new AcquireGetterDataParams.AcquireGetterDataParamsBuilder().setChannel(AcquireGetterDataParams.Channel.WAVE).setWaveIds(waveIds).build();

        List<GetterSheetEntry> sheetEntries = printWaveGetterService.getFieldValues(staff, acquireGetterDataParams, userGetterTemplate);
        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("sheetEntries", sheetEntries);
        result.put("template", userGetterTemplate);
        UserGetterTemplates userGetterTemplates = userGetterTemplateService.getListAll(staff, 1);
        result.put("userGetterTemplates", TemplateHelper.getUserGetterTemplateList(staff, userGetterTemplates));
        result.put("templateLockPrintList", TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate));
//        List<UserDeliverTemplate> userDeliverTemplates = userDeliverTemplateService.listUserDeliverTemplates(staff);
//        result.put("deliverTemplates", TemplateHelper.listUserTemplates(staff, userDeliverTemplates));
        if (waveIds.size() > 0) {
            waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waveIds, WaveTraceOperateEnum.PRINT_WAVEGETTER_DATA, "打印拿货单"));
        }
        return result;
    }

    /**
     * 获取收货单的打印数据
     *
     * @param printReceiptDataParams 打印参数
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/receipt/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printReceiptData(PrintReceiptDataParams printReceiptDataParams, String api_name) throws Exception {

        final Staff staff = getStaff();
        logger.info(LogHelper.buildLog(staff,"打印收货单入参:" + JSONObject.toJSONString(printReceiptDataParams)));

        Long templateId = printReceiptDataParams.getTemplateId();
        String ids = printReceiptDataParams.getIds();
        Boolean onlyShowPrinterList = printReceiptDataParams.getOnlyShowPrinterList();
        Boolean mandatory = printReceiptDataParams.getMandatory();
        String mergePrint = printReceiptDataParams.getMergePrint();
        Boolean printReceipt = printReceiptDataParams.getPrintReceipt();

        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);

        final Long[] idArray = ArrayUtils.toLongArray(ids);
        Object object = new AbstractInvoicesPrintData() {

            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryByWarehouseEntryId(staff, Arrays.asList(idArray));
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getMergePrintData(param, staff, ids, onlyShowPrinterList, mandatory, 1, EnumInvoicesType.RECEIPT, EnumTemplateKind.RECEIPT, "收货单",
                userInvoicesTemplateService, printAdapterService, printConfigService, "caigou", mergePrint);
        if (!(object instanceof Exception) && printReceipt != null && printReceipt) {
            // 打印成功更新收货单状态 & 更新收货单打印次数
            purchaseService.updatePrintStatusByWarehouseEntryId(staff, Arrays.asList(idArray), WarehouseEntry.WAREHOUSE_ENTRY_FINISH_PRINT);
            purchaseService.updateWarehouseEntryPrintNum(staff, Arrays.asList(idArray),null);
        }
        return object;
    }

    @RequestMapping(value = "/receipt/before/verify", method = RequestMethod.POST)
    @ResponseBody
    public Object printReceiptBeforeVerify(String receiptIds, String api_name) throws Exception {

        List<Long> receiptIdList = ArrayUtils.toLongList(receiptIds);
        final Staff staff = getStaff();
        List<WarehouseEntry> warehouseEntries = null;
        try {
            warehouseEntries = purchaseService.queryByIds(staff, receiptIdList, false);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "收货单打印前置校验，批量查询收货单信息Dubbo接口失败"));
        }
        JSONObject result = new JSONObject();
        if (CollectionUtils.isEmpty(warehouseEntries)) {
            return result;
        }
        List<VerifyReceiptPrintResp> list = warehouseEntries.stream()
                .filter(entity -> entity.getPrintNum() != null && entity.getPrintNum() > 0)
                .map(entity -> new VerifyReceiptPrintResp(entity.getCode(), entity.getPrintNum()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            Collections.reverse(list);
        }
        result.put("printInfo", list);
        return result;
    }

    /**
     * 商家编码打印
     *
     * @param param
     * @param onlyShowPrinterList
     * @param mandatory
     * @param source              请求来源（trade/caigou）
     * @param isDelimited         是否需要分隔码
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/merchantCode/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printMerchantCodeData(PrintMerchantCodeDataParam param, Boolean onlyShowPrinterList, Boolean mandatory, String api_name, Boolean isDelimited, String source) throws Exception {
        final JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList && StringUtils.isEmpty(param.getSysItemIds()) && StringUtils.isEmpty(param.getSysSkuIds())) {//用来过滤波次编码打印的请求
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.queryUserEnableTemplates(getStaff(), Arrays.asList(EnumInvoicesType.MERCHANTCODE.getValue(), EnumInvoicesType.TAGS.getValue()));
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(getStaff(), userInvoicesTemplates));
            return result;
        }
        PrintTemplateHelper.idsDistinct(param);
        final List<Long> sysItemIdList = new ArrayList<>();
        final List<Long> sysSkuIdList = new ArrayList<>();
        final Staff staff = getStaff();
        if (param != null && StringUtils.isNotEmpty(param.getOperatePath())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "打印商家编码页面来源：" + param.getOperatePath()));
            }
        }
        StringBuilder ids = new StringBuilder();
        initSysItemAndSkuList(staff, param, ArrayUtils.toLongArray(param.getSysItemIds()), ArrayUtils.toLongArray(param.getSysSkuIds()), sysItemIdList, sysSkuIdList, ids, param.getSids(), param.getSuitType(), source);
        return new AbstractInvoicesPrintData() {
            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryDmjItems4Print(staff, sysItemIdList, sysSkuIdList);
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }
        }.getPrintData(param, staff, ids.toString(), onlyShowPrinterList, mandatory, 1, EnumInvoicesType.MERCHANTCODE,
                EnumTemplateKind.MERCHANTCODE, "商家编码单", userInvoicesTemplateService, printAdapterService, printConfigService, isDelimited, merchantCodeGroupService, source);
    }

    /**
     * 获取自定义商品商家编码模板的打印数据
     *
     * @param param
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/merchantCode/data/custom", method = RequestMethod.POST)
    @ResponseBody
    public Object printMerchantCodeDataCustom(PrintMerchantCodeDataParam param, Boolean onlyShowPrinterList, Boolean mandatory, String api_name, String source) throws Exception {
        return printMerchantCodeData(param, onlyShowPrinterList, mandatory, api_name, false, source);
    }

    /**
     * 商家吊牌打印
     *
     * @param sysItemIds
     * @param sysSkuIds
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/tags/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printTagsData(String workOrderIds, Long templateId, String sysItemIds, String sysSkuIds, Boolean onlyShowPrinterList,
                                Boolean mandatory, String printNums, String api_name, String source) throws Exception {
        /**吊牌打印支持商家编码模版打印*/
        PrintMerchantCodeDataParam param = new PrintMerchantCodeDataParam();
        UserInvoicesTemplate userInvoicesTemplate = null;
        final Staff staff = getStaff();
        String templateName = "商家吊牌单";
        EnumTemplateKind enumTemplateKind = EnumTemplateKind.TAGS;
        EnumInvoicesType[] invoicesTypes = {EnumInvoicesType.TAGS, EnumInvoicesType.MERCHANTCODE};
        if (templateId != null && !templateId.equals("")) {
            for (int i = 0; i < invoicesTypes.length; i++) {
                userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, invoicesTypes[i]);
                if (userInvoicesTemplate != null) {
                    Integer invoicesType = userInvoicesTemplate.getInvoicesType();
                    if (invoicesType == EnumInvoicesType.MERCHANTCODE.getValue()) {
                        templateName = "商家编码单";
                        enumTemplateKind = EnumTemplateKind.MERCHANTCODE;
                        param = new PrintMerchantCodeDataParam();
                    }
                    break;
                }
            }
            com.alibaba.dubbo.common.utils.Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板" + templateId);
        }
        param.setTemplateId(templateId);
        if (null == onlyShowPrinterList) {
            onlyShowPrinterList = Boolean.FALSE;
        }
        if (onlyShowPrinterList && StringUtils.isEmpty(sysItemIds) && StringUtils.isEmpty(sysSkuIds)) {
            JSONObject result = new JSONObject();
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.MERCHANTCODE, true);
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
            return result;
        }

        if (StringUtils.isEmpty(sysItemIds) || StringUtils.isEmpty(sysSkuIds)) {
            throw new IllegalArgumentException("请求参数不正确");
        }
        Long[] sysItemIdArray = ArrayUtils.toLongArray(sysItemIds);
        Long[] sysSkuIdArray = ArrayUtils.toLongArray(sysSkuIds);
        if (sysItemIdArray.length != sysSkuIdArray.length) {
            throw new IllegalArgumentException("sysItemIds和sysSkuIds的参数个数不一致.");
        }
        param.setPrintNums(printNums);
        param.setSysItemIds(sysItemIds);
        param.setSysSkuIds(sysSkuIds);
        param.setWorkOrderIds(workOrderIds);

        final List<Long> sysItemIdList = new ArrayList<Long>();
        final List<Long> sysSkuIdList = new ArrayList<Long>();
        StringBuilder ids = new StringBuilder();
        initSysItemAndSkuList(staff, param, sysItemIdArray, sysSkuIdArray, sysItemIdList, sysSkuIdList, ids, null, null, null);

        EnumInvoicesType[] invoicesTypes2 = {EnumInvoicesType.MERCHANTCODE};
        return new AbstractInvoicesPrintData() {

            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryDmjItems4Print(staff, sysItemIdList, sysSkuIdList);
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getPrintData(param, staff, ids.toString(), onlyShowPrinterList, mandatory, 1, invoicesTypes2, enumTemplateKind, templateName,
                userInvoicesTemplateService, printAdapterService, printConfigService, false, null, source, null);
    }

    /**
     * 获取商品商家箱码的打印数据
     *
     * @param boxCodes
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/boxCode/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printBoxCodeData(Long templateId, String boxCodes, Boolean onlyShowPrinterList, String printNums, Boolean mandatory, String api_name, String source) throws Exception {
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        param.setPrintNums(printNums);
        final Staff staff = getStaff();
        Assert.hasText(boxCodes, "请传入箱码");

        final List<String> boxCodeList = Arrays.asList(boxCodes.split(","));


        return new AbstractInvoicesPrintData() {

            @Override
            public int getTemplateType(UserInvoicesTemplate userInvoicesTemplate) {
                return userInvoicesTemplate.getTemplateType();
            }

            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryBoxs4PrintByBoxCodes(staff, boxCodeList);
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getPrintData(param, staff, boxCodes, onlyShowPrinterList, mandatory, 2, EnumInvoicesType.BOXCODE, EnumTemplateKind.BOXCODE, "商家箱码单",
                userInvoicesTemplateService, printAdapterService, printConfigService, source);
    }

    @RequestMapping(value = "/print/boxLabel/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printBoxLabelData(Long templateId, String boxLabels, Boolean onlyShowPrinterList, Boolean isPrint, String api_name) throws SessionException {
        Assert.hasText(boxLabels, "请传入箱唛");
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        final Staff staff = getStaff();
        final String[] boxLabelArray = Strings.getAsStringArray(boxLabels, ",", true);

        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.BOXLABEL);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.BOXLABEL);
        }
        Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板" + templateId);
        JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList) {
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.BOXLABEL, true);
            result.put("invoicesTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        }
        List<BoxLabelPrintVO> boxLabelPrintVOS = printBoxLabelService.getFieldValues(staff, boxLabelArray, isPrint, userInvoicesTemplate);
        result.put("fieldValues", boxLabelPrintVOS);
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 获取货位的打印数据
     *
     * @param goodsSectionIds
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/goodsSection/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printGoodsSectionData(String goodsSectionIds, Boolean onlyShowPrinterList, Boolean mandatory, Long templateId, String api_name, String source) throws Exception {
        final List<Long> goodsSectionIdList = PtStringUtils.toList(goodsSectionIds, Long.class);
        Assert.notEmpty(goodsSectionIdList, "请传入货位id");
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        final Staff staff = getStaff();

        return new AbstractInvoicesPrintData() {
            @Override
            protected Object getTargetData() {
                return printPageSearchService.queryGoodsSections(staff, goodsSectionIdList);
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getPrintData(param, staff, goodsSectionIds, onlyShowPrinterList, mandatory, 1, EnumInvoicesType.GOODSSECTION, EnumTemplateKind.GOODSSECTION, "商家货位单",
                userInvoicesTemplateService, printAdapterService, printConfigService, source);
    }

    /**
     * 获取 分拣货位打印数据
     *
     * @param params 分拣货位查询参数
     */
    @RequestMapping(value = "/print/warehousePosition/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printWarehousePosition(WarehousePositionConfigParams params, Boolean onlyShowPrinterList, Boolean mandatory, Long templateId, String api_name, String source) throws Exception {
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        final Staff staff = getStaff();
        return new AbstractInvoicesPrintData() {
            @Override
            protected Object getTargetData() {
                //查询分拣数据 转为货位数据去获取打印数据
                List<WarehousePositionConfig> warehousePositionConfigs = wmsService.queryWarehousePositionConfigList(staff, params);
                List<GoodsSectionVo> goodsSectionVos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(warehousePositionConfigs)) {
                    for (WarehousePositionConfig warehousePositionConfig : warehousePositionConfigs) {
                        GoodsSectionVo goodsSectionVo = new GoodsSectionVo();
                        goodsSectionVos.add(goodsSectionVo);
                        goodsSectionVo.setDisplay(warehousePositionConfig.getPositionNo());
                        goodsSectionVo.setCode(warehousePositionConfig.getPositionNo());
                    }
                }
                return goodsSectionVos;
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }

        }.getPrintData(param, staff, "", onlyShowPrinterList, mandatory, 1, EnumInvoicesType.GOODSSECTION, EnumTemplateKind.GOODSSECTION, "商家货位单",
                userInvoicesTemplateService, printAdapterService, printConfigService, source);
    }

    /**
     * 获取普通面单的打印数据
     *
     * @param sids
     * @param templateId
     * @param mandatory           进行检查sids是否正常
     * @param onlyShowPrinterList 是否只返回打印机列表
     * @param billType            单据类型 null 原有逻辑    1采购退货单
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/express/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printExpressData(String sids, final Long templateId, Boolean mandatory,
                                   final Boolean onlyShowPrinterList, Boolean exceptionPrint, final Integer billType,
                                   String needPrintLog, String api_name, String validateKey) throws Exception {

        Assert.notNull(templateId, "请先选择快递模板");
        Assert.isTrue(templateId != 0L, "请先选择快递模板");
        // 0. 参数检查
        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        final Staff staff = getStaff();
        final List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        Assert.isTrue(trades.size() == sidArray.length, "查询的订单数量和传入的订单数量不一致");
        if (mandatory != null && !mandatory && validateKey != null) {
            return validatePrint(staff, validateKey, trades);
        }
        if (mandatory != null && mandatory) {
            if (exceptionPrint != null && exceptionPrint) {
                TemplateHelper.validateExceptionPrintTrade(staff, trades);
            }
        }
        if ("1".equals(needPrintLog)) {
            //需要记录日志的话 给对应订单记录日志和加系统日志
            processTrace(staff, sidArray, trades, templateId);
            //更新打印次数
            updateTradePrintCount(staff, trades);
        }
        ILockCallback<Object> lockCallback = () -> {
            JSONObject result = new JSONObject();
            try {
                // 1. 获取模板数据
                Boolean lOnlyShowPrinterList = onlyShowPrinterList;
                UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQuery(staff, templateId, false);
                if (userExpressTemplate == null) {
                    logger.warn(LogHelper.buildLog(staff, "当前用户找不到这个模板" + templateId));
                    lOnlyShowPrinterList = true;
                }
                // 2. 获取模板填充数据
                if (Boolean.TRUE.equals(lOnlyShowPrinterList)) {
                    UserDeliverTemplate userDeliverTemplate = userDeliverTemplateService.userDefaultGet(staff);
                    UserPickerTemplate userPickerTemplate = userPickerTemplateService.getDefault(staff);

                    List<UserDeliverTemplate> userDeliverTemplates = userDeliverTemplateService.listUserDeliverTemplates(staff);
                    List<UserPickerTemplate> userPickerTemplates = userPickerTemplateService.listUserPickerTemplates(staff);
                    List<UserTemplateLockPrintModel> templateLockPrintList = TemplateHelper.getUserTemplateLockPrintList(userExpressTemplate,
                            userDeliverTemplate, userPickerTemplate);
                    result.put("templateLockPrintList", templateLockPrintList);
                    result.put("deliverTemplates", TemplateHelper.listUserTemplates(staff, userDeliverTemplates));
                    result.put("pickerTemplates", TemplateHelper.listUserTemplates(staff, userPickerTemplates));
                } else {
                    List<String> needValues = TemplateHelper.getNeedValues(userExpressTemplate.getFieldSettings());
                    IPrintRequest printRequest = new FieldValuesRequest(trades, sidArray, needValues, userExpressTemplate, billType);
                    printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
                    FieldValuesResponse response = printAdapterService.getFieldValues(staff, printRequest);
                    List<Map<String, Object>> fieldValues = response.getResult();
                    WlbExpressHelper.handFilterOrderFromFieldValues(staff, response, result);
                    result.put("template", userExpressTemplate);
                    result.put("fieldValues", fieldValues);
                    result.put("filterItemSids", response.getFilterItemSids());
                    result.put("filterTradeSids", response.getFilterTradeSids());
                }
                return result;
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("打印普通面单抛出异常:").append(Arrays.toString(TradeUtils.toSids(trades))).append(e.getMessage()), e);
                return getErrorReports(staff, trades, "订单普通面单打印异常:" + e.getMessage());
            }
        };
        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArray), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "获取打印数据时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }

    /**
     * 获取电子面单的打印数据
     *
     * @param sids                订单id串
     * @param templateId          模板id
     * @param mandatory           进行检查sids是否正常
     * @param onlyShowPrinterList 是否只返回打印机列表
     * @param billType            单据类型 null 原有逻辑    1采购退货单
     * @param needPrintLog        是否需要记录日志 1 需要 0 或没有不需要
     * @throws Exception
     */
    @RequestMapping(value = "/print/wlb/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printWlbData(String sids, Long templateId, Boolean mandatory, Boolean onlyShowPrinterList,
                               Boolean exceptionPrint, Integer billType, String needPrintLog, String api_name, String validateKey) throws Exception {
        Assert.notNull(templateId, "请输入templateId参数");
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Staff staff = getStaff();
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        if (mandatory != null && !mandatory && validateKey != null) {
            return validatePrint(staff, validateKey, trades);
        }
        if (mandatory != null && mandatory) {
            if (exceptionPrint != null && exceptionPrint) {
                TemplateHelper.validateExceptionPrintTrade(staff, trades);
            }
        }
        if ("1".equals(needPrintLog)) {
            //需要记录日志的话 给对应订单记录日志和加系统日志
            processTrace(staff, sidArray, trades, templateId);
            //更新打印次数
            updateTradePrintCount(staff, trades);
            // 记录回收池打印次数
            SendWayBillEcUtils.buildAndSend(staff, this,
                    new PrintPathParam(trades),
                    null,
                    PREVIEW_PRINT,
                    eventCenter);
        }
        return printDataShowService.getWlbData(staff, templateId, onlyShowPrinterList, billType, sidArray);
    }

    /**
     * 获取放心购云模板打印数据
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/print/cloud/fxg/data", method = RequestMethod.POST)
    public Object getFxgCloudData(@Valid FxgPrintDataParam printDataParam, WavePrintParam wavePrintParam, String api_name, Boolean isHotSaleItemPrint) throws Exception {
        Long[] sidArray = ArrayUtils.toLongArray(printDataParam.getSids());
        Staff staff = getStaff();
        logger.info("printBatchId:" + printDataParam.getPrintBatchId() + " 开始预览/打印放心购数据:" + sidArray);
        // 获取模板数据
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, printDataParam.getTemplateId(), false);

        // 波次打印
        if (!java.util.Objects.equals(printDataParam.getIsPreview(), true)) {
            List<WaveTrace> waveTraces = buildWaveTrace(staff, sidArray, wavePrintParam);
            printDataParam.setSids(filterSidsForWave(staff, printDataParam.getSids(), wavePrintParam.getWaveId()));
            printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), sidArray);
            waveTraceService.batchAddWaveTrace(staff, waveTraces);
        }

        // 云模板url为空
        if (!FxgXsdExpressTemplateUtil.isFxgCloudTemplate(userWlbExpressTemplate)) {
            return printWlbData(printDataParam.getSids(), printDataParam.getTemplateId(), printDataParam.getMandatory(),
                    printDataParam.getOnlyShowPrinterList(), printDataParam.getExceptionPrint(),
                    printDataParam.getBillType(), String.valueOf(printDataParam.getNeedPrintLog()), api_name, null);
        }

        // 获取打印数据
        Object printData = printDataShowService.getWlbData(staff, printDataParam.getTemplateId(),
                printDataParam.getOnlyShowPrinterList(), printDataParam.getBillType(), sidArray);

        // 获取打印数据异常 || 若为预览操作，直接返回
        if (!(printData instanceof JSONObject) || java.util.Objects.equals(printDataParam.getIsPreview(), true)) {
            return printData;
        }
        //踢出过滤的订单
        try {
            printDataParam.setSids(filterCloseSids((JSONObject) printData, printDataParam.getSids()));
        } catch (Exception e) {
            return printData;
        }
        // 记录日志
        makeSystemFilterLog(staff, (JSONObject) printData, printDataParam.getSids());
        Map<Long, String> batchNoMap = new HashMap<>();
        wavePrintParam.setBatchNoMap(batchNoMap);
        printWlbEnd(printDataParam.getSids(), printDataParam.getPrintBatchId(), printDataParam.getPrinter(),
                wavePrintParam, printDataParam.getExceptionPrint(), null, api_name, isHotSaleItemPrint);
        // 记录打印日志后,添加批次号字段
        JSONObject jsonPrintData = (JSONObject) printData;
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
            String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
            List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
            fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
        }

        if ("1".equals(printDataParam.getRepeatPrint())) {
            //提示后还重新打印
            logger.debug(LogHelper.buildLog(staff, "订单号:" + printDataParam.getSids() + "非第一次打印提示后重新打印!"));
            TradeRepeatPrintTraceParams params = new TradeRepeatPrintTraceParams();
            params.setStaff(staff);
            params.setSids(ArrayUtils.toLongList(printDataParam.getSids()));
            eventCenter.fireEvent(this, new EventInfo("trade.repeat.print.log").setArgs(new Object[]{params}), null);
        }
        return printData;
    }

    /**
     * 获取电子面单的打印数据（自定义打印）
     *
     * @param sids
     * @param templateId
     * @param mandatory           进行检查sids是否正常
     * @param onlyShowPrinterList 是否只返回打印机列
     * @param billType            其他单据调用快递模板打印时，单据的类型
     * @param needPrintLog        是否需要记录日志 1 需要 0 或没有不需要
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/wlb/data/custom", method = RequestMethod.POST)
    @ResponseBody
    public Object printWlbDataCustom(String sids, Long templateId, Boolean mandatory, Boolean onlyShowPrinterList,
                                     Integer billType, String needPrintLog, String api_name, String validateKey) throws Exception {
        return printWlbData(sids, templateId, mandatory, onlyShowPrinterList, null, billType, needPrintLog, api_name, validateKey);
    }

    /**
     * 获取云打印电子面单的打印数据
     *
     * @param sids
     * @param templateId
     * @param mandatory           进行检查sids是否正常
     * @param onlyShowPrinterList 是否只返回打印机列表billType
     * @param billType            单据类型  null 原有   1采购退货单
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/cloud/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printCloudData(String sids, Long templateId, Boolean mandatory, Boolean onlyShowPrinterList, Boolean exceptionPrint, Integer billType, String needPrintLog, String api_name, String validateKey, Boolean isFxgBicTrade, Integer entrance) throws Exception {
        isFxgBicTrade = isFxgBicTrade != null && isFxgBicTrade;
        String printType = "";
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Staff staff = getStaff();
        if (billType == null && entrance != null) {
            billType = RequestEntranceEnum.getByCode(entrance).getBillType();
        }
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        if (mandatory != null && !mandatory && validateKey != null) {
            return validatePrint(staff, validateKey, trades);
        }
        if (mandatory != null && mandatory) {
            if (exceptionPrint != null && exceptionPrint) {
                TemplateHelper.validateExceptionPrintTrade(staff, trades);
            }
        }
        if (validateKey != null && validateKey.equals("validatePrintTradeForDdbd")) {
            processTrace(staff, sidArray, trades, templateId, validateKey);
            printType = "kdbd";
        } else if ("1".equals(needPrintLog)) {
            //需要记录日志的话 给对应订单记录日志和加系统日志
            processTrace(staff, sidArray, trades, templateId);
            //更新打印次数
            updateTradePrintCount(staff, trades);
            // 记录回收池打印次数
            SendWayBillEcUtils.buildAndSend(staff, this,
                    new PrintPathParam(trades),
                    null,
                    PREVIEW_PRINT,
                    eventCenter);
        }
        UserWlbExpressTemplate userWlbExpressTemplate = isFxgBicTrade ? userWlbExpressTemplateService.getBicWlbExpressTemplate(staff, trades) : printDataShowService.getTemplate(staff, templateId, trades);
        return printDataShowService.getCloudDataByTrade(staff, userWlbExpressTemplate, onlyShowPrinterList, billType, printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray)), printType);
    }

    public Object printCloudData(String sids, Long templateId, Boolean mandatory, Boolean onlyShowPrinterList, Boolean exceptionPrint, Integer billType, String needPrintLog, String api_name, String validateKey, Integer entrance) throws Exception {
        return printCloudData(sids, templateId, mandatory, onlyShowPrinterList, exceptionPrint, billType, needPrintLog, api_name, validateKey, false, entrance);
    }

    private void processTrace(Staff staff, Long[] sidArray, List<Trade> trades, Long templateId) {
        processTrace(staff, sidArray, trades, templateId, null);
    }

    private void processTrace(Staff staff, Long[] sidArray, List<Trade> trades, Long templateId, String validateKey) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        UserWlbExpressTemplate userExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateId, false);
        Map<Long, Trade> tradeMap = trades.stream().collect(Collectors.toMap(t -> t.getSid(), t -> t, (k1, k2) -> k2));
        Date matchTime = new Date();
        for (Long sid : sidArray) {
            TradeTrace trace = ConfigLogUtils.createTradeTrace(staff.getUser(), sid, matchTime, "预发货打印,不影响订单状态!", staff);
            if (userExpressTemplate != null) {
                trace.setContent(new StringBuilder(trace.getContent()).append(" 打印模版：").append(userExpressTemplate.getName()).toString());
            }
            Trade trade = tradeMap.get(sid);
            if (trade != null) {
                if (validateKey != null && validateKey.equals("validatePrintTradeForDdbd")) {
                    trace = ConfigLogUtils.createTradeTrace(staff.getUser(), sid, matchTime, "", staff);
                    trace.setAction("快递单补打");
                    trace.setContent(new StringBuilder(" 快递补打：打印模版：").append(trade.getTemplateName()).append(" 快递单号：").append(trade.getOutSid()).toString());
                } else {
                    trace.setContent(new StringBuilder(trace.getContent()).append(" 快递单号：").append(trade.getOutSid()).append("；第").append(trade.getPrintCount() == null ? 1 : (trade.getPrintCount() + 1)).append("次打印").toString());
                }
            }
            tradeTraces.add(trace);
        }
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        opLogService.record(staff, ConfigLogUtils.buildOpLog(staff, IpUtils.getClientIP(request), Domain.PRINT,
                "预发货打印", "预发货打印,不影响订单状态", JSONObject.toJSONString(sidArray)));
    }

    @RequestMapping(value = "/print/jdlop/data", method = RequestMethod.POST)
    @ResponseBody
    public Object getJdlopPrintData(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                    Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint,
                                    Boolean isPreview, Integer printCountNum, String validateKey, String repeatPrint, String api_name, String waybillPathName) throws Exception {
        return getJdlopPrintData(sids, templateId, printBatchId, printer, wavePrintParam, mandatory, onlyShowPrinterList, billType, exceptionPrint, isPreview, printCountNum, validateKey, repeatPrint, api_name, false, waybillPathName);
    }


    public Object getJdlopPrintData(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                    Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint,
                                    Boolean isPreview, Integer printCountNum, String validateKey, String repeatPrint, String api_name, Boolean isHotSaleItemPrint, String waybillPathName) throws Exception {
        Staff staff = getStaff();
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        if (mandatory != null && !mandatory && validateKey != null) {
            return validatePrint(staff, validateKey, trades);
        }
        if (mandatory != null && mandatory) {
            if (exceptionPrint != null && exceptionPrint) {
                TemplateHelper.validateExceptionPrintTrade(staff, trades);
            }
        }
        List<WaveTrace> waveTraces = buildWaveTrace(staff, ArrayUtils.toLongArray(sids), wavePrintParam);

        sids = filterSidsForWave(staff, sids, wavePrintParam.getWaveId());
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), ArrayUtils.toLongArray(sids));
        waveTraceService.batchAddWaveTrace(staff, waveTraces);

        Object printData = printDataShowService.getJdlopPrintData(getStaff(), templateId, onlyShowPrinterList, billType, trades);

        if (isPreview == null || isPreview || !(printData instanceof JSONObject)) {
            return printData;
        }

        // 记录运单生命周期打印次数
        SendWayBillEcUtils.buildAndSend(staff, this,
                new PrintPathParam(tradeSearchService.queryBySids(staff, false, ArrayUtils.toLongArray(sids))),
                null,
                StringUtils.isEmpty(waybillPathName) ? SINGLE_OR_BATCH_PRINT : PrintPathEnum.getEnumByName(waybillPathName),
                eventCenter);

        PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.JDLOP, null, sids, staff.getCompanyId(), staff.getId());
        logger.info(printLog);

        if (billType == null) {
            sids = filterCloseSids((JSONObject) printData, sids);
            makeSystemFilterLog(staff, (JSONObject) printData, sids);
            Map<Long, String> batchNoMap = new HashMap<>();
            wavePrintParam.setBatchNoMap(batchNoMap);
            printWlbEnd(sids, printBatchId, printer, wavePrintParam, exceptionPrint, null, api_name, isHotSaleItemPrint);
            // 记录打印日志后,添加批次号字段
            JSONObject jsonPrintData = (JSONObject) printData;
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
                String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
                List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
                fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
            }
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "express", staff);
        }
        if ("1".equals(repeatPrint)) {
            //提示后还重新打印
            logger.debug(LogHelper.buildLog(staff, "订单号:" + sids + "非第一次打印提示后重新打印!"));
            TradeRepeatPrintTraceParams params = new TradeRepeatPrintTraceParams();
            params.setStaff(staff);
            params.setSids(ArrayUtils.toLongList(sids));
            eventCenter.fireEvent(this, new EventInfo("trade.repeat.print.log").setArgs(new Object[]{params}), null);
        }
        dataOperation(staff, request);
        //通过参数控制打印返回给前端的数据日志
        TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);//通过参数控制打印返回给前端的数据日志
        PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, printCountNum);
        return printData;
    }

    /**
     * 获取发货单的打印数据并设置为已打印
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/deliver/data/batch/printer", method = RequestMethod.POST)
    @ResponseBody
    public Object printDeliverDataBatchEnd(TradeControllerParams queryParams, Long templateId, Integer printNums, String printBatchId, String printer,
                                           Long waveId, Boolean mandatory, Integer forcePrint, String api_name) throws Exception {
        Staff staff = getStaff();
        List<Trade> tradeList;
        String sid = queryParams.getSid();
        if (StringUtils.isBlank(sid)) {
            Page page = new Page();
            //暂时写死配置为100000，solr查询时，仍会限制，过大会抛出异常
            page.setPageNo(1).setPageSize(100000);
            TradeQueryParams params = convertParams(queryParams, page, null);
            params.setQueryLargeResult(true);
            params.setQueryFlag(1);
            Trades trades = tradeSearchService.search(staff, params);
            tradeList = trades.getList();
        } else {
            tradeList = printPageSearchService.queryBySids(staff, Arrays.asList(ArrayUtils.toLongArray(sid)));
        }
        if (tradeList == null || tradeList.isEmpty()) {
            throw new IllegalArgumentException("未查询到任何订单或订单数量太大！");
        }
        List<String> sidList = new ArrayList<String>();
        for (Trade trade : tradeList) {
            sidList.add(String.valueOf(trade.getSid()));
        }
        return printDeliverDataEnd(Strings.join(",", sidList), templateId, printNums, printBatchId, printer,
                waveId, mandatory, null, null, api_name, null, null, forcePrint);
    }


    /**
     * 批量打印发货单
     */
    @RequestMapping(value = "/print/deliver/data/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object printDeliverDataBatch(TradeControllerParams queryParams, Long templateId, String api_name) throws Exception {
        Staff staff = getStaff();
        List<Trade> tradeList;
        String sid = queryParams.getSid();
        if (StringUtils.isBlank(sid)) {
            Page page = new Page();
            //暂时写死配置为100000，solr查询时，仍会限制，过大会抛出异常
            page.setPageNo(1).setPageSize(100000);
            TradeQueryParams params = convertParams(queryParams, page, null);
            params.setQueryLargeResult(true);
            params.setQueryFlag(1);
            Trades trades = tradeSearchService.search(staff, params);
            tradeList = trades.getList();
        } else {
            tradeList = printPageSearchService.queryBySids(staff, Arrays.asList(ArrayUtils.toLongArray(sid)));
        }
        if (tradeList == null || tradeList.isEmpty()) {
            throw new IllegalArgumentException("未查询到任何订单或订单数量太大！");
        }
        try {
            UserDeliverTemplate userDeliverTemplate = null;
            if (templateId != null) {
                userDeliverTemplate = userDeliverTemplateService.userQuery(staff, templateId);
            } else {
                userDeliverTemplate = userDeliverTemplateService.userDefaultGet(staff);
            }
            if (userDeliverTemplate == null) throw new IllegalArgumentException("当前用户找不到这个模板" + templateId);
            List<String> needValues = TemplateHelper.getNeedValues(userDeliverTemplate.getFields());
            IPrintRequest printRequest = new FieldValuesRequest(tradeList, TradeUtils.toSids(tradeList), needValues, userDeliverTemplate, null);
            printRequest.setTemplateKind(EnumTemplateKind.DELIVER.getValue());
            FieldValuesResponse response = printAdapterService.getFieldValues(staff, printRequest);
            List<Map<String, Object>> fieldValues = response.getResult();
            JSONObject result = new JSONObject();
            WlbExpressHelper.handFilterOrderFromFieldValues(staff, response, result);
            result.put("fieldValues", fieldValues);
            result.put("template", userDeliverTemplate);
            result.put("filterItemSids", response.getFilterItemSids());
            result.put("filterTradeSids", response.getFilterTradeSids());
            return result;
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("打印发货单抛出异常:").append(TradeUtils.toSids(tradeList)).append(e.getMessage()), e);
            return getErrorReports(staff, tradeList, "订单发货单打印异常:" + e.getMessage());
        }
    }


    /**
     * 获取发货单的打印数据
     *
     * @param sids
     * @param templateId
     * @param mandatory
     * @param api_name
     * @param billType   数据源类型  null 数据库查询
     * @param forcePrint 是否强制打印发货单，如果forcePrint=1，订单补打时不会过滤其他平台发货子订单
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/deliver/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printDeliverData(String sids, Long templateId, Integer printNums, Boolean mandatory, Integer billType, Integer forcePrint, String api_name) throws Exception {

        Long[] sidArray = ArrayUtils.toLongArray(sids);
        final Staff staff = getStaff();
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        if (mandatory != null && !mandatory || CollectionUtils.isEmpty(trades)) {
            return successResponse();
        }
        try {
            UserDeliverTemplate userDeliverTemplate = null;
            if (templateId != null) {
                userDeliverTemplate = userDeliverTemplateService.userQuery(staff, templateId);
            } else {
                userDeliverTemplate = userDeliverTemplateService.userDefaultGet(staff);
            }
            if (userDeliverTemplate == null) {
                throw new IllegalArgumentException("当前用户找不到这个模板" + templateId);
            }
            List<String> needValues = TemplateHelper.getNeedValues(userDeliverTemplate.getFields());

            IPrintRequest printRequest = new FieldValuesRequest(trades, sidArray, needValues, userDeliverTemplate, billType, forcePrint, printNums);
            printRequest.setTemplateKind(EnumTemplateKind.DELIVER.getValue());
            FieldValuesResponse response = printAdapterService.getFieldValues(staff, printRequest);
            List<Map<String, Object>> fieldValues = response.getResult();

            JSONObject result = new JSONObject();
            WlbExpressHelper.handFilterOrderFromFieldValues(staff, response, result);
            result.put("fieldValues", fieldValues);
            result.put("template", userDeliverTemplate);
            result.put("filterItemSids", response.getFilterItemSids());
            result.put("filterTradeSids", response.getFilterTradeSids());
            return result;
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("打印发货单抛出异常:").append(TradeUtils.toSids(trades)).append(e.getMessage()), e);
            return getErrorReports(staff, trades, "订单发货单打印异常:" + e.getMessage());
        }
    }

    /**
     * 获取发货单的打印数据（自定义打印）
     *
     * @param sids
     * @param templateId
     * @param mandatory
     * @param forcePrint 是否强制打印发货单，如果forcePrint=1，订单补打时不会过滤其他平台发货子订单
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/deliver/data/custom", method = RequestMethod.POST)
    @ResponseBody
    public Object printDeliverDataCustom(String sids, Long templateId, Integer printNums, Boolean mandatory, Integer billType, Integer forcePrint, String api_name) throws
            Exception {
        return printDeliverData(sids, templateId, printNums, mandatory, billType, forcePrint, api_name);
    }

    /**
     * 获取大量拣货单的打印数据
     * 波次打印页面
     *
     * @param sids       单据id
     * @param templateId 模板id
     * @param mandatory  是否只校验
     * @param billType   数据源类型  null 数据库查询   1 采购退货单提供
     */
    @RequestMapping(value = "/print/picker/large", method = RequestMethod.POST)
    @ResponseBody
    public Object printLargePickerData(Long waveId, String sids, Long templateId, Boolean mandatory, Integer needSaveLog, Integer billType, String api_name) throws Exception {
        needSaveLog = null != needSaveLog ? needSaveLog : 0;
        return printLargePickerData(waveId, sids, templateId, mandatory, billType, "", null, needSaveLog, api_name, true);
    }

    /**
     * 获取大量拣货单的打印数据
     *
     * @param sids                  单据id
     * @param templateId            模板id
     * @param mandatory             是否只校验
     * @param billType              数据源类型  null 数据库查询   1 采购退货单提供
     * @param pickerPrintLogDetails 打印日志详情 不是分波次打印的时候必须传null
     */
    private Object printLargePickerData(Long waveId, String sids, Long templateId, Boolean mandatory, Integer billType, String printerName,
                                        List<PickerPrintLogDetail> pickerPrintLogDetails, Integer needSaveLog, String api_name, Boolean isWaveGroup) throws Exception {

        Long[] sidArray = ArrayUtils.toLongArray(sids);
        final Staff staff = getStaff();
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray, waveId));
        Assert.isTrue(CollectionUtils.isNotEmpty(trades), "未获取到订单!");
        //填充分销商名称
        printHelperService.buildSourceName(staff, trades);
        //填充店铺名称
        printHelperService.buildShopName(staff, trades);
        Long[] waveIds = trades.stream().map(Trade::getWaveId).toArray(Long[]::new);
        List<Wave> waves = printPageSearchService.queryWaveByIds(staff, Arrays.asList(waveIds));
        //默认波次号升序排序
        Collections.sort(waves, new Comparator<Wave>() {
            @Override
            public int compare(Wave o1, Wave o2) {
                return o1.getCode().compareTo(o2.getCode());
            }
        });

//        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sidArray);
//        去除对拣货单打印时订单的校验，统一放到弹框中进行（弹框入口有普通快递、菜鸟电子、云打印）
//        TemplateHelper.validatePrintTrade(staff, trades);
        if (mandatory != null && !mandatory) {
            return successResponse();
        }

        UserPickerTemplate userPickerTemplate;
        if (templateId != null) {
            userPickerTemplate = userPickerTemplateService.userQuery(staff, templateId);
        } else {
            userPickerTemplate = userPickerTemplateService.getDefault(staff);
        }
        if (userPickerTemplate == null) {
            throw new IllegalArgumentException("当前用户找不到拣货单模板");
        }

        List<String> needValues = TemplateHelper.getNeedValues(userPickerTemplate.getFields());

        printHelperService.allocateGoodsWhenPrint(staff, waveId, sidArray);
        //打印详情
        if (pickerPrintLogDetails == null) {
            pickerPrintLogDetails = new ArrayList<>();
        }
        FieldValuesResponse response;
        if (userPickerTemplate.getTemplateType() != null && (userPickerTemplate.getTemplateType() == 1
                || userPickerTemplate.getTemplateType() == 4
                || userPickerTemplate.getTemplateType() == 5)) {
            Boolean openWms = getStaff().getConf().isOpenWms();
            Assert.isTrue(Boolean.TRUE.equals(openWms), "当前未开启仓储功能，无法使用“货位版拣货单”模板打印");

            //货位版
            response = printLargePickerVipService.getFieldValues(staff, trades, needValues, userPickerTemplate, billType, waves, pickerPrintLogDetails, isWaveGroup);
        } else {
            //商品版
            response = printLargePickerService.getFieldValues(staff, trades, needValues, userPickerTemplate, billType, waves, pickerPrintLogDetails, isWaveGroup);
        }
        if (needSaveLog.equals(1)) {
            if ("1".equals(cache.get(PICKER_ASYN_SAVE_CACHE_KEY))) {
                pickerPrintLogService.addPickerPrintLog(staff, userPickerTemplate, printerName, trades.get(0).getWarehouseId(), (long) waves.size(),
                        pickerPrintLogDetails);
            } else {
                pickerPrintLogService.asynAddPickerPrintLog(staff, userPickerTemplate, printerName, trades.get(0).getWarehouseId(), (long) waves.size(),
                        pickerPrintLogDetails);
            }
            addWaveTraceAndOplog(staff, waves);
        }
        JSONObject result = new JSONObject();
        WlbExpressHelper.handFilterOrderFromFieldValues(staff, response, result);
        result.put("fieldValues", response.getResult());
        result.put("template", userPickerTemplate);
        result.put("filterItemSids", response.getFilterItemSids());
        result.put("filterTradeSids", response.getFilterTradeSids());
        return result;
    }

    /**
     * 记录波次日志和系统日志
     *
     * @param staff
     * @param waves
     */
    void addWaveTraceAndOplog(Staff staff, List<Wave> waves) {
        // 波次号去重
        Set<Long> waveIdSet = waves.stream().filter(wave -> wave.getId() != null && wave.getId() > 0).map(Wave::getId).collect(Collectors.toSet());
        if (waveIdSet.isEmpty()) {
            return;
        }

        List<WaveTrace> waveTraces = new ArrayList<>(waveIdSet.size());
        ArrayList<OpLog> opLogs = new ArrayList<>(waveIdSet.size());
        for (Long waveId : waveIdSet) {
            //记录波次日志
            waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PRINT_PICKER_DATA, "拣货单打印"));
            //记录系统日志
            opLogs.add(ConfigLogUtils.buildOpLog(staff, IpUtils.getClientIP(request), Domain.TRADE,
                    "printLargePickerData", "拣货单打印，波次号：" + waveId, null));
        }
        waves.forEach(w -> w.setPickListPrintStatus(CommonConstants.ENABLE_STATUS_NORMARL));
        tradeWaveService.updateWavePickListPrintStatus(staff, waves);
        waveTraceService.batchAddWaveTrace(staff, waveTraces);
        opLogService.batchRecord(staff, opLogs);
    }

    public Object printLargePickerDataWaveGroup(String sids, Long templateId, Boolean mandatory, Integer billType, String printerName, Integer needSaveLog, String api_name) throws Exception {
        final Staff staff = getStaff();
        List list = new ArrayList();
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        Map<Long, List<Trade>> map = trades.stream().collect(Collectors.groupingBy(Trade::getWaveId));
        List<PickerPrintLogDetail> pickerPrintLogDetails = new ArrayList<>();
        for (Long waveId : map.keySet()) {
            List<Trade> tradeList = map.get(waveId);
            if (tradeList != null && tradeList.size() > 0) {
                List<Long> sidList = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
                //分波次打印 单次不记录日志
                List<PickerPrintLogDetail> wavePickerPrintLogDetails = new ArrayList<>();
                list.add(printLargePickerData(null, StringUtils.join(sidList, ","), templateId, mandatory, billType, printerName, wavePickerPrintLogDetails,
                        0, api_name, true));
                pickerPrintLogDetails.addAll(wavePickerPrintLogDetails);
            }
        }
        if (needSaveLog.equals(1)) {
            UserPickerTemplate userPickerTemplate;
            if (templateId != null) {
                userPickerTemplate = userPickerTemplateService.userQuery(staff, templateId);
            } else {
                userPickerTemplate = userPickerTemplateService.getDefault(staff);
            }
            if ("1".equals(cache.get(PICKER_ASYN_SAVE_CACHE_KEY))) {
                pickerPrintLogService.addPickerPrintLog(staff, userPickerTemplate, printerName, trades.get(0).getWarehouseId(), (long) map.size(),
                        pickerPrintLogDetails);
            } else {
                pickerPrintLogService.asynAddPickerPrintLog(staff, userPickerTemplate, printerName, trades.get(0).getWarehouseId(), (long) map.size(),
                        pickerPrintLogDetails);
            }

        }
        return list;
    }

    /**
     * 获取大量拣货单的打印数据（自定义打印）
     * 订单打印页面
     *
     * @param sids        sids
     * @param templateId  模板id
     * @param mandatory   是否校验数据
     * @param billType    数据来源
     * @param isWaveGroup 是否波次分组
     * @param printerName 打印机名称
     * @param needSaveLog 是否需要记录日志 0不需要 1 需要
     */
    @RequestMapping(value = "/print/picker/large/custom", method = RequestMethod.POST)
    @ResponseBody
    public Object printLargePickerDataCustom(Long waveId, Boolean isWaveGroup, String sids, Long templateId, Boolean mandatory, Integer billType, String printerName,
                                             Integer needSaveLog, String api_name) throws Exception {
        if (needSaveLog == null || billType != null) {
            //默认不记录日志 非订单打印不记录日志
            needSaveLog = 0;
        }
        if (isWaveGroup != null && isWaveGroup)
            return printLargePickerDataWaveGroup(sids, templateId, mandatory, billType, printerName, needSaveLog, api_name);
        return printLargePickerData(waveId, sids, templateId, mandatory, billType, printerName, null, needSaveLog, api_name, false);
    }

    /**
     * 获取拣货单数据（大量）
     *
     * @return
     * @throws com.raycloud.dmj.session.SessionException 已过期，请使用{@link com.raycloud.dmj.controller.getter.GetterPrintController#getGetterData(com.raycloud.dmj.getter.GetterPrintDataQueryParams, java.lang.String)}
     */
    @RequestMapping(value = "/print/getter/large", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Object getGetterData(TradeControllerParams queryParams, Long templateId, String api_name) throws SessionException {
        Staff staff = getStaff();

        List<Trade> tradeList;
        String sid = queryParams.getSid();

        if (StringUtils.isBlank(sid)) {
            Page page = new Page();
            //暂时写死配置为100000，solr查询时，仍会限制，过大会抛出异常
            page.setPageNo(1).setPageSize(100000);
            TradeQueryParams params = convertParams(queryParams, page, null);
            params.setQueryLargeResult(true);
            params.setQueryFlag(1);
            Trades trades = tradeSearchService.search(staff, params);
            tradeList = trades.getList();
            if (tradeList == null || tradeList.isEmpty()) {
                throw new IllegalArgumentException("未查询到任何订单或订单数量太大！");
            }
        } else {
            tradeList = printPageSearchService.queryBySids(staff, Arrays.asList(ArrayUtils.toLongArray(sid)));
            tradeList = tradeList.stream().filter(t -> !(t.getIsCancel() != null && t.getIsCancel() == 1)).collect(Collectors.toList());
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeStatusUtils.validSysStatus(tradeList, tradeConfig, new String[]{Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_WAIT_MANUAL_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED}, "拿货单打印");
//        Long templateId = null;

        //检查是否有未匹配的商品
        Collection<Long> unAllocatedSidList = PtTradeAndOrderUtils.checkOrderStatus(tradeList);
        if (!CollectionUtils.isEmpty(unAllocatedSidList)) {
            JSONObject result = new JSONObject();
            result.put("isSuccess", Boolean.FALSE);
            result.put("sidList", unAllocatedSidList);
            return result;
        }

        UserGetterTemplate userGetterTemplate = null;
        if (templateId != null) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, templateId);
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 1);
        }
        Assert.notNull(userGetterTemplate, "当前用户找不到拿货单模板");

        UserInvoicesTemplate userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.MERCHANTCODE);
        Assert.notNull(userInvoicesTemplate, "当前用户找不到编码打印模板");
        Map<String, Object> msgMap = Maps.newHashMap();
        Long warehouseId = null;
        String warehouseIds = queryParams.getWarehouseId();
        if (StringUtils.isNotBlank(warehouseIds)) {
            warehouseId = Long.valueOf(warehouseIds.split(",")[0]);
        }
        List<GetterSheetEntry> sheetEntries = printLargeGetterService.getFieldValues(staff, tradeList, userGetterTemplate, msgMap, warehouseId);
        JSONObject result = new JSONObject();
        for (Map.Entry<String, Object> entry : msgMap.entrySet()) {
            result.put(entry.getKey(), entry.getValue());
        }
        result.put("isSuccess", Boolean.TRUE);
        result.put("sheetEntries", sheetEntries);
        result.put("template", userGetterTemplate);
        result.put("merchantCodeTemplate", userInvoicesTemplate);

        UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.MERCHANTCODE, true);
        result.put("merchantCodeTemplates", TemplateHelper.getUserInvoicesTemplateList(staff, userInvoicesTemplates));
        UserGetterTemplates userGetterTemplates = userGetterTemplateService.getActivationTemplateList(staff, 1);
        result.put("userGetterTemplates", TemplateHelper.getUserGetterTemplateList(staff, userGetterTemplates));
        List<UserDeliverTemplate> userDeliverTemplates = userDeliverTemplateService.listUserDeliverTemplates(staff);
        result.put("deliverTemplates", TemplateHelper.listUserTemplates(staff, userDeliverTemplates));
        return result;
    }

    /**
     * 获取装箱清单数据
     *
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/boxingList/data", method = RequestMethod.GET)
    @ResponseBody
    public Object getBoxingListData(String api_name, Long sid, String boxNos, Integer dataSource) throws SessionException {
        if (dataSource == null || dataSource == 0) {
            if (null == sid) {
                throw new IllegalArgumentException("请输入sid参数");
            }
        }
        List<String> boxNoList = null;
        if (StringUtils.isNotBlank(boxNos)) {
            boxNoList = new ArrayList<String>();
            for (String boxNo : StringUtils.split(boxNos, ",")) {
                if (StringUtils.isNotBlank(boxNo)) {
                    boxNoList.add(StringUtils.trim(boxNo));
                }
            }
        }
        Staff staff = getStaff();
        return printBoxingListData(staff, sid, boxNoList, dataSource);
    }

    /**
     * 获取装箱清单打印数据
     *
     * @param boxCodes 箱码
     * @param api_name api_name
     */
    @RequestMapping(value = "/box/getPrintData")
    @ResponseBody
    public Object getBoxPrintData(String boxCodes, String templateId, String api_name, String mergePrint) throws SessionException {
        Assert.isTrue(StringUtils.isNotBlank(boxCodes), "箱码不能为空,请检查参数!");
        Staff staff = getStaff();
        UserGetterTemplate userGetterTemplate;
        if (StringUtils.isNotBlank(templateId)) {
            userGetterTemplate = userGetterTemplateService.userQuery(staff, Long.valueOf(templateId));
        } else {
            userGetterTemplate = userGetterTemplateService.getDefault(staff, 0);
        }
        if (userGetterTemplate == null) {
            throw new IllegalArgumentException("当前用户找不到装箱清单模板");
        }
        List<String> boxCodeList = Arrays.asList(boxCodes.split(","));
        List<Map<String, Object>> fieldValues = boxingListPrintService.getFieldValues(staff, userGetterTemplate, boxCodeList, mergePrint);
        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("fieldValues", fieldValues);
        result.put("template", userGetterTemplate);
        List<UserTemplateLockPrintModel> templateLockPrintList = TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate);
        result.put("templateLockPrintList", templateLockPrintList);
        return result;
    }

    /**
     * 获取装箱清单数据
     *
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/boxingList/data/print", method = RequestMethod.POST)
    @ResponseBody
    public Object printBoxingListData(String api_name, String boxNos, Long sid, Integer dataSource) throws SessionException {
        if (dataSource == null || dataSource == 0) {
            if (null == sid) {
                throw new IllegalArgumentException("请输入sid参数");
            }
        }
        List<String> boxNoList = null;
        if (StringUtils.isNotBlank(boxNos)) {
            boxNoList = new ArrayList<String>();
            for (String boxNo : StringUtils.split(boxNos, ",")) {
                if (StringUtils.isNotBlank(boxNo)) {
                    boxNoList.add(StringUtils.trim(boxNo));
                }
            }
        }
        Staff staff = getStaff();
        Object data = printBoxingListData(staff, sid, boxNoList, dataSource);
        if (dataSource == null || dataSource == 0) {
            setBoxingListPrinted(staff, sid, boxNoList);
        }
        return data;
    }

    /**
     * 获取拿货单锁定打印机信息
     *
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/userBoxingListTemplateLockPrintList/data", method = RequestMethod.GET)
    @ResponseBody
    @AccessShield(value = "260", extendsType = false)
    public Object getUserGetterTemplateLockPrintList() throws SessionException {
        Staff staff = getStaff();

        UserGetterTemplate userGetterTemplate = userGetterTemplateService.getDefault(staff, 0);

        if (userGetterTemplate == null) {
            throw new IllegalArgumentException("当前用户找不到拿货单模板");
        }

        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);

        List<UserTemplateLockPrintModel> templateLockPrintList = TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate);
        result.put("templateLockPrintList", templateLockPrintList);

        return result;
    }

    /**
     * 手写快递单
     *
     * @param sids
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/print/handwrite/end", method = RequestMethod.POST)
    @ResponseBody
    public Object printHandwriteEnd(String sids, String api_name) throws SessionException {
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        if (org.apache.commons.lang.ArrayUtils.isEmpty(sidArray)) {
            return successResponse();
        }
        Staff staff = getStaff();
        List<Trade> trades = printPageSearchService.queryBySids(staff, Arrays.asList(ArrayUtils.toLongArray(sids)));
        StringBuilder sidsOfTemp0 = new StringBuilder();
        StringBuilder sidsOfTemp1 = new StringBuilder();
        StringBuilder sidsOfTemp2 = new StringBuilder();
        for (Trade trade : trades) {
            Assert.isTrue(trade.getSysStatus().equals(Trade.SYS_STATUS_FINISHED_AUDIT), String.format("订单%s不是已审核状态,不能进行手写快递", trade.getSid()));
            Assert.isTrue(!(trade.getIsExcep() != null && trade.getIsExcep().intValue() == 1), String.format("异常订单%s,不能进行手写快递", trade.getSid()));
            if (!CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource())) {
                Assert.hasText(trade.getOutSid(), new StringBuilder("订单[sid=").append(trade.getSid()).append
                        ("]没有快递单号，不能进行手写快递").toString());
            }
            Integer templateType = trade.getTemplateType();
            if (0 == templateType.intValue()) {
                sidsOfTemp0.append(trade.getSid()).append(",");
            } else if (1 == templateType.intValue()) {
                sidsOfTemp1.append(trade.getSid()).append(",");
            } else if (2 == templateType.intValue()) {
                sidsOfTemp2.append(trade.getSid()).append(",");
            }
        }
        printHelperService.allocateGoodsWhenPrint(staff, null, sidArray);
        if (sidsOfTemp0.length() > 0) {
            sidsOfTemp0.deleteCharAt(sidsOfTemp0.length() - 1);
            handwriteExpressEnd(sidsOfTemp0.toString(), staff);
        }
        if (sidsOfTemp1.length() > 0) {
            sidsOfTemp1.deleteCharAt(sidsOfTemp1.length() - 1);
            handwriteWlbEnd(sidsOfTemp1.toString(), staff);
        }
        return successResponse();
    }

    /**
     * 获取普通打印数据并设置为已打印
     *
     * @param sids
     * @param templateId
     * @param outSids
     * @param printBatchId
     * @param printer
     * @param waveId
     * @param mandatory
     * @param onlyShowPrinterList
     * @param billType
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/express/data/printer", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "(#exceptionPrint == null || !#exceptionPrint ? '打印快递单' : '异常单打印') + '，系统单号：' + #sids + '，打印机：' + #printer", action = "print_express")
    @ResponseBody
    public Object printExpressDataEnd(String sids, Long templateId, String outSids, String printBatchId, String printer,
                                      Long waveId, Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint, String api_name, String validateKey) throws Exception {

        Staff staff = getStaff();
        printHelperService.allocateGoodsWhenPrint(staff, waveId, ArrayUtils.toLongArray(sids));
        PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.NORMAL, null, sids, staff.getCompanyId(), staff.getId());
        logger.info(printLog);

        Object printData = printExpressData(sids, templateId, mandatory, onlyShowPrinterList, null, billType, null, api_name, validateKey);
        if (!(printData instanceof JSONObject)) {
            return printData;
        }
        if (!((JSONObject) printData).containsKey("fieldValues")) {
            return printData;
        }
/**
 *       retry参数是用来区分重试请求，重试的时候就不执行修改状态和插入打印记录的请求
 *       在后端发生错误时导致了第一次请求报错之后再次重试的时候不执行修改订单状态，插入打印记录等操作
 *       现在改为直接后端在插入打印记录的时候做一个去重处理
 *        if (null != retry && retry){
 *            return printData;
 *        }
 */
        if (billType == null) {
            makeSystemFilterLog(getStaff(), (JSONObject) printData, sids);
            printExpressEnd(sids, outSids, printBatchId, printer, waveId, exceptionPrint, api_name);
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "express", getStaff());
        }
        dataOperation(staff, request);
        //通过参数控制打印返回给前端的数据日志
        TemplateHelper.handleExpressFieldValuesLog(getStaff(), printData, config);//通过参数控制打印返回给前端的数据日志
        return printData;
    }

    /**
     * 快递单普通面单的打印
     *
     * @param sids
     * @param outSids
     * @param printer
     * @param waveId   波次id
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/print/express/end")
    @LogTag(key = "#sids", content = "'打印快递单，系统单号：' + #sids + '，打印机：' + #printer", action = "print_express")
    @ResponseBody
    public Object printExpressEnd(String sids, String outSids, String printBatchId, String printer, Long waveId, Boolean exceptionPrint, String api_name) throws SessionException {

        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");

        Long[] sidArray = ArrayUtils.toLongArray(sids);
        String[] outSidArray = ArrayUtils.toStringArray(outSids);
        Assert.isTrue(outSids == null || outSidArray.length == 0 || sidArray.length == outSidArray.length, "打印的订单数必须和运单号的数量保持一致");
        Staff staff = getStaff();

        EndPrintTimeRequest printRequest = new EndPrintTimeRequest(sidArray, printer, outSidArray, printBatchId, waveId);
        printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
        printRequest.setClientIp(IpUtils.getClientIP(request));
        printRequest.setExceptionPrint(exceptionPrint != null && exceptionPrint);
        printAdapterService.endPrintTime(staff, printRequest);
        return successResponse();
    }

    /**
     * 手写普通面单快递单
     *
     * @param sids
     * @throws com.raycloud.dmj.session.SessionException
     */
    private void handwriteExpressEnd(String sids, Staff staff) throws SessionException {

        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");

        Long[] sidArray = ArrayUtils.toLongArray(sids);

        IPrintRequest printRequest = new EndPrintTimeRequest(sidArray, "手写快递", null);
        printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
        printRequest.setClientIp(IpUtils.getClientIP(request));
        printAdapterService.endPrintTime(staff, printRequest);

        OpLogHelper.recodeOpLog(opLogService, request, staff, "handwriteExpressEnd", sids, "手写普通面单快递单，系统单号：" + sids, null);
    }

    /**
     * 获取菜鸟电子面单打印数据并设置为已打印
     *
     * @param sids
     * @param templateId
     * @param printBatchId
     * @param printer
     * @param mandatory
     * @param onlyShowPrinterList
     * @param wavePrintParam      波次打印参数
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/wlb/data/printer", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "(#exceptionPrint == null || !#exceptionPrint ? '打印快递单' : '异常单打印') + '，系统单号：' + #sids + '，打印机：' + #printer", action = "print_wlb_express")
    @ResponseBody
    public Object printWlbDataEnd(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                  Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint, String api_name,
                                  Integer printCountNum, String validateKey, String repeatPrint, Boolean isHotSaleItemPrint, String waybillPathName) throws Exception {

        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, wavePrintParam.getStaffId());
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(billType, staff, sidArray));
        if (mandatory != null && !mandatory && validateKey != null) {
            return validatePrint(staff, validateKey, trades);
        }
        List<WaveTrace> waveTraces = buildWaveTrace(staff, ArrayUtils.toLongArray(sids), wavePrintParam);

        sids = filterSidsForWave(staff, sids, wavePrintParam.getWaveId());
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), ArrayUtils.toLongArray(sids));
        waveTraceService.batchAddWaveTrace(staff, waveTraces);

        Object printData = printWlbData(sids, templateId, mandatory, onlyShowPrinterList, null, billType, "0", api_name, validateKey);
        PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.WLB, null, sids, staff.getCompanyId(), staff.getId());
        logger.info(printLog);

        if (!(printData instanceof JSONObject)) {
            return printData;
        }
        if (!((JSONObject) printData).containsKey("fieldValues")) {
            return printData;
        }

        // 记录运单生命周期打印次数
        SendWayBillEcUtils.buildAndSend(staff, this,
                new PrintPathParam(tradeSearchService.queryBySids(staff, false, ArrayUtils.toLongArray(sids))),
                null,
                StringUtils.isEmpty(waybillPathName) ? SINGLE_OR_BATCH_PRINT : PrintPathEnum.getEnumByName(waybillPathName),
                eventCenter);
/**
 *       retry参数是用来区分重试请求，重试的时候就不执行修改状态和插入打印记录的请求
 *       在后端发生错误时导致了第一次请求报错之后再次重试的时候不执行修改订单状态，插入打印记录等操作
 *       现在改为直接后端在插入打印记录的时候做一个去重处理
 *        if (null != retry && retry){
 *            return printData;
 *        }
 */
        if (billType == null) {
            sids = filterCloseSids((JSONObject) printData, sids);
            makeSystemFilterLog(staff, (JSONObject) printData, sids);
            Map<Long, String> batchNoMap = new HashMap<>();
            wavePrintParam.setBatchNoMap(batchNoMap);
            printWlbEnd(sids, printBatchId, printer, wavePrintParam, exceptionPrint, wavePrintParam.getStaffId(), api_name, isHotSaleItemPrint);
            // 记录打印日志后,添加批次号字段
            JSONObject jsonPrintData = (JSONObject) printData;
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
                String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
                List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
                fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
            }
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "express", staff);
        }
        if ("1".equals(repeatPrint)) {
            //提示后还重新打印
            logger.debug(LogHelper.buildLog(staff, "订单号:" + sids + "非第一次打印提示后重新打印!"));
            TradeRepeatPrintTraceParams params = new TradeRepeatPrintTraceParams();
            params.setStaff(staff);
            params.setSids(Arrays.asList(sidArray));
            eventCenter.fireEvent(this, new EventInfo("trade.repeat.print.log").setArgs(new Object[]{params}), null);
        }
        dataOperation(staff, request);
        //通过参数控制打印返回给前端的数据日志
        TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);//通过参数控制打印返回给前端的数据日志
        PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, printCountNum);
        return printData;
    }

    /**
     * 过滤sid中已经关闭的订单
     *
     * @param printData
     * @param sids
     */
    public String filterCloseSids(JSONObject printData, String sids) {
        if (printData == null) {
            return sids;
        }
        JSONArray filterTradeSids = printData.getJSONArray("filterTradeSids");
        if (filterTradeSids == null) {
            return sids;
        }
        List<Long> filterSids = filterTradeSids.toJavaList(Long.class);
        List<Long> sidList = ArrayUtils.toLongUniqList(sids);
        sidList.removeAll(filterSids);
        Assert.notEmpty(sidList, "由于订单已发货、已关闭、其他平台发货等原因被过滤");
        return StringUtils.join(sidList, ",");
    }

    public void removefilterSids(JSONObject printData, List<Long> sids) {
        if (printData == null) {
            return;
        }
        JSONArray filterTradeSids = printData.getJSONArray("filterTradeSids");
        if (filterTradeSids == null) {
            return;
        }
        List<Long> filterSids = filterTradeSids.toJavaList(Long.class);
        sids.removeAll(filterSids);
        Assert.notEmpty(sids, "没有可打印的数据");
    }


    /**
     * 包装验货支持快递批打（港仔临时方案，循环业务）
     */
    @RequestMapping(value = "/print/wlb/data/printer/page", method = RequestMethod.POST)
    @ResponseBody
    public Object printWlbDataPage(@RequestBody PrintWlbDataPageBo printWlbDataPageBo) throws SessionException {
        PrintWlbDataPageVo vo = new PrintWlbDataPageVo();
        List<Object> responseList = new ArrayList<>();
        List<Object> errorMsgList = new ArrayList<>();
        Logs.debug("包装验货支持快递批打参数：" + JSONObject.toJSONString(printWlbDataPageBo));
        List<PrintWlbDataPageData> pageDataList = printWlbDataPageBo.getPageDataList();
        Map<String, PrintWlbDataPageData> mergeMap = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        for (PrintWlbDataPageData printWlbDataPageData : pageDataList) {
            String key = printWlbDataPageData.getTemplateId() + "_" + (printWlbDataPageData.getWavePrintParam() != null ? printWlbDataPageData.getWavePrintParam().getWaveId() : "");
            PrintWlbDataPageData pageData = mergeMap.get(key);
            if (pageData == null) {
                pageData = new PrintWlbDataPageData();
                BeanUtils.copyProperties(printWlbDataPageData, pageData);
            } else {
                pageData.setSids(pageData.getSids() + "," + printWlbDataPageData.getSids());
            }
            mergeMap.put(key, pageData);
        }
        for (Map.Entry<String, PrintWlbDataPageData> entry : mergeMap.entrySet()) {
            PrintWlbDataPageData value = entry.getValue();

            try {
                Object o = printWlbDataEnd(value.getSids(),
                        value.getTemplateId(),
                        value.getPrintBatchId(),
                        value.getPrinter(),
                        value.getWavePrintParam(),
                        value.getMandatory(),
                        value.getOnlyShowPrinterList(),
                        value.getBillType(),
                        value.getExceptionPrint(),
                        value.getApi_name(),
                        value.getPrintCountNum(),
                        value.getValidateKey(),
                        null,
                        false,
                        PACKING_PRINT.name());
                if (o instanceof JSONObject) {
                    JSONObject result = (JSONObject) o;
                    Long[] sids = ArrayUtils.toLongArray(value.getSids());
                    for (int i = 0; i < sids.length; i++) {
                        JSONObject newResult = JSONObject.parseObject(result.toJSONString());
                        if (newResult.containsKey("fieldValues")) {
                            JSONArray fieldValues = newResult.getJSONArray("fieldValues");
                            JSONArray newJSONArray = new JSONArray();
                            newJSONArray.add(fieldValues.get(i));
                            newResult.put("fieldValues", newJSONArray);
                        }
                        resultMap.put(String.valueOf(sids[i]), newResult);
                    }
                }
            } catch (Exception e) {
                for (Long sid : ArrayUtils.toLongArray(value.getSids())) {
                    Map error = new HashMap();
                    error.put("sid", sid);
                    error.put("error", e.getMessage());
                    errorMsgList.add(error);
                }
            }
        }
        for (PrintWlbDataPageData data : pageDataList) {
            for (String s : ArrayUtils.toStringArray(data.getSids())) {
                Object o = resultMap.get(s);
                if (o != null) {
                    responseList.add(o);
                }
            }
        }

        vo.setResponse(responseList);
        vo.setErrorMsg(errorMsgList);
        return vo;
    }

    /**
     * 获取菜鸟电子面单打印数据并设置为已打印（自定义打印）
     *
     * @param sids
     * @param templateId
     * @param printBatchId
     * @param printer
     * @param mandatory
     * @param onlyShowPrinterList
     * @param wavePrintParam      波次打印参数
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/wlb/data/printer/custom", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'打印快递单，系统单号：' + #sids + '，打印机：' + #printer", action = "print_wlb_express")
    @ResponseBody
    public Object printWlbDataEndCustom(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                        Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean retry, String api_name, String validateKey, String repeatPrint, Boolean isHotSaleItemPrint) throws Exception {
        return printWlbDataEnd(sids, templateId, printBatchId, printer, wavePrintParam, mandatory, onlyShowPrinterList, billType, retry, api_name, 1, validateKey, repeatPrint, isHotSaleItemPrint, SINGLE_OR_BATCH_PRINT.name());
    }

    /**
     * 获取云打印电子面单打印数据并设置为已打印
     *
     * @param sids
     * @param templateId
     * @param printBatchId
     * @param printer
     * @param mandatory
     * @param onlyShowPrinterList
     * @param wavePrintParam      波次打印参数
     * @param billType
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/cloud/data/printer", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "(#exceptionPrint == null || !#exceptionPrint ? '打印快递单' : '异常单打印') + '，系统单号：' + #sids + '，打印机：' + #printer", action = "print_wlb_express")
    @ResponseBody
    public Object printCloudDataEnd(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                    Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint, String api_name, Integer printCountNum, String validateKey, Long staffId, String repeatPrint, Boolean isHotSaleItemPrint, String waybillPathName, Integer entrance) throws Exception {
        String beginSids = sids;
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, staffId);
        List<WaveTrace> waveTraces = buildWaveTrace(staff, ArrayUtils.toLongArray(sids), wavePrintParam);
        sids = filterSidsForWave(staff, sids, wavePrintParam.getWaveId());
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), ArrayUtils.toLongArray(sids));
        waveTraceService.batchAddWaveTrace(staff, waveTraces);

        waveHelpBusiness.checkSidsPrintSort(staff, beginSids, sids);
        Object printData = printCloudData(sids, templateId, mandatory, onlyShowPrinterList, null, billType, "0", api_name, validateKey, entrance);
        PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.CLOUD, null, sids, staff.getCompanyId(), staff.getId());
        logger.info(printLog);

        if (!(printData instanceof JSONObject)) {
            return printData;
        }
        if (!((JSONObject) printData).containsKey("fieldValues")) {
            return printData;
        }

/**
 *       retry参数是用来区分重试请求，重试的时候就不执行修改状态和插入打印记录的请求
 *       在后端发生错误时导致了第一次请求报错之后再次重试的时候不执行修改订单状态，插入打印记录等操作
 *       现在改为直接后端在插入打印记录的时候做一个去重处理
 *        if (null != retry && retry){
 *            return printData;
 *        }
 */
        if (billType == null) {
            sids = filterCloseSids((JSONObject) printData, sids);
            makeSystemFilterLog(staff, (JSONObject) printData, sids);
            Map<Long, String> batchNoMap = new HashMap<>();
            wavePrintParam.setBatchNoMap(batchNoMap);
            printWlbEnd(sids, printBatchId, printer, wavePrintParam, exceptionPrint, staffId, api_name, isHotSaleItemPrint);
            // 记录打印日志后,添加批次号字段
            JSONObject jsonPrintData = (JSONObject) printData;
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
                String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
                List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
                fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
            }
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "express", staff);
        }
        if ("1".equals(repeatPrint)) {
            //提示后还重新打印
            logger.debug(LogHelper.buildLog(staff, "订单号:" + sids + "非第一次打印提示后重新打印!"));
            TradeRepeatPrintTraceParams params = new TradeRepeatPrintTraceParams();
            params.setStaff(staff);
            Long[] sidArray = ArrayUtils.toLongArray(sids);
            params.setSids(Arrays.asList(sidArray));
            eventCenter.fireEvent(this, new EventInfo("trade.repeat.print.log").setArgs(new Object[]{params}), null);
        }

        // 记录运单生命周期打印次数
        SendWayBillEcUtils.buildAndSend(staff, this,
                new PrintPathParam(tradeSearchService.queryBySids(staff, false, ArrayUtils.toLongArray(sids))),
                null,
                StringUtils.isEmpty(waybillPathName) ? SINGLE_OR_BATCH_PRINT : PrintPathEnum.getEnumByName(waybillPathName),
                eventCenter);

        dataOperation(staff, request);
        TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);//通过参数控制打印返回给前端的数据日志
        //打印序号处理
        PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, printCountNum);
        return printData;
    }

    private String getCanPrintSids(Staff staff, Integer wavePrintType, String sids) {
        if (!Objects.equal(WavePrintType.SEED_PART.getValue(), wavePrintType) || StringUtils.isEmpty(sids)) {
            return sids;
        }

        // 过滤掉待审核的订单
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, ArrayUtils.toLongArray(sids));
        List<Trade> filterTrades = trades.stream().filter(trade -> !Objects.equal(trade.getSysStatus(), Trade.SYS_STATUS_WAIT_AUDIT)).collect(Collectors.toList());
        if (filterTrades.isEmpty()) {
            throw new IllegalArgumentException("当前波次无可打印订单！");
        }
        return StringUtils.join(TradeUtils.toSids(filterTrades), ",");
    }

    /**
     * 抖音质检面单打印
     */
    @RequestMapping(value = "/print/bicTrade/data/printer", method = RequestMethod.POST)
    @ResponseBody
    public Object printBicTradeDataEnd(String sids, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                       Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint, String api_name, Integer printCountNum, String validateKey, Long staffId, String repeatPrint, Boolean isHotSaleItemPrint, Integer entrance) throws Exception {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, staffId);
        List<WaveTrace> waveTraces = buildWaveTrace(staff, ArrayUtils.toLongArray(sids), wavePrintParam);
        sids = filterSidsForWave(staff, sids, wavePrintParam.getWaveId());
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), ArrayUtils.toLongArray(sids));
        waveTraceService.batchAddWaveTrace(staff, waveTraces);

        Object printData = printCloudData(sids, null, mandatory, onlyShowPrinterList, null, billType, "0", api_name, validateKey, true, entrance);
        PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.CLOUD, null, sids, staff.getCompanyId(), staff.getId());
        logger.info(printLog);
        if (!(printData instanceof JSONObject)) {
            return printData;
        }
        if (!((JSONObject) printData).containsKey("fieldValues")) {
            return printData;
        }
/**
 *       retry参数是用来区分重试请求，重试的时候就不执行修改状态和插入打印记录的请求
 *       在后端发生错误时导致了第一次请求报错之后再次重试的时候不执行修改订单状态，插入打印记录等操作
 *       现在改为直接后端在插入打印记录的时候做一个去重处理
 *        if (null != retry && retry){
 *            return printData;
 *        }
 */
        if (billType == null) {
            sids = filterCloseSids((JSONObject) printData, sids);
            makeSystemFilterLog(staff, (JSONObject) printData, sids);
            Map<Long, String> batchNoMap = new HashMap<>();
            wavePrintParam.setBatchNoMap(batchNoMap);
            printWlbEnd(sids, printBatchId, printer, wavePrintParam, exceptionPrint, staffId, api_name, isHotSaleItemPrint);
            // 记录打印日志后,添加批次号字段
            JSONObject jsonPrintData = (JSONObject) printData;
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
                String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
                List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
                fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
            }
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "express", staff);
        }
        if ("1".equals(repeatPrint)) {
            //提示后还重新打印
            logger.debug(LogHelper.buildLog(staff, "质检订单:" + sids + "非第一次打印提示后重新打印!"));
            TradeRepeatPrintTraceParams params = new TradeRepeatPrintTraceParams();
            params.setStaff(staff);
            Long[] sidArray = ArrayUtils.toLongArray(sids);
            params.setSids(Arrays.asList(sidArray));
            eventCenter.fireEvent(this, new EventInfo("trade.repeat.print.log").setArgs(new Object[]{params}), null);
        }
        dataOperation(staff, request);
        TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);//通过参数控制打印返回给前端的数据日志
        //打印序号处理
        PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, printCountNum);
        return printData;
    }


    @RequestMapping(value = "/print/data/closeTrade", method = RequestMethod.POST)
    @ResponseBody
    public Object printCloudDataByClose(String sids, Long templateId, String printBatchId, String printer, WavePrintParam wavePrintParam,
                                        Boolean mandatory, Boolean onlyShowPrinterList, Integer billType, Boolean exceptionPrint, String api_name, Integer printCountNum, String validateKey, Integer entrance) throws Exception {
        Staff staff = getStaff();
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, ArrayUtils.toLongArray(sids));
        List<Trade> filterTrades = trades.stream().filter(t -> t.getTemplateType() != 1).collect(Collectors.toList());
        if (filterTrades != null && filterTrades.size() > 0) {
            Map<String, Object> map = new HashedMap();
            map.put("filterSids", filterTrades.stream().map(Trade::getSid).collect(Collectors.toList()));
            return map;
        }
        List<WaveTrace> waveTraces = buildWaveTrace(staff, ArrayUtils.toLongArray(sids), wavePrintParam);
        sids = filterSidsForWave(staff, sids, wavePrintParam.getWaveId());
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), ArrayUtils.toLongArray(sids));
        waveTraceService.batchAddWaveTrace(staff, waveTraces);
        Object printData = printCloudData(sids, templateId, mandatory, onlyShowPrinterList, null, billType, "0", api_name, validateKey, entrance);
        return printData;
    }

    private List<WaveTrace> buildWaveTrace(Staff staff, Long[] sids, WavePrintParam wavePrintParam) {
        if (sids == null || sids.length == 0) {
            return null;
        }
        Integer wavePrintType = wavePrintParam.getWavePrintType();
        WaveTraceOperateEnum operateEnum = WaveTraceOperateEnum.PRINT_DATA_PRINTER;
        StringBuilder content = new StringBuilder();
        if (wavePrintType == null) {//前置打印
            content.append("波次前置打印，踢出波次，");
        } else if (wavePrintType == 4) {//播种全部打印
            content.append("播种全部打印：");
        } else if (wavePrintType == 7) {//后置全部打印
            content.append("后置全部打印：");
        } else if (wavePrintType == 8) {//播种仅打已播
            content.append("播种仅打已播：");
        }

        if (content.length() == 0) {
            return null;
        }
        List<Long> sidArray = Arrays.asList(sids);
        List<Trade> cacheTrades = TradePostPrintContextUtils.getTrades(sidArray);
        List<Trade> trades = CollectionUtils.isNotEmpty(cacheTrades) ? cacheTrades : printPageSearchService.queryBySids(staff, sidArray);
        if (trades == null || trades.size() == 0) {
            return null;
        }

        List<Long> sidList = trades.stream().filter(t -> t.getWaveId() != null && t.getWaveId() > 0).map(Trade::getSid).collect(Collectors.toList());
        if (sidList.size() == 0) {
            return null;
        }

        List<WaveSorting> waveSortings = waveSortingService.querySortingBySids(staff, sidList);
        if (waveSortings == null || waveSortings.size() == 0) {
            return null;
        }
        Map<Long, Set<Integer>> waveIdPositionNosMap = new HashMap<>();
        for (WaveSorting waveSorting : waveSortings) {
            Set<Integer> positionNos = waveIdPositionNosMap.computeIfAbsent(waveSorting.getWaveId(), k -> new HashSet<>());
            positionNos.add(waveSorting.getPositionNo());
        }

        List<WaveTrace> waveTraces = new ArrayList<>();
        for (Map.Entry<Long, Set<Integer>> entry : waveIdPositionNosMap.entrySet()) {
            WaveTraceUtils.buildWaveTrace(staff, entry.getKey(), operateEnum, content.append("位置号：").append(entry.getValue()).toString());
        }
        return waveTraces;
    }

    public String filterSidsForWave(Staff staff, String sids, Long waveId) {
        return filterSidsForWave(staff, sids, waveId, null);
    }

    /**
     * 如果是波次打印，过滤掉异常订单
     *
     * @param staff
     * @param sids
     * @param waveId
     * @return
     */
    public String filterSidsForWave(Staff staff, String sids, Long waveId, Integer wavePrintType) {
        if (waveId == null || waveId <= 0L) {
            return getCanPrintSids(staff, wavePrintType, sids);
        }
        Long[] sidArr = ArrayUtils.toLongArray(sids);
        List<Trade> trades;
        if (null == sidArr) {
            trades = new ArrayList<>();
        } else {
            trades = TemplateHelper.filterTradesForWave(staff, printPageSearchService, tradePostPrintService, Arrays.asList(sidArr), waveId, false);
        }
        return StringUtils.join(TradeUtils.toSids(trades), ",");
    }

    private String wavePrintDeliverfilterSids(Staff staff, String sids, Long waveId, Integer itemLimit, Integer itemKindCountType) {
        if (StringUtils.isBlank(sids)) return null;
        Long[] sidArr = ArrayUtils.toLongArray(sids);
        List<Trade> trades = TemplateHelper.filterTradesForWave(staff, printPageSearchService, tradePostPrintService, Arrays.asList(sidArr), waveId, false);
        if (itemLimit != null) {
            List<Long> sidList = trades.stream().filter(t -> TemplateHelper.countItemKindNum(t, itemKindCountType) > itemLimit).map(Trade::getSid).collect(Collectors.toList());
            return StringUtils.join(sidList, ",");
        }
        return StringUtils.join(TradeUtils.toSids(trades), ",");
    }

    /**
     * 查询未打印情况下的订单
     * 过滤拣选情况
     *
     * @param waveId
     * @param pickingId
     * @param pickedStatus null  全部状态
     * @param needOrders   是否需要子单信息
     * @return
     * @throws Exception
     */
    @RequestMapping("/pick/sids")
    @ResponseBody
    public Object queryCanSeedPrintSidsByPick(Long waveId, Long pickingId, Integer pickedStatus, Boolean isUnPickedPrint, Integer printStatus, Long staffId, Boolean needOrders, String templateStr, String notInTemplateStr, String logisticsCompanyIdStr, String notInLogisticsCompanyIdStr) throws Exception {
        Assert.notNull(pickingId, "请扫描拣选号！");
        Assert.notNull(pickedStatus, "请输入拣选状态！");
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, staffId);
        List<Long> sids = tradePostPrintService.queryUnPrintSidsByPickStatus(staff, waveId, pickingId, pickedStatus, isUnPickedPrint, printStatus);
        List<Trade> trades = TemplateHelper.filterTradesForWave(staff, printPageSearchService, tradePostPrintService, sids, waveId, true);
        Map<Long, List<Order>> sidAndOrderMaps = Maps.newHashMap();
        if (BooleanUtils.isTrue(needOrders)) {
            sidAndOrderMaps.putAll(tradePostPrintService.queryDetailsBySidsAndPickingIds(staff, Lists.newArrayList(pickingId), sids));
        }
        logger.debug(LogHelper.buildLog(staff, String.format("查询单品波次只打已拣的订单.sids:%s", Arrays.toString(TradeUtils.toSids(trades)))));
        List<Long> logisticsCompanyIdList = ArrayUtils.toLongList(logisticsCompanyIdStr);
        List<Long> notInLogisticsCompanyIdList = ArrayUtils.toLongList(notInLogisticsCompanyIdStr);
        return trades.stream().filter(v -> {
            return (!StringUtils.isNotEmpty(templateStr) || templateStr.contains(v.getTemplateId() + "_" + v.getTemplateType()))
                    && (!StringUtils.isNotEmpty(notInTemplateStr) || !notInTemplateStr.contains(v.getTemplateId() + "_" + v.getTemplateType()))
                    && (!CollectionUtils.isNotEmpty(logisticsCompanyIdList) || logisticsCompanyIdList.contains(v.getLogisticsCompanyId()))
                    && (!CollectionUtils.isNotEmpty(notInLogisticsCompanyIdList) || !notInLogisticsCompanyIdList.contains(v.getLogisticsCompanyId()));
        }).map(trade -> convertSimpleTrade(trade, sidAndOrderMaps)).collect(Collectors.toList());
    }

    /**
     * 查询未打印情况下的订单
     * 过滤播种情况
     *
     * @param waveIds
     * @param pickingIds
     * @param matchedStatus null  全部状态
     * @param needOrders    是否需要子订单的信息
     * @return
     * @throws Exception
     */
    @RequestMapping("/seed/sids")
    @ResponseBody
    public Object queryCanSeedPrintSids(String waveIds, String pickingIds, Integer matchedStatus, Boolean isUnPickedPrint, Integer wavePrintType, Integer printStatus, Long staffId, Boolean needOrders, String templateStr, String notInTemplateStr, String logisticsCompanyIdStr, String notInLogisticsCompanyIdStr) throws Exception {
        Assert.notNull(pickingIds, "请扫描拣选号！");
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, staffId);
        List<Long> sids = tradePostPrintService.queryUnPrintSids(staff, DataUtils.ids2List(waveIds), DataUtils.ids2List(pickingIds), matchedStatus, isUnPickedPrint, wavePrintType, printStatus);
        List<Trade> trades = TemplateHelper.filterTradesForWave(staff, printPageSearchService, tradePostPrintService, sids, null, true);
        Map<Long, List<Order>> sidAndOrderMaps = Maps.newHashMap();
        if (BooleanUtils.isTrue(needOrders)) {
            sidAndOrderMaps.putAll(tradePostPrintService.queryDetailsBySidsAndPickingIds(staff, DataUtils.ids2List(pickingIds), sids));
        }
        logger.debug(LogHelper.buildLog(staff, String.format("查询多品波次只打已播的订单.sids:%s", Arrays.toString(TradeUtils.toSids(trades)))));
        List<Long> logisticsCompanyIdList = ArrayUtils.toLongList(logisticsCompanyIdStr);
        List<Long> notInLogisticsCompanyIdList = ArrayUtils.toLongList(notInLogisticsCompanyIdStr);
        return trades.stream().filter(v -> {
            return (!StringUtils.isNotEmpty(templateStr) || templateStr.contains(v.getTemplateId() + "_" + v.getTemplateType()))
                    && (!StringUtils.isNotEmpty(notInTemplateStr) || !notInTemplateStr.contains(v.getTemplateId() + "_" + v.getTemplateType()))
                    && (!CollectionUtils.isNotEmpty(logisticsCompanyIdList) || logisticsCompanyIdList.contains(v.getLogisticsCompanyId()))
                    && (!CollectionUtils.isNotEmpty(notInLogisticsCompanyIdList) || !notInLogisticsCompanyIdList.contains(v.getLogisticsCompanyId()))
                    && (!Objects.equal(Trade.SYS_STATUS_WAIT_AUDIT, v.getSysStatus()));
        }).map(trade -> convertSimpleTrade(trade, sidAndOrderMaps)).collect(Collectors.toList());
    }

    private Trade convertSimpleTrade(Trade trade, Map<Long, List<Order>> sidAndOrderMaps) {
        Trade copyTrade = new TbTrade();
        copyTrade.setSid(trade.getSid());
        copyTrade.setOutSid(trade.getOutSid());
        copyTrade.setTemplateId(trade.getTemplateId());
        copyTrade.setTemplateType(trade.getTemplateType());
        copyTrade.setTemplateName(trade.getTemplateName());
        copyTrade.setUserId(trade.getUserId());
        copyTrade.setTaobaoId(trade.getTaobaoId());
        copyTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
        copyTrade.setTags(trade.getTags());
        copyTrade.setSource(trade.getSource());
        List<Order> orders = sidAndOrderMaps.get(trade.getSid());
        if (CollectionUtils.isNotEmpty(orders)) {
            TradeUtils.setOrders(copyTrade, orders);
        }
        return copyTrade;
    }

    /**
     * 快递单电子面单的打印
     *
     * @param sids
     * @param printBatchId   打印批次编号
     * @param printer
     * @param wavePrintParam 是否把包装验货设置为已包装
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/print/wlb/end")
    @LogTag(key = "#sids", content = "'打印快递单，系统单号：' + #sids + '，打印机：' + #printer", action = "print_wlb_express")
    @ResponseBody
    public Object printWlbEnd(String sids, String printBatchId, String printer, WavePrintParam wavePrintParam, Boolean exceptionPrint, Long staffId, String api_name, Boolean isHotSaleItemPrint) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, staffId);
        return printDataService.printWlbEnd(staff, sids, printBatchId, printer, wavePrintParam, exceptionPrint, isHotSaleItemPrint, request);
    }

    /**
     * 自动包装验货取消缺货异常
     *
     * @param staff
     */
    private void packTradesCancelInsufficient(Staff staff, List<Trade> trades) {
        //开启仓储版本的订单，先取消缺货异常，再自动包装验货
        if (staff.getConf().isOpenWms()) {
            List<Long> insufficientSids = trades.stream().filter(t -> Objects.equal(t.getIsExcep(), CommonConstants.JUDGE_YES) && Trade.STOCK_STATUS_INSUFFICIENT.equals(t.getStockStatus()))
                    .map(Trade::getSid).collect(Collectors.toList());
            if (!insufficientSids.isEmpty()) {
                logger.debug(LogHelper.buildLog(staff, String.format("仓储版本，后置打印后，自动包装前缺货订单[%s]自动取消缺货", insufficientSids)));
                try {
                    Map<String, String> errMap = tradeService.cancelInsufficient(staff, insufficientSids.toArray(new Long[0]));
                    if (errMap != null && !errMap.isEmpty()) {
                        logger.warn(LogHelper.buildLog(staff, String.format("取消缺货异常存在异常返回，订单号：%s，错误map：%s", insufficientSids, errMap)));
                    }
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "取消缺货异常失败！"), e);
                }
            }
        }
    }

    private void doAutoPack(Staff staff, WavePrintParam wavePrintParam, Wave wave, String clientIp, WavePrintType wavePrintType, List<Trade> trades) {
        List<TradePackScanInfo> tradePackScanInfos = tradeWavePackBusiness.buildTradePackScanInfos(staff, trades, wavePrintType, Optional.ofNullable(wave).map(Wave::getId).orElse(null), wavePrintParam.getTradePackScanInfoList());
        tradeWavePackBusiness.packTrades(staff, trades.stream().map(Trade::getSid).collect(Collectors.toList()), tradePackScanInfos);
    }

    @RequestMapping(value = "/check/deliver/repeat/printer", method = RequestMethod.POST)
    @ResponseBody
    public Object checkDeliverRepeatPrinter(String sids, String api_name) throws Exception {
        Staff staff = getStaff();
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sidArray));
        JSONObject result = new JSONObject();
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TradeConfigEnum.DELIVER_NOT_REPEAT_PRINT);
        boolean needCheckRepeatPrint = tradeConfigNew.getConfigValue() != null && Integer.valueOf(tradeConfigNew.getConfigValue()).equals(1);
        result.put("needCheckRepeatPrint", needCheckRepeatPrint);
        if (needCheckRepeatPrint) {
            List<Trade> deliverPrintTradeList = trades.stream()
                    .filter(trade -> TradeStatusUtils.getPrintStatus(trade.getDeliverPrintTime()) == 1)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(deliverPrintTradeList)) {
                List<DeliverMessage> deliverMessageList = deliverPrintTradeList.stream()
                        .map(trade -> new DeliverMessage(trade.getSid(), trade.getOutSid()))
                        .collect(Collectors.toList());

                List<Long> failSids = deliverMessageList.stream()
                        .map(DeliverMessage::getSid)
                        .collect(Collectors.toList());
                result.put("failList", deliverMessageList);
                List<String> successSid = trades.stream()
                        .filter(trade -> !failSids.contains(trade.getSid()))
                        .map(trade -> String.valueOf(trade.getSid()))
                        .collect(Collectors.toList());
                result.put("successList", successSid);
            } else {
                result.put("failList", new ArrayList<>());
                List<String> successSid = trades.stream()
                        .map(trade -> String.valueOf(trade.getSid()))
                        .collect(Collectors.toList());
                result.put("successList", successSid);
            }
            return result;
        }
        return result;
    }

    /**
     * 获取发货单的打印数据并设置为已打印
     *
     * @param sids
     * @param templateId
     * @param printBatchId
     * @param printer
     * @param mandatory
     * @param api_name
     * @param itemLimit         商品数量（超出打印快递单的同时打印对应订单的发货单）
     * @param itemKindCountType 商品种类计算类型 1-套件 2-明细
     * @param forcePrint        强制打印发货单，不过滤非erp发货订单
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/deliver/data/printer", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'打印发货单，系统单号：' + #sids + '，打印机：' + #printer", action = "print_deliver")
    @ResponseBody
    public Object printDeliverDataEnd(String sids, Long templateId, Integer printNums, String printBatchId, String printer,
                                      Long waveId, Boolean mandatory, Integer billType, Integer needFilter, String api_name,
                                      @RequestParam(required = false) Integer itemLimit,
                                      @RequestParam(required = false) Integer itemKindCountType, Integer forcePrint) throws Exception {
        //波次批打打印发货单需过滤订单
        Staff staff = getStaff();
        if (null != needFilter && 1 == needFilter) {
            sids = wavePrintDeliverfilterSids(staff, sids, waveId, itemLimit, itemKindCountType);
            Assert.isTrue(StringUtils.isNotBlank(sids), "当前订单种类数量不满足打印发货单配置条件");
        }
        printHelperService.allocateGoodsWhenPrint(getStaff(), waveId, ArrayUtils.toLongArray(sids));

        //获取发货单的打印数据
        Object printData = printDeliverData(sids, templateId, printNums, mandatory, billType, forcePrint, api_name);
        if (!(printData instanceof JSONObject)) {
            return printData;
        }
        if (!((JSONObject) printData).containsKey("fieldValues")) {
            return printData;
        }

        if (billType == null) {
            if (CollectionUtils.isEmpty((List) ((JSONObject) printData).get("fieldValues"))) {
                return printData;
            }
            makeSystemFilterLog(getStaff(), (JSONObject) printData, sids);
            printDeliverEnd(sids, printBatchId, printer, waveId, api_name);
        } else {
            PrintTemplateHelper.handleMsgToOtherBills(this, billType, sids, eventCenter, "deliver", getStaff());
        }
        return printData;
    }

    /**
     * 快递单发货单的打印
     *
     * @param sids
     * @param printer
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/print/deliver/end")
    @LogTag(key = "#sids", content = "'打印发货单，系统单号：' + #sids + '，打印机：' + #printer", action = "print_deliver")
    @ResponseBody
    public Object printDeliverEnd(String sids, String printBatchId, String printer, Long waveId, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");

        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        final Staff staff = getStaff();

        /*现在的逻辑中后置打印的发货单打印与快递单打印没有关系，不需要进行相应校验
        List<Trade> trades = null;
        Integer openPrintDelay = staff.getConf().getOpenPrintDelay();
        //开启了后置打印，需要验证订单的面单号是否填写
        if (openPrintDelay != null && openPrintDelay == 1) {
            trades = tradeSearchService.queryBySids(staff, false, sidArray);
            boolean haveWave = false;
            for (Trade trade : trades) {
                if (null != trade.getWaveId() && trade.getWaveId() > 0) {
                    haveWave = true;
                    break;
                }
            }
            if (haveWave) {
                PrintTemplateHelper.verifyPrintDelayTrades(trades);
            }
        }*/

        final EndPrintTimeRequest printRequest = new EndPrintTimeRequest(sidArray, printer, null, printBatchId, waveId);
        printRequest.setTemplateKind(EnumTemplateKind.DELIVER.getValue());
        printRequest.setClientIp(IpUtils.getClientIP(request));
        printAdapterService.endPrintTime(staff, printRequest);
        return successResponse();
    }

    /**
     * 验货出库订单唯一码
     *
     * @param param
     * @param printer
     * @param printerSettings
     * @param api_name
     * @param validateKey
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/order/unique/code", method = RequestMethod.POST)
    @ResponseBody
    public Object printOrderUniqueCode(WavePickingParam param, String printer, String printerSettings, String validateKey, String api_name) throws Exception {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        // 第二阶段增加一个10s的缓存，有些打包机重试第二阶段导致重复出单
        if (DataUtils.checkLongNotEmpty(param.getSid()) && Objects.equal(2, param.getOperateType())) {
            Assert.isTrue(cache.add(buildOrderUniqueCodePrintCacheKey(param, staff), 1, 10), "当前订单正在获取打印数据，请稍后重试!");
        }
        ILockCallback<Object> lockCallback = () -> {
            try {
                fillOrderUniqueCodeBeforeInfo(staff, param);
                WavePickingScanResult result = printOrderUniqueCode0(staff, param, printer, printerSettings, api_name, validateKey);
                boolean hasTransaction = TransactionSynchronizationManager.isActualTransactionActive();//是否有事务管理
                if (!hasTransaction) {
                    logger.debug(LogHelper.buildLog(staff, "获取打印数据结束!当前事务已经被释放!"));
                }
                saveInspectionTrace(staff, param, result);
                setExpressCode(result.getTrade());
                return result;
            } catch (TradeValidatorException e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("唯一码验货出库，订单校验异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
                Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);
                result.put("errType", TradeValidatorException.EXCEPTION_TYPE);
                result.put("errCode", e.getErrCode());
                result.put("errMsg", e.getErrMsg());
                fillTradeAllUniqueCode(staff, param.getUniqueCode(), result);
                executeExceptionTradeUniqueCodeReceive(staff, param.isNewStyle(), param.getUniqueCode());
                return result;
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "唯一码验货出库，异常，msg:"), e);
                Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);
                fillTradeAllUniqueCode(staff, param.getUniqueCode(), result);
                result.put("errMsg", e.getMessage());
                return result;
            }
        };

        // 拆分之后请求uniqueCode参数为空
        if (StringUtils.isEmpty(param.getUniqueCode())) {
            return lockCallback.callback();
        } else {
            // 用户扫描的uniqueCode可能有误超长，会导致加锁失败
            return ptLockBusiness.otherLock(staff, Collections.singletonList(getOrderUniqueCodeTradeLockKey(staff, param.getUniqueCode())), lockCallback, PtLockBusiness.UNIQUE_CODE_ITEM_OUT_PRINT_LOCKPREFIX,
                    "存在正在并发操作的唯一码，请稍后处理", "验货出库加锁处理异常，请重试", true);

        }
    }

    @RequestMapping(value = "/print/end/order/unique/code", method = RequestMethod.POST)
    @ResponseBody
    public Object printEndOrderUniqueCode(WavePickingParam param, String printer, String printerSettings, String validateKey, String api_name) throws Exception {
        List<Long> sidList = new ArrayList<>();
        if (BooleanUtils.isTrue(param.getTradePrintStatusRetry())) {
            Assert.isTrue(StringUtils.isNotEmpty(param.getSidStr()), "sidStr为空");
            sidList.addAll(ArrayUtils.toLongList(param.getSidStr()));
            Assert.isTrue(sidList.size() <= 500, "sid数量超过限制");
        } else {
            Assert.notNull(param.getSid(), "sid为空！");
            sidList.add(param.getSid());
        }
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("快速验货模式多件订单修改订单状态，sids：%s", sidList)));
        }
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sidList.toArray(new Long[0]));
        Assert.notEmpty(tradeList, "查询不到该订单");

        // 多件订单成单后取消对应关系异常
        if (DataUtils.checkLongNotEmpty(param.getSid())) {
            Trade trade = cancelRelationChangedException(staff, tradeList.get(0));
            // 取消对应关系异常后的订单走后续逻辑
            tradeList = Lists.newArrayList(trade);
        }
        WavePrintParam wavePrintParam = new WavePrintParam();
        wavePrintParam.setStaffId(staff.getId());
        wavePrintParam.setWithPackage(true);
        wavePrintParam.setWavePrintType(WavePrintType.ORDER_UNIQUE_CODE.getValue());
        try {
            printDataService.postPrintEnd(staff, tradeList, wavePrintParam, printer, null);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("唯一码验货出库，订单修改状态异常,sid:%s", param.getSid())), e);
            if (BooleanUtils.isTrue(param.getTradePrintStatusRetry())) {
                throw e;
            }
            eventCenter.fireEvent(this, new EventInfo("print.end.retry").setArgs(new Object[]{staff, param.getSid(), printer}), null);
        }
        return successResponse();
    }

    private String getOrderUniqueCodeTradeLockKey(Staff staff, String uniqueCode) {
        List<WaveUniqueCode> codes = orderUniqueCodeService.fastQueryByUniqueCodes(staff, Lists.newArrayList(uniqueCode));
        if (CollectionUtils.isNotEmpty(codes) && DataUtils.checkLongNotEmpty(codes.get(0).getSid())) {
            return codes.get(0).getSid().toString();
        }
        return uniqueCode.length() > 30 ? uniqueCode.substring(0, 30) : uniqueCode;
    }

    /**
     * 记录验货日志
     */
    private void saveInspectionTrace(Staff staff, WavePickingParam param, WavePickingScanResult result) {
        if (result != null) {
            List<WaveUniqueCode> waveUniqueCodes = new ArrayList<>();
            if (param.isNewStyle()) {
                if (CollectionUtils.isNotEmpty(param.getCurrentMatchedUniqueCodes()) && CollectionUtils.isNotEmpty(result.getTradeAllUniqueCodes()) && param.getCurrentMatchedUniqueCodes().size() == result.getTradeAllUniqueCodes().size()) {
                    waveUniqueCodes = result.getTradeAllUniqueCodes();
                }
            }
            if (CollectionUtils.isNotEmpty(waveUniqueCodes)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(waveUniqueCodes), WaveUniqueOpType.INSPECTION}), null);
            }
        }
    }

    private void examinePrintAfter(Staff staff, WavePickingScanResult result, boolean ignorePrintData) {
        logger.info(LogHelper.buildLog(staff, "验货出库验货后处理开始"));
        if (result == null || result.getTrade() == null) {
            return;
        }
        if (!ignorePrintData && result.getPrintData() == null) {
            logger.info(LogHelper.buildLog(staff, "打印数据为空"));
            return;
        }
        PurchaseConfig purchaseConfig = purchaseService.getPurchaseConfig(staff);
        if (purchaseConfig == null || !OrderUniqueCodeUtils.isTrue(purchaseConfig.getAutoMoveStock())) {
            logger.info(LogHelper.buildLog(staff, "采购自动平移库存配置没开,不平移库存"));
            return;
        }
        orderUniqueCodeExamineService.examinePrintAfter(staff, result.getTrade().getSid());
    }

    private void fillOrderUniqueCodeBeforeInfo(Staff staff, WavePickingParam param) {
        MultiPrintConfig multiPrintConfig = multiPrintConfigService.get(staff);
        if (multiPrintConfig != null) {
            param.setProPrinterConfig(multiPrintConfig.getProPrinterConfig());
            param.setMultiplePrinterSettings(multiPrintConfig.getMultiplePrinterSettings());
            param.setOpenDefaultPrinter(multiPrintConfig.getOpenDefaultPrinter());
            Object uniqueCodeWlbTemplateTypes = multiPrintConfig.get(MultiPrintExtendConfigsEnum.UNIQUE_CODE_WLB_TEMPLATE_TYPES.getKey());
            if (uniqueCodeWlbTemplateTypes != null) {
                param.setUniqueCodeWlbTemplateTypes(uniqueCodeWlbTemplateTypes.toString());
            }
            param.setAbroadPrintSetting(multiPrintConfig.getAbroadPrintSetting());
        }
        if (param != null && BooleanUtils.isTrue(param.isOrderUniqueCodeExceptForce())) {
            OrderUniqueCodeConfig config = orderUniqueCodeService.queryConfig(staff);
            param.setOrderUniqueCodeForce(param.isOrderUniqueCodeExceptForce());
            param.setIgnoreExceptIds(config.getIgnoreExceptIds());
        }
        param.setClientIp(IpUtils.getClientIP(request));
        param.setAllowCancel(true);
    }

    private void fillTradeAllUniqueCode(Staff staff, String uniqueCode, Map<String, Object> result) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return;
        }
        WavePickingScanResult scanResult = new WavePickingScanResult();
        orderUniqueCodeService.fillTradeAllUniqueCode(staff, uniqueCode, scanResult);
        setExpressCode(scanResult.getTrade());
        result.put("tradeAllUniqueCodes", scanResult.getTradeAllUniqueCodes());
        result.put("trade", WaveUtils.simplifyTrade(scanResult.getTrade()));
    }

    /**
     * 快速验货模式下,执行异常订单的唯一码收货
     *
     * @param staff
     * @param isNewStyle
     * @param uniqueCode
     */
    @SuppressWarnings("unchecked")
    private void executeExceptionTradeUniqueCodeReceive(Staff staff, boolean isNewStyle, String uniqueCode) {
        try {
            // 判断唯一码是否存在
            WaveUniqueCode code = orderUniqueCodeService.queryByUniqueCode(staff, uniqueCode);
            if (code == null) {
                return;
            }

            //单件唯一码不执行收货
            if (isNewStyle && !Objects.equal(code.getCodeType(), CommonConstants.VALUE_YES)) {
                ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
                uniqueCodeParams.setOpType(0);
                uniqueCodeParams.setUniqueCodeArr(ArrayUtils.toStringList(uniqueCode));
                uniqueCodeParams.setExcepReceive(true);
                orderUniqueCodeService.batchReceive(staff, uniqueCodeParams);
            }
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "快速验货模式，异常订单唯一码收货失败：" + uniqueCode));
            }
        }
    }

    private static void setWaveScanException(WavePickingScanResult scanResult, WaveScanException e) {
        scanResult.setErrCode(e.getErrCode());
        scanResult.setErrMsg(e.getErrMsg());
        scanResult.setErrType(WaveScanException.EXCEPTION_TYPE);
    }

    private WavePickingScanResult printOrderUniqueCode0ByNewStyle(Staff staff, WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码验货新模式，currentMatchedUniqueCodeStr：" + param.getCurrentMatchedUniqueCodeStr()));
        }
        Assert.isTrue(StringUtils.isNotEmpty(param.getCurrentMatchedUniqueCodeStr()), "要验货的唯一码不能为空！");
        param.setCurrentMatchedUniqueCodes(ArrayUtils.toStringList(param.getCurrentMatchedUniqueCodeStr()));
        param.setUniqueCode(param.getCurrentMatchedUniqueCodes().get(0));

        WavePickingScanResult scanResult = null;
        long start = System.currentTimeMillis();
        long seedEnd = start;
        try {
            scanResult = orderUniqueCodeService.scanByUniqueCode(staff, param);
            seedEnd = System.currentTimeMillis();
            WaveSorting sorting = scanResult.getSorting();
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "订单唯一码验货出库，匹配结束，耗时：" + (seedEnd - start)));
            }
            Trade trade = scanResult.getTrade();
            orderUniqueCodeMatchedAllGetPrintData(staff, trade, param, sorting, printer, printerSettings, api_name, validateKey, scanResult, null);
        } catch (WaveScanException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            if (scanResult == null) {
                scanResult = new WavePickingScanResult();
            }
            setWaveScanException(scanResult, e);
        }
        long end = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("匹配时间：%s，打印时间：%s", seedEnd - start, end - seedEnd)));
        }
        orderUniqueCodeService.fillTradeAllUniqueCode(staff, param.getUniqueCode(), scanResult);
        if (scanResult.getTrade() != null) {
            setExpressCode(scanResult.getTrade());
            setOrderSalePrice(scanResult.getTrade(), scanResult.getOrderId());
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        return scanResult;
    }

    private String getCurrentMatchedUniqueCodes(Staff staff, boolean newStyle, String currentMatchedUniqueCodeStr) {
        if (newStyle || !OrderUniqueCodeUtils.isOpenHybridScan(orderUniqueCodeService.getHybridScanType(staff)) || StringUtils.isEmpty(currentMatchedUniqueCodeStr)) {
            return currentMatchedUniqueCodeStr;
        }
        WaveUniqueCode code = orderUniqueCodeService.queryByUniqueCode(staff, ArrayUtils.toStringList(currentMatchedUniqueCodeStr).get(0));
        if (code == null || !DataUtils.checkLongNotEmpty(code.getSid())) {
            return currentMatchedUniqueCodeStr;
        }
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "单多混扫模式进行拆分订单！"));
        }

        List<String> matchedUniqueCodes = orderUniqueCodeService.getMatchedUniqueCodes(staff, code);
        if (!CollectionUtils.isEmpty(matchedUniqueCodes)) {
            return Strings.join(",", matchedUniqueCodes);
        }
        return currentMatchedUniqueCodeStr;
    }

    private WavePickingScanResult printOrderUniqueCode0(Staff staff, WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        if (param.isNewStyle() && !DataUtils.checkLongNotEmpty(param.getSid())) {
            return printOrderUniqueCode0ByNewStyle(staff, param, printer, printerSettings, api_name, validateKey);
        }

        Assert.isTrue(param.getUniqueCode() != null || param.getSid() != null, "唯一码/订单号不能为空！");
        WavePickingScanResult scanResult = null;
        long start = System.currentTimeMillis();
        long seedEnd = start;
        try {
            if (StringUtils.isNotEmpty(param.getUniqueCode())) {
                scanResult = orderUniqueCodeService.scanByUniqueCode(staff, param);
            } else {
                param.setPickingId(0L);
                if (Objects.equal(param.getOperateType(), 2) || orderUniqueCodeService.openOrderUniqueCodeNewSplit(staff)) {
                    param.setIgnoreCheckWaveSorting(true);
                    param.setForce(true);
                }
                scanResult = getSeedTradeResult(staff, param);
                // 单多混扫模式下，currentMatchedUniqueCodeStr 要从数据库中获取，然后组装
                param.setCurrentMatchedUniqueCodeStr(getCurrentMatchedUniqueCodes(staff, param.isNewStyle(), param.getCurrentMatchedUniqueCodeStr()));
            }
            seedEnd = System.currentTimeMillis();
            WaveSorting sorting = scanResult.getSorting();
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "订单唯一码验货出库，匹配结束，耗时：" + (seedEnd - start)));
            }
            Trade trade = scanResult.getTrade();
            orderUniqueCodeMatchedAllGetPrintData(staff, trade, param, sorting, printer, printerSettings, api_name, validateKey, scanResult, null);
        } catch (WaveScanException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            // 判断是否需要返回打印数据
            Integer hybridScanType = orderUniqueCodeExamineService.getHybridScanType(staff);
            if (Objects.equal(WaveScanException.ERROR_CODE_UNIQUE_CODE_REPEAT, e.getErrCode()) && !Objects.equal(hybridScanType, 2)) {
                logger.info(LogHelper.buildLog(staff, "重复验货获取打印数据"));
                WaveUniqueCode code = orderUniqueCodeService.queryByUniqueCode(staff, param.getUniqueCode());
                param.setForce(true);
                param.setSid(code.getSid());
                param.setPickingId(0L);
                param.setIgnoreCheckWaveSorting(Objects.equal(1, code.getCodeType()) || orderUniqueCodeService.openOrderUniqueCodeNewSplit(staff));
                scanResult = getSeedTradeResult(staff, param);
                if (Objects.equal(1, code.getCodeType())) {
                    scanResult.setTradeMatched(true);
                } else {
                    scanResult.setTradeMatched(orderUniqueCodeService.isAllMatch(staff, code));
                }
                orderUniqueCodeService.cancelTradeExcept(staff, scanResult.getTrade(), true);
                fillUniqueItem(staff, scanResult, code);
                orderUniqueCodeMatchedAllGetPrintData(staff, scanResult.getTrade(), param, scanResult.getSorting(), printer, printerSettings, api_name, validateKey, scanResult, code);
            }
            if (scanResult == null) {
                scanResult = new WavePickingScanResult();
            }
            boolean isRecheck = param.isMultiTrade()
                    && Objects.equal(hybridScanType, 3)
                    && Objects.equal(WaveScanException.ERROR_CODE_UNIQUE_CODE_REPEAT, e.getErrCode())
                    && BooleanUtils.isTrue(scanResult.getTradeMatched());
            boolean isThrowException = !isRecheck
                    || (scanResult.getTrade() != null && scanResult.getTrade().getExpressPrintTime() != null
                    && scanResult.getTrade().getExpressPrintTime().after(TradeConstants.INIT_DATE));
            if (isThrowException) {
                setWaveScanException(scanResult, e);
            }
            scanResult.setRecheck(isRecheck);
        }
        long end = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("匹配时间：%s，打印时间：%s", seedEnd - start, end - seedEnd)));
        }
        orderUniqueCodeService.fillTradeAllUniqueCode(staff, param.getUniqueCode(), scanResult, param);
        if (scanResult.getTrade() != null) {
            setExpressCode(scanResult.getTrade());
            setOrderSalePrice(scanResult.getTrade(), scanResult.getOrderId());
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        return scanResult;
    }

    private void fillUniqueItem(Staff staff, WavePickingScanResult scanResult, WaveUniqueCode code) {
        Map<String, DmjItem> dmjItemMap = waveHelpBusiness.queryItemMap(staff, Lists.newArrayList(code.getSysItemId()), Lists.newArrayList(code.getSysSkuId()));
        if (org.apache.commons.collections.MapUtils.isEmpty(dmjItemMap)) {
            return;
        }
        DmjItem dmjItem = Lists.newArrayList(dmjItemMap.values()).get(0);
        scanResult.setItem(WavePickUtils.simplifyItem(dmjItem));
    }

    private void orderUniqueCodeMatchedAllGetPrintData(Staff staff, Trade trade, WavePickingParam param,
                                                       WaveSorting sorting, String printer,
                                                       String printerSettings, String api_name, String validateKey,
                                                       WavePickingScanResult scanResult, WaveUniqueCode code) {
        boolean currentMatchedAll = false;
        if (BooleanUtils.isTrue(scanResult.getTradeMatched())
                && (currentMatchedAll = orderUniqueCodeExamineService.currentMatchedAll(staff, param.getCurrentMatchedUniqueCodes()) || param.isOrderUniqueCodeForce())) {
            scanResult.setCurrentTradeMatched(true);
        }
        if (!OrderUniqueCodeUtils.orderUniqueCodeCanPrint(trade, scanResult.getTradeMatched())) {
            logger.info(LogHelper.buildLog(staff, "订单打印不获取打印数据"));
            return;
        }
        if (!currentMatchedAll) {
            logger.info(LogHelper.buildLog(staff, "唯一码没匹配成功不获取打印数据"));
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "开始获取打印数据！"));
        }
        param.setWaveId(WaveTypeEnum.ORDER_UNIQUE_CODE.getWaveId());

        // 对应关系改动不拦截 全部验货完成｜｜拆单发货,需要取消对应关系改动异常
        if (param.isRelationChangedMultiExamine() || DataUtils.checkLongNotEmpty(param.getSid())) {
            trade = cancelRelationChangedException(staff, trade);
            scanResult.setTrade(trade);
        } else {
            OrderUniqueCodeUtils.validateTrade(staff, trade, true, code);
        }

        orderUniqueCodeService.validateTemplate(staff, param, trade, null);
        // orderUniqueCodeExceptForce=true的时候会把orderUniqueCodeForce置为true，只有当orderUniqueCodeForce=true且orderUniqueCodeExceptForce=false的时候才不校验标签
        if (!(BooleanUtils.isNotTrue(param.isOrderUniqueCodeExceptForce()) && BooleanUtils.isTrue(param.isOrderUniqueCodeForce()))) {
            OrderUniqueCodeUtils.interceptRemarks(param.getInterceptRemarks(), trade);
        }
        fillTradeWlbTemplateType(staff, trade);
        handleExceptionTrade(staff, trade, param.getPickingId(), sorting.getId());
        // 快速验货模式 多件订单 开启异步获取打印数据
        if (param.isNewStyle() && orderUniqueCodeService.checkOpenUniqueCodeAsyncGetPrintDataFeature(staff) && param.isMultiTrade()) {
            long postPrintEndStart = System.currentTimeMillis();
            examinePrintAfter(staff, scanResult, true);
            orderUniqueCodeService.saveOrderUniqueScanRecord(staff, trade.getSid(), param);
            logger.info(LogHelper.buildLog(staff, "开启异步获取打印数据，耗时：" + (System.currentTimeMillis() - postPrintEndStart)));
            return;
        }
        if (Objects.equal(param.getOperateType(), 1)) {
            // 告诉前端可以进行第二阶段
            scanResult.setCurrentTradeMatched(true);
            // 提前获取快递单号
            long GetWaybillCodeStart = System.currentTimeMillis();
            postPrintWlbBusiness.batchGetWaybillCode(staff, BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate()), Lists.newArrayList(trade));
            logger.debug(LogHelper.buildLog(staff, "第一阶段获取单号耗时：" + (System.currentTimeMillis() - GetWaybillCodeStart)));
            logger.info(LogHelper.buildLog(staff, "打包机接口分离，第一阶段，不返回打印数据"));
            return;
        }
        Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trade);
        if (BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate()) || checkHasNewModeFeature(staff)) {
            WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), Lists.newArrayList(trade), param, scanResult, WavePrintType.ORDER_UNIQUE_CODE, RequestEntranceEnum.ITEM_OUT_PRINT, sorting.getPositionCode());
            logger.info(LogHelper.buildLog(staff, String.format("快递公司, 打印数据%s为空", waybillPrintResponse != null ? "不" : "")));
            if (waybillPrintResponse == null || CollectionUtils.isNotEmpty(waybillPrintResponse.getFailList())) {
                logger.error(LogHelper.buildLog(staff, "打印失败！原因：获取打印数据失败"));
                throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, String.format("打印失败！原因：获取打印数据失败 %s", getPrintDataFailReason(waybillPrintResponse)));
            }
            scanResult.setPrintData(waybillPrintResponse);
            examinePrintAfter(staff, scanResult, false);
            if (trade.getLogisticsCompanyId() != null && trade.getLogisticsCompanyId() > 0L) {
                // 记录唯一码绩效
                orderUniqueCodeService.saveOrderUniqueScanRecord(staff, trade.getSid(), param);
            }
        } else {
            PostPrintRequest postPrintRequest = new PostPrintRequest(staff, Lists.newArrayList(trade), param.getWaveId(), sidPrinterMap, WavePrintType.ORDER_UNIQUE_CODE, scanResult.getTradePackScanInfoList(), false, IpUtils.getClientIP(request), new HashMap<>(), param);
            List<Object> printDatas = postPrintWlbBusiness.printEWaybillByTrade(postPrintRequest);
            logger.info(LogHelper.buildLog(staff, String.format("打印数据%s为空", CollectionUtils.isNotEmpty(printDatas) ? "不" : "")));
            if (CollectionUtils.isNotEmpty(printDatas)) {
                scanResult.setPrintData(printDatas.get(0));
                examinePrintAfter(staff, scanResult, false);
            }
            // 记录唯一码绩效
            orderUniqueCodeService.saveOrderUniqueScanRecord(staff, trade.getSid(), param);
        }
    }

    private void itemUniqueCodeMatchedAllGetPrintData(Staff staff, Trade trade, WavePickingParam param,
                                                       String uniquePositionNo, String printer,
                                                       String printerSettings, String api_name, String validateKey,
                                                       WavePickingScanResult scanResult, WaveUniqueCode code) {
        if (scanResult.getErrCode() != null || scanResult.getErrMsg() != null) {
            return;
        }
        boolean currentMatchedAll = false;
        if (BooleanUtils.isTrue(scanResult.getTradeMatched())
                && (currentMatchedAll = itemUniqueCodeService.currentMatchedAll(staff, param.getUniqueCode()) || param.isOrderUniqueCodeForce())) {
            scanResult.setCurrentTradeMatched(true);
        }
        if (!OrderUniqueCodeUtils.orderUniqueCodeCanPrint(trade, scanResult.getTradeMatched())) {
            return;
        }
        if (!currentMatchedAll) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "开始获取打印数据！"));
        }
        param.setWaveId(scanResult.getWaveId());
        OrderUniqueCodeUtils.validateTrade(staff, trade, true, code);
        fillTradeWlbTemplateType(staff, trade);
        handleExceptionTrade(staff, trade, null, null);
        Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trade);
        WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), Lists.newArrayList(trade), param, scanResult, WavePrintType.FAST_CONSIGN_PRINT, RequestEntranceEnum.ITEM_OUT_PRINT, uniquePositionNo);
        scanResult.setPrintData(waybillPrintResponse);
    }

    // 取消对应关系异常,其他异常抛出异常
    private Trade cancelRelationChangedException(Staff staff, Trade trade) throws TradeValidatorException {
        try {
            OrderUniqueCodeUtils.validateTrade(staff, trade);
        } catch (TradeValidatorException e) {
            // 对应关系改动的子订单不是INSUFFICIENT会抛出RELATION_CHANGED,否则会抛出EXCEPTION
            boolean relationChanged = Objects.equal(e.getErrCode(), TradeValidator.Error.RELATION_CHANGED.getCode()) ||
                    (Objects.equal(e.getErrCode(), TradeValidator.Error.EXCEPTION.getCode()) &&
                            TradeUtils.getOrders4Trade(trade).stream().anyMatch(order -> Objects.equal(order.getRelationChanged(), 1)));
            if (relationChanged) {
                long cancelStart = System.currentTimeMillis();
                trade = orderUniqueCodeService.cancelRelationChangedException(staff, trade);
                long cancelEnd = System.currentTimeMillis();
                logger.debug(String.format("对应关系异常不拦截,订单%s验货完成,取消对应关系异常！耗时:%s", trade.getSid(), cancelEnd - cancelStart));
            } else {
                throw e;
            }
        }
        return trade;
    }

    private void fillTradeWlbTemplateType(Staff staff, Trade trade) {
        if (trade == null || !DataUtils.checkLongNotEmpty(trade.getTemplateId())) {
            return;
        }
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
        if (userWlbExpressTemplate != null) {
            trade.setWlbTemplateType(userWlbExpressTemplate.getWlbTemplateType());
        }
    }

    private static void setOrderSalePrice(Trade trade, Long orderId) {
        if (trade == null || !DataUtils.checkLongNotEmpty(orderId)) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<Long, String> orderMap = orders.stream()
                .filter(order -> order.getPrice() != null)
                .collect(Collectors.toMap(Order::getId, Order::getPrice));
        trade.setSalePrice(orderMap.get(orderId));
    }

    /**
     * 播种打印
     *
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/print/seed", method = RequestMethod.POST)
    @ResponseBody
    public Object printSeed(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        try {
            WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.NORMAL);
            return printSeed0(staff, param, printer, printerSettings, api_name, validateKey);
        } catch (TradeValidatorException e) {
            waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_SEED, ("播种打印出单,订单异常!"), 1));
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("播种扫描printSeed,订单校验异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);
            result.put("errType", TradeValidatorException.EXCEPTION_TYPE);
            result.put("errCode", e.getErrCode());
            result.put("errMsg", e.getErrMsg());
            return result;
        }
    }

    /**
     * 多波次播种打印
     *
     * @param param
     * @param printer
     * @param printerSettings
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/seed/multi", method = RequestMethod.POST)
    @ResponseBody
    public Object printSeedMulti(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        {
            String barcode = param.getOuterId();
            WaveUtils.setScanInfo(param, barcode, TradePackScanInfo.ScanCodeType.UNIQUE_CODE);
            WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesBarcode(barcode, waveConfigService.get(staff));
            Assert.notNull(multiWavesSeedKey, String.format("多波次后置打印扫描的条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(barcode)));
            waveHelpBusiness.fillMultiWaveSeedKey(staff, multiWavesSeedKey);
            WaveUtils.setCurrentPickingParam(param, multiWavesSeedKey);
        }

        WavePicking wavePicking = tradePostPrintService.scanByPickingCode(staff, param.getPickingCode(), WavePickingScanParam.SCAN_TYPE_MULTI_SEED, true);
        param.setWaveId(wavePicking.getWaveId());
        param.setPickingId(wavePicking.getId());
        param.setMultiWaves(true);

        try {
            JSONObject result = new JSONObject();
            result.put("wavePicking", wavePicking);
            result.put("printResult", printSeed0(staff, param, printer, printerSettings, api_name, validateKey));
            return result;
        } catch (TradeValidatorException e) {
            waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_SEED, ("播种打印出单,订单异常!" + e.getErrMsg()), 1));
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("播种扫描printSeedMulti,订单校验异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);
            result.put("errType", TradeValidatorException.EXCEPTION_TYPE);
            result.put("errCode", e.getErrCode());
            result.put("errMsg", e.getErrMsg());
            return result;
        }
    }

    private void fillBindUniqueCode(Staff staff, WavePickingParam param) {
        if (!itemUniqueCodeService.openUniqueCode(staff)) {
            return;
        }
        String sourKey = param.getOriginOuterId();
        if (StringUtils.isBlank(sourKey)) {
            return;
        }
        if (Strings.hasEmoji(sourKey)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "输入的条码有emoji表情，请检查!");
        }
        List<WaveUniqueCode> codes = itemUniqueCodeService.queryItemUniqueCodesByCodes(staff, true, Collections.singletonList(sourKey));
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        // 放入要绑定的唯一码
        WaveUniqueCode bindUniqueCode = codes.get(0);
        param.setBindUniqueCode(bindUniqueCode);
        param.setOuterId(StringUtils.isNotEmpty(bindUniqueCode.getSkuOuterId()) ? bindUniqueCode.getSkuOuterId() : bindUniqueCode.getMainOuterId());
        // 跳过后置打印波次唯一码校验
        param.setOriginOuterId(null);
    }

    private Map<String, Object> printSeed0(Staff staff, WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) {
        long t1, t2 = 0, t3 = 0, t4 = 0, t5;
        long start0 = System.currentTimeMillis();
        checkBeforeSeed(staff, param);
        fillBindUniqueCode(staff, param);
        checkIfSimpleStyle(staff, param);
        boolean seedOuterId = StringUtils.isNotEmpty(param.getOuterId());
        WavePickingScanResult scanResult = null;
        long start = System.currentTimeMillis();
        long seedEnd = start;
        Integer postionNo = null;
        t1 = System.currentTimeMillis() - start0;
        try {
            start0 = System.currentTimeMillis();
            if (seedOuterId) {
                scanResult = StringUtils.isNotEmpty(param.getMixPickingCode()) ? tradeMultiWavesPostPrintService.seedByOuterId(staff, param) : tradePostPrintService.seedByOuterId(staff, param);
            } else {
                scanResult = getSeedTradeResult(staff, param);
            }
            t2 = System.currentTimeMillis() - start0;
            seedEnd = System.currentTimeMillis();
            WaveSorting sorting = scanResult.getSorting();
            postionNo = sorting.getPositionNo();
            logger.debug(LogHelper.buildLog(staff, String.format("播种扫描商品，拣选号：%s，商家编码：%s，sid:%s，匹配到系统订单：%s，位置号：%s", param.getPickingCode(), param.getOuterId(), param.getSid(), sorting.getSid(), sorting.getPositionNo())));
            Trade trade = scanResult.getTrade();
            // 校验店铺权限
            TradeUtils.checkTradeShops(staff, Lists.newArrayList(trade));
            WaveConfig waveConfig = waveConfigService.get(staff);
            //处理包材
            if (BooleanUtils.isTrue(scanResult.getTradeMatched()) && trade != null) {
                tradeWavePackBusiness.consumePackma(staff, WavePackmaItemParam.builder().sidList(Lists.newArrayList(trade.getSid())).packmaOuterIds(param.getPackmaOuterIds()).packMaOptSource(AiPackmaOpEnum.WAVE_SEED.getOpType()).build());
            }
            start0 = System.currentTimeMillis();
            if (BooleanUtils.isTrue(scanResult.getTradeMatched()) && trade != null && (trade.getExpressPrintTime() == null ||
                    !trade.getExpressPrintTime().after(TradeConstants.INIT_DATE)) && !isFilterAbroadTrade(staff, trade, sorting)) {
                WaveUtils.tradeValidator(staff, trade, waveConfig, Boolean.TRUE);
                handleExceptionTrade(staff, trade, param.getPickingId(), sorting.getId());
                if (BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate())) {
                    scanResult.setPrintData(tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), Lists.newArrayList(trade), param, scanResult, WavePrintType.SEED, RequestEntranceEnum.TRADE_PACKING_SEED, sorting.getPositionCode()));
                } else {
                    Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trade);
                    printer = sidPrinterMap.get(trade.getSid());
                    PrintResult printResult = printEWaybillByTrade(staff, Lists.newArrayList(trade), param.getWaveId(), sidPrinterMap, WavePrintType.SEED, validateKey, scanResult.getTradePackScanInfoList());
                    if (CollectionUtils.isNotEmpty(printResult.getPrintData())) {
                        scanResult.setPrintData(printResult.getPrintData().get(0));
                    }
                    if (trade.getTemplateType() != null && trade.getTemplateType() != 0) {
                        Wave wave = new Wave();
                        wave.setId(trade.getWaveId());
                        handleAfterEWaybillPrintByWave(staff, Lists.newArrayList(trade), sorting.getPositionCode(), WavePrintType.SEED, printResult.getPrintTemplateType(), printer, null, wave);
                    } else {
                        logger.debug(LogHelper.buildLog(staff, String.format("普通面单%s，不进行播种打印", trade.getSid())));
                    }
                }
                waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_SEED, ("播种打印出单：位置号：" + postionNo)));
            }
            t3 = System.currentTimeMillis() - start0;
            start0 = System.currentTimeMillis();
            //播种打印商品日志输出
            try {
                List<WaveSortingDetail> details = waveSortingService.queryDetailByOuterId(staff, sorting.getId(), param.getOuterId());
                if (CollectionUtils.isNotEmpty(details)) {
                    List<WaveSortingDetail> waveSortingDetails = new ArrayList<>();
                    waveSortingDetails.add(details.get(0));
                    WaveItemTracePTLogHelper.batchRecodeItemTraceLogByWaveSortingDetail(itemTraceService, staff, waveSortingDetails, ItemTraceActionEnum.WAVE_PC_SEED.getCode(),
                            ItemTraceBillTypeEnum.WAVE.getCode(), String.valueOf(sorting.getWaveId()), WaveItemTraceActionModulePathEnum.PRINT_SEED.getName(), param.getBindUniqueCode());
                } else {
                    logger.error(LogHelper.buildLog(staff, String.format("播种打印商品日志输出%s，商品为空", trade.getSid())));
                }
            } catch (Exception e) {
                logger.error("播种打印商品日志输出失败", e);
            }
            t4 = System.currentTimeMillis() - start0;
        } catch (WaveScanException e) {
            waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_SEED, ("播种打印出单失败:" + e.getErrMsg()), 1));
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("播种扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            if (scanResult == null) {
                scanResult = new WavePickingScanResult();
            }
            scanResult.setErrCode(e.getErrCode());
            scanResult.setErrMsg(e.getErrMsg());
            scanResult.setErrType(WaveScanException.EXCEPTION_TYPE);
        }

        long end = System.currentTimeMillis();
        logger.debug(LogHelper.buildLog(staff, String.format("播种时间：%s，打印时间：%s", seedEnd - start, end - seedEnd)));
        Trade copyTrade = null;
        start0 = System.currentTimeMillis();
        if (scanResult.getTrade() != null) {
            setExpressCode(scanResult.getTrade());
            fillWithSkuPropertiesName(scanResult);
            assembleTradeShipper(scanResult, staff);
            assembleTradeLogisticsCompanyName(scanResult,staff);
            copyTrade = scanResult.getTrade();
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        t5 = System.currentTimeMillis() - start0;

        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L)) {
            logger.info(LogHelper.buildLog(staff, "printSeed0耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5));
        }

        HashMap<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put("sysItemSku", SysItemSkuVo.build(scanResult.getItem(), copyTrade));
        map.put("waveSeedResult", scanResult);
        return map;
    }

    private void fillWithSkuPropertiesName(WavePickingScanResult result) {
        //扫描返回平台规格信息
        DmjItem item = result.getItem();
        String key = "";
        if (item != null) {
            if (item instanceof DmjSku) {
                key = TradeItemUtils.getItemKey(item.getSysItemId(), ((DmjSku) item).getSysSkuId());
            } else {
                key = TradeItemUtils.getItemKey(item.getSysItemId(), null);
            }
        }
        List<Order> orders = OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(result.getTrade()), false);
        if (CollectionUtils.isNotEmpty(orders)) {
            Map<String, String> platformPropertiesNameMap = new HashMap<>(orders.size());
            for (Order order : orders) {
                if (key.equals(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()))) {
                    result.setSkuPropertiesName(order.getSkuPropertiesName());
                }
                platformPropertiesNameMap.put(WmsKeyUtils.buildOrderKey(order), order.getSkuPropertiesName());
            }
            result.setSkuPropertiesNameMap(platformPropertiesNameMap);
        }
    }

    /**
     * 播种前的校验
     */
    private void checkBeforeSeed(Staff staff, WavePickingParam param) {
        if (!param.isOpenSeedSimpleStyle()) {
            if (StringUtils.isNotEmpty(param.getMixPickingCode())) {
                Assert.notEmpty(param.getWaveIds(), "请选择波次！");
                Assert.notEmpty(param.getPickingIds(), "请输入拣选号！");
            } else {
                Assert.notNull(param.getWaveId(), "请选择波次！");
                Assert.notNull(param.getPickingId(), "请输入拣选号！");
            }
        }

        Assert.isTrue(Objects.equal(staff.getConf().getOpenSeed(), 1), "请先开启播种！");

        if (StringUtils.isEmpty(param.getOuterId()) && param.getSid() == null && StringUtils.isEmpty(param.getOriginOuterId())) {
            throw new IllegalArgumentException("请传入商家编码或订单号！");
        }
        param.setShowTradeInfo(true);
    }

    /**
     * 简洁模式扫唯一码校验波次号/拣选号
     *
     * @param param
     */
    private void checkIfSimpleStyle(Staff staff, WavePickingParam param) {
        if (param.isOpenSeedSimpleStyle()) {
            if (param.getBindUniqueCode() == null) {
                Assert.notNull(param.getWaveId(), "请先输入波次号！");
                Assert.notNull(param.getPickingId(), "请先输入拣选号！");
            } else if (param.getWaveId() == null || param.getPickingId() == null) {
                if (!DataUtils.checkLongNotEmpty(param.getBindUniqueCode().getSid())) {
                    throw new IllegalArgumentException("请先输入波次号！");
                }
                WaveTradeQueryParams queryParams = new WaveTradeQueryParams();
                queryParams.setSid(param.getBindUniqueCode().getSid());
                queryParams.setTradeWaveStatus(WaveTrade.TRADE_WAVE_STATUS_IN);
                List<WaveTrade> waveTrades = tradeWaveService.queryWaveTradeByParams(staff, queryParams);
                Assert.notEmpty(waveTrades, String.format("订单[%s]不在波次中！", param.getBindUniqueCode().getSid()));
                WaveTrade waveTrade = waveTrades.get(0);
                Assert.notNull(waveTrade.getWaveId(), String.format("订单[%s]不在波次中！", param.getBindUniqueCode().getSid()));
                try {
                    WavePicking picking = tradePostPrintService.scanByPickingCode(staff, waveTrade.getWaveId().toString(), WavePickingScanParam.SCAN_TYPE_SEED, true);
                    Assert.notNull(picking, String.format("根据波次号: %s未查询到拣选记录！", waveTrade.getWaveId()));
                    param.setWaveId(picking.getWaveId());
                    param.setPickingId(picking.getId());
                    param.setPickingCode(picking.getPickingCode());
                } catch (Exception e) {
                    throw new IllegalArgumentException(e.getMessage());
                }
            }
        }
    }

    /**
     * 获取播种订单信息
     */
    private WavePickingScanResult getSeedTradeResult(Staff staff, WavePickingParam param) {
        WaveSorting waveSorting = tradePostPrintService.queryWaveSortingBySid(staff, param.getPickingId(), param.getSid());
        // 唯一码复用时不校验分拣记录
        if (waveSorting == null && param.isIgnoreCheckWaveSorting()) {
            waveSorting = new WaveSorting();
        }
        Assert.notNull(waveSorting, "该订单分拣记录不存在！");
        if (BooleanUtils.isNotTrue(param.getForce())) {
            Assert.isTrue(waveSorting.getMatchedStatus() != null && waveSorting.getMatchedStatus() == WaveSorting.MATCHED_STATUS_OVER, "该订单未播种完成！");
        }

        Trade trade = tradeSearchService.queryBySid(staff, true, param.getSid());
        Assert.notNull(trade, "该订单不存在！");
        WavePickingScanResult result = new WavePickingScanResult();
        result.setTrade(trade);
        result.setTradeMatched(true);
        result.setTradeAllMatched(false);
        result.setSorting(waveSorting);
        waveSorting.setShopName(tradePostPrintService.queryUserIdShopNameMap(staff).get(trade.getUserId()));
        return result;
    }

    void setExpressCode(Trade trade) {
        if (trade == null || StringUtils.isNotEmpty(trade.getExpressCode())) {
            return;
        }
        if (trade.getExpressCompanyId() != null) {
            ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(trade.getExpressCompanyId());
            trade.setExpressCode(expressCompany != null ? expressCompany.getCode() : null);
            trade.setExpressName(expressCompany != null ? expressCompany.getName() : null);
        }
    }

    @RequestMapping(value = "/print/post/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object printPostBatch(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Throwable {
        Assert.notNull(param.getWaveId(), "请选择波次！");
        Assert.notNull(param.getPickingId(), "请输入拣选号！");

        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        WavePickingScanResult scanResult = new WavePickingScanResult();
        long start = System.currentTimeMillis();
        long postEnd = start;
        Set<Integer> postionNos = new HashSet<>();
        try {
            validAllowMultiWave(param, staff);
            param.setValidatePrintNum(multiPrintConfigService.get(staff).getValidatePrintNum());
            WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.NORMAL);
            WavePickingScanResult result = tradePostPrintService.scanBatchByOuterId(staff, param);
            postEnd = System.currentTimeMillis();
            List<Trade> trades = result.getTrades();
            scanResult.setItem(result.getItem());
            scanResult.setTradeMatched(result.getTradeMatched());
            List<WaveSorting> waveSortings = result.getSortings();
            if (waveSortings != null && waveSortings.size() > 0) {
                postionNos = waveSortings.stream().map(WaveSorting::getPositionNo).collect(Collectors.toSet());
            }
            scanResult.setSortings(waveSortings);
            scanResult.setShopNames(result.getShopNames());
            Map<Long, Trade> sidTradeMap = Optional.ofNullable(trades).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (a, b) -> a));
            filterUnPrintTrades(staff, param, trades, scanResult);
            // filterUnPrintTrades中handleExceptionTrades 取消缺货异常的订单会重新查询
            reFillTradeInfo(trades, sidTradeMap);
            if (CollectionUtils.isNotEmpty(trades)) {
                scanResult.setTrades(trades);
                scanResult.setTrade(trades.get(0));
                scanResult.setSids(trades.stream().map(Trade::getSid).collect(Collectors.toList()));
                scanResult.setMatchedTradeNum(trades.size());
                //由于是单品单件，所以播出的商品数量就是订单数
                scanResult.setMatchedItemNum(scanResult.getMatchedTradeNum());
                //打印成功后设置打印时间,不用重新查询
                for (Trade trade : trades) {
                    trade.setExpressPrintTime(new Date());
                }
            }
            if (!Objects.equal(param.getPostPrintBatchNewMode(), CommonConstants.JUDGE_YES) && CollectionUtils.isNotEmpty(trades)) {
                if (BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate())) {
                    WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), trades, param, scanResult, WavePrintType.POST_BATCH, RequestEntranceEnum.TRADE_PACKING_REAR, getPostType(param));
                    if (waybillPrintResponse == null || CollectionUtils.isNotEmpty(waybillPrintResponse.getFailList())) {
                        logger.error(LogHelper.buildLog(staff, "打印失败！原因：获取打印数据失败"));
                        throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！原因：获取打印数据失败 " + getPrintDataFailReason(waybillPrintResponse));
                    }
                    scanResult.setPrintData(waybillPrintResponse);
                } else {
                    Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trades.toArray(new Trade[0]));
                    // 获取打印数据
                    PrintResult printResult = printEWaybillByTrade(staff, trades, param.getWaveId(), sidPrinterMap, WavePrintType.POST_BATCH, validateKey, result.getTradePackScanInfoList());
                    // 后置打印设置总的打印序号
                    List<Object> printData = printResult.getPrintData();
                    scanResult.setPrintData(printData);
                    // 添加打印记录
                    handleAfterEWaybillPrintByWave(staff, trades, getPostType(param), WavePrintType.POST_BATCH, printResult.getPrintTemplateType(), "", null, null);
                }
                waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_POST_BATCH, ("后置连打出单：位置号：" + postionNos)));
                tradePostPrintService.fastInOutConsignAfter(staff, param, trades);
                // 用于当响应超时，前端发起重试请求返回打印数据。缓存两分钟
                try {
                    cache.set(buildPostPrintQueryCacheKey(param, staff), TradeUtils.toSids(trades), 120);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "前端重试打印数据缓存失败！"), e);
                }
            }
        } catch (WaveScanException e) {
            eventCenter.fireEvent(this, new EventInfo("wave.trace.add").setArgs(new Object[]{staff, Collections.singletonList(param.getWaveId()), WaveTraceOperateEnum.PRINT_POST_BATCH, ("后置连打出单失败：" + e.getErrMsg()), 1}), null);
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("后置连续扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            scanResult.setErrType(WaveScanException.EXCEPTION_TYPE);
            scanResult.setErrCode(e.getErrCode());
            scanResult.setErrMsg(e.getErrMsg());
            tradePostPrintService.cancelPostPrint(staff, param, scanResult.getSortings());
        } catch (Throwable e) {
            eventCenter.fireEvent(this, new EventInfo("wave.trace.add").setArgs(new Object[]{staff, Collections.singletonList(param.getWaveId()), WaveTraceOperateEnum.PRINT_POST_BATCH, ("后置连打出单失败"), 1}), null);
            logger.error(LogHelper.buildLog(staff, "后置连续扫描异常"), e);
            tradePostPrintService.cancelPostPrint(staff, param, scanResult.getSortings());
            throw e;
        }

        long end = System.currentTimeMillis();
        logger.debug(LogHelper.buildLog(staff, String.format("后置匹配时间：%s，打印时间：%s", postEnd - start, end - postEnd)));
        if (scanResult.getTrade() != null) {
            fillWithSkuPropertiesName(scanResult);
            setExpressCode(scanResult.getTrade());
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        return scanResult;
    }

    private String getPrintDataFailReason(WaybillPrintResponse waybillPrintResponse) {
        if (waybillPrintResponse == null || CollectionUtils.isEmpty(waybillPrintResponse.getFailList())) {
            return "";
        } else {
            WaybillMessage waybillMessage = waybillPrintResponse.getFailList().get(0);
            return (waybillMessage.getSid() == null ? waybillMessage.getMessage() : (waybillMessage.getSid() + waybillMessage.getMessage()));
        }
    }

    private void filterUnPrintTrades(Staff staff, WavePickingParam param, List<Trade> trades, WavePickingScanResult result) {
        List<WavePickingScanResult.ErrorItem> errorItems = tradePostPrintService.handleExceptionTrades(staff, trades);
        if (CollectionUtils.isNotEmpty(errorItems)) {
            List<WaveSorting> sortings = result.getSortings();
            List<Trade> errorTrades = Lists.newArrayListWithCapacity(errorItems.size());
            List<WaveSorting> errorSortings = Lists.newArrayListWithCapacity(errorItems.size());

            for (WavePickingScanResult.ErrorItem errorItem : errorItems) {
                for (Trade trade : trades) {
                    if (errorItem.getSid().equals(trade.getSid())) {
                        errorTrades.add(trade);
                    }
                }
                for (WaveSorting sorting : sortings) {
                    if (errorItem.getSid().equals(sorting.getSid())) {
                        errorSortings.add(sorting);
                    }
                }
            }

            result.setErrorData(errorItems);
            trades.removeAll(errorTrades);
            tradePostPrintService.cancelPostPrint(staff, param, errorSortings);
        }
    }

    /**
     * 后置连打取消缺货异常会重新查订单,之前填充的数据从老订单拷贝下
     * @param trades
     * @param sidTradeMap
     */
    private void reFillTradeInfo(List<Trade> trades, Map<Long, Trade> sidTradeMap) {
        if (CollectionUtils.isEmpty(trades) || sidTradeMap == null || sidTradeMap.isEmpty()) {
            return;
        }
        for (Trade trade : trades) {
            Trade originTrade = sidTradeMap.get(trade.getSid());
            if (originTrade == null) {
                continue;
            }
            if (StringUtils.isNotBlank(originTrade.getShopName())) {
                trade.setShopName(originTrade.getShopName());
            }
            if (originTrade.getWaveId() != null) {
                trade.setWaveId(originTrade.getWaveId());
            }
            if (originTrade.getPositionNo() != null) {
                trade.setPositionNo(originTrade.getPositionNo());
            }
            if (StringUtils.isNotBlank(originTrade.getWarehouseName())) {
                trade.setWarehouseName(originTrade.getWarehouseName());
            }
            if (StringUtils.isNotBlank(originTrade.getTemplateName())) {
                trade.setTemplateId(originTrade.getTemplateId());
                trade.setTemplateType(originTrade.getTemplateType());
                trade.setTemplateName(originTrade.getTemplateName());
            }
            if (StringUtils.isNotBlank(originTrade.getLogisticsCompanyName())) {
                trade.setLogisticsCompanyId(originTrade.getLogisticsCompanyId());
                trade.setLogisticsCompanyName(originTrade.getLogisticsCompanyName());
            }

        }
    }

    /**
     * 后置打印
     *
     * @param param
     * @param printer
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/post", method = RequestMethod.POST)
    @ResponseBody
    public Object printPost(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Throwable {
        Assert.notNull(param.getWaveId(), "请选择波次！");
        Assert.notNull(param.getPickingId(), "请输入拣选号！");

        if (param.getPositionNo() != null && param.getPositionNo() > 0L) {
            param.setScanType(WavePickingScanParam.SCAN_TYPE_SEED_CHECK);
        }
        WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.NORMAL);
        try {
            return printPost0(param, printer, printerSettings, api_name, validateKey);
        } finally {
            TradePostPrintContextUtils.remove();
        }
    }

    /**
     * 多波次后置打印
     *
     * @param param
     * @param printer
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/post/multi", method = RequestMethod.POST)
    @ResponseBody
    public Object printPostMulti(final WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Throwable {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        {
            String barcode = param.getOuterId();
            WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.UNIQUE_CODE);
            WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesBarcode(barcode, waveConfigService.get(staff));
            Assert.notNull(multiWavesSeedKey, String.format("多波次后置打印扫描的条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(barcode)));
            waveHelpBusiness.fillMultiWaveSeedKey(staff, multiWavesSeedKey);
            WaveUtils.setCurrentPickingParam(param, multiWavesSeedKey);
        }

        WavePicking wavePicking = tradePostPrintService.scanByPickingCode(staff, param.getPickingCode(), 1, true);
        param.setWaveId(wavePicking.getWaveId());
        param.setPickingId(wavePicking.getId());
        param.setMultiWaves(true);

        JSONObject result = new JSONObject();
        result.put("wavePicking", wavePicking);

        if (!Objects.equal(tradeStaffConfigService.get(staff).getPostAllowMultiWave(), 1) && PickingType.MULTI.getValue().equals(wavePicking.getPickingType())) {
            WavePickingScanResult scanResult = new WavePickingScanResult();
            scanResult.setErrCode(WaveScanException.ERROR_CODE_POST_NOT_ALLOW_MULTI_WAVE);
            scanResult.setErrMsg("多件波次不允许后置打印！");
            result.put("printResult", scanResult);
            return result;
        }
        try {
            result.put("printResult", printPost0(param, printer, printerSettings, api_name, validateKey));
        } finally {
            TradePostPrintContextUtils.remove();
        }
        return result;
    }

    private List<String> getShopNames(Staff staff, WavePickingParam param, Trade trade) {
        if (param == null || trade == null
                || trade.getUserId() == null || param.getWaveId() > 0) {
            return new ArrayList<>();
        }
        Map<Long, String> shopMap = tradePostPrintService.queryUserIdShopNameMap(staff);
        String shopName = shopMap.get(trade.getUserId());
        if (shopName == null) {
            return new ArrayList<>();
        }
        return Lists.newArrayList(shopName);
    }

    private WavePickingScanResult printPost0(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Throwable {
        long start = System.currentTimeMillis();

        //获取staff 1
        Staff staff = WaveUtils.getNoSupplierInfoStaff(staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId()));
        PrintESLog printESLog = PrintESLog.build(staff, PrintLogType.POST.getLogType());//打印耗时记录

        WavePrintType wavePrintType = Objects.equal(param.getScanType(), WavePickingScanParam.SCAN_TYPE_SEED_CHECK) ? WavePrintType.CHECK : WavePrintType.POST;
        WavePickingScanResult scanResult = new WavePickingScanResult();
        Integer positionNo = null;
        try {
            //唯一码校验 2
            fillBindUniqueCode(staff, param);
            logger.debug(LogHelper.buildLog(staff, String.format("后置打印查询唯一码，costTime：%s", System.currentTimeMillis() - start)));
            validAllowMultiWave(param, staff);
            long getMultiPrintConfigStart = System.currentTimeMillis();
            //获取多平台打印设置 3
            MultiPrintConfig multiPrintConfig = multiPrintConfigService.get(staff);
            param.setPostPrintPositionFirst(multiPrintConfig.getPostPrintPositionFirst());
            Object orderBySort = multiPrintConfig.get(MultiPrintExtendConfigsEnum.ORDER_BY_SORT.getKey());
            if (orderBySort != null) {
                param.setOrderBySort(Integer.parseInt(orderBySort.toString()));
            }
            TradePostPrintContextUtils.getWithInit().setGetMultiPrintConfigTime(System.currentTimeMillis() - getMultiPrintConfigStart);
            TradePostPrintContextUtils.getWithInit().setMatchBeforeTime(System.currentTimeMillis() - start);
            //匹配订单 4
            if ((StringUtils.isEmpty(param.getOuterId()) && param.getSid() != null) || (Objects.equal(param.getOperateType(), 2) && param.getSid() != null)) {
                if (Objects.equal(param.getOperateType(), 2)) {
                    param.setIgnoreCheckWaveSorting(true);
                    param.setForce(true);
                }
                scanResult = getSeedTradeResult(staff, param);
                // 两阶段模式，第二阶段不需要再次绑定唯一码； 但是后置拆分场景下也会走到这里，是需要绑定的
                if (!Objects.equal(param.getOperateType(), 2)) {
                    List<WaveUniqueCode> waveUniqueCodes = postPrintUniqueCodeBusiness.bindUniqueCodeForPostSplit(staff, param, scanResult.getSorting());
                    scanResult.setBindUniqueCodes(waveUniqueCodes);
                }
                if (Objects.equal(param.getOperateType(), 2) && scanResult.getSorting() != null) {
                    scanResult.getSorting().setSid(param.getSid());
                }
            } else {
                scanResult = tradePostPrintService.scanByOuterId(staff, param);
                setContext(scanResult);
            }
            logger.debug(LogHelper.buildLog(staff, String.format("后置打印扫描商品，拣选号：%s，商家编码：%s", param.getPickingCode(), param.getOuterId())));
            Trade trade = scanResult.getTrade();
            //填充店铺名称 5
            scanResult.setShopNames(getShopNames(staff, param, trade));
            long postEnd = System.currentTimeMillis();//匹配订单结束
            TradePostPrintContextUtils.getWithInit().setMatchTime(postEnd - start);
            printESLog.setScanTradeTook(postEnd - start);
            assembleItemShipper(staff,scanResult);
            WaveSorting sorting = scanResult.getSorting();
            if (sorting != null) {
                positionNo = sorting.getPositionNo();
                dealPositionCodeInfo(staff, sorting, param);
            }
            if (trade != null && sorting != null && !isFilterAbroadTrade(staff, trade, sorting)) {
                // 测试代码可以，后期可以删除
                stageOnTest(staff, param, 1, trade.getSid());
                if (Objects.equal(param.getOperateType(), 1) && StringUtils.isEmpty(trade.getOutSid())) {
                    long GetWaybillCodeStart = System.currentTimeMillis();
                    postPrintWlbBusiness.batchGetWaybillCode(staff, BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate()), Lists.newArrayList(trade));
                    logger.debug(LogHelper.buildLog(staff, "匹配接口获取单号耗时：" + (System.currentTimeMillis() - GetWaybillCodeStart)));
                }
                //订单校验与缺货订单取消缺货 6
                handleExceptionTrade(staff, trade, sorting.getPickingId(), sorting.getId());
                fillTradeWlbTemplateType(staff, scanResult.getTrade());
                //货主
                if (!Objects.equal(param.getOperateType(), 1)) {
                    long buildPrintDataEnd;
                    // 测试代码可以，后期可以删除
                    stageOnTest(staff, param, 2, trade.getSid());
                    if (BooleanUtils.isTrue(param.getHasPrintTemplateIntegrate()) || checkHasNewModeFeature(staff)) {
                        WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), Lists.newArrayList(trade), param, scanResult, wavePrintType, RequestEntranceEnum.TRADE_PACKING_REAR, sorting.getPositionCode());
                        if (waybillPrintResponse != null && !CollectionUtils.isEmpty(waybillPrintResponse.getFailList())) {
                            //由于在构造打印数据的方法 WaybillPrintBusinessServiceImpl#buildWlbPrintData 捕获了异常，导致异常无法向上抛出，上游无法感知到，导致业务数据无法回滚，故需要调用方
                            //做业务判断，来判定业务方法是否执行正常。
                            WaybillMessage waybillMessage = waybillPrintResponse.getFailList().get(0);
                            logger.error(LogHelper.buildLog(staff, String.format("订单:[%s]播种/后置打印异常,原因:%s", trade.getSid(), waybillMessage.getMessage())));
                            throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, String.format("订单:[%s]打印失败！原因：%s", trade.getSid(), waybillMessage.getMessage()));
                        }
                        scanResult.setPrintData(waybillPrintResponse);
                        buildPrintDataEnd = System.currentTimeMillis();//组装打印数据结束
                        printESLog.setBuildDataTook(buildPrintDataEnd - postEnd);
                    } else {
                        //只有完全匹配到这种商品组合才会返回订单
                        Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trade);
                        Map<Long, String> positionCodeMap = new HashMap<>();
                        positionCodeMap.put(sorting.getSid(), sorting.getPositionCode());
                        // 获取打印数据与修改订单 无单号订单获取单号 7
                        PostPrintRequest printRequest = new PostPrintRequest(staff, Collections.singletonList(trade),
                                param.getWaveId(), sidPrinterMap, wavePrintType, scanResult.getTradePackScanInfoList(), false, IpUtils.getClientIP(request), positionCodeMap, param);

                        List<Object> printData = postPrintWlbBusiness.printEWaybillByTrade(printRequest);

                        if (CollectionUtils.isNotEmpty(printData)) {
                            scanResult.setPrintData(printData.get(0));
                        }
                        buildPrintDataEnd = System.currentTimeMillis();//组装打印数据结束
                        printESLog.setBuildDataTook(buildPrintDataEnd - postEnd);
                    }
                    TradePostPrintContextUtils.getWithInit().setPrintTime(System.currentTimeMillis() - postEnd);
                    waveTraceService.addWaveTrace(staff, getWaveTrace(param, staff, positionNo, TradePostPrintContextUtils.getWithInit(), trade.getWaveId()));
                    fireBlindScanRecord(staff, param.getWaveId(), trade);
                    tradePostPrintService.fastInOutConsignAfter(staff, param, Lists.newArrayList(trade));
                    long afterTook = System.currentTimeMillis();// 处理wavetrace 等后置操作结束
                    printESLog.setAfterTook(afterTook - buildPrintDataEnd);
                }
                // 记录盲扫商品操作日志
                WaveItemTraceLogHelper.batchRecodeItemTraceLogByBlindScanning(itemTraceService, staff, trade, param, scanResult.getBindUniqueCodes());
                // 用于当响应超时，前端发起重试请求返回打印数据。缓存两分钟
                try {
                    cache.set(buildPostPrintQueryCacheKey(param, staff), new Long[]{trade.getSid()}, 120);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "前端重试打印数据缓存失败！"), e);
                }
            }
        } catch (WaveScanException e) {
            eventCenter.fireEvent(this, new EventInfo("wave.trace.add").setArgs(new Object[]{staff, Collections.singletonList(param.getWaveId()), WaveTraceOperateEnum.PRINT_POST, ("后置打印出单失败：" + e.getErrMsg()), 1}), null);
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("后置扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            scanResult.setErrType(WaveScanException.EXCEPTION_TYPE);
            scanResult.setErrCode(e.getErrCode());
            scanResult.setErrMsg(e.getErrMsg());
            if (scanResult.getSorting() != null) {
                tradePostPrintService.cancelPostPrint(staff, param, Lists.newArrayList(scanResult.getSorting()));
            }
        } catch (Throwable e) {
            eventCenter.fireEvent(this, new EventInfo("wave.trace.add").setArgs(new Object[]{staff, Collections.singletonList(param.getWaveId()), WaveTraceOperateEnum.PRINT_POST, ("后置打印出单失败"), 1}), null);
            logger.error(LogHelper.buildLog(staff, "后置打印异常"), e);
            tradePostPrintService.cancelPostPrint(staff, param, scanResult.getSorting() != null ? Lists.newArrayList(scanResult.getSorting()) : null);
            throw e;
        }
        logger.debug(LogHelper.buildLog(staff, String.format("后置匹配时间：%s，打印时间：%s", printESLog.getScanTradeTook(), printESLog.getBuildDataTook())));
        if (scanResult.getTrade() != null) {
            setExpressCode(scanResult.getTrade());
            fillWithSkuPropertiesName(scanResult);
            fillPlatformPicPath(scanResult);
            assembleTradeShipper(scanResult, staff);
            Trade trade = WaveUtils.simplifyTrade(scanResult.getTrade());
            //填充收货人姓名/电话/手机/地址
            if (!param.getPickingCode().equals("0") && !param.getPickingCode().equals("-1") && !param.getPickingCode().equals("-2")) {
                setTradeReceiverInfo(scanResult.getTrade(), trade);
            }
            scanResult.setTrade(trade);
        }
        printESLog.setTotalTook(System.currentTimeMillis() - start);
        logger.info(printESLog);
        return scanResult;
    }

    private void assembleTradeShipper(WavePickingScanResult scanResult, Staff staff) {
        //货主
        Long userId = scanResult.getTrade().getUserId();
        if(CompanyUtils.openMultiShipper(staff)&&userId!=null&&iShipperDubbo!=null){
            List<User> users = iShipperDubbo.queryByUserId(staff, Lists.newArrayList(userId));
            if(CollectionUtils.isNotEmpty(users)){
                Map<Long, Pair<String, String>> userMap = users.stream().filter(user -> StringUtils.isNotEmpty(user.getShipperId())).collect(toMap(User::getId, user -> Pair.of(user.getShipperId(), user.getShipperName())));
                Pair<String, String> pair = userMap.get(userId);
                if(pair!=null){
                    scanResult.getTrade().setShipperName(pair.getValue());
                    scanResult.getTrade().setShipper(pair.getValue());
                }else {
                    scanResult.getTrade().setShipperName(staff.getCompanyName());
                    scanResult.getTrade().setShipper(staff.getCompanyName());
                }
            }
        }
    }


    private void assembleTradeLogisticsCompanyName(WavePickingScanResult scanResult, Staff staff){
        //货主
        if(scanResult.getTrade()!=null&&scanResult.getTrade().getLogisticsCompanyId()!=null){
            String name = Optional.ofNullable(userLogisticsCompanyBusiness.queryById(staff, scanResult.getTrade().getLogisticsCompanyId(), false))
                    .map(UserLogisticsCompany::getName)
                    .orElse("");
            scanResult.getTrade().setLogisticsCompanyName(name);
        }
    }


    private void assembleItemShipper(Staff staff,WavePickingScanResult scanResult){
        //货主
        DmjItem item = scanResult.getItem();
        if(CompanyUtils.openMultiShipper(staff)&&item!=null&&item.getSysItemId()!=null){
            List<DmjItem> dmjItems = itemService.queryItemWithSysItemId(staff, Lists.newArrayList(item.getSysItemId()));
            if(CollectionUtils.isNotEmpty(dmjItems)){
                item.setShipper(dmjItems.get(0).getShipper());
                item.setShipperId(dmjItems.get(0).getShipperId());
            }
        }
    }

    private void assembleTradeShipper(){

    }

    /**
     * 跨境面单不打印快递单，并且模板为普通模板 直接更新为已打印
     *
     * @param staff
     * @param trade
     * @return
     */
    private boolean isFilterAbroadTrade(Staff staff, Trade trade, WaveSorting sorting) {
        if (PrintTemplateHelper.filterAfterAbroad(trade, multiPrintConfigService.get(staff))) {
            EndPrintTimeRequest printRequest = new EndPrintTimeRequest(new Long[]{trade.getSid()}, null, null, null, sorting.getWaveId());
            printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
            printRequest.setClientIp(IpUtils.getClientIP(request));
            printRequest.setExceptionPrint(false);
            printRequest.setAddTradePrintTrace(false);
            printAdapterService.endPrintTime(staff, printRequest);
            TradeTrace traceWithTrade = TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "后置打印", staff.getName(), new Date(), "后置打印快递单，未开启打印快递单配置，不真实打印快递单");
            tradeTraceService.addTradeTrace(staff, traceWithTrade);
            return true;
        }
        return false;
    }

    private void stageOnTest(Staff staff, WavePickingParam param, Integer currentStage, Long sid) throws CacheException {
        if (!Objects.equal(param.getOperateType(), 1) && !Objects.equal(param.getOperateType(), 2)) {
            return;
        }

        String stage = cache.get(WavePickingParam.print_post_split_stage);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "stage：" + stage));
        }
        if (StringUtils.isEmpty(stage) || Objects.equal("0", stage)) {
            return;
        }
        if (!Objects.equal(stage, currentStage.toString())) {
            return;
        }

        String errType = cache.get(WavePickingParam.print_post_split_error_type);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "errType：" + errType));
        }
        if (StringUtils.isEmpty(errType) || Objects.equal("0", errType)) {
            return;
        }

        String errSids = cache.get(WavePickingParam.print_post_split_sid);
        if (StringUtils.isEmpty(errSids) || ArrayUtils.toLongList(errSids).contains(sid)) {
            if (Objects.equal(errType, "1")) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "第" + currentStage + "阶段报错");
            } else if (Objects.equal(errType, "2")) {
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {

                }
            }
        }
    }

    public boolean checkHasNewModeFeature(Staff staff) {
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setCode("postPrintBatchNewMode");
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        if (response == null) {
            return false;
        }
        return response.isHasFeature();
    }

    @Nullable
    private WaveTrace getWaveTrace(WavePickingParam param, Staff staff, Integer positionNo, TradePostPrintContext printContext, Long waveId) {
        WaveTrace waveTrace = WaveTraceUtils.buildWaveTrace(staff, waveId, Objects.equal(EfficientEnum.EFFICIENT_SINGLE_WAVE.getWaveId(), param.getWaveId()) ? WaveTraceOperateEnum.NEGATIVE_2_PRINT_POST : WaveTraceOperateEnum.PRINT_POST, ("后置打印出单：位置号：" + positionNo));
        if (printContext != null && waveTrace != null) {
            waveTrace.setPrintTime(printContext.getPrintTime() == null ? 0L : printContext.getPrintTime());
            waveTrace.setMatchTime(printContext.getMatchTime() == null ? 0L : printContext.getMatchTime());
            waveTrace.setMatchBeforeTime(printContext.getMatchBeforeTime() == null ? 0L : printContext.getMatchBeforeTime());
            waveTrace.setFillInfoTime(printContext.getFillInfoTime() == null ? 0L : printContext.getFillInfoTime());

            waveTrace.setGetExecuteStaffTime(printContext.getGetExecuteStaffTime() == null ? 0L : printContext.getGetExecuteStaffTime());
            waveTrace.setGetTradeConfigTime(printContext.getGetTradeConfigTime() == null ? 0L : printContext.getGetTradeConfigTime());
            waveTrace.setFillResultWithParamTime(printContext.getFillResultWithParamTime() == null ? 0L : printContext.getFillResultWithParamTime());
            waveTrace.setFillItemCatNameTime(printContext.getFillItemCatNameTime() == null ? 0L : printContext.getFillItemCatNameTime());
            waveTrace.setMatchWaveSortingTime(printContext.getMatchWaveSortingTime() == null ? 0L : printContext.getMatchWaveSortingTime());
            waveTrace.setHandleMatchedWaveSortingTime(printContext.getHandleMatchedWaveSortingTime() == null ? 0L : printContext.getHandleMatchedWaveSortingTime());
        }
        return waveTrace;
    }

    /**
     * 处理订单日志记录盲扫类型
     */
    private void dealPositionCodeInfo(Staff staff, WaveSorting sorting, WavePickingParam param) {
        try {
            if (Objects.equal(param.getOperateType(), 2) && Integer.parseInt(param.getPickingCode()) <= 0 && StringUtils.isEmpty(sorting.getPositionCode())) {
                sorting.setPositionCode("快速盲扫"); // 盲扫类型，第二阶段位置号为空时，处理位置号
            }

            if (StringUtils.isEmpty(sorting.getPositionCode()) || param.getPickingCode() == null
                    || !DataUtils.checkIncNum(param.getPickingCode()) || Integer.parseInt(param.getPickingCode()) > 0) {
                return;
            }

            if (sorting.getPositionCode().contains("快速盲扫")) {
                sorting.setPositionCode(getPostType(param) + "快速盲扫");
            } else {
                sorting.setPositionCode(sorting.getPositionCode() + "," + getPostType(param) + "快速盲扫");
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "处理订单日志记录盲扫类型失败!"), e);
        }
    }

    private String getPostType(WavePickingParam param) {
        if (Objects.equal(param.getPickingCode(), "-1")) {
            if (!DataUtils.checkIntegerNotEmpty(param.getMoveStockType())) {
                return param.getPickingCode();
            }

            switch (param.getMoveStockType()) {
                case 1:
                    return "-4";
                case 2:
                    return "-5";
                case 3:
                    return "-6";
                default:
                    return param.getPickingCode();
            }
        }

        return param.getPickingCode();
    }

    private void fillPlatformPicPath(WavePickingScanResult scanResult) {
        if (scanResult.getTrade() == null) {
            return;
        }

        // 目前支持单件或多件订单设置平台图片
        List<Order> orders = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(scanResult.getTrade()), false);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<String, String> itemPlatformPicPathMap = new HashMap<>(orders.size());
        for (Order order : orders) {
            String orderKey = WmsKeyUtils.buildOrderKey(order); // key为itemSysId_skuSysId
            itemPlatformPicPathMap.put(orderKey, order.getPicPath()); // value为平台图片
            if (order.isSuit() && CollectionUtils.isNotEmpty(order.getSuits())) {
                // 套件明细平台图片
                for (Order suit : order.getSuits()) {
                    itemPlatformPicPathMap.put(WmsKeyUtils.buildOrderKey(suit), suit.getPicPath());
                }
            }
        }
        scanResult.setItemPlatformPicPathMap(itemPlatformPicPathMap);
    }

    private void validAllowMultiWave(WavePickingParam param, Staff staff) {
        // OriginOuterId 不为空 且 OriginOuterId与OuterId不同时
        if (StringUtils.isNotEmpty(param.getOriginOuterId()) && !Objects.equal(param.getOuterId(), param.getOriginOuterId())) {
            WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesUniqueBarcode(param.getOriginOuterId());
            if (multiWavesSeedKey == null) {
                return;
            }
            String uniqueCode = multiWavesSeedKey.getUniqueCode();
            List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeService.queryByUniqueCodes(staff, Lists.newArrayList(uniqueCode));
            if (CollectionUtils.isNotEmpty(waveUniqueCodes)) {
                waveUniqueCodeService.validAllowMultiWave(staff, UniqueCodeReceiveSourceEnum.POST.getSource(), waveUniqueCodes);
            }
        }
    }

    private void fireBlindScanRecord(Staff staff, Long waveId, Trade trade) {
        if (waveId == null || trade == null
                || (!Objects.equal(EfficientEnum.EFFICIENT_WAVE.getWaveId(), waveId)
                && !Objects.equal(EfficientEnum.EFFICIENT_FAST_WAVE.getWaveId(), waveId))) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo("blind.scan.record").setArgs(new Object[]{staff, Lists.newArrayList(trade.getSid()), WavePrintType.POST}), null);
    }

    private void setTradeReceiverInfo(Trade source, Trade target) {
        target.setReceiverName(source.getReceiverName());
        target.setReceiverCity(source.getReceiverCity());
        target.setReceiverState(source.getReceiverState());
        target.setReceiverDistrict(source.getReceiverDistrict());
        target.setReceiverAddress(source.getReceiverAddress());
        target.setReceiverPhone(source.getReceiverPhone());
        target.setReceiverMobile(source.getReceiverMobile());
    }

    /**
     * @param printer         默认打印机
     * @param printerSettings 模板和打印机对应关系
     * @param templateId      模板id
     * @return 打印机名称
     */
    private String getPrinter(String printer, String printerSettings, Long templateId) {
        if (StringUtils.isNotEmpty(printerSettings)) {
            JSONObject settings = JSONObject.parseObject(printerSettings);
            for (String setting : settings.keySet()) {
                if (setting.replaceAll("w", "").equals(String.valueOf(templateId))) {
                    return settings.getJSONObject(setting).getString("printerName");
                }
            }
        }

        return printer;
    }

    private Map<Long, String> getPrinterMap(String printer, String printerSettings, Trade... trades) {
        Map<Long, String> sidPrinterMap = Maps.newHashMapWithExpectedSize(trades.length);
        for (Trade trade : trades) {
            sidPrinterMap.put(trade.getSid(), getPrinter(printer, printerSettings, trade.getTemplateId()));
        }
        return sidPrinterMap;
    }

    private void handleExceptionTrade(Staff staff, Trade trade, Long pickingId, Long sortingId) {
        List<WavePickingScanResult.ErrorItem> errorItems = tradePostPrintService.handleExceptionTrades(staff, Lists.newArrayList(trade));
        if (CollectionUtils.isNotEmpty(errorItems)) {
            WavePickingScanResult.ErrorItem errorItem = errorItems.get(0);
            throw new WaveScanException(WaveScanException.ERROR_CODE_EXCEPTION, String.format("订单[%s]打印失败，原因：%s！", errorItem.getSid(), errorItem.getErrorMsg()));
        }
    }

    private TradeTemplateTypeMap batchGetWaybillCode(Staff staff, List<Trade> trades) {
        TradeTemplateTypeMap tradeTemplateTypeMap = new TradeTemplateTypeMap();
        Long warehouseId = null;
        Map<Long, List<Trade>> templateIdTradesMap = Maps.newHashMap();
        Map<Long, List<Trade>> kjTemplateIdTradesMap = Maps.newHashMap();
        List<Long> noOutSids = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            if (warehouseId == null) {
                warehouseId = trade.getWarehouseId();
            }
            if (StringUtils.isEmpty(trade.getOutSid())) {
                noOutSids.add(trade.getSid());
            }
            Assert.isTrue(warehouseId.equals(trade.getWarehouseId()), "请选择同仓库订单处理！");
            if (trade.getTemplateType() != null && trade.getTemplateType() == 1) {
                MapUtils.putToMapList(templateIdTradesMap, trade.getTemplateId(), trade);
            } else if (trade.getTemplateType() != null && trade.getTemplateType() == 2) {
                MapUtils.putToMapList(kjTemplateIdTradesMap, trade.getTemplateId(), trade);
            } else {
                MapUtils.putToMapList(tradeTemplateTypeMap.getNormalTradesMap(), trade.getTemplateId(), trade);
            }
        }

        if (!templateIdTradesMap.isEmpty()) {
            for (Map.Entry<Long, List<Trade>> entry : templateIdTradesMap.entrySet()) {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, entry.getKey(), false);
                Assert.notNull(userWlbExpressTemplate, "模板不存在！");
                List<Trade> tempTrades = entry.getValue();
                if (userWlbExpressTemplate.getWlbTemplateType() == 2 || userWlbExpressTemplate.getWlbTemplateType() == 8) {
                    tradeTemplateTypeMap.getCloudTradesMap().put(entry.getKey(), tempTrades);
                } else {
                    tradeTemplateTypeMap.getWlbTradesMap().put(entry.getKey(), tempTrades);
                }
                getWaybillCode(staff, warehouseId, entry.getKey(), userWlbExpressTemplate.getWlbType(), tempTrades, false);
            }
        }
        if (!kjTemplateIdTradesMap.isEmpty()) {
            for (Map.Entry<Long, List<Trade>> entry : kjTemplateIdTradesMap.entrySet()) {
                getWaybillCode(staff, warehouseId, entry.getKey(), null, entry.getValue(), true);
                tradeTemplateTypeMap.getKjTradesMap().put(entry.getKey(), entry.getValue());
            }
        }
        if (!noOutSids.isEmpty()) {
            Map<Long, Trade> sidMap = TradeUtils.toMapBySid(printPageSearchService.queryBySids(staff, noOutSids));
            for (Trade trade : trades) {
                Trade origin = sidMap.get(trade.getSid());
                if (origin != null) {
                    trade.setOutSid(origin.getOutSid());
                    trade.setTemplateName(origin.getTemplateName());
                    trade.setLogisticsCompanyId(origin.getLogisticsCompanyId());
                    trade.setLogisticsCompanyName(origin.getLogisticsCompanyName());
                }
            }
        }
        return tradeTemplateTypeMap;
    }

    private static class TradeTemplateTypeMap {
        private Map<Long, List<Trade>> wlbTradesMap = Maps.newHashMap();

        private Map<Long, List<Trade>> cloudTradesMap = Maps.newHashMap();

        private Map<Long, List<Trade>> kjTradesMap = Maps.newHashMap();

        private Map<Long, List<Trade>> normalTradesMap = Maps.newHashMap();

        public Map<Long, List<Trade>> getWlbTradesMap() {
            return wlbTradesMap;
        }

        public void setWlbTradesMap(Map<Long, List<Trade>> wlbTradesMap) {
            this.wlbTradesMap = wlbTradesMap;
        }

        public Map<Long, List<Trade>> getCloudTradesMap() {
            return cloudTradesMap;
        }

        public void setCloudTradesMap(Map<Long, List<Trade>> cloudTradesMap) {
            this.cloudTradesMap = cloudTradesMap;
        }

        public Map<Long, List<Trade>> getKjTradesMap() {
            return kjTradesMap;
        }

        public void setKjTradesMap(Map<Long, List<Trade>> kjTradesMap) {
            this.kjTradesMap = kjTradesMap;
        }

        public Map<Long, List<Trade>> getNormalTradesMap() {
            return normalTradesMap;
        }

        public void setNormalTradesMap(Map<Long, List<Trade>> normalTradesMap) {
            this.normalTradesMap = normalTradesMap;
        }
    }

    private void getWaybillCode(Staff staff, Long warehouseId, Long templateId, Integer wlbTemplateType, List<Trade> trades, boolean isKj) {
        List<Trade> filter = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            if (StringUtils.isEmpty(trade.getOutSid())) {
                filter.add(trade);
            }
        }
        if (filter.isEmpty()) {
            return;
        }
        Map<Long, String> sidAndErrorStrMap = new HashMap<>();
        List<Trade> needGetWaybillTradeList = waybillUsingRuleService.qiMenGetWaybillRuleCheck(staff, null, filter, userWlbExpressTemplateService, expressCompanyService, sidAndErrorStrMap, tradeTraceService, null);
        WlbStatus wlbStatus;
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateCloudService.userQuery(staff, templateId, false);
        WlbRequestGet wlbRequestGet = new WlbRequestGet(warehouseId, templateId, TradeUtils.toSids(needGetWaybillTradeList));
        wlbRequestGet.setWlbType(wlbTemplateType);
        wlbRequestGet.setClientIp(IpUtils.getClientIP(request));
        wlbRequestGet.setEntrance(RequestEntranceEnum.TRADE_PACKING_FORWARD.getCode());
        logger.info(LogHelper.buildLog(staff, "后置打印开始获取单号,订单号:" + JSONObject.toJSONString(needGetWaybillTradeList.stream().map(Trade::getSid).collect(Collectors.toList()))));
        wlbStatus = waybillGetTradeGroupService.getWlbWaybillCodeGroupByUserId(staff, userWlbExpressTemplate, wlbRequestGet, userWlbExpressCompanyAdapterService);
        SaveTradeTraceHelper.saveTradeTrace(TradeTraceParam.builder()
                .staff(staff)
                .waybillCode(wlbStatus)
                .tradeList(needGetWaybillTradeList)
                .templateId(templateId)
                .templateName(userWlbExpressTemplate.getName())
                .tradeTraceService(tradeTraceService)
                .uploadConsignPdd(new ArrayList<>())
                .featureSetBusiness(featureSetBusiness)
                .userLogisticsCompanyService(userLogisticsCompanyService)
                .build());

        // 后置打印取号先直接保存一次回收池数据，防止和打印事件冲突，事件里面只记录日志
        outSidRecyclePoolService.bacthInsertOrUpdate(
                staff,
                allocateGetPath.getOutSidRecyclePool(
                        PtWaybillPathContext.builder()
                                .staff(staff)
                                .pathParam(new SingleOrBatchGetParam(wlbRequestGet))
                                .pathResult(new WlbStatusResult(wlbStatus))
                                .waybillPath(POST_GET)
                                .sidAndRuleMap(needGetWaybillTradeList.stream()
                                        .filter(trade -> trade.getWaybillUsingRule() != null)
                                        .collect(Collectors.toMap(Trade::getSid, Trade::getWaybillUsingRule, (t1, t2) -> t1)))
                                .build())
        );

        // 后置打印获取单号运单生命周期处理
        SendWayBillEcUtils.buildAndSend(staff, this,
                new SingleOrBatchGetParam(wlbRequestGet),
                new WlbStatusResult(wlbStatus),
                POST_GET,
                eventCenter);

        if (StringUtils.isNotEmpty(wlbStatus.getErrorMessage())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取电子面单号失败！原因：" + wlbStatus.getErrorMessage());
        }
        if (CollectionUtils.isNotEmpty(wlbStatus.getErrorResult())) {
            String msg = "";
            Object errorResult = wlbStatus.getErrorResult().get(0);
            if (errorResult instanceof WlbResult) {
                msg = ((WlbResult) errorResult).getErrorMsg();
            }
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取电子面单号失败！原因：" + msg);
        }
        List successResult = wlbStatus.getSuccessResult();
        if (CollectionUtils.isEmpty(successResult)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取电子面单号失败！没有获取成功的订单!");
        }
        Set<String> successSids = new HashSet<>();
        for (Object o : wlbStatus.getSuccessResult()) {
            if (o instanceof WlbResult) {
                successSids.add(((WlbResult) o).getSid());
            }
        }
        if (needGetWaybillTradeList.size() != successSids.size()) {
            logger.info(LogHelper.buildLog(staff, "后置打印获取成功的订单和传入的订单数量不一致,成功订单号:" + JSONObject.toJSONString(successSids)));
            logger.info(LogHelper.buildLog(staff, "后置打印获取失败返回列表:" + JSONObject.toJSONString(wlbStatus.getErrorResult())));
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取电子面单号失败！存在未获取单号成功订单!");
        }
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(sidAndErrorStrMap)) {
            logger.info(LogHelper.buildLog(staff, "后置打印获取失败返回列表:" + JSONObject.toJSONString(sidAndErrorStrMap)).toString());
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "奇门获取电子面单号失败！存在未获取单号成功订单!");
        }
    }

    /**
     * 根据trade打印电子面单 非电子面单返回null
     */
    @SuppressWarnings("unchecked")
    private PrintResult printEWaybillByTrade(Staff staff, List<Trade> trades, Long waveId, Map<Long, String> sidPrinterMap, WavePrintType printType, String validateKey, List<TradePackScanInfo> tradePackScanInfoListt) {
        long start = System.currentTimeMillis();
        try {
            //1 获取单号
            batchGetWaybillCode(staff, trades);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "播种/后置打印获取单号异常"), e);
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取单号失败！原因：" + e.getMessage());
        }
        long getWaybillEnd = System.currentTimeMillis();//获取单号结束
        PrintResult printResult = new PrintResult();
        List<Object> printDatas;
        PrintESLog printESLog = PrintESLog.build(staff, PrintLogType.PRINT.getLogType());
        try {
            //2 获取打印数据
            List<Long> sids = trades.stream().map(Trade::getSid).collect(Collectors.toList());
            logger.info(LogHelper.buildLog(staff, "sids:" + JSONObject.toJSONString(sids)));
            Map<Long, String> batchNoMap = new HashMap<>();
            printDatas = printTemplateGroupedTrades(staff, waveId, sids, sidPrinterMap, printType, tradePackScanInfoListt, batchNoMap, printESLog);
            dealWaveScanException(printDatas);//打印失重置匹配状态
            for (Trade trade : trades) {
                // 记录批次打印记录的信息
                String batchNoAndSeq = batchNoMap.get(trade.getSid());
                if (StringUtils.isNotBlank(batchNoAndSeq)) {
                    String[] batchNoAndSeqSplit = batchNoAndSeq.split("_");
                    trade.setBatchNo(batchNoAndSeqSplit[0]);// 批次打印记录的批次号
                    trade.setSeq(Integer.valueOf(batchNoAndSeqSplit[1]));// 批次打印记录里的序号
                }
                trade.setPrintCount(trade.getPrintCount() == null ? 1 : trade.getPrintCount() + 1);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "播种/后置打印异常"), e);
            throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！原因：" + e.getMessage());
        }
        printResult.setPrintData(printDatas);
        long buildPrintDataEnd = System.currentTimeMillis();//组装打印数据结束

        //3 后置打印记录运单生命周期打印次数
        SendWayBillEcUtils.buildAndSend(staff, this,
                new PrintPathParam(trades),
                null,
                PrintPathEnum.getEnumByWavePrintTypeValue(printType),
                eventCenter);
        long saveRecycleLogEnd = System.currentTimeMillis();//生成回收池打印记录结束

        printESLog.setWaybillGetTook(getWaybillEnd - start);
        printESLog.setBuildDataTook(buildPrintDataEnd - getWaybillEnd);
        printESLog.setRecyclePoolLogTook(saveRecycleLogEnd - buildPrintDataEnd);
        printESLog.setTotalTook(saveRecycleLogEnd - start);

        logger.info(printESLog);
        return printResult;
    }

    /**
     * 后置打印
     */
    private List<Object> printTemplateGroupedTrades(Staff staff, Long waveId, List<Long> sids, Map<Long, String> sidPrinterMap, WavePrintType printType, List<TradePackScanInfo> tradePackScanInfoList, Map<Long, String> batchNoMap, PrintESLog printESLog) throws Exception {
        long start = System.currentTimeMillis();
        /**
         * 打印的时候需要处理的东西
         * 1 查询与过滤订单
         */
        WavePrintParam wavePrintParam = new WavePrintParam();
        wavePrintParam.setWaveId(waveId);
        wavePrintParam.setWithPackage(true);
        wavePrintParam.setWavePrintType(printType.getValue());
        wavePrintParam.setAddTraceByPt(false);
        wavePrintParam.setTradePackScanInfoList(tradePackScanInfoList);
        wavePrintParam.setStaffId(staff.getId());
        wavePrintParam.setBatchNoMap(batchNoMap);
        List<Trade> trades = TemplateHelper.getTradesForWave(staff, sids, wavePrintParam.getWaveId(), printPageSearchService);
        List<Long> closeTradeSids = trades.stream().filter(t -> Trade.SYS_STATUS_CLOSED.equals(t.getSysStatus()) || Trade.SYS_STATUS_CANCEL.equals(t.getSysStatus())).map(Trade::getSid).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(closeTradeSids), "订单:" + closeTradeSids + ",为交易关闭或作废订单,无法打印!");
        Assert.isTrue(CollectionUtils.isNotEmpty(trades), "校验后无需要打印的订单!");
        //一些校验和分组
        List<Long> notOutSid = trades.stream().filter(t -> t.getTemplateId() == null || t.getTemplateId() < 1 || StringUtils.isBlank(t.getOutSid())).map(Trade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notOutSid)) {
            throw new IllegalArgumentException("部分订单没有模板或没有运单号,订单列表:" + JSONObject.toJSONString(notOutSid));
        }
        long tradeSearchCheckEnd = System.currentTimeMillis();//查询订单结束
        //2 配货
        printHelperService.allocateGoodsWhenPrint(staff, wavePrintParam.getWaveId(), sids.toArray(new Long[0]));
        List<WaveTrace> waveTraces = buildWaveTrace(staff, sids.toArray(new Long[0]), wavePrintParam);
        waveTraceService.batchAddWaveTrace(staff, waveTraces);
        long allocateGoodsEnd = System.currentTimeMillis();//配货结束
        /**
         * 3 获取打印数据
         */
        Map<Long, Trade> sidTradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, v -> v, (v1, v2) -> v1));
        Map<Long, List<Trade>> templateTradeMap = new LinkedHashMap<>();
        for (Long sid : sids) {
            //根据sid的顺序排序
            Trade trade = sidTradeMap.get(sid);
            if (trade == null) {
                continue;
            }
            templateTradeMap.computeIfAbsent(trade.getTemplateId(), v -> new ArrayList<>()).add(trade);
        }

        List<Object> printDatas = new ArrayList<>();//打印数据
        List<Long> printSids = new ArrayList<>();//所有 组装了打印数据的 的订单号
        List<Trade> sortTrades = new ArrayList<>();//订单的出纸顺序
        for (Map.Entry<Long, List<Trade>> entry : templateTradeMap.entrySet()) {
            Long templateId = entry.getKey();
            List<Trade> tradeList = entry.getValue();
            sortTrades.addAll(tradeList);
            List<Long> templateSids = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
            printSids.addAll(tradeList.stream().map(Trade::getSid).collect(Collectors.toList()));
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateId, false);
            Object printData;
            if (WlbTemplateTypeEnum.isJdLop(userWlbExpressTemplate.getWlbTemplateType())) {
                printData = pds.getJdlopPrintData(getStaff(), templateId, false, null, tradeList);
            } else {
                printData = printDataShowService.getWlbData(staff, templateId, false, null, templateSids.toArray(new Long[0]));
                //printData = printWlbData(templateSids, templateId, null, false, null, null, "0", null, null);
            }
            if (null == printData) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！请到订单打印界面重试");
            }
            if (printData instanceof List) {
                List<BatchExecutException.ExceptionReport> exceptionReports = (List<BatchExecutException.ExceptionReport>) printData;
                String errorMsg = "打印失败！请到订单打印界面重试";
                if (CollectionUtils.isNotEmpty(exceptionReports)) {
                    errorMsg = exceptionReports.get(0).getErrorMsg();
                }
                throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, errorMsg);
            }
            removefilterSids((JSONObject) printData, printSids);//去掉过滤的订单
            String templateSidStr = StringUtils.join(templateSids, ",");
            makeSystemFilterLog(staff, (JSONObject) printData, templateSidStr);//oplog
            //通过参数控制打印返回给前端的数据日志 先注释 灰度config 目前前端有记录
            //TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);
            PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, 1);
            ((JSONObject) printData).put("templateId", templateId);
            printDatas.add(printData);
            PrintLog printLog = new PrintLog(PrintDataType.EXPRESS, PrintEntrance.NORMAL, PrintTemplateType.WLB, null, templateSidStr, staff.getCompanyId(), staff.getId());
            logger.info(printLog);
        }
        long getPrintDataEnd = System.currentTimeMillis();
        /**
         * 4 修改订单状态
         */
        String printer = sidPrinterMap.get(trades.get(0).getSid());
        tradePostPrintService.setPrintNum(sortTrades, printDatas);
        wavePrintParam.setPrintdataList(printDatas);
        printDataService.printWlbEnd(staff, StringUtils.join(printSids, ","), null, printer, wavePrintParam, null, false, request);
        //printWlbEnd(StringUtils.join(printSids, ","), null, printer, wavePrintParam, null, wavePrintParam.getStaffId(), "", false);
        long updateTradeEnd = System.currentTimeMillis();
        printESLog.setTradeSearchTook(tradeSearchCheckEnd - start);
        printESLog.setAllocateGoodsTook(allocateGoodsEnd - tradeSearchCheckEnd);
        printESLog.setGetPrintDataTook(getPrintDataEnd - allocateGoodsEnd);
        printESLog.setTradeUpdateTook(updateTradeEnd - getPrintDataEnd);
        printESLog.setTotalTook(updateTradeEnd - start);
        for (Object printData : printDatas) {
            // 记录打印日志后,添加批次号字段
            JSONObject jsonPrintData = (JSONObject) printData;
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(batchNoMap) && jsonPrintData.get("fieldValues") != null) {
                String batchNo = batchNoMap.values().stream().map(t -> t.split("_")[0]).findFirst().orElse("");
                List<Map<String, Object>> fieldValues = (List<Map<String, Object>>) jsonPrintData.get("fieldValues");
                fieldValues.forEach(t -> t.put("trade_print_batch_no", batchNo));
            }
        }
        return printDatas;
    }


    private static void dealWaveScanException(List<Object> printData) {
        if (CollectionUtils.isEmpty(printData)) {
            return;
        }
        List<Object> lists = printData.stream().filter(data -> data instanceof List).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lists)) {
            return;
        }
        for (Object data : lists) {
            List data2 = (List) data;
            if (CollectionUtils.isEmpty(data2)) {
                continue;
            }
            if (data2.get(0) instanceof BatchExecutException.ExceptionReport) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！原因：" + ((BatchExecutException.ExceptionReport) data2.get(0)).getErrorMsg());
            }
        }
    }

    private static class PrintResult {
        private List<Object> printData;

        private PrintTemplateType printTemplateType;

        public List<Object> getPrintData() {
            return printData;
        }

        public void setPrintData(List<Object> printData) {
            this.printData = printData;
        }

        public PrintTemplateType getPrintTemplateType() {
            return printTemplateType;
        }

        public void setPrintTemplateType(PrintTemplateType printTemplateType) {
            this.printTemplateType = printTemplateType;
        }
    }

    private void handleAfterEWaybillPrintByWave(Staff staff, List<Trade> trades, String positionCode, WavePrintType printType, PrintTemplateType printTemplateType, String printer, String printBatchId, Wave wave) {
        printDataService.handleAfterEWaybillPrintByWave(staff, trades, positionCode, printType, printTemplateType, printer, printBatchId, wave, null, request);
    }

    /**
     * 批次打印记录查询
     *
     * @return
     */
    @AccessShields(shield = {
            @AccessShield(value = "217", condition = @AccessCondition(field = "queryId", expected = {"31"})),
            @AccessShield(value = "223", condition = @AccessCondition(field = "queryId", expected = {"31"})),
            @AccessShield(value = "29102", condition = @AccessCondition(field = "queryId", expected = {"31"})),
            @AccessShield(value = "291", condition = @AccessCondition(field = "queryId", expected = {"31"}))
    })
    @RequestMapping(value = "/print/record", method = RequestMethod.GET)
    @ResponseBody
    public Object queryPrintLogs(PrintTradeLogQueryParams params, Page page) throws Exception {
        Staff staff = getStaff();
        PrintTradeLogs printLogs = search4Validate(staff, params, page);
        return printLogs;
    }


    /**
     * 爆款打印记录查询
     *
     * @return
     */
    @RequestMapping(value = "/hotSale/print/record", method = RequestMethod.GET)
    @ResponseBody
    public Object queryHotSalePrintLogs(PrintTradeLogQueryParams params, Page page) throws Exception {
        Staff staff = getStaff();
        PrintTradeLogs printLogs = printTradeLogService.searchByHotSale(staff, params, page);
        return printLogs;
    }


    /**
     * 批量保存普通面单运单号
     *
     * @param sids       订单号
     * @param outSids    快递单号
     * @param templateId 模板id
     * @param checkPdd   是否需要校验pdd订单
     */
    @RequestMapping(value = "/logistics/express/save", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'保存快递模板，系统单号：' + #sids + '，物流单号：' + #outSids", action = "save_express")
    @ResponseBody
    public Object saveLogisticsExpress(String sids, final String outSids, final String templateId, final String queryId, final String checkPdd, final String cancelOutsid, final Boolean skipTheWarning, String waveIds,
                                       Boolean singleTradeSave, Boolean isForceRecycling, String api_name) throws NumberFormatException, SessionException {
        final Staff staff = getStaff();
        sids = convertWaveIds2Sids(staff, waveIds, sids);
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        Assert.isTrue(StringUtils.isNotEmpty(templateId), "请输入templateId参数");

        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        Assert.isTrue(sidArray.length > 0, "请至少输入一笔订单");
        final TradeConfig tradeConfig = tradeConfigService.get(staff);

        ILockCallback<Object> lockCallback = new ILockCallback<Object>() {
            @Override
            public Object callback() {
                UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQuery(staff, Long.parseLong(templateId), false);
                Assert.notNull(userExpressTemplate, "该快递模板找不到");
                List<Trade> tradeList = printPageSearchService.queryBySids(staff, false, Arrays.asList(sidArray));
                List<Trade> haveSplitGetTradeList = tradeList.stream()
                        .filter(trade -> StringUtils.isNotEmpty(trade.getOutSid()) && TradeUtils.haveAllSplitGetTag(trade))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(haveSplitGetTradeList)) {
                    throw new RuntimeException("通过拆分订单获取子母单的订单不支持更换快递，需要先回收单号，才能更换快递！");
                }
                List<Trade> mpUpdateTagTradeList = tradeList.stream()
                        .filter(TradeUtils::haveMPUpdateTag)
                        .collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mpUpdateTagTradeList)) {
                    throw new IllegalArgumentException("更换快递模板系统内会回收单号，导致修改运单号的包裹无法打印面单发货，不能更换快递模板！");
                }
                validateTradeUnAudit(staff, tradeList);
                //validateTradJIT(tradeList);
//                TradeStatusUtils.validSysStatus(tradeList, tradeConfig, new String[]{Trade.SYS_STATUS_WAIT_AUDIT,
//                        Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED}, "修改快递");
                //校验订单所属的店铺是否有该快递模板
                TradeHelper.verifyTradeSaveTemplate(staff, tradeList, userExpressTemplate.getExpressId(), userService
                        , userLogisticsCompaniesJdService, expressCompanyService);
                //获取单号时候或者保存模板时候增加一些统一校验
                WayBillCompanyHelper.filterTradesWithTemplate(staff, userWlbExpressTemplateService, userExpressTemplate, tradeList, tradeConfig);

                // 校验并过滤订单,找出不符合电子面单配置的订单并过滤
                ExpressTemplateConfigHelper.validAndFilter(staff, expressTemplateConfigService, tradeList, userExpressTemplate);

                List<TradeTrace> tradeTraces = new ArrayList<>();
                tradeList.stream().forEach(trade -> {
                    if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus())
                            || (trade.getPrintCount() != null && trade.getPrintCount() > 0)
                            || trade.getIsUpload() != 0
                            || trade.isSysTriggerUpload()) {
                        if ((skipTheWarning != null && skipTheWarning) || java.util.Objects.equals("1", cancelOutsid)) {
                            tradeTraces.add(TradeTraceUtils.createTradeTrace(staff.getCompanyId(), trade.getSid(), "打印后修改模版",
                                    staff.getName(), new Date(), "【风险！】打印后修改快递模版：" + userExpressTemplate.getName()));
                        } else {
                            throw new HaveToPrintPtException("订单已打印，修改模板会取消原单号，可能会造成多发，请确认是否需要修改快递模板？");
                        }
                    }
                });
                if (CollectionUtils.isNotEmpty(tradeTraces)) {
                    tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
                }
                JSONObject result = new JSONObject();
                result.put("errorResults", ExpressTemplateConfigHelper.convertWlbResult());
                if (CollectionUtils.isEmpty(tradeList)) {
                    return result;
                }

                TradeConfig tradeConfig = tradeConfigService.get(staff);
                if (tradeConfig.getAllowChangeTemplate() == 0) {
                    //开启发货后pdd订单不允许修改快递单号
                    TradeStatusUtils.checkUpdateTemplatePdd(tradeList);
                }
                TradeStatusUtils.updateTemplateCheckJitx(tradeList, "修改快递模板");
                //校验订单是否允许修改模板  只在queryId = 62时校验
                if (queryId != null && queryId.equals(SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED + "")) {
                    tradeList = TradeStatusUtils.checkUpdateTemplate(tradeList, Long.parseLong(templateId), tradeConfig);
                }
                tradeList = checkUpdateTemplateByWave(templateId, queryId, tradeList, tradeConfig);
                List<Long> uploadConsignPdd = new ArrayList<>();
                if ("1".equals(checkPdd)) {
                    TradeStatusUtils.checkUpTemplateOutsid(tradeList, "修改模板", true);
                } else {
                    uploadConsignPdd = TradeStatusUtils.checkUpTemplateOutsid(tradeList, "修改模板", false);
                }

                //获取到历史模版
                Map<Long, Long> sidTemplateIdMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Trade::getTemplateId, (k1, k2) -> k2));
                List<Long> collect = tradeList.stream().map(Trade::getTemplateId).filter(java.util.Objects::nonNull).distinct().collect(Collectors.toList());
                Long[] longs = collect.toArray(new Long[]{});
                Map<Long, String> idTemplateNameMap = userWlbExpressTemplateService.queryNamesByIds(staff, longs);

                /**
                 * 在批量修改物流的时候，切换成普通面单，取消已经生成的电子面单号
                 */
                List<Trade> trades;
                boolean onlySaveTemplate = "1".equals(cancelOutsid);
                List<Trade> faildTrades = null;
                if (onlySaveTemplate) {
                    trades = tradeList;
                    //修改单号模板
                    faildTrades = upOutsidTemplate(staff, tradeList, Long.parseLong(templateId), userExpressTemplate.getTemplateType());
                    if (CollectionUtils.isEmpty(tradeList)) {
                        //都被过滤了
                        if (CollectionUtils.isNotEmpty(faildTrades)) {
                            throw new IllegalArgumentException("更换后模板与原模板对应快递网点不一致,订单号:" + JSONObject.toJSONString(faildTrades.stream().map(Trade::getSid).collect(Collectors.toList())));
                        }
                        result.put("addressReachable", TradeCanDeliverModel.build(trades));
                        return result;
                    }
                    String[] outSidArr = new String[tradeList.size()];
                    Arrays.fill(outSidArr, "-100");
                    trades = expressService.batchSaveLogisticsExpress(staff, tradeList.stream().map(Trade::getSid).toArray(Long[]::new), outSidArr, Long.parseLong(templateId), 1);
                } else {
                    boolean needFoceRecycCheck = singleTradeSave != null && singleTradeSave && Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(tradeList.get(0).getStatus());
                    List<Trade> tradeListCopy = tradeList;
                    //在取消单号前判断是否要取消单号
                    tradeList = filterTradeByTemplate(tradeList, Long.parseLong(templateId), staff, userExpressTemplate.getTemplateType());
                    try {
                        cancelSidOutSid(staff, tradeList);
                    } catch (Exception e) {
                        if (needFoceRecycCheck) {
                            if (isForceRecycling != null && isForceRecycling) {
                                // 系统内强制回收
                                String outSid = tradeList.get(0).getOutSid();
                                if (StringUtils.isNotEmpty(outSid)) {
                                    List<OutSidPool> outSidPoolList = new ArrayList<>();
                                    OutSidPool outSidPool = new OutSidPool();
                                    outSidPool.setOutSid(tradeList.get(0).getOutSid());
                                    outSidPool.setStatus(1);
                                    outSidPoolList.add(outSidPool);
                                    outSidPoolService.batchUpdateByOutSid(staff, outSidPoolList);
                                }
                            } else {
                                throw new ForceRecyclingException("单号回收失败，是否需要清空快递单号？（注意：已发货已揽收的订单清空单号再重新取号可能会造成重复发货，导致资金损失，请谨慎操作！）");
                            }
                        } else {
                            throw new IllegalArgumentException(e);
                        }
                    }
                    //因为有过滤逻辑写入 所有修改模板时需要过滤掉不需要修改模板的数据
                    String[] outSidArray;
                    List<Long> sidList;
                    if (StringUtils.isEmpty(outSids)) {
                        //没有单号则不是手动填入单号 需要将不需要清除单号的订单过滤
                        sidList = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
                        outSidArray = ArrayUtils.toStringArray(StringUtils.leftPad("", sidArray.length - 1, ','));
                        if (outSidArray == null) {
                            outSidArray = new String[]{""};
                        }
                    } else {
                        sidList = tradeListCopy.stream().map(Trade::getSid).collect(Collectors.toList());
                        outSidArray = ArrayUtils.toStringArray(outSids);
                    }
                    outSidArray = Arrays.copyOf(outSidArray, sidList.size());//觉得没有用,但是不敢删
                    // 保存之前先校验运单号是否被使用
                    expressService.checkUsedOutSids(staff, sidArray, outSidArray);
                    trades = expressService.batchSaveLogisticsExpress(staff, sidList.toArray(new Long[0]), outSidArray, Long.parseLong(templateId), 0);
                }
                //trades = tradeFilterBusiness.filterTrades(trades, true);
                List<WlbResult> successResult = new ArrayList<>();
                for (Trade trade : trades) {
                    successResult.add(new WlbResult(String.valueOf(trade.getSid()), null, trade.getOutSid()));
                    trade.setTemplateName(userExpressTemplate.getName());
                }
                WayBillCompanyHelper.sendReissueTradeToAfterSale(this, staff, eventCenter, successResult, trades);
                // 订阅快递助手并生成物流预警任务
                logisticsWarningBusiness.subscribeLogisticsEvent(staff, userExpressTemplate, successResult, trades);
                result.put("addressReachable", TradeCanDeliverModel.build(trades));
//                eventCenter.fireEvent(this, new EventInfo("trade.expresstemplate.save").setArgs(new Object[]{staff}), trades);
                cloudSmartMatchHelper.updateTradeSmart(staff, YesNoEnum.NO.getValue(), trades);
                gxTradeModifyTemplateLogService.saveOrderModifyLog(staff, trades);
                //记录日志
                saveDangerTradeTrace(staff, trades, uploadConsignPdd, "修改模板", "修改模板为:" + userExpressTemplate.getName(), sidTemplateIdMap, idTemplateNameMap, true, null);
                // 波次日志保存快递模板
                buildWaveTraceBySaveExpress(waveIds, staff, userExpressTemplate.getName());
                //修改波次列快递公司
                updateWaveExpressId(staff, waveIds, false, userExpressTemplate.getExpressId());
                if (CollectionUtils.isNotEmpty(faildTrades)) {
                    throw new IllegalArgumentException("更换后模板与原模板对应快递网点不一致,订单号:" + JSONObject.toJSONString(faildTrades.stream().map(Trade::getSid).collect(Collectors.toList())));
                }
                return result;
            }
        };

        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArray), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "修改模板时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }

    private void updateWaveExpressId(Staff staff, String waveIds, boolean isDelete, Long expressId) {
        if (StringUtils.isBlank(waveIds)) {
            return;
        }
        if (!isDelete && (expressId == null || expressId <= 0)) {
            return;
        }

        logger.debug(LogHelper.buildLog(staff, "根据波次修改快递，快递公司:" + expressId));
        tradeWaveService.updateWavesExpressId(staff, ArrayUtils.toLongList(waveIds), expressId);
    }

    /**
     * 批量京东快递（手工填写）运单号
     *
     * @param sids
     * @param outSids
     * @param templateId
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     * @throws NumberFormatException
     */
    @RequestMapping(value = "/logistics/jdExpress/save", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'保存快递模板，系统单号：' + #sids + '，物流单号：' + #outSids", action = "save_jdExpress")
    public
    @ResponseBody
    Object saveLogisticsJdExpress(final String sids, final String outSids, final String templateId,
                                  final String queryId, final String checkPdd, final String cancelOutsid, final Boolean skipTheWarning, Boolean skipAoXiangWarning, String api_name) throws NumberFormatException, SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        Assert.isTrue(StringUtils.isNotEmpty(templateId), "请输入templateId参数");

        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        Assert.isTrue(sidArray.length > 0, "请至少输入一笔订单");
        final Staff staff = getStaff();

        ILockCallback<Object> lockCallback = new ILockCallback<Object>() {
            @Override
            public Object callback() {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, Long.parseLong(templateId), false);
                Assert.notNull(userWlbExpressTemplate, "该快递模板找不到");
                List<Trade> tradeList = printPageSearchService.queryBySids(staff, false, Arrays.asList(sidArray));
                List<Trade> haveSplitGetTradeList = tradeList.stream()
                        .filter(trade -> StringUtils.isNotEmpty(trade.getOutSid()) && TradeUtils.haveAllSplitGetTag(trade))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(haveSplitGetTradeList)) {
                    throw new RuntimeException("通过拆分订单获取子母单的订单不支持更换快递，需要先回收单号，才能更换快递！");
                }
                List<Trade> mpUpdateTagTradeList = tradeList.stream()
                        .filter(TradeUtils::haveMPUpdateTag)
                        .collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mpUpdateTagTradeList)) {
                    throw new IllegalArgumentException("更换快递模板系统内会回收单号，导致修改运单号的包裹无法打印面单发货，不能更换快递模板！");
                }
                // 校验并过滤订单，找出不符合电子面单配置的订单并过滤
                ExpressTemplateConfigHelper.validAndFilter(staff, expressTemplateConfigService, tradeList, userWlbExpressTemplate);
                JSONObject result = new JSONObject();
                result.put("errorResults", ExpressTemplateConfigHelper.convertWlbResult());
                if (CollectionUtils.isEmpty(tradeList)) {
                    return result;
                }

                TradeConfig tradeConfig = tradeConfigService.get(staff);
                if (tradeConfig.getAllowChangeTemplate() == 0) {
                    //开启发货后pdd订单不允许修改快递单号
                    TradeStatusUtils.checkUpdateTemplatePdd(tradeList);
                }
                TradeStatusUtils.updateTemplateCheckJitx(tradeList, "修改快递模板");
                //校验订单是否允许修改模板  只在queryId = 62时校验
                if (queryId != null && queryId.equals(SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED + "")) {
                    tradeList = TradeStatusUtils.checkUpdateTemplate(tradeList, Long.parseLong(templateId), tradeConfig);
                }
                //校验订单所属的店铺是否有该快递模板
                TradeHelper.verifyTradeSaveTemplate(staff, tradeList, userWlbExpressTemplate.getExpressId(),
                        userService, userLogisticsCompaniesJdService, expressCompanyService);
                /**
                 * 在批量修改物流的时候，切换成京东快递（手工填写）面单，取消已经生成的电子面单号
                 */
                //获取单号时候或者保存模板时候增加一些统一校验
                WayBillCompanyHelper.filterTradesWithTemplate(staff, userWlbExpressTemplate, tradeList);
                if (java.util.Objects.nonNull(skipAoXiangWarning) && !skipAoXiangWarning) {
                    WayBillCompanyHelper.checkAoXiang(staff, tradeList, userWlbExpressTemplate, platformRecommendationService);
                }
                //更换模版通用校验
                WayBillCompanyHelper.checkTradeAndUserWlbExpressTemplateProxy(staff, tradeList, userWlbExpressTemplate, userService, skipTheWarning, tradeTraceService, cancelOutsid, false);

                //获取到历史模版
                Map<Long, Long> sidTemplateIdMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Trade::getTemplateId, (k1, k2) -> k2));
                List<Long> collect = tradeList.stream().map(Trade::getTemplateId).filter(java.util.Objects::nonNull).distinct().collect(Collectors.toList());
                Long[] longs = collect.toArray(new Long[]{});
                Map<Long, String> idTemplateNameMap = userWlbExpressTemplateService.queryNamesByIds(staff, longs);

                //校验pdd已上传发货订单
                List<Long> uploadConsignPdd = new ArrayList<>();
                if ("1".equals(checkPdd)) {
                    TradeStatusUtils.checkUpTemplateOutsid(tradeList, "修改模板", true);
                } else {
                    uploadConsignPdd = TradeStatusUtils.checkUpTemplateOutsid(tradeList, "修改模板", false);
                }
                List<Trade> trades;
                boolean onlySaveTemplate = "1".equals(cancelOutsid);
                List<Trade> faildTrades = null;
                if (onlySaveTemplate) {
                    trades = tradeList;
                    //修改单号模板
                    faildTrades = upOutsidTemplate(staff, tradeList, Long.parseLong(templateId), userWlbExpressTemplate.getTemplateType());
                    if (CollectionUtils.isEmpty(tradeList)) {
                        //都被过滤了
                        if (CollectionUtils.isNotEmpty(faildTrades)) {
                            throw new IllegalArgumentException("更换后模板与原模板对应快递网点不一致,订单号:" + JSONObject.toJSONString(faildTrades.stream().map(Trade::getSid).collect(Collectors.toList())));
                        }
                        result.put("addressReachable", TradeCanDeliverModel.build(trades));
                        return result;
                    }
                    String[] outSidArr = new String[tradeList.size()];
                    Arrays.fill(outSidArr, "-100");
                    trades = expressService.batchSaveLogisticsExpress(staff, tradeList.stream().map(Trade::getSid).toArray(Long[]::new), outSidArr, Long.parseLong(templateId), 1);
                } else {
                    List<Trade> tradeListCopy = tradeList;
                    //在取消单号前判断是否要取消单号
                    tradeList = filterTradeByTemplate(tradeList, Long.parseLong(templateId), staff, userWlbExpressTemplate.getTemplateType());
                    cancelSidOutSid(staff, tradeList);
                    //因为有过滤逻辑写入 所有修改模板时需要过滤掉不需要修改模板的数据
                    List<Long> sidList;
                    String[] outSidArray;
                    if (StringUtils.isEmpty(outSids)) {
                        //没有单号则不是手动填入单号 需要将不需要清除单号的订单过滤
                        sidList = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
                        outSidArray = ArrayUtils.toStringArray(StringUtils.leftPad("", sidArray.length - 1, ','));
                        if (outSidArray == null) {
                            outSidArray = new String[]{""};
                        }
                    } else {
                        sidList = tradeListCopy.stream().map(Trade::getSid).collect(Collectors.toList());
                        outSidArray = ArrayUtils.toStringArray(outSids);
                    }
                    outSidArray = Arrays.copyOf(outSidArray, sidList.size());//觉得没啥用,但是不敢删
                    // 保存之前先校验运单号是否被使用
                    expressService.checkUsedOutSids(staff, sidArray, outSidArray);
                    trades = expressService.batchSaveLogisticsExpress(staff, sidList.toArray(new Long[0]), outSidArray, Long.parseLong(templateId), 1);
                }

                if (TradeDiamondUtils.openNewPrintWrite(staff) && CollectionUtils.isNotEmpty(trades)) {
                    tradePtService.saveByTrades(staff, trades);
                }
                List<WlbResult> successResult = new ArrayList<>();
                for (Trade trade : trades) {
                    successResult.add(new WlbResult(String.valueOf(trade.getSid()), null, trade.getOutSid()));
                    trade.setTemplateName(userWlbExpressTemplate.getName());
                }
                WayBillCompanyHelper.sendReissueTradeToAfterSale(this, staff, eventCenter, successResult, trades);
                // 订阅快递助手并生成物流预警任务
                logisticsWarningBusiness.subscribeLogisticsEvent(staff, userWlbExpressTemplate, successResult, trades);
                result.put("addressReachable", TradeCanDeliverModel.build(trades));
//                eventCenter.fireEvent(this, new EventInfo("trade.expresstemplate.save").setArgs(new Object[]{staff}), trades);
                cloudSmartMatchHelper.updateTradeSmart(staff, YesNoEnum.NO.getValue(), trades);
                //记录日志
                saveDangerTradeTrace(staff, trades, uploadConsignPdd, "修改模板", "修改模板为:" + userWlbExpressTemplate.getName(), sidTemplateIdMap, idTemplateNameMap, true, null);
                //记录修改日志 用于资金流水重算查询
                gxTradeModifyTemplateLogService.saveOrderModifyLog(staff, trades);
                if (CollectionUtils.isNotEmpty(faildTrades)) {
                    throw new IllegalArgumentException("更换后模板与原模板对应快递网点不一致,订单号:" + JSONObject.toJSONString(faildTrades.stream().map(Trade::getSid).collect(Collectors.toList())));
                }
                return result;
            }
        };

        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArray), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "修改模板时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }

    @Resource
    private ITradePrintService tradePrintService;

    @RequestMapping(value = "/logistics/wlb/save", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'保存快递模板，系统单号：' + #sids", action = "save_wlb_express")
    public
    @ResponseBody
    Object saveWlbLogisticsExpress(SaveTemplateParams params, String api_name) throws NumberFormatException, SessionException {
        params.setStaff(getStaff());
        params.setType("wlb");
        return tradePrintService.saveAnyLogisticsExpress(params);
    }

    private String convertWaveIds2Sids(Staff staff, String waveIds, String sids) {
        if (StringUtils.isEmpty(waveIds) || StringUtils.isNotEmpty(sids)) {
            return sids;
        }
        List<Long> waveIdList = ArrayUtils.toLongList(waveIds);
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIdList.toArray(new Long[0]));
        List<Long> pickedWaveIds = waves.stream().filter(wave -> !Objects.equal(Wave.DISTRIBUTION_STATUS_NONE, wave.getDistributionStatus())).map(Wave::getId).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(pickedWaveIds), String.format("波次%s波次状态不是【未拣选】状态", pickedWaveIds));
        List<Long> sidList = tradeWaveService.querySidsByWaveIds(staff, waveIdList);
        Assert.notEmpty(sidList, "波次可修改订单为空");
        return StringUtils.join(sidList, ",");
    }

    @RequestMapping(value = "/logistics/jdalpha/save", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'保存快递模板，系统单号：' + #sids", action = "save_jdalpha_express")
    public
    @ResponseBody
    Object saveJdalphaLogisticsExpress(SaveTemplateParams params, String api_name) throws NumberFormatException, SessionException {
        params.setStaff(getStaff());
        params.setType("jdAlpha");
        return tradePrintService.saveAnyLogisticsExpress(params);
    }

    @RequestMapping(value = "/logistics/cloud/save", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'保存快递模板，系统单号：' + #sids", action = "save_cloud_express")
    public
    @ResponseBody
    Object saveCloudLogisticsExpress(SaveTemplateParams params, String api_name) throws NumberFormatException, SessionException {
        params.setStaff(getStaff());
        params.setType("cloud");
        return tradePrintService.saveAnyLogisticsExpress(params);
    }


    /**
     * 清空模版-支持批量
     */
    @RequestMapping(value = "/logistics/wlb/delete", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'清空快递模板，系统单号：' + #sids", action = "delete_wlb_express")
    public
    @ResponseBody
    Object deleteWlbLogisticsExpress(String sids, String queryId, String checkPdd, String waveIds, String api_name) throws NumberFormatException, SessionException {
        sids = convertWaveIds2Sids(getStaff(), waveIds, sids);
        return deleteAnyLogisticsExpress(sids, waveIds, queryId, checkPdd);
    }

    /**
     * 批量修改快递模版（查询结果中的订单）
     * 包含清空模版
     *
     * @param params       查询参数
     * @param templateId   模版id
     * @param queryId      查询id
     * @param checkPdd     是否是pdd
     * @param templateType 模版类型 null或0 其他 1-普通面单 2- 京东手工单
     * @param bizType      业务类型 0 修改模版 1-清空模版
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/logistics/wlb/batch/update", method = RequestMethod.POST)
    @ResponseBody
    public Object batchUpdateWlbLogistics(TradeControllerParams params,
                                          String templateId,
                                          String queryId,
                                          String checkPdd,
                                          Integer templateType,
                                          Integer bizType,
                                          String cancelOutsid,
                                          String needGetWaybill,
                                          Long logisticsCompanyId,
                                          String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_MODIFY_EXPRESS), "上一次批量修改快递模版还未执行完毕，请稍等！");
        if (bizType == 0) {
            //修改模版
            eventCenter.fireEvent(this, new EventInfo("batch.update.logistics").setArgs(new Object[]{staff, params, templateId, queryId, checkPdd, templateType, cancelOutsid, needGetWaybill, logisticsCompanyId}), null);
        } else if (bizType == 1) {
            //清空模版
            eventCenter.fireEvent(this, new EventInfo("batch.delete.logistics").setArgs(new Object[]{staff, params, queryId, checkPdd, logisticsCompanyId}), null);
        }
        return successResponse();
    }

    /**
     * 波次日志保存快递模板
     *
     * @param waveIds
     * @param staff
     * @param name
     */
    private void buildWaveTraceBySaveExpress(String waveIds, Staff staff, String name) {
        if (StringUtils.isEmpty(waveIds)) {
            return;
        }
        List<Long> waveIdList = ArrayUtils.toLongList(waveIds);
        waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waveIdList, WaveTraceOperateEnum.UPDATE_EXPRESS, "修改模板为:" + name));
    }

    /**
     * 校验波次订单是否允许修改模板
     */
    private List<Trade> checkUpdateTemplateByWave(String templateId, String queryId, List<Trade> tradeList, TradeConfig tradeConfig) {
        if (queryId != null &&
                Lists.newArrayList(SystemTradeQueryParamsContext.QUERY_WAVE_PRINT.toString(), SystemTradeQueryParamsContext.QUERY_WAVE_MANAGE.toString()).contains(queryId)) {
            tradeList = TradeStatusUtils.checkWaveUpdateTemplate(tradeList, StringUtils.isNotEmpty(templateId) ? Long.parseLong(templateId) : null, tradeConfig);
        }
        return tradeList;
    }

    /**
     * 修改运单号的模板
     */
    public List<Trade> upOutsidTemplate(Staff staff, List<Trade> tradeList, Long templateId, Integer templateType) {
        Long warehouseId = tradeList.get(0).getWarehouseId();
        templateType = templateType == null ? 0 : templateType;
        WarehouseTemplate warehouseTemplate =
                warehouseTemplateService.getWarehouseTemplate(staff, warehouseId, templateId, templateType == null ? 0 : templateType);
        Assert.isTrue(warehouseTemplate != null, "模板编号:" + templateId + ",仓库编号:" + warehouseId + ",");
        Long[] sidArr = tradeList.stream().map(Trade::getSid).toArray(Long[]::new);
        List<OutSidPool> outSidPools = outSidPoolService.queryBySids(staff, sidArr, null, null);
        Map<Long, OutSidPool> outSidPoolMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outSidPools)) {
            outSidPoolMap = outSidPools.stream().collect(Collectors.toMap(OutSidPool::getSid, o -> o, (o1, o2) -> o1));
        }
        //先过滤一下
        List<Trade> fieltTrade = new ArrayList<>();
        List<Trade> faildTrades = new ArrayList<>();
        List<OutSidPool> upOutsidPools = new ArrayList<>();
        for (Trade trade : tradeList) {
            OutSidPool outSidPool = outSidPoolMap.get(trade.getSid());
            if (templateId.equals(trade.getTemplateId())) {
                //模板一样
                fieltTrade.add(trade);
            } else {
                //模板不一样
                if (StringUtils.isNoneBlank(trade.getOutSid()) && outSidPool != null) {
                    //有运单号
                    if (outSidPool.getBranchId().equals(warehouseTemplate.getBranchId()) && outSidPool.getBranchAddressId().equals(warehouseTemplate.getBranchAddressId())) {
                        //单号网点一致修改面单池模板 保留单号
                        OutSidPool up = new OutSidPool();
                        up.setId(outSidPool.getId());
                        up.setTemplateId(warehouseTemplate.getAddressTemplateId());
                        upOutsidPools.add(up);
                    } else {
                        //单号网点不一致修改面单池模板
                        faildTrades.add(trade);
                    }
                }
                //模板不一样 没有运单号就不管修改订单模板
            }
        }
        if (CollectionUtils.isNotEmpty(fieltTrade)) {
            tradeList.removeAll(fieltTrade);
        }
        if (CollectionUtils.isNotEmpty(faildTrades)) {
            tradeList.removeAll(faildTrades);
        }
        outSidPoolService.batchUpdate(staff, upOutsidPools);
        logger.debug(LogHelper.buildLog(staff, "修改面单池模板结束,修改数量:" + upOutsidPools.size()));
        return faildTrades;
    }

    /**
     * 清空模版
     */
    public Object deleteAnyLogisticsExpress(final String sids, final String waveIds, final String queryId, final String checkPdd) throws NumberFormatException,
            SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        final Long[] sidArray = ArrayUtils.toLongArray(sids);
        Assert.isTrue(sidArray.length > 0, "请至少输入一笔订单");
        final Staff staff = getStaff();
        ILockCallback<Object> lockCallback = () -> {
            // 查询是否有快递模版，有模版的才能清空，否则跳过
            List<Trade> tradeList = printPageSearchService.queryBySids(staff, false, Arrays.asList(sidArray));
            List<Trade> deleteTrades = tradeList.stream().filter(trade -> trade.getTemplateId() != null && trade.getTemplateId() > 0).collect(Collectors.toList());
            Assert.isTrue(deleteTrades.size() > 0, "所选订单没有快递模版无法清空!");
            List<Trade> haveSplitGetTradeList = deleteTrades.stream()
                    .filter(trade -> StringUtils.isNotEmpty(trade.getOutSid()) && TradeUtils.haveAllSplitGetTag(trade))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(haveSplitGetTradeList)) {
                throw new RuntimeException("通过拆分订单获取子母单的订单不支持更换快递，需要先回收单号，才能更换快递！");
            }
            List<Trade> mpUpdateTagTradeList = deleteTrades.stream()
                    .filter(TradeUtils::haveMPUpdateTag)
                    .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mpUpdateTagTradeList)) {
                throw new IllegalArgumentException("更换快递模板系统内会回收单号，导致修改运单号的包裹无法打印面单发货，不能更换快递模板！");
            }
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            if (tradeConfig.getAllowChangeTemplate() == 0) {
                // 开启发货后pdd订单不允许修改快递单号
                TradeStatusUtils.checkUpdateTemplatePdd(deleteTrades);
            }
            TradeStatusUtils.updateTemplateCheckJitx(tradeList, "清空模板");
            // 校验订单是否允许修改模板  只在queryId = 62时校验
            if (queryId != null && queryId.equals(SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED + "")) {
                deleteTrades = TradeStatusUtils.checkUpdateTemplate(deleteTrades, tradeConfig);
                if (CollectionUtils.isEmpty(deleteTrades)) {
                    return successResponse();
                }
            }
            tradeList = checkUpdateTemplateByWave(null, queryId, tradeList, tradeConfig);
            List<Long> uploadConsignPdd = new ArrayList<>();
            if ("1".equals(checkPdd)) {
                TradeStatusUtils.checkUpTemplateOutsid(tradeList, "清空模板", true);
            } else {
                uploadConsignPdd = TradeStatusUtils.checkUpTemplateOutsid(tradeList, "清空模板", false);
            }

            //获取到历史模版
            Map<Long, Long> sidTemplateIdMap = deleteTrades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getTemplateId, (k1, k2) -> k2));
            List<Long> collect = deleteTrades.stream().map(Trade::getTemplateId).filter(java.util.Objects::nonNull).distinct().collect(Collectors.toList());
            Long[] longs = collect.toArray(new Long[]{});
            Map<Long, String> idTemplateNameMap = userWlbExpressTemplateService.queryNamesByIds(staff, longs);

            //取消面单号
            deletTempleteOutSid(staff, deleteTrades);
            //最后更改Trade模版Id，type
            List<Trade> updateTrades = Lists.newArrayListWithCapacity(deleteTrades.size());
            deleteTrades.forEach(trade -> {
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(trade.getSid());
                updateTrade.setTemplateId(-1L);
                updateTrade.setLogisticsCompanyId(0L);
                updateTrade.setTemplateType(0);
                updateTrade.setV(trade.getV());
                if(TradeUtils.isFxOrMixTrade(trade)){
                    updateTrade.removeV(V_IF_FX_APPOINT_TEMPLATE_ID);
                }
                updateTrades.add(updateTrade);
            });
            tradePtService.saveByTrades(staff, updateTrades);
            tradeUpdateService.updateTrades(staff, updateTrades);
            updateWaveExpressId(staff, waveIds, true, 0L);

            JSONObject result = new JSONObject();
            result.put("addressReachable", TradeCanDeliverModel.build(deleteTrades));
//                eventCenter.fireEvent(this, new EventInfo("trade.expresstemplate.remove").setArgs(new Object[]{staff}), deleteTrades);
            cloudSmartMatchHelper.updateTradeSmart(staff, YesNoEnum.NO.getValue(), deleteTrades);
            //记录日志
            saveDangerTradeTrace(staff, deleteTrades, uploadConsignPdd, "修改模板", "清空模板", sidTemplateIdMap, idTemplateNameMap, false, null);
            return result;
        };

        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArray), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "清空模板时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }

    @RequestMapping(value = "/address/reachable", method = RequestMethod.GET)
    @ResponseBody
    public Object addressReachable(String receiverState, String receiverCity, String receiverDistrict, String receiverAddress, String expressCompanyId, String api_name) throws SessionException {
        if (StringUtils.isEmpty(receiverState) || StringUtils.isEmpty(receiverCity)
                || StringUtils.isEmpty(receiverAddress) || StringUtils.isEmpty(expressCompanyId))
            throw new IllegalArgumentException("请求参数不对，请重新输入！");

        Staff staff = getStaff();
        if (staff.getUsers().size() == 0)
            throw new IllegalArgumentException("您无此权限操作");
        JSONObject result = new JSONObject();

        Integer canDeliver = sysTradeService.addressReachable(staff, staff.getUsers().get(0), expressCompanyId, buildReceiverAddress(receiverState, receiverCity, receiverDistrict, receiverAddress));
        result.put("result", canDeliver);
        return result;
    }

    /**
     * 批量查询某个收货地址，有多少个快递公司可送达
     *
     * @param warehouseId
     * @param receiverState
     * @param receiverCity
     * @param receiverDistrict
     * @param receiverAddress
     * @param api_name
     * @return
     * @throws com.raycloud.dmj.session.SessionException
     */
    @RequestMapping(value = "/address/reachable/batch", method = RequestMethod.GET)
    @ResponseBody
    public Object addressReachablebatch(Long warehouseId, String receiverState, String receiverCity, String receiverDistrict, String receiverAddress, String api_name) throws SessionException {
        if (StringUtils.isEmpty(receiverState) || StringUtils.isEmpty(receiverCity)
                || StringUtils.isEmpty(receiverAddress))
            throw new IllegalArgumentException("请求参数不对，请重新输入！");

        Assert.notNull(warehouseId, "请传入warehouseId参数");

        Staff staff = getStaff();
        if (staff.getUsers().size() == 0)
            throw new IllegalArgumentException("您无此权限操作");
        List<UserExpressTemplate> templates = warehouseTemplateService.getSimpleTemplatesByWarehouseId(staff, warehouseId);
        if (templates == null || templates.size() == 0) return Collections.emptyList();

        Trade address = buildReceiverAddress(receiverState, receiverCity, receiverDistrict, receiverAddress);

        List<String> companyCodes = new ArrayList<String>();
        for (UserExpressTemplate template : templates) {
            if (companyCodes.contains(String.valueOf(template.getExpressId())))
                continue;
            companyCodes.add(template.getExpressId().toString());
        }

        Map<Long, ExpressCompany> idMap = expressCompanyService.getExpressCompanyIdMap();
        Map<Long, List<UserExpressTemplate>> tplMap = WlbExpressHelper.getExpressIdMapUserExpressWlb(templates);
        //接口不要了过期了
//        List<AddressReachableResult> results = sysTradeService.addressReachable(staff, staff.getUsers().get(0), StringUtils.join(companyCodes, ","), address);
        JSONArray ja = new JSONArray();
//        for (AddressReachableResult result : results) {
//            JSONObject jo = new JSONObject();
//            jo.put("partnerId", result.getPartnerId());
//            jo.put("reachable", result.getReachable());
//            jo.put("serviceType", result.getServiceType());
//            if (idMap.containsKey(result.getPartnerId())) {
//                jo.put("partnerName", idMap.get(result.getPartnerId()).getCustomName());
//            }
//            jo.put("templates", tplMap.get(result.getPartnerId()));
//            ja.add(jo);
//        }
        return ja;
    }

    /**
     * 取消电子面单
     *
     * @param staff     公司
     * @param tradeList 订单列表
     */
    private WlbStatus cancelSidOutSid(Staff staff, List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return null;
        }
        return WayBillCancelParamHelper.cancelTidSysTidOutSid(staff, userWlbExpressCompanyAdapterService,
                userWlbExpressTemplateService, tradeList, -3, false, false);
    }

    private Trade buildReceiverAddress(String receiverState, String receiverCity, String receiverDistrict, String receiverAddress) {
        Trade address = new Trade();
        address.setReceiverState(receiverState);
        address.setReceiverCity(receiverCity);
        address.setReceiverDistrict(receiverDistrict);
        address.setReceiverAddress(receiverAddress);
        return address;
    }

    /**
     * 优化返回的结果
     */
    private void optimizePrintTradeLogResult(PrintTradeLogs printTradeLogs) {
        if (printTradeLogs == null) {
            return;
        }
        List<PrintTradeLog> tradeLogList = printTradeLogs.getList();
        if (CollectionUtils.isEmpty(tradeLogList)) {
            return;
        }
        for (PrintTradeLog log : tradeLogList) {
            List<PrintTradeLogDetail> details = PrintTradeLoghelper.distinctPrintTradeLogDetails(log.getDetails());
            if (CollectionUtils.isNotEmpty(details)) {
                // 根据companyId筛掉不属于自己的脏数据
                details = details.stream().filter(detail -> log.getCompanyId().equals(detail.getCompanyId())).collect(Collectors.toList());
            }
            log.setDetailsLen(details.size());
            log.setDetails(null);
        }
    }

    /**
     * 根据queryId为31的编号查询待校验的订单
     */
    private PrintTradeLogs search4Validate(Staff staff, PrintTradeLogQueryParams params, Page page) {
        String matchStr = "^[0-9]+(,[0-9]+)*$";
        //系统单号检查
        if (StringUtils.isNotBlank(params.getSid())) {
            Assert.isTrue(params.getSid().matches(matchStr), "请输入正确的系统单号");
        }
        //内部单号检查
        if (StringUtils.isNotBlank(params.getShortId())) {
            Assert.isTrue(params.getShortId().matches(matchStr), "请输入正确的内部单号");
        }
        return printTradeLogService.searchByTime(staff, params, page);
    }

    static List<BatchExecutException.ExceptionReport> getErrorReports(Staff staff, List<Trade> tradeList, String ErrorMsg) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return Collections.emptyList();
        }
        String ErrorCode = "20150";
        String SubMsg = "系统异常订单";
        List<BatchExecutException.ExceptionReport> reports = new ArrayList<>(tradeList.size());
        for (Trade trade : tradeList) {
            BatchExecutException.ExceptionReport report = new BatchExecutException.ExceptionReport().setErrorCode(ErrorCode).setErrorMsg(ErrorMsg).setSubMsg(SubMsg).setTaobaoId(
                    trade.getTaobaoId()).setTid(trade.getTid()).setSid(trade.getSid().toString());
            reports.add(report);
        }
        return reports;
    }

    TradeQueryParams convertParams(TradeControllerParams params, Page page, Sort sort) {
        TradeQueryParams query = TradeQueryParams.copyParams(params);
        query.setPage(page);
        query.setSort(sort);
        return query;
    }

    /**
     * 在修改模板前检查是否允许修改
     *
     * @param tradeList  订单列表
     * @param templateId 模板id
     * @param errorSids  不允许修改的订单sids
     * @return 允许修改模板的trade
     */
    private List<Trade> checkTemplate4Allow(List<Trade> tradeList, Long templateId, List<String> errorSids) {
//         2018年5月11日 去除校验
//        19年0321开始校验 且增加校验
        List<Trade> allowTrades = new ArrayList<Trade>();
        for (Trade trade : tradeList) {
            //模板一样
            if (trade.getTemplateId() != null && trade.getTemplateId().equals(templateId)) {
                allowTrades.add(trade);
                continue;
            }
            if (TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()).equals(1)) {
                errorSids.add("订单号:" + trade.getSid() + "不允许更改模板!");
                continue;
            }
            //手动修改只允许待审核和待打印且未进入波次的订单修改模板
            //待审核 待财审 待人工审核 都是待审核
            if (Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_MANUAL_AUDIT.equals(trade.getSysStatus())) {
                allowTrades.add(trade);
            } else if (((Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(trade.getSysStatus())) && !DataUtils.checkLongNotEmpty(trade.getWaveId()))) {
                //审核完成 和待打印快递单 为待打印订单 且未进入波次则可以修改模板
                allowTrades.add(trade);
            } else {
                errorSids.add("订单号:" + trade.getSid() + "不允许更改模板!");
            }
        }
        return allowTrades;
    }

    private void setBoxingListPrinted(Staff staff, Long sid, List<String> boxNoList) {
        List<Trade> trades = printPageSearchService.queryBySids(staff, Arrays.asList(sid));
        if (null != trades && trades.size() == 1) {
            Trade trade = trades.get(0);
            String boxingList = trade.getBoxingList();

            if (StringUtils.isNotBlank(boxingList)) {
                List<BoxingListItem> boxingListItems = JSON.parseArray(boxingList, BoxingListItem.class);
                if (CollectionUtils.isEmpty(boxNoList)) {
                    for (BoxingListItem item : boxingListItems) {
                        item.setIsPrint(1);
                    }
                } else {
                    Set<String> boxNoSet = new HashSet<String>(boxNoList);
                    for (BoxingListItem item : boxingListItems) {
                        if (boxNoSet.contains(item.getBoxNo())) {
                            item.setIsPrint(1);
                        }
                    }
                }
                trade.setBoxingList(JSON.toJSONString(boxingListItems));
                sysTradeService.updateBoxingList(staff, (TbTrade) trade, false);
            }
        }
    }

    private Object printBoxingListData(Staff staff, Long sid, List<String> boxNos, Integer dataSource) {
        Trade trade = null;
        if (dataSource == null || dataSource == 0) {
            List<Trade> trades = printPageSearchService.queryBySids(staff, true, Collections.singletonList(sid));
            Assert.isTrue(CollectionUtils.isNotEmpty(trades), String.format("未找到sid=%s的订单", sid));
            trade = trades.get(0);
            //检查是否有未匹配的商品
            Collection<Long> unAllocatedSidList = PtTradeAndOrderUtils.checkOrderStatus(Collections.singletonList(trade));
            if (!CollectionUtils.isEmpty(unAllocatedSidList)) {
                JSONObject result = new JSONObject();
                result.put("isSuccess", Boolean.FALSE);
                result.put("sidList", unAllocatedSidList);
                return result;
            }
        }

        UserGetterTemplate userGetterTemplate = userGetterTemplateService.getDefault(staff, 0);

        if (userGetterTemplate == null) {
            throw new IllegalArgumentException("当前用户找不到拿货单模板");
        }

        List<Map<String, Object>> fieldValues = boxingListPrintService.getFieldValues(staff, trade, userGetterTemplate, boxNos, dataSource);

        JSONObject result = new JSONObject();
        result.put("isSuccess", Boolean.TRUE);
        result.put("fieldValues", fieldValues);
        result.put("template", userGetterTemplate);

        List<UserTemplateLockPrintModel> templateLockPrintList = TemplateHelper.getUserGetterTemplateLockPrintList(userGetterTemplate);
        result.put("templateLockPrintList", templateLockPrintList);

        return result;
    }

    /**
     * 手写电子面单快递单
     *
     * @param sids
     * @throws com.raycloud.dmj.session.SessionException
     */
    private void handwriteWlbEnd(String sids, Staff staff) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");

        Long[] sidArray = ArrayUtils.toLongArray(sids);

        IPrintRequest printRequest = new EndPrintTimeRequest(sidArray, "手写快递", null);
        printRequest.setTemplateKind(EnumTemplateKind.WLB.getValue());
        printRequest.setClientIp(IpUtils.getClientIP(request));

        printAdapterService.endPrintTime(staff, printRequest);

        OpLogHelper.recodeOpLog(opLogService, request, staff, "handwriteWlbEnd", sids, "手写电子面单快递单，系统单号：" + sids, null);
    }

    void initSysItemAndSkuList(Staff staff, PrintInvoicesDataParam printInvoicesDataParam, Long[] sysItemIdArray, Long[] sysSkuIdArray, List<Long> sysItemIdList, List<Long> sysSkuIdList, StringBuilder ids, String sids, Integer suitType, String source) {
        PrintMerchantCodeDataParam param = (PrintMerchantCodeDataParam) printInvoicesDataParam;
        if (StringUtils.isNotBlank(param.getPrintNums())) {
            int maxNum;
            try {
                String maxNumStr = cache.get(MERCHANT_NUM_CACHE_KEY);
                maxNum = StringUtils.isNotBlank(maxNumStr) ? Integer.parseInt(maxNumStr) : 5000;
            } catch (Exception e) {
                logger.debug(LogHelper.buildLog(staff, "缓存读取失败!" + e.getMessage()));
                maxNum = 5000;
            }
            maxNum = maxNum > 0 ? maxNum : 5000;
            String printNums = param.getPrintNums();
            String[] printNumArr = printNums.split(",");
            long sum = 0L;
            for (String s : printNumArr) {
                int num;
                try {
                    num = Integer.parseInt(s);
                } catch (Exception e) {
                    throw new IllegalArgumentException("校验打印数量失败:数据类型转换失败," + e.getMessage());
                }
                if (num < 0 || num > maxNum) {
                    logger.debug("打印数量参数超出限制,数量:" + printNums);
                    throw new IllegalArgumentException("编码打印数量过大,单个商品打印数量超出限制(不超过" + maxNum + "),错误的打印数量:" + s);
                }
                sum += num;
            }
            Assert.isTrue(sum < 100000L, "一次打印总数量不能不能超过:" + 100000L);
        }
        if ((sysItemIdArray == null || sysSkuIdArray == null) && sids != null) {
            //订单取商品
            printHelperService.getItemIdBySids(staff, sids, sysItemIdList, sysSkuIdList, ids, suitType);
            printInvoicesDataParam.setSysItemIds(Strings.join(",", sysItemIdList));
            printInvoicesDataParam.setSysSkuIds(Strings.join(",", sysSkuIdList));
        } else if ((sysItemIdArray == null || sysSkuIdArray == null) && param.getWorkOrderIds() != null && "aftersale".equals(source)) {
            //售后工单取商品
            if (param.getSysSkuIds() != null && param.getSysItemIds() != null) {
                printInvoicesDataParam.setSysItemIds(param.getSysItemIds());
                printInvoicesDataParam.setSysSkuIds(param.getSysSkuIds());
                printInvoicesDataParam.setPrintNums(param.getPrintNums());
            } else {
                printHelperService.getItemIdByWorkOrderIds(staff, param.getWorkOrderIds(), sysItemIdList, sysSkuIdList, ids);
                printInvoicesDataParam.setSysItemIds(Strings.join(",", sysItemIdList));
                printInvoicesDataParam.setSysSkuIds(Strings.join(",", sysSkuIdList));
            }

        } else {
            if (sysItemIdArray == null || sysSkuIdArray == null) {
                throw new IllegalArgumentException("不根据订单打印必须传入商品id和规格id!!!");
            }
            List<String> printNums = new ArrayList<>();
            for (int i = 0; i < sysItemIdArray.length; i++) {
                if (sysSkuIdArray[i] != null && sysSkuIdArray[i] > 0) {
                    sysSkuIdList.add(sysSkuIdArray[i]);
                    ids.append(sysSkuIdArray[i]).append(",");
                    printNums.add("1");
                    continue;
                }
                if (sysItemIdArray[i] > 0) {
                    ids.append(sysItemIdArray[i]).append(",");
                    sysItemIdList.add(sysItemIdArray[i]);
                    printNums.add("1");
                }
            }
            if (ids.length() > 0) {
                ids.deleteCharAt(ids.length() - 1);
            }
            if (StringUtils.isBlank(param.getPrintNums()) || param.getPrintNums().split(",").length == 0) {
                param.setPrintNums(StringUtils.join(printNums, ","));
            }
        }
    }

    public void makeSystemFilterLog(Staff staff, JSONObject printData, String sids) {
        if (printData == null) {
            return;
        }
        String filterMessage = printData.getString("filterMessage");
        if (StringUtils.isNotBlank(filterMessage)) {
            OpLogHelper.recodeOpLog(opLogService, request, staff, "printExpressDataEnd", sids, filterMessage, null);
        }
    }

    /**
     * 维修单打印数据
     *
     * @param templateId
     * @param ids
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/repair/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printRepairData(Long templateId, String ids, Boolean onlyShowPrinterList, Boolean mandatory, String api_name, String source) throws Exception {
        if (onlyShowPrinterList == null) {
            onlyShowPrinterList = false;
        }
        if (StringUtils.isEmpty(ids) || templateId == null || templateId == 0L) {
            throw new IllegalArgumentException("需要打印的数据为空！");
        }
        PrintInvoicesDataParam param = new PrintInvoicesDataParam();
        param.setTemplateId(templateId);
        final Long[] idArray = ArrayUtils.toLongArray(ids);
        final Staff staff = getStaff();
        return new AbstractInvoicesPrintData() {
            @Override
            protected Object getTargetData() {
                return repairOrderService.getPrintData(staff, ArrayUtils.toIntegerArray("0,1,2,3,4"), idArray);
            }

            @Override
            protected Object verifyData() {
                return successResponse();
            }
        }.getPrintData(param, staff, ids, onlyShowPrinterList, mandatory, 1, EnumInvoicesType.REPAIR,
                EnumTemplateKind.REPAIR, "维修单", userInvoicesTemplateService, printAdapterService, printConfigService, source);
    }

    /**
     * 自定义维修单打印
     *
     * @param templateId
     * @param ids
     * @param onlyShowPrinterList
     * @param mandatory
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/repair/data/custom", method = RequestMethod.POST)
    @ResponseBody
    public Object printRepairDataCustom(Long templateId, String ids, Boolean onlyShowPrinterList, Boolean mandatory, String api_name, String source) throws Exception {
        return printRepairData(templateId, ids, onlyShowPrinterList, mandatory, api_name, source);
    }

    /**
     * 订单操作公司内部数据统计
     *
     * @param staff
     * @param request
     */
    public void dataOperation(Staff staff, HttpServletRequest request) {
        try {
            com.raycloud.eagle.audit.AuditDataUpload.operation(request, "自研平台", "快麦ERP企业版", "打印模块",
                    staff.getUser().getTaobaoId().toString(), staff.getUser().getNick(), staff.getName(), staff.getId().toString(), "订单打印", null);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "打印订单操作回传给公司内部审计系统出错"));
        }
    }


    @RequestMapping(value = "/print/scan/print", method = RequestMethod.POST)
    @ResponseBody
    public Object scanPrint(String sids, Long templateId, String printBatchId, String printer, Boolean exceptionPrint, String api_name, String validateKey) throws Exception {
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Staff staff = getStaff();
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sidArray));
        TemplateHelper.validatePrintTrade(staff, trades);
        return getPrintResult(staff, trades, templateId, printBatchId, printer, exceptionPrint, api_name, validateKey, SCAN_PRINT.name());
    }

    private PrintResult getPrintResult(Staff staff, List<Trade> trades, Long templateId, String printBatchId, String printer, Boolean exceptionPrint, String api_name, String validateKey, String waybillPathName) {
        TradeTemplateTypeMap tradeTemplateTypeMap;
        try {
            tradeTemplateTypeMap = batchGetWaybillCode(staff, trades);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "打印获取单号异常"), e);
            throw new IllegalArgumentException("获取单号失败！原因：" + e.getMessage());
        }
        trades.forEach(e -> {
            try {
                if (!cache.add(CACHE_PRINT_LOCK_KEY_ + e.getSid(), String.valueOf(e.getSid()), 10)) {
                    //10秒钟的拦截时间，add不成功，则说明不允许继续打印
                    throw new IllegalArgumentException("订单已经在打印中，请勿重复打印");
                }
            } catch (CacheException cacheException) {
                cacheException.printStackTrace();
            }
        });
        PrintResult printResult = new PrintResult();
        List<Object> printData = Lists.newArrayList();
        try {
            for (Map.Entry<Long, List<Trade>> entry : tradeTemplateTypeMap.getWlbTradesMap().entrySet()) {
                printResult.setPrintTemplateType(PrintTemplateType.WLB);
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateId, false);
                Object result;
                if (WlbTemplateTypeEnum.isJdLop(userWlbExpressTemplate.getWlbTemplateType())) {
                    result = getJdlopPrintData(StringUtils.join(TradeUtils.toSids(entry.getValue())), templateId, printBatchId, printer, new WavePrintParam(), null, false, null, exceptionPrint, false, 1, validateKey, null, api_name, waybillPathName);
                } else {
                    result = printWlbDataEnd(StringUtils.join(TradeUtils.toSids(entry.getValue())), templateId, printBatchId, printer, new WavePrintParam(), null, false, null, exceptionPrint, api_name, 1, validateKey, null, false, waybillPathName);
                }
                printData.add(result);
            }
            for (Map.Entry<Long, List<Trade>> entry : tradeTemplateTypeMap.getCloudTradesMap().entrySet()) {
                printData.add(printCloudDataEnd(StringUtils.join(TradeUtils.toSids(entry.getValue())), templateId, printBatchId, printer, new WavePrintParam(), null, false, null, exceptionPrint, api_name, 1, validateKey, null, null, false, waybillPathName, null));
                printResult.setPrintTemplateType(PrintTemplateType.CLOUD);
            }
            for (Map.Entry<Long, List<Trade>> entry : tradeTemplateTypeMap.getNormalTradesMap().entrySet()) {
                printData.add(printExpressDataEnd(StringUtils.join(TradeUtils.toSids(entry.getValue())), templateId, null, printBatchId, printer, null, null, false, null, exceptionPrint, api_name, validateKey));
                printResult.setPrintTemplateType(PrintTemplateType.NORMAL);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "打印异常"), e);
            throw new IllegalArgumentException("打印失败！原因：" + e.getMessage());
        }
        printResult.setPrintData(printData);
        return printResult;
    }

    /**
     * 订单转发打印
     *
     * @param printBatchId   时间戳后端用来生成记录
     * @param printer        打印机名称，记录中会用到
     * @param exceptionPrint 异常打印,跳过部分异常检验，控制订单操作记录
     */
    @RequestMapping(value = "/transpond/print", method = RequestMethod.POST)
    @ResponseBody
    public Object transpondPrint(String sids, Long warehouseId, Integer templateType, Long templateId, String printBatchId, String printer, Boolean exceptionPrint, String api_name, String validateKey, Long logisticsCompanyId) throws Exception {
        Assert.notNull(warehouseId, "请选择仓库！");
        Staff staff = getStaff();

        // 查询订单
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sidArray));
        // 校验打印的订单，如果订单中有包含退款中的订单，则抛出异常
        TemplateHelper.validatePrintTrade(staff, trades, warehouseId);

        List<Trade> officialPickUp1688Trades = trades.stream().filter(PlatformUtils::is1688OfficialPickUp).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(officialPickUp1688Trades)) {
            throw new IllegalArgumentException("1688官方物流订单不支持快递转发！");
        }

        // 匹配模板： 根据logisticsCompanyId匹配订单模板ID
        String logisticsCompanyName = null;
        if (logisticsCompanyId != null && logisticsCompanyId > 0) {
            UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyBusiness.queryById(staff, logisticsCompanyId, true);
            Map<Long, UserExpressTemplate> longUserExpressTemplateMap = userLogisticsCompanyTemplateService.matchOldTemplateProxy(staff, trades, userLogisticsCompany);
            for (Trade trade : trades) {
                if (longUserExpressTemplateMap.get(trade.getSid()) == null) {
                    continue;
                }
                templateId = longUserExpressTemplateMap.get(trade.getSid()).getId();
                templateType = longUserExpressTemplateMap.get(trade.getSid()).getTemplateType();
            }
            logisticsCompanyName = userLogisticsCompany.getName();
        }
        Assert.notNull(templateType, "请选择模板！");
        Assert.notNull(templateId, "请选择模板！");

        // 记录转发日志
        List<TranspondPrintLog> logs = buildTranspondPrintLogs(staff, trades, printBatchId, templateType, templateId, logisticsCompanyId, logisticsCompanyName);

        // 更新订单模板
        StringBuilder log = new StringBuilder("快递转发打印,订单号：").append(sids);
        for (Trade t : trades) {
            String logisticsCompany = t.getLogisticsCompanyId() != null && t.getLogisticsCompanyId() > 0 ? t.getLogisticsCompanyName() : t.getTemplateName();
            log.append(",原").append(t.getLogisticsCompanyId() != null && t.getLogisticsCompanyId() > 0 ? "快递公司：" : "模板：").append(StringUtils.trimToEmpty(logisticsCompany)).append(",原运单号：").append(t.getOutSid());
            t.setOldTemplateId(t.getTemplateId());
            t.setOldTemplateType(t.getTemplateType());
            t.setOldLogisticsCompanyId(t.getLogisticsCompanyId());
            t.setOldOutSid(t.getOutSid());
            t.setTemplateType(templateType);
            t.setTemplateId(templateId);
            t.setOutSid("");
            t.setLogisticsCompanyId(logisticsCompanyId);
            t.setLogisticsCompanyName(logisticsCompanyName);
        }


        Long finalTemplateId = templateId;
        String finalLogisticsCompanyName = logisticsCompanyName;
        ILockCallback<Object> lockCallback = () -> {
            // 回收通知物流预警
                List<OutSidPool> outSidPoolList =  WayBillCancelParamHelper.getOutSidPoolListPolicy(staff, null, sidArray,
                        outSidPoolService, null, EnumOutSidType.TYPE_TRADE.getValue());
                List<MultiPacksOutSidDetail> outSidDetails = defaultWayBillPostProcessor.buildRecycleOutSidDetail(staff, null, Arrays.asList(sidArray), outSidPoolList);

            try {
                //清空原运单信息
                deleteOutSidPool(staff, trades);
                updateTrade(staff, trades);
                //获取单号并返回结果
                PrintResult printResult = getPrintResult(staff, trades, finalTemplateId, printBatchId, printer, exceptionPrint, api_name, validateKey, TRANSPOND_PRINT.name());
                for (Trade t : trades) {
                    String logisticsCompany = StringUtils.isEmpty(finalLogisticsCompanyName) ? t.getTemplateName() : finalLogisticsCompanyName;
                    log.append(",新").append(StringUtils.isEmpty(finalLogisticsCompanyName) ? "模板：" : "快递公司：").append(StringUtils.trimToEmpty(logisticsCompany)).append(",新运单号：").append(t.getOutSid());
                }
                //记录修改日志 用于资金流水重算查询
                gxTradeModifyTemplateLogService.saveOrderModifyLog(staff, trades);
                //过滤出淘系订单
                List<Trade> tbTradeList = trades.stream().filter(trade -> CanResend(trade.getSource()) || (TradeUtils.isGxOrMixTrade(trade) && CanResend(trade.getSubSource()))).collect(Collectors.toList());
                //快递转发完成后自动重新发货
                if (!CollectionUtils.isEmpty(tbTradeList)) {
                    tradeService.asyncResend(staff, tbTradeList, IpUtils.getClientIP(request), 100);
                }
                OpLogHelper.recodeOpLog(opLogService, request, staff, "transpondPrint", sids, log.toString(), null);
                eventCenter.fireEvent(this, new EventInfo("trade.pt.transpond.print").setArgs(new Object[]{staff}), trades);

                // 保存转发记录
                saveTranspondPrintLog(staff, trades, logs);

                // 发送事件通知物流预警单号回收了
                if (CollectionUtils.isNotEmpty(outSidDetails)) {
                    eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.waybill.recycle").setArgs(new Object[]{staff, outSidDetails}), null);
                }
                return printResult;
            } catch (Exception e) {
                // 手动会滚outsid数据
                rollbackOutSidPool(staff, trades.get(0));

                // 恢复旧快递公司
                for (Trade t : trades) {
                    t.setLogisticsCompanyId(t.getOldLogisticsCompanyId());
                }
                updateTrade(staff, trades);
                throw e;
            }
        };
        return ptLockBusiness.ffLock(staff, Arrays.asList(sidArray), lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                "存在正在并发操作的订单，请稍后处理", "订单转发打印时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
    }


    private List<TranspondPrintLog> buildTranspondPrintLogs(Staff staff, List<Trade> trades, String printBatchId, Integer templateType, Long templateId, Long logisticsCompanyId, String logisticsCompanyName) {
        List<TranspondPrintLog> logs = new ArrayList<>();
        for (Trade trade : trades) {
            TranspondPrintLog transpondPrintLog = new TranspondPrintLog();
            transpondPrintLog.setSid(trade.getSid());
            transpondPrintLog.setPid(Long.parseLong(printBatchId));
            transpondPrintLog.setOldOutSid(trade.getOutSid());
            transpondPrintLog.setOldLogisticsCompanyId(trade.getLogisticsCompanyId());
            transpondPrintLog.setOldTemplateId(trade.getTemplateId());
            transpondPrintLog.setTemplateId(templateId);
            transpondPrintLog.setLogisticsCompanyId(logisticsCompanyId);
            // 填充老模板名称
            if (trade.getTemplateType() == 0) {
                UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQueryWithCache(staff, transpondPrintLog.getOldTemplateId(), false);
                transpondPrintLog.setOldTemplateName(userExpressTemplate == null ? null : userExpressTemplate.getName());
                transpondPrintLog.setOldLogisticsCompanyName(userExpressTemplate == null ? null : userExpressTemplate.getLogisticsCompanyName());
            }
            if (trade.getTemplateType() == 1) {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQueryWithCache(staff, transpondPrintLog.getOldTemplateId(), false);
                transpondPrintLog.setOldTemplateName(userWlbExpressTemplate == null ? null : userWlbExpressTemplate.getName());
                transpondPrintLog.setOldLogisticsCompanyName(userWlbExpressTemplate == null ? null : userWlbExpressTemplate.getLogisticsCompanyName());
            }
            // 填充新模板名称
            if (templateType == 0) {
                UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQueryWithCache(staff, transpondPrintLog.getTemplateId(), false);
                transpondPrintLog.setTemplateName(userExpressTemplate == null ? null : userExpressTemplate.getName());
            }
            if (templateType == 1) {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQueryWithCache(staff, transpondPrintLog.getTemplateId(), false);
                transpondPrintLog.setTemplateName(userWlbExpressTemplate == null ? null : userWlbExpressTemplate.getName());
            }
            transpondPrintLog.setLogisticsCompanyName(logisticsCompanyName);

            // 填充快递公司名称
            if (trade.getLogisticsCompanyId() != null && trade.getLogisticsCompanyId() > 0 && StringUtils.isBlank(transpondPrintLog.getOldLogisticsCompanyName())) {
                UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyService.queryById(staff, trade.getLogisticsCompanyId());
                transpondPrintLog.setOldLogisticsCompanyName(userLogisticsCompany == null ? null : userLogisticsCompany.getName());
            }
            logs.add(transpondPrintLog);
        }
        return logs;
    }


    private void saveTranspondPrintLog(Staff staff, List<Trade> trades, List<TranspondPrintLog> transpondPrintLogs) {
        // 打印成功数据
        Map<Long, Trade> successSidMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (v1, v2) -> v2));
        for (TranspondPrintLog log : transpondPrintLogs) {
            log.setOutSid(successSidMap.get(log.getSid()) == null ? null : successSidMap.get(log.getSid()).getOutSid());
        }
        eventCenter.fireEvent(this, new EventInfo("pt.transpond.print.log").setArgs(new Object[]{staff}), transpondPrintLogs);
    }

    private void deleteOutSidPool(Staff staff, List<Trade> trades) {
        outSidPoolService.batchUpdateByOutSid(staff, trades.stream()
                .map(trade -> {
                    OutSidPool outSidPool = new OutSidPool();
                    outSidPool.setOutSid(trade.getOldOutSid());
                    outSidPool.setStatus(1);
                    return outSidPool;
                })
                .collect(Collectors.toList()));

        // 删除一单多包数据
        MultiPacksPrintTradeLogDetailParams params = new MultiPacksPrintTradeLogDetailParams();
        params.setSid(trades.get(0).getSid());
        params.setTemplateId(trades.get(0).getOldTemplateId());
        multiPacksPrintTradeLogService.deleteDetail(staff, params);

        multiPacksPrintTradeLogService.deleteBySid(staff,trades.get(0).getSid());
    }

    /**
     * @description: 手动回滚outsid数据
     * @author: tanyi
     * @date: 2024-11-05 16:13
     */
    private void rollbackOutSidPool(Staff staff, Trade trade) {
        OutSidPool outSidPool = new OutSidPool();
        outSidPool.setSid(trade.getSid());
        outSidPoolService.batchUpdateEnableStatus(staff, Collections.singletonList(outSidPool));


        // 删除一单多包数据
        MultiPacksPrintTradeLogDetailParams params = new MultiPacksPrintTradeLogDetailParams();
        params.setSid(trade.getSid());
        params.setTemplateId(trade.getOldTemplateId());
        params.setQueryAll(true);
        List<MultiPacksPrintTradeLogDetail> details = multiPacksPrintTradeLogService.queryDetails(staff, params);
        if (CollectionUtils.isNotEmpty(details)) {
            multiPacksPrintTradeLogService.enableDetailByIds(staff, details.stream().map(MultiPacksPrintTradeLogDetail::getId).collect(Collectors.toList()));
        }
    }

    private void updateTrade(Staff staff, List<Trade> trades) {
        if (trades.get(0).getLogisticsCompanyId() != null && trades.get(0).getLogisticsCompanyId() > 0) {
            Trade trade = new TbTrade();
            trade.setSid(trades.get(0).getSid());
            trade.setLogisticsCompanyId(trades.get(0).getLogisticsCompanyId());
            tradePtService.saveByTrades(staff, Lists.newArrayList(trade));
            tradeUpdateService.updateTrades(staff, trade);
        }
    }

    private boolean CanResend(String source) {
        return CommonConstants.PLAT_FORM_TYPE_1688.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_JD.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_FX.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_PDD.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_FXG.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(source);
    }

    /**
     * 商家编码打印
     */
    @RequestMapping(value = "/print/caigou/merchantCode/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printCaigouMerchantCodeData(PrintCaigouMerchantCodeParam param) throws Exception {
        return printDataSaveJobService.printCaigouMerchantCodeData(param, getStaff());
    }

    /**
     * 打印异步接口
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/asyn/createJob", method = RequestMethod.POST)
    @ResponseBody
    public Object printAysnCreateJob(PrintCaigouMerchantCodeParam param, Boolean isDelimited) throws Exception {
        printDataSaveJobService.createPrintDataJob(param, getStaff(), isDelimited);
        return new JSONObject();
    }

    /**
     * 打印数据列表
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/asyn/getJobs", method = RequestMethod.POST)
    @ResponseBody
    public Object printAysnGetJobs(PrintDataGetParams param) throws Exception {
        param.setType(0);
        return printDataSaveJobService.getPrintDataTaskJobs(getStaff(), param);
    }

    /**
     * 任务详情接口
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/asyn/getJobDetail", method = RequestMethod.POST)
    @ResponseBody
    public Object printAysnGetJobDetail(AsynJobPrintParams param) throws Exception {
        return printDataSaveJobService.getPrintDataTaskJobDetail(getStaff(), param);
    }

    /**
     * 打印打印数据列表的
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/asyn/print", method = RequestMethod.POST)
    @ResponseBody
    public Object asynJobPrint(AsynJobPrintParams param) throws Exception {
        printDataSaveJobService.asynJobPrint(getStaff(), param);
        return new JSONObject();
    }

    @Resource
    private WaybillTradeQueryBusinessService waybillTradeQueryBusinessService;

    /**
     * 获取波次自动打印数据
     *
     * @return
     */
    @RequestMapping(value = "/print/auto/wave/get", method = RequestMethod.GET)
    @ResponseBody
    public Object getWaveAutoPrintData(Long warehouseId, String api_name) throws SessionException {
        Staff staff = getStaff();
        // 查询所有打印任务
        List<WaveAutoPrintVO> waveAutoPrintVOList = waveAutoPrintService.queryNoPrintData(staff, warehouseId, staff.getId());
        if (CollectionUtils.isEmpty(waveAutoPrintVOList)) {
            logger.info(LogHelper.buildLog(staff, "[波次自动打印] 没有自动打印任务！"));
            return new ArrayList<>();
        }
        // 获取可打印订单波次号
        Map<Long, List<Trade>> waveIdAndTradesMap = waybillTradeQueryBusinessService.getTradeListForPrint(buildWaybillValidRequest(staff, waveAutoPrintVOList), new WaybillValidResponse())
                .stream()
                .collect(Collectors.groupingBy(Trade::getWaveId));

        // 过滤没有订单的波次数据
        List<WaveAutoPrintVO> newWaveAutoPrintVOList = waveAutoPrintVOList.stream()
                .filter(waveAutoPrintVO -> waveIdAndTradesMap.containsKey(waveAutoPrintVO.getWaveId()))
                .peek(waveAutoPrintVO -> waveAutoPrintVO.setPrintCount(waveIdAndTradesMap.get(waveAutoPrintVO.getWaveId()).size()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(newWaveAutoPrintVOList)) {
            return newWaveAutoPrintVOList;
        }

        logger.info(LogHelper.buildLog(staff, "[波次自动打印] 返回打印数据：" + JSONArray.toJSONString(newWaveAutoPrintVOList)));

        // 记录波次日志
        waveTraceService.batchAddWaveTrace(staff,
                WaveTraceUtils.buildBatchWaveTrace(staff, newWaveAutoPrintVOList.stream()
                                .map(WaveAutoPrintVO::getWaveId)
                                .collect(Collectors.toList()),
                        WaveTraceOperateEnum.WAVE_AUTO_PRINT,
                        "波次订单自动打印", 0));
        return newWaveAutoPrintVOList;
    }

    private WaybillValidRequest buildWaybillValidRequest(Staff staff, List<WaveAutoPrintVO> waveAutoPrintVOList) {
        List<Long> waveIdList = waveAutoPrintVOList.stream()
                .map(WaveAutoPrintVO::getWaveId)
                .collect(Collectors.toList());
        WaybillValidRequest waybillValidRequest = new WaybillValidRequest();
        waybillValidRequest.setEntrance(RequestEntranceEnum.TRADE_WAVE_AUTO_PRINT.getCode());
        waybillValidRequest.setStaff(staff);
        waybillValidRequest.setNeedOrder(false);
        waybillValidRequest.setMultiTemplate(1);
        waybillValidRequest.setOperateType(0);
        waybillValidRequest.setWaveIdList(waveIdList);
        waybillValidRequest.setWaveIds(waveIdList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
        waybillValidRequest.setFilterRepeat(1);
        waybillValidRequest.setErrRepeat(1);
        waybillValidRequest.setWaveFilter(1);
        return waybillValidRequest;
    }

    @RequestMapping(value = "/wireless/print/queryMutil", method = RequestMethod.GET)
    @ResponseBody
    public Object queryPdaMutilListByUniqueCode(String uniqueCode, Integer size, String api_name) throws SessionException {
        Assert.notNull(uniqueCode, "请输入无线打印唯一码！");
        final Staff staff = getStaff();

        return printDataService.getPdaPrintTradeData(staff, uniqueCode, size);
    }

    @RequestMapping(value = "/wireless/print/query", method = RequestMethod.GET)
    @ResponseBody
    public Object queryWirelessPrintListByUniqueCode(String uniqueCode, Integer size, String wirelessBatchId, String templateIds, String api_name) throws SessionException {
        Assert.notNull(uniqueCode, "请输入无线打印唯一码！");
        final Staff staff = getStaff();

        WirelessPrintVO wirelessPrintVO = printDataService.getExpressPrintData(staff, uniqueCode, size, wirelessBatchId, templateIds, request);

        // 发货单无线任务
        BaseWirelessPrintParams deliverParams = new BaseWirelessPrintParams();
        deliverParams.setUniqueCode(uniqueCode);
        deliverParams.setWirelessBatchId(wirelessBatchId);
        deliverParams.setTemplateIds(templateIds);
        deliverParams.setSize(size);
        deliverParams.setWirelessPrintTypeEnum(WirelessPrintTypeEnum.DELIVER);
        deliverParams.setLockCacheKey(WirelessPrintConstant.DELIVER_LOCK_KEY);
        deliverParams.setQueryCacheKey(WirelessPrintConstant.DELIVER_QUERY_KEY);
        BaseWirelessPrintDTO deliverDto = new BaseWirelessPrintDTO();
        try {
            deliverDto = baseWirelessPrintService.queryWirelessPrintWithCache(staff, deliverParams);
        } catch (IllegalArgumentException e) {
            deliverDto.setMessage(e.getMessage());
            logger.debug(LogHelper.buildLog(staff, "无线打印发货单异常：" + e.getMessage()));
        } catch (Exception e) {
            deliverDto.setMessage(e.getMessage());
            logger.error(LogHelper.buildLog(staff, "无线打印发货单异常：" + e.getMessage()), e);
        }

        // 返回数据组装
        wirelessPrintVO.setDeliverWirelessPrintList(deliverDto.getWirelessPrints());
        wirelessPrintVO.setDeliverMessage(deliverDto.getMessage());
        return wirelessPrintVO;
    }

    /**
     * 销货单无线打印
     *
     * @param uniqueCode
     * @param size
     * @param templateId
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/sales/wireless/print", method = RequestMethod.POST)
    @ResponseBody
    public Object querySalesWirelessPrintInfo(String uniqueCode, Integer size, Long templateId, String api_name) throws SessionException {
        Assert.notNull(uniqueCode, "请传入无线打印唯一码");
        Staff staff = getStaff();
        String key = "sales_wireless_print" + staff.getCompanyId() + uniqueCode;
        boolean globalFlag = true;
        try {
            globalFlag = cache.add(key, 1, 5);
        } catch (CacheException e) {
            logger.error(LogHelper.buildLog(staff, "销货单无线打印设置缓存失败，错误信息：" + ExceptionUtils.getFullStackTrace(e)));
            throw new RuntimeException("设置缓存失败");
        }
        if (!globalFlag) {
            throw new RuntimeException("当前正在打印中请稍后重试");
        }
        if (null == size || size < 1) {
            size = 10;
        }
        SalesWirelessPrintQueryParams queryParams = new SalesWirelessPrintQueryParams();
        queryParams.setUniqueCode(uniqueCode);
        queryParams.setPrintStatus(0);
        queryParams.setCompanyId(staff.getCompanyId());
        queryParams.setPage(new Page(1, size));
        List<SalesWirelessPrint> wirelessPrints = salesWirelessPrintService.queryInfoByParams(staff, queryParams);
        if (CollectionUtils.isEmpty(wirelessPrints)) {
            return new JSONObject();
        }
        List<String> salesIds = new ArrayList<>();
        Map<Long, List<String>> mergeSalesIds = new HashMap<>();
        MultiPrintConfig config = multiPrintConfigService.get(staff);
        SalesWirelessPrintConfigVO settings = JSON.parseObject(config.getSalesWirelessPrintSetting(), SalesWirelessPrintConfigVO.class);
        if (settings == null) {
            throw new RuntimeException("未设置打印配置");
        }
        if (templateId != null && !templateId.equals(settings.getTemplateId())) {
            throw new RuntimeException("打印配置已变更，请刷新页面");
        }
        for (SalesWirelessPrint print : wirelessPrints) {
            print.setTemplateId(settings.getTemplateId());
            print.setTemplateName(settings.getTemplateName());
            print.setPrinterName(settings.getPrinterName());
            print.setPrintStatus(1);
            String mergeSaleTradeIds = print.getMergeSaleTradeIds();
            if (StringUtils.isNotBlank(mergeSaleTradeIds)) {
                // 有这个字段说明是合并打印,要把一起合并打印的数据
                mergeSalesIds.put(print.getSaleTradeId(), Arrays.stream(mergeSaleTradeIds.split(",")).map(t -> t + "_" + print.getPrintNum()).collect(Collectors.toList()));
            } else {
                salesIds.add(print.getSaleTradeId() + "_" + print.getPrintNum());
            }
        }
        salesWirelessPrintService.updateSalesPrintInfo(staff, wirelessPrints);

        JSONObject printData = new JSONObject();
        if (CollectionUtils.isNotEmpty(salesIds)) {
            try {
                printData = (JSONObject) saleTradePrintData(templateId, StringUtils.join(salesIds, ","), false, null, null);
            } catch (Exception e) {
//            for (SalesWirelessPrint print : wirelessPrints) {
//                print.setPrintStatus(0);
//            }
//            salesWirelessPrintService.updateSalesPrintInfo(staff, wirelessPrints);
                logger.error(LogHelper.buildLog(staff, "销货单无线打印获取打印数据失败，错误信息:" + ExceptionUtils.getFullStackTrace(e)));
                throw new RuntimeException("销货单无线打印异常：" + e.getMessage());
            }
        }
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(mergeSalesIds)) {
            JSONArray fieldValues = printData.getJSONArray("fieldValues");
            for (List<String> mergeSaleIds : mergeSalesIds.values()) {
                try {
                    JSONObject mergePrintData = (JSONObject) saleTradePrintData(templateId, StringUtils.join(mergeSaleIds, ","), false, null, "1");
                    JSONArray mergeFieldValues = mergePrintData.getJSONArray("fieldValues");
                    if (CollectionUtils.isEmpty(fieldValues)) {
                        printData = mergePrintData;
                        fieldValues = new JSONArray();
                    }
                    fieldValues.addAll(mergeFieldValues == null ? new JSONArray() : mergeFieldValues);
                } catch (Exception e) {
                    logger.error(LogHelper.buildLog(staff, "销货单无线打印获取打印数据失败，错误信息:" + ExceptionUtils.getFullStackTrace(e)));
                    throw new RuntimeException("销货单无线打印异常：" + e.getMessage());
                }
            }
            printData.put("fieldValues", fieldValues);
        }
        return printData;
    }

    /**
     * 箱码无线打印
     *
     * @param uniqueCode
     * @param size
     * @param templateId
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/boxCode/wireless/print", method = RequestMethod.POST)
    @ResponseBody
    public Object queryBoxCodeWirelessPrintInfo(String uniqueCode, Integer size, Long templateId, String api_name, String source) throws SessionException {
        Assert.notNull(uniqueCode, "请传入无线打印唯一码");
        Staff staff = getStaff();
        String key = "box_code_wireless_print" + staff.getCompanyId() + uniqueCode;
        boolean globalFlag = true;
        try {
            globalFlag = cache.add(key, 1, 3);
        } catch (CacheException e) {
            logger.error(LogHelper.buildLog(staff, "箱码无线打印设置缓存失败，错误信息：" + ExceptionUtils.getFullStackTrace(e)));
            throw new RuntimeException("设置缓存失败");
        }
        if (!globalFlag) {
            throw new RuntimeException("当前正在打印中请稍后重试");
        }
        if (null == size || size < 1) {
            size = 10;
        }
        BoxCodeWirelessPrintQueryParams queryParams = new BoxCodeWirelessPrintQueryParams();
        queryParams.setUniqueCode(uniqueCode);
        queryParams.setPrintStatus(0);
        queryParams.setCompanyId(staff.getCompanyId());
        queryParams.setPage(new Page(1, size));
        List<BoxCodeWirelessPrintNote> wirelessPrints = boxCodeWirelessPrintService.queryInfoByParams(staff, queryParams);
        if (CollectionUtils.isEmpty(wirelessPrints)) {
            return new JSONObject();
        }
        List<String> boxCodes = new ArrayList<>();
        List<Integer> printNums = new ArrayList<>();
        MultiPrintConfig config = multiPrintConfigService.get(staff);
        BoxCodeWirelessPrintConfigVO settings = JSON.parseObject(config.getBoxCodeWirelessPrintSetting(), BoxCodeWirelessPrintConfigVO.class);
        if (null != templateId && !templateId.equals(settings.getTemplateId())) {
            throw new RuntimeException("打印配置已变更，请刷新页面");
        }
        for (BoxCodeWirelessPrintNote print : wirelessPrints) {
            print.setTemplateId(settings.getTemplateId());
            print.setTemplateName(settings.getTemplateName());
            print.setPrinterName(settings.getPrinterName());
            print.setPrintStatus(1);
            boxCodes.add(print.getBoxCode());
            printNums.add(print.getPrintNum());
        }
        boxCodeWirelessPrintService.updateBoxCodePrintInfo(staff, wirelessPrints);

        try {
            JSONObject printData = (JSONObject) printBoxLabelData(templateId, String.join(",", boxCodes), false, true, api_name);
            return printData;
        } catch (Exception e) {
            for (BoxCodeWirelessPrintNote print : wirelessPrints) {
                print.setPrintStatus(0);
            }
            boxCodeWirelessPrintService.updateBoxCodePrintInfo(staff, wirelessPrints);
            logger.error(LogHelper.buildLog(staff, "箱码无线打印获取打印数据失败，错误信息:" + ExceptionUtils.getFullStackTrace(e)));
            throw new RuntimeException("箱码无线打印异常：" + e.getMessage());
        }
    }


    /**
     * 获取后置打印打印数据
     *
     * @param param
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/print/post/query", method = RequestMethod.POST)
    @ResponseBody
    public Object printPostQuery(WavePickingParam param, String api_name) throws Exception {
        Assert.notNull(param.getPickingCode(), "请输入拣选号！");
        Assert.notNull(param.getOuterId(), "请输入商家编码！");
        Assert.notNull(param.getTimestamp(), "请输入时间戳！");
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        WavePickingScanResult scanResult = new WavePickingScanResult();
        Long[] sidArray = null;
        try {
            sidArray = cache.get(buildPostPrintQueryCacheKey(param, staff));
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "前端重试打印数据获取缓存失败！"), e);
        }
        if (sidArray == null) {
            return scanResult;
        }
        // 查询订单数据，打印需要查详情
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sidArray);
        Map<Long, List<Trade>> templateId2TradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getTemplateId));
        // 获取打印数据
        scanResult.setPrintData(getPrintObjects(staff, templateId2TradeMap));
        scanResult.setTrades(trades);
        return scanResult;
    }

    private List<Object> getPrintObjects(Staff staff, Map<Long, List<Trade>> templateId2TradeMap) {
        List<Object> printDataList = Lists.newArrayList();
        try {
            for (Map.Entry<Long, List<Trade>> entrySet : templateId2TradeMap.entrySet()) {
                Object printData = printDataShowService.getPrintDataAuto(staff, entrySet.getKey(), false, null, entrySet.getValue(), false);
                TemplateHelper.handleExpressFieldValuesLog(staff, printData, config);//通过参数控制打印返回给前端的数据日志
                if (printData instanceof JSONObject) {
                    PrintTemplateHelper.handlePrintTotalNum((JSONObject) printData, 1);//打印序号处理
                }
                printDataList.add(printData);
            }
            dealWaveScanException(printDataList);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "播种/后置打印异常"), e);
            throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！原因：" + e.getMessage());
        }
        return printDataList;
    }

    private String buildPostPrintQueryCacheKey(WavePickingParam param, Staff staff) {
        return "post_print_" + staff.getId() + "_" + param.getPickingCode() + "_" + param.getOuterId() + "_" + param.getTimestamp();
    }

    private String buildOrderUniqueCodePrintCacheKey(WavePickingParam param, Staff staff) {
        return "order_unique_code_print_" + param.getOperateType() + "_" + param.getSid() + "_" + staff.getId();
    }


    /**
     * 根据模板id和网点过滤模板和网点都一样的订单
     * 在修改模板时使用
     */
    private List<Trade> filterTradeByTemplate(List<Trade> trades, Long templateId, Staff staff, Integer templateType) {
        List<Trade> allowTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return allowTrades;
        }
        templateType = templateType == null ? 0 : templateType;
        WarehouseTemplate warehouseTemplate =
                warehouseTemplateService.getWarehouseTemplate(staff, trades.get(0).getWarehouseId(), templateId, templateType);
        for (Trade trade : trades) {
            if (templateId.equals(trade.getTemplateId())) {
                if (StringUtils.isBlank(trade.getOutSid())) {
                    //模板一样没有运单号的直接过滤
                    continue;
                }
                //模板id一样就判断网点是否一样一样的话就过滤
                OutSidPool outSidPool = outSidPoolService.getOutSidPoolWithOutSid(staff, trade.getOutSid());
                if (outSidPool == null || warehouseTemplate == null || outSidPool.getBranchId().equals(warehouseTemplate.getBranchId())) {
                    //订单未获取单号或者没有模板网点对应关系则或者网点一直则过滤
                    continue;
                }
            }
            allowTrades.add(trade);
        }
        return allowTrades;
    }

    /**
     * 清空模版时取消单号
     *
     * @param staff
     * @param deleteTrades
     */
    private void deletTempleteOutSid(Staff staff, List<Trade> deleteTrades) {
        //可能有模版没有运单号，需要把有运单号的找出来然后取消运单号
        List<Trade> outSidTrades = deleteTrades.stream().filter(trade -> StringUtils.isNotBlank(trade.getOutSid())).collect(Collectors.toList());
        //取消电子面单，outSidPool状态修改为1-解绑状态,不是普通面单走这个取消单号
        if (outSidTrades.size() > 0) {
            cancelSidOutSid(staff, outSidTrades);
        }

        //普通面单直接清空数据库取消单号
        List<Trade> normalOutSidsTrades = outSidTrades.stream().filter(trade -> Integer.valueOf(0).equals(trade.getTemplateType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(normalOutSidsTrades)) {
            List<Trade> updateTrades = Lists.newArrayListWithCapacity(normalOutSidsTrades.size());
            normalOutSidsTrades.forEach(trade -> {
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(trade.getSid());
                updateTrade.setOutSid("");
                updateTrades.add(updateTrade);
            });
            tradePtService.saveByTrades(staff, updateTrades);
            tradeUpdateService.updateTrades(staff, updateTrades);
        }
    }

    /**
     * 更新打印次数
     *
     * @param staff
     * @param trades
     */
    public void updateTradePrintCount(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            List<Trade> updateTrades = new ArrayList<>();
            trades.forEach(trade -> {
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(trade.getSid());
                updateTrade.setPrintCount(trade.getPrintCount() + 1);
                updateTrades.add(updateTrade);
            });
            tradeUpdateService.updateTrades(staff, updateTrades);
        }
    }

    private class StatusVO extends Status {

        public StatusVO(List<Trade> tradeList, Object validatePrintResultMap) {
            this.setStatus("success");
            this.validatePrintResultMap = validatePrintResultMap;
            this.tradeResult = tradeList;
        }

        private Object validatePrintResultMap;

        private List<Trade> tradeResult;

        public List<Trade> getTradeResult() {
            return tradeResult;
        }

        public void setTradeResult(List<Trade> tradeResult) {
            this.tradeResult = tradeResult;
        }

        public Object getValidatePrintResultMap() {
            return validatePrintResultMap;
        }

        public void setValidatePrintResultMap(Object validatePrintResultMap) {
            this.validatePrintResultMap = validatePrintResultMap;
        }
    }

    private StatusVO validatePrint(Staff staff, String validateKey, List<Trade> tradesList) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Map validatePrintResultMap = new HashMap();
        Map<String, String> tradeMap = new HashMap();
        if (validateKey != null && validateKey.contains("validatePrintTradeForKddwdy")) {
            validatePrintResultMap = TemplateHelper.validatePrintTradeForKddwdy(staff, tradesList, tradeConfig, tradeMap);
            List<Trade> gxTrades = TradeUtils.filterGxTrades(tradesList);
            if (CollectionUtils.isNotEmpty(gxTrades)) {
                //TODO 为什么要这么写？ 不理解
                validatePrintResultMap = TemplateHelper.validatePrintTradeForGx(staff, getFxTradesByTids(staff, gxTrades), tradesList, tradeMap);
            }
            tradesList = (List<Trade>) validatePrintResultMap.get(TemplateHelper.TRADELIST);
        }
        if (validateKey != null && validateKey.contains("validatePrintTradeForDyfh")) {
            validatePrintResultMap = TemplateHelper.validatePrintTradeForDyfh(staff, tradesList, tradeConfig, tradeMap);
            List<Trade> gxTrades = TradeUtils.filterGxTrades(tradesList);
            if (CollectionUtils.isNotEmpty(gxTrades)) {
                //TODO 为什么要这么写？ 不理解
                validatePrintResultMap = TemplateHelper.validatePrintTradeForGx(staff, getFxTradesByTids(staff, gxTrades), tradesList, tradeMap);
            }
            tradesList = (List<Trade>) validatePrintResultMap.get(TemplateHelper.TRADELIST);
        }
        if (validateKey != null && validateKey.contains("validatePrintTradeForBcdy")) {
            validatePrintResultMap = TemplateHelper.validatePrintTradeForBcdy(tradesList, tradeConfig, tradeMap);
            tradesList = (List<Trade>) validatePrintResultMap.get(TemplateHelper.TRADELIST);
        }
        if (validateKey != null && validateKey.contains("validatePrintTradeForDdbd")) {
            validatePrintResultMap = TemplateHelper.validatePrintTradeForDdbd(tradesList, tradeConfig, tradeMap);
            tradesList = (List<Trade>) validatePrintResultMap.get(TemplateHelper.TRADELIST);
        }
        if (validateKey != null && validateKey.contains("validatePrintTradeForCkddy")) {
            validatePrintResultMap = TemplateHelper.validatePrintTradeForCkddy(tradesList, tradeConfig, tradeMap);
            tradesList = (List<Trade>) validatePrintResultMap.get(TemplateHelper.TRADELIST);
        }
        //TemplateHelper.validatePrintTrade(staff, tradesList);
        return new StatusVO(tradesList, validatePrintResultMap.get(TemplateHelper.TRADEMAP));
    }

    private List<Trade> getFxTradesByTids(Staff staff, List<Trade> gxTrades) {
        List<Trade> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gxTrades)) {
            Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
            Iterator<Map.Entry<Long, List<Trade>>> iterator = sourceIdMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, List<Trade>> next = iterator.next();
                Long key = next.getKey();
                List<Trade> value = next.getValue();
                Staff fxStaff = staffService.queryFullByCompanyId(key);
                //TODO 这个方法需要这样实现
                List<Trade> fxTrades = fxBusiness.getFxTradesByTids(fxStaff, value);
                if (CollectionUtils.isNotEmpty(fxTrades)) {
                    result.addAll(fxTrades);
                }
            }
        }
        return result;
    }

    /**
     * 校验供销订单反审核异常订单
     *
     * @param trades
     */
    private void validateTradeUnAudit(Staff staff, List<Trade> trades) {
        trades.forEach(trade -> {
            Assert.isTrue(!TradeUtils.isUnAuditExcep(staff, trade), String.format("分销商反审核异常不能进行此操作[系统订单号=%s]", trade.getSid()));

        });
    }

    private void validateTradJIT(List<Trade> trades) {
        trades.forEach(trade -> {
            Assert.isTrue(!CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()), String.format("JIT订单不能进行此操作[系统订单号=%s]", trade.getSid()));
        });
    }


    /**
     * 记录操作日志 发货后换模板 修改回收单号等 危险操作的订单日志
     */
    private void saveDangerTradeTrace(Staff staff, List<Trade> trades, List<Long> uploadConsignPdd, String action, String content, Map<Long, Long> sidTemplateIdMap, Map<Long, String> idTemplateNameMap, boolean saveExpressTemplate, Staff gxStaff) {
        if (CollectionUtils.isNotEmpty(trades)) {
            Date matchTime = new Date();
            List<TradeTrace> tradeTraces = new ArrayList<>();
            for (Trade trade : trades) {
                String saveAction = action;
                String saveContent = content;
                if (uploadConsignPdd.contains(trade.getSid())) {
                    //是pdd发货后操作则需要记录一个危险
                    saveAction = "[风险]" + action;
                    saveContent = "[风险!]上传发货后强制进行操作:" + content;
                }
                if (org.apache.commons.collections.MapUtils.isNotEmpty(sidTemplateIdMap)) {
                    Long templateId = sidTemplateIdMap.get(trade.getSid());
                    if (templateId != null) {
                        String templateName = null;
                        if (CollUtil.isNotEmpty(idTemplateNameMap)) {
                            templateName = idTemplateNameMap.get(templateId);
                        }
                        if (StringUtils.isNotBlank(templateName)) {
                            saveContent = saveContent + " 原模板：" + templateName;
                        }
                    }
                }
                if (StringUtils.isNotBlank(trade.getOutSid()) && saveExpressTemplate) {
                    saveContent = saveContent + " 保存快递单号：" + trade.getOutSid();
                }
                tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, saveAction, staff.getName(), matchTime, saveContent));
            }
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }
    }

    /**
     * 批量打印（按波次）
     */
    @RequestMapping(value = "/print/wlb/data/printer/wave", method = RequestMethod.POST)
    @ResponseBody
    public Object batchPrintWlbDataWave(@RequestBody BatchPrintWlbDataAsWaveBo waveBo) throws SessionException {
        BatchPrintWlbDataAsWaveResult vo = new BatchPrintWlbDataAsWaveResult();
        List<Object> responseList = new ArrayList<>();
        Staff staff = getStaff();
        Long printWaveId = waveBo.getPrintWaveId();
        if (printWaveId != null && printWaveId > 0 && Integer.valueOf(2).equals(waveBo.getPrintBatches())) {
            printCheckService.removeCheck(staff, printWaveId);
        }
        RequestBodyParamsUtils.setParams(staff, waveBo);
        //记录请求顺序
        List<Long> sids = waveBo.getDataList().stream().map(BatchPrintWlbDataAsWaveData::getSid).collect(Collectors.toList());
        Logs.debug(LogHelper.buildLog(staff, "批量打印（按波次）,收到参数：" + JSONObject.toJSONString(waveBo)));
        Map<Long, BatchPrintWlbDataAsWaveData> dataMap = Maps.newHashMap();
        waveBo.getDataList().stream().forEach((data) -> dataMap.put(data.getSid(), data));
        //打印结果Map k:sid, v:描述或打印数据
        Map<Long, Object> resultData = Maps.newHashMap();
        //打印模板容器
        Map<Long, Object> templateMap = Maps.newHashMap();
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(null, staff, sids.toArray(new Long[0])));
        List<Long> waveId = trades.stream().filter(java.util.Objects::nonNull).map(Trade::getWaveId).distinct().collect(Collectors.toList());
        //错误的订单集合(记录到数据库未展示页面上)
        List<Long> errorList = new ArrayList<>();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        validatePrintTradeForBatchWave(trades, tradeConfig, resultData, waveBo, staff, templateMap, dataMap);
        //printWlbDataEnd按模板ID分组
        Map<Long, List<BatchPrintWlbDataAsWaveData>> groupData = waveBo.getDataList().stream().filter((data) -> !resultData.containsKey(data.getSid())).collect(Collectors.groupingBy((data) -> data.getTemplateId()));

        for (Map.Entry<Long, List<BatchPrintWlbDataAsWaveData>> entry : groupData.entrySet()) {
            try {
                List<BatchPrintWlbDataAsWaveData> datas = entry.getValue();
                List<Long> groupSids = datas.stream().map(BatchPrintWlbDataAsWaveData::getSid).collect(Collectors.toList());
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, entry.getKey(), false);
                Assert.notNull(userWlbExpressTemplate, "未找到该模板,按波次批量打印不能使用普通电子面单");
                Object printData;
                if (WlbTemplateTypeEnum.isJdLop(userWlbExpressTemplate.getWlbTemplateType())) {
                    printData = getJdlopPrintData(StringUtils.join(groupSids, ","),
                            entry.getKey(),
                            waveBo.getPrintBatchId(),
                            datas.get(0).getPrinter(),
                            waveBo.getWavePrintParam(),
                            null,
                            null,
                            null,
                            null,
                            false,
                            waveBo.getPrintCountNum(),
                            null,
                            null,
                            null,
                            WAVE_PRINT.name());
                } else {
                    printData = printWlbDataEnd(StringUtils.join(groupSids, ","),
                            entry.getKey(),
                            waveBo.getPrintBatchId(),
                            datas.get(0).getPrinter(),
                            waveBo.getWavePrintParam(),
                            null,
                            null,
                            null,
                            null,
                            null,
                            waveBo.getPrintCountNum(),
                            null,
                            null,
                            false,
                            WAVE_PRINT.name());
                }
                handleBatchWlbPrintAsWaveResult(resultData, printData, templateMap, dataMap, staff);
            } catch (Exception e) {
                logger.error("获取打印数据失败 request：" + JSON.toJSONString(waveBo), e);
                List<BatchPrintWlbDataAsWaveData> datas = entry.getValue();
                datas.stream().forEach((d) -> {
                    Long sid = d.getSid();
                    String printer = dataMap.get(sid).getPrinter();
                    Object template = getTemplate(staff, dataMap.get(sid).getTemplateId(), templateMap);
                    BatchPrintWlbDataAsWaveErrorResult errorResult = new BatchPrintWlbDataAsWaveErrorResult(sid, e.getMessage() == null ? "获取打印数据系统错误" : StringUtils.substring(e.getMessage(), 0, 50), true, template, printer);
                    resultData.put(sid, errorResult);
                });
            }
        }
        for (Long sid : sids) {
            if (resultData.get(sid) == null) {
                String printer = dataMap.get(sid).getPrinter();
                Object template = getTemplate(staff, dataMap.get(sid).getTemplateId(), templateMap);
                BatchPrintWlbDataAsWaveErrorResult errorResult = new BatchPrintWlbDataAsWaveErrorResult(sid, "获取打印数据失败", true, template, printer);
                responseList.add(errorResult);
            } else {
                responseList.add(resultData.get(sid));
            }
        }
        vo.setResponse(responseList);
        List<FailPrintParam> failPrintParamList = new ArrayList<>();
        //未打印的订单集合
        List<Long> warnList = new ArrayList<>();
        responseList.forEach(response -> {
            if (response instanceof BatchPrintWlbDataAsWaveErrorResult) {
                BatchPrintWlbDataAsWaveErrorResult result = (BatchPrintWlbDataAsWaveErrorResult) response;
                warnList.add(result.getSid());
                FailPrintParam failPrintParam = new FailPrintParam();
                failPrintParam.setLogId(waveBo.getLogId());
                failPrintParam.setSid(result.getSid());
                failPrintParam.setErrorMessage(result.getDesc());
                failPrintParamList.add(failPrintParam);
            }
        });
        if (waveBo.getLogId() != null) {
            printHelperService.saveFailPrintTradeLog(staff, failPrintParamList);
        }
        OpLog opLogEnd = getEoorOpLog(staff, sids, waveId, errorList, warnList);
        //记录失败的波次的sid
        opLogService.record(staff, opLogEnd);
        return vo;
    }

    private OpLog getEoorOpLog(Staff staff, List<Long> sids, List<Long> waveId, List<Long> errorList, List<Long> warnList) {
        OpLog opLog = new OpLog();
        if (!CollectionUtils.isNotEmpty(warnList)) {
            opLog.setCompanyId(staff.getCompanyId());
            opLog.setStaffId(staff.getId());
            opLog.setAccountName(staff.getAccountName());
            opLog.setStaffName(staff.getName());
            opLog.setIp(IpUtils.getClientIP(request));
            opLog.setDomain(Domain.PRINT);//领域，例如订单领域为trade，商品领域为item，库存领域为stock
            opLog.setAction("波次打印");//业务方法名称
            opLog.setContent("批量打印(按波次) 波次号:" + waveId.toString() + " 系统订单号:" + sids.toString());//业务操作内容
            opLog.setArgs(" 系统订单号:" + sids.toString()); //json化的业务参数
            opLog.setCreated(new Date());
            opLog.setIsError(0);
        } else {
            opLog.setCompanyId(staff.getCompanyId());
            opLog.setStaffId(staff.getId());
            opLog.setAccountName(staff.getAccountName());
            opLog.setStaffName(staff.getName());
            opLog.setIp(IpUtils.getClientIP(request));
            opLog.setDomain(Domain.PRINT);//领域，例如订单领域为trade，商品领域为item，库存领域为stock
            opLog.setAction("波次打印");//业务方法名称
            opLog.setContent("批量打印(按波次) 波次号:" + waveId.toString() + " 系统订单号:" + sids.toString() + "，部分订单因不满足条件被过滤，明细为: " + warnList.toString());//业务操作内容
            opLog.setArgs("系统订单号:" + sids.toString() + "错误订单号:" + errorList.toString()); //json化的业务参数
            opLog.setCreated(new Date());
            opLog.setIsError(0);
        }
        return opLog;
    }

    /**
     * 分组获取打印数据，再按sid拆解出来
     *
     * @param resultData
     * @param printData
     */
    private void handleBatchWlbPrintAsWaveResult(Map<Long, Object> resultData, Object printData, Map<Long, Object> templateMap, Map<Long, BatchPrintWlbDataAsWaveData> dataMap, Staff staff) {
        if (printData instanceof JSONObject) {
            //获取打印数据成功
            JSONObject printDataJson = (JSONObject) printData;
            //fieldValues
            JSONArray fieldValues = printDataJson.getJSONArray("fieldValues");
            JSONArray printDatas = printDataJson.getJSONArray("printData");
            String params = printDataJson.getString("params");
            if (null != fieldValues) {
                for (int i = 0; i < fieldValues.size(); i++) {
                    JSONObject value = fieldValues.getJSONObject(i);
                    Long sid = value.getLong("sid");
                    JSONObject singlePrintData = new JSONObject();
                    JSONArray singleFieldValues = new JSONArray();
                    singleFieldValues.add(value);
                    singlePrintData.put("printCountNum", printDataJson.get("printCountNum"));
                    JSONObject template = printDataJson.getJSONObject("template");
                    singlePrintData.put("template", template);
                    singlePrintData.put("fieldValues", singleFieldValues);
                    if (StringUtils.isNotBlank(params)) {
                        singlePrintData.put("params", params);
                    }
                    if (printDatas != null) {
                        JSONArray singlePrintDatas = new JSONArray();
                        singlePrintDatas.add(printDatas.get(i));
                        singlePrintData.put("printData", singlePrintDatas);
                        singlePrintData.put("tradeCode", printDataJson.get("tradeCode"));
                    }
                    resultData.put(sid, singlePrintData);
                }
            }
        } else if (printData instanceof List) {
            // 获取打印数据失败
            List<BatchExecutException.ExceptionReport> exceptionReports = (List<BatchExecutException.ExceptionReport>) printData;
            exceptionReports.stream().forEach((e) -> {
                Long sid = Long.valueOf(e.getSid());
                String printer = dataMap.get(sid).getPrinter();
                Object template = getTemplate(staff, dataMap.get(sid).getTemplateId(), templateMap);
                BatchPrintWlbDataAsWaveErrorResult errorResult = new BatchPrintWlbDataAsWaveErrorResult(Long.valueOf(e.getSid()), e.getErrorMsg() == null ? "获取打印数据错误" : e.getErrorMsg(), true, template, printer);
                resultData.put(sid, errorResult);
            });
        }
    }

    private Object getTemplate(Staff staff, Long templateId, Map<Long, Object> templateMap) {
        Object template = templateMap.get(templateId);
        if (template == null) {
            try {
                UserWlbExpressTemplate userWlbExpressTemplate = printDataShowService.getTemplate(staff, templateId, null);
                templateMap.put(templateId, userWlbExpressTemplate);
                return userWlbExpressTemplate;
            } catch (Exception e) {
                logger.error("查询用户打印模板失败 templateId:" + templateId, e);
                return null;
            }
        } else {
            return template;
        }
    }


    /**
     * 订单打印快递单校验, 并记录过滤结果
     */
    private void validatePrintTradeForBatchWave(List<Trade> tradeList, TradeConfig tradeConfig, Map<Long, Object> resultData, BatchPrintWlbDataAsWaveBo waveBo, Staff staff, Map<Long, Object> templateMap, Map<Long, BatchPrintWlbDataAsWaveData> dataMap) {
        for (Trade trade : tradeList) {
            Long sid = trade.getSid();
            Object template = getTemplate(staff, trade.getTemplateId(), templateMap);
            //必须为【待打印快递单】或【待包装】或【待称重】或【待发货】
            if (!Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(TradeStatusUtils.convertSysStatus(trade, tradeConfig))
                    && !Trade.SYS_STATUS_WAIT_PACKAGE.equals(TradeStatusUtils.convertSysStatus(trade, tradeConfig))
                    && !Trade.SYS_STATUS_WAIT_WEIGHT.equals(TradeStatusUtils.convertSysStatus(trade, tradeConfig))
                    && !Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(TradeStatusUtils.convertSysStatus(trade, tradeConfig))) {
                resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单系统状态不符合打印条件，打印时将会被过滤，不打印。", true, template, dataMap.get(sid).getPrinter()));
            } else if (StringUtils.isEmpty(trade.getOutSid())) {
                resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单未填写运单号", true, template, dataMap.get(sid).getPrinter()));
            } else {
                //订单无任何系统异常和自定义异常，且未被挂起
                if (trade.getIsExcep() == 1 || TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT)) {
                    resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单存在异常，打印时将会被过滤，不打印。", true, template, dataMap.get(sid).getPrinter()));
                } else if (waveBo.getFilterRepeat() != null && waveBo.getFilterRepeat()) {
                    //若打印状态为【已打印】，文字提示
                    if (trade.getExpressPrintTime() != null && trade.getExpressPrintTime().after(TradeConstants.INIT_DATE)) {
                        resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单已打印", true, template, dataMap.get(sid).getPrinter()));
                    } else {
                        //若打印次数大于0， 文字提示
                        if (trade.getPrintCount() != null && trade.getPrintCount() > 0) {
                            resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单打印次数为:" + trade.getPrintCount(), true, template, dataMap.get(sid).getPrinter()));
                        }
                    }
                }
                if (waveBo.getPrintWaveId() != null && waveBo.getPrintWaveId() > 0 && (trade.getWaveId() == null || !trade.getWaveId().equals(waveBo.getPrintWaveId()))) {
                    resultData.put(sid, new BatchPrintWlbDataAsWaveErrorResult(sid, "订单已经踢出波次，不打印。", true, template, dataMap.get(sid).getPrinter()));
                }
            }
        }
        if (org.apache.commons.collections.MapUtils.isNotEmpty(resultData)) {
            for (Map.Entry<Long, Object> entry : resultData.entrySet()) {
                Long key = entry.getKey();
                Object value = entry.getValue();
                Logs.debug("校验失败不打印，sid:" + key + ",原因：" + JSONObject.toJSONString(value));
            }
        }
    }

    /**
     * 发送销货单打印数据
     *
     * @param saleTradeId 销货单id
     * @param uniqueCode  唯一码id
     * @param printNum    打印数量，默认1
     */
    @RequestMapping(value = "send/saleTrade/data", method = RequestMethod.POST)
    @ResponseBody
    public Object sendTradePrintData(Long saleTradeId, String uniqueCode, Integer printNum, String api_name, Long sid) throws Exception {
        Assert.notNull(uniqueCode, "请传入唯一码id");
        String regex = "[0][0][0]\\d*";
        boolean matches = uniqueCode.matches(regex);
        Assert.isTrue(matches, "唯一码不正确，请确认");
        Assert.isTrue(saleTradeId != null || sid != null, "请传入销货单id或者系统订单号sid");
        List<SalesWirelessPrintParams> var2 = new ArrayList<>();
        SalesWirelessPrintParams salesWirelessPrintParams = new SalesWirelessPrintParams();
        salesWirelessPrintParams.setUniqueCode(uniqueCode);
        salesWirelessPrintParams.setSaleTradeId(saleTradeId);
        if (printNum != null) {
            salesWirelessPrintParams.setPrintNum(printNum);
        } else {
            salesWirelessPrintParams.setPrintNum(1);
        }
        salesWirelessPrintParams.setSid(sid);
        var2.add(salesWirelessPrintParams);
        Staff staff = getStaff();
        return salesWirelessPrintService.saveSalesPrintInfo(staff, var2);
    }

//
//    /**
//     * 过滤异常订单，审核前订单
//     * @param resultData
//     * @param trades
//     * @param staff
//     */
//    private void filterTrade(Map<Long, Object> resultData, List<Trade> trades, Staff staff, Map<Long,Object> templateMap) {
//        if (CollectionUtils.isNotEmpty(trades)) {
//            trades.forEach((trade) -> {
//                TradeExceptionUtils.analyze(staff, trade);
//                if(!filterTradeForPrint(trade)){
//                    Object template = getTemplate(staff, trade.getTemplateId(), templateMap);
//                    resultData.put(Long.valueOf(trade.getSid()), new BatchPrintWlbDataAsWaveErrorResult(Long.valueOf(trade.getSid()),"异常订单", true, template));
//                }
//            });
//        }
//    }

//    private boolean filterTradeForPrint(Trade trade) {
//        return !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus()) &&
//                !Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()) &&
//                !Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus()) &&
//                CollectionUtils.isEmpty(trade.getExceptions());
//    }

    /**
     * 订单唯一码重新扫描-打印快递单
     */
    @RequestMapping(value = "/print/again", method = RequestMethod.POST)
    @ResponseBody
    public Object printAgain(String uniqueCode, String printer, String printerSettings, Boolean hasPrintTemplateIntegrate, String api_name, String validateKey) throws Exception {
        Assert.notNull(uniqueCode, "唯一码不能为空！");
        Staff staff = getStaff();
        WaveUniqueCode waveUniqueCode = orderUniqueCodeService.queryByUniqueCode(staff, uniqueCode);
        if (waveUniqueCode == null || !DataUtils.checkLongNotEmpty(waveUniqueCode.getSid())) {
            throw new IllegalArgumentException("唯一码不存在或已解绑!");
        }
        if (orderUniqueCodeExamineService.currentMatchedAll(staff, uniqueCode)) {
            Trade trade = tradeSearchService.queryBySid(staff, true, waveUniqueCode.getSid());
            Assert.notNull(trade, "订单不存在！");
            Map<String, Object> result = Maps.newHashMap();
            //已打印过快递单的才能重新打印
            if (trade.getExpressPrintTime() != null) {
                OrderUniqueCodeUtils.validateTrade(staff, trade);
                if (BooleanUtils.isTrue(hasPrintTemplateIntegrate) || checkHasNewModeFeature(staff)) {
                    WavePickingParam param = new WavePickingParam();
                    param.setWaveId(-5L);
                    WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), Lists.newArrayList(trade), param, null, WavePrintType.ORDER_UNIQUE_CODE, RequestEntranceEnum.ITEM_OUT_PRINT, null);
                    trade.setPrintCount(trade.getPrintCount() == null ? 1 : trade.getPrintCount() + 1);
                    result.put("trade", trade);
                    result.put("printData", waybillPrintResponse);
                    uniqueCodePrintAgainTradeTrace(staff, trade);
                    return result;
                } else {
                    Map<Long, String> sidPrinterMap = getPrinterMap(printer, printerSettings, trade);
                    PrintResult printResult = printEWaybillByTrade(staff, Lists.newArrayList(trade), -5L, sidPrinterMap, WavePrintType.ORDER_UNIQUE_CODE, validateKey, null);
                    if (CollectionUtils.isNotEmpty(printResult.getPrintData())) {
                        result.put("trade", trade);
                        result.put("printData", printResult.getPrintData().get(0));
                        uniqueCodePrintAgainTradeTrace(staff, trade);
                        return result;
                    }
                }
            } else {
                throw new IllegalArgumentException("该唯一码关联订单未打印过快递单，不可重新打印!");
            }
        } else {
            throw new IllegalArgumentException("存在未验货唯一码，请先执行验货！!");
        }
        return null;
    }

    private void uniqueCodePrintAgainTradeTrace(Staff staff, Trade trade) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        String tradeTrace = new StringBuilder("唯一码重新打印快递单").append("；快递单号：").append(trade.getOutSid()).append("；第").append(trade.getPrintCount() == null ? 1 : trade.getPrintCount()).append("次打印").toString();
        tradeTraces.add(TradeTraceUtils.createTradeTrace(staff, trade.getSid(), trade.getTaobaoId(), "唯一码重新打印快递单", tradeTrace));
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    /**
     * 打印预约入库单
     */
    @RequestMapping(value = "/print/prein/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printPreinData(Long templateId, String ids, String detailIds, Boolean onlyShowPrinterList, String mergePrint, String api_name) throws Exception {
        Assert.notNull(ids, "请传入预约入库单id");
        List<Long> preinIds = Strings.getAsLongList(ids, ",", true);
        List<Long> preinDetailIds = Strings.getAsLongList(detailIds, ",", true);
        if (CollectionUtils.isNotEmpty(preinDetailIds)) {
            //如果传了明细ID,那么只能传1个预约入库单号,这些明细是属于该预约入库单的
            Assert.isTrue(preinIds.size() == 1, "只能勾选同一个预约入库单下的明细商品,预约入库单号只能传一个");
        }
        final Staff staff = getStaff();
        PreinOrderParams orderParams = new PreinOrderParams();
        orderParams.setIds(preinIds);
        orderParams.setFillDetails(true);
        List<PreinOrder> preinOrders = purchaseService.preinOrderList(staff, orderParams);
        Assert.isTrue(CollectionUtils.isNotEmpty(preinOrders), "未查询到预约入库单信息，预约入库单id为" + ids);
        if (CollectionUtils.isNotEmpty(preinDetailIds)) {
            PreinOrder preinOrder = preinOrders.get(0);
            if (CollectionUtils.isNotEmpty(preinOrder.getDetailList())) {
                preinOrder.setDetailList(preinOrder.getDetailList().stream().filter(t -> preinDetailIds.contains(t.getId())).collect(Collectors.toList()));
            }
        }
        return getJsonPreinData(staff, templateId, preinOrders, onlyShowPrinterList, mergePrint);
    }

    public JSONObject getJsonPreinData(Staff staff, Long templateId, List<PreinOrder> preinOrderList, Boolean onlyShowPrinterList, String mergePrint) {
        UserInvoicesTemplate userInvoicesTemplate;
        if (templateId != null) {
            userInvoicesTemplate = userInvoicesTemplateService.userQuery(staff, templateId, false, EnumInvoicesType.PREIN);
        } else {
            userInvoicesTemplate = userInvoicesTemplateService.userDefaultGet(staff, EnumInvoicesType.PREIN);
        }
        Assert.notNull(userInvoicesTemplate, "当前用户找不到这个模板，templateId为：" + templateId);
        JSONObject result = new JSONObject();
        if (onlyShowPrinterList != null && onlyShowPrinterList) {
            UserInvoicesTemplates userInvoicesTemplates = userInvoicesTemplateService.userAllList(staff, EnumInvoicesType.PREIN, true);
            result.put("invoicesTemplate", userInvoicesTemplates);
        }
        List<PreinPrintVo> preinPrintVos = printPreinService.getFieldValues(staff, preinOrderList, userInvoicesTemplate, mergePrint);
        result.put("fieldValues", preinPrintVos);
        result.put("template", userInvoicesTemplate);
        return result;
    }

    /**
     * 设置上下文信息
     */
    private void setContext(WavePickingScanResult scanResult) {
        TradePostPrintContextUtils.getWithInit().setWave(scanResult.getWave());
        if (scanResult.getTrade() != null) {
            TradePostPrintContextUtils.getWithInit().setSid2TradeMap(TradeUtils.toMapBySid(Lists.newArrayList(scanResult.getTrade())));
        }
    }

    @RequestMapping(value = "/print/shein/boxLabel/data", method = RequestMethod.POST)
    @ResponseBody
    public Object printSheinBoxLabelData(String sids, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotBlank(sids), "未获取到订单号,请检查入参!");
        Staff staff = getStaff();
        return printBoxLabelService.getSheinBoxLabelData(staff, sids);
    }

    @RequestMapping(value = "/print/allocateSectionGoods", method = RequestMethod.POST)
    @ResponseBody
    public Object getDate(String apiName, Long waveId, String sids, String queryParams) throws SessionException {
        Staff staff = getStaff();
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        if (StringUtils.isNotBlank(queryParams)) {
            // 优先按查询条件查询订单
            Page page = new Page();
            //暂时写死配置为100000，solr查询时，仍会限制，过大会抛出异常
            page.setPageNo(1).setPageSize(100000);
            TradeQueryParams tradeQueryParams = TradeQueryParams.copyParams(JSONObject.parseObject(queryParams, TradeControllerParams.class));
            tradeQueryParams.setPage(page);
            tradeQueryParams.setQueryLargeResult(true);
            tradeQueryParams.setQueryFlag(1);
            tradeQueryParams.setAllowedPgl(true).setBreakQuery(true).setQueryOrder(false).setFields("t.sid").setIgnoreFilter(1).setNeedFill(false);
            Trades trades = tradeSearchService.search(staff, tradeQueryParams);
            List<Trade> tradeList = trades.getList();
            if (tradeList == null || tradeList.isEmpty()) {
                throw new IllegalArgumentException("未查询到任何订单或订单数量太大！");
            }
            // 查询trade
            sidArray = TradeUtils.toSids(tradeList);
        }
        if (sidArray != null) {
            if (sidArray.length > 200) {
                List<Long> sidList = Arrays.stream(sidArray)
                        .collect(Collectors.toList());
                for (List<Long> longs : Lists.partition(sidList, 200)) {
                    printHelperService.allocateGoodsWhenPrint(staff, waveId, longs.toArray(new Long[0]));
                }
            } else {
                printHelperService.allocateGoodsWhenPrint(staff, waveId, sidArray);
            }
        } else {
            logger.debug("未拿到入参中的sids数据,请检查入参!");
        }
        return successResponse();
    }

    private Staff getSessionStaff(Staff staff) {
        try {
            return getStaff();
        } catch (SessionException e) {
            return staff;
        }
    }

    /**
     * 编码打印记录查询任务
     */
    @RequestMapping(value = "/print/code/getJobs", method = RequestMethod.POST)
    @ResponseBody
    public Object printCodeGetJobs(PrintDataGetParams param) throws Exception {
        param.setType(1); //查询编码打印记录
        return printDataSaveJobService.getPrintDataTaskJobs(getStaff(), param);
    }


    /**
     * 批次打印记录导出
     */
    @RequestMapping(value = "/print/record/export", method = RequestMethod.POST)
    @ResponseBody
    public Object printRecordExport(String logIds) throws Exception {
        if (StringUtils.isEmpty(logIds)) {
            throw new IllegalArgumentException("请先勾选需要导出的批次打印记录!");
        }
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.PT.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        Map<String, Object> map = Maps.newHashMap();
        eventCenter.fireEvent(this, new EventInfo("print.record.export.excel").setArgs(new Object[]{staff, logIds}), false);
        map.put("result", "success");
        return map;
    }


    /**
     * 快销发货打印
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/print/fast/consign", method = RequestMethod.POST)
    @ResponseBody
    public Object printFastConsign(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        try {
            WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.NORMAL);
            return printFastConsign0(staff, param, printer, printerSettings, api_name, validateKey);
        } catch (Exception e) {
            Map<String, Object> result = Maps.newHashMapWithExpectedSize(3);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }

    private Object printFastConsign0(Staff staff, WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Exception {
        long start = System.currentTimeMillis();
        checkBeforeFastConsign(staff, param);
        fillBindUniqueCode(staff, param);
        WaveUniqueCode bindUniqueCode = param.getBindUniqueCode();
        if (bindUniqueCode != null &&
                !(Objects.equal(bindUniqueCode.getStatus(), OrderUniqueCodeStatusEnum.WAIT_IN.getType()) || Objects.equal(bindUniqueCode.getStatus(), OrderUniqueCodeStatusEnum.IN.getType()))){
            throw new IllegalArgumentException("仅支持【待入库】【在库】唯一码验货！");
        }
        WavePickingScanResult scanResult = fastConsignPrintService.scanByOuterId(staff, param);
        long matchedEnd = System.currentTimeMillis();
        try {
            itemUniqueCodeMatchedAllGetPrintData(staff, scanResult.getTrade(), param, scanResult.getUniquePositionNo(), printer, printerSettings, validateKey, api_name, scanResult, null);
        } catch (Exception e) {
            logger.debug(String.format("快销发货获取打印数据失败！%s", e.getMessage()));
            uniqueCodeServiceDubbo.cancelFastConsign(staff, scanResult.getTradeAllUniqueCodes());
            throw e;
        }
        if (scanResult.getTrade() != null) {
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        long end = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug(String.format("快销发货-匹配时间:%s, 打印时间:%s", matchedEnd- start, end - matchedEnd));
        }
        return scanResult;
    }


    /**
     * 快销发货前的校验
     */
    private void checkBeforeFastConsign(Staff staff, WavePickingParam param) {
        Assert.isTrue(Objects.equal(staff.getConf().getOpenWave(), 1), "请先开启波次！");
        if (param.getSid() == null && StringUtils.isEmpty(param.getOuterId())) {
            throw new IllegalArgumentException("请传入商家编码！");
        }
        param.setShowTradeInfo(true);
    }


    @RequestMapping(value = "/print/fast/consign/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object printFastConsignBatch(WavePickingParam param, String printer, String printerSettings, String api_name, String validateKey) throws Throwable {
        Staff staff = staffAssembleBusiness.getExecuteStaffById(this, param.getStaffId());
        WavePickingScanResult scanResult = new WavePickingScanResult();
        try {
            long start = System.currentTimeMillis();
            long scanEnd, printEnd;
            WaveUtils.setScanInfo(param, param.getOuterId(), TradePackScanInfo.ScanCodeType.NORMAL);
            WavePickingScanResult result = fastConsignPrintService.scanBatchByOuterId(staff, param);
            scanEnd = System.currentTimeMillis();

            List<Trade> trades = result.getTrades();
            scanResult.setItem(result.getItem());
            scanResult.setTradeMatched(result.getTradeMatched());
            if (CollectionUtils.isNotEmpty(trades)) {
                scanResult.setTrades(trades);
                scanResult.setTrade(trades.get(0));
                scanResult.setSids(trades.stream().map(Trade::getSid).collect(Collectors.toList()));
                scanResult.setMatchedTradeNum(trades.size());
                //由于是单品单件，所以播出的商品数量就是订单数
                scanResult.setMatchedItemNum(scanResult.getMatchedTradeNum());
            }

            if (!Objects.equal(param.getPostPrintBatchNewMode(), CommonConstants.JUDGE_YES) && CollectionUtils.isNotEmpty(trades)) {
                WaybillPrintResponse waybillPrintResponse = tradeWavePrintBusiness.getWaybillPrintResponse(staff, getSessionStaff(staff), trades, param, scanResult, WavePrintType.FAST_CONSIGN_BATCH_PRINT, RequestEntranceEnum.TRADE_PACKING_REAR, DataUtils.checkLongNotEmpty(param.getWaveId()) ? param.getWaveId().toString() : "");
                if (waybillPrintResponse == null || CollectionUtils.isNotEmpty(waybillPrintResponse.getFailList())) {
                    logger.error(LogHelper.buildLog(staff, "打印失败！原因：获取打印数据失败"));
                    throw new WaveScanException(WaveScanException.ERROR_CODE_PRINT, "打印失败！原因：获取打印数据失败 " + getPrintDataFailReason(waybillPrintResponse));
                }
                scanResult.setPrintData(waybillPrintResponse);
                //打印成功后设置打印时间,不用重新查询
                for (Trade trade : trades) {
                    trade.setExpressPrintTime(new Date());
                }
                // 用于当响应超时，前端发起重试请求返回打印数据。缓存两分钟
                try {
                    cache.set(buildPostPrintQueryCacheKey(param, staff), TradeUtils.toSids(trades), 120);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "前端重试打印数据缓存失败！"), e);
                }
            }
            printEnd = System.currentTimeMillis();
            logger.debug(String.format("快销发货连打匹配耗时：%s, 打印耗时：%s", scanEnd - start, printEnd - scanEnd));
        } catch (WaveScanException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("快销发货连续扫描异常，code:[%s], msg: [%s]", e.getErrCode(), e.getErrMsg())), e);
            scanResult.setErrType(WaveScanException.EXCEPTION_TYPE);
            scanResult.setErrCode(e.getErrCode());
            scanResult.setErrMsg(e.getErrMsg());
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "快销发货连续扫描异常"), e);
            throw e;
        }
        if (scanResult.getTrade() != null) {
            scanResult.setTrade(WaveUtils.simplifyTrade(scanResult.getTrade()));
        }
        return scanResult;
    }

}
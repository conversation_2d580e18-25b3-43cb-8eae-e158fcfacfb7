package com.raycloud.dmj.web.controllers.pt;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.pt.common.PtLockBusiness;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.enums.RequestEntranceEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeControllerParams;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyTemplateService;
import com.raycloud.dmj.express.request.template.*;
import com.raycloud.dmj.express.response.template.CompanyAbroadTemplateResponse;
import com.raycloud.dmj.express.response.template.CompanyTemplateResponse;
import com.raycloud.dmj.express.response.template.CompanyTemplateTradeResponse;
import com.raycloud.dmj.services.ILockCallback;
import com.raycloud.dmj.services.pt.IPrintDataSourceService;
import com.raycloud.dmj.services.pt.model.PrintDataSourceParam;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeWaveService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 快递公司绑定模板
 *
 * @Date 2022/12/28
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/pt/logistics/company/template")
public class LogisticsCompanyTemplateController extends Sessionable {
    @Resource
    IUserLogisticsCompanyTemplateService userLogisticsCompanyTemplateService;
    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;
    @Resource
    private IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;
    @Resource
    ITradeWaveService tradeWaveService;
    @Resource
    PtLockBusiness ptLockBusiness;
    @Resource
    IPrintDataSourceService printDataSourceService;

    @Resource
    private IProgressService progressService;

    @Resource
    private IEventCenter eventCenter;

    /**
     * 查询快递公司电子面单设置
     */
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ResponseBody
    public Object queryTemplateList(CompanyTemplateQueryRequest param, String api_name) throws SessionException {
        initStaffRequest(param);
        return userLogisticsCompanyTemplateService.queryTemplateList(param);
    }

    /**
     * 保存电子面单配置
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public Object save(@RequestBody CompanyTemplateSaveRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.save(request);
        return successResponse();
    }

    /**
     * 新增网点
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/saveBranch", method = RequestMethod.POST)
    @ResponseBody
    public Object saveBranch(@RequestBody CompanyTemplateSaveBranchRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        return userLogisticsCompanyTemplateService.saveBranch(request);
    }

    /**
     * 修改网点
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/updateBranch", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBranch(@RequestBody CompanyTemplateSaveBranchRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.updateBranch(request);
        return successResponse();
    }


    /**
     * 删除网点
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/deleteBranch", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteBranch(@RequestBody CompanyTemplateDeleteBranchRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.deleteBranch(request);
        return successResponse();
    }


    /**
     * 新增网点地址
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/saveBranchAddress", method = RequestMethod.POST)
    @ResponseBody
    public Object saveBranchAddress(@RequestBody CompanyTemplateSaveBranchAddressRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        return userLogisticsCompanyTemplateService.saveBranchAddress(request);
    }


    /**
     * 修改网点地址
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/updateBranchAddress", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBranchAddress(@RequestBody CompanyTemplateUpdateBranchAddressRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.updateBranchAddress(request);
        return successResponse();
    }


    /**
     * 删除网点地址
     */
    @AccessShield(value = "293",  extendsType = false)
    @RequestMapping(value = "/deleteBranchAddress", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteBranchAddress(@RequestBody CompanyTemplateDeleteBranchAddressRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.deleteBranchAddress(request);
        return successResponse();
    }

    /**
     * 设置默认模板
     */
    @RequestMapping(value = "/defaultTemplate", method = RequestMethod.POST)
    @ResponseBody
    public Object defaultTemplate(@RequestBody CompanyTemplateDefaultRequest request, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(request));
        initStaffRequest(request);
        userLogisticsCompanyTemplateService.defaultTemplate(request);
        return successResponse();
    }


    /**
     * 初始化请求参数
     */
    private void initStaffRequest(StaffRequest staffRequest) throws SessionException {
        staffRequest.setStaff(getStaff());
        staffRequest.setClientIp(IpUtils.getClientIP(super.request));
    }

    @RequestMapping(value = "/replaceTemplate", method = RequestMethod.POST)
    @ResponseBody
    public Object replaceTemplate(@RequestBody CompanyTemplateSaveRequest param, String api_name) throws SessionException {
        logger.debug("请求入参:" + JSONObject.toJSONString(param));
        initStaffRequest(param);
        // 按查询条件修改
        if (Objects.nonNull(param.getIsQuery()) && param.getIsQuery()) {
            org.springframework.util.Assert.isTrue(!progressService.hasProgress(param.getStaff(), ProgressEnum.PROGRESS_MODIFY_EXPRESS), "上一次批量修改快递模版还未执行完毕，请稍等！");
            if (Objects.nonNull(param.getClearLogisticsCompany()) && param.getClearLogisticsCompany()) {
                //清空模版
                eventCenter.fireEvent(this, new EventInfo("batch.delete.logistics").setArgs(new Object[]{
                        param.getStaff(), param.getTradeQueryParams(), param.getQueryId(), param.getCheckPdd(),
                        param.getLogisticsCompanyId()}), null);
            } else {
                //修改模版
                eventCenter.fireEvent(this, new EventInfo("batch.update.logistics").setArgs(new Object[]{
                        param.getStaff(), param.getTradeQueryParams(), null, param.getQueryId(), param.getCheckPdd(), null,
                        param.getCancelOutsid(), Objects.nonNull(param.getNeedGetWaybill()) && param.getNeedGetWaybill() ? "1" : "0",
                        param.getLogisticsCompanyId()}), null);
            }
            CompanyTemplateResponse companyTemplateResponse = new CompanyTemplateResponse();
            companyTemplateResponse.setIsSuccess(true);
            return companyTemplateResponse;
        } else {
            // 按勾选订单修改
            List<Long> sids = Arrays.stream(param.getSids().split(",")).map(Long::valueOf).collect(Collectors.toList());
            ILockCallback<CompanyTemplateResponse> lockCallback = () -> userLogisticsCompanyTemplateService.saveTemplateIdToTrade(param.getStaff(), param);
            return ptLockBusiness.ffLock(param.getStaff(), sids, lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                    "存在正在并发操作的订单，请稍后处理", "修改模板时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
        }
    }

    @RequestMapping(value = "/replaceAbroad", method = RequestMethod.POST)
    @ResponseBody
    public Object replaceAbroad(CompanyAbroadTemplateSaveRequest request, String api_name) throws SessionException {
        initStaffRequest(request);
        CompanyAbroadTemplateResponse response = userLogisticsCompanyTemplateService.replaceAbroad(request);
        return response;
    }

    @RequestMapping(value = "/queryOldTemplate", method = RequestMethod.GET)
    @ResponseBody
    public Object queryOldTemplate(CompanyTemplateSaveRequest param, String api_name) throws SessionException {
        Assert.notNull(param.getLogisticsCompanyId(), "快递公司id不能为空！");
        Assert.notNull(param.getSids(), "系统单号不能为空！");
        initStaffRequest(param);

        Long[] sids = Arrays.stream(param.getSids().split(","))
                .map(Long::valueOf)
                .toArray(Long[]::new);
        //得到查询的订单信息
        RequestEntranceEnum entranceEnum = RequestEntranceEnum.getByCode(param.getEntrance());
        List<Trade> trades = printDataSourceService.getTradesByBillType(new PrintDataSourceParam(entranceEnum == null ? null : entranceEnum.getBillType(), param.getStaff(), sids, false, false));

        List<CompanyTemplateTradeResponse> responseList = new ArrayList<>();
        Map<Long, UserExpressTemplate> longUserExpressTemplateMap = userLogisticsCompanyTemplateService.matchOldTemplateProxy(param.getStaff(), trades, userLogisticsCompanyBusiness.queryById(param.getStaff(), param.getLogisticsCompanyId(), true));
        longUserExpressTemplateMap.forEach((sid, template) -> {
            CompanyTemplateTradeResponse response = CompanyTemplateTradeResponse.builder()
                    .sid(sid)
                    .userExpressTemplate(template)
                    .build();
            responseList.add(response);
        });
        return responseList;
    }

    @RequestMapping(value = "/wave/replaceTemplate", method = RequestMethod.POST)
    @ResponseBody
    public Object waveReplaceTemplate(@RequestBody CompanyTemplateSaveWaveRequest param, String api_name) throws SessionException {
        initStaffRequest(param);
        Assert.isTrue(StringUtils.isNotEmpty(param.getWaveIds()), "波次号号不能为空！");
        List<Long> sidList = convertWaveIds2Sids(param.getStaff(), param.getWaveIds());
        Assert.isTrue(CollectionUtils.isNotEmpty(sidList), "波次可修改订单为空！");
        CompanyTemplateResponse response = null;
        for (List<Long> subSids : Lists.partition(sidList, 1000)) {
            String sids = StringUtils.join(subSids, ",");
            param.setSids(sids);
            ILockCallback<CompanyTemplateResponse> lockCallback = () -> userLogisticsCompanyTemplateService.saveTemplateIdToTrade(param.getStaff(), param);
            CompanyTemplateResponse subResponse = ptLockBusiness.ffLock(param.getStaff(), subSids, lockCallback, PtLockBusiness.GETWAYBILLCODE_PRINT_LOCKPREFIX,
                    "存在正在并发操作的订单，请稍后处理", "波次修改模板时加锁处理异常，请重试", PtLockBusiness.GETWAYBILLCODE_PRINT_LOCK_EXPIRE_TIME);
            if (response == null) {
                response = subResponse;
            } else {
                if (BooleanUtils.isFalse(response.getIsSuccess())) {
                    response.setIsSuccess(false);
                }
                if (CollectionUtils.isEmpty(response.getSuccessList())) {
                    response.setSuccessList(subResponse.getSuccessList());
                } else if (CollectionUtils.isNotEmpty(subResponse.getSuccessList())) {
                    response.getSuccessList().addAll(subResponse.getSuccessList());
                }
                if (CollectionUtils.isEmpty(response.getErrorList())) {
                    response.setErrorList(subResponse.getErrorList());
                } else if (CollectionUtils.isNotEmpty(subResponse.getErrorList())) {
                    response.getErrorList().addAll(subResponse.getErrorList());
                }
            }
        }
        if (BooleanUtils.isTrue(param.getClearLogisticsCompany())) {
            clearWaveExpressId(param.getStaff(), param.getWaveIds());
        } else if (CollectionUtils.isEmpty(response.getErrorList())) {
            updateWaveExpressId(param.getStaff(), param.getWaveIds(), param.getLogisticsCompanyId());
        }
        return response;
    }

    private List<Long> convertWaveIds2Sids(Staff staff, String waveIds) {
        List<Long> waveIdList = ArrayUtils.toLongList(waveIds);
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIdList.toArray(new Long[0]));
        List<Long> canceledWave = waves.stream().filter(wave -> Objects.equals(wave.getStatus(), Wave.STATUS_DELETED)).map(Wave::getId).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(canceledWave), String.format("波次%s已取消,不支持修改快递!", canceledWave));
        return tradeWaveService.querySidsByWaveIds(staff, waveIdList);
    }

    private void updateWaveExpressId(Staff staff, String waveIds, Long logisticsCompanyId) {
        if (StringUtils.isBlank(waveIds)) {
            return;
        }
        if (logisticsCompanyId == null) {
            return;
        }
        UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyBusiness.queryById(staff, logisticsCompanyId, false);
        if (userLogisticsCompany == null) {
            logger.debug(LogHelper.buildLog(staff, "根据波次修改快递，快递公司名称为空:" + logisticsCompanyId));
            return;
        }
        logger.debug(LogHelper.buildLog(staff, "根据波次修改快递，快递公司名称:" + logisticsCompanyId));
        tradeWaveService.updateWavesLogisticsCompanyId(staff, ArrayUtils.toLongList(waveIds), logisticsCompanyId, userLogisticsCompany.getExpressId());
    }

    private void clearWaveExpressId(Staff staff, String waveIds) {
        if (StringUtils.isBlank(waveIds)) {
            return;
        }
        tradeWaveService.updateWavesLogisticsCompanyId(staff, ArrayUtils.toLongList(waveIds), 0L, 0L);
    }
}

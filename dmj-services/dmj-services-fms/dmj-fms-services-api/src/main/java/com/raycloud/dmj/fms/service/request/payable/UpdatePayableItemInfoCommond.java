package com.raycloud.dmj.fms.service.request.payable;

import com.raycloud.dmj.fms.service.enums.PayableType;
import com.raycloud.dmj.fms.service.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


/**
 * 修改商品信息
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/10 下午5:33
 */
@Setter
@Getter
public class UpdatePayableItemInfoCommond extends BaseRequest implements Serializable {
    /**
     * @see PayableType
     */
    private PayableType payableType;

    /**
     * 修改的商品信息
     */
    private List<ItemInfo> itemInfoList;

    @Setter
    @Getter
    public static class ItemInfo implements Serializable{
        /**
         * 上游单据号
         */
        private String bussinessNumber;

        /**
         * 主商家编码Id
         */
        private Long sysItemId;

        /**
         * 规格商家编码Id
         */
        private Long sysSkuId;

        /**
         * 修改后商品标题
         */
        private String nowTitle;

        /**
         * 修改后主商家编码
         */
        private String nowOuterId;

        /**
         * 修改后规格商家编码
         */
        private String nowSkuOuterId;

        /**
         * 修改后规格
         */
        private String nowPropertiesName;
    }
}

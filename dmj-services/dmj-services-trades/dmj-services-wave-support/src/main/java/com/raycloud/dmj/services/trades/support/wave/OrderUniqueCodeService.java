package com.raycloud.dmj.services.trades.support.wave;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.commons.xserialnumber.api.XSerialNumberService;
import com.raycloud.dmj.Buffers;
import com.raycloud.dmj.EventNameConstants;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.basis.consts.PglDelayTableConst;
import com.raycloud.dmj.basis.dao.WmsPerformanceMiddleDao;
import com.raycloud.dmj.basis.service.IPglDelayStatisticsService;
import com.raycloud.dmj.business.wave.*;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.wave.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.basis.WmsPerformanceMiddle;
import com.raycloud.dmj.domain.basis.WmsPerformanceMiddleType;
import com.raycloud.dmj.domain.caigou.PdaFastScanReturnBO;
import com.raycloud.dmj.domain.caigou.PurchaseConfig;
import com.raycloud.dmj.domain.caigou.result.BatchResult;
import com.raycloud.dmj.domain.caigou.result.ErrorItem;
import com.raycloud.dmj.domain.caigou.result.SuccessItem;
import com.raycloud.dmj.domain.caigou.vo.PdaFastScanReturnVO;
import com.raycloud.dmj.domain.caigou.vo.PurchaseReturnUniqueCodeVo;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.enums.GoodsMemorandumEnum;
import com.raycloud.dmj.domain.enums.WaveTypeEnum;
import com.raycloud.dmj.domain.enums.YesNoEnum;
import com.raycloud.dmj.domain.item.extend.DmjItemExtend;
import com.raycloud.dmj.domain.item.sys.SkuERPBridge;
import com.raycloud.dmj.domain.item.tag.ItemSystemTag;
import com.raycloud.dmj.domain.item.tag.ItemTag;
import com.raycloud.dmj.domain.item.tag.dto.ItemTagDto;
import com.raycloud.dmj.domain.caigou.ColumnConfListWrapper;
import com.raycloud.dmj.domain.pt.enums.TemplateTypeEnum;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.model.waybill.get.WlbRequestGet;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.wave.enums.OrderUniqueCodeExtConfigEnum;
import com.raycloud.dmj.domain.wave.params.OrderUniqueCodeSplitParams;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.vo.AnalysisOrderUniqueCodesResult;
import com.raycloud.dmj.domain.wave.vo.BeforeArrivedCheckVO;
import com.raycloud.dmj.domain.wave.vo.UniqueCodeArrivedPrintVO;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.contants.BaseConstants;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.wash.params.ItemWashLabelVo;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.params.OrderUniqueCodeFilterDTO;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.enums.StockOutLocationEnum;
import com.raycloud.dmj.domain.wms.enums.WaveIdRecordEnum;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.dubbo.IWaybillPrintServiceDubbo;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.response.ItemCatIdAndSellerCidsResponse;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.business.OrderUniqueCodeAnalysisBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.OrderUniqueCodeArrivedPrintBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.UniqueCodeArrivedCheckBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.UniqueCodeCrossBorderBusiness;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodeConfigService;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodePushService;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodeService;
import com.raycloud.dmj.services.trades.wave.IUniqueCodeForceAllocateGoodsSupplierService;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.dubbo.IWorkingStorageSectionDubboService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.waybill.common.context.PtWaybillPathContext;
import com.raycloud.dmj.waybill.common.enums.AllocateGetPathEnum;
import com.raycloud.dmj.waybill.common.params.PathParam;
import com.raycloud.dmj.waybill.common.params.PathResult;
import com.raycloud.dmj.waybill.common.params.ResultInfo;
import com.raycloud.dmj.web.model.wms.*;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.web.source.OperateSourceContext;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.domain.contants.BaseConstants.*;
import static java.util.stream.Collectors.*;

/**
 * @Author: qingfeng
 * @Description: 订单唯一码服务
 * @Date: 2020-12-24 14:59
 */
@Service
public class OrderUniqueCodeService implements IOrderUniqueCodeService, ApplicationContextAware {

    private Logger logger = Logger.getLogger(this.getClass());

    private static final long DEFAULT_WAVE_ID = -5L;
    private static final String BUSINESS_NAME = "wave_order_unique";
    private static final String COLOMN_ITEM_TAG_NAMES = "itemTagNames";
    private static final String COLUMN_POSITION_STOCK_REGION = "positionStockRegion";
    private static final Integer PURCHASE_ITEM_QUERY_NUM_MAX = 100000;
    private static final Integer PURCHASE_ITEM_QUERY_BATCH_NUM = 1000;
    private static final Integer PAGE_ID = 201;
    private static final Integer POST_PRINT_PAGE_ID = 257;
    private static final Integer PARTION_NUM = 500;

    private static final String EXCEP_ITEM_RELATION_MODIFIED = "EXCEP_ITEM_RELATION_MODIFIED";
    /**
     * 商品上架
     */
    public static final Integer SOURCE_ITEM = 1;

    @Autowired
    private WaveQueryDao waveQueryDao;
    @Resource
    private IWmsService wmsService;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;
    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;
    @Resource
    private UniqueCodePglDao uniqueCodePglDao;
    @Resource
    private ILockService lockService;
    @Resource
    private WaveQueryConditionDao waveQueryConditionDao;
    @Resource
    private IItemServiceDubbo itemServiceDubbo;
    @Resource
    private WaveProgressBusiness waveProgressBusiness;
    @Resource
    private IOrderUniqueCodeConfigService orderUniqueCodeConfigService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private IShopService shopService;
    @Resource
    private WaveUniqueCodeLogDao waveUniqueCodeLogDao;
    @Resource
    private IEventCenter eventCenter;
    @Autowired
    private WaveSortingDao waveSortingDao;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private TbOrderDAO tbOrderDAO;
    @Resource
    private TbTradeDao tbTradeDao;
    @Resource
    private ITradeWaveService tradeWaveService;
    @Resource
    private IOrderUniqueCodePushService orderUniqueCodePushService;
    @Resource
    private OrderUniqueCodeTradeSyncBusiness orderUniqueCodeTradeSyncBusiness;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private OrderUniqueCodeHelpBusiness orderUniqueCodeHelpBusiness;
    @Autowired(required = false)
    private IUniqueCodePurchaseService uniqueCodePurchaseServiceDubbo;
    @Resource
    private XSerialNumberService xSerialNumberService;
    @Resource
    private OrderUniqueCodeUnboundLogDao orderUniqueCodeUnboundLogDao;
    @Resource
    private IBufferService bufferService;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IShareUrlService shareUrlService;
    @Resource
    private IWorkingStorageSectionDubboService workingStorageSectionDubboService;
    @Resource(name = "tradeTraceServiceDubbo")
    private ITradeTraceService tradeTraceServiceDubbo;
    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    private OrderUniqueCodeGenerateBusiness orderUniqueCodeGenerateBusiness;
    @Resource
    private IUniqueCodeForceAllocateGoodsSupplierService uniqueCodeForceAllocateGoodsSupplierService;
    @Resource
    private IUserService userService;

    @Resource
    private OrderUniqueCodeArrivedPrintBusiness orderUniqueCodeArrivedPrintBusiness;
    @Resource
    private IPglDelayStatisticsService pglDelayStatisticsService;
    @Resource
    private IWaveColumnConfService waveColumnConfService;
    @Resource
    private ICache cache;
    @Resource
    private OrderUniqueCodeAnalysisBusiness orderUniqueCodeAnalysisBusiness;
    @Resource
    private WmsPerformanceMiddleDao wmsPerformanceMiddleDao;
    private ApplicationContext applicationContext;
    private IOrderUniqueCodeService beanProxy;
    @Resource
    private IWaybillPrintServiceDubbo waybillPrintServiceDubbo;
    @Resource
    private UniqueCodeRelationDao uniqueCodeRelationDao;
    @Resource
    private IUniqueCodeBaseService uniqueCodeBaseService;
    @Resource
    private IItemUniqueCodeExtendService itemUniqueCodeExtendService;

    @PostConstruct
    public void startup() {
        beanProxy = applicationContext.getBean(OrderUniqueCodeService.class);
    }
    @Resource
    private IExpressCompanyService expressCompanyService;
    @Resource
    private UniqueCodeArrivedCheckBusiness uniqueCodeArrivedCheckBusiness;

    @Resource
    private IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;

    @Resource
    private FeatureService featureService;

    @Resource
    private IOrderUniqueCodeTagService uniqueCodeTagService;
    @Resource
    private UniqueCodeExtendHelpBusiness uniqueCodeExtendHelpBusiness;
    @Resource
    private IUniqueCodeExtendService uniqueCodeExtendService;
    @Resource
    private IIndexDubboService indexDubboService;
    @Resource
    private IOrderUniqueCodeExamineService orderUniqueCodeExamineService;
    @Resource
    private UniqueCodeHelpBusiness uniqueCodeHelpBusiness;
    @Resource
    private ITradeWaveProportionChangeItemService tradeWaveProportionChangeItemService;
    @Resource
    private UniqueCodeCrossBorderBusiness crossBorderBusiness;
    @Resource
    private UniqueCodeExtendDao uniqueCodeExtendDao;
    @Resource
    private IWaveWriteTradeTraceService waveWriteTradeTraceService;
    @Resource
    private IUniqueCodeBaseExtService uniqueCodeBaseExtService;
    @Resource
    private LogisticsWaveRelationBusiness logisticsService;
    @Resource
    private IOrderUniqueCodeExtendService orderUniqueCodeExtendService;


    @Override
    public Map<Long, Pair<Integer, Integer>> queryOrderUniqueCodeUsed(Staff staff, Long warehouseId) {
        return queryOrderUniqueCodeUsedNew(staff, warehouseId, false);
    }

    @Override
    public Map<Long, Pair<Integer, Integer>> queryOrderUniqueCodeUsedNew(Staff staff, Long warehouseId, boolean containItemUniqueCode) {
        Map<Long, Pair<Integer, Integer>> usedMap = Maps.newHashMap();

        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        Date created = (OrderUniqueCodeUtils.isTrue(config.getPositionOccupyCalculateType()) ? DateUtil.getFirstTime(new Date()) : null);

        // 如果分拣货位勾选了隔天解除绑定（但是不应该解除新标的绑定）
        boolean careNewSplit = (created != null && OrderUniqueCodeUtils.isTrue(config.getNewSplit()));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "查询分拣货位占用，careNewSplit：" + careNewSplit));
        }

        long start = System.currentTimeMillis();
        List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeUsed(staff, warehouseId, created, careNewSplit, containItemUniqueCode);
        if (logger.isInfoEnabled()){
            logger.info(LogHelper.buildLog(staff, String.format("queryOrderUniqueCodeUsedNew, 查询唯一码使用分拣货位数量耗时:【%s】" , (System.currentTimeMillis() - start))));
        }
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return usedMap;
        }

        Map<Long, List<WaveUniqueCode>> postionIdMap = uniqueCodes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getPositionNoId));
        for (Map.Entry entry : postionIdMap.entrySet()) {
            List<WaveUniqueCode> codes = (List<WaveUniqueCode>) entry.getValue();
            if (CollectionUtils.isEmpty(codes)) {
                continue;
            }
            Long count = codes.stream().collect(Collectors.counting());
            Long sum = codes.stream().collect(Collectors.summarizingInt(WaveUniqueCode::getItemNum)).getSum();
            usedMap.put((Long) entry.getKey(), Pair.of(count.intValue(), sum.intValue()));
        }
        return usedMap;
    }

    @Override
    public List<WaveItem> queryUniqueItemsSimple(Staff staff, String uniqueCode, Integer type) {
        // 波次唯一码转化
        uniqueCode = formatUniqueCode(uniqueCode, type);
        if (StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }
        WaveUniqueCodeParams uniqueCodeParams = new WaveUniqueCodeParams();
        uniqueCodeParams.setUniqueCode(uniqueCode);
        uniqueCodeParams.setType(type);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryByConditionWithNotWaveId(staff, uniqueCodeParams);
        List<Long> pureSysItemIds = codes.stream().map(WaveUniqueCode::getSysItemId).filter(Objects::nonNull).distinct().collect(toList());
        List<Long> pureSysSkuIds = codes.stream().map(WaveUniqueCode::getSysSkuId).filter(Objects::nonNull).distinct().collect(toList());
        if (CollectionUtils.isEmpty(pureSysItemIds) && CollectionUtils.isEmpty(pureSysSkuIds)) {
            return Lists.newArrayList();
        }
        // 商品信息
        Map<String, DmjItem> itemMap = waveHelpBusiness.queryItemMap(staff, pureSysItemIds, pureSysSkuIds);
        // 水洗唛
        Map<Long, List<ItemWashLabelVo>> itemId2ItemWashLabelVoMap = itemServiceDubbo.getItemWashLabelVoBySysItemIds(staff, pureSysItemIds).stream().collect(Collectors.groupingBy(ItemWashLabelVo::getSysItemId));
        return codes.stream()
                .map(code -> buildItemByUnique(itemMap.get(WmsKeyUtils.buildItemKey(code.getSysItemId(), code.getSysSkuId())), code, itemId2ItemWashLabelVoMap.get(code.getSysItemId())))
                .collect(toList());

    }

    private String formatUniqueCode(String uniqueCode, Integer type) {
        // 订单、商品唯一码不需要转化
        if (Objects.equals(type, 1) || Objects.equals(type, 2)) {
            return uniqueCode;
        }
        WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesUniqueBarcode(uniqueCode);
        if (multiWavesSeedKey != null) {
            return multiWavesSeedKey.getUniqueCode();
        }
        return uniqueCode;
    }

    public WaveItem buildItemByUnique(DmjItem item, WaveUniqueCode code, List<ItemWashLabelVo> itemWashLabelVos) {
        WaveItem waveItem = new WaveItem();
        waveItem.setSysItemId(code.getSysItemId());
        waveItem.setSysSkuId(code.getSysSkuId());
        waveItem.setOuterId(code.getOuterId());
        if (item != null) {
            waveItem.setItemOuterId(item.getOuterId());
            waveItem.setOuterId(item.getOuterId());
            waveItem.setTitle(item.getTitle());
            waveItem.setPicPath(item.getPicPath());
            waveItem.setWashLabelJson(WmsUtils.getWashLabelJson(itemWashLabelVos));
            if (item instanceof DmjSku) {
                DmjSku sku = (DmjSku) item;
                waveItem.setPropertiesName(sku.getPropertiesName());
                waveItem.setPropertiesAlias(sku.getPropertiesAlias());
                waveItem.setPicPath(sku.getPicPath());
                waveItem.setSkuOuterId(sku.getOuterId());
            }
        }
        return waveItem;
    }

    @Override
    public PageListBase<WaveUniqueCode> queryList(Staff staff, OrderUniqueCodeQueryParams params) {
        if (DataUtils.checkLongNotEmpty(params.getQueryId())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "按照条件查询订单唯一码，queryId：" + params.getQueryId()));
            }
            List<WaveQueryCondition> conditions = waveQueryConditionDao.queryList(staff, Lists.newArrayList(params.getQueryId()), 0);
            Assert.notEmpty(conditions, "未查询到条件！");
            String content = conditions.get(0).getContent();
            OrderUniqueCodeQueryParams queryParams = JSON.parseObject(content, OrderUniqueCodeQueryParams.class);
            params = queryParams;
        }
        initParams(staff, params);
        // 爆款码转唯一码
        params.setUniqueCodes(uniqueCodeExtendHelpBusiness.convertHotSaleCodesToUniqueCodes(staff, params.getUniqueCodes()));
        params.setNeedInitRelationCodes(false);
        // 来源外链，需要添加外链配置信息
        if (BooleanUtils.isTrue(params.getShareFilter())) {
            shareUrlService.buildParams(staff, params);
        }
        // 供应商排序填充供应商管理的排序
        orderUniqueCodeExtendService.fillSortSupplierIds4Query(staff, params);
        PageListBase<WaveUniqueCode> pageList = new PageList<>();
        if (params.getUniqueCodeIds() != null && params.getUniqueCodeIds().size() > 0) {
            params.setPage(null);
            List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
            // 打囤货标记
            fillStoreFlag4QueryList(staff, params, codes);
            // 填充关联表信息
            fillRelation(staff, params, codes);
            pageList.setList(codes);
            return pageList;
        }
        if (params.getPage() == null) {
            params.setPage(new Page().setPageSize(1000));
        }
        Long count = countOrderUniqueCodeAdaptive(staff, params);
        pageList.setTotal(count);
        pageList.setPage(params.getPage());
        if (count > params.getPage().getStartRow()) {
            List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
            // 打囤货标记
            fillStoreFlag4QueryList(staff, params, codes);
            // 填充关联表信息
            fillRelation(staff, params, codes);
            pageList.setList(codes);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    private void fillStoreFlag4QueryList(Staff staff, OrderUniqueCodeQueryParams params, List<WaveUniqueCode> codes) {
        if (!params.isQueryStoreFlag() || CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<Long> storeSids = codes.stream().filter(c -> Objects.equals(c.getCodeType(), 2) &&
                        (Objects.equals(c.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType()) ||
                                Objects.equals(c.getStatus(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType())))
                .map(WaveUniqueCode::getSid).distinct().collect(toList());
        if (CollectionUtils.isEmpty(storeSids)) {
            return;
        }
        List<UniqueCodeExtend> uniqueCodeExtends = uniqueCodeExtendDao.listSid4StoreFlag(staff, storeSids);
        if (CollectionUtils.isEmpty(uniqueCodeExtends)) {
            return;
        }
        Map<Long, Long> storeFlagMap = uniqueCodeExtends.stream().collect(toMap(UniqueCodeExtend::getSid, UniqueCodeExtend::getSid, (a, b) -> a));
        for (WaveUniqueCode code : codes) {
            boolean storeFlag = Objects.equals(code.getCodeType(), 2) &&
                    Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()) &&
                    storeFlagMap.get(code.getSid()) != null;
            code.setStoreFlag(storeFlag ? 1 : 0);
        }
    }

    @Override
    public PageListBase<WaveUniqueCode> queryByOrderId(Staff staff, OrderUniqueCodeQueryParams params) {
        PageListBase<WaveUniqueCode> pageList = new PageList<>();
        if (params.getPage() == null) {
            params.setPage(new Page().setPageSize(1000));
        }
        Long count = waveUniqueCodeDao.countOrderUniqueCode(staff, params);
        pageList.setTotal(count);
        pageList.setPage(params.getPage());
        if (count > params.getPage().getStartRow()) {
            List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
            pageList.setList(codes);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    @Override
    public void fillCondition(Staff staff,OrderUniqueCodeQueryParams params){
        params.setOpenSearchOpt(true);
        params.setUnboundOrUserIds(getUserIds(staff,params));
        params.setWarehouseIds(getWarehouseIds(staff,params));
    }


    /**
     * 获取staff自有店铺权限
     */
    private List<Long> getUserIds(Staff staff,OrderUniqueCodeQueryParams params){
        List<Long> userIds = params.getUserIds();
        List<User> users = userService.queryUsersByStaffPrivilege(staff);
        if(!CollectionUtils.isEmpty(userIds)) {
            userIds = users.stream().map(user -> user.getId()).filter(userIds::contains).collect(Collectors.toList());
        }else{
            userIds = users.stream().map(user -> user.getId()).collect(Collectors.toList());
            // 默认添加供销店铺权限
            userIds.add(100000000L);
        }
        return userIds;
    }

    /**
     * 获取staff自有仓库权限
     */
    private List<Long> getWarehouseIds(Staff staff,OrderUniqueCodeQueryParams params){
        List<Long> warehousesIds = params.getWarehouseIds();
        List<Warehouse> warehouses = warehouseService.queryWarehouseByPower(staff,null);
        if(!CollectionUtils.isEmpty(warehousesIds)) {
            warehousesIds = warehouses.stream().map(warehouse -> warehouse.getId()).filter(warehousesIds::contains).collect(Collectors.toList());
        }else{
            warehousesIds = warehouses.stream().map(warehouse -> warehouse.getId()).collect(Collectors.toList());
        }
        return warehousesIds;
    }

    List<WaveUniqueCode> queryOrderUniqueCodeAdaptive(Staff staff, OrderUniqueCodeQueryParams params) {
        List<WaveUniqueCode> list;
        if (params.isPageUserPgl()) {
            try {
                list = uniqueCodePglDao.queryPglOrderUniqueCodeByCondition(staff, params);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "唯一码查询调用pgl失败！"), e);
                list = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
            }
        } else {
            list = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        }
        return list;
    }

    @Override
    public Long countOrderUniqueCodeAdaptive(Staff staff, OrderUniqueCodeQueryParams params) {
        if (params.isNeedInitRelationCodes()) {
            initParams(staff, params);
        }
        if (params.isNeedPierceThroughQuery()) {
            OrderUniqueCodeUtils.pierceThroughQuery(params);
        }
        beforeQueryInitParam(staff, params);
        Long count;
        checkPglDelay(staff, params);

        if (params.isCountUserPgl()) {
            try {
                count = uniqueCodePglDao.countPglOrderUniqueCode(staff, params);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "唯一码查询调用pgl失败！"), e);
                count = waveUniqueCodeDao.countOrderUniqueCode(staff, params);
            }

        } else {
            count = waveUniqueCodeDao.countOrderUniqueCode(staff, params);
        }
        logCountResult(staff, params, count);
        return count;
    }

    private void logCountResult(Staff staff, OrderUniqueCodeQueryParams params, Long count) {
        try {
            if (logger.isDebugEnabled()) {
                if (params != null) {
                    logger.debug(LogHelper.buildLog(staff, "countOrderUniqueCodeAdaptive查询条件：" + JSON.toJSONString(params)));
                    logger.debug(LogHelper.buildLog(staff, "countOrderUniqueCodeAdaptive数量：" + count));
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "logCountResult失败"), e);
        }

    }

    private void checkPglDelay(Staff staff, OrderUniqueCodeQueryParams params) {
        if (!params.isCountUserPgl()) {
            return;
        }
        if (pglDelayStatisticsService.isSyncDelay(staff.getDbKey(), PglDelayTableConst.DELAY)) {
            params.setCountUserPgl(false);
            params.setPageUserPgl(false);
        }
    }

    @Override
    public List<WaveUniqueCode> queryOrderUniqueCodeByCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        if (params.getPage() != null
                && params.getPage().getPageNo() != null
                && params.getPage().getPageNo() > 5000) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码查询翻页超过5000，clueId：" + staff.getClueId() + "，pageNo：" + params.getPage().getPageNo()));
            }
        }
        if (BooleanUtils.isTrue(params.getEmptyQuery())) {
            return Lists.newArrayList();
        }
        beforeQueryInitParam(staff, params);
        if (params.isNeedInitRelationCodes()) {
            initParams(staff, params);
        }
        if (params.isNeedPierceThroughQuery()) {
            OrderUniqueCodeUtils.pierceThroughQuery(params);
        }
        if (params.isNeedPermissionControl()) {
            fillCondition(staff, params);
        }
        // 爆款码转唯一码
        params.setUniqueCodes(uniqueCodeExtendHelpBusiness.convertHotSaleCodesToUniqueCodes(staff, params.getUniqueCodes(), params.getOperateSource()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeAdaptive(staff, params);
        initVals(staff, codes);
        fillTemplateName(staff, codes);
        fillLogisticsCompanyName(staff, codes);
        fillShopName(staff, codes);
        fillWarehouseName(staff, codes);
        fillItemTags(staff, codes);
        fillItemType(staff, codes, params);
        if (params.isNeedPositionInfo()) {
            fillPositionInfo(staff, codes);
        }

        // 格式化分拣货位
        if (params.isNeedFormatPositionNo()) {
            formatPositionNo(staff, codes, params.isNeedAppendPositionSortNo());
        }

        // 打囤货标记
        fillStoreFlag4QueryList(staff, params, codes);
        // 填充关联表信息
        fillRelation(staff, params, codes);

        return codes;
    }

    private void fillRelation(Staff staff, OrderUniqueCodeQueryParams params, List<WaveUniqueCode> codes) {
        if (!params.isNeedRelationMsg() || CollectionUtils.isEmpty(codes)) {
            return;
        }
        OrderUniqueCodeQueryParams relationParams = new OrderUniqueCodeQueryParams();
        relationParams.setUniqueCodes(codes.stream().map(WaveUniqueCode::getUniqueCode).distinct().collect(Collectors.toList()));
        List<UniqueCodeRelation> uniqueCodeRelationList = uniqueCodeRelationDao.queryByUniqueCondition(staff, relationParams);
        if (CollectionUtils.isEmpty(uniqueCodeRelationList)) {
            return;
        }
        Map<String, String> takenOrderCodeMaps = Maps.newHashMap();
        Map<String, Integer> takenOrderStatusMaps = Maps.newHashMap();
        for (UniqueCodeRelation code : uniqueCodeRelationList) {
            if (code.filterBusinessType(UniqueCodeRelation.BusinessType.TAKEN_ORDER)) {
                takenOrderCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                takenOrderStatusMaps.put(code.getUniqueCode(), code.getStatus());
            }
        }
        for (WaveUniqueCode code : codes) {
            code.setTakenOrderStatus(takenOrderStatusMaps.get(code.getUniqueCode()) != null && !Objects.equals(takenOrderStatusMaps.get(code.getUniqueCode()), 20) ? 1 : 0);
        }
    }

    private void beforeQueryInitParam(Staff staff, OrderUniqueCodeQueryParams params) {
        if (params == null) {
            return;
        }
        // 将供应商分类转换成供应商
        if (!CollectionUtils.isEmpty(params.getSupplierCategoryNames())) {
            List<Supplier> suppliers = supplierService.queryByCategoryNameList(staff, params.getSupplierCategoryNames());
            if (CollectionUtils.isEmpty(suppliers)) {
                return;
            }
            params.setCategorySupplierIds(suppliers.stream().map(Supplier::getId).collect(Collectors.toList()));
            params.setSupplierCategoryNames(null);
        }
    }

    private void fillItemType(Staff staff, List<WaveUniqueCode> codes, OrderUniqueCodeQueryParams params){
        if (CollectionUtils.isEmpty(codes)||!params.isNeedItemType()) {
            return;
        }
        List<DmjItem> dmjItems = new ArrayList<>();
        List<Long> sysItemIds = codes.stream().map(WaveUniqueCode::getSysItemId).distinct().collect(toList());
        for (List<Long> itemIds : Lists.partition(sysItemIds, PARTION_NUM)) {
            dmjItems.addAll(itemServiceDubbo.queryItemWithSysItemId(staff,itemIds));
        }
        Map<Long, DmjItem> itemMap = dmjItems.stream().collect(Collectors.toMap(DmjItem::getSysItemId, Function.identity()));
        codes.forEach(code->{
            DmjItem item = itemMap.get(code.getSysItemId());
            item = item==null?new DmjItem():item;
            if(item.isSuite()){
                code.setItemTypeName("套件商品");
            } else if(item.isGroup()){
                code.setItemTypeName("组合商品");
            } else if(item.isProcess()){
                code.setItemTypeName("加工商品");
            }else if(item.isNormal()){
                code.setItemTypeName("普通商品");
            }
        });
    }

    private void fillItemTags(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<String> visibleColumnFieldList = waveColumnConfService.getVisibleColumnFieldList(staff, PAGE_ID);
        if (CollectionUtils.isEmpty(visibleColumnFieldList) || !visibleColumnFieldList.contains(COLOMN_ITEM_TAG_NAMES)) {
            return;
        }
        List<ItemTagDto> itemTagDtos = codes.stream().map(item -> {
            ItemTagDto itemTagDto = new ItemTagDto();
            itemTagDto.setSysItemId(item.getSysItemId());
            itemTagDto.setSysSkuId(item.getSysSkuId());
            return itemTagDto;
        }).collect(Collectors.toList());

        Map<String, ItemTagDto> itemTagDtoMap = Optional.ofNullable(itemServiceDubbo.queryItemTagByItemId(staff, itemTagDtos)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(ItemTagDto::buildItemKey, Function.identity(), (v1, v2) -> v1));

        for (WaveUniqueCode code : codes) {
            ItemTagDto itemTagDto = itemTagDtoMap.get(WmsKeyUtils.buildItemKey(code.getSysItemId(), code.getSysSkuId()));
            if (itemTagDto == null) {
                continue;
            }
            Set<String> itemTagNames = Sets.newHashSet();
            if (!CollectionUtils.isEmpty(itemTagDto.getItemTags())) {
                itemTagNames.addAll(itemTagDto.getItemTags().stream().map(ItemTag::getName).collect(toSet()));
            }
            if (!CollectionUtils.isEmpty(itemTagDto.getItemSystemTags())) {
                itemTagNames.addAll(itemTagDto.getItemSystemTags().stream().map(ItemSystemTag::getName).collect(toSet()));
            }
            if (CollectionUtils.isEmpty(itemTagNames)) {
                continue;
            }
            code.setItemTagNames(String.join(",", itemTagNames));
        }
    }

    @Override
    public void fillPositionNoInfo(Staff staff, List<WaveUniqueCode> codes) {
        // 分拣库区
        fillPositionInfo(staff, codes);
        // 格式化分拣货位
        formatPositionNo(staff, codes, true);
    }

    private void formatPositionNo(Staff staff, List<WaveUniqueCode> codes, boolean needAppendPositionSortNo) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        formatPositionNo(staff, codes, config);
        if (needAppendPositionSortNo) {
            OrderUniqueCodeUtils.appendPositionNoSort(codes, config);
        }
    }

    private void formatPositionNo(Staff staff, List<WaveUniqueCode> codes, OrderUniqueCodeConfig config) {
        if (config == null || config.getPositionShowType() == null
                || config.getPositionShowType() == 0 || CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<Long> positionNoIds = codes.stream().map(WaveUniqueCode::getPositionNoId).filter(DataUtils::checkLongNotEmpty).distinct().collect(toList());
        if (CollectionUtils.isEmpty(positionNoIds)) {
            return;
        }

        // 查询分拣货位信息
        WarehousePositionConfigParams params = new WarehousePositionConfigParams();
        params.setIds(positionNoIds);
        List<WarehousePositionConfig> positionConfigs = wmsService.queryWarehousePositionConfigList(staff, params);
        if (CollectionUtils.isEmpty(positionConfigs)) {
            return;
        }
        Map<Long, WarehousePositionConfig> positionConfigMap = positionConfigs.stream().collect(Collectors.toMap(WarehousePositionConfig::getId, t -> t, (v1, v2) -> v1));
        for (WaveUniqueCode code : codes) {
            formatPositionNo(code, config.getPositionShowType(), positionConfigMap.get(code.getPositionNoId()));
        }
    }

    public static void formatPositionNo(WaveUniqueCode code, Integer positionShowType, WarehousePositionConfig config) {
        if (!DataUtils.checkLongNotEmpty(code.getPositionNoId()) || config == null) {
            return;
        }
        String group = config.getGroup();
        String positionNo = config.getPositionNo();
        // 1：分组排-层-格 2：分组排层格
        if (positionShowType == 1) {
            code.setPositionNo(group + positionNo);
        } else if (positionShowType == 2) {
            code.setPositionNo(group + positionNo.replaceAll("-", ""));
        }
    }

    private void fillPositionInfo(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<Long> positionIds = codes.stream().map(WaveUniqueCode::getPositionNoId).distinct().collect(toList());
        WarehousePositionConfigParams params = new WarehousePositionConfigParams();
        params.setIds(positionIds);
        params.setNeedStockRegionInfo(true);
        List<WarehousePositionConfig> positionConfigs = wmsService.queryWarehousePositionConfigList(staff, params);
        if (CollectionUtils.isEmpty(positionConfigs)) {
            return;
        }
        Map<Long, WarehousePositionConfig> configMap = positionConfigs.stream().collect(Collectors.toMap(WarehousePositionConfig::getId, a -> a, (b1, b2) -> b1));
        for (WaveUniqueCode code : codes) {
            WarehousePositionConfig config = configMap.get(code.getPositionNoId());
            if (config == null) {
                continue;
            }
            code.setPositionStockRegion(config.getStockRegionName());
            code.setPositionGroup(config.getGroup());
        }
    }

    @Override
    public Long countOrderUniqueCode(Staff staff, OrderUniqueCodeQueryParams params) {
        beforeQueryInitParam(staff, params);
        if (params.isNeedPermissionControl()) {
            fillCondition(staff, params);
        }
        return waveUniqueCodeDao.countOrderUniqueCode(staff, params);
    }

    private void fillTemplateName(Staff staff, List<WaveUniqueCode> expresses) {
        if (CollectionUtils.isEmpty(expresses)) {
            return;
        }
        Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);
        for (WaveUniqueCode express : expresses) {
            express.setTemplateName(getTemplateName(express.getTemplateType(), express.getTemplateId(), templateMap));
        }
    }

    private void fillLogisticsCompanyName(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        Set<Long> logisticsIdSet = codes.stream().filter(code -> code.getLogisticsCompanyId() != null && code.getLogisticsCompanyId() > 0L)
                .map(WaveUniqueCode::getLogisticsCompanyId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(logisticsIdSet)) {
            return;
        }
        List<UserLogisticsCompany> logisticsCompanyList = userLogisticsCompanyBusiness.queryByIds(staff, new ArrayList<>(logisticsIdSet), false);
        if (CollectionUtils.isEmpty(logisticsCompanyList)) {
            return;
        }
        Map<Long, UserLogisticsCompany> logisticsCompanyMap = logisticsCompanyList.stream().collect(Collectors.toMap(UserLogisticsCompany::getId, Function.identity(), (t1, t2) -> t1));
        for (WaveUniqueCode code : codes) {
            if (code.getLogisticsCompanyId() == null || code.getLogisticsCompanyId() == 0L || logisticsCompanyMap.get(code.getLogisticsCompanyId()) == null) {
                continue;
            }
            code.setLogisticsCompanyName(logisticsCompanyMap.get(code.getLogisticsCompanyId()).getName());
        }
    }

    @Override
    public void fillWarehouseName(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<Warehouse> warehouses = warehouseService.queryAll(staff, CommonConstants.ENABLE_STATUS_NORMARL);
        if (CollectionUtils.isEmpty(warehouses)) {
            return;
        }
        Map<Long, String> warehouseMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Warehouse::getName));
        for (WaveUniqueCode code : codes) {
            code.setWarehouseName(warehouseMap.get(code.getWarehouseId()));
        }
    }

    private void fillShopName(Staff staff, List<WaveUniqueCode> expresses) {
        if (CollectionUtils.isEmpty(expresses)) {
            return;
        }
        List<Shop> shopList = shopService.queryByCompanyId(staff);
        Map<Long, String> shopMap = new HashMap<>();
        Map<Long, String> shopSourceMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(shopList)) {
            for (Shop shop : shopList) {
                shopMap.put(shop.getUserId(), StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : shop.getTitle());
                shopSourceMap.put(shop.getUserId(), shop.getSourceName());
            }
        }
        for (WaveUniqueCode express : expresses) {
            express.setShopName(shopMap.get(express.getUserId()));
            express.setShopSource(shopSourceMap.get(express.getUserId()));
        }
    }

    private static String getTemplateName(Integer templateType, Long templateId, Map<String, UserExpressTemplate> templateMap) {
        if (DataUtils.checkLongNotEmpty(templateId)) {
            UserExpressTemplate template = templateMap.get((templateType == 0 ? "" : "w") + templateId);
            if (template != null) {
                return template.getName();
            }
        }

        return "";
    }

    private void validateCondition(Staff staff, WaveQueryCondition condition) {
        if (condition == null) {
            return;
        }
        List<WaveQueryCondition> conditions = queryConditionAndGroups(staff);
        List<WaveQueryCondition> repeats = conditions.stream().filter(c -> Objects.equals(c.getName(), condition.getName())
                && Objects.equals(c.getType(), condition.getType())
                && Objects.equals(c.getGroupId(), condition.getGroupId())
                && !Objects.equals(c.getId(), condition.getId())).collect(toList());
        if (!CollectionUtils.isEmpty(repeats)) {
            throw new IllegalArgumentException("名称重复，请重新输入！");
        }
    }

    @Override
    public List<WaveQueryCondition> queryConditionList(Staff staff) {
        List<WaveQueryCondition> conditionAndGroups = queryConditionAndGroups(staff);
        if (CollectionUtils.isEmpty(conditionAndGroups)) {
            return Collections.EMPTY_LIST;
        }

        List<WaveQueryCondition> groups = conditionAndGroups.stream().filter(c -> Objects.equals(CommonConstants.JUDGE_YES, c.getType())).collect(toList());
        List<WaveQueryCondition> conditions = conditionAndGroups.stream().filter(c -> Objects.equals(CommonConstants.JUDGE_NO, c.getType())).collect(toList());
        if (CollectionUtils.isEmpty(conditions)) {
            return groups;
        }
        Map<Long, List<WaveQueryCondition>> conditionMap = conditions.stream().collect(Collectors.groupingBy(WaveQueryCondition::getGroupId));
        groups.forEach(g -> g.setConditions(conditionMap.get(g.getId())));
        return groups;
    }

    private List<WaveQueryCondition> queryConditionAndGroups(Staff staff) {
        // 添加默认分组
        WaveQueryCondition defaultGroup = new WaveQueryCondition();
        defaultGroup.setId(0L);
        defaultGroup.setName("未分组");
        defaultGroup.setType(CommonConstants.JUDGE_YES);
        List<WaveQueryCondition> conditionAndGroups = Lists.newArrayList(defaultGroup);

        // 添加公共分组
        WaveQueryCondition commonGroup = new WaveQueryCondition();
        commonGroup.setId(-1L);
        commonGroup.setName("公共分组");
        commonGroup.setType(CommonConstants.JUDGE_YES);
        conditionAndGroups.add(commonGroup);

        List<WaveQueryCondition> list = new ArrayList<>();
        // 查询分组与条件，员工维度
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryList(staff, null, null);
        if (!CollectionUtils.isEmpty(conditions)) {
            list.addAll(conditions);
        }

        // 查询公共分组条件，公司维度
        List<WaveQueryCondition> commons = waveQueryConditionDao.queryCommon(staff);
        if (!CollectionUtils.isEmpty(commons)) {
            list.addAll(commons);
        }
        //按分组顺序排序

        Collections.sort(list, Comparator.comparing(pre -> Optional.ofNullable(pre.getGroupId()).orElse(999999L)));
        Collections.sort(list, (pre, next) -> {
            int res = Optional.ofNullable(pre.getGroupId()).orElse(999999L).compareTo(Optional.ofNullable(next.getGroupId()).orElse(999999L));
            return (res != 0) ? res : Optional.ofNullable(pre.getOrderNo()).orElse(999999).compareTo(Optional.ofNullable(next.getOrderNo()).orElse(999999));
        });

        conditionAndGroups.addAll(list);

        return conditionAndGroups;
    }

    @Override
    @Transactional
    public WaveQueryCondition addCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        Assert.notNull(params, "条件不能为空！");
        Assert.hasText(params.getName(), "名称不能为空！");
        params.setQueryId(null);

        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setCompanyId(staff.getCompanyId());
        condition.setContent(JSON.toJSONString(params));
        condition.setCreated(new Date());
        condition.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
        condition.setName(params.getName());
        condition.setStaffId(staff.getId());
        condition.setGroupId(params.getParamsGroupId());
        condition.setType(params.isFromParamsGroup() ? 1 : 0);
        condition.setOrderNo(params.getOrderNo());

        validateCondition(staff, condition);
        waveQueryConditionDao.batchInsert(staff, Lists.newArrayList(condition));
        return condition;
    }

    @Override
    @Transactional
    public void batchDeleteCondition(Staff staff, List<Long> ids) {
        Assert.notEmpty(ids, "条件Id不能为空！");
        checkDeleteGroup(staff, ids);
        waveQueryConditionDao.batchDelete(staff, ids);
    }

    private void checkDeleteGroup(Staff staff, List<Long> ids) {
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryList(staff, ids, null);
        if (!CollectionUtils.isEmpty(conditions)
                && Objects.equals(conditions.get(0).getType(), CommonConstants.JUDGE_YES)) {
            Long groupId = conditions.get(0).getId();
            // 如果该分组下还有查询条件，则不允许删除
            List<WaveQueryCondition> groupCondition = waveQueryConditionDao.queryList(staff, null, CommonConstants.JUDGE_YES, groupId);
            if (CollectionUtils.isEmpty(groupCondition)) {
                return;
            }
            throw new IllegalArgumentException("该分组中下还有过滤条件，不允许删除！");
        }
    }

    @Override
    @Transactional
    public void saveCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        Assert.notNull(params, "条件不能为空！");
        Assert.isTrue(DataUtils.checkLongNotEmpty(params.getQueryId()), "自定义条件Id不能为空！");
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryList(staff, Lists.newArrayList(params.getQueryId()), null);
        if (CollectionUtils.isEmpty(conditions)) {
            List<WaveQueryCondition> common = waveQueryConditionDao.queryCommon(staff);
            if (!CollectionUtils.isEmpty(common)) {
                conditions = common.stream().filter(c -> params.getQueryId().equals(c.getId())).collect(toList());
            }
        }
        Assert.notEmpty(conditions, "根据queryId未查询到自定义条件！");

        WaveQueryCondition condition = conditions.get(0);
        condition.setName(params.getName());
        condition.setStaffId(staff.getId());
        params.setQueryId(null);
        condition.setContent(JSON.toJSONString(params));
        condition.setOrderNo(params.getOrderNo());
        condition.setTimeType(params.getTimeType());

        validateCondition(staff, condition);
        waveQueryConditionDao.batchUpdate(staff, Lists.newArrayList(condition));
    }

    @Override
    public void importUniqueCode(Staff staff, String[][] data, Long warehouseId) {
        Assert.notNull(data, "导入数据不能为空！");
        Assert.isTrue(data.length > 0, "导入数据不能为空！");
        List<String> errorMsg = new ArrayList<>();
        // 转化数据
        ProgressData progress = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_ORDER_UNIQUE_CODE_IMPORT, WaveProgressContext.build(warehouseId));
        List<WaveUniqueCode> codes = convertAndCheckImportData(staff, data, errorMsg, warehouseId);
        if (CollectionUtils.isEmpty(errorMsg)) {
            progress.setCountCurrent(progress.getCountAll() / 2);
        } else {
            progress.setErrorMsg(errorMsg);
            progress.setCountCurrent(progress.getCountAll());
            progress.setProgress(2);
        }
        waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_ORDER_UNIQUE_CODE_IMPORT, progress, WaveProgressContext.build(warehouseId));

        // 保存唯一码
        if (CollectionUtils.isEmpty(errorMsg)) {
            saveImportCode(staff, codes);
            progress.setCountCurrent(progress.getCountAll());
            progress.setProgress(2);
            waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_ORDER_UNIQUE_CODE_IMPORT, progress, WaveProgressContext.build(warehouseId));
        }
    }

    private void saveImportCode(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveUniqueCode> saveCodes = Lists.newArrayList();
        for (WaveUniqueCode code : codes) {
            if (code.getNum() == null || code.getNum() <= 0) {
                continue;
            }
            for (int i = 0; i < code.getNum(); i++) {
                WaveUniqueCode save = new WaveUniqueCode();
                BeanUtils.copyProperties(code, save);
                saveCodes.add(save);
            }
        }
        orderUniqueCodeHelpBusiness.saveUniqueCodes(staff, saveCodes, null);

        //外采唯一码自动生成唯一码采购单
        if (!CollectionUtils.isEmpty(saveCodes)) {
            List<String> uniqueCodes = saveCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList());
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.auto.create.purchase").setArgs(new Object[]{staff, uniqueCodes}), null);
        }
    }

    private List<WaveUniqueCode> convertAndCheckImportData(Staff staff, String[][] data, List<String> errorMsg, Long warehouseId) {
        Map<Integer, Pair<String, Integer>> outerIdAndNumMap = Maps.newHashMap();
        List<WaveUniqueCode> codes = new ArrayList<>();
        int lineNum = 1;
        for (String[] lineData : data) {
            StringBuilder message = new StringBuilder();
            lineNum++;
            String outerId = StringUtils.trimToNull(lineData[0]);
            if (StringUtils.isBlank(outerId)) {
                message.append("第" + lineNum + "行，商家编码不能为空！");
            }
            Integer num = 0;
            if (StringUtils.isBlank(lineData[1]) || !NumberUtils.isNumber(lineData[1])) {
                message.append("第" + lineNum + "行，备货数量为空或格式不正确！");
            } else {
                num = Integer.valueOf(lineData[1]);
            }
            if (outerIdAndNumMap.get(outerId) != null) {
                message.append("第" + lineNum + "行，商家编码重复！");
            }
            if (message.length() == 0) {
                outerIdAndNumMap.put(lineNum, Pair.of(outerId, num));
            } else {
                errorMsg.add(message.toString());
            }
        }
        // 判断商家编码是否存在
        if (MapUtils.isNotEmpty(outerIdAndNumMap)) {
            List<String> outerIds = outerIdAndNumMap.values().stream().map(Pair::getKey).distinct().collect(toList());
            Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, outerIds, 123);
            Set<Map.Entry<Integer, Pair<String, Integer>>> entries = outerIdAndNumMap.entrySet();
            for (Map.Entry<Integer, Pair<String, Integer>> entry : entries) {
                StringBuilder message = new StringBuilder();
                Pair<String, Integer> value = entry.getValue();
                if (!itemMap.containsKey(value.getKey())) {
                    message.append("第" + entry.getKey() + "行，商家编码不存在！");
                    errorMsg.add(message.toString());
                    continue;
                }
                DmjItem item = (DmjItem) itemMap.get(value.getKey());
                if (item.getActiveStatus() != 1) {
                    message.append("第" + entry.getKey() + "行，商品编码" + value.getKey() + "对应的商品已停用！");
                    errorMsg.add(message.toString());
                } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(item.getSkus())) {
                    message.append("第" + entry.getKey() + "行，商品编码" + value.getKey() + "对应的商品含有规格！");
                    errorMsg.add(message.toString());
                } else if (Integer.valueOf(1).equals(item.getIsVirtual())) {
                    message.append("第" + entry.getKey() + "行，商品编码" + value.getKey() + "对应的商品是虚拟商品！");
                    errorMsg.add(message.toString());
                } else if (StringUtils.isNotEmpty(item.getType()) && ("1".equals(item.getType()) || "2".equals(item.getType()))) {
                    message.append("第" + entry.getKey() + "行，商品编码" + value.getKey() + "对应的商品是套件商品！");
                    errorMsg.add(message.toString());
                }
                if (message.length() == 0) {
                    WaveUniqueCode code = new WaveUniqueCode();
                    code.setOuterId(value.getKey());
                    code.setSysItemId(item.getSysItemId());
                    if (item instanceof DmjSku) {
                        code.setSysSkuId(((DmjSku) item).getSysSkuId());
                    } else {
                        code.setSysSkuId(0L);
                    }
                    code.setNum(value.getValue());
                    ItemSupplierBridge bestItemSupplierBridge = item.getBestItemSupplierBridge();
                    if (bestItemSupplierBridge != null) {
                        code.setSupplierId(bestItemSupplierBridge.getSupplierId());
                        code.setSupplierName(bestItemSupplierBridge.getSupplierName());
                    }
                    code.setStatus(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
                    code.setType(CommonConstants.JUDGE_YES);
                    code.setCodeType(3);
                    code.setWaveId(DEFAULT_WAVE_ID);
                    code.setWarehouseId(warehouseId);
                    code.setStockStatus(CommonConstants.ENABLE_STATUS_DELETED);
                    code.setTagIds(WaveUtils.getOrderUniqueStockTagId(code));
                    code.setCreated(new Date());
                    code.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
                    codes.add(code);
                }
            }
        }
        return codes;
    }

    private void initVals(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        for (WaveUniqueCode code : codes) {
            code.setNum(1);
            code.setSerialNumberStr(code.getDateNo() + "-" + code.getSerialNumber());
        }
        // 填充标签名称
        uniqueCodeTagService.fillTagNames(staff, codes);
    }

    @Override
    public WaveUniqueCode queryByUniqueCode(Staff staff, String uniqueCode) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(Lists.newArrayList(uniqueCode));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "唯一码不存在！");
        WaveUniqueCode code = codes.get(0);
        return code;
    }

    @Override
    public List<WaveUniqueCode> fastQueryByUniqueCodes(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyList();
        }
        return waveUniqueCodeDao.queryByUniqueCodes(staff, uniqueCodes, 1);
    }

    private List<WaveUniqueCode> queryBySidAndDateNo(Staff staff, Long sid, Integer dateNo, Integer batchNo) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(Lists.newArrayList(sid));
        params.setDateNo(dateNo);
        params.setBatchNo(batchNo);
        return waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
    }

    @Override
    public List<WaveUniqueCode> queryBySid(Staff staff, Long sid) {
        if (DataUtils.checkLongNotEmpty(sid)) {
            OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
            params.setSids(Lists.newArrayList(sid));
            return waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        } else {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "根据sid查询唯一码，sid为空！"));
            }
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional
    public Integer batchOffShelfOrCancelNotThrowException(Staff staff, List<Long> ids, Integer opType) {
        try {
            return batchOffShelfOrCancel(staff, ids, opType);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "唯一码取消失败！"), e);
            return 0;
        }
    }

    @Override
    public Integer batchOffShelfOrCancel(Staff staff, List<Long> ids, Integer opType){
        return batchOffShelfOrCancel(staff, ids, opType, null);
    }

    @Override
    @Transactional
    public Integer batchOffShelfOrCancel(Staff staff, List<Long> ids, Integer opType, Integer requestSource) {
        Assert.notEmpty(ids, "请选择唯一码！");
        Assert.notNull(opType, "请选择操作类型！");
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodeIds(ids);
        List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Integer num;
        try {
            Assert.notEmpty(uniqueCodes, "未查询到唯一码！");
            num = executeBatchOffShelfOrCancel(staff, uniqueCodes, opType, requestSource);
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.error(LogHelper.buildErrorLog(staff, e, "唯一码下架失败"), e);
            }
            waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, uniqueCodes, e, WaveUniqueOpType.ORDER_OFF_SHELF, null, requestSource));
            throw e;
        }

        return num;
    }

    @Override
    @Transactional
    public void batchOffShelfOrCancelUniqueCode(Staff staff, OrderUniqueCodeQueryParams params, Integer opType, ProgressData progressData) {
        Assert.notNull(opType, "操作类型不能为空！");
        List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(uniqueCodes, "未查询到唯一码！");
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_BATCH_CANCEL_ORDER_UNIQUE_CODE, progressData, 30);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码批量取消/下架，数量：" + uniqueCodes.size()));
        }
        Integer count = executeBatchOffShelfOrCancel(staff, uniqueCodes, opType);
        progressData.setSucNum(Long.valueOf(count));
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_BATCH_CANCEL_ORDER_UNIQUE_CODE, progressData, 80);
    }

    private boolean isOffOpType(Integer opType) {
        return opType != null && opType <= CommonConstants.JUDGE_NO;
    }

    @Override
    public Integer executeBatchOffShelfOrCancel(Staff staff, List<WaveUniqueCode> uniqueCodes, Integer opType) {
        return executeBatchOffShelfOrCancel(staff, uniqueCodes, opType, null);
    }

    /**
     * 执行唯一码批量取消/下架操作
     * @param opType 操作类型 0 下架；1 取消 -1 缺货拆分下架
     * @return
     */
    @Override
    public Integer executeBatchOffShelfOrCancel(Staff staff, List<WaveUniqueCode> uniqueCodes, Integer opType, Integer requestSource) {
        Assert.notEmpty(uniqueCodes, "唯一码不能为空");
        /**
         * 等待采购/等待收货/等待拣选时，才允许下架
         * 等待采购/等待拣选时，才允许取消
         */
        List<WaveUniqueCode> filters = uniqueCodes.stream().filter(code -> isOffOpType(opType)
                ? (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()))
                : (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()))).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filters)){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, "唯一码不允许操作" + (isOffOpType(opType) ? "下架" : "取消"));
        }
        OrderUniqueCodeConfig orderUniqueCodeConfig = orderUniqueCodeConfigService.get(staff);
        // 添加关联信息
        addRelations(staff, opType, filters, orderUniqueCodeConfig);
        List<WaveUniqueCode> updates = new ArrayList<>();
        Set<Long> orderIds = new HashSet<>();
        for (WaveUniqueCode code : filters) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setId(code.getId());
            update.setStatus(isOffOpType(opType)
                    ? OrderUniqueCodeStatusEnum.OFF_SHELF.getType()
                    : OrderUniqueCodeStatusEnum.CANCEL.getType());
            // 取消状态的唯一码一定解绑
            if (Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), update.getStatus())) {
                OrderUniqueCodeUtils.buildRelease(update);
            }
            update.setOffTime(isOffOpType(opType) ? new Date() : null);
            orderIds.add(code.getOrderId());
            code.setOldStatus(code.getStatus());
            code.setStatus(update.getStatus());
            code.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            updates.add(update);
        }
        waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, uniqueCodes, null,
                isOffOpType(opType) ? WaveUniqueOpType.ORDER_OFF_SHELF : WaveUniqueOpType.ORDER_CANCEL, null, requestSource));

        if (Objects.equals(CommonConstants.JUDGE_YES, opType)) {
            cancelDeleteRelations(staff, filters, orderIds);
        }
        // 放在更新订单波次号后面更新唯一码, 防止更新波次号的时候根据订单查不到唯一码
        waveUniqueCodeDao.batchUpdate(staff, updates);
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(filters);
        for (List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> subWaveUniqueCodeLogDTOS : Lists.partition(waveUniqueCodeLogDTOS, 3000)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, Lists.newArrayList(subWaveUniqueCodeLogDTOS), isOffOpType(opType) ? WaveUniqueOpType.ORDER_OFF_SHELF : WaveUniqueOpType.ORDER_CANCEL}), null);
        }
        orderUniqueCodePushService.pushUniqueCodes(staff, filters);
        // 商品下架
        if (isOffOpType(opType)) {
            itemOffShelf(staff, filters, opType);
        }
        return filters.size();
    }

    private void cancelDeleteRelations(Staff staff, List<WaveUniqueCode> codes, Set<Long> orderIds) {
        List<Long> filterOrderIds = orderIds.stream().filter(DataUtils::checkLongNotEmpty).collect(toList());
        if (!CollectionUtils.isEmpty(filterOrderIds)) {
            for (List<Long> subFilterOrderIds : Lists.partition(filterOrderIds, 3000)) {
                wmsService.deleteAllocateGoodsRecordsByMultiIds(staff, null, subFilterOrderIds, null);
            }
        }

        List<WaveUniqueCode> filters = codes.stream().filter(code -> DataUtils.checkLongNotEmpty(code.getSid())).collect(toList());
        if (!CollectionUtils.isEmpty(filters)) {
            Map<String, Integer> statusMap = filters.stream().collect(toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getStatus, (a, b) -> a));
            orderUniqueCodeTradeSyncBusiness.kickOutWave(staff, filters, statusMap);
        }
    }

    @Override
    @Transactional
    public void revertOffShelf(Staff staff, OrderUniqueCodeQueryParams params, ProgressData progressData, Integer source) {
        List<Integer> statusList = Lists.newArrayList();
        // 只撤销已下架的唯一码
        statusList.add(OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
        params.setStatusList(statusList.stream().distinct().collect(toList()));
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "未查询到唯一码！");
        for (WaveUniqueCode code : codes) {
            Assert.isTrue(DataUtils.checkLongNotEmpty(code.getSid()), code.getUniqueCode() + "唯一码已解绑不允许撤销！");
        }
        List<WaveUniqueCode> updates = Lists.newArrayList();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setStatus(Optional.ofNullable(code.getPrintNum()).orElse(0) > 0
                    ? OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()
                    : OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
            update.setOldStatus(code.getStatus());
            update.setOffTime(TradeTimeUtils.INIT_DATE);
            if (Objects.equals(code.getStockStatus(), CommonConstants.VALUE_YES)) {
                update.setStatus(OrderUniqueCodeStatusEnum.WAIT_PICK.getType());
            }
            update.setStockPosition(Objects.equals(code.getStockStatus(), CommonConstants.VALUE_YES) && Objects.equals(Optional.ofNullable(code.getPrintNum()).orElse(0), 0)
                    ? UniqueCodeStockPositionEnum.SHELVES.getType() : UniqueCodeStockPositionEnum.DEFAULT.getType());
            code.setStatus(update.getStatus());
            code.setOldStatus(update.getOldStatus());
            updates.add(update);
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes), SOURCE_ITEM.equals(source) ? WaveUniqueOpType.REVERT_OFF_SHELF_BY_ITEM : WaveUniqueOpType.REVERT_OFF_SHELF}), null);
        waveUniqueCodeDao.batchUpdate(staff, updates);

        // 订单去除下架标签和备注
        revertOrderOffTagRemark(staff, codes);

    }

    /**
     * 订单去除下架标签和备注
     */
    @Override
    public void revertOrderOffTagRemark(Staff staff, List<WaveUniqueCode> codes) {
        Set<Long> codeIds = codes.stream().map(WaveUniqueCode::getId).collect(Collectors.toSet());
        Set<Long> sids = codes.stream().map(WaveUniqueCode::getSid).collect(toSet());
        Set<Long> orderIds = codes.stream().map(WaveUniqueCode::getOrderId).collect(toSet());
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        OrderUniqueCodeQueryParams sidParams = new OrderUniqueCodeQueryParams();
        sidParams.setSids(Lists.newArrayList(sids));
        sidParams.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.OFF_SHELF.getType()));
        // 这里查询出来的结果有可能主从没有同步导致结果是旧的
        List<WaveUniqueCode> waveUniqueCodeList = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, sidParams);
        // 这个订单还有标签未下架，则不去除标签
        Set<Long> hasOffSids = waveUniqueCodeList.stream().map(WaveUniqueCode::getSid).collect(toSet());
        Set<Long> hasNotOffSids = sids.stream().filter(sid -> !hasOffSids.contains(sid)).collect(toSet());

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("调用交易item.uniquecode.onshelf事件，sid:%s,orderId:%s", hasNotOffSids, orderIds)));
        }
        if (CollectionUtils.isEmpty(hasNotOffSids) && CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo("item.uniquecode.onshelf").setArgs(new Object[]{staff, orderIds, hasNotOffSids}), null);
    }

    /**
     * 下架商品和订单标记下架
     */
    private void itemOffShelf(Staff staff, List<WaveUniqueCode> uniqueCodes, Integer opType) {
        List<WaveUniqueCode> offShelfAllCode = uniqueCodes.stream().filter(code -> BooleanUtils.isTrue(code.getOffAllItem())).collect(toList());
        List<WaveUniqueCode> offShelfSingleCode = uniqueCodes.stream().filter(code -> BooleanUtils.isNotTrue(code.getOffAllItem())).collect(toList());
        doItemOffShelf(staff, offShelfAllCode, true, opType);
        doItemOffShelf(staff, offShelfSingleCode, false, opType);
    }

    /**
     * 下架商品和订单标记下架
     */
    private void doItemOffShelf(Staff staff, List<WaveUniqueCode> uniqueCodes, boolean offAllUnicode, Integer opType) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return;
        }
        Set<Long> skuIds = Sets.newHashSet();
        Set<Long> itemIds = Sets.newHashSet();
        Set<String> itemKeys = Sets.newHashSet();
        Set<Long> orderIds = Sets.newHashSet();
        for (WaveUniqueCode data : uniqueCodes) {
            orderIds.add(data.getOrderId());
            if (Objects.equals(data.getSysSkuId(), -1L)) {
                itemIds.add(data.getSysItemId());
                itemKeys.add(data.getSysItemId() + StringUtils.EMPTY);
            } else {
                skuIds.add(data.getSysSkuId());
                itemKeys.add(String.format("%s_%s_%s", data.getSysItemId(), data.getSysSkuId(), SKU_SUFFIX));
            }
        }
        if (offAllUnicode) {
            if (!CollectionUtils.isEmpty(itemIds)) {
                itemServiceDubbo.updateGoodsStatusByItemId(staff, Lists.newArrayList(itemIds));
            }
            if (!CollectionUtils.isEmpty(skuIds)) {
                itemServiceDubbo.updateGoodsStatusBySkuId(staff, Lists.newArrayList(skuIds));
            }
            if (!CollectionUtils.isEmpty(itemKeys)) {
                bufferService.buffer(Buffers.build(staff, EVENT_ITEM_UNICODE_OFF_SHELF), Lists.newArrayList(itemKeys));
                eventCenter.fireEvent(this, new EventInfo(EVENT_ITEM_UNICODE_OFF_SHELF).setArgs(new Object[]{staff}), null);
            }
        }
        if (!CollectionUtils.isEmpty(orderIds)) {
            eventCenter.fireEvent(this, new EventInfo(EVENT_TRADE_UNICODE_OFF_SHELF).setArgs(new Object[]{staff, orderIds, null, opType}), null);
        }
    }

    private void addRelations(Staff staff, Integer opType, List<WaveUniqueCode> uniqueCodes, OrderUniqueCodeConfig orderUniqueCodeConfig) {
        if (!Objects.equals(CommonConstants.JUDGE_NO, opType)) {
            return;
        }
        List<Long> ids = uniqueCodes.stream().map(WaveUniqueCode::getId).collect(Collectors.toList());
        Set<Long> offShelfAllShops = OrderUniqueCodeUtils.getOffShelfShopUserIds(orderUniqueCodeConfig, CommonConstants.JUDGE_YES);
        Set<Long> offShelfSingleShops = OrderUniqueCodeUtils.getOffShelfShopUserIds(orderUniqueCodeConfig, CommonConstants.JUDGE_NO);
        List<WaveUniqueCode> relationCodes = uniqueCodes.stream()
                .filter(data -> OrderUniqueCodeUtils.isOffAll(orderUniqueCodeConfig, offShelfAllShops, offShelfSingleShops, data))
                .peek(data -> data.setOffAllItem(true))
                .collect(toList());
        if (!CollectionUtils.isEmpty(relationCodes)) {
            List<Long> sysItemIds = relationCodes.stream().map(WaveUniqueCode::getSysItemId).collect(toList());
            List<Long> sysSkuIds = relationCodes.stream().map(WaveUniqueCode::getSysSkuId).collect(toList());
            List<String> relationOuterIds = relationCodes.stream().map(WaveUniqueCode::getOuterId).distinct().collect(toList());
            OrderUniqueCodeQueryParams temp = new OrderUniqueCodeQueryParams();
            temp.setOuterIdOrItemOuterIds(relationOuterIds);
            temp.setSysItemIds(sysItemIds);
            temp.setSysSkuIds(sysSkuIds);
            temp.setNotInIdList(ids);
            temp.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()));
            Map<String, List<WaveUniqueCode>> outerId2CodeMap = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, temp)
                    .stream()
                    .filter(data -> OrderUniqueCodeUtils.isOffAll(orderUniqueCodeConfig, offShelfAllShops, offShelfSingleShops, data))
                    .peek(data -> data.setOffAllItem(true))
                    .collect(Collectors.groupingBy(WaveUniqueCode::getOuterId));
            relationOuterIds.forEach(outerId -> uniqueCodes.addAll(outerId2CodeMap.getOrDefault(outerId, Lists.newArrayList())));
        }
    }

    @Override
    @Transactional
    public void batchCancelReject(Staff staff, OrderUniqueCodeQueryParams params, ProgressData progressData) {
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "未查询到唯一码！");
        List<WaveUniqueCode> filters;
        if (Objects.equals(params.getCancelType(), 2)) {
            filters = codes.stream().filter(OrderUniqueCodeUtils::canReturnExchange).collect(toList());
            doBatchReturnExchange(staff, progressData, filters);
        } else {
            filters = codes.stream().filter(OrderUniqueCodeUtils::canCancelReject).collect(toList());
            doBatchCancelReject0(staff, progressData, filters, false, params.getCancelTypeEnum());
        }
    }

    @Override
    public void batchPickReturnFireEvent(Staff staff, OrderUniqueCodeQueryParams params) {
        // 已解绑的
        pickReturnInitParam(params);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "无可操作【取消（拣选归还）】的唯一码！");
        Assert.isTrue(!waveProgressBusiness.hasProgress(staff, ProgressEnum.PROGRESS_PICK_RETURN_ORDER_UNIQUE_CODE), "正在取消（拣选归还），请稍等！");
        if (waveProgressBusiness.addProgress(staff, ProgressEnum.PROGRESS_PICK_RETURN_ORDER_UNIQUE_CODE)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.pick.return").setArgs(new Object[]{staff, params}), null);
        }
    }

    @Override
    @Transactional
    public void batchPickReturn(Staff staff, OrderUniqueCodeQueryParams params, ProgressData progressData) {
        pickReturnInitParam(params);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "无符合条件的唯一码！");
        List<WaveUniqueCode> filters = codes.stream().filter(OrderUniqueCodeUtils::canPickReturn).collect(toList());
        Assert.notEmpty(codes, "无符合条件的唯一码！");

        // 按照仓库分组
        Map<Long, List<WaveUniqueCode>> codeWarehouseMap = filters.stream().collect(Collectors.groupingBy(WaveUniqueCode::getWarehouseId));
        for (Map.Entry<Long, List<WaveUniqueCode>> entry : codeWarehouseMap.entrySet()) {
            List<WaveUniqueCode> value = entry.getValue();
            for (List<WaveUniqueCode> subCodes : Lists.partition(value, 500)) {
                try {
                    doBatchPickReturn(staff, subCodes);
                    waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData, 30);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "唯一码归还失败！"));
                }
            }
        }
    }

    private void pickReturnInitParam(OrderUniqueCodeQueryParams params) {
        // 已解绑的
        params.setUnbind(2);
        // 已拣选的
        List<Integer> statusList = params.getStatusList();
        if (CollectionUtils.isEmpty(statusList)) {
            params.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.PICKED.getType()));
        } else {
            statusList.add(OrderUniqueCodeStatusEnum.PICKED.getType());
        }
    }

    private void doBatchPickReturn(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        // 从解绑记录表中查找需要归还的货位
        Map<String, OrderUniqueCodeUnboundLog> unbindLogMap = getPickReturnUnbindLogMap(staff, codes);
        if (MapUtils.isEmpty(unbindLogMap)){
            return;
        }
        Map<String, Long> goodsSectionMap = getPickReturnGoodsSection(staff, unbindLogMap);
        if (MapUtils.isEmpty(goodsSectionMap)) {
            return;
        }
        WorkingStorageSection workingStorageSection = WmsUtils.createSection(staff, WorkingStorageSection.TypeEnum.PICK, codes.get(0).getWarehouseId());
        workingStorageSection.setNeedCheckStockPositive(false);
        workingStorageSection.setBusiType(StockChangeBusiType.PICK_BACK);
        List<WorkingStorageSectionGoods> goodsList = new ArrayList<>();
        List<WaveUniqueCode> effectiveCodes = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            OrderUniqueCodeUnboundLog unboundLog = unbindLogMap.get(code.getUniqueCode());
            if (unboundLog == null || StringUtils.isEmpty(unboundLog.getGoodsSectionCode())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "唯一码拣选归还，无解绑记录或者解绑记录无货位！" + code.getUniqueCode()));
                }
                continue;
            }
            if (!goodsSectionMap.containsKey(code.getWarehouseId() + "_" + unboundLog.getGoodsSectionCode())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "唯一码拣选归还，货位不存在！" + code.getUniqueCode() + "，" + code.getGoodsSectionCode()));
                }
                continue;
            }
            effectiveCodes.add(code);
            WorkingStorageSectionGoods good = new WorkingStorageSectionGoods();
            good.setWarehouseId(code.getWarehouseId());
            good.setSysItemId(code.getSysItemId());
            good.setWorkOrderId(code.getUniqueCode());
            good.setSysSkuId(code.getSysSkuId());
            good.setNum(1);
            good.setOprGoodsSectionId(goodsSectionMap.get(code.getWarehouseId() + "_" + unboundLog.getGoodsSectionCode()));
            good.setQualityType(true);
            good.setGoodsSectionCode(unboundLog.getGoodsSectionCode());
            goodsList.add(good);
        }
        workingStorageSection.setGoodsList(goodsList);
        wmsService.upShelf(staff, workingStorageSection);

        // 取消唯一码
        pickReturnCancelCodes(staff, effectiveCodes);
    }

    private Map<String, Long> getPickReturnGoodsSection(Staff staff, Map<String, OrderUniqueCodeUnboundLog> unbindLogMap) {
        List<String> goodsSectionCodes = unbindLogMap.values().stream().map(OrderUniqueCodeUnboundLog::getGoodsSectionCode).distinct().collect(toList());
        if (CollectionUtils.isEmpty(goodsSectionCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "解绑记录中没有货位"));
            }
            return Maps.newHashMap();
        }
        List<GoodsSectionVo> goodsSectionVos = wmsService.queryGoodsSectionsByCodes(staff, goodsSectionCodes);
        if (CollectionUtils.isEmpty(goodsSectionVos)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "解绑记录中的货位系统中已不存在！"));
            }
            return Maps.newHashMap();
        }
        return goodsSectionVos.stream().collect(Collectors.toMap(this::buildGsKey, GoodsSectionVo::getId, (a1, a2) -> a1));
    }

    public String buildGsKey(GoodsSectionVo goodsSectionVo) {
        return goodsSectionVo.getWarehouseId() + "_" + goodsSectionVo.getCode();
    }

    private Map<String, OrderUniqueCodeUnboundLog> getPickReturnUnbindLogMap(Staff staff, List<WaveUniqueCode> codes) {
        List<OrderUniqueCodeUnboundLog> orderUniqueCodeUnboundLogs = orderUniqueCodeUnboundLogDao.queryAllSidByLastOne(staff, codes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
        if (CollectionUtils.isEmpty(orderUniqueCodeUnboundLogs)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到唯一码解绑记录！"));
            }
            return Maps.newHashMap();
        }
        return orderUniqueCodeUnboundLogs.stream().collect(Collectors.toMap(OrderUniqueCodeUnboundLog::getUniqueCode, a -> a, (a1, a2) -> a1));
    }

    private void pickReturnCancelCodes(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveUniqueCode> updates = codes.stream().map(code -> {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            // 取消状态的唯一码一定解绑
            OrderUniqueCodeUtils.buildRelease(update);
            update.setStatus(OrderUniqueCodeStatusEnum.CANCEL.getType());
            return update;
        }).collect(toList());
        waveUniqueCodeDao.batchUpdate(staff, updates);
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes);
        waveUniqueCodeLogDTOS.forEach(dto -> dto.setPickReturnCancel(true));
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, waveUniqueCodeLogDTOS, WaveUniqueOpType.ORDER_CANCEL}), null);
    }

    /**
     * 批量取消驳回唯一码
     * @param staff
     * @param progressData
     * @param filters
     * @param isNewSplit 是否拆分新建
     */
    @Override
    @Transactional
    public List<WaveUniqueCode> doBatchCancelReject(Staff staff, ProgressData progressData, List<WaveUniqueCode> filters, boolean isNewSplit) {
        return doBatchCancelReject0(staff, progressData, filters, isNewSplit, null);
    }

    @Transactional
    public List<WaveUniqueCode> doBatchCancelReject0(Staff staff, ProgressData progressData, List<WaveUniqueCode> filters, boolean isNewSplit, UnboundReasonTypeEnum unboundReasonTypeEnum) {
        Assert.notEmpty(filters, "无可取消（复制新增）的唯一码！");
        Map<String, Long> uniqueCodeShortSidMap = filters.stream().filter(f -> DataUtils.checkLongNotEmpty(f.getShortSid())).collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getShortSid));
        Map<String, Long> uniqueCodeSidMap = filters.stream().filter(f -> DataUtils.checkLongNotEmpty(f.getSid())).collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getSid));
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData, 20);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码取消驳回，数量：" + filters.size()));
        }
        Map<String, UniqueCodeExtend> uniqueCodeExtendMap = getUniqueCodeExtendMap4CancelReject(staff, filters);
        List<WaveUniqueCode> inserts = new ArrayList<>();
        List<UniqueCodeExtend> insertExtends = new ArrayList<>();
        List<WaveUniqueCode> pushCodes = new ArrayList<>();
        for (WaveUniqueCode filter : filters) {
            if (isNewSplit) {
                filter.setNewSplit(CommonConstants.JUDGE_NO);
            }
            // 拷贝生成新的唯一码
            copyUniqueCodes(inserts, filter);
            copyUniqueCodeExtends(insertExtends, uniqueCodeExtendMap.get(filter.getUniqueCode()));
            // 已打印唯一码解绑；未打印唯一码取消
            if (filter.getPrintNum() <= 0) {
                filter.setStatus(OrderUniqueCodeStatusEnum.CANCEL.getType());
                // 取消状态的唯一码一定解绑
                OrderUniqueCodeUtils.buildRelease(filter);
            } else {
                OrderUniqueCodeUtils.buildRelease(filter);
            }
            if (Objects.equals(filter.getStockStatus(), CommonConstants.JUDGE_NO)) {
                pushCodes.add(filter);
            }
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData, 30);
        Map<String, String> codeMap = Maps.newHashMap();
        // 自定义首字母
        Integer customFirstDigitNum = OrderUniqueCodeUtils.getCustomFirstDigitNum(orderUniqueCodeConfigService.get(staff));
        for (List<WaveUniqueCode> subCodes : Lists.partition(inserts, 1000)) {
            String[] uniqueCodes = xSerialNumberService.nextIds(staff.getCompanyId(), BUSINESS_NAME, subCodes.size());
            for (int i = 0, size = subCodes.size(); i < size; i++) {
                String newCode = OrderUniqueCodeUtils.fillUniqueCodeZero(uniqueCodes[i], null, customFirstDigitNum);
                codeMap.put(subCodes.get(i).getUniqueCode(), newCode);
                subCodes.get(i).setUniqueCode(newCode);
            }
        }
        // 自动取消不重新配货, 新增的码和原来一样
        if (!Objects.equals(unboundReasonTypeEnum, UnboundReasonTypeEnum.AUTO_CANCEL_REJECT)) {
            Set<Long> supplierIds = uniqueCodeForceAllocateGoodsSupplierService.getSupplierIds(staff);
            reSetStockStatus(staff, inserts, supplierIds);
            // 供应商固定家里拿货
            orderUniqueCodeGenerateBusiness.supplierForeAllocateGoods(staff, inserts, supplierIds);
        }
        // 商品没有供应商，取订单的供应商
        uniqueCodeBaseExtService.fillOrderSupplier4Generate(staff, inserts);
        List<WaveUniqueCode> needPushInserts = inserts.stream().filter(in -> Objects.equals(in.getStockStatus(), CommonConstants.JUDGE_NO)).collect(toList());
        if (!CollectionUtils.isEmpty(needPushInserts)) {
            pushCodes.addAll(needPushInserts);
        }

        if (!CollectionUtils.isEmpty(filters)) {
            for (List<WaveUniqueCode> subFilters : Lists.partition(filters, 2000)) {
                waveUniqueCodeDao.batchUpdate(staff, subFilters);
            }
        }
        if (!CollectionUtils.isEmpty(inserts)) {
            for (List<WaveUniqueCode> subInserts : Lists.partition(inserts, 2000)) {
                waveUniqueCodeDao.batchInsert(staff, subInserts);
            }
        }
        if (!CollectionUtils.isEmpty(insertExtends)) {
            for (List<UniqueCodeExtend> subInsertExtends : Lists.partition(insertExtends, 2000)) {
                subInsertExtends.forEach(s -> s.setUniqueCode(codeMap.get(s.getUniqueCode())));
                List<String> uniqueCodes = subInsertExtends.stream().filter(s -> StringUtils.isNotEmpty(s.getUniqueCode())).map(UniqueCodeExtend::getUniqueCode).collect(toList());
                Map<String, Long> uniqueCodeIdMap = waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, uniqueCodes).stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getId, (a, b) -> a));
                subInsertExtends.forEach(s -> s.setUniqueCodeId(uniqueCodeIdMap.get(s.getUniqueCode())));
                uniqueCodeExtendDao.batchInsert(staff, subInsertExtends);
            }
        }

        fireBatchCancelRejectEvent(staff, inserts, filters, uniqueCodeShortSidMap, isNewSplit,uniqueCodeSidMap);
        // 外采唯一码自动生成唯一码采购单
        if (!CollectionUtils.isEmpty(inserts)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.auto.create.purchase").setArgs(new Object[]{staff, inserts.stream().filter(code -> Objects.equals(code.getStockStatus(), 0)).map(WaveUniqueCode::getUniqueCode).collect(toList())}), null);
        }
        orderUniqueCodePushService.pushUniqueCodes(staff, pushCodes);
        if (progressData != null) {
            progressData.setSucNum(Long.valueOf(filters.size()));
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData, 80);

        return inserts;
    }

    private Map<String, UniqueCodeExtend> getUniqueCodeExtendMap4CancelReject(Staff staff, List<WaveUniqueCode> filters) {
        Map<String, UniqueCodeExtend> uniqueCodeExtendMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(filters)) {
            return uniqueCodeExtendMap;
        }
        List<String> codes = filters.stream().filter(f -> StringUtils.isNotEmpty(f.getUniqueCode())).map(WaveUniqueCode::getUniqueCode).distinct().collect(toList());
        for (List<String> subCodes : Lists.partition(codes, 2000)) {
            UniqueCodeExtendParams params = new UniqueCodeExtendParams().setUniqueCodes(subCodes);
            uniqueCodeExtendMap.putAll(uniqueCodeExtendDao.queryList(staff, params).stream().collect(toMap(UniqueCodeExtend::getUniqueCode, a -> a, (a, b) -> a)));
        }
        return uniqueCodeExtendMap;
    }

    @Override
    @Transactional
    public List<WaveUniqueCode> doBatchReturnExchange(Staff staff, ProgressData progressData, List<WaveUniqueCode> filters) {
        Assert.notEmpty(filters, "无可退档换货的唯一码！");
        Map<String, Long> uniqueCodeShortSidMap = filters.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getShortSid));
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData, 20);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("唯一码退档换货，唯一码：【%s】", JSONObject.toJSONString(filters.stream().map(w -> "uniqueCode:" + w.getUniqueCode() + ",type:" + w.getType() + ",status:" + w.getStatus()).collect(Collectors.toList())))));
        }

        List<WaveUniqueCode> inserts = new ArrayList<>();
        for (WaveUniqueCode filter : filters) {
            // 拷贝生成新的唯一码
            copyUniqueCodes(inserts, filter);
            // 唯一码解绑
            OrderUniqueCodeUtils.buildRelease(filter);
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData, 30);
        // 自定义首字母
        Integer customFirstDigitNum = OrderUniqueCodeUtils.getCustomFirstDigitNum(orderUniqueCodeConfigService.get(staff));
        for (List<WaveUniqueCode> subCodes : Lists.partition(inserts, 1000)) {
            String[] uniqueCodes = xSerialNumberService.nextIds(staff.getCompanyId(), BUSINESS_NAME, subCodes.size());
            for (int i = 0, size = subCodes.size(); i < size; i++) {
                subCodes.get(i).setUniqueCode(OrderUniqueCodeUtils.fillUniqueCodeZero(uniqueCodes[i], null, customFirstDigitNum));
            }
        }
        Set<Long> supplierIds = uniqueCodeForceAllocateGoodsSupplierService.getSupplierIds(staff);
        reSetStockStatus(staff, inserts, supplierIds);
        // 供应商固定家里拿货
        orderUniqueCodeGenerateBusiness.supplierForeAllocateGoods(staff, inserts, supplierIds);
        // 商品没有供应商，取订单的供应商
        uniqueCodeBaseExtService.fillOrderSupplier4Generate(staff, inserts);

        waveUniqueCodeDao.batchUpdate(staff, filters);
        waveUniqueCodeDao.batchInsert(staff, inserts);

        fireBatchReturnExchangeEvent(staff, inserts, filters, uniqueCodeShortSidMap);
        // 外采唯一码自动生成唯一码采购单
        if (!CollectionUtils.isEmpty(inserts)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.auto.create.purchase").setArgs(new Object[]{staff, inserts.stream().filter(code -> Objects.equals(code.getStockStatus(), 0)).map(WaveUniqueCode::getUniqueCode).collect(toList())}), null);
        }
        if (progressData != null) {
            progressData.setSucNum(Long.valueOf(filters.size()));
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData, 80);

        return inserts;
    }

    private void copyUniqueCodes(List<WaveUniqueCode> inserts, WaveUniqueCode filter) {
        WaveUniqueCode insert = new WaveUniqueCode();
        BeanUtils.copyProperties(filter, insert);
        insert.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(filter, OrderUniqueCodeTagEnum.REJECT.getId(), CommonConstants.VALUE_YES));
        // 等待收货 -> 等待采购；已拣选 -> 等待拣选
        if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), filter.getStatus())) {
            insert.setStatus(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
        } else if (Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), filter.getStatus())) {
            insert.setStatus(OrderUniqueCodeStatusEnum.WAIT_PICK.getType());
        } else {
            insert.setStatus(filter.getStatus());
        }
        insert.setReceiveStatus(CommonConstants.ENABLE_STATUS_DELETED);
        insert.setPrintNum(0);
        insert.setPrintTime(null);
        insert.setCustomPrintTime(null);
        insert.setCreated(new Date());
        insert.setBusinessId(0L);
        insert.setBusinessCode("");
        inserts.add(insert);
    }

    private void copyUniqueCodeExtends(List<UniqueCodeExtend> inserts, UniqueCodeExtend filter) {
        if (filter == null) {
            return;
        }
        UniqueCodeExtend insert = new UniqueCodeExtend();
        BeanUtils.copyProperties(filter, insert);
        insert.setCreated(new Date());
        insert.setModified(null);
        inserts.add(insert);
    }

    private void buildTradeTrace(Staff staff, List<WaveUniqueCode> filterList, List<WaveUniqueCode> insertList,Map<String, Long> uniqueCodeSidMap){
        if (uniqueCodeSidMap == null || uniqueCodeSidMap.isEmpty()) {
            return;
        }
        filterList.forEach(f -> f.setSid(uniqueCodeSidMap.get(f.getUniqueCode())));
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> filterWaveUniqueCode = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(filterList);
        List<TradeTrace> tradeTraces = new ArrayList<>();
        for (WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO waveUniqueCode:filterWaveUniqueCode) {
            tradeTraces.add(TradeTraceUtils.createTradeTrace(staff.getCompanyId(), waveUniqueCode.getOldSid(), "唯一码取消", staff.getName(), new Date(), String.format("唯一码 %s 解绑(复制新增)", waveUniqueCode.getUniqueCode())));
        }
        for(WaveUniqueCode waveUniqueCode:insertList){
            tradeTraces.add(TradeTraceUtils.createTradeTrace(staff.getCompanyId(), waveUniqueCode.getSid(), "唯一码新增", staff.getName(), new Date(), String.format("唯一码新增：%s(%s)",waveUniqueCode.getOuterId(),waveUniqueCode.getUniqueCode())));
        }
        waveWriteTradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    /**
     * 新增的订单唯一码，设置家里/外采
     *  @param staff
     * @param inserts
     * @param supplierIds 固定生成家里供应商
     */
    private void reSetStockStatus(Staff staff, List<WaveUniqueCode> inserts, Set<Long> supplierIds) {
        for (List<WaveUniqueCode> subCodes : Lists.partition(inserts, 1000)) {
            doReSetStockStatus(staff, subCodes, supplierIds);
        }
    }

    private void doReSetStockStatus(Staff staff, List<WaveUniqueCode> inserts, Set<Long> supplierIds) {
        if (CollectionUtils.isEmpty(inserts) || !WmsUtils.isNewWms(staff)) {
            return;
        }
        List<AllocateGoodsRecord> records = new ArrayList<>();
        List<WmsChangeAffect> affects = WmsUtils.build4UniqueCodes(inserts, CommonConstants.JUDGE_NO);
        affects.forEach(a ->{
            a.setSectionCanAllocate(true);
        });
        List<WaveUniqueCode> afterDeleteList = inserts.stream().filter(c -> DataUtils.checkLongNotEmpty(c.getSupplierId()) && supplierIds.contains(c.getSupplierId())
                && StringUtils.contains(c.getTagIds(), OrderUniqueCodeTagEnum.OUT_STOCK.getId().toString())).collect(toList());
        List<WaveUniqueCode> firstDeleteList = inserts.stream().filter(data -> !afterDeleteList.contains(data)).collect(toList());
        if (!CollectionUtils.isEmpty(afterDeleteList) && logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "固定供应商缺货标签数量" + afterDeleteList.size()));
        }
        //  唯一码(非固定缺货标签)重新配货记录
        wmsService.reSetAllocateRecords(staff, firstDeleteList);

        // 对配货唯一码进行分组，仓库维度
        Map<Long, List<WmsChangeAffect>> warehouseGroup = affects.stream().collect(Collectors.groupingBy(WmsChangeAffect::getWarehouseId));
        Set<Map.Entry<Long, List<WmsChangeAffect>>> entries = warehouseGroup.entrySet();
        entries.forEach(e -> reAllocateGoods4UniqueCode(staff, e.getValue(), records));

        //  唯一码重新配货记录（固定缺货标签，因为重新配货时会减去缺货的配货记录，如果先删配货记录会导致库存多减一次）
        wmsService.reSetAllocateRecords(staff, afterDeleteList);

        Map<String, AllocateGoodsRecord> recordMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(records)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "订单唯一码重新配货，全部未配到！"));
            }
        } else {
            recordMap.putAll(records.stream().collect(Collectors.toMap(AllocateGoodsRecord::getWorkOrderId, t -> t, (v1, v2) -> v1)));
            Optional.ofNullable(records.stream().filter(r -> r.getAllocatedNum() != null && r.getAllocatedNum() < 0).collect(Collectors.toList())).orElse(Lists.newArrayList()).forEach(r -> logger.debug(LogHelper.buildLog(staff, String.format("初始化配货记录配货数量小于0, allocatedNum:【%s】, sid:【%s】, orderId:【%s】, workOrderId:【%s】, goodsSectionId:【%s】, goodsSectionCode:【%s】", r.getAllocatedNum(), r.getSid(), r.getOrderId(), r.getWorkOrderId(), r.getGoodsSectionId(), r.getGoodsSectionCode()))));
        }
        // 非固定供应商配货
        firstDeleteList.forEach(f -> {
            boolean haveStock = haveStock(recordMap.get(f.getUniqueCode()));
            // 重新配货没有配到库存的，原状态为【等待收货】的唯一码，不变更
            boolean notChange = !haveStock && Objects.equals(f.getStatus(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType());
            if (notChange) {
                f.setChangeStatus4ResetAllocateRecords(false);
            }
        });
        inserts.forEach(in -> {
            if (!in.getChangeStatus4ResetAllocateRecords()) {
                return;
            }
            boolean haveStock = haveStock(recordMap.get(in.getUniqueCode()));
            in.setStockStatus(BooleanUtils.toInteger(haveStock));
            in.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(in, OrderUniqueCodeTagEnum.IN_STOCK.getId(), BooleanUtils.toInteger(!haveStock)));
            in.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(in, OrderUniqueCodeTagEnum.OUT_STOCK.getId(), BooleanUtils.toInteger(haveStock)));
            if (haveStock) {
                // 等待拣选->等待拣选，打印次数不清空
                if (Objects.equals(in.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())) {
                    in.setOldStatus(in.getStatus());
                }
                in.setStatus(OrderUniqueCodeStatusEnum.WAIT_PICK.getType());
                // recordMap.get不会为null
                in.setGoodsSectionId(recordMap.get(in.getUniqueCode()).getGoodsSectionId());
                in.setGoodsSectionCode(recordMap.get(in.getUniqueCode()).getGoodsSectionCode());
                in.setReceiveStatus(CommonConstants.ENABLE_STATUS_DELETED);
            } else {
                in.setStatus(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
                in.setGoodsSectionId(0L);
                in.setGoodsSectionCode("");
                in.setReceiveStatus(CommonConstants.ENABLE_STATUS_DELETED);
            }
        });

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 合并配货记录保存
        Map<String, AllocateGoodsRecord> recordSaveMap = records.stream()
                .peek(t -> t.setWorkOrderId(t.getSid() != null ? t.getSid().toString() : null))
                .collect(Collectors.toMap(WmsKeyUtils::buildAllocateGoodsRecordWarehouseItemKey, t -> t, (t1, t2) -> {
                    t1.setAllocatedNum(t1.getAllocatedNum() + t2.getAllocatedNum());
                    return t1;
                }));
        wmsService.saveAllocateGoodsRecords(staff, new ArrayList<>(recordSaveMap.values()));
    }

    private void reAllocateGoods4UniqueCode(Staff staff, List<WmsChangeAffect> affects, List<AllocateGoodsRecord> records) {
        if (CollectionUtils.isEmpty(affects)) {
            return;
        }
        Map<String, List<WmsChangeAffect>> itemGroup = affects.stream().collect(Collectors.groupingBy(a -> WmsKeyUtils.buildItemKey(a.getSysItemId(), a.getSysSkuId())));
        Set<Map.Entry<String, List<WmsChangeAffect>>> entries = itemGroup.entrySet();

        // subtractForceSupplierLackNum 加工计划用到的东西，现在已经没有加工计划功能了，这个参数也没用了，后续可以考虑删了
        AllocateGoodsParams params = new AllocateGoodsParams.Builder().subtractForceSupplierLackNum(false).filterAllocateNum(true).filterOrderLockNum(false).build();
        for (List<Map.Entry<String, List<WmsChangeAffect>>> subEntries : Lists.partition(new ArrayList<>(entries), 3000)) {

            records.addAll(wmsService.preAllocateGoods(staff,
                    subEntries.stream().flatMap(en -> en.getValue().stream()).collect(toList()), params));
        }
    }

    private static boolean haveStock(AllocateGoodsRecord record) {
        return record != null && DataUtils.checkIntegerNotEmpty(record.getAllocatedNum());
    }

    /**
     * 取消复制新增发送唯一码日志
     * @param isNewSplit 是否拆分新标签
     */
    private void fireBatchCancelRejectEvent(Staff staff, List<WaveUniqueCode> inserts, List<WaveUniqueCode> updates, Map<String, Long> uniqueCodeShortSidMap, boolean isNewSplit,Map<String, Long> uniqueCodeSidMap) {
        // 唯一码重新生成
        if (!CollectionUtils.isEmpty(inserts)) {
            WaveUniqueOpType waveUniqueOpType = isNewSplit ? WaveUniqueOpType.ORDER_RE_GENERATE_SPLIT : WaveUniqueOpType.ORDER_RE_GENERATE;
            for (List<WaveUniqueCode> subCodes : Lists.partition(inserts, 1000)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                        WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(subCodes), waveUniqueOpType}), null);
            }
        }
        // 唯一码取消驳回
        if (!CollectionUtils.isEmpty(updates) && uniqueCodeShortSidMap != null && !uniqueCodeShortSidMap.isEmpty()) {
            updates.forEach(u -> u.setShortSid(uniqueCodeShortSidMap.get(u.getUniqueCode())));
            for (List<WaveUniqueCode> subCodes : Lists.partition(updates, 1000)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                        WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(subCodes), WaveUniqueOpType.ORDER_CANCEL_REJECT}), null);
            }
        }
        buildTradeTrace(staff,updates,inserts,uniqueCodeSidMap);
    }

    private void fireBatchReturnExchangeEvent(Staff staff, List<WaveUniqueCode> inserts, List<WaveUniqueCode> updates, Map<String, Long> uniqueCodeShortSidMap) {
        // 唯一码重新生成
        if (!CollectionUtils.isEmpty(inserts)) {
            updates.forEach(u -> u.setShortSid(uniqueCodeShortSidMap.get(u.getUniqueCode())));
            for (List<WaveUniqueCode> subCodes : Lists.partition(inserts, 1000)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                        WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(subCodes), WaveUniqueOpType.RETURN_EXCHANGE_RE_GENERATE}), null);
            }
        }
        // 唯一码退档换货
        if (!CollectionUtils.isEmpty(updates)) {
            updates.forEach(u -> u.setShortSid(uniqueCodeShortSidMap.get(u.getUniqueCode())));
            for (List<WaveUniqueCode> subCodes : Lists.partition(updates, 1000)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                        WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(subCodes), WaveUniqueOpType.ORDER_RETURN_EXCHANGE}), null);
            }
        }
    }

    @Override
    @Transactional
    public void batchReject(Staff staff, OrderUniqueCodeQueryParams params, ProgressData progressData) {
        //权限过滤
        fillCondition(staff, params);
        PurchaseConfig purchaseConfig = uniqueCodePurchaseServiceDubbo.autoCreateUniqueCodePurchaseOrder(staff);
        // 填充仓库权限
        params.setWarehouseIds(getWarehouseIds(staff,params));
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "未查询到唯一码！");
        //开启自动生成唯一码采购单，驳回打印时间不超过2天
        if (Objects.equals(purchaseConfig.getAutoCreateUniqueCodePurchaseOrder(), CommonConstants.VALUE_YES)){
            codes = codes.stream().filter(code -> !OrderUniqueCodeUtils.checkPrintTimeIsOverTime(code.getPrintTime(), OrderUniqueCodeHelpBusiness.PRINT_TIME_DAY)).collect(toList());
        }
        List<WaveUniqueCode> receivedCodes = codes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())).collect(toList());
        List<WaveUniqueCode> pickedCodes = codes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())).collect(toList());
        List<WaveUniqueCode> waitReceiveCodes = codes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())).collect(toList());
        if (CollectionUtils.isEmpty(receivedCodes)
                && CollectionUtils.isEmpty(pickedCodes)
                && CollectionUtils.isEmpty(waitReceiveCodes)) {
            throw new IllegalArgumentException("无可驳回的唯一码！");
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码驳回，已收货唯一码数量：" + receivedCodes.size()
                    + "，已拣选唯一码数量：" + pickedCodes.size()) + "，待收货唯一码数量：" + waitReceiveCodes.size());
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_REJECT_ORDER_UNIQUE_CODE, progressData, 20);

        // 除了已收货外的唯一码（已拣选+等待收货）
        List<WaveUniqueCode> exceptReceivedCodes = ArrayUtils.saveAddAll(pickedCodes, waitReceiveCodes);
        rejectOrderUniqueCode(staff, exceptReceivedCodes, params.getRejectFromMemorandum());
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_REJECT_ORDER_UNIQUE_CODE, progressData, 30);
        // 有远程调用，可能会失败，放在后面
        rejectReceivedCode(staff, receivedCodes, params);
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_REJECT_ORDER_UNIQUE_CODE, progressData, 50);


        if (!CollectionUtils.isEmpty(receivedCodes)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                    WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(receivedCodes), getOpType(params.getRejectFromMemorandum())}), null);
        }
        if (!CollectionUtils.isEmpty(exceptReceivedCodes)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                    WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(exceptReceivedCodes), getOpType(params.getRejectFromMemorandum())}), null);
        }
    }

    private static WaveUniqueOpType getOpType(Integer rejectFromMemorandum) {
        return (rejectFromMemorandum == null || rejectFromMemorandum == 0)
                ? WaveUniqueOpType.ORDER_REJECT
                : WaveUniqueOpType.MEMORANDUM_REJECT;
    }

    private void rejectReceivedCode(Staff staff, List<WaveUniqueCode> codes, OrderUniqueCodeQueryParams params) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        Integer rejectFromMemorandum = params.getRejectFromMemorandum();
        List<WaveUniqueCode> updates = new ArrayList<>();
        List<WaveUniqueCode> adjustDetails = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            update.setStatus(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType());
            update.setReceiveStatus(CommonConstants.JUDGE_NO);
            update.setReceiveTime(UniqueCodeUtils.getDefaultDate());
            update.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            if (rejectFromMemorandum == null || rejectFromMemorandum == 0) {
                update.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(code, OrderUniqueCodeTagEnum.REJECT.getId(), CommonConstants.VALUE_NO));
            }
            if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType())) {
                update.setBusinessId(0L);
                update.setBusinessCode("");
            }
            updates.add(update);

            WaveUniqueCode adjustDetail = new WaveUniqueCode();
            adjustDetail.setSysItemId(code.getSysItemId());
            adjustDetail.setSysSkuId(code.getSysSkuId());
            adjustDetail.setUniqueCode(code.getUniqueCode());
            adjustDetail.setOuterId(code.getOuterId());
            adjustDetails.add(adjustDetail);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);

        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType())) {
            uniqueCodeBaseService.releaseBindRelation(staff, params.getReleaseRelationIds());
        }
    }

    private void rejectOrderUniqueCode(Staff staff, List<WaveUniqueCode> codes, Integer rejectFromMemorandum) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            Integer status = OrderUniqueCodeUtils.REJECT_STATUS_MAP.get(code.getStatus());
            Assert.notNull(status, code.getUniqueCode() + "不支持驳回");
            update.setStatus(status);
            update.setStockPosition(Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())
                    ? UniqueCodeStockPositionEnum.SHELVES.getType() : UniqueCodeStockPositionEnum.DEFAULT.getType());
            // 【等待收货】驳回为等待采购，【打印时间】清空、【打印次数】清空
            if (Objects.equals(status, OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType())) {
                update.setPrintNum(0);
                update.setPrintTime(TradeTimeUtils.INIT_DATE);
            }
            if (rejectFromMemorandum == null || rejectFromMemorandum == 0) {
                update.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(code, OrderUniqueCodeTagEnum.REJECT.getId(), CommonConstants.VALUE_NO));
            }
            updates.add(update);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
    }

    @Override
    @Transactional
    public WaveUniqueCode offShelfUniqueCode(Staff staff, String uniqueCode, Integer requestSource) {
        Assert.hasText(uniqueCode, "唯一码不能为空！");
        WaveUniqueCode code = getByCode(staff, uniqueCode);
        batchOffShelfOrCancel(staff, Lists.newArrayList(code.getId()), CommonConstants.JUDGE_NO, requestSource);
        return code;
    }

    private static String getLogContent(WaveUniqueOpType type, Integer requestSource) {
        switch (type) {
            case ORDER_OFF_SHELF:
                return "扫描下架" + (Objects.equals(requestSource, 1) ? "(APP端)" : "");
            case ORDER_RECEIVE:
                return "扫描收货";
            case ORDER_CANCEL:
                return "取消";
            case TRANSLATION:
                return "暂存区平移，库存位置变更为销退暂存区";
            default:
                return "";
        }
    }

    private void orderUniqueReceiveCheck(Staff staff, WaveUniqueCode code, boolean allowCancel) {
        // 一次收货，1: 驳回状态的不能收 2: 已经收货的不能再收
        singleOrderUniqueCodeReceiveCheck(staff, code, allowCancel);
        if (code.getReceiveStatus() == CommonConstants.JUDGE_YES) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, String.format("唯一码[%s]已重复扫描！", code.getUniqueCode()));
        }
        if (!Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())
                && (!allowCancel && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus()))
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, String.format("该唯一码[%s]状态不允许收货！", code.getUniqueCode()));
        }
    }

    private static void receiveCheck(WaveUniqueCode code, ReceiveUniqueCodeParams params) {
        if (Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus())
                && Objects.equals(code.getStockStatus(), CommonConstants.JUDGE_YES)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF,
                    String.format("唯一码[%s]已下架！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }

        // 已采退不校验重复收货
        if (!Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus()) &&
                code.getReceiveStatus() == CommonConstants.JUDGE_YES && !params.getRescan()) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED,
                    String.format("唯一码[%s]已重复扫描！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }
        if ((!Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus()))
                && !(Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()))
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED,
                    String.format("该唯一码[%s]状态不允许收货！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }
        if (!params.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_CANCEL,
                    String.format("唯一码[%s]已取消，不允许收货！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }
    }

    @Override
    public WaveUniqueCode receiveCheck(Staff staff, ReceiveUniqueCodeParams params) throws WaveScanException {
        String uniqueCode = params.getUniqueCodes();
        Assert.notNull(uniqueCode, "唯一码不能为空！");
        WaveUniqueCode code = getByCode(staff, uniqueCode);
        Assert.notNull(code, "唯一码不存在！");
        try {
            if (OrderUniqueCodeUtils.isItemUniqueCode(code)) {
                checkUniqueCodeItemRefund(staff, code);
                singleItemUniqueCodeReceiveCheck(code);
            } else {
                orderUniqueReceiveCheck(staff, code, params.isAllowCancel());
            }
        } catch (WaveScanException e) {
            // 唯一码收货校验抛出WaveScanException时优先判断是否存在商品退款异常
            WaveScanException exception = e;
            if (Objects.equals(WaveScanException.ERROR_CODE_ITEM_REFUND, code.getErrorCode())) {
                exception = new WaveScanException(WaveScanException.ERROR_CODE_ITEM_REFUND, String.format("唯一码[%s] 商品退款中！", code.getUniqueCode()));
            }
            waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, Lists.newArrayList(code), exception, WaveUniqueOpType.ORDER_RECEIVE));
            boolean skipException = Objects.equals(orderUniqueCodeConfigService.get(staff).getPositionNoWay(), 2) && (Objects.equals(WaveScanException.ERROR_CODE_UNIQUE_CODE_ALL, e.getErrCode()) || Objects.equals(WaveScanException.ERROR_CODE_UNIQUE_CODE_ARRIVAL, e.getErrCode()));
            if (!skipException) {
                throw exception;
            }
        }
        if (Objects.equals(WaveScanException.ERROR_CODE_ITEM_REFUND, code.getErrorCode())) {
            code.setErrorCode(null); // 商品退款中正常收货时清空唯一码的errorCode
        }
        if (params.getRescan()) {
            fillExpressInfo(staff, code);
        }
        if (params.isShowPropertiesName()) {
            orderUniqueCodeExtendService.fillPlatformInfo4Receive(staff, code, null, true);
        }
        return code;
    }

    @Override
    @Transactional
    public List<WaveScanException> batchReceive(Staff staff, ReceiveUniqueCodeParams uniqueCodeParams) {
        Assert.notEmpty(uniqueCodeParams.getUniqueCodeArr(), "唯一码不能为空！");
        return doBatchReceive(staff, uniqueCodeParams).getExceptions();
    }

    @Override
    @Transactional
    public ReceiveUniqueCodeVO batchReceiveV2(Staff staff, ReceiveUniqueCodeParams uniqueCodeParams) {
        Assert.notEmpty(uniqueCodeParams.getUniqueCodeArr(), "唯一码不能为空！");
        return doBatchReceive(staff, uniqueCodeParams);
    }

    private ReceiveUniqueCodeVO doBatchReceive(Staff staff, ReceiveUniqueCodeParams uniqueCodeParams) {
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodeParams.getUniqueCodeArr());
        List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        Assert.notEmpty(uniqueCodes, "未查询到唯一码！");

        List<WaveScanException> exceptions = new ArrayList<>();
        List<WaveUniqueCodeLog> errorLogs = new ArrayList<>();
        List<WaveUniqueCode> updates = new ArrayList<>();
        List<WaveUniqueCode> successCodes = new ArrayList<>();
        ReceiveUniqueCodeVO vo = new ReceiveUniqueCodeVO().setExceptions(exceptions).setSuccessCodes(successCodes);
        // 分配分拣货位
        orderUniqueCodeHelpBusiness.allocatePositionNo4Receive(staff, uniqueCodes, exceptions);
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        for (WaveUniqueCode code : uniqueCodes) {
            try {
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(code.getId());
                if (OrderUniqueCodeUtils.isItemUniqueCode(code)) {
                    if (!uniqueCodeParams.isSkipReceiveOverTime()) {
                        // 唯一码收货，超时判断
                        code = checkReceiveOverTime(staff, code);
                    }
                    checkPurchaseReturnReceive(staff, code, wmsConfig);
                    itemUniqueCodeExtendService.fill4BatchReceive(uniqueCodeParams, code, update);
                } else {
                    if (Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())) {
                        continue;
                    }
                    receiveCheck(code, uniqueCodeParams);
                    if (!uniqueCodeParams.isSkipReceiveOverTime()) {
                        // 唯一码收货，超时判断
                        code = checkReceiveOverTime(staff, code);
                    }
                    checkPurchaseReturnReceive(staff, code, wmsConfig);
                    revertOffTime(code, update);
                    if (!OrderUniqueCodeUtils.cannotReceive(code, uniqueCodeParams.isAllowCancel())) {
                        // 已采退的老数据收货更新库存位置
                        if (Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus()) ||
                                Objects.equals(code.getReceiveStatus(), CommonConstants.VALUE_NO)) {
                            update.setStockPosition(getStockPosition4BatchReceive(uniqueCodeParams, code));
                        }
                        if (Objects.equals(uniqueCodeParams.getReceiveType(), 2) ||
                                Objects.equals(uniqueCodeParams.getReceiveType(), 3) ||
                                Objects.equals(uniqueCodeParams.getReceiveType(), 5) ||
                                Objects.equals(uniqueCodeParams.getReceiveType(), 4) ||
                                Objects.equals(uniqueCodeParams.getReceiveType(), 6)) {
                            // 包装验货入入库暂存区
                            update.setStockPosition(UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType());
                        }
                        if (Objects.equals(uniqueCodes.size(), 1)) {
                            if (logger.isDebugEnabled()) {
                                logger.debug(LogHelper.buildLog(staff, "唯一码收货，修改receiveStatus"));
                            }
                        }
                        update.setReceiveStatus(CommonConstants.JUDGE_YES);
                        update.setReceiveTime(new Date());
                    }
                    update.setStatus(getStatus4BatchReceive(uniqueCodeParams, code));
                }
                update.setUniqueCode(code.getUniqueCode());
                update.setType(code.getType());
                update.setOuterId(code.getOuterId());
                update.setSysItemId(code.getSysItemId());
                update.setSysSkuId(code.getSysSkuId());
                update.setNum(1);
                update.setWarehouseId(code.getWarehouseId());
                update.setProcessStatus(code.getProcessStatus());
                if (uniqueCodeParams.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
                    OrderUniqueCodeUtils.buildRelease(update);
                }
                updates.add(update);
                successCodes.add(code);
            } catch (WaveScanException e) {
                exceptions.add(e);
                errorLogs.addAll(buildLogs(staff, Lists.newArrayList(code), e, WaveUniqueOpType.ORDER_RECEIVE));
            }
        }
        waveUniqueCodeLogDao.batchInsert(staff, errorLogs);
        if (CollectionUtils.isEmpty(updates)) {
            return vo;
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);

        List<String> updateCodes = updates.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        Map<String, Integer> updateStatusMap = updates.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getStatus, (a, b) -> a));
        List<WaveUniqueCode> itemUniqueCodes = uniqueCodes.stream().filter(u -> updateCodes.contains(u.getUniqueCode()) && OrderUniqueCodeUtils.isItemUniqueCode(u)).collect(toList());

        /**
         * 一次收货，非家里唯一码需要加入收货单
         *         家里的唯一码根据配置判断是否需要下架暂存区
         */
        List<WaveUniqueCode> needReceives = uniqueCodes.stream().filter(u -> updateCodes.contains(u.getUniqueCode())
                && !OrderUniqueCodeUtils.isItemUniqueCode(u)
                && ((!u.getStatus().equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType()) && !u.getStatus().equals(OrderUniqueCodeStatusEnum.CANCEL.getType())) || (uniqueCodeParams.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), u.getStatus()) && Objects.equals(u.getStockStatus(), 0)))).collect(toList());
        if (!CollectionUtils.isEmpty(needReceives)) {
            // 收货单导入不收货, 采购自己收货
            if (uniqueCodeParams.isGenerateReceive()) {
                releaseWarehouseEntry4PurchaseReturnCode(staff, needReceives);
                eventCenter.fireEvent(this, new EventInfo("wave.unique.code.receive").setArgs(new Object[]{staff, needReceives, uniqueCodeParams.getSource(), uniqueCodeParams.getOneByOneReceive()}), null);
            }
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, getWaveUniqueCodeLogDTOS(updateStatusMap, needReceives), uniqueCodeParams.isExcepReceive() ? WaveUniqueOpType.EXCEPT_ORDER_RECEIVE : WaveUniqueOpType.ORDER_RECEIVE, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(uniqueCodeParams.getLogTemplate())}), null);
        }

        List<WaveUniqueCode> needPicks = uniqueCodes.stream().filter(u -> updateCodes.contains(u.getUniqueCode())
                && !OrderUniqueCodeUtils.isItemUniqueCode(u)
                && (u.getStatus().equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType()) || (uniqueCodeParams.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), u.getStatus()) && Objects.equals(u.getStockStatus(), 1)))).collect(toList());
        if (!CollectionUtils.isEmpty(needPicks) && !OrderUniqueCodeUtils.isTrue(orderUniqueCodeConfigService.get(staff).getPrintChangeStatus())) {
            uniqueCodePrintPicked(staff, needPicks, true);
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                    WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(needPicks), uniqueCodeParams.isExcepReceive() ? WaveUniqueOpType.EXCEPT_ORDER_RECEIVE : WaveUniqueOpType.ORDER_RECEIVE}), null);
        }

        if (!CollectionUtils.isEmpty(itemUniqueCodes)) {
            // 收货单导入不收货, 采购自己收货
            if (uniqueCodeParams.isGenerateReceive()) {
                releaseWarehouseEntry4PurchaseReturnCode(staff, itemUniqueCodes);
                eventCenter.fireEvent(this, new EventInfo("wave.unique.code.receive").setArgs(new Object[]{staff, itemUniqueCodes, uniqueCodeParams.getSource(), uniqueCodeParams.getOneByOneReceive()}), null);
            }
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                    WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(uniqueCodes.stream().filter(u -> updateCodes.contains(u.getUniqueCode()) && OrderUniqueCodeUtils.isItemUniqueCode(u))
                            .collect(Collectors.toList())), WaveUniqueOpType.ORDER_RECEIVE, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(uniqueCodeParams.getLogTemplate())}), null);
        }
        sendOrderUniqueCodeReceiveEvent(staff, updates);

        return vo;
    }

    private List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> getWaveUniqueCodeLogDTOS(Map<String, Integer> updateStatusMap, List<WaveUniqueCode> needReceives) {
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> dtos = WaveUniqueCodeLogUtils.buildLogByOperateSource(needReceives, "PDA".equalsIgnoreCase(OperateSourceContext.acquireOperateSourceNew().getProject()) ? UniqueCodeOperateSourceEnum.PDA_RECEIVE : UniqueCodeOperateSourceEnum.PC_RECEIVE);
        if (CollectionUtils.isEmpty(dtos)) {
            return dtos;
        }
        /**
         * 快速验货模式收货有事务问题
         * 收货方法没加事务，所以日志事件收到之后，查询唯一码可能查到的是未提交的
         */
        for (WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto : dtos) {
            Integer updateStatus = updateStatusMap.get(dto.getUniqueCode());
            if (updateStatus != null) {
                dto.setCurrentStatus(updateStatus);
            }
        }
        return dtos;
    }

    private Integer getStockPosition4BatchReceive(ReceiveUniqueCodeParams uniqueCodeParams, WaveUniqueCode code) {
        if (uniqueCodeParams.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
            return uniqueCodeBaseService.getStockPosition4AllowCancel(code);
        }
        return Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())
                ? UniqueCodeStockPositionEnum.PICK_AREA.getType() : UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType();
    }

    private Integer getStatus4BatchReceive(ReceiveUniqueCodeParams uniqueCodeParams, WaveUniqueCode code) {
        if (uniqueCodeParams.isAllowCancel() && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
            return uniqueCodeBaseService.getStatus4AllowCancel(code);
        }
        return Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())
                ? OrderUniqueCodeStatusEnum.PICKED.getType() : OrderUniqueCodeStatusEnum.RECIVED.getType();
    }

    /**
     * 加工状态为等待加工的、已打印选的订单唯一码,需要发送收货事件到仓储
     * @param staff
     * @param codes
     */
    public void sendOrderUniqueCodeReceiveEvent(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码参数为空,未执行order.unique.code.pick事件"));
            }
            return;
        }

        List<WaveUniqueCode> codeList = codes.stream().filter(code ->
                Objects.equals(code.getType(), CommonConstants.VALUE_YES) &&
                        Objects.equals(code.getProcessStatus(), CommonConstants.VALUE_YES) &&
                        code.getPrintNum() != null && code.getPrintNum() > CommonConstants.VALUE_NO).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeList)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码过滤之后为空,不执行order.unique.code.pick事件"));
            }
            return;
        }

        eventCenter.fireEvent(this, new EventInfo(EventNameConstants.ORDER_UNIQUE_CODE_PICK).setArgs(new Object[]{staff, codeList}), null);
    }

    /**
     * 清除下架时间
     */
    private void revertOffTime(WaveUniqueCode code, WaveUniqueCode update) {
        if (!Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus())) {
            return;
        }
        update.setOffTime(TradeTimeUtils.INIT_DATE);
    }

    private List<WaveUniqueCodeLog> buildLogs(Staff staff, List<WaveUniqueCode> uniqueCodes, Throwable throwable, WaveUniqueOpType type) {
        return buildLogs(staff,uniqueCodes,throwable,type,null);
    }
    private static List<WaveUniqueCodeLog> buildLogs(Staff staff, List<WaveUniqueCode> uniqueCodes, Throwable throwable, WaveUniqueOpType type,String content, Integer requestSource) {
        List<WaveUniqueCodeLog> logs = Lists.newArrayListWithCapacity(uniqueCodes.size());
        String errorMsg = throwable == null ? "" : throwable.getMessage();
        int isError = throwable != null ? 1 : 0;
        for (WaveUniqueCode uniqueCode : uniqueCodes) {
            WaveUniqueCodeLog log = new WaveUniqueCodeLog();
            log.setWaveId(uniqueCode.getWaveId());
            log.setUniqueCode(uniqueCode.getUniqueCode());
            log.setIsError(isError);
            log.setErrorMsg(errorMsg);
            log.setOpType(type);
            if(content!=null) {
                log.setContent(content);
            }else{
                log.setContent("唯一码：" + uniqueCode.getUniqueCode() + getLogContent(type, requestSource));
            }
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setStatus(uniqueCode.getStatus());
            logs.add(log);
        }
        return logs;
    }

    public static List<WaveUniqueCodeLog> buildLogs(Staff staff, List<WaveUniqueCode> uniqueCodes, Throwable throwable, WaveUniqueOpType type, String content) {
        return buildLogs(staff,uniqueCodes,throwable,type,content, null);
    }

    @Override
    public WaveUniqueCode getByCode(Staff staff, String uniqueCode) {
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, ItemUniqueCodeQueryParams.build(uniqueCode));
        fillWarehouseName(staff, codes);
        if (CollectionUtils.isEmpty(codes)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_EXIST, "唯一码不存在！");
        }
        WaveUniqueCode code = codes.get(0);
        return code;
    }

    /**
     * 已采退的外采唯一码会无单收货生成新的收货单,需要解绑原来的采购单
     * @param staff
     * @param codes
     */
    private void releaseWarehouseEntry4PurchaseReturnCode(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<String> uniqueCodes = codes.stream().filter(code -> Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType())).map(WaveUniqueCode::getUniqueCode).collect(toList());
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        params.setBusinessType(UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType());
        List<UniqueCodeRelation> relations = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        if (!CollectionUtils.isEmpty(relations)) {
            uniqueCodeBaseService.releaseBindRelation(staff, relations.stream().map(UniqueCodeRelation::getId).collect(toList()));
        }
        // 已采退唯一码 businessId置空走无单收货
        codes.forEach(code -> {
            if (uniqueCodes.contains(code.getUniqueCode())) {
                code.setBusinessId(null);
                code.setBusinessCode("");
                code.setOldStatus(code.getStatus());
            }
        });
    }

    @Override
    public String getSidFreeScanOuterIds(Staff staff, Long sid) {
        if (!DataUtils.checkLongNotEmpty(sid)) {
            return null;
        }
        List<WaveSortingDetail> details = waveSortingDao.queryDetailBySids(staff, Lists.newArrayList(sid));
        if (CollectionUtils.isEmpty(details)) {
            return null;
        }

        List<WaveSortingDetail> freeScanDetails = details.stream().filter(detail -> Objects.equals(detail.getGiftPickAndCheck(), 0)).collect(toList());
        if (CollectionUtils.isEmpty(freeScanDetails)) {
            return null;
        }

        List<String> itemList = new ArrayList<>();
        for (WaveSortingDetail detail : freeScanDetails) {
            itemList.add(detail.getOuterId() + "*" + detail.getItemNum());
        }
        return Strings.join(",", itemList);
    }

    @Override
    public List<WaveUniqueCode> getByUniqueOrGenericCode(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        List<WaveUniqueCode> resultCodes = Lists.newArrayList();
        List<ErrorItem> errorItems = Lists.newArrayList();
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, ItemUniqueCodeQueryParams.build(params.getUniqueCodes(), params.getShipperId()));
        Map<String, Integer> returnStatusMap = queryPurchaseReturnStatus(staff, codes);
        resultCodes.addAll(Optional.ofNullable(codes).orElse(Lists.newArrayList()).stream().filter(code -> filterWaveUniqueCodeStatus(code, errorItems, params.isSingleScan(), returnStatusMap, staff,BooleanUtils.isTrue(params.getAutoOut()) ? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue())).collect(Collectors.toList()));
        List<String> uniqueCodes = Optional.ofNullable(codes).orElse(Lists.newArrayList()).stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        params.getUniqueCodes().removeAll(uniqueCodes);
        if (!CollectionUtils.isEmpty(params.getUniqueCodes())) {
            resultCodes.addAll(getScanGenericCodes(staff, params));
        }
        return resultCodes;
    }

    @Override
    public PdaFastScanReturnBO getByUniqueOrGenericCode(Staff staff, List<String> uniqueCodes) {
        PdaFastScanReturnBO returnBO = new PdaFastScanReturnBO();
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, ItemUniqueCodeQueryParams.build(uniqueCodes));
        Map<String, Integer> returnStatusMap = queryPurchaseReturnStatus(staff, codes);
        returnBO.getWaveUniqueCodes().addAll(Optional.ofNullable(codes).orElse(Lists.newArrayList()).stream().filter(code -> filterWaveUniqueCodeStatus(staff,code,returnBO.getErrorItems(),returnStatusMap)).collect(Collectors.toList()));
        return returnBO;
    }

    private void singleOrderUniqueCodeReceiveCheck(Staff staff, WaveUniqueCode code, boolean allowCancel) {

        // 校验唯一码对应商品是否退款
        checkUniqueCodeItemRefund(staff, code);

        if (Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus())
                && Objects.equals(code.getStockStatus(), CommonConstants.JUDGE_YES)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("唯一码[%s]已下架！", code.getUniqueCode()));
        }
        if (Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OUT, String.format("唯一码[%s]已出库！", code.getUniqueCode()));
        }

        if (Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())) {
            checkTradeReceived(staff, code);
        }

        if (!allowCancel && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_CANCEL, String.format("唯一码[%s]已取消！", code.getUniqueCode()));
        }

        if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())) {
            checkUnboundUniqueCodeStatua(staff, code);
        }

        if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())) {
            checkWaitPurchase(staff, code);
        }

        if (Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())) {
            checkPickAndOffShelfUniqueCode(staff, code);
        }

        if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())) {
            checkUniqueCodeWaitReceiving(staff, code);
        }

        orderUniqueCodeHelpBusiness.checkPrintTime(staff, code, false);
    }

    /**
     * 校验唯一码对应商品退款状态(正常收货）
     * @param staff
     * @param code
     */
    private void checkUniqueCodeItemRefund(Staff staff, WaveUniqueCode code) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (config == null || config.getExtConfigMap() == null) {
            return;
        }
        Integer playUniqueCodeItemRefund = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.PLAY_UNIQUE_CODE_ITEM_REFUND.getKey(), 0);
        // 是否开启唯一码收货播报退款中
        if (!Objects.equals(1, playUniqueCodeItemRefund)) {
            return;
        }
        // 校验唯一码关联子订单对应商品退款状态
        if (DataUtils.checkLongNotEmpty(code.getOrderId())) {
            List<TbOrder> orders = tbOrderDAO.queryByIds(staff, code.getOrderId());
            if (CollectionUtils.isEmpty(orders)) {
                return;
            }
            boolean refunding = OrderUniqueCodeUtils.isRefunding(orders.get(0));
            if (refunding) { // 根据refundind判断是否退款，在不能收货时根据errorCode判断优先抛出商品退款异常
                code.setErrorCode(WaveScanException.ERROR_CODE_ITEM_REFUND);
                code.setRefunding(refunding);
            }
        }
    }

    /**
     * 判断已拣选和已下架状态的唯一码(解绑的订单状态)
     */
    public void checkPickAndOffShelfUniqueCode(Staff staff, WaveUniqueCode code) {
        //判断是否已经解绑
        if (DataUtils.checkLongNotEmpty(code.getSid())) {
            //判断是否到齐
            boolean allReceiving = judgeOrderReceivingStatus(staff, code);
            if (allReceiving) {
                code.setErrorCode(WaveScanException.ERROR_CODE_UNIQUE_CODE_ALL);
            } else {
                code.setErrorCode(WaveScanException.ERROR_CODE_UNIQUE_CODE_ARRIVAL);
            }
        } else {
            checkUnboundUniqueCodeStatua(staff, code);
        }
    }

    /**
     * 判断已解绑唯一码的订单状态(正常收货)
     *
     * @param code
     */
    public void checkUnboundUniqueCodeStatua(Staff staff, WaveUniqueCode code) {
        if (!DataUtils.checkLongNotEmpty(code.getSid())) {
            //已解绑
            OrderUniqueCodeUnboundLog log = orderUniqueCodeUnboundLogDao.queryByLastOne(staff, code.getUniqueCode());
            if (log != null) {
                switch (log.getReasonType()) {
                    case 1:
                        //订单作废
                        code.setErrorCode(WaveScanException.ERROR_CODE_TRADE_CANCEL);
                        break;
                    case 2:
                        //反审核
                        code.setErrorCode(WaveScanException.ERROR_CODE_TRADE_WAIT_AUDIT);
                        break;
                    case 3:
                        //交易关闭
                        code.setErrorCode(WaveScanException.ERROR_CODE_TRADE_CLOSED);
                        break;
                    case 4:
                        //已发货
                        code.setErrorCode(WaveScanException.ERROR_CODE_TRADE_SEND_GOODS);
                        break;
                    case 5:
                        //交易完成
                        code.setErrorCode(WaveScanException.ERROR_CODE_TRADE_FINISHED);
                        break;
                    case 9:
                        //其他ERP发货
                        code.setErrorCode(WaveScanException.ERROR_CODE_UNIQUE_CODE_OTHER_ERP);
                        break;
                    default:
                        break;
                }
            } else {
                //已解绑
                code.setErrorCode(WaveScanException.ERROR_CODE_UNIQUE_CODE_UNBOUND);
            }
        }
    }

    /**
     * 判断等待收货/已出库未收货状态的唯一码(订单状态)
     *
     * @param code
     */
    public void checkUniqueCodeWaitReceiving(Staff staff, WaveUniqueCode code) {
        //校验采购单状态
        checkUniqueCodeWaitReceivingInner(staff, code);
        if (DataUtils.checkLongNotEmpty(code.getSid())) {
            boolean allReceiving = judgeOrderReceivingStatus(staff, code);
            if (allReceiving) {
                code.setAllReceive(CommonConstants.VALUE_YES);
            } else {
                code.setAllReceive(CommonConstants.VALUE_NO);
            }
        } else {
            checkUnboundUniqueCodeStatua(staff, code);
        }
    }

    private void checkUniqueCodeWaitReceivingInner(Staff staff, WaveUniqueCode code) {
        //校验采购单状态
        if (DataUtils.checkLongNotEmpty(code.getBusinessId())) {
            HashMap<String, Long> purchaseUniqueCodeMap = new HashMap<>();
            purchaseUniqueCodeMap.put(code.getUniqueCode(), code.getBusinessId());
            try {
                uniqueCodePurchaseServiceDubbo.purchaseOrderUniqueCodeReceiveCheck(staff, purchaseUniqueCodeMap);
            } catch (WaveScanException e) {
                throw new WaveScanException(e.getErrCode(), e.getMessage());
            }
        }
    }

    /**
     * 判断等待采购唯一码关联订单的状态
     */
    public void checkWaitPurchase(Staff staff, WaveUniqueCode code) {
        if (DataUtils.checkLongNotEmpty(code.getSid())) {
            //校验采购单状态
            if (DataUtils.checkLongNotEmpty(code.getBusinessId())) {
                HashMap<String, Long> purchaseUniqueCodeMap = new HashMap<>();
                purchaseUniqueCodeMap.put(code.getUniqueCode(), code.getBusinessId());
                try {
                    uniqueCodePurchaseServiceDubbo.purchaseOrderUniqueCodeReceiveCheck(staff, purchaseUniqueCodeMap);
                } catch (WaveScanException e) {
                    throw new WaveScanException(e.getErrCode(), e.getMessage());
                }
            }
        } else {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_UNBOUND, String.format("唯一码[%s]已解绑！", code.getUniqueCode()));
        }
    }

    /**
     * 判断唯一码已收货状态
     */
    public void checkTradeReceived(Staff staff, WaveUniqueCode code) {
        //判断唯一码是否解绑
        if (DataUtils.checkLongNotEmpty(code.getSid())) {
            boolean allReceiving = judgeOrderReceivingStatus(staff, code);
            if (allReceiving) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_ALL, String.format("唯一码[%s]订单全部收货！", code.getUniqueCode()));
            } else {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_ARRIVAL, String.format("唯一码[%s]订单部分收货！", code.getUniqueCode()));
            }
        } else {
            checkedUnboundReasonException(staff, code.getUniqueCode());
        }
    }

    /**
     * 判断唯一码关联订单是否全部收货
     */
    public boolean judgeOrderReceivingStatus(Staff staff, WaveUniqueCode code) {
        if (!DataUtils.checkLongNotEmpty(code.getSid())) {
            throw new IllegalArgumentException("判断全部收货失败(唯一码已解绑)");
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(Lists.newArrayList(code.getSid()));
        List<WaveUniqueCode> codes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params));
        //判断部分到货or到齐
        boolean flag = true;
        for (WaveUniqueCode waveUniqueCode : codes) {
            //不判断自身的收货状态
            if (StringUtils.equals(code.getUniqueCode(), waveUniqueCode.getUniqueCode())) {
                continue;
            }
            // 等待采购/等待拣选/已出库未收货/等待收货 --提示未到齐
            if (OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType().equals(waveUniqueCode.getStatus())
                    || OrderUniqueCodeStatusEnum.WAIT_PICK.getType().equals(waveUniqueCode.getStatus())
                    || OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType().equals(code.getStatus())
                    || OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType().equals(waveUniqueCode.getStatus())) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    private static void singleItemUniqueCodeReceiveCheck(WaveUniqueCode code) {
        if ((!Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus()))
                && !Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, String.format("%s唯一码，%s状态！", code.getUniqueCode(), OrderUniqueCodeStatusEnum.getNameByStatus(code.getStatus())));
        }
        if (Objects.equals(CommonConstants.JUDGE_YES, code.getReceiveStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON,
                    String.format("唯一码[%s]已重复扫描！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }
    }

    @Override
    public void checkUnbound(Staff staff,String uniqueCode){
        Assert.hasText(uniqueCode, "唯一码不能为空！");
        WaveUniqueCode code = getByCode(staff, uniqueCode);
        if(code==null){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_FOUND, String.format("唯一码[%s]不存在！", code.getUniqueCode()));
        }else if(!DataUtils.checkLongNotEmpty(code.getSid())){
            checkedUnboundReasonException(staff, code.getUniqueCode());
        }
    }

    @Override
    @Transactional
    public List<WaveScanException> receives(Staff staff, List<String> uniqueCodes) throws WaveScanException {
        Assert.notEmpty(uniqueCodes, "唯一码不能为空！");
        ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
        uniqueCodeParams.setUniqueCodeArr(uniqueCodes);
        return batchReceive(staff,uniqueCodeParams);
    }

    @Override
    @Transactional
    public WaveUniqueCode receive(Staff staff, String uniqueCode, Integer source, boolean rescan, boolean fromOpen) throws WaveScanException {
        return receive(staff, uniqueCode, source, rescan, fromOpen, false);
    }

    @Override
    @Transactional
    public WaveUniqueCode receive(Staff staff, String uniqueCode, Integer source, boolean rescan, boolean fromOpen, boolean allowCancel) throws WaveScanException {
        Assert.hasText(uniqueCode, "唯一码不能为空！");

        WaveUniqueCode code = uniqueCodeHelpBusiness.queryUniqueCodeInfo(staff, new UniqueCodeInfoQueryParams().setQueryType(1).setOriginCode(uniqueCode)).getUniqueCode();
        if (code == null || (!Objects.equals(code.getType(), 1) && !Objects.equals(code.getType(), 2))) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_EXIST, "唯一码不存在！");
        }
        WmsConfig wmsConfig = wmsService.getConfig(staff);

        // 开放平台调用，返回订单系统异常和自定义异常
        if (fromOpen && DataUtils.checkLongNotEmpty(code.getSid())) {
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, code.getSid());
            if (!CollectionUtils.isEmpty(trades)) {
                Trade trade = trades.get(0);
                TradeExceptionUtils.analyze(staff, trade);
                code.setIsExcep(trade.getIsExcep());
                // 自定义异常
                code.setExceptNames(trade.getExceptNames());
                // 系统异常需要处理
                code.setExceptions(trade.getExceptions());
            }
        }

        // 开放平台调用的，已收货和已出库的直接返回
        if (fromOpenNeedReturn(fromOpen, code)) {
            return code;
        }
        // 分配分拣货位
        orderUniqueCodeHelpBusiness.allocatePositionNo4Receive(staff, Lists.newArrayList(code), null);
        try {
            // 订单唯一码收货校验
            if (!OrderUniqueCodeUtils.isItemUniqueCode(code)) {
                //验货出库时校验状态
                if (Objects.equals(1, source)) {
                    // 验货出库填充是否播报多件
                    code.setVoiceMulti(Objects.equals(code.getCodeType(), 2) && orderUniqueCodeHelpBusiness.isOpenHybridScan(staff));
                    checkInspectionAndDeliveryStatus(code);
                }
                singleOrderUniqueCodeReceiveCheck(staff, code, allowCancel);
                if (Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())) {
                    fillScanVoice(staff, code);
                    return code;
                }

                if (code.getReceiveStatus() == CommonConstants.JUDGE_YES) {
                    // 如果是来自验货出库，不提示重复扫描
                    if (Objects.equals(1, source)) {
                        return code;
                    }
                    if (rescan) {
                        fillScanVoice(staff, code);
                        return code;
                    }
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, String.format("唯一码[%s]已重复扫描！", code.getUniqueCode()));
                }
                if (OrderUniqueCodeUtils.cannotReceive(code, allowCancel)) {
                    // 如果来自验货出库且家里的唯一码，不报错
                    if (Objects.equals(1, source) && Objects.equals(CommonConstants.JUDGE_YES, code.getStockStatus())) {
                        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(code)), WaveUniqueOpType.INSPECTION}), null);
                        return code;
                    }
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, String.format("该唯一码[%s]状态不允许收货！", code.getUniqueCode()));
                }
            } else { // 商品唯一码收货校验
                checkUniqueCodeItemRefund(staff, code);
                singleItemUniqueCodeReceiveCheck(code);
            }
            // 唯一码收货，超时判断
            code = checkReceiveOverTime(staff, code);
            checkPurchaseReturnReceive(staff, code, wmsConfig);
            // 处理已取消的唯一码
            boolean handleCancelCode = allowCancel && Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus());
            boolean isPick = OrderUniqueCodeUtils.isPick(code) || (handleCancelCode && Objects.equals(code.getStockStatus(), 1));
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            revertOffTime(code, update);
            if (!isPick) {
                update.setReceiveStatus(CommonConstants.JUDGE_YES);
                update.setReceiveTime(new Date());
            }
            update.setStatus(receiveAfterChangeStatus(code, handleCancelCode));
            update.setStockPosition(fillStockPosition(code, update, handleCancelCode));
            if (handleCancelCode) {
                OrderUniqueCodeUtils.buildRelease(update);
            }
            waveUniqueCodeDao.update(staff, update);

            // 唯一码收货
            if (!isPick) {
                // 已采退的外采唯一码收货时会进行无单收货生成新的收货单,上次收货的收货单要解绑
                releaseWarehouseEntry4PurchaseReturnCode(staff, Lists.newArrayList(code));
                eventCenter.fireEvent(this, new EventInfo("wave.unique.code.receive").setArgs(new Object[]{staff, Lists.newArrayList(code)}), null);
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.buildLogByOperateSource(Lists.newArrayList(code), "PDA".equalsIgnoreCase(OperateSourceContext.acquireOperateSourceNew().getProject()) ? UniqueCodeOperateSourceEnum.PDA_RECEIVE : UniqueCodeOperateSourceEnum.PC_RECEIVE), WaveUniqueOpType.ORDER_RECEIVE, staff.getName()}), null);
            } else {
                // 唯一码拣选
                uniqueCodePrintPicked(staff, Lists.newArrayList(code), true);
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.buildLogByOperateSource(Lists.newArrayList(code), Objects.equals(1, source) ? UniqueCodeOperateSourceEnum.PC_EXAMINE : UniqueCodeOperateSourceEnum.PC_RECEIVE), WaveUniqueOpType.ORDER_RECEIVE, staff.getName()}), null);
            }
            sendOrderUniqueCodeReceiveEvent(staff, Lists.newArrayList(code));
        } catch (WaveScanException ex) {
            // 唯一码收货抛出WaveScanException时优先判断是否存在商品退款异常
            WaveScanException exception  = ex;
            if (Objects.equals(WaveScanException.ERROR_CODE_ITEM_REFUND, code.getErrorCode())) {
                exception = new WaveScanException(WaveScanException.ERROR_CODE_ITEM_REFUND, "商品退款中！");
            }
            logger.error(LogHelper.buildErrorLog(staff, exception, "唯一码收货失败"), exception);
            waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, Lists.newArrayList(code), exception, WaveUniqueOpType.ORDER_RECEIVE));
            throw exception;
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "唯一码收货失败"), e);
            waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, Lists.newArrayList(code), e, WaveUniqueOpType.ORDER_RECEIVE));
            throw e;
        }
        // 填充到货/到齐/已拣
        fillScanVoice(staff, code);
        // 填充快递公司
        fillExpressInfo(staff, code);
        // 填充分拣货位
        fillPositionNo4Receive(staff, code);
        return code;
    }

    private void fillPositionNo4Receive(Staff staff, WaveUniqueCode code) {
        if (code == null) {
            return;
        }
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (!Objects.equals(config.getPositionNoWay(), 2)) {
            return;
        }
        WaveUniqueCode uniqueCode = uniqueCodeHelpBusiness.queryUniqueCodeInfo(staff, new UniqueCodeInfoQueryParams().setQueryType(1).setOriginCode(code.getUniqueCode())).getUniqueCode();
        if (uniqueCode == null) {
            return;
        }
        code.setPositionNo(uniqueCode.getPositionNo());
    }

    @Override
    public WaveUniqueCode checkReceiveOverTime(Staff staff, WaveUniqueCode code) throws WaveScanException {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (config == null || config.getExtConfigMap() == null) {
            return code;
        }
        Integer receiveUniqueCodeOverTimeConfig = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.RECEIVE_UNIQUE_CODE_OVER_TIME_CONFIG.getKey(), 0);
        Integer receiveUniqueCodeOverTimeLength = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.RECEIVE_UNIQUE_CODE_OVER_TIME_LENGTH.getKey(), 0);
        if (receiveUniqueCodeOverTimeConfig == 0 || receiveUniqueCodeOverTimeLength <= 0) {
            return code;
        }
        Integer receiveUniqueCodeOverTimeType = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.RECEIVE_UNIQUE_CODE_OVER_TIME_TYPE.getKey(), 1);
        Integer receiveUniqueCodeOverTimeHandleType = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.RECEIVE_UNIQUE_CODE_OVER_TIME_HANDLE_TYPE.getKey(), 1);
        Integer logGoodsMemorandum = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.LOG_GOODS_MEMORANDUM.getKey(), 0);
        Date now = new Date();
        if (receiveUniqueCodeOverTimeType == 1) {
            if (DataUtils.convertNullIntegerToDefault(code.getPrintNum()) == 0) {
                return code;
            }
            if (now.after(DateUtil.addDateByHour(code.getPrintTime(), receiveUniqueCodeOverTimeLength))) {
                if (receiveUniqueCodeOverTimeHandleType == 1) {
                    orderUniqueCodeExtendService.logGoodsMemorandum(staff, code, logGoodsMemorandum, "超时收货（拦截）");
                    throw new WaveScanException(WaveScanException.RECEIVE_UNIQUE_CODE_OVER_TIME, "超时不可收货");
                } else if (receiveUniqueCodeOverTimeHandleType == 2) {
                    orderUniqueCodeExtendService.logGoodsMemorandum(staff, code, logGoodsMemorandum, "超时收货未拦截");
                    code.setReceiveOverTime(true);
                }
            }
        } else if (receiveUniqueCodeOverTimeType == 2) {
            if (code.getCreated() == null) {
                return code;
            }
            if (now.after(DateUtil.addDateByHour(code.getCreated(), receiveUniqueCodeOverTimeLength))) {
                if (receiveUniqueCodeOverTimeHandleType == 1) {
                    orderUniqueCodeExtendService.logGoodsMemorandum(staff, code, logGoodsMemorandum, "超时收货（拦截）");
                    throw new WaveScanException(WaveScanException.RECEIVE_UNIQUE_CODE_OVER_TIME, "超时不可收货");
                } else if (receiveUniqueCodeOverTimeHandleType == 2) {
                    orderUniqueCodeExtendService.logGoodsMemorandum(staff, code, logGoodsMemorandum, "超时收货未拦截");
                    code.setReceiveOverTime(true);
                }
            }
        }
        return code;
    }

    @Override
    public void checkPurchaseReturnReceive(Staff staff, WaveUniqueCode code, WmsConfig wmsConfig) throws WaveScanException {
        if (wmsConfig == null) {
            wmsConfig = wmsService.getConfig(staff);
        }
        Object obj = wmsConfig.get(WmsConfigExtInfoEnum.PURCHASE_RETURN_ALLOW_RECEIVE.getKey());
        Integer purchaseReturnAllowReceive = (obj == null ? 1 : (Integer) obj);
        if (!Objects.equals(1, purchaseReturnAllowReceive) && Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_PURCHASE_RETURN_CODE_NOT_ALLOW_RECEIVE, "已采退状态唯一码不允许收货！");
        }
    }

    /**
     * 填充快递信息
     */
    private void fillExpressInfo(Staff staff, WaveUniqueCode code){
        if (code == null || !DataUtils.checkLongNotEmpty(code.getTemplateId())) {
            return;
        }

        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (config == null || config.getExtConfigMap() == null) {
            return;
        }

        Integer playExpressName = (Integer) config.getExtConfigMap().get(OrderUniqueCodeExtConfigEnum.PLAY_EXPRESS_NAME.getKey());
        if (Objects.equals(playExpressName, CommonConstants.VALUE_YES)) {
            if (code.getTemplateType() == TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal()) {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQueryWithCache(staff, code.getTemplateId(), false);
                code.setExpressName(userWlbExpressTemplate == null ? null : userWlbExpressTemplate.getExpressName());
            } else {
                UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQueryWithCache(staff, code.getTemplateId(), false);
                code.setExpressName(userExpressTemplate == null ? null : userExpressTemplate.getExpressName());
            }
        }
    }

    /**
     * 开放平台调用的，已收货和已出库的直接返回
     */
    private boolean fromOpenNeedReturn(boolean fromOpen, WaveUniqueCode code) {
        if (fromOpen) {
            return Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())
                    || Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus());
        }
        return false;
    }

    /**
     * 扫描采退日志
     */
    public void recordScanLog(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        if (CollectionUtils.isEmpty(params.getReturnOrderUniqueCodeList())) {
            return;
        }
        List<WaveUniqueCode> transCodes = params.getReturnOrderUniqueCodeList().stream().map(r -> Optional.ofNullable(r.getWaveUniqueCodeList()).orElse(Lists.newArrayList())).flatMap(List::stream).collect(toList());
        transCodes = CollectionUtils.isEmpty(transCodes) ? params.getReturnOrderUniqueCodeList().stream().map(w -> {
            WaveUniqueCode code = new WaveUniqueCode();
            code.setUniqueCode(w.getScanUniqueCode());
            return code;
        }).collect(toList()) : transCodes;
        if (CollectionUtils.isEmpty(transCodes)) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(transCodes), WaveUniqueOpType.SCAN_PURCHASE_RETURN, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setSource(params.getSource())}), null);
    }

    @Transactional
    public void batchUpdateProcessStatus(Staff staff, List<String> uniqueCodes, Integer processStatus) {
        Assert.isTrue(!CollectionUtils.isEmpty(uniqueCodes), "唯一码不能为空! ");
        Assert.notNull(processStatus, "变更状态不能为空! ");
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到唯一码！"));
            }
            return;
        }

        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode waveUniqueCode = new WaveUniqueCode();
            waveUniqueCode.setId(code.getId());
            waveUniqueCode.setProcessStatus(processStatus);
            updates.add(waveUniqueCode);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("更新加工状态为%s, 唯一码%s", processStatus, updates.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()))));
        }
    }

    private List<WaveUniqueCode> getReviceGenericCodes(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        List<WaveUniqueCode> waveUniqueCodes = Lists.newArrayList();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(params.getUniqueCodes())) {
            Map<String, Object> itemSkuMap = DataUtils.toLowerCase(waveHelpBusiness.queryMultiShipperItemSkuAdapter(staff, params.getUniqueCodes(),  params.getShipperId(), 123));
            Warehouse warehouse = warehouseService.queryDefault(staff);
            if (CollectionUtils.isEmpty(itemSkuMap)) {
                if (CompanyUtils.openMultiShipper(staff)) {
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, "商品货主信息与采退单货主不一致！");
                }
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, "唯一码(通用码)不存在！");
            }

            List<ReturnOrderUniqueCode> frontReturnOrderUniqueCodeList = params.getReturnOrderUniqueCodeList().stream().filter(returnOrderUniqueCode -> params.getUniqueCodes().contains(returnOrderUniqueCode.getUniqueCode())).collect(toList());

            waveUniqueCodes = frontReturnOrderUniqueCodeList.stream().map(uniqueCode -> {
                if (StringUtils.isBlank(uniqueCode.getUniqueCode())) {
                    if (CompanyUtils.openMultiShipper(staff)) {
                        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("货主%s，不存在唯一码(通用码)%s！", params.getShipperName(), uniqueCode.getUniqueCode()));
                    }
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("%s唯一码(通用码)不存在！", uniqueCode.getUniqueCode()));
                }
                Object obj = itemSkuMap.get(uniqueCode.getUniqueCode().toLowerCase());
                if (obj == null){
                    if (CompanyUtils.openMultiShipper(staff)) {
                        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("货主%s，不存在唯一码(通用码)%s！", params.getShipperName(), uniqueCode.getUniqueCode()));
                    }
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("%s唯一码(通用码)不存在！", uniqueCode.getUniqueCode()));
                }
                WaveUniqueCode waveUniqueCode = new WaveUniqueCode();
                if (obj instanceof DmjSku) {
                    DmjSku dmjSku = (DmjSku) obj;
                    waveUniqueCode.setSysItemId(dmjSku.getSysItemId());
                    waveUniqueCode.setSysSkuId(dmjSku.getSysSkuId());
                    waveUniqueCode.setPicPath(dmjSku.getPicPath());
                    waveUniqueCode.setUniqueCode(uniqueCode.getUniqueCode());
                    waveUniqueCode.setSkuPicPath(dmjSku.getSkuPicPath());
                    waveUniqueCode.setSkuOuterId(dmjSku.getSkuOuterId());
                    waveUniqueCode.setOuterId(dmjSku.getSkuOuterId());
                    waveUniqueCode.setMainOuterId(dmjSku.getItemOuterId());
                    waveUniqueCode.setSkuTitle(dmjSku.getTitle());
                    waveUniqueCode.setPropertiesName(dmjSku.getPropertiesName());
                    if (dmjSku.getBestItemSupplierBridge() != null && null != dmjSku.getBestItemSupplierBridge().getDiscountRate()) {
                        waveUniqueCode.setItemDiscountRate(dmjSku.getBestItemSupplierBridge().getDiscountRate());
                    }
                    waveUniqueCode.setSupplierId(Optional.ofNullable(dmjSku).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierId).orElse(0L));
                    waveUniqueCode.setSupplierName(Optional.ofNullable(dmjSku).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierName).orElse(""));
                    if (uniqueCode.getSupplierId() != null) {
                        waveUniqueCode.setSupplierId(uniqueCode.getSupplierId());
                        waveUniqueCode.setSupplierName(uniqueCode.getSupplierName());
                    }
                    waveUniqueCode.setScanStaffId(uniqueCode.getScanStaffId());
                    waveUniqueCode.setScanStaffName(uniqueCode.getScanStaffName());
                    waveUniqueCode.setScanTime(uniqueCode.getScanTime());
                    waveUniqueCode.setType(6);
                } else if (obj instanceof DmjItem) {
                    DmjItem dmjItem = (DmjItem) obj;
                    waveUniqueCode.setSysItemId(dmjItem.getSysItemId());
                    waveUniqueCode.setOuterId(dmjItem.getOuterId());
                    waveUniqueCode.setSysSkuId(0L);
                    waveUniqueCode.setUniqueCode(uniqueCode.getUniqueCode());
                    waveUniqueCode.setPicPath(dmjItem.getPicPath());
                    waveUniqueCode.setMainOuterId(dmjItem.getOuterId());
                    waveUniqueCode.setMainTitle(dmjItem.getTitle());
                    if (dmjItem.getBestItemSupplierBridge() != null && null != dmjItem.getBestItemSupplierBridge().getDiscountRate()) {
                        waveUniqueCode.setItemDiscountRate(dmjItem.getBestItemSupplierBridge().getDiscountRate());
                    }
                    waveUniqueCode.setSupplierId(Optional.ofNullable(dmjItem).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierId).orElse(0L));
                    waveUniqueCode.setSupplierName(Optional.ofNullable(dmjItem).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierName).orElse(""));
                    if (uniqueCode.getSupplierId() != null) {
                        waveUniqueCode.setSupplierId(uniqueCode.getSupplierId());
                        waveUniqueCode.setSupplierName(uniqueCode.getSupplierName());
                    }
                    waveUniqueCode.setScanStaffId(uniqueCode.getScanStaffId());
                    waveUniqueCode.setScanStaffName(uniqueCode.getScanStaffName());
                    waveUniqueCode.setScanTime(uniqueCode.getScanTime());
                    waveUniqueCode.setType(6);
                }

                waveUniqueCode.setWarehouseId(getGenericCodeWarehouseId(staff, warehouse, params));
                waveUniqueCode.setScanUniqueCode(uniqueCode.getScanUniqueCode());

                return waveUniqueCode;
            }).collect(Collectors.toList());

        }
        return waveUniqueCodes;
    }




    private List<WaveUniqueCode> getScanGenericCodes(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        List<WaveUniqueCode> waveUniqueCodes = Lists.newArrayList();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(params.getUniqueCodes())) {
            Map<String, Object> itemSkuMap = DataUtils.toLowerCase(waveHelpBusiness.queryMultiShipperItemSkuAdapter(staff, params.getUniqueCodes(), params.getShipperId(), 123));
            Warehouse warehouse = warehouseService.queryDefault(staff);
            if (CollectionUtils.isEmpty(itemSkuMap)) {
                if (CompanyUtils.openMultiShipper(staff)) {
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("货主%S，不存在该唯一码(通用码)%S！", params.getShipperName(), params.getUniqueCodes()));
                }
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("%s唯一码(通用码)不存在！", params.getUniqueCodes()));
            }


            waveUniqueCodes = params.getUniqueCodes().stream().map(outerId -> {
                Object obj = itemSkuMap.get(outerId.toLowerCase());
                if (obj == null){
                    if (CompanyUtils.openMultiShipper(staff)) {
                        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("货主%s，不存在唯一码(通用码)%S！", params.getShipperName(), outerId));
                    }
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("%s唯一码(通用码)不存在！", outerId));
                }
                WaveUniqueCode waveUniqueCode = new WaveUniqueCode();
                if (obj instanceof DmjSku) {
                    DmjSku dmjSku = (DmjSku) obj;
                    waveUniqueCode.setSysItemId(dmjSku.getSysItemId());
                    waveUniqueCode.setSysSkuId(dmjSku.getSysSkuId());
                    waveUniqueCode.setPicPath(dmjSku.getPicPath());
                    waveUniqueCode.setUniqueCode(outerId);
                    waveUniqueCode.setSkuPicPath(dmjSku.getSkuPicPath());
                    waveUniqueCode.setSkuOuterId(dmjSku.getSkuOuterId());
                    waveUniqueCode.setOuterId(dmjSku.getSkuOuterId());
                    waveUniqueCode.setMainOuterId(dmjSku.getItemOuterId());
                    waveUniqueCode.setSkuTitle(dmjSku.getTitle());
                    waveUniqueCode.setPropertiesName(dmjSku.getPropertiesName());
                    waveUniqueCode.setSupplierId(Optional.ofNullable(dmjSku).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierId).orElse(0L));
                    waveUniqueCode.setSupplierName(Optional.ofNullable(dmjSku).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierName).orElse(""));
                    setItemSupplierBridge(waveUniqueCode, dmjSku);
                    waveUniqueCode.setType(6);
                    // 商品简称/规格简称/规格备注  商品简称需要再调用dubbo查询影响效率,所以先判断该列配置是否展示
                    if (shortTitleShow(staff)) {
                        waveUniqueCode.setShortTitle(Optional.ofNullable(itemServiceDubbo.queryItemWithSysItemId(staff, dmjSku.getSysItemId())).map(DmjItem::getShortTitle).orElse(""));
                    }
                    waveUniqueCode.setSkuShortTitle(dmjSku.getSkuShortTitle());
                    waveUniqueCode.setSkuRemark(dmjSku.getSkuRemark());
                    waveUniqueCode.setItemActiveStatus(dmjSku.getActiveStatus());
                } else if (obj instanceof DmjItem) {
                    DmjItem dmjItem = (DmjItem) obj;
                    waveUniqueCode.setSysItemId(dmjItem.getSysItemId());
                    waveUniqueCode.setOuterId(dmjItem.getOuterId());
                    waveUniqueCode.setSysSkuId(0L);
                    waveUniqueCode.setUniqueCode(outerId);
                    waveUniqueCode.setPicPath(dmjItem.getPicPath());
                    waveUniqueCode.setMainOuterId(dmjItem.getOuterId());
                    waveUniqueCode.setMainTitle(dmjItem.getTitle());
                    waveUniqueCode.setSupplierId(Optional.ofNullable(dmjItem).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierId).orElse(0L));
                    waveUniqueCode.setSupplierName(Optional.ofNullable(dmjItem).map(DmjItem::getBestItemSupplierBridge).map(ItemSupplierBridge::getSupplierName).orElse(""));
                    setItemSupplierBridge(waveUniqueCode, dmjItem);
                    waveUniqueCode.setType(6);
                    // 商品简称
                    waveUniqueCode.setShortTitle(dmjItem.getShortTitle());
                    waveUniqueCode.setItemActiveStatus(dmjItem.getActiveStatus());
                }

                waveUniqueCode.setWarehouseId(getGenericCodeWarehouseId(staff, warehouse, params));

                return waveUniqueCode;
            }).collect(Collectors.toList());

        }
        return waveUniqueCodes;
    }

    private void setItemSupplierBridge(WaveUniqueCode waveUniqueCode, DmjItem dmjSku) {
        if (CollectionUtils.isEmpty(dmjSku.getItemSupplierBridgeList())) {
            return;
        }
        List<ItemSupplierBridge> simpleList = Lists.newArrayList();
        for (ItemSupplierBridge itemSupplierBridge : dmjSku.getItemSupplierBridgeList()) {
            ItemSupplierBridge bridge = new ItemSupplierBridge();
            bridge.setSupplierId(itemSupplierBridge.getSupplierId());
            bridge.setSupplierName(itemSupplierBridge.getSupplierName());
            simpleList.add(bridge);
        }
        waveUniqueCode.setItemSupplierBridgeList(simpleList);
    }

    private static Integer getGenericCodeOutLocation(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        if (params != null && DataUtils.checkIntegerNotEmpty(params.getGenericCodeOutLocation())) {
            return params.getGenericCodeOutLocation();
        }
        return staff.getConf().getGenericCodeOutLocation() != 0
                ? staff.getConf().getGenericCodeOutLocation()
                : StockOutLocationEnum.TYPE_3.getCode();
    }

    private static Long getGenericCodeWarehouseId(Staff staff, Warehouse defaultWarehouse, ReturnOrderUniqueCodeReceiveParams params) {
        if (params != null && DataUtils.checkLongNotEmpty(params.getGenericCodeReturnWarehouseId())) {
            return params.getGenericCodeReturnWarehouseId();
        }
        return staff.getConf().getGenericCodeWarehouseId() != 0
                ? staff.getConf().getGenericCodeWarehouseId()
                : defaultWarehouse.getId();
    }

    private Map<String, WmsStockVo> getWmsStockVoMap(Staff staff, ReturnOrderUniqueCodeReceiveParams params, Warehouse warehouse) {
        WmsStockQueryParams wmsStockQueryParams = new WmsStockQueryParams();
        wmsStockQueryParams.setTexts(params.getUniqueCodes());
        Integer genericCodeOutLocation = getGenericCodeOutLocation(staff, params);
        Long genericCodeWarehouseId = getGenericCodeWarehouseId(staff, warehouse, params);
        if (StockOutLocationEnum.TYPE_3.getCode().equals(genericCodeOutLocation)) {
            wmsStockQueryParams.setStockType(6);
            wmsStockQueryParams.addTypeEnums(WorkingStorageSection.TypeEnum.REFUND);
        } else if (StockOutLocationEnum.TYPE_2.getCode().equals(genericCodeOutLocation)) {
            wmsStockQueryParams.setStockType(5);
            wmsStockQueryParams.addTypeEnums(WorkingStorageSection.TypeEnum.PURCHASE);
        } else if (StockOutLocationEnum.TYPE_7.getCode().equals(genericCodeOutLocation)) {
            wmsStockQueryParams.setStockType(11);
            wmsStockQueryParams.addTypeEnums(WorkingStorageSection.TypeEnum.RETURN);
        }
        wmsStockQueryParams.setWarehouseId(genericCodeWarehouseId);
        List<WmsStockVo> wmsStockVos = workingStorageSectionDubboService.query(staff, wmsStockQueryParams, new Page().setPageSize(params.getUniqueCodes().size()));
        return Optional.ofNullable(wmsStockVos).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(WmsStockVo::getOuterId, Function.identity(), (v1, v2) -> v1));
    }

    public List<WaveUniqueCode> updateReturnOrderReceiveCode(Staff staff, ReturnOrderUniqueCodeReceiveParams params,BatchResult<SuccessItem> result, List<WaveUniqueCode> uniqueCodeList) {
        List<WaveUniqueCode> codes = Lists.newArrayList();
        List<WaveUniqueCode> resultCodes = Lists.newArrayList();

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(params.getUniqueCodes())){
            codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, ItemUniqueCodeQueryParams.build(params.getUniqueCodes()));
            if (!CollectionUtils.isEmpty(codes)) {
                List<ErrorItem> errorItems = Lists.newArrayList();
                Map<String, ReturnOrderUniqueCode> realyUniqueMap = getRealyUniqueMap(codes, params);
                //过滤无效唯一码
                Map<String, Integer> returnStatusMap = queryPurchaseReturnStatus(staff, codes);
                resultCodes.addAll(codes.stream().filter(code -> filterWaveUniqueCodeStatus(code, errorItems, false, returnStatusMap, staff,BooleanUtils.isTrue(params.getAutoOut()) ? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue())).collect(Collectors.toList()));
                List<Long> ids = resultCodes.stream().map(code -> {
                    code.setReturnOrderStatus(CommonConstants.JUDGE_YES);
                    ReturnOrderUniqueCode returnOrderUniqueCode = realyUniqueMap.get(code.getUniqueCode());
                    if (returnOrderUniqueCode != null) {
                        code.setScanStaffId(returnOrderUniqueCode.getScanStaffId());
                        code.setScanTime(returnOrderUniqueCode.getScanTime());
                        code.setScanStaffName(returnOrderUniqueCode.getScanStaffName());
                    }
                    return code.getId();
                }).collect(Collectors.toList());

                WaveUniqueCode update = new WaveUniqueCode();
                update.setReturnOrderStatus(CommonConstants.JUDGE_YES);
                waveUniqueCodeDao.batchUpdateByIds(staff, update, ids);
                result.setErrors(errorItems);
            }
            List<String> uniqueCodes = Optional.ofNullable(codes).orElse(Lists.newArrayList()).stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
            params.getUniqueCodes().removeAll(uniqueCodes);
            //填充通用码
            if (!CollectionUtils.isEmpty(params.getUniqueCodes())) {
                resultCodes.addAll(getReviceGenericCodes(staff, params));
            }
        }
        return resultCodes;
    }

    private Map<String, ReturnOrderUniqueCode> getRealyUniqueMap(List<WaveUniqueCode> codes,ReturnOrderUniqueCodeReceiveParams params){
        if (org.apache.commons.collections.CollectionUtils.isEmpty(codes)) {
            return Maps.newHashMap();
        }
        List<String> realyUniques = codes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList());
        List<ReturnOrderUniqueCode> frontRealyUniqueCodes = params.getReturnOrderUniqueCodeList().stream().filter(r -> realyUniques.contains(r.getUniqueCode())).collect(toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(frontRealyUniqueCodes)) {
            return Maps.newHashMap();
        }
        Map<String, ReturnOrderUniqueCode> map = frontRealyUniqueCodes.stream().collect(Collectors.toMap(ReturnOrderUniqueCode::getUniqueCode, v -> v, (k1, k2) -> k1));
        return map;
    }

    /**
     *
     * @param code
     * @param errorItems
     * @param singleScan
     * @param returnStatusMap
     * @param source 0:非离线模式扫描采退 1：离线模式扫描采退
     * @return
     */
    private boolean filterWaveUniqueCodeStatus(WaveUniqueCode code, List<ErrorItem> errorItems, boolean singleScan, Map<String , Integer> returnStatusMap, Staff staff,Integer source) {
        PurchaseConfig purchaseConfig = uniqueCodePurchaseServiceDubbo.autoCreateUniqueCodePurchaseOrder(staff);
        boolean boundUniqueCodePurchaseRetun = Objects.equals(purchaseConfig.getInteger("boundUniqueCodePurchaseReturn"), 1);
        logger.debug("未解绑唯一码支持采退:" + boundUniqueCodePurchaseRetun);
        // 关联采退单的状态
        Integer returnStatus = returnStatusMap != null  ? returnStatusMap.get(code.getUniqueCode()) : null;
        if (Objects.equals(code.getReturnOrderStatus(), CommonConstants.JUDGE_YES) && returnStatus != null) {
            if (checkReturnStatus(returnStatus) && Objects.equals(source,0)) {
                if (singleScan) {
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("[%s]唯一码已存在【待出库】的采退单，需要重复退货请先将单据出库！", code.getUniqueCode()));
                }
                errorItems.add(new ErrorItem(code.getUniqueCode(), String.format("唯一码[%s]已重复扫描！", code.getUniqueCode())));
                logger.debug("扫描采退过滤的唯一码:" + code.getUniqueCode());
                return false;
            }
            // 已作废的采退单再次用唯一码扫描采退，可以生成采退单，不生成采退调整单
            if (Objects.equals(returnStatus, 3) || Objects.equals(returnStatus, 0)) {
                // 重复扫描的唯一码需要单独处理
                code.setIsRepeat(CommonConstants.VALUE_YES);
                code.setSupportRepeat(true);
            }
        }

        if (!Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("唯一码[%s]未收货(未拣货 未在库)！", code.getUniqueCode()));
        }
        if ((Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.RETURN.getType(), code.getStatus())
                //售后唯一码不校验是否在库唯一码未解绑
        ) && !code.getSid().equals(0L) && !(Objects.equals(3,code.getCreateSource()) && Objects.equals(2,code.getType()))
                && !boundUniqueCodePurchaseRetun ){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_UNBOUND, String.format("唯一码[%s]在库唯一码未解绑！", code.getUniqueCode()));
        }
        if (boundUniqueCodePurchaseRetun && DataUtils.checkLongNotEmpty(code.getSid())) {
            code.setCanReturnExchange(true);
        }

        if (StringUtils.isBlank(code.getSkuOuterId()) && StringUtils.isBlank(code.getMainOuterId())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_UNBOUND, String.format("唯一码[%s]对应的商家编码已删除！", code.getUniqueCode()));
        }
        return true;
    }


    /**
     * PDA快速扫描采退过滤掉不符合的唯一码
     * @param code
     * @param errorItems
     * @param returnStatusMap
     * @return
     */
    private boolean filterWaveUniqueCodeStatus(Staff staff,WaveUniqueCode code, List<ErrorItem> errorItems,Map<String , Integer> returnStatusMap) {
        PurchaseConfig purchaseConfig = uniqueCodePurchaseServiceDubbo.autoCreateUniqueCodePurchaseOrder(staff);
        boolean boundUniqueCodePurchaseRetun = Objects.equals(purchaseConfig.getInteger("boundUniqueCodePurchaseReturn"), 1);
        logger.debug("未解绑唯一码支持采退:" + boundUniqueCodePurchaseRetun);
        if (Objects.equals(code.getStatus(),OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType())) {
            //待出库/已出库的采退单，不支持重复退货
            errorItems.add(new ErrorItem(code.getUniqueCode(),"重复退货"));
            logger.debug("扫描采退过滤的唯一码:" + code.getUniqueCode());
            return false;
        }
        if (!Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())) {
            errorItems.add(new ErrorItem(code.getUniqueCode(),"唯一码未收货(未拣货 未在库)！"));
            return false;
        }
        if ((Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.RETURN.getType(), code.getStatus())
                //售后唯一码不校验是否在库唯一码未解绑
        ) && !code.getSid().equals(0L) && !(Objects.equals(3,code.getCreateSource()) && Objects.equals(2,code.getType()))
                && !boundUniqueCodePurchaseRetun ){
            errorItems.add(new ErrorItem(code.getUniqueCode(),"在库唯一码未解绑！"));
            return false;
        }
        if (StringUtils.isBlank(code.getSkuOuterId()) && StringUtils.isBlank(code.getMainOuterId())) {
            errorItems.add(new ErrorItem(code.getUniqueCode(),"唯一码对应的商家编码已删除！"));
            return false;
        }
        //根据上面复制而来
        if (boundUniqueCodePurchaseRetun && DataUtils.checkLongNotEmpty(code.getSid())) {
            code.setCanReturnExchange(true);
        }
        return true;
    }

    public boolean checkReturnStatus(Integer returnStatus) {
        return !(Objects.equals(returnStatus, 3) || Objects.equals(returnStatus, 0));
    }

    public Map<String, Integer> queryPurchaseReturnStatus(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new HashMap<>();
        }

        try {
            List<Long> uniqueCodeIds = codes.stream().map(WaveUniqueCode::getId).collect(Collectors.toList());
            return uniqueCodePurchaseServiceDubbo.queryPurchaseReturnStatusByUniqueCodeIds(staff, uniqueCodeIds);
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.error(LogHelper.buildLog(staff, "采购dubbo查询出错"), e);
            }
            return new HashMap<>();
        }
    }

    /**
     * 验货出库状态校验
     *
     * @param code
     */
    public void checkInspectionAndDeliveryStatus(WaveUniqueCode code) {
        if (code == null) {
            return;
        }
        if (Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.UNIQUE_CODE_PICK, String.format("唯一码[%s]已拣选！", code.getUniqueCode()));
        }
        if (Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), code.getStatus())) {
            throw new WaveScanException(WaveScanException.UNIQUE_CODE_RECEIVED, String.format("唯一码[%s]已收货！", code.getUniqueCode()));
        }

    }


    @Override
    @Deprecated
    public List<WaveUniqueCode> returnOrderScan(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        WaveHelpBusiness.partitionDebug(staff, logger, "扫描前uniqueCode：" + params.getUniqueCodes());
        AnalysisOrderUniqueCodesResult result= orderUniqueCodeAnalysisBusiness.analysisOrderUniqueCodes(staff, params.getUniqueCodes(), params.getShipperId(), params.getShipperName());
        List<String> analysisAfterCodes = result.getAllCodeList();
        Map<String, String> scanUniqueMap = result.getScanUniqueMap();
        WaveHelpBusiness.partitionDebug(staff, logger, "扫描后跟前，uniqueCode：" + scanUniqueMap);
        params.setUniqueCodes(analysisAfterCodes);
        if (CollectionUtils.isEmpty(analysisAfterCodes)){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("唯一码状态不满足扫描要求！唯一码%s", analysisAfterCodes));
        }
        List<WaveUniqueCode> codes = getByUniqueOrGenericCode(staff, params);
        if (CollectionUtils.isEmpty(codes)){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("唯一码状态不满足扫描要求！唯一码%s", analysisAfterCodes));
        }
        uniqueCodePurchaseServiceDubbo.scanUniqueCodeVerifySupplierCategory(staff, codes.get(0));
        return returnSacnAfter(staff,codes,scanUniqueMap,params);
    }

    private List<WaveUniqueCode> returnSacnAfter(Staff staff,List<WaveUniqueCode> codes,Map<String, String> scanUniqueMap,ReturnOrderUniqueCodeReceiveParams params){
        fillWarehouseName(staff,codes);
        if (CollectionUtils.isEmpty(codes)){
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("唯一码[%s]不满足创建采退单要求！", params.getUniqueCodes()));
        }

        Map<String, PurchaseReturnUniqueCodeVo> purchaseReturnUniqueCodeMap = uniqueCodePurchaseServiceDubbo.queryByUniqueCodeIds(staff, codes.stream().map(WaveUniqueCode::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(PurchaseReturnUniqueCodeVo::getUniqueCode, a -> a, (a, b) -> a));
        for (WaveUniqueCode code : codes) {
            // 已出库没有库存位置，也支持生成采退单
            // 已经生成过采退单的唯一码不校验库存位置，因为第一次生成采退单的时候会清掉唯一码的库存位置
            if (Objects.equals(2, code.getType()) && purchaseReturnUniqueCodeMap.get(code.getUniqueCode()) == null && !DataUtils.checkIntegerNotEmpty(code.getStockPosition()) && !Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_RECEIVED, String.format("%s，库存位置为空不支持创建采退单！", code.getUniqueCode()));
            }
            if (!Objects.equals(code.getItemActiveStatus(), CommonConstants.JUDGE_YES)) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OFF_SHELF, String.format("唯一码[%s]对应的商品已停用！", code.getUniqueCode()));
            }
        }


        // 规格商家编码 skuOuterId || outerId（先取值skuOuterId，没有就取outerId）
        codes.stream().peek(waveUniqueCode -> waveUniqueCode.setSkuOuterId(waveUniqueCode.getSkuOuterId() == null ? waveUniqueCode.getOuterId() : waveUniqueCode.getSkuOuterId())).collect(Collectors.toList());
        if (MapUtils.isNotEmpty(scanUniqueMap)) {
            for (WaveUniqueCode code : codes) {
                code.setScanUniqueCode(scanUniqueMap.get(code.getUniqueCode()));
            }
        }
        return codes;
    }
    @Override
    public List<WaveUniqueCode> returnOrderScanPda(Staff staff, ReturnOrderUniqueCodeReceiveParams params) throws WaveScanException {
        return returnOrderScan(staff,params);
    }


    private void fillScanVoice(Staff staff, WaveUniqueCode code) {
        Long sid = code.getSid();
        if (sid == null || sid <= 0) {
            return;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(Collections.singletonList(sid));
        List<WaveUniqueCode> codes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params));
        List<WaveUniqueCode> waitReceivedCodes = codes.stream().filter(c -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), c.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), c.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), c.getStatus())).collect(toList());
        List<WaveUniqueCode> waitReceivedItemCodes = codes.stream().filter(c -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), c.getStatus())).collect(toList());
        if (CollectionUtils.isEmpty(waitReceivedCodes) && CollectionUtils.isEmpty(waitReceivedItemCodes)) {
            code.setAllReceive(1);
        } else if (OrderUniqueCodeUtils.isPick(code) || isPicked(code)) {
            code.setAllReceive(2);
        } else {
            code.setAllReceive(0);
        }
    }

    private static boolean isPicked(WaveUniqueCode code) {
        return code.getStatus() != null && Objects.equals(code.getType(), 1) && Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), code.getStatus());
    }

    private Integer receiveAfterChangeStatus(WaveUniqueCode code, boolean handleCancelCode) {
        if (handleCancelCode) {
            return uniqueCodeBaseService.getStatus4AllowCancel(code);
        }
        if (OrderUniqueCodeUtils.isItemUniqueCode(code)) {
            return OrderUniqueCodeStatusEnum.IN.getType();
        } else {
            if (OrderUniqueCodeUtils.isPick(code)) {
                return OrderUniqueCodeStatusEnum.PICKED.getType();
            }
            return Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())
                    ? OrderUniqueCodeStatusEnum.OUT.getType()
                    : OrderUniqueCodeStatusEnum.RECIVED.getType();
        }

    }

    /**
     * 订单唯一码收货填充库存位置,多次收货不处理
     * @return
     */
    private Integer fillStockPosition(WaveUniqueCode code, WaveUniqueCode update, boolean handleCancelCode) {
        if (handleCancelCode) {
            return uniqueCodeBaseService.getStockPosition4AllowCancel(code);
        }
        //已出库的唯一码库存位置需要清空
        if (Objects.equals(update.getStatus(), OrderUniqueCodeStatusEnum.OUT.getType())) {
            return UniqueCodeStockPositionEnum.DEFAULT.getType();
        }
        if (Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), update.getStatus())) {
            return UniqueCodeStockPositionEnum.PICK_AREA.getType();
        }
        if (Objects.equals(update.getStatus(), OrderUniqueCodeStatusEnum.RECIVED.getType()) || OrderUniqueCodeUtils.isItemUniqueCode(code)) {
            return UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType();
        }
        return null;
    }

    @Override
    public boolean openOrderUniqueCodeNewSplit(Staff staff) {
        return orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
    }

    @Override
    public WavePickingScanResult scanByUniqueCode(Staff staff, WavePickingParam param) {
        WaveUniqueCode code = getByCode(staff, param.getUniqueCode());
        if (Objects.equals(code.getCodeType(), 2)) {
            param.setMultiTrade(true);
        }
        if (Objects.equals(code.getStockPosition(), UniqueCodeStockPositionEnum.DEFECTIVE_AREA.getType())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "次品暂存区唯一码不支持验货！");
        }
        // 校验唯一码的采购单状态
        if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())) {
            checkUniqueCodeWaitReceivingInner(staff, code);
        }
        param.setIsSingle(Objects.equals(CommonConstants.JUDGE_YES, code.getCodeType()) || Objects.equals(code.getCodeType(), 3) ? 1 : 2);
        Integer isSingle = param.getIsSingle();
        handleHybridScan(staff, param, code);
        param.setUniqueCodeMultiplex(!DataUtils.checkLongNotEmpty(code.getSid()));
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        param.setInterceptTagIds(config.getInterceptTagIds());
        setIgnoreExceptIds(config, param);
        param.setSubmitExamine(isSubmitExamine(staff, param, code));
        param.setUniqueCodeMultiplex(config.getUniqueCodeMultiplex());

        List<WaveScanException> receiveExceptions = new ArrayList<>();
        if (param.isNewStyle() && param.isSubmitExamine()) {
            receiveExceptions.addAll(beanProxy.examineBatchReceive(staff, param.getCurrentMatchedUniqueCodes(), param.isNewStyle(), param.isAllowCancel()));
        }

        param.setOrderUniqueCodeConfig(config);
        WavePickingScanResult result = (
                (isSingle == 1 || param.isUniqueCodeMultiplex())
                        ? scanByUniqueCodeSingle(staff, param, waveUniqueCodeDao, waveHelpBusiness, tradeSearchService, orderUniqueCodeHelpBusiness, tradeTraceServiceDubbo ,waveUniqueCodeLogDao, shopService)
                        : scanByUniqueCodeMulti(staff, param, waveUniqueCodeDao, waveHelpBusiness, tradeSearchService, orderUniqueCodeHelpBusiness, tradeTraceServiceDubbo ,waveUniqueCodeLogDao, shopService));

        if (result.getTrade() != null && isTipFreeScan(config)) {
            result.setFreeScanOuterIds(getSidFreeScanOuterIds(staff, result.getTrade().getSid()));
        }
        result.setWaveScanExceptions(receiveExceptions.stream().filter(e ->
                !Objects.equals(WaveScanException.ERROR_CODE_UNIQUE_CODE_RECEIVED, e.getErrCode())).collect(toList()));

        return result;
    }

    /**
     * 单多混扫模式验货，前端不再调用唯一码收货接口，只调用验货接口
     * @param staff
     * @param param
     * @param code
     */
    private void handleHybridScan(Staff staff, WavePickingParam param, WaveUniqueCode code) {
        Integer hybridScanType = getHybridScanType(staff);
        if (param.isNewStyle() || !OrderUniqueCodeUtils.isOpenHybridScan(hybridScanType)) {
            return;
        }
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "单多混扫模式进行收货！"));
        }

        List<String> matchedUniqueCodes = getMatchedUniqueCodes(staff, code, true);
        param.setAllowCancelReceive4HybridScan(true);
        if (!CollectionUtils.isEmpty(matchedUniqueCodes) && !Objects.equals(hybridScanType, 3)) {
            param.setCurrentMatchedUniqueCodeStr(Strings.join(",", matchedUniqueCodes));
        }
        if (Objects.equals(code.getReceiveStatus(), CommonConstants.JUDGE_YES)) {
            return;
        }
        ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
        uniqueCodeParams.setOpType(0);
        uniqueCodeParams.setUniqueCodeArr(Lists.newArrayList(code.getUniqueCode()));
        uniqueCodeParams.setAllowCancel(true);
        beanProxy.batchReceive(staff, uniqueCodeParams);
    }

    private static boolean isTipFreeScan(OrderUniqueCodeConfig config) {
        if (config == null || MapUtils.isEmpty(config.getExtConfigMap())) {
            return false;
        }
        return Objects.equals(config.getExtConfigMap().get("openTipFreeScan"), 1);
    }

    private static Integer getUniqueCodeMultiplex(WavePickingParam param) {
        return param.getUniqueCodeMultiplexFront() == null ? param.getUniqueCodeMultiplex() : param.getUniqueCodeMultiplexFront();
    }

    /**
     * 判断解绑原因
     * @param staff
     * @param uniqueCode
     */
    public void checkedUnboundReasonException(Staff staff, String uniqueCode) {
        OrderUniqueCodeUnboundLog orderUniqueCodeUnboundLog = orderUniqueCodeUnboundLogDao.queryByLastOne(staff, uniqueCode);
        if (orderUniqueCodeUnboundLog != null) {
            switch (orderUniqueCodeUnboundLog.getReasonType()) {
                case 1:
                    //订单作废
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_TRADE_CANCEL, "解绑原因: 订单作废");
                case 2:
                    //反审核
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_TRADE_WAIT_AUDIT, "解绑原因: 反审核");
                case 3:
                    //交易关闭
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_TRADE_CLOSED, "解绑原因: 交易关闭");
                case 4:
                    //已发货
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_TRADE_SEND_GOODS, "解绑原因: 卖家已发货");
                case 5:
                    //交易完成
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_TRADE_FINISHED, "解绑原因: 交易完成");
                case 9:
                    //其他ERP发货
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_OTHER_ERP, "解绑原因: 其他ERP发货");
                default:
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_UNBOUND, "唯一码已解绑");
            }
        } else {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_UNBOUND, "唯一码已解绑");
        }
    }

    private boolean isSubmitExamine(Staff staff, WavePickingParam param, WaveUniqueCode code) {
        if (!param.isNewStyle()  // 1. 旧模式
                || OrderUniqueCodeUtils.isTrue(param.getIsSingle()) // 2. 单件
                || param.isUniqueCodeMultiplex()) { // 3. 唯一码复用
            return true;
        }
        List<WaveUniqueCode> codes = OrderUniqueCodeUtils.filterFinalGenerates(queryBySid(staff, code.getSid()));
        if (!CollectionUtils.isEmpty(codes)
                && (codes.size() == 1 || UniqueCodeUtils.removeCancelCodes(codes).size() == 1)) {
            param.setMultiTrade(false);
        }
        if (codes.stream().allMatch(c -> Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), c.getStatus()))) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_CANCEL, "唯一码已取消！");
        }

        return codes.stream().allMatch(c -> Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), c.getStatus())
                || param.getCurrentMatchedUniqueCodes().contains(c.getUniqueCode()));
    }

    @Override
    @Transactional
    public List<WaveScanException> examineBatchReceive(Staff staff, List<String> currentMatchedUniqueCodes, boolean newStyle) {
        return examineBatchReceive(staff, currentMatchedUniqueCodes, newStyle, false);
    }

    @Override
    @Transactional
    public List<WaveScanException> examineBatchReceive(Staff staff, List<String> currentMatchedUniqueCodes, boolean newStyle, boolean allowCancel) {
        if (!newStyle) {
            return Collections.emptyList();
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "验货触发收货！"));
        }

        ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
        uniqueCodeParams.setOpType(0);
        uniqueCodeParams.setUniqueCodeArr(currentMatchedUniqueCodes);
        uniqueCodeParams.setAllowCancel(allowCancel);
        return batchReceive(staff, uniqueCodeParams);
    }

    private static void setIgnoreExceptIds(OrderUniqueCodeConfig config, WavePickingParam param) {
        if (config == null || param == null
                || StringUtils.isNotEmpty(param.getIgnoreExceptIds())
                || StringUtils.isEmpty(config.getIgnoreExceptInfo())) {
            return;
        }
        String ignoreExceptInfo = config.getIgnoreExceptInfo();
        List<String> ignores = getIgnoreExcepts(ignoreExceptInfo);
        /**
         * 允许异常订单强制发货，配置对应关系异常不拦截
         * 1.单件取消对应关系异常并验货(原逻辑）
         * 2.多件不取消对应关系异常且正常/对应关系唯一码都可以验货，在打印节点才会取消对应关系异常
         */
        if (Objects.equals(param.getIsSingle(), 2)) {
            if (ignores.contains(EXCEP_ITEM_RELATION_MODIFIED)) {
                // 标记对应关系异常不拦截时扫描的正常/对应关系唯一码都可以验货
                param.setRelationChangedMultiExamine(true);
                ignores.removeIf(i -> i.equals(EXCEP_ITEM_RELATION_MODIFIED));
            }
        }

        param.setIgnoreExceptIds(StringUtils.join(ignores, ","));
    }

    private static List<String> getIgnoreExcepts(String ignoreExceptInfo) {
        List<String> ignores = new ArrayList<>();
        for (String ignoreExcept : ignoreExceptInfo.split(",")) {
            if (StringUtils.isEmpty(ignoreExcept) || !ignoreExcept.contains(":")) {
                continue;
            }
            String[] s = ignoreExcept.split(":");
            if (Objects.equals(CommonConstants.JUDGE_NO, Integer.valueOf(s[1]))) {
                ignores.add(s[0]);
            }
        }
        return ignores;
    }

    private WavePickingScanResult scanByUniqueCodeSingle(Staff staff, WavePickingParam param,
                                                         WaveUniqueCodeDao daoParam, WaveHelpBusiness waveHelpParam, ITradeSearchService tradeSearchParam, OrderUniqueCodeHelpBusiness helpBusiness, ITradeTraceService tradeTraceParam,WaveUniqueCodeLogDao uniqueCodeLogDao, IShopService shopParam) {
        return new AbstractOrderUniqueCodeExamineBusiness() {
            @Override
            public void initService() {
                setWaveUniqueCodeDao(daoParam);
                setWaveHelpBusiness(waveHelpParam);
                setTradeSearchService(tradeSearchParam);
                setOrderUniqueCodeHelpBusiness(helpBusiness);
                setTradeTraceService(tradeTraceParam);
                setWaveUniqueCodeLogDao(uniqueCodeLogDao);
                setShopService(shopParam);
            }

            @Override
            public void examineBefore(Staff staff, WavePickingParam param, WaveUniqueCode code) {
                //判断是否开启唯一码复用和已解绑
                if (Objects.equals(CommonConstants.VALUE_NO, getUniqueCodeMultiplex(param)) && !DataUtils.checkLongNotEmpty(code.getSid())) {
                    checkedUnboundReasonException(staff, param.getUniqueCode());
                }
            }

            @Override
            @Transactional
            public WavePickingScanResult doExamine(Staff staff, WaveUniqueCode code, Trade trade, WavePickingParam param) {
                WavePickingScanResult result = new WavePickingScanResult();
                result.setBeforeStatus(code.getStatus());
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(code.getId());
                update.setCompanyId(staff.getCompanyId());
                update.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                waveUniqueCodeDao.update(staff, update);

                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "单件唯一码：" + code.getUniqueCode() + "出库成功！"));
                }
                result.setUniqueCodeId(code.getId());
                result.setTradeMatched(true);
                result.setTradeAllMatched(true);
                return result;
            }

            @Override
            public Trade matchTradeAfter(Staff staff, WavePickingParam param, Trade trade) {
                return cancelTradeExcept(staff, param.getIgnoreExceptIds(), trade);
            }

            @Override
            public void validateTemplateInfo(Staff staff, WavePickingParam param, Trade trade, WaveUniqueCode code) {
                validateTemplate(staff,param,trade, code);
            }

            @Override
            public void examineAfter(Staff staff,List<WaveUniqueCode> waveUniqueCodes){}

            @Override
            public Trade matchTrade(Staff staff, WaveUniqueCode code) {
                if (DataUtils.checkLongNotEmpty(code.getSid())) {
                    return super.matchTrade(staff, code);
                }
                Long sysSkuId = (code.getSysSkuId() == null || code.getSysSkuId() == 0) ? -1L : code.getSysSkuId();
                // 复用唯一码
                WavePickingParam wavePickingParam = new WavePickingParam();
                wavePickingParam.setWarehouseId(code.getWarehouseId());
                wavePickingParam.setTemplateStr(param.getTemplateStr());
                wavePickingParam.setLogisticsCompanyIdStr(param.getLogisticsCompanyIdStr());
                // 复用配置范围
                setReuseRange(staff, code, wavePickingParam);
                Trade trade = waveQueryDao.querySingleItemTrade(staff, wavePickingParam, code.getSysItemId(), sysSkuId);
                if (trade == null) {
                    //唯一码复用失败
                    checkedUnboundReasonException(staff, code.getUniqueCode());
                }
                if (Objects.equals(DEFAULT_WAVE_ID, trade.getWaveId())) {
                    // 复用已生成唯一码的订单，无需还原waveId
                    cancel4Trade(staff, Lists.newArrayList(trade.getSid()), true, UnboundReasonTypeEnum.REUSE);
                } else {
                    List<Trade> trades = Lists.newArrayList(OrderUniqueCodeUtils.buildUpdateTrade(trade, WaveTypeEnum.ORDER_UNIQUE_CODE.getWaveId()));
                    tradeWaveService.updateTradeWaveId(staff,trades);
                    logisticsService.batchInsert(staff,trades, LogisticsWaveRelation.LogisticsType.TRADE, LogisticsWaveRelation.WaveTypeEnum.UNIQUE_CODE, LogisticsWaveRelation.WaveSubTypeEnum.ORDER_UNIQUE_CODE);
                }
                trade = tradeSearchService.queryBySid(staff, true, trade.getSid());
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);

                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(code.getId());
                update.setSid(trade.getSid());
                update.setOrderId(orders4Trade.get(0).getId());
                update.setCodeType(1);
                update.setUniqueCode(code.getUniqueCode());
                update.setShortSid(trade.getShortId());
                waveUniqueCodeDao.update(staff, update);

                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(update)), WaveUniqueOpType.ORDER_MULTIPLEXING, "", Objects.equals(code.getCodeType(), 3)}), null);
                return trade;
            }

            @Override
            public void checkSorting(Staff staff, Trade trade) {
                checkAndCreateSorting(staff, trade);
            }
        }.examine(staff, param);
    }

    private void checkAndCreateSorting(Staff staff, Trade trade) {
        if (trade == null) {
            return;
        }
        boolean open = orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
        // 订单唯一码拆分新模式，不需要wave_sorting
        if (open) {
            return;
        }
        WaveSorting waveSorting = waveSortingDao.queryBySid(staff, trade.getSid());
        if (waveSorting == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "补偿生成sorting信息，sid：" + trade.getSid()));
            }
            WavePicking wavePicking = new WavePicking();
            wavePicking.setEnableStatus(WavePicking.STATUS_CREATED);
            wavePicking.setWaveId(DEFAULT_WAVE_ID);
            orderUniqueCodeGenerateBusiness.createWaveSortings4Lock(staff, wavePicking, Lists.newArrayList(trade.getSid()), false);
        }
    }

    /**
     * 复用范围
     * 唯一码状态是【已收货】的解绑唯一码，执行重新绑定时，只能匹配【已审核，未打印，标签状态为未到货(等待收获、等待采购)】和【已审核，未打印，未生成标签】的订单
     * 唯一码状态是【已拣选】的解绑唯一码，执行重新绑定时，只能匹配【已审核，未打印，标签状态为等待拣选】和【已审核，未打印，未生成标签】的订单
     */
    private void setReuseRange(Staff staff, WaveUniqueCode code, WavePickingParam wavePickingParam) {
        OrderUniqueCodeConfig orderUniqueCodeConfig = orderUniqueCodeConfigService.get(staff);
        Integer reuseRange = orderUniqueCodeConfig.getReuseRange();
        if (Objects.equals(reuseRange, CommonConstants.JUDGE_YES)) {
            if (Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.RECIVED.getType())) {
                wavePickingParam.setReuseStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()));
            } else if (Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.PICKED.getType())) {
                wavePickingParam.setReuseStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.WAIT_PICK.getType()));
            } else {
                // 不是已收货，已拣选，按老逻辑来
                reuseRange = CommonConstants.JUDGE_NO;
            }
        }
        wavePickingParam.setReuseRange(reuseRange);
    }

    private WavePickingScanResult scanByUniqueCodeMulti(Staff staff, WavePickingParam param,
                                                        WaveUniqueCodeDao daoParam, WaveHelpBusiness waveHelpParam, ITradeSearchService tradeSearchParam, OrderUniqueCodeHelpBusiness helpBusiness, ITradeTraceService tradeTraceParam, WaveUniqueCodeLogDao uniqueCodeLogDao, IShopService shopParam) {
        return new AbstractOrderUniqueCodeExamineBusiness() {
            @Override
            public void initService() {
                setWaveUniqueCodeDao(daoParam);
                setWaveHelpBusiness(waveHelpParam);
                setTradeSearchService(tradeSearchParam);
                setOrderUniqueCodeHelpBusiness(helpBusiness);
                setTradeTraceService(tradeTraceParam);
                setWaveUniqueCodeLogDao(uniqueCodeLogDao);
                setShopService(shopParam);
            }

            @Override
            public void examineBefore(Staff staff, WavePickingParam param, WaveUniqueCode code) {
                //判断是否开启唯一码复用和已解绑
                if (Objects.equals(CommonConstants.VALUE_NO, getUniqueCodeMultiplex(param)) && !DataUtils.checkLongNotEmpty(code.getSid())) {
                    checkedUnboundReasonException(staff, param.getUniqueCode());
                }
                if (param.getSingleMode() != null && param.getSingleMode() == 1) {
                    throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON, "单件模式不支持多件订单！");
                }
            }

            @Override
            public void checkSorting(Staff staff, Trade trade) {
                checkAndCreateSorting(staff, trade);
            }

            @Override
            @Transactional
            public WavePickingScanResult doExamine(Staff staff, WaveUniqueCode code, Trade trade, WavePickingParam param) {

                WavePickingScanResult result = buildSeedResult(code);
                result.setTrade(trade);
                bigTradeGetWayBill(staff, trade, param);
                // 非提交验货，只返回唯一码、商品、订单信息
                if (!param.isSubmitExamine()) {
                    return result;
                }
                // 批量验货
                if (param.isNewStyle()) {
                    result.setTradeMatched(true);
                    result.setTradeAllMatched(true);
                    forceExamineUpdateSorting(staff, code);
                    updateUniqueCodeStatus(staff, queryByUniqueCodes(staff, param.getCurrentMatchedUniqueCodes()) , WaveSorting.MATCHED_STATUS_OVER, null);
                    return result;
                }

                updateUniqueCodeStatus(staff, queryCanExamines(staff, param.isOrderUniqueCodeForce(), code), WaveSorting.MATCHED_STATUS_OVER, null);
                // 判断是否所有唯一码都已验货
                if (param.isOrderUniqueCodeForce() || isAllMatch(staff, code)) {
                    result.setTradeMatched(true);
                    result.setTradeAllMatched(true);
                    Integer hybridScanType = orderUniqueCodeHelpBusiness.getHybridScanType(staff);
                    if (!param.isOrderUniqueCodeForce() && (Objects.equals(hybridScanType, 2) || Objects.equals(hybridScanType, 3))) {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, "单多混扫统计"));
                        }
                        result.setTradeMatched(false);
                        result.setTradeAllMatched(false);
                    }
                }
                // 更新分拣信息
                if (param.isOrderUniqueCodeForce()) {
                    forceExamineUpdateSorting(staff, code);
                    forceExamineReceive(staff, code,param);
                } else {
                    updateWaveSorting(staff, code, param);
                }
                return result;
            }

            @Override
            public Trade matchTradeAfter(Staff staff, WavePickingParam param, Trade trade) {
                return cancelTradeExcept(staff, param.getIgnoreExceptIds(), trade);
            }

            @Override
            public void validateTemplateInfo(Staff staff, WavePickingParam param, Trade trade, WaveUniqueCode code) {
                validateTemplate(staff,param,trade, code);
            }

            @Override
            public void examineAfter(Staff staff,List<WaveUniqueCode> waveUniqueCodes){}
        }.examine(staff, param);
    }

    /**
     * 大订单提前获取单号
     * @param staff
     * @param trade
     * @param param
     */
    private void bigTradeGetWayBill(Staff staff, Trade trade, WavePickingParam param) {
        if (trade == null || !DataUtils.checkLongNotEmpty(trade.getTemplateId())
                || trade.getTemplateType() == 0 || StringUtils.isNotEmpty(trade.getOutSid())) {
            return;
        }

        int getWayBillItemNumLimit = orderUniqueCodeHelpBusiness.getUniqueCodeGetWayBillItemNum();
        List<WaveUniqueCode> codes = OrderUniqueCodeUtils.filterFinalGenerates(queryBySid(staff, trade.getSid()));
        if (CollectionUtils.isEmpty(codes) || codes.size() <= getWayBillItemNumLimit || codes.stream().anyMatch(c -> Objects.equals(c.getMatchedStatus(), WaveSorting.MATCHED_STATUS_OVER))) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "大件订单，扫描第一件获取单号！"));
        }

        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
        Assert.notNull(userWlbExpressTemplate, "模板不存在！");
        getWaybillCode(staff, trade.getWarehouseId(), userWlbExpressTemplate, userWlbExpressTemplate.getWlbType(), Lists.newArrayList(trade), param.getClientIp());
    }

    @Override
    public void cancelTradeExcept(Staff staff, Trade trade) {
        cancelTradeExcept(staff, trade, false);
    }

    @Override
    public void cancelTradeExcept(Staff staff, Trade trade, boolean exclueRelationChanged) {
        if (!TradeExceptUtils.isExcept(staff, trade)) {
            return;
        }

        OrderUniqueCodeConfig config = queryConfig(staff);
        if (StringUtils.isEmpty(config.getIgnoreExceptInfo())) {
            return;
        }
        List<String> ignoreExcepts = getIgnoreExcepts(config.getIgnoreExceptInfo());
        if (exclueRelationChanged && ignoreExcepts.contains(EXCEP_ITEM_RELATION_MODIFIED)) {
            ignoreExcepts.removeIf(i -> i.equals(EXCEP_ITEM_RELATION_MODIFIED));
        }
        String ignores = StringUtils.join(ignoreExcepts, ",");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "已验货的唯一码开始取消订单异常，ignores：" + ignores));
        }

        cancelTradeExcept(staff, ignores, trade);
    }

    @Override
    public Trade cancelRelationChangedException(Staff staff, Trade trade) {
        Trade cancelExceptTrade = cancelTradeExcept(staff, EXCEP_ITEM_RELATION_MODIFIED, trade);
        if (cancelExceptTrade != null && cancelExceptTrade.getUserId() != null) {
            Shop shop = shopService.queryByUserId(staff, cancelExceptTrade.getUserId());
            if (shop != null) {
                cancelExceptTrade.setShopName(shop.getPriorityTitle());
            }
        }
        return cancelExceptTrade;
    }

    private Trade cancelTradeExcept(Staff staff, String ignoreExcepts, Trade trade) {
        if (StringUtils.isEmpty(ignoreExcepts) || trade == null) {
            return trade;
        }
        if (!TradeExceptUtils.isExcept(staff,trade)) {
            return trade;
        }
        // 订单系统异常
        Set<Integer> fixExcepts = TradeUtils.parseExcept(staff,trade);
        List<String> customExcepts = TradeExceptUtils.getCustomExceptIdsStr(staff, trade).stream().collect(toList());
        // 如果订单有退款异常需要单独取消
        boolean isRefund = refuseRefund(staff, ignoreExcepts, trade, fixExcepts);
        // 配置的可以忽略的异常
        OrderUniqueCodeUtils.IgnoreExcept ignoreExcept = OrderUniqueCodeUtils.transferIgnoreExcepts(ignoreExcepts);
        fixExcepts.retainAll(ignoreExcept.getFixExcepts());
        customExcepts.retainAll(ignoreExcept.getCustomExcepts());
        if (!CollectionUtils.isEmpty(fixExcepts) || !CollectionUtils.isEmpty(customExcepts)) {
            tradeServiceDubbo.cancelExcep4Wave(staff, OrderUniqueCodeUtils.getFixExceptString(fixExcepts), DataUtils.string2LongList(customExcepts), new Long[]{trade.getSid()});
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码取消异常，自定义异常：" + customExcepts + "，系统异常：" + fixExcepts));
            }
            return tradeSearchService.queryBySid(staff, true, trade.getSid());
        } else if (isRefund) {
            return tradeSearchService.queryBySid(staff, true, trade.getSid());
        }
        return trade;
    }

    private boolean refuseRefund(Staff staff, String ignoreExcepts, Trade trade, Set<Integer> fixExcepts) {
        if (CollectionUtils.isEmpty(fixExcepts) || !fixExcepts.contains(TradeConstants.IDX_REFUNDING)) {
            return false;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单拒绝退款：" + trade.getSid()));
        }
        List<String> ignoreExceptStr = Lists.newArrayList(Arrays.asList(ignoreExcepts.split(",")));
        if (ignoreExceptStr.contains(OrderUniqueCodeUtils.EXCEP_REFUND)) {
            tradeServiceDubbo.refuseRefund(staff, new Long[]{trade.getSid()});
            return true;
        }

        return false;
    }

    private void forceExamineReceive(Staff staff, WaveUniqueCode code,WavePickingParam param) {
        if (!DataUtils.checkLongNotEmpty(code.getSid())) {
            return;
        }
        List<WaveUniqueCode> codes = queryBySidAndDateNo(staff, code.getSid(), code.getDateNo(), code.getBatchNo());
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<String> currentMatchedUniqueCode = ArrayUtils.toStringList(param.getCurrentMatchedUniqueCodeStr());
        List<String> currentCode = currentMatchedUniqueCode==null?new ArrayList<>():currentMatchedUniqueCode;
        List<WaveUniqueCode> inspectionUniqueCodes = codes.stream().filter(u->currentCode.contains(u.getUniqueCode())).collect(toList());
        List<WaveUniqueCode> unInspectionUniqueCodes = codes.stream().filter(u->!currentCode.contains(u.getUniqueCode())).collect(toList());
        if(!CollectionUtils.isEmpty(inspectionUniqueCodes)) {// 强制收货已扫描日志
            String content = String.format("唯一码订单强制发货，扫描唯一码【%s】",inspectionUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), code.getSid(), "唯一码强制发货", staff.getName(), new Date(), content);
            waveWriteTradeTraceService.batchAddTradeTrace(staff, Arrays.asList(tradeTrace));
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(inspectionUniqueCodes), WaveUniqueOpType.ORDER_FORCE_INSPECTION}), null);
        }
        if(!CollectionUtils.isEmpty(unInspectionUniqueCodes)) {// 强制收货未扫描日志
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(unInspectionUniqueCodes), WaveUniqueOpType.ORDER_FORCE_UN_INSPECTION}), null);
        }
        ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
        uniqueCodeParams.setOpType(0);
        uniqueCodeParams.setUniqueCodeArr(codes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
        batchReceive(staff, uniqueCodeParams);
    }

    private void forceExamineUpdateSorting(Staff staff, WaveUniqueCode code) {
        if (!DataUtils.checkLongNotEmpty(code.getSid())) {
            return;
        }
        // 唯一码拆分新模式，不使用wave_sorting
        boolean open = orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
        if (open) {
            return;
        }
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySid(staff, 0L, code.getSid(), false);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        details.forEach(d -> {
            d.setMatchedNum(d.getItemNum());
            d.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
        });
        waveSortingDao.batchUpdateMatchedDetails(staff, details);

        WaveSorting waveSorting = buildMatchWaveSorting(details.get(0), -5L);
        waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
        waveSortingDao.updateStatus(staff, waveSorting);
    }

    private List<WaveUniqueCode> queryCanExamines(Staff staff, boolean isForce, WaveUniqueCode code) {
        if (!isForce) {
            return Lists.newArrayList(code);
        }
        List<WaveUniqueCode> codes = queryBySidAndDateNo(staff, code.getSid(), code.getDateNo(), code.getBatchNo());
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码强制发货，未查询到唯一码信息，sid：" + code.getSid()
                        + "，dateNo：" + code.getDateNo() + "，batchNo：" + code.getBatchNo()));
            }
            return Lists.newArrayList(code);
        }

        return filterCanExamineCodes(codes, false);
    }

    public static List<WaveUniqueCode> filterCanExamineCodes(List<WaveUniqueCode> codes, boolean reExamine) {
        if (CollectionUtils.isEmpty(codes)) {
            return codes;
        }
        return codes.stream().filter(c ->
                (reExamine ? Objects.equals(WaveSorting.MATCHED_STATUS_OVER, c.getMatchedStatus())
                        : Objects.equals(WaveSorting.MATCHED_STATUS_NOT, c.getMatchedStatus()))
                        && !Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), c.getStatus())
                        && !Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), c.getStatus())
                        && !Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), c.getStatus())
                        && !Objects.equals(3, c.getMatchedStatus())).collect(Collectors.toList());
    }

    @Override
    public void examineBatchUpdateWaveSorting(Staff staff, List<String> currentMatchedUniqueCodes) {
        examineBatchUpdateWaveSorting(staff, currentMatchedUniqueCodes, null);
    }

    @Override
    public void examineBatchUpdateWaveSorting(Staff staff, List<String> currentMatchedUniqueCodes, List<WaveUniqueCode> paramCodes) {
        if (CollectionUtils.isEmpty(currentMatchedUniqueCodes)) {
            return;
        }
        boolean open = orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
        // 唯一码拆分新模式，不使用wave_sorting
        if (open) {
            return;
        }
        List<WaveUniqueCode> codes = (CollectionUtils.isEmpty(paramCodes) ? queryByUniqueCodes(staff, currentMatchedUniqueCodes) : paramCodes);
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveSortingDetail> details = waveSortingDao.queryMatchedDetails(staff, 0L, codes.stream().map(WaveUniqueCode::getOrderId).collect(toList()), true, 0);
        if (CollectionUtils.isEmpty(details)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "多个唯一码批量验货，未查询到分拣信息！"));
            }
            return;
        }
        Map<Long, Long> matchedNumMap = codes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId, Collectors.counting()));
        List<WaveSortingDetail> updates = new ArrayList<>();
        for (WaveSortingDetail detail : details) {
            if (matchedNumMap.get(detail.getOrderId()) == null) {
                continue;
            }
            detail.setChangeMatchedNum(matchedNumMap.get(detail.getOrderId()).intValue());
            detail.setMatchedNum(detail.getMatchedNum() + detail.getChangeMatchedNum());
            detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
            if (detail.getMatchedNum().intValue() == detail.getItemNum()) {
                detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
            }
            updates.add(detail);
        }
        waveSortingDao.batchUpdateMatchedDetails(staff, updates);
        Integer count = waveSortingDao.queryNotMatchedDetailsCount(staff, details.get(0).getSortingId());
        WaveSorting waveSorting = buildMatchWaveSorting(details.get(0), DEFAULT_WAVE_ID);
        if (count == 0) {
            waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "订单唯一码全部验货完成，sid：" + codes.get(0).getSid()));
            }
        }
        waveSortingDao.updateStatus(staff, waveSorting);
    }

    private List<WaveUniqueCode> queryByUniqueCodes(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyList();
        }

        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        return queryOrderUniqueCodeByCondition(staff, params);
    }

    private void updateWaveSorting(Staff staff, WaveUniqueCode code, WavePickingParam param) {
        boolean open = orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
        if (open) {
            return;
        }
        WaveSortingDetail detail = waveSortingDao.queryOneMatchedDetailByOuterId(staff, 0L,
                code.getOuterId(), null, code.getOrderId(), true, 0, null);
        if (detail == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "多件订单验货出库，未查询到分拣信息！"));
            }
            return;
        }
        detail.setChangeMatchedNum(1);
        detail.setMatchedNum(detail.getMatchedNum() + detail.getChangeMatchedNum());
        detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);

        WaveSorting waveSorting = buildMatchWaveSorting(detail, param.getWaveId());
        if (detail.getMatchedNum().intValue() == detail.getItemNum()) {
            detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
            waveSortingDao.updateMatchedDetail(staff, detail);
            Integer count = waveSortingDao.queryNotMatchedDetailsCount(staff, detail.getSortingId());
            if (count == 0) {
                waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                logger.debug(LogHelper.buildLog(staff, String.format("所有分拣明细都已匹配完成，分拣号:%s,商家编码：%s,sid:%s, posNo:%s", detail.getSortingId(), code.getOuterId(), detail.getSid(), detail.getPositionNo())));
            }
        } else {
            waveSortingDao.updateMatchedDetail(staff, detail);
        }
        waveSortingDao.updateStatus(staff, waveSorting);
    }

    private static WaveSorting buildMatchWaveSorting(WaveSortingDetail detail, Long waveId) {
        WaveSorting waveSorting = new WaveSorting();
        waveSorting.setSid(detail.getSid());
        waveSorting.setId(detail.getSortingId());
        waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
        waveSorting.setWaveId(waveId);
        waveSorting.setPositionNo(detail.getPositionNo().intValue());
        return waveSorting;
    }

    @Override
    public boolean isAllMatch(Staff staff, WaveUniqueCode code) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(Lists.newArrayList(code.getSid()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "根据订单号：" + code.getSid() + "未查询到唯一码！");
        List<WaveUniqueCode> filters = filterCanExamineCodes(codes, false);
        if (CollectionUtils.isEmpty(filters)) {
            return true;
        }
        return false;
    }

    @Override
    public void updateUniqueCodeStatus(Staff staff, List<WaveUniqueCode> waveUniqueCodes, Integer matchedStatus, Integer postStatus) {
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return;
        }

        waveUniqueCodeDao.batchUpdate(staff, waveUniqueCodes.stream().map(code -> {
            WaveUniqueCode codeUpdate = new WaveUniqueCode();
            codeUpdate.setMatchedStatus(matchedStatus);
            codeUpdate.setId(code.getId());
            codeUpdate.setPostStatus(postStatus);
            return codeUpdate;
        }).collect(toList()));
    }

    private static WavePickingScanResult buildSeedResult(WaveUniqueCode code) {
        WavePickingScanResult result = new WavePickingScanResult();
        result.setSid(code.getSid());
        return result;
    }

    @Override
    public void writePrintLog(Staff staff, String uniqueCode, Long sid) {
        // 目前什么都不做
    }

    @Override
    @Transactional
    public void uniqueCodePrint(Staff staff, List<WaveUniqueCode> codes) {
        uniqueCodePrint(staff, codes, null);
    }

    @Override
    @Transactional
    public void uniqueCodePrint(Staff staff, List<WaveUniqueCode> codes, Supplier supplier) {
        uniqueCodePrint(staff, codes, supplier, null);
    }

    @Override
    @Transactional
    public void uniqueCodePrint(Staff staff, List<WaveUniqueCode> codes, Supplier supplier, UniqueCodeOperateSourceEnum sourceEnum) {
        uniqueCodePrint(staff, codes, supplier, sourceEnum, CommonConstants.VALUE_YES);
    }

    @Override
    @Transactional
    public void uniqueCodePrint(Staff staff, List<WaveUniqueCode> codes, Supplier supplier, UniqueCodeOperateSourceEnum sourceEnum, Integer needAutoReceive) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }

        // 处理爆款码
        List<WaveUniqueCode> uniqueCodes;
        if ((uniqueCodes = uniqueCodeExtendService.handleHotSaleCode4Print(staff, codes, sourceEnum)) != null) {
            if (CollectionUtils.isEmpty(uniqueCodes)) {
                // 没有爆款码对应的唯一码直接返回
                return;
            }
            codes = uniqueCodes;
        }

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码打印，唯一码数量：" + codes.size()
                    + "，供应商名称：" + (supplier == null ? "" : supplier.getName())));
        }
        List<WaveUniqueCode> needStatusLogs = new ArrayList<>();
        List<WaveUniqueCode> needPrintTimeTypeLogs = new ArrayList<>();
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        // 更新唯一码状态
        List<WaveUniqueCode> updates = Lists.newArrayListWithExpectedSize(codes.size());
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            // 只有等待采购/等待拣选 打印之后才可以改唯一码状态
            if (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus()) || (Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()) && OrderUniqueCodeUtils.isTrue(config.getPrintChangeStatus()))) {
                update.setStatus(Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()) ? OrderUniqueCodeStatusEnum.PICKED.getType() : OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType());
            }
            update.setPrintTime(code.getPrintTime());
            // 打印会把次数 + 1
            update.setPrintNum(code.getPrintNum());
            update.setCustomPrintTime(code.getCustomPrintTime());
            if (Objects.equals(update.getStatus(), OrderUniqueCodeStatusEnum.PICKED.getType())) {
                update.setStockPosition(UniqueCodeStockPositionEnum.PICK_AREA.getType());
            }
            update.setType(code.getType());
            updates.add(update);
            // 重复打印记录打印日志
            if (code.getPrintNum() != null && code.getPrintNum() > CommonConstants.ENABLE_STATUS_NORMARL) {
                needPrintTimeTypeLogs.add(update);
            } else {
                // 第一次打印记录日志
                needStatusLogs.add(code);
            }
        }

        Map<String, Integer> updateStatusMap = updates.stream().filter(u -> u.getStatus() != null).collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getStatus));
        uniqueCodePrintUpdate(staff, updates, codes);
        uniqueCodePrintWriteLog(staff, needStatusLogs, needPrintTimeTypeLogs, supplier, codes, sourceEnum, updateStatusMap);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.print").setArgs(new Object[]{staff, codes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList())}), null);
        sendOrderUniqueCodePickEvent(staff, config, codes);
        // 到货打印处理备货唯一码回调
        if (UniqueCodeOperateSourceEnum.ARRIVED_PRINT.equals(sourceEnum) && Objects.equals(CommonConstants.VALUE_YES, needAutoReceive)) {
            backUniqueCodePrintCallBack(staff, codes);
        }
        // 到货清点回调
        if (UniqueCodeOperateSourceEnum.ARRIVED_CHECK.equals(sourceEnum)) {
            uniqueCodeArrivedCheckBusiness.arrivedCheckPrintCallBack(staff, codes);
        }
        // 有货商品下架
        List<WaveUniqueCode> filter = codes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()) && (code.getPrintNum() != null && code.getPrintNum() == 1)).collect(toList());
        if (CollectionUtils.isEmpty(filter)) {
            return;
        }
        uniqueCodePrintPicked(staff, filter, OrderUniqueCodeUtils.isTrue(config.getPrintChangeStatus()), true);
    }

    public void sendOrderUniqueCodePickEvent(Staff staff, OrderUniqueCodeConfig config, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes) || !OrderUniqueCodeUtils.isTrue(config.getPrintChangeStatus())) {
            return;
        }

        // 发送唯一码拣选事件(等待拣选,加工中的订单唯一码)
        List<WaveUniqueCode> codeList = codes.stream().filter(code ->
                Objects.equals(code.getProcessStatus(), CommonConstants.VALUE_YES)
                        && Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(codeList)) {
            eventCenter.fireEvent(this, new EventInfo(EventNameConstants.ORDER_UNIQUE_CODE_PICK).setArgs(new Object[]{staff, codeList}), null);
        } else {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码打印,过滤唯一码为空,未发送唯一码拣选事件! "));
            }
        }
    }

    /**
     * 唯一码打印更新唯一码信息
     * @param staff
     * @param updates 需要更新的唯一码
     * @param codes
     */
    void uniqueCodePrintUpdate(Staff staff, List<WaveUniqueCode> updates, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(updates)) {
            return;
        }

        Map<String, Integer> codeOldStatusMap = codes.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getStatus, (a, b) -> a));
        for (WaveUniqueCode update : updates) {
            // 商品唯一码不校验
            // 第一次打印不校验
            if (Objects.equals(update.getType(), 2) || Objects.equals(update.getPrintNum(), 1)) {
                continue;
            }
            if (OrderUniqueCodeUtils.isReceivedPicked(codeOldStatusMap.get(update.getUniqueCode()))) {
                update.setCreated(null);
                update.setPrintTime(null);
                update.setCustomPrintTime(null);
            }
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
    }

    /**
     * 唯一码打印记录日志
     * @param staff
     * @param needStatusLogs 第一次打印的唯一码
     * @param needPrintTimeTypeLogs 多次打印的唯一码
     * @param supplier 打印的供应商
     * @param codes
     */
    private void uniqueCodePrintWriteLog(Staff staff, List<WaveUniqueCode> needStatusLogs, List<WaveUniqueCode> needPrintTimeTypeLogs, Supplier supplier, List<WaveUniqueCode> codes, UniqueCodeOperateSourceEnum sourceEnum, Map<String, Integer> updateStatusMap) {
        // 第一次打印日志
        if (!CollectionUtils.isEmpty(needStatusLogs)) {
            /**
             * 如果是到货打印，则需要同步记录日志，因为到货打印业务【打印】和【收货】是同时触发的
             * 为了保证打印日志在前，这里使用同步
             * 到缺货报表需要打印日志在收货日志之前
             */
            if (Objects.equals(UniqueCodeOperateSourceEnum.ARRIVED_PRINT, sourceEnum)) {
                waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, Lists.newArrayList(needStatusLogs), null, WaveUniqueOpType.PRINT, "到货打印唯一码"));
            } else {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, buildPrintLogs(needStatusLogs, supplier, sourceEnum, updateStatusMap), WaveUniqueOpType.PRINT}), null);
            }
        }

        if (CollectionUtils.isEmpty(needPrintTimeTypeLogs)) {
            return;
        }
        Map<String, Integer> codeOldStatusMap = codes.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getStatus, (a, b) -> a));
        // 收货前打印日志
        List<WaveUniqueCode> receivedPickedBeforeLogs = new ArrayList<>();
        // 收货后打印日志
        List<WaveUniqueCode> receivedPickedAfterLogs = new ArrayList<>();
        for (WaveUniqueCode code : needPrintTimeTypeLogs) {
            if (OrderUniqueCodeUtils.isReceivedPicked(codeOldStatusMap.get(code.getUniqueCode()))) {
                receivedPickedAfterLogs.add(code);
            } else {
                receivedPickedBeforeLogs.add(code);
            }
        }

        if (!CollectionUtils.isEmpty(receivedPickedBeforeLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, buildPrintLogs(receivedPickedBeforeLogs, supplier, sourceEnum, null), WaveUniqueOpType.PRINT_TIME_TYPE}), null);
        }
        if (!CollectionUtils.isEmpty(receivedPickedAfterLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, buildPrintLogs(receivedPickedAfterLogs, supplier, sourceEnum, null), WaveUniqueOpType.PRINT_AFTER_RECEIVED_PICKED}), null);
        }
    }

    private void uniqueCodePrintPicked(Staff staff, List<WaveUniqueCode> codes, boolean changePickStatus) {
        uniqueCodePrintPicked(staff, codes, changePickStatus, false);
    }

    private void uniqueCodePrintPicked(Staff staff, List<WaveUniqueCode> codes, boolean changePickStatus, boolean fromUniqueCodePrint) {
        if (!changePickStatus) {
            return;
        }

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "开始对唯一码进行拣选！"));
        }
        // 过滤掉加工状态为未加工中/完成、等待拣选的订单唯一码
        List<WaveUniqueCode> filterCodes = codes.stream().filter(code -> OrderUniqueCodeUtils.needChangeStockByProcessStatus(code)).collect(Collectors.toList());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "过滤出加工中/完成的订单唯一码" + codes.stream().filter(code -> !OrderUniqueCodeUtils.needChangeStockByProcessStatus(code)).collect(Collectors.toList())));
        }
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        //判断是否开启(PDA自由拣将货位库存转移至拣选暂存区)配置
        if (CommonConstants.VALUE_YES.equals(config.getPickNeedTransferStock())) {
            //不执行库存转移逻辑
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "开启了配置(通过PDA自由拣将货位库存转移至拣选暂存区),不执行库存转移逻辑！"));
            }
        } else {
            boolean openAsyncPick = orderUniqueCodeHelpBusiness.isOpenUniqueCodeAsyncPick(staff);
            if (openAsyncPick) {
                for (List<WaveUniqueCode> subCodes : Lists.partition(filterCodes, 1000)) {
                    eventCenter.fireEvent(this, new EventInfo("order.unique.code.pick").setArgs(new Object[]{staff, fromUniqueCodePrint, Lists.newArrayList(subCodes)}), null);
                }
            } else {
                doUniqueCodePick(staff, fromUniqueCodePrint, filterCodes);
            }
        }
    }

    @Override
    public void doUniqueCodePick(Staff staff, boolean fromUniqueCodePrint, List<WaveUniqueCode> filterCodes) {
        long start = System.currentTimeMillis();
        List<WaveUniqueCode> noAllocateRecordCodes = getNoAllocateRecordCodes(staff, filterCodes, fromUniqueCodePrint);
        Map<String, Long> codeMap = filterCodes.stream().collect(Collectors.groupingBy(f -> WmsKeyUtils.buildItemKey(f.getOrderId(), f.getGoodsSectionId()), Collectors.counting()));
        wmsService.uniqueCodePrint(staff, codeMap);
        // 无配货记录的唯一码拣选
        noAllocateRecordUniqueCodePrint(staff, noAllocateRecordCodes);

        if (logger.isInfoEnabled()){
            logger.info(LogHelper.buildLog(staff, "唯一码拣选完成耗时：" + (System.currentTimeMillis() - start)));
        }
    }

    void noAllocateRecordUniqueCodePrint(Staff staff, List<WaveUniqueCode> noAllocateRecordCodes) {
        if (CollectionUtils.isEmpty(noAllocateRecordCodes)) {
            return;
        }

        // 对唯一码进行配货，生成配货记录，不保存到数据库
        List<WmsChangeAffect> affects = WmsUtils.build4UniqueCodes(noAllocateRecordCodes, CommonConstants.JUDGE_YES);
        List<AllocateGoodsRecord> records = new ArrayList<>();
        reAllocateGoods4UniqueCode(staff, affects, records);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (AllocateGoodsRecord allocateGoodsRecord : records) {
            allocateGoodsRecord.setPickedNumInc(allocateGoodsRecord.getAllocatedNum());
            allocateGoodsRecord.setAllocateType(AllocateType.TRADE.getValue());
            allocateGoodsRecord.setPickedNum(0);
            allocateGoodsRecord.setWaveId(0L);
        }

        // 拣选下架
        for (List<AllocateGoodsRecord> subRecord : Lists.partition(records, 2000)) {
            wmsService.pickGoods(staff, subRecord);
        }
        modifyUniqueCodeGoodsSectionInfo(staff, records, noAllocateRecordCodes);
    }

    void modifyUniqueCodeGoodsSectionInfo(Staff staff, List<AllocateGoodsRecord> records, List<WaveUniqueCode> noAllocateRecordCodes) {
        if (CollectionUtils.isEmpty(records) || CollectionUtils.isEmpty(noAllocateRecordCodes)) {
            return;
        }

        // 配货是把唯一码Id当成sid，所以record根据sid是唯一的
        Map<Long, AllocateGoodsRecord> idRecordMap = records.stream().collect(Collectors.toMap(AllocateGoodsRecord::getSid, c -> c, (a, b) -> a));
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : noAllocateRecordCodes) {
            AllocateGoodsRecord record = idRecordMap.get(DataUtils.checkLongNotEmpty(code.getSid()) ? code.getSid() : code.getId());
            if (record == null) {
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setGoodsSectionId(record.getGoodsSectionId());
            update.setGoodsSectionCode(record.getGoodsSectionCode());
            code.setGoodsSectionCode(record.getGoodsSectionCode());
            updates.add(update);
        }
        batchUpdate(staff, updates);
    }

    /**
     * 得到没有配货记录的唯一码，这部分唯一码需要再次配货
     * @param staff
     * @param codes 等待拣选状态的唯一码
     * @param fromUniqueCodePrint
     * @return
     */
    List<WaveUniqueCode> getNoAllocateRecordCodes(Staff staff, List<WaveUniqueCode> codes, boolean fromUniqueCodePrint) {
        List<WaveUniqueCode> noAllocateRecordCodes = new ArrayList<>();
        // 如果来自唯一码打印，唯一码的信息是不完整的，需要重新查询
        if (fromUniqueCodePrint) {
            codes = queryCodeByIds(staff, codes);
        }
        // 解绑的唯一码
        noAllocateRecordCodes.addAll(codes.stream().filter(code -> !DataUtils.checkLongNotEmpty(code.getSid())).collect(toList()));
        Set<Long> sids = codes.stream().map(WaveUniqueCode::getSid).collect(toSet());
        if (CollectionUtils.isEmpty(sids)) {
            return noAllocateRecordCodes;
        }

        // 未配货的订单
        Set<Long> allocatedGoodsSids = getAllocatedGoodsSids(staff, sids);
        noAllocateRecordCodes.addAll(codes.stream().filter(code -> DataUtils.checkLongNotEmpty(code.getSid()) && !allocatedGoodsSids.contains(code.getSid())).collect(toList()));
        return noAllocateRecordCodes;
    }

    private List<WaveUniqueCode> queryCodeByIds(Staff staff, List<WaveUniqueCode> sourceCodes) {
        if (CollectionUtils.isEmpty(sourceCodes)) {
            return Collections.emptyList();
        }

        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodeIds(sourceCodes.stream().map(WaveUniqueCode::getId).collect(toList()));
        return waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
    }

    /**
     * 获取已配货的订单号
     * @param staff
     * @param sids
     * @return
     */
    private Set<Long> getAllocatedGoodsSids(Staff staff, Set<Long> sids) {
        Set<Long> allocatedGoodsSids = Sets.newHashSet();
        for (List<Long> subSids : Lists.partition(Lists.newArrayList(sids), 1000)) {
            List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder()
                    .sids(subSids).allocateType(AllocateType.TRADE.getValue())
                    .statusList(Lists.newArrayList(AllocateGoodsRecord.AllocateGoodsStatusEnum.UNPICKED.getValue(), AllocateGoodsRecord.AllocateGoodsStatusEnum.PICKED.getValue()))
                    .containerType(ContainerTypeEnum.GOODS_SECTION.getValue()).build());
            if (!CollectionUtils.isEmpty(records)) {
                allocatedGoodsSids.addAll(records.stream().map(AllocateGoodsRecord::getSid).collect(toSet()));
            }
        }
        return allocatedGoodsSids;
    }

    private static List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> buildPrintLogs(List<WaveUniqueCode> needPrintTimeTypeLogs, Supplier supplier, UniqueCodeOperateSourceEnum sourceEnum, Map<String, Integer> updateStatusMap) {
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> dtos = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(needPrintTimeTypeLogs);
        Map<Long, Date> codeCreateMap = needPrintTimeTypeLogs.stream().filter(c -> c.getCreated() != null).collect(Collectors.toMap(WaveUniqueCode::getId, WaveUniqueCode::getCreated, (v1, v2) -> v1));

        for (WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto : dtos) {
            Date created = codeCreateMap.get(dto.getId());
            if (created != null) {
                dto.setCreated(created);
            }

            if (supplier != null) {
                dto.setSupplier(supplier);
            }

            if (sourceEnum != null) {
                dto.setOperateSource(sourceEnum);
            }

            if ((Objects.equals(UniqueCodeOperateSourceEnum.ARRIVED_PRINT, sourceEnum) || Objects.equals(UniqueCodeOperateSourceEnum.ARRIVED_CHECK, sourceEnum)) && MapUtils.isNotEmpty(updateStatusMap)) {
                dto.setCurrentStatus(updateStatusMap.get(dto.getUniqueCode()));
            }
        }

        return dtos;
    }

    @Override
    @Transactional
    public void cancel4Trade(Staff staff, List<Long> sids, boolean includeMerge, UnboundReasonTypeEnum type) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<Trade> updateWaveIdTrades = new ArrayList<>();
        if (includeMerge) {
            // 合单 + 正常单
            List<Trade> trades = waveUseTradeServiceProxy.queryBySidsContainMergeTrade(staff, false, true, true, sids.toArray(new Long[0]));
            List<Long> sidList = TradeUtils.toSidList(trades);
            List<Trade> fullTrades = waveUseTradeServiceProxy.queryBySids(staff, true, sidList.toArray(new Long[0]));
            TradeUtils.toFullTradesOnlySuit(fullTrades, true);

            List<Order> orders4Trade = TradeUtils.getOrders4Trade(fullTrades);
            List<Long> orderIds = orders4Trade.stream().map(Order::getId).collect(toList());
            cancel4Order(staff, orderIds, type);
            // 删除分拣信息
            List<WaveSortingDetail> details = waveSortingDao.queryDetailsByOrderIds(staff, orderIds);
            for (Long sid : sids) {
                waveSortingDao.deleteSortingBySid(staff, sid, null);
            }
            if (!CollectionUtils.isEmpty(details)) {
                waveSortingDao.deleteDetailByIds(staff, details.stream().map(WaveSortingDetail::getId).collect(toList()));
            }
            updateWaveIdTrades.addAll(trades);
        } else {
            // 合单的隐藏单
            List<Trade> tbTrades = waveUseTradeServiceProxy.queryBySids(staff,true,sids.toArray(new Long[0]));
            if (CollectionUtils.isEmpty(tbTrades)) {
                return;
            }
            updateWaveIdTrades.addAll(tbTrades);
            List<Order> orders = TradeUtils.getOrders4Trade(tbTrades);
            cancel4Order(staff, orders.stream().map(Order::getId).collect(toList()), type);
        }
        // 修改订单波次号
        updateWaveIdByUnbound(staff, type, updateWaveIdTrades);
    }

    /**
     * 修改订单波次号
     */
    private void updateWaveIdByUnbound(Staff staff, UnboundReasonTypeEnum type, List<Trade> updateWaveIdTrades) {
        if (Objects.equals(type, UnboundReasonTypeEnum.REUSE)) {
            return;
        }
        List<Trade> filters = updateWaveIdTrades.stream().filter(t -> t.getWaveId() != null && t.getWaveId() != 0).collect(toList());
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }
        doAndRetry(staff, () -> {
                    tradeWaveService.updateRemoveWaveTrades(staff, filters);
                    return null;
                }
        );
    }

    public List<Trade> doAndRetry(Staff staff, Callable<List<Trade>> callable) {
        Retryer<List<Trade>> retryer = RetryerBuilder
                .<List<Trade>>newBuilder()
                // 递增间隔
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 20, TimeUnit.SECONDS))
                // 重试3次
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .retryIfException()
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        logger.warn(LogHelper.buildLog(staff, String.format("唯一码业务失败，进行第%s次重试", attempt.getAttemptNumber())));
                    }
                })
                .build();
        try {
            return retryer.call(callable);
        } catch (ExecutionException | RetryException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "唯一码业务重试失败"), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional
    public void cancel4Order(Staff staff, List<Long> orders) {
        cancel4Order(staff, orders, true, null);
    }

    @Override
    @Transactional
    public void cancel4Order(Staff staff, List<Long> orders, UnboundReasonTypeEnum typeEnum) {
        cancel4Order(staff, orders, true, typeEnum);
    }

    @Override
    @Transactional
    public void cancelConsign(Staff staff, List<Long> orders) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "接收到订单撤销发货事件，orderIds：" + Strings.join(",", orders)));
        }

        cancel4Order(staff, orders, UnboundReasonTypeEnum.ORDER_CANCEL_CONSIGN);
        List<TbOrder> orders1 = tbOrderDAO.queryByIds(staff, orders.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(orders1)) {
            return;
        }
        List<Long> sids = orders1.stream().map(TbOrder::getSid).collect(toList());
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        deleteSortingBySids(staff, sids);
        waveSortingDao.deleteDetailsBySids(staff, sids, 0L);
        // 撤销发货清除订单波次号
        clearTradeWaveIds(staff, sids);
    }

    /**
     * 撤销发货，交易关闭的订单清空订单波次号
     * 交易白名单功能-交易关闭的订单允许撤销发货
     * 波次白名单功能-波次拣货单打印取值新逻辑，订单数据从trade取，其逻辑包括要过滤掉撤销发货的订单
     * 因此撤销发货交易关闭的订单需要去掉订单波次号
     * @param staff
     * @param sids
     */
    private void clearTradeWaveIds(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> updateTrades = trades.stream().filter(t -> Trade.SYS_STATUS_CLOSED.equals(t.getSysStatus())).map(t -> OrderUniqueCodeUtils.buildUpdateTrade(t, 0L)).collect(toList());
        logger.debug("撤销发货交易关闭的订单清空订单波次号, sids:" + TradeUtils.toSidList(updateTrades));
        // TODO: 波次号分离任务发布后需要增加处理订单波次关联关系表
        if (!CollectionUtils.isEmpty(updateTrades)) {
            tradeServiceDubbo.updateTrades4Wave(staff, updateTrades);
        }
    }

    private void deleteSortingBySids(Staff staff, List<Long> sids) {
        List<WaveSorting> updates = new ArrayList<>();
        for (Long sid : sids) {
            WaveSorting update = new WaveSorting();
            update.setSid(sid);
            update.setEnableStatus(CommonConstants.ENABLE_STATUS_DELETED);
            updates.add(update);
        }
        if (CollectionUtils.isEmpty(updates)) {
            return;
        }
        waveSortingDao.batchUpdateStatus(staff, updates);
    }

    @Override
    @Transactional
    public void cancel4Order(Staff staff, List<Long> orderIds, boolean deleteRecord, UnboundReasonTypeEnum typeEnum) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("cancel4Order，typeEnum:【%s】", typeEnum)));
        }
        if (typeEnum != UnboundReasonTypeEnum.ORDER_CHANGE_ITEM && typeEnum != UnboundReasonTypeEnum.ORDER_PLAT_CHANGE_ITEM && typeEnum != UnboundReasonTypeEnum.RELATION_CHANGED_CHANGE_ITEM) {
            // 把套件本身转化为套件明细
            transferSuitDetails(staff, orderIds);
        }

        // 处理商品唯一码
        List<Long> itemUniqueCodeOrderIds = getItemUniqueCodeOrderIds(staff, orderIds);
        itemUniqueCodeExtendService.dealItemUniqueCodeCancel(staff, orderIds, typeEnum);

        // 处理订单唯一码
        List<Long> orderUniqueCodeOrderIds = orderIds.stream().filter(o -> !itemUniqueCodeOrderIds.contains(o)).collect(toList());
        if (!CollectionUtils.isEmpty(orderUniqueCodeOrderIds)) {
            dealUnPrinted(staff, orderUniqueCodeOrderIds, deleteRecord, typeEnum);
            dealPrinted(staff, orderUniqueCodeOrderIds, deleteRecord, typeEnum);
        }

        // 异步更新单多类型
        eventCenter.fireEvent(this, new EventInfo("unique.code.update.code.type").setArgs(new Object[]{staff, null, orderIds}), null);
    }

    /**
     * 把套件商品orderId，转化成套件明细的orderId
     * @param staff
     * @param orderIds
     * @return
     */
    @Override
    public List<Order> transferSuitDetails(Staff staff, List<Long> orderIds) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "套件单品转套件明细入参orderIds：" + orderIds));
        }

        // 获取套件本身子单 order
        List<TbOrder> suitSelfOrders = getSuitSelfOrders(staff, orderIds);
        if (CollectionUtils.isEmpty(suitSelfOrders)) {
            return new ArrayList<>();
        }
        // 查询对应的订单
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, suitSelfOrders.stream().map(TbOrder::getSid).distinct().collect(toList()).toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }

        // 添加套件明细子单Id
        List<Long> suitDetailOrderIds = new ArrayList<>();
        List<Order> suitDetailOrders = new ArrayList<>();
        Map<Long, Order> idOrderMap = TradeUtils.getOrders4Trade(trades).stream().collect(Collectors.toMap(Order::getId, a -> a, (a1, a2) -> a1));
        for (TbOrder suitSelfOrder : suitSelfOrders) {
            Order order = idOrderMap.get(suitSelfOrder.getId());
            if (order == null || CollectionUtils.isEmpty(order.getSuits())) {
                if (logger.isDebugEnabled() && order == null) {
                    logger.debug(LogHelper.buildLog(staff, "查询出的order为空, orderId: " + suitSelfOrder.getId()));
                }
                continue;
            }
            suitDetailOrderIds.addAll(order.getSuits().stream().map(Order::getId).collect(toList()));
            suitDetailOrders.addAll(order.getSuits());
        }
        orderIds.addAll(suitDetailOrderIds);

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "获取到套件明细orderIds：" + suitDetailOrderIds));
        }

        return suitDetailOrders;
    }

    /**
     * 把套件商品orderId，转化成套件明细的orderId,包含已删除的子订单
     */
    private List<TbOrder> transferSuitDetailsWithDeleted(Staff staff, Long fromOrderId) {
        List<TbOrder> fromOrders = tbOrderDAO.queryByKeys(staff, true, true, "order_" + staff.getDbInfo().getOrderDbNo(), null, "id", fromOrderId);
        // 获取套件本身
        List<Long> suiSelfOrderIds = fromOrders.stream().filter(order -> Objects.equals(order.getType(), Order.TypeOfCombineOrder) && Objects.equals(order.getCombineId(), 0L)).map(TbOrder::getId).collect(toList());
        List<Long> suiSelfSids = fromOrders.stream().filter(order -> Objects.equals(order.getType(), Order.TypeOfCombineOrder) && Objects.equals(order.getCombineId(), 0L)).map(TbOrder::getSid).collect(toList());
        // 过滤套件本身
        fromOrders = fromOrders.stream().filter(data -> !suiSelfOrderIds.contains(data.getId())).collect(toList());
        if (!CollectionUtils.isEmpty(suiSelfOrderIds)) {
            // 添加套件明细
            List<TbOrder> suiltSingleOrders = tbOrderDAO.queryByKeys(staff, true, true, "order_" + staff.getDbInfo().getOrderDbNo(), null, "sid", suiSelfSids.toArray(new Long[0]));
            suiltSingleOrders = suiltSingleOrders.stream().filter(s -> suiSelfOrderIds.contains(s.getCombineId())).collect(toList());;
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "发现套件明细orderIds：" + suiltSingleOrders.stream().map(TbOrder::getId).collect(toList())));
            }
            fromOrders = ArrayUtils.saveAddAll(fromOrders, suiltSingleOrders);
        }
        return fromOrders;
    }

    private List<TbOrder> getSuitSelfOrders(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }
        List<TbOrder> orders = tbOrderDAO.queryByIds(staff, orderIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(orders)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到套件子订单orderIds: " + orderIds));
            }
            return null;
        }

        // 获取套件本身
        List<TbOrder> suitSelfOrders = orders.stream().filter(order -> Objects.equals(order.getType(), Order.TypeOfCombineOrder) && Objects.equals(order.getCombineId(), 0L)).collect(toList());
        if (CollectionUtils.isEmpty(suitSelfOrders)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "查询出的子订单按条件过滤之后为空, combineIds" + orders.stream().map(TbOrder::getCombineId).collect(toList())));
            }
            return null;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "发现套件本身orderIds：" + suitSelfOrders.stream().map(TbOrder::getId).collect(toList())));
        }
        return suitSelfOrders;
    }

    private List<Long> getItemUniqueCodeOrderIds(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        List<WaveUniqueCode> codes = new ArrayList<>();
        for (List<Long> subIds : Lists.partition(orderIds, 2000)) {
            codes.addAll(waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, buildOrderIdsQueryParams(subIds)));
        }
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<WaveUniqueCode> itemUniqueCodes = codes.stream().filter(OrderUniqueCodeUtils::isItemUniqueCode).collect(toList());
        if (CollectionUtils.isEmpty(itemUniqueCodes)) {
            return Collections.emptyList();
        }
        return itemUniqueCodes.stream().filter(c -> DataUtils.checkLongNotEmpty(c.getOrderId())).map(WaveUniqueCode::getOrderId).distinct().collect(toList());
    }

    private static ItemUniqueCodeQueryParams buildOrderIdsQueryParams(List<Long> orderIds) {
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setOrderIds(orderIds);
        // 强制走order_id的索引
        params.setForceOrderIdIndex(true);
        // 减少填充加快速度
        params.setNeedCategroyAndCatName(false);
        params.setNeedRelationMsg(false);
        return params;
    }

    private static ItemUniqueCodeQueryParams buildSidsQueryParams(List<Long> sids) {
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setSids(sids.stream().map(String::valueOf).distinct().collect(toList()));
        // 减少填充加快速度
        params.setNeedCategroyAndCatName(false);
        params.setNeedRelationMsg(false);
        return params;
    }

    @Override
    public void fillAllUniqueCodes(Staff staff, WaveUniqueCode code) {
        if (code == null || StringUtils.isEmpty(code.getUniqueCode()) || !DataUtils.checkLongNotEmpty(code.getSid())) {
            return;
        }

        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        params.setSids(Lists.newArrayList(code.getSid()));
        List<WaveUniqueCode> tradeAllCodes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params));
        code.setAllUniqueCodes(tradeAllCodes);
    }

    @Override
    public List<String> getMatchedUniqueCodes(Staff staff, WaveUniqueCode code) {
        return getMatchedUniqueCodes(staff, code, false);
    }

    private List<String> getMatchedUniqueCodes(Staff staff, WaveUniqueCode code, boolean allowCancelReceive4HybridScan) {
        if (code == null || StringUtils.isEmpty(code.getUniqueCode()) || !DataUtils.checkLongNotEmpty(code.getSid())) {
            return Collections.emptyList();
        }

        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        if (!allowCancelReceive4HybridScan) {
            params.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        }
        params.setSids(Lists.newArrayList(code.getSid()));
        List<WaveUniqueCode> tradeAllCodes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params));
        if (CollectionUtils.isEmpty(tradeAllCodes)) {
            return Collections.emptyList();
        }

        // 当前扫描的唯一码验货状态修改
        for (WaveUniqueCode dbCode : tradeAllCodes) {
            if (Objects.equals(dbCode.getUniqueCode(), code.getUniqueCode())) {
                dbCode.setMatchedStatus(2);
            }
        }
        return tradeAllCodes.stream().filter(c -> Objects.equals(c.getMatchedStatus(), 2)).map(WaveUniqueCode::getUniqueCode).collect(toList());
    }

    @Override
    public void fillVisibleColumnUniqueCodes(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<String> visibleColumnFieldList = waveColumnConfService.getVisibleColumnFieldList(staff, POST_PRINT_PAGE_ID);
        if (!CollectionUtils.isEmpty(visibleColumnFieldList) && visibleColumnFieldList.contains(COLUMN_POSITION_STOCK_REGION)) {
            fillPositionInfo(staff, codes);
        }
    }

    @Override
    public Integer getHybridScanType(Staff staff) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (config == null || config.getExtConfigMap() == null) {
            return CommonConstants.VALUE_NO;
        }

        Integer type = (Integer) config.getExtConfigMap().get(OrderUniqueCodeExtConfigEnum.HYBRID_SCAN_TYPE.getKey());
        if (type == null) {
            return CommonConstants.VALUE_NO;
        }

        return type;
    }

    @Override
    public void fillWarehouseEntryCode(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(codes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
        params.setBusinessType(UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType());
        params.setPage(new Page(1, 10000));
        List<UniqueCodeRelation> uniqueCodeRelations = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        if (CollectionUtils.isEmpty(uniqueCodeRelations)) {
            return;
        }
        Map<String, String> relationMap = uniqueCodeRelations.stream().collect(toMap(UniqueCodeRelation::getUniqueCode, UniqueCodeRelation::getBusinessCode, (a, b) -> b));
        for (WaveUniqueCode code : codes) {
            if (StringUtils.isBlank(relationMap.get(code.getUniqueCode()))) {
                continue;
            }
            code.setWarehouseEntryCode(relationMap.get(code.getUniqueCode()));
        }
    }

    @Override
    public void fillTradeAllUniqueCode(Staff staff, String uniqueCode, WavePickingScanResult scanResult) {
        fillTradeAllUniqueCode(staff, uniqueCode, scanResult, null);
    }

    @Override
    public void fillTradeAllUniqueCode(Staff staff, String uniqueCode, WavePickingScanResult scanResult, WavePickingParam param) {
        if (uniqueCode == null || scanResult == null) {
            return;
        }

        // 判断唯一码是否存在
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, OrderUniqueCodeQueryParams.build(uniqueCode));
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        if (!DataUtils.checkLongNotEmpty(codes.get(0).getSid())) {
            scanResult.setTradeAllUniqueCodes(codes);
            return;
        }
        scanResult.setVoiceMulti(Objects.equals(codes.get(0).getCodeType(), 2) && OrderUniqueCodeUtils.isOpenHybridScan(getHybridScanType(staff)));

        // 查询该订单下的所有唯一码，除了已下架及已取消状态
        Long sid = codes.get(0).getSid();
        OrderUniqueCodeQueryParams params2 = new OrderUniqueCodeQueryParams();
        if (param == null || !param.isAllowCancelReceive4HybridScan()) {
            params2.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        }
        params2.setSids(Lists.newArrayList(sid));
        List<WaveUniqueCode> tradeAllCodes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params2));
        if (CollectionUtils.isEmpty(tradeAllCodes)) {
            return;
        }
        List<Trade> trades = (scanResult.getTrade() == null ? waveUseTradeServiceProxy.queryBySids(staff, true, sid) : Lists.newArrayList(scanResult.getTrade()));
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        if (scanResult.getTrade() == null) {
            Trade trade = trades.get(0);
            if (trade != null && trade.getUserId() != null) {
                Shop shop = shopService.queryByUserId(staff, trade.getUserId());
                if (shop != null) {
                    trade.setShopName(shop.getPriorityTitle());
                }
            }
            scanResult.setTrade(trade);
        }
        scanResult.setTradeAllUniqueCodes(tradeAllCodes);
        // 给唯一码填充价格
        List<Order> orders = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trades.get(0)), true);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<Long, String> orderMap = orders.stream().filter(order -> StringUtils.isNotEmpty(order.getPrice())).collect(Collectors.toMap(Order::getId, Order::getPrice));
        Map<Long, Boolean> orderRefundingMap = orders.stream().collect(Collectors.toMap(Order::getId, order -> OrderUniqueCodeUtils.isRefunding(order)));
        Map<Long, Boolean> orderSuitDetailMap = orders.stream().collect(Collectors.toMap(Order::getId, order -> OrderUniqueCodeUtils.isSuitDetail(order)));
        Map<Long, Order> orderModelMap = orders.stream().collect(toMap(Order::getId, a -> a, (a1, a2) -> a1));
        Map<Long, String> skuPropertiesNameMap = orders.stream().filter(order -> !ObjectUtils.isEmpty(order.getSkuPropertiesName()))
                .collect(Collectors.toMap(Order::getId, Order::getSkuPropertiesName, (a1, a2) -> a1));
        Map<String, String> platformPicPathMap = orders.stream().filter(order -> StringUtils.isNotEmpty(order.getPicPath())) // 根据SKU取订单中的平台图片
                .collect(Collectors.toMap(WmsKeyUtils::buildOrderKey, Order::getPicPath, (a1, a2) -> a1));
        for (WaveUniqueCode code : tradeAllCodes) {
            code.setBuyerNick(trades.get(0).getBuyerNick());
            if (orderModelMap.get(code.getOrderId()) == null) {
                continue;
            }
            code.setRelationChangedException(OrderUniqueCodeUtils.haveRelationChangedException(orderModelMap.get(code.getOrderId())));
            code.setSalePrice(orderMap.get(code.getOrderId()));
            code.setRefunding(orderRefundingMap.get(code.getOrderId()));
            code.setSuitDetail(BooleanUtils.toBooleanDefaultIfNull(orderSuitDetailMap.get(code.getOrderId()), false));
            code.setMergeTrade(TradeUtils.isMerge(trades.get(0)));
            code.setPlatformSkuPropertiesName(skuPropertiesNameMap.get(code.getOrderId())); // 平台规格
            fillPlatformPicPath(code, platformPicPathMap.get(WmsKeyUtils.buildWaveUniqueCodeItemKey(code)));
        }
        //填充非默认列配置字段
        fillVisibleColumnUniqueCodes(staff, tradeAllCodes);
    }

    @Override
    public void tradePrintAfter(Staff staff, List<Long> sids) {
        // 什么也不做，前置打印也不踢出
    }

    @Override
    @Transactional
    public void tradeConsignAfter(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<Trade> trades = waveUseTradeServiceProxy.queryBySidsContainMergeTrade(staff, false, true, true, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "订单发货未查询到：" + sids));
            }
            return;
        }
        List<Long> sidList = TradeUtils.toSidList(trades);
        if (CollectionUtils.isEmpty(sidList)) {
            return;
        }
        List<Trade> fullTrades = waveUseTradeServiceProxy.queryBySids(staff, true, sidList.toArray(new Long[0]));
        TradeUtils.toFullTradesOnlySuit(fullTrades, true);

        // 处理订单唯一码
        if (!CollectionUtils.isEmpty(fullTrades)) {
            List<Long> orderIds = getOrderIds(fullTrades);
            // 处理商品唯一码，更新唯一码状态为已出库
            dealItemUniqueCode(staff, orderIds);
            // 处理未打印唯一码
            dealUnPrinted(staff, orderIds, false, null);
            // 处理已打印唯一码
            consignDealPrinted(staff, orderIds);
        }
    }

    private static List<Long> getOrderIds(List<Trade> fullTrades) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(fullTrades);
        List<Long> orderIds = orders4Trade.stream().map(Order::getId).collect(toList());
        return orderIds;
    }

    private void dealItemUniqueCode(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单发货，处理商品唯一码，orderIds：" + orderIds));
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setOrderIds(orderIds);
        params.setType(2);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setStatus(OrderUniqueCodeStatusEnum.OUT.getType());
            updates.add(update);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes), WaveUniqueOpType.ITEM_OUT}), null);
    }

    private void consignDealPrinted(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单发货，处理已打印唯一码，orderIds：" + orderIds));
        }
        List<WaveUniqueCode> codes = queryPrintedUniqueCodes(staff, orderIds);
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        List<WaveUniqueCode> releaseLogs = new ArrayList<>();
        List<WaveUniqueCode> outLogs = new ArrayList<>();
        List<String> unboundUniqueCodes = new ArrayList<>();
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            if (!OrderUniqueCodeUtils.codeEnableStatus(code)
                    || Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())
                    || Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())) {
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            // 唯一码未匹配 && （等待收货 || 已拣选），唯一码与订单接触绑定
            if (OrderUniqueCodeUtils.canRelease(code) && Objects.equals(WaveSorting.MATCHED_STATUS_NOT, code.getMatchedStatus())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "唯一码：" + code.getUniqueCode() + "与原订单解绑，sid：" + code.getSid()));
                }
                OrderUniqueCodeUtils.buildRelease(update);
                unboundUniqueCodes.add(code.getUniqueCode());
                update.setUniqueCode(code.getUniqueCode());
                updates.add(update);
                releaseLogs.add(code);
                continue;
            }
            update.setMatchedStatus(3);
            Integer receiveStatus = code.getReceiveStatus();
            update.setStatus((Objects.equals(CommonConstants.JUDGE_YES, receiveStatus) || Objects.equals(CommonConstants.JUDGE_YES, code.getStockStatus()))
                    ? OrderUniqueCodeStatusEnum.OUT.getType()
                    : OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType());
            updates.add(update);
            outLogs.add(code);
        }
        //保存唯一码解绑记录
        saveUnboundLog(staff, releaseLogs, UnboundReasonTypeEnum.ORDER_SHIPPED.getType(), updates, unboundUniqueCodes);
        waveUniqueCodeDao.batchUpdate(staff, updates);
        if (!CollectionUtils.isEmpty(releaseLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(releaseLogs, UnboundReasonTypeEnum.ORDER_SHIPPED.getDesc()), WaveUniqueOpType.ORDER_RELEASE}), null);
            orderUniqueCodePushService.pushUniqueCodes(staff, releaseLogs.stream().map(WaveUniqueCode::getId).toArray(Long[]::new));
        }
        if (!CollectionUtils.isEmpty(outLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(outLogs), WaveUniqueOpType.FINISH}), null);
        }
    }

    private void dealPrinted(Staff staff, List<Long> orderIds, boolean deleteRecord, UnboundReasonTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单状态改变，处理已打印唯一码，orderIds：" + orderIds));
        }
        List<WaveUniqueCode> codes = queryPrintedUniqueCodes(staff, orderIds);
        List<WaveUniqueCode> updates = new ArrayList<>();
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到有效状态的已打印唯一码！"));
            }
            return;
        }
        List<WaveUniqueCode> releaseLogs = new ArrayList<>();
        List<String> unboundUniqueCodes = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            if (!OrderUniqueCodeUtils.codeEnableStatus(code)) {
                continue;
            }
            if (Objects.equals(UnboundReasonTypeEnum.ORDER_CANCEL_CONSIGN, typeEnum) || OrderUniqueCodeUtils.canRelease(code)) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "唯一码：" + code.getUniqueCode() + "与原订单解绑，sid：" + code.getSid()));
                }
                OrderUniqueCodeUtils.buildRelease(update);
                if(UnboundReasonTypeEnum.ORDER_TRANSACTION_CLOSED.equals(typeEnum)){
                    update.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(code, OrderUniqueCodeTagEnum.CLOSE.getId(), CommonConstants.VALUE_NO));
                }
                unboundUniqueCodes.add(code.getUniqueCode());
                update.setUniqueCode(code.getUniqueCode());
                updates.add(update);
                releaseLogs.add(code);
                continue;
            }

            // 修改唯一码匹配状态为3，表示订单状态变更，唯一码不需要验货
            update.setMatchedStatus(3);
            updates.add(update);
        }
        //保存唯一码解绑记录
        saveUnboundLog(staff, releaseLogs, typeEnum == null ? null : typeEnum.getType(), updates, unboundUniqueCodes);
        waveUniqueCodeDao.batchUpdate(staff, updates);
        // 删除配货记录
        if (deleteRecord) {
            wmsService.deleteAllocateGoodsRecordsByMultiIds(staff, null, orderIds, null);
        }
        // 删除分拣信息
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsByOrderIds(staff, orderIds);
        if (!CollectionUtils.isEmpty(details)) {
            waveSortingDao.deleteDetailByIds(staff, details.stream().map(WaveSortingDetail::getId).collect(toList()));
        }
        // 打日志
        if (!CollectionUtils.isEmpty(releaseLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(releaseLogs, typeEnum.getDesc()), WaveUniqueOpType.ORDER_RELEASE}), null);
            orderUniqueCodePushService.pushUniqueCodes(staff, releaseLogs.stream().map(WaveUniqueCode::getId).toArray(Long[]::new));
        }
    }

    private List<WaveUniqueCode> queryPrintedUniqueCodes(Staff staff, List<Long> orderIds) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setOrderIds(orderIds);
        params.setPrinted(1);
        return waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
    }

    private void dealUnPrinted(Staff staff, List<Long> orderIds, boolean deleteRecord, UnboundReasonTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单状态改变，处理未打印唯一码，orderIds：" + orderIds));
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setOrderIds(orderIds);
        params.setPrinted(0);
        //不查询已取消和已下架状态的唯一码
        params.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到有效状态的唯一码！"));
            }
            return;
        }
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setStatus(OrderUniqueCodeStatusEnum.CANCEL.getType());
            // 取消状态的唯一码一定解绑
            OrderUniqueCodeUtils.buildRelease(update);
            if(UnboundReasonTypeEnum.ORDER_TRANSACTION_CLOSED.equals(typeEnum)){
                update.setTagIds(OrderUniqueCodeUtils.incrementBuildTagIds(code, OrderUniqueCodeTagEnum.CLOSE.getId(), CommonConstants.VALUE_NO));
            }
            update.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            updates.add(update);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes, (typeEnum == null ? null : typeEnum.getDesc())), WaveUniqueOpType.ORDER_CANCEL_AUTO}), null);

        // 删除分拣信息
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsByOrderIds(staff, orderIds);
        if (!CollectionUtils.isEmpty(details)) {
            waveSortingDao.deleteDetailByIds(staff, details.stream().map(WaveSortingDetail::getId).collect(toList()));
        }
        // 推送唯一码
        codes.forEach(c -> c.setStatus(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        orderUniqueCodePushService.pushUniqueCodes(staff, codes);
        // 删除配货记录
        if (deleteRecord) {
            wmsService.deleteAllocateGoodsRecordsByMultiIds(staff, null, orderIds, null);
        }
    }

    @Override
    public Map<Long, String> queryPositionNoBySid(Staff staff, List<Long> sids) {
        List<WaveUniqueCode> waveUniqueCodes = queryPositionNoBySidCommon(staff, sids, false);
        Map<Long, String> res = Maps.newHashMap();
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return res;
        }
        for (WaveUniqueCode code : waveUniqueCodes) {
            if (!DataUtils.checkLongNotEmpty(code.getPositionNoId()) || res.containsKey(code.getSid())) {
                continue;
            }
            res.put(code.getSid(), code.getPositionNo());
        }
        return res;
    }

    @Override
    public Map<Long, WaveUniqueCode> queryPositionNoBySidNew(Staff staff, List<Long> sids) {
        List<WaveUniqueCode> waveUniqueCodes = queryPositionNoBySidCommon(staff, sids, true);
        Map<Long, WaveUniqueCode> res = Maps.newHashMap();
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return res;
        }
        for (WaveUniqueCode code : waveUniqueCodes) {
            if (!DataUtils.checkLongNotEmpty(code.getPositionNoId()) || res.containsKey(code.getSid())) {
                continue;
            }
            WaveUniqueCode codeCopy = new WaveUniqueCode();
            codeCopy.setPositionNo(code.getPositionNo());
            codeCopy.setPositionGroup(code.getPositionGroup());
            codeCopy.setPositionStockRegion(code.getPositionStockRegion());
            res.put(code.getSid(), codeCopy);
        }
        return res;
    }

    private List<WaveUniqueCode> queryPositionNoBySidCommon(Staff staff, List<Long> sids, boolean isNewMode) {
        List<WaveUniqueCode> waveUniqueCodes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(sids)) {
            return waveUniqueCodes;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(sids);
        waveUniqueCodes = OrderUniqueCodeUtils.filterFinalGenerates(waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params));
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return waveUniqueCodes;
        }
        if (isNewMode) {
            fillPositionNoInfo(staff, waveUniqueCodes);
        } else {
            formatPositionNo(staff, waveUniqueCodes, true);
        }
        return waveUniqueCodes;
    }

    @Override
    public List<Long> queryCancelOrderIds(Staff staff, Integer dateNo, Integer batchNo) {
        Assert.isNull(dateNo, "dateNo不能为空");
        Assert.isNull(batchNo, "batchNo不能为空");
        return waveUniqueCodeDao.queryCancelOrderIds(staff, dateNo, batchNo);
    }

    private WaveUniqueCode queryByUniqueCodeSimple(Staff staff, String uniqueCode) {
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, OrderUniqueCodeQueryParams.build(uniqueCode));
        Assert.notEmpty(codes, "唯一码不存在！");
        WaveUniqueCode code = codes.get(0);
        return code;
    }

    private OrderUniqueCodeConfig getConfig(Staff staff, OrderUniqueCodeConfig paramConfig) {
        if (paramConfig != null) {
            return paramConfig;
        }
        return orderUniqueCodeConfigService.get(staff);
    }

    @Override
    public Trade validateInterceptRemarks(Staff staff, List<String> uniqueCodes, String interceptRemarks, OrderUniqueCodeConfig uniqueCodeConfig, OrderUniqueCodeSplitParams params) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return null;
        }
        WaveUniqueCode code = queryByUniqueCodeSimple(staff, uniqueCodes.get(0));
        if (code == null || !DataUtils.checkLongNotEmpty(code.getSid())) {
            return null;
        }
        if (params != null && Objects.equals(code.getCodeType(), 2)) {
            params.setMultiTrade(true);
        }
        Trade trade = null;
        OrderUniqueCodeConfig config = getConfig(staff, uniqueCodeConfig);
        String splitInterceptTagIds = (MapUtils.isNotEmpty(config.getExtConfigMap()) ? (String) config.getExtConfigMap().get(OrderUniqueCodeExtConfigEnum.SPLIT_INTERCEPT_TAG_IDS.getKey()) : "");
        // 页面备注拦截
        if (StringUtils.isNotEmpty(interceptRemarks)
                // 订单标签拦截
                || StringUtils.isNotEmpty(config.getInterceptTagIds())
                // 订单拆分标签拦截
                || StringUtils.isNotEmpty(splitInterceptTagIds)) {
            long start = System.currentTimeMillis();
            List<Trade> trades = waveUseTradeServiceProxy.queryBySidsNoFilter(staff, true, code.getSid());
            Assert.notEmpty(trades, "根据订单号：" + code.getSid() + "未查询到订单信息！");
            trade = trades.get(0);
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, "查询订单耗时：" + (System.currentTimeMillis() - start)));
            }
        }
        OrderUniqueCodeUtils.interceptRemarks(interceptRemarks, trade);
        orderUniqueCodeHelpBusiness.interceptTradeTags(staff, config.getInterceptTagIds(), trade);
        orderUniqueCodeHelpBusiness.interceptTradeTags(staff, splitInterceptTagIds, trade, true);
        return trade;
    }

    @Override
    public OrderUniqueCodeConfig queryConfig(Staff staff) {
        return orderUniqueCodeConfigService.get(staff);
    }

    @Override
    public void saveUnboundLog(Staff staff, List<WaveUniqueCode> waveUniqueCodes, Integer reasonType, List<WaveUniqueCode> updates, List<String> unboundUniqueCodes) {
        // 只有绑定订单的才解绑
        waveUniqueCodes = waveUniqueCodes.stream().filter(w -> DataUtils.checkLongNotEmpty(w.getSid())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return;
        }
        List<OrderUniqueCodeUnboundLog> logs = Lists.newArrayListWithCapacity(waveUniqueCodes.size());
        for (WaveUniqueCode waveUniqueCode : waveUniqueCodes) {
            OrderUniqueCodeUnboundLog log = new OrderUniqueCodeUnboundLog();
            log.setCompanyId(staff.getCompanyId());
            log.setOrderId(waveUniqueCode.getOrderId());
            log.setSid(waveUniqueCode.getSid());
            log.setUniqueCode(waveUniqueCode.getUniqueCode());
            log.setReasonType(reasonType);
            log.setReason(UnboundReasonTypeEnum.getByType(reasonType));
            log.setPositionNoId(waveUniqueCode.getPositionNoId());
            log.setPositionNo(waveUniqueCode.getPositionNo());
            log.setGoodsSectionId(waveUniqueCode.getGoodsSectionId());
            log.setGoodsSectionCode(waveUniqueCode.getGoodsSectionCode());
            log.setPositionNoSort(waveUniqueCode.getPositionNoSort());
            if (reasonType == null) {
                Trade trade = tradeSearchService.queryBySid(staff, false, waveUniqueCode.getSid());
                if (CommonConstants.VALUE_YES.equals(trade.getIsCancel())) {
                    //订单作废
                    reasonType = UnboundReasonTypeEnum.ORDER_VOID.getType();
                } else {
                    switch (trade.getSysStatus()) {
                        case Trade.SYS_STATUS_WAIT_AUDIT:
                            //反审核
                            reasonType = UnboundReasonTypeEnum.ORDER_DE_AUDIT.getType();
                            break;
                        case Trade.SYS_STATUS_CLOSED:
                            //交易关闭
                            reasonType = UnboundReasonTypeEnum.ORDER_TRANSACTION_CLOSED.getType();
                            break;
                        case Trade.SYS_STATUS_FINISHED:
                            //交易完成
                            reasonType = UnboundReasonTypeEnum.ORDER_TRANSACTION_SUCCEEDED.getType();
                            break;
                        default:
                            continue;
                    }
                }
                log.setReasonType(reasonType);
                log.setReason(UnboundReasonTypeEnum.getByType(reasonType));
            }
            log.setEnableStatus(BaseConstants.IS_ENABLE);
            logs.add(log);
        }
        orderUniqueCodeUnboundLogDao.batchInsert(staff, logs);
        //填充解绑Id
        fillUniqueCodeUnboundId(staff,  updates, unboundUniqueCodes);
    }

    public void fillUniqueCodeUnboundId(Staff staff, List<WaveUniqueCode> updates, List<String> unboundUniqueCodes) {
        if (CollectionUtils.isEmpty(updates)) {
            return;
        }

        Map<String, Long> map = orderUniqueCodeUnboundLogDao.queryIdByLastUnbound(staff, unboundUniqueCodes);

        if (map.isEmpty()) {
            return;
        }

        for (WaveUniqueCode code : updates) {
            if (DataUtils.checkLongNotEmpty(map.get(code.getUniqueCode()))) {
                code.setUnboundLogId(map.get(code.getUniqueCode()));
            }
        }
    }

    public void initParams(Staff staff, OrderUniqueCodeQueryParams params) {
        //判断是否需要查询出所关联的订单下的所有的唯一码
        if (!CollectionUtils.isEmpty(params.getRelationCodes())) {
            OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeQueryParams();
            queryParams.setUniqueCodes(params.getRelationCodes());
            List<WaveUniqueCode> queryList = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, queryParams);

            List<String> allCode = new ArrayList<>();
            allCode.addAll(params.getRelationCodes());
            List<Long> sids = new ArrayList<>();
            for (WaveUniqueCode waveUniqueCode : queryList) {
                //判断此唯一码是否已解绑,未解绑不需要查询最近一次解绑记录
                if (DataUtils.checkLongNotEmpty(waveUniqueCode.getSid())) {
                    sids.add(waveUniqueCode.getSid());
                } else {
                    Long sid = orderUniqueCodeUnboundLogDao.getSidByLastTime(staff, waveUniqueCode.getUniqueCode());
                    if (DataUtils.checkLongNotEmpty(sid)) {
                        OrderUniqueCodeQueryParams orderUniqueCodeQueryParams = new OrderUniqueCodeQueryParams();
                        orderUniqueCodeQueryParams.setSids(Lists.newArrayList(sid));
                        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, orderUniqueCodeQueryParams);
                        allCode.addAll(waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
                    }
                    List<String> uniques = orderUniqueCodeUnboundLogDao.queryAllByUniqueCodeAndTime(staff, waveUniqueCode.getUniqueCode());
                    allCode.addAll(uniques);
                }
            }
            //未解绑唯一码需查询关联订单下所有唯一码
            if (!CollectionUtils.isEmpty(sids)) {
                OrderUniqueCodeQueryParams orderUniqueCodeQueryParams = new OrderUniqueCodeQueryParams();
                orderUniqueCodeQueryParams.setSids(Lists.newArrayList(sids));
                List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, orderUniqueCodeQueryParams);
                List<String> relationCodeList = waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).distinct().collect(toList());
                allCode.addAll(relationCodeList);
            }
            if (!CollectionUtils.isEmpty(params.getUniqueCodes())) {
                List<String> uniqueList = Stream.of(allCode, params.getUniqueCodes())
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList());
                params.setUniqueCodes(uniqueList);
            } else {
                params.setUniqueCodes(allCode);
            }
        }
        if (StringUtils.isNotEmpty(params.getPositionNo())) {
            params.setPositionNo(params.getPositionNo().replaceAll("-", ""));
        }

        if (!params.isCountUserPgl() && !params.isForceCreatedIndex() && isForceStatusIndex(params)) {
            params.setForceStatusIndex(true);
        }
    }

    private static boolean statusQueryPointCompany(Staff staff) {
        return Objects.equals(staff.getCompanyId(), 26083L) || Objects.equals(staff.getCompanyId(), 50172L) || Objects.equals(staff.getCompanyId(), 49909L);
    }

    /**
     * 针对小宅女的特殊处理，走唯一码状态索引
     * @param params
     * @return
     */
    private boolean isForceStatusIndex(OrderUniqueCodeQueryParams params) {
        if (CollectionUtils.isEmpty(params.getWarehouseIds()) || CollectionUtils.isEmpty(params.getStatusList())) {
            return false;
        }

        // 可以走状态索引的唯一码状态
        List<Integer> forceStatusList = new ArrayList<>();
        forceStatusList.add(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
        forceStatusList.add(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType());
        forceStatusList.add(OrderUniqueCodeStatusEnum.WAIT_PICK.getType());
        forceStatusList.add(OrderUniqueCodeStatusEnum.PICKED.getType());

        for (Integer status : params.getStatusList()) {
            if (!forceStatusList.contains(status)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 过滤唯一码订单（返回 未生成唯一码的sids+待采购和待收货的sids）
     */
    @Override
    public List<Long> filterOrderUniqueCodeTrades(Staff staff, OrderUniqueCodeFilterDTO orderUniqueCodeFilterDTO) {
        return null;
    }



    @Override
    public void doChangeItem(Staff staff, Long toOrderId, Long fromOrderId,UnboundReasonTypeEnum unboundReasonTypeEnum) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单执行换商品，回调唯一码修改，toOrderId：" + toOrderId + "，fromOrderId：" + fromOrderId));
        }
        if (!DataUtils.checkLongNotEmpty(toOrderId)
                || !DataUtils.checkLongNotEmpty(fromOrderId)) {
            return;
        }
        OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeQueryParams();

        List<TbOrder> fromOrders = transferSuitDetailsWithDeleted(staff, fromOrderId);
        List<Long> fromOrderIds = fromOrders.stream().map(TbOrder::getId).collect(toList());
        if (CollectionUtils.isEmpty(fromOrderIds)) {
            logger.debug(LogHelper.buildLog(staff, "fromOrderId 转换后为空"));
            return;
        }
        queryParams.setOrderIds(fromOrderIds);
        // 已下架的商品要支持换商品
        queryParams.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.OUT.getType(),
                OrderUniqueCodeStatusEnum.CANCEL.getType(),
                OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, queryParams);
        if (CollectionUtils.isEmpty(codes)) {
            logger.debug(LogHelper.buildLog(staff, "根据fromOrderId未查询到唯一码信息！"));
            return;
        }

        boolean open = orderUniqueCodeHelpBusiness.openOrderUniqueCodeNewSplit(staff);
        List<WaveSortingDetail> details = new ArrayList<>();
        if (!open) {
            // 更换前的商品唯一码解绑或者取消
            details = waveSortingDao.queryDetailsByOrderIds(staff, Lists.newArrayList(fromOrderId));
            Assert.notEmpty(details, "根据fromOrderId未查询到分拣信息！");
        }

        // 查询子订单信息,如果是商品是套件则转化为套件明细
        List<TbOrder> toOrders = transferSuitDetailsWithDeleted(staff, toOrderId);
        Assert.notEmpty(toOrders, "根据toOrderId未查询到子单！");
        // 如果套件替换套件，如果有明细在替换前后商品编码和数量一致，那么唯一码需要保留，不作废
        handleRepeatOrderCodes(staff, fromOrders, toOrders, codes);

        cancel4Order(staff, fromOrders.stream().map(TbOrder::getId).collect(toList()), unboundReasonTypeEnum);
        if (CollectionUtils.isEmpty(toOrders)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "换商品无需新增订单唯一码! "));
            }
            return;
        }

        if (!open) {
            // 重建新的分拣明细信息
            saveWaveSortingDetails(staff, details.get(0), toOrders);
        }
        // 创建新商品的唯一码
        List<WaveUniqueCode> inserts = new ArrayList<>();
        copyNewUniqueCode(staff, codes, inserts, toOrders);

        // 填充唯一码
        fillUniqueCode(staff, inserts);

        List<String> offShelfUniqueCodes = Lists.newArrayList();

        // 商品对应关系异常重设生成时间，并记录已下架唯一码
        if(Objects.equals(unboundReasonTypeEnum, UnboundReasonTypeEnum.RELATION_CHANGED_CHANGE_ITEM) && !CollectionUtils.isEmpty(inserts)) {
            inserts.forEach(insert -> insert.setCreated(new Date()));
            offShelfUniqueCodes = inserts.stream().filter(code -> Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.OFF_SHELF.getType())).map(WaveUniqueCode::getUniqueCode).collect(toList());
        }

        // 新唯一码配货
        Set<Long> supplierIds = uniqueCodeForceAllocateGoodsSupplierService.getSupplierIds(staff);
        reSetStockStatus(staff, inserts, supplierIds);

        // 已下架唯一码设置【已下架】
        if (!CollectionUtils.isEmpty(offShelfUniqueCodes)) {
            for (WaveUniqueCode insert : inserts) {
                if (offShelfUniqueCodes.contains(insert.getUniqueCode())) {
                    insert.setStatus(OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
                }
            }
        }

        // 供应商固定家里拿货
        orderUniqueCodeGenerateBusiness.supplierForeAllocateGoods(staff, inserts, supplierIds);
        waveUniqueCodeDao.batchInsert(staff, inserts);
        WaveUniqueOpType opType = WaveUniqueOpType.CHANGE_ITEM_GENERATE;
        if (Objects.equals(unboundReasonTypeEnum, UnboundReasonTypeEnum.RELATION_CHANGED_CHANGE_ITEM)) {
            opType = WaveUniqueOpType.RELATION_CHANGED_GENERATE;
        }
        // 外采唯一码自动生成唯一码采购单(先只处理商品标签页面的换商品)
        if (!CollectionUtils.isEmpty(inserts) && Objects.equals(unboundReasonTypeEnum, UnboundReasonTypeEnum.RELATION_CHANGED_CHANGE_ITEM)) {
            autoCreatePurchaseOrder(staff, inserts.stream().filter(code -> Objects.equals(code.getStockStatus(), 0)).map(WaveUniqueCode::getUniqueCode).collect(toList()));
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff,
                WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(inserts), opType}), null);
        orderUniqueCodePushService.pushUniqueCodes(staff, inserts);
    }

    /**
     * 处理商品重复的订单唯一码，不作废不取消，只更新orderId
     */
    private void handleRepeatOrderCodes(Staff staff, List<TbOrder> fromOrders, List<TbOrder> toOrders, List<WaveUniqueCode> codes) {
        Map<Long, Long> fromToOrderIdMap = Maps.newHashMap();
        List<TbOrder> repeatItemOrders = Lists.newArrayList();
        for (TbOrder fromOrder : fromOrders) {
            for (TbOrder toOrder : toOrders) {
                if (Objects.equals(WmsKeyUtils.buildOrderNumKey(fromOrder), WmsKeyUtils.buildOrderNumKey(toOrder)) && !Objects.equals(fromOrder.getId(),toOrder.getId())) {
                    fromToOrderIdMap.put(fromOrder.getId(), toOrder.getId());
                    repeatItemOrders.add(fromOrder);
                    repeatItemOrders.add(toOrder);
                }

            }
        }
        if (CollectionUtils.isEmpty(repeatItemOrders)) {
            return;
        }
        logger.debug(LogHelper.buildLog(staff, "套件明细替换前后商品编码和数量一致, 商家编码：" + repeatItemOrders.stream().map(TbOrder::getSysOuterId).collect(Collectors.joining())));
        fromOrders.removeIf(repeatItemOrders::contains);
        toOrders.removeIf(repeatItemOrders::contains);

        Set<Long> repeatOrderIds = fromToOrderIdMap.keySet();
        List<WaveUniqueCode> repeatOrderCodes = codes.stream().filter(code -> repeatOrderIds.contains(code.getOrderId())).collect(toList());
        if (!CollectionUtils.isEmpty(repeatOrderCodes)) {
            List<WaveUniqueCode> updates = Lists.newArrayList();
            for (WaveUniqueCode code : repeatOrderCodes) {
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(code.getId());
                update.setOrderId(fromToOrderIdMap.get(code.getOrderId()));
                updates.add(update);
            }
            waveUniqueCodeDao.batchUpdate(staff, updates);
        }
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsByOrderIds(staff, Lists.newArrayList(repeatOrderIds));
        if (!CollectionUtils.isEmpty(details)) {
            List<WaveSortingDetail> updates = Lists.newArrayList();
            for (WaveSortingDetail detail : details) {
                WaveSortingDetail update = new WaveSortingDetail();
                update.setId(detail.getId());
                update.setOrderId(fromToOrderIdMap.get(detail.getOrderId()));
                updates.add(update);
            }
            waveSortingDao.batchUpdateDetails(staff, updates);
        }

        wmsService.updateAllocateGoodsRecordsOrderId(staff, fromToOrderIdMap);
    }

    private void saveWaveSortingDetails(Staff staff, WaveSortingDetail detail, List<TbOrder> orders) {
        if (CollectionUtils.isEmpty(orders) || detail == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "执行换商品时保存WaveSortingDetail失败! "));
            }
            return;
        }

        List<WaveSortingDetail> details = new ArrayList<>();
        for (Order order : orders) {
            WaveSortingDetail newDetail = new WaveSortingDetail();
            BeanUtils.copyProperties(detail, newDetail);
            newDetail.setOrderId(order.getId());
            newDetail.setSysItemId(order.getItemSysId());
            newDetail.setSysSkuId(DataUtils.getZeroIfDefault(order.getSkuSysId()));
            newDetail.setOuterId(order.getSysOuterId());
            details.add(newDetail);
        }

        waveSortingDao.batchInsertDetails(staff, details);
    }

    private Supplier queryBestSupplier(Staff staff, Order order) {
        Map<String, ItemSupplierBridge> bestSupplierMap = waveHelpBusiness.getBestSupplierMap(staff, Lists.newArrayList(Pair.of(order.getItemSysId(), DataUtils.getZeroIfDefault(order.getSkuSysId()))), true);
        if (MapUtils.isEmpty(bestSupplierMap)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到商品关联的供应商，sysItemId：" + order.getItemSysId() + "，sysSkuId：" + order.getSkuId()));
            }
            return null;
        }

        String itemKey = WmsKeyUtils.buildItemKey(order.getItemSysId(), order.getSkuSysId());
        ItemSupplierBridge itemSupplierBridge = bestSupplierMap.get(itemKey);
        if (itemSupplierBridge != null) {
            return supplierService.queryById(staff, itemSupplierBridge.getSupplierId());
        }
        return null;
    }

    private void fillUniqueCode(Staff staff, List<WaveUniqueCode> inserts) {
        String[] uniqueCodes = xSerialNumberService.nextIds(staff.getCompanyId(), BUSINESS_NAME, inserts.size());
        // 自定义首字母
        Integer customFirstDigitNum = OrderUniqueCodeUtils.getCustomFirstDigitNum(orderUniqueCodeConfigService.get(staff));
        for (int i = 0, size = inserts.size(); i < size; i++) {
            inserts.get(i).setUniqueCode(OrderUniqueCodeUtils.fillUniqueCodeZero(uniqueCodes[i], null, customFirstDigitNum));
        }
    }

    private void copyNewUniqueCode(Staff staff, List<WaveUniqueCode> codes, List<WaveUniqueCode> inserts, List<TbOrder> orders) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        for (Order order : orders) {
            for (int i = 0; i < order.getNum(); i++) {
                WaveUniqueCode insert = new WaveUniqueCode();
                BeanUtils.copyProperties(codes.get(0), insert);
                insert.setCreated(codes.get(0).getCreated());
                insert.setTagIds(null);
                insert.setPrintNum(0);
                insert.setPrintTime(null);
                // 换商品生成的唯一码，创建时间与原唯一码相同
                insert.setCreated(codes.get(0).getCreated());
                insert.setSysItemId(order.getItemSysId());
                insert.setSysSkuId(DataUtils.getZeroIfDefault(order.getSkuSysId()));
                insert.setOrderId(order.getId());
                insert.setOuterId(order.getSysOuterId());
                // 填充供应商与供应商分类
                insert.setSupplierId(0L);
                insert.setSupplierName("");
                insert.setSupplierCategory("");
                Supplier supplier = queryBestSupplier(staff, order);
                if (supplier != null) {
                    insert.setSupplierId(supplier.getId());
                    insert.setSupplierName(supplier.getName());
                    insert.setSupplierCategory(supplier.getCategoryName());
                }
                // 自选打印时间设置为默认时间
                insert.setCustomPrintTime(DataUtils.DEFAULT_DATE);
                // 清空采购单
                insert.setBusinessId(null);
                insert.setBusinessCode("");
                // 商品已下架设置唯一码下架
                insert.setStatus(OrderUniqueCodeStatusEnum.DEFAULT.getType());
                Map<String, DmjItem> dmjItemMap = waveHelpBusiness.queryItemMap(staff, Lists.newArrayList(order.getItemSysId()), Lists.newArrayList(DataUtils.getZeroIfDefault(order.getSkuSysId())));
                Map<String, DmjItemExtend> dmjItemExtMap = waveHelpBusiness.queryItemExtendMap(staff, Lists.newArrayList(order.getItemSysId()), Lists.newArrayList(DataUtils.getZeroIfDefault(order.getSkuSysId())));
                DmjItem dmjItem = dmjItemMap.get(WmsKeyUtils.buildItemKey(order.getItemSysId(), DataUtils.getZeroIfDefault(order.getSkuSysId())));
                if (dmjItem != null && config != null) {
                    Set<Long> offShelfAllShops = OrderUniqueCodeUtils.getOffShelfShopUserIds(config, CommonConstants.JUDGE_YES);
                    // 商品档案中标记下架 && 店铺配置【下架扫描标签同SKU全部未收标签】，对应店铺的订单生成【已下架】
                    if (Objects.equals(dmjItem.getGoodsStatus(), CommonConstants.JUDGE_NO)
                            && OrderUniqueCodeUtils.isOffAllByUserId(config, offShelfAllShops, order.getUserId())) {
                        insert.setStatus(OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
                        // 下架时间取商品档案
                        insert.setOffTime(waveHelpBusiness.getItemOffTime(dmjItemExtMap.get(WmsKeyUtils.buildItemKey(dmjItem))));
                    }
                }
                inserts.add(insert);
            }
        }

        // 如果订单之前是单件，更换成套件，则唯一码类型要修改成多
        if (!CollectionUtils.isEmpty(inserts) && inserts.size() > 1) {
            inserts.forEach(insert -> insert.setCodeType(WaveUniqueCode.CodeTypeEnum.MULTI.getValue()));
            fillPostionNoInfo(staff, inserts.stream().filter(t -> StringUtils.isEmpty(t.getPositionNo())).collect(Collectors.toList()));
        }
    }

    private void fillPostionNoInfo(Staff staff, List<WaveUniqueCode> inserts) {
        if (CollectionUtils.isEmpty(inserts)) {
            return;
        }

        OrderUniqueCodeConfig config = queryConfig(staff);
        if (config != null && Objects.equals(config.getPositionNoWay(), CommonConstants.JUDGE_NO)) {
            List<WaveItem> waveItems = orderUniqueCodeArrivedPrintBusiness.getAllocatePositionNoByPrint(staff, config, inserts);
            Map<Long, WaveItem> order2WaveItemMap = waveItems.stream().collect(Collectors.toMap(WaveItem::getOrderId, Function.identity()));
            for (WaveUniqueCode waveUniqueCode : inserts) {
                WaveItem waveItem = order2WaveItemMap.get(waveUniqueCode.getOrderId());
                if (waveItem == null) {
                    continue;
                }

                if (waveItem.getPositionNoId() == null) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(waveItem.getOuterId() + "未分配到分拣货位,原因分拣货位不足");
                    }
                    continue;
                }
                waveUniqueCode.setPositionNo(waveItem.getPositionNo());
                waveUniqueCode.setPositionNoId(waveItem.getPositionNoId());
                waveUniqueCode.setWarehouseId(waveUniqueCode.getWarehouseId());
            }
            // 设置分拣货位序号
            orderUniqueCodeHelpBusiness.fillPositionNoSort(staff, inserts);
        }
    }

    @Override
    public void saveOrderUniqueScanRecord(Staff staff, Long sid, WavePickingParam param) {
        eventCenter.fireEvent(this, new EventInfo("unique.code.scan.record").setArgs(new Object[]{staff, sid, param}), null);
    }

    @Override
    public Map<Long, Long> queryActiveUniqueCodeMap(Staff staff, List<Long> sids) {
        return waveUniqueCodeDao.queryActiveUniqueCodeMap(staff, sids);
    }

    @Override
    @Transactional
    public void batchUpdate(Staff staff, List<WaveUniqueCode> codes) {
        waveUniqueCodeDao.batchUpdate(staff, codes);
    }

    @Override
    public Map<String, Object> orderUniqueCodePick(Staff staff, String uniqueCode) {
        Assert.notNull(uniqueCode, "唯一码不能为空!");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "pda唯一码分拣：" + uniqueCode));
        }
        Map<String, Object> result = Maps.newHashMap();
        WaveUniqueCode waveUniqueCode = new WaveUniqueCode();
        boolean allPick = false;
        try {
            waveUniqueCode = uniqueCodeHelpBusiness.queryUniqueCodeInfo(staff, new UniqueCodeInfoQueryParams().setNeedExtendMsg(true).setQueryType(1).setOriginCode(uniqueCode)).getUniqueCode();
            if (waveUniqueCode == null) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "唯一码不存在!");
            }
            if (!DataUtils.checkLongNotEmpty(waveUniqueCode.getSid())) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "无关联订单，无需分拣!");
            }
            if (Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), waveUniqueCode.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), waveUniqueCode.getStatus())) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "唯一码已出库，无需分拣!");
            }
            if (Objects.equals(waveUniqueCode.getType(), 1) && StringUtils.isEmpty(waveUniqueCode.getPositionNo())) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "关联订单为单件订单，无需分拣!");
            }
            if (Objects.equals(waveUniqueCode.getType(), 2) && !Objects.equals(waveUniqueCode.getCodeType(), 2)) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "关联订单为单件订单，无需分拣!");
            }
            if (Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), waveUniqueCode.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), waveUniqueCode.getStatus())) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "唯一码已取消或已下架!");
            }
            WmsPerformanceMiddle queryMiddle = new WmsPerformanceMiddle();
            queryMiddle.setBusiCode(uniqueCode);
            queryMiddle.setTypeEnum(WmsPerformanceMiddleType.UNIQUE_CODE_SCAN_PICK);
            Long count = wmsPerformanceMiddleDao.count(staff, queryMiddle);
            if (count != null && count > 0) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(waveUniqueCode)), WaveUniqueOpType.SCAN_PICK}), null);
                throw new WaveScanException(WaveScanException.ERROR_CODE_OTHER, "唯一码已分拣!");
            }
            allPick = fillPositionNo4UniqueCodePick(staff, waveUniqueCode);
            waveUniqueCode.setPickStatus(CommonConstants.VALUE_YES);
            // 避免分配分拣货位后分拣id被覆盖，导致第二次分配分拣货位再次被分配
            waveUniqueCode.setPositionNoId(null);
            waveUniqueCodeDao.update(staff, waveUniqueCode);
            recordPerformance(staff, waveUniqueCode);
            waveUniqueCode.setAllPick(allPick ? 1 : 0);
        } catch (WaveScanException e) {
            result.put("errMsg", e.getErrMsg());
            result.put("errCode", e.getErrCode());
        }
        result.put("data", waveUniqueCode);
        return result;
    }

    private boolean fillPositionNo4UniqueCodePick(Staff staff, WaveUniqueCode waveUniqueCode) {
        // 是否全部到齐(在库)
        boolean allPick = false;
        if (!Objects.equals(waveUniqueCode.getType(), 2) || !DataUtils.checkLongNotEmpty(waveUniqueCode.getSid())) {
            return allPick;
        }
        // 订单里的所有唯一码
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, new ItemUniqueCodeQueryParams().setNeedCategroyAndCatName(false).setNeedRelationMsg(false).setType(2).setSids(Lists.newArrayList(String.valueOf(waveUniqueCode.getSid()))));
        if (CollectionUtils.isEmpty(codes)) {
            return allPick;
        }
        allPick = codes.stream().allMatch(c -> Objects.equals(c.getStatus(), OrderUniqueCodeStatusEnum.IN.getType()));
        // 没有分拣货位的
        List<String> noPositionCodes = codes.stream().filter(c -> !DataUtils.checkLongNotEmpty(c.getPositionNoId())).map(WaveUniqueCode::getUniqueCode).distinct().collect(toList());
        if (CollectionUtils.isEmpty(noPositionCodes)) {
            return allPick;
        }
        // 分配分拣货位
        BeforeArrivedCheckVO vo = crossBorderBusiness.allocatePositionNo(staff, new UniqueCodeArrivedCheckParams().setPrintUniqueCodes(noPositionCodes));
        if (!CollectionUtils.isEmpty(vo.getFailUniqueCodes())) {
            throw new WaveScanException(WaveScanException.ALLOCATE_POSITION_NO_FAIL, "唯一码未分配到分拣货位, 原因分拣货位不足");
        }
        List<WaveUniqueCode> successUniqueCodes = vo.getSuccessUniqueCodes();
        if (CollectionUtils.isEmpty(successUniqueCodes)) {
            return allPick;
        }
        String positionNo = successUniqueCodes.stream().collect(toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getPositionNo, (a, b) -> a)).get(waveUniqueCode.getUniqueCode());
        if (StringUtils.isEmpty(positionNo)) {
            return allPick;
        }
        // 前端展示
        waveUniqueCode.setPositionNo(positionNo);
        return allPick;
    }

    private void recordPerformance(Staff staff, WaveUniqueCode waveUniqueCode){
        WmsPerformanceMiddle wmsPerformanceMiddle = new WmsPerformanceMiddle();
        wmsPerformanceMiddle.setCompanyId(staff.getCompanyId());
        wmsPerformanceMiddle.setStaffId(staff.getId());
        wmsPerformanceMiddle.setStaffName(staff.getName());
        wmsPerformanceMiddle.setBusiCode(waveUniqueCode.getUniqueCode());
        wmsPerformanceMiddle.setWarehouseId(waveUniqueCode.getWarehouseId());
        wmsPerformanceMiddle.setSysItemId(waveUniqueCode.getSysItemId());
        wmsPerformanceMiddle.setSysSkuId(waveUniqueCode.getSysSkuId());
        wmsPerformanceMiddle.setNum(1);
        if (StringUtils.isNotEmpty(waveUniqueCode.getOuterId())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("outerId", waveUniqueCode.getOuterId());
            wmsPerformanceMiddle.setInfoJson(jsonObject.toJSONString());
        }
        wmsPerformanceMiddle.setOperateTime(new Date());
        wmsPerformanceMiddle.setTypeEnum(WmsPerformanceMiddleType.UNIQUE_CODE_SCAN_PICK);
        wmsPerformanceMiddleDao.insert(staff, wmsPerformanceMiddle);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(waveUniqueCode)), WaveUniqueOpType.SCAN_PICK}), null);
    }

    private void getWaybillCode(Staff staff, Long warehouseId, UserWlbExpressTemplate userWlbExpressTemplate, Integer wlbTemplateType, List<Trade> trades, String clientIP) {
        List<Trade> filter = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            if (StringUtils.isEmpty(trade.getOutSid())) {
                filter.add(trade);
            }
        }
        if (filter.isEmpty()) {
            return;
        }
        WlbStatus wlbStatus;
        WlbRequestGet wlbRequestGet = new WlbRequestGet(warehouseId, userWlbExpressTemplate.getId(), TradeUtils.toSids(filter));
        wlbRequestGet.setWlbType(wlbTemplateType);
        wlbRequestGet.setClientIp(clientIP);
        wlbStatus = waybillPrintServiceDubbo.getWaybillCodeGroupByUserId(staff, userWlbExpressTemplate, wlbRequestGet);

        if (StringUtils.isNotEmpty(wlbStatus.getErrorMessage())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_GET_WAYBILL, "获取电子面单号失败！原因：" + wlbStatus.getErrorMessage());
        }
        List<WlbResult> successResult = (List<WlbResult>) wlbStatus.getSuccessResult();
        getWayBillAndSendProxy(staff, this, wlbRequestGet, successResult, trades);
    }

    private void getWayBillAndSendProxy(Staff staff, Object source, WlbRequestGet wlbRequestGet, List<WlbResult> successResult, List<Trade> trades) {
        if (CollectionUtils.isEmpty(successResult)) {
            return;
        }
        try {
            PtWaybillPathContext ptWaybillPathContext = PtWaybillPathContext.builder()
                    .staff(staff)
                    .pathParam(PathParam.builder().sids(wlbRequestGet.getSidArr()).templateId(wlbRequestGet.getTemplateId()).build())
                    .pathResult(PathResult.builder().successResults(successResult.stream().map(wlbResult -> new ResultInfo(Long.valueOf(wlbResult.getSid()), wlbResult.getOutSid())).collect(Collectors.toList())).build())
                    .waybillPath(AllocateGetPathEnum.GENERATE_ORDER_UNIQUE_CODE_GET)
                    .sidAndRuleMap(trades.stream()
                            .filter(trade -> Objects.nonNull(trade.getWaybillUsingRule()))
                            .collect(Collectors.toMap(Trade::getSid, Trade::getWaybillUsingRule, (t1, t2) -> t1)))
                    .build();
            eventCenter.fireEvent(source, new EventInfo("waybill.cycle.modify").setArgs(new Object[]{staff, ptWaybillPathContext.getWaybillPath().getName(), ptWaybillPathContext}), null);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "waybill.cycle.modify事件构建运单生命周期上下文失败！" + AllocateGetPathEnum.GENERATE_ORDER_UNIQUE_CODE_GET.getName()), e);
        }
    }
    @Override
    @Transactional
    public void batchInsertLog(Staff staff, List<WaveUniqueCodeLog> logs) {
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        waveUniqueCodeLogDao.batchInsert(staff, logs);
    }

    /**
     * 已解绑标签填充历史信息
     */
    @Override
    public void fillUnBoundHistoryInfo(Staff staff, List<WaveUniqueCode> waveUniqueCodes) {
        List<WaveUniqueCode> releaseWaveUniqueCodes = waveUniqueCodes.stream().filter(OrderUniqueCodeUtils::isRelease).collect(toList());
        List<String> codes = releaseWaveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList());
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        Map<String, OrderUniqueCodeUnboundLog> code2LogMap = orderUniqueCodeUnboundLogDao.queryAllSidByLastOne(staff, codes)
                .stream().collect(Collectors.toMap(OrderUniqueCodeUnboundLog::getUniqueCode, Function.identity(), (d1, d2) -> d2));
        for (WaveUniqueCode code : releaseWaveUniqueCodes) {
            OrderUniqueCodeUnboundLog unboundLog = code2LogMap.get(code.getUniqueCode());
            if (unboundLog == null) {
                continue;
            }
            code.setPositionNoId(unboundLog.getPositionNoId());
            code.setPositionNo(unboundLog.getPositionNo());
            // 交易关闭返回解绑前sid
            if (UnboundReasonTypeEnum.ORDER_TRANSACTION_CLOSED.getType().equals(unboundLog.getReasonType())) {
                code.setSid(unboundLog.getSid());
            }
        }
    }

    /**
     * 复制新增拆分唯一码
     */
    @Override
    @Transactional
    public void generateSplitTag(Staff staff, List<Long> warehouseIdList, List<Long> userIdList) {
        lockService.lock(WaveLockBusiness.key2ERPLock(staff, ProgressEnum.PROGRESS_GENERATE_SPLIT_TAG.getKey()),
                () -> doGenerateSplitTag(staff, warehouseIdList, userIdList),
                1L,
                TimeUnit.SECONDS);
    }

    @Transactional
    public boolean doGenerateSplitTag(Staff staff, List<Long> warehouseIdList, List<Long> userIdList) {
        // 获取存在标记的【等待采购】【等待拣选】【等待收货】【已收货】【已拣选】的唯一码
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(),
                OrderUniqueCodeStatusEnum.WAIT_PICK.getType(),
                OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(),
                OrderUniqueCodeStatusEnum.RECIVED.getType(),
                OrderUniqueCodeStatusEnum.PICKED.getType()));
        params.setNewSplit(CommonConstants.JUDGE_YES);
        params.setUnbind(CommonConstants.JUDGE_YES);
        params.setWarehouseIds(warehouseIdList);
        params.setUserIds(userIdList);
        params.setForceStatusIndex(true);
        List<WaveUniqueCode> waveUniqueCodeList = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(waveUniqueCodeList)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未找到拆分新增的唯一码"));
            }
            return false;
        }
        // 复制新增拆分唯一码
        List<WaveUniqueCode> waveUniqueCodes = doBatchCancelReject(staff, null, waveUniqueCodeList, true);
        // 匹配分拣货位
        allocatePositionNo(staff,waveUniqueCodes);
        return true;
    }

    @Override
    public List<String> purchaseMatchUniqueCode(Staff staff, List<PurchaseMatchUniqueCodeParams> queryParams) {
        Assert.notEmpty(queryParams, "采购单商品明细不能为空！");
        Integer queryNum = 0;
        List<String> itemLog = new ArrayList<>();
        for (PurchaseMatchUniqueCodeParams params : queryParams) {
            queryNum += params.getNum();
            itemLog.add(params.toString());
        }

        Assert.isTrue(queryNum < PURCHASE_ITEM_QUERY_NUM_MAX, "匹配唯一码数量过大，请分批次匹配！");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "采购单匹配唯一码，商品信息：" + Strings.join(",", itemLog)) + "，商品总数：" + queryNum);
        }

        List<String> result = new ArrayList<>();
        for (List<PurchaseMatchUniqueCodeParams> items : Lists.partition(queryParams, PURCHASE_ITEM_QUERY_BATCH_NUM)) {
            List<String> codes = matchUniqueCodes(staff, items);
            if (CollectionUtils.isEmpty(codes)) {
                continue;
            }
            result.addAll(codes);
        }
        return result;
    }

    private List<String> matchUniqueCodes(Staff staff, List<PurchaseMatchUniqueCodeParams> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.emptyList();
        }
        List<String> uniqueCodeList = new ArrayList<>();
        List<String> logs = new ArrayList<>();
        List<String> uniqueCodes;
        for (PurchaseMatchUniqueCodeParams param : params) {
            if (param.getBusyCode() == null) {
                uniqueCodes = matchUniqueCodeByWarehouseId(staff, param);
            } else {
                uniqueCodes = matchUniqueCodeByPurchaseCode(staff, param);
            }
            if (CollectionUtils.isEmpty(uniqueCodes)) {
                logs.add(String.format("商品[%s],在仓库[%s]中无唯一码可匹配！", WmsKeyUtils.buildItemKey(param.getSysItemId(), param.getSysSkuId()), param.getWarehouseId()));
                continue;
            }
            uniqueCodeList.addAll(uniqueCodes);
        }
        if (logger.isDebugEnabled() && !CollectionUtils.isEmpty(logs)) {
            logger.debug(LogHelper.buildLog(staff, Strings.join(",", logs)));
        }
        return uniqueCodeList;
    }

    private List<String> matchUniqueCodeByWarehouseId(Staff staff, PurchaseMatchUniqueCodeParams param) {
        if (!DataUtils.checkLongNotEmpty(param.getSysItemId()) && !DataUtils.checkLongNotEmpty(param.getSysSkuId())) {
            throw new IllegalArgumentException("商品Id不能为空! ");
        }
        if (!DataUtils.checkIntegerNotEmpty(param.getNum())) {
            return Lists.newArrayList();
        }
        List<Long> sysSkuIds = DataUtils.checkLongNotEmpty(param.getSysSkuId()) ? Lists.newArrayList(param.getSysSkuId()) : new ArrayList<>();
        List<String> uniqueCodes = waveUniqueCodeDao.purchaseMatchUniqueCodes(staff, Lists.newArrayList((param.getSysItemId())), sysSkuIds, param.getWarehouseId());
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            logger.debug(LogHelper.buildLog(staff, "商品：" + WmsKeyUtils.buildItemKey(param.getSysItemId(), param.getSysSkuId()) + "无唯一码匹配！"));
        } else {
            uniqueCodes = uniqueCodes.subList(0, Math.min(param.getNum(), uniqueCodes.size()));
        }
        createBackUniqueCodes(staff, param, uniqueCodes);
        return uniqueCodes;
    }

    /**
     * 通过采购单匹配唯一码
     * @param staff
     * @param param
     * @return
     */
    private List<String> matchUniqueCodeByPurchaseCode(Staff staff, PurchaseMatchUniqueCodeParams param) {
        logger.debug(String.format("根据采购单 %s 匹配唯一码, paramStr：%s", param.getBusyCode(), param.toString()));
        if (!DataUtils.checkLongNotEmpty(param.getSysItemId()) && !DataUtils.checkLongNotEmpty(param.getSysSkuId())) {
            throw new IllegalArgumentException("商品Id不能为空! ");
        }
        if (param.getBusyCode() == null) {
            throw new IllegalArgumentException("采购单号不能为空! ");
        }
        if (param.getWarehouseId() == null) {
            throw new IllegalArgumentException("仓库不能为空! ");
        }
        if (!DataUtils.checkIntegerNotEmpty(param.getNum())) {
            return Lists.newArrayList();
        }
        // 查询采购单唯一码关联关系
        OrderUniqueCodeQueryParams relationQueryParam = new OrderUniqueCodeQueryParams();
        relationQueryParam.setBusinessType(UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType());
        relationQueryParam.setBusinessCodes(Lists.newArrayList(param.getBusyCode()));
        List<UniqueCodeRelation> uniqueCodeRelations = uniqueCodeRelationDao.queryByUniqueCondition(staff, relationQueryParam);
        if(CollectionUtils.isEmpty(uniqueCodeRelations)) {
            throw new IllegalArgumentException("采购单不是唯一码采购单！");
        }
        // 查询唯一码
        List<String> relationUniqueCodes = uniqueCodeRelations.stream().map(UniqueCodeRelation::getUniqueCode).collect(Collectors.toList());
        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, new ItemUniqueCodeQueryParams().setUniqueCodes(relationUniqueCodes));
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            throw new IllegalArgumentException("唯一码不存在！");
        }
        // 唯一码采购单类型（唯一码类型）
        Integer type = waveUniqueCodes.get(0).getType();
        // 匹配符合条件的唯一码
        String key = WmsKeyUtils.buildItemKey(param.getSysItemId(), DataUtils.getZeroIfDefault(param.getSysSkuId()));
        List<Integer> statusList = Lists.newArrayList(1, 4, 5, 10, 11);
        List<String> uniqueCodes = waveUniqueCodes.stream().filter(code -> Objects.equals(param.getWarehouseId(), code.getWarehouseId())
                        && key.equals(WmsKeyUtils.buildWaveUniqueCodeItemKey(code))
                        && statusList.contains(code.getStatus())
                        && Objects.equals(0, code.getPrintNum()))
                .map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        uniqueCodes = uniqueCodes.subList(0, Math.min(param.getNum(), uniqueCodes.size()));

        // 超出部分生成备采唯一码
        createBackUniqueCodes(staff, param, uniqueCodes, type);
        return uniqueCodes;
    }


    /**
     * 超出部分生成备货唯一码
     */
    private void createBackUniqueCodes(Staff staff, PurchaseMatchUniqueCodeParams param, List<String> uniqueCodes) {
        createBackUniqueCodes(staff, param, uniqueCodes, 1);
    }

    /**
     * 超出部分生成备采唯一码,生成指定类型的备采唯一码
     * @param type
     */
    private void createBackUniqueCodes(Staff staff, PurchaseMatchUniqueCodeParams param, List<String> uniqueCodes, Integer type) {
        if (!param.isNeedCreateBack()) {
            return;
        }
        List<WaveUniqueCode> waveUniqueCodes = Lists.newArrayList();
        if (param.getNum() > uniqueCodes.size()) {
            int backNum = param.getNum() - uniqueCodes.size();
            if (Objects.equals(1, type)) {
                createBackOrderUniqueCodes(staff, param, waveUniqueCodes, backNum);
            } else if (Objects.equals(2, type)) {
                itemUniqueCodeExtendService.createBackItemUniqueCodes(staff, param, waveUniqueCodes, backNum);
            }

            uniqueCodes.addAll(waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
        }
    }

    /**
     * 生成备货类型的订单唯一码
     * @param staff
     * @param param
     * @param waveUniqueCodes
     * @param backNum
     */
    private void createBackOrderUniqueCodes(Staff staff, PurchaseMatchUniqueCodeParams param, List<WaveUniqueCode> waveUniqueCodes, int backNum) {
        for (int i = 0; i < backNum; i++) {
            WaveUniqueCode code = new WaveUniqueCode();
            code.setOuterId(param.getOuterId());
            code.setSysItemId(param.getSysItemId());
            code.setSysSkuId(param.getSysSkuId());
            code.setNum(1);
            code.setSupplierId(param.getSupplierId());
            code.setSupplierName(param.getSupplierName());
            code.setStatus(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType());
            code.setType(CommonConstants.JUDGE_YES);
            code.setCodeType(OrderUniqueCodeUtils.CODE_TYPE_BACK);
            code.setWaveId(WaveTypeEnum.ORDER_UNIQUE_CODE.getWaveId());
            code.setWarehouseId(param.getWarehouseId());
            code.setStockStatus(CommonConstants.ENABLE_STATUS_DELETED);
            code.setCreated(new Date());
            code.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
            code.setBusinessCode(param.getBusyCode());
            waveUniqueCodes.add(code);

        }
        orderUniqueCodeHelpBusiness.saveUniqueCodes(staff, waveUniqueCodes, null, null, Lists.newArrayList(), Maps.newHashMap(), param.getBusyCode() != null);
        logger.debug("商品" + WmsKeyUtils.buildItemKey(param.getSysItemId(), DataUtils.getZeroIfDefault(param.getSysSkuId())) + "生成备采类型订单唯一码数量:" + waveUniqueCodes.size());
        if (param.getBusyCode() != null) {
            List<UniqueCodeRelation> purchaseOrderRelationList = getPurchaseOrderRelationList(waveUniqueCodes);
            if (!CollectionUtils.isEmpty(purchaseOrderRelationList)) {
                uniqueCodeRelationDao.batchInsert(staff, purchaseOrderRelationList);
                logger.debug(String.format("生成备采订单唯一码绑定采购单, 采购单号：%s, 唯一码：%s", param.getBusyCode(), waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList())));
            }
        }
    }

    @Override
    @Transactional
    public Integer uniqueCodeRefreshSupplier(Staff staff, ProgressData progressData) {
        Page page = new Page(1, 20000); // 只返回id可以大一些
        List<Long> uniqueCodeIds = new ArrayList<>();
        while (true) {
            List<Long> uniqueCodeIdsTempt = waveUniqueCodeDao.queryUniqueCodeId4RefreshSupplier(staff, page);
            if (CollectionUtils.isEmpty(uniqueCodeIdsTempt)) {
                break;
            }
            uniqueCodeIds.addAll(uniqueCodeIdsTempt);
            page.setPageNo(page.getPageNo() + 1);
        }

        // 没有需要更新的唯一码
        if (CollectionUtils.isEmpty(uniqueCodeIds)) {
            return 0;
        }
        if (progressData != null) {
            progressData.setCountAll(uniqueCodeIds.size());
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_UNIQUE_CODE_REFRESH_SUPPLIER, progressData, 10);
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        boolean refreshSupplierWithBusinessNo = OrderUniqueCodeUtils.isTrue(config.getRefreshSupplierWithBusinessNo());

        // 分批次处理唯一码
        int successNum = 0;
        for (List<Long> subUniqueCodeIds : Lists.partition(uniqueCodeIds, 5000)) {
            successNum += doRefreshSupplier(staff, subUniqueCodeIds, refreshSupplierWithBusinessNo);
            waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_UNIQUE_CODE_REFRESH_SUPPLIER, progressData, 20);
        }
        return successNum;
    }

    /**
     * 执行刷新供应商操作
     * @param staff
     * @param uniqueCodeIds
     * @param refreshSupplierWithBusinessNo
     * @return
     */
    private int doRefreshSupplier(Staff staff, List<Long> uniqueCodeIds, boolean refreshSupplierWithBusinessNo) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodeIds(uniqueCodeIds);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            return 0;
        }

        List<WaveUniqueCode> updates = new ArrayList<>();
        Map<String, ItemSupplierBridge> bestSupplierMap = getBestSupplierMap(staff, codes);
        for (WaveUniqueCode code : codes) {
            ItemSupplierBridge bridge = bestSupplierMap.getOrDefault(WmsKeyUtils.buildItemKey(code.getSysItemId(), code.getSysSkuId()), new ItemSupplierBridge());
            if (Objects.equals(DataUtils.getZeroIfDefault(bridge.getSupplierId()), 0L) || (Objects.equals(code.getSupplierId(), Optional.ofNullable(bridge.getSupplierId()).orElse(0L)) && Objects.equals(code.getSupplierName(), bridge.getSupplierName()))) {
                continue;
            }
            if (!refreshSupplierWithBusinessNo && StringUtils.isNotEmpty(code.getBusinessCode())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "唯一码关联业务单据：" + code.getBusinessCode() + "，不更新供应商！"));
                }
                continue;
            }

            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setSupplierId(Optional.ofNullable(bridge.getSupplierId()).orElse(0L));
            update.setSupplierName(Optional.ofNullable(bridge.getSupplierName()).orElse(""));
            update.setBeforeSupplierName(Optional.ofNullable(code.getSupplierName()).orElse(""));
            updates.add(update);
        }

        waveUniqueCodeDao.batchUpdate(staff, updates);
        refreshFireLog(staff, updates);

        return updates.size();
    }

    /**
     * 获取最优供应商
     * @param staff
     * @param codes 需要获取最优供应商的唯一码
     * @return
     */
    private Map<String, ItemSupplierBridge> getBestSupplierMap(Staff staff, List<WaveUniqueCode> codes) {
        List<Pair<Long, Long>> pairs = codes.stream().map(w -> Pair.of(w.getSysItemId(), DataUtils.getZeroIfDefault(w.getSysSkuId()))).collect(toList());
        return  waveHelpBusiness.getBestSupplierMap(staff, pairs, true);
    }

    /**
     * 刷新供应商记录唯一码日志
     * @param updates
     */
    private void refreshFireLog(Staff staff, List<WaveUniqueCode> updates) {
        if (CollectionUtils.isEmpty(updates)) {
            return;
        }

        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(updates), WaveUniqueOpType.REFRESH_SUPPLIER}), null);
    }

    /**
     * 校验打印模版
     * @param param
     * @param trade
     */
    public void validateTemplate(Staff staff,WavePickingParam param, Trade trade, WaveUniqueCode code) {
        if (featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE)) {
            validateLogisticsCompany(staff, param, trade, code);
            return;
        }

        if (trade == null || param == null
                || trade.getTemplateType() == null
                || trade.getTemplateType() == 0 // 非电子面单不在这里校验
                || !OrderUniqueCodeUtils.checkLongNotEmpty(trade.getTemplateId())) {
            return;
        }

        // 匹配快递模版
        Long templateId = trade.getTemplateId();

        JSONObject jsonObject = null;
        // 勾选指定快递的模版集合
        List<String> isLockTemplateList = null;
        if (StringUtils.isNotBlank(param.getProPrinterConfig())) {
            JSONArray printerSettingList = JSONObject.parseObject(param.getProPrinterConfig()).getJSONArray("multiPrinterSettingList");
            if (!CollectionUtils.isEmpty(printerSettingList)) {
                isLockTemplateList = printerSettingList.stream().filter(o -> {
                    Boolean isLockTemplate = ((JSONObject) o).getBoolean("isLockTemplate");
                    return Objects.nonNull(isLockTemplate) && isLockTemplate;
                }).map(o -> ((JSONObject) o).getString("templateId")).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(isLockTemplateList)) {
            return;
        } else {
            // 有勾选指定快递选项则判断是否为勾选的指定快递
            if (isLockTemplateList.contains(templateId.toString())) {
                return;
            }
        }

        // 匹配快递模版类型
        List<String> wlbTemplateTypes = OrderUniqueCodeUtils.getMultiplePrinterTemplates(param.getUniqueCodeWlbTemplateTypes());
        if (!CollectionUtils.isEmpty(wlbTemplateTypes)) {
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
            if (userWlbExpressTemplate != null && wlbTemplateTypes.contains(userWlbExpressTemplate.getWlbTemplateType().toString())) {
                return;
            }
        }
        // 没有勾选指定快递的时候才校验默认打印机
        if (CollectionUtils.isEmpty(isLockTemplateList)) {
            // 匹配默认模版
            Integer openDefaultPrinter = param.getOpenDefaultPrinter();
            if (openDefaultPrinter != null && openDefaultPrinter == 1) {
                return;
            }
        }

        setExpressCode(trade);
        String msg = "唯一码扫描验货：订单快递模板与打印机设置不符";
        if (StringUtils.isNotEmpty(trade.getExpressName())) {
            msg += "（" + trade.getExpressName() + "）";
        }
        if (code != null) {
            waveUniqueCodeLogDao.batchInsert(staff, Lists.newArrayList(buildLogs(staff, Lists.newArrayList(code), null, WaveUniqueOpType.INSPECTION,msg)));
        }
        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_PRINT_TEMPLATE, msg);
    }

    private void setExpressCode(Trade trade) {
        if (trade == null || StringUtils.isNotEmpty(trade.getExpressCode())) {
            return;
        }
        if (trade.getExpressCompanyId() != null) {
            ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(trade.getExpressCompanyId());
            trade.setExpressCode(expressCompany != null ? expressCompany.getCode() : null);
            trade.setExpressName(expressCompany != null ? expressCompany.getName() : null);
        }
    }

    //校验逻辑类似validateTemplate方法
    public void validateLogisticsCompany(Staff staff,WavePickingParam param, Trade trade, WaveUniqueCode code) {
        if (trade == null || param == null) {
            return;
        }

        // 勾选指定快递的快递公司名称集合
        List<Long> isLockLogisticsCompanyId = new ArrayList<>();
        List<Long> allSettingLogisticsCompanyId = new ArrayList<>();
        // 勾选默认打印机
        boolean openDefaultPrinter = false;

        if (StringUtils.isNotEmpty(param.getProPrinterConfig())) {
            JSONObject jsonObject = JSONObject.parseObject(param.getProPrinterConfig());
            JSONArray multiPrinterSettingList = jsonObject.getJSONArray("multiPrinterSettingList");
            if (jsonObject.getInteger("openDefaultPrinter") != null && jsonObject.getInteger("openDefaultPrinter") == 1) {
                openDefaultPrinter = true;
            }
            if (multiPrinterSettingList != null && multiPrinterSettingList.size() > 0) {
                for (Object obj : multiPrinterSettingList) {
                    JSONObject settingObj = (JSONObject) obj;
                    Boolean isLockTemplate = settingObj.getBoolean("isLockTemplate");
                    Long logisticsCompanyId = settingObj.getLong("logisticsCompanyId");
                    if (logisticsCompanyId == null) {
                        continue;
                    }
                    allSettingLogisticsCompanyId.add(logisticsCompanyId);
                    if (BooleanUtils.isNotTrue(isLockTemplate)) {
                        continue;
                    }
                    isLockLogisticsCompanyId.add(logisticsCompanyId);
                }
            }
        }

        if (CollectionUtils.isEmpty(isLockLogisticsCompanyId) && (openDefaultPrinter || allSettingLogisticsCompanyId.contains(trade.getLogisticsCompanyId()))) {
            return;
        }
        if (isLockLogisticsCompanyId.contains(trade.getLogisticsCompanyId())) {
            return;
        }

        // 匹配快递模版类型
        List<String> wlbTemplateTypes = getWlbTemplateTypes(param.getAbroadPrintSetting());
        if (!CollectionUtils.isEmpty(wlbTemplateTypes) && trade.getTemplateType() != null && trade.getTemplateType() != 0 &&
                OrderUniqueCodeUtils.checkLongNotEmpty(trade.getTemplateId())) {
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
            if (userWlbExpressTemplate != null && wlbTemplateTypes.contains(userWlbExpressTemplate.getWlbTemplateType().toString())) {
                return;
            }
        }

        String msg = "唯一码扫描验货：订单快递公司名称与打印机设置不符";
        if (code != null) {
            waveUniqueCodeLogDao.batchInsert(staff, Lists.newArrayList(buildLogs(staff, Lists.newArrayList(code), null, WaveUniqueOpType.INSPECTION,msg)));
        }
        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_PRINT_LOGISTICS_COMPANY, msg);
    }

    private List<String> getWlbTemplateTypes(String abroadPrintSetting) {
        if (StringUtils.isEmpty(abroadPrintSetting)) {
            return new ArrayList<>();
        }
        List<String> wlbTemplateTypes = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(abroadPrintSetting);
        for (Object obj : jsonArray) {
            if (obj instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) obj;
                String wlbTemplateType = jsonObject.getString("wlbTemplateType");
                wlbTemplateTypes.add(wlbTemplateType);
            }
        }
        return wlbTemplateTypes;
    }

    /**
     * 记录验货日志
     * @param staff
     * @param waveUniqueCodes
     */
    public void saveExamineTrace(Staff staff,List<WaveUniqueCode> waveUniqueCodes){
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(waveUniqueCodes), WaveUniqueOpType.INSPECTION}), null);
    }

    @Override
    public void batchCancelOrUnboundUniqueCode(Staff staff, OrderUniqueCodeQueryParams params, Integer opType, ProgressData progressData) {
        Assert.notNull(opType, "操作类型不能为空！");
        params.setReceiveStatus(2);
        params.setPage(null);
        //判断是否开启pgl查询
        params.setPageUserPgl(orderUniqueCodeHelpBusiness.openUsePgl(staff));
        List<WaveUniqueCode> uniqueCodes = queryOrderUniqueCodeAdaptive(staff, params);
        Assert.notEmpty(uniqueCodes, "未查询到唯一码！");
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_AND_UNBOUND_ORDER_UNIQUE_CODE, progressData, 30);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码批量取消/解绑，数量：" + uniqueCodes.size()));
        }
        //过滤已取消状态
        Set<Long> sids = uniqueCodes.stream().filter(t -> !Objects.equals(t.getStatus(), OrderUniqueCodeStatusEnum.CANCEL.getType())).map(WaveUniqueCode::getSid).collect(Collectors.toSet());
        for (List<Long> sidList : Lists.partition(new ArrayList<>(sids), 2000)) {
            cancel4Trade(staff, sidList, true, params.getCancelTypeEnum() != null ? params.getCancelTypeEnum() : UnboundReasonTypeEnum.MANUAL_VOID);
        }
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_AND_UNBOUND_ORDER_UNIQUE_CODE, progressData, 80);
    }

    /**
     * 到货打印生成备货标签
     */
    @Override
    public List<WaveUniqueCode> saveBackUniqueCodes4Print(Staff staff, OrderUniqueCodeQueryParams orderUniqueCodeQueryParams) {
        return orderUniqueCodeArrivedPrintBusiness.saveBackUniqueCodes4Print(staff, orderUniqueCodeQueryParams);
    }

    /**
     * 到货打印处理备货唯一码回调
     */
    public void backUniqueCodePrintCallBack(Staff staff, List<WaveUniqueCode> codes) {
        orderUniqueCodeArrivedPrintBusiness.backUniqueCodePrintCallBack(staff, codes);
    }

    /**
     * 执行订单拆分重新匹配分拣货位
     */
    public void allocatePositionNo(Staff staff,List<WaveUniqueCode> waveUniqueCodes){
        OrderUniqueCodeConfig orderUniqueCodeConfig = orderUniqueCodeConfigService.get(staff);
        Integer positionOccupyCalculateType = orderUniqueCodeConfig.getPositionOccupyCalculateType();
        if(Integer.valueOf(1).equals(positionOccupyCalculateType)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "执行订单拆分重新匹配分拣货位"));
            }
            StringBuffer sb = new StringBuffer();
            OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
            params.setUniqueCodes(waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
            List<WaveUniqueCode> list = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff,params);
            List<WaveItem> waveItems = bulidWaveItem(list);
            Map<Long, List<WaveItem>> waveItemMap = waveItems.stream().collect(Collectors.groupingBy(WaveItem::getWarehouseId));
            List<WarehousePositionConfig> positionConfigs = bulidWarehousePositionConfig(staff, waveItems);
            Map<Long, List<WarehousePositionConfig>> warehousePositionConfigMap = positionConfigs.stream().collect(Collectors.groupingBy(WarehousePositionConfig::getWarehouseId));
            for (Long warehouseId : waveItemMap.keySet()) {
                Map<Long, Pair<Integer, Integer>> usedMap = buildUsedMap(staff, warehouseId);
                orderUniqueCodeGenerateBusiness.allocatePositionNo(staff, waveItemMap.get(warehouseId), warehousePositionConfigMap.get(warehouseId), null, usedMap, orderUniqueCodeConfig, null, Maps.newHashMap());
            }
            Map<Long, WaveItem> orderItemMap = waveItems.stream().collect(Collectors.toMap(WaveItem::getOrderId, Function.identity()));
            for(WaveUniqueCode code:list){
                WaveItem waveItem = orderItemMap.get(code.getOrderId());
                if(waveItem!=null) {
                    sb.append(code.getUniqueCode()+" 重新匹配分拣货位 "+code.getPositionNo()+" -> "+waveItem.getPositionNo()+"; ");
                    code.setPositionNo(waveItem.getPositionNo());
                    code.setPositionNoId(waveItem.getPositionNoId());
                }
            }
            orderUniqueCodeHelpBusiness.fillPositionNoSort(staff, list);
            waveUniqueCodeDao.batchUpdate(staff,list);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码重新匹配分拣货位：" + sb.toString()));
            }
        }
    }

    private List<WaveItem> bulidWaveItem(List<WaveUniqueCode> waveUniqueCodes){
        Map<Long, List<WaveUniqueCode>> orderUniqueCode = waveUniqueCodes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId));
        List<WaveItem> orderItems = Lists.newArrayList();
        for (Long key : orderUniqueCode.keySet()) {
            List<WaveUniqueCode> uniqueCodes = orderUniqueCode.get(key);
            WaveUniqueCode waveUniqueCode = uniqueCodes.get(0);
            WaveItem waveItem = new WaveItem();
            waveItem.setOrderId(waveUniqueCode.getOrderId());
            waveItem.setSid(waveUniqueCode.getSid());
            waveItem.setOuterId(waveUniqueCode.getOuterId());
            waveItem.setSysItemId(waveUniqueCode.getSysItemId());
            waveItem.setSysSkuId(waveUniqueCode.getSysSkuId());
            waveItem.setNum(uniqueCodes.size());
            waveItem.setPayTime(waveUniqueCode.getPayTime());
            waveItem.setWarehouseId(waveUniqueCode.getWarehouseId());
            waveItem.setTemplateId(waveUniqueCode.getTemplateId());
            waveItem.setTemplateType(waveUniqueCode.getTemplateType());
            waveItem.setLogisticsCompanyId(waveUniqueCode.getLogisticsCompanyId());
            waveItem.setUserId(waveUniqueCode.getUserId());
            orderItems.add(waveItem);
        }
        return orderItems;
    }

    private List<WarehousePositionConfig> bulidWarehousePositionConfig(Staff staff,List<WaveItem> waveItems){
        WarehousePositionConfigParams warehousePositionConfigParams = new WarehousePositionConfigParams();
        warehousePositionConfigParams.setWarehouseIdList(waveItems.stream().map(waveItem -> waveItem.getWarehouseId()).collect(Collectors.toList()));
        return wmsService.queryWarehousePositionConfigList(staff, warehousePositionConfigParams);
    }

    private Map<Long, Pair<Integer, Integer>> buildUsedMap(Staff staff,Long warehouseId){
        Map<Long, Pair<Integer, Integer>> usedMap = queryOrderUniqueCodeUsed(staff, warehouseId);
        return usedMap;
    }

    @Override
    public void updateUniqueCodeStockPosition(Staff staff, List<String> uniqueCodes, boolean recordLog) {
        if (!CollectionUtils.isEmpty(uniqueCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码更改存库位置：" + uniqueCodes.toString()));
            }

            OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeQueryParams();
            queryParams.setUniqueCodes(uniqueCodes);
            List<WaveUniqueCode> codes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, queryParams);
            if (!CollectionUtils.isEmpty(codes)) {
                codes.forEach(code -> code.setStockPosition(recordLog ? UniqueCodeStockPositionEnum.SALES_RETURN_AREA.getType() : UniqueCodeStockPositionEnum.DEFAULT.getType()));
                waveUniqueCodeDao.batchUpdate(staff, codes);

                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "修改成功的唯一码：" + codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList())));
                }
                if (recordLog) {
                    waveUniqueCodeLogDao.batchInsert(staff, buildLogs(staff, codes, null, WaveUniqueOpType.TRANSLATION));
                }
            }
        }
    }

    /**
     * 重新配货
     */
    @Override
    @Transactional
    public void reAllocate(Staff staff, ProgressData progressData, OrderUniqueCodeQueryParams queryParams) {
        Set<Long> supplierIds = uniqueCodeForceAllocateGoodsSupplierService.getSupplierIds(staff);
        if (queryParams == null) {
            queryParams = new OrderUniqueCodeQueryParams();
        }
        Date threeMonthDate = LocalDateUtils.convertLDTToDate(LocalDateUtils.convertDateToLDT(new Date()).minusMonths(3));
        if (queryParams.getCreateBegin() == null || threeMonthDate.after(queryParams.getCreateBegin())) {
            queryParams.setCreateBegin(threeMonthDate);
        }
        queryParams.setForceSupplierIds(Lists.newArrayList(Optional.ofNullable(supplierIds).orElse(Sets.newHashSet())));
        List<WaveUniqueCode> forceSupplierList = Lists.newArrayList();
        //固定供应商
        if (!CollectionUtils.isEmpty(supplierIds)) {
            queryParams.setReAllocateType(1);
            forceSupplierList = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, queryParams);
        }
        // 非固定供应商
        queryParams.setReAllocateType(2);
        List<WaveUniqueCode> notForceSupplierList = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, queryParams);
        List<WaveUniqueCode> codes = ArrayUtils.saveAddAll(forceSupplierList, notForceSupplierList);

        for (List<WaveUniqueCode> subCodes : Lists.partition(new ArrayList<>(codes), 500)) {
            doReAllocate(staff, progressData, subCodes, supplierIds);
        }
    }

    /**
     * 重新配货
     */
    private void doReAllocate(Staff staff, ProgressData progressData, List<WaveUniqueCode> filters, Set<Long> supplierIds) {
        Assert.notEmpty(filters, "无可重新配货的唯一码！");
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(filters);
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_REALLOCATE_ORDER_UNIQUE_CODE, progressData, 30);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "唯一码新配货，数量：" + filters.size()));
        }
        reSetStockStatus(staff, filters, supplierIds);
        // 供应商固定家里拿货
        orderUniqueCodeGenerateBusiness.supplierForeAllocateGoods(staff, filters, supplierIds);

        List<WaveUniqueCode> updates = Lists.newArrayList();
        for (WaveUniqueCode filter : filters) {
            if (!filter.getChangeStatus4ResetAllocateRecords()) {
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(filter.getId());
            update.setStatus(filter.getStatus());
            update.setStockStatus(filter.getStockStatus());
            update.setGoodsSectionCode(filter.getGoodsSectionCode());
            update.setGoodsSectionId(filter.getGoodsSectionId());
            update.setTagIds(filter.getTagIds());
            // 状态回退重置打印次数
            // 等待拣选->等待拣选，打印次数不清空
            boolean emptyPrintNum = Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), filter.getStatus()) ||
                    (!Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), filter.getOldStatus()) && Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), filter.getStatus()));
            if (emptyPrintNum) {
                update.setPrintNum(0);
            }
            updates.add(update);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, waveUniqueCodeLogDTOS, WaveUniqueOpType.RE_ALLOCATE}), null);

        // 外采唯一码自动生成唯一码采购单
        if (!CollectionUtils.isEmpty(updates)) {
            List<Long> uniqueCodeIds = updates.stream().filter(code -> Objects.equals(code.getStockStatus(), 0)).map(WaveUniqueCode::getId).collect(toList());
            List<String> uniqueCodes = filters.stream().filter(code -> uniqueCodeIds.contains(code.getId())).map(WaveUniqueCode::getUniqueCode).collect(toList());
            if (!CollectionUtils.isEmpty(uniqueCodes)) {
                eventCenter.fireEvent(this, new EventInfo("order.unique.code.auto.create.purchase").setArgs(new Object[]{staff, uniqueCodes}), null);
            }
        }

        orderUniqueCodePushService.pushUniqueCodes(staff, filters);
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_REALLOCATE_ORDER_UNIQUE_CODE, progressData, 80);
    }


    /**
     *  从缓存中列配置判断商品简称是否显示
     */
    private Boolean shortTitleShow(Staff staff) {
        // 扫描创建采退单列配置
        Long pageId = 302L;
        // 商品简称colId
        Long colId = 2825L;
        String key = "caigou_column_conf_" + staff.getCompanyId() + "_" + staff.getId() + "_" + pageId;
        ColumnConfListWrapper value = null;
        try {
            value = cache.get(key);
        } catch (CacheException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "扫描创建采退单列配置查询列配置缓存失败"), e);
        }
        if (value == null) {
            return true;
        }
        return Optional.ofNullable(value.getColumnConfList())
                .orElse(Collections.emptyList())
                .stream().filter(e -> e.getColId().equals(colId) && e.getVisible() == 1).findAny().isPresent();
    }


    public List<WaveUniqueCode> fillItemCategoryName(Staff staff, Integer isShow, List<WaveUniqueCode> codes){
        if(isShow == null || isShow == 1){
            ItemCatIdAndSellerCidsResponse response = itemServiceDubbo.queryItemCatIdAndSellerCids(staff,codes.stream().map(w->w.getOuterId()).distinct().collect(Collectors.toList()));
            if (response != null && !org.apache.commons.collections.CollectionUtils.isEmpty(response.getItemList())) {
                Map<String,String> map = response.getItemList().stream().collect(Collectors.toMap(ItemCatIdAndSellerCidsResponse.SimpleItem::getOuterId,ItemCatIdAndSellerCidsResponse.SimpleItem::getItemCategoryNames,(a,b)->a));
                codes.forEach(code->code.setItemCategoryNames(map.get(code.getOuterId())==null?"":map.get(code.getOuterId())));
            }
        }
        return codes;
    }

    @Override
    public boolean checkOpenUniqueCodeAsyncGetPrintDataFeature(Staff staff) {
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setCode("uniqueCodeAsynGetPrintData");
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        if (response == null) {
            return false;
        }
        return response.isHasFeature();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * PDA混放上架，批量解绑订单唯一码
     * @param staff
     * @param itemList 查询条件
     */
    @Override
    @Transactional
    public void pdaOnShelfBatchUnboundUniqueCode(Staff staff, List<PdaOnShelfUnboundItem> itemList) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        if (ObjectUtils.isEmpty(config) || ObjectUtils.isEmpty(config.getExtConfigMap())) {
            return;
        }
        Integer singleOnShelfUnbindUniqueCode = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.SINGLE_ON_SHELF_UNBIND_UNIQUE_CODE.getKey(), 0);
        Integer multiOnShelfUnbindUniqueCode = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.MUTLI_ON_SHELF_UNBIND_UNIQUE_CODE.getKey(), 0);
        if ((singleOnShelfUnbindUniqueCode == null || Objects.equals(0, singleOnShelfUnbindUniqueCode)) && (multiOnShelfUnbindUniqueCode == null || Objects.equals(0, multiOnShelfUnbindUniqueCode))) {
            logger.debug(LogHelper.buildLog(staff, "未开启上架解绑外采唯一码设置"));
            return;
        }
        Assert.notEmpty(itemList, "接收到PDA上架商品为空！");
        List<WaveUniqueCode> uniqueCodes = Lists.newArrayList(); // 所有需要解绑的唯一码
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        fillPdaOnShelfOrderUniqueCodeQueryParams(itemList, singleOnShelfUnbindUniqueCode, multiOnShelfUnbindUniqueCode, params); // 填充查询pda上架解绑唯一码的查询参数
        List<WaveUniqueCode> codes = queryOrderUniqueCodeAdaptive(staff, params); // 查询唯一码
        if (CollectionUtils.isEmpty(codes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(String.format("PDA上架商品无对应需要解绑的唯一码！\n %s", itemList));
            }
            return;
        }
        for (PdaOnShelfUnboundItem item : itemList) {
            getUnboundUniqueCodesByItem(staff, item, codes, uniqueCodes, config);
        }
        List<List<WaveUniqueCode>> uniqueCodesList = Lists.partition(uniqueCodes, 1000);
        // 分批处理唯一码
        for (List<WaveUniqueCode> subList : uniqueCodesList) {
            handlePdaOnShelfUnboundUniqueCode(staff, subList);
        }

    }

    /**
     * 填充pda上架解绑唯一码的订单唯一码查询参数
     * @param itemList
     * @param params
     */
    private void fillPdaOnShelfOrderUniqueCodeQueryParams(List<PdaOnShelfUnboundItem> itemList, Integer singleOnShelfUnbindUniqueCode, Integer multiOnShelfUnbindUniqueCode, OrderUniqueCodeQueryParams params) {
        // 缺货来源：外采
        params.setStockStatus(0);
        // 唯一码 sysItemIds
        List<Long> sysItemIds = itemList.stream().map(PdaOnShelfUnboundItem::getSysItemId).distinct().collect(toList());
        params.setSysItemIds(sysItemIds);
        // 唯一码 sysSkuIds
        List<Long> sysSkuIds = itemList.stream().map(item -> (ObjectUtils.isEmpty(item.getSysSkuId()) || item.getSysSkuId() <= 0L) ? -1L : item.getSysSkuId()).distinct().collect(toList());
        params.setSysSkuIds(sysSkuIds);
        // 唯一码状态：等待采购/等待收货
        params.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()));
        // 唯一码类型：单件/多件
        List<Integer> codeTypeList = Lists.newArrayList();
        if (Objects.equals(singleOnShelfUnbindUniqueCode, 1)) {
            codeTypeList.add(WaveUniqueCode.CodeTypeEnum.SINGLE.getValue());
        }
        if (Objects.equals(multiOnShelfUnbindUniqueCode, 1)) {
            codeTypeList.add(WaveUniqueCode.CodeTypeEnum.MULTI.getValue());
        }
        params.setCodeTypes(codeTypeList);
        // 唯一码是否解绑：未解绑
        params.setUnbind(1);
        // 排序：按照唯一码生成顺序从早到晚
        params.setSortField("created");
        params.setSortOrder(0);
    }

    /**
     * PDA上架解绑唯一码分批处理并记录唯一码日志
     * @param staff
     * @param uniqueCodes
     */
    @Transactional
    public void handlePdaOnShelfUnboundUniqueCode(Staff staff, List<WaveUniqueCode> uniqueCodes) {
        List<WaveUniqueCode> updates = Lists.newArrayList(); // 更新唯一码
        List<String> unboundUniqueCodes = Lists.newArrayList(); // 解绑唯一码（String）
        //拆分后需要重新查询唯一获取最新的订单号用于解绑
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeAdaptive(staff, params);
        List<Long> sids = codes.stream().map(WaveUniqueCode::getSid).collect(toList());
        // 订单踢出-5波次
        cancel4Trade(staff, sids, true, UnboundReasonTypeEnum.PDA_MULTI_SHELF_UNBINDING); // 取消或解绑并记录唯一码日志
        // 解绑唯一码更新
        for (WaveUniqueCode code : uniqueCodes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setSid(0L);
            update.setOrderId(0L);
            update.setCodeType(3);
            if (Objects.equals(0, code.getPrintNum())) { // 未打印的唯一码状态变更为已取消
                update.setStatus(OrderUniqueCodeStatusEnum.CANCEL.getType());
                // 取消状态的唯一码一定解绑
                OrderUniqueCodeUtils.buildRelease(update);
            }
            update.setUniqueCode(code.getUniqueCode());
            update.setId(code.getId());
            update.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            unboundUniqueCodes.add(code.getUniqueCode());
            updates.add(update);
        }
        // 新增解绑日志，用于再次解绑
        saveUnboundLog(staff, uniqueCodes, UnboundReasonTypeEnum.PDA_MULTI_SHELF_UNBINDING.getType(), updates, unboundUniqueCodes);
        waveUniqueCodeDao.batchUpdate(staff, updates);
    }

    /**
     * 获取需要解绑的唯一码
     * @param staff
     * @param item 上架解绑商品
     * @param codes 未过滤的唯一码
     * @param uniqueCodes 过滤后需要解绑的唯一码
     * @param config 唯一码配置
     */
    @Transactional
    public void getUnboundUniqueCodesByItem(Staff staff, PdaOnShelfUnboundItem item, List<WaveUniqueCode> codes, List<WaveUniqueCode> uniqueCodes, OrderUniqueCodeConfig config) {
        List<WaveUniqueCode> codeList = codes.stream().filter(code -> Objects.equals(item.getSysItemId(), code.getSysItemId()) && Objects.equals(item.getSysSkuId(), DataUtils.getZeroIfDefault(code.getSysSkuId()))).collect(toList());
        if (CollectionUtils.isEmpty(codeList)) {
            return;
        }
        List<WaveUniqueCode> needUnboundCodes = Lists.newArrayList(); // 上架商品需要解绑的唯一码
        // 解绑单件唯一码
        List<WaveUniqueCode> singleCodes = codeList.stream().filter(code -> WaveUniqueCode.CodeTypeEnum.SINGLE.getValue().equals(code.getCodeType())).limit(item.getTotalNum()).collect(Collectors.toList());
        needUnboundCodes.addAll(singleCodes);
        logger.debug(String.format("PDA上架解绑的单件类型唯一码: %s , 解绑数量: %s", singleCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()), singleCodes.size()));
        // 解绑多件唯一码
        if (item.getTotalNum() > singleCodes.size()) {
            List<WaveUniqueCode> multiCodes = codeList.stream().filter(code -> WaveUniqueCode.CodeTypeEnum.MULTI.getValue().equals(code.getCodeType())).collect(Collectors.toList());
            // 校验员工店铺权限
            multiCodes = filterByUser(staff, multiCodes);
            if (CollectionUtils.isEmpty(multiCodes)) {
                logger.debug("员工店铺权限过滤后没有能解绑的多件唯一码！");
            } else {
                for (WaveUniqueCode code : multiCodes) {
                    // 拆分前商品数量
                    int itemNum = 0;
                    List<TbOrder> orders = tbOrderDAO.queryByIds(staff, code.getOrderId());
                    if (!CollectionUtils.isEmpty(orders)) {
                        itemNum = orders.get(0).getNum();
                    }
                    try {
                        List<WaveUniqueCode> waveUniqueCodes = OrderUniqueCodeUtils.filterFinalGenerates(queryBySid(staff, code.getSid())); // 订单下的所有唯一码
                        if (waveUniqueCodes.size() == 1) { // 拆单后的订单只剩一个多件类型唯一码时不需要拆分
                            needUnboundCodes.add(waveUniqueCodes.get(0));
                            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(waveUniqueCodes.get(0))), WaveUniqueOpType.PDA_ON_SHELF_MULTI_SPLIT}), null);
                        } else {
                            List<String> codeStrs = waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).filter(uniqueCode -> !code.getUniqueCode().equals(uniqueCode)).collect(Collectors.toList()); // 解绑的唯一码拆到新订单中，其余唯一码保留在原订单
                            logger.debug(String.format("PDA上架，多件类型唯一码拆分,保留在原订单唯一码: %s,绑定到新订单唯一码: [%s]", codeStrs, code.getUniqueCode()));
                            WaveTradeSplitResult result = orderUniqueCodeExamineService.pdaOnShelfSplitTrades(staff, codeStrs, false, false, false, false, null, config, true);
                            if (result.getSuccess()) {
                                needUnboundCodes.add(code);
                                eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(Lists.newArrayList(code)), WaveUniqueOpType.PDA_ON_SHELF_MULTI_SPLIT}), null);
                            }
                        }
                    } catch (Exception e) {
                        logger.debug("PDA上架解绑外采, 唯一码多件订单拆分失败！");
                        // 如果是订单拆分成功,唯一码拆分失败,需要将未成功拆分到新订单的唯一码解绑
                        List<TbOrder> checkOrders = tbOrderDAO.queryByIds(staff, code.getOrderId());
                        if (!CollectionUtils.isEmpty(checkOrders)) {
                            int checkNum = orders.get(0).getNum();
                            // 拆分前后子订单数量不一致表示订单拆分了
                            if (itemNum != checkNum) {
                                logger.debug(String.format("订单拆分成功,唯一码拆分失败！需要解绑唯一码:%s", code.getUniqueCode()));
                                needUnboundCodes.add(code);
                            }
                        }
                        break;
                    }
                    if (needUnboundCodes.size() >= item.getTotalNum()) {
                        break;
                    }
                }
            }
        }
        logger.debug(String.format("商品 [sysItemId: %s sysSkuId: %s] 需要解绑的唯一码: %s", item.getSysItemId(), item.getSysSkuId(), needUnboundCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList())));
        uniqueCodes.addAll(needUnboundCodes);
    }

    private List<WaveUniqueCode> filterByUser(Staff staff, List<WaveUniqueCode> multiCodes) {
        List<User> users = staff.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            logger.debug(String.format("员工%s[%s]没有店铺权限,多件订单无法拆分!", staff.getName(), staff.getId()));
            return null;
        }
        List<Long> userIds = users.stream().map(User::getId).collect(toList());
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, multiCodes.stream().map(WaveUniqueCode::getSid).filter(DataUtils::checkLongNotEmpty).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(tbTrades)) {
            return null;
        }
        List<Long> filterSids = tbTrades.stream().filter(t -> userIds.contains(t.getUserId())).map(TbTrade::getSid).collect(toList());
        multiCodes = multiCodes.stream().filter(u -> filterSids.contains(u.getSid())).collect(toList());
        return multiCodes;
    }


    /**
     * 填充平台图片
     * @param code
     * @param picPath
     */
    private void fillPlatformPicPath(WaveUniqueCode code, String picPath) {
        if (StringUtils.isEmpty(picPath)) {
            code.setPlatformPicPath("/resources/css/build/images/no_pic.png");
        } else {
            code.setPlatformPicPath(picPath);
        }
    }
    /**
     * 获取唯一码采购单关联关系
     * @param codes
     * @return
     */
    private List<UniqueCodeRelation> getPurchaseOrderRelationList(List<WaveUniqueCode> codes) {
        List<UniqueCodeRelation> purchaseOrderRelationList = Lists.newArrayList();
        for (WaveUniqueCode code : codes) {
            UniqueCodeRelation purchaseOrderRelation = new UniqueCodeRelation();
            purchaseOrderRelation.setUniqueCodeId(0L);
            purchaseOrderRelation.setUniqueCode(code.getUniqueCode());
            purchaseOrderRelation.setBusinessId(0L);
            purchaseOrderRelation.setBusinessCode(code.getBusinessCode());
            purchaseOrderRelation.setBusinessType(UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType());
            purchaseOrderRelation.setSubBusinessId(0L);
            purchaseOrderRelation.setCreated(code.getCreated());
            purchaseOrderRelation.setStatus(code.getStatus());
            purchaseOrderRelationList.add(purchaseOrderRelation);
        }
        return purchaseOrderRelationList;
    }

    /**
     * 商品标签-商品对应关系异常换商品
     *
     * @param staff
     * @param uniqueCodes
     */
    @Override
    public boolean changeItem(Staff staff, List<String> uniqueCodes) {
        TradeConfig config = tradeServiceDubbo.queryTradeConfig(staff);
        if (ObjectUtils.isEmpty(config)) {
            return false;
        }
        String changeItemException = String.valueOf(config.get("changeItemException"));
        List<String> changeItemExceptions = ArrayUtils.toStringList(changeItemException);
        if (changeItemExceptions == null || !changeItemExceptions.contains("EX_RELATION_MODIFIED")) {
            throw new IllegalArgumentException("请先在快进快出配置-异常订单允许智能换商品的类型勾选商品对应关系改动！");
        }
        List<WaveUniqueCode> waveUniqueCodes = queryByUniqueCodes(staff, uniqueCodes);
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            throw new IllegalArgumentException("唯一码不存在！");
        }
        List<WaveUniqueCode> needChangeCodes = Lists.newArrayList(); // 需要换商品的唯一码
        List<TradeChangeItemMiddle> middles = buildMiddles(staff, waveUniqueCodes, needChangeCodes);
        if (!waveHelpBusiness.lockBefore(staff, ProgressEnum.PROGRESS_ORDER_UNIQUE_DO_CHANGE_ITEM, 100)) {
            return false;
        }
        eventCenter.fireEvent(this, new EventInfo("trade.change.item.do").setArgs(new Object[]{staff, middles, Boolean.TRUE, needChangeCodes}), null);
        return true;
    }


    /**
     * 构建TradeChangeItemMiddle(不存储）
     * @param staff
     * @param waveUniqueCodes
     * @param needChangeCodes 需要换商品的唯一码
     * @return
     */
    private List<TradeChangeItemMiddle> buildMiddles(Staff staff, List<WaveUniqueCode> waveUniqueCodes, List<WaveUniqueCode> needChangeCodes) {
        List<TradeChangeItemMiddle> middles = Lists.newArrayList();
        List<Long> sids = waveUniqueCodes.stream().map(WaveUniqueCode::getSid).filter(DataUtils::checkLongNotEmpty).distinct().collect(toList());
        List<String> uniqueCodes = waveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList());
        logger.debug(String.format("勾选唯一码关联订单: %s", sids));
        Assert.notEmpty(sids, "唯一码未绑定订单！");

        // 1. 查询订单关联所有订单唯一码
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(sids);
        List<WaveUniqueCode> allCodes = queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(allCodes, "唯一码不存在！");

        // 2. 查询订单、子订单，包含合单隐藏单
        List<Trade> trades = waveUseTradeServiceProxy.queryBySidsContainMergeTrade(staff,true, false, sids.toArray(new Long[0]));
        Assert.notEmpty(trades, "订单不存在！");
        List<Order> orders = TradeUtils.getOrders4Trade(trades);
        Assert.notEmpty(orders, "子订单不存在！");
        // 子订单userId为null,要填充下
        Map<Long, Long> sidUserIdMap = trades.stream().collect(toMap(Trade::getSid, Trade::getUserId, (a, b) -> a));
        orders.forEach(order -> order.setUserId(sidUserIdMap.get(order.getSid())));

        // 3. 处理套件，包含套件本身、套件明细
        Map<Long, Order> orderMap = new HashMap<>(orders.size() + 1);
        for (Order order : orders) {
            orderMap.put(order.getId(), order);
            if (!CollectionUtils.isEmpty(order.getSuits())) {
                for (Order suit : order.getSuits()) {
                    orderMap.put(suit.getId(), suit);
                }
            }
        }
        // 4. 按订单商品维度将唯一码分组，套件按订单套件维度(key:套件本身orderId)
        allCodes = allCodes.stream().peek(code -> {
            Order order = orderMap.get(code.getOrderId());
            if(OrderUniqueCodeUtils.isSuitDetail(order)) {
                code.setOrderId(order.getCombineId());
            }
        }).collect(toList());
        Map<Long, List<WaveUniqueCode>> orderIdCodeMap = allCodes.stream().collect(groupingBy(WaveUniqueCode::getOrderId));

        // 5. 按订单商品维度判断唯一码状态并构建TradeChangeItemMiddle(只换勾选唯一码对应商品/套件）
        List<Long> orderIds = allCodes.stream().filter(code -> uniqueCodes.contains(code.getUniqueCode())).map(WaveUniqueCode::getOrderId).filter(DataUtils::checkLongNotEmpty).distinct().collect(toList());
        logger.debug(String.format("商品/套件本身的子订单id:%s", orderIds));
        for (Long orderId : orderIds) {
            List<WaveUniqueCode> codes = orderIdCodeMap.get(orderId);
            if (CollectionUtils.isEmpty(codes)) {
                continue;
            }
            // 5.1 换商品仅支持等待拣选/等待采购/等待收货
            codes = codes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus())
                    || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                    || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(codes)) {
                logger.debug(String.format("子订单：%s, 没有状态为等待拣选/等待采购/等待收货唯一码!", orderId));
                continue;
            }
            // 5.2 根据子订单获取商品对应关系表
            Order order = orderMap.get(orderId);
            if (order == null) {
                logger.debug(String.format("未查询到子单 orderId: %s", orderId));
                continue;
            }
            // numIid skuId同时为空，非平台订单
            if (order.getNumIid() == null && order.getSkuId() == null) {
                logger.debug(String.format("订单[%s]非平台订单！", order.getSid()));
                continue;
            }
            // 根据userId、numIid、skuId查询商品对应表
            String skuId = order.getSkuId() == null ? "0" : order.getSkuId();
            List<SkuERPBridge> skuERPBridges = itemServiceDubbo.querySkuErpBridgesByNumIidsAndSkuIds(staff, order.getUserId(), Lists.newArrayList(order.getNumIid()), Lists.newArrayList(skuId));
            if (CollectionUtils.isEmpty(skuERPBridges)) {
                logger.debug(String.format("numIid:%s skuId:%s,未查询到商品对应关系!", order.getNumIid(), skuId));
                continue;
            }
            SkuERPBridge skuERPBridge = skuERPBridges.get(0);
            Long sysItemId = skuERPBridge.getSysItemId();
            Long sysSkuId = skuERPBridge.getSysId();
            if (Objects.equals(sysItemId, -1L) && Objects.equals(sysSkuId, -1L)) {
                logger.debug(String.format("平台商品[numIid:%s skuId:%s]未匹配系统商品", order.getNumIid(), skuId));
                continue;
            }

            // 5.3 根据平台对应关系表获取系统商品
            String itemKey = WmsKeyUtils.buildItemKey(sysItemId, DataUtils.getZeroIfDefault(sysSkuId));
            Map<String, DmjItem> dmjItemMap = waveHelpBusiness.queryItemMap(staff, Lists.newArrayList(sysItemId), Lists.newArrayList(DataUtils.getZeroIfDefault(sysSkuId)));
            DmjItem dmjItem = dmjItemMap.get(itemKey);
            if (dmjItem == null) {
                logger.debug(String.format("商品[%s]不存在！", itemKey));
                continue;
            }
            String afterOuterId = dmjItem.getOuterId();
            Long afterSysItemId = dmjItem.getSysItemId();
            Long afterSysSkuId = dmjItem instanceof DmjSku ? ((DmjSku) dmjItem).getSysSkuId() : 0L;

            // 5.4 构建TradeChangeItemMiddle, 套件换商品时整体更换(取套件本身商家编码、sysItemId、sysSKuId)
            if (Objects.equals(WmsKeyUtils.buildOrderKey(order), WmsKeyUtils.buildItemKey(afterSysItemId, afterSysSkuId))) {
                logger.debug(String.format("订单 %s 商品：%s 换商品前后，商品一致!", order.getSid(), WmsKeyUtils.buildOrderKey(order)));
                continue;
            }
            TradeChangeItemMiddle middle = new TradeChangeItemMiddle();
            middle.setAfterOuterId(afterOuterId);
            middle.setAfterSysItemId(afterSysItemId);
            middle.setAfterSysSkuId(afterSysSkuId);
            middle.setSysItemId(order.getItemSysId());
            middle.setSysSkuId(DataUtils.getZeroIfDefault(order.getSkuSysId()));
            // 合单取主单sid
            middle.setSids(DataUtils.checkLongNotEmpty(order.getBelongSid()) ? order.getBelongSid().toString() : order.getSid().toString());
            middle.setNeedSplitTrade(0);
            middle.setNotNeedSplitWarehouseIds("");
            middle.setPropertiesName("");
            middle.setRemark("");
            middles.add(middle);
            needChangeCodes.addAll(codes);
        }
        if (!CollectionUtils.isEmpty(middles)) {
            // 填充更换前商品相关信息
            tradeWaveProportionChangeItemService.fillInfo(staff, middles);
            // 填充更换后商品供应商信息
            tradeWaveProportionChangeItemService.fillSupplierInfo(staff, middles, true);
        }
        return middles;
    }



    @Override
    public void autoCreatePurchaseOrder(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return;
        }
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        Integer autoCreatePurchaseOrder = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.AUTO_CREATE_PURCHASE_ORDER.getKey(), 0);
        Integer sameSupplierNeedMerge = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.SAME_SUPPLIER_NEED_MERGE.getKey(), 0);
        if (logger.isDebugEnabled()) {
            logger.debug(String.format("外采唯一码自动生成唯一码采购单:%s,相同供应商自动合并:%s", autoCreatePurchaseOrder, sameSupplierNeedMerge));
        }
        if (Objects.equals(autoCreatePurchaseOrder, CommonConstants.JUDGE_NO)) {
            return;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        params.setNeedMerge(Objects.equals(sameSupplierNeedMerge, CommonConstants.JUDGE_YES));
        ProgressData progressData = new ProgressData();
        progressData.setCountAll(100);
        Assert.isTrue(!waveProgressBusiness.hasProgress(staff, ProgressEnum.GENERATE_PURCHASE_UNIQUE_CODE_ORDER), "正在计算，请稍候！");
        waveProgressBusiness.setProgress(staff, ProgressEnum.GENERATE_PURCHASE_UNIQUE_CODE_ORDER, progressData);

        eventCenter.fireEvent(this, new EventInfo("purchase.uniqueCode.generate").setArgs(new Object[]{staff, params}), null);


    }

    @Override
    @Transactional
    public void batchReturnExchange4PurchaseReturn(Staff staff, OrderUniqueCodeQueryParams params) {
        List<String> uniqueCodes = params.getUniqueCodes();
        Assert.notEmpty(uniqueCodes, "唯一码不能为空！");
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "唯一码不存在！");
        codes = codes.stream().filter(code -> DataUtils.checkLongNotEmpty(code.getSid())).collect(toList());
        if (CollectionUtils.isEmpty(codes)) {
            logger.debug("扫描采退执行退档换货,无绑定订单唯一码！");
            return;
        }
        // 退档换货
        batchReturnExchange(staff, codes);
    }

    @Override
    @Transactional
    public void defectiveUniqueCodesAutoReturnExchange(Staff staff, List<WaveUniqueCode> defectiveUniqueCodes) {
        if (CollectionUtils.isEmpty(defectiveUniqueCodes)) {
            return;
        }
        // 快进快出配置-质检次品自动执行退档换货
        OrderUniqueCodeConfig config = orderUniqueCodeConfigService.get(staff);
        Integer autoReturnExchange  = (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.DEFECTIVE_UNIQUE_CODE_AUTO_RETURN_EXCHANGE.getKey(), 0);
        if (Objects.equals(autoReturnExchange, 0)) {
            return;
        }
        // 根据uniqueCode重新查唯一码判断是否有需要退档换货的唯一码
        OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeQueryParams();
        queryParams.setUniqueCodes(defectiveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(toList()));
        List<WaveUniqueCode> codes = queryOrderUniqueCodeByCondition(staff, queryParams);
        defectiveUniqueCodes = Optional.ofNullable(codes).orElse(new ArrayList<>()).stream()
                .filter(code -> Objects.equals(code.getStockPosition(), UniqueCodeStockPositionEnum.DEFECTIVE_AREA.getType()) && DataUtils.checkLongNotEmpty(code.getSid())).collect(toList());
        if (CollectionUtils.isEmpty(defectiveUniqueCodes)) {
            logger.debug("质检扫描无绑定订单的次品唯一码,不需要执行退档换货！");
            return;
        }
        // 退档换货
        batchReturnExchange(staff, defectiveUniqueCodes);
    }

    private void batchReturnExchange(Staff staff, List<WaveUniqueCode> codes) {
        Assert.isTrue(waveProgressBusiness.addProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE), "正在执行退档换货，请稍后重试...");
        ProgressData progressData = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE);
        progressData.setCountAll(100);
        progressData.setSucNum(0L);
        waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData);
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData, 10);

        try {
            beanProxy.doBatchReturnExchange(staff, progressData, codes);
        } catch (Exception e) {
            logger.debug("执行退档换货失败！原因：" + e.getMessage());
            throw e;
        } finally {
            progressData.setProgress(2);
            waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE, progressData);
            waveProgressBusiness.clearProgress(staff, ProgressEnum.PROGRESS_RETURN_EXCHANGE_ORDER_UNIQUE_CODE);
        }
    }
    @Override
    public void fillLogisticsCompanyOrTemplate(Staff staff, List<WaveUniqueCode> codes) {
        boolean hasPrintTemplateIntegrate = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
        // 填充快递公司
        if (hasPrintTemplateIntegrate) {
            fillLogisticsCompanyName(staff, codes);
        } else {
            // 填充快递模版
            fillTemplateName(staff, codes);
        }
    }

    @Override
    public boolean checkVisibleColumn(Staff staff, Long pageId, String colCode) {
        if (pageId == null || StringUtils.isEmpty(colCode)) {
            return false;
        }
        List<String> visibleColumnFieldList;
        if (Objects.equals(pageId, 2000091L)) {
            visibleColumnFieldList = waveColumnConfService.getPageVisibleColumnFieldList(staff, pageId);
        } else {
            visibleColumnFieldList = waveColumnConfService.getVisibleColumnFieldList(staff, pageId);
        }
        return !CollectionUtils.isEmpty(visibleColumnFieldList) && visibleColumnFieldList.contains(colCode);
    }

    @Override
    @Transactional
    public void cancelReject4OnShelf(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        // 取消复制新增
        doCancelReject(staff, codes, false);
    }

    private void doCancelReject(Staff staff, List<WaveUniqueCode> codes, boolean isNewSplit) {
        Assert.isTrue(waveProgressBusiness.addProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE), "正在执行取消复制新增，请稍后重试...");
        ProgressData progressData = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE);
        progressData.setCountAll(100);
        progressData.setSucNum(0L);
        waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData);
        waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData, 10);

        try {
            beanProxy.doBatchCancelReject(staff, progressData, codes, isNewSplit);
        } catch (Exception e) {
            logger.debug("执行取消复制新增失败！原因：" + e.getMessage());
            throw e;
        } finally {
            progressData.setProgress(2);
            waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE, progressData);
            waveProgressBusiness.clearProgress(staff, ProgressEnum.PROGRESS_CANCEL_REJECT_ORDER_UNIQUE_CODE);
        }
    }

    public boolean containsAnyVisibleColumn(Staff staff, Long pageId, List<String> colCodes) {
        if (pageId == null || CollectionUtils.isEmpty(colCodes)) {
            return false;
        }
        List<String> visibleColumnFieldList;
        if (Objects.equals(pageId, 2000091L)) {
            visibleColumnFieldList = waveColumnConfService.getPageVisibleColumnFieldList(staff, pageId);
        } else {
            visibleColumnFieldList = waveColumnConfService.getVisibleColumnFieldList(staff, pageId);
        }
        return !CollectionUtils.isEmpty(visibleColumnFieldList) && colCodes.stream().anyMatch(visibleColumnFieldList::contains);
    }
}

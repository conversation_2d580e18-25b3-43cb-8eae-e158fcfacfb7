<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.raycloud.dmj</groupId>
		<artifactId>dmj-services-trades</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>dmj-services-trades-support</artifactId>
	<name>DMJ Services Trades Support</name>
	<description>订单服务的API接口默认实现</description>

	<dependencies>
		<dependency>
			<groupId>com.raycloud</groupId>
			<artifactId>ibatis-plugin</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>erp-record-core</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-wave-support</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-wms-services-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-items-services-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-print-base-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-items-stock</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>erp-purchase-wx-api</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>erp-purchase-wx-domain</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>dmj-services-trades-stock</artifactId>
            <version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-kdniao-support</artifactId>
		</dependency>
		<!--ERP锁-->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-lock</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!--大卖家模块存储功能-->
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-services-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-storage-oss</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>*</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
				<exclusion>
					<artifactId>poi</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml-schemas</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-services-api-commons</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-services-trades-platform-api</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba.rocketmq</groupId>
					<artifactId>rocketmq-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<dependency>
			<groupId>com.raycloud.cache</groupId>
			<artifactId>cache-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.raycloud.ec</groupId>
			<artifactId>ec-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.raycloud.cache</groupId>
			<artifactId>cache-simple</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dubbo</artifactId>
		</dependency>
		<dependency>
			<groupId>zkclient</groupId>
			<artifactId>zkclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
            <exclusions>
                <exclusion>
					<groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>com.raycloud</groupId>
			<artifactId>secret-api-interface</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
		</dependency>

		<!--店长的电子面单对接网点接口-->
		<dependency>
			<groupId>com.raycloud</groupId>
			<artifactId>express-service-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-jd-trade</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-jd-logistics</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>erp-tb-trade</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>erp-tb-logistics</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.erp</groupId>
			<artifactId>erp-buffer-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.erp</groupId>
			<artifactId>erp-buffer-support</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.commons</groupId>
			<artifactId>xserial-number-api</artifactId>
		</dependency>


		<!--hystrix-->
		<dependency>
			<groupId>com.netflix.hystrix</groupId>
			<artifactId>hystrix-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.netflix.hystrix</groupId>
			<artifactId>hystrix-javanica</artifactId>
		</dependency>

		<dependency>
			<groupId>com.kuaidizs.wly</groupId>
			<artifactId>kdzs-wly-api</artifactId>
			<version>1.3.1-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.kuaidizs.wly</groupId>
					<artifactId>kdzs-wly-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.github.rholder</groupId>
					<artifactId>guava-retrying</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>pac.sdk.cp</artifactId>
					<groupId>pac.sdk</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.jd.open</groupId>
					<artifactId>open-api-sdk</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.kuaidizs.wly</groupId>
			<artifactId>kdzs-wly-common</artifactId>
			<version>1.3.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
					<groupId>org.apache.poi</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
				<exclusion>
					<groupId>com.github.rholder</groupId>
					<artifactId>guava-retrying</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>pac.sdk.cp</artifactId>
					<groupId>pac.sdk</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.jd.open</groupId>
					<artifactId>open-api-sdk</artifactId>
				</exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-api-trades</artifactId>
			<version>${version.erpkjapi}</version>
		</dependency>
		<dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-api-trades</artifactId>
            <version>${version.erpkjapi}</version>
        </dependency>
		<dependency>
			<groupId>com.taobao.sdk</groupId>
			<artifactId>sdk</artifactId>
			<version>${version.tb.sdk}</version>
		</dependency>
		<!--快递助手网点服务-->
		<dependency>
			<groupId>com.kuaidizs.express</groupId>
			<artifactId>kdzs-express-service-api</artifactId>
			<version>erp.0.5-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.jd.open</groupId>
					<artifactId>open-api-sdk</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>lts</artifactId>
            <version>1.7.2</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-fms-services-api</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-dms-service-api</artifactId>
			<version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-newfx-trade</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-item-search-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud.commons</groupId>
            <artifactId>xserial-number-api</artifactId>
        </dependency>
		<dependency>
			<groupId>com.raycloud.middle</groupId>
			<artifactId>sdk-client</artifactId>
			<version>2.0.8-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-supply-transfer-service</artifactId>
            <version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.github.rholder</groupId>
			<artifactId>guava-retrying</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-services-print</artifactId>
            <version>${project.version}</version>
        </dependency>

		<!-- 售后 -->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-domain-aftersale</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-aftersale-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-service-basis-support</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-service-basis-support</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- ERP测试工具包 -->
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>test-utils</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
		<!--订单池API-->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-trades-orderpool-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- 外链 -->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-domain-share</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- DAO-->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-trades-dao</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!--订单池API-->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-trades-orderpool-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- 订单配置服务 -->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-trades-config</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-logistics-warning-service-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- 新的订单异常服务，旧的以后会下掉 -->
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-trade-except</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-print-express-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-logistics-warning-service-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-waybill-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-services-caigou-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.weidian</groupId>
			<artifactId>open_security_sdk</artifactId>
			<version>${version.weidian.security}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.0.6</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-api-items-stock-product</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>erp-data-dubbo-provider-api</artifactId>
			<version>${version.erpdata}</version>
		</dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-rule-match-api</artifactId>
			<version>${project.version}</version>
        </dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-trades-stock-api</artifactId>
			<version>${project.version}</version>
		</dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-rematch-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-api-trades-upload</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>dmj-services-trades-stalls</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.raycloud.dmj</groupId>
			<artifactId>dmj-print-rulematch-api</artifactId>
			<version>${project.version}</version>
		</dependency>
	<dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-services-trades-gift</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazon.sdk</groupId>
            <artifactId>amazon-sdk</artifactId>
            <version>raycloud_2021120115</version>
            <scope>compile</scope>
        </dependency>
	</dependencies>

</project>
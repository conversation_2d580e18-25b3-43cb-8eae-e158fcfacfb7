package com.raycloud.dmj.services.trades.support.search;


import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.trade.UnionTradeDecryptBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.StringBlurUtil;
import com.raycloud.dmj.domain.trades.utils.TradeConfigUtils;
import com.raycloud.dmj.domain.trades.utils.TradeKjUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;


/**
 * @Description <pre>
 * 总的原则
 * 1 对于系统加密订单(含手工单) 如果对应店铺平台支持加解密的 则做系统脱敏处理 否则展示明文(如档口店铺订单)
 * 2 对于未走解密流程的密文  直接展示为 "****"
 * 3 其他需过滤或者需屏蔽的(跨境业务 BuyerNick存储的是仓库信息 不能脱敏) 特殊处理
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-07
 */
@Component
public class TradeSearchBlurService implements InitializingBean {

    @Resource
    private UnionTradeDecryptBusiness unionTradeDecryptBusiness;

    private static TradeSearchBlurService instance;

    public static TradeSearchBlurService getInstance() {
        return instance;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        instance = this;
    }

    /**
     * <pre>
     *     系统脱敏,这里不会调用平台脱敏接口 可以看做一个最终的兜底
     *     密文将直接替换为 ****
     *
     *     手机号 131****1234
     *     姓名   张*   王*二
     *     地址   麓谷信息****
     *
     *
     *     已脱敏数据不处理
     *
     * </pre>
     *
     * @param staff
     * @param tbTrade
     * @param tradeConfig
     */
    public void blurData(Staff staff, Trade tbTrade,TradeConfig tradeConfig) {

        if (tbTrade.getOriReceiverAddress() == null) {
            tbTrade.setOriReceiverAddress(tbTrade.getReceiverAddress());
        }
        if (tbTrade.getOriReceiverMobile() == null) {
            tbTrade.setOriReceiverMobile(tbTrade.getReceiverMobile());
        }
        if (tbTrade.getOriReceiverName() == null) {
            tbTrade.setOriReceiverName(tbTrade.getReceiverName());
        }
        if (tbTrade.getOriBuyerNick() == null) {
            tbTrade.setOriBuyerNick(tbTrade.getBuyerNick());
        }


        //屏蔽厂商代发订单具体地址加密信息
        if (CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(tbTrade.getSource())) {
            tbTrade.setReceiverAddress("****");
        }
        //针对无法解密的密文收件信息（小平台第三方渠道平台订单）  页面展示"***"处理
        handleUnDecryptReceiverInfo(staff,tbTrade);
        String userRefSource = null;
        if (staff.getUserIdMap() != null) {
            User user = staff.getUserIdMap().get("newfx".equalsIgnoreCase(tbTrade.getSource()) ? tbTrade.getTaobaoId() : tbTrade.getUserId());
            if (user != null) {
                userRefSource = user.getSource();
            }
        }
        if (CommonConstants.PLAT_FORM_TYPE_FXG_CS.equals(userRefSource)) {
            // 抖音超市收件人信息展示***
            commonBlur(staff,tbTrade,true);
        }
        if (TradeUtils.platformMatch(staff, tbTrade, CommonConstants.PLATFROM_TYPE_THH)) {
            tbTrade.setReceiverPhone("");
        }

        //优先取前端传入的控制值 否则取配置值
        Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(tradeConfig);
        Integer blurTrade = null;
        if (configMap.containsKey("blurTradeFromWeb") && configMap.get("blurTradeFromWeb") != null ) {
            blurTrade = (Integer) configMap.get("blurTradeFromWeb");
        }else {
            blurTrade = tradeConfig.getInteger("blurTrade");
        }

        //通过TJ进入模糊敏感信息
        if ((!StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague())) || blurTrade == 1) {
            //默认查询出来是1    前端传值会修改为0 但是不会保存到数据库
            if (tradeConfig.getInteger("blurTrade") == 1) {
                blurTradePlatform(tbTrade, staff,userRefSource);
            } else {
                TradeUtils.blurTrade(tbTrade);
            }
        }else{
            //没开启模糊的情况下 如果数据本身是密文,并且不是前端指定的blurTrade 仍转换为****
            if (configMap.containsKey("blurTradeFromWeb") && configMap.get("blurTradeFromWeb") != null ) {
                if (TradeUtils.ifEncrypt(tbTrade.getReceiverMobile())) {
                    tbTrade.setReceiverMobile("****");
                }
                if (TradeUtils.ifEncrypt(tbTrade.getReceiverAddress())) {
                    tbTrade.setReceiverAddress("****");
                }
                if (TradeUtils.ifEncrypt(tbTrade.getReceiverName())) {
                    tbTrade.setReceiverName("****");
                }
                if (TradeUtils.ifEncrypt(tbTrade.getBuyerNick())) {
                    tbTrade.setBuyerNick("****");
                }
            }
        }
    }

    /**
     * 模糊订单中敏感信息
     *
     * @param trade
     * @param staff
     */
    private void blurTradePlatform(Trade trade, Staff staff,String userRefSource) {
        if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_JD)) {
            if (!StringUtils.contains(trade.getReceiverAddress(), "***")) {//如果地址是脱密的  直接显示脱密就行  不需要再设置***
                trade.setReceiverAddress("****");
                trade.setReceiverName("****");
                if (!StringUtils.contains(trade.getReceiverMobile(), "**")) {
                    trade.setReceiverMobile("****");
                    trade.setReceiverPhone("****");
                }
                trade.setBuyerNick("****");
            }
            return;
        }
        if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_QIMEN)) {
            trade.setReceiverAddress("****");
            trade.setReceiverName("****");
            if (!StringUtils.contains(trade.getReceiverMobile(), "**")) {
                trade.setReceiverMobile("****");
                trade.setReceiverPhone("****");
            }
            trade.setBuyerNick("****");
            return;
        }
        if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TAO_BAO)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_1688_C2M)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_1688)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_FX)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TMCS)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TMGJZY)
                || TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_TMYP)) {
            String receiverAddress = trade.getReceiverAddress();
            commonBlur(staff,trade,false);
            trade.setReceiverAddress(receiverAddress);
            return;
        }

        /**
         * 数据本身加密的平台
         */
        if (unionTradeDecryptBusiness.isDecryptSupported(trade.getSource() )
                || unionTradeDecryptBusiness.isDecryptSupported(trade.getSubSource())
                || unionTradeDecryptBusiness.isDecryptSupported(userRefSource)
            ) {
            //跨境业务 BuyerNick存储的是仓库信息 不能脱敏
            commonBlur(staff,trade,TradeUtils.platformMatch(staff,trade, TradeKjUtils.KJ_SOURCE_LIST.toArray(new String[0])));
            return;
        }
    }

    public static void commonBlur(Staff staff,Trade trade,boolean handleBuyerNick) {
        trade.setReceiverAddress(commonBlur(trade.getReceiverAddress(), 10,1,"**"));
        trade.setReceiverName(commonBlur(trade.getReceiverName(), 1,1,"*"));
        trade.setReceiverMobile(commonBlur(trade.getReceiverMobile(), 3,4,"****"));
        if (handleBuyerNick) {
            trade.setBuyerNick(commonBlur(trade.getBuyerNick(), 1,1,"*"));
        }

    }

    /**
     *
     *
     * @param data
     * @param nonBlurCharCount
     * @return
     */
    private static String commonBlur(String data, Integer start,Integer end,String fill) {
        if (StringUtils.isBlank(data)){
            return data;
        }
        if (TradeUtils.ifEncrypt(data)) {
            return "****";
        }
        if (TradeUtils.ifSensitived(data) && data.length() < 20) {
            return data;
        }
        return StringBlurUtil.commonBlur(data,start,end,fill);
    }

    /**
     * 处理无法解密的收件人信息显示（一些平台的第三方渠道订单）
     */
    private static void handleUnDecryptReceiverInfo(Staff staff,Trade t) {
        if (CommonConstants.PLAT_FORM_TYPE_XIAOMANG.equals(t.getSource())
                && (CommonConstants.PLAT_FORM_TYPE_FXG.equals(t.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_PDD.equals(t.getSubSource())
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(t.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_POISON.equals(t.getSubSource())
                || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(t.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_XHS.equals(t.getSubSource())
        )
                || (CommonConstants.PLAT_FORM_TYPE_SHULIANTONG.equals(t.getSource()) && CommonConstants.PLAT_FORM_TYPE_XHS.equals(t.getSubSource()))
        ) {
            //小芒平台抖店渠道订单
            commonBlur(staff,t,true);
        } else if (CommonConstants.PLAT_FORM_TYPE_1889.equals(t.getSource()) && CommonConstants.PLAT_FORM_TYPE_FXG.equals(t.getSubSource()) && CipherTextUtils.ifEncrypt(t)) {
            commonBlur(staff,t,false);
        } else if (TradeUtils.platformMatch(staff,t,CommonConstants.PLAT_FORM_TYPE_PDD)){
            //pdd为端解密 后端不支持解密
            commonBlur(staff,t,true);
        }


    }

}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.ExchangeRateDao;
import com.raycloud.dmj.domain.trades.ExchangeRate;
import com.raycloud.dmj.services.trades.IExchangeRateService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ExchangeRateService implements IExchangeRateService {

    @Resource
    ExchangeRateDao dao;

    @Override
    @Cacheable(value = "defaultCache#600", key = "'erp_exchange_rate_lastest_' + #from")
    public ExchangeRate queryLastestByFrom(String from) {
        return dao.queryLastestByFrom(from);
    }

    @Override
    public ExchangeRate queryLastestByFromAndDate(String from, Date createTime) {
        return dao.queryLastestByFromAndDate(from, createTime);
    }


    @Override
    public Long insert(ExchangeRate rate) {
        return dao.insert(rate);
    }

    @Override
    public void batchInsert( List<ExchangeRate> rates) {
        dao.batchInsert(rates);
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.business.buyout.TopUpBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.buyout.TopUpCompany;
import com.raycloud.dmj.domain.buyout.TopUpTransaction;
import com.raycloud.dmj.services.trades.ITopUpService;
import com.raycloud.dmj.services.trades.TradeException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @created 2019-04-04 17:43
 */
@Service
public class TopUpService implements ITopUpService {

    @Resource
    TopUpBusiness topUpBusiness;

    @Override
    public TopUpCompany getByCompanyId(Staff staff) {
        return topUpBusiness.getByCompanyId(staff);
    }

    @Override
    public void topup(Staff staff, TopUpTransaction transaction) {
        topUpBusiness.topup(staff, transaction);
    }

    @Override
    public TopUpCompany check(Staff staff, boolean throwError) {
        if (staff.getCompany().getOrderType() - 1 == 0) {
            TopUpCompany tuc = getByCompanyId(staff);
            if (tuc == null || tuc.getAmount() <= 0) {
                if (throwError) {
                    throw new TradeException("剩余单数不足,请先充值");
                }
            }
            return tuc;
        }
        return null;
    }
}

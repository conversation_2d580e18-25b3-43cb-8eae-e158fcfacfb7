package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.tb.trade.business.TaobaoSecretBusiness;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.ITaobaoSecretService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by yang<PERSON><PERSON> on 17/5/24.
 */
@Service
public class TaobaoSecretService implements ITaobaoSecretService {

    @Autowired
    TaobaoSecretBusiness taobaoSecretBusiness;

    @Override
    public TbTrade decode(User user, TbTrade tbTrade) {
        taobaoSecretBusiness.decode(user,tbTrade);
        return tbTrade;
    }
}

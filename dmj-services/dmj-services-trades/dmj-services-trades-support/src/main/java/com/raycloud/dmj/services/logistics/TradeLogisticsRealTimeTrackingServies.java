package com.raycloud.dmj.services.logistics;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.ILogisticsTrackingRecordService;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * TradeLogisticsRealTimeTrackingServies
 *  菜鸟物流
 * <AUTHOR>
 * @Date 2019-08-03
 * @Time 18:07
 */
@Service
public class TradeLogisticsRealTimeTrackingServies extends AbstractTradeLogisticsTrackingServices {

    private final String CACHE_KEY = "trade_logistics_real_time_tracking";

    private List<Integer> SYNC_TIME = Arrays.asList(16, 18, 20, 22, 8, 10);

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    ILogisticsTrackingRecordService logisticsTrackingRecordService;

    @Resource
    ICache cache;

    @Override
    public Integer getOrder() {
        return 0;
    }

    @Override
    public boolean isAllow(Staff staff) {
        boolean logisticsRealTime = tradeLocalConfigurable.isLogisticsRealTime(staff.getCompanyId());
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        if (!SYNC_TIME.contains(hour) || !logisticsRealTime) {
            return false;
        }
        boolean result = false;
        try {
            boolean add = cache.add(CACHE_KEY, "1", 60 * 60 * 24);
            boolean add1 = cache.add(CACHE_KEY + "_" + staff.getCompanyId(), "1", 60 * 60 * 24);
            boolean add2 = cache.add(CACHE_KEY + "_" + staff.getCompanyId() + "_" + hour, "1", 60 * 90);
            if (add && add1 && add2) {
                result =  true;
            } else {
                if (add) {
                    cache.delete(CACHE_KEY);
                }
                if (add1) {
                    cache.delete(CACHE_KEY + "_" + staff.getCompanyId());
                }
            }

        } catch (CacheException e) {
            Logs.error("缓存读取出错："+ e.getMessage());
            return false;
        }
        return result;
    }

    @Override
    public void invokeLogisticsInfo(Staff staff) {
        Long start = System.currentTimeMillis();
        logisticsTrackingRecordService.synLogisticsInfo(staff);
        Logs.debug(String.format("companyId:[%s],took:[%s]", staff.getCompanyId(), System.currentTimeMillis() - start));
    }

    @Override
    public void end(Staff staff) {
        try {
            cache.delete(CACHE_KEY);
            cache.delete(CACHE_KEY +"_"+ staff.getCompanyId());
        } catch (CacheException e) {
            Logs.error("缓存删除出错："+ e.getMessage());
        }
    }
}

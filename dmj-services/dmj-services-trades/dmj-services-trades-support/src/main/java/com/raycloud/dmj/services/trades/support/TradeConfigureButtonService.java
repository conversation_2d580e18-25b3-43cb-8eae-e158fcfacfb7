package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.TradeConfigureButtonDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeConfigureButton;
import com.raycloud.dmj.services.trades.ITradeConfigureButtonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: chenchaochao
 * @Date: 2020/4/9 2:22 下午
 */
@Service
public class TradeConfigureButtonService implements ITradeConfigureButtonService {

    @Resource
    private TradeConfigureButtonDao tradeConfigureButtonDao;

    private final String defaultConfigVal = "";

    @Override
    public void saveTradeConfigureButton(Staff staff, TradeConfigureButton tradeConfigureButton) {
        tradeConfigureButtonDao.insert(staff, tradeConfigureButton);
    }

    @Override
    public TradeConfigureButton getTradeConfigureButton(Staff staff) {
        TradeConfigureButton tradeConfigureButton = tradeConfigureButtonDao.get(staff);
        if(tradeConfigureButton == null){
            tradeConfigureButton = new TradeConfigureButton();
        }
        return  tradeConfigureButton;
    }
}

package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.dao.stock.StockOrderErrorDAO;
import com.raycloud.dmj.domain.StockOrderError;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.stock.IStockOrderErrorService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Auther mengfanguang
 * @Date 2023/9/7
 */
@Service
public class StockOrderErrorService implements IStockOrderErrorService {


    @Resource
    StockOrderErrorDAO stockOrderErrorDAO;

    @Override
    public void batchInsert(Staff staff, List<StockOrderError> errors) {
        if (CollectionUtils.isEmpty(errors)) {
            return;
        }
        stockOrderErrorDAO.batchInsert(staff, errors);
    }
}
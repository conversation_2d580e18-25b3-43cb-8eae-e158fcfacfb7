package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.conf.AppConfiguration;
import com.raycloud.dmj.domain.LocalConfigurable;
import com.raycloud.dmj.services.trades.IdWorkerTwoService;
import com.raycloud.dmj.web.utils.IpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.IdWorker;

import javax.annotation.Resource;

@Service
public class IdWorkerSupportService extends LocalConfigurable implements IdWorkerTwoService {

    protected final IdWorker idWorker;

    @Resource
    AppConfiguration appConfiguration;

    public IdWorkerSupportService() {
        //没有在config中配置的时候优先取tomcat中配置的 dmj.workerid
        int workerId = Integer.parseInt(System.getProperty("dmj.workerid", "0"));
        idWorker = new IdWorker(workerId);
    }

    @Override
    public long getFlushInterval() {
        return 10 * 60 * 1000;//每隔10分钟刷新一下
    }

    @Override
    protected String getTimerName() {
        return "idworker-flush-timer";
    }

    @Override
    public long nextId() {
        return idWorker.nextId();
    }

    @Override
    public void flush() {
        String key = getKey();
        String t = getProperty(key);
        if (t != null && !(t = t.trim()).isEmpty()) {
            System.out.println(key + ": " + t);
            idWorker.setWorkerId(Integer.parseInt(t));
        }
    }

    private String getKey() {
        //获取docker中设置的hostName，如gray3-erp-trades-0，gray3-erp-trades-1
        String hostName = IpUtils.getHostName();
        String index = "0";
        if (hostName != null) {
            //获取最后面的数字
            index = hostName.substring(hostName.lastIndexOf("-") + 1);
            if (!StringUtils.isNumeric(index)) {
                index = "0";
            }
        }
        return appConfiguration.getProject() + "-" + appConfiguration.getProfile() + "-" + index;
    }

}

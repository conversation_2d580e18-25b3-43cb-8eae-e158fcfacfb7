package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.trades.OrderHotItem;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.services.trades.support.search.OrderHotItemService;
import com.raycloud.dmj.services.trades.support.search.convert.order.IOrderConditionConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 * https://gykj.yuque.com/entavv/eltb2u/vhx3np15ki5nppg7
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_ORDER)
public class OrderConditionConverter extends AbsConditionConverter {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Autowired
    List<IOrderConditionConverter> conditionConverters;

    @Resource
    OrderHotItemService orderHotItemService;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return notNull(condition.getOrderRefCondition());
    }

    @Override
    protected void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
         Query orderQry = new Query();
         convert(staff,condition.getOrderRefCondition(),context,orderQry);

        if (orderQry.isStopQuery()) {
            q.setStopReason(orderQry.getStopReason());
            q.setStopQuery(true);
            return;
        }
        if (orderQry.getQ().length() > 0) {
            q.append(" AND t.sid in (")
                    .append(" select o.belong_sid from ").append(ConditionConstants.ORDER_TABLE_PLACE_HOLDER).append(" o where o.enable_status in (1,2)  AND o.company_id= ? ").add(staff.getCompanyId());
            andListCondition(q,"o.sys_status",TradeStatusUtils.getAllSysStatus());
            q.append(orderQry.getQ().toString()).add(orderQry.getArgs());
            q.append(")");
        }
    }

    public boolean  convert(Staff staff, OrderRefCondition condition,ConvertContext context, Query orderQry) {
        //hotQry 走热点商品表做初筛
        //orderQry 走order 表做二次筛选 理论上这个query应该是一个完整的条件 也就是说单独走这个query,其结果也应该和走了初筛后再过滤的结果应该是一致的 只是会慢
        Query hotQry = new Query();

        boolean added = false;
        DevLogBuilder timer = DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("order过滤sql构建 耗时统计").setBaseTooklmt(100L).startTimer();
        for (IOrderConditionConverter converter : conditionConverters) {
            added  = added | converter.convert(staff,context,condition,orderQry,hotQry);
            timer.recordTimer(converter.getClass().getSimpleName());
        }
        timer.startWatch().appendTook(timer.curEnabled()?1L:1000L).printDebug(logger);
        if (orderQry.isStopQuery()) {
            return false;
        }
        if (!added) {
            return false;
        }

        Set<Long> itemSysIds = new HashSet<>();
        Set<Long> skuSysIds = new HashSet<>();
        /**
         * 如果原始的查询条件本身就有 item_sys_id  sku_sys_id
         * 这几个order本身就有索引的字段 并且是精确查询的 这里实际直接走order表查询就可以了不用搂热点表
         *
         * 有 num_iid sku_id 的场景不多 理论上来说热点表数据量很小搂一次也很快 先忽略
         */
        if (!hotQry.isStopQuery() && hotQry.getQ().length() > 0 && ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getQueryByOrderHotItemCompanyIds(),staff.getCompanyId())) {
            DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("order过滤sql hotQry:").append(orderQry.getQ().toString()).printDebug(logger);
            // hotQry查询 转换为系统Id
            List<OrderHotItem> orderHotItems = orderHotItemService.queryHotItem(staff,hotQry);
            if (CollectionUtils.isNotEmpty(orderHotItems)) {
                itemSysIds = orderHotItems.stream().map(x->x.getItemSysId()).collect(Collectors.toSet());
                skuSysIds = orderHotItems.stream().map(x->x.getSkuSysId()).collect(Collectors.toSet());
            }

            //数据库里未匹配的 可能为0 和 -1
            if (itemSysIds.contains(-1L)) {
                itemSysIds.add(0L);
            }
        }

        // 有可能热点表数据还没有产生 因此这里仍要走二次筛选去兜底查一次(orderQry本身就是一个包含所有条件的sql)
        //if (CollectionUtils.isEmpty(ItemSysIds)) {
        //    q.setStopReason("热点商品表未查到到商品信息");
        //    q.setStopQuery(true);
        //    return;
        //}

        if (CollectionUtils.isNotEmpty(itemSysIds)) {
            orderQry.append(" AND ").append("(");
            listCondition(orderQry,"o.item_sys_id",itemSysIds,1);
            andListCondition(orderQry,"o.sku_sys_id",skuSysIds,1);
            orderQry.append(")");
        }

        DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("order过滤sql orderQry:").append(orderQry.getQ().toString()).printDebug(logger);



        return true;

    }



}

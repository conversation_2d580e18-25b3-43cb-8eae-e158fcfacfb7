package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertBase;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 *  混合信息查询
 *  这个Converter 涉及的条件 如果已匹配(item_sys_id > 0)则过滤系统信息 未匹配则过滤平台信息
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_MUTI)
public class MutiPropConverter extends AbsOrderConditionConverter {

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return notNull(condition.getItemTitle()) || notEmpty(condition.getOuterIds());
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        boolean added = false;
        Query sysRef = new Query();
        Query platRef = new Query();

        if (notNull(condition.getItemTitle())) {
            andSingleCondition(sysRef,"o.sys_title",condition.getItemTitle(),condition.getQueryType());
            andSingleCondition(platRef,"o.title",condition.getItemTitle(),condition.getQueryType());
            added = true;
        }

        Integer refType = condition.getOuterIdRefType();
        if (refType == null) {
            refType = 0;
        }

        if (notEmpty(condition.getOuterIds())) {
            andListCondition(sysRef,"o.sys_outer_id",condition.getOuterIds(),condition.getQueryType());
            platRef.append("AND (");
            listCondition(platRef,"o.outer_iid",condition.getOuterIds(),condition.getQueryType());
            orListCondition(platRef,"o.outer_sku_id",condition.getOuterIds(),condition.getQueryType());
            platRef.append(")");

            if (notNull(refType)) {
                if (Objects.equals(refType, 1)) {
                    sysRef.append(" AND o.type = 0 ");
                    platRef.append(" AND o.type = 0 ");
                } else if (Objects.equals(refType, 2)) {
                    sysRef.append(" AND o.type = 2 ");
                    platRef.append(" AND o.type = 2 ");
                } else if (Objects.equals(refType, 3)) {
                    sysRef.append(" AND o.type = 3 ");
                    platRef.append(" AND o.type = 3 ");
                }
                //热点商品表没有stock_status字段 放下面处理
                //else if (Objects.equals(refType, 4)) {
                //    sysRef.append(" AND o.stock_status ='INSUFFICIENT' ");
                //    platRef.append(" AND o.stock_status ='INSUFFICIENT' ");
                //}
            }

            added = true;
        }

        if (notNull(condition.getSkuOuterId())) {
            andSingleCondition(sysRef,"o.sys_outer_id",condition.getSkuOuterId(), ALL_MATCH);
            andSingleCondition(platRef,"o.outer_sku_id",condition.getSkuOuterId(),ALL_MATCH);
            added = true;
        }

        if (added) {
            /**
             * TradeSqlQueryBuilder 中的原代码
             * if (Objects.equals(params.getItemType(), 2) || (Objects.equals(params.getItemType(), 0) && StringUtils.isNotBlank(outerId)) || Objects.equals(params.getItemType(), 3))
             */
            List<Integer> needQueryCombineType = Arrays.asList(0,2,3);
            boolean needQryCombineSubOrder = notEmpty(condition.getOuterIds()) && needQueryCombineType.contains(refType);


            StringBuilder local = new StringBuilder();

            if (needQryCombineSubOrder) {
                local.append("AND ( ");
            }else {
                local.append("AND o.combine_id = 0 AND ( ");
            }
            local.append("( o.item_sys_id > 0 ").append(sysRef.getQ().toString()).append(")")
                    .append(" OR ( o.item_sys_id in (0,-1) ").append(platRef.getQ().toString()).append(")")
                    .append(")");

            hotQry.append(local.toString()).add(sysRef.getArgs()).add(platRef.getArgs());


            if (notEmpty(condition.getOuterIds()) && Objects.equals(refType, 4)) {
                sysRef.append(" AND o.stock_status ='INSUFFICIENT' ");
                platRef.append(" AND o.stock_status ='INSUFFICIENT' ");
            }
            local = new StringBuilder();


            if (needQryCombineSubOrder) {
                local.append("AND ( ");
            }else {
                local.append("AND o.combine_id = 0 AND ( ");
            }
            local.append("( o.item_sys_id > 0 ").append(sysRef.getQ().toString()).append(")")
                    .append(" OR ( o.item_sys_id in (0,-1) ").append(platRef.getQ().toString()).append(")")
                    .append(")");
            orderQry.append(local.toString()).add(sysRef.getArgs()).add(platRef.getArgs());
        }
        return added;
    }

}




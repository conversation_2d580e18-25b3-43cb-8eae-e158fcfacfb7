package com.raycloud.dmj.services.trades.support;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.data.dubbo.api.TradeColdDataQueryApi;
import com.raycloud.dmj.data.dubbo.request.TradeColdQueryParameter;
import com.raycloud.dmj.data.dubbo.tools.ParameterConverts;
import com.raycloud.dmj.domain.account.LightStaffParam;
import com.raycloud.dmj.business.payment.support.MonitorLogBuilder;
import com.raycloud.dmj.domain.basis.StaffDataPrivilege;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.params.OrderAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.TradeCursorQueryRequest;
import com.raycloud.dmj.domain.trades.search.QueryKeyEnum;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.search.BaseTrade;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.item.search.request.DmjItemSearchField;
import com.raycloud.dmj.item.search.request.QueryMiniItemByOuterIdListRequest;
import com.raycloud.dmj.item.search.response.QueryMiniItemByOuterIdListResponse;
import com.raycloud.dmj.services.basis.IHandleStaffPrivilegeService;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.logistics.UploadUtils;
import com.raycloud.dmj.business.operate.PackSplitBusiness;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.business.trade.distributor.TradeDistributorQueryBusiness;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.business.tradeext.TradeExtQueryBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.conf.AppConfiguration;
import com.raycloud.dmj.dao.order.OrderDAO;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.ConsignRecordDao;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.pgl.OrderExtDao;
import com.raycloud.dmj.dao.wave.OrderUniqueCodeUnboundLogDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.pt.IExpressTemplateBase;
import com.raycloud.dmj.domain.pt.SysItemSku;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradePickingParams;
import com.raycloud.dmj.domain.trades.search.ItemParams;
import com.raycloud.dmj.domain.trades.search.ItemQueryParams;
import com.raycloud.dmj.domain.trades.search.exception.SearchIllegalArgumentException;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.tradepay.TradePayDetail;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeUnboundLog;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.item.search.request.QueryDmjItemByOuterIdRequest;
import com.raycloud.dmj.item.search.request.QueryPureItemByParamsRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryDmjItemByOuterIdListResponse;
import com.raycloud.dmj.item.search.response.QueryPureItemByParamsResponse;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.filter.support.DecodeFieldFilter;
import com.raycloud.dmj.services.filter.support.JdDecodeFilter;
import com.raycloud.dmj.services.helper.trade.TradeStatusHelper;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.trade.tradeExt.TradeExtService;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.fill.TradeExtFill;
import com.raycloud.dmj.services.trades.fill.TradeTagFill;
import com.raycloud.dmj.services.trades.fill.TradeExceptFill;
import com.raycloud.dmj.services.trades.filter.ITradeFilterService;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.search.QueryBuilder;
import com.raycloud.dmj.services.trades.support.search.SearchAddressUtils;
import com.raycloud.dmj.services.trades.support.search.TradeSearchSupport;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.LabelAndExceptionConditonConverter;
import com.raycloud.dmj.services.trades.support.search.scene.SceneLightQueryer;
import com.raycloud.dmj.services.trades.support.search.scene.SceneLightQueryContext;
import com.raycloud.dmj.services.trades.support.utils.QueryUtils;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import com.raycloud.dmj.services.trades.support.utils.TradeWeighUtils;
import com.raycloud.dmj.services.trades.wave.IWaveUniqueCodeService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.web.pgl.MySql2PglSqlException;
import com.raycloud.dmj.web.pgl.MySql2PglSqlResolver;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.Strings.getAsStringArray;
import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;


/**
 * 淘宝订单的搜索服务
 * Created by ZuoYun on 2014/7/8.
 * Time: 14:43
 * Information:
 */

@Service("tbTradeSearchService")
public class TbTradeSearchService extends AbsTradeSearchService {

    @Autowired
    TbTradeDao tbTradeDao;

    @Autowired
    TbOrderDAO tbOrderDao;

    @Resource
    OrderDAO orderDAO;

    @Autowired
    ConsignRecordDao consignRecordDao;

    @Resource
    ITradeFilterService filterService;

    @Resource
    ITradeFillService tradeFillService;

    @Resource
    TradeTagFill tradeTagFill;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource(name = "tradePSqlQueryBuilder")
    TradeQueryBuilder tradeSqlQueryBuilder;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;

    @Resource
    PackSplitBusiness packSplitBusiness;

    @Resource
    ITradeDataService tradeDataService;


    @Resource
    private IItemServiceWrapper itemServiceWrapper;

    @Resource
    JdDecodeFilter jdDecodeFilter;


    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    OrderExtDao orderExtDao;

    @Resource
    TradeColdDataQueryApi tradeColdDataQueryApi;

    @Resource
    private TradePddTradeBusiness tradePddTradeBusiness;

    @Resource
    private TradeExtQueryBusiness tradeExtQueryBusiness;

    @Resource
    private TradeExtBusiness tradeExtBusiness;

    @Resource
    private ITradeWaveService tradeWaveService;

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Resource
    IWaveUniqueCodeService waveUniqueCodeService;

    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;

    @Resource
    OrderUniqueCodeUnboundLogDao orderUniqueCodeUnboundLogDao;

    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource
    IItemServiceDubbo iItemServiceDubbo;

    @Resource
    TradePayBusiness tradePayBusiness;

    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    IConsignRecordService consignRecordService;

    @Resource
    IStaffService staffService;

    @Resource
    TradeExtService tradeExtService;

    @Resource
    TradeDistributorQueryBusiness tradeDistributorQueryBusiness;

    @Resource
    private IShopService shopService;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    IUserExpressTemplateService userExpressTemplateService;

    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    IExpressTemplateCommonService expressTemplateCommonService;

    @Resource
    AppConfiguration appConfiguration;

    @Resource(name = "tradeKjQueryBuilder")
    TradeKjQueryBuilder tradeKjQueryBuilder;

    @Resource
    private TradeExceptFill tradeExceptFill;
    @Resource
    private ITradeExceptQueryService tradeExceptQueryService;

    @Resource
    private ITradeConfigNewService tradeConfigNewService;

    @Resource
    private IHandleStaffPrivilegeService handleStaffPrivilegeService;

    @Resource
    private DecodeFieldFilter decodeFieldFilter;



    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    TradeSearchParamsAgent tradeSearchParamsAgent;

    @Resource
    protected ICompanyService companyService;

    @Resource
    protected QueryBuilder queryBuilder;

    @Resource
    protected TradeSearchSupport tradeSearchSupport;



    private final Logger logger = Logger.getLogger(this.getClass());

    private final static Pattern WHERE_PATTERN = Pattern.compile("where", Pattern.CASE_INSENSITIVE);

    private final static List<String> TAOBAO_SERIES_PLATFORM = Arrays.asList(
            CommonConstants.PLAT_FORM_TYPE_TAO_BAO,
            CommonConstants.PLAT_FORM_TYPE_TIAN_MAO,
            CommonConstants.PLAT_FORM_TYPE_1688,
            CommonConstants.PLAT_FORM_TYPE_1688_C2M,
            CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU,
            CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB,
            CommonConstants.PLAT_FORM_TYPE_XHS);

    @Override
    protected TradeAssembleBusiness getTradeAssembleBusiness() {
        return tradeAssembleBusiness;
    }

    @Override
    public <T> List<Trade> queryByKeys(Staff staff, String tradeFields, String orderFields, String key, boolean filter, T... values) {
        validate(key,values);
        Set<String> tradeFieldSet = Strings.getAsStringSet(tradeFields, ",", true);
        if (!tradeFieldSet.isEmpty()) {
            tradeFieldSet.add("sid");
            tradeFieldSet.add("tid");
            tradeFieldSet.add("merge_sid");
            tradeFieldSet.add("split_sid");
        }
        Set<String> orderFieldSet = Strings.getAsStringSet(orderFields, ",", true);
        if (!orderFieldSet.isEmpty()) {
            orderFieldSet.add("sid");
            orderFieldSet.add("tid");
            orderFieldSet.add("id");
            orderFieldSet.add("oid");
            orderFieldSet.add("num_iid");
            orderFieldSet.add("sku_id");
            orderFieldSet.add("item_sys_id");
            orderFieldSet.add("sku_sys_id");
            orderFieldSet.add("type");
            orderFieldSet.add("combine_id");
        }
        tradeFields = tradeFieldSet.isEmpty() ? null : Strings.join(",", tradeFieldSet);
        orderFields = orderFieldSet.isEmpty() ? null : Strings.join(",", orderFieldSet);
        List<Trade> trades;
        if ("sid".equals(key) || "tid".equals(key) || "mergeSid".equals(key) || "merge_sid".equals(key) || "user_id".equals(key) || "warehouse_id".equals(key) || "split_sid".equals(key)) {
            List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, tradeFields, key, values);
            List<TbOrder> tbOrders = tbOrderDao.queryByKeys(staff, true, orderFields, "sid", TradeUtils.toSids(tbTrades));
            trades = TradeUtils.assemblyBySid(tbTrades, tbOrders);
        } else if ("orderId".equals(key) || "oid".equals(key)) {
            key = (key.equals("orderId") ? "id" : key);
            List<TbOrder> tbOrders = tbOrderDao.queryByKeys(staff, true, orderFields, key, values);
            List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, tradeFields, "sid", OrderUtils.toSids(tbOrders));
            trades = TradeUtils.assemblyBySid(tbTrades, tbOrders);
        } else {
            throw new IllegalArgumentException("暂不支持按照\"" + key + "\"查询");
        }
        tradeFillService.fill(staff, trades);
        if (filter) {
            try {
                filterService.filterTrades(staff, trades);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "订单过滤处理失败"), e);
            }
        }
        return trades;
    }

    @Override
    public <T> List<TbTrade> queryByKeys(Staff staff, String fields, String key, T... values) {
        validate(key,values);
        return tbTradeDao.queryByKeys(staff, fields, key, values);
    }

    /**
     * 老的queryByKeys对于key是没有做任何限制的 但是好在没有对外开放dubbo 这里只做松散的校验
     */
    private <T> void validate(String key, T... values){
        Assert.notNull(key,"查询key不能为空");
        key = key.trim();
        if (Objects.equal(key,"mergeSid")) {
            key = "merge_sid";
        }

        QueryKeyEnum byField = QueryKeyEnum.getByField(key.toLowerCase());
        for (T value : values) {
            if (value == null) {
                continue;
            }
            if (value != null && value instanceof Collection) {
                throw new IllegalArgumentException("传参错误 values预期为数组但是传入的是" + value.getClass());
            }
            if (Objects.equal("warehouse_id",key) && Objects.equal(-1L,value)) {
                throw new IllegalArgumentException("key:"+key+ "不支持的查询值:"+value);
            }
            if (byField != null && byField.getForbiddenValues().contains(value)) {
                throw new IllegalArgumentException("key:"+key+ "不支持的查询值:"+value);
            }
        }
    }

    @Override
    public <T> List<Trade> queryAndAssemblyByKeys(Staff staff, TradeAssembleParams params, String key, T... values) {
        Assert.notNull(key,"查询key不能为空");
        if (values == null || values.length == 0) {
            return new ArrayList<>();
        }
        QueryKeyEnum byField = QueryKeyEnum.getByField(key);
        if (byField == null) {
            throw new IllegalArgumentException("不支持的key:"+ key +",见QueryKeyEnum");
        }
        for (T value : values) {
            if (value == null) {
                throw new IllegalArgumentException("values不应包含null值");
            }
            if (value instanceof Collection) {
                throw new IllegalArgumentException("传参错误 values预期为数组但是传入的是" + value.getClass());
            }
            //if (!java.util.Objects.equals(value.getClass(),byField.getType())) {
            //    throw new IllegalArgumentException("参数类型不符:"+value+",预期为:"+byField.getType());
            //}
            if (byField.getForbiddenValues().contains(value)) {
                throw new IllegalArgumentException("key:"+key+ "不支持的查询值:"+value);
            }
        }

        String fields = null;
        if (CollectionUtils.isNotEmpty(params.getQueryFields())) {
            fields = Strings.join(",",params.getQueryFields());
        }
        AbsLogBuilder logBuilder = new QueryLogBuilder(staff).setBaseTooklmt(100L).startTimer();
        List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, fields, key, values);
        logBuilder.append("keysize",values == null? 0:values.length).recordTimer("tbTradeDao");
        decodeFieldFilter.filterTrades(staff,tbTrades);
        List<Trade> result = tradeAssembleBusiness.assemblyTrades(staff, params, logBuilder, tbTrades);
        logBuilder.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),values == null? 0:values.length,null)).printInfo(logger);
        return result;
    }

    @Override
    public List<TbTrade> queryByTidsNeedExt(Staff staff, boolean showDetail, boolean needExt, String... tids) {
        List<TbTrade> tbTrades = queryByTids(staff, showDetail, tids);
        if (needExt) {
            if (CollectionUtils.isNotEmpty(tbTrades)) {
                List<Long> extSids = tbTrades.stream().filter(TradeUtils::needTradeExt).map(Trade::getSid).collect(Collectors.toList());
                List<TradeExt> tradeExtList = tradeExtDao.tradeExtsGetBySids(staff, extSids);
                if (CollectionUtils.isNotEmpty(tradeExtList)) {
                    Map<Long, TradeExt> map = tradeExtList.stream().collect(Collectors.toMap(TradeExt::getSid, tradeExt -> tradeExt));
                    for (TbTrade tbTrade : tbTrades) {
                        if (map.get(tbTrade.getSid()) != null) {
                            tbTrade.setTradeExt(map.get(tbTrade.getSid()));
                        }
                    }
                }
            }
        }
        return tbTrades;
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, String... tids) {
        return queryByTids(staff,showDetail,false,tids);
    }

    @Override
    public List<TbTrade> queryByTidsWithOutFill(Staff staff, boolean showDetail, String... tids) {
        return queryByTids(staff,showDetail,true,false,tids);
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay, String... tids) {
        return queryByTids(staff,showDetail,true,queryTradePay,tids);
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay, AbsLogBuilder logBuilder, String... tids) {
        return queryByTids(staff,showDetail,true,queryTradePay,logBuilder,tids);
    }

    protected List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean needFill, boolean queryTradePay, String... tids) {
        return queryByTids(staff,showDetail,needFill,queryTradePay,null,tids);
    }

    protected List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean needFill, boolean queryTradePay,AbsLogBuilder logBuilder, String... tids) {
        if (tids == null || tids.length == 0) {
            return new ArrayList<>();
        }
        AbsLogBuilder local = null;
        if (logBuilder == null) {
            local = new QueryLogBuilder(staff).append("queryByTids 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
            logBuilder = local;
        }
        tids = tradeSqlQueryBuilder.preHandleTid(staff, tids).toArray(new String[0]);
        logBuilder.recordTimer("preHandleTid");
        List<TbTrade> tbTrades = tbTradeDao.queryByTids(staff,tids);
        logBuilder.append("refTradeSize",tbTrades.size());
        logBuilder.recordTimer("tbTradeDao");
        if (!tbTrades.isEmpty() && showDetail) {
            List<TbOrder> orders = tbOrderDao.queryByTids(staff, TradeUtils.toTids(tbTrades));
            logBuilder.append("refOrderSize",orders.size());
            logBuilder.recordTimer("tbOrderDao");
            //查询 order_ext 信息
            List<OrderExt> orderExts = orderExtDao.queryByOrderIds(staff, orders.stream().map(Order::getId).toArray(Long[]::new));
            logBuilder.recordTimer("orderExtDao");
            if(CollectionUtils.isNotEmpty(orderExts)){
                Map<Long,OrderExt> extMap = orderExts.stream().collect(Collectors.toMap(OrderExt::getId,v -> v,(v1,v2)->v2));
                for(TbOrder order : orders){
                    order.setOrderExt(extMap.get(order.getId()));
                }
            }
            TradeUtils.assemblyBySid(tbTrades, orders);
        }
        if (needFill) {
            tradeFillService.fill(staff, tbTrades);
            logBuilder.recordTimer("fill");
        }
        try {
            filterService.filterTrades(staff, TradeUtils.toTrades(tbTrades));
            logBuilder.recordTimer("filter");
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        }
        if(queryTradePay){
            tradeAssembleBusiness.fillTradePay(staff,tbTrades);
            logBuilder.recordTimer("fillTradePay");
        }
        if (local != null) {
            local.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),tids.length,null)).printDebug(logger);
        }
        return tbTrades;
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, Long... sids) {
        //if (sids == null || sids.length == 0) {
        //    return new ArrayList<>();
        //}
        //QueryLogBuilder logBuilder = new QueryLogBuilder(staff).append("queryBySids 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
        //List<Trade> trades = _queryBySids(staff, sids, showDetail,logBuilder);
        ////当showDetail为true  _queryBySids里bindOrders 动作已经做了fill
        ////if (!showDetail) {
        //tradeFillService.fill(staff, trades,TradeExtFill.class);
        //logBuilder.recordTimer("fill(size:"+trades.size()+")");
        ////}
        //try {
        //    List<Trade> filter = filterService.filterTrades(staff, trades);
        //    logBuilder.recordTimer("filter");
        //    if (CollectionUtils.isNotEmpty(filter)) {
        //        tradeExtQueryBusiness.queryHugeTradeExt(staff, filter);
        //        logBuilder.recordTimer("queryHugeTradeExt");
        //    }
        //    try {
        //        jdDecodeFilter.decodeTrades(staff, trades);
        //        logBuilder.recordTimer("jdDecodeFilter");
        //    } catch (Exception e) {
        //        logger.warn(LogHelper.buildErrorLog(staff, e, "jd 解密失败" + e.getMessage()), e);
        //    }
        //    return filter;
        //} catch (TradeFilterException e) {
        //    logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        //    return trades;
        //}finally {
        //    logBuilder.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),sids == null? 0:sids.length,null)).printInfo(logger);
        //}
        return queryBySids(staff,showDetail,false,false,false,null,sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, AbsLogBuilder logBuilder, Long... sids) {
        return queryBySids(staff,showDetail,false,false,false,logBuilder,sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, Long... sids) {
        //if (sids == null || sids.length == 0) {
        //    return new ArrayList<>();
        //}
        //QueryLogBuilder logBuilder = new QueryLogBuilder(staff).append("queryBySids 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
        //List<Trade> trades = _queryBySids(staff, sids, showDetail, showTradeExt,logBuilder);
        //tradeFillService.fill(staff, trades);
        //logBuilder.recordTimer("fill");
        //try {
        //    List<Trade> filter = filterService.filterTrades(staff, trades);
        //    logBuilder.recordTimer("filter");
        //    try {
        //        jdDecodeFilter.decodeTrades(staff, trades);
        //        logBuilder.recordTimer("jdDecodeFilter");
        //    } catch (Exception e) {
        //        logger.warn(LogHelper.buildErrorLog(staff, e, "jd 解密失败" + e.getMessage()), e);
        //    }
        //    return filter;
        //} catch (TradeFilterException e) {
        //    logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        //    return trades;
        //}finally{
        //    logBuilder.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),sids == null? 0:sids.length,null)).printInfo(logger);
        //}
        return queryBySids(staff,showDetail,showTradeExt,false,false,null,sids);
    }

    private List<Trade> queryBySids(Staff staff, boolean showDetail,boolean showTradeExt, boolean pddMaskData,boolean setExtItemQuantity, AbsLogBuilder logBuilder, Long... sids) {
        if (sids == null || sids.length == 0) {
            return new ArrayList<>();
        }
        AbsLogBuilder local = null;
        if (logBuilder == null) {
            local = new QueryLogBuilder(staff).append("queryBySids 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
            logBuilder = local;
        }
        List<Trade> trades = _queryBySids(staff, sids, showDetail,showTradeExt,logBuilder);
        //当showDetail为true  _queryBySids里bindOrders 动作已经做了fill
        //if (!showDetail) {
        tradeFillService.fill(staff, trades,TradeExtFill.class);
        logBuilder.recordTimer("fill(size:"+trades.size()+")");
        //}
        try {
            List<Trade> filter = filterService.filterTrades(staff, trades);
            logBuilder.recordTimer("filter");
            if (CollectionUtils.isNotEmpty(filter)) {
                tradeExtQueryBusiness.queryHugeTradeExt(staff, filter);
                logBuilder.recordTimer("queryHugeTradeExt");
            }
            try {
                jdDecodeFilter.decodeTrades(staff, trades);
                logBuilder.recordTimer("jdDecodeFilter");
            } catch (Exception e) {
                logger.warn(LogHelper.buildErrorLog(staff, e, "jd 解密失败" + e.getMessage()), e);
            }
            trades = filter;
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        }
        if (setExtItemQuantity) {
            this.setTradeExtItemQuantity(staff, trades);
            logBuilder.recordTimer("setExtItemQuantity");
        }
        if (pddMaskData) {
            tradePddTradeBusiness.pddMaskDataReplace(staff, trades);
            logBuilder.recordTimer("pddSensitive");
            fxgTradeDecryptBusiness.batchSensitive(staff, trades);
            logBuilder.recordTimer("fxgSensitive");
            commonTradeDecryptBusiness.batchSensitive(staff, trades);
            logBuilder.recordTimer("commonSensitive");
        }

        if (local != null) {
            local.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),sids == null? 0:sids.length,null)).printInfo(logger);
        }
        return trades;
    }

    @Override
    public List<Trade> queryBySplitSids(Staff staff, boolean showDetail, Long... splitSids) {
        List<Trade> trades = queryBySplitSids(staff, splitSids, showDetail);
        tradeFillService.fill(staff, trades);
        try {
            List<Trade> filter = filterService.filterTrades(staff, trades);
            try {
                jdDecodeFilter.decodeTrades(staff, trades);
            } catch (Exception e) {
                logger.warn(LogHelper.buildErrorLog(staff, e, "jd 解密失败" + e.getMessage()), e);
            }
            return filter;
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
            return trades;
        }
    }

    private List<Trade> queryBySplitSids(Staff staff, Long[] splitSids, boolean showDetail) {
        List<TbTrade> tbTrades = tbTradeDao.queryBySplitSids(staff, splitSids);

        tradeFillService.fill(staff, tbTrades);
        if (!showDetail) {
            return TradeUtils.toTrades(tbTrades);
        }
        return tradeAssembleBusiness.bindOrders(staff, tbTrades, true);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, boolean pddMaskData, Long... sids) {
        //List<Trade> trades = queryBySids(staff, showDetail, showTradeExt, sids);
        //this.setTradeExtItemQuantity(staff, trades);
        //if (pddMaskData) {
        //    tradePddTradeBusiness.pddMaskDataReplace(staff, trades);
        //    fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        //    commonTradeDecryptBusiness.batchSensitive(staff, trades);
        //}
        //return trades;
        return queryBySids(staff,showDetail,showTradeExt,pddMaskData,true,null,sids);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, Long... sids) {
        return _queryBySids(staff, sids, showDetail,AbsLogBuilder.getNvlInstance());
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, AbsLogBuilder logBuilder, Long... sids) {
        return _queryBySids(staff, sids, showDetail,logBuilder);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, boolean needFill, Long... sids) {
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        if (!showDetail) {
            return TradeUtils.toTrades(tbTrades);
        }
        return tradeAssembleBusiness.bindOrders(staff, tbTrades, true, null, needFill);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, TradeQueryNoFilterParams params, Long... sids) {
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        if (!BooleanUtils.isTrue(params.getShowDetail())) {
            return TradeUtils.toTrades(tbTrades);
        }
        return tradeAssembleBusiness.bindOrders(staff, tbTrades, true, null, BooleanUtils.isTrue(params.getNeedFillExt()), BooleanUtils.isTrue(params.getNeedFill()), AbsLogBuilder.getNvlInstance());
    }

    @Override
    public Trade queryBySid(Staff staff, boolean showDetail, Long sid) {
        List<Trade> trades = queryBySids(staff, showDetail, sid);
        if (trades.isEmpty()) {
            return null;
        }
        return trades.get(0);
    }

    @Override
    public Trade queryBySidWithOutFill(Staff staff, boolean showDetail, Long sid) {
        List<Trade> trades = _queryBySids(staff, new Long[]{sid},showDetail,new QueryLogBuilder(staff));
        if (trades.isEmpty()) {
            return null;
        }
        return trades.get(0);
    }

    @Override
    public Trade queryBySid(Staff staff, boolean showDetail, int pddDecrypt, Long sid) {
        Trade trade = queryBySid(staff, showDetail, sid);
        if (pddDecrypt == 1 && trade != null) {
            List<Trade> trades = tradePddTradeBusiness.decryptPddTrade(staff, Collections.singletonList(trade));
            if (trades.isEmpty()) {
                return null;
            }
            return trades.get(0);
        } else if (pddDecrypt == 2 && trade != null) {
            List<Trade> trades = tradePddTradeBusiness.pddMaskDataReplace(staff, Collections.singletonList(trade));
            fxgTradeDecryptBusiness.batchSensitive(staff, trades);
            commonTradeDecryptBusiness.batchSensitive(staff, trades);
            if (trades.isEmpty()) {
                return null;
            }
            return trades.get(0);
        }
        return trade;
    }

    @Override
    public List<Trade> queryByShortId(Staff staff, boolean showDetail, Long... shortIds) {
        List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, null, "short_id", shortIds);

        if (!showDetail) {
            tradeFillService.fill(staff, tbTrades);
            return TradeUtils.toTrades(tbTrades);
        }
        List<Trade> trades = tradeAssembleBusiness.bindOrders(staff, tbTrades, true);
        tradeFillService.fill(staff, trades);
        try {
            return filterService.filterTrades(staff, trades);
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
            return trades;
        }

    }

    @Override
    public List<Trade> queryByShortIdWithOutFill(Staff staff, boolean showDetail, Long... shortIds) {
        List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, null, "short_id", shortIds);
        List<Trade> trades = TradeUtils.toTrades(tbTrades);
        if (showDetail) {
            trades = tradeAssembleBusiness.bindOrders(staff, tbTrades, true);
        }
        return trades;
    }

    /**
     * http://doc.raycloud.com/pages/viewpage.action?pageId=30547829
     *
     * @param staff
     * @param params
     * @param params
     * @return
     */
    public void fillSysOuterByMainOuterId(Staff staff, TradeQueryParams params) {
        if (StringUtils.isBlank(params.getMainOuterId())) {
            return;
        }
        QueryDmjItemByOuterIdRequest request = new QueryDmjItemByOuterIdRequest();
        request.setOuterId(params.getMainOuterId());
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        try {
            QueryDmjItemByOuterIdListResponse response = dmjItemCommonSearchApi.queryItemByOuterId(request);
            if (response == null || !response.isSuccess() || CollectionUtils.isEmpty(response.getList())) {
                params.setOuterId(TradeConstants.NO);
                return;
            }

            List<DmjItemDto> list = response.getList();
            List<String> sysOuterIds = Lists.newArrayListWithCapacity(list.size());
            for (DmjItemDto dmjItemDto : list) {
                sysOuterIds.add(StringUtils.isBlank(dmjItemDto.getSkuOuterId()) ? dmjItemDto.getOuterId() : dmjItemDto.getSkuOuterId());
            }
            if (CollectionUtils.isEmpty(sysOuterIds)) {
                params.setOuterId(TradeConstants.NO);
                return;
            }

            if (sysOuterIds.size() > 500) {
                logger.debug(LogHelper.buildLogHead(staff).append("根据主商家编码查询商品超过500!"));
            }
            params.setOuterId(Strings.join(",", sysOuterIds));
        } catch (Exception e) {
            params.setOuterId(TradeConstants.NO);
            logger.error(LogHelper.buildLogHead(staff).append("根据主商家编码查询商品出错:").append(e.getMessage()),e);
        }
    }

    /**
     * 主商家编码只支持精确搜索,根据编码填充sysItemId
     * 1.mainOuterId支持多个，用逗号隔开   mainOuterId=a,b,c
     * 2.根据商品的纯商品接口返回失败或者没有数据返回false 不用再走数据库查询
     *
     * @param staff
     * @param params
     */
    public boolean fillSysItemIdByMainOuterId(Staff staff, TradeQueryParams params) {
        if (StringUtils.isBlank(params.getMainOuterId())) {
            return true;
        }
        reBaseTimer(params);
        QueryPureItemByParamsRequest request = new QueryPureItemByParamsRequest();
        request.setOuterIds(Strings.getAsStringList(params.getMainOuterId(), ",", true));
        request.setPageNo(1);
        request.setPageSize(100);
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        try {
            QueryPureItemByParamsResponse response = dmjItemCommonSearchApi.queryPureItem(request);
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getList())) {
                logger.warn(LogHelper.buildLogHead(staff).append("主商家编码: ").append(params.getMainOuterId()).append(" ,根据商品的纯商品接口返回：").append(JSON.toJSONString(response)));
                return false;
            }

            List<DmjItem> list = response.getData().getList();
            List<Long> sysItemIds = Lists.newArrayListWithCapacity(list.size());
            for (DmjItem dmjItem : list) {
                sysItemIds.add(dmjItem.getSysItemId());
            }
            if (CollectionUtils.isEmpty(sysItemIds)) {
                return false;
            }
            params.setSysItemIds(sysItemIds.toArray(new Long[0]));
            return true;
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("根据主商家编码查询商品出错:").append(e.getMessage()),e);
            return false;
        }finally{
            recordTimer(params,"fillSysItemIdByMainOuterId");
        }

    }

    /**
     * 根据供应商查询
     * https://gykj.yuque.com/entavv/xb9xi5/uqrozv
     *
     * @param staff  用户信息
     * @param params 查询参数
     */
    public void fillSysItemIdBySupplierIds(Staff staff, TradeQueryParams params) {
        if (CollectionUtils.isEmpty(params.getSupplierIds())) {
            return;
        }
        try {
            List<ItemSupplierBridge> itemSupplierBridges = iItemServiceDubbo.queryBridgesBySupplierIds(staff, params.getSupplierIds());
            if (CollectionUtils.isNotEmpty(itemSupplierBridges)) {
                //系统纯商品id
                Set<Long> sysItemIds = itemSupplierBridges.stream().map(ItemSupplierBridge -> ItemSupplierBridge.getSysItemId()).collect(Collectors.toSet());
                //系统商品规格id
                Set<Long> sysSkuIds = itemSupplierBridges.stream().filter(itemSupplierBridge -> itemSupplierBridge.getSysSkuId() > 0).map(ItemSupplierBridge -> ItemSupplierBridge.getSysSkuId()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(sysItemIds)) {
                    params.setSysItemIds(sysItemIds.toArray(new Long[sysItemIds.size()]));
                }
                if (CollectionUtils.isNotEmpty(sysSkuIds)) {
                    params.setSysSkuIds(sysSkuIds.toArray(new Long[sysSkuIds.size()]));
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("根据供应商查询商品出错:").append(e));
        }
    }

    private boolean fillSidsByUniqueCodes(Staff staff, TradeQueryParams params) {
        List<String> uniqueCodes = params.getUniqueCodes();
        if (uniqueCodes != null && uniqueCodes.size() > 0) {
            reBaseTimer(params);
            ItemUniqueCodeQueryParams uniqueCodeParams = new ItemUniqueCodeQueryParams();
            uniqueCodeParams.setUniqueCodes(uniqueCodes);
            List<WaveUniqueCode> waveUniqueCodes = itemUniqueCodeService.queryItemUniqueCodeByCondition(staff, uniqueCodeParams);
            Set<Long> sids = new HashSet<>();
            if (waveUniqueCodes != null && waveUniqueCodes.size() > 0) {
                waveUniqueCodes.forEach(o -> {if(o.getSid() !=null){
                    sids.add(o.getSid());
                }});

                if (params.isAllowQueryUnboundUniqueCode()) {
                    List<OrderUniqueCodeUnboundLog> orderUniqueCodeUnboundLogs = orderUniqueCodeUnboundLogDao.queryAllSidByLastOne(staff, uniqueCodes);
                    orderUniqueCodeUnboundLogs.forEach(o -> {if(o.getSid() !=null){
                        sids.add(o.getSid());
                    }} );
                }
                if (CollectionUtils.isEmpty(sids)) {
                    return false;
                }

                //需要取params中已存在的sid来和根据订单唯一码查询得到的sid取交集
                Long[] sidArr = params.getSid();
                Set<Long> existSids = new HashSet<>();
                if (sidArr != null && sidArr.length > 0) {
                    existSids.addAll(Arrays.stream(sidArr).collect(Collectors.toSet()));
                    sids.retainAll(existSids);
                }
                if (CollectionUtils.isEmpty(sids)) {
                    return false;
                }
                params.setSid(sids.toArray(new Long[0]));
            }else {
                return  false;
            }
            recordTimer(params,"fillSidsByUniqueCodes");
        }
        return true;
    }


    @HystrixCommand(commandProperties = {
            @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "40000")
    }, threadPoolKey = "tradeSearchThreadPool",
            threadPoolProperties = {
                    @HystrixProperty(name = "coreSize", value = "30")
            }, ignoreExceptions = {SearchIllegalArgumentException.class, IllegalArgumentException.class, MySql2PglSqlException.class, UnsupportedOperationException.class}
    )
    @Override
    public Trades search(Staff staff, TradeQueryParams params) {
        HystrixUtil.setMdcInfo(staff);
        //业务自定义的查询参数处理
        reBaseTimer(params);
        QueryUtils.wrapQueryParams(staff, params);
        return backTradeSearchUnfuse(staff, params);
    }


    /**
     * 后端不熔断的查询
     *
     * @param staff
     * @param params
     * @return
     */
    public Trades backTradeSearchUnfuse(Staff staff, TradeQueryParams params) {
        AbsLogBuilder pLogger = params.getContext().getLogBuilder();
        if (pLogger != null) {
            pLogger.reBaseTimer();
        }
        //这里用启用独立的耗时统计 防止Hystrix超时,真实的耗时打不全
        QueryLogBuilder curLog = new QueryLogBuilder(staff).append("backTradeSearchUnfuse 耗时统计:");
        params.getContext().setLogBuilder(curLog);
        try {

        boolean res = tradeSearchParamsAgent.handle(staff, params);
        recordTimer(params, "paramsHandler");
        if (!res) {
            return createTrades(null, 0L, params.getPage(), params.getSort());
        }
        initDataAuth(staff, params);
        recordTimer(params, "initDataAuth");
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Integer itemExcepOpen = 1;
        params.setItemExcepOpen(itemExcepOpen);
        params.setUseNewQuery(tradeConfig.getUseNewQuery());
        if (!fillSysItemIdByMainOuterId(staff, params)) {
            new QueryLogBuilder(staff).append("查询中断","根据商品的纯商品接口返回失败或者没有数据").printDebug(logger);
            return Trades.createTrades(new ArrayList<>(),0L,params.getPage(), params.getSort());
        }
//      fillSysOuterByMainOuterId(staff, params);
//        fillSysItemIdBySupplierIds(staff, params);
//        if (searchSupplierFail(staff, params)) {
//            return new Trades();
//        }
        if (!fillSidsByUniqueCodes(staff, params)) {
            new QueryLogBuilder(staff).append("查询中断","唯一码接口返回失败或者没有数据").printDebug(logger);
            return Trades.createTrades(new ArrayList<>(),0L,params.getPage(), params.getSort());
        }

        Long queryId = params.getQueryId();
        if (queryId != null) {
            //不查询三方库 待出库、已出库、装箱清单、拆分装箱
            if (queryId == QUERY_WAIT_PACK) {
                return queryTradeWaitPack(staff, params);
            } else if (queryId == QUERY_PACK_SPLITABLE) {
                return queryTradePackSplitable(staff, params);
            } else if (queryId == QUERY_WAIT_WEIGH) {
                return queryTradeWaitWeigh(staff, params);
            } else if (queryId == QUERY_WAIT_CONSIGN) {
                return queryTradeWaitConsign(staff, params);
            } else if (queryId == QUERY_WAIT_OUTSTOCK || queryId == QUERY_FINISH_OUTSTOCK || queryId == QUERY_BOXING) {
                params.setWarehouseType(0);
            } else if (queryId == QUERY_UN_CONSIGNED) {
                //查询异常订单时不查询待付款状态订单  只校验query = 62的  页面查询的时候 queryId = 62 都有sysStatus 的 下载的时候没有所以加一下
                if (params.getExceptionStatus() != null && params.getExceptionStatus().length > 0 && params.getSysStatus() != null && params.getSysStatus().length > 0) {
                    params.getStockStatus();
                    List<String> sysStatusList = new ArrayList<String>();
                    for (String sysStatus : params.getSysStatus()) {
                        sysStatusList.add(sysStatus);
                    }
                    if (CollectionUtils.isEmpty(sysStatusList)) {
                        //查询异常订单时如果只选择了待付款则直接返回空
                        return Trades.createTrades(new ArrayList<>(), 0L, params.getPage(), params.getSort());
                    }
                    String[] sysStatusArr = new String[sysStatusList.size()];
                    sysStatusList.toArray(sysStatusArr);
                    params.setSysStatus(sysStatusArr);
                }
                //订单管理页面指定收件人/商家编码/手机号，回车直接查询时查询全部状态，非预售，未作废订单
                if (params.isAssignTrade()) {
                    params.setSysStatus(null);
                    params.setIsPresell(0);
                    params.setIsCancel(0);
                }
            } else if (queryId - QUERY_3MONTH_AGO == 0) {
                params.setUseNewQuery(false);//可写可不写
                return tradeDataService.search3Month(staff, params, true);
            }
        }
        return _search(staff, tradeConfig, params);

        }finally{
            curLog.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,staff.getCompanyId(),LogBusinessEnum.QUERY.getSign())?1000L:8000L).multiPrintDebug(logger);
            if (pLogger != null) {
                pLogger.recordTimer("backTradeSearchUnfuse");
            }
            params.getContext().setLogBuilder(pLogger);
        }
    }

    public void initDataAuth(Staff staff, TradeQueryParams params){
        //超级管理员不需要关注
        if (staff.isDefaultStaff()) {
            params.getContext().setOpenDistributorAuth(false);
            params.getContext().setOpenSalesmanAuth(false);
        }else{
            DevLogBuilder builder = new DevLogBuilder(staff, LogBusinessEnum.QUERY.getSign());
            //供销商权限查询
            Staff distributor = staffService.queryDistributorById(LightStaffParam.build(staff));
            if (distributor != null) {
                staff.setDistributorGroup(distributor.getDistributorGroup());
                builder.append("供销商权限",distributor.getDistributorGroup());
            }else {
                builder.append("供销商权限","distributor为空");
            }
            params.getContext().setOpenDistributorAuth(true);
            //业务员权限查询
            builder.append("业务员权限:");
            TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TradeConfigEnum.OPEN_TRADE_SALESMAN);
            boolean hasSalesman = tradeConfigNew.isOpen();
            builder.append("启用业务员业务",hasSalesman);
            if (hasSalesman) {
                tradeConfigNew =  tradeConfigNewService.get(staff, TradeConfigEnum.SALES_MAN_PRIVILEGE);
                hasSalesman = tradeConfigNew.isOpen();
                builder.append("启用业务员权限",hasSalesman);
            }
            if (hasSalesman) {
                StaffDataPrivilege staffDataPrivilege = handleStaffPrivilegeService.querySalesmanByStaff(staff.getId());
                builder.append(JSON.toJSONString(staffDataPrivilege));
                //拥有所有业务员权限则不校验
                if (staffDataPrivilege.getAllSalesmanFlag() != null && staffDataPrivilege.getAllSalesmanFlag()) {
                    params.getContext().setOpenSalesmanAuth(false);
                }else{
                    if (staffDataPrivilege.getAllSalesmanFlag() !=null && staffDataPrivilege.getAllSalesmanFlag()) {
                        staff.setStaffGroup("all");
                    }else {
                        String salesmanIds = staffDataPrivilege.getSalesmanIds();
                        //注意noSalesmanFlag 不是没有业务员的权限  是指查询 业务员为空的订单 的权限
                        if (staffDataPrivilege.getNoSalesmanFlag() !=null && staffDataPrivilege.getNoSalesmanFlag()) {
                            salesmanIds = "-1," + salesmanIds;
                        }
                        staff.setStaffGroup(salesmanIds);
                    }
                    params.getContext().setOpenSalesmanAuth(true);
                }
            }else{
                params.getContext().setOpenSalesmanAuth(false);
            }
            builder.printDebug(logger);
        }
    }

    @Override
    public List<Order> listSuits(Staff staff, Long orderId) {
        List<TbOrder> tbOrders = tbOrderDao.queryByKeys(staff, true, "id, sid", "id", orderId);
        Assert.isTrue(tbOrders.size() > 0, "子订单[" + orderId + "]找不到");
        List<TbOrder> suitOrders = tbOrderDao.querySuits(staff, tbOrders.get(0).getSid(), orderId);
        List<Order> list = new ArrayList<Order>(suitOrders.size());
        list.addAll(suitOrders);
        return list;
    }

    @Override
    public List<Long> querySidsBySysItemSkuId(Staff staff, Long sysItemId, Long sysSkuId, Page page, String... stockStatuss) {
        if (sysItemId == null || sysItemId <= 0) {
            throw new IllegalArgumentException("参数异常: sysItemId不能为空！");
        }
        Query q = new Query().append("company_id = ? AND enable_status = 1 AND is_cancel = 0").add(staff.getCompanyId());
        q.append(" AND item_sys_id = ?").add(sysItemId);
        if (sysSkuId != null) {
            q.append(" AND sku_sys_id = ?").add(sysSkuId <= 0 ? -1 : sysSkuId);
        }
        q.append(" AND sys_status IN(?, ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT);
        if (stockStatuss != null && stockStatuss.length > 0) {
            tradeSqlQueryBuilder.buildArrayQuery(q, "stock_status", stockStatuss);
            for (String stockStatus : stockStatuss) {
                if (Trade.STOCK_STATUS_RELATION_MODIFIED.equals(stockStatus)) {
                    q.and().append("relation_changed = 1");
                }
            }
        }
        List<TbOrder> orders = tbOrderDao.queryOrders(staff, "order_not_consign", "DISTINCT(sid) AS sid", q, page);
        return OrderUtils.toSidList(orders);
    }

    @Override
    public List<Long> querySidsByItemSkuIdBatch(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, Page page) {
        if (CollectionUtils.isEmpty(sysItemIds)) {
            throw new IllegalArgumentException("参数异常: sysItemId不能为空！");
        }
        Query q = new Query().append("company_id = ? AND enable_status > 0 AND is_cancel = 0").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildListQuery(q, "item_sys_id", sysItemIds);
        tradeSqlQueryBuilder.buildListQuery(q, "sku_sys_id", sysSkuIds);
        q.append(" AND sys_status IN(?, ?, ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT).add(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT);
        List<TbOrder> orders = tbOrderDao.queryOrders(staff, "order_not_consign", "DISTINCT(sid) AS sid", q, page);
        return OrderUtils.toSidList(orders);
    }

    @Override
    public List<Long> querySidsByItemIds(Staff staff, List<String> itemIds, Boolean matched, Page page) {
        return querySidsByItemSkuIds(staff, itemIds, matched, "num_iid", page);
    }

    @Override
    public List<Long> querySidsBySkuIds(Staff staff, List<String> skuIds, Boolean matched, Page page) {
        return querySidsByItemSkuIds(staff, skuIds, matched, "sku_id", page);
    }

    private List<Long> querySidsByItemSkuIds(Staff staff, List<String> itemSkuIds, Boolean matched, String itemSkuId, Page page) {
        Query q = new Query().append("company_id = ? AND enable_status = 1 AND is_cancel = 0 ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildListQuery(q, itemSkuId, itemSkuIds);
        q.append(" AND sys_status IN(?, ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT);
        if (matched != null) {
            q.append(" AND item_sys_id");
            if (matched) {//查询商品对应关系为改动的子订单
                q.append(" > 0 AND relation_changed = 0");
            } else {//查询未匹配系统商品的子订单
                q.append(" < 0");
            }
        } else {
            q.append(" AND item_sys_id >0");
        }
        List<TbOrder> orders = tbOrderDao.queryOrders(staff, "order_not_consign", "DISTINCT(sid) AS sid", q, page);
        return OrderUtils.toSidList(orders);
    }

    @Override
    public List<Long> querySidsByItemSkuIds(Staff staff, List<String> itemIds, List<String> skuIds, Boolean matched, Page page) {
        Query q = new Query().append("company_id = ? AND enable_status = 1 AND is_cancel = 0").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildListQuery(q, "num_iid", itemIds);
        tradeSqlQueryBuilder.buildListQuery(q, "sku_id", skuIds);
        q.append(" AND sys_status IN(?, ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT);
        if (matched != null) {
            q.append(" AND item_sys_id");
            if (matched) {//查询商品对应关系为改动的子订单
                q.append(" > 0 AND relation_changed = 0");
            } else {//查询未匹配系统商品的子订单
                q.append(" < 0");
            }
        } else {
            q.append(" AND item_sys_id>0");
        }
        List<TbOrder> orders = tbOrderDao.queryOrders(staff, "order_not_consign", "DISTINCT(sid) AS sid", q, page);
        return OrderUtils.toSidList(orders);
    }

    /**
     *
     *  1.第一页才返回total
     *  2.支持分页查询
     *
     *
     *  缺货异常也支持打印的配置开启，统计系统状态是正常发货中的订单&仅缺货异常的发货中订单，且剔除已发货、交易关闭、交易完成的订单商品
     *    统计逻辑：包含
     *     ⅰ. 只要订单包含该商品就会统计
     *   套件商品根据套件统计
     *   开启了虚拟商品不计入订单商品总数时，无需统计虚拟商品
     *   开启了无需发货商品不计入订单商品总数时，无需统计无需发货商品
     *   列表展示
     *       展示字段：规格商家编码、商品名称、系统规格、包含商品的订单条数、订单汇总商品总数
     *       列表排序：按照商品总数降序；商品总数一样再按照订单条数降序；条数一样再按照规格商家编码降序
     *
     * @param staff
     * @param params
     * @return
     */
    @Override
    public Orders queryHotItem(Staff staff, TradeQueryParams params) {
        //供销商权限查询
        Staff distributor = staffService.queryDistributorById(LightStaffParam.build(staff));
        if (distributor != null) {
            staff.setDistributorGroup(distributor.getDistributorGroup());
        }
        params.getContext().setOpenDistributorAuth(true);

        Orders orders = new Orders();
        orders.setPage(params.getPage());

        Query q =  new Query();
        q.setTradeTable("trade_not_consign");
        q.setOrderTable("order_not_consign");
        q = tradeSqlQueryBuilder.buildQuery(q, staff, params);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSysStatusQuery(q, params, null);
        if(org.apache.commons.lang3.ArrayUtils.contains(params.getSysStatus(),Trade.SYS_STATUS_WAIT_EXPRESS_PRINT)){
            q.append(" AND (t.sys_status IN(?) AND t.express_print_time <= '2000-01-01 00:00:00' AND t.belong_type in (0,2)) ").add(Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            q.append(" AND (t.sys_status IN(?) AND t.belong_type in (0,2)) ").add(Trade.SYS_STATUS_FINISHED_AUDIT);
        }
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, params.getWarehouseType(), params.getWarehouseIds());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_cancel", params.getIsCancel());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_refund", params.getIsRefund());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_halt", params.getIsHalt());

        Query tbOrderQuery = new Query().append(" company_id = ?").add(staff.getCompanyId());
        tbOrderQuery.append(" AND sys_status IN(?)").add(Trade.SYS_STATUS_FINISHED_AUDIT)
                .append(" AND o.enable_status in (1,2)")
                .append(" AND belong_sid in (").append(" select sid FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE ")
                .append(q.getQ()).add(q.getArgs()).append(")");

        if(Objects.equal(params.getMiniHotItem(),1)){
            tbOrderQuery.append(" AND ((combine_id>0) OR (combine_id=0 AND type IN(0,1))) ");
        }else {
            tbOrderQuery.append(" AND combine_id = 0 ");
        }

        //添加商品级别的处理
        boolean hasItemQuery = params.getContext().hasItemQuery();
        if (hasItemQuery) {
            Query q1 = new Query();
            q1.setOrderTable(q.getOrderTable());
            setUniqueCodeTable(staff, q1);
            tradeSqlQueryBuilder.buildItemQuery(staff, q1, params);
            if (q1.isStopQuery()) {
                tbOrderQuery.setStopQuery(true);
                tbOrderQuery.setStopReason(q1.getStopReason());
                orders.setList(new ArrayList());
                return  orders;
            }

            if (q1.getQ().length() > 0) {
                tbOrderQuery.and().append("(");
                tbOrderQuery.append(q1.getQ()).add(q1.getArgs());
                tbOrderQuery.append(")");
            }else{
                params.setCheckItem(false);
            }
        }
        Query tbOrderQuerySrc = null;
        if(java.util.Objects.equals(1,params.getHotItemType())){
            tbOrderQuerySrc = new Query();
            tbOrderQuerySrc.setQ(new StringBuilder(tbOrderQuery.getQ().toString()));
            List<Object> args = new ArrayList<>(tbOrderQuery.getArgs().size());
            args.addAll(tbOrderQuery.getArgs());
            tbOrderQuerySrc.setArgs(args);
            tbOrderQuery.append(" group by item_sys_id ");
        }else {
            tbOrderQuery.append(" group by item_sys_id,sku_sys_id ");
        }

        TradeConfig tradeConfig = tradeConfigService.get(staff);
        String itemNumExcludeVirtual = "1=2", itemNumExcludeNonConsign = "1=2";

        // 虚拟商品不统计
        if (tradeConfig != null && Objects.equal(tradeConfig.getItemNumExcludeVirtual(),1)) {
            itemNumExcludeVirtual = "1=1";
        }
        // 无需发货商品不统计
        if (tradeConfig != null  && Objects.equal(tradeConfig.getItemNumExcludeNonConsign(), 1)) {
            itemNumExcludeNonConsign = "1=1";
        }



        Query query4Count = new Query();
        query4Count.append(" (select 1 from ").append("order_not_consign").append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o where")
                .append(tbOrderQuery.getQ()).add(tbOrderQuery.getArgs()).append(") a");
        Query query = new Query();
        query.append(" 1=1");


        AbsLogBuilder logBuilder = new QueryLogBuilder(staff).append("queryHotItem耗时统计").setBaseTooklmt(100L).append("统计方式", java.util.Objects.equals(1, params.getHotItemType())?"按款":"按商品").startTimer();

        long t0 = System.currentTimeMillis();

        if(params.getPage()!=null && Objects.equal(params.getPage().getPageNo(),1)){
            orders.setTotal(NumberUtils.Integer2Long(tbOrderDao.queryOrdersCount(staff,query4Count.toString(), query)));
            StringBuilder sql4Count = new StringBuilder();
            sql4Count.append("select count(*) from ").append(query4Count);
            logger.debug(LogHelper.buildLog(staff,String.format("queryHotItem count dbNo:%s,queryId:%s,total:%s,took:%s ms,sql:%s",staff.getDbKey(),params.getQueryId(),orders.getTotal(),System.currentTimeMillis()-t0,sql4Count)));
            logBuilder.recordTimer("queryHotItem count");
        }

        tbOrderQuery.append(" order by num desc,trade_num desc,sys_outer_id desc");
        String fields = String.format("count(distinct belong_sid) as trade_num,item_sys_id,sku_sys_id,sys_outer_id,sys_title,short_title,sys_sku_properties_name,id," +
                "sys_sku_properties_alias,sys_pic_path,sys_item_remark,sys_sku_remark,sid,pic_path,source,sys_item_changed,item_changed,insufficient_canceled,sys_consigned,relation_changed,type," +
                "sum(case when ((is_virtual=1 and %s) or (non_consign=1 and %s))  then 0 else num end) num", itemNumExcludeVirtual, itemNumExcludeNonConsign);
        List<TbOrder> tbOrders = tbOrderDao.queryOrders(staff, "order_not_consign", "o",fields, tbOrderQuery, params.getPage());
        StringBuilder sql = new StringBuilder();
        sql.append("select ").append(fields).append(" from order_not_consign").append("_").append(staff.getDbInfo().getOrderDbNo())
                .append(" o  where ").append(tbOrderQuery);
        logger.debug(LogHelper.buildLog(staff,String.format("queryHotItem search dbNo:%s,queryId:%s,numFound:%s,took:%s ms,sql:%s",staff.getDbKey(),params.getQueryId(),CollectionUtils.isEmpty(tbOrders)?0:tbOrders.size(),System.currentTimeMillis()-t0,sql)));
        logBuilder.recordTimer("queryHotItem search");

        if(CollectionUtils.isEmpty(tbOrders)){
            orders.setList(Collections.EMPTY_LIST);
            return  orders;
        }

        List<Order> list = new ArrayList<>(tbOrders.size());
        list.addAll(tbOrders);
        orders.setList(list);
        handleHotItemDetail(staff,params,tbOrders.stream().filter(o->o.getSkuSysId()!=null && o.getSkuSysId()>0).collect(Collectors.toList()), tbOrderQuerySrc, logBuilder);
        logBuilder.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV, staff.getCompanyId()) ? 1L : 3500L).printDebug(logger);
        return orders;
    }


    /**
     *
     * 获取spu下 按 sku分组的数据，order by trade_num desc,sys_outer_id desc
     * 设置好spu下itemDetails，按tradeNum倒序排
     *
     * @param staff
     * @param params
     * @param orders
     * @param tbOrderQuery
     */
    private void handleHotItemDetail(Staff staff, TradeQueryParams params, List<Order> orders,Query tbOrderQuery ,AbsLogBuilder logBuilder) {
        if(!java.util.Objects.equals(1,params.getHotItemType()) || CollectionUtils.isEmpty(orders)){
            return;
        }
        fillSysItemInfo(staff,orders,logBuilder);

        Set<Long> itemSysIds = orders.stream().map(Order::getItemSysId).collect(Collectors.toSet());

        tbOrderQuery.append(" AND o.sku_sys_id>0  AND o.item_sys_id in ").append(itemSysIds.stream().map(o->"?").collect(Collectors.joining(",","(",")")))
                .add(itemSysIds);

        tbOrderQuery.append(" group by item_sys_id,sku_sys_id ");
        String fields = "count(distinct belong_sid) as trade_num,item_sys_id,sku_sys_id,sys_outer_id,sys_title,short_title,sys_sku_properties_name,id,sys_sku_properties_alias";

        long t0 = System.currentTimeMillis();
        Page page = new Page(1,params.getPage().getPageSize()*10);
        List<TbOrder> tbOrders = tbOrderDao.queryOrders(staff, "order_not_consign", "o",fields, tbOrderQuery, page);
        StringBuilder sql = new StringBuilder();
        sql.append("select ").append(fields).append(" from order_not_consign").append("_").append(staff.getDbInfo().getOrderDbNo())
                .append(" o  where ").append(tbOrderQuery);
        logger.debug(LogHelper.buildLog(staff,String.format("queryHotItemDetail search dbNo:%s,queryId:%s,numFound:%s,took:%s ms,sql:%s",staff.getDbKey(),params.getQueryId(),CollectionUtils.isEmpty(tbOrders)?0:tbOrders.size(),System.currentTimeMillis()-t0,sql)));

        logBuilder.recordTimer("queryHotItemDetail");


        Map<Long, List<TbOrder>> itemSysIdGroupedData = tbOrders.stream()
                .collect(Collectors.groupingBy(
                        Order::getItemSysId, // 分组键
                        HashMap::new,
                        Collectors.toList()
                ));

        orders.forEach(o->{
            List<TbOrder> orderList = itemSysIdGroupedData.get(o.getItemSysId());
            if(CollectionUtils.isNotEmpty(orderList)){
                List<TradeHotItemDetail> itemDetails = Lists.newArrayListWithExpectedSize(orderList.size());
                orderList.forEach(obj->{
                    itemDetails.add(TradeHotItemDetail.builder().tradeNum(obj.getTradeNum()).sysOuterId(obj.getSysOuterId())
                                    .sysSkuPropertiesName(obj.getSysSkuPropertiesName()).sysSkuPropertiesAlias(obj.getSysSkuPropertiesAlias())
                                    .sysTitle(obj.getSysTitle()).title(obj.getTitle()).build());
                });
                // 按：tradeNum倒序排
                itemDetails.sort(Comparator.comparing(TradeHotItemDetail::getTradeNum,Comparator.reverseOrder()));
                o.setItemDetails(itemDetails);
            }
        });
        logBuilder.recordTimer("setItemDetails");

    }

    /**
     *
     * 填充spu的图片，编码，名称
     *
     * @param staff
     * @param orders
     * @param logBuilder
     */
    private void fillSysItemInfo(Staff staff,List<Order> orders,AbsLogBuilder logBuilder){
        if(CollectionUtils.isEmpty(orders)){
            return;
        }

        Set<Long> itemSysIds = orders.stream().map(Order::getItemSysId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(itemSysIds)){
            return;
        }

        Map<Long, DmjItem> map = new HashMap<>();

        List<List<Long>> partition = Lists.partition(Lists.newArrayList(itemSysIds), 500);
        for (List<Long> itemIds : partition) {
            List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, itemIds, "picPath,title,sysItemId,outerId");
            for (DmjItem item : itemList) {
                map.put(item.getSysItemId(), item);
            }
        }

        orders.forEach(o->{
            DmjItem target = map.get(o.getItemSysId());
            if(target!=null){
                o.setSysPicPath(target.getPicPath());
                o.setPicPath(target.getPicPath());
                o.setSysOuterId(target.getOuterId());
                o.setOuterId(target.getOuterId());
                o.setSysTitle(target.getTitle());
                o.setTitle(target.getTitle());
                o.setMainOuterId(target.getOuterId());
            }
        });

        logBuilder.recordTimer("fillSysItemInfo");
    }

    @Override
    public List<TbOrder> queryFieldByItemSkuIds(Staff staff, ItemQueryParams params) {
        Query q = new Query().append("company_id = ?").add(staff.getCompanyId());
        q.append(" AND sys_status IN(?, ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT);
        for (ItemParams param : params.getItemParams()) {
            //交易库sku_id为null，纯商品与sku商品混合需要拼接sku_id is null
            if ("skuId".equals(param.getName()) && CollectionUtils.isNotEmpty(param.getValues())) {
                q.append(" AND (").append(param.getField()).append(" IS NULL");
                tradeSqlQueryBuilder.buildArrayOrQuery(q, param.getField(), param.getValues().toArray());
                q.append(")");
            } else {
                tradeSqlQueryBuilder.buildListQuery(q, param.getField(), param.getValues());
            }
        }
        q.append(" AND enable_status = 1 AND is_cancel = 0");
        q.append(" AND item_sys_id");
        if (params.isMatch()) {//查询商品对应关系为改动的子订单
            q.append(" > 0 AND relation_changed = 0");
        } else {//查询未匹配系统商品的子订单
            q.append(" < 0");
        }
        q.append(" group by num_iid, sku_id");
        return tbOrderDao.queryOrders(staff, "order_not_consign", "num_iid, sku_id, group_concat(sid) as tid", q, params.getPage());
    }

    @Override
    public List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder, String... sysStatusList) {
        return queryByOutSid(staff,outSid,queryOrder,true,sysStatusList);
    }

    @Override
    public List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder, boolean needFill,String... sysStatusList) {
        Query q = new Query();
        outSid = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(),outSid);
        q.append("company_id = ? AND out_sid = ? AND (enable_status = 1 OR enable_status = 2)").add(staff.getCompanyId()).add(outSid);
        if (sysStatusList != null && sysStatusList.length > 0) {
            q.and().append("sys_status IN(");
            for (int i = 0; i < sysStatusList.length; i++) {
                q.conjunct(", ", i > 0).append("?").add(sysStatusList[i]);
            }
            q.append(")");
        }
        q.setSort("sid DESC");
        q.setContainHashKey(true);

        List<TbTrade> tbTrades = tbTradeDao.queryTrades(staff, null, q, new Page(), false, false);
        if (CollectionUtils.isEmpty(tbTrades)) {
            Long sid = multiPacksPrintTradeLogService.querySidByOutSid(staff, outSid);
            if (sid != null) {
                TbTrade tbTrade = tbTradeDao.queryBySid(staff, sid);
                if (tbTrade != null) {
                    tbTrades.add(tbTrade);
                }
            }
        }

        if (needFill) {
            tradeFillService.fill(staff, tbTrades);
        }
        return !tbTrades.isEmpty() && queryOrder ? tradeAssembleBusiness.bindOrders(staff, tbTrades, true) : TradeUtils.toTrades(tbTrades);
    }

    @Override
    public List<Trade> queryByPickingParams(Staff staff, TradePickingParams tradePickingParams, boolean queryOrder, String... sysStatusList) {
        Query q = new Query();
        //根据内部单号查询
        if ("shortId".equals(tradePickingParams.getPickParamsName())) {
            q.append("company_id = ? AND short_id = ? AND enable_status = 1").add(staff.getCompanyId()).add(tradePickingParams.getValue());
        } else {
            //根据系统订单号查询
            q.append("company_id = ? AND sid = ? AND enable_status = 1").add(staff.getCompanyId()).add(tradePickingParams.getValue());

        }
        if (sysStatusList != null && sysStatusList.length > 0) {
            q.and().append("sys_status IN(");
            for (int i = 0; i < sysStatusList.length; i++) {
                q.conjunct(", ", i > 0).append("?").add(sysStatusList[i]);
            }
            q.append(")");
        }
        q.setSort("sid DESC");
        q.setContainHashKey(true);

        List<TbTrade> tbTrades = tbTradeDao.queryTrades(staff, null, q, new Page(), false, false);

        tradeFillService.fill(staff, tbTrades);
        return !tbTrades.isEmpty() && queryOrder ? tradeAssembleBusiness.bindOrders(staff, tbTrades, true) : TradeUtils.toTrades(tbTrades);
    }

    private Trades queryTradeWaitPack(Staff staff, TradeQueryParams params) {
        Query q = new Query();
        getTradeTable(staff, q, params);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (tradeConfig.getOpenPackageExamine() != null && tradeConfig.getOpenPackageExamine() == 0) {
            return createTrades(null, 0L, params.getPage(), null);
        }
        tradeSqlQueryBuilder.buildWaitPackQuery(q, staff, params, tradeConfig);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return new Trades();
        }
        buildSysConsigned(q, params);
        Trades trades = queryTrades(staff, q, params);
        filterUnnecessaryOrder(trades);
        //补充order的条码信息
        fillTradeInfo(staff, trades.getList());
        return trades;
    }


    /**
     * order添加条码信息
     *
     * @param staff
     * @param tradeList
     */
    @Override
    public void fillTradeInfo(Staff staff, List<Trade> tradeList) {
        if (tradeList != null && tradeList.size() > 0) {
            //纯商品
            Set<Long> sysItemIds = new HashSet<>();
            //规格商品
            Set<Long> sysSkuIds = new HashSet<Long>();
            //如果纯商品Id<0那么是纯商品
            for (Trade trade : tradeList) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    //判断是不是系统商品
                    if (order.getItemSysId() > 0) {
                        Long skuSysId = order.getSkuSysId();
                        //如果sku规格 <= 0  说明是纯商品
                        if (skuSysId == null || skuSysId <= 0) {
                            sysItemIds.add(order.getItemSysId());
                        } else { //否则是规格商品
                            sysSkuIds.add(skuSysId);
                        }
                    }
                }
            }

            //所有纯商品和规格商品Map
            Map<Long, SysItemSku> map = new HashMap<Long, SysItemSku>();
            if (!sysItemIds.isEmpty()) {//纯商品
                List<List<Long>> partition = Lists.partition(Lists.newArrayList(sysItemIds), 50);
                for (List<Long> itemIds : partition) {
                    List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, itemIds, "sysItemId,outerId,barcode");
                    for (DmjItem item : itemList) {
                        map.put(item.getSysItemId(), buildItemSku(item.getSysItemId(), null, item.getOuterId(), item.getBarcode()));
                    }
                }
            }
            if (!sysSkuIds.isEmpty()) {//规格
                List<List<Long>> partition = Lists.partition(Lists.newArrayList(sysSkuIds), 50);
                for (List<Long> skuIds : partition) {
                    List<DmjSku> skuList = itemServiceWrapper.queryBySysSkuIds(staff, skuIds, "sysItemId,sysSkuId,outerId,barcode");
                    for (DmjSku sku : skuList) {
                        map.put(sku.getSysSkuId(), buildItemSku(sku.getSysItemId(), sku.getSysSkuId(), sku.getOuterId(), sku.getBarcode()));
                    }
                }
            }

            //放入order返回
            for (Trade trade : tradeList) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order o : orders) {
                    SysItemSku target = (o.getSkuSysId() == null || o.getSkuSysId() <= 0) ? map.get(o.getItemSysId()) : map.get(o.getSkuSysId());
                    if (target != null) {
                        o.setBarcode(target.getBarcode());
                        o.setMainOuterId(target.getOuterId());
                    }

                }
            }
        }
    }

    @Override
    public List<String> checkIds(Staff staff, TradeQueryParams params) {
        return null;
    }

    @Override
    public Long countTaobaoSeries(Staff staff, TradeQueryParams params) {
        String originSource = params.getSource();
        if (StringUtils.isBlank(params.getSource())) {
            params.setSource(Strings.join(",", TAOBAO_SERIES_PLATFORM));
        } else {
            if (!TAOBAO_SERIES_PLATFORM.contains(params.getSource())) {
                return 0L;
            }
        }
        Query q = getQuery(staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return 0L;
        }
        Long count = tbTradeDao.queryTradesCount(staff, q, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), false, params.getContext().hasTradeExtSearch());
        params.setSource(originSource);
        return count == null ? 0 : count;
    }

    private Trades queryTradePackSplitable(Staff staff, TradeQueryParams params) {
        Query q = new Query();
        getTradeTable(staff, q, params);
        tradeSqlQueryBuilder.buildPackSplitableQuery(q, staff, params);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return new Trades();
        }
        buildSysConsigned(q, params);
        Trades trades = queryTrades(staff, q, params);
        filterUnnecessaryOrder(trades);
        return trades;
    }

    private void filterUnnecessaryOrder(Trades trades) {
        if (trades.getList() != null) {
            for (Trade trade : trades.getList()) {
                TradeUtils.setOrders(trade, filterOrders(TradeUtils.getOrders4Trade(trade)));
                TradeStatusHelper.__filterNotWaitSellerSendGoodsTrade(trade);
            }
        }
    }

    private Trades queryTradeWaitWeigh(Staff staff, TradeQueryParams params) {
        Query q = new Query();
        getTradeTable(staff, q, params);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (tradeConfig.getOpenPackageWeigh() != null && tradeConfig.getOpenPackageWeigh() == 0) {
            return createTrades(null, 0L, params.getPage(), null);
        }
        tradeSqlQueryBuilder.buildWaitWeighQuery(q, staff, params, tradeConfig);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return new Trades();
        }
        buildSysConsigned(q, params);
        Trades trades = queryTrades(staff, q, params);
        filterUnnecessaryOrder(trades);
        return trades;
    }

    private Trades queryTradeWaitConsign(Staff staff, TradeQueryParams params) {
        Query q = new Query();
        getTradeTable(staff, q, params);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        tradeSqlQueryBuilder.buildWaitConsignQuery(q, staff, params, tradeConfig);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return new Trades();
        }
        buildSysConsigned(q, params);

        buildExpressQuery(q, params.getExpress());
        buildLogisticsCompanyQuery(q, params.getLogisticsCompanyIds());
        tradeSqlQueryBuilder.buildDateRangeQuery(q, params.getTimeType(), params.getStartTime(), params.getEndTime());
        buildSqlSort(staff, tradeConfig, q, params.getSort(), null);
        return queryTrades(staff, q, params);
    }

    private List<Order> filterOrders(List<Order> orders) {
        List<Order> list = new ArrayList<Order>(orders.size());
        for (Order order : orders) {
            if (order.isVirtual() || order.ifNonConsign()) {
                continue;
            }
            if (order.getSuits() != null) {
                list.addAll(order.getSuits());
                continue;
            }
            list.add(order);
        }
        return list;
    }

    public static final List<String> SLOW_SORT_FIELDS = Arrays.asList("buyer_nick","payment","buyer_message","sys_memo","seller_memo");

    private Trades _search(Staff staff, TradeConfig tradeConfig, TradeQueryParams params) {
        Query q = getQuery(staff, params);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return Trades.createTrades(new ArrayList<>(),0L,params.getPage(), params.getSort());
        }
        buildSqlSort(staff, tradeConfig, q, params.getSort(), params.getItemOrder());
        if (params.getSort() != null && params.getSort().getField() != null) {
            if (SLOW_SORT_FIELDS.contains(params.getSort().getField())) {
                params.getContext().addSuggests("去除或换其他字段进行排序,当前排序:"+ getFieldChName(params.getSort().getField()));
            }
        }

        buildSqlSortBySortConfig(staff, tradeConfig, params, q);
        if (java.util.Objects.equals(params.getQueryId(), QUERY_WAIT_SELF_SEND_GOODS)) {
            q.and().append(" not (t.convert_type = 1 AND  t.belong_type in(1,3)  and t.dest_id>0) ");
        }

        String sort = q.getSort();
        if (TradeQueryParams.TRADE_EXPORT.equals(params.getExportSource()) && (sort.contains("ASC") || sort.contains("DESC"))) {
            q.setSort(sort + " , t.sid ASC");
        }

        forceIndexKey(staff,tradeConfig, q, params);
        return queryTrades(staff, q, params);
    }

    private void forceIndexKey(Staff staff, TradeConfig tradeConfig,Query q, TradeQueryParams params) {
        TradeQueryContext context = params.getContext();
        if (Objects.equal(params.getQueryFlag(),1)) {
            return;
        }
        // 包含sid tid out_sid等条件的 不走强制索引
        if (params.getContext().isUniqueQuery()) {
            return;
        }
        //total统计不适合走索引的 查询列表也同样不适用
        if (!q.isAllowTradeCountIndex()) {
            return;
        }
        //前面已经指定过强制索引的 这里不再指定
        if (StringUtils.isNotBlank(q.getTradeIndex())) {
            return;
        }

        String timeType = params.getTimeType() == null?"pay_time":params.getTimeType();
        String sort = params.getSort() !=null? params.getSort().getField():null;

        if (notNull(params.getNumIid()) || notNull(params.getSkuId()) || notNull(params.getSysItemIds() )
                || params.getContext().isUniqueQuery()
                || notNull(params.getExceptIds()) || notNull(params.getExcludeExceptIds()) || notNull(params.getExceptionStatus()) || notNull(params.getExcludeExceptionStatus())
                || notNull(params.getTagIds()) || notNull(params.getExcludeTagIds())
        ) {
            return;
        }

        tradeSearchSupport.forceIndexKey(staff,context,q, timeType,params.getStartTime(),params.getEndTime(),sort,params.getSysStatus());
    }

    private <T> boolean notNull(T[] values){
        return values !=null && values.length > 0;
    }
    private void buildSqlSortBySortConfig(Staff staff, TradeConfig tradeConfig, TradeQueryParams params, Query q) {
        List<SortConfig> sortConfigs = params.getSortConfigs();
        if (CollectionUtils.isEmpty(sortConfigs)) {
            return;
        }

        String sql = sortConfigs.stream()
                .filter(t -> !SortConfig.SINGLE_DATA_TYPE.equals(t.getDataType()))
                .sorted(Comparator.nullsFirst(Comparator.comparing(SortConfig::getIndex)))
                .map(t -> t.getSql())
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
        String sort = StringUtils.trimToEmpty(q.getSort());
        if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(sql)) {
            sort += ",";
        }
        sort += sql;
        if (StringUtils.isNotBlank(sort)) {
            q.setSort(sort);
        }
    }

    private Query getQuery(Staff staff, TradeQueryParams params) {
        Query q = new Query();
        getTradeTable(staff, q, params);
        reBaseTimer(params);
        buildSqlQuery(staff, q, params);
        recordTimer(params,"buildSqlQuery");
        return q;
    }

    private String getTradeTable(Staff staff, Query q, TradeQueryParams params) {
        long queryId = params.getQueryId() != null ? params.getQueryId() : 0L;
        //开启了数据分离+特定的查询+没有传入运单号+
        if (!params.getContext().isUniqueQuery() && params.getContext().needMergeSysStatus() && !params.isAssignTrade()
                && !(params.getPackageStatus() != null && params.getPackageStatus().length > 0)) {
            if (QUERY_BEFORE_CONSIGN.contains(queryId)) {
                q.setTradeTable("trade_not_consign");
                q.setOrderTable("order_not_consign");
            }else if (Objects.equal(queryId,QUERY_PRINT_FAHUO) && tradeLocalConfigurable.isQueryPrintFahuoInNotConsign(staff.getCompanyId())){
                //白名单用户 订单打印->打印发货 走小表
                q.setTradeTable("trade_not_consign");
                q.setOrderTable("order_not_consign");
            }
        }

        setUniqueCodeTable(staff, q);
        return q.getTradeTable();
    }

    private void setUniqueCodeTable(Staff staff, Query q) {
        q.setUniqueCodeTable("wave_unique_code_" + staff.getDbInfo().getOrderDbNo());
    }

    @Deprecated
    private void buildSqlSort(Staff staff, TradeConfig tradeConfig, Query q, Sort sort, String itemOrder) {
        q.setSortTrade(sort);
//        //如果配置文件开启了，则走sort_string排序逻辑,并且是走小表时候
//        if ("trade_not_consign".equalsIgnoreCase(q.getTradeTable()) && tradeConfigService.isOpenStringSort(staff)) {
//            if (itemOrder != null && !(itemOrder = itemOrder.trim()).isEmpty()) {
//                if ("single".equals(itemOrder)) {
//                    q.setSort("t.item_kind_num ASC, t.sort_string ASC");
//                } else if ("multi".equals(itemOrder)) {
//                    q.setSort("t.item_kind_num DESC, t.sort_string DESC");
//                }
//            }
//        }else{
        if (itemOrder != null && !(itemOrder = itemOrder.trim()).isEmpty()) {
            if ("single".equals(itemOrder)) {
                q.setSort("t.item_kind_num ASC, CONVERT( t.`sys_outer_id` USING 'GBK') ASC, t.item_num ASC,t.sid ASC ,t.pay_time ASC ");
            } else if ("multi".equals(itemOrder)) {
                q.setSort("t.item_kind_num DESC,  CONVERT( t.`sys_outer_id` USING 'GBK') ASC, t.item_num ASC , t.sid ASC,t.pay_time DESC ");
            } else if ("outerIdDesc".equals(itemOrder) || "outerIdAsc".equals(itemOrder) || "orderTypeSort".equals(itemOrder)) {
                //https://gykj.yuque.com/entavv/xb9xi5/fykbd511we6icg8h
                String orderType = "ASC";
                if ("outerIdDesc".equals(itemOrder)) {
                    orderType = "DESC";
                }
                q.setSort("CONVERT( t.`sys_outer_id` USING 'GBK') " + orderType + "," +
                        "t.item_kind_num " + orderType + "," +
                        "t.item_num " + orderType + "," +
                        "t.pay_time " + orderType);
            }
        }
//        }
        if (q.getSort() == null && sort != null) {
            StringBuilder qSort = new StringBuilder();
            String field = sort.getField();
            if (field == null || (field = field.trim()).isEmpty()) {
                return;
            }
            String orderType = sort.getOrder();
            //智能审核单独排序
            if (TradeExtendConfigsEnum.AI_AUDIT_ORDER.getKey().equalsIgnoreCase(field)) {
                qSort.append(orderType);
            } else if ("sysStatus".equals(sort.getField())) {
                //系统状态排序
                buildSysStatusSort(qSort, tradeConfig);
            } else if ("excep".equals(sort.getField())) {
                //异常状态排序
                buildExcepSort(qSort, sort, false);
            } else if ("tradeStatus".equals(sort.getField())) {
                //异常状态排序
                buildTradeStatuspSort(qSort, sort, tradeConfig);
            } else if("timeout_action_time".equals(sort.getField())){
                buildTimeoutActionTimeSort(qSort,orderType);
            } else {
                if (!"ASC".equalsIgnoreCase(orderType) && !"DESC".equalsIgnoreCase(orderType)) {
                    orderType = "DESC";
                }
                if ("payment".equals(field)) {
                    if ("ASC".equalsIgnoreCase(orderType)) {
                        qSort.append("   CAST(ifnull(tp.payment,t.payment) AS DECIMAL(15, 2)) ASC ");
                    } else {
                        qSort.append("   CAST(ifnull(tp.payment,t.payment) AS DECIMAL(15, 2)) DESC ");
                    }
                } else if ("weight".equalsIgnoreCase(field)) {
                    qSort.append("t.`").append(field).append("` ").append(orderType.toUpperCase()).append(", t.created DESC ");
                } else if ("buyer_message".equals(sort.getField()) || "seller_memo".equals(sort.getField()) || "sys_memo".equals(sort.getField())) {
                    qSort.append("(t.`").append(field).append("` is null OR t.`").append(field).append("` = '' ) ").append(orderType.toUpperCase()).append(",").append(" CONVERT( t.`").append(field).append("` USING 'GBK') ").append(orderType.toUpperCase()).append(" ,t.pay_time ").append(orderType.toUpperCase());
                } else {
                    if (!SortConfig.SUPPORT_SORT_FIELD_LIST.contains(field.toLowerCase(Locale.ROOT))) {
                        return;
                    }
                    qSort.append("t.`").append(field).append("` ").append(orderType.toUpperCase());
                }
                //TODO sid排序 可能会引发重排，
//                if ("DESC".equalsIgnoreCase(orderType)){
//                    qSort.append(",t.user_id ASC");
//                }
            }
            q.setSort(qSort.toString());
        }
    }

    /**
     * 先按异常排序，再按系统状态排序
     * 详情见: https://gykj.yuque.com/entavv/xb9xi5/unw31s
     *
     * @param sb
     * @param sort
     */
    private void buildTradeStatuspSort(StringBuilder sb, Sort sort, TradeConfig tradeConfig) {
        StringBuilder except = buildExcepSort(sb, sort, true);
        if (except != null && except.length() > 0) {
            buildSysStatusSort(sb.append(","), tradeConfig);
        } else {
            buildSysStatusSort(sb, tradeConfig);
        }
    }

    /**
     * 按前端传递的异常顺序按异常状态排序
     * 详情见：https://gykj.yuque.com/entavv/xb9xi5/unw31s
     *
     * @param sb
     * @param sort
     */
    private StringBuilder buildExcepSort(StringBuilder sb, Sort sort, boolean isTradeStatus) {
        List<SortExcepLevel> sortExcepLevel = sort.getSortExcepLevel();
        if (CollectionUtils.isEmpty(sortExcepLevel)) {
            return null;
        }
        for (int i = 0; i < sortExcepLevel.size(); i++) {
            SortExcepLevel excepLevel = sortExcepLevel.get(i);
            //系统异常
            if (excepLevel.getType() == 0 && TradeQueryParams.SYS_EXCEP_SETS.contains(excepLevel.getExcepName())) {
                sb.append(excepLevel.getExcepName().replace(" ", "")).append(" DESC,");
            } else if (excepLevel.getType() == 1 && StringUtils.isNumeric(excepLevel.getExcepTagId())) {
                sb.append("自定义标签").append(i).append(" DESC,");
            }
        }
        if (!isTradeStatus) {
            return sb.append("t.pay_time ASC");
        } else {
            return sb.append(sb.substring(0, sb.length() - 1));
        }
    }

    /**
     * 按系统状态排序
     * 详情见：https://gykj.yuque.com/entavv/xb9xi5/unw31s
     *
     * @param sb
     * @param tradeConfig
     */
    private void buildSysStatusSort(StringBuilder sb, TradeConfig tradeConfig) {
        //待付款
        sb.append("(CASE WHEN t.sys_status ='").append(Trade.SYS_STATUS_WAIT_BUYER_PAY).append("' THEN 0 ");
        //待审核
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_WAIT_AUDIT).append("' THEN 1 ");
        //待财审
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT).append("' THEN 2 ");
        //待打印快递单
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED_AUDIT).append("' AND t.convert_type in (0,1)  AND t.belong_type in (0,2) AND t.express_print_time = '").append(TradeTimeUtils.INIT_DATE_STR).append("' THEN 3 ");
        //待包装
        if (tradeConfig.getOpenPackageExamine() == 1) {
            sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED_AUDIT).append("' AND t.convert_type in (0,1) AND t.belong_type in (0,2) AND t.is_package =0 THEN 4 ");
        }
        //待称重
        if (tradeConfig.getOpenPackageWeigh() == 1) {
            sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED_AUDIT).append("' AND t.convert_type in (0,1) AND t.belong_type in (0,2) AND t.is_weigh =0 THEN 5 ");
        }
        //待发货
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_WAIT_SEND_GOODS).append("' AND t.convert_type in (0,1) AND t.belong_type in (0,2) THEN 6 ");
        //待供销商发货
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED_AUDIT).append("' AND t.convert_type = 1 AND t.belong_type = 1 THEN 7 ");
        //卖家已发货
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_SELLER_SEND_GOODS).append("'  THEN 8 ");
        //交易成功
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED).append("'  THEN 9 ");
        //交易作废
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_CANCEL).append("'  THEN 10 ");
        //交易关闭
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_CLOSED).append("'  THEN 11 ELSE 99 END ) ASC, t.pay_time ASC");


    }

    private void buildTimeoutActionTimeSort(StringBuilder sb, String orderType){
        // 订单列表排序排两部分：1订单状态发货之前状态（待审核，待财审，发货中（待打印快递单，待包装，待称重，待发货），待供销商发货）的订单
        //2.订单状态为【卖家已发货】但是is_upload不等于1
        // 以上两部分订单才参与排序，其他状态不管是正序还是倒叙都排在最后
        String currentOrderType = " ASC ";
        String date = " '2900-01-01 00:00:00.0' ";
        if ("DESC".equalsIgnoreCase(orderType)) {
            currentOrderType = " DESC ";
            date = " '1900-01-01 00:00:00.0' ";
        }
        String defaultTimeoutActionTime = "'2000-01-01 00:00:00.0'";
        //待付款
        sb.append("(CASE  WHEN t.sys_status ='").append(Trade.SYS_STATUS_WAIT_BUYER_PAY).append("' THEN ").append(date);
        //交易成功
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_FINISHED).append("' THEN ").append(date);
        //交易关闭
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_CLOSED).append("' THEN ").append(date);
        //卖家已发货
        sb.append(" WHEN t.sys_status ='").append(Trade.SYS_STATUS_SELLER_SEND_GOODS).append("' AND t.is_upload=1 ")
                .append(" THEN ")
                .append(date)
                .append(" ELSE t.timeout_action_time END ) ")
                .append(currentOrderType);
    }


    private void buildSqlQuery(Staff staff, Query q, TradeQueryParams params) {
        int queryType = params.getQueryType();
        TradeConfig tc = tradeConfigService.get(staff);
        tradeSqlQueryBuilder.buildQuery(q, staff, params);
        tradeSqlQueryBuilder.buildExtItemQuantityQuery(q, staff, params);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            return;
        }
        tradeSqlQueryBuilder.buildSysStatusQuery(q, params, tc);
        tradeSqlQueryBuilder.buildStatusQuery(q, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, params.getWarehouseType(), params.getWarehouseIds());
        tradeSqlQueryBuilder.buildOrderRefundStatusQuery(q, staff, params);

        ////跨境业务专属的查询条件
        buildKjQuery(staff, q, params,tc);
        if (q.isStopQuery()) {
            return;
        }

        convertInsertOuterIds(staff, q, params);
        if (q.isStopQuery()) {
            return;
        }

        if(TradeExceptWhiteUtils.openExceptNewSearchCompanyIds(staff)){
            // 开启新的异常查询白名单
            tradeExceptQueryService.buildExceptQuery(staff, q, params);
        }else{
            tradeExceptQueryService.buildOldExceptQuery(staff, q, params);
            /*tradeSqlQueryBuilder.buildExceptionQuery(staff, q, params, false, false);
            // 筛选订单异常状态，支持同时筛选包含和排除指定异常状态 https://gykj.yuque.com/entavv/xb9xi5/pqsth9
            if ((params.getExcludeExceptionStatus() != null && params.getExcludeExceptionStatus().length > 0) ||
                    (params.getExcludeExceptIds() != null && params.getExcludeExceptIds().length > 0)) {
                TradeQueryParams paramsCopy = new TradeQueryParams();
                BeanUtils.copyProperties(params, paramsCopy);
                //原组合：包含+排除中包含是否包含除无异常之外其他异常，因为接下来排除会加入异常中
                boolean unchanged = (params.getExceptionStatus() == null || params.getExceptionStatus().length <= 0)
                        && (params.getExceptIds() == null || params.getExceptIds().length <= 0
                        || (params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])));
                paramsCopy.setExceptionStatus(paramsCopy.getExcludeExceptionStatus());
                paramsCopy.setExceptIds(paramsCopy.getExcludeExceptIds());
                // 排除
                paramsCopy.setOnlyContain(2);
                tradeSqlQueryBuilder.buildExceptionQuery(staff, q, paramsCopy, true, unchanged);
            }
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_refund", params.getIsRefund());
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_halt", params.getIsHalt());*/
        }
        if (!params.getContext().isHaveCancel()) {
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_cancel", params.getIsCancel());
        }
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.source", Strings.getAsStringArray(params.getSource(), ",", true));
        //支持排除订单来源
        buildExcludeSource(q,params);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.sub_source", Strings.getAsStringArray(params.getSubSource(), ",", true));

        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_urgent", params.getIsUrgent());
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.stock_status", params.getStockStatus());
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.status", params.getPlatformStatus());
        //排除
        tradeSqlQueryBuilder.buildNotInQuery(q, "t.sid", params.getExcludeSids());
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.sid", params.getSid());
        if (params.getSid() != null && params.getSid().length > 0) {
            q.setContainHashKey(true);
        }
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.short_id", params.getShortId());
        tradeSqlQueryBuilder.buildTidQuery(staff, q, params);
        if (params.getTid() != null && params.getTid().length > 0) {
            q.setContainHashKey(true);
        }
        tradeSqlQueryBuilder.buildOutSidQuery(q, params);

        //查询供销商对应的分销商店铺
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.taobao_id", params.getTaobaoIds());
        buildSysConsigned(q, params);
        tradeSqlQueryBuilder.buildMixKeyQuery(q,staff, params);
        if (StringUtils.isNotEmpty(params.getMixKey())) {
            q.setContainHashKey(true);
        }

        //如果存在customerNick查询条件, 则追加到buyerNick查询条件中,并且在tradeType中追加一个21, 即客户订单
        buildCustomerQuery(q, params);

//        tradeSqlQueryBuilder.buildArrayQuery(q, "t.open_uid", params.getOpenUid());
        try {
            tradeSqlQueryBuilder.buildBuyerNickQuery(staff, q, params);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "查询订单时加密买家昵称／旺旺号出错: " + e.getMessage()), e);
        }
        buildReceiverQuery(staff, q, params);
        tradeSqlQueryBuilder.buildLogisticsCodeQuery(q,params);
        if ((params.getContext().existAnySource(CommonConstants.PLAT_FORM_TYPE_PDD) && (params.getWrapperDescriptionFlag() != null))) {
            if (params.getWrapperDescriptionFlag() != null) {
                if (params.getWrapperDescriptionFlag() == 1) {
                    q.and().append(" e.wrapper_description = '指定包材' ");
                } else {
                    q.and().append(" e.wrapper_description is null ");
                }
            }
        } else {
            params.setWrapperDescriptionFlag(null);
        }
        //对于tradeTyp的处理 tradeType代表当前查询的订单类型 比如:3平台订单 4手工订单
        tradeSqlQueryBuilder.buildTradeTypeQuery(staff, q, params.getQueryId(), Strings.getAsIntArray(params.getTradeType(), ",", true), Strings.getAsIntArray(params.getExcludeTradeType(), ",", true), params.getTradeTypeNewParams());

        // 构建供应商查询
        tradeSqlQueryBuilder.buildOrderSupplierQuery(staff, q, params.getSupplierIds());

        //商品标签查询
        tradeSqlQueryBuilder.buildItemTagIdQuery(staff, q, params.getTradeQueryItemTagIdsParams());

        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.address_type", params.getAddressType());
//        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.can_delivered", params.getCanDelivered());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_weigh", params.getIsWeigh());
        buildExpressQuery(q, params.getExpress());
        buildLogisticsCompanyQuery(q, params.getLogisticsCompanyIds());
        //买家留言、卖家备注等查询,有商品相关的属性字段
        buildMemoQuery(staff, q, params);

        if (TradeDiamondUtils.openNewLabelRead(staff)) {
            //包含条件
            buildTradeLabelContainQuery(staff, q, params);
            //排除条件
            buildTradeLabelNotContainQuery(staff, q, params);
            //同时条件
            buildTradeLabelAllContainQuery(staff, q, params);

        }

        buildTradeO2oSysStatusSql(staff,q,params);
        buildDateRangeQuery(staff,q, params);

        tradeSqlQueryBuilder.buildDeliveryTimeQuery(q, params.getDeliveryTimes());
        tradeSqlQueryBuilder.buildSignTimeQuery(q, params.getSignTimes());

        if (null != params.getContainExpress()) {
            q.append(" AND ").append(params.getContainExpress() ? "t.template_id > 0" : "t.template_id = -1");
        }
        if (null != params.getContainOutsid()) {
            q.append(" AND ").append(params.getContainOutsid() ? "(t.out_sid != '' and t.out_sid is not null)" : "(t.out_sid = '' or t.out_sid is null)");
        }
        buildPrintStatus(q, "t.express_print_time", params.getExpressStatus());
        buildPrintStatus(q, "t.deliver_print_time", params.getDeliverStatus());
        buildPrintStatus(q, "t.assembly_print_time", params.getAssemblyStatus());

        //查询某个预售规则下的预售订单时设置
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.presell_rule_id", params.getPresellRuleId());

        //是否有发票查询
        if (params.getHasInvoice() != null) {
            if (params.getHasInvoice() == 0) {//无发票
                q.and().append("(t.invoice_name = '' OR t.invoice_name IS NULL) AND ( NOT EXISTS (select 1 from trade_invoice_"+staff.getDbInfo().getTradeDbNo()+" inv where inv.company_id = t.company_id  AND inv.sid =  t.sid AND inv.enable_status = 1 ))");
            } else if (params.getHasInvoice() - 1 == 0) {//有发票
                q.and().append("((t.invoice_name != '' AND t.invoice_name IS NOT NULL) OR EXISTS (select 1 from trade_invoice_"+staff.getDbInfo().getTradeDbNo()+" inv where inv.company_id = t.company_id  AND inv.sid =  t.sid AND inv.enable_status = 1 ))");
            }
        }

        //唯品会订单查询设置
        if (StringUtils.isNotBlank(params.getPoNo())) {
            tradeSqlQueryBuilder.buildLikeQuery(q, 1, "t.po_nos", "," + params.getPoNo());
        }

        // 收件人街道
        String[] receiverStreets = params.getReceiverStreets();
        if (null != receiverStreets && receiverStreets.length > 0) {
            tradeSqlQueryBuilder.buildLikeQuery(q, 1, "t.receiver_street", receiverStreets);
        }

        if (params.getPoNos() != null) {
            tradeSqlQueryBuilder.buildLikeQuery(q, 1, "t.po_nos", params.getPoNos());
            if (params.getVipSids() != null) {
                tradeSqlQueryBuilder.buildNotInQuery(q, "t.sid", params.getVipSids());
            }
        }
        tradeSqlQueryBuilder.buildLikeQuery(q, 1, "t.vip_pick_no", params.getVipPickNo());
        tradeSqlQueryBuilder.buildVipStorageNoQuery(q, params);
        buildTradeFromQuery(q, params);
        tradeSqlQueryBuilder.buildDecimalRangeQuery(q, "t.net_weight", params.getNetWeightStart(), params.getNetWeightEnd());
        tradeSqlQueryBuilder.buildDecimalRangeQuery(q, "t.weight", params.getWeightStart(), params.getWeightEnd());
        tradeSqlQueryBuilder.buildNumberRangeQuery(q, "t.item_num", params.getItemCountStart(), params.getItemCountEnd());
        tradeSqlQueryBuilder.buildNumberRangeQuery(q, "t.item_kind_num", params.getItemKindStart(), params.getItemKindEnd());
        tradeSqlQueryBuilder.buildNumberRangeQuery(q, "t.insufficient_num", params.getInsufficientNumStart(), params.getInsufficientNumEnd());
        tradeSqlQueryBuilder.buildNumberRangeQuery(q, "t.print_count", params.getPrintCountStart(), params.getPrintCountEnd());
        tradeSqlQueryBuilder.buildNumberRangeQuery(q, "t.cost", params.getCostLowerLimit(), params.getCostUpperLimit());
        tradeSqlQueryBuilder.buildMergeMoneyRangeQuery(staff, q, "post_fee", params.getPostFeeLowerLimit(), params.getPostFeeUpperLimit());
        tradeSqlQueryBuilder.buildMergeMoneyRangeQuery(staff, q, "theory_post_fee", params.getTheoryPostFeeLowerLimit(), params.getTheoryPostFeeUpperLimit());
        tradeSqlQueryBuilder.buildMergeMoneyRangeQuery(staff, q, "discount_fee", params.getDiscountFeeLowerLimit(), params.getDiscountFeeUpperLimit());
        tradeSqlQueryBuilder.buildOrderNumberRangeQuery(staff, q, "o.discount_rate", params.getDiscountRateLowerLimit(), params.getDiscountRateUpperLimit());
        buildMergeNumQuery(staff,q,params);

        if (!TradeConfig.openNewPaymentShare(tc)) {
            tradeSqlQueryBuilder.buildMergeMoneyRangeQuery(staff, q, "payment", params.getPaymentLowerLimit(), params.getPaymentUpperLimit());
        } else {
            tradeSqlQueryBuilder.buildMergeMoneyRangeQuery(staff, q, "ac_payment", params.getPaymentLowerLimit(), params.getPaymentUpperLimit());

        }

        if (params.getContext().hasExcludeClosedTradeQuery()){
            List<String> sysStatus = new ArrayList<>();
            if (tradeConfigNewService.get(staff, TradeConfigEnum.QUERY_BY_OUTER_ID_EXCLUDE_CONSIGN_TRADE).isOpen()){
                sysStatus.add(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                sysStatus.add(Trade.SYS_STATUS_FINISHED);
            }
            if (tc.openQueryByOuterIdExcludeClosedTrade()){
                sysStatus.add(Trade.SYS_STATUS_CLOSED);
            }
            tradeSqlQueryBuilder.buildExcludeTradeByOrderSysStatusQuery(staff, q, params, sysStatus);
        }

        //根据抖音BIC订单唯一码精确查询
        tradeSqlQueryBuilder.buildArrayQuery(q, "e.bic_unique_code", StringUtils.isEmpty(params.getBicUniqueCode()) ? null : params.getBicUniqueCode().split(","));
        buildInsufficientRateQuery(q, params.getInsufficientRate());

        // 根据常态合作码查询
        if (1 == queryType) {
            tradeSqlQueryBuilder.buildArrayQuery(q, "e.cooperation_no", StringUtils.isEmpty(params.getCooperationNo()) ? null : params.getCooperationNo().split(","));
        } else if (0 == queryType) {
            tradeSqlQueryBuilder.buildLikeQuery(q, 0, "e.cooperation_no", params.getCooperationNo());
        }

        //放心购商家代发店铺id查询
        tradeSqlQueryBuilder.buildArrayQuery(q, "e.mall_mask_id", StringUtils.isEmpty(params.getFxgDfMallMaskIds()) ? null : params.getFxgDfMallMaskIds().split(","));
        // 根据JITX常态合作码查询，这里先用subSource为条件筛选出JITX的订单
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.sub_source", StringUtils.isEmpty(params.getCooperationNoJitx()) ? null : CommonConstants.PLAT_FORM_TYPE_VIPJITX);

        //构建商品查询条件
        buildInWaveQuery(q, params.getInWave(),params);
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.wave_id", params.getWaveId());
        //构建物流过滤条件
        appendExpressFilterCondition(q, params.getCommonIds(), params.getCloudIds());
        //库区类型
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.stock_region_type", params.getStockRegionTypes());
        if (null != params.getWaveId()) {
            q.setContainHashKey(true);
        }

        if (null != params.getQueryId() && params.getQueryId() == QUERY_DELIVER_EXCEPTION) {
            tradeSqlQueryBuilder.buildDeliverAndUploadQuery(q, staff, params);
        }

        //查询consign_record，仅在上传/发货异常界面触发
        if (params.getContext().hasConsignRecordQuery()) {
            tradeSqlQueryBuilder.buildConsignRecordQuery(q, staff, params);
        }

        // 仅包含商品种类条件
        tradeSqlQueryBuilder.buildOnlyContainsQuery(q, staff, params);

        //达人信息
        if (StringUtils.isNotBlank(params.getAuthorId()) || StringUtils.isNotBlank(params.getAuthorName())) {
            tradeSqlQueryBuilder.buildAuthorInfoQuery(q, staff, params);
        }

        //业务员信息
        if (StringUtils.isNotBlank(params.getSalesmanName())) {
            tradeSqlQueryBuilder.buildTradeSalesmanQuery(q, staff, params);
        }

        //按单采购单号查询
        if (StringUtils.isNotBlank(params.getCaigouCode())) {
            tradeSqlQueryBuilder.buildOrderCaigouCodeQuery(q, staff, params);
        }

        //图片质检结果
        if (StringUtils.isNotBlank(params.getBtasPictureQualityResult())) {
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.sub_source", CommonConstants.PLAT_FORM_TYPE_BTAS);
            tradeSqlQueryBuilder.buildBtasPictureQualityResultQuery(q, staff, params);
        }

        //BTAS订单码检索
        if (StringUtils.isNotBlank(params.getBtasOrderCode())) {
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.sub_source", CommonConstants.PLAT_FORM_TYPE_BTAS);
            tradeSqlQueryBuilder.buildBtasOrderCodeQuery(q, staff, params);
        }

        //团长信息
        if (StringUtils.isNotBlank(params.getDistributorId()) || StringUtils.isNotBlank(params.getDistributorName()) || StringUtils.isNotBlank(params.getActivityName()) ) {
            tradeSqlQueryBuilder.buildDistributorInfoQuery(q, staff, params);
        }

        /**
         * 毛利润区间
         */
        if (params.getMinGrossProfit() != null || params.getMaxGrossProfit() != null) {
            tradeSqlQueryBuilder.buildDecimalRangeQuery(q, "t.gross_profit", params.getMinGrossProfit(), params.getMaxGrossProfit());
        }
        /**
         * 毛利率区间
         */
        if (params.getMinGrossProfitRate() != null || params.getMaxGrossProfitRate() != null) {
            tradeSqlQueryBuilder.buildDecimalRangeQuery(q, "t.gross_profit_rate", params.getMinGrossProfitRate(), params.getMaxGrossProfitRate());
        }

        // 物流预警
        tradeSqlQueryBuilder.buildLogisticsWarningQuery(staff, q, params);


        tradeSqlQueryBuilder.buildArrayQuery(q, "t.type", StringUtils.isEmpty(params.getType()) ? null : params.getType().split(","));


        //淘宝/天猫收货码查询
        tradeSqlQueryBuilder.buildReceiveCodeQuery(q,staff,params);
        //自定义的where 直接拼接到最后
        if (StringUtils.isNotEmpty(params.getCustomWhere())) {
            q.getQ().append(" and ( ").append(params.getCustomWhere()).append(" )");
        }
        buildPaidTradeNotDisplayedSql(staff, q, params);


        if (params.getPackageStatus() != null && params.getPackageStatus().length > 0) {
            tradeSqlQueryBuilder.buildArrayQuery(q, "t.sys_status", Trade.SYS_STATUS_FINISHED,Trade.SYS_STATUS_CLOSED,Trade.SYS_STATUS_SELLER_SEND_GOODS);
            if (params.getPackageStatus().length == 1) {
                if (java.util.Objects.equals(params.getPackageStatus()[0],1)) {
                    q.and().append(" t.is_package = 0 ");
                }else {
                    q.and().append(" t.is_package = 1 ");
                }
            }
        }
    }


    private void convertInsertOuterIds(Staff staff, Query q, TradeQueryParams params) {
        if (q.isStopQuery()) {
            return;
        }
        /*
         * 包含任意：订单中商品只要包含以下设置中的任意商品
         * 仅包含（所有）：订单中的商品必须有且仅有设置中的商品才可以查询出来。举例说明，若选择了A+B，订单商品必须是A+B，才可以查询到，否则查询不到
         * 仅包含（任意）：订单中商品必须仅包含设置中的商品一种或者多种，不能有设置外的商品。举例说明，若设置了A+B，如果订单包含商品A+B+C，则查询不到，如果订单商品是A或者B或者A+B则可以查询到
         * 同时包含：订单中的商品只要包含所有设置商品，就可以查询到。举例说明，若设置了A+B，订单商品如果是A+B+C，则可以查询出来，如果是A+C则查询不到
         *
         *
         * 包含任意商家编码
         * outerIdAndSysItemIds
         * onlyOuterIdAndSysSkuIdType 0
         *
         * 仅包含（所有）主商家编码
         * onlyOuterIdAndSysItemIds
         * onlyOuterIdAndSysSkuIdType 0
         *
         * 仅包含（任意）主商家编码
         * onlyOuterIdAndSysItemIds
         * onlyOuterIdAndSysSkuIdType 1
         *
         * 同时包含主商家编码
         * onlyOuterIdAndSysItemIds
         * onlyOuterIdAndSysSkuIdType 2
         * </pre>
         *
         */
        String[][] outerIds = convertInsertOuterIds(staff, q, params.getOuterIdAndSysItemIds(),params.getOuterIdAndSysSkuIds(),0);
        params.setOuterIdAndSysItemIds(outerIds[0]);
        params.setOuterIdAndSysSkuIds(outerIds[1]);

        boolean allExist = !java.util.Objects.equals(params.getOnlyOuterIdAndSysSkuIdType() , 1);
        String[][] OnlyOuterIds = convertInsertOuterIds(staff, q, params.getOnlyOuterIdAndSysItemIds(),params.getOnlyOuterIdAndSysSkuIds(),allExist?1:0);
        params.setOnlyOuterIdAndSysItemIds(OnlyOuterIds[0]);
        params.setOnlyOuterIdAndSysSkuIds(OnlyOuterIds[1]);

        String[][] excludeOuterIds = convertInsertOuterIds(staff, q, params.getExcludeOuterIdAndSysItemIds(),params.getExcludeOuterIdAndSysSkuIds(),2);
        params.setExcludeOuterIdAndSysItemIds(excludeOuterIds[0]);
        params.setExcludeOuterIdAndSysSkuIds(excludeOuterIds[1]);

    }


    /**
     * @param staff
     * @param q
     * @param input
     * @param querySku
     * @param type 0 选择值与输入值必需存在一个 1 选择值与输入值必需全部存在 2 都可以不存在
     * @return
     */
    private String[][] convertInsertOuterIds(Staff staff,Query q,String[] items,String[] skus,int type){
        if ((items == null || items.length == 0 ) && (skus == null || skus.length == 0 )) {
            return new String[][]{items,skus};
        }
        List<String> itemJsons = new ArrayList<>();
        List<String> skuJsons = new ArrayList<>();

        Set<String> needQuery = new HashSet<>();
        // 界面条件选规格相关的 但是如果选择的商品是纯商品 还是会把这个商品挂在主商品相关的字段上,因此最终item和sku的条件都会有值
        // 但是输入的条件 一定还是挂在界面上选择的条件对应字段上的
        boolean querySku = false;
        //这里实际是一个混合的条件 包含输入的编码 和界面上选择的商品 {outerId1:sysItemId},outerId2
        if (items != null) {
            for (String key : items) {
                if (key.startsWith("{")) {
                    itemJsons.add(key);
                }else {
                    needQuery.add(key);
                    querySku = false;
                }
            }
        }
        if (skus != null) {
            for (String key : skus) {
                if (key.startsWith("{")) {
                    skuJsons.add(key);
                }else {
                    needQuery.add(key);
                    querySku = true;
                }
            }
        }
        if (needQuery.isEmpty()) {
            return new String[][]{items,skus};
        }

        DevLogBuilder builder = DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("查询商品信息 ").append("按规格查", querySku)
                .append("type",type).append("outerIds", needQuery);
        QueryMiniItemByOuterIdListRequest outerIdRequest = new QueryMiniItemByOuterIdListRequest();
        outerIdRequest.setOuterIdList(Lists.newArrayList(needQuery));
        outerIdRequest.setFillDmsFxPrice(false);
        //传了最小维度 那就只会查 sku以及纯商品
        outerIdRequest.setQueryMiniItem(querySku);
        outerIdRequest.setStaffRequest(StaffRequest.builder().staffId(staff.getId()).companyId(staff.getCompanyId()).build());
        outerIdRequest.setDmjItemSearchField(DmjItemSearchField.DmjItemSearchFieldBuilder.builder().build());
        QueryMiniItemByOuterIdListResponse outerIdResponse = dmjItemCommonSearchApi.queryMiniItemByOuterIdList(outerIdRequest);
        boolean found = false;
        if (CollectionUtils.isNotEmpty(outerIdResponse.getList())) {
            for (DmjItemDto dmjItemDto : outerIdResponse.getList()) {
                boolean isSku = dmjItemDto.getSysSkuId() != null && dmjItemDto.getSysSkuId() > 0;
                //按规格条件
                if (querySku) {
                    if (isSku) {
                        JSONObject json = new JSONObject();
                        json.put(dmjItemDto.getSkuOuterId(),dmjItemDto.getSysSkuId());
                        found = true;
                        skuJsons.add(json.toJSONString());
                        builder.group("Sku",json.toJSONString());
                    }else{
                        // 因为上面outerIdRequest.setQueryMiniItem(true)这个条件 这里剩下的实际上都是纯商品
                        // 纯商品 需要把这个商品挂在主商品相关的字段上
                        JSONObject json = new JSONObject();
                        json.put(dmjItemDto.getOuterId(),dmjItemDto.getSysItemId());
                        found = true;
                        itemJsons.add(json.toJSONString());
                        builder.group("PureItem",json.toJSONString());
                    }
                }else {
                    //按主编码条件,则需要过滤掉sku
                    if (isSku) {
                        JSONObject json = new JSONObject();
                        json.put(dmjItemDto.getOuterId(),dmjItemDto.getSysSkuId());
                        builder.group("Sku",json.toJSONString());
                    }else {
                        JSONObject json = new JSONObject();
                        json.put(dmjItemDto.getOuterId(),dmjItemDto.getSysItemId());
                        found = true;
                        itemJsons.add(json.toJSONString());
                        builder.group("Item",json.toJSONString());
                    }
                }
            }
        }
        if (!found && type == 1) {
            q.setStopQuery(true);
            q.setStopReason("未找到商品:"+needQuery);
        }
        if (!found && type == 0 && itemJsons.size() == 0 && skuJsons.size() == 0) {
            q.setStopQuery(true);
            q.setStopReason("未找到商品:"+needQuery);
        }
        String[][] result = {itemJsons.toArray(new String[0]), skuJsons.toArray(new String[0])};
        builder.append("found",found).printDebug(logger);
        return result;

    }

    /**
     * 仅支持搜索待发货订单
     * 构建区间筛选：合单数量
     *
     * @param staff
     * @param q
     * @param params
     */
    private void buildMergeNumQuery(Staff staff, Query q, TradeQueryParams params) {

        if(params.getMergeNumStart() == null && params.getMergeNumEnd() ==null){
            return;
        }
        q.and().append(" t.merge_sid IN (SELECT t2.merge_sid FROM trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" t2 WHERE t2.company_id=? AND t2.merge_sid>0 AND enable_status>0 GROUP BY t2.company_id,t2.merge_sid  ").add(staff.getCompanyId())
                .append(" HAVING ");
        Integer mergeNumStart = params.getMergeNumStart();
        Integer mergeNumEnd = params.getMergeNumEnd();
        if(mergeNumStart != null && mergeNumEnd != null && mergeNumEnd < mergeNumStart){
            q.setStopQuery(true);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("[订单查询],mergeNumStart=%s,大于 mergeNumEnd=%s,返回空", mergeNumStart, mergeNumEnd)));
            }
        }
        if(mergeNumStart!=null){
            q.append(" COUNT(*)>=").append(mergeNumStart);
        }
        if(mergeNumEnd!=null){
            q.append(" AND ",mergeNumStart!=null);
            q.append(" COUNT(*)<=").append(mergeNumEnd);
        }
        q.append(")");
    }

    @Resource
    private LabelAndExceptionConditonConverter labelAndExceptionConditonConverter;
    /**
     * 订单标签拆分-查询-包含
     */
    private void buildTradeLabelContainQuery(Staff staff, Query q, TradeQueryParams params) {
        labelAndExceptionConditonConverter.buildTradeLabelContainQuery(staff,q,params.getTagIds());
    }

    /**
     * 订单标签拆分-查询-不包含
     */
    private void buildTradeLabelNotContainQuery(Staff staff, Query q, TradeQueryParams params) {
        labelAndExceptionConditonConverter.buildTradeLabelNotContainQuery(staff,q,params.getExcludeTagIds());
    }

    /**
     * 订单标签拆分-查询-同时包含
     */
    private void buildTradeLabelAllContainQuery(Staff staff, Query q, TradeQueryParams params) {
        //不要被字段命名onlyTagIds骗了 这个是同时包含 不是仅包含
        labelAndExceptionConditonConverter.buildTradeLabelAllContainQuery(staff,q,params.getOnlyTagIds());
    }

    private void buildKjQuery(Staff staff, Query q, TradeQueryParams params,TradeConfig tc) {
        tradeKjQueryBuilder.buildPrintBoxMarkQuery(q,staff,params);
        tradeKjQueryBuilder.buildShippingOrderCondition(q, staff, params);
        tradeKjQueryBuilder.buildItemPrintStatusQuery(q,staff,params);
        tradeKjQueryBuilder.buildAgedProductCodeQuery(q,staff,params);

    }


    /**
     *
     * o2o 没有合单暂时不考虑合单处理
     *
     * @param staff
     * @param query
     * @param params
     */
    private void buildTradeO2oSysStatusSql(Staff staff,Query query, TradeQueryParams params){
        String[] o2oSysStatus = params.getO2oSysStatus();
        if(org.apache.commons.lang3.ArrayUtils.isEmpty(o2oSysStatus)){
            return;
        }
        query.and().append(" t.sid IN (SELECT t1.sid FROM trade_o2o_").append(staff.getDbInfo().getTradeDbNo())
                .append(" t1 WHERE t.sid=t1.sid AND t1.enable_status=1 AND t1.company_id =").append(staff.getCompanyId())
                .append(" AND t1.sys_status IN ").append(Stream.of(o2oSysStatus).map(o->"?").collect(Collectors.joining(",","(",")")))
                .add(o2oSysStatus).append(")");
    }

    /**
     * enable_status=1 的是非合单和合单的主单
     *
     * @param staff
     * @param q
     * @param params
     */
    private void buildPaidTradeNotDisplayedSql(Staff staff, Query q, TradeQueryParams params) {
        Optional.ofNullable(params)
                .map(TradeQueryParams::getMinutesAfterPaidOrderAreNotDisplayed)
                .filter(minutes -> minutes > 0)
                .ifPresent(minutes -> {
                    Date endDate = DateUtil.addDateByMinute(new Date(), -minutes);
                    q.and().append("(").append("t.pay_time <= ? ").add(endDate);
                    //查询合单
                    q.or().append("exists (select 1 ")
                            .append(" from ")
                            .append(getTableName(staff, q))
                            .append(" t1 where t1.company_id = ")
                            .append(staff.getCompanyId())
                            .append(" and t1.merge_sid = t.merge_sid")
                            .append(" and t1.merge_sid > 0 ")
                            .append(" and t1.enable_status=2 ")
                            .append(" and t1.pay_time <= ?")
                            .add(endDate)
                            .append(") ")
                            .append(")");
                });
    }

    private String getTableName(Staff staff, Query q) {
        return q.getTradeTable() + "_" + staff.getDbInfo().getTradeDbNo() + " ";
    }

    private void buildSysConsigned(Query q, TradeQueryParams params) {

        Integer isSysConsigned = params.getIsSysConsigned();
        if (isSysConsigned != null) {
            if (isSysConsigned == 1) {
                q.and().append("t.sys_consigned = 1");
            }
            if (isSysConsigned == 0 || isSysConsigned == 2) {
                q.and().append("t.sys_consigned = 2");
            }
        }
    }

    private void buildInsufficientRateQuery(Query q, List<Integer> v) {
        if (v != null && !v.isEmpty() && v.size() < 3) {
            if (v.contains(0)) {
                if (v.contains(1)) {
                    q.and().append("(t.insufficient_rate = 0 OR t.insufficient_rate >= 100)");
                } else if (v.contains(2)) {
                    q.and().append("t.insufficient_rate >= 0 AND t.insufficient_rate < 100");
                } else {
                    q.and().append("t.insufficient_rate = 0");
                }
            } else if (v.contains(1)) {
                if (v.contains(2)) {
                    q.and().append("t.insufficient_rate > 0 AND t.insufficient_rate <= 100");
                } else {
                    q.and().append("t.insufficient_rate >= 100");
                }
            } else if (v.contains(2)) {
                q.and().append("t.insufficient_rate > 0 AND t.insufficient_rate < 100");
            }
        }
    }

    private void buildTradeFromQuery(Query q, TradeQueryParams params) {
        String[] tradeFroms = params.getTradeFrom();
        if (params.getTradeFrom() == null || params.getTradeFrom().length == 0) {
            return;
        }
        List<String> list = Arrays.asList(tradeFroms);
        StringBuilder sql = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            String tradeFrom = list.get(i);
            if ("OTHER".equals(tradeFrom)) {
                Query.conjunct(sql, " OR ", i > 0).append("t.trade_from = '' ");
            } else {
                Query.conjunct(sql, " OR ", i > 0).append("t.trade_from LIKE CONCAT('%', ?, '%') ");
                q.add(tradeFrom);
            }
        }
        if (sql.length() > 0) {
            q.and().append("(").append(sql).append(")");
        }
    }

    private void buildInWaveQuery(Query q, Boolean inWave, TradeQueryParams params) {
        // 有波次号或者短波好忽略是否在波次参数
        if(params.getWaveId() !=null){
            return;
        }
        if (null != inWave) {
            if (inWave) {
                q.and().append("t.wave_id > 0");
            } else {
                q.and().append("t.wave_id = 0");
            }
        }
    }

    private void buildDateRangeQuery(Staff staff,Query q, TradeQueryParams params) {
        TradeQueryContext context = params.getContext();
        //timeType取值: created pay_time consign_time
        String timeType = params.getTimeType();
        if (timeType == null || (timeType = timeType.trim()).isEmpty()) {
            timeType = "pay_time";
        }
        // 不是唯一性查询且有时间参数[快递打印时间，付款时间，下单时间] 且开启了强制索引白名单 且不走pg，count走强制索引
        // pg 不支持强制索引
        if(!params.getContext().isUniqueQuery()){
            if((StringUtils.equals(timeType,"express_print_time") || StringUtils.equals(timeType,"pay_time") || StringUtils.equals(timeType,"created")) &&
                    (params.getStartTime() != null || params.getEndTime() != null) &&
                    tradeLocalConfig.isTradeSearchCountForceIndex(staff.getCompanyId())){
                if(StringUtils.equals(timeType,"express_print_time")){
                    q.setTradeCountIndex(" force index (idx_company_express_user) ");
                }else if(StringUtils.equals(timeType,"pay_time") && org.apache.commons.lang3.ArrayUtils.isEmpty(params.getSysStatus())){
                    q.setTradeCountIndex(" force index (idx_company_paytime_user) ");
                }else if(StringUtils.equals(timeType,"created")){
                    q.setTradeCountIndex(" force index (idx_company_created_user) ");
                }
            }
        }
        tradeSqlQueryBuilder.buildDateRangeQuery(q, "t.`" + timeType + "`", params.getStartTime(), params.getEndTime());
        if (!TradeSearchSupport.HAS_INDEX_TIME_FIELDS.contains(timeType)) {
            context.addSuggests("优先使用订单付款时间,订单发货时间,订单创建时间,系统打印时间等时间范围条件");
        }else if(params.getSort() != null && params.getSort().getField() != null && !Objects.equal(params.getSort().getField(),timeType)){
            context.addSuggests("使用与时间范围条件相同的字段进行排序 当前排序字段:" + getFieldChName(params.getSort().getField()));
        }

        if (params.getInserted() != null) {
            q.and().append("t.inserted < ?").add(params.getInserted());
        }
        tradeSearchSupport.addUpdTimeRef(staff,q,timeType,params.getStartTime(),params.getEndTime(),params.getSysStatus());
        if ("audit_time".equals(timeType) && (params.getStartTime() != null || params.getEndTime() != null)) {
            //查询审核时间的时候排除掉已审核之前的状态
            q.append(" AND t.sys_status NOT IN( ?, ?)").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT);
        }
        //承诺时间
        if (params.getTimeoutActionTime() != null || (params.getTimeoutActionTimeType() != null && params.getTimeoutActionTimeType() != 0)) {
            if (TradeQueryParams.TIMEOUTACTIONTIMEBEFORE_NULL.equals(params.getTimeoutActionTime())) {
                q.and().append("t.timeout_action_time").append(" = ?").add(TradeQueryParams.TIMEOUTACTIONTIMEBEFORE_NULL);
            } else if (java.util.Objects.equals(params.getTimeoutActionTimeType(), 2)) {
                q.and().append("t.timeout_action_time").append(" < ?").add(new Date());
                q.and().append("t.timeout_action_time").append(" > ?").add(TradeTimeUtils.INIT_DATE);
            } else  {
                //剩余时间区间下限查询
                if (params.getTimeoutActionTimeAfter() != null) {
                    q.and().append("t.timeout_action_time").append(" >= ?").add(DateUtils.addHours(new Date(), params.getTimeoutActionTimeAfter()));
                } else {
                    q.and().append("t.timeout_action_time").append(" > ?").add(java.util.Objects.equals(params.getTimeoutActionTimeType(), 1) ? new Date() : TradeTimeUtils.INIT_DATE);
                }
                if (params.getTimeoutActionTime() != null && params.getTimeoutActionTime().after(TradeQueryParams.TIMESTAMP_MIN_DATE)) {
                    q.and().append("t.timeout_action_time").append(" <= ?").add(params.getTimeoutActionTime());
                }
            }
            //1订单状态发货之前状态（待审核，待财审，发货中（待打印快递单，待包装，待称重，待发货），待供销商发货）的订单
            //2.订单状态为【卖家已发货】但是is_upload不等于1
            //按照剩余发货时间搜索，以上两部分订单才参与搜索
            q.and().append("( (t.sys_status NOT IN (?,?,?,?)) OR (t.sys_status = ? and t.is_upload=0 ))")
                    .add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_FINISHED)
                    .add(Trade.SYS_STATUS_CLOSED).add(Trade.SYS_STATUS_SELLER_SEND_GOODS).add(Trade.SYS_STATUS_SELLER_SEND_GOODS);

        }

    }

    private void buildCustomerQuery(Query q, TradeQueryParams params) {
        //如果存在customerNick查询条件, 则追加到buyerNick查询条件中,并且在tradeType中追加一个21, 即客户订单
        if (null != params.getCustomerNick() && params.getCustomerNick().length > 0) {
            List<String> buyerNickList = params.getBuyerNick() != null && params.getBuyerNick().length > 0 ? new ArrayList<String>(Arrays.asList(params.getBuyerNick())) : new ArrayList<>();
            buyerNickList.addAll(Arrays.asList(params.getCustomerNick()));
            params.setBuyerNick(getAsStringArray(StringUtils.join(buyerNickList, ","), ",", true));
            List<Integer> tradeTypeList = StringUtils.isNotEmpty(params.getTradeType()) ? Strings.getAsIntList(params.getTradeType(), ",", true) : new ArrayList<>();
            tradeTypeList.add(21);
            params.setTradeType(StringUtils.join(tradeTypeList, ","));
        }
    }


    private void buildReceiverQuery(Staff staff, Query q, TradeQueryParams params) {
        if (q.isStopQuery()) {
            return;
        }
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.mobile_tail", params.getMobileTail());
        tradeSqlQueryBuilder.buildLikeQuery(q, 0,"t.receiver_state", params.getReceiverState());
        tradeSqlQueryBuilder.buildLikeQuery(q, 0,"t.receiver_city", params.getReceiverCity());
        tradeSqlQueryBuilder.buildLikeQuery(q, 0,"t.receiver_district", params.getReceiverDistrict());
        //收件人姓名
        tradeSqlQueryBuilder.buildReceiverNameQuery(staff, q, params);
        //收件人手机
        tradeSqlQueryBuilder.buildReceiverMobileQuery(staff, q, params);
        //收件人固话
        String receiverPhone = StringUtils.trimToEmpty(params.getReceiverPhone());
        if (StringUtils.isNotEmpty(receiverPhone)) {
            try {
                tradeSqlQueryBuilder.buildArrayQuery(q, "t.receiver_phone", receiverPhone, secretBusiness.encodeNum(staff, receiverPhone));
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人固话出错: " + e.getMessage()), e);
            }
        }
        tradeSqlQueryBuilder.buildAreaQuery(q, params.getReceiverArea());

        tradeSqlQueryBuilder.buildReceiverAddressQuery(staff, q, params);

    }

    private void buildReceiverSql(Staff staff, Query q, TradeQueryParams params) {

        List<String> provincesNames = new ArrayList<>(), cityNames = new ArrayList<>();
        if (StringUtils.isNotBlank(params.getReceiverState())) {
            provincesNames.add(params.getReceiverState());
        }
        if (StringUtils.isNotBlank(params.getReceiverCity())) {
            cityNames.add(params.getReceiverCity());
            provincesNames.addAll(SearchAddressUtils.getProvinceByCity(params.getReceiverCity()));
        }
        if (StringUtils.isNotBlank(params.getReceiverDistrict())) {
            provincesNames.addAll(SearchAddressUtils.getProvinceByDistrict(params.getReceiverDistrict()));
            cityNames.addAll(SearchAddressUtils.getCityByDistrict(params.getReceiverDistrict()));
        }

        if (hasParsedAndNoParsedProvinces(params, provincesNames)) {
            q.setStopQuery(true);
            q.setStopReason("输入区县未解析出上级区县");
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("[查询订单时,输入区县未解析出上级区县,终止查询 %s %s %s", params.getReceiverState(), params.getReceiverCity(), params.getReceiverDistrict())));
            }
            return;
        }

        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.mobile_tail", params.getMobileTail());
        if (CollectionUtils.isNotEmpty(provincesNames)) {
            tradeSqlQueryBuilder.buildListQuery(q, "t.receiver_state", provincesNames);
        }
        if (CollectionUtils.isNotEmpty(cityNames)) {
            tradeSqlQueryBuilder.buildListQuery(q, "t.receiver_city", cityNames);
        }
        if (StringUtils.isNotBlank(params.getReceiverDistrict())) {
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.receiver_district", params.getReceiverDistrict());
        }

        //收件人固话
        String receiverPhone = StringUtils.trimToEmpty(params.getReceiverPhone());
        if (StringUtils.isNotEmpty(receiverPhone)) {
            try {
                tradeSqlQueryBuilder.buildArrayQuery(q, "t.receiver_phone", receiverPhone, secretBusiness.encodeNum(staff, receiverPhone));
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人固话出错: " + e.getMessage()), e);
            }
        }
        tradeSqlQueryBuilder.buildAreaQuery(q, params.getReceiverArea());

        tradeSqlQueryBuilder.buildReceiverAddressQuery(staff, q, params);

        //收件人姓名
        tradeSqlQueryBuilder.buildReceiverNameQueryWithTradeAddress(staff, q, params);
        //收件人手机
        tradeSqlQueryBuilder.buildReceiverMobileQueryWithTradeAddress(staff, q, params);

    }

    private boolean hasParsedAndNoParsedProvinces(TradeQueryParams params, List<String> provincesNames) {
        return (StringUtils.isNotBlank(params.getReceiverCity()) || StringUtils.isNotBlank(params.getReceiverCity()) || StringUtils.isNotBlank(params.getReceiverDistrict()))
                && CollectionUtils.isEmpty(provincesNames);
    }

    protected void buildMemoQuery(Staff staff, Query q, TradeQueryParams params) {
        // 备注默认模糊查询，不走上面的下拉框控制
        int qt = 0;

        Query q0 = new Query();
        Query tradeLabelQ = new Query();

        StringBuilder s1 = new StringBuilder();
        StringBuilder s2 = new StringBuilder();

        String buyerMessage = params.getBuyerMessage() != null ? params.getBuyerMessage().trim() : "";
        if (params.getHasBuyerMessage() != null && params.getHasBuyerMessage() == 0) {
            Query.and(s1).append("buyer_message = ''");
            Query.or(s2).append("buyer_message <> ''");
        } else if (!buyerMessage.isEmpty()) {
            q0.and().append("buyer_message ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(buyerMessage);
        } else if (params.getHasBuyerMessage() != null && params.getHasBuyerMessage() - 1 == 0) {
            q0.and().append("buyer_message <> ''");
        }
        if (params.getHasBuyerMessage() != null && params.getHasBuyerMessage() - 2 == 0) { //有未处理留言
            q0.and().append(" (is_handler_message = 0 && buyer_message <> '' )");
        }


        String sellerMemo = params.getSellerMemo() != null ? params.getSellerMemo().trim() : "";
        if (params.getHasSellerMemo() != null && params.getHasSellerMemo() == 0) {
            Query.and(s1).append("seller_memo = ''");
            Query.or(s2).append("seller_memo <> ''");
        } else if (!sellerMemo.isEmpty()) {
            q0.and().append("seller_memo ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(sellerMemo);
        } else if (params.getHasSellerMemo() != null && params.getHasSellerMemo() - 1 == 0) {
            q0.and().append("seller_memo <> ''");
        }
        if (params.getHasSellerMemo() != null && params.getHasSellerMemo() - 2 == 0) {
            q0.and().append(" (is_handler_memo = 0 && seller_memo <> '') ");
        }

        if (params.getSellerMemoUpdate() != null) {
            if (params.getSellerMemoUpdate() == 0) {
                q0.and().append("seller_memo_update = 0");
            } else if (params.getSellerMemoUpdate() == 1) {
                q0.and().append("seller_memo_update IN(1, 2)");
            }
        }

        String sysMemo = params.getSysMemo() != null ? params.getSysMemo().trim() : "";
        if (params.getHasSysMemo() != null && params.getHasSysMemo() == 0) {
            Query.and(s1).append("(sys_memo = '' or sys_memo is null)");
            Query.or(s2).append("sys_memo <> ''");
        } else if (!sysMemo.isEmpty()) {
            q0.and().append("sys_memo ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(sysMemo);
        } else if (params.getHasSysMemo() != null && params.getHasSysMemo() - 1 == 0) {
            q0.and().append("sys_memo <> ''");
        }

        String memos = params.getMemos() != null ? params.getMemos().trim() : "";
        if (params.getHasMemo() != null && params.getHasMemo() == 0) {
            Query.and(s1).append("(sys_memo = '' or sys_memo is null)");
            Query.or(s2).append("sys_memo <> ''");
        } else if (!memos.isEmpty()) {
            tradeSqlQueryBuilder.buildLikeQuery(q0, 1, "sys_memo", memos.split(","));
        } else if (params.getHasMemo() != null && params.getHasMemo() - 1 == 0) {
            q0.and().append("sys_memo <> ''");
        }

        boolean hasSellerFlag = params.getSellerFlags() != null && params.getSellerFlags().length > 0;
        int i = 0;
        if (hasSellerFlag) {
            int j = 0;
            //是否查询无旗帜
            boolean hasNon = TradeUtils.hasNonSellerFlag(params.getSellerFlags());
            if (q0.getQ().length() > 0) {
                q0.and().append("(");
            } else {
                q0.append("(");
            }
            for (Integer sellerFlag : params.getSellerFlags()) {
                if (sellerFlag != null) {
                    if (Integer.valueOf(-1).equals(sellerFlag)) {
                        continue;
                    }
                    if (i++ == 0) {
                        //大于1是可能q0拼接了其他条件比如留言备注
                        if (q0.getQ().length() > 1) {
                            q0.append("seller_flag IN(?").add(sellerFlag);
                            j++;
                        } else {
                            q0.append("seller_flag IN(?").add(sellerFlag);
                            j++;
                        }
                    } else {
                        q0.append(", ?").add(sellerFlag);
                    }
                }
            }

            if (i > 0) {
                q0.append(")");
            }
            if (hasNon) {
                if (params.getExcludeSellerFlags().length > 0) {
                    Query.conjunct(q0.getQ(), " or ", q0.getQ().length() > 1 && j > 0).append(" (seller_flag IS NULL OR seller_flag = -1) ");
                } else {
                    Query.conjunct(q0.getQ(), " or ", q0.getQ().length() > 1 && j > 0).append(" (seller_flag IS NULL OR seller_flag = -1) ");
                }
            }
            q0.append(")");
        }

        //无标签
        if (!TradeDiamondUtils.openNewLabelRead(staff) && params.getTagIds() != null && Arrays.asList(params.getTagIds()).contains("-1") && params.getTagIds().length == 1) {
            Query.and(s1).append("(IFNULL(tag_ids,'') NOT REGEXP '[0-9]+' )");
            Query.or(s2).append("(IFNULL(tag_ids,'') REGEXP '[0-9]+')");
        }

        if (params.getExceptIds() != null && params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])) {
            Query.and(s1).append("(except_ids NOT REGEXP '[0-9]+' OR except_ids is null)");
            Query.or(s2).append("(except_ids REGEXP '[0-9]+' AND except_ids is not null) ");
        }

        //以下两个if判断是为了兼容以前的老数据
        if (params.getContainMemo() != null && params.getContainMemo() == 0) {
            Query.and(s1).append("buyer_message = '' AND seller_memo = ''");
            Query.or(s2).append("(buyer_message <> '' OR seller_memo <> '')");
        }
        if (params.getContainMemo() != null) {
            //当卖家或卖家留言包含关键字时命中该订单。
            String keyWordOrNull = StringUtils.isNotBlank(params.getMemoKeyWord()) ? params.getMemoKeyWord().trim() : null;
            if (params.getContainMemo() - 1 == 0) {//包含留言
                q0.and().append("buyer_message <> ''");
            } else if (params.getContainMemo() - 2 == 0) {//包含卖家备注
                if (keyWordOrNull != null) {
                    q0.and().append("seller_memo LIKE '%" + keyWordOrNull + "%'");
                } else {
                    q0.and().append("seller_memo <> ''");
                }
            } else if (params.getContainMemo() - 3 == 0) {//有买家留言或卖家备注
                if (keyWordOrNull != null) {
                    q0.and().append("(buyer_message LIKE '%" + keyWordOrNull + "%' OR seller_memo LIKE '%" + keyWordOrNull + "%')");
                } else {
                    q0.and().append("(buyer_message <> '' OR seller_memo <> '')");
                }
            } else if (params.getContainMemo() - 4 == 0) {// 有未处理留言或备注
                if (keyWordOrNull != null) {
                    q0.and().append("((is_handler_message = 0 && buyer_message LIKE '%" + keyWordOrNull + "%' ) OR (is_handler_memo = 0 && seller_memo LIKE '%" + keyWordOrNull + "%'))");
                } else {
                    q0.and().append("((is_handler_message = 0 && buyer_message <> '' ) OR (is_handler_memo = 0 && seller_memo <> ''))");
                }
            }
        }
        // 三合一备注
        String mixMemo = params.getMixMemo();
        if (StringUtils.isNotBlank(mixMemo)) {
            q0.and().append(" (buyer_message ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(mixMemo)
                    .append(" OR seller_memo ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(mixMemo)
                    .append(" OR sys_memo ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')").add(mixMemo).append(") ");
        }

        tradeSqlQueryBuilder.buildExcludeQuery(staff, params, s1, s2, q);

        //对于 无备注+已处理备注 条件,必需主子单都不存在待处理备注
        buildHandledMessage(staff,q,params);

        if (params.getContext().isUniqueQuery() || params.isUseNewQuery() == null || !params.isUseNewQuery()) {//使用老的逻辑
            Query q2 = tradeSqlQueryBuilder.buildArrayQuery(new Query(), "b.sys_outer_id", params.getExcludeSysOuterIds());
            Query q3 = tradeSqlQueryBuilder.buildArrayQuery(new Query(), "b.type", params.getExcludeOrderTypes());
            Long[] sysItemIdArray = null;
            Long[] sysSkuIdArray = null;
            if (params.getExcludeOuterIdAndSysItemIds() != null && params.getExcludeOuterIdAndSysItemIds().length > 0) {
                sysItemIdArray =  tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysItemIds()).toArray(new Long[0]);
            }
            if (params.getExcludeOuterIdAndSysSkuIds() != null && params.getExcludeOuterIdAndSysSkuIds().length > 0) {
                sysSkuIdArray =  tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysSkuIds()).toArray(new Long[0]);
            }
            Query q4 = tradeSqlQueryBuilder.buildArrayQuery(new Query(), "b.item_sys_id", sysItemIdArray);
            Query q5 = tradeSqlQueryBuilder.buildArrayQuery(new Query(), "b.sku_sys_id", sysSkuIdArray);

            if (q2.getQ().length() > 0 || q3.getQ().length() > 0) {
                if (q2.getQ().length() > 0) {
                    buildOrderNotExists(staff, s1, s2, q, q2, params);
                }
                if (q3.getQ().length() > 0) {
                    buildOrderNotExists(staff, s1, s2, q, q3, params);
                }
                if (q4.getQ().length() > 0 && q5.getQ().length() <= 0) {
                    buildOrderNotExists(staff, s1, s2, q, q4, params);
                }
                if (q5.getQ().length() > 0 && q4.getQ().length() <= 0) {
                    buildOrderNotExists(staff, s1, s2, q, q5, params);
                }
                if (q4.getQ().length() > 0 && q5.getQ().length() > 0) {
                    buildOrderItemIdAndSkuIdNotExists(staff, s1, s2, q, params);
                }
            } else if (s1.length() > 0) {
                buildExclude(staff, q, s1, s2, params);
            }

            if (!TradeDiamondUtils.openNewLabelRead(staff)) {
                tradeSqlQueryBuilder.buildTagIdsQuery(tradeLabelQ, params);
            }

            boolean hasItemQuery = params.getContext().hasItemQuery();
            if (hasItemQuery) {
                buidItemQuery(staff, q, q0, params);
            } else if (q0.getQ().length() > 0) {
                buildExists(staff, q, q0, params);
            }
            //合单标签处理
            if (!TradeDiamondUtils.openNewLabelRead(staff) && tradeLabelQ.getQ().length() > 0) {
                buildExists(staff, q, tradeLabelQ, params);
            }
            buildExclude(staff, q, params);
        } else {
            if (s1.length() > 0) {
                buildExclude(staff, q, s1, s2, params);
            }
            if (!TradeDiamondUtils.openNewLabelRead(staff)) {
                tradeSqlQueryBuilder.buildTagIdsQuery(tradeLabelQ, params);
            }
            //合单标签处理
            if (!TradeDiamondUtils.openNewLabelRead(staff) && tradeLabelQ.getQ().length() > 0) {
                buildExists(staff, q, tradeLabelQ, params);

            }
            if (q0.getQ().length() > 0) {//trade级别的条件
                buildExists(staff, q, q0, params);
            }
            boolean hasItemQuery = params.getContext().hasItemQuery();
            if (hasItemQuery) {//添加商品级别的处理
                Query q1 = new Query();
                q1.setOrderTable(q.getOrderTable());
                setUniqueCodeTable(staff, q1);
                tradeSqlQueryBuilder.buildItemQuery(staff, q1, params);
                if (q1.isStopQuery()) {
                    q.setStopQuery(true);
                    q.setStopReason(q1.getStopReason());
                    return;
                }
                if (q1.getQ().length() > 0) {
                    q.and().append("(");
                    q.append(q1.getQ()).add(q1.getArgs());
                    q.append(")");
                }else {
                    params.setCheckItem(false);
                }

            }
            buildExclude(staff, q, params);
        }
    }

    private void buildHandledMessage(Staff staff, Query q, TradeQueryParams params){
        //  AND (
        //        #单据本身不存在需要处理的留言（已处理或无留言）
        //        ((is_handler_message = 1 && buyer_message <> '') || buyer_message = '')
        //        # 非合单 或者 不存在需要处理留言的子订单
        //        AND (merge_sid < 0 OR  NOT exists(SELECT 1
        //                FROM trade_45 t1
        //                WHERE company_id = 100145
        //                AND t1.merge_sid = t.merge_sid
        //                and enable_status = 2
        //                AND ((is_handler_message <> 1 && buyer_message <> '')))
        //             )
        //  )

        Query handled = new Query();
        boolean hasHandled = false;
        if (params.getHasSellerMemo() != null && params.getHasSellerMemo() - 3 == 0) {
            handled.and().append(" ((is_handler_memo = 1 && seller_memo <> '') || seller_memo = '') ");
            hasHandled = true;
        }
        if (params.getHasBuyerMessage() != null && params.getHasBuyerMessage() - 3 == 0) { //无备注+已处理备注
            handled.and().append(" ((is_handler_message = 1 && buyer_message <> '') || buyer_message = '') ");
            hasHandled = true;
        }
        if(hasHandled){
            buildSubNotExists(staff,q,handled,params);
        }
    }
    private void buildSubNotExists(Staff staff, Query q, Query q0, TradeQueryParams params) {
        q.and().append("(").append(q0.getQ()).add(q0.getArgs());
        q.append(" AND (merge_sid < 0 OR NOT EXISTS (SELECT 1 FROM ")
                .append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
        q.append(" WHERE t1.company_id = ? AND t1.merge_sid = t.merge_sid  AND t1.enable_status IN (1, 2)" +
                " AND (t1.is_handler_message <> 1 && t1.buyer_message <> '')").add(staff.getCompanyId());
        buildDateRangeQuerySub(q, params);
        q.append(")))");
    }


    private void buildExclude(Staff staff, Query q, StringBuilder s1, StringBuilder s2, TradeQueryParams params) {
        q.and().append(s1).append(" AND (merge_sid = -1 OR merge_sid not in (SELECT merge_sid FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND merge_sid > 0 AND enable_status = 2 AND t.merge_sid = merge_sid AND ").append("(").append(s2).append(")))").add(staff.getCompanyId());
        if (!TradeDiamondUtils.openNewLabelRead(staff) && params.getExcludeTagIds() != null && params.getExcludeTagIds().length > 0) {
            String[] notNoneIds = TradeSqlQueryBuilder.getNotNoneIds(params.getExcludeTagIds());
            if (notNoneIds.length > 0) {
                q.add(notNoneIds);
            }
        }
    }

    private void buildExists(Staff staff, Query q, Query q0, TradeQueryParams params) {
        q.and().append("(").append(q0.getQ()).add(q0.getArgs());
        /*q.append(" OR (merge_sid > 0 AND t.`merge_sid` in(SELECT merge_sid  FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        q.append(" WHERE company_id = ? AND merge_sid > 0 and enable_status = 2 AND ").add(staff.getCompanyId());*/
        q.append(" OR (merge_sid > 0 AND exists (SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
        q.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 and enable_status = 2 AND ").add(staff.getCompanyId());
        q.append(q0.getQ()).add(q0.getArgs());
        buildDateRangeQuerySub(q, params);
        q.append(")))");
    }


    /**
     * 子查询里面加入打印时间过滤
     */
    private void buildDateRangeQuerySub(Query q, TradeQueryParams params) {
        String timeType = params.getTimeType();
        if ("express_print_time".equalsIgnoreCase(timeType)) {
            tradeSqlQueryBuilder.buildDateRangeQuery(q, timeType, params.getStartTime(), params.getEndTime());
        }
    }

    private void buildExclude(Staff staff, Query q, TradeQueryParams params) {
        if (params.getExcludeSysOuterIds() != null && params.getExcludeSysOuterIds().length > 0) {
            buildNotExists(staff, q, "sys_outer_id", params.getExcludeSysOuterIds());
        }
        if (params.getExcludeOuterIdAndSysItemIds() != null && params.getExcludeOuterIdAndSysItemIds().length > 0 &&
                (params.getExcludeOuterIdAndSysSkuIds() == null || params.getExcludeOuterIdAndSysSkuIds().length <= 0)) {
            //buildNotExists(staff, q, "item_sys_id", Arrays.stream(params.getExcludeOuterIdAndSysItemIds()).map(s -> Long.valueOf(JSONObject.parseObject(s).values().iterator().next().toString())).toArray(Long[]::new));
            buildNotExistsContainsMerge(staff, q, "item_sys_id", tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysItemIds()).toArray(new Long[0]));
        }
        if (params.getExcludeOuterIdAndSysSkuIds() != null && params.getExcludeOuterIdAndSysSkuIds().length > 0 &&
                (params.getExcludeOuterIdAndSysItemIds() == null || params.getExcludeOuterIdAndSysItemIds().length <= 0)) {
            //buildNotExists(staff, q, "sku_sys_id", Arrays.stream(params.getExcludeOuterIdAndSysSkuIds()).map(s -> Long.valueOf(JSONObject.parseObject(s).values().iterator().next().toString())).toArray(Long[]::new));
            buildNotExistsContainsMerge(staff, q, "sku_sys_id",tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysSkuIds()).toArray(new Long[0]));
        }
        if (params.getExcludeOuterIdAndSysItemIds() != null && params.getExcludeOuterIdAndSysItemIds().length > 0 && params.getExcludeOuterIdAndSysSkuIds() != null && params.getExcludeOuterIdAndSysSkuIds().length > 0) {
            //buildItemIdAndSkuIdNotExists(staff, q, params);
            buildItemIdAndSkuIdNotExistsContainsMerge(staff, q, params);
        }
        if (params.getExcludeOrderTypes() != null && params.getExcludeOrderTypes().length > 0) {
            buildNotExists(staff, q, "type", params.getExcludeOrderTypes());
        }
        if (StringUtils.isNotBlank(params.getExcludeOuterId())) {
            buildNotLike(staff, q, "sys_outer_id", params.getExcludeOuterId());
        }
    }

    private void buildItemIdAndSkuIdNotExistsContainsMerge(Staff staff, Query q, TradeQueryParams params) {
        if (tradeLocalConfig.isTradeSearchWithOrderUseExistsCompanyIds(staff.getCompanyId())) {
            q.and().append(" t.sid NOT IN (SELECT o.belong_sid FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
            q.append(" o WHERE belong_sid = t.sid  and company_id = ? AND enable_status in(1,2) ").add(staff.getCompanyId());
            q.append(" AND o").append(TradeSqlQueryBuilder.TRADE_ORDER_SYS_STATUS_SQL);
            StringBuilder q1 = buildItemIdAndSkuId(q, params);
            q.append(q1).append(")");
            return;
        }
        q.and().append("((").append("t.merge_sid=-1 ");
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" WHERE sid = t.sid  and company_id = ? AND (enable_status = 1 or  enable_status = 2) ").add(staff.getCompanyId());
        StringBuilder q1 = buildItemIdAndSkuId(q, params);
        q.append(q1).append(")) or (t.merge_sid>0 ");
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o1");
        q.append(" INNER JOIN ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1");
        q.append(" ON o1.company_id=t1.company_id and o1.sid=t1.sid");
        q.append(" WHERE t1.merge_sid=t.merge_sid AND t1.company_id = ? AND o1.company_id = ? AND o1.enable_status = 1 AND t1.merge_sid>0 AND t1.enable_status>=1").add(staff.getCompanyId()).add(staff.getCompanyId());
        StringBuilder q2 = buildItemIdAndSkuId(q, params);
        q.append(q2).append(")))");
    }

    private void buildNotExistsContainsMerge(Staff staff, Query q, String filed, Long[] args) {
        if (tradeLocalConfig.isTradeSearchWithOrderUseExistsCompanyIds(staff.getCompanyId())) {
            q.and().append(" t.sid NOT IN (SELECT o.belong_sid FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
            q.append(" o WHERE belong_sid = t.sid  and company_id = ? AND enable_status in(1,2) ").add(staff.getCompanyId());
            q.append(" AND o").append(TradeSqlQueryBuilder.TRADE_ORDER_SYS_STATUS_SQL);
            tradeSqlQueryBuilder.buildArrayQuery(q, filed, args).append(")");
            return;
        }
        q.and().append("((").append("t.merge_sid=-1 ");
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" WHERE sid = t.sid  and company_id = ? AND (enable_status = 1 or  enable_status = 2) ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildArrayQuery(q, filed, args).append(")");
        q.append(") or (t.merge_sid>0 ");
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o1");
        q.append(" INNER JOIN ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1");
        q.append(" ON o1.company_id=t1.company_id and o1.sid=t1.sid");
        q.append(" WHERE t1.merge_sid=t.merge_sid AND t1.company_id = ? AND o1.company_id = ? AND o1.enable_status = 1 AND t1.merge_sid>0 AND t1.enable_status>=1").add(staff.getCompanyId()).add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildArrayQuery(q, filed, args).append(")");
        q.append("))");
    }

    private void buildNotLike(Staff staff, Query q, String filed, String args) {
        if (tradeLocalConfig.isTradeSearchWithOrderUseExistsCompanyIds(staff.getCompanyId())) {
            q.and().append(" t.sid NOT IN (SELECT o.belong_sid FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
            q.append(" o  WHERE belong_sid = t.sid  and company_id = ? AND enable_status in(1,2) ").add(staff.getCompanyId());
            q.append(" AND o").append(TradeSqlQueryBuilder.TRADE_ORDER_SYS_STATUS_SQL);
            q.and().append(filed).append(" LIKE CONCAT(?, '%')").add(args).append(")");
            return;
        }
        q.and().append(" t.sid NOT IN(SELECT sid FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" WHERE sid = t.sid  and company_id = ? AND (enable_status = 1 or  enable_status = 2) ").add(staff.getCompanyId());
        q.and().append(filed).append(" LIKE CONCAT(?, '%')").add(args);
        q.append(" union SELECT merge_sid FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        q.append(" a INNER JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" b ON a.sid = b.sid AND b.enable_status = 1");
        q.append(" WHERE a.company_id = ? AND a.enable_status = 2 AND a.merge_sid > 0").add(staff.getCompanyId());
        q.and().append(" b.").append(filed).append(" LIKE CONCAT(?, '%')").add(args);
        q.append(")");
    }

    private <T> void buildNotExists(Staff staff, Query q, String filed, T... args) {
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" WHERE sid = t.sid  and company_id = ? AND (enable_status = 1 or  enable_status = 2) ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildArrayQuery(q, filed, args).append(")");
    }

    private void buildItemIdAndSkuIdNotExists(Staff staff, Query q, TradeQueryParams params) {
        q.and().append("NOT EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
        q.append(" WHERE sid = t.sid  and company_id = ? AND (enable_status = 1 or  enable_status = 2) ").add(staff.getCompanyId());
        StringBuilder q1 = buildItemIdAndSkuId(q, params);
        q.append(q1).append(")");
    }

    private void buildOrderItemIdAndSkuIdNotExists(Staff staff, StringBuilder s1, StringBuilder s2, Query q, TradeQueryParams params) {
        if (s1.length() > 0) {
            q.and().append(s1);
        }
        q.and().append("(merge_sid = -1 OR NOT EXISTS(SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" a INNER JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" b ON b.sid = a.sid AND b.company_id = a.company_id AND b.enable_status = 1 ");
        q.append("WHERE a.company_id = ? AND merge_sid = t.merge_sid AND a.enable_status = 2").add(staff.getCompanyId());
        q.conjunct(" AND (" + s2 + ")", s1.length() > 0);
        if (!TradeDiamondUtils.openNewLabelRead(staff) && params.getExcludeTagIds() != null && params.getExcludeTagIds().length > 0) {
            String[] notNoneIds = TradeSqlQueryBuilder.getNotNoneIds(params.getExcludeTagIds());
            if (notNoneIds.length > 0) {
                q.add(notNoneIds);
            }
        }
        StringBuilder q1 = buildItemIdAndSkuId(q, params);
        q.append(q1).append("))");
    }

    private StringBuilder buildItemIdAndSkuId(Query q, TradeQueryParams params) {
        StringBuilder q1 = new StringBuilder();
        Query.conjunct(q1, " AND (", true).append("item_sys_id IN (");
        if(org.apache.commons.lang3.ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysItemIds())) {
            List<Long> sysItemIds = tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysItemIds());
            if(CollectionUtils.isNotEmpty(sysItemIds)){
                String inStr = sysItemIds.stream().map(tid -> "?").collect(Collectors.joining(",", "", ""));
                q1.append(inStr);
                q.add(sysItemIds);
            }
        }
        q1.append(")");
        Query.conjunct(q1, " OR ", true).append("sku_sys_id IN (");
        if(org.apache.commons.lang3.ArrayUtils.isNotEmpty(params.getExcludeOuterIdAndSysSkuIds())) {
            List<Long> sysSkuIds = tradeSqlQueryBuilder.parseJsonToList(params.getExcludeOuterIdAndSysSkuIds());
            if(CollectionUtils.isNotEmpty(sysSkuIds)){
                String inStr = sysSkuIds.stream().map(tid -> "?").collect(Collectors.joining(",", "", ""));
                q1.append(inStr);
                q.add(sysSkuIds);
            }
        }
        q1.append("))");
        return q1;
    }

    private void buildOrderNotExists(Staff staff, StringBuilder s1, StringBuilder s2, Query q, Query q2, TradeQueryParams params) {
        if (s1.length() > 0) {
            q.and().append(s1);
        }
        q.and().append("(merge_sid = -1 OR merge_sid not in (SELECT merge_sid FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" a INNER JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" b ON b.sid = a.sid AND b.company_id = a.company_id AND b.enable_status = 1 ");
        q.append("WHERE a.company_id = ? AND merge_sid > 0 AND a.enable_status = 2");
        q.conjunct(" AND (" + s2 + ")", s1.length() > 0);
        if (!TradeDiamondUtils.openNewLabelRead(staff) && params.getExcludeTagIds() != null && params.getExcludeTagIds().length > 0) {
            String[] notNoneIds = TradeSqlQueryBuilder.getNotNoneIds(params.getExcludeTagIds());
            if (notNoneIds.length > 0) {
                q.add(notNoneIds);
            }
        }
        q.and().append(q2.getQ()).append("))").add(staff.getCompanyId()).add(q2.getArgs());
    }

    private void buidItemQuery(Staff staff, Query q, Query q0, TradeQueryParams params) {
        if (tradeLocalConfig.isTradeSearchWithOrderUseExistsCompanyIds(staff.getCompanyId()) && q0.getQ().length() <= 0) {
            Query q1 = new Query();
            q1.setOrderTable(q.getOrderTable());
            setUniqueCodeTable(staff, q1);
            tradeSqlQueryBuilder.buildItemQuery(staff, q1, params);
            if (q1.isStopQuery()) {
                q.setStopQuery(true);
                q.setStopReason(q1.getStopReason());
                return;
            }
            if (q1.getQ().length() == 0) {
                //部分场景下 order相关的条件会自动优化为没有条件 这种情况下就不需要check
                params.setCheckItem(false);
                return;
            }
            params.getContext().setHasUseJoinOrder(false);
            q.and().append(" t.sid IN (SELECT o.belong_sid from ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o where o.belong_sid=t.sid  and o.enable_status in (1,2) ").and();
            q.append(q1.getQ()).add(q1.getArgs()).append(")");
            return;
        }
        q.and().append("(");
        if (q0.getQ().length() > 0) {
            q.append(q0.getQ()).add(q0.getArgs()).and();
        }
        Query q1 = new Query();
        q1.setOrderTable(q.getOrderTable());
        setUniqueCodeTable(staff, q1);
        tradeSqlQueryBuilder.buildItemQuery(staff, q1, params);
        if (q1.isStopQuery()) {
            q.setStopQuery(true);
            q.setStopReason(q1.getStopReason());
            return;
        }
        if (q1.getQ().length() == 0) {
            //部分场景下 order相关的条件会自动优化为没有条件 这种情况下就不需要check
            params.setCheckItem(false);
            return;
        }
        //下面是对于合单的查询
        q.append(q1.getQ()).add(q1.getArgs());
        /*q.append(" OR (merge_sid > 0 AND t.`merge_sid` in(SELECT merge_sid  FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        q.append(" a INNER JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" b ON a.sid = b.sid AND b.enable_status = 1");
        q.append(" WHERE a.company_id = ? AND a.enable_status = 2 AND a.merge_sid > 0 AND ").add(staff.getCompanyId());*/
        q.append(" OR (merge_sid > 0 AND exists (SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        q.append(" a INNER JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" b ON a.sid = b.sid AND b.enable_status = 1");
        q.append(" WHERE a.company_id = ? AND a.merge_sid = t.merge_sid AND a.enable_status = 2 AND a.merge_sid > 0 AND ").add(staff.getCompanyId());
        if (q0.getQ().length() > 0) {
            q.append(q0.getQ()).add(q0.getArgs()).and();
        }
        //替换合单子查询中的trade和order两个表的别名
        q.append(q1.getQ().toString().replaceAll("o\\.", "b.").replaceAll("t\\.", "a.")).add(q1.getArgs());
        q.append(")))");
    }


    private Query buildPrintStatus(Query q, String key, Integer printStatus) {
        if (printStatus != null) {
            if (printStatus == 0) {
                q.and().append(key).append(" <= ?").add(TradeTimeUtils.INIT_DATE);
            } else if (printStatus == 1) {
                q.and().append(key).append(" > ?").add(TradeTimeUtils.INIT_DATE);
            }
        }
        return q;
    }

    private void buildExpressQuery(Query q, String... expressList) {
        if (expressList != null && expressList.length > 0) {
            List<Long> commonIds = new ArrayList<Long>(), wlbIds = new ArrayList<Long>(), kjIds = new ArrayList<Long>();
            Boolean queryNoExpress = false;
            Boolean queryExpress = false;
            for (String express : expressList) {
                if (express.contains("_")) {
                    String[] exs = express.split("_");
                    long expressId = Long.parseLong(exs[0].trim());
                    if (exs.length > 1) {
                        if (exs[1].trim().isEmpty()) {
                            commonIds.add(expressId);
                        } else {
                            int expressType = Integer.parseInt(exs[1].trim());
                            if (expressType == 1) {//电子面单
                                wlbIds.add(expressId);
                            } else if (expressType == 2) { //跨境模板
                                kjIds.add(expressId);
                            } else { //expressType == 0 普通面单
                                commonIds.add(expressId);
                            }
                        }
                    } else {
                        commonIds.add(expressId);
                    }
                } else if (express.startsWith("w")) {//电子面单
                    wlbIds.add(Long.parseLong(express.substring(1)));
                } else if ("-1".equals(express)) {
                    queryNoExpress = true;
                } else if ("-2".equals(express)) {//查询已分配快递的
                    queryExpress = true;
                } else {
                    if (StringUtils.isNotBlank(express)) {
                        commonIds.add(Long.parseLong(express));
                    }
                }
            }
            StringBuilder buf = new StringBuilder();
            int c = appendExpressTemplate(q, buf, commonIds, 0);
            c += appendExpressTemplate(q, buf, wlbIds, 1);
            c += appendExpressTemplate(q, buf, kjIds, 2);
            if (queryNoExpress) {
                c += appendExpressTemplate(q, buf, new ArrayList<Long>(Arrays.asList(-1L)), null);
            }
            if (queryExpress && CollectionUtils.isEmpty(commonIds) &&
                    CollectionUtils.isEmpty(wlbIds) && CollectionUtils.isEmpty(kjIds)) {
                if (buf.length() > 0) {
                    buf.append(" OR ");
                }
                buf.append(" t.template_id > 0 ");
                c++;
            }
            //已分配快递与其他模板（除开未分配快递）是且的关系
            //未快递与其他模板是或的关系
            q.and().conjunct("(", c > 1).append(buf).conjunct(")", c > 1);

        }
    }

    /**
     * 组装新版的 快递公司查询参数
     */
    private void buildLogisticsCompanyQuery(Query q, String logisticsCompanyIds) {
        if (StringUtils.isBlank(logisticsCompanyIds)) {
            return;
        }
        List<Long> logisticsCompanyIdList = Arrays.stream(logisticsCompanyIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        q.append(" AND t.logistics_company_id in (");
        for (int i = 0; i < logisticsCompanyIdList.size(); i++) {
            q.append(i == 0 ? "?" : ", ?");
            q.add(logisticsCompanyIdList.get(i));
        }
        q.append(")");
    }

    private int appendExpressTemplate(Query q, StringBuilder buf, List<Long> templateIds, Integer templateType) {
        if (templateIds.isEmpty()) {
            return 0;
        }
        if (buf.length() > 0) {
            buf.append(" OR ");
        }
        buf.append("(template_id IN(");
        for (int i = 0; i < templateIds.size(); i++) {
            buf.append(i == 0 ? "?" : ", ?");
            q.add(templateIds.get(i));
        }
        buf.append(")");
        if (templateType != null) {
            buf.append(" AND template_type = ?");
            q.add(templateType);
        }
        buf.append(")");
        return 1;
    }

    private void appendExpressFilterCondition(Query q, List<Long> commonIds, List<Long> cloudIds) {
        StringBuilder buf = new StringBuilder();
        boolean first = true;
        int count = 0;
        if (CollectionUtils.isNotEmpty(commonIds)) {
            buf.append("(t.template_id IN (");
            for (Long commonId : commonIds) {
                buf.append(first ? "?" : ", ?");
                q.add(commonId);
                first = false;
            }
            buf.append(") AND t.template_type = 0)");
            count++;
        }
        if (CollectionUtils.isNotEmpty(cloudIds)) {
            if (!first) {
                buf.append(" OR ");
            }
            buf.append("(t.template_id IN (");
            first = true;
            for (Long cloudId : cloudIds) {
                buf.append(first ? "?" : ", ?");
                q.add(cloudId);
                first = false;
            }
            buf.append(") AND t.template_type = 1)");
            count++;
        }
        if (StringUtils.isNotBlank(buf)) {
            q.and().conjunct("(", count > 1).append(buf).conjunct(")", count > 1);
        }
    }

    private Trades queryTrades(Staff staff, Query q, TradeQueryParams params) {
        Page page = params.getPage();
        if (page == null) {
            page = new Page().setPageNo(1).setPageSize(10);
        }
        Trades trades = new Trades();
        trades.setPage(new Page().setPageNo(page.getPageNo()).setPageSize(page.getPageSize()));
        JSONObject logJson = new JSONObject();

        long t0 = System.currentTimeMillis();
        long tradeTook = 0, assembleTook = 0, filterTook = 0, countTook = 0;
        try {
            List<Trade> list = null;
            int queryFlag = params.getQueryFlag() == null ? 0 : params.getQueryFlag();
            q.setAllowedPgl(params.isAllowedPgl());
            // pg没有trade_ext表
            if(params.getContext().hasTradeExtSearch()){
                q.setAllowedPgl(false);
            }
            int useHasNext = params.getUseHasNext() == null ? 0 : params.getUseHasNext();
            if (useHasNext == 1) {
                //不能删除，
                Integer startRow = page.getStartRow();
                page.setPageSize(page.getPageSize() + 1);
                page.setStartRow(startRow);
                trades.setTotal(Long.valueOf(startRow + page.getPageSize()));
            }
            checkAndFIllProfile(staff);

            if (queryFlag == 0 || queryFlag == 1 || queryFlag == 4 || useHasNext == 1) {//要查询订单数据
                // 唯一性查询条件需要根据 merge_sid去重，因为前端不展示子单，但又需要根据子单查询出主单,TradeAssembleBusiness.bindMerge()
                if (params.getContext().isUniqueQuery() && !java.util.Objects.equals(params.getHasSqlGroupBy(), Boolean.TRUE)) {
                    q.setGroupByPart(" case t.merge_sid when -1 then t.sid else t.merge_sid end ");
                    q.setContainHashKey(true);
                }
                q.setNeedSaveSql(true);
                reBaseTimer(params);
                List<TbTrade> tbTrades = queryFlag == 4 ?
                        tbTradeDao.queryTradesFromRuntime(staff, q, params.getRuntimeIdSnapshot(), page) :
                        tbTradeDao.queryTrades(staff, params.getFields(), q, page, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.isUseNewQuery() != null && params.isUseNewQuery() && !params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery());
                recordTimer(params,"tbTradeDao");
                if (!params.isPureTrade() && params.isNeedFill()) {
                    fill(staff,params, tbTrades, logJson);
                } else if (!params.isPureTrade() && TradeDiamondUtils.openNewLabelRead(staff)) {
                    tradeTagFill.fill(staff, tbTrades);
                    recordTimer(params,"tradeTagFill");
                }
                if(!params.isPureTrade() && !params.isNeedFill()&&TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)){
                    tradeExceptFill.fill(staff,tbTrades);
                    recordTimer(params,"tradeExceptFill");
                }
                if (params.isBreakQuery()) {
                    list = new ArrayList<>();
                    if (tbTrades != null && tbTrades.size() > 0) {
                        HashSet<Long> sids = new HashSet<>();
                        for (Trade trade : tbTrades) {
                            if (sids.add(trade.getSid())) {
                                list.add(trade);
                            }
                        }
                    }
                    trades.setTotal((long) list.size());
                } else {
                    tradeTook = System.currentTimeMillis() - t0;
                    if (!tbTrades.isEmpty()) {
                        if (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()) {
                            reBaseTimer(params);
                            Set<Long> sids = TradeUtils.toSidSet(tbTrades);
                            if (params.isCheckItem()) {
                                List<TbTrade> tempTrades = tbTradeDao.queryBySids(staff, sids.toArray(new Long[0]));
                                if (!params.isPureTrade() && params.isNeedFill()) {
                                    tradeFillService.fill(staff, tempTrades);
                                }
                                if (!params.isPureTrade() && !params.isNeedFill() && TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
                                    tradeExceptFill.fill(staff, tempTrades);
                                }
                                Map<Long, Trade> sid2Trade = TradeUtils.toMapBySid(tempTrades);
                                tradeTook = System.currentTimeMillis() - t0;
                                Assert.isTrue(sids.size() == sid2Trade.size(), "订单根据商品条件查询出错");
                                for (int i = 0; i < tbTrades.size(); i++) {
                                    tbTrades.set(i, (TbTrade) sid2Trade.get(tbTrades.get(i).getSid()));
                                }
                            } else {
                                tbTrades = tbTradeDao.queryBySids(staff, sids.toArray(new Long[0]));
                                if (!params.isPureTrade() && params.isNeedFill()) {
                                    fill(staff,params, tbTrades, logJson);
                                }
                                if (!params.isPureTrade() && !params.isNeedFill() && TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
                                    tradeExceptFill.fill(staff, tbTrades);
                                }
                            }
                            recordTimer(params,"hasItemQuery");
                        }
                        // 根据唯一码关联查询拆分订单
                        reBaseTimer(params);
                        tbTrades = querySplitTrades(staff, params, tbTrades);
                        recordTimer(params,"querySplitTrades");
//                        paymentValueErrorLog(staff, tbTrades);
                        if (!params.isPureTrade() && (params.getQueryOrder() == null || params.getQueryOrder())) {
                            String orderFields = getOrderFields(params);
                            list = tradeAssembleBusiness.bindOrders(staff, tbTrades, params.getContainsSuitSingle(), orderFields,true,params.isNeedFill(),params.getContext().getLogBuilder());
                            recordTimer(params,"bindOrders");
                            assembleTook = System.currentTimeMillis() - t0 - tradeTook;
                            //bindOrders里已经fill
                            //if (params.isNeedFill()) {
                            //    fill(staff, tbTrades, logJson);
                            //    recordTimer(params,"fillAfterBindOrders");
                            //}
                            //这里的tradeExceptFill 在bindOrders里其实也做了的 TradeExceptFill做了判断已经fill了的就不会重复fill 先不管他
                            if (!params.isNeedFill() && TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
                                tradeExceptFill.fill(staff, tbTrades);
                                recordTimer(params,"exceptFillAfterBindOrders");
                            }
                            if (params.getIgnoreFilter() != null && params.getIgnoreFilter() == 1) {
                                //filter不处理
                            } else {
                                list = filterService.filterTrades(staff, list);
                                recordTimer(params,"filterAfterBindOrders");
                                filterTook = System.currentTimeMillis() - t0 - tradeTook - assembleTook;
                            }
                        } else {
                            if (!params.isPureTrade() && params.isUndoMergeFill()) {
                                reBaseTimer(params);
                                list = tradeAssembleBusiness.bindMerge(staff, tbTrades);
                                if (params.isNeedFill()) {
                                    tradeFillService.fill(staff, list);
                                }
                                if (!params.isNeedFill() && TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
                                    tradeExceptFill.fill(staff, tbTrades);
                                }
                                recordTimer(params,"bindMerge");
                            } else {
                                list = TradeUtils.toTrades(tbTrades);
                            }
                            if (params.isPureTrade() || params.getIgnoreFilter() != null && params.getIgnoreFilter() == 1) {
                                //filter不处理
                            } else {
                                list = filterService.filterTrades(staff, list);
                                filterTook = System.currentTimeMillis() - t0 - tradeTook;
                            }
                        }
                    }
                    long count = list == null ? 0L : list.size();
                    long originCount = tbTrades == null ? 0L : tbTrades.size();
                    if (useHasNext == 1) {
                        boolean hasNext = count > trades.getPage().getPageSize();
                        trades.setHasNext(hasNext);
                        if (hasNext) {
                            //删除最后一个
                            list.remove(list.size() - 1);
                        }
                    } else if (queryFlag == 0 && useHasNext == 0) {//查询订单数据以及总数
                        reBaseTimer(params);
                        if (count < page.getPageSize() && !params.getContext().isUniqueQuery() && originCount > 0 && originCount < page.getPageSize()) {
                            trades.setTotal((page.getPageNo() - (long) 1) * page.getPageSize() + count);
                        } else {
                            if (params.getContext().isUniqueQuery()) {
                                q.setCountField(" count(distinct case t.merge_sid when -1 then t.sid else t.merge_sid end) ");
                            }
                            trades.setTotal(calcTradeTotal(staff,q,params));
                            countTook = System.currentTimeMillis() - t0 - tradeTook - assembleTook - filterTook;
                        }
                        recordTimer(params,"handleCount");
                    } else {//只查询订单数据不查询总数 queryFlag=1
                        trades.setTotal(count);
                    }
                }

                //获取consignRecord里的报错类型
                if (!params.isPureTrade() && params.getContext().hasConsignRecordQuery()) {
                    reBaseTimer(params);
                    consignRecordService.bindConsignRecord(staff, list);
                    recordTimer(params,"bindConsignRecord");
                }

                // tmallAsdpAds 这个在生成MessageMemo时就已经填充 不需要在这里继续填充 见 TradeAssembleBusiness.getMainTrade MessageMemo.valueOf
                //if (CollectionUtils.isNotEmpty(list) && params.isNeedFill()) {
                //    reBaseTimer(params);
                //    tradeExtQueryBusiness.queryHugeTradeExt(staff, list);
                //    setingTmallLogisticsUpgradeMessageMemo(staff, list);
                //    recordTimer(params,"setingTmallLogisticsUpgradeMessageMemo");
                //}
            } else if (queryFlag == 2) {//只查询订单总数
                // total只计算合单后的数量
                if (params.getContext().isUniqueQuery()) {
                    q.setCountField(" count(distinct case t.merge_sid when -1 then t.sid else t.merge_sid end) ");
                }
                q.setNeedSaveSql(true);
                trades.setTotal(calcTradeTotal(staff,q,params));
            } else if (queryFlag == 3) {//将查询到到sid插入运行时。
                trades.setTotal(tbTradeDao.queryTradeSidInsertRuntime(staff, q, params.getContext().hasItemQuery(), params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getRuntimeIdSnapshot()));
            } else if (queryFlag == 5) {
                //订单导出只生成sql并写入任务
                trades.setTotal(tbTradeDao.queryTradeSqlInsertTask(staff, q, params));
            }
            if (null != params.getQueryId() && SystemTradeQueryParamsContext.QUERY_PACK_SPLITABLE == params.getQueryId()) {
                //设置拆分装箱信息
                reBaseTimer(params);
                packSplitBusiness.fillTrades(staff, list);
                recordTimer(params,"packSplitFill");
            }
            TradeSysConsignUtils.handleSysConsigned(list);
            if (!params.isPureTrade()) {
                reBaseTimer(params);
                fillWaveShortId(staff, list);
                recordTimer(params,"fillWaveShortId");
            }
            trades.setList(list);
            return trades;
        }catch (SearchIllegalArgumentException e){
            new QueryLogBuilder(staff).appendError("订单查询参数错误",e).printWarn(logger,e);
            throw e;
        }catch (QueryTimeoutException e ){
            new QueryLogBuilder(staff).appendError("订单查询超时",e).printWarn(logger,e);
            throw e;
        }catch (org.springframework.jdbc.BadSqlGrammarException e ){
            new QueryLogBuilder(staff).appendError("查询语句组织错误",e).printWarn(logger,e);
            throw e;
        } catch (Exception e) {
            new QueryLogBuilder(staff).appendError("订单查询出错",e).printError(logger,e);
            throw new TradeException("糟糕，订单查询出错了。。。");
        } finally {
            if (logger.isDebugEnabled()) {
                Long numFound = null == trades.getHasNext() ? trades.getTotal() : CollectionUtils.isEmpty(trades.getList()) ? Long.valueOf(0) : Long.valueOf(trades.getList().size());
                String sql = q.isNeedSaveSql() && StringUtils.isNotBlank(q.getWholeSql()) ? q.getWholeSql() : q.toString();
                logger.debug(LogHelper.buildLog(staff, String.format("[订单查询 queryId=%s, table=%s ,join tables=%s,db=%s] sort: %s; numFound: %s; took: %s ms(tradeTook:%sms, assembleTook:%sms, filterTook:%s, countTook:%sms)，sql: %s;",
                        params.getQueryId(), q.getTradeTable(), getJoinTables4Log(params, q),staff.getDbNo(), q.getSort(), numFound, (System.currentTimeMillis() - t0), tradeTook, assembleTook, filterTook, countTook, sql)));
                logTradeSearch(staff, params, q, numFound, tradeTook, assembleTook, filterTook, countTook, logJson);
            }
            params.reset();
        }
    }

    /**
     *
     * 时间跨度比较大，且时间在当前时间两天前的且只查询trade表的信息。 满足条件的订单百万级别的走冷数据库统计。
     *
     * 1.不在白名单里面的不走
     * 2.除了trade表有其他表或唯一性查询不走
     * 3.结束时间和开始时间有为空的不走
     * 4.开始时间在三个月前不走
     * 5.结束时间在两天内的不走
     * 6.查询时间类型冷数据不存在的不走
     *
     * @param staff
     * @param q
     * @param params
     * @return
     */
    private Long calcTradeTotal(Staff staff, Query q, TradeQueryParams params) {
        if(!ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getTradeTotalInvokeColdApi(),String.valueOf(staff.getCompanyId())) || params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery() || params.getContext().isUniqueQuery() || params.getContext().hasTradeExtSearch() || params.getContext().hasConsignRecordQuery() || (params.getStartTime()==null || params.getEndTime()==null || DateUtil.diffDate(new Date(),params.getStartTime())>91 || DateUtil.diffDate(new Date(),params.getEndTime())<1)){
            return tbTradeDao.queryTradesCount(staff, q, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery());
        }

        boolean ifExistsTradeColdTimeType = false;
        for (TradeColdQueryParameter.QueryTimeType value : TradeColdQueryParameter.QueryTimeType.values()) {
            if (value.getTypeName().equals(params.getTimeType())){
                ifExistsTradeColdTimeType = true;
                break;
            }
        }


        String queryString = tbTradeDao.buildTradeQuerySql(staff, null, q, null, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery()).toString();
        if(!ifExistsTradeColdTimeType || !q.getParamBeforeWhere().isEmpty() ||(q.getSortTrade() != null && "payment".equals(q.getSortTrade().getField())) || countWhereOccurrences(queryString) > 1){
            return tbTradeDao.queryTradesCount(staff, q, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery());
        }

        params.setCompanyId(staff.getCompanyId());
        params.setStaffId(staff.getId());
        TradeColdQueryParameter queryParameter = ParameterConverts.tradeColdQueryConvert(params);
        return  tradeColdDataQueryApi.tradeColdTotal(queryParameter);

    }

    /**
     *
     * 下面这些不考虑
     * 1. select * from trade_1 t,order_1 o where  t.sid=o.sid    -- 逗号形式关联的
     * 2. select * from trade_1 t where t.tid like 'where'        -- 查询条件包含where
     * 3. select * from trade_1 t                                 -- 没有where条件的sql
     *
     *
     * 原因： druid 1.0.7 解析后没有别名 MySql2PglSqlResolver 解析比较耗时
     * 所以： 用最简单粗暴的方式先实现，判断不是很准也不影响，走老的方式统计
     *
     *
     * @param input
     * @return 大于1 说明有表关联，否则就是单表简单查询可以走冷数据接口 tradeColdDataQueryApi
     */
    public static int countWhereOccurrences(String input) {
        Matcher matcher = WHERE_PATTERN.matcher(input);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }




    private <T extends Trade> void fill(Staff staff,TradeQueryParams params, List<T> trades, JSONObject logJson) {
        if (logJson == null) {
            logJson = new JSONObject();
        }
        Long start = System.currentTimeMillis();
        reBaseTimer(params);
        tradeAssembleBusiness.fullExt(staff,trades);
        recordTimer(params,"fullExt");
        tradeFillService.fill(staff, trades, TradeExtFill.class);
        recordTimer(params,"fill");
        logJson.put("fillTook", System.currentTimeMillis() - start);
        logJson.put("fillCount", CollectionUtils.size(trades));
    }


    /**
     * 打点订单查询
     *
     * @param supplier
     * @param key
     * @param logJson
     * @param <T>
     * @return
     */
    private <T> T tookLog(Supplier<T> supplier, String key, JSONObject logJson) {
        if (logJson == null) {
            logJson = new JSONObject();
        }
        long s = System.currentTimeMillis();
        T result = supplier.get();
        long took = System.currentTimeMillis() - s;
        logJson.put(key, took);
        return result;
    }

    private void logTradeSearch(Staff staff, TradeQueryParams params, Query q, Long total, long tradeTook, long assembleTook, long filterTook, long countTook, JSONObject logJson) {
        String profile = appConfiguration.getProfile();
        if (!(StringUtils.isNotBlank(profile) && profile.startsWith("gray"))) {
            return;
        }
        try {
            String sql = q.isNeedSaveSql() && StringUtils.isNotBlank(q.getWholeSql()) ? q.getWholeSql() : q.toString();
            TradeLog tradeLog = new TradeLog(staff);
            tradeLog.setLogBiz("tradeSearch");
            JSONObject log = (JSONObject) JSON.toJSON(params);
            log.put("numberFund", total);
            log.put("tradeTook", tradeTook);
            log.put("assembleTook", assembleTook);
            log.put("filterTook", filterTook);
            log.put("countTook", countTook);
            log.put("sql", sql);
            if (params.getContext().hasItemQueryOrigin()) {
                log.put("hasItem", 1);
                if (tradeLocalConfig.isTradeSearchWithOrderUseExistsCompanyIds(staff.getCompanyId())) {
                    log.put("hasItemUseExists", 1);
                }
            }

            tradeLog.appendLog(log);
            tradeLog.appendLog(logJson);
            logger.debug(tradeLog);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单查询日志创建失败" + e.getMessage()), e);
        }


    }

    private String getJoinTables4Log(TradeQueryParams params, Query q) {
        StringBuilder stringBuilder = new StringBuilder();
        if (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()) {
            stringBuilder.append(q.getOrderTable()).append(",");
        }
        if (params.getContext().hasTradeExtSearch()) {
            stringBuilder.append(q.getTradeExtTable()).append(",");
        }
        //删除最后的","
        if (stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }

    /**
     * 天猫物流升级messageMemo设置
     *
     * @param staff
     * @param list
     */
    private List<Trade> setingTmallLogisticsUpgradeMessageMemo(Staff staff, List<Trade> list) {
        List<Trade> mergeTradeList = list.stream().filter(TradeUtils::isMerge).collect(Collectors.toList());
        List<Long> mergeSidList = mergeTradeList.stream()
                .filter(trade -> CollectionUtils.isNotEmpty(trade.getMessageMemos()))
                .flatMap(trade -> trade.getMessageMemos().stream().map(MessageMemo::getSid))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(mergeSidList)) {
            List<TradeExt> tradeExtList = tradeExtDao.tradeExtsGetBySids(staff, mergeSidList);
            Map<Long, TradeExt> tradeExtMap = tradeExtList.stream().collect(Collectors.toMap(TradeExt::getSid, tradeExt -> tradeExt));

            mergeTradeList.forEach(trade -> {
                if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                    trade.getMessageMemos().forEach(messageMemo -> {
                        TradeExt tradeExt = tradeExtMap.get(messageMemo.getSid());
                        if (java.util.Objects.nonNull(tradeExt)) {
                            messageMemo.setTmallAsdpAds(tradeExt.getTmallAsdpAds());
                            messageMemo.setTmallAsdpBizType(tradeExt.getTmallAsdpBizType());
                        }
                    });
                }
            });
        }
        return list;
    }

    private void fillWaveShortId(Staff staff, List<Trade> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> waveIds = list.stream().filter(t -> t.getWaveId() != null && t.getWaveId() > 0).map(Trade::getWaveId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(waveIds)) {
                Map<Long, Long> waveMap = tradeWaveService.queryWaveIdAndShortId(staff, null, Lists.newArrayList(waveIds));
                if (MapUtils.isNotEmpty(waveMap)) {
                    list.stream().forEach(t -> t.setWaveShortId(waveMap.get(t.getWaveId())));
                }
            }
        }
    }

    /**
     * 订单金额有误相关
     *
     * @param staff
     * @param tbTrades
     */
    private void paymentValueErrorLog(Staff staff, List<TbTrade> tbTrades) {
        // 金额相关数据重新赋值，防止查询的时候报错
        tbTrades.forEach(tbTrade -> {
            boolean flag = false;
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("tid:[{").append(tbTrade.getTid()).append("}] ;");
            stringBuilder.append("sid:[{").append(tbTrade.getSid()).append("}] ;");
            if (!PaymentUtils.isNumeric(tbTrade.getPayment())) {
                stringBuilder.append("payment error :[{").append(tbTrade.getPayment()).append("}] ;");
                tbTrade.setPayment("0");
                flag = true;
            }
            if (!PaymentUtils.isNumeric(tbTrade.getDiscountFee())) {
                stringBuilder.append("discountFee error :[{").append(tbTrade.getDiscountFee()).append("}] ;");
                tbTrade.setDiscountFee("0");
                flag = true;
            }
            if (!PaymentUtils.isNumeric(tbTrade.getTotalFee())) {
                stringBuilder.append("totalFee error :[{").append(tbTrade.getTotalFee()).append("}] ;");
                tbTrade.setTotalFee("0");
                flag = true;
            }
            if (!PaymentUtils.isNumeric(tbTrade.getAcPayment())) {
                stringBuilder.append("acPayment error :[{").append(tbTrade.getAcPayment()).append("}] ;");
                tbTrade.setAcPayment("0");
                flag = true;
            }
            if (!PaymentUtils.isNumeric(tbTrade.getActualPostFee())) {
                stringBuilder.append("actualPostFee error :[{").append(tbTrade.getActualPostFee()).append("}] ;");
                tbTrade.setActualPostFee("0");
                flag = true;
            }
            if (flag) {
                logger.error(LogHelper.buildLog(staff, stringBuilder.toString()));
            }
        });
    }

    private String getOrderFields(TradeQueryParams params) {
        Integer needOrder = params.getNeedOrder();
        if (needOrder != null && needOrder == 0) {
            //当needOrder=0时,由于不查询order的详细信息。那么只查询出需要的order字段给trade业务计算+列配置使用即可.
            return "id,oid,sid,belong_sid,tid,status,num,num_iid,sku_id,outer_iid,type,item_sys_id,sku_sys_id,sid,pic_path,sys_sku_properties_alias,sys_sku_properties_name,sys_outer_id,sys_title,title,sys_pic_path,sku_properties_name,outer_sku_id,sys_sku_remark,payment,price,total_fee,combine_id," +
                    "sys_status,stock_status,stock_num,gift_num,insufficient_canceled,adjust_fee,discount_fee,net_weight,sys_item_remark,source,sys_item_changed,relation_changed,item_changed,refund_status,short_title,sys_consigned,ac_payment,cost,divide_order_fee,v";
        }

        return null;
    }

    /**
     * 根据sids查询发货记录
     *
     * @param staff
     * @param sids
     * @return
     */
    @Override
    public List<ConsignRecord> queryConsignRecordBySids(Staff staff, Long... sids) {
        return consignRecordDao.listBySids(staff, sids);
    }


    List<Trade> _queryBySids(Staff staff, Long[] sids, boolean showDetail,AbsLogBuilder logBuilder) {
        //checkAndFIllProfile(staff);
        //List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        //logBuilder.recordTimer("queryBySids");
        //if (!showDetail) {
        //    tradeExceptFill.fillMergeExcept(staff, tbTrades);
        //    return TradeUtils.toTrades(tbTrades);
        //}
        //return tradeAssembleBusiness.bindOrders(staff, tbTrades, true, null,true,true,logBuilder);
        return _queryBySids(staff,sids,showDetail,false,true,logBuilder);
    }

     List<Trade> _queryBySids(Staff staff, Long[] sids, boolean showDetail, boolean showTradeExt,AbsLogBuilder logBuilder) {
        //checkAndFIllProfile(staff);
        //List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        //logBuilder.recordTimer("queryBySids");
        //if (showTradeExt) {
        //    if (CollectionUtils.isNotEmpty(tbTrades)) {
        //        List<Long> extSids = tbTrades.stream().filter(TradeUtils::needTradeExt).map(Trade::getSid).collect(Collectors.toList());
        //        List<TradeExt> tradeExtList = tradeExtDao.tradeExtsGetBySids(staff, extSids);
        //        if (CollectionUtils.isNotEmpty(tradeExtList)) {
        //            Map<Long, TradeExt> map = tradeExtList.stream().collect(Collectors.toMap(TradeExt::getSid, tradeExt -> tradeExt));
        //            for (TbTrade tbTrade : tbTrades) {
        //                if (map.get(tbTrade.getSid()) != null) {
        //                    tbTrade.setTradeExt(map.get(tbTrade.getSid()));
        //                }
        //            }
        //        }
        //        logBuilder.recordTimer("setTradeExt");
        //    }
        //}
        //List<Trade> trades = null;
        //if (!showDetail) {
        //    trades = TradeUtils.toTrades(tbTrades);
        //} else {
        //    trades = tradeAssembleBusiness.bindOrders(staff, tbTrades, true, null,true,true,logBuilder);
        //}
        //return trades;
        return _queryBySids(staff,sids,showDetail,showTradeExt,false,logBuilder);
    }

    private void checkAndFIllProfile(Staff staff) {
        if (staff.getDbInfo() == null) {
            try {
                new QueryLogBuilder(staff).append("staff profile为空 尝试补全 ").append("companyId",staff.getCompanyId()).printWarn(logger);
                Company company =  staff.getCompany();
                if (company == null) {
                    company = companyService.queryById(staff.getCompanyId());
                    Assert.notNull(company,"非法的companyId:"+staff.getCompanyId());
                    staff.setCompany(company);
                }
                CompanyProfile profile = company.getProfile();
                if (profile == null) {
                    profile = companyService.getCompanyProfile(staff.getCompanyId());
                    company.setProfile(profile);
                }
            }catch (Throwable e){
                new QueryLogBuilder(staff).appendError("尝试补全profile失败",e).printError(logger,e);
            }

        }
    }


    private List<Trade> _queryBySids(Staff staff, Long[] sids, boolean showDetail, boolean showTradeExt,boolean fillMergeExcept,AbsLogBuilder logBuilder) {
        checkAndFIllProfile(staff);
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        logBuilder.append("关联trade",tbTrades.size());
        logBuilder.recordTimer("queryBySids");
        if (showTradeExt) {
            if (CollectionUtils.isNotEmpty(tbTrades)) {
                List<Long> extSids = tbTrades.stream().filter(TradeUtils::needTradeExt).map(Trade::getSid).collect(Collectors.toList());
                List<TradeExt> tradeExtList = tradeExtDao.tradeExtsGetBySids(staff, extSids);
                if (CollectionUtils.isNotEmpty(tradeExtList)) {
                    Map<Long, TradeExt> map = tradeExtList.stream().collect(Collectors.toMap(TradeExt::getSid, tradeExt -> tradeExt));
                    for (TbTrade tbTrade : tbTrades) {
                        if (map.get(tbTrade.getSid()) != null) {
                            tbTrade.setTradeExt(map.get(tbTrade.getSid()));
                        }
                    }
                }
                logBuilder.recordTimer("setTradeExt");
            }
        }
        List<Trade> trades = null;
        if (!showDetail) {
            if (fillMergeExcept) {
                tradeExceptFill.fillMergeExcept(staff, tbTrades);
            }
            trades = TradeUtils.toTrades(tbTrades);
        } else {
            trades = tradeAssembleBusiness.bindOrders(staff, tbTrades, true, null,true,true,logBuilder);
        }
        return trades;
    }

    @Override
    public List<Trade> searchDbTradeByDate(Staff staff, Page page, List<String> statusList, Date startDate, Date endDate) {
        List<Trade> list = tbTradeDao.searchDbTradeByDate(staff, page, statusList, startDate, endDate);
        tradeFillService.fill(staff, list);
        return list;
    }

    @Override
    public List<Trade> searchTradeByQueryParams(Staff staff, TradeQueryParams tradeQueryParams) {

        List<Trade> tradeList = tbTradeDao.searchTradeByQueryParams(staff, tradeQueryParams);
        if (CollectionUtils.isNotEmpty(tradeList) && BooleanUtils.isTrue(tradeQueryParams.getQueryOrder())) {
            tradeList = tradeAssembleBusiness.bindOrders(staff, tradeList, tradeQueryParams.getContainsSuitSingle());
        }


        tradeFillService.fill(staff, tradeList);

        try {
            filterService.filterTrades(staff, tradeList);
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        }

        return tradeList;
    }

    @Override
    public Long searchDbTradeByDateOfCount(Staff staff, List<String> statusList, Date startDate, Date endDate) {
        checkAndFIllProfile(staff);
        return tbTradeDao.searchDbTradeByDateOfCount(staff, statusList, startDate, endDate);
    }

    public TbTradeDao getTbTradeDao() {
        return tbTradeDao;
    }

    public void setTbTradeDao(TbTradeDao tbTradeDao) {
        this.tbTradeDao = tbTradeDao;
    }

    public TbOrderDAO getTbOrderDao() {
        return tbOrderDao;
    }

    public void setTbOrderDao(TbOrderDAO tbOrderDao) {
        this.tbOrderDao = tbOrderDao;
    }

    @Override
    public List<Trade> queryBySidsContainDeleteTrade(Staff staff, boolean showDetail, Long... sids) {
        checkAndFIllProfile(staff);
        List<TbTrade> tbTrades = tbTradeDao.queryByKeys(staff, "trade_" + staff.getDbInfo().getTradeDbNo(), null, true, "sid", sids);
        tradeFillService.fill(staff, tbTrades);
        if (!showDetail) {
            return TradeUtils.toTrades(tbTrades);
        }
        return tradeAssembleBusiness.bindOrders(staff, tbTrades, true);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, Long... sids) {
        return queryBySidsContainMergeTrade(staff, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, Long... sids) {
        return queryBySidsContainMergeTrade(staff, queryOrder, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, Long... sids) {
        return queryBySidsContainMergeTrade(staff, queryOrder, filter, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, Long... sids) {
        return queryBySidsContainMergeTrade(staff,queryOrder,filter,containConsign,null,sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, AbsLogBuilder logBuilder, Long... sids) {
        if (sids == null || sids.length == 0) {
            return new ArrayList<>();
        }
        AbsLogBuilder local = null;
        if (logBuilder == null) {
            logBuilder = new QueryLogBuilder(staff).append("queryBySidsContainMergeTrade 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
            local = logBuilder;
        }
        List<Trade> result = null;
        //这个接口影响范围太广了 先走白名单
        if(tradeLocalConfigurable.isConfigOn(TradeLocalConfigurableConstants.QUERY_BY_MERGE_SIDS_NEW,staff.getCompanyId())){
            TradeAssembleParams params = new TradeAssembleParams()
                    .setQueryOrder(queryOrder).setQueryOrderExt(true).setQueryExt(true).setQueryMerge(true)
                    .setFill(true).setFilter(filter).setContainConsign(containConsign)
                    .setOrderAssembleParams(OrderAssembleParams.orderWithSuitSingle());

            result =  queryBySidsContainMergeTrade(staff,params,logBuilder,sids);
            logBuilder.append("[new]");
        }else {
            result =  orgiQueryBySidsContainMergeTrade(staff,queryOrder,filter,containConsign,logBuilder,sids);
        }
        if (local != null) {
            local.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),sids == null? 0:sids.length,null)).printInfo(logger);
        }
        return result;
    }

    private List<Trade> orgiQueryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, AbsLogBuilder logBuilder, Long... sids) {
        checkAndFIllProfile(staff);
        List<Trade> result = new ArrayList<Trade>();
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids);
        List<Long> mergeSids = new ArrayList<Long>();
        List<Long> orderSids = new ArrayList<Long>();
        List<Long> extSids = new ArrayList<>();

        //过滤出合单的订单
        for (Trade trade : tbTrades) {
            if (TradeUtils.isMerge(trade)) {
                mergeSids.add(trade.getMergeSid());
            } else {
                orderSids.add(trade.getSid());
                result.add(trade);
            }
        }

        //将合单后隐藏的订单查出来
        if (mergeSids.size() > 0) {
            //2、根据mergeSids查询所有合单的订单
            List<TbTrade> mergeTrades = tbTradeDao.queryByMergeSids(staff, mergeSids.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(mergeTrades)) {

                tradeAssembleBusiness.handleMergeTrades(mergeTrades, result, orderSids, containConsign);
            }
            logBuilder.recordTimer("handleMergeTrades");
        }
        //找出需要ext数据的订单,包括合单
        for (Trade trade : result) {
            if (TradeUtils.needTradeExt(trade)) {
                extSids.add(trade.getSid());
            }
        }
        //将订单的ext数据查询出来并塞到trade中
        if (extSids.size() > 0) {
            tradeExtQueryBusiness.queryTradeExt(staff, result);
            logBuilder.recordTimer("queryTradeExt");
        }

        //是否需要查询order
        if (queryOrder) {
            List<TbOrder> tbOrders = tbOrderDao.queryBySids(staff, orderSids.toArray(new Long[0]));
            TradeUtils.assemblyBySid(result, tbOrders);
            List<Long> oids = tbOrders.stream().map(Order::getId).collect(Collectors.toList());
            List<OrderExt> orderExts = orderExtDao.queryByOrderIds(staff, oids.toArray(new Long[oids.size()]));
            TradeUtils.assemblyBySidForOrderExt(tbOrders, orderExts);
            logBuilder.recordTimer("queryOrder");
        }
        tradeFillService.fill(staff, result);
        logBuilder.recordTimer("fill");
        if (filter) {
            try {
                filterService.filterTrades(staff, result);
            } catch (TradeFilterException e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
            }
            logBuilder.recordTimer("filter");
        }
        return result;
    }


    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, TradeAssembleParams params, Long... sids) {
        QueryLogBuilder logBuilder = new QueryLogBuilder(staff).append("queryBySidsContainMergeTrade 耗时统计").setBaseTooklmt(DevLogBuilder.isDevEnv()?0L: 100L).startTimer();
        return queryBySidsContainMergeTrade(staff,params,logBuilder,sids);
    }

    private List<Trade> queryBySidsContainMergeTrade(Staff staff, TradeAssembleParams params,AbsLogBuilder logBuilder, Long... sids) {
        checkAndFIllProfile(staff);
        params.setQueryMerge(true);
        List<TbTrade>  tbTrades = null;
        if (CollectionUtils.isNotEmpty(params.getQueryFields())) {
            String fields = Strings.join(",",params.getQueryFields());
            tbTrades = tbTradeDao.queryByKeys(staff, fields, "sid", sids);
        }else {
            tbTrades = tbTradeDao.queryBySids(staff, sids);
        }
        logBuilder.recordTimer("queryBySids");
        decodeFieldFilter.filterTrades(staff,tbTrades);
        logBuilder.append("原始订单[").append(tbTrades.size()).append("];");
        List<Trade> result = tradeAssembleBusiness.assemblyTrades(staff, params, logBuilder, tbTrades);
        return result;
    }




    @Override
    public List<String> queryTids(Staff staff, Long... sids) {
        return tbTradeDao.queryTids(staff, sids);
    }

    @Override
    public List<String> queryTidsExist(Staff staff, List<String> tidList) {
        checkAndFIllProfile(staff);
        return tbTradeDao.queryTidsExist(staff, tidList);
    }

    /**
     * 根据商品条件搜索订单时,需要重新排序查询出来的订单
     *
     * @param trades    根据商品条件搜索的只包含sid的订单集合
     * @param newTrades 新查询出来的包含所有信息的订单集合
     */
    private List<TbTrade> storeOriginSort(List<TbTrade> trades, List<TbTrade> newTrades) {
        List<TbTrade> result = new ArrayList<TbTrade>(newTrades.size());
        Map<Long, Trade> map = TradeUtils.toMapBySid(newTrades);
        for (Trade trade : trades) {
            Trade newTrade = map.get(trade.getSid());
            if (newTrade != null) {
                result.add((TbTrade) newTrade);
            }
        }
        return result;
    }

    @Override
    public Map<String, Integer> queryNotFinishedRefundOrScalpingItemNumMap(Staff staff, Long warehouseId) {
        checkAndFIllProfile(staff);
        List<Order> orders = tbOrderDao.queryNotFinishedRefundOrScalpingOrder(staff, getPrivilegeWarehouseIds(staff, warehouseId));
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyMap();
        }

        Map<String, Integer> itemKeyNumMap = Maps.newHashMap();
        for (Order order : orders) {
            if (!(Objects.equal(order.getScalping(), 1) || RefundUtils.isRefundOrder(order))) {
                continue;
            }
            String key = order.getItemSysId() + "_" + (order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
            Integer num = itemKeyNumMap.get(key);
            if (num == null) {
                itemKeyNumMap.put(key, order.getNum());
            } else {
                itemKeyNumMap.put(key, num + order.getNum());
            }
        }
        return itemKeyNumMap;
    }

    @Override
    public Long hasPlatformOrderInfo(Staff staff, String tid, Long oid) {
        checkAndFIllProfile(staff);
        return tbOrderDao.hasPlatformOrderInfo(staff, tid, oid, new ArrayList<>(UploadUtils.SPLIT_SUPPORTS_PLAT));
    }

    @Override
    public List<Order> queryPlatformOrderInfo(Staff staff, String tid, Long oid) {
        checkAndFIllProfile(staff);
        return tbOrderDao.queryPlatformOrderInfo(staff, tid, oid);
    }

    private List<Long> getPrivilegeWarehouseIds(Staff staff, Long warehouseId) {
        Assert.notNull(warehouseId, "请选择仓库！");
        if (staff.isDefaultStaff()) {
            return warehouseId == 0L ? null : Lists.newArrayList(warehouseId);
        }

        List<Long> pWarehouseIds = ArrayUtils.toLongList(staff.getAllWarehouseGroup());
        Assert.notEmpty(pWarehouseIds, "当前用户无此仓库权限！");
        if (warehouseId == 0L) {
            return pWarehouseIds;
        }
        Assert.isTrue(pWarehouseIds.contains(warehouseId), "当前用户无此仓库权限！");
        return Lists.newArrayList(warehouseId);
    }

    private SysItemSku buildItemSku(Long sysItemId, Long sysSkuId, String outerId, String barcode) {
        SysItemSku itemSku = new SysItemSku();
        itemSku.setSysItemId(sysItemId);
        itemSku.setSysSkuId(sysSkuId);
        itemSku.setOuterId(outerId);
        itemSku.setBarcode(barcode);
        return itemSku;
    }

    @Override
    public List<Trade> queryByWaveId(Staff staff, Page page, boolean showDetail, String tradeFields, Long warehouseId, Long waveId) {
        checkAndFIllProfile(staff);
        List<TbTrade> tbTrades = tbTradeDao.queryByWaveIds(staff, page, false, tradeFields, warehouseId, waveId);

        tradeFillService.fill(staff, tbTrades);
        if (!showDetail) {
            return TradeUtils.toTrades(tbTrades);
        }
        return tradeAssembleBusiness.bindOrders(staff, tbTrades, true);
    }

    @Override
    public List<Trade> queryByMobileTail(Staff staff, String tradeFields, String ... mobileTail) {
        checkAndFIllProfile(staff);
        return TradeUtils.toTrades(tbTradeDao.queryByKeys(staff, tradeFields, "mobile_tail", mobileTail));

    }

    @Override
    public List<TradeExt> getTradeExtBySid(Staff staff, List<Long> sids) {
        checkAndFIllProfile(staff);
        return tradeExtDao.tradeExtsGetBySids(staff, sids);
    }

    /**
     * 是否查询供应商失败
     *
     * @param params
     * @return
     */
    private boolean searchSupplierFail(Staff staff, TradeQueryParams params) {
        if (!CollectionUtils.isEmpty(params.getSupplierIds())) {
            if (params.getSysItemIds() == null && params.getSysSkuIds() == null) {
                return true;
            }
            //如果size超过2000，失败处理
            if (params.getSysItemIds() != null && params.getSysItemIds().length > 2000) {
                logger.debug(LogHelper.buildLogHead(staff).append("根据供应商查询商品超过2000! ").append("供应商id：").append(params.getSupplierIds()).append("当前数量：").append(params.getSysItemIds().length));
                return true;
            }
            if (params.getSysSkuIds() != null && params.getSysSkuIds().length > 2000) {
                logger.debug(LogHelper.buildLogHead(staff).append("根据供应商查询商品规格超过2000! ").append("供应商id：").append(params.getSupplierIds()).append("当前数量：").append(params.getSysItemIds().length));
                return true;
            }
        }
        return false;
    }

    @Override
    public List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, InvaildItemQueryParams params) {
        return queryItemExceptionItem(staff,page,null,params,null);
    }

    @Override
    public List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, Sort sort, InvaildItemQueryParams params,List<Long> sids) {
        checkAndFIllProfile(staff);
        Long[] userIds = null;
        String key = null;
        String value = null;
        boolean isAccurate = false;
        if (params != null) {
            userIds = ArrayUtils.toLongArray(params.getInvaildUserIds());
            key = getExceptionItemQueryKey(params);
            value = params.getInvaildQueryText();
            isAccurate = params.getIsAccurate() == 1;
        }
        List<InvaildItem> invaildItems = tbTradeDao.queryExceptionItem(staff, page,sort, key, value, userIds, isAccurate,sids);

        return invaildItems;
    }

    @Override
    public Long queryItemExceptionItemCount(Staff staff, InvaildItemQueryParams params,List<Long> sids) {
        checkAndFIllProfile(staff);
        Long[] userIds = null;
        String key = null;
        String value = null;
        boolean isAccurate = false;
        if (params != null) {
            userIds = ArrayUtils.toLongArray(params.getInvaildUserIds());
            key = getExceptionItemQueryKey(params);
            value = params.getInvaildQueryText();
            isAccurate = params.getIsAccurate() == 1;
        }
        Long count = tbTradeDao.queryExceptionItemCount(staff, key, value, userIds, isAccurate,sids);
        return count;
    }

    @VisibleForTesting
    protected String getExceptionItemQueryKey(InvaildItemQueryParams params) {
        assert params != null;
        String key = params.getInvaildQueryType();
        boolean hit = false;
        if ("title".equals(key)) {
            hit = true;
            if (params.getPlatform() - 1 == 0) {
                key = "title";
            } else {
                key = "sys_title";
            }
        }
        if ("propertiesName".equals(key)) {
            hit = true;
            if (params.getPlatform() - 1 == 0) {
                key = "sku_properties_name";
            } else {
                key = "sys_sku_properties_name";
            }
        }
        if ("outerId".equals(key)) {
            hit = true;
            if (params.getPlatform() - 1 == 0) {
                key = "outer_iid";
            } else {
                key = "sys_item_id";
            }
        }
        if ("skuItemId".equals(key)) {
            hit = true;
            if (params.getPlatform() - 1 == 0) {
                key = "sku_id";
            } else {
                key = "sku_sys_id";
            }
        }
        if (!hit) {
            throw new IllegalArgumentException("queryType 参数取值无法识别");
        }
        return key;
    }

    @Override
    public List<InvaildItemTrade> queryItemExceptionTrade(Staff staff, Page page, InvaildItemTradeQueryParams params) {
        checkAndFIllProfile(staff);
        String source = null;
        String numIid = null;
        String skuId = null;
        if (params != null) {
            source = params.getSource();
            numIid = params.getNumIid();
            skuId = params.getSkuId();
        }
        List<InvaildItemTrade> invaildItemTrades = tbTradeDao.queryInvaildItemTrade(staff, page, source, numIid, skuId);
        return invaildItemTrades;
    }

    @Override
    public Long queryItemExceptionTradeCount(Staff staff, InvaildItemTradeQueryParams params) {
        checkAndFIllProfile(staff);
        String source = null;
        String numIid = null;
        String skuId = null;
        if (params != null) {
            source = params.getSource();
            numIid = params.getNumIid();
            skuId = params.getSkuId();
        }
        Long count = tbTradeDao.queryInvaildItemTradeCount(staff, source, numIid, skuId);
        return count;
    }

    @Override
    public List<Trade> queryNotSysTradeByTids(Staff staff, List<String> tids) {
        checkAndFIllProfile(staff);
        return tbTradeDao.queryNotSysTradeByTids(staff, tids);
    }

    @Override
    public List<Trade> querySomeFieldsByWaveIds(Staff staff, String fields, List<Long> waveIds) {
        checkAndFIllProfile(staff);
        return tbTradeDao.querySomeFieldsByWaveIds(staff, fields, waveIds);
    }

    @Override
    public Long statisticsWithNotConsign(Staff staff, Date startTime, Date endTime) {
        long start = System.currentTimeMillis();
        try {
            // 组装查询语句
            TradeQueryParams params = new TradeQueryParams();
            params.setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
            params.setQueryId(62L).setCheckActive(true).setIsOutstock(0).setIsPresell(0);
            params.setLogisticsWarningStartTime(startTime);
            params.setLogisticsWarningEndTime(endTime);

            TradeConfig tc = tradeConfigService.get(staff);
            Query q = new Query();
            q.setTradeTable("trade_not_consign");
            tradeSqlQueryBuilder.buildQuery(q, staff, params);
            tradeSqlQueryBuilder.buildSysStatusQuery(q, params, tc);
            tradeSqlQueryBuilder.buildLogisticsWarningQuery(staff, q, params);
            tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_cancel", 0);

            checkAndFIllProfile(staff);
            Long count = tbTradeDao.queryTradesCount(staff, q, false);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s from db took: %sms, NumFound: %s, Sql: %s", "未发货订单物流预警统计", (System.currentTimeMillis() - start), count, q.toString())));
            }
            return count == null ? 0 : count;
        } catch (Exception e) {
            throw new TradeException("物流预警统计出错", e);
        }
    }

    @Override
    public List<Trade> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids, List<Long> mergeSids) {
        checkAndFIllProfile(staff);
        return tbTradeDao.querySomeFieldsBySids(staff, fields, sids, mergeSids);
    }

    @Override
    public List<Trade> querySomeFieldsByCondition(Staff staff, String fields, Map<String, Object> condition) {
        checkAndFIllProfile(staff);
        return tbTradeDao.querySomeFieldsByCondition(staff, fields, condition);
    }

    @Override
    public List<Order> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids) {
        checkAndFIllProfile(staff);
        return orderDAO.querySomeFieldsBySids(staff, fields, new ArrayList<>(sids));
    }

    @Override
    public List<TbTrade> statisticsWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params) {
        long start = System.currentTimeMillis();
        try {
            Query q = buildQuerySql(staff, params);
            checkAndFIllProfile(staff);
            List<TbTrade> trades = tbTradeDao.queryTrades(staff, "sid,tid,out_sid,pay_time,source,unified_status,sys_status,warehouse_name,warehouse_id,taobao_id,user_id,convert_type,belong_type,template_id,dest_id,timeout_action_time", q, params.getPage(), false, false);
            // 获取店铺名称map
            Map<Long, String> shopNameMap = buildShopMap(trades);
            // 获取快递公司,key是templateId
            Map<Long, ExpressCompany> expressCompanyMap = buildExpressCompanyMap(staff, trades);

            for (TbTrade trade : trades) {
                Long userId = TradeUtils.isGxTrade(trade) ? trade.getTaobaoId() : trade.getUserId();
                ExpressCompany expressCompany = expressCompanyMap.get(trade.getTemplateId()) == null ? new ExpressCompany() : expressCompanyMap.get(trade.getTemplateId());
                trade.setShopName(shopNameMap.get(userId));
                trade.setExpressCode(expressCompany.getCode());
                trade.setExpressName(expressCompany.getName());
                trade.setUserId(userId);
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s from db took: %sms, NumFound: %s, Sql: %s", "未发货订单物流预警统计", (System.currentTimeMillis() - start), trades.size(), q.toString())));
            }
            return trades;
        } catch (Exception e) {
            throw new TradeException("物流预警统计出错", e);
        }
    }

    @Override
    public Long statisticsCountWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params) {
        long start = System.currentTimeMillis();
        try {
            Query q = buildQuerySql(staff, params);
            checkAndFIllProfile(staff);
            Long count = tbTradeDao.queryTradesCount(staff, q, false);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s from db took: %sms, NumFound: %s, Sql: %s", "未发货订单物流预警统计", (System.currentTimeMillis() - start), count, q.toString())));
            }
            return count == null ? 0 : count;
        } catch (Exception e) {
            throw new TradeException("物流预警统计出错", e);
        }
    }

    /**
     * 根据订单唯一码查询到的sid是否为空
     *
     * @param params
     * @return
     */
    private boolean searchEmptyByUniqueCodes(TradeQueryParams params) {
        if (params.getSid() == null || params.getSid().length > 0) {
            return false;
        }

        if (params.getUniqueCodes() == null || params.getUniqueCodes().size() == 0) {
            return false;
        }

        return true;
    }

    List<TbTrade> querySplitTrades(Staff staff, TradeQueryParams params, List<TbTrade> list) {
        if (StringUtils.isNotEmpty(params.getUniqueCodeSplitQuery())) {
            checkAndFIllProfile(staff);
            List<String> tidList = list.stream().map(t -> t.getTid()).collect(Collectors.toList());
            List<TbTrade> tbTrades = tbTradeDao.queryByTids(staff, tidList.toArray(new String[0]));
            return tbTrades;
        }
        return list;
    }

    /**
     * 获取订单中的智能计算数量
     */
    @Override
    public Trades setTradeExtItemQuantity(Staff staff, Trades trades) {
        if (CollectionUtils.isEmpty(trades.getList())) {
            return trades;
        }
        List<Trade> tradeList = trades.getList();
        List<Trade> res = setTradeExtItemQuantity(staff, tradeList);
        trades.setList(res);
        return trades;
    }

    /**
     * 获取订单中的智能计算数量
     */
    @Override
    public List<Trade> setTradeExtItemQuantity(Staff staff, List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return tradeList;
        }
        Map<Long,TradeExt> tradeExtMap = new HashMap<>();
        Set<Long> sidList = tradeList.stream().filter(x-> x.getTradeExt() == null).map(TradeBase::getSid).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(sidList)) {
            //获取当前扩展表中的智能单品数量
           List<TradeExt> tradeExtList = tradeExtService.queryTradeExtJsonItemQuantity(staff, new ArrayList<>(sidList), null, null);
            if (CollectionUtils.isNotEmpty(tradeExtList)) {
                tradeExtMap = tradeExtList.stream().collect(Collectors.toMap(x->x.getSid(),x->x));
            }
        }

        //给查询结果赋值
        for (Trade trade : tradeList) {
            TradeExt tradeExt = trade.getTradeExt();
            if (null != trade.getSid() && tradeExt == null) {
                tradeExt = tradeExtMap.get(trade.getSid());
            }
            if (tradeExt !=null) {
                if ( tradeExt.getItemQuantity() != null) {
                    trade.setItemQuantity(tradeExt.getItemQuantity());
                }else {
                    Object itemQuantity = TradeExtUtils.getExtraFieldValue(tradeExt, "itemQuantity");
                    trade.setItemQuantity(itemQuantity == null?null:Integer.parseInt(itemQuantity.toString()));
                }
            }
        }
        return tradeList;
    }


    public Query buildQuerySql(Staff staff, LogisticsWarningTradeQueryParams param) {
        TradeQueryParams queryParams = new TradeQueryParams();
        if (CollectionUtils.isNotEmpty(param.getSysStatus())) {
            queryParams.setSysStatus(param.getSysStatus().toArray(new String[0]));
        }
        queryParams.setQueryId(62L).setCheckActive(true).setIsOutstock(0).setIsPresell(0);
        queryParams.setLogisticsWarningStartTime(param.getStartPayDate());
        queryParams.setLogisticsWarningEndTime(param.getEndPayDate());
        queryParams.setDeliveryStartTime(param.getStartTime());
        queryParams.setDeliveryEndTime(param.getEndTime());
        queryParams.setStatusList(CollectionUtils.isNotEmpty(param.getStatus()) ? param.getStatus().toArray(new String[0]) : new String[0]);//平台状态
        queryParams.setUserIds(CollectionUtils.isNotEmpty(param.getShopIds()) ? param.getShopIds().toArray(new Long[0]) : new Long[0]);
        // 根据快递公司获取模板ID
        getTemplateIdsByCpcode(staff, param, queryParams);

        TradeConfig tc = tradeConfigService.get(staff);
        Query q = new Query();
        q.setTradeTable("trade_not_consign");
        // 强制引用索引
        q.setTradeIndex(" force index (idx_company_paytime_user) ");
        if (StringUtils.isNotBlank(param.getSearchKey())) {
            queryParams.setMixKey(param.getSearchKey());
            tradeSqlQueryBuilder.buildMixKeyQuery(q,staff, queryParams);
        }
        tradeSqlQueryBuilder.buildQuery(q, staff, queryParams);
        tradeSqlQueryBuilder.buildSysStatusQuery(q, queryParams, tc);
        tradeSqlQueryBuilder.buildStatusQuery(q, queryParams);
        tradeSqlQueryBuilder.buildLogisticsWarningQuery(staff, q, queryParams);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.sid", CollectionUtils.isNotEmpty(param.getSids()) ? param.getSids().toArray(new Long[0]) : new Long[0]);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.out_sid", CollectionUtils.isNotEmpty(param.getOutSids()) ? param.getOutSids().toArray(new String[0]) : new String[0]);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.tid", CollectionUtils.isNotEmpty(param.getTids()) ? param.getTids().toArray(new String[0]) : new String[0]);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.warehouse_id", CollectionUtils.isNotEmpty(param.getWarehouseIds()) ? param.getWarehouseIds().toArray(new Long[0]) : new Long[0]);
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.source", CollectionUtils.isNotEmpty(param.getSources()) ? param.getSources().toArray(new String[0]) : new String[0]);
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_cancel", 0);
        appendExpressFilterCondition(q, queryParams.getCommonIds(), queryParams.getCloudIds());
        return q;
    }

    public Map<Long, String> buildShopMap(List<TbTrade> tbTrades) {
        Map<Long, String> shopMap = new HashMap<>();
        List<Long> userIds = tbTrades.stream().map(t -> {
            if (TradeUtils.isGxTrade(t)) {
                return t.getTaobaoId();
            } else {
                return t.getUserId();
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<Shop> shops = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
            shopMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, shop -> StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle()), (v1, v2) -> v2));
        }
        return shopMap;
    }

    public Map<Long, ExpressCompany> buildExpressCompanyMap(Staff staff, List<TbTrade> tbTrades) {
        Map<Long, ExpressCompany> expressCompanyMap = new HashMap<>();
        Map<Long, List<TbTrade>> templateId2TradeMap = tbTrades.stream().filter(t -> t.getTemplateId() != null).collect(Collectors.groupingBy(TbTrade::getTemplateId));
        for (Map.Entry<Long, List<TbTrade>> entrySet : templateId2TradeMap.entrySet()) {
            IExpressTemplateBase template = userWlbExpressTemplateService.userQueryWithCache(staff, entrySet.getKey(), false);
            if (template == null) {
                template = userExpressTemplateService.userQueryWithCache(staff, entrySet.getKey(), false);
            }
            Long expressId = template != null ? template.getExpressId() : null;
            if (expressId != null) {
                ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(expressId);
                expressCompanyMap.put(template.getId(), expressCompany);
            }
        }
        return expressCompanyMap;
    }

    public void getTemplateIdsByCpcode(Staff staff, LogisticsWarningTradeQueryParams params, TradeQueryParams tradeQueryParams) {
        if (StringUtils.isNotBlank(params.getCpCode())) {
            List<Long> commmonTemplateIds = new ArrayList<>();
            List<Long> cloudTemplateIds = new ArrayList<>();
            ExpressCompany expressCompany = expressCompanyService.getExpressCompanyByCode(params.getCpCode());
            Long expressId = expressCompany != null ? expressCompany.getId() : null;
            if (expressId != null) {
                expressTemplateCommonService.getTemplateListByExpressIds(staff, Collections.singletonList(expressId), commmonTemplateIds, cloudTemplateIds);
            }
            tradeQueryParams.setCommonIds(commmonTemplateIds);
            tradeQueryParams.setCloudIds(cloudTemplateIds);
        }
    }

    @Override
    public List<ReplenishTrades> checkReplenishTrade(Staff staff, Long... sids) {
        Assert.notEmpty(sids, "订单系统id信息不能为空");
        //根据sid查询订单
        Trades search = search(staff, new TradeQueryParams().setSid(sids));
        if (CollectionUtils.isEmpty(search.getList())) {
            return null;
        }
        Map<String, Long> tidSidMap = search.getList().stream().collect(Collectors.toMap(Trade::getTid, Trade::getSid));
        checkAndFIllProfile(staff);
        //根据tid查询补发订单
        List<TbTrade> tbTradeList = tbTradeDao.queryTradeByAfterSales(staff, TradeTypeEnum.REISSUE_TRADE, tidSidMap.keySet().toArray(new String[1]));
        if (CollectionUtils.isEmpty(tbTradeList)) {
            return null;
        }

        Map<String, ReplenishTrades> replenishTradesMap = new HashMap<>();
        tbTradeList.forEach(trade -> {
            String tid = trade.getTid();
            int index = tid.lastIndexOf("-");
            if (index > 0) {
                tid = tid.substring(0, index);
            }
            Long sid = tidSidMap.get(tid);
            if (sid == null || sid <= 0) {
                return;
            }
            ReplenishTrades replenishTrades = replenishTradesMap.get(tid);
            if (java.util.Objects.isNull(replenishTrades)) {
                replenishTrades = new ReplenishTrades();
                replenishTrades.setReplenishTrades(new ArrayList<>());
                replenishTrades.setSid(sid);
                replenishTrades.setTid(tid);
                replenishTradesMap.put(tid, replenishTrades);
            }
            replenishTrades.getReplenishTrades().add(trade);
        });

        return new ArrayList<>(replenishTradesMap.values());
    }



    /**
     * 支持排除订单来源
     * https://gykj.yuque.com/entavv/xb9xi5/ga143h99txg5q1q4
     *
     * @param q
     * @param params
     */
    public void buildExcludeSource(Query q, TradeQueryParams params) {
        if (StringUtils.isNotEmpty(params.getExcludeSource())) {
            String[] vs = getAsStringArray(params.getExcludeSource(), ",", true);
            if (vs.length > 0) {
                q.and().append("t.source").append(" NOT IN(");
                for (int i = 0; i < vs.length; i++) {
                    q.append(i == 0 ? "?" : ", ?").add(vs[i]);
                }
                q.append(")");
            }
        }
    }

    /**
     * 获取打印需要用到的订单信息 只会返回部分字段
     *
     * @param needMerge 需要合单
     */
    @Override
    public List<Trade> queryTrades4PrintEnd(Staff staff, List<Long> sids, boolean needMerge) {
        String fields = "sid,merge_sid,tid,short_id,merge_type,out_sid,print_count,scalping,is_deduct,template_id,tag_ids,type,sys_status,net_weight,item_num,logistics_company_id,wave_id,template_type,warehouse_id,user_id,source,receiver_name,receiver_state,receiver_city,receiver_country,receiver_district,receiver_address,receiver_phone,receiver_mobile,payment,taobao_id,post_fee,status,split_sid,split_type,source_id,dest_id,convert_type,belong_type";
        checkAndFIllProfile(staff);
        List<TbTrade> trades = tbTradeDao.querySomeFieldsJDBC(staff, fields, sids, null);
        if (!needMerge) {
            return new ArrayList<>(trades);
        }
        List<Long> mergeSids = trades.stream().filter(TradeUtils::isMerge).map(Trade::getSid).collect(Collectors.toList());
        List<TbTrade> mergeTrades = tbTradeDao.querySomeFieldsJDBC(staff, fields, null, mergeSids);
        mergeTrades.addAll(trades.stream().filter(t -> !TradeUtils.isMerge(t)).collect(Collectors.toList()));
        return new ArrayList<>(mergeTrades);
    }

    @Override
    public List<Trade> queryMergeTradeList(Staff staff, List<Long> mergeSids) {
        if (CollectionUtils.isEmpty(mergeSids)) {
            return new ArrayList<>();
        }
        String fields = "sid,merge_sid,tid,sys_consigned,short_id,merge_type,out_sid,print_count,scalping,is_deduct,template_id,tag_ids,type,sys_status,item_num,logistics_company_id,wave_id,template_type,warehouse_id,user_id,source,receiver_name,receiver_state,receiver_city,receiver_country,receiver_district,receiver_address,receiver_phone,receiver_mobile,payment,taobao_id,post_fee,status,split_sid,split_type";
        List<TbTrade> mergeTrades = tbTradeDao.querySomeFieldsJDBC(staff, fields, null, mergeSids);
        return new ArrayList<>(mergeTrades);
    }



    @Override
    public List<Trade> queryTrades4Print(Staff staff, List<Long> sids, boolean needOrder) {
        List<Trade> trades = _queryBySids(staff, sids.toArray(new Long[0]), needOrder,new QueryLogBuilder(staff));

        return trades;
    }

    @Override
    public CursorListBase<BaseTrade> searchBaseTrade(TradeQueryRequest request) {
        QueryLogBuilder logBuilder = new QueryLogBuilder(request.getCompanyId(),request.getStaffId(),null).append("searchBaseTrade").startTimer();
        CursorListBase<BaseTrade> base = searchTrade(request, null, null,false, new IResultConvert<BaseTrade>() {
            @Override
            public List<BaseTrade> convert(Staff staff, List<TbTrade> trades) {
                List<BaseTrade> list = new ArrayList<>();
                for (TbTrade trade : trades) {
                    BaseTrade base = new BaseTrade();
                    base.setCompanyId(trade.getCompanyId());
                    base.setUserId(trade.getUserId());
                    base.setSid(trade.getSid());
                    base.setTid(trade.getTid());
                    base.setOutSid(trade.getOutSid());
                    base.setSource(trade.getSource());
                    base.setSubSource(trade.getSubSource());
                    base.setEnableStatus(trade.getEnableStatus());
                    base.setSysStatus(trade.getSysStatus());
                    base.setUnifiedStatus(trade.getUnifiedStatus());
                    base.setPayTime(trade.getPayTime());
                    base.setMergeSid(trade.getMergeSid());
                    base.setSplitSid(trade.getSplitSid());
                    base.setCreated(trade.getCreated());
                    base.setUpdTime(trade.getUpdTime());
                    base.setConsignTime(trade.getConsignTime());
                    base.setExpressPrintTime(trade.getExpressPrintTime());
                    list.add(base);
                }
                return list;
            }
        });
        logBuilder.appendTook(queryTookThreshold(request.getCompanyId(),null,5000L)).printDebug(logger);
        tradeSearchSupport.fliterBaseMergeByCursor(request,base);
        return base;
    }

    @Override
    public CursorListBase<TbTrade> searchTrade(TradeQueryRequest request, TradeAssembleParams params) {
        validateParams(request, params,false);

        QueryLogBuilder logBuilder = new QueryLogBuilder(request.getCompanyId(),request.getStaffId(),null).append("searchTrade").startTimer();
        boolean assemblyByMerge = params.isQueryMerge() && Objects.equal(params.getMergeStyle(), TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB);
        CursorListBase<TbTrade> cursorListBase = searchTrade(request, params.getQueryFields(), logBuilder,assemblyByMerge, new IResultConvert<TbTrade>() {
            @Override
            public List<TbTrade> convert(Staff staff, List<TbTrade> trades) {
                if (CollectionUtils.isEmpty(trades)) {
                    return trades;
                }
                List<Trade> result = tradeAssembleBusiness.assemblyTrades(staff, params, logBuilder, trades);
                return result.stream().map(x -> (TbTrade) x).collect(Collectors.toList());
            }
        });
        logBuilder.appendTook(queryTookThreshold(request.getCompanyId(),null,5000L)).printDebug(logger);

        tradeSearchSupport.fliterMergeByCursor(request,cursorListBase);
        return cursorListBase;
    }

    private void validateParams(TradeQueryRequest request, TradeAssembleParams params,boolean count) {
        Assert.notNull(request,"request 不能为空");
        Assert.notNull(request.getCompanyId(),"companyId 不能为空");
        Assert.notNull(request.getStaffId(),"staffId 不能为空");
        Assert.notNull(params,"params 不能为空");

        if (request.getReceiverMobiles() != null) {
            Assert.isTrue(request.getReceiverMobiles().length <= 10,"最大支持10个收件人手机号");
        }

        Page page = request.getPage();
        if (!count && page != null) {
            if (page.getPageSize() !=null && page.getPageSize() > 50000) {
                Assert.isTrue(true,"单页最大支持查询5万条数据");
            }
            Long size = MathUtils.multiply(page.getPageNo(), page.getPageSize()).longValue();
            if (size > 100000) {
                new MonitorLogBuilder(request.getStaffId(),LogBusinessEnum.QUERY.getSign())
                        .append("深翻页查询预警").format("pageNo: %s pageSize: %s",page.getPageNo(),page.getPageSize()).printWarn(logger,new Exception());
            }
            //防呆 防止调用方无脑循环查询
            if (size > 1000000) {
                Assert.isTrue(true,"查询分页数据过多,请缩小查询范围分片进行查询");
            }
        }
        if (params.isQueryMerge()) {
            request.setContainsMerged(true);
        }
    }

    @Override
    public Long countTrade(TradeQueryRequest request, TradeAssembleParams params) {
        validateParams(request, params,true);

        QueryLogBuilder logBuilder = new QueryLogBuilder(request.getCompanyId(),request.getStaffId(),null).append("countTrade").startTimer();
        boolean assemblyByMerge = params.isQueryMerge() && Objects.equal(params.getMergeStyle(), TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB);

        Staff staff =  tradeSearchSupport.getFullStaff(request.getCompanyId(),request.getStaffId());
        logBuilder.recordTimer("queryFullStaffById");
        Query query = queryBuilder.convert2Query(staff,null,request,assemblyByMerge,true);
        logBuilder.recordTimer("convert2Query");

        if (query.isStopQuery()) {
            logBuilder.append(query.getStopReason()).printDebug(logger);
            return 0L;
        }
        DevLogBuilder.forDev(staff,LogBusinessEnum.QUERY.getSign()).append("countTrade").append("sql",replaceHolder(query,query.toString())).multiPrintDebug(logger);
        String sql = replaceHolder(query,query.getQ().toString());
        Long aLong = tbTradeDao.getJdbcTemplate(staff).queryForObject(sql, query.getArgs().toArray(), Long.class);
        logBuilder.recordTimer("tbTradeDao").append(" trades["+aLong+"];");
        logBuilder.appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,request.getCompanyId(),LogBusinessEnum.QUERY.getSign())?100L:8000L).printDebug(logger);
        return aLong;
    }

    private <T>  CursorListBase<T> searchTrade(TradeQueryRequest request, List<String> queryFields, AbsLogBuilder logBuilder, boolean assemblyByMerge, IResultConvert<T> converter) {
        new DevLogBuilder(request.getCompanyId(),LogBusinessEnum.QUERY.getSign()).append("request:").appendJSon(request).printDebug(logger);
        if (logBuilder == null) {
            logBuilder = AbsLogBuilder.getNvlInstance();
        }
        Staff staff =  tradeSearchSupport.getFullStaff(request.getCompanyId(),request.getStaffId());
        try{
            CursorListBase<T> result = new CursorListBase<>();
            logBuilder.reBaseTimer();
            logBuilder.recordTimer("queryFullStaffById");
            Query query = queryBuilder.convert2Query(staff,queryFields,request,assemblyByMerge,false);
            logBuilder.recordTimer("convert2Query");
            result.setHasNext(false);


            if (query.isStopQuery()) {
                result.setList(Collections.emptyList());
                result.setMessage(query.getStopReason());
                return result;
            }

            new QueryLogBuilder(staff).append("sql",replaceHolder(query,query.toString())).printDebug(logger);
            String sql = replaceHolder(query,query.getQ().toString());
            List<TbTrade> tbTrades = tbTradeDao.queryWithJdbcTemplate(staff, sql, query.getArgs().toArray());
            logBuilder.recordTimer("tbTradeDao").append(" trades["+tbTrades.size()+"];");

            if (CollectionUtils.isEmpty(tbTrades)) {
                result.setList(Collections.emptyList());
                return result;
            }
            int pageSize = 0;
            if (request instanceof TradeCursorQueryRequest) {
                TradeCursorQueryRequest cursorRequest = (TradeCursorQueryRequest) request;
                pageSize = cursorRequest.getPageSize();

            }else {
                pageSize = request.getPage() == null? 2000 : request.getPage().getPageSize();
            }
            if (tbTrades.size() >= pageSize + 1) {
                result.setHasNext(true);
                tbTrades.remove(tbTrades.size() - 1);
            }
            if (request instanceof TradeCursorQueryRequest) {
                TbTrade tbTrade = tbTrades.get(tbTrades.size() - 1);
                Long sid = tbTrade.getSid();
                if (assemblyByMerge) {
                    //(CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END)
                    if (tbTrade.getMergeSid() != null && tbTrade.getMergeSid() > 0) {
                        sid = tbTrade.getMergeSid();
                    }
                }
                Date time = TradeSearchSupport.getDateByField(tbTrade, request.getTimeType());
                result.setCursor(time.getTime() + "_" +sid );
            }


        //系统解密
        decodeFieldFilter.filterTrades(staff,tbTrades);

        List<T> list = converter.convert(staff,tbTrades);
        result.setList(list);

            return result;
        }catch (BadSqlGrammarException e){
            new QueryLogBuilder(staff).appendError("sql语句错误",e).printError(logger,e);
            new RuntimeException("服务器内部错误");
        }catch (QueryTimeoutException e){
            new QueryLogBuilder(staff).appendError("订单查询超时",e).printError(logger,e);
            new RuntimeException("订单查询超时");
        }
        return null;
    }

    private String replaceHolder(Query q,String sql){
        sql = sql.replace(ConditionConstants.TRADE_TABLE_PLACE_HOLDER, q.getTradeTable())
                 .replace(ConditionConstants.ORDER_TABLE_PLACE_HOLDER, q.getOrderTable())
                 .replace(ConditionConstants.FORCE_INDEX_HOLDER, q.getTradeIndex() ==null ? "":q.getTradeIndex());
        return sql;
    }

    interface IResultConvert<T>{

        List<T> convert(Staff staff,List<TbTrade> trade);

    }


    @Resource
    private SceneLightQueryer sceneLightQueryer;

    @Override
    public List<Trade> queryLightTradeByScene(Staff staff, SenceCodeEnum sceneCode,List<Integer> fieldGroups,Long... sids) {
        Assert.notEmpty(sids, "订单系统id信息不能为空");
        Assert.notNull(sceneCode, "业务操作场景不能为空");
        Assert.notNull(staff, "staff不能为空");
        SceneLightQueryContext context = new SceneLightQueryContext();
        context.setStaff(staff);
        context.setSceneCode(sceneCode);
        context.setFieldGroup(fieldGroups);
        return sceneLightQueryer.doQueryTrades(context,sids);
    }
    @Override
    public List<Long> querySidsByOuterIIds(Staff staff, List<String> outerIIds, boolean matched, Page page) {
        return querySidsByItemSkuIds(staff, outerIIds, matched, "outer_iid", page);
    }

}

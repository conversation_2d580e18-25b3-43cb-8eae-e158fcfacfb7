package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.business.buyout.DeductBusiness;
import com.raycloud.dmj.business.buyout.DeductRevenueBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.IDeductService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @created 2019-04-04 17:57
 */
@Service
public class DeductService implements IDeductService {

    @Resource
    DeductRevenueBusiness deductRevenueBusiness;

    @Override
    public void deduct(Staff staff, Long deductAmount) {
        deductRevenueBusiness.asyncDeduct(staff, deductAmount);
    }
}

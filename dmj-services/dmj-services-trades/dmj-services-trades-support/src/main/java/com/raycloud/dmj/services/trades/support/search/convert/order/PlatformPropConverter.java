package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_PLAT)
public class PlatformPropConverter extends AbsOrderConditionConverter {

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return notEmpty(condition.getPlatformOuterIds())
                || notEmpty(condition.getOuterIids())
                || notNull(condition.getPlatFormTitle())
                || notEmpty(condition.getNumIids())
                || notEmpty(condition.getSkuIds())
                || notNull(condition.getPlatSkuProp());
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        boolean added = false;
        Query platRef = new Query();
        //查询平台信息 需要过滤掉套件下的单品
        platRef.append(" AND ( o.combine_id = 0  ");

        if (notEmpty(condition.getPlatformOuterIds())) {
            platRef.append("AND (");
            listCondition(platRef,"o.outer_iid",condition.getPlatformOuterIds(),1);
            orListCondition(platRef,"o.outer_sku_id",condition.getPlatformOuterIds(),1);
            platRef.append(")");
            added = true;
        }

        added = added | andListCondition(platRef, "o.outer_iid", condition.getOuterIids(), condition.getQueryType());
        added = added | andSingleCondition(platRef, "o.title", condition.getPlatFormTitle(), condition.getQueryType());
        added = added | andListCondition(platRef, "o.num_iid", condition.getNumIids(),condition.getQueryType());
        added = added | andListCondition(platRef, "o.sku_id", condition.getSkuIds(),condition.getQueryType() );
        added = added | andSingleCondition(platRef, "o.sku_properties_name", condition.getPlatSkuProp(), condition.getQueryType());

        platRef.append(")");

        if (added) {
            orderQry.append(platRef.getQ().toString()).add(platRef.getArgs());
            hotQry.append(platRef.getQ().toString()).add(platRef.getArgs());
        }
        return added;
    }

}

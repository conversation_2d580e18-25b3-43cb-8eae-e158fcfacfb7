package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeFillEnum;
import com.raycloud.dmj.domain.trades.search.ReturnFieldGroupEnum;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-17
 */
@Component
public class CommonQueryer extends TradeAssembleStrategy{

    @Override
    public SenceCodeEnum supportScene() {
        return SenceCodeEnum.COMMON;
    }

    @Override
    TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long... sids) {
        TradeAssembleParams assembleParams = TradeAssembleParams.justTrade().setQueryMerge(true).setMergeStyle(TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB);

        if (containsGroup(context, ReturnFieldGroupEnum.EXCEPTION)) {
            assembleParams.setFill(true)
                    .addIncludeFills(TradeFillEnum.ORDER_EXCEPT_FILL)
                    .addIncludeFills(TradeFillEnum.TRADE_EXCEPT_FILL)
                    .addIncludeFills(TradeFillEnum.TRADE_TAG_FILL);
        }
        if (containsGroup(context, ReturnFieldGroupEnum.EXCEPTION, ReturnFieldGroupEnum.FULL_ORDER)) {
            assembleParams.setQueryOrder(true);
        }

        return assembleParams;
    }

    boolean containsGroup(SceneLightQueryContext context, ReturnFieldGroupEnum ... groups){
        if (CollectionUtils.isEmpty(context.getFieldGroup())) {
            return false;
        }
        for (ReturnFieldGroupEnum group : groups) {
            if (context.getFieldGroup().contains(group.getKey())) {
                return true;
            }
        }
        return false;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.item.ItemReplaceSingleToSuitBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.audit.TradeAuditResult;
import com.raycloud.dmj.domain.trade.audit.TradeUnAuditResult;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trades.ITradeImportService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.utils.CommonConstants.PLAT_FORM_TYPE_SYS;

/**
 * 取消换货前商品平台信息变更异常
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Service
@ListenerBind("trade.batch.cancel.exchangeItemPlatModifyExcept")
public class TradeCancelExchangeItemPlatModifyExceptService implements IEventListener {

    @Resource
    private IStaffService staffService;

    @Resource
    private ILockService lockService;

    @Resource
    private TradeLockBusiness tradeLockBusiness;

    @Resource
    private ProgressService progressService;

    @Resource(name = "tbTradeSearchService")
    private  ITradeSearchService tradeSearchService;

    @Resource
    private ITradeImportService tradeImportService;

    @Resource
    ITradeAuditService tradeAuditService;

    @Resource
    private ItemReplaceSingleToSuitBusiness itemReplaceSingleToSuitBusiness;


    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) (source);
        Staff staff = staffService.queryFullById(evt.getArg(0, Long.class));
        TradeControllerParams params = evt.getArg(1, TradeControllerParams.class);

        if (Objects.isNull(staff)) {
            Logs.error(String.format("staffId=%s is not exist", evt.getArg(0, Long.class)));
            return;
        }

        long start = System.currentTimeMillis();
        ProgressData progress = initProgress(staff);
        try {
            List<Trade> trades;
            String type = StringUtils.isNotBlank(params.getUserSearch()) ? params.getUserSearch() : "0";
            if ("0".equals(type)) { // 0代表页面勾选勾选订单
                trades = cancelExceptBySids(staff, progress, params);
            } else {    // 1代表查询结果
                trades = cancelExceptByQuery(staff, progress, params);
            }
            progress.setSucNum((long) trades.size());
        } catch (Throwable e) {
            Logs.error(LogHelper.buildLog(staff, "取消换货前商品平台信息变更异常"), e);
        } finally {
            progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_CANCEL_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT, progress);
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("事件[%s]处理结束,耗时:%sms", evt.getEventName(), (System.currentTimeMillis() - start))));
        }
    }

    private ProgressData initProgress(Staff staff) {
        ProgressData progress = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_CANCEL_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT).setProgress(1);
        progress.setAtomicSucNum(new AtomicLong(0));
        return progress;
    }

    private List<Trade> cancelExceptBySids(Staff staff, ProgressData progress, TradeControllerParams params) {
        if (StringUtils.isBlank(params.getSids())) {
            Logs.error("处理单号不能为空");
            return Lists.newArrayList();
        }
        return cancelExceptBySids(staff, progress, ArrayUtils.toLongArray(params.getSids()));
    }

    private List<Trade> cancelExceptByQuery(Staff staff, ProgressData progress, TradeControllerParams params) {
        List<Trade> trades = queryTrades(staff, params);
        if (CollectionUtils.isEmpty(trades)) {
            return Lists.newArrayList();
        }
        // 暂不用多线程
        List<Trade> result = Lists.newArrayList();
        Lists.partition(trades, 100).forEach(
                partition -> result.addAll(cancelExceptBySids(staff, progress, TradeUtils.toSidList(partition).toArray(new Long[0])))
        );
        return result;
    }

    private List<Trade> queryTrades(Staff staff, TradeControllerParams params) {
        long start = System.currentTimeMillis();
        TradeQueryParams tradeQueryParams = TradeQueryParams.copyParams(params);
        tradeQueryParams.setIsCancel(0);
        tradeQueryParams.setNeedFill(false).setQueryFlag(1).setCheckItem(false);
        tradeQueryParams.setUndoMergeFill(false);
        tradeQueryParams.setQueryOrder(false);
        tradeQueryParams.setPage(new Page(1, 100000));
        tradeQueryParams.setIsExcep(1);
        tradeQueryParams.setQueryId(SystemTradeQueryParamsContext.QUERY_EXCEP);
        tradeQueryParams.setExceptionStatus(String.valueOf(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getId()));
        Trades search = tradeSearchService.search(staff, tradeQueryParams);
        Logs.debug(LogHelper.buildLog(staff, String.format("查询出结果订单%s条，耗时%s", Optional.ofNullable(search.getTotal()).orElse(0L), System.currentTimeMillis() - start)));
        return search.getList();
    }


    public List<Trade> cancelExceptBySids(Staff staff, ProgressData progress, Long... sids) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            // 已审核订单，需要先反审核
            cancelAudit(staff, originTrades);

            List<Trade> sysTrades = Lists.newArrayList();
            List<Trade> platTrades = Lists.newArrayList();
            originTrades.forEach(trade -> {
                if (PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
                    sysTrades.add(trade);
                } else {
                    platTrades.add(trade);
                }
            });

            // 处理系统单
            processSysTrade(staff, sysTrades, progress);
            // 处理平台单
            return processPlatTrade(staff, platTrades);
        });
    }

    // 已审核订单需要先反审核
    private void cancelAudit(Staff staff, List<Trade> trades) {
        List<Trade> needCancelAuditTrades = Lists.newArrayList();
        Iterator<Trade> iterator = trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT)) {
                needCancelAuditTrades.add(trade);
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(needCancelAuditTrades)) {
            return;
        }

        Logs.debug(LogHelper.buildLog(staff, String.format("取消【换货前商品平台信息变更】反审核订单 sids:%s", TradeUtils.toSidList(needCancelAuditTrades))));
        TradeUnAuditResult auditResult = tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_EXCEPT, TradeUtils.toSids(needCancelAuditTrades));
        if (CollectionUtils.isNotEmpty(auditResult.getSuccessTrades())) {
            trades.addAll(auditResult.getSuccessTrades());
        }
    }

    private void processSysTrade(Staff staff, List<Trade> sysTrades, ProgressData progress) {
        List<Trade> trades = itemReplaceSingleToSuitBusiness.cancelSysTradeExceptSingleToSuitOrders(staff, sysTrades);
        trades.forEach(trade -> progress.getErrorMsg().add(String.format("{\"sid\":%s, \"tid\":\"%s\"}", trade.getSid(), trade.getTid())));
    }

    private List<Trade> processPlatTrade(Staff staff,  List<Trade> platTrades) {
        // 还原商品数据
        List<Trade> trades = itemReplaceSingleToSuitBusiness.restoreHasExceptSingleToSuitOrders(staff, platTrades);
        if (CollectionUtils.isEmpty(trades)) {
            return Lists.newArrayList();
        }

        // 指定下载
        Map<Long, List<Trade>> userId2trades = trades.stream().collect(Collectors.groupingBy(Trade::getUserId));
        userId2trades.forEach((userId, subTrades) -> {
            User user = staff.getUserByUserId(userId);
            tradeImportService.syncTradesByTid(user, true, TradeUtils.toTids(subTrades));
        });
        return trades;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.business.ThreadPoolBusiness;
import com.raycloud.dmj.business.operate.SyncTradeBusiness;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.TradeImportResult;
import com.raycloud.dmj.domain.trades.vo.MultiUserTradeDownVo;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.diamond.TradeSyncConfigUtils;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ISysTradeService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;


/**
 * @description 订单批量指定下载
 * <AUTHOR>
 * @date 2023/4/20 13:57
 */
@Service
public class TradeBatchDownloadService extends ThreadPoolBusiness<String> {

    private static final Logger logger = Logger.getLogger(TradeBatchDownloadService.class);

    public static final int PROGRESS_DOWNLOADING = 1;
    public static final int PROGRESS_FINISH = 2;

    @Resource
    ISysTradeService sysTradeService;

    @Resource
    private SyncTradeBusiness syncTradeBusiness;

    @Resource
    IProgressService progressService;

    @Override
    protected int getCoreThreadCount() {
        return 6;
    }

    @Override
    protected int getMaxThreadCount() {
        return 6;
    }

    @Override
    protected int getTouchPoolSize() {
        return 50;
    }

    @Override
    protected int getBatchSize() {
        return 50;
    }

    @Override
    protected BlockingQueue<Runnable> getWorkQueue() {
        return new LinkedBlockingQueue<>(200);
    }

    public void batchDownload(User user, Set<String> tidSet) {
        if (CollectionUtils.isEmpty(tidSet)) {
            return;
        }
        long start = System.currentTimeMillis();
        Map<String, String> failResult = new ConcurrentHashMap<>(tidSet.size());
        try {
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD, tidSet.size(), PROGRESS_DOWNLOADING);
            run(user.getStaff(), new ArrayList<>(tidSet), new AbstractBusiness() {
                @Override
                public void doBusiness(List<String> data) {
                    logger.debug(LogHelper.buildUserLog(user, String.format("多线程执行批量指定下载，当前线程下载数:%d", data.size())));
                    doBatchDownload(user, data, failResult);
                }
            });
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD,
                    buildResult(user, tidSet.size(), failResult));
        } catch (Exception e) {
            logger.error(LogHelper.buildUserLog(user, String.format("批量指定下载时发生异常,tids:%s", tidSet)), e);
            ProgressData data = new ProgressData();
            data.setCountAll(tidSet.size());
            data.setProgress(PROGRESS_FINISH);
            data.setErrorMsg(Lists.newArrayList("服务异常，请稍后重试或咨询客服处理"));
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD, data);
        }
        logger.debug(LogHelper.buildUserLog(user, String.format("本次批量指定下载%d笔订单，耗时%d毫秒", tidSet.size(), (System.currentTimeMillis() - start))));
    }

    /**
     * 多店铺同批批量下载订单
     * @param user
     * @param list
     */
    public void multiUserBatchDownload(User user,List<MultiUserTradeDownVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        long start = System.currentTimeMillis();
        Long count =  list.stream().flatMap(dto -> dto.getTids().stream()).count();
        Map<String, String> failResult = new ConcurrentHashMap<>();
        try {
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD, count.intValue(), PROGRESS_DOWNLOADING);
            for (MultiUserTradeDownVo userTrade : list){
                List<String> tids = userTrade.getTids();
                for (List<String> tidList : Lists.partition(tids, 100)) {
                    try {
                        run(user.getStaff(), new ArrayList<>(tidList), new AbstractBusiness() {
                            @Override
                            public void doBusiness(List<String> data) {
                                logger.debug(LogHelper.buildUserLog(user, String.format("多店铺多线程执行批量指定下载，当前线程下载数:%d", data.size())));
                                doBatchDownload(user.getStaff().getUserByUserId(userTrade.getUserId()), data, failResult);
                            }
                        });
                    } catch (Exception e) {
                        logger.error(LogHelper.buildUserLog(user, String.format("【%s】多店铺批量指定下载时发生异常,tids:%s",userTrade.getUserId() ,JSONObject.toJSONString(tidList))), e);
                        failResult.put(JSONObject.toJSONString(tidList), e.getMessage());
                    }
                }
            }
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD,
                    buildResult(user, count.intValue(), failResult));
        }catch (Exception e){
            ProgressData data = new ProgressData();
            data.setCountAll(count.intValue());
            data.setProgress(PROGRESS_FINISH);
            data.setErrorMsg(Lists.newArrayList("服务异常，请稍后重试或咨询客服处理"));
            progressService.setProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD, data);
        }
        logger.debug(LogHelper.buildUserLog(user, String.format("本次批量指定下载%d笔订单，耗时%d毫秒", count.intValue(), (System.currentTimeMillis() - start))));
    }

    private void doBatchDownload(User user, List<String> tids, Map<String, String> failResult){
        TradeImportResult result = new TradeImportResult();
        if(TradeSyncConfigUtils.canUpdatePaymentUserIds(user.getId())){
            result.setCanUpdatePayment(true);
        }
        try {
//            sysTradeService.syncTradeByTid(user, StringUtils.join(tids, ","));
            syncTradeBusiness.syncTradeByTid(user, StringUtils.join(tids, ","), result, true);
        } catch (Exception e) {
            // 发生异常时，需要对这一批订单进行轮询
            for (String tid : tids) {
                try {
//                    sysTradeService.syncTradeByTid(user, tid);
                    syncTradeBusiness.syncTradeByTid(user, tid, result, true);
                } catch (Exception exception) {
                    failResult.put(tid, exception.getMessage());
                }
            }
        }
    }

    private ProgressData buildResult(User user, Integer totalNum, Map<String, String> failResult){
        ProgressData result = new ProgressData();
        result.setProgress(PROGRESS_FINISH);
        result.setCountAll(totalNum);
        if (MapUtils.isNotEmpty(failResult)) {
            logger.error(LogHelper.buildUserLog(user, String.format("批量指定下载发生错误，失败信息:%s", JSONObject.toJSONString(failResult))));
            List<String> errorMsg = new ArrayList<>(failResult.size());
            StringBuilder str = new StringBuilder();
            for (Map.Entry<String, String> entry : failResult.entrySet()) {
                str.append(entry.getKey()).append(":").append(entry.getValue());
                errorMsg.add(str.toString());
                str.setLength(0);
            }

            long errNum = Integer.valueOf(failResult.size()).longValue();
            result.setErrorNum(errNum);
            result.setSucNum(totalNum.longValue() - errNum);
            result.setErrorMsg(errorMsg);
        } else {
            result.setSucNum(totalNum.longValue());
            result.setErrorNum(0L);
        }
        return result;
    }
}

package com.raycloud.dmj.services.sensitive.platform;

import com.google.common.base.Strings;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.filter.support.DecodeFieldFilter;
import com.raycloud.dmj.services.sensitive.AbstractPlatformTradeSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 系统加密的手工单 尝试系统解密
 */
@Service
public class CommonPlatformTradeSecurityService extends AbstractPlatformTradeSecurityService {

    private final DecodeFieldFilter decodeFieldFilter;

    @Autowired
    public CommonPlatformTradeSecurityService(DecodeFieldFilter decodeFieldFilter) {
        this.decodeFieldFilter = decodeFieldFilter;
    }

    @Override
    public void doSensitiveTrades(Staff staff, List<Trade> trades) {
        decodeFieldFilter.filterTrades(staff, trades);
        // 如果是fxg的系统加密的手工单，将mobile设置为buyerNick
        trades.forEach(trade -> {
            if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_FXG) && !Strings.isNullOrEmpty(trade.getReceiverMobile())) {
                trade.setBuyerNick(trade.getReceiverMobile());
            }
        });
    }

    @Override
    public List<Trade> filterTrades(Staff staff, List<Trade> trades) {
        return trades;
    }
}

package com.raycloud.dmj.services.trades.support.utils;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.common.SimpleTrade;
import com.raycloud.dmj.business.tag.util.SpiderTagUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.TradeTagRule;
import com.raycloud.dmj.domain.trade.spider.SpiderUtils;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.utils.spider.SpiderDifferentAddressUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * description:
 * date: 2020/12/22
 *
 * @author: y<PERSON><PERSON><PERSON>
 */
public class DifferentAddressUtils {

    private final static List<String> SPECIAL_CITY = Lists.newArrayList(("上海,北京,天津,重庆".split(",")));


    /**
     * 解析没有省的详细地址
     * <AUTHOR>
     * @param receiverAddress
     * @return
     */
    public static List<Map<String,String>> addressResolution(String receiverAddress){
        //        String regex="(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        String regex="(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m= Pattern.compile(regex).matcher(receiverAddress);
        String province=null,city=null,county=null,town=null,village=null;
        List<Map<String,String>> table=new ArrayList<Map<String,String>>();
        Map<String,String> row=null;
        while(m.find()){
            row=new LinkedHashMap<String,String>();
            city=m.group("city");
            row.put("city", city==null?"":city.trim());
            county=m.group("county");
            row.put("county", county==null?"":county.trim());
            town=m.group("town");
            row.put("town", town==null?"":town.trim());
            village=m.group("village");
            row.put("village", village==null?"":village.trim());
            table.add(row);
        }
        return table;
    }


    /**
     * 标签匹配时校验详细地址里的省市是否与外部一致
     * 采取的时内存里直接挨个比较的方式
     */
    public static boolean validateDifferentAddress(SimpleTrade simpleTrade) {
        //从diamond配置中获取省市列表
        Map<String,List<String>> provinceMap = TradeDiamondUtils.getAddressForMatchTag();
        String province = null;
        String city = null;
        List<String> possibleProvince = findAllInString(provinceMap.keySet(),simpleTrade.getReceiverAddress());
        //没解析出省份的不匹配
        if(CollectionUtils.isEmpty(possibleProvince)){
            //对详细地址尝试一次解析，看能不能解析出市，和订单上的市进行匹配
            List<Map<String,String>> table=  addressResolution(simpleTrade.getReceiverAddress());
            if (table.size()>0){
                String parseCity = table.get(0).get("city");
                if (StringUtils.isNotEmpty(parseCity) && StringUtils.isNotEmpty(simpleTrade.getReceiverCity())
                        && !parseCity.contains(simpleTrade.getReceiverCity()) && !simpleTrade.getReceiverCity().contains(parseCity)){
                    return true;
                }
                return false;
            }else {
                return false;
            }
        }
        int matchCount = 0;
        for(String str : possibleProvince) {
            String temp = findInString(provinceMap.get(str), simpleTrade.getReceiverAddress());
            if(temp!=null){
                province = str;
                city = temp;
                matchCount ++;
            }
        }
        //没解析出市的不匹配
        if(city==null){
            return false;
        }
        //解析出俩个省市的匹配
        if(matchCount > 1){
            return true;
        }
        //直辖市的单独判断
        if(SPECIAL_CITY.contains(province)) {
            return validateSpecialCity(simpleTrade,province,city,provinceMap.get(province));
        }
        return validateCommon(simpleTrade,province,city);
    }

    /**
     * 标签匹配时校验详细地址里的省市是否与外部一致
     * 采取的时内存里直接挨个比较的方式
     */
    public static boolean validateDifferentAddress(SimpleTrade simpleTrade, String companyId, String ruleId) {
        //从diamond配置中获取省市列表
        Map<String,List<String>> provinceMap = TradeDiamondUtils.getAddressForMatchTag();
        String province = null;
        String city = null;
        List<String> possibleProvince = findAllInString(provinceMap.keySet(),simpleTrade.getReceiverAddress());
        //没解析出省份的不匹配
        if(CollectionUtils.isEmpty(possibleProvince)){
            //对详细地址尝试一次解析，看能不能解析出市，和订单上的市进行匹配
            List<Map<String,String>> table=  addressResolution(simpleTrade.getReceiverAddress());
            if (table.size()>0){
                String parseCity = table.get(0).get("city");
                if (StringUtils.isNotEmpty(parseCity) && StringUtils.isNotEmpty(simpleTrade.getReceiverCity())
                        && !parseCity.contains(simpleTrade.getReceiverCity()) && !simpleTrade.getReceiverCity().contains(parseCity)){
                    String reason = "双地址匹配成功(详细地址中未解析出省，通过市匹配成功)，"+"订单中市区地址为：" + simpleTrade.getReceiverCity() +"，详细地址中解析出的市区地址为：" + parseCity;
                    SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason );
                    return true;
                }
                String reason = "双地址匹配失败(详细地址中未解析出省，通过市匹配失败)，"+"订单中市区地址为：" + simpleTrade.getReceiverCity() +"，详细地址中解析出的市区地址为：" + parseCity;
                SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason );
                return false;
            }else {
                String reason = "双地址匹配失败"+"(详细地址中未解析出省或者市)";
                SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason );
                return false;
            }
        }
        int matchCount = 0;
        for (String str : possibleProvince) {
            String temp = findInString(provinceMap.get(str), simpleTrade.getReceiverAddress());
            if (temp != null) {
                province = str;
                city = temp;
                matchCount++;
            }
        }
        //没解析出市的不匹配
        if (city == null) {
            String reason = "双地址匹配失败，订单详细地址中未解析出市";
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason);
            return false;
        }
        //解析出俩个省市的匹配
        if (matchCount > 1) {
            String reason = "双地址匹配成功，订单详细地址中解析出两个省市";
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason);
            return true;
        }
        //直辖市的单独判断
        if (SPECIAL_CITY.contains(province)) {
            return validateSpecialCity(companyId, ruleId, simpleTrade, province, city, provinceMap.get(province));
        }
        return validateCommon(simpleTrade, province, city, companyId, ruleId);
    }


    /**
     * 标签匹配时校验详细地址里的省市是否与外部一致
     * 采取的时内存里直接挨个比较的方式
     */
    public static boolean validateDifferentAddress(SimpleTrade simpleTrade, String doubleAddressKeywords) {
        //双地址关键字是否配置，没有配置的话，不成功
        if (StringUtils.isEmpty(doubleAddressKeywords)) {
            String reason = "双地址匹配失败，【双地址关键字未配置】";
            SpiderDifferentAddressUtils.addResult(simpleTrade, false, reason );
            return false;
        }
        //从diamond配置中获取省市列表
        Map<String,List<String>> provinceMap = TradeDiamondUtils.getAddressForMatchTag();
        //解析地址关键字,取出订单中包含关键字的地址。
        doubleAddressKeywords = doubleAddressKeywords.trim().replace("，",",");
        List<String> doubleAddressKeywordList = Arrays.stream(doubleAddressKeywords.split(",")).map(item-> item = item.trim()).collect(Collectors.toList());

        //详细地址中包含的关键字
        List<String> receiverAddressContainKeywords = doubleAddressKeywordList.stream().filter(item -> simpleTrade.getReceiverAddress().contains(item)).collect(Collectors.toList());
        String reason;
        if (CollectionUtils.isEmpty(receiverAddressContainKeywords)) {
            reason = "双地址匹配失败，【订单详细地址未匹配双地址关键字】，地址关键字为：" + doubleAddressKeywords;
            SpiderDifferentAddressUtils.addResult(simpleTrade, false, reason );
            return false;
        }

        //通过关键字解析出省份（关键字可能是省也可能是市，最后只取省）
        List<String> analysisProvinces = analysisProvince(receiverAddressContainKeywords,provinceMap);
        //详细地址中未包含关键字
        if (CollectionUtils.isEmpty(analysisProvinces)) {
            reason = "双地址匹配失败，【双地址省份匹配失败】，未通过关键字解析出详细地址中的省份：" + analysisProvinces + "，订单省份为：" + simpleTrade.getReceiverState();
            SpiderDifferentAddressUtils.addResult(simpleTrade, false, reason );
            return Boolean.FALSE;
        }

        //通过关键词解析出来的省份与订单的省份进行比较
        for (String analysisProvince: analysisProvinces){
            if (!analysisProvince.contains(simpleTrade.getReceiverState()) && !simpleTrade.getReceiverState().contains(analysisProvince)) {
                reason = "双地址匹配成功，【双地址省份不一致】，通过关键字与详细地址解析出来的省份为：" + analysisProvince + "，订单省份为：" + simpleTrade.getReceiverState();
                SpiderDifferentAddressUtils.addResult(simpleTrade, false, reason );
                return Boolean.TRUE;
            }

        }
        //匹配成功
        reason = "双地址匹配失败，【双地址省份匹配失败】，通过关键字与详细地址解析出来的省份为：" + analysisProvinces + "，订单省份为：" + simpleTrade.getReceiverState();
        SpiderDifferentAddressUtils.addResult(simpleTrade, true, reason );
        return Boolean.FALSE;
    }

    private static List<String> analysisProvince(List<String> receiverAddressContainKeywords, Map<String, List<String>> provinceMap) {
        if (CollectionUtils.isEmpty(receiverAddressContainKeywords) || CollectionUtils.isEmpty(provinceMap)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (String str : receiverAddressContainKeywords) {
            if (provinceMap.containsKey(str)) {
                result.add(str);
            } else {
                Set<String> set = provinceMap.keySet();
                for (String provinceKey : set) {
                    List<String> cityList = provinceMap.get(provinceKey);
                    if (cityList.contains(str)){
                        result.add(provinceKey);
                    }
                }
            }
        }
        return result;
    }



    private static boolean validateCommon(SimpleTrade simpleTrade, String province, String city, String companyId, String ruleId) {
        //不含省的直接不匹配
        if(StringUtils.isEmpty(simpleTrade.getReceiverState())){
            String reason = "双地址匹配失败，订单地址中不包含省";
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason);
            return false;
        }
        //含有省且省不一致的匹配
        if (!simpleTrade.getReceiverState().contains(province)) {
            String reason = "双地址匹配成功（省份不一致），订单地址中省份为：" + simpleTrade.getReceiverState() + "详细地址中解析的省份为：" + province ;
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason);
            return true;
        }
        //不含市的不匹配
        if(StringUtils.isEmpty(simpleTrade.getReceiverCity())){
            String reason = "双地址匹配失败，订单地址中不包含市";
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason);
            return false;
        }
        //含有市且市不一致的匹配
        if(!simpleTrade.getReceiverCity().contains(city)){
            String reason = "双地址匹配成功（市区不一致），订单地址中市区为：" + simpleTrade.getReceiverCity() + "详细地址中解析的市区为：" + city ;
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason);
            return true;
        }
        return false;
    }

    private static boolean validateCommon(SimpleTrade simpleTrade, String province, String city) {
        //不含省的直接不匹配
        if(StringUtils.isEmpty(simpleTrade.getReceiverState())){
            return false;
        }
        //含有省且省不一致的匹配
        if(!simpleTrade.getReceiverState().contains(province)){
            return true;
        }
        //不含市的不匹配
        if(StringUtils.isEmpty(simpleTrade.getReceiverCity())){
            return false;
        }
        //含有市且市不一致的匹配
        if(!simpleTrade.getReceiverCity().contains(city)){
            return true;
        }
        return false;
    }

    /**
     * 验证直辖市的区字段是否为双地址
     * @return 是否双地址
     */
    private static boolean validateSpecialCity(SimpleTrade simpleTrade, String province, String city, List<String> cityList){
        if(StringUtils.isEmpty(simpleTrade.getReceiverState())){
            return false;
        }
        if(!simpleTrade.getReceiverState().contains(province)){
            return true;
        }
        if(StringUtils.isEmpty(simpleTrade.getReceiverCity()) && StringUtils.isEmpty(simpleTrade.getReceiverDistrict())){
            return false;
        }
        //直辖市的区可能在市字段也可能在区字段也可能没写
        if(StringUtils.contains(simpleTrade.getReceiverCity(),city) //区写在市字段里
                || StringUtils.contains(simpleTrade.getReceiverDistrict(),city) //区写在区字段里
                //市字段区字段都不含其他区（直辖市的市区取值很乱没有标准，如果仅有市没有区，认为它依旧不匹配）
                || (findInString(cityList,simpleTrade.getReceiverDistrict())==null && findInString(cityList,simpleTrade.getReceiverCity())==null)
        ){
            return false;
        }
        return true;
    }



    /**
     * 验证直辖市的区字段是否为双地址
     * @return 是否双地址
     */
    private static boolean validateSpecialCity(String companyId, String ruleId, SimpleTrade simpleTrade, String province, String city, List<String> cityList){
        if(StringUtils.isEmpty(simpleTrade.getReceiverState())){
            String reason = "双地址匹配失败，订单中直辖市市区为空";
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason );
            return false;
        }
        if(!simpleTrade.getReceiverState().contains(province)){
            String reason = "双地址匹配成功，订单中直辖市为：" + simpleTrade.getReceiverState() + "，详细地址中省市为：" + "province" ;
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason );
            return true;
        }
        if(StringUtils.isEmpty(simpleTrade.getReceiverCity()) && StringUtils.isEmpty(simpleTrade.getReceiverDistrict())){
            String reason = "双地址匹配失败，订单中直辖市的市区为空：" ;
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason );
            return false;
        }
        //直辖市的区可能在市字段也可能在区字段也可能没写
        if(StringUtils.contains(simpleTrade.getReceiverCity(),city) //区写在市字段里
                || StringUtils.contains(simpleTrade.getReceiverDistrict(),city) //区写在区字段里
                //市字段区字段都不含其他区（直辖市的市区取值很乱没有标准，如果仅有市没有区，认为它依旧不匹配）
                || (findInString(cityList,simpleTrade.getReceiverDistrict())==null && findInString(cityList,simpleTrade.getReceiverCity())==null)
        ){
            String reason = "双地址匹配失败，订单中直辖市仅有市没有区：" ;
            SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, false, reason );
            return false;
        }
        String reason = "直辖市默认匹配成功：" ;
        SpiderDifferentAddressUtils.addResult(companyId, simpleTrade, ruleId, true, reason );
        return true;
    }



    /**
     * string中是否包含集合中的元素，包含则返回
     */
    private static String findInString(Collection<String> collection, String receiverAddress) {
        if(CollectionUtils.isEmpty(collection) || StringUtils.isBlank(receiverAddress)){
            return null;
        }
        for(String str : collection){
            if(receiverAddress.contains(str)){
                return str;
            }
        }
        return null;
    }

    /**
     * 获取所有包含的
     */
    private static List<String> findAllInString(Collection<String> collection, String receiverAddress) {
        if(CollectionUtils.isEmpty(collection) || StringUtils.isBlank(receiverAddress)){
            return null;
        }
        List<String> result = new ArrayList<>();
        for(String str : collection){
            if(receiverAddress.contains(str)){
                result.add(str);
            }
        }
        return result;
    }


}

package com.raycloud.dmj.services.trades.support.utils;

import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.gift.bo.ItemParamBO;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.user.IShopService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ConfigTraceUtils {

    private static final String EMPTY_WORD = "";
    private static final String SPLIT_CHAR = "; ";
    private static final String JOIN_CHAR = ": ";
    private static final String POINT_CHAR = " -> ";

    @SuppressWarnings("unchecked")
    public static String buildConfigUpdateLog(Map<String,Object> newConfig, Map<String,Object> originConfig){
        if(MapUtils.isEmpty(originConfig)){
           return buildConfigSaveLog(newConfig);
        }
        if(MapUtils.isEmpty(newConfig)){
            return EMPTY_WORD;
        }
        StringBuilder builder = new StringBuilder();
        Set<String> keySet = new LinkedHashSet<>(newConfig.keySet());
        keySet.addAll(originConfig.keySet());
        for(String key : keySet){
            Object oldValue = Optional.ofNullable(originConfig.get(key)).orElse(EMPTY_WORD);
            Object newValue = Optional.ofNullable(newConfig.get(key)).orElse(EMPTY_WORD);
            if(oldValue instanceof Map && newValue instanceof Map ) {
                MapDifference<String,String> difference = Maps.difference((Map<String,String>)oldValue, (Map<String,String>)newValue);
                if(!difference.areEqual()){
                    builder.append(key).append(JOIN_CHAR).append("{");
                    if(MapUtils.isNotEmpty(difference.entriesOnlyOnLeft())){
                        builder.append("删除:");
                        for(Object value : difference.entriesOnlyOnLeft().values()){
                            builder.append("【").append(value).append("】");
                        }
                        builder.append(SPLIT_CHAR);
                    }
                    if(MapUtils.isNotEmpty(difference.entriesOnlyOnRight())){
                        builder.append("新建:");
                        for(Object value : difference.entriesOnlyOnRight().values()){
                            builder.append("【").append(value).append("】");
                        }
                        builder.append(SPLIT_CHAR);
                    }
                    if(MapUtils.isNotEmpty(difference.entriesDiffering())){
                        builder.append("修改:");
                        for(String k : difference.entriesDiffering().keySet()){
                            builder.append("【").append(((Map<String,String>) newValue).get(k)).append("】");
                        }
                        builder.append(SPLIT_CHAR);
                    }
                    builder.append("}").append(SPLIT_CHAR);
                }
            }else if (!oldValue.equals(newValue)){
                //配置名: 老值 -> 新值 ;
                builder.append(key).append(JOIN_CHAR).append(oldValue).append(POINT_CHAR).append(newValue).append(SPLIT_CHAR);
            }
        }
        return builder.toString();
    }

    @SuppressWarnings("unchecked")
    public static String buildConfigSaveLog(Map<String, Object> newConfig) {
        if(MapUtils.isEmpty(newConfig)){
            return EMPTY_WORD;
        }
        StringBuilder builder = new StringBuilder();
        for(Map.Entry<String,Object> entry : newConfig.entrySet()){
            if(entry.getValue() instanceof String) {
                builder.append(entry.getKey()).append(JOIN_CHAR).append(entry.getValue()).append(SPLIT_CHAR);
            }else if(entry.getValue() instanceof Map){
                builder.append(entry.getKey()).append(JOIN_CHAR).append("{");
                for(String value : ((Map<String,String>) entry.getValue()).values()){
                    builder.append("【").append(value).append("】");
                }
                builder.append("}").append(SPLIT_CHAR);
            }
        }
        return builder.toString();
    }

    /**
     * 根据系统item的id，系统sku的id查询商品的商家编码
     * @param sysItemIds 如果是纯商品根据系统item的id查
     * @param sysSkuIds 如果是规格商品根据系统sku的id查
     * @return 逗号拼接的商家编码字符串
     */
    public static String queryItemOutIds(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, IItemServiceWrapper itemServiceWrapper) {
        StringBuilder sb = new StringBuilder();

        if (!sysItemIds.isEmpty()) {
            List<DmjItem> items = itemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId, outerId");
            for (DmjItem item : items) {
                sb.append(item.getOuterId()).append(",");
            }
        }
        if (!sysSkuIds.isEmpty()) {
            List<DmjSku> skuList = itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId, sysSkuId, outerId");
            for (DmjSku sku : skuList) {
                sb.append(sku.getOuterId()).append(",");
            }
        }
        if (sb.length() > 0) {
            return sb.deleteCharAt(sb.length() - 1).toString();
        }
        return EMPTY_WORD;
    }

    /**
     * 根据店铺id查店铺名称,分销店铺也用这个方法
     */
    public static String queryShopNames(Staff staff, List<Long> userIds, IShopService shopService){
        if(CollectionUtils.isEmpty(userIds)){
            return EMPTY_WORD;
        }
        List<Shop> shopList = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
        if(CollectionUtils.isEmpty(shopList)){
            return EMPTY_WORD;
        }
        return shopList.stream().map(o-> StringUtils.isNotEmpty(o.getShortTitle()) ? o.getShortTitle() : o.getTitle()).collect(Collectors.joining(","));
    }

    /**
     * 根据公司id查询公司名称,查供分销公司直接用这个方法
     */
    public static String queryCompanyNames(Staff staff, List<Long> companyIds, ICompanyService companyService){
        if(CollectionUtils.isEmpty(companyIds)){
            return EMPTY_WORD;
        }
        List<Company> companyList = companyService.querComapanyListByIds(companyIds.toArray(new Long[0]));
        if(CollectionUtils.isEmpty(companyList)){
            return EMPTY_WORD;
        }
        return companyList.stream().map(Company::getName).collect(Collectors.joining(","));
    }


    /**
     * 根据系统item的id，系统sku的id查询商品的商家编码
     * @param sysItemIds 如果是纯商品根据系统item的id查
     * @param sysSkuIds 如果是规格商品根据系统sku的id查
     * @return 逗号拼接的商家编码字符串
     */
    public static ItemParamBO queryMapItemOutIds(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, IItemServiceWrapper itemServiceWrapper) {

        ItemParamBO itemParamBO=new ItemParamBO();
        if (CollectionUtils.isNotEmpty(sysItemIds)) {
            // list 去重
            List<Long> uniqueSysItemIds = sysItemIds.stream().distinct().collect(Collectors.toList());
            List<DmjItem> items = itemServiceWrapper.queryBySysItemIds(staff, uniqueSysItemIds, "sysItemId, outerId");
            Map<Long,String> itemMap=new HashMap<>();
            for (DmjItem item : items) {
                itemMap.put(item.getSysItemId(),item.getOuterId());
            }
            itemParamBO.setItemMap(itemMap);
        }
        if (CollectionUtils.isNotEmpty(sysSkuIds)) {
            // list 去重
            List<Long> uniqueSysSkuIds = sysSkuIds.stream().distinct().collect(Collectors.toList());
            List<DmjSku> skuList = itemServiceWrapper.queryBySysSkuIds(staff, uniqueSysSkuIds, "sysItemId, sysSkuId, outerId");
            Map<Long,String> skuItemMap=new HashMap<>();
            for (DmjSku sku : skuList) {
                skuItemMap.put(sku.getSysSkuId(),sku.getOuterId());
            }
            itemParamBO.setSkuItemMap(skuItemMap);
        }
        return itemParamBO;
    }

}
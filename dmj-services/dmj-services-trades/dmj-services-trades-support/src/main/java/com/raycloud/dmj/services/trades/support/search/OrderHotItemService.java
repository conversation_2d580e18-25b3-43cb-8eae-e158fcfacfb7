package com.raycloud.dmj.services.trades.support.search;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.raycloud.dmj.Buffers;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.OrderHotItemDao;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2025/3/21 14:58
 */
@Service
public class OrderHotItemService {

    private final Logger logger = Logger.getLogger(getClass());

    @Resource
    TbOrderDAO tbOrderDAO;

    @Resource
    OrderHotItemDao orderHotItemDao;

    @Resource
    IEventCenter eventCenter;

    @Resource
    IBufferService bufferService;

    @Resource
    IdWorkerService guidService;

    Cache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .build();

    final static String CACHE_MARK = "1";

    public void write(Staff staff, List<Long> sids) {
        LogKit.took(() -> {
            try {
                if (staff == null || CollectionUtils.isEmpty(sids)) {
                    return null;
                }
                if (openOrderHotItem(staff)) {
                    Optional.ofNullable(queryOrders(staff, sids))
                            .filter(list -> !list.isEmpty())
                            .map(orders -> OrderUtils.toFullOrderList(orders, false))
                            .ifPresent(orders -> {
                                List<OrderHotItem> inserts = orders.stream()
                                        .map(o -> OrderHotItem.valueOf(o, guidService.nextId()))
                                        .peek((item) -> item.setDigest(DigestUtils.md5Hex(OrderHotItem.hash(item))))
                                        .filter(item -> StringUtils.isEmpty(cache.getIfPresent(item.getDigest())))
                                        .collect(Collectors.toList());
                                orderHotItemDao.batchInsert(staff, inserts);
                                Optional.of(inserts).ifPresent(items -> items.forEach(is -> cache.put(is.getDigest(), CACHE_MARK)));
                            });
                }
            } catch (Exception ex) {
                Logs.error(LogHelper.buildErrorLog(staff, ex, "更新商品热点数据失败!!!" + "sids:" + sids), ex);
            }
            return null;
        }, staff, 3000, LogKit.getFullMethodName(this, "write"), logger);
    }

    public void asyncWrite(Staff staff, List<Trade> insertTrades, List<Trade> updateTrades, List<Order> insertOrders, List<Order> updateOrders) {
        if ((CollectionUtils.isEmpty(insertTrades)
                && CollectionUtils.isEmpty(insertOrders)
                && CollectionUtils.isEmpty(updateOrders))) {
            return;
        }
        if (!openOrderHotItem(staff)) {
            return;
        }
        Set<Long> sids = new HashSet<>();
        Optional.ofNullable(insertTrades).ifPresent(trades -> sids.addAll(TradeUtils.toSidList(trades)));
        Optional.ofNullable(insertOrders).ifPresent(orders -> sids.addAll(OrderUtils.toSidList(orders)));
        Optional.ofNullable(updateOrders).ifPresent(orders -> {
            List<Order> filterOrders = orders.stream().filter(OrderHotItemService::itemChanged).collect(Collectors.toList());
            Optional.of(filterOrders).ifPresent(ods -> {
                List<Long> oids = ods.stream()
                        .filter(od -> od.getSid() == null && od.getId() != null)
                        .map(Order::getId)
                        .collect(Collectors.toList());
                ods.stream()
                        .filter(od -> od.getSid() != null)
                        .map(Order::getSid)
                        .forEach(sids::add);
                if (!oids.isEmpty()) {
                    List<TbOrder> tbOrders = tbOrderDAO.queryByKeys(staff, false, false, "order_" + staff.getDbInfo().getOrderDbNo(), "sid", "id", oids.toArray(new Long[0]));
                    Optional.ofNullable(tbOrders).ifPresent(tbs -> sids.addAll(OrderUtils.toSidList(tbs)));
                }
            });
        });
        if (sids.isEmpty()) {
            return;
        }
        bufferService.buffer(Buffers.build(staff, TradeEvents.ORDER_HOT_ITEM_SYNC), new ArrayList<>(sids));
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.ORDER_HOT_ITEM_SYNC).setArgs(new Object[]{staff}), null);
    }

    public static boolean itemChanged(Order o) {
        return StringUtils.isNotEmpty(o.getTitle()) ||
                StringUtils.isNotEmpty(o.getNumIid()) ||
                StringUtils.isNotEmpty(o.getSkuId()) ||
                StringUtils.isNotEmpty(o.getOuterIid()) ||
                StringUtils.isNotEmpty(o.getOuterSkuId()) ||
                StringUtils.isNotEmpty(o.getSkuPropertiesName()) ||
                o.getItemSysId() != null ||
                o.getSkuSysId() != null ||
                StringUtils.isNotEmpty(o.getSysOuterId()) ||
                StringUtils.isNotEmpty(o.getSysSkuPropertiesName()) ||
                StringUtils.isNotEmpty(o.getSysTitle()) ||
                StringUtils.isNotEmpty(o.getShortTitle()) ||
                StringUtils.isNotEmpty(o.getSysSkuPropertiesAlias()) ||
                StringUtils.isNotEmpty(o.getSysItemRemark()) ||
                StringUtils.isNotEmpty(o.getSysSkuRemark()) ||
                o.getIsVirtual() != null ||
                o.getType() != null ||
                o.getGiftNum() != null;
    }

    public boolean openOrderHotItem(Staff staff) {
        return ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getQueryByOrderHotItemCompanyIds(), staff.getCompanyId());
    }

    List<Order> queryOrders(Staff staff, List<Long> sids) {
        return OrderUtils.toTree(tbOrderDAO.queryByKeys(staff, false, true, "order_" + staff.getDbInfo().getOrderDbNo(), "*", "belong_sid", sids.toArray(new Long[0])));
    }

    public List<OrderHotItem> queryHotItem(Staff staff, Query q) {
        if (staff == null || q == null || StringUtils.isEmpty(q.getQ())) {
            return Collections.EMPTY_LIST;
        }
        return orderHotItemDao.getJdbcTemplate(staff).query("select o.item_sys_id,o.sku_sys_id from order_hot_item_" + staff.getDbInfo().getOrderDbNo() + " o" + " where company_id = " + staff.getCompanyId() + " " + q.getQ(), q.getArgs().toArray(), new BeanPropertyRowMapper<>(OrderHotItem.class));
    }
}

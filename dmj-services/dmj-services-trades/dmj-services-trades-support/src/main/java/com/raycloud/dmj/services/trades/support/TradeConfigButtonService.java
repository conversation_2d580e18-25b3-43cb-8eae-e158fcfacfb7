package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.TradeConfigButtonDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeConfigButton;
import com.raycloud.dmj.services.trades.ITradeConfigButtonService;
import com.raycloud.dmj.services.utils.TraceConfigButtonConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @program: erp-core
 * @description: 按钮配置
 * Created by luzhehen
 * Created on 2022/8/4 2:11 下午
 * Copyright(c)2022  版权所有
 */
@Service("tradeConfigButtonService")
public class TradeConfigButtonService implements ITradeConfigButtonService{

    @Resource
    private TradeConfigButtonDao configButtonDao;

    @Override
    public void saveTradeConfigureButton(Staff staff, TradeConfigButton configButton) {
        if (StringUtils.isEmpty(configButton.getConfigVal())) {
            throw new NullPointerException("按钮配置内容不能为空！");
        }
        if (null == configButton.getConfigType()) {
            throw new NullPointerException("按钮类型不能为空！");
        }
        //不合法按钮类型
        if (!TraceConfigButtonConstants.ConfigButton.vagueDoesItExist(configButton.getConfigType())) {
            throw new IllegalArgumentException("按钮类型不合法！");
        }
        configButtonDao.insert(staff, configButton);
    }

    @Override
    public TradeConfigButton getTradeConfigureButton(Staff staff, Long configType) {
        if (null == configType) {
            throw new NullPointerException("按钮类型不能为空！");
        }
        //不合法按钮类型
        if (!TraceConfigButtonConstants.ConfigButton.vagueDoesItExist(configType)) {
            throw new IllegalArgumentException("按钮类型不合法！");
        }
        TradeConfigButton configButton = configButtonDao.get(staff, configType);
        if (configButton == null) {
            configButton = new TradeConfigButton();
        }
        return configButton;
    }

}

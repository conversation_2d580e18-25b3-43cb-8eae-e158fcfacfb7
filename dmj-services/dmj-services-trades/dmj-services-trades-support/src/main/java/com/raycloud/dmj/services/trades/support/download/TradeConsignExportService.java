package com.raycloud.dmj.services.trades.support.download;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.trades.ConsignRecord;
import com.raycloud.dmj.domain.trades.ConsignRecordQueryParams;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.trades.IConsignRecordService;
import com.raycloud.dmj.services.user.IShopService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

public class TradeConsignExportService implements IDownloadCenterCallback {

    private Staff staff;
    ConsignRecordQueryParams params;
    private IConsignRecordService consignRecordService;

    protected IShopService shopService;

    public TradeConsignExportService(Staff staff, ConsignRecordQueryParams params, IConsignRecordService consignRecordService,IShopService shopService) {
        this.staff = staff;
        this.params = params;
        this.consignRecordService = consignRecordService;
        this.shopService = shopService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult();
        result.setFlag(true);
        params.setPage(downloadParam.getPage());
        String[][] tradeConsign = tradeConsignExport(staff, params);
        result.setData(tradeConsign);
        return result;
    }

    private String[][] tradeConsignExport(Staff staff, ConsignRecordQueryParams params) {
        List<ConsignRecord> records = consignRecordService.list(staff, params);

        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        Set<Long> userIds = new HashSet<Long>();
        for(ConsignRecord record:records){
            Long userId = record.getUserId();
            if(userId !=null && userId>0){
                userIds.add(userId);
            }
        }
        Map<Long, Shop> userId2Shop = getShops(staff, shopService, userIds);
        List<String[]> resultList = new ArrayList<>();
        for (ConsignRecord record:records){
            List<String> row = Lists.newArrayListWithCapacity(11);
            row.add(String.valueOf(record.getSid()));
            row.add(record.getTid());
            row.add(DateUtils.format(record.getCreated(),"yyyy-MM-dd HH:mm:ss"));
            row.add(DateUtils.format(record.getTradePay(),"yyyy-MM-dd HH:mm:ss"));
            row.add(DateUtils.format(record.getUploadTime(),"yyyy-MM-dd HH:mm:ss"));
            row.add(record.getWarehouseName());
            row.add(record.getTemplateName());
            row.add(record.getOutSid());
            row.add(SendType.getDescByType(record.getConsignType()));
            row.add(record.getIsError().equals(1)?"上传失败":"上传中");
            row.add(record.getErrorDesc());
            Shop shop = userId2Shop.get(record.getUserId());
            if(shop!=null){
                row.add(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle()));
            }
            resultList.add(row.toArray(new String[12]));
        }
        return resultList.toArray(new String[resultList.size()][]);
    }

    private static Map<Long, Shop> getShops(Staff staff, IShopService shopService, Set<Long> userIds){
        if(org.apache.commons.collections.CollectionUtils.isEmpty(userIds)){
            return new HashMap<Long, Shop>();
        }
        Long[] userIdArr = new Long[userIds.size()];
        userIds.toArray(userIdArr);
        List<Shop> shops = shopService.queryByUserIds(staff,userIdArr);

        Map<Long, Shop> shopMap = new HashMap<Long, Shop>();
        for(Shop shop:shops){
            shopMap.put(shop.getUserId(),shop);
        }
        return shopMap;
    }

}

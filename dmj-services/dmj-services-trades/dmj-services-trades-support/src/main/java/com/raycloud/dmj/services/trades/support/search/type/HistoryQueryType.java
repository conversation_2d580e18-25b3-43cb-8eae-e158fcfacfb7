package com.raycloud.dmj.services.trades.support.search.type;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.type.TradeTypeNewEnum;
import com.raycloud.dmj.domain.trades.utils.TradePromiseUtils;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;


/**
 * @Description <pre>
 *
 *  历史代码 新增的type不允许在这里添加 需要新定义ITradeQueryType实现
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-19
 */
@Component
public class HistoryQueryType implements ITradeQueryType{

    @Override
    public TradeTypeEnum getType() {
        return null;
    }

    @Override
    public String getSql(Staff staff, Integer type,Query q, Long queryId) {
        return getTypeQuery(staff, q, type, queryId);
    }

    private String getTypeQuery(Staff staff, Query q, Integer type, Long queryId) {
        //如果是新枚举 会走 buildTradeTypeNewSql,这里不需要处理
        TradeTypeNewEnum typeNewEnum = TradeTypeNewEnum.getById(type);
        if (typeNewEnum != null) {
            return "";
        }

        if (type - 1 == 0) {//货到付款
            return "t.type IN('cod', 'jd-1')";
        }
        if (type - 3 == 0) {//平台订单
            return "t.source <> 'sys'";
        }
        if (type - 4 == 0) {//系统订单
            return "t.source = 'sys'";
        }
        if (type - 5 == 0) {// TODO 分销订单
            return "t.fx_company_id > 0";
        }
        if (type - 6 == 0) {//预售订单
            if (queryId != null) {
                if (queryId == QUERY_COMMON || queryId == QUERY_3MONTH_AGO || queryId == QUERY_BOXING) {//订单查询，三个月以前订单查询，装箱清单查询预售订单，包含预售锁定订单和预售解锁订单
                    return " (t.is_presell IN(1, 2) OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.is_presell IN(1, 2))))";
                } else if (queryId == QUERY_PRESELL) {//预售订单查询只包含预售锁定订单
                    return " (t.is_presell=1 OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.is_presell=1 )))";
                } else {//其它界面订单查询只包含预售解锁订单
                    return " (t.is_presell=2 OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.is_presell=2 )))";
                }
            }
        }
        if (type - 7 == 0) {//合单
            return " t.merge_type IN (" + TradeMergeEnum.MERGE_AUTO.getDbType() + ", " + TradeMergeEnum.MERGE_MANUAL.getDbType() + ", " +TradeMergeEnum.MERGE_NULTIL.getDbType()+ ")";
        }
        if (type - 8 == 0) {//拆单
            return " t.split_type = " + TradeSplitEnum.SPLIT_SKU.getDbType();
        }

        if (type - 9 == 0) {//加急订单
            return " t.is_urgent = 1";
        }
        if (type - 10 == 0) {//空单
            return " t.scalping = 1";
        }
        if (type - 11 == 0) {//有合单标记的订单
            return " t.check_manual_merge_count = 1";
        }
        if (type - 12 == 0) {//门店订单
            return " t.is_store = 1";
        }
        if (type - 13 == 0) {//换货订单
            return " t.type = 'changeitem'";
        }
        if (type - 14 == 0) {//补发订单
            return " t.type = 'reissue'";
        }
        if (type - 15 == 0) {//跨境订单
            return " t.sub_source LIKE CONCAT('kj_', '%')";
        }
        if (type - 16 == 0) {//海外仓订单
            return " t.type = 'overseas_warehouse'";
        }
        if (type - 17 == 0) {//lazada订单
            return " t.type = 'lazada'";
        }
        if (type - 18 == 0) {//报损出库单
            return " t.type like 'trade_out_1%'";
        }
        if (type - 19 == 0) {//领用出库单
            return " t.type like 'trade_out_2%'";
        }
        if (type - 20 == 0) {//调整出库单
            return " t.type like 'trade_out_3%'";
        }
        if (type - 21 == 0) {//客户订单
            if (queryId != null && (SystemTradeQueryParamsContext.QUERY_WAIT_OUTSTOCK == queryId || SystemTradeQueryParamsContext.QUERY_FINISH_OUTSTOCK == queryId)) {
                return " t.type like '%_customer'";
            } else if (queryId != null && SystemTradeQueryParamsContext.QUERY_COMMON == queryId) {
                return " t.type like '%customer'";
            } else {
                return " t.type = 'customer'";
            }
        }
        //3pl查询
        if (type - 22 == 0) {
            return " t.is_tmall_delivery = 1";
        }

        if (type - 23 == 0) {//平台预售
            return " (t.type='step' OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.type ='step' )))";
        }
        if (type - 24 == 0) {//京东仓直发
            //京东供销直发订单  source=jd   subsource=jd_gxpt，type=jd_warehouse。
            //京东直发订单  source=jd   subsource=jd_warehouse，
            return " (t.source='jd' and (t.sub_source = 'jd_warehouse' or  t.type='jd_warehouse') )";
        }
        if (type - 25 == 0) {//天猫时效
            //TODO
        }
        if (type - 26 == 0) {//当日发货
            return getTmPromise(TradePromiseUtils.TM_PROMISE_CONSIGN) + " and DATEDIFF(t.delivery_time,t.pay_time)=0 ";
        }
        if (type - 27 == 0) {//24小时发货
            return getTmPromise(TradePromiseUtils.TM_PROMISE_CONSIGN) + " and DATEDIFF(t.delivery_time,t.pay_time)!=0 ";
        }
        if (type - 28 == 0) {//当日达
            return getTmPromise(TradePromiseUtils.TM_PROMISE_ARRIVAL) + " and t.es_time=0 ";
        }
        if (type - 29 == 0) {//次日达
            return getTmPromise(TradePromiseUtils.TM_PROMISE_ARRIVAL) + " and t.es_time=1 ";
        }
        if (type - 30 == 0) {//三日达
            return getTmPromise(TradePromiseUtils.TM_PROMISE_ARRIVAL) + " and t.es_time=2 ";
        }
        if (type - 31 == 0) {//四日达
            return getTmPromise(TradePromiseUtils.TM_PROMISE_ARRIVAL) + " and t.es_time=3 ";
        }
        if (type - 32 == 0) {//五日达
            return getTmPromise(TradePromiseUtils.TM_PROMISE_ARRIVAL) + " and t.es_time=4 ";
        }
        if (type - 99 == 0) {//出库单
            return " t.type like 'trade_out%'";
        }
        if (type - 33 == 0) {//分销订单
            return " t.belong_type = 1 and t.convert_type = 1 ";
        }
        if (type - 34 == 0) {//供销订单
            return " t.belong_type = 2 and t.convert_type = 1 ";
        }
        if (type - 35 == 0) {//京东京配
            return " t.sub_source = 'jd_jp' or t.type = 'jd_jp' ";
        }
        if (type - 37 == 0) {//JIT订单
            return " t.source ='vipjit' and t.sub_source = 'vipjit' ";
        }
        if (type - 38 == 0) {//JITX订单
            return " t.source ='vipjit' and t.sub_source = 'vipjitx' ";
        }
        if (type - 46 == 0) {//档口订单
            return " t.type = 'dangkou'";
        }
        if (type == 47 && staff != null && q != null) {//已退款订单
            Query q1 = new Query();
            q1.append("EXISTS(SELECT 1 FROM ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo());
            q1.append(" WHERE belong_sid = t.sid  and company_id = ? and refund_status = 'SUCCESS')").add(staff.getCompanyId());
            return q1.toString();
        }

        if (type - 36 == 0) {// 平台分销
            return " t.type = 'platform_cost_per_sale'";
        }
        if (type - 39 == 0) {//抖音BIC订单包括 WMS BIC订单
            return " ((t.source = 'fxg' and t.sub_source = 'BIC') or t.type = 'BIC')";
        }
        if (type - 65 == 0) {//抖音BTAS订单包括 WMS BTAS订单
            return "(t.sub_source = 'BTAS' or t.type = 'BTAS')";
        }
        if (type - 48 == 0) {//多级分销订单
            return " t.belong_type = 3 and t.convert_type = 1 ";
        }
        if (type - 49 == 0) {//pdd厂家代打订单
            return " t.source = 'pdd' and t.sub_source = 'fds'";
        }
        if (type - 50 == 0) {//店铺预售
            return " (t.type='shopPresell' OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.type='shopPresell' )))";
        }
        if (type - 51 == 0) {///抖音厂商代发
            return " t.source = 'fxg_df'";
        }
        if (type - 52 == 0) {// 平台分销(统一)
            return " t.belong_type = 1 and t.convert_type = 2 ";
        }
        if (type - 53 == 0) {// 亚马逊FBA
            return " t.source = 'amazon' and t.sub_source IN ('AFN_0','AFN_1')";
        }
        if (type - 54 == 0) {// 亚马逊FBM
            return " t.source = 'amazon' and t.sub_source IN ('MFN_0','MFN_1')";
        }
        if (type - 55 == 0) {// 亚马逊多渠道
            return " t.source = 'amazon' and t.sub_source IN ('AFN_0','MFN_0')";
        }
        if (type - 57 == 0) {//得物普通现货
            return " t.type = '0' and t.source = 'poison'";
        }
        if (type - 58 == 0) {//得物极速现货
            return " t.type = '7' and t.source = 'poison'";
        }
        if (type - 61 == 0) {//得物品牌直发
            return " t.type = '26' and t.source = 'poison'";
        }
        if (type - 60 == 0) {//全款预售
            return " (t.type='fullPresell' OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND merge_sid > 0 AND t2.enable_status = 2 AND t2.type='fullPresell' )))";
        }
        if (type - 56 == 0) {// 奇门供销
            return " ((t.convert_type = 3 and t.belong_type = 2) or (t.v & 128 > 0))";
        }
        if (type - 62 == 0) {//lazada fbl订单
            return " t.source = 'lazada' and t.sub_source = 'Lazada-FBL'";
        }
        if (type - 63 == 0) {///淘宝厂商代发
            return " t.type = 'tb_df' ";
        }
        if (type - 68 == 0) {// 天猫直邮订单 3PL
            return " t.type = 'post_gate_declare' ";
        }
        if (type - 69 == 0) {
            return " t.type = 'sp' ";
        }

        if (type - 72 == 0) {//快麦通代发标记
            return " t.v & 16>0 ";
        }
        if (type - 73 == 0) {//1688分销小站
            return " t.source = '1688_fx' ";
        }
        //速卖通全托管 74
        if (isMatchType(TradeTypeEnum.SMT_QTG, type)) {
            return " ( (t.source = 'smtqtg' OR t.sub_source = 'smtqtg') or (t.source = '1688' and  t.sid IN  (select tl.sid from trade_label_"+staff.getDbInfo().getTradeLabelDbNo()+" tl where tl.company_id = "+staff.getCompanyId()+" AND label_id = "+ TradeSystemLabelEnum.TAG_1000000043.getId()+"  and t.sid = tl.sid and tl.enable_status = 1)  ) ) ";
        }
        //快麦通分销订单,用于快麦通版本前端过滤分销订单数据 75
        if (isMatchType(TradeTypeEnum.FX_TRADE_WITH_DEST, type)) {
            return " t.belong_type = 1 and t.convert_type = 1  and t.dest_id>0 ";
        }
        //有拆单标记的订单 76
        if (isMatchType(TradeTypeEnum.HAVE_SPLITED, type)) {
            return " ( t.split_type = 1 OR ( t.merge_sid > 0 AND  exists (select 1 from trade_"+staff.getDbInfo().getTradeDbNo()+" t2 where" +
                    " t2.company_id = "+staff.getCompanyId()+" AND t2.merge_sid = t.sid AND t2.enable_status = 2 AND t2.split_type = 1)))";
        }
        //后续新增的内容 必需基于 ITradeQueryType 实现 严禁再往这里加if !!!

        return null;
    }

    private boolean isMatchType(TradeTypeEnum typeEnum, Integer type) {
        if (type == null) {
            return false;
        }
        return Objects.equals(typeEnum.getcode(), String.valueOf(type));
    }

    private  String getTmPromise(String promiseService) {
        return " t.timing_promise = '" + TradePromiseUtils.TM_PROMISE + "' and t.promise_service like ('%" + promiseService + "%') ";
    }

}

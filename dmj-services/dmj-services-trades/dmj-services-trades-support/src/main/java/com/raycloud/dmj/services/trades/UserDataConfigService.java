package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.dao.trade.UserDataConfigDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.model.PageDataConfig;
import com.raycloud.dmj.domain.trades.model.UserDataConfig;
import org.apache.commons.collections.MapUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserDataConfigService implements IUserDataConfigService {

    @Resource
    private UserDataConfigDAO userDataConfigDAO;

    @Override
    @Cacheable(value = "defaultCache#600", key = "'user_data_config_'+ #staff.companyId + '_' + #staff.id + '_' + #pageId ")
    public UserDataConfigResponse getUserDataConfigs(Staff staff, long pageId) {

        UserDataConfigResponse response = new UserDataConfigResponse();

        List<UserDataConfig> userDataConfigs = userDataConfigDAO.getUserDataConfigs(staff, pageId);

        List<PageDataConfig> pageDataConfigs = userDataConfigDAO.getPageDataConfigs(pageId);

        if(CollectionUtils.isEmpty(userDataConfigs)) {
            response.setUserDataConfigs(pageDataConfigs.stream().map(this::buildUserDataConfigDTO).collect(Collectors.toList()));
            response.setDefault(true);
            return response;
        }

        Map<Long, PageDataConfig> pageDataConfigMap = pageDataConfigs.stream().collect(Collectors.toMap(PageDataConfig::getId, PageDataConfig -> PageDataConfig));

        List<UserDataConfigDTO> userDataConfigDTOS = userDataConfigs.stream()
                .filter(userDataConfig -> pageDataConfigMap.containsKey(userDataConfig.getDataId()))
                .map(userDataConfig -> {
                    UserDataConfigDTO userDataConfigDTO = new UserDataConfigDTO();
                    userDataConfigDTO.setDataId(userDataConfig.getDataId());
                    userDataConfigDTO.setField(pageDataConfigMap.get(userDataConfig.getDataId()).getDataCode());
                    userDataConfigDTO.setTitle(pageDataConfigMap.get(userDataConfig.getDataId()).getDataTitle());
                    userDataConfigDTO.setVisible(userDataConfig.getVisible());
                    userDataConfigDTO.setSortNo(userDataConfig.getSortNo());
                    pageDataConfigMap.remove(userDataConfig.getDataId());
                    return userDataConfigDTO;
                }).collect(Collectors.toList());

        if(MapUtils.isEmpty(pageDataConfigMap)) {
            response.setUserDataConfigs(userDataConfigDTOS);
            response.setDefault(false);
            return response;
        }

        userDataConfigDTOS.addAll(pageDataConfigs.stream()
                .filter(pageDataConfig -> pageDataConfigMap.containsKey(pageDataConfig.getId()))
                .map(this::buildUserDataConfigDTO).collect(Collectors.toList()));

        response.setUserDataConfigs(userDataConfigDTOS.stream().sorted(Comparator.comparing(UserDataConfigDTO::getSortNo)).collect(Collectors.toList()));
        response.setDefault(false);
        return response;
    }

    private UserDataConfigDTO buildUserDataConfigDTO(PageDataConfig pageDataConfig) {
        UserDataConfigDTO userDataConfigDTO = new UserDataConfigDTO();
        userDataConfigDTO.setDataId(pageDataConfig.getId());
        userDataConfigDTO.setField(pageDataConfig.getDataCode());
        userDataConfigDTO.setTitle(pageDataConfig.getDataTitle());
        userDataConfigDTO.setVisible(pageDataConfig.getIsDefault());
        userDataConfigDTO.setSortNo(pageDataConfig.getSortNo());
        return userDataConfigDTO;
    }

    @Override
    @CacheEvict(value = "defaultCache", key = "'user_data_config_'+ #staff.companyId + '_' + #staff.id + '_' + #pageId ")
    public void updateUserDataConfig(Staff staff, long pageId, List<UserDataConfigDTO> userDataConfigDTOS) {
        userDataConfigDAO.deleteUserDataConfigByPageId(staff, pageId);
        if (CollectionUtils.isEmpty(userDataConfigDTOS)) {
            return;
        }

        List<UserDataConfig> userDataConfigs = userDataConfigDTOS.stream().map(userDataConfigDTO -> {
            UserDataConfig userDataConfig = new UserDataConfig();
            userDataConfig.setCompanyId(staff.getCompanyId());
            userDataConfig.setStaffId(staff.getId());
            userDataConfig.setPageId(pageId);
            userDataConfig.setDataId(userDataConfigDTO.getDataId());
            userDataConfig.setSortNo(userDataConfigDTO.getSortNo());
            userDataConfig.setVisible(userDataConfigDTO.getVisible());
            return userDataConfig;
        }).collect(Collectors.toList());

        userDataConfigDAO.insertUserDataConfigs(staff, userDataConfigs);
    }

    @Override
    public void updateDataConfigToDefault(Staff staff, long pageId) {
        userDataConfigDAO.deleteUserDataConfigByPageId(staff, pageId);
    }
}

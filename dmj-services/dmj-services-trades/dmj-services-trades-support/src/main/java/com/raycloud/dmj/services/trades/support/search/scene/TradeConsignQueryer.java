package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.params.OrderAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeFillEnum;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import org.springframework.stereotype.Component;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-17
 */
@Component
public class TradeConsignQueryer extends TradeAssembleStrategy{

    @Override
    public SenceCodeEnum supportScene() {
        return SenceCodeEnum.CONSIGN;
    }

    @Override
    TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long... sids) {
        return TradeAssembleParams.justTrade().setQueryExt(true)
                //.addBaseField().addExceptRefField().addPaymentField().addQueryField("t.seller_memo", "t.seller_memo_update", "t.is_handler_memo", "t.seller_flag")
                .setQueryMerge(true).setMergeStyle(TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB)
                .setQueryOrder(true).setOrderAssembleParams(OrderAssembleParams.justOrder()
                        //.addBaseField().addPaymentField()
                )
                .setFill(true)
                .addIncludeFills(TradeFillEnum.ORDER_EXCEPT_FILL)
                .addIncludeFills(TradeFillEnum.TRADE_EXCEPT_FILL)
                .setFilter(true);
    }
}

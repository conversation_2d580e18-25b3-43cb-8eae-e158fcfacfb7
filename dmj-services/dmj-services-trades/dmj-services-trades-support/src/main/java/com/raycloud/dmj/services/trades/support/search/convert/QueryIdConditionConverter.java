package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.QueryIdCondition;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;


/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_QUERY_ID)
public class QueryIdConditionConverter extends AbsConditionConverter{


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return true;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        Long queryId = condition.getQueryId();
        if (queryId == null) {
            queryId = SystemTradeQueryParamsContext.QUERY_COMMON;
        }
        QueryIdCondition json = SystemTradeQueryParamsContext.getQueryIdCondition(queryId);

        addConditionByMerge(condition, json,q,queryId);
        //addConditionByCover(condition, json);
    }

    private void addConditionByMerge(TradeQueryRequest condition, QueryIdCondition json,Query q,Long queryId) {
        int merge = merge(condition.getIsExcep(), json.getIsExcep());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"isExcep"));
            return;
        }else {
            condition.setIsExcep(merge == 1 ? condition.getIsExcep() : json.getIsExcep());
        }

        merge = merge(condition.getIsCancel(), json.getIsCancel());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"isCancel"));
            return;
        }else {
            condition.setIsCancel(merge == 1 ? condition.getIsCancel() : json.getIsCancel());
        }

        merge = merge(condition.getIsPresell(), json.getIsPresell());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"isPresell"));
            return;
        }else {
            condition.setIsPresell(merge == 1 ? condition.getIsPresell() : json.getIsPresell());
        }

        merge = merge(condition.getContainExpress(), json.getContainExpress());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"containExpress"));
            return;
        }else {
            condition.setContainExpress(merge == 1 ? condition.getContainExpress() : json.getContainExpress());
        }

        merge = merge(condition.getIsOutstock(), json.getIsOutstock());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"isOutstock"));
            return;
        }else {
            condition.setIsOutstock(merge == 1 ? condition.getIsOutstock() : json.getIsOutstock());
        }

        merge = merge(condition.getExpressStatus(), json.getExpressStatus());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"expressStatus"));
            return;
        }else {
            condition.setExpressStatus(merge == 1 ? condition.getExpressStatus() : json.getExpressStatus());
        }

        merge = merge(condition.getIsHalt(), json.getIsHalt());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"isHalt"));
            return;
        }else {
            condition.setIsHalt(merge == 1 ? condition.getIsHalt() : json.getIsHalt());
        }


        merge = merge(condition.getCheckActive(), json.getCheckActive());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"checkActive"));
            return;
        }else {
            condition.setCheckActive(merge == 1 ? condition.getCheckActive() : json.getCheckActive());
        }

        merge = merge(condition.getCheckActive(), json.getCheckActive());
        if (merge == 2) {
            q.setStopQuery(true);
            q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"checkActive"));
            return;
        }else {
            condition.setCheckActive(merge == 1 ? condition.getCheckActive() : json.getCheckActive());
        }


        if (isEmpty(condition.getSysStatus())) {
            condition.setSysStatus(json.getSysStatus());
        } else if (notEmpty(json.getSysStatus())) {
            Collection<String> intersection = intersection(condition.getSysStatus(), json.getSysStatus());
            if (CollectionUtils.isNotEmpty(intersection)) {
                condition.setSysStatus(intersection.toArray(new String[0]));
            }else {
                q.setStopQuery(true);
                q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"sysStatus"));
                return;
            }
        }

        if (isEmpty(condition.getExceptionStatus())) {
            condition.setExceptionStatus(json.getExceptionStatus());
        } else if (notEmpty(json.getExceptionStatus())) {
            Collection<String> intersection = intersection(condition.getExceptionStatus(), json.getExceptionStatus());
            if (CollectionUtils.isNotEmpty(intersection)) {
                condition.setExceptionStatus(intersection.toArray(new String[0]));
            }else {
                q.setStopQuery(true);
                q.setStopReason(String.format("传入条件与卡盘条件冲突 queryId:%s, condition:%s",queryId,"exceptionStatus"));
                return;
            }
        }

    }

    /**
     *
     * @return 0  取原有值 1 取新值 2 两个值冲突
     * @param <T>
     */
    public <T> int merge(T orgi, T newObj){
        if (orgi == null) {
            return 1;
        }
        if (newObj == null) {
            return 0;
        }
        if (Objects.equals(orgi, newObj)) {
            return 0;
        }
        return 2;
    }



    //自定义条件 优先级应该要比卡盘条件高 如果存在自定义条件 则覆盖卡盘条件
    private void addConditionByCover(TradeQueryRequest condition, QueryIdCondition json) {
        // 设置是否异常订单
        if (isNull(condition.getIsExcep())) {
            condition.setIsExcep(json.getIsExcep());
        }
        // 设置是否作废订单 0未作废, 1已作废
        if (isNull(condition.getIsCancel())) {
            condition.setIsCancel(json.getIsCancel());
        }
        // 设置sysStatus
        if (isEmpty(condition.getSysStatus())) {
            condition.setSysStatus(json.getSysStatus());
        }
        // 设置是否需要检查店铺用户是否激活（是否需要过滤掉店铺已停用的订单）
        if (isNull(condition.getCheckActive())) {
            condition.setCheckActive(json.getCheckActive());
        }

        if (isNull(condition.getIsPresell())) {
            condition.setIsPresell(json.getIsPresell());
        }
        // 设置订单是否分配了快递公司
        if (isNull(condition.getContainExpress())) {
            condition.setContainExpress(json.getContainExpress());
        }

        if (isNull(condition.getIsOutstock())) {
            condition.setIsOutstock(json.getIsOutstock());
        }
        // 设置快递单打印状态，0未打印，1已打印
        if (isNull(condition.getExpressStatus())) {
            condition.setExpressStatus(json.getExpressStatus());
        }
        // 设置exceptionStatus
        if (isNull(condition.getExceptionStatus())) {
            condition.setExceptionStatus(json.getExceptionStatus());
        }
        // 设置是否挂起订单
        if (isNull(condition.getIsHalt())) {
            condition.setIsHalt(json.getIsHalt());
        }
    }



}

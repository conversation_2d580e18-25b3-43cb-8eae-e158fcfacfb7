package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeInspectErrorEnum;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStockUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.item.IItemMatchService;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: ruanyaguang
 * @Date : 2017/5/22
 * @Info : 订单恢复服务实现类
 */
@Service
public class TradeRecoverService implements ITradeRecoverService {

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IItemMatchService itemMatchService;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ILockService lockService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradeMergeService tradeMergeService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Override
    public TradeRecoverResult recoverTrades(final Staff staff, final TradeInspectResult result) {
        final Long[] sids = getSidsFromTradeInspectResults(result);
        final ExcepData excepData = new ExcepData();

        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询订单
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
            Assert.isTrue(originTrades.size() > 0, "修复订单时找不到订单");

            //过滤出需要更新的订单及子订单
            checkCancelExcep(staff, originTrades, excepData);
            if (excepData.unMatchedOrders.size() > 0) {//未匹配系统商品的子订单需要匹配系统商品
                for (Map.Entry<User, List<Order>> entry : excepData.unMatchedOrders.entrySet()) {
                    //匹配系统商品之后需要申请库存
                    List<Order> matchedList = itemMatchService.matchSysItem(entry.getKey(), entry.getValue());
                    if (matchedList.size() > 0) {
                        excepData.stockOrders.addAll(matchedList);
                        excepData.matchedOrders.putAll(OrderUtils.toMap(matchedList));
                    }
                }
            }
            if (excepData.stockOrders.size() > 0) {
                orderStockService.checkOrderStock(staff, excepData.stockOrders);
            }

            createUpdateTrades(staff, excepData);

            //更新数据库
            tradeUpdateService.updateTrades(staff, excepData.getUpdateTrades(), excepData.getUpdateOrders(), null, excepData.toInsertOrders);

            return excepData.handleTrades;
        });

        tradeMergeService.mergeRedo(staff, excepData.sourceMergeSidsMap);
        logAfterCancelExcep(staff, excepData);

        TradeRecoverResult tradeRecoverResult = new TradeRecoverResult();
        tradeRecoverResult.setSuccessRecoverTrades(excepData.handleTrades);
        return tradeRecoverResult;
    }

    private Long[] getSidsFromTradeInspectResults(TradeInspectResult result) {
        Map<Long, Set<TradeInspectErrorEnum>> sidInspectErrorMap = result.getSidInspectErrorMap();
        if (MapUtils.isEmpty(sidInspectErrorMap)) {
            return new Long[0];
        }
        Long[] sids = new Long[sidInspectErrorMap.size()];
        int i = 0;
        for (Long sid : sidInspectErrorMap.keySet()) {
            sids[i++] = sid;
        }
        return sids;
    }

    private void checkCancelExcep(Staff staff, List<Trade> originTrades, ExcepData excepData) {
        for (Trade originTrade : originTrades) {
            //如果订单无异常，则不需要处理
            if (originTrade.getIsExcep() != null && originTrade.getIsExcep() == 0) {
                continue;
            }
            //合单的订单需要额外处理
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, originTrade)) {
                excepData.sourceMergeSidsMap.computeIfAbsent(originTrade.getSource(), k -> new HashSet<>()).add(originTrade.getMergeSid());
            }

            //处理order的异常
            cancelOrder(staff, originTrade, excepData);

            originTrade.setOldSysStatus(originTrade.getSysStatus());
            originTrade.setOldStockStatus(originTrade.getStockStatus());
            excepData.handleTrades.add(originTrade);
        }
    }

    /**
     * 处理子订单的异常
     *
     * @param staff
     * @param originTrade
     * @param excepData
     * @return
     */
    private void cancelOrder(Staff staff, Trade originTrade, ExcepData excepData) {
        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
        for (Order originOrder : originOrders) {
            //已发货的订单不处理
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {
                continue;
            }
            //需要库存校验
            fillStockInfo(originOrder, originTrade);

            if (originOrder.getItemSysId() <= 0) {//商品未匹配
                cancelUnMatchedOrder(staff, originTrade, originOrder, excepData);
            } else {//校验库存
                cancelStockOrder(originTrade, originOrder, excepData);
            }
        }
    }

    /**
     * 有未匹配的子订单
     *
     * @param originOrder
     * @param excepData
     * @return
     */
    private void cancelUnMatchedOrder(Staff staff, Trade originTrade, Order originOrder, ExcepData excepData) {
        User user = staff.getUserByUserId(originTrade.getUserId());
        if (user != null) {
            excepData.unMatchedOrders.computeIfAbsent(user, k -> new ArrayList<>()).add(originOrder);
            originOrder.setOperable(true);
        }
    }

    /**
     * 有可能需要处理库存异常的子订单
     *
     * @param originTrade
     * @param originOrder
     * @param excepData
     * @return
     */
    private void cancelStockOrder(Trade originTrade, Order originOrder, ExcepData excepData) {
        excepData.stockOrders.add(originOrder);
        //套件的话需要处理单品
        if (originOrder.getSuits() != null) {
            for (Order son : originOrder.getSuits()) {
                fillStockInfo(son, originTrade);
            }
        }
        originOrder.setOperable(true);
    }

    private void fillStockInfo(Order order, Trade trade) {
        order.setWarehouseId(trade.getWarehouseId());
        order.setUserId(trade.getUserId());
        order.setOldStockStatus(order.getStockStatus());
        order.setOldStockNum(order.getStockNum());
    }

    private void createUpdateTrades(Staff staff, ExcepData excepData) {
        Map<Long, List<Order>> orderListMap = OrderUtils.toMapBySid(excepData.stockOrders);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Trade trade : excepData.handleTrades) {
            List<Order> list = orderListMap.get(trade.getSid());
            if (list != null) {
                for (Order order : list) {
                    if (excepData.matchedOrders.containsKey(order.getId())) {//刚刚匹配系统商品的子订单
                        if (order.getSuits() != null) {//匹配到套件，单品子订单作插入操作
                            excepData.toInsertOrders.addAll(order.getSuits());
                        }
                        excepData.updateOrders.put(order.getId(), order);//套件本身或非套件作全字段更新
                    } else {
                        //套件本身或非套件
                        createUpdateOrder(staff,order, excepData);
                        //套件单品子订单
                        if (order.getSuits() != null) {
                            for (Order son : order.getSuits()) {
                                createUpdateOrder(staff,son, excepData);
                            }
                        }
                    }
                }
            }

            createUpdateTrade(staff, trade, excepData, tradeConfig);
        }
    }

    void createUpdateTrade(Staff staff, Trade originTrade, ExcepData excepData, TradeConfig tradeConfig) {
        int oldInsufficientNum = originTrade.getInsufficientNum();
        double oldInsufficientRate = originTrade.getInsufficientRate();
        //计算trade库存状态时需要使用完整的order集合: TradeUtils.getOrders4Trade(trade)
        TradeStockUtils.resetTradeStockStatus(staff, originTrade, tradeConfig);
        if (!originTrade.getStockStatus().equals(originTrade.getOldStockStatus())) {//订单库存状态未改变不需要更新
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
         //   updateTrade.setStockStatus(originTrade.getStockStatus());
            TradeExceptUtils.setStockStatus(staff,updateTrade,originTrade.getStockStatus());
        }
        if (originTrade.getInsufficientNum() - oldInsufficientNum != 0) {
            excepData.getUpdateTrade(originTrade).setInsufficientNum(originTrade.getInsufficientNum());
        }
        if (originTrade.getInsufficientRate() - oldInsufficientRate != 0) {
            excepData.getUpdateTrade(originTrade).setInsufficientRate(originTrade.getInsufficientRate());
        }
        //重新计算订单异常状态，确定是否需要更新异常状态
        TradeUtils.setTradeExcep(staff,originTrade);
        excepData.getUpdateTrade(originTrade).setIsExcep(originTrade.getIsExcep());
    }

    //比较库存状态前后是否发生变化，未发生改变不需要更新，返回null
    void createUpdateOrder(Staff staff,Order originOrder, ExcepData excepData) {
        if (!originOrder.getStockStatus().equals(originOrder.getOldStockStatus())) {
            Order updateOrder = excepData.getUpdateOrder(originOrder);
          //  updateOrder.setStockStatus(originOrder.getStockStatus());
            OrderExceptUtils.setStockStatus(staff,updateOrder,originOrder.getStockStatus());
        }
        if (originOrder.getStockNum() - originOrder.getOldStockNum() != 0) {
            excepData.getUpdateOrder(originOrder).setStockNum(originOrder.getStockNum());
        }
    }

    private void logAfterCancelExcep(Staff staff, ExcepData excepData) {
        List<Trade> updateTrades = excepData.getUpdateTrades();
        for (Trade t : updateTrades) {
            StringBuilder buf = new StringBuilder();
            if (t.getStockStatus() != null) {
                buf.append("stockStatus:").append(t.getOrigin().getOldStockStatus()).append("->").append(t.getStockStatus()).append(", ");
            }
            if (t.getIsExcep() != null) {
                buf.append("isExcep:").append(t.getIsExcep()).append(", ");
            }
            if (t.getSysStatus() != null) {
                buf.append("sysStatus:").append(t.getOrigin().getOldSysStatus()).append("->").append(t.getSysStatus()).append(", ");
            }
            if (!TradeExceptUtils.isContainExcept(staff,t, ExceptEnum.HALT)) {
                buf.append("isHalt:1->0");
            }
            if (buf.length() > 0) {
                Logs.ifDebug(LogHelper.buildLog(staff, buf.toString()));
            }
        }

        List<Order> updateOrders = excepData.getUpdateOrders();
        for (Order o : updateOrders) {
            if (excepData.matchedOrders.containsKey(o.getId())) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("恢复订单[sid=%s]:子订单[id=%s]匹配到系统商品[sysItemId=%s,sysSkuId=%s,sysTitle=%s],库存状态[stockStatus=%s,stockNum=%s]", o.getSid(), o.getId(), o.getItemSysId(), o.getSkuSysId(), o.getSysTitle(), o.getStockStatus(), o.getStockNum())));
            } else {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("恢复订单[sid=%s]:子订单[id=%s]库存状态变化[stockStatus:%s->%s,stockNum:%s->%s]", o.getOrigin().getSid(),
                        o.getId(), o.getOrigin().getOldStockStatus(), o.getStockStatus(), o.getOrigin().getOldStockNum(), o.getStockNum())));
            }
        }
    }

    static class ExcepData {
        /**
         * 未匹配系统商品的子订单，按店铺user分组
         */

        Map<User, List<Order>> unMatchedOrders = new HashMap<>();

        /**
         * 未匹配系统商品的子订单经过匹配系统商品操作后，匹配上系统商品的子订单
         */
        Map<Long, Order> matchedOrders = new HashMap<>();

        /**
         * 需要进行库存操作的订单
         */
        List<Order> stockOrders = new ArrayList<>();
        /**
         * 需要处理的trade
         */
        List<Trade> handleTrades = new ArrayList<>();
        /**
         * 合单的订单
         */
        Map<String, Set<Long>> sourceMergeSidsMap = new HashMap<>();


        /**
         * 需要更新的订单
         */
        Map<Long, Trade> updateTrades = new HashMap<>();

        /**
         * 需要更新的子订单
         */
        Map<Long, Order> updateOrders = new HashMap<>();

        List<Order> toInsertOrders = new ArrayList<>();


        Trade getUpdateTrade(Trade originTrade) {
            Trade updateTrade = updateTrades.get(originTrade.getSid());
            if (updateTrade == null) {
                updateTrade = new TbTrade();
                updateTrade.setSid(originTrade.getSid());
                updateTrade.setOrigin(originTrade);
                updateTrades.put(originTrade.getSid(), updateTrade);
            }
            return updateTrade;
        }

        Order getUpdateOrder(Order originOrder) {
            Order updateOrder = updateOrders.get(originOrder.getId());
            if (updateOrder == null) {
                updateOrder = new TbOrder();
                updateOrder.setId(originOrder.getId());
                updateOrder.setOrigin(originOrder);
                updateOrders.put(originOrder.getId(), updateOrder);
            }
            return updateOrder;
        }

        List<Trade> getUpdateTrades() {
            if (updateTrades.size() == 0) {
                return new ArrayList<>();
            }
            return new ArrayList<>(updateTrades.values());
        }

        List<Order> getUpdateOrders() {
            if (updateOrders.size() == 0) {
                return new ArrayList<>();
            }
            return new ArrayList<>(updateOrders.values());
        }
    }
}

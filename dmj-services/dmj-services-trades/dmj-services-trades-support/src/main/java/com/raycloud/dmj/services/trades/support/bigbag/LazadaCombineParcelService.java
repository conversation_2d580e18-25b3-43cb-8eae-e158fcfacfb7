package com.raycloud.dmj.services.trades.support.bigbag;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.logistics.EnumCombineParcelNum;
import com.raycloud.dmj.domain.platform.trades.lazada.*;
import com.raycloud.dmj.dao.trade.TradeCombineParcelDAO;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeParcelDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.logistics.EnumShopeeTransitWarehouse;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import com.raycloud.dmj.domain.trades.params.TradeListRequest;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.lazada.ILazadaFmService;
import com.raycloud.dmj.services.trades.ILogisticsProviderService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LazadaCombineParcelService extends CombineParcelAbstractHandler {

    private static final Logger logger = Logger.getLogger(LazadaCombineParcelService.class);

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    PlatformManagement platformManagement;

    @Resource
    IUserService userService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    private IFileUploadService fileUploadService;

    @Resource
    private ICache cache;

    @Override
    public void afterPropertiesSet() throws Exception {
        CombineParcelFactory.register(CommonConstants.PLAT_FORM_TYPE_LAZADA, this);
    }

    @Override
    @Transactional
    public void addParcelToCombine(Staff staff, List<Trade> tradesList, Long taobaoId) {
        //仅使用 LGS-FM40(深圳)或LGS-FM41(义乌)发货渠道的订单可参与组包
        for (Trade trade : tradesList) {
            if (trade.getTradeExt() == null) {
                throw new RuntimeException("存在订单没有附表信息");
            }
            if (StringUtils.isBlank(trade.getTradeExt().getShippingCarrier())) {
                throw new RuntimeException("缺少物流公司信息");
            }
            if (trade.getTradeExt().getCombineParcelId() != null && trade.getTradeExt().getCombineParcelId() != 0) {
                throw new RuntimeException("存在已上传的订单");
            }
        }
        //key=平台-系统发货仓库-中转仓库
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : tradesList) {
            String key = trade.getSource() + trade.getWarehouseId() + trade.getTradeExt().getTransitWarehouseId();
            tradeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(trade);
        }

        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            List<Trade> tempTrades = entry.getValue();
            Map<String, Object> combineParcelCondition = new HashMap<>();
            combineParcelCondition.put("source", tempTrades.get(0).getSource());
            combineParcelCondition.put("consignWarehouseId", tempTrades.get(0).getWarehouseId());
            combineParcelCondition.put("transferWarehouseId", tempTrades.get(0).getTradeExt().getShippingCarrier());
            //根据来源，发货仓库和中转仓库查询大包列表
            List<TradeCombineParcel> allCombineParcel = tradeCombineParcelDAO.queryValidParcel(staff, combineParcelCondition);
            //lazada过滤出没有平台揽货批次号的大包
            List<TradeCombineParcel> combineParcels = allCombineParcel.stream().filter(e -> StringUtils.isEmpty(e.getPlatformBatchNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(combineParcels)) {
                //如果没有查询到列表，则创建一个新的大包，将同一个key的trade放到一个大包中
                createNewCombineParcel(staff, tempTrades, null);
            } else {
                List<TradeCombineParcel> notEmptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() > 0).collect(Collectors.toList());
                //过滤大包中的小包数量是否大于0
                if (CollectionUtils.isNotEmpty(notEmptyCombineParcels)) {
                    //已存在大包数据
                    TradeCombineParcel notEmptyCombineParcel = notEmptyCombineParcels.get(0);
                    if ((EnumCombineParcelNum.LAZADA.getMaxNum() - notEmptyCombineParcel.getParcelNum()) < tempTrades.size()) {
                        //一个大包中最多放入300条小包数据，如果大包中数据超过300条，则分多个包裹放入
                        int leftNum = EnumCombineParcelNum.LAZADA.getMaxNum() - notEmptyCombineParcel.getParcelNum();
                        List<Trade> leftTrades = tempTrades.subList(0, leftNum);
                        List<Trade> nextTrades = tempTrades.subList(leftNum, tempTrades.size());
                        //当前包裹还能装下更多的小包订单，大包没有满
                        addToOldCombineParcel(staff, leftTrades, notEmptyCombineParcel);
                        //筛选当前大包中的包裹数量是否为0，如果为0则不为空，则还有其他大包未装满，填装其他大包中
                        List<TradeCombineParcel> emptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() == 0).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(emptyCombineParcels)) {
                            createNewCombineParcel(staff, nextTrades, null);
                        } else {
                            addToOldCombineParcel(staff, nextTrades, emptyCombineParcels.get(0));
                        }
                    } else {
                        addToOldCombineParcel(staff, tempTrades, notEmptyCombineParcel);
                    }
                } else {
                    addToOldCombineParcel(staff, tempTrades, combineParcels.get(0));
                }
            }
        }
    }

    private void addToOldCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        List<TradeParcel> oldTradeParcels = tradeParcelDAO.queryBySidsAndCombineParcelId(staff, trades.stream().map(Trade::getSid).toArray(Long[]::new), combineParcel.getId());
        if (CollectionUtils.isNotEmpty(oldTradeParcels)) {
            StringBuilder builder = new StringBuilder();
            for (TradeParcel oldTradeParcel : oldTradeParcels) {
                builder.append(oldTradeParcel.getSid()).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            throw new RuntimeException("[" + builder.toString() + "]对应的小包已存在");
        }
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcel.getId());
        tradeParcelDAO.batchInsert(staff, parcels);
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcel.getId());
            //添加系统日志
            String action = "移入:";
            String content = "lazada首公里预报移入大包：" + combineParcel.getId();
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        TradeCombineParcel increase = new TradeCombineParcel();
        increase.setId(combineParcel.getId());
        increase.setParcelNum(combineParcel.getParcelNum() + parcels.size());
        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(increase));
        if (TradeCombineParcel.UPLOAD_STATUS_UPLOADED == combineParcel.getUploadStatus()) {
            TradeCombineParcel update = new TradeCombineParcel();
            update.setId(combineParcel.getId());
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
        }
    }

    private void createNewCombineParcel(Staff staff, List<Trade> trades, TradeGroupParcelRequest request) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        if(request!=null){
            combineParcel.setTaobaoId(request.getTaobaoId());
            combineParcel.setGatherType(request.getGatherType());
            combineParcel.setTemplateId(request.getTemplateId());
        }else{
            combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        }
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseId(trades.get(0).getTradeExt().getShippingCarrier());
        combineParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(trades.get(0).getTradeExt().getShippingCarrier(), trades.get(0).getTradeExt().getWarehouseAddress()));
        combineParcel.setTransferWarehouseAddress(trades.get(0).getTradeExt().getWarehouseAddress());
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
        tradeCombineParcelDAO.insert(staff, combineParcel);
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);
        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            String content = "Lzada首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);
    }


    /**
     * 生成系统日志
     *
     * @param staff
     * @param Sid
     * @param action
     * @param content
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    private List<TradeParcel> buildParcelsCreate(List<Trade> trades, Long combineParcelId) {
        return trades.stream().map(x -> {
            TradeParcel parcel = new TradeParcel();
            parcel.setCombineParcelId(combineParcelId);
            parcel.setTaobaoId(x.getTaobaoId());
            parcel.setShippingCarrier(x.getTradeExt().getShippingCarrier());
            if (x.getTradeExt().getLogisticId() != null) {
                parcel.setLogisticId(Integer.valueOf(x.getTradeExt().getLogisticId().intValue()));
            }
            parcel.setSid(x.getSid());
            parcel.setTid(x.getTid());
            parcel.setOutSid(x.getOutSid());
            parcel.setWeight(x.getWeight());
            parcel.setNetWeight(x.getNetWeight());
            parcel.setVolume(x.getVolume());
            parcel.setUploadStatus(TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
            return parcel;
        }).collect(Collectors.toList());
    }

    @Override
    public UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel) throws Exception {
        User user = userService.queryByTaobaoId(staff.getCompanyId(), param.getTaobaoId());
        if ((TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD != combineParcel.getStatus() && TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED != combineParcel.getStatus())) {
            throw new RuntimeException("没有可用的大包");
        }

        if (TradeCombineParcel.EnumGatherType.LAZADA_SELF_POST.getType().equals(param.getGatherType()) && StringUtils.isEmpty(param.getTrackingNo())) {
            throw new RuntimeException("自寄方式快递运单号不能为空");
        }
        //查询指定大包里的所有小包
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, null);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException(String.format("大包【%s】中没有小包数据", combineParcel.getId()));
        }
        ILazadaFmService lazadaFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_LAZADA, ILazadaFmService.class);
        List<User> userList = staff.getUsers();
        if (CollectionUtils.isEmpty(userList)) {
            userList = userService.queryByCompanyId(staff.getCompanyId());
        }
        Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(us -> us.getTaobaoId(), Function.identity(), (a, b) -> b));
        //1.对多店铺进行绑定
        Boolean bindResult = LazadaAccountBind(user, userMap, parcels, lazadaFmService);
        if (bindResult == null && !bindResult) {
            throw new RuntimeException(String.format("大包【%s】组包店铺绑定失败", combineParcel.getId()));
        }
        //2.查询菜鸟标准地址
        Warehouse warehouse = warehouseService.queryById(combineParcel.getConsignWarehouseId());
        AddressQueryResponse addressResponse = lazadaAddressQueryByWarehouse(user, lazadaFmService, warehouse);
        //3.获取店铺邮箱信息
        Shop shop = lazadaFmService.getShop(user);
        UploadTradeParcelResult result = new UploadTradeParcelResult();
        bigbagCommint(staff, param, combineParcel, user, lazadaFmService, result, warehouse, addressResponse, shop);
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_UPLOADED);
        }
        return result;

    }

    /**
     * lazada大包上传平台或更新
     *
     * @param staff
     * @param param
     * @param combineParcel
     * @param user
     * @param lazadaFmService
     * @param result
     * @param warehouse
     * @param address
     * @param shop
     */
    private void bigbagCommint(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel,
                               User user, ILazadaFmService lazadaFmService, UploadTradeParcelResult result,
                               Warehouse warehouse, AddressQueryResponse address, Shop shop) {
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcel.getId()}, null);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查询小包为空");
        }
        List<TradeParcel> successList = new ArrayList<>();
        List<TradeParcel> failList = new ArrayList<>(1);
        TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
        combineParcelUpdate.setGatherType(param.getGatherType());
        combineParcelUpdate.setId(combineParcel.getId());
        combineParcelUpdate.setTaobaoId(param.getTaobaoId());
        //揽货批次号为空，新建提交
        if (StringUtils.isEmpty(combineParcel.getPlatformBatchNo())) {
            BigBagCommitRequest bigBagCommitRequest = buildBigBagCommintRequest(param, parcels, warehouse, address, shop);
            BigBagCommitResponse bigBagCommitResponse = lazadaFmService.bigBagCommit(user, bigBagCommitRequest);
            //新增大包成功，更改大包状态，
            combineParcelUpdate.setPlatformBatchNo(bigBagCommitResponse.getHandoverContentCode());
            combineParcelUpdate.setTrackingNo(param.getTrackingNo());
            combineParcelUpdate.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
            combineParcelUpdate.setStatus(TradeCombineParcel.STATUS_OUTBOUNDED);
            successList.addAll(parcels.stream().filter(parcel -> parcel.getUploadStatus() != TradeParcel.UPLOAD_STATUS_UPLOADED).collect(Collectors.toList()));
        } else {
            BigBagUpdateRequest bigBagUpdateRequest = buildBigBagUpdateRequest(combineParcel, parcels);
            BigBagUpdateResponse bigBagUpdateResponse = lazadaFmService.bigBagUpdate(user, bigBagUpdateRequest);
            if (bigBagUpdateResponse.getResult()) {
                combineParcelUpdate.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
                successList.addAll(parcels.stream().filter(parcel -> parcel.getUploadStatus() != TradeParcel.UPLOAD_STATUS_UPLOADED).collect(Collectors.toList()));
            } else {
                throw new RuntimeException(String.format("Lazada更新大包报错：[%s]", bigBagUpdateResponse.getErrorMsg()));
            }
        }
        tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(combineParcelUpdate));
        result.setSuccessList(successList);
        result.setFailList(failList);
        //打印组包上传日志
        printPlatformBatchNo(successList, failList, staff, combineParcelUpdate);
    }

    /**
     * 构建修改组包请求
     *
     * @param combineParcel
     * @param parcels
     * @return
     */
    private BigBagUpdateRequest buildBigBagUpdateRequest(TradeCombineParcel combineParcel, List<TradeParcel> parcels) {
        List<String> orderCodeList = parcels.stream().map(TradeParcel::getOutSid).collect(Collectors.toList());
        BigBagUpdateRequest request = new BigBagUpdateRequest();
        request.setOrderCodeList(orderCodeList);
        request.setWeight(String.valueOf(parcels.stream().mapToDouble(TradeParcel::getWeight).sum() == 0 ? (int) 1 : (int) (parcels.stream().mapToDouble(TradeParcel::getWeight).sum() * 1000)));
        request.setWeightUnit("g");
        request.setOrderCode(combineParcel.getPlatformBatchNo());
        return request;
    }

    /**
     * 构建提交组包请求
     *
     * @param parcels
     * @param warehouse
     * @param addressQueryResponse
     * @param shop
     * @return
     */
    private BigBagCommitRequest buildBigBagCommintRequest(TradeCombineParcel param, List<TradeParcel> parcels, Warehouse warehouse, AddressQueryResponse addressQueryResponse, Shop shop) {
        List<String> orderCodeList = parcels.stream().map(TradeParcel::getOutSid).collect(Collectors.toList());
        BigBagCommitRequest request = new BigBagCommitRequest();
        BigBagCommitRequest.PickupInfo pickupInfo = new BigBagCommitRequest.PickupInfo();
        BigBagCommitRequest.ReturnInfo returnInfo = new BigBagCommitRequest.ReturnInfo();
        BigbagAddres pickUpAddress = new BigbagAddres();
        //地址信息
        pickUpAddress.setCountry("中国");
        pickUpAddress.setZipCode(warehouse.getAddressCode());
        pickUpAddress.setCity(warehouse.getCity());
        pickUpAddress.setProvince(warehouse.getState());
        pickUpAddress.setStreet(warehouse.getDistrict());
        pickUpAddress.setDistrict(warehouse.getDistrict());
        pickUpAddress.setDetailAddress(warehouse.getAddress());
        //揽收人信息
        pickupInfo.setAddressId(addressQueryResponse.getAddressId());
        pickupInfo.setCourierCompany("");
        pickupInfo.setAddress(pickUpAddress);
        pickupInfo.setPhone(warehouse.getContactPhone());
        pickupInfo.setName(warehouse.getName());
        pickupInfo.setMobile(warehouse.getContactPhone());
        pickupInfo.setEmail(shop.getEmail());
        //退货人信息
        BeanUtils.copyProperties(pickupInfo, returnInfo);
        returnInfo.setAddress(pickUpAddress);
        request.setOrderCodeList(orderCodeList);
        //组包单位为g,如果商品重量为0，则默认值为1
        request.setWeight(String.valueOf(parcels.stream().mapToDouble(TradeParcel::getWeight).sum() == 0 ? (int) 1 : (int) (parcels.stream().mapToDouble(TradeParcel::getWeight).sum() * 1000)));
        request.setWeightUnit("g");
        request.setPickupInfo(pickupInfo);
        request.setReturnInfo(returnInfo);
        request.setType(TradeCombineParcel.LazadaEnumGatherType.getEnumLazadaGatherType(param.getGatherType()).getCode());
        request.setSellerTrackingNumber(TradeCombineParcel.EnumGatherType.LAZADA_SELF_POST.getGatherType().equals(param.getGatherType()) ? param.getTrackingNo() : "");

        if (TradeCombineParcel.EnumGatherType.PICKUP_COLLECTION.getGatherType().equals(param.getGatherType())) {
            if (StringUtils.isEmpty(param.getPickUpCode())) {
                throw new RuntimeException("集货模式，集货点编码不能为空");
            }
            BigBagCommitRequest.CollectionInfo collectionInfo = new BigBagCommitRequest.CollectionInfo();
            collectionInfo.setPickUpCode(param.getPickUpCode());
            request.setCollectionInfo(collectionInfo);
        }
        return request;
    }

    /**
     * 根据仓库地址获取菜鸟标准地址
     *
     * @param user
     * @param lazadaFmService
     * @param warehouse
     * @return
     */
    private AddressQueryResponse lazadaAddressQueryByWarehouse(User user, ILazadaFmService lazadaFmService, Warehouse warehouse) {
        AddressQueryRequest request = new AddressQueryRequest();
        //默认中国
        request.setCountry("中国");
        request.setCity(warehouse.getCity());
        request.setProvince(warehouse.getState());
        //仓库地址没有街道字段，用区代替
        request.setStreet(warehouse.getDistrict());
        request.setDistrict(warehouse.getDistrict());
        request.setDetailAddress(warehouse.getAddress());
        AddressQueryResponse response = lazadaFmService.addressQuery(user, request);
        return response;
    }

    /**
     * lazada店铺关联
     *
     * @param user
     * @param userMap
     * @param parcels
     */
    private boolean LazadaAccountBind(User user, Map<Long, User> userMap, List<TradeParcel> parcels, ILazadaFmService lazadaFmService) {
        Set<Long> taobaoIds = parcels.stream().map(TradeParcel::getTaobaoId).collect(Collectors.toSet());
        AccountBindRequest request = new AccountBindRequest();
        List<AccountBindRequest.SellerList> sellerList = new ArrayList<>();
        for (Long taobaoid : taobaoIds) {
            AccountBindRequest.SellerList seller = new AccountBindRequest.SellerList();
            seller.setSellerId(String.valueOf(taobaoid));
            seller.setCountry(String.valueOf(userMap.get(taobaoid).getUserConf().getSellerInfo().get("country")));
            sellerList.add(seller);
        }
        request.setSellerList(sellerList);
        AccountBindResponse response = lazadaFmService.accountBind(user, request);
        return response.isResult();
    }


    private void updateParcelStatus(Staff staff, List<TradeParcel> parcels, int uploadStatus) {
        List<TradeParcel> parcelUpdates = parcels.stream().map(x -> {
            TradeParcel parcelUpdate = new TradeParcel();
            parcelUpdate.setId(x.getId());
            parcelUpdate.setUploadStatus(uploadStatus);
            return parcelUpdate;
        }).collect(Collectors.toList());

        tradeParcelDAO.batchUpdate(staff, parcelUpdates);
    }

    /**
     * 组包上传打印日志，记录揽货批次号
     *
     * @param successList        上传成功的订单
     * @param failList           上传失败的订单
     * @param tradeCombineParcel 组包信息
     * <AUTHOR>
     * @date 2022/1/16 下午2:42
     */
    void printPlatformBatchNo(List<TradeParcel> successList, List<TradeParcel> failList, Staff staff, TradeCombineParcel tradeCombineParcel) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<Long> failSid = new ArrayList<>();
        //添加上传失败的订单日志
        for (TradeParcel tradeParcel : failList) {
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), tradeParcel.getSid(), "组包上传", staff.getName(), new Date(), "组包上传：组包上传失败，失败原因：" + tradeParcel.getReason());
            tradeTraces.add(tradeTrace);
            failSid.add(tradeParcel.getSid());
        }
        //添加上传成功的订单日志
        successList.stream().filter(item -> !failSid.contains(item.getSid())).forEach(item -> {
            String content;
            if (TradeCombineParcel.EnumGatherType.PICK_UP.getType().equals(tradeCombineParcel.getGatherType())) {
                //上门揽收
                content = "组包上传：组包上传成功（上门揽收），揽货批次号：" + tradeCombineParcel.getPlatformBatchNo();
            } else {
                //快递寄送
                content = "组包上传：组包上传成功（快递寄送）";
            }
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), item.getSid(), "组包上传", staff.getName(), new Date(), content);
            tradeTraces.add(tradeTrace);
        });
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return;
        }
        try {
            //入库
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "入库组包上传日志时抛出异常"));
        }
    }

    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
    }

    @Transactional
    @Override
    void cancelCombineParcel(Staff staff, TradeCombineParcel parcel){

        ILazadaFmService lazadaFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_LAZADA, ILazadaFmService.class);
        List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
        //判断如果当前大包中未存在小包，则直接置为取消状态
        if (parcel.getParcelNum() == 0) {
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
        } else {
            if (StringUtils.isNotEmpty(parcel.getPlatformBatchNo())) {
                User user = userService.queryByTaobaoId(staff.getCompanyId(), parcel.getTaobaoId());
                BigBagCancelRequest request = new BigBagCancelRequest();
                request.setOrderCode(parcel.getPlatformBatchNo());
                AccountBindResponse response = lazadaFmService.bigBagCancel(user, request);
                if (!response.isResult()) {
                    throw new RuntimeException(response.getErrorMsg());
                }
            }
        }
        // 待上传组包，移出组包
        if (CollectionUtils.isNotEmpty(parcelList)) {
            //将小包移出大包
            removeParcel(staff, parcel.getId(), parcelList.stream().map(TradeParcel::getId).toArray(Long[]::new));
        }
        //更改大包状态为已取消
        updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeParcel(Staff staff, Long combineParcelId, Long[] parcelIds) {
        if (combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);

        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.stream().forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "lazada首公里预报移出大包：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });

    }

    @Override
    CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception {
        if (taobaoId == null || combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        User user = userService.queryByTaobaoId(staff.getCompanyId(), taobaoId);
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询的大包为空");
        }
        TradeCombineParcel combineParcel = combineParcels.get(0);

        CancelUploadTradeParcelResult result = new CancelUploadTradeParcelResult();
        List<TradeParcel> successList = new ArrayList<>();

        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查询的小包为空");
        }
        List<TradeParcel> allParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_UPLOADED);
        //两个list取差集
        List<TradeParcel> tradeParcelList =
                allParcels.stream().filter(all -> !parcels.stream().map(parcel -> parcel.getOutSid()).collect(
                        Collectors.toList()).contains(all.getOutSid())).collect(Collectors.toList());

        ILazadaFmService lazadaFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_LAZADA, ILazadaFmService.class);
        //调用大包修改操作
        BigBagUpdateRequest bigBagUpdateRequest = buildBigBagUpdateRequest(combineParcel, tradeParcelList);
        BigBagUpdateResponse bigBagUpdateResponse = lazadaFmService.bigBagUpdate(user, bigBagUpdateRequest);
        if (bigBagUpdateResponse.getResult()) {
            successList.addAll(parcels);
            result.setSuccessList(successList);
        } else {
            throw new RuntimeException(String.format("移出小包出错：[%s]", bigBagUpdateResponse.getErrorMsg()));
        }
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_CANCELLED);
        }
        //将小包移出大包
        removeParcel(staff, combineParcelId, parcelIds);
        return result;
    }

    @Override
    public TradeCombineParcelResponse getPlatformBatchNoPrintData(TradeCombineParcel tradeCombineParcel, Map<Long, Warehouse> warehouseMap, Staff staff,String subbagId){
        TradeCombineParcelResponse response = new TradeCombineParcelResponse();
        String fileUrl = "";
        try {
            fileUrl = cache.get(String.format("%s_%s", tradeCombineParcel.getCompanyId(), tradeCombineParcel.getId()));
        } catch (CacheException e) {
            logger.info("缓存查询失败");
        }
        if (StringUtils.isEmpty(fileUrl)) {
            User user = userService.queryByTaobaoId(staff.getCompanyId(), tradeCombineParcel.getTaobaoId());
            ILazadaFmService lazadaFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_LAZADA, ILazadaFmService.class);
            BigBagPdfRequest bigBagPdfRequest = new BigBagPdfRequest();

            if(StringUtils.isNotBlank(tradeCombineParcel.getTrackingNo())){
                bigBagPdfRequest.setTrackingNumber(tradeCombineParcel.getTrackingNo());
            }else{
                bigBagPdfRequest.setOrderCode(tradeCombineParcel.getPlatformBatchNo());
            }
            String fileName = String.format("%s_%s.pdf", tradeCombineParcel.getCompanyId(), tradeCombineParcel.getId());

            BigBagPdfResponse bigBagPdfResponse = lazadaFmService.bigBagPdf(user, bigBagPdfRequest);
            InputStream inputStream = new ByteArrayInputStream(bigBagPdfResponse.getBody());

            FileResult result = fileUploadService.uploadToTemp(fileName, inputStream, bigBagPdfResponse.getBody().length);
            if (result != null && StringUtils.isNotEmpty(result.getUrl())) {
                try {
                    fileUrl = result.getUrl();
                    cache.set(String.format("%s_%s", tradeCombineParcel.getCompanyId(), tradeCombineParcel.getId()), result.getUrl(), 3600 * 24);
                } catch (CacheException e) {
                    logger.info("缓存查询失败");
                }
            } else {
                throw new RuntimeException("lazada打印文件上传失败");
            }
        }
        response.setPlatformBatchNo(tradeCombineParcel.getPlatformBatchNo());
        response.setPdfContent(fileUrl);
        return response;
    }

    @Override
    @Transactional
    public void addParcelToCombineAndSplit(Staff staff, List<Trade> tradeList, TradeGroupParcelRequest request) {
        if ((tradeList.size() / request.getPackageNum()) > EnumCombineParcelNum.LAZADA.getMaxNum()) {
           throw new RuntimeException("预计生成的大包数量不够装载所选小包数量，请重新填写要生成的大包数量");
       }
        List<List<Trade>> splitLists = CombineParceUtils.averageAssign(tradeList, request.getPackageNum());
        for (int i = 0; i < splitLists.size(); i++) {
            List<Trade> trades = splitLists.get(i);
            createNewCombineParcel(staff, trades, request);
        }
    }
}

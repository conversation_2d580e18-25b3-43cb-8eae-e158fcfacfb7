package com.raycloud.dmj.services.trades.warehouse;


import com.raycloud.dmj.dao.warehouse.CrossBorderWarehousesDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.bo.CrossBorderWarehouseBO;
import com.raycloud.dmj.domain.trades.warehouse.CrossBorderWarehouses;
import com.raycloud.dmj.services.trades.ICrossBorderWarehouseService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-10-11
 * @description 跨境平台仓库实现
 */
@Service
public class CrossBorderWarehouseServiceImpl implements ICrossBorderWarehouseService {
    @Resource
    CrossBorderWarehousesDao crossBorderWarehousesDao;


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int addBatch(Staff staff, List<CrossBorderWarehouseBO> list) {
        List<CrossBorderWarehouses> crossBorderWarehousesList = new ArrayList<>(list.size());
        for (CrossBorderWarehouseBO bo : list) {
            CrossBorderWarehouses crossBorderWarehouses = new CrossBorderWarehouses();
            BeanUtils.copyProperties(bo, crossBorderWarehouses);
            crossBorderWarehousesList.add(crossBorderWarehouses);
        }
        return crossBorderWarehousesDao.batchInsert(staff, crossBorderWarehousesList);
    }

    @Override
    public int update(Staff staff, CrossBorderWarehouseBO bo) {
        CrossBorderWarehouses crossBorderWarehouses = new CrossBorderWarehouses();
        BeanUtils.copyProperties(bo, crossBorderWarehouses);
        return crossBorderWarehousesDao.update(staff, crossBorderWarehouses);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateStatus(Staff staff, CrossBorderWarehouseBO bo) {
        Map<String, Object> condition = new HashMap<String, Object>(2);
        condition.put("id", bo.getId());
        condition.put("enableStatus", bo.getEnableStatus());
        return crossBorderWarehousesDao.updateCrossBorderWarehousesStatus(condition);
    }

    @Override
    @Cacheable(value = "defaultCache#300", key = "'cross_border_warehouse_' + #bo.source")
    public List<CrossBorderWarehouses> getListByExample(CrossBorderWarehouseBO bo, Page page) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", bo.getId());
        params.put("source", bo.getSource());
        params.put("code", bo.getCode());
        params.put("page", page);
        return crossBorderWarehousesDao.getList(params);
    }
}

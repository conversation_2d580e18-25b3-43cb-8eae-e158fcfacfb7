package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.raycloud.bizlogger.Logger;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.dao.trade.PurchasePrintConfigDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.PurchasePrintConfig;
import com.raycloud.dmj.domain.trades.vo.PrinterConfigVO;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.IPurchasePrintConfigService;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * PurchasePrintConfig  Service
 *
 * <AUTHOR>
 * @since 2019-06-26
 */
@Service
public class PurchasePrintConfigService implements IPurchasePrintConfigService {

    @Resource
    PurchasePrintConfigDAO purchasePrintConfigDAO;
    @Resource
    IOpLogService opLogService;
    @Resource
    ICache cache;
    private static final String PURCHASEPRINTCONFIG_CACHE_KEY_HEAD = "Purchase_Print_Config_";
    private static final Logger logger = Logger.getLogger(PurchasePrintConfigService.class);

    /**
     * 插入
     */
    @Override
    public Integer addPurchasePrintConfig(Staff staff, PurchasePrintConfig purchasePrintConfig) {
        return purchasePrintConfigDAO.addPurchasePrintConfig(staff, purchasePrintConfig);
    }

    /**
     * 根据主键查找
     */
    @Override
    public PurchasePrintConfig getPurchasePrintConfigByKey(Staff staff, Long id) {
        return purchasePrintConfigDAO.getPurchasePrintConfigByKey(staff, id);
    }

    /**
     * 根据账号查找
     */
    @Override
    public PurchasePrintConfig getPurchasePrintConfigByStaff(Staff staff) {
        PurchasePrintConfig purchasePrintConfig;
        try {
            purchasePrintConfig = cache.get(PURCHASEPRINTCONFIG_CACHE_KEY_HEAD + staff.getId());
            if (purchasePrintConfig == null) {
                purchasePrintConfig = purchasePrintConfigDAO.getPurchasePrintConfigByStaff(staff);
                getDealData(purchasePrintConfig);
                if (purchasePrintConfig != null) {
                    cache.set(PURCHASEPRINTCONFIG_CACHE_KEY_HEAD + staff.getId(), purchasePrintConfig, 60 * 60);
                }
            }
        } catch (Exception e) {
            logger.debug("快速入库缓存读取失败异常!从数据库重新获取");
            purchasePrintConfig = purchasePrintConfigDAO.getPurchasePrintConfigByStaff(staff);
        }
        return purchasePrintConfig;
    }

    private void getDealData(PurchasePrintConfig purchasePrintConfig) {
        StringBuilder builder = new StringBuilder();
        Integer completePrint = purchasePrintConfig.getCompletePrint();
        for (Integer printEle : PurchasePrintConfig.completePrintList) {
            int temp = printEle & completePrint;
            if (printEle == temp) {
                builder.append(printEle).append(",");
            }
        }
        String completePrintStr = builder.toString();
        if (StringUtils.isNotEmpty(completePrintStr)) {
            purchasePrintConfig.setCompletePrintStr(completePrintStr.substring(0, completePrintStr.length() - 1 ));
        }
        //第一次获取,将历史数据进行转换
        if (StringUtils.isEmpty(purchasePrintConfig.getPrinterConfig())) {
            Map<String, PrinterConfigVO> printConfigMap = Maps.newHashMap();
            PrinterConfigVO outerPrintConfig = new PrinterConfigVO();
            outerPrintConfig.setPrinterId("" + purchasePrintConfig.getOuterPrinterId());
            outerPrintConfig.setPrinterName(purchasePrintConfig.getOuterPrinterName());
            outerPrintConfig.setTemplateId("" + purchasePrintConfig.getOuterTemplateId());
            printConfigMap.put("3", outerPrintConfig);
            PrinterConfigVO tagPrintConfig = new PrinterConfigVO();
            tagPrintConfig.setPrinterId("" + purchasePrintConfig.getTagPrinterId());
            tagPrintConfig.setPrinterName(purchasePrintConfig.getTagPrinterName());
            tagPrintConfig.setTemplateId("" + purchasePrintConfig.getTagTemplateId());
            printConfigMap.put("5", tagPrintConfig);
            purchasePrintConfig.setPrinterConfig(JSON.toJSONString(printConfigMap));
        }
    }

//    /**
//     * 根据主键组进行查找
//     */
//    @Override
//    public List<PurchasePrintConfig> getPurchasePrintConfigByKeys(Staff staff, List<Long> idList) {
//        return purchasePrintConfigDAO.getPurchasePrintConfigByKeys(staff,idList);
//    }

//    /**
//     * 根据主键删除
//     */
//    @Override
//    public Integer deleteByKey(Staff staff, Long id) {
//        return purchasePrintConfigDAO.deleteByKey(id);
//    }

    /**
     * 根据主键更新
     */
    @Override
    public Integer updatePurchasePrintConfigByKey(Staff staff, PurchasePrintConfig purchasePrintConfig) throws Exception {
        Integer num = purchasePrintConfigDAO.updatePurchasePrintConfigByKey(staff, purchasePrintConfig);
        try {
            cache.delete(PURCHASEPRINTCONFIG_CACHE_KEY_HEAD + staff.getId());
        } catch (Exception e) {
            throw new IllegalAccessException("删除采购快速收货打印配置缓存失败!");
        }
        return num;
    }

    /**
     * 保存设置
     */
    @Override
    public void savePrintConfig(Staff staff, PurchasePrintConfig purchasePrintConfig) throws Exception {
        PurchasePrintConfig oldConfig = getPurchasePrintConfigByStaff(staff);
        saveDealData(purchasePrintConfig);
        if (oldConfig == null) {
            purchasePrintConfig.setCompanyId(staff.getCompanyId());
            purchasePrintConfig.setStaffId(staff.getId());
            purchasePrintConfigDAO.addPurchasePrintConfig(staff, purchasePrintConfig);
        } else {
            purchasePrintConfig.setId(oldConfig.getId());
            updatePurchasePrintConfigByKey(staff, purchasePrintConfig);
            buildUpdateLog(staff, purchasePrintConfig, oldConfig);
        }
    }

    private void saveDealData(PurchasePrintConfig purchasePrintConfig) {
        if (StringUtils.isNotEmpty(purchasePrintConfig.getCompletePrintStr())) {
            List<Integer> printList = ArrayUtils.toIntegerListPosition(purchasePrintConfig.getCompletePrintStr());
            int completePrint = 0;
            //或计算 |
            for (Integer printEle : printList) {
                completePrint |= printEle;
            }
            purchasePrintConfig.setCompletePrint(completePrint);
        }
        if (StringUtils.isNotEmpty(purchasePrintConfig.getPrinterConfig())) {
            //带泛型的Map反序列化
            Map<String, PrinterConfigVO> parseObject = JSON.parseObject(purchasePrintConfig.getPrinterConfig(), new TypeReference<Map<String, PrinterConfigVO>>(){});
            purchasePrintConfig.setPrinterConfig(JSON.toJSONString(parseObject));
        }
        
    }

    /**
     * 保存修改日志
     */
    private void buildUpdateLog(Staff staff, PurchasePrintConfig newConf, PurchasePrintConfig oldConf) {
        StringBuilder buf = new StringBuilder();
        ConfigLogUtils.addLogContent(buf, "逻辑删除标志", newConf.getEnableStatus(), oldConf.getEnableStatus());
        ConfigLogUtils.addLogContent(buf, "保存收货数时打印设置", newConf.getSaveNumberPrint(), oldConf.getSaveNumberPrint());
        ConfigLogUtils.addLogContent(buf, "收货完成时打印设置", newConf.getCompletePrint(), oldConf.getCompletePrint());
        ConfigLogUtils.addLogContent(buf, "商家编码模板id", newConf.getOuterTemplateId(), oldConf.getOuterTemplateId());
        ConfigLogUtils.addLogContent(buf, "商家编码打印机id", newConf.getOuterPrinterId(), oldConf.getOuterPrinterId());
        ConfigLogUtils.addLogContent(buf, "商家编码打印机名称", newConf.getOuterPrinterName(), oldConf.getOuterPrinterName());
        ConfigLogUtils.addLogContent(buf, "吊牌模板id", newConf.getTagTemplateId(), oldConf.getTagTemplateId());
        ConfigLogUtils.addLogContent(buf, "吊牌打印机id", newConf.getTagPrinterId(), oldConf.getTagPrinterId());
        ConfigLogUtils.addLogContent(buf, "吊牌打印机名称", newConf.getTagPrinterName(), oldConf.getTagPrinterName());
        ConfigLogUtils.addLogContent(buf, "添加商品弹窗确认时", newConf.getDialogConfirm(), oldConf.getDialogConfirm());
        ConfigLogUtils.addLogContent(buf, "录入商品后打印设置", newConf.getSaveItemPrint(), oldConf.getSaveItemPrint());

        if (buf.length() > 0) {
            ConfigLogUtils.addUpdateLog(staff, buf.toString(), "purchase.print.Config.update", opLogService);
        }
    }

}

package com.raycloud.dmj.services.logistics;

import com.google.common.collect.Maps;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.dao.pt.WarehouseTemplateDAO;
import com.raycloud.dmj.dao.trade.LogisticsTrackingPollPoolDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.LogisticsStatusEnum;
import com.raycloud.dmj.domain.platform.trades.LogisticsRequest;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.WarehouseTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.user.UserConf;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.PlatformLogisticsTrackingService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.support.LogisticsTrackingConfigService;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.TradeLogisticsTrackingUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多平台更新自有物流
 *
 * <AUTHOR> 2019/11/14
 */
@Service
public class TradeLogisticsPlatformTrackingServices extends AbstractTradeLogisticsTrackingServices {
    private final String LOGISTICS_SYNC_PREFIX = "trade_logistics_tracking_support_platform";

    private static final List<String> allowSyncList = new ArrayList<>();

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private ICache cache;

    @Resource
    private PlatformManagement platformManagement;

    @Resource
    private LogisticsTrackingPollPoolDao logisticsTrackingPollPoolDao;

    @Resource
    private IExpressTemplateDubboService expressTemplateDubboService;

    @Resource
    private IExpressCompanyService expressCompanyService;

    @Resource
    private LogisticsTrackingConfigService logisticsTrackingConfigService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private TradeConfigService tradeConfigService;

    @Resource
    private IUserService userService;

    @Resource
    private WarehouseTemplateDAO warehouseTemplateDAO;

    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;

    static {
        allowSyncList.add(CommonConstants.PLAT_FORM_TYPE_PDD);
    }

    @Override
    public Integer getOrder() {
        return super.getOrder();
    }

    @Override
    public boolean isAllow(Staff staff) {
        try {
            boolean globalSwitch = cache.add(LOGISTICS_SYNC_PREFIX, "1", 60 * 60);
            boolean companySwitch = cache.add(LOGISTICS_SYNC_PREFIX + "_" + staff.getCompanyId(), "1", 60 * 60);
            boolean canSync = cache.add(LOGISTICS_SYNC_PREFIX + "_" + "platform" + "_" + staff.getCompanyId(), "1", 60 * 30);
            if (globalSwitch && companySwitch && canSync) {
                //是否开启物流跟踪
                TradeConfig tradeConfig = tradeConfigService.get(staff);
                if (1 != tradeConfig.getOpenLogisticsTracking()) {
                    //是否开启揽收上传
                    List<User> userList = userService.queryByCompanyId(staff.getCompanyId());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        List<User> pddUser = userList.stream().filter(u -> CommonConstants.PLAT_FORM_TYPE_PDD.equals(u.getSource())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(pddUser)) {
                            for (User user : pddUser) {
                                if (null != getPddUploadStrategy(user) && 2 == getPddUploadStrategy(user)) {
                                    return true;
                                }
                            }

                        }
                    }
                } else {
                    return true;
                }
            } else {
                if (globalSwitch) {
                    cache.delete(LOGISTICS_SYNC_PREFIX);
                }
                if (companySwitch) {
                    cache.delete(LOGISTICS_SYNC_PREFIX + "_" + staff.getCompanyId());
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("设置缓存出错；").append(e.getMessage()));
            return false;
        }
        return false;
    }

    @Override
    public void end(Staff staff) {
        try {
            cache.delete(LOGISTICS_SYNC_PREFIX);
            cache.delete(LOGISTICS_SYNC_PREFIX + "_" + staff.getCompanyId());
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("清除缓存出错；").append(e.getMessage()));
        }
    }

    @Override
    public void invokeLogisticsInfo(Staff staff) {
        Page page = new Page();
        int pageNo = 1;
        page.setPageSize(100);
        LogisticsTrackingPollPoolQueryParams queryParams = new LogisticsTrackingPollPoolQueryParams();
        queryParams.setPage(page);
        List<LogisticsTrackingPollPool> logisticsInfosDb;
        Map<String, UserExpressTemplate> expressTemplateMap = Maps.newHashMap();
        Map<Long, ExpressCompany> expressCompanyMap = Maps.newHashMap();
        LogisticsTrackingConfig config;
        boolean templateFlag = true;
        boolean expressFlag = true;
        int totalCount = 0;
        int succCount = 0;
        Map<String, User> branchUserMap = new HashMap<>(); //存放实际获取单号的网点的user
        for (; ; ) {
            page.setPageNo(pageNo);
            logisticsInfosDb = logisticsTrackingPollPoolDao.queryUnsignedLogisticsRecord(staff, queryParams);
            if (CollectionUtils.isEmpty(logisticsInfosDb)) {
                break;
            }
            Map<Long, Trade> tradeMap = tradeSearchService.queryBySids(staff, false, logisticsInfosDb.stream().map(LogisticsTrackingPollPool::getSid).toArray(Long[]::new))
                    .stream().filter(t -> StringUtils.equals(Trade.SYS_STATUS_SELLER_SEND_GOODS, t.getSysStatus()) || StringUtils.equals(Trade.SYS_STATUS_FINISHED, t.getSysStatus()))
                    .collect(Collectors.toMap(Trade::getSid, java.util.function.Function.identity(), (a, b) -> b));
            List<LogisticsTrackingPollPool> unsignedRecords = new ArrayList<>();
            for (LogisticsTrackingPollPool logisticsInfo : logisticsInfosDb) {
                Trade trade = tradeMap.get(logisticsInfo.getSid());
                if (trade != null && allowSyncList.contains(trade.getSource())) {
                    TradeLogisticsTrackingUtils.fullTrade(logisticsInfo, trade);
                    unsignedRecords.add(logisticsInfo);
                }
            }
            if (CollectionUtils.isEmpty(unsignedRecords)) {
                pageNo++;
                page.setPageNo(pageNo);
                continue;
            }

            if (templateFlag) {
                List<UserExpressTemplate> expressTemplates = expressTemplateDubboService.getUserExpressWlbMixIncHidden(staff);
                expressTemplateMap.putAll(expressTemplates.stream().collect(Collectors.toMap(t -> t.getId() + "_" + t.getIsWlb(), t -> t, (k1, k2) -> k2)));
                templateFlag = false;
            }
            if (expressFlag) {
                expressCompanyMap.putAll(expressCompanyService.getExpressCompanyIdMap());
                expressFlag = false;
            }
            config = logisticsTrackingConfigService.get(staff);
            List<LogisticsTrackingPollPool> updateLogisticsList = new ArrayList<>();
            for (LogisticsTrackingPollPool logistics : unsignedRecords) {
                User user = getBranchUser(staff, logistics, branchUserMap);
                if(user ==null){
                    logger.debug(LogHelper.buildLogHead(staff).append("查询物流失败！sid:").append(logistics.getSid()).append("错误信息:").append("未获取到user!"));
                    continue;
                }
                try {
                    LogisticsTrackingPollPool trackingPollPool = platformManagement.getAccess(logistics.getSource(), PlatformLogisticsTrackingService.class).
                            queryPlatformLogisticInfo(user, buildRequest(staff, expressTemplateMap, expressCompanyMap, logistics));
                    if (trackingPollPool != null) {
                        updateLogisticsList.add(afterSet(staff, logistics, trackingPollPool, config));
                    }
                } catch (Exception e) {
                    logger.debug(LogHelper.buildLogHead(staff).append("多平台").append(user.getSource()).append("查询物流失败！sid:").append(logistics.getSid()).append("错误信息:").append(e.getMessage()));
                }
            }
            if (CollectionUtils.isNotEmpty(updateLogisticsList)) {
                succCount += logisticsTrackingPollPoolDao.batchUpdate(staff, updateLogisticsList);
            }
            totalCount += unsignedRecords.size();
            pageNo++;
        }
        logger.debug(LogHelper.buildLogHead(staff).append(" 跟新总记录数:").append(totalCount).append(" 跟新成功记录数:").append(succCount));
    }

    /**
     * 获取物流追踪的单号的实际的店铺信息 老数据可能会不准的
     */
    private User getBranchUser(Staff staff, LogisticsTrackingPollPool logistics, Map<String, User> branchUserMap) {
        //先判断数据里是否有网点对应的店铺编号 后面做好了新数据就有
        Long branchUserId = logistics.getBranchUserId();
        if (branchUserId != null && branchUserId > 0) {
            User user = branchUserMap.get(branchUserId.toString());
            if (user == null) {
                user = staff.getUserByUserId(branchUserId);
            }
            return user;
        }
        //下面是兼容现在的数据或者说是老数据的
        Long warehouseId = logistics.getWarehouseId();
        Long templateId = logistics.getTemplateId();
        if (warehouseId == null || warehouseId < 1 || templateId == null || templateId < 1) {
            return staff.getUserByUserId(logistics.getUserId());
        }
        String key = warehouseId + "_" + templateId;
        User user = branchUserMap.get(key);
        if (user == null) {
            WarehouseTemplate warehouseTemplate = warehouseTemplateDAO.getWarehouseTemplateByWarehouseAndTemplate(staff, warehouseId, templateId);
            if (warehouseTemplate == null) {
                logger.debug(LogHelper.buildLog(staff, "历史数据 物流追踪 仓库id:" + warehouseId + " 模板id:" + templateId + "未查询到仓库绑定模板 使用订单对应的user!"));
                user = staff.getUserByUserId(logistics.getUserId());
                branchUserMap.put(key, user);
            } else {
                List<UserWlbExpressTemplate> wlbExpressTemplates = userWlbExpressTemplateService.getUserExpressTemplateIdNameExpressNeme(staff,
                        new Long[]{warehouseTemplate.getAddressTemplateId()});
                if (CollectionUtils.isEmpty(wlbExpressTemplates)) {
                    logger.debug(LogHelper.buildLog(staff, "历史数据 物流追踪 仓库id:" + warehouseId + " 模板id:" + templateId + "未查询到仓库绑定网点对应的模板使用订单对应的user!"));
                    user = staff.getUserByUserId(logistics.getUserId());
                    branchUserMap.put(key, user);
                } else {
                    UserWlbExpressTemplate wlbExpressTemplate = wlbExpressTemplates.get(0);
                    logger.debug(LogHelper.buildLog(staff, "历史数据 物流追踪 仓库id:" + warehouseId + " 模板id:" + templateId + "网点模板Id:" + wlbExpressTemplate.getId() +
                            "对应的平台店铺id:" + wlbExpressTemplate.getTaobaoId()) + "找到仓库现在绑定的店铺!");
                    user = staff.getUserByTaobaoId(wlbExpressTemplate.getTaobaoId());
                    branchUserMap.put(key, user);
                }
            }
        }
        return user;
    }

    private LogisticsTrackingPollPool afterSet(Staff staff, LogisticsTrackingPollPool origin, LogisticsTrackingPollPool update, LogisticsTrackingConfig config) {
        update.setSid(origin.getSid());
        update.setCompanyId(origin.getCompanyId());
        update.setLogisticsExceptType("");
        update.setUserId(origin.getUserId());
        if (StringUtils.isBlank(update.getLogisticsStatus())) {
            int apartTime = DateUtils.differentHoursByMillisecond(origin.getLogisticsModified() != null && origin.getLogisticsModified().after(TradeTimeUtils.INIT_DATE) ? origin.getLogisticsModified() : origin.getConsignTime(), new Date());
            if (getStatusWeight(origin.getLogisticsStatus()) < LogisticsStatusEnum.ACCEPT.getKey()) {
                //未揽收
                Integer logisticsModifyDeadline = config.getLogisticsModifyDeadline();
                if (apartTime > logisticsModifyDeadline) {
                    logger.debug(LogHelper.buildLogHead(staff).append(origin.getSid()).append(" intervalHours=").append(apartTime).append("h ").append(LogisticsTrackingPollPool.GATHERED_CONFIRM_TIMEOUT));
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.GATHERED_CONFIRM_TIMEOUT);

                }
            } else if (getStatusWeight(origin.getLogisticsStatus()) == LogisticsStatusEnum.ACCEPT.getKey()) {
                Integer noTransformDeadline = config.getNoTransformDeadline();
                if (apartTime > noTransformDeadline) {
                    logger.debug(LogHelper.buildLogHead(staff).append(origin.getSid()).append(" intervalHours=").append(apartTime).append("h ").append(LogisticsTrackingPollPool.LOGISTICS_NOTRANSFORM_TIMEOUT));
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.LOGISTICS_NOTRANSFORM_TIMEOUT);

                }
            } else {
                Integer logisticsModifyDeadline = config.getGatheredConfirmDeadline();
                if (apartTime > logisticsModifyDeadline) {
                    logger.debug(LogHelper.buildLogHead(staff).append(origin.getSid()).append(" intervalHours=").append(apartTime).append("h ").append(LogisticsTrackingPollPool.LOGISTICS_MODIFY_TIMEOUT));
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.LOGISTICS_MODIFY_TIMEOUT);

                }
            }
            return update;
        }
        String logisticsStatus = update.getLogisticsStatus();
        Date lastTime = update.getLogisticsModified();
        if (LogisticsStatusEnum.SIGN.getValue().equals(logisticsStatus) || LogisticsStatusEnum.AGENT_SIGN.getValue().equals(logisticsStatus)) {
            //已签收或代签收
            update.setLogisticsStatus(LogisticsStatusEnum.SIGN.getValue());
            update.setLogisticsExceptType("");
            update.setLogisticsModified(lastTime);
        } else {
            int intervalHours = DateUtils.differentHoursByMillisecond(lastTime != null ? lastTime : origin.getLogisticsModified() != null && origin.getLogisticsModified().after(TradeTimeUtils.INIT_DATE) ? origin.getLogisticsModified() : origin.getConsignTime(), new Date());
            update.setLogisticsStatus(logisticsStatus);
            update.setLogisticsModified(lastTime);
            update.setLogisticsExceptType("");
            if (getStatusWeight(logisticsStatus) > LogisticsStatusEnum.ACCEPT.getKey()) {
                //在途中
                Integer logisticsModifyDeadline = config.getLogisticsModifyDeadline();
                if (intervalHours > logisticsModifyDeadline) {
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.LOGISTICS_MODIFY_TIMEOUT);
                }
            } else if (getStatusWeight(logisticsStatus) == LogisticsStatusEnum.ACCEPT.getKey()) {
                Integer noTransformDeadline = config.getNoTransformDeadline();
                if (intervalHours > noTransformDeadline) {
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.LOGISTICS_NOTRANSFORM_TIMEOUT);
                }
            } else {
                //未揽收
                Integer logisticsModifyDeadline = config.getGatheredConfirmDeadline();
                if (intervalHours >= logisticsModifyDeadline) {
                    update.setLogisticsExceptType(LogisticsTrackingPollPool.GATHERED_CONFIRM_TIMEOUT);
                }
            }
        }
        return update;
    }

    private int getStatusWeight(String status) {
        if (StringUtils.isEmpty(status)) {
            return -1;
        }
        return LogisticsStatusEnum.getEnum(status).getKey();
    }

    private LogisticsRequest buildRequest(Staff staff, Map<String, UserExpressTemplate> expressTemplateMap, Map<Long, ExpressCompany> expressCompanyMap, LogisticsTrackingPollPool logistics) {
        LogisticsRequest logisticsRequest = new LogisticsRequest();
        logisticsRequest.setSid(logistics.getSid());
        logisticsRequest.setExpressNo(logistics.getOutSid());
        UserExpressTemplate userExpressTemplate = expressTemplateMap.get(logistics.getTemplateId() + "_" + logistics.getTemplateType());
        if (userExpressTemplate == null) {
            logger.debug(LogHelper.buildLogHead(staff).append("多平台更新物流信息获取快递模版失败，快递单号:").append(logistics.getOutSid()));
            return logisticsRequest;
        }
        ExpressCompany expressCompany = expressCompanyMap.get(userExpressTemplate.getExpressId());
        logisticsRequest.setExpressCompany(expressCompany);
        return logisticsRequest;
    }

    private Integer getPddUploadStrategy(User user) {
        if (user == null) {
            return null;
        }
        UserConf userConf = user.getUserConf();
        if (userConf == null) {
            return null;
        }
        return userConf.getPddUploadStrategy();
    }

}

package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.AddressUtils;
import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.FxgOperationCodeEnum;
import com.raycloud.dmj.domain.enums.TradeDecryptSourceEnum;
import com.raycloud.dmj.domain.request.SecurityEventTrackingBatchOrderRequest;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.utils.TradeConvertUtils;
import com.raycloud.dmj.domain.trades.utils.TradeCopier;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.PlatformAddressUpdateBusiness;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.SignUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FxgSpiService {

    @Resource
    private IUserService userService;
    @Resource
    private IStaffService staffService;
    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private ITradeUpdateService tradeUpdateService;
    @Resource
    ITradeMergeService tradeMergeService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;
    @Resource
    private PlatformManagement platformManagement;



    private static final String APP_KEY = "6839207088506488334";

    private static final String APP_SECRET = "982ca8f5-38d8-4be5-988c-7e25ec0b829e";

    public JSONObject addressModify(HttpServletRequest request,SecurityEventTrackingBatchOrderRequest securityEventTrackingBatchOrderRequest) throws IOException {
        String contentType = request.getContentType();
        String charset = request.getCharacterEncoding();
        if (null == charset) {
            charset = "UTF-8";
        }
        String logId = request.getHeader("Log-Id");
        Enumeration enumerations = request.getHeaderNames();
        Map<String, String> headMap = new HashMap<>();
        while (enumerations.hasMoreElements()) {
            String key = (String)enumerations.nextElement();
            String value = request.getHeader(key);
            headMap.put(key, value);
            if (null == logId && "log-id".equalsIgnoreCase(key)) {
                logId = value;
            }
        }
        String orderId = request.getParameter("order_id");
        String shopId = request.getParameter("shop_id");
        if (StringUtils.isNotBlank(shopId)) {
            List<User> users = userService.queryUsersByTaobaoId(Long.valueOf(shopId));
            Logs.info(LogHelper.buildUserLog(users.get(0), String.format("抖店spi改地址，logId:%s,orderId:%s", logId, orderId)));
        }
        Map<String, String> queryMap = getQueryMap(request, charset);
        String queryMessage = JSON.toJSONString(queryMap);
        String paramString;
        if (!contentType.startsWith("application/json") && !contentType.startsWith("text/xml") && !contentType.startsWith("application/xml") && !contentType.startsWith("text/plain")) {
            if (!contentType.startsWith("application/x-www-form-urlencoded")) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", FxgOperationCodeEnum.PARAMS_ERROR.getCode());
                jsonObject.put("message", "Unspported SPI request");
                return jsonObject;
            }
            paramString = JSON.toJSONString(getFormMap(request, queryMap));

        } else {
            paramString = getStreamAsString(request.getInputStream(), charset);
        }
        String sign = queryMap.get("sign");
        String currentSign = SignUtil.spiSign(APP_KEY, APP_SECRET, queryMap.get("timestamp"), paramString, SignUtil.SIGN_METHOD_MD5);
        log.info(String.format("抖店spi改地址，logId=%s, sign=%s, currentSign=%s, query=%s", logId, sign, currentSign, queryMessage));
        if (StringUtils.equals(sign, currentSign)) {
            return doAddressModify(JSONObject.parseObject(paramString), securityEventTrackingBatchOrderRequest);
        } else {
            return buildResponse(FxgOperationCodeEnum.SIGN_FAIL);
        }

    }

    private JSONObject doAddressModify(JSONObject paramJson,SecurityEventTrackingBatchOrderRequest securityEventTrackingBatchOrderRequest) {
        Long shopId = paramJson.getLong("shop_id");
        String orderId = paramJson.getString("order_id");
        JSONObject toReceiverInfo = paramJson.getJSONObject("to_receiver_info");
        String receiverName = toReceiverInfo.getString("encrypt_post_receiver");
        String receiverMobile = toReceiverInfo.getString("encrypt_post_tel");
        JSONObject postAddress = toReceiverInfo.getJSONObject("post_address");
        String province = postAddress.getJSONObject("province").getString("name");
        String city = postAddress.getJSONObject("city").getString("name");
        String town = postAddress.getJSONObject("town").getString("name");
        String street = postAddress.getJSONObject("street").getString("name");
        String receiverAddress = postAddress.getString("encrypt_detail");
        log.info(String.format("抖店spi改地址，shopId=%s, orderId=%s",shopId,orderId));
        if (null == shopId || StringUtils.isBlank(orderId)) {
            return buildResponse(FxgOperationCodeEnum.PARAMS_ERROR);
        }
        List<User> users = userService.queryUsersByTaobaoId(shopId);
        if (CollectionUtils.isEmpty(users)) {
            return buildResponse(FxgOperationCodeEnum.PARAMS_ERROR);
        }
        List<User> fxgUser = users.stream().filter(u-> CommonConstants.PLAT_FORM_TYPE_FXG.equals(u.getSource())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fxgUser)) {
            return buildResponse(FxgOperationCodeEnum.PARAMS_ERROR);
        }
        Staff staff = staffService.queryDefaultStaffByCompanyId(fxgUser.get(0).getCompanyId());
        List<TbTrade> tradeList = tradeSearchService.queryByTids(staff, false, orderId);
        if (CollectionUtils.isEmpty(tradeList)) {//兼容带A
            tradeList = tradeSearchService.queryByTids(staff, false, orderId + TradeConstants.FXG_SUFFIX);

        }
        if (CollectionUtils.isEmpty(tradeList)) {
            return buildResponse(FxgOperationCodeEnum.ORDER_NOT_EXIST);
        }
        List<Trade> toUpdates = new ArrayList<Trade>();
        List<Trade> originMergeTrades = new ArrayList<>();
        Trade uploadAddressTrade = null;
        for (Trade originTrade : tradeList) {
            //待付款、待审核订单地址可修改
            if (TradeStatusUtils.isWaitPay(originTrade.getSysStatus()) || TradeStatusUtils.isWaitAudit(originTrade.getSysStatus())) {
                TbTrade update = new TbTrade();
                update.setSid(originTrade.getSid());
                update.setTid(originTrade.getTid());
                update.setCompanyId(originTrade.getCompanyId());
                update.setReceiverState(province);
                update.setReceiverCity(city);
                update.setReceiverDistrict(town);
                update.setReceiverStreet(street);
                update.setReceiverAddress(receiverAddress);
                update.setReceiverName(receiverName);
                update.setReceiverMobile(receiverMobile);
                toUpdates.add(update);
                TradeExt tradeExt = new TradeExt();
                tradeExt.setSid(originTrade.getSid());
                tradeExt.setCompanyId(originTrade.getCompanyId());
                tradeExt.setReceiverNameIndex(CipherTextUtils.cutOutIndex(receiverName));
                tradeExt.setReceiverMobileIndex(CipherTextUtils.cutOutIndex(receiverMobile));
                tradeExt.setReceiverAddressIndex(CipherTextUtils.cutOutIndex(receiverAddress));
                tradeExt.setDesensitizationReceiverName("");
                tradeExt.setDesensitizationReceiverMobile("");
                tradeExt.setDesensitizationReceiverAddress("");
                update.setTradeExt(tradeExt);
                if (TradeUtils.isMerge(originTrade)) {
                    originMergeTrades.add(originTrade);
                }
                if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(originTrade.getSource()) && null == uploadAddressTrade) {
                    uploadAddressTrade = new TradeCopier<>().copy(originTrade, new Trade());
                    AddressUtils.coverAddress(update, uploadAddressTrade);
                }
            } else {
                if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(originTrade.getSysStatus())) {
                    return buildResponse(FxgOperationCodeEnum.ORDER_AUDIT);
                } else if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(originTrade.getSysStatus())) {
                    return buildResponse(FxgOperationCodeEnum.ORDER_SEND);
                } else if (Trade.SYS_STATUS_FINISHED.equals(originTrade.getSysStatus())) {
                    return buildResponse(FxgOperationCodeEnum.ORDER_FINISH);
                }
            }
        }
        if (null != uploadAddressTrade && CipherTextUtils.ifEncrypt(uploadAddressTrade)) {
            try {
                fxgTradeDecryptBusiness.batchDecrypt(staff, Lists.newArrayList(uploadAddressTrade), TradeDecryptSourceEnum.CHANGE_ADDRESS_DECRYPT,securityEventTrackingBatchOrderRequest);

                if (!CipherTextUtils.ifEncrypt(uploadAddressTrade)) {
                    User user = staff.getUserIdMap().get(uploadAddressTrade.getUserId());
                    PlatformResponse resp = platformManagement.getAccess(user.getSource(), PlatformAddressUpdateBusiness.class).update(user, uploadAddressTrade);
                    if (resp.isSuccess()) {
                        log.info("抖店spi改地址上传成功，订单号：" + uploadAddressTrade.getSid());
                    } else {
                        log.info("抖店spi改地址上传失败，订单号:" + uploadAddressTrade.getSid() + ";失败信息：" + resp.getMsg() + ";" + resp.getSubMsg());
                    }
                }
            } catch (Exception e) {
                log.info("抖店spi改地址上传失败，失败信息:"+ ExceptionUtils.getFullStackTrace(e));
            }
        }
        tradeUpdateService.updateTrades(staff, toUpdates);
        if (CollectionUtils.isNotEmpty(originMergeTrades)) {
            //被修改地址的订单取消合单
            tradeMergeService.mergeUndo(staff, originMergeTrades.stream().map(Trade::getMergeSid).filter(mergeSid -> mergeSid >0).collect(Collectors.toList()));
        }
        //发送事件
        eventCenter.fireEvent(this, new EventInfo("trade.updateShippingAddress").setArgs(new Object[]{staff}), toUpdates);

        // 更新电子面单号
        List<Trade> updateWayBillTrades = tradeList.stream().filter(originTrade -> TradeStatusUtils.isWaitPay(originTrade.getSysStatus()) || TradeStatusUtils.isWaitAudit(originTrade.getSysStatus())).collect(Collectors.toList());
        eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.update").setArgs(new Object[]{staff, TradeConvertUtils.buildSimpleTrades(updateWayBillTrades)}), null);

        return buildResponse(FxgOperationCodeEnum.SUCCESS);
    }

    private JSONObject buildResponse(FxgOperationCodeEnum codeEnum) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", codeEnum.getCode());
        jsonObject.put("message", codeEnum.getDesc());
        return jsonObject;
    }

    private String getStreamAsString(InputStream stream, String charset) throws IOException {
        try {
            Reader reader = new InputStreamReader(stream, charset);
            StringBuilder response = new StringBuilder();
            char[] buff = new char[1024];

            int read;
            while((read = reader.read(buff)) > 0) {
                response.append(buff, 0, read);
            }

            String paramString = response.toString();
            return paramString;
        } finally {
            if (stream != null) {
                stream.close();
            }

        }
    }

    private Map<String, String> getQueryMap(HttpServletRequest request, String charset) throws IOException {
        Map<String, String> queryMap = new HashMap();
        String queryString = request.getQueryString();
        String[] params = queryString.split("&");

        for(int i = 0; i < params.length; ++i) {
            String[] kv = params[i].split("=");
            String key;
            if (kv.length == 2) {
                key = URLDecoder.decode(kv[0], charset);
                String value = URLDecoder.decode(kv[1], charset);
                queryMap.put(key, value);
            } else if (kv.length == 1) {
                key = URLDecoder.decode(kv[0], charset);
                queryMap.put(key, "");
            }
        }

        return queryMap;
    }

    private Map<String, String> getFormMap(HttpServletRequest request, Map<String, String> queryMap) throws IOException {
        Map<String, String> formMap = new HashMap();
        Set<?> keys = request.getParameterMap().keySet();
        Iterator iterator = keys.iterator();

        while(iterator.hasNext()) {
            Object tmp = iterator.next();
            String key = String.valueOf(tmp);
            if (!queryMap.containsKey(key)) {
                String value = request.getParameter(key);
                if (StringUtils.isEmpty(value)) {
                    formMap.put(key, "");
                } else {
                    formMap.put(key, value);
                }
            }
        }

        return formMap;
    }
}

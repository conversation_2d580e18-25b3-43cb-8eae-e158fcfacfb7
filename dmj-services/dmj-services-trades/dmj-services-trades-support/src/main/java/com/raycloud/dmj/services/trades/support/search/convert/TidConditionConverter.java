package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.fx.FxSupplierSrcInfo;
import com.raycloud.dmj.domain.trades.search.TidConditionProps;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.fx.IFxSupplierSrcService;
import com.raycloud.dmj.tradepush.api.service.IQimenTradeDubboService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_TID)
public class TidConditionConverter extends AbsConditionConverter{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private IQimenTradeDubboService qimenTradeDubboService;

    @Resource
    private IFxSupplierSrcService fxSupplierSrcService;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query) {
        return  request.getTids() != null &&  request.getTids().length > 0;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if ( condition.getTids() == null ||  condition.getTids().length == 0) {
            return;
        }
        buildTidQuery(staff,q, condition.getTids(),context.userSourceMap,condition.getTidProps());
    }

    public void buildTidQuery(Staff staff,Query q, String[] tids, Map<String, List<User>> userSourceMap, TidConditionProps... tidProps){
        if (tidProps == null || tidProps.length == 0) {
            tidProps = new TidConditionProps[]{TidConditionProps.STRICT};
        }

        List<TidConditionProps> props = Arrays.asList(tidProps);
        List<String> list = new ArrayList<>(Arrays.asList(tids));
        List<String> converted = new ArrayList<>();

        if (props.contains(TidConditionProps.SOURCE_TID)) {
            converted = convertOrgiTids(staff,userSourceMap,list,true);
        }

        List<String> lazadas = new ArrayList<>();
        if (userSourceMap.containsKey(CommonConstants.PLAT_FORM_TYPE_LAZADA)) {
            List<User> users = userSourceMap.get(CommonConstants.PLAT_FORM_TYPE_LAZADA);
            for (User user : users) {
                for (String t: list) {
                    String s = t + "-" + user.getTaobaoId();
                    lazadas.add(s);
                }
            }
        }

        List<String> ins = new ArrayList<>();
        ins.addAll(list);
        ins.addAll(converted);
        ins.addAll(lazadas);

        List<String> likes = new ArrayList<>();
        likes.addAll(list);
        likes.addAll(lazadas);

        if (props.contains(TidConditionProps.REF_TRADES) && likes.size() > 0) {
            Assert.isTrue(likes.size() < 1000,"REF_TRADES 模式下tid不能超过1000个");
            q.and().append("(");
            ConvertBase.listCondition(q,"t.tid", ins);
            likes = likes.stream().map(x->{ return  x+"-";}).collect(Collectors.toList());
            ConvertBase.orListCondition(q,"t.tid", likes,2);
            q.append(")");
        }else{
            ConvertBase.andListCondition(q,"t.tid", ins);
        }
    }


    /**
     * 奇门订单的tid实际存储的是上游系统的系统id而非平台本身的id 这里需要先将平台原始的id转换为上游的系统id
     * @param staff
     * @param userSourceMap
     * @param tidList
     */
    protected List<String> convertOrgiTids(Staff staff, Map<String, List<User>> userSourceMap, List<String> tidList,boolean throwWhenExpt) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(tidList)) {
            return result;
        }

        if ( userSourceMap != null && userSourceMap.containsKey(CommonConstants.PLAT_FORM_TYPE_QIMEN) && qimenTradeDubboService != null)  {
            try {
                List<Long> userIds = userSourceMap.get(CommonConstants.PLAT_FORM_TYPE_QIMEN).stream().map(x -> x.getId()).collect(Collectors.toList());
                Map<String, List<String>> map = qimenTradeDubboService.queryMapBySourceOrderCode(staff,userIds ,tidList);
                if (map.size() > 0) {
                    for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                        String key = entry.getKey();
                        //如果已经明确转换为奇门发货单号了 那么就不需要再用前端输入的tid去查了
                        tidList.remove(key);
                        result.addAll(entry.getValue());
                    }
                }

            }catch (Exception e){
                new QueryLogBuilder(staff).appendError("调用tradepush接口查询奇门订单原始单号对应tid失败",e).printWarn(logger,e);
                if (throwWhenExpt) {
                    throw new RuntimeException("调用tradepush接口查询奇门订单原始单号对应tid失败",e);
                }
            }
        }

        try {
            if (fxSupplierSrcService != null) {
                List<FxSupplierSrcInfo> fxSupplierSrcInfos = fxSupplierSrcService.queryByFxTids(staff, tidList.toArray(new String[0]));
                if (CollectionUtils.isNotEmpty(fxSupplierSrcInfos)) {
                    Set<String> collect = fxSupplierSrcInfos.stream().map(x -> x.getTid()).collect(Collectors.toSet());
                    tidList.addAll(collect);
                    for (FxSupplierSrcInfo fxSupplierSrcInfo : fxSupplierSrcInfos) {
                        String key = fxSupplierSrcInfo.getFxTid();
                        tidList.remove(key);
                        result.add(fxSupplierSrcInfo.getTid());
                    }
                }
            }
        }catch (Exception e){
            new QueryLogBuilder(staff).appendError("按源头分销单Tid查询对应tid失败",e).printWarn(logger,e);
            if (throwWhenExpt) {
                throw new RuntimeException("按源头分销单Tid查询对应tid失败",e);
            }
        }
        return result;
    }
}

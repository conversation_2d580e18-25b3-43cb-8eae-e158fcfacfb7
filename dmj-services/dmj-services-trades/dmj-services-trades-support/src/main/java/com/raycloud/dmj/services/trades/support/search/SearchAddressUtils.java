package com.raycloud.dmj.services.trades.support.search;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/5/7 13:13
 * @Description
 */
public class SearchAddressUtils {

    /**
     * 一个市对应的所有可能的省份
     */
    private static Map<String, List<String>> cityProvinceMap = new HashMap<>();

    /**
     * 一个区对应的所有可能的市
     */
    private static Map<String, List<String>> districtCityMap = new HashMap<>();

    //先写到代码里面
    static {
        for (ProvinceEnum p : ProvinceEnum.values()) {
            initData(StringUtils.trimToEmpty(p.getName()), p.getChildrens(), cityProvinceMap);
        }
        for (CityEnum c : CityEnum.values()) {
            initData(StringUtils.trimToEmpty(c.getName()), c.getChildrens(), districtCityMap);
        }
    }

    private static void initData(String name, String[] childrens, Map<String, List<String>> dataMap) {
        if (StringUtils.isNotEmpty(name) && childrens != null && childrens.length > 0) {
            for (String children : childrens) {
                children = StringUtils.trimToEmpty(children);
                if (StringUtils.isNotEmpty(children)) {
                    dataMap.computeIfAbsent(children, k -> new ArrayList<>()).add(name);
                }
            }
        }
    }

    /**
     * @param districtNames 区名
     * @return 返回districtName对应的市
     */
    public static List<String> getCityByDistrict(String... districtNames) {
        List<String> cityNames = new ArrayList<>();
        for (String districtName : districtNames) {
            List<String> citys = districtCityMap.get(districtName);
            if (CollectionUtils.isNotEmpty(citys)) {
                cityNames.addAll(citys);
            }
        }
        return cityNames;
    }

    /**
     * @param cityNames 市名
     * @return 返回cityName对应的省份
     */
    public static List<String> getProvinceByCity(String... cityNames) {
        List<String> provinceNames = new ArrayList<>();
        for (String cityName : cityNames) {
            cityName = StringUtils.trimToEmpty(cityName);
            if (StringUtils.isNotEmpty(cityName)) {
                List<String> provinces = cityProvinceMap.get(cityName);
                if (CollectionUtils.isNotEmpty(provinces)) {
                    provinceNames.addAll(provinces);
                }
            }
        }
        return provinceNames;
    }

    /**
     * @param districtNames 区名
     * @return 返回districtName对应的省
     */
    public static List<String> getProvinceByDistrict(String... districtNames) {
        List<String> cityNames = getCityByDistrict(districtNames);
        return CollectionUtils.isNotEmpty(cityNames) ? getProvinceByCity(cityNames.toArray(new String[0])) : new ArrayList<>();
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.logistics.UploadUtils;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.enums.*;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trade.rule.convert.OvertimeWarningConvertUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.vo.TradeOvertimeWarningVo;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.user.*;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TradeOvertimeWarningService implements ITradeOvertimeWarningService {

    public static final String OVERTIME_WARNING_ENABLE = "overtimeWarningEnable";
    public static final String OVERTIME_WARNING_TIME = "overtimeWarningTime";

    @Resource
    IUserService userService;

    @Resource
    IShopService shopService;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    ILockService lockService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    TradeSysLabelBusiness tradeTagUpdateBusiness;

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    ITradeRuleService tradeRuleService;

    @Resource
    ITradeTraceService tradeTraceService;

    @Override
    public List<TradeOvertimeWarningVo> getOvertimeWarningList(Staff staff) {
        List<User> users = userService.queryByCompanyId(staff.getCompanyId());
        List<TradeOvertimeWarningVo> vos = buildVos(staff.getCompanyId(), users);
        if (CollectionUtils.isEmpty(vos)) {
            return vos;
        }
        List<Long> userIds = new ArrayList<>();
        vos.forEach(vo -> userIds.add(vo.getUserId()));

        //fill 店铺信息
        List<Shop> shops = shopService.queryByUserIds(staff, userIds.toArray(new Long[0]));
        Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, o -> o));
        vos.forEach(o -> {
            if (shopMap.get(o.getUserId()) != null) {
                Shop shop = shopMap.get(o.getUserId());
                o.setShopName(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : shop.getTitle());
                if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(shop.getSource()) && CommonConstants.PLAT_FORM_TYPE_VIPSOV.equals(shop.getSubSource())) {
                    o.setSourceName(shop.getSubSourceName());
                } else {
                    o.setSourceName(shop.getSourceName());
                }
            }
        });
        return vos;
    }

    @Override
    public void update(Staff staff, TradeOvertimeWarningVo newWarning) {
        User user = userService.queryById(newWarning.getUserId());
        Assert.notNull(user, "找不到店铺");
        TradeOvertimeWarningVo oldWarning = buildVo(staff.getCompanyId(), user);
        //无变化
        if (oldWarning != null && NumberUtils.isEquals(newWarning.getEnableStatus(), oldWarning.getEnableStatus()) && NumberUtils.isEquals(newWarning.getTime(), oldWarning.getTime())) {
            return;
        }
        if (oldWarning == null) {
            add(staff, newWarning);
        } else {
            modify(staff, newWarning, oldWarning);
        }
    }

    private void add(Staff staff, TradeOvertimeWarningVo newWarning) {
        Map<String, Object> map = new HashMap<>();
        map.put(OVERTIME_WARNING_ENABLE, newWarning.getEnableStatus());
        map.put(OVERTIME_WARNING_TIME, newWarning.getTime());
        userService.updateUserConf(staff, newWarning.getUserId(), map);

        newWarning.setBusinessType(TradeBusinessRuleEnum.OVERTIME_WARNING_RULE.getBusinessId());
        newWarning.setName(NumberUtils.long2Str(newWarning.getUserId()));
        tradeRuleService.initTradeRule(staff, newWarning);
    }

    private void modify(Staff staff, TradeOvertimeWarningVo newWarning, TradeOvertimeWarningVo oldWarning) {
        Map<String, Object> map = new HashMap<>();
        if (newWarning.getEnableStatus() != null) {
            map.put(OVERTIME_WARNING_ENABLE, newWarning.getEnableStatus());
        } else {
            newWarning.setEnableStatus(NumberUtils.nvlInteger(oldWarning.getEnableStatus(), 0));
        }
        if (newWarning.getTime() != null) {
            map.put(OVERTIME_WARNING_TIME, newWarning.getTime());
        } else {
            newWarning.setTime(NumberUtils.nvlInteger(oldWarning.getTime(), 0));
        }
        userService.updateUserConf(staff, newWarning.getUserId(), map);
        //老的不存在，先保存一份老的数据
        oldWarning.setBusinessType(TradeBusinessRuleEnum.OVERTIME_WARNING_RULE.getBusinessId());
        oldWarning.setName(NumberUtils.long2Str(newWarning.getUserId()));
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, oldWarning);
        //更新
        tradeRuleService.modify(staff, OvertimeWarningConvertUtils.convert(staff, newWarning, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    @Override
    public void autoTag(Long companyId) {
        Staff staff = staffAssembleBusiness.getDefaultStaff(companyId);
        if (!TradeDiamondUtils.allowTradeOvertimeWarning(staff)) {
            Logs.ifDebug(LogHelper.buildLog(staff, "非白名单公司，且不在时间区间内，不执行超时预警任务，companyId=" + companyId));
            return;
        }
        Assert.notNull(staff, "执行超时预警任务失败，companyId:" + companyId + ",未查询到默认staff");
        staff.setName("系统");
        List<User> users = Optional.ofNullable(userService.queryByCompanyId(companyId)).orElse(new ArrayList<>());
        List<TradeOvertimeWarningVo> vos = buildVos(companyId, users);
        vos.removeIf(o -> o.getEnableStatus() != 1);
        Logs.ifDebug(LogHelper.buildLog(staff, "开始执行超时预警任务，执行店铺：" + vos.stream().map(TradeOvertimeWarningVo::getUserId).collect(Collectors.toList())));
        vos.forEach(o -> {
            try {
                executeAutoTag(staff, o);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, "店铺id：" + o.getUserId() + "执行超时预警报错:"), e);
            }
        });
    }

    private void executeAutoTag(Staff staff, TradeOvertimeWarningVo o) {

        Assert.isTrue(o.getTime() != null && o.getTime() > 0, "超时预警时间错误：" + o.getTime());
        Page page = new Page().setPageSize(100).setPageNo(1);
        Date now = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //系统订单unified_status这个字段为null
        Query q = new Query().append("company_id = ? and user_id = ? and sys_status not in (?,?) and (unified_status in (?,?,?) or unified_status is null) and LOCATE(?, IFNULL (`tag_ids`,'')) = 0 and timeout_action_time != ? and timeout_action_time <= ? and enable_status > 0")
                .add(o.getCompanyId()).add(o.getUserId()).add(Trade.SYS_STATUS_CLOSED).add(Trade.SYS_STATUS_FINISHED).add(TradeStatus.TB_WAIT_BUYER_PAY).add(TradeStatus.TB_SELLER_CONSIGNED_PART).add(Trade.SYS_STATUS_WAIT_SEND_GOODS).add(SystemTags.TAG_OVERTIME.getId()).add(TradeTimeUtils.INIT_DATE_STR).add(format.format(DateUtil.addDateByHour(now, o.getTime())));
        q.setTradeTable("trade_not_consign");
        List<TbTrade> trades;
        //先查sid，然后加锁再查一次全内容
        while (!(trades = tbTradeDao.queryTrades(staff, "sid", q, page, false, false)).isEmpty()) {
            Long[] sids = TradeUtils.toSids(trades);
            lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
                List<Trade> list = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, sids);
                //部分发货的订单要判断子订单的状态所以还得查一次子订单
                List<Trade> needOrderTrades = list.stream().filter(trade -> TradeStatus.TB_SELLER_CONSIGNED_PART.equals(trade.getStatus()) || TradeUtils.isSplit(trade)).collect(Collectors.toList());
                tradeAssembleBusiness.bindOrders(staff, needOrderTrades, true);

                List<Trade> needLabelTrades = list.stream()
                        .filter(trade -> checkIfNeedTag(staff, trade, o))
                        .collect(Collectors.toList());

                List<Trade> updateTrades = needLabelTrades.stream()
                        .map(trade -> {
                            Trade updateTrade = new TbTrade();
                            updateTrade.setSid(trade.getSid());
                            updateTrade.setTagIds(trade.getTagIds());
                            return updateTrade;
                        })
                        .collect(Collectors.toList());
                tradeTagUpdateBusiness.addTags(staff, needLabelTrades, OpEnum.TAG_UPDATE, Lists.newArrayList(SystemTags.TAG_OVERTIME));
                if (!updateTrades.isEmpty()) {
                    tradeUpdateService.updateTrades(staff, updateTrades);
                    List<TradeTrace> tradeTraceList = updateTrades
                            .stream()
                            .map(trade -> TradeTraceUtils.createTradeTraceWithTrade(staff, trade, OpEnum.OVER_TIME_WARNING.getName(), staff.getName(), new Date(), "订单已达到设置的超时预警时间，添加系统标签[即将超时]"))
                            .collect(Collectors.toList());
                    tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
                }
                return null;
            });
            page.setPageNo(page.getPageNo() + 1);
        }
    }

    private boolean checkIfNeedTag(Staff staff, Trade trade, TradeOvertimeWarningVo o) {
        //如果是手工订单 检测系统状态是否是已发货
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && TradeUtils.isAfterSendGoods(trade)) {
            return false;
        }
        if (StringUtils.contains(trade.getTagIds(), SystemTags.TAG_OVERTIME.getId().toString())) {
            // 如果订单已经有即将超时标签, 则无需重复处理
            return false;
        }
        //校验一遍订单是否平台已经上传发货
        return !UploadUtils.platformIsConsigned(staff, trade);
    }

    private List<TradeOvertimeWarningVo> buildVos(Long companyId, List<User> users) {
        List<TradeOvertimeWarningVo> vos = new ArrayList<>();
        for (User user : users) {
            TradeOvertimeWarningVo vo = buildVo(companyId, user);
            if (vo != null) {
                vos.add(vo);
            }
        }
        return vos;
    }

    private TradeOvertimeWarningVo buildVo(Long companyId, User user) {
        if (user == null || user.getUserConf() == null) {
            return null;
        }
        Map<String, Object> confAttrInfoMap = user.getUserConf().getConfAttrInfoIfEmpty();
        Object overtimeWarningEnable = confAttrInfoMap.get(OVERTIME_WARNING_ENABLE);
        if (overtimeWarningEnable == null) {
            return null;
        }
        TradeOvertimeWarningVo vo = new TradeOvertimeWarningVo();
        vo.setCompanyId(companyId);
        vo.setUserId(user.getId());
        vo.setEnableStatus(Integer.parseInt(String.valueOf(overtimeWarningEnable)));
        vo.setTime(Integer.parseInt(String.valueOf(confAttrInfoMap.get(OVERTIME_WARNING_TIME))));
        return vo;
    }
}

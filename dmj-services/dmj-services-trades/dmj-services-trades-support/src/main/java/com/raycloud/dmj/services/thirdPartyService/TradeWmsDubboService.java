package com.raycloud.dmj.services.thirdPartyService;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.IWmsTradeDubboService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022-04-27 14:12
 * @description： 交易调用仓储服务中间类
 * @modified By：
 * @version: $
 */
@Component
public class TradeWmsDubboService {

    private final Logger logger = Logger.getLogger(this.getClass());

    /**
     * 兼容逻辑,后期稳定后去除wmsService的调用逻辑
     */
    @Resource
    private IWmsService wmsService;

    @Resource
    private IWmsTradeDubboService wmsTradeService;


    public List<GoodsSectionOrderRecord> applyGoodsStock(Staff staff, List<Long> orderIds) {
        try {
            return wmsTradeService.applyGoodsStock(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("applyGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), orderIds)), e);
            return wmsService.applyGoodsStock(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> applyGoodsStock(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.applyGoodsStock(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("applyGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.applyGoodsStock(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> resumeGoodsStock(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.resumeGoodsStock(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("resumeGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.resumeGoodsStock(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> resumeGoodsAllocate(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.resumeGoodsAllocate(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("resumeGoodsAllocate,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.resumeGoodsAllocate(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> consumeGoodsStock(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.consumeGoodsStock(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("consumeGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.consumeGoodsStock(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> consumeGoodsAllocate(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.consumeGoodsAllocate(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("consumeGoodsAllocate,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.consumeGoodsAllocate(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> applyGoodsStock(Staff staff, Long[] orderIds, Boolean simpleHandle) {
        try {
            return wmsTradeService.applyGoodsStock(staff, orderIds, simpleHandle);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("applyGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.applyGoodsStock(staff, orderIds, simpleHandle);
        }
    }

    public List<GoodsSectionOrderRecord> consumeGoodsStock(Staff staff, Long[] orderIds, Boolean simpleHandle, Boolean forceUnfinishExcep) {
        try {
            return wmsTradeService.consumeGoodsStock(staff, orderIds, simpleHandle, forceUnfinishExcep);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("consumeGoodsStock,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.consumeGoodsStock(staff, orderIds, simpleHandle, forceUnfinishExcep);
        }
    }

    public List<GoodsSectionOrderRecord> consumeGoodsAllocate(Staff staff, Long[] orderIds, Boolean simpleHandle, Boolean forceUnfinishExcep) {
        try {
            return wmsTradeService.consumeGoodsAllocate(staff, orderIds, simpleHandle, forceUnfinishExcep);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("consumeGoodsAllocate,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.consumeGoodsAllocate(staff, orderIds, simpleHandle, forceUnfinishExcep);
        }
    }


    public List<GoodsSectionOrderRecord> cancelTradeConsign(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.cancelTradeConsign(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("cancelTradeConsign,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.cancelTradeConsign(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> cancelTradeConsignDeleteOrderRecords(Staff staff, Long[] orderIds) {
        try {
            return wmsTradeService.cancelTradeConsignDeleteOrderRecords(staff, orderIds);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("cancelTradeConsignDeleteOrderRecords,[copmanyId=%s,orderIds=%s]", staff.getCompanyId(), Arrays.asList(orderIds))), e);
            return wmsService.cancelTradeConsignDeleteOrderRecords(staff, orderIds);
        }
    }

    public List<GoodsSectionOrderRecord> consumeWholeGoodsStock(Staff staff, Long[] sids) {
        try {
            return wmsTradeService.consumeWholeGoodsStock(staff, sids);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("consumeWholeGoodsStock,[copmanyId=%s,sids=%s]", staff.getCompanyId(), Arrays.asList(sids))), e);
            return wmsService.consumeWholeGoodsStock(staff, sids);
        }
    }




}

package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import lombok.Data;

import java.util.List;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-05
 */
@Data
public class SceneLightQueryContext {

    private Staff staff;

    private SenceCodeEnum sceneCode;


    /**
     * COMMON 场景下 前端指定需要填充的内容
     */
    List<Integer> fieldGroup;


}

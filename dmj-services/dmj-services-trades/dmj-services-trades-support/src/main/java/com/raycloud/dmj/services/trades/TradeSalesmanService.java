package com.raycloud.dmj.services.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.EventNameConstants;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.TradeSalesmanDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.sales.SalesMan;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.sales.ISalesManService;
import com.raycloud.dmj.services.trade.tradeExt.TradeExtService;
import com.raycloud.dmj.services.trades.config.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 订单业务员
 * <AUTHOR>
 * @Date 2022/9/1
 */
@Service
public class TradeSalesmanService implements ITradeSalesmanService {

    @Resource
    TradeSalesmanDao tradeSalesmanDao;

    @Resource
    IStaffService staffService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    ICache cache;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IProgressService progressService;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    ITradeConfigNewService tradeConfigNewService;
    @Resource
    ISalesManService salesManService;
    @Resource
    protected IUserService userService;
    @Resource
    TradeExtService tradeExtService;

    public static final String QUERY_FILEDS =  "t.sid,t.tid,t.warehouse_id, t.is_excep,t.user_id,t.merge_sid,t.merge_type,t.source,t.sub_source,t.convert_type";

    @Override
    public List<TradeSalesman> queryBySids(Staff staff, List<Long> sids) {
        return tradeSalesmanDao.queryBySids(staff, sids);
    }

    @Override
    public void fill(Staff staff, List<Trade> trades) {
        boolean openSalesman = tradeConfigNewService.get(staff, TradeConfigEnum.OPEN_TRADE_SALESMAN).isOpen();
        if (CollectionUtils.isEmpty(trades) || !openSalesman) {
            return;
        }
        Set<Long> sids = new HashSet<>();
        for (Trade trade : trades) {
            sids.add(trade.getSid());
            // 合单
            if (trade.getMergeSid() != null && trade.getMergeSid() > 0) {
                sids.add(trade.getMergeSid());
            }
        }
        Map<Long, TradeSalesman> tradeSalesmanMap = buildTradeSalesmanMapBySid(staff, sids, new HashSet<>());
        Set<Long> salesmanIds = new HashSet<>();
        for (TradeSalesman tradeSalesman : tradeSalesmanMap.values()) {
            salesmanIds.add(tradeSalesman.getSalesmanId());
        }
        Map<Long, String> staffInfoMap = buildStaffInfoMap(staff, salesmanIds);
        for (Trade trade : trades) {
            // 填充默认值
            //trade.setSalesmanId(0L);
            TradeSalesman tradeSalesMan = tradeSalesmanMap.get(trade.getSid());
            if (MapUtils.isNotEmpty(staffInfoMap) && Objects.nonNull(tradeSalesMan)) {
                String staffName = staffInfoMap.get(tradeSalesMan.getSalesmanId());
                if (StringUtils.isNotBlank(staffName)) {
                    trade.setSalesmanId(tradeSalesMan.getSalesmanId());
                    trade.setSalesmanName(staffName);
                }
            }
        }
    }

    private Map<Long, String> buildStaffInfoMap(Staff staff, Set<Long> salesmanIds) {
        if (CollectionUtils.isEmpty(salesmanIds)) {
            return Collections.emptyMap();
        }
        Map<Long, String> staffNameMap = null;
        try {
            staffNameMap = cache.get("sales_man_staff_name_" + staff.getCompanyId());
            if (Objects.isNull(staffNameMap)) {
                staffNameMap = new HashMap<>();
            }
            // 当前登陆的员工不用查询
            staffNameMap.put(staff.getId(), staff.getName());
            // 过滤需要查询的员工ID
            Set<Long> needQuerySalesmanIds = filterNeedQuerSalesmanId(staffNameMap, salesmanIds);
            if (CollectionUtils.isNotEmpty(needQuerySalesmanIds)) {
                List<Staff> staffList = staffService.querySimpleStaffByIdList(staff, new ArrayList<>(needQuerySalesmanIds));
                if (CollectionUtils.isNotEmpty(staffList)) {
                    for (Staff ff : staffList) {
                        staffNameMap.put(ff.getId(), ff.getName());
                    }
                }
                // 缓存24小时
                cache.set("sales_man_staff_name_" + staff.getCompanyId(), staffNameMap, 24 * 60 * 60);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "【业务员】读取缓存信息失败:" + e.getMessage()));
        }
        return staffNameMap;
    }

    private Set<Long> filterNeedQuerSalesmanId(Map<Long, String> staffNameMap, Set<Long> salesmanIds) {
        Set<Long> result = new HashSet<>();
        for (Long id : salesmanIds) {
            if (!staffNameMap.containsKey(id)) {
                result.add(id);
            }
        }
        return result;
    }

    @Override
    public void saveTradeSalesman(Staff staff, List<TradeSalesman> tradeSalesmanList) {
        if(CollectionUtils.isEmpty(tradeSalesmanList)){
            return;
        }
        if (!tradeConfigNewService.get(staff, TradeConfigEnum.OPEN_TRADE_SALESMAN).isOpen()) {
            return;
        }
        Set<Long> salesmanIdSet = tradeSalesmanList.stream().map(TradeSalesman::getSalesmanId).collect(Collectors.toSet());
        Set<Long> sidSet = tradeSalesmanList.stream().map(TradeSalesman::getSid).collect(Collectors.toSet());
        List<TradeSalesman> deleteList = new ArrayList<>();
        List<TradeSalesman> insertList = new ArrayList<>();
        List<TradeTrace> tradeTraceList = new ArrayList<>();
        // 查询订单所有的记录
        Map<Long, TradeSalesman> tradeSalesmanMap = buildTradeSalesmanMapBySid(staff, sidSet, salesmanIdSet);
        // 查询所有记录的员工信息
        Map<Long, String> staffInfoMap = buildStaffInfoMap(staff, salesmanIdSet);
        tradeSalesmanList.forEach(tradeSalesman -> {

            TradeSalesman oldTradeSalesman = tradeSalesmanMap.get(tradeSalesman.getSid());

            if (NumberUtils.negative2Zero(tradeSalesman.getSalesmanId()) <= 0 && Objects.isNull(oldTradeSalesman)) {
                // 不存在指定业务员
                // 则判断是否存在旧业务员, 不存在则新增为当前操作人员
                tradeSalesman.setSalesmanId(staff.getId());
            }

            if (Objects.isNull(oldTradeSalesman)) {
                // 如果不存在旧业务员
                if (Objects.equals(tradeSalesman.getEnableStatus(), 1)) {
                    // 新业务员状态为新增
                    insertList.add(tradeSalesman);
                    tradeTraceList.add(TradeTraceUtils.createTradeTrace(staff, tradeSalesman.getSid(), null, OpEnum.TRADE_SALESMAN_ADD.getName(), "匹配业务员：" + StringUtils.defaultIfBlank(staffInfoMap.get(tradeSalesman.getSalesmanId()), "系统管理员")));
                }
            }else {
                // 如果存在旧业务员
                if (NumberUtils.negative2Zero(tradeSalesman.getSalesmanId()) > 0
                        && !Objects.equals(oldTradeSalesman.getSalesmanId(), tradeSalesman.getSalesmanId())
                        && Objects.equals(tradeSalesman.getEnableStatus(), 1)) {
                    // 新旧业务员不同,并且新业务员状态为新增
                    deleteList.add(oldTradeSalesman);
                    insertList.add(tradeSalesman);
                    tradeTraceList.add(TradeTraceUtils.createTradeTrace(staff, tradeSalesman.getSid(), null, OpEnum.TRADE_SALESMAN_UPDATE.getName(), "修改业务员，修改前：" + StringUtils.defaultIfBlank(staffInfoMap.get(oldTradeSalesman.getSalesmanId()), "系统管理员") + "，修改后：" + StringUtils.defaultIfBlank(staffInfoMap.get(tradeSalesman.getSalesmanId()), "系统管理员")));
                }else if (Objects.equals(oldTradeSalesman.getSalesmanId(), tradeSalesman.getSalesmanId()) && Objects.equals(tradeSalesman.getEnableStatus(), 0)) {
                    // 新旧业务员相同, 并且状态为删除
                    // 需要删除的  取消拆单的情况
                    deleteList.add(oldTradeSalesman);
                }

            }
        });

        if (!CollectionUtils.isEmpty(deleteList)) {
            tradeSalesmanDao.batchDelete(staff, deleteList);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            tradeSalesmanDao.batchInsert(staff, insertList);
        }
        // 记录操作日志
        if (!CollectionUtils.isEmpty(tradeTraceList)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
        }
        // 发出业务员变更事件 售后需要
        eventCenter.fireEvent(this, new EventInfo(EventNameConstants.TRADE_SALESMAN_UPDATE).setArgs(new Object[]{staff, sidSet}), null);
    }

    @Override
    public void saveTradeDefaultSalesmanIfNotExist(Staff staff, List<Trade> trades) {
        if(CollectionUtils.isEmpty(trades)){
            return;
        }
        if (!TradeConfigGetUtil.get(staff, TradeConfigEnum.OPEN_TRADE_SALESMAN).isOpen()) {
            return;
        }
        List<Long> sids = TradeUtils.toSidList(trades);
        Map<Long, TradeSalesman> tradeSalesmanMap = tradeSalesmanDao.querySid2SalesmanMapBySids(staff, sids);

        List<TradeSalesman> insertList = new ArrayList<>();
        List<TradeSalesman> deleteList = new ArrayList<>();
        List<TradeTrace> tradeTraceList = new ArrayList<>();

        for (Trade trade : trades) {
            TradeSalesman oldTradeSalesman = tradeSalesmanMap.get(trade.getSid());
            if (Objects.isNull(oldTradeSalesman)) {
                // 如果不存在业务员
                TradeSalesman tradeSalesman = TradeSalesmanUtils.buildSalesman(staff, trade);
                insertList.add(tradeSalesman);
            }else {
                if (NumberUtils.negative2Zero(trade.getSalesmanId()) > 0) {
                    // 如果指定了业务员
                    TradeSalesman tradeSalesman = TradeSalesmanUtils.buildSalesman(staff, trade);
                    insertList.add(tradeSalesman);

                    // 删除旧业务员
                    deleteList.add(oldTradeSalesman);
                }
            }
        }
        if (!CollectionUtils.isEmpty(deleteList)) {
            tradeSalesmanDao.batchDelete(staff, deleteList);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            tradeSalesmanDao.batchInsert(staff, insertList);
            Set<Long> salesmanIds = insertList.stream().map(TradeSalesman::getSalesmanId).collect(Collectors.toSet());
            Map<Long, String> staffInfoMap = buildStaffInfoMap(staff, salesmanIds);
            for (TradeSalesman tradeSalesman : insertList) {
                tradeTraceList.add(TradeTraceUtils.createTradeTrace(staff, tradeSalesman.getSid(), null, OpEnum.TRADE_SALESMAN_ADD.getName(), "匹配业务员：" + StringUtils.defaultIfBlank(staffInfoMap.get(tradeSalesman.getSalesmanId()), "系统管理员")));
            }
        }

        // 记录操作日志
        if (!CollectionUtils.isEmpty(tradeTraceList)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
        }
        // 发出业务员变更事件 售后需要
        eventCenter.fireEvent(this, new EventInfo(EventNameConstants.TRADE_SALESMAN_UPDATE).setArgs(new Object[]{staff, new HashSet<>(sids)}), null);
    }

    private Map<Long, TradeSalesman> buildTradeSalesmanMapBySid(Staff staff, Set<Long> sids, Set<Long> salesmanIdSet) {
        if (CollectionUtils.isEmpty(sids)) {
            return Collections.emptyMap();
        }
        List<TradeSalesman> tradeSalesmanList = tradeSalesmanDao.queryBySids(staff, new ArrayList<>(sids));
        Map<Long, TradeSalesman> tradeSalesmanMap = new HashMap<>();
        for (TradeSalesman tradeSalesman : tradeSalesmanList) {
            tradeSalesmanMap.put(tradeSalesman.getSid(), tradeSalesman);
            salesmanIdSet.add(tradeSalesman.getSalesmanId());
        }
        return tradeSalesmanMap;
    }


    @Override
    public void batchUpdateSalesman(Staff staff, TradeSalesManBusinessContext context) {
        List<Trade> trades = queryTrades(staff, context.queryParams);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        int countAll = trades.size() + 1;
        updateProgress(staff, context.progressData, countAll, 0, 0, 0);

        List<List<Trade>> groupTrades = ListUtils.splitList(trades, 500);
        for (List<Trade> subTrades : groupTrades) {
            int incCountCurrent = subTrades.size(), incSucNum = 0;
            try {
                doBatchUpdateSalesman(staff, subTrades,context);
                incSucNum = subTrades.size();
                if( ( null!= context.queryParams.getSid() && context.queryParams.getSid().length()>0 )  || !StringUtils.isEmpty(context.queryParams.getMixKey()) ){
                    context.progressData.getRefreshSids().addAll(subTrades.stream().map(x->x.getSid().toString()).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                String errorMsg = String.format("%s 出错[sids=%s]", context.progressEnum.getMsg(), TradeUtils.toSidList(subTrades));
                Logs.error(LogHelper.buildLog(staff, errorMsg), e);
                if (null!= context.progressData){
                    context.progressData.getErrorMsg().add(errorMsg);
                }
            }
            updateProgress(staff, context.progressData, countAll, incCountCurrent, incSucNum, incCountCurrent - incSucNum);
        }
    }

    private List<Trade> queryTrades(Staff staff, TradeControllerParams params) {
        TradeQueryParams queryParams = TradeQueryParams.copyParams(params)
                .setFields(QUERY_FILEDS)
                .setQueryOrder(false)
                .setPage(new Page().setPageNo(1).setPageSize(50000));
        return queryParams.getSid().length>0  ? tradeSearchService.queryBySids(staff, false, queryParams.getSid()) : tradeSearchService.search(staff, queryParams).getList();
    }

    private void updateProgress(Staff staff, ProgressData progressData, int countAll, int incCountCurrent, int incSucNum, int incErrorNum) {
        if(null == progressData){
            return;
        }
        if (progressData.getCountAll()==null){
            progressData.setCountAll(0);
        }
        if (progressData.getCountCurrent()==null){
            progressData.setCountCurrent(0);
        }
        if (progressData.getSucNum()==null){
            progressData.setSucNum(0L);
        }
        if (progressData.getErrorNum()==null){
            progressData.setErrorNum(0L);
        }
        progressData.setCountAll(countAll)
                .setCountCurrent(progressData.getCountCurrent() + incCountCurrent)
                .setSucNum(progressData.getSucNum() + incSucNum)
                .setErrorNum(progressData.getErrorNum() + incErrorNum);
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_BATCH_UPDATE_SALESMAN, progressData);
    }

    private void doBatchUpdateSalesman(Staff staff, List<Trade> subTrades, TradeSalesManBusinessContext context) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false,false,true, TradeUtils.toSids(subTrades));
        List<Long> sidList = TradeUtils.toSidList(trades);
        if (CollectionUtils.isEmpty(sidList)) return ;
        List<TradeSalesman> toDeletes = new ArrayList<>();
        List<TradeSalesman> toInserts = new ArrayList<>();

        List<TradeSalesman> beforeList = tradeSalesmanDao.queryBySids(staff, sidList);
        Set<Long> beforeSidSet = new HashSet<>();
        Set<Long> beforeSalesmanIdSet = new HashSet<>();
        for(TradeSalesman b:beforeList){
            beforeSidSet.add(b.getSid());
            beforeSalesmanIdSet.add(b.getSalesmanId());
            if(null !=b.getSalesmanId() && !context.salesmanId.equals(b.getSalesmanId())){
                toDeletes.add(b);

                TradeSalesman temp = new TradeSalesman();
                temp.setCompanyId(b.getCompanyId());
                temp.setSid(b.getSid());
                temp.setSalesmanId(context.salesmanId);
                toInserts.add(temp);
            }
        }
        List<TradeSalesman> toInsertsNotInDb = new ArrayList<>();
        for(Long sid:sidList){
            if(!beforeSidSet.contains(sid)){
                TradeSalesman temp = new TradeSalesman();
                temp.setCompanyId(staff.getCompanyId());
                temp.setSid(sid);
                temp.setSalesmanId(context.salesmanId);
                toInsertsNotInDb.add(temp);
                toInserts.add(temp);
            }
        }

        if(CollectionUtils.isNotEmpty(toDeletes)){
            tradeSalesmanDao.batchDelete(staff,toDeletes);
        }
        if(CollectionUtils.isNotEmpty(toInserts)){
            tradeSalesmanDao.batchInsert(staff,toInserts);
        }

        beforeSalesmanIdSet.add(context.salesmanId);
        beforeSalesmanIdSet.removeAll(context.salesManCache.keySet());
        List<Staff> staffList = staffService.querySimpleStaffByIdList(staff, new ArrayList<>(beforeSalesmanIdSet));
        for (Staff salesman:staffList){
            context.salesManCache.putIfAbsent(salesman.getId(),salesman.getName());
        }
        List<Trade> tradeTraces = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(toDeletes)){
            for(TradeSalesman d:toDeletes){
                String content = String.format("批量指定业务员，由{%s}更新为{%s}",context.salesManCache.get(d.getSalesmanId()),context.salesManCache.get(context.salesmanId));
                Trade trade = new TbTrade();
                trade.setSid(d.getSid());
                trade.getOperations().put(OpEnum.TRADE_BATCH_UPDATE_SALESMAN,  content);
                tradeTraces.add(trade);
            }
        }
        if(CollectionUtils.isNotEmpty(toInsertsNotInDb)){
            for(TradeSalesman n:toInsertsNotInDb){
                String content = String.format("批量指定业务员，由{无}更新为{%s}",context.salesManCache.get(context.salesmanId));
                Trade trade = new TbTrade();
                trade.setSid(n.getSid());
                trade.getOperations().put(OpEnum.TRADE_BATCH_UPDATE_SALESMAN,  content);
                tradeTraces.add(trade);
            }
        }
        if (CollectionUtils.isNotEmpty(tradeTraces)) {
            tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.TRADE_BATCH_UPDATE_SALESMAN);
        }
    }

    /**
     * 查出国际站待发货的所有订单，重新计算业务员
     */
    @Override
    public void updateALiBaBaICBUTradeSalesMan(Staff staff, SalesMan salesMan) {
        //根据source,companyId,enableStatus找到所有的UserId
        Long[] userIdList = getUsers(staff);
        if (null==userIdList || userIdList.length == 0) {
            return;
        }

        //查询国际站店铺中待发货的订单 source=CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU and sysStatus=待发货 and companyId='' and enableStatus=1 and userId in (userIds)
        TradeQueryParams params = new TradeQueryParams();
        params.setPage(new Page(1, 2000));
        params.setQueryOrder(false);
        params.setQueryFlag(1);
        params.setFields("t.sid");
        params.setUserIds(userIdList);
        params.setSource(CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU);
        params.setEnableStatus(1);
        params.setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY,Trade.SYS_STATUS_WAIT_AUDIT,Trade.SYS_STATUS_WAIT_FINANCE_AUDIT,
                Trade.SYS_STATUS_WAIT_MANUAL_AUDIT,Trade.SYS_STATUS_FINISHED_AUDIT);
        params.setIgnoreFilter(1);
        List<Trade> tradeList = tradeSearchService.search(staff, params).getList();
        if (CollectionUtils.isEmpty(tradeList)) {
            return ;
        }

        //取出订单号，根据订单号查出业务员信息 转成Map 待用
        Set<Long> sidSet = tradeList.stream().map(Trade::getSid).collect(Collectors.toSet());
        List<TradeSalesman> tradeSalesmanList = tradeSalesmanDao.queryBySids(staff, new ArrayList<>(sidSet));
        if (CollectionUtils.isEmpty(tradeSalesmanList)) {
            return ;
        }
        Map<Long, TradeSalesman> tradeSalesmanMap = tradeSalesmanList.stream()
                .collect(Collectors.toMap(TradeSalesman::getSid, Function.identity()));

        //获取订单扩展信息，转成Map待用
        List<TradeExt> tradeExtList = tradeExtService.tradeExtsGetBySids(staff, new ArrayList<>(sidSet));
        if (CollectionUtils.isEmpty(tradeExtList)) {
            return ;
        }
        Map<Long, TradeExt> tradeExtMap = tradeExtList.stream()
                .collect(Collectors.toMap(TradeExt::getSid, Function.identity()));

        List<TradeSalesman> insertList = new ArrayList<>();
        List<TradeSalesman> deleteList = new ArrayList<>();
        List<TradeTrace> tradeTraceList = new ArrayList<>();

        //循环，判断订单是否已匹配业务员，已匹配的数据就需要重新匹配，并且收集起来，先删除后新增
        for (Trade trade:tradeList) {
            TradeSalesman salesman = tradeSalesmanMap.get(trade.getSid());
            if (null==salesman || null==salesman.getSalesmanId()) {
                continue;
            }
            TradeExt tradeExt = tradeExtMap.get(trade.getSid());
            if (tradeExt == null) {
                continue;
            }
            String extraFields = tradeExt.getExtraFields();
            if (StringUtils.isBlank(extraFields)) {
                continue;
            }
            JSONObject jsonObject;
            try {
                jsonObject = JSONObject.parseObject(extraFields);
            } catch (Exception e) {
                continue;
            }
            String fullName = jsonObject.getString("fullName");
            if (StringUtils.isBlank(fullName)) {
                continue;
            }
            if (!salesMan.getName().contains(fullName)) {
                continue;
            }
            //如果业务员没变化就不处理
            if (salesman.getSalesmanId().equals(salesMan.getStaffId())) {
                continue;
            }
            deleteList.add(salesman);
//            //复制数据
            copy(salesman, salesMan, insertList);
            String traceMsg = "根据业务员映射修改业务员为"+salesMan.getStaffName();
            tradeTraceList.add(TradeTraceUtils.createTradeTrace(
                    staff, trade.getSid(), null, OpEnum.TRADE_SALESMAN_UPDATE.getName(), traceMsg));
        }

        //删除旧数据
        if (CollectionUtils.isNotEmpty(deleteList)) {
            tradeSalesmanDao.batchDelete(staff, deleteList);
        }
        //新增数据
        if (CollectionUtils.isNotEmpty(insertList)) {
            tradeSalesmanDao.batchInsert(staff, insertList);
        }
        // 记录操作日志
        if (CollectionUtils.isNotEmpty(tradeTraceList)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
        }
        // 发出业务员变更事件 售后需要  暂时还没有对接售后
//        eventCenter.fireEvent(this, new EventInfo(EventNameConstants.TRADE_SALESMAN_UPDATE).setArgs(new Object[]{staff, sidSet}), null);
    }

    private Long[] getUsers(Staff staff) {
        List<User> list = userService.queryByCompanyId(staff.getCompanyId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //循环list，取id 组装成long数组
        return list.stream()
                .filter(user->CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(user.getSource()))
                .map(User::getId)
                .toArray(Long[]::new);
    }

    private void copy(TradeSalesman salesman, SalesMan salesMan, List<TradeSalesman> insertList) {
        TradeSalesman tradeSalesman = new TradeSalesman();
        tradeSalesman.setSid(salesman.getSid());
        tradeSalesman.setCompanyId(salesman.getCompanyId());
        tradeSalesman.setEnableStatus(salesman.getEnableStatus());
        tradeSalesman.setTable(salesman.getTable());
        tradeSalesman.setDbNo(salesman.getDbNo());
        tradeSalesman.setDbKey(salesman.getDbKey());
        tradeSalesman.setSalesmanId(salesMan.getStaffId());
        insertList.add(tradeSalesman);
    }

    /**
     * 处理业务员字段
     */
    @Override
    public void handleALiBaBaICBUSalesMan(Staff staff,User user,List<Trade> originCurrentTrades) {
        try {
            if (CollectionUtils.isEmpty(originCurrentTrades)) {
                return;
            }
            if (Objects.nonNull(user) && !CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(user.getSource())){
                return;
            }
            long alibabaIcbuOrderNum = originCurrentTrades.stream().filter(trade ->CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())).count();
            if (alibabaIcbuOrderNum == 0){
                return;
            }
            Map<String, Long> salesManStaffMap = buildSalesManStaffInfoMap(staff);
            if (salesManStaffMap == null || salesManStaffMap.isEmpty()){
                return;
            }
            Map<Long, String> staffNameMap = buildStaffInfoMap(staff, salesManStaffMap.values().stream().collect(Collectors.toSet()));
//            List<TradeTrace> tradeTraceList = new ArrayList<>();
            for (Trade trade : originCurrentTrades) {
                try {
                    //过滤掉国际站以外的数据
                    if (!trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU)) {
                        continue;
                    }
                    TradeExt tradeExt = trade.getTradeExt();
                    if (tradeExt == null) {
                        continue;
                    }
                    String extraFields = tradeExt.getExtraFields();
                    if (StringUtils.isBlank(extraFields)) {
                        continue;
                    }
                    JSONObject jsonObject;
                    try {
                        jsonObject = JSONObject.parseObject(extraFields);
                    } catch (Exception e) {
                        continue;
                    }
                    String fullName = jsonObject.getString("fullName");
                    if (StringUtils.isBlank(fullName)) {
                        continue;
                    }
                    Long staffId = salesManStaffMap.get(fullName);
                    if (null == staffId) {
                        continue;
                    }
                    trade.setSalesmanId(staffId);
                    trade.setSalesmanName(staffNameMap.getOrDefault(staffId,""));
//                    String traceMsg = "根据业务员映射修改业务员为"+salesMan.getStaffName();
//                    tradeTraceList.add(TradeTraceUtils.createTradeTrace(
//                            staff, trade.getSid(), null, OpEnum.TRADE_SALESMAN_UPDATE.getName(), traceMsg));
                } catch (Exception e) {
                    Logs.error(LogHelper.buildLog(staff, "TID:"+trade.getTid()+"【业务员】映射失败:" + e.getMessage()));
                }
            }
            // 记录操作日志
//            if (CollectionUtils.isNotEmpty(tradeTraceList)) {
//                tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
//            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, e.getMessage()));
        }
    }

    /**
     * 阿里国际站销售员映射关系
     * @param staff
     * @return
     */
    private Map<String,Long> buildSalesManStaffInfoMap(Staff staff) {
        if (Objects.isNull(staff)){
            return new HashMap<>();
        }
        Map<String,Long> salesManStaffMap = null;
        try {
            salesManStaffMap = cache.get("sales_man_staff_id_" + staff.getCompanyId());
            if (Objects.nonNull(salesManStaffMap)) {
                return salesManStaffMap;
            }
            salesManStaffMap = new HashMap<>();
            List<SalesMan> salesManList = salesManService.queryByCompanyId(staff.getCompanyId());
            if (CollectionUtils.isNotEmpty(salesManList)) {
                salesManStaffMap.putAll(salesManList.stream().filter(k->StringUtils.isNotBlank(k.getName()) && Objects.nonNull(k.getStaffId())).collect(Collectors.toMap(k->k.getName(),v->v.getStaffId(),(k1,k2)->k1)));
            }
            // 缓存24小时
            cache.set("sales_man_staff_id_" + staff.getCompanyId(), salesManStaffMap, 24 * 60 * 60);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "【阿里国际站业务员】读取缓存信息失败:" + e.getMessage()));
        }
        return salesManStaffMap;
    }
}

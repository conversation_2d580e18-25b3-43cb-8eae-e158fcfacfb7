package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.services.tag.ITradeTagService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/8/1 11:10 上午
 * @Description:
 */
@Service
@Deprecated
public class TradeNewExceptSqlQueryBuilder {

    @Resource
    private ITradeTagService tradeTagService;


    /**
     *  获取所有的自定义异常
     * @param staff
     * @return
     */
    private  List<Long> getSlefExceptIds(Staff staff){
        // 获取自定义异常
        List<TradeTag> list = tradeTagService.list(staff, 1);
        List<Long> newQueryExcept = list.stream().map(e->e.getId()).collect(Collectors.toList());
        // 添加第三方自定义异常
      //  newQueryExcept.addAll(ExceptEnum.getNewQueryExcept());
        return newQueryExcept;
    }

    /**
     * 获取不包含的自定义异常
     * @param staff
     * @param newExceptIds
     * @return
     */
    private  Set<Long> getNotContailExceptIds(Staff staff,Set<Long> newExceptIds) {
        List<Long> newQueryExcept =getSlefExceptIds(staff);
        Set<Long> notContailExceptIds = new TreeSet<>();
        for (Long exceptId : newQueryExcept) {
            if (!newExceptIds.contains(exceptId)) {
                notContailExceptIds.add(exceptId);
            }
        }
        return notContailExceptIds;
    }







    private  String buildLongInSql(Set<Long> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return null;
        }
        long count = objects.stream().filter(e -> !Objects.equals(e, -1L)).count();
        if(count==0){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (Object object : objects) {
            sb.append(object).append(",");
        }
        if (sb.length() > 0) {
            return sb.substring(0, sb.length() - 1);
        }
        return null;
    }

    /******************************************新异常查询基础SQL******************************************************************************/

    /**
     *  非合单包含sql
     * @param staff
     * @param q
     * @param insql
     * @return
     */
    private Query getNotMergeContailSql(Staff staff,  Query q,String insql){
        Query q0=new Query();
       /* if(StringUtils.isBlank(insql)){
            return q0;
        }
        StringBuilder sb=new StringBuilder();
        sb.append("exists ( select 1 from ").append(q.getTradeExceptTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? and t.sid = s.sid AND s.enable_status=1 and s.except_id in (").append(insql).append(")) ");
        q0.append(sb.toString()).add(staff.getCompanyId());*/
        return q0;
    }



    /**
     *  合单包含sql
     * @param staff
     * @param q
     * @param insql
     * @return
     */
    private Query getMergeContailSql(Staff staff, Query q,String insql){
        Query q0=new Query();
        if(StringUtils.isBlank(insql)){
            return q0;
        }
       /* StringBuilder sb = new StringBuilder();
        sb.append("exists ( select 1 from ").append(q.getTradeExceptTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" s where").append(" s.company_id = ? ");
        // 合单异常条件
        sb.append(" and s.sid in (").append("select sid from ").append(q.getTradeTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" t2 where t2.company_id=? and s.company_id=t2.company_id AND t2.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 ").append(") ");
        // 主条件
        sb.append(" AND s.enable_status=1 and s.except_id in (").append(insql).append("))");
        q0.append(sb.toString()).add(staff.getCompanyId()).add(staff.getCompanyId());*/
        return q0;
    }


    /**
     * 获取非合单排除的sql
     * @param staff
     * @param q
     * @param inSql
     * @return
     */
    private Query getNotMergeExcludeSql(Staff staff,  Query q,String inSql){
        Query q0=new Query();
       /* if(StringUtils.isBlank(inSql)){
            return q0;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" NOT EXISTS ( select 1 from ").append(q.getTradeExceptTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? and t.sid = s.sid AND s.enable_status=1 and s.except_id in (").append(inSql).append(")) ");
        q0.append(sb.toString()).add(staff.getCompanyId());*/
        return q0;
    }
    /**
     * 获取合单排除的sql
     * @param staff
     * @param q
     * @param inSql
     * @return
     */
    private Query getMergeExcludeSql(Staff staff,  Query q,String inSql){
        Query q0=new Query();
       /* if(StringUtils.isBlank(inSql)){
            return q0;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" NOT EXISTS ( select 1 from ").append(q.getTradeExceptTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" s where").append(" s.company_id = ? ");
        sb.append(" and s.sid in (").append("select sid from ").append(q.getTradeTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" t2 where t2.company_id=?  AND t2.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 ").append(") ");
        // 主条件
        sb.append(" AND s.enable_status=1 and s.except_id in (").append(inSql).append("))");
        q0.append(sb.toString()).add(staff.getCompanyId()).add(staff.getCompanyId());*/
        return q0;
    }






    /**
     *  非合单仅包含
     * @param staff
     * @param q
     * @param inSql
     * @return
     */
    private Query getNotMergeOnlyContailSql(Staff staff, Query q,String inSql,String notInSql,Set<Long> newExceptIds){
        Query q0=new Query();
        /* if(StringUtils.isBlank(inSql)){
            return q0;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" EXISTS (");
        sb.append("select 1 from ")
                .append(q.getTradeExceptTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? AND s.enable_status=1 and t.sid = s.sid and t.enable_status=1 ")
                .append(" and s.except_id in (").append(inSql).append(") ");
        if(StringUtils.isNotBlank(notInSql)){
            sb.append(" and s.except_id not in (").append(notInSql).append(")");
        }
        sb.append(")");
        q0.append(sb.toString()).add(staff.getCompanyId());*/
        return q0;
    }

    /**
     *  合单仅包含
     * @param staff
     * @param q
     * @param inSql
     * @param notInSql
     * @param newExceptIds
     * @return
     */
    private Query getMergeOnlyContailSql(Staff staff, Query q,String inSql,String notInSql,Set<Long> newExceptIds){
        Query q0=new Query();
       /* if(StringUtils.isBlank(inSql)){
            return q0;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" EXISTS (select 1 from ")
                .append(q.getTradeExceptTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? AND s.enable_status=1 ")
                .append(" and s.sid in (")
                .append("select sid from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" t2 where t2.company_id=?  AND t2.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 ")
                .append(")")
                .append(" and s.except_id in (").append(inSql).append(") ");
        if(StringUtils.isNotBlank(notInSql)){
            sb.append(" and s.except_id not in (").append(notInSql).append(")");
        }
        sb.append(")");
        q0.append(sb.toString()).add(staff.getCompanyId()).add(staff.getCompanyId());*/
        return q0;
    }

    /**
     * 非合单同时包含SQL
     * @param staff
     * @param q
     * @param inSql
     * @param newExceptIds
     * @return
     */
    private Query  getNotMergeContainAllSql(Staff staff, Query q,String inSql,Set<Long> newExceptIds){
        Query q0=new Query();
       /* StringBuilder sb = new StringBuilder();
        sb.append("((").append("select count(DISTINCT except_id) as count from ")
                .append(q.getTradeExceptTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? AND s.enable_status=1 and t.sid = s.sid and t.enable_status=1 ")
                .append(" and s.except_id in (").append(inSql).append(") ")
                .append(")=").append(newExceptIds.size()).append(")");
        q0.append(sb.toString()).add(staff.getCompanyId());*/
        return q0;
    }

    /**
     * 合单同时包含SQL
     * @param staff
     * @param q
     * @param inSql
     * @param newExceptIds
     * @return
     */
    private Query getMergeContainAllSql(Staff staff, Query q,String inSql,Set<Long> newExceptIds){
        Query q0=new Query();
       /* StringBuilder sb = new StringBuilder();
        sb.append("((select count(DISTINCT except_id) as count from ")
                .append(q.getTradeExceptTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" s where s.company_id = ? AND s.enable_status=1 ")
                .append(" and s.sid in (")
                .append("select sid from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                .append(" t2 where t2.company_id= ?  AND t2.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 ")
                .append(")")
                .append(" and s.except_id in (").append(inSql).append(") ")
                .append(")=").append(newExceptIds.size()).append(")");
        q0.append(sb.toString()).add(staff.getCompanyId()).add(staff.getCompanyId());*/
        return q0;
    }
    /******************************************新异常查询SQL******************************************************************************/
    /**
     *  自定义异常，包含sql
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptNotMergeContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isEmpty(exceptIds)){
            return q0;
        }
        Set<Long> newExceptIds=new TreeSet<>();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getNotMergeContailSql(staff, q, inSql);*/
        return q0;
    }

    /**
     *  自定义异常合单包含SQL
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptMergeContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isEmpty(exceptIds)){
            return q0;
        }
        Set<Long> newExceptIds=new TreeSet<>();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getMergeContailSql(staff, q, inSql);*/
        return q0;
    }

    /**
     *  系统异常非合单包含SQl
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptNotMergeContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
        /*String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<Long> needQueryExceptIds = getNewQueryContailSysExceptIds(staff, exceptions);

        String inSql = buildLongInSql(needQueryExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getNotMergeContailSql(staff, q, inSql);*/
        return q0;
    }



    /**
     *  系统异常合单包含SQL
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptMergeContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
        /*String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<Long> needQueryExceptIds = getNewQueryContailSysExceptIds(staff, exceptions);
        String inSql = buildLongInSql(needQueryExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getMergeContailSql(staff, q, inSql);*/
        return q0;
    }

    /***********************排除查询****************************/
    /**
     *  自定异常，非合单，排除SQL
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptNotMergeExcludeSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isEmpty(exceptIds)){
            return q0;
        }
        Set<Long> newExceptIds=new TreeSet<>();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getNotMergeExcludeSql(staff, q, inSql);*/
        return q0;
    }

    /**
     * 自定义异常合单排除查询
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptMergeExcludeSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
        /*String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isEmpty(exceptIds)){
            return q0;
        }
        Set<Long> newExceptIds=new TreeSet<>();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getMergeExcludeSql(staff, q, inSql);*/
        return q0;
    }
    /**
     *  系统异常，非合单，排除SQL
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptNotMergeExcludeSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] excludeExceptionStatus = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(excludeExceptionStatus)){
            return q0;
        }
        Set<Long> needQueryExceptIds = getNewQueryContailSysExceptIds(staff, excludeExceptionStatus);
        String inSql = buildLongInSql(needQueryExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getNotMergeExcludeSql(staff, q, inSql);*/
        return q0;
    }

    /**
     * 系统异常，合单，排除SQL
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptMergeExcludeSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] excludeExceptionStatus = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(excludeExceptionStatus)){
            return q0;
        }
        Set<Long> needQueryExceptIds = getNewQueryContailSysExceptIds(staff, excludeExceptionStatus);
        String inSql = buildLongInSql(needQueryExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getMergeExcludeSql(staff, q, inSql);*/
        return q0;
    }


    /********仅包含查询****************/
    /**
     * 自定义异常，非合单仅包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptNotMergeOnlyContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* Set<Long> newExceptIds=new TreeSet<>();
        String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isEmpty(inSql)){
            return q0;
        }
        Set<Long> notContailExceptIds = getNotContailExceptIds(staff,newExceptIds);
        String notInSql = buildLongInSql(notContailExceptIds);
        q0 = getNotMergeOnlyContailSql(staff, q, inSql, notInSql, newExceptIds);*/
        return q0;
    }

    /**
     *  * 自定义异常，合单仅包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptMergeOnlyContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* Set<Long> newExceptIds=new TreeSet<>();
        String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isEmpty(inSql)){
            return q0;
        }
        Set<Long> notContailExceptIds = getNotContailExceptIds(staff,newExceptIds);
        String notInSql = buildLongInSql(notContailExceptIds);
        q0 = getMergeOnlyContailSql(staff, q, inSql, notInSql, newExceptIds);*/
        return q0;
    }


    /**
     * 非合单，系统异常 ，仅包含
     * @param staff
     */
    public  Query getSysExceptNotMergeOnlyContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<String> containExceps = new HashSet<String>();
        for (String s : exceptions) {
            containExceps.add(s);
        }
        Set<Long> newSysExceptIds = getNewQueryContailSysExceptIds(staff,containExceps.toArray(new String[0]));
        String inSql = buildLongInSql(newSysExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        Set<Long> notContailExceptIds = getNotContailSysExceptIds(staff,newSysExceptIds);
        String notInSql = buildLongInSql(notContailExceptIds);
        q0 = getNotMergeOnlyContailSql(staff, q, inSql, notInSql, newSysExceptIds);*/
        return q0;
    }

    /**
     * 合单，系统异常 ，仅包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public  Query getSysExceptMergeOnlyContailSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<String> containExceps = new HashSet<String>();
        for (String s : exceptions) {
            containExceps.add(s);
        }
        Set<Long> newSysExceptIds = getNewQueryContailSysExceptIds(staff,containExceps.toArray(new String[0]));
        String inSql = buildLongInSql(newSysExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        Set<Long> notContailExceptIds = getNotContailSysExceptIds(staff,newSysExceptIds);
        String notInSql = buildLongInSql(notContailExceptIds);
        q0 = getMergeOnlyContailSql(staff, q, inSql, notInSql, newSysExceptIds);*/
        return q0;
    }


    /********************同时包含********************************/
    /**
     *  自定义异常非合单，同时包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
   public Query getSelfExceptNotMergeContainAllSql(Staff staff,  Query q,TradeQueryParams params){
       Query q0=new Query();

      /* Set<Long> newExceptIds=new TreeSet<>();
       String[] exceptIds = params.getExceptIds();
       if(ArrayUtils.isNotEmpty(exceptIds)){
           newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
       }
       String inSql = buildLongInSql(newExceptIds);
       if(StringUtils.isEmpty(inSql)){
           return q0;
       }
       q0 = getNotMergeContainAllSql(staff, q, inSql, newExceptIds);*/
       return q0;
   }

    /**
     *  自定义异常合单，同时包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSelfExceptMergeContainAllSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();

       /* Set<Long> newExceptIds=new TreeSet<>();
        String[] exceptIds = params.getExceptIds();
        if(ArrayUtils.isNotEmpty(exceptIds)){
            newExceptIds= Arrays.stream(exceptIds).map(e->Long.valueOf(e)).collect(Collectors.toSet());
        }
        String inSql = buildLongInSql(newExceptIds);
        if(StringUtils.isEmpty(inSql)){
            return q0;
        }
        q0 = getMergeContainAllSql(staff, q, inSql, newExceptIds);*/
        return q0;
    }

    /**
     *  系统异常，非合单，同时包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptNotMergeContainAllSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<String> containExceps = new HashSet<String>();
        for (String s : exceptions) {
            containExceps.add(s);
        }
        Set<Long> newSysExceptIds = getNewQueryContailSysExceptIds(staff,containExceps.toArray(new String[0]));
        String inSql = buildLongInSql(newSysExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getNotMergeContainAllSql(staff, q, inSql, newSysExceptIds);*/
        return q0;
    }


    /**
     *  系统异常，合单，同时包含
     * @param staff
     * @param q
     * @param params
     * @return
     */
    public Query getSysExceptMergeContainAllSql(Staff staff,  Query q,TradeQueryParams params){
        Query q0=new Query();
       /* String[] exceptions = params.getExceptionStatus();
        if(ArrayUtils.isEmpty(exceptions)){
            return q0;
        }
        Set<String> containExceps = new HashSet<String>();
        for (String s : exceptions) {
            containExceps.add(s);
        }
        Set<Long> newSysExceptIds = getNewQueryContailSysExceptIds(staff,containExceps.toArray(new String[0]));
        String inSql = buildLongInSql(newSysExceptIds);
        if(StringUtils.isBlank(inSql)){
            return q0;
        }
        q0 = getMergeContainAllSql(staff, q, inSql, newSysExceptIds);*/
        return q0;
    }

    /******************无异常********************/

    public Query hasNoExcept(Staff staff,  Query q){
        Query q0=new Query();
       /* StringBuilder sb=new StringBuilder();
        sb.append(" NOT EXISTS (select 1 from  ")
                .append(q.getTradeExceptTable()).append("_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" s ")
                .append(" where s.sid=t.sid and s.company_id=t.company_id and s.enable_status=1)");
        q0.append(sb.toString());*/
        return q0;
    }
}

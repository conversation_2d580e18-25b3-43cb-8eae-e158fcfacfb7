package com.raycloud.dmj.services.trades.support.download;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.presell.PresellRuleBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.QueryItemSkuCondition;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class TradeRuleItemExportService implements IDownloadCenterCallback {

    private Staff staff;
    private QueryItemSkuCondition queryItemSkuCondition;
    private PresellRuleBusiness presellRuleBusiness;

    public TradeRuleItemExportService(Staff staff, QueryItemSkuCondition queryItemSkuCondition, PresellRuleBusiness presellRuleBusiness) {
        this.staff = staff;
        this.queryItemSkuCondition = queryItemSkuCondition;
        this.presellRuleBusiness = presellRuleBusiness;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        queryItemSkuCondition.setPageNo(downloadParam.getPage().getPageNo());
        queryItemSkuCondition.setPageSize(downloadParam.getPage().getPageSize());
        String[][] tradeTrace = tradeRuleItemExport(staff, queryItemSkuCondition);
        DownloadResult result = new DownloadResult();
        result.setFlag(null != tradeTrace);
        result.setData(tradeTrace);
        return result;
    }

    private String[][] tradeRuleItemExport(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        List<DmjSku> presellItemSkus = presellRuleBusiness.queryPresellItem(staff, queryItemSkuCondition);
        if (CollectionUtils.isEmpty(presellItemSkus)) {
            return null;
        }
        List<String[]> resultList = new ArrayList<>();
        presellItemSkus.forEach(dmjSku -> {
            List<String> row = Lists.newArrayListWithCapacity(4);
            row.add(dmjSku.getOuterId());
            row.add(dmjSku.getTitle());
            row.add(dmjSku.getShortTitle());
            String propertiesName = dmjSku.getPropertiesName();
            String propertiesAlias = dmjSku.getPropertiesAlias();
            StringBuilder sb = new StringBuilder("");
            if (StringUtils.isNotBlank(propertiesName)) {
                sb.append(propertiesName);
            }
            if (StringUtils.isNotBlank(propertiesAlias)) {
                sb.append("(").append(propertiesAlias).append(")");
            }
            row.add(sb.toString());
            resultList.add(row.toArray(new String[4]));
        });
        return resultList.toArray(new String[resultList.size()][]);
    }
}

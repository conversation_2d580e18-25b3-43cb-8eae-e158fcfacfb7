package com.raycloud.dmj.services.trades.support;

import com.raycloud.commons.xserialnumber.api.client.GUIDGenerator;
import com.raycloud.dmj.services.trades.IdWorkerService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;

@Service
public class GuidService implements IdWorkerService {
    @Autowired
    private GUIDGenerator guidGenerator;

    @Resource IdWorkerSupportService idWorkerSupportService;

    private final Logger logger = Logger.getLogger(this.getClass());


    private Random random = new Random();

    @Override
    public long nextId() {
        long nextId;
        try {
            nextId = guidGenerator.nextId();
        } catch (Throwable e) {
            logger.error("guidGeneratorId获取失败,调用老的方法补偿！");
            nextId = idWorkerSupportService.nextId();
        }
        return nextId;
    }
}

package com.raycloud.dmj.services.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.split.support.SplitUpdateData;
import com.raycloud.dmj.dao.trade.pgl.OrderExtDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.OrderExt;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.DMJItemUtils;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.ItemIdInfoDto;
import com.raycloud.dmj.item.search.request.QueryByItemIdInfoListRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryByItemIdInfoListResponse;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.utils.helper.BaseHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统商品设置发货周期 自定义属性 计算承诺发货时间逻辑
 * <a href="https://gykj.yuque.com/entavv/xb9xi5/xgnbfogih7g63ft5">语雀文档</a>
 */
@Service
@Slf4j
public class TradeSysProductTimeoutActionTimeService {

    /**
     * 默认时间 2000-01-01 00:00:00
     */
    final Date def = new Date(946656000000L);

    @Resource
    FeatureService featureService;

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    TradeTimeoutContextBuilder tradeTimeoutContextBuilder;

    @Resource
    OrderExtDao orderExtDao;

    public void setTradeTimeoutActionTimeFromProduct(Staff staff, List<Trade> trades) {
        if (!featureService.checkHasFeature(staff.getCompanyId(), Feature.EDIT_DELIVERY_TIME)) {
            return;
        }

        List<Trade> filterTrades = trades.stream().filter(t -> TradeStatusUtils.isWaitAudit(t.getSysStatus())).collect(Collectors.toList());

        List<ItemIdInfoDto> itemIds = new ArrayList<>();
        List<Order> allOrders = TradeUtils.getOrders4Trade(filterTrades);
        for (Order order : allOrders) {
            Long itemSysId = order.getItemSysId();
            if (itemSysId == null || itemSysId <= 0) {
                continue;
            }

            Long skuSysId = order.getSkuSysId();
            ItemIdInfoDto itemIdInfoDto = new ItemIdInfoDto();
            itemIdInfoDto.setSysItemId(itemSysId);
            itemIdInfoDto.setSysSkuId(skuSysId == null || skuSysId <= 0 ? 0 : skuSysId);
            itemIds.add(itemIdInfoDto);
        }

        if (itemIds.isEmpty()) {
            return;
        }

        QueryByItemIdInfoListRequest req = new QueryByItemIdInfoListRequest();
        req.setStaffRequest(StaffRequest.builder().staffId(staff.getId()).companyId(staff.getCompanyId()).build());
        req.setNeedFields("defined_json");
        req.setItemIdInfoList(itemIds);
        QueryByItemIdInfoListResponse resp = dmjItemCommonSearchApi.queryByItemIdInfoList(req);
        Map<String, Integer> itemKeyToI = resp.getDmjItemList().
                stream().collect(
                        Collectors.toMap(dto -> BaseHelper.buildItemKey(dto.getSysItemId(), dto.getSysSkuId()),
                                dto -> {
                                    List<DmjItem.ItemDefined> definedList = DMJItemUtils.dealDefinedJson(null, dto.getDefinedJson());
                                    for (DmjItem.ItemDefined itemDefined : definedList) {
                                        if ("发货周期".equals(itemDefined.getDefinedKey())) {
                                            String definedValue = itemDefined.getDefinedValue();
                                            if (!NumberUtils.isDigits(definedValue)) {
                                                continue;
                                            }

                                            return Integer.parseInt(definedValue);
                                        }
                                    }
                                    return -1;
                                },
                                (a, b) -> b)
                );

        Map<Long, OrderExt> idToExt = orderExtDao.queryByIds(staff, allOrders.stream().map(Order::getId).filter(Objects::nonNull).collect(Collectors.toList()));

        for (Trade trade : filterTrades) {
            Date payTime = trade.getPayTime();
            if (payTime == null || def.equals(payTime)) {
                continue;
            }
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                Long itemSysId = order.getItemSysId();
                if (itemSysId == null || itemSysId <= 0) {
                    continue;
                }

                Integer deliveryTimeSub = null;

                OrderExt dbOrderExt = idToExt.get(order.getId());
                OrderExt orderExt = dbOrderExt == null ? order.getOrderExt() : dbOrderExt;
                String customization = null;
                JSONObject customJSONObj = null;
                if (
                        orderExt != null &&
                                (customization = orderExt.getCustomization()) != null &&
                                !customization.isEmpty() && (customJSONObj = JSONObject.parseObject(customization)) != null &&
                                customJSONObj.containsKey("deliveryCycle")
                ) {
                    deliveryTimeSub = Integer.parseInt(customJSONObj.get("deliveryCycle").toString());
                }
                if (deliveryTimeSub == null) {
                    Long skuSysId = order.getSkuSysId();
                    String itemKey = BaseHelper.buildItemKey(itemSysId, skuSysId == null || skuSysId <= 0 ? 0 : skuSysId);
                    deliveryTimeSub = itemKeyToI.get(itemKey);
                    if (orderExt == null) {
                        orderExt = new OrderExt();
                        orderExt.setId(order.getId());
                        orderExt.setSid(order.getSid());
                        orderExt.setTid(order.getTid());
                        orderExt.setCompanyId(order.getCompanyId());
                        order.setOrderExt(orderExt);
                    }
                    if (customJSONObj == null) {
                        customJSONObj = new JSONObject();
                    }
                    customJSONObj.put("deliveryCycle", deliveryTimeSub);
                    customization = customJSONObj.toJSONString();
                    orderExt.setCustomization(customization);
                }
                if (deliveryTimeSub == null || deliveryTimeSub < 0) {
                    continue;
                }

                Date deliveryTimeFromProduct = DateUtils.addDays(payTime, deliveryTimeSub);
                if (order.getEstimateConTime() == null || order.getEstimateConTime().after(deliveryTimeFromProduct)) {
                    order.setEstimateConTime(deliveryTimeFromProduct);
                }
            }
        }

        List<TradeTrace> traces = new ArrayList<>();
        for (Trade trade : filterTrades) {
            Date recalculation = TradeEsConTimeUtils.recalculation(trade, tradeTimeoutContextBuilder.build(staff).isTimeoutFromSysProduct());
            if (recalculation != null && !Objects.equals(trade.getTimeoutActionTime(), recalculation)) {
                TradeTrace trace = TradeTraceUtils.createTradeTraceWithTrade(
                        staff,
                        trade,
                        "根据商品设置的发货周期重算承诺发货时间",
                        staff.getName(),
                        new Date(),
                        "根据商品设置的发货周期重算承诺发货时间: " + DateFormatUtils.format(recalculation, "yyyy-MM-dd HH:mm:ss"));
                traces.add(trace);
                trade.setTimeoutActionTime(recalculation);
            }
        }
        tradeTraceService.batchAddTradeTrace(staff, traces);
    }

    /**
     * 取消拆单后
     */
    public void recalculationAfterUndoSplitting(Staff staff, Trade mainTrade, List<? extends Trade> originSplitTrades, SplitUpdateData splitUpdateData) {
        if (!featureService.checkHasFeature(staff.getCompanyId(), Feature.EDIT_DELIVERY_TIME) ||
                originSplitTrades == null ||
                originSplitTrades.isEmpty()
        ) {
            return;
        }

        originSplitTrades.
                stream().
                filter(t -> TradeTagUtils.checkIfExistTag(t, SystemTags.TAG_UPDATE_TIMEOUT)).
                map(Trade::getTimeoutActionTime).
                filter(Objects::nonNull).
                max(Date::compareTo).
                ifPresent(date -> {
                    for (Trade updateTrade : splitUpdateData.getUpdateTrades()) {
                        if (updateTrade.getSid().equals(mainTrade.getSid())) {
                            updateTrade.setTimeoutActionTime(date);
                            break;
                        }
                    }
                });
    }
}

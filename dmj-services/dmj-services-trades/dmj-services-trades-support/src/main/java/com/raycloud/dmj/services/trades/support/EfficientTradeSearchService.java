package com.raycloud.dmj.services.trades.support;

import com.github.ltsopensource.core.commons.utils.DateUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.services.trades.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.QUERY_BEFORE_CONSIGN;
import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED;

@Service
public class EfficientTradeSearchService implements IEfficientTradeSearchService {

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tbTradeSearchService;

    @Override
    public List<Trade> search(Staff staff, TradeQueryParams params) {
        Date startTime = params.getStartTime();
        Date endTime = params.getEndTime();
        if (startTime == null || endTime == null || DateUtils.addDay(startTime, 1).before(endTime)){
            throw new IllegalArgumentException("起始时间不能为空，或起始时间大于二十四小时");
        }
        params.setQueryId(QUERY_UN_CONSIGNED);//走小表查询
        params.setTimeType("upd_time");
        params.setQueryOrder(false);//不查询order信息
        params.setQueryFlag(1);//只查询订单 不查询总数
        params.setFields("t.sid,t.tid,t.merge_sid,t.user_id,t.warehouse_id,t.template_id,t.template_type,t.is_excep,t.enable_status,t.scalping,t.pay_time");
        params.setIgnoreFilter(1);//不走filter

        return tbTradeSearchService.search(staff, params).getList();
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, Long... sids) {
        return tbTradeSearchService.queryBySidsContainMergeTrade(staff, sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, Long... sids) {
        return tbTradeSearchService.queryBySids(staff, showDetail, sids);
    }
}

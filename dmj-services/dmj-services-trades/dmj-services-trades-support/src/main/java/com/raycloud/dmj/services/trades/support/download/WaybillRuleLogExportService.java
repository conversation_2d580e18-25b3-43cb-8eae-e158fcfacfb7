package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.dto.WaybillUsingRuleLogDTO;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WaybillUsingRuleLogParam;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.pt.IWaybillUsingRuleLogService;
import org.apache.commons.collections.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

public class WaybillRuleLogExportService implements IDownloadCenterCallback {
    private Staff staff;
    private WaybillUsingRuleLogParam waybillUsingRuleLogParam;
    private IWaybillUsingRuleLogService waybillUsingRuleLogService;
    private Page page;

    public WaybillRuleLogExportService() {
    }

    public WaybillRuleLogExportService(Staff staff, WaybillUsingRuleLogParam waybillUsingRuleLogParam, IWaybillUsingRuleLogService waybillUsingRuleLogService, Page page) {
        this.staff = staff;
        this.waybillUsingRuleLogParam = waybillUsingRuleLogParam;
        this.waybillUsingRuleLogService = waybillUsingRuleLogService;
        this.page = page;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult(){{this.setFlag(false);}};
        // 导出需要导出查询条件的数据，不要分页
        List<WaybillUsingRuleLogDTO> waybillUsingRuleLogDTOS = waybillUsingRuleLogService.queryList(staff, waybillUsingRuleLogParam, null);
        if(CollectionUtils.isEmpty(waybillUsingRuleLogDTOS)){
            result.setFlag(false);
            result.setData(new String[0][]);
            return result;
        }
        int count = 1;
        //开始组装excel模板内容
        // {"序号", "货主名称", "快递", "快递单号", "系统订单号", createdStr};
        List<String[]> logDtoExportExcelList = new LinkedList<String[]>();
        for (WaybillUsingRuleLogDTO dto : waybillUsingRuleLogDTOS) {
            List<String> line = new ArrayList<String>();
            line.add(getValue(count ++));
            line.add(getValue(dto.getRuleName()));
            line.add(getValue(dto.getExpressName()));
            line.add(getValue(dto.getOutSid()));
            line.add(getValue(dto.getSid()));
            line.add(getValue(dto.getCreated()));
            String[] linetoarray = new String[line.size()];
            logDtoExportExcelList.add(line.toArray(linetoarray)) ;
        }
        String[][] content = new String[logDtoExportExcelList.size()][];
        for (int currentNum = 0; currentNum < logDtoExportExcelList.size(); currentNum++) {
            content[currentNum] = logDtoExportExcelList.get(currentNum);
        }
        result.setData(content);
        return result;
    }

    private String getValue(Object o) {
        if (o != null){
            if (o instanceof Date) {
                Date date = (Date) o;
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
            }
            return String.valueOf(o);
        }
        return "";
    }
}

package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.CacheBusiness;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.logistics.LogisticsOfflineBusiness;
import com.raycloud.dmj.business.logistics.UploadBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.ConsignCache;
import com.raycloud.dmj.domain.consign.ConsignCacheUtils;
import com.raycloud.dmj.domain.consign.ConsignEventData;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.consign.ExternalConsignRequest;
import com.raycloud.dmj.services.trade.consign.IConsignCacheService;
import com.raycloud.dmj.services.trades.IConsignUploadService;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.ConsignUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @created 2020-03-09 21:04
 */
@Service
public class ConsignUploadService implements IConsignUploadService {

    @Resource
    LogisticsOfflineBusiness logisticsOfflineBusiness;
    @Resource
    UploadBusiness uploadBusiness;

    @Resource
    IConsignCacheService consignCacheServices;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    CacheBusiness cacheBusiness;

    @Override
    public void asyncConsign(Staff staff, List<Long> sids, boolean exceptConsign, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel) {
        logisticsOfflineBusiness.asyncConsign(staff, sids, exceptConsign, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel);
    }

    @Override
    public void consign(Staff staff, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel) {
        logisticsOfflineBusiness.consign(staff, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, TradeEvents.TRADE_CONSIGN);
    }

    @Override
    public void consign(Staff staff, ConsignEventData eventData) {
        logisticsOfflineBusiness.consign(staff, eventData);
        //走一遍buffer  防止之前的数据未处理
        consign(staff, ConsignUtils.getSendType(eventData.getConsignType()), eventData.getClientIp(), eventData.getDummyType(), eventData.getNoLogisticsName(), eventData.getNoLogisticsTel());
    }

    @Override
    public void consignScan(Staff staff) {
        String progressKey = String.format("trade_consign_scan_%s", staff.getCompanyId());
        ProgressData progress = addProgress(staff, progressKey);
        if (Objects.isNull(progress)) {
            return;
        }

        try {
            List<ConsignCache> consignCaches = consignCacheServices.scan(staff);
            if (CollectionUtils.isEmpty(consignCaches)) {
                return;
            }
            if (consignCaches.size() > 3000) {
                Logs.warn(LogHelper.buildLog(staff, "5分钟前UploadCache数据:" + consignCaches.size()));
            }

            for (List<ConsignCache> subConsignCaches : Lists.partition(consignCaches, 200)) {
                List<ConsignEventData> eventDatas = ConsignCacheUtils.parseConsignCache(staff, subConsignCaches);
                if (CollectionUtils.isNotEmpty(eventDatas)) {
                    for (ConsignEventData eventData : eventDatas) {
                        Staff tempStaff = eventData.getStaffId() == null || eventData.getStaffId() - staff.getId() == 0 ? staff : staffAssembleBusiness.getSafeStaff(staff, eventData.getStaffId());
                        logisticsOfflineBusiness.consign(tempStaff, eventData);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "处理5分钟前uploadCache数据报错"), e);
        } finally {
            cacheBusiness.delete(progressKey);
        }
    }

    private ProgressData addProgress(Staff staff, String progressKey) {
        try {
            ProgressData progressData = cacheBusiness.get(progressKey);
            if (progressData != null && progressData.getProgress() != 2) {
                Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("上一次重试任务还未结束，clueId: %s", progressData.getClueId())));
                return null;
            }
            progressData = new ProgressData().setProgress(1).setClueId(ClueIdUtil.getClueId());
            boolean isAdd = cacheBusiness.add(progressKey, progressData, 3600);
            if (!isAdd) {
                Logs.ifDebug(LogHelper.buildLogHead(staff).append("已存在进行中的重试任务"));
                return null;
            }
            return progressData;
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(staff).append("添加缓存报错"), e);
        }
        return null;
    }

    @Override
    public void consignFx(Staff staff, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel) {
        logisticsOfflineBusiness.consign(staff, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, TradeEvents.TRADE_FX_CONSIGN);
    }

    @Override
    public void asyncUpload(Staff staff, List<Long> sids, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel) {
        uploadBusiness.asyncUpload(staff, sids, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, false);
    }

    @Override
    public void upload(Staff staff, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel, Boolean isExternal) {
        uploadBusiness.upload(staff, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
    }

    @Override
    public void externalAsyncUpload(Staff staff, ExternalConsignRequest request) {
        uploadBusiness.asyncUpload(staff, Lists.newArrayList(request.getSids()), ConsignUtils.getSendType(request.getConsignType()), request.getClientIp(), null, null, null, true);
    }

}

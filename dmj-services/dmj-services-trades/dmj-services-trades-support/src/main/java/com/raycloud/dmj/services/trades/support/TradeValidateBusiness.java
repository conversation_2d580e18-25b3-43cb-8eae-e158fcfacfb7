package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.business.operate.CancelInsufficientBusiness;
import com.raycloud.dmj.business.operate.WeightBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.TradeValidator;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.TradeWeightUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.pt.IExpressAddressRuleService;
import com.raycloud.dmj.services.trades.support.utils.TradeWeighUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @created 2019-06-11 17:39
 */
@Service
public class TradeValidateBusiness {

    @Resource
    CancelInsufficientBusiness cancelInsufficientBusiness;
    @Resource
    IEventCenter eventCenter;
    @Resource
    WeightBusiness weightBusiness;
    @Resource
    private IExpressAddressRuleService expressAddressRuleService;

    public void checkConsign(Staff staff, Trade trade, TradeValidator validator, TradeConfig tradeConfig) {
        check(staff, trade, validator, t -> {
            if (!checkWaitPay(t, validator) && !checkWaitAudit(t, validator) && !checkWaitFinanceAudit(t, validator) && !checkSellerSendGoods(t, validator) &&
                    !checkFinished(t, validator) && checkPrint(t, validator)) {
                if (tradeConfig.getOpenPackageExamine() != null && tradeConfig.getOpenPackageExamine() - 1 == 0 && t.getIsPackage() == 0 && !skipPackageTrade(trade)) {
                    validator.setError(30004, "订单未包装验货");
                } else if (tradeConfig.getOpenPackageWeigh() != null && tradeConfig.getOpenPackageWeigh() - 1 == 0 && t.getIsWeigh() == 0 && !skipWaveTrade(trade)) {
                    validator.setError(30002, "订单未称重");
                }
            }
        });
    }

    public boolean skipPrintTrade(Trade trade){
        if (TradeUtils.isTbXsdTrade(trade)&&!TradeUtils.isTbXsdB2cTrade(trade)) {
            return true;
        }
        if(PlatformUtils.is1688ZiTiTrade(trade)) {
            return true;
        }
        return false;
    }

    public boolean skipPackageTrade(Trade trade){
        if(TradeUtils.isTbXsdTrade(trade)){
            return true;
        }
        return false;
    }

    public boolean skipWaveTrade(Trade trade){
        if(TradeUtils.isTbXsdTrade(trade)){
            return true;
        }
        return false;
    }

    @Deprecated
    public void checkPack(Staff staff, Trade trade, TradeValidator validator) {
        checkPack(staff, trade, validator, false);
    }

    public void checkPack(Staff staff, Trade trade, TradeValidator validator, boolean allowUnprintPack) {
        // 30001 非已审核订单不能包装验货
        // 30002 订单已包装
        // 30003 未打印订单不能包装验货
        check(staff, trade, validator, t -> {
            if (!checkWaitPay(t, validator) && !checkWaitAudit(t, validator) && !checkWaitFinanceAudit(t, validator)
                    && (validator.isSupportSellerSend() || !checkSellerSendGoods(t, validator))
                    && (validator.isSupportSellerSend() || !checkFinished(t, validator))
                    && (allowUnprintPack || checkPrint(t, validator))
                    && !checkOtherConsigned(t, validator)
                    && trade.getIsPackage() != null && t.getIsPackage() - 1 == 0) {
                validator.setError(30002, "订单已包装验货");
            }
        });
    }

    public void checkPackUnCancelInsufficient(Staff staff, Trade trade, TradeValidator validator) {
        // 30001 非已审核订单不能包装验货
        // 30002 订单已包装
        // 30003 未打印订单不能包装验货
        checkUnCancelInsufficient(staff, trade, validator, t -> {
            if (!checkWaitPay(t, validator) && !checkWaitAudit(t, validator) && !checkWaitFinanceAudit(t, validator) && !checkSellerSendGoods(t, validator) &&
                    !checkFinished(t, validator) && checkPrint(t, validator) && !checkOtherConsigned(t, validator)
                    && trade.getIsPackage() != null && t.getIsPackage() - 1 == 0) {
                validator.setError(30002, "订单已包装验货");
            }
        });
    }


    public void checkWeighBeforeConsign(Staff staff, Trade trade, TradeValidator validator, TradeConfig tradeConfig, String outSid) {
        // 30001 非已审核订单不能称重
        // 30002 订单已称重
        // 30003 未打印订单不能称重
        // 30004 订单未包装验货（开启了包装验货，称重前必须先包装验货）
        check(staff, trade, validator, t -> {
            if (!checkWaitPay(t, validator) && !checkWaitAudit(t, validator) && !checkWaitFinanceAudit(t, validator) && !checkSellerSendGoods(t, validator) &&
                    !checkFinished(t, validator) && checkPrint(t, validator)) {
                if (tradeConfig != null && tradeConfig.getOpenPackageExamine() - 1 == 0 && t.getIsPackage() == 0) {
                    validator.setError(30004, "订单未包装验货");
                } else if (isTradeWeight(staff, t, outSid)) {
                    validator.setError(30002, "订单已称重");
                }
            }
        });
    }

    public void checkWeighAfterConsign(Staff staff, Trade trade, TradeValidator validator, TradeConfig config, Integer kind, String outSid,Integer sendType) {
        // 30001 非已发货、交易成功订单不能称重
        // 30002 订单已称重
        check(staff, trade, validator, t -> {
            if (!Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(t.getSysStatus()) && !Trade.SYS_STATUS_FINISHED.equals(t.getSysStatus())) {
                if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(t.getSysStatus())) {
                    if (!isPrint(t)) {
                        validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "待打印订单不能发货后称重");
                    } else if (!isPack(t, config)) {
                        validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "待包装订单不能发货后称重");
                    } else if (!isWeigh(staff, t, config, outSid)) {
                        validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "待称重订单不能发货后称重");
                    } else {
                        validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "待发货订单不能发货后称重");
                    }
                } else if (checkWaitPay(t, validator) || checkWaitAudit(t, validator) || checkWaitFinanceAudit(t, validator)) {
                    return;
                }
            }else {
                // 需求地址：https://tb.raycloud.com/task/62eb3c484d3a0a001d49391a
                if (ObjectUtils.isEmpty(sendType) || sendType == 0) {
                    if ((kind - 1 == 0) && (isTradeWeight(staff, t, outSid))) {
                        validator.setError(30002, "订单已称重");
                    }
                }
            }
        });
    }


    public void checkRedoWeigh(Staff staff, Trade trade, TradeValidator validator, String outSid) {
        check(staff, trade, validator, t -> {
            // 30002 未称重订单不能重复称重
            if (!isTradeWeight(staff, t, outSid)) {
                validator.setError(30002, "未称重订单不能重复称重");
            }
        });

    }

    public void checkScanPrint(Staff staff, Trade trade, TradeValidator validator, boolean force) {
        // 30001 待付款/待审核/待财神订单不能扫码打印
        // 30002 订单已打印（force为true时继续打印,不校验该错误）
        // 30003 订单未设置快递模版
        // 30004 普通面单不支持扫描打印
        check(staff, trade, validator, t -> {
            if (TradeStatusUtils.getSysStatusWeight(t.getSysStatus()) < TradeStatusUtils.getSysStatusWeight(Trade.SYS_STATUS_FINISHED_AUDIT)) {
                validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), TradeStatusUtils.getChSysStatusView(t.getSysStatus()) + "订单不能扫描打印");
            } else {
                if (hasTemplate(t, validator) && hasOutSid(t, validator)) {
                    if (t.getTemplateType() != null && t.getTemplateType() == 0) {
                        validator.setError(30004, "普通面单不能扫码打印");
                    } else if (isPrint(t) && !force) {
                        validator.setError(30002, "订单已打印");
                    }
                }
            }
        });
    }

    /**
     * 订单转发
     */
    public void checkTranspondPrint(Staff staff, Trade trade, TradeValidator validator) {
        check(staff, trade, validator, t -> {
            if (!Trade.SYS_STATUS_FINISHED.equals(t.getSysStatus()) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(t.getSysStatus())) {
                validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "订单非已发货/交易成功订单，不能使用快递转发！");
            }
            if (TradeUtils.haveAllSplitGetTag(t)) {
                validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "拆分订单获取子母单的订单不支持快递转发!");
            }
            if (validator.isCheckAddressRule() && expressAddressRuleService.checkNotSendAddressRule(staff, trade, validator.getCpCode())) {
                // 校验快递禁发规则
                validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "快递不可达，请重新选择！");
            }
//            else if(CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())){
//                validator.setError(TradeValidator.Error.SYS_STATUS_ERROR.getCode(), "拼多多订单发货后不允许修改，不能使用快递转发！");
//            }
        });
    }

    private boolean checkPrint(Trade trade, TradeValidator validator) {
        if (!isPrint(trade)&&!skipPrintTrade(trade)) {
            validator.setError(30003, "订单未打印");
            return false;
        }
        return true;
    }

    private void check(Staff staff, Trade trade, TradeValidator validator, Consumer<Trade> consumer) {
        validator.check(staff, trade);
        if (!validator.hasError()) {
            consumer.accept(trade);
        }
        validator.checkThrowError();
        //只有缺货异常，检查是否需要取消缺货异常
        if (!validator.hasError() && validator.isCancelInsufficient()) {
            cancelInsufficientBusiness.cancel(staff, trade.getSid());
        }
    }

    private void checkUnCancelInsufficient(Staff staff, Trade trade, TradeValidator validator, Consumer<Trade> consumer) {
        validator.check(staff, trade);
        if (!validator.hasError()) {
            consumer.accept(trade);
        }
        validator.checkThrowError();
    }

    private boolean isPrint(Trade trade) {
        return trade.getExpressPrintTime() != null && trade.getExpressPrintTime().compareTo(TradeTimeUtils.INIT_DATE) > 0;
    }

    private boolean isPack(Trade trade, TradeConfig config) {
        return config.getOpenPackageExamine() != null && config.getOpenPackageExamine() - 1 == 0 && trade.getIsPackage() != null && trade.getIsPackage() - 1 == 0;
    }

    private boolean isWeigh(Staff staff, Trade trade, TradeConfig config, String outSid) {
        return config.getOpenPackageWeigh() != null && config.getOpenPackageWeigh() - 1 == 0 && isTradeWeight(staff, trade, outSid);
    }

    private boolean hasTemplate(Trade trade, TradeValidator validator) {
        if (trade.getTemplateId() == null || trade.getTemplateId() <= 0) {
            validator.setError(30003, "订单未设置快递模版");
            return false;
        }
        return true;
    }

    private boolean hasOutSid(Trade trade, TradeValidator validator) {
        if (StringUtils.isBlank(trade.getOutSid())) {
            validator.setError(30005, "订单未设置快递单号");
            return false;
        }
        return true;
    }

    public void checkTradeWeight(Staff staff, Trade trade, TradeValidator validator, TradeConfig tradeConfig, Integer kind,Integer sendType) {
        checkTradeWeight(staff, trade, validator, tradeConfig, kind, true, trade.getOutSid(),sendType);
    }

    /**
     * 校验订单称重
     *
     * @param staff
     * @param trade
     * @param validator
     * @param tradeConfig
     * @param kind
     * @param sendType 0 单个、1批量
     */
    public void checkTradeWeight(Staff staff, Trade trade, TradeValidator validator, TradeConfig tradeConfig, Integer kind, boolean recordLog, String outSid,Integer sendType) {
        //重复称重校验
        outSid = outSid == null ? trade.getOutSid() : outSid;
        boolean reWeight = TradeWeightUtils.isReWeight(kind);
        if (trade != null && reWeight && isTradeWeight(staff, trade, outSid)) {
            checkRedoWeigh(staff, trade, validator, outSid);
        } else {
            if (TradeWeightUtils.isBeforeConsignWeight(kind)) {//发货前称重查询
                validator.setIgnoreInsufficient(true);
                checkWeighBeforeConsign(staff, trade, validator, tradeConfig, outSid);
            } else {//发货后称重查询
                validator.setCheckAfterSendGoodsRefundException(true);
                checkWeighAfterConsign(staff, trade, validator, tradeConfig, kind, outSid, sendType);
            }
        }
        if (recordLog && validator.hasError() && TradeValidator.Error.NOT_FOUND.getCode() != validator.getCode()) {
            eventCenter.fireEvent(this, new EventInfo("trade.custom.trace.save").setArgs(new Object[]{staff, "称重"}),
                    Collections.singletonList(TradeTraceUtils.simplifyWithTrace(trade, "称重失败，原因：" + validator.getMessage())));
        }
    }

    private boolean checkWaitPay(Trade trade, TradeValidator validator) {
        if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus())) {
            validator.setMessage("订单待付款");
            validator.setCode(300010);
            return true;
        }
        return false;
    }

    private boolean checkOtherConsigned(Trade trade, TradeValidator validator) {
        if (Objects.equals(trade.getSysConsigned(), 2)) {
            validator.setMessage("其他ERP发货！");
            validator.setCode(300022);
            return true;
        }
        return false;
    }

    private boolean checkWaitAudit(Trade trade, TradeValidator validator) {
        if (Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
            validator.setMessage("订单待审核");
            validator.setCode(300011);
            return true;
        }
        return false;
    }

    private boolean checkWaitFinanceAudit(Trade trade, TradeValidator validator) {
        if (Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus())) {
            validator.setMessage("订单待财审");
            validator.setCode(300012);
            return true;
        }
        return false;
    }

    private boolean checkSellerSendGoods(Trade trade, TradeValidator validator) {
        if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus())) {
            validator.setMessage("订单已发货");
            validator.setCode(300014);
            return true;
        }
        return false;
    }

    private boolean checkFinished(Trade trade, TradeValidator validator) {
        if (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())) {
            validator.setMessage("订单已完成");
            validator.setCode(300015);
            return true;
        }
        return false;
    }

    public boolean isTradeWeight(Staff staff, Trade trade, String outSid) {
        return (trade.getIsWeigh() != null && trade.getIsWeigh() - 1 == 0 && trade.getOutSid() != null && trade.getOutSid().equalsIgnoreCase(TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(),outSid)))
                || weightBusiness.isWeightByOutSid(staff, trade.getSid(), outSid);
    }
}

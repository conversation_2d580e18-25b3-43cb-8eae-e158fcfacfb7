package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.TradeUserConfigDao;
import com.raycloud.dmj.domain.RequestData;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.enums.TradeBusinessRuleEnum;
import com.raycloud.dmj.domain.trade.enums.TradeRuleOperationTypeEnum;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trades.TradeUserConfig;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.domain.trade.rule.convert.PromiseConvertUtils;
import com.raycloud.dmj.services.trades.ITradeUserConfigService;
import com.raycloud.dmj.services.user.IShopService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 订单承诺时间配置服务
 *
 * <AUTHOR>
 */
@Service
public class TradeUserConfigService implements ITradeUserConfigService {

    @Resource
    TradeUserConfigDao tradeUserConfigDao;
    @Resource
    IShopService shopService;
    @Resource
    ITradeRuleService tradeRuleService;

    @Override
    @Cacheable(value = "defaultCache#600", key = "'trade_user_config_' + #staff.companyId")
    public List<TradeUserConfig> queryAll(Staff staff) {
        List<Shop> shops = shopService.queryByCompanyId(staff);
        if (shops.size() == 0) {
            return new ArrayList<>();
        }

        List<TradeUserConfig> list = new ArrayList<>();
        List<TradeUserConfig> confs = tradeUserConfigDao.queryByCompanyId(staff);
        Map<Long, TradeUserConfig> confMap = new HashMap<>();
        confs.forEach(conf -> confMap.put(conf.getUserId(), conf));

        for (Shop shop : shops) {
            TradeUserConfig conf;
            if (confMap.containsKey(shop.getUserId())) {
                conf = confMap.get(shop.getUserId());
            } else {
                conf = new TradeUserConfig();
                conf.setCompanyId(staff.getCompanyId());
                conf.setUserId(shop.getUserId());
                conf.setEstConsignTime(7);
                conf.setEnableStatus(0);
            }

            conf.setShopName(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : shop.getTitle());
            conf.setSource(shop.getSource());
            if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(shop.getSource()) && CommonConstants.PLAT_FORM_TYPE_VIPSOV.equals(shop.getSubSource())) {
                conf.setSourceName(shop.getSubSourceName());
            } else {
                conf.setSourceName(shop.getSourceName());
            }
            list.add(conf);
        }
        return list;
    }

    @Override
    @CacheEvict(value = "defaultCache#600", key = "'trade_user_config_' + #staff.companyId")
    public void save(Staff staff, RequestData<TradeUserConfig> data) {
        TradeUserConfig conf = data.getObject();
        Assert.notNull(conf.getUserId(), "请输入userId参数");
        Shop shop = shopService.queryByUserId(staff, conf.getUserId());
        Assert.notNull(shop, "店铺不存在");
        TradeUserConfig oldConf = tradeUserConfigDao.get(staff, conf.getUserId());
        if (null == oldConf) {
            add(staff, conf);
        } else {
            modify(staff, conf, oldConf);
        }
    }

    private void add(Staff staff, TradeUserConfig conf) {
        conf.setEstConsignTime(7);
        tradeUserConfigDao.insert(staff, conf);
        conf.setBusinessType(TradeBusinessRuleEnum.PROMISE_RULE.getBusinessId());
        conf.setName(NumberUtils.long2Str(conf.getUserId()));
        tradeRuleService.initTradeRule(staff, conf);
    }

    private void modify(Staff staff, TradeUserConfig newConf, TradeUserConfig oldConf) {
        if (newConf.getEstConsignTime() == null) {
            newConf.setEstConsignTime(oldConf.getEstConsignTime());
        }
        if (newConf.getEnableStatus() == null) {
            newConf.setEnableStatus(oldConf.getEnableStatus());
        }
        //没有修改任何内容
        if (NumberUtils.isEquals(newConf.getEnableStatus(), oldConf.getEnableStatus()) && NumberUtils.isEquals(newConf.getEstConsignTime(), oldConf.getEstConsignTime())) {
            return;
        }
        tradeUserConfigDao.update(staff, newConf);
        //老的不存在，先保存一份老的数据
        oldConf.setBusinessType(TradeBusinessRuleEnum.PROMISE_RULE.getBusinessId());
        oldConf.setName(NumberUtils.long2Str(oldConf.getUserId()));
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, oldConf);
        //更新
        tradeRuleService.modify(staff, PromiseConvertUtils.convert(staff, newConf, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    @Override
    public TradeUserConfig getByUser(Staff staff, User user) {
        Assert.notNull(staff, "staff不能为空");
        Assert.notNull(user, "user不能为空");
        Assert.notNull(user.getId(), "userId不能为空");
        return tradeUserConfigDao.get(staff, user.getId());
    }
}

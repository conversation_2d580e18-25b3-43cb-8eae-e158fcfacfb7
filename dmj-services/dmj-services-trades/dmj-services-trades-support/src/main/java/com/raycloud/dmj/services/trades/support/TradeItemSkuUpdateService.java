package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.modify.TradeCostCalculateBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.BusinessNodeEnum;
import com.raycloud.dmj.domain.enums.TradeExtraFieldEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.DMJItemUtils;
import com.raycloud.dmj.domain.utils.DmjSkuUtils;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by windy26205 on 19/7/18.
 */
@Service
public class TradeItemSkuUpdateService implements ITradeItemSkuUpdateService {

    private static final Logger logger = Logger.getLogger(TradeItemSkuUpdateService.class);

    private static final String ORDER_FIELDS = "id,sid,item_sys_id,taobao_id,sku_sys_id,combine_id,sys_status,num,type,is_virtual,non_consign,sys_outer_id,sys_title,short_title,unit,sku_unit,sys_item_remark,sys_sku_remark,sys_sku_properties_alias,sys_sku_properties_name,net_weight,cost,volume,sys_pic_path,cids";


    @Resource
    TbOrderDAO tbOrderDAO;

    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IItemServiceDubbo itemServiceDubbo;

    @Resource
    IWaveSortingService waveSortingService;

    @Resource
    private ILockService lockService;
    @Resource
    TradeQueryBuilder tradeSqlQueryBuilder;
    @Resource
    TradeCostCalculateBusiness tradeCostCalculateBusiness;
    @Resource
    TradeConfigService tradeConfigService;
    @Resource
    ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;
    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    IEventCenter eventCenter;

    @Override
    public boolean handleItemUpdate(Staff staff, List<Long> sysItemIdList) {
        if (sysItemIdList == null || sysItemIdList.isEmpty()) {
            return true;
        }
        List<DmjItem> itemList = itemServiceDubbo.queryItemWithSysItemId(staff, sysItemIdList);
        if (CollectionUtils.isEmpty(itemList)) {
            return true;
        }

        itemList.forEach(item -> log(staff, "item[itemId=%s,cid=%s,title=%s,shortTitle=%s,unit=%s,netWeight=%s,volume=%s,cost=%s,remark=%s,picPath=%s]",
                item.getSysItemId(), item.getSellerCids(), item.getTitle(), item.getShortTitle(), item.getUnit(), item.getWeight(), item.getVolume(), item.getPriceImport(), item.getRemark(), item.getPicPath()));

        Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(itemList);
        Map<Long, DmjSku> skuMap = new HashMap<>();//sku缓存
        //外层取了20条商品信息
        int pageSize = TradeDiamondUtils.getTradeItemsUpdatePageSize(staff);
        List<TbOrder> tbOrders = queryOrders4Lock(staff, DMJItemUtils.getSysItemIds(itemList), null, new Page()
                .setPageNo(1).setPageSize(pageSize));

        if (CollectionUtils.isEmpty(tbOrders)) {
            return true;
        }

        Map<String, List<TbOrder>> orderGroup = tbOrders.stream().collect(Collectors.groupingBy(TbOrder::getTid));

        boolean result = true;
        for (List<List<TbOrder>> subOrders : ListUtils.splitList(new ArrayList<>(orderGroup.values()), 200)) {
            Set<String> lockTid = subOrders.stream().filter(orders -> !CollectionUtils.isEmpty(orders)).map(orders -> orders.get(0).getTid()).collect(Collectors.toSet());
            try {
                lockService.locks(TradeLockBusiness.tids2ERPLocks(staff, new ArrayList<>(lockTid)), () -> {
                    List<Long> subOrderIds = new ArrayList<>();
                    subOrders.stream().filter(orders -> !CollectionUtils.isEmpty(orders)).forEach(
                            orders -> subOrderIds.addAll(orders.stream().filter(Objects::nonNull).map(TbOrder::getId).collect(Collectors.toList())));
                    List<TbOrder> orders = tbOrderDAO.queryByKeys(staff, true, ORDER_FIELDS, "id", subOrderIds.toArray(new Long[0]));
                    List<TradeTrace> tradeTraces = buildTradeTrace(staff, orders, itemMap, skuMap);
                    querySku(staff, OrderUtils.getSysSkuIds(orders), skuMap);
                    doUpdate(staff, orders, itemMap, skuMap);
                    if (!CollectionUtils.isEmpty(tradeTraces)) {
                        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
                    }
                    return null;
                });
            } catch (Exception e) {
                result = false;
                logger.error(LogHelper.buildLog(staff, String.format("item.update:%s,后订单:%s,处理失败", sysItemIdList, lockTid)), e);
            }
        }
        if (tbOrders.size() >= pageSize) {
            return false;
        }
        return result;
    }

    /**
     * 一个订单的order中，如果商品编码被修改，记录到订单操作日志汇总。
     * skuMap == null时，只修改了主商家编码。
     * combine_id = 0,只关注订单展示的商品。
     * 1,非sku,修改主商家编码-->记录。
     * 2,sku,只修改主商家编码-->不记录(order表记录的是item_sys_id,没有记录主商家编码，无法对比是否改变)
     * 3,sku,修改sku编码-->记录
     * @return 订单操作日志。
     */
    private List<TradeTrace> buildTradeTrace(Staff staff, List<TbOrder> orders, Map<Long, DmjItem> itemMap, Map<Long, DmjSku> skuMap) {
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<TbOrder> normalOrderList = orders.stream().filter(e -> e.getCombineId() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalOrderList)) {
            return tradeTraces;
        }
        Set<String> dupItemSid = new HashSet<>();//去重,维度sid_sysOuterId/sid_skuOuterId,避免同一个sid出现多条相同商品。
        //获取商品商家编码
        normalOrderList.forEach(order -> {
            DmjItem dmjItem = itemMap.get(order.getItemSysId());
            if (dmjItem != null) {
                String newSysOuterId = dmjItem.getOuterId();
                //order中是sku商品,但是商品没有修改sku,只修改主商品，不需要记录，情况2
                boolean notNeedCompareFlag = order.getSkuSysId() != null && order.getSkuSysId() > 0 && skuMap.size() == 0;
                DmjSku newSku = order.getSkuSysId() == null || order.getSkuSysId() <= 0 ? null : skuMap.get(order.getSkuSysId());
                if (newSku != null) {
                    newSysOuterId = StringUtils.isNotBlank(newSku.getSkuOuterId()) ? newSku.getSkuOuterId() : newSku.getOuterId();
                }
                if (!notNeedCompareFlag && !contentEquals(order.getSysOuterId(), newSysOuterId) && !dupItemSid.contains(order.getSid() + "_" + order.getSysOuterId())) {
                    dupItemSid.add(order.getSid() + "_" + order.getSysOuterId());
                    Trade trade = new Trade();
                    trade.setSid(order.getSid());
                    trade.setTid(order.getTid());
                    trade.setTaobaoId(order.getTaobaoId());
                    String content = "商品档案中商品编码修改,由" + order.getSysOuterId() + "->" + newSysOuterId;
                    tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "商品修改", "系统", new Date(), content));
                }
            }
        });
        return tradeTraces;
    }

    @Override
    public boolean handleSkuUpdate(Staff staff, List<Long> sysSkuIdList) {
        if (sysSkuIdList == null || sysSkuIdList.isEmpty()) {
            return true;
        }
        List<DmjSku> allSkuList = itemServiceDubbo.querySkuWithSysSkuId(staff, sysSkuIdList);
        if (CollectionUtils.isEmpty(allSkuList)) {
            return true;
        }

        allSkuList.forEach(sku -> log(staff, "sku[itemId=%s,skuId=%s, sysOuterId=%s,propertiesName=%s,propertiesAlias=%s,unit=%s,netWeight=%s,volume=%s,cost=%s,remark=%s,picPath=%s]",
                sku.getSysItemId(), sku.getSysSkuId(), sku.getSkuOuterId(), sku.getPropertiesName(), sku.getPropertiesAlias(), sku.getUnit(), sku.getWeight(), sku.getVolume(), sku.getPriceImport(), sku.getRemark(),sku.getSkuPicPath()));
        Map<Long, DmjSku> skuMap = DmjSkuUtils.skuList2Map(allSkuList);

        List<DmjItem> dmjItemList = itemServiceDubbo.queryItemWithSysItemId(staff, DmjSkuUtils.getSysItemIds(allSkuList));
        if (CollectionUtils.isEmpty(dmjItemList)) {
            return true;
        }
        dmjItemList.forEach(item -> log(staff, "item[itemId=%s,cid=%s,title=%s,shortTitle=%s,unit=%s,netWeight=%s,volume=%s,cost=%s,remark=%s,picPath=%s]",
                item.getSysItemId(), item.getSellerCids(), item.getTitle(), item.getShortTitle(), item.getUnit(), item.getWeight(), item.getVolume(), item.getPriceImport(), item.getRemark(), item.getPicPath()));

        Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(dmjItemList);
        int pageSize = TradeDiamondUtils.getTradeItemsUpdatePageSize(staff);
        List<TbOrder> tbOrders = queryOrders4Lock(staff, DmjSkuUtils.getSysItemIds(allSkuList), DmjSkuUtils.getSysSkuIds(allSkuList),
                new Page().setPageNo(1).setPageSize(pageSize));

        if (CollectionUtils.isEmpty(tbOrders)) {
            return true;
        }

        Map<String, List<TbOrder>> orderGroup = tbOrders.stream().collect(Collectors.groupingBy(TbOrder::getTid));

        boolean result = true;
        for (List<List<TbOrder>> subOrders : ListUtils.splitList(new ArrayList<>(orderGroup.values()), 200)) {
            Set<String> lockTid = subOrders.stream().filter(orders -> !CollectionUtils.isEmpty(orders)).map(orders -> orders.get(0).getTid()).collect(Collectors.toSet());
            try {
                lockService.locks(TradeLockBusiness.tids2ERPLocks(staff, new ArrayList<>(lockTid)), () -> {
                    List<Long> subOrderIds = new ArrayList<>();
                    subOrders.stream().filter(orders -> !CollectionUtils.isEmpty(orders)).forEach(
                            orders -> subOrderIds.addAll(orders.stream().filter(Objects::nonNull).map(TbOrder::getId).collect(Collectors.toList())));
                    List<TbOrder> orders = tbOrderDAO.queryByKeys(staff, true, ORDER_FIELDS, "id", subOrderIds.toArray(new Long[0]));
                    List<TradeTrace> tradeTraces = buildTradeTrace(staff, orders, itemMap, skuMap);
                    doUpdate(staff, orders, itemMap, skuMap);
                    if (!CollectionUtils.isEmpty(tradeTraces)) {
                        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
                    }
                    return null;
                });
            } catch (Exception e) {
                result = false;
                logger.error(LogHelper.buildLog(staff, String.format("sku.update:%s,后订单:%s,处理失败", sysSkuIdList, lockTid)), e);
            }
        }
        if (tbOrders.size() >= pageSize) {
            return false;
        }
        return result;
    }

    private void querySku(Staff staff, List<Long> sysSkuIds, Map<Long, DmjSku> skuMap) {
        List<Long> sysSkuIdList = new ArrayList<>();
        for (Long sysSkuId : sysSkuIds) {
            if (!skuMap.containsKey(sysSkuId)) {
                sysSkuIdList.add(sysSkuId);
            }
        }
        if (!sysSkuIdList.isEmpty()) {
            List<DmjSku> skuList = itemServiceDubbo.querySkuWithSysSkuId(staff, sysSkuIdList);
            for (DmjSku sku : skuList) {
                log(staff, "sku[itemId=%s,skuId=%s, sysOuterId=%s,propertiesName=%s,propertiesAlias=%s,unit=%s,netWeight=%s,volume=%s,cost=%s,remark=%s,picPath=%s]",
                        sku.getSysItemId(), sku.getSysSkuId(), sku.getSkuOuterId(), sku.getPropertiesName(), sku.getPropertiesAlias(), sku.getUnit(), sku.getWeight(), sku.getVolume(), sku.getPriceImport(), sku.getRemark(),sku.getSkuPicPath());
                skuMap.put(sku.getSysSkuId(), sku);
            }
        }
    }

    private <T extends Order> void doUpdate(Staff staff, List<T> orders, Map<Long, DmjItem> itemMap, Map<Long, DmjSku> skuMap) {
        Set<Long> updateSids = new HashSet<>();
        List<Order> updateOrders = buildUpdateOrders(staff, orders, itemMap, skuMap, updateSids);
        List<Trade> updateTrades = buildUpdateTrades(staff, updateOrders, updateSids);
        //填充商品标签信息到order
        tradeOrderSyncItemTagFill.fillByItemDubbo(staff, updateOrders);
        //标记是否拣选验货
        OrderUtils.fillIsPick(updateOrders);
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        waveSortingService.batchUpdateOuterId(staff, updateOrders);

        Set<Long> asyncCalculateTheoryFeeSids = new HashSet<>();
        for (Order order : updateOrders) {
            if (order.getNetWeight() != null || order.getVolume() != null) {
                asyncCalculateTheoryFeeSids.add(order.getSid());
            }
        }
        asyncCalculateTheoryFeeAfterUpdateNetWeightVolume(staff, asyncCalculateTheoryFeeSids);
    }

    /**
     * 根据需要更新的子订单构造需要更新的trade
     *
     * @param updateList 需要更新的子订单，每个子订单必须包含设置了要修改字段的origin属性
     * @param sids       需要更新的trade的系统订单号
     */
    private List<Trade> buildUpdateTrades(Staff staff, List<Order> updateList, Set<Long> sids) {
        if (sids == null || sids.isEmpty()) {
            return null;
        }
        Map<Long, Order> updateOrderMap = OrderUtils.toMap(updateList);
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, sids.toArray(new Long[0])));
        List<TbOrder> orderList = tbOrderDAO.queryBySids(staff, tradeMap.keySet().toArray(new Long[0]));
        for (int i = 0; i < orderList.size(); i++) {
            Order updateOrder = updateOrderMap.get(orderList.get(i).getId());
            if (updateOrder != null) {//在前面设置过origin，且origin中需要更新的字段都已设置过，与updateOrder保持一致
                orderList.set(i, (TbOrder) updateOrder.getOrigin());//用更新后的order替换掉旧的order
            }
        }
        Map<Long, List<Order>> ordersMap = OrderUtils.toMapBySid(OrderUtils.toTree(orderList));
        List<Trade> tradeList = new ArrayList<>();
        Set<Trade> mainList = new HashSet<>();
        for (Trade trade : tradeMap.values()) {
            List<Order> orders = ordersMap.get(trade.getSid());
            if (orders != null) {
                TradeUtils.getOrders4Trade(trade).addAll(orders);
                if (isMerge(trade)) {//合单
                    if (isMain(trade)) {//主单（能查询出合单，主单必定是要更新的）
                        mainList.add(trade);
                    } else {//合单后隐藏订单
                        if (sids.contains(trade.getSid())) {//合单后隐藏订单只有在带更新集合中才会更新
                            tradeList.add(buildUpdateTrade(trade));
                        }
                        //找出合单所属主单，将隐藏订单下的order添加到主单下，以便于后面的计算
                        Trade main = tradeMap.get(trade.getMergeSid());
                        if (main != null) {
                            TradeUtils.getOrders4Trade(main).addAll(orders);
                            mainList.add(main);
                        }
                    }
                } else {//非合单（能查询出来必定是在待更新集合中，所以需要更新）
                    tradeList.add(buildUpdateTrade(trade));
                }
            }
        }
        for (Trade trade : mainList) {
            tradeList.add(buildUpdateTrade(trade));
        }
        return tradeList;
    }

    private Trade buildUpdateTrade(Trade trade) {
        Trade toUpdate = new TbTrade();
        toUpdate.setSid(trade.getSid());
        toUpdate.setCost(TradeUtils.calculateCost(trade));

        toUpdate.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
        //新包材流程 净重需加上包材重量
        TradeExt tradeExt = trade.getTradeExt();
        JSONObject extraJson = (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields()))?null:JSON.parseObject(tradeExt.getExtraFields());
        if (extraJson != null) {
            Double packmaWeight = extraJson.getDouble(TradeExtraFieldEnum.PACKMA_WEIGHT.getField());
            if (!MathUtils.equalsZero(packmaWeight)) {
                toUpdate.setNetWeight(toUpdate.getNetWeight() + packmaWeight);
            }
        }
        //已经打包过的,订单体积保持原来打包时的体积
        //如果打包的包材体积为0 仍按商品体积来计算
        Double packmaVolume = extraJson ==null?null:extraJson.getDouble(TradeExtraFieldEnum.PACKMA_VOLUME.getField());
        if (!Objects.equals(CommonConstants.JUDGE_YES,trade.getIsPackage()) || MathUtils.equalsZero(packmaVolume)) {
            toUpdate.setVolume(TradeUtils.calculateVolume(trade));
        }

        trade.setNetWeight(toUpdate.getNetWeight());
        trade.setCost(toUpdate.getCost());
        trade.setVolume(toUpdate.getVolume());

        return toUpdate;
    }

    private <T extends Order> List<Order> buildUpdateOrders(Staff staff, List<T> orders, Map<Long, DmjItem> itemMap, Map<Long, DmjSku> skuMap, Set<Long> sids) {
        List<Order> updateList = new ArrayList<>();
        for (Order order : orders) {
            DmjItem item = itemMap.get(order.getItemSysId());
            DmjSku sku = order.getSkuSysId() == null || order.getSkuSysId() <= 0 ? null : skuMap.get(order.getSkuSysId());
            Order updateOrder = buildUpdateOrder(staff, order, item, sku);
            if (updateOrder != null && updateOrder.getId() != null && updateOrder.getId() > 0) {
                updateList.add(updateOrder);
                if (updateOrder.getFlag() == 999) {
                    sids.add(order.getSid());
                }
            }
        }
        return updateList;
    }

    private Order buildUpdateOrder(Staff staff, Order order, DmjItem item, DmjSku sku) {
        if (item == null) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]中的DmjItem没有找到", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId());
            return null;
        }
        TbOrder toUpdate = new TbOrder();
        toUpdate.setSid(order.getSid());

        if (!contentEquals(order.getCids(), item.getSellerCids())) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品类目变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getCids(), item.getSellerCids());
            order.setCids(item.getSellerCids());
            toUpdate.setCids(order.getCids());
            toUpdate.setId(order.getId());
        }
        if (!contentEquals(order.getSysTitle(), item.getTitle())) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品名称变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysTitle(), item.getTitle());
            order.setSysTitle(item.getTitle());
            toUpdate.setSysTitle(order.getSysTitle());
            toUpdate.setId(order.getId());
        }
        if (!contentEquals(order.getShortTitle(), item.getShortTitle())) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品简称变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getShortTitle(), item.getShortTitle());
            order.setShortTitle(item.getShortTitle() != null ? item.getShortTitle() : "");
            toUpdate.setShortTitle(order.getShortTitle());
            toUpdate.setId(order.getId());
        }
        if (!contentEquals(order.getUnit(), item.getUnit())) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品单位变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getUnit(), item.getUnit());
            order.setUnit(item.getUnit() != null ? item.getUnit() : "");
            toUpdate.setUnit(order.getUnit());
            toUpdate.setId(order.getId());
        }
        if (!contentEquals(order.getSysItemRemark(), item.getRemark())) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品备注变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysItemRemark(), item.getRemark());
            order.setSysItemRemark(item.getRemark() != null ? item.getRemark() : "");
            toUpdate.setSysItemRemark(order.getSysItemRemark());
            toUpdate.setId(order.getId());
        }
        double cost = item.getPriceImport() == null?0.0:item.getPriceImport();
        double netWeight = item.getWeight()== null ? 0.0 : item.getWeight();
        double volume = item.getVolume() == null ? 0.0 : item.getVolume();
        String sysOuterId = item.getOuterId();
        String picPath = item.getPicPath();
        if (sku != null) {
            sysOuterId = StringUtils.isNotBlank(sku.getSkuOuterId()) ? sku.getSkuOuterId() : sku.getOuterId();
            if (!isEmptyPic(sku.getSkuPicPath())) {//规格有图片取规格图片，否则取商品图片
                picPath = sku.getSkuPicPath();
            } else if (!isEmptyPic(sku.getPicPath())) {
                picPath = sku.getPicPath();
            }
            //成本价、净重、体积 如果规格没有则取商品
            double skuCost = sku.getPriceImport() == null?0.0:sku.getPriceImport();
            if (skuCost != 0) {
                cost = skuCost;
            }
            double skuNetWeight = sku.getWeight()== null ? 0.0 : sku.getWeight();
            if (skuNetWeight != 0) {
                netWeight = skuNetWeight;
            }
            double skuVolume = sku.getVolume() == null ? 0.0 : sku.getVolume();
            if (skuVolume != 0) {
                volume = skuVolume;
            }
            if (!contentEquals(order.getSkuUnit(), sku.getUnit())) {
                log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统规格单位变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSkuUnit(), sku.getUnit());
                order.setSkuUnit(sku.getUnit() != null ? sku.getUnit() : "");
                toUpdate.setSkuUnit(order.getSkuUnit());
                toUpdate.setId(order.getId());
            }
            if (!contentEquals(order.getSysSkuPropertiesAlias(), sku.getPropertiesAlias())) {
                log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统规格别名变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysSkuPropertiesAlias(), sku.getPropertiesAlias());
                order.setSysSkuPropertiesAlias(sku.getPropertiesAlias() != null ? sku.getPropertiesAlias() : "");
                toUpdate.setSysSkuPropertiesAlias(order.getSysSkuPropertiesAlias());
                toUpdate.setId(order.getId());
            }
            if (!contentEquals(order.getSysSkuRemark(), sku.getSkuRemark())) {
                log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统规格备注变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysSkuRemark(), sku.getSkuRemark());
                order.setSysSkuRemark(sku.getSkuRemark() != null ? sku.getSkuRemark() : "");
                toUpdate.setSysSkuRemark(order.getSysSkuRemark());
                toUpdate.setId(order.getId());
            }
            if (!contentEquals(order.getSysSkuPropertiesName(), sku.getPropertiesName())) {
                log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统规格名称变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysSkuPropertiesName(), sku.getPropertiesName());
                order.setSysSkuPropertiesName(sku.getPropertiesName() != null ? sku.getPropertiesName() : "");
                toUpdate.setSysSkuPropertiesName(order.getSysSkuPropertiesName());
                toUpdate.setId(order.getId());
            }
        } else if (order.getSkuSysId() > 0) {//order中的规格找不到了，打个日志，方便排查问题
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]中的DmjSku没有找到", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId());
        }
        if (!contentEquals(order.getSysPicPath(), picPath)) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品(规格)图片变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysPicPath(), picPath);
            order.setSysPicPath(picPath);
            toUpdate.setSysPicPath(order.getSysPicPath());
            toUpdate.setId(order.getId());
        }
        if (!contentEquals(order.getSysOuterId(), sysOuterId)) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品(规格)商家编码变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getSysOuterId(), sysOuterId);
            order.setSysOuterId(sysOuterId != null ? sysOuterId : "");
            toUpdate.setSysOuterId(order.getSysOuterId());
            toUpdate.setId(order.getId());
        }
        //开启移动加权成本价的用户，修改商品成品价时不处理订单成本价
        //if ((staff.getConf().getMovingWeighted() == null || staff.getConf().getMovingWeighted() != 1) && !doubleEquals(order.getCost(), cost)) {
        if ((!tradeCostCalculateBusiness.isOpenHistoryPriceImport(staff,tradeConfigService.get(staff))) && !doubleEquals(order.getCost(), cost)) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品(规格)成本价变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getCost(), cost);
            order.setCost(cost);
            toUpdate.setCost(order.getCost());//订单需要重新计算成本价
            toUpdate.setId(order.getId());
            if (order.getCombineId() == 0) {//只有套件单品成本价改变时（套件本身成本价没变），订单成本价不更新
                toUpdate.setFlag(999);//临时标记表示所属trade需要更新
            }
        }
        if (!doubleEquals(order.getNetWeight(), netWeight)) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品(规格)净重变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), MathUtils.toString(order.getNetWeight(),4), netWeight);
            order.setNetWeight(netWeight);
            toUpdate.setNetWeight(order.getNetWeight());
            toUpdate.setId(order.getId());
            if (order.getCombineId() == 0) {//只有套件单品重量改变时，订单净重不更新
                toUpdate.setFlag(999);//临时标记表示所属trade需要更新
            }
        }
        if (!doubleEquals(order.getVolume(), volume)) {
            log(staff, "order[sid=%s,id=%s,sysItemId=%s,sysSkuId=%s]系统商品(规格)体积变化: %s -> %s", order.getSid(), order.getId(), order.getItemSysId(), order.getSkuSysId(), order.getVolume(), volume);
            order.setVolume(volume);
            toUpdate.setVolume(order.getVolume());
            toUpdate.setId(order.getId());
            if (order.getCombineId() == 0) {
                toUpdate.setFlag(999);//临时标记表示所属trade需要更新
            }
        }
        toUpdate.setOrigin(order);
        return toUpdate;
    }

    private List<TbOrder> queryOrders4Lock(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, Page page) {
        Query q = new Query().append("company_id = ? AND enable_status > 0 AND is_cancel = 0 AND belong_type in (0,2) ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildListQuery(q, "item_sys_id", sysItemIds);
        tradeSqlQueryBuilder.buildListQuery(q, "sku_sys_id", sysSkuIds);
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        List<TbOrder> orders = tbOrderDAO.queryOrders(staff, "order_not_consign", "id, sid, tid", q, page);
        logger.info(LogHelper.buildLog(staff,String.format("参数:{companyId:%s, sysItemIds:%s, sysSkuIds:%s, pageSize:%s, resultSize:%s}",
                        staff.getCompanyId(), sysItemIds, sysSkuIds, page.getPageSize(), orders.size())));
        return orders;
    }

    private boolean isMerge(Trade trade) {
        return trade.getMergeSid() > 0;
    }

    private boolean isMain(Trade trade) {
        return trade.getSid() - trade.getMergeSid() == 0;
    }

    private boolean contentEquals(String s, String t) {
        String v1 = s == null ? "" : s.trim();
        String v2 = t == null ? "" : t.trim();
        return v1.equals(v2);
    }

    private boolean doubleEquals(Double a, Double b) {
        double ad = a != null ? a : 0;
        double bd = b != null ? b : 0;
        return ad - bd == 0;
    }

    private boolean isEmptyPic(String path) {
        return path == null || (path = path.trim()).isEmpty() || StockConstants.PATH_NO_PIC.equals(path);
    }

    private void log(Staff staff, String formatStr, Object... args) {
        Logs.ifDebug(LogHelper.buildLog(staff, String.format(formatStr, args)));
    }

    /**
     * 更新 netWeight volume 后 异步重算理论运费
     */
    private void asyncCalculateTheoryFeeAfterUpdateNetWeightVolume(Staff staff, Set<Long> asyncCalculateTheoryFeeSids) {
        if (asyncCalculateTheoryFeeSids.isEmpty()) {
            return;
        }

        logger.info(LogHelper.buildLog(staff, String.format("修改重量或体积重算理论运费, sids: %s", asyncCalculateTheoryFeeSids)));
        eventCenter.fireEvent(this, new EventInfo("modify.template.calculate.theoryPostFee").setArgs(new Object[]{staff, new ArrayList<>(asyncCalculateTheoryFeeSids), BusinessNodeEnum.TRADE_MODIFY_TEMPLATE}), null);
    }
}

package com.raycloud.dmj.services.sensitive;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeSysDigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description 订单安全相关，用来定义一些需要加密的平台或者需要模糊字段的平台
 * @date 2021/12/30 上午10:41
 **/
@Service
public class DataSecurityHandleService {

    private final DistributorService distributorService;

    private final TradeDataSensitiveService tradeDataSensitiveService;

    @Autowired
    public DataSecurityHandleService(DistributorService distributorService, TradeDataSensitiveService tradeDataSensitiveService) {
        this.distributorService = distributorService;
        this.tradeDataSensitiveService = tradeDataSensitiveService;
    }

    /**
     * 对订单列表根据不同平台进行旺旺脱敏处理
     */
    public void sensitiveTradeBuyerNick(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        tradeDataSensitiveService.sensitiveTradeBuyerNick(staff, trades);
    }

    /**
     * 对订单列表，针对不同平台实行不同的系统脱敏策略
     *
     * @param usePlatform 是否针对部分平台去调用平台脱敏接口
     */
    public void sensitiveTrade(Staff staff, List<Trade> trades, boolean usePlatform) {
        sensitiveTrade(staff, trades, usePlatform, true);
    }

    /**
     * 对订单列表，针对不同平台实行不同的系统脱敏策略
     *
     * @param usePlatform         是否针对部分平台去调用平台脱敏接口
     * @param isSensitiveSysTrade 是否脱敏系统单
     */
    public void sensitiveTrade(Staff staff, List<Trade> trades, boolean usePlatform, boolean isSensitiveSysTrade) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        distributorService.doDistribute(staff, trades, usePlatform, isSensitiveSysTrade);
        TradeSysDigestUtils.batchSensitive(staff,trades);
    }
}

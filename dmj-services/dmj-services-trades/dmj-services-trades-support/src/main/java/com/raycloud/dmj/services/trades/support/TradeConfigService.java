package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.conf.TradeConfigBusiness;
import com.raycloud.dmj.business.query.TradeCountBusiness;
import com.raycloud.dmj.dao.trade.TradeConfigDao;
import com.raycloud.dmj.dao.wave.WaveRuleDao;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Conf;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.config.TradeConfigNewUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeConfigCheckContext;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.utils.TradeConfigUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.DataUtils;
import com.raycloud.dmj.domain.wave.WaveRule;
import com.raycloud.dmj.domain.wave.WaveRuleCondition;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.PickingType;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.cache.CommonMixedCache;
import com.raycloud.dmj.services.cache.CommonMixedSharedCache;
import com.raycloud.dmj.services.ec.TradeConfigDelayDeleteCache;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeWaveService;
import com.raycloud.dmj.services.trades.config.ConfigCompatibleUtils;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.config.inner.cache.IConfigMixedSharedCache;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.*;

/**
 * 交易配置服务实现
 *
 * <AUTHOR>
 */
@Service
public class TradeConfigService implements ITradeConfigService {

    @Resource
    TradeConfigDao tradeConfigDao;

    @Resource
    TradeCountBusiness tradeCountBusiness;

    @Resource
    ICompanyService companyService;

    @Resource
    CommonMixedCache commonMixedCache;

    @Resource
    CommonMixedSharedCache commonMixedSharedCache;

    @Resource
    ICache cache;

    @Resource
    ITradeWaveService tradeWaveService;

    @Resource
    WaveRuleDao waveRuleDao;

    @Resource
    IStaffService staffService;
    @Resource
    IOpLogService opLogService;

    @Resource
    TradeConfigBusiness tradeConfigBusiness;

    @Resource
    ITradeTagService tradeTagService;

    @Value("${project.profile}")
    String profile;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    Configurable config;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IUserService userService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    IConfigMixedSharedCache configMixedSharedCache;

    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public void init(Staff staff) {
        TradeConfig tradeConfig = new TradeConfig();
        tradeConfig.setShareDiscount(1);//默认开启 @罗俊峰
        tradeConfig.setAutoShare(1);//默认开启
        String chatConfigs = "{\"openScalpNotApplyStock\":1,\"openRefundpNotApplyStock\":1,\"openPartRefund\":1}";
        if (staff.getCompany().isKmt()) {
            chatConfigs = "{\"openScalpNotApplyStock\":1,\"openRefundpNotApplyStock\":1,\"openPartRefund\":1,\"auditAutoInsufficientCanceled\":1}";
        }
        tradeConfig.setChatConfigs(chatConfigs);
        tradeConfig.setTradeExtendConfig("{}");
        tradeConfigDao.insert(staff, tradeConfig);
        Logs.ifDebug(LogHelper.buildLogHead(staff).append("初始化交易配置成功,chatConfigs=").append(chatConfigs));
    }

    private String getTradeConfigCacheKey(Staff staff) {
        return "trade_config_" + staff.getCompanyId();
    }

    @Override
    public TradeConfig get(Staff staff) {
        if (staff.getCompanyId() == null) {
            Logs.ifDebug(LogHelper.buildLog(staff, "获取config配置CompanyId为空，无法获取"));
            return null;
        }
        String key = getTradeConfigCacheKey(staff);
        TradeConfig config = commonMixedSharedCache.get(key);
        if (config != null) {
            return config;
        }
        config = tradeConfigDao.get(staff);
        if (null == config) {
            Logs.ifDebug(LogHelper.buildLog(staff, "没有查询到tradeConfig,初始化一个"));
            String tradeConfigInitKey = "trade_config_init_key_" + staff.getCompanyId();
                try {
                    boolean flag = cache.add(tradeConfigInitKey, 1, 20);
                    if (!flag) {
                        throw new RuntimeException("当前正在初始化系统配置, 请重试");
                    }
                    init(staff);
                } catch (CacheException e) {
                    throw new RuntimeException(e);
                } finally {
                    try {
                        cache.delete(tradeConfigInitKey);
                    } catch (CacheException e) {
                        logger.error(LogHelper.buildLog(staff, "删除缓存异常, key: " + tradeConfigInitKey));
                    }
                }
            config = tradeConfigDao.get(staff);
        }

        Company company = staff.getCompany();
        if(company == null){
            logger.debug(LogHelper.buildLog(staff, "查询公司信息, companyId: " + staff.getCompanyId()));
            company = companyService.queryById(staff.getCompanyId());
            staff.setCompany(company);
        }

        Map<String, Object> defaultMap = Arrays.stream(TradeExtendConfigsEnum.values())
                .filter(t -> t.getDefaultValue() != null)
                .collect(Collectors.toMap(TradeExtendConfigsEnum::getKey, TradeExtendConfigsEnum::getDefaultValue));
        Conf conf = staff.getConf();
        if (conf != null) {
            config.setOpenWave(conf.getOpenWave());
            config.setOpenPrintDelay(conf.getOpenPrintDelay());
            config.setOpenSeed(conf.getOpenSeed());
            config.setOpenSeedPrint(conf.getOpenSeedPrint());
            config.setOpenSeedRowPosition(conf.getOpenSeedRowPosition());
            config.setSeedRowNum(conf.getSeedRowNum());
            config.setSeedRowPositionNum(conf.getSeedRowPositionNum());
            config.setWaveAutoGetWaybillCode(conf.getWaveAutoGetWaybillCode());
            if (config.get(TradeExtendConfigsEnum.OPEN_EXPRESS_MATCH_FREIGHT.getKey()) == null) {
                defaultMap.put(TradeExtendConfigsEnum.OPEN_EXPRESS_MATCH_FREIGHT.getKey(), conf.getExpressMatchPriority());
            }
            defaultMap.put("blurTrade", conf.getBlurTrade());
            //https://gykj.yuque.com/entavv/xb9xi5/aw27l5#jgvPZ
            if (config.get(TradeExtendConfigsEnum.TRADE_ITEM_COST_CONFIG.getKey()) == null) {
                //默认取数据配置报表成本价取值
                defaultMap.put(TradeExtendConfigsEnum.TRADE_ITEM_COST_CONFIG.getKey(), conf.getMovingWeighted());
            }
        }

        if (TradeConfigUtils.parseExtendConfigWithResult(defaultMap, config)) {
            handlePaymentInitConf(defaultMap, company);
            config.setChatConfigs(JSONObject.toJSONString(defaultMap));
            if (defaultMap.get(TradeExtendConfigsEnum.OPEN_EXCEP_TRADE_GENERATE_WAVE.getKey()) != null) {
                config.setOpenExcepTradeGenerateWave(Integer.valueOf(defaultMap.getOrDefault(TradeExtendConfigsEnum.OPEN_EXCEP_TRADE_GENERATE_WAVE.getKey(), CommonConstants.VALUE_NO).toString()));
            }
        }
        Map<String, TradeConfigNew> newConfigMap = tradeConfigNewService.getMap(staff, EXCEPTION_NOT_LOCK_STOCK, ITEM_NUM_EXCLUDE_VIRTUAL, ITEM_NUM_EXCLUDE_NON_CONSIGN, EXPRESS_WHETHER_FILTER_TRADE);
        config.setAutoUnattainableNotApplyStock(Integer.valueOf(newConfigMap.get(EXCEPTION_NOT_LOCK_STOCK.getConfigKey()).getConfigValue()));
        config.setItemNumExcludeVirtual(Integer.valueOf(newConfigMap.get(ITEM_NUM_EXCLUDE_VIRTUAL.getConfigKey()).getConfigValue()));
        config.setItemNumExcludeNonConsign(Integer.valueOf(newConfigMap.get(ITEM_NUM_EXCLUDE_NON_CONSIGN.getConfigKey()).getConfigValue()));
        TradeConfigNew expressWhetherFilterTrade = newConfigMap.get(EXPRESS_WHETHER_FILTER_TRADE.getConfigKey());
        config.setExpressWhetherFilterTrade(expressWhetherFilterTrade.getConfigValue() == null ? 0 : Integer.valueOf(expressWhetherFilterTrade.getConfigValue()));
        TradeConfigNew mergeTradeClearTemplate = tradeConfigNewService.get(staff, TradeConfigEnum.MERGE_TRADE_CLEAR_TEMPLATE);
        config.setMergeTradeClearTemplate(mergeTradeClearTemplate.getConfigValue() == null ? 0 : Integer.valueOf(mergeTradeClearTemplate.getConfigValue()));
        commonMixedSharedCache.set(key, config, 60 * 60 * 24);
        TradeConfigNew abroadAuditAutoGetWaybillCode = tradeConfigNewService.get(staff, TradeConfigEnum.ABROAD_AUDIT_AUTO_GET_WAYBILL_CODE);
        config.setAbroadAuditAutoGetWaybillCode(abroadAuditAutoGetWaybillCode.getConfigValue() == null ? 0 : Integer.valueOf(abroadAuditAutoGetWaybillCode.getConfigValue()));
        return config;
    }

    /**
     * 为金额配置设置默认值
     * @param defaultMap
     * @param company
     */
    private void handlePaymentInitConf(Map<String, Object> defaultMap, Company company){
        Object paymentInitConf = defaultMap.get(TRADE_PLATFORM_AMOUNT_MAPPING.getConfigKey());
        String defaultVal = null;
        try {
            if (tradeLocalConfig.isCompanyCreatedAfterPaymentNewInitTime(company)) {
                defaultVal = TRADE_PLATFORM_AMOUNT_MAPPING_NEW.getDefaultValue();
            } else {
                defaultVal = TRADE_PLATFORM_AMOUNT_MAPPING.getDefaultValue();
            }
            JSONArray defaultValArr = JSONObject.parseArray(defaultVal);
            if (paymentInitConf == null) {
                defaultMap.put(TRADE_PLATFORM_AMOUNT_MAPPING.getConfigKey(), defaultValArr);
            } else {
                JSONArray paymentInitArr = null;
                if (paymentInitConf instanceof JSONArray) {
                    paymentInitArr = (JSONArray) paymentInitConf;
                } else if (paymentInitConf instanceof String) {
                    paymentInitArr = JSONObject.parseArray((String) paymentInitConf);
                }
                JSONObject defaultValJSON = (JSONObject) defaultValArr.get(0);
                JSONObject paymentInitJSON = (JSONObject) paymentInitArr.get(0);
                if (defaultValJSON == null) {
                    return;
                }
                if (paymentInitJSON == null) {
                    defaultMap.put(TRADE_PLATFORM_AMOUNT_MAPPING.getConfigKey(), defaultValArr);
                    return;
                }
                Iterator<String> defaultValIt = defaultValJSON.keySet().iterator();
                while (defaultValIt.hasNext()) {
                    String source = (String) defaultValIt.next();
                    if (!paymentInitJSON.containsKey(source)) {//没有的则使用默认值
                        paymentInitJSON.put(source, defaultValJSON.get(source));
                    } else if(CommonConstants.PLAT_FORM_TYPE_JD.equals(source) && "1".equals(paymentInitJSON.getString(source))){
                        paymentInitJSON.put(source, "2");//京东去除调用户侧金额配置，改为2
                    }
                }
                defaultMap.put(TRADE_PLATFORM_AMOUNT_MAPPING.getConfigKey(), paymentInitArr);
            }
        } catch (Exception e){
            logger.error(String.format("设置金额配置信息出错，companyId:%s,tradePlatformAmountMapping:%s,defaultVal:%s", company.getId(), paymentInitConf, defaultVal), e);
        }
    }

    private void deleteTradeConfigCache(Staff staff){
        String key = getTradeConfigCacheKey(staff);
        commonMixedSharedCache.delete(key);
        commonMixedSharedCache.delete("tradeConfigNewServiceGetAllList" + "_" + staff.getCompanyId());
        configMixedSharedCache.deleteAll(staff);
        eventCenter.fireEvent(this, new EventInfo(TradeConfigDelayDeleteCache.EC_NAME).
                setArgs(new Object[]{staff, key}), null);
    }
    @Override
    @Transactional
    public void update(Staff staff, TradeConfig config) {
        TradeConfig oldConfig = tradeConfigDao.get(staff);
        initExtendConfig(staff, config, oldConfig);
        initTradeExtendConfig(staff, config, oldConfig);
        verifyUpdate(staff, config, oldConfig);
        updateConf(staff, config);
        tradeConfigDao.update(staff, config);
        deleteTradeConfigCache(staff);
        buildUpdateLog(staff, config, oldConfig);
    }

    @Override
    public void update(Staff staff, String field, Object value) {
        tradeConfigDao.update(staff, field, value);
        deleteTradeConfigCache(staff);
    }

    @Override
    public TradeConfig getForSync(Staff staff) {
        TradeConfig config = tradeConfigDao.get(staff);
        if (config == null) {
            logger.info(LogHelper.buildLog(staff, "没有查询到tradeConfig,初始化一个"));
            init(staff);
            config = tradeConfigDao.get(staff);
        }
        return config;
    }

    /**
     * 更新 companyProfile
     *
     * @param staff
     * @param config
     */
    private void updateConf(Staff staff, TradeConfig config) {
        //防止覆盖
        if (config.getOpenWave() == null && config.getOpenPrintDelay() == null) {
            return;
        }
        //只有【是否开启重新上传发货】开启  是否开启预发货的拆分订单重新上传发货 才生效 否则不启用
        if (config.getOpenReUploadConsign() == null || config.getOpenReUploadConsign() == 0) {
            config.setOpenSplitReUploadConsign(0);
        }

        //对于波次开关的处理
        Conf conf = staff.getConf();
        if (!Integer.valueOf(1).equals(conf.getOpenWave()) && Integer.valueOf(1).equals(config.getOpenWave())) {//开启波次打印
            Long count = waveRuleDao.count(staff);
            if (count.intValue() == 0) {
                //TODO:插入初始数据
                List<WaveRule> rules = new ArrayList<WaveRule>();

                WaveRule rule = new WaveRule();
                rule.setExpressEq(true);
                rule.setNumDown(1);
                rule.setNumUp(500);
                rule.setName("秒杀单");
                rule.setSort(1);
                rule.setCreated(new Date());

                WaveRuleCondition condition = new WaveRuleCondition();
                condition.setItemEq(true);
                condition.setItemNumDown(1);
                condition.setItemNumUp(1);
                rule.setRuleCondition(condition);
                rules.add(rule);

                WaveRule rule1 = new WaveRule();
                rule1.setExpressEq(true);
                rule1.setNumDown(1);
                rule1.setNumUp(500);
                rule1.setName("单件");
                rule1.setSort(3);
                rule1.setCreated(new Date());

                WaveRuleCondition condition1 = new WaveRuleCondition();
                condition1.setItemNumDown(1);
                condition1.setItemNumUp(1);
                rule1.setRuleCondition(condition1);
                rules.add(rule1);

                WaveRule rule2 = new WaveRule();
                rule2.setExpressEq(true);
                rule2.setNumDown(1);
                rule2.setNumUp(500);
                rule2.setName("单品");
                rule2.setSort(2);
                rule2.setCreated(new Date());

                WaveRuleCondition condition2 = new WaveRuleCondition();
                condition2.setItemEq(true);
                condition2.setItemKindsDown(1);
                condition2.setItemKindsUp(1);
                rule2.setRuleCondition(condition2);
                rules.add(rule2);

                for (WaveRule r : rules) {
                    r.setCondition(JSONObject.toJSONString(r.getRuleCondition()));
                    waveRuleDao.insert(staff, r);
                }
            }
        }
        if (config.getOpenPrintDelay() != null) {
            conf.setOpenPrintDelay(config.getOpenPrintDelay());
            if (config.getOpenPrintDelay() != null && conf.getOpenPrintDelay() == 0) {
                conf.setOpenSeed(0);
                conf.setOpenSeedPrint(0);
            } else {
                if (config.getOpenSeed() != null) {
                    conf.setOpenSeed(config.getOpenSeed());
                }
                if (config.getOpenSeedPrint() != null) {
                    conf.setOpenSeedPrint(config.getOpenSeedPrint());
                }
            }
            if (config.getSeedRowNum() != null) {
                Map<String, Object> confAttrInfo = conf.getConfAttrInfo();
                if (confAttrInfo == null) {
                    confAttrInfo = new HashMap<>();
                    conf.setConfAttrInfo(confAttrInfo);
                }
                confAttrInfo.put("seedRowNum", config.getSeedRowNum());
            }
            if (config.getSeedRowPositionNum() != null) {
                conf.setSeedRowPositionNum(config.getSeedRowPositionNum());
            }
            if (config.getOpenSeedRowPosition() != null) {
                conf.setOpenSeedRowPosition(config.getOpenSeedRowPosition());
            }
        }

        if (null != config.getWaveAutoGetWaybillCode()) {
            conf.setWaveAutoGetWaybillCode(config.getWaveAutoGetWaybillCode());
        }

        boolean printChanged = conf.getOpenWave() != (config.getOpenWave() == null ? 0 : config.getOpenWave());

        if (config.getOpenWave() != null) {
            conf.setOpenWave(config.getOpenWave());
        }
        //logger.debug(LogHelper.buildLog(staff, "conf:" + JSONObject.toJSONString(staff.getCompany().getProfile().getConf())));
        companyService.updateCompanyProfile(staff, staff.getCompany().getProfile());

        if (printChanged) {
            // 更新完成之后，清除缓存
            try {
                commonMixedCache.delete("privilege_array_normal_" + staff.getCompanyId());
                commonMixedCache.delete("all_privilege");
                commonMixedCache.delete("company_privileges_" + staff.getCompanyId());
                //				cache.delete("privilege_" + privilegeId);
                //				cache.delete("privilege_children_" + privilegeId);
                //				cache.delete("privilege_direct_children_" + privilegeId);
                //				cache.delete("privilege_kind_" + privilegeId);
                commonMixedCache.delete("privilege_tree_" + staff.getCompanyId());

                commonMixedCache.delete("company_users_" + staff.getCompanyId());
                commonMixedCache.delete("company_master_user_" + staff.getCompanyId());
                commonMixedCache.delete("privilege_direct_children_" + staff.getCompanyId());
                commonMixedCache.delete("company_privileges_" + staff.getCompanyId());

                Long rootStaffId = staffService.queryDefaultStaff(staff.getCompanyId()).getId();

                List<Staff> staffs = staffService.queryByCompanyId(staff.getCompanyId(), staff.getCompanyName());
                for (Staff cur : staffs) {
                    commonMixedCache.delete("staff_menu_" + cur.getId());
                }

                commonMixedCache.delete("staff_" + rootStaffId);
                commonMixedCache.delete("staff_info_" + rootStaffId);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "清除缓存失败"), e);
            }
        }
    }

    private void verifyUpdate(Staff staff, TradeConfig config, TradeConfig oldConfig) {
        Integer salePrice = config.getInteger(NewTradeExtendConfigEnum.FX_SALE_PRICE_FIELD_SOURCE);
        Integer cost = config.getInteger(NewTradeExtendConfigEnum.FX_COST_FIELD_SOURCE);
        if(Objects.equals(salePrice,0) && Objects.equals(cost,1) ){
            throw new IllegalArgumentException("订单列表中, \"分销\"和\"成本\"字段的取值配置,必需至少有一个为\"对应供销商的商品分销价\"");
        }

        TradeConfigCheckContext ctx = new TradeConfigCheckContext();
        verifyUpdatePack(staff, ctx, oldConfig, config);
        verifyUpdateWeigh(staff, ctx, config, oldConfig);
        verifyUpdateConsign(staff, ctx, config, oldConfig);
        checkUpdateFinanceAudit(staff, config, oldConfig);
        verifyOpenScalpNotApplyStock(staff, config, oldConfig);
        verifyOpenRefundpNotApplyStock(staff, config, oldConfig);
        verifyUpdateBtasQuality(staff, config, oldConfig);
        verifyTimeoutLimit(staff, config);
        //检验推送配置
        verifyChatConfigs(staff, config);
        verifyCloudMatch(staff, config);
        verifyFirstRuleThenAllocate(staff, config);
        verifyAutoCreateWave(staff, config);
        Integer openUploadConsign = config.getOpenUploadConsign();
        //开启打印、验货、称重的自动上传发货开关
        if (openUploadConsign != null && openUploadConsign == 1) {
            Integer uploadConsignName = config.getUploadConsignName();
            if (uploadConsignName != null && uploadConsignName == 2) {//验货
                Integer oldOpenPackageExamine = oldConfig.getOpenPackageExamine();
                if (oldOpenPackageExamine == null || oldOpenPackageExamine != 1) {
                    throw new IllegalArgumentException("包装验货已关闭，无法开启验货自动上传发货！");
                }
            } else if (uploadConsignName != null && uploadConsignName == 3) {//称重
                Integer oldOpenPackageWeigh = oldConfig.getOpenPackageWeigh();
                if (oldOpenPackageWeigh == null || oldOpenPackageWeigh != 1) {
                    throw new IllegalArgumentException("包装称重已关闭，无法开启称重自动上传发货！");
                }
            }
        }

        boolean seedClose = config.getOpenSeed() != null && (!config.getOpenSeed().equals(staff.getConf().getOpenSeed())) && config.getOpenSeed() == 0;
        boolean seedPrintOpen = config.getOpenSeedPrint() != null && (!config.getOpenSeedPrint().equals(staff.getConf().getOpenSeedPrint())) && config.getOpenSeedPrint() == 1;
        boolean printDelayClose = config.getOpenPrintDelay() != null && (!config.getOpenPrintDelay().equals(staff.getConf().getOpenPrintDelay())) && config.getOpenPrintDelay() == 0;
        if (printDelayClose || seedClose) {
            boolean flag = tradeWaveService.checkHasDistributionWaves(staff, printDelayClose ? "1,2,3,4" : "3");
            if (flag) {
                if (printDelayClose) {
                    throw new IllegalArgumentException("存在进行中的后置打印波次！");
                } else {
                    throw new IllegalArgumentException("存在'播种中'的后置打印波次！");
                }
            }
        }

        if (seedPrintOpen && (config.getOpenSeed() == null || config.getOpenSeed() <= 0)) {
            throw new IllegalArgumentException("请先开启'多品多件是否启用播种'！");
        }

        Integer seedRowPositionNum = config.getSeedRowPositionNum();
        if (seedRowPositionNum == null || seedRowPositionNum <= 0) {
            config.setSeedRowPositionNum(null);
        }
        tradeConfigBusiness.addressAppliedSwitch(staff, config, oldConfig);
    }

    /**
     * 校验开启同步时使用菜鸟智选物流
     *
     * @param staff
     * @param config
     */
    private void verifyCloudMatch(Staff staff, TradeConfig config) {
        int integer = config.getInteger(TradeExtendConfigsEnum.OPEN_CLOUD_SMART_MATCH.getKey());
        if (integer == 1 && 1 != staff.getConf().getOpenIntelLigent()) {
            throw new IllegalArgumentException("请先开启智选物流！");
        }
    }

    private void changeOpenUploadConsign(TradeConfig config, TradeConfig oldConfig, int state) {
        Integer oldOpenUploadConsign = oldConfig.getOpenUploadConsign();
        if (oldOpenUploadConsign == null || oldOpenUploadConsign == 0) {
            return;
        }
        Integer oldUploadConsignName = oldConfig.getUploadConsignName();
        //关闭验货、称重的时候，需要将验货自动上传发货的开关也关掉
        if (oldUploadConsignName != null && oldUploadConsignName == state) {
            config.setOpenUploadConsign(0);
            config.setUploadConsignName(0);
        }
    }

    /**
     * 校验关闭验货
     */
    private void verifyUpdatePack(Staff staff, TradeConfigCheckContext ctx, TradeConfig oldConfig, TradeConfig config) {
        if (config.getOpenPackageExamine() != null && config.getOpenPackageExamine() == 0) {
            if (tradeCountBusiness.hasWaitPack(staff, ctx, oldConfig)) {
                throw new IllegalArgumentException("当前还有待包装订单未处理，请先处理再关闭包装开关!");
            }
            changeOpenUploadConsign(config, oldConfig, 2);
        }
    }

    private void verifyFirstRuleThenAllocate(Staff staff, TradeConfig config) {
        if (!WmsUtils.isNewWms(staff)) {
            config.setOpenRuleFirstThenAllocate(0);
        }
    }

    private void verifyAutoCreateWave(Staff staff, TradeConfig config) {
        if (DataUtils.checkI(config.getAutoCreateWave())) {
            Assert.notNull(config.getAutoCreateWave(), "开启自动生成波次，请先设置间隔时间！");
        }
    }

    private void verifyUpdateStaffWaveRule(Staff staff, TradeConfig config, TradeConfig oldConfig) {
        if (config == null || oldConfig == null
                || StringUtils.isEmpty(config.getChatConfigs())) {
            return;
        }
        int openStallWaveRule = config.getInteger(TradeExtendConfigsEnum.OPEN_STALL_WAVE_RULE.getKey());
        int oldOpenStallWaveRule = oldConfig.getInteger(TradeExtendConfigsEnum.OPEN_STALL_WAVE_RULE.getKey());
        if (openStallWaveRule == oldOpenStallWaveRule) {
            return;
        }

        WaveRule waveRuleQuery = new WaveRule();
        waveRuleQuery.setRuleType(WaveRule.RULE_TYPE_STALL);
        List<WaveRule> rules = tradeWaveService.queryRules(staff, waveRuleQuery);
        try {
            // 关闭档口波次规则
            if (openStallWaveRule == 0 && !CollectionUtils.isEmpty(rules)) {
                tradeWaveService.deleteRule(staff, rules.get(0).getId());
            }
            // 开启档口波次规则
            if (openStallWaveRule == 1 && CollectionUtils.isEmpty(rules)) {
                tradeWaveService.saveWaveRule(staff, WaveUtils.buildStallWaveRule(null));
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "修改开启档口波次规则失败！"), e);
        }
    }

    /**
     * 校验发货提醒时间设置
     */
    private void verifyTimeoutLimit(Staff staff, TradeConfig config) {
        if (StringUtils.isNotEmpty(config.getTimeoutLimit())) {
            BigDecimal bigDecimal = new BigDecimal(config.getTimeoutLimit());
            Assert.isTrue(bigDecimal.doubleValue() > 0 && bigDecimal.doubleValue() < 999999.9, "发货提醒时限需大于0且小于999999.9");
            Assert.isTrue(bigDecimal.scale() < 2, "最多保留1位小数");
        }
    }

    /**
     * 校验关闭称重
     */
    private void verifyUpdateWeigh(Staff staff, TradeConfigCheckContext ctx, TradeConfig config, TradeConfig oldConfig) {
        if (config.getOpenPackageWeigh() != null && config.getOpenPackageWeigh() == 0) {//关闭称重
            if (tradeCountBusiness.hasWaitWeigh(staff, ctx, oldConfig)) {
                throw new IllegalArgumentException("当前还有待称重订单未处理，请先处理再关闭称重开关!");
            }
            changeOpenUploadConsign(config, oldConfig, 3);
        }
    }

    /**
     * 校验关闭发货
     */
    private void verifyUpdateConsign(Staff staff, TradeConfigCheckContext ctx, TradeConfig config, TradeConfig oldConfig) {
        if (config.getOpenConsign() != null && config.getOpenConsign() == 0) {//关闭发货
            if (tradeCountBusiness.hasWaitConsign(staff, ctx, oldConfig)) {
                throw new IllegalArgumentException("当前还有待发货订单未处理，请先处理再关闭发货开关!");
            }
        }
    }

    /**
     * 判断财审开关是否可关闭
     *
     * @param staff
     * @param config
     * @param origin
     * @return
     */
    private void checkUpdateFinanceAudit(Staff staff, TradeConfig config, TradeConfig origin) {
        Integer openFinanceAudit = config.getOpenFinanceAudit();
        if (openFinanceAudit == null) {
            return;
        }
        int oldOpenFinanceAudit = origin.getOpenFinanceAudit();
        if (oldOpenFinanceAudit == 0 && config.getOpenFinanceAudit() == 0) {
            return;
        }

        if (oldOpenFinanceAudit == 1 && openFinanceAudit == 0) {
            Assert.isTrue(!tradeCountBusiness.hasWaitFinanceAudit(staff, null, null), "当前还有待财审订单未处理，请先处理再关闭财审开关!");
        }

        addUpdateLog(staff, ((openFinanceAudit > 0) ? "开启" : "关闭") + "财审流程");
    }

    private void verifyOpenScalpNotApplyStock(Staff staff, TradeConfig config, TradeConfig origin) {
        if (StringUtils.isNotEmpty(config.getChatConfigs())) {
            if (config.getOpenScalpNotApplyStock() - origin.getOpenScalpNotApplyStock() != 0 && tradeCountBusiness.hasBeforeConsignScalp(staff, origin)) {
                throw new IllegalArgumentException("订单中还有发货前的空包订单，不允许修改配置!");
            }
        }
    }

    private void verifyOpenRefundpNotApplyStock(Staff staff, TradeConfig config, TradeConfig origin) {
        if (StringUtils.isNotEmpty(config.getChatConfigs())) {
            if (config.getOpenRefundNotApplyStock() - origin.getOpenRefundNotApplyStock() != 0 && tradeCountBusiness.hasBeforeRefund(staff, origin)) {
                throw new IllegalArgumentException("存在退款中订单，不允许更改配置!");
            }
        }
    }

    private void verifyUpdateBtasQuality(Staff staff, TradeConfig config, TradeConfig origin) {
        List<Long> deleteUserIds = new ArrayList<>();
        Integer openNew = (Integer) config.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING.getKey());
        Integer openOld = (Integer) origin.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING.getKey());
        if (null == openNew || null == openOld) {
            return;
        }
        Integer open = 1;
        if (open.equals(openNew) && !open.equals(openOld)) {
            //BTAS质检配置从关->开，此时无须校验
        } else if (open.equals(openNew)) {
            //BTAS质检配置开关未更改（为开），查询店铺是否有下线,此时对比旧配置，缺少的店铺为下限店铺
            deleteUserIds = parseDeleteUserIds((String) config.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING_SHOP.getKey()),
                    (String) origin.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING_SHOP.getKey()), true);
        } else if (open.equals(openOld)) {
            //BTAS质检配置从开->关，旧配置的所有店铺均下线
            deleteUserIds = parseDeleteUserIds((String) config.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING_SHOP.getKey()),
                    (String) origin.get(TradeExtendConfigsEnum.OPEN_BTAS_QUALITY_TESTING_SHOP.getKey()), false);
        } else {
            //BTAS质检配置开关未更改（为关），无须校验
        }
        if (!CollectionUtils.isEmpty(deleteUserIds)) {
            //移除的店铺，查询是否有被删除，排除掉被删除的店铺
            List<User> users = userService.queryByIdList(staff.getCompanyId(), deleteUserIds);
            if (CollectionUtils.isEmpty(users)){
                //配置中被移除的店铺，都被删除，不进行后续校验
                return;
            }
            //查询这些店铺内是否有待审核、或已审核未发货的BTAS订单
            TradeQueryParams tradeQueryParams = new TradeQueryParams();
            tradeQueryParams.setQueryFlag(2);
            tradeQueryParams.setPage(new Page(1, 10));
            tradeQueryParams.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_SEND_GOODS);
            tradeQueryParams.setUserIds(users.stream().map(User::getId).toArray(Long[]::new));
            tradeQueryParams.setTradeType(TradeTypeEnum.BTAS.getcode());
            tradeQueryParams.setIsCancel(0);
            tradeQueryParams.setQueryOrder(false);
            if (tradeSearchService.search(staff, tradeQueryParams).getTotal().intValue() > 0) {
                throw new IllegalArgumentException(String.format("店铺[%s]存在待审核或已审核未发货的Btas订单，不允许关闭Btas配置!", deleteUserIds));
            }
        }
    }

    private List<Long> parseDeleteUserIds(String shopsNew, String shopsOld, boolean compare) {
        List<Long> userIdsNew = new ArrayList<>();
        List<Long> userIdsOld = new ArrayList<>();
        if (StringUtils.isNotEmpty(shopsNew)) {
            userIdsNew = Arrays.stream(shopsNew.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(shopsOld)) {
            userIdsOld = Arrays.stream(shopsOld.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }

        if (compare) {
            List<Long> finalUserIdsNew = userIdsNew;
            return userIdsOld.stream().filter(t -> !finalUserIdsNew.contains(t)).collect(Collectors.toList());
        } else {
            return userIdsOld;
        }
    }

    private String buildFinanceLog(Staff staff, Set<Long> updateUserIds, Integer openFinanceAudit) {
        return new StringBuilder((openFinanceAudit != null && openFinanceAudit > 0) ? "开启" : "关闭").append("财审流程").toString();
    }

    private void addUpdateLog(Staff staff, String content) {
        if (StringUtils.isNotEmpty(content)) {
            OpLog log = new OpLog();
            log.setDomain(OpLog.DOMAIN_TRADE);
            log.setAction("trade.config.update");
            log.setKey(staff.getCompanyId().toString());
            log.setCompanyId(staff.getCompanyId());
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setAccountName(staff.getAccountName());
            log.setContent(content);
            opLogService.record(staff, log);
        }
    }

    private void buildUpdateLog(Staff staff, TradeConfig newConf, TradeConfig oldConf) {
        StringBuilder buf = new StringBuilder();
        ConfigLogUtils.addLogContent(buf, "金额联动", newConf.getOpenLinkPayment(), oldConf.getOpenLinkPayment());
        ConfigLogUtils.addLogContent(buf, "价格修改", newConf.getOpenPriceUpdate(), oldConf.getOpenPriceUpdate());
        ConfigLogUtils.addLogContent(buf, "财审", newConf.getOpenFinanceAudit(), oldConf.getOpenFinanceAudit());
        ConfigLogUtils.addLogContent(buf, "覆盖模式", newConf.getCoverSysStatus(), oldConf.getCoverSysStatus());
        ConfigLogUtils.addLogContent(buf, "包装", newConf.getOpenPackageExamine(), oldConf.getOpenPackageExamine());
        ConfigLogUtils.addLogContent(buf, "称重", newConf.getOpenPackageWeigh(), oldConf.getOpenPackageWeigh());
        ConfigLogUtils.addLogContent(buf, "发货", newConf.getOpenConsign(), oldConf.getOpenConsign());
        ConfigLogUtils.addLogContent(buf, "发货上传", newConf.getOpenUploadConsign(), oldConf.getOpenUploadConsign());
        ConfigLogUtils.addLogContent(buf, "发货上传环节", newConf.getUploadConsignName(), oldConf.getUploadConsignName());
        ConfigLogUtils.addLogContent(buf, "订单图片展示", newConf.getOrderImageSource(), oldConf.getOrderImageSource());
        ConfigLogUtils.addLogContent(buf, "同步待付款订单", newConf.getSyncWaitPay(), oldConf.getSyncWaitPay());
        ConfigLogUtils.addLogContent(buf, "赠品自动匹配", newConf.getAutoGiftMatch(), oldConf.getAutoGiftMatch());
        ConfigLogUtils.addLogContent(buf, "赠品重新匹配", newConf.getRematchGift(), oldConf.getRematchGift());
        ConfigLogUtils.addLogContent(buf, "黑名单", newConf.getOpenBlack(), oldConf.getOpenBlack());
        ConfigLogUtils.addLogContent(buf, "波次自动生成", newConf.getAutoCreateWave(), oldConf.getAutoCreateWave());
        ConfigLogUtils.addLogContent(buf, "波次自动生成时间间隔", newConf.getAutoCreateWaveTimeInterval(), oldConf.getAutoCreateWaveTimeInterval());
        ConfigLogUtils.addLogContent(buf, "播种打印后支持包装验货", newConf.getOpenWavePackage(), oldConf.getOpenWavePackage());
        ConfigLogUtils.addLogContent(buf, "单件波次订单打印后，PDA/PC拣选完成后自动完成验货", newConf.getSingleWaveAutoPackage(), oldConf.getSingleWaveAutoPackage());
        ConfigLogUtils.addLogContent(buf, "智能合单异常订单是否合并", newConf.getMamFilterExcep(), oldConf.getMamFilterExcep());
        ConfigLogUtils.addLogContent(buf, "智能合单拆单是否合并", newConf.getMamFilterSplit(), oldConf.getMamFilterSplit());
        ConfigLogUtils.addLogContent(buf, "智能合单已取消合单的订单是否合并", newConf.getMamFilterUndo(), oldConf.getMamFilterUndo());
        ConfigLogUtils.addLogContent(buf, "是否开启备注异常", newConf.getOpenMemoExp(), oldConf.getOpenMemoExp());
        ConfigLogUtils.addLogContent(buf, "取消审核是否保留单号", newConf.getUnAuditWaybillCancel(), oldConf.getUnAuditWaybillCancel());
        ConfigLogUtils.addLogContent(buf, "结束波次时自动重新审核未完成的订单", newConf.getFinishWaveTradeAutoUnaudit(), oldConf.getFinishWaveTradeAutoUnaudit());
        ConfigLogUtils.addLogContent(buf, "强制包装验货", newConf.getOpenForceTradePack(), oldConf.getOpenForceTradePack());
        ConfigLogUtils.addLogContent(buf, "是否开启自动审核", newConf.getOpenAutoAudit(), oldConf.getOpenAutoAudit());
        ConfigLogUtils.addLogContent(buf, "自动反审核", newConf.getAutoCancelAudit(), oldConf.getAutoCancelAudit());
        ConfigLogUtils.addLogContent(buf, "是否开启拆单缺货自动上传备注", newConf.getOpenUploadSellerMemo(), oldConf.getOpenUploadSellerMemo());
        ConfigLogUtils.addLogContent(buf, "拼多多订单发货后（已发货/交易成功），允许修改快递模板", newConf.getAllowChangeTemplate(), oldConf.getAllowChangeTemplate());
        ConfigLogUtils.addLogContent(buf, "重新审核重新计算缺货发货订单库存", newConf.getUnAuditReplaceInsufficientCanceled(), oldConf.getUnAuditReplaceInsufficientCanceled());
        ConfigLogUtils.addLogContent(buf, "智能审核先匹配模板后审核", newConf.getOpenAutoAuditMatchTemplate(), oldConf.getOpenAutoAuditMatchTemplate());
        ConfigLogUtils.addLogContent(buf, "审核后自动获取单号", newConf.getAuditAutoGetWaybillCode(), oldConf.getAuditAutoGetWaybillCode());
//        ConfigLogUtils.addLogContent(buf, "推送配置", newConf.getChatConfigs(), oldConf.getChatConfigs());
        ConfigLogUtils.addLogContent(buf, "审核后自动获取单号", newConf.getAuditAutoGetWaybillCode(), oldConf.getAuditAutoGetWaybillCode());
        ConfigLogUtils.addLogContent(buf, "波次播种一个波次只能由一个账号进行播种", newConf.getOpenTradeWaveSeedSorter(), oldConf.getOpenTradeWaveSeedSorter());
        ConfigLogUtils.addLogContent(buf, "允许打印单件波次", newConf.getOpenPrintSingleWave(), oldConf.getOpenPrintSingleWave());
        ConfigLogUtils.addLogContent(buf, "是否开启支持撤销发货", newConf.getCancelConsign(), oldConf.getCancelConsign());
        ConfigLogUtils.addLogContent(buf, "开启波次唯一码管理", newConf.getOpenWaveUniqueCode(), oldConf.getOpenWaveUniqueCode());
        ConfigLogUtils.addLogContent(buf, "是否生成波次时，支持填写波次备注", newConf.getOpenWaveRemark(), oldConf.getOpenWaveRemark());
        ConfigLogUtils.addLogContent(buf, "波次播种支持对所有未播订单（包含已打印和未打印）进行播种", newConf.getWaveSeedType(), oldConf.getWaveSeedType());
        ConfigLogUtils.addLogContent(buf, "有打印次数的订单不进入波次", newConf.getPrintedNotInWave(), oldConf.getPrintedNotInWave());
        ConfigLogUtils.addLogContent(buf, "生成波次时，先判断规则，然后配货", newConf.getOpenRuleFirstThenAllocate(), oldConf.getOpenRuleFirstThenAllocate());
        ConfigLogUtils.addLogContent(buf, "波次循环播种时，分配位置号规则", newConf.getLoopWaveAllocateNumRule(), oldConf.getLoopWaveAllocateNumRule());
        ConfigLogUtils.addLogContent(buf, "只允许一单多件波次进入播种流程", newConf.getOnlyTradeMultiSeed(), oldConf.getOnlyTradeMultiSeed());
        ConfigLogUtils.addLogContent(buf, "拣选暂存位库存不足时，不允许出单", newConf.getNegativeOneBlindScanAllocate(), oldConf.getNegativeOneBlindScanAllocate());
        ConfigLogUtils.addLogContent(buf, "自动勾选已打订单", newConf.getAutoSelectPrintedTrade(), oldConf.getAutoSelectPrintedTrade());
        ConfigLogUtils.addLogContent(buf, "拣选中的波次支持重新分配拣选员", newConf.getPickingArrange(), oldConf.getPickingArrange());
        ConfigLogUtils.addLogContent(buf, "播种中的波次支持重新分配播种员", newConf.getSeedingArrange(), oldConf.getSeedingArrange());
        ConfigLogUtils.addLogContent(buf, "订单波次点击【拣选完成】时，自动将货位库存扣到拣选暂存区", newConf.getOpenDirectPickIntoWss(), oldConf.getOpenDirectPickIntoWss());
        ConfigLogUtils.addLogContent(buf, "开启波次编码打印新逻辑", newConf.getOpenWaveCodePrintNew(), oldConf.getOpenWaveCodePrintNew());

        ConfigLogUtils.addLogContent(buf, "播种拆分后，拆分后的未播订单保留原订单号，拆分的已播订单自动生成新的位置号", newConf.getSeedSplitAssignPositionNo(), oldConf.getSeedSplitAssignPositionNo());
        ConfigLogUtils.addLogContent(buf, "拣选中波次允许强制取消", newConf.getOpenPickingCancel(), oldConf.getOpenPickingCancel());
        ConfigLogUtils.addLogContent(buf, "波次拣选时，相同货位的不同商品排序类型", newConf.getPickingSameSectionSortType(), oldConf.getPickingSameSectionSortType());

        // 前端传没有传值就不要做特殊处理了 不然处理为空字符串会被记录日志
        String newSubSectionPickTypes = newConf.getSubSectionPickTypes() == null ? null : PickingType.getTypeNames(newConf.getSubSectionPickTypes());
        String oldSubSectionPickTypes = oldConf.getSubSectionPickTypes() == null ? null : PickingType.getTypeNames(oldConf.getSubSectionPickTypes());
        ConfigLogUtils.addLogContent(buf, "开启波次分段拣选", newSubSectionPickTypes, oldSubSectionPickTypes);
        ConfigLogUtils.addLogContent(buf, "退款商品不能播种", newConf.getSeedNotAllowRefund(), oldConf.getSeedNotAllowRefund());

        ConfigLogUtils.addLogContent(buf, "波次列表排序按照相同波次规则排序", newConf.getWaveSortRule(), oldConf.getWaveSortRule());
        ConfigLogUtils.addLogContent(buf, "波次列表排序按照波次创建时间", newConf.getWaveSortTime(), oldConf.getWaveSortTime());
        ConfigLogUtils.addLogContent(buf, "生成波次时自动获取单号", newConf.getWaveAutoGetWaybillCode(), oldConf.getWaveAutoGetWaybillCode());
        ConfigLogUtils.addLogContent(buf, "开启后置打印", newConf.getOpenPrintDelay(), oldConf.getOpenPrintDelay());
        ConfigLogUtils.addLogContent(buf, "多品多件是否启用播种", newConf.getOpenSeed(), oldConf.getOpenSeed());
        ConfigLogUtils.addLogContent(buf, "播种后置打印扫描类型", newConf.getSeedScanType(), oldConf.getSeedScanType());
        ConfigLogUtils.addLogContent(buf, "播种位置号提示为排数", newConf.getOpenSeedRowPosition(), oldConf.getOpenSeedRowPosition());
        ConfigLogUtils.addLogContent(buf, "播种排数", newConf.getSeedRowNum(), oldConf.getSeedRowNum());
        ConfigLogUtils.addLogContent(buf, "播种每排位置数", newConf.getSeedRowPositionNum(), oldConf.getSeedRowPositionNum());
        ConfigLogUtils.addLogContent(buf, "拣选中的波次支持后置/播种打印", newConf.getOpenPrintInPicking(), oldConf.getOpenPrintInPicking());
        ConfigLogUtils.addLogContent(buf, "后置打印出单顺序类型", newConf.getPostPrintSortType(), oldConf.getPostPrintSortType());
        ConfigLogUtils.addLogContent(buf, "波次拣选时，相同货位的不同商品排序类型", newConf.getPickingSameSectionSortType(), oldConf.getPickingSameSectionSortType());
        ConfigLogUtils.addLogContent(buf, "后置打印匹配订单顺序 ", newConf.getPostPrintMatchTradeSort(), oldConf.getPostPrintMatchTradeSort());
        ConfigLogUtils.addLogContent(buf, "包装验货/包裹称重支持扫包材", newConf.getOpenScanItemPackma(), oldConf.getOpenScanItemPackma());
        ConfigLogUtils.addLogContent(buf, "包装验货配置，识别码", newConf.getOpenIdentCode(), oldConf.getOpenIdentCode());
        if (StringUtils.isNotBlank(newConf.getChatConfigs())) {
            for (TradeExtendConfigsEnum value : TradeExtendConfigsEnum.values()) {
                //老版本配置针对于当前"addressChangeAppliedUsers"key不进行存储
                if (Objects.equals(value.getKey(), TradeConfigEnum.ADDRESS_CHANGE_APPLIED_USERS.getConfigKey())) {
                    continue;
                }
                String remark = value.getRemark();
                if (StringUtils.isNotBlank(remark)) {
                    try {
                        ConfigLogUtils.addLogContent(staff, buf, value.getRemark(), newConf.get(value.getKey()), oldConf.get(value.getKey()));
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLogHead(staff).append(String.format("配置修改日志保存出错！buf=%s,remark=%s,newValue=%s,oldValue=%s", buf, value.getRemark(), newConf.get(value.getKey()), oldConf.get(value.getKey()))));
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(newConf.getTradeExtendConfig())) {
            Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(newConf.getTradeExtendConfig());
            Map<String, Object> oldConfigMap = TradeConfigUtils.parseExtendConfig(oldConf.getTradeExtendConfig());
            for (NewTradeExtendConfigEnum value : NewTradeExtendConfigEnum.values()) {
                // 这个配置修改日志波次已经打了，交易不用打了
                if (Objects.equals(NewTradeExtendConfigEnum.CHANGE_ITEM_SUBS_STAGING_STOCK, value) ||
                        Objects.equals(NewTradeExtendConfigEnum.ALLOW_CHANGE_ITEM_WHEN_INSUFFICIENT, value)) {
                    continue;
                }
                if (StringUtils.isNotBlank(value.getRemark())) {
                    try {
                        ConfigLogUtils.addLogContent(staff, buf, value.getRemark(), configMap.get(value.getKey()), oldConfigMap.get(value.getKey()));
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLogHead(staff).append(String.format("extend配置修改日志保存出错！buf=%s,remark=%s,newValue=%s,oldValue=%s", buf, value.getRemark(), newConf.get(value.getKey()), oldConf.get(value.getKey()))));
                    }
                }
            }
        }
        if (buf.length() > 0) {
            addUpdateLog(staff, buf.toString());
        }
    }

    @Override
    public void updateTradeSyncStatus(Long companyId, Long userId, int value) {
        tradeConfigBusiness.updateOpenTradeSync(companyId, userId, value);
    }

    @Override
    public void updateTradeSyncType(long userId, Integer syncType) {
        updateTradeSyncType(userId, syncType, null);
    }

    @Override
    public void updateTradeSyncType(long userId, Integer syncType, String source) {
        tradeConfigBusiness.updateTradeSyncType(userId, syncType, source);
    }

    @Override
    public void updateExcepTradeParty3SendGoods(Staff staff, Integer excepTradeParty3SendGoods) {
        TradeConfig oldConfig = tradeConfigDao.get(staff);
        if (oldConfig == null) {
            init(staff);
            return;
        }

        TradeConfig tradeConfig = new TradeConfig();
        tradeConfig.setCompanyId(staff.getCompanyId());
        tradeConfig.setExcepTradeParty3SendGoods(excepTradeParty3SendGoods);
        tradeConfigDao.update(staff, tradeConfig);
        deleteTradeConfigCache(staff);

        StringBuilder buf = new StringBuilder();
        ConfigLogUtils.addLogContent(buf, "第三方异常订单发货", excepTradeParty3SendGoods, oldConfig.getExcepTradeParty3SendGoods());
        if (buf.length() > 0) {
            OpLog log = new OpLog();
            log.setDomain(OpLog.DOMAIN_TRADE);
            log.setAction("trade.config.update");
            log.setKey(staff.getCompanyId().toString());
            log.setCompanyId(staff.getCompanyId());
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setAccountName(staff.getAccountName());
            log.setContent(buf.toString());
            opLogService.record(staff, log);
        }
    }

    @Override
    public List<Long> queryCompanyIdByChatConfigs(String chatConfig, Object value) {
        return tradeConfigDao.queryCompanyIdByChatConfigs(chatConfig, value, config.getCurrentDbs());
    }


    private void initExtendConfig(Staff staff, TradeConfig newConfig, TradeConfig old) {
        if (newConfig == null) {
            return;
        }
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotBlank(newConfig.getChatConfigs()) &&
                TradeConfigUtils.parseExtendConfigWithResult(result, old) &&
                TradeConfigUtils.parseExtendConfigWithResult(result, newConfig)) {
            handlerSysExceps(staff, result, 1);
            // 新配置不能出现在老配置的接口里 兜底逻辑
            TradeConfigNewUtils.removeNewKey(result);
            newConfig.setChatConfigs(JSONObject.toJSONString(result));
        } else {
            newConfig.setChatConfigs(null);
        }
    }

    public void handlerSysExceps(Staff staff, Map<String, Object> map, int converType) {
        try {
            if (map == null || map.size() == 0) {
                return;
            }
            String sysExceps = (String) map.get(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SYS.getKey());
            if (StringUtils.isBlank(sysExceps)) {
                return;
            }

            String newSysExceps;
            // 系统异常FrontToId
            if (converType == 1) {
                newSysExceps = ConfigCompatibleUtils.sysExceptFrontToId(sysExceps);
            } else {
                newSysExceps = ConfigCompatibleUtils.sysExceptIdToFront(sysExceps);
            }
            map.put(TradeExtendConfigsEnum.INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SYS.getKey(), newSysExceps);
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, String.format("解析系统异常出错,map=%s", map)), e);
        }
    }


    private void initTradeExtendConfig(Staff staff, TradeConfig config, TradeConfig oldConfig) {
        if (config == null) {
            return;
        }

        //针对已经有配置的公司，先初始化这个字段为{}，后续才能插入值
        String tradeExtendConfig = config.getTradeExtendConfig();
        if (StringUtils.isEmpty(oldConfig.getTradeExtendConfig())) {
            config.setTradeExtendConfig("{}");
            tradeConfigDao.updateExtendConfig(staff, config);
            //前面修改为{}后面要改回来，否则转换为json就变成null了
            config.setTradeExtendConfig(tradeExtendConfig);
        }

        Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(config.getTradeExtendConfig());

        List<Object> configList = new ArrayList<>();
        //$符号在ibatis中会被识别为分割符
        //又因为ibatis只能传list不能传map，所以要把map的key和value放入list中
        //且只有key需要拼接$符，value不需要，所以把拼接$符的操作放在此处
        StringBuilder temp = new StringBuilder("$.");
        configMap.forEach((key, value) -> {
            temp.append(key);
            configList.add(temp.toString());
            configList.add(value);
            temp.delete(2, temp.length());
        });
        if (StringUtils.isNotBlank(config.getTradeExtendConfig()) && configMap.size() > 0) {
            config.setTradeExtendConfigMap(configMap);
            config.setTradeExtendConfigList(configList);
        }
    }


    /**
     * 校验推送配置是否正确
     **/
    private void verifyChatConfigs(Staff staff, TradeConfig config) {
        String chatConfigs = config.getChatConfigs();
        if (StringUtils.isNotBlank(chatConfigs)) {
            JSONObject jsonObject = JSONObject.parseObject(chatConfigs);
            String openDelayChat = jsonObject.getString("openDelayChat");
            String days = jsonObject.getString("days");
            //延迟推送消息开启，天数设置，不能一个开启一个关闭
            if (("1".equals(openDelayChat) && StringUtils.isBlank(days)) || (StringUtils.isNotBlank(days) && "0".equals(openDelayChat))) {
                throw new IllegalArgumentException("延迟未发货自动推送开关和天数设置不正确!");
            }

            //校验包装验货上传备注
            Integer openPackUploadSellerMemo = jsonObject.getInteger("openPackUploadSellerMemo");
            if (openPackUploadSellerMemo != null && openPackUploadSellerMemo == 1) {
                Preconditions.checkArgument(StringUtils.isNotEmpty(jsonObject.getString("packUploadInfo")), "验货成功后上传备注至少要勾选一个字段");
            }
        }
    }

    public int getTbXsdUploadSet(Staff staff){
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TB_XSD_UPLOAD_SET);
        String configValue = tradeConfigNew.getConfigValue();
        if(StringUtils.isEmpty(configValue)){
            return 0;
        }
        return Integer.valueOf(configValue);
    }

    public int getTbXsdUploadSet(TradeConfig tradeConfig){
        Object configValueObj = tradeConfig.get(TB_XSD_UPLOAD_SET.getConfigKey());
        if(Objects.nonNull(configValueObj)){
            String configValue = (String) configValueObj;
            return Integer.parseInt(configValue);
        }
        return 0;
    }
}

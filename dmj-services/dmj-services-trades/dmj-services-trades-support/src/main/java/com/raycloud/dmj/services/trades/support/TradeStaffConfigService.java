package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.dao.trade.TradeStaffConfigDao;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeStaffConfig;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeStaffConfigService;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 交易子账号配置服务类
 *
 * @Description:TODO
 * @author:zhangzhaowu
 * @time:Sep 16, 2019 11:10:33 AM
 */
@Service
public class TradeStaffConfigService implements ITradeStaffConfigService {

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private TradeStaffConfigDao tradeStaffConfigDao;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Override
    @CacheEvict(value = "defaultCache#600", key = "'trade_staff_config_' + #staff.companyId + '_' + #staff.id")
    public TradeStaffConfig save(Staff staff, TradeStaffConfig tradeStaffConfig) {
        TradeStaffConfig oldConfig = get(staff);
        tradeStaffConfigDao.update(staff, tradeStaffConfig);
        saveOpLog(staff, tradeStaffConfig, oldConfig);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("保存trade用户配置，原配置：%s，新配置：%s", JSON.toJSONString(oldConfig),
                    JSON.toJSONString(tradeStaffConfig))));
        }
        return tradeStaffConfigDao.get(staff);
    }


    @Override
    @Cacheable(value = "defaultCache#600", key = "'trade_staff_config_' + #staff.companyId + '_' + #staff.id")
    public TradeStaffConfig get(Staff staff) {
        TradeStaffConfig tradeStaffConfig = tradeStaffConfigDao.get(staff);
        return tradeStaffConfig == null ? insert(staff, new TradeStaffConfig()) : initOpenForceTradePack(staff, tradeStaffConfig);
    }

    private TradeStaffConfig insert(Staff staff, TradeStaffConfig tradeStaffConfig) {
        tradeStaffConfigDao.insert(staff, initOpenForceTradePack(staff, tradeStaffConfig));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("新增trade用户配置：%s", JSON.toJSONString(tradeStaffConfig))));
        }
        return tradeStaffConfigDao.get(staff);
    }

    private TradeStaffConfig initOpenForceTradePack(Staff staff, TradeStaffConfig newConfig) {
        if (newConfig.getOpenForceTradePack() == null) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            Integer newOpenForceTradePack = tradeConfig.getOpenForceTradePack();
            if (newOpenForceTradePack == null) {
                newOpenForceTradePack = 0;
            }
            newConfig.setOpenForceTradePack(newOpenForceTradePack);
        }
        return newConfig;
    }

    private void saveOpLog(Staff staff, TradeStaffConfig newConfig, TradeStaffConfig oldConfig) {
        StringBuilder log = new StringBuilder();
        ConfigLogUtils.addLogContent(log, "强制包装验货开关", getSwitchDesc(newConfig.getOpenForceTradePack()), getSwitchDesc(oldConfig.getOpenForceTradePack()));
        ConfigLogUtils.addLogContent(log, "开启备货区优先拣选", getSwitchDesc(newConfig.getOpenBackRegionPickFirst()), getSwitchDesc(oldConfig.getOpenBackRegionPickFirst()));
        ConfigLogUtils.addLogContent(log, "单个sku生成一个波次", getSwitchDesc(newConfig.getCheckedWaveSameSku()), getSwitchDesc(oldConfig.getCheckedWaveSameSku()));
        ConfigLogUtils.addLogContent(log, "波次分组，每个波次最大订单数", newConfig.getCheckedWaveMaxNumPer(), oldConfig.getCheckedWaveMaxNumPer());
        ConfigLogUtils.addLogContent(log, "相同快递生成同一个波次", newConfig.getExpressEq(), oldConfig.getExpressEq());
        ConfigLogUtils.addLogContent(log, "按套件本身验货", newConfig.getOpenSuitselfPack(), oldConfig.getOpenSuitselfPack());
        ConfigLogUtils.addLogContent(log, "相同快递公司名称生成同一个波次", newConfig.getLogisticsCompanyEq(), oldConfig.getLogisticsCompanyEq());
        ConfigLogUtils.addLogContent(log, "包装验货配置，箱规码扫描", newConfig.getOpenBoxCodeScan(), oldConfig.getOpenBoxCodeScan());

        if (log.length() > 0) {
            recordOpLog(staff, log.toString());
        }
    }

    private void recordOpLog(Staff staff, String content) {
        OpLog opLog = new OpLog();
        opLog.setCompanyId(staff.getCompanyId());
        opLog.setStaffId(staff.getId());
        opLog.setAccountName(staff.getAccountName());
        opLog.setStaffName(staff.getName());
        opLog.setDomain(Domain.TRADE);
        opLog.setAction("tradeStaffSave");
        opLog.setKey(staff.getCompanyId() + "");
        opLog.setContent(content);
        opLog.setCreated(new Date());
        opLogService.record(staff, opLog);
    }

    private String getSwitchDesc(Integer switchkey) {
        if (switchkey == null) {
            return null;
        }
        return switchkey == 0 ? "关闭" : "开启";
    }
}

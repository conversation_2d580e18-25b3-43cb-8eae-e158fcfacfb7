package com.raycloud.dmj.services.trades.support.utils;

import com.raycloud.dmj.domain.diamond.ConfigHolder;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-10
 */
public class TradeWeighUtils {

    /**
     * <pre>
     * jd快递单号准确逻辑是 京东运单号-x-y-' 其中 x:序号 y:总包裹号,
     * 如JDVC25243947695-1-2-  代表 2个包裹中的第1个包裹,会走一单多包(子母单)的逻辑
     * 但对于-1-1-结尾的数据,实际就是一个包裹,不会走一单多包(子母单)的逻辑,因此这里要还原为原始的运单号
     * https://jos.jd.com/faqdetail?listId=117&itemId=893
     * </pre>
     */
    public static String restoreJdOutSid(Long companyId,String outSid){
        if (StringUtils.isBlank(outSid)) {
            return outSid;
        }
        if (!ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isJdOutSidCompanyId(companyId)) {
            return outSid;
        }
        if (outSid.startsWith("JD") && outSid.endsWith("-1-1-")) {
            return outSid.substring(0,outSid.indexOf("-"));
        }
        return outSid;
    }

    /**
     * 对于JD运单号 打印会将 -1-n- 结尾的运单对应主订单的包裹
     * @param outSid
     * @return
     */
    public static String getJdMainOutSid(Long companyId,String outSid){
        if (StringUtils.isBlank(outSid)) {
            return outSid;
        }
        if (!ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isJdOutSidCompanyId(companyId)) {
            return outSid;
        }
        if (outSid.startsWith("JD") && outSid.endsWith("-")) {
            if ((outSid.contains("-1-") && !outSid.endsWith("-1-")) || outSid.endsWith("-1-1-")) {
                return outSid.substring(0,outSid.indexOf("-"));
            }
        }
        return outSid;
    }
}


package com.raycloud.dmj.services.loadtest;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * user: hj
 * time: 2019/10/17 14:15
 * desc:
 */
@Service
public class JDPConfigService {

    private static Map<Long, String> companyId_Token = new HashMap<>();
    private static Map<Long, Long> companyId_TaobaoId = new HashMap<>();


    static {
        companyId_Token.put(14050L, "304949486563840");
        companyId_Token.put(14513L, "305457044992512");
        companyId_Token.put(14147L, "305798276725248");
        companyId_Token.put(14151L, "305798299937280");
        companyId_Token.put(14154L, "305798312241664");
        companyId_Token.put(14155L, "305798326004224");
        companyId_Token.put(14515L, "305798339049984");
        companyId_Token.put(14516L, "305798351950336");
        companyId_Token.put(14517L, "305798365110784");

        companyId_TaobaoId.put(14050L, 544323876L);
        companyId_TaobaoId.put(14147L, 0L);
        companyId_TaobaoId.put(14151L, 2204171709492L);
        companyId_TaobaoId.put(14154L, 3962312853L);
        companyId_TaobaoId.put(14155L, 1044518778L);
        companyId_TaobaoId.put(14513L, 4060169958L);
        companyId_TaobaoId.put(14515L, 4049157826L);
        companyId_TaobaoId.put(14516L, 4175279022L);
        companyId_TaobaoId.put(14517L, 2201217110418L);
    }

    public String getAccessToken(Long companyId){

        return companyId_Token.get(companyId);
    }

    public Long getTaobaoId(Long companyId){

        return companyId_TaobaoId.get(companyId);
    }
}

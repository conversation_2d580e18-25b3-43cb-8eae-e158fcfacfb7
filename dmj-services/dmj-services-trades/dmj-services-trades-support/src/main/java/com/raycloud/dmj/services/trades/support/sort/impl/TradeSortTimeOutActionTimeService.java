package com.raycloud.dmj.services.trades.support.sort.impl;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.sort.SortContext;
import com.raycloud.dmj.domain.sort.enums.SortTypeEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trades.support.sort.ITradeSortService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther mengfanguang
 * @Date 2023/11/2
 * @desc 承诺发货时间优先
 */
@Service
public class TradeSortTimeOutActionTimeService implements ITradeSortService {
    @Override
    public SortTypeEnum getSortTypeEnum() {
        return SortTypeEnum.AUDIT_OUT_ACTION_TIME;
    }

    @Override
    public void sort(Staff staff, SortContext sortContext) {
        List<List<Trade>> sortedTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(sortContext.getNeedSortTrades())) {
            return;
        }
        for (List<Trade> subTrades : sortContext.getNeedSortTrades()) {
            //按承诺时间升序
            List<Trade> timeOutActionTimeSorted = subTrades.stream().sorted(Comparator.comparing(Trade::getTimeoutActionTime)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(timeOutActionTimeSorted)) {
                continue;
            }
            sortedTrades.add(timeOutActionTimeSorted);
        }
        sortContext.setNeedSortTrades(sortedTrades);
    }
}
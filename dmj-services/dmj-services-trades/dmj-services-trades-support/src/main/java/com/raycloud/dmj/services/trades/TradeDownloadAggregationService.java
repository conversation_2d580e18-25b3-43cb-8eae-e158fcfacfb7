package com.raycloud.dmj.services.trades;


import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.enums.UserSyncTypeEnum;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformTradeAccess;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.erp.orderpool.v2.base.api.server.IOrderPoolTradeDownloadDubbo;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 查询平台订单入口
 *
 * <AUTHOR>
 * @date 2022-4-19
 */
@Service
public class TradeDownloadAggregationService {

//    @Resource
//    private IOPTradeDownloadDubbo opTradeDownloadDubbo;

    @Resource
    private IOrderPoolTradeDownloadDubbo orderPoolTradeDownloadDubbo;

    @Resource
    private PlatformManagement platformManagement;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    /**
     * 查询平台订单或者订单池订单数据
     * 0: 多平台拉单
     * 1: 订单池V1  已下线  如有调v2接口
     * 2: 订单池V2
     *
     * @param user 用户信息
     * @param tid  平台订单号
     * @return
     */
    public Trade downloadTrade(User user, String tid) {
        Trade trade = null;
        if (!ObjectUtils.isEmpty(user) && !ObjectUtils.isEmpty(user.getNewSync())) {
            if (UserSyncTypeEnum.ORDER_POOL_V2.getType().equals(user.getNewSync())) {
                trade = orderPoolTradeDownloadDubbo.downloadTrade(user, tid);
            } else {//1 == user.getNewSync() 改为走platform 去调订单池dubbo
                IPlatformTradeAccess access = platformManagement.getAccess(user.getSource(), IPlatformTradeAccess.class);
                if (!ObjectUtils.isEmpty(access)) {
                    trade = access.downloadTrade(user, tid);
                }
            }
        }
        return trade;
    }
}

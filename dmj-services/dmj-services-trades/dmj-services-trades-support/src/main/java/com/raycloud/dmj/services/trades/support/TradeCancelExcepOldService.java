package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.*;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.except.*;
import com.raycloud.dmj.business.nonconsign.TradeNonConsignBusiness;
import com.raycloud.dmj.business.operate.HaltBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.TradeDelayProcessRecordDao;
import com.raycloud.dmj.dms.domain.dto.TradeFxExceptSyncEcDto;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.fx.Constants;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.ec.fx.FxTradeDownloadNotifyListener;
import com.raycloud.dmj.services.item.IItemMatchService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.utils.TradeExceptBusinessUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.common.TaobaoAccessException;
import com.raycloud.ec.api.*;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.zookeeper.Op;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.*;

/**
 * 订单取消异常业务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-03-01 16:09
 */
@Service
@Deprecated
public class TradeCancelExcepOldService extends TradeBatchExecutor implements ITradeCancelExcepOldService{

    /**
     * 允许取消平拍更换商品异常的系统状态
     */
    private static final Set<String> ALLOW_CANCEL_PLATFORM_CHANGE_ITEM_EXCEPTION_STATUS_SET = Stream.of(Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_WAIT_AUDIT).collect(Collectors.toSet());


    @Resource
    IEventCenter eventCenter;

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    IItemMatchService itemMatchService;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    ILockService lockService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    FxTradeDownloadNotifyListener fxTradeDownloadNotifyListener;

    @Resource
    IStaffService staffService;

    @Resource
    TradeParty3Business tradeParty3Business;

    @Resource
    ITradeImportService tradeImportService;

    @Resource
    ITradeDownloadService tradeDownloadService;
    @Resource
    ITradeTagService tradeTagService;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;

    @Resource
    HaltBusiness haltBusiness;

    @Resource
    TradeDelayProcessRecordDao tradeDelayProcessRecordDao;

    @Resource
    ITradeAuditService tradeAuditService;

    @Resource
    ITradeMergeService tradeMergeService;

    @Resource
    AuditPoisonBusiness auditPoisonBusiness;

    @Resource
    TradeNonConsignBusiness tradeNonConsignBusiness;
    @Resource
    private TradeExceptCancelBusiness tradeExceptCancelBusiness;
    @Resource
    private TradeExceptCancelThreadPoolBusiness tradeExceptCancelThreadPoolBusiness;

    @Resource
    private ISysTradeService sysTradeService;
    @Override
    public void cancelExceptByEvent(Staff staff,  ExceptHandlerDto exceptHandlerDto){

        TradeControllerParams params =exceptHandlerDto.getQueryParams();
        List<String> systemExceptions = exceptHandlerDto.getSystems();
        List<Long> customExceptions = exceptHandlerDto.getCustoms();
        String eventName = exceptHandlerDto.getEventName();
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("接收到事件%s", eventName)));
        long start = System.currentTimeMillis();
        TradeBatchRequest request = new TradeBatchRequest();
        try {
            if (TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
                ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_CANCEL_EXCEP).setProgress(1);
                progressData.setAtomicSucNum(new AtomicLong(0));
                request.setProgress(progressData);
                request.setProgressEnum(ProgressEnum.PROGRESS_CANCEL_EXCEP);
                  tradeExceptCancelThreadPoolBusiness.cancelExcept(staff, params, systemExceptions, customExceptions, progressData);
            } else {
                request.setProgressEnum(ProgressEnum.PROGRESS_CANCEL_EXCEP);
                request.setParams(params);
                request.setSystemExceptions(systemExceptions);
                request.setCustomExceptions(customExceptions);
                request.setIsCancelSmallRefund(exceptHandlerDto.getIsCancelSmallRefund());
                execute(staff, request);
            }

        } catch (Throwable e) {
            Logs.error(LogHelper.buildLog(staff, "取消异常出错"), e);
        } finally {
            progressService.updateProgressComplete(staff, request.getProgressEnum(), request.getProgress());
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("事件[%s]处理结束,耗时:%sms", eventName, (System.currentTimeMillis() - start))));
        }
    }


    @Override
    protected int getTouchPoolSize() {
        return Integer.MAX_VALUE;
    }

    @Override
    protected <R extends TradeBatchRequest> Object[] beforeExecute(Staff staff, R request) {
        return null;
    }

    @Override
    protected <R extends TradeBatchRequest> void execute(Staff staff, R request, List<Trade> trades, Object... args) {
        ExceptHandlerDto exceptHandlerDto=new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(true);
        exceptHandlerDto.setSids(TradeUtils.toSids(trades));
        exceptHandlerDto.setCustoms( request.getCustomExceptions());
        exceptHandlerDto.setSystems( request.getSystemExceptions());
        exceptHandlerDto.setIsCancelSmallRefund(request.getIsCancelSmallRefund());
        List<Trade> update = cancelExcep(staff, exceptHandlerDto);
        request.getProgress().setSucNum(request.getProgress().getSucNum() + update.size());
        request.getProgress().setCountCurrent(request.getProgress().getCountCurrent() + trades.size());
    }


    @Override
    public List<Trade> cancelExcep(Staff staff, Long... sids) {
        return cancelExcep(staff, true, sids);
    }

    @Override
    public List<Trade> cancelExcep(Staff staff, List<String> systems, List<Long> customs, Long... sids) {
        ExceptHandlerDto exceptHandlerDto=new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(true);
        exceptHandlerDto.setSids(sids);
        exceptHandlerDto.setCustoms(customs);
        exceptHandlerDto.setSystems(systems);
        return cancelExcep(staff,exceptHandlerDto);
    }

    @Override
    public List<Trade> cancelExcep(Staff staff, boolean recordTrace, Long... sids) {
        ExceptHandlerDto exceptHandlerDto=new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(recordTrace);
        exceptHandlerDto.setSids(sids);
        return cancelExcep(staff,exceptHandlerDto);
    }

    @Override
    public List<Trade> cancelExcep(Staff staff,ExceptHandlerDto exceptHandlerDto) {
        boolean recordTrace = exceptHandlerDto.isRecordTrace();
        Integer isCancelSmallRefund = exceptHandlerDto.getIsCancelSmallRefund();
        List<String> systems = exceptHandlerDto.getSystems();
        List<Long> customs = exceptHandlerDto.getCustoms();
        Long[] sids = exceptHandlerDto.getSids();
        if (TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            ExceptHandleContext exceptHandleContext = new ExceptHandleContext();
            exceptHandleContext.setRecordTrace(recordTrace);
            exceptHandleContext.setCustomExceptions(customs);
            exceptHandleContext.setSystemExceptions(systems);
            exceptHandleContext.setIsCancelSmallRefund(isCancelSmallRefund);
            exceptHandleContext.setTradeFxExcepSync(exceptHandlerDto.isTradeFxExcepSync());
            return tradeExceptCancelThreadPoolBusiness.doCancelExcep(staff, exceptHandleContext, Arrays.stream(sids).collect(Collectors.toList()));
        }
        ExcepData excepData = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询订单
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            Map<Long, Trade> tradeMap = new HashMap<>();
            List<Long> errorSids = new ArrayList<>();
            Map<User,List<Trade>> downloadTradeMap=new HashMap<>();
            for (Trade trade : originTrades) {
                if (!Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS) && !TradeUtils.getExceptionRemark(staff,trade).isEmpty() &&
                        !trade.isDangkouTrade() && !trade.isOutstock()) {//缺少部分信息,需要指定下载
                    User user = staff.getUserByUserId(trade.getUserId());
                    if(user==null){
                        continue;
                    }
                    downloadTradeMap.computeIfAbsent(user, k -> new ArrayList<>()).add(trade);
                    errorSids.add(trade.getSid());
                } else {
                    tradeMap.put(trade.getSid(), trade);
                }
            }
            if(MapUtils.isNotEmpty(downloadTradeMap)){
                Set<Map.Entry<User, List<Trade>>> entries = downloadTradeMap.entrySet();
                for(Map.Entry<User, List<Trade>> entry:entries){
                    List<Trade> trades = entry.getValue();
                    User user = entry.getKey();
                    tradeImportService.syncTradesByTid(user,true,TradeUtils.toTids(trades));
                }
            }
            if (!errorSids.isEmpty()) {//重新查询指定下载之后的订单
                originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, errorSids.toArray(new Long[0]));
                for (Trade trade : originTrades) {
                    tradeMap.put(trade.getSid(), trade);
                }
                originTrades = new ArrayList<>(tradeMap.values());
            }
            Assert.isTrue(!originTrades.isEmpty(), "取消异常的时候找不到订单");
            return cancelExcep(staff, originTrades, exceptHandlerDto);
        });
        if (CollectionUtils.isNotEmpty(excepData.needCanceledSids)) {
            sysTradeService.cancelTrades(staff, excepData.needCanceledSids.toArray(new Long[0]));
        }
        if (recordTrace) {
            tradeTraceBusiness.asyncTrace(staff, excepData.getTradeTraces(), OpEnum.EXCEPTION_CANCEL);
        }
        return excepData.handleTrades;
    }

    @Override
    public List<Trade> cancelException(final Staff staff, final List<String> systems, List<TradeExt> tradeExtList, final Long... sids) {
        if (TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            ExceptHandleContext exceptHandleContext = new ExceptHandleContext();
            exceptHandleContext.setRecordTrace(true);
            exceptHandleContext.setSystemExceptions(systems);
            exceptHandleContext.setTradeExtList(tradeExtList);
            return tradeExceptCancelThreadPoolBusiness.doCancelExcep(staff, exceptHandleContext, Arrays.stream(sids).collect(Collectors.toList()));
        }
        ExcepData excepData = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询订单
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            Map<Long, Trade> tradeMap = new HashMap<>();
            List<Long> errorSids = new ArrayList<>();
            for (Trade trade : originTrades) {
                if (!TradeUtils.getExceptionRemark(staff,trade).isEmpty()) {//缺少部分信息,需要指定下载
                    User user = staff.getUserByUserId(trade.getUserId());
                    tradeImportService.syncTradesByTid(user, trade.getTid());
                    errorSids.add(trade.getSid());
                } else {
                    tradeMap.put(trade.getSid(), trade);
                }
            }
            if (!errorSids.isEmpty()) {//重新查询指定下载之后的订单
                originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, errorSids.toArray(new Long[0]));
                for (Trade trade : originTrades) {
                    tradeMap.put(trade.getSid(), trade);
                }
                originTrades = new ArrayList<>(tradeMap.values());
            }
            Assert.isTrue(originTrades.size() > 0, "取消异常的时候找不到订单");
            ExceptHandlerDto exceptHandlerDto = new ExceptHandlerDto();
            exceptHandlerDto.setSystems(systems);
            return cancelExcep(staff, originTrades, exceptHandlerDto);
        });
        Map<Long, TradeExt> tradeExtMap = tradeExtList.stream().collect(Collectors.toMap(TradeExt::getSid, tradeExt -> tradeExt, (t, t2) -> t2));

        ZoneId zoneId = ZoneId.of("UTC+8");

        excepData.getUpdateTrades().forEach(trade -> {
            TradeExt tradeExt = tradeExtMap.get(trade.getSid());
            if (Objects.nonNull(tradeExt)) {
                trade.getOperations().remove(OpEnum.EXCEPTION_CANCEL);
                trade.getOperations().put(OpEnum.EXCEPTION_AUTO_HOOK_CANCEL, String.format("自动取消挂起异常：指定挂起时长%s小时已到达，自动取消挂起异常", tradeExt.getAutoUnHook()));
                Date expireDate = Date.from(LocalDate.of(2000, 1, 1).atStartOfDay(zoneId).toInstant());
                tradeExt.setAutoUnHookTime(expireDate);
            }
        });
        tradeTraceBusiness.asyncTrace(staff, excepData.getUpdateTrades(), OpEnum.EXCEPTION_AUTO_HOOK_CANCEL);
        return excepData.handleTrades;
    }

    @Override
    @Transactional
    public ExcepData cancelExcep(Staff staff, List<Trade> originTrades) {
        return cancelExcep(staff, originTrades,new ExceptHandlerDto());
    }

    @Override
    @Transactional
    public ExcepData cancelExcep(Staff staff, List<Trade> originTrades,ExceptHandlerDto exceptHandlerDto) {
        List<Long> customExceptions = Optional.ofNullable(exceptHandlerDto).orElse(new ExceptHandlerDto()).getCustoms();
        List<String> systemExceptions =  Optional.ofNullable(exceptHandlerDto).orElse(new ExceptHandlerDto()).getSystems();
        boolean tradeFxExcepSync = Optional.ofNullable(exceptHandlerDto).orElse(new ExceptHandlerDto()).isTradeFxExcepSync();
        if (TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            return tradeExceptCancelBusiness.cancelExceptNoLock(staff, originTrades, exceptHandlerDto);
        }
        ExcepData excepData = new ExcepData();
        Integer isCancelSmallRefund = Optional.ofNullable(exceptHandlerDto).orElse(new ExceptHandlerDto()).getIsCancelSmallRefund();
        excepData.setIsCancelSmallRefund(isCancelSmallRefund);
        excepData.cacelAllException = CollectionUtils.isEmpty(systemExceptions) && CollectionUtils.isEmpty(customExceptions);
        // 获取所有的自定义异常
        List<TradeTag> tradeTags = tradeTagService.list(staff, 1);
        Map<Long,String> exceptNameMap=new HashMap<>();
        for(TradeTag tradeTag:tradeTags){
            exceptNameMap.put(tradeTag.getId(),tradeTag.getTagName());
        }
        if(!exceptHandlerDto.isTradeFxExcepSync()){
            originTrades.forEach(t-> t.addOpV(OpVEnum.TRADE_FX_EXCEPT_SYNC));
        }

        //过滤出需要更新的订单及子订单
        checkCancelExcep(staff, originTrades, excepData, systemExceptions, customExceptions,exceptNameMap);
        if (excepData.unMatchedOrders.size() > 0) {//未匹配系统商品的子订单需要匹配系统商品
            for (Map.Entry<User, List<Order>> entry : excepData.unMatchedOrders.entrySet()) {
                //匹配系统商品之后需要申请库存
                List<Order> matchedList = itemMatchService.matchSysItem(entry.getKey(), entry.getValue());
                if (matchedList.size() > 0) {
                    excepData.stockOrders.addAll(matchedList);
                    excepData.matchedOrders.putAll(OrderUtils.toMap(matchedList));
                }
            }
        }
        if (excepData.stockOrders.size() > 0) {
            List<Order> collect = excepData.stockOrders.stream().filter(order -> !Objects.equals(order.getIsCancel(), 1)).collect(Collectors.toList());
            orderStockService.checkOrderStock(staff, collect);
        }

        createUpdateTrades(staff, excepData);
        //更新数据库
        List<Trade> updateTrades = excepData.getUpdateTrades();
        tradeUpdateService.updateTrades(staff, updateTrades, excepData.getUpdateOrders(), null, excepData.toInsertOrders);
        //  tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.MANUAL.getAutoSign());
        orderModifyLogBusiness.addLog(staff,excepData.orderModifyLogs);
        //如果是刚刚匹配到商品的订单，肯定不是已审核，所以不用处理 excepData.toInsertOrders
        tradeParty3Business.filterCancelExcep(staff, TradeUtils.toSids(updateTrades));

        updateTradeDelayProcessStatus(staff, excepData);

        tradeMergeService.mergeRedo(staff, excepData.sourceMergeSidsMap);

        logAfterCancelExcep(staff, excepData);
        // 取消快递异常
        if (CollectionUtils.isNotEmpty(excepData.stockRecordNeedChangeSids)) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("sids=%s,取消快递异常或者锁定记录需要变动,发送库存处理消息 !", excepData.stockRecordNeedChangeSids)));
            eventCenter.fireEvent(this, new EventInfo(TradeConfigStockBusiness.EVENT_NAME).setArgs(new Object[]{staff, excepData.stockRecordNeedChangeSids}), null);
        }
        if(CollectionUtils.isNotEmpty(excepData.gxTrades)){
            List<Long> gxSids = excepData.gxTrades.stream().filter(t->t.hasOpV(OpVEnum.TRADE_CANCEL_PART_REFUND_EXCEPT)).map(Trade::getSid).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(gxSids)){
                Logs.debug(new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("sids=%s,供销取消部分退款异常" + gxSids).toString());
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_CANCEL_PART_REFUND).setArgs(new Object[]{staff, gxSids}), null);
            }
        }
        if (CollectionUtils.isNotEmpty(excepData.fxStopDeliverSids)&&tradeFxExcepSync) {
            Logs.debug(new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append(String.format("sids=%s,分销取消暂停发货异常", excepData.fxStopDeliverSids)).toString());
            TradeFxExceptSyncEcDto tradeFxExceptSyncEcDto = new TradeFxExceptSyncEcDto();
            tradeFxExceptSyncEcDto.setSids(new ArrayList<>(excepData.fxStopDeliverSids));
            tradeFxExceptSyncEcDto.setOpType(2);
            eventCenter.fireEvent(this, new EventInfo("trade.fx.excep.sync").setArgs(new Object[]{staff, tradeFxExceptSyncEcDto}), null);
        }
        return excepData;
    }


    @Override
    public List<Trade> cancelPlatformExcept(final Staff staff, boolean recordTrace, final Long... sids) {
        ExcepData excepData = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询订单
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, sids);
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            TradeStatusUtils.validSysStatus(originTrades, tradeConfig, new String[]{Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT}, "取消异常");
            Assert.isTrue(originTrades.size() > 0, "取消异常的时候找不到订单");
            Map<Long, List<Trade>> userTradeMap = TradeUtils.toMapByUserId(originTrades);
            Set<Map.Entry<Long, List<Trade>>> entries = userTradeMap.entrySet();
            for (Map.Entry<Long, List<Trade>> entry : entries) {
                Long userId = entry.getKey();
                User user = staff.getUserByUserId(userId);
                if (user == null || Objects.equals(userId, Constants.FxDefaultUserId)) {
                    Logs.debug(LogHelper.buildLog(staff, String.format("user参数为空 UserId = %s ", userId)));
                    continue;
                }
                List<Trade> trades = entry.getValue();
                tradeImportService.syncTradesByTid(user, true, TradeUtils.toTids(trades));
            }
            originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
            //取消待审核合单
            cancelMergeAddressExcept(staff, originTrades.stream().filter(trade -> TradeStatusUtils.isWaitAudit(trade.getSysStatus())).collect(Collectors.toList()));//处理待审核合单异常，取消合单，取消异常
            originTrades = originTrades.stream().filter(trade -> !TradeStatusUtils.isWaitAudit(trade.getSysStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originTrades)) {
                Logs.ifDebug("取消异常的订单状态都是待审核状态，不需要执行下面的取消异常操作");
                return new ExcepData();
            }
            return cancelItemAndAddressExcep(staff, originTrades);
        });
        if (CollectionUtils.isNotEmpty(excepData.addressExceptTrades)) {
            eventCenter.fireEvent(this, new EventInfo("trade.cancelAndCoverExcep").setArgs(new Object[]{staff, 1}), excepData.addressExceptTrades);
        }

        if (CollectionUtils.isNotEmpty(excepData.unAuditTrades)) {
            //反审核
            tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_PLAT_CHANG_ITEM, TradeUtils.toSids(excepData.unAuditTrades));
        }
        return excepData.originTrades;
    }

    /**
     * https://gykj.yuque.com/entavv/xb9xi5/cgxan1
     *
     * @param staff
     * @param originTrades
     */
    private void cancelMergeAddressExcept(Staff staff, List<Trade> originTrades) {
        try {
            if (staff == null || originTrades == null) {
                return;
            }
            //交易配置
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            //取消合单集合
            Map<Long, Set<Long>> cancelAddressChangeMergeSids = new ConcurrentHashMap<>();
            //取消异常集合
            List<Trade> cancelAddressChangeMergeExceptTrades = new ArrayList<>();
            //取消审核集合
            List<Trade> undoAuditAddressChangeMergeTrades = new ArrayList<>();

            for (Trade origin : originTrades) {
                //非合单
                if (!TradeUtils.isMerge(origin)) {
                    continue;
                }
                if (TradeStatusUtils.isWaitAudit(origin.getSysStatus())) {
                    //待审核自动取消合单
                    continue;
                }
                //取消异常
                cancelAddressChangeMergeExceptTrades.add(origin);
                //取消合单
                cancelAddressChangeMergeSids.computeIfAbsent(origin.getMergeSid(), k -> new HashSet<Long>()).add(origin.getSid());
                //已审核未开启自动反审核,需要反审核
                if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(origin.getSysStatus()) || TradeStatusUtils.isWaitFinanceAudit(origin.getSysStatus())) {
                    if (!(tradeConfig.isCancelAuditIfAddressChanged() && TradeStatusUtils.ifAutoCancelAudit(origin, tradeConfig))) {
                        undoAuditAddressChangeMergeTrades.add(origin);
                    }
                }
            }
            //取消合单
            tradeMergeService.mergeUndoAutoPart(staff, cancelAddressChangeMergeSids, TradeMergeEnum.MERGE_UNDO_PART_AUTO);
            //反审核
            if (CollectionUtils.isNotEmpty(undoAuditAddressChangeMergeTrades)) {
                tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_PLAT_CHANG_ADDRESS, TradeUtils.toSids(undoAuditAddressChangeMergeTrades));
            }
            if (CollectionUtils.isNotEmpty(cancelAddressChangeMergeExceptTrades)) {
                //通过待审核取消异常
                Map<Long, List<Trade>> downloadTrades = TradeUtils.toMapByUserId(cancelAddressChangeMergeExceptTrades);
                for (Long userId : downloadTrades.keySet()) {
                    List<Trade> trades = downloadTrades.get(userId);
                    Map<String, Trade> tid2Trade = TradeUtils.toMapByTid(trades);
                    TradeImportResult result = tradeImportService.syncTradesByTid(staff.getUserByUserId(userId), true, tid2Trade.keySet().toArray(new String[0]));
                    if (result.getError()) {
                        throw new TaobaoAccessException(result.getErrorMsg());
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(LogHelper.buildLog(staff, "取消合单地址异常失败"), ex);
        }
    }


    private Trade createUnauditTrade(Staff staff,Trade originTrade) {
        Trade update = TradeBuilderUtils.builderUpdateTrade(originTrade);
        update.setSid(originTrade.getSid());
        update.setCompanyId(originTrade.getCompanyId());
        update.setEnableStatus(originTrade.getEnableStatus());
        update.setDeliverPrintTime(TradeTimeUtils.INIT_DATE);
        update.setExpressPrintTime(TradeTimeUtils.INIT_DATE);
        update.setAssemblyPrintTime(TradeTimeUtils.INIT_DATE);
        update.setIsExcep(0);
        update.setIsPackage(0);
        update.setIsWeigh(0);
        update.setIsAutoAudit(3);
        update.setAuditTime(new Date());
        update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
        return update;
    }

    private List<Order> createUnauditOrder(Staff staff,List<Order> orders) {
        List<Order> originOrders = OrderUtils.toFullOrderList(orders, false);
        List<Order> updateOrders = new ArrayList<Order>();
        for (Order originOrder : originOrders) {
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {//平台子订单, 对于部分发货交易, 取消审核后, 已发货已完成已关闭的子订单系统状态仍然为原状态
                continue;
            }
            Order update = OrderBuilderUtils.builderUpdateOrder(originOrder);
            update.setId(originOrder.getId());
            update.setSid(originOrder.getSid());
            // update.setItemChanged(0);
            OrderExceptUtils.updateExceptOrder(staff,update,ExceptEnum.ITEM_CHANGED,0L);
            update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
            updateOrders.add(update);
        }
        return updateOrders;
    }

    private Trade createAuditTrade(Staff staff,Trade originTrade, Set<Long> mergeSids,Set<Long> addressNormalSids) {
        Trade update = TradeBuilderUtils.builderUpdateTrade(originTrade);
        update.setSid(originTrade.getSid());
        update.setIsAutoAudit(originTrade.getIsAutoAudit());
        update.setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT);
        TradeExceptUtils.updateExcept(staff,update,ExceptEnum.ADDRESS_CHANGED,0L);
        if (mergeSids.contains(originTrade.getSid()) && !addressNormalSids.contains(originTrade.getSid())) {
            update.setIsExcep(1);
            TradeExceptUtils.updateExcept(staff,update,ExceptEnum.ADDRESS_CHANGED,1L);
        }
        return update;
    }

    private List<Order> createAuditOrder(Staff staff,List<Order> orders) {
        List<Order> originOrders = OrderUtils.toFullOrderList(orders, false);
        List<Order> updateOrders = new ArrayList<>();
        for (Order originOrder : originOrders) {
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {//平台子订单, 对于部分发货交易, 取消审核后, 已发货已完成已关闭的子订单系统状态仍然为原状态
                continue;
            }
            Order update = OrderBuilderUtils.builderUpdateOrder(originOrder);
            update.setId(originOrder.getId());
            update.setSid(originOrder.getSid());
            // update.setItemChanged(0);
            OrderExceptUtils.updateExceptOrder(staff,update,ExceptEnum.ITEM_CHANGED,0L);
            update.setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT);
            updateOrders.add(update);
        }
        return updateOrders;
    }


    private ExcepData cancelItemAndAddressExcep(Staff staff, List<Trade> originTrades) {
        ExcepData excepData = new ExcepData();
        List<Trade> allDownloadedTrades = Lists.newArrayList();
        Multimap<String, Order> tid2ItemExcepOrder = ArrayListMultimap.create();

        Set<Long> addrMergeSids = new HashSet<>(), itemExceptSids = new HashSet<>(), itemExceptOrderIds = new HashSet<>();
        List<Trade> isSplitTrades = new ArrayList<>();
        List<Trade> mergeTrades = new ArrayList<>();
        for (Trade originTrade : originTrades) {
            if (TradeUtils.isSplit(originTrade)) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("当前订单有拆单,sid=%s,tid=%s", originTrade.getSid(), originTrade.getTid())));
                //处理拆单
                if (!TradeUtils.isMerge(originTrade)) {
                    isSplitTrades.add(originTrade);
                }
            }
            if (TradeUtils.isMerge(originTrade)) {
                mergeTrades.add(originTrade);
            }
            Assert.isTrue(ALLOW_CANCEL_PLATFORM_CHANGE_ITEM_EXCEPTION_STATUS_SET.contains(originTrade.getSysStatus()), originTrade.getSid() + "的系统状态不是已审核、待审核");

            //是否有订单同步的时候地址变更产生的异常
            if ( TradeExceptUtils.isContainExcept(staff,originTrade,ExceptEnum.ADDRESS_CHANGED)) {
                if (TradeUtils.isMerge(originTrade)) {
                    addrMergeSids.add(originTrade.getMergeSid());
                }  else {
                    excepData.addressExceptTrades.add(originTrade);
                }
            }

        }
        if (addrMergeSids.size() > 0) {
            for (Trade originTrade : originTrades) {
                if (addrMergeSids.contains(originTrade.getMergeSid())) {
                    if (isOpenWms(staff) && itemExceptSids.contains(originTrade.getMergeSid())) {
                        continue;
                    }
                    excepData.addressExceptTrades.add(originTrade);
                }
            }
        }
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("addressExceptTrades size=%s,tid2ItemExcepOrder size=%s,isSplitTrades size=%s", excepData.addressExceptTrades.size(), tid2ItemExcepOrder.size(), isSplitTrades.size())));
        // Assert.isTrue(excepData.addressExceptTrades.size() > 0 || tid2ItemExcepOrder.size() > 0 || isSplitTrades.size() > 0, "选中的订单不是平台修改商品与平台修改地址的异常，请重新选择！");
        if(CollectionUtils.isEmpty(excepData.addressExceptTrades) && tid2ItemExcepOrder.size()==0 && CollectionUtils.isEmpty(isSplitTrades)){
            return excepData;
        }

        // KMERP-148078: 处理平台换商品，取消无需发货标记
        processNonConsign(staff, excepData.itemExceptTrades);

        allDownloadedTrades.addAll(excepData.addressExceptTrades);
        allDownloadedTrades.addAll(excepData.itemExceptTrades);
        List<Trade> updateTrades = Lists.newArrayList();
        List<Order> updateOrders = Lists.newArrayList();
        for (Trade downloadedTrade : allDownloadedTrades) {
            updateTrades.add(createUnauditTrade(staff,downloadedTrade));
            updateOrders.addAll(createUnauditOrder(staff,TradeUtils.getOrders4Trade(downloadedTrade)));
        }
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        //  tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.MANUAL.getAutoSign());
        Map<Long, List<Trade>> splitedTrades = TradeUtils.toMapByUserId(allDownloadedTrades);
        for (Long userId : splitedTrades.keySet()) {
            List<Trade> trades = splitedTrades.get(userId);
            if(Objects.equals(userId,Constants.FxDefaultUserId)){
                syncGxTrade(staff,trades);
                continue;
            }
            Map<String, Trade> tid2Trade = TradeUtils.toMapByTid(trades);
            User user = staff.getUserByUserId(userId);
            user.setStaff(staff);
            TradeImportResult result = tradeImportService.syncTradesByTid(user, true, tid2Trade.keySet().toArray(new String[0]));
            if (result.getError()) {
                throw new TaobaoAccessException(result.getErrorMsg());
            }
        }

        updateTrades.clear();
        updateOrders.clear();
        //做过指定下载必须用最新的数据
        List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, false, TradeUtils.toSids(allDownloadedTrades));
        // 不存在地址异常的
        Set<Long> addressNormalSids = tradeList.stream().filter(trade -> !TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.ADDRESS_CHANGED)).map(Trade::getSid).collect(Collectors.toSet());
        for (Trade downloadedTrade : allDownloadedTrades) {
            updateTrades.add(createAuditTrade(staff,downloadedTrade, addrMergeSids,addressNormalSids));
            updateOrders.addAll(createAuditOrder(staff,TradeUtils.getOrders4Trade(downloadedTrade)));
        }
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        //  tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.MANUAL.getAutoSign());
        //处理合单已审核地址异常订单,取消合单,覆盖地址不标异常
        cancelMergeAddressExcept(staff, mergeTrades);

        //处理拆单，不走订单同步逻辑，直接更新地址
        if (isSplitTrades.size() > 0) {
            List<Trade> updateSplits = cancelSplitAddressException(staff, isSplitTrades);
            excepData.addressExceptTrades.addAll(updateSplits);
            excepData.dataCoveredTrades.addAll(updateSplits);
        }
        excepData.itemExceptOrderIds.addAll(itemExceptOrderIds);
        excepData.dataCoveredTrades.addAll(updateTrades);
        excepData.dataCoveredOrders.addAll(updateOrders);
        excepData.originTrades.addAll(originTrades);
        return excepData;
    }

    /**
     * 同步供销订单
     * @param staff
     * @param trades
     */
    private void syncGxTrade(Staff staff, List<Trade> trades) {
        if(CollectionUtils.isEmpty(trades)){
            return;
        }
        Map<Long, List<Trade>> sourceIdMap = trades.stream()
                .filter(t->Objects.nonNull(t.getSourceId()))
                .collect(Collectors.groupingBy(Trade::getSourceId));
        sourceIdMap.forEach((k,v)->{
            Staff fxStaff = staffService.queryFullByCompanyId(k);
            fxTradeDownloadNotifyListener.handleGxTrade(fxStaff,v.stream().map(trade -> Long.parseLong(trade.getTid())).distinct().toArray(Long[]::new),null);
        });

    }

    //拆单订单取消改地址异常
    private List<Trade> cancelSplitAddressException(Staff staff, List<Trade> originTrades) {
        List<Trade> updateTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(originTrades)) {
            return updateTrades;
        }
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("开始处理拆单地址异常,sid:%s", StringUtils.join(originTrades.stream().map(Trade::getSid).collect(Collectors.toList()), ","))));
        for (Trade trade : originTrades) {
            User user = staff.getUserByUserId(trade.getUserId());
            Trade newTrade = tradeDownloadService.downloadTrade(user, trade.getTid());
            if (newTrade != null) {
                Trade update = initUpdateTrade(staff, user, trade, newTrade);
                if (update != null) {
                    updateTrades.add(update);
                }
            }
        }
        //更新拆单的
        tradeUpdateService.updateTrades(staff, updateTrades);
        //  tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.MANUAL.getAutoSign());
        return updateTrades;
    }

    private Trade initUpdateTrade(Staff staff, User user, Trade origin, Trade newTrade) {
        Trade trade = new TbTrade();
        trade.setSid(origin.getSid());
        trade.setCompanyId(origin.getCompanyId());
        if (!AddressUtils.equalsAddress(staff, newTrade, origin)) {
            AddressUtils.coverAddress(newTrade, trade);
        } else {
            Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("订单处理地址异常失败：订单为空或地址与原来订单相同 userId=%s,sid=%s,tid=%s", user.getId(), origin.getSid(), origin.getTid())));
            return null;
        }
        return trade;
    }

    private boolean isOpenWms(Staff staff) {
        return staff.getConf().isOpenWms();
    }

    private void checkCancelExcep(Staff staff, List<Trade> originTrades, ExcepData excepData, List<Long> customs,Map<Long,String> exceptNameMap) {
        for (Trade originTrade : originTrades) {
            //合单的订单需要额外处理
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, originTrade)) {
                excepData.sourceMergeSidsMap.computeIfAbsent(originTrade.getSource(), k -> new HashSet<>()).add(originTrade.getMergeSid());
            }
            TradeExceptBusinessUtils.checkAllExcept(staff, originTrade, excepData, auditPoisonBusiness);
            //取消自定义异常
            TradeExceptBusinessUtils.cancelCustomExcept(staff, originTrade, excepData, customs, true, exceptNameMap);
            originTrade.setOldSysStatus(originTrade.getSysStatus());
            originTrade.setOldStockStatus(originTrade.getStockStatus());
            //处理order的异常
            cancelOrder(staff, originTrade, excepData);

            excepData.handleTrades.add(originTrade);
        }
    }

    private void checkCancelExcep(Staff staff, List<Trade> originTrades, ExcepData excepData, List<String> systems, List<Long> customs,Map<Long,String> exceptNameMap) {
        if (null == systems) {
            checkCancelExcep(staff, originTrades, excepData, customs,exceptNameMap);
        } else {
            for (Trade originTrade : originTrades) {
                //合单的订单需要额外处理
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, originTrade)) {
                    excepData.sourceMergeSidsMap.computeIfAbsent(originTrade.getSource(), k -> new HashSet<>()).add(originTrade.getMergeSid());
                }
                TradeExceptBusinessUtils.checkSysExcept(staff, originTrade, excepData, systems, auditPoisonBusiness);
                //取消order异常
                cancelOrder(staff, originTrade, excepData, systems);
                //取消自定义异常
                TradeExceptBusinessUtils.cancelCustomExcept(staff, originTrade, excepData, customs,false,exceptNameMap);
                originTrade.setOldSysStatus(originTrade.getSysStatus());
                originTrade.setOldStockStatus(originTrade.getStockStatus());
                // to do 取消财审核异常
                excepData.handleTrades.add(originTrade);
            }
        }
    }

    /**
     * 处理子订单的异常
     */
    private void cancelOrder(Staff staff, Trade originTrade, ExcepData excepData) {
        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
        for (Order originOrder : originOrders) {
            //已发货的订单不处理
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {
                continue;
            }
            if (originOrder.getBelongType() != null && originOrder.getBelongType() == 0) {
                //需要库存校验
                fillStockInfo(originOrder, originTrade);
                if (originOrder.getItemSysId() <= 0) {//商品未匹配
                    cancelUnMatchedOrder(staff, originTrade, originOrder, excepData);
                } else {//校验库存
                    cancelStockOrder(staff, originTrade, originOrder, excepData);
                }
            }
        }
    }

    private void cancelOrder(Staff staff, Trade originTrade, ExcepData excepData, List<String> systems) {
        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
        for (Order originOrder : originOrders) {
            //已发货的订单不处理
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {
                continue;
            }

            if (originOrder.getBelongType() != null && originOrder.getBelongType() == 0) {
                //需要库存校验
                fillStockInfo(originOrder, originTrade);
                if (originOrder.getItemSysId() <= 0) {//商品未匹配
                    cancelUnMatchedOrder(staff, originTrade, originOrder, excepData);
                } else {//校验库存
                    cancelStockOrder(staff, originTrade, originOrder, excepData);
                }
            }
        }
    }


    /**
     * 有未匹配的子订单
     */
    private void cancelUnMatchedOrder(Staff staff, Trade originTrade, Order originOrder, ExcepData excepData) {
        User user = staff.getUserByUserId(originTrade.getUserId());
        if (user != null) {
            excepData.unMatchedOrders.computeIfAbsent(user, u -> new ArrayList<>()).add(originOrder);
            originOrder.setOperable(true);
        }
    }


    /**
     * 有可能需要处理库存异常的子订单
     */
    private void cancelStockOrder(Staff staff, Trade originTrade, Order originOrder, ExcepData excepData) {
        if (staff.openAuditActiveStockRecord()) {
            return;
        }
        excepData.stockOrders.add(originOrder);
        //套件的话需要处理单品
        if (originOrder.getSuits() != null) {
            for (Order son : originOrder.getSuits()) {
                fillStockInfo(son, originTrade);
            }
        }
        originOrder.setOperable(true);
    }

    private void fillStockInfo(Order order, Trade trade) {
        order.setWarehouseId(trade.getWarehouseId());
        order.setUserId(trade.getUserId());
        order.setOldStockStatus(order.getStockStatus());
        order.setOldStockNum(order.getStockNum());
    }

    private void createUpdateTrades(Staff staff, ExcepData excepData) {
        Map<Long, List<Order>> orderListMap = OrderUtils.toMapBySid(excepData.stockOrders);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Trade trade : excepData.handleTrades) {
            List<Order> list = orderListMap.get(trade.getSid());
            if (list != null) {
                for (Order order : list) {
                    if (excepData.matchedOrders.containsKey(order.getId())) {//刚刚匹配系统商品的子订单
                        if (order.getSuits() != null) {//匹配到套件，单品子订单作插入操作
                            excepData.toInsertOrders.addAll(order.getSuits());
                        }
                        excepData.updateOrders.put(order.getId(), order);//套件本身或非套件作全字段更新
                    } else {
                        //套件本身或非套件
                        createUpdateOrder(staff,order, excepData);
                        //套件单品子订单
                        if (order.getSuits() != null) {
                            for (Order son : order.getSuits()) {
                                createUpdateOrder(staff,son, excepData);
                            }
                        }
                    }
                }
            }

            createUpdateTrade(staff, trade, excepData, tradeConfig);
        }
    }

    void createUpdateTrade(Staff staff, Trade originTrade, ExcepData excepData, TradeConfig tradeConfig) {
        int oldInsufficientNum = originTrade.getInsufficientNum();
        double oldInsufficientRate = originTrade.getInsufficientRate();
        TradeStockUtils.resetTradeStockStatus(staff, originTrade, TradeUtils.getOrders4Trade(originTrade), false, tradeConfig);
        boolean insufficientNumChange = originTrade.getInsufficientNum() - oldInsufficientNum != 0;
        boolean insufficientRateChanged = originTrade.getInsufficientRate() - oldInsufficientRate != 0;
        boolean containSuiteChangeExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.SUITE_CHANGE);
        boolean stockStatusChanged = !originTrade.getStockStatus().equals(originTrade.getOldStockStatus());
        if(!insufficientNumChange&&!insufficientRateChanged&&!containSuiteChangeExcept&&!stockStatusChanged){
            // 没变动不需要更新
            return;
        }
        Trade toUpdate = excepData.getUpdateTrade(originTrade);
        if (stockStatusChanged) {//订单库存状态未改变不需要更新
            TradeExceptUtils.setStockStatus(staff,toUpdate,originTrade.getStockStatus());
        }
        if (insufficientNumChange) {
            toUpdate.setInsufficientNum(originTrade.getInsufficientNum());
        }
        if (insufficientRateChanged) {
            toUpdate.setInsufficientRate(originTrade.getInsufficientRate());
        }
        //取消订单上的"套"异常
        if (containSuiteChangeExcept) {
            TradeExceptUtils.updateExcept(staff,originTrade,ExceptEnum.SUITE_CHANGE,0L);
            TradeExceptUtils.syncTradeExcept(staff,toUpdate,originTrade,ExceptEnum.SUITE_CHANGE);
        }
        //重新计算订单异常状态，确定是否需要更新异常状态
        TradeUtils.setTradeExcep(staff,originTrade);
        toUpdate.setIsExcep(originTrade.getIsExcep());
        TradeExceptUtils.syncTradeExcept(staff,toUpdate,originTrade,ExceptEnum.LOST_MSG);
        TradeExceptUtils.syncTradeExcept(staff,toUpdate,originTrade,ExceptEnum.REFUNDING);

    }

    //比较库存状态前后是否发生变化，未发生改变不需要更新，返回null
    void createUpdateOrder(Staff staff,Order originOrder, ExcepData excepData) {
        if (!originOrder.getStockStatus().equals(originOrder.getOldStockStatus())) {
            Order updateOrder = excepData.getUpdateOrder(originOrder);
            OrderExceptUtils.setStockStatus(staff,updateOrder,originOrder.getStockStatus());
        }
        if (originOrder.getStockNum() - originOrder.getOldStockNum() != 0) {
            excepData.getUpdateOrder(originOrder).setStockNum(originOrder.getStockNum());
        }
    }

    private void logAfterCancelExcep(Staff staff, ExcepData excepData) {
        List<Trade> updateTrades = excepData.getUpdateTrades();
        List<String> logbuf = new ArrayList<>();
        for (Trade t : updateTrades) {
            StringBuilder buf = new StringBuilder();
            if (t.getStockStatus() != null) {
                buf.append("stockStatus:").append(t.getOrigin().getOldStockStatus()).append("->").append(t.getStockStatus()).append(", ");
            }
            if (t.getIsExcep() != null) {
                buf.append("isExcep:").append(t.getIsExcep()).append(", ");
            }
            if (t.getSysStatus() != null) {
                buf.append("sysStatus:").append(t.getOrigin().getOldSysStatus()).append("->").append(t.getSysStatus()).append(", ");
            }
            if (TradeExceptUtils.isContainExcept(staff,t,ExceptEnum.HALT)) {
                buf.append("isHalt:1->0");
            }
            if (t.getUnattainable() != null) {
                buf.append("unattainable:1->0");
            }
            if (buf.length() > 0) {
                buf.append(" sid:").append(t.getSid()).append(";");
                logbuf.add(buf.toString());
            }
        }
        if (CollectionUtils.isNotEmpty(logbuf)) {
            Lists.partition(logbuf, 20).stream().forEach(e -> {
                Logs.ifDebug(LogHelper.buildLog(staff, e.toString()));
            });
        }

        List<Order> updateOrders = excepData.getUpdateOrders();
        for (Order o : updateOrders) {
            if (excepData.matchedOrders.containsKey(o.getId())) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s]取消异常:子订单[id=%s]匹配到系统商品[sysItemId=%s,sysSkuId=%s,sysTitle=%s],库存状态[stockStatus=%s,stockNum=%s]", o.getSid(), o.getId(), o.getItemSysId(), o.getSkuSysId(), o.getSysTitle(), o.getStockStatus(), o.getStockNum())));
            } else {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s]取消异常:子订单[id=%s]库存状态变化[stockStatus:%s->%s,stockNum:%s->%s]", o.getOrigin().getSid(), o.getId(), o.getOrigin().getOldStockStatus(), o.getStockStatus(), o.getOrigin().getOldStockNum(), o.getStockNum())));
            }
        }

    }




    /**
     * 取消换货订单的等待退货入仓异常
     */
    @Override
    public List<Trade> cancelReturnWmsExcept(Staff staff, boolean recordTrace, Long... sids) {
        //查询订单
        List<Trade> oldTrade = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
        ExcepData excepData = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            TradeStatusUtils.validSysStatus(originTrades, tradeConfig, new String[]{Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT}, "取消异常");
            Assert.isTrue(originTrades.size() > 0, "取消异常的时候找不到订单");
            ExcepData excepData1 = new ExcepData();
            originTrades.forEach(originTrade -> {
                TradeExceptBusinessUtils.cancelWaitingReturnWmsExecp(staff,originTrade, excepData1);
                excepData1.handleTrades.add(originTrade);
            });
            return excepData1;
        });
        createUpdateTrades(staff, excepData);
        //更新数据库
        List<Trade> updateTrades = excepData.getUpdateTrades();
        tradeUpdateService.updateTrades(staff, updateTrades, excepData.getUpdateOrders(), null, excepData.toInsertOrders);
        // tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.MANUAL.getAutoSign());
        if (recordTrace) {
            eventCenter.fireEvent(this, new EventInfo("trade.cancelExcep").setArgs(new Object[]{staff, oldTrade}), updateTrades);
        }
        return excepData.handleTrades;
    }


    /**
     * 更新挂起订单延迟处理状态
     */
    private void updateTradeDelayProcessStatus(Staff staff, ExcepData excepData) {
        if (CollectionUtils.isNotEmpty(excepData.cancelHaltTradeList)) {
            List<Long> sidList = excepData.cancelHaltTradeList.stream().map(Trade::getSid).collect(Collectors.toList());
            List<TradeDelayProcessRecord> delayProcessRecordList = haltBusiness.queryExistTradeDelayProcessRecord(staff, sidList, TradeConstants.STATUS_EXCEP_HALT);
            delayProcessRecordList.forEach(delayProcessRecord -> {
                delayProcessRecord.setProcessStatus(1);
                delayProcessRecord.setEnableStatus(0);
            });
            if (CollectionUtils.isNotEmpty(delayProcessRecordList)) {
                tradeDelayProcessRecordDao.batchUpdate(staff, delayProcessRecordList);
            }
        }
    }

    private void processNonConsign(Staff staff, List<Trade> trades) {
        List<Order> orders = TradeUtils.getOrders4Trade(trades).stream().filter(o -> Objects.equals(o.getItemChanged(), 1)).collect(Collectors.toList());
        tradeNonConsignBusiness.removeNonConsign(staff, orders);
    }



    @Override
    public int getPageSize(Staff staff, TradeBatchRequest request){
        return 100000;
    }


}

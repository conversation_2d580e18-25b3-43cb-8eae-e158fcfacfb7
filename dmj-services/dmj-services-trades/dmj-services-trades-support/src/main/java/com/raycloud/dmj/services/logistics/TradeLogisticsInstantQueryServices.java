package com.raycloud.dmj.services.logistics;

import com.google.common.collect.Lists;
import com.kdzs.wly.enums.ExpressPacketStatusEnum;
import com.raycloud.bizlogger.Logger;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.logistics.UploadDelayBusiness;
import com.raycloud.dmj.business.logistics.WlyLogisticsTrackingBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.model.QueryTemplateBuilder;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.trades.IConsignRecordService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * TradeLogisticsInstanQueryServices
 * 8001 即时查询接口（增值版） 查询物流
 *
 * <AUTHOR>
 * @Date 2019-08-15
 * @Time 13:40
 */
@Service
@Deprecated
public class TradeLogisticsInstantQueryServices extends AbstractTradeLogisticsTrackingServices {
    private final String CACHE_KEY = "trade_logistics_instant_80001_query_tracking";
    private static final Logger logger = Logger.getLogger(TradeLogisticsInstantQueryServices.class);

    @Resource
    WlyLogisticsTrackingBusiness wlyLogisticsTrackingBusiness;
    @Resource
    IExpressCompanyService expressCompanyService;
    @Resource
    UploadDelayBusiness uploadDelayBusiness;

    @Resource
    ICache cache;

    @Override
    public boolean isAllow(Staff staff) {
        logger.debug(LogHelper.buildLog(staff, "快递鸟下线了,不查询快递鸟的接口获取物流信息了!"));
        return false;
    }

    @Override
    public Integer getOrder() {
        return 1;
    }

    @Resource
    private IConsignRecordService consignRecordService;

    @Override
    public void invokeLogisticsInfo(Staff staff) {
        Page page = new Page();
        int pageNo = 1;
        page.setPageSize(500);
        LogisticsTrackingPollPoolQueryParams queryParams = new LogisticsTrackingPollPoolQueryParams();
        queryParams.setPage(page);
        int totalCount = 0;
        int succCount = 0;
        for (; ; ) {
            ConsignRecordQueryParams params = new ConsignRecordQueryParams();
            page.setPageNo(pageNo);
            params.setPage(page);
            params.setIsError(2);
            List<ConsignRecord> records = consignRecordService.list(staff, params);
            pageNo++;
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            List<ConsignRecord> logisticsRecord = createLogisticsRecord(staff, records);
            if (CollectionUtils.isNotEmpty(logisticsRecord)) {
                succCount += logisticsRecord.size();
                uploadDelayBusiness.upload(staff, logisticsRecord.stream().map(ConsignRecord::getSid).toArray(Long[]::new), SendType.DELAY, null);
            }
            totalCount += records.size();
        }
        Logs.debug(LogHelper.buildLogHead(staff).append(" 跟新总记录数:").append(totalCount).append(" 跟新成功记录数:").append(succCount));
    }

    private List<ConsignRecord> createLogisticsRecord(Staff staff, List<ConsignRecord> logisticsInfos) {
        List<ConsignRecord> updateLogisticsList = Lists.newLinkedList();
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return updateLogisticsList;
        }
        Long start = System.currentTimeMillis();
        Map<String, String> codeMap = wlyLogisticsTrackingBusiness.queryCompanyFromWly(WlyLogisticsTrackingBusiness.PLATEFORM_TYPE);
        Map<String, UserExpressTemplate> expressTemplateMap = constrctKey2ExpressTemplate(staff);
        for (int i = 0; i < logisticsInfos.size(); i++) {
            ConsignRecord record = logisticsInfos.get(i);
            String expressCode = getExpressCode(record, staff, codeMap, expressTemplateMap);
            KDniaoTrackQueryAPI.Result result = null;
            try {
                result = KDniaoTrackQueryAPI.getTradeTraces(expressCode, record.getOutSid());
                if (result != null) {
                    if ("1".equals(result.getState()) ||
                            "2".equals(result.getState()) ||
                            "3".equals(result.getState()) ||
                            "4".equals(result.getState())) {
                        ConsignRecord consignRecord = new ConsignRecord();
                        consignRecord.setSid(record.getSid());
                        updateLogisticsList.add(consignRecord);

                    }
                }
            } catch (Exception e) {
                Logs.error("查询实时物流记录失败，msg:" + e.getMessage());
            } finally {
                Logs.debug(String.format("订单【sid:%s,outSid:%s,expressCode:%s,state:%s,key:%s】", record.getSid(), record.getOutSid(), expressCode, result == null ? "" : getState(result.getState()), createTplKey(record.getTemplateId(), record.getTemplateType())));
            }

            if (i % 10 == 0) {
                Long sleepTime = 1000 - (System.currentTimeMillis() - start);
                if (sleepTime > 0) {
                    try {
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException e) {
                    }
                }
                start = System.currentTimeMillis();
            }
        }
        return updateLogisticsList;
    }

    private Integer getLogisticsStatusWeight(String logisticsStatus) {
        if (StringUtils.isEmpty(logisticsStatus)) {
            return -1;
        }
        for (ExpressPacketStatusEnum status : ExpressPacketStatusEnum.values()) {
            if (status.getValue().equals(logisticsStatus)) {
                return Integer.valueOf(status.getKey());
            }
        }
        return -1;
    }


    private String getState(String state) {
//        0-无轨迹 1-已揽收 2-在途中 3-签收 4-问题件
        switch (state) {
            case "0":
                return "无轨迹";
            case "1":
                return "已揽收";
            case "2":
                return "在途中";
            case "3":
                return "签收";
            case "4":
                return "问题件";
            default:
                return "无轨迹";
        }
    }

    private String getExpressCode(ConsignRecord record, Staff staff, Map<String, String> codeMap, Map<String, UserExpressTemplate> expressTemplateMap) {
        String tplKey = createTplKey(record.getTemplateId(), record.getTemplateType());
        UserExpressTemplate tpl = expressTemplateMap.get(tplKey);
        if (tpl == null) {
            Logs.error(LogHelper.buildLogHead(staff).append(record.getSid() + "订单快递模版找不到"));
            return "OTHER";
        }
        ExpressCompany ec = expressCompanyService.getExpressCompanyById(tpl.getExpressId());
        if (null == ec) {
            Logs.error(LogHelper.buildLogHead(staff).append(record.getSid() + "订单的快递模板所属的快递公司找不到"));
            return "OTHER";
        }
        String code = codeMap.get(ec.getName());
        return code == null ? "OTHER" : code;
    }

    @Resource
    IExpressTemplateCommonService expressTemplateCommonService;

    private Map<String, UserExpressTemplate> constrctKey2ExpressTemplate(Staff staff) {
        List<UserExpressTemplate> expressTemplates = expressTemplateCommonService.getUserExpressWlbMix(staff, QueryTemplateBuilder.buildMixNoMergeWlbExpress());
        Map<String, UserExpressTemplate> expressTemplateMap = new HashMap<String, UserExpressTemplate>(expressTemplates.size(), 1);
        for (UserExpressTemplate tpl : expressTemplates) {
            expressTemplateMap.put(createTplKey(tpl.getId(), tpl.getIsWlb()), tpl);
        }
        return expressTemplateMap;
    }

    private String createTplKey(Long templateId, Integer templateType) {
        return new StringBuilder().append(templateId).append("_").append(templateType).toString();
    }

    @Override
    public void end(Staff staff) {
        try {
            cache.delete(CACHE_KEY);
            cache.delete(CACHE_KEY + "_" + staff.getCompanyId());
        } catch (CacheException e) {
            Logs.error("缓存删除出错：" + e.getMessage());
        }
    }
}

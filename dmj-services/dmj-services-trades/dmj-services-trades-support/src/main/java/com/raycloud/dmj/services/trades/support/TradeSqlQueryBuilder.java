package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.cache.*;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.account.ThriftStaff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.SystemExcepts;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.ErpPlatformConfig;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLog;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.PaymentRangeUtils;
import com.raycloud.dmj.domain.trades.search.OrderAdditionalConditionEnum;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.except.constant.ExceptConstantOld;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.ec.support.StaffTransformService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.fx.IFxSupplierSrcService;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertBase;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import com.raycloud.dmj.services.trades.support.search.convert.OrderConditionConverter;
import com.raycloud.dmj.services.trades.support.search.convert.ReceiverConditionConverter;
import com.raycloud.dmj.services.trades.support.search.convert.TradeTypeConditionConverter;
import com.raycloud.dmj.services.trades.support.utils.TradeWeighUtils;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.tradepush.api.service.IQimenTradeDubboService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;

import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;

/**
 * 订单查询sql查询条件生成类
 * Created by CXW on 2016/12/1.
 */
@Component
public class TradeSqlQueryBuilder extends TradeQueryBuilder {

    @Autowired(required = false)
    private IQimenTradeDubboService qimenTradeDubboService;

    @Autowired(required = false)
    private IFxSupplierSrcService fxSupplierSrcService;

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    IWarehouseService warehouseService;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    ITradeTagService tradeTagService;

    @Resource
    TradeExtBusiness tradeExtBusiness;
    @Resource
    IItemServiceWrapper itemServiceWrapper;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    public TradeNewExceptSqlQueryBuilder tradeNewExceptSqlQueryBuilder;

    @Resource
    IShopService shopService;

    @Resource
    StaffTransformService staffTransformService;


    @Resource
    FeatureService featureService;


    private  static  final  Map<Integer,String> ONLY_CONTAIN_TYPE_2_SYMBOL_MAP = Maps.newHashMapWithExpectedSize(8);
    static {
        ONLY_CONTAIN_TYPE_2_SYMBOL_MAP.put(0,"=");
        ONLY_CONTAIN_TYPE_2_SYMBOL_MAP.put(1,"<=");
        ONLY_CONTAIN_TYPE_2_SYMBOL_MAP.put(2,">=");
    }

    private static final Cache<Long, List<String>> EXCEPTIDS  = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();


    public static final String TRADE_ORDER_SYS_STATUS_SQL = ".sys_status IN ('WAIT_BUYER_PAY','WAIT_AUDIT','WAIT_FINANCE_AUDIT','FINISHED_AUDIT','SELLER_SEND_GOODS','FINISHED','CLOSED')";


    @Resource
    IStaffService staffService;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Override
    public Query buildWaitPackQuery(Query q, Staff staff, TradeQueryParams params, TradeConfig tc) {
        boolean hasNoMixKey = StringUtils.isBlank(params.getMixKey());
        if (hasNoMixKey) {//单个订单扫描包装时，查询出店铺停用的订单，反馈错误信息给前端，否则不查询店铺停用订单
            params.setCheckActive(true);
        } else {
            params.setIsExcep(null);
        }
        buildQuery(q, staff, params.setIsOutstock(0));
        buildWarehouseQuery(q, staff, 0, params.getWarehouseIds());
        buildMixKeyQuery(q, staff , params);
        q.append(" AND t.sys_status = ?").add(Trade.SYS_STATUS_FINISHED_AUDIT);
        q.append(" AND t.is_package = ?").add(0);
        if (params.getAllowUnPrintPack() == null || params.getAllowUnPrintPack() == 0) {
            q.append(buildPrintStatusQuery(q, tc));
        }
        if (hasNoMixKey) {//单个订单扫描称重时需要包含异常订单，用于将异常信息反馈给前端，否则不查询异常订单
            q.append(" AND t.is_cancel = 0 AND t.is_halt = 0");
        }
        return q;
    }

    public Query buildPackSplitableQuery(Query q, Staff staff, TradeQueryParams params) {
        boolean hasNoMixKey = StringUtils.isBlank(params.getMixKey());
        if (hasNoMixKey) {//单个订单扫描包装时，查询出店铺停用的订单，反馈错误信息给前端，否则不查询店铺停用订单
            params.setCheckActive(true);
        } else {
            params.setIsExcep(null);
        }
        buildQuery(q, staff, params.setIsOutstock(0));
        buildWarehouseQuery(q, staff, 0, params.getWarehouseIds());
        buildMixKeyQuery(q, staff , params);
        q.append(" AND t.sys_status = ?").add(Trade.SYS_STATUS_FINISHED_AUDIT);
        q.append(" AND ").append("(t.out_sid != '' and t.out_sid is not null)");
        if (hasNoMixKey) {//单个订单扫描称重时需要包含异常订单，用于将异常信息反馈给前端，否则不查询异常订单
            q.append(" AND t.is_cancel = 0 AND t.is_halt = 0");
        }
        return q;
    }

    @Override
    public Query buildWaitWeighQuery(Query q, Staff staff, TradeQueryParams params, TradeConfig tc) {
        boolean hasNoMixKey = StringUtils.isBlank(params.getMixKey());
        if (hasNoMixKey) {//单个订单扫描称重时，查询出店铺停用的订单，反馈错误信息给前端，否则不查询店铺停用订单
            params.setCheckActive(true);
        } else {
            params.setIsExcep(null);
        }
        buildQuery(q, staff, params.setIsOutstock(0));
        buildWarehouseQuery(q, staff, 0, params.getWarehouseIds());
        buildMixKeyQuery(q, staff, params);
        if (!Boolean.TRUE.equals(params.getValidateLater())) {
            q.append(" AND t.sys_status = ?").add(Trade.SYS_STATUS_FINISHED_AUDIT);
            q.append(" AND t.is_weigh = ?").add(0);
            q.append(buildIsPackQuery(q, tc));
            q.append(buildPrintStatusQuery(q, tc));
        }
        if (hasNoMixKey) {//单个订单扫描包装时需要包含异常订单，用于将异常信息反馈给前端，否则不查询异常订单
            q.append(" AND t.is_cancel = 0 AND t.is_halt = 0");
        }
        return q;
    }

    @Override
    public Query buildWaitConsignQuery(Query q, Staff staff, TradeQueryParams params, TradeConfig tc) {
        boolean hasNoMixKey = StringUtils.isBlank(params.getMixKey());
        if (hasNoMixKey) {//单个订单扫描发货时，查询出店铺停用的订单，反馈错误信息给前端，否则不查询店铺停用订单
            params.setCheckActive(true);
        } else {
            params.setIsExcep(null);
        }
        buildQuery(q, staff, params.setIsOutstock(0));
        buildWarehouseQuery(q, staff, 0, params.getWarehouseIds());
        buildMixKeyQuery(q,staff, params);
        q.append(" AND t.sys_status = ?").add(Trade.SYS_STATUS_FINISHED_AUDIT);
        String consignType = params.getConsignType() != null ? params.getConsignType().trim() : "";
        if (SendType.DUMMY.name().equals(consignType) || "虚拟发货".equals(consignType)) {
            q.append(" AND t.template_id = 0 AND t.can_confirm_send = ? AND t.sub_source is null").add(0);
        } else {
            String isPackage = buildIsPackQuery(q, tc);
            String isWeigh = buildIsWeighQuery(q, tc);
            String printStatus = buildPrintStatusQuery(q, tc);
            if (consignType.isEmpty()) {
                q.append(" AND ((t.template_id > 0").append(isWeigh).append(isPackage).append(printStatus).append(") OR t.template_id = 0)");
            } else if (SendType.OFFLINE.name().equals(consignType) || SendType.ONLINE.name().equals(consignType)) {
                q.append(isWeigh).append(isPackage).append(printStatus).append(" AND t.template_id > 0");
            } else {
                throw new IllegalArgumentException("暂不支持的的发货方式: " + consignType);
            }
        }
        if (hasNoMixKey) {//单个订单扫描发货时需要包含异常订单，用于将异常信息反馈给前端，否则不查询异常订单
            q.append(" AND t.is_cancel = 0 ");
        }
        return q;
    }

    @Override
    public Query buildQuery(Query q, Staff staff, TradeQueryParams params) {
        if (params == null) {
            return buildQuery(q, staff, false);
        }
        // orderQry.and().append("t.company_id = ? AND (t.enable_status = 1 OR t.enable_status = 2)").add(staff.getCompanyId());
        q.and().append("t.company_id = ? AND (t.enable_status = 1").add(staff.getCompanyId());
        q.conjunct(" OR t.enable_status = 2", params.getContext().isUniqueQuery()).append(")");
        if (_buildTypeQuery(q, params)) {//如果是出库单，不拼接users
            return q;
        }
        buildUserQuery(q, staff, params);
        buildDestQuery(q, params);
        buildSourceQuery(q, params);
        return q;
    }


    public Query buildDestQuery(Query q, TradeQueryParams params) {
        Long[] destIds = params.getDestIds();
        if (destIds != null && destIds.length > 0) {
            StringBuilder sb = new StringBuilder();
            for (Long destId : destIds) {
                buidDestSource(q, destId, sb);
            }
            q.and().append(" t.dest_id IN (").append(sb).append(")");
        }
        return q;
    }

    public Query buildSourceQuery(Query q, TradeQueryParams params) {
        Long[] sourceIds = params.getSourceIds();
        if (sourceIds != null && sourceIds.length > 0) {
            StringBuilder sb = new StringBuilder();
            for (Long sourceId : sourceIds) {
                buidDestSource(q, sourceId, sb);
            }
            q.and().append(" t.source_id IN (").append(sb).append(")");
        }
        return q;
    }

    private StringBuilder buidDestSource(Query q, Long destId, StringBuilder buf) {
        q.add(destId);
        if (buf.length() > 0) {
            buf.append(", ");
        }
        buf.append("?");
        return buf;
    }

    public Query buildDistributorQuery(Query q, Staff staff, TradeQueryParams params){
        if (!params.getContext().isOpenDistributorAuth()){
            return q;
        }
        //用户的分销商权限过滤
        String distributorGroup = staff.getDistributorGroup();
        if (StringUtils.isEmpty(distributorGroup)) {
            //过滤掉所有的供销、分销且供销的订单
            q.and().append(" NOT (( ").append(getTypeQuery(staff, q, 34, null)).append(" ) OR ( ").append(getTypeQuery(staff, q, 48, null)).append(" ))");
        } else if (distributorGroup.contains("all")) {
            //不做过滤动作
        } else {
            //供销、分销且供销订单的过滤
            List<Long> distributorIds = com.raycloud.dmj.domain.utils.ArrayUtils.toLongList(distributorGroup);
            if (CollectionUtils.isEmpty(distributorIds)) {
                q.and().append(" NOT (( ").append(getTypeQuery(staff, q, 34, null)).append(" ) OR ( ").append(getTypeQuery(staff, q, 48, null)).append(" ))");
            } else {
                q.add(distributorIds).and().append(" NOT ((( ").append(getTypeQuery(staff, q, 34, null)).append(" ) OR ( ").append(getTypeQuery(staff, q, 48, null)).append(" )) AND t.source_id NOT IN ( ").append(distributorIds.stream().map(t -> "?").collect(Collectors.joining(","))).append(" ))");
            }
        }
        return q;
    }

    /**
     * 业务员权限
     * @param q
     * @param staff
     * @param params
     * @return
     */
    public Query buildSalesmanQuery(Query q, Staff staff, TradeQueryParams params){
        if (!params.getContext().isOpenSalesmanAuth()){
            return q;
        }
        if (q.isStopQuery()) {
            return q;
        }

        //用户的分销商权限过滤
        String salesman = staff.getStaffGroup();
        String table = "trade_salesman_" +staff.getDbInfo().getTradeDbNo();
        String tradeTable = q.getTradeTable()+"_"+staff.getDbInfo().getTradeDbNo();



        String salesmanNulSql =" NOT EXISTS ("
                    +"( SELECT 1 FROM "+table +" salesman WHERE salesman.sid = t.sid AND salesman.company_id = t.company_id AND salesman.enable_status = 1 )"
                    +" UNION ALL "
                    +"( SELECT 1 FROM "+ tradeTable +" tmp WHERE t.merge_sid > 0 AND tmp.merge_sid =t.sid AND tmp.company_id =t.company_id AND tmp.enable_status = 2 "
                    +" AND EXISTS ( SELECT 1 FROM "+table +" salesman WHERE salesman.sid = tmp.sid AND salesman.company_id = tmp.company_id AND salesman.enable_status = 1 )"
                    +")"
                +")";

        if (StringUtils.isBlank(salesman)) {
            q.setStopQuery(true);
            q.setStopReason("无业务员权限");
            return q;
        } else if (salesman.contains("all")) {
            //不做过滤动作
        } else {
            //查询业务员在权限范围 或为空的数据
            List<Long> salesmanIds = com.raycloud.dmj.domain.utils.ArrayUtils.toLongList(salesman);
            boolean  queryNoSalesman = false;
            if (salesmanIds.contains(-1L)) {
                queryNoSalesman = true;
                salesmanIds.remove(-1L);
            }
            if (CollectionUtils.isEmpty(salesmanIds)) {
                q.setStopQuery(true);
                q.setStopReason("无业务员权限");
                return q;
            } else {
                q.and().append("( ")
                            .append(" EXISTS (")
                                .append("( SELECT 1 FROM "+table +" salesman WHERE salesman.sid = t.sid AND salesman.company_id = t.company_id AND salesman.enable_status = 1 ")
                                    .append("AND salesman.salesman_id in ( ").append(salesmanIds.stream().map(t -> "?").collect(Collectors.joining(","))).append(" )").add(salesmanIds)
                                .append(") UNION ALL ")
                                .append("( SELECT 1 FROM "+ tradeTable +" tmp WHERE t.merge_sid > 0 AND tmp.merge_sid =t.sid AND tmp.company_id =t.company_id AND tmp.enable_status = 2 ")
                                    .append("AND EXISTS ( SELECT 1 FROM "+table +" salesman WHERE salesman.sid = tmp.sid AND salesman.company_id = tmp.company_id AND salesman.enable_status = 1 ")
                                    .append("AND salesman.salesman_id in ( ").append(salesmanIds.stream().map(t -> "?").collect(Collectors.joining(","))).append(" )").add(salesmanIds)
                                    .append(")")
                                .append(")")
                            .append(")");
                            if (queryNoSalesman){
                                q.or().append(salesmanNulSql);
                            }
                q.append(")");

            }
        }
        return q;
    }

    @Override
    public Query buildQuery(Query q, Staff staff, boolean ignoreUserPrivilege) {
        q.add(staff.getCompanyId());
        q.and().append("t.company_id = ? AND t.enable_status = 1");
        if (!ignoreUserPrivilege) {
            if (staff.getUsers() == null || staff.getUsers().isEmpty()) {
                return q.append(" AND t.user_id = -1");
            }
            buildArrayQuery(q, "t.user_id", staff.getUsers().stream().map(User::getId).toArray(Long[]::new));
        }
        return q;
    }

    @Override
    public <T> Query buildArrayQuery(Query q, String key, T... array) {
        if (array == null || array.length == 0 || key == null || (key = key.trim()).isEmpty()) {
            return q;
        }
        StringBuilder buf = new StringBuilder();
        int n = 0;
        for (T v : array) {
            if (v == null) {
                continue;
            }
            if (n++ > 0) {
                buf.append(", ");
            }
            buf.append("?");
            q.add(v);
        }
        return n > 1 ? q.and().append(key).append(" IN (").append(buf).append(")") : (n == 1 ? q.and().append(key).append(" = ").append(buf) : q);
    }

    @Override
    public Query buildReceiverPhoneQuery(Staff staff, Query q, String key, List<String> array) {
        if (array == null || array.size() == 0 || key == null || (key = key.trim()).isEmpty()) {
            return q;
        }
        StringBuilder buf = new StringBuilder();
        int n = 0;
        for (String v : array) {
            if (v == null) {
                continue;
            }
            if (n++ > 0) {
                buf.append(", ");
            }
            buf.append("?");
            q.add(v);
        }
        //for key1
        for (String v : array) {
            if (v == null) {
                continue;
            }
            q.add(v);
        }
        return q.and().append(" ( ")
                .append(" t.").append(key).append(" IN (").append(buf).append(")")
                .append(" or exists (" + buildExists(staff, key, buf.toString())).append(")")
                .append(" )");
    }


    @Override
    public <T> Query buildArrayOrQuery(Query q, String key, T... array) {
        if (array == null || array.length == 0 || key == null || (key = key.trim()).isEmpty()) {
            return q;
        }
        StringBuilder buf = new StringBuilder();
        int n = 0;
        for (T v : array) {
            if (v == null) {
                continue;
            }
            if (n++ > 0) {
                buf.append(", ");
            }
            buf.append("?");
            q.add(v);
        }
        return n > 1 ? q.or().append(key).append(" IN (").append(buf).append(")") : (n == 1 ? q.or().append(key).append(" = ").append(buf) : q);
    }

    @Override
    public <T> Query buildListQuery(Query q, String key, List<T> list) {
        if (list == null || list.isEmpty()) {
            return q;
        }
        StringBuilder buf = new StringBuilder();
        int n = 0;
        for (T v : list) {
            if (v == null) {
                continue;
            }
            if (n++ > 0) {
                buf.append(", ");
            }
            buf.append("?");
            q.add(v);
        }
        return n > 1 ? q.and().append(key).append(" IN (").append(buf).append(")") : (n == 1 ? q.and().append(key).append(" = ").append(buf) : q);
    }

    @Override
    public <T> Query buildNotInQuery(Query q, String key, T... vs) {
        if (vs != null && vs.length > 0) {
            q.and().append("(").append(key).append(" IS NULL").or().append(key).append(" NOT IN(");
            for (int i = 0; i < vs.length; i++) {
                q.append(i == 0 ? "?" : ", ?").add(vs[i]);
            }
            q.append("))");
        }
        return q;
    }

    @Override
    public <T> Query buildNotNullQuery(Query q, String key, T v) {
        if (v != null) {
            if (v instanceof String) {
                String v1 = v.toString().trim();
                if (!v1.isEmpty()) {
                    q.and().append(key).append(" = ?").add(v);
                }
            } else {
                q.and().append(key).append(" = ?").add(v);
            }
        }
        return q;
    }

    /**
     * 构建非空的单字段查询条件
     * and (key = v or key1 = v or ...)
     *
     * @param q
     * @param v    字段值
     * @param keys 字段名
     * @return
     */
    public Query buildNotNullQueryAndKeyOrKey(Query q, String v, List<String> keys) {
        if (v != null) {
            if (v instanceof String) {
                String v1 = v.toString().trim();
                if (v1.isEmpty()) {
                    return q;
                }
            }
            if (keys.size() == 1) {
                q.and().append(keys).append(" = ?").add(v);
            } else if (keys.size() > 1) {
                q.and().append("(");
                int i = 0;
                for (String key : keys) {
                    if (i++ > 0) {
                        q.append(" or ");
                    }
                    q.append(key).append(" = ?").add(v);
                }
                q.append(")");
            }
        }
        return q;
    }

    @Override
    public Query buildOrderSupplierQuery(Staff staff, Query q, List<Long> supplierIds) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return q;
        }
        String supplierStr = supplierIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" INNER JOIN (select t.sid from order_supplier_").append(staff.getDbInfo().getOrderDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.supplier_id in (").append(supplierStr).append(")");
        queryBuilder.append(" and t.merge_sid=-1 and s.enable_status=1 and t.enable_status=1");
        queryBuilder.append(" union ");
        queryBuilder.append(" select t.merge_sid sid from order_supplier_").append(staff.getDbInfo().getOrderDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.supplier_id in (").append(supplierStr).append(")");
        queryBuilder.append(" and t.merge_sid>-1 and s.enable_status=1 and t.enable_status>=1) orderSupplier on t.sid=orderSupplier.sid");
        q.getParamBeforeWhere().put("orderSupplier", queryBuilder.toString());
        q.setOrderSupplier(true);
        return q;
    }

    @Override
    public Query buildNotNullQueryAndKeyOrExistsKey(Staff staff, Query q, String key, String v) {
        if (v != null) {
            if (v instanceof String) {
                String v1 = v.toString().trim();
                if (v1.isEmpty()) {
                    return q;
                }
            }
            q.and().append(" (")
                    .append(" t.").append(key).append(" = ?").add(v)
                    .append(" or ")
                    .append("exists (" + buildExists(staff, key, "？")).append(")").add(v)
                    .append(") ");
        }
        return q;
    }

    @Override
    public Query buildLikeQuery(Query q, int type, String key, String... values) {
        if (values == null || values.length == 0) {
            return q;
        }
        StringBuilder tq = new StringBuilder();
        int n = 0;
        for (String v : values) {
            String v1 = v != null ? v.trim() : "";
            if (!v1.isEmpty()) {
                if (type == 0) {
                    Query.conjunct(tq, " OR ", n++ > 0).append(key).append(" LIKE CONCAT(?, '%')");
                    q.add(v1);
                } else if (type == 1) {
                    Query.conjunct(tq, " OR ", n++ > 0).append(key).append(" LIKE CONCAT('%', ?, '%')");
                    q.add(v1);
                } else if (type == 2) {
                    Query.conjunct(tq, " OR ", n++ > 0).append(key).append(" LIKE CONCAT('%', ?)");
                    q.add(v1);
                }
            }
        }
        return n >= 1 ? q.and().append("(").append(tq).append(")") : q;
    }

    public Query buildNotLikeQuery(Query q, int type, String key, String... values) {
        if (values == null || values.length == 0) {
            return q;
        }
        StringBuilder tq = new StringBuilder();
        int n = 0;
        for (String v : values) {
            String v1 = v != null ? v.trim() : "";
            if (!v1.isEmpty()) {
                if (type == 0) {
                    Query.conjunct(tq, " AND ", n++ > 0).append(key).append(" NOT LIKE CONCAT(?, '%')");
                    q.add(v1);
                } else if (type == 1) {
                    Query.conjunct(tq, " AND ", n++ > 0).append(key).append(" NOT LIKE CONCAT('%', ?, '%')");
                    q.add(v1);
                } else if (type == 2) {
                    Query.conjunct(tq, " AND ", n++ > 0).append(key).append(" NOT LIKE CONCAT('%', ?)");
                    q.add(v1);
                }
            }
        }
        return n >= 1 ? q.and().append("(").append(tq).append(")") : q;
    }

    @Override
    public Query buildUserQuery(Query q, Staff staff, TradeQueryParams params) {//isOutstock=null或不等于1时才会走这里
        boolean checkActive = params.getCheckActive() != null && params.getCheckActive();
        StringBuilder enabledUidBuf = new StringBuilder(), disabledUidBuf = new StringBuilder();
        List<Long> enabledUidList = new ArrayList<Long>(), disabledUidList = new ArrayList<Long>();
        //供销订单
        Integer[] asIntArray = Strings.getAsIntArray(params.getTradeType(), ",", true);
        //奇门店铺
        boolean hasSecondShopQuery = StringUtils.isNotEmpty(params.getSecondUserId()) && StringUtils.isNotEmpty(params.getSellerNicks()) && StringUtils.isNotEmpty(params.getSubSources());
        //仅包含二级店铺
        boolean onlySecondShopQuery = false;
        Query secondShopQuery = new Query();
        boolean hasSelectedUsers = classifyUsers(staff, params, enabledUidBuf, enabledUidList, disabledUidBuf, disabledUidList, params.getTradeType());
        if (!hasSelectedUsers && params.getIsOutstock() == null) {//订单查询页面包含出库单
            addUserId(0L, enabledUidBuf, enabledUidList);
        }
        if (!hasSelectedUsers || (!hasSelectedUsers && asIntArray.length == 0)) {//订单查询页面包含出库单
            addUserId(Constants.FxDefaultUserId, enabledUidBuf, enabledUidList);
        }
        if (!checkActive) {
            if (CollectionUtils.isEmpty(enabledUidList) && CollectionUtils.isEmpty(disabledUidList)) {
                q.setStopQuery(true);
                q.setStopReason("无有效的店铺条件");
                return q;
            }
        }else {
            if (CollectionUtils.isEmpty(enabledUidList)) {
                q.setStopQuery(true);
                q.setStopReason("无有效的店铺条件");
                return q;
            }
        }
        if (hasSecondShopQuery) {
            //user_id+seller_nick+sub_source
            String[] sellerNicks = StringUtils.split(params.getSellerNicks(), ",");
            String[] subSource = StringUtils.split(params.getSubSources(), ",");
            String[] secondUserId = StringUtils.split(params.getSecondUserId(), ",");
            secondShopQuery.append("(")
                    .append("t.user_id IN (" + StringUtils.join(Stream.of(secondUserId).map(s -> "?").collect(Collectors.toList()), ",") + ")")
                    .append(" AND t.seller_nick IN (" + StringUtils.join(Stream.of(sellerNicks).map(s -> "?").collect(Collectors.toList()), ",") + ")")
                    .append(" AND (t.source IN (" + StringUtils.join(Stream.of(subSource).map(s -> "?").collect(Collectors.toList()), ",") + ")")
                    .append(" OR t.sub_source IN (" + StringUtils.join(Stream.of(subSource).map(s -> "?").collect(Collectors.toList()), ",") + "))")
                    .append(")")
                    .add(secondUserId).add(sellerNicks).add(subSource).add(subSource);
            hasSecondShopQuery = CollectionUtils.isNotEmpty(secondShopQuery.getArgs());
            onlySecondShopQuery = hasSecondShopQuery && params.getUserIds() == null || (params.getUserIds() != null && params.getUserIds().length == 0);
        }
        if (params.getIsExcep() != null && params.getIsExcep() == 1) { //查询异常订单
            if (!checkActive) {//不排除停用的店铺
                q.add(enabledUidList).add(disabledUidList);
                if (!enabledUidList.isEmpty()) {
                    q.and().conjunct("(", !disabledUidList.isEmpty());
                    //如果勾选无异常订单，按具体的类型
                    if (params.getTickExcep() - 1 != 0) {
                        q.conjunct("(is_excep = 1 AND ", !params.getContext().isUniqueQuery());//指根据订单号查询时不加异常条件，而是查询出来再过滤，避免合单查不出来
                    } else {
                        q.conjunct("(", !params.getContext().isUniqueQuery());
                    }
                    q.conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(enabledUidBuf);
                    if (!disabledUidList.isEmpty()) {
                        q.append(", ").append(disabledUidBuf);
                    }
                    q.append(")").conjunct(onlySecondShopQuery ? " AND " : " OR ",hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery);
                    q.conjunct(")", hasSecondShopQuery).conjunct(")", !params.getContext().isUniqueQuery()).conjunct(")",!disabledUidList.isEmpty());
                } else if (!disabledUidList.isEmpty()) {
                    q.and().conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(disabledUidBuf).append(")");
                    q.conjunct(onlySecondShopQuery ? " AND " : " OR ",hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery).conjunct(")", hasSecondShopQuery);
                }
            } else {//排除停用的店铺
                checkException("亲，您当前没有开启的店铺哦!", enabledUidList.isEmpty() && !hasSecondShopQuery);
                //如果勾选无异常订单，按具体的类型
                if (params.getTickExcep() - 1 != 0) {
                    q.add(enabledUidList).and().conjunct("is_excep = 1 AND ", !params.getContext().isUniqueQuery());//指根据订单号查询时不加异常条件，而是查询出来再过滤，避免合单查不出来
                } else {
                    q.add(enabledUidList).and();//指根据订单号查询时不加异常条件，而是查询出来再过滤，避免合单查不出来
                }
                if (enabledUidList.isEmpty() && hasSecondShopQuery) {
                    q.append(secondShopQuery.getQ()).add(secondShopQuery.getArgs());
                } else {
                    q.conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(enabledUidBuf).append(")").conjunct(onlySecondShopQuery ? " AND " : " OR ", hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery).conjunct(")", hasSecondShopQuery);
                }
            }
            //异常订单不包含已完成已关闭订单
            q.append(" AND t.sys_status IN(?, ?, ?)").add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT);
        } else {//非异常订单或订单查询页面
            buildIsExcep(staff,q,params);
            if (!checkActive) {
                q.add(enabledUidList).add(disabledUidList);
                if (!enabledUidList.isEmpty()) {
                    q.conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(enabledUidBuf);
                    if (!disabledUidList.isEmpty()) {
                        q.append(", ").append(disabledUidBuf);
                    }
                    q.append(")").conjunct(onlySecondShopQuery ? " AND " : " OR ", hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery).conjunct(")", hasSecondShopQuery);
                } else if (!disabledUidList.isEmpty()) {
                    q.conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(disabledUidBuf).append(")")
                            .conjunct(onlySecondShopQuery ? " AND " : " OR ",hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery).conjunct(")", hasSecondShopQuery);
                }
            } else {
                checkException("亲，您当前没有开启的店铺哦!", enabledUidList.isEmpty() && !hasSecondShopQuery);
                if (enabledUidList.isEmpty() && hasSecondShopQuery) {
                    q.append(secondShopQuery.getQ()).add(secondShopQuery.getArgs());
                } else {
                    q.add(enabledUidList).conjunct("(", hasSecondShopQuery).append("t.user_id IN(").append(enabledUidBuf).append(")").conjunct(onlySecondShopQuery ? " AND " : " OR ",hasSecondShopQuery).append(secondShopQuery.getQ(), hasSecondShopQuery).add(secondShopQuery.getArgs(), hasSecondShopQuery).conjunct(")", hasSecondShopQuery);
                }
            }
        }
        return q;
    }

    private void buildIsExcep(Staff staff, Query q, TradeQueryParams params){
        if(!buildSupportStockInsufficient(staff,q,params)){
            q.and().conjunct("is_excep = 0 AND ", params.getIsExcep() != null && params.getIsExcep()==0);
        }
    }
    /**
     * <pre>
     * 查询非异常订单时 需要展示库存不足订单的场景处理
     *
     * 财审页面 如果开启 financial-auditing
     *  https://gykj.yuque.com/entavv/xb9xi5/rkzasuhgg6wts06d
     *
     * 快递打印V2 -打印发货或者快递打印- 快递单未打印，仅有缺货异常的订单，需要开启TRADE_PRINT_SUPPORT_ONLY_STOCK_INSUFFICIENT配置
     *  queryId: 77 QUERY_PRINT_V2    快递打印V2 -打印发货
     *  queryId: 26 QUERY_WAIT_PRINT  快递打印- 快递单未打印
     *  queryId: 28 QUERY_PRINT_FAHUO 快递打印-打印发货
     * </pre>
     *
     * @param staff
     * @param q
     * @param params
     */
    private boolean buildSupportStockInsufficient(Staff staff, Query q, TradeQueryParams params) {
        if(java.util.Objects.isNull(params.getQueryId())){
            return false;
        }

        if (params.getQueryId() == QUERY_WAIT_PRINT || params.getQueryId() == QUERY_PRINT_V2 || params.getQueryId() == QUERY_PRINT_FAHUO
                || params.getQueryId().equals(QUERY_RAPID_PRINT_WAIT_CONSIGN)) {
            TradeConfigNew tradePrintSupportOnlyStockInsufficient = TradeConfigGetUtil.get(staff, TradeConfigEnum.TRADE_PRINT_SUPPORT_ONLY_STOCK_INSUFFICIENT);
            if (tradePrintSupportOnlyStockInsufficient == null || !tradePrintSupportOnlyStockInsufficient.isOpen()) {
                return false;
            }
        } else if (params.getQueryId() == QUERY_WAIT_FINANCE_AUDIT) {
            if (!featureService.checkHasFeatureByCode(staff.getCompanyId(), "financial-auditing")){
                return  false;
            }
        } else {
            return false;
        }

        // 设置成2，后续业务可以避免被过滤掉，SolrTradeSearchService.filterTrades()
        params.setIsExcep(2);
        q.and().append("(").append(" (t.is_excep=0 OR (t.item_excep=2 AND t.v & 4 = 0 AND t.excep=0 AND t.is_halt = 0 AND t.unattainable = 0 AND t.is_refund = 0 AND ifnull(t.except_ids,'')='')) ");
        q.append(" OR (merge_sid > 0 AND exists (SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
        q.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 and enable_status = 2 ").add(staff.getCompanyId());
        q.and().append(" (t1.is_excep=0 OR (t1.item_excep=2 AND t1.v & 4 = 0 AND t1.excep=0 AND t1.is_halt = 0 AND t1.unattainable = 0 AND t1.is_refund = 0 AND ifnull(t1.except_ids,'')='')) ");
        q.append("))) AND ");
        return true;
    }

    /**
     * 供销订单特殊设置，因为供销订单没有真实店铺
     * 需要在没有选择店铺或者tradeType =34 查询供销订单时加上供销userId
     *
     * @param userId
     * @param buf
     * @param userIds
     */
    private void addUserId(Long userId, StringBuilder buf, List<Long> userIds) {
        userIds.add(userId);
        if (buf.length() > 0) {
            buf.append(", ");
        }
        buf.append("?");
    }

    private boolean classifyUsers(Staff staff, TradeQueryParams params, StringBuilder enabledUids, List<Long> enabledUidList, StringBuilder disabledUids, List<Long> disabledUidList, String tradeType) {
        Long[] userIds = params.getUserIds();
        List<User> users = staff.getUsers();
        //正常没有权限的 这个users应该是空list 这里一般是走event造成的缺失 @see com.raycloud.dmj.session.controller.ERPControllerBase.getUsers
        if (users == null) {
            ThriftStaff ts = staffTransformService.transformStaff(staff);
            Integer extraFields = ts.getExtraFields();
            extraFields |= ThriftStaff.getUsersMark();
            ts.setExtraFields(extraFields);
            Staff filled = staffTransformService.transformThriftStaff(ts);
            staff.setUsers(filled.getUsers());
            new QueryLogBuilder(staff).append("staff不包含users属性,自动做兜底填充").printWarn(logger);
            users = filled.getUsers();
        }
        checkException("亲，您当前没有任何店铺权限哦!", users == null || users.isEmpty());
        boolean checkActive = params.getCheckActive() != null && params.getCheckActive();
        Map<Long, User> userMap = staff.getUserIdMap();
        if (userMap == null) {
            userMap = new HashMap<Long, User>(users.size(), 1);
            for (User user : users) {
                userMap.put(user.getId(), user);
            }
        }

        Set<String> sources = new HashSet<>();


        List<String> taobaoIds = null;
        if (params.getTaobaoIds() !=null && params.getTaobaoIds().length > 0) {
            taobaoIds = Arrays.asList(params.getTaobaoIds());
        }


        String source = params.getSource();

        QueryLogBuilder builder = new QueryLogBuilder(staff).append("checkActive",checkActive).append("店铺条件异常:").startWatch();

        Set<String> sourceSet = Strings.getAsStringSet(source, ",", true);
        //选择了店铺
        if ((userIds != null && userIds.length > 0 ) || StringUtils.isNotBlank(params.getSecondUserId())) {
            List<Long> unFounds = new ArrayList<>();
            for (int i = 0; i < userIds.length; i++) {
                Long userId = userIds[i];
                //供销订单，供销店铺是虚拟的，这里过滤，允许查询
                if (Constants.FxDefaultUserId.equals(userId)) {
                    addUserId(userId, enabledUids, enabledUidList);
                    continue;
                }
                User user = userMap.get(userId);
                if (user == null) {
                    unFounds.add(userId);
                    continue;
                }
                if (source != null && !sourceSet.contains(user.getSource())) {
                    continue;
                }
                if (user.getActive() != null && user.getActive() == 0) {//停用的店铺
                    builder.group("停用店铺",user.getId());
                    addUserId(user.getId(), disabledUids, disabledUidList);
                    add2UserSourceMap(params, user);
                } else {//启用的店铺
                    //如果指定了平台店铺Id 则这里提前把不符合的user也过滤掉
                    //if (taobaoIds != null && !taobaoIds.contains(String.valueOf(user.getTaobaoId()))) {
                    //    continue;
                    //}
                    if (CommonConstants.PLAT_FORM_TYPE_FXG_GX.equals(user.getSource())){
                        sources.add(CommonConstants.PLAT_FORM_TYPE_FXG_GX);
                    }
                    addUserId(user.getId(), enabledUids, enabledUidList);
                    add2UserSourceMap(params, user);
                }
            }

            //支持奇门二级店铺查询
            String secondUserIds = params.getSecondUserId();
            if (StringUtils.isNotBlank(secondUserIds)) {
                List<Long> list = Strings.getAsLongList(secondUserIds, ",", false);
                for (Long userId : list) {
                    User user = userMap.get(userId);
                    if (user == null) {
                        unFounds.add(userId);
                        continue;
                    }else{
                        add2UserSourceMap(params, user);
                        //没有其他店铺的情况下 把这个二级店铺放进去,保证user_id条件拼装
                        //这个二级店铺的条件是独立拼装的 在存在其他店铺的情况下 与user_id条件是OR的关系
                        if ((userIds == null || userIds.length == 0 )) {
                            addUserId(user.getId(), enabledUids, enabledUidList);
                        }
                    }
                }
            }


            if (CollectionUtils.isNotEmpty(unFounds)) {
                List<Shop> list = shopService.queryByUserIds(staff,unFounds.toArray(new Long[0]));

                if (CollectionUtils.isNotEmpty(list)) {
                    for (Shop shop : list) {
                        String sourceName = PlatformSourceConstants.getSourceName(shop.getSource());
                        String subSource = shop.getSubSource();
                        if (subSource !=null) {
                            String subSourceName = PlatformSourceConstants.getSourceName(subSource);
                            if (subSourceName != null) {
                                sourceName = subSourceName;
                            }
                        }
                        String title = shop.getTitle();
                        if (sourceName != null) {
                            builder.append(sourceName);
                        }
                        builder.group("无权限",title+"(" +shop.getUserId() + ")");
                        unFounds.remove(shop.getUserId());
                    }
                }
                if (CollectionUtils.isNotEmpty(unFounds)) {
                    for (Long unFound : unFounds) {
                        builder.group("无效店铺",unFound);
                    }
                }
            }

            builder.printWarn(logger);

            params.getContext().setSources(sources);

            return true;
        } else {
            params.getContext().addSuggests("指定或缩小需要筛选的店铺范围");
            //没有选择，需要把供销订单也加进去
            for (int i = 0; i < users.size(); i++) {//区分开启用店铺和停用店铺
                User user = users.get(i);

                if (source != null && !sourceSet.contains(user.getSource())) {
                    continue;
                }
                if (user.getActive() != null && user.getActive() == 0) {//停用的店铺
                    addUserId(user.getId(), disabledUids, disabledUidList);
                    add2UserSourceMap(params, user);
                } else {//启用的店铺
                    //如果指定了平台店铺Id 则这里提前把不符合的user也过滤掉
                    //if (taobaoIds != null && !taobaoIds.contains(String.valueOf(user.getTaobaoId())) && !Constants.FxDefaultUserId.equals(user.getId())) {
                    //    continue;
                    //}
                    if (CommonConstants.PLAT_FORM_TYPE_FXG_GX.equals(user.getSource())) {
                        sources.add(CommonConstants.PLAT_FORM_TYPE_FXG_GX);
                    }
                    addUserId(user.getId(), enabledUids, enabledUidList);
                    add2UserSourceMap(params, user);
                }
            }
            params.getContext().setSources(sources);
            return false;
        }
    }

    private void add2UserSourceMap(TradeQueryParams params, User user) {
        params.getContext().addUserSourceMap(user.getSource(), user.getId());
    }

    @Override
    public Query buildSysStatusQuery(Query q, TradeQueryParams params, TradeConfig tc) {
        String[] sysStatusList = params.getSysStatus();
        int c = 0;
        boolean hasCancel = false;
        StringBuilder buf = null;
        if (sysStatusList != null && sysStatusList.length > 0) {
            buf = new StringBuilder();
            if (params.getPackageStatus() != null && params.getPackageStatus().length > 0) {
                List<String> list = Arrays.asList(sysStatusList);
                if (!(list.contains(Trade.SYS_STATUS_FINISHED) || list.contains(Trade.SYS_STATUS_CLOSED) || list.contains(Trade.SYS_STATUS_SELLER_SEND_GOODS))) {
                    q.setStopQuery(true);
                    q.setStopReason("查询 已发未验/已发已验 订单 但是指定查询状态不包含发货后状态");
                }
            }
            for (String s : sysStatusList) {
                if (TradeStatusUtils.isSysStatus(s)) { //如果是系统状态,过滤掉交易作废的订单
                    q.add(s);
                    Query.conjunct(buf, " OR ", c++ > 0).append("t.sys_status = ?");
                } else if (TradeQueryParams.STATUS_CANCEL.equals(s)) {
                    hasCancel = true;
                } else if (Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(s)) {//待打印快递单
                    q.add(Trade.SYS_STATUS_FINISHED_AUDIT).add(TradeTimeUtils.INIT_DATE);
                    Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.express_print_time <= ? AND t.belong_type in (0,2))");
                } else if (Trade.SYS_STATUS_WAIT_DELIVERY_PRINT.equals(s)) {//待打印发货单
                    q.add(Trade.SYS_STATUS_FINISHED_AUDIT).add(TradeTimeUtils.INIT_DATE);
                    Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.deliver_print_time <= ?)");
                } else {
                    String sysStatusTemp = "null";
                    if (Trade.SYS_STATUS_WAIT_PACKAGE.equals(s)) {//待包装
                        if (tc.getOpenPackageExamine() == 1) {
                            sysStatusTemp = Trade.SYS_STATUS_FINISHED_AUDIT;
                            q.add(sysStatusTemp).add(0);
                            Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.is_package = ?").append(buildPrintStatusQuery(q, tc)).append(")");
                        }
                    } else if (Trade.SYS_STATUS_WAIT_WEIGHT.equals(s)) {//待称重
                        if (tc.getOpenPackageWeigh() == 1) {
                            sysStatusTemp = Trade.SYS_STATUS_FINISHED_AUDIT;
                            q.add(sysStatusTemp).add(0);
                            Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.is_weigh = ?").append(buildIsPackQuery(q, tc)).append(buildPrintStatusQuery(q, tc)).append(")");
                        }
                    } else if (Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(s)) {//待发货
                        if (params.getIsOutstock() != null) {
                            if (params.getIsOutstock() == 1) {//待出库单页面查询待发货的出库单
                                Query.conjunct(buf, " OR ", c++ > 0).append("t.sys_status = ?");
                                q.add(Trade.SYS_STATUS_FINISHED_AUDIT);
                            } else if (params.getIsOutstock() == 0) {//只查询正常的待发货订单，不包括出库单
                                q.add(Trade.SYS_STATUS_FINISHED_AUDIT);
                                Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND ((t.template_id > 0 ").append(buildIsWeighQuery(q, tc)).append(buildIsPackQuery(q, tc)).append(buildPrintStatusQuery(q, tc)).append(") OR t.template_id = 0))");
                            }
                        } else {//出库单非出库单只要是待发货的都查询
                            q.add(Trade.SYS_STATUS_FINISHED_AUDIT);
                            Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND ((t.template_id > 0 ").append(buildIsWeighQuery(q, tc)).append(buildIsPackQuery(q, tc)).append(buildPrintStatusQuery(q, tc)).append(") OR t.template_id = 0 OR t.type like 'trade_out%'))");
                        }
                    } else if (Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(s)) {//待供销商发货
                        q.add(Trade.SYS_STATUS_FINISHED_AUDIT);
                        Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.convert_type = 1 AND  t.belong_type IN (1,3))");
                        q.add(Trade.SYS_STATUS_FINISHED_AUDIT);
                        Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.v & 8388608>0)");
                        q.add(Trade.SYS_STATUS_WAIT_AUDIT);
                        Query.conjunct(buf, " OR ", c++ > 0).append("(t.sys_status = ? AND t.convert_type = 2 AND  t.belong_type = 1 )");
                    }
                }
            }
        }
        if (c > 0) {
            if (params.getContext().isHaveCancel()) {
                q.and().conjunct("(", c > 1).append(buf).conjunct(")", c > 1);
            } else {
                //如果参数中没有设置isCancel，此处需要拼接is_cancel = ?
                boolean joinUnCancel = params.getIsCancel() == null;
                q.and().conjunct("(", hasCancel).conjunct("(", joinUnCancel);
                q.conjunct("(", c > 1).append(buf).conjunct(")", c > 1);
                q.conjunct(" AND t.is_cancel = 0)", joinUnCancel).conjunct(" OR (t.is_cancel = 1))", hasCancel);
            }
        } else {
            if (hasCancel) {
                q.and().append("(t.is_cancel = 1 AND t.sys_status IN(?, ?, ?, ?))").add(Trade.SYS_STATUS_WAIT_BUYER_PAY).add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.SYS_STATUS_FINISHED_AUDIT).add(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT);
            }
        }
        return q;
    }

    @Override
    public Query buildStatusQuery(Query q, TradeQueryParams params) {
        String[] statusList = params.getStatusList();
        if (statusList == null || statusList.length == 0) {
            return q;
        }
        int c = 0;
        for (String status : statusList) {
            if (status != null && !(status = status.trim()).isEmpty()) {
                if (c++ == 0) {
                    q.and().append("(").append("t.unified_status = ?").add(status);
                } else {
                    q.or().append("t.unified_status = ?").add(status);
                }
            }
        }
        return q.conjunct(")", c > 0);
    }

    @Override
    public Query buildExceptionQuery(Staff staff, Query q, TradeQueryParams params,
                                     boolean primaryOnlyContain, boolean unchanged) {
        Integer itemExcepOpen = params.getItemExcepOpen();
        Integer onlyContain = params.getOnlyContain();
        //仅包含
        if (itemExcepOpen != null && itemExcepOpen - 1 == 0 && onlyContain != null && onlyContain - 1 == 0) {
            return buildExcepSqlOnlyContain(staff, q, params);
        } else if (onlyContain != null && onlyContain - 2 == 0) {
            //排除
            Query q1 = buildExceptSqlExclude(staff,q,params, true, primaryOnlyContain);
            Query q2 = buildExceptSqlExclude(staff,q,params, false, primaryOnlyContain);
            if (q1.getQ().length() > 0) {
                q.and();
                if (params.getTickExcep() - 1 == 0) {
                    if (!primaryOnlyContain) {
                        q.append("is_excep = 1").and();
                    }
                }
                q.append(q1);
                q.append(" AND (merge_sid = -1 ");
                //如果是组合包含过来，
                if (primaryOnlyContain) {
                    q.or().append("is_excep = 0 ");
                }
                q.append("OR merge_sid not in (SELECT merge_sid FROM ")
                        .append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo())
                        .append(" WHERE company_id = ? AND merge_sid > 0 AND enable_status = 2 AND ")
                        .append("(");
                q.append(q2);
                if (params.getTickExcep() - 1 == 0) {
                    if (!primaryOnlyContain) {
                        q.and().append("is_excep = 1");
                    }
                }
                q.append(")))").add(staff.getCompanyId());
            }
            //如果是支持同时筛选包含和排除指定异常状态
            //勾选无异常订单:无异常订单。此时，会筛选出 所有存在异常的订单。
            if (params.getTickExcep() - 1 == 0) {
                //如果单纯的无异常订单
                boolean isTickExcep = (params.getExceptionStatus() == null || params.getExceptionStatus().length <= 0)
                        && (params.getExcludeExceptionStatus() == null || params.getExcludeExceptionStatus().length <= 0)
                        && (params.getExceptIds() == null || params.getExceptIds().length <= 0
                        || (params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])))
                        && (params.getExcludeExceptIds() == null || params.getExcludeExceptIds().length <= 0);
                //1.正常排除
                //1.1单纯排除无异常
                //1.2排除无异常+其他异常
                //1.3排除其他异常
                //2.组合 包含+排除
                //2.1 包含单纯无异常   排除指定异常
                //2.2 包含无异常+指定异常   排除指定异常
                //2.3 包含指定异常     排除指定异常
                //2.4 包含指定异常     排除无异常
                //2.5 包含指定异常     排除无异常+指定异常
                if (q.getQ().length() > 0) {
                    if (!primaryOnlyContain) {
                        if (isTickExcep) {
                            q.and();
                        }
                    } else {
                        if (isTickExcep) {
                            q.and();
                        } else {
                            if (unchanged) {
                                q.and();
                            }
                        }
                    }
                }
                if (!primaryOnlyContain) {
                    if (isTickExcep) {
                        q.append("is_excep = 1");
                    }
                } else {
                    if (isTickExcep) {
                        q.append("is_excep = 0");
                    } else {
                        if (unchanged) {
                            q.append("is_excep = 0");
                        }
                    }
                }
            }
            return q;
        } else if (onlyContain != null && onlyContain - 3 == 0) { //且
            return buildExcepSqlAllContain(staff, q, params);
        } else {
            //包含
            return buildExcepSql(staff, q, params);
        }
    }


    private Query buildExcepSqlOnlyContain(Staff staff, Query q, TradeQueryParams params) {
        //是否包含订单异常但是不包含自定义异常的查询
        boolean containExcep = (params.getExceptionStatus() != null && params.getExceptionStatus().length > 0) && (params.getExceptIds() == null || params.getExceptIds().length == 0);
        String[] exceptions = params.getExceptionStatus();
        Query q1 = buildExceptSqlNotContain(staff,exceptions);
        Query q0 = buildExceptSqlOnlyContain(staff, q,q1, params, containExcep);
         // 系统异常已在buildExceptSqlOnlyContain 做过排除，无需额外做排除
        if (q0.getQ().length() > 0) {
            q.and().append("(");
            //勾选无异常订单
            if (params.getTickExcep() - 1 == 0) {
                q.append("is_excep = 0").or();
            }
            q.append(q0.getQ()).add(q0.getArgs()).append(")");
        }
        if(q1.getQ().length() > 0){
            q.and().append("(");
            boolean needRightBrackets = false;
            //勾选无异常订单
            if (params.getTickExcep() - 1 == 0) {
                q.append("is_excep = 0");
                if (ArrayUtils.isEmpty(params.getExceptionStatus()) && ArrayUtils.isEmpty(params.getExceptIds())) {
                    q.and();
                } else {
                    q.or().append("(");
                    needRightBrackets = true;
                }
            }
            q.append(q1.getQ()).add(q1.getArgs()).append(")").conjunct(")",needRightBrackets);
        }

        if (q0.getQ().length() == 0 && q1.getQ().length() == 0) {
            q.and().append("is_excep = 0");
        }

        //如果仅包含选择了异常没有选择自定义异常，还是走原来的逻辑，排除自定义异常
        if (containExcep) {
            Query q2 = new Query();
            q2.append("t.merge_sid <= 0 AND t.except_ids not regexp '[0-9]+'");
            q.and().append("(").append(q2.getQ());
            q.append(" or (t.merge_sid > 0 AND t.except_ids not regexp '[0-9]+' AND merge_sid not in  (select  merge_sid from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
            q.append(" WHERE company_id = ? AND enable_status = 2 AND merge_sid > 0 AND (").add(staff.getCompanyId());
            //勾选无异常订单 子单包含没有异常的订单会被排除掉
//            if (params.getTickExcep() - 1 == 0) {
//                orderQry.append("(").append("is_excep = 0").or();
//            }
            q.append(" except_ids regexp '[0-9]+'))))");
            //勾选无异常订单
//            if (params.getTickExcep() - 1 == 0) {
//                orderQry.append(")");
//            }
        }
        return q;
    }

    private Query buildExcepSqlAllContain(Staff staff, Query query, TradeQueryParams params) {
        Query q0 = buildExceptSqlAllContain( staff,params,query,params.getExceptionStatus(), params.getExceptIds(), false);
        Query q1 = buildExceptSqlAllContain(staff,params,query,params.getExceptionStatus(), params.getExceptIds(), true);
        if (q0.getQ().length() > 0) {
            query.and().append("((");
            //如勾选了无异常订单 和 挂起订单。此时，会筛选出空的订单。
            if (params.getTickExcep() - 1 == 0) {
                query.append("is_excep = 0").and();
            }
            query.append(q0.getQ()).add(q0.getArgs());
            query.append(")");
        }
        if (q1.getQ().length() > 0) {
            /*query.append(" or (t.merge_sid > 0 AND t.`merge_sid` in(select  merge_sid from ").append(query.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
            query.append(" WHERE company_id = ? AND merge_sid > 0 AND enable_status = 2 ").add(staff.getCompanyId());*/
            query.append(" or (t.merge_sid > 0 AND exists (select 1 from ").append(query.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
            query.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 ").add(staff.getCompanyId());
            query.and();
            //如勾选了无异常订单 和 挂起订单。此时，会筛选出空的订单。
            if (params.getTickExcep() - 1 == 0) {
                query.append("is_excep = 0").and();
            }
            query.append(q1.getQ()).add(q1.getArgs()).append(")))");
        }
        if (q0.getQ().length() == 0 && q1.getQ().length() == 0) {
            query.and().append("is_excep = 0");
        }
        return query;

    }

    /**
     *  查询异常包含查询
     * @param staff
     * @param q
     * @param params
     * @return
     */
    private Query buildExcepSql(Staff staff, Query q, TradeQueryParams params) {
        //如果单纯的无异常订单
        boolean isTickExcep = (params.getExceptionStatus() == null || params.getExceptionStatus().length <= 0)
                && (params.getExcludeExceptionStatus() == null || params.getExcludeExceptionStatus().length <= 0)
                && (params.getExceptIds() == null || params.getExceptIds().length <= 0
                || (params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])))
                && (params.getExcludeExceptIds() == null || params.getExcludeExceptIds().length <= 0);
        //勾选无异常订单:如勾选了 无异常订单 和 快递异常。此时，会筛选出 无异常的订单和 包含快递异常的订单。
        if (params.getTickExcep() - 1 == 0 && isTickExcep) {
            q.and().append("is_excep = 0");
        }
        String[] exceptions = params.getExceptionStatus();
        String[] exceptIds = params.getExceptIds();
        Query q0 = buildExceptSql(staff,exceptions, params.getItemExcepOpen(),q,params);
        boolean ifExcepId = (params.getExceptIds() != null && params.getExceptIds().length > 0 && !(params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])));
        if(q0.getQ().length() <= 0 && !ifExcepId){
            return q;
        }
        boolean excep = (exceptions != null && exceptions.length > 0) || (params.getExceptIds() != null && params.getExceptIds().length > 0 && !(params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])));
        if (excep) {
            q.and().append("(");
        }
        if (params.getExceptIds() != null && params.getExceptIds().length == 1 && "0".equals(params.getExceptIds()[0])) {
            q0.or();
            //勾选无异常订单
            if (params.getTickExcep() - 1 == 0) {
                q0.append("is_excep = 0").or();
            }
            q0.append("t.except_ids regexp '[0-9]+'");
        }
       // 系统异常拼接
        buidMergeExcepQuery(staff, q0, q, false, null, params,true);
        Query excepIdQuery = new Query();
        //自定义异常拼接
        buildExceptQuery(staff,excepIdQuery, "except_ids", params.getExceptIds(), params);
        buidMergeExcepQuery(staff, excepIdQuery, q, true, q0, params,false);
        if (excep) {
            q.append(")");
        }
        // 只有上传异常查询，从trade_stat表查询可以加速过滤数据
        if(exceptions!=null && exceptions.length==1 &&
                StringUtils.equals(exceptions[0], TradeExceptionStatusUtils.EX_UPLOAD_DELIVER) &&
                ArrayUtils.isEmpty(exceptIds)){
            q.and().append(" t.sid in (select sid from trade_stat_").append(staff.getDbInfo().getTradeDbNo()).append(" where ");
            q.append(" company_id = ").append(staff.getCompanyId());
            q.and().append(" is_cancel = 0 and enable_status = 1 and  upload_excep = 1)");
            params.setAllowedPgl(false);
            q.setAllowTradeCountIndex(false);
        }
        return q;
    }

    /**
     *
     * @param staff
     * @param q0
     * @param q
     * @param isExcepSearch
     * @param q1
     * @param params
     * @param isSys 是否是系统异常
     */
    private void buidMergeExcepQuery(Staff staff, Query q0, Query q, boolean isExcepSearch, Query q1,
                                     TradeQueryParams params,Boolean isSys) {
        //如果单纯的无异常订单
        boolean isTickExcep = (params.getExceptionStatus() == null || params.getExceptionStatus().length <= 0)
                && (params.getExcludeExceptionStatus() == null || params.getExcludeExceptionStatus().length <= 0)
                && (params.getExceptIds() == null || params.getExceptIds().length <= 0
                ||(params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])))
                && (params.getExcludeExceptIds() == null || params.getExcludeExceptIds().length <= 0);
        // 系统异常合单包含
        Query sysExceptMergeContailSql = new Query();
        // 自定义异常，合单拼接
        Query selfExceptMergeContailSql = new Query();
        if(isSys){
             sysExceptMergeContailSql = tradeNewExceptSqlQueryBuilder.getSysExceptMergeContailSql(staff, q, params);
        }else{
            selfExceptMergeContailSql = tradeNewExceptSqlQueryBuilder.getSelfExceptMergeContailSql(staff, q, params);
        }
        if(q0.getQ().length() <= 0
                &&sysExceptMergeContailSql.getQ().length()<=0
        &&selfExceptMergeContailSql.getQ().length()<=0){
            return;
        }
        boolean b = isExcepSearch && StringUtils.isNotBlank(q1.getQ());
        if (b) {
            q.or().append("((");
        } else {
            q.append("((");
        }
        if (params.getTickExcep() - 1 == 0) {
            q.append("is_excep = 0");
            if(isTickExcep){
                q.and();
            }else{
                q.or();
            }
        }

        if(q0.getQ().length()>0){
            q.append(q0.getQ()).add(q0.getArgs());
        }
        /*************/
        q.append(" OR (t.merge_sid > 0 AND exists ( SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
        q.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 AND (").add(staff.getCompanyId());
        //勾选无异常订单:如勾选了 无异常订单 和 快递异常。此时，会筛选出 无异常的订单和 包含快递异常的订单。
        if (params.getTickExcep() - 1 == 0) {
            q.append("(").append("is_excep = 0");
            if(isTickExcep){
                q.and();
            }else{
                q.or();
            }
        }
        /*************/
        if(sysExceptMergeContailSql.getQ().length()>0||selfExceptMergeContailSql.getQ().length()>0){
            q.append(" (");
        }
        if(q0.getQ().length()>0){
            q.append(q0.getQ().toString().replaceAll("t\\.", "t1.")).add(q0.getArgs());
        }
        if(sysExceptMergeContailSql.getQ().length()>0||selfExceptMergeContailSql.getQ().length()>0){
            if(isSys){
                // 系统异常合单包含
                if(sysExceptMergeContailSql.getQ().length()>0){
                    q.conjunct(" OR ",q0.getQ().length()>0).append(sysExceptMergeContailSql.getQ()).add(sysExceptMergeContailSql.getArgs());
                }
            }else{
                // 自定义异常，合单拼接
                if(selfExceptMergeContailSql.getQ().length()>0){
                    q.conjunct(" OR ",q0.getQ().length()>0).append(selfExceptMergeContailSql.getQ()).add(selfExceptMergeContailSql.getArgs());
                }
            }
            q.append(")");
        }
        /*************/
        //勾选无异常订单:如勾选了 无异常订单 和 快递异常。此时，会筛选出 无异常的订单和 包含快递异常的订单。
        if (params.getTickExcep() - 1 == 0) {
            q.append(")");
        }
        q.append(")))))");



    }


    private Query buildExceptSqlOnlyContain(Staff staff, Query q, Query notContainQ,TradeQueryParams params, boolean containExcep) {
        String[] exceptions = params.getExceptionStatus();
        Query q0 = new Query();
        if (exceptions != null && exceptions.length > 0) {
            Set<String> containExceps = new HashSet<String>();
            for (String s : exceptions) {
                containExceps.add(s);
            }
            int c = 0;
            // t.excep & 所有存在异常的位 >0  t.item_excep & 所有存在异常的位 >0
            long excep = 0, itemExcep = 0;
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_HALT)) {//异常状态挂起
                q0.conjunct(" OR ", c++ > 0).append("t.is_halt = 1");
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNATTAINABLE)) {//快递异常
                q0.conjunct(" OR ", c++ > 0).append("t.unattainable = 1");
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REFUND)) {//退款
                q0.conjunct(" OR ", c++ > 0).append("t.is_refund = 1");
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED)) {//对应关系变动
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.RELATION_CHANGED).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.RELATION_CHANGED;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED)) {//未分配
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.UNALLOCATED).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.UNALLOCATED;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT)) {//缺货
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.INSUFFICIENT).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.INSUFFICIENT;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED)) {//地址变动异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ADDRESS_CHANGED).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.ADDRESS_CHANGED;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED)) {//地址变动异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_CHANGED).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.ITEM_CHANGED;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_BLACK)) {//黑名单
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.BLACK_NICK).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.BLACK_NICK;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED)) {//备注异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.SELLER_MEMO_UPDATE).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.SELLER_MEMO_UPDATE;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF)) {//唯一码下架
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.UNIQUE_CODE_OFFSHELF;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_LOST_MSG)) {//信息确实
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.LOST_MESSAGE).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.LOST_MESSAGE;
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PART_REFUND)) {//部分退款异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PART_REFUND).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.PART_REFUND;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_RISK_ORDER)) {//风控异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_RISKEXCEP).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.PDD_RISKEXCEP;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_DELIVER)) {//发货异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.DELIVER_EXCEPT).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.DELIVER_EXCEPT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER)) {//上传异常
                q0.conjunct(" OR ", c++ > 0).append("t.v & 4").append(" > 0 ");
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE)) {//套件数量修改异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.SUITE_QUANTITY_CHANGE;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE)) {//普通商品转加工
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.ITEM_PROCESS_EXCEP;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX)) {//不明确供应商异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_AMBIGUITY).append(" > 0 ");
                excep = excep | TradeConstants.FX_AMBIGUITY;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX)) {//风控异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_UNAUDIT).append(" > 0 ");
                excep = excep | TradeConstants.FX_UNAUDIT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX)) {//风控异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_WAITPAY).append(" > 0 ");
                excep = excep | TradeConstants.FX_WAITPAY;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REPULSE_FX)) {//供销商打回
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_REPULSE);
                excep = excep | TradeConstants.FX_REPULSE;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT)) {//外仓商品待拆分
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT).append(" > 0 ");
                excep = excep | TradeConstants.PLATFORM_FX_SPLIT_EXCEPT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT)) {//天猫超市缺货回告
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.TMCS_STOCK_OUT).append(" > 0 ");
                excep = excep | TradeConstants.TMCS_STOCK_OUT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT)) {//天猫国际直营缺货回告
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.TMGJZY_STOCK_OUT).append(" > 0 ");
                excep = excep | TradeConstants.TMGJZY_STOCK_OUT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE)) {
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE).append(" > 0 ");
                excep = excep | TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE;
            }
            // 唯品会常态合作码未匹配异常
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH)){
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH).append(" > 0 ");
                excep = excep | TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH;
            }
            // 速卖通全托管 未接单
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION)){
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION).append(" > 0 ");
                excep = excep | TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION;
            }
            //平台仓未匹配
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING)) {
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION).append(" > 0 ");
                excep = excep | TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_COD_REPEAT)) {//货到付款订单重复异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.COD_REPEAT).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.COD_REPEAT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_OUT)) {//缺货已处理异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_STOCK_OUT).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.PDD_STOCK_OUT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_WAITING_RETURN_WMS)) {//等待退货入仓异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.WATING_RETURN_WMS_EXCEPT).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.WATING_RETURN_WMS_EXCEPT;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAIT_MERGE)) {//等待合并异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.WAIT_MERGE).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.WAIT_MERGE;
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF)) {//商品停用异常
                // q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_SHUTOFF).append(" > 0 ");
                itemExcep = itemExcep | TradeConstants.ITEM_SHUTOFF;
            }
            if (containExceps.contains(String.valueOf(ExceptEnum.OUTSID_RECOVERY_FAIL.getId()))) {//单号回收失败异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL).append(" > 0 ");
                excep = excep | ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL;
            }
            if (containExceps.contains(ExceptConstantOld.EX_REFUND_ITEM_NUM_EXCEPT)) {//退款商品数量异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_PART_PAY_EXCEPT)) {//部分付款异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PART_PAY_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.PART_PAY_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_ONLINE_LOCK_EXCEPT)) {//部分付款异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.ONLINE_LOCK_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_GX_ITEM_CHANGE_EXCEPT)) {//部分付款异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT)) {//部分付款异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_CAI_GOU_TRADE_EXCEPT)) {
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.CAI_GOU_TRADE_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_PLAT_MODIFY_ITEM_NUM_EXCEPT)) {//平台修改商品数量异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT;
            }
            if (containExceps.contains(ExceptConstantOld.EX_PO_JIA_TRADE_EXCEPT)) {//平台修改商品数量异常
                // q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT).append(" > 0 ");
                excep = excep | ExceptConstantOld.PO_JIA_TRADE_EXCEPT;
            }

            if(excep>0){
                q0.conjunct(" OR ", c++ > 0).append("t.excep & " + excep).append(" > 0 ");
            }
            if(itemExcep>0){
                q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + itemExcep).append(" > 0 ");
            }
            if (containExceps.contains(ExceptConstantOld.EX_ONLINE_STATUS_EXCEPT)) {
                q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT).append(" > 0 ");
            }
            if (containExceps.contains(ExceptConstantOld.EX_SMALL_REFUND_EXCEPT)) {
                q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT).append(" > 0 ");
            }
        }
        // 系统异常,非合单，仅包含
        Query sysExceptMergeOnlyContailSql = tradeNewExceptSqlQueryBuilder.getSysExceptNotMergeOnlyContailSql(staff, q, params);
        if(sysExceptMergeOnlyContailSql.getQ().length()>0){
            q0.conjunct(" OR ",q0.getQ().length()>0).append(sysExceptMergeOnlyContailSql.getQ()).add(sysExceptMergeOnlyContailSql.getArgs());
        }
        // 系统异常,合单，仅包含
        Query sysExceptNotMergeOnlyContailSql = tradeNewExceptSqlQueryBuilder.getSysExceptMergeOnlyContailSql(staff, q, params);
        if(sysExceptNotMergeOnlyContailSql.getQ().length()>0){
            q0.conjunct(" OR ",q0.getQ().length()>0).append(sysExceptNotMergeOnlyContailSql.getQ()).add(sysExceptNotMergeOnlyContailSql.getArgs());
        }
        if(Objects.equals(params.getTickExcep(),1)){
            q0.conjunct(" OR ",q0.getQ().length()>0).append(" t.is_excep=0 ");
        }
        // 仅包含任意系统异常查询系统异常兼容合单的子单
        if(q0.getQ().length()>0 && containExcep){
            q.and().append("((").append(q0.getQ().toString()).add(q0.getArgs()).append(")");
            if(notContainQ.getQ().length()>0){
                q.and().append("(").append(notContainQ.getQ().toString()).add(notContainQ.getArgs()).append(")");
            }
            q.append(" OR (t.merge_sid > 0 AND exists ( SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
            q.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 AND (").add(staff.getCompanyId());
            q.append(q0.getQ().toString().replaceAll("t\\.", "t1.")).add(q0.getArgs());
            if(notContainQ.getQ().length()>0){
                q.and().append("(").append(notContainQ.getQ().toString().replaceAll("t\\.", "t1.")).add(notContainQ.getArgs()).append(")");
            }
            q.append("))))");
        }
        //订单异常和自定义异常都查询
        String[] tempExceptIds = params.getExceptIds();
        if (!containExcep) {
            Query excepIdOnlySqQuery = new Query();
            Query excepIdQuery = new Query();
            Query excludeExcepIdQuery = new Query();
            int c = 0;
            if (tempExceptIds != null && tempExceptIds.length > 0) {
                for (String exceptIds :tempExceptIds) {
                    if (exceptIds != null && !(exceptIds = exceptIds.trim()).isEmpty()) {
                        if (c++ == 0) {
                            excepIdQuery.and().append(" (LENGTH(except_ids)<=").append(String.join(",",tempExceptIds).length())
                                    .and().append("(").append("LOCATE('" + exceptIds + "',`except_ids`)>0 ");
                            excepIdQuery.conjunct(" OR "+q0.getQ().toString(),q0.getQ().length()>0);
                        } else {
                            excepIdQuery.or().append(" LOCATE('" + exceptIds + "',`except_ids`)>0 ");
                        }
                    }
                }
                excepIdQuery.conjunct("))",c>0);

                // 仅包含任意，自定义异常里面的排除
                // 订单中必须仅包含勾选异常的一种或者多种，不能有勾选外的异常。举例说明，若勾选了A+B，如果订
                // 单有异常A+B+C，则查询不到，如果订单是A或者B或者A+B则可以查询到
                List<String> excludeExceptIds = getExceptIdsByCache(staff);
                excludeExceptIds.removeAll(Lists.newArrayList(tempExceptIds));
                if(CollectionUtils.isNotEmpty(excludeExceptIds)){
                    c = 0;
                    for (String e : excludeExceptIds) {
                        if (c++ == 0) {
                            excludeExcepIdQuery.and().append("(").append("LOCATE('" + e + "',`except_ids`)=0 ");
                            excludeExcepIdQuery.conjunct(" AND  "+notContainQ.getQ().toString(),notContainQ.getQ().length()>0);
                        } else {
                            excludeExcepIdQuery.and().append(" LOCATE('" + e + "',`except_ids`)=0 ");
                        }
                    }
                    excludeExcepIdQuery.conjunct(")",c>0);
                }

                // 自定义非合单异常仅包含
                Query selfExceptNotMergeOnlyContailSql = tradeNewExceptSqlQueryBuilder.getSelfExceptNotMergeOnlyContailSql(staff, q, params);
                // 自定义合单异常仅包含
                Query selfExceptMergeOnlyContailSql = tradeNewExceptSqlQueryBuilder.getSelfExceptMergeOnlyContailSql(staff, q, params);
                // 自定义合单异常仅包含
                if(selfExceptMergeOnlyContailSql.getQ().length()>0){
                    if(excludeExcepIdQuery.getQ().length()>0){
                        excludeExcepIdQuery.or().append(selfExceptMergeOnlyContailSql.getQ()).add(selfExceptMergeOnlyContailSql.getArgs());
                    }else{
                        excludeExcepIdQuery.and().append(selfExceptMergeOnlyContailSql.getQ()).add(selfExceptMergeOnlyContailSql.getArgs());
                    }
                }
                // 自定义非合单异常仅包含
                if(selfExceptNotMergeOnlyContailSql.getQ().length()>0){
                    if(excepIdQuery.getQ().length()>0){
                        excepIdQuery.or().append(selfExceptNotMergeOnlyContailSql.getQ()).add(selfExceptNotMergeOnlyContailSql.getArgs());
                    }else{
                        excepIdQuery.and().append(selfExceptNotMergeOnlyContailSql.getQ()).add(selfExceptNotMergeOnlyContailSql.getArgs());
                    }
                }
                if (excepIdQuery.getQ().length() > 0) {
                    Query wholeExcepIdQuery = new Query();
                    wholeExcepIdQuery.append(excepIdQuery.getQ()).conjunct(" AND "+excludeExcepIdQuery.getQ(),excludeExcepIdQuery.getQ().length()>0);
                    excepIdOnlySqQuery.append(" (").append(wholeExcepIdQuery).append(" OR (t.merge_sid > 0 AND EXISTS (SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 WHERE company_id = ? AND merge_sid > 0 AND merge_sid = t.merge_sid AND enable_status = 2 AND ").conjunct(wholeExcepIdQuery.getQ().toString().replaceAll("t\\.", "t1."),wholeExcepIdQuery.getQ().length()>0).append("))) ");
                    q.add(staff.getCompanyId());
                    q.conjunct(" AND "+excludeExcepIdQuery.getQ(),excludeExcepIdQuery.getQ().length()>0);
                    q.conjunct(" AND "+excepIdOnlySqQuery.getQ(),excepIdOnlySqQuery.getQ().length()>0);
                }
            }
        }
        return new Query();
    }

    private List<String> getExceptIdsByCache(Staff staff) {
        try {
            return EXCEPTIDS.get(staff.getCompanyId(),()-> {
                List<TradeTag> tags = tradeTagService.list(staff, 1);
                tags.addAll(SystemExcepts.getSystemExcepts());
                return tags.stream().filter(e->e.getId()!=null && e.getId()>10000L)
                        .map(e->String.valueOf(e.getId())).collect(Collectors.toList());
            });
        } catch (ExecutionException e) {
            logger.error(LogHelper.buildLogHead(staff).append(e.getMessage()),e);
        }
        return  Lists.newArrayList();
    }

    private Query buildExceptSqlNotContain(Staff staff, String[] exceptions) {
        Query q0 = new Query();
        if (exceptions == null || exceptions.length == 0) {
            exceptions = new String[]{"-1"};
        }
        Set<String> containExceps = new HashSet<String>();
        for (String s : exceptions) {
            containExceps.add(s);
        }
        int c = 0;
        // t.excep & 所有不存在异常的位 =0 t.item_excep & 所有不存在异常的位 =0
        long excep = 0, itemExcep = 0;
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_HALT)) {//异常状态挂起
            q0.conjunct(" AND ", c++ > 0).append("t.is_halt = 0");
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNATTAINABLE)) {//快递异常
            q0.conjunct(" AND ", c++ > 0).append("t.unattainable = 0");
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_REFUND)) {//异常状态挂起
            q0.conjunct(" AND ", c++ > 0).append("t.is_refund = 0");
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED)) {//对应关系变动
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.RELATION_CHANGED + " = 0");
            itemExcep = itemExcep | TradeConstants.RELATION_CHANGED;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED)) {//未分配
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNALLOCATED + " = 0");
            itemExcep = itemExcep | TradeConstants.UNALLOCATED;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT)) {//缺货
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.INSUFFICIENT + " = 0");
            itemExcep = itemExcep | TradeConstants.INSUFFICIENT;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED)) {//地址变动异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ADDRESS_CHANGED + " = 0");
            itemExcep = itemExcep | TradeConstants.ADDRESS_CHANGED;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED)) {//地址变动异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_CHANGED + " = 0");
            itemExcep = itemExcep | TradeConstants.ITEM_CHANGED;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_BLACK)) {//黑名单
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.BLACK_NICK + " = 0");
            itemExcep = itemExcep | TradeConstants.BLACK_NICK;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED)) {//备注异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SELLER_MEMO_UPDATE + " = 0");
            itemExcep = itemExcep | TradeConstants.SELLER_MEMO_UPDATE;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF)) {//唯一码下架
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF + " = 0");
            itemExcep = itemExcep | TradeConstants.UNIQUE_CODE_OFFSHELF;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_LOST_MSG)) {//信息确实
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.LOST_MESSAGE + " = 0");
            itemExcep = itemExcep | TradeConstants.LOST_MESSAGE;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_PART_REFUND)) {//部分退款异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PART_REFUND + " = 0");
            itemExcep = itemExcep | TradeConstants.PART_REFUND;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_RISK_ORDER)) {//风控异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_RISKEXCEP + " = 0");
            itemExcep = itemExcep | TradeConstants.PDD_RISKEXCEP;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_DELIVER)) {//发货异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.DELIVER_EXCEPT + " = 0");
            itemExcep = itemExcep | TradeConstants.DELIVER_EXCEPT;
        }
        if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER)) {//上传异常
            q0.conjunct(" AND ", c++ > 0).append("t.v & 4 = 0");
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE)) {//套件数量修改异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE + " = 0");
            itemExcep = itemExcep | TradeConstants.SUITE_QUANTITY_CHANGE;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE)) {//普通商品转加工
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP + " = 0");
            itemExcep = itemExcep | TradeConstants.ITEM_PROCESS_EXCEP;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_AMBIGUITY + " = 0");
            excep = excep | TradeConstants.FX_AMBIGUITY;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_OUT)) {//缺货已处理异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_STOCK_OUT + " = 0");
            itemExcep = itemExcep | TradeConstants.PDD_STOCK_OUT;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_UNAUDIT + " = 0");
            excep = excep | TradeConstants.FX_UNAUDIT;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_WAITPAY + " = 0");
            excep = excep | TradeConstants.FX_WAITPAY;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_REPULSE_FX)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_REPULSE + " = 0");
            excep = excep | TradeConstants.FX_REPULSE;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT + " = 0");
            excep = excep | TradeConstants.PLATFORM_FX_SPLIT_EXCEPT;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMCS_STOCK_OUT + " = 0");
            excep = excep | TradeConstants.TMCS_STOCK_OUT;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMGJZY_STOCK_OUT + " = 0");
            excep = excep | TradeConstants.TMGJZY_STOCK_OUT;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE + " = 0");
            excep = excep | TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE;
        }
        // 唯品会未匹配常态合作码
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH + " = 0");
            excep = excep | TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH;
        }
        // 速卖通全托管 未接单
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION + " = 0");
            excep = excep | TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION;
        }
        //平台仓未匹配
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION + " = 0");
            excep = excep | TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_COD_REPEAT)) {//货到付款订单重复异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.COD_REPEAT + " = 0");
            itemExcep = itemExcep | TradeConstants.COD_REPEAT;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAIT_MERGE)) {//等待合并异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.WAIT_MERGE + " = 0");
            itemExcep = itemExcep | TradeConstants.WAIT_MERGE;
        }

        if (!containExceps.contains(TradeQueryParams.STATUS_WAITING_RETURN_WMS)) {//等待退货入仓异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.WATING_RETURN_WMS_EXCEPT + " = 0");
            itemExcep = itemExcep | TradeConstants.WATING_RETURN_WMS_EXCEPT;
        }
        if (!containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF)) {//商品停用异常
            // q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_SHUTOFF + " = 0");
            itemExcep = itemExcep | TradeConstants.ITEM_SHUTOFF;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_REFUND_ITEM_NUM_EXCEPT)) {//商品数量退款异常
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_PART_PAY_EXCEPT)) {//部分付款
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PART_PAY_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.PART_PAY_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_ONLINE_LOCK_EXCEPT)) {//线上锁定
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.ONLINE_LOCK_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_GX_ITEM_CHANGE_EXCEPT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_CAI_GOU_TRADE_EXCEPT)) {
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.CAI_GOU_TRADE_EXCEPT;
        }

        if (!containExceps.contains(ExceptConstantOld.EX_PLAT_MODIFY_ITEM_NUM_EXCEPT)) {//平台修改商品数量异常
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT;
        }

        if (!containExceps.contains(ExceptConstantOld.EX_PO_JIA_TRADE_EXCEPT)) {//平台修改商品数量异常
            // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.PO_JIA_TRADE_EXCEPT;

        }
        if (!containExceps.contains(ExceptConstantOld.EX_ONLINE_STATUS_EXCEPT)) {
           // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT).append(" = 0 ");
            excep = excep | ExceptConstantOld.ONLINE_STATUS_EXCEPT;
        }
        if (!containExceps.contains(ExceptConstantOld.EX_SMALL_REFUND_EXCEPT)) {
           // q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT).append(" = 0 ");
             excep = excep | ExceptConstantOld.SMALL_REFUND_EXCEPT;
        }
        if(excep>0){
            q0.conjunct(" AND ", c++ > 0).append("t.excep & " + excep).append(" = 0 ");
        }
        if(itemExcep>0){
            q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + itemExcep).append(" = 0 ");
        }

        return q0;
    }

    private Query buildExceptSql(Staff staff, String[] exceptions, Integer itemExcepOpen, Query q, TradeQueryParams params) {
        Query q1 = new Query();
        Query q0 = new Query();
        if (exceptions != null && exceptions.length > 0) {
            int c = 0;
            for (String s : exceptions) {
                if (TradeQueryParams.STATUS_EXCEP_HALT.equals(s)) {//异常状态挂起
                    q0.conjunct(" OR ", c++ > 0).append("t.is_halt = 1");
                } else if (TradeQueryParams.STATUS_EXCEP_REFUND.equals(s)) {//退款订单
                    q0.conjunct(" OR ", c++ > 0).append("t.is_refund = 1");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED.equals(s)) {//系统商品对应关系修改
                    if (itemExcepOpen != null && itemExcepOpen - 1 == 0) {
                        q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.RELATION_CHANGED).append(" > 0 ");
                    } else {
                        q0.conjunct(" OR ", c++ > 0).append("t.stock_status = ?").add(Trade.STOCK_STATUS_RELATION_MODIFIED);
                    }
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED.equals(s)) {//商品未匹配异常
                    if (itemExcepOpen != null && itemExcepOpen - 1 == 0) {
                        q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.UNALLOCATED).append(" > 0 ");
                    } else {
                        q0.conjunct(" OR ", c++ > 0).append("t.stock_status = ?").add(Trade.STOCK_STATUS_UNALLOCATED);
                    }
                } else if (TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT.equals(s)) {//库存不足
                    if (itemExcepOpen != null && itemExcepOpen - 1 == 0) {
                        q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.INSUFFICIENT).append(" > 0 ");
                    } else {
                        q0.conjunct(" OR ", c++ > 0).append("t.stock_status = ?").add(Trade.STOCK_STATUS_INSUFFICIENT);
                    }
                } else if (TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.address_changed = 1");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_CHANGED).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_BLACK.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.black_buyer_nick = 1");
                } else if (TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.seller_memo_update = 2");
                } else if (TradeQueryParams.STATUS_EXCEP_UNATTAINABLE.equals(s)) {//快递停发异常
                    q0.conjunct(" OR ", c++ > 0).append("t.unattainable  = 1");
                } else if (TradeQueryParams.STATUS_EXCEP_LOST_MSG.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.is_lost_msg = 1");
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.LOST_MESSAGE).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_PART_REFUND.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PART_REFUND).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_RISK_ORDER.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_RISKEXCEP).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_DELIVER.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.DELIVER_EXCEPT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.v & 4").append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX.equals(s)) {
                    //不明确供销商
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_AMBIGUITY).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX.equals(s)) {
                    //分销商反审核
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_UNAUDIT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX.equals(s)) {
                    //分销商未付款
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_WAITPAY).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_REPULSE_FX.equals(s)) {
                    //供销商打回
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.FX_REPULSE).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT.equals(s)) {
                    //外仓商品待拆分
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT.equals(s)) {
                    //天猫超市回告
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.TMCS_STOCK_OUT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING.equals(s)) {
                    //平台仓未匹配
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT.equals(s)) {
                    //天猫国际直营回告
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.TMGJZY_STOCK_OUT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE.equals(s)) {
                    //得物直发物流模板匹配
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH.equals(s)) {
                    //唯品会未匹配常态合作码
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH).append(" > 0 ");
                }else if (TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION.equals(s)) {
                    //速卖通全托管 未接单
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION).append(" > 0 ");
                }else if (TradeQueryParams.STATUS_EXCEP_STOCK_OUT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_STOCK_OUT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_WAITING_RETURN_WMS.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.WATING_RETURN_WMS_EXCEPT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_WAIT_MERGE.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.WAIT_MERGE).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_COD_REPEAT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.COD_REPEAT).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_SHUTOFF).append(" > 0 ");
                } else if (TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF).append(" > 0 ");
                }else if(ExceptConstantOld.EX_REFUND_ITEM_NUM_EXCEPT.equals(s)){
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT).append(" > 0 ");
                } else if(String.valueOf(ExceptEnum.OUTSID_RECOVERY_FAIL.getId()).equals(s)){
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL).append(" > 0 ");
                }else if (ExceptConstantOld.EX_PART_PAY_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PART_PAY_EXCEPT).append(" > 0 ");
                } else if(ExceptConstantOld.EX_PLAT_MODIFY_ITEM_NUM_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT).append(" > 0 ");
                } else if (ExceptConstantOld.EX_ONLINE_LOCK_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT).append(" > 0 ");
                } else if (ExceptConstantOld.EX_GX_ITEM_CHANGE_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT).append(" > 0 ");
                } else if(ExceptConstantOld.EX_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.equals(s)){
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT).append(" > 0 ");
                } else if (ExceptConstantOld.EX_CAI_GOU_TRADE_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT).append(" > 0 ");
                }else if (ExceptConstantOld.EX_PO_JIA_TRADE_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT).append(" > 0 ");
                }else if (ExceptConstantOld.EX_ONLINE_STATUS_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT).append(" > 0 ");
                }else if (ExceptConstantOld.EX_SMALL_REFUND_EXCEPT.equals(s)) {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT).append(" > 0 ");
                }else {
                    logger.info(LogHelper.buildLog(staff,"异常状态[exceptionStatus]参数错误：" + s));
                }
            }
        }
        Query sysExceptNotMergeContailSql = tradeNewExceptSqlQueryBuilder.getSysExceptNotMergeContailSql(staff, q, params);
        if(q0.getQ().length()>0||sysExceptNotMergeContailSql.getQ().length()>0){
            q1.append("(");
        }
        if(q0.getQ().length()>0){
            q1.append(q0.getQ());
        }
        if(sysExceptNotMergeContailSql.getQ().length()>0){
            q1.conjunct(" OR ",q0.getQ().length()>0).append(sysExceptNotMergeContailSql.getQ()).add(sysExceptNotMergeContailSql.getArgs());
        }
        if(q0.getQ().length()>0||sysExceptNotMergeContailSql.getQ().length()>0){
            q1.append(")");
        }
        return q1;
    }

    private Query buildExceptSqlAllContain(Staff staff,TradeQueryParams params,Query query, String[] exceptions, String[] exceptIds, boolean seed) {
        Query q0 = new Query();
        int c = 0;
        if (null != exceptions && exceptions.length > 0) {
            Set<String> containExceps = new HashSet<String>();
            for (String s : exceptions) {
                containExceps.add(s);
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_HALT)) {//异常状态挂起
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("is_halt = 1");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.is_halt = 1");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNATTAINABLE)) {//快递异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("unattainable = 1");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.unattainable = 1");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REFUND)) {//异常状态挂起
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("is_refund = 1");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.is_refund = 1");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED)) {//对应关系变动
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.RELATION_CHANGED + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.RELATION_CHANGED + " > 0");

                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED)) {//未分配
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.UNALLOCATED + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNALLOCATED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT)) {//缺货
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.INSUFFICIENT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.INSUFFICIENT + " > 0");

                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED)) {//地址变动异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.ADDRESS_CHANGED + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ADDRESS_CHANGED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED)) {//地址变动异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_CHANGED + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_CHANGED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_BLACK)) {//黑名单
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.BLACK_NICK + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.BLACK_NICK + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED)) {//备注异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.SELLER_MEMO_UPDATE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SELLER_MEMO_UPDATE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF)) {//唯一码下架
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_LOST_MSG)) {//信息确实
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.LOST_MESSAGE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.LOST_MESSAGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PART_REFUND)) {//部分退款异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.PART_REFUND + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PART_REFUND + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_RISK_ORDER)) {//风控异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.PDD_RISKEXCEP + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_RISKEXCEP + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_DELIVER)) {//发货异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.DELIVER_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.DELIVER_EXCEPT + " > 0");
                }
            }
/*        if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER)){//上传异常
            q0.conjunct(" AND ", c++ > 0).append("t.v & 4 = 0");
        }*/

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE)) {//套件数量修改异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE)) {//套件数量修改异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.FX_AMBIGUITY + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_AMBIGUITY + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.FX_UNAUDIT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_UNAUDIT + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.FX_WAITPAY + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_WAITPAY + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REPULSE_FX)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.FX_REPULSE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_REPULSE + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.TMCS_STOCK_OUT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMCS_STOCK_OUT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.TMGJZY_STOCK_OUT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMGJZY_STOCK_OUT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE + " > 0");
                }
            }
            //平台仓未匹配异常
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_COD_REPEAT)) {//货到付款订单重复异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.COD_REPEAT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.COD_REPEAT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAIT_MERGE)) {//等待合并异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.WAIT_MERGE + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.WAIT_MERGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_OUT)) {//商品停用异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.PDD_STOCK_OUT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_STOCK_OUT + " > 0");
                }
            }


            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF)) {//商品停用异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_SHUTOFF + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_SHUTOFF + " > 0");
                }
            }

            // 未匹配常态合作码异常
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_REFUND_ITEM_NUM_EXCEPT)) {//退款商品数量异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_PART_PAY_EXCEPT)) {//部分付款
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.PART_PAY_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PART_PAY_EXCEPT + " > 0");
                }
            }
            // 速卖通全托管
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_ONLINE_LOCK_EXCEPT)) {//部分付款
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_GX_ITEM_CHANGE_EXCEPT)) {//部分付款
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT + " > 0");
                }
            }

            if (containExceps.contains(ExceptConstantOld.EX_CAI_GOU_TRADE_EXCEPT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT + " > 0");
                }
            }

            if (containExceps.contains(ExceptConstantOld.EX_PLAT_MODIFY_ITEM_NUM_EXCEPT)) {//平台修改商品数量异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_PO_JIA_TRADE_EXCEPT)) {//平台修改商品数量异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_ONLINE_STATUS_EXCEPT)) {//平台修改商品数量异常
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_SMALL_REFUND_EXCEPT)) {
                if (seed) {
                    q0.conjunct(" AND ", c++ > 0).append("excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT + " > 0");
                } else {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT + " > 0");
                }
            }
        }
        // 系统异常，非合单，同时包含
        Query sysExceptNotMergeContainAllSql = tradeNewExceptSqlQueryBuilder.getSysExceptNotMergeContainAllSql(staff, query, params);
        // 系统异常，合单，同时包含
        Query sysExceptMergeContainAllSql = tradeNewExceptSqlQueryBuilder.getSysExceptMergeContainAllSql(staff, query, params);
        if(!seed){
            if(sysExceptNotMergeContainAllSql.getQ().length()>0){
                q0.conjunct( " AND ", q0.getQ().length() > 0).append(sysExceptNotMergeContainAllSql.toString());//.add(sysExceptNotMergeContainAllSql.getArgs());
            }
        }else{
            if(sysExceptMergeContainAllSql.getQ().length()>0){
                q0.conjunct( " AND ", q0.getQ().length() > 0).append(sysExceptMergeContainAllSql.toString());//.add(sysExceptMergeContainAllSql.getArgs());
            }
        }
        if (null != exceptIds && exceptIds.length > 0) {
            q0.and().append(" (( ");
            Query  q=new Query();
            if (!seed) {
                for (String exceptId : exceptIds) {
                   // q0.conjunct(" AND ", c++ > 0).append(" LOCATE( '" + exceptId + "', `except_ids` ) > 0  ");
                    q.conjunct(" AND ", q.getQ().length() > 0).append(" LOCATE( '" + exceptId + "', `except_ids` ) > 0  ");
                }
            } else {
                for (String exceptId : exceptIds) {
                   // q0.conjunct(" AND ", c++ > 0).append(" LOCATE( '" + exceptId + "', t.`except_ids` ) > 0  ");
                    q.conjunct(" AND ", q.getQ().length() > 0).append(" LOCATE( '" + exceptId + "', `except_ids` ) > 0  ");
                }
            }
            if(q.getQ().length()>0){
                q0.append(q.getQ());
            }
            q0.append(")");
            Query selfExceptMergeContainAllSql = tradeNewExceptSqlQueryBuilder.getSelfExceptMergeContainAllSql(staff, q, params);
            Query selfExceptNotMergeContainAllSql = tradeNewExceptSqlQueryBuilder.getSelfExceptNotMergeContainAllSql(staff, q, params);
            if(!seed){
                if(selfExceptNotMergeContainAllSql.getQ().length()>0){
                    q0.conjunct( " OR ", q0.getQ().length() > 0).append(selfExceptNotMergeContainAllSql.toString());//.add(selfExceptNotMergeContainAllSql.getArgs());
                }

            }else{
                if(selfExceptMergeContainAllSql.getQ().length()>0){
                    q0.conjunct( " OR ", q0.getQ().length() > 0).append(selfExceptMergeContainAllSql.toString());//.add(selfExceptMergeContainAllSql.getArgs());
                }
            }
            q0.append(")");
        }
        return q0;
    }


    private Query buildExceptSqlExclude(Staff staff,Query q,TradeQueryParams params, boolean isMergeExclude, boolean primaryOnlyContain) {
        String[] exceptIds = params.getExceptIds();
        String[] exceptions = params.getExceptionStatus();
        Query q0 = new Query();
        if (exceptions != null && exceptions.length > 0) {
            Set<String> containExceps = new HashSet<String>();
            for (String s : exceptions) {
                containExceps.add(s);
            }
            int c = 0;
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_HALT)) {//异常状态挂起
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.is_halt != 1");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("is_halt = 1");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNATTAINABLE)) {//快递异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.unattainable != 1");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("unattainable = 1");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REFUND)) {//异常状态挂起
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.is_refund != 1");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("is_refund = 1");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED)) {//对应关系变动
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.RELATION_CHANGED + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.RELATION_CHANGED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED)) {//未分配
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNALLOCATED + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.UNALLOCATED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT)) {//缺货
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.INSUFFICIENT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.INSUFFICIENT + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED)) {//地址变动异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ADDRESS_CHANGED + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.ADDRESS_CHANGED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED)) {//地址变动异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_CHANGED + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_CHANGED + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_BLACK)) {//黑名单
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.BLACK_NICK + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.BLACK_NICK + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED)) {//备注异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SELLER_MEMO_UPDATE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.SELLER_MEMO_UPDATE + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF)) {//唯一码下架
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.UNIQUE_CODE_OFFSHELF + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_LOST_MSG)) {//信息确实
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.LOST_MESSAGE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.LOST_MESSAGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PART_REFUND)) {//部分退款异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PART_REFUND + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.PART_REFUND + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_RISK_ORDER)) {//风控异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_RISKEXCEP + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.PDD_RISKEXCEP + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_DELIVER)) {//发货异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.DELIVER_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.DELIVER_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER)) {//上传异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.v & 4 = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("v & 4 > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE)) {//套件数量修改异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.SUITE_QUANTITY_CHANGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE)) {//套件数量修改异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_PROCESS_EXCEP + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_COD_REPEAT)) {//套件数量修改异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.COD_REPEAT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.COD_REPEAT + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAIT_MERGE)) {//等待合并异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.WAIT_MERGE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.WAIT_MERGE + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX)) {//不明确供销商
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_AMBIGUITY + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.FX_AMBIGUITY + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX)) {//反审核异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_UNAUDIT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.FX_UNAUDIT + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX)) {//待付款异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_WAITPAY + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.FX_WAITPAY + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_REPULSE_FX)) {//供销商打回
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.FX_REPULSE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.FX_REPULSE + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT)) {//外仓商品待拆分
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.PLATFORM_FX_SPLIT_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT)) {//天猫超市缺货回告
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMCS_STOCK_OUT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.TMCS_STOCK_OUT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT)) {//天猫国际直营缺货回告
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.TMGJZY_STOCK_OUT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.TMGJZY_STOCK_OUT + " > 0");
                }
            }
            //得物直发物流匹配
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE + " > 0");
                }
            }
            //平台仓未匹配
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING)) {//天猫国际直营缺货回告
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_STOCK_OUT)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.PDD_STOCK_OUT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.PDD_STOCK_OUT + " > 0");
                }
            }
            if (containExceps.contains(TradeQueryParams.STATUS_WAITING_RETURN_WMS)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.WATING_RETURN_WMS_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.WATING_RETURN_WMS_EXCEPT + " > 0");
                }
            }

            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.item_excep & " + TradeConstants.ITEM_SHUTOFF + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("item_excep & " + TradeConstants.ITEM_SHUTOFF + " > 0");
                }
            }

            // 唯品会未匹配常态合作码
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH + " > 0");
                }
            }

            if (containExceps.contains(ExceptConstantOld.EX_REFUND_ITEM_NUM_EXCEPT)) {//退款商品数量异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + ExceptConstantOld.REFUND_ITEM_NUM_EXCEPT + " > 0");
                }
            }
            // 单号回收失败异常
            if (containExceps.contains(String.valueOf(ExceptEnum.OUTSID_RECOVERY_FAIL.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL + " > 0");
                }
            }
            // 速卖通全托管
            if (containExceps.contains(TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION + " > 0");
                }
            }
            // 线上锁定
            if (containExceps.contains(String.valueOf(ExceptEnum.ONLINE_LOCK.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("excep & " + ExceptConstantOld.ONLINE_LOCK_EXCEPT + " > 0");
                }
            }

            if (containExceps.contains(String.valueOf(ExceptEnum.GX_ITEM_CHANGE_EXCEPT.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.GX_ITEM_CHANGE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(String.valueOf(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT + " > 0");
                }
            }

            if (containExceps.contains(String.valueOf(ExceptEnum.CAI_GOU_TRADE_EXCEPT.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.CAI_GOU_TRADE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_PLAT_MODIFY_ITEM_NUM_EXCEPT)) {//平台修改商品数量异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT + " > 0");
                }
            }
            // 破价异常
            if (containExceps.contains(String.valueOf(ExceptEnum.PO_JIA_EXCEPT.getId()))) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.PO_JIA_TRADE_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_ONLINE_STATUS_EXCEPT)) {//平台修改商品数量异常
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.ONLINE_STATUS_EXCEPT + " > 0");
                }
            }
            if (containExceps.contains(ExceptConstantOld.EX_SMALL_REFUND_EXCEPT)) {
                if (isMergeExclude) {
                    q0.conjunct(" AND ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT + " = 0");
                } else {
                    q0.conjunct(" OR ", c++ > 0).append("t.excep & " + ExceptConstantOld.SMALL_REFUND_EXCEPT + " > 0");
                }
            }
        }
        // 系统异常
        if (isMergeExclude) {
            // 非合单排除
            Query sysExceptNotMergeExcludeSql = tradeNewExceptSqlQueryBuilder.getSysExceptNotMergeExcludeSql(staff, q, params);
            if (sysExceptNotMergeExcludeSql.getQ().length() > 0) {
                q0.conjunct(" and ", q0.getQ().length() > 0).append(sysExceptNotMergeExcludeSql.getQ()).add(sysExceptNotMergeExcludeSql.getArgs());
            }
        } else {
            // 合单排除, 内部包含
            Query sysExceptMergeExcludeSql = tradeNewExceptSqlQueryBuilder.getSysExceptMergeContailSql(staff, q, params);
            if (sysExceptMergeExcludeSql.getQ().length() > 0) {
                q0.conjunct(" OR ", q0.getQ().length() > 0).append(sysExceptMergeExcludeSql.getQ()).add(sysExceptMergeExcludeSql.getArgs());
            }
        }
        if (exceptIds != null && exceptIds.length > 0) {//先判空下，智能审核原先设置了-1，现在设置了null @贾凯乐
            Query tempQuery = new Query();
            for (String exceptId : exceptIds) {
                if (isMergeExclude) {
                    tempQuery.conjunct(" AND ", tempQuery.getQ().length() > 0).append(" LOCATE( '" + exceptId + "', `except_ids` ) = 0  ");
                } else {
                    tempQuery.conjunct(" OR ", tempQuery.getQ().length() > 0).append(" LOCATE( '" + exceptId + "', `except_ids` ) > 0  ");
                }
            }
            if (tempQuery.getQ().length() > 0 && isMergeExclude) {
                q0.conjunct(" AND ", q0.getQ().length() > 0).append(tempQuery.getQ());
            }
            if (tempQuery.getQ().length() > 0 && !isMergeExclude) {
                q0.conjunct(" OR ", q0.getQ().length() > 0).append(tempQuery.getQ()).append("");
            }

            if (params.getTickExcep() - 1 == 0 && null != params.getOnlyContain() && !primaryOnlyContain) {
                q0.and().append("is_excep = 1");
            }
        }
        return q0;
    }

    @Override
    public Query buildWarehouseQuery(Query q, final Staff staff, Integer warehouseType, final Long... warehouseIds) {
        boolean selected = warehouseIds != null && warehouseIds.length > 0;
        int c = 0;//仓库ID数
        if (staff.isDefaultStaff()) {//管理员
            if (warehouseType != null && warehouseType == 0) {//只查询自有仓库订单
                List<Warehouse> warehouses = warehouseService.queryWarehouseByPower(staff, 1);
                checkException("亲，您当前没有任何自有仓库哦！", warehouses.isEmpty());
                if (selected) {//在用户选择的仓库中筛选出自有仓库
                    for (Long warehouseId : warehouseIds) {
                        for (Warehouse warehouse : warehouses) {
                            if (warehouse.getId() - warehouseId == 0) {
                                c++;
                                q.add(warehouseId);
                                break;
                            }
                        }
                    }
                    checkException("亲，请选择您的自有仓库哦", c == 0);
                } else {//没有选择仓库时查询所有自有仓库订单
                    for (Warehouse warehouse : warehouses) {
                        q.add(warehouse.getId());
                        c++;
                    }
                }
            } else if (warehouseType != null && warehouseType == 1) {//只查询第三方仓库订单
                List<Warehouse> warehouses = warehouseService.queryAll(staff.getCompanyId(), 1);
                if (selected) {//在用户选择的仓库中筛选出第三方仓库
                    for (Long warehouseId : warehouseIds) {
                        for (Warehouse warehouse : warehouses) {
                            if (warehouse.getId() - warehouseId == 0 && warehouse.getType() > 0) {
                                c++;
                                q.add(warehouseId);
                                break;
                            }
                        }
                    }
                    checkException("亲，请选择您的第三方仓库哦", c == 0);
                } else {//没有选择仓库时查询所有第三方仓库订单
                    for (Warehouse warehouse : warehouses) {
                        if (warehouse.getType() > 0) {
                            c++;
                            q.add(warehouse.getId());
                        }
                    }
                    checkException("亲，您当前没有第三方仓库哦", c == 0);
                }
            } else {//既查询自有仓库订单也查询第三方仓库订单
                if (selected) {
                    for (Long warehouseId : warehouseIds) {
                        q.add(warehouseId);
                        c++;
                    }
                }
                //else 管理员没有选择仓库，默认查询所有仓库订单
            }
        } else {//非管理员
            if (warehouseType != null && warehouseType == 0) {
                if (selected) {//在用户选择的仓库中筛选出有权限的自有仓库
                    for (Long warehouseId : warehouseIds) {
                        if (Warehouse.isWarehouseSys(staff, warehouseId)) {
                            q.add(warehouseId);
                            c++;
                        }
                    }
                    if (c == 0) {
                        wrapperWarehouseException(staff,warehouseIds);
                    }
                } else {//没有选择仓库时查询所有有权限的自有仓库的订单
                    c += extractWarehouseIds(q, staff.getWarehouseGroup());
                    checkException("亲，您没有任何自有仓库权限哦！", c == 0);
                }
            } else if (warehouseType != null && warehouseType == 1) {
                if (selected) {//在用户选择的仓库中筛选出有权限的第三方仓库
                    for (Long warehouseId : warehouseIds) {
                        if (isParty3Warehouse(staff, warehouseId)) {
                            q.add(warehouseId);
                            c++;
                        }
                    }
                    if (c == 0) {
                        wrapperWarehouseException(staff,warehouseIds);
                    }
                } else {//没有选择仓库时查询所有有权限的第三方仓库的订单
                    c += extractWarehouseIds(q, staff.getWarehouseExtraGroup());
                    checkException("亲，您没有任何第三方仓库权限哦！", c == 0);
                }
            } else {//既查询自有仓库订单也查询第三方仓库订单
                List<Long> allWarehouseIds = extractAllWarehouseIds(staff);
                checkException("亲，您当前没有任何仓库权限哦！", allWarehouseIds.isEmpty());
                if (selected) {//在用户选择的仓库中筛选出有权限的仓库（包括自有仓库和第三方仓库）
                    for (Long warehouseId : warehouseIds) {
                        if (allWarehouseIds.contains(warehouseId)) {
                            q.add(warehouseId);
                            c++;
                        }
                    }
                    if (c == 0) {
                        wrapperWarehouseException(staff,warehouseIds);
                    }
                } else {//没有选择仓库时查询所有有权限的仓库（包括自有仓库和第三方仓库）的订单
                    for (Long warehouseId : allWarehouseIds) {
                        q.add(warehouseId);
                        c++;
                    }
                }
            }
        }
        if (c > 0) {
            q.and().append("t.warehouse_id IN(");
            for (int i = 0; i < c; i++) {
                q.append(i > 0 ? ", ?" : "?");
            }
            q.append(")");
        }
        return q;
    }

    private void wrapperWarehouseException(Staff staff, Long ... warehouseIds){
        List<Warehouse> warehouses = warehouseService.queryAll(staff.getCompanyId(), 1);
        if (warehouses == null) {
            warehouses = Collections.emptyList();
        }
        StringBuilder s = new StringBuilder("亲，当前查询条件中包含");
        Map<Long, Warehouse> warehouseMap = warehouses.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        for (Long warehouseId : warehouseIds) {
            Warehouse warehouse = warehouseMap.get(warehouseId);
            s.append(warehouse == null? warehouseId : warehouse.getName()).append(",");
        }
        s = s.deleteCharAt(s.length() - 1);
        s.append("仓库，但您没有所选仓库的权限。请联系主账号进行处理");
        Assert.isTrue(false,s.toString());
    }


    private int extractWarehouseIds(Query q, String warehouseIds) {
        int c = 0;
        if (warehouseIds != null) {
            String[] widArr = warehouseIds.split(",");
            for (String wid : widArr) {
                if ((wid = wid.trim()).matches("\\d+")) {
                    q.add(Long.parseLong(wid));
                    c++;
                }
            }
        }
        return c;
    }

    private List<Long> extractAllWarehouseIds(Staff staff) {
        List<Long> list = new ArrayList<Long>();
        String allWarehouseIds = staff.getAllWarehouseGroup();
        if (allWarehouseIds != null && allWarehouseIds.length() > 0) {
            String[] widArr = allWarehouseIds.split(",");
            for (String wid : widArr) {
                if ((wid = wid.trim()).matches("\\d+")) {
                    list.add(Long.valueOf(wid));
                }
            }
        }
        return list;
    }

    private boolean isParty3Warehouse(Staff staff, Long warehouseId) {
        return (Staff.isWarehouseExtra(staff, warehouseId) || Staff.isWarehouseStore(staff, warehouseId));
    }

    @Override
    public Query buildDateRangeQuery(Query q, String key, Date start, Date end) {
        if (start != null || end != null) {
            q.and();
            if (start != null) {
                q.append(key).append(" >= ?").add(start);
            }
            if (end != null) {
                if (start != null) {
                    q.append(" AND ");
                }
                q.append(key).append(" <= ?").add(end);
            }
        }
        return q;
    }

    @Override
    public Query buildMixKeyQuery(Query q, Staff staff,TradeQueryParams params) {
        String mixKey = params.getMixKey();
        mixKey = mixKey == null ? "" : mixKey.trim();
        if (mixKey.contains("_") && mixKey.substring(mixKey.length() - 2, mixKey.length() - 1).equals("_")) {
            String[] mixIds = new String[2];
            mixIds[0] = mixKey.substring(0, mixKey.length() - 2);
            mixIds[1] = mixKey.substring(mixKey.length() - 1);
            Integer flag = Strings.getAsInt(mixIds[1],false);
            if (Objects.equals(flag,1)) {//根据sid查询
                q.and().append("t.sid = ? ").add(StringUtils.isNumeric(mixIds[0])?Long.parseLong(mixIds[0]):-1);
            } else if (Objects.equals(flag,2)) {//根据shortId查询
                String idStr = StringUtils.equals("0", mixIds[0]) ? "-1" : mixIds[0];
                Long id = StringUtils.isNumeric(idStr)?Long.parseLong(idStr):-1;
                q.and().append("t.short_id = ? ").add(id);
            } else if (Objects.equals(flag,3)) {//根据outSid查询
                String outSid = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(),mixIds[0]);
                Set<Long> sidsByMultiPacks = getSidsByMultiPacks(staff, outSid);
                q.and().append("( t.out_sid = ? ").add(outSid);
                if (CollectionUtils.isNotEmpty(sidsByMultiPacks)) {
                    q.or().append(" t.sid in (").append(sidsByMultiPacks.stream().map(t -> "?").collect(Collectors.joining(","))).append(" )").add(sidsByMultiPacks);
                }
                q.append(") ");
            } else {
                q.and().append("t.sid = 0 ");
            }
            return q;
        }
        if (!mixKey.isEmpty()) {
            Long queryId = params.getQueryId();
            if (queryId != null &&
                    (queryId == QUERY_UN_CONSIGNED || queryId == QUERY_PRINT_V2 || QUERY_RAPID_PRINT.equals(queryId) || queryId == QUERY_WAIT_AUDIT ||
                            queryId.equals(QUERY_SELLER_SEND_GOODS) || queryId.equals(QUERY_CANCEL) || queryId.equals(QUERY_WAIT_CONSIGN) )) {

                if (mixKey.length() < 10 && StringUtils.isNumeric(mixKey) && Long.parseLong(mixKey) < *********L) {
                    if(tradeLocalConfig.isTradeSearchShortIdFourInOne(staff.getCompanyId())){
                        buildMixKeyShortIdFourInOneQuery(staff,q,Long.parseLong(mixKey));
                    }else {
                        q.and().append(" t.short_id = ?").add(Long.parseLong(mixKey));
                    }

                } else {

                    List<String> tids = new ArrayList<>();
                    tids.add(mixKey);
                    Map<String, List<Long>> userSourceMap = params.getContext().getUserSourceMap();
                    //奇门订单的tid实际存储的是上游系统的系统id而非平台本身的id 这里需要先将平台原始的id转换为上游的系统id

                    List<String> inTids = new ArrayList<>(tids);
                    List<String> likeTids = new ArrayList<>(tids);
                    addFxTids(staff, userSourceMap, qimenTradeDubboService,fxSupplierSrcService, tids,inTids,likeTids);
                    q.and().append("(");

                    q.append(" t.tid in (").append(inTids.stream().map(t -> "?").collect(Collectors.joining(","))).append(" )").add(inTids);
                    if (CollectionUtils.isNotEmpty(likeTids)) {
                        for (int i = 0; i < likeTids.size(); i++) {
                            q.or().append(" t.tid like ? ").add(likeTids.get(i) + "%");
                        }
                    }

                    //如果条件要求没有分配运单号 那么out_sid不为空的记录一定是不满足的 这个OR条件就可以不加
                    if (null == params.getContainOutsid() || params.getContainOutsid()) {
                        String outSid = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(),mixKey);
                        q.append(" OR t.out_sid = ? ").add(outSid);

                        Set<Long> sidsByMultiPacks = getSidsByMultiPacks(staff, mixKey);
                        if (CollectionUtils.isNotEmpty(sidsByMultiPacks)) {
                            q.or().append(" t.sid in (").append(sidsByMultiPacks.stream().map(t -> "?").collect(Collectors.joining(","))).append(" )").add(sidsByMultiPacks);
                        }
                    }

                    q.append(" OR t.sid = ? )").add(mixKey);
                }
                // 订单管理页面，四合一查询
//                orderQry.and().append("(t.tid = ? OR t.out_sid = ? ").add(mixKey).add(mixKey);
//                if (mixKey.length() < 15) {
//                    if (mixKey.length() < 10 && StringUtils.isNumeric(mixKey)) {
//                        orderQry.append("OR t.short_id = ?").add(Long.parseLong(mixKey));
//                    }
//                } else {
//                    orderQry.append("OR t.sid = ?").add(mixKey);
//                }
//                orderQry.append(")");
            } else {
                // 原有逻辑
                boolean isNumSid = StringUtils.isNumeric(mixKey) && mixKey.length() >= 15 && mixKey.length() < 19;//可能是系统订单号sid
                q.and().conjunct("(", isNumSid);
                if (isNumSid) {
                    q.append("t.sid = ? OR ").add(Long.parseLong(mixKey));
                }
                String outSid = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(),mixKey);
                q.append("t.out_sid = ?").conjunct(")", isNumSid).add(outSid);
            }
        }
        return q;
    }

    public Set<Long> getSidsByMultiPacks(Staff staff, String ... outSids){
        Set<Long> outSidsRelSidSet = Sets.newHashSet();
        if (outSids != null && outSids.length > 0) {
            //开启白名单后，快递单号查询时，如果单号是子母单，且是拆分订单，则只查询出对应订单，不查出所有
            boolean exactSearchForSubShipment = featureService.checkHasFeatureByCode(staff.getCompanyId(), "ExactSearchForSubShipment");
            //依据运单号获取一单多包详情
            outSidsRelSidSet = multiPacksPrintTradeLogService.getSidsByMultiPacks(staff,exactSearchForSubShipment, Arrays.asList(outSids));
        }
        return outSidsRelSidSet;
    }

    /**
     *
         AND ( t.sid IN
         (SELECT trade_short_id_temp.sid
         FROM
         (SELECT t_id_temp.sid
         FROM trade_92 t_id_temp
         WHERE t_id_temp.company_id = 50092
         AND t_id_temp.enable_status > 0
         AND t_id_temp.tid IN('123') or t_id_temp.tid like '123-%'
         UNION ALL
         SELECT short_id_temp.sid
         FROM trade_92 short_id_temp
         WHERE short_id_temp.company_id = 50092
         AND short_id_temp.enable_status > 0
         AND short_id_temp.short_id=123
         )
         AS trade_short_id_temp))

     * @param staff
     * @param q
     * @param shortId
     * @return
     */
    private Query buildMixKeyShortIdFourInOneQuery(Staff staff, Query q, Long shortId) {
        if(Objects.isNull(shortId)){
            return q;
        }

        List<Long> shortIds = new ArrayList<>();
        shortIds.add(shortId);
        List<String> tids = Lists.newArrayList(String.valueOf(shortId));

        q.and().append("( t.sid in (select short_id_temp.sid from (");
        Query shortIdTempQuery = buildTradeTempQuery(staff, "short_id", shortIds);
        Query tidTempQuery = buildTradeTidTempQuery(staff, "tid", tids,String.valueOf(shortId));
        q.append(shortIdTempQuery.getQ()).add(shortIdTempQuery.getArgs())
                .append(" UNION ALL ")
                .append(tidTempQuery.getQ()).add(tidTempQuery.getArgs());
        q.append(") as short_id_temp))");

        return q;
    }

    private <T> Query  buildTradeTidTempQuery(Staff staff, String key, List<T> list,String v){
        Query q = new Query();
        if (CollectionUtils.isNotEmpty(list)) {
            String collect = list.stream().map(a -> "?").collect(Collectors.joining(","));
            String tableName = key +"_temp";
            String sql = " SELECT "
                    +tableName+
                    ".sid FROM trade_" + staff.getDbInfo().getTradeDbNo() +" "
                    +tableName+
                    " WHERE "+tableName+".company_id =  " + staff.getCompanyId() +
                    " AND "+tableName+".enable_status > 0 AND ("+tableName+"."+key+" IN("
                    + collect
                    + ") OR "+tableName+"."+key+" LIKE "+" CONCAT(?, '-%'))";
            q.append(sql);
            q.add(list).add(v);
        }
        return q;
    }

    @Override
    public Query buildTradeTypeQuery(Staff staff, Query q, Long queryId, Integer[] tradeTypes, Integer[] excludeTradeTypes, TradeTypeNewParams tradeTypeNewParams) {
        if ((tradeTypes == null || tradeTypes.length == 0) && (excludeTradeTypes == null || excludeTradeTypes.length == 0) && tradeTypeNewParams == null) {
            return q;
        }

        //新的类型查询
        StringBuilder newTq = new StringBuilder();
        StringBuilder newEtq = new StringBuilder();
        if (tradeTypeNewParams != null) {
            newTq = buildTradeTypeNewSql(staff, tradeTypeNewParams.getContainTypeIds());
            newEtq = buildTradeTypeNewSql(staff, tradeTypeNewParams.getNotContainTypeIds());
        }

        boolean containsSysAndPlat = containsSysAndPlatform(tradeTypes, excludeTradeTypes);//判断是否同时查询线下订单和平台订单，如果是，则相应条件的不用拼接
        StringBuilder tq = new StringBuilder();
        StringBuilder etq = new StringBuilder();
        int validNum = 0;
        int excludeValidNum = 0;
        boolean containPlatformTrade = false;
        for (Integer tradeType : tradeTypes) {
            if (Objects.equals(tradeType, 3)) {
                containPlatformTrade = true;
            }
            if (tradeType == null || (containsSysAndPlat && (tradeType == 3 || tradeType == 4))) {
                continue;
            }
            String typeQuery = getTypeQuery(staff, q, tradeType, queryId);
            if (!typeQuery.isEmpty()) {
                Query.conjunct(tq, " OR ", validNum++ > 0);
                tq.append("(").append(typeQuery).append(")");
            }
        }
        //这里拼接在老的后面
        if (!newTq.toString().isEmpty()) {
            Query.conjunct(tq, " OR ", validNum++ > 0);
            tq.append(newTq);
        }

        for (Integer excludeTradeType : excludeTradeTypes) {
            if (excludeTradeType == null || (containsSysAndPlat && (excludeTradeType == 3 || excludeTradeType == 4))) {
                continue;
            }
            String typeQuery = getTypeQuery(staff, q, excludeTradeType, queryId);
            if (!typeQuery.isEmpty()) {
                Query.conjunct(etq, " OR ", excludeValidNum++ > 0);
                etq.append("(").append(typeQuery).append(")");
            }
        }
        //这里拼接在老的后面
        if (!newEtq.toString().isEmpty()) {
            Query.conjunct(etq, " OR ", excludeValidNum++ > 0);
            etq.append(newEtq);
        }

        if (validNum > 0) {
            if (containPlatformTrade && tq.toString().contains("t.type")) {//兼容type为空的平台单
                tq.append(" OR t.type = ''");
                validNum++;
            }
            if (validNum > 1) {
                q.and().append("(").append(tq).append(")");
            } else if (validNum == 1) {
                q.and().append(tq);
            }
        }
        if (excludeValidNum > 0) {
            q.and().append(" NOT (").append(etq).append(")");
        }
        return q;
    }

    /**
     * 商品标签查询
     */
    @Override
    public Query buildItemTagIdQuery(Staff staff, Query q, TradeQueryItemTagIdsParams tradeQueryItemTagIdsParams) {
        if (tradeQueryItemTagIdsParams == null || (
                CollectionUtils.isEmpty(tradeQueryItemTagIdsParams.getContainItemTagIds()) &&
                        CollectionUtils.isEmpty(tradeQueryItemTagIdsParams.getNotContainItemTagIds()))) {
            return q;
        }

        //包含 这里需要考虑合单
        if (CollectionUtils.isNotEmpty(tradeQueryItemTagIdsParams.getContainItemTagIds())) {
            StringBuilder contain = new StringBuilder();
            String inSql = Joiner.on(", ").join(tradeQueryItemTagIdsParams.getContainItemTagIds());

            //IF (t.merge_sid > 0, 合单sql, 非合单sql)
            contain.append(" IF (t.merge_sid > 0,").
                    append(" EXISTS (SELECT 1 FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" AS t1 WHERE t1.company_id = ").
                    append(staff.getCompanyId()).append(" AND t1.merge_sid > 0 AND t1.merge_sid = t.merge_sid AND EXISTS (SELECT oit1.sid FROM order_item_tag_").append(staff.getDbInfo().getOrderDbNo()).
                    append(" AS oit1 WHERE oit1.company_id = ").append(staff.getCompanyId()).append(" AND oit1.sid > 0 AND oit1.sid = t1.sid AND oit1.item_tag_id IN (").append(inSql).append(") AND oit1.enable_status = 1))").
                    append(",").
                    append(" EXISTS (SELECT oit2.sid FROM order_item_tag_").append(staff.getDbInfo().getOrderDbNo()).append(" AS oit2 WHERE oit2.company_id = ").
                    append(staff.getCompanyId()).append(" AND oit2.sid > 0 AND oit2.sid = t.sid AND oit2.item_tag_id IN (").append(inSql).append(") AND oit2.enable_status = 1))");

            q.and().append(contain);
        }

        //排除 这里需要考虑合单
        if (CollectionUtils.isNotEmpty(tradeQueryItemTagIdsParams.getNotContainItemTagIds())) {
            StringBuilder notContain = new StringBuilder();
            String inSql = Joiner.on(", ").join(tradeQueryItemTagIdsParams.getNotContainItemTagIds());

            //IF (t.merge_sid > 0, 合单sql, 非合单sql)
            notContain.append(" IF (t.merge_sid > 0,").
                    append(" NOT EXISTS (SELECT 1 FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" AS t2 WHERE t2.company_id = ").
                    append(staff.getCompanyId()).append(" AND t2.merge_sid > 0 AND t2.merge_sid = t.merge_sid AND EXISTS (SELECT oit3.sid FROM order_item_tag_").append(staff.getDbInfo().getOrderDbNo()).
                    append(" AS oit3 WHERE oit3.company_id = ").append(staff.getCompanyId()).append(" AND oit3.sid > 0 AND oit3.sid = t2.sid AND oit3.item_tag_id IN (").append(inSql).append(") AND oit3.enable_status = 1))").
                    append(",").
                    append(" NOT EXISTS (SELECT oit4.sid FROM order_item_tag_").append(staff.getDbInfo().getOrderDbNo()).append(" AS oit4 WHERE oit4.company_id = ").
                    append(staff.getCompanyId()).append(" AND oit4.sid > 0 AND oit4.sid = t.sid AND oit4.item_tag_id IN (").append(inSql).append(") AND oit4.enable_status = 1))");

            q.and().append(notContain);
        }

        return q;
    }

    @Override
    public StringBuilder buildTradeTypeNewSql(Staff staff, List<Long> typeIds) {
        if (typeIds == null || typeIds.isEmpty()) {
            return new StringBuilder("");
        }

        StringBuilder sb = new StringBuilder("EXISTS ( SELECT sid FROM trade_type_");
        sb.append(staff.getDbInfo().getTradeDbNo()).append(" AS tradeType WHERE t.company_id = tradeType.company_id AND t.sid = tradeType.sid AND type_id IN ( ");
        sb.append(Joiner.on(", ").join(typeIds)).append(" ) )");

        return sb;
    }

    /**
     *
     * @param q
     * @param areas 江西省@九江市@新余市$渝水区$分宜县@鹰潭市@赣州市@吉安市@宜春市@抚州市@上饶市@萍乡市$莲花县$上栗县@南昌市$湾里区$青山湖区$南昌县$新建区,湖南省@长沙市$天心区$岳麓区$开福区$长沙县$望城区$宁乡市$浏阳市$其它区@株洲市$荷塘区$石峰区$天元区$株洲县$攸县$茶陵县$炎陵县$醴陵市$其它区$渌口区
     * @return
     */
    @Override
    public Query buildAreaQuery(Query q, String areas) {
        if (areas == null || areas.isEmpty()) {
            return q;
        }
        StringTokenizer at = new StringTokenizer(areas, ",");
        StringBuilder tq = new StringBuilder();
        int pn = 0;
        while (at.hasMoreTokens()) {
            String area = at.nextToken().trim();
            if (area.isEmpty()) {
                continue;
            }
            String[] countrys = area.split("-");
            if (countrys.length == 2 && countrys[0].equals("")) {
                //是国外地址 -Italy
                Query.conjunct(tq, " OR ", pn++ > 0);
                tq.append("t.receiver_country LIKE CONCAT(?, '%')");
                q.add(countrys[1]);
                continue;
            }

            //是国内地址，肯定有省，可能有市区
            String province;
            if (countrys.length == 1) {
                //浙江省@杭州市$西湖区,江西省@抚州市$乐安县
                province = countrys[0];
            } else {
                //中国%浙江省@杭州市$西湖区,中国%江西省@抚州市$乐安县
                province = countrys[1];
            }
            StringTokenizer pct = new StringTokenizer(province, "@");
            int pcNum = pct.countTokens(), cn = 0;
            Query.conjunct(tq, " OR ", pn++ > 0);
            Query.conjunct(tq, "(", pcNum >= 2);
            tq.append("t.receiver_state LIKE CONCAT(?, '%')");
            String prov = handleArea(pct.nextToken().trim(), 0, null);
            q.add(prov);
            if (pcNum >= 2) {
                tq.append(pcNum > 2 ? " AND (" : " AND ");
            }
            while (pct.hasMoreTokens()) {
                StringTokenizer cdt = new StringTokenizer(pct.nextToken().trim(), "$");
                int cdNum = cdt.countTokens(), dn = 0;
                Query.conjunct(tq, " OR ", cn++ > 0);
                Query.conjunct(tq, "(", pcNum > 2 && cdNum >= 2);
                tq.append("t.receiver_city LIKE CONCAT(?, '%')");
                q.add(handleArea(cdt.nextToken().trim(), 1, prov));
                if (cdNum >= 2) {
                    tq.append(cdNum > 2 ? " AND (" : " AND ");
                }
                while (cdt.hasMoreTokens()) {
                    Query.conjunct(tq, " OR ", dn++ > 0).append("t.receiver_district LIKE CONCAT(?, '%')");
                    q.add(handleArea(cdt.nextToken().trim(), 2, prov));
                }
                Query.conjunct(tq, ")", cdNum > 2);
                Query.conjunct(tq, ")", pcNum > 2 && cdNum >= 2);
            }
            Query.conjunct(tq, ")", pcNum > 2);
            Query.conjunct(tq, ")", pcNum >= 2);
        }
        return pn > 1 ? q.and().append("(").append(tq).append(")") : (pn == 1 ? q.and().append(tq) : q);
    }

    /**
     * 此方法没有四级匹配
     *
     * @param q
     * @param areas
     * @return
     */
    @Override
    public Query buildAreaQueryWithTradeAddress(Query q, String areas) {
        if (areas == null || areas.isEmpty()) {
            return q;
        }
        StringTokenizer at = new StringTokenizer(areas, ",");
        StringBuilder tq = new StringBuilder();
        int pn = 0;
        while (at.hasMoreTokens()) {
            String area = at.nextToken().trim();
            if (area.isEmpty()) {
                continue;
            }
            String[] countrys = area.split("-");
            if (countrys.length == 2 && countrys[0].equals("")) {
                //是国外地址 -Italy
                Query.conjunct(tq, " OR ", pn++ > 0);
                tq.append(" ( ").append("ta.receiver_country LIKE CONCAT(?, '%')")
                        .append(" or ").append("t.receiver_country LIKE CONCAT(?, '%')")
                        .append(" ) ");
                q.add(countrys[1]);
                q.add(countrys[1]);
                continue;
            }

            //是国内地址，肯定有省，可能有市区
            String province;
            if (countrys.length == 1) {
                //浙江省@杭州市$西湖区,江西省@抚州市$乐安县
                province = countrys[0];
            } else {
                //中国%浙江省@杭州市$西湖区,中国%江西省@抚州市$乐安县
                province = countrys[1];
            }
            StringTokenizer pct = new StringTokenizer(province, "@");
            int pcNum = pct.countTokens(), cn = 0;
            Query.conjunct(tq, " OR ", pn++ > 0);
            Query.conjunct(tq, "(", pcNum >= 2);
            String provincePrefix = handleArea(pct.nextToken().trim(), 0, null);
            tq.append(" ( ").append("ta.receiver_state LIKE CONCAT(?, '%')")
                    .append(" or ").append("t.receiver_state LIKE CONCAT(?, '%')")
                    .append(" ) ");
            q.add(provincePrefix);
            q.add(provincePrefix);

            if (pcNum >= 2) {
                tq.append(pcNum > 2 ? " AND (" : " AND ");
            }
            while (pct.hasMoreTokens()) {
                StringTokenizer cdt = new StringTokenizer(pct.nextToken().trim(), "$");
                int cdNum = cdt.countTokens(), dn = 0;
                Query.conjunct(tq, " OR ", cn++ > 0);
                Query.conjunct(tq, "(", pcNum > 2 && cdNum >= 2);
                String cityPrefix = handleArea(cdt.nextToken().trim(), 1, provincePrefix);
                tq.append(" ( ").append("ta.receiver_city LIKE CONCAT(?, '%')")
                        .append(" or ").append("t.receiver_city LIKE CONCAT(?, '%')")
                        .append(" ) ");
                q.add(cityPrefix);
                q.add(cityPrefix);
                if (cdNum >= 2) {
                    tq.append(cdNum > 2 ? " AND (" : " AND ");
                }
                while (cdt.hasMoreTokens()) {
                    String districtPrefix = handleArea(cdt.nextToken().trim(), 2, provincePrefix);
                    Query.conjunct(tq, " OR ", dn++ > 0).append(" ( ")
                            .append("ta.receiver_district LIKE CONCAT(?, '%')")
                            .append(" or ").append("t.receiver_district LIKE CONCAT(?, '%')")
                            .append(" ) ");
                    q.add(districtPrefix);
                    q.add(districtPrefix);
                }
                Query.conjunct(tq, ")", cdNum > 2);
                Query.conjunct(tq, ")", pcNum > 2 && cdNum >= 2);
            }
            Query.conjunct(tq, ")", pcNum > 2);
            Query.conjunct(tq, ")", pcNum >= 2);
        }
        return pn > 1 ? q.and().append("(").append(tq).append(")") : (pn == 1 ? q.and().append(tq) : q);
    }

    private String outerIdStr(String outerId, List<Object> l1) {
        if (StringUtils.isBlank(outerId)) {
            return "= '' ";
        }
        String[] split = outerId.split(",");
        StringBuffer sb = new StringBuffer("IN (");
        for (int i = 0; i < split.length; i++) {
            sb.append(i > 0 ? ", ?" : "?");
            l1.add(split[i]);
        }
        sb.append(")");
        return sb.toString();
    }

    @Override
    public Query buildItemQuery(Staff staff, Query q, TradeQueryParams params) {
        if (!params.getContext().hasItemQuery()) {
            return q;
        }
        if (q.isStopQuery()) {
            return q;
        }
        if (ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getQueryByOrderHotItemCompanyIds(),staff.getCompanyId())) {
            return buildHotItemQuery(staff,q,params);
        }

        if (params.getItemTitle() != null || params.getShortTitle() != null || params.getItemRemark() != null) {
            params.getContext().addSuggests("使用 商家编码,主商家编码,平台商品Id 或 平台skuId等条件进行过滤");
        }

        //唯一码只能精确查询
        String uniqueCode = params.getUniqueCode() != null ? params.getUniqueCode().trim() : "";
        if (!uniqueCode.isEmpty()) {
            q.and().conjunct(String.format("o.id in (select uc.order_id from %s uc where uc.company_id = ? and uc.enable_status = 1 and uc.unique_code = ?)", q.getUniqueCodeTable()), true);
            q.getArgs().add(staff.getCompanyId());
            q.getArgs().add(uniqueCode);
        }

        int qt = params.getQueryType() != null ? params.getQueryType() : 0;
        //主商家编码查询只支持精确
        if (StringUtils.isNotBlank(params.getMainOuterId())) {
            qt = 1;
        }
        StringBuilder q1 = new StringBuilder(), q2 = new StringBuilder();
        List<Object> l1 = new ArrayList<Object>(), l2 = new ArrayList<Object>();
        String outerId = params.getOuterId() != null ? params.getOuterId().trim() : "";
        boolean appendAnd = false;
        if (!outerId.isEmpty()) {
            q1.append("o.sys_outer_id ").append(qt == 1 ? outerIdStr(outerId, l1) : outerIdLikeStr(staff));
            if (qt != 1) {
                l1.add(outerId);
            }
            q2.append("(o.outer_iid ").append(qt == 1 ? outerIdStr(outerId, l2) : outerIdLikeStr(staff)).append(" OR o.outer_sku_id ").append(qt == 1 ? outerIdStr(outerId, l2) + " )" : outerIdLikeStr(staff) + ")");
            if (qt != 1) {
                l2.add(outerId);
                l2.add(outerId);
            }
            if (Objects.equals(params.getItemType(), 1)) {
                q1.append(" AND o.type = 0 ");
                q2.append(" AND o.type = 0 ");
            } else if (Objects.equals(params.getItemType(), 2)) {
                q1.append(" AND o.type = 2 ");
                q2.append(" AND o.type = 2 ");
            } else if (Objects.equals(params.getItemType(), 3)) {
                q1.append(" AND o.type = 3 ");
                q2.append(" AND o.type = 3 ");
            } else if (Objects.equals(params.getItemType(), 4)) {
                q1.append(" AND o.stock_status ='INSUFFICIENT' ");
                q2.append(" AND o.stock_status ='INSUFFICIENT' ");
            }
            appendAnd = true;
        }
        Integer itemContainNotConsign = params.getItemContainNonConsign();
        if(Objects.equals(1,itemContainNotConsign)){
            Query.conjunct(q1, " AND ", appendAnd).append("o.non_consign = 1 ");
            appendAnd = true;
        }
        if (params.getSysOuterIds() != null && params.getSysOuterIds().length > 0) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_outer_id IN (");
            for (int i = 0; i < params.getSysOuterIds().length; i++) {
                q1.append(i > 0 ? ", ?" : "?");
                l1.add(params.getSysOuterIds()[i]);
            }
            q1.append(")");
            appendAnd = true;
        }

        if (params.getSysItemIds() != null && params.getSysItemIds().length > 0) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.item_sys_id IN (");
            for (int i = 0; i < params.getSysItemIds().length; i++) {
                q1.append(i > 0 ? ", ?" : "?");
                l1.add(params.getSysItemIds()[i]);
            }
            q1.append(")");
            appendAnd = true;
        }

        if (params.getSysSkuIds() != null && params.getSysSkuIds().length > 0) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sku_sys_id IN (");
            for (int i = 0; i < params.getSysSkuIds().length; i++) {
                q1.append(i > 0 ? ", ?" : "?");
                l1.add(params.getSysSkuIds()[i]);
            }
            q1.append(")");
            appendAnd = true;
        }

        if (params.getOuterIdAndSysItemIds() != null && params.getOuterIdAndSysItemIds().length > 0 && params.getOuterIdAndSysSkuIds() != null && params.getOuterIdAndSysSkuIds().length > 0) {
            Query.conjunct(q1, " AND ", appendAnd).append("(o.item_sys_id IN (");
            handOuterIdAndIds(params.getOuterIdAndSysItemIds(),q1,l1);
            q1.append(")");
            appendAnd = true;
            Query.conjunct(q1, " OR ", appendAnd).append("o.sku_sys_id IN (");
            handOuterIdAndIds(params.getOuterIdAndSysSkuIds(),q1,l1);
            q1.append("))");
        }

        if (params.getOuterIdAndSysItemIds() != null && params.getOuterIdAndSysItemIds().length > 0 && (params.getOuterIdAndSysSkuIds() == null || params.getOuterIdAndSysSkuIds().length <= 0)) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.item_sys_id IN (");
            handOuterIdAndIds(params.getOuterIdAndSysItemIds(),q1,l1);
            q1.append(")");
            appendAnd = true;
        }

        if (params.getOuterIdAndSysSkuIds() != null && params.getOuterIdAndSysSkuIds().length > 0 && (params.getOuterIdAndSysItemIds() == null || params.getOuterIdAndSysItemIds().length <= 0)) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sku_sys_id IN (");
            handOuterIdAndIds(params.getOuterIdAndSysSkuIds(),q1,l1);
            q1.append(")");
            appendAnd = true;
        }

        String title = params.getItemTitle() != null ? params.getItemTitle().trim() : "";
        if (!title.isEmpty()) {
            if(qt!=1 && tradeLocalConfig.isTradeSearchTitleReplaceSpacesWithPercentage(staff.getCompanyId())){
                title = replaceSpacesWithPercentage(title);
            }
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_title ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            Query.conjunct(q2, " AND ", appendAnd).append("o.title ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(title);
            l2.add(title);
            appendAnd = true;
        }

        String platFormTitle = params.getPlatFormTitle() != null ? params.getPlatFormTitle().trim() : "";
        if (!platFormTitle.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.title ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            Query.conjunct(q2, " AND ", appendAnd).append("o.title ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(platFormTitle);
            l2.add(platFormTitle);
            appendAnd = true;
        }

        String shortTitle = params.getShortTitle() != null ? params.getShortTitle().trim() : "";
        if (!shortTitle.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.short_title ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(shortTitle);
            appendAnd = true;
        }
        String skuProps = params.getSkuProp() != null ? params.getSkuProp().trim() : "";
        if (!skuProps.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_sku_properties_name ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(skuProps);
            appendAnd = true;
        }
        String platSkuProps = params.getPlatSkuProp() != null ? params.getPlatSkuProp().trim() : "";
        if (!platSkuProps.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sku_properties_name ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            Query.conjunct(q2, " AND ", appendAnd).append("o.sku_properties_name ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(platSkuProps);
            l2.add(platSkuProps);
            appendAnd = true;
        }
        String skuPropAlias = params.getSkuPropAlias() != null ? params.getSkuPropAlias().trim() : "";
        if (!skuPropAlias.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_sku_properties_alias ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(skuPropAlias);
            appendAnd = true;
        }
        String itemRemark = params.getItemRemark() != null ? params.getItemRemark().trim() : "";
        if (!itemRemark.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_item_remark ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(itemRemark);
            appendAnd = true;
        }
        String skuRemark = params.getSkuRemark() != null ? params.getSkuRemark().trim() : "";
        if (!skuRemark.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.sys_sku_remark ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(skuRemark);
            appendAnd = true;
        }
        if (params.getCid() != null && params.getCid() > 0) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.cids LIKE CONCAT('%', ?, '%')");
            l1.add(params.getCid());
            appendAnd = true;
        }
        if (params.getCid() != null && params.getCid() == -1) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.cids = '-1' ");
            appendAnd = true;
        }
        if (params.getCid() != null && params.getCid() == -1) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.cids = '-1' ");
            appendAnd = true;
        }
        String identCode = params.getIdentCode() != null ? params.getIdentCode().trim() : "";
        if (!identCode.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("o.ident_code ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
            l1.add(identCode);
            appendAnd = true;
        }
        boolean containItemNonMatch = false;
        if (params.getNumIid() != null && params.getNumIid().length > 0) {
            if (params.getNumIid().length == 1) {
                String numIid = params.getNumIid()[0] != null ? params.getNumIid()[0].trim() : "";
                if (StringUtils.isNotEmpty(numIid)) {
                    Query.conjunct(q1, " AND ", appendAnd).append("o.num_iid ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
                    l1.add(numIid);
                    appendAnd = true;
                    containItemNonMatch = true;
                }
            } else {
                if (qt != 1) throw new UnsupportedOperationException("商家编码:批量匹配与模糊匹配不兼容！");
                Query.conjunct(q1, " AND ", appendAnd).append("o.num_iid IN (");
                for (int i = 0; i < params.getNumIid().length; i++) {
                    q1.append(i > 0 ? ", ?" : "?");
                    l1.add(params.getNumIid()[i]);
                }
                q1.append(")");
                containItemNonMatch = true;
                appendAnd = true;
            }
        }

        if (params.getOuterIid() != null && params.getOuterIid().length > 0) {
            if (params.getOuterIid().length == 1) {
                String outerIid = params.getOuterIid()[0] != null ? params.getOuterIid()[0].trim() : "";
                if (StringUtils.isNotEmpty(outerIid)) {
                    Query.conjunct(q1, " AND ", appendAnd).append("o.outer_iid ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
                    l1.add(outerIid);
                    appendAnd = true;
                    containItemNonMatch = true;
                }
            } else {
                if (qt != 1) throw new UnsupportedOperationException("商家编码:批量匹配与模糊匹配不兼容！");
                Query.conjunct(q1, " AND ", appendAnd).append("o.outer_iid IN (");
                for (int i = 0; i < params.getOuterIid().length; i++) {
                    q1.append(i > 0 ? ", ?" : "?");
                    l1.add(params.getOuterIid()[i]);
                }
                q1.append(")");
                containItemNonMatch = true;
                appendAnd = true;
            }
        }

        if (params.getSkuId() != null && params.getSkuId().length > 0) {
            if (params.getSkuId().length == 1) {
                String skuId = params.getSkuId()[0] != null ? params.getSkuId()[0].trim() : "";
                if (StringUtils.isNotEmpty(skuId)) {
                    Query.conjunct(q1, " AND ", appendAnd).append("o.sku_id ").append(qt == 1 ? "= ?" : "LIKE CONCAT('%', ?, '%')");
                    l1.add(skuId);
                    appendAnd = true;
                    containItemNonMatch = true;
                }
            } else {
                if (qt != 1) throw new UnsupportedOperationException("平台skuId:批量匹配与模糊匹配不兼容！");
                Query.conjunct(q1, " AND ", appendAnd).append("o.sku_id IN (");
                for (int i = 0; i < params.getSkuId().length; i++) {
                    q1.append(i > 0 ? ", ?" : "?");
                    l1.add(params.getSkuId()[i]);
                }
                q1.append(")");
                containItemNonMatch = true;
                appendAnd = true;
            }
        }

        String originPlatformOuterId = (StringUtils.isBlank(params.getOuterId()) && params.getOriginPlatformOuterId() != null) ? params.getOriginPlatformOuterId().trim() : "";
        if (!originPlatformOuterId.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("( o.outer_iid = ? OR o.outer_sku_id = ? )");
            l1.add(originPlatformOuterId);
            l1.add(originPlatformOuterId);
            appendAnd = true;
            containItemNonMatch = true;
        }

        String skuOuterId = params.getSkuOuterId() != null ? params.getSkuOuterId().trim() : "";
        if (!skuOuterId.isEmpty()) {
            Query.conjunct(q1, " AND ", appendAnd).append("((o.sku_sys_id > 0 AND o.sys_outer_id LIKE CONCAT('%', ?, '%')) OR (o.item_sys_id <= 0 AND o.outer_sku_id LIKE CONCAT('%',?, '%')))");
            l1.add(skuOuterId);
            l1.add(skuOuterId);
            appendAnd = true;
            containItemNonMatch = true;
        }

/*        //平台换商品
        if (Strings.contains(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED, params.getExceptionStatus())) {
            orderQry.and().append("o.item_changed = 1");
        }*/
        if (isAppendItemPops(staff, params, q1, l1, appendAnd,q)) {
            appendAnd = true;
            containItemNonMatch = true;
        }
        if (q.isStopQuery()) {
            return q;
        }

        if (appendAnd) {
            q1.append(" AND o").append(TRADE_ORDER_SYS_STATUS_SQL).append(" AND o.company_id=? ");
            l1.add(staff.getCompanyId());
            if(q2.length()>0){
                q2.append(" AND o").append(TRADE_ORDER_SYS_STATUS_SQL).append(" AND o.company_id=? ");
                l2.add(staff.getCompanyId());
            }

            if (Objects.equals(params.getItemType(), 2) || (Objects.equals(params.getItemType(), 0) && StringUtils.isNotBlank(outerId)) || Objects.equals(params.getItemType(), 3)) {
                q.and().conjunct(" (", q2.length() > 0);
            } else {
                q.and().conjunct("o.combine_id = 0 AND (", q2.length() > 0);
            }
            if (containItemNonMatch) {
                q.append(q1);
            } else {
                q.append("(o.item_sys_id > 0 AND ").append(q1).append(")");
            }
            q.getArgs().addAll(l1);
            q.conjunct(" OR (o.item_sys_id <= 0 " + (StringUtils.trimToEmpty(q2.toString()).startsWith("AND") ? "" : " AND ") + q2.toString() + "))", q2.length() > 0);
            q.getArgs().addAll(l2);
        }
        return q;
    }


    @Resource
    private OrderConditionConverter orderConditionConverter;

    protected Query buildHotItemQuery(Staff staff, Query orderQry, TradeQueryParams params) {
        OrderRefCondition condition = new  OrderRefCondition();
        ConvertContext context = new  ConvertContext(staff);

        int qt = params.getQueryType() != null ? params.getQueryType() : 0;

        condition.setQueryType(params.getQueryType());

        if (StringUtils.isNotBlank(params.getUniqueCode())) {
            condition.setUniqueCodes(new String[]{params.getUniqueCode().trim()});
        }
        condition.setQueryType(params.getQueryType());

        String outerId = params.getOuterId() != null ? params.getOuterId().trim() : "";
        if (StringUtils.isNotBlank(outerId)) {
            condition.setOuterIds(outerId.trim().split(","));
            condition.setOuterIdRefType(params.getItemType());
        }

        String skuOuterId = params.getSkuOuterId();
        if (StringUtils.isNotBlank(skuOuterId)) {
            condition.setSkuOuterId(skuOuterId);
        }

        Integer itemContainNotConsign = params.getItemContainNonConsign();
        if(Objects.equals(1,itemContainNotConsign)){
            condition.setNonConsign(1);
        }

        if (params.getSysOuterIds() != null && params.getSysOuterIds().length > 0) {
            condition.setSysOuterIds(params.getSysOuterIds());
        }

        if (params.getSysItemIds() != null && params.getSysItemIds().length > 0) {
            condition.setSysItemIds(params.getSysItemIds());
        }

        if (params.getSysSkuIds() != null && params.getSysSkuIds().length > 0) {
            condition.setSysSkuIds(params.getSysSkuIds());
        }

        // 这个在前面已经转换为sysItemId了 不需要再赋值
        //if (params.getMainOuterId() != null) {
        //    condition.setMainOuterIds(new String[]{params.getMainOuterId().trim()});
        //}

        String title = params.getItemTitle() != null ? params.getItemTitle().trim() : "";
        if (!title.isEmpty()) {
            if(qt!=1 && tradeLocalConfig.isTradeSearchTitleReplaceSpacesWithPercentage(staff.getCompanyId())){
                title = replaceSpacesWithPercentage(title);
            }
            condition.setItemTitle(title);
        }

        String platFormTitle = params.getPlatFormTitle() != null ? params.getPlatFormTitle().trim() : "";
        if (!platFormTitle.isEmpty()) {
            condition.setPlatFormTitle(platFormTitle);
        }

        String shortTitle = params.getShortTitle() != null ? params.getShortTitle().trim() : "";
        if (!shortTitle.isEmpty()) {
            condition.setShortTitle(shortTitle);
        }
        String skuProps = params.getSkuProp() != null ? params.getSkuProp().trim() : "";
        if (!skuProps.isEmpty()) {
            condition.setSkuProps(skuProps);
        }
        String platSkuProps = params.getPlatSkuProp() != null ? params.getPlatSkuProp().trim() : "";
        if (!platSkuProps.isEmpty()) {
            condition.setPlatSkuProp(platSkuProps);
        }
        String skuPropAlias = params.getSkuPropAlias() != null ? params.getSkuPropAlias().trim() : "";
        if (!skuPropAlias.isEmpty()) {
            condition.setSkuPropAlias(skuPropAlias);
        }
        String itemRemark = params.getItemRemark() != null ? params.getItemRemark().trim() : "";
        if (!itemRemark.isEmpty()) {
            condition.setItemRemark(itemRemark);
        }
        String skuRemark = params.getSkuRemark() != null ? params.getSkuRemark().trim() : "";
        if (!skuRemark.isEmpty()) {
            condition.setSkuRemark(skuRemark);
        }
        if (params.getNumIid() != null && params.getNumIid().length > 0) {
            condition.setNumIids(params.getNumIid());
        }
        if (params.getOuterIid() != null && params.getOuterIid().length > 0) {
            condition.setOuterIids(params.getOuterIid());
        }
        if (params.getSkuId() != null && params.getSkuId().length > 0) {
            condition.setSkuIds(params.getSkuId());
        }
        String originPlatformOuterId = (StringUtils.isBlank(params.getOuterId()) && params.getOriginPlatformOuterId() != null) ? params.getOriginPlatformOuterId().trim() : "";
        if (!originPlatformOuterId.isEmpty()) {
            condition.setPlatformOuterIds(new String[]{originPlatformOuterId});
        }
        if (params.getCid() != null) {

        }
        String identCode = params.getIdentCode() != null ? params.getIdentCode().trim() : "";
        if (!identCode.isEmpty()) {
            condition.setIdentCode(identCode);
        }

        Integer[] itemPops = params.getItemPops();
        appendItemPops(staff, orderQry, condition, itemPops);

        /**
         * 这里传入的格式是这样的 outerIdAndSysItemIds:{"item_F":527125898674688}
         * 所以其实只要拿传入的JSON对应的 value 作为ItemSysIds或SkuSysId条件即可
         */
        List<Long> refItemSysIds = parseJsonToList(params.getOuterIdAndSysItemIds());
        List<Long> refSkuSysIds = parseJsonToList(params.getOuterIdAndSysSkuIds());

        if (CollectionUtils.isNotEmpty(refItemSysIds)) {
            Long[] array = refItemSysIds.toArray(new Long[0]);
            if (condition.getSysItemIds() == null || condition.getSysItemIds().length == 0) {
                condition.setSysItemIds(array);
            }else{
                Collection<Long> intersection = ConvertBase.intersection(condition.getSysItemIds(), array);
                condition.setSysItemIds(intersection.toArray(new Long[0]));
            }
        }
        if (CollectionUtils.isNotEmpty(refSkuSysIds)) {
            Long[] array = refSkuSysIds.toArray(new Long[0]);
            if (condition.getSysSkuIds() == null || condition.getSysSkuIds().length == 0) {
                condition.setSysSkuIds(array);
            }else{
                Collection<Long> intersection = ConvertBase.intersection(condition.getSysSkuIds(), array);
                condition.setSysSkuIds(intersection.toArray(new Long[0]));
            }
        }

        boolean appendAnd = orderConditionConverter.convert(staff,condition,context, orderQry);
        if (orderQry.isStopQuery()) {
            return orderQry;
        }

        StringBuilder q = orderQry.getQ();
        if (q.length() > 0) {
            // orderConditionConverter 构建的会以AND开头
            StringBuilder nw = new StringBuilder(" o.company_id = ").append(staff.getCompanyId()).append(" AND o").append(TRADE_ORDER_SYS_STATUS_SQL)
                    .append(q.toString());
            orderQry.setQ(nw);
        }

        return orderQry;
    }

    private void appendItemPops(Staff staff, Query q,OrderRefCondition condition, Integer[] itemPops) {
        Set<OrderAdditionalConditionEnum> additionals = new HashSet<>();
        if (itemPops == null || itemPops.length == 0) {
            return;
        }

        boolean hasVirtual =  ArrayUtils.contains(itemPops, 5);

        boolean added = false;
        if (ArrayUtils.contains(itemPops, Order.TypeOfNormal)) {
            additionals.add(hasVirtual?OrderAdditionalConditionEnum.TYPE_NORMAL:OrderAdditionalConditionEnum.TYPE_NORMAL_IGNORE_VIRTUAL);
            additionals.add(hasVirtual?OrderAdditionalConditionEnum.TYPE_GROUP:OrderAdditionalConditionEnum.TYPE_GROUP_IGNORE_VIRTUAL);
            additionals.add(hasVirtual?OrderAdditionalConditionEnum.TYPE_PROCESS:OrderAdditionalConditionEnum.TYPE_PROCESS_IGNORE_VIRTUAL);
            added = true;
        }
        if (ArrayUtils.contains(itemPops, Order.TypeOfCombineOrder)) {
            additionals.add(hasVirtual?OrderAdditionalConditionEnum.TYPE_COMBINE:OrderAdditionalConditionEnum.TYPE_COMBINE_IGNORE_VIRTUAL);
            added = true;
        }
        //有个历史遗留 原来的加工商品是归类到了普通商品里的，为保证兼容性，
        // 这里虽然单独加了加工商品的独立条件,前面普通商品里的也没有去掉
        if (ArrayUtils.contains(itemPops, Order.TypeOfProcessOrder)) {
            additionals.add(hasVirtual?OrderAdditionalConditionEnum.TYPE_PROCESS:OrderAdditionalConditionEnum.TYPE_PROCESS_IGNORE_VIRTUAL);
            added = true;
        }
        if (hasVirtual && !added) {
            additionals.add(OrderAdditionalConditionEnum.PROP_VIRTUAL);
        }

        if (ArrayUtils.contains(itemPops, Order.TypeOfGiftOrder)) {
            additionals.add(OrderAdditionalConditionEnum.PROP_GIFT);
        }
        if (ArrayUtils.contains(itemPops, Order.TypeOfNoGiftOrder)) {
            additionals.add(OrderAdditionalConditionEnum.PROP_NO_GIFT);
        }
        if (ArrayUtils.contains(itemPops, 7)) {
            additionals.add(OrderAdditionalConditionEnum.TYPE_NO_PROCESS);
        }
        if (additionals.size() > 0) {
            condition.setAdditionals(additionals.toArray(new OrderAdditionalConditionEnum[0] ));
        }


    }

    /**
     * 商品属性筛选
     * 0 普通商品
     * 1 赠品 是否是赠品不能根据type来判断，而要根据giftNum>0 来判断
     * 2 套件商品
     * 3 组合商品
     * 4 加工商品
     * 5 虚拟商品 使用Order中的is_virtual进行判断
     * 7 非加工商品
     */
    private boolean isAppendItemPops(Staff staff, TradeQueryParams params, StringBuilder q1, List<Object> l1, boolean appendAnd,Query q) {

        Integer[] itemPops = params.getItemPops();
        boolean flag = false;
        if (ArrayUtils.isNotEmpty(itemPops) && itemPops.length > 0) {
            if (ArrayUtils.contains(itemPops, Order.TypeOfGiftOrder) && ArrayUtils.contains(itemPops, Order.TypeOfNoGiftOrder)) {
                return false;
            }
            //这里先拼接了一个and
            Query.conjunct(q1, " AND ", appendAnd);
            if (ArrayUtils.contains(itemPops, Order.TypeOfNormal) || ArrayUtils.contains(itemPops, Order.TypeOfCombineOrder)) {
                q1.append("o.type in (");
                if (ArrayUtils.contains(itemPops, Order.TypeOfNormal)) {
                    q1.append(Order.TypeOfNormal);
                    q1.append(",").append(Order.TypeOfGroupOrder);
                    q1.append(",").append(Order.TypeOfProcessOrder);
                }
                if (ArrayUtils.contains(itemPops, Order.TypeOfNormal) && ArrayUtils.contains(itemPops, Order.TypeOfCombineOrder)) {
                    q1.append(",");
                }
                if (ArrayUtils.contains(itemPops, Order.TypeOfCombineOrder)) {
                    q1.append(Order.TypeOfCombineOrder);
                }
                q1.append(" ) ");
                //由于在新建商品的时候可以类型为套件或者普通商品 然后又是虚拟商品所以在这里需要加上对虚拟商品的判断
                q1.append(" and ( o.is_virtual=0)");
                flag = true;
            }
            if (ArrayUtils.contains(itemPops, 7)) {
                if (flag) {
                    q1.append(" AND ");
                }
                q1.append(" NOT EXISTS ( SELECT 1 from ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o2 WHERE o2.type IN (4) AND o2.company_id = ? AND o2.enable_status=1 AND o2.belong_sid = o.belong_sid )");
                l1.add(staff.getCompanyId());
                flag = true;
            }
            if (ArrayUtils.contains(itemPops, Order.TypeOfNoGiftOrder)) {
                if (flag) {
                    q1.append(" and ");
                }
                q1.append(" NOT EXISTS ( SELECT 1 from ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o2 WHERE o2.gift_num > 0 and o2.company_id = ? and o2.enable_status=1 and o2.belong_sid = o.belong_sid )");
                l1.add(staff.getCompanyId());
                flag = true;
            }
            //赠品和虚拟商品都只能用包含所以这里用or拼接
            if (ArrayUtils.contains(itemPops, Order.TypeOfGiftOrder)) {
                if (flag) {
                    q1.append(" OR ");
                }
                q1.append(" ( o.gift_num > 0").append(" and t.company_id = ? and o.enable_status=1 )");
                l1.add(staff.getCompanyId());
                flag = true;
            }
            if (ArrayUtils.contains(itemPops, 5)) {
                if (flag) {
                    q1.append(" OR ");
                }
                q1.append(" ( o.is_virtual = 1").append(" and t.company_id = ? and o.enable_status=1 ) ");
                l1.add(staff.getCompanyId());
            }
            return true;
        }
        return false;
    }

    public String outerIdLikeStr(Staff staff) {
        return "LIKE CONCAT(?, '%')";
    }

    @Override
    public <T extends Number> Query buildNumberRangeQuery(Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        q.and().append("(");
        if (start != null) {
            q.append(key).append(" >= ?").add(start);
        }
        if (end != null) {
            q.conjunct(" AND ", start != null).append(key).append(" <= ?").add(end);
        }
        return q.append(")");
    }

    @Override
    public <T extends Number> Query buildDecimalRangeQuery(Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        q.and().append("(");
        if (start != null) {
            q.append("CAST(").append(key).append(" AS DECIMAL(15,4))>=?").add(start);
        }
        if (end != null) {
            q.conjunct(" AND ", start != null).append("CAST(").append(key).append(" AS DECIMAL(15,4))<= ?").add(end);
        }
        return q.append(")");
    }

    public <T extends Number> Query buildMergeNumberRangeQuery(Staff staff, Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        q.and().append(" ((t.merge_sid = -1 ");
        if (start != null) {
            q.and().append("CAST(t.").append(key).append(" AS DECIMAL(15,4) ) >= ?").add(start);
        }
        if (end != null) {
            q.and().append("CAST(t.").append(key).append(" AS  DECIMAL(15,4) ) <= ?").add(end);
        }
        String sum = "sum(CAST(" + key + " AS DECIMAL(15,4)))";
        q.append(" )");
        q.or().append(" t.sid in (select sid from ( select merge_sid sid, ").append(sum).append(" sum from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" where company_id = ").append(staff.getCompanyId()).append(" and enable_status in (1,2) and merge_sid > 0 ");
        q.append("group by merge_sid having ");
        if (start != null) {
            q.append(sum).append(" >= ?").add(start);
        }
        if (end != null) {
            q.conjunct(" AND ", start != null).append(sum).append(" <= ?").add(end);
        }
        return q.append(") temp))");
    }

    /**
     * 累计order查询
     *
     * @param staff
     * @param q
     * @param key
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    public <T extends Number> Query buildOrderNumberRangeQuery(Staff staff, Query q, String key, T start, T end, boolean excludeCombineOrder) {
        if (start == null && end == null) {
            return q;
        }
        String sum = null;
        if (excludeCombineOrder) {//如果是 type=3,4 的计算时为0.0
            sum = "sum(IF((((o.type = 4 OR o.type = 3) AND o.combine_id = 0) OR (o.type <> 4 AND o.type <> 3)),CAST(o." + key + " AS DECIMAL(15, 4)),0.0))";
        } else {
            sum = "sum(CAST(o." + key + " AS DECIMAL(15, 4)))";
        }
        q.and().append(" ((t.merge_sid = -1 ");
        q.and().append(" t.sid in (select sid from order_").append(staff.getDbInfo().getOrderDbNo()).append(" o where company_id = ").append(staff.getCompanyId()).append(" and enable_status > 0");
        q.append(" group by sid having ").append(buildNumberRang(q, start, end, sum).getQ()).append("))");

        q.or().append(" (t.merge_sid > 0 and t.merge_sid in (select t.merge_sid FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t JOIN order_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.sid  where t.company_id = ").append(staff.getCompanyId()).append(" and t.enable_status in (1,2) and o.enable_status = 1 and t.merge_sid > 0 ");
        q.append(" group by merge_sid having ").append(buildNumberRang(q, start, end, sum).getQ()).append(")))");
        return q;
    }

    @Override
    public <T extends Number> Query buildMoneyRangeQuery(Staff staff,Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        BigDecimal[] range = getMoneyRange(staff,key,start,end);
        return buildNumberRangeQuery(q, key, range[0], range[1]);
    }

    @Override
    public <T extends Number> Query buildMergeMoneyRangeQuery(Staff staff, Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        BigDecimal[] range = getMoneyRange(staff,key,start,end);
        return buildMergeNumberRangeQuery(staff,q, key, range[0], range[1]);
    }

    /**
     * 累计order查询
     *
     * @param staff
     * @param q
     * @param key
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    @Override
    public <T extends Number> Query buildOrderMoneyRangeQuery(Staff staff, Query q, String key, T start, T end, boolean excludeCombineOrder) {
        if (start == null && end == null) {
            return q;
        }
        BigDecimal[] range = getMoneyRange(staff,key,start,end);
        return buildOrderNumberRangeQuery(staff,q, key, range[0], range[1],excludeCombineOrder);
    }

    private <T extends Number> BigDecimal[] getMoneyRange(Staff staff,String key, T start, T end){
        if (start == null && end == null) {
            return null;
        }
        return PaymentRangeUtils.getRange(staff.getCompanyId(), toBigDecimal(start), toBigDecimal(end));
    }

    private BigDecimal toBigDecimal(Number a){
        if (a == null) {
            return null;
        }
        if (a instanceof BigDecimal) {
            return (BigDecimal)a;
        }
        return BigDecimal.valueOf(a.doubleValue());
    }




    private <T extends Number> Query buildNumberRang(Query q0, T start, T end, String sum) {
        Query q = new Query();
        if (start != null) {
            q.append(sum).append(" >= ?");
            q0.add(start);
        }
        if (end != null) {
            q.conjunct(" AND ", start != null).append(sum).append(" <= ?");
            q0.add(end);
        }
        return q;
    }

    public static void main(String[] args) {
        TradeSqlQueryBuilder sqlQueryBuilder = new TradeSqlQueryBuilder();
        Query q = new Query();
        q.append("SELECT DISTINCT(t.sid) FROM trade_32 t JOIN order_132 o ON t.sid = o.sid AND o.enable_status > 0 WHERE t.company_id = 11732 AND (t.enable_status = 1 OR t.enable_status = 2)");
        Query q2 = sqlQueryBuilder.buildOrderNumberRangeQuery(null, q, "discount_fee", 1.0, 100.0, false);
        System.out.println(q2.getQ());
    }

    /**
     * @deprecated 订单标签已拆出 24年6月后 trade表的tag_ids字段不再维护
     */
    @Override
    @Deprecated
    public Query buildTagIdsQuery(Query q, TradeQueryParams params) {
        buildTagQuery(q, "tag_ids", params.getTagIds(),0, params);
        buildTagQuery(q, "tag_ids", params.getOnlyTagIds(),1, params);
        return q;
    }

    @Override
    public void buildExcludeQuery(Staff staff, TradeQueryParams params, StringBuilder s1, StringBuilder s2, Query q) {
        //这个表示有多少个排除查询（排除组合查询，如果有多个，合单查询则用OR）
        int excludeTimes = 0;
        //排除标签
        if (!TradeDiamondUtils.openNewLabelRead(staff) && params.getExcludeTagIds() != null && params.getExcludeTagIds().length > 0) {
            Query excludeQueryAnd = buildExcludeTagQueryAnd("tag_ids", params.getExcludeTagIds());
            Query excludeQueryOr = buildExcludeTagQueryOr("tag_ids", params.getExcludeTagIds());
            Query.and(s1).append(excludeQueryAnd.getQ());
            String[] notNoneIds = getNotNoneIds(params.getExcludeTagIds());
            if (notNoneIds.length > 0) {
                q.add(notNoneIds);
            }
            if (s2.length() > 0) {
                Query.or(s2).append(excludeQueryOr.getQ());
            } else {
                Query.and(s2).append(excludeQueryOr.getQ());
            }
            excludeTimes++;
        }

        //排除旗帜
        if (params.getExcludeSellerFlags() != null && params.getExcludeSellerFlags().length > 0) {
            Query selleFlagQueryAnd = buildExcludeSellerFlagQueryAnd("seller_flag", params.getExcludeSellerFlags());
            Query selleFlagQueryOr = buildExcludeSellerFlagQueryOr("seller_flag", params.getExcludeSellerFlags());
            Query.and(s1).append(selleFlagQueryAnd.getQ());
            if (excludeTimes > 0 || s2.length() > 0) {
                Query.or(s2).append(selleFlagQueryOr.getQ());
            } else {
                Query.and(s2).append(selleFlagQueryOr.getQ());
            }
        }
    }

    public static String[] getNotNoneIds(String[] excludeTagIds) {
        List<String> ids = new ArrayList<>();
        for (String excludeTagId : excludeTagIds) {
            if ("-1".equals(excludeTagId)) {
                continue;
            }
            ids.add(excludeTagId);
        }
        return ids.toArray(new String[0]);
    }


    /**
     * 拼接排除标签-AND
     *
     * @param
     * @return
     * @date 2019/7/15 1:43 PM
     */
    private Query buildExcludeTagQueryAnd(String key, String[] ids) {
        Query q = new Query();
        int c = 0;
        for (String tagId : ids) {
            if (tagId != null && !(tagId.trim()).isEmpty()) {
                if ("-1".equals(tagId)) {
                    continue;
                }
                if (c++ == 0) {
                    q.and().append("(LOCATE(?, IFNULL (`").append(key).append("`,'')) = 0");
                } else {
                    q.and().append("LOCATE(?, IFNULL (`").append(key).append("`,'')) = 0");
                }
            }
        }

        //排除标签中选择了无标签
        if (Arrays.asList(ids).contains("-1")) {
            q.and().append("(IF((t.merge_sid = t.sid AND(IFNULL(TAG_IDS, '') NOT REGEXP '[0-9]+')), 1 = 1, (IFNULL(TAG_IDS, '') REGEXP '[0-9]+')) )");
        }

        q.conjunct(")", c > 0);
        return q;
    }


    /**
     * 拼接排除标签-OR
     *
     * @param
     * @return
     * @date 2019/7/15 1:43 PM
     */
    private Query buildExcludeTagQueryOr(String key, String[] ids) {
        Query q = new Query();
        int c = 0;
        for (String tagId : ids) {
            if (tagId != null && !(tagId.trim()).isEmpty()) {
                if ("-1".equals(tagId)) {
                    continue;
                }
                if (c++ == 0) {
                    q.and().append("(LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0");
                } else {
                    q.or().append("LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0");
                }
            }
        }

        //排除标签中选择了无标签

        //合单如果任意一单有标签认为整个订单有标签
        if (Arrays.asList(ids).contains("-1")) {
            q.or().append("(IF((IFNULL(t.tag_ids, '') REGEXP '[0-9]+') , False, ( (IFNULL(tag_ids, '') NOT  REGEXP '[0-9]+')  AND  (IFNULL(t.tag_ids, '') NOT  REGEXP '[0-9]+') )) )");
        }
        q.conjunct(")", c > 0);
        return q;
    }


    /**
     * 拼接排除旗帜-AND
     *
     * @param
     * @return
     * @date 2019/7/15 1:43 PM
     */
    private Query buildExcludeSellerFlagQueryAnd(String key, Integer[] ids) {
        Query q = new Query();
        int c = 0;
        Boolean hasNonSellerFlag = TradeUtils.hasNonSellerFlag(ids);
        for (Integer sellFlag : ids) {
            if (sellFlag != null) {
                if (Integer.valueOf(-1).equals(sellFlag)) {
                    continue;
                }
                if (c++ == 0) {
                    q.and().append("(").append(key).append(" <> ").append(sellFlag);
                } else {
                    q.and().append(key).append(" <> ").append(sellFlag);
                }
            }
        }
        if (!hasNonSellerFlag) {
            Query.conjunct(q.getQ(), " or ", q.getQ().length() > 1).append(" (seller_flag IS  NULL OR seller_flag = '-1')");
        } else {
            Query.conjunct(q.getQ(), " and ", q.getQ().length() > 1).append(" (seller_flag IS NOT NULL AND seller_flag != '-1')");
        }
        q.conjunct(")", c > 0);
        return q;
    }


    /**
     * 拼接排除旗帜-OR
     *
     * @param
     * @return
     * @date 2019/7/15 1:43 PM
     */
    private Query buildExcludeSellerFlagQueryOr(String key, Integer[] ids) {
        Query q = new Query();
        int c = 0;
        Boolean hasNonSellerFlag = TradeUtils.hasNonSellerFlag(ids);
        for (Integer sellFlag : ids) {
            if (sellFlag != null) {
                if (Integer.valueOf(-1).equals(sellFlag)) {
                    continue;
                }
                if (c++ == 0) {
                    q.and().append("(").append(key).append(" = ").append(sellFlag);
                } else {
                    q.or().append(key).append(" = ").append(sellFlag);
                }
            }
        }
        if (hasNonSellerFlag) {
            if (ids.length == 1) {
                Query.conjunct(q.getQ(), " and ", q.getQ().length() > 1).append(" (seller_flag IS NULL OR seller_flag = '-1')");
            } else {
                Query.conjunct(q.getQ(), " and ", q.getQ().length() > 1).append(" (seller_flag IS NOT NULL AND seller_flag != '-1')");
            }
        }
        q.conjunct(")", c > 0);
        return q;
    }

    /**
     *
     * @param q    组装的query
     * @param key  数据库字段
     * @param ids  标签ids
     * @param queryTagType  0: 包含，1：同时包含
     * @deprecated 订单标签已拆出 24年6月后 trade表的tag_ids字段不再维护
     */
    @Deprecated
    private void buildTagQuery(Query q, String key, String[] ids, int queryTagType, TradeQueryParams params) {
        if (ids == null || ids.length == 0) {
            return;
        }
        //判断是包含，排除，还是组合查询
        boolean include = (ids != null && ids.length > 0) && (params.getExcludeTagIds() == null || params.getExcludeTagIds().length == 0);
        if (ids.length == 1) {
            if ("0".equals(ids[0])) {// 有标签(自定义异常)
                if ("tag_ids".equals(key)) {//只处理标签,自定义异常在buildExceptSql中处理
                    q.and().append(key).append(" regexp '[0-9]+' ");
                }
                return;
            }
        }
        int c = 0;
        for (String tagId : ids) {
            if (tagId != null && !(tagId = tagId.trim()).isEmpty()) {
                if ("-1".equals(tagId)) {
                    continue;
                }
                if (c++ == 0) {
                    q.and().append("(LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0").add(tagId);
                } else {
                    if(queryTagType == 1){
                        q.and().append("LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0").add(tagId);
                    }else {
                        q.or().append("LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0").add(tagId);
                    }
                }
            }
        }
        //勾选无异常订单:如勾选了 无异常订单 和 快递异常。此时，会筛选出 无异常的订单和 包含快递异常的订单。
        if (params.getTickExcep() - 1 == 0 && null != params.getOnlyContain()) {
            q.or().append("is_excep = 0");
        }
        if (include) {
            //如果仅仅是包涵-1（无标签），外围已处理，此处只处理
            //包含标签中选择了无标签
            if (Arrays.asList(ids).contains("-1")) {
                if(queryTagType == 1){
                    q.and().append("( tag_ids = '' ");
                    q.or().append(" tag_ids IS NULL )");
                }else {
                    q.or().append("( tag_ids = '' ");
                    q.or().append(" tag_ids IS NULL )");
                }
            }
        }
        q.conjunct(")", c > 0);
    }
    private void buildExceptQuery(Staff staff,Query q, String key, String[] ids, TradeQueryParams params) {
        Query q0=new Query();
        Query selfExceptNotMergeContailSql = tradeNewExceptSqlQueryBuilder.getSelfExceptNotMergeContailSql(staff, q, params);
        //判断是包含，排除，还是组合查询
        boolean include = (params.getTagIds() != null && params.getTagIds().length > 0) && (params.getExcludeTagIds() == null || params.getExcludeTagIds().length == 0);
        if (ids == null || ids.length == 0) {
            return;
        }
        int c = 0;
        for (String tagId : ids) {
            if (tagId != null && !(tagId = tagId.trim()).isEmpty()) {
                if ("-1".equals(tagId)) {
                    continue;
                }
                if (c++ == 0) {
                    q0.and().append("(LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0").add(tagId);
                } else {
                    q0.or().append("LOCATE(?, IFNULL(`").append(key).append("`,'')) > 0").add(tagId);
                }
            }
        }
        //勾选无异常订单:如勾选了 无异常订单 和 快递异常。此时，会筛选出 无异常的订单和 包含快递异常的订单。
        if (params.getTickExcep() - 1 == 0 && null != params.getOnlyContain()) {
            q0.or().append("is_excep = 0");
        }
        if (include) {
            //如果仅仅是包涵-1（无标签），外围已处理，此处只处理
            //包含标签中选择了无标签
            if (Arrays.asList(ids).contains("-1")) {
                q0.or().append("( tag_ids = '' ");
                q0.or().append(" tag_ids IS NULL )");
            }
        }
        q0.conjunct(")", c > 0);
        if(q0.getQ().length()>0||selfExceptNotMergeContailSql.getQ().length()>0){
            q.append("(");
        }
        if(q0.getQ().length()>0){
            q.append("(").append(q0.getQ()).append(")").add(q0.getArgs());
        }
        if(selfExceptNotMergeContailSql.getQ().length()>0){
            q.conjunct(" OR ",q0.getQ().length()>0).append("(").append(selfExceptNotMergeContailSql.getQ()).append(")").add(selfExceptNotMergeContailSql.getArgs());
        }
        if(q0.getQ().length()>0||selfExceptNotMergeContailSql.getQ().length()>0){
            q.append(")");
        }
    }
    @Override
    public Query buildBuyerNickQuery(Staff staff, Query q, String... buyerNick) {
        if (buyerNick != null && buyerNick.length > 0) {
            int c = 0;
            String base = " t.sid in (select tempTrade.sid from " + q.getTradeTable() + "_" + staff.getDbInfo().getTradeDbNo() + " tempTrade  where tempTrade.enable_status != 0 AND company_id = " + staff.getCompanyId() + " AND tempTrade.buyer_nick IN(";
            for (String tempBuyerNick : buyerNick) {
                if (!(tempBuyerNick = tempBuyerNick.trim()).isEmpty()) {
                    if (c == 0) {
                        q.and().append(base == null ? "t.buyer_nick IN(" : base);
                    }
                    if (c > 0) {
                        q.append(", ");
                    }
                    q.append("?, ?").add(tempBuyerNick).add(secretBusiness.encodeStr(staff, tempBuyerNick));
                    c++;

                }
            }
            if (c > 0) {
                q.append("))");
            }
        }
        return q;
    }

    @Override
    public Query buildTidQuery(Staff staff, Query q, TradeQueryParams params) {
        String[] tid = params.getTid();
        if (tid == null || tid.length == 0) {
            return q;
        }
        if (tid.length > 10000) {
            params.getContext().addSuggests("减小平台单号数量,分批次进行查询(当前"+tid.length+"个单号)");
        }

        String base = " t.sid in (select if(tempTrade.merge_sid>0,tempTrade.merge_sid,tempTrade.sid) from " + q.getTradeTable() + "_" + staff.getDbInfo().getTradeDbNo() + " tempTrade ";
        if (tid.length > 1000) {
            new QueryLogBuilder(staff).append("tids size",tid.length).append("强制索引","idx_tid").printDebug(logger);
            base += " force index(idx_tid) ";
            params.getContext().addOptimizations("forceIndex");
        }
        base = base + " where tempTrade.enable_status != 0 AND   tempTrade.company_id = "+ staff.getCompanyId();
        //前置处理tid
        List<String>[] tidList = preHandleTid(staff,params.getContext().getUserSourceMap(),qimenTradeDubboService,fxSupplierSrcService, tid);
        List<String> inTids = tidList[0];
        List<String> likeTids = tidList[1];

        /**
         * 1 1000 以内 按 tid in (A) Or tid like 'A-%' 查询 查询原订单 售后单(A-1 ... A-n) 及复制原订单生成的系统单(A-xxxxxx)
         * 2 1000 - 2000 按 tid in (A,A-1，... A-n ) n<=5 查询 售后单直接拼接查询 最多查5个,不考虑复制新建的订单
         * 3 2000以上 按 tid in (A) 查询 仅查询tid对应的原订单
         */
        Query likeQuery = null;
        if (CollectionUtils.isNotEmpty(likeTids)) {
            if(tid.length <= 1000){
                likeQuery = buildLikeQuery(new Query(), 0, " tempTrade.tid ", likeTids.stream().map(t->t+"-").toArray(String[]::new));
            }else if(tid.length > 1000 && tid.length <= 2000){
                List<String> list = new ArrayList<>();
                for (String s : likeTids) {
                    list.add(s);
                    for (int i = 1; i <=5 ; i++) {
                        list.add(s+"-"+i);
                    }
                }
                inTids.addAll(list);
            }
        }

        Query inQuery = buildListQuery(new Query(), " tempTrade.tid ", inTids);
        q.and().append(base).and().append(" (")
                .append(inQuery.getQ()).add(inQuery.getArgs());
        if(likeQuery != null){
            q.or().append(likeQuery.getQ()).add(likeQuery.getArgs());
        }
        q.append(") )");
        return q;
    }

    /**
     * 有特殊平台用户  通过tid查询  平台tid与快麦tid  不一致情况
     * 放心购带A 不带A 支持查询
     *
     * @param staff
     * @param q
     * @param tid
     * @return
     */
    private List<User> getSpcialPlatUserForTidQuery(Staff staff, Query q, String... tid) {
        List<User> allUsers = staff.getUsers();
        List<User> specialPlatUsers = new ArrayList<>();
        if (CollectionUtils.isEmpty(allUsers)) {
            return specialPlatUsers;
        }

        List<User> lazadaUsers = allUsers.stream().filter(user -> CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(user.getSource())).collect(Collectors.toList());
        List<User> fxgUsers = allUsers.stream().filter(user -> CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())).collect(Collectors.toList());

        specialPlatUsers.addAll(lazadaUsers);
        specialPlatUsers.addAll(fxgUsers);
        return specialPlatUsers;
    }

    /**
     * 特殊场景，tid兼容某些特殊规则查询
     *
     * @param staff
     * @param users
     * @param q
     * @param tid
     * @return
     */
    private Query buildTidQueryForSpecial(Staff staff, List<User> users, Query q, String tid) {
        if (StringUtils.isBlank(tid) || CollectionUtils.isEmpty(users)) {
            return q;
        }
        q.and().append("(t.tid IN(?").add(tid);
        int c = 0;
        boolean existFxgUser = false;
        for (User user : users) {
            if (CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(user.getSource())) {
                if (!tid.contains("-")) {//没有 中划线  则拼上  中划线和 taobaoId查询
                    q.append(",? ").add(tid + "-" + user.getTaobaoId());
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())) {
                existFxgUser = true;
            }
        }
        //放心购增加订单号带A 或  不带A 查询
        if (existFxgUser) {
            if (tid.endsWith(TradeConstants.FXG_SUFFIX)) {
                q.append(",? ").add(tid.substring(0, tid.length() - 1));//去A
            } else {
                q.append(",? ").add(tid + TradeConstants.FXG_SUFFIX);//带A
            }
        }
        q.append(") OR t.tid LIKE CONCAT(?, '-%'))").add(removeFirstPercentSignTid(tid));

        return q;
    }

    private String removeFirstPercentSignTid(String tid) {
        while (tid != null && tid.length() > 0 && tid.startsWith("%")) {
            tid = tid.substring(1);
        }
        return tid;
    }

    @Override
    public Query buildDeliveryTimeQuery(Query q, Integer... deliveryTimes) {
        return buildPromiseRangeQuery(q, deliveryTimes);
    }

    @Override
    public Query buildSignTimeQuery(Query q, Integer... signTimes) {
        return buildPromiseRangeQuery(q, signTimes);
    }

    @Override
    public Query buidFxtaobaoIdQuery(Staff staff, Query q, TradeQueryParams params) {
        String[] taobaoIds = params.getTaobaoIds();
        if (taobaoIds != null && taobaoIds.length > 0) {

        }
        return q;
    }

    @Resource
    private ReceiverConditionConverter receiverConditionConverter;

    @Override
    public Query buildReceiverMobileQuery(Staff staff, Query q, TradeQueryParams params) {
        if (q.isStopQuery()) {
            return q;
        }
        String receiverMobile = StringUtils.trimToEmpty(params.getReceiverMobile());

        if (StringUtils.isNotEmpty(receiverMobile)) {
            Map<String, List<Long>> userSourceMap = params.getContext().getUserSourceMap();
            Set<String> sources = userSourceMap == null? new HashSet<>(0):userSourceMap.keySet();
            String[] tradeTypes = StringUtils.isBlank(params.getTradeType())?null:new String[]{params.getTradeType()};
            receiverConditionConverter.buildReceiverMobileQuery(staff,q,Strings.splitByDelimiter(receiverMobile,","),params.getOrTids(),sources,params.getUserIds(),tradeTypes);
        }
        return q;
    }

    @Override
    public Query buildReceiverMobileQueryWithTradeAddress(Staff staff, Query q, TradeQueryParams params) {
        String receiverMobile = StringUtils.trimToEmpty(params.getReceiverMobile());
        if (StringUtils.isNotEmpty(receiverMobile)) {
            String[] array = getReceiverMobile(staff, receiverMobile);
            StringBuilder buf = new StringBuilder();
            int n = 0;
            for (String v : array) {
                if (v == null) {
                    continue;
                }
                if (n++ > 0) {
                    buf.append(", ");
                }
                buf.append("?");
                q.add(v);
            }
            q.and().append(" t.receiver_mobile").append(" IN (").append(buf).append(")");
            if (null != params.getOrTids() && params.getOrTids().length > 0 && !q.isTradeAddress()) {
                StringBuilder sb = new StringBuilder();
                int k = 0;
                for (String tid : params.getOrTids()) {
                    if (tid == null) {
                        continue;
                    }
                    if (k++ > 0) {
                        sb.append(", ");
                    }
                    sb.append("?");
                    q.add(tid);
                }
                q.append(" union all select temp1.sid from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                q.and().append(" temp1.tid IN (").append(sb).append(")");
            }
            try {
                List<Long> sidList = tradeExtBusiness.getSids4TradeExt(staff, params);
                if (CollectionUtils.isNotEmpty(sidList)) {
                    q.append(" union all select temp1.sid from trade_address_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                    int m = 0;
                    StringBuilder sidBuf = new StringBuilder();
                    for (Long v : sidList) {
                        if (v == null) {
                            continue;
                        }
                        if (m++ > 0) {
                            sidBuf.append(", ");
                        }
                        sidBuf.append("?");
                        q.add(v);
                    }
                    q.and().append("temp1.sid").append(" IN (").append(sidBuf).append(")");
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人手机出错: " + e.getMessage()), e);
            }
            if (null != params.getOrTids() && params.getOrTids().length > 0) {
                q.append(" union all select temp1.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                Set<String> tids = Sets.newHashSet(params.getOrTids());

                String inStr = tids.stream().map(tid-> "?").collect(Collectors.joining(",","(",")"));
                String orStr = tids.stream().map(tid-> " OR temp1.tid LIKE CONCAT(?, '-%')").collect(Collectors.joining(" "));

                q.and().append("( temp1.tid in ").append(inStr).add(params.getOrTids()).append(orStr).append(")").add(params.getOrTids());
            }
        }
        return q;
    }

    private String buildExists(Staff staff, String field, String buf) {
        return " select 1 from trade_address_" + staff.getDbInfo().getTradeDbNo() + " temp2 where t.sid = temp2.sid and temp2.company_id =" + staff.getCompanyId() + " and  temp2." + field + "  IN ( " + buf + ") ";
    }

    @Override
    public Query buildReceiverAddressQuery(Staff staff, Query q, TradeQueryParams params) {
        String receiverAddress = StringUtils.trimToEmpty(params.getReceiverAddress());
        if (StringUtils.isNotEmpty(receiverAddress)) {
            q.append(" AND (");
            if(params.getContext().existAnySource(CommonConstants.PLATFORM_TYPE_HIPAC)){
                String receiverAddressEncoderStr = secretBusiness.encodeStr(staff, receiverAddress);
                if (params.getQueryType() - 1 == 0) {
                    q.append("t.receiver_address").append(" in (?,?) ").add(receiverAddress).add(receiverAddressEncoderStr);
                } else {
                    q.append("(t.receiver_address").append(" LIKE CONCAT('%', ?, '%') ").add(receiverAddress);
                    q.append(" or t.receiver_address").append(" LIKE CONCAT('%', ?, '%')) ").add(receiverAddressEncoderStr);
                }
            }else {
                List<String> encryptReceiverAddressList = commonTradeDecryptBusiness.encryptByUser(staff,params.getReceiverAddress(),1);
                boolean hasEncryptReceiverAddress = CollectionUtils.isNotEmpty(encryptReceiverAddressList);
                if (params.getQueryType() - 1 == 0) {
                    List<String> addressList = Lists.newArrayList(receiverAddress);
                    String receiverAddressEncoderStr = secretBusiness.encodeStr(staff, receiverAddress);
                    if(StringUtils.isNotBlank(receiverAddressEncoderStr)){
                        addressList.add(receiverAddressEncoderStr);
                    }
                    if(hasEncryptReceiverAddress){
                        addressList.addAll(encryptReceiverAddressList);
                    }
                    appendInClause("t.receiver_address", addressList, q);
                } else {
                    q.append("t.receiver_address").append(" LIKE CONCAT('%', ?, '%')").add(params.getReceiverAddress());

                }
            }
            try {
                Query queryTradeExt = tradeExtBusiness.buildReceiverInfo(staff, params);
                if (queryTradeExt.getQ().length() > 0) {
                    q.or().append("t.sid IN (").append(queryTradeExt.getQ()).add(queryTradeExt.getArgs()).append(")");
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人手机出错: " + e.getMessage()), e);
            }
            q.append(")");
        }
        return q;
    }

    @Override
    public Query buildReceiverAddressQueryWithTradeAddress(Staff staff, Query q, TradeQueryParams params) {
        String receiverAddress = StringUtils.trimToEmpty(params.getReceiverAddress());
        if (StringUtils.isNotEmpty(receiverAddress)) {
            q.append(" AND (");
            if (params.getQueryType() - 1 == 0) {
                q.append(" ( ");
                q.append("ta.receiver_address").append(" = ?").add(params.getReceiverAddress());
                q.append(" or t.receiver_address").append(" = ?").add(params.getReceiverAddress());
                q.append(" ) ");
            } else {
                q.append(" ( ")
                        .append("ta.receiver_address").append(" LIKE CONCAT('%', ?, '%')").add(params.getReceiverAddress())
                        .append(" or t.receiver_address").append(" LIKE CONCAT('%', ?, '%')").add(params.getReceiverAddress())
                        .append(" ) ");
            }
            try {
                List<Long> sidList = tradeExtBusiness.getSids4TradeExt(staff, params);
                if (CollectionUtils.isNotEmpty(sidList)) {
                    int m = 0;
                    StringBuilder sidBuf = new StringBuilder();
                    for (Long v : sidList) {
                        if (v == null) {
                            continue;
                        }
                        if (m++ > 0) {
                            sidBuf.append(", ");
                        }
                        sidBuf.append("?");
                        q.add(v);
                    }
                    q.or().append("t.sid").append(" IN (").append(sidBuf).append(")");
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人手机出错: " + e.getMessage()), e);
            }
            q.append(")");
        }
        return q;
    }

    /**
     * 此处的零时表不要冒然去掉，可以走receiver_name，tid索引。
     * 参考：https://gykj.yuque.com/entavv/eltb2u/up7m2a
     *
     * @param staff
     * @param q
     * @param params
     * @return
     */
    @Override
    public Query buildReceiverNameQuery(Staff staff, Query q, TradeQueryParams params) {
        if (q.isStopQuery()) {
            return q;
        }
        String receiverName = StringUtils.trimToEmpty(params.getReceiverName());

        if (StringUtils.isNotEmpty(receiverName)) {
            String[] array = getReceiverName(staff, receiverName);
            q.setAllowTradeCountIndex(false);
            q.append(" AND ( t.sid IN( select sid from ( select temp.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp where temp.company_id= " + staff.getCompanyId() + " and temp.enable_status > 0 ");
            StringBuilder buf = new StringBuilder();
            int n = 0;
            for (String v : array) {
                if (v == null) {
                    continue;
                }
                if (n++ > 0) {
                    buf.append(", ");
                }
                buf.append("?");
                q.add(v);
            }
            q.append("AND temp.receiver_name").append(" IN (").append(buf).append(")");
            try {
                Query queryTradeExt =  tradeExtBusiness.buildReceiverInfo(staff,params);
                if (queryTradeExt.getQ().length() > 0) {
                    q.append(" UNION ALL ").append(queryTradeExt.getQ()).add(queryTradeExt.getArgs());
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人姓名出错: " + e.getMessage()), e);
            }
            if (null != params.getOrTids() && params.getOrTids().length > 0) {
                q.append(" union all select temp1.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                Set<String> tids = Sets.newHashSet(params.getOrTids());

                String inStr = tids.stream().map(tid -> "?").collect(Collectors.joining(",", "(", ")"));
                String orStr = tids.stream().map(tid -> " OR temp1.tid LIKE CONCAT(?, '-%')").collect(Collectors.joining(" "));

                q.and().append("( temp1.tid in ").append(inStr).add(params.getOrTids()).append(orStr).append(")").add(params.getOrTids());
            }
            q.append(") unionTemp");
            q.append("))");
        }
        return q;
    }

    @Override
    public Query buildReceiverNameQueryWithTradeAddress(Staff staff, Query q, TradeQueryParams params) {
        String receiverName = StringUtils.trimToEmpty(params.getReceiverName());
        if (StringUtils.isNotEmpty(receiverName)) {
            String[] array = getReceiverName(staff, receiverName);
            StringBuilder buf = new StringBuilder();
            int n = 0;
            for (String v : array) {
                if (v == null) {
                    continue;
                }
                if (n++ > 0) {
                    buf.append(", ");
                }
                buf.append("?");
                q.add(v);
            }
            q.and().append(" t.receiver_name").append(" IN (").append(buf).append(")");
            if (null != params.getOrTids() && params.getOrTids().length > 0 && !q.isTradeAddress()) {
                StringBuilder sb = new StringBuilder();
                int k = 0;
                for (String tid : params.getOrTids()) {
                    if (tid == null) {
                        continue;
                    }
                    if (k++ > 0) {
                        sb.append(", ");
                    }
                    sb.append("?");
                    q.add(tid);
                }
                q.append(" union all select temp1.sid from ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                q.and().append(" temp1.tid IN (").append(sb).append(")");
            }
            try {
                List<Long> sidList = tradeExtBusiness.getSids4TradeExt(staff, params);
                if (CollectionUtils.isNotEmpty(sidList)) {
                    q.append(" union all select temp1.sid from trade_address_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                    int m = 0;
                    StringBuilder sidBuf = new StringBuilder();
                    for (Long v : sidList) {
                        if (v == null) {
                            continue;
                        }
                        if (m++ > 0) {
                            sidBuf.append(", ");
                        }
                        sidBuf.append("?");
                        q.add(v);
                    }
                    q.and().append("temp1.sid").append(" IN (").append(sidBuf).append(")");

                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人姓名出错: " + e.getMessage()), e);
            }
            if (null != params.getOrTids() && params.getOrTids().length > 0) {
                q.append(" union all select temp1.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                Set<String> tids = Sets.newHashSet(params.getOrTids());

                String inStr = tids.stream().map(tid-> "?").collect(Collectors.joining(",","(",")"));
                String orStr = tids.stream().map(tid-> " OR temp1.tid LIKE CONCAT(?, '-%')").collect(Collectors.joining(" "));

                q.and().append("( temp1.tid in ").append(inStr).add(params.getOrTids()).append(orStr).append(")").add(params.getOrTids());
            }
        }
        return q;
    }

    private void qOrSidList(Query q, List<Long> sidList) {
        if (CollectionUtils.isNotEmpty(sidList)) {
            int m = 0;
            StringBuilder sidBuf = new StringBuilder();
            for (Long v : sidList) {
                if (v == null) {
                    continue;
                }
                if (m++ > 0) {
                    sidBuf.append(", ");
                }
                sidBuf.append("?");
                q.add(v);
            }
            q.or().append("temp.sid").append(" IN (").append(sidBuf).append(")");
        }
    }
    public <T> Query  buildTradeTempQuery(Staff staff, String key, List<T> list){
        Query q = new Query();
        if (CollectionUtils.isNotEmpty(list)) {
            String collect = list.stream().map(a -> "?").collect(Collectors.joining(","));
            String tableName = key +"_temp";
            String sql = " SELECT "
                    +tableName+
                    ".sid FROM trade_" + staff.getDbInfo().getTradeDbNo() +" "
                    +tableName+
                    " WHERE "+tableName+".company_id =  " + staff.getCompanyId() +
                    " AND "+tableName+".enable_status > 0 AND "+tableName+"."+key+" IN("
                    + collect
                    + ")";
            q.append(sql);
            q.add(list);
        }
        return q;
    }

    /**
     *  此处的零时表不要冒然去掉，可以走buyer_nick，open_uid, tid索引。
     *  参考：https://gykj.yuque.com/entavv/eltb2u/up7m2a
     * @param staff
     * @param q
     * @param params
     * @return
     */
    @Override
    public Query buildBuyerNickQuery(Staff staff, Query q, TradeQueryParams params) {
        if (params.getBuyerNick() != null && params.getBuyerNick().length > 0) {
            List<String> nickList = new ArrayList<>();
            //nick + encode(nick)
            for (String s : params.getBuyerNick()) {
                nickList.add(s);
                nickList.add(secretBusiness.encodeStr(staff, s));
            }
            //openUid
            if (params.getOpenUid() != null) {
                for (String s : params.getOpenUid()) {
                    nickList.add(s);
                    nickList.add(secretBusiness.encodeStr(staff, s));
                }
            }
            q.setAllowTradeCountIndex(false);
            q.and().append("( t.sid in (select trade_temp.sid from (");
            Query buyer_nick = buildTradeTempQuery(staff, "buyer_nick", nickList);
            Query open_uid = buildTradeTempQuery(staff, "open_uid", nickList);
            q.append(buyer_nick.getQ()).add(buyer_nick.getArgs())
                    .append(" UNION ALL ")
                    .append(open_uid.getQ()).add(open_uid.getArgs());
            try {
                Query queryTradeExt = tradeExtBusiness.buildReceiverInfo(staff,params);
                if (queryTradeExt.getQ().length() > 0) {
                    q.append(" UNION ALL ").append(queryTradeExt.getQ()).add(queryTradeExt.getArgs());
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人姓名出错: " + e.getMessage()), e);
            }
            q.append(") as trade_temp))");
        }
        return q;
//
//            //sql主体结构
//            orderQry.append(" AND ( t.sid IN( select temp.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp where temp.company_id= " + staff.getCompanyId() + " and temp.enable_status > 0 ");
//            int c = 0;
//            for (String tempBuyerNick : params.getBuyerNick()) {
//                if (!(tempBuyerNick = tempBuyerNick.trim()).isEmpty()) {
//                    if (c == 0) {
//                        orderQry.append(" AND temp.buyer_nick IN(");
//                    }
//                    if (c > 0) {
//                        orderQry.append(", ");
//                    }
//                    orderQry.append("?, ?").add(tempBuyerNick).add(secretBusiness.encodeStr(staff, tempBuyerNick));
//                    c++;
//                }
//            }
//            if (c > 0) {
//                orderQry.append(")");
//            }
//            try {
//                List<Long> sidList = tradeExtBusiness.getSids4TradeExt(staff, params);
//                qOrSidList(orderQry, sidList);
//            }catch (Exception e) {
//                logger.error(LogHelper.buildLog(staff, "查询订单时加密收件人姓名出错: " + e.getMessage()), e);
//            }
//            orderQry.append("))");
//        }
    }

    private Query buildPromiseRangeQuery(Query q, Integer... promiseTypes) {
        if (promiseTypes == null || promiseTypes.length == 0) {
            return q;
        }
        StringBuilder tq = new StringBuilder();
        int validNum = 0;
        for (Integer type : promiseTypes) {
            String typeQuery = getTypeQuery(null, null, type, null);
            if (!typeQuery.isEmpty()) {
                Query.conjunct(tq, " OR ", validNum++ > 0);
                tq.append(typeQuery);
            }
        }
        return validNum > 1 ? q.and().append("(").append(tq).append(")") : (validNum == 1 ? q.and().append(tq) : q);
    }

    @Resource
    private TradeTypeConditionConverter tradeTypeConditionConverter;
    /**
     * 原代码已迁移至 HistoryQueryType , 合并代码时注意比对两部分的差异
     * @see com.raycloud.dmj.services.trades.support.search.type.HistoryQueryType
     * @return
     */
    private String getTypeQuery(Staff staff, Query q, Integer type, Long queryId) {
        return tradeTypeConditionConverter.getTypeQuery(staff, q, type, queryId);
    }

    private String buildPrintStatusQuery(Query q, TradeConfig tc) {
        String printStatus;
        if (tc.getOpenDeliverPrint() != null && tc.getOpenDeliverPrint() == 1) {
            printStatus = " AND (t.express_print_time > ? AND t.deliver_print_time > ?)";
            q.add(TradeTimeUtils.INIT_DATE).add(TradeTimeUtils.INIT_DATE);
        } else {
            printStatus = " AND t.express_print_time > ?";
            q.add(TradeTimeUtils.INIT_DATE);
        }
        return printStatus;
    }

    private String buildIsPackQuery(Query q, TradeConfig tc) {
        String isPack = "";
        if (tc.getOpenPackageExamine() != null && tc.getOpenPackageExamine() == 1) {
            isPack = " AND t.is_package = ?";
            q.add(1);
        }
        return isPack;
    }

    private String buildIsWeighQuery(Query q, TradeConfig tc) {
        String isWeigh = "";
        if (tc.getOpenPackageWeigh() != null && tc.getOpenPackageWeigh() == 1) {
            isWeigh = " AND t.is_weigh = ?";
            q.add(1);
        }
        return isWeigh;
    }

    private boolean _buildTypeQuery(Query q, TradeQueryParams params) {
        boolean isOutstock = false;
        //区分出库单
        if (params.getIsOutstock() != null) {
            if (params.getIsOutstock() == 1) {
                q.and().append("t.type like ?").add(TradeConstants.TYPE_TRADE_OUT + "%").and().append("t.user_id = 0");
                isOutstock = true;
            } else if (params.getIsOutstock() == 0) {
                q.and().append("t.type not like ?").add(TradeConstants.TYPE_TRADE_OUT + "%");
            }
        }
        if (params.getIsPresell() != null) {
            if (params.getIsPresell() == 1) {
                q.and().append("(").append("t.is_presell = ").append(params.getIsPresell());
                q.or().append("t.is_presell = ").append("3").append(")");
            } else if (params.getIsPresell() == 2) {
                q.and().append("t.is_presell = ").append(params.getIsPresell());
            } else if (params.getIsPresell() == 0) {
                q.and().append("(").append("t.is_presell <> 1");
                q.and().append("t.is_presell <> 3").append(")");
            } else if (params.getIsPresell() == 3) {
                q.and().append("(").append("t.is_presell = ").append("1");
                q.or().append("t.is_presell = ").append("3").append(")");
            }
        }
        return isOutstock;
    }

    private String[] getReceiverMobile(Staff staff, String receiverMobile) {
        String[] split = receiverMobile.split(",");
        List<String> receiverMobileList = Lists.newArrayListWithCapacity(split.length);

        for (String mobile : split) {
            receiverMobileList.add(mobile);
            receiverMobileList.add(secretBusiness.encodeNum(staff, mobile));
            List<String> values = commonTradeDecryptBusiness.encryptByUser(staff, mobile,3,null,true);
            if (CollectionUtils.isNotEmpty(values)) {
                receiverMobileList.addAll(values);
            }
        }
        return receiverMobileList.toArray(new String[0]);
    }

    private String[] getReceiverName(Staff staff, String receiverName) {
        List<String> receiverNameList = Lists.newArrayList();
        receiverNameList.add(receiverName);
        receiverNameList.add(secretBusiness.encodeStr(staff, receiverName));
        List<String> values = commonTradeDecryptBusiness.encryptByUser(staff, receiverName,2,null,true);
        if (CollectionUtils.isNotEmpty(values)) {
            receiverNameList.addAll(values);
        }
        return receiverNameList.toArray(new String[0]);
    }

    @Override
    public Query buildDeliverAndUploadQuery(Query q, Staff staff, TradeQueryParams params) {
        if (null != params.getQueryId() && params.getQueryId() == QUERY_DELIVER_EXCEPTION) {

            String statTable = "trade_stat_" + staff.getDbInfo().getTradeDbNo();
            Query statQuery = new Query();
            statQuery.append(" company_id = ").append(staff.getCompanyId());
            statQuery.and().append(" is_cancel = 0");
            statQuery.and().append(" enable_status = 1 and (deliver_excep = 1 or upload_excep = 1)");

            StringBuilder statSql = new StringBuilder().append("select sid from ").append(statTable).append(" where ").append(statQuery.getQ());

            q.and().append("t.sid").append(" IN (").append(statSql.toString()).append(")");
            q.setAllowTradeCountIndex(false);
        }
        return null;
    }

    @Override
    public Query buildConsignRecordQuery(Query q, Staff staff, TradeQueryParams params) {
        String uploadErrorTypeStr = StringUtils.join(params.getUploadErrorType(), ",");
        //exist查询合单
        Query existQuery = new Query();
        existQuery.append(" company_id = ").append(staff.getCompanyId());
        existQuery.and().append(String.format(" merge_sid = t.merge_sid and is_error = 1 and error_type in (%s)", uploadErrorTypeStr));
        String existQuerySql = String.format("select 1 from consign_record_%s where %s", staff.getDbInfo().getTradeDbNo(), existQuery.getQ());

        q.and().append("(")
                .append(String.format("IFNULL(cr.error_type,0) IN (%s)", uploadErrorTypeStr))
                .or().append("(").append(" t.merge_sid > 0").and().append(" EXISTS(").append(existQuerySql).append("))")
                .append(")");
        return null;
    }

    /**
     * SELECT t2.sid FROM order_182 t2
     * WHERE
     * t2.sid in (
     * SELECT t1.sid
     * FROM order_182 t1 WHERE t1.company_id = 19982
     * and t1.combine_id = 0
     * and t1.item_sys_id in (354126478324323,411087081140736,407337459114513)
     * and t1.sku_sys_id IN (354126478324331,411087625638411,407337459114517)
     * and not exists (
     * select 1 from order_182 o
     * where
     * o.sid = t1.sid
     * -- 					and o.combine_id = 0
     * and o.item_sys_id not in (354126478324323,411087081140736,407337459114513)
     * and o.sku_sys_id not in (354126478324331,411087625638411,407337459114517)
     * )
     * )
     * GROUP BY t2.sid
     * HAVING
     * count(distinct(t2.item_sys_id)) = 3;
     *
     * @param q
     * @param staff
     * @param params
     * @return
     */
    @Override
    public Query buildOnlyContainsQuery(Query q, Staff staff, TradeQueryParams params) {
        List<Long> sysSkuIds = new ArrayList<>();
        List<Long> sysItemIds = new ArrayList<>();
        if (null != params.getOnlyOuterIdAndSysSkuIds() && params.getOnlyOuterIdAndSysSkuIds().length > 0) {
            sysSkuIds = parseJsonToList(params.getOnlyOuterIdAndSysSkuIds());
            List<DmjSku> skus = itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId,sysSkuId");
            sysItemIds = skus.stream().map(x -> x.getSysItemId()).collect(Collectors.toList());
        }
        if (null != params.getOnlyOuterIdAndSysItemIds() && params.getOnlyOuterIdAndSysItemIds().length > 0) {
            List<Long> tempSysItemIds = parseJsonToList(params.getOnlyOuterIdAndSysItemIds());
            sysItemIds.addAll(tempSysItemIds);
        }


        onlySysOuterIdContains(q,staff,params);
        onlyContains(q,staff,sysItemIds,sysSkuIds,params.getOnlyOuterIdAndSysSkuIdType());
        return null;
    }


    /**
     *
     * 系统商家编码仅包含
     *  t.sid IN (
     *    select o.belong_sid from order_1 o
     *    WHERE o.belong_sid=t.sid AND o.enable_status >0
     *    GROUP BY o.belong_sid
     *    HAVING COUNT(DISTINCT CASE WHEN o.sys_outer_id IN ('abc','abd') THEN o.sys_outer_id END) = 2
     *     AND COUNT(DISTINCT o.sys_outer_id) = 2
     *  )
     *
     *
     * @param q
     * @param staff
     * @param params
     */
    private void onlySysOuterIdContains(Query q, Staff staff, TradeQueryParams params) {

        String[] sysOuterIds = params.getSysOuterIds();
        if(Objects.equals(params.getItemType(),5) && ArrayUtils.isNotEmpty(sysOuterIds)){
            String orderTable = q.getOrderTable() +"_" + staff.getDbInfo().getOrderDbNo();
            StringBuilder step1 = new StringBuilder().append(" AND t.sid IN ( select o.belong_sid from ");
            step1.append(orderTable).append(" o WHERE o.belong_sid=t.sid AND o.enable_status >0 AND combine_id=0 GROUP BY o.belong_sid  ");

            String inStr = Stream.of(sysOuterIds).map(tid -> "?").collect(Collectors.joining(",", "(", ")"));

            step1.append(" HAVING COUNT(DISTINCT CASE WHEN o.sys_outer_id IN ").append(inStr)
                    .append(" THEN o.sys_outer_id END) = ").append(sysOuterIds.length)
                    .append(" AND COUNT(DISTINCT o.sys_outer_id) = ")
                    .append(sysOuterIds.length)
                    .append(")");

            q.append(step1).add(sysOuterIds);

        }
    }

    @Override
    public Query buildAuthorInfoQuery(Query q, Staff staff, TradeQueryParams params) {
        List<String> authorIds = null;
        if (StringUtils.isNotBlank(params.getAuthorId())) {
            authorIds = Arrays.stream(params.getAuthorId().replaceAll("，", ",").split(",")).filter(s -> StringUtils.isNotBlank(s)).map(s -> s.trim()).collect(Collectors.toList());
//            authorIds.forEach(authorId -> {
//                if (!NumberUtils.isNumber(authorId)) {
//                    throw new IllegalArgumentException("达人ID只能输入数字");
//                }
//            });
        }
        List<String> authorNames = null;
        if (StringUtils.isNotBlank(params.getAuthorName())) {
            authorNames = Arrays.stream(params.getAuthorName().replaceAll("，", ",").split(",")).filter(s -> StringUtils.isNotBlank(s)).map(s -> s.trim()).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(authorIds) || CollectionUtils.isNotEmpty(authorNames)) {
            q.and().append(" EXISTS (select 1 from ")
                    .append(q.getOrderExtTable()).append("_").append(staff.getDbInfo().getOrderDbNo())
                    .append(" oe where t.sid = oe.sid AND t.company_id = oe.company_id");

            if (CollectionUtils.isNotEmpty(authorIds)) {
                String[] authorIdArr = new String[authorIds.size()];
                authorIds.toArray(authorIdArr);
                //mysql 针对数值类型字段查询  会自动把字符串自动截取  如 author_id = '123Ae'  会截取为  author_id = '123'
                List<String> longAuthorIds = authorIds.stream().filter(id -> NumberUtils.isNumber(id)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(longAuthorIds)){
                    //todo 过渡阶段  后续 author_id 废弃
                    q.append(" AND ( ").append(" oe.author_id > 0 ");//这里必须设置不等于0（实际数据是大于0的） 否则author_id = '字符串' 会默认把0都查出来
                    this.buildListQuery(q, "oe.author_id", longAuthorIds);
                    this.buildArrayOrQuery(q, "oe.author_no", authorIdArr);
                    q.append(")");
                } else {
                    this.buildListQuery(q, "oe.author_no", authorIds);
                }

            }
            if (CollectionUtils.isNotEmpty(authorNames)) {
                this.buildListQuery(q, "oe.author_name", authorNames);
            }
            q.append(")");
        }
        return q;
    }


    @Override
    public Query buildDistributorInfoQuery(Query q, Staff staff, TradeQueryParams params) {
        List<String> distributorIds = null;
        if (StringUtils.isNotBlank(params.getDistributorId())) {
            distributorIds = Arrays.stream(params.getDistributorId().replaceAll("，", ",").split(",")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
        }
        List<String> distributorNames = null;
        if (StringUtils.isNotBlank(params.getDistributorName())) {
            distributorNames = Arrays.stream(params.getDistributorName().replaceAll("，", ",").split(",")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(distributorIds) && CollectionUtils.isEmpty(distributorNames) && StringUtils.isBlank(params.getActivityName())) {
            return q;
        }

        q.and().append(" EXISTS (select 1 from trade_distributor_")
                .append(staff.getDbInfo().getTradeDbNo())
                .append(" oe where t.sid = oe.sid ");

        if (CollectionUtils.isNotEmpty(distributorIds)){
            this.buildListQuery(q, "oe.distributor_id", distributorIds);
        }

        if (StringUtils.isNotBlank(params.getActivityName()) || CollectionUtils.isNotEmpty(distributorNames)) {
            q.append(" AND ( ");

            boolean added = false;
            if (StringUtils.isNotBlank(params.getActivityName())) {
                if(Objects.equals(params.getQueryType(),1)){
                    q.append(" oe.distributor_name = ? ").add(params.getActivityName());
                }else {
                    q.append(" oe.distributor_name LIKE CONCAT('%',?, '%')").add(params.getActivityName());
                }
                added = true;
            }
            if (CollectionUtils.isNotEmpty(distributorNames)) {
                if (added) {
                    q.or();
                }
                if(Objects.equals(params.getQueryType(),1)){
                    String inStr = distributorNames.stream().map(i-> "?").collect(Collectors.joining(",","(",")"));
                    q.append(" oe.distributor_name in ").append(inStr).add(distributorNames);
                }else {
                    String orStr = distributorNames.stream().map(i-> " oe.distributor_name LIKE CONCAT('%',?, '%')").collect(Collectors.joining(" OR ","(",")"));
                    q.append(orStr).add(distributorNames);
                }
            }

            q.append(")");
        }

        q.append(")");
        return q;
    }

    /**
     * 物流预警
     * 未发货：订单付款后X小时未在系统内发货
     */
    @Override
    public Query buildLogisticsWarningQuery(Staff staff, Query q, TradeQueryParams params) {
        if (params.getLogisticsWarningStartTime() != null || params.getLogisticsWarningEndTime() != null) {
            buildDateRangeQuery(q, "t.pay_time", params.getLogisticsWarningStartTime(), params.getLogisticsWarningEndTime());
            if (params.getDeliveryStartTime() == null && params.getDeliveryEndTime() == null) {
                q.and().append("t.sys_status not in ('SELLER_SEND_GOODS','FINISHED','CLOSED','CANCEL')");
            }
        }
        // 发货即将超时,查询剩余发货时间在某个时间段的订单,timeout_action_time是订单承诺发货时间,不过滤系统的一些状态
        if (params.getDeliveryStartTime() != null || params.getDeliveryEndTime() != null) {
            buildDateRangeQuery(q, "t.timeout_action_time", params.getDeliveryStartTime(), params.getDeliveryEndTime());
            q.and().append("t.unified_status in ('WAIT_SEND_GOODS','SELLER_CONSIGNED_PART')");
        }
        return q;
    }

    @Override
    public Query buildExcludeTradeByOrderSysStatusQuery(Staff staff, Query q, TradeQueryParams params, List<String> sysStatus) {
        if (CollectionUtils.isEmpty(sysStatus)){
            return q;
        }
        q.and().append("NOT EXISTS (select 1 from ")
                .append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo())
                .append(" o where o.belong_sid = t.sid AND o.enable_status in (1,2) AND o.sys_status in (");
        for (int i = 0; i < sysStatus.size(); i++){
            String status = sysStatus.get(i);
            q.append("'").append(status).append("'");
            if (i < sysStatus.size() - 1){
                q.append(",");
            }
        }
        q.append("))");
        return q;
    }

    @Override
    public Query buildBtasPictureQualityResultQuery(Query q, Staff staff, TradeQueryParams params) {
        String result = params.getBtasPictureQualityResult();
        if (StringUtils.isBlank(result)) {
            return q;
        }
        if (!OrderUtils.BtasPictureQualityResultEnum.SUCCESS.descAndCode.equals(result) && !OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode.equals(result) && !OrderUtils.BtasPictureQualityResultEnum.UNDEFINED.descAndCode.equals(result) && !OrderUtils.BtasPictureQualityResultEnum.SELLER_SEND.descAndCode.equals(result)) {
            throw new IllegalArgumentException("无法识别的参数值：btasPictureQualityResult=" + result);
        }
        q.and().append(" EXISTS (select 1 from ")
                .append(q.getOrderExtTable()).append("_").append(staff.getDbInfo().getOrderDbNo())
                .append(" oe where t.sid = oe.sid AND t.company_id = oe.company_id");
        if (OrderUtils.BtasPictureQualityResultEnum.SUCCESS.descAndCode.equals(result)) {
            this.buildLikeQuery(q, 1, "oe.customization", OrderUtils.BtasPictureQualityResultEnum.SUCCESS.descAndCode);
        }
        if (OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode.equals(result)) {
            this.buildLikeQuery(q, 1, "oe.customization", OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode);
        }
        if (OrderUtils.BtasPictureQualityResultEnum.SELLER_SEND.descAndCode.equals(result)) {
            this.buildLikeQuery(q, 1, "oe.customization", OrderUtils.BtasPictureQualityResultEnum.SELLER_SEND.descAndCode);
        }
        if (OrderUtils.BtasPictureQualityResultEnum.UNDEFINED.descAndCode.equals(result)) {
            this.buildNotLikeQuery(q, 1, "oe.customization", OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode, OrderUtils.BtasPictureQualityResultEnum.SUCCESS.descAndCode);
        }
        q.append(")");
        return q;
    }

    @Override
    public Query buildBtasOrderCodeQuery(Query q, Staff staff, TradeQueryParams params) {
        if (StringUtils.isBlank(params.getBtasOrderCode())){
            return q;
        }
        q.and().append(" EXISTS (select 1 from ")
                .append(q.getOrderExtTable()).append("_").append(staff.getDbInfo().getOrderDbNo())
                .append(" oe where t.sid = oe.sid AND t.company_id = oe.company_id");
        //订单唯一码没有单独字段，只能单个进行模糊匹配
        this.buildLikeQuery(q, 1, "oe.customization", params.getBtasOrderCode());
        q.append(")");
        return q;
    }

    @Override
    public Query buildTradeSalesmanQuery(Query q, Staff staff, TradeQueryParams params) {
        Staff queryStaff = staffService.querySimpleStaffByName(staff, params.getSalesmanName());
        Long tradeSalesmanId = Objects.isNull(queryStaff) ? 0L : queryStaff.getId();
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" INNER JOIN (select t.sid from trade_salesman_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.salesman_id = ").append(tradeSalesmanId).append(" and t.merge_sid=-1 and s.enable_status=1 and t.enable_status=1 ");
        queryBuilder.append(" union ");
        queryBuilder.append(" select t.merge_sid sid from trade_salesman_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.salesman_id = ").append(tradeSalesmanId).append(" and t.merge_sid>-1 and s.enable_status=1 and t.enable_status>=1) tradeSalesman on t.sid=tradeSalesman.sid");
        q.getParamBeforeWhere().put("tradeSalesman", queryBuilder.toString());
        q.setTradeSalesman(true);
        return q;
    }

    @Override
    public Query buildOrderCaigouCodeQuery(Query q, Staff staff, TradeQueryParams params) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" INNER JOIN (select t.sid from order_caigou_").append(staff.getDbInfo().getOrderDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.code ='").append(params.getCaigouCode()).append("' ");
        queryBuilder.append(" and t.merge_sid=-1 and s.enable_status=1 and t.enable_status=1");
        queryBuilder.append(" union ");
        queryBuilder.append(" select t.merge_sid sid from order_caigou_").append(staff.getDbInfo().getOrderDbNo());
        queryBuilder.append(" s inner join ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
        queryBuilder.append(" t on s.company_id= t.company_id and s.sid=t.sid where s.company_id=").append(staff.getCompanyId());
        queryBuilder.append(" and s.code ='").append(params.getCaigouCode()).append("' ");
        queryBuilder.append(" and t.merge_sid>-1 and s.enable_status=1 and t.enable_status>=1) orderCaigou on t.sid=orderCaigou.sid");
        q.getParamBeforeWhere().put("orderCaigou", queryBuilder.toString());
        q.setOrderCaigou(true);
        return q;
    }

    /**
     *  outSidsRelSidSet是根据outSids获取到的一单多包关系的系统订单号
     *
     *  1.没有outSidsRelSidSet的话直接拼接t.out_sid
     *  2.否则 (t.out_id in ('a','b') or t.sid in(1,2))
     *
     * @param q
     * @param params
     * @return
     */
    @Override
    public Query buildOutSidQuery(Query q, TradeQueryParams params) {
        String[]  outSids = params.getOutSids();
        Set<Long> outSidsRelSidSet = params.getContext().getOutSidsRelSidSet();
        if(CollectionUtils.isEmpty(outSidsRelSidSet)){
            return buildArrayQuery(q, "t.out_sid", outSids);
        }

        String outIdInStr = "";
        if(outSids!=null && outSids.length>0){
            outIdInStr = Stream.of(outSids).map(tid -> "?").collect(Collectors.joining(",", "(", ")"));
        }
        String sidInStr = outSidsRelSidSet.stream().map(tid -> "?").collect(Collectors.joining(",", "(", ")"));

        // and (t.out_id in ('a','b') or t.sid in(1,2))
        if(StringUtils.isNotBlank(outIdInStr) && StringUtils.isNotBlank(sidInStr)){
            q.and().append(" (t.out_sid in ").append(outIdInStr).append("  or  ").append(" t.sid in ").append(sidInStr).add(outSids).add(outSidsRelSidSet.toArray(new Long[0])).append(") ");
        }
        return q;
    }


    private void handOuterIdAndIds(String[] values,StringBuilder stringBuilder,List<Object> list){
        if(ArrayUtils.isEmpty(values)){
            return;
        }
        List<Long> ids = parseJsonToList(values);
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        String inStr = ids.stream().map(tid -> "?").collect(Collectors.joining(",", "", ""));
        stringBuilder.append(inStr);
        list.addAll(ids);
    }

    @Override
    public List<Long> parseJsonToList(String[] values) {
        if (values == null) {
            return new ArrayList<>();
        }
        Set<Long> targetIds = new HashSet<>();
        for (int i = 0; i < values.length; i++) {
            try{
                Long temp = Long.valueOf(JSONObject.parseObject(values[i]).values().iterator().next().toString());
                targetIds.add(temp);
            }catch (Exception e){
                // 保证拼接的sql没有问题，新增一个不存的id
                targetIds.add(-2L);
            }
        }
        return Lists.newArrayList(targetIds);
    }

    /**
     * SELECT
     * t.*
     * FROM
     * trade_not_consign_34 t
     * inner join (
     * select step3.sid from order_134 step3
     * where step3.belong_sid in (
     * select step1.sid from order_134 step1
     * where step1.company_id = 13534 and step1.enable_status = 1 and step1.combine_id = 0
     * and step1.item_sys_id in ( 295576152551939,295576152551939 )
     * and step1.sku_sys_id in ( 295576152551941,295576152551942 )
     * and not exists (
     * select 1 from order_134 step2  where step2.belong_sid = step1.sid and step2.enable_status = 1
     * and (step2.item_sys_id not in ( 295576152551939,295576152551939 )  or step2.sku_sys_id not in ( 295576152551941,295576152551942 )  )  )
     * )  and step3.enable_status = 1 group by step3.belong_sid  having count(distinct(step3.sku_sys_id)) = 2) step4 on t.sid = step4.sid
     * where
     * 	t.company_id = 13534 AND (t.enable_status = 1) AND t.type not like 'trade_out%' AND (t.is_presell <> 1 AND t.is_presell <> 3) AND t.user_id IN(21193, 21190, 73120, *********) AND (t.sys_status = 'WAIT_BUYER_PAY' OR t.sys_status = 'WAIT_AUDIT' OR t.sys_status = 'WAIT_FINANCE_AUDIT' OR t.sys_status = 'FINISHED_AUDIT') AND t.is_cancel = 0 AND t.`pay_time` <= '2022-01-06 14:51:54' ORDER BY t.`pay_time` DESC;
     *
     *
     *
     * 同时包含
     SELECT *
     FROM trade_not_consign_43 t
     INNER JOIN
     (SELECT step3.belong_sid
     FROM order_13 step3
     WHERE step3.belong_sid IN
     (  SELECT step1.sid
     FROM order_13 step1
     WHERE step1.company_id = 1693
     AND step1.enable_status = 1
     AND step1.combine_id = 0
     AND step1.item_sys_id IN ( 491638167564800,451215858926080,451215889181184)
     AND  EXISTS
     (
     select 1 from (SELECT GROUP_CONCAT(DISTINCT step2.item_sys_id ORDER BY item_sys_id SEPARATOR ',') group_item_id
     FROM order_13 step2
     WHERE   step2.company_id = 1693
     AND step2.enable_status = 1
     AND step2.combine_id = 0
     AND step2.item_sys_id  IN (491638167564800,451215858926080,451215889181184 )
     GROUP BY  step2.belong_sid
     ) step10 where locate(group_item_id,'451215858926080,451215889181184,491638167564800')>0

     )
     )
     AND step3.enable_status = 1
     AND step3.combine_id = 0
     GROUP BY  step3.belong_sid
     HAVING count(distinct(step3.item_sys_id)) =3) step4
     ON t.sid = step4.belong_sid
     WHERE t.company_id = 1693
     AND (t.enable_status = 1)

     *    https://gykj.yuque.com/entavv/xb9xi5/xg3ykl
     *    包含：        订单商品和查询商品组合有交集，就可以查询到
     *    仅包含（任意）：订单商品是查询商品组合的子集，不能有设置外的商品。举例说明，若选择了A+B，如果订单包含商品A+B+C，则查询不到，如果订单商品是A或者B或者A+B则可以查询到
     *    仅包含（所有）：订单商品和查询商品完全一致。举例说明，若选择了A+B，订单商品必须是A+B，才可以查询到，否则查询不到
     *    同时包含：     查询商品是订单商品的子集。举例说明，若选择了A+B，订单商品如果是A+B+C，则可以查询出来，如果是A+C则查询不到
     *
     *    demo  查询 A+B    订单  T1:A, T2:B, T3:A+B, T4:A+C, T5: A+B+C T6:D
          结果------>
          包含：        T1,T2,T3,T4,T5
          仅包含（任意）：T1,T2,T3
          仅包含（所有）：T3
          同时包含：     T3,T5

     *
     *
     * @param q
     * @param staff
     * @param sysItemIds
     * @param sysSkuIds
     * @param onlyContainType  0仅包含-所有，1仅包含-任意，2同时包含 兼容原来的值，默认不传也是仅包含-所有，也就是0
     */
    private void onlyContains(Query q, Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds,Integer onlyContainType) {
        String orderTable = q.getOrderTable() +"_" + staff.getDbInfo().getOrderDbNo();
        if (CollectionUtils.isEmpty(sysItemIds) && CollectionUtils.isEmpty(sysSkuIds)) {
            return;
        }
        if (sysItemIds.size() > sysSkuIds.size() && sysSkuIds.size() > 0) {
            sysSkuIds.add(-1L);
            sysSkuIds.add(0L);
        }

        String aliasStep1 = "step1";
        String aliasStep2 = "step2";
        StringBuilder step1 = new StringBuilder().append(" select step1.sid from ")
                .append(orderTable).append(" step1")
                .append(" where step1.company_id = ").append(staff.getCompanyId())
                .append(" and step1.enable_status = 1")
                .append(" and step1.combine_id = 0");
        step1 = appendInSet(sysItemIds, sysSkuIds, step1,aliasStep1);

        if(!Objects.equals(onlyContainType,2)){
            StringBuilder step2 = new StringBuilder().append(" select 1 from ").append(orderTable).append(" step2 ")
                    .append(" where step2.belong_sid = step1.sid")
                    .append(" and step2.enable_status = 1")
                    .append(" and step2.company_id = ").append(staff.getCompanyId())
                    .append(" and step2.combine_id = 0");
            step2 = appendNotInSet(sysItemIds, sysSkuIds, step2,aliasStep2);
            step1.append(" and not exists (").append(step2).append(" ) ");
        }else {
            String groupItemField = "sku_sys_id";
            String searchItemIds="";
            if(sysItemIds.size()> sysSkuIds.size()){
                searchItemIds = sysItemIds.stream().sorted().map(Object::toString).collect(Collectors.joining(","));
                groupItemField = "item_sys_id";
            }else {
                searchItemIds = sysSkuIds.stream().sorted().map(Object::toString).collect(Collectors.joining(","));
            }

            StringBuilder step2 = new StringBuilder().append(" select step10.belong_sid from (SELECT step2.belong_sid,GROUP_CONCAT(DISTINCT ").append(groupItemField).append(" ORDER BY ").append(groupItemField).append(" SEPARATOR ',') group_item_id from ").append(orderTable).append(" step2 ")
                    .append(" where step2.company_id = ").append(staff.getCompanyId())
                    .append(" and step2.enable_status = 1")
                    .append(" and step2.combine_id = 0");
            appendInSet(sysItemIds, sysSkuIds, step2,aliasStep2);
            step2.append(" group by step2.belong_sid ) step10  where locate('").append(searchItemIds).append("',group_item_id)>0");
            step1.append(" and step1.belong_sid in (").append(step2).append(" ) ");
        }

        String countKey = new String();
        if (sysItemIds.size() > sysSkuIds.size()) {
            countKey = "item_sys_id";
        } else {
            countKey = "sku_sys_id";
        }

        StringBuilder step3 = new StringBuilder().append(" select step3.belong_sid from ")
                .append(orderTable).append(" step3")
                .append(" where step3.belong_sid in (").append(step1).append(" ) ")
                .append(" and step3.enable_status = 1")
                .append(" and step3.combine_id = 0")
                .append(" group by step3.belong_sid ")
                .append(" having count(distinct(step3.").append(countKey).append(")) ").append(ONLY_CONTAIN_TYPE_2_SYMBOL_MAP.getOrDefault(onlyContainType,"="));

        Integer kindCount = Math.max(sysItemIds.size(), sysSkuIds.size());
        step3.append(kindCount);
        q.getParamBeforeWhere().put("onlyContains", " inner join ( " + step3 + ") step4 on t.sid = step4.belong_sid ");
        q.setOnlyContains(true);
    }

    private StringBuilder appendInSet(List<Long> sysItemIds, List<Long> sysSkuIds, StringBuilder sb, String alias) {
        boolean ifSet = false;
        if (CollectionUtils.isNotEmpty(sysItemIds)) {
            ifSet = true;
            sb.append(" and ").append(alias).append(".item_sys_id in ( ").append(addCommas(sysItemIds)).append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(sysSkuIds)) {
            ifSet = true;
            sb.append(" and ").append(alias).append(".sku_sys_id in ( ").append(addCommas(sysSkuIds)).append(" ) ");
        }
        if(ifSet){
            sb.append(" and ").append(alias).append(TRADE_ORDER_SYS_STATUS_SQL);
        }
        return sb;
    }

    private StringBuilder appendNotInSet(List<Long> sysItemIds, List<Long> sysSkuIds, StringBuilder sb, String alias) {
        boolean ifSet = false;
        if (CollectionUtils.isNotEmpty(sysItemIds) && CollectionUtils.isNotEmpty(sysSkuIds)) {
            sb.append(" and (")
                    .append(alias).append(".item_sys_id not in ( ").append(addCommas(sysItemIds)).append(" ) ")
                    .append(" or ").append(alias).append(".sku_sys_id not in ( ").append(addCommas(sysSkuIds)).append(" ) ")
                    .append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(sysItemIds) && CollectionUtils.isEmpty(sysSkuIds)) {
            ifSet = true;
            sb.append(" and ").append(alias).append(".item_sys_id not in ( ").append(addCommas(sysItemIds)).append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(sysSkuIds) && CollectionUtils.isEmpty(sysItemIds)) {
            ifSet = true;
            sb.append(" and ").append(alias).append(".sku_sys_id not in ( ").append(addCommas(sysSkuIds)).append(" ) ");
        }
        if(ifSet){
            sb.append(" and ").append(alias).append(TRADE_ORDER_SYS_STATUS_SQL);
        }
        return sb;
    }

    private StringBuilder addCommas(List<Long> list) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(list.get(i));
        }
        return sb;
    }

    /**
     *
     * 把空格替换成%，多个空格也值替换成一个
     * a b c     --> a%b%c
     * a   b   c --> a%b%c
     *
     * @param input
     * @return
     */
    private static String replaceSpacesWithPercentage(String input) {
        if(StringUtils.isBlank(input) || !StringUtils.contains(input," ")){
            return input;
        }
        String[] words = StringUtils.split(input," ");
        return StringUtils.join(words,"%");
    }

    @Override
    public Query buildExtItemQuantityQuery(Query q, Staff staff, TradeQueryParams params) {
        if(null != params.getSmartItemNumStart()){
            q.and().append("CASE WHEN e.extra_fields is NOT NULL THEN json_extract(e.extra_fields,'$.itemQuantity') ELSE 0 END >= ").append(params.getSmartItemNumStart());
        }
        if(null != params.getSmartItemNumEnd()){
            q.and().append("CASE WHEN e.extra_fields is NOT NULL THEN json_extract(e.extra_fields,'$.itemQuantity') ELSE 0 END <= ").append(params.getSmartItemNumEnd());
        }
        return q;
    }

    @Autowired(required = false)
    private TaobaoReceiveCodeQuery taobaoReceiveCodeQuery;

    @Override
    public Query buildReceiveCodeQuery(Query q, Staff staff, TradeQueryParams params) {
        if (params == null || StringUtils.isBlank(params.getReceiveCode()) || q.isStopQuery()) {
            return q;
        }
        Map<String, List<Long>> userSourceMap = params.getContext().getUserSourceMap();
        List<String> list = taobaoReceiveCodeQuery.queryTidsByReceiveCodes(staff,userSourceMap, params.getReceiveCode());
        if (list == null) {
            return q;
        }
        if (list.size() == 0) {
            q.setStopQuery(true);
            return q;
        }
        q.and().append(" t.tid in (").append(list.stream().map(t -> "?").collect(Collectors.joining(","))).append(")").add(list);
        return q;
    }

    /**
     * 查询商品退款状态
     *
     * @param q
     * @param staff
     * @param params
     * @return
     */
    @Override
    public Query buildOrderRefundStatusQuery(Query q, Staff staff, TradeQueryParams params) {
        if (StringUtils.isNotEmpty(params.getOrderRefundStatus())) {
            String[] refundStatus = StringUtils.split(params.getOrderRefundStatus(), ",");
            String tradeTable = q.getTradeTable() + "_" + staff.getDbInfo().getTradeDbNo();
            String orderTable = q.getOrderTable() + "_" + staff.getDbInfo().getOrderDbNo();
            Query q1 = new Query();
            q1.append("( ")
                    .append(" EXISTS (")
                    .append("( SELECT 1 FROM " + orderTable + " o WHERE o.sid = t.sid AND o.company_id = t.company_id AND o.enable_status = 1 ")
                    .append("AND o.refund_status in ( ").append(Stream.of(refundStatus).map(t -> "?").collect(Collectors.joining(","))).append(" )").add(refundStatus)
                    .append(") UNION ALL ")
                    .append("( SELECT 1 FROM " + tradeTable + " tmp WHERE t.merge_sid > 0 AND tmp.merge_sid =t.sid AND tmp.company_id =t.company_id AND tmp.enable_status = 2 ")
                    .append("AND EXISTS ( SELECT 1 FROM " + orderTable + " o WHERE o.sid = tmp.sid AND o.company_id = tmp.company_id AND o.enable_status = 1 ")
                    .append("AND o.refund_status in ( ").append(Stream.of(refundStatus).map(t -> "?").collect(Collectors.joining(","))).append(" )").add(refundStatus)
                    .append(")")
                    .append(")")
                    .append(")")
                    .append(")");
            q.and().append(q1.toString());
        }
        return q;
    }

    @Override
    public Query buildVipStorageNoQuery(Query q, TradeQueryParams params) {
        buildLikeQuery(q, 1, "t.vip_storage_no", params.getVipStorageNo());
        if (StringUtils.isEmpty(params.getVipStorageNo()) && null != params.getContainOrExcludeVipStorageNo()) {
            if (params.getContainOrExcludeVipStorageNo() == 0) {
                //0.已匹配出仓单
                q.and().append("(t.vip_storage_no != '' and t.vip_storage_no is not null)");
            } else if (params.getContainOrExcludeVipStorageNo() == 1) {
                //1.未匹配出仓单
                q.and().append("(t.vip_storage_no is null)");
            }
        }
        return q;
    }

    @Override
    public Query buildLogisticsCodeQuery(Query q, TradeQueryParams params) {
        if (StringUtils.isNotBlank(params.getLogisticsCode())) {
            List<String> logisticsCodes = Strings.getAsStringList(params.getLogisticsCode(), ",", true);
            Set<String> logisticsCodeMap = new HashSet<>();
            ErpPlatformConfig erpPlatformConfig = ConfigHolder.ERP_PLATFORM_CONFIG;
            if (erpPlatformConfig != null && erpPlatformConfig.getLogisticsCodeValueMap() != null) {
                for (String logisticsCode : logisticsCodes) {
                    Set<String> codes = erpPlatformConfig.getLogisticsCodeValueMap().get(logisticsCode);
                    if (codes != null) {
                        logisticsCodeMap.addAll(codes);
                    }
                }
            }
            buildListQuery(q, "e.logistics_code", !logisticsCodeMap.isEmpty() ? new ArrayList<>(logisticsCodeMap) : logisticsCodes);
        }
        return q;
    }

    private void appendInClause(String fieldName, List<String> values, Query query) {
        if(CollectionUtils.isEmpty(values)){
            return;
        }
        if(StringUtils.isEmpty(fieldName)){
            return;
        }
        if(query==null){
            return;
        }
        int index = 0;
        StringBuilder placeholders = new StringBuilder();
        for (String value : values) {
            if (value == null) {
                continue;
            }
            if (index++ > 0) {
                placeholders.append(", ");
            }
            placeholders.append("?");
            query.add(value);
        }
        query.append(fieldName).append(" IN (").append(placeholders).append(") ");
    }

    private void appendLikeClause(String fieldName, List<String> values, Query query) {
        if(CollectionUtils.isEmpty(values)){
            return;
        }
        if(StringUtils.isEmpty(fieldName)){
            return;
        }
        if(query==null){
            return;
        }
        int index = 0;
        StringBuilder placeholders = new StringBuilder();
        for (String value : values) {
            if (value == null) {
                continue;
            }
            if (index++ > 0) {
                placeholders.append(" OR ");
            }
            placeholders.append(fieldName).append(" LIKE CONCAT('%', ?, '%') ");
            query.add(value);
        }
        query.append(" (").append(placeholders).append(") ");
    }

    /**
     * 商品范围查询
     *
     * @param staff
     * @param q
     * @param key
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    @Override
    public <T extends Number> Query buildOrderNumberRangeQuery(Staff staff, Query q, String key, T start, T end) {
        if (start == null && end == null) {
            return q;
        }
        q.and().append(" ((t.merge_sid = -1 ");
        q.and().append(" t.sid in (select sid from order_").append(staff.getDbInfo().getOrderDbNo()).append(" o where company_id = ").append(staff.getCompanyId()).append(" and enable_status > 0 and o.combine_id= 0 ");
        buildNumberRangeQuery(q, key, start, end);
        q.append("))");

        q.or().append(" (t.merge_sid > 0 and t.merge_sid in (select t.merge_sid FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t JOIN order_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.sid  where t.company_id = ").append(staff.getCompanyId()).append(" and t.enable_status in (1,2) and o.enable_status = 1 and t.merge_sid > 0 ");
        q.append(" and o.combine_id= 0 ");
        buildNumberRangeQuery(q, key, start, end);
        q.append(")))");
        return q;
    }

}

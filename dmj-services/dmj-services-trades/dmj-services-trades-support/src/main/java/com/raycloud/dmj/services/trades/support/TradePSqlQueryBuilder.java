package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.web.pgl.DbAdapter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class TradePSqlQueryBuilder extends TradeSqlQueryBuilder{

    @Resource
    DbAdapter dbAdapter;

    @Resource
    ITradeConfigService tradeConfigService;

    @Override
    public String outerIdLikeStr(Staff staff) {
        //todo  潜在问题  psql执行失败后会直接使用这个sql语句去mysql中执行
        Boolean allow = allowSearch(staff);
        return allow ? "LIKE CONCAT('%',?, '%')" : dbAdapter.filter(staff,
                //pgl
                () -> "LIKE CONCAT('%',?, '%')",
                //mysql
                () -> super.outerIdLikeStr(staff));
    }

    private Boolean allowSearch(Staff staff) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return tradeConfig != null && tradeConfig.getInteger(TradeExtendConfigsEnum.OUTID_LIKE.getKey()) == 1;
    }
}

package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.TradeCursorQueryRequest;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_CURSOR)
public class CursorConditionConverter extends AbsConditionConverter{
    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return condition instanceof TradeCursorQueryRequest;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        TradeCursorQueryRequest cursorRequest = (TradeCursorQueryRequest) condition;
        String field = condition.getTimeType().getField();

        String cursor = cursorRequest.getCursor();
        if (StringUtils.isNotBlank(cursor)) {
            Date time = null;
            Long sid= null;
            try {
                String[] split = cursor.split("_");
                time = new Date(Long.parseLong(split[0]));
                sid = Long.parseLong(split[1]);
            }catch (Exception e){
                throw new IllegalArgumentException("游标非法:" + cursor);
            }

            String order = cursorRequest.getOrder();
            if (StringUtils.isBlank(order)) {
                order = "desc";
            }
            String refIdField = "(CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END)";
            if ("asc".equalsIgnoreCase(order.trim())) {
                q.append(" AND ").append("((").append(refIdField).append(" > ? ").add(sid).append(" AND ").append("t.").append(field).append(" = ?").add(time).append(") ");
                q.append(" OR ").append("t.").append(field).append(" > ? ").add(time).append(")");
            }else {
                q.append(" AND ").append("((").append(refIdField).append(" < ? ").add(sid).append(" AND ").append("t.").append(field).append(" = ?").add(time).append(") ");
                q.append(" OR ").append("t.").append(field).append(" < ? ").add(time).append(")");
            }
        }
    }
}

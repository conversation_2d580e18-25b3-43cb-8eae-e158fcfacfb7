package com.raycloud.dmj.services.sensitive.platform;


import com.google.common.collect.Lists;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.sensitive.AbstractPlatformTradeSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.*;

import static com.raycloud.dmj.domain.utils.CommonConstants.*;

/**
 * 部分平台解密的通用解密
 */
@Service
public class PartPlatformTradeSecurityService extends AbstractPlatformTradeSecurityService {

    static final List<String> SUPPORT_PLATFORM = Lists.newArrayList(PLATFROM_TYPE_THH, PLAT_FORM_TYPE_KUAI_SHOU, PLAT_FORM_TYPE_SN, PLAT_FORM_TYPE_XHS);

    private final CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Autowired
    public PartPlatformTradeSecurityService(CommonTradeDecryptBusiness commonTradeDecryptBusiness) {
        this.commonTradeDecryptBusiness = commonTradeDecryptBusiness;
    }

    @Override
    public void doSensitiveTrades(Staff staff, List<Trade> trades) {
        commonTradeDecryptBusiness.batchSensitive(staff, trades);
    }

    @Override
    public List<Trade> filterTrades(Staff staff, List<Trade> trades) {
        return trades.stream().filter(trade -> SUPPORT_PLATFORM.contains(trade.getSource())).collect(Collectors.toList());
    }
}

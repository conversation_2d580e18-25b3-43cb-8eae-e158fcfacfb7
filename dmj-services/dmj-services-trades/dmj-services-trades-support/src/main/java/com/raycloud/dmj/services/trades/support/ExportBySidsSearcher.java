package com.raycloud.dmj.services.trades.support;

import com.google.api.client.util.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.trades.ExportParams;
import com.raycloud.dmj.domain.trades.TimeSlice;
import com.raycloud.dmj.domain.trades.TradeExportParams;
import com.raycloud.dmj.services.utils.LogKit;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public abstract class ExportBySidsSearcher<T> {

    public List<T> search(TradeExportParams params){
        if (params.isFirstFilter()){
            filter(params);
            params.setFirstFilter(false);
        }
        List<List<Long>> exportSidsPage = params.getExportSidsPage();
        Iterator<List<Long>> iterator = exportSidsPage.iterator();
        List<T> result = new ArrayList<>();
        while (iterator.hasNext()) {
            List<Long> next = iterator.next();
            if (CollectionUtils.isEmpty(next)){
                return null;
            }
            List<T> pageResult = query(next);

        }
        return Lists.newArrayList();
    }

    protected void filter(ExportParams params){
        return;
    }

    protected abstract List<T> query(List<Long> sids);


}

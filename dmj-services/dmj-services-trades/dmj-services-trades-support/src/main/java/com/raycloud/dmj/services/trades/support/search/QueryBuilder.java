package com.raycloud.dmj.services.trades.support.search;

import com.jdcloud.sdk.apim.utils.StringUtils;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.search.TimeTypeEnum;
import com.raycloud.dmj.domain.trades.search.TradeCursorQueryRequest;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.services.trades.support.search.convert.AbsConditionConverter;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConvertService;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertBase;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-31
 */
@Component
public class QueryBuilder {


    private static final Logger logger = Logger.getLogger(QueryBuilder.class);

    @Resource
    TradeSearchSupport tradeSearchSupport;

    @Resource
    ConditionConvertService conditionConverter;

    public  Query convert2Query(Staff staff, List<String> queryFields,TradeQueryRequest condition,boolean assemblyByMerge,boolean isCount) {
        Sort sort = condition.getSort();
        Page page = condition.getPage();
        ConvertContext context = new ConvertContext(staff);
        context.setCountQuery(isCount);
        context.setCursorQuery(condition instanceof TradeCursorQueryRequest);

        boolean deepSearch =(null != page && page.getPageNo() !=null && page.getPageNo() > 1 && MathUtils.multiply(page.getPageNo(),page.getPageSize()).longValue() > 10000);
        context.setInnerJoinTmp(deepSearch || assemblyByMerge);

        Query q = new Query();

        if(context.isCursorQuery()){
            if (condition.getTimeType() == null ) {
                condition.setTimeType(TimeTypeEnum.UPD_TIME);
            }
        }

        String table = ConditionConstants.TRADE_TABLE_PLACE_HOLDER;

        if (condition.getForceMaster() !=null && condition.getForceMaster()) {
            q.append(" /* FORCE_MASTER */");
        }
        q.append(" SELECT ");

        if (isCount) {
            if (assemblyByMerge) {
                q.append(" COUNT( DISTINCT( CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END ) ) ");
            }else {
                q.append(" COUNT(1) ");
            }
            q.append(" FROM ").append(table).append(" t ");
        }else {
            //如果是深翻页 使用 INNER JOIN  减少回表次数
            //如果是按合单组装数据 这里也需要 INNER JOIN 保证只查询到主订单 后面会补充子订单信息
            if (deepSearch || assemblyByMerge ) {
                if (deepSearch) {
                    new QueryLogBuilder(staff).format("深翻页查询 pageNo %s pageSize %s",page.getPageNo(),page.getPageSize()).printWarn(logger,new Exception("深翻页查询"));
                }
                appendQueryFields(queryFields, condition, q);

                q.append(" FROM ").append(table).append(" t ");
                if (assemblyByMerge) {
                    q.append(" INNER JOIN (SELECT DISTINCT( CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END ) as id FROM " ).append(table).append(" t ");
                }else {
                    q.append(" INNER JOIN (SELECT t.sid as id FROM " ).append(table).append(" t ");
                }
            }else {
                appendQueryFields(queryFields, condition, q);
                q.append(" FROM ").append(table).append(" t ");
            }
        }
        q.append(ConditionConstants.FORCE_INDEX_HOLDER);
        q.append(" WHERE t.company_id = ?").add(staff.getCompanyId());

        conditionConverter.convert(staff,context,condition,q);
        if (q.isStopQuery()) {
            return q;
        }

        String sortField = condition.getSort() !=null? condition.getSort().getField():null;
        String timeField = condition.getTimeType() !=null? condition.getTimeType().getField():null;
        tradeSearchSupport.forceIndexKey(staff,null,q, timeField,condition.getStartTime(),condition.getEndTime(),sortField,condition.getSysStatus());

        if (!isCount) {
            handlePageAndSort(condition, sort, page, q,assemblyByMerge);
        }

        if (deepSearch  || assemblyByMerge) {
            q.append(" ) tmp ON t.sid = tmp.id ");
        }

        /**
         * @see TbTradeSearchService#replaceHolder(Query, String)
         */
        q.setTradeTable(context.getTradeTable());
        q.setOrderTable(context.getOrderTable());

        return q;
    }


    private  void appendQueryFields(List<String> queryFields, TradeQueryRequest condition, Query q) {
        if (CollectionUtils.isNotEmpty(queryFields)) {
            queryFields = queryFields.stream().map(x->x.toLowerCase().trim()).collect(Collectors.toList());

            if (!queryFields.contains("sid") && !queryFields.contains("t.sid")) {
                queryFields.add("t.sid");
            }
            if (condition.getTimeType() != null){
                String field = condition.getTimeType().getField();
                if ( !queryFields.contains(field) && !queryFields.contains("t."+field)) {
                    queryFields.add("t." + field);
                }
            }
            q.append(String.join(",", queryFields));
        }else {
            q.append(" * ");
        }
    }

    private  void handlePageAndSort(TradeQueryRequest condition, Sort sort, Page page, Query q,boolean assemblyByMerge) {
        if (condition instanceof TradeCursorQueryRequest) {
            TradeCursorQueryRequest cursorRequest = (TradeCursorQueryRequest) condition;
            if (condition.getTimeType() == null ) {
                condition.setTimeType(TimeTypeEnum.UPD_TIME);
            }
            String field = condition.getTimeType().getField();

            q.append(" ORDER BY ").append(field).append(" ").append(cursorRequest.getOrder());
            if (assemblyByMerge) {
                q.append(",(CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END) ").append(cursorRequest.getOrder());
                //加了(CASE WHEN t.merge_sid > 0 THEN t.merge_sid ELSE t.sid END)的排序可能会导致索引走的不对 这里强制指定
                while(true){
                    if (StringUtils.isNotBlank(q.getTradeIndex())) {
                        break;
                    }
                    if (!TradeSearchSupport.TIME_FIELDS_INDEX_MAP.containsKey(field) || !ConvertBase.hasTimeCondition(condition,condition.getTimeType())) {
                        break;
                    }
                    if (ConvertBase.notEmpty(condition.getSids()) || ConvertBase.notEmpty(condition.getTids()) || ConvertBase.notEmpty(condition.getOutSids())) {
                        break;
                    }
                    q.setTradeIndex(" force index("+TradeSearchSupport.TIME_FIELDS_INDEX_MAP.get(field)+") ");
                    break;
                }
            }else {
                q.append(",sid ").append(cursorRequest.getOrder());
            }
            //多查一条以确认是否有下一页数据
            q.append(" LIMIT ").append(cursorRequest.getPageSize() + 1);
        }else {
            if (sort != null && sort.getField()!= null)  {
                q.append(" ORDER BY ").append(sort.getField());
                if (sort.getOrder() !=null) {
                    q.append(" ").append(sort.getOrder());
                }
            }else {
                if (condition.getTimeType() != null) {
                    q.append(" ORDER BY ").append(condition.getTimeType().getField());
                }
            }
            //多查一条以确认是否有下一页数据
            if (null != page) {
                q.append(" LIMIT ").append(page.getStartRow()).append(", ").append(page.getOffsetRow() + 1);
            }else {
                q.append(" LIMIT ").append(2001);
            }
        }
    }


}

package com.raycloud.dmj.services.trades.support.bigbag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.dao.trade.TradeCombineParcelDAO;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeParcelDAO;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.logistics.EnumCombineParcelNum;
import com.raycloud.dmj.domain.logistics.EnumShopeeTransitWarehouse;
import com.raycloud.dmj.domain.platform.trades.shopee.FirstMileCodeBindOrderResponse;
import com.raycloud.dmj.domain.platform.trades.shopee.FirstMileUnbindResponse;
import com.raycloud.dmj.domain.platform.trades.shopee.GenerateFMTrackingNoResponse;
import com.raycloud.dmj.domain.pt.UserLogisticsChannel;
import com.raycloud.dmj.domain.pt.UserLogisticsProvider;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.shopee.IShopeeFmService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.ILogisticsProviderService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import com.raycloud.dmj.domain.platform.trades.shopee.BindOrderList;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShopeeCombineParcelService extends CombineParcelAbstractHandler {

    private static final Logger logger = Logger.getLogger(ShopeeCombineParcelService.class);

    private static final int SHOPEE_LOGISTIC_ID = 813;

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    PlatformManagement platformManagement;

    @Resource
    IUserService userService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    ILogisticsProviderService logisticsProviderService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addParcelToCombine(Staff staff, List<Trade> tradesList, Long shopeeId) {
        for (Trade trade : tradesList) {
            if (!CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())) {
                throw new RuntimeException(String.format("订单号：【%s】非虾皮的订单", trade.getSid()));
            }
            if (trade.getTradeExt() == null) {
                throw new RuntimeException(String.format("订单号：【%s】存在订单没有附表信息", trade.getSid()));
            }
            if (trade.getTradeExt().getCombineParcelId() != null && trade.getTradeExt().getCombineParcelId() != 0) {
                throw new RuntimeException(String.format("订单号：【%s】为已上传的订单", trade.getSid()));
            }


        }

        //获取中转仓配置
        HashMap<Long, UserLogisticsProvider> providerConfig = getProviderConfig(staff);
        //key=平台-系统发货仓库
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : tradesList) {
            String key = trade.getSource() + trade.getWarehouseId();
            tradeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(trade);
        }
        TradeGroupParcelRequest request =new TradeGroupParcelRequest();
        request.setTaobaoId(shopeeId);
        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            List<Trade> tempTrades = entry.getValue();
            Map<String, Object> combineParcelCondition = new HashMap<>();
            combineParcelCondition.put("source", tempTrades.get(0).getSource());
            combineParcelCondition.put("consignWarehouseId", tempTrades.get(0).getWarehouseId());
            UserLogisticsProvider userLogisticsProvider = Optional.ofNullable(providerConfig.get(tempTrades.get(0).getTemplateId())).orElse(new UserLogisticsProvider());

            //校验是否有中转仓
            String transitWarehouse = checkTransitWarehouse(userLogisticsProvider);
            //根据来源，发货仓库和中转仓库查询大包列表
            List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryValidParcel(staff, combineParcelCondition);
            if (CollectionUtils.isEmpty(combineParcels)) {
                //如果没有查询到列表，则创建一个新的大包，将同一个key的trade放到一个大包中
                createNewCombineParcel(staff, tempTrades, request, transitWarehouse);
            } else {
                List<TradeCombineParcel> notEmptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() > 0).collect(Collectors.toList());
                //过滤大包中的小包数量是否大于0
                if (CollectionUtils.isNotEmpty(notEmptyCombineParcels)) {
                    //已存在大包数据
                    TradeCombineParcel notEmptyCombineParcel = notEmptyCombineParcels.get(0);
                    if ((EnumCombineParcelNum.SHOPEE.getMaxNum() - notEmptyCombineParcel.getParcelNum()) < tempTrades.size()) {
                        //一个大包中最多放入10000条小包数据，如果大包中数据超过10000条，则分多个包裹放入
                        int leftNum = EnumCombineParcelNum.SHOPEE.getMaxNum() - notEmptyCombineParcel.getParcelNum();
                        List<Trade> leftTrades = tempTrades.subList(0, leftNum);
                        List<Trade> nextTrades = tempTrades.subList(leftNum, tempTrades.size());
                        //当前包裹还能装下更多的小包订单，大包没有满
                        addToOldCombineParcel(staff, leftTrades, notEmptyCombineParcel);
                        //筛选当前大包中的包裹数量是否为0，如果为0则不为空，则还有其他大包未装满，填装其他大包中
                        List<TradeCombineParcel> emptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() == 0).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(emptyCombineParcels)) {
                            createNewCombineParcel(staff, nextTrades, request,transitWarehouse);
                        } else {
                            addToOldCombineParcel(staff, nextTrades, emptyCombineParcels.get(0));
                        }
                    } else {
                        addToOldCombineParcel(staff, tempTrades, notEmptyCombineParcel);
                    }
                } else {
                    addToOldCombineParcel(staff, tempTrades, combineParcels.get(0));
                }
            }
        }
    }

    private static String checkTransitWarehouse(UserLogisticsProvider userLogisticsProvider) {
        String config = userLogisticsProvider.getConfig();
        if(StringUtils.isEmpty(config)){
            throw new RuntimeException(String.format("物流商[%s]没有配置中转仓，请前往物流商设置配置中转仓",userLogisticsProvider.getName()));
        }
        JSONObject extraFields = JSONObject.parseObject(config);

        String transitWarehouse = extraFields.getString("transitWarehouse");
        if(StringUtils.isEmpty(transitWarehouse)){
            throw new RuntimeException(String.format("物流商[%s]没有配置中转仓，请前往物流商设置配置中转仓",userLogisticsProvider.getName()));

        }
        return transitWarehouse;
    }

    private HashMap<Long, UserLogisticsProvider> getProviderConfig(Staff staff) {
        HashMap<Long, UserLogisticsProvider> providerConfig = new HashMap<>(500);

        UserLogisticsProvider param = new UserLogisticsProvider();
        param.setEnableStatus(1);
        param.setType(1);
        List<UserLogisticsProvider> list = logisticsProviderService.list(staff, param);

        for (UserLogisticsProvider userLogisticsProvider : list) {
            List<UserLogisticsChannel> channels = userLogisticsProvider.getChannels();
            for (UserLogisticsChannel channel : channels) {
                providerConfig.put(channel.getTemplateId(), userLogisticsProvider);
            }
        }

        return providerConfig;
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeParcel(Staff staff, Long combineParcelId, Long[] parcelIds) {
        if (combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);

        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.stream().forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "shopee首公里预报移出大包：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });

    }

    /**
     * 生成系统日志
     *
     * @param staff
     * @param Sid
     * @param action
     * @param content
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel) throws Exception {
        User user = userService.queryByTaobaoId(staff.getCompanyId(), param.getTaobaoId());
        Assert.isTrue(user != null, "未找到该用户的店铺信息");
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{param.getId()});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询大包为空");
        }
        IShopeeFmService shopeeFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_SHOPEE, IShopeeFmService.class);
        UploadTradeParcelResult result = new UploadTradeParcelResult();
        if (TradeCombineParcel.EnumGatherType.PICK_UP.getType().equals(param.getGatherType())) {
            param.setGatherAddressType(1);
            //上门揽收
            handlePickUp(staff, param, combineParcel, user, shopeeFmService, result);
        } else {
            //快递寄送
            handleDropOff(staff, param, combineParcel, user, shopeeFmService, result);
        }

        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_UPLOADED);
        }
        if (CollectionUtils.isNotEmpty(result.getFailList())) {
            updateParcelStatus(staff, result.getFailList(), TradeParcel.UPLOAD_STATUS_FAIL);
        }

        return result;
    }

    private void handleDropOff(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel, User user, IShopeeFmService shopeeFmService, UploadTradeParcelResult result) throws Exception {
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查询小包为空");
        }
        if (combineParcel.getTemplateId() == null || StringUtils.isBlank(combineParcel.getTrackingNo())) {
            throw new RuntimeException("组包快递模版或快递单号为空");
        }
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, combineParcel.getTemplateId(), false);

        if (userWlbExpressTemplate == null) {
            throw new RuntimeException("快递模版不存在");
        }
        ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(userWlbExpressTemplate.getExpressId());
        if (expressCompany == null || expressCompany.getShopeeLogisticId() == null || expressCompany.getShopeeLogisticId() == 0) {
            throw new RuntimeException("快递公司不存在或shopee物流id为空");
        }
        List<TradeParcel> successList = new ArrayList<>();
        List<TradeParcel> failList = new ArrayList<>();

        Map<Long, List<TradeParcel>> tradeMap = parcels.stream().collect(Collectors.groupingBy(TradeParcel::getTaobaoId));
        Map<Long, User> userCacheMap = new HashMap<>();
        for (Map.Entry<Long, List<TradeParcel>> entry : tradeMap.entrySet()) {
            User tradeUser = userCacheMap.computeIfAbsent(entry.getKey(), taobaoId -> userService.queryByTaobaoId(staff.getCompanyId(), taobaoId));
            List<TradeParcel> parcelList = entry.getValue();
            int pageSize;
            if (parcelList.size() % 50 == 0) {
                pageSize = parcelList.size() / 50;
            } else {
                pageSize = parcelList.size() / 50 + 1;
            }
            for (int i = 0; i < pageSize; i++) {
                if (i == pageSize - 1) {
                    bindOrderSinglePage(staff,param, tradeUser, param.getTrackingNo(), parcelList.subList(i * 50, parcelList.size()), expressCompany.getShopeeLogisticId(), shopeeFmService, successList, failList);
                } else {
                    bindOrderSinglePage(staff,param, tradeUser, param.getTrackingNo(), parcelList.subList(i * 50, i * 50 + 50), expressCompany.getShopeeLogisticId(), shopeeFmService, successList, failList);
                }
            }
        }
        result.setSuccessList(successList);
        result.setFailList(failList);
        TradeCombineParcel update = new TradeCombineParcel();
        update.setId(param.getId());
        //如果不存在失败数据修改状态为已上传,否则为上传失败，未上传
        if (CollectionUtils.isEmpty(result.getFailList())) {
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
        } else {
            //如果失败数据不为空
            if (parcels.size() == result.getFailList().size()) {
                //全部失败，未上传
                update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
            } else {
                //部分失败
                update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            }
        }
        update.setGatherType(param.getGatherType());
        update.setTaobaoId(param.getTaobaoId());
        tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
        //打印组包上传日志
        printPlatformBatchNo(successList, failList, staff, update);
    }

    private void updateParcelStatus(Staff staff, List<TradeParcel> parcels, int uploadStatus) {
        List<TradeParcel> parcelUpdates = parcels.stream().map(x -> {
            TradeParcel parcelUpdate = new TradeParcel();
            parcelUpdate.setId(x.getId());
            parcelUpdate.setUploadStatus(uploadStatus);
            return parcelUpdate;
        }).collect(Collectors.toList());

        tradeParcelDAO.batchUpdate(staff, parcelUpdates);
    }

    private void handlePickUp(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel, User user, IShopeeFmService shopeeFmService, UploadTradeParcelResult result) throws Exception {
        if (param.getGatherAddressType() == null) {
           param.setGatherAddressType(1);
        }
        if (param.getGatherTime() == null) {
            param.setGatherTime(new Date());
        }
        String fmTn = "";
        if (StringUtils.isEmpty(combineParcel.getPlatformBatchNo())) {
            Map<String, String> addressMap = new HashMap<>();
            Warehouse warehouse = warehouseService.queryById(combineParcel.getConsignWarehouseId());
            if (param.getGatherAddressType() == 1) {
                String state = warehouse.getState() == null ? "" : warehouse.getState();
                String city = warehouse.getCity() == null ? "" : warehouse.getCity();
                String district = warehouse.getDistrict() == null ? "" : warehouse.getDistrict();
                String addressDetail = warehouse.getAddress() == null ? "" : warehouse.getAddress();
                addressMap.put("address", state + city + district + addressDetail);
                addressMap.put("name", warehouse.getContact());
                addressMap.put("zipcode", warehouse.getAddressCode());
                addressMap.put("phone", warehouse.getContactPhone());
            }
            GenerateFMTrackingNoResponse response;
            try {
                response = shopeeFmService.generateFMTrackingNo(user, DateFormatUtils.format(param.getGatherTime(), "yyyy-MM-dd"), 1, JSON.toJSONString(addressMap));
            } catch (Exception e) {
                logger.error("生成批次号失败:" + e.getMessage(), e);
                throw e;
            }
            if (CollectionUtils.isEmpty(response.getFmTnList())) {
                throw new RuntimeException("生成的fmTn为空");
            }
            fmTn = response.getFmTnList().get(0);
        } else {
            fmTn = combineParcel.getPlatformBatchNo();
        }
        List<TradeParcel> successList = new ArrayList<>();
        List<TradeParcel> failList = new ArrayList<>();
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
        parcels.addAll(tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, TradeParcel.UPLOAD_STATUS_FAIL));

        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查询小包为空");
        }
        Map<Long, List<TradeParcel>> tradeMap = parcels.stream().collect(Collectors.groupingBy(TradeParcel::getTaobaoId));
        for (Map.Entry<Long, List<TradeParcel>> entry : tradeMap.entrySet()) {
            User tradeUser = userService.queryByTaobaoId(staff.getCompanyId(), entry.getKey());
            List<TradeParcel> parcelList = entry.getValue();
            int pageSize;
            if (parcelList.size() % 50 == 0) {
                pageSize = parcelList.size() / 50;
            } else {
                pageSize = parcelList.size() / 50 + 1;
            }
            for (int i = 0; i < pageSize; i++) {
                if (i == pageSize - 1) {
                    bindOrderSinglePage(staff,param, tradeUser, fmTn, parcelList.subList(i * 50, parcelList.size()), SHOPEE_LOGISTIC_ID, shopeeFmService, successList, failList);
                } else {
                    bindOrderSinglePage(staff,param, tradeUser, fmTn, parcelList.subList(i * 50, i * 50 + 50), SHOPEE_LOGISTIC_ID, shopeeFmService, successList, failList);
                }
            }
        }
        result.setSuccessList(successList);
        result.setFailList(failList);
        TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
        combineParcelUpdate.setGatherType(param.getGatherType());
        combineParcelUpdate.setId(combineParcel.getId());

        //如果不存在失败数据修改状态为已上传,否则为上传失败，未上传
        if (CollectionUtils.isEmpty(result.getFailList())) {
            combineParcelUpdate.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
        } else {
            //如果失败数据不为空
            if (parcels.size() == result.getFailList().size()) {
                //全部失败，未上传
                combineParcelUpdate.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
            } else {
                //部分失败
                combineParcelUpdate.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            }
        }

        combineParcelUpdate.setPlatformBatchNo(fmTn);
        combineParcelUpdate.setTaobaoId(param.getTaobaoId());
        tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(combineParcelUpdate));
        //打印组包上传日志
        printPlatformBatchNo(successList, failList, staff, combineParcelUpdate);
    }

    private void bindOrderSinglePage(Staff staff,TradeCombineParcel param, User user, String fmTn, List<TradeParcel> parcels, Integer logisticId, IShopeeFmService shopeeFmService, List<TradeParcel> successList, List<TradeParcel> failList) throws Exception {
        List<BindOrderList> orderLists = getBindOrderLists(staff, parcels);
        if (CollectionUtils.isEmpty(orderLists)) {
            return;
        }
        FirstMileCodeBindOrderResponse response = shopeeFmService.firstMileCodeBindOrder(
                user,
                fmTn,
                TradeCombineParcel.EnumGatherType.getCodeByType(param.getGatherType()),
                orderLists,
                logisticId);

        if (response != null && CollectionUtils.isNotEmpty(response.getSuccessInfos())) {
            List<String> successTidList = response.getSuccessInfos().stream().map(FirstMileCodeBindOrderResponse.SuccessInfo::getOrdersn).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successTidList)) {
                List<TradeParcel> successParcels = parcels.stream().filter(x -> successTidList.contains(x.getTid())).collect(Collectors.toList()) ;
                successList.addAll(successParcels);
            }
        }

        if (response != null && CollectionUtils.isNotEmpty(response.getFailInfos())) {
            Map<String, String> failTidReasonMap =
                    response.getFailInfos().stream().collect(Collectors.toMap(FirstMileCodeBindOrderResponse.FailInfo::getOrdersn, FirstMileCodeBindOrderResponse.FailInfo::getReason));

            if (MapUtils.isNotEmpty(failTidReasonMap)) {
                List<TradeParcel> failParcels = parcels.stream().filter(x -> failTidReasonMap.containsKey(x.getTid())).collect(Collectors.toList());
                for (TradeParcel failParcel : failParcels) {
                    failParcel.setReason(failTidReasonMap.get(failParcel.getTid()));
                }
                failList.addAll(failParcels);
            }
        }
    }

    @NotNull
    public List<BindOrderList> getBindOrderLists(Staff staff, List<TradeParcel> parcels) {
        if(CollectionUtils.isEmpty(parcels)){
            return Collections.EMPTY_LIST;
        }
        List<TradeExt> tradeExts = tradeExtDao.tradeExtsGetBySids(staff, parcels.stream().map(TradeParcel::getSid).collect(Collectors.toList()));
        Map<Long,TradeExt>  tradeExtHashMap=new HashMap<>(tradeExts.size());
        if(CollectionUtils.isNotEmpty(tradeExts)){
            tradeExtHashMap = tradeExts.stream().collect(Collectors.toMap(TradeExt::getSid, Function.identity(), (k1, k2) -> k1));
        }
        List<BindOrderList> orderLists=new ArrayList<BindOrderList>();
        for (TradeParcel parcel: parcels) {
            BindOrderList orderList =new BindOrderList();
            orderList.setOrderNo(parcel.getTid());
            orderList.setPackageNumber(Optional.ofNullable(tradeExtHashMap.get(parcel.getSid())).orElse(new TradeExt()).getPackageNumber());
            orderLists.add(orderList);
        }
        return orderLists;
    }

    @Transactional(rollbackFor = Exception.class)
    public CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception {
        if (taobaoId == null || combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        CancelUploadTradeParcelResult result = new CancelUploadTradeParcelResult();
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查询的小包为空");
        }
        List<TradeParcel> filterParcels = parcels.stream()
                .filter(x -> TradeParcel.UPLOAD_STATUS_UPLOADED == x.getUploadStatus() && combineParcelId.equals(x.getCombineParcelId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterParcels)) {
            return result;
        }
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询的大包为空");
        }
        TradeCombineParcel combineParcel = combineParcels.get(0);
        String fmTn = TradeCombineParcel.EnumGatherType.PICK_UP.getType().equals(combineParcel.getGatherType()) ? combineParcel.getPlatformBatchNo() : combineParcel.getTrackingNo();
        User user = userService.queryByTaobaoId(staff.getCompanyId(), taobaoId);
        Assert.notNull(user, "未找到店铺信息，taobaoId:" + taobaoId);
        IShopeeFmService shopeeFmService = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_SHOPEE, IShopeeFmService.class);
        int pageSize;
        if (parcels.size() % 50 == 0) {
            pageSize = filterParcels.size() / 50;
        } else {
            pageSize = filterParcels.size() / 50 + 1;
        }

        for (int i = 0; i < pageSize; i++) {
            if (i == pageSize - 1) {
                List<TradeParcel> subList = filterParcels.subList(i * 50, parcels.size());
                FirstMileUnbindResponse response = shopeeFmService.firstMileUnbind(user, fmTn, subList.stream().map(TradeParcel::getTid).collect(Collectors.toList()));
                buildUnbindParcelResult(result, response, subList);
            } else {
                List<TradeParcel> subList = filterParcels.subList(i * 50, i * 50 + 50);
                FirstMileUnbindResponse response = shopeeFmService.firstMileUnbind(user, fmTn, subList.stream().map(TradeParcel::getTid).collect(Collectors.toList()));
                buildUnbindParcelResult(result, response, subList);
            }
        }
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_CANCELLED);
        }
        List<TradeParcel> cancelledParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_CANCELLED);
        if (CollectionUtils.isNotEmpty(cancelledParcels) && cancelledParcels.size() == combineParcel.getParcelNum()) {
            TradeCombineParcel update = new TradeCombineParcel();
            update.setId(combineParcelId);
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_CANCELLED);
        }
        //将小包移出大包
        removeParcel(staff, combineParcelId, parcelIds);
        return result;
    }

    @Override
    @Transactional
    public TradeCombineParcelResponse getPlatformBatchNoPrintData(TradeCombineParcel tradeCombineParcel, Map<Long, Warehouse> warehouseMap, Staff staff ,String subbagId) {
        // 打印前如果大包中有待上传的小包将其上传
        eventCenter.fireEvent(this, new EventInfo("combineParcel.print.upload").setArgs(new Object[]{staff, tradeCombineParcel}), null);
        // 打印时大包状态如果为待出库，则打印时将其出库
        if (tradeCombineParcel.getStatus() == TradeCombineParcel.STATUS_TO_OUTBOUND) {
            updateCombineStatus(staff, Arrays.asList(tradeCombineParcel), TradeCombineParcel.STATUS_OUTBOUNDED);
        }
        TradeCombineParcelResponse response = new TradeCombineParcelResponse();
        response.setPlatformBatchNo(tradeCombineParcel.getPlatformBatchNo());
        Warehouse warehouse = warehouseMap.get(tradeCombineParcel.getConsignWarehouseId());
        String state = warehouse.getState() == null ? "" : warehouse.getState();
        String city = warehouse.getCity() == null ? "" : warehouse.getCity();
        String district = warehouse.getDistrict() == null ? "" : warehouse.getDistrict();
        String addressDetail = warehouse.getAddress() == null ? "" : warehouse.getAddress();
        response.setConsignName(warehouse.getContact());
        response.setConsignAddress(state + city + district + addressDetail);
        response.setZipcode("311100");
        response.setConsignPhone(warehouse.getContactPhone());
        return response;
    }

    @Override
    @Transactional
    public void addParcelToCombineAndSplit(Staff staff, List<Trade> tradeList, TradeGroupParcelRequest request) {
        if((tradeList.size()/request.getPackageNum())> EnumCombineParcelNum.SHOPEE.getMaxNum()){
            throw new RuntimeException("预计生成的大包数量不够装载所选小包数量，请重新填写要生成的大包数量");
        }
        //获取中转仓配置
        HashMap<Long, UserLogisticsProvider> providerConfig = getProviderConfig(staff);
        List<List<Trade>> splitLists = CombineParceUtils.averageAssign(tradeList, request.getPackageNum());
        for (int i = 0; i < splitLists.size(); i++) {
            List<Trade> trades = splitLists.get(i);
            UserLogisticsProvider userLogisticsProvider = providerConfig.get(trades.get(0).getTemplateId());
            String transitWarehouse = checkTransitWarehouse(userLogisticsProvider);
            createNewCombineParcel(staff, trades, request, transitWarehouse);
        }
    }

    private void buildUnbindParcelResult(CancelUploadTradeParcelResult result, FirstMileUnbindResponse response, List<TradeParcel> parcels) {
        if (CollectionUtils.isNotEmpty(response.getFailInfos())) {
            Map<String, String> failTidReasonMap = response.getFailInfos().stream().collect(Collectors.toMap(FirstMileUnbindResponse.FailInfo::getOrdersn, FirstMileUnbindResponse.FailInfo::getReason));
            if (MapUtils.isNotEmpty(failTidReasonMap)) {
                List<TradeParcel> failParcels = parcels.stream().filter(x -> failTidReasonMap.containsKey(x.getTid())).collect(Collectors.toList());
                for (TradeParcel failParcel : failParcels) {
                    failParcel.setReason(failTidReasonMap.get(failParcel.getTid()));
                }
                result.setFailList(failParcels);
            }
        }

        if (CollectionUtils.isNotEmpty(response.getSuccessInfos())) {
            List<String> successTids = response.getSuccessInfos().stream().map(FirstMileUnbindResponse.SuccessInfo::getOrdersn).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successTids)) {
                result.setSuccessList(parcels.stream().filter(x -> successTids.contains(x.getTid())).collect(Collectors.toList()));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelCombineParcel(Staff staff, TradeCombineParcel parcel) throws Exception {
        //订单状态为已出库的，不能进行取消操作
        if (TradeCombineParcel.STATUS_OUTBOUNDED == parcel.getStatus()) {
            throw new RuntimeException("所选择大包状态不是待出库，请重新选择！");
        }
        List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
        //判断如果当前大包中未存在小包，则直接置为取消状态
        if (parcelList.size() == 0) {
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
        } else {
            //当前大包中存在小包,并且状态为已上传的小包
            List<TradeParcel> uploadList = parcelList.stream().filter(x -> (x.getUploadStatus() == TradeParcel.UPLOAD_STATUS_UPLOADED)).collect(Collectors.toList());
            //待上传 和 已取消的小包
            List<TradeParcel> toUploadList = parcelList.stream().filter(x -> (x.getUploadStatus() != TradeParcel.UPLOAD_STATUS_UPLOADED)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uploadList)) {
                //有已上传状态的小包，调用接口，取消首公里绑定
                Map<Long, List<TradeParcel>> parcelMap = uploadList.stream().collect(Collectors.groupingBy(TradeParcel::getTaobaoId));
                for (Map.Entry<Long, List<TradeParcel>> entry : parcelMap.entrySet()) {
                    Map<Long, List<TradeParcel>> combineMap = entry.getValue().stream().collect(Collectors.groupingBy(TradeParcel::getCombineParcelId));
                    for (Map.Entry<Long, List<TradeParcel>> temp : combineMap.entrySet()) {
                        cancelUploadParcel(staff, entry.getKey(), temp.getKey(), temp.getValue().stream().map(TradeParcel::getId).toArray(Long[]::new));
                    }
                }
            }
            // 待上传组包，移出组包
            if (CollectionUtils.isNotEmpty(toUploadList)) {
                //将小包移出大包
                removeParcel(staff, parcel.getId(), toUploadList.stream().map(TradeParcel::getId).toArray(Long[]::new));
            }
            //更改大包状态为已取消
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
        }
    }

    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
    }

    @Transactional(rollbackFor = Exception.class)
    public void outboundCombineParcel(Staff staff, Long[] combineParcelIds) {
        if (combineParcelIds == null || combineParcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelIds);
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("没有可用的组包");
        }
        List<TradeCombineParcel> filterCombineParcels = combineParcels.stream()
                .filter(x -> (TradeCombineParcel.STATUS_TO_OUTBOUND == x.getStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterCombineParcels)) {
            throw new RuntimeException("没有可用的组包");
        }
        updateCombineStatus(staff, filterCombineParcels, TradeCombineParcel.STATUS_OUTBOUNDED);
    }

    private void createNewCombineParcel(Staff staff, List<Trade> trades,TradeGroupParcelRequest request,String transitWarehouse) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        if (request != null && request.getTaobaoId() != null) {
            combineParcel.setTaobaoId(request.getTaobaoId());
            combineParcel.setGatherType(request.getGatherType());
            combineParcel.setTemplateId(request.getTemplateId());
        }else{
            combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        }
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseId(transitWarehouse);
        combineParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(transitWarehouse,null));
        combineParcel.setTransferWarehouseAddress(trades.get(0).getTradeExt().getWarehouseAddress());
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);

        tradeCombineParcelDAO.insert(staff, combineParcel);

        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);

        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);

            //添加系统日志
            String action = "移入:";
            String content = "shopee首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);

    }

    private void addToOldCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        List<TradeParcel> oldTradeParcels = tradeParcelDAO.queryBySidsAndCombineParcelId(staff, trades.stream().map(Trade::getSid).toArray(Long[]::new), combineParcel.getId());
        if (CollectionUtils.isNotEmpty(oldTradeParcels)) {
            StringBuilder builder = new StringBuilder();
            for (TradeParcel oldTradeParcel : oldTradeParcels) {
                builder.append(oldTradeParcel.getSid()).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            throw new RuntimeException("[" + builder.toString() + "]对应的小包已存在");
        }
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcel.getId());
        tradeParcelDAO.batchInsert(staff, parcels);
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcel.getId());
            //添加系统日志
            String action = "移入:";
            String content = "shopee首公里预报移入大包：" + combineParcel.getId();
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        TradeCombineParcel increase = new TradeCombineParcel();
        increase.setId(combineParcel.getId());
        increase.setParcelNum(combineParcel.getParcelNum() + parcels.size());
        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(increase));
        if (TradeCombineParcel.UPLOAD_STATUS_UPLOADED == combineParcel.getUploadStatus()) {
            TradeCombineParcel update = new TradeCombineParcel();
            update.setId(combineParcel.getId());
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
        }
    }

    private List<TradeParcel> buildParcelsCreate(List<Trade> trades, Long combineParcelId) {
        return trades.stream().map(x -> {
            TradeParcel parcel = new TradeParcel();
            parcel.setCombineParcelId(combineParcelId);
            parcel.setTaobaoId(x.getTaobaoId());
            parcel.setShippingCarrier(x.getTradeExt().getShippingCarrier());
            if (x.getTradeExt().getLogisticId() != null) {
                parcel.setLogisticId(Integer.valueOf(x.getTradeExt().getLogisticId().intValue()));
            }
            parcel.setSid(x.getSid());
            parcel.setTid(x.getTid());
            parcel.setOutSid(x.getOutSid());
            parcel.setWeight(x.getWeight());
            parcel.setNetWeight(x.getNetWeight());
            parcel.setVolume(x.getVolume());
            parcel.setUploadStatus(TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
            return parcel;
        }).collect(Collectors.toList());
    }

    /**
     * 组包上传打印日志，记录揽货批次号
     * <AUTHOR>
     * @date 2022/1/16 下午2:42
     * @param successList 上传成功的订单
     * @param failList 上传失败的订单
     * @param tradeCombineParcel 组包信息
     */
    void printPlatformBatchNo(List<TradeParcel> successList, List<TradeParcel> failList, Staff staff, TradeCombineParcel tradeCombineParcel) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<Long> failSid = new ArrayList<>();
        //添加上传失败的订单日志
        for (TradeParcel tradeParcel : failList) {
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), tradeParcel.getSid(), "shopee首公里预报", staff.getName(), new Date(), "shopee首公里预报失败，失败原因：" + tradeParcel.getReason());
            tradeTraces.add(tradeTrace);
            failSid.add(tradeParcel.getSid());
        }
        //添加上传成功的订单日志
        successList.stream().filter(item -> !failSid.contains(item.getSid())).forEach(item ->{
            String content;
            if(TradeCombineParcel.EnumGatherType.PICK_UP.getType().equals(tradeCombineParcel.getGatherType())) {
                //上门揽收
                content = "shopee首公里预报成功，揽货批次号：" + tradeCombineParcel.getPlatformBatchNo();
            } else {
                //快递寄送
                content = "shopee首公里预报成功：(快递寄送)";
            }
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), item.getSid(), "shopee首公里预报", staff.getName(), new Date(), content);
            tradeTraces.add(tradeTrace);
        });
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return;
        }
        try {
            //入库
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "入库组包上传日志时抛出异常"));
        }
    }



    @Override
    public void afterPropertiesSet() throws Exception {
        CombineParcelFactory.register(CommonConstants.PLAT_FORM_TYPE_SHOPEE, this);
    }
}

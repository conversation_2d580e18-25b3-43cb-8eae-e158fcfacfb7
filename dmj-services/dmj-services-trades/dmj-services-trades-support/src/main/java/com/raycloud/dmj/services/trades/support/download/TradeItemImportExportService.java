package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-02 10:56:55
 */
public class TradeItemImportExportService implements IDownloadCenterCallback {

    public static final String EVENT_NAME = "trade.item.import.download.excel";

    private Staff staff;

    private List<String> errorMsg;

    public static final String[] TITLE = {"行数", "失败原因"};

    public TradeItemImportExportService(Staff staff, List<String> errorMsg) {
        this.staff = staff;
        this.errorMsg = errorMsg;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        String[][] dataArr = null;
        String defaultString = "";
        if (!errorMsg.isEmpty()) {
            dataArr = new String[errorMsg.size()][];
            for (int i = 0; i < errorMsg.size(); i++) {
                String msg = errorMsg.get(i);
                String[] splitMsg = msg.split("[,，]");
                dataArr[i] = new String[TITLE.length];
                dataArr[i][0] = StringUtils.defaultIfEmpty(splitMsg[0], defaultString);
                dataArr[i][1] = StringUtils.defaultIfEmpty(splitMsg[1], defaultString);
            }
        }
        DownloadResult result = new DownloadResult();
        result.setData(dataArr);
        result.setFlag(false);
        return result;
    }
}

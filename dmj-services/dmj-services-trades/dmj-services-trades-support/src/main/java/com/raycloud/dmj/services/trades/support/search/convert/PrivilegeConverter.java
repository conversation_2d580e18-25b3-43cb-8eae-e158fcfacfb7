package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.domain.DmsConstant;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.user.IUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_PRIVILEGE)
public class PrivilegeConverter extends AbsConditionConverter {



    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    IUserService userService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    FxBusiness fxBusiness;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query) {
        return true;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        handleUserPrivilege(staff, context, condition, q);
        handleWareHousePrivilege(staff, context, condition, q);
    }

    private void handleWareHousePrivilege(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        List<Integer> types = condition.getWarehouseTypes() == null?new ArrayList<>():Arrays.asList(condition.getWarehouseTypes());
        if (isTrue( condition.getIgnoreWareHousePrivilege()) || staff.isDefaultStaff()) {
            if (types.isEmpty()) {
                andListCondition(q, "t.warehouse_id", condition.getWareHouseIds());
                return;
            }

            List<Long> warehouseRangeIds = new ArrayList<>();
            List<Warehouse> warehouses = warehouseService.queryAll(staff.getCompanyId(), 1);
            if (CollectionUtils.isEmpty(warehouses)) {
                q.setStopQuery(true);
                q.setStopReason("无仓库数据");
                return;
            }
            for (Warehouse warehouse : warehouses) {
                if (types.contains(warehouse.getType())) {
                    warehouseRangeIds.add(warehouse.getId());
                }
            }
            if (CollectionUtils.isEmpty(warehouseRangeIds)) {
                q.setStopQuery(true);
                q.setStopReason("无指定类型仓库数据");
                return;
            }
            Collection<Long> intersection = intersection(warehouseRangeIds, condition.getWareHouseIds());
            if (intersection.isEmpty()) {
                q.setStopQuery(true);
                q.setStopReason("指定类型仓库与指定id无交集");
                return;
            }
            andListCondition(q, "t.warehouse_id", intersection);
            return;
        }

        List<Long> list = getPrivilegeWarehouseIds(staff,types);
        if (CollectionUtils.isEmpty(list)) {
            q.setStopQuery(true);
            if (types.isEmpty()) {
                q.setStopReason("无仓库权限");
            }else {
                q.setStopReason("无指定类型仓库权限");
            }
            return;
        }
        Collection<Long> intersection = intersection(list, condition.getWareHouseIds());
        if (intersection.isEmpty()) {
            q.setStopQuery(true);
            q.setStopReason("仓库权限范围与指定id无交集");
            return;
        }
        andListCondition(q, "t.warehouse_id", intersection);

    }

    private List<Long> getPrivilegeWarehouseIds(Staff staff,List<Integer> types) {
        List<Long> list = new ArrayList<Long>();
        if (types.isEmpty()) {
            String allWarehouseIds = staff.getAllWarehouseGroup();
            return getLongs(list, allWarehouseIds);
        }
        if (types.contains(Warehouse.TYPE_OWN)) {
            list.addAll( getLongs(list, staff.getWarehouseGroup()));
        }
        if (types.contains(Warehouse.TYPE_EXTRA)) {
            list.addAll( getLongs(list, staff.getWarehouseExtraGroup()));
        }
        if (types.contains(Warehouse.TYPE_STORE)) {
            list.addAll( getLongs(list, staff.getWarehouseStoreGroup()));
        }
        return list;
    }

    private List<Long> getLongs(List<Long> list, String allWarehouseIds) {
        if (allWarehouseIds != null && allWarehouseIds.length() > 0) {
            String[] widArr = allWarehouseIds.split(",");
            for (String wid : widArr) {
                if ((wid = wid.trim()).matches("\\d+")) {
                    list.add(Long.valueOf(wid));
                }
            }
        }
        return list;
    }

    private void handleUserPrivilege(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        List<User> refUsers = new ArrayList<>();

        boolean checkActive = isTrue(condition.getCheckActive());

        List<Long> orgiUserIds = (condition.getUserIds() == null || condition.getUserIds().length == 0)?new ArrayList<>():new ArrayList<>(Arrays.asList(condition.getUserIds()));
        List<String> orgiSources = (condition.getSources() == null || condition.getSources().length == 0)?new ArrayList<>():new ArrayList<>(Arrays.asList(condition.getSources()));
        List<Long> orgiTaobaoIds = (condition.getTaobaoIds() == null || condition.getTaobaoIds().length == 0)?new ArrayList<>():new ArrayList<>(Arrays.asList(condition.getTaobaoIds()));

        Set<Long> specialUserIds = addSpecialUserIds(staff,condition,orgiUserIds,orgiSources);

        List<User> users = new ArrayList<>();
        if ((condition.getIgnoreUserPrivilege() != null && condition.getIgnoreUserPrivilege()) || staff.isDefaultStaff() ) {
            users = userService.queryByCompanyId(staff.getCompanyId());
        }else{
            if ((staff.getUsers() == null || staff.getUsers().isEmpty()) && specialUserIds.isEmpty()) {
                q.setStopQuery(true);
                q.setStopReason("无店铺权限");
                context.setRefUsers(refUsers);
                return;
            }
            users = staff.getUsers();
        }

        //取权限范围与传入值的交集
        List<Long> userIds = new ArrayList<>();
        Set<String> sources = new HashSet<>();


        DevLogBuilder builder = DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("店铺相关条件处理 ")
                .appendArray("权限范围",users,100,(user)->user.getId()).startWatch();
        for (User user : users) {
            if (orgiUserIds.size() > 0 && !orgiUserIds.contains(user.getId())) {
                builder.group("未指定",user.getId());
                continue;
            }
            if (orgiSources.size() > 0 && !orgiSources.contains(user.getSource())
                    && !orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_SYS) && !orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_QIMEN)) {
                builder.group("source不符",user.getId());
                continue;
            }
            if (orgiTaobaoIds.size() > 0 && !orgiTaobaoIds.contains(user.getTaobaoId())) {
                builder.group("平台id不符",user.getId());
                continue;
            }
            if (checkActive && user.getActive() != null && user.getActive() == 0) {//停用的店铺
                builder.group("停用店铺",user.getId());
                continue;
            }
            userIds.add(user.getId());
            refUsers.add(user);
        }
        builder.printDebug(logger);

        if (CollectionUtils.isEmpty(refUsers) && specialUserIds.isEmpty()) {
            q.setStopQuery(true);
            q.setStopReason("无店铺权限或权限范围与指定id无交集");
            context.setRefUsers(refUsers);
            return;
        }

        userIds.addAll(specialUserIds);
        andListCondition(q, "t.user_id", userIds.toArray(new Long[0]));

        if (CollectionUtils.isNotEmpty(orgiSources)) {
            if (orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_SYS)) {
                sources.add(CommonConstants.PLAT_FORM_TYPE_SYS);
            }
            if (orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_NEWFX) || orgiUserIds.contains(Constants.FxDefaultUserId)) {
                sources.add(CommonConstants.PLAT_FORM_TYPE_NEWFX);
            }
            if (orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_QIMEN)) {
                sources.add(CommonConstants.PLAT_FORM_TYPE_QIMEN);
            }
            andListCondition(q, "t.source", sources.toArray(new String[0]));
        }

        context.setRefUsers(refUsers);
    }

    private Set<Long>  addSpecialUserIds(Staff staff, TradeQueryRequest condition,List<Long> orgiUserIds, List<String> orgiSources) {
        Set<Long> specialUserIds = new HashSet<>();

        //指定要查newFx的订单 新增供销默认的UserId
        if (orgiUserIds.contains(Constants.FxDefaultUserId)) {
            specialUserIds.add(Constants.FxDefaultUserId);
        }else if (orgiSources.contains(CommonConstants.PLAT_FORM_TYPE_NEWFX)
                || contains( condition.getTradeTypes(), Integer.parseInt(TradeTypeEnum.SUPPLY_TRADE.getcode()))
                || contains( condition.getTradeTypes(), Integer.parseInt(TradeTypeEnum.FX_MULTI_LEVEL.getcode()))) {
            specialUserIds.add(Constants.FxDefaultUserId);
        }else if(orgiSources.isEmpty() && orgiUserIds.isEmpty()){
            specialUserIds.add(Constants.FxDefaultUserId);
        }
        // else{
        //    if (!orgiUserIds.isEmpty()) {
        //        Integer dmsRole = null;
        //        try {
        //            dmsRole = fxBusiness.queryDmsRole(staff.getCompanyId(), DmsConstant.DMS_ROLE_TYPE_GX);
        //        }catch (Exception e){
        //            new FxLogBuilder(staff.getCompanyId(), FxLogBuilder.ROLE_FX).append("获取当前企业供分销角色失败").printError(logger,e);
        //            //出错默认给查
        //            dmsRole = DmsConstant.DMS_ROLE_TYPE_GX;
        //        }
        //        if(dmsRole == null || Objects.equals(DmsConstant.DMS_ROLE_TYPE_GX,dmsRole)){
        //            specialUserIds.add(Constants.FxDefaultUserId);
        //        }
        //    }
        //}

        //出库单 其userId为0
        if (orgiUserIds.contains(0L)) {
            specialUserIds.add(0L);
        }else{
            if (!Objects.equals(condition.getIsOutstock(),0)
                    || contains( condition.getTradeTypes(), Integer.parseInt(TradeTypeEnum.ADJUST_TRADE.getcode()))
                    || contains( condition.getTradeTypes(), Integer.parseInt(TradeTypeEnum.CLAIM_TRADE.getcode()))
                    || contains( condition.getTradeTypes(), Integer.parseInt(TradeTypeEnum.REPORTING_BROKEN_TRADE.getcode()))) {
                specialUserIds.add(0L);
            }
        }

        return specialUserIds;
    }
}

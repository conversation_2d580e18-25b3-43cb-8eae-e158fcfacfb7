package com.raycloud.dmj.services.trades.support.platform;

import com.raycloud.dmj.dao.trade.UserChangeSourceLogDao;
import com.raycloud.dmj.domain.platform.UserChangeSourceLog;
import com.raycloud.dmj.domain.platform.bo.UserChangeSourceLogBo;
import com.raycloud.dmj.services.trades.platform.IUserChangeSourceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description
 * <AUTHOR>
 * @date 2024/3/25 14:29
 */
@Service
public class UserChangeSourceServiceImpl implements IUserChangeSourceService {

    @Resource
    UserChangeSourceLogDao userChangeSourceLogDao;

    @Override
    public void add(UserChangeSourceLogBo userChangeSourceInfoBo) {
        UserChangeSourceLog target = bo2Entity(userChangeSourceInfoBo);
        if (Objects.isNull(target.getChangeTime())) {
            target.setChangeTime(new Date());
        }
        userChangeSourceLogDao.insert(target);
    }

    @Override
    public List<UserChangeSourceLog> listAll() {
        return userChangeSourceLogDao.listAll();
    }

    @Override
    public List<Long> listAllUserIds() {
        return userChangeSourceLogDao.listAllUserIds();
    }

    @Override
    public UserChangeSourceLog queryByUserId(Long userId) {
        return userChangeSourceLogDao.queryByUserId(userId);
    }

    @Override
    public void update(UserChangeSourceLogBo userChangeSourceLogBo) {
        userChangeSourceLogDao.update(bo2Entity(userChangeSourceLogBo));
    }

    private UserChangeSourceLog bo2Entity(UserChangeSourceLogBo source) {
        UserChangeSourceLog target = new UserChangeSourceLog();
        BeanUtils.copyProperties(source, target);
        return target;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.business.logistics.BtasUploadResult;
import com.raycloud.dmj.business.logistics.PlatformUploader;
import com.raycloud.dmj.business.logistics.UploadBusiness;
import com.raycloud.dmj.business.trade.TradeStatBusiness;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.dao.trade.pgl.OrderExtDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.enums.YesNoEnum;
import com.raycloud.dmj.domain.platform.enums.fxg.FxgBTASExpressProductEnum;
import com.raycloud.dmj.domain.platform.trades.PackagesNoticeRequest;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidType;
import com.raycloud.dmj.domain.pt.model.CancelWaybillVO;
import com.raycloud.dmj.domain.pt.model.ChannelConfig;
import com.raycloud.dmj.domain.pt.model.waybill.cancel.CustomWaybillCancelRequest;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trade.type.TradeTypeNewEnum;
import com.raycloud.dmj.domain.trade.type.TypeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.bo.ExpressCompanyMappingBO;
import com.raycloud.dmj.domain.trades.params.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.AbroadAddress;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.merchantCode.base.ICBULogisticsEnum;
import com.raycloud.dmj.merchantCode.base.SumaitongLogisticsEnum;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.ConsignUploader;
import com.raycloud.dmj.services.pt.IUserWlbExpressCompanyService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.user.IAbroadAddressService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.warehouse.services.ITradeWarehouseService;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.enums.ProgressEnum.PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO;
import static com.raycloud.dmj.domain.enums.ProgressEnum.PROGRESS_BTAS_REUPLOAD_TRACKINGNO;
import static com.raycloud.dmj.domain.enums.TradeStatusEnum.SYS_STATUS_SELLER_SEND_GOODS;
import static com.raycloud.dmj.domain.utils.CommonConstants.*;

/**
 * @description: 线下组包实现
 * @author: zhaojianbo
 * @time: 2022/4/28 14:07
 */
@Service
public class OfflineCombineParcelServiceImpl implements IOfflineCombineParcelService {
    private static final Logger logger = LoggerFactory.getLogger(OfflineCombineParcelServiceImpl.class);
    //线下组包默认查询速卖通和阿里巴巴国际站
    List<String> defaultSource = Arrays.asList(CommonConstants.PLAT_FORM_TYPE_SMT, CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU,CommonConstants.PLAT_FORM_TYPE_TIKTOK);

    private String PAY_TIME = "pay_time";
    private String CONSIGN_TIME = "consign_time";

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    TradeUpdateService tradeUpdateService;

    @Resource
    TradeStatBusiness tradeStatBusiness;

    @Resource
    ITradeService tradeService;

    @Resource
    OrderExtDao orderExtDao;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    IShopService shopService;

    @Resource
    IAbroadAddressService abroadAddressService;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource(name = "userWlbExpressCompanyAdapterService")
    IUserWlbExpressCompanyService userWlbExpressCompanyAdapterService;

    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    IExpressCompanyMappingService expressCompanyMappingService;

    @Resource
    private PlatformManagement platformManagement;

    @Resource
    IProgressService progressService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    ICache cache;

    @Resource
    UploadBusiness uploadBusiness;

    @Resource
    ConsignRecordDao consignRecordDao;

    @Resource
    ITradeWarehouseService tradeWarehouseService;

    @Resource
    IConsignUploadService consignUploadService;

    private static final int BATCH_SIZE = 100;

    @Override
    public TradeParcels listTrade(Staff staff, OfflineParcelTradeRequest request, Page page) {
        TradeParcels tradeParcels = new TradeParcels();
        request.setContainExcept(Boolean.TRUE);
        if (CollectionUtils.isEmpty(request.getSource())) {
            request.setSource(defaultSource);
            request.setBtas(1);
        }else if(request.getSource().contains(PLAT_FORM_TYPE_BTAS)){
            request.setBtas(1);
        }
        if (Objects.equals(PAY_TIME, request.getTimeType())) {
            request.setPayStartTime(request.getStartTime() == null ? null : new Date(request.getStartTime()));
            request.setPayEndTime(request.getEndTime() == null ? null : new Date(request.getEndTime()));
        }
        if (Objects.equals(CONSIGN_TIME, request.getTimeType())) {
            request.setConsignStartTime(request.getStartTime() == null ? null : new Date(request.getStartTime()));
            request.setConsignEndTime(request.getEndTime() == null ? null : new Date(request.getEndTime()));
        }
        request.setCombineParcelId(0L);
        List<TradeParcel> list = tradeParcelDAO.queryOfflinePage(staff, request, page);
        if (CollectionUtils.isEmpty(list)) {
            tradeParcels.setTotal(0L);
            tradeParcels.setPage(page);
            return tradeParcels;
        }
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeParcel parcel : list) {
                if (parcel.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(parcel.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                parcel.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
                parcel.setTemplateName(StringUtils.isEmpty(parcel.getTemplateName()) ? "" : parcel.getTemplateName());
            }
        }
        list.forEach(parcel ->{
            //前端不能展示为0，处理为不返回
            if (Long.valueOf(0).equals(parcel.getWaveId())){
                parcel.setWaveId(null);
            }
        });
        tradeParcels.setList(list);
        tradeParcels.setTotal(tradeParcelDAO.offlineCountAll(staff, request));
        tradeParcels.setPage(page);
        return tradeParcels;
    }

    @Override
    public TradeCombineParcels listCombineParcel(Staff staff, OfflineCombineParcelRequest request, Page page) {
        TradeCombineParcels tradeCombineParcels = new TradeCombineParcels();
        List<TradeCombineParcel> list = tradeCombineParcelDAO.queryOfflineCombinePage(staff, request, page);
        if (CollectionUtils.isEmpty(list)) {
            tradeCombineParcels.setTotal(0L);
            tradeCombineParcels.setPage(page);
            return tradeCombineParcels;
        }
        parseExtraFields(staff, list);
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeCombineParcel combineParcel : list) {
                if (combineParcel.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(combineParcel.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                combineParcel.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
                combineParcel.setTemplateName(StringUtils.isEmpty(combineParcel.getTemplateName()) ? "" : combineParcel.getTemplateName());
            }
        }

        tradeCombineParcels.setList(list);
        tradeCombineParcels.setTotal(tradeCombineParcelDAO.countOfflineCombineAll(staff, request));
        tradeCombineParcels.setPage(page);
        updateWmsParcelStatus(staff, list);
        return tradeCombineParcels;
    }

    private void updateWmsParcelStatus(Staff staff, List<TradeCombineParcel> list){
        List<TradeCombineParcel> needUpdate = new ArrayList<>();
        for (TradeCombineParcel tradeCombineParcel : list){
            if (!tradeCombineParcel.isWmsBtasParcel() || Integer.valueOf(TradeCombineParcel.STATUS_OUTBOUNDED).equals(tradeCombineParcel.getStatus())){
                //已出库的组包，非WMS组包，不实时更新状态
                continue;
            }
            //找到所有的订单。
            List<TradeParcel> tradeParcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{tradeCombineParcel.getId()}, null);
            if (CollectionUtils.isEmpty(tradeParcelList)) {
                continue;
            }
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, tradeParcelList.stream().map(TradeParcel::getSid).toArray(Long[]::new));
            tradeList = tradeList.stream().filter(t ->!isUploadSuccess(t)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tradeList)){
                tradeCombineParcel.setStatus(TradeCombineParcel.STATUS_OUTBOUNDED);
                needUpdate.add(tradeCombineParcel);
            }
        }
        needUpdate = needUpdate.stream().map(t ->{
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(t.getId());
            combineParcelUpdate.setStatus(TradeCombineParcel.STATUS_OUTBOUNDED);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needUpdate)){
            tradeCombineParcelDAO.batchUpdate(staff, needUpdate);
        }
    }

    private void parseExtraFields(Staff staff, List<TradeCombineParcel> list){
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        try {
            list.forEach(TradeCombineParcel::parseBtasFromExtraFields);
        }catch (Exception e){
            logger.error(LogHelper.buildLogHead(staff).append(String.format("组包解析ExtraFields信息出错, id=%s", list.stream().map(TradeCombineParcel::getId).collect(Collectors.toList()))).toString());
        }
    }

    @Override
    public CombineParcelConsignResult combineParcelUpload(Staff staff,CombineParcelConsignRequest request) {

        Long[] combineParcelId = (Long[]) ConvertUtils.convert(request.getCombineParcelIds().split(","), Long.class);
        List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelId);
        CombineParcelConsignResult result = new CombineParcelConsignResult();

        //1.检查和过滤组包
        List<TradeCombineParcel> validateData = validateAndFilter(staff,tradeCombineParcels, result,request);
        if (CollectionUtils.isEmpty(validateData) || validateData.size()!=tradeCombineParcels.size()){
            return result;
        }
        ProgressData progressData = null;
        if (request.isEvent()){
            progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO);
        }

        //2.统计订单数量，如果超过20个就发事件。
        int pacelCount = 0;
        for (TradeCombineParcel validateDatum : validateData) {
            pacelCount+=validateDatum.getParcelNum();
        }
        if (pacelCount>=20){
            request.setEvent(true);
            result.setProgressEnable(true);
            //创建进度条
            Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO), "已经在执行BTAS运单号上传任务，请稍后再试！");
            progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO);
            progressData.setCountAll(pacelCount);
            progressData.setCountCurrent(0);
            progressService.updateProgress(staff,ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO,progressData);
            eventCenter.fireEvent(this, new EventInfo("trade.btas.tracking.upload").setArgs(new Object[]{staff,request}),null);
            return result;
        }
        parseExtraFields(staff, validateData);
        for (TradeCombineParcel validateDatum : validateData) {
            TradeCombineParcel updateTradeCombineParcel = new TradeCombineParcel();
            updateTradeCombineParcel.setCompanyId(validateDatum.getCompanyId());
            updateTradeCombineParcel.setId(validateDatum.getId());

            //物流信息预处理。
            String trackingNo = validateDatum.getTrackingNo();
            UserWlbExpressTemplate userWlbExpressTemplate = null;
            ExpressCompany expressCompanyByCode = new ExpressCompany();
            ExpressCompanyMapping expressCompanyMapping =new ExpressCompanyMapping();
            if (Objects.nonNull(validateDatum.getTemplateId())){
                userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, validateDatum.getTemplateId(), false);
                if (Objects.nonNull(userWlbExpressTemplate)) {
                    expressCompanyByCode = expressCompanyService.getExpressCompanyByCode(userWlbExpressTemplate.getCpCode());
                    expressCompanyMapping = getFxgCompany().get(expressCompanyByCode.getId());
                }
            }

            Integer successCount = 0;
            Integer parcelNum = validateDatum.getParcelNum();
            try {
                //上传平台记录发货方式
                if (request.getType() - 1 == 0) {
                    if (request.getBtasConsignType()==null || request.getBtasConsignType() - 1 == 0) {
                        updateTradeCombineParcel.setGatherType(TradeCombineParcel.EnumGatherType.YOUJI.getType());
                    } else if (request.getBtasConsignType() - 2 == 0) {
                        updateTradeCombineParcel.setGatherType(TradeCombineParcel.EnumGatherType.DUMMY.getType());
                    } else {
                        throw new IllegalArgumentException("未知的发货类型 consignType：" + request.getBtasConsignType());
                    }
                }else {
                    updateTradeCombineParcel.setGatherType(validateDatum.getGatherType());
                }

                int consignType = -1;
                if (updateTradeCombineParcel.getGatherType() - TradeCombineParcel.EnumGatherType.YOUJI.getType() == 0) {
                    consignType = 1;
                } else if (updateTradeCombineParcel.getGatherType() - TradeCombineParcel.EnumGatherType.DUMMY.getType() == 0) {
                    consignType = 2;
                } else {
                    throw new IllegalArgumentException("未知的发货类型 consignType：" + validateDatum.getGatherType());
                }

                //2.查出订单
                Page page = new Page();
                page.setPageSize(BATCH_SIZE);
                Integer count = tradeParcelDAO.queryTradeSidCountByCombineParcelId(staff, validateDatum.getId());
                int loop = count / BATCH_SIZE + ((count % BATCH_SIZE == 0) ? 0 : 1);
                for (int i = 0; i < loop; i++) {
                    List<Long> sids = tradeParcelDAO.queryTradeSidByCombineParcelId(staff, validateDatum.getId(), page);
                    if (CollectionUtils.isEmpty(sids)) {
                        break;
                    }
                    List<Trade> trades = tradeSearchService.queryBySids(staff, true, sids.toArray(new Long[0]));
                    List<Trade> updateTradeList = new ArrayList<>();
                    List<TradeTrace>  tradeTraces = new ArrayList<>();
                    List<OrderExt> updateOrderExtList = new ArrayList<>();
                    try {
                        for (Trade trade : trades) {
                            if (request.isEvent()) {
                                progressData.setCountCurrent(progressData.getCountCurrent());
                            }
                            boolean success = false;
                            try {
                                BtasCombineParcelUploadRequest req = new BtasCombineParcelUploadRequest();
                                req.staff = staff;
                                req.request = request;
                                req.result =result;
                                req.validateDatum = validateDatum;
                                req.trackingNo = trackingNo;
                                req.expressCompanyByCode = expressCompanyByCode;
                                req.expressCompanyMapping = expressCompanyMapping;
                                req.consignType = consignType;
                                req.btasExpressProduct = validateDatum.getBtasExpressProduct();
                                req.updateTradeList = updateTradeList;
                                req.updateOrderExtList = updateOrderExtList;
                                req.tradeTraces = tradeTraces;
                                req.trade = trade;
                                tradeService.consign(staff,new Long[]{trade.getSid()}, "DUMMY",null,null,3,null,null,false);
                                success = uploadTradeTrackingNo(req);
                                if (req.resultSkip){
                                    successCount++;
                                    if (logger.isDebugEnabled()){
                                        logger.debug(LogHelper.buildLogHead(staff,"订单组包上传跳过").append("sid:").append(trade.getSid()).append("req:").append(JSON.toJSONString(request)).toString());
                                    }
                                    continue;
                                }
                                tradeTraceService.batchAddTradeTrace(staff,tradeTraces);
                            }catch (Exception e){
                                logger.error(LogHelper.buildErrorLog(staff,e,"订单组包上传失败").append("sid:").append(trade.getSid()).append("req:").append(JSON.toJSONString(request)).toString(),e);
                            }
                            if (success){
                                successCount++;
                            }
                        }
                    } catch (Exception e) {
                        //订单维度忽略。
                        logger.error(LogHelper.buildErrorLog(staff, e, "组包上传失败").toString(), e);
                    } finally {
                        tradeUpdateService.updateTrades(staff,updateTradeList);
                        if (CollectionUtils.isNotEmpty(updateOrderExtList)){
                            orderExtDao.batchUpdate(staff, updateOrderExtList);
                        }
                        if (request.isEvent()) {
                            progressService.updateProgress(staff,ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO,progressData);
                        }
                    }
                    page.setPageNo(page.getPageNo() + 1);
                }

            }catch (Exception e){
                if (request.isEvent()){
                    progressData.setProgress(2);
                }
                logger.error(LogHelper.buildErrorLog(staff, e, "组包上传失败").toString(), e);
                throw e;
            }finally {
                if (parcelNum-successCount==0){
                    updateTradeCombineParcel.setUploadStatus(3);
                    updateTradeCombineParcel.putExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO,trackingNo);
                }else if (successCount!=0){
                    updateTradeCombineParcel.setUploadStatus(2);
                    updateTradeCombineParcel.putExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO,trackingNo);
                }else {
                    updateTradeCombineParcel.setUploadStatus(4);
                }
                tradeCombineParcelDAO.batchUpdate(staff, Lists.newArrayList(updateTradeCombineParcel));
                if (request.isEvent()) {
                    progressService.updateProgress(staff, ProgressEnum.PROGRESS_BTAS_UPLOAD_TRACKINGNO, progressData);
                }
            }

        }
        //3.上传物流
        return result;
    }


    private boolean uploadTradeTrackingNo(BtasCombineParcelUploadRequest req) {

        PackagesNoticeRequest packagesNoticeRequest = new PackagesNoticeRequest();
        packagesNoticeRequest.setExpressCompany(req.expressCompanyByCode);
        packagesNoticeRequest.setExpressCompanyMapping(req.expressCompanyMapping);
        packagesNoticeRequest.setTrackingNo(req.trackingNo);
        packagesNoticeRequest.setOldTrackingNo(req.validateDatum.pullExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO));
        packagesNoticeRequest.setReplace(req.request.getType() - 3 == 0 ? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());
        packagesNoticeRequest.setConsignType(req.consignType);
        packagesNoticeRequest.setBtasExpressProduct(req.btasExpressProduct);
        packagesNoticeRequest.setType(req.request.getType());
        packagesNoticeRequest.setTrade(req.trade);
        ConsignUploader access = platformManagement.getAccess("fxg", ConsignUploader.class);
        User userByUserId = req.staff.getUserByUserId(req.trade.getUserId());

        //上传平台。
        PlatformResponse platformResponse = access.packagesNotice(userByUserId, packagesNoticeRequest);
        if (platformResponse.isSuccess() && CommonConstants.BTAS_UPLOAD_SKIP_CODE.equals(platformResponse.getCode())){
            req.resultSkip = true;
            return true;
        }
        //搜集orderExt统一更新orderCode上传状态。
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(req.trade);
        for (Order order : orders4Trade){
            OrderExt newOrderExt = order.getOrderExt();
            if (null != newOrderExt){
                OrderExt updateOrderExt = new OrderExt();
                updateOrderExt.setId(order.getId());
                updateOrderExt.setCustomization(newOrderExt.getCustomization());
                req.updateOrderExtList.add(updateOrderExt);
            }
        }
        String consigTypeCh = req.consignType == 1 ? "邮寄" : "无需物流";
        if (!platformResponse.isSuccess()) {
            //失败的订单写到返回值
            CombineParcelConsignResult.CombineParcelConsignResultVo vo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
            vo.setId(req.validateDatum.getId());
            vo.setSid(req.trade.getSid());
            vo.setErrorMsg("btas组包上传失败【" + platformResponse.getMsg() + "】");
            if (req.result.getErrorList()==null){
                req.result.setErrorList(new ArrayList<>());
            }
            req.result.getErrorList().add(vo);
            logger.error("btas组包上传失败【" + platformResponse.getMsg() + "】");
            req.tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(req.staff,req.trade,"组包上传",req.staff.getName(),new Date(),"大包单号["+req.validateDatum.getId()+"], 实物质检「"+ consigTypeCh +"」"+(Integer.valueOf(3).equals(req.request.getType())?"重新":"")+"上传发货失败，原因："+platformResponse.getMsg()+"！"));
            return false;
        } else {
            //构建UpdateTrade
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(req.trade.getSid());
            updateTrade.setCompanyId(req.trade.getCompanyId());
            updateTrade.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
            updateTrade.setIsUpload(1);
            //成功的订单记录当前的物流单号到tradeExt中。
            JSONObject jsonObject = JSONObject.parseObject(req.trade.getTradeExt().getExtraFields());
            if (jsonObject == null) {
                jsonObject = new JSONObject();
            }
            jsonObject.remove(TradeExtendConfigsEnum.BTAS_TRACKING_NO.getKey());
            jsonObject.put(TradeExtendConfigsEnum.BTAS_TRACKING_NO.getKey(), req.trackingNo);

            //构造updateTradeExt，将上传成功的tracking_no存入。
            TradeExt updateTradeExt = new TradeExt();
            updateTradeExt.setCompanyId(req.trade.getTradeExt().getCompanyId());
            updateTradeExt.setSid(req.trade.getTradeExt().getSid());
            updateTradeExt.setExtraFields(jsonObject.toJSONString());
            updateTrade.setTradeExt(updateTradeExt);
            req.updateTradeList.add(updateTrade);
            req.tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(req.staff,req.trade,"组包上传",req.staff.getName(),new Date(),"大包单号["+req.validateDatum.getId()+"], 实物质检「"+consigTypeCh+"」"+(Integer.valueOf(3).equals(req.request.getType())?"重新":"")+"上传发货成功！"));
            return true;
        }
    }

    public Map<Long, ExpressCompanyMapping> getFxgCompany(){
        Map<Long, ExpressCompanyMapping> expressMappingMap=null;
        String fxgExpressMappingKey = "fxg_database_express_company_cache";
        try {
            expressMappingMap = cache.get(fxgExpressMappingKey);
        } catch (Exception e) {
            //这里缓存出错走数据库查询
            logger.warn("fxg_database_express_company_cache 获取缓存失败");
        }
        if (null == expressMappingMap || expressMappingMap.isEmpty()) {
            //查询数据库
            ExpressCompanyMappingBO expressCompanyMappingBO = new ExpressCompanyMappingBO();
            expressCompanyMappingBO.setSource("fxg");
            expressCompanyMappingBO.setMatchType("1");
            expressMappingMap = expressCompanyMappingService.getExpressMappingMap(expressCompanyMappingBO);
            try {
                cache.set(fxgExpressMappingKey, expressMappingMap, PlatformUploader.CACHE_OVER_TIME);
            } catch (Exception e) {
                logger.warn("fxg_database_express_company_cache 设置缓存失败");
            }
        }
        return expressMappingMap;
    }

    /**
     * 校验BTAS的包是否能够上传。并返回能上传的包。
     * */
    private List<TradeCombineParcel> validateAndFilter(Staff staff,List<TradeCombineParcel> list,CombineParcelConsignResult result,CombineParcelConsignRequest request){
        LinkedList<TradeCombineParcel> fliterList = new LinkedList<>();
        List<Long> combineParcelIds  = new LinkedList<>();
        int countTrades = 0;
        for (TradeCombineParcel tradeCombineParcel : list) {
            if (Integer.valueOf(3).equals(request.getType()) && tradeCombineParcel.getTrackingNo().equals(tradeCombineParcel.pullExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO))){
                result.setErrorMsg("新运单号与老运单号一致，请更新运单号后重新上传平台！");
                continue;
            }
            if(!PLAT_FORM_TYPE_FXG.equals(tradeCombineParcel.getSource())){
                result.setErrorMsg("【"+tradeCombineParcel.getId()+"】不是BTAS的订单，不支持上传平台。");
                continue;
            }
            if (StringUtils.isBlank(tradeCombineParcel.getTrackingNo()) && Integer.valueOf(1).equals(request.getBtasConsignType())){
                result.setErrorMsg("【"+tradeCombineParcel.getId()+"】未获取运单号，请先获取运单号!");
                continue;
            }
            combineParcelIds.add(tradeCombineParcel.getId());
            countTrades += tradeCombineParcel.getParcelNum()==null?0:tradeCombineParcel.getParcelNum();
        }
        if (combineParcelIds.isEmpty()){
            return fliterList;
        }
        OfflineParcelTradeRequest offlineParcelTradeRequest = new OfflineParcelTradeRequest();
        offlineParcelTradeRequest.setCombineParcelIds(combineParcelIds);
        offlineParcelTradeRequest.setUnifiedStatus(SYS_STATUS_SELLER_SEND_GOODS.getcode());
        Page page = new Page();
        page.setPageSize(100);
        int loopCount = (countTrades/100) + (countTrades%100==0?0:1);
        for (int i = 1; i <= loopCount; i++) {
            page.setPageNo(1);
            List<TradeParcel> tradeParcels = tradeParcelDAO.platSellerSendGoodsTrades(staff, offlineParcelTradeRequest, page);
            //将找到的不能上传的订单拼接到返回中,并移除组包列表
            if (!CollectionUtils.isEmpty(tradeParcels)) {
                for (TradeParcel tradeParcel : tradeParcels) {
                    CombineParcelConsignResult.CombineParcelConsignResultVo combineParcelConsignResultVo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
                    combineParcelConsignResultVo.setId(tradeParcel.getCombineParcelId());
                    combineParcelConsignResultVo.setSid(tradeParcel.getSid());
                    if (Objects.nonNull(tradeParcel.getIsRefund()) && Long.valueOf(1).equals(tradeParcel.getIsRefund())) {
                        combineParcelConsignResultVo.setErrorMsg("订单退款中，请检查后重新进行上传发货!");
                    }else {
                        combineParcelConsignResultVo.setErrorMsg("状态为待审核/已发货/交易关闭/交易作废/交易成功，请检查后重新进行上传发货！");
                    }
                    if (result.getErrorList()==null){
                        result.setErrorList(new ArrayList<>());
                    }
                    result.getErrorList().add(combineParcelConsignResultVo);
                    combineParcelIds.remove(tradeParcel.getCombineParcelId());
                }
            }
            //将验证通过的返回.
            if (!CollectionUtils.isEmpty(combineParcelIds)) {
                for (TradeCombineParcel tradeCombineParcel : list) {
                    if (combineParcelIds.contains(tradeCombineParcel.getId())) {
                        fliterList.add(tradeCombineParcel);
                    }
                }
            }
        }
        return fliterList;
    }

    @Override
    public TradeParcels listParcel(Staff staff, OfflineParcelTradeRequest request, Page page) {
        TradeParcels tradeParcels = new TradeParcels();
        List<TradeParcel> list = tradeParcelDAO.queryOfflinePage(staff, request, page);
        if (CollectionUtils.isEmpty(list)){
            tradeParcels.setList(list);
            tradeParcels.setTotal(0L);
            tradeParcels.setPage(page);
            return tradeParcels;
        }
        List<Long> sids = list.stream().map(TradeParcel::getSid).collect(Collectors.toList());
        List<TradeExt> tradeExts = tradeExtDao.queryBySidIn(staff, sids);
        Map<Long, TradeExt> sidToTradeExt = tradeExts.stream().collect(Collectors.toMap(TradeExt::getSid, x -> x));
        for (TradeParcel tradeParcel : list) {
            Long sid = tradeParcel.getSid();
            TradeExt tradeExt = sidToTradeExt.get(sid);
            tradeParcel.setUploadSuccess(Objects.nonNull(tradeExt) && StringUtils.isNotBlank(tradeExt.getExtraFields()) && tradeExt.getExtraFields().contains(TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey()));
        }
        if (CollectionUtils.isEmpty(list)) {
            tradeParcels.setTotal(0L);
            tradeParcels.setPage(page);
            return tradeParcels;
        }
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeParcel parcel : list) {
                if (parcel.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(parcel.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                parcel.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
            }
        }
        tradeParcels.setList(list);
        tradeParcels.setTotal(tradeParcelDAO.offlineCountAll(staff, request));
        tradeParcels.setPage(page);
        return tradeParcels;
    }

    @Override
    @Transactional
    public Map addParcelToCombine(Staff staff, OfflineParcelTradeRequest request) {
        List<Trade> tradesList = tradeSearchService.queryBySids(staff, false, request.getSids().toArray(new Long[request.getSids().size()]));
        Map<String, Long> combineParcel = new HashMap();
        if (StringUtils.isNotBlank(request.getParcelTrackingNo())) {
            //物流单号或者大包ID不为空,移入原有大包中
            TradeCombineParcel tradeCombineParcel = tradeCombineParcelDAO.queryByIdOrTrackingNo(staff, request);
            if (tradeCombineParcel == null) {
                throw new RuntimeException("输入的大包号/快递单号不存在");
            }
            if (tradeCombineParcel.getStatus() != TradeCombineParcel.STATUS_TO_OUTBOUND) {
                throw new RuntimeException("大包状态不为待出库，不能绑定小包");
            }
            List<String> fxgPlatform = Lists.newArrayList(tradeCombineParcel.getSource());
            for (Trade trade:tradesList) {
                if (!Objects.equals(trade.getWarehouseId(), tradeCombineParcel.getConsignWarehouseId())){
                    throw new RuntimeException("请选择相同仓库的订单");
                }
                if (TradeTypeUtils.isFxgBtasTrade(trade)){
                    //BTAS订单，手工单也放过
                    if (!TradeUtils.platformContain(staff, trade, fxgPlatform)){
                        throw new RuntimeException("请选择相同平台的订单");
                    }
                    //BTAS订单，平台拆单的手工单与平台订单也能组合大包
                    if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && !TypeUtils.hasTradeType(trade, TradeTypeNewEnum.split_plat)){
                        throw new RuntimeException("存在不属于平台拆单类型的手工订单");
                    }
                }else {
                    if (!Objects.equals(trade.getSource(), tradeCombineParcel.getSource())){
                        throw new RuntimeException("请选择相同平台的订单");
                    }
                }
            }
            addToOldCombineParcel(staff, tradesList, tradeCombineParcel);
            combineParcel.put("combineParcelId", tradeCombineParcel.getId());
        } else {
            //保存收件人地址
            AbroadAddress abroadAddress=request.getReceiveAddress();
            if(abroadAddress==null){
                throw new RuntimeException("收件人地址不能为空");
            }
            abroadAddress.setCompanyId(staff.getCompanyId());
            abroadAddress.setType(2);
            Long addressId = abroadAddressService.saveAbroadAddress(staff, abroadAddress);
            TradeCombineParcel combine=new TradeCombineParcel();
            combine.setTransferWarehouseId(String.valueOf(addressId));
            combine.setTransferWarehouseAddress(abroadAddress.getAddr());
            combine.setTemplateId(request.getTemplateId());
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, request.getTemplateId(), false);
            if (userWlbExpressTemplate != null) {
                combine.setTemplateName(userWlbExpressTemplate.getName());
            }
            combine.setExtraFields(initExtraFields(request));
            //创建一个新的的大包
            Long combineParcelId = createNewCombineParcel(staff, tradesList, combine);
            combineParcel.put("combineParcelId", combineParcelId);
        }
        return combineParcel;
    }

    private String initExtraFields(OfflineParcelTradeRequest request){
        Map<String, Object> extraFields = new HashMap<>();
        if (null != request.getBtasExpressProduct()){
            extraFields.put(TradeCombineParcel.ExtraField.BTAS_EXPRESS_PRODUCT.getName(), request.getBtasExpressProduct());
        }
        return JSON.toJSONString(extraFields);
    }

    private Long createNewCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setParcelNum(trades.size());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setCombineParcelType(1);
        tradeCombineParcelDAO.insert(staff, combineParcel);
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);
        tradeParcelDAO.batchInsert(staff, parcels);
        parseExtraFields(staff, Collections.singletonList(combineParcel));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            StringBuilder content = new StringBuilder("绑定大包号：" ).append(combineParcelId);
            if (null != combineParcel.getBtasExpressProduct()){
                content.append("; 质检发顾客快递: ").append(FxgBTASExpressProductEnum.getByKey(combineParcel.getBtasExpressProduct()).getName());
            }
            tradeTrack(staff, x.getSid(), action, content.toString());

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);
        return combineParcelId;
    }

    private List<TradeParcel> buildParcelsCreate(List<Trade> trades, Long combineParcelId) {
        return trades.stream().map(x -> {
            TradeParcel parcel = new TradeParcel();
            parcel.setCombineParcelId(combineParcelId);
            parcel.setTaobaoId(x.getTaobaoId());
            parcel.setSid(x.getSid());
            parcel.setTid(x.getTid());
            parcel.setOutSid(x.getOutSid());
            parcel.setUploadStatus(TradeParcel.UPLOAD_STATUS_UPLOADED);
            return parcel;
        }).collect(Collectors.toList());
    }

    private void addToOldCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        List<TradeParcel> oldTradeParcels = tradeParcelDAO.queryBySidsAndCombineParcelId(staff, trades.stream().map(Trade::getSid).toArray(Long[]::new), combineParcel.getId());
        if (CollectionUtils.isNotEmpty(oldTradeParcels)) {
            StringBuilder builder = new StringBuilder();
            for (TradeParcel oldTradeParcel : oldTradeParcels) {
                builder.append(oldTradeParcel.getSid()).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            throw new RuntimeException("[" + builder.toString() + "]对应的小包已存在");
        }
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcel.getId());
        tradeParcelDAO.batchInsert(staff, parcels);
        parseExtraFields(staff, Collections.singletonList(combineParcel));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcel.getId());
            //添加系统日志
            String action = "移入:";
            StringBuilder content = new StringBuilder("绑定大包号：" ).append(combineParcel.getId());
            if (null != combineParcel.getBtasExpressProduct()){
                content.append("; 质检发顾客快递: ").append(FxgBTASExpressProductEnum.getByKey(combineParcel.getBtasExpressProduct()).getName());
            }
            tradeTrack(staff, x.getSid(), action, content.toString());

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        TradeCombineParcel increase = new TradeCombineParcel();
        increase.setId(combineParcel.getId());
        increase.setParcelNum(combineParcel.getParcelNum() + parcels.size());
        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(increase));

    }


    /**
     * 生成系统日志
     *
     * @param staff
     * @param Sid
     * @param action
     * @param content
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }


    @Override
    @Transactional
    public void removeParcel(Staff staff, Long combineParcelId, List<Long> sids) {
        if (combineParcelId == null || sids == null || sids.size() == 0) {
            throw new RuntimeException("请选择订单再执行移出");
        }
        List<TradeCombineParcel> tradeCombineParcelList = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        for (TradeCombineParcel combineParcel:tradeCombineParcelList) {
            if(TradeCombineParcel.STATUS_OUTBOUNDED==combineParcel.getStatus()){
                throw new RuntimeException("大包状态已出库，无法移出");
            }
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryBySids(staff, sids.toArray(new Long[sids.size()]));

        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);
        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, null);
        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.stream().forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "解除绑定大包号：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });
    }

    @Override
    @Transactional
    public List<CancelWaybillVO> cancelCombineParcel(Staff staff, List<Long> combineParcelIds) {

        List<CancelWaybillVO> cancelWaybillVOS=new ArrayList<>();
        //根据大包ID查询大包是否为空
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelIds.toArray(new Long[combineParcelIds.size()]));
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询待取消大包为空");
        }
        for (TradeCombineParcel parcel : combineParcels) {
            List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
            //判断如果当前大包中未存在小包，或者不是待出库的组包，则直接置为取消状态
            if (parcel.getStatus() == TradeCombineParcel.STATUS_OUTBOUNDED) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(parcelList)) {
                //将小包移出大包
                removeParcel(staff, parcel.getId(), parcelList.stream().map(TradeParcel::getSid).collect(Collectors.toList()));
            }

            //更改大包状态为已取消
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
            // 如果大包已经获取单号，则将单号回收
            try {
                if (StringUtils.isNotBlank(parcel.getTrackingNo())) {
                    WaybillCancel(staff, parcel);
                }
            } catch (Exception e) {
                CancelWaybillVO cancelWaybillVO = new CancelWaybillVO();
                cancelWaybillVO.setCombineParcelId(parcel.getId());
                cancelWaybillVO.setTrackingNo(parcel.getTrackingNo());
                cancelWaybillVO.setFailureReason(e.getMessage());
                cancelWaybillVOS.add(cancelWaybillVO);
            }
        }
        return cancelWaybillVOS;
    }

    @Override
    @Transactional
    public CombineParcelConsignResult outboundCombineParcel(Staff staff, List<Long> combineParcelIds,String ip,Integer consignType) {
        CombineParcelConsignResult result = new CombineParcelConsignResult();
        result.setErrorList(Lists.newArrayList());

        if (combineParcelIds == null || combineParcelIds.size() == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelIds.toArray(new Long[combineParcelIds.size()]));
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("没有可出库组包");
        }
        List<TradeCombineParcel> filterCombineParcels = combineParcels.stream()
                .filter(x -> (TradeCombineParcel.STATUS_TO_OUTBOUND == x.getStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterCombineParcels)) {
            throw new RuntimeException("没有可用的组包");
        }

        //更新发货类型。
        updateCombinParcelGatherType(staff, consignType, result, filterCombineParcels);
        if (CollectionUtils.isEmpty(filterCombineParcels)){
            return result;
        }

        boolean async = needSync(filterCombineParcels);
        if (async) {
            //创建进度条
            Assert.isTrue(!progressService.hasProgress(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO), "已经在执行组包出库上传任务，请稍后再试！");
            ProgressData progressData = progressService.getOrCreate(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO);
            int count = 0;
            for (TradeCombineParcel filterCombineParcel : filterCombineParcels) {
                count+=filterCombineParcel.getParcelNum();
            }
            progressData.setCountAll(count);
            progressData.setCountCurrent(0);
            progressService.updateProgress(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
            eventCenter.fireEvent(this,new EventInfo("trade.btas.consignAndUpload").setArgs(new Object[]{staff,ip,result,filterCombineParcels}),null);
            result.setProgressEnable(true);
        }else {
            consignAndOutBound(staff, ip, result, filterCombineParcels,false);
        }
        return result;
    }

    public CombineParcelConsignResult consignAndOutBound(Staff staff, String ip, CombineParcelConsignResult result, List<TradeCombineParcel> filterCombineParcels,boolean async) {
        ProgressData progressData=null;
        if (async){
            progressData = progressService.getOrCreate(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO);
        }
        List<Long> removeTradeList = new ArrayList<>();
        try {
            for (TradeCombineParcel parcel : filterCombineParcels) {
                //找到所有的订单。
                List<TradeParcel> tradeParcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
                if (CollectionUtils.isEmpty(tradeParcelList)) {
                    continue;
                }
                List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, tradeParcelList.stream().map(TradeParcel::getSid).toArray(Long[]::new));
                //与组包不在同一个仓库需要移出组包的订单
                List<Long> filterTradeSidList = Lists.newArrayList();
                //与组包在同一个仓库，需要发货上传的订单。
                List<Long> needOutBoundSid = Lists.newArrayList();
                List<Trade> needOutBoundTrade = Lists.newArrayList();
                Map<Long, Trade> sidToTrade = new HashMap<>();
                for (Trade trade : tradeList) {
                    if (!trade.getWarehouseId().equals(parcel.getConsignWarehouseId())) {
                        filterTradeSidList.add(trade.getSid());
                        continue;
                    }
                    //如果订单已上传，跳过后续上传处理
                    if (isUploadSuccess(trade)){
                        continue;
                    }
                    needOutBoundSid.add(trade.getSid());
                    needOutBoundTrade.add(trade);
                    sidToTrade.put(trade.getSid(), trade);
                }
                //filterTradeList 移出组包
                if (CollectionUtils.isNotEmpty(filterTradeSidList)) {
                    removeTradeList.addAll(filterTradeSidList);
                    removeParcel(staff, parcel.getId(), filterTradeSidList);
                    if (async) {
                        progressData.setCountCurrent(progressData.getCountCurrent() + filterTradeSidList.size());
                        progressService.updateProgress(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO, progressData);
                    }

                }
                if (CollectionUtils.isEmpty(needOutBoundSid)){
                    //更改组包状态
                    updateCombineStatus(staff, Lists.newArrayList(parcel), TradeCombineParcel.STATUS_OUTBOUNDED);
                    continue;
                }else {
                    if (parcel.isWmsBtasParcel()){
                        updateWmsTradeFromParcel(staff, parcel, needOutBoundTrade, false);
                    }
                }
                //如果btas发货不成功则不变更组包状态.
                if (parcel.isBtasParcel() && !consignAndUpload(staff, parcel, needOutBoundSid, needOutBoundTrade, sidToTrade, ip, result, async, progressData)) {
                    if (async) {
                        progressData.setCountCurrent(filterTradeSidList.size() + needOutBoundTrade.size());
                        progressService.updateProgress(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO, progressData);
                    }
                    continue;
                }
                if (async) {
                    progressData.setCountCurrent(filterTradeSidList.size() + needOutBoundTrade.size());
                    progressService.updateProgress(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO, progressData);
                }
                //更改组包状态(WMS组包去除)
                if (!parcel.isWmsBtasParcel()){
                    updateCombineStatus(staff, Lists.newArrayList(parcel), TradeCombineParcel.STATUS_OUTBOUNDED);
                }
            }
        }finally {
            result.setRemoveParcelIds(removeTradeList);
            result.computeSuccessStatus();
            if (async) {
                progressData.setExecutResult(result);
                progressService.updateProgressComplete(staff, PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO, progressData);
            }
        }
        return result;
    }

    private void updateWmsTradeFromParcel(Staff staff, TradeCombineParcel parcel, Collection<Trade> trades, boolean reUpload){
        ExpressCompanyMapping expressCompanyMapping = null;
        try {
            if (Objects.nonNull(parcel.getTemplateId())){
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, parcel.getTemplateId(), false);
                if (Objects.nonNull(userWlbExpressTemplate)) {
                    ExpressCompany expressCompanyByCode = expressCompanyService.getExpressCompanyByCode(userWlbExpressTemplate.getCpCode());
                    expressCompanyMapping = getFxgCompany().get(expressCompanyByCode.getId());
                }
            }
        }catch (Exception e){
            logger.warn("组包--平台物流公司id获取失败", e);
        }
        parseExtraFields(staff, Collections.singletonList(parcel));
        String wmsBtasPlatformBizId = Objects.isNull(expressCompanyMapping) ? "12" : expressCompanyMapping.getPlatformBizId();
        Integer btasExpressProduct = Objects.isNull(parcel.getBtasExpressProduct()) ? -1 : parcel.getBtasExpressProduct();
        Boolean wmsBtasReUpload = reUpload;
        List<Trade> updates = new ArrayList<>();
        for (Trade trade : trades){
            String oldOutSid = trade.getOutSid();
            Trade update = new TbTrade();
            update.setSid(trade.getSid());
            update.setOutSid(parcel.getTrackingNo());
            TradeExt tradeExt = trade.getTradeExt();
            if (Objects.isNull(tradeExt)){
                tradeExt = new TradeExt();
            }
            //成功的订单记录当前的物流单号到tradeExt中。
            ArrayList<Object> extraFieldsList = Lists.newArrayList();
            extraFieldsList.add("$." + WMS_BTAS_PLATFORM_BIZ_ID);
            extraFieldsList.add(wmsBtasPlatformBizId);
            extraFieldsList.add("$." + WMS_BTAS_EXPRESS_PRODUCT);
            extraFieldsList.add(btasExpressProduct);
            extraFieldsList.add("$." + WMS_BTAS_OLD_OUT_SID);
            extraFieldsList.add(oldOutSid);
            extraFieldsList.add("$." + WMS_BTAS_RE_UPLOAD);
            extraFieldsList.add(wmsBtasReUpload);

            //构造updateTradeExt，将上传成功的tracking_no存入。
            TradeExt updateTradeExt = new TradeExt();
            updateTradeExt.setCompanyId(trade.getCompanyId());
            updateTradeExt.setSid(trade.getSid());
            updateTradeExt.setExtraFieldsList(extraFieldsList);
            update.setTradeExt(updateTradeExt);
            updates.add(update);
        }
        if (CollectionUtils.isNotEmpty(updates)){
            tradeUpdateService.updateTrades(staff,updates);
        }
    }

    public static boolean isUploadSuccess(Trade trade){
        String extraFields = Optional.ofNullable(trade.getTradeExt()).map(TradeExt::getExtraFields).orElse("");
        return extraFields.contains(TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey());
    }

    private boolean needSync(List<TradeCombineParcel> filterCombineParcels) {
        boolean async =false;
        for (TradeCombineParcel filterCombineParcel : filterCombineParcels) {
            if (filterCombineParcel.isBtasParcel()) {
                async = true;
                break;
            }
        }
        return async;
    }

    private void updateCombinParcelGatherType(Staff staff, Integer consignType, CombineParcelConsignResult result, List<TradeCombineParcel> filterCombineParcels) {
        List<TradeCombineParcel> updateCombineParcels = Lists.newArrayList();
        List<TradeCombineParcel> removeCombineParcels = Lists.newArrayList();
        for (TradeCombineParcel filterCombineParcel : filterCombineParcels) {
            //质检大包处理
            if (filterCombineParcel.isBtasParcel()){
                int size = result.getErrorList().size();
                CombineParcelConsignResult.CombineParcelConsignResultVo combineParcelConsignResultVo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
                combineParcelConsignResultVo.setId(filterCombineParcel.getId());
                if (consignType==null){
                    if (!TradeCombineParcel.EnumGatherType.YOUJI.getType().equals(filterCombineParcel.getGatherType()) && !TradeCombineParcel.EnumGatherType.DUMMY.getType().equals(filterCombineParcel.getGatherType())){
                        combineParcelConsignResultVo.setErrorMsg("组包没有选的的上传方式【邮寄/无需物流】");
                        result.getErrorList().add(combineParcelConsignResultVo);
                    }
                    if((TradeCombineParcel.EnumGatherType.YOUJI.getType().equals(filterCombineParcel.getGatherType()) && Strings.isNullOrEmpty(filterCombineParcel.getTrackingNo()))){
                        combineParcelConsignResultVo.setErrorMsg("组包邮寄没有填写物流单号");
                        result.getErrorList().add(combineParcelConsignResultVo);
                    }
                }else {
                    boolean error = false;
                    TradeCombineParcel updateCombineParcel = new TradeCombineParcel();
                    updateCombineParcel.setId(filterCombineParcel.getId());
                    if (consignType==1){
                        if (Strings.isNullOrEmpty(filterCombineParcel.getTrackingNo())) {
                            combineParcelConsignResultVo.setErrorMsg("组包邮寄没有填写物流单号");
                            result.getErrorList().add(combineParcelConsignResultVo);
                            error = true;
                        }
                    }
                    if (!error) {
                        updateCombineParcel.setGatherType(consignType == 1 ? TradeCombineParcel.EnumGatherType.YOUJI.getType() : TradeCombineParcel.EnumGatherType.DUMMY.getType());
                        filterCombineParcel.setGatherType(updateCombineParcel.getGatherType());
                        updateCombineParcels.add(updateCombineParcel);
                    }
                }
                if (result.getErrorList().size()-size!=0) {
                    removeCombineParcels.add(filterCombineParcel);
                }
            }
        }
        result.computeSuccessStatus();
        filterCombineParcels.removeAll(removeCombineParcels);
        if(CollectionUtils.isNotEmpty(updateCombineParcels)) {
            tradeCombineParcelDAO.batchUpdate(staff, updateCombineParcels);
        }
    }

    private boolean consignAndUpload(Staff staff, TradeCombineParcel parcel, List<Long> needOutBoundSid, List<Trade> needOutBoundTrade, Map<Long, Trade> sidToTrade,String ip,CombineParcelConsignResult result ,boolean async,ProgressData progressData) {
        if (!parcel.isBtasParcel()){
            return true;
        }
        //needOutBoundSid 发货上传
        if (CollectionUtils.isEmpty(needOutBoundSid)){
            return true;
        }
        //确定处理组包的发货类型。
        int consignType = getConsignType(parcel);
        //查出需要系统发货的单子
        List<Long> needSysConsignSid = getNeedConsignTrades(needOutBoundTrade);
        if (CollectionUtils.isNotEmpty(needSysConsignSid)){
            //系统发货一下
            List<ConsignRecord> consign = tradeService.consignBTAS(staff, needSysConsignSid.toArray(new Long[]{}), SendType.BTAS_COMBINE_PARCEL.name(), ip, null, 3, "", "", false, parcel);
            if (parcel.isWmsBtasParcel()){
                return handleWmsBTASConsignResult(staff, parcel, result, consign, consignType, progressData);
            }else {
                //处理系统发货结果
                handleConsignResult(staff,parcel, result, consign,consignType);
                return uploadCombineParcelTrackingNo(staff, parcel, needOutBoundSid, sidToTrade, result, consignType, false,async,progressData, ip);
            }
        }
        return false;

    }

    /**
     * BTAS订单的上传操作（WMS BTAS专用）
     * @param staff
     * @param trades
     * @param consignType 发货类型：1：邮寄， 2：无需物流
     * @return
     */
    public Map<Long, BtasUploadResult> uploadOMSBtasTrackingNo(Staff staff, List<Trade> trades, int consignType){
        Map<Long, BtasUploadResult> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(trades) || Objects.isNull(staff)){
            return resultMap;
        }
        long start = System.currentTimeMillis();
        ConsignUploader access = platformManagement.getAccess("fxg", ConsignUploader.class);

        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<Trade> successTradeList = new ArrayList<>();
        TradeUtils.groupByUserId(trades).forEach((userId, values)->{
            User user = staff.getUserByUserId(userId);
            if (Objects.isNull(user)){
                values.forEach(trade -> {
                    resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, "店铺信息为空"));
                });
                return;
            }
            values.forEach(trade -> {
                String tradeExtraFields = Optional.ofNullable(trade.getTradeExt()).map(TradeExt::getExtraFields).orElse("");
                if (StringUtils.isBlank(tradeExtraFields)){
                    resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, "WMS组包信息为空"));
                    return;
                }
                try {
                    JSONObject jsonObject = JSONObject.parseObject(tradeExtraFields);
                    String platformBizId = jsonObject.getString(WMS_BTAS_PLATFORM_BIZ_ID);
                    String oldTrackingNo = jsonObject.getString(WMS_BTAS_OLD_OUT_SID);
                    Boolean reUpload = jsonObject.getBoolean(WMS_BTAS_RE_UPLOAD);
                    Integer btasExpressProduct = jsonObject.getInteger(WMS_BTAS_EXPRESS_PRODUCT);
                    if (Objects.isNull(btasExpressProduct)){
                        resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, "WMS组包信息不符合上传要求"));
                        return;
                    }
                    //WMS组包二段物流小于0（一般是-1），表示没有指定组包的二段物流
                    if (btasExpressProduct < 0){
                        btasExpressProduct = null;
                    }
                    PackagesNoticeRequest packagesNoticeRequest = new PackagesNoticeRequest();
                    ExpressCompanyMapping expressCompanyMapping = new ExpressCompanyMapping();
                    expressCompanyMapping.setPlatformBizId(platformBizId);
                    packagesNoticeRequest.setExpressCompanyMapping(expressCompanyMapping);
                    packagesNoticeRequest.setTrackingNo(trade.getOutSid());
                    packagesNoticeRequest.setOldTrackingNo(oldTrackingNo);
                    packagesNoticeRequest.setReplace(Boolean.TRUE.equals(reUpload) ? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());
                    packagesNoticeRequest.setConsignType(consignType);
                    packagesNoticeRequest.setBtasExpressProduct(btasExpressProduct);
                    packagesNoticeRequest.setType(Boolean.TRUE.equals(reUpload) ? 3:1);
                    packagesNoticeRequest.setTrade(trade);
                    PlatformResponse platformResponse = access.packagesNotice(user, packagesNoticeRequest);
                    handlePlatformResponse(staff, packagesNoticeRequest, platformResponse, successTradeList, tradeTraces, jsonObject, resultMap);
                }catch (Exception e){
                    logger.error("组包上传失败：sid:"+trade.getSid(),e);
                    resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, String.format("BTAS订单组包上传异常:%s", e.getMessage())));
                }
            });
        });
        //订单更新操作
        if (CollectionUtils.isNotEmpty(successTradeList)) {
            tradeUpdateService.updateTrades(staff, successTradeList);
        }
        //订单操作日志的更新
        if (CollectionUtils.isNotEmpty(tradeTraces)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }
        //通知WMS上传结果
        tradeWarehouseService.fxgBtasTradeUpdate(staff, tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(successTradeList)));
        long end = System.currentTimeMillis();
        logger.info(LogHelper.buildLogHead(staff).append(String.format("OMS BTAS订单上传完成: took=%s", (end - start))).toString());
        return resultMap;
    }

    private void handlePlatformResponse(Staff staff, PackagesNoticeRequest packagesNoticeRequest, PlatformResponse platformResponse, List<Trade> successTradeList, List<TradeTrace> tradeTraces, JSONObject extraFields, Map<Long, BtasUploadResult> resultMap){
        Trade trade = packagesNoticeRequest.getTrade();
        if(platformResponse.isSuccess() && CommonConstants.BTAS_UPLOAD_SKIP_CODE.equals(platformResponse.getCode())){
            resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, "订单组包上传跳过"));
            return;
        }
        String consigTypeCh = packagesNoticeRequest.getConsignType() == 1 ? "邮寄" : "无需物流";
        if (!platformResponse.isSuccess()) {
            //失败的订单写到返回值
            String errMsg = "实物质检「"+ consigTypeCh +"」上传发货失败，原因："+platformResponse.getMsg()+"！";
            resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), false, errMsg));
            logger.error("WMS BTAS组包上传失败【" + platformResponse.getMsg() + "】");
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade,"组包上传", staff.getName(), new Date(), errMsg));
        } else {
            String content = "实物质检「"+consigTypeCh+"」上传发货成功！";
            //构建UpdateTrade
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(trade.getSid());
            updateTrade.setCompanyId(trade.getCompanyId());
            updateTrade.setIsUpload(1);
            //成功的订单记录当前的物流单号到tradeExt中。
            ArrayList<Object> extraFieldsList = Lists.newArrayList();
            extraFieldsList.add("$."+TradeExtendConfigsEnum.BTAS_TRACKING_NO.getKey());
            extraFieldsList.add(packagesNoticeRequest.getTrackingNo());
            extraFieldsList.add("$."+TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey());
            extraFieldsList.add(1);

            //构造updateTradeExt，将上传成功的tracking_no存入。
            TradeExt updateTradeExt = new TradeExt();
            updateTradeExt.setCompanyId(trade.getTradeExt().getCompanyId());
            updateTradeExt.setSid(trade.getTradeExt().getSid());
            updateTradeExt.setExtraFieldsList(extraFieldsList);
            //使用autoUnHookTime，存入组包上传的时间点
            updateTradeExt.setAutoUnHookTime(new Date());
            updateTrade.setTradeExt(updateTradeExt);
            successTradeList.add(updateTrade);
            resultMap.put(trade.getSid(), new BtasUploadResult(trade.getSid(), true, content));
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade,"组包上传", staff.getName(), new Date(), content));
        }
    }

    private int getConsignType(TradeCombineParcel parcel) {
        int consignType;
        if (parcel.getGatherType() - TradeCombineParcel.EnumGatherType.YOUJI.getType() == 0) {
            consignType = 1;
        } else if (parcel.getGatherType() - TradeCombineParcel.EnumGatherType.DUMMY.getType() == 0) {
            consignType = 2;
        } else {
            throw new IllegalArgumentException("组包:"+parcel.getId()+",未知的发货类型 consignType：" + parcel.getGatherType());
        }
        return consignType;
    }

    public CombineParcelConsignResult uploadCombineParcelTrackingNo(Staff staff, Long combineParcelId, String ip){
        CombineParcelConsignResult result = new CombineParcelConsignResult();
        List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(tradeCombineParcels)){
            result.setErrorMsg("没有可上传的的订单。");
            result.setSuccess(false);
            result.setProgressEnable(false);
            return result;
        }
        TradeCombineParcel parcel = tradeCombineParcels.get(0);
        Assert.isTrue(TradeCombineParcel.STATUS_OUTBOUNDED==parcel.getStatus(),"组包未出库不允许重新发货");
        Assert.isTrue(TradeCombineParcel.EnumGatherType.YOUJI.getType().equals(parcel.getGatherType()),"非邮寄不允许组包重新上传。");
        Assert.isTrue(!parcel.getTrackingNo().equals(parcel.pullExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO)),"新运单号与老运单号一致，请更新运单号后重新上传平台！");

        List<TradeParcel> tradeParcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, null);
        if (CollectionUtils.isEmpty(tradeParcelList)) {
            result.setErrorMsg("没有可上传的的订单。");
            result.setSuccess(false);
            result.setProgressEnable(false);
            return result;
        }
        //创建进度条
        Assert.isTrue(!progressService.hasProgress(staff, PROGRESS_BTAS_REUPLOAD_TRACKINGNO), "已经在执行组包出库重新上传任务，请稍后再试！");
        ProgressData progressData = progressService.getOrCreate(staff, PROGRESS_BTAS_REUPLOAD_TRACKINGNO);
        progressData.setCountAll(tradeParcelList.size());
        progressData.setCountCurrent(0);
        progressService.updateProgress(staff, PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
        result.setProgressEnable(true);

        eventCenter.fireEvent(this,new EventInfo("trade.btas.reUpload").setArgs(new Object[]{staff,combineParcelId, ip}),null);

        result.computeSuccessStatus();
        return result;
    }

    public CombineParcelConsignResult uploadCombineParcelSidTrackingNo(Staff staff,Long combineParcelId,Long sid, String ip){
        CombineParcelConsignResult result = new CombineParcelConsignResult();
        List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(tradeCombineParcels)){
            result.setErrorMsg("没有找到组包:"+combineParcelId);
            result.setSuccess(false);
            result.setProgressEnable(false);
            return result;
        }
        TradeCombineParcel parcel = tradeCombineParcels.get(0);
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sid);
        Assert.notEmpty(tradeList,"没有找到订单sid:"+sid);
        Map<Long,Trade> sidToTrade = new HashMap();
        sidToTrade.put(sid,tradeList.get(0));
        List<Long> needOutBoundSid = Lists.newArrayList(sid);
        if (parcel.isWmsBtasParcel()){
            updateWmsTradeFromParcel(staff, parcel, sidToTrade.values(), false);
        }
        uploadCombineParcelTrackingNo(staff, parcel, needOutBoundSid, sidToTrade, result, getConsignType(parcel), false, false, null, ip);
        List<TradeExt> tradeExts = tradeExtDao.queryBySidIn(staff,needOutBoundSid);
        TradeExt tradeExt = CollectionUtils.isEmpty(tradeExts)?null:tradeExts.get(0);
        result.setSuccess(Objects.nonNull(tradeExt) && StringUtils.isNotBlank(tradeExt.getExtraFields()) && tradeExt.getExtraFields().contains(TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey()));
        return result;
    }

    public void doReUpload(Staff staff,Long combineParcelId, String ip){
        CombineParcelConsignResult result = new CombineParcelConsignResult();
        List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(tradeCombineParcels)){
            logger.error(LogHelper.buildLogHead(staff).append("组包对象查询为空。").toString());
            return;
        }
        TradeCombineParcel parcel = tradeCombineParcels.get(0);
        List<TradeParcel> tradeParcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, null);
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, tradeParcelList.stream().map(TradeParcel::getSid).toArray(Long[]::new));
        List<Long> needOutBoundSid = Lists.newArrayList();
        Map<Long, Trade> sidToTrade = new HashMap<>();
        for (Trade trade : tradeList) {
            if (trade.getWarehouseId().equals(parcel.getConsignWarehouseId())) {
                needOutBoundSid.add(trade.getSid());
                sidToTrade.put(trade.getSid(), trade);
            }
        }
        ProgressData progressData = progressService.getOrCreate(staff, PROGRESS_BTAS_REUPLOAD_TRACKINGNO);
        try {
            if (parcel.isWmsBtasParcel()){
                updateWmsTradeFromParcel(staff, parcel, sidToTrade.values(), true);
            }
            uploadCombineParcelTrackingNo(staff, parcel, needOutBoundSid, sidToTrade, result, getConsignType(parcel), true, true, progressData, ip);
        }finally {
            progressData.setExecutResult(result);
            progressService.updateProgressComplete(staff, PROGRESS_BTAS_REUPLOAD_TRACKINGNO, progressData);
        }

    }

    public void notifyWms(Staff staff, List<Long> sids){
        if (CollectionUtils.isEmpty(sids)){
            return;
        }
        tradeWarehouseService.fxgBtasTradeUpdate(staff, tradeSearchService.queryBySids(staff, true, sids.toArray(new Long[0])));
    }

    /**
     * 对组包中的订单码进行上传。
     * @param staff
     * @param parcel
     * @param needOutBoundSid
     * @param sidToTrade
     * @param result
     * @param consignType
     * @return
     */
    private boolean uploadCombineParcelTrackingNo(Staff staff, TradeCombineParcel parcel, List<Long> needOutBoundSid, Map<Long, Trade> sidToTrade, CombineParcelConsignResult result, int consignType,boolean reUpload,boolean async, ProgressData progressData, String ip) {
        //查询系统发货记录。如果系统发货成功一定能查到发货记录。
        List<ConsignRecord> consignedRecords = consignRecordDao.queryBySids(staff, needOutBoundSid.toArray(new Long[]{}));
        if (CollectionUtils.isEmpty(consignedRecords)){
            if (reUpload){
                progressService.updateProgressComplete(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
            }else if (async){
                progressService.updateProgressComplete(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
            }
            return false;
        }
        //拿到所有已经系统发货的订单。
        List<Long> consignedSid = consignedRecords.stream().map(x -> x.getSid()).collect(Collectors.toList());
        //直接检查这些订单的tradeExt.如果orderCode没有上传则需要上传一下。needUploadTrade
        List<Trade> needUploadTrade = Lists.newArrayList();
        for (Long sid : consignedSid) {
            Trade trade = sidToTrade.get(sid);
            if (existUnUploadOrdeCode(trade)){
                needUploadTrade.add(trade);
            }
        }
        int handleCount=needOutBoundSid.size()-needUploadTrade.size();
        if (reUpload){
            progressData.setCountCurrent(progressData.getCountCurrent()+handleCount);
            progressService.updateProgress(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
        }else if (async){
            progressData.setCountCurrent(progressData.getCountCurrent()+handleCount);
            progressService.updateProgress(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
        }
        //WMS组包，直接触发上传
        if (parcel.isWmsBtasParcel()){
            SendType type = StringUtils.isBlank(parcel.getTrackingNo()) ? SendType.DUMMY : SendType.OFFLINE;
            consignUploadService.asyncUpload(staff, needUploadTrade.stream().map(Trade::getSid).collect(Collectors.toList()), type , ip, null, null, null);
            if (reUpload){
                progressData.setCountCurrent(progressData.getCountCurrent() + needUploadTrade.size());
                progressService.updateProgress(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
            }else if (async){
                progressData.setCountCurrent(progressData.getCountCurrent() + needUploadTrade.size());
                progressService.updateProgress(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
            }
            return true;
        }

        //通过userId对订单分组。
        Map<Long, List<Trade>> userIdToTrades = needUploadTrade.stream().collect(Collectors.groupingBy(Trade::getUserId));
        ConsignUploader access = platformManagement.getAccess("fxg", ConsignUploader.class);
        //处理物流信息
        String trackingNo = parcel.getTrackingNo();
        ExpressCompany expressCompanyByCode = new ExpressCompany();
        ExpressCompanyMapping expressCompanyMapping =new ExpressCompanyMapping();
        String oldTrackingNo = parcel.pullExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO);

        UserWlbExpressTemplate userWlbExpressTemplate = null;
        if (Objects.nonNull(parcel.getTemplateId())){
            userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, parcel.getTemplateId(), false);
            if (Objects.nonNull(userWlbExpressTemplate)) {
                expressCompanyByCode = expressCompanyService.getExpressCompanyByCode(userWlbExpressTemplate.getCpCode());
                expressCompanyMapping = getFxgCompany().get(expressCompanyByCode.getId());
            }
        }

        List<Trade> updateTradeList = new ArrayList<>();
        List<Trade> successTradeList = new ArrayList<>();
        List<TradeTrace>  tradeTraces = new ArrayList<>();
        List<OrderExt> updateOrderExtList = new ArrayList<>();
        parseExtraFields(staff, Collections.singletonList(parcel));

        for (Long userId : userIdToTrades.keySet()) {
            User user = staff.getUserByUserId(userId);
            if (Objects.isNull(user)){
                logger.warn(LogHelper.buildLogHead(staff).append(String.format("店铺(%s)信息为空,跳过BTAS上传流程", userId)).toString());
                continue;
            }
            List<Trade> trades = userIdToTrades.get(userId);
            for (Trade trade : trades) {
                try {
                    PackagesNoticeRequest packagesNoticeRequest = new PackagesNoticeRequest();
                    packagesNoticeRequest.setExpressCompany(expressCompanyByCode);
                    packagesNoticeRequest.setExpressCompanyMapping(expressCompanyMapping);
                    packagesNoticeRequest.setTrackingNo(trackingNo);
                    packagesNoticeRequest.setOldTrackingNo(oldTrackingNo);
                    packagesNoticeRequest.setReplace(reUpload? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());
                    packagesNoticeRequest.setConsignType(consignType);
                    packagesNoticeRequest.setBtasExpressProduct(parcel.getBtasExpressProduct());
                    packagesNoticeRequest.setType(reUpload?3:1);
                    packagesNoticeRequest.setTrade(trade);
                    PlatformResponse platformResponse = access.packagesNotice(user, packagesNoticeRequest);
                    handlePlatformResponse(staff, parcel, packagesNoticeRequest, platformResponse, updateTradeList, successTradeList, tradeTraces, updateOrderExtList, result);
                }catch (RuntimeException e){
                    Trade updateTrade = new TbTrade();
                    updateTrade.setSid(trade.getSid());
                    updateTrade.addV(4);
                    updateTradeList.add(updateTrade);
                    logger.error("组包上传失败：sid:"+trade.getSid(),e);
                }
                if (reUpload){
                    progressData.setCountCurrent(progressData.getCountCurrent()+1);
                    progressService.updateProgress(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
                }else if (async){
                    progressData.setCountCurrent(progressData.getCountCurrent()+1);
                    progressService.updateProgress(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(tradeTraces)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }
        if (CollectionUtils.isNotEmpty(updateTradeList)) {
            tradeUpdateService.updateTrades(staff,updateTradeList);
            if (CollectionUtils.isNotEmpty(successTradeList)){
                TradeCombineParcel updateTradeCombineParcel = new TradeCombineParcel();
                updateTradeCombineParcel.setId(parcel.getId());
                updateTradeCombineParcel.putExtraFields(TradeCombineParcel.ExtraField.UPLOADED_TRACKING_NO,trackingNo);
                tradeCombineParcelDAO.batchUpdate(staff, Lists.newArrayList(updateTradeCombineParcel));
            }
        }
        if (CollectionUtils.isNotEmpty(updateOrderExtList)){
            orderExtDao.batchUpdate(staff, updateOrderExtList);
        }

        //最后查一遍tradeExt,如果全部上传成功则出库成功。
        List<TradeExt> tradeExts = tradeExtDao.queryBySidIn(staff, needOutBoundSid);
        boolean allUpload =true;
        for (TradeExt tradeExt : tradeExts) {
            if(StringUtils.isBlank(tradeExt.getExtraFields()) || !tradeExt.getExtraFields().contains(TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey())){
                allUpload = false;
            }
        }

        if (reUpload){
            progressService.updateProgressComplete(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
        }else if (async){
            progressService.updateProgressComplete(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
        }

        return allUpload;
    }

    private List<Long> getNeedConsignTrades(List<Trade> needOutBoundTrade) {
        List<Long> needSysConsignSid = Lists.newArrayList();
        for (Trade trade : needOutBoundTrade) {
            //如果没有系统发货，就系统发货
            if (!TradeUtils.isAfterSendGoods(trade)){
                needSysConsignSid.add(trade.getSid());
            }
        }
        return needSysConsignSid;
    }

    private void handleConsignResult(Staff staff,TradeCombineParcel parcel, CombineParcelConsignResult result, List<ConsignRecord> consign,int consignType) {
        if (result.getErrorList()==null) {
            result.setErrorList(Lists.newArrayList());
        }
        if (CollectionUtils.isEmpty(consign)){
            return;
        }
        List<TradeTrace> tradeTraces = new LinkedList<>();
        String consigTypeCh = consignType == 1 ? "邮寄" : "无需物流";
        List<Long> sids = consign.stream().map(x -> x.getSid()).collect(Collectors.toList());
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sids.toArray(new Long[0]));
        Map<Long, Trade> sidToTrade = tradeList.stream().collect(Collectors.toMap(x -> x.getSid(), x -> x));
        for (ConsignRecord consignRecord : consign) {
            if (consignRecord.getIsError()!=0){
                logger.error("btas组包上传失败【" + consignRecord.getErrorDesc() + "】");
                CombineParcelConsignResult.CombineParcelConsignResultVo vo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
                vo.setId(parcel.getId());
                vo.setSid(consignRecord.getSid());
                vo.setErrorMsg("btas组包上传失败【" + consignRecord.getErrorDesc() + "】");
                result.getErrorList().add(vo);
                tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff,sidToTrade.get(consignRecord.getSid()),"组包上传",staff.getName(),new Date(),"大包单号["+parcel.getId()+"], 实物质检「"+ consigTypeCh +"」"+"上传发货失败，原因："+consignRecord.getErrorDesc()+"！"));
            }
        }
        result.computeSuccessStatus();
        tradeTraceService.batchAddTradeTrace(staff,tradeTraces);
    }

    private boolean handleWmsBTASConsignResult(Staff staff,TradeCombineParcel parcel, CombineParcelConsignResult result, List<ConsignRecord> consign, int consignType , ProgressData progressData) {
        if (result.getErrorList()==null) {
            result.setErrorList(Lists.newArrayList());
        }
        List<TradeTrace> tradeTraces = new LinkedList<>();
        String consigTypeCh = consignType == 1 ? "邮寄" : "无需物流";
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, consign.stream().map(ConsignRecord::getSid).toArray(Long[]::new));
        Map<Long, Trade> sidToTrade = tradeList.stream().collect(Collectors.toMap(TradeBase::getSid, x -> x));
        boolean allSuccess = true;
        for (ConsignRecord consignRecord : consign) {
            if (consignRecord.getIsError()!=0){
                allSuccess = false;
                logger.error("WMS BTAS组包上传失败【" + consignRecord.getErrorDesc() + "】");
                CombineParcelConsignResult.CombineParcelConsignResultVo vo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
                vo.setId(parcel.getId());
                vo.setSid(consignRecord.getSid());
                vo.setErrorMsg("WMS BTAS组包上传失败【" + consignRecord.getErrorDesc() + "】");
                result.getErrorList().add(vo);
                tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff,sidToTrade.get(consignRecord.getSid()),"组包上传",staff.getName(),new Date(),"大包单号["+parcel.getId()+"], 实物质检「"+ consigTypeCh +"」"+"上传发货失败，原因："+consignRecord.getErrorDesc()+"！"));
            }
        }
        result.computeSuccessStatus();
        tradeTraceService.batchAddTradeTrace(staff,tradeTraces);
        int handleCount = consign.size();
        if (true){
            progressData.setCountCurrent(progressData.getCountCurrent() + handleCount);
            progressService.updateProgress(staff,PROGRESS_BTAS_REUPLOAD_TRACKINGNO,progressData);
        }else if (false){
            progressData.setCountCurrent(progressData.getCountCurrent() + handleCount);
            progressService.updateProgress(staff,PROGRESS_BTAS_CONSIGN_UPLOAD_TRACKINGNO,progressData);
        }
        return allSuccess;
    }

    private void handlePlatformResponse(Staff staff,TradeCombineParcel parcel, PackagesNoticeRequest packagesNoticeRequest,PlatformResponse platformResponse,List<Trade> updateTradeList,List<Trade> successTradeList,List<TradeTrace> tradeTraces,List<OrderExt> updateOrderExtList,CombineParcelConsignResult result){
        if(platformResponse.isSuccess() && CommonConstants.BTAS_UPLOAD_SKIP_CODE.equals(platformResponse.getCode())){
            logger.info(LogHelper.buildLogHead(staff).append("订单组包上传跳过，组包id:").append(parcel.getId()).append(",sid:").append(packagesNoticeRequest.getTrade().getSid()).toString());
            return;
        }
        //搜集orderExt统一更新orderCode上传状态。
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(packagesNoticeRequest.getTrade());
        for (Order order : orders4Trade){
            OrderExt newOrderExt = order.getOrderExt();
            if (null != newOrderExt){
                OrderExt updateOrderExt = new OrderExt();
                updateOrderExt.setId(order.getId());
                updateOrderExt.setCustomization(newOrderExt.getCustomization());
                updateOrderExtList.add(updateOrderExt);
            }
        }
        String consigTypeCh = packagesNoticeRequest.getConsignType() == 1 ? "邮寄" : "无需物流";
        if (!platformResponse.isSuccess()) {
            //失败的订单写到返回值
            CombineParcelConsignResult.CombineParcelConsignResultVo vo = new CombineParcelConsignResult.CombineParcelConsignResultVo();
            vo.setId(parcel.getId());
            vo.setSid(packagesNoticeRequest.getTrade().getSid());
            vo.setErrorMsg("btas组包上传失败【" + platformResponse.getMsg() + "】");
            if (result.getErrorList()==null){
                result.setErrorList(new ArrayList<>());
            }
            result.getErrorList().add(vo);
            logger.error("btas组包上传失败【" + platformResponse.getMsg() + "】");
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(packagesNoticeRequest.getTrade().getSid());
            updateTrade.addV(4);
            updateTradeList.add(updateTrade);
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff,packagesNoticeRequest.getTrade(),"组包上传",staff.getName(),new Date(),"大包单号["+parcel.getId()+"], 实物质检「"+ consigTypeCh +"」上传发货失败，原因："+platformResponse.getMsg()+"！"));
        } else {
            //构建UpdateTrade
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(packagesNoticeRequest.getTrade().getSid());
            updateTrade.setCompanyId(packagesNoticeRequest.getTrade().getCompanyId());
            updateTrade.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
            updateTrade.setIsUpload(1);
            //成功的订单记录当前的物流单号到tradeExt中。
            JSONObject jsonObject = JSONObject.parseObject(packagesNoticeRequest.getTrade().getTradeExt().getExtraFields());
            if (jsonObject == null) {
                jsonObject = new JSONObject();
            }
            ArrayList<Object> extraFieldsList = Lists.newArrayList();
            extraFieldsList.add("$."+TradeExtendConfigsEnum.BTAS_TRACKING_NO.getKey());
            extraFieldsList.add(packagesNoticeRequest.getTrackingNo());
            extraFieldsList.add("$."+TradeExtendConfigsEnum.BTAS_SUCCESS_UPLOADED.getKey());
            extraFieldsList.add(1);

            //构造updateTradeExt，将上传成功的tracking_no存入。
            TradeExt updateTradeExt = new TradeExt();
            updateTradeExt.setCompanyId(packagesNoticeRequest.getTrade().getTradeExt().getCompanyId());
            updateTradeExt.setSid(packagesNoticeRequest.getTrade().getTradeExt().getSid());
            updateTradeExt.setExtraFieldsList(extraFieldsList);
            //使用autoUnHookTime，存入组包上传的时间点
            updateTradeExt.setAutoUnHookTime(new Date());
            updateTrade.setTradeExt(updateTradeExt);
            updateTradeList.add(updateTrade);
            successTradeList.add(updateTrade);
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff,packagesNoticeRequest.getTrade(),"组包上传",staff.getName(),new Date(),"大包单号["+parcel.getId()+"], 实物质检「"+consigTypeCh+"」上传发货成功！"));
        }
    }

    /**是否存在没上传的订单码,存在返回true*/
    private boolean existUnUploadOrdeCode(Trade trade){
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        Set<String> orderCodeSet = new HashSet<>();
        //先把所有的订单码收集到一起。
        for (Order order : orderList) {
            List<String> btasOrderCode = OrderUtils.getBtasOrderCode(order);
            orderCodeSet.addAll(btasOrderCode);
        }
        if (CollectionUtils.isEmpty(orderCodeSet)){
            return false;
        }
        for (Order order : orderList) {
            if (null == order.getOrderExt()){
                continue;
            }
            if (StringUtils.isBlank(order.getOrderExt().getCustomization())){
                continue;
            }
            JSONObject customization = JSONObject.parseObject(order.getOrderExt().getCustomization());
            Map<String,String> objQualityMap = customization.getObject(CommonConstants.BTAS_OBJECT_QUALITY_STATUS,Map.class);
            if (MapUtils.isEmpty(objQualityMap)){
                continue;
            }
            //移出掉已经组包上传的
            orderCodeSet.removeAll(objQualityMap.keySet());
        }
        //如果是空就是全部上传了，不存在没上传返回false
        return !orderCodeSet.isEmpty();
    }

    @Override
    public void replenishTrackingNo(Staff staff, ChannelConfig expressConfig, Long combineParcelId, Trade trade) {
        List<TradeCombineParcel> combineParcelList= tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(combineParcelList)){
            return;
        }
        TradeCombineParcel tradeCombineParcel= combineParcelList.get(0);
        if (tradeCombineParcel.getCombineParcelType() != 1 || StringUtils.isEmpty(tradeCombineParcel.getTrackingNo())) {
            return;
        }
        expressConfig.setTrackingNo(tradeCombineParcel.getTrackingNo());
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, tradeCombineParcel.getTemplateId(), false);
        if (trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_SMT)) {
            SumaitongLogisticsEnum sumaitongLogisticsEnum = SumaitongLogisticsEnum.getEnum(userWlbExpressTemplate.getCpCode());
            expressConfig.setCompanyName(sumaitongLogisticsEnum.getServiceName());
            expressConfig.setCompanyCode(String.valueOf(sumaitongLogisticsEnum.getLogisticsCode()));

        } else if (trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU)) {
            ICBULogisticsEnum icbuLogisticsEnum = ICBULogisticsEnum.getEnum(userWlbExpressTemplate.getCpCode());
            expressConfig.setCompanyName(icbuLogisticsEnum.getServiceName());
            expressConfig.setCompanyCode(String.valueOf(icbuLogisticsEnum.getLogisticsCode()));
        }
    }

    @Cacheable(value = "defaultCache#3600", key = "'TradeCombineParcelService_getShopMap_' + #staff.companyId")
    public Map<Long, Shop> getShopMap(Staff staff) {
        List<Shop> shops = shopService.queryByCompanyId(staff);
        if (CollectionUtils.isEmpty(shops)) {
            return null;
        }
        return shops.stream().collect(Collectors.toMap(Shop::getTaobaoId, shop -> shop, (v1, v2) -> v2));
    }


    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(combineParcelUpdates)) {
            tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
        }
    }

   public WlbStatus WaybillCancel(Staff staff,TradeCombineParcel combineParcel){
       UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, combineParcel.getTemplateId(), false);
       Assert.notNull(userWlbExpressTemplate, "模板找不到");
       CustomWaybillCancelRequest cancelRequest = new CustomWaybillCancelRequest(new Long[]{combineParcel.getId()}, EnumOutSidStatus.BOUND.getValue(), true,
               null, EnumOutSidType.TYPE_COMBINE_PARCEL.getValue(), userWlbExpressTemplate.getWlbType());
       WlbStatus result = (WlbStatus) userWlbExpressCompanyAdapterService.customWaybillCodeCancelByType(staff, cancelRequest);
       if ("success".equals(result.getStatus()) || CollectionUtils.isNotEmpty(result.getSuccessResult())) {
           TradeCombineParcel update = new TradeCombineParcel();
           update.setId(combineParcel.getId());
           update.setTrackingNo("");
           tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
       } else {
           throw new IllegalArgumentException(result.getErrorMessage());
       }
       return result;
   }

   private static class BtasCombineParcelUploadRequest{
       Staff staff;
       CombineParcelConsignRequest request;
       CombineParcelConsignResult result;
       TradeCombineParcel validateDatum;
       String trackingNo;
       ExpressCompany expressCompanyByCode;
       ExpressCompanyMapping expressCompanyMapping;
       int consignType;
       Integer btasExpressProduct;
       List<Trade> updateTradeList;
       List<OrderExt> updateOrderExtList;
       List<TradeTrace> tradeTraces;
       Trade trade;
       boolean resultSkip=false;
    }

    @Override
    public TradeCombineParcelDetail getTradeCombineParcelDetail(Staff staff, Long combineParcelId) {
        TradeCombineParcelDetail tradeCombineParcelDetail = new TradeCombineParcelDetail();
        List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(tradeCombineParcels)){
            return tradeCombineParcelDetail;
        }
        List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, null);
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, parcelList.stream().map(TradeParcel::getSid).toArray(Long[]::new));
        tradeCombineParcelDetail.setTradeNum(trades.size());
        //订单码数量
        int orderCodeNum = 0;
        List<TradeParcelBTASDetail> details = new ArrayList<>();
        for (Trade trade : trades){
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders){
                List<String> orderCodes = OrderUtils.getBtasOrderCode(order);
                orderCodeNum += orderCodes.size();
                orderCodes.forEach(code ->{
                    TradeParcelBTASDetail parcelBTASDetail = new TradeParcelBTASDetail();
                    parcelBTASDetail.setSid(trade.getSid());
                    parcelBTASDetail.setOrderCode(code);
                    parcelBTASDetail.setOutId(order.getSysOuterId());
                    parcelBTASDetail.setIsExcept(trade.getIsExcep());
                    details.add(parcelBTASDetail);
                });
            }
        }
        tradeCombineParcelDetail.setOrderNum(orderCodeNum);
        tradeCombineParcelDetail.setParcelDetails(details);
        tradeCombineParcelDetail.setStatus(tradeCombineParcels.get(0).getStatus());
        return tradeCombineParcelDetail;
    }
}

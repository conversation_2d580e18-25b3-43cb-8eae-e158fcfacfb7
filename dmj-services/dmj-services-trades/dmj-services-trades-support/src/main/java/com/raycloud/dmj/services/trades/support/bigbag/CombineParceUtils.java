package com.raycloud.dmj.services.trades.support.bigbag;

import com.raycloud.dmj.domain.logistics.EnumShopeeTransitWarehouse;
import com.raycloud.dmj.domain.pt.UserLogisticsChannel;
import com.raycloud.dmj.domain.trades.TradeCombineParcel;
import com.raycloud.dmj.domain.trades.TradeParcel;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 组包工具类
 * @author: zhaojianbo
 * @time: 2022/4/14 10:18 上午
 */
public class CombineParceUtils {
    private static final String SESSION_SUFFIX = "-null";
    /**
     * 按指定包裹拆分订单
     * @param source
     * @param n
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> source,int n){
        List<List<T>> result = new ArrayList<List<T>>();
        int remaider = source.size() % n;  //(先计算出余数)
        int number = source.size() / n;  //然后是商
        int offset = 0;//偏移量
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    /**
     * 转换中转仓信息
     * @param tradeParcel
     * @param channelMap
     */
    public static void convertWarehouseName(TradeParcel tradeParcel, Map<Long, UserLogisticsChannel> channelMap) {
        if (CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(tradeParcel.getSource())) {
            tradeParcel.setTransferWarehouseName(tradeParcel.getStoreName());
            UserLogisticsChannel channel = channelMap.get(tradeParcel.getTemplateId());
            tradeParcel.setShippingCarrier(channel == null ? tradeParcel.getShippingCarrier() : channel.getName());
        } else if (CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(tradeParcel.getSource())) {
            tradeParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(tradeParcel.getShippingCarrier(), tradeParcel.getWarehouseAddress()));
        } else {
            tradeParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(tradeParcel.getTransferWarehouseId(), tradeParcel.getWarehouseAddress()));
        }
    }

    /**
     * 判断是否是shopee  v1接口授权的user
     * 数据库中，v1的用户存在下面三种情况：
     *      1、没有SessionId
     *      2、有SessionId，但是SessionId和user的taobaoId相等
     *      3、有SessionId，但是SessionId的值是shopee-null
     * @param user
     * @return
     */
    public static boolean isShopeeV1(User user) {
        if(StringUtils.isBlank(user.getSessionId())) {
            return true;
        }
        if(user.getTaobaoId().toString().equals(user.getSessionId())) {
            return true;
        }
        if(user.getSessionId().endsWith(SESSION_SUFFIX)){
            return true;
        }
        return false;
    }

}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trades.ITradeExceptQueryService;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2024/2/18 6:01 下午
 * @Description:
 */
@Service
public class TradeExceptQueryService implements ITradeExceptQueryService {

    @Resource(name = "tradePSqlQueryBuilder")
    private TradeQueryBuilder tradeSqlQueryBuilder;

    @Resource
    private ITradeTagService tradeTagService;
    @Override
    public void buildExceptQuery(Staff staff, Query q, TradeQueryParams params) {

        List<Long> containsExceptIds = getContainsExceptIds(params);
        List<Long> excludeExceptIds = getExcludeExceptIds(params);
        // 是否无异常订单
        boolean isTickExcept = isTickExcept(params);
        Query q0 = new Query();
        Query query = null;
        boolean isExcludeQuery = isExcludeQuery(params);
        boolean onlyContainsQuery = isOnlyContainsQuery(params);
        boolean isContainsQuery = false;
        boolean isAndQuery = isAndQuery(params);
        if (onlyContainsQuery) {
            // 仅包含
            query = buildExceptOnlyContainsQuery(staff, containsExceptIds, isTickExcept);
        } else if (isAndQuery) {
            // 同时包含
            query = buildExceptSqlAllContain(staff, containsExceptIds, isTickExcept);
        } else {
            // 排除
            if(isExcludeQuery){
               // 排除可以使用包含的exceptId参数
                excludeExceptIds.addAll(containsExceptIds);
            }else {
                isContainsQuery=true;
                // 包含
                query = buildExceptContainsQuery(staff, containsExceptIds, isTickExcept);
            }
        }
        if (query != null && query.getQ().length() > 0) {
            if (q0.getQ().length() > 0) {
                q0.append(" and ").append(query);
            } else {
                q0.append(query);
            }
        }


        // 排除异常查询
        if (CollectionUtils.isNotEmpty(excludeExceptIds) || isExcludeQuery) {
            Query excludeQuery = new Query();
            if (onlyContainsQuery || isContainsQuery || isAndQuery) {
                // 存在任意包含查询时，不能排除无异常订单
                excludeQuery = buildExceptExcludeQuery(staff, excludeExceptIds, false);
            } else {
                excludeQuery = buildExceptExcludeQuery(staff, excludeExceptIds, isTickExcept);
            }

            if (q0.getQ().length() > 0) {
                q0.append(" and ").append(excludeQuery);
            } else {
                q0.append(excludeQuery);
            }
        }


        List<Long> otherContainsQueryExceptIds = new ArrayList<>();
        List<Long> otherExcludeQueryExceptIds = new ArrayList<>();
        buildOtherQueryExceptIds(otherContainsQueryExceptIds, otherExcludeQueryExceptIds, params.getIsRefund(), ExceptEnum.REFUNDING);
        buildOtherQueryExceptIds(otherContainsQueryExceptIds, otherExcludeQueryExceptIds, params.getIsHalt(), ExceptEnum.HALT);
        if (CollectionUtils.isNotEmpty(otherContainsQueryExceptIds)) {
            Query q1 = buildExceptContainsQuery(staff, otherContainsQueryExceptIds, false);
            if (q0.getQ().length() > 0) {
                q0.append(" and ").append(q1);
            } else {
                q0.append(q1);
            }
        }
        if (CollectionUtils.isNotEmpty(otherExcludeQueryExceptIds)) {
            Query q2 = buildExceptExcludeQuery(staff, otherExcludeQueryExceptIds, false);
            if (q0.getQ().length() > 0) {
                q0.append(" and ").append(q2);
            } else {
                q0.append(q2);
            }
        }
        if(q0.getQ().length()>0){
            q.and().append("(").append(q0).append(")");
        }
    }

    /**
     * 老的异常查询
     * @param staff
     * @param q
     * @param params
     */
    @Override
    public void buildOldExceptQuery(Staff staff, Query q, TradeQueryParams params) {
        tradeSqlQueryBuilder.buildExceptionQuery(staff, q, params, false, false);
        // 筛选订单异常状态，支持同时筛选包含和排除指定异常状态 https://gykj.yuque.com/entavv/xb9xi5/pqsth9
        if ((params.getExcludeExceptionStatus() != null && params.getExcludeExceptionStatus().length > 0) ||
                (params.getExcludeExceptIds() != null && params.getExcludeExceptIds().length > 0)) {
            TradeQueryParams paramsCopy = new TradeQueryParams();
            BeanUtils.copyProperties(params, paramsCopy);
            //原组合：包含+排除中包含是否包含除无异常之外其他异常，因为接下来排除会加入异常中
           boolean unchanged = (params.getExceptionStatus() == null || params.getExceptionStatus().length <= 0)
                    && (params.getExceptIds() == null || params.getExceptIds().length <= 0
                    || (params.getExceptIds().length == 1 && "-1".equals(params.getExceptIds()[0])));
            paramsCopy.setExceptionStatus(paramsCopy.getExcludeExceptionStatus());
            paramsCopy.setExceptIds(paramsCopy.getExcludeExceptIds());
            // 排除
            paramsCopy.setOnlyContain(2);
            tradeSqlQueryBuilder.buildExceptionQuery(staff, q, paramsCopy, true, unchanged);
        }
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_refund", params.getIsRefund());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "t.is_halt", params.getIsHalt());
    }


    /**
     * 包含异常
     * @param staff
     */
    private Query buildExceptContainsQuery(Staff staff, List<Long> exceptIdList, boolean isTickExcep) {
        Query q0 = new Query();
        Query q1 = new Query();
        Query q2 = new Query();
        // 无异常
        if (isTickExcep) {
            q0.append(" t.is_excep=0 ");
        }
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(exceptIdList)) {
            sb.append("  t.sid IN ( select s.sid from trade_except_").append(staff.getDbInfo().getTradeExceptDbNo())
                    .append(" s where").append(" s.company_id =  ").append(staff.getCompanyId()).append(" and enable_status=1 ").append(" and s.company_id=t.company_id");
            sb.append(" and s.sid=t.sid ").append(" and s.except_id in (").append(StringUtils.join(exceptIdList, ",")).append("))");
            q2.append(sb.toString());
        }
        if (q0.getQ().length() == 0 && q2.getQ().length() == 0) {
            return q1;
        }
        q1.append(" ( ");
        if (q0.getQ().length() > 0) {
            q1.append(q0);
        }
        if (q0.getQ().length() > 0 && q2.getQ().length() > 0) {
            q1.append(" OR ").append(q2);
        }
        if (q0.getQ().length() == 0 && q2.getQ().length() > 0) {
            q1.append(q2);
        }
        q1.append(" ) ");
        return q1;
    }

    /**
     * 仅包含
     * @param staff
     * @return
     */
    private Query buildExceptOnlyContainsQuery(Staff staff, List<Long> exceptIdList,boolean isTickExcep) {
        Query q = new Query();
        Query q0 = new Query();
        if (isTickExcep) {
            q0.append(" t.is_excep=0 ");
        }
        Query query = new Query();
        if (CollectionUtils.isNotEmpty(exceptIdList)) {
            query = buildExceptOnlyContainsQuery(staff, exceptIdList);
        }
        if(query.getQ().length() == 0 && q0.getQ().length() == 0){
            return q;
        }
        if (query.getQ().length() > 0 || q0.getQ().length() > 0) {
            q.append(" ( ");
            if (query.getQ().length() > 0 && q0.getQ().length() > 0) {
                q.append(query).append(" OR ").append(q0);
            } else if (query.getQ().length() > 0) {
                q.append(query);
            } else {
                q.append(q0);
            }
            q.append(" ) ");
        }
        return q;
    }

    private Query buildExceptOnlyContainsQuery(Staff staff, List<Long> exceptIdList){
        Query q0 = new Query();
        List<TradeTag> list = tradeTagService.list(staff, 1);
        List<Long> customerExceptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            customerExceptIds = list.stream().map(TradeTag::getId).collect(Collectors.toList());
        }
        List<Long> excludeExceptIds = new ArrayList<>();
        for (Long exceptId : customerExceptIds) {
            if (!exceptIdList.contains(exceptId)) {
                excludeExceptIds.add(exceptId);
            }
        }
        for (ExceptEnum exceptEnum : ExceptEnum.allExceptEnums) {
            if (!exceptIdList.contains(exceptEnum.getId())) {
                excludeExceptIds.add(exceptEnum.getId());
            }
        }
        Query containsQuery = buildExceptContainsQuery(staff, exceptIdList,false);
        Query excludeQuery = buildExceptExcludeQuery(staff, excludeExceptIds,false);
        if (containsQuery.getQ().length() == 0 && excludeQuery.getQ().length() == 0) {
            return q0;
        }
        if (excludeQuery.getQ().length() > 0 || containsQuery.getQ().length() > 0) {
            q0.append(" ( ");
            if (excludeQuery.getQ().length() > 0 && containsQuery.getQ().length() > 0) {
                q0.append(excludeQuery).append(" and ").append(containsQuery);
            } else if (containsQuery.getQ().length() > 0) {
                q0.append(containsQuery);
            } else {
                q0.append(excludeQuery);
            }
            q0.append(" ) ");
        }

        return q0;
    }

    /**
     *  排除
     * @param staff
     * @param exceptIdList
     * @return
     */
    private Query buildExceptExcludeQuery(Staff staff, List<Long> exceptIdList,boolean isTickExcep){
        Query q0 = new Query();
        Query q1 = new Query();
        Query q2 = new Query();
        // 无异常
        if (isTickExcep) {
            q0.append(" t.is_excep=1 ");
        }
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(exceptIdList)) {
            sb.append(" t.sid NOT IN  ( select s.sid from trade_except_").append(staff.getDbInfo().getTradeExceptDbNo())
                    .append(" s where").append(" s.company_id =  ").append(staff.getCompanyId()).append(" and enable_status=1 ").append(" and s.company_id=t.company_id");
            sb.append(" and s.sid=t.sid ").append(" and s.except_id in (").append(StringUtils.join(exceptIdList, ",")).append("))");
            q2.append(sb.toString());
        }
        if (q0.getQ().length() == 0 && q2.getQ().length() == 0) {
            return q1;
        }
        q1.append(" ( ");
        if (q0.getQ().length() > 0) {
            q1.append(q0);
        }
        if (q0.getQ().length() > 0 && q2.getQ().length() > 0) {
            q1.append(" and ").append(q2);
        }
        if (q0.getQ().length() == 0 && q2.getQ().length() > 0) {
            q1.append(q2);
        }
        q1.append(" ) ");
        return q1;
    }


    /**
     * 有且包含,同时包含
     * @param staff
     * @param exceptIdList
     * @return
     */
    private Query buildExceptSqlAllContain(Staff staff, List<Long> exceptIdList,boolean isTickExcep) {
        Query q0 = new Query();
        Query q1 = new Query();
        Query q2 = new Query();
        if (isTickExcep) {
            q0.append(" t.is_excep=0 ");
        }
        List<Long> exceptIds = null;
        List<Query> queryList=new ArrayList<>();
        for (Long exceptId : exceptIdList) {
            exceptIds = new ArrayList<>();
            exceptIds.add(exceptId);
            Query query = buildExceptContainsQuery(staff, exceptIds,false);
            if(query.getQ().length()>0){
                queryList.add(query);
            }
        }
        if (CollectionUtils.isNotEmpty(queryList)) {
            for (int i = 0; i < queryList.size(); i++) {
                if (i == 0) {
                    q1.append(queryList.get(i));
                } else {
                    q1.append(" and ").append(queryList.get(i));
                }
            }
        }
        if (q0.getQ().length() == 0 && q1.getQ().length() == 0) {
            return q2;
        }
        q2.append(" ( ");
        if (q0.getQ().length() > 0) {
            q2.append(q0);
        }
        if (q0.getQ().length() > 0 && q1.getQ().length() > 0) {
            q2.append(" and ").append(q1);
        }
        if (q0.getQ().length() == 0 && q2.getQ().length() > 0) {
            q2.append(q1);
        }
        q2.append(" ) ");
        return q2;
    }


    /**
     * 仅包含
     * @param params
     * @return
     */
    private boolean isOnlyContainsQuery(TradeQueryParams params){
        Integer itemExcepOpen = params.getItemExcepOpen();
        Integer onlyContain = params.getOnlyContain();
        return itemExcepOpen != null && itemExcepOpen - 1 == 0 && onlyContain != null && onlyContain - 1 == 0;
    }

    /**
     * 排除
     *
     * @param params
     * @return
     */
    private boolean isExcludeQuery(TradeQueryParams params) {
        Integer onlyContain = params.getOnlyContain();
        return onlyContain != null && onlyContain - 2 == 0 ;
    }

    /**
     * 且
     * @param params
     * @return
     */
    private boolean isAndQuery(TradeQueryParams params){
        Integer onlyContain = params.getOnlyContain();
        return onlyContain != null && onlyContain - 3 == 0;
    }

    /**
     * 无异常订单
     * @param params
     * @return
     */
    private boolean isTickExcept(TradeQueryParams params){

        return Objects.equals(params.getTickExcep(),1);
    }



    private List<Long> getContainsExceptIds(TradeQueryParams params){
        String[] exceptions = params.getExceptionStatus();
        String[] exceptIds = params.getExceptIds();
        List<Long> exceptIdList = new ArrayList<>();
        if (exceptions != null && exceptions.length != 0) {
            List<String> collect = Arrays.stream(exceptions).collect(Collectors.toList());
            List<ExceptEnum> exceptEnums = ExceptEnum.getEnumByOldEnglish(collect);
            List<Long> exceptEnumIds = exceptEnums.stream().map(ExceptEnum::getId).collect(Collectors.toList());
            exceptIdList.addAll(exceptEnumIds);
        }
        if (exceptIds != null && exceptIds.length != 0) {
            List<Long> collect = Arrays.stream(exceptIds).map(Long::valueOf).filter(exceptId->exceptId>0L).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                for(Long exceptId:collect){
                    ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
                    // 可能是三方仓的异常
                    if(exceptEnum!=null){
                        exceptIdList.add(exceptEnum.getId());
                    }else {
                        exceptIdList.add(exceptId);
                    }
                }
            }
        }
        return exceptIdList;
    }



    private List<Long> getExcludeExceptIds(TradeQueryParams params){
        String[] exceptions = params.getExcludeExceptionStatus();
        String[] exceptIds = params.getExcludeExceptIds();
        List<Long> exceptIdList = new ArrayList<>();
        if (exceptions != null && exceptions.length != 0) {
            List<String> collect = Arrays.stream(exceptions).collect(Collectors.toList());
            List<ExceptEnum> exceptEnums = ExceptEnum.getEnumByOldEnglish(collect);
            List<Long> exceptEnumIds = exceptEnums.stream().map(ExceptEnum::getId).collect(Collectors.toList());
            exceptIdList.addAll(exceptEnumIds);
        }
        if (exceptIds != null && exceptIds.length != 0) {
            List<Long> collect = Arrays.stream(exceptIds).map(Long::valueOf).filter(exceptId -> exceptId > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                for(Long exceptId:collect){
                    ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
                    // 可能是三方仓的异常
                    if(exceptEnum!=null){
                        exceptIdList.add(exceptEnum.getId());
                    }else {
                        exceptIdList.add(exceptId);
                    }
                }
            }
        }
        return exceptIdList;
    }



    private void buildOtherQueryExceptIds(List<Long> otherContainsQueryExceptIds, List<Long> otherExcludeQueryExceptIds, Integer value,ExceptEnum exceptEnum) {
        if (value != null) {
            if (value == 1) {
                otherContainsQueryExceptIds.add(exceptEnum.getId());
            }
            if (value == 0) {
                otherExcludeQueryExceptIds.add(exceptEnum.getId());
            }
        }
    }





}


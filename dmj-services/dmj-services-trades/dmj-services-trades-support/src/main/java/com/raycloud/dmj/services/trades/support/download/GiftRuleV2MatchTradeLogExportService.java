package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.gift.GiftPromotionMatchTradeLogQuery;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.gift.param.GiftSearchParam;
import com.raycloud.dmj.services.gift.GiftPromotionMatchTradeLogService;
import com.raycloud.dmj.services.trades.gift.service.GiftRecordLogService;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28
 */
public class GiftRuleV2MatchTradeLogExportService implements IDownloadCenterCallback {

    private final Staff staff;
    private final GiftRecordLogService giftRecordLogService;
    private final GiftSearchParam query;

    public GiftRuleV2MatchTradeLogExportService(Staff staff, GiftSearchParam query, GiftRecordLogService giftRecordLogService) {
        this.staff = staff;
        this.query = query;
        this.giftRecordLogService = giftRecordLogService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {

        query.getPage().setPageNo(downloadParam.getPage().getPageNo());
        query.getPage().setPageSize(downloadParam.getPage().getPageSize());
        String[][] giftMatchTradeLogInfoExcelContent = giftRecordLogService.exportInfo(staff, query);
        DownloadResult result = new DownloadResult();
        result.setData(giftMatchTradeLogInfoExcelContent);
        result.setFlag(null != giftMatchTradeLogInfoExcelContent);
        return result;
    }
}

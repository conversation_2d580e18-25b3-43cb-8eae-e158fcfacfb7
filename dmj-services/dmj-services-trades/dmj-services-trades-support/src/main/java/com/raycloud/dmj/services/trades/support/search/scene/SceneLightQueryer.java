package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.Trade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-05
 */
@Component
public class SceneLightQueryer {

    @Autowired
    List<ISceneLightQueryStrategy> sceneLightQueryStrategys;


    public List<Trade> doQueryTrades(SceneLightQueryContext context, Long ... sids){
        if (sids == null || sids.length == 0) {
            return new ArrayList<>();
        }
        for (ISceneLightQueryStrategy strategy : sceneLightQueryStrategys) {
            if (Objects.equals(strategy.supportScene(),context.getSceneCode())) {
                return strategy.doQueryTrades(context,sids);
            }
        }
        throw new IllegalArgumentException("不支持的业务操作场景:" + context.getSceneCode().name());
    }

}

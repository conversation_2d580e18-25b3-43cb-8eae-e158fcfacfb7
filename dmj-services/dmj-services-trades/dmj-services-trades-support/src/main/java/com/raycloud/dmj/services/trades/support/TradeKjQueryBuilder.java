package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.Trades;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeKjUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.IPlatformToolAccess;
import com.raycloud.dmj.services.platform.trades.dto.PlatformTradeInfoDTO;
import com.raycloud.dmj.services.platform.trades.dto.PlatformTradeQueryDTO;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import com.raycloud.dmj.services.utils.LogKit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description <pre>
 *  跨境业务查询支持
 * </pre>
 * <AUTHOR>
 * @Date 2023-12-11
 */
@Component
public class TradeKjQueryBuilder {
    final Logger logger = Logger.getLogger(this.getClass());
    @Resource(name = "tradePSqlQueryBuilder")
    TradeQueryBuilder tradeSqlQueryBuilder;
    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    PlatformManagement platformManagement;

    /**
     * 是否打印箱唛  TEMU
     *
     * @param q
     * @param staff
     * @param params
     * @param tc
     */
    public void buildPrintBoxMarkQuery(Query q, Staff staff, TradeQueryParams params) {
        if (params.getPrintBoxMark() == null) {
            return;
        }
        String table = "abroad_trade_print_status_" +staff.getDbInfo().getAbroadTradePrintStatusDbNo();
        String sql = " SELECT 1 FROM "+table+" atps WHERE atps.company_id = t.company_id AND atps.sid = t.sid AND atps.box_print_status = 1";
        if (Objects.equals(params.getPrintBoxMark(),1)) {
            q.and().append(" EXISTS ( ").append(sql).append(")");
        }else if (Objects.equals(params.getPrintBoxMark(),0)) {
            q.and().append(" NOT EXISTS ( ").append(sql).append(")");
        }else {
            throw new IllegalArgumentException("非法的箱唛打印条件:"+params.getPrintBoxMark());
        }
    }

    /**
     * 商品编码打印状态 1.已打印 0.未打印
     *
     * @param q
     * @param staff
     * @param params
     */
    public void buildItemPrintStatusQuery(Query q, Staff staff, TradeQueryParams params) {
        if (params.getItemPrintStatus() == null) {
            return;
        }
        String table = "abroad_trade_print_status_" + staff.getDbInfo().getAbroadTradePrintStatusDbNo();
        String sql = " SELECT 1 FROM " + table + " atps WHERE atps.company_id = t.company_id AND atps.sid = t.sid AND atps.item_print_status = 1";
        q.and().conjunct(" NOT ", Objects.equals(params.getItemPrintStatus(), 0)).append(" EXISTS ( ").append(sql).append(")");
    }


    /**
     * 时效产品查询
     *
     * @param q
     * @param staff
     * @param params
     */
    public void buildAgedProductCodeQuery(Query q, Staff staff, TradeQueryParams params) {
        if (StringUtils.isEmpty(params.getAgedProductCode())) {
            return;
        }
        String table = "trade_ext_" + staff.getDbInfo().getTradeDbNo();
        String sql = " SELECT 1 FROM " + table + " ext WHERE ext.company_id = t.company_id AND ext.sid = t.sid AND json_extract(ext.extra_fields,'$.agedProductCode') ='" + params.getAgedProductCode() + "'";
        q.and().append(" EXISTS ( ").append(sql).append(")");
    }

    /**
     * 发货单相关条件
     *
     * @param q
     * @param staff
     * @param params
     */
    public void buildShippingOrderCondition(Query q, Staff staff, TradeQueryParams params) {
        QueryLogBuilder builder = new QueryLogBuilder(staff).append("跨境发货单条件处理");
        try {
            if (CollectionUtils.isEmpty(params.getShippingOrderStatus()) && CollectionUtils.isEmpty(params.getShippingOrderNumber())
                    && CollectionUtils.isEmpty(params.getDeliveryBatchCodes()) && StringUtils.isBlank(params.getFulfillPickupOrderCode())) {
                return;
            }
            if (q.isStopQuery()) {
                return;
            }

            Set<String> sources = params.getContext().getUserSourceMap() != null ? params.getContext().getUserSourceMap().keySet() : new HashSet<>();
            if (params.getSource() != null) {
                sources = new HashSet<>();
                sources.addAll(Strings.getAsStringList(params.getSource(), ",", true));
            }
            if (isWaitCreateQuery(params)) {
                //既然发货单都不存在 那肯定没有满足发货单号和发货批次号条件的数据
                if (CollectionUtils.isNotEmpty(params.getShippingOrderNumber())
                        ||  CollectionUtils.isNotEmpty(params.getDeliveryBatchCodes())) {
                    q.setStopQuery(true);
                    q.setStopReason("发货单未创建下不会有满足其他发货单条件的数据");
                    return;
                }

                Set<Long> hasDeliverySids = new HashSet<>();

                Map<String, Set<Long>> sidBySource = null;
                Map<String, List<Trade>> tradeMap = null;
                int stopNum = 0;
                for (String source : sources) {
                    if (!platformManagement.isContainAccess(source, IPlatformToolAccess.class)) {
                        continue;
                    }
                    IPlatformToolAccess toolAccess = platformManagement.getAccess(source, IPlatformToolAccess.class);
                    PlatformTradeQueryDTO platformTradeQueryDTO = new PlatformTradeQueryDTO();

                    List<String> sids = null;
                    if (ArrayUtils.isEmpty(params.getSid())) {
                        if (sidBySource == null) {
                            sidBySource = querySids(staff,params);
                        }
                        Set<Long> set = sidBySource.get(source);
                        if (CollectionUtils.isEmpty(set)) {
                            stopNum++;
                            continue;
                        }
                        sids = set.stream().map(String::valueOf).collect(Collectors.toList());
                    }else {
                        sids =  Arrays.stream(params.getSid()).map(String::valueOf).collect(Collectors.toList());
                    }
                    if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_TEMU, params.getSource())) {
                        platformTradeQueryDTO.setShippingOrderStatus(Arrays.asList("1", "2"));
                        if (CollectionUtils.isNotEmpty(sids)) {
                            List<Trade> trades = tradeSearchService.querySomeFieldsBySids(staff,"sid,tid", sids.stream().map(Long::parseLong).collect(Collectors.toList()),null);
                            if (CollectionUtils.isNotEmpty(trades)) {
                                platformTradeQueryDTO.setTidList(TradeUtils.toTidList(trades));
                                tradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getTid));
                                sids.clear();
                            }
                        }
                    }
                    platformTradeQueryDTO.setSidList(sids.stream().map(String::valueOf).collect(Collectors.toList()));
                    PlatformResponse<List<PlatformTradeInfoDTO>> platformResponse =
                            LogKit.took(() -> toolAccess.queryPlatformTradeList(staff, platformTradeQueryDTO), staff, logger);
                    if (platformResponse != null && CollectionUtils.isNotEmpty(platformResponse.getData())) {
                        List<Long> deliverySids = new ArrayList<>();
                        List<String> deliveryTids = new ArrayList<>();
                        platformResponse.getData().forEach(data -> {
                            if (data.getSid() != null) {
                                deliverySids.add(data.getSid());
                            }
                            if (data.getTid() != null) {
                                deliveryTids.add(data.getTid());
                            }
                        });
                        if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_TEMU, params.getSource())) {
                            if (CollectionUtils.isNotEmpty(deliverySids)) {
                                hasDeliverySids.addAll(deliverySids);
                            }
                            if (CollectionUtils.isNotEmpty(deliveryTids) && tradeMap != null) {
                                for (String deliveryTid : deliveryTids) {
                                    List<Trade> trades = tradeMap.get(deliveryTid);
                                    if (trades != null) {
                                        hasDeliverySids.addAll(TradeUtils.toSidList(trades));
                                    }
                                }
                            }
                        } else {
                            hasDeliverySids.addAll(deliverySids);
                        }
                    }
                }
                if (CollectionUtils.isEmpty(hasDeliverySids)) {
                    builder.append("不存在已创建发货单的sid");
                    return;
                }
                if (stopNum == sources.size()) {
                    q.setStopQuery(true);
                    return;
                }
                builder.appendArray("排除已存在发货单的sid",hasDeliverySids);
                if (params.getExcludeSids() == null) {
                    params.setExcludeSids(hasDeliverySids.toArray(new Long[0]));
                }else {
                    List<Long> list = Arrays.asList(params.getExcludeSids());
                    list.addAll(hasDeliverySids);
                    params.setExcludeSids(list.toArray(new Long[0]));
                }

            }else{
                Set<Long> includeSids = new HashSet<>();
                for (String source : sources) {
                    if (!platformManagement.isContainAccess(source, IPlatformToolAccess.class)) {
                        continue;
                    }
                    IPlatformToolAccess toolAccess = platformManagement.getAccess(source, IPlatformToolAccess.class);
                    PlatformTradeQueryDTO platformTradeQueryDTO = new PlatformTradeQueryDTO();
                    platformTradeQueryDTO.setSidList(params.getSid() == null? null : Arrays.stream(params.getSid()).map(String::valueOf).collect(Collectors.toList()));
                    platformTradeQueryDTO.setShippingOrderStatus(params.getShippingOrderStatus());
                    platformTradeQueryDTO.setDeliveryCodeList(params.getShippingOrderNumber());
                    platformTradeQueryDTO.setDeliveryBatchCodeList(params.getDeliveryBatchCodes());
                    platformTradeQueryDTO.setFulfillPickupOrderCode(params.getFulfillPickupOrderCode());
                    PlatformResponse<List<PlatformTradeInfoDTO>> platformResponse =
                            LogKit.took(() -> toolAccess.queryPlatformTradeList(staff, platformTradeQueryDTO), staff, logger);
                    if (platformResponse != null && CollectionUtils.isNotEmpty(platformResponse.getData())) {
                        includeSids.addAll(platformResponse.getData().stream().map(PlatformTradeInfoDTO::getSid).collect(Collectors.toList()));
                    }
                }
                if (CollectionUtils.isEmpty(includeSids)) {
                    q.setStopQuery(true);
                    q.setStopReason("发货单相关条件无数据");
                    builder.append("发货单相关条件无数据");
                }else {
                    builder.appendArray("发货单对应sid",includeSids);
                    List<Long> merged =  mergeCondition(params.getSid(),includeSids.toArray(new Long[0]),params.getExcludeSids());
                    if (CollectionUtils.isEmpty(merged)) {
                        builder.append("发货单对应sid 与其他sid条件无交集");
                        q.setStopQuery(true);
                        q.setStopReason("发货单条件与其他sid条件无交集");
                    }else {
                        params.setSid(merged.toArray(new Long[0]));
                    }
                }
            }

            builder.printDebug(logger);
        }catch (Throwable e){
            builder.appendError("跨境发货单条件处理失败",e).printError(logger,e);
            throw new IllegalArgumentException("跨境发货单条件处理失败");
        }
    }

    /**
     * @param maps    集合
     * @param sids    已存在发货单
     * @param include 是否包含 true:包含 false:不包含
     */
    protected List<Long> getSids(Map<String, Set<Long>> maps, Set<Long> sids, boolean include) {
        if (MapUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        Set<Long> total = new HashSet<>();
        for (Set<Long> value : maps.values()) {
            total.addAll(value);
        }
        boolean suc = include ? total.retainAll(sids) : total.removeIf(sids::contains);
        return new ArrayList<>(total);
    }

    protected Map<String, Set<Long>> querySids(Staff staff, TradeQueryParams params) {
        Map<String, Set<Long>> sids = new HashMap<>();
        while (true) {
            TradeQueryParams queryParams = new TradeQueryParams()
                    .setFields("t.sid,t.source,t.sub_source")
                    .setNeedFill(false)
                    .setQueryOrder(false)
                    .setIgnoreFilter(1)
                    .setUndoMergeFill(false)
                    .setCheckItem(false)
                    .setUserIds(params.getUserIds())
                    .setSid(params.getSid())
                    .setTid(params.getTid())
                    .setTimeType(params.getTimeType())
                    .setStartTime(params.getStartTime())
                    .setEndTime(params.getEndTime())
                    .setPlatformStatus(params.getPlatformStatus())
                    .setTagIds(params.getTagIds())
                    .setWarehouseIds(params.getWarehouseIds())
                    .setQueryFlag(1)
                    .setSysStatus(getSysStatus(params))
                    .setSource(params.getSource())
                    .setPage(new Page(1, 2000));
            Trades tmp = tradeSearchService.search(staff, queryParams);
            if (CollectionUtils.isNotEmpty(tmp.getList())) {
                Page page = queryParams.getPage();
                page.setPageNo(page.getPageNo() + 1);
                queryParams.setPage(page);
                for (Trade trade : tmp.getList()) {
                    sids.computeIfAbsent(TradeKjUtils.getSource(trade), (k) -> new HashSet<>()).add(trade.getSid());
                }
                if (tmp.getList().size() < page.getPageSize()) {
                    break;
                }
            } else {
                break;
            }
        }
        return sids;
    }

    String[] getSysStatus(TradeQueryParams params) {
        if (isWaitCreateQuery(params)) {
            if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG, params.getSource())
                    || Objects.equals(CommonConstants.PLAT_FORM_TYPE_TIKTOK_QTG, params.getSource())) {
                return new String[]{Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED};
            } else if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_TEMU, params.getSource())) {
                return new String[]{Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED};
            }
        }
        return new String[]{Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED};
    }

    protected boolean isWaitCreateQuery(TradeQueryParams params) {
        List<String> shippingOrderStatus = params.getShippingOrderStatus();
        if (shippingOrderStatus.contains("WAIT_CREATE")) {
            if (shippingOrderStatus.size() > 1) {
                throw new IllegalArgumentException("待创建发货单状态不能与其他状态一起查询");
            }
            return true;
        }
        return false;
    }

    protected <T> List<T> mergeCondition(T[] curr, T[] includeds, T[] excludeds) {
        return mergeCondition(curr == null ? null : Arrays.asList(curr),
                includeds == null ? null : Arrays.asList(includeds),
                excludeds == null ? null : Arrays.asList(excludeds));
    }

    protected <T> List<T> mergeCondition(List<T> curr, List<T> includeds, List<T> excludeds) {
        if (CollectionUtils.isEmpty(includeds)) {
            return null;
        }

        if (CollectionUtils.isEmpty(curr)) {
            curr =  includeds;
        }else{
            curr.retainAll(includeds);
        }
        if (CollectionUtils.isEmpty(curr)) {
            return null;
        }

        if (excludeds != null) {
            curr.removeAll(excludeds);
        }
        return curr;
    }
}

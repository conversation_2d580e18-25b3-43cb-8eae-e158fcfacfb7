package com.raycloud.dmj.services.trades;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.party3.TradeParty3BusinessTypeEnum;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeIOUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by yangheng on 16/11/25.
 * 处理售后退款的业务实现
 */
@Service
public class RefundTradeService implements IRefundTradeService {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    private ILockService lockService;

    @Resource
    private TradeParty3Business tradeParty3Business;

    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public void handle(final Staff staff, final Long sid, final String tid, final Long refundId, final String status) {
        Trade tradeOrigin = queryTrade(staff, sid, tid, refundId);
        if (tradeOrigin == null) {
            logger.warn(LogHelper.buildLogHead(staff).append(String.format("处理workOrder.operation事件时查询不到订单[sid:%s,,tid:%s,refundId:%s,status:%s]", sid, tid, refundId, status)));
            return;
        }

        List<Trade> reCreates = new ArrayList<>();

        lockService.lock(TradeLockBusiness.trade2ERPLock(staff, tradeOrigin), () -> {
            Trade trade = queryTrade(staff, sid, tid, refundId);
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            List<Order> refundOrders = new ArrayList<>();
            for (Order order : orders) {
                if (StringUtils.isEquals(order.getRefundId(), refundId.toString())) {
                    refundOrders.add(order);
                }
            }

            if (CollectionUtils.isEmpty(refundOrders)) {
                logger.warn(LogHelper.buildLogHead(staff).append(String.format("处理workOrder.operation事件时查询不到订单[sid:%s,,tid:%s,refundId:%s,status:%s]", sid, tid, refundId, status)));
                return null;
            }

            boolean beforeRefunding = false;
            for (Order order : orders) {
                if (RefundUtils.isRefundOrder(order)) {
                    beforeRefunding = true;
                }
            }

            if ("refuse".equals(status)) {//拒绝退款
                handleRefuse(staff, trade, refundOrders);
            } else if ("agree".equals(status)) {//同意退款
                handleAgree(staff, trade, refundOrders);
            }

            boolean afterRefunding = false;
            for (Order order : orders) {
                if (RefundUtils.isRefundOrder(order)) {
                    afterRefunding = true;
                }
            }

            if (!isWarehouseSys(staff, trade.getWarehouseId()) && trade.getSysStatus().equalsIgnoreCase(Trade.SYS_STATUS_FINISHED_AUDIT) && !Integer.valueOf(1).equals(trade.getIsCancel()) && !TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.HALT)) {
                if (beforeRefunding && !afterRefunding) {
                    reCreates.add(trade);
                }
            }

            //如果处理的合单后隐藏的订单，需要重新计算主单的异常状态
            if (TradeUtils.isMerge(trade) && trade.getEnableStatus() != 1) {
                handleMerge(staff, trade);
            }
            //后续事件处理
            fireEvent(staff, trade, status);
            //记录日志
            logTrade(staff, trade, refundOrders, refundId, status);
            return null;
        });
        tradeParty3Business.sendEvent2Warehouse3(staff, null, reCreates,null, TradeParty3BusinessTypeEnum.AFTER_REFUND);
    }

    //修改合单后主单的退款状态和异常状态
    private void handleMerge(Staff staff, Trade trade) {
        List<Trade> mergeTrades = tradeSearchService.queryBySids(staff, true, trade.getSid());
        for (Trade mergeTrade : mergeTrades) {
            if (mergeTrade.getEnableStatus() != 1) {
                continue;
            }
            TradeIOUtils.fillTradeExcep(staff, mergeTrade);
            tradeUpdateService.updateTrades(staff, getUpdateTrade(staff,mergeTrade));
            return;
        }
    }

    private Boolean isWarehouseSys(Staff staff, Long warehouseId) {
        if (BooleanUtils.isTrue(staff.isDefaultStaff())) {
            Warehouse warehouse = warehouseService.queryById(warehouseId);
            return warehouse != null && Warehouse.TYPE_OWN == warehouse.getType();
        }
        return Warehouse.isWarehouseSys(staff, warehouseId);
    }

    /**
     * 查询订单，如果根据sid查询不到，那么交易系统中可能是合单了或者拆单了，如果是合单，那么需要根据tid查找，如果是拆单，则需要根据tid然后再根据oid过滤,另外，如果订单的子订单的退款状态不符合设置继续的发货状态时，不会返回订单
     *
     * @param staff
     * @param sid
     * @param tid
     * @param refundId
     * @return
     */
    Trade queryTrade(Staff staff, Long sid, String tid, Long refundId) {
        //先按sid查询
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sid);
        if (trades.size() > 0) {
            for (Trade trade : trades) {
                if (tradeCanRefund(trade, refundId)) {
                    return trade;
                }
            }
        }

        if (StringUtils.isEmpty(tid)) {
            return null;
        }

        // 根据tid进行搜索,拆单的订单可能找到多个订单，需要找到包含退款的那个订单
        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, true, tid);
        if (CollectionUtils.isEmpty(tbTrades)) {
            return null;
        }

        for (TbTrade trade : tbTrades) {
            if (tradeCanRefund(trade, refundId)) {
                return trade;
            }
        }
        return null;
    }


    /**
     * 判断trade是否包含refundId退款的子订单
     *
     * @param trade
     * @param refundId
     * @return
     */
    private boolean tradeCanRefund(Trade trade, Long refundId) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (StringUtils.isEquals(order.getRefundId(), refundId.toString()) && RefundUtils.canUpdateContinueConsign(order.getRefundStatus())) {
                return true;
            }
        }
        return false;
    }

    /**===================================================拒绝退款start===============================================**/
    /**
     * 拒绝退款
     *
     * @param staff
     * @param trade
     * @param refundOrders
     */
    void handleRefuse(Staff staff, Trade trade, List<Order> refundOrders) {
        List<Order> updateOrders = getRefuseUpdateOrder(refundOrders);
        TradeIOUtils.fillTradeExcep(staff, trade);
        tradeUpdateService.updateTrades(staff, getUpdateTrade(staff,trade), updateOrders);
    }

    /**
     * 拒绝退款,修改子订单
     *
     * @param orders
     */
    private List<Order> getRefuseUpdateOrder(List<Order> orders) {
        List<Order> updateOrders = new ArrayList<Order>(orders.size());
        for (Order order : orders) {
            order.setRefundStatus(Order.REFUND_SELLER_CONTINUE_CONSIGN);
            Order updateOrder = new TbOrder();
            updateOrder.setId(order.getId());
            updateOrder.setRefundStatus(order.getRefundStatus());
            updateOrders.add(updateOrder);
        }
        return updateOrders;
    }
    /**===================================================拒绝退款end=================================================**/


    /**===================================================同意退款start===============================================**/
    /**
     * 同意退款
     *
     * @param staff
     * @param trade
     * @param refundOrders
     */
    void handleAgree(Staff staff, Trade trade, List<Order> refundOrders) {
        //获取需要修改的子订单
        List<Order> updateOrders = getAgreeUpdateOrder(trade, refundOrders);
        //获取需要修改的订单、修改数据库
        TradeIOUtils.fillTradeExcep(staff, trade);
//        PaymentUtils.calculateUpdateTrade(trade);
        tradeUpdateService.updateTrades(staff, getUpdateTrade(staff,trade), updateOrders);
        //归还库存
        try {
            orderStockService.resumeOrderStockLocal(staff, refundOrders, null);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("刷单订单还库存出错"), e);
        }
    }

    /**
     * 同意退款,修改子订单
     *
     * @param trade
     * @param orders
     */
    private List<Order> getAgreeUpdateOrder(Trade trade, List<Order> orders) {
        List<Order> updateOrders = new ArrayList<Order>(orders.size());
        for (Order order : orders) {
            Order updateOrder = new TbOrder();
            updateOrder.setId(order.getId());
            updateOrder.setRefundStatus(Order.REFUND_SUCCESS);//退款成功
            updateOrder.setSysStatus(Trade.SYS_STATUS_CLOSED);//系统状态关闭
            order.setSysStatus(updateOrder.getSysStatus());
            order.setRefundStatus(updateOrder.getRefundStatus());
            order.setWarehouseId(trade.getWarehouseId());
            updateOrders.add(updateOrder);
        }
        return updateOrders;
    }
    /**===================================================同意退款end=================================================**/

    /**
     * 修改订单
     *
     * @param trade
     * @return
     */
    private List<Trade> getUpdateTrade(Staff staff,Trade trade) {
        List<Trade> updateTrades = new ArrayList<Trade>();
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade,false);
        updateTrade.setSid(trade.getSid());
        updateTrade.setIsExcep(trade.getIsExcep());
      //  updateTrade.setIsRefund(trade.getIsRefund());
        TradeExceptUtils.syncTradeExcept(staff,updateTrade,trade,ExceptEnum.REFUNDING);
        updateTrade.setSysStatus(TradeStatusUtils.getTradeSysStatus(trade));//更新系统状态
        updateTrades.add(updateTrade);
        return updateTrades;
    }

    private void fireEvent(Staff staff, Trade trade, String status) {
        List<Trade> trades = new ArrayList<Trade>();
        trades.add(trade);
        String eventName = "";
        if ("refuse".equals(status)) {//拒绝退款
            eventName = "trade.continue.send.goods";
        } else if ("agree".equals(status)) {//同意退款
            eventName = "trade.close.send.goods";
        }

        if (StringUtils.isNotEmpty(eventName)) {
            eventCenter.fireEvent(this, new EventInfo(eventName).setArgs(new Object[]{staff}), trades);
        }
    }

    private void logTrade(Staff staff, Trade trade, List<Order> refundOrders, Long refundId, String status) {
        if (logger.isDebugEnabled()) {
            List<String> orderIds = new ArrayList<String>(refundOrders.size() + 1);
            for (Order order : refundOrders) {
                orderIds.add(String.valueOf(order.getId()));
            }
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("成功处理workOrder.operation事件,[sid:%s,tid:%s,refundId:%s,status:%s,orderId:%s]", trade.getSid(), trade.getTid(), refundId, status, StringUtils.join(orderIds, ","))));
        }
    }
}

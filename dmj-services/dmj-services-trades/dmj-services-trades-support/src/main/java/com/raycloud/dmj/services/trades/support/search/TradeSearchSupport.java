package com.raycloud.dmj.services.trades.support.search;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.TradeLocalConfigurableConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.account.ThriftStaff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.audit.AuditDiamondUtils;
import com.raycloud.dmj.domain.trades.CursorListBase;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryContext;
import com.raycloud.dmj.domain.trades.search.BaseTrade;
import com.raycloud.dmj.domain.trades.search.TimeTypeEnum;
import com.raycloud.dmj.domain.trades.search.TradeCursorQueryRequest;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.ec.support.StaffTransformService;
import com.raycloud.dmj.services.trades.support.search.convert.AbsConditionConverter;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertBase;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;



/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-07
 */
@Component
public class TradeSearchSupport {

    private final Logger logger = Logger.getLogger(this.getClass());


    @Resource
    StaffTransformService staffTransformService;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;


    @Resource
    IStaffService staffService;




    public  Staff getFullStaff(Long companyId,Long staffId){
        ThriftStaff ts = new ThriftStaff();
        ts.setId(staffId);
        ts.setCompanyId(companyId);
        Integer extraFields = 1;
        extraFields |= ThriftStaff.getCompanyMark();
        extraFields |= ThriftStaff.getUsersMark();
        ts.setExtraFields(extraFields);
        Staff filled = staffTransformService.transformThriftStaff(ts);
        Assert.notNull(filled,"根据staffId:" + staffId + "找不到staff");
        return filled;
    }

    public  Staff getFullStaff(Staff staff){
        ThriftStaff ts = staffTransformService.transformStaff(staff);
        Integer extraFields = ts.getExtraFields();
        if (extraFields == null) {
            extraFields = 1;
        }
        boolean needFill = false;
        if (staff.getCompany() == null) {
            extraFields |= ThriftStaff.getCompanyMark();
            needFill = true;
        }
        if (CollectionUtils.isEmpty(staff.getUsers())) {
            extraFields |= ThriftStaff.getUsersMark();
            needFill = true;
        }
        Staff filled = staff;
        if (needFill) {
            ts.setExtraFields(extraFields);
            filled = staffTransformService.transformThriftStaff(ts);
            filled.setClueId(staff.getClueId());
            new QueryLogBuilder(filled).append("传入的staff对象不完整,进行兜底补全").printWarn(logger,new Exception());
        }
        return filled;
    }

    public Map<Long, User> getUserMap(Staff staff){
        Map<Long, User> userMap = staff.getUserIdMap();
        if (userMap == null) {
            new QueryLogBuilder(staff).append("传入的staff对象没有userMap,进行兜底补全").printWarn(logger,new Exception());
            userMap = new HashMap<>();

            List<User> users = staff.getUsers();
            if (CollectionUtils.isEmpty(users)) {
                Staff staff1 = staffService.queryFullById(staff.getId());
                users = staff1.getUsers();
                staff.setCompany(staff1.getCompany());
            }

            if (CollectionUtils.isNotEmpty(users)) {
                staff.setUsers(users);
                for (User user : users) {
                    user.setStaff(staff);
                    userMap.put(user.getId(), user);
                }
            }else {
                staff.setUsers(new ArrayList<>());
            }
            staff.setUserIdMap(userMap);

        }
        return userMap;
    }


    /**
     * 部分操作会同步更新 upd_time 字段 因此可以将操作时间转换为upd_time条件以走对应索引
     */
    public void addUpdTimeRef(Staff staff, Query q, String timeField,Date  startTime,Date  endTime,String[] sysStatus) {
        if (!TradeSearchSupport.HAS_INDEX_TIME_FIELDS.contains(timeField) &&TradeSearchSupport.REL_UPD_TIME.contains(timeField) && startTime != null) {
            //订单审核会强制不再更新updTime 这里查询短期内订单的不加upd_time条件
            if (java.util.Objects.equals(timeField,"audit_time")) {
                Long daysBetween = getDaysBetween(startTime,new Date());
                if (daysBetween < 7) {
                    return;
                }
            }
            //如果按发货时间查询 那么只有明确是查询发货后状态的订单时才能加updTime的条件
            if (com.google.common.base.Objects.equal(timeField,"consign_time")) {
                if (sysStatus == null || sysStatus.length == 0) {
                    return;
                }
                for (String status : sysStatus) {
                    if (!(Trade.SYS_STATUS_FINISHED.equals(status) ||  Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(status))) {
                        return;
                    }
                }
            }
            if (com.google.common.base.Objects.equal(timeField,"express_print_time")) {
                if (sysStatus == null || sysStatus.length == 0) {
                    return;
                }
                for (String status : sysStatus) {
                    if (!TradeStatusUtils.isAfterPrint(status)) {
                        return;
                    }
                }
            }
            ConvertBase.andDateRangeQuery(q, "t.`upd_time`", startTime, null,1);
        }
    }

    public void forceIndexKey(Staff staff, TradeQueryContext context, Query q, String timeField,Date  startTime,Date  endTime,String sort,String[] sysStatus) {

        //前面已经指定过强制索引的 这里不再指定
        if (StringUtils.isNotBlank(q.getTradeIndex())) {
            return;
        }

        if (startTime == null && endTime == null ) {
            return;
        }
        if (StringUtils.isBlank(timeField)) {
            return;
        }
        if (Objects.equals(timeField,sort) && HAS_INDEX_TIME_FIELDS.contains(timeField)) {
            return;
        }
        if (!tradeLocalConfig.isConfigOn(TradeLocalConfigurableConstants.TRADE_SEARCH_LIST_FORCE_INDEX,staff.getCompanyId())) {
            return;
        }

        //9库小表很多索引都没有对齐 跳过
        if (Objects.equals("trade_not_consign",q.getTradeTable())  && staff.getDbNo() == 9) {
            return;
        }

        Long daysBetween = getDaysBetween(startTime,endTime);


        //这里的基本逻辑是 一个订单 支付时间是一定会早于打印时间的,如果按支付时间查询 按打印时间排序(这种情况mysql会默认走排序字段的索引),支付时间上的查询条件可以转化为打印时间的条件(两个条件and),从而更好的走排序索引
        if (HAS_INDEX_TIME_FIELDS.contains(timeField) && HAS_INDEX_TIME_FIELDS.contains(sort) ) {

            Date sortBeginTime = null,sortEndTime = null;
            if (Objects.equals(timeField,"created")) {
                if (Objects.equals(sort,"pay_time")) {
                    //if (startTime != null) {
                    //    sortBeginTime = DateUtil.addDate( startTime,-15);
                    //}
                }else  {
                    //"express_print_time","consign_time"
                    if (startTime != null) {
                        sortBeginTime = startTime;
                    }
                }
            }
            if (Objects.equals(timeField,"pay_time")) {
                if (Objects.equals(sort,"created")) {
                    //if (startTime != null) {
                    //    sortBeginTime = DateUtil.addDate( startTime,-15);
                    //}
                }else {
                    //"express_print_time","consign_time"
                    if (startTime != null) {
                        sortBeginTime = startTime;
                    }
                }
            }
            if (Objects.equals(timeField,"express_print_time")) {
                if (Objects.equals(sort,"consign_time")) {
                    if (startTime != null) {
                        sortBeginTime = DateUtil.addDate( startTime,-3);
                    }
                }else  {
                    // "created","pay_time"
                    if (endTime != null) {
                        sortEndTime = endTime;
                    }
                }
            }
            if (Objects.equals(timeField,"consign_time")) {
                if (Objects.equals(sort,"express_print_time")) {
                    //if (startTime != null) {
                    //    sortBeginTime = DateUtil.addDate( startTime,-3);
                    //}
                    if (endTime != null) {
                        sortEndTime = endTime;
                    }
                }else  {
                    // "created","pay_time"
                    if (endTime != null) {
                        sortEndTime = endTime;
                    }
                }
            }
            if (sortBeginTime != null || sortEndTime != null) {
                ConvertBase.andDateRangeQuery(q, "t.`"+sort+"`", sortBeginTime, sortEndTime);
                new QueryLogBuilder(staff).append("timeField",timeField).append("sort",sort)
                        .append("添加排序字段时间条件",sortBeginTime).printDebug(logger);
                context.addOptimizations("addTime");
            }else if (daysBetween <= 3) {
                //如果指定时间范围很小 那么强制走时间范围对应的索引
                String index = null;
                if(StringUtils.equals(timeField,"express_print_time")){
                    index = "idx_company_express_user";
                }else if(StringUtils.equals(timeField,"pay_time") && org.apache.commons.lang3.ArrayUtils.isEmpty(sysStatus)){
                    index = "idx_company_paytime_user";
                }else if(StringUtils.equals(timeField,"created")){
                    index = "idx_company_created_user";
                }
                if (StringUtils.isNotBlank(index)) {
                    q.setTradeIndex(" force index("+index+") ");
                    new QueryLogBuilder(staff).append("timeField",timeField).append("sort",sort).append("timeRange",daysBetween)
                            .append("强制索引",index).printDebug(logger);
                    q.setTradeIndex(" force index("+index+") ");
                    context.addOptimizations("forceIndex");
                }
            }
            return;
        }

        //本身也是有索引 但是区分度不是很好的排序字段
        if (HAS_INDEX_TIME_FIELDS.contains(timeField) && HAS_INDEX_SLOW_FIELDS.contains(sort) ) {
            if (daysBetween <= 30) {
                //如果指定时间范围很小 那么强制走时间范围对应的索引
                String index = null;
                if(StringUtils.equals(timeField,"express_print_time")){
                    index = "idx_company_express_user";
                }else if(StringUtils.equals(timeField,"pay_time") && org.apache.commons.lang3.ArrayUtils.isEmpty(sysStatus)){
                    index = "idx_company_paytime_user";
                }else if(StringUtils.equals(timeField,"created")){
                    index = "idx_company_created_user";
                }
                if (StringUtils.isNotBlank(index)) {
                    new QueryLogBuilder(staff).append("timeType",timeField).append("sort",sort).append("timeRange",daysBetween)
                            .append("强制索引",index).printDebug(logger);
                    q.setTradeIndex(" force index("+index+") ");
                    context.addOptimizations("forceIndex");
                }
                return;
            }
        }

        //部分非索引的时间条件 会同步新增 upd_time 的条件
        if (startTime != null && REL_UPD_TIME.contains(timeField)) {
            if (!HAS_INDEX_TIME_FIELDS.contains(sort) || daysBetween <= 1) {
                String index = "idx_company_updated";
                //订单审核会强制不再更新updTime 这里查询短期内订单的不加upd_time条件
                if (java.util.Objects.equals(timeField, "audit_time")) {
                    Long diff = getDaysBetween(startTime, new Date());
                    if (diff < 7 && !AuditDiamondUtils.openSaveUpdateTime(staff)) {
                        return;
                    }
                }

                //如果按发货时间排序 那么只有明确是查询发货后状态的订单时才能加发货时间的条件
                if (com.google.common.base.Objects.equal(sort,"consign_time")) {
                    if (sysStatus == null || sysStatus.length == 0) {
                        return;
                    }
                    for (String status : sysStatus) {
                        if (!(Trade.SYS_STATUS_FINISHED.equals(status) ||  Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(status))) {
                            return;
                        }
                    }
                }
                if (com.google.common.base.Objects.equal(sort,"express_print_time")) {
                    if (sysStatus == null || sysStatus.length == 0) {
                        return;
                    }
                    for (String status : sysStatus) {
                        if (!TradeStatusUtils.isAfterPrint(status)) {
                            return;
                        }
                    }
                }

                new QueryLogBuilder(staff).append("timeType",timeField).append("sort",sort).append("timeRange",daysBetween)
                        .append("强制更新时间索引", index).printDebug(logger);
                q.setTradeIndex(" force index(" + index + ") ");
                context.addOptimizations("forceUdpIndex");
                return;
            }
        }

    }


    public static Set<String> HAS_INDEX_TIME_FIELDS = null;

    public static  Map<String,String> TIME_FIELDS_INDEX_MAP =  null;

    /**
     * 做相关操作时 会同步更新upd_time的时间字段
     */
    public  static List<String> REL_UPD_TIME =  null;
    public static  List<String> HAS_INDEX_SLOW_FIELDS  =  null;

    static {
        TIME_FIELDS_INDEX_MAP = new HashMap<>();
        TIME_FIELDS_INDEX_MAP.put("upd_time","idx_company_updated");
        TIME_FIELDS_INDEX_MAP.put("consign_time","idx_consintime");
        TIME_FIELDS_INDEX_MAP.put("created","idx_company_created_user");
        TIME_FIELDS_INDEX_MAP.put("express_print_time","idx_company_express_user");
        TIME_FIELDS_INDEX_MAP.put("pay_time","idx_company_paytime_user");

        HAS_INDEX_TIME_FIELDS =  TIME_FIELDS_INDEX_MAP.keySet();

        REL_UPD_TIME = Arrays.asList("audit_time,end_time,modified".split(","));

        HAS_INDEX_SLOW_FIELDS = Arrays.asList("buyer_nick");
    }



    private Long getDaysBetween(Date begin,Date end){
        if (begin ==  null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.MONTH, -3);
            begin = calendar.getTime();
        }
        if (end ==  null) {
            end = new Date();
        }
        return (end.getTime() - begin.getTime()) / (1000 * 60 * 60 * 24);
    }

    public  void fliterBaseMergeByCursor(TradeQueryRequest request, CursorListBase<BaseTrade> result){
        TradeConvert<BaseTrade> convert = new TradeConvert<BaseTrade>() {
            @Override
            Date getDateByField(TimeTypeEnum type) {
                Date time = null;
                switch (type) {
                    case CREATED:time = target.getCreated();break;
                    case UPD_TIME:time = target.getUpdTime();break;
                    case PAY_TIME:time = target.getPayTime();break;
                    case CONSIGN_TIME:time = target.getConsignTime();break;
                    case EXPRESS_PRINT_TIME:time = target.getExpressPrintTime();break;
                }
                if (time == null) {
                    time = TradeTimeUtils.INIT_DATE;
                }
                return time;
            }

            @Override
            Long getSid() {
                return target.getSid();
            }

            @Override
            Long getMergeSid() {
                return target.getMergeSid();
            }
        };
        fliterMergeByCursor(request,result,convert);
    }

    public void fliterMergeByCursor(TradeQueryRequest request, CursorListBase<TbTrade> result){
        TradeConvert<TbTrade> convert = new TradeConvert<TbTrade>() {
            @Override
            Date getDateByField(TimeTypeEnum type) {
                return TradeSearchSupport.getDateByField(target,type);
            }

            @Override
            Long getSid() {
                return target.getSid();
            }

            @Override
            Long getMergeSid() {
                return target.getMergeSid();
            }
        };
        fliterMergeByCursor(request,result,convert);
    }

    private <T> void fliterMergeByCursor(TradeQueryRequest request, CursorListBase<T> result, TradeConvert<T> convert){
        if (result == null) {
            return;
        }
        if (request instanceof TradeCursorQueryRequest) {
            List<T> list = result.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                TradeCursorQueryRequest cursorRequest = (TradeCursorQueryRequest) request;
                String order = cursorRequest.getOrder();
                if (StringUtils.isBlank(order)) {
                    order = "desc";
                }
                boolean isAsc = "asc".equalsIgnoreCase(order.trim());

                Long cursorSid = null;
                Date cursorDate = null;
                String cursor = cursorRequest.getCursor();
                if (StringUtils.isNotBlank(cursor)) {
                    try {
                        String[] split = cursor.split("_");
                        cursorDate = new Date(Long.parseLong(split[0]));
                        cursorSid = Long.parseLong(split[1]);
                    }catch (Exception e){
                        throw new IllegalArgumentException("游标非法:" + cursor);
                    }
                }

                //https://gykj.yuque.com/entavv/eltb2u/gyybb0evbzebzycy
                Iterator<T> iterator = list.iterator();
                while (iterator.hasNext()) {
                    T tbTrade = iterator.next();
                    convert.setTarget(tbTrade);
                    if (convert.getMergeSid() != null && convert.getMergeSid() > 0) {
                        Date curTime = convert.getDateByField(cursorRequest.getTimeType());
                        //如果合单本身是在查询条件范围之外,说明是因为查询到子单关联出来的,不用处理
                        if(request.getStartTime() != null && request.getStartTime().before(curTime)
                                || request.getEndTime() != null && request.getEndTime().after(curTime)){
                            continue;
                        }
                        if (cursorDate == null) {
                            continue;
                        }
                        //如果合单的时间在当前游标时间之前(asc)或之后(desc),那么前面的分页已经返回过了 这里不重复返回
                        if ((isAsc && cursorDate.after(curTime)) ||(!isAsc && cursorDate.before(curTime)) ) {
                            iterator.remove();
                        }
                        if(cursorDate.equals(curTime)){
                            //时间等于当前游标时间 如果当前mergeSid在游标sid 之前(asc)或之后(desc),那么前面的分页已经返回过了 这里不重复返回
                            if ((isAsc && convert.getMergeSid() < cursorSid) ||(!isAsc && convert.getMergeSid() > cursorSid) || Objects.equals(convert.getMergeSid(), cursorSid)) {
                                iterator.remove();
                            }
                        }

                    }
                }
            };
        }
    }


    public abstract class TradeConvert<T>{

        protected T target;

        public void setTarget(T target) {
            this.target = target;
        }


        abstract Date getDateByField(TimeTypeEnum type);

        abstract Long getSid();

        abstract Long  getMergeSid();
    }


    public static Date getDateByField(Trade target,TimeTypeEnum type){
        Date time = null;
        switch (type) {
            case CREATED:time = target.getCreated();break;
            case UPD_TIME:time = target.getUpdTime();break;
            case PAY_TIME:time = target.getPayTime();break;
            case CONSIGN_TIME:time = target.getConsignTime();break;
            case EXPRESS_PRINT_TIME:time = target.getExpressPrintTime();break;
        }
        if (time == null) {
            time = TradeTimeUtils.INIT_DATE;
        }
        return time;
    }


}

package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeItemChangeDetailExportParams;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.trades.ISysTradeService;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/1/16 下午7:23
 **/
public class ItemChangeDetailExportService implements IDownloadCenterCallback {

    private Staff staff;

    private TradeItemChangeDetailExportParams params;

    private ISysTradeService sysTradeService;

    public ItemChangeDetailExportService(Staff staff, TradeItemChangeDetailExportParams params, ISysTradeService sysTradeService) {
        this.staff = staff;
        this.params = params;
        this.sysTradeService = sysTradeService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult();
        result.setFlag(true);
        params.setPage(downloadParam.getPage());
        params.getParams().setPage(downloadParam.getPage());
        String[][] tradeExcelContent = sysTradeService.exportItemChangeDetail(staff, params, false);

        result.setData(tradeExcelContent);
        return result;
    }
}

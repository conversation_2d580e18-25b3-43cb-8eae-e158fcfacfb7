package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.services.trades.ITradeSearchService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-05
 */
public abstract class TradeAssembleStrategy implements ISceneLightQueryStrategy{

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    abstract TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long ... sids);

    @Override
    public List<Trade> doQueryTrades(SceneLightQueryContext context, Long... sids) {
        TradeAssembleParams params = buildTradeAssembleParams(context,sids);
        List<Trade> trades = tradeSearchService.queryAndAssemblyByKeys(context.getStaff(), params, "t.sid", sids);
        return trades;
    }
}

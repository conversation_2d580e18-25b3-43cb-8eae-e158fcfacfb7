package com.raycloud.dmj.services.trades.support;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.business.trade.TbTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.TradeOpenUidTransformBusiness;
import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.account.ThriftStaff;
import com.raycloud.dmj.domain.base.ClassWrapper;
import com.raycloud.dmj.domain.base.FieldWrapper;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLog;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradePickingParams;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.payment.util.StackTracesUtils;
import com.raycloud.dmj.domain.trades.search.BaseTrade;
import com.raycloud.dmj.domain.trades.search.ItemQueryParams;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.exception.SearchTimeoutException;
import com.raycloud.dmj.domain.trades.search.exception.SearchFieldsNotSupportFilterException;
import com.raycloud.dmj.domain.trades.search.utils.ControllerParamConverter;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.ec.support.StaffTransformService;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.search.TradeSearchSupport;
import com.raycloud.dmj.services.trades.support.search.convert.ReceiverConditionConverter;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.erp.trade.search.db.TradeStatCountBusiness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.*;
import java.util.concurrent.RejectedExecutionException;
import java.util.stream.Collectors;

import static com.raycloud.dmj.Strings.contains;
import static com.raycloud.dmj.Strings.getAsStringArray;
import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;
import static java.util.stream.Collectors.toList;

@Service("solrTradeSearchService")
public class SolrTradeSearchService extends AbsTradeSearchService {

    @Resource(name = "tbTradeSearchService")
    AbsTradeSearchService tbTradeSearchService;
    @Resource
    ITradeQueryParamsService tradeQueryParamsService;
    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    IExpressTemplateCommonService expressTemplateCommonService;
    @Resource
    TradePddTradeBusiness tradePddTradeBusiness;
    @Resource
    TbTradeDecryptBusiness tbTradeDecryptBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Autowired
    ITradeWaveService tradeWaveService;

    @Resource
    TradeOpenUidTransformBusiness tradeOpenUidTransformBusiness;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Resource
    TradeSqlQueryBuilder tradeSqlQueryBuilder;

    @Resource
    StaffTransformService staffTransformService;
    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;


    @Resource
    TradeSearchSupport tradeSearchSupport;

    private final Logger logger = Logger.getLogger(this.getClass());

    private final List<String> NICK_SOURCE = Arrays.asList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO, CommonConstants.PLAT_FORM_TYPE_1688);

    @Override
    protected TradeAssembleBusiness getTradeAssembleBusiness() {
        return tradeAssembleBusiness;
    }

    @Override
    public Trades search(Staff staff, TradeQueryParams params) {
        TradeQueryContext context = params.getContext();
        QueryLogBuilder logBuilder = null;
        if (context.getLogBuilder() == null || context.getLogBuilder() == AbsLogBuilder.getNvlInstance()) {
            logBuilder = new QueryLogBuilder(staff).append("查询耗时统计:").startTimer();
            context.setLogBuilder(logBuilder);
        }
        long startTime = System.currentTimeMillis();
        try {
            if (!doCheckParams(staff,params)) {
                return createTrades(null, 0L, params.getPage(), params.getSort());
            }
            //是否为高级查询或自定义查询,queryId转换成Systatus在这个方法里
            doBeforeSearch(staff, params);
            //进行订单过滤                实际查询订单的在这里
            Trades trades = null;
            if (null != params.getQueryId() && params.getQueryId() == QUERY_DELIVER_EXCEPTION) {
                trades = deliverAndUploadQuery(staff, params, false);
            } else {
                trades = tbTradeSearchService.search(staff, params);
            }
            //如果智能单品数量没有指定，拿着当前的sid去获取ext中的数据
            //获取订单智能计算单品数量
            reBaseTimer(params);
            if (params.isFillItemQuantity() == null || params.isFillItemQuantity()){
                trades = tbTradeSearchService.setTradeExtItemQuantity(staff, trades);
                recordTimer(params,"setTradeExtItemQuantity");
            }
            if (!params.isBreakQuery()) {
                reBaseTimer(params);
                filterTrades(staff, trades, params);
                recordTimer(params,"filterTrades");
            }
            if(BooleanUtils.isNotFalse(params.getIfSensitive()) || ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getInvokePlatformSensitiveApi(),String.valueOf(staff.getCompanyId()))){
                if (trades != null && CollectionUtils.isNotEmpty(trades.getList()) && params.getSysMask() == 1) {
                    reBaseTimer(params);
                    TradeSysDigestUtils.batchSensitive(staff, trades.getList());
                    recordTimer(params,"TradeSysDigestSensitive");
                }
                if (params.isPddMask()) {
                    reBaseTimer(params);
                    trades.setList(tradePddTradeBusiness.pddMaskDataReplace(staff, trades.getList()));
                    recordTimer(params,"pddMaskDataReplace");
                }
            }
            if (params.isConvertSysStatus() && StringUtils.isEmpty(params.getFields())) {
                convertSysStatus(trades.getList(), tradeConfigService.get(staff));
            }
            if ((params.getQueryOrder() == null || params.getQueryOrder()) && StringUtils.isEmpty(params.getFields()) && CollectionUtils.isNotEmpty(trades.getList())) {
                reBaseTimer(params);
                for (Trade trade : trades.getList()) {
                    TradeExceptionUtils.analyze(staff, trade);
                }
                recordTimer(params,"analyzeException");
            }
            return trades;
        }catch (QueryTimeoutException e) {
            logger.error(LogHelper.buildLog(staff, "数据库查询超时"), StackTracesUtils.filterStackTraces(e));
            handDbtimeOut(staff, params, context, logBuilder, e);
        } catch (HystrixRuntimeException e) {
            Throwable cause = e.getCause();
            new QueryLogBuilder(staff).appendError("订单查询出错",e).appendIf(()->(cause != null),"cause:" + cause.getMessage()).printError(logger,e);
            if (cause != null) {
                if (cause instanceof QueryTimeoutException || Objects.equals("Statement cancelled due to timeout or client request",cause.getMessage())) {
                    handDbtimeOut(staff, params, context, logBuilder, e);
                    throw new TradeException("数据库查询超时");
                }
                if (cause instanceof java.util.concurrent.TimeoutException) {
                    handDbtimeOut(staff, params, context, logBuilder, e);
                    throw new TradeException("订单查询超时");
                }
                if (cause instanceof org.springframework.jdbc.BadSqlGrammarException) {
                    throw new TradeException("查询语句组织错误");
                }
                if (cause instanceof RejectedExecutionException) {
                    throw new TradeException("当前系统繁忙,请稍后再试");
                }
                if (Objects.equals("Hystrix circuit short-circuited and is OPEN",cause.getMessage())) {
                    throw new TradeException("当前服务不可用,请稍后再试");
                }
                throw new TradeException(cause.getMessage());
            }
            throw new TradeException("订单查询出错");
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "订单查询出错:" + e.getMessage()), StackTracesUtils.filterStackTraces(e));
            throw e;
        }finally {
            if (logBuilder != null) {
                logBuilder.startWatch().appendTook(TbTradeSearchService.queryTookThreshold(staff.getCompanyId(),null,3800L)).multiPrintDebug(logger);
            }
        }
        return null;
    }

    private void handDbtimeOut(Staff staff, TradeQueryParams params, TradeQueryContext context, QueryLogBuilder logBuilder, RuntimeException e) {
        if (logBuilder != null) {
            logBuilder.append("timeout");
        }

        String field = params.getSort() != null ? params.getSort().getField() : null;
        Long daysBetween = getDaysBetween(params.getStartTime(), params.getEndTime());
        if (daysBetween == null || daysBetween > 60) {
            context.addSuggests("请指定时间范围条件 或者缩小查询时间范围分批次查询");
        }
        analysisLog(staff, params, e, field, daysBetween);

        String suggestString = context.getSuggestString(null,true);
        if (suggestString == null) {
            throw new SearchTimeoutException("当前查询数据量过载，请尝试缩小时间范围,使用下单时间/付款时间等字段进行排序,或者增加其他查询条件后再次查询,当前时间范围(天):"+ daysBetween+",当前排序:"+ getFieldChName(field));
        }else {
            throw new SearchTimeoutException(suggestString);
        }
    }


    private void analysisLog(Staff staff, TradeQueryParams params, RuntimeException e, String field, Long daysBetween) {
        new QueryLogBuilder(staff).append("当前查询数据超时,").append("queryId", params.getQueryId()).append("queryFlag", params.getQueryFlag()).append("useHasNext", params.getUseHasNext())
                .append("sort", field).append("timeType", params.getTimeType()).append("sameField",Objects.equals(params.getTimeType(), field))
                .append("start",format(params.getStartTime())).append("end", format(params.getEndTime())).append("range", daysBetween)
                .append("queryItem", params.getContext().hasItemQueryOrigin()).append("itemIndex", Objects.equals(params.getKey(),"numIid") || Objects.equals(params.getKey(),"skuId")
                        || Objects.equals(params.getKey(),"mainOuterId")  )
                .append("optimizations", params.getContext().getOptimizations() == null?"null":Strings.join(",", params.getContext().getOptimizations()))
                .printError(logger, e);
    }

    private String format(Date date){
        if (date == null) {
            return null;
        }
        return DateUtil.format(date,DateUtil.LONG_FORMAT);
    }

    private Long getDaysBetween(Date begin,Date end){
        if (begin ==  null && end == null) {
            return null;
        }
        if (begin ==  null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.MONTH, -3);
            begin = calendar.getTime();
        }
        if (end ==  null) {
            end = new Date();
        }
        return (end.getTime() - begin.getTime()) / (1000 * 60 * 60 * 24);
    }

    private boolean waveShortIdToWaveId(Staff staff, TradeQueryParams params) {
        Long waveShortId = params.getWaveShortId();
        if (waveShortId == null) {
            return true;
        }
        Map<Long, Long> waveIdMap = tradeWaveService.queryWaveIdAndShortId(staff, Collections.singletonList(waveShortId), null);
        if (MapUtils.isEmpty(waveIdMap)) {
            return false;
        }
        Set<Long> dbWaveIdSet = waveIdMap.keySet();
        Long requestWaveId = params.getWaveId();
        if (requestWaveId != null && !dbWaveIdSet.contains(requestWaveId)) {
            return false;
        }
        if (requestWaveId == null) {
            params.setWaveId(dbWaveIdSet.iterator().next());
        }
        return true;
    }


    private void convertSysStatus(List<Trade> list, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Trade trade : list) {
            trade.setSysStatus(TradeStatusUtils.convertSysStatus(trade, tradeConfig));
        }
    }

    public Trades backTradeSearchUnfuse(Staff staff, TradeQueryParams params) {
        QueryLogBuilder logBuilder = null;
        if (params.getContext().getLogBuilder() == null) {
            logBuilder = new QueryLogBuilder(staff).append("查询耗时统计:").startTimer();
            params.getContext().setLogBuilder(logBuilder);
        }
        try {
            if (!doCheckParams(staff,params)) {
                return createTrades(null, 0L, params.getPage(), params.getSort());
            }
            //是否为高级查询或自定义查询,queryId转换成Systatus在这个方法里
            doBeforeSearch(staff, params);
            //进行订单过滤                实际查询订单的在这里
            Trades trades = null;
            if (null != params.getQueryId() && params.getQueryId() == QUERY_DELIVER_EXCEPTION) {
                trades = deliverAndUploadQuery(staff, params, true);
            } else {
                trades = tbTradeSearchService.backTradeSearchUnfuse(staff, params);
            }
            if (!params.isPureTrade() && !params.isBreakQuery()) {
                reBaseTimer(params);
                filterTrades(staff, trades, params);
                recordTimer(params,"filterTrades");
            }
            if (!params.isPureTrade() && params.isPddMask()) {
                reBaseTimer(params);
                trades.setList(tradePddTradeBusiness.pddMaskDataReplace(staff, trades.getList()));
                recordTimer(params,"pddMaskDataReplace");
            }
            return trades;
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "订单查询出错"), e);
            throw e;
        }finally {
            if (logBuilder != null) {
                logBuilder.startWatch().appendTook(DevLogBuilder.isDevEnv()?100L: 3800L).multiPrintDebug(logger);
            }
        }
    }

    @Override
    public void fillTradeInfo(Staff staff, List<Trade> trades) {
        tbTradeSearchService.fillTradeInfo(staff, trades);
    }

    ;

    @Override
    public List<Order> listSuits(Staff staff, Long orderId) {
        return tbTradeSearchService.listSuits(staff, orderId);
    }


    /**
     * 当前端传递的是某一个主店铺下的电子面单时,就要找出其他店铺下同样的电子面单
     *
     * @param staff
     * @param params
     */
    private void handleQueryExpressTemplate(Staff staff, TradeQueryParams params) {
        if (params.getExpress() != null && params.getExpress().length > 0) {
            reBaseTimer(params);
            Set<String> templates = new HashSet<String>();
            List<Long> wlbExpressIds = new ArrayList<Long>();
            for (String template : params.getExpress()) {
                templates.add(template);
                String[] array = template.split("_");
                if (array.length == 2 && "1".equals(array[1])) {
                    wlbExpressIds.add(Long.parseLong(array[0]));
                }
            }
            List<String> extraTemplates = new ArrayList<String>();
            if (!wlbExpressIds.isEmpty()) {
                Map<Long, List<UserWlbExpressTemplate>> wlbTemplateMap = userWlbExpressTemplateService.queryAllByMasterTemplateId(staff, wlbExpressIds);
                if (!wlbTemplateMap.isEmpty()) {
                    for (Map.Entry<Long, List<UserWlbExpressTemplate>> entry : wlbTemplateMap.entrySet()) {
                        List<UserWlbExpressTemplate> list = entry.getValue();
                        if (list != null) {
                            for (UserWlbExpressTemplate template : list) {
                                extraTemplates.add(template.getId() + "_1");
                            }
                        }
                    }
                }
            }
            if (!extraTemplates.isEmpty()) {
                templates.addAll(extraTemplates);
                params.setExpress(templates.toArray(new String[templates.size()]));
            }
            recordTimer(params,"handleQueryExpressTemplate");
        }
    }

    //isExport 是否是导出查询
    private Trades deliverAndUploadQuery(Staff staff, TradeQueryParams params, boolean isExport) {
        //先查一下trade_stat,然后指定sid来查询trade，然后设置total
        //params.setQueryFlag(0);//不计数
        if ((null != params.getSid() && params.getSid().length > 0) || (null != params.getTid() && params.getTid().length > 0) || (null != params.getOutSids() && params.getOutSids().length > 0) || null != params.getStartTime() || null != params.getEndTime()) {
            return isExport ? tbTradeSearchService.backTradeSearchUnfuse(staff, params) : tbTradeSearchService.search(staff, params);
        }

        /*Page page = params.getPage();
        if (null == page) {
            page = new Page();
        }
        List<Long> sids = tradeStatBusiness.queryDeliverAndUploadSids(staff, params);
        if (CollectionUtils.isEmpty(sids)) {
            return new Trades();
        }
        List<Long> needQuery = null;
        if (page.getStartRow()>sids.size()) {
            return new Trades();
        }
        Integer endRow = (page.getStartRow()==1?0:page.getStartRow())+page.getOffsetRow();
        if (sids.size()>endRow) {
            needQuery = sids.subList(page.getStartRow()==1?0:page.getStartRow(),endRow);
        } else {
            needQuery = sids.subList(page.getStartRow()==1?0:page.getStartRow(),sids.size());
        }
        if (CollectionUtils.isEmpty(needQuery)) {
            return new Trades();
        }
        Integer oldPageNo = page.getPageNo();
        params.setSid(sids.toArray(new Long[0]));
        params.getPage().setPageNo(1);
        Trades trades = tbTradeSearchService.search(staff, params);
        trades.setTotal((long)sids.size());
        trades.getPage().setPageNo(oldPageNo);*/
        Trades trades = tbTradeSearchService.search(staff, params);
        return trades;
    }


    private void processExpressCompany(Staff staff, TradeQueryParams params) {
        if (null != params.getCommonIds() && 0 < params.getCommonIds().size()) {
            reBaseTimer(params);
            List<Long> expressIds = params.getCommonIds();
            List<Long> queryExpressIds = new ArrayList<>();
            List<Long> commmonTemplateIds = new ArrayList<>();
            List<Long> cloudTemplateIds = new ArrayList<>();
            for (Long expressId : expressIds) {
                if (expressId > 0L) {
                    queryExpressIds.add(expressId);
                }
                if (expressId.equals(0L) || expressId.equals(-2L)) {
                    commmonTemplateIds.add(expressId.equals(-2L) ? -1L : expressId);
                    cloudTemplateIds.add(expressId.equals(-2L) ? -1L : expressId);
                }
            }
            if (queryExpressIds.size() > 0) {
                expressTemplateCommonService.getTemplateListByExpressIds(staff, queryExpressIds, commmonTemplateIds, cloudTemplateIds);
            }
            params.setCommonIds(commmonTemplateIds);
            params.setCloudIds(cloudTemplateIds);
            recordTimer(params,"processExpressCompany");
        }
    }

    @Override
    public <T> List<Trade> queryByKeys(Staff staff, String tradeFields, String orderFields, String key, boolean filter, T... values) {
        return tbTradeSearchService.queryByKeys(staff, tradeFields, orderFields, key, filter, values);
    }

    @Override
    public <T> List<TbTrade> queryByKeys(Staff staff, String fields, String key, T... values) {
        return tbTradeSearchService.queryByKeys(staff, fields, key, values);
    }

    @Override
    public <T> List<Trade> queryAndAssemblyByKeys(Staff staff, TradeAssembleParams params, String key, T... values) {
        return tbTradeSearchService.queryAndAssemblyByKeys(staff, params, key, values);
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, String... tids) {
        return tbTradeSearchService.queryByTids(staff, showDetail, tids);
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay,String... tids) {
        return tbTradeSearchService.queryByTids(staff, showDetail, queryTradePay, tids);
    }

    @Override
    public List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay, AbsLogBuilder logBuilder, String... tids) {
        return tbTradeSearchService.queryByTids(staff, showDetail, queryTradePay,logBuilder, tids);
    }



    @Override
    public List<TbTrade> queryByTidsWithOutFill(Staff staff, boolean showDetail, String... tids) {
        return tbTradeSearchService.queryByTidsWithOutFill(staff, showDetail, tids);
    }

    /**
     * 这个实现，将会先根据sids，查询出
     */
    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, Long... sids) {
        return tbTradeSearchService.queryBySids(staff, showDetail, sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, AbsLogBuilder logBuilder, Long... sids) {
        return tbTradeSearchService.queryBySids(staff, showDetail,logBuilder, sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, Long... sids) {
        return tbTradeSearchService.queryBySids(staff, showDetail, showTradeExt, sids);
    }

    @Override
    public List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, boolean pddMaskData, Long... sids) {
        return tbTradeSearchService.queryBySids(staff, showDetail, showTradeExt, pddMaskData, sids);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, Long... sids) {
        return tbTradeSearchService.queryBySidsNoFilter(staff, showDetail, sids);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, AbsLogBuilder logBuilder, Long... sids) {
        return tbTradeSearchService.queryBySidsNoFilter(staff, showDetail,logBuilder, sids);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, boolean needFill, Long... sids) {
        return tbTradeSearchService.queryBySidsNoFilter(staff, showDetail, needFill, sids);
    }

    @Override
    public List<Trade> queryBySidsNoFilter(Staff staff, TradeQueryNoFilterParams params, Long... sids) {
        return tbTradeSearchService.queryBySidsNoFilter(staff, params, sids);
    }

    @Override
    public List<Trade> queryBySplitSids(Staff staff, boolean showDetail, Long... splitSids) {
        return tbTradeSearchService.queryBySplitSids(staff, showDetail, splitSids);
    }

    @Override
    public Trade queryBySid(Staff staff, boolean showDetail, Long sid) {
        return tbTradeSearchService.queryBySid(staff, showDetail, sid);
    }

    @Override
    public Trade queryBySidWithOutFill(Staff staff, boolean showDetail, Long sid) {
        return tbTradeSearchService.queryBySidWithOutFill(staff, showDetail, sid);
    }

    @Override
    public Trade queryBySid(Staff staff, boolean showDetail, int pddDecrypt, Long sid) {
        return tbTradeSearchService.queryBySid(staff, showDetail, pddDecrypt, sid);
    }

    @Override
    public List<Trade> queryByShortId(Staff staff, boolean showDetail, Long... shortIds) {
        return tbTradeSearchService.queryByShortId(staff, showDetail, shortIds);
    }

    @Override
    public List<Trade> queryByShortIdWithOutFill(Staff staff, boolean showDetail, Long... shortIds) {
        return tbTradeSearchService.queryByShortIdWithOutFill(staff, showDetail, shortIds);
    }

    @Override
    public List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder, String... sysStatusList) {
        return tbTradeSearchService.queryByOutSid(staff, outSid, queryOrder, sysStatusList);
    }

    public List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder,boolean needFill, String... sysStatusList) {
        return tbTradeSearchService.queryByOutSid(staff, outSid, queryOrder,needFill, sysStatusList);
    }

    @Override
    public List<Long> querySidsBySysItemSkuId(Staff staff, Long itemSysId, Long skuSysId, Page page, String... stockStatuss) {
        return tbTradeSearchService.querySidsBySysItemSkuId(staff, itemSysId, skuSysId, page, stockStatuss);
    }

    @Override
    public List<Long> querySidsByItemSkuIdBatch(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, Page page) {
        return tbTradeSearchService.querySidsByItemSkuIdBatch(staff, sysItemIds, sysSkuIds, page);
    }

    @Override
    public List<Long> querySidsByItemIds(Staff staff, List<String> itemIds, Boolean matched, Page page) {
        return tbTradeSearchService.querySidsByItemIds(staff, itemIds, matched, page);
    }

    @Override
    public List<Long> querySidsBySkuIds(Staff staff, List<String> skuIds, Boolean matched, Page page) {
        return tbTradeSearchService.querySidsBySkuIds(staff, skuIds, matched, page);
    }

    @Override
    public List<Long> querySidsByItemSkuIds(Staff staff, List<String> itemIds, List<String> skuIds, Boolean matched, Page page) {
        return tbTradeSearchService.querySidsByItemSkuIds(staff, itemIds, skuIds, matched, page);
    }

    @Override
    public Orders queryHotItem(Staff staff, TradeQueryParams params) {
        doBeforeSearch(staff, params);
        return tbTradeSearchService.queryHotItem(staff, params);
    }

    @Override
    public List<TbOrder> queryFieldByItemSkuIds(Staff staff, ItemQueryParams params) {
        return tbTradeSearchService.queryFieldByItemSkuIds(staff, params);
    }

    @Override
    public List<Trade> searchDbTradeByDate(Staff staff, Page page, List<String> statusList, Date startDate, Date endDate) {
        return tbTradeSearchService.searchDbTradeByDate(staff, page, statusList, startDate, endDate);
    }

    @Override
    public List<Trade> searchTradeByQueryParams(Staff staff, TradeQueryParams tradeQueryParams) {
        return tbTradeSearchService.searchTradeByQueryParams(staff, tradeQueryParams);
    }

    @Override
    public Long searchDbTradeByDateOfCount(Staff staff, List<String> statusList, Date startDate, Date endDate) {
        return tbTradeSearchService.searchDbTradeByDateOfCount(staff, statusList, startDate, endDate);
    }

    @Override
    public List<ConsignRecord> queryConsignRecordBySids(Staff staff, Long... sids) {
        return tbTradeSearchService.queryConsignRecordBySids(staff, sids);
    }

    @Override
    public List<Trade> queryByPickingParams(Staff staff, TradePickingParams tradePickingParams, boolean queryOrder, String... sysStatus) {
        return tbTradeSearchService.queryByPickingParams(staff, tradePickingParams, queryOrder, sysStatus);
    }

    public void doBeforeSearch(Staff staff, TradeQueryParams params) {
        if (params.getId() != null && params.getId() > 0) {
            reBaseTimer(params);
            TradeQueryParams templateParams = tradeQueryParamsService.queryById(staff, params.getId());
            mergeTemplateParams(params, templateParams);
            recordTimer(params,"mergeTemplateParams");
        }
        params.getContext().setOrignSysStatus(params.getSysStatus());
        params.getContext().setOrignUserIds(params.getUserIds());
        params.setCompanyId(staff.getCompanyId());
        //处理时间
        doProcessTime(params);
        doProcessMultiPacks(staff, params);

        if (params.getQueryId() != null) {
            Long rapidQueryId = rapidPrintQueryMap.get(params.getQueryId());
            if (rapidQueryId != null) {
                params.setQueryId(rapidQueryId);
            }
            TradeQueryParams templateParams = getByQueryId(params.getQueryId());
            if (QUERY_RAPID_PRINT.equals(params.getQueryId()) && StringUtils.isNotBlank(params.getRapidPrintQueryIds())) {
                templateParams = getRapidQueryId(params.getRapidPrintQueryIds());
            }
            mergeTemplateParams(params, templateParams);
        }
        //在没有指定系统状态的条件下 如果有已发未验/已发已验条件 默认查询已发货后的订单
        if (params.getPackageStatus() != null && params.getPackageStatus().length > 0 && !params.getContext().hasOrignSysStatus()) {
            Set<String> list = new HashSet<>();
            list.add(Trade.SYS_STATUS_FINISHED);
            list.add(Trade.SYS_STATUS_CLOSED);
            list.add(Trade.SYS_STATUS_SELLER_SEND_GOODS);
            list.addAll(Arrays.asList(params.getSysStatus()));
            params.setSysStatus(list.toArray(new String[0]));
        }

        handleQueryExpressTemplate(staff, params);
        processExpressCompany(staff, params);
        if (StringUtils.isNotBlank(params.getReceiverName()) || StringUtils.isNotBlank(params.getReceiverMobile())) {
            reBaseTimer(params);
            processDesensitizationQuery(staff, params);
            recordTimer(params,"processDesensitizationQuery");
        }
        if (params.getBuyerNick() != null && params.getBuyerNick().length > 0) {
            reBaseTimer(params);
            //数据过大一定会慢
            LogKit.took(() -> {
                processBuyerNickQuery(staff, params);
                return null;
            }, staff, logger);
            recordTimer(params,"processBuyerNickQuery");
        }
    }

    private void doProcessMultiPacks(Staff staff, TradeQueryParams params) {
        reBaseTimer(params);
        Set<Long> sidsByMultiPacks = tradeSqlQueryBuilder.getSidsByMultiPacks(staff, params.getOutSids());
        if (CollectionUtils.isNotEmpty(sidsByMultiPacks)) {
            params.getContext().setOutSidsRelSidSet(sidsByMultiPacks);
        }
        recordTimer(params,"doProcessMultiPacks");
    }

    private void processBuyerNickQuery(Staff staff, TradeQueryParams params) {
        List<User> users = staff.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            return;
        }

        //客户订单并且使用呢称搜索，昵称已经是传入的明文 不去调用平台的转换openUid的接口 防止对平台接口的大量无效调用
        if (org.apache.commons.lang.StringUtils.equals(org.apache.commons.lang.StringUtils.trimToEmpty(params.getTradeType()),"21")) {
            return;
        }
        String[] buyerNick = params.getBuyerNick();
        Long[] userIds = params.getUserIds();
        boolean containsUserId = userIds == null || userIds.length == 0;
        List<String> list = tradeOpenUidTransformBusiness.loginIds2OpenUid(
                users.stream().filter(u -> containsUserId || Arrays.asList(params.getUserIds()).contains(u.getId())).collect(toList()),
                Arrays.asList(buyerNick));
        if (CollectionUtils.isNotEmpty(list)) {
            params.setOpenUid(list.toArray(new String[0]));
        }
    }

    private boolean checkUser(User u) {
        boolean result = NICK_SOURCE.contains(u.getSource())  //允许uid查询的source
                && !u.isSandbox() //非沙箱
                && u.getActive() != null && u.getActive() != 0 //非停用
                && u.getInvalidSession() != null && u.getInvalidSession() != 0;//非授权过期
        return result;
    }

    @Resource
    private ReceiverConditionConverter receiverConditionConverter;

    private void processDesensitizationQuery(Staff staff, TradeQueryParams params) {
        List<String> tids = receiverConditionConverter.processDesensitizationQuery(staff,params.getReceiverName(),params.getReceiverMobile(),params.getReceiverPhone(),params.getStartTime(),params.getEndTime(),params.getUserIds());
        if (CollectionUtils.isNotEmpty(tids)) {
            params.setOrTids(Sets.newHashSet(tids).toArray(new String[0]));
        }
    }


    public void doProcessTime(TradeQueryParams params) {
        if (params.getId() != null && params.getId() > 0) {
            if (null != params.getDaysSelection()) {
                if (1 == params.getDaysSelection()) {
                    params.setTimeType("pay_time");
                    if (null != params.getDaysOfPaymentStart()) {
                        params.setEndTime(getTimeEnd(DateUtils.addDays(new Date(), -params.getDaysOfPaymentStart())));
                    }
                    if (null != params.getDaysOfPaymentEnd()) {
                        params.setStartTime(getTimeStart(DateUtils.addDays(new Date(), -params.getDaysOfPaymentEnd())));
                    }
                } else if (2 == params.getDaysSelection()) {
                    params.setTimeType("created");
                    if (null != params.getDaysOfCreateEnd()) {
                        params.setEndTime(getTimeEnd(DateUtils.addDays(new Date(), -params.getDaysOfCreateStart())));
                    }
                    if (null != params.getDaysOfCreateEnd()) {
                        params.setStartTime(getTimeStart(DateUtils.addDays(new Date(), -params.getDaysOfCreateEnd())));
                    }
                } else if (3 == params.getDaysSelection() && params.getRemainTimeHours() != null) {
                    Date TimeoutActionTime = DateUtils.addHours(new Date(), params.getRemainTimeHours());
                    params.setTimeoutActionTime(TimeoutActionTime);
                }
            }
        }
        /**
         * 不是根据sid,tid,outSid,buyerNick查询 && 开始时间和结束时间都是空 && 选择了商品信息，订单时间默认选择近十五天。
         * 如果查询的时候手动选择了订单时间，则按照用户手动输入的来
         *
         * outerIdAndSysItemIds;
         * outerIdAndSysSkuIds;
         * onlyOuterIdAndSysItemIds;
         * onlyOuterIdAndSysSkuIds;
         * excludeOuterIdAndSysItemIds;
         * excludeOuterIdAndSysSkuIds;
         */
        if (!(params.getContext().isUniqueQuery() || (params.getOutSids() != null && params.getOutSids().length > 0) || ("mobileTail".equals(params.getKey()) && StringUtils.isNotBlank(params.getText())))) {
            if (params.getStartTime() == null && params.getEndTime() == null && params.getTimeoutActionTime() == null) {
                if (params.getContext().hasItemQuery4TimeProcess() || (params.getContext().hasItemWholeQuery4TimeProcess() && tradeLocalConfigurable.isTradeSearchWithOrderDefaultTime(params.getCompanyId()))) {
                    params.setStartTime(getTimeStart(DateUtils.addDays(new Date(), -15)));
                }
            }
        }
    }

    private Date getTimeStart(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    private Date getTimeEnd(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    private boolean doCheckParams(Staff staff,TradeQueryParams params) {
        //仅数字 - 和作为多个分隔的","
        String regex = "[\\d-,]+";
        if (StringUtils.isNotBlank(params.getReceiverMobile())) {
            Assert.isTrue(params.getReceiverMobile().matches(regex),"手机号条件非法:"+params.getReceiverMobile());
        }
        if (StringUtils.isNotBlank(params.getReceiverPhone())) {
            Assert.isTrue(params.getReceiverPhone().matches(regex),"固话条件非法:"+params.getReceiverPhone());
        }
        String regex2 = "[\\d-]+";
        if (StringUtils.isNotBlank(params.getMobileTail())) {
            Assert.isTrue(params.getMobileTail().matches(regex2),"手机号后四位非法:"+params.getMobileTail());
        }


        reBaseTimer(params);
        //短波号转波次号
        boolean waveShortIdIsValid = waveShortIdToWaveId(staff, params);
        recordTimer(params,"waveShortIdToWaveId");
        if(!waveShortIdIsValid){
            new QueryLogBuilder(staff).append("查询中断","短波号转波次号接口返回失败或者没有数据").printDebug(logger);
            return  false;
        }
        //根据sid,tid,outSid,buyerNick查询时去除查询起始时间
        if (params.getContext().isUniqueQuery() || (params.getOutSids() != null && params.getOutSids().length > 0) || ("mobileTail".equals(params.getKey()) && StringUtils.isNotBlank(params.getText()))) {
            params.setStartTime(null);
        }
        if (StringUtils.isNotBlank(params.getTimeType()) && !SortConfig.TIME_TYPE_SETS.contains(params.getTimeType().toLowerCase(Locale.ROOT))) {
            params.setTimeType("pay_time");
        }
        Long queryId = params.getQueryId();
        if (queryId != null) {
            if (isFixedQueryId(queryId)) {
                TradeQueryParams tp = getByQueryId(queryId);
                if ((params.getSysStatus() != null && params.getSysStatus().length > 0)) {
                    if (tp != null && tp.getSysStatus() != null && tp.getSysStatus().length > 0
                            && !contains(tp.getSysStatus()[0], getViewSysStatus(params.getSysStatus()))) {
                        return false;
                    }
                }
            } else if (queryId == QUERY_FINISHE_AUDIT) {
                if ((params.getSysStatus() != null && params.getSysStatus().length > 0) &&
                        !contains(Trade.SYS_STATUS_FINISHED_AUDIT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_PACKAGE, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_WEIGHT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_SEND_GOODS, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS, params.getSysStatus())) {
                    return false;
                }
            } else if (queryId == QUERY_EXCEP) {
                if ((params.getSysStatus() != null && params.getSysStatus().length > 0) &&
                        !contains(Trade.SYS_STATUS_WAIT_AUDIT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_FINISHED_AUDIT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_DELIVERY_PRINT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_EXPRESS_PRINT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_PACKAGE, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_WEIGHT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_SEND_GOODS, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_SELLER_SEND_GOODS, params.getSysStatus())) {
                    return false;
                }
            } else if (queryId == QUERY_WAIT_OUTSTOCK) {
                if ((params.getSysStatus() != null && params.getSysStatus().length > 0) &&
                        !contains(Trade.SYS_STATUS_WAIT_AUDIT, params.getSysStatus()) &&
                        !contains(Trade.SYS_STATUS_WAIT_SEND_GOODS, params.getSysStatus())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     *
     * 存在已审核的视图层的状态需要加上Trade.SYS_STATUS_FINISHED_AUDIT
     *
     * @param sysStatus
     * @return
     */
    private String[] getViewSysStatus(String[] sysStatus){
        if(ArrayUtils.isEmpty(sysStatus)){
            return sysStatus;
        }
        HashSet<String> sysStatusSet = Sets.newHashSet(sysStatus);
        if(CollectionUtils.containsAny(TradeStatusUtils.FINISHED_AUDIT_VIEW_STATUS_MAP.keySet(), sysStatusSet)){
            sysStatusSet.add(Trade.SYS_STATUS_FINISHED_AUDIT);
            return sysStatusSet.toArray(new String[0]);
        }
        return sysStatus;
    }

    /**
     * 将系统模版参数合并到当前用户自定义参数中,优先取用户自定义参数值
     *
     * @param template 模版参数对象
     * @return 返回合并了用户自定义参数的模版对象
     */
    private void mergeTemplateParams(TradeQueryParams params, TradeQueryParams template) {
        if (template != null) {
            ClassWrapper<TradeQueryParams> wrapper = new ClassWrapper<TradeQueryParams>(TradeQueryParams.class);
            Map<String, FieldWrapper> fields = wrapper.wrap();
            for (Map.Entry<String, FieldWrapper> entry : fields.entrySet()) {
                String key = entry.getKey();
                if ("class".equals(key)) {//忽略的字段
                    continue;
                }
                if ("sysStatus".equals(key)) {//系统状态特殊处理
                    if (params.getContext().needMergeSysStatus()) {
                        mergeSysStatus(params, template.getSysStatus());
                    }
                    continue;
                }
                FieldWrapper fw = entry.getValue();
                try {
                    Object v1 = fw.get(template);
                    if (v1 != null) {
                        if ((Collection.class.isAssignableFrom(v1.getClass()) && ((Collection) v1).isEmpty()) ||
                                (v1.getClass().isArray() && Array.getLength(v1) == 0)) {
                            continue;
                        }
                        Object v2 = fw.get(params);
                        if (v2 == null || (v2.getClass().isArray() && Array.getLength(v2) == 0) ||
                                (Collection.class.isAssignableFrom(v2.getClass()) && ((Collection) v2).isEmpty())) {//优先取用用户传递的参数
                            fw.set(params, v1);
                        }
                    }
                } catch (Exception e) {
                    logger.error(String.format("合并订单查询参数出错[property=%s]", key));
                }
            }
            if (StringUtils.isNotBlank(params.getKey())) {
                ControllerParamConverter.processKeyQuery(params, params.getKey(), params.getText());
            }
            if (!params.isCheckExcep()) {
                params.setIsExcep(null);
            }
            //自定义 无异常
            if (null != template.getTickExcep() && template.getTickExcep() - 1 == 0) {
                params.setTickExcep(template.getTickExcep());
            }
        }
    }

    private void mergeSysStatus(TradeQueryParams params, String... templates) {
        String[] sysStatusList = params.getSysStatus();//记录用户传递的系统状态
        if (sysStatusList == null || sysStatusList.length == 0) {//用户没有传递任何系统状态，以模版系统状态为准
            params.setSysStatus(templates);
        } else if (templates != null && templates.length > 0) {//用户传递了系统状态且模版也设置了系统状态，需要做合并处理
            //待付款、待审核、待财审、已发货、待出库、已出库 等页面只有一个系统状态，只能以模板理设置的为准
            if (templates.length == 1 && !Trade.SYS_STATUS_FINISHED_AUDIT.equals(templates[0])) {
                params.setSysStatus(templates);
            } else {
                List<String> list = new ArrayList<String>();
                for (String sysStatus : sysStatusList) {
                    String s = sysStatus.trim();
                    if ((Strings.contains(s, templates) ||
                            Trade.SYS_STATUS_WAIT_DELIVERY_PRINT.equals(s) ||
                            Trade.SYS_STATUS_WAIT_EXPRESS_PRINT.equals(s) ||
                            Trade.SYS_STATUS_WAIT_PACKAGE.equals(s) ||
                            Trade.SYS_STATUS_WAIT_WEIGHT.equals(s) ||
                            Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(s) ||
                            Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(s)) &&
                            !list.contains(s)) {
                        list.add(s);
                    }
                }
                params.setSysStatus(list.isEmpty() ? templates : list.toArray(new String[0]));
            }
        }
        //模版未设置系统状态，用户也传递了系统状态以用户的为准，如三个月内订单查询页面
    }

    private Trades filterTrades(Staff staff, Trades trades, TradeQueryParams params) {
        List<Trade> list = trades.getList();
        if (list == null || list.isEmpty()) {
            return trades;
        }
        boolean needRemove = false;
        if (params.getContext().isUniqueQuery()) {
            if (params.getQueryId() != null) {
                Iterator<Trade> iter = list.iterator();
                while (iter.hasNext()) {
                    Trade trade = iter.next();
                    //查询待审核订单和已分配快递待打印订单进行如下过滤
                    if (needFilterTrade(params.getQueryId()) && !Objects.equals(2, params.getIsExcep())) {
                        if(trade.getIsExcep() == null || trade.getUserId() ==null){
                            throw new SearchFieldsNotSupportFilterException(params.getQueryId(),"is_excep","user_id");
                        }
                        //是否异常订单
                        if (trade.getIsExcep() - 1 == 0) {
                            needRemove = true;
                        } else if (trade.getUserId() > 0) {
                            //是否为公司下店铺订单
                            User user = staff.getUserByUserId(trade.getUserId());
                            needRemove = user != null && user.getActive() == 0;
                        }
                    } else if (params.getQueryId() - SystemTradeQueryParamsContext.QUERY_EXCEP == 0 && trade.getUserId() > 0) {
                        if(trade.getIsExcep() == null || trade.getUserId() ==null){
                            throw new SearchFieldsNotSupportFilterException(params.getQueryId(),"is_excep","user_id");
                        }
                        //查询异常订单切订单用户id>0 则进行如下过滤
                        //供销没有真实店铺
                        if (TradeUtils.isGxTrade(trade)) {
                            needRemove = trade.getIsExcep() == 0;
                        } else {
                            User user = staff.getUserByUserId(trade.getUserId());
                            //店铺为激活状态且订单为非异常订单则过滤
                            needRemove = user != null && null != user.getActive() && user.getActive() - 1 == 0 && trade.getIsExcep() == 0;
                        }
                    }
                    if (needRemove) {
                        iter.remove();
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("订单号：%s，数据被过滤", trade.getSid())));
                        }
                        if (trades.getTotal() != null && trades.getTotal() > 0) {
                            trades.setTotal(trades.getTotal() - 1);
                        }
                    }
                }
            }
        }
        return trades;
    }

    boolean needFilterTrade(Long queryId) {
        return queryId - SystemTradeQueryParamsContext.QUERY_WAIT_AUDIT == 0 ||
                queryId - SystemTradeQueryParamsContext.QUERY_WAIT_PRINT == 0;
    }

    @Override
    public List<Trade> queryBySidsContainDeleteTrade(Staff staff, boolean showDetail, Long... sids) {
        return tbTradeSearchService.queryBySidsContainDeleteTrade(staff, showDetail, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, Long... sids) {
        return queryBySidsContainMergeTrade(staff, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, Long... sids) {
        return queryBySidsContainMergeTrade(staff, queryOrder, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, Long... sids) {
        return queryBySidsContainMergeTrade(staff, queryOrder, filter, false, sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, Long... sids) {
        return tbTradeSearchService.queryBySidsContainMergeTrade(staff, queryOrder, filter, containConsign, sids);
    }
    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, TradeAssembleParams params, Long... sids) {
        return tbTradeSearchService.queryBySidsContainMergeTrade(staff,params,sids);
    }

    @Override
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, AbsLogBuilder logBuilder, Long... sids) {
        return tbTradeSearchService.queryBySidsContainMergeTrade(staff, queryOrder, filter, containConsign,logBuilder, sids);
    }


    @Override
    public List<String> queryTids(Staff staff, Long... sids) {
        return tbTradeSearchService.queryTids(staff, sids);
    }

    @Override
    public List<String> queryTidsExist(Staff staff, List<String> tidList) {
        return tbTradeSearchService.queryTidsExist(staff, tidList);
    }

    @Override
    public Map<String, Integer> queryNotFinishedRefundOrScalpingItemNumMap(Staff staff, Long warehouseId) {
        return tbTradeSearchService.queryNotFinishedRefundOrScalpingItemNumMap(staff, warehouseId);
    }

    @Override
    public Long hasPlatformOrderInfo(Staff staff, String tid, Long oid) {
        return tbTradeSearchService.hasPlatformOrderInfo(staff, tid, oid);
    }

    @Override
    public List<Order> queryPlatformOrderInfo(Staff staff, String tid, Long oid) {
        return tbTradeSearchService.queryPlatformOrderInfo(staff, tid, oid);
    }

    @Override
    public List<Trade> queryByWaveId(Staff staff, Page page, boolean showDetail, String tradeFields, Long warehouseId, Long waveId) {
        return tbTradeSearchService.queryByWaveId(staff, page, showDetail, tradeFields, warehouseId, waveId);
    }

    @Override
    public List<String> checkIds(Staff staff, TradeQueryParams params) {

        if ( (params.getSid() == null ||  params.getSid().length == 0)
            &&  (params.getTid() == null ||  params.getTid().length == 0)
            &&  (params.getShortId() == null ||  params.getShortId().length == 0)
            &&  (params.getOutSids() == null ||  params.getOutSids().length == 0)
            &&  (params.getBuyerNick() == null ||  params.getBuyerNick().length == 0))
        {
            throw new IllegalArgumentException("检查数据仅支持系统订单号、内部订单号、平台订单号、快递单号、买家昵称！");
        }

        Trades search;
        params.setPage(new Page().setPageNo(1).setPageSize(10000));
        doBeforeSearch(staff, params);
        if (!ObjectUtils.isEmpty(params.getQueryId())) {
            if (params.getQueryId() - QUERY_EXCEP == 0) {
                //这里设置isOutstock为null,原因是如果isOUtstock为null 才会吧 is_excep=1设置到sql中
                params.setIsOutstock(null);
            }
            search = tbTradeSearchService.search(staff, params);
            if (params.getQueryId() - QUERY_EXCEP == 0) {
                //异常订单的tab的订单查询.在sql拼接的时候不会加上is_excep=1这个条件.需要后面过滤剔除
                if (!ObjectUtils.isEmpty(search) && CollectionUtils.isNotEmpty(search.getList())) {
                    Iterator<Trade> iter = search.getList().iterator();
                    while (iter.hasNext()) {
                        Trade trade = iter.next();
                        //异常订单则过滤
                        if (trade.getIsExcep() == 0) {
                            iter.remove();
                            if (search.getTotal() != null && search.getTotal() > 0) {
                                search.setTotal(search.getTotal() - 1);
                            }
                        }
                    }
                }
            }
        } else {
            search = tbTradeSearchService.search(staff, params);
        }
        if (ObjectUtils.isEmpty(search)) {
            return Collections.emptyList();
        }
        //筛选哪些id不符合要求
        List<String> source = new ArrayList<>();
        List<String> target = new ArrayList<>();
        //1 先判断传入的参数的类型，是sid，还是shortid,tid，还是outSid
        if (CollectionUtils.isNotEmpty(Arrays.asList(params.getSid()))) {
            source = longArray2Stringlist(params.getSid());
            if (search.getList() != null) {
                for (Trade var : search.getList()) {
                    if (CollectionUtils.isNotEmpty(var.getMessageMemos())){
                        var.getMessageMemos().forEach(messageMemo -> {
                            target.add(messageMemo.getSid() + "");
                        });
                    }else {
                        target.add(var.getSid() + "");
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(Arrays.asList(params.getShortId()))) {
            source = longArray2Stringlist(params.getShortId());
            if (search.getList() != null) {
                for (Trade var : search.getList()) {
                    if (CollectionUtils.isNotEmpty(var.getMessageMemos())){
                        var.getMessageMemos().forEach(messageMemo -> {
                            target.add(messageMemo.getShortId() + "");
                        });
                    }else {
                        target.add(var.getShortId() + "");
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(Arrays.asList(params.getTid()))) {
            source = longArray2Stringlist(params.getTid());
            if (search.getList() != null) {
                for (Trade var : search.getList()) {
                    if (CollectionUtils.isNotEmpty(var.getMessageMemos())){
                        var.getMessageMemos().forEach(messageMemo -> {
                            target.add(messageMemo.getTid() + "");
                        });
                    }else {
                        target.add(var.getTid() + "");
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(Arrays.asList(params.getOutSids()))) {
            source = longArray2Stringlist(params.getOutSids());
            HashMultimap<String,String>  primaryOutSid2SubOutSid = doProcessMultiPacks4Primary(staff,params);
            boolean primaryOutSid2SubOutSidisNotEmpty = !primaryOutSid2SubOutSid.isEmpty();
            if (search.getList() != null) {
                for (Trade var : search.getList()) {
                    target.add(var.getOutSid() + "");
                    if(primaryOutSid2SubOutSidisNotEmpty){
                        Set<String> outSids = primaryOutSid2SubOutSid.get(var.getOutSid());
                        if(CollectionUtils.isNotEmpty(outSids)){
                            target.addAll(outSids);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(Arrays.asList(params.getBuyerNick()))) {
            source = longArray2Stringlist(params.getBuyerNick());
            if (search.getList() != null) {
                for (Trade var : search.getList()) {
                    target.add(var.getBuyerNick() + "");
                }
            }
        }
        source.removeAll(target);
        return source;
    }

    /**
     * 返回map   key: primaryoutsid --> value: suboutsid
     * @param staff
     * @param params
     * @return
     */
    private HashMultimap<String, String> doProcessMultiPacks4Primary(Staff staff, TradeQueryParams params) {
        HashMultimap<String,String>  primaryOutSid2SubOutSid = HashMultimap.create();
        String[] outSids = params.getOutSids();
        if (outSids != null && outSids.length > 0) {
            //依据运单号获取一单多包详情
            List<MultiPacksPrintTradeLogDetail> tradeLogDetailList = multiPacksPrintTradeLogService.queryDetailByOutSid(staff,Arrays.asList(outSids));
            if(CollectionUtils.isEmpty(tradeLogDetailList)){
                return primaryOutSid2SubOutSid;
            }

            Map<Long,List<MultiPacksPrintTradeLogDetail>>  pidListMap = tradeLogDetailList.stream().collect(Collectors.groupingBy(MultiPacksPrintTradeLogDetail::getPid));
            //依据ID获取对应一单多包信息  id --> pid
            if(MapUtils.isNotEmpty(pidListMap)){
                List<MultiPacksPrintTradeLog> tradeLogList = multiPacksPrintTradeLogService.queryByIdList(staff,Lists.newArrayList(pidListMap.keySet()));
                if(CollectionUtils.isNotEmpty(tradeLogList)){
                    tradeLogList.forEach(l->{
                        List<MultiPacksPrintTradeLogDetail> logDetails = pidListMap.get(l.getId());
                        if(CollectionUtils.isNotEmpty(logDetails)){
                            logDetails.forEach(l1->{
                                primaryOutSid2SubOutSid.put(l.getOutSid(),l1.getOutSid());
                            });
                        }
                    });
                }
            }
        }
        return primaryOutSid2SubOutSid;
    }

    @Override
    public Long countTaobaoSeries(Staff staff, TradeQueryParams params) {
        return tbTradeSearchService.countTaobaoSeries(staff, params);
    }

    public <T> List<String> longArray2Stringlist(T[] longs) {
        List<String> aa = new ArrayList<>();
        for (T var : longs) {
            aa.add(var + "");
        }
        return aa;
    }

    @Override
    public List<TradeExt> getTradeExtBySid(Staff staff, List<Long> sids) {
        return tbTradeSearchService.getTradeExtBySid(staff, sids);
    }

    @Override
    public List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, InvaildItemQueryParams params) {
        return tbTradeSearchService.queryItemExceptionItem(staff, page, params);
    }

    @Override
    public List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, Sort sort, InvaildItemQueryParams params,List<Long> sids) {
        return tbTradeSearchService.queryItemExceptionItem(staff, page,sort, params,sids);
    }

    @Override
    public Long queryItemExceptionItemCount(Staff staff, InvaildItemQueryParams params,List<Long> sids) {
        return tbTradeSearchService.queryItemExceptionItemCount(staff, params,sids);
    }

    @Override
    public List<InvaildItemTrade> queryItemExceptionTrade(Staff staff, Page page, InvaildItemTradeQueryParams params) {
        return tbTradeSearchService.queryItemExceptionTrade(staff, page, params);
    }

    @Override
    public Long queryItemExceptionTradeCount(Staff staff, InvaildItemTradeQueryParams params) {
        return tbTradeSearchService.queryItemExceptionTradeCount(staff, params);
    }

    @Override
    public List<Trade> queryNotSysTradeByTids(Staff staff, List<String> tids) {
        return tbTradeSearchService.queryNotSysTradeByTids(staff, tids);
    }

    @Override
    public List<Trade> querySomeFieldsByWaveIds(Staff staff, String fields, List<Long> waveIds) {
        return tbTradeSearchService.querySomeFieldsByWaveIds(staff, fields, waveIds);
    }

    @Override
    public Long statisticsWithNotConsign(Staff staff, Date startTime, Date endTime) {
        return tbTradeSearchService.statisticsWithNotConsign(staff, startTime, endTime);
    }

    @Override
    public List<Trade> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids, List<Long> mergeSids) {
        return tbTradeSearchService.querySomeFieldsBySids(staff, fields, sids, mergeSids);
    }

    @Override
    public List<Order> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids) {
        return tbTradeSearchService.querySomeFieldsBySids(staff, fields, new ArrayList<>(sids));
    }

    @Override
    public List<TbTrade> queryByTidsNeedExt(Staff staff, boolean showDetail, boolean needExt, String... tids) {
        return tbTradeSearchService.queryByTidsNeedExt(staff, showDetail, needExt, tids);
    }

//    /**
//     * 获取订单中的智能计算数量
//     */
//    private Trades setTradeExtItemQuantity(Staff staff, Trades trades) {
//        if(CollectionUtils.isEmpty(trades.getList())) {
//            return trades;
//        }
//        List<Trade> tradeList = trades.getList();
//        Set<Long> sidList = tradeList.stream().map(TradeBase::getSid).collect(Collectors.toSet());
//        //获取当前扩展表中的智能单品数量
//        List<TradeExt> tradeExtList = tradeExtService.queryTradeExtJsonItemQuantity(staff, new ArrayList<>(sidList), null, null);
//        if(CollectionUtils.isEmpty(tradeExtList)){
//            return trades;
//        }
//        //给查询结果赋值
//        for(Trade trade:tradeList){
//            for(TradeExt tradeExt:tradeExtList){
//                if(null != trade.getSid() && null != tradeExt.getSid() && trade.getSid().equals(tradeExt.getSid())){
//                    trade.setItemQuantity(tradeExt.getItemQuantity());
//                    break;
//                }
//            }
//        }
//        trades.setList(tradeList);
//        return trades;
//    }

    @Override
    public Trades setTradeExtItemQuantity(Staff staff, Trades trades) {
        return tbTradeSearchService.setTradeExtItemQuantity(staff, trades);
    }

    /**
     * 获取订单中的智能计算数量
     */
    @Override
    public List<Trade> setTradeExtItemQuantity(Staff staff, List<Trade> tradeList) {
        return tbTradeSearchService.setTradeExtItemQuantity(staff, tradeList);
    }


    @Override
    public List<TbTrade> statisticsWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params) {
        return tbTradeSearchService.statisticsWithNotConsign(staff, params);
    }

    @Override
    public Long statisticsCountWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params) {
        return tbTradeSearchService.statisticsCountWithNotConsign(staff, params);
    }

    @Override
    public List<ReplenishTrades> checkReplenishTrade(Staff staff, Long... sids) {
        return tbTradeSearchService.checkReplenishTrade(staff, sids);
    }

    @Override
    public List<Trade> queryTrades4PrintEnd(Staff staff, List<Long> sids, boolean needMerge) {
        return tbTradeSearchService.queryTrades4PrintEnd(staff, sids, needMerge);
    }

    @Override
    public List<Trade> queryMergeTradeList(Staff staff, List<Long> mergeSids) {
        return tbTradeSearchService.queryMergeTradeList(staff, mergeSids);
    }

    @Override
    public List<Trade> queryTrades4Print(Staff staff, List<Long> sids, boolean needOrder) {
        return tbTradeSearchService.queryTrades4Print(staff, sids, needOrder);
    }

    @Override
    public CursorListBase<BaseTrade> searchBaseTrade(TradeQueryRequest request) {
        return tbTradeSearchService.searchBaseTrade(request);
    }

    @Override
    public CursorListBase<TbTrade> searchTrade(TradeQueryRequest request, TradeAssembleParams params) {
        return tbTradeSearchService.searchTrade(request,params);
    }

    @Override
    public Long countTrade(TradeQueryRequest request, TradeAssembleParams params) {
        return tbTradeSearchService.countTrade(request,params);
    }

    @Override
    public List<Trade> queryLightTradeByScene(Staff staff, SenceCodeEnum senceCode,List<Integer> fieldGroups, Long... sids) {
        return tbTradeSearchService.queryLightTradeByScene(staff, senceCode,fieldGroups, sids);
    }

    @Override
    public List<Long> querySidsByOuterIIds(Staff staff, List<String> outerIIds, boolean matched, Page page) {
        return tbTradeSearchService.querySidsByOuterIIds(staff, outerIIds, matched, page);
    }
}

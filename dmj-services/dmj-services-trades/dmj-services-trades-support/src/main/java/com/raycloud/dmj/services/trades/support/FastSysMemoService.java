package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.FastSysMemoDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.IFastSysMemoService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2021_05_25 09:48
 */
@Service
public class FastSysMemoService implements IFastSysMemoService {

    @Resource
    FastSysMemoDao fastSysMemoDao;

    @Override
    public void updateUseTime(Staff staff, Long[] fastSysMemoId) {
        fastSysMemoDao.updateUseTime(staff,fastSysMemoId);
    }

    @Override
    public Object list(Staff staff) {
        return fastSysMemoDao.list(staff,false);
    }

    @Override
    public Object listTop5(Staff staff) {
        return fastSysMemoDao.list(staff,true);
    }

    @Override
    public Object delete(Staff staff, Long fastSysMemoId) {
        return fastSysMemoDao.delete(staff,fastSysMemoId);
    }

    @Override
    public Object add(Staff staff, String content) {
        Long id = IdWorkerFactory.getIdWorker().nextId();
        return fastSysMemoDao.addFastSysMemo(staff,id,content);
    }
}

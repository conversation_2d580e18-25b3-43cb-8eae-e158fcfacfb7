package com.raycloud.dmj.services.trades;

import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.trade.ColumnConfigDao;
import com.raycloud.dmj.dao.trade.UserDataConfigDAO;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.contants.BaseConstants;
import com.raycloud.dmj.domain.enums.ColumnConfigFilterKey;
import com.raycloud.dmj.domain.enums.ColumnConfigFilterValue;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.model.UserDataConfig;
import com.raycloud.dmj.domain.trades.params.PageColumnConfUpdateParam;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.ColumnConfToDataPrivilegeIdEnum;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.domain.utils.DataPrivilegeIds;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.context.IProjectContext;
import com.raycloud.dmj.services.context.ProjectContextEnum;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.privilegeUtils.DataPrivilegeFilter;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.wms.WmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 列配置
 * Created by ruanyg on 16/5/24.
 */
@Service
public class ColumnConfService implements IColumnConfService, ApplicationContextAware {
    private Logger logger = Logger.getLogger(ColumnConfService.class);

    public static final String COLUMN_CONF_CACHE_PREFIX = "column_conf_";

    public static final String PAGE_COLUMN_CONF_CACHE_PREFIX = "page_column_conf_";

    private static final String NEW_STOCK_MENU_KEY = "new_stock_menu";

    private static final String REPORT_SALE_NUM45 = "report_sale_num_45";

    /**
     * 订单打印--快递单未打印
     */
    private static final Long TRADE_PRINT_ID_26 = 26L;
    /**
     * 订单打印--打印发货
     */
    private static final Long TRADE_PRINT_ID_28 = 28L;
    /**
     * 库存状态页面ID
     */
    private static final Long STOCK_STATUS_PAGE_ID = 63L;

    /**
     * 商品库存
     */
    private static final Long ITEM_STOCK_PAGE_ID = 1000140L;

    private static final Long STOCK_ALARM_PAGE_ID = 64L;
    private static final Long WAREHOUSE_STOCK_STATUS_PAGE_ID = 65L;

    private static final Long EXPRESS_SMART_MATCH_ID = 92L;

    private static final List<Long> NEED_CHECK_NEW_WMS = Lists.newArrayList(STOCK_STATUS_PAGE_ID, STOCK_ALARM_PAGE_ID, WAREHOUSE_STOCK_STATUS_PAGE_ID, ITEM_STOCK_PAGE_ID);

    @Resource
    private ColumnConfigDao columnConfigDao;
    @Resource
    private IIndexDubboService indexDubboService;
    @Resource
    private IProjectContext projectContext;
    @Resource
    private Configurable config;
    @Resource
    private TradePayBusiness tradePayBusiness;

    @Resource
    private FeatureService featureService;

    @Resource
    private UserDataConfigDAO userDataConfigDAO;

    @Resource
    private IUserDataConfigService userDataConfigService;

    private ApplicationContext applicationContext;

    private IColumnConfService beanProxy;

    /**
     * 订单打印
     */
    private static final List<String> TRADE_PRINT = Lists.newArrayList("tradeStockRegionType");

    /**
     * 暂存区字段
     */
    private static final List<String> WORKING_SECTION = Lists.newArrayList("sellableNum", "goodGoodsSectionNum", "pickStockNum",
            "commonStockNum", "replenishStockNum", "refundStock", "purchaseStock", "goodsStock", "backGoodsStock", "defectiveStockNum");

    /**
     * 虚拟仓库存字段
     */
    private static final List<String> VIRTUAL_WAREHOUSE_COLUMN = Lists.newArrayList("publicStock", "privateStock");

    private static final List<String> NEW_TEMPLATE_LIST = Lists.newArrayList("templateId", "expressName");

    private static final String PURCHASE_PRICE_COLUMN = "purchasePrice";

    private static final String LOGISTICS_COMPANY_NAME = "logisticsCompanyName";

    @PostConstruct
    public void start() {
        this.beanProxy = applicationContext.getBean(IColumnConfService.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取系统的列配置
     * 注：用户级别，有特殊处理
     */
    @Cacheable(value = "defaultCache#600", key = "'page_column_conf_'+ #staff.companyId + '_' + #pageId ")
    @Override
    public List<PageColumnConf> getPageColumnConfList(Staff staff, long pageId) {
        //查询当前环境
        String projectProfile = projectContext.getProjectProfile();
        List<String> envList = Lists.newArrayList();
        envList.add(projectProfile);
        List<PageColumnConf> pageColumnConfList = columnConfigDao.getPageColumnConfList(pageId, envList);

        if (EXPRESS_SMART_MATCH_ID.equals(pageId)) {
            boolean hasVirtualWarehouse = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
            if (hasVirtualWarehouse) {
                pageColumnConfList = pageColumnConfList.stream()
                        .filter(column -> !NEW_TEMPLATE_LIST.contains(column.getColCode()))
                        .collect(Collectors.toList());
            } else {
                pageColumnConfList = pageColumnConfList.stream()
                        .filter(column -> !LOGISTICS_COMPANY_NAME.equals(column.getColCode()))
                        .collect(Collectors.toList());
            }
        }

        if (STOCK_STATUS_PAGE_ID.equals(pageId) || ITEM_STOCK_PAGE_ID.equals(pageId)) {
            //没有开启虚拟仓功能移除虚拟仓相关列
            boolean hasVirtualWarehouse = featureService.checkHasFeature(staff.getCompanyId(), Feature.VIRTUAL_WAREHOUSE_STOCK);
            if (!hasVirtualWarehouse) {
                pageColumnConfList = pageColumnConfList.stream().filter(conf -> !VIRTUAL_WAREHOUSE_COLUMN.contains(conf.getColCode())).collect(Collectors.toList());
            }
        }

        if (NEED_CHECK_NEW_WMS.contains(pageId)) {
            //非暂存区去除
            if (!WmsUtils.isNewWms(staff)) {
                pageColumnConfList = pageColumnConfList.stream().filter(conf -> !WORKING_SECTION.contains(conf.getColCode())).collect(Collectors.toList());
            }
        } else if (TRADE_PRINT_ID_26.equals(pageId) || TRADE_PRINT_ID_28.equals(pageId)) {
            //非暂存区去除
            if (!WmsUtils.isNewWms(staff)) {
                pageColumnConfList = pageColumnConfList.stream().filter(conf -> !TRADE_PRINT.contains(conf.getColCode())).collect(Collectors.toList());
            }
        }

        return pageColumnConfList;
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'column_conf_'+ #staff.companyId + '_' + #staff.id + '_' + #pageId ")
    public ColumnConfListWrapper getColumnConfigList(Staff staff, long pageId) {
        ColumnConfQueryParam param = new ColumnConfQueryParam();
        param.setPageId(pageId);
        ColumnConfListWrapper wrapper = _getColumnConfigList(staff, param);
        filterColumn(staff, wrapper, pageId);
        return wrapper;
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'column_conf_'+ #staff.companyId + '_' + #staff.id + '_' + #param.pageId ")
    public ColumnConfListWrapper getColumnConfigList(Staff staff, ColumnConfQueryParam param) {
        ColumnConfListWrapper wrapper = _getColumnConfigList(staff, param);
        filterColumn(staff, wrapper, param.getPageId());
        return wrapper;
    }

    @Override
    public ColumnConfListWrapper getStaffColumnConfigList(Staff staff, ColumnConfQueryParam param) {
        ColumnConfListWrapper wrapper = _getColumnConfigList(staff, param);
        filterColumn(staff, wrapper, param.getPageId());
        return wrapper;
    }

    @Override
    public List<ColumnConf> getVisibleColumnConfList(Staff staff, long pageId) {
        ColumnConfListWrapper wrapper = beanProxy.getColumnConfigList(staff, pageId);
        return wrapper.getColumnConfList().stream().filter(columnConf -> columnConf.getVisible() == 1).collect(Collectors.toList());
    }

    @Override
    public List<String> getVisibleColumnFieldList(Staff staff, long pageId) {
        ColumnConfListWrapper wrapper = beanProxy.getColumnConfigList(staff, pageId);
        return wrapper.getColumnConfList().stream().filter(columnConf -> columnConf.getVisible() == 1).map(ColumnConf::getField).collect(Collectors.toList());
    }

    private void filterColumn(Staff staff, ColumnConfListWrapper wrapper, long pageId) {
        List<ColumnConf> columnConfList = wrapper.getColumnConfList();
        if (CollectionUtils.isEmpty(columnConfList)) {
            return;
        }

        tradePayBusiness.handleColumnConfList(staff, columnConfList, pageId);

        filterStockStatusColumn(staff, columnConfList, pageId);
        filterItemRecordColumn(staff, columnConfList, pageId);
        filterSubItemInfoColumn(staff, columnConfList);
    }



    private void filterStockStatusColumn(Staff staff, List<ColumnConf> columnConfList, long pageId) {
        filterNewStockMenu(staff, columnConfList, pageId);
        filterReportSale45Num(staff, columnConfList, pageId);
    }

    //过滤库存状态、仓库库存 45天销量
    private void filterReportSale45Num(Staff staff, List<ColumnConf> columnConfList, long pageId) {
        if (pageId != PageColumnConf.WAREHOUSE_STOCK_STATUS_CONF && pageId != PageColumnConf.Stock_Status_Conf && pageId != ITEM_STOCK_PAGE_ID) {
            return;
        }

        filterColumnConfList(REPORT_SALE_NUM45, staff.getCompanyId(), columnConfList, "sale45Days");
    }

    //剔除老版的库存状态的销量增长率字段
    private void filterNewStockMenu(Staff staff, List<ColumnConf> columnConfList, long pageId) {
        if (pageId != PageColumnConf.Stock_Status_Conf && pageId != ITEM_STOCK_PAGE_ID) {
            return;
        }

        filterColumnConfList(NEW_STOCK_MENU_KEY, staff.getCompanyId(), columnConfList, "saleIncreRate");
    }

    private void filterColumnConfList(String key, Long companyId, List<ColumnConf> columnConfList, String columnConfName) {
        String companyIds = config.getProperty(key, StringUtils.EMPTY);
        if (StringUtils.isBlank(companyIds)) {
            return;
        }

        if ((BaseConstants.COMMA_SPILT + companyIds + BaseConstants.COMMA_SPILT).contains(BaseConstants.COMMA_SPILT + companyId + BaseConstants.COMMA_SPILT)) {
            return;
        }

        Iterator<ColumnConf> confIterator = columnConfList.iterator();
        while (confIterator.hasNext()) {
            ColumnConf columnConf = confIterator.next();
            if (columnConfName.equals(columnConf.getField())) {
                confIterator.remove();
            }
        }
    }

    private void filterGoodsSourceColumn(Staff staff, List<ColumnConf> columnConfList, long pageId) {
        if (pageId != PageColumnConf.ITEM_RECORD_PAGE_ID) {
            return;
        }
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setFeature(Feature.SKU_REPEAT_PROPERTIES);
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        boolean hasFeature = response.isHasFeature();
        if (hasFeature) {
            return;
        }

        columnConfList.removeIf(columnConf ->
                "goodsSource".equals(columnConf.getField()));

    }

    private void filterItemRecordColumn(Staff staff, List<ColumnConf> columnConfList, long pageId) {
        if (pageId != PageColumnConf.ITEM_RECORD_PAGE_ID && pageId != ITEM_STOCK_PAGE_ID) {
            return;
        }

        filterItemRecordOrderUniqueCodeColumn(staff, columnConfList);
    }

    private void filterGoodsSourceColumn(Staff staff, List<ColumnConf> columnConfList) {
        boolean hasFeature = featureService.checkHasFeature(staff.getCompanyId(), Feature.SKU_REPEAT_PROPERTIES);
        if (hasFeature) {
            return;
        }

        columnConfList.removeIf(columnConf -> "goodsSource".equals(columnConf.getField()));
    }

    private void filterItemRecordOrderUniqueCodeColumn(Staff staff, List<ColumnConf> columnConfList) {
        CompanyProfile profile = staff.getCompany().getProfile();

        if (profile.getConf().openOrderUniqueCode()) {
            return;
        }

        columnConfList.removeIf(columnConf -> "uniqueCodeType".equals(columnConf.getField()));
    }

    private ColumnConfListWrapper _getColumnConfigList(Staff staff, ColumnConfQueryParam param) {
        //user column custom
        Long pageId = param.getPageId();
        List<UserColumnCustomConfig> userCustomConfigs = columnConfigDao.getUserColumnCustomConfigList(staff, pageId);
        Map<String, UserColumnCustomConfig> customConfigMap = userCustomConfigs.stream().collect(
                Collectors.toMap(g -> buildKey(staff.getCompanyId(), g.getColId(), pageId), g -> g,
                        (k1, k2) -> k2.getStaffId() != null? k2: k1)
        );

        //page column
        List<PageColumnConf> pageColumnConfList = beanProxy.getPageColumnConfList(staff, pageId);

        pageColumnConfList = filterPageColumnConf(staff, pageColumnConfList, param);

        //去除没有数据权限的列
        if (!DataPrivilegeFilter.hasDataPrivilegeRight(staff, DataPrivilegeIds.ITEM_PURCHASE_PRICE)) {
            pageColumnConfList = pageColumnConfList.stream().filter(conf -> !PURCHASE_PRICE_COLUMN.equals(conf.getColCode())).collect(Collectors.toList());
        }
        Map<Long, PageColumnConf> pageColumnConfMap = pageColumnConfList.stream().collect(
                Collectors.toMap(PageColumnConf::getId, p -> p, (k1, k2) -> k2)
        );

        //user column
        List<UserColumnConf> userColumnConfList = columnConfigDao.getUserColumnConfList(staff, pageId);
        //这两个列配置查不到去查userDataConfig 后面全环境后可以去除
        if ((pageId == PageColumnDataConfigConstants.PACK_ORDER_COLUMN_PAGE_ID || pageId == PageColumnDataConfigConstants.POST_ORDER_COLUMN_PAGE_ID) && CollectionUtils.isEmpty(userColumnConfList)) {
            userColumnConfList = convertFromUserDataConfig(staff, pageId);
        }

        // 用户没有自定义配置列就走默认的页面配置
        if (userColumnConfList.isEmpty()) {
            List<ColumnConf> columnConfList = pageColumnConfList.stream().map(pageColumnConf -> {
                ColumnConf columnConf = new ColumnConf();
                columnConf.setField(pageColumnConf.getColCode());
                columnConf.setTitle(pageColumnConf.getColTitle());
                columnConf.setVisible(pageColumnConf.getIsDefault());
                columnConf.setColId(pageColumnConf.getId());
                columnConf.setWidth(pageColumnConf.getWidth());
                columnConf.setConfig(StringUtils.isEmpty(pageColumnConf.getConfig()) ? null : JSONObject.parseObject(pageColumnConf.getConfig()));
                // 成本价/销售价/批发价/进货价/市场价/历史成本价/其他价格123 判断是否有权限
                String dataPrivilegeId = ColumnConfToDataPrivilegeIdEnum.getDataPrivilegeIdByColCode(pageColumnConf.getColCode());
                if (StringUtils.isNotEmpty(dataPrivilegeId)) {
                    columnConf.setDisabled(!DataPrivilegeFilter.hasDataPrivilegeRight(staff, dataPrivilegeId));
                    if (!DataPrivilegeFilter.hasDataPrivilegeRight(staff, dataPrivilegeId)) {
                        // 未开启数据权限
                        columnConf.setVisible(CommonConstants.JUDGE_NO);
                    }
                } else {
                    columnConf.setDisabled(false);
                }
                if (null==columnConf.getConfig()){
//                    logger.info(LogHelper.buildLog(staff,String.format("公司:%s在获取列配置的时候出现columnConf.getConfig()==null",staff.getCompanyId())));
                    return columnConf;
                }
                Optional.ofNullable(customConfigMap.get(buildKey(staff.getCompanyId(),columnConf.getColId(),pageId)))
                        .map(UserColumnCustomConfig::getConfig).filter(StringUtils::isNotEmpty)
                        .ifPresent(config ->columnConf.getConfig().putAll(JSONObject.parseObject(config)));
                return columnConf;
            }).collect(Collectors.toList());
            return new ColumnConfListWrapper(columnConfList, true);
        }

        // 检测页面默认配置的config是否新增配置，有的话就给用户添加
        checkUserConfig(customConfigMap,pageColumnConfMap,staff);

        // 合并用户列和页面默认列
        List<ColumnConf> columnConfList = userColumnConfList.stream().filter(conf -> pageColumnConfMap.containsKey(conf.getColId())
        ).map(userColumnConf -> {
            PageColumnConf pageColumnConf = pageColumnConfMap.remove(userColumnConf.getColId());
            ColumnConf columnConf = new ColumnConf();
            columnConf.setField(pageColumnConf.getColCode());
            columnConf.setTitle(pageColumnConf.getColTitle());
            columnConf.setVisible(userColumnConf.getVisible());
            columnConf.setColId(userColumnConf.getColId());
            columnConf.setWidth(userColumnConf.getWidth());
            return columnConf;
        }).collect(Collectors.toList());
        // add not exist page column
        if (!pageColumnConfMap.isEmpty()) {
            columnConfList.addAll(pageColumnConfMap.values().stream().map(pageColumnConf -> {
                ColumnConf columnConf = new ColumnConf();
                columnConf.setColId(pageColumnConf.getId());
                columnConf.setWidth(pageColumnConf.getWidth());
                columnConf.setField(pageColumnConf.getColCode());
                columnConf.setTitle(pageColumnConf.getColTitle());
                columnConf.setVisible(pageColumnConf.getIsDefault());
                return columnConf;
            }).collect(Collectors.toList()));
        }
        // 添加用户自定义配置config
        columnConfList.forEach(columnConf -> {
                    columnConf.setConfig(Optional.ofNullable(customConfigMap.get(buildKey(staff.getCompanyId(), columnConf.getColId(), pageId)))
                            .map(UserColumnCustomConfig::getConfig).filter(StringUtils::isNotEmpty).map(JSONObject::parseObject).orElse(null));
                    // 成本价/销售价/批发价/进货价/市场价/历史成本价/其他价格123 判断是否有权限
                    String dataPrivilegeId = ColumnConfToDataPrivilegeIdEnum.getDataPrivilegeIdByColCode(columnConf.getField());
                    if (StringUtils.isNotEmpty(dataPrivilegeId)) {
                        columnConf.setDisabled(!DataPrivilegeFilter.hasDataPrivilegeRight(staff, dataPrivilegeId));
                        if (!DataPrivilegeFilter.hasDataPrivilegeRight(staff, dataPrivilegeId)) {
                            // 未开启数据权限
                            columnConf.setVisible(CommonConstants.JUDGE_NO);
                        }
                    } else {
                        columnConf.setDisabled(false);
                    }
                }
        );
        return new ColumnConfListWrapper(columnConfList, false);
    }

    private List<PageColumnConf> filterPageColumnConf(Staff staff, List<PageColumnConf> pageColumnConfList, ColumnConfQueryParam param) {
        if(CollectionUtils.isEmpty(pageColumnConfList)) {
            return pageColumnConfList;
        }
        Map<String, String> filteMap = Maps.newHashMap();
        filteMap.put(ColumnConfigFilterKey.WMS_VERSION.getCode(), WmsUtils.isOpenWms(staff) ?
                (WmsUtils.isNewWms(staff) ? ColumnConfigFilterValue.NEW_WMS.getCode() : ColumnConfigFilterValue.OLD_WMS.getCode()) : ColumnConfigFilterValue.NO_WMS.getCode());

        filteMap.put(ColumnConfigFilterKey.OPEN_SHIPPER.getCode(), CompanyUtils.openMultiShipper(staff) ? ColumnConfigFilterValue.YES.getCode() :  ColumnConfigFilterValue.NO.getCode());

        if(StringUtils.isNotEmpty(param.getUpdateVersion())) {
            filteMap.put(ColumnConfigFilterKey.UPDATE_VERSION.getCode(), param.getUpdateVersion());
        }
        if(StringUtils.isNotEmpty(param.getTabVersion())) {
            filteMap.put(ColumnConfigFilterKey.TAB_VERSION.getCode(), param.getTabVersion());
        }
        if (Objects.equals(param.getPageId(), 327L)) {
            List<Long> companyIdList = ArrayUtils.toLongList(config.getProperty("new_package_item", ""));
            filteMap.put(ColumnConfigFilterKey.OPEN_NEW_PACKAGE.getCode(),
                    CollectionUtils.isNotEmpty(companyIdList) && companyIdList.contains(staff.getCompanyId()) ? "1" : "0");
        }
        Iterator<PageColumnConf> iterator = pageColumnConfList.iterator();
        while(iterator.hasNext()) {
            PageColumnConf pageColumnConf = iterator.next();
            if (StringUtils.isEmpty(pageColumnConf.getFilterConfig())) {
                continue;
            }
            JSONObject pageConfigObj = JSONObject.parseObject(pageColumnConf.getFilterConfig());
            for(String key : pageConfigObj.keySet()) {
                String valueStr = pageConfigObj.getString(key);
                String filterValue = filteMap.get(key);
                // filterValue 为空无法判断,不进行过滤; valueStr 为空 所有都允许,不进行过滤; 只有两者都不为空,不包含时过滤掉;
                if(StringUtils.isNotEmpty(valueStr) && StringUtils.isNotEmpty(filterValue) && !valueStr.contains(filterValue)) {
                    iterator.remove();
                }
            }
        }
        return pageColumnConfList;
    }

    /**
     * 更新用户自定义列配置信息
     *
     * @param staff          staff
     * @param pageId         pageId
     * @param columnConfList columnConfList
     */
    @Override
    @CacheEvict(value = "defaultCache", key = "'column_conf_'+ #staff.companyId + '_' + #staff.id + '_' +  #pageId ")
    public void updateColumnConfig(Staff staff, long pageId, List<ColumnConf> columnConfList) {
        columnConfigDao.deleteUserColumnConfByPageId(staff, pageId);
        if (CollectionUtils.isEmpty(columnConfList)) {
            return;
        }

        List<UserColumnConf> userColumnConfList = Lists.newArrayList();
        for (int i = 0; i < columnConfList.size(); i++) {
            ColumnConf columnConf = columnConfList.get(i);
            UserColumnConf userColumnConf = new UserColumnConf();
            userColumnConf.setCompanyId(staff.getCompanyId());
            userColumnConf.setStaffId(staff.getId());
            userColumnConf.setPageId(pageId);
            userColumnConf.setColId(columnConf.getColId());
            userColumnConf.setVisible(columnConf.getVisible());
            userColumnConf.setSortNo(i);
            userColumnConf.setWidth(columnConf.getWidth());
            userColumnConfList.add(userColumnConf);
        }
        columnConfigDao.insertUserColumnConfList(staff, userColumnConfList);
        //双写 全环境后去掉
        if ((pageId == PageColumnDataConfigConstants.PACK_ORDER_COLUMN_PAGE_ID || pageId == PageColumnDataConfigConstants.POST_ORDER_COLUMN_PAGE_ID) && CollectionUtils.isNotEmpty(userColumnConfList)) {
            updateDataConfigFromColumn(staff, pageId, userColumnConfList);
        }
    }

    @Override
    @CacheEvict(value = "defaultCache", key = "'column_conf_'+ #staff.companyId + '_' + #staff.id + '_' + #pageId ")
    public void updateUserColumnCustomConfig(Staff staff, long pageId, ColumnConf columnConf) {
        if (columnConf == null || columnConf.getConfig() == null || columnConf.getColId() == null) {
            throw new IllegalArgumentException("参数错误");
        }
        UserColumnCustomConfig update = new UserColumnCustomConfig();
        update.setColId(columnConf.getColId());
        update.setCompanyId(staff.getCompanyId());
        update.setPageId(pageId);
        update.setStaffId(staff.getId());
        update.setConfig(columnConf.getConfig() == null ? null : columnConf.getConfig().toJSONString());
        columnConfigDao.insertUserColumnCustomConfig(staff, update);
    }

    @Override
    @CacheEvict(value = "defaultCache", key = "'column_conf_'+ #staff.companyId + '_' + #staff.id + '_' + #pageId ")
    public void updateColumnConfigToDefault(Staff staff, long pageId) {
        columnConfigDao.deleteUserColumnConfByPageId(staff, pageId);
        //这两个列配置双写 后面全环境后可以去除
        if (pageId == PageColumnDataConfigConstants.PACK_ORDER_COLUMN_PAGE_ID) {
            userDataConfigService.updateDataConfigToDefault(staff, PageColumnDataConfigConstants.PACK_ORDER_DATA_PAGE_ID);
        }
        if (pageId == PageColumnDataConfigConstants.POST_ORDER_COLUMN_PAGE_ID) {
            userDataConfigService.updateDataConfigToDefault(staff, PageColumnDataConfigConstants.POST_ORDER_DATA_PAGE_ID);
        }
    }

    @Override
    public void updateDeFaultColumn(Staff staff, PageColumnConf columnConf) {
        if (columnConf == null) {
            return;
        }
        if (columnConf.getId() == null) {
            //新增
            List<PageColumnConf> pageColumnConfList = columnConfigDao.getPageColumnConfList(columnConf.getPageId(), Arrays.stream(ProjectContextEnum.values()).map(ProjectContextEnum::getValue).collect(Collectors.toList()));
            if (pageColumnConfList.size() > 0) {
                List<PageColumnConf> origins = pageColumnConfList.stream()
                        .filter(origin -> origin.getColCode().equals(columnConf.getColCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(origins)) {
                    throw new IllegalArgumentException("列配置重复");
                }
            }
        }
        columnConfigDao.insertOrUpdateColumnConf(staff, columnConf);
    }

    @Override
    public void repairUpdateColumnConfEnv(PageColumnConfUpdateParam param) {
        columnConfigDao.updateColumnConfEnv(param);
    }

    @Override
    public void createTempConf(PageColumnConfUpdateParam condition) {
        /*if (StringUtils.isBlank(condition.getDbNo())) {
            LocalDate date = LocalDate.now();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy_MM_dd");
            String dateNo = date.format(fmt);
            condition.setDbNo(dateNo);
        }*/
//        columnConfigDao.createTempConf(condition);
        columnConfigDao.insertTempConf(condition);
    }

    private static String buildKey(Long companyId, Long colId, long pageId) {
        return String.join("_", String.valueOf(companyId), String.valueOf(colId), String.valueOf(pageId));
    }

    @Override
    public boolean checkColumn(Staff staff,Long pageId,String title,String field) {
        if(pageId == null){
            return true;
        }
        ColumnConfListWrapper columnConfigList =getColumnConfigList(staff, pageId);
        Assert.notNull(columnConfigList, "没有找到列配置数据,pageId="+pageId);
        List<ColumnConf> columnConfList = columnConfigList.getColumnConfList();
        for(ColumnConf conf : columnConfList) {
            if(conf.getVisible().equals(1)) {
                if(StringUtils.isNoneBlank(title) && conf.getTitle().equals(title)){
                    return true;
                }
                if(StringUtils.isNoneBlank(field) && conf.getField().equals(field)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 解决数据库新增默认页配置的扩展配置，程序走用户自定义配置，两个配置不同步的情况
     */
    public void checkUserConfig(Map<String, UserColumnCustomConfig> customConfigMap,Map<Long, PageColumnConf> pageColumnConfMap,Staff staff){
        for(Long colId:pageColumnConfMap.keySet()) {
            PageColumnConf pageColumnConf = pageColumnConfMap.get(colId);
            if(!StringUtils.isEmpty(pageColumnConf.getConfig())) {
                // 从页面默认配置中组合出键
                String key = buildKey(staff.getCompanyId(),colId,pageColumnConf.getPageId());
                if(customConfigMap.get(key) != null) {
                    // 用户有自定义配置就检测默认配置的扩展配置（config）中是否有新增配置
                    String userConfig = customConfigMap.get(key).getConfig();
                    String pageConfig = pageColumnConf.getConfig();
                    JSONObject pageConfigObj = JSONObject.parseObject(pageConfig);
                    JSONObject userConfigObj = JSONObject.parseObject(userConfig);
                    // 比较两个config的键的数量，用户config少了就加
                    for(String key2 :pageConfigObj.keySet()) {
                        if(StringUtils.isEmpty(userConfigObj.getString(key2))) {
                            userConfigObj.put(key2,pageConfigObj.getString(key2));
                        }
                    }
                    // 比较两个config的键的数量，pageConfig中没有，用户config删除
                    userConfigObj.keySet().removeIf(key2 -> StringUtils.isEmpty(pageConfigObj.getString(key2)));
                    customConfigMap.get(key).setConfig(userConfigObj.toJSONString());
                }else {
                    // 用户没有自定义扩展配置就添加
                    UserColumnCustomConfig userColumnCustomConfig = new UserColumnCustomConfig();
                    userColumnCustomConfig.setConfig(pageColumnConf.getConfig());
                    userColumnCustomConfig.setColId(pageColumnConf.getPageId());
                    userColumnCustomConfig.setCompanyId(staff.getCompanyId());
                    customConfigMap.put(key,userColumnCustomConfig);
                }
            }
        }
    }

    /**
     * 根据权限过滤【子商品信息】列配置
     * @param staff
     * @param columnConfList
     */
    private void filterSubItemInfoColumn(Staff staff, List<ColumnConf> columnConfList) {
        String dataPrivilegeSetting = staff.getPowerDataPrivilegeSettings();
        if (StringUtils.isEmpty(dataPrivilegeSetting)) {
            return;
        }

        Set<String> dataPrivilegeSettingSet = Stream.of(dataPrivilegeSetting.split(",")).filter(s -> StringUtils.isNotEmpty(s)).collect(Collectors.toSet());
        if (dataPrivilegeSettingSet.contains(DataPrivilegeIds.SUB_ITEM_INFO)) {
            return;
        }

        Iterator<ColumnConf> iterator = columnConfList.iterator();
        while (iterator.hasNext()) {
            ColumnConf columnConf = iterator.next();
            if (columnConf.getField().equals("subItem")) {
                iterator.remove();
                break;
            }
        }
    }

    private List<UserColumnConf> convertFromUserDataConfig(Staff staff, long pageId) {
        if (PageColumnDataConfigConstants.pageIdMap.get(pageId) == null) {
            return Lists.newArrayList();
        }
        List<UserDataConfig> userDataConfigs = userDataConfigDAO.getUserDataConfigs(staff, (Long) PageColumnDataConfigConstants.pageIdMap.get(pageId));
        if (CollectionUtils.isEmpty(userDataConfigs)) {
            return Lists.newArrayList();
        }
        List<UserColumnConf> userColumnConfList = new ArrayList<>();
        for (UserDataConfig userDataConfig : userDataConfigs) {
            if (PageColumnDataConfigConstants.columnIdMap.getKey(userDataConfig.getDataId()) == null || PageColumnDataConfigConstants.pageIdMap.getKey(userDataConfig.getPageId()) == null) {
                continue;
            }
            UserColumnConf userColumnConf = new UserColumnConf();
            userColumnConf.setCompanyId(userDataConfig.getCompanyId());
            userColumnConf.setVisible(userDataConfig.getVisible());
            userColumnConf.setSortNo(userDataConfig.getSortNo());
            userColumnConf.setStaffId(userDataConfig.getStaffId());
            userColumnConf.setWidth(PageColumnDataConfigConstants.DEFAULT_WIDTH);
            userColumnConf.setPageId((Long) PageColumnDataConfigConstants.pageIdMap.getKey(userDataConfig.getPageId()));
            userColumnConf.setColId((Long) PageColumnDataConfigConstants.columnIdMap.getKey(userDataConfig.getDataId()));
            userColumnConfList.add(userColumnConf);
        }
        userColumnConfList.sort(Comparator.comparing(UserColumnConf::getSortNo));
        return userColumnConfList;
    }

    private void updateDataConfigFromColumn(Staff staff, long pageId, List<UserColumnConf> userColumnConfList) {
        if (PageColumnDataConfigConstants.pageIdMap.get(pageId) == null) {
            return;
        }
        List<UserDataConfigDTO> userDataConfigDTOS = new ArrayList<>();
        for (UserColumnConf userColumnConf : userColumnConfList) {
            if (PageColumnDataConfigConstants.columnIdMap.get(userColumnConf.getColId()) == null || PageColumnDataConfigConstants.pageIdMap.get(userColumnConf.getPageId()) == null) {
                continue;
            }
            UserDataConfigDTO userDataConfigDTO = new UserDataConfigDTO();
            userDataConfigDTO.setDataId((Long) PageColumnDataConfigConstants.columnIdMap.get(userColumnConf.getColId()));
            userDataConfigDTO.setSortNo(userColumnConf.getSortNo());
            userDataConfigDTO.setVisible(userColumnConf.getVisible());
            userDataConfigDTOS.add(userDataConfigDTO);
        }
        if (CollectionUtils.isNotEmpty(userDataConfigDTOS)) {
            userDataConfigService.updateUserDataConfig(staff, (Long) PageColumnDataConfigConstants.pageIdMap.get(pageId), userDataConfigDTOS);
        }
    }
}

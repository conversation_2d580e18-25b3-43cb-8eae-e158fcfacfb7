package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.common.SysTradeQimenBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.common.TbAppInfo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-0903 17:13
 * @Description 奇门业务处理
 */
@Service
public class SysTradeQimenService extends SysTradeQimenBusiness implements ISysTradeQimenService {

    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IWarehouseService warehouseService;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    private ITradeUpdateService tradeUpdateService;
    private final Logger logger = Logger.getLogger(this.getClass());


    /**
     * 奇门催发货
     * @return
     */
    @Override
    public JSONObject priorityConsign(String body) {
        try {
            return priorityConsign(JSON.parseObject(body));
        } catch (Exception e) {
            logger.error(String.format("msg=%s]",e.getMessage()));
            return TradeQimenUtils.buildResultObject(false, "sign-check-failure", "search-logisticTime.-failed!",null);
        }
    }

    @Override
    public JSONObject priorityConsignTest(JSONObject jsonObject) {
        try {
            return priorityConsign(jsonObject);
        } catch (Exception e) {
            logger.error(String.format("msg=%s]",e.getMessage()));
            return TradeQimenUtils.buildResultObject(false, "sign-check-failure", "search-logisticTime.-failed!",null);
        }
    }

    private JSONObject priorityConsign(JSONObject jsonObject){
        //业务逻辑，标记订单，返回预计发货时间，不同店铺不同订单
        String sellerNick = jsonObject.getString("sellerNick");
        String mainOrderId = jsonObject.getString("mainOrderId");
        String customerTime = jsonObject.getString("customerTime");
        User user = getUserByNick(sellerNick);
        if (user == null) {
            logger.error(String.format("ERP中店铺不存在,订单=%s,店铺=%s,催发货时间=%s]", mainOrderId,sellerNick,customerTime));
            return TradeQimenUtils.buildResultObject(true, "modify-user-failure", "ERP中不存在对应卖家!",null);
        }
        Staff staff = buildSysStaff(user);
        if (user.getActive() != null && user.getActive() - 1 != 0) {
            logger.error(LogHelper.buildLog(staff, String.format("ERP中不是活跃用户,订单=%s,店铺=%s,催发货时间=%s]", mainOrderId,sellerNick,customerTime)));
            return TradeQimenUtils.buildResultObject(true, "not-active-failure", "非活跃用户!",null);
        }

        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, false, mainOrderId);

        if (CollectionUtils.isEmpty(tbTrades)) {
            logger.error(LogHelper.buildLog(staff, String.format("ERP中没有此订单,订单=%s,店铺=%s,催发货时间=%s]", mainOrderId,sellerNick,customerTime)));
            return TradeQimenUtils.buildResultObject(true, "not-order-failure", "没有此订单!",null);
        }

        List<TbTrade> needTagTrades = tbTrades.stream().filter(tbTrade -> !TradeStatusUtils.isAfterSendGoods(tbTrade.getSysStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needTagTrades)) {
            logger.error(LogHelper.buildLog(staff, String.format("已发货订单,订单=%s,店铺=%s,催发货时间=%s]", mainOrderId,sellerNick,customerTime)));
            return TradeQimenUtils.buildResultObject(true, "not-order-failure", "已发货订单!",null);
        }
        TbTrade tbTrade = needTagTrades.get(0);
        Warehouse warehouse = warehouseService.queryById(tbTrade.getWarehouseId());
        if (null == warehouse) {
            logger.error(LogHelper.buildLog(staff, String.format("ERP中没有此仓库,订单=%s,店铺=%s,warehouseId=%s]", mainOrderId,sellerNick,tbTrade.getWarehouseId())));
            return TradeQimenUtils.buildResultObject(true, "not-warehouse-failure", "没有此仓库!",null);
        }

        String logisticTime = TradeQimenUtils.checkConsignTime(warehouse, LocalDateTime.ofInstant(tbTrade.getPayTime().toInstant(), ZoneId.systemDefault()));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("订单=%s,店铺=%s,warehouseId=%s,lastPayTime=%s,lastDeliveryTime=%s,logisticTime=%s]", mainOrderId,sellerNick,tbTrade.getWarehouseId(),warehouse.getLastPayTime(),warehouse.getLastDeliveryTime(),logisticTime)));
        }
        JSONObject logisticResult = new JSONObject();
        logisticResult.put("processCode",TradeQimenUtils.getProcessCode(tbTrade,tradeConfigService.get(staff)));
        logisticResult.put("logisticTime",logisticTime);
        logisticResult.put("mainOrderId",mainOrderId);
        logisticResult.put("sellerNick",sellerNick);
        //发送本地事件
        eventCenter.fireEvent(this, new EventInfo("trade.qimen.priorityconsign").setArgs(new Object[]{staff,TradeUtils.toSidList(needTagTrades)}), null);
        return TradeQimenUtils.buildResultObject(true, 0, "",logisticResult);
    }


}

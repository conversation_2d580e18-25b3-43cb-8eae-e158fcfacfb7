package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.TimeTypeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
public  abstract class AbsConditionConverter implements IConditionConverter{


    @Override
    public void convert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if (needConvert(staff, context, condition, q)){
            doConvert(staff, context, condition, q);
        }
    }

    public boolean needConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query){
        if (query.isStopQuery()) {
            return false;
        }
        return isNeedConvert(staff, context, request, query);
    }

    /**
     * 是否需要处理
     * @param staff
     * @param context
     * @param request
     * @param query
     * @return
     */
     abstract boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q);

    /**
     * 条件转换
     * @param staff
     * @param context
     * @param request
     * @param query
     * @return
     */
     abstract void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q);


}

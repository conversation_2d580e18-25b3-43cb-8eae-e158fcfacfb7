package com.raycloud.erp.trade.search.db;

import com.raycloud.dmj.dao.JdbcQueryDao;
import com.raycloud.dmj.dao.trade.TradeItemExcepDao;
import com.raycloud.dmj.dao.trade.TradeSeparationDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by windy26205 on 19/8/6.
 */
@Service
public class TradeItemExcepBusiness {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    JdbcQueryDao jdbcQueryDao;
    @Resource
    TradeItemExcepDao tradeItemExcepDao;
    @Resource
    TradeConfigService tradeConfigService;

    public long migrate(Staff staff) {
        Query q = new Query();
        q.append("SELECT ").append(TradeItemExcepDao.TRADE_FIELDS).append(" FROM trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status > 0  order by sid asc LIMIT ?, ?").add(staff.getCompanyId());
        Page page = new Page().setPageSize(1000).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        List<TbTrade> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbTrade.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            tradeItemExcepDao.update(staff, list, true);
            r += list.size();
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("用户%s修复itemExcep中,已修复%s", staff.getCompanyId(), r)));
            }
            if (list.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
            q.getArgs().set(q.getArgs().size() - 2, page.getStartRow());
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        tradeConfig.setItemExcepOpen(1);
        tradeConfigService.update(staff, "item_excep_open", 1);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("用户%s switch itemExcepOpen数据成功,itemExcepOpenk开启", staff.getCompanyId())));
        }
        return r;
    }

}



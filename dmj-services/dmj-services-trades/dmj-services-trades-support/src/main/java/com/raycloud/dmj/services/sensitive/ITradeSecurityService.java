package com.raycloud.dmj.services.sensitive;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description 订单安全相关操作
 * @date 2022-01-19
 */
public interface ITradeSecurityService {

    /**
     * 对订单进行解密
     */
    void doSensitiveTrades(Staff staff, List<Trade> trades);

    default void sensitiveTrades(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        doSensitiveTrades(staff, filterTrades(staff, trades));
    }

    /**
     * 过滤出需要脱敏的订单
     */
    List<Trade> filterTrades(Staff staff, List<Trade> trades);
}
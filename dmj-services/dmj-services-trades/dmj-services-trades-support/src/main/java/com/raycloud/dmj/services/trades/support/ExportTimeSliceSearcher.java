package com.raycloud.dmj.services.trades.support;

import com.google.api.client.util.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.trades.ExportParams;
import com.raycloud.dmj.domain.trades.TimeSlice;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public abstract class ExportTimeSliceSearcher<T> {

    public List<T> search(ExportParams params){
        if (params.isFirstFilter()){
            filter(params);
            params.setFirstFilter(false);
        }
        List<TimeSlice> timeSlices = params.getTimeSlices();

        Iterator<TimeSlice> iterator = timeSlices.iterator();
        while (iterator.hasNext()) {
            TimeSlice ts = iterator.next();
            Page lastPage = params.getLastPage();
            if (lastPage == null){
                lastPage = new Page();
                lastPage.setPageSize(200);
            } else {
                lastPage.setPageNo(lastPage.getPageNo() + 1);
            }
            List<T> result = query(ts.getStart(), ts.getEnd(), lastPage);
            if (CollectionUtils.isEmpty(result)){
                Logs.debug(ts +"--" + lastPage.getPageNo() + "没有查询到数据清除上次时间片重新查询");
                //清除上次查询page
                params.setLastPage(null);
                iterator.remove();
            } else {
//                if ( result.size() < lastPage.getPageSize()){
//                    Logs.debug(ts +"--查询到数据size:" + result.size() + "<pageSize,下一页无数据，清除当前时间片");
//                    params.setLastPage(null);
//                    iterator.remove();
//                } else {
                    Logs.debug(ts +"--" + lastPage.getPageNo() + "查询到数据size:"+ result.size());
                    //记录本次查询 page
                    params.setLastPage(lastPage);
//                }
                return result;
            }

        }
        return Lists.newArrayList();
    }

    protected void filter(ExportParams params){
        return;
    }

    protected abstract List<T> query(Date start, Date end, Page page);


}

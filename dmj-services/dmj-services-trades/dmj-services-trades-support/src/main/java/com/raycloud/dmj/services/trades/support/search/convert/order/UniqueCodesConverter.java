package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 * 商品唯一码
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_UNIQUECODES)
public class UniqueCodesConverter extends AbsOrderConditionConverter {

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return notEmpty(condition.getUniqueCodes());
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        boolean added = false;
        String table =  "wave_unique_code_" + staff.getDbInfo().getOrderDbNo();
        orderQry.append(" AND ").append("o.id in (select uc.order_id from %s uc where uc.company_id = ? and uc.enable_status = 1 ").add(staff.getCompanyId());
        andListCondition(orderQry,"uc.unique_code",condition.getUniqueCodes(),1);
        orderQry.add(")");

        return added;
    }

}




package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * AsyncOrderStockListener
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Component
@ListenerBind("trade.stock.async.opt")
public class AsyncOrderStockListener implements IEventListener {

    @Resource
    private OrderStockService orderStockService;


    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) source;
        Staff staff = evt.getArg(0, Staff.class);
        String type = evt.getArg(1, String.class);
        List<Order> orders = evt.getArgList(1, Order.class);
        try {
            orderStockService.resumeOrderStockLocalNoWms(staff, orders, null);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("异步操作库存失败 type:%s", type)), e);
        }
    }
}

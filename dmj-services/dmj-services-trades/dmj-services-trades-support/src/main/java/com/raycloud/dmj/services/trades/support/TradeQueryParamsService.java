package com.raycloud.dmj.services.trades.support;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import com.raycloud.dmj.dao.trade.TradeQueryParamsDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.services.trades.ITradeQueryParamsService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
public class TradeQueryParamsService implements ITradeQueryParamsService{

	@Resource
	private TradeQueryParamsDAO tradeQueryParamsDao;
	
	@Override
	public List<TradeQueryParams> list(Staff staff, Long queryId) {
		return tradeQueryParamsDao.queryByQueryId(staff, queryId);
	}

	@Override
	public List<TradeQueryParams> listGroup(Staff staff, Long queryId) {
		return tradeQueryParamsDao.queryGroupByQueryId(staff, queryId);
	}

	@Override
	public TradeQueryParams queryById(Staff staff, Long id) {
		return tradeQueryParamsDao.queryById(staff, id);
	}

	@Override
	public void insert(Staff staff, TradeQueryParams params) {
		tradeQueryParamsDao.insert(staff, params);
	}

	@Override
	public void update(Staff staff, TradeQueryParams params) {
		tradeQueryParamsDao.update(staff, params);
	}

	@Override
	public void delete(Staff staff, TradeQueryParams params) {
		tradeQueryParamsDao.delete(staff, params);
	}

	@Override
	@Transactional
	public void batchSave(Staff staff, List<TradeQueryParams> params) {
		if (CollectionUtils.isEmpty(params)) {
			return;
		}
		Long queryId = params.get(0).getQueryId();
		Assert.isTrue(queryId != null && queryId > 0 , "参数不正确");
		List<TradeQueryParams> exist = this.list(staff, queryId);
		Map<Long, TradeQueryParams> map = new HashMap<Long, TradeQueryParams>();
		if (exist != null) {
			for (TradeQueryParams tradeQueryParams : exist) {
				map.put(tradeQueryParams.getId(), tradeQueryParams);
			}
		}
		List<TradeQueryParams> needUpdate = new ArrayList<TradeQueryParams>();
		List<TradeQueryParams> needInsert = new ArrayList<TradeQueryParams>();
		List<TradeQueryParams> needDelete = new ArrayList<TradeQueryParams>();
		for (int i = 0; i < params.size(); i++) {
			TradeQueryParams param = params.get(i);
			param.setSortOrder(i);
			if (param.getId() == null) {
				needInsert.add(param);
			} else {
				needUpdate.add(param);
				map.remove(param.getId());
			}
		}

		if (MapUtils.isNotEmpty(map)) {
			needDelete.addAll(map.values());
		}
		batch(staff, needUpdate, needInsert, needDelete);

	}

	private void batch(Staff staff,List<TradeQueryParams> needUpdate, List<TradeQueryParams> needInsert, List<TradeQueryParams> needDelete){
		if (CollectionUtils.isNotEmpty(needDelete)){
			if (needUpdate == null) {
				needUpdate = new ArrayList<TradeQueryParams>();
			}
			for (TradeQueryParams tradeQueryParams : needDelete) {
				TradeQueryParams delete = new TradeQueryParams();
				delete.setId(delete.getId());
				delete.setEnableStatus(0);
				needUpdate.add(delete);
			}
		}
		if (CollectionUtils.isNotEmpty(needUpdate)){
			tradeQueryParamsDao.batchUpdate(staff, needUpdate);
		}
		if (CollectionUtils.isNotEmpty(needInsert)){
			tradeQueryParamsDao.batchInsert(staff, needInsert);
		}

	}

	@Override
	public List<TradeQueryParams> queryExcludeCurrentId(Staff staff, Long queryId, String name, Long id) {
		return tradeQueryParamsDao.queryExcludeCurrentId(staff,queryId,name,id);
	}

	@Override
	public List<TradeQueryParams> queryByGroupId(Staff staff, Long id) {
		return tradeQueryParamsDao.queryByGroupId(staff,id);
	}

	@Override
	public void exchangeGroup(Staff staff, Long conditionId, Long groupId) {
		tradeQueryParamsDao.exchangeGroup(staff, conditionId, groupId);
	}
}

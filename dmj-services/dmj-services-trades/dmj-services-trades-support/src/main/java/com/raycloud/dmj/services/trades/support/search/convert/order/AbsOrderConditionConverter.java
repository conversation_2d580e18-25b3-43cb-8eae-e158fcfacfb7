package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertBase;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;


/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-20
 */
public  abstract class AbsOrderConditionConverter implements IOrderConditionConverter {


    @Override
    public boolean convert(Staff staff, ConvertContext context, OrderRefCondition condition,  Query orderQry, Query hotQry) {
        if (needConvert(staff, context, condition,orderQry,hotQry)){
            return doConvert(staff, context, condition,orderQry,hotQry);
        }
        return false;
    }

    public boolean needConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry){
        if (condition == null) {
            return false;
        }
        if (orderQry.isStopQuery()) {
            return false;
        }
        return isNeedConvert(staff, context, condition,orderQry,hotQry);
    }



    abstract boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry);

    abstract boolean doConvert(Staff staff, ConvertContext context,OrderRefCondition condition, Query orderQry, Query hotQry);
}

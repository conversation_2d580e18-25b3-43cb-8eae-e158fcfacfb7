package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.*;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.gift.GiftMatchManualBusiness;
import com.raycloud.dmj.business.logistics.UploadPackagesNoticeBusiness;
import com.raycloud.dmj.business.modify.*;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.stock.StockSwapBusiness;
import com.raycloud.dmj.domain.pt.params.MultiPacksPrintTradeLogDetailParams;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.business.wms.TradeWmsBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.vo.PrintTradeLogDetailVO;
import com.raycloud.dmj.domain.trade.memo.*;
import com.raycloud.dmj.domain.trade.notify.TradeNotifyConstant;
import com.raycloud.dmj.domain.trade.order.SuitToSingleContext;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.CancelData;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.params.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.express.api.IPrintExpressTemplateService;
import com.raycloud.dmj.express.response.template.ExpressCompanyDTO;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.*;
import com.raycloud.dmj.item.search.request.*;
import com.raycloud.dmj.item.search.response.QueryByItemIdInfoListResponse;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.impl.LockService;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformTradeAccess;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.utils.TradeWeighUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.EventFireSplitTradeUtils;
import com.raycloud.ec.api.*;
import com.raycloud.erp.buffer.model.BufferRequest;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单系统业务服务
 *
 * <AUTHOR>
 */
@Service
public class SysTradeService implements ISysTradeService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private SysMemoLogDao sysMemoLogDao;

    @Resource
    TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;
    @Resource
    private UpdateInvoiceNameBusiness updateInvoiceNameBusiness;
    @Resource
    private ScalpBusiness scalpBusiness;

    @Resource
    private CancelBusiness cancelBusiness;

    @Resource
    private CancelUndoBusiness cancelUndoBusiness;

    @Resource
    private DeleteOutBusiness deleteOutBusiness;

    @Resource
    private HaltBusiness haltBusiness;

    @Resource
    private UrgentBusiness urgentBusiness;
    @Resource
    private PackBusiness packBusiness;

    @Resource
    private PackWaveBusiness packWaveBusiness;

    @Resource
    private WeightBusiness weightBusiness;

    @Resource
    private UpdateBoxingBusiness updateBoxingBusiness;

    @Resource
    private UpdatePostfeeBusiness updatePostfeeBusiness;

    @Resource
    private PickingBusiness pickingBusiness;

    @Resource
    private SyncTradeBusiness syncTradeBusiness;

    @Resource
    private AddressBusiness addressBusiness;

    @Resource
    private LogisticBusiness logisticBusiness;

    @Resource
    private PrintBusiness printBusiness;

    @Resource
    private CopyBusiness copyBusiness;

    @Resource
    private ExportBusiness exportBusiness;

    @Resource
    private TradeExportBusiness tradeExportBusiness;

    @Resource
    private TbTradeDao tbTradeDao;

    @Resource
    private ITradePtService tradePtService;

    @Resource
    private TbOrderDAO tbOrderDAO;

    @Resource
    private IOrderStockService orderStockService;

    @Resource
    TradeWmsBusiness tradeWmsBusiness;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private RefuseRefundBusiness refuseRefundBusiness;

    @Resource
    StockSwapBusiness stockSwapBusiness;

    @Resource
    private TradeAddBusiness tradeAddBusiness;

    @Resource
    private TradeUpdateBusiness tradeUpdateBusiness;

    @Resource
    private TradeGiftUpdateBusiness tradeGiftUpdateBusiness;

    @Resource
    private SuitSwitchBusiness suitSwitchBusiness;

    @Resource
    private SuitSwitchInsufficientBusiness suitSwitchInsufficientBusiness;

    @Resource
    private PackSplitBusiness packSplitBusiness;

    @Resource
    private TradePackSplitDao tradePackSplitDao;

    @Resource
    private GiftMatchManualBusiness giftMatchManualBusiness;

    @Resource
    private IFreightTemplateService freightTemplateService;

    @Resource
    private IFreightExpressService freightExpressService;

    @Resource
    private IFreightTemplateVolumeService freightTemplateVolumeService;

    @Resource
    private IFastSysMemoService fastSysMemoService;

    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    IOpLogService opLogService;
    @Resource
    private IProgressService progressService;
    @Resource
    private IPrintExpressTemplateService printExpressTemplateService;

    @Resource
    private IIndexDubboService indexDubboService;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;



    private static final Integer PAGE_SIZE = 200;

    private static final SimplePropertyPreFilter DMJ_ITEM_SIMPLE_PROPERTY_PRE_FILTER = new SimplePropertyPreFilter("dmjItemList", "errorCode", "errorMsg", "success", "outerId", "skuOuterId", "itemSupplierBridgeList", "supplierItemOuterId", "sysItemId", "sysSkuId");


    @Resource
    TradeAddressReachableBusiness tradeAddressReachableBusiness;

    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    private ITradeTraceService tradeTraceService;

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;
    @Resource
    private ITradeConfigService tradeConfigService;
    @Resource
    private TradeValidateBusiness tradeValidateBusiness;
    @Resource
    PlatformManagement platformManagement;
    @Resource
    private TradeSysLabelBusiness tradeTagUpdateBusiness;
    @Resource
    private IWmsService wmsService;

    @Resource
    private ITradeService tradeService;

    @Resource
    private TradeUpdateService tradeUpdateService;

    @Resource
    private ITradesCollarService tradesCollarService;

    @Resource
    private TradeSpiteReportBusiness tradeSpiteReportBusiness;
    @Resource
    UploadPackagesNoticeBusiness uploadPackagesNoticeBusiness;

    @Resource
    TradeDeclareDao tradeDeclareDao;
    @Resource
    IExchangeRateService exchangeRateService;
    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    IStaffService staffService;
    @Resource
    IBufferService bufferService;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    LockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    ITradeAuditService tradeAuditService;

    @Override
    public List<SysMemoLog> querySysMemoLogs(Staff staff, Long sid) {
        return sysMemoLogDao.queryBySid(staff, sid);
    }

    @Override
    public void updateTrade4Party3(Staff staff, List<Trade> trades) {
        //这里要判断一下订单的快递模板是否有变更，有的话要打个系统标签
        List<Trade> needTagTrades = null;
        try {
            needTagTrades = addTemplateIdChangedTag(staff, trades);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("三方仓模板变更标记失败"), e);
        }
        tradePtService.saveByTrades(staff, trades);
        tbTradeDao.batchUpdateParty3(staff, trades);
        if (!CollectionUtils.isEmpty(needTagTrades)) {
            tradeTraceBusiness.asyncTrace(staff, needTagTrades, OpEnum.TAG_UPDATE);
        }
    }

    @Override
    public List<Trade> addTemplateIdChangedTag(Staff staff, List<Trade> trades) {
        Assert.notEmpty(trades, "三方仓模板变更标记时,订单为空，标记失败");
        List<Trade> list = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, trades.stream().map(Trade::getSid).toArray(Long[]::new));
        Assert.notEmpty(list, "三方仓模板变更标记时，找不到原订单，标记失败,sids:" + trades.stream().map(Trade::getSid).collect(Collectors.toList()));
        List<Trade> needTagTrades = new ArrayList<>();
        List<Long> templateIds = new ArrayList<>();
        Set<Long> oriTemplateIds = list.stream().map(Trade::getTemplateId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(oriTemplateIds)) {
            // 原始订单模板ID为空 没有选择快递模板的 不用标记换快递标签 直接返回
            return needTagTrades;
        }
        templateIds.addAll(oriTemplateIds);
        templateIds.addAll(trades.stream().map(Trade::getTemplateId).collect(Collectors.toSet()));
        Map<Long, ExpressCompanyDTO> expressCompanyDTOMap = printExpressTemplateService.queryExpressCompanyMapByTemplateIds(staff, templateIds);
        Map<Long, Trade> map = list.stream().collect(Collectors.toMap(Trade::getSid, o -> o, (k1, k2) -> k1));
        trades.forEach(trade -> {
            Trade t = map.get(trade.getSid());
            if (t != null) {
                ExpressCompanyDTO newTemplate = expressCompanyDTOMap.get(trade.getTemplateId());
                ExpressCompanyDTO originTemplate = expressCompanyDTOMap.get(t.getTemplateId());
                if (Objects.nonNull(newTemplate) && Objects.nonNull(originTemplate) && !newTemplate.getCode().equals(originTemplate.getCode())) {
                    trade.setTagIds(t.getTagIds());
                    needTagTrades.add(trade);
                }
            }
        });
        tradeTagUpdateBusiness.addTags(staff, needTagTrades, OpEnum.TAG_UPDATE, Lists.newArrayList(SystemTags.TAG_PARTY3_TEMPLATE_CHANGED));
        return needTagTrades;
    }

    @Override
    public void updateInvoiceName(Staff staff, Trade trade) {
        updateInvoiceNameBusiness.updateInvoiceName(staff, trade);
    }

    @Override
    public void scalp(Staff staff, boolean isCancel, Long... sids) {
        List<Trade> updateTrades = scalpBusiness.scalp(staff, isCancel, sids);
        tradeTraceBusiness.asyncTrace(staff, updateTrades, isCancel ? OpEnum.SCALP_CANCEL : OpEnum.SCALP);
    }

    @Override
    public void cainiaoConvert(Staff staff, Long... sids) {
        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            List<Trade> trades = tradeSearchService.queryBySids(staff, false, sids);
            handleCainiaoConvert(staff, trades);
            return null;
        });
    }


    public void handleCainiaoConvert(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> updateTrades = new ArrayList<>();
        for (Trade trade : trades) {
            //非菜鸟仓自流转-自发的过滤
            if (!TradeUtils.isCainiaoWarehouseTrade(trade) && !TradeUtils.isCainiaoWarehouseZfTrade(trade)) {
                continue;
            }
            if (TradeUtils.isAfterSendGoods(trade)) {
                continue;
            }
            Trade toUpdate = new TbTrade();
            toUpdate.setSid(trade.getSid());
            toUpdate.setTid(trade.getTid());
            toUpdate.setUserId(trade.getUserId());
            toUpdate.setCompanyId(trade.getCompanyId());
            toUpdate.setTagIds(trade.getTagIds());
            toUpdate.setSubSource(trade.getSubSource());
            toUpdate.setSource(trade.getSource());
            TradeUtils.cainiaoWarehouseConvert(toUpdate);
            updateTrades.add(toUpdate);
        }
        tradeSysLabelBusiness.matchSystemLabels(staff, updateTrades, Lists.newArrayList(SystemTags.CAINIAO_WAREHOUSE, SystemTags.CAINIAO_WAREHOUSE_ZF), false, false, true);
        tradeUpdateService.updateTrades(staff, updateTrades);
        tradeTraceBusiness.asyncTrace(staff, updateTrades, OpEnum.TAG_UPDATE);
    }


    @Override
    public List<TradeResult> scalp(Staff staff, String field, String... values) {
        return scalpBusiness.scalp(staff, field, values);
    }

    @Override
    public List<TradeResult> scalp(Staff staff, String field, boolean containAudit, boolean needHalt, String... values) {
        return scalpBusiness.scalp(staff, field, containAudit, needHalt, values);
    }

    @Override
    public List<Trade> cancel(Staff staff, Long[] sids) {
        return cancelBusiness.cancel(staff, sids, null, 0);
    }

    @Override
    public Map<String, String> checkTradeCancelByFx(Staff staff, Long... sids) {
        return cancelBusiness.checkTradeCancelByFx(staff, sids, null, 1);
    }

    @Override
    public CancelResult cancelTrades(Staff staff, Long[] sids) {
        CancelData cancelData = CancelData.builder()
                .staff(staff).sidArr(sids)
                .gxTrades(null).isCancelAttr(0)
                .ifBatch(false)
                .build();
        return cancelBusiness.cancelTrades(cancelData);
    }

    @Override
    public Map<String, String> uncancel(Staff staff, Long[] sids, boolean download) {
        return cancelUndoBusiness.uncancelMap(staff, sids, download, 0);
    }

    @Override
    public void delete(Staff staff, Long[] sids) {
        deleteOutBusiness.delete(staff, sids);
    }

    @Override
    public void deleteTradesByCondition(Staff staff, TradeDeleteParams params) {
        Assert.isTrue(params.getUserId() != null || params.getWarehouseId() != null, "请传入删除参数");
        Page page = new Page().setPageSize(100).setPageNo(1);
        Query q = new Query().append("company_id = ? ").add(staff.getCompanyId());
        if (params.getUserId() != null) {
            q.append(" and sys_status in ('CLOSED','FINISHED','FINISHED_AUDIT','SELLER_SEND_GOODS','WAIT_AUDIT','WAIT_BUYER_PAY','WAIT_FINANCE_AUDIT') and user_id = ? ").add(params.getUserId());
        }
        if (params.getWarehouseId() != null) {
            q.append(" and warehouse_id = ? ").add(params.getWarehouseId());
        }
        q.append(" and enable_status > 0 ");
        List<TbTrade> trades;
        while (!(trades = tbTradeDao.queryTrades(staff, null, q, page, false, false)).isEmpty()) {
            deleteTrades(staff, trades);
        }
    }

    @Override
    public void deleteTrades(Staff staff, List<TbTrade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> deleteList = new ArrayList<>(trades.size());
        List<Long> mergeSidList = trades.stream().filter(TradeUtils::isMerge).map(TbTrade::getMergeSid).collect(Collectors.toList());
        //合单情况下所有子单都需要删除
        if (!CollectionUtils.isEmpty(mergeSidList)) {
            List<TbTrade> mergeTradeList = tbTradeDao.queryByMergeSids(staff, mergeSidList.toArray(new Long[0]));
            List<Long> sidList = trades.stream().map(TbTrade::getSid).collect(Collectors.toList());
            for (TbTrade mergeTrade : mergeTradeList) {
                if (!sidList.contains(mergeTrade.getSid())) {
                    trades.add(mergeTrade);
                }
            }
        }
        List<Long> unAuditSidList = trades.stream().filter(trade -> Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())).map(TbTrade::getSid).collect(Collectors.toList());
        //已审核情况下需要走一下反审核再删除
        if (!CollectionUtils.isEmpty(unAuditSidList)) {
            tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_DELETE, unAuditSidList.toArray(new Long[0]));
        }
        trades.forEach(trade -> {
            Trade toDelete = new TbTrade();
            toDelete.setSid(trade.getSid());
            toDelete.setCompanyId(staff.getCompanyId());
            toDelete.setEnableStatus(0);
            deleteList.add(toDelete);
        });
        List<TbOrder> orders = tbOrderDAO.queryBySids(staff, TradeUtils.toSids(trades));
        tradeUpdateService.updateTrades(staff, deleteList);
        tbOrderDAO.deleteByIds(staff, orders);
        Map<Long, Long> sidToWarehouseIdMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getWarehouseId, (v1, v2) -> v2));
        List<Order> stockOrders = new ArrayList<>();
        for (TbOrder order : orders) {
            order.setWarehouseId(sidToWarehouseIdMap.get(order.getSid()));
            if (order.getWarehouseId() != null) {
                stockOrders.add(order);
            }
        }
        //这一步也会设置订单的warehouseid 但是因为设置时，订单已经删掉了 所以设置不进去
        List<Order> resumeOrders = OrderUtils.toTree(stockOrders);
        orderStockService.resumeOrderStockLocal(staff, resumeOrders, null);
        tradeWmsBusiness.sendResume(staff, TradeUtils.assemblyBySid(trades, resumeOrders), resumeOrders);
    }

    @Override
    public List<Trade> halt(Staff staff, Long[] sids, int isHalt) {
        List<Trade> updateTrades = halt(staff, sids, isHalt, isHalt == 1 ? OpEnum.HALT : OpEnum.HALT_CANCEL);
        return updateTrades;
    }

    @Override
    public List<Trade> halt(Staff staff, Long[] sids, int isHalt, OpEnum opEnum) {
        List<Trade> updateTrades = haltBusiness.halt(staff, sids, isHalt);
        tradeTraceBusiness.asyncTrace(staff, updateTrades, opEnum);
        return updateTrades;
    }

    @Override
    public List<Trade> haltWithHours(Staff staff, Long[] sids, int isHalt, int hours) {
        List<Trade> updateTrades = haltWithHours(staff, sids, isHalt, hours, OpEnum.HALT);
        return updateTrades;
    }

    @Override
    public List<Trade> haltWithHours(Staff staff, Long[] sids, int isHalt, int hours, OpEnum opEnum) {
        List<Trade> updateTrades = haltBusiness.haltWithHours(staff, sids, isHalt, hours);
        //需要输出日志 (挂起订单，指定挂起时长：10小时)
        updateTrades.stream().forEach(trade -> {
            StringBuilder opLog = new StringBuilder();
            opLog = opLog.append(opEnum.getName()).append(",指定挂起时长：").append(hours).append("小时");
            trade.getOperations().put(opEnum, opLog.toString());
        });
        tradeTraceBusiness.asyncTrace(staff, updateTrades, opEnum);
        return updateTrades;
    }

    @Override
    public void urgent(Staff staff, Long[] sids, int isUrgent, Map<String, String> result) {
        List<Trade> updateTrades = urgentBusiness.urgent(staff, sids, isUrgent, result);
        if (updateTrades.size() > 0) {
            tradeTraceBusiness.asyncTrace(staff, updateTrades, isUrgent == 1 ? OpEnum.URGENT : OpEnum.URGENT_CANCEL);
        }
    }

    @Override
    public void clearExpressTemplate(Staff staff, Long[] sids) {
        tradeUpdateBusiness.clearExpressTemplate(staff, sids);
    }

    @Override
    public void packTrades(Staff staff, Long[] sids, List<Order> orders, String clientIp) {
        packBusiness.packTrades(staff, sids, orders, null, clientIp);
    }

    @Override
    public void packTradesWithScanInfo(Staff staff, Long[] sids, List<Order> orders, List<TradePackScanInfo> packScanInfos, String clientIp) {
        TradePackParams params = new TradePackParams.Builder().sids(sids).orders(orders).packScanInfos(packScanInfos).isBind(false).clientIp(clientIp)
                .autoPack(true).builder();
        packBusiness.packTrades(staff, params);
    }

    @Override
    public void packTradesWithScanInfo(Staff staff, Long[] sids, List<Order> orders, List<TradePackScanInfo> packScanInfos, List<String> uniqueCodes, String clientIp) {
        TradePackParams params = new TradePackParams.Builder().sids(sids).orders(orders).packScanInfos(packScanInfos).uniqueCodes(uniqueCodes).isBind(true).clientIp(clientIp).builder();
        packBusiness.packTrades(staff, params);
    }

    @Override
    public void packTradesBatchByParams(Staff staff, TradePackParams params) {
        packBusiness.packTrades(staff, params);
    }

    @Override
    public void packTradesByParams(Staff staff, TradePackParams params) {
        params.setBind(true);
        //开启包装验货按套件维度，不支持强唯一码验证
        if (params.isSuitPack()) {
            params.setBind(false);
        }
        packBusiness.packTrades(staff, params);
    }

    @Override
    public void packTradesWithScanInfo(Staff staff, TradePackParams params) {
        if (!params.isCanForce() && CollectionUtils.isEmpty(params.getPackScanInfos())) {
            throw new IllegalArgumentException("不支持强制验货时,请传入商品扫描信息! ");
        }
        packBusiness.checkOrderItemNum(staff, params);
        packBusiness.packTrades(staff, params);
    }

    @Override
    public void packTradesWithPost(Staff staff, Long[] sids, List<String> uniqueCodes, String clientIp) {
        TradePackParams params = new TradePackParams.Builder().sids(sids).uniqueCodes(uniqueCodes).isBind(false).clientIp(clientIp).builder();
        packBusiness.packTrades(staff, params);
    }

    @Override
    public PackWaveResult packTradesWave(Staff staff, Long waveId, String clientIp) {
        return packWaveBusiness.packTradesWave(staff, waveId, clientIp);
    }

    @Override
    public PackWaveResult packTradesCombineParcel(Staff staff, Long combineParcelId, String clientIp) {
        return packWaveBusiness.packTradesCombineParcel(staff, combineParcelId, clientIp);
    }

    @Override
    public Map<String, String> batchWeigh(final Staff staff, Map<Long, Trade> sid_trade, String clientIp, Integer kind,Staff packStaff) {
        return weightBusiness.batchWeigh(staff, sid_trade, clientIp, kind,null,packStaff);
    }

    @Override
    public void weigh(TradeWeighParams tradeWeighParams) {
        weightBusiness.weigh(tradeWeighParams.getStaff(), tradeWeighParams.getSidTradeMap(), tradeWeighParams.getClientIp(), tradeWeighParams.getOuterId(), null,null);
    }

    @Override
    public CalculateFreightCostResult calculateFreightCostAndWeight(TradeWeightParams params) {
        CalculateFreightCostResult calculateFreightCostResult;
        if (StringUtils.isEmpty(params.getCost())) {
            calculateFreightCostResult = calculateFreightCost(new CalculateFreightCostParams.Builder().sid(params.getSid()).weight(params.getWeight()).staff(params.getStaff()).build());
            FreightCost freightCost = calculateFreightCostResult.getCost();
            if (!freightCost.isError()) {
                params.setCost(String.valueOf(freightCost.getCount()));
            } else {
                throw new IllegalArgumentException("称重时计算运费失败，" + freightCost.getReason());
            }
        } else {
            FreightCost freightCost = new FreightCost();
            freightCost.setSid(params.getSid());
            freightCost.setError(false);
            freightCost.setCount(Double.valueOf(params.getCost().trim()));
            calculateFreightCostResult = new CalculateFreightCostResult(params.getSid(), freightCost);
        }
        if (params.getScanType().equals(3)) {
            tradeNewWeight(params);
        } else {
            TradeWeighParams tradeWeighParams = new TradeWeighParams.Builder().staff(params.getStaff()).sids(params.getSid().toString()).weight(params.getWeight()).cost(params.getCost()).clientIp(params.getClientIp()).outerId(params.getOuterId()).build();
            weightBusiness.weigh(tradeWeighParams.getStaff(), tradeWeighParams.getSidTradeMap(), tradeWeighParams.getClientIp(), tradeWeighParams.getOuterId(), null,params.getPackmaOuterIds());
        }
        return calculateFreightCostResult;
    }

    @Override
    public void reWeightTrade(Staff staff, Long sid, Double weight, String actualPostFee, String outerId,String packmaOuterIds) {
        weightBusiness.reWeightTrade(staff, sid, weight, actualPostFee, outerId, null,packmaOuterIds);
    }

    @Override
    public void weighDeliveredTrade(Staff staff, Long sid, Double weight, String actualPostFee) {
        weightBusiness.weighDeliveredTrade(staff, sid, weight, actualPostFee, null);
    }

    @Override
    public List<BoxingListItem> updateBoxingList(Staff staff, TbTrade trade, boolean needToGenerateBn) {
        return updateBoxingBusiness.updateBoxing(staff, trade, needToGenerateBn);
    }

    @Override
    public Map<String, Object> updatePostfee(Staff staff, Long sid, String postfee) {
        Trade updateTrade = updatePostfeeBusiness.updatePostfee(staff, sid, postfee);
        List<Trade> trades = new ArrayList<Trade>();
        trades.add(updateTrade);
        //发送事件
        eventCenter.fireEvent(this, new EventInfo("trade.updatePostFee").setArgs(new Object[]{staff}), trades);
        //返回
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("postfee", updateTrade.getPostFee());
        result.put("payment", updateTrade.getPayment());
        return result;
    }

    @Override
    public void updateTheoryPostFee(Staff staff, Long sid, String theoryPostFee) {
        Trade updateTrade = updatePostfeeBusiness.updateTheoryPostFee(staff, sid, theoryPostFee);
        List<Trade> trades = new ArrayList<Trade>();
        trades.add(updateTrade);
        eventCenter.fireEvent(this, new EventInfo("trade.updateTheoryPostFee").setArgs(new Object[]{staff}), trades);
    }

    /**
     * sids,和查询条件
     *
     * @param staff
     * @param sids
     * @param params
     * @param actualPostFee
     */
    @Override
    public void updateActualPostFee(Staff staff, String sids, TradeQueryParams params, String actualPostFee) {
        if (StringUtils.isNotBlank(sids)) {
            Long[] sidList = (Long[]) ConvertUtils.convert(sids.split(","), Long.class);
            updateActualPostFee(staff, Arrays.asList(sidList), actualPostFee);
        } else {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE);
            int success = 0;
            long start = System.currentTimeMillis();
            try {
                params.setQueryFlag(2);
                params.setPage(new Page(1, PAGE_SIZE));
                Trades tradePages = tradeSearchService.search(staff, params);
                if (tradePages.getTotal().equals(0L)) {
                    progressData.setSucNum(0L);
                    progressData.setErrorNum(0L);
                    progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE, progressData);
                    return;
                } else {
                    progressData.setProgress(1);
                    progressData.setCountCurrent(0);
                    progressData.setCountAll(tradePages.getTotal().intValue());
                    progressData.setSucNum(0L);
                    progressData.setErrorNum(0L);
                    progressService.updateProgress(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE, progressData);
                }
                int pageSize = tradePages.getTotal().intValue() % PAGE_SIZE == 0 ? tradePages.getTotal().intValue() / PAGE_SIZE : (tradePages.getTotal().intValue() / PAGE_SIZE + 1);
                for (int i = 1; i <= pageSize; i++) {
                    params.setQueryFlag(1);
                    params.setPage(new Page(i, PAGE_SIZE));
                    List<Trade> tradeList = tradeSearchService.search(staff, params).getList();
                    if (org.apache.commons.collections.CollectionUtils.isEmpty(tradeList)) {
                        continue;
                    }
                    int querySize = tradeList.size();
                    List<Long> sidsList = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
                    int successSize = updateActualPostFee(staff, sidsList, actualPostFee);
                    success += successSize;
                    progressData.setCountCurrent(progressData.getCountCurrent() + querySize);
                    progressData.setSucNum((long) success);
                    progressData.setErrorNum(progressData.getErrorNum());
                    progressService.updateProgress(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE, progressData);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                progressData.setSucNum((long) success);
                progressData.setCountCurrent(progressData.getCountAll());
                progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE, progressData);
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(staff).append("批量修改实际运费").append(success).append("条，耗时:").append(System.currentTimeMillis() - start));
                }
            }


        }
    }


    /**
     * 实际更新运费
     *
     * @param staff
     * @param sids
     * @param actualPostFee
     * @return
     */
    private int updateActualPostFee(Staff staff, List<Long> sids, String actualPostFee) {
        List<List<Long>> splitSidList = Lists.partition(sids, 20);
        for (List<Long> splitSids : splitSidList) {
            List<Trade> trades = new ArrayList<>(splitSids.size());
            splitSids.forEach(s -> {
                Trade updateTrade = updatePostfeeBusiness.updateActualPostFee(staff, s, actualPostFee);
                trades.add(updateTrade);
            });
            //记录订单日志
            eventCenter.fireEvent(this, new EventInfo("trade.updateActualPostFee").setArgs(new Object[]{staff}), trades);
            //记录oplog
            opLogService.batchRecord(staff, buildOplogs(staff, trades));
        }
        return sids.size();
    }


    @Override
    public Trade pickingTrade(Staff staff, Long staffId, String staffName, String outSid, boolean force, boolean consign, String clientIP) {
        TradePickingParams tradePickingParams = new TradePickingParams();
        tradePickingParams.setValue(outSid);
        tradePickingParams.setPickParamsName("outSid");
        Trade trade = getPickTrade(staff, staffId, staffName, tradePickingParams, force, consign, clientIP);
        return trade;
    }

    @Override
    public List<Trade> pickingTrades(Staff staff, Long staffId, String staffName, Long waveId, boolean force, boolean consign, String clientIP) {
        List<Trade> trades = pickingBusiness.pickingTradesByWaveId(staff, staffId, waveId, force);
        doPickingTrades(staff, staffId, staffName, consign, clientIP, trades);
        return trades;
    }

    /**
     * 实现拣选领单动作
     *
     * @param staff
     * @param staffId
     * @param staffName
     * @param consign
     * @param clientIP
     * @param trades
     */
    private void doPickingTrades(Staff staff, Long staffId, String staffName, boolean consign, String clientIP, List<Trade> trades) {
        if (!CollectionUtils.isEmpty(trades)) {
            Staff pickStaff = staffAssembleBusiness.getStaffById(staffId);
            pickingBusiness.addTradePickingLog(pickStaff, trades, staffId, staffName);
            new EventFireSplitTradeUtils(eventCenter, 200).splitResult(this, new EventInfo("trade.picking").setArgs(new Object[]{pickStaff, null, staffId, staffName, null}), trades);
            if (consign) {
                for (List<Trade> subTrades : Lists.partition(trades, 200)) {
                    eventCenter.fireEvent(this, new EventInfo("wave.picking.trade.consign").setArgs(new Object[]{staff, subTrades.stream().map(Trade::getSid).collect(Collectors.toList()), clientIP}), null);
                }
            }
        }
    }

    @Override
    public WavePickingScanResult pickingPapersScanCode(Staff staff, Long waveId, Integer isExcep) {
        return tradesCollarService.pickingPapersScanCode(staff, waveId, isExcep);
    }

    @Override
    public List<Trade> syncTradeByTid(User user, String originTid) {
        return syncTradeBusiness.syncTradeByTid(user, originTid, true);
    }


    @Override
    public JSONObject smartAddressModify(Staff staff, JSONObject paramsObject) {
        String tid = paramsObject.getString("bizOrderId");
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(tid).append(" 奇门智能修改地址开始"));
        JSONObject modifiedAddress = paramsObject.getJSONObject("modifiedAddress");
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(tid).append(" modifiedAddress:").append(modifiedAddress.toJSONString()));
        JSONObject originalAddress = paramsObject.getJSONObject("originalAddress");
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(tid).append(" originalAddress:").append(originalAddress.toJSONString()));
        String receiverState = modifiedAddress.getString("province");
        String receiverCity = modifiedAddress.getString("city");
        String receiverDistrict = modifiedAddress.getString("area");
        String receiverTown = modifiedAddress.getString("town");
        String receiverAddress = modifiedAddress.getString("addressDetail");
        String receiverZip = modifiedAddress.getString("postCode");
        String receiveName = modifiedAddress.getString("name");
        String receiveMobile = modifiedAddress.getString("phone");
        Trade uodateTrade = new Trade();
        uodateTrade.setTid(tid);
        uodateTrade.setReceiverState(receiverState);
        uodateTrade.setReceiverCity(receiverCity);
        uodateTrade.setReceiverDistrict(receiverDistrict);
        uodateTrade.setReceiverAddress(receiverAddress);
        uodateTrade.setReceiverZip(receiverZip);
        uodateTrade.setReceiverName(receiveName);
        uodateTrade.setReceiverMobile(receiveMobile);
        uodateTrade.setUserId(paramsObject.getLong("sysUserId"));

        try {
            addressBusiness.smartAddressModify(staff, uodateTrade);
        } catch (Exception e) {
            //这里要对抛出的异常处理，不能直接返回 modify-address-forbid
            if (e instanceof TradeAddressModifiedException) {
                TradeAddressModifiedException addressModifiedException = (TradeAddressModifiedException) e;
                logger.error(LogHelper.buildLogHead(staff).append(tid).append(" 奇门智能修改地址失败！").append(addressModifiedException.getErrCode() + addressModifiedException.getErrMsg()));
                return buildExceptionResult(addressModifiedException);
            }
            logger.error(LogHelper.buildLogHead(staff).append(tid).append(" 奇门智能修改地址失败！ERP系统未捕获转换的异常"), e);
            return buildFailResult();
        }
        return buildSuccessResult();
    }

    private JSONObject buildFailResult() {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("errorCode", "3002");
        result.put("errorMsg", "自助修改地址失败，请联系商家修改");
        return result;
    }


    private JSONObject buildExceptionResult(TradeAddressModifiedException tradeAddressModifiedException) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("errorCode", tradeAddressModifiedException.getErrCode().toString());
        result.put("errorMsg", tradeAddressModifiedException.getErrMsg());
        return result;
    }

    private JSONObject buildSuccessResult() {
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }

    @Override
    public Trade updateShippingAddress(Staff staff, Trade current, boolean force) {
        return addressBusiness.updateAddress(staff, current, force);
    }

    @Override
    public Integer addressReachable(Staff staff, User user, String companyCode, Trade trade) {
        return tradeAddressReachableBusiness.addressReachable(staff, user, companyCode, trade);
    }

    @Override
    public List<Trade> addressReachablebatch(Staff staff, User user, List<Trade> trades) {
        return tradeAddressReachableBusiness.addressReachablebatch4Trade(staff, user, trades);
    }

    @Deprecated
    @Override
    public List<Trade> queryMergeTrades(Staff staff, Trade mergeTrade) {
        Assert.isTrue(TradeUtils.isMerge(mergeTrade), String.format("当前订单不是合单[sid:%s]", mergeTrade.getSid()));
        return tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, mergeTrade.getSid());
    }

    @Override
//    @Cacheable(value = "defaultCache#360", key = "'searchTraceLogistics_'+#sid+'_'+#oids")
    public List<TradeLogisticsTrace> searchTraceLogistics(Staff staff, Long sid, String oids) {
        return logisticBusiness.searchTraceLogistics(staff, sid);
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'searchExceptTraceLogistics_'+ #sid")
    public List<TradeLogisticsTrace> searchExceptTraceLogistics(Staff staff, Long sid) {
        Assert.notNull(sid, "缺少系统单号");
        Trade trade = tradeSearchService.queryBySid(staff, false, sid);
        Assert.notNull(trade, sid + "无法查找到此笔订单");
        return logisticBusiness.searchExceptTraceLogistics(staff, trade);
    }

    @Override
    public void delayReceiveTime(Staff staff, Long sid, Integer days) {
        logisticBusiness.delayReceiveTime(staff, sid, days);
    }

    @Override
    public PrintTradeLog validateTrades(Staff staff, Long logId, Integer startSeq, String startOutSid, Integer endSeq, String endOutSid) {
        return printBusiness.validateTrades(staff, logId, startSeq, startOutSid, endSeq, endOutSid);
    }

    @Override
    public List<Trade> queryReprintTrades(Staff staff, Long logId) {
        return printBusiness.queryReprintTrades(staff, logId);
    }

    @Override
    public PageListBase<PrintTradeLogDetailVO> queryReprintTradesDetail(Staff staff, Long logId, Page page, TradeControllerParams params) {
        return printBusiness.queryReprintTradesDetail(staff, logId, page, params);
    }

    @Override
    public Trade saveTrade(Staff staff, Trade trade, boolean force) {
        return tradeAddBusiness.save(staff, trade, force);
    }

    @Override
    public Trade safeSaveTrade(Staff staff, Trade trade, boolean force) {
        if (StringUtils.isEmpty(trade.getTid())) {
            return saveTrade(staff, trade, force);
        } else {
            return lockService.lock(TradeLockBusiness.trade2ERPLock(staff, trade), () -> tradeAddBusiness.save(staff, trade, force));
        }
    }

    @Override
    public ModifyData updateTrade(Staff staff, Trade trade) {
        return tradeUpdateBusiness.update(staff, trade, false, false);
    }

    @Override
    public ModifyData updateTradeGift(Staff staff, Trade trade) {
        return tradeGiftUpdateBusiness.updateTradeGift(staff, trade);
    }


    @Override
    public ModifyData updateTrade4Suit(Staff staff, Trade trade) {
        return suitSwitchBusiness.suitChange2Single(staff, trade);
    }

    @Override
    public ModifyData updateTrade4SuitBatch(Staff staff, Trade trade, Long orderId) {
        return suitSwitchBusiness.suitChange2SingleBatch(staff, trade, orderId);
    }

    @Override
    public ModifyData updateTradeSuit2Single(Staff staff, SuitToSingleContext context) {
        return context.getInsufficient() == 0 ? suitSwitchBusiness.suit2Single(staff, context) : suitSwitchInsufficientBusiness.suitInsufficient2Single(staff, context);
    }

    @Override
    public List<Trade> copy(Staff staff, TradeCopyParams tradeCopyParams) {
        return copyBusiness.copy(staff, tradeCopyParams);
    }

    @Override
    public List<Trade> copyBatch(Staff staff, TradeCopyParams tradeCopyParams) {
        return copyBusiness.copyBatch(staff, tradeCopyParams);
    }

    @Override
    public String[][] getTradeTitle() {
        return tradeExportBusiness.getExporTitle(TradeExportFieldEnum.FIELD_TRADE_SCOPE);
    }

    @Override
    public String[][] getOrderTitle() {
        return tradeExportBusiness.getExporTitle(TradeExportFieldEnum.FIELD_ORDER_SCOPE);
    }

    @Override
    public String[][] getTradeTitle(Long companyId) {
        return tradeExportBusiness.getExporTitle(TradeExportFieldEnum.FIELD_TRADE_SCOPE, companyId);
    }

    @Override
    public String[][] getOrderTitle(Long companyId) {
        return tradeExportBusiness.getExporTitle(TradeExportFieldEnum.FIELD_ORDER_SCOPE, companyId);
    }

    @Override
    public String[][] getExceptLogisticsTitle() {
        return exportBusiness.getExceptLogisticsTitle();
    }

    @Override
    public String[][] exportTrade(Staff staff, TradeExportParams params, boolean isAsync) {
        return tradeExportBusiness.transformExportData(tradeExportBusiness.exportTrade(staff, params, isAsync, null, null));
    }

    @Override
    public String[][] exportOrder(Staff staff, TradeExportParams params, boolean isAsync) {
        return tradeExportBusiness.transformExportData(tradeExportBusiness.exportOrder(staff, params, isAsync, null, null));
    }

    @Override
    public String[][] exportExceptLogistics(Staff staff, LogisticsTrackingPollPoolQueryParams params, String sids) {
        return exportBusiness.exportExceptLogistics(staff, params, sids);
    }

    @Override
    public Map<Long, String> packTrades(Staff staff, Long[] sids) {
        return packBusiness.markPacked(staff, sids);
    }

    @Override
    public Map<Long, String> weightTrades(Staff staff, Long[] sids) {
        return weightBusiness.markWeighed(staff, sids);
    }

    @Override
    public List<Trade> refuseRefund(Staff staff, Long[] sids) {
        return refuseRefundBusiness.refuseRefund(staff, sids);
    }

    @Override
    public List<Trade> swapStock(Staff staff, List<Long> sourceSids, List<Long> targetSids) {
        return tradeSearchService.queryBySids(staff, true, stockSwapBusiness.swap(staff, sourceSids, targetSids, true));
    }

    @Override
    public void swapStockOuterIds(Staff staff, List<String> sysOuterIds, Integer kind) {
        stockSwapBusiness.swapStockOuterIds(staff, sysOuterIds, kind);
    }

    //==============================================拆分装箱=============================================================
    @Override
    public TradePackSplit packSplitTrade(Staff staff, TradePackSplit tradePackSplit) {
        return packSplitBusiness.packSplitTrade(staff, tradePackSplit);
    }

    @Override
    public void updatePackSplitTrade(Staff staff, TradePackSplit tradePackSplit) {
        tradePackSplitDao.update(staff, tradePackSplit);
    }

    @Override
    public void updatePackSplitTrades(Staff staff, List<TradePackSplit> tradePackSplits) {
        tradePackSplitDao.batchUpdate(staff, tradePackSplits);
    }

    @Override
    public List<TradePackSplit> searchPackSplitTrade(Staff staff, Long sid) {
        return packSplitBusiness.queryTradePackSplit(staff, sid);
    }

    @Override
    public List<OrderPackSplit> searchPackSplitTradeDetail(Staff staff, Long packId) {
        return packSplitBusiness.queryPackSplitTradeDetail(staff, packId);
    }

    @Override
    public List<Trade> giftMatch(Staff staff, Integer reMatch, Long[] sidArray) {
        return giftMatchManualBusiness.match(staff, reMatch, sidArray);
    }

    @Override
    public CalculateFreightCostResult calculateFreightCost(CalculateFreightCostParams params) {
        Long sid = params.getSid();
        String weight = params.getWeight();

        Assert.notNull(sid, "请输入订单号");
        Assert.isTrue(StringUtils.isNotEmpty(weight), "请传入重量");

        String regex = "^\\d{1,10}\\.?\\d{0,4}$";
        if (!weight.matches(regex)) {
            throw new IllegalArgumentException("重量长度过长或格式不正确，请重新输入！");
        }

        Staff staff = params.getStaff();
        Assert.notNull(staff, "请传入staff");

        Trade trade = params.getCurrentTrade();

        if(trade==null){
            List<Trade> trades = tradeSearchService.queryBySids(staff, false, sid);
            Assert.isTrue(trades.size() > 0, "查不到此笔订单");
            trade = trades.get(0);
        }

        trade.setWeight(Double.valueOf(weight));
        String selectType;
        if (isNewTemplate(staff)) {
            selectType = freightExpressService.getFreightExpressType(staff, trade.getLogisticsCompanyId());
        } else {
            selectType = freightTemplateService.getSelectFreightTemplateType(staff, trade.getTemplateId(), trade.getTemplateType());
        }
        FreightCost cost;
        if ("volume".equals(selectType)) {
            cost = freightTemplateVolumeService.countVolume(staff, trade);
        } else {
            cost = freightTemplateService.countWeight(staff, trade);
        }
        if (cost.isError()) {
            new PostFeeLogBuilder(staff).appendTrade(trade).format("计算运费出错 templateId:%s templateType:%s selectType:%s reason:%s",
                    trade.getTemplateId(), trade.getTemplateType(), selectType, cost.getReason()).printError(logger, null);
        }
        return new CalculateFreightCostResult(sid, cost);
    }

    private boolean isNewTemplate(Staff staff) {
        CheckHasFeatureRequest indexRequest = new CheckHasFeatureRequest();
        indexRequest.setCompanyId(staff.getCompanyId());
        indexRequest.setFeature(Feature.PRINT_TEMPLATE_INTEGRATE);
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(indexRequest);
        boolean isNewTemplate = response.isSuccess() && response.isHasFeature();
        if (isNewTemplate) {
            return true;
        }
        return false;
    }

    @Override
    public List<Trade> handleMessagememo(Staff staff, Long[] sids,String  type , Integer isHandlerMessage, Integer isHandlerMemo) {
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_IS_HAND.getType())
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .isHandlerMemo(isHandlerMemo)
                .isHandlerMessage(isHandlerMessage)
                .handleMergeTrade("1".equals(type))
                .build();
        return tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, sids).getUpdatedTrades();
    }

    @Override
    public void tradeWeight(Staff staff, Long sid, String weight, String cost, Integer kind, String ip, String outSid,String packmaOuterIds) {
        tradeNewWeight(new TradeWeightParams.Builder().staff(staff).sid(sid).weight(weight).cost(cost).kind(kind).clientIp(ip).outSid(outSid).packmaOuterIds(packmaOuterIds).build());
    }

    @Override
    public void tradeWeightBatch(Staff staff, Long waveId, Long warehouseId, String weight, String cost, Integer kind, String ip, ProgressData progressData, Integer sendType, String packmaOuterIds,Staff packStaff) {
        String tradeFields = "sid,wave_id,user_id,taobao_id,company_id,source,sub_source,tid,status,type,buyer_message,buyer_nick,seller_nick,post_fee,seller_memo,seller_memo_update,seller_flag,consign_time,sys_consigned,enable_status,can_confirm_send,express_print_time,deliver_print_time,assembly_print_time,check_manual_merge_count,is_excep,is_lost_msg,is_halt,is_refund,is_package,is_weigh,is_presell,can_delivered,warehouse_id,warehouse_name,sys_status,sys_memo,is_cancel,is_urgent,audit_time,stock_status,insufficient_num,insufficient_rate,weight,actual_post_fee,net_weight,cost,scalping,is_upload,address_changed,merge_type,merge_sid,split_type,split_sid,is_store,black_buyer_nick,is_jz,is_smart,tag_ids,except_ids,three_pl_timing,is_tmall_delivery,is_deduct,is_sendmessage,except_memo,is_handler_message,is_handler_memo,pick_goods_type,delivery_time,collect_time,sign_time,es_time,item_excep,pt_consign_time,unattainable,handled,v,theory_post_fee,stock_region_type,dest_id,source_id,convert_type,belong_type,excep,out_sid";
        Page page = new Page();
        int pageNo = 1;
        int pageSize = 200;
        page.setPageSize(pageSize);
        int countAll = 0;
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Map<String, String> errorMap = new HashMap<>();
        for (; ; ) {
            page.setPageNo(pageNo);
            List<Trade> tradeList = tradeSearchService.queryByWaveId(staff, page, false, tradeFields, warehouseId, waveId);
            countAll += (tradeList == null ? 0 : tradeList.size());
            Map<Long, Trade> reWeighTrade = new HashMap<>();
            Map<Long, Trade> weighTrade = new HashMap<>();
            processWeightTrade(staff, tradeConfig, tradeList, kind, errorMap, reWeighTrade, weighTrade, weight, cost, sendType);
            if (reWeighTrade.size() > 0) {
                weightBusiness.batchReWeightTrade(staff, reWeighTrade, errorMap, weight,packmaOuterIds,packStaff);
            }
            if (weighTrade.size() > 0) {
                if (TradeWeightUtils.isAfterConsignWeight(kind)) {
                    weightBusiness.weighDeliveredTradeBatch(staff, weighTrade.keySet().toArray(new Long[0]), new BigDecimal(weight).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue(), cost,packStaff);
                } else {
                    Map<String, String> resultMap = weightBusiness.batchWeigh(staff, weighTrade, ip, kind,packmaOuterIds,packStaff);
                    if (null != resultMap && resultMap.size() > 0) {
                        errorMap.putAll(resultMap);
                    }
                }
            }
            //发货前称重/重复称重支持消耗包材
            if (reWeighTrade.size() > 0 || weighTrade.size() > 0) {
                Map<Long, Trade> tradeMap = new HashMap<>();
                if (MapUtils.isNotEmpty(reWeighTrade)) {
                    tradeMap.putAll(reWeighTrade);
                }
                if (MapUtils.isNotEmpty(weighTrade)) {
                    tradeMap.putAll(weighTrade);
                }
                if (MapUtils.isNotEmpty(tradeMap)) {
                    processBatchWeightAfter(staff, waveId, kind, ip, packmaOuterIds, tradeMap, errorMap);
                }
            }
            if (tradeList.size() < pageSize) {
                break;
            }
            pageNo++;
        }
        progressData.setExecutResult(errorMap);
        progressData.setCountAll(countAll);
        progressData.setErrorNum((long) errorMap.size());
        progressData.setSucNum((long) (countAll - errorMap.size()));
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH, progressData);
    }

    /**
     * 批量称重后处理
     *
     * @param staff
     * @param waveId
     * @param kind
     * @param ip
     * @param packmaOuterIds
     * @param weighTrade
     * @param resultMap
     */
    protected void processBatchWeightAfter(Staff staff, Long waveId, Integer kind, String ip, String packmaOuterIds, Map<Long, Trade> weighTrade, Map<String, String> resultMap) {
        //发货前波次称重,增加包材商品
        if (null != waveId && StringUtils.isNotEmpty(packmaOuterIds)
                && MapUtils.isNotEmpty(weighTrade)
                && TradeWeightUtils.isBeforeConsignWeight(kind)) {
            List<Trade> weightSuccessTrades = new ArrayList<>();
            List<Trade> packmaTrades = new ArrayList<>();
            if (MapUtils.isNotEmpty(resultMap)) {
                weightSuccessTrades.addAll(weighTrade.values().stream().filter(trade -> !resultMap.containsKey(trade.getSid().toString())).collect(Collectors.toList()));
            } else {
                weightSuccessTrades.addAll(weighTrade.values());
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(weightSuccessTrades)) {
                for (Trade trade : weightSuccessTrades) {
                    if (TradeUtils.isMerge(trade)) {
                        if (trade.getMergeSid() != null && trade.getSid() != null
                                && Objects.equals(trade.getMergeSid(), trade.getSid())) {
                            packmaTrades.add(trade);
                        }
                        //合单子单不消耗包材
                    } else {
                        packmaTrades.add(trade);
                    }
                }
                List<Long> sids = packmaTrades.stream().map(Trade::getSid).distinct().collect(Collectors.toList());
                if (sids.size() > 0) {
                    packBusiness.consumePackma(staff, sids, packmaOuterIds, ip,kind);
                }
            }
        }
    }

    private void processWeightTrade(Staff staff, TradeConfig tradeConfig, List<Trade> tradeList, Integer kind,
                                    Map<String, String> errorMap, Map<Long, Trade> reWeighSids, Map<Long, Trade> weighSids,
                                    String weight, String cost, Integer sendType) {
        double actWeight = MathUtils.toDouble(weight,4);;
        String actCost = StringUtils.isBlank(cost) ? null : cost.trim();
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        boolean reWeight = TradeWeightUtils.isReWeight(kind);
        Long companyId = staff.getCompanyId();
        for (Trade trade : tradeList) {
            //校验
            TradeValidator validator = new TradeValidator();
            validator.setThrowExceptionIfError(false);
            if (11369L == companyId || 49365L == companyId) {
                // 校验上传异常,只开11369和49365这两个公司
                validator.setCheckUploadException(true);
            }
            tradeValidateBusiness.checkTradeWeight(staff, trade, validator, tradeConfig, kind, sendType);
            if (validator.hasError()) {
                errorMap.put(trade.getSid().toString(), validator.getMessage());
                continue;
            }
            trade.setWeight(actWeight);
            trade.setActualPostFee(actCost);
            if (1 == trade.getIsWeigh() && !reWeight) {
                errorMap.put(trade.getSid().toString(), "重复称重");
            } else {
                if (1 == trade.getIsWeigh()) {
                    reWeighSids.put(trade.getSid(), trade);
                } else {
                    weighSids.put(trade.getSid(), trade);
                }
            }
        }
    }

    private List<OpLog> buildOplogs(Staff staff, List<Trade> trades) {
        List<OpLog> oplogList = Lists.newArrayListWithCapacity(trades.size());
        trades.forEach(trade -> {
            OpLog opLog = new OpLog();
            opLog.setCompanyId(staff.getCompanyId());
            opLog.setStaffId(staff.getId());
            opLog.setAccountName(staff.getAccountName());
            opLog.setStaffName(staff.getName());
            opLog.setDomain(Domain.TRADE);
            opLog.setAction("update_actualPostFee");
            opLog.setContent("修改实际运费:" + trade.getOrigin().getActualPostFee() + "->" + trade.getActualPostFee());
            opLog.setCreated(new Date());
            oplogList.add(opLog);
        });
        return oplogList;
    }


    private void addProcessLog(Staff staff, String weight, Trade trade) {
        StringBuilder sb = new StringBuilder("包裹称重失败，重量：").append(weight).append("kg，具体称重人：").append(staff.getName());
        TradeTrace trace = TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "称重", staff.getName(), new Date(), sb.toString());
        tradeTraceService.addTradeTrace(staff, trace);
    }

    @Override
    public void fillBrandSupplierId(Staff staff, List<Trade> trades) {
        fillBrandSupplierId(staff, trades, true, false);
    }

    private String buildItemKey(Long itemSysId, Long skuSysId) {
        return itemSysId + "_" + ((skuSysId == null || skuSysId < 0) ? 0 : skuSysId);
    }

    private <T extends Trade> Map<String, List<DmjItemDto>> searchItemSuppllyAndBrandInfo(Staff staff, List<Order> orders) {
        Set<String> itemKeySet = new HashSet<>();
        FxLogBuilder builder = FxLogBuilder.fx(staff).append("根据商品档案匹配供应商,忽略order ").startWatch();
        for (Order order : orders) {
            if (order.getItemSysId() == null || order.getItemSysId() <= 1) {
                builder.group("商品未匹配",order.getId());
                continue;
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(order.getSupplierIds()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(order.getBrandIds())) {
                builder.group("已有品牌和供应商",order.getId());
                continue;
            }
            itemKeySet.add(buildItemKey(order.getItemSysId(), order.getSkuSysId()));
        }
        builder.printDebug(logger);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemKeySet)) {
            return Maps.newHashMap();
        }
        QueryByItemIdInfoListRequest request = new QueryByItemIdInfoListRequest();
        List<ItemIdInfoDto> itemIdInfoList = Lists.newArrayListWithCapacity(orders.size());
        for (String key : itemKeySet) {
            String[] splitKey = key.split("_");
            ItemIdInfoDto itemIdInfoDto = new ItemIdInfoDto();
            itemIdInfoDto.setSysItemId(Long.parseLong(splitKey[0]));
            itemIdInfoDto.setSysSkuId(Long.parseLong(splitKey[1]));
            itemIdInfoList.add(itemIdInfoDto);
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemIdInfoList)) {
            logger.debug(LogHelper.buildLogHead(staff).append("没有需要填充的商品"));
            return Maps.newHashMap();
        }
        request.setItemIdInfoList(itemIdInfoList);
        DmjItemNeedFilledInfo needFiled = DmjItemNeedFilledInfo.builder().needSupplier(true).needBrand(true).build();
        request.setNeedFilledInfo(needFiled);
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        request.setNeedFields("company_id,sys_item_id,sys_sku_id,properties_name,pic_path,outer_id,brand");
        logger.debug(LogHelper.buildLogHead(staff).append("request:" + JSON.toJSONString(request)));
        QueryByItemIdInfoListResponse response = dmjItemCommonSearchApi.queryByItemIdInfoList(request);
        logger.debug(LogHelper.buildLogHead(staff).append("response:" + JSON.toJSONString(response, DMJ_ITEM_SIMPLE_PROPERTY_PRE_FILTER)));
        if (response == null || org.apache.commons.collections.CollectionUtils.isEmpty(response.getDmjItemList())) {
            return Maps.newHashMap();
        }

        Map<String, List<DmjItemDto>> searchItemKeyMap = response.getDmjItemList().stream().collect(Collectors.groupingBy(item -> buildItemKey(item.getSysItemId(), item.getSysSkuId())));
        return searchItemKeyMap;
    }

    @Override
    public <T extends Trade> void fillBrandSupplierId(Staff staff, List<T> trades, boolean onlyFillFirstSupplier, boolean isMatchFxSupplierItem) {
        try {
            long startTime = System.currentTimeMillis();
            if (isMatchFxSupplierItem) {
                tradeQueryService.fillDestAndSourceName(trades);
            }
            List<Order> orders = new ArrayList<>();

            for (T trade : trades) {
                if (Objects.isNull(trade)) {
                    return;
                }

                List<TbOrder> orderList = ((TbTrade) trade).getOrders();
                if (CollectionUtils.isEmpty(orderList)) {
                    return;
                }

                orders.addAll(orderList);
                orderList.forEach(order -> {
                    if (Objects.isNull(order) || CollectionUtils.isEmpty(order.getSuits())) {
                        return;
                    }
                    orders.addAll(order.getSuits());
                });
            }
            //查询品牌和供应商
            Map<String, List<DmjItemDto>> searchItemKeyMap = searchItemSuppllyAndBrandInfo(staff, orders);
            if (MapUtils.isEmpty(searchItemKeyMap)) {
                return;
            }

            Map<String, List<Order>> orderMap = orders.stream().collect(Collectors.groupingBy(order -> buildItemKey(order.getItemSysId(), order.getSkuSysId())));

            Map<Long, Trade> sidMap = TradeUtils.toMapBySid(trades);

            orderMap.forEach((k, v) -> {
                List<DmjItemDto> dmjItemDtos = searchItemKeyMap.get(k);
                if (org.apache.commons.collections.CollectionUtils.isEmpty(dmjItemDtos)) {
                    return;
                }
                DmjItemDto dmjItemDto = dmjItemDtos.get(0);
                List<BrandDto> brandList = dmjItemDto.getBrandList();
                List<ItemSupplierBridgeDto> itemSupplierBridgeList = dmjItemDto.getItemSupplierBridgeList();
                FxLogBuilder builder = FxLogBuilder.fx(staff).append("供应商为空或者供应商名称和供销商不一致 ").startArray().startWatch();
                for (Order order : v) {
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(brandList)) {
                        order.setBrandIds(Collections.singletonList(brandList.get(0).getBrandId()));
                    }
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemSupplierBridgeList)) {
                        if (onlyFillFirstSupplier || itemSupplierBridgeList.size() == 1) {
                            order.setSupplierIds(Collections.singletonList(itemSupplierBridgeList.get(0).getSupplierId()));
                        } else {
                            order.setSupplierIds(itemSupplierBridgeList.stream().map(ItemSupplierBridgeDto::getSupplierId).collect(Collectors.toList()));
                        }
                        if (isMatchFxSupplierItem) {
                            OrderExt orderExt = new OrderExt();
                            orderExt.setId(order.getId());
                            orderExt.setSid(order.getSid());
                            String destName = Optional.ofNullable(sidMap.get(order.getBelongSid())).map(Trade::getDestName).orElse("");
                            boolean setted = false;
                            for (ItemSupplierBridgeDto itemSupplierBridgeDto : itemSupplierBridgeList) {
                                if (Objects.equals(itemSupplierBridgeDto.getSupplierName(),destName)) {
                                    orderExt.setSupplierItemOuterId(itemSupplierBridgeDto.getSupplierItemOuterId());
                                    orderExt.setSupplierName(itemSupplierBridgeDto.getSupplierName());
                                    setted = true;
                                    break;
                                }
                            }
                            if (!setted) {
                                builder.startObject().append(order.getSysOuterId()).append("trade",destName)
                                        .append("items",itemSupplierBridgeList.stream().map(ItemSupplierBridgeDto::getSupplierName).collect(Collectors.toList())).endObject();
                            }
                            order.setOrderExt(orderExt);
                        }
                    }
                }
                if (builder.isChanged()) {
                    builder.endArray().printInfo(logger);
                }
            });
            long endTime = System.currentTimeMillis();
            logger.info("fillBrandSupplierCost:" + (endTime - startTime));
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "设置供应商信息出错"), e);
        }
    }

    @Override
    public Trade queryByPickingParams(Staff staff, Long staffId, String staffName, TradePickingParams tradePickingParams, boolean force, boolean consign, String clientIP) {
        return getPickTrade(staff, staffId, staffName, tradePickingParams, force, consign, clientIP);
    }

    @Override
    public BaseResult handleTradeCancelRequest(User user, String tids, boolean agree, String refuseReason) {
        Assert.isTrue(StringUtils.isNoneBlank(tids), "请传入订单号！");
        BaseResult baseResult = platformManagement.getAccess(user.getSource(), IPlatformTradeAccess.class).handleTradeCancelRequest(user, tids, agree, refuseReason);
        eventCenter.fireEvent(this, new EventInfo("trade.cancel.trace").setArgs(new Object[]{user.getStaff(), tids, agree, refuseReason}), null);
        //发送指定下载事件更新订单
        BufferRequest bufferRequest = Buffers.build(user.getStaff(), TradeNotifyConstant.NOTIFY_DOWNLOAD);
        bufferRequest.setGroupId(user.getId());
        bufferService.buffer(bufferRequest, Arrays.asList(tids.split(",")), true);
        eventCenter.fireEvent(this, new EventInfo(TradeNotifyConstant.NOTIFY_DOWNLOAD).setArgs(new Object[]{user.getStaff(), bufferRequest}), null);
        return baseResult;
    }

    @Override
    public List<Map<String, String>> packagesNotice(Staff staff, Long[] sids) {
        return uploadPackagesNoticeBusiness.packagesNoticeManual(staff, sids);
    }

    @Override
    public void tradeNewWeight(TradeWeightParams tradeWeightParams) {
        Staff staff = tradeWeightParams.getStaff();
        Long sid = tradeWeightParams.getSid();
        String weight = tradeWeightParams.getWeight();
        String cost = tradeWeightParams.getCost();
        String ip = tradeWeightParams.getClientIp();
        String outSid = tradeWeightParams.getOutSid();
        Integer kind = tradeWeightParams.getKind();
        String outerId = tradeWeightParams.getOuterId();
        String sendType = tradeWeightParams.getOuterId();
        AbsLogBuilder logBuilder = tradeWeightParams.getLogBuilder();
        String actualVolume = tradeWeightParams.getActualVolume();
        String actualLengthWidthAndHeight = tradeWeightParams.getActualLengthWidthAndHeight();

        outSid = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(), outSid);

        Trade trade = null;
        //发货后称重 不走锁定逻辑的企业直接取外部传入的trade减少查询
        if (tradeWeightParams.getTrade() != null
                && ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isUnlockWhenTradeWeightCompanyId(staff.getCompanyId())) {
            trade = tradeWeightParams.getTrade();
        } else {
            reBaseTimer(logBuilder);
            trade = tradeQueryService.queryByMixKey(staff, String.valueOf(sid));
            recordTimer(logBuilder, "queryByMixKey");
        }
        Assert.notNull(trade,sid+" 对应订单不存在");

        if (!tradeWeightParams.isChecked()) {
            TradeValidator validator = new TradeValidator();
            validator.setThrowExceptionIfError(true);
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            tradeValidateBusiness.checkTradeWeight(staff, trade, validator, tradeConfig, kind, false, outSid, 0);
            recordTimer(logBuilder, "checkTradeWeight");
        }

        if (logBuilder != null) {
            logBuilder.append("kind", kind).append("isWeigh", trade.getIsWeigh()).append("originOutSid", trade.getOutSid());
        }
        outSid = outSid == null ? trade.getOutSid() : outSid;
        if (!validateOutSid(staff,outSid, trade)) {
            throw new TradeException("传入运单号:"+outSid + " 与订单数据不一致");
        }
        if (tradeValidateBusiness.isTradeWeight(staff, trade, outSid)) {
            if (TradeWeightUtils.isReWeight(kind)) {
                weightBusiness.reWeightTrade(staff, sid, Double.parseDouble(weight.trim()), StringUtils.isBlank(cost) ? null : cost, null, outSid, logBuilder,tradeWeightParams.getPackmaOuterIds(),tradeWeightParams.getPackStaff(),actualVolume);
                return;
            } else {
                //添加日志
                addProcessLog(staff, weight, trade);
                throw new TradeException("已称重订单在重复称重关闭时不能进行重复称重！");
            }
        }
        if (TradeWeightUtils.isBeforeConsignWeight(kind)) {
            Map<Long, Trade> sidTradeMap = new HashMap<Long, Trade>();
            Trade simple = new TbTrade();
            simple.setSid(trade.getSid());
            simple.setWeight(Double.parseDouble(weight));
            simple.setActualPostFee(cost);
            simple.setActualVolume(actualVolume);
            simple.setActualLengthWidthAndHeight(actualLengthWidthAndHeight);
            sidTradeMap.put(trade.getSid(), simple);
            weightBusiness.weigh(staff, sidTradeMap, ip, null, outSid, logBuilder,tradeWeightParams.getPackmaOuterIds(),tradeWeightParams.getPackStaff(),tradeWeightParams.isAsyncCalcFreight());
        } else {
            weightBusiness.weighDeliveredTrade(staff, sid, Double.parseDouble(weight.trim()), null, outSid, trade, logBuilder,tradeWeightParams.getPackStaff(), actualVolume, actualLengthWidthAndHeight);
        }
    }

    private boolean validateOutSid(Staff staff,String outSid, Trade trade) {
        if (outSid != null) {
            if (Objects.equals(trade.getOutSid(),outSid)) {
                return true;
            }

            List<MultiPacksPrintTradeLogDetail> details = multiPacksPrintTradeLogService.queryDetails(staff, new MultiPacksPrintTradeLogDetailParams(trade.getSid(), trade.getTemplateId()));
            if (org.apache.commons.collections.CollectionUtils.isEmpty(details)) {
                return false;
            }

            for (MultiPacksPrintTradeLogDetail detail : details) {
                if (Objects.equals(detail.getOutSid(),outSid)) {
                    return true;
                }
            }
        }
        return false;
    }

    protected void recordTimer(AbsLogBuilder logBuilder, String msg) {
        if (logBuilder != null) {
            logBuilder.recordTimer(msg);
        }
    }

    protected void reBaseTimer(AbsLogBuilder logBuilder) {
        if (logBuilder != null) {
            logBuilder.reBaseTimer();
        }
    }


    @Override
    public BaseResult outBoundTrade(User user, String tid) {
        return platformManagement.getAccess(user.getSource(), IPlatformTradeAccess.class).outBoundTrade(user, tid);
    }

    @Override
    public String[][] exportItemChangeDetail(Staff staff, TradeItemChangeDetailExportParams exportParams, boolean isAsync) {
        return tradeExportBusiness.exportItemChangeDetail(staff, exportParams, isAsync);
    }

    private Trade getPickTrade(Staff staff, Long staffId, String staffName, TradePickingParams tradePickingParams, boolean force, boolean consign, String clientIP) {
        Trade trade = pickingBusiness.pickingTrade(staff, tradePickingParams, force, true);
        //领单记录
        List<Trade> trades = new ArrayList<Trade>();
        trades.add(trade);
        Staff pickStaff = staffAssembleBusiness.getStaffById(staffId);
        pickStaff = pickStaff == null ? staff : pickStaff;
        pickingBusiness.addTradePickingLog(pickStaff, trades, staffId, staffName);
        eventCenter.fireEvent(this, new EventInfo("trade.picking").setArgs(new Object[]{pickStaff, trade.getSid(), staffId, staffName, tradePickingParams.getValue()}), trades);
        if (consign) {
            //给该笔订单发货
            consignThisTrade(staff, trade, clientIP);
        }
        return trade;
    }

    @Override
    public Map<String, Object> pickingBatchByWavePosition(Staff staff, Long staffId, String staffName, boolean force, boolean consign, String clientIP, String firstOutId, String lastOutId) {
        Trade firstTrade = getPickTradeWithNotValidate(staff, force, firstOutId);
        Trade lastTrade = getPickTradeWithNotValidate(staff, force, lastOutId);
        Assert.isTrue(firstTrade.getWaveId() != null && firstTrade.getWaveId() > 0L, String.format("该订单未在波次中，不支持批量拣选！运单号：%s", firstTrade.getOutSid()));
        Assert.isTrue(Objects.equals(firstTrade.getWaveId(), lastTrade.getWaveId()), "首尾单号不在同一波次中！");
        Map<String, Object> resultMap = pickingBusiness.pickingBatchByWavePositionSection(staff, firstTrade.getWaveId(), firstTrade.getSid(), lastTrade.getSid(), force);
        List<Trade> successList = (ArrayList<Trade>) resultMap.get("successList");

        //拣选领单
        doPickingTrades(staff, staffId, staffName, consign, clientIP, successList);

        return resultMap;
    }

    private Trade getPickTradeWithNotValidate(Staff staff, boolean force, String firstOutId) {
        TradePickingParams firstParams = new TradePickingParams();
        firstParams.setPickParamsName("outSid");
        firstParams.setValue(firstOutId);
        return pickingBusiness.pickingTrade(staff, firstParams, force, false);
    }

    private void consignThisTrade(Staff staff, Trade trade, String clientIP) {
        if (trade != null) {
            String source = trade.getSource();
            List<ConsignRecord> consignRecords = null;
            if (trade.isOutstock() || trade.isDangkouTrade()) { //出库单，档口订单发货
                if (staff.getConf().isOpenWms() && !staff.getConf().openWmsStorageSection()) {
                    wmsService.checkGoodsOrderRecords(staff, ArrayUtils.toLongList(trade.getSid().toString()));
                }
                consignRecords = tradeService.consign(staff, ArrayUtils.toLongArray(trade.getSid().toString()), TradeConstants.TYPE_TRADE_OUT, null, null, null, null, null);
            } else if (source != null) { //平台单发货
                consignRecords = tradeService.consign(staff, ArrayUtils.toLongArray(trade.getSid().toString()), SendType.ONLINE.name(), clientIP, null, null, null, null, true);
            }
        }
    }

    @Override
    public Map<String, String> spiteReport(Staff staff, List<Long> sidList, SpiteReportDTO reportDTO) {
        return tradeSpiteReportBusiness.spiteReport(staff, sidList, reportDTO);
    }

    @Override
    public List<TradeDeclare> queryTradeDeclare(Staff staff, Long sid) {
        //1.先查询报关信息表
        TbTrade tbTrade = tbTradeDao.queryBySid(staff, sid);
        Assert.notNull(tbTrade, "订单不存在");
        List<TradeDeclare> tradeDeclares = tradeDeclareDao.queryTradeDeclareBySid(staff, tbTrade.getUserId(), sid);
        if (!CollectionUtils.isEmpty(tradeDeclares)) {
            return tradeDeclares;
        }

        List<TradeDeclare> result = new ArrayList<>();
        //2.如果没有，则自动生成报关信息返回
        //3.查询子订单
        List<TbOrder> orders = tbOrderDAO.queryBySids(staff, sid);
        //4.查询系统商品档案
        QueryByItemIdInfoListRequest request = new QueryByItemIdInfoListRequest();
        List<ItemIdInfoDto> itemIdInfoList = new ArrayList<>();
        for (TbOrder order : orders) {
            if (Objects.isNull(order.getItemSysId())) {
                continue;
            }
            ItemIdInfoDto itemIdInfoDto = new ItemIdInfoDto();
            if (Objects.nonNull(order.getSkuSysId()) && order.getSkuSysId() > 0) {
                itemIdInfoDto.setSysSkuId(order.getSkuSysId());
            }
            itemIdInfoDto.setSysItemId(order.getItemSysId());
            itemIdInfoList.add(itemIdInfoDto);
        }
        Map<String, DmjItemDto> itemDtoMap;
        if (itemIdInfoList.size() > 0) {
            request.setItemIdInfoList(itemIdInfoList);
            DmjItemNeedFilledInfo needFiled = DmjItemNeedFilledInfo.builder().needCustomsDeclarationInfo(true).build();
            request.setNeedFilledInfo(needFiled);
            request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
            QueryByItemIdInfoListResponse response = dmjItemCommonSearchApi.queryByItemIdInfoList(request);
            List<DmjItemDto> dmjItems = Optional.ofNullable(response).map(QueryByItemIdInfoListResponse::getDmjItemList).orElse(new ArrayList<>());
            itemDtoMap = dmjItems.stream().collect(Collectors.toMap(obj -> {
                Long sysSkuId = obj.getSysSkuId();
                if (Objects.isNull(sysSkuId) || sysSkuId < 0) {
                    sysSkuId = 0L;
                }
                return obj.getSysItemId() + "_" + sysSkuId;
            }, obj -> obj, (v1, v2) -> v2));
        } else {
            itemDtoMap = new HashMap<>();
        }

        //5.信息组装
        for (TbOrder order : orders) {
            //根据子单的条数生成对应的报关信息
            TradeDeclare tradeDeclare = new TradeDeclare();
            tradeDeclare.setCompanyId(order.getCompanyId());
            tradeDeclare.setUserId(tbTrade.getUserId());
            tradeDeclare.setSid(order.getSid());
            tradeDeclare.setOid(order.getOid());
            tradeDeclare.setOrderId(order.getId());
            tradeDeclare.setPicPath(order.getPicPath());
            tradeDeclare.setOuterSkuId(order.getOuterSkuId());
            tradeDeclare.setTitle(order.getTitle());
            tradeDeclare.setNum(order.getNum());
            if (Objects.nonNull(order.getPrice())) {
                tradeDeclare.setPrice(Double.parseDouble(convertPriceToUsd(order.getPrice(), tbTrade.getCreated(), order.getNum())));
            }
            if (Objects.nonNull(order.getNetWeight()) && order.getNetWeight() > 0) {
                tradeDeclare.setWeight(order.getNetWeight());
            }
            Long skuSysId = order.getSkuSysId();
            if (Objects.isNull(skuSysId) || skuSysId < 0) {
                skuSysId = 0L;
            }
            DmjItemDto dmjItem = itemDtoMap.get(order.getItemSysId() + "_" + skuSysId);
            if (Objects.isNull(dmjItem)) {
                result.add(tradeDeclare);
                continue;
            }
            tradeDeclare.setDeclareZh(dmjItem.getDeclareNameZh());
            tradeDeclare.setDeclareEn(dmjItem.getDeclareNameEn());
            tradeDeclare.setHsCode(dmjItem.getHsCode());
            //如果系统商品有值，则优先取系统商品信息
            if (Objects.nonNull(dmjItem.getDeclarePrice())) {
                tradeDeclare.setPrice(dmjItem.getDeclarePrice());
            }
            if (Objects.nonNull(dmjItem.getDeclaredWeight())) {
                tradeDeclare.setWeight(dmjItem.getDeclaredWeight());
            }
            if (StringUtils.isNotBlank(dmjItem.getOuterId())) {
                tradeDeclare.setOuterSkuId(dmjItem.getOuterId());
            }
            if (StringUtils.isNotBlank(dmjItem.getPicPath())) {
                tradeDeclare.setPicPath(dmjItem.getPicPath());
            }
            if (StringUtils.isNotBlank(dmjItem.getTitle())) {
                tradeDeclare.setTitle(dmjItem.getTitle());
            }
            result.add(tradeDeclare);
        }

        return result;
    }

    @Override
    public void saveTradeDeclare(Staff staff, List<TradeDeclare> tradeDeclareList) {
        //保存订单报关信息
        tradeDeclareDao.insertTradeDeclare(staff, tradeDeclareList);
    }

    @Override
    public void refreshIsPick(Staff staff, Long companyId, Date endTime) {
        if (Objects.isNull(staff) || !Objects.equals(staff.getCompanyId(), companyId)) {
            staff = staffService.queryFullByCompanyId(companyId);
        }

        if (staff == null) {
            return;
        }
        if (endTime == null) {
            return;
        }
        while (true) {
            List<TbOrder> byNeedRefreshIsPick = tbOrderDAO.getByNeedRefreshIsPick(staff, endTime);
            if (CollectionUtils.isEmpty(byNeedRefreshIsPick)) {
                return;
            }
            List<TbOrder> updateOrders = byNeedRefreshIsPick.parallelStream().map(originOrder -> {
                TbOrder order = new TbOrder();
                order.setId(originOrder.getId());
                order.setCompanyId(originOrder.getCompanyId());
                order.setIsPick(2);
                return order;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(updateOrders)) {
                return;
            }
            tbOrderDAO.batchUpdate(staff, updateOrders);
        }

    }


    /**
     * 将人民币转成美元
     *
     * @param amount 金额
     * @return 人民币金额
     */
    private String convertPriceToUsd(String amount, Date date, Integer orderNum) {
        String convertedPrice = amount;
        if (StringUtils.isNotBlank(amount)) {
            String rate = Optional.ofNullable(exchangeRateService.queryLastestByFromAndDate("CNY", date))
                    .map(ExchangeRate::getToInJson)
                    .map(JSON::parseObject)
                    .map(multiCurrencyRate -> multiCurrencyRate.getJSONObject("USD"))
                    .map(targetCurrencyRateInfo -> targetCurrencyRateInfo.getString("rate"))
                    .orElse("1"); //异常情况不进行汇率转换，将汇率设置成1
            BigDecimal amountBigDecimal = new BigDecimal(amount);
            BigDecimal rateBigDecimal = new BigDecimal(rate);
            convertedPrice = amountBigDecimal.multiply(rateBigDecimal).divide(BigDecimal.valueOf(orderNum), 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return convertedPrice;
    }

    /**
     * 修改归档订单系统备注
     */
    @Override
    public void updateArchiveTradeSysMemo(Staff staff, Long sid, String sysMemo, Long[] fastSysMemoIds) {
        if (fastSysMemoIds != null && fastSysMemoIds.length > 0) {
            fastSysMemoService.updateUseTime(staff, fastSysMemoIds);
        }
        tradeUpdateSellerMemoFlagBusiness.updateArchiveTrade(staff, sid, sysMemo);
    }

}

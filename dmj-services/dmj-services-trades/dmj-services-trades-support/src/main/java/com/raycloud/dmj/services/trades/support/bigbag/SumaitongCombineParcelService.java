package com.raycloud.dmj.services.trades.support.bigbag;

import com.alibaba.fastjson.JSON;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.dao.trade.TradeCombineParcelDAO;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeParcelDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.logistics.EnumCombineParcelNum;
import com.raycloud.dmj.domain.platform.trades.smt.OpenParcelOrderList;
import com.raycloud.dmj.domain.platform.trades.smt.SmtCommitRequest;
import com.raycloud.dmj.domain.platform.trades.smt.SmtCommitResponse;
import com.raycloud.dmj.domain.platform.trades.smt.SubbagDetail;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.merchantCode.base.SumaitongBigbagEnum;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.platform.trades.smt.ISumaitongFmService;
import com.raycloud.dmj.services.trades.ILogisticsProviderService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SumaitongCombineParcelService extends CombineParcelAbstractHandler {

    private static final Logger logger = Logger.getLogger(SumaitongCombineParcelService.class);

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    IUserService userService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    private IFileUploadService fileUploadService;

    @Resource
    private ICache cache;

    @Resource
    ILogisticsProviderService logisticsProviderService;

    @Resource
    ISumaitongFmService sumaitongFmService;

    @Override
    public void afterPropertiesSet() throws Exception {
        CombineParcelFactory.register(CommonConstants.PLAT_FORM_TYPE_SUMAITONG, this);
    }

    @Override
    @Transactional
    public void addParcelToCombine(Staff staff, List<Trade> tradesList, Long taobaoId) {
        for (Trade trade : tradesList) {
            if (trade.getTradeExt() == null) {
                throw new RuntimeException("存在订单没有附表信息");
            }
            if (StringUtils.isBlank(trade.getTradeExt().getShippingCarrier())) {
                throw new RuntimeException("缺少物流公司信息");
            }
            if (trade.getTradeExt().getCombineParcelId() != null && trade.getTradeExt().getCombineParcelId() != 0) {
                throw new RuntimeException("存在已上传的订单");
            }
        }
        //key=平台-系统发货仓库-中转仓库
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : tradesList) {
            if (StringUtils.isEmpty(trade.getTradeExt().getWrapperDescription())) {
                throw new RuntimeException("速卖通物流中转信息为空");
            }
            trade.getTradeExt().setTransitWarehouseId(trade.getTradeExt().getWrapperDescription());
            String key = trade.getSource() + trade.getWarehouseId() + trade.getTradeExt().getTransitWarehouseId();
            tradeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(trade);
        }

        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            List<Trade> tempTrades = entry.getValue();
            Map<String, Object> combineParcelCondition = new HashMap<>();
            combineParcelCondition.put("source", tempTrades.get(0).getSource());
            combineParcelCondition.put("consignWarehouseId", tempTrades.get(0).getWarehouseId());
            combineParcelCondition.put("transferWarehouseId", tempTrades.get(0).getTradeExt().getTransitWarehouseId());
            //根据来源，发货仓库和中转仓库查询大包列表
            List<TradeCombineParcel> allCombineParcel = tradeCombineParcelDAO.queryValidParcel(staff, combineParcelCondition);
            List<TradeCombineParcel> combineParcels = allCombineParcel.stream().filter(e -> StringUtils.isEmpty(e.getPlatformBatchNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(combineParcels)) {
                //如果没有查询到列表，则创建一个新的大包，将同一个key的trade放到一个大包中
                createNewCombineParcel(staff, tempTrades);
            } else {
                List<TradeCombineParcel> notEmptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() > 0).collect(Collectors.toList());
                //过滤大包中的小包数量是否大于0
                if (CollectionUtils.isNotEmpty(notEmptyCombineParcels)) {
                    //已存在大包数据
                    TradeCombineParcel notEmptyCombineParcel = notEmptyCombineParcels.get(0);
                    if ((EnumCombineParcelNum.SMT.getMaxNum() - notEmptyCombineParcel.getParcelNum()) < tempTrades.size()) {
                        //速卖通最多放入200条小包数据，如果大包中数据超过200条，则分多个包裹放入
                        int leftNum = EnumCombineParcelNum.SMT.getMaxNum() - notEmptyCombineParcel.getParcelNum();
                        List<Trade> leftTrades = tempTrades.subList(0, leftNum);
                        List<Trade> nextTrades = tempTrades.subList(leftNum, tempTrades.size());
                        //当前包裹还能装下更多的小包订单，大包没有满
                        addToOldCombineParcel(staff, leftTrades, notEmptyCombineParcel);
                        //筛选当前大包中的包裹数量是否为0，如果为0则不为空，则还有其他大包未装满，填装其他大包中
                        List<TradeCombineParcel> emptyCombineParcels = combineParcels.stream().filter(x -> x.getParcelNum() == 0).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(emptyCombineParcels)) {
                            createNewCombineParcel(staff, nextTrades);
                        } else {
                            addToOldCombineParcel(staff, nextTrades, emptyCombineParcels.get(0));
                        }
                    } else {
                        addToOldCombineParcel(staff, tempTrades, notEmptyCombineParcel);
                    }
                } else {
                    addToOldCombineParcel(staff, tempTrades, combineParcels.get(0));
                }
            }
        }
    }

    private void addToOldCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        List<TradeParcel> oldTradeParcels = tradeParcelDAO.queryBySidsAndCombineParcelId(staff, trades.stream().map(Trade::getSid).toArray(Long[]::new), combineParcel.getId());
        if (CollectionUtils.isNotEmpty(oldTradeParcels)) {
            StringBuilder builder = new StringBuilder();
            for (TradeParcel oldTradeParcel : oldTradeParcels) {
                builder.append(oldTradeParcel.getSid()).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            throw new RuntimeException("[" + builder.toString() + "]对应的小包已存在");
        }
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcel.getId());
        tradeParcelDAO.batchInsert(staff, parcels);
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcel.getId());
            //添加系统日志
            String action = "移入:";
            String content = "速卖通首公里预报移入大包：" + combineParcel.getId();
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        TradeCombineParcel increase = new TradeCombineParcel();
        increase.setId(combineParcel.getId());
        increase.setParcelNum(combineParcel.getParcelNum() + parcels.size());
        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(increase));
        if (TradeCombineParcel.UPLOAD_STATUS_UPLOADED == combineParcel.getUploadStatus()) {
            TradeCombineParcel update = new TradeCombineParcel();
            update.setId(combineParcel.getId());
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
        }
    }

    private void createNewCombineParcel(Staff staff, List<Trade> trades) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseId(trades.get(0).getTradeExt().getTransitWarehouseId());
        combineParcel.setTransferWarehouseName(trades.get(0).getTradeExt().getStoreName());
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);

        tradeCombineParcelDAO.insert(staff, combineParcel);

        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);

        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            String content = "速卖通首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);
    }


    /**
     * 生成系统日志
     *
     * @param staff
     * @param Sid
     * @param action
     * @param content
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    private List<TradeParcel> buildParcelsCreate(List<Trade> trades, Long combineParcelId) {
        return trades.stream().map(x -> {
            TradeParcel parcel = new TradeParcel();
            parcel.setCombineParcelId(combineParcelId);
            parcel.setTaobaoId(x.getTaobaoId());
            parcel.setShippingCarrier(x.getTradeExt().getShippingCarrier());
            if (x.getTradeExt().getLogisticId() != null) {
                parcel.setLogisticId(Integer.valueOf(x.getTradeExt().getLogisticId().intValue()));
            }
            parcel.setSid(x.getSid());
            parcel.setTid(x.getTid());
            parcel.setOutSid(x.getOutSid());
            parcel.setWeight(x.getWeight());
            parcel.setNetWeight(x.getNetWeight());
            parcel.setVolume(x.getVolume());
            parcel.setUploadStatus(TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
            parcel.setLPOrderCode(x.getTradeExt().getMallMaskName());
            return parcel;
        }).collect(Collectors.toList());
    }

    @Override
    public UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel) throws Exception {
        User user = userService.queryByTaobaoId(staff.getCompanyId(), combineParcel.getTaobaoId());
        if ((TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD != combineParcel.getStatus() && TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED != combineParcel.getStatus())) {
            throw new RuntimeException("没有可用的大包");
        }
        combineParcel.setTemplateId(param.getTemplateId());
        combineParcel.setTrackingNo(param.getTrackingNo());
        //1.查询指定大包里的所有小包
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, null);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("当前大包中没有小包");
        }
        //2.查询仓库地址
        Warehouse warehouse = warehouseService.queryById(combineParcel.getConsignWarehouseId());
        UploadTradeParcelResult result = new UploadTradeParcelResult();
        //3.提交上传操作
        TradeCombineParcel combineParcelCommint  =bigbagCommint(staff, combineParcel, user, sumaitongFmService, result, warehouse, parcels);
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_UPLOADED);
            //速卖通上传成功后返回提示信息,如果上传平台大包并成功返回的时间为14点之前
            LocalDateTime dateTime = LocalDateTime.now();
            Map<String, String> success = new HashMap<>();
            success.put("platformBatchNo", combineParcelCommint.getPlatformBatchNo());
            if (dateTime.getHour() < 13) {
                success.put("message", "上传成功的大包，司机将在今天上门揽收，请准备货物，如有需要请和司机联系，谢谢");
            } else {
                success.put("message", "上传成功的大包，司机将在明天上门揽收，请准备货物，如有需要请和司机联系，谢谢");
            }
            result.setSuccessMessage(JSON.toJSONString(success));
        }
        return result;

    }

    /**
     * sumaitong大包上传平台或更新
     *
     * @param staff
     * @param combineParcel
     * @param user
     * @param sumaitongFmService
     * @param result
     * @param warehouse
     */
    private TradeCombineParcel bigbagCommint(Staff staff, TradeCombineParcel combineParcel,
                               User user, ISumaitongFmService sumaitongFmService, UploadTradeParcelResult result,
                               Warehouse warehouse, List<TradeParcel> parcels) {
        List<TradeParcel> successList = new ArrayList<>();
        List<TradeParcel> failList = new ArrayList<>(1);
        TradeCombineParcel combineParcelCommint = new TradeCombineParcel();
        combineParcelCommint.setGatherType(combineParcel.getGatherType());
        combineParcelCommint.setId(combineParcel.getId());
        combineParcelCommint.setTaobaoId(combineParcel.getTaobaoId());

        //揽货批次号为空，新建提交
        if (StringUtils.isEmpty(combineParcel.getPlatformBatchNo())) {
            SmtCommitRequest commitRequest = buildCommintRequest(staff, combineParcel, warehouse, parcels);
            SmtCommitResponse commitResponse = sumaitongFmService.handoverCommit(user, commitRequest);
            //新增大包成功，更改大包状态，
            combineParcelCommint.setPlatformBatchNo(commitResponse.getHandoverContentCode());
            combineParcelCommint.setPlatformContentId(String.valueOf(commitResponse.getHandoverContentId()));
            combineParcelCommint.setPlatformOrderId(String.valueOf(commitResponse.getHandoverOrderId()));
            combineParcelCommint.setTrackingNo(combineParcel.getTrackingNo());
            combineParcelCommint.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
            combineParcelCommint.setStatus(TradeCombineParcel.STATUS_OUTBOUNDED);
            successList.addAll(parcels.stream().filter(parcel -> parcel.getUploadStatus() != TradeParcel.UPLOAD_STATUS_UPLOADED).collect(Collectors.toList()));
        }
        tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(combineParcelCommint));
        result.setSuccessList(successList);
        result.setFailList(failList);
        //打印组包上传日志
        printPlatformBatchNo(successList, failList, staff, combineParcelCommint);
        return combineParcelCommint;
    }

    private SmtCommitRequest buildCommintRequest(Staff staff, TradeCombineParcel combineParcel, Warehouse warehouse, List<TradeParcel> parcels) {
        SmtCommitRequest smtCommitRequest = new SmtCommitRequest();
        List<String> orderCode = parcels.stream().map(TradeParcel::getLPOrderCode).collect(Collectors.toList());
        smtCommitRequest.setOrderCodeList(StringUtils.join(orderCode, ","));
        Map<Long, List<TradeParcel>> parcelMap = parcels.stream().collect(Collectors.groupingBy(TradeParcel::getTaobaoId));
        List<SmtCommitRequest.ParcelOrderList> parcelOrderLists = new ArrayList<>(parcelMap.size());
        for (Map.Entry<Long, List<TradeParcel>> parcel : parcelMap.entrySet()) {
            SmtCommitRequest.ParcelOrderList parcelOrder = new SmtCommitRequest.ParcelOrderList();
            parcelOrder.setSellerId(parcel.getKey());
            parcelOrder.setOrderCodeList(StringUtils.join(parcel.getValue().stream().map(TradeParcel::getLPOrderCode).collect(Collectors.toList()), ","));
            parcelOrderLists.add(parcelOrder);
        }
        smtCommitRequest.setSellerParcelOrderList(parcelOrderLists);
        SmtCommitRequest.ReturnerDto returnerDto = new SmtCommitRequest.ReturnerDto();
        SmtCommitRequest.AddressDto addressDto = new SmtCommitRequest.AddressDto();
        addressDto.setDetailAddress(warehouse.getAddress());
        addressDto.setStreet(warehouse.getDistrict());
        addressDto.setDistrict(warehouse.getDistrict());
        addressDto.setCity(warehouse.getCity());
        addressDto.setProvince(warehouse.getState());
        addressDto.setZipCode(warehouse.getAddressCode());
        addressDto.setCountry("中国");
        returnerDto.setAddress(addressDto);
        returnerDto.setName(warehouse.getContact());
        returnerDto.setMobile(warehouse.getContactPhone());
        smtCommitRequest.setReturnInfo(returnerDto);
        SmtCommitRequest.PickupDto pickupDto = new SmtCommitRequest.PickupDto();
        pickupDto.setAddress(addressDto);
        pickupDto.setName(warehouse.getContact());
        pickupDto.setMobile(warehouse.getContactPhone());
        smtCommitRequest.setPickupInfo(pickupDto);
        smtCommitRequest.setWeight(Long.valueOf(parcels.stream().mapToDouble(TradeParcel::getWeight).sum() == 0 ? 1 : (int) (parcels.stream().mapToDouble(TradeParcel::getWeight).sum())));
        smtCommitRequest.setWeightUnit("kg");
        smtCommitRequest.setType(TradeCombineParcel.EnumGatherType.getCodeByType(combineParcel.getGatherType()));

        if (StringUtils.isNotBlank(combineParcel.getTrackingNo())) {
            UserWlbExpressTemplate template = logisticsProviderService.queryWlbExpressByTemplateId(staff, combineParcel.getTemplateId());
            SumaitongBigbagEnum bigwigEnum = SumaitongBigbagEnum.getEnum(template.getCpCode());
            smtCommitRequest.setDomesticLogisticsCompany(bigwigEnum.getServiceName());
            smtCommitRequest.setDomesticLogisticsCompanyId(bigwigEnum.getLogisticsCode());
            smtCommitRequest.setDomesticTrackingNo(combineParcel.getTrackingNo());
        }
        smtCommitRequest.setAppointmentType(Objects.equals(combineParcel.getAppointmentType(), 0) ? TradeCombineParcel.APPOINTMENT_BIGBAG : TradeCombineParcel.APPOINTMENT_BATCH);
        SmtCommitRequest.UserInfoDto userInfoDto = new SmtCommitRequest.UserInfoDto();
        userInfoDto.setTopUserKey(String.valueOf(combineParcel.getCompanyId()));
        smtCommitRequest.setUserInfo(userInfoDto);
        return smtCommitRequest;
    }

    private void updateParcelStatus(Staff staff, List<TradeParcel> parcels, int uploadStatus) {
        List<TradeParcel> parcelUpdates = parcels.stream().map(x -> {
            TradeParcel parcelUpdate = new TradeParcel();
            parcelUpdate.setId(x.getId());
            parcelUpdate.setUploadStatus(uploadStatus);
            return parcelUpdate;
        }).collect(Collectors.toList());

        tradeParcelDAO.batchUpdate(staff, parcelUpdates);
    }

    /**
     * 组包上传打印日志，记录揽货批次号
     *
     * @param successList        上传成功的订单
     * @param failList           上传失败的订单
     * @param tradeCombineParcel 组包信息
     * <AUTHOR>
     * @date 2022/1/16 下午2:42
     */
    void printPlatformBatchNo(List<TradeParcel> successList, List<TradeParcel> failList, Staff staff, TradeCombineParcel tradeCombineParcel) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<Long> failSid = new ArrayList<>();
        //添加上传失败的订单日志
        for (TradeParcel tradeParcel : failList) {
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), tradeParcel.getSid(), "组包上传", staff.getName(), new Date(), "组包上传：组包上传失败，失败原因：" + tradeParcel.getReason());
            tradeTraces.add(tradeTrace);
            failSid.add(tradeParcel.getSid());
        }
        //添加上传成功的订单日志
        successList.stream().filter(item -> !failSid.contains(item.getSid())).forEach(item -> {
            String content;
            if (TradeCombineParcel.EnumGatherType.CAINIAO_PICKUP.getType().equals(tradeCombineParcel.getGatherType())) {
                //上门揽收
                content = "组包上传：组包上传成功（上门揽收），揽货批次号：" + tradeCombineParcel.getPlatformBatchNo();
            } else {
                //快递寄送
                content = "组包上传：组包上传成功（快递寄送）";
            }
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), item.getSid(), "组包上传", staff.getName(), new Date(), content);
            tradeTraces.add(tradeTrace);
        });
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return;
        }
        try {
            //入库
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "入库组包上传日志时抛出异常"));
        }
    }

    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
    }

    @Transactional
    @Override
    void cancelCombineParcel(Staff staff, TradeCombineParcel parcel) {
        //订单状态为已出库的，不能进行取消操作
        List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
        //判断如果当前大包中未存在小包，则直接置为取消状态
        if (parcel.getParcelNum() == 0) {
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
        } else {
            if (StringUtils.isNotEmpty(parcel.getPlatformBatchNo())) {
                User user = userService.queryByTaobaoId(staff.getCompanyId(), parcel.getTaobaoId());
                sumaitongFmService.handoverCancel(user, Long.valueOf(parcel.getPlatformContentId()));
            }
        }
        // 待上传组包，移出组包
        if (CollectionUtils.isNotEmpty(parcelList)) {
            //将小包移出大包
            removeParcel(staff, parcel.getId(), parcelList.stream().map(TradeParcel::getId).toArray(Long[]::new));
        }
        //更改大包状态为已取消
        updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeParcel(Staff staff, Long combineParcelId, Long[] parcelIds) {
        if (combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);

        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.stream().forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "速卖通首公里预报移出大包：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });

    }

    @Override
    CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception {
        throw new RuntimeException("速卖通不支持小包取消上传");
    }

    @Override
    public TradeCombineParcelResponse getPlatformBatchNoPrintData(TradeCombineParcel tradeCombineParcel, Map<Long, Warehouse> warehouseMap, Staff staff, String subbagId) {
        User user = userService.queryByTaobaoId(staff.getCompanyId(), tradeCombineParcel.getTaobaoId());
        TradeCombineParcelResponse response = new TradeCombineParcelResponse();
        response.setPlatformBatchNo(tradeCombineParcel.getPlatformBatchNo());

        if (StringUtils.isNotBlank(subbagId)) {
            //单个子包打印
            String fileUrl = uploadPdf(user, tradeCombineParcel, subbagId);
            response.setPdfUrlList(Collections.singletonList(fileUrl));
            return response;
        } else {
            String fileUrl = uploadPdf(user, tradeCombineParcel, tradeCombineParcel.getPlatformContentId());
            response.setPlatformBatchNo(tradeCombineParcel.getPlatformBatchNo());
            response.setPdfUrlList(Collections.singletonList(fileUrl));
            return response;
        }
    }

    private String uploadPdf(User user, TradeCombineParcel tradeCombineParcel, String platformContentId) {
        String fileUrl = "";
        try {
            fileUrl = cache.get(String.format("%s_%s", tradeCombineParcel.getCompanyId(), platformContentId));
        } catch (CacheException e) {
            logger.info("缓存查询失败");
        }
        if (StringUtils.isEmpty(fileUrl)) {
            String printData = sumaitongFmService.handoverPdfGet(user, Long.valueOf(platformContentId));
            String fileName = String.format("%s_%s.txt", tradeCombineParcel.getCompanyId(), platformContentId);
            InputStream inputStream = new ByteArrayInputStream(printData.getBytes(StandardCharsets.UTF_8));
            FileResult result = fileUploadService.uploadToTemp(fileName, inputStream, printData.getBytes(StandardCharsets.UTF_8).length);
            if (result != null && StringUtils.isNotEmpty(result.getUrl())) {
                try {
                    fileUrl = result.getUrl();
                    cache.set(String.format("%s_%s", tradeCombineParcel.getCompanyId(), platformContentId), result.getUrl(), 3600 * 24);
                } catch (CacheException e) {
                    logger.info("缓存查询失败");
                }
            } else {
                throw new RuntimeException("速卖通打印文件上传失败");
            }
        }
        return fileUrl;
    }


    @Override
    @Transactional
    public void addParcelToCombineAndSplit(Staff staff, List<Trade> tradeList, TradeGroupParcelRequest request) {
        Assert.notNull(request.getAppointmentType(), "约揽方式不能为空");
        List<Long> waningList = new ArrayList<>();
        for (Trade trade : tradeList) {
            // DeliveryType 1 上门揽收，2 自行配送 3快递寄送
            // GatherType  2 快递寄送  3上门揽收  4 自行配送
            if (!Objects.equals(trade.getTradeExt().getDeliveryType(), TradeCombineParcel.EnumGatherType.getEnumGatherType(request.getGatherType()).getGatherType())) {
                waningList.add(trade.getSid());
            }
        }
        if(CollectionUtils.isNotEmpty(waningList)){
            throw new RuntimeException(String.format("所选小包的交货方式不一致，sid:%s", waningList.toString()));
        }
        createNewCombineParcel(staff, tradeList, request);
    }

    private void createNewCombineParcel(Staff staff, List<Trade> trades, TradeGroupParcelRequest request) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        if (request != null && request.getTaobaoId() != null) {
            combineParcel.setTaobaoId(request.getTaobaoId());
        } else {
            combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        }
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseId(Objects.equals(TradeCombineParcel.STATUS_OUTBOUNDED, request.getGatherType()) ? request.getAbroadAddressId() : trades.get(0).getTradeExt().getTransitWarehouseId());
        combineParcel.setTransferWarehouseName(trades.get(0).getTradeExt().getStoreName());
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
        combineParcel.setAppointmentType(request.getAppointmentType());
        combineParcel.setTrackingNo(request.getTrackingNo());
        combineParcel.setTemplateId(request.getTemplateId());
        combineParcel.setTemplateName(request.getTemplateName());
        combineParcel.setGatherType(request.getGatherType());
        tradeCombineParcelDAO.insert(staff, combineParcel);

        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);

        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            String content = "速卖通首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);

    }

    public String handoverContentQuery(Staff staff, TradeCombineParcel combineParcel) {
        if (StringUtils.isEmpty(combineParcel.getPlatformBatchNo())) {
            return "草稿";
        }
        User user = userService.queryByTaobaoId(staff.getCompanyId(), combineParcel.getTaobaoId());
        String statusName = sumaitongFmService.handoverContentQuery(user, combineParcel.getPlatformBatchNo());
        return statusName;
    }


    public Map<String, OpenParcelOrderList> handoverContentQueryList(Staff staff, TradeCombineParcel combineParcel) {
        Map<String, OpenParcelOrderList> openParcelOrderDtoHashMap = new HashMap<>();
        User user = userService.queryByTaobaoId(staff.getCompanyId(), combineParcel.getTaobaoId());
        List<OpenParcelOrderList> openParcelOrderDtos = sumaitongFmService.handoverContentQueryList(user, combineParcel.getPlatformBatchNo());
        if (CollectionUtils.isNotEmpty(openParcelOrderDtos)) {
            return openParcelOrderDtos.stream().collect(Collectors.toMap(OpenParcelOrderList::getOrderCode, Function.identity(), (v1, v2) -> v1));
        }
        return openParcelOrderDtoHashMap;
    }

    @Transactional
    public void addCombineParcel(Staff staff, TradeCombineParcel tradeCombineParcel, TradeCombineParcel param) {
        List<SubbagDetail> subbagDetailAll = new ArrayList<>();
        List<SubbagDetail> subbagDetail = JSON.parseArray(tradeCombineParcel.getExtraFields(), SubbagDetail.class);
        if (CollectionUtils.isNotEmpty(subbagDetail)) {
            subbagDetailAll.addAll(subbagDetail);
        }
        if (CollectionUtils.isNotEmpty(subbagDetailAll)) {
            if (subbagDetailAll.size() + param.getAddCombineParcelNum() > EnumCombineParcelNum.SMT_MAX_SUBBAG.getMaxNum()) {
                throw new RuntimeException("当前约揽批次加包最大不能超过50个!");
            }
        }
        User user = userService.queryByTaobaoId(staff.getCompanyId(), tradeCombineParcel.getTaobaoId());
        List<SubbagDetail> subbagDetailVos = sumaitongFmService.handoverSubbagAdd(user, tradeCombineParcel.getPlatformBatchNo(), param.getAddCombineParcelNum());
        if (CollectionUtils.isEmpty(subbagDetailVos)) {
            throw new RuntimeException("当前约揽批次加包失败");
        }
        subbagDetailAll.addAll(subbagDetailVos);
        TradeCombineParcel updateCombineParcel = new TradeCombineParcel();
        updateCombineParcel.setId(tradeCombineParcel.getId());
        updateCombineParcel.setMaxCombineParcelNum(subbagDetailAll.size());
        updateCombineParcel.setExtraFields(JSON.toJSONString(subbagDetailAll));
        tradeCombineParcelDAO.batchUpdate(staff, Arrays.asList(updateCombineParcel));
    }
}

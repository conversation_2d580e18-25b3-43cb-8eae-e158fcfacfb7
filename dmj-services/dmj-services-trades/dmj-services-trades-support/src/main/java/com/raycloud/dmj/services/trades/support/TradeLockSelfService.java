package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.operate.SelfLockTradeBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.ITradeLockSelfService;
import com.raycloud.dmj.services.trades.TradeSelfLockException;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Service
public class TradeLockSelfService implements ITradeLockSelfService {
    private final Logger logger = Logger.getLogger(this.getClass());
    /**
     * 直接转发的VIP的请求路径
     */
    private final String VIP_URL_HTTPS = "https://viperp.superboss.cc/trade/lockSelfVIP";

    @Resource
    private SelfLockTradeBusiness selfLockTradeBusiness;

    @Override
    public JSONObject smartSelfLockTrade(User user, JSONObject paramsObject, String source) {
        return dealWithLockSelf(user, paramsObject, source);
    }

    @Override
    public JSONObject selfLockTrade2VIP(User user, JSONObject paramsObject, String source) {
        Integer errorCode;
        try {
            JSONObject body = new JSONObject();
            body.put("paramsObject", paramsObject);
            body.put("source", source);
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            logger.debug(LogHelper.buildLogHead(user).append("VIP接口请求参数:").append(body.toJSONString()));
            HttpEntity<String> fromEntity = new HttpEntity<String>(body.toJSONString(), headers);
            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.postForObject(VIP_URL_HTTPS, fromEntity, String.class, new Object());
            JSONObject respResult = JSONObject.parseObject(response);
            if (respResult != null) {
                JSONObject result = respResult.getJSONObject("result");
                if (result != null) {
                    boolean success = result.getBoolean("success");
                    if (success) {
                        logger.debug(LogHelper.buildLogHead(user).append("VIP锁单成功:").append(JSON.toJSONString(response)));
                    } else {
                        logger.debug(LogHelper.buildLogHead(user).append("VIP锁单失败:").append(JSON.toJSONString(response)));
                    }
                } else {
                    logger.debug(LogHelper.buildLogHead(user).append("VIP锁单失败:").append(JSON.toJSONString(response)));
                }
            } else {
                logger.debug(LogHelper.buildLogHead(user).append("VIP锁单失败:").append(JSON.toJSONString(response)));
            }
            return selfLockTradeBusiness.buildResultObject(true, 200, "VIP锁单成功");
        } catch (Exception e) {
            errorCode = TradeSelfLockException.RESULT_NULL;
            logger.error(LogHelper.buildLogHead(user).append(String.format("[userId=%s,nick=%s]VIP锁单失败出错", user.getId(), user.getNick())), e);
        }
        return selfLockTradeBusiness.buildResultObject(false, errorCode, "VIP锁单失败");
    }

    @Override
    public JSONObject selfLockTradeForVIP(User user, JSONObject paramsObject, String source) {
        Integer errorCode;
        try {
            return dealWithLockSelf(user, paramsObject, source);
        } catch (Exception e) {
            errorCode = TradeSelfLockException.RESULT_NULL;
            logger.error(LogHelper.buildLogHead(user).append(String.format("[userId=%s,nick=%s]VIP锁单失败出错", user.getId(), user.getNick())), e);
        }
        return selfLockTradeBusiness.buildResultObject(false, errorCode, "VIP锁单失败");
    }

    private JSONObject dealWithLockSelf(User user, JSONObject paramsObject, String source) {
        String callType = paramsObject.getString("callType");
        JSONObject bizOrder = paramsObject.getJSONObject("bizOrder");
        String tid = bizOrder.getString("tid");
        String subOrderIds = bizOrder.getString("subOrderIds");
        if (callType == null || tid == null || subOrderIds == null) {
            logger.debug(LogHelper.buildLogHead(user).append(tid).append(subOrderIds).append(callType)
                    .append("qimen".equals(source) ? " 奇门自助锁单参数" : " 第三方自助锁单参数"));
            throw new TradeSelfLockException(TradeSelfLockException.INFO_EORROR, "参数传输有误");
        }
        logger.debug(LogHelper.buildLogHead(user).append(tid)
                .append("qimen".equals(source) ? " 奇门自助锁单开始" : " 第三方自助锁单开始"));
        return selfLockTradeBusiness.smartSelfLockTrade(user, tid, subOrderIds, callType, source);
    }

}

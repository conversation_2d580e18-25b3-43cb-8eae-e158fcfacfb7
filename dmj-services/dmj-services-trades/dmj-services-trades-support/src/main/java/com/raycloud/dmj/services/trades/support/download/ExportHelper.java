package com.raycloud.dmj.services.trades.support.download;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cglib.beans.BeanMap;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导出辅助类
 */
@Slf4j
public class ExportHelper {

    /**
     * 标题列表，可能存在多个，第一个为列表的标题，第二个为明细的标题
     */
    private final List<String[]> titleArrayList;

    /**
     * 字段列表，可能存在多个，第一个为列表的字段，第二个为明细的字段
     */
    private final List<List<String>> fieldList;

    @SafeVarargs
    private ExportHelper(List<ExportColumnVo>... columnListArray) {
        titleArrayList = Lists.newArrayList();
        fieldList = Lists.newArrayList();
        Stream.of(columnListArray).forEach(columnList -> {
            titleArrayList.add(columnList.stream().map(ExportColumnVo::getTitle).toArray(String[]::new));
            fieldList.add(columnList.stream().map(ExportColumnVo::getField).collect(Collectors.toList()));
        });
    }

    @SafeVarargs
    public static ExportHelper buildExportHelper(List<ExportColumnVo>... columnListArray) {
        return new ExportHelper(columnListArray);
    }

    public String[] getTitleArray() {
        return titleArrayList.stream().flatMap(Stream::of).toArray(String[]::new);
    }

    public String[] buildDataArray(int index, Object data) {
        BeanMap beanMap = BeanMap.create(data);
        if (beanMap == null) {
            return new String[0];
        }
        return fieldList.get(index).stream().map(field -> String.valueOf(Optional.ofNullable(beanMap.get(field)).orElse(StringUtils.EMPTY))).toArray(String[]::new);
    }
}

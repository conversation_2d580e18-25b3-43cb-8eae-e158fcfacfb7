package com.raycloud.dmj.services.trades;


import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.base.CommonLogBuilder;
import com.raycloud.dmj.business.common.SharedCacheBusiness;
import com.raycloud.dmj.dao.trade.SeparateWeightConfDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.SeparateWeightConf;
import com.raycloud.dmj.domain.trades.SeparateWeightExpressConf;
import com.raycloud.dmj.domain.trades.SeparateWeightMachineConf;
import com.raycloud.dmj.services.account.IStaffService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.Assert;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SeparateWeightConfService
 *
 * <AUTHOR>
 * @Date 2019/9/17
 * @Time 15:59
 */
@Service
public class SeparateWeightConfService implements ISeparateWeightConfService{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    SeparateWeightConfDao separateWeightConfDao;
    @Resource
    IStaffService staffService;
    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    SharedCacheBusiness sharedCacheBusiness;

    @Override
    public SeparateWeightConf getConf(Staff staff, boolean convert) {
        SeparateWeightConf conf = getConf(staff);
        convertStr(staff, conf, convert);
        return conf;
    }

    private SeparateWeightConf getConf(Staff staff) {
        String key = "all_separate_weight_conf_" + staff.getCompanyId();
        SeparateWeightConf conf = sharedCacheBusiness.get(key);
        if (conf != null) {
            return conf;
        }
        conf = separateWeightConfDao.getConf(staff);
        if (conf == null) {
            conf = new SeparateWeightConf();
            init(staff, conf);
            separateWeightConfDao.update(staff, conf);
            conf = separateWeightConfDao.getConf(staff);
        }
        sharedCacheBusiness.set(key,conf,600);
        return conf;
    }

    private void convertStr(Staff staff, SeparateWeightConf conf, boolean convert) {
        if (StringUtils.isNotBlank(conf.getMachineConfStr())) {
            JSONObject machineConfsJSON = JSONObject.parseObject(conf.getMachineConfStr());
            Map<Long, String> staffMap =
                    convert ? Optional.ofNullable(staffService.queryByCompanyId(staff.getCompanyId(), staff.getCompanyName())).orElse(new ArrayList<>())
                            .stream().collect(Collectors.toMap(Staff::getId, Staff::getName, (a, b) -> b))
                            : new HashMap<>();
            List<SeparateWeightMachineConf> machineConfs = new ArrayList<>();
            for (Map.Entry<String, Object> entry : machineConfsJSON.entrySet()) {
                SeparateWeightMachineConf machineConf = new SeparateWeightMachineConf();
                machineConf.setStaffId((Long) entry.getValue());
                machineConf.setName(String.valueOf(entry.getKey()));
                machineConf.setStaffName(staffMap.get(machineConf.getStaffId()));
                machineConfs.add(machineConf);
            }
            conf.setMachineConfs(machineConfs);
        }
        // 优先读取新配置
        List<SeparateWeightExpressConf> confs = new ArrayList<>();
        if (StringUtils.isNotBlank(conf.getExpressChannelConfList())) {
            try {
                // 解析新配置
                confs = JSONObject.parseArray(conf.getExpressChannelConfList(), SeparateWeightExpressConf.class);
            } catch (JSONException e) {
                logger.error("新配置解析失败", e);
            }
        } else if (StringUtils.isNotBlank(conf.getExpressChannelConfStr())) { // 兼容旧配置
            JSONObject expressChannelConfJSON = JSONObject.parseObject(conf.getExpressChannelConfStr());
            Map<Long, ExpressCompany> expressCompanyIdMap = expressCompanyService.getExpressCompanyIdMap();
            for (Map.Entry<String, Object> entry : expressChannelConfJSON.entrySet()) {
                SeparateWeightExpressConf expressConf = new SeparateWeightExpressConf();
                expressConf.setChannel(String.valueOf(entry.getValue()));
                expressConf.setExpressId(Long.valueOf(entry.getKey()));
                expressConf.setExpressName(expressCompanyIdMap.get(expressConf.getExpressId()).getName());
                confs.add(expressConf);
            }
        }
        conf.setExpressChannelConfs(confs);
    }

    private void init(Staff staff, SeparateWeightConf obj) {
        if (obj == null) return;
        obj.setCompanyId(staff.getCompanyId());
        if (obj.getExpressChannelConfs() != null) {
            // 新环境存储到新配置字段里
            obj.setExpressChannelConfList(JSONObject.toJSONString(obj.getExpressChannelConfs()));
        }
        if (obj.getMachineConfs() != null) {
            obj.setMachineConfStr(JSONObject.toJSONString(
                    obj.getMachineConfs().stream().
                            collect(Collectors.toMap(SeparateWeightMachineConf::getName, SeparateWeightMachineConf::getStaffId, (a, b) -> b))));
        }

    }

    @Override
    public void updateConf(Staff staff, SeparateWeightConf conf) {
        Assert.isTrue(conf != null, "需要修改的数据不存在");
        init(staff, conf);
        logUpdate(staff, separateWeightConfDao.getConf(staff), conf);
        separateWeightConfDao.update(staff, conf);
        String key = "all_separate_weight_conf_" + staff.getCompanyId();
        sharedCacheBusiness.delete(key);
    }


    private void logUpdate(Staff staff, SeparateWeightConf origin, SeparateWeightConf conf) {
        List<String> logs = new ArrayList<>();
        CommonLogBuilder builder = CommonLogBuilder.forWeigh(staff).append("一分多称重配置变更:").startWatch();
        if (conf.getWeightKind() != null && !Objects.equals(origin.getWeightKind(), conf.getWeightKind())) {
            builder.append("WeightKind:" +  origin.getWeightKind() +" -->"+ conf.getWeightKind());
        }
        if (conf.getMachineConfStr() != null && !StringUtils.equals(origin.getMachineConfStr(), conf.getMachineConfStr())) {
            builder.append("MachineConf:" +  origin.getMachineConfStr() +" -->"+ conf.getMachineConfStr());
        }
        if (conf.getExpressChannelConfStr() != null && !StringUtils.equals(origin.getExpressChannelConfStr(), conf.getExpressChannelConfStr())) {
            builder.append("ExpressChannelConfStr:" +  origin.getExpressChannelConfStr() +" -->"+ conf.getExpressChannelConfStr());
        }
        builder.printDebug(logger);
    }
}

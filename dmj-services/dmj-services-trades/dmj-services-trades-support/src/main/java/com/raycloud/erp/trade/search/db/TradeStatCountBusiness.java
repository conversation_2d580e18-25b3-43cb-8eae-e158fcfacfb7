package com.raycloud.erp.trade.search.db;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.trade.TradeStatBusiness;
import com.raycloud.dmj.dao.rowmapper.RowMapperContext;
import com.raycloud.dmj.dao.trade.TradeO2oDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemExcepts;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.label.TradeLabelCountStat;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.*;
import com.raycloud.dmj.domain.trades.vo.TradeLabelVo;
import com.raycloud.dmj.domain.trade.except.TradeExceptOldUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeCount;
import com.raycloud.dmj.domain.trades.TradeO2oCount;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.fx.FxTradeStatisticsData;
import com.raycloud.dmj.domain.trades.fx.FxTradeStatisticsParams;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.except.domain.TradeExceptCount;
import com.raycloud.dmj.except.enums.*;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.except.enums.ExceptOldEnum;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trade.except.TradeExceptCountBusiness;
import com.raycloud.dmj.services.trade.label.ITradeLabelService;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.web.pgl.*;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.*;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2019-01-07 17:14
 */
@Service
public class TradeStatCountBusiness {

    @Resource
    ITradeTagService tradeTagService;
    @Resource
    TradeQueryBuilder tradeSqlQueryBuilder;
    @Resource
    JdbcTemplateAdapter jdbcTemplateAdapter;
    @Resource
    DbAdapter dbAdapter;
    @Resource
    TradeStatBusiness tradeStatBusiness;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;


    @Resource
    ITradeLabelService tradeLabelService;

    @Resource
    TradeO2oDao tradeO2oDao;

    @Resource
    protected FeatureService featureService;
    @Resource
    private TradeExceptCountBusiness tradeExceptCountBusiness;


    private final Logger logger = Logger.getLogger(this.getClass());

    private static final Integer PG_TRADE_DB_NO = 8888;

    public long migrate(Staff staff) {
        long r = tradeStatBusiness.migrate(staff);
        Logs.info(LogHelper.buildLog(staff, String.format("migrate trade stat: %s", r)));
        return r;
    }

    public void repair(Staff staff, Long... sids) {
        tradeStatBusiness.update(staff, sids);
    }

    protected ExecutorService threadPool;

    @PostConstruct
    public void startup() {
        threadPool = new ThreadPoolExecutor(
                6,
                10,
                1000, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(12),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public TradeCount countPrint(Staff staff) {
        TradeCount tradeCount = new TradeCount();
        tradeCount.setSysStatusList(Lists.newArrayList(Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT));
        return count(staff,tradeCount);
    }

    public TradeCount count(Staff staff) {
        TradeCount tradeCount = new TradeCount();
        return count(staff,tradeCount);
    }


    public TradeCount count(Staff staff,TradeCount tradeCount) {
        QueryLogBuilder builder = new QueryLogBuilder(staff).append("红点统计 耗时统计").startTimer();

        List<CountTask> tasks = new ArrayList<>();

        countTradeExcept(staff, tradeCount,builder,tasks);
        tasks.add(this::countSysStatus4Normal);
        tasks.add(this::count4Label);
        tasks.add(this::countMemoAndMessage);
        tasks.add(this::count4O2o);

        if(ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getTradeStatThreadPool(),String.valueOf(staff.getCompanyId()))){
            builder.append("[并行]");
            List<Future<Void>> futures = new ArrayList<>();

            for (CountTask task : tasks) {
                Future<Void> submit = threadPool.submit(new Callable<Void>() {
                    @Override
                    public Void call() {
                        task.doCount(staff, tradeCount, builder);
                        return null;
                    }
                });
                futures.add(submit);
            }

            for (int j = 0; j < futures.size(); j++) {
                try {
                    futures.get(j).get();
                } catch (Exception e) {
                    new QueryLogBuilder(staff).append("订单红点统计异常").printWarn(logger,e);
                }
            }

        }else {
            builder.append("[串行]");
            for (CountTask task : tasks) {
                task.doCount(staff,tradeCount,builder);
            }
        }

        if(Objects.equals(tradeCount.getStatDestStockInsufficient(),1)){
            builder.reBaseTimer();
            countDestStockInsufficient(staff,tradeCount);
            builder.recordTimer("countDestStockInsufficient");
        }

        builder.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,staff.getCompanyId(), LogBusinessEnum.QUERY.getSign())? 1L:3000L).printDebug(logger);
        return tradeCount;
    }

    interface CountTask{
        void doCount(Staff staff,TradeCount tradeCount,QueryLogBuilder builder);
    }

    /**
     * 标签数量气泡
     * @param staff
     */
    public void count4Label(Staff staff, TradeCount tradeCount,QueryLogBuilder builder) {
        tradeCount.setLabelMap(doCount4Label(staff,new TradeLabelVo(),builder));
    }

    public Map<String, Integer>  doCount4Label(Staff staff, TradeLabelVo labelVo,QueryLogBuilder builder) {
        if (TradeDiamondUtils.closeTradeLabelCount(staff)) {
            return new HashMap<>();
        }
        List<TradeLabelCountStat> tradeLabelCountStats;

        Map<String,Object> params = Maps.newHashMap();
        List<Long> wareHouseIds = extractAllWarehouseIds(staff);
        if(CollectionUtils.isNotEmpty(wareHouseIds)){
            params.put("warehouseIds",wareHouseIds);
        }

        List<Long> userIds = Lists.newArrayList();
        List<String> sources = labelVo.getSource();
        if(CollectionUtils.isNotEmpty(sources)){
            userIds = staff.getUsers().stream().filter(u->!Objects.equals(u.getActive(),0) && sources.contains(u.getSource())).map(User::getId).collect(Collectors.toList());
        }else {
            userIds = staff.getUsers().stream().filter(u->!Objects.equals(u.getActive(),0)).map(User::getId).collect(Collectors.toList());
            userIds.add(Constants.FxDefaultUserId);
        }

        params.put("userIds",userIds);
        params.put("labelIds",labelVo.getLabelIds());

        if (TradeDiamondUtils.openTradeLabelNotConsignTable(staff)) {
            tradeLabelCountStats = tradeLabelService.queryNotConsignCountStat(staff,params);
            builder.recordTimer("notConsignLabel");
        } else {
            tradeLabelCountStats =tradeLabelService.queryCountStat(staff,params);
            builder.recordTimer("countLabel");
        }

        if (CollectionUtils.isEmpty(tradeLabelCountStats)) {
            return new HashMap<>();
        }
        Map<String, Integer> resultMap = new HashMap<>();
        tradeLabelCountStats.forEach(label -> {
            resultMap.put(String.valueOf(label.getLabelId()), label.getNum());
        });
        return resultMap;
    }

    public void count4O2o(Staff staff, TradeCount tradeCount,QueryLogBuilder builder) {

        if(!ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getCountTradeO2o(),String.valueOf(staff.getCompanyId()))){
            return;
        }

        List<TradeO2oCount> tradeO2oCounts;

        Map<String,Object> params = Maps.newHashMap();
        List<Long> wareHouseIds = extractAllWarehouseIds(staff);
        if(CollectionUtils.isNotEmpty(wareHouseIds)){
            params.put("warehouseIds",wareHouseIds);
        }

        List<Long> userIds = staff.getUsers().stream().filter(u->!Objects.equals(u.getActive(),0)).map(User::getId).collect(Collectors.toList());
        userIds.add(Constants.FxDefaultUserId);
        params.put("userIds",userIds);


        tradeO2oCounts =tradeO2oDao.queryCountStat(staff,params);


        if (CollectionUtils.isEmpty(tradeO2oCounts)) {
            return;
        }
        Map<String, Integer> resultMap = new HashMap<>();
        tradeO2oCounts.forEach(label -> {
            resultMap.put(label.getSysStatus(), label.getNum());
        });
        tradeCount.setO2oMap(resultMap);
    }

    private List<Long> extractAllWarehouseIds(Staff staff) {
        if(staff.isDefaultStaff()){
            return Collections.emptyList();
        }
        List<Long> list = new ArrayList<>();
        String allWarehouseIds = staff.getAllWarehouseGroup();
        if (allWarehouseIds != null && allWarehouseIds.length() > 0) {
            String[] widArr = allWarehouseIds.split(",");
            for (String wid : widArr) {
                if ((wid = wid.trim()).matches("\\d+")) {
                    list.add(Long.valueOf(wid));
                }
            }
        }
        return list;
    }

    /**
     * 测试PG查询自定义异常
     * @return
     */
    public TradeCount countPgTest() {
        TradeCount tradeCount = new TradeCount();
        List<Long> tags = new ArrayList<>();
        tags.add(2244259119710208L);
        tags.add(2239897842089984L);
        tags.add(2239939922051072L);
        tags.add(2239940150345728L);
        tags.add(2247179643404288L);

        long start = System.currentTimeMillis();
        StringBuilder sql = new StringBuilder("SELECT ");
        tags.forEach(tag -> sql.append("SUM(CASE WHEN (POSITION('")
                .append(tag).append("' IN EXCEPT_IDS) > 0 OR (T.MERGE_SID > 0 ")
                .append("AND EXISTS(SELECT 1 FROM TRADE_").append(20).append(" M ")
                .append("WHERE M.COMPANY_ID = ").append(11920).append(" AND M.ENABLE_STATUS = 2 AND M.MERGE_SID > 0 AND M.MERGE_SID = T.MERGE_SID AND ")
                .append("POSITION ('").append(tag).append("' IN M.EXCEPT_IDS) >0)))")
                .append("THEN 1 ELSE 0 END) AS EX_").append(tag).append(","));
        sql.deleteCharAt(sql.length()-1);
        sql.append(" FROM TRADE_").append(20).append(" T WHERE T.EXCEPT_IDS <> '' AND T.ENABLE_STATUS = 1 AND T.COMPANY_ID = 11920 AND IS_CANCEL = 0  AND t.is_presell <> 1 AND t.is_excep = 1 AND t.sys_status IN('WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'FINISHED_AUDIT') ");
        jdbcTemplateAdapter.get(PG_TRADE_DB_NO).query(sql.toString(), rs -> {
            Map<String, Long> resultMap = new HashMap<>();
            for (Long tag : tags) {
                resultMap.put(String.valueOf(tag), rs.getLong("EX_" + tag));
            }
            tradeCount.setExceptTagMap(resultMap);
        });
        long took = System.currentTimeMillis()-start;
        logger.debug("查询数据库耗时:"+took+"ms");
        return tradeCount;
    }

    private void countSysStatus4Normal(Staff staff, final TradeCount tradeCount,QueryLogBuilder builder) {
        builder.reBaseTimer();
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(0);
        String table = "trade_stat_" + staff.getDbInfo().getTradeDbNo();
        if(tradeLocalConfig.isUseTradeTable4StatCompanyIds(staff.getCompanyId())){
            table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
        }
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("is_cancel = 0");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = null;
        if(tradeLocalConfig.isUseTradeTable4StatCompanyIds(staff.getCompanyId())){
            sql = "SELECT (CASE " +
                "WHEN (" +
                "(((convert_type = 1 and belong_type in (1, 3)) or t.v & 8388608 > 0) and sys_status = 'FINISHED_AUDIT')" +
                " OR " +
                "(convert_type = 2 and belong_type = 1 and sys_status = 'WAIT_AUDIT')" +
                ") then 'WAIT_DEST_SEND_GOODS'" +
                " ELSE sys_status END) AS status_lab, COUNT(*) AS num " +
                " FROM " + table + " t WHERE " + q.getQ() + " GROUP BY status_lab";}else {
            new QueryLogBuilder(staff).append("trade_stat 表没有 convert_type,belong_type字段 暂不支持统计 WAIT_DEST_SEND_GOODS ").printWarn(logger);
            sql = "SELECT sys_status AS status_lab, COUNT(*) AS num " +
                    " FROM " + table + " t WHERE " + q.getQ() + " GROUP BY status_lab";
        }
        //DevLogBuilder.forDev(staff,LogBusinessEnum.QUERY.getSign()).append("订单状态红点统计",sql).printDebug(logger);
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(r.getString("status_lab"))) {
                tradeCount.setWaitPayNum(r.getLong("num"));
            }
            if (Trade.SYS_STATUS_WAIT_AUDIT.equals(r.getString("status_lab"))) {
                tradeCount.setWaitAuditNum(r.getLong("num"));
            }
            if (Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(r.getString("status_lab"))) {
                tradeCount.setWaitFinanceAudit(r.getLong("num"));
            }
            if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(r.getString("status_lab"))) {
                tradeCount.setFinishAuditNum(r.getLong("num"));
            }
            if (Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(r.getString("status_lab"))) {
                tradeCount.setWaitDestSendNum(r.getLong("num"));
            }
        });
        builder.recordTimer("sysStatus");
    }

    private void count4PartRefund(Staff staff,final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and item_excep & ").append(TradeConstants.PART_REFUND)
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  item_excep & ").append(TradeConstants.PART_REFUND).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) partRefundNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setPartRefundNum(r.getLong("partRefundNum"));
        });
    }


    private void count4ExItemProcessNum(Staff staff, final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and item_excep & ").append(TradeConstants.ITEM_PROCESS_EXCEP)
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  item_excep & ").append(TradeConstants.ITEM_PROCESS_EXCEP).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) itemProcessNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setItemProcessNum(r.getLong("itemProcessNum"));
        });
    }

    private void countUnConfirmNum(Staff staff, final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION)
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION).append(" > 0)))");
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        String sql = "SELECT count(1) unConfirmNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setUnConfirmNum(r.getLong("unConfirmNum"));
        });
    }

    public List<FxTradeStatisticsData> count4FxTrade(Staff staff, FxTradeStatisticsParams params){
        long start = System.currentTimeMillis();
        Query q = new Query();
        tradeSqlQueryBuilder.buildArrayQuery(q, "t.sys_status", Trade.SYS_STATUS_WAIT_AUDIT);
        q.and().append(" t.is_cancel = 0 AND t.enable_status=1 AND  t.type not like 'trade_out%' AND convert_type=1 AND belong_type in (1,3)  AND dest_id=? ").add(params.getDestId());
        q.and().append(" t.company_id=? ").add(staff.getCompanyId());
        Date now = new Date();
        tradeSqlQueryBuilder.buildDateRangeQuery(q, "t.pay_time", DateUtils.addDays(now,-7), now);

        Date timeoutActionTime = DateUtils.addHours(now, params.getTimeoutActionTimeBefore());
        q.append(" AND t.timeout_action_time > '2000-01-01 00:00:00' ");
        tradeSqlQueryBuilder.buildDateRangeQuery(q, "t.timeout_action_time",null ,timeoutActionTime);


        q.append(" group by case when t.sub_source='' OR t.sub_source is null  then t.source else t.sub_source end");

        StringBuilder sql = new StringBuilder("select count(*) trade_num,case when t.sub_source='' OR t.sub_source is null  then t.source else t.sub_source end as source");
        sql.append(" FROM trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE ").append(q.getQ());

        List<FxTradeStatisticsData> tradeStatisticsDataList = jdbcTemplateAdapter.get(staff.getDbNo()).query(sql.toString(), q.getArgs().toArray(), RowMapperContext.fxTradeStatisticsDataRowMapper);

        long took = System.currentTimeMillis() - start;
        Query wholeQuery = new Query();
        wholeQuery.setQ(sql).setArgs(q.getArgs());
        String logStr = String.format("[分销待审核],numFound: %s; took: %s ms,sql:%s",tradeStatisticsDataList.size(),took,wholeQuery.toString());
        logger.debug(LogHelper.buildLogHead(staff).append(logStr));

        return tradeStatisticsDataList;
    }

    public void count4Abnormal(Staff staff, final TradeCount tradeCount,QueryLogBuilder builder) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null, null);
        q.and().append("is_cancel = 0");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        StringBuilder sqlB = new StringBuilder()
                .append("SELECT SUM(is_excep) exceptNum, SUM(is_halt) haltNum, SUM(is_refund & is_excep) refundNum, SUM(address_changed) addressChangedNum, SUM(seller_memo_changed) sellerMemoChangedNum, ")
                .append("SUM(black_buyer_nick) blackBuyerNickNum, SUM(insufficient) insufficientNum, SUM(unmatched) unmatchedNum, SUM(relation_changed) relationChangedNum, ")
                .append("SUM(item_changed) itemChangedNum,")
                .append("SUM(is_lost_msg) isLostMsgNum,")
                .append("SUM(unattainable) unattainableNum,")
                .append("SUM(deliver_excep) deliverExcepNum,")
                .append("SUM(waiting_return_wms_excep) waitingReturnWmsNum,")
                .append("SUM(ambiguitye_excep) ambiguityeExcepNum,")
                .append("SUM(repulse_excep) repulseExcepNum,")
                .append("SUM(wait_merge) waitMergeNum,")
                .append("SUM(item_shutoff) itemShutoff,")
                .append("SUM(suite_change) suiteChangeNum,")
                .append("SUM(item_unique_shelfoff) itemUniqueShelfoff")
                .append(" FROM trade_stat_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE ").append(q.getQ());
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sqlB.toString(), q.getArgs().toArray(), r -> {
            tradeCount.setExcepNum(r.getLong("exceptNum"));
            tradeCount.setHaltNum(r.getLong("haltNum"));
            tradeCount.setRefundNum(r.getLong("refundNum"));
            tradeCount.setAddressChangedNum(r.getLong("addressChangedNum"));
            tradeCount.setSellerMemoChangedNum(r.getLong("sellerMemoChangedNum"));
            tradeCount.setBlackBuyerNickNum(r.getLong("blackBuyerNickNum"));
            tradeCount.setInsufficientNum(r.getLong("insufficientNum"));
            tradeCount.setUnmatchedNum(r.getLong("unmatchedNum"));
            tradeCount.setRelationChangedNum(r.getLong("relationChangedNum"));
            tradeCount.setItemChangedNum(r.getLong("itemChangedNum"));
            tradeCount.setIsLostMsgNum(r.getLong("isLostMsgNum"));
            tradeCount.setUnattainableNum(r.getLong("unattainableNum"));
            tradeCount.setDeliverExcepNum(r.getLong("deliverExcepNum"));
            tradeCount.setWaitingReturnWmsNum(r.getLong("waitingReturnWmsNum"));
            tradeCount.setAmbiguityeExcepNum(r.getLong("ambiguityeExcepNum"));
            tradeCount.setRepulseExcepNum(r.getLong("repulseExcepNum"));
            tradeCount.setWaitMergeNum(r.getLong("waitMergeNum"));
            tradeCount.setItemShutoffNum(r.getLong("itemShutoff"));
            tradeCount.setSuiteChangeNum(r.getLong("suiteChangeNum"));
            tradeCount.setItemUniqueShelfoffNum(r.getLong("itemUniqueShelfoff"));
        });
        builder.recordTimer("trade_stat");
        calculateExceptIdsCount(staff, tradeCount, q);
        builder.recordTimer("customExcept");
        statisticsSpecialExcepCount(staff, tradeCount);
        builder.recordTimer("uploadExcepNum");
    }


    public void countDeleverExcep(Staff staff, final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("is_cancel = 0");
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        String sqlB = "SELECT SUM(deliver_excep) deliverExcepNum" +
                " FROM trade_stat_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sqlB, q.getArgs().toArray(), r -> {
            tradeCount.setDeliverExcepNum(r.getLong("deliverExcepNum"));
        });
    }

    private void statisticsSpecialExcepCount(Staff staff, TradeCount tradeCount) {
        String uploadCountLimit = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getUploadExcepCountLimit();
        if (StringUtils.isNotBlank(uploadCountLimit)) {
            List<String> limit = Strings.getAsStringList(uploadCountLimit,",",true);
            if (limit.contains("all") || limit.contains(staff.getCompanyId().toString())){
                return;
            }

        }
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("is_cancel = 0");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        q.and().append("upload_excep = 1");

        String querySql = "SELECT count(*) uploadExcepNum" +
                " FROM trade_stat_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(querySql,q.getArgs().toArray(), r->{
            tradeCount.setUploadExcepNum(r.getLong("uploadExcepNum"));
        });
    }

    private void countDestStockInsufficient(Staff staff, TradeCount tradeCount) {
        String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
        String tradeExcept = "trade_except_" + staff.getDbInfo().getTradeExceptDbNo();
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("t.is_cancel = 0 AND t.convert_type=1 and t.belong_type in(1,3) ");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "t.sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "t.sys_status", tradeCount.getSysStatusList());
        }

        StringBuilder querySql = new StringBuilder();
        querySql.append("select count(distinct(t.sid)) as exceptCount from ").append(table).append(" t join ").append(tradeExcept).append(" tl on t.company_id =").append(staff.getCompanyId()).append(" AND t.sid = tl.sid AND t.enable_status = 1 AND tl.enable_status = 1 ")
                .append(" where tl.except_id=6 ");

        querySql.append(" AND ").append(q.getQ());
        jdbcTemplateAdapter.get(staff.getDbNo()).query(querySql.toString(),q.getArgs().toArray(), r->{
            tradeCount.setInsufficientNum(r.getLong("exceptCount"));
        });

        if(CollectionUtils.isNotEmpty(tradeCount.getSysExceptCountList())){
            for (TradeExceptCount tradeExceptCount : tradeCount.getSysExceptCountList()) {
                if(Objects.equals(tradeExceptCount.getExceptId(),ExceptEnum.INSUFFICIENT.getId())){
                    tradeExceptCount.setExceptCount(tradeCount.getInsufficientNum());
                }
            }
        }
    }


    private MySql2PglSqlResolver createdMysql2PglResolver(Staff staff) {
        return MySql2PglSqlResolver.createDefaultResolver(staff)
                .appendTableNameAlias("trade_not_consign", "trade").
                appendTableNameAlias("order_not_consign", "order");
    }


    private void calculateExceptIdsCount(Staff staff, TradeCount tradeCount, Query q) {
        DevLogBuilder logBuilder = new DevLogBuilder(staff).appendHead("自定义耗时统计").startWatch().startTimer();
        if(ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getCalcExceptIdsByExceptIds(),String.valueOf(staff.getCompanyId()))){
            calculateExceptIdsCountByExceptids(staff,tradeCount);
            logBuilder.recordTimer("calculateExceptIdsCountByExceptids");
            logBuilder.appendTook(1000).printError(logger,null);
            return;
        }
        if(ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getCalcExceptIdsBySid(),String.valueOf(staff.getCompanyId()))){
            calculateExceptIdsCountBySid(staff,tradeCount);
            logBuilder.recordTimer("calculateExceptIdsCountBySid");
            logBuilder.appendTook(1000).printError(logger,null);
            return;
        }
        List<TradeTag> tags = tradeTagService.list(staff, 1);
        tags.addAll(SystemExcepts.getSystemExcepts());

        String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
        StringBuilder sql = new StringBuilder("SELECT ");
        tags.forEach(tag -> sql.append("SUM(CASE WHEN (LOCATE('").append(tag.getId()).append("', except_ids) > 0 OR (merge_sid > 0 AND EXISTS(SELECT 1 FROM ").append(table).append(" WHERE company_id = ").append(staff.getCompanyId()).append(" AND merge_sid>0 AND merge_sid = t.sid AND enable_status = 2 AND LOCATE(").append(tag.getId()).append(", except_ids) > 0))").append(") THEN 1 ELSE 0 END) AS EX_").append(tag.getId()).append(","));
        sql.deleteCharAt(sql.length()-1);
        sql.append(" FROM ").append(table).append(" t WHERE ").append(q.getQ());
        jdbcTemplateAdapter.get(staff).query(sql.toString(), q.getArgs().toArray(), rs -> {
            Map<String, Long> resultMap = new HashMap<>();
            tags.forEach(tag -> {
                try {
                    // 排除id为17的
                    if(!Objects.equals(tag.getId(), ExceptEnum.UNATTAINABLE.getId())){
                        resultMap.put(String.valueOf(tag.getId()), rs.getLong("EX_"+tag.getId()));
                    }
                } catch (SQLException e) {
                    logger.error(LogHelper.buildLog(staff, e.getMessage()), e);
                }
            });
            tradeCount.setExceptTagMap(resultMap);
        });
        logBuilder.recordTimer("calculateExceptIdsCount");
        logBuilder.appendTook(1000).printError(logger,null);
    }

    /**
     *
     * 查询子单和主单，按异常分组
     * @param staff
     * @param tradeCount
     */
    private void calculateExceptIdsCountByExceptids(Staff staff, TradeCount tradeCount) {
        String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = new Query();
        q.append(" SELECT except_ids,count(distinct(if(merge_sid > 0,merge_sid,sid))) as num  from ").append(table).append(" t ");
        q.append(" where t.company_id = ? AND t.enable_status in(1,2) AND t.type not like 'trade_out%' AND (t.is_presell <> 1 AND t.is_presell <> 3) ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildUserQuery(q, staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null, null);
        q.and().append(" is_cancel = 0  AND t.except_ids!='' ");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        q.append("GROUP BY except_ids ");

        List<TradeTag> tags = tradeTagService.list(staff, 1);
        tags.addAll(SystemExcepts.getSystemExcepts().stream().filter(o->o.getId()!=null && o.getId()>1000L).collect(Collectors.toList()));

        StringBuilder sql = new StringBuilder("SELECT ");
        tags.forEach(tag -> sql.append("SUM(CASE WHEN (LOCATE('").append(tag.getId()).append("', except_ids) > 0)  THEN t2.num ELSE 0 END) AS EX_").append(tag.getId()).append(","));
        sql.deleteCharAt(sql.length()-1);
        sql.append(" FROM ").append("(").append(q.getQ()).append(")t2");
        jdbcTemplateAdapter.get(staff).query(sql.toString(), q.getArgs().toArray(), rs -> {
            Map<String, Long> resultMap = new HashMap<>();
            tags.forEach(tag -> {
                try {
                    resultMap.put(String.valueOf(tag.getId()), rs.getLong("EX_"+tag.getId()));
                } catch (SQLException e) {
                    logger.error(LogHelper.buildLog(staff, e.getMessage()), e);
                }
            });
            tradeCount.setExceptTagMap(resultMap);
        });
    }

    /**
     * 查询子单和主单，按订单分组
     *
     * @param staff
     * @param tradeCount
     */
    private void calculateExceptIdsCountBySid(Staff staff, TradeCount tradeCount) {
        String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();

        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = new Query();
        q.append(" SELECT t.sid,t.merge_sid,group_concat(t.except_ids) AS except_ids  from ").append(table).append(" t ");
        q.append(" where t.company_id = ? AND t.enable_status in(1,2) AND t.type not like 'trade_out%' AND (t.is_presell <> 1 AND t.is_presell <> 3) ").add(staff.getCompanyId());
        tradeSqlQueryBuilder.buildUserQuery(q, staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null, null);
        q.and().append(" is_cancel = 0 AND t.except_ids!='' ");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        q.append(" GROUP BY  CASE t.merge_sid WHEN -1 THEN  t.sid ELSE t.merge_sid END ");

        List<TradeTag> tags = tradeTagService.list(staff, 1);
        tags.addAll(SystemExcepts.getSystemExcepts().stream().filter(o->o.getId()!=null && o.getId()>1000L).collect(Collectors.toList()));

        StringBuilder sql = new StringBuilder("SELECT ");
        tags.forEach(tag -> sql.append("SUM(CASE WHEN (LOCATE('").append(tag.getId()).append("', except_ids) > 0)  THEN 1 ELSE 0 END) AS EX_").append(tag.getId()).append(","));
        sql.deleteCharAt(sql.length()-1);
        sql.append(" FROM ").append("(").append(q.getQ()).append(")t2");
        jdbcTemplateAdapter.get(staff).query(sql.toString(), q.getArgs().toArray(), rs -> {
            Map<String, Long> resultMap = new HashMap<>();
            tags.forEach(tag -> {
                try {
                    resultMap.put(String.valueOf(tag.getId()), rs.getLong("EX_"+tag.getId()));
                } catch (SQLException e) {
                    logger.error(LogHelper.buildLog(staff, e.getMessage()), e);
                }
            });
            tradeCount.setExceptTagMap(resultMap);
        });
    }

    private void calculateExceptIdsCount2(Staff staff, TradeCount tradeCount, Query q) {
        List<TradeTag> tags = tradeTagService.list(staff, 1);
        if (!tags.isEmpty()) {
            String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
            q.and().append("(LOCATE(?, except_ids) > 0 OR(merge_sid > 0 AND EXISTS(SELECT 1 FROM ").append(table).append(" WHERE company_id = ? AND merge_sid > 0 AND merge_sid = t.sid AND enable_status = 2 AND LOCATE(?, except_ids) > 0").append(")))");
            StringBuilder sql = new StringBuilder("SELECT COUNT(1) FROM ").append(table).append(" t WHERE ").append(q.getQ());
            tradeCount.setExceptTagMap(new HashMap<>(tags.size(), 1));
            for (TradeTag tag : tags) {
                List<Object> args = new ArrayList<>(q.getArgs());
                args.add(String.valueOf(tag.getId()));
                args.add(staff.getCompanyId());
                args.add(String.valueOf(tag.getId()));
                PglContext.open();
                tradeCount.getExceptTagMap().put(String.valueOf(tag.getId()),
                        dbAdapter.query(staff, sql.toString(),(pgSql, template) -> template.queryForObject(pgSql, args.toArray(), Long.class)));
            }
        }
    }

    private void countMemoAndMessage(Staff staff, TradeCount tradeCount,QueryLogBuilder builder){
        builder.reBaseTimer();
        String table = "trade_not_consign_" + staff.getDbInfo().getTradeDbNo();
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, null);
        q.and().append("is_cancel = 0");
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        String mainCondition = "((seller_memo <> '' and is_handler_memo=0) or (buyer_message <> '' and is_handler_message=0) or (seller_memo <> '' and is_handler_memo=1) or ((buyer_message <> '' and is_handler_message=1) OR buyer_message=''))";
        StringBuilder sql = new StringBuilder()
                .append("select memoCount+messageCount as totalCount,memoCount,messageCount from(")
                .append("select  sum(case when seller_memo <> '' and is_handler_memo=0 then 1 else 0 end) memoCount," +
                        " sum(case when buyer_message <> '' and is_handler_message=0 then 1 else 0 end) messageCount from ")
                .append(table).append(" t where ").append(q.getQ()).append(" AND t.enable_status = 1 ").append(" AND (").append(mainCondition)
                //.append(" OR (merge_sid > 0 AND t.`merge_sid` in(SELECT merge_sid FROM ").append(table).append(" where ").append(" company_id = ").append(staff.getCompanyId()).append(" AND ").append(mainCondition).append(" AND enable_status = 2 )))) tt ");
                .append(" OR (merge_sid > 0 AND exists (SELECT 1 FROM ").append(table).append(" t1 ")
                .append(" where ").append(" company_id = ").append(staff.getCompanyId()).append(" AND t1.merge_sid=t.merge_sid ")
                .append(" AND ").append(mainCondition).append(" AND enable_status = 2 )))) tt ");
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql.toString(), q.getArgs().toArray(), r -> {
            tradeCount.setUnCheckTotalNum(r.getLong("totalCount"));
            tradeCount.setUnCheckMemoNum(r.getLong("memoCount"));
            tradeCount.setUnCheckMessageNum(r.getLong("messageCount"));
        });
        builder.recordTimer("unCheckMsgAndMemo");


        //        #单据本身不存在需要处理的留言（已处理或无留言）
        //        ((is_handler_message = 1 && buyer_message <> '') || buyer_message = '')
        //        # 非合单 或者 不存在需要处理留言的子订单
        //        AND (merge_sid < 0 OR  NOT exists(SELECT 1
        //                FROM trade_45 t1
        //                WHERE company_id = 100145
        //                AND t1.merge_sid = t.merge_sid
        //                and enable_status = 2
        //                AND ((is_handler_message <> 1 && buyer_message <> '')))
        //             )
        q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, null);
        q.and().append("is_cancel = 0");
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        sql = new StringBuilder()
                .append("select  count(1) as checkedMessageAndNoneCount from ")
                .append(table).append(" t where ").append(q.getQ()).append(" AND t.enable_status = 1 ").append(" AND (")
                .append("((is_handler_message = 1 && buyer_message <> '') || buyer_message = '')")
                .append(" AND (merge_sid < 0 OR NOT exists (SELECT 1 FROM ").append(table).append(" t1 ")
                .append(" where ").append(" t1.company_id = ").append(staff.getCompanyId()).append(" AND t1.merge_sid=t.merge_sid  AND t1.enable_status = 2 ")
                .append(" AND (t1.is_handler_message <> 1 && t1.buyer_message <> ''))))");
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql.toString(), q.getArgs().toArray(), r -> {
            tradeCount.setCheckedMessageAndNoneCount(r.getLong("checkedMessageAndNoneCount"));
        });
        builder.recordTimer("handledOrNoneMsg");

        q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, null);
        q.and().append("is_cancel = 0");
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        sql = new StringBuilder()
                .append("select  count(1) as checkedMemoAndNoneCount from ")
                .append(table).append(" t where ").append(q.getQ()).append(" AND t.enable_status = 1 ").append(" AND (")
                .append("((is_handler_memo = 1 && seller_memo <> '') || seller_memo = '')")
                .append(" AND (merge_sid < 0 OR NOT exists (SELECT 1 FROM ").append(table).append(" t1 ")
                .append(" where ").append(" t1.company_id = ").append(staff.getCompanyId()).append(" AND t1.merge_sid=t.merge_sid  AND t1.enable_status = 2 ")
                .append(" AND (t1.is_handler_memo <> 1 && t1.seller_memo <> ''))))");
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql.toString(), q.getArgs().toArray(), r -> {
            tradeCount.setCheckedMemoAndNoneCount(r.getLong("checkedMemoAndNoneCount"));
        });
        builder.recordTimer("handledOrNoneMemo");
    }

    public List<Long> queryDeliverAndUploadSids(Staff staff, TradeQueryParams params) {
        String table = "trade_stat_" + staff.getDbInfo().getTradeDbNo();
        Query query = new Query();
        query.append(" company_id = ? ").add(staff.getCompanyId());
        if (null != params.getWarehouseIds()) {
            tradeSqlQueryBuilder.buildArrayQuery(query,"warehouse_id",params.getWarehouseIds());
        }
        if (null != params.getUserIds()) {
            tradeSqlQueryBuilder.buildArrayQuery(query,"user_id", params.getUserIds());
        }
        query.and().append(" enable_status = 1 and (deliver_excep = 1 or upload_excep = 1) order by sid asc ");
        return jdbcTemplateAdapter.get(staff.getDbNo()).queryForList("select sid from " + table + " where " + query.getQ(),query.getArgs().toArray(),Long.class);
    }


    /**
     *  单号回收异常
     * @param staff
     * @param tradeCount
     */
    private void count4OutSidRecoveryFail(Staff staff,final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.OUTSID_RECOVERY_FAIL.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.OUTSID_RECOVERY_FAIL.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) outSidRecoveryFailNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setOutSidRecoveryFailNum(r.getLong("outSidRecoveryFailNum"));
        });
    }
    @Deprecated // 废除
    private void fillTradeExcept(Staff staff, TradeCount tradeCount){
       /* List<Long> exceptIds =new ArrayList<>();
        exceptIds.add(ExceptEnum.FINANCE_REJECT_EXCEPT.getId());
        List<ExceptCount> exceptCounts = tradeExceptService.queryCount(staff,exceptIds);
        Map<String, Long> exceptTagMap = tradeCount.getExceptTagMap();
        if(CollectionUtils.isNotEmpty(exceptCounts)){
            Map<Long, Integer> collect = exceptCounts.stream().collect(Collectors.toMap(ExceptCount::getExceptId, ExceptCount::getExceptCount, (key1, key2) -> key1));
            if(exceptTagMap==null){
                exceptTagMap=new HashMap<>();
            }
            Map<Long, Integer> exceptMap = ExceptEnum.initExceptNum(collect);
            Set<Map.Entry<Long, Integer>> entries = exceptMap.entrySet();
            for(Map.Entry<Long, Integer> entry:entries){
                exceptTagMap.put(String.valueOf(entry.getKey()),Long.valueOf(entry.getValue()));
            }
            tradeCount.setExceptTagMap(exceptTagMap);
            Integer financeRejectUnm = Optional.ofNullable(exceptMap.get(ExceptEnum.FINANCE_REJECT_EXCEPT.getId())).orElse(0);
            tradeCount.setFinanceRejectUnm(financeRejectUnm.longValue());
        }*/

    }


    private void countRefunNumExcept(Staff staff, TradeCount tradeCount){
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.REFUND_ITEM_NUM_EXCEPT.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.REFUND_ITEM_NUM_EXCEPT.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) countRefunNumExceptNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setRefundNumExceptNum(r.getLong("countRefunNumExceptNum"));
        });
    }

    private void countOnlineLockExcept(Staff staff, TradeCount tradeCount){
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.ONLINE_LOCK.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.ONLINE_LOCK.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) onlineLockNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setOnlineLockNum(r.getLong("onlineLockNum"));
        });
    }


    private void countPartPayExcept(Staff staff, TradeCount tradeCount){
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.PART_PAY_EXCEPT.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.PART_PAY_EXCEPT.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) partPayExceptNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setPartPayExceptNum(r.getLong("partPayExceptNum"));
        });
    }

    /**
     * 统计异常
     * @param staff
     * @param tradeCount
     */
    public void countTradeExcept(Staff staff, TradeCount tradeCount,QueryLogBuilder builder,List<CountTask> tasks)  {
        if(TradeExceptWhiteUtils.openTradeExceptCountNewRead(staff)){
            tasks.add((staff1, tradeCount1, builder1) -> {
                List<TradeExceptCount> tradeExceptCounts;
                if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
                    tradeExceptCounts = tradeExceptCountBusiness.countExcept(staff);
                }else {
                    tradeExceptCounts = tradeExceptCountBusiness.countExcept(staff,tradeCount.getSysStatusList());
                }
                tradeExceptCount2TradeCount(staff1,tradeCount1, tradeExceptCounts);
                builder1.recordTimer("newReadTradeExcept");
            });
            return;
        }
        tasks.add((staff1, tradeCount1, builder1) -> {
            countExcepts(staff1,tradeCount1);
            builder1.recordTimer("countExcept");
        });

        tasks.add((staff1, tradeCount1, builder1) -> {
            countExceptIgnoreWareHouse(staff1,tradeCount1);
            builder1.recordTimer("countExceptIgnoreWareHouse");
        });



        //countRefunNumExcept(staff,tradeCount);
        //builder.recordTimer("refunNumExcept");
        //countOnlineLockExcept(staff, tradeCount);
        //builder.recordTimer("onlineLockExcept");
        //if(featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT)){
        //    countPartPayExcept(staff,tradeCount);
        //    builder.recordTimer("partPayExcept");
        //}
        //count4OutSidRecoveryFail(staff,tradeCount);
        //builder.recordTimer("outSidRecoveryFail");
        //countGxItemChangeNum(staff, tradeCount);
        //builder.recordTimer("gxItemChange");
        //countPlatModifyExchangeItemExcept(staff, tradeCount);
        //builder.recordTimer("platModifyItemExcept");
        //countCaiGouExcept(staff, tradeCount);
        //builder.recordTimer("caiGouExcept");
        //if(TradeDiamondUtils.allowCountExItemProcessNum(staff)){
        //    count4ExItemProcessNum(staff,tradeCount);
        //    builder.recordTimer("exProcessExcept");
        //};
        //count4PartRefund(staff, tradeCount);
        //builder.recordTimer("partRefundExcept");

        tasks.add((staff1, tradeCount1, builder1) -> {
            count4Abnormal(staff1, tradeCount1, builder1);
        });

    }




    private void countExcepts(Staff staff, TradeCount tradeCount){
        Map<Long,String> exceptMap = new HashMap<>();

        exceptMap.put(ExceptEnum.REFUND_ITEM_NUM_EXCEPT.getOldExceptEnum().getTradeExcep(),"countRefunNumExceptNum");
        exceptMap.put(ExceptEnum.ONLINE_LOCK.getOldExceptEnum().getTradeExcep(),"onlineLockNum");
        if(featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT)){
            exceptMap.put(ExceptEnum.PART_PAY_EXCEPT.getOldExceptEnum().getTradeExcep(),"partPayExceptNum");
        }
        exceptMap.put(ExceptEnum.OUTSID_RECOVERY_FAIL.getOldExceptEnum().getTradeExcep(),"outSidRecoveryFailNum");
        exceptMap.put(ExceptEnum.GX_ITEM_CHANGE_EXCEPT.getOldExceptEnum().getTradeExcep(),"gxItemChangeNum");
        exceptMap.put(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getOldExceptEnum().getTradeExcep(),"platModifyExchangeItemExcept");
        if(TradeDiamondUtils.allowCountExItemProcessNum(staff)){
            exceptMap.put( Long.valueOf(TradeConstants.ITEM_PROCESS_EXCEP),"itemProcessNum");
        };
        exceptMap.put(Long.valueOf(TradeConstants.PART_REFUND),"partRefundNum");

        Map<String, Long> stringLongMap = queryExceptCount(staff, false, exceptMap);

        tradeCount.setRefundNumExceptNum(stringLongMap.get("countRefunNumExceptNum"));
        tradeCount.setOnlineLockNum(stringLongMap.get("onlineLockNum"));
        if(featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT)){
            tradeCount.setPartPayExceptNum(stringLongMap.get("partPayExceptNum"));
        }
        tradeCount.setOutSidRecoveryFailNum(stringLongMap.get("outSidRecoveryFailNum"));
        tradeCount.setGxItemChangeNum(stringLongMap.get("gxItemChangeNum"));
        tradeCount.setPlatModifyExchangeItemExcept(stringLongMap.get("platModifyExchangeItemExcept"));
        if(TradeDiamondUtils.allowCountExItemProcessNum(staff)){
            tradeCount.setItemProcessNum(stringLongMap.get("itemProcessNum"));
        };
        tradeCount.setPartRefundNum(stringLongMap.get("partRefundNum"));
    }

    private void countExceptIgnoreWareHouse(Staff staff, TradeCount tradeCount){
        Map<Long,String> exceptMap = new HashMap<>();
        exceptMap.put(ExceptEnum.CAI_GOU_TRADE_EXCEPT.getOldExceptEnum().getTradeExcep(),"caiGouNum");
        if(featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT)){
            exceptMap.put(ExceptEnum.PART_PAY_EXCEPT.getOldExceptEnum().getTradeExcep(),"partPayExceptNum");
        }

        Map<String, Long> stringLongMap = queryExceptCount(staff, true, exceptMap);
        tradeCount.setCaiGouNum(stringLongMap.get("caiGouNum"));
        if(featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT)){
            tradeCount.setPartPayExceptNum(stringLongMap.get("partPayExceptNum"));
        }
    }

    private Map<String,Long> queryExceptCount(Staff staff, boolean ignoreWareHouse,Map<Long,String> exceptMap){
        Map<String,Long> result = new HashMap<>();
        try {
            StringBuilder sql = new StringBuilder("SELECT ");
            for (Map.Entry<Long, String> e : exceptMap.entrySet()) {
                addCaseWhen(sql,e.getKey(),e.getValue()).append(",");
            }

            sql.append("t.company_id FROM trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" t ");
            sql.append(" LEFT JOIN trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" sub_t on ")
                    .append("sub_t.company_id = ").append(staff.getCompanyId()).append(" AND sub_t.enable_status = 2 AND sub_t.merge_sid= t.sid AND sub_t.is_cancel = 0 AND sub_t.excep > 0")
                    .append(" WHERE ");

            TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true);
            Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
            if (!ignoreWareHouse) {
                tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
            }
            tradeSqlQueryBuilder.buildArrayQuery(q, "t.sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);

            sql.append(q.getQ().toString()).append("  AND t.enable_status=1 AND (t.is_excep = 1 OR sub_t.is_excep = 1) AND (t.excep > 0 or sub_t.excep > 0 ) ");
            DevLogBuilder.forDev(staff,LogBusinessEnum.QUERY.getSign()).append("异常订单统计").append(sql.toString()).printDebug(logger);


            jdbcTemplateAdapter.get(staff.getDbNo()).query(sql.toString(), q.getArgs().toArray(), r -> {
                for (String value : exceptMap.values()) {
                    result.put(value,r.getLong(value));
                }
            });
        }catch (Exception e){
            new QueryLogBuilder(staff).append("异常订单统计错误").printWarn(logger,e);
        }
        return result;
    }

    /**
     * SUM(CASE
     *             WHEN (t.is_cancel = 0 AND t.enable_status = 1 AND t.excep & 55 > 0)
     *                  OR
     *                  (t.merge_sid > 0 AND sub_t.sid IS NOT NULL AND sub_t.excep & 55  > 0)
     *             THEN 1 ELSE 0 END) AS onlineLockNum,
     */
    private StringBuilder addCaseWhen(StringBuilder sql,Long exceptionId,String alias){
        sql.append("SUM(CASE WHEN (t.excep IS NOT NULL AND t.excep & ").append(exceptionId).append(" > 0) OR (t.merge_sid > 0 AND sub_t.sid IS NOT NULL AND sub_t.excep & ").append(exceptionId).append(" > 0)")
                .append(" THEN 1 ELSE 0 END) AS ").append(alias);
        return sql;
    }

    private void tradeExceptCount2TradeCount(Staff staff, TradeCount tradeCount, List<TradeExceptCount> tradeExceptCounts) {
        List<TradeTag> tags = tradeTagService.list(staff, 1);
        // 自定义异常
        List<Long> customerExceptIds = tags.stream().map(TradeTag::getId).collect(Collectors.toList());
        // 三方仓的异常
        List<Long> collect = ExceptEnum.party3ExceptMap.keySet().stream().collect(Collectors.toList());
        customerExceptIds.addAll(collect);
        try {
            Optional<TradeExceptCount> first = tradeExceptCounts.stream().filter(tradeExceptCount -> tradeExceptCount.getExceptId() == -1L).findFirst();
            // exceptId 为-1 是异常订单总数
            tradeCount.setExcepNum(first.isPresent()?first.get().getExceptCount():0L);
            Field[] declaredFields = ReflectUtil.getFields(TradeCount.class);
            Map<Long, Long> tradeExceptCountMap = tradeExceptCounts.stream().collect(Collectors.toMap(TradeExceptCount::getExceptId, TradeExceptCount::getExceptCount, (k1, k2) -> k2));
            handleSysExcept(staff,tradeCount,tradeExceptCounts.stream().filter(o->!customerExceptIds.contains(o.getExceptId())).collect(Collectors.toList()));
            for (Field field : declaredFields) {
                boolean aStatic = Modifier.isStatic(field.getModifiers());
                if (aStatic) {
                    continue;
                }
                String name = field.getName();
                Long exceptId = ExceptEnum.exceptCountFieldMap.get(name);
                field.setAccessible(true);
                if (exceptId != null) {
                    // 存在其他的属性，所以首先保证是异常属性
                    if(tradeExceptCountMap.get(exceptId) != null){
                        field.set(tradeCount, tradeExceptCountMap.get(exceptId));
                    }else{
                        // 没有的默认0L
                        field.set(tradeCount, 0L);
                    }
                }
            }
            Map<String, Long> resultMap = new HashMap<>();
            for(Long customerExceptId:customerExceptIds){
                Long count = Optional.ofNullable(tradeExceptCountMap.get(customerExceptId)).orElse(0L);
                // 三方仓的异常还要再转一下
                if (ExceptEnum.party3ExceptMap.get(customerExceptId) != null) {
                    ExceptEnum exceptEnum = ExceptEnum.party3ExceptMap.get(customerExceptId);
                    resultMap.put(String.valueOf(exceptEnum.getOldExceptEnum().getOldIdx()), count);
                } else {
                    resultMap.put(String.valueOf(customerExceptId), count);
                }
            }
            tradeCount.setExceptTagMap(resultMap);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "异常统计失败！"), e);
        }
    }


    /**
     *  供销单商品信息变更
     * @param staff
     * @param tradeCount
     */
    private void countGxItemChangeNum(Staff staff,final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.GX_ITEM_CHANGE_EXCEPT.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.GX_ITEM_CHANGE_EXCEPT.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) gxItemChangeNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setGxItemChangeNum(r.getLong("gxItemChangeNum"));
        });
    }

    private void countPlatModifyExchangeItemExcept(Staff staff, final TradeCount tradeCount) {
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) platModifyExchangeItemExcept" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setPlatModifyExchangeItemExcept(r.getLong("platModifyExchangeItemExcept"));
        });
    }

    private void handleSysExcept(Staff staff, TradeCount tradeCount, List<TradeExceptCount> tradeExceptCounts) {

        tradeCount.setIfCalcAllSysExcept(true);
        if(CollectionUtils.isEmpty(tradeExceptCounts)){
            return;
        }
        ArrayList<TradeExceptCount> sysExceptCountList = Lists.newArrayListWithExpectedSize(tradeExceptCounts.size());
        tradeCount.setSysExceptCountList(sysExceptCountList);
        tradeExceptCounts.forEach(e->{
            Long exceptId = e.getExceptId();
            ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
            if (exceptEnum != null) {
                e.setExceptName(exceptEnum.getChinese());
                sysExceptCountList.add(e);
                ExceptOldEnum exceptOldEnum = TradeExceptOldUtils.exceptId2ExceptOldEnumMap.get(exceptId);
                if(exceptOldEnum!=null){
                    e.setExceptCode(exceptOldEnum.getEnglish());
                }else{
                    e.setExceptCode(exceptEnum.getOldExceptEnum().getEnglish());
                }
            }
        });

    }

    private void countCaiGouExcept(Staff staff, TradeCount tradeCount){
        TradeQueryParams params = new TradeQueryParams().setIsPresell(0).setIsOutstock(0).setIsCancel(0).setCheckActive(true).setIsExcep(1);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        q.and().append("((is_cancel = 0 and enable_status=1 and excep & ").append(ExceptEnum.CAI_GOU_TRADE_EXCEPT.getOldExceptEnum().getTradeExcep())
                .append(" > 0 ) or (t.merge_sid > 0 and exists (select 1 From trade_not_consign_").append(staff.getDbInfo().getTradeDbNo())
                .append(" where company_id = ? AND enable_status = 2 AND merge_sid >0 AND merge_sid = t.merge_sid AND ").add(staff.getCompanyId())
                .append(" is_cancel = 0 and  excep & ").append(ExceptEnum.CAI_GOU_TRADE_EXCEPT.getOldExceptEnum().getTradeExcep()).append(" > 0)))");
        if(CollectionUtils.isEmpty(tradeCount.getSysStatusList())){
            tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
        }else {
            tradeSqlQueryBuilder.buildListQuery(q, "sys_status", tradeCount.getSysStatusList());
        }
        String sql = "SELECT count(1) caiGouNum" +
                "  From trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " t WHERE " + q.getQ();
        jdbcTemplateAdapter.get(staff.getDbNo()).query(sql, q.getArgs().toArray(), r -> {
            tradeCount.setCaiGouNum(r.getLong("caiGouNum"));
        });
    }

}

package com.raycloud.dmj.services.trades.support.sort;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.sort.SortContext;
import com.raycloud.dmj.domain.sort.enums.SortTypeEnum;
import com.raycloud.dmj.domain.trades.Trade;

import java.util.List;

/**
 * @Auther mengfanguang
 * @Date 2023/10/31
 */
public interface ITradeSortService {

    SortTypeEnum getSortTypeEnum();

    void sort(Staff staff, SortContext sortContext);
}
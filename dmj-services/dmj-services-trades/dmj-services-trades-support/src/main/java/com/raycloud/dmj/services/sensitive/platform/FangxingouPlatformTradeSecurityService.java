package com.raycloud.dmj.services.sensitive.platform;

import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.sensitive.AbstractPlatformTradeSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.*;

@Service
public class FangxingouPlatformTradeSecurityService extends AbstractPlatformTradeSecurityService {

    private final FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Autowired
    public FangxingouPlatformTradeSecurityService(FxgTradeDecryptBusiness fxgTradeDecryptBusiness) {
        this.fxgTradeDecryptBusiness = fxgTradeDecryptBusiness;
    }

    @Override
    public void doSensitiveTrades(Staff staff, List<Trade> trades) {
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        // 放心购订单没有buyerNick，系统保存时是保存的receiverMobile的索引串，需要进行替换
        trades.forEach(trade -> trade.setBuyerNick(trade.getReceiverMobile()));
    }

    @Override
    public List<Trade> filterTrades(Staff staff, List<Trade> trades) {
        // 筛选出放心购平台单或放心购系统单且以放心购方式加密的订单
        return trades.stream().filter(trade -> CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource()) || (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_FXG) && CipherTextUtils.ifEncrypt(trade))).collect(Collectors.toList());
    }
}

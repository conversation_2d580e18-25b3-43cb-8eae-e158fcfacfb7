package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;

import java.util.List;

/**
 * @Description <pre>
 * 基于业务操作的数据查询,用于前端业务操作后返回需要局部刷新的数据
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-05
 */
public interface ISceneLightQueryStrategy {

     SenceCodeEnum supportScene();

     List<Trade> doQueryTrades(SceneLightQueryContext context, Long ... sids);

}

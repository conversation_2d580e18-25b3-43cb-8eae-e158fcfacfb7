package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeExportParams;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.trades.ISysTradeService;

/**
 * Created by weixin on 2017/10/17.
 */
public class TradeOrderExportService implements IDownloadCenterCallback {

    private Staff staff;
    private TradeExportParams exportParams;
    private ISysTradeService sysTradeService;
    private String exportFields;

    public TradeOrderExportService(Staff staff, TradeExportParams exportParams, ISysTradeService sysTradeService) {
        this.staff= staff;
        this.exportParams = exportParams;
        this.sysTradeService = sysTradeService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult();
        result.setFlag(true);

        String[][] tradeExcelContent = sysTradeService.exportTrade(staff, exportParams, false);

        result.setData(tradeExcelContent);
        return result;
    }


}

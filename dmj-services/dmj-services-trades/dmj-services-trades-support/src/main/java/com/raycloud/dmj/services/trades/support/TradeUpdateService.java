package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.Lists;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.trade.GrossProfitBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.OutSidPool;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.trade.except.TradeExceptBizBusiness;
import com.raycloud.dmj.services.trade.stat.ITradeStatSyncService;
import com.raycloud.dmj.services.trade.type.ITradeTypeService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.search.OrderHotItemService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.erp.trade.search.db.OrderFixBusiness;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.Priority;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单更新服务，支持批量更新多个平台的订单
 *
 * <AUTHOR>
 */
@Service
public class TradeUpdateService implements ITradeUpdateService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    TbOrderDAO tbOrderDao;

    @Resource
    OrderFixBusiness orderFixBusiness;

    @Resource
    ITradePtService tradePtService;

    @Resource
    GrossProfitBusiness grossProfitBusiness;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    ShortIdBusiness shortIdBusiness;

    @Resource
    ITradeStatSyncService tradeStatSyncService;
    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    ITradeTypeService tradeTypeService;

    @Resource
    ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;
    @Resource
    TradeExceptBizBusiness tradeExceptBizBusiness;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    OrderHotItemService orderHotItemService;

    private List<Order> getUpdateOrders(List<Trade> trades, List<Order> toAdds) {
        List<Order> toUpdates = new ArrayList<>();
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                toUpdates.add(order);
                if (order.getSuits() != null) {
                    for (Order son : order.getSuits()) {
                        if (son.getFlag() == 0) {
                            toUpdates.add(son);
                        } else {
                            toAdds.add(son);
                        }
                    }
                }
            }
        }
        return toUpdates;
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> trades, boolean updateOrders) {
        try {
            List<Order> updateOrderList=new ArrayList<>();
            List<Order> insertOrderList=new ArrayList<>();
            if (updateOrders) {
                updateOrderList = getUpdateOrders(trades, insertOrderList);
                paymentCheckBefore(staff, null, updateOrderList, null, insertOrderList);
                if (CollectionUtils.isNotEmpty(updateOrderList)) {
                    tbOrderDao.batchUpdate(staff, updateOrderList);
                }
                if (CollectionUtils.isNotEmpty(insertOrderList)) {
                    tbOrderDao.batchInsert(staff, insertOrderList);
                }
            }

            paymentCheckBefore(staff, trades, null, null, null);
            secretBusiness.encodeTrades(staff, trades);
            tbTradeDao.batchUpdate(staff, trades);
            tradeExceptBizBusiness.updateTradeExcept(staff, trades,updateOrderList,null,insertOrderList);
            updateTradeStat(staff, null, trades,updateOrderList,insertOrderList);
            orderFixBusiness.fixOrderBelongSid(staff, updateOrderList);
            grossProfitBusiness.updateGrossProfit(staff, TradeUtils.toSids(trades));
            paymentCheckAfter(staff,trades,null,"更新订单");
        } catch (Exception e) {
            String errorMsg = buildTradeUpdateErrorMsg(null, trades);
            Logs.error(LogHelper.buildErrorLog(staff, e, "更新订单失败!!" + errorMsg), e);
            throw new TradeException("糟糕,更新订单失败!!" + errorMsg + " errMsg:" + e.getMessage(), e);
        }
    }

    private String buildTradeUpdateErrorMsg(List<Trade> insertTrades, List<Trade> updateTrades) {
        StringBuilder result = new StringBuilder();
        if (CollectionUtils.isNotEmpty(insertTrades)) {
            result.append(String.format("insertTids:[%s];", insertTrades.stream().map(Trade::getTid).collect(Collectors.joining(","))));
        }
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            StringBuilder updateTids = new StringBuilder();
            StringBuilder updateSids = new StringBuilder();
            for (Trade trade : updateTrades) {
                if (StringUtils.isNotBlank(trade.getTid())) {
                    updateTids.append(trade.getTid()).append(",");
                } else {
                    updateSids.append(trade.getSid()).append(",");
                }
            }
            if (updateTids.length() > 0) {
                result.append(String.format("updateTids:[%s];", updateTids.deleteCharAt(updateTids.length() - 1)));
            }
            if (updateSids.length() > 0) {
                result.append(String.format("updateSids:[%s];", updateSids.deleteCharAt(updateSids.length() - 1)));
            }
        }
        return result.toString();
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, Trade updateTrade) {
        List<Trade> updateTrades = new ArrayList<>();
        if (updateTrade != null) {
            updateTrades.add(updateTrade);
        }
        updateTrades(staff, updateTrades);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades) {
        updateTrades(staff, updateTrades, null);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders) {
        updateTrades(staff, updateTrades, updateOrders, null);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders, List<Trade> insertTrades) {
        updateTrades(staff, updateTrades, updateOrders, insertTrades, null);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, Trade updateTrade, List<Order> updateOrders, Trade insertTrade) {
        updateTrades(staff, updateTrade, updateOrders, insertTrade, null);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, Trade updateTrade, List<Order> updateOrders, Trade insertTrade, List<Order> insertOrders) {
        List<Trade> insertTrades = new ArrayList<>();
        if (insertTrade != null) {
            insertTrades.add(insertTrade);
        }
        updateTrades(staff, updateTrade, updateOrders, insertTrades, insertOrders);
    }


    @Override
    @Transactional
    public void updateTrades(Staff staff, Trade updateTrade, List<Order> updateOrders, List<Trade> insertTrades, List<Order> insertOrders) {
        List<Trade> updateTrades = new ArrayList<>();
        if (updateTrade != null) {
            updateTrades.add(updateTrade);
        }
        updateTrades(staff, updateTrades, updateOrders, insertTrades, insertOrders);
    }

    @Override
    @Transactional
    public void insertTrades(Staff staff, List<Trade> insertTrades, List<Order> insertOrders) {
        updateTrades(staff, new ArrayList<>(), new ArrayList<>(), insertTrades, insertOrders);
    }

    @Override
    public void batchUpdateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders) {
        int updateTradeSize = 0, updateOrderSize = 0;
        long updateTradeTook = 0, updateOrderTook = 0, allTook = 0;
        try {
            long begin = System.currentTimeMillis();
            paymentCheckBefore(staff, updateTrades, updateOrders, null, null);
            if (CollectionUtils.isNotEmpty(updateOrders)) {
                updateOrderSize = updateOrders.size();
                long start = System.currentTimeMillis();
                if (updateOrders.size() > 1) {
                    updateOrders.sort(Comparator.comparing(Order::getId));
                }
                tbOrderDao.realBatchUpdate(staff, updateOrders);
                updateOrderTook = System.currentTimeMillis() - start;
            }

            if (CollectionUtils.isNotEmpty(updateTrades)) {
                updateTradeSize = updateTrades.size();
                long start = System.currentTimeMillis();
                sortTradeOrOrders(staff, updateTrades);
                secretBusiness.encodeTrades(staff, updateTrades);
                tbTradeDao.realBatchUpdate(staff, updateTrades);


                updateTradeTook = System.currentTimeMillis() - start;
            }
            allTook = System.currentTimeMillis() - begin;
            tradeExceptBizBusiness.updateTradeExcept(staff, updateTrades,updateOrders,null,null);
            updateTradeStat(staff, null, updateTrades,updateOrders,null);
            grossProfitBusiness.updateGrossProfit(staff, TradeUtils.toSids(updateTrades));
            paymentCheckAfter(staff,null,updateTrades,"订单批量更新");
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "订单批量更新失败!!"), e);
            throw new TradeException("糟糕,订单批量更新失败:" + e.getMessage(), e);
        } finally {
            if (allTook > 1500) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单批量修改比较慢，updateTradeSize=%s, updateOrderSize=%s, updateTradeTook=%s, updateOrderTook=%s, allTook=%s", updateTradeSize, updateOrderSize, updateTradeTook, updateOrderTook, allTook)));
            }
        }
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders, List<Trade> insertTrades, List<Order> insertOrders) {
        updateTrades(staff, updateTrades, updateOrders, insertTrades, insertOrders, true);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders, List<Trade> insertTrades, List<Order> insertOrders, boolean needSecret) {
        updateTrades(staff, updateTrades, updateOrders, insertTrades, insertOrders, needSecret, true);
    }

    @Override
    @Transactional
    public void updateTrades(Staff staff, List<Trade> updateTrades, List<Order> updateOrders, List<Trade> insertTrades, List<Order> insertOrders, boolean needSecret, boolean needFixOrderBelongSid) {
        int updateTradeSize = 0, updateOrderSize = 0, insertTradeSize = 0, insertOrderSize = 0;
        long updateTradeTook = 0, updateOrderTook = 0, insertTradeTook = 0, insertOrderTook = 0, fixTook = 0 , tradeStatTook = 0, syncTypeTool = 0, syncOrderItemTagTool = 0, updateGrossProfitTool = 0, encodeTradeTook = 0, updateTradeExceptTook = 0, syncTypeTook = 0, syncOrderItemTagTook = 0, updateGrossProfitTook = 0, allTook = 0;
        try {
            long begin = System.currentTimeMillis();
            paymentCheckBefore(staff, updateTrades, updateOrders, insertTrades, insertOrders);
            if (CollectionUtils.isNotEmpty(updateOrders)) {
                updateOrderSize = updateOrders.size();
                long start = System.currentTimeMillis();
                if (updateOrders.size() > 1) {
                    updateOrders.sort(Comparator.comparing(Order::getId));
                }
                tbOrderDao.batchUpdate(staff, updateOrders);
                updateOrderTook = System.currentTimeMillis() - start;
            }

            if (CollectionUtils.isNotEmpty(insertOrders)) {
                insertOrderSize = insertOrders.size();
                long start = System.currentTimeMillis();
                tbOrderDao.batchInsert(staff, insertOrders);
                insertOrderTook = System.currentTimeMillis() - start;
            }
            long startEncode = System.currentTimeMillis();
            encodeTrades(staff, updateTrades, insertTrades, needSecret);
            encodeTradeTook = System.currentTimeMillis() - startEncode;

            if (CollectionUtils.isNotEmpty(updateTrades)) {
                updateTradeSize = updateTrades.size();
                long start = System.currentTimeMillis();
                sortTradeOrOrders(staff, updateTrades);
                handleTradeEmojiMsg(updateTrades);
                tbTradeDao.batchUpdate(staff, updateTrades);
                updateTradeTook = System.currentTimeMillis() - start;
                paymentCheckAfter(staff, null, updateTrades, "订单更新");
            }

            if (CollectionUtils.isNotEmpty(insertTrades)) {
                insertTradeSize = insertTrades.size();
                long start = System.currentTimeMillis();
                shortIdBusiness.checkShortId(staff.getCompanyId(), insertTrades);
                tbTradeDao.batchInsert(staff, insertTrades);
                insertTradeTook = System.currentTimeMillis() - start;
                paymentCheckAfter(staff, insertTrades, null, "订单新增");
            }

            long startUpdateTradeExcept = System.currentTimeMillis();
            tradeExceptBizBusiness.updateTradeExcept(staff, updateTrades, updateOrders, insertTrades, insertOrders);
            updateTradeExceptTook = System.currentTimeMillis() - startUpdateTradeExcept;
            //更新统计表
            long startStat = System.currentTimeMillis();
            updateTradeStat(staff, insertTrades, updateTrades,updateOrders,insertOrders);
            tradeStatTook = System.currentTimeMillis() - startStat;

            long syncTypeBegin = System.currentTimeMillis();
            //同步订单类型
            long startSyncType = System.currentTimeMillis();
            syncType(staff, insertTrades, updateTrades, insertOrders, updateOrders);
            syncTypeTook = System.currentTimeMillis() - startSyncType;

            long syncOrderItemTagBegin = System.currentTimeMillis();
            //同步商品标签
            long startSyncOrderItemTag = System.currentTimeMillis();
            syncOrderItemTag(staff, insertOrders, updateOrders);
            syncOrderItemTagTook = System.currentTimeMillis() - startSyncOrderItemTag;

            if (needFixOrderBelongSid) {
                long start = System.currentTimeMillis();
                orderFixBusiness.fixOrderBelongSid(staff, insertOrders);
                fixTook = System.currentTimeMillis() - start;
            }

            long startUpdateGrossProfit = System.currentTimeMillis();
            updateGrossProfit(staff, insertTrades, updateTrades);
            updateGrossProfitTook = System.currentTimeMillis() - startUpdateGrossProfit;

            //同步热点商品
            orderHotItemService.asyncWrite(staff, insertTrades, updateTrades, insertOrders, updateOrders);

            allTook = System.currentTimeMillis() - begin;

        } catch (Exception e) {
            String errorMsg = buildTradeUpdateErrorMsg(insertTrades, updateTrades);
            Logs.error(LogHelper.buildErrorLog(staff, e, "更新订单失败!!" + errorMsg), e);
            throw new TradeException("糟糕,更新订单失败!!" + errorMsg + " errMsg:" + e.getMessage(), e);
        } finally {
            if (allTook > 1500) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单修改比较慢,updateTradeSize=%s,updateOrderSize=%s,insertTradeSize=%s,insertOrderSize=%s,updateTradeTook=%s,updateOrderTook=%s,insertTradeTook=%s,insertOrderTook=%s,fixTook=%s,tradeStatTook=%s,encodeTradeTook=%s,updateTradeExceptTook=%s,syncTypeTook=%s,syncOrderItemTagTook=%s,updateGrossProfitTook=%s,allTook=%s", updateTradeSize, updateOrderSize, insertTradeSize, insertOrderSize, updateTradeTook, updateOrderTook, insertTradeTook, insertOrderTook, fixTook, tradeStatTook, encodeTradeTook, updateTradeExceptTook, syncTypeTook, syncOrderItemTagTook, updateGrossProfitTook, allTook)));
            }
        }
    }

    private void paymentCheckBefore(Staff staff, List<Trade> updateTrades, List<Order> updateOrders, List<Trade> insertTrades, List<Order> insertOrders) {
        try {
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                for (Trade trade : updateTrades) {
                    PaymentWhistler.checkTradeNumbers(staff, trade);
                }
            }
            if (CollectionUtils.isNotEmpty(updateOrders)) {
                for (Order order : updateOrders) {
                    PaymentWhistler.checkOrderNumbers(staff, order);
                }
            }
            if (CollectionUtils.isNotEmpty(insertTrades)) {
                for (Trade trade : insertTrades) {
                    PaymentWhistler.checkTradeNumbers(staff, trade);
                }
            }
            if (CollectionUtils.isNotEmpty(insertOrders)) {
                for (Order order : insertOrders) {
                    PaymentWhistler.checkOrderNumbers(staff, order);
                }
            }
        } catch (Throwable e) {
            Logs.error(new PaymentLogBuilder(staff).append("入库前金额检查出现异常").toString(), e);
        }
    }

    private void paymentCheckAfter(Staff staff,List<Trade> insertTrades, List<Trade> updateTrades,String scene) {
        try {
            if (!DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,staff.getCompanyId(),LogBusinessEnum.PAYMENT.getSign())) {
                return;
            }
            List<Trade> allTrades = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(insertTrades)) {
                allTrades.addAll(insertTrades);
            }
            Map<Long, List<Trade>> updateTradeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                allTrades.addAll(updateTrades);
                //一次更新操作 可能会更新同一个订单多次
                for (Trade updateTrade : updateTrades) {
                    updateTradeMap.computeIfAbsent(updateTrade.getSid(),k -> new ArrayList<>()).add(updateTrade);
                }
            }

            DevLogBuilder logBuilder = new DevLogBuilder(staff, LogBusinessEnum.PAYMENT.getSign());
            Exception e = new Exception();
            logBuilder.append(PaymentLogHelper.buildTradePaymentLogs(allTrades, scene +" 金额快照 ")).multiPrint(logger, Priority.DEBUG,e);
        }catch (Exception e){

        }
    }

    private void encodeTrades(Staff staff, List<Trade> updateTrades, List<Trade> insertTrades, boolean needSecret) {
        if (CollectionUtils.isNotEmpty(insertTrades)) {
            secretBusiness.encodeTrades(staff, insertTrades);
        }
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            if (needSecret) {
                secretBusiness.encodeTrades(staff, updateTrades);
            }
        }
    }

    /**
     * 兼容处理修改订单时，emoji报错问题
     */
    void handleTradeEmojiMsg(List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        for (Trade trade : updateTrades) {
            if (Strings.hasEmoji(trade.getBuyerNick())) {
                trade.setBuyerNick(Strings.replaceUtf8mb4(trade.getBuyerNick()));
            }
            if (Strings.hasEmoji(trade.getBuyerMessage())) {
                trade.setBuyerMessage(Strings.replaceUtf8mb4(trade.getBuyerMessage()));
            }
            if (Strings.hasEmoji(trade.getSellerMemo())) {
                trade.setSellerMemo(Strings.replaceUtf8mb4(trade.getSellerMemo()));
            }
        }
    }

    /**
     * 更新情况下做重排序
     */
    private void sortTradeOrOrders(Staff staff, List<Trade> updateTrades) {
        if (updateTrades != null && updateTrades.size() > 1) {
            updateTrades.sort((pre, next) -> {
                if (null != pre.getSid()) {
                    return pre.getSid().compareTo(next.getSid());
                } else {
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("通过tid更新订单，preSid=%s, nextSid=%s, preTid=%s，nextTid=%s", pre.getSid(), next.getSid(), pre.getTid(), next.getTid())));
                    return pre.getTid().compareTo(next.getTid());
                }
            });
        }
    }

    private void updateGrossProfit(Staff staff, List<Trade> insertTrades, List<Trade> updateTrades) {
        Set<Long> sids = new HashSet<>();
        try {
            if (CollectionUtils.isNotEmpty(insertTrades)) {
                insertTrades.forEach(t -> sids.add(t.getSid()));
            }
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                updateTrades.forEach(t -> {
                    if (t.getEnableStatus() == null || t.getEnableStatus() == 1) {
                        sids.add(t.getSid());
                    }
                });
            }
            if (sids.size() > 0) {
                grossProfitBusiness.updateGrossProfit(staff, sids.toArray(new Long[0]));
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("更新毛利润失败，sids=%s", sids)), e);
        }
    }

    public void updateTradeStat(Staff staff, List<Trade> insertTrades, List<Trade> updateTrades,List<Order> updateOrders,List<Order> insertOrders) {
        Set<Long> sids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(insertTrades)) {
            insertTrades.forEach(t -> sids.add(t.getSid()));
        }
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            updateTrades.forEach(t -> sids.add(t.getSid()));
        }
        if(CollectionUtils.isNotEmpty(updateOrders)){
            Set<Long> collect = updateOrders.stream().filter(order -> order.getSid() != null).map(Order::getSid).collect(Collectors.toSet());
            sids.addAll(collect);
        }
        if(CollectionUtils.isNotEmpty(insertOrders)){
            Set<Long> collect = insertOrders.stream().filter(order -> order.getSid() != null).map(Order::getSid).collect(Collectors.toSet());
            sids.addAll(collect);
        }

        Long[] sidArr = sids.toArray(new Long[0]);
        tradeStatSyncService.save(staff, sidArr);
    }

    private void syncType(Staff staff, List<Trade> insertTrades, List<Trade> updateTrades, List<Order> insertOrders, List<Order> updateOrders) {
        List<Trade> tradeTypes = new ArrayList<>();
        List<Order> orderTypes = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(insertTrades)) {
            tradeTypes.addAll(insertTrades);
        }
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            tradeTypes.addAll(updateTrades);
        }
        if (CollectionUtils.isNotEmpty(insertOrders)) {
            orderTypes.addAll(insertOrders);
        }
        if (CollectionUtils.isNotEmpty(updateOrders)) {
            orderTypes.addAll(updateOrders);
        }
        tradeTypeService.sync(staff, tradeTypes, orderTypes);
    }

    private void syncOrderItemTag(Staff staff, List<Order> insertOrders, List<Order> updateOrders) {
        List<Order> orderList = Lists.newArrayList();
        if (insertOrders != null && !insertOrders.isEmpty()) {
            orderList.addAll(insertOrders);
        }
        if (updateOrders != null && !updateOrders.isEmpty()) {
            orderList.addAll(updateOrders);
        }

        if (orderList.isEmpty()) {
            return;
        }

        tradeOrderSyncItemTagFill.syncTradeOrderItemTagByOrders(staff, orderList);
    }

    private List<Order> getOrder4Trade(List<Trade> trades) {
        List<Order> orders = Lists.newArrayList();
        if (trades == null || trades.isEmpty()) {
            return orders;
        }

        for (Trade trade : trades) {
            List<Order> tempOrder = TradeUtils.getOrders4Trade(trade);
            if (tempOrder != null && !tempOrder.isEmpty()) {
                orders.addAll(tempOrder);
            }
        }
        return orders;
    }

    /**
     * 清空未绑定订单的快递单号
     *
     * @param staff
     * @param outSidPoolList 运单池列表
     * @param tradeList      订单列表
     */
    @Override
    public void clearUnboundOutSids(Staff staff, List<OutSidPool> outSidPoolList, List<Trade> tradeList) {
        // 删除和订单相同模板的一单多包数据
        multiPacksPrintTradeLogService.deleteDetailsByTrades(staff, tradeList);

        // 找出状态是未绑定的运单
        List<Long> unBoundSids = outSidPoolList.stream().map(OutSidPool::getSid).collect(Collectors.toList());

        // 清空订单上运单号
        if (CollectionUtils.isNotEmpty(unBoundSids)) {
            List<Long> filterSidList = tradeList.stream().filter(x -> StringUtils.isBlank(x.getOutSid())).map(Trade::getSid).collect(Collectors.toList());
            List<Trade> updateTrades = Lists.newArrayListWithCapacity(unBoundSids.size());
            for (Long sid : unBoundSids) {
                // 过滤没有outSid的订单
                if (filterSidList.contains(sid)) {
                    continue;
                }
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(sid);
                updateTrade.setOutSid("");
                updateTrades.add(updateTrade);
            }

            tradePtService.saveByTrades(staff, updateTrades);
            updateTrades(staff, updateTrades, null, null, null, true);

        }
    }

    @Override
    @Transactional
    public void updatePrintTradesMergeProxy(Staff staff, List<Trade> updateTrades, Long[] mergeSids) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        // 填充合单订单，一起更新
        updateTrades = setMergeTrades(staff, updateTrades, mergeSids);
        updatePrintTrades(staff, updateTrades);
    }

    @Override
    @Transactional
    public void updatePrintTrades(Staff staff, List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        sortTradeOrOrders(staff, updateTrades);
        tbTradeDao.batchUpdate(staff, updateTrades);
    }

    @Override
    @Transactional
    public void updatePrintTradesRetry(Staff staff, List<Trade> updateTrades, String errorMessage) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        sortTradeOrOrders(staff, updateTrades);
        try {
            tbTradeDao.batchUpdate(staff, updateTrades);
            return;
        } catch (Exception e) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("第一次更新失败，准备重试。错误: %s", e.getMessage())));
        }

        // 第二次尝试
        try {
            tbTradeDao.batchUpdate(staff, updateTrades);
        } catch (Exception secondError) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("第二次更新失败，准备重试。错误: %s", secondError.getMessage())));
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 填充合单订单，一起更新*
     * @param staff
     * @param updateTrades
     * @return
     */
    public List<Trade> setMergeTrades(Staff staff, List<Trade> updateTrades, Long[] mergeSids) {
        Map<Long, List<Trade>> mergeSidAndTradeMap = new HashMap<>();
        if (Objects.nonNull(mergeSids) && mergeSids.length > 0) {
            mergeSidAndTradeMap = tradeSearchService.queryBySidsContainMergeTrade(staff, false, true, true, mergeSids)
                    .stream()
                    .collect(Collectors.groupingBy(Trade::getMergeSid));
        }

        if (MapUtils.isNotEmpty(mergeSidAndTradeMap)) {
            List<Trade> newUpdateTradeList = new ArrayList<>();
            for (Trade updateTrade : updateTrades) {
                if (mergeSidAndTradeMap.containsKey(updateTrade.getSid())) {
                    List<TbTrade> updateMergeTradeList = mergeSidAndTradeMap.get(updateTrade.getSid())
                            .stream()
                            .filter(trade -> !trade.getSid().equals(updateTrade.getSid()))
                            .map(trade -> {
                                TbTrade tbTrade = new TbTrade();
                                BeanUtils.copyProperties(updateTrade, tbTrade);
                                tbTrade.setSid(trade.getSid());
                                tbTrade.setTid(trade.getTid());
                                return tbTrade;
                            })
                            .collect(Collectors.toList());
                    newUpdateTradeList.addAll(updateMergeTradeList);
                }
            }
            newUpdateTradeList.addAll(updateTrades);
            return newUpdateTradeList;
        }
        return updateTrades;
    }
}

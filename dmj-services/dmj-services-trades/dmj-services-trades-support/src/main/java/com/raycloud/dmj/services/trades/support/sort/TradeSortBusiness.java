package com.raycloud.dmj.services.trades.support.sort;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.sort.SortContext;
import com.raycloud.dmj.domain.sort.enums.SortTypeEnum;
import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * @Auther mengfanguang
 * @Date 2023/10/31
 * @desc 订单排序 (只是运算，不进行出入库)
 */
@Service
public class TradeSortBusiness {


    @Resource
    List<ITradeSortService> tradeSortServices;


    public Map<SortTypeEnum, ITradeSortService> sortTypeEnumsMap;


    @PostConstruct
    public void init() {
        sortTypeEnumsMap = new HashMap<>(tradeSortServices.size());
        for (ITradeSortService business : tradeSortServices) {
            sortTypeEnumsMap.put(business.getSortTypeEnum(), business);
        }
    }


    public List<Trade> sort(Staff staff, SortContext sortContext) {
        List<Trade> returnTrades = new ArrayList<>();
        List<List<Trade>> needSortTrades = sortContext.getNeedSortTrades();
        if (CollectionUtils.isEmpty(needSortTrades)) {
            return new ArrayList<>();
        }
        List<SortTypeEnum> sortEnumList = sortContext.getSortEnumList();
        if (CollectionUtils.isEmpty(sortEnumList)) {
            return new ArrayList<>();
        }
        for (SortTypeEnum sortTypeEnum : sortEnumList) {
            ITradeSortService sortService = sortTypeEnumsMap.get(sortTypeEnum);
            if (Objects.isNull(sortService)) {
                continue;
            }
            sortService.sort(staff, sortContext);
        }
        for (List<Trade> list : sortContext.getNeedSortTrades()) {
            returnTrades.addAll(list);
        }
        return returnTrades;
    }


}
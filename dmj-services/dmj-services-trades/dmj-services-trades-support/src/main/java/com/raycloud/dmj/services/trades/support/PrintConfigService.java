package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.trade.PrintConfigDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.trades.PrintConfig;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.IPrintConfigService;
import com.raycloud.dmj.merchantCode.dto.AutoPrinterSettingDTO;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;
import org.apache.commons.lang3.StringUtils;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * PrintConfig  Service
 *
 * <AUTHOR>
 * @since 2019-05-17
 */
@Service
public class PrintConfigService implements IPrintConfigService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    PrintConfigDAO printConfigDAO;
    @Resource
    IOpLogService opLogService;

    /**
     * 插入
     */
    @Override
    public Integer addPrintConfig(Staff staff, PrintConfig printConfig) {
        /* 由于数据库是存的公司所有账号的打印机设置（json数组），
           而与前端交互是当前账号的打印机设置（单个json对象）
           所以需要处理：不是数组格式，则包装成数组
         */
        String autoPrinterSettings = printConfig.getAutoPrinterSettings();
        if (autoPrinterSettings != null && !autoPrinterSettings.trim().startsWith("[")) {
            AutoPrinterSettingDTO printerSetting = JSONObject.parseObject(autoPrinterSettings, AutoPrinterSettingDTO.class);
            printerSetting.setStaffId(staff.getId());
            autoPrinterSettings = "[" + JSONObject.toJSONString(printerSetting) + "]";
            printConfig.setAutoPrinterSettings(autoPrinterSettings);
        }
        return printConfigDAO.addPrintConfig(staff, printConfig);
    }

    /**
     * 查找用户的配置
     */
    @Override
    public PrintConfig getPrintConfigBtStaff(Staff staff) {
        PrintConfig printConfig = printConfigDAO.getPrintConfigBtStaff(staff);
        // 对autoPrinterSettings进行处理，从json数组中取出当前账号(staffId)的打印机设置
        if (printConfig != null) {
            Map<Long, AutoPrinterSettingDTO> printerSetMap = parsePrinterSettings(printConfig.getAutoPrinterSettings());
            AutoPrinterSettingDTO set = printerSetMap.get(staff.getId());
            printConfig.setAutoPrinterSettings(set == null ? "" : JSONObject.toJSONString(set));
        }
        return printConfig;
    }
//
//    /**
//     * 根据主键查找
//     */
//    @Override
//    public PrintConfig getPrintConfigByKey(Staff staff, Long id) {
//        return printConfigDAO.getPrintConfigByKey(staff, id);
//    }
//
//
//    /**
//     * 根据主键组进行查找
//     */
//    @Override
//    public List<PrintConfig> getPrintConfigByKeys(Staff staff, List<Long> idList) {
//        return printConfigDAO.getPrintConfigByKeys(staff, idList);
//    }
//
//    /**
//     * 根据主键删除
//     */
//    @Override
//    public Integer deleteByKey(Staff staff, Long id) {
//        return printConfigDAO.deleteByKey(staff, id);
//    }
//
//    /**
//     * 根据主键组进行删除
//     */
//    @Override
//    public Integer deleteByKeys(Staff staff, List<Long> idList) {
//        return printConfigDAO.deleteByKeys(staff, idList);
//    }

    /**
     * 根据主键更新
     */
    @Override
    public Integer updatePrintConfigByKey(Staff staff, PrintConfig printConfig) {
        return printConfigDAO.updatePrintConfigByKey(staff, printConfig);
    }

    /**
     * 保存配置
     */
    @Override
    public void savePrintConfig(Staff staff, PrintConfig printConfig) {
        PrintConfig oldConfig = printConfigDAO.getPrintConfigBtStaff(staff);
        if (oldConfig == null) {
            printConfig.setCopmanyId(staff.getCompanyId());
            addPrintConfig(staff, printConfig);
        } else {
            printConfig.setId(oldConfig.getId());
            translate(oldConfig, printConfig, staff.getId());
            updatePrintConfigByKey(staff, printConfig);
            buildUpdateLog(staff, printConfig, oldConfig);
        }
    }

    private void buildUpdateLog(Staff staff, PrintConfig newConf, PrintConfig oldConf) {
        StringBuilder buf = new StringBuilder();
        ConfigLogUtils.addLogContent(buf, "逻辑删除标志", newConf.getEnableStatus(), oldConf.getEnableStatus());
        ConfigLogUtils.addLogContent(buf, "打印编码排序方式", newConf.getMerchantCodeSort(), oldConf.getMerchantCodeSort());
        ConfigLogUtils.addLogContent(buf, "单件显示内容", newConf.getSingleRemark(), oldConf.getSingleRemark());
        ConfigLogUtils.addLogContent(buf, "多件显示内容", newConf.getMuchRemark(), oldConf.getMuchRemark());
        ConfigLogUtils.addLogContent(buf, "打印模版和打印机设置", newConf.getAutoPrinterSettings(), oldConf.getAutoPrinterSettings());
        if (newConf.getExtensiblePrintConfig() != null && oldConf.getExtensiblePrintConfig() != null) {
            ConfigLogUtils.addLogContent(buf, "云打印快递模板影藏停用模板", newConf.getExtensiblePrintConfig().getConcealDisableTempltate(), oldConf.getExtensiblePrintConfig().getConcealDisableTempltate());
            ConfigLogUtils.addLogContent(buf, "订单类型显示-单件显示", newConf.getExtensiblePrintConfig().getUniqueSingleText(), oldConf.getExtensiblePrintConfig().getUniqueSingleText());
            ConfigLogUtils.addLogContent(buf, "订单类型显示-多件显示", newConf.getExtensiblePrintConfig().getUniqueMultiText(), oldConf.getExtensiblePrintConfig().getUniqueMultiText());
            ConfigLogUtils.addLogContent(buf, "订单类型显示-备货显示", newConf.getExtensiblePrintConfig().getUniqueBackupText(), oldConf.getExtensiblePrintConfig().getUniqueBackupText());
            ConfigLogUtils.addLogContent(buf, "单多备排序规则", newConf.getExtensiblePrintConfig().getUniqueCodeSortType(), oldConf.getExtensiblePrintConfig().getUniqueCodeSortType());
            ConfigLogUtils.addLogContent(buf, "唯一码分组", newConf.getExtensiblePrintConfig().getUniqueSeparateType(), oldConf.getExtensiblePrintConfig().getUniqueSeparateType());
            ConfigLogUtils.addLogContent(buf, "标签来源", newConf.getExtensiblePrintConfig().getSource(), oldConf.getExtensiblePrintConfig().getSource());
            ConfigLogUtils.addLogContent(buf, "不同供应商之间打出空白标签", newConf.getExtensiblePrintConfig().getNeedSeparate(), oldConf.getExtensiblePrintConfig().getNeedSeparate());
            ConfigLogUtils.addLogContent(buf, "按急标/补标/新标优先级排序", newConf.getExtensiblePrintConfig().getUrgentTagSort(), oldConf.getExtensiblePrintConfig().getUrgentTagSort());
            ConfigLogUtils.addLogContent(buf, "按剩余时间升序排序", newConf.getExtensiblePrintConfig().getRemainTimeSort(), oldConf.getExtensiblePrintConfig().getRemainTimeSort());
            ConfigLogUtils.addLogContent(buf, "自选打印时间", newConf.getExtensiblePrintConfig().getCustomPrintTimeSet(), oldConf.getExtensiblePrintConfig().getCustomPrintTimeSet());
            ConfigLogUtils.addLogContent(buf, "编码模板ID", newConf.getExtensiblePrintConfig().getUniqueTemplateId(), oldConf.getExtensiblePrintConfig().getUniqueTemplateId());
            ConfigLogUtils.addLogContent(buf, "编码打印机", newConf.getExtensiblePrintConfig().getUniquePrinterName(), oldConf.getExtensiblePrintConfig().getUniquePrinterName());
            ConfigLogUtils.addLogContent(buf, "按供应商档案列表序号顺序排序", newConf.getExtensiblePrintConfig().getSupplierSort(), oldConf.getExtensiblePrintConfig().getSupplierSort());
        }
        if (buf.length() > 0) {
            addUpdateLog(staff, buf.toString());
        }
    }

    private void addUpdateLog(Staff staff, String content) {
        if (StringUtils.isNotEmpty(content)) {
            OpLog log = new OpLog();
            log.setDomain(OpLog.DOMAIN_TRADE);
            log.setAction("print.config.update");
            log.setKey(staff.getCompanyId().toString());
            log.setCompanyId(staff.getCompanyId());
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setAccountName(staff.getAccountName());
            log.setContent(content);
            opLogService.record(staff, log);
        }
    }

    private void translate(PrintConfig oldConfig, PrintConfig printConfig, Long staffId) {
        if (oldConfig.getExtensibleConfigJson() != null && printConfig.getExtensibleConfigJson() != null) {
            try {
                JSONObject newConfig = JSONObject.parseObject(printConfig.getExtensibleConfigJson());
                JSONObject old = JSONObject.parseObject(oldConfig.getExtensibleConfigJson());
                for (Map.Entry<String, Object> entry : newConfig.entrySet()) {
                    Object value = entry.getValue();
                    String key = entry.getKey();
                    old.put(key, value);
                }
                printConfig.setExtensibleConfigJson(old.toJSONString());
            } catch (Exception e) {
                Logs.error(String.format("translate printConfig err , errMsg 【%s】", e.getMessage()), e);
            }
        }
        // 更新当前staff账号下的自动打印机配置信息
        String autoPrinterSettings = printConfig.getAutoPrinterSettings();
        if (isNotBlank(autoPrinterSettings)) {
            logger.info("translate old autoPrinterSettings:" + oldConfig.getAutoPrinterSettings() + ",new settings:"+ autoPrinterSettings);
            Map<Long, AutoPrinterSettingDTO> printerSetMap = parsePrinterSettings(oldConfig.getAutoPrinterSettings());
            // 覆盖老配置 or 新增staff维度的配置
            AutoPrinterSettingDTO newPrintConfig = JSONObject.parseObject(autoPrinterSettings, AutoPrinterSettingDTO.class);
            newPrintConfig.setStaffId(staffId);
            printerSetMap.compute(staffId, (k, oldPrintConfig) -> AutoPrinterSettingDTO.combineSetting(oldPrintConfig, newPrintConfig));
            printConfig.setAutoPrinterSettings(JSONObject.toJSONString(printerSetMap.values()));
        }
    }

    /**
     * 解析 自动打印机与模版配置 信息
     * @param autoPrinterSettings 打印机与模版配置信息
     * @return K(staffId)-V(AutoPrinterSettingDTO)
     */
    private Map<Long, AutoPrinterSettingDTO> parsePrinterSettings(String autoPrinterSettings) {
        Map<Long, AutoPrinterSettingDTO> printerSetMap = new HashMap<>();
        if (isNotBlank(autoPrinterSettings)) {
            List<AutoPrinterSettingDTO> array = JSONArray.parseArray(autoPrinterSettings, AutoPrinterSettingDTO.class);
            for (AutoPrinterSettingDTO set : array) {
                printerSetMap.put(set.getStaffId(), set);
            }
        }
        return printerSetMap;
    }

}

package com.raycloud.dmj.services.logistics;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.ITradeLogisticsTrackingServices;

/**
 * AbstractTradeLogisticsTrackingServices
 *
 * <AUTHOR>
 * @Date 2019-08-03
 * @Time 18:08
 */
public abstract class AbstractTradeLogisticsTrackingServices implements ITradeLogisticsTrackingServices {
    @Override
    public Integer getOrder() {
        return 1;
    }

    @Override
    public boolean isAllow(Staff staff) {
        return true;
    }

    @Override
    public void end(Staff staff) {
    }
}

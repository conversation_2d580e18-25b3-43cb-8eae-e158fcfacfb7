package com.raycloud.dmj.services.trades;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStockUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2017-12-25-14:53
 */
@Deprecated
@Service
@Slf4j
public class TradeGiftMatchService implements ITradeGiftMatchService {


    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService solrTradeSearchService;

    @Resource(name = "tradeGiftMatchBusiness")
    ITradeBusiness tradeGiftMatchBusiness;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    private ILockService lockService;

    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;

    @Resource
    ITradeConfigService tradeConfigService;

    @Override
    public List<Trade> giftMatch(final Staff staff, final Long[] sidArray) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sidArray), () -> {
            List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, false, false,  sidArray);

            Collection<Trade> mergeTrades = filterMergeTrades(trades);
            Map<Long, String> mergesid2Payment = groupByMergesidPayment(mergeTrades);

            validate(staff, trades);
            trades = tradeAssembleBusiness.bindOrders(staff, trades, true);
            Collection<Trade> mergeTradesBindOrder = filterMergeTrades(trades);
            for (Trade trade : mergeTradesBindOrder) {
                trade.setPayment(mergesid2Payment.get(trade.getMergeSid()));
            }

            Map<Long, List<Trade>> userId2Trades = TradeUtils.toMapByUserId(trades);

            List<Order> stockOrders = new ArrayList<>();
            List<Trade> updateTrades = new ArrayList<>();
            Map<Long, Trade> tradeMap = new HashMap<>();

            for (Map.Entry<Long, List<Trade>> entry : userId2Trades.entrySet()) {
                User user = new User();
                user.setStaff(staff);
                user.setId(entry.getKey());
                tradeGiftMatchBusiness.handleInsert(user, entry.getValue(), null);
            }

            for (Trade trade : trades) {
                if (CollectionUtils.isEmpty(trade.getGiftOrders())) {
                    continue;
                }
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    //新增的赠品
                    if ( order.getGiftNum() != null && order.getGiftNum() > 0 && order.getFlag() == 1 &&
                            (Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus()))) {
                        stockOrders.add(order);
                        tradeMap.put(trade.getSid(), trade);
                    }
                }
            }

            if (!stockOrders.isEmpty()) {
                orderStockService.applyOrderStockLocal(staff, stockOrders);
                TradeConfig tradeConfig = tradeConfigService.get(staff);
                for (Trade trade : tradeMap.values()) {
                    TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
                    Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade,false);
                    updateTrade.setSid(trade.getSid());
                   // updateTrade.setStockStatus(trade.getStockStatus());
                    TradeExceptUtils.setStockStatus(staff,updateTrade,trade.getStockStatus());
                    TradeUtils.setTradeExcep(staff,trade, updateTrade);
                    TradeUtils.resetTradeItemNum(trade, tradeConfig);
                    updateTrade.setItemKindNum(trade.getItemKindNum());
                    updateTrade.setItemNum(trade.getItemNum());
                    updateTrade.setNetWeight(trade.getNetWeight());
                    updateTrade.setCost(trade.getCost());
                    updateTrade.setSaleFee(trade.getSaleFee());
                    updateTrade.setVolume(trade.getVolume());
                    updateTrades.add(updateTrade);
                }
                tradeUpdateService.updateTrades(staff, updateTrades, null, null, stockOrders);
            }
            eventCenter.fireEvent(this, new EventInfo("manual.giftmatch").setArgs(new Object[]{staff}), updateTrades);
            return updateTrades;
        });
    }

    private Map<Long, String> groupByMergesidPayment(Collection<Trade> mergeTrades) {
        Map<Long, String> map = new HashMap<Long, String>();
        Multimap<Long, Trade> mergeSid2Trades = Multimaps.index(mergeTrades, new Function<Trade, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable Trade input) {
                return input.getMergeSid();
            }
        });
        for (Long mergeSid : mergeSid2Trades.keySet()) {
            Collection<Trade> trades = mergeSid2Trades.get(mergeSid);
            BigDecimal tradePayment = new BigDecimal(0);
            Trade mainTrade = null;
            for (Trade trade : trades) {
                if (trade.getSid().equals(trade.getMergeSid())) {
                    mainTrade = trade;
                }
                tradePayment = tradePayment.add(new BigDecimal(trade.getPayment()));
            }
            if (mainTrade == null) {
                log.error("手动匹配赠品找不到主单,sid:" + mergeSid);
                continue;
            }
            map.put(mainTrade.getSid(), tradePayment.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
        return map;
    }

    private  Collection<Trade> filterMergeTrades(List<Trade> trades) {
        return Collections2.filter(trades, new Predicate<Trade>() {
            @Override
            public boolean apply(@Nullable Trade input) {
                return TradeUtils.isMerge(input);
            }
        });
    }

    private void validate(Staff staff, List<Trade> trades) {
        Iterator<Trade> it = trades.iterator();
        while (it.hasNext()) {
            Trade trade = it.next();
            User user = staff.getUserByUserId(trade.getUserId());
            Assert.isTrue(user != null, trade.getSid() + "没有订单所属店铺的权限");
            Assert.isTrue(user.getActive() - 1 == 0, trade.getSid() + "订单所属店铺已停用");
            Assert.isTrue(trade.getIsCancel() == 0, trade.getSid() + "订单已作废");
            Assert.isTrue(Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()), String.format("%s为非待审核订单，不能执行赠品匹配，请重新选择订单", trade.getSid()));
            if (trade.getMergeSid() > 0 && !trade.getSid().equals(trade.getMergeSid())) {
                //去掉合单中隐藏的订单
                it.remove();
            }
        }
    }
}

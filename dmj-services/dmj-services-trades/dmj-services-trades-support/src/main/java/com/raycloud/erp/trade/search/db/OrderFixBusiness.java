package com.raycloud.erp.trade.search.db;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.JdbcQueryDao;
import com.raycloud.dmj.dao.trade.update.OrderUpdateDao;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.trade.update.builder.OrderUpdateSqlBuilder;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by windy26205 on 19/8/1.
 */
@Service
public class OrderFixBusiness {

    private static final String TRADE_FIELDS = "sid,tid,merge_sid,merge_type,sys_status,enable_status,company_id,user_id";
    private static final String ORDER_FIELDS = "id,sid,tid,company_id,sys_status,stock_status,relation_changed,item_changed,insufficient_canceled,belong_sid";

    public static final String EC_NAME = "order.fix";
    @Resource
    TbTradeDao tbTradeDao;
    @Resource
    TbOrderDAO tbOrderDAO;
    @Resource
    JdbcQueryDao jdbcQueryDao;
    @Resource
    TradeConfigService tradeConfigService;
    @Resource
    IEventCenter eventCenter;

    @Resource
    OrderUpdateDao orderUpdateDao;

    public void fixOrderBelongSid(Staff staff, List<Order> orderList) {
        if (orderList != null && orderList.size() > 0) {
            Set<Long> sidList = new HashSet<>();
            for (Order order : orderList) {
                sidList.add(order.getSid());
            }
            if (TradeDiamondUtils.asyncFixOrder(staff)) {
                eventCenter.fireEvent(this, new EventInfo(EC_NAME).setArgs(new Object[]{staff, sidList.toArray(new Long[0])}), null);
            } else {
                update(staff, sidList.toArray(new Long[0]));
            }
        }
    }

    public long migrate(Staff staff) {
        Query q = new Query();
        q.append("SELECT ").append(TRADE_FIELDS).append(" FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status > 0 LIMIT ?, ?").add(staff.getCompanyId());
        Page page = new Page().setPageSize(1000).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        List<TbTrade> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbTrade.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            update(staff, TradeUtils.toMapBySid(list));
            r += list.size();
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("用户%s修复useNewQuery中,已修复%s", staff.getCompanyId(), r)));
            if (list.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
            q.getArgs().set(q.getArgs().size() - 2, page.getStartRow());
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        tradeConfig.setUseNewQuery(true);
        tradeConfigService.update(staff, "use_new_query", true);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("用户%s switch useNewQuery数据成功,useNewQuery切换成功", staff.getCompanyId())));
        return r;
    }

    public void update(Staff staff, Long... sids) {
        if (sids.length > 0) {
            List<TbTrade> tradeList = tbTradeDao.queryByKeys(staff, "trade_" + staff.getDbInfo().getTradeDbNo(), TRADE_FIELDS, true, "sid", sids);
            update(staff, TradeUtils.toMapBySid(tradeList));
        }
    }

    private void update(Staff staff, Map<Long, Trade> tradeMap) {
        List<TbOrder> orders = tbOrderDAO.queryByKeys(staff, true, ORDER_FIELDS, "sid", tradeMap.keySet().toArray(new Long[0]));
        if (CollectionUtils.isNotEmpty(orders)) {
            List<Order> updateOrders = new ArrayList<>();
            for (TbOrder order : orders) {
                Trade trade = tradeMap.get(order.getSid());
                long belongSid = trade.getSid();
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    belongSid = trade.getMergeSid();
                }
                if (order.getBelongSid() == null || order.getBelongSid() - belongSid != 0) {
                    Order updateOrder = new TbOrder();
                    updateOrder.setId(order.getId());
                    updateOrder.setBelongSid(belongSid);
                    updateOrders.add(updateOrder);
                }
            }
            if (updateOrders.size() > 0) {
                orderUpdateDao.batchUpdate(staff, updateOrders, new OrderUpdateSqlBuilder());
            }
        }
    }
}

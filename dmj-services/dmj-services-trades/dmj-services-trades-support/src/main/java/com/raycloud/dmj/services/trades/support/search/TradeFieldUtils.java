package com.raycloud.dmj.services.trades.support.search;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description <pre>
 *
 *  对查询结果中的一些字段的特殊处理
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-25
 */
public class TradeFieldUtils {


    /**
     * @Description <pre>
     *
     *  展示逻辑中有关buyerNick的处理
     * 1 淘系 买家昵称和openUid互换展示
     * ( source in （"tb" "tm" "tjb" "1688"）
     * or (source = "newFx" and sub_source in （"tb" "tm" "tjb" "1688"）)
     * or (source = "sys" and user.source in （"tb" "tm" "tjb" "1688"）)
     * )
     * AND buyerNick.length >= 24
     *
     * 2 buyerNick设置为receiverMobile
     * source = "alibabac2m"
     * or ( source = "sys" and user.source = "alibabac2m")
     *
     * </pre>
     * <AUTHOR>
     * @Date 2025-03-25
     */
    public static List<BuyerNickResult> handleBuyerNickAndOpenUids(Staff staff,List<Trade> trades){
        List<BuyerNickResult> list =new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return list;
        }
        for (Trade trade : trades) {
            BuyerNickResult nickResult = handleBuyerNickAndOpenUid(staff, trade);
            if (nickResult != null) {
                list.add(nickResult);
            }
        }
        return list;
    }

    public static BuyerNickResult handleBuyerNickAndOpenUid(Staff staff,Trade trade){
        if (trade == null) {
            return null;
        }

        Map<Long, User> userIdMap = staff.getUserIdMap();
        if (userIdMap == null && staff.getUsers() != null) {
            userIdMap = new HashMap<>();
            for (User user : staff.getUsers()) {
                userIdMap.put(user.getId(),user);
            }
        }
        Assert.notNull(userIdMap,"staff没有包含users信息");
        BuyerNickResult result = new BuyerNickResult(trade.getSid(),trade.getBuyerNick(),trade.getOpenUid());
        User user = userIdMap.get(trade.getUserId());
        switchOpenUidAndBuyerNick(user,trade,result);
        resetBuyerNick(user,trade,result);
        return result;
    }

    private static void switchOpenUidAndBuyerNick(User user,  Trade tbTrade,BuyerNickResult result) {
        if (tbTrade == null) {
            return;
        }
        if (needSwithOpenUidAndBuyerNickSource(tbTrade.getSource())
                || (CommonConstants.PLAT_FORM_TYPE_NEW_FX.equals(tbTrade.getSource()) && needSwithOpenUidAndBuyerNickSource(tbTrade.getSubSource()))//供销订单
                || ("sys".equals(tbTrade.getSource()) &&(user != null && needSwithOpenUidAndBuyerNickSource(user.getSource())) )) {
            if (StringUtils.isNotBlank(tbTrade.getOpenUid()) && tbTrade.getBuyerNick() != null && tbTrade.getBuyerNick().length() >= 24) {
                //代表buyerNick存的是openUid  两者互换  页面展示用
                result.setOpenUid(tbTrade.getBuyerNick());
                result.setBuyerNick(tbTrade.getOpenUid());
            }
        }
    }

    private static void resetBuyerNick(User user, Trade tbTrade,BuyerNickResult result) {
        if (tbTrade == null) {
            return;
        }
        if (CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(tbTrade.getSource())
                || ("sys".equals(tbTrade.getSource()) && (user != null && CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(user.getSource())))) {
            result.setBuyerNick(tbTrade.getReceiverMobile());
        }
    }

    private static boolean needSwithOpenUidAndBuyerNickSource(String source) {
        if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_1688.equals(source)) {
            return true;
        }
        return false;
    }

    public static class BuyerNickResult{

        private Long sid;

        private String buyerNick;

        private String openUid;

        public BuyerNickResult(Long sid, String buyerNick, String openUid) {
            this.sid = sid;
            this.buyerNick = buyerNick;
            this.openUid = openUid;
        }

        public Long getSid() {
            return sid;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }

        public String getBuyerNick() {
            return buyerNick;
        }

        public void setBuyerNick(String buyerNick) {
            this.buyerNick = buyerNick;
        }

        public String getOpenUid() {
            return openUid;
        }

        public void setOpenUid(String openUid) {
            this.openUid = openUid;
        }
    }

}

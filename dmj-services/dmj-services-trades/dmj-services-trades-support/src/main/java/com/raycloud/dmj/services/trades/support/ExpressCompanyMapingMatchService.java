package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.ExpressCompanyMappingBatchDTO;
import com.raycloud.dmj.domain.trades.bo.ExpressCompanyMappingBO;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 这里注意 几个常用的物流公司
 * 四通一达，是申通快递、圆通速递、中通快递、百世汇通、韵达快递
 */
@Service
public class ExpressCompanyMapingMatchService {
    private static final Logger logger = Logger.getLogger(ExpressCompanyMapingMatchService.class);
    private static List<KeyWord> KEY_BEAN_LIST = new ArrayList();

    @Autowired
    private IExpressCompanyService iExpressCompanyService;

    public List<ExpressCompanyMappingBO> createMappingDataByMap(ExpressCompanyMappingBatchDTO dto) {
        if (null == dto || dto.getMap().isEmpty()) {
            return Lists.newArrayList();
        }
        //erp物流公司list
        List<ExpressCompany> expressCompanies = JSONArray.parseArray(KM_EXPRESS_COMPANY_JSON, ExpressCompany.class);
        List<ExpressCompanyMappingBO> newBoList = new ArrayList<>();
        //生成映射对象
        for (Map.Entry<String, String> entry : dto.getMap().entrySet()) {
            //key
            Optional<ExpressCompany> expressCompanyOptional1 = expressCompanies.stream().filter(expressCompany -> entry.getKey().equals(expressCompany.getCustomName())).findFirst();

            Optional<ExpressCompany> expressCompanyOptional2 = expressCompanies.stream().filter(expressCompany -> entry.getKey().equals(expressCompany.getName())).findFirst();

            if (!expressCompanyOptional1.isPresent() && !expressCompanyOptional2.isPresent()) {
                //不存在则跳过
                continue;
            }
            ExpressCompany expressCompanyFormap = null;
            if (expressCompanyOptional1.isPresent()) {
                expressCompanyFormap = expressCompanyOptional1.get();
            } else {
                expressCompanyFormap = expressCompanyOptional2.get();

            }

            ExpressCompanyMappingBO bo = new ExpressCompanyMappingBO();
            bo.setMatchType("2");
            bo.setPlatformBizCode(entry.getValue() + "");
            bo.setPlatformBizName(entry.getKey());
            bo.setSource(dto.getSource());
            bo.setActive(true);
            bo.setEnableStatus(true);
            bo.setRelativeExpressCompanyId(expressCompanyFormap.getId());
            bo.setSysBizCode(expressCompanyFormap.getCode());
            bo.setSysBizName(expressCompanyFormap.getName());
            newBoList.add(bo);
        }
        return newBoList;
    }


    public List<ExpressCompanyMappingBO> createMappingDataByBoList(ExpressCompanyMappingBatchDTO dto) {
        if (CollectionUtils.isEmpty(dto.getList())) {
            return Lists.newArrayList();
        }
        //erp物流公司信息
        List<ExpressCompany> kmErpExpressCompanyList = iExpressCompanyService.getExpressCompanys();
        if (CollectionUtils.isEmpty(kmErpExpressCompanyList)) {
            logger.warn("没有查询到库中的物流公司列表使用硬编码的物流公司数据");
            kmErpExpressCompanyList = JSONArray.parseArray(KM_EXPRESS_COMPANY_JSON, ExpressCompany.class);
        }
        //这里是平台物流公司信息,class类按照自己的需要替换
        List<ExpressCompanyMappingBO> platformCompanies = dto.getList();
        long start = System.currentTimeMillis();
        List<ExpressCompanyMappingBO> allMatchList = new ArrayList<>();
        List<ExpressCompanyMappingBO> sameMatchList = new ArrayList<>();
        Iterator<ExpressCompanyMappingBO> allMatchIterator = platformCompanies.iterator();
        while (allMatchIterator.hasNext()) {
            ExpressCompanyMappingBO expressCompanyMappingBO = allMatchIterator.next();
            ExpressCompany expressCompanyMatch = matchByCompanyName(expressCompanyMappingBO, kmErpExpressCompanyList);
            ExpressCompanyMappingBO bo = getExpressCompanyMappingBO(expressCompanyMappingBO, dto.getSource(), dto.getMatchType());
            if (expressCompanyMatch != null) {
                bo.setRelativeExpressCompanyId(expressCompanyMatch.getId());
                bo.setSysBizCode(expressCompanyMatch.getCode());
                bo.setSysBizName(expressCompanyMatch.getName());
                allMatchList.add(bo);
                allMatchIterator.remove();
                continue;
            }
        }
        Iterator<ExpressCompanyMappingBO> keyWordMatchIterator = platformCompanies.iterator();
        while (keyWordMatchIterator.hasNext()) {
            ExpressCompanyMappingBO expressCompanyMappingBO = keyWordMatchIterator.next();
            ExpressCompanyMappingBO bo = getExpressCompanyMappingBO(expressCompanyMappingBO, dto.getSource(), dto.getMatchType());
            ExpressCompany expressCompanyMatch = keyWordBeanMatch(expressCompanyMappingBO, kmErpExpressCompanyList);
            if (expressCompanyMatch != null) {
                bo.setRelativeExpressCompanyId(expressCompanyMatch.getId());
                bo.setSysBizCode(expressCompanyMatch.getCode());
                bo.setSysBizName(expressCompanyMatch.getName());
                allMatchList.add(bo);
            } else {
                sameMatchList.add(bo);
            }
            keyWordMatchIterator.remove();
        }
        logger.debug("耗时:" + (System.currentTimeMillis() - start));
        logger.debug("匹配上的:" + JSONArray.toJSONString(allMatchList));
        logger.debug("没有匹配的:" + JSONArray.toJSONString(sameMatchList));
        return allMatchList;
    }


    /**
     * 关键字以及权重组合匹配
     */
    private ExpressCompany keyWordBeanMatch(ExpressCompanyMappingBO expressCompanyMappingBO, List<ExpressCompany> expressCompanies) {
        ExpressCompany tempExpressCompany = null;
        Iterator<ExpressCompany> expressCompanyIterator = expressCompanies.iterator();
        while (expressCompanyIterator.hasNext()) {
            ExpressCompany expressCompany = expressCompanyIterator.next();
            boolean containsTheSameKeyWords = isContainsByKeyWordBean(expressCompany.getName(), expressCompanyMappingBO.getPlatformBizName());
            if (containsTheSameKeyWords) {
                tempExpressCompany = expressCompany;
                expressCompanyIterator.remove();
                return tempExpressCompany;
            }
        }
        return null;
    }

    /**
     * 判断平台物流公司名称和erp物流公司名称是否都包含同一个相同权重的关键字
     */
    private boolean isContainsByKeyWordBean(String erpCompanyName, String threeCompanyName) {
        //找到平台物流公司匹配度最高的关键字 比如:"邮政小包"和"邮政" 邮政小包在数组中的权重要比"邮政"在数组中的索引要大
        Optional<KeyWord> erpMatchWord = KEY_BEAN_LIST.stream().filter(keyWord -> erpCompanyName.contains(keyWord.getWord())).max(Comparator.comparing(KeyWord::getWeight));

        Optional<KeyWord> threeeMathcWord = KEY_BEAN_LIST.stream().filter(keyWord -> threeCompanyName.contains(keyWord.getWord())).max(Comparator.comparing(KeyWord::getWeight));

        if (!erpMatchWord.isPresent() || !threeeMathcWord.isPresent()) {
            return false;
        }
        return erpMatchWord.get().getWord().equals(threeeMathcWord.get().getWord());
    }

    private ExpressCompanyMappingBO getExpressCompanyMappingBO(ExpressCompanyMappingBO expressCompanyMappingBO, String source, String matchType) {
        ExpressCompanyMappingBO bo = new ExpressCompanyMappingBO();
        //这里也可以不设置matchType的值
        bo.setMatchType(matchType);
        bo.setPlatformBizId(expressCompanyMappingBO.getPlatformBizId() + "");
        bo.setPlatformBizCode(expressCompanyMappingBO.getPlatformBizCode() + "");
        bo.setPlatformBizName(expressCompanyMappingBO.getPlatformBizName());
        bo.setSource(source);
        bo.setActive(true);
        bo.setEnableStatus(true);
        return bo;
    }


    /**
     * 使用物流公司名称来进行名称的完全匹配
     */
    private ExpressCompany matchByCompanyName(ExpressCompanyMappingBO expressCompanyMappingBO, List<ExpressCompany> expressCompanies) {
        String platformCompanyName = expressCompanyMappingBO.getPlatformBizName();
        ExpressCompany tempExpressCompany = null;
        Iterator<ExpressCompany> expressCompanyIterator = expressCompanies.iterator();
        while (expressCompanyIterator.hasNext()) {
            ExpressCompany expressCompany = expressCompanyIterator.next();
            if (platformCompanyName.equals(expressCompany.getName())) {
                tempExpressCompany = expressCompany;
                expressCompanyIterator.remove();
                return tempExpressCompany;
            }
        }
        return null;
    }

    //平台放对于一些快递的叫法确实不一样,关键字也不能保证完全的匹配,执行后结果需要在筛选一下.
    static {
        KEY_BEAN_LIST.add(new KeyWord("邮政", 1));
        KEY_BEAN_LIST.add(new KeyWord("邮政小包", 2));
        KEY_BEAN_LIST.add(new KeyWord("EMS", 1));
        KEY_BEAN_LIST.add(new KeyWord("申通", 1));
        KEY_BEAN_LIST.add(new KeyWord("圆通", 1));
        KEY_BEAN_LIST.add(new KeyWord("韵达", 1));
        KEY_BEAN_LIST.add(new KeyWord("韵达快运", 2));
        KEY_BEAN_LIST.add(new KeyWord("百世", 1));
        KEY_BEAN_LIST.add(new KeyWord("德邦", 1));
        KEY_BEAN_LIST.add(new KeyWord("如风达", 1));
        KEY_BEAN_LIST.add(new KeyWord("万象", 1));
        KEY_BEAN_LIST.add(new KeyWord("中邮", 1));
        KEY_BEAN_LIST.add(new KeyWord("日日顺", 1));
        KEY_BEAN_LIST.add(new KeyWord("苏宁", 1));
        KEY_BEAN_LIST.add(new KeyWord("佳怡", 1));
        KEY_BEAN_LIST.add(new KeyWord("安能", 1));
        KEY_BEAN_LIST.add(new KeyWord("中通", 1));
        KEY_BEAN_LIST.add(new KeyWord("顺丰", 1));
        KEY_BEAN_LIST.add(new KeyWord("京东", 1));
        KEY_BEAN_LIST.add(new KeyWord("京东大件物流", 2));
        KEY_BEAN_LIST.add(new KeyWord("盛辉", 1));
        KEY_BEAN_LIST.add(new KeyWord("邮政EMS", 1));
        KEY_BEAN_LIST.add(new KeyWord("联邦", 1));
        KEY_BEAN_LIST.add(new KeyWord("天天", 1));
        KEY_BEAN_LIST.add(new KeyWord("佳吉", 1));
        KEY_BEAN_LIST.add(new KeyWord("中铁", 1));
        KEY_BEAN_LIST.add(new KeyWord("联昊通", 1));
        KEY_BEAN_LIST.add(new KeyWord("丰网速运", 2));
        KEY_BEAN_LIST.add(new KeyWord("丰网", 1));
    }

    private static class KeyWord {
        //关键字
        String word;
        //权重
        int weight;

        public KeyWord() {
        }

        public KeyWord(String word, int weight) {
            this.word = word;
            this.weight = weight;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }
    }

    /**
     * 快麦erp物流公司json串
     */
    public static String KM_EXPRESS_COMPANY_JSON = "[\n" +
            "    {\n" +
            "        \"id\": 1,\n" +
            "        \"name\": \"中国邮政\",\n" +
            "        \"code\": \"POST\",\n" +
            "        \"consignCode\": \"POST\",\n" +
            "        \"customName\": \"邮政平邮\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 2,\n" +
            "        \"name\": \"EMS\",\n" +
            "        \"code\": \"EMS\",\n" +
            "        \"consignCode\": \"EMS\",\n" +
            "        \"customName\": \"EMS标准快递\",\n" +
            "        \"expressCompanyJdId\": 3668,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 3,\n" +
            "        \"name\": \"EMS经济快递\",\n" +
            "        \"code\": \"EYB\",\n" +
            "        \"consignCode\": \"EYB\",\n" +
            "        \"customName\": \"EMS经济快递\",\n" +
            "        \"expressCompanyJdId\": 465,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 90,\n" +
            "        \"name\": \"晟邦物流\",\n" +
            "        \"code\": \"CHENGBANG\",\n" +
            "        \"consignCode\": \"CHENGBANG\",\n" +
            "        \"customName\": \"晟邦物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551945246000,\n" +
            "        \"modified\": 1551945252000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 100,\n" +
            "        \"name\": \"申通快递\",\n" +
            "        \"code\": \"STO\",\n" +
            "        \"consignCode\": \"STO\",\n" +
            "        \"customName\": \"申通快递\",\n" +
            "        \"expressCompanyJdId\": 470,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 435\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 101,\n" +
            "        \"name\": \"圆通速递\",\n" +
            "        \"code\": \"YTO\",\n" +
            "        \"consignCode\": \"YTO\",\n" +
            "        \"customName\": \"圆通速递\",\n" +
            "        \"expressCompanyJdId\": 463,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"YTO\",\n" +
            "        \"shopeeLogisticId\": 285\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 102,\n" +
            "        \"name\": \"韵达快递\",\n" +
            "        \"code\": \"YUNDA\",\n" +
            "        \"consignCode\": \"YUNDA\",\n" +
            "        \"customName\": \"韵达快递\",\n" +
            "        \"expressCompanyJdId\": 1327,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"YUNDA\",\n" +
            "        \"shopeeLogisticId\": 222\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 103,\n" +
            "        \"name\": \"宅急送\",\n" +
            "        \"code\": \"ZJS\",\n" +
            "        \"consignCode\": \"ZJS\",\n" +
            "        \"customName\": \"宅急送\",\n" +
            "        \"expressCompanyJdId\": 1409,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"ZJS\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 105,\n" +
            "        \"name\": \"百世物流\",\n" +
            "        \"code\": \"BEST\",\n" +
            "        \"consignCode\": \"BEST\",\n" +
            "        \"customName\": \"百世物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 106,\n" +
            "        \"name\": \"联邦快递\",\n" +
            "        \"code\": \"FEDEX\",\n" +
            "        \"consignCode\": \"FEDEX\",\n" +
            "        \"customName\": \"联邦快递\",\n" +
            "        \"expressCompanyJdId\": 2096,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 107,\n" +
            "        \"name\": \"德邦物流\",\n" +
            "        \"code\": \"DBL\",\n" +
            "        \"consignCode\": \"DBL\",\n" +
            "        \"customName\": \"德邦物流\",\n" +
            "        \"expressCompanyJdId\": 2130,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897819000,\n" +
            "        \"modified\": 1407897819000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 108,\n" +
            "        \"name\": \"华强物流\",\n" +
            "        \"code\": \"SHQ\",\n" +
            "        \"consignCode\": \"SHQ\",\n" +
            "        \"customName\": \"华强物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 130,\n" +
            "        \"name\": \"拼多多:如风达\",\n" +
            "        \"code\": \"RFD\",\n" +
            "        \"consignCode\": \"RFD\",\n" +
            "        \"customName\": \"拼多多:如风达\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551948746000,\n" +
            "        \"modified\": 1551948752000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 132,\n" +
            "        \"name\": \"邮政快递包裹\",\n" +
            "        \"code\": \"YZXB\",\n" +
            "        \"consignCode\": \"YZXB\",\n" +
            "        \"customName\": \"邮政快递包裹\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551948942000,\n" +
            "        \"modified\": 1551948946000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 140,\n" +
            "        \"name\": \"南京晟邦\",\n" +
            "        \"code\": \"NJCHENGBANG\",\n" +
            "        \"consignCode\": \"NJCHENGBANG\",\n" +
            "        \"customName\": \"南京晟邦\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551949392000,\n" +
            "        \"modified\": 1551949397000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 142,\n" +
            "        \"name\": \"万象物流\",\n" +
            "        \"code\": \"WXWL\",\n" +
            "        \"consignCode\": \"WXWL\",\n" +
            "        \"customName\": \"万象物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551949524000,\n" +
            "        \"modified\": 1551949528000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 148,\n" +
            "        \"name\": \"安达信\",\n" +
            "        \"code\": \"ADX\",\n" +
            "        \"consignCode\": \"ADX\",\n" +
            "        \"customName\": \"安达信\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551949807000,\n" +
            "        \"modified\": 1551949813000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 149,\n" +
            "        \"name\": \"海外快递\",\n" +
            "        \"code\": \"HWKD\",\n" +
            "        \"consignCode\": \"HWKD\",\n" +
            "        \"customName\": \"海外快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551952573000,\n" +
            "        \"modified\": 1551952578000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 154,\n" +
            "        \"name\": \"黄马甲\",\n" +
            "        \"code\": \"HUANGMAJIA\",\n" +
            "        \"consignCode\": \"HUANGMAJIA\",\n" +
            "        \"customName\": \"黄马甲\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551952646000,\n" +
            "        \"modified\": 1551952651000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 156,\n" +
            "        \"name\": \"亚马逊物流\",\n" +
            "        \"code\": \"YAMAXUNWULIU\",\n" +
            "        \"consignCode\": \"YAMAXUNWULIU\",\n" +
            "        \"customName\": \"亚马逊物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552010655000,\n" +
            "        \"modified\": 1552010659000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 163,\n" +
            "        \"name\": \"芝麻开门\",\n" +
            "        \"code\": \"ZMKM\",\n" +
            "        \"consignCode\": \"ZMKM\",\n" +
            "        \"customName\": \"芝麻开门\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552010820000,\n" +
            "        \"modified\": 1552010824000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 165,\n" +
            "        \"name\": \"汇通小红马\",\n" +
            "        \"code\": \"HTXMJ\",\n" +
            "        \"consignCode\": \"HTXMJ\",\n" +
            "        \"customName\": \"汇通小红马\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552010866000,\n" +
            "        \"modified\": 1552010871000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 180,\n" +
            "        \"name\": \"拼多多:其他\",\n" +
            "        \"code\": \"SELF\",\n" +
            "        \"consignCode\": \"SELF\",\n" +
            "        \"customName\": \"拼多多:其他\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552010987000,\n" +
            "        \"modified\": 1552010991000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 187,\n" +
            "        \"name\": \"青岛安捷\",\n" +
            "        \"code\": \"ANJELEX\",\n" +
            "        \"consignCode\": \"ANJELEX\",\n" +
            "        \"customName\": \"青岛安捷\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552011298000,\n" +
            "        \"modified\": 1552011302000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 205,\n" +
            "        \"name\": \"远成快运\",\n" +
            "        \"code\": \"YCGWL\",\n" +
            "        \"consignCode\": \"YCGWL\",\n" +
            "        \"customName\": \"远成快运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552011793000,\n" +
            "        \"modified\": 1552011797000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 211,\n" +
            "        \"name\": \"中邮速递\",\n" +
            "        \"code\": \"ZHONGYOUWULIU\",\n" +
            "        \"consignCode\": \"ZHONGYOUWULIU\",\n" +
            "        \"customName\": \"中邮速递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012117000,\n" +
            "        \"modified\": 1552012121000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 213,\n" +
            "        \"name\": \"EMS-国际件\",\n" +
            "        \"code\": \"INTEREMS\",\n" +
            "        \"consignCode\": \"INTEREMS\",\n" +
            "        \"customName\": \"EMS-国际件\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012256000,\n" +
            "        \"modified\": 1552012260000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 214,\n" +
            "        \"name\": \"中铁物流(飞豹)\",\n" +
            "        \"code\": \"ZTKY\",\n" +
            "        \"consignCode\": \"ZTKY\",\n" +
            "        \"customName\": \"中铁物流(飞豹)\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012672000,\n" +
            "        \"modified\": 1552012677000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 217,\n" +
            "        \"name\": \"Flash Express\",\n" +
            "        \"code\": \"FLASH\",\n" +
            "        \"consignCode\": \"FLASH\",\n" +
            "        \"customName\": \"Flash Express\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012832000,\n" +
            "        \"modified\": 1552012835000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 227,\n" +
            "        \"name\": \"日日顺物流\",\n" +
            "        \"code\": \"RRS\",\n" +
            "        \"consignCode\": \"RRS\",\n" +
            "        \"customName\": \"日日顺物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012924000,\n" +
            "        \"modified\": 1552012928000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 228,\n" +
            "        \"name\": \"苏宁快递\",\n" +
            "        \"code\": \"SNWL\",\n" +
            "        \"consignCode\": \"SNWL\",\n" +
            "        \"customName\": \"苏宁快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552012992000,\n" +
            "        \"modified\": 1552012995000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 231,\n" +
            "        \"name\": \"拼多多:微特派\",\n" +
            "        \"code\": \"WEITEPAI\",\n" +
            "        \"consignCode\": \"WEITEPAI\",\n" +
            "        \"customName\": \"拼多多:微特派\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552013756000,\n" +
            "        \"modified\": 1552013759000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 232,\n" +
            "        \"name\": \"AAE全球专递\",\n" +
            "        \"code\": \"MYAAE\",\n" +
            "        \"consignCode\": \"MYAAE\",\n" +
            "        \"customName\": \"AAE全球专递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552013960000,\n" +
            "        \"modified\": 1552013964000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 233,\n" +
            "        \"name\": \"Aramex\",\n" +
            "        \"code\": \"ARAMEX\",\n" +
            "        \"consignCode\": \"ARAMEX\",\n" +
            "        \"customName\": \"Aramex\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014371000,\n" +
            "        \"modified\": 1552014375000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 239,\n" +
            "        \"name\": \"DHL中国\",\n" +
            "        \"code\": \"DHLCN\",\n" +
            "        \"consignCode\": \"DHLCN\",\n" +
            "        \"customName\": \"DHL中国\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014501000,\n" +
            "        \"modified\": 1552014504000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 246,\n" +
            "        \"name\": \"UPS\",\n" +
            "        \"code\": \"UPS\",\n" +
            "        \"consignCode\": \"UPS\",\n" +
            "        \"customName\": \"UPS\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014555000,\n" +
            "        \"modified\": 1552014559000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 262,\n" +
            "        \"name\": \"传喜物流\",\n" +
            "        \"code\": \"CXCOD\",\n" +
            "        \"consignCode\": \"CXCOD\",\n" +
            "        \"customName\": \"传喜物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014667000,\n" +
            "        \"modified\": 1552014670000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 264,\n" +
            "        \"name\": \"递四方\",\n" +
            "        \"code\": \"4PX\",\n" +
            "        \"consignCode\": \"4PX\",\n" +
            "        \"customName\": \"递四方\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014781000,\n" +
            "        \"modified\": 1552014785000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 273,\n" +
            "        \"name\": \"恒路物流\",\n" +
            "        \"code\": \"HLWL\",\n" +
            "        \"consignCode\": \"HLWL\",\n" +
            "        \"customName\": \"恒路物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014889000,\n" +
            "        \"modified\": 1552014892000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 275,\n" +
            "        \"name\": \"加运美\",\n" +
            "        \"code\": \"TMS56\",\n" +
            "        \"consignCode\": \"TMS56\",\n" +
            "        \"customName\": \"加运美\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552014930000,\n" +
            "        \"modified\": 1552014932000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 277,\n" +
            "        \"name\": \"佳怡物流\",\n" +
            "        \"code\": \"JIAYI\",\n" +
            "        \"consignCode\": \"JIAYI\",\n" +
            "        \"customName\": \"佳怡物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552015197000,\n" +
            "        \"modified\": 1552015201000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 281,\n" +
            "        \"name\": \"京广速递\",\n" +
            "        \"code\": \"CP449455\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13462882\",\n" +
            "        \"customName\": \"京广速递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552015369000,\n" +
            "        \"modified\": 1552015373000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 291,\n" +
            "        \"name\": \"民航快递\",\n" +
            "        \"code\": \"CAE\",\n" +
            "        \"consignCode\": \"CAE\",\n" +
            "        \"customName\": \"民航快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552015718000,\n" +
            "        \"modified\": 1552015721000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 305,\n" +
            "        \"name\": \"盛丰物流\",\n" +
            "        \"code\": \"FJSFWLJTYXGS\",\n" +
            "        \"consignCode\": \"FJSFWLJTYXGS\",\n" +
            "        \"customName\": \"盛丰物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552015902000,\n" +
            "        \"modified\": 1552015905000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 306,\n" +
            "        \"name\": \"拼多多:盛辉物流\",\n" +
            "        \"code\": \"SHENGHUI\",\n" +
            "        \"consignCode\": \"SHENGHUI\",\n" +
            "        \"customName\": \"拼多多:盛辉物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552015997000,\n" +
            "        \"modified\": 1552016000000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 312,\n" +
            "        \"name\": \"万家物流\",\n" +
            "        \"code\": \"MANCOWL\",\n" +
            "        \"consignCode\": \"MANCOWL\",\n" +
            "        \"customName\": \"万家物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016096000,\n" +
            "        \"modified\": 1552016100000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 319,\n" +
            "        \"name\": \"燕文物流\",\n" +
            "        \"code\": \"YWWL\",\n" +
            "        \"consignCode\": \"YWWL\",\n" +
            "        \"customName\": \"燕文物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016278000,\n" +
            "        \"modified\": 1552016281000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 324,\n" +
            "        \"name\": \"邮政标准快递\",\n" +
            "        \"code\": \"POSTBBZ\",\n" +
            "        \"consignCode\": \"POSTBBZ\",\n" +
            "        \"customName\": \"邮政标准快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016360000,\n" +
            "        \"modified\": 1552016363000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 325,\n" +
            "        \"name\": \"邮政国际包裹\",\n" +
            "        \"code\": \"CNPOSTGJ\",\n" +
            "        \"consignCode\": \"CNPOSTGJ\",\n" +
            "        \"customName\": \"邮政国际包裹\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016451000,\n" +
            "        \"modified\": 1552016454000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 335,\n" +
            "        \"name\": \"优邦国际速运\",\n" +
            "        \"code\": \"YOUBANG\",\n" +
            "        \"consignCode\": \"YOUBANG\",\n" +
            "        \"customName\": \"优邦国际速运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016543000,\n" +
            "        \"modified\": 1552016546000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 336,\n" +
            "        \"name\": \"天际快递\",\n" +
            "        \"code\": \"TJ\",\n" +
            "        \"consignCode\": \"TJ\",\n" +
            "        \"customName\": \"天际快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016620000,\n" +
            "        \"modified\": 1552016623000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 337,\n" +
            "        \"name\": \"飞洋快递\",\n" +
            "        \"code\": \"FY\",\n" +
            "        \"consignCode\": \"FY\",\n" +
            "        \"customName\": \"飞洋快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016660000,\n" +
            "        \"modified\": 1552016662000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 338,\n" +
            "        \"name\": \"拼多多:斑马物联网\",\n" +
            "        \"code\": \"BM\",\n" +
            "        \"consignCode\": \"BM\",\n" +
            "        \"customName\": \"拼多多:斑马物联网\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016739000,\n" +
            "        \"modified\": 1552016743000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 339,\n" +
            "        \"name\": \"易客满\",\n" +
            "        \"code\": \"EKM\",\n" +
            "        \"consignCode\": \"EKM\",\n" +
            "        \"customName\": \"易客满\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016883000,\n" +
            "        \"modified\": 1552016887000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 340,\n" +
            "        \"name\": \"京东大件物流\",\n" +
            "        \"code\": \"JDKD\",\n" +
            "        \"consignCode\": \"JDKD\",\n" +
            "        \"customName\": \"京东大件物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552016951000,\n" +
            "        \"modified\": 1552016954000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 341,\n" +
            "        \"name\": \"速必达\",\n" +
            "        \"code\": \"SUBIDA\",\n" +
            "        \"consignCode\": \"SUBIDA\",\n" +
            "        \"customName\": \"速必达\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017006000,\n" +
            "        \"modified\": 1552017009000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 342,\n" +
            "        \"name\": \"东骏快捷\",\n" +
            "        \"code\": \"DJKJWL\",\n" +
            "        \"consignCode\": \"DJKJWL\",\n" +
            "        \"customName\": \"东骏快捷\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017044000,\n" +
            "        \"modified\": 1552017048000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 344,\n" +
            "        \"name\": \"韵达快运\",\n" +
            "        \"code\": \"CN7000001021040\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13421750\",\n" +
            "        \"customName\": \"韵达快运\",\n" +
            "        \"expressCompanyJdId\": 731302,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017179000,\n" +
            "        \"modified\": 1552017182000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 767\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 345,\n" +
            "        \"name\": \"安能快运\",\n" +
            "        \"code\": \"CN7000001000869\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_12017865\",\n" +
            "        \"customName\": \"安能快运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017231000,\n" +
            "        \"modified\": 1552017234000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 346,\n" +
            "        \"name\": \"安得物流\",\n" +
            "        \"code\": \"ANDE\",\n" +
            "        \"consignCode\": \"ANDE\",\n" +
            "        \"customName\": \"安得物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017298000,\n" +
            "        \"modified\": 1552017302000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 347,\n" +
            "        \"name\": \"中粮我买网\",\n" +
            "        \"code\": \"WM\",\n" +
            "        \"consignCode\": \"WM\",\n" +
            "        \"customName\": \"中粮我买网\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552017409000,\n" +
            "        \"modified\": 1552017412000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 348,\n" +
            "        \"name\": \"壹米滴答\",\n" +
            "        \"code\": \"YMDD\",\n" +
            "        \"consignCode\": \"YMDD\",\n" +
            "        \"customName\": \"壹米滴答\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024469000,\n" +
            "        \"modified\": 1552024473000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 349,\n" +
            "        \"name\": \"当当网\",\n" +
            "        \"code\": \"DD\",\n" +
            "        \"consignCode\": \"DD\",\n" +
            "        \"customName\": \"当当网\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024521000,\n" +
            "        \"modified\": 1552024525000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 350,\n" +
            "        \"name\": \"品骏\",\n" +
            "        \"code\": \"PJ\",\n" +
            "        \"consignCode\": \"PJ\",\n" +
            "        \"customName\": \"品骏\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024559000,\n" +
            "        \"modified\": 1552024562000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 351,\n" +
            "        \"name\": \"承诺达特快\",\n" +
            "        \"code\": \"OTP\",\n" +
            "        \"consignCode\": \"OTP\",\n" +
            "        \"customName\": \"承诺达特快\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024827000,\n" +
            "        \"modified\": 1552024831000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 352,\n" +
            "        \"name\": \"安迅物流\",\n" +
            "        \"code\": \"AXWL\",\n" +
            "        \"consignCode\": \"AXWL\",\n" +
            "        \"customName\": \"安迅物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024879000,\n" +
            "        \"modified\": 1552024882000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 354,\n" +
            "        \"name\": \"D速物流\",\n" +
            "        \"code\": \"SDSD\",\n" +
            "        \"consignCode\": \"SDSD\",\n" +
            "        \"customName\": \"D速物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1552024937000,\n" +
            "        \"modified\": 1552024941000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 500,\n" +
            "        \"name\": \"中通快递\",\n" +
            "        \"code\": \"ZTO\",\n" +
            "        \"consignCode\": \"ZTO\",\n" +
            "        \"customName\": \"中通快递\",\n" +
            "        \"expressCompanyJdId\": 1499,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"ZTO\",\n" +
            "        \"shopeeLogisticId\": 371\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 502,\n" +
            "        \"name\": \"百世汇通\",\n" +
            "        \"code\": \"HTKY\",\n" +
            "        \"consignCode\": \"HTKY\",\n" +
            "        \"customName\": \"百世汇通\",\n" +
            "        \"expressCompanyJdId\": 1748,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 504,\n" +
            "        \"name\": \"天天快递\",\n" +
            "        \"code\": \"TTKDEX\",\n" +
            "        \"consignCode\": \"TTKDEX\",\n" +
            "        \"customName\": \"天天快递\",\n" +
            "        \"expressCompanyJdId\": 2009,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 505,\n" +
            "        \"name\": \"顺丰速运\",\n" +
            "        \"code\": \"SF\",\n" +
            "        \"consignCode\": \"SF\",\n" +
            "        \"customName\": \"顺丰速运\",\n" +
            "        \"expressCompanyJdId\": 467,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"SF\",\n" +
            "        \"shopeeLogisticId\": 541\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 507,\n" +
            "        \"name\": \"亚风\",\n" +
            "        \"code\": \"AIR\",\n" +
            "        \"consignCode\": \"AIR\",\n" +
            "        \"customName\": \"亚风\",\n" +
            "        \"expressCompanyJdId\": 323141,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 511,\n" +
            "        \"name\": \"长宇\",\n" +
            "        \"code\": \"CYEXP\",\n" +
            "        \"consignCode\": \"CYEXP\",\n" +
            "        \"customName\": \"长宇\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 512,\n" +
            "        \"name\": \"大田\",\n" +
            "        \"code\": \"DTW\",\n" +
            "        \"consignCode\": \"DTW\",\n" +
            "        \"customName\": \"大田\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 513,\n" +
            "        \"name\": \"长发\",\n" +
            "        \"code\": \"YUD\",\n" +
            "        \"consignCode\": \"YUD\",\n" +
            "        \"customName\": \"长发\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1016,\n" +
            "        \"name\": \"中铁快运\",\n" +
            "        \"code\": \"CRE\",\n" +
            "        \"consignCode\": \"CRE\",\n" +
            "        \"customName\": \"中铁快运(飞豹)\",\n" +
            "        \"expressCompanyJdId\": 466,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1056,\n" +
            "        \"name\": \"佳吉快递\",\n" +
            "        \"code\": \"CNEX\",\n" +
            "        \"consignCode\": \"CNEX\",\n" +
            "        \"customName\": \"佳吉快递\",\n" +
            "        \"expressCompanyJdId\": 2460,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1121,\n" +
            "        \"name\": \"飞远(爱彼西)配送\",\n" +
            "        \"code\": \"HZABC\",\n" +
            "        \"consignCode\": \"HZABC\",\n" +
            "        \"customName\": \"飞远(爱彼西)配送\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1137,\n" +
            "        \"name\": \"东方汇\",\n" +
            "        \"code\": \"DFH\",\n" +
            "        \"consignCode\": \"DFH\",\n" +
            "        \"customName\": \"东方汇\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1138,\n" +
            "        \"name\": \"首业\",\n" +
            "        \"code\": \"SY\",\n" +
            "        \"consignCode\": \"SY\",\n" +
            "        \"customName\": \"首业\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1139,\n" +
            "        \"name\": \"远长\",\n" +
            "        \"code\": \"YC\",\n" +
            "        \"consignCode\": \"YC\",\n" +
            "        \"customName\": \"远长\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1185,\n" +
            "        \"name\": \"黑猫宅急便\",\n" +
            "        \"code\": \"YCT\",\n" +
            "        \"consignCode\": \"YCT\",\n" +
            "        \"customName\": \"黑猫宅急便\",\n" +
            "        \"expressCompanyJdId\": 1549,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1186,\n" +
            "        \"name\": \"新邦物流\",\n" +
            "        \"code\": \"XB\",\n" +
            "        \"consignCode\": \"XB\",\n" +
            "        \"customName\": \"新邦物流\",\n" +
            "        \"expressCompanyJdId\": 2461,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1191,\n" +
            "        \"name\": \"天地华宇\",\n" +
            "        \"code\": \"HOAU\",\n" +
            "        \"consignCode\": \"HOAU\",\n" +
            "        \"customName\": \"天地华宇\",\n" +
            "        \"expressCompanyJdId\": 2462,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1192,\n" +
            "        \"name\": \"能达速递\",\n" +
            "        \"code\": \"NEDA\",\n" +
            "        \"consignCode\": \"NEDA\",\n" +
            "        \"customName\": \"能达速递\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1195,\n" +
            "        \"name\": \"龙邦速递\",\n" +
            "        \"code\": \"LB\",\n" +
            "        \"consignCode\": \"LB\",\n" +
            "        \"customName\": \"龙邦速递\",\n" +
            "        \"expressCompanyJdId\": 471,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1204,\n" +
            "        \"name\": \"快捷快递\",\n" +
            "        \"code\": \"FAST\",\n" +
            "        \"consignCode\": \"FAST\",\n" +
            "        \"customName\": \"快捷快递\",\n" +
            "        \"expressCompanyJdId\": 2094,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1207,\n" +
            "        \"name\": \"优速快递\",\n" +
            "        \"code\": \"UC\",\n" +
            "        \"consignCode\": \"UC\",\n" +
            "        \"customName\": \"优速快递\",\n" +
            "        \"expressCompanyJdId\": 1747,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"UC\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1208,\n" +
            "        \"name\": \"增益速递\",\n" +
            "        \"code\": \"QRT\",\n" +
            "        \"consignCode\": \"QRT\",\n" +
            "        \"customName\": \"增益速递\",\n" +
            "        \"expressCompanyJdId\": 3044,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1214,\n" +
            "        \"name\": \"联昊通\",\n" +
            "        \"code\": \"LTS\",\n" +
            "        \"consignCode\": \"LTS\",\n" +
            "        \"customName\": \"联昊通\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1216,\n" +
            "        \"name\": \"全峰快递\",\n" +
            "        \"code\": \"QFKD\",\n" +
            "        \"consignCode\": \"QFKD\",\n" +
            "        \"customName\": \"全峰快递\",\n" +
            "        \"expressCompanyJdId\": 2016,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1237,\n" +
            "        \"name\": \"发网\",\n" +
            "        \"code\": \"UNIPS\",\n" +
            "        \"consignCode\": \"UNIPS\",\n" +
            "        \"customName\": \"发网\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1259,\n" +
            "        \"name\": \"全一快递\",\n" +
            "        \"code\": \"UAPEX\",\n" +
            "        \"consignCode\": \"UAPEX\",\n" +
            "        \"customName\": \"全一快递\",\n" +
            "        \"expressCompanyJdId\": 2100,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1262,\n" +
            "        \"name\": \"城市100\",\n" +
            "        \"code\": \"BJCS\",\n" +
            "        \"consignCode\": \"BJCS\",\n" +
            "        \"customName\": \"城市100\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1269,\n" +
            "        \"name\": \"广东EMS\",\n" +
            "        \"code\": \"GDEMS\",\n" +
            "        \"consignCode\": \"GDEMS\",\n" +
            "        \"customName\": \"广东EMS\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1270,\n" +
            "        \"name\": \"加运美速递\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"加运美速递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1557197168000,\n" +
            "        \"modified\": 1557197171000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1271,\n" +
            "        \"name\": \"极兔速递\",\n" +
            "        \"code\": \"JTSD\",\n" +
            "        \"consignCode\": \"JTSD\",\n" +
            "        \"customName\": \"极兔速递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1588661633000,\n" +
            "        \"modified\": 1588661633000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 12000,\n" +
            "        \"name\": \"顺心捷达快运\",\n" +
            "        \"code\": \"CP471906\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13484485\",\n" +
            "        \"customName\": \"顺心捷达快运\",\n" +
            "        \"expressCompanyJdId\": 832230,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1589374248000,\n" +
            "        \"modified\": 1589374248000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"SXJD\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 15000,\n" +
            "        \"name\": \"顺丰快运\",\n" +
            "        \"code\": \"SFKY\",\n" +
            "        \"consignCode\": \"SFKY\",\n" +
            "        \"customName\": \"顺丰快运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1591688272000,\n" +
            "        \"modified\": 1591688272000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 1097\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 105031,\n" +
            "        \"name\": \"百世快运\",\n" +
            "        \"code\": \"BESTQJT\",\n" +
            "        \"consignCode\": \"BESTQJT\",\n" +
            "        \"customName\": \"百世快运\",\n" +
            "        \"expressCompanyJdId\": 1748,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1475909762000,\n" +
            "        \"modified\": 1475909762000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 200143,\n" +
            "        \"name\": \"国通快递\",\n" +
            "        \"code\": \"GTO\",\n" +
            "        \"consignCode\": \"GTO\",\n" +
            "        \"customName\": \"国通快递\",\n" +
            "        \"expressCompanyJdId\": 2465,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 200427,\n" +
            "        \"name\": \"飞远配送 \",\n" +
            "        \"code\": \"GZLT\",\n" +
            "        \"consignCode\": \"GZLT\",\n" +
            "        \"customName\": \"飞远配送 \",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 200734,\n" +
            "        \"name\": \"邮政国内小包\",\n" +
            "        \"code\": \"POSTB\",\n" +
            "        \"consignCode\": \"POSTB\",\n" +
            "        \"customName\": \"邮政国内小包\",\n" +
            "        \"expressCompanyJdId\": 2170,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"ZGYZZHDD\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 200740,\n" +
            "        \"name\": \"E速宝\",\n" +
            "        \"code\": \"ESB\",\n" +
            "        \"consignCode\": \"ESB\",\n" +
            "        \"customName\": \"E速宝\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897819000,\n" +
            "        \"modified\": 1407897819000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 201174,\n" +
            "        \"name\": \"速尔\",\n" +
            "        \"code\": \"SURE\",\n" +
            "        \"consignCode\": \"SURE\",\n" +
            "        \"customName\": \"速尔快递\",\n" +
            "        \"expressCompanyJdId\": 2105,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"SE\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 202855,\n" +
            "        \"name\": \"信丰物流\",\n" +
            "        \"code\": \"XFWL\",\n" +
            "        \"consignCode\": \"XFWL\",\n" +
            "        \"customName\": \"信丰物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 202857,\n" +
            "        \"name\": \"凡宇速递\",\n" +
            "        \"code\": \"GZFY\",\n" +
            "        \"consignCode\": \"GZFY\",\n" +
            "        \"customName\": \"凡宇速递\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 204900,\n" +
            "        \"name\": \"居无忧\",\n" +
            "        \"code\": \"JWY\",\n" +
            "        \"consignCode\": \"JWY\",\n" +
            "        \"customName\": \"居无忧\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897819000,\n" +
            "        \"modified\": 1407897819000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 204902,\n" +
            "        \"name\": \"贝业新兄弟\",\n" +
            "        \"code\": \"BOYOL\",\n" +
            "        \"consignCode\": \"BOYOL\",\n" +
            "        \"customName\": \"贝业新兄弟\",\n" +
            "        \"expressCompanyJdId\": 222693,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897819000,\n" +
            "        \"modified\": 1407897819000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 204904,\n" +
            "        \"name\": \"合众阳晟\",\n" +
            "        \"code\": \"YS\",\n" +
            "        \"consignCode\": \"YS\",\n" +
            "        \"customName\": \"合众阳晟\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897819000,\n" +
            "        \"modified\": 1407897819000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 881232,\n" +
            "        \"name\": \"众邮快递\",\n" +
            "        \"code\": \"ZYKD\",\n" +
            "        \"consignCode\": \"ZYKD\",\n" +
            "        \"customName\": \"众邮快递\",\n" +
            "        \"expressCompanyJdId\": 881232,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1597973872000,\n" +
            "        \"modified\": 1597973876000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000001,\n" +
            "        \"name\": \"厂家自送\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"厂家自送\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000002,\n" +
            "        \"name\": \"嘉里大通物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"嘉里大通物流\",\n" +
            "        \"expressCompanyJdId\": 2101,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000003,\n" +
            "        \"name\": \"中国邮政挂号信\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"中国邮政挂号信\",\n" +
            "        \"expressCompanyJdId\": 2171,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000004,\n" +
            "        \"name\": \"快行线物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"快行线物流\",\n" +
            "        \"expressCompanyJdId\": 2909,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000005,\n" +
            "        \"name\": \"微特派\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"微特派\",\n" +
            "        \"expressCompanyJdId\": 4605,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000007,\n" +
            "        \"name\": \"斑马物联网\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"斑马物联网\",\n" +
            "        \"expressCompanyJdId\": 6012,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000008,\n" +
            "        \"name\": \"一智通物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"一智通物流\",\n" +
            "        \"expressCompanyJdId\": 171683,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000009,\n" +
            "        \"name\": \"易宅配物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"易宅配物流\",\n" +
            "        \"expressCompanyJdId\": 171686,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000010,\n" +
            "        \"name\": \"如风达\",\n" +
            "        \"code\": \"*********\",\n" +
            "        \"consignCode\": \"*********\",\n" +
            "        \"customName\": \"如风达\",\n" +
            "        \"expressCompanyJdId\": 313214,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000011,\n" +
            "        \"name\": \"上药物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"上药物流\",\n" +
            "        \"expressCompanyJdId\": 328977,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000012,\n" +
            "        \"name\": \"海关自提\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"海关自提\",\n" +
            "        \"expressCompanyJdId\": 332098,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 10000013,\n" +
            "        \"name\": \"京东大件开放承运商\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"京东大件开放承运商\",\n" +
            "        \"expressCompanyJdId\": 336878,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 1566886844,\n" +
            "        \"name\": \"德邦快运\",\n" +
            "        \"code\": \"CN7000001009020\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13327526\",\n" +
            "        \"customName\": \"德邦快运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1566887033000,\n" +
            "        \"modified\": 1566887033000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000007003,\n" +
            "        \"name\": \"美国速递\",\n" +
            "        \"code\": \"MGSD\",\n" +
            "        \"consignCode\": \"MGSD\",\n" +
            "        \"customName\": \"美国速递\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000038002,\n" +
            "        \"name\": \"派易国际物流77\",\n" +
            "        \"code\": \"PKGJWL\",\n" +
            "        \"consignCode\": \"PKGJWL\",\n" +
            "        \"customName\": \"派易国际物流77\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000046009,\n" +
            "        \"name\": \"RUSTON\",\n" +
            "        \"code\": \"RUSTON\",\n" +
            "        \"consignCode\": \"RUSTON\",\n" +
            "        \"customName\": \"RUSTON\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000046011,\n" +
            "        \"name\": \"ZTOSH\",\n" +
            "        \"code\": \"ZTOSH\",\n" +
            "        \"consignCode\": \"ZTOSH\",\n" +
            "        \"customName\": \"ZTOSH\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000048014,\n" +
            "        \"name\": \"ZTOGZ\",\n" +
            "        \"code\": \"ZTOGZ\",\n" +
            "        \"consignCode\": \"ZTOGZ\",\n" +
            "        \"customName\": \"ZTOGZ\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000051004,\n" +
            "        \"name\": \"燕文北京\",\n" +
            "        \"code\": \"YANWENBJ\",\n" +
            "        \"consignCode\": \"YANWENBJ\",\n" +
            "        \"customName\": \"燕文北京\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000051006,\n" +
            "        \"name\": \"燕文义乌\",\n" +
            "        \"code\": \"YANWENYW\",\n" +
            "        \"consignCode\": \"YANWENYW\",\n" +
            "        \"customName\": \"燕文义乌\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000051008,\n" +
            "        \"name\": \"燕文广州\",\n" +
            "        \"code\": \"YANWENGZ\",\n" +
            "        \"consignCode\": \"YANWENGZ\",\n" +
            "        \"customName\": \"燕文广州\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000052002,\n" +
            "        \"name\": \"燕文国际\",\n" +
            "        \"code\": \"YANWENINTE\",\n" +
            "        \"consignCode\": \"YANWENINTE\",\n" +
            "        \"customName\": \"燕文国际\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000052004,\n" +
            "        \"name\": \"燕文上海\",\n" +
            "        \"code\": \"YANWENSH\",\n" +
            "        \"consignCode\": \"YANWENSH\",\n" +
            "        \"customName\": \"燕文上海\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 21000052006,\n" +
            "        \"name\": \"燕文深圳\",\n" +
            "        \"code\": \"YANWENSZ\",\n" +
            "        \"consignCode\": \"YANWENSZ\",\n" +
            "        \"customName\": \"燕文深圳\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5000000004901,\n" +
            "        \"name\": \"安能快递\",\n" +
            "        \"code\": \"2608021499_235\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_12017865\",\n" +
            "        \"customName\": \"安能快递\",\n" +
            "        \"expressCompanyJdId\": 596494,\n" +
            "        \"source\": 1,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"ANXB\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5000000007756,\n" +
            "        \"name\": \"邮政国内标准快递\",\n" +
            "        \"code\": \"5000000007756\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_1710055\",\n" +
            "        \"customName\": \"邮政国内标准快递\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1551184883000,\n" +
            "        \"modified\": 1551184890000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5000000110730,\n" +
            "        \"name\": \"德邦快递\",\n" +
            "        \"code\": \"DBKD\",\n" +
            "        \"consignCode\": \"DBKD\",\n" +
            "        \"customName\": \"德邦快递\",\n" +
            "        \"expressCompanyJdId\": 3046,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"DBKD\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5200000110730,\n" +
            "        \"name\": \"京东快递\",\n" +
            "        \"code\": \"JD\",\n" +
            "        \"consignCode\": \"JD\",\n" +
            "        \"customName\": \"京东快递\",\n" +
            "        \"expressCompanyJdId\": 2087,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407897818000,\n" +
            "        \"modified\": 1407897818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"JD\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110730,\n" +
            "        \"name\": \"菜鸟相关物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"菜鸟相关物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407901418000,\n" +
            "        \"modified\": 1407901418000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110731,\n" +
            "        \"name\": \"临空物流\",\n" +
            "        \"code\": \"LKTO\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"临空物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1407905018000,\n" +
            "        \"modified\": 1407905018000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110732,\n" +
            "        \"name\": \"中通快运\",\n" +
            "        \"code\": \"3108002701_1011\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13222803\",\n" +
            "        \"customName\": \"中通快运\",\n" +
            "        \"expressCompanyJdId\": 680414,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1515373744000,\n" +
            "        \"modified\": 1515373744000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 670\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110733,\n" +
            "        \"name\": \"跨越速运\",\n" +
            "        \"code\": \"CN7000001003751\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13211725\",\n" +
            "        \"customName\": \"跨越速运\",\n" +
            "        \"expressCompanyJdId\": 599866,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1519732870000,\n" +
            "        \"modified\": 1519732870000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"KYE\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110734,\n" +
            "        \"name\": \"远成快运\",\n" +
            "        \"code\": \"2460304407_385\",\n" +
            "        \"consignCode\": \"2460304407_385\",\n" +
            "        \"customName\": \"远成快运\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1521112803000,\n" +
            "        \"modified\": 1521112803000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110735,\n" +
            "        \"name\": \"九曳供应链\",\n" +
            "        \"code\": \"2383545689_32\",\n" +
            "        \"consignCode\": \"2383545689_32\",\n" +
            "        \"customName\": \"九曳供应链\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1521112803000,\n" +
            "        \"modified\": 1521112803000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110736,\n" +
            "        \"name\": \"佳吉快运\",\n" +
            "        \"code\": \"JIAJI\",\n" +
            "        \"consignCode\": \"JIAJI\",\n" +
            "        \"customName\": \"佳吉快运\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1525950781000,\n" +
            "        \"modified\": 1525950781000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110737,\n" +
            "        \"name\": \"百腾物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"百腾物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1528887780000,\n" +
            "        \"modified\": 1528887780000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110738,\n" +
            "        \"name\": \"盛辉物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"盛辉物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1528887780000,\n" +
            "        \"modified\": 1528887780000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110750,\n" +
            "        \"name\": \"自提\",\n" +
            "        \"code\": \"ZT\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"自提\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1519715509000,\n" +
            "        \"modified\": 1519715509000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110760,\n" +
            "        \"name\": \"宇浩物流\",\n" +
            "        \"code\": \"OTHER\",\n" +
            "        \"consignCode\": \"OTHER\",\n" +
            "        \"customName\": \"宇浩物流\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1533726960000,\n" +
            "        \"modified\": 1533726960000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110780,\n" +
            "        \"name\": \"壹米滴答\",\n" +
            "        \"code\": \"2744832184_543\",\n" +
            "        \"consignCode\": \"2744832184_543\",\n" +
            "        \"customName\": \"壹米滴答\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1538962923000,\n" +
            "        \"modified\": 1538962923000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110790,\n" +
            "        \"name\": \"圆通承诺达\",\n" +
            "        \"code\": \"CP468398\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13469985\",\n" +
            "        \"customName\": \"圆通承诺达\",\n" +
            "        \"expressCompanyJdId\": 1274,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1519715509000,\n" +
            "        \"modified\": 1519715509000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110791,\n" +
            "        \"name\": \"丹鸟\",\n" +
            "        \"code\": \"CP570969\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_13503931\",\n" +
            "        \"customName\": \"丹鸟\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1606376631000,\n" +
            "        \"modified\": 1606376631000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110792,\n" +
            "        \"name\": \"丰网速运\",\n" +
            "        \"code\": \"FENGWANG\",\n" +
            "        \"consignCode\": \"FENGWANG\",\n" +
            "        \"customName\": \"丰网速运\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1614771818000,\n" +
            "        \"modified\": 1614771818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110793,\n" +
            "        \"name\": \"邮政EMS标准快递\",\n" +
            "        \"code\": \"\",\n" +
            "        \"consignCode\": \"\",\n" +
            "        \"customName\": \"邮政EMS标准快递\",\n" +
            "        \"expressCompanyJdId\": 3668,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1614775418000,\n" +
            "        \"modified\": 1614775418000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"EMSBZ\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110794,\n" +
            "        \"name\": \"邮政EMS\",\n" +
            "        \"code\": \"\",\n" +
            "        \"consignCode\": \"\",\n" +
            "        \"customName\": \"邮政EMS\",\n" +
            "        \"expressCompanyJdId\": 465,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1614779018000,\n" +
            "        \"modified\": 1614779018000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"EMS\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110795,\n" +
            "        \"name\": \"淘特官方物流\",\n" +
            "        \"code\": \"CN7000001003876\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_30452995\",\n" +
            "        \"customName\": \"淘特官方物流\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1625625818000,\n" +
            "        \"modified\": 1625625818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110796,\n" +
            "        \"name\": \"加运美\",\n" +
            "        \"code\": \"LE33128010\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_31375544\",\n" +
            "        \"customName\": \"加运美\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1628217818000,\n" +
            "        \"modified\": 1628217818000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    },\n" +
            "    {\n" +
            "        \"id\": 5300000110797,\n" +
            "        \"name\": \"菜鸟裹裹同城特快\",\n" +
            "        \"code\": \"LE10576340\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_30505049\",\n" +
            "        \"customName\": \"菜鸟裹裹同城特快\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1628563418000,\n" +
            "        \"modified\": 1628563418000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    }\n" +
            "    {\n" +
            "        \"id\": 357,\n" +
            "        \"name\": \"丰网速运（菜鸟新）\",\n" +
            "        \"code\": \"LE09252050\",\n" +
            "        \"consignCode\": \"DISTRIBUTOR_30464910\",\n" +
            "        \"customName\": \"丰网速运（菜鸟新）\",\n" +
            "        \"expressCompanyJdId\": 0,\n" +
            "        \"source\": 0,\n" +
            "        \"created\": 1657161038000,\n" +
            "        \"modified\": 1657161038000,\n" +
            "        \"enableStatus\": null,\n" +
            "        \"isKuaj\": 0,\n" +
            "        \"jdCpCode\": \"[null]\",\n" +
            "        \"shopeeLogisticId\": 0\n" +
            "    }\n" +
            "]";
}

package com.raycloud.dmj.services.trades;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.trade.ColumnConfigRuleDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ModuleEnum;
import com.raycloud.dmj.domain.trades.ColumnConf;
import com.raycloud.dmj.domain.trades.ColumnConfListWrapper;
import com.raycloud.dmj.domain.trades.ColumnConfRule;
import com.raycloud.dmj.domain.trades.vo.ColumnConfRuleVo;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ColumnConfRuleService implements IColumnConfRuleService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private ColumnConfigRuleDao columnConfigRuleDao;

    @Resource
    private IColumnConfService columnConfService;

    @Override
    public List<ColumnConfRule> getColumnConfigRuleList(Staff staff, Integer moduleId, Long pageId, Long id) {
        List<ColumnConfRule> columnConfigRuleList = columnConfigRuleDao.getColumnConfigRuleList(staff, id, moduleId, pageId);
        if (CollectionUtils.isEmpty(columnConfigRuleList)) {
            return Lists.newArrayList();
        }

        for (ColumnConfRule columnConfRule : columnConfigRuleList) {
            String moduleName = ModuleEnum.getNameByModuleId(columnConfRule.getModuleId());
            columnConfRule.setModule(moduleName);
        }
        // 将设置值转化为列配置详情信息
        ColumnConfListWrapper columnConfigList = columnConfService.getColumnConfigList(staff, pageId);
        List<ColumnConf> columnConfList = columnConfigList.getColumnConfList();
        convertData(staff, columnConfigRuleList, columnConfList);
        return columnConfigRuleList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveColumnConfRule(Staff staff, ColumnConfRule columnConfRule) {
        Long id = columnConfRule.getId();
        // 填充操作人信息
        columnConfRule.setFounder(staff.getName());
        if (id == null) {
            // 走新增逻辑
            // 检查列配置规则
            checkColumnConfRule(staff, columnConfRule, id);

            // 持久化保存
            columnConfigRuleDao.saveColumnConfigRule(staff, columnConfRule);
            id = columnConfRule.getId();
            logger.info(LogHelper.buildLog(staff, "新增列配置规则：" + columnConfRule.getColumnConfig()));
        } else {// 走更新逻辑
            checkColumnConfRule(staff, columnConfRule, id);

            // 修改
            columnConfigRuleDao.updateColumnConfigRule(staff, columnConfRule);
            logger.info(LogHelper.buildLog(staff, "修改列配置规则成功"));
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveColumnConfRuleList(Staff staff, List<ColumnConfRule> columnConfRuleList) {
        if (CollectionUtils.isEmpty(columnConfRuleList)) {
            return;
        }

        // 保存的模版集合中是否存在有相同模版名称
        checkColumnConfRuleListNameRepeat(columnConfRuleList);

        for (ColumnConfRule columnConfRule : columnConfRuleList) {
            String moduleName = columnConfRule.getModule();
            Integer moduleId = ModuleEnum.getIdByModuleName(moduleName);
            Assert.notNull(moduleId, "该模块功能未实现");
            columnConfRule.setModuleId(moduleId);

            columnConfRule.setFounder(staff.getName());
        }

        List<ColumnConfRule> insertColumnConfRules = columnConfRuleList.stream().filter(columnConfRule -> columnConfRule.getId() == null).collect(Collectors.toList());
        List<ColumnConfRule> updateColumnConfRules = columnConfRuleList.stream().filter(columnConfRule -> columnConfRule.getId() != null).collect(Collectors.toList());

        columnConfigRuleDao.batchInsert(staff, insertColumnConfRules);
        columnConfigRuleDao.batchUpdate(staff, updateColumnConfRules);
    }

    private void checkColumnConfRuleListNameRepeat(List<ColumnConfRule> columnConfRuleList) {
        Map<String, List<ColumnConfRule>> columnConfRuleByNameMap = columnConfRuleList.stream().collect(Collectors.groupingBy(ColumnConfRule::getColumnRuleName));
        for (String columnRuleName : columnConfRuleByNameMap.keySet()) {
            List<ColumnConfRule> columnConfRules = columnConfRuleByNameMap.get(columnRuleName);
            if (CollectionUtils.isNotEmpty(columnConfRules) && columnConfRules.size() > 1) {
                throw new IllegalArgumentException("模版名称重复，请检查后重新输入");
            }
        }
    }

    @Override
    public void deleteColumnConfRule(Staff staff, Long id) {
        columnConfigRuleDao.deleteColumnConfRuleById(staff, id);
    }

    private void convertData(Staff staff, List<ColumnConfRule> columnConfigRuleList, List<ColumnConf> columnConfList) {
        if (CollectionUtils.isEmpty(columnConfList)) {
            logger.info(LogHelper.buildLog(staff, "列配置信息不匹配"));
            return;
        }
        Map<Long, ColumnConf> columnConfMap = columnConfList.stream().collect(Collectors.toMap(ColumnConf::getColId, columnConf -> columnConf, (oldValue, newValue) -> oldValue));
        for (ColumnConfRule columnConfRule : columnConfigRuleList) {
            List<ColumnConf> columnConfs = Lists.newArrayList();
            String columnConfig = columnConfRule.getColumnConfig();
            List<ColumnConfRuleVo> ColumnConfRuleVoList = getColumnConfRuleVoList(columnConfig);
            if (CollectionUtils.isEmpty(ColumnConfRuleVoList)) {
                return;
            }
            for (ColumnConfRuleVo columnConfRuleVo : ColumnConfRuleVoList) {
                ColumnConf column = columnConfMap.get(columnConfRuleVo.getId());
                if (column == null) {
                    continue;
                }
                ColumnConf columnConf = new ColumnConf();
                columnConf.setColId(column.getColId());
                columnConf.setEnv(column.getEnv());
                columnConf.setTitle(column.getTitle());
                columnConf.setField(column.getField());
                columnConf.setVisible(columnConfRuleVo.getVisible());
                columnConf.setWidth(columnConfRuleVo.getWidth());

                // 列额外配置
                JSONObject customizeConfig = columnConfRuleVo.getCustomizeConfig();
                if (customizeConfig != null && !customizeConfig.isEmpty()) {
                    columnConf.setConfig(customizeConfig);
                }

                columnConfs.add(columnConf);
            }
            columnConfRule.setColumnConfList(columnConfs);
        }
    }

    private List<ColumnConfRuleVo> getColumnConfRuleVoList(String columnConfig) {
        if (StringUtils.isBlank(columnConfig)) {
            return Lists.newArrayList();
        }

        List<ColumnConfRuleVo> columnConfRuleVos = JSON.parseArray(columnConfig, ColumnConfRuleVo.class);
        return columnConfRuleVos;
    }

    private void checkColumnConfRule(Staff staff, ColumnConfRule columnConfRule, Long id) {
        String columnRuleName = columnConfRule.getColumnRuleName();
        String moduleName = columnConfRule.getModule();
        Integer moduleId = ModuleEnum.getIdByModuleName(moduleName);

        Assert.notNull(moduleId, "该模块功能未实现");

        columnConfRule.setModuleId(moduleId);

        List<ColumnConfRule> columnConfigRuleList = columnConfigRuleDao.getColumnConfigRuleList(staff, null, moduleId, columnConfRule.getPageId());
        if (CollectionUtils.isEmpty(columnConfigRuleList)) {
            return;
        }
        if (id == null) {
            for (ColumnConfRule confRule : columnConfigRuleList) {
                if (columnRuleName.equals(confRule.getColumnRuleName())) {
                    // 该规则和已存在规则名称重复
                    throw new IllegalArgumentException("模版名称重复，请检查后重新输入");
                }
            }
        } else {
            for (ColumnConfRule confRule : columnConfigRuleList) {
                if (!id.equals(confRule.getId()) && columnRuleName.equals(confRule.getColumnRuleName())) {
                    // 该规则和已存在规则名称重复
                    throw new IllegalArgumentException("模版名称重复，请检查后重新输入");
                }
            }
        }
    }
}

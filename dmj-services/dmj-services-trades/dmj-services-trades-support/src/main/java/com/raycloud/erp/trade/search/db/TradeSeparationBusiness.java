package com.raycloud.erp.trade.search.db;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.clear.Clear3MonthBeforeDataBusiness;
import com.raycloud.dmj.dao.trade.OrderSeparationDao;
import com.raycloud.dmj.dao.trade.TradeSeparationDao;
import com.raycloud.dmj.dao.trade.TradeStatDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2018-10-24 20:14
 */
@Service
public class TradeSeparationBusiness {

    private final static List<String> CLEAR_EVENT_LIST = new ArrayList<>();

    @Resource
    TradeSeparationDao tradeSeparationDao;
    @Resource
    OrderSeparationDao orderSeparationDao;
    @Resource
    ICompanyService companyService;
    @Resource
    IStaffService staffService;
    @Resource
    TradeStatDao tradeStatDao;
    @Resource
    IEventCenter eventCenter;

    static {
        CLEAR_EVENT_LIST.add(Clear3MonthBeforeDataBusiness.CLEAR_3MONTH_BEFORE_WMS_EVENT);
        CLEAR_EVENT_LIST.add(Clear3MonthBeforeDataBusiness.CLEAR_3MONTH_BEFORE_ITEM_EVENT);
        CLEAR_EVENT_LIST.add(Clear3MonthBeforeDataBusiness.CLEAR_3MONTH_BEFORE_TRADE_EVENT);
        CLEAR_EVENT_LIST.add(Clear3MonthBeforeDataBusiness.CLEAR_3MONTH_BEFORE_PRINT_EVENT);
        CLEAR_EVENT_LIST.add(Clear3MonthBeforeDataBusiness.CLEAR_3MONTH_BEFORE_TRADE_EXCLUDE_EVENT);
    }

    public long migrate(Staff staff) {
        long tradeTotal = tradeSeparationDao.migrate(staff);
        long orderTotal = orderSeparationDao.migrate(staff);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("迁移数据[trade:%s,order:%s]", tradeTotal, orderTotal)));
        return tradeTotal;
    }

    public void repairAll(Staff staff, Integer v) {
        clear(staff, v != null && (v - 1 == 0 || v - 2 == 0));
        if (v == null || v - 2 != 0) {
            migrate(staff);
        }
    }

    public void repair(Staff staff, Long... sids) {
        tradeSeparationDao.repair(staff, sids);
        orderSeparationDao.repair(staff, sids);
    }

    public void clear(Staff staff, boolean clearAll) {
        long start = System.currentTimeMillis();
        int a = tradeSeparationDao.clear(staff, clearAll);
        int b = orderSeparationDao.clear(staff, clearAll);
        int c = tradeStatDao.clear(staff);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("清理数据[companyId=%s, trade_not_consign=%s, order_not_consign=%s, trade_stat=%s, took=%s]", staff.getCompanyId(), a, b, c, (System.currentTimeMillis() - start))));
    }

    public void clear3MonthBefore() {
        Page page = new Page().setPageNo(1).setPageSize(500);
        Date date = new Date();
        List<Staff> staffList = new ArrayList<>();
        List<Long> list;
        long start = System.currentTimeMillis();
        while (!(list = companyService.getActiveCompanyIds(page, 7, date)).isEmpty()) {
            for (Long companyId : list) {
                try {
                    Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
                    if (ignoreCompany(staff)) {
                        staffList.add(staff);
                    }
                } catch (Exception e) {
                    Logs.error(String.format("获取公司默认员工账号报错，companyId=%s", companyId), e);
                }

            }
            if (list.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
        }
        Logs.ifDebug(String.format("共【%s】个公司需要清理数据,查询公司耗时=%s ms", staffList.size(), (System.currentTimeMillis() - start)));
        for (Map.Entry<Integer, List<Staff>> entry : staffList.stream().collect(Collectors.groupingBy(Staff::getDbKey)).entrySet()) {
            List<Staff> staffs = entry.getValue();
            Logs.ifDebug(String.format("数据库【%s】开始清理数据，共【%s】个公司需要清理", entry.getKey(), staffs.size()));
            fireEvent(staffs.get(0), staffs.stream().map(Staff::getCompanyId).collect(Collectors.toList()));
        }
    }

    private void fireEvent(Staff staff, List<Long> companyIds) {
        for (String eventName : CLEAR_EVENT_LIST) {
            eventCenter.fireEvent(this, new EventInfo(eventName).setArgs(new Object[]{staff}), companyIds);
        }
    }

    /**
     * @return true:清理 false：不清理
     */
    private boolean ignoreCompany(Staff staff) {
        Integer clear3Trade = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getClear3Trade();
        if (clear3Trade == null || clear3Trade == 1) {
            return true;
        } else {
            Logs.debug(LogHelper.buildLogHead(staff) + " 跳过数据清理");
        }
        return false;
    }

}

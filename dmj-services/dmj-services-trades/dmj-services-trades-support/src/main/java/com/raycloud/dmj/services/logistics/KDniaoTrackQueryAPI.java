package com.raycloud.dmj.services.logistics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.annotations.SerializedName;
import com.raycloud.dmj.Logs;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * KDniaoTrackQueryAPI
 * 快递鸟实时接口
 *      接口调用限制  10次/s
 * <AUTHOR>
 * @Date 2019-08-15
 * @Time 13:44
 */
public class KDniaoTrackQueryAPI {

    //电商ID
    private static String EBusinessID = "1318754";
    //电商加密私钥，快递鸟提供，注意保管，不要泄漏
    private static String AppKey = "66ec0c0b-08a8-401c-a662-2d468a0496bb";
    //请求url
    private static String ReqURL = "http://api.kdniao.com/api/dist";

    public static Result getTradeTraces(String expCode, String outSid) throws Exception {

//        String orderTracesByJson = getOrderTracesByJson(expCode, outSid);
//        try {
//            if (StringUtils.isNotEmpty(orderTracesByJson)) {
//                return JSON.parseObject(orderTracesByJson, Result.class);
//            }
//        } catch (Exception e) {
//            Logs.error("转换物流查询结果错误，msg：" + e.getMessage());
//        }

        return null;
    }


    public static void main(String[] args) {
        try {
            Result yd = getTradeTraces("YD", "4600937416927");
            List<Result> yds = new ArrayList<>();
            for (int i = 0; i < 100; i++) {
                yds.add(yd);
            }
            System.out.println(yds.stream().map(a -> a.getLogisticCode() +"_" + a.getState()).collect(Collectors.joining(",")));;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Json方式 查询订单物流轨迹
     *
     * @throws Exception
     */
    private static String getOrderTracesByJson(String expCode, String expNo) throws Exception {
        String requestData = "{'OrderCode':'','ShipperCode':'" + expCode + "','LogisticCode':'" + expNo + "'}";

        Map<String, String> params = new HashMap<String, String>();
        params.put("RequestData", urlEncoder(requestData, "UTF-8"));
        params.put("EBusinessID", EBusinessID);
        params.put("RequestType", "8001");
        String dataSign = encrypt(requestData, AppKey, "UTF-8");
        params.put("DataSign", urlEncoder(dataSign, "UTF-8"));
        params.put("DataType", "2");

        String result = sendPost(ReqURL, params);

        //根据公司业务处理返回的信息......

        return result;
    }


    /**
     * MD5加密
     *
     * @param str     内容
     * @param charset 编码方式
     * @throws Exception
     */
    @SuppressWarnings("unused")
    private static String MD5(String str, String charset) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(str.getBytes(charset));
        byte[] result = md.digest();
        StringBuffer sb = new StringBuffer(32);
        for (int i = 0; i < result.length; i++) {
            int val = result[i] & 0xff;
            if (val <= 0xf) {
                sb.append("0");
            }
            sb.append(Integer.toHexString(val));
        }
        return sb.toString().toLowerCase();
    }

    /**
     * base64编码
     *
     * @param str     内容
     * @param charset 编码方式
     * @throws UnsupportedEncodingException
     */
    private static String base64(String str, String charset) throws UnsupportedEncodingException {
        String encoded = base64Encode(str.getBytes(charset));
        return encoded;
    }

    @SuppressWarnings("unused")
    private static String urlEncoder(String str, String charset) throws UnsupportedEncodingException {
        String result = URLEncoder.encode(str, charset);
        return result;
    }

    /**
     * 电商Sign签名生成
     *
     * @param content  内容
     * @param keyValue Appkey
     * @param charset  编码方式
     * @return DataSign签名
     * @throws UnsupportedEncodingException ,Exception
     */
    @SuppressWarnings("unused")
    private static String encrypt(String content, String keyValue, String charset) throws UnsupportedEncodingException, Exception {
        if (keyValue != null) {
            return base64(MD5(content + keyValue, charset), charset);
        }
        return base64(MD5(content, charset), charset);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url    发送请求的 URL
     * @param params 请求的参数集合
     * @return 远程资源的响应结果
     */
    @SuppressWarnings("unused")
    private static String sendPost(String url, Map<String, String> params) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // POST方法
            conn.setRequestMethod("POST");
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.connect();
            // 获取URLConnection对象对应的输出流
            out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            // 发送请求参数
            if (params != null) {
                StringBuilder param = new StringBuilder();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (param.length() > 0) {
                        param.append("&");
                    }
                    param.append(entry.getKey());
                    param.append("=");
                    param.append(entry.getValue());
                    //System.out.println(entry.getKey()+":"+entry.getValue());
                }
                //System.out.println("param:"+param.toString());
                out.write(param.toString());
            }
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }


    private static char[] base64EncodeChars = new char[]{
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
            'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
            'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
            'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
            'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
            'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
            'w', 'x', 'y', 'z', '0', '1', '2', '3',
            '4', '5', '6', '7', '8', '9', '+', '/'};

    private static String base64Encode(byte[] data) {
        StringBuffer sb = new StringBuffer();
        int len = data.length;
        int i = 0;
        int b1, b2, b3;
        while (i < len) {
            b1 = data[i++] & 0xff;
            if (i == len) {
                sb.append(base64EncodeChars[b1 >>> 2]);
                sb.append(base64EncodeChars[(b1 & 0x3) << 4]);
                sb.append("==");
                break;
            }
            b2 = data[i++] & 0xff;
            if (i == len) {
                sb.append(base64EncodeChars[b1 >>> 2]);
                sb.append(base64EncodeChars[((b1 & 0x03) << 4) | ((b2 & 0xf0) >>> 4)]);
                sb.append(base64EncodeChars[(b2 & 0x0f) << 2]);
                sb.append("=");
                break;
            }
            b3 = data[i++] & 0xff;
            sb.append(base64EncodeChars[b1 >>> 2]);
            sb.append(base64EncodeChars[((b1 & 0x03) << 4) | ((b2 & 0xf0) >>> 4)]);
            sb.append(base64EncodeChars[((b2 & 0x0f) << 2) | ((b3 & 0xc0) >>> 6)]);
            sb.append(base64EncodeChars[b3 & 0x3f]);
        }
        return sb.toString();
    }

    public static class Result {


        /**
         * StateEx : 304
         * LogisticCode : 4600937416927
         * ShipperCode : YD
         * Traces : [{"Action":"1","AcceptStation":"【苏州市】  江苏主城公司常熟月美服务部周怡群分部 已揽收","AcceptTime":"2019-08-07 19:44:32","Location":"苏州市"},{"Action":"2","AcceptStation":"【苏州市】 已到达  江苏苏州分拨中心","AcceptTime":"2019-08-07 22:25:31","Location":"苏州市"},{"Action":"2","AcceptStation":"【苏州市】 已离开  江苏苏州分拨中心 发往 上海浦东分拨中心","AcceptTime":"2019-08-07 22:26:31","Location":"苏州市"},{"Action":"2","AcceptStation":"【苏州市】 已离开  江苏苏州分拨中心 发往 上海浦东分拨中心","AcceptTime":"2019-08-07 22:41:46","Location":"苏州市"},{"Action":"2","AcceptStation":"【上海市】 已到达  上海浦东分拨中心","AcceptTime":"2019-08-08 05:05:51","Location":"上海市"},{"Action":"2","AcceptStation":"【上海市】 已离开  上海浦东分拨中心 发往 上海浦东新区南汇西部公司","AcceptTime":"2019-08-08 05:43:08","Location":"上海市"},{"Action":"202","AcceptStation":"【上海市】 已到达  上海浦东新区南汇西部公司 马上为您派送","AcceptTime":"2019-08-08 07:50:42","Location":"上海市"},{"Action":"202","AcceptStation":"【上海市】  上海浦东新区南汇西部公司 派件员 张合银(***********)正在为您派送","AcceptTime":"2019-08-08 17:52:30","Location":"上海市"},{"Action":"304","AcceptStation":"【上海市】 已签收 : 由昱星688弄小区物业管理处旁丰巢智能柜FC02115723 代签收，如有问题联系张合银(***********)","AcceptTime":"2019-08-08 18:51:25","Location":"上海市"}]
         * State : 3
         * EBusinessID : 1318754
         * Success : true
         * Location : 上海市
         */

        @SerializedName("StateEx")
        private String stateEx;
        @SerializedName("LogisticCode")
        private String logisticCode;
        @SerializedName("ShipperCode")
        private String shipperCode;
        @SerializedName("State")
        private String state;
        @SerializedName("EBusinessID")
        private String eBusinessID;
        @SerializedName("Success")
        private boolean success;
        @SerializedName("Location")
        private String location;
        @SerializedName("Traces")
        private List<tracesBean> traces;

        public String getStateEx() {
            return stateEx;
        }

        public void setStateEx(String stateEx) {
            this.stateEx = stateEx;
        }

        public String getLogisticCode() {
            return logisticCode;
        }

        public void setLogisticCode(String logisticCode) {
            this.logisticCode = logisticCode;
        }

        public String getShipperCode() {
            return shipperCode;
        }

        public void setShipperCode(String shipperCode) {
            this.shipperCode = shipperCode;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getEBusinessID() {
            return eBusinessID;
        }

        public void setEBusinessID(String eBusinessID) {
            this.eBusinessID = eBusinessID;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public List<tracesBean> getTraces() {
            return traces;
        }

        public void setTraces(List<tracesBean> traces) {
            this.traces = traces;
        }

        public static class tracesBean {
            /**
             * Action : 1
             * AcceptStation : 【苏州市】  江苏主城公司常熟月美服务部周怡群分部 已揽收
             * AcceptTime : 2019-08-07 19:44:32
             * Location : 苏州市
             */

            @SerializedName("Action")
            private String action;
            @SerializedName("AcceptStation")
            private String acceptStation;
            @SerializedName("AcceptTime")
            private String acceptTime;
            @SerializedName("Location")
            private String location;

            public String getAction() {
                return action;
            }

            public void setAction(String action) {
                this.action = action;
            }

            public String getAcceptStation() {
                return acceptStation;
            }

            public void setAcceptStation(String acceptStation) {
                this.acceptStation = acceptStation;
            }

            public String getAcceptTime() {
                return acceptTime;
            }

            public void setAcceptTime(String acceptTime) {
                this.acceptTime = acceptTime;
            }

            public String getLocation() {
                return location;
            }

            public void setLocation(String location) {
                this.location = location;
            }
        }
    }

}

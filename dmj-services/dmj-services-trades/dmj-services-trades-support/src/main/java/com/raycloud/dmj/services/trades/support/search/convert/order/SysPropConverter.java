package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_SYS)
public class SysPropConverter extends AbsOrderConditionConverter {


    @Resource
    private MutiPropConverter mutiPropConverter;


    @Resource
    private PlatformPropConverter platformPropConverter;

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return true;
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        boolean added = false;
        Query sysRef = new Query();

        /**
         * 这里原来的查询逻辑很诡异 只要包含平台条件 那么对于未匹配的订单 都直接忽略掉系统商品相关的条件
         *  比如前端传入的条件为 title:A  sysProp:B 对于未匹配(o.item_sys_id < 0)订单 sysProp条件将被忽略,实际的查询逻辑等同于
         *  ( o.item_sys_id > 0  AND sys_title like '%A%' AND sys_sku_properties_name like '%B%')
         *  OR
         *  ( o.item_sys_id < 0 AND title like '%A%' )
         *
         *  相当于平台商品条件和系统商品条件 两组条件是 OR 的关系而非 AND 的关系
         *  这里继承原有逻辑,处理方式为 如果带平台商品相关的条件 则加上一个 OR item_sys_id in (0,-1 ) 跳过对未匹配商品的过滤
         *
         *  (( o.item_sys_id > 0  AND sys_title like '%A%' ) OR ( o.item_sys_id in (0,-1 ) AND title like '%A%' ))
         *  AND
         *  ((o.item_sys_id > 0 AND sys_sku_properties_name like '%B%') OR item_sys_id in (0,-1 ) )
         *
         * @see com.raycloud.dmj.services.trades.support.TradeSqlQueryBuilder#buildItemQuery(com.raycloud.dmj.domain.account.Staff, com.raycloud.dmj.domain.Query, com.raycloud.dmj.domain.trades.TradeQueryParams)
         */

        boolean containPlatform = mutiPropConverter.isNeedConvert(staff,context,condition,orderQry,hotQry)
                || platformPropConverter.isNeedConvert(staff,context,condition,orderQry,hotQry);

        added = added | andSingleCondition(sysRef,"o.short_title",condition.getShortTitle(),condition.getQueryType());
        added = added | andSingleCondition(sysRef,"o.sys_item_remark",condition.getItemRemark(),condition.getQueryType());
        added = added | andSingleCondition(sysRef,"o.sys_sku_remark",condition.getSkuRemark(),condition.getQueryType());
        added = added | andSingleCondition(sysRef,"o.sys_sku_properties_name",condition.getSkuProps(),condition.getQueryType());
        added = added | andSingleCondition(sysRef,"o.sys_sku_properties_alias",condition.getSkuPropAlias(),condition.getQueryType());
        added = added | andListCondition(sysRef,"o.sys_outer_id",condition.getSysOuterIds(),1);
        if (notNull(condition.getCid()) && condition.getCid() == -1L) {
            added = added | andSingleCondition(sysRef,"o.cids",-1L,1);
        }else {
            //这个我也不知道为啥要like 原始代码是like查询 -_-!
            added = added | andSingleCondition(sysRef,"o.cids",condition.getCid(),0);
        }

        added = added | andListCondition(sysRef,"o.sys_outer_id",condition.getSysOuterIds(),1);

        if (added || notEmpty(condition.getTypes())) {

            orderQry.append(" AND (( ");
            hotQry.append(" AND (( ");

            /**
             * 既然平台商品条件已经走另外一个 OR 了,这里查询系统商品信息的条件去不去掉 o.item_sys_id > 0 没有任何意义
             * 除非存在这样的数据: 未匹配但是神奇的包含了系统商品信息
             */
            //boolean containItemNonMatch = false;
            //containItemNonMatch = containItemNonMatch | notEmpty(condition.getNumIids());
            //containItemNonMatch = containItemNonMatch | notEmpty(condition.getSkuIds());
            //containItemNonMatch = containItemNonMatch | notEmpty(condition.getOuterIds());
            //containItemNonMatch = containItemNonMatch | notEmpty(condition.getPlatformOuterIds());
            //containItemNonMatch = containItemNonMatch | notEmpty(condition.getTypes());
            //
            //if (!containItemNonMatch) {
            //    orderQry.append(" AND o.item_sys_id > 0  ");
            //    hotQry.append(" AND o.item_sys_id > 0  ");
            //}

            orderQry.append("o.item_sys_id > 0  ");
            hotQry.append("o.item_sys_id > 0  ");

            if (added) {
                orderQry.append(sysRef.getQ().toString()).add(sysRef.getArgs());
                hotQry.append(sysRef.getQ().toString()).add(sysRef.getArgs());
            }

            if (notEmpty(condition.getTypes()) ) {
                added = true;

                List<Integer> types = new ArrayList<>();
                for (Integer type : condition.getTypes()) {
                    if (Objects.equals(com.raycloud.dmj.domain.trades.Order.TypeOfGiftOrder,type)) {
                        continue;
                    }
                    types.add(type);
                }
                boolean list = false;
                orderQry.append(" AND ").append("(");
                list =  andSingleCondition(sysRef,"o.type",types,1);
                hotQry.append(" AND ").append("(");
                andSingleCondition(sysRef,"o.type",types,1);

                if (ArrayUtils.contains(condition.getTypes(), com.raycloud.dmj.domain.trades.Order.TypeOfGiftOrder)) {
                    if (list) {
                        orderQry.append(" OR ");
                        hotQry.append(" OR ");
                    }
                    orderQry.append(" (o.gift_num > 0 and o.enable_status=1) ) ");
                    hotQry.append(" (o.is_gift = 1 and o.enable_status=1) ) ");
                }

            }

            orderQry.append(")");
            hotQry.append(")");

            if (containPlatform) {
                orderQry.append(" OR item_sys_id in (0,-1 )");
                hotQry.append(" OR item_sys_id in (0,-1 )");
            }
            orderQry.append(")");
            hotQry.append(")");
        }
        return added;
    }




}

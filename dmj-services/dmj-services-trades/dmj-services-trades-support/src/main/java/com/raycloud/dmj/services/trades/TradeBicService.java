package com.raycloud.dmj.services.trades;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.business.trade.TradeStatBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.platform.trades.WarehouseAndAgency;
import com.raycloud.dmj.domain.pt.OutSidPool;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.platform.trades.IPlatformQualityInspection;
import com.raycloud.dmj.services.pt.IOutSidPoolService;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.erp.orderpool.v2.base.api.server.IOrderPoolTradeBicServiceDubbo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 抖音BIC订单service
 * <AUTHOR>
 * @Date 2021/8/4 下午2:42
 **/
@Service
public class TradeBicService {

    private static final Logger log = LoggerFactory.getLogger(TradeBicService.class);

    private static final String FIELD = "company_id,taobao_id,tid,sid,out_sid";

    @Resource
    private TbTradeDao tbTradeDao;

    @Resource
    private ITradePtService tradePtService;

    @Resource
    private IUserService userService;

//    @Resource
//    private ITradeBicServiceDubbo tradeBicServiceDubbo;

    @Resource
    private IOrderPoolTradeBicServiceDubbo orderPoolTradeBicServiceDubbo;

    @Resource
    private TradeStatBusiness tradeStatBusiness;

    @Resource
    IOutSidPoolService outSidPoolService;

    @Resource
    IPlatformQualityInspection platformQualityInspection;
    /**
     * 获取订单码
     * @param staff
     * @param sidList
     * @param type 0：一单一码  1：混订单
     * @return
     */
    public Object getOrderCode(Staff staff, List<Long> sidList, Integer type) {
        List<TbTrade> tbTradeList = tbTradeDao.queryByKeys(staff, FIELD, "sid", sidList.toArray(new Long[]{}));
        Assert.notEmpty(tbTradeList, "根据订单号查询不到订单！");

        // 订单池V1/V2接口切换兼容
        List<OrderCode> orderCodeList = new ArrayList<>();
        List<TbTrade> filterTradeList = new ArrayList<>();
        Map<Long, List<TbTrade>> taobaoTradeMap = tbTradeList.stream().collect(Collectors.groupingBy(TbTrade::getTaobaoId));
        for (Long taobaoId : taobaoTradeMap.keySet()) {
            User user = userService.queryByTaobaoId(staff.getCompanyId(), taobaoId);
            Assert.notNull(user, "查询不到当前用户！");
            user.setStaff(null);
            List<TbTrade> tbTrades = taobaoTradeMap.get(taobaoId);
            tbTradeList = filterExistOutSidTrade(tbTrades);
            if (CollUtil.isEmpty(tbTrades)) {
                continue;
            }
            filterTradeList.addAll(tbTradeList);
            List<String> orderIds = tbTradeList.stream().map(TradeBase::getTid).collect(Collectors.toList());
            orderCodeList.addAll(getOrderCodeList(type, user, orderIds));
        }

        WlbStatus status = new WlbStatus();
        if (CollectionUtils.isEmpty(filterTradeList)) {
            return status;
        }

        Assert.notEmpty(orderCodeList, "获取BIC订单码为空！");
        Map<String, OrderCode> orderCodeMap = orderCodeList.stream().collect(Collectors.toMap(OrderCode::getTid, o -> o));

        List<Result> successResult = new ArrayList<>();
        StringBuilder errorMsg = new StringBuilder();
        List<Trade> updateTradeList = new ArrayList<>();
        for (TbTrade t : filterTradeList) {
            OrderCode orderCode = orderCodeMap.get(t.getTid());
            if (orderCode == null) {
                errorMsg.append(String.format("[%s,返回值为null]", t.getTid()));
                continue;
            }
            if (orderCode.isSuccess()){
                String code = orderCode.getOrderCode();

                TradeExt tradeExt = new TradeExt();
                tradeExt.setTid(t.getTid());
                tradeExt.setBicUniqueCode(code);
                tradeExt.setDeliveryType(orderCode.getDeliveryType());
                tradeExt.setShipType(orderCode.getShipType());

                TbTrade tbTrade = new TbTrade();
                tbTrade.setSid(t.getSid());
                tbTrade.setOutSid(orderCode.getOrderCode());
                tbTrade.setTradeExt(tradeExt);
                updateTradeList.add(tbTrade);

                Result r = new Result();
                r.setSid(t.getSid());
                r.setOutSid(code);

                successResult.add(r);
            }else{
                errorMsg.append(String.format("[%s,%s]", orderCode.getTid() ,orderCode.getMessage()));
            }
        }
        status.setSuccessResult(successResult);

        if (errorMsg.length() > 0) {
            log.info(String.format("调用BIC获取订单码接口 返回信息：%s", errorMsg.toString()));
        }
        tradePtService.saveByTrades(staff, updateTradeList);
        tbTradeDao.batchUpdate(staff, updateTradeList);
        tradeStatBusiness.update(staff, TradeUtils.toSids(updateTradeList));
        return status;
    }



    /**
     * 获取bic复检订单订单码
     * @param staff
     * @param sidList
     * @return
     */
    public Object getOrderCodeReCheck(Staff staff, List<Long> sidList, String warehouseCode, String agencyId) {
        List<TbTrade> tbTradeList = tbTradeDao.queryByKeys(staff, FIELD, "sid", sidList.toArray(new Long[]{}));
        Assert.notEmpty(tbTradeList, "根据订单号查询不到订单！");
        User user = userService.queryByTaobaoId(staff, tbTradeList.get(0).getTaobaoId());
        Assert.notNull(user, "查询不到当前用户！");
        user.setStaff(null);

        tbTradeList = filterExistOutSidTrade(tbTradeList);
        WlbStatus status = new WlbStatus();
        if (CollectionUtils.isEmpty(tbTradeList)) {
            return status;
        }

        List<String> orderIds = tbTradeList.stream().map(TradeBase::getTid).collect(Collectors.toList());

        // 订单池V1/V2接口切换兼容
        List<OrderCode> orderCodeList = getOrderCodeListReCheck(user, orderIds, warehouseCode, agencyId);

        Assert.notEmpty(orderCodeList, "获取BIC订单码为空！");
        Map<String, OrderCode> orderCodeMap = orderCodeList.stream().collect(Collectors.toMap(OrderCode::getTid, o -> o));

        List<Result> successResult = new ArrayList<>();
        List<Result> errorResult = new ArrayList<>();
        StringBuilder errorMsg = new StringBuilder();
        List<OutSidPool> outSidPools = new ArrayList<>();
        for (TbTrade t : tbTradeList) {
            OrderCode orderCode = orderCodeMap.get(t.getTid());
            if (Objects.nonNull(orderCode) && orderCode.isSuccess()){
                OutSidPool outSidPool = buildOutSidPool(t, orderCode);
                outSidPools.add(outSidPool);
                String code = orderCode.getOrderCode();
                t.setOutSid(orderCode.getOrderCode());

                TradeExt tradeExt = new TradeExt();
                tradeExt.setTid(t.getTid());
                tradeExt.setBicUniqueCode(code);
                tradeExt.setDeliveryType(orderCode.getDeliveryType());
                tradeExt.setShipType(orderCode.getShipType());
                t.setTradeExt(tradeExt);

                Result r = new Result();
                r.setSid(t.getSid());
                r.setOutSid(code);

                successResult.add(r);
            } else {
                Result r = new Result();
                r.setSid(t.getSid());
                r.setTid(t.getTid());
                r.setErrorMsg(Objects.nonNull(orderCode) ? orderCode.getMessage() : "平台返回数据为空");
                errorResult.add(r);
                errorMsg.append(String.format("[%s,%s]", t.getTid(), Objects.nonNull(orderCode) ? orderCode.getMessage() : "平台返回数据为空"));
            }
        }
        status.setSuccessResult(successResult);
        status.setErrorResult(errorResult);

        if (errorMsg.length() > 0) {
            log.info(String.format("调用BIC获取订单码接口 返回信息：%s", errorMsg));
        }
        tradePtService.saveByTrades(staff, TradeUtils.toTrades(tbTradeList));
        tbTradeDao.batchUpdate(staff, tbTradeList);
        outSidPoolService.batchSave(staff, outSidPools);
        tradeStatBusiness.update(staff, TradeUtils.toSids(tbTradeList));
        return status;
    }

    private OutSidPool buildOutSidPool(TbTrade t, OrderCode orderCode) {
        if (Objects.isNull(t) || Objects.isNull(orderCode)) {
            return null;
        }
        OutSidPool outSidPool = new OutSidPool();
        outSidPool.setSid(t.getSid());
        outSidPool.setCompanyId(t.getCompanyId());
        outSidPool.setOutSid(orderCode.getOrderCode());
        outSidPool.setTaobaoId(t.getTaobaoId());
        Map<String, Object> pmap = new TreeMap();
        if (StringUtils.isNotEmpty(orderCode.getWarehouseCode())) {
            pmap.put("warehouseCode", orderCode.getWarehouseCode());
        }
        if (StringUtils.isNotEmpty(orderCode.getAgencyId())) {
            pmap.put("agencyId", orderCode.getAgencyId());
        }
        outSidPool.setShippingOptionService(JSONObject.toJSONString(pmap));
        return outSidPool;
    }


    public Object getWarehouseAndAgencyList(Staff staff) {
        List<WarehouseAndAgency> warehouseAndAgencyList = new ArrayList<>();
        List<User> users = staff.getUsers();

        for (User user : users) {
            if (!CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())) {
                continue;
            }
            warehouseAndAgencyList = platformQualityInspection.getWarehouseAndAgencyList(user);
            if (CollectionUtils.isEmpty(warehouseAndAgencyList)) {
                continue;
            }
            return warehouseAndAgencyList;
        }
        return warehouseAndAgencyList;
    }


    private List<OrderCode> getOrderCodeListReCheck(User user, List<String> orderIds, String warehouseCode, String agencyId) {
        List<OrderCode> orderCodeList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orderIds) || StringUtils.isEmpty(warehouseCode) || StringUtils.isEmpty(agencyId)) {
            return orderCodeList;
        }
        orderCodeList = orderPoolTradeBicServiceDubbo.getOrderCodeReCheck(user, orderIds, warehouseCode, agencyId);
        return orderCodeList;
    }


    /**
     * 订单池V1/V2接口切换兼容
     *
     * @param type     类型
     * @param user     用户
     * @param orderIds 订单集合
     * @return
     */
    private List<OrderCode> getOrderCodeList(Integer type, User user, List<String> orderIds) {
        List<OrderCode> orderCodeList = Lists.newArrayList();
        //兼容rds
//        if (user.getNewSync() == 1 || user.getNewSync() == 2) {
            orderCodeList = orderPoolTradeBicServiceDubbo.getOrderCode(user, orderIds, type);
//        }
        return orderCodeList;
    }

    private List<TbTrade> filterExistOutSidTrade(List<TbTrade> tradeList) {
        Iterator<TbTrade> iterator = tradeList.iterator();
        while (iterator.hasNext()) {
            TbTrade tbTrade = iterator.next();
            if (StringUtils.isNotEmpty(tbTrade.getOutSid())) {
                iterator.remove();
            }
        }
        return tradeList;
    }

    private static class Result{
        private String outSid;

        private Long sid;

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        private String tid;

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        private String errorMsg;

        public String getOutSid() {
            return outSid;
        }

        public void setOutSid(String outSid) {
            this.outSid = outSid;
        }

        public Long getSid() {
            return sid;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.TradeWeighConfigDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeWeighConfig;
import com.raycloud.dmj.services.trades.ITradeWeighConfigService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单称重偏差配置
 * Created by shaoxianchang on 16/9/21.
 */
@Service
public class TradeWeighConfigService implements ITradeWeighConfigService {
    @Resource
    private TradeWeighConfigDao tradeWeighConfigDao;

    @Override
    @Cacheable(value="defaultCache#600", key="'trade_weight_config_' + #staff.companyId")
    public TradeWeighConfig get(Staff staff) {
        TradeWeighConfig config = tradeWeighConfigDao.get(staff);
        if (config == null) {
            config = new TradeWeighConfig(false, TradeWeighConfig.WEIGH_TYPE_WEIGHT, "0", "0");
            tradeWeighConfigDao.save(staff, config);
        }

        return config;
    }

    @Override
    public void save(Staff staff, TradeWeighConfig config) {
        tradeWeighConfigDao.save(staff, config);
    }

    @Override
    @CacheEvict(value="defaultCache#600", key="'trade_weight_config_' + #staff.companyId")
    public void update(Staff staff, TradeWeighConfig config) {
        tradeWeighConfigDao.update(staff, config);
    }
}

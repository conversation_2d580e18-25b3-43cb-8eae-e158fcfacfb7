package com.raycloud.dmj.services.sensitive;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.sensitive.system.CommonCustomizeTradeSensitiveService;
import com.raycloud.dmj.services.sensitive.system.CommonCustomizeTradeSensitiveService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.*;

import static com.raycloud.dmj.domain.utils.CommonConstants.PLAT_FORM_TYPE_SYS;

@Service
public class DistributorService {

    private final List<AbstractPlatformTradeSecurityService> platformTradeSecurityServiceList;

    private final List<AbstractCustomizeTradeSecurityService> customizeTradeSecurityServices;

    @Autowired
    public DistributorService(List<AbstractPlatformTradeSecurityService> platformTradeSecurityServiceList, List<AbstractCustomizeTradeSecurityService> customizeTradeSecurityServices) {
        this.platformTradeSecurityServiceList = platformTradeSecurityServiceList;
        this.customizeTradeSecurityServices = customizeTradeSecurityServices;
    }

    /**
     * 脱敏订单
     * 1：将订单调用平台接口进行脱敏
     * 2：将订单使用自定义脱敏方式进行脱敏{@link CommonCustomizeTradeSensitiveService}
     *
     * @param staff               用户
     * @param trades              订单列表
     * @param usePlatform         是否调用平台脱敏（若存在）
     * @param isSensitiveSysTrade 是否脱敏系统单，否的话系统单原样展示
     */
    public void doDistribute(Staff staff, List<Trade> trades, boolean usePlatform, boolean isSensitiveSysTrade) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // source为sys的手工单，不参与接下来的平台脱敏或自定义脱敏
        if (!isSensitiveSysTrade) {
            trades = trades.stream().filter(trade -> !PLAT_FORM_TYPE_SYS.equals(TradeUtils.getSourceByTrade(trade))).collect(Collectors.toList());
        }
        if (usePlatform) {
            doPlatFormDistribute(staff, trades);
        } else {
            doCustomizeDistribute(staff, trades);
        }
    }

    /**
     * 对订单进行自定义脱敏
     */
    public void doCustomizeDistribute(Staff staff, List<Trade> trades) {
        customizeTradeSecurityServices.forEach(customizeTradeSecurityService -> customizeTradeSecurityService.sensitiveTrades(staff, trades));
    }

    /**
     * 对订单进行平台接口调用解密
     */
    public void doPlatFormDistribute(Staff staff, List<Trade> trades) {
        platformTradeSecurityServiceList.forEach(platformTradeSecurityService -> platformTradeSecurityService.sensitiveTrades(staff, trades));
    }
}
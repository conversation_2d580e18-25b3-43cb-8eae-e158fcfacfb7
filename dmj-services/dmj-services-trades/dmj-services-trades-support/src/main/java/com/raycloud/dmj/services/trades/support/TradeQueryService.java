package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.HttpMethodManager;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.dms.domain.dto.DmsDistributorInfoDto;
import com.raycloud.dmj.dms.domain.dto.DmsSupplierInfoDto;
import com.raycloud.dmj.dms.request.DmsDistributorInfoRequest;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.conf.MenuInfo;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.pt.OutSidRecyclePool;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeSysConsignUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.TradeWeightUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.wave.model.OrderUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.IOutSidPoolDuubo;
import com.raycloud.dmj.services.trades.ITradeCountService;
import com.raycloud.dmj.services.trades.ITradeQueryService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.fill.ITradeFill;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.fill.TradeExceptFill;
import com.raycloud.dmj.services.trades.fill.TradeTagFill;
import com.raycloud.dmj.services.trades.filter.ITradeFilterService;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.wave.WaveUniqueCodeSerivce;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.warehouse.services.ITradeWarehouseService;
import com.raycloud.erp.trade.search.db.TradeStatCountBusiness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.QUERY_PRINT_V2;
import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED;

/**
 * Created by dev on 2016/12/13.
 */
@Service
public class TradeQueryService implements ITradeQueryService {

    private final Logger logger = Logger.getLogger(this.getClass());

    private static final Logger LOGGER = Logger.getLogger(TradeQueryService.class);
    private static final String PENDING_WORKORDERNUM_REQUEST_URL = "/as/order/index/count";
    private static final String ITEM_UNMATCH_EXCEPTION_TRADE_SID_PREFIX = "ITEM_UNMATCH_EXCEPTION_TRADE_SID";
    public static final long QUERY_DELIVER_EXCEP = 100L;
    public static final long QUERY_PARTY3_PUSH_FAIL = 101L;
    public static final long QUERY_WAREHOUSE_FAIL = 102L;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ICache cache;
    @Resource(name = "tradeSqlCountService")
    ITradeCountService tradeCountService;
    @Resource
    HttpMethodManager httpMethodManager;
    @Resource
    ITradeWarehouseService tradeWarehouseService;
    @Resource
    private ICompanyService companyService;

    @Resource
    private IStaffService staffService;

    @Resource
    private TradeStatCountBusiness tradeStatCountBusiness;

    @Resource
    WaveUniqueCodeSerivce waveUniqueCodeSerivce;

    @Resource
    private TradePddTradeBusiness tradePddTradeBusiness;

    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    IDmsTradeService dmsTradeService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    private IOutSidPoolDuubo outSidPoolDuubo;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;


    @Resource
    private ITradeFilterService tradeFilterService;
    @Resource
    private ITradeFillService tradeFillService;

    @Override
    public List<Trade> queryTradesByPresellRule(Staff staff, Long presellRuleId, boolean queryOrder, Page page) {
        TradeQueryParams params = new TradeQueryParams().setIsCancel(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT).setIsPresell(1).setPresellRuleId(presellRuleId).setQueryFlag(1).setQueryOrder(queryOrder);
        Trades trades = tradeSearchService.search(staff, params);
        return trades.getList();
    }

    private String getCountCacheKey(Staff staff) {
        return "index_backlogs_" + staff.getCompanyId() + "_" + staff.getId();
    }

    @Override
    public TradeCount cachedCount(Staff staff, TradeCountParams tradeCountParams) throws Exception {
        if (tradeLocalConfigurable == null) {
            logger.debug(LogHelper.buildLog(staff, "tradeLocalConfigurable 为空 跳过是否走缓存判断"));
            return count(tradeCountParams);
        }
        if (!tradeLocalConfigurable.isUseTradeCountCache(staff.getCompanyId())) {
            return count(tradeCountParams);
        }

        Integer sec = tradeLocalConfigurable.tradeCountCache();
        if (sec == null || sec == 0) {
            return count(tradeCountParams);
        }
        //极端情况下 根据配置直接返回0数据
        if (sec < 0) {
            logger.debug(LogHelper.buildLog(staff, "tradeLocalConfigurable 缓存配置时长为 -1 固定返回数值为0的数据"));
            return getZeroTradeCount();
        }

        String key = getCountCacheKey(staff, tradeCountParams);
        if (key == null) {
            return count(tradeCountParams);
        }
        TradeCount count = cache.get(key);
        if (count == null) {
            count = count(tradeCountParams);
            cache.set(key, count, sec);
        } else {
            logger.debug(LogHelper.buildLog(staff, "tradeLocalConfigurable 缓存配置时长为" + sec + " 返回缓存数值"));
        }
        return count;
    }

    public static final String NEW_PARAMS_STRING = JSON.toJSONString(new TradeControllerParams(), SerializerFeature.MapSortField);

    private String getCountCacheKey(Staff staff, TradeCountParams params) {
        //有自定义条件的不走缓存
        TradeControllerParams queryParams = params.getQueryParams();
        if (queryParams != null) {
            //queryParams 和纯新建的对象一样 则说明前端未传入
            String jsonString = JSON.toJSONString(queryParams, SerializerFeature.MapSortField);
            if (!Objects.equals(jsonString, NEW_PARAMS_STRING)) {
                return null;
            }
        }
        String queryIds = params.getQueryIds();
        if (StringUtils.isBlank(queryIds)) {
            return null;
        }
        return "trade_count_" + staff.getCompanyId() + "_" + staff.getId() + "_" + queryIds;
    }

    private TradeCount getZeroTradeCount() {
        TradeCount count = new TradeCount();
        count.setExcepNum(0L);
        count.setRefundNum(0L);
        count.setWaitPayNum(0L);
        count.setWaitAuditNum(0L);
        count.setWaitFinanceAudit(0L);
        count.setFinishAuditNum(0L);
        count.setWaitSetTemplateNum(0L);
        count.setWaitPrintNum(0L);
        count.setFinishPrintNum(0L);
        count.setWaitPackNum(0L);
        count.setPackSplitableNum(0L);
        count.setOpenIdentCode(0);
        count.setWaitWeighNum(0L);
        count.setWaitConsignNum(0L);
        count.setFinishConsignNum(0L);
        count.setFinishedNum(0L);
        count.setWaitOutNum(0L);
        count.setFinishOutNum(0L);
        count.setPresellNum(0L);
        count.setPendingWorkOrderNum(0L);
        count.setUploadFailNum(0L);
        count.setNum(0L);
        count.setParty3PushFailNum(0L);
        count.setUnConsignedNum(0L);
        count.setHaltNum(0L);
        count.setInsufficientNum(0L);
        count.setUnmatchedNum(0L);
        count.setRelationChangedNum(0L);
        count.setItemChangedNum(0L);
        count.setAddressChangedNum(0L);
        count.setBlackBuyerNickNum(0L);
        count.setSellerMemoChangedNum(0L);
        count.setIsLostMsgNum(0L);
        count.setPartRefundNum(0L);
        count.setItemProcessNum(0L);
        count.setSellerSendNoWeightNum(0L);
        count.setUnattainableNum(0L);
        count.setUnCheckMemoNum(0L);
        count.setUnCheckMessageNum(0L);
        count.setUnCheckTotalNum(0L);
        count.setCheckedMessageAndNoneCount(0L);
        count.setCheckedMemoAndNoneCount(0L);
        count.setDeliverExcepNum(0L);
        count.setUploadExcepNum(0L);
        count.setUploadingNum(0L);
        count.setWaitMergeNum(0L);
        count.setWaitingReturnWmsNum(0L);
        count.setAmbiguityeExcepNum(0L);
        count.setRepulseExcepNum(0L);
        count.setItemShutoffNum(0L);
        count.setItemUniqueShelfoffNum(0L);
        count.setFinanceRejectUnm(0L);
        count.setWaitDestSendGoodsNum(0L);
        count.setWaitSelfSendGoodsNum(0L);
        count.setWaitSelfAuditGoodsNum(0L);
        count.setWaitDfAuditGoodsNum(0L);
        count.setSuiteChangeNum(0L);
        count.setIsExcep(0);
        return count;
    }

    @Override
    public TradeCount count(TradeCountParams tradeCountParams) throws Exception {
        return count(tradeCountParams,false);
    }

    @Override
    public TradeCount count(TradeCountParams tradeCountParams,boolean ignoreCache) throws Exception {
        Staff staff = tradeCountParams.getStaff();
        TradeControllerParams queryParams = tradeCountParams.getQueryParams();
        String queryIds = tradeCountParams.getQueryIds();
        //HttpServletRequest request = tradeCountParams.getRequest();

        //customId为0时表示请求来自首页
        boolean isIndexReq = ((Long) 0L).equals(queryParams.getCustomId());
        if (isIndexReq) {
            //来自首页的多个维度订单数量查询
            TradeCount tc = cache.get(getCountCacheKey(staff));
            if (tc != null || ignoreCache) {
                if (tc ==null) {
                    tc = new TradeCount();
                }
                if(StringUtils.isBlank(queryIds)){
                    return countAfter(tradeCountParams,tc);
                }
                List<Long> queryIdList = Strings.getAsLongList(queryIds, ",", false);
                // 上传失败订单 不走缓存
                if (queryIdList.contains(SystemTradeQueryParamsContext.QUERY_UPLOAD_FAIL)) {
                    tradeCountService.setTradeNum(staff, SystemTradeQueryParamsContext.QUERY_UPLOAD_FAIL, tc);
                }
                return countAfter(tradeCountParams, tc);
            }
            cache.set(getCountCacheKey(staff), new TradeCount(), 600);
        }
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        params.setPage(new Page().setPageSize(1));
        TradeCount tc = new TradeCount();
        tc.setIsExcep(params.getIsExcep());
        if (queryIds != null && !(queryIds = queryIds.trim()).isEmpty()) {
            StringTokenizer tokenizer = new StringTokenizer(queryIds, ",");
            if (isIndexReq) {
                while (tokenizer.hasMoreTokens()) {
                    long queryId = Long.parseLong(tokenizer.nextToken().trim());
                    if (queryId == QUERY_DELIVER_EXCEP) {
                        tradeStatCountBusiness.countDeleverExcep(staff, tc);
                    } else if (queryId == QUERY_PARTY3_PUSH_FAIL) {
                        try {
                            tc.setParty3PushFailNum(tradeWarehouseService.queryPushFailCount(staff));

                        } catch (Exception e) {
                         LOGGER.error("请求三方库推送订单数量失败", e);
                        }
                    } else if (queryId == QUERY_WAREHOUSE_FAIL) {
                        Map<String, Long> deliveryMap = tradeWarehouseService.queryWarehouseLogFailCount(staff);
                        tc.setCancelInterceptFailNum(deliveryMap.get("totalDeliveryCancelFail"));
                        tc.setShipmentFailNum(deliveryMap.get("totalDeliveryFail"));
                        tc.setCreateShipmentOrderFailNum(deliveryMap.get("totalDeliveryCreateFail"));
                    } else {
                        tradeCountService.setTradeNum(staff, queryId, tc);
                    }
                }
                tc.setWaitPrintNum(Optional.ofNullable(tc.getWaitPrintNum()).orElse(0L) + Optional.ofNullable(tc.getWaitSetTemplateNum()).orElse(0L));
                cache.set(getCountCacheKey(staff), tc, 600);

            } else {
                while (tokenizer.hasMoreTokens()) {
                    long queryId = Long.parseLong(tokenizer.nextToken().trim());
                    tradeCountService.setTradeNum(staff, queryId, tc, params.getAllowUnPrintPack());
                }
            }
        } else {
            params.setQueryFlag(2);//只查询总数不查订单
            Trades trades = tradeSearchService.search(staff, params);
            tc.setNum(trades.getTotal());
        }

        return countAfter(tradeCountParams, tc);
    }

    private TradeCount countAfter(TradeCountParams tradeCountParams, TradeCount tc) {
        if (tradeCountParams.getIgnoreAuth() != null && tradeCountParams.getIgnoreAuth()) { //忽略权限
            return tc;
        }
        Staff staff = tradeCountParams.getStaff();
        //获取权限信息
        Staff currentStaff = staffService.get(staff.getId());
        String privilegeSetting = currentStaff.getPrivilegeSetting();
        if (currentStaff.getParentId() > 0 && StringUtils.isNotBlank(privilegeSetting)) {
            List<String> privilegeSettingList = Splitter.on(",").splitToList(privilegeSetting);
            //获取权限
            Set<String> userRolePrivilegeSettings = currentStaff.getUserRolePrivilegeSettings() == null ? Sets.newHashSet() : currentStaff.getUserRolePrivilegeSettings();
            //待审核订单
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_WAIT_AUDIT) && !privilegeSettingList.contains(MenuInfo.TRAEE_WAIT_AUDIT)) {
                tc.setWaitAuditNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_WAIT_PRINT) && !privilegeSettingList.contains(MenuInfo.TRAEE_WAIT_PRINT)) {
                tc.setWaitPrintNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_WAIT_PACK) && !privilegeSettingList.contains(MenuInfo.TRAEE_WAIT_PACK)) {
                tc.setWaitPackNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_WAIT_WEIGH) && !privilegeSettingList.contains(MenuInfo.TRAEE_WAIT_WEIGH)) {
                tc.setWaitWeighNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_WAIT_CONSIGN) && !privilegeSettingList.contains(MenuInfo.TRAEE_WAIT_CONSIGN)) {
                tc.setWaitConsignNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_EXCEP) && !privilegeSettingList.contains(MenuInfo.TRAEE_EXCEP)) {
                tc.setExcepNum(null);
                tc.setDeliverExcepNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_PENDING_WORKORDER) && !privilegeSettingList.contains(MenuInfo.TRAEE_PENDING_WORKORDER)) {
                tc.setPendingWorkOrderNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRAEE_UPLOAD_FAIL) && !privilegeSettingList.contains(MenuInfo.TRAEE_UPLOAD_FAIL)) {
                tc.setUploadFailNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.PARTY3_PUSH_FAIL_NUM) && !privilegeSettingList.contains(MenuInfo.PARTY3_PUSH_FAIL_NUM)) {
                tc.setParty3PushFailNum(null);
                tc.setCancelInterceptFailNum(null);
                tc.setShipmentFailNum(null);
                tc.setCreateShipmentOrderFailNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRADE_QUERY_IN_THREE_MONTH) && !privilegeSettingList.contains(MenuInfo.TRADE_QUERY_IN_THREE_MONTH)) {
                tc.setSellerSendNoWeightNum(null);
            }
            if (!userRolePrivilegeSettings.contains(MenuInfo.TRADE_TRACE) && !privilegeSettingList.contains(MenuInfo.TRADE_TRACE)) {
                tc.setUploadingNum(null);
            }
        }
        return tc;
    }

    @Override
    public Trades queryByMixKey(Staff staff, TradeQueryParams params) {
        Assert.isTrue(StringUtils.isNotBlank(params.getMixKey()), "请指定快订单号或系统订单号");
        params.setQueryOrder(true).setQueryFlag(1);
        boolean isPackQuery = false;
        if (params.getQueryId() != null && params.getQueryId() == SystemTradeQueryParamsContext.QUERY_WAIT_PACK && BooleanUtils.isTrue(params.getSupportPackSellerSend())) {
            isPackQuery = true;
            params.setQueryId(null);
        }
        Trades trades = tradeSearchService.search(staff, params);
        if (BooleanUtils.isTrue(params.getSupportMultiPackPrint()) && (trades == null || CollectionUtils.isEmpty(trades.getList()))) {
            Long sid = multiPacksPrintTradeLogService.querySidByOutSid(staff, params.getMixKey());
            if (sid != null) {
                params.setMixKey(sid.toString());
                trades = tradeSearchService.search(staff, params);
            }
        }
        if (params.isPddMask()) {
            tradePddTradeBusiness.pddMaskDataReplace(staff, trades.getList());
        }
        //查询待打包的订单、查询待称重的订单、查询待发货的订单支持系统订单号的查询，如果快递单号与系统订单号存在重复，可能会出现多条，优先显示快单号
        filterOutSid(trades, params.getQueryId(), params.getMixKey());
        Assert.isTrue(trades.getList() != null && !trades.getList().isEmpty(), "亲，没有查询到满足条件的订单哦");
        Trade trade = trades.getList().get(0);
        if (isPackQuery) {
            Assert.isTrue(Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()) ||
                    Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()), "亲，没有查询到满足条件的订单哦");
            Assert.isTrue(trade.getIsPackage() == 0, "亲，订单已验货了哦");
        }
        Assert.isTrue(trade.getIsCancel() == 0, "亲，订单已作废了哦");
        Assert.isTrue(!TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.HALT), "亲，订单已挂起了哦");
        Assert.isTrue(Trade.STOCK_STATUS_NORMAL.equals(trade.getStockStatus()), "亲，订单库存状态异常哦[" + trade.getStockStatus() + "]");
        Assert.isTrue(!TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.REFUNDING), "亲，订单退款处理中哦");
        Assert.isTrue(trade.getIsExcep() == 0, "亲，异常的订单哦");
        if (!(trade.isOutstock() || TradeUtils.isGxOrMixTrade(trade))) {
            User user = staff.getUserByUserId(trade.getUserId());
            Assert.isTrue(user != null, "亲，您没有订单店铺的权限哦");
            Assert.isTrue(user.getActive() - 1 == 0, "亲，订单所属店铺已停用了哦");
        }
        return trades;
    }

    @Override
    public void filterOutSid(Trades trades, Long queryId, String mixKey) {
        if (null != queryId && (queryId == QUERY_UN_CONSIGNED || queryId == QUERY_PRINT_V2)) {
            return;
        }
        if (mixKey == null || mixKey.trim().isEmpty()) {//只有待包装、待称重、待发货、待装箱清单查询时会可能有mixKey
            return;
        }
        if (trades.getList() == null || trades.getList().size() <= 1) {
            return;
        }
        List<Trade> list = trades.getList();
        //根据mixKey查询出两条记录，删除快递单号不为mixKey的订单
        for (int i = 1; i >= 0; i--) {
            Trade trade = list.get(i);
            if (!StringUtils.equals(mixKey, trade.getOutSid())) {
                list.remove(trade);
                trades.setTotal(trades.getTotal() - 1);
                return;
            }
        }
    }

    @Override
    public Trade queryByMixKey(Staff staff, String mixKey) {
        return queryByMixKey(staff, mixKey, true);
    }

    @Override
    public Trade queryByMixKey(Staff staff, String mixKey, boolean includeMaskDate) {
        return queryByMixKey(staff, mixKey, includeMaskDate, null);
    }

    /**
     * @param withOutMaskDate 是否需要脱敏数据
     */
    @Override
    public Trade queryByMixKey(Staff staff, String mixKey, boolean withOutMaskDate, Long queryId) {
        return queryByMixKey(staff,mixKey,true,true,withOutMaskDate,queryId);
    }

    @Override
    public Trade queryByMixKey(Staff staff, String mixKey, boolean needFill, boolean needDetail, boolean includeMaskDate){
        return queryByMixKey(staff,mixKey,needFill,needDetail,includeMaskDate,null);
    }

    /**
     *
     * @return
     *
     * @deprecated 这个方法 已经添加了太多业务支持 非常臃肿了 建议新的业务不要再走这个方法 老的业务尽量拆解出来
     * @see TradeQueryService#queryForWeigh(Staff, String, Integer)
     */
    @Deprecated
    protected Trade queryByMixKey(Staff staff, String mixKey, boolean needFill, boolean needDetail, boolean includeMaskDate, Long queryId) {
        mixKey = mixKey != null ? mixKey.trim() : "";
        Assert.isTrue(!mixKey.isEmpty(), "缺少单号");
        Trade target = null;
        if ("_".equals(mixKey.trim())) {
            throw  new IllegalArgumentException("非法的单号:_");
        }
        if (mixKey.contains("_") && mixKey.charAt(mixKey.length() - 2) == '_') {
            String[] mixIds = new String[2];
            mixIds[0] = mixKey.substring(0, mixKey.length() - 2);
            mixIds[1] = mixKey.substring(mixKey.length() - 1);
            int flag = Integer.parseInt(mixIds[1]);
            if (1 == flag) {//根据sid查询
                if (needFill) {
                    target = tradeSearchService.queryBySid(staff, needDetail, parseLongId(mixIds[0]));
                }else {
                    target = tradeSearchService.queryBySidWithOutFill(staff, needDetail, parseLongId(mixIds[0]));
                }
            } else if (2 == flag) {//根据shortId查询
                Long id = parseLongId(StringUtils.equals("0", mixIds[0]) ? "-1" : mixIds[0]);
                List<Trade> tradeList = null;
                if (needFill) {
                    tradeList = tradeSearchService.queryByShortId(staff, needDetail, id);
                }else {
                    tradeList = tradeSearchService.queryByShortIdWithOutFill(staff, needDetail, id);
                }
                target = tradeList != null && !tradeList.isEmpty() ? tradeList.get(0) : null;
            } else if (3 == flag) {//根据outSid查询
                List<Trade> trades = tradeSearchService.queryByOutSid(staff, mixIds[0], needDetail,needFill);
                // 从快递单号回收池再查一遍
                if (CollectionUtils.isEmpty(trades) && Objects.equals(SystemTradeQueryParamsContext.QUERY_WAIT_PACK, queryId)) {
                    List<Trade> tradesFromOutSidRecyclePool = getTradesFromOutSidRecyclePool(staff, mixIds[0]);
                    if (CollectionUtils.isNotEmpty(tradesFromOutSidRecyclePool) && tradesFromOutSidRecyclePool.get(0).getIsCancel() != null && (tradesFromOutSidRecyclePool.get(0).getIsCancel() - 1 == 0)) {
                        //支持快递单号是【已回收】状态且订单状态是【已作废】订单提示【订单已作废】，已回收的快递单号但是订单状态是【已作废】以外的状态订单提示【没找到订单】
                        //已作废的订单前端会进行状态判定。 如果是非【已作废】那么就属于”没有找到的情况”
                        trades = tradesFromOutSidRecyclePool;
                    }
                }
                // 过滤其他erp发货订单
                if (CollectionUtils.isNotEmpty(trades) && trades.size() > 1) {
                    trades = trades.stream().filter(trade -> Integer.valueOf("1").equals(trade.getSysConsigned()) || !TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())).collect(Collectors.toList());
                }
                target = trades != null ? findTarget(staff, trades) : null;
            } else if (4 == flag) {//根据波次唯一码查询
                WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesUniqueBarcode(mixIds[0]);
                Assert.notNull(multiWavesSeedKey, "编码不符合波次唯一码格式！");
                String uniqueCode = (multiWavesSeedKey == null ? mixIds[0] : multiWavesSeedKey.getUniqueCode());
                List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeSerivce.queryByUniqueCodes(staff, Collections.singletonList(uniqueCode));
                Assert.isTrue(waveUniqueCodes != null && waveUniqueCodes.size() != 0, "唯一码信息为空，请校验唯一码");
                List<Long> sids = waveUniqueCodes.stream().map(WaveUniqueCode::getSid).collect(Collectors.toList());
                if (needFill) {
                    target = tradeSearchService.queryBySid(staff, needDetail, sids.get(0));
                }else {
                    target = tradeSearchService.queryBySidWithOutFill(staff, needDetail, sids.get(0));
                }
            } else if (5 == flag) { // 根据订单唯一码查询
                OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
                params.setUniqueCodes(Collections.singletonList(mixIds[0]));
                List<WaveUniqueCode> codes = waveUniqueCodeSerivce.queryOrderUniqueCodeByCondition(staff, params);
                Assert.isTrue(codes != null && codes.size() > 0, "根据订单唯一码未查询到订单！");
                Long sid = codes.get(0).getSid();
                Assert.isTrue(sid != null && sid > 0, "该订单唯一码未关联订单！");
                target = tradeSearchService.queryBySidWithOutFill(staff, needDetail, sid);
                if (needFill) {
                    target = tradeSearchService.queryBySid(staff, needDetail, sid);
                }else {
                    target = tradeSearchService.queryBySidWithOutFill(staff, needDetail, sid);
                }
            } else if (6 == flag) {
                String penetrateCode = mixIds[0];
                if(!penetrateCode.contains("-")){
                    throw new IllegalArgumentException("穿透单号:" + penetrateCode + ",格式有误!");
                }
                List<TbTrade> trades = null;
                if (needFill) {
                    trades = tradeSearchService.queryByTids(staff, needDetail, penetrateCode);
                }else {
                    trades = tradeSearchService.queryByTidsWithOutFill(staff, needDetail, penetrateCode);
                }
                if (CollectionUtils.isEmpty(trades)) {
                    throw new IllegalArgumentException("穿透单号:" + penetrateCode + ",未查询到穿透单!");
                }
                target = trades.get(0);
                //填充分销商名称
                fillSourceName(staff, target);
            } else {
                throw new IllegalArgumentException("参数有误");
            }
        } else {
            List<Trade> trades = tradeSearchService.queryByOutSid(staff, mixKey, needDetail,needFill);
            if (trades.isEmpty()) {//超过15位的mixKey可能是sid
                if (StringUtils.isNumeric(mixKey) && mixKey.length() > 15) {
                    try {
                        if (needFill) {
                            target = tradeSearchService.queryBySid(staff, needDetail, Long.parseLong(mixKey));
                        }else {
                            target = tradeSearchService.queryBySidWithOutFill(staff, needDetail, Long.parseLong(mixKey));
                        }
                    } catch (Exception e) {
                        new QueryLogBuilder(staff).appendError("超过15位的mixKey尝试sid查询失败",e).printError(logger,e);
                        throw new IllegalArgumentException("单号错误");
                    }
                }
                // 尝试内部单号
                try {
                    if (Objects.isNull(target) && StringUtils.isNumeric(mixKey)) {
                        if (needFill) {
                            trades = tradeSearchService.queryByShortId(staff, needDetail, Long.parseLong(mixKey));
                        }else {
                            trades = tradeSearchService.queryByShortIdWithOutFill(staff, needDetail, Long.parseLong(mixKey));
                        }
                        target = CollectionUtils.isNotEmpty(trades) ? trades.get(0) : null;
                    }
                } catch (Exception e) {
                    new QueryLogBuilder(staff).appendError("尝试内部单号查询失败",e).printError(logger,e);
                    throw new IllegalArgumentException("单号错误");
                }
            } else {
                target = findTarget(staff, trades);
            }
        }

        if (includeMaskDate) {
            tradePddTradeBusiness.pddTradeMaskDataReplace(staff, target);
            if (null != target) {
                fxgTradeDecryptBusiness.batchSensitive(staff, Lists.newArrayList(target));
                commonTradeDecryptBusiness.batchSensitive(staff, Lists.newArrayList(target));
            }
        }
        return target;
    }

    @Override
    public Trade queryForWeigh(Staff staff, String mixKey, Integer kind,boolean sensitive,AbsLogBuilder logBuilder){
        if (logBuilder == null) {
            logBuilder = AbsLogBuilder.getNvlInstance();
        }
        Assert.isTrue(StringUtils.isNotBlank(mixKey), "缺少单号");
        mixKey = mixKey.trim();
        if ("_".equals(mixKey)) {
            throw  new IllegalArgumentException("非法的单号:_");
        }
        Trade target = null;

        Integer flag = null;
        String key = null;
        if (mixKey.contains("_") && mixKey.charAt(mixKey.length() - 2) == '_') {
            String[] mixIds = new String[2];
            mixIds[0] = mixKey.substring(0, mixKey.length() - 2);
            mixIds[1] = mixKey.substring(mixKey.length() - 1);
            try {
                flag = Integer.parseInt(mixIds[1]);
            } catch (NumberFormatException e) {
            }
            key = mixIds[0];
            logBuilder.recordTimer("parseFlag");
        }

        if (flag != null) {
            if (1 == flag) {//根据sid查询
                target =  tradeSearchService.queryBySidWithOutFill(staff, true, parseLongId(key));
                logBuilder.recordTimer("queryBySid");
            } else if (2 == flag) {//根据shortId查询
                List<Trade> trades = tradeSearchService.queryByShortId(staff, true, parseLongId(key));
                logBuilder.recordTimer("queryByShortId");
                target =  CollectionUtils.isNotEmpty(trades) ? trades.get(0) : null;
            } else if (3 == flag) {//根据outSid查询
                target =  quaryWeighTradeByOutSid(staff, kind, key, true,logBuilder);
            } else if (4 == flag) {//根据波次唯一码查询
                // 这个已经切到新接口 /trade/query/useWave
                throw new IllegalArgumentException("不支波次唯一码:" +flag);
            } else{
                throw new IllegalArgumentException("未知的单号类型:" +flag);
            }
        }else{
            target = quaryWeighTradeByOutSid(staff, kind, mixKey, false,logBuilder);
        }

        if (target != null) {
            try {
                tradeFilterService.filterTrade(staff, target);
                logBuilder.recordTimer("filter");
            } catch (TradeFilterException e) {
                throw new RuntimeException(e);
            }
            List<Class<? extends ITradeFill>> includes = Arrays.asList(
                    TradeExceptFill.class,
                    TradeTagFill.class
            );
            tradeFillService.fill(staff, Arrays.asList(target),includes,null);
            logBuilder.recordTimer("fill");

            if (sensitive) {
                tradePddTradeBusiness.pddTradeMaskDataReplace(staff, target);
                logBuilder.recordTimer("pddSensitive");
                fxgTradeDecryptBusiness.batchSensitive(staff, Lists.newArrayList(target));
                logBuilder.recordTimer("fxgSensitive");
                commonTradeDecryptBusiness.batchSensitive(staff, Lists.newArrayList(target));
                logBuilder.recordTimer("commonSensitive");
            }
        }

        return target;
    }

    @Nullable
    private Trade quaryWeighTradeByOutSid(Staff staff, Integer kind, String outsid, boolean strictOutSid,AbsLogBuilder logBuilder) {
        List<Trade> trades = tradeSearchService.queryByOutSid(staff, outsid, true,false);
        logBuilder.recordTimer("queryByOutSid");
        Trade target = null;
        if (trades.isEmpty()) {
            if (!strictOutSid) {
                //超过15位的mixKey可能是sid
                if (StringUtils.isNumeric(outsid) && outsid.length() > 15) {
                    try {
                        target = tradeSearchService.queryBySid(staff, true, Long.parseLong(outsid));
                        logBuilder.recordTimer("queryBySidTry");
                    } catch (Exception e) {
                        new QueryLogBuilder(staff).appendError("超过15位的mixKey尝试sid查询失败",e).printError(logger,e);
                        throw new IllegalArgumentException("单号错误");
                    }
                }
                // 尝试内部单号
                try {
                    if (Objects.isNull(target) && StringUtils.isNumeric(outsid)) {
                        trades = tradeSearchService.queryByShortIdWithOutFill(staff, true, Long.parseLong(outsid));
                        logBuilder.recordTimer("queryByShortIdTry");
                        target = CollectionUtils.isNotEmpty(trades) ? trades.get(0) : null;
                    }
                } catch (Exception e) {
                    new QueryLogBuilder(staff).appendError("尝试内部单号查询失败",e).printError(logger,e);
                    throw new IllegalArgumentException("单号错误");
                }
            }
            return target;
        }

        if (trades.size() == 1) {
            return trades.get(0);
        }

        Trade tempTrade = null;
        List<Trade> alternative = new ArrayList<>();
        for (Trade trade : trades) {
            if (trade.getEnableStatus() - 1 == 0) {
                if (TradeWeightUtils.isAfterConsignWeight(kind) && TradeUtils.isAfterSendGoods(trade) && !Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
                    alternative.add(trade);
                }else if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
                    alternative.add(trade);
                }
                if (tempTrade == null ||
                        (!staff.getUserIdMap().containsKey(tempTrade.getUserId()) && staff.getUserIdMap().containsKey(trade.getUserId())) ||
                        (Trade.SYS_STATUS_CLOSED.equals(tempTrade.getSysStatus()) && !Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()))) {
                    tempTrade = trade;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(alternative)) {
            //优先匹配系统发货的订单
            if (TradeWeightUtils.isAfterConsignWeight(kind)) {
                List<Trade> list = alternative.stream().filter(x -> !(TradeUtils.isAfterSendGoods(x) && TradeSysConsignUtils.isOtherErpConsigned(staff, x))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    return list.get(0);
                }
            }
            return alternative.get(0);
        }
        logBuilder.recordTimer("findTarget");
        return tempTrade;
    }

    private List<Trade> getTradesFromOutSidRecyclePool(Staff staff, String outSid) {
        List<Trade> trades = Lists.newArrayList();
        if (StringUtils.isEmpty(outSid)) {
            return trades;
        }
        try {
            List<OutSidRecyclePool> outSidRecyclePools = outSidPoolDuubo.queryRecyclePoolByOutSids(staff, Lists.newArrayList(outSid));
            if (CollectionUtils.isNotEmpty(outSidRecyclePools)) {
                Long sid = outSidRecyclePools.get(0).getSid();
                if (DataUtils.checkLongNotEmpty(sid)) {
                    Trade trade = tradeSearchService.queryBySid(staff, true, sid);
                    if (trade != null) {
                        trades.add(trade);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "getTradesFromOutSidRecyclePool失败"), e);
        }

        return trades;
    }

    /**
     * 穿透面单填充分销商名称
     */
    public <T extends Trade> void fillSourceName(Staff staff, Trade trade) {
        if (trade.getSourceId() == null || trade.getSourceId() < 0) {
            return;
        }
        Company company = companyService.queryById(trade.getSourceId());
        trade.setSourceName(company != null ? company.getName() : "");
        trade.setPenetrateTrade(true);
    }


    @Override
    public Trade findTarget(Staff staff, List<Trade> trades) {
        Trade target = null;
        Trade tempTrade = null;
        for (Trade trade : trades) {
            if (trade.getEnableStatus() - 1 == 0) {
                if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
                    target = trade;
                    break;
                }
                if (tempTrade == null ||
                        (!staff.getUserIdMap().containsKey(tempTrade.getUserId()) && staff.getUserIdMap().containsKey(trade.getUserId())) ||
                        (Trade.SYS_STATUS_CLOSED.equals(tempTrade.getSysStatus()) && !Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()))) {
                    tempTrade = trade;
                }
            }
        }
        if (target == null) {
            target = tempTrade;
        }
        return target;
    }


    /**
     * 填充供销商和分销商名称
     */
    @Override
    public <T extends Trade> void fillDestAndSourceName(List<T> trades) {
        List<Trade> fxTrades = trades.stream().filter(trade -> TradeUtils.isFxSource(trade) || TradeUtils.isQimenFxSource(trade) || TradeUtils.isDangKou(trade) || TradeUtils.getOrders4Trade(trade).stream().anyMatch(o -> !Objects.equals(0L, o.getDestId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fxTrades)) {
            return;
        }

        List<Trade> aliFxTrades = trades.stream().filter(TradeUtils::isAlibabaFxRoleTrade).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(aliFxTrades)){
            List<DmsSupplierInfoDto> dmsSupplierInfoDtos = fxBusiness.queryDmsSupplierInfoListByDmsDistributorId(aliFxTrades.get(0).getCompanyId());
            if(CollectionUtils.isNotEmpty(dmsSupplierInfoDtos)){
               Map<Long,DmsSupplierInfoDto> companyId2DmsSupplierInfoDtoMap = dmsSupplierInfoDtos.stream().filter(i->Objects.nonNull(i.getSupplierCompanyId())).collect(Collectors.toMap(DmsSupplierInfoDto::getSupplierCompanyId,a->a,(a, b)->b));
                aliFxTrades.forEach(t->{
                    DmsSupplierInfoDto dmsSupplierInfoDto = companyId2DmsSupplierInfoDtoMap.get(t.getDestId());
                    if(dmsSupplierInfoDto!=null){
                        t.setDestAliasName(dmsSupplierInfoDto.getAliasName());
                    }
                });
            }
        }


        Set<Long> companyIds = new HashSet<>();
        Map<Long,List<Long>> gxDestSourceMap = new HashMap<>();
        fxTrades.forEach(trade -> {
            companyIds.add(trade.getSourceId());
            companyIds.add(trade.getDestId());
            TradeUtils.getOrders4Trade(trade).stream().filter(o -> !Objects.equals(0L, o.getDestId())).forEach(o -> {
                companyIds.add(o.getDestId());
            });
            if ((TradeUtils.isGxTrade(trade) || TradeUtils.isGxAndFxTrade(trade) || TradeUtils.isQimenFxSource(trade) || TradeUtils.isAlibabaFxTrade(trade))
                    && trade.getSourceId() != null && trade.getSourceId() > 0) {
                gxDestSourceMap.computeIfAbsent(getDestId(trade), (key) -> new ArrayList<>()).add(trade.getSourceId());
            }
        });
        List<Company> companies = companyService.querComapanyListByIds(companyIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(companies)) {
            return;
        }
        Map<Long, Company> companyMap = companies.stream().collect(Collectors.toMap(Company::getId, company -> company));
        Map<String, DmsDistributorInfoDto> dmsDistributorInfoDtoMap = new HashMap<>();
        Function<Pair<Long, Long>, String> keyFunction = (key) -> key.getLeft() + "_" + key.getRight();
        if (MapUtils.isNotEmpty(gxDestSourceMap)) {
            for (Long destId : gxDestSourceMap.keySet()) {
                List<Long> sourceIds = gxDestSourceMap.get(destId);
                if (CollectionUtils.isNotEmpty(sourceIds)) {
                    DmsDistributorInfoRequest request = new DmsDistributorInfoRequest();
                    request.setSupplierCompanyId(destId);
                    request.setDistributorCompanyIdList(sourceIds);
                    List<DmsDistributorInfoDto> dmsDistributorInfoDtos = dmsTradeService.queryDmsDistributorBaseList(request);
                    if (CollectionUtils.isNotEmpty(dmsDistributorInfoDtos)) {
                        dmsDistributorInfoDtoMap.putAll(Maps.uniqueIndex(dmsDistributorInfoDtos, (dto) -> keyFunction.apply(Pair.of(destId, dto.getDistributorCompanyId()))));
                    }
                }
            }
        }
        fxTrades.forEach(trade -> {
            if (trade.getSourceId() != null && trade.getSourceId() > 0) {
                DmsDistributorInfoDto dmsDistributorInfoDto = dmsDistributorInfoDtoMap.get(keyFunction.apply(Pair.of(getDestId(trade), trade.getSourceId())));
                if (dmsDistributorInfoDto != null) {
                    trade.setSourceName(dmsDistributorInfoDto.getDistributorCompanyName());
                    trade.setSourceAliasName(dmsDistributorInfoDto.getAliasName());
                } else {
                    //查询分销商
                    Company company = companyMap.get(trade.getSourceId());
                    trade.setSourceName(company != null ? company.getName() : "");
                }
            }
            if (trade.getDestId() != null && trade.getDestId() > 0) {
                if (trade.getDestId().equals(trade.getCompanyId())) {
                    return;
                }
                Company company = companyMap.get(trade.getDestId());
                //查询供销商
                trade.setDestName(company != null ? company.getName() : "");
            }
            TradeUtils.getOrders4Trade(trade).stream().filter(o -> !Objects.equals(0L, o.getDestId())).forEach(o -> {
                Company company = companyMap.get(o.getDestId());
                o.setDestName(company != null ? company.getName() : "");
            });

        });
    }

    @Override
    public <T extends Trade> void fillSubSourceName(List<T> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        trades.forEach(trade -> trade.setSubSourceName(TradeUtils.getSubSourceName(trade)));
    }

    private long parseLongId(String key) {
        try {
            return Long.parseLong(key);
        } catch (NumberFormatException ex) {
            throw new IllegalArgumentException("单号格式不正确");
        }
    }

    Long getDestId(Trade trade) {
        if (TradeUtils.isGxAndFxTrade(trade)
                || TradeUtils.isQimenMixSource(trade)
                || TradeUtils.isAlibabaFxTrade(trade) ||
                (TradeUtils.isQimenFxSource(trade)
                        && TradeUtils.isReissueOrChangeitem(trade)
                        && trade.getDestId() != null
                        && trade.getDestId() <= 0)
                || (TradeUtils.isQimenFxSource(trade)
                && trade.getDestId() != null
                && trade.getDestId() <= 0)) {
            return trade.getCompanyId();
        }
        return trade.getDestId();
    }
}

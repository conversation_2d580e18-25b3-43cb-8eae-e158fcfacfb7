package com.raycloud.dmj.services.trades.support.utils;

import com.raycloud.dmj.*;
import com.raycloud.dmj.business.audit.AuditPoisonBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.constant.ExceptConstantOld;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trade.label.TagUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.MDC;

import java.util.*;
import java.util.stream.*;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2024/4/18 3:12 下午
 * @Description: 对应com.raycloud.dmj.services.trades.support.TradeCancelExcepService
 */
public class TradeExceptBusinessUtils {

    public static Map<String, ExceptEnum> cancelExceptViewMap = new HashMap<>();
    /**
     * 不允许直接取消地址异常的平台
     */
    private static final Set<String> NOT_ALLOW_CANCEL_ADDRESS_EXCEPT_SOURCE_SET = Stream.of(CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU).collect(Collectors.toSet());


    private static final List<ExceptEnum> orderExceptEnums = Arrays.asList(
            ExceptEnum.ITEM_SHUTOFF,
            ExceptEnum.RELATION_CHANGED,
            ExceptEnum.SUITE_CHANGE,
            ExceptEnum.UNIQUE_CODE_OFFSHELF,
            ExceptEnum.ITEM_CHANGED,
            ExceptEnum.ITEM_PROCESS,
            ExceptEnum.GX_ITEM_CHANGE_EXCEPT,
            ExceptEnum.REFUND_ITEM_NUM_EXCEPT
    );

    static {
        cancelExceptViewMap.put(TradeQueryParams.STATUS_EXCEP_HALT, ExceptEnum.HALT);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_CHANGE_ADDRESS, ExceptEnum.ADDRESS_CHANGED);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_UPDATED_SELLERMEMO, ExceptEnum.SELLER_MEMO_UPDATE);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_BLACK, ExceptEnum.BLACK_NICK);
        cancelExceptViewMap.put(TradeExceptionUtils.EX_ITEM_SHUTOFF, ExceptEnum.ITEM_SHUTOFF);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_CHANGE_ITEM, ExceptEnum.ITEM_CHANGED);
        cancelExceptViewMap.put(TradeExceptionUtils.EX_UNIQUE_CODE_OFFSHELF, ExceptEnum.UNIQUE_CODE_OFFSHELF);
        cancelExceptViewMap.put(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED, ExceptEnum.RELATION_CHANGED);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_SUITE_CHANGE, ExceptEnum.SUITE_CHANGE);
        cancelExceptViewMap.put(TradeConstants.STATUS_EXCEP_UNATTAINABLE, ExceptEnum.UNATTAINABLE);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_PART_REFUND, ExceptEnum.PART_REFUND);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_DELIVER, ExceptEnum.DELIVER_EXCEPT);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_UPLOAD_DELIVER, ExceptEnum.UPLOAD_EXCEPT);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_COD_REPEAT, ExceptEnum.COD_REPEAT);
        cancelExceptViewMap.put(TradeExceptionStatusUtils.EX_WAIT_MERGE, ExceptEnum.WAIT_MERGE);
        cancelExceptViewMap.put(TradeConstants.STATUS_PLATFORM_WAREHOUSE_MAPPING_EXCEPTION, ExceptEnum.PLATFORM_WAREHOUSE_MATCH);
        //cancelExceptViewMap.put(TradeExceptionUtils.EX_ITEM_PROCESS_EXCEP, ExceptEnum.ITEM_PROCESS);
        cancelExceptViewMap.put(TradeConstants.EX_WAITING_RETURN_WMS, ExceptEnum.WAITING_RETURN_WMS);
        cancelExceptViewMap.put(ExceptConstantOld.EX_OUTSID_RECOVERY_FAIL, ExceptEnum.OUTSID_RECOVERY_FAIL);
        cancelExceptViewMap.put(ExceptEnum.FX_REPULSE.getOldExceptEnum().getEnglish(), ExceptEnum.FX_REPULSE);
        cancelExceptViewMap.put(ExceptEnum.PART_PAY_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.PART_PAY_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.REFUND_ITEM_NUM_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.REFUND_ITEM_NUM_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.GX_ITEM_CHANGE_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.CAI_GOU_TRADE_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.CAI_GOU_TRADE_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.PO_JIA_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.PO_JIA_EXCEPT);
        cancelExceptViewMap.put(ExceptEnum.SMALL_REFUND_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.SMALL_REFUND_EXCEPT);
       /* cancelExceptViewMap.put(ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT);*/
        cancelExceptViewMap.put(ExceptEnum.ONLINE_STATUS_EXCEPT.getOldExceptEnum().getEnglish(), ExceptEnum.ONLINE_STATUS_EXCEPT);
    }

    public static void checkAllExcept(Staff staff, Trade originTrade, ExcepData excepData, AuditPoisonBusiness auditPoisonBusiness){
        Collection<ExceptEnum> exceptEnums = cancelExceptViewMap.values();
        for(ExceptEnum exceptEnum:exceptEnums){
            checkExcept(staff, originTrade, excepData, exceptEnum, auditPoisonBusiness);
        }

    }

    public static void checkSysExcept(Staff staff, Trade originTrade, ExcepData excepData, List<String> sysExceptExs,AuditPoisonBusiness auditPoisonBusiness){
        for(String sysExceptEx:sysExceptExs){
            ExceptEnum exceptEnum = cancelExceptViewMap.get(sysExceptEx);
            if (exceptEnum != null) {
                checkExcept(staff, originTrade, excepData, exceptEnum, auditPoisonBusiness);
            } else {
                Logs.debug(LogHelper.buildLog(staff, String.format("%s 异常不存在实现!", sysExceptEx)));
            }
        }

    }

    /**
     *是否需要操作order的异常
     */
    public static boolean exceptNeedOrder(Staff staff, List<String> systems, List<Long> customs) {
        if (systems == null) {
            return true;
        }
        for (String sysExceptEx : systems) {
            ExceptEnum exceptEnum = cancelExceptViewMap.get(sysExceptEx);
            if (exceptEnum != null && orderExceptEnums.contains(exceptEnum)) {
                return true;
            }
        }
        return false;
    }



    private static void checkExcept(Staff staff, Trade originTrade, ExcepData excepData, ExceptEnum exceptEnum, AuditPoisonBusiness auditPoisonBusiness) {
        switch (exceptEnum) {
            case HALT:
            case DELIVER_EXCEPT:
            case UPLOAD_EXCEPT:
            case COD_REPEAT:
            case WAITING_RETURN_WMS:
            case OUTSID_RECOVERY_FAIL:
            case WAIT_MERGE:
            case BLACK_NICK:
            case CAI_GOU_TRADE_EXCEPT:
            case PO_JIA_EXCEPT:
                cancelTradeExcept(staff, originTrade, excepData, exceptEnum);
                break;
            case ADDRESS_CHANGED:
                checkAddressChange(staff, originTrade, excepData);
                break;
            case SELLER_MEMO_UPDATE:
                checkSellerMemoUpdate(staff, originTrade, excepData);
                break;
            case ITEM_SHUTOFF:
            case RELATION_CHANGED:
            case SUITE_CHANGE:
            case UNIQUE_CODE_OFFSHELF:
                cancelTradeAndOrder(staff, originTrade, excepData, exceptEnum);
                break;
            case ITEM_CHANGED:
                cancelItemChange(staff, excepData, originTrade);
                break;
            case UNATTAINABLE:
                cancelUnattainableExcept(staff, originTrade, excepData);
                break;
            case FX_REPULSE:
                cancelFxExecp(staff, originTrade, excepData);
                break;
            case PART_REFUND:
                checkExceptPartRefund(staff, originTrade, excepData);
                break;
            case PLATFORM_WAREHOUSE_MATCH:
                cancelPlatformWarehouseMappingException(staff, originTrade, excepData, auditPoisonBusiness);
                break;
          /*  case ITEM_PROCESS:
                cancelItemProcess(staff, originTrade, excepData);
                break;*/
            case REFUND_ITEM_NUM_EXCEPT:
                cancelRefundItemNumExcept(staff, originTrade, excepData);
                break;
            case PART_PAY_EXCEPT:
                cancelPartPayExcept(staff, originTrade, excepData);
                break;
            case GX_ITEM_CHANGE_EXCEPT:
                cancelGxItemChangeExcept(staff, originTrade, excepData, exceptEnum);
                break;
            case PLAT_MODIFY_ITEM_NUM_EXCEPT:
                cancelTradeAndOrderAddOrderModifyLog(staff, originTrade, exceptEnum,OrderModifyLogTypeEnum.CANCEL_PLAT_ITEM_NUM_EXCEPT,excepData);
                break;
            case ONLINE_STATUS_EXCEPT:
                cancelTradeAndOrderAddOrderModifyLog(staff, originTrade, exceptEnum,OrderModifyLogTypeEnum.ONLINE_STATUS_EXCEPT_CANCEL,excepData);
                break;
           case SMALL_REFUND_EXCEPT:
               cancelTradeAndOrderAddOrderModifyLog(staff, originTrade, exceptEnum,OrderModifyLogTypeEnum.CANCEL_SMALL_REFUND_EXCEPT,excepData);
               break;
            default:
                Logs.debug(LogHelper.buildLog(staff, String.format("%s 未实现！", exceptEnum.getChinese())));
                break;
        }


    }


    /**
     * 平台仓映射异常
     */
    private static void cancelPlatformWarehouseMappingException(Staff staff, Trade originTrade, ExcepData excepData, AuditPoisonBusiness auditPoisonBusiness) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.PLATFORM_WAREHOUSE_MATCH)) {
            String exceptionName = "天猫物流升级平台仓映射异常";
            if (CommonConstants.PLAT_FORM_TYPE_POISON.equals(originTrade.getSource())) {
                exceptionName = "平台仓未匹配异常";
                String platformWarehouseId = auditPoisonBusiness.getPlatFormWarehouseIdFromMapping(staff, originTrade);
                if (StringUtils.isNotBlank(platformWarehouseId)) {
                    boolean flag = auditPoisonBusiness.feedBackPlatformWarehouse(staff, originTrade, platformWarehouseId);
                    if (!flag) {
                        return;
                    }
                } else {
                    return;
                }
            }
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.PLATFORM_WAREHOUSE_MATCH, 0L);
            TradeExceptUtils.updateExcept(staff, excepData.getUpdateTrade(originTrade), ExceptEnum.PLATFORM_WAREHOUSE_MATCH, 0L);
            processTrace(originTrade, excepData, exceptionName);
        }
    }

    /**
     * 单号回收失败
     *
     * @param staff
     * @param originTrade
     * @param excepData
     */
    private static void cancalOutsidRecoveryFail(Staff staff, Trade originTrade, ExcepData excepData) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.OUTSID_RECOVERY_FAIL)) {
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.OUTSID_RECOVERY_FAIL, 0L);
            TradeExceptUtils.updateExcept(staff, excepData.getUpdateTrade(originTrade), ExceptEnum.OUTSID_RECOVERY_FAIL, 0L);
            processTrace(originTrade, excepData, ExceptEnum.OUTSID_RECOVERY_FAIL.getChinese());
        }
    }


    /**
     * 普通商品转加工商品异常
     *
     * @param staff
     * @param originTrade
     * @param excepData
     */
    private static void cancelItemProcess(Staff staff, Trade originTrade, ExcepData excepData) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.ITEM_PROCESS)) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
            for (Order order : orders4Trade) {
                if (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ITEM_PROCESS)) {
                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.ITEM_PROCESS, 0L);
                    OrderExceptUtils.updateExceptOrder(staff, excepData.getUpdateOrder(order), ExceptEnum.ITEM_PROCESS, 0L);
                }
                if (order.getSuits() != null) {
                    for (Order suit : order.getSuits()) {
                        if (OrderExceptUtils.isContainsExcept(staff, suit, ExceptEnum.ITEM_PROCESS)) {
                            OrderExceptUtils.updateExceptOrder(staff, suit, ExceptEnum.ITEM_PROCESS, 0L);
                            OrderExceptUtils.updateExceptOrder(staff, excepData.getUpdateOrder(suit), ExceptEnum.ITEM_PROCESS, 0L);
                        }
                    }
                }
            }
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.ITEM_PROCESS, 0L);
            TradeExceptUtils.updateExcept(staff, excepData.getUpdateTrade(originTrade), ExceptEnum.ITEM_PROCESS, 0L);
            processTrace(originTrade, excepData, "商品类型转换异常");
        }
    }


    /**
     * 取消退款商品数量异常
     *
     * @param staff
     * @param originTrade
     * @param excepData
     */
    private static void cancelRefundItemNumExcept(Staff staff, Trade originTrade, ExcepData excepData) {
        boolean containExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.REFUND_ITEM_NUM_EXCEPT);
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
        List<Order> orders = OrderUtils.toFullOrderList(orders4Trade, false);
        for (Order originOrder : orders) {
            if (OrderExceptUtils.isContainsExcept(staff, originOrder, ExceptEnum.REFUND_ITEM_NUM_EXCEPT)) {
                OrderExceptUtils.updateExceptOrder(staff, originOrder, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
                Order updateOrder = excepData.getUpdateOrder(originOrder);
                // 兼容一下旧的
                updateOrder.setV(originOrder.getV());
                OrderExceptUtils.updateExceptOrder(staff, updateOrder, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
                OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(originOrder, OrderModifyLogTypeEnum.REFUND_ITEM_NUM_EXCEPT);
                excepData.orderModifyLogs.add(orderModifyLog);
            }
            List<Order> suits = originOrder.getSuits();
            if (CollectionUtils.isEmpty(suits)) {
                continue;
            }
            for (Order suit : suits) {
                if (OrderExceptUtils.isContainsExcept(staff, suit, ExceptEnum.REFUND_ITEM_NUM_EXCEPT)) {
                    OrderExceptUtils.updateExceptOrder(staff, suit, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
                    Order updateOrder = excepData.getUpdateOrder(suit);
                    OrderExceptUtils.updateExceptOrder(staff, updateOrder, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
                }
            }
        }
        if (containExcept) {
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            updateTrade.setExcep(originTrade.getExcep());
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.REFUND_ITEM_NUM_EXCEPT, 0L);
            processTrace(originTrade, excepData, "商品数量异常");
        }

    }

    /**
     * 取消部分付款异常
     *
     * @param staff
     * @param originTrade
     * @param excepData
     */
    public static void cancelPartPayExcept(Staff staff, Trade originTrade, ExcepData excepData) {
        boolean containExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.PART_PAY_EXCEPT);
        if (containExcept) {
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.PART_PAY_EXCEPT, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.PART_PAY_EXCEPT, 0L);
            processTrace(originTrade, excepData, ExceptEnum.PART_PAY_EXCEPT.getChinese());
        }

    }


    /**
     * 平台换商品
     */
    private static void cancelItemChange(Staff staff, ExcepData excepData, Trade originTrade) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.ITEM_CHANGED)) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
            for (Order originOrder : orders4Trade) {
                if (OrderExceptUtils.isContainsExcept(staff, originOrder, ExceptEnum.ITEM_CHANGED)) {
                    OrderExceptUtils.updateExceptOrder(staff, originOrder, ExceptEnum.ITEM_CHANGED, 0L);
                    Order updateOrder = excepData.getUpdateOrder(originOrder);
                    // 兼容一下旧的数据，取消过平台换商品异常
                    originOrder.addV(1);
                    updateOrder.setV(originOrder.getV());

                    OrderExceptUtils.updateExceptOrder(staff, updateOrder, ExceptEnum.ITEM_CHANGED, 0L);
                    OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(originOrder, OrderModifyLogTypeEnum.CANCELED_ITEM_CHANGE_EXCEPT);
                    excepData.orderModifyLogs.add(orderModifyLog);
                }
                if (CollectionUtils.isNotEmpty(originOrder.getSuits())) {
                    for (Order suit : originOrder.getSuits()) {
                        if (OrderExceptUtils.isContainsExcept(staff, suit, ExceptEnum.ITEM_CHANGED)) {
                            OrderExceptUtils.updateExceptOrder(staff, suit, ExceptEnum.ITEM_CHANGED, 0L);
                            Order updateSuit = excepData.getUpdateOrder(suit);
                            OrderExceptUtils.updateExceptOrder(staff, updateSuit, ExceptEnum.ITEM_CHANGED, 0L);
                        }
                    }
                }

            }
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.ITEM_CHANGED, 0L);
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            // 兼容旧的数据,itemExcep 值还没有透传过来
            updateTrade.setItemExcep(originTrade.getItemExcep());
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.ITEM_CHANGED, 0L);

            processTrace(originTrade, excepData, ExceptEnum.ITEM_CHANGED.getChinese());
        }

    }


    /**
     * 是否有订单同步的时候地址变更产生的异常
     * https://gykj.yuque.com/entavv/xb9xi5/ztv3gp6yax2bdfbf
     */
    private static void checkAddressChange(Staff staff, Trade originTrade, ExcepData excepData) {
        if (NOT_ALLOW_CANCEL_ADDRESS_EXCEPT_SOURCE_SET.contains(originTrade.getSource())) {
            Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s tid=%s source=%s 不支持取消地址异常!", originTrade.getSid(), originTrade.getTid(), originTrade.getSource())));
            return;
        }
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.ADDRESS_CHANGED)) {
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.ADDRESS_CHANGED, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.ADDRESS_CHANGED, 0L);
            processTrace(originTrade, excepData, "平台换地址异常");
            //已审核的订单或者待审核的地址已处理的拆单，标记该订单已取消地址异常
            if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(originTrade.getSysStatus()) || (TradeUtils.isSplit(originTrade) && originTrade.isAddressHandled())) {
                originTrade.addV(1);
                updateTrade.setV(originTrade.getV());
            }
        }
    }

    private static void processTrace(Trade originTrade, ExcepData excepData, String exceptionName) {
        if (StringUtils.isBlank(exceptionName)) {
            return;
        }
        String exceptionHeaderName = excepData.cacelAllException ? "取消全部异常：" : "取消异常：";
        if(Objects.equals(1, MDC.get(TradeConstants.GX_TRADE_CANCEL_EXCEPT))){
            exceptionHeaderName = "供销"+exceptionHeaderName;
        }
        if(originTrade.hasOpV(OpVEnum.TRADE_FX_EXCEPT_SYNC)){
            exceptionHeaderName = (TradeUtils.isFxOrMixTrade(originTrade)?"供销商":"分销商") +exceptionHeaderName;
        }
        String content = excepData.getTradeTrace(originTrade).getOperations().get(OpEnum.EXCEPTION_CANCEL);
        excepData.getTradeTrace(originTrade).getOperations().put(OpEnum.EXCEPTION_CANCEL, StringUtils.isBlank(content) ? (exceptionHeaderName + exceptionName) : (content + "、" + exceptionName));
    }

    /**
     * 是否有订单同步的时候卖家备注产生的异常
     */
    private static void checkSellerMemoUpdate(Staff staff, Trade originTrade, ExcepData excepData) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.SELLER_MEMO_UPDATE)) {
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.SELLER_MEMO_UPDATE, 0L);
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.SELLER_MEMO_UPDATE, 0L);
            processTrace(originTrade, excepData, "平台改备注异常");
        }
    }


    /**
     * 取消分销异常
     */
    private static void cancelFxExecp(Staff staff, Trade originTrade, ExcepData excepData) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_REPULSE)) {
            //这个异常有异常说明 也需要清空
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.FX_REPULSE, 0L);
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.FX_REPULSE, 0L);
            originTrade.setExceptMemo("");
            updateTrade.setExceptMemo(originTrade.getExceptMemo());
            processTrace(originTrade, excepData, "分销异常");
        }
    }


    private static void checkExceptPartRefund(Staff staff, Trade trade, ExcepData data) {
        //检查是否有部分退款的异常
        if (!TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_REFUND)) {
            return;
        }
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_REFUND, 0L);
        TradeExceptUtils.updateExcept(staff, data.getUpdateTrade(trade), ExceptEnum.PART_REFUND, 0L);
        processTrace(trade, data, "部分退款异常");
        data.getUpdateTrade(trade).setItemExcep(TradeUtils.getItemExcep(staff, trade, null));
        if(TradeUtils.isGxTrade(trade)){
            trade.addOpV(OpVEnum.TRADE_CANCEL_PART_REFUND_EXCEPT);
            data.gxTrades.add(trade);
        }
    }

    private static void cancelUnattainableExcept(Staff staff, Trade originTrade, ExcepData excepData) {
        if (!TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.UNATTAINABLE)) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(originTrade);
        for (Order order : orders) {
            order.setAutoUnattainable(0);
            if (order.getSuits() != null) {
                order.getSuits().forEach(suit -> suit.setAutoUnattainable(0));
            }
        }
        TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.UNATTAINABLE, 0L);
        Trade updateTrade = excepData.getUpdateTrade(originTrade);
        TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.UNATTAINABLE, 0L);
        excepData.stockRecordNeedChangeSids.add(originTrade.getSid());
        processTrace(originTrade, excepData, "取消快递停发异常");
    }


    public static void cancelCustomExcept(Staff staff, Trade trade, ExcepData data, List<Long> customs, boolean clearALl, Map<Long,String> exceptNameMap) {
        Logs.debug(LogHelper.buildLog(staff, String.format("3 sid:%s clearALl:%s 取消异常:%s ", trade.getSid(), clearALl, customs)));
        if (!clearALl && CollectionUtils.isEmpty(customs)) {
            // 非清空所有异常，未指定自定义异常
            return;
        }
        Set<Long> needCancel = TradeExceptUtils.getCustomExceptIds(staff, trade);
        if (CollectionUtils.isEmpty(needCancel)) {
            return;
        }
        Trade updateTrade = data.getUpdateTrade(trade);
        if (CollectionUtils.isEmpty(customs)) {
            customs = new ArrayList<>(needCancel);
        }
        Set<Long> cancelLog = new HashSet<>();
        for (Long exceptId : customs) {
            ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
            // 三方仓的需要特殊处理一下,三方仓传过来的是旧的exceptId，后期needCancel 可能存的是新的异常id
            if (needCancel.contains(exceptId) || (exceptEnum != null && needCancel.contains(exceptEnum.getId()))) {
                cancelLog.add(exceptId);
            }
            TradeExceptUtils.updateExcept(staff, trade, exceptId, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, exceptId, 0L);
            // 分销暂停发货异常
            if ((ExceptEnum.FX_STOP_DELIVER_EXCEPT.getId() - exceptId == 0
                    || (exceptEnum!=null&&ExceptEnum.FX_STOP_DELIVER_EXCEPT.getId() - exceptEnum.getId() == 0)) && TradeUtils.isFxSource(trade)) {
                data.fxStopDeliverSids.add(trade.getSid());
            }
        }
        // 取消后的自定义异常
        Set<Long> customExceptIds = TradeExceptUtils.getCustomExceptIds(staff, trade);
        if (CollectionUtils.isEmpty(customExceptIds)) {
            trade.setExceptMemo("");
            updateTrade.setExceptMemo("");
        }
        trade.setManualMarkIds(TagUtils.replaceMarkData(trade.getManualMarkIds(), CollectionUtils.isEmpty(customExceptIds) ? null : customExceptIds, 2));
        processTrace(trade, data, getTagNames(staff, cancelLog, exceptNameMap));
    }

    private static String getTagNames(Staff staff, Set<Long> ids, Map<Long,String> exceptNameMap) {
        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }
        Set<String> tagNames=new HashSet<>();
        for (Long id : ids) {
            String exceptName = exceptNameMap.get(id);
            if (StringUtils.isBlank(exceptName)) {
                // 可能是三方仓的异常
                ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(id);
                exceptName = exceptEnum != null ? exceptEnum.getChinese() : "";
            }
            if (StringUtils.isNotBlank(exceptName)) {
                tagNames.add(exceptName);
            }
        }
        if (CollectionUtils.isNotEmpty(tagNames)) {
            return  "自定义异常（" + Strings.join(",", tagNames) + "）";
        }
        return "";
    }

    /**
     * 取消退货入仓异常
     */
    public static void cancelWaitingReturnWmsExecp(Staff staff, Trade originTrade, ExcepData excepData) {
        if (!TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.WAITING_RETURN_WMS)) {
            return;
        }
        TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.WAITING_RETURN_WMS, 0L);
        TradeExceptUtils.updateExcept(staff, excepData.getUpdateTrade(originTrade), ExceptEnum.WAITING_RETURN_WMS, 0L);
        processTrace(originTrade, excepData, ExceptEnum.WAITING_RETURN_WMS.getChinese());
    }


    private static void cancelTradeExcept(Staff staff, Trade originTrade, ExcepData excepData, ExceptEnum exceptEnum) {
        if (!TradeExceptUtils.isContainExcept(staff, originTrade, exceptEnum)) {
            return;
        }
        TradeExceptUtils.updateExcept(staff, originTrade, exceptEnum, 0L);
        Trade updateTrade = excepData.getUpdateTrade(originTrade);
        TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 0L);
        processTrace(originTrade, excepData, exceptEnum.getChinese());
    }

    private static void cancelTradeAndOrder(Staff staff, Trade originTrade, ExcepData excepData, ExceptEnum exceptEnum) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, exceptEnum)) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
            for (Order originOrder : orders4Trade) {
                if (OrderExceptUtils.isContainsExcept(staff, originOrder, exceptEnum)) {
                    OrderExceptUtils.updateExceptOrder(staff, originOrder, exceptEnum, 0L);
                    Order updateOrder = excepData.getUpdateOrder(originOrder);
                    OrderExceptUtils.updateExceptOrder(staff, updateOrder, exceptEnum, 0L);
                }
                if (CollectionUtils.isNotEmpty(originOrder.getSuits())) {
                    for (Order suit : originOrder.getSuits()) {
                        if (OrderExceptUtils.isContainsExcept(staff, suit, exceptEnum)) {
                            OrderExceptUtils.updateExceptOrder(staff, suit, exceptEnum, 0L);
                            Order updateSuit = excepData.getUpdateOrder(suit);
                            OrderExceptUtils.updateExceptOrder(staff, updateSuit, exceptEnum, 0L);
                        }
                    }
                }
            }
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, originTrade, exceptEnum, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 0L);
            processTrace(originTrade, excepData, exceptEnum.getChinese());
        }
    }

    /**
     * 取消trade和order的异常并增加操作日志
     */
    private static void cancelTradeAndOrderAddOrderModifyLog(Staff staff, Trade originTrade,ExceptEnum exceptEnum,OrderModifyLogTypeEnum orderModifyLogTypeEnum, ExcepData excepData) {
        // 取消order异常
        List<Order> orders = TradeUtils.getOrders4Trade(originTrade);
        boolean haseExcept=false;
        for (Order originOrder : orders) {
            if (!haseExcept) {
                haseExcept = OrderExceptUtils.isContainsExcept(staff, originOrder, exceptEnum);
            }
            cancelOrder(staff, originOrder, exceptEnum, orderModifyLogTypeEnum, excepData);
        }
        if (!haseExcept) {
            return;
        }
        if (ExceptEnum.SMALL_REFUND_EXCEPT == exceptEnum && Objects.equals(excepData.getIsCancelSmallRefund(), 1)) {
            // 取消小额退款异常需要作废订单
            excepData.needCanceledSids.add(originTrade.getSid());
        }
        // 取消trade异常
        TradeExceptUtils.updateExcept(staff, originTrade, exceptEnum, 0L);
        Trade updateTrade = excepData.getUpdateTrade(originTrade);
        TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 0L);
        processTrace(originTrade, excepData, exceptEnum.getChinese());
    }

    /**
     * 取消order
     * @param staff
     * @param originOrder
     * @param exceptEnum
     * @param orderModifyLogTypeEnum
     * @param excepData
     */
    private static void cancelOrder(Staff staff, Order originOrder, ExceptEnum exceptEnum, OrderModifyLogTypeEnum orderModifyLogTypeEnum, ExcepData excepData) {
        if (OrderExceptUtils.isContainsExcept(staff, originOrder, exceptEnum)) {
            OrderExceptUtils.updateExceptOrder(staff, originOrder, exceptEnum, 0L);
            Order updateOrder = excepData.getUpdateOrder(originOrder);
            OrderExceptUtils.updateExceptOrder(staff, updateOrder, exceptEnum, 0L);
            if(orderModifyLogTypeEnum!=null){
                OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(originOrder, orderModifyLogTypeEnum);
                excepData.orderModifyLogs.add(orderModifyLog);
            }
            if (CollectionUtils.isNotEmpty(originOrder.getSuits())) {
                for (Order suit : originOrder.getSuits()) {
                    cancelOrder(staff, suit, exceptEnum, orderModifyLogTypeEnum, excepData);
                }
            }
        }
    }

    /**
     * 分销商变更商品
     */
    private static void cancelGxItemChangeExcept(Staff staff, Trade originTrade, ExcepData excepData, ExceptEnum exceptEnum) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
        List<String> cancelSkuIds = new ArrayList<>();
        List<String> cancelOuterIds = new ArrayList<>();
        boolean containExcept = TradeExceptUtils.isContainExcept(staff, originTrade, exceptEnum);
        for (Order originOrder : orders4Trade) {
            if (OrderExceptUtils.isContainsExcept(staff, originOrder, exceptEnum)) {
                OrderExceptUtils.updateExceptOrder(staff, originOrder, exceptEnum, 0L);
                Order updateOrder = excepData.getUpdateOrder(originOrder);
                OrderExceptUtils.updateExceptOrder(staff, updateOrder, exceptEnum, 0L);
                cancelSkuIds.add(originOrder.getSkuId());
                cancelOuterIds.add(originOrder.getOuterIid());
            }
            if (CollectionUtils.isNotEmpty(originOrder.getSuits())) {
                for (Order suit : originOrder.getSuits()) {
                    if (OrderExceptUtils.isContainsExcept(staff, suit, exceptEnum)) {
                        OrderExceptUtils.updateExceptOrder(staff, suit, exceptEnum, 0L);
                        Order updateSuit = excepData.getUpdateOrder(suit);
                        OrderExceptUtils.updateExceptOrder(staff, updateSuit, exceptEnum, 0L);
                    }
                }
            }
        }
        if (containExcept) {
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.updateExcept(staff, originTrade, exceptEnum, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 0L);
        }
        if (CollectionUtils.isNotEmpty(cancelSkuIds)) {
            String format = getLogFormat(cancelSkuIds, cancelOuterIds, originTrade);
            processTrace(originTrade, excepData, format);
        }
    }
    private static String getLogFormat(List<String> cancelSkuIds, List<String> cancelOuterIds, Trade trade) {
        //奇门订单可能存在skuId为空的情况，为空的时候直接取平台商家编码
        if (TradeUtils.isQimenFxSource(trade)){
            List<String> filterNullSkuIds = cancelSkuIds.stream().filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(filterNullSkuIds) ? "skuID为：" + StringUtils.join(filterNullSkuIds)
                    : "平台商家编码为：" + StringUtils.join(cancelOuterIds);
        }
        return "skuID为：" + StringUtils.join(cancelOuterIds);
    }


    public static void markExcept(Staff staff, Trade originTrade, Long exceptId, ExcepData excepData, Map<Long, String> exceptNameMap) {
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        // 兼容一下三方仓异常
        if (exceptEnum == null) {
            exceptEnum = ExceptEnum.CUSTOM;
        }
        switch (exceptEnum) {
            case INSUFFICIENT:
                markInsufficient(staff, originTrade, excepData);
                break;
            case UNATTAINABLE:
                markTradeExcept(staff, originTrade, excepData, exceptEnum);
                break;
            case CUSTOM:
            case PARTY3_WAREHOUSE:
            case PARTY3_UNCREATE:
            case PARTY3_CREATE:
            case PARTY3_RECREATE:
            case QIMEN_TRADE_INTERCEPT_SUCCESS:
            case QIMEN_TRADE_INTERCEPT_FAILED:
            case PARTY3_UNATTAINABLE:
            case UNIQUE_CODE_CLOSE:
            case SHIP_BOX_SPLIT_ORDER:
            case FX_STOP_DELIVER_EXCEPT:
                // 兼容三方仓异常，三方仓异常属于自定义异常标记
                markCustomerExcept(staff, originTrade, excepData, exceptId, exceptNameMap);
                break;
            default:
                Logs.debug(LogHelper.buildLog(staff, String.format("%s 未实现！", exceptId)));
                break;
        }
    }


    /**
     * 标记库存不足异常
     * @param staff
     * @param originTrade
     * @param excepData
     */
    private static void markInsufficient(Staff staff,Trade originTrade,ExcepData excepData){
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
        boolean containExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.INSUFFICIENT);
        for (Order order : orders4Trade) {
            Order updateOrder = excepData.getUpdateOrder(order);
            OrderExceptUtils.setStockStatus(staff, updateOrder, Trade.STOCK_STATUS_INSUFFICIENT);
            if (order.getSuits() != null) {
                for (Order suit : order.getSuits()) {
                    Order updateSuit = excepData.getUpdateOrder(suit);
                    OrderExceptUtils.setStockStatus(staff, updateSuit, Trade.STOCK_STATUS_INSUFFICIENT);
                }
            }
        }
        if(!containExcept){
            Trade updateTrade = excepData.getUpdateTrade(originTrade);
            TradeExceptUtils.setStockStatus(staff,updateTrade,Trade.STOCK_STATUS_INSUFFICIENT);
            processMarkTrace(updateTrade, excepData, ExceptEnum.INSUFFICIENT.getChinese(),OpEnum.EXCEPT_UPDATE);
        }

    }

    /**
     * trade上的异常标记
     * @param staff
     * @param originTrade
     * @param excepData
     * @param exceptEnum
     */
    private static void markTradeExcept(Staff staff, Trade originTrade, ExcepData excepData, ExceptEnum exceptEnum) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, exceptEnum)) {
            return;
        }
        Trade updateTrade = excepData.getUpdateTrade(originTrade);
        TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 1L);
        processMarkTrace(updateTrade, excepData, exceptEnum.getChinese(),OpEnum.EXCEPT_UPDATE);
    }

    /**
     * 自定义异常标记
     * @param staff
     * @param originTrade
     * @param excepData
     * @param exceptId
     * @param exceptNameMap
     */
    private static void markCustomerExcept(Staff staff, Trade originTrade, ExcepData excepData, Long exceptId, Map<Long, String> exceptNameMap) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, exceptId)) {
            return;
        }
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        String exceptName = TradeExceptUtils.getExceptChineseName(staff, exceptId, exceptNameMap);
        Trade updateTrade = excepData.getUpdateTrade(originTrade);
        TradeExceptUtils.updateExcept(staff, updateTrade, exceptId, 1L);
        if(StringUtils.isBlank(exceptName)){
            // 理论上肯定会有中文的
           Logs.debug(LogHelper.buildLog(staff,String.format("%s 异常找不到中文翻译！sid=%s",exceptId,originTrade.getSid() )));
        }
        if ((ExceptEnum.FX_STOP_DELIVER_EXCEPT.getId() - exceptId == 0 ||
                (exceptEnum != null && ExceptEnum.FX_STOP_DELIVER_EXCEPT.getId() - exceptEnum.getId() == 0))
                && TradeUtils.isFxSource(originTrade)) {
            excepData.fxStopDeliverSids.add(originTrade.getSid());
        }
        processMarkTrace(updateTrade, excepData, exceptName,OpEnum.EXCEPT_UPDATE);
    }


    public static void processMarkTrace(Trade originTrade, ExcepData excepData, String exceptionName,OpEnum opEnum) {
        if (StringUtils.isBlank(exceptionName)) {
            return;
        }
        String optPrefix = "";
        if(originTrade.hasOpV(OpVEnum.TRADE_FX_EXCEPT_SYNC) && TradeUtils.isFxSource(originTrade)){
            optPrefix = TradeUtils.isFxOrMixTrade(originTrade)?"供销商":"分销商";
        }
        if(opEnum==OpEnum.EXCEPT_UPDATE){
            String exceptionHeaderName = optPrefix + "转异常：";
            String content = excepData.getUpdateTrade(originTrade).getOperations().get(opEnum);
            excepData.getUpdateTrade(originTrade).getOperations().put(opEnum, StringUtils.isBlank(content) ? (exceptionHeaderName + exceptionName) : (content + "、" + exceptionName));
        }else if(opEnum==OpEnum.EXCEPTION_CANCEL){
            String exceptionHeaderName = optPrefix + "覆盖取消异常：";
            String content = excepData.getUpdateTrade(originTrade).getOperations().get(opEnum);
            excepData.getUpdateTrade(originTrade).getOperations().put(opEnum, StringUtils.isBlank(content) ? (exceptionHeaderName + exceptionName) : (content + "、" + exceptionName));
        }

    }

}

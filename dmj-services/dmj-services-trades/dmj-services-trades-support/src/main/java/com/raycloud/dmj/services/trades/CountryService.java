package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.dao.trade.CountryDAO;
import com.raycloud.dmj.domain.trades.Country;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: ruanyaguang
 * @Date : 2017/11/10
 */
@Service
public class CountryService implements ICountryService {
    @Resource
    CountryDAO countryDAO;

    @Cacheable(value = "defaultCache#31104000", key = "'country_isoname_map'")
    @Override
    public Map<String, Country> getIsoNameMap() {
        return countryDAO.getIsoNameMap();
    }

    @Cacheable(value = "defaultCache#31104000", key = "'country_enname_map'")
    @Override
    public Map<String, Country> getEnNameMap() {
        return countryDAO.getEnNameMap();
    }
}

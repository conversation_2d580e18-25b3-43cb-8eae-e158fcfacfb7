package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONObject;
import com.github.ltsopensource.core.commons.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.request.TradePictureMemoUpdateParams;
import com.raycloud.dmj.services.trades.ITradePictureMemoService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @gitname lxc
 * @eamil <EMAIL>
 * @time 2022_09_21 16:11
 */
@Service
public class TradePictureMemoService implements ITradePictureMemoService {

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    TbTradeDao tbTradeDao;

    @Override
    public int update(Staff staff, TradePictureMemoUpdateParams params) {
        List<TradeExt> tradeExts = tradeExtDao.queryBySidIn(staff, Lists.newArrayList(params.getSid()));
        TradeExt dbTradeExt = null;
        TradeExt updateTradeExt = new TradeExt();
        if (!CollectionUtils.isEmpty(tradeExts)){
            dbTradeExt = tradeExts.get(0);
            updateTradeExt.setSid(dbTradeExt.getSid());
        }

        //请求里面没有值表示想清空这个图片列表
        if (CollectionUtils.isEmpty(params.getTradePictureMemoUris())){
            if (Objects.isNull(dbTradeExt)){
                return 1;
            }else {
                List temp = Lists.newArrayList("$.tradePictureMemoUris", new ArrayList<String>());
                updateTradeExt.setExtraFieldsList(temp);
                tradeExtDao.batchUpdate(staff,Lists.newArrayList(updateTradeExt));
                return 1;
            }
        }

        //如果请求有值
        Assert.isTrue(params.getTradePictureMemoUris().size()<=10,"图片数量必须小于/等于10张!");
        if (Objects.isNull(dbTradeExt)){
            //TradeExt表没有，尝试从trade拿数据
            TbTrade tbTrade = tbTradeDao.queryBySid(staff, params.getSid());
            if (tbTrade==null || tbTrade.getSid()==null){
                throw new IllegalArgumentException("找不到系统单号为:"+params.getSid()+"的订单！");
            }
            TradeExt insertTradeExt = initTradeExt(tbTrade);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tradePictureMemoUris",params.getTradePictureMemoUris());
            insertTradeExt.setExtraFields(jsonObject.toJSONString());
            tradeExtDao.batchInsert(staff,Lists.newArrayList(insertTradeExt));
            return 1;
        }else if(Objects.isNull(dbTradeExt.getExtraFields())){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tradePictureMemoUris",params.getTradePictureMemoUris());
            updateTradeExt.setExtraFields(jsonObject.toJSONString());
            tradeExtDao.batchInitExtraField(staff,Lists.newArrayList(updateTradeExt));
            return 1;
        }else {
            tradeExtDao.updateForExtFieldArray(staff,updateTradeExt.getSid(),"$.tradePictureMemoUris",params.getTradePictureMemoUris());
            return 1;
        }
    }

    @NotNull
    private TradeExt initTradeExt(TbTrade tbTrade) {
        TradeExt insertTradeExt = new TradeExt();
        insertTradeExt.setSid(tbTrade.getSid());
        insertTradeExt.setCompanyId(tbTrade.getCompanyId());
        insertTradeExt.setTid(tbTrade.getTid());
        insertTradeExt.setUserId(tbTrade.getUserId());
        return insertTradeExt;
    }
}

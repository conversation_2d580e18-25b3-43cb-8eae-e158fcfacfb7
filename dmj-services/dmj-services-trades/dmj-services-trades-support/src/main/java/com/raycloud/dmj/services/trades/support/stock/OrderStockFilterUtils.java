package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.utils.CommonConstants;

import java.util.Objects;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/11/11 11:44 上午
 * @Description:
 */
public class OrderStockFilterUtils {

    public static boolean orderResumesStockFilter(Order order) {
        Order originOrder = order.getOrigin();
        if (order.getItemSysId() <= 0 && (originOrder == null || originOrder.getItemSysId() <= 0)) {
            return true;
        }
        //原来已经关闭的单，目前还是关闭的
        if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
            if (originOrder != null && Trade.SYS_STATUS_CLOSED.equals(originOrder.getSysStatus())) {
                return true;
            }
            if (Trade.SYS_STATUS_CLOSED.equals(order.getOldSysStatus()) && CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                return true;
            }
        }
        return false;
    }
}

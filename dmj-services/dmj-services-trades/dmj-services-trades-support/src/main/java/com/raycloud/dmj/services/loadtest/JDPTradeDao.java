package com.raycloud.dmj.services.loadtest;

import com.alibaba.dubbo.common.utils.Assert;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.services.loadtest.domain.JDPTrade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 查询聚石塔订单推送数据库的dao
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 16/9/20.
 */
@Repository
public class JDPTradeDao {

    private static final JDPTradeRowMapper rowMapper = new JDPTradeRowMapper();

    private static final String LIST_TRADE_SQL = "SELECT * FROM jdp_tb_trade WHERE seller_nick = ? AND jdp_modified >= ? AND jdp_modified < ? ORDER BY jdp_modified LIMIT ?,?";

    @Autowired(required = false)
    private JdbcTemplate jdpTemplate;

    public List<JDPTrade> queryPageListByJdpModified(String sellerNick, Date startModified, Date endModified, Page page) {
        return jdpTemplate.query(LIST_TRADE_SQL, buildQueryArguments(sellerNick, startModified, endModified, page), rowMapper);
    }

    public List<JDPTrade> queryByTids(String tid, Integer count) {
        Assert.notNull(count, "请输入count");
        Long[] tids = new Long[count];
        List<String> placeholders = new ArrayList<>(count + 1);
        for (int i = 1; i <= count; i++) {
            tids[i - 1] = (Long.parseLong(tid + StringUtils.leftPad(i + "", 6, "0")));
            placeholders.add("?");
        }
        return jdpTemplate.query("select * from jdp_tb_trade where tid in (" + StringUtils.join(placeholders, ",") + ")", tids, rowMapper);
    }

    public List<JSONObject> executeSelectSql(String selectSql) {
        return jdpTemplate.query(selectSql, new Object[0], (rs, rowNum) -> {
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            JSONObject rowJSON = new JSONObject();
            for (int index = 1; index <= columnCount; index++) {
                String column = JdbcUtils.lookupColumnName(rsmd, index);
                rowJSON.put(column, rs.getString(index));
            }

            return rowJSON;
        });
    }

    private static Object[] buildQueryArguments(String sellerNick, Date startCreated, Date endCreated, Page page) {
        Object[] arguments = new Object[5];
        arguments[0] = sellerNick;
        arguments[1] = startCreated;
        arguments[2] = endCreated;
        arguments[3] = page.getStartRow();
        arguments[4] = page.getPageSize();
        return arguments;
    }

    private static class JDPTradeRowMapper implements RowMapper<JDPTrade> {

        @Override
        public JDPTrade mapRow(ResultSet rs, int rowNum) throws SQLException {
            JDPTrade trade = new JDPTrade();
            trade.setBuyerNick(rs.getString("buyer_nick"));
            trade.setCreate(rs.getTimestamp("created"));
            trade.setJdpCreated(rs.getDate("jdp_created"));
            trade.setJdpHashcode(rs.getString("jdp_hashcode"));
            trade.setJdpModified(rs.getTimestamp("jdp_modified"));
            trade.setJdpResponse(rs.getString("jdp_response"));
            trade.setModified(rs.getTimestamp("modified"));
            trade.setSellerNick(rs.getString("seller_nick"));
            trade.setStatus(rs.getString("status"));
            trade.setTid(rs.getLong("tid"));
            trade.setType(rs.getString("type"));

            return trade;
        }
    }
}

package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.business.trade.TradeStockData;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OrderOpeartEnum;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-09-19 10:46
 * @Description OrderStockUtils
 */
public class OrderStockUtils {

    public static final String OPERATION_APPLY = "apply";
    public static final String OPERATION_AUDIT = "audit";
    public static final String OPERATION_UNAUDIT = "unaudit";
    public static final String OPERATION_CHANGE_WAREHOUSE = "change_warehouse";
    public static final String OPERATION_CHANGE_ITEM_KIND = "change_item_kind";
    public static final String OPERATION_CHANGE_ITEM_NUM = "change_item_num";
    public static final String OPERATION_CHECK = "check";


    private final static Logger logger = Logger.getLogger(OrderStockUtils.class);

    static TradeStockData filterApplyStockOrders(Staff staff, List<Order> orders, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig) {
        return filterApplyStockOrders(staff, orders, tradeLocalConfigurable, tradeConfig, true);
    }

    static TradeStockData filterApplyStockOrders(Staff staff, List<Order> orders, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig, boolean check) {
        TradeStockData data = new TradeStockData();
        if (orders == null || orders.size() == 0) {
            return data;
        }
        for (Order order : orders) {
            if (order.getEnableStatus() == 0) {
                continue;
            }

            boolean applySon = false;
            List<Order> suits = order.getSuits();
            if (suits != null && suits.size() != 0 && order.getType() == Order.TypeOfCombineOrder) {//套件子订单,需申请单品的库存
                for (Order son : suits) {
                    if (son.isVirtual() || son.ifNonConsign() || OrderUtils.isFxOrMixOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order)) {//虚拟商品不申请库存,分销商品不申请库存
                        if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                            // KMERP-264022: 分销订单也要标记库存不足异常
                            continue;
                        }
                        if(!OrderPlatExceptUtils.isContainsExcept(staff,son,ExceptEnum.UNALLOCATED)){
                            son.setStockNum(son.getNum());
                            OrderExceptUtils.setStockStatus(staff, son, Trade.STOCK_STATUS_NORMAL);
                        }
                        continue;
                    }
                    son.setUserId(order.getUserId());
                    son.setWarehouseId(order.getWarehouseId());
                    if (checkOrder4Apply(staff, son, tradeLocalConfigurable, tradeConfig, check)) {
                        data.addApplyRequest(staff, son);
                        applySon = true;
                    }
                }
                if (applySon) {
                    data.addApplyRequest(staff, order);
                } else {
                    // 待付款拍下减库存，不需要锁库存才更新stockstatus,不需要申请库存，但是需要去锁库存的
                    if (isNeedLock(staff, tradeLocalConfigurable, order, check)) {
                        if(!OrderExceptUtils.isContainsExcept(staff,order,ExceptEnum.UNALLOCATED)){
                            order.setStockNum(order.getNum());
                            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
                        }
                    }

                }
            } else {
                if (Objects.equals(order.getType(),Order.TypeOfProcessOrder)) {
                    // 加工商品子商品不锁库存
                    if (CollectionUtils.isNotEmpty(suits)) {
                        for (Order suit : suits) {
                            if (OrderPlatExceptUtils.isContainsExcept(staff, suit, ExceptEnum.UNALLOCATED)) {
                                continue;
                            }
                            suit.setStockNum(suit.getNum());
                            OrderExceptUtils.setStockStatus(staff, suit, Trade.STOCK_STATUS_NORMAL);
                        }
                    }
                }
                if (checkOrder4Apply(staff, order, tradeLocalConfigurable, tradeConfig, check)) {
                    data.addApplyRequest(staff, order);
                }
            }
        }
        return data;
    }

    private static boolean isWaitPaySubStock(Staff staff, TradeLocalConfigurable tradeLocalConfigurable, Order order) {
        Integer openWaitPayNotLockStock = staff.getConf().getOpenWaitPayNotLockStock();
        //修改了原有的逻辑，tj配置的优先级提高了
        //boolean openConfig = tradeLocalConfigurable.isIgnoreSubStock(order.getCompanyId()) || (openWaitPayNotLockStock != null && openWaitPayNotLockStock == 1);
        //return (!openConfig && Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus()) && (order.getSubStock() != null && order.getSubStock() == 0));
        //如果开启了全局付款减库存，或者商品并非处于待付款状态，返回false
        if (tradeLocalConfigurable.isIgnoreSubStock(order.getCompanyId()) || !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus())) {
            return false;
        } else if (openWaitPayNotLockStock.equals(0)) {
            //默认情况 根据订单也就是商品属性判断
            return order.getSubStock() != null && order.getSubStock() == 0;
        } else {
            return !openWaitPayNotLockStock.equals(1);
        }

    }

    private static boolean checkOrder4Apply(Staff staff, Order order, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig, boolean check) {
        boolean needLock = false;
        if (!check || Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus())) {
            if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) || isWaitPaySubStock(staff, tradeLocalConfigurable, order)) {
                if (order.getItemSysId() > 0) {
                    needLock = true;
                } else {
                    OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_UNALLOCATED);
                }
            }
        }

        if(CommonConstants.PLAT_FORM_TYPE_QINSI.equals(order.getSource()) && order.getItemSysId() != null && order.getItemSysId() > 0){
            needLock = true;
        }
        boolean needApplyStock = !TradeStockUtils.needNotApplyStock(staff, order, tradeConfig) && !OrderUtils.isFxOrMixOrder(order);
        if (!needApplyStock && needLock && !OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
            if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                // KMERP-264022: 分销订单也要标记库存不足异常
                return false;
            }
            order.setStockNum(order.getNum());
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
        }
        return needApplyStock&&needLock;
    }

    public static void handleApplyStock(Staff staff, List<Order> orders, List<StockOrderRecord> records, String operation, TradeConfig tradeConfig) {
        boolean fillRealStockNum = true;
        if (staff.openAuditActiveStockRecord() && (OPERATION_UNAUDIT.equals(operation) || OPERATION_APPLY.equals(operation))) {
            fillRealStockNum = false;
        }
        handleApplyStock(staff, orders, records, operation, fillRealStockNum, tradeConfig);
    }

    static void handleApplyStock(Staff staff, List<Order> orders, List<StockOrderRecord> records, String operation, boolean fillRealStockNum, TradeConfig tradeConfig) {
        if (records == null || records.size() == 0) {
            return;
        }
        Map<Long, Order> orderMap = OrderUtils.toFullOrderMap(orders);
        Map<Long, Order> parents = new HashMap<>();
        StringBuilder log = new StringBuilder();
        int count = 0;
        for (StockOrderRecord record : records) {
            Order order = orderMap.get(record.getOrderId());
            if (order != null) {
                count++;
                if (order.getSuits() != null && order.getSuits().size() != 0 && order.getType() == Order.TypeOfCombineOrder) {//套件本身需要根据单品的回执计算
                    continue;
                }
                if (logger.isDebugEnabled()) {
                    log.append("[id=").append(order.getId()).append(",sysStatus=").append(order.getSysStatus()).append(",num").append(order.getNum()).append(",stockNum=").append(order.getStockNum()).append(",recordStockNum=").append(record.getStockNum()).append(",fillRealStockNum=").append(fillRealStockNum).append(",keepInsufficientStockStatus=").append(order.getKeepInsufficientStockStatus()).append(",stockStatus=").append(order.getStockStatus()).append(",type=").append(order.getType()).append(",isVirtual").append(order.getIsVirtual()).append("]");
                    if (count > 6) {
                        logger.debug(LogHelper.buildLog(staff, log.toString()));
                        log = new StringBuilder();
                        count = 0;
                    }
                }
                if (order.getOldStockStatus() == null) {
                    order.setOldStockStatus(order.getStockStatus());
                }
                // 换仓过来的，并且库存足够，取实际申请的stockNum
                if (OPERATION_CHANGE_WAREHOUSE.equals(operation) &&
                        Objects.equals(order.getNum(), Optional.ofNullable(record.getStockNum()).map(Long::intValue).orElse(0))) {
                    order.setStockNum(record.getStockNum() == null ? 0 : record.getStockNum().intValue());
                } else if (order.getKeepInsufficientStockStatus()) {
                    order.setStockNum(0);
                } else {
                    order.setStockNum(fillRealStockNum ? record.getStockNum() == null ? 0 : record.getStockNum().intValue() : order.getNum());
                    /**
                     * {@link com.raycloud.dmj.business.trade.StockApply2Business#after} 这里的realStockNum是给后置锁单用的
                     */
                    if (record.getRealStockNum() != null) {
                        order.setDiffStockNum(order.getNum() - record.getRealStockNum());
                    }
                }
                //order.setStockStatus(TradeStockUtils.getStockStatusStr(order.getStockNum(), order.getNum()));
                OrderExceptUtils.setStockStatus(staff,order,TradeStockUtils.getStockStatusStr(order.getStockNum(), order.getNum()));
                if (order.getCombineId() != null && order.getCombineId() > 0) {
                    Order parent = orderMap.get(order.getCombineId());
                    if (parent != null) {
                        parents.put(parent.getId(), parent);
                    }
                }
            }else {
                log.append("[找不到对应order id=").append(record.getOrderId()).append(",sid:").append(record.getSid()).append("stockNum:").append(record.getStockNum()).append(",num:").append(record.getNum()).append("]");
            }
        }
        if (log.length() > 0) {
            logger.debug(LogHelper.buildLog(staff, log.toString()));
        }
        if (parents.size() > 0) {//根据套件单品库存计算套件本身的库存状态
            for (Map.Entry<Long, Order> entry : parents.entrySet()) {
                Order parent = entry.getValue();
                TradeStockUtils.handleSuitOrderStock(staff, parent, fillRealStockNum, tradeConfig);
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("[%s]交易[sid=%s,tid=%s]套件[id=%s,numIid=%s,skuId=%s,sysItemId=%s,sysSkuId=%s,num=%s]库存计算结果: stockNum=%s,stockStatus=%s",
                            operation, parent.getSid(), parent.getTid(), parent.getId(), parent.getNumIid(), parent.getSkuId(), parent.getItemSysId(), parent.getSkuSysId(), parent.getNum(), parent.getStockNum(), parent.getStockStatus())));
                }
            }
        }
    }

    static void handleResumeStock(Staff staff,List<Order> orders, TradeConfig tradeConfig) {
        Map<Long, Order> orderMap = OrderUtils.toFullOrderMap(orders);
        Map<Long, Order> parents = new HashMap<>();
        for (Order order : orderMap.values()) {
            if (order.getSuits() != null && order.getSuits().size() != 0) {//套件本身需要根据单品的回执计算
                continue;
            }
            if (OrderUtils.isFxOrMixOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order)) {
                continue;
            }
            handleResumeStock(staff,order, tradeConfig);
            order.setStockNum(0);
            if (order.getCombineId() != null && order.getCombineId() > 0) {
                Order parent = orderMap.get(order.getCombineId());
                if (parent != null) {
                    parents.put(parent.getId(), parent);
                }
            }
        }
        if (parents.size() > 0) {
            for (Map.Entry<Long, Order> entry : parents.entrySet()) {
                Order parent = entry.getValue();
                handleResumeStock(staff,parent, tradeConfig);
                parent.setStockNum(0);
            }
        }
    }

    private static void handleResumeStock(Staff staff,Order order, TradeConfig tradeConfig) {
        if (order.getOrderOpeart() == OrderOpeartEnum.DELETE || Objects.equals(order.getEnableStatus(), 0)) {
            //删除的order不要在计算库存
            return;
        }
        String stockStatus = TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) || order.isVirtual() || order.ifNonConsign() || TradeStockUtils.needNotApplyStock(staff, order, tradeConfig) ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_EMPTY;
        if(OrderPlatExceptUtils.isContainsExcept(staff,order,ExceptEnum.UNALLOCATED)){
            OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_UNALLOCATED);
        }else {
            OrderExceptUtils.setStockStatus(staff,order,stockStatus);
        }
       // order.setStockStatus(stockStatus);

    }


    static TradeStockData filterUpdateStockOrders(Staff staff, List<Order> orders, TradeLocalConfigurable tradeLocalConfigurable, boolean containVirtual, TradeConfig tradeConfig) {
        TradeStockData data = new TradeStockData();
        if (orders == null || orders.size() == 0) {
            return data;
        }
        for (Order order : orders) {
            boolean updateSon = false;
            if (order.getSuits() != null && order.getSuits().size() != 0 && order.getType() == Order.TypeOfCombineOrder) {
                for (Order son : order.getSuits()) {
                    son.setWarehouseId(order.getWarehouseId());
                    son.setOldWarehouseId(order.getOldWarehouseId());
                    son.setUserId(order.getUserId());
                    son.setExceptData(order.getExceptData());
                    son.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                    if (checkOrder4Update(staff, son, tradeLocalConfigurable, containVirtual, tradeConfig)) {
                        data.addAllRequest(staff, son);
                        updateSon = true;
                    }
                }
                if (updateSon) {
                    data.addAllRequest(staff, order);
                }
            } else {
                if (checkOrder4Update(staff, order, tradeLocalConfigurable, containVirtual, tradeConfig)) {
                    data.addAllRequest(staff, order);
                }
            }
        }
        return data;
    }

    private static boolean checkOrder4Update(Staff staff, Order order, TradeLocalConfigurable tradeLocalConfigurable, boolean containVirtual, TradeConfig tradeConfig) {
        boolean needUpdate = false;
        //平台分销订单不处理
        if (OrderUtils.isPlatformFxOrder(order)) {
            return false;
        }
        if (order.getItemSysId() > 0 &&
                (TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) || isWaitPaySubStock(staff, tradeLocalConfigurable, order))) {//匹配了系统商品并且未发货的情况下才会做库存检查
            needUpdate = order.isOperable();
        }
        return (!TradeStockUtils.needNotApplyStock(staff,order, tradeConfig) || (containVirtual && (order.getScalping() == null || order.getScalping() == 0))) && needUpdate;
    }


    static void fillOrder(Trade trade, List<Order> stockOrders) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            order.setUserId(order.getUserId() == null ? trade.getUserId() : order.getUserId());
            order.setWarehouseId(order.getWarehouseId() == null ? trade.getWarehouseId() : order.getWarehouseId());
            order.setScalping(trade.getScalping());
            order.setAutoUnattainable(trade.getAuditMatchRule());
            stockOrders.add(order);
            List<Order> suits = order.getSuits();
            if (suits != null && suits.size() > 0) {
                for (Order suit : suits) {
                    suit.setUserId(order.getUserId() == null ? trade.getUserId() : order.getUserId());
                    suit.setWarehouseId(order.getWarehouseId() == null ? trade.getWarehouseId() : order.getWarehouseId());
                    suit.setScalping(trade.getScalping());
                    suit.setAutoUnattainable(trade.getAuditMatchRule());
                }
            }
        }
    }

    /**
     * 后置解锁
     * @param staff
     * @param orders
     * @param tradeLocalConfigurable
     * @param tradeConfig
     * @param check
     * @return
     */
    static TradeStockData rearUnlockFilterApplyStockOrders(Staff staff, List<Order> orders, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig, boolean check) {
        TradeStockData data = new TradeStockData();
        data.setPreLock(Boolean.TRUE);
        if (orders == null || orders.size() == 0) {
            return data;
        }
        for (Order order : orders) {
            if (OrderUtils.isFxOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order)) {
                continue;
            }
            boolean applySon = false;
            List<Order> suits = order.getSuits();
            if (suits != null && suits.size() != 0 && order.getType() == Order.TypeOfCombineOrder) {//套件子订单,需申请单品的库存
                for (Order son : suits) {
                    if (son.isVirtual() || son.ifNonConsign()) {//虚拟商品不申请库存
                        continue;
                    }
                    if (OrderUtils.isFxOrder(order)) {//分销商品不申请库存
                        continue;
                    }
                    son.setUserId(order.getUserId());
                    son.setWarehouseId(order.getWarehouseId());
                    if (rearUnlockCheckOrder4Apply(staff,son, tradeLocalConfigurable, tradeConfig, check)) {
                        data.addApplyRequest(staff, son);
                        applySon = true;
                    }
                }
                if (applySon) {
                    data.addApplyRequest(staff, order);
                }
            } else {
                if (rearUnlockCheckOrder4Apply(staff,order, tradeLocalConfigurable, tradeConfig, check)) {
                    data.addApplyRequest(staff, order);
                }
            }
        }
        return data;
    }

    private static boolean rearUnlockCheckOrder4Apply(Staff staff,Order order, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig, boolean check) {
        boolean needLock = false;
        if (!check || Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus())
                || Trade.STOCK_STATUS_NORMAL.equals(order.getStockStatus())) {
            if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) || ((!tradeLocalConfigurable.isIgnoreSubStock(order.getCompanyId()) && Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus()) && (order.getSubStock() != null && order.getSubStock() == 0)))) {
                if (order.getItemSysId() > 0) {
                    needLock = true;
                } else {
                    //order.setStockStatus(Trade.STOCK_STATUS_UNALLOCATED);
                    OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_UNALLOCATED);
                }
            }
        }
        return !TradeStockUtils.needNotApplyStock(staff,order, tradeConfig) && needLock && !OrderUtils.isFxOrder(order);
    }

    /**
     * 是否是拍下减库存
     * @param staff
     * @param tradeLocalConfigurable
     * @param order
     * @param check
     * @return
     */
   public static boolean isNeedLock(Staff staff, TradeLocalConfigurable tradeLocalConfigurable, Order order, boolean check) {
        if (!check || Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus())) {
            if (TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) || isWaitPaySubStock(staff, tradeLocalConfigurable, order)) {
                if (order.getItemSysId() > 0) {
                    return true;
                }
            }
        }
        return false;
    }
}

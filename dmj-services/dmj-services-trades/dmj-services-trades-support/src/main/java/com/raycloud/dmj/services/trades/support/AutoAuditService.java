package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.dao.trade.AutoAuditConfigDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.trade.enums.TradeBusinessRuleEnum;
import com.raycloud.dmj.domain.trade.enums.TradeRuleOperationTypeEnum;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trade.rule.convert.AuditConvertUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.SortUtils;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.utils.ConfigTraceUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 审核订单服务
 *
 * <AUTHOR>
 */
@Service
public class AutoAuditService implements IAutoAuditService {

    @Resource
    SpelConditionBusiness spelConditionBusiness;

    @Resource
    AutoAuditConfigDao autoAuditConfigDao;

    @Resource
    ITradeRuleService tradeRuleService;

    @Override
    public List<AutoAuditConfig> queryConfigs(Staff staff, Integer isOpen) {
        return autoAuditConfigDao.queryByCompanyId(staff, isOpen);
    }

    @Override
    public AutoAuditConfig save(Staff staff, AutoAuditConfig newConfig) {
        validateCondition(staff, newConfig);
        AutoAuditConfig oldConfig;
        if (newConfig.getId() == null) {
            autoAuditConfigDao.insert(staff, newConfig);
            newConfig.setBusinessType(TradeBusinessRuleEnum.AUDIT_RULE.getBusinessId());
            tradeRuleService.initTradeRule(staff, newConfig);
            oldConfig = newConfig;
        } else {
            oldConfig = autoAuditConfigDao.queryById(staff, newConfig.getId());
            autoAuditConfigDao.update(staff, newConfig);
            //老的不存在，先保存一份老的数据
            oldConfig.setBusinessType(TradeBusinessRuleEnum.AUDIT_RULE.getBusinessId());
            if (StringUtils.isBlank(oldConfig.getName())) {
                oldConfig.setName(newConfig.getName());
            }
            TradeRule oldRule = tradeRuleService.initTradeRule(staff, oldConfig);
            if (newConfig.getIsOpen() == null) {
                newConfig.setIsOpen(oldConfig.getIsOpen());
            }
            //更新
            if(CollectionUtils.isNotEmpty(newConfig.getConditions())){
                newConfig.setConditionDesc(JSON.toJSONString(newConfig.getConditions()));
            }
            tradeRuleService.modify(staff, AuditConvertUtils.convert(staff, newConfig, oldRule, TradeRuleOperationTypeEnum.UPDATE));
        }
        return oldConfig;
    }

    private void validateCondition(Staff staff, AutoAuditConfig config) {
        checkName(staff, config);
        Assert.isTrue(config.getConditions().size() > 0, "请设置审核条件");
        String excludeStart = config.getExcludeStart();
        String excludeEnd = config.getExcludeEnd();
        //判断日期是否输入
        if (excludeStart == null || excludeEnd == null) {
            throw new IllegalArgumentException("请设置正确的审核时间段！");
        }
        try {
            //判断日期是否输入正确
            Date start = turnDate(excludeStart);
            Date end = turnDate(excludeEnd);
            if (start != null && end != null) {
                if (start.getTime() > end.getTime()) {
                    throw new IllegalArgumentException("开始日期不能大于结束日期！");
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("审核时间格式错误，请重新设置");
        }
        Assert.isTrue(!CollectionUtils.isEmpty(config.getUserIds()), "店铺为空！");
        SysTradeBusiness.checkSupplierAndBrandIds(config.getSupplierIds(), config.getBrandIds());
        spelConditionBusiness.validateCondition(config.getConditions());
        //去除重复的userId
        SortUtils.deleteDuplicate(config.getUserIds());
        for (SpelCondition condition : config.getConditions()) {
            if(SpelCondition.FIELD_ORDER_SYS_OUTER_IDS.equals(condition.getField()) && StringUtils.isNotBlank(condition.getValue())){
                condition.setValue(condition.getValue().trim().replaceAll("，", ","));
                int length = condition.getValue().split(",").length;
                Assert.isTrue(length <= 2000, "商品配置条件最多支持2000个商品，当前设置超过2000，请重新减少后重新保存");
            }
        }

    }

    private void checkName(Staff staff, AutoAuditConfig rule) {
        String name = StringUtils.trimToEmpty(rule.getName());
        if (StringUtils.isBlank(name)) {
            throw new IllegalArgumentException("规则名称不能为空");
        }
        List<AutoAuditConfig> configs = autoAuditConfigDao.queryByCompanyId(staff, null);
        if (CollectionUtils.isNotEmpty(configs)) {
            for (AutoAuditConfig config : configs) {
                if (StringUtils.equals(name, config.getName()) && (rule.getId() == null || config.getId() - rule.getId() != 0)) {
                    throw new IllegalArgumentException("名称[" + name + "]已经存在，请修改名称！");
                }
            }
        }
    }

    private Date turnDate(String stringDate) {
        if (StringUtils.isNotBlank(stringDate)) {
            String[] split = stringDate.split(":");
            Calendar cal = new GregorianCalendar();
            cal.setTime(new Date());
            cal.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
            cal.set(Calendar.MINUTE, Integer.parseInt(split[1]));
            cal.set(Calendar.SECOND, 0);
            return cal.getTime();
        }
        return null;
    }

    @Override
    public void turnOnOff(Staff staff, Long configId, Integer isOpen) {
        AutoAuditConfig newConfig = new AutoAuditConfig();
        newConfig.setId(configId);
        newConfig.setIsOpen(isOpen);
        AutoAuditConfig oldConfig = autoAuditConfigDao.queryById(staff, newConfig.getId());
        autoAuditConfigDao.update(staff, newConfig);

        oldConfig.setBusinessType(TradeBusinessRuleEnum.AUDIT_RULE.getBusinessId());
        if (StringUtils.isBlank(oldConfig.getName())) {
            oldConfig.setName(NumberUtils.long2Str(configId));
        }
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, oldConfig);
        oldConfig.setIsOpen(newConfig.getIsOpen());
        //更新
        tradeRuleService.modify(staff, AuditConvertUtils.convert(staff, oldConfig, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    @Override
    public void delete(Staff staff, Long configId) {
        AutoAuditConfig oldConfig = autoAuditConfigDao.queryById(staff, configId);

        autoAuditConfigDao.delete(staff, configId);
        oldConfig.setBusinessType(TradeBusinessRuleEnum.AUDIT_RULE.getBusinessId());
        tradeRuleService.remove(staff, oldConfig);
        Logs.ifDebug(LogHelper.buildLogHead(staff).append("删除了自动审核配置, configId:").append(configId));
    }

    @Override
    public String translateCondition(Staff staff, List<SpelCondition> conditions) {
        return spelConditionBusiness.translateCondition(staff, conditions);
    }

    @Override
    public String buildUpdateLog(Staff staff, AutoAuditConfig conf, AutoAuditConfig origin) {
        return ConfigTraceUtils.buildConfigUpdateLog(buildConfMap(staff, conf), buildConfMap(staff, origin));
    }

    private Map<String, Object> buildConfMap(Staff staff, AutoAuditConfig conf) {
        if (conf == null) {
            return null;
        }
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("渠道店铺", (ObjectUtils.nullSafeEquals(conf.getRuleType(), 0) ? "店铺" : "分销店铺") + String.format("{%s}", StringUtils.trimToEmpty(conf.getShopNames())));
        resultMap.put("品牌", conf.getBrandList() == null ? null : String.format("{%s}", conf.getBrandList().stream().map(Brand::getBrandName).collect(Collectors.toList())));
        resultMap.put("供应商", conf.getSupplierList() == null ? null : String.format("{%s}", conf.getSupplierList().stream().map(Supplier::getName).collect(Collectors.joining(","))));
        resultMap.put("仓库", conf.getWarehouseList() == null ? null : String.format("{%s}", conf.getWarehouseList().stream().map(Warehouse::getName).collect(Collectors.joining(","))));
        resultMap.put("手工单是否参与审核", ObjectUtils.nullSafeEquals(conf.getAuditSysTrade(), 1) ? "是" : "否");
        resultMap.put("订单审核时间", String.format("订单审核时间:%s-%s", conf.getExcludeStart(), conf.getExcludeEnd()));
        resultMap.put("订单暂存时间", String.format("%s分钟", conf.getBeforePayTime()));
        resultMap.put("审核条件", String.format("{%s}", conf.getConditionDesc()));
        return resultMap;
    }
}

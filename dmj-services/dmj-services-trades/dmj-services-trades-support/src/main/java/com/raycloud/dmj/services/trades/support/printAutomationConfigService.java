package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.PrintAutomationConfigDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.PrintAutomationConfig;
import com.raycloud.dmj.services.trades.IPrintAutomationConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class printAutomationConfigService implements IPrintAutomationConfigService {

    @Resource
    private PrintAutomationConfigDao printAutomationConfigDao;

    @Override
    public PrintAutomationConfig query(Staff staff, Integer pageId) {
        return printAutomationConfigDao.query(staff, pageId);
    }

    @Override
    public void save(Staff staff, PrintAutomationConfig config) {
        if (null != config.getId() && config.getId() > 0){
            config.setModified(new Date());
            printAutomationConfigDao.updateById(staff, config);
        } else {
            config.setCreated(new Date());
            config.setModified(new Date());
            printAutomationConfigDao.insert(staff, config);
        }
    }
}

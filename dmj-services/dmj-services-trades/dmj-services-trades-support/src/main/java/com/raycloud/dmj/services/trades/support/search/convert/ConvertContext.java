package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.support.TbTradeSearchService;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Data
public class ConvertContext {

    public ConvertContext(Staff staff) {
        this.staff = staff;
        setTradeTable("trade");
        setOrderTable("order");
    }

    Staff staff;

    /**
     * 查询门店范围(已权限过滤)
     */
    List<User> refUsers = new ArrayList<>();

    Map<String,List<User>> userSourceMap = new HashMap<>();

    public void setRefUsers(List<User> refUsers) {
        this.refUsers = refUsers;
        for (User refUser : refUsers) {
            userSourceMap.computeIfAbsent(refUser.getSource(),x->new ArrayList<>()).add(refUser);
        }
    }

    String tradeTable;
    String orderTable;

    /**
     * 是否按游标查询
     */
    boolean isCursorQuery = false;

    /**
     * 是否为数量统计查询
     */
    boolean isCountQuery = false;

    /**
     * 是否使用了innerJoin
     */
    boolean innerJoinTmp = false;



    public void setTradeTable(String tradeTable) {
        this.tradeTable = tradeTable + "_" +  staff.getDbInfo().getTradeDbNo();
    }

    /**
     * 拼接过程中不要用这个 要使用 ConditionConstants.TRADE_TABLE_PLACE_HOLDER
     * @see ConditionConstants#TRADE_TABLE_PLACE_HOLDER
     * @see TbTradeSearchService#replaceHolder(Query, String)
     * @return
     */
    public String getTradeTable() {
        return tradeTable;
    }

    public void setOrderTable(String orderTable) {
        this.orderTable = orderTable  + "_" +  staff.getDbInfo().getOrderDbNo();
    }

    /**
     * 拼接过程中不要用这个 要使用 ConditionConstants.ORDER_TABLE_PLACE_HOLDER
     * @see ConditionConstants#ORDER_TABLE_PLACE_HOLDER
     * @see TbTradeSearchService#replaceHolder(Query, String)
     * @return
     */
    public String getOrderTable() {
        return orderTable;
    }
}

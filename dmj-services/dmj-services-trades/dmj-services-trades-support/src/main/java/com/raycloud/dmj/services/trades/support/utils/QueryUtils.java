package com.raycloud.dmj.services.trades.support.utils;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName QueryUtils
 * @Description 查询工具
 * <AUTHOR>
 * @Date 2023/11/20
 * @Version 1.0
 */
public abstract class QueryUtils {

    /**
     * 订单查询上下文必须查询的字段
     */
    private static final String[] MUST_QUERY_FIELDS = {"merge_sid"};

    public static void wrapQueryParams(Staff staff, TradeQueryParams params) {
        wrapFields(staff, params);
    }

    private static void wrapFields(Staff staff, TradeQueryParams params) {
        StringBuilder fields = new StringBuilder(StringUtils.trimToEmpty(params.getFields()));
        if (StringUtils.isBlank(fields.toString()) || fields.toString().contains("*")) {
            return;
        }
        for (String mustQueryField : MUST_QUERY_FIELDS) {
            if (!fields.toString().contains(mustQueryField)) {
                fields.append(",t.").append(mustQueryField);
            }
        }
        params.setFields(" " + fields + " ");
    }
}

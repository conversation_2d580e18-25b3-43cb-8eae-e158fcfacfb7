package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.LogisticsTrackingConfigDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.LogisticsTrackingConfig;
import com.raycloud.dmj.services.trades.ILogisticsTrackingConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-01-12-9:09
 */
@Service
public class LogisticsTrackingConfigService implements ILogisticsTrackingConfigService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    LogisticsTrackingConfigDao logisticsTrackingConfigDao;


    @Override
    @Cacheable(value = "defaultCache#600", key = "'logistics_tracking_config_' + #staff.companyId")
    public LogisticsTrackingConfig get(Staff staff) {
        LogisticsTrackingConfig config = logisticsTrackingConfigDao.get(staff);
        if (null == config && staff.getCompanyId() != null) {
            return init(staff);
        }
        return config;
    }

    private LogisticsTrackingConfig init(Staff staff) {
        LogisticsTrackingConfig config = new LogisticsTrackingConfig();
        config.setGatheredConfirmDeadline(20);
        config.setLogisticsModifyDeadline(20);
        config.setNoTransformDeadline(20);
        logisticsTrackingConfigDao.insert(staff, config);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append("初始化物流异常跟踪配置成功"));
        }
        return config;
    }

    @Override
    @CacheEvict(value = "defaultCache#600", key = "'logistics_tracking_config_' + #staff.companyId")
    public void update(Staff staff, LogisticsTrackingConfig config) {
        LogisticsTrackingConfig old = get(staff);
        if (old == null) {
            config.initNodeSetStr();
        } else {
            LogisticsTrackingConfig.NodeDeadlineSet oldNodeSet = old.getNodeDeadlineSet();
            LogisticsTrackingConfig.NodeDeadlineSet newNodeSet = config.getNodeDeadlineSet();
            LogisticsTrackingConfig.parseNodeSet(oldNodeSet, newNodeSet);
            config.setNodeDeadlineSet(newNodeSet);
        }
        logisticsTrackingConfigDao.update(staff, config);
    }

}

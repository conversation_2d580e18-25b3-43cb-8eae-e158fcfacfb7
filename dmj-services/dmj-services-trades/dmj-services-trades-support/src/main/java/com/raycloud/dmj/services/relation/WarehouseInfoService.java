package com.raycloud.dmj.services.relation;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.services.basis.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仓库信息服务类
 */
@Slf4j
@Service("wmsWarehouseInfoService")
public class WarehouseInfoService {

    @Resource
    private IWarehouseService warehouseService;

    /**
     * 填充仓库信息
     */
    public <T extends WarehouseRelation> void fillWarehouseInfo(T warehouseRelationVo, long warehouseId) {
        try {
            Warehouse warehouse = warehouseService.queryLightById(warehouseId);
            if (warehouse == null) {
                return;
            }

            warehouseRelationVo.setWarehouse(warehouse.getName());
        } catch (Exception e) {
            log.error("填充仓库信息异常", e);
        }
    }

    /**
     * 批量填充仓库信息
     */
    public <T extends WarehouseRelation> void fillWarehouseInfo(List<T> warehouseRelationVoList) {
        if (CollectionUtils.isEmpty(warehouseRelationVoList)) {
            return;
        }

        try {
            Map<Long, List<T>> warehouseRelationVoListMap = Maps.newHashMap();
            for (T vo : warehouseRelationVoList) {
                List<T> warehouseInfoVoList = warehouseRelationVoListMap.computeIfAbsent(vo.getWarehouseId(), k -> Lists.newLinkedList());
                warehouseInfoVoList.add(vo);
            }

            List<Warehouse> warehouseList = warehouseService.queryByIds(Lists.newArrayList(warehouseRelationVoListMap.keySet()));
            if (CollectionUtils.isEmpty(warehouseList)) {
                return;
            }

            Map<Long, String> warehouseNameMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, Warehouse::getName));
            for (Map.Entry<Long, List<T>> entry : warehouseRelationVoListMap.entrySet()) {
                String warehouseName = warehouseNameMap.get(entry.getKey());
                if (warehouseName == null) {
                    continue;
                }

                List<T> warehouseRelationList = entry.getValue();
                for (T warehouseRelation : warehouseRelationList) {
                    warehouseRelation.setWarehouse(warehouseName);
                }
            }
        } catch (Exception e) {
            log.error("批量填充仓库信息异常", e);
        }
    }

}

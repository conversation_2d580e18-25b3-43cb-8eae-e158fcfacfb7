package com.raycloud.dmj.services.trades.support.bigbag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.dao.trade.TradeCombineParcelDAO;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeParcelDAO;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.logistics.EnumCombineParcelNum;
import com.raycloud.dmj.domain.logistics.EnumShopeeTransitWarehouse;
import com.raycloud.dmj.domain.platform.trades.smt.OpenParcelOrderList;
import com.raycloud.dmj.domain.platform.trades.smt.ParcelExceptionCode;
import com.raycloud.dmj.domain.pt.OutSidPool;
import com.raycloud.dmj.domain.pt.UserLogisticsChannel;
import com.raycloud.dmj.domain.pt.enums.EnumLogisticsProviderType;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeCombineParcelRequest;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import com.raycloud.dmj.domain.trades.params.TradeListRequest;
import com.raycloud.dmj.domain.trades.params.TradeParcelRequest;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.trades.vo.SubbagDetailVo;
import com.raycloud.dmj.domain.user.AbroadAddress;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.pt.IOutSidPoolService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.user.IAbroadAddressService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.FieldPosition;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TradeCombineParcelService implements ITradeCombineParcelService {

    private static final Logger logger = Logger.getLogger(TradeCombineParcelService.class);

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    TradeExtBusiness tradeExtBusiness;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    IShopService shopService;

    @Resource
    private IOutSidPoolService outSidPoolService;
    @Resource
    SumaitongCombineParcelService sumaitongCombineParcelService;

    @Resource
    ILogisticsProviderService logisticsProviderService;

    @Resource
    IUserService userService;

    @Resource
    IAbroadAddressService abroadAddressService;




    @Override
    public TradeCombineParcels listCombineParcel(Staff staff, TradeCombineParcelRequest request, Page page) {
        TradeCombineParcels tradeCombineParcels = new TradeCombineParcels();
        List<TradeCombineParcel> list = tradeCombineParcelDAO.queryPage(staff, request, page);
        if (CollectionUtils.isEmpty(list)) {
            tradeCombineParcels.setTotal(0L);
            tradeCombineParcels.setPage(page);
            return tradeCombineParcels;
        }
        //查询小包列表，方便统计数量和重量
        Map<Long, List<TradeParcel>> tradeMap = new HashMap<>();
        for (TradeCombineParcel tradeCombineParcel : list) {
            List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{tradeCombineParcel.getId()}, null);
            if (CollectionUtils.isNotEmpty(tradeParcels)) {
                tradeMap.put(tradeCombineParcel.getId(), tradeParcels);
            }
        }
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeCombineParcel combineParcel : list) {
                combineParcel.setMaxParcelNum(EnumCombineParcelNum.getCombineParcelNum(combineParcel.getSource()));
                combineParcel.setSourceName(CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(combineParcel.getSource()) ? "速卖通" : combineParcel.getSource());
                if (!CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(combineParcel.getSource()) && Objects.equals(combineParcel.getGatherType(), TradeCombineParcel.STATUS_OUTBOUNDED) && StringUtils.isNotEmpty(combineParcel.getTransferWarehouseId())) {
                    AbroadAddress abroadAddress = abroadAddressService.queryById(staff, Long.valueOf(combineParcel.getTransferWarehouseId()));
                    if (abroadAddress != null) {
                        combineParcel.setTransferWarehouseAddress(abroadAddress.getProvince() + abroadAddress.getCity() + abroadAddress.getCountry() + abroadAddress.getAddr());
                    }
                }
                if (combineParcel.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(combineParcel.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                combineParcel.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
                if (StringUtils.isBlank(combineParcel.getTransferWarehouseName())) {
                    combineParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(combineParcel.getTransferWarehouseId(), combineParcel.getTransferWarehouseAddress()));
                }
                List<TradeParcel> parcelList = tradeMap.get(combineParcel.getId());
                combineParcel.setParcelNum(CollectionUtils.isEmpty(parcelList) ? 0 : parcelList.size());
                combineParcel.setWeight(CollectionUtils.isEmpty(parcelList) ? 0 : formatDigit(parcelList.stream().mapToDouble(TradeParcel::getWeight).sum(), 3));
                combineParcel.setAppointmentType(combineParcel.getAppointmentType() == null ? 0 : combineParcel.getAppointmentType());

                //计算大包状态
                getCombineParcelStatus(combineParcel, parcelList);
                //计算关联大包数量
                if (Objects.equals(combineParcel.getSource(), CommonConstants.PLAT_FORM_TYPE_SMT)) {
                    combineParcel.setMaxCombineParcelNum(EnumCombineParcelNum.SMT_MAX_SUBBAG.getMaxNum());
                    List<SubbagDetailVo> subbagDetailVoList = JSON.parseArray(combineParcel.getExtraFields(), SubbagDetailVo.class);
                    if (CollectionUtils.isNotEmpty(subbagDetailVoList)) {
                        combineParcel.setSubbagDetailList(subbagDetailVoList);
                        combineParcel.setAddCombineParcelNum(subbagDetailVoList.size());
                    } else {
                        combineParcel.setAddCombineParcelNum(0);
                    }
                }
            }
        }
        tradeCombineParcels.setList(list);
        tradeCombineParcels.setTotal(tradeCombineParcelDAO.countAll(staff, request));
        tradeCombineParcels.setPage(page);
        return tradeCombineParcels;
    }

    /**
     * 将数据转换为保留指定小数位数（0，1，2）格式的数，四舍五入
     */
    public static double formatDigit(double num, int decimalPlace) {
        DecimalFormat fm = null;
        switch (decimalPlace) {
            case 0:
                fm = new DecimalFormat("##");
                break;
            case 1:
                fm = new DecimalFormat("##.#");
                break;
            case 2:
                fm = new DecimalFormat("##.##");
                break;
            case 3:
                fm = new DecimalFormat("##.###");
                break;
            case 4:
                fm = new DecimalFormat("##.####");
                break;
            default:
                break;
        }

        if (fm == null) {
            return num;
        }

        StringBuffer sbf = new StringBuffer();
        fm.format(num, sbf, new FieldPosition(java.text.NumberFormat.FRACTION_FIELD));
        return Double.parseDouble(sbf.toString());
    }

    private void getCombineParcelStatus(TradeCombineParcel combineParcel, List<TradeParcel> parcelList) {
        if (CollectionUtils.isEmpty(parcelList)) {
            return;
        }
        //筛选已上传的小包
        Long uploadUpload = parcelList.stream().filter(e -> e.getUploadStatus() == TradeParcel.UPLOAD_STATUS_UPLOADED).count();
        long uploadTouUpload = parcelList.stream().filter(e -> e.getUploadStatus() != TradeParcel.UPLOAD_STATUS_UPLOADED).count();
        System.out.println(uploadUpload);
        if (parcelList.size() == uploadUpload) {
            combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
            return;
        }
        if (parcelList.size() == uploadTouUpload) {
            combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
            return;
        }
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
    }


    @Cacheable(value = "defaultCache#3600", key = "'TradeCombineParcelService_getShopMap_' + #staff.companyId")
    public Map<Long, Shop> getShopMap(Staff staff) {
        List<Shop> shops = shopService.queryByCompanyId(staff);
        if (CollectionUtils.isEmpty(shops)) {
            return null;
        }
        return shops.stream().collect(Collectors.toMap(Shop::getTaobaoId, shop -> shop, (v1, v2) -> v2));
    }

    @Override
    public TradeParcels listParcel(Staff staff, TradeParcelRequest request, Page page) {
        TradeParcels tradeParcels = new TradeParcels();
        if (StringUtils.isNotBlank(request.getShippingCarrier())) {
            request.setShippingCarriers(request.getShippingCarrier().split(","));
        }
        Map<String, UserLogisticsChannel> channelMap = new HashMap<>();
        List<UserLogisticsChannel> userLogisticsChannel = logisticsProviderService.queryChannelByProviderType(staff, EnumLogisticsProviderType.ALIEXPRESS.getValue());
        if (CollectionUtils.isNotEmpty(userLogisticsChannel)) {
            for (UserLogisticsChannel channel : userLogisticsChannel) {
                channelMap.put(channel.getLogisticsMapping(), channel);
            }
        }

        Map<String, OpenParcelOrderList> openParcelOrderDtoMap = handoverContentQueryList(staff, request);
        List<TradeParcel> list = tradeParcelDAO.queryPage(staff, request, page);
        if (CollectionUtils.isEmpty(list)) {
            tradeParcels.setTotal(0L);
            tradeParcels.setPage(page);
            return tradeParcels;
        }
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeParcel parcel : list) {
                if (parcel.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(parcel.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                parcel.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
                UserLogisticsChannel channel = channelMap.get(parcel.getShippingCarrier());
                parcel.setShippingCarrier(channel == null ? parcel.getShippingCarrier() : channel.getName());
                OpenParcelOrderList openParcelOrderDto = openParcelOrderDtoMap.get(parcel.getLPOrderCode());
                if(openParcelOrderDto!=null){
                    parcel.setExceptionStatus(ParcelExceptionCode.fromDescription(openParcelOrderDto.getExceptionCode()));
                }

            }
        }
        tradeParcels.setList(list);
        tradeParcels.setTotal(tradeParcelDAO.countAll(staff, request));
        tradeParcels.setPage(page);
        return tradeParcels;
    }

    private Map<String, OpenParcelOrderList> handoverContentQueryList(Staff staff, TradeParcelRequest request) {
        Map<String, OpenParcelOrderList> openParcelOrderDtoMap = new HashMap<>();
        try {
            List<TradeCombineParcel> tradeCombineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{request.getCombineParcelId()});
            if (CollectionUtils.isNotEmpty(tradeCombineParcels)) {
                TradeCombineParcel tradeCombineParcel = tradeCombineParcels.get(0);
                if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_SMT, tradeCombineParcel.getSource())) {
                    openParcelOrderDtoMap.putAll(sumaitongCombineParcelService.handoverContentQueryList(staff, tradeCombineParcel));
                }
            }
        } catch (Exception e) {
            logger.info("查询速卖通小包出错" + e);
        }

        return openParcelOrderDtoMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addParcelToCombine(Staff staff, List<Long> sids, Long taobaoId) {
        if (CollectionUtils.isEmpty(sids)) {
            throw new RuntimeException("勾选订单后操作");
        }
        if (sids.size() > 5000) {
            throw new RuntimeException("一次操作不能超过5000笔订单");
        }
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            throw new RuntimeException("查询trades为空");
        }
        tradeExtBusiness.fillTradeExt(staff, trades);
        Map<String, List<Trade>> tradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getSource));
        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            CombineParcelFactory.getInvokeStrategy(entry.getKey()).addParcelToCombine(staff, entry.getValue(), taobaoId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeParcel(Staff staff, Long combineParcelId, Long[] parcelIds) {
        if (combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, null);

        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "首公里预报移出大包：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });

    }

    /**
     * 生成系统日志
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    /**
     * 上传平台大包
     */
    @Override
    public UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param) throws Exception {
        if (param.getId() == null || param.getTaobaoId() == null || param.getGatherType() == null) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{param.getId()});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询大包为空");
        }
        TradeCombineParcel combineParcel = combineParcels.get(0);
        return CombineParcelFactory.getInvokeStrategy(combineParcel.getSource()).uploadCombineParcel(staff, param, combineParcel);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception {
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{combineParcelId});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询的大包为空");
        }
        TradeCombineParcel combineParcel = combineParcels.get(0);
        return CombineParcelFactory.getInvokeStrategy(combineParcel.getSource()).cancelUploadParcel(staff, taobaoId, combineParcelId, parcelIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelCombineParcel(Staff staff, Long[] combineParcelIds) throws Exception {
        if (combineParcelIds == null || combineParcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        //根据大包ID查询大包是否为空
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelIds);
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("查询的大包为空");
        }
        for (TradeCombineParcel parcel : combineParcels) {
            CombineParcelFactory.getInvokeStrategy(parcel.getSource()).cancelCombineParcel(staff, parcel);
        }
    }

    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void outboundCombineParcel(Staff staff, Long[] combineParcelIds) {
        if (combineParcelIds == null || combineParcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, combineParcelIds);
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("没有可用的组包");
        }
        List<TradeCombineParcel> filterCombineParcels = combineParcels.stream()
                .filter(x -> (TradeCombineParcel.STATUS_TO_OUTBOUND == x.getStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterCombineParcels)) {
            throw new RuntimeException("没有可用的组包");
        }
        updateCombineStatus(staff, filterCombineParcels, TradeCombineParcel.STATUS_OUTBOUNDED);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createEmptyCombineParcel(Staff staff, TradeCombineParcel param) {
        if (StringUtils.isBlank(param.getSource())
                || param.getConsignWarehouseId() == null
                || StringUtils.isBlank(param.getConsignWarehouseName())
                || param.getTaobaoId() == null
                || param.getGatherType() == null) {

            throw new RuntimeException("缺少参数");

        }
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        combineParcel.setId(IdWorkerFactory.getIdWorker().nextId());
        combineParcel.setTaobaoId(param.getTaobaoId());
        combineParcel.setSource(param.getSource());
        combineParcel.setConsignWarehouseId(param.getConsignWarehouseId());
        combineParcel.setConsignWarehouseName(param.getConsignWarehouseName());
        combineParcel.setGatherType(param.getGatherType());
        combineParcel.setTemplateId(param.getTemplateId());
        combineParcel.setTransferWarehouseId(param.getTransferWarehouseId());
        combineParcel.setTransferWarehouseName(param.getTransferWarehouseName());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
        tradeCombineParcelDAO.insert(staff, combineParcel);
    }

    @Override
    public TradeParcels listTrade(Staff staff, TradeListRequest request, Page page) {
        List<TradeExt> updateExtList = new ArrayList<>();

        if (StringUtils.isNotBlank(request.getShippingCarrier())) {
            request.setShippingCarriers(request.getShippingCarrier().split(","));
        } else {
            if (CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(request.getSource())) {
                request.setShippingCarriers(new String[]{EnumShopeeTransitWarehouse.LGSFM40.getWarehouseId(),EnumShopeeTransitWarehouse.LGSFM41.getWarehouseId(),EnumShopeeTransitWarehouse.LGSFM48.getWarehouseId()});
            }
        }
        List<Long> userList = staff.getUsers().stream().filter(e -> Objects.equals(e.getSource(), request.getSource())).map(User::getId).collect(Collectors.toList());
        request.setUserList(userList);
        Map<Long, UserLogisticsChannel> channelName = new HashMap<>();
        if (CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(request.getSource())) {
            List<UserLogisticsChannel> userLogisticsChannel = logisticsProviderService.queryChannelByProviderType(staff, EnumLogisticsProviderType.ALIEXPRESS.getValue());
            if (CollectionUtils.isNotEmpty(userLogisticsChannel)) {
                channelName = userLogisticsChannel.stream().collect(Collectors.toMap(UserLogisticsChannel::getTemplateId, channel -> channel));
            }
        }
        if (CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(request.getSource())) {
            request.setReceiverCountry(Arrays.asList("SG", "MY", "TH", "PH", "VN"));
        }
        TradeParcels trades = new TradeParcels();
        List<TradeParcel> list = tradeParcelDAO.getTradeFromShopee(staff, request, page);
        if (CollectionUtils.isEmpty(list)) {
            trades.setTotal(0L);
            trades.setPage(page);
            return trades;
        }
        Map<Long, Shop> shopMap = getShopMap(staff);
        if (MapUtils.isNotEmpty(shopMap)) {
            for (TradeParcel tr : list) {
                CombineParceUtils.convertWarehouseName(tr, channelName);
                if (tr.getTaobaoId() == null) {
                    continue;
                }
                Shop shop = shopMap.get(tr.getTaobaoId());
                if (shop == null) {
                    continue;
                }
                tr.setShopName(StringUtils.isEmpty(shop.getShortTitle()) ? shop.getTitle() : shop.getShortTitle());
                if (CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(tr.getSource()) && StringUtils.isEmpty(tr.getTransferWarehouseName())) {
                    List<OutSidPool> outSidPools = outSidPoolService.queryBySids(staff, Collections.singletonList(tr.getSid())).stream().filter(e -> StringUtils.isNotBlank(e.getShippingOptionService())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(outSidPools)) {
                        OutSidPool outSidPool = outSidPools.get(0);
                        JSONObject logistics = Optional.ofNullable(JSON.parseObject(outSidPool.getShippingOptionService())).orElse(new JSONObject()).getJSONObject("logistics");
                        if (StringUtils.isEmpty(logistics.getString("warehouse_id"))) {
                            continue;
                        }
                        TradeExt tradeExt = new TradeExt();
                        tradeExt.setSid(tr.getSid());
                        tradeExt.setLogisticId(logistics.getLong("logistic_id"));
                        tradeExt.setTransitWarehouseId(logistics.getString("warehouse_id"));
                        updateExtList.add(tradeExt);
                    }
                }
                if (CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(tr.getSource())) {
                    tr.setShippingCarrier("Tik Tok线上发货");
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateExtList)) {
            tradeExtDao.batchUpdate(staff, updateExtList);
        }
        trades.setList(list);
        trades.setTotal(tradeParcelDAO.getTradeFromShopeeCount(staff, request));
        trades.setPage(page);
        return trades;

    }

    @Override
    public void addParcelToCombineAndSplit(Staff staff, TradeGroupParcelRequest request) {
        Assert.hasText(request.getConsignWarehouseId(), "系统仓库不能为空");
        Assert.hasText(request.getSource(), "平台不能为空");
        if (!CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(request.getSource()) && !CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(request.getSource())) {
            Assert.hasText(request.getTransferWarehouseName(), "中转仓不能为空");
        }
        Assert.isTrue(null != request.getPackageNum() && 0 != request.getPackageNum(), "生成大包数量不能为空");
        List<Long> sids = tradeParcelDAO.getTradeSidByCombine(staff, request);
        if (CollectionUtils.isEmpty(sids)) {
            throw new RuntimeException("没有能移入组包的数据！");
        }
        if (Objects.equals(request.getAppointmentType(), 0) && sids.size() > EnumCombineParcelNum.SMT_BIGBAG.getMaxNum()) {
            throw new RuntimeException("大包约揽方式每次移入组包不能超过：" + EnumCombineParcelNum.SMT_BIGBAG.getMaxNum());
        }
        if (Objects.equals(request.getAppointmentType(), 1) && sids.size() > EnumCombineParcelNum.SMT_BATCH.getMaxNum()) {
            throw new RuntimeException("批次约揽方式每次移入组包不能超过：" + EnumCombineParcelNum.SMT_BATCH.getMaxNum());
        }
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, sids.toArray(new Long[0]));

        if (CollectionUtils.isEmpty(trades)) {
            throw new RuntimeException("查询trades为空");
        }
        tradeExtBusiness.fillTradeExt(staff, trades);
        Map<String, List<Trade>> tradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getSource));
        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            CombineParcelFactory.getInvokeStrategy(entry.getKey()).addParcelToCombineAndSplit(staff, entry.getValue(), request);
        }
    }

    @Override
    public TradeCombineParcel combineParcelDetails(Staff staff, TradeParcelRequest request) {
        if (request.getCombineParcelId() == null) {
            throw new RuntimeException("大包单号不能为空");
        }
        Long[] ids = new Long[]{request.getCombineParcelId()};
        List<TradeCombineParcel> combineParcelList = tradeCombineParcelDAO.queryByIds(staff, ids);
        if (CollectionUtils.isEmpty(combineParcelList)) {
            throw new RuntimeException("查询大包信息为空");
        }

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, ids, null);
        //查询小包列表，方便统计数量和重量
        Map<Long, List<TradeParcel>> tradeMap = new HashMap<>();
        for (TradeParcel tradeParcel : tradeParcels) {
            tradeMap.computeIfAbsent(tradeParcel.getCombineParcelId(), k -> new ArrayList<>()).add(tradeParcel);
        }

        for (TradeCombineParcel combineParcel : combineParcelList) {
            if (combineParcel.getTaobaoId() == null) {
                continue;
            }
            combineParcel.setMaxParcelNum(EnumCombineParcelNum.getCombineParcelNum(combineParcel.getSource()));
            if (StringUtils.isBlank(combineParcel.getTransferWarehouseName())) {
                combineParcel.setTransferWarehouseName(EnumShopeeTransitWarehouse.getWarehouseNameCNById(combineParcel.getTransferWarehouseId(), combineParcel.getTransferWarehouseAddress()));
            }
            List<TradeParcel> parcelList = tradeMap.get(combineParcel.getId());
            combineParcel.setParcelNum(CollectionUtils.isEmpty(parcelList) ? 0 : parcelList.size());
            combineParcel.setWeight(CollectionUtils.isEmpty(parcelList) ? 0 : formatDigit(parcelList.stream().mapToDouble(TradeParcel::getWeight).sum(), 3));
            //计算大包状态
            combineParcel.setPlatformStatus("");
            getCombineParcelStatus(combineParcel, parcelList);
            combineParcel.setSourceName(CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(combineParcel.getSource()) ? "速卖通" : combineParcel.getSource());
            if (CommonConstants.PLAT_FORM_TYPE_SMT.equals(combineParcel.getSource())) {
                List<SubbagDetailVo> subbagDetailVoList = JSON.parseArray(combineParcel.getExtraFields(), SubbagDetailVo.class);
                combineParcel.setSubbagDetailList(subbagDetailVoList);
                combineParcel.setPlatformStatus(sumaitongCombineParcelService.handoverContentQuery(staff, combineParcel));
            }
        }
        return combineParcelList.get(0);
    }


    @Override
    public void batchUpdate(Staff staff, List<TradeCombineParcel> combineParcel) {
        tradeCombineParcelDAO.batchUpdate(staff, combineParcel);
    }

    @Override
    /**
     * 构建组合订单
     *
     * @param staff          员工信息
     * @param combineParcel  包裹信息
     * @param request        请求参数
     * @return 构建的订单对象
     */
    public Trade buildCombineTrade(Staff staff, TradeCombineParcel combineParcel, TradeCombineParcelRequest request) {
        TbTrade trade = initializeTrade(staff, combineParcel, request);
        // 判断组合包裹类型是否为出库状态
        if (isLazadaPlatform(combineParcel)) {
            buildLazadaOrder(combineParcel, trade);
            //兼容线下组包
        } else if (isSmtPlatform(combineParcel) || TradeCombineParcel.STATUS_TO_OUTBOUND == combineParcel.getCombineParcelType()) {
            buildSmtOrder(staff, combineParcel, request, trade);
        } else if (isTiktokPlatform(combineParcel)) {
            buildTiktokOrder(combineParcel, request, trade);
        } else {
            buildOtherOrder(combineParcel, request, trade);
        }

        return trade;
    }

    /**
     * 初始化订单的基础信息
     */
    private TbTrade initializeTrade(Staff staff, TradeCombineParcel combineParcel, TradeCombineParcelRequest request) {
        TbTrade trade = new TbTrade();
        User user = userService.queryByTaobaoId(staff.getCompanyId(), request.getTaobaoId());
        trade.setSid(combineParcel.getId());
        trade.setOutSid(combineParcel.getTrackingNo());
        trade.setPayment("0.1"); // 设置订单金额默认值
        trade.setTaobaoId(request.getTaobaoId());
        trade.setUserId(user.getId());
        trade.setWarehouseId(combineParcel.getConsignWarehouseId());
        trade.setTemplateId(request.getTemplateId());
        trade.setTid(String.valueOf(combineParcel.getId()));

        return trade;
    }

    /**
     * 是否为出库状态
     */
    private boolean isToOutbound(TradeCombineParcel combineParcel) {
        return TradeCombineParcel.STATUS_TO_OUTBOUND == combineParcel.getCombineParcelType();
    }

    /**
     * 是否为Lazada平台订单
     */
    private boolean isLazadaPlatform(TradeCombineParcel combineParcel) {
        return Objects.equals(CommonConstants.PLAT_FORM_TYPE_LAZADA, combineParcel.getSource());
    }

    /**
     * 是否为SMT平台订单
     */
    private boolean isSmtPlatform(TradeCombineParcel combineParcel) {
        return Objects.equals(CommonConstants.PLAT_FORM_TYPE_SMT, combineParcel.getSource());
    }

    private boolean isTiktokPlatform(TradeCombineParcel combineParcel) {
        return Objects.equals(CommonConstants.PLAT_FORM_TYPE_TIKTOK, combineParcel.getSource());
    }
    private boolean isShopeePlatform(TradeCombineParcel combineParcel) {
        return Objects.equals(CommonConstants.PLAT_FORM_TYPE_SHOPEE, combineParcel.getSource());
    }
    /**
     * 构建Shopee订单
     */
    private void buildLazadaOrder(TradeCombineParcel combineParcel, TbTrade trade) {
        if (StringUtils.isEmpty(combineParcel.getTransferWarehouseId())) {
            throw new RuntimeException("中转仓为空");
        }
        biuldOrder(combineParcel, trade);
        switch (combineParcel.getTransferWarehouseId()) {
            case "LGS-FM40":
                setReceiverInfo(trade, "叶智勇", "15986832040", "广东省", "东莞市", "沙田镇",
                        "沿河路281号第一产业集团东莞沙田物流园3号库9号垛口");
                break;
            case "LGS-FM41":
                setReceiverInfo(trade, "金龙哥", "15857945451", "浙江省", "东莞市", "金华市",
                        "义乌市稠江街道四海大道盛辉物流园区 分拨中心二楼2-10号");
                break;
            case "LGS-FM48":
                setReceiverInfo(trade, "4PX到货组-LZD", "18759512835", "福建省", "泉州市", "常平镇",
                        "鲤城区常泰街道新塘社区常泰路598号一楼物流中心（东南医药集团内-递四方）");
                break;
            default:
                throw new RuntimeException("未知的中转仓ID");
        }
    }

    /**
     * 构建SMT订单
     */
    private void buildSmtOrder(Staff staff, TradeCombineParcel combineParcel, TradeCombineParcelRequest request, TbTrade trade) {
        if (StringUtils.isEmpty(combineParcel.getTransferWarehouseId())) {
            throw new RuntimeException("收件人地址信息为空");
        }

        AbroadAddress abroadAddress = abroadAddressService.queryById(staff, Long.valueOf(combineParcel.getTransferWarehouseId()));
        if (abroadAddress == null) {
            throw new RuntimeException("收件人地址信息为空");
        }

        setReceiverInfo(trade, abroadAddress.getContactName(), StringUtils.isEmpty(abroadAddress.getPhone()) ? abroadAddress.getMobilePhone() : abroadAddress.getPhone(),
                abroadAddress.getProvince(), abroadAddress.getCity(),
                abroadAddress.getCountry(), abroadAddress.getAddr());

        if (!Objects.equals("print", request.getSource())) {
            biuldOrder(combineParcel, trade);
        }
    }
    private void buildTiktokOrder(TradeCombineParcel combineParcel, TradeCombineParcelRequest request, TbTrade trade) {
        biuldOrder(combineParcel, trade);
        if (StringUtils.isEmpty(request.getDropOffCode()) && StringUtils.isNotBlank(combineParcel.getTrackingNo())) {
            return;
        }
        if (StringUtils.isEmpty(request.getDropOffCode())) {
            throw new RuntimeException("收件人地址dropOffCode信息为空");
        }
        switch (request.getDropOffCode()) {
            case "TTDG":
                setReceiverInfo(trade, "TT 东莞自建分拨中心", "18098201825", "广东省", "东莞市", "洪梅镇",
                        "望沙路115号易商物流园");
                break;
            case "WSSH":
                setReceiverInfo(trade, "万色上海分拨中心", "4008206207", "上海市", "上海市", "宝山区",
                        "上海市宝山区共悦路419号");
                break;
            case "WSYW":
                setReceiverInfo(trade, "万色义乌分拨中心", "4008206207", "浙江省", "义乌市", "富通大道",
                        "富通大道与祥瑞路交又口义乌圆通妈妈商贸二号仓库二楼C单元");
                break;
            case "WSQZ":
                setReceiverInfo(trade, "万色泉州分拨中心", "4008206207", "福建省", "泉州市", "磁灶镇",
                        "张林东环路 999 号豪川运派物流园C区");
                break;
            case "WSDG":
                setReceiverInfo(trade, "万色东莞分拨中心", "4008206207", "广东省", "东莞市", "黄江镇",
                        "黄江镇田星路 39 号巨卓产业园");
                break;
            case "SFJHAM":
                setReceiverInfo(trade, "顺丰金华(金东）", "15660972614", "浙江省", "金华市", "金东区",
                        "兴盛街 855 号(丰泰产业园-金义)B103 分");
                break;
            case "SFJHPM":
                setReceiverInfo(trade, "顺丰金华(金东）", "18034483844", "浙江省", "金华市", "金东区",
                        "兴盛街 855 号(丰泰产业园-金义)B103 分");
                break;
            case "SFJHEM":
                setReceiverInfo(trade, "顺丰金华(金东）", "13959528775", "浙江省", "金华市", "金东区",
                        "兴盛街 855 号(丰泰产业园-金义)B103 分");
                break;
            default:
                throw new RuntimeException("未知的中转仓code" + request.getDropOffCode());
        }
    }

    /**
     * 构建其他平台订单
     */
    private void buildOtherOrder(TradeCombineParcel combineParcel, TradeCombineParcelRequest request, TbTrade trade) {
        biuldOrder(combineParcel, trade);
        if (isShopeePlatform(combineParcel)) {
            if (StringUtils.isEmpty(combineParcel.getTransferWarehouseId())) {
                throw new RuntimeException("中转仓为空");
            }
            if (request.getTaobaoId() == null) {
                throw new RuntimeException("店铺不能为空");
            }
            setOtherReceiverInfo(combineParcel, trade);
        }
    }

    /**
     * 设置收件人信息
     */
    private void setReceiverInfo(TbTrade trade, String name, String mobile, String state, String city, String district, String address) {
        trade.setReceiverName(name);
        trade.setReceiverMobile(mobile);
        trade.setReceiverState(state);
        trade.setReceiverCity(city);
        trade.setReceiverDistrict(district);
        trade.setReceiverAddress(address);
    }

    /**
     * 根据仓库设置收件人信息
     */
    private void setOtherReceiverInfo(TradeCombineParcel combineParcel, TbTrade trade) {
        switch (combineParcel.getTransferWarehouseId()) {
            case "TWS01":
                setReceiverInfo(trade, "李灿", "02160562952", "广东省", "东莞市", "常平镇", "麦元村犀牛一街神鹰仓库一楼");
                break;
            case "ECP04":
            case "ECP01_QUANZHOU":
                setReceiverInfo(trade, "邓凯", "4001268888-3", "浙江省", "义乌市", "", "四海大道1666号义乌公路港2期26栋西南侧");
                break;
            case "ECP03":
                setReceiverInfo(trade, "马超", "19105959566", "福建省", "泉州市", "晋江市", "磁灶镇 张林东路999号豪川运派物流园2号库C区万色速递");
                break;
            case "ECP01_SHANGHAI":
                setReceiverInfo(trade, "万色速递", "4008-206-207", "上海市", "上海市", "宝山区", "共悦路419号万色");
                break;
            case "ECP01_YIWU":
                setReceiverInfo(trade, "马超", "13822245042", "福建省", "泉州市", "", "晋江市磁灶镇张林东路999号豪川运派物流园2号库C区");
                break;
            default:
                throw new RuntimeException("未知的中转仓ID");
        }
    }

    /**
     * 根据地址设置ECP01仓库的收件人信息
     */
    private void setReceiverInfoForECP01(TradeCombineParcel combineParcel, TbTrade trade) {
        if (combineParcel.getTransferWarehouseAddress().contains("Shanghai")) {
            setReceiverInfo(trade, "万色速递", "4008-206-207", "上海市", "上海市", "宝山区", "共悦路419号万色");
        } else if (combineParcel.getTransferWarehouseAddress().contains("Yiwu")) {
            setReceiverInfo(trade, "邓凯", "4001268888-3", "浙江省", "义乌市", "", "四海大道1666号义乌公路港2期26栋西南侧");
        } else if (combineParcel.getTransferWarehouseAddress().contains("Quanzhou")) {
            setReceiverInfo(trade, "马超", "13822245042", "福建省", "泉州市", "", "晋江市磁灶镇张林东路999号豪川运派物流园2号库C区");
        }
    }


    private void biuldOrder(TradeCombineParcel combineParcel, TbTrade trade) {
        TbOrder order = new TbOrder();
        order.setTid(String.valueOf(combineParcel.getId()));
        order.setTitle("线下组包订单");
        order.setNum(1);
        order.setNumIid(String.valueOf(combineParcel.getId()));
        order.setNetWeight(0.1D);
        order.setItemSysId(-1L);
        order.setSkuSysId(-1L);
        order.setOldItemSysId(-1L);
        order.setId(-1L);
        order.setOldNum(1);
        trade.setOrders(Collections.singletonList(order));
    }

    @Override
    public void addCombineParcel(Staff staff, TradeCombineParcel param) {
        List<TradeCombineParcel> combineParcels = tradeCombineParcelDAO.queryByIds(staff, new Long[]{param.getId()});
        if (CollectionUtils.isEmpty(combineParcels)) {
            throw new RuntimeException("大包不存在，请检查参数是否正确");
        }
        TradeCombineParcel tradeCombineParcel = combineParcels.get(0);
        if (StringUtils.isEmpty(tradeCombineParcel.getPlatformBatchNo())) {
            throw new RuntimeException("大包揽货批次号不存在，无法添加小包，请先上传大包");
        }
        sumaitongCombineParcelService.addCombineParcel(staff, tradeCombineParcel, param);

    }

    @Override
    public List<TradeCombineParcel> queryByIds(Staff staff, Long[] sidArray) {
        return tradeCombineParcelDAO.queryByIds(staff, sidArray);
    }
}

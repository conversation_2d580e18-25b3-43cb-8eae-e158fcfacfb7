package com.raycloud.dmj.services.trades.support.sort.impl;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.sort.SortContext;
import com.raycloud.dmj.domain.sort.enums.SortTypeEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trades.support.sort.ITradeSortService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther mengfanguang
 * @Date 2023/11/2
 * @desc 现货订单按付款时间先排序，预售订单再按计划发货时间排序
 */
@Service
public class TradeSortPayTimeBeforeService implements ITradeSortService {
    @Override
    public SortTypeEnum getSortTypeEnum() {
        return SortTypeEnum.AUDIT_ORDER_BEFORE_PAY_TIME_AFTER_TIMEOUT_ACTION_TIME;
    }

    @Override
    public void sort(Staff staff, SortContext sortContext) {
        List<List<Trade>> sortedTrades = new ArrayList<>();
        if (CollectionUtils.isEmpty(sortContext.getNeedSortTrades())) {
            return;
        }
        for (List<Trade> subTrades : sortContext.getNeedSortTrades()) {
            //现货订单按付款时间先排序，预售订单再按计划发货时间排序
            List<Trade> normalTrades = new ArrayList<>();
            List<Trade> preSellTrades = new ArrayList<>();
            for (Trade trade : subTrades) {
                if (0 == trade.getIsPresell()) {
                    normalTrades.add(trade);
                }
                if (2 == trade.getIsPresell()) {
                    preSellTrades.add(trade);
                }
            }

            if (CollectionUtils.isNotEmpty(normalTrades)) {
                sortedTrades.add(normalTrades.stream().sorted(Comparator.comparing(Trade::getPayTime)).collect(Collectors.toList()));
            }

            if (CollectionUtils.isNotEmpty(preSellTrades)) {
                sortedTrades.add(preSellTrades.stream().sorted(Comparator.comparing(Trade::getTimeoutActionTime)).collect(Collectors.toList()));
            }
        }
        sortContext.setNeedSortTrades(sortedTrades);
    }
}
package com.raycloud.dmj.services.trades.support;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.dao.trade.TradeWarehouseConfDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.trades.TradeWarehouseConf;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.trades.ITradeWarehouseConfService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TradeWarehouseConfService implements ITradeWarehouseConfService {

	@Resource
	private IWarehouseService warehouseService;
	
	@Resource
	private TradeWarehouseConfDao tradeWarehouseConfDao;
	
	@Resource
	private IShopService shopService;
	
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Override
	@Cacheable(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_' + #open")
	public List<TradeWarehouseConf> queryAll(Staff staff, Integer open) {
		return tradeWarehouseConfDao.queryByCompanyId(staff, open);
	}

	@Override
	@Caching(evict={
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_null'"),
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_true'"),
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_false'")
	})
	public void save(Staff staff, TradeWarehouseConf conf) {
		if(null == conf.getId()){
			insert(staff, conf);
			return ;
		}
		
		update(staff, conf);
	}
	
	void insert(Staff staff, TradeWarehouseConf conf){
		conf.setShopNames(getShopNames(staff, conf));
		tradeWarehouseConfDao.insert(staff, conf);
		if(logger.isDebugEnabled()){
			logger.debug(LogHelper.buildLogHead(staff).append("新增了一条trade_warehouse_conf配置:").append(JSON.toJSONString(conf)));
		}
	}
	
	void update(Staff staff, TradeWarehouseConf conf){
		conf.setShopNames(getShopNames(staff, conf));
		tradeWarehouseConfDao.update(staff, conf);
		if(logger.isDebugEnabled()){
			logger.debug(LogHelper.buildLogHead(staff).append("更新了一条trade_warehouse_conf配置:").append(JSON.toJSONString(conf)));
		}
	}
	
	/**
	 * 通过userIds获取对应的店铺名称
	 * @param staff
	 * @param conf
	 * @return
	 */
	String getShopNames(Staff staff, TradeWarehouseConf conf){
		if(StringUtils.isEmpty(conf.getUserIds()))
			return null;
		
		Long[] userIds = ArrayUtils.toLongArray(conf.getUserIds());
		if(userIds.length == 0)
			return null;
		
		List<Shop> shops = shopService.queryByCompanyId(staff);
		Map<Long, Shop> shopMap = new HashMap<Long, Shop>(shops.size(), 1);
		for(Shop shop : shops){
			shopMap.put(shop.getUserId(), shop);
		}
		
		String[] shopNames = new String[userIds.length];
		int index = 0;
		for(Long userId : userIds){
			if(!shopMap.containsKey(userId))
				throw new IllegalArgumentException("此用户店铺不存在:" + userId);
			
			shopNames[index++] = shopMap.get(userId).getTitle();
		}
		return StringUtils.join(shopNames, ',');
	}

	@Override
	@Caching(evict={
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_null'"),
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_true'"),
			@CacheEvict(value="defaultCache#600", key="'trade_warehouse_conf_' + #staff.companyId + '_false'")
	})
	public void delete(Staff staff, TradeWarehouseConf conf) {
		tradeWarehouseConfDao.delete(staff, conf);
		if(logger.isDebugEnabled()){
			logger.debug(LogHelper.buildLogHead(staff).append("删除了trade_warehouse_conf:").append(conf.getId()));
		}
	}

	@Override
	public Warehouse getDefaultWarehouse(Staff staff) {
		return warehouseService.queryDefault(staff.getCompanyId());
	}

}

package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.request.QueryPureItemByParamsRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryPureItemByParamsResponse;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *   可以直接转换为 item_sys_id skU_sys_id in 语句的相关条件 如果这里已经有拼接 那么就可以不用再走热点商品表初筛
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-20
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_SYS_ID)
public class SysIdConverter extends AbsOrderConditionConverter{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return notEmpty(condition.getMainOuterIds()) || notEmpty(condition.getSysItemIds())  || notEmpty(condition.getSysSkuIds()) ;
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        Collection<Long> sysItemIds = new ArrayList<>();
        if (notEmpty(condition.getSysItemIds())) {
            sysItemIds.addAll(Arrays.asList(condition.getSysItemIds()));
        }
        if (notEmpty(condition.getMainOuterIds())) {
            Collection<Long> refItemIds = fillSysItemIdByMainOuterId(staff, Arrays.asList(condition.getMainOuterIds()));
            if (CollectionUtils.isEmpty(refItemIds)) {
                orderQry.setStopReason("主商家编码无对应商品");
                orderQry.setStopQuery(true);
                return false;
            }

            if (notEmpty(condition.getSysItemIds())) {
                Collection<Long> intersection = intersection(condition.getSysItemIds(), refItemIds.toArray(new Long[0]));
                if (intersection.isEmpty()) {
                    orderQry.setStopReason("主商家编码与指定商品Id无交集");
                    orderQry.setStopQuery(true);
                    return false;
                }else {
                    sysItemIds = intersection;
                }
            }else{
                sysItemIds = refItemIds;
            }
        }

        andListCondition(hotQry,"o.item_sys_id",sysItemIds);
        andListCondition(orderQry,"o.item_sys_id",sysItemIds);

        andListCondition(orderQry,"o.sku_sys_id",condition.getSysSkuIds());
        andListCondition(hotQry,"o.sku_sys_id",condition.getSysSkuIds());

        //有系统Id了可以直接走索引 没必要再查热点数据表
        if (sysItemIds.size() < 5000) {
            hotQry.setStopQuery(true);
        }
        return true;
    }



    public List<Long> fillSysItemIdByMainOuterId(Staff staff, List<String> mainOuterIds) {
        List<Long> sysItemIds = new ArrayList<>();
        QueryPureItemByParamsRequest request = new QueryPureItemByParamsRequest();
        request.setOuterIds(mainOuterIds);
        request.setPageNo(1);
        request.setPageSize(100);
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        try {
            QueryPureItemByParamsResponse response = dmjItemCommonSearchApi.queryPureItem(request);
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getList())) {
                new QueryLogBuilder(staff).append("主商家编码",mainOuterIds).append("根据商品的纯商品接口返回").appendJSon(response).printDebug(logger);
                return Collections.emptyList();
            }

            List<DmjItem> list = response.getData().getList();
            for (DmjItem dmjItem : list) {
                sysItemIds.add(dmjItem.getSysItemId());
            }
        } catch (Exception e) {
            new QueryLogBuilder(staff).append("主商家编码",mainOuterIds).append("根据主商家编码查询商品出错").printError(logger,e);
            return Collections.emptyList();
        }
        return sysItemIds;
    }
}

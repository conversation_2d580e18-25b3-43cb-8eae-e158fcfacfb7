package com.raycloud.dmj.services.backend;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;

import java.util.HashSet;
import java.util.Set;

/**
 * 公司处理工具类
 * Created by guzy on 16/6/1.
 */
public interface CompanyDealOp<T> {

    T deal(Staff staff);

    default String getBreakKey(){
        return null;
    }

    default Page getPage(){
        return new Page().setPageNo(1).setPageSize(20);
    }

    default Set<Long> getExcludeCompanyId(){
        return new HashSet<>();
    }
}

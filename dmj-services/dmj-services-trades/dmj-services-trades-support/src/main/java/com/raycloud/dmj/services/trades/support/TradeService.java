package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.*;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.item.*;
import com.raycloud.dmj.business.item.match.ItemRelationChangeHandlerBusiness;
import com.raycloud.dmj.business.logistics.*;
import com.raycloud.dmj.business.modify.TradeSuiteUpdateBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.ConsignParams;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.model.UserExpressWlbModel;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.consign.*;
import com.raycloud.dmj.domain.trades.request.TradeUpdateTimeoutActionTimeRequest;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.rematch.api.IReMatchService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformTradeAccess;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.pt.smart_match.IExpressPercentageMatchService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.except.TradeExceptAdapter;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.deliverylimit.*;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.tb.logistics.TbLogisticsAccess;
import com.raycloud.dmj.tb.trade.TbTradeAccess;
import com.raycloud.ec.api.*;
import com.taobao.api.domain.Shipping;
import com.taobao.api.response.*;
import lombok.Data;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.*;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.*;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 交易相关操作业务接口实现
 *
 * <AUTHOR> CXW
 */
@Service
public class TradeService implements ITradeService {

    private static final Logger logger = Logger.getLogger(TradeService.class);

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TbTradeAccess tbTradeAccess;
    @Resource
    TbLogisticsAccess tbLogisticsAccess;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeFillService tradeFillService;

    @Resource
    LogisticsOfflineBusiness logisticsOfflineBusiness;
    @Resource
    LogisticsOutBusiness logisticsOutBusiness;
    @Resource
    LogisticsReUploadBusiness logisticsReUploadBusiness;
    @Resource
    LogisticsUploadCancelBusiness logisticsUploadCancelBusiness;
    @Resource
    LogisticsResendBusiness logisticsResendBusiness;
    @Resource
    LogisticsOfflineAllBusiness logisticsOfflineAllBusiness;

    @Resource
    CancelInsufficientBusiness cancelInsufficientBusiness;
    @Resource
    ExpressTemplateMatchBusiness expressTemplateMatchBusiness;
    @Resource
    IExpressPercentageMatchService expressPercentageMatchService;

    @Resource
    ITradeAuditService tradeAuditService;

    @Resource
    ConsignCancelBusiness consignCancelBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradePtService tradePtService;

    @Resource
    AddressBusiness addressBusiness;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    ItemMatchByRelationBusiness itemMatchByRelationBusiness;
    @Resource
    ItemMatchByOuterIdBusiness itemMatchByOuterIdBusiness;
    @Resource
    TradeSuiteUpdateBusiness tradeSuiteUpdateBusiness;
    @Resource
    ILockService lockService;
    @Resource
    UploadBusiness uploadBusiness;
    @Resource
    IConsignUploadService consignUploadService;
    @Resource
    DeliveryLimitBusiness deliveryLimitBusiness;
    @Resource
    IExpressTemplateDubboService expressTemplateDubboService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ItemConvertMatchByRelationBusiness itemConvertMatchByRelationBusiness;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    PlatformManagement platformManagement;

    @Resource
    IUserService userService;
    @Resource
    private TradeExceptAdapter tradeExceptAdapter;
    @Resource
    private ItemRelationChangeHandlerBusiness itemRelationChangeHandlerBusiness;

    @Resource
    FeatureService featureService;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    IReMatchService reMatchBusiness;


    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel) {
        if (exceptionConsign == null) {
            exceptionConsign = false;
        }
        if (consignType != null && consignType.startsWith(TradeConstants.TYPE_TRADE_OUT)) {
            return logisticsOutBusiness.consign(staff, sids);
        } else {
            SendType type = ConsignUtils.getSendType(consignType);
            Assert.isTrue(type != null, "糟糕，暂不支持的发货类型:" + consignType);
            ConsignParams params = ConsignParams.builder()
                    .sids(sids)
                    .consignType(type)
                    .clientIp(clientIp)
                    .exceptionConsign(exceptionConsign)
                    .dymmyType(dymmyType)
                    .noLogisticsName(noLogisticsName)
                    .noLogisticsTel(noLogisticsTel)
                    .build();
            return logisticsOfflineBusiness.consign(staff, params);
        }
    }

    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel, String operateType) {
        if (exceptionConsign == null) {
            exceptionConsign = false;
        }
        SendType type = ConsignUtils.getSendType(consignType);
        Assert.isTrue(type != null, "糟糕，暂不支持的发货类型:" + consignType);
        ConsignParams params = ConsignParams.builder()
                .sids(sids)
                .consignType(type)
                .clientIp(clientIp)
                .exceptionConsign(exceptionConsign)
                .dymmyType(dymmyType)
                .noLogisticsName(noLogisticsName)
                .noLogisticsTel(noLogisticsTel)
                .operateType(operateType)
                .build();
        return logisticsOfflineBusiness.consign(staff, params);
    }

    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel, boolean isFilterPartyWarehouse) {
        if (exceptionConsign == null) {
            exceptionConsign = false;
        }
        if (consignType != null && consignType.startsWith(TradeConstants.TYPE_TRADE_OUT)) {
            return logisticsOutBusiness.consign(staff, sids);
        } else {
            SendType type = ConsignUtils.getSendType(consignType);
            Assert.isTrue(type != null, "糟糕，暂不支持的发货类型:" + consignType);
            ConsignParams params = ConsignParams.builder()
                    .sids(sids)
                    .consignType(type)
                    .clientIp(clientIp)
                    .exceptionConsign(exceptionConsign)
                    .dymmyType(dymmyType)
                    .noLogisticsName(noLogisticsName)
                    .noLogisticsTel(noLogisticsTel)
                    .isFilterPartyWarehouse(isFilterPartyWarehouse)
                    .build();
            return logisticsOfflineBusiness.consign(staff, params);
        }
    }

    @Override
    public List<ConsignRecord> consignBTAS(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel, boolean isFilterPartyWarehouse, TradeCombineParcel parcel) {
        if (exceptionConsign == null) {
            exceptionConsign = false;
        }
        SendType type = ConsignUtils.getSendType(consignType);
        if (SendType.BTAS_COMBINE_PARCEL != type){
            throw new IllegalArgumentException("糟糕，暂不支持的发货类型:" + consignType);
        }
        ConsignParams params = ConsignParams.builder()
                .sids(sids)
                .consignType(type)
                .clientIp(clientIp)
                .exceptionConsign(exceptionConsign)
                .dymmyType(dymmyType)
                .noLogisticsName(noLogisticsName)
                .noLogisticsTel(noLogisticsTel)
                .isFilterPartyWarehouse(isFilterPartyWarehouse)
                .parcel(parcel)
                .build();
        return logisticsOfflineBusiness.consign(staff, params);
    }

    @Override
    public List<ConsignRecord> consignStall(Staff staff, Long[] sids) {
        List<Long> consignSids = new ArrayList<>();
        List<TbTrade> trades = tbTradeDao.queryBySids(staff, sids);
        if (trades != null && trades.size() > 0) {
            trades.forEach(trade -> {
                if (containsSelfPickTag(trade) && notContainsSaleTradePreAllocateGoodsTag(trade)) {
                    consignSids.add(trade.getSid());
                } else {
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("非现场自提的订单直接忽略，sid=%s", trade.getSid())));
                }
            });
        }
        return consignSids.size() > 0 ? consign(staff, consignSids.toArray(new Long[0]), null, null, null, null, null, null) : Collections.EMPTY_LIST;
    }

    private boolean notContainsSaleTradePreAllocateGoodsTag(TbTrade trade){
        return !containsSaleTradePreAllocateGoodsTag(trade);
    }

    private boolean containsSaleTradePreAllocateGoodsTag(TbTrade trade) {
        return StringUtils.indexOf(trade.getTagIds(), SystemTags.TAG_SALE_TRDE_PRE_ALLOCATE_GOODS.getId() + "") >= 0;
    }

    private boolean containsSelfPickTag(TbTrade trade) {
        return StringUtils.indexOf(trade.getTagIds(), SystemTags.TAG_SELF_PICK.getId() + "") >= 0;
    }


    @Override
    public List<ConsignRecord> consignUpload(Staff staff, Long[] sids, String consignType, String clientIp, boolean process,Integer isGx) {
        if(!TradeConsignConfigDiamondUtils.getFinishUploadCompanyIds().isEmpty()&&TradeConsignConfigDiamondUtils.getFinishUploadCompanyIds().contains(staff.getCompanyId())){
            throw new IllegalArgumentException("该公司预发货已被拦截！");
        }
        SendType type = null;
        String errorDesc = null;
        if (consignType == null) {
            type = SendType.UPLOAD;
        } else {
            if(consignType.startsWith(TradeConstants.TYPE_TRADE_OUT)){
                errorDesc = "出库单暂时不支持直接发货上传";
            }
            type = ConsignUtils.getSendType(consignType);
            if(type == null){
                errorDesc = "暂不支持的发货类型:" + consignType;
            }
            if(StringUtils.isNotBlank(errorDesc)){
                List<ConsignRecord> consignRecords = new ArrayList<>();
                for(Long sid : sids){
                    consignRecords.add(ConsignUtils.buildSimpleRecord(sid,null,1,errorDesc));
                }
                return consignRecords;
            }
        }
        Assert.isTrue(type != null, "糟糕，暂不支持的发货类型:" + consignType);
        return consignUpload(staff, sids, type, clientIp, process,isGx,null);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED) // 内层方法没有事务
    public List<ConsignRecord> consignUploadNotSupported(Staff staff, Long[] sids, String consignType, String clientIp, boolean process, Integer isGx) {
        boolean isActualTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        logger.info("consignUploadNotSupported方法当前是否有事务：" + isActualTransactionActive);
        return consignUpload(staff, sids, consignType, clientIp, process, isGx);
    }


    @Override
    public List<ConsignRecord> consignUpload(Staff staff, Long[] sids, SendType sendType, String clientIp, boolean process, Integer isGx, Staff gxStaff) {
        if (sendType == null) {
            sendType = SendType.UPLOAD;
        }
        UploadResult uploadResult = logisticsOfflineBusiness.consignUpload(staff, sids, sendType, clientIp, false, process, isGx, gxStaff);
        if (Objects.isNull(uploadResult)) {
            return Lists.newArrayList();
        }
        List<ConsignRecord> consignRecords = uploadResult.consignRecords;
        List<Trade> successTrades = uploadResult.successTrades;
        addPreUploadTag(staff, successTrades, consignRecords);
        return consignRecords;
    }

    @Override
    public void addPreUploadTag(Staff staff, List<Trade> trades, List<ConsignRecord> consignRecords) {
        Map<Long, Trade> sidTradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (o1, o2) -> o1));
        List<Long> tagSids = consignRecords.stream()
                .filter(r -> r.getIsError() == 0)   // 发货成功
                .filter(r -> !r.isUploadByService()) // 通过新版发货服务的不用处理
                .filter(r -> !SendType.RESEND.name().equals(r.getConsignType()))   // 不为重新发货
                .map(ConsignRecord::getSid)
                .filter(sid -> Optional.ofNullable(sidTradeMap.get(sid)).filter(trade -> !TradeUtils.isAfterSendGoods(trade)).isPresent())  // 订单状态不为系统已发货
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagSids)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("提前发货完成，需要增加预发货标签，tagSids:%s", tagSids)));
            }
            eventCenter.fireEvent(this, new EventInfo("trade.autotag.preupload").setArgs(new Object[]{staff, tagSids}), null);
        }
    }

    @Override
    public List<ConsignRecord> reUpload(Staff staff, ConsignRecordQueryParams params, String consignType, String clientIp) {
        return logisticsReUploadBusiness.reUpload(staff, params, ConsignUtils.getSendType(consignType), clientIp);
    }

    @Override
    public int cancelUpload(Staff staff, Long[] sids) {
        return logisticsUploadCancelBusiness.cancel(staff, sids);
    }

    @Override
    public ConsignCancelData cancelConsign(Staff staff, Long[] sids, Integer isGx, boolean triggerFx, boolean addUnauditExcep) {
        return cancelConsign(staff, sids, isGx, triggerFx, addUnauditExcep, false);
    }

    @Override
    public ConsignCancelData cancelConsign(Staff staff, Long[] sids, Integer isGx, boolean triggerFx, boolean addUnauditExcep, boolean handleFromWeb) {
        ConsignCancelData cancel = consignCancelBusiness.cancel(staff, isGx, triggerFx, addUnauditExcep, handleFromWeb, sids);
        return cancel;
    }

    @Override
    public List<ConsignRecord> resend(Staff staff, List<Trade> frontTrades, String clientIp, Integer flag) {
        return logisticsResendBusiness.resend(staff, frontTrades, clientIp, flag);
    }

    @Override
    public void asyncResend(Staff staff, List<Trade> frontTrades, String clientIp, Integer flag) {
        logisticsResendBusiness.asyncResend(staff, frontTrades, clientIp, flag);
    }

    @Override
    public Object getConsigningAllResult(Staff staff) {
        return logisticsOfflineAllBusiness.getOfflineConsigningAllResult(staff);
    }

    @Override
    public Map<String, String> cancelInsufficient(Staff staff, Long... sids) {
        return cancelInsufficientBusiness.cancel(staff, sids);
    }

    @Override
    public void matchTemplate(Staff staff, TradeQueryParams params) {
        expressTemplateMatchBusiness.match(staff, params, 0);
    }

    @Override
    public void matchTemplate(Staff staff, TradeQueryParams params, Integer revert) {
        expressTemplateMatchBusiness.match(staff, params, revert);
    }

    @Override
    public void matchTemplate(Staff staff, TradeQueryParams params, Integer revert,OpEnum op) {
        expressTemplateMatchBusiness.match(staff, params, revert,op);
    }

    @Override
    public void matchTemplate(Staff staff, TradeQueryParams params, Integer revert, OpEnum op, ProgressEnum pe) {
        expressTemplateMatchBusiness.match(staff, params, revert, op, pe);
    }

    @Override
    public Map<String, Long> percentageMatchTemplate(Staff staff, TradeQueryParams params) {
        return expressPercentageMatchService.percentageMatch(staff, params);
    }

    @Override
    public Map<String, String> distributionStatistics(Staff staff, Long... sids) {
        return tradeAuditService.distributionStatistics(staff, sids);
    }

    @Override
    public List<Trade> financeAudit(Staff staff, Long... sids) {
        return tradeAuditService.financeAudit(staff, sids);
    }

    @Override
    public Map<String, String> financeDistribution(Staff staff, Map<String, String> distributionDetailMap) {
        return tradeAuditService.financeDistribution(staff, distributionDetailMap);
    }

    @Override
    public List<Trade> financeReject(Staff staff, Long... sids) {
        return tradeAuditService.financeReject(staff, sids);
    }

    @Override
    @Cacheable(value = "defaultCache#3600", key = "'searchTraceLogistics_'+ #staff.companyId + '_' + #sid")
    public List<LogisticsTraceSearchResponse> searchTraceLogistics(Staff staff, User user, Long sid, String tid) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(user).append(" searchTraceLogistics, sid:").append(sid));
        }

        List<LogisticsTraceSearchResponse> results = new LinkedList<>();
        try {
            TbTrade tbTrade = tbTradeDao.queryBySid(staff, sid);

            tradeFillService.fill(staff,tbTrade);
            if (!CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(tbTrade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(tbTrade.getSource())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff,
                            String.format("订单不是淘宝订单，无法获取物流流转信息，sid:%s", sid)));
                }
                return results;
            }
            //合单订单需要单独处理
            if (TradeUtils.isMerge(tbTrade)) {
                List<TbTrade> mergeTbTrades = tbTradeDao.queryByMergeSids(staff, tbTrade.getMergeSid());

                tradeFillService.fill(staff,mergeTbTrades);
                List<TbTrade> diffLogistics = TradeUtils.isDiffLogistics(mergeTbTrades);
                for (TbTrade trade : diffLogistics) {
                    results.add(____searchTraceLogistics(user, trade.getTid(), null, null));
                }
            } else {
                results.add(____searchTraceLogistics(user, tid, null, null));
            }
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildErrorLog(user, e, "查询物流流转信息出错"));
            }
            try {
                results.addAll(__searchTraceLogistics(user, tid));
            } catch (Exception e2) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildErrorLog(user, e, "查询物流流转信息出错"));
                }
            }
        }

        for (LogisticsTraceSearchResponse result : results) {
            result.setHeaderContent(null);
        }
        return results;
    }

    private LogisticsTraceSearchResponse ____searchTraceLogistics(User user, String tid, String oid, Long isSplit) {
        Long tidNum = Long.parseLong(tid);
        LogisticsTraceSearchResponse rsp = tbLogisticsAccess.searchTraceLogistics(user, tidNum, isSplit, oid);
        //有空实例, 清除
        if (rsp.getTraceList().size() == 1 && rsp.getTraceList().get(0).getStatusTime() == null && rsp.getTraceList().get(0).getStatusDesc() == null) {
            rsp.setTraceList(null);
            rsp.setStatus("卖家已发货");
        }
        return rsp;
    }

    private List<LogisticsTraceSearchResponse> __searchTraceLogistics(User user, String tid) {
        Long tidNum = Long.parseLong(tid);
        List<LogisticsTraceSearchResponse> results = new LinkedList<>();

        //如果失败, 有可能是因为, 当前订单拆单发货并不是在 大卖家系统环境,  调取 LogisticsOrdersGet 接口查看 子订单物流分部
        LogisticsOrdersGetResponse rsp = tbTradeAccess.LogisticsOrderGet(user, tidNum);

        for (Shipping shipping : rsp.getShippings()) {
            if (shipping.getStatus().equals("CLOSED")) continue;

            StringBuilder sb = new StringBuilder();
            int subTidsSize = shipping.getSubTids().size();
            for (int i = 0; i < subTidsSize; i++) {
                sb.append(shipping.getSubTids().get(i));
                if (i != subTidsSize - 1) {
                    sb.append(",");
                }
            }

            LogisticsTraceSearchResponse result = ____searchTraceLogistics(user, tid, sb.toString(), 1L);

            if (result.getTid() == 0) {
                result = new LogisticsTraceSearchResponse();
                result.setOutSid(sb.toString());
                result.setStatus("订单物流流转信息异常,物流编号无法查询..");
                result.setOutSid(shipping.getOutSid());
                result.setTid(tidNum);
                result.setCompanyName(shipping.getCompanyName());
                results.add(result);
            } else if (!shipping.getStatus().equals("CLOSED")) {
                results.add(result);
            }
        }

        return results;
    }



    @Override
    public List<TradeResult> handleAddress(Staff staff, int action, Long... sids) {
        return addressBusiness.handleAddress(staff, action, sids);
    }
    @Override
    public List<Map<String, String>> matchItemByRelation(Staff staff, Long... sids) {
        if(TradeExceptWhiteUtils.openHandlerItemRelationCompanyIds(staff)){
            ItemMatchContext itemMatchContext = new ItemMatchContext();

            itemRelationChangeHandlerBusiness.handlerItem(staff, itemMatchContext, sids);
            return itemMatchContext.getErrors();
        }
        ItemMatchData data = itemMatchByRelationBusiness.match(staff,null, sids);
        tradeTraceBusiness.asyncTrace(staff, data.updateTrades, OpEnum.ITEM_RELATION_BIND);
        return data.errors;
    }

    @Override
    public List<Map<String, String>> matchItemByOuterId(Staff staff, Long... sids) {
        ItemMatchData data = itemMatchByOuterIdBusiness.match(staff,null, sids);
        tradeTraceBusiness.asyncTrace(staff, data.updateTrades, OpEnum.ITEM_RELATION_BIND);
        reMatchBusiness.reMatch(staff, TradeUtils.toSidList(data.updateTrades), EventEnum.EVENT_TRADE_MATCH_ITEM, Boolean.TRUE);
        return data.errors;
    }

    @Override
    public List<Trade> updateSuite2Latest(Staff staff, Long... sids) {
        return tradeSuiteUpdateBusiness.update(staff, sids);
    }
    @Override
    public List<Map<String, String>> updateProcess2Latest(Staff staff, Long... sids) {
        ItemMatchData data = itemConvertMatchByRelationBusiness.match(staff, null,sids);
        tradeTraceBusiness.asyncTrace(staff, data.updateTrades, OpEnum.ITEM_PROGRESS_UPDATE);
        return data.errors;
    }

    @Override
    public List<ConsignRecord> externalConsignUpload(Staff staff, ExternalConsignRequest request) {
        if(logger.isDebugEnabled()){
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("收到开放平台发货请求，sids:{%s},tid:{%s},consignType:{%s},expressCode:{%s},outSid:{%s},clientIp:{%s},source:{%s}",Arrays.toString(request.getSids()),request.getTid(),request.getConsignType(),request.getExpressCode(),request.getOutSid(),request.getClientIp(), request.getSource())));
        }
        List<ConsignRecord> consignRecords;
        try {
            consignRecords = validateBeforeConsign(staff, request);
        }catch (IllegalArgumentException e){
            logger.error(LogHelper.buildLogHead(staff).append("开放平台发货请求失败,原因:").append(e.getMessage()),e);
            throw e;
        }
        if(logger.isDebugEnabled()){
            logger.debug(LogHelper.buildLogHead(staff).append("开放平台发货请求过滤后可以进行发货的sid:").append(Arrays.toString(request.getSids())));
        }

        if (StringUtils.isNotEmpty(request.getOperateType()) && "1".equalsIgnoreCase(request.getOperateType())||"2".equals(request.getOperateType())){
            consignRecords.addAll(consign(staff,request.getSids(),request.getConsignType(), request.getClientIp(), null, null ,null, null, request.getOperateType()));
        } else if(request.isOnlyUpload() && (StringUtils.isNotBlank(request.getSource()) && !request.getSource().equalsIgnoreCase(CommonConstants.PLAT_FORM_TYPE_SYS))) { //平台单只上传平台，不走系统发货
            consignUploadService.externalAsyncUpload(staff, request);
        } else { //系统单（走系统发货，发货内部逻辑会过滤不上传平台）或者非只上传订单，走一次系统发货
            consignRecords.addAll(consign(staff,request.getSids(),request.getConsignType(), request.getClientIp(), null, null ,null, null));
        }
        return consignRecords;
    }

    private List<ConsignRecord> validateBeforeConsign(Staff staff, ExternalConsignRequest request) {
        Assert.isTrue(StringUtils.isNotEmpty(request.getTid()) || ArrayUtils.isNotEmpty(request.getSids()),"未指定发货订单");
        List<? extends Trade> trades = null;
        if(ArrayUtils.isNotEmpty(request.getSids())){
            trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, request.getSids());
            Assert.notEmpty(trades,"查询指定订单为空");
        }else {
            trades = tradeSearchService.queryByTids(staff,false,request.getTid());
            Assert.notEmpty(trades,"查询指定订单为空");
            Assert.isTrue(trades.size()==1,"该tid对应多笔订单，可能存在拆单");
        }
        if(request.isOnlyUpload()){
            trades.removeIf(trade -> StringUtils.isNotEmpty(trade.getOutSid()));
            Assert.notEmpty(trades,"订单已有运单号,不可指定运单号");
            trades.removeIf(trade->{
                try {
                    return !deliveryLimitBusiness.checkDeliveryLimit(trade.getSource(),request.getExpressCode(),request.getOutSid());
                } catch (DeliveryLimitException e) {
                    return true;
                }
            });
            Assert.notEmpty(trades,"所选订单不可使用当前单号发货");
            trades = setTradeTemplateByExpressCode(staff,request.getExpressCode(),request.getOutSid(), OpEnum.EXTERNAL_SAVE_TEMPLATE,trades.stream().map(Trade::getSid).toArray(Long[]::new));
            Assert.notEmpty(trades,String.format("快递公司（%s）没有绑定到相应仓库中。", request.getExpressCode()));
        }

        // KMERP-146578: 走系统发货的订单先校验一遍订单状态，防止已发货的合单返回给开放平台内容为空
        List<ConsignRecord> records = Lists.newArrayList();
        if (!(request.isOnlyUpload() && (StringUtils.isNotBlank(request.getSource()) && !request.getSource().equalsIgnoreCase(CommonConstants.PLAT_FORM_TYPE_SYS)))) {
            Iterator<? extends Trade> iterator = trades.iterator();
            while (iterator.hasNext()) {
                Trade trade = iterator.next();
                if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
                    records.add(ConsignUtils.buildFullRecord(trade, ConsignUtils.getSendType(request.getConsignType()), 1, 1).setErrorDesc("非待发货订单[卖家已发货]"));
                    iterator.remove();
                }
            }
        }
        request.setSids(trades.stream().map(Trade::getSid).toArray(Long[]::new));
        return records;
    }

    @Override
    public List<Trade> setTradeTemplateByExpressCode(Staff staff, String expressCode, String outSid, OpEnum op, Long... sids) {
        if (sids == null || sids.length == 0) {
            return new ArrayList<>();
        }
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, sids);
            if (CollectionUtils.isEmpty(trades)) {
                return new ArrayList<>();
            }
            Set<Long> warehouseIds = trades.stream().map(Trade::getWarehouseId).collect(Collectors.toSet());
            Map<Long, Map<String, List<UserExpressWlbModel>>> warehouseIdExpressCodeTemplateMap = new HashMap<>();
            List<Trade> updateTrades = new ArrayList<>();
            for (Long warehouseId : warehouseIds) {
                warehouseIdExpressCodeTemplateMap.put(warehouseId, expressTemplateDubboService.getSimpleTemplateMapByWarehouseId(staff, warehouseId));
            }
            for (Trade trade : trades) {
                Map<String, List<UserExpressWlbModel>> expressCodeTemplateMap = warehouseIdExpressCodeTemplateMap.get(trade.getWarehouseId());
                if (MapUtils.isNotEmpty(expressCodeTemplateMap)) {
                    List<UserExpressWlbModel> models = expressCodeTemplateMap.get(expressCode);
                    if (CollectionUtils.isNotEmpty(models)) {
                        UserExpressWlbModel model = models.get(0);
                        Trade updateTrade = new TbTrade();
                        updateTrades.add(updateTrade);
                        updateTrade.setSid(trade.getSid());
                        updateTrade.setTemplateId(Long.parseLong(model.getTemplateId()));
                        updateTrade.setTemplateType(Objects.equals(1, model.getIsWlb()) ? 1 : 0);
                        updateTrade.setOutSid(outSid);
                        updateTrade.getOperations().put(op,String.format(op.getName()+",快递模板修改为:%s,快递单号修改为%s。",model.getTemplateName(),outSid));
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(updateTrades)){
                tradePtService.saveByTrades(staff, updateTrades);
                tradeUpdateService.updateTrades(staff,updateTrades);
                tradeTraceBusiness.asyncTrace(staff,updateTrades,OpEnum.EXTERNAL_SAVE_TEMPLATE);
            }
            // 修改快递需要计算理论运费
            eventCenter.fireEvent(this, new EventInfo("modify.template.calculate.theoryPostFee").setArgs(new Object[]{staff, TradeUtils.toSidList(updateTrades)}), null);
            return updateTrades;
        });
    }

    @Override
    public JSONArray getTradeTypeList(){
        List<TradeTypeInfo> list = new ArrayList();
        Arrays.stream(TradeTypeEnum.values()).forEach(type->{{
            list.add(new TradeTypeInfo(type.name(),type.getcode(),type.getName()));
        }});
        return JSONArray.parseArray(JSONObject.toJSONString(list));
    }

    @Override
    public List<Trade> handleCooperationNoExcept(Staff staff, Long... sids) {
        if(logger.isDebugEnabled()){
            logger.debug(LogHelper.buildLogHead(staff).append("开始尝试处理这些订单的【未匹配常态合作编码】异常，sids:").append(Arrays.toString(sids)));
        }
        if (sids == null || sids.length == 0){
            return new ArrayList<>();
        }
        return lockService.locks(tradeLockBusiness.getERPLocks(staff,sids),() -> {
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
            List<Trade> canUpdateTrades = new ArrayList<>();
            for (Trade trade : trades) {
                User user = staff.getUserByUserId(trade.getUserId());
                if (user == null) {
                    Logs.debug(LogHelper.buildLog(staff, String.format("sid = %s userId=%s 没有查询到user!", trade.getSid(), trade.getUserId())));
                    continue;
                }
                if (!TradePlatExceptUtils.hasCooperationCodeNotMatch(user, trade) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH)) {
                    Trade updateTrade = TradeBuilderUtils.builderUpdateExcep(trade);
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH, 0L);
                    TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.VIP_COOPERATION_CODE_NOT_MATCH, 0L);
                    canUpdateTrades.add(updateTrade);
                }
            }
            if (!canUpdateTrades.isEmpty()){
                tradeUpdateService.updateTrades(staff,canUpdateTrades);
              //  tradeExceptAdapter.saveExcept(staff, canUpdateTrades, ExceptSignWayEnum.AUTO.getAutoSign());
                tradeTraceService.batchAddTradeTrace(staff,TradeTraceUtils.createTradeTraceWithTrades(staff,canUpdateTrades,"处理【未匹配常态合作编码】异常",staff.getName(),new Date(),""));
            }
            List<Long> sidList = canUpdateTrades.stream().map(Trade::getSid).collect(Collectors.toList());
            if(logger.isDebugEnabled()){
                logger.debug(LogHelper.buildLogHead(staff).append("本次成功取消以下订单的【未匹配常态合作编码】异常，sids:").append(sidList));
            }
            return trades;
        });
    }

    @Override
    public Map<String, String> getFxgOpenId(Staff staff, String tid) {
        if (tid.contains("-")) {
            // 带 - 的是手工单
            String newTid = tid.split("-")[0];
            return doGetFxgOpenId(staff, newTid);
        }
        return doGetFxgOpenId(staff, tid);
    }

    private Map<String, String> doGetFxgOpenId(Staff staff, String tid){
        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, true, tid);
        if (CollectionUtils.isNotEmpty(tbTrades)) {
            Trade trade = getFxgTrade(tbTrades);
            Assert.notNull(trade, String.format("根据平台订单号[%s]没有查询到系统订单，请检查平台订单号是否存在", tid));
            User user = userService.queryById(trade.getUserId());
            if (user == null){
                logger.error(LogHelper.buildLogHead(staff).append(String.format("根据原平台单[%s]没有查询到店铺[%s]的信息", tid, trade.getUserId())));
                throw new IllegalArgumentException(String.format("根据原平台单[%s]没有查询到店铺信息", tid));
            }
            if (!CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())){
                throw new IllegalArgumentException(String.format("根据原平台单[%s]查询到的店铺不是抖音店铺，请检查该订单是否属于抖音店铺", tid));
            }
            try {
                IPlatformTradeAccess access = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_FXG, IPlatformTradeAccess.class);
                Object originalTrade = access.getOriginalTrade(user, tid);
                Assert.notNull(originalTrade, String.format("根据平台订单号[%s]没有查询到平台订单数据", tid));
                String doudianOpenId = JSONObject.parseObject(JSONObject.toJSONString(originalTrade)).getString("doudian_open_id");
                Assert.isTrue(StringUtils.isNotBlank(doudianOpenId), String.format("根据平台订单号[%s]没有查询到openId", tid));
                Map<String, String> map = new HashMap<>();
                map.put("doudian_open_id", doudianOpenId);
                return map;
            } catch (Exception e) {
                String errMsg = "获取平台订单数据失败" + (StringUtils.isNotEmpty(e.getMessage()) ? (":" + e.getMessage()) : "");
                logger.error(LogHelper.buildErrorLog(staff, e, errMsg));
                throw new IllegalArgumentException(errMsg);
            }
        } else {
            throw new IllegalArgumentException(String.format("根据平台订单号[%s]没有查询到系统订单，请检查平台订单号是否存在", tid));
        }
    }

    @Override
    public void updateTimeoutAction(Staff staff, TradeUpdateTimeoutActionTimeRequest req) {
        Preconditions.checkArgument(featureService.checkHasFeature(staff.getCompanyId(), Feature.EDIT_DELIVERY_TIME),
                "当前公司无权限, 需要去tj开启该功能白名单");
        // 校验
        req.check();

        List<TbTrade> dbTrades = tbTradeDao.queryByKeys(staff, "sid, company_id, timeout_action_time", "sid", req.getSids().toArray(new Long[0]));
        Map<Trade, Trade> beforeToAfter = buildTimeoutActionTimeUpdate(staff, req, dbTrades);
        if (beforeToAfter.isEmpty()) {
            logger.warn(LogHelper.buildLog(staff, "批量修改承诺发货时间过滤后没有需要更新的订单"));
            return;
        }

        tbTradeDao.batchUpdate(staff, new ArrayList<>(beforeToAfter.values()));
        List<Long> updatedSids = beforeToAfter.keySet().stream().map(Trade::getSid).collect(Collectors.toList());
        tradeSysLabelBusiness.handleTagsBySids(staff, updatedSids, OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_UPDATE_TIMEOUT), true);
        List<TradeTrace> traces = new ArrayList<>(dbTrades.size());
        for (Map.Entry<Trade, Trade> beforeToAfterEntry : beforeToAfter.entrySet()) {
            Trade before = beforeToAfterEntry.getKey(), after = beforeToAfterEntry.getValue();
            Date beforeTimeoutActionTime = before.getTimeoutActionTime();
            String preDateFormat = beforeTimeoutActionTime != null && beforeTimeoutActionTime.after(TradeConstants.INIT_DATE) ?
                    DateFormatUtils.format(before.getTimeoutActionTime(), "yyyy-MM-dd HH:mm:ss") : "空";
            String afterDateFormat = DateFormatUtils.format(after.getTimeoutActionTime(), "yyyy-MM-dd HH:mm:ss");
            TradeTrace trace = TradeTraceUtils.createTradeTraceWithTrade(
                    staff,
                    before,
                    "人为修改订单承诺发货和揽收时间",
                    staff.getName(),
                    new Date(),
                    "人为修改订单承诺发货和揽收时间, 修改前: " + preDateFormat + ", 修改后: " + afterDateFormat
            );
            traces.add(trace);
        }
        if (!traces.isEmpty()) {
            tradeTraceService.batchAddTradeTrace(staff, traces);
        }
    }

    public Map<Trade, Trade> buildTimeoutActionTimeUpdate(Staff staff, TradeUpdateTimeoutActionTimeRequest req, List<TbTrade> dbTrades) {
        Date timeoutActionTime = null;
        String timeoutActionTimeStr = req.getTimeoutActionTime();
        if (timeoutActionTimeStr != null) {
            try {
                timeoutActionTime = DateUtils.parseDate(timeoutActionTimeStr, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException e) {
                String msg = "批量修改承诺发货时间错误，时间参数格式不正确 " + timeoutActionTimeStr;
                logger.error(LogHelper.buildErrorLog(staff, e, msg));
                throw new IllegalArgumentException(msg, e);
            }
        }

        Map<Trade, Trade> beforeToAfter = new HashMap<>(dbTrades.size());
        if (timeoutActionTime != null) {
            long time = timeoutActionTime.getTime();
            for (Trade dbTrade : dbTrades) {
                Date dbTradeTimeoutActionTime = dbTrade.getTimeoutActionTime();
                if (dbTradeTimeoutActionTime != null &&
                        dbTradeTimeoutActionTime.getTime() == time
                ) {
                    continue;
                }
                TbTrade t = new TbTrade();
                t.setSid(dbTrade.getSid());
                t.setCompanyId(staff.getCompanyId());
                t.setTimeoutActionTime(timeoutActionTime);
                beforeToAfter.put(dbTrade, t);
            }
            return beforeToAfter;
        }
        // 提前或延后
        int beforeOrAfter = req.getBeforeOrAfter();
        int days = req.getDays(), hours = req.getHours();
        Date added;
        for (Trade dbTrade : dbTrades) {
            Date dbTimeoutActionTime = dbTrade.getTimeoutActionTime();
            if (dbTimeoutActionTime == null || dbTimeoutActionTime.getTime() == TradeConstants.INIT_DATE_TIME) {
                logger.warn(LogHelper.buildLog(staff, String.format("订单承诺发货时间为空,手动批量修改承诺发货时间过滤,sid: %s", dbTrade.getSid())));
                continue;
            }
            TbTrade t = new TbTrade();
            t.setSid(dbTrade.getSid());
            t.setCompanyId(staff.getCompanyId());
            added = dbTimeoutActionTime;
            if (days > 0) {
                added = DateUtils.addDays(added, days * beforeOrAfter);
            }
            if (hours > 0) {
                added = DateUtils.addHours(added, hours * beforeOrAfter);
            }
            t.setTimeoutActionTime(added);
            beforeToAfter.put(dbTrade, t);
        }
        return beforeToAfter;
    }

    /**
     * 取放心购平台单
     * @param tbTrades
     * @return
     */
    private Trade getFxgTrade(List<TbTrade> tbTrades){
        for (TbTrade tbTrade : tbTrades) {
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(tbTrade.getSource())) {
                return tbTrade;
            }
        }
        return null;
    }

    @Data
    static class TradeTypeInfo{
        String key;
        String code;
        String name;
        public TradeTypeInfo(String key, String code, String name){
            this.key = key;
            this.code = code;
            this.name = name;
        }
    }
}

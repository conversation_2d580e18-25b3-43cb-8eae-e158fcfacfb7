package com.raycloud.dmj.services.sensitive.platform;

import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.sensitive.AbstractPlatformTradeSecurityService;
import com.raycloud.dmj.services.utils.SecretUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @date 2022/4/19
 */
@Service
public class PddPlatformTradeSecurityService extends AbstractPlatformTradeSecurityService {

    TradePddTradeBusiness pddTradeBusiness;

    @Autowired
    public PddPlatformTradeSecurityService(TradePddTradeBusiness pddTradeBusiness) {
        this.pddTradeBusiness = pddTradeBusiness;
    }

    @Override
    public void doSensitiveTrades(Staff staff, List<Trade> trades) {
        pddTradeBusiness.pddMaskDataReplace(staff, trades);
    }

    @Override
    public List<Trade> filterTrades(Staff staff, List<Trade> trades) {
        // 筛选出pdd平台单或pdd系统单且以pdd方式加密的订单
        return trades.stream().filter(trade -> {
            if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())) {
                return true;
            }
            // 对于系统单，且可以进行平台解密的订单（tid为对应平台单的tid + "-xxx"），截取出原始的tid，且将其source设置为pdd，防止被后续调用的平台脱敏逻辑过滤掉
            if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_PDD) && isPddEncrypt(trade)) {
                if (StringUtils.isNotBlank(trade.getTid()) && trade.getTid().lastIndexOf("-") > 0) {
                    trade.setTid(trade.getTid().substring(0, trade.getTid().lastIndexOf("-")));
                    trade.setSource(CommonConstants.PLAT_FORM_TYPE_PDD);
                }
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    /**
     * 校验订单是否是拼多多加密
     */
    private boolean isPddEncrypt(Trade trade) {
        return SecretUtils.isPddEncrypt(trade.getReceiverName()) || SecretUtils.isPddEncrypt(trade.getReceiverMobile()) || SecretUtils.isPddEncrypt(trade.getReceiverAddress());
    }
}

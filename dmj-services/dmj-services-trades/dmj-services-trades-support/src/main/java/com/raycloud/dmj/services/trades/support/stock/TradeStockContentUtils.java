package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.business.trade.TradeStockData;
import com.raycloud.dmj.domain.TradeStockContent;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;

import java.util.List;

/**
 * @Auther mengfanguang
 * @Date 2023/8/30
 */
public class TradeStockContentUtils {

    public static TradeStockData transferTradeStockData(Staff staff, TradeLocalConfigurable tradeLocalConfigurable, TradeConfig tradeConfig,  TradeStockContent content) {
        TradeStockData data = OrderStockUtils.filterApplyStockOrders(staff, content.getRequestOrders(), tradeLocalConfigurable, tradeConfig, content.isCheck());
        return data;
    }

}
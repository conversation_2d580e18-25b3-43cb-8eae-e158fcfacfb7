package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeFillEnum;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import org.springframework.stereotype.Component;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-17
 */
@Component
public class TradeAuditQueryer extends TradeAssembleStrategy{

    @Override
    public SenceCodeEnum supportScene() {
        return SenceCodeEnum.AUDIT;
    }

    @Override
    TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long... sids) {
        return TradeAssembleParams.fullDataWithFill();
    }
}

package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Sets;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Buffers;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.modify.TradeUpdateBusiness;
import com.raycloud.dmj.business.gift.GiftDeductCountBusiness;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.split.*;
import com.raycloud.dmj.business.split.config.*;
import com.raycloud.dmj.business.split.filter.ITradeSplitFilterService;
import com.raycloud.dmj.business.split.postlock.SplitAuditAutoBusiness;
import com.raycloud.dmj.business.split.support.*;
import com.raycloud.dmj.business.split.warehouse.ai.help.WarehouseAiClient;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.split.SplitCheckResult;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.split.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.dto.SplitPlatformTradeDTO;
import com.raycloud.dmj.services.platform.trades.dto.SplitPlatformTradeRespDTO;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.ec.api.*;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.raycloud.dmj.services.platform.trades.*;

/**
 * <AUTHOR>
 * <p>
 * 18/10/13
 */
@Deprecated
@Service
public class TradeSplitOldService implements ITradeSplitOldService {

    public static OrderCopier<Order, Order> orderCopier = new OrderCopier<>();
    private static final TradeCopier<Trade, Trade> tradeCopier = new TradeCopier<>();

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    TbOrderDAO tbOrderDAO;

    @Resource
    ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    SplitUndoBusiness splitUndoBusiness;

    @Resource
    SplitAllInOneBusiness splitAllInOneBusiness;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    SplitAuditAutoBusiness splitAuditAutoBusiness;

    @Resource
    IEventCenter eventCenter;

    @Resource
    SplitFilterBusiness splitFilterBusiness;

    @Resource
    SplitMergeOutSidBusiness splitMergeOutSidBusiness;

    @Resource
    ITradeAuditService tradeAuditService;

    @Resource
    IWmsService wmsService;

    @Resource
    ITradeFillService tradeFillService;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeSplitFilterService tradeSplitFilterService;

    @Resource
    ReMatchBusiness reMatchBusiness;

    @Resource
    SplitConfigBusiness splitConfigBusiness;
    @Resource
    WarehouseAiClient warehouseAiClient;
    @Resource
    TradeQueryBuilder tradeSqlQueryBuilder;
    @Resource
    private TradeTraceBusiness tradeTraceBusiness;
    @Resource
    private ITradeConfigNewService tradeConfigNewService;
    @Resource
    private IShopService shopService;
    @Resource
    private GiftDeductCountBusiness giftDeductCountBusiness;

    @Resource
    private TradeUpdateBusiness tradeUpdateBusiness;
    @Resource
    private PlatformTradeSplitBusiness platformTradeSplitBusiness;

    @Resource
    private IBufferService bufferService;

    @Override
    public SplitResult splitSku(Staff staff, Long sid, Long orderId) {
        return SplitResult.builder()
                .beforeSplitCount(1)
                .resultSids(splitKind(staff, TradeSplitEnum.SPLIT_SKU, false, sid, orderId))
                .build();
    }

    @Override
    public SplitResult splitNum(Staff staff, Long sid, Long orderId, Integer count, Integer num) {
        Assert.isTrue(count <= 5000, "拆分最大数量不能超过5000!");
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sid), () -> {
            TbTrade originTrade = tbTradeDao.queryBySid(staff, sid);
            Assert.notNull(originTrade, "需要拆分的订单已不存在！");
            Assert.isTrue(!supportQimenTradeForbidSplit(staff, originTrade), "已开启“奇门订单不支持拆单”配置，本单为奇门订单不支持拆单");

            tradeFillService.fill(staff, originTrade);
            tradeSplitFilterService.filterTrades(staff, Lists.newArrayList(originTrade), true);
            List<TbOrder> originTbOrders = tbOrderDAO.queryByIds(staff, orderId);
            Assert.isTrue(originTbOrders != null && originTbOrders.size() == 1, "需要拆分的子订单已不存在！");
            TbOrder originTbOrder = originTbOrders.get(0);
            Assert.notNull(originTbOrder, "需要拆分的子订单已不存在！");
            List<TbOrder> tbOrders = originTrade.getMergeSid() < 0 ? tbOrderDAO.queryBySids(staff, sid) : tbOrderDAO.queryBySids(staff, TradeUtils.toSids(tbTradeDao.queryByMergeSids(staff, originTrade.getMergeSid())));
            tbOrderDAO.fillOrderExt(staff,tbOrders);
            List<Trade> splitTrades = new ArrayList<>();
            int splitNum = 0;
            for (int i = 0; i < count; i++) {
                TbTrade splitTrade = new TbTrade();
                TbOrder splitOrder = new TbOrder();
                splitOrder.setSourceId(originTbOrder.getId());
                splitOrder.setNum(num);
                splitOrder.setNumIid(originTbOrder.getNumIid());
                splitOrder.setSkuId(originTbOrder.getSkuId());
                splitOrder.setSkuSysId(originTbOrder.getSkuSysId());
                splitOrder.setOuterSkuId(originTbOrder.getOuterSkuId());
                splitOrder.setOuterId(originTbOrder.getOuterId());
                splitOrder.setSysOuterId(originTbOrder.getSysOuterId());
                splitOrder.setOuterIid(originTbOrder.getOuterIid());
                splitTrade.setOrders(Lists.newArrayList(splitOrder));
                splitTrades.add(splitTrade);
                splitNum += splitOrder.getNum();
            }
            if (splitNum >= originTbOrder.getNum()) {
                Logs.warn(LogHelper.buildLog(staff, String.format("拆分数量比原数量小，无法按数量拆分,sid=%s,orderId=%s,originNum=%s,count=%s,num=%s", sid, orderId, originTbOrder.getNum(), count, num)));
                throw new TradeException("拆分数量比原数量小，无法按数量拆分！");
            }
            SplitParams params = new SplitParams();
            params.splitPlatGiftFollowMainOrder=false;
            params.splitDataMap.put(originTrade, splitTrades);
            Long[] originSids = new Long[1];
            originSids[0] = sid;
            if(MapUtils.isEmpty(params.splitDataMap)){
                return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
            }
            SplitResult splitResult = buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_NUM, params), 1);
            if(splitResult.getResultSids().length>0){
                List<Trade> tradeTraces = PlatGiftSplitUtils.getTradeTraces(staff, originTbOrders, tbOrders);
                tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.SPLIT);
            }
            return splitResult;
        });
    }

    @Override
    public SplitResult splitMix(Staff staff, TbTrade trade, String groupCount, boolean splitFx2GxNew) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, trade.getSid()), () -> {
            TbTrade originTrade = tbTradeDao.queryBySid(staff, trade.getSid());
            Assert.notNull(originTrade, "需要拆分的订单已不存在！");
            Assert.isTrue(!supportQimenTradeForbidSplit(staff, trade), "已开启“奇门订单不支持拆单”配置，本单为奇门订单不支持拆单");
            tradeFillService.fill(staff, originTrade);
            List<TbTrade> tbTrades = tradeSplitFilterService.filterTrades(staff, Lists.newArrayList(originTrade), false);
            if (CollectionUtils.isEmpty(tbTrades)) {
                return new SplitResult();
            }
            List<TbOrder> tbOrders = originTrade.getMergeSid() < 0 ? tbOrderDAO.queryBySids(staff, trade.getSid()) : tbOrderDAO.queryBySids(staff, TradeUtils.toSids(tbTradeDao.queryByMergeSids(staff, originTrade.getMergeSid())));
            tbOrderDAO.fillOrderExt(staff,tbOrders);
            originTrade = tbTrades.get(0);
            StringBuilder sb = new StringBuilder("混合拆分，sid=").append(trade.getSid()).append(",groupCount=").append(groupCount);
            List<Trade> splitTrades = new ArrayList<>();
            Integer[] groupCountArr = ArrayUtils.toIntegerArray(groupCount);
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            List<Order> splitOrderList=new ArrayList<>();
            for (Integer count : groupCountArr) {
                List<Order> splitOrders = new ArrayList<>();
                for (int i = 0; i < count; i++) {
                    if (CollectionUtils.isEmpty(orders)) {
                        continue;
                    }
                    Order splitOrder = orders.remove(0);
                    sb.append(",orderId=").append(splitOrder.getId()).append(",num=").append(splitOrder.getNum()).append(",splitNum=").append(splitOrder.getSplitNum());
                    if (splitOrder.getSplitNum() != null && splitOrder.getSplitNum() > 0) {
                        splitOrder.setSourceId(splitOrder.getId());
                        splitOrder.setNum(splitOrder.getSplitNum());
                        splitOrders.add(splitOrder);
                    }
                }
                if (!splitOrders.isEmpty()) {
                    TbTrade splitTrade = new TbTrade();
                    TradeUtils.setOrders(splitTrade, splitOrders);
                    splitTrades.add(splitTrade);
                    splitOrderList.addAll(splitOrders);
                }
            }
            Logs.ifDebug(LogHelper.buildLog(staff, sb.toString()));
            if (splitTrades.isEmpty()) {
                throw new TradeException("请至少给一个商品设置大于0的拆分数量。");
            }
            // 处理原单没有商品的情况 这里和配置拆分保持一致
            // splitAllInOneBusiness.split 当原单没有商品是不会做拆分的
            // 配置拆分 SplitConfigXXX 是提前处理这种情况 取消一个拆分订单的商品保留到原单
            handlerAllSplitMix(staff, originTrade, splitTrades);
            // 这里再次判空
            if (splitTrades.isEmpty()) {
                throw new TradeException("原订单全部拆分成一个拆单，无法混合拆分！");
            }
            SplitParams params = new SplitParams();
            SplitConfigRule splitConfigRule = new SplitConfigRule();
            HashMap<String, Object> extendParams = Maps.newHashMapWithExpectedSize(2);
            extendParams.put("splitByGx", TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()?1:0);
            splitConfigRule.setExtendParams(extendParams);
            params.splitConfigRule = splitConfigRule;
            params.splitPlatGiftFollowMainOrder=false;
            params.stockUse = 1;
            params.splitDataMap.put(originTrade, splitTrades);
            if (splitFx2GxNew) {
                params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
            }
            tradeSplitFilterService.filterVirtual(staff, params);
            SplitResult splitResult = buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_MIX, params), 1);

            if(splitResult.getResultSids().length>0){
                List<Trade> tradeTraces = PlatGiftSplitUtils.getTradeTraces(staff, splitOrderList, OrderUtils.toOrderList(tbOrders));
                tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.SPLIT);
            }
            return splitResult;
        });
    }

    private void handlerAllSplitMix(Staff staff, TbTrade originTrade, List<Trade> splitTrades) {
        Set<Long> orderIds = Sets.newHashSet();
        for (Trade splitTrade : splitTrades) {
            List<Order> os = TradeUtils.getOrders4Trade(splitTrade);
            for (Order o : os) {
                orderIds.add(o.getSourceId());
            }
        }

        Query q = new Query();
        tradeSqlQueryBuilder.buildListQuery(q, "id", Lists.newArrayList(orderIds));
        List<TbOrder> tbOrders = tbOrderDAO.queryOrders(staff, "id, sid", q, new Page().setPageSize(orderIds.size()));

        SplitAllUtils.handlerAllSplit(staff, originTrade, tbOrders, splitTrades);
    }

    @Override
    public SplitResult splitRefund(Staff staff, Long[] originSids) {
        SplitParams silterData = splitFilterBusiness.filterRefund(staff, originSids);
        SplitParams filterParams = splitFilterBusiness.getRefundSplitParams(staff, silterData.getSids());
        if (MapUtils.isEmpty(filterParams.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(silterData.locks, () -> {
            SplitParams params = splitFilterBusiness.getRefundSplitParams(staff, silterData.getSids());
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_REFUND, params), originSids.length);
        });
    }

    @Override
    public SplitResult splitUnmatched(Staff staff, Long[] originSids, boolean throwException) {
        SplitParams filterData = splitFilterBusiness.filterUnmatched(staff, originSids);
        if (filterData.sids.size() == 0) {
            if (throwException) {
                throw new TradeException("没有需要拆分的订单！");
            } else {
                return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
            }
        }
        SplitParams filterParams = splitFilterBusiness.getUnmatchedSplitParams(staff, filterData.getSids(), throwException);
        if (MapUtils.isEmpty(filterParams.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.getUnmatchedSplitParams(staff, filterData.getSids(), throwException);
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_UNMATCHED, params), originSids.length);
        });
    }

    @Override
    public SplitResult splitInsufficient(Staff staff, Long[] originSids) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);

        SplitParams filterData = splitFilterBusiness.filterInsufficient(staff, originSids, tradeConfig);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        if (MapUtils.isEmpty(filterData.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.filterInsufficient(staff, filterData.getSids(), tradeConfig);
            SplitResultData splitResultData = splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_INSUFFICIENT, params);
            splitAuditAutoBusiness.splitAuditAuto(staff, tradeConfig, params.getAfterSids());
            return buildSplitResult(params, splitResultData, originSids.length);
        });
    }


    public SplitParams filterDestId(Staff staff, Long[] sids) {
        SplitParams filterData = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        for (Trade originTrade : originTrades) {
            Map<Long, List<Order>> collect = TradeUtils.getOrders4Trade(originTrade).stream().collect(Collectors.groupingBy(Order::getDestId));
            if (MapUtils.isNotEmpty(collect) && collect.size() > 1) {
                filterData.sids.add(originTrade.getSid());
                filterData.locks.add(TradeLockBusiness.trade2ERPLock(staff, originTrade));
            }
        }
        if (filterData.sids.size() == 0) {
            throw new TradeException("没有需要拆分的订单！");
        }
        return filterData;
    }

    public SplitParams getDestIdSplitParams(Staff staff, Long[] sids) {
        SplitParams params = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        for (Trade originTrade : originTrades) {
            Map<Long, List<Order>> collect = TradeUtils.getOrders4Trade(originTrade).stream().collect(Collectors.groupingBy(Order::getDestId));

            if (MapUtils.isNotEmpty(collect) && collect.size() > 1) {
                boolean needContinue = true;
                for (Map.Entry<Long, List<Order>> entry : collect.entrySet()) {
                    if (needContinue) {
                        needContinue = false;
                        continue;
                    }
                    Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                    TradeUtils.setOrders(splitTrade, entry.getValue());
                    params.splitDataMap.computeIfAbsent(originTrade, k -> new ArrayList<>()).add(splitTrade);
                }

            }
        }
        if (params.splitDataMap.size() == 0) {
            throw new TradeException("没有需要拆分的订单！");
        }
        return params;
    }

    @Override
    public SplitResult splitSupply(Staff staff, Long[] originSids, boolean isContinue) {
        SplitParams filterData = this.filterDestId(staff, originSids);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = this.getDestIdSplitParams(staff, originSids);
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_SUPPLY, params), originSids.length);
        });
    }

    @Override
    public SplitResult splitWeight(Staff staff, Long[] originSids, Double weight, boolean isContinue) {
        SplitParams filterData = splitFilterBusiness.filterWeight(staff, originSids, weight, isContinue);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        if (MapUtils.isEmpty(filterData.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.filterWeight(staff, filterData.getSids(), weight, isContinue);
            params.isContinue = isContinue;
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_WEIGHT, params), originSids.length);
        });
    }

    @Override
    public SplitResult splitConfig(Staff staff, Long[] originSids, SplitConfigRule rule) {
        return splitConfig(staff, originSids, rule, false);
    }

    @Override
    public SplitResult splitConfig(Staff staff, Long[] originSids, SplitConfigRule rule, boolean splitFx2GxNew){
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        SplitConfigFilter splitConfig = splitConfigBusiness.getSplitConfig(rule);
        SplitParams filterData = splitFilterBusiness.filterConfig(staff, originSids, splitConfig, rule, tradeConfig);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.filterConfig(staff, filterData.getSids(), splitConfig, rule, tradeConfig, splitFx2GxNew);
            tradeSplitFilterService.filterVirtual(staff, params);
            if (params.splitDataMap.size() > 0) {
                if(splitFx2GxNew){
                    params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
                }
                params.splitConfigRule = rule;
                params.tracePrefix = "批量拆分";
                SplitResultData splitResultData = splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_CONFIG, params);
                if (rule.getSplitType() == TradeSplitConfigTypeEnum.INSUFFICIENT.getType() || rule.getSplitType() == TradeSplitConfigTypeEnum.INSUFFICIENT_KIND.getType()) {
                    splitAuditAutoBusiness.splitAuditAuto(staff, tradeConfigService.get(staff), params.getAfterSids());
                }
                return buildSplitResult(params, splitResultData, originSids.length);
            } else {
                return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
            }
        });
    }

    public SplitResult splitOneClick(Staff staff, Long[] sids, List<SplitConfigRule> rules) {
        return splitOneClick(staff, sids, rules, false);
    }

    @Override
    public SplitResult splitOneClick(Staff staff, Long[] sids, List<SplitConfigRule> rules, boolean splitFx2GxNew) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        SplitResult splitResult = new SplitResult();
        Map<Long, List<Long>> sidSplitSidsMap = new HashMap<>();
        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            Long[] tempSids = sids;
            Long[] needSplitSids = sids;
            List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, needSplitSids);
            for (SplitConfigRule rule : rules) {
                SplitParams params = splitFilterBusiness.filterConfig(staff, originTrades, splitConfigBusiness.getSplitConfig(rule), rule, tradeConfig, splitFx2GxNew);
                if (params.splitDataMap.size() > 0) {
                    SplitResultData splitResultData = splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_CONFIG, params);
                    mergeMap(sidSplitSidsMap, splitResultData.sidSplitSidsMap);
                    if (rule.getSplitType() == TradeSplitConfigTypeEnum.INSUFFICIENT.getType() || rule.getSplitType() == TradeSplitConfigTypeEnum.INSUFFICIENT_KIND.getType()) {
                        splitAuditAutoBusiness.splitAuditAuto(staff, tradeConfig, params.getAfterSids());
                    }
                    Long[] splitSids = SplitUtils.getResultSids(params, splitResultData);
                    if (splitSids.length > 0) {
                        Set<Long> set = new HashSet<>(Arrays.asList(splitSids));
                        if (tempSids != null && tempSids.length > 0) {
                            set.addAll(Arrays.asList(tempSids));
                        }
                        tempSids = set.toArray(new Long[0]);
                        // 继续拆分的sid
                        Set<Long> tempSet = new HashSet<>(Arrays.asList(splitSids));
                        if (needSplitSids != null && needSplitSids.length > 0) {
                            tempSet.addAll(Arrays.asList(needSplitSids));
                        }
                        needSplitSids = tempSet.toArray(new Long[0]);
                    }
                    if (needSplitSids != null && needSplitSids.length > 0) {
                        // 需要继续拆分的sid
                        Set<Long> contineSplitSids = Arrays.stream(needSplitSids).collect(Collectors.toSet());
                        // 过滤一单一包的订单的sid
                        needSplitSids = SplitUtils.filterSingleTradeSids(staff, contineSplitSids, splitResultData);
                        if (needSplitSids.length == 0) {
                            break;
                        }
                    }
                    originTrades = tradeSearchService.queryBySids(staff, true, needSplitSids);
                }
            }
            splitResult.setResultSids(tempSids);
            splitResult.parseAndSetSidSplitSidsMap(sidSplitSidsMap);
            return tempSids;
        });
        splitResult.setBeforeSplitCount(sids.length);
        splitResult.setIncreaseSplitCount(getIncreaseSplitCount(sidSplitSidsMap));
        return splitResult;
    }

    @Override
    public SplitResult splitAuto(Staff staff, Long[] sids, List<SplitConfigRule> rules, TradeSplitEnum tradeSplitEnum, EventEnum event) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        SplitResult splitResult = new SplitResult();
        Map<Long, List<Long>> sidSplitSidsMap = new HashMap<>();
        List<List<Long>> splitList = ListUtils.splitList(Arrays.asList(sids), 500);
        for (List<Long> list : splitList) {
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, false, list.toArray(new Long[0]));
            // 自动拆单过滤
            tradeList= tradeSplitFilterService.splitAutoFilter(staff,tradeList,false);
            if (CollectionUtils.isEmpty(tradeList)) {
                return splitResult;
            }
            doBeforeSplit(staff,rules);
            Long[] sidArr = TradeUtils.toSids(tradeList);
            lockService.locks(tradeLockBusiness.getERPLocks(staff, sidArr), () -> {
                Long[] tempSids = sidArr;
                List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, tempSids);
                for (SplitConfigRule rule : rules) {
                    if (tempSids.length == 0) {
                        break;
                    }
                    SplitParams params = splitFilterBusiness.filterAutoSplitTrade(staff, originTrades, splitConfigBusiness.getSplitConfig(rule), rule, tradeConfig);
                    if (params.splitDataMap.size() > 0) {
                        params.event = event;
                        SplitResultData splitResultData = splitAllInOneBusiness.split(staff, tradeSplitEnum, params);
                        if (splitResultData.updateTrades.size() > 0) {
                            Set<Long> set = new HashSet<>(Arrays.asList(tempSids));
                            List<Long> splitSids = TradeUtils.toSidList(splitResultData.updateTrades);
                            // 拆分成功的单，不允许继续拆分
                            if (!Objects.equals(rule.getAutoContinueSplit(), 1)) {
                                //拆分成功的订单不再继续匹配后面规则拆分
                                splitSids.forEach(set::remove);
                            } else {
                                // 拆封成功的单继续拆分
                                if (!CollectionUtils.isEmpty(splitResultData.insertTrades)) {
                                    set.addAll(TradeUtils.toSidSet(splitResultData.insertTrades));
                                }
                            }
                            tempSids = set.toArray(new Long[0]);
                            originTrades = tradeSearchService.queryBySids(staff, true, tempSids);
                        }
                        mergeMap(sidSplitSidsMap, params.sidSplitSidsMap);
                    }
                }
                return tempSids;
            });
        }
        splitResult.setBeforeSplitCount(sids.length);
        splitResult.setIncreaseSplitCount(getIncreaseSplitCount(sidSplitSidsMap));
        return splitResult;
    }

    private void doBeforeSplit(Staff staff, List<SplitConfigRule> rules) {
        int dmsTradeGxEditNotRelFx = tradeConfigNewService.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()?1:0;
        HashMap<String, Object> extendParams = new HashMap<>(2,1);
        extendParams.put("splitByGx",dmsTradeGxEditNotRelFx);
        rules.forEach(r->r.setExtendParams(extendParams));
    }

    private void mergeMap(Map<Long, List<Long>> sidSplitSidsMap, Map<Long, List<Long>> subSidSplitSidsMap) {
        if (subSidSplitSidsMap == null || subSidSplitSidsMap.size() == 0) {
            return;
        }
        if (sidSplitSidsMap.size() == 0) {
            sidSplitSidsMap.putAll(subSidSplitSidsMap);
            return;
        }
        for (Map.Entry<Long, List<Long>> entry : subSidSplitSidsMap.entrySet()) {
            List<Long> list = sidSplitSidsMap.get(entry.getKey());
            if (list == null) {
                sidSplitSidsMap.put(entry.getKey(), entry.getValue());
            } else {
                list.addAll(entry.getValue());
            }
        }
    }

    @Override
    public Long[] splitAuditedKind(Staff staff, Long sid, Long[] orderIds) {
        TradeSplitEnum.SPLIT_WAVE.setMsg("按播种拆分");
        Long[] resultSids = splitKind(staff, TradeSplitEnum.SPLIT_WAVE, tradeLocalConfig.isTradeSplitFx2GxCompanyIds(staff.getCompanyId()), sid, orderIds);
        if (staff.getConf().isOpenWms()) {
            eventCenter.fireEvent(this, new EventInfo("trade.split.audited.sku").setArgs(new Object[]{staff, resultSids}), resultSids);
        }
        return resultSids;
    }

    @Override
    @Deprecated
    public Long[] splitAuditedNum(Staff staff, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, TradeSplitEnum tradeSplitEnum) {
        return splitAuditedNum(staff, tradeSplitEnum, sid, splitOrders, sourceAndNewOrderIdMap, false, false);
    }

    @Override
    @Deprecated
    public Long[] splitAuditedNum(Staff staff, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, TradeSplitEnum tradeSplitEnum, boolean splitFx2GxNew) {
        return splitAuditedNum(staff, tradeSplitEnum, sid, splitOrders, sourceAndNewOrderIdMap, false, splitFx2GxNew);
    }

    @Override
    @Deprecated
    public Long[] splitAuditedNum(Staff staff, TradeSplitEnum splitEnum, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, boolean needUnAudit, boolean splitFx2GxNew) {
        Map<Long, Order> splitOrderMap = OrderUtils.toMap(splitOrders);
        Long[] resultSids = lockService.locks(tradeLockBusiness.getERPLocks(staff, sid), () -> {
            TbTrade originTrade = tbTradeDao.queryBySid(staff, sid);
            Assert.notNull(originTrade, "需要拆分的订单已不存在！");

            tradeFillService.fill(staff, originTrade);
            List<TbTrade> tbTrades = tradeSplitFilterService.filterTrades(staff, Lists.newArrayList(originTrade), false);
            if (CollectionUtils.isEmpty(tbTrades)) {
                return new Long[0];
            }
            originTrade = tbTrades.get(0);
            List<TbOrder> originTbOrders = tbOrderDAO.queryByIds(staff, splitOrderMap.keySet().toArray(new Long[0]));
            Assert.isTrue(originTbOrders.size() == splitOrders.size(), "需要拆分的子订单已不存在！");
            for (TbOrder originTbOrder : originTbOrders) {
                originTbOrder.setNum(splitOrderMap.get(originTbOrder.getId()).getSplitNum());
            }
            originTrade.setOrders(originTbOrders);

            SplitParams params = new SplitParams();
            params.splitDataMap.put(originTrade, Lists.newArrayList(originTrade));
            if (splitFx2GxNew) {
                params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
            }
            SplitResultData split = splitAllInOneBusiness.split(staff, getSplitEnum(splitEnum, originTrade), params);
            sourceAndNewOrderIdMap.putAll(params.sourceAndNewOrderIdMap);
            return SplitUtils.getResultSids(params, split);
        });

        if (staff.getConf().isOpenWms()) {
            if (needUnAudit) {
                wmsService.tradePackSplitHandleRecords(staff, resultSids);
            } else {
                eventCenter.fireEvent(this, new EventInfo("trade.split.audited.sku").setArgs(new Object[]{staff, resultSids}), resultSids);
            }
        }
        return resultSids;
    }

    @Override
    public SplitAuditedNumResponse splitAuditedNum(Staff staff, SplitAuditedNumRequest req) {
        Map<Long, Order> splitOrderMap = OrderUtils.toMap(req.getSplitOrders());
        SplitAuditedNumResponse resp = lockService.locks(tradeLockBusiness.getERPLocks(staff, req.getSid()), () -> {
            TbTrade originTrade = tbTradeDao.queryBySid(staff, req.getSid());
            if (originTrade == null) {
                return SplitAuditedNumResponse.buildFailure("需要拆分的订单已不存在！");
            }

            tradeFillService.fill(staff, originTrade);
            List<TbTrade> tbTrades = tradeSplitFilterService.filterTrades(staff, Lists.newArrayList(originTrade), false);
            if (CollectionUtils.isEmpty(tbTrades)) {
                return SplitAuditedNumResponse.buildFailure("过滤后没有需要拆分的订单");
            }
            originTrade = tbTrades.get(0);
            List<TbOrder> originTbOrders = tbOrderDAO.queryByIds(staff, splitOrderMap.keySet().toArray(new Long[0]));
            if (originTbOrders.size() != req.getSplitOrders().size()) {
                return SplitAuditedNumResponse.buildFailure("需要拆分的子订单已不存在！");
            }
            for (TbOrder originTbOrder : originTbOrders) {
                originTbOrder.setNum(splitOrderMap.get(originTbOrder.getId()).getSplitNum());
            }
            originTrade.setOrders(originTbOrders);

            SplitParams params = new SplitParams();
            params.splitDataMap.put(originTrade, Lists.newArrayList(originTrade));
            if (req.getSplitFx2GxNew()) {
                params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
            }
            SplitResultData split = splitAllInOneBusiness.split(staff, getSplitEnum(req.getSplitEnum(), originTrade), params);
            Long[] resultSids = SplitUtils.getResultSids(params, split);
            if (resultSids.length <= 1) {
                String msg = String.format("没有拆分出去的订单, resultSids: %s", Joiner.on(",").join(resultSids));
                Logs.warn(LogHelper.buildLog(staff, msg).toString());
                return SplitAuditedNumResponse.buildFailure(msg);
            }
            return SplitAuditedNumResponse.buildSuccess(
                    resultSids,
                    SplitAuditedNumResponse.buildOrderIdToMainSid(
                            split.insertTrades, split.insertOrders,
                            split.updateTrades, split.updateOrders),
                    params.sourceAndNewOrderIdMap);
        });

        Long[] resultSids = resp.getResultSids();
        if (staff.getConf().isOpenWms() && resp.getSuccess() && resultSids != null) {
            if (req.getNeedUnAudit()) {
                wmsService.tradePackSplitHandleRecords(staff, resultSids);
            } else {
                eventCenter.fireEvent(this, new EventInfo("trade.split.audited.sku").setArgs(new Object[]{staff, resultSids}), resultSids);
            }
        }
        return resp;
    }

    /**
     * @param sid         原系统订单号，运单号留在这个订单
     * @param splitOrders 拆分出去的订单
     */
    @Override
    public SplitPickResponse splitPick(Staff staff, Long sid, List<Order> splitOrders) {
        Map<Long, Order> splitOrderMap = OrderUtils.toMap(splitOrders);
        try {
            return lockService.locks(tradeLockBusiness.getERPLocks(staff, sid), () -> {
                TbTrade originTrade = tbTradeDao.queryBySid(staff, sid);
                if(CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(originTrade.getSource())){
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("抖音厂商代发订单不允许拣选拆分,sid=%s", sid)));
                    return SplitPickResponse.buildResponse(sid, null, false, "抖音厂商代发订单不允许拣选拆分");
                }
                tradeFillService.fill(staff, originTrade);
                if (StringUtils.indexOf(originTrade.getTagIds(), SystemTags.TAG_SALE_TRDE_PRE_ALLOCATE_GOODS.getId() + "") >= 0) {
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("预配货订单不允许拣选拆分,sid=%s", sid)));
                    return SplitPickResponse.buildResponse(sid, null, false, "预配货订单不允许拣选拆分");
                }

                StringBuilder log = new StringBuilder("拣选拆分，sid=").append(sid).append(",orders=");
                List<TbOrder> originOrders = tbOrderDAO.queryByIds(staff, splitOrderMap.keySet().toArray(new Long[0]));
                Assert.isTrue(originOrders.size() == splitOrders.size(), "需要拆分的子订单已不存在！");
                originOrders.forEach(order -> {
                    order.setOldNum(order.getNum());
                    order.setSplitNum(splitOrderMap.get(order.getId()).getSplitNum());
                    order.setNum(splitOrderMap.get(order.getId()).getSplitNum());
                    log.append("[orderId=").append(order.getId()).append(",num=").append(order.getNum()).append(",splitNum=").append(order.getSplitNum()).append("],");
                });
                Logs.ifDebug(LogHelper.buildLog(staff, log.toString()));

                originTrade.setOrders(originOrders);

                SplitParams params = new SplitParams();
                params.splitDataMap.put(originTrade, Lists.newArrayList(originTrade));
                splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_PICK, params);

                SplitPickResponse response = splitMergeOutSidBusiness.splitPickOutSid(staff, originTrade, originOrders, params.sidSplitSidsMap);
                if (response.isSuccess()) {
                    tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_PICK, new Long[]{response.getUnPickData().getSid()});
                }
                return response;
            });
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("拣选拆分失败，sid=%s", sid)), e);
            return SplitPickResponse.buildResponse(sid, null, false, "拣选拆分报错：" + e.getMessage());
        }
    }

    private static TradeSplitEnum getSplitEnum(TradeSplitEnum tradeSplitEnum, TbTrade originTrade) {
        if (originTrade == null || tradeSplitEnum == null) {
            return tradeSplitEnum;
        }
        if (originTrade.getWaveId() != null && originTrade.getWaveId() == -5L) {
            tradeSplitEnum.setMsg("按唯一码拆分");
            return tradeSplitEnum;
        }
        if (originTrade.getWaveId() == null || originTrade.getWaveId() == 0L) {
            if ("按播种拆分".equals(tradeSplitEnum.getMsg())) {
                tradeSplitEnum.setMsg("更换商品拆分");
            }
            return tradeSplitEnum;
        }
        return tradeSplitEnum;
    }

    private Long[] splitKind(Staff staff, TradeSplitEnum tradeSplitEnum, boolean splitFx2GxNew, Long sid, Long... orderIds) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sid), () -> {
            TbTrade originTrade = tbTradeDao.queryBySid(staff, sid);
            Assert.notNull(originTrade, "需要拆分的订单已不存在！");
            Assert.isTrue(!supportQimenTradeForbidSplit(staff, originTrade), "已开启“奇门订单不支持拆单”配置，本单为奇门订单不支持拆单");

            tradeFillService.fill(staff, originTrade);
            tradeSplitFilterService.filterTrades(staff, Lists.newArrayList(originTrade), true);
            List<TbOrder> tbOrders = originTrade.getMergeSid() < 0 ? tbOrderDAO.queryBySids(staff, sid) : tbOrderDAO.queryBySids(staff, TradeUtils.toSids(tbTradeDao.queryByMergeSids(staff, originTrade.getMergeSid())));
            if (tbOrders == null || tbOrders.size() <= 1) {
                throw new IllegalArgumentException("只有一个子订单，无法按种类拆分！");
            }
            tbOrderDAO.fillOrderExt(staff,tbOrders);
            // 填充异常信息到order
            tbOrders.forEach(order->order.setExceptData(TradeExceptUtils.getTradeExceptData(originTrade)));
            originTrade.setOrders(tbOrders);
            List<Long> orderIdList = Arrays.asList(orderIds);
            List<TbOrder> originTbOrders = tbOrders.stream().filter(o -> orderIdList.contains(o.getId())).collect(Collectors.toList());
            if (originTbOrders.size() == 0) {
                throw new IllegalArgumentException("需要拆分的子订单已不存在！");
            }
            SplitParams params = new SplitParams();
            params.splitPlatGiftFollowMainOrder=false;
            Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
            TradeUtils.getOrders4Trade(splitTrade).addAll(originTbOrders);
            params.splitDataMap.put(originTrade, Lists.newArrayList(splitTrade));
            if (splitFx2GxNew) {
                params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
            }
            Long[] resultSids = SplitUtils.getResultSids(params, splitAllInOneBusiness.split(staff, tradeSplitEnum, params));
            if(resultSids.length>0){
                List<Trade> tradeTraces = PlatGiftSplitUtils.getTradeTraces(staff, originTbOrders, tbOrders);
                tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.SPLIT);
            }
            return resultSids;
        });
    }

    @Override
    public List<Trade> splitUndo(Staff staff, Long splitSid, boolean shopeeUndo) {
        return splitUndoBusiness.splitUndo(staff, splitSid, shopeeUndo);
    }

    @Override
    public SplitResult splitInactiveItem(Staff staff, Long[] originSids, Set<String> inactiveItemKeys) {
        SplitParams filterData = splitFilterBusiness.filterInactiveItem(staff, originSids, inactiveItemKeys);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        if (MapUtils.isEmpty(filterData.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.filterInactiveItem(staff, filterData.getSids(), inactiveItemKeys);
            params.isContinue = true;  //有异常订单跳过，继续拆分。
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_ITEM_INACTIVE, params), originSids.length);
        });
    }

    private SplitResult buildSplitResult(SplitParams params, SplitResultData splitResultData, int beforeSplitCount) {
        SplitResult splitResult = SplitResult.builder()
                .beforeSplitCount(beforeSplitCount)
                .increaseSplitCount(getIncreaseSplitCount(params.sidSplitSidsMap))
                .resultSids(SplitUtils.getResultSids(params, splitResultData))
                .insertSids(TradeUtils.toSidList(splitResultData.insertTrades))
                .updateSids(TradeUtils.toSidList(splitResultData.updateTrades))
                .mergeUndoSids(TradeUtils.toSidList(splitResultData.mergeUndoTrades))
                .build();
        splitResult.parseAndSetSidSplitSidsMap(splitResultData.sidSplitSidsMap);
        return splitResult;
    }

    private int getIncreaseSplitCount(Map<Long, List<Long>> sidSplitSidsMap) {
        int beforeSize = sidSplitSidsMap.size();
        int aferSize = 0;
        for (List<Long> sids : sidSplitSidsMap.values()) {
            aferSize += sids.size();
        }
        return Math.max(aferSize - beforeSize, 0);
    }

    /**
     * 按箱规则拆单
     *
     * @param staff
     * @param originSids
     * @param isIncludeSplit
     * @param isIncludeMerge
     * @return
     */
    @Override
    public SplitResult splitDefaultBoxRule(Staff staff, Long[] originSids, Boolean isIncludeSplit, Boolean isIncludeMerge) {
        SplitParams filterData = splitFilterBusiness.filterDefaultBoxRule(staff, originSids, isIncludeSplit, isIncludeMerge);
        if (filterData.sids.size() == 0) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        if (MapUtils.isEmpty(filterData.splitDataMap)) {
            return SplitResult.builder().resultSids(originSids).beforeSplitCount(originSids.length).build();
        }
        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.filterDefaultBoxRule(staff, originSids, isIncludeSplit, isIncludeMerge);
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.DEFAULT_BOX_RULE, params), originSids.length);
        });
    }

    /**
     * 取消拆单 重新匹配赠品，仓库，快递模板
     *
     * @param staff
     * @param originTrades
     */
    @Override
    public void splitUndoRecalculationUpdate(Staff staff, List<Trade> originTrades) {
        if (CollectionUtils.isEmpty(originTrades)) {
            return;
        }
        // 只重新匹配主单的
        List<Long> sids = originTrades.stream().filter(trade -> trade.getEnableStatus() == 1).map(Trade::getSid).collect(Collectors.toList());
        List<Long> removeSids = originTrades.stream().filter(trade -> trade.getEnableStatus() == 0).map(Trade::getSid).collect(Collectors.toList());
        if (sids.size() > 0) {
            //取消拆单后删除赠品匹配记录
            giftDeductCountBusiness.returnGiftCountBySids(staff, removeSids);
            //取消拆单后新版标签重算。
            reMatchBusiness.reMatch(staff, sids, EventEnum.EVENT_TRADE_MERGE_TRADE_LABEL, Boolean.FALSE);
            //取消拆单后重新匹配
            reMatchBusiness.reMatch(staff, sids, removeSids, null,  EventEnum.EVENT_TRADE_SPLIT_UNDO_AFTER, Boolean.FALSE);
        }
    }

    /**
     * 批量混合拆分
     */
    @Override
    public SplitResult splitMixBatch(Staff staff, Map<Trade, String> tradeGroupCount, boolean splitFx2GxNew) {
        if (tradeGroupCount == null || tradeGroupCount.size() == 0) {
            return null;
        }
        Set<Long> sids = tradeGroupCount.keySet().stream().map(Trade::getSid).collect(Collectors.toSet());
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids.toArray(new Long[0])), () -> {
            List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sids.toArray(new Long[0]));
            if (CollectionUtils.isEmpty(tbTrades)) {
                Assert.notNull(tbTrades, String.format("sids=%s 需要拆分的订单已不存在！", sids));
            }
            // 查询出来的sids
            Set<Long> collect = tbTrades.stream().map(TbTrade::getSid).collect(Collectors.toSet());
            // 没有被查出来的原始sid
            Set<Long> insuficientSids = sids.stream().filter(e -> !collect.contains(e)).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(insuficientSids)) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("未找到sids= %s 需要拆分的订单！ ", insuficientSids)));
            }

            tradeFillService.fill(staff, tbTrades);
            tbTrades = tradeSplitFilterService.filterTrades(staff, tbTrades, false);
            Map<Long, TbTrade> originTradeMap = tbTrades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (v1, v2) -> v2));
            List<String> logs = new ArrayList<>();
            SplitParams params = new SplitParams();
            SplitConfigRule splitConfigRule = new SplitConfigRule();
            HashMap<String, Object> extendParams = Maps.newHashMapWithExpectedSize(2);
            extendParams.put("splitByGx", TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()?1:0);
            splitConfigRule.setExtendParams(extendParams);
            params.splitConfigRule = splitConfigRule;
            for (Map.Entry<Trade, String> entry : tradeGroupCount.entrySet()) {
                StringBuilder sb = new StringBuilder();
                Trade trade = entry.getKey();
                TbTrade originTrade = originTradeMap.get(trade.getSid());
                if (originTrade == null) {
                    sb.append("混合拆分，sid=").append(trade.getSid()).append(" 订单不存在,");
                    continue;
                }
                String groupCount = entry.getValue();
                sb.append("混合拆分参数，sid=").append(trade.getSid()).append(",groupCount=").append(groupCount);
                Integer[] groupCountArr = ArrayUtils.toIntegerArray(groupCount);
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                List<Trade> splitTrades = new ArrayList<>();
                for (Integer count : groupCountArr) {
                    List<Order> splitOrders = new ArrayList<>();
                    for (int i = 0; i < count; i++) {
                        Order splitOrder = orders.remove(0);
                        sb.append(",orderId=").append(splitOrder.getId()).append(",num=").append(splitOrder.getNum()).append(",splitNum=").append(splitOrder.getSplitNum());
                        if (splitOrder.getSplitNum() > 0) {
                            splitOrder.setSourceId(splitOrder.getId());
                            splitOrder.setNum(splitOrder.getSplitNum());
                            splitOrders.add(splitOrder);
                        }
                    }
                    if (splitOrders.size() > 0) {
                        TbTrade splitTrade = new TbTrade();
                        TradeUtils.setOrders(splitTrade, splitOrders);
                        splitTrades.add(splitTrade);
                    }
                }
                if (splitTrades.size() == 0) {
                    sb.append(", splitTrades.size() == 0 参数不正确，无法混合拆分！");
                    continue;
                }
                if (sb.length() > 0) {
                    logs.add(sb.toString());
                }
                params.splitDataMap.put(originTrade, splitTrades);
            }
            params.stockUse = 1;
            if (CollectionUtils.isNotEmpty(logs)) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.join(";", logs)));
            }
            if (splitFx2GxNew) {
                params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
            }
            SplitResultData split = splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_MIX, params);
            return buildSplitResult(params, split, 1);
        });
    }

    /**
     * 批量拆分抖音自动流转订单
     *
     * @param staff
     * @param sids
     */
    @Override
    public SplitResult splitFxgAutoLzBatch(Staff staff, Long[] sids) {
        if (org.apache.commons.lang3.ArrayUtils.isEmpty(sids)) {
            return SplitResult.builder().resultSids(sids).beforeSplitCount(sids.length).build();
        }

        SplitParams filterData = splitFilterBusiness.filterFxgAutoLz(staff, sids);
        if (CollectionUtils.isEmpty(filterData.sids)) {
            return SplitResult.builder().resultSids(sids).beforeSplitCount(sids.length).build();
        }

        return lockService.locks(filterData.locks, () -> {
            SplitParams params = splitFilterBusiness.getFxgAutoLz(staff, filterData.getSids());
            return buildSplitResult(params, splitAllInOneBusiness.split(staff, TradeSplitEnum.SPLIT_MIX, params), sids.length);
        });
    }

    @Override
    public List<SplitCheckResult> splitPlatGiftCheck(Staff staff, List<Long> orderIds) {
        List<SplitCheckResult> splitCheckResults = new ArrayList<>();
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TradeConfigEnum.SPLIT_PLAT_GIFT_FOLLOW_MAIN_ORDER);
        if(!Objects.equals(tradeConfigNew.getConfigValue(),"1")){
            return  splitCheckResults;
        }
        List<TbOrder> tbOrders = tbOrderDAO.queryByIds(staff, orderIds.toArray(new Long[0]));
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, OrderUtils.toSids(tbOrders));
        List<Order> originOrders = TradeUtils.getOrders4Trade(tradeList);
        for (Long orderId : orderIds) {
            SplitCheckResult splitCheckResult = PlatGiftSplitUtils.getSplitCheckResult(staff, orderId, originOrders);
            if (splitCheckResult != null) {
                splitCheckResults.add(splitCheckResult);
            }
        }
        return splitCheckResults;
    }

    /**
     * 判断奇门订单是否支持拆单
     */
    public Boolean supportQimenTradeForbidSplit(Staff staff, TbTrade trade) {
        TradeConfigNew qimenForbidSplit = tradeConfigNewService.get(staff, TradeConfigEnum.QIMEN_ORDER_FORBID_SPLIT);
        if (trade.getUserId() == null) {
            return false;
        }
        Shop shop = shopService.queryByUserId(staff, trade.getUserId());
        boolean noSplitFlag = qimenForbidSplit.getConfigValue() != null && Integer.parseInt(qimenForbidSplit.getConfigValue()) == 1;
        return noSplitFlag && shop != null && "qimen".equals(shop.getSource());
    }



    @Override
    public SplitResult splitOZONOrder(Staff staff, Trade trade) {
        TbTrade tbTrade = tbTradeDao.queryBySid(staff, trade.getSid());
        if(tbTrade==null){
            throw new RuntimeException(String.format("%s订单不存在，不能操作",trade.getSid() ));
        }
        if (!Objects.equals(tbTrade.getSource(), CommonConstants.PLAT_FORM_TYPE_OZON)) {
            throw new RuntimeException(String.format("非%s订单，不能操作", CommonConstants.PLAT_FORM_TYPE_OZON));
        }
        Assert.notNull(tbTrade, "需要拆分的订单已不存在！");
        Map<User, String> lockRst = lockService.locks(tradeLockBusiness.getERPLocks(staff, trade.getSid()), () -> {
            Trade originTrade = tradeSearchService.queryBySid(staff, true, trade.getSid());
            User user = staff.getUserByUserId(originTrade.getUserId());
            Assert.notNull(originTrade, "需要拆分的订单已不存在！");
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            List<Order> originOrders4Trade = TradeUtils.getOrders4Trade(originTrade);
            Map<Long, Order> originOrderMap = originOrders4Trade.stream().collect(Collectors.toMap(Order::getId, Function.identity(), (v1, v2) -> v2));
            List<SplitPlatformTradeDTO.SpiltItem> spiltItemList = new ArrayList<>();
            int allSplit=0;
            for (Order order : orders) {
                Order originOrder = originOrderMap.get(order.getId());
                Integer num = order.getNum();
                Integer splitNum = order.getSplitNum();
                if (originOrder != null) {
                    SplitPlatformTradeDTO.SpiltItem spiltItem = new SplitPlatformTradeDTO.SpiltItem();
                    if (num < splitNum) {
                        throw new RuntimeException(String.format("Ozon订单拆分数量大于原订单数量，sid=%s", originTrade.getSid()));
                    }
                    if (num - splitNum == 0) {
                        allSplit++;
                    }
                    originOrder.setNum(num - splitNum);
                    spiltItem.setSkuId(originOrder.getSkuId());
                    spiltItem.setNumIid(originOrder.getNumIid());
                    spiltItem.setOuterId(originOrder.getOuterIid());
                    spiltItem.setNum(splitNum);
                    spiltItemList.add(spiltItem);
                }
            }
            if (allSplit == orders.size()) {
                throw new RuntimeException(String.format("所有order都被拆分出去了，不允许拆，sid=%s", originTrade.getSid()));
            }
            if(CollectionUtils.isEmpty(spiltItemList)){
                Logs.debug(LogHelper.buildLog(staff, String.format("spiltItemList为空,不能拆，sid=%s",trade.getSid())));
                return new HashMap<>();
            }
            SplitPlatformTradeDTO splitPlatformTradeDto = new SplitPlatformTradeDTO();
            splitPlatformTradeDto.setTid(originTrade.getTid());
            splitPlatformTradeDto.setSpiltItemList(spiltItemList);
            tradeUpdateBusiness.update(staff, originTrade, false, false, false);
            // 灰度三日志
            Logs.debug(LogHelper.buildLog(staff, String.format("splitPlatformTradeDto:%s", JSONObject.toJSONString(splitPlatformTradeDto))));
            PlatformResponse<SplitPlatformTradeRespDTO> response = platformTradeSplitBusiness.splitPlatformTrade(user, splitPlatformTradeDto);
            String code = response.getCode();
            String msg = response.getMsg();
            String subCode = response.getSubCode();
            String subMsg = response.getSubMsg();
            Map<User, String> rstMap = new HashMap<>();
            SplitPlatformTradeRespDTO data = Optional.ofNullable( response.getData()).orElse(new SplitPlatformTradeRespDTO());
            String newTid = data.getNewTid();
            Logs.debug(LogHelper.buildLog(staff, String.format("code:%s, subCode:%s, msg:%s,subMsg:%s,newTid:%s",code,subCode,msg,subMsg, newTid)));
            if (!response.isSuccess() || StringUtils.isBlank(newTid)) {
                throw new RuntimeException(String.format("Ozon订单拆分失败 code:%s, subCode:%s, msg:%s,subMsg:%s", code, subCode, msg, subMsg));
            }
            rstMap.put(user, newTid);
            return rstMap;
        });
        SplitResult splitResult = new SplitResult();
        splitResult.setSplitNewTids(new ArrayList<>());
        splitResult.getSplitNewTids().add(trade.getTid());
        splitResult.setResultSids(new Long[]{trade.getSid()});
        if (MapUtils.isEmpty(lockRst)) {
            return splitResult;
        }

        if (MapUtils.isNotEmpty(lockRst)) {
            List<String> tids = new ArrayList<>();
            Set<Map.Entry<User, String>> entries = lockRst.entrySet();
            for (Map.Entry<User, String> entry : entries) {
                splitResult.getSplitNewTids().add(entry.getValue());
                User user = entry.getKey();
                tids.add(user.getId() + "/" + entry.getValue());
            }
            bufferService.buffer(Buffers.build(staff, TradeEvents.TRADE_DOWNLOAD), tids);
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_DOWNLOAD).setArgs(new Object[]{staff}), null);
        }
        return splitResult;
    }

}

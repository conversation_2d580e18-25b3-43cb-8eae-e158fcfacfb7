package com.raycloud.dmj.services.logistics;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.ITradeLogisticsTrackingServices;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * TradeLogisticeTrackingService
 *
 * <AUTHOR>
 * @Date 2019-08-03
 * @Time 16:05
 */
@Service
public class TradeLogisticsTrackingAdapter implements InitializingBean, ApplicationContextAware {


    private ApplicationContext applicationContext;

    private List<ITradeLogisticsTrackingServices> servicesList;

    @Resource
    private StaffAssembleBusiness staffAssembleBusiness;

    @Override
    public void afterPropertiesSet() throws Exception {
        servicesList = new ArrayList<>();
        String[] beanNamesForType = applicationContext.getBeanNamesForType(ITradeLogisticsTrackingServices.class);
        for (String name : beanNamesForType) {
            servicesList.add(applicationContext.getBean(name, ITradeLogisticsTrackingServices.class));
        }
        Collections.sort(servicesList, (o1, o2) -> o1.getOrder() == o2.getOrder() ? 0 : o1.getOrder() > o2.getOrder() ? -1 : 1);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public void invokeLogisticsInfo(Staff staff) {
        invokeLogisticsInfo(staff, false);
    }

    public boolean needinvokeLogistics(Long companyId){
        Staff defaultStaff = staffAssembleBusiness.getDefaultStaff(companyId);
        for (ITradeLogisticsTrackingServices trackingServices : servicesList) {
            if (trackingServices.isAllow(defaultStaff)) {
                return true;
            }
        }
        return false;
    }

    public void invokeLogisticsInfo(Staff staff, boolean force) {
        Staff defaultStaff = staffAssembleBusiness.getDefaultStaff(staff.getCompanyId());
        for (ITradeLogisticsTrackingServices trackingServices : servicesList) {
            if (force || trackingServices.isAllow(defaultStaff)) {
                try {
                    trackingServices.invokeLogisticsInfo(defaultStaff);
                } catch (Exception e) {
                    Logs.error(trackingServices.getClass().getName()+"物流查询失败" + e.getMessage(), e);
                } finally {
                    trackingServices.end(defaultStaff);
                }
            }
        }
    }
}

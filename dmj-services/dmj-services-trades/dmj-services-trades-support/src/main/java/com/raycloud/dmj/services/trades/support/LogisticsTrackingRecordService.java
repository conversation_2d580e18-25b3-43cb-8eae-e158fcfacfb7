package com.raycloud.dmj.services.trades.support;

import com.kdzs.wly.domain.LogisticsTrace;
import com.kdzs.wly.domain.LogisticsTraceDetail;
import com.kdzs.wly.enums.ExpressPacketStatusEnum;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.trade.TradePddTradeBusiness;
import com.raycloud.dmj.dao.trade.LogisticsTrackingPollPoolDao;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.LogisticsTrackingPollPool;
import com.raycloud.dmj.domain.trades.LogisticsTrackingPollPoolQueryParams;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.trades.ILogisticsTrackingRecordService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.TradeLogisticsTrackingUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 发货记录服务
 *
 * <AUTHOR>
 */
@Service
public class LogisticsTrackingRecordService implements ILogisticsTrackingRecordService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    LogisticsTrackingPollPoolDao logisticsTrackingPollPoolDao;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    TradePddTradeBusiness tradePddTradeBusiness;

    @Resource
    ITradeFillService tradeFillService;

    @Override
    public List<LogisticsTrackingPollPool> listLogisticsExcept(Staff staff, LogisticsTrackingPollPoolQueryParams params) {
        fillNullSort(params);
        List<LogisticsTrackingPollPool> consignRecords = logisticsTrackingPollPoolDao.listLogisticsExcept(staff, params);

        tradeFillService.fill(staff,consignRecords);
        tradePddTradeBusiness.pddMaskDataReplace(staff, consignRecords);
        secretBusiness.decodeTrades(staff, consignRecords);
        return consignRecords;
    }

    @Override
    public List<LogisticsTrackingPollPool> exceptLogisticsQueryBysids(Staff staff, Long... sids) {
        List<LogisticsTrackingPollPool> pollPools = logisticsTrackingPollPoolDao.queryExceptLogisticsByIds(staff, sids);
        if (CollectionUtils.isNotEmpty(pollPools)) {
            fullTrades(pollPools, getTrades(staff, pollPools));
        }
        return pollPools;
    }

    private List<Trade> getTrades(Staff staff, List<LogisticsTrackingPollPool> pollPools) {
        if (CollectionUtils.isEmpty(pollPools)) {
            return new ArrayList<>();
        }
        return tradeSearchService.queryBySids(staff, false, pollPools.stream().map(LogisticsTrackingPollPool::getSid).toArray(Long[]::new));
    }

    private void fullTrades(List<LogisticsTrackingPollPool> pollPools, List<Trade> trades) {
        if (CollectionUtils.isEmpty(pollPools)) {
            return;
        }
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
        for (LogisticsTrackingPollPool pollPool : pollPools) {
            TradeLogisticsTrackingUtils.fullTrade(pollPool, tradeMap.get(pollPool.getSid()));
        }
    }


    @Override
    public Integer countListLogisticsExcept(Staff staff, LogisticsTrackingPollPoolQueryParams params) {
        return logisticsTrackingPollPoolDao.countlistLogisticsExcept(staff, params);
    }

    private void fillNullSort(LogisticsTrackingPollPoolQueryParams params) {
        if (params.getSort() == null) {
            params.setSort(new Sort("consign_time", "DESC"));
        }
        params.getSort().setField("consign_time");
        if (params.getSort().getOrder() == null || params.getSort().getOrder().trim().isEmpty()) {
            params.getSort().setOrder("DESC");
        }
        if (params.getEnableStatus() == null) {
            params.setEnableStatus(1);
        }

    }

    @Override
    public boolean synLogisticsInfo(Staff staff) {
//        Page page = new Page();
//        int pageNo = 1;
//        page.setPageSize(500);
//        LogisticsTrackingPollPoolQueryParams queryParams = new LogisticsTrackingPollPoolQueryParams();
//        queryParams.setPage(page);
//        List<LogisticsTrackingPollPool> logisticsInfos;
//        int totalCount = 0;
//        int succCount = 0;
//        for (; ; ) {
//            page.setPageNo(pageNo);
//            logisticsInfos = logisticsTrackingPollPoolDao.queryUnsignedLogisticsRecord(staff, queryParams);
//            if (CollectionUtils.isEmpty(logisticsInfos)) {
//                break;
//            }
//            Map<Long, Trade> tradeMap = getTrades(staff, logisticsInfos).stream().filter(t -> StringUtils.equals(Trade.SYS_STATUS_SELLER_SEND_GOODS, t.getSysStatus()) || StringUtils.equals(Trade.SYS_STATUS_FINISHED, t.getSysStatus())).collect(Collectors.toMap(Trade::getSid, Function.identity(), (a, b) -> b));
//            List<LogisticsTrackingPollPool> syncLogisticsInfoList = new ArrayList<>();
//            for (LogisticsTrackingPollPool logisticsInfo : logisticsInfos) {
//                Trade trade = tradeMap.get(logisticsInfo.getSid());
//                if (trade != null) {
//                    TradeLogisticsTrackingUtils.fullTrade(logisticsInfo, trade);
//                    syncLogisticsInfoList.add(logisticsInfo);
//                }
//            }
//            if (CollectionUtils.isEmpty(syncLogisticsInfoList)) {
//                pageNo++;
//                continue;
//            }
//
//            List<LogisticsTrackingPollPool> updateLogisticsList = createLogisticsRecord(staff, syncLogisticsInfoList);
//            if (CollectionUtils.isNotEmpty(updateLogisticsList)) {
//                succCount += logisticsTrackingPollPoolDao.batchUpdate(staff, updateLogisticsList);
//            }
//            totalCount += syncLogisticsInfoList.size();
//            pageNo++;
//        }
//        logger.debug(LogHelper.buildLogHead(staff).append(" 跟新总记录数:").append(totalCount).append(" 跟新成功记录数:").append(succCount));

        return true;
    }

    @Override
    public void updateLogisticsExceptTrackingStatus(Staff staff, Long sid, Integer needTracking) {
        logisticsTrackingPollPoolDao.updateLogisticsExceptTrackingStatus(staff, sid, needTracking);
    }

    @Override
    public Map<String, Object> batchUpdateLogisticsExceptTrackingStatus(Staff staff, Long[] sids, Integer needTracking) {
        int errorNum = 0;
        int successNum = 0;
        List<Long> successSids = new ArrayList<>(sids.length);
        LogisticsTrackingPollPool pollPool = null;
        for (Long sid : sids) {
            try {
                pollPool = logisticsTrackingPollPoolDao.getLogisticsExceptBySidAndNeedTracking(staff, sid, needTracking);
                if (pollPool == null) {
                    int size = logisticsTrackingPollPoolDao.updateLogisticsExceptTrackingStatus(staff, sid, needTracking);
                    if (size > 0) {
                        successSids.add(sid);
                        successNum++;
                        continue;
                    }
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append(" 批量修改异常物流跟踪关注状态异常！"), e);
            }
            errorNum++;
        }
        StringBuilder message = new StringBuilder();
        message.append("成功").append(successNum).append("条,");
        message.append("失败").append(errorNum).append("条");

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", message.toString());
        resultMap.put("successSids", successSids);
        return resultMap;
    }


    /**
     * 获取物流信息状态权重
     *
     * @param logisticsStatus
     * @return
     */
    private Integer getLogisticsStatusWeight(String logisticsStatus) {
        if (StringUtils.isEmpty(logisticsStatus)) {
            return -1;
        }
        for (ExpressPacketStatusEnum status : ExpressPacketStatusEnum.values()) {
            if (status.getValue().equals(logisticsStatus)) {
                return Integer.valueOf(status.getKey());
            }
        }
        return -1;
    }

    private String getLogisticsStatus(LogisticsTrace trace) {
        String logisticsStatus = trace.getLogisticsStatus();
        List<LogisticsTraceDetail> detailList = trace.getLogisticsTraceDetailList();
        if (CollectionUtils.isNotEmpty(detailList) && getLogisticsStatusWeight(detailList.get(detailList.size() - 1).getLogisticsStatus()) > getLogisticsStatusWeight(logisticsStatus)) {
            logisticsStatus = detailList.get(detailList.size() - 1).getLogisticsStatus();
        }
        return logisticsStatus;
    }
}

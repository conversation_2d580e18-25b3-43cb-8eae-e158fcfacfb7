package com.raycloud.dmj.services.trades.support.bigbag;

import com.google.api.client.util.Maps;
import com.raycloud.dmj.business.export.TradeExportAbstractHandler;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @description:跨境组包工厂
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @time: 2022/1/24 7:31 下午
 */
public class CombineParcelFactory {

    private static Map<String, CombineParcelAbstractHandler> strategyMap = Maps.newHashMap();

    public static CombineParcelAbstractHandler getInvokeStrategy(String source) {
        return strategyMap.get(source);
    }

    public static void register(String name, CombineParcelAbstractHandler handler) {
        if (StringUtils.isEmpty(name) || null == handler) {
            return;
        }
        strategyMap.put(name, handler);
    }

}

package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.business.stock.OrderKeepInsufficientBusiness;
import com.raycloud.dmj.business.stock.StockOrderFillPropBusiness;
import com.raycloud.dmj.business.trade.StockTradeRouteBusiness;
import com.raycloud.dmj.business.trade.TradeStockData;
import com.raycloud.dmj.domain.TradeStockContent;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.stock.IStockTradeRouteService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-08-29 15:09
 * @Description 选择正确的business执行对应的业务
 */
@Service
public class StockTradeRouteService implements IStockTradeRouteService {

    @Resource
    StockTradeRouteBusiness stockTradeRouteBusiness;

    @Resource
    private TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    TradeConfigService tradeConfigService;

    @Resource
    StockOrderFillPropBusiness stockOrderFillPropBusiness;


    @Override
    public List<StockOrderRecord> apply(Staff staff, TradeStockContent data) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return stockTradeRouteBusiness.apply(staff, TradeStockContentUtils.transferTradeStockData(staff, tradeLocalConfigurable, tradeConfig, data));
    }

    @Override
    public List<StockOrderRecord> resume(Staff staff, TradeStockContent data) {
        TradeStockData tradeStockData = new TradeStockData();
        tradeStockData.addNotApplyRequests(staff, OrderUtils.toFullOrderList(data.getRequestOrders(), false));
        return stockTradeRouteBusiness.resume(staff, tradeStockData);
    }

    @Override
    public List<StockOrderRecord> consume(Staff staff, TradeStockContent data) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return stockTradeRouteBusiness.consume(staff, TradeStockContentUtils.transferTradeStockData(staff, tradeLocalConfigurable, tradeConfig, data));
    }

    @Override
    public List<StockOrderRecord> audit(Staff staff, TradeStockContent data) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return stockTradeRouteBusiness.audit(staff, TradeStockContentUtils.transferTradeStockData(staff, tradeLocalConfigurable, tradeConfig, data));
    }

    @Override
    public List<StockOrderRecord> unAudit(Staff staff, TradeStockContent data) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return stockTradeRouteBusiness.unaudit(staff, TradeStockContentUtils.transferTradeStockData(staff, tradeLocalConfigurable, tradeConfig, data));
    }

    @Override
    public List<Order> changeStock(Staff staff, List<Order> orders, String operation, boolean containVirtual) {
        OrderKeepInsufficientBusiness.keepInsufficientStockStatus(staff, orders);
       /* tradeConfigStockBusiness.fillOrderScalping(staff, orders);
        // 填充快递异常
        tradeConfigStockBusiness.fillOrderUnattainabl(staff,orders);*/
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeStockData data = OrderStockUtils.filterUpdateStockOrders(staff, orders, tradeLocalConfigurable, containVirtual, tradeConfig);
        if (data.requestOrders().size() > 0) {
            List<Order> norMalOrders = data.requestOrders().stream().filter(order -> !OrderUtils.isFxOrMixOrder(order)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(norMalOrders)) {
                //先归还
                stockTradeRouteBusiness.resume(staff, data);
                //再申请
                OrderStockUtils.handleApplyStock(staff, orders, stockTradeRouteBusiness.apply(staff, data), operation, !staff.openAuditActiveStockRecord(), tradeConfig);
            }
        }
        return orders;
    }
}

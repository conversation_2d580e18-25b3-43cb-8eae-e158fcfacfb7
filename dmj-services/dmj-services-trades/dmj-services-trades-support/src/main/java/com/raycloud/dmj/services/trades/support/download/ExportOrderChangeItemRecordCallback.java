package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.business.modify.OrderChangeItemRecordBusiness;
import com.raycloud.dmj.dao.params.QueryOrderChangeItemRecordParam;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.response.OrderChangeItemRecordVo;
import org.springframework.context.ApplicationContext;

import java.util.List;

public class ExportOrderChangeItemRecordCallback implements IDownloadCenterCallback {

    private final Staff staff;

    private final QueryOrderChangeItemRecordParam param;

    private final ExportHelper exportHelper;

    private final OrderChangeItemRecordBusiness orderChangeItemRecordBusiness;

    public ExportOrderChangeItemRecordCallback(Staff staff, QueryOrderChangeItemRecordParam param, ExportHelper exportHelper, ApplicationContext applicationContext) {
        this.staff = staff;
        this.param = param;
        this.exportHelper = exportHelper;
        this.orderChangeItemRecordBusiness = applicationContext.getBean(OrderChangeItemRecordBusiness.class);
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        Page page = downloadParam.getPage();
        param.setPage(page);
        List<OrderChangeItemRecordVo> voList = orderChangeItemRecordBusiness.queryList(staff, param);
        return buildDownloadResult(voList);
    }

    protected DownloadResult buildDownloadResult(List<?> voList) {
        if (voList.isEmpty()) {
            return null;
        }

        DownloadResult result = new DownloadResult();
        result.setFlag(true);
        result.setData(voList.stream().map(vo -> exportHelper.buildDataArray(0, vo)).toArray(String[][]::new));
        return result;
    }

}

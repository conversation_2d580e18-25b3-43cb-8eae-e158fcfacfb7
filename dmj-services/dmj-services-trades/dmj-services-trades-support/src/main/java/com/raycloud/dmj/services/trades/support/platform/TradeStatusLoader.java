package com.raycloud.dmj.services.trades.support.platform;

import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.services.platform.trades.IStatusMap;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;

/**
 * 由于不同平台的订单状态都不一样，所以需要加载各个平台上的平台状态和系统状态的映射表
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/5/24.
 */
@Component
public class TradeStatusLoader implements ApplicationContextAware {

    ApplicationContext applicationContext;

    private final Logger logger = Logger.getLogger(this.getClass());

    @PostConstruct
    public void load() {
        Map<String, IStatusMap> beansOfType = applicationContext.getBeansOfType(IStatusMap.class);
        if (beansOfType.size() == 0) {
            return;
        }
        Set<String> beanNames = beansOfType.keySet();
        for (String beanName : beanNames) {
            IStatusMap statusMap = beansOfType.get(beanName);
            try {
                TradeStatus.loadPlatformStatus(statusMap.getMap());
                if (logger.isDebugEnabled()) {
                    logger.debug(String.format("平台[%s]状态加载完成:%s", statusMap.getSource(), statusMap.getMap().values()));
                }
            } catch (Exception e) {
                logger.error(String.format("平台[%s]状态加载失败", statusMap.getSource()), e);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

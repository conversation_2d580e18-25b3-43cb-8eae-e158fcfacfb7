package com.raycloud.dmj.services.trades.support.bigbag;

import com.raycloud.cache.CacheException;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;
import java.util.Map;

/**
 * 跨境组包抽象接口
 */
public abstract class CombineParcelAbstractHandler implements InitializingBean {

   /**
    * 添加大包
    * @param staff
    * @param trades
    * @param taobaoId
    */
   abstract void addParcelToCombine(Staff staff, List<Trade> trades, Long taobaoId);

   /**
    * 上传大包到平台
    * @param staff
    * @param param
    * @param combineParcel
    * @return
    * @throws Exception
    */
   abstract UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel) throws Exception;

   /**
    * 取消大包
    * @param staff
    * @param parcel
    */
   abstract void cancelCombineParcel(Staff staff, TradeCombineParcel parcel) throws Exception ;

   /**
    * 批量取消小包
    * @param staff
    * @param taobaoId
    * @param combineParcelId
    * @param parcelIds
    * @return
    */
   abstract CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception;

   /**
    * 获取打印数据
    * @param tradeCombineParcel
    * @param warehouseMap
    * @param staff
    * @return
    */
   public abstract TradeCombineParcelResponse getPlatformBatchNoPrintData(TradeCombineParcel tradeCombineParcel, Map<Long, Warehouse> warehouseMap, Staff staff, String subbagId);

   /**
    * 移入组包并生成指定数量的大包
    * @param staff
    * @param tradeList
    * @param request
    */
   public abstract void addParcelToCombineAndSplit(Staff staff, List<Trade> tradeList, TradeGroupParcelRequest request);
}

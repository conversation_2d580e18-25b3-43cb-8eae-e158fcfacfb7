package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/8 10:22 上午
 */
@Service
public class SmtqtgTradeService implements ISmtqtgTradeService{

    @Override
    public void  batchConfirm(Staff staff, List<Trade> trades) {

    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.domian.UploadErrorType;
import com.raycloud.dmj.domain.trades.ConsignRecord;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.services.trades.IUploadErrorTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 上传异常类型转换
 */
@Service
public class UploadErrorTypeService implements IUploadErrorTypeService {

    public final static String OTHER_TYPE = "其他";

    @Override
    public List<UploadErrorType> getErrorTypeAll(Staff staff){
        List<UploadErrorType> sysUploadErrorTypes = TradeDiamondUtils.getSysUploadErrorTypes();
        if(CollectionUtils.isNotEmpty(sysUploadErrorTypes)){
            for(UploadErrorType uploadErrorType : sysUploadErrorTypes){
                uploadErrorType.setPlatformRespList(ArrayUtils.toStringList(uploadErrorType.getPlatformResp()));
            }
            //按优先级排序优先级高的先匹配（目前写死的省去排序步骤）
            //sysUploadErrorTypes = sysUploadErrorTypes.stream().sorted(Comparator.comparing(UploadErrorType::getPriority)).collect(Collectors.toList());
        }
        return sysUploadErrorTypes;
    }

    @Override
    public void setErrorTypeDesc(Staff staff, List<ConsignRecord> records){
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        List<UploadErrorType> errorTypes = getErrorTypeAll(staff);
        if(CollectionUtils.isEmpty(errorTypes)){
            return;
        }
        Map<Long, String> errorTypeIdDescMap = errorTypes.stream().collect(Collectors.toMap(UploadErrorType::getId, UploadErrorType::getDesc, (v1, v2) -> v1));
        for (ConsignRecord record : records){
            if(record.getErrorType()!=null){
                record.setErrorTypeDesc(errorTypeIdDescMap.get(record.getErrorType()));
            }
            if(record.getErrorTypeDesc()==null){
                record.setErrorTypeDesc(OTHER_TYPE);
            }
        }
    }

    @Override
    public void setErrorType(Staff staff, List<ConsignRecord> records){
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        List<UploadErrorType> errorTypes = getErrorTypeAll(staff);
        if(CollectionUtils.isEmpty(errorTypes)){
            return;
        }
        for (ConsignRecord record : records){
            if(StringUtils.isEmpty(record.getErrorDesc())){
                continue;
            }
            for (UploadErrorType errorType : errorTypes){
                if(CollectionUtils.isEmpty(errorType.getPlatformRespList())){
                    continue;
                }
                for (String str : errorType.getPlatformRespList()){
                    if(str.contains(record.getErrorDesc())){
                        record.setErrorType(errorType.getId());
                    }
                }
            }
        }
    }



}

package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.gift.GiftPromotionMatchTradeLogQuery;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.gift.GiftPromotionMatchTradeLogService;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28
 */
public class GiftPromotionMatchTradeLogExportService implements IDownloadCenterCallback {

    private Staff staff;
    private GiftPromotionMatchTradeLogQuery giftPromotionMatchTradeLogQuery;
    private GiftPromotionMatchTradeLogService giftPromotionMatchTradeLogService;

    public GiftPromotionMatchTradeLogExportService(Staff staff, GiftPromotionMatchTradeLogQuery giftPromotionMatchTradeLogQuery, GiftPromotionMatchTradeLogService giftPromotionMatchTradeLogService) {
        this.staff = staff;
        this.giftPromotionMatchTradeLogQuery = giftPromotionMatchTradeLogQuery;
        this.giftPromotionMatchTradeLogService = giftPromotionMatchTradeLogService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        giftPromotionMatchTradeLogQuery.setPageNo(downloadParam.getPage().getPageNo());
        giftPromotionMatchTradeLogQuery.setPageSize(downloadParam.getPage().getPageSize());
        String[][] giftMatchTradeLogInfoExcelContent = giftPromotionMatchTradeLogService.exportInfo(staff, giftPromotionMatchTradeLogQuery);
        DownloadResult result = new DownloadResult();
        result.setData(giftMatchTradeLogInfoExcelContent);
        result.setFlag(null != giftMatchTradeLogInfoExcelContent);
        return result;
    }
}

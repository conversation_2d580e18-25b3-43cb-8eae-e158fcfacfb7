package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.trades.support.search.type.HistoryQueryType;
import com.raycloud.dmj.services.trades.support.search.type.ITradeQueryType;
import com.raycloud.dmj.services.trades.support.search.type.TradeQueryFactory;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_TRADE_TYPE)
public class TradeTypeConditionConverter extends AbsConditionConverter{

    @Resource
    TradeQueryFactory tradeQueryFactory;

    @Resource
    private HistoryQueryType historyQueryType;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return notEmpty(condition.getTradeTypes());
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if (notEmpty(condition.getTradeTypes())) {
            StringBuilder tq = new StringBuilder();
            int validNum = 0;
            for (Integer tradeType : condition.getTradeTypes()) {
                if (tradeType == null) {
                    continue;
                }
                String typeQuery = getTypeQuery(staff, q, tradeType, condition.getQueryId() == null ?SystemTradeQueryParamsContext.QUERY_COMMON:condition.getQueryId());
                if (!typeQuery.isEmpty()) {
                    Query.conjunct(tq, " OR ", validNum++ > 0);
                    tq.append("(").append(typeQuery).append(")");
                }
            }
            if (validNum > 0) {
                q.append(" AND ").append("(").append(tq).append(")");
            }
        }
    }

    public String getTypeQuery(Staff staff, Query q, Integer type, Long queryId) {
        if (type == null) {
            return "";
        }
        //改造前支持的订单类型查询
        String sql = historyQueryType.getSql(staff, type, q, queryId);
        if (sql != null) {
            return sql;
        }

        ITradeQueryType queryType = tradeQueryFactory.getTradeQueryType(staff, type);
        if (queryType == null) {
            throw new IllegalArgumentException("不支持的订单类型:"+type);
        }
        return queryType.getSql(staff,type, q, queryId);
    }


}

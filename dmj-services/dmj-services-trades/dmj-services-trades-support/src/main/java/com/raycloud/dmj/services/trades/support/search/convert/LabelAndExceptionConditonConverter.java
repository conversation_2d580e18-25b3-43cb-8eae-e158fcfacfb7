package com.raycloud.dmj.services.trades.support.search.convert;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.exception.SearchIllegalArgumentException;
import com.raycloud.dmj.services.trades.support.TradeExceptQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_LAB_EXPT)
public class LabelAndExceptionConditonConverter extends AbsConditionConverter{

    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return notEmpty(condition.getTagIds()) || notEmpty(condition.getExcludeTagIds())
                || notEmpty(condition.getExceptionStatus()) || notEmpty(condition.getExceptIds());
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if (notEmpty(condition.getTagIds())) {
            buildTradeLabelContainQuery(staff,q,condition.getTagIds());
        }
        if (notEmpty(condition.getExcludeTagIds())) {
            buildTradeLabelNotContainQuery(staff,q,condition.getExcludeTagIds());
        }

        if (notEmpty(condition.getExceptionStatus()) || notEmpty(condition.getExceptIds())) {
            buildExceptionStatusQuery(staff,q,condition.getExceptionStatus(),condition.getExceptIds(),condition.getExcptQryType(),null,null);
        }
    }

    /**
     * 升级后的标签 需要仍能查询到对应原来老的标签数据
     */
    public static final Map<String ,List<String>> LABEL_UPGRADE_LIST = new HashMap(){
        {
            //老的各平台的催发货 统一为新的催发货标签
            put("1000000032", Lists.newArrayList("1000000097", "1000000031", "1000000502", "1000001600", "1000001701"));
        }
    };

    protected void addOldLabels(List<String> tagList){
        if (CollectionUtils.isNotEmpty(tagList)) {
            for (String s : LABEL_UPGRADE_LIST.keySet()) {
                if (tagList.contains(s)) {
                    tagList.addAll(LABEL_UPGRADE_LIST.get(s));
                }
            }
        }
    }


    public void buildTradeLabelContainQuery(Staff staff, Query q, String[] tagIds) {
        StringBuilder tradeLabelQ = new StringBuilder();
        if (java.util.Objects.isNull(tagIds) || tagIds.length < 1) {
            return;
        }
        validateLabelParams(staff,tagIds);
        List<String> tagList = Lists.newArrayList(tagIds);
        addOldLabels(tagList);
        //正常标签
        List<String> normalTags = tagList.stream().filter(item -> !item.contains("-1")).collect(Collectors.toList());
        //无标签的值是-1
        List<String> noTags = tagList.stream().filter(item -> item.contains("-1")).collect(Collectors.toList());

        //正常标签和无标签都勾选
        if (CollectionUtils.isNotEmpty(normalTags) && CollectionUtils.isNotEmpty(noTags)) {
            tradeLabelQ.append(" AND ( EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND label_id in (").append(StringUtils.join(normalTags, ",")).append(") and t.sid = tl.sid and tl.enable_status = 1)")
                    .append(" OR NOT EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ")
                    .append(staff.getCompanyId()).append(" AND  t.sid = tl.sid and tl.enable_status = 1))");
        }
        //只勾选正常标签
        if (CollectionUtils.isNotEmpty(normalTags) && CollectionUtils.isEmpty(noTags)) {
            tradeLabelQ.append(" AND  t.sid IN  (select tl.sid from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND label_id in (").append(StringUtils.join(normalTags, ",")).append(") and t.sid = tl.sid and tl.enable_status = 1)");
        }
        //只勾选无标签
        if (CollectionUtils.isEmpty(normalTags) && CollectionUtils.isNotEmpty(noTags)) {
            tradeLabelQ.append(" AND  NOT EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND  t.sid = tl.sid and tl.enable_status = 1)");
        }
        q.append(tradeLabelQ);
    }

    private void validateLabelParams(Staff staff, String[] tagIds) {
        if(org.apache.commons.lang3.ArrayUtils.isEmpty(tagIds)){
            return;
        }
        for (String tagId : tagIds) {
            if("-1".equals(tagId)){
                continue;
            }
            if(!StringUtils.isNumeric(tagId)){
                throw new SearchIllegalArgumentException(String.format("标签参数%s,非法",tagId));
            }
        }
    }

    /**
     * 订单标签拆分-查询-不包含
     */
    public void buildTradeLabelNotContainQuery(Staff staff, Query q, String[] tagIds) {
        StringBuilder tradeLabelQ = new StringBuilder();
        if (java.util.Objects.isNull(tagIds) || tagIds.length < 1) {
            return;
        }
        validateLabelParams(staff,tagIds);
        List<String> excludeTagList = Lists.newArrayList(tagIds);
        addOldLabels(excludeTagList);
        //正常标签
        List<String> normalTags = excludeTagList.stream().filter(item -> !item.contains("-1")).collect(Collectors.toList());
        //无标签的值是-1
        List<String> noTags = excludeTagList.stream().filter(item -> item.contains("-1")).collect(Collectors.toList());

        //正常标签和无标签都勾选
        if (CollectionUtils.isNotEmpty(normalTags) && CollectionUtils.isNotEmpty(noTags)) {
            tradeLabelQ.append(" AND ( NOT EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND label_id in (").append(StringUtils.join(normalTags, ",")).append(") and t.sid = tl.sid and tl.enable_status = 1)")
                    .append(" AND EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ")
                    .append(staff.getCompanyId()).append(" AND  t.sid = tl.sid and tl.enable_status = 1))");
        }
        //只勾选正常标签
        if (CollectionUtils.isNotEmpty(normalTags) && CollectionUtils.isEmpty(noTags)) {
            tradeLabelQ.append(" AND  NOT EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND label_id in (").append(StringUtils.join(normalTags, ",")).append(") and t.sid = tl.sid and tl.enable_status = 1)");
        }
        //只勾选无标签
        if (CollectionUtils.isEmpty(normalTags) && CollectionUtils.isNotEmpty(noTags)) {
            tradeLabelQ.append(" AND  EXISTS (select 1 from trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl where tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND  t.sid = tl.sid and tl.enable_status = 1)");
        }
        q.append(tradeLabelQ);
    }

    /**
     * <pre>
     * 订单标签-同时包含 即包含所有指定的标签
     *
     *  EXISTS (
     *  SELECT 1 FROM trade_label_93 tl
     *    where tl.company_id = 66093 AND label_id in (a1,.. an) and t.sid = tl.sid AND tl.enable_status = 1
     *    GROUP BY tl.sid
     *    <strong>HAVING COUNT(DISTINCT tl.label_id) >=n </strong>
     *  )
     * </pre>
     * @param staff
     * @param q
     * @param params
     */
    public void buildTradeLabelAllContainQuery(Staff staff, Query q, String[] tagIds) {
        if (java.util.Objects.isNull(tagIds) || tagIds.length < 1) {
            return;
        }
        validateLabelParams(staff,tagIds);
        StringBuilder tradeLabelQ = new StringBuilder();
        List<String> containsAll = Lists.newArrayList(tagIds);

        //只勾选正常标签
        if (CollectionUtils.isNotEmpty(containsAll)) {
            int size = containsAll.size();

            addOldLabels(containsAll);
            tradeLabelQ.append(" AND  EXISTS (SELECT 1 FROM trade_label_").append(staff.getDbInfo().getTradeLabelDbNo()).append(" tl WHERE tl.company_id = ").append(staff.getCompanyId())
                    .append(" AND label_id in (").append(StringUtils.join(containsAll, ",")).append(") AND t.sid = tl.sid AND tl.enable_status = 1 GROUP BY tl.sid HAVING COUNT(DISTINCT ");

            boolean containsNew = false;
            for (String s : containsAll) {
                if (LABEL_UPGRADE_LIST.containsKey(s)) {
                    tradeLabelQ.append(" CASE WHEN tl.label_id in (").append(StringUtils.join(LABEL_UPGRADE_LIST.get(s), ",")).append(") THEN ").append(s);
                    containsNew= true;
                }
            }
            if (containsNew) {
                tradeLabelQ.append(" ELSE tl.label_id END ");
            }else {
                tradeLabelQ.append("tl.label_id ");
            }
            tradeLabelQ.append(") >=").append(size).append(") ");
            q.append(tradeLabelQ);
        }

    }


    @Resource
    TradeExceptQueryService tradeExceptQueryService;

    public void buildExceptionStatusQuery(Staff staff, Query q, String[] exceptionStatus,String[] exceptIds,Integer excptQueryType,Integer isRefund,Integer isCancel) {
        TradeQueryParams params = new TradeQueryParams();
        params.setExceptionStatus(exceptionStatus);
        params.setExceptIds(exceptIds);
        if (excptQueryType != null) {
            params.setOnlyContain(excptQueryType);
        }
        params.setItemExcepOpen(1);
        params.setIsRefund(isRefund);
        params.setIsCancel(isCancel);
        if(TradeExceptWhiteUtils.openExceptNewSearchCompanyIds(staff)){
            // 开启新的异常查询白名单
            tradeExceptQueryService.buildExceptQuery(staff, q, params);
        }else{
            tradeExceptQueryService.buildOldExceptQuery(staff, q, params);
        }
    }

}

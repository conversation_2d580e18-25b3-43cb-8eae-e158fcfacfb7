package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.platform.basis.PlatformAccessException;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.TradeDownloader;
import com.raycloud.dmj.services.trades.ITradeDownloadService;
import com.raycloud.dmj.services.trades.TradeDownloadAggregationService;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.secret_api.api.SecretRequest;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 各平台订单下载业务类
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/3/29.
 */
@Service
public class TradeDownloadService implements ITradeDownloadService {

    @Resource SecretRequest secretRequest;

    @Resource PlatformManagement platformManagement;

    @Resource
    private TradeDownloadAggregationService tradePlatformDownloadService;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public Trade
    downloadTrade(User user, String tid) {
        TradeDownloader downloader;
        try {
            downloader = platformManagement.getAccess(user.getSource(), TradeDownloader.class);
        } catch (PlatformAccessException e) {
            //部分多平台尚未实现TradeDownloader，继续走原先接口
            //return platformManagement.getAccess(user.getSource(), IPlatformTradeAccess.class).downloadTrade(user, tid);
            // 查询平台订单或者订单池订单数据
            return tradePlatformDownloadService.downloadTrade(user, tid);
        }
        //实现TradeDownloader，走新接口
        return downloadPlatformTrade(downloader, user, tid);
    }

    Trade downloadPlatformTrade(TradeDownloader downloader, User user, String tid) {
        PlatformResponse<TbTrade> resp = downloader.getAndConvert(user, tid);
        if (resp.getData() != null) {
            if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(user.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(user.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_TAO_BAO_XSD.equals(user.getSource())) {
                com.raycloud.dmj.services.utils.EncodeSensitiveFieldHelper.encodeSensitiveField(user, resp.getData(), secretRequest, logger);
            }
            Trade trade = resp.getData();
            if (trade != null && TradeStatus.PAID_FORBID_CONSIGN.equals(trade.getStatus()) && !tradeLocalConfig.isTbSyncPaidForbidConsign(user.getCompanyId())) {
                throw new IllegalArgumentException("拼团中/拼团失败的订单不同步，如需同步，请联系客服找技术开启同步配置");
            }
            return resp.getData();
        } else if (resp.getE() != null) {
            if (resp.getE().getMessage() != null && resp.getE().getMessage().contains("订单不存在")) {
                throw new IllegalArgumentException(String.format("单号:%s，该订单不存在，请检查单号是否是该店铺的订单或者单号是否正确",tid));
            } else {
                throw new IllegalArgumentException(resp.getE().getMessage());
            }
        } else if (!resp.isSuccess() && resp.getSubMsg() != null) {
            throw new IllegalArgumentException(user.getSource() + "订单下载失败: " + resp.getSubMsg());
        } else {
            throw new IllegalArgumentException("平台交易号不存在，请检查后重新输入");
        }
    }

}

package com.raycloud.dmj.services.backend;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.backend.DatabaseHelpDao;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.basis.TableInformation;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-02-24 10:57
 * @Description 生成分表号
 */
@Service
public class GenerateDbNoHelper {

    @Resource
    DatabaseHelpDao databaseHelpDao;

    /**
     * @param companyId  公司id
     * @param tableCount 表数量 {@link DbInfo}
     * @return 返回分表号
     */
    public Integer simpleGenerate(Long companyId, Integer tableCount) {
        return (int) (companyId % tableCount);
    }

    /**
     * @param companyId     公司id
     * @param dbNo          分库编号
     * @param baseTableName 表名称（无分表号的基表）
     * @return 动态返回分表号
     */
    public Integer dynamicGenerate(Long companyId, Integer dbNo, String baseTableName, Integer tableCount) {
        Integer simpleTableNo = simpleGenerate(companyId, tableCount);
        if (tableCount == 1) {
            return simpleTableNo;
        }

        List<TableInformation> tableInformations = databaseHelpDao.getTableDataLength(dbNo, initTableNames(baseTableName, tableCount));
        if (tableInformations == null || tableInformations.size() <= 1) {
            return simpleTableNo;
        }

        //已使用最小值
        Double minDataLength = tableInformations.get(0).getDataLength();
        Map<Double, List<Integer>> datalenthTableNamesMap = new HashMap<>();
        for (TableInformation t : tableInformations) {
            datalenthTableNamesMap.computeIfAbsent(t.getDataLength(), k -> new ArrayList<>()).add(parseTableNo(t.getTableName(), simpleTableNo));
            if (minDataLength > t.getDataLength()) {
                minDataLength = t.getDataLength();
            }
        }

        //已使用最小值的分表号集合如果包含简单分表号，则优先使用简单分表号
        List<Integer> tableNos = datalenthTableNamesMap.get(minDataLength);
        if (tableNos == null || tableNos.size() == 0 || tableNos.contains(simpleTableNo)) {
            return simpleTableNo;
        }

        //已使用最小值的分表号集合里面取第一个
        return tableNos.get(0);
    }

    private List<String> initTableNames(String baseTableName, Integer tableCount) {
        List<String> tableNames = new ArrayList<>();
        for (int i = 0; i < tableCount; i++) {
            tableNames.add(baseTableName + "_" + i);
        }
        return tableNames;
    }

    private Integer parseTableNo(String tableName, Integer defaultTableNo) {
        String[] strs = tableName.split("_");
        try {
            return Integer.parseInt(strs[strs.length - 1]);
        } catch (Exception e) {
            Logs.error(String.format("动态解析分表号出错, tableName=%s", tableName));
            return defaultTableNo;
        }
    }
}

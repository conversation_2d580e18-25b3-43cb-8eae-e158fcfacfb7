package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.conf.AppConfiguration;
import com.raycloud.dmj.dao.DaoLogUtils;
import com.raycloud.dmj.dao.TradeSidSnapshotInsertExecutor;
import com.raycloud.dmj.dao.TradeSidSnapshotInsertWorker;
import com.raycloud.dmj.dao.trade.RuntimeIdSnapshotDao;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.trade.export.TradeExcelExportTaskDao;
import com.raycloud.dmj.data.dubbo.common.ThrottlingException;
import com.raycloud.dmj.data.dubbo.request.TradeColdQueryParameter;
import com.raycloud.dmj.data.dubbo.tools.ParameterConverts;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.export.TradeExcelExportTask;
import com.raycloud.dmj.domain.trades.report.OrderExtra;
import com.raycloud.dmj.domain.trades.report.TradeExtra;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.TradeSysConsignUtils;
import com.raycloud.dmj.domain.trades.utils.TradeSysDigestUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trades.ITradeDataService;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.filter.ITradeFilterService;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by yangheng on 16/7/29.
 */
@Service("tradeDataService")
public class TradeDataService implements ITradeDataService {

    private final static Logger logger = Logger.getLogger(TradeDataService.class);

    @Resource
    JdbcTemplateAdapter jdbcTemplateAdapter;

    @Resource ITradeFilterService filterService;
    @Resource
    ITradeFillService tradeFillService;

    @Resource TbTradeDao tbTradeDao;
    @Resource(name = "tradeSqlQueryBuilder") TradeQueryBuilder tradeSqlQueryBuilder;

    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;
    @Resource
    AppConfiguration appConfiguration;
    @Resource
    RuntimeIdSnapshotDao runtimeIdSnapshotDao;
    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;
    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;
    @Resource
    protected IStaffService staffService;
    @Resource
    TradeExcelExportTaskDao tradeExcelExportTaskDao;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    TradeColdDataService tradeColdDataService;

    @Resource
    private TbTradeSearchService tbTradeSearchService;

    @Resource
    private TradeQueryService tradeQueryService;

    /**
     * 三个月之前订单的查询
     * @param staff
     * @param params
     * @return
     */
    @Override
    public Trades search3Month(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo){
        handleQueryParams(params);
        //if(!tradeLocalConfig.isUseTradeColdQueryCompanyIds(staff.getCompanyId())){
        //    //交易这边已经不挂载归档数据库了
        //    new QueryLogBuilder(staff).append("查询三个月前订单，走MySQL兜底查询").printWarn(logger);
        //    return queryByMysql(staff, params, onlyQuery3monthAgo);
        //}else {
            try{
                params.setPage(buildPage(params));
                long startTime = System.currentTimeMillis();
                Trades coldDataTrades = tradeColdDataService.search3Month(staff, params, onlyQuery3monthAgo);
                if((System.currentTimeMillis() - startTime) > LogKit.THRESHOLD){
                    logger.debug(LogHelper.buildLogHead(staff).append("请求参数: ").append(JSON.toJSONString(ParameterConverts.tradeColdQueryConvert(params))));
                }
                List<Trade> list = coldDataTrades.getList();
                if (CollectionUtils.isNotEmpty(list)) {
                    tradeQueryService.fillDestAndSourceName(list);
                }
                return coldDataTrades;
            } catch (ThrottlingException | RpcException e){
                new QueryLogBuilder(staff).append("冷数据接口查询三个月前订单出错:").append(JSON.toJSONString(ParameterConverts.tradeColdQueryConvert(params))).printError(logger,e);
                throw e;
            }
        //}
    }

    @NotNull
    private Trades queryByMysql(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        //数据权限权限条件
        tbTradeSearchService.initDataAuth(staff, params);
        Trades trades = new Trades();
        trades.setPage(params.getPage());
        trades.setSort(params.getSort());
        List<Trade> tradeList;
        Integer queryFlag = params.getQueryFlag();
        if (queryFlag!=null && queryFlag == 3) {
             trades.setTotal(buildSnapshot(staff, params, onlyQuery3monthAgo));
            return trades;
        }else if(queryFlag!=null && queryFlag ==4){
            tradeList = queryTradesBySnapshot(staff, params, onlyQuery3monthAgo);
        }else {
            try {
                tradeList = queryTrades(staff, params, onlyQuery3monthAgo);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "三个月前订单查询出错"), e);
                throw new TradeException("糟糕，订单查询出错了。。。");
            }
        }
        fillSource(tradeList);
        tradeFillService.fill(staff, tradeList);
        tradeQueryService.fillDestAndSourceName(tradeList);
        if (CollectionUtils.isNotEmpty(trades.getList()) && params.getSysMask() == 1) {
            TradeSysDigestUtils.batchSensitive(staff,trades.getList());
        }
        try {
            filterService.filterTrades(staff, tradeList);
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        }
        fxgTradeDecryptBusiness.batchSensitive(staff, tradeList);
        commonTradeDecryptBusiness.batchSensitive(staff, tradeList);
        TradeSysConsignUtils.handleSysConsigned(tradeList);
        trades.setList(tradeList);
        Page page = params.getPage();
        if (page.getPageNo() > 1) {
            trades.setTotal(tradeList.size() < page.getPageSize() ? (page.getPageNo() - 1) * page.getPageSize() + (long) tradeList.size() : 2000L);
        } else {
            trades.setTotal(tradeList.size() < params.getPage().getPageSize() ? (long) tradeList.size() : 2000L);
        }
        return trades;
    }

    public static void handleQueryParams(TradeQueryParams params){
        if(!params.getContext().isUniqueQuery() && org.apache.commons.lang3.ArrayUtils.isEmpty(params.getSysStatus())){
            params.setSysStatus(Trade.SYS_STATUS_CLOSED,Trade.SYS_STATUS_FINISHED);
        }
        if(params.getContext().isUniqueQuery()){
            params.setStartTime(null);
            params.setEndTime(null);
        }
    }

    private void fillSource(List<Trade> tradeList) {
        if (CollectionUtils.isNotEmpty(tradeList)){
            for (Trade trade : tradeList) {
                if (StringUtils.equals(trade.getSubSource(), CommonConstants.PLAT_FORM_TYPE_SYS)){
                    trade.setSource( CommonConstants.PLAT_FORM_TYPE_SYS);
                    trade.setSubSource(null);
                }
            }
        }
    }

    /**
     * 查询订单历史成本
     * @param staff
     * @param sids
     * @return
     */
    @Override
    public List<TradeExtra> searchHistoryCost(Staff staff, List<Long> sids) {
        if(CollectionUtils.isEmpty(sids)){
            return new ArrayList<>();
        }
        List<TradeExtra> tradeExtraList = queryTradeExtra(staff, sids);
        if(CollectionUtils.isEmpty(tradeExtraList)){
            return tradeExtraList;
        }
        List<OrderExtra> orderExtraList = queryOrderExtra(staff, sids);
        Map<Long,List<OrderExtra>> orderExtraMap = new HashMap<>();
        for(OrderExtra orderExtra:orderExtraList){
            List<OrderExtra> toTradeList = orderExtraMap.get(orderExtra.getSid());
            if(toTradeList==null){
                toTradeList = new ArrayList<>();
                orderExtraMap.put(orderExtra.getSid(),toTradeList);
            }
            toTradeList.add(orderExtra);
        }
        List<Long> mergeSids = new ArrayList<>();
        List<TradeExtra> mainTradeExtra = new ArrayList<>();
        for(TradeExtra tradeExtra:tradeExtraList){
            if(tradeExtra.getMergeSid()>0){
                mergeSids.add(tradeExtra.getSid());
                mainTradeExtra.add(tradeExtra);
                continue;
            }
            tradeExtra.setOrderExtraList(orderExtraMap.get(tradeExtra.getSid()));
        }
        if(mergeSids.size()>0){
            Map<Long, List<TradeExtra>> mergeMap = new HashMap<>();
            List<TradeExtra> mergeTradeExtraList = queryTradeExtraMergeSid(staff, mergeSids);
            for (TradeExtra tradeExtra:mergeTradeExtraList){
                List<TradeExtra> mergeList = mergeMap.get(tradeExtra.getMergeSid());
                if(mergeList==null){
                    mergeList = new ArrayList<>();
                    mergeMap.put(tradeExtra.getMergeSid(),mergeList);
                }
                mergeList.add(tradeExtra);
            }
            List<Long> fullMergeSids = mergeTradeExtraList.stream().map(TradeExtra::getSid).collect(Collectors.toList());
            if(fullMergeSids.size()>0){
                List<OrderExtra> mergeOrderExtraList = queryOrderExtra(staff,fullMergeSids);
                assembleMergeTradeExtra(mainTradeExtra, mergeMap, mergeOrderExtraList);
            }
        }
        return tradeExtraList;
    }

    @Override
    public List<String> search3MonthColdDirectQueryByTids(Staff staff, Set<String> tids) {
        if (CollectionUtils.isEmpty(tids)) {
            return Lists.newArrayList();
        }
        Set<String> queryColumns = new HashSet<>();
        queryColumns.add("sid");
        queryColumns.add("tid");
        queryColumns.add("upd_time");
        List<TbTrade> existTrades = tradeColdDataService.tradeColdDirectQueryByTids(staff, queryColumns, tids);
        if (CollectionUtils.isNotEmpty(existTrades)) {
            return existTrades.stream().map(TbTrade::getTid).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    @Override
    public List<String> search3MonthColdDirectQueryByTids(Staff staff, Set<String> tids,  boolean fuzzyMatchingTid) {
        if (CollectionUtils.isEmpty(tids)) {
            return Lists.newArrayList();
        }
        Set<String> queryColumns = new HashSet<>();
        queryColumns.add("tid");
        List<TbTrade> existTrades = tradeColdDataService.tradeColdDirectQueryByTids(staff, queryColumns, tids, fuzzyMatchingTid);
        if (CollectionUtils.isNotEmpty(existTrades)) {
            return existTrades.stream().map(TbTrade::getTid).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }


    /**
     * 查询满足条件的所有trade
     * @param staff
     * @param params
     * @return
     */
    List<Trade> queryTrades(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        Query q = buildQuery(staff, params, onlyQuery3monthAgo);
        Page page = buildPage(params);
        return _queryTrades(staff, q, page, params.getFields(), params.getContext().getTaskId());
    }

    @NotNull
    private Page buildPage(TradeQueryParams params) {
        Page page = params.getPage();
        if (page == null) {
            page = new Page();
            params.setPage(page);
        }
        if (page.getPageNo() == null || page.getPageNo() <= 0) {
            page.setPageNo(Page.DEFAULT_PAGE_NUM);
        }
        if (page.getPageSize() == null || page.getPageSize() <= 0) {
            page.setPageSize(Page.DEFAULT_PAGE_SIZE);
        }
        return page;
    }

    @NotNull
    private Query buildQuery(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        Query q = new Query();
        tradeSqlQueryBuilder.buildQuery(q, staff, params);
        tradeSqlQueryBuilder.buildDistributorQuery(q, staff, params);
        tradeSqlQueryBuilder.buildSalesmanQuery(q, staff, params);
        if (q.isStopQuery()) {
            return q;
        }
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, params.getWarehouseType(), params.getWarehouseIds());
        tradeSqlQueryBuilder.buildArrayQuery(q, "sid", params.getSid());
        tradeSqlQueryBuilder.buildTidQuery(staff, q, params);
        tradeSqlQueryBuilder.buildArrayQuery(q, "out_sid", params.getOutSids());
        tradeSqlQueryBuilder.buildArrayQuery(q, "sid", StringUtils.split(params.getSids(), ","));
        tradeSqlQueryBuilder.buildBuyerNickQuery(staff, q, params.getBuyerNick());
        tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", params.getSysStatus());
        tradeSqlQueryBuilder.buildArrayQuery(q, "short_id", params.getShortId());
        String timeType = params.getTimeType() != null && !params.getTimeType().isEmpty() ?  params.getTimeType() : "pay_time";
        timeType = StringUtils.defaultIfBlank(TradeColdQueryParameter.QueryTimeType.find(timeType).getTypeName(),"pay_time");
        Date startTime = params.getStartTime();
        if (startTime == null || startTime.before(TradeTimeUtils.INIT_DATE)) {
            startTime = TradeTimeUtils.INIT_DATE;
        }
        q.and().append(timeType).append(" > ?").add(DateUtils.datetime2Str(startTime));
        Date endTime = params.getEndTime();
        if (onlyQuery3monthAgo && !params.getContext().isUniqueQuery()) {//只查询三个月以前的订单
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, -3);
            if (endTime != null) {
                if (endTime.after(c.getTime())) {
                    endTime = c.getTime();
                }
            } else {
                endTime = c.getTime();
            }
            q.and().append(timeType).append(" < ?").add(DateUtils.datetime2Str(endTime));
        } else if (endTime != null) {
            q.and().append(timeType).append(" < ?").add(DateUtils.datetime2Str(endTime));
        }
        return q;
    }

    List<Trade> _queryTrades(Staff staff, Query q, Page page, String fields, Long taskId) {
        if (null != taskId){
            return queryTradeSqlInsertTask(staff, q, page, taskId);
        }
        StringBuilder tradeQuerySql = tbTradeDao.buildTradeQuerySql(staff, StringUtils.isBlank(fields) ? "*" : fields, q, page, false,false, false);
        q.add(page.getStartRow()).add(page.getPageSize());
        long start = System.currentTimeMillis();
        JdbcTemplate jdbcTemplate = getJdbcTemplate(staff);
        jdbcTemplate.setQueryTimeout(30);
        List<TbTrade> tbTrades = jdbcTemplate.query(tradeQuerySql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class), q.getArgs().toArray());
        List<Trade> result = null;
        if (!tbTrades.isEmpty()) {
            result = assembleOrders(staff, tbTrades);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("[三个月之前订单查询] num: %s, WhereSql: %s;  Args: %s), 耗时: %sms", (result != null ? result.size() : 0), q.getQ(), q.getArgs(), (System.currentTimeMillis() - start))));
        }
        return result != null ? result : new ArrayList<Trade>(0);
    }

    private List<Trade> queryTradeSqlInsertTask(Staff staff, Query q, Page page, Long taskId){
        StringBuilder fileDownloadSql = tbTradeDao.buildTaskTradeQuery3MonthSql(staff, q);
        TradeExcelExportTask tradeExcelExportTask = tradeExcelExportTaskDao.queryByTaskId(staff, taskId);
        tradeExcelExportTask.setTradeCount(300000L);
        tradeExcelExportTask.setFileDownloadSql(fileDownloadSql.toString());
        tradeExcelExportTask.setExportStatus(TradeExcelExportTask.TASK_READY);
        tradeExcelExportTaskDao.update(staff, tradeExcelExportTask);
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[sid快照建立查询三个月前模版 table=%s] sql: %s; sort: %s;", q.getTradeTable(), fileDownloadSql, q.getSort())));
        return new ArrayList<>();
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public Long buildSnapshot(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo){
        Query q = buildQuery(staff, params, onlyQuery3monthAgo);
        if (q.isStopQuery()) {
            new QueryLogBuilder(staff).append("查询中断",q.getStopReason()).printDebug(logger);
            return 0L;
        }
        RuntimeIdSnapshot runtime = params.getRuntimeIdSnapshot();

        //设置tradeCount计算核心线程数用。
        StringBuilder countSql = tbTradeDao.buildTradeQuerySql(staff, "COUNT(1)", q, null, false,false, false);
        runtime.setTradeCount(getJdbcTemplate(staff).queryForObject(countSql.toString(),Long.class,q.getArgs().toArray()));

        //构造流式查询sql
        Page page = new Page(1,1);
        StringBuilder tradeQuerySql = tbTradeDao.buildTradeQuerySql(staff, "t.sid", q, page, false,false, false);
        tradeQuerySql.delete(tradeQuerySql.length()-" LIMIT ?, ?".length(),tradeQuerySql.length());
        TradeSidSnapshotInsertWorker worker=null;
        if (runtime.isBatch()){
            if (runtime.getSnapshotWorker()==null) {
                runtime.setSnapShotWorker(new TradeSidSnapshotInsertWorker(staff,runtime,runtimeIdSnapshotDao,getDbNo(staff)));
                TradeSidSnapshotInsertExecutor.getInstance().execute(((TradeSidSnapshotInsertWorker) runtime.getSnapshotWorker()));
            }
            worker = (TradeSidSnapshotInsertWorker) runtime.getSnapshotWorker();
        }else {
            worker = new TradeSidSnapshotInsertWorker(staff,runtime,runtimeIdSnapshotDao,getDbNo(staff));
            TradeSidSnapshotInsertExecutor.getInstance().execute(worker);
        }
        Assert.notNull(worker,"worker 不可以为空！！");
        if(worker.isReject()){
            return TradeSidSnapshotInsertWorker.REJECT;
        }

        boolean exception = false;
        try {
            TradeSidSnapshotInsertWorker finalWorker = worker;
            getJdbcTemplate(staff).query(con -> {
                ArgumentPreparedStatementSetter argumentPreparedStatementSetter = new ArgumentPreparedStatementSetter(q.getArgs().toArray());
                PreparedStatement preparedStatement =
                        con.prepareStatement(tradeQuerySql.toString(),
                                ResultSet.TYPE_FORWARD_ONLY,
                                ResultSet.CONCUR_READ_ONLY);
                argumentPreparedStatementSetter.setValues(preparedStatement);
                preparedStatement.setFetchSize(Integer.MIN_VALUE);
                preparedStatement.setFetchDirection(ResultSet.FETCH_FORWARD);
                return preparedStatement;
            }, rs -> {
                finalWorker.addId(rs.getLong(1));
            });
        }catch (Throwable e){
            exception = true;
        }finally {
            if (exception
                    ||(runtime.isBatch()&&runtime.finalBatch())
                    ||!runtime.isBatch()) {
                worker.queryEnd();
            }
        }
        return runtime.getTradeCount();
    }

    /**
     * @deprecated  这里的逻辑已迁移到冷库查询 这段代码实际已经没有调用了
     */
    @Deprecated
    List<Trade> queryTradesBySnapshot(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        Query q = buildQuery(staff, params, onlyQuery3monthAgo);
        RuntimeIdSnapshot runtime = params.getRuntimeIdSnapshot();
        Page page = params.getPage();
        StringBuilder sql = tbTradeDao.buildSnapshotQuerySql(staff, q, runtime,page);
        long start = System.currentTimeMillis();
        List<TbTrade> tbTrades = getJdbcTemplate(staff).query(sql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class));
        List<Trade> result = null;
        if (!tbTrades.isEmpty()) {
            result = assembleOrders(staff, tbTrades);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("[三个月之前订单查询] num: %s, WhereSql: %s;  Args: %s), 耗时: %sms", (result != null ? result.size() : 0), q.getQ(), q.getArgs(), (System.currentTimeMillis() - start))));
        }
        return result != null ? result : new ArrayList<Trade>(0);
    }

    private List<Trade> assembleOrders(Staff staff, List<TbTrade> trades) {
        Set<Long> mergeSids = new HashSet<Long>();
        Set<Long> sids = new HashSet<Long>(trades.size() + 2);
        for (Trade trade : trades) {
            sids.add(trade.getSid());
            if (trade.getMergeSid() > 0) {
                mergeSids.add(trade.getMergeSid());
            }
        }
        Map<Long, List<TbTrade>> mergeTradesMap = new HashMap<Long, List<TbTrade>>(mergeSids.size(), 1);
        if (!mergeSids.isEmpty()) {
            Query q = new Query();
            q.append("SELECT * FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status > 0 AND merge_sid IN(").add(staff.getCompanyId());
            int i = 0;
            for (Long mergeSid : mergeSids) {
                q.append(i++ > 0 ? ", ?" : "?").add(mergeSid);
            }
            q.append(")");
            List<TbTrade> mergeTrades = getJdbcTemplate(staff).query(q.getQ().toString(), q.getArgs().toArray(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class));
            for (TbTrade trade : mergeTrades) {
                sids.add(trade.getSid());
                List<TbTrade> tradeList = mergeTradesMap.get(trade.getMergeSid());
                if (tradeList == null) {
                    tradeList = new ArrayList<TbTrade>();
                    mergeTradesMap.put(trade.getMergeSid(), tradeList);
                }
                tradeList.add(trade);
            }
        }
        List<TbOrder> orders = queryOrders(staff, sids);
        return assembleMergeTrades(staff,trades, mergeTradesMap, orders);
    }

    private List<Trade> assembleMergeTrades(Staff staff,List<TbTrade> trades, Map<Long, List<TbTrade>> mergeTradesMap, List<TbOrder> orders) {
        int oldSize = trades.size();
        for (List<TbTrade> list : mergeTradesMap.values()) {
            trades.addAll(list);
        }
        TradeUtils.assemblyBySid(trades, orders);
        List<Trade> result = new ArrayList<Trade>();
        Set<Long> usedMergeSids = new HashSet<Long>();
        for (int i = 0; i < oldSize; i++) {
            Trade trade = trades.get(i);
            if (trade.getMergeSid() > 0) {
                if (usedMergeSids.add(trade.getMergeSid())) {
                    List<TbTrade> mergeTrades = mergeTradesMap.get(trade.getMergeSid());
                    Trade main = tradeAssembleBusiness.getMainTrade(staff, mergeTrades, false);
                    if (main != null) {
                        result.add(main);
                        if (StringUtils.isBlank(main.getOutSid())){
                            List<TbTrade> outSidTrades = mergeTrades.stream().filter(t -> StringUtils.isNotBlank(t.getOutSid())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(outSidTrades)){
                                main.setOutSid(outSidTrades.get(0).getOutSid());
                            }
                        }
                    }
                }
            } else {
                result.add(trade);
            }
        }
        return result;
    }

    private List<TbOrder> queryOrders(Staff staff, Set<Long> sids) {
        Query q = new Query();
        q.append("SELECT * FROM order_").append(staff.getDbInfo().getOrderDbNo());
        q.add(staff.getCompanyId()).append(" WHERE company_id = ? AND enable_status > 0 AND sid IN(");
        int i = 0;
        for (Long sid : sids) {
            q.append(i++ > 0 ? ", ?" : "?").add(sid);
        }
        q.append(")");
        return getJdbcTemplate(staff).query(q.getQ().toString(), q.getArgs().toArray(), new BeanPropertyRowMapper<TbOrder>(TbOrder.class));
    }

    JdbcTemplate getJdbcTemplate(Staff staff) {
//        //本地及stage环境报表库与订单库是同一个库,此时jdbcTemplate为null
//        //线上环境及灰度环境有单独的报表库,此时jdbcTemplate不为null
//        String profile = appConfiguration.getProfile();

//        if (profile.equals("prod") || profile.equals("preissue") || profile.startsWith("gray")) {
        //TODO  2023-01-29第二次找回代码，如果这里有冲突请只保留  return jdbcTemplateAdapter.get(staff.getReportDbNo()); 其他代码一律不要
        //TODO 只注释不删除的原因就是怕给我又把代码处理冲突的时候给我搞回来了，求放过
            return jdbcTemplateAdapter.get(staff.getReportDbNo());
////        } else {
//            return jdbcTemplateAdapter.get(staff.getDbNo());
////        }
    }

    Integer getDbNo(Staff staff){
        String profile = appConfiguration.getProfile();
        if (profile.equals("prod") || profile.equals("preissue") || profile.startsWith("gray")) {
            return staff.getReportDbNo();
        } else {
            return staff.getDbNo();
        }
    }

    private List<TradeExtra> queryTradeExtra(Staff staff, List<Long> sids){
        Query query = new Query();
        query.append("SELECT sid, merge_sid mergeSid, cost_snapshot costSnapShot, cost_origin costOrigin FROM trade_extra_").append(staff.getDbInfo().getReportTradeExtraDbNo());
        query.add(staff.getCompanyId()).append(" where company_id = ? AND enable_status > 0 AND sid IN (");
        int i = 0;
        for(Long sid:sids){
            query.append(i++ > 0 ? ", ?" : "?").add(sid);
        }
        query.append(")");
        return  getJdbcTemplate(staff).query(query.getQ().toString(),query.getArgs().toArray(), new BeanPropertyRowMapper<>(TradeExtra.class));
    }

    private List<OrderExtra> queryOrderExtra(Staff staff, List<Long> sids){
        Query query = new Query();
        query.append("SELECT sid, id oid, cost_snapshot costSnapShot, cost_origin costOrigin FROM order_extra_").append(staff.getDbInfo().getReportOrderExtraDbNo());
        query.add(staff.getCompanyId()).append(" where company_id = ? AND enable_status > 0 AND sid IN (");
        int i = 0;
        for (Long sid:sids){
            query.append(i++ > 0 ? ", ?" : "?").add(sid);
        }
        query.append(")");
        return getJdbcTemplate(staff).query(query.getQ().toString(),query.getArgs().toArray(), new BeanPropertyRowMapper<>(OrderExtra.class));
    }

    private List<TradeExtra> queryTradeExtraMergeSid(Staff staff, List<Long> mergeSids){
        Query query = new Query();
        query.append("SELECT sid, merge_sid mergeSid, cost_snapshot costSnapShot, cost_origin costOrigin FROM trade_extra_").append(staff.getDbInfo().getReportTradeExtraDbNo());
        query.add(staff.getCompanyId()).append(" where company_id = ? AND enable_status > 0 AND merge_sid IN (");
        int i = 0;
        for(Long mergeSid:mergeSids){
            query.append(i++ > 0 ? ", ?" : "?").add(mergeSid);
        }
        query.append(")");
        return  getJdbcTemplate(staff).query(query.getQ().toString(),query.getArgs().toArray(), new BeanPropertyRowMapper<>(TradeExtra.class));
    }

    private void assembleMergeTradeExtra(List<TradeExtra> main, Map<Long,List<TradeExtra>> mergeMap, List<OrderExtra> mergeOrderExtraList){
        Map<Long,List<OrderExtra>> mergeOrderExtraMap = new HashMap<>();
        for(OrderExtra orderExtra:mergeOrderExtraList){
            List<OrderExtra> mergeOrderList = mergeOrderExtraMap.get(orderExtra.getSid());
            if(mergeOrderList==null){
                mergeOrderList = new ArrayList<>();
                mergeOrderExtraMap.put(orderExtra.getSid(),mergeOrderList);
            }
            mergeOrderList.add(orderExtra);
        }
        for(TradeExtra tradeExtra:main){
            List<OrderExtra> fullList = new ArrayList<>();
            List<TradeExtra> mergeList = mergeMap.get(tradeExtra.getSid());
            if(CollectionUtils.isNotEmpty(mergeList)){
                for(TradeExtra extra:mergeList){
                    if(mergeOrderExtraMap.get(extra.getSid()) != null){
                        fullList.addAll(mergeOrderExtraMap.get(extra.getSid()));
                    }
                }
            }
            tradeExtra.setOrderExtraList(fullList);
        }
    }
}

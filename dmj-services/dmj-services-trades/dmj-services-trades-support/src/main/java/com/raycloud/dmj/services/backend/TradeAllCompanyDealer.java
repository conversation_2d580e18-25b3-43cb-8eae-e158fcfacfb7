package com.raycloud.dmj.services.backend;

import com.raycloud.dmj.business.common.CacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 遍历所有公司，并进行处理的工具类
 * Created by guzy on 16/6/1.
 */
@Component
public class TradeAllCompanyDealer {

    @Resource
    private IStaffService staffService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private IUserService userService;

    @Resource
    private CacheBusiness cacheBusiness;

    private Logger logger= Logger.getLogger(TradeAllCompanyDealer.class);

    /**
     * 填充所有公司的asso内容
     */
    public List<Object> dealWithAllCompanys(CompanyDealOp dealer) {
        Page page = dealer.getPage();
        List<Object> returnObjs=new ArrayList<Object>();
        List<Company> list = null;
        while ((list = companyService.list(page)).size() > 0) {
            String breakKey = dealer.getBreakKey();
            if(StringUtils.isNotEmpty(breakKey)){
                String breakValue = cacheBusiness.get(breakKey);
                if("1".equals(breakValue)){
                    break;
                }
            }
            try {
                List<Staff> staffs=getStaffs(list);
                for(Staff staff:staffs){
                    //此公司本次不处理
                    if(dealer.getExcludeCompanyId().contains(staff.getCompanyId())){
                        continue;
                    }
                    returnObjs.add(dealer.deal(staff));
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            } finally {
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        return returnObjs;
    }

    public List<Object> dealWithCompanys(CompanyDealOp dealer,List<Company> list) {
        List<Object> returnObjs=new ArrayList<Object>();
        List<Staff> staffs=getStaffs(list);
        for(Staff staff:staffs){
            try {
                returnObjs.add(dealer.deal(staff));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }

        return returnObjs;
    }


    /**
     * 从会话缓存中获取员工详情信息
     */
    public List<Staff> getStaffs(Staff staff,Long[] companyIds) {
        List<Company> companys = companyService.querComapanyListByIds(companyIds);
        if(companys == null || companys.size() == 0){
            logger.warn(LogHelper.buildLog(staff, String.format("找不到Company信息,companyIds=%s",Arrays.asList(companyIds))));
            return null;
        }
        List<Staff> staffs = staffService.queryDefaultStaffList(companyIds);
        if (staffs == null || staffs.size() == 0){
            logger.warn(LogHelper.buildLog(staff, String.format("找不到Staff信息,companyIds=%s",Arrays.asList(companyIds))));
            return null;
        }

        List<User> users = userService.queryValidateByCompanyIds(companyIds);
        if(users == null || users.size() == 0){
            logger.warn(LogHelper.buildLog(staff, String.format("找不到User信息,companyIds=%s",Arrays.asList(companyIds))));
        }

        List<CompanyProfile> profiles = companyService.getCompanyProfiles(companyIds);
        if(profiles == null || profiles.size() == 0){
            logger.warn(LogHelper.buildLog(staff, String.format("找不到CompanyProfile信息,companyIds=%s",Arrays.asList(companyIds))));
            return null;
        }
        assemblyStaffs(staffs, companys, profiles, users);
        return staffs;
    }

    /**
     * 从会话缓存中获取员工详情信息
     *
     * @return
     */
    public List<Staff> getStaffs(List<Company> companies) {
        Long[] companyIds = getCompanyIds(companies);
        List<Staff> staffs = staffService.queryDefaultStaffList(companyIds);
        if (staffs.size() == 0)
            return staffs;

        List<User> users = userService.queryValidateByCompanyIds(companyIds);
        List<CompanyProfile> profiles = companyService.getCompanyProfiles(companyIds);
        assemblyStaffs(staffs, companies, profiles, users);
        return staffs;
    }

    /**
     * 将users整合到staffs中
     *
     * @param staffs
     * @param users
     */
    void assemblyStaffs(List<Staff> staffs, List<Company> companies,
                        List<CompanyProfile> profiles, List<User> users) {
        Map<Long, Staff> map = new HashMap<Long, Staff>();
        for (Staff staff : staffs) {
            map.put(staff.getCompanyId(), staff);
        }

        for (Company c : companies) {
            if (!map.containsKey(c.getId())) {
                continue;
            }
            map.get(c.getId()).setCompany(c);
        }

        for (CompanyProfile p : profiles) {
            if (!map.containsKey(p.getCompanyId())) {
                continue;
            }
            Company c = map.get(p.getCompanyId()).getCompany();
            if (null == c)
                continue;
            c.setProfile(p);
        }

        for (User user : users) {
            if (!map.containsKey(user.getCompanyId()))
                continue;
            map.get(user.getCompanyId()).getUsers().add(user);
            user.setStaff(map.get(user.getCompanyId()));
        }

        for(Staff staff : staffs){
            staff.setUserIdMap(toUserIdMap(staff));
        }
    }

    private Map<Long, User> toUserIdMap(Staff staff) {
        if (staff.getUsers().size() == 0)
            return new HashMap<Long, User>();

        Map<Long, User> map = new HashMap<Long, User>(staff.getUsers().size(),
                1);
        for (User user : staff.getUsers()) {
            map.put(user.getId(), user);
        }
        return map;
    }

    protected Long[] getCompanyIds(List<Company> list) {
        Long[] cids = new Long[list.size()];
        int index = 0;
        for (Company company : list) {
            cids[index++] = company.getId();
        }
        return cids;
    }
}

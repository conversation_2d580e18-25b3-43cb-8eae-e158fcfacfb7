package com.raycloud.dmj.services.trades.support.search.type;

import com.raycloud.dmj.domain.account.Staff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-19
 */
@Component
public class TradeQueryFactory {

     @Autowired
     private List<ITradeQueryType> tradeQueryTypes;

     public ITradeQueryType getTradeQueryType(Staff staff, Integer type){
         if (type == null) {
             return null;
         }
         for(ITradeQueryType tradeQueryType : tradeQueryTypes){
             if(tradeQueryType.getType() != null && Objects.equals(tradeQueryType.getType().getcode(), String.valueOf(type))){
                 return tradeQueryType;
             }
         }
         return null;
     }


}
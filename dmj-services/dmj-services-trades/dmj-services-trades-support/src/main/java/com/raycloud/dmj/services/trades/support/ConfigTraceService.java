package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.trade.ConfigTraceDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.ConfigTrace;
import com.raycloud.dmj.services.trades.IConfigTraceService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description ConfigTraceService
 * @Date 2021/8/4 7:57 下午
 * @Created 杨恒
 */
@Service
public class ConfigTraceService implements IConfigTraceService {

    @Resource
    private ConfigTraceDAO configTraceDAO;

    @Override
    public void addConfigTrace(Staff staff, ConfigTrace configTrace) {
        addConfigTrace(staff, Collections.singletonList(configTrace));
    }

    @Override
    public void addConfigTrace(Staff staff, List<ConfigTrace> configTraces) {
        try {
            for (ConfigTrace configTrace : configTraces) {
                String content = configTrace.getContent();
                // content超长截断处理 text 64kb
                if (content != null && content.length() > 10000) {
                    configTrace.setContent(content.substring(0, 10000));
                }
            }
            configTraceDAO.insert(staff, configTraces);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("保存配置修改记录失败，configTrace=%s", JSON.toJSONString(configTraces))), e);
        }
    }

    @Override
    public void removeConfigTrace(Staff staff, List<Long> ids) {
        try {
            configTraceDAO.delete(staff, ids);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("删除配置修改记录失败，ids=%s", ids)), e);
        }
    }

    @Override
    public List<ConfigTrace> query(Staff staff, Long configId, Integer configType) {
        List<ConfigTrace> tradeTraces = null;
        try {
            tradeTraces = configTraceDAO.query(staff, configId, configType);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("查询配置修改记录失败，configId=%s,configType=%s", configId, configType)), e);
        }
        return tradeTraces;
    }

    @Override
    public List<ConfigTrace> queryByConfigIds(Staff staff, Long[] configIds, Integer configType) {
        if (Objects.isNull(configIds) || configIds.length < 1) {
            return new ArrayList<>();
        }
        List<ConfigTrace> tradeTraces = null;
        try {
            tradeTraces = configTraceDAO.queryByConfigIds(staff, configIds, configType);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("查询配置修改记录失败，configIds=%s,configType=%s", Arrays.toString(configIds), configType)), e);
        }
        return tradeTraces;
    }
}

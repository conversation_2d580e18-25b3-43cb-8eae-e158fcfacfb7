package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.*;
import com.google.common.base.Function;
import com.google.common.collect.*;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.area.ErpAddressLibraryBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.logistics.LogisticsWarningBusiness;
import com.raycloud.dmj.business.modify.*;
import com.raycloud.dmj.business.operate.PayAmountCalculateBusiness;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.domain.utils.diamond.TradeSyncConfigUtils;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.business.stalls.SaleTradeV2Business;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dms.domain.basic.CooperateStateEnum;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.response.DmsDistributorInfoResponse;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.area.AddressNode;
import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.customer.basis.*;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.*;
import com.raycloud.dmj.domain.pt.model.print.*;
import com.raycloud.dmj.domain.pt.wlb.*;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.invoice.*;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.tradepay.TradePaySource;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.download.domain.*;
import com.raycloud.dmj.download.service.*;
import com.raycloud.dmj.express.api.*;
import com.raycloud.dmj.express.context.ImportOutSidsContext;
import com.raycloud.dmj.express.request.template.SearchLogisticsCompanyTemplateParams;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.*;
import com.raycloud.dmj.item.search.dto.ItemIdInfoDto;
import com.raycloud.dmj.item.search.request.*;
import com.raycloud.dmj.item.search.response.QueryByItemIdInfoListResponse;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.customer.basis.CmCustomerDubboService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.trade.config.TradeConfigContextBusiness;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trade.supplier.IOrderSupplierService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.utils.AddressParseUtil;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import com.raycloud.ec.api.*;
import com.raycloud.middle.client.business.common.ParseAddress;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.*;

import javax.annotation.*;
import java.math.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trades.Trade.*;

/**
 * <AUTHOR>
 * @since 16/2/23
 */
@Service("tradeIOService")
public class TradeIOService{

    public static final String[] TRADE_IMPORT_SINGLE_TITLE = new String[]{"收货人", "昵称（旺旺号）", "手机号码", "固定电话", "仓库名称", "省", "市", "区", "详细地址", "邮政编码", "商家编码/规格商家编码", "数量", "商品付款金额", "运费", "发票抬头", "买家留言", "卖家备注", "平台交易号", "下单时间", "付款时间", "主商家编码", "规格", "系统备注", "是否客户订单", "是否为档口订单", "分销商名称", "支付方式", "商品单价", "是否货到付款", "定金", "代收金额"};//商品单价取27
    public static final String[] TRADE_IMPORT_SINGLE_TITLE_MINI = new String[]{"收货人", "仓库名称", "省", "市", "区", "详细地址", "商家编码/规格商家编码", "数量", "商品付款金额", "平台交易号"};

    public static final String[] TRADE_IMPORT_MULTI_TITLE = new String[]{"收货人", "昵称（旺旺号）", "手机号码", "固定电话", "仓库名称", "省", "市", "区", "详细地址", "邮政编码", "商家编码/规格商家编码", "数量", "商品付款金额", "运费", "发票抬头", "买家留言", "卖家备注", "平台交易号", "下单时间", "付款时间", "店铺编号", "主商家编码", "规格", "系统备注", "是否客户订单", "是否为档口订单", "分销商名称", "支付方式", "商品单价", "是否货到付款", "定金", "代收金额"};//商品单价取28
    public static final String[] TRADE_IMPORT_MULTI_TITLE_MINI = new String[]{"收货人", "仓库名称", "省", "市", "区", "详细地址", "商家编码/规格商家编码", "数量", "商品付款金额", "平台交易号", "店铺编号"};

    private static final Logger logger = Logger.getLogger(TradeIOService.class);

    private static Map<String, String> TRADE_IMPORT_FILED_ALIAS_MAP = new HashMap<>();

    private static final String OUTER_IID_SPLIT = "#####——####";

    @Autowired
    private TbOrderDAO tbOrderDAO;

    @Autowired
    private TbTradeDao tbTradeDao;

    @Resource
    private IItemServiceWrapper itemServiceWrapper;

    @Resource
    private IdWorkerService idGenerator;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private IOrderStockService orderStockService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private ITradeUpdateService tradeUpdateService;

    @Resource
    private ITradePtService tradePtService;

    @Resource
    private IPrintService printAdapterService;

    @Resource
    private IOpLogService opLogService;

    @Resource
    IProgressService progressService;

    @Resource
    private ILockService lockService;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Resource
    private IUserExpressTemplateService templateService;

    @Resource
    private ITradeService tradeService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    private ITradeUserConfigService tradeUserConfigService;

    @Resource
    private TradeCostCalculateBusiness tradeCostCalculateBusiness;

    @Resource
    private IDownloadCenterFileService downloadCenterFileService;

    @Resource
    TradePayBusiness tradePayBusiness;

    @Resource
    CmCustomerDubboService cmCustomerDubboService;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    TradeSysLabelBusiness tradeTagUpdateBusiness;
    @Resource
    TradeFilterBusiness tradeFilterBusiness;
    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    SaleTradeV2Business saleTradeV2Business;
    @Resource
    TradeExtBusiness tradeExtBusiness;
    @Resource
    PayAmountCalculateBusiness payAmountCalculateBusiness;
    @Resource
    private IOrderSupplierService orderSupplierService;
    @Resource
    ITradeSalesmanService tradeSalesmanService;
    @Resource
    FxBusiness fxBusiness;
    @Resource
    TradeDataService tradeDataService;
    @Resource
    LogisticsWarningBusiness logisticsWarningBusiness;
    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    IUserExpressTemplateService userExpressTemplateService;

    @Resource
    ReMatchBusiness reMatchBusiness;

    @Resource
    ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;

    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Resource
    FeatureService featureService;
    @Resource
    IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;
    @Resource
    IUserLogisticsCompanyTemplateBusiness userLogisticsCompanyTemplateBusiness;
    @Resource
    ITradeMergeService tradeMergeService;
    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Resource
    ITradeInvoiceService tradeInvoiceService;

    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;


    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;


    @Resource
    ErpAddressLibraryBusiness erpAddressLibraryBusiness;

    @Resource
    TradeSysProductTimeoutActionTimeService tradeSysProductTimeoutActionTimeService;

    @Resource
    protected IUserService userService;

    @Resource
    TradeConfigContextBusiness tradeConfigContextBusiness;

    AttrCopier<Order, Order> orderCopier = new OrderCopier<Order, Order>();


    private final static int PALTFROM_TRADE = 1;
    private final static int SYS_TRADE_NOT_WAIT_AUDIT = 2;
    private final static int SYS_TRADE_WAIT_AUDIT_SPLIT = 3;
    private final static int SYS_TRADE_WAIT_AUDIT_MERGE = 4;
    private final static int SYS_TRADE_WAIT_AUDIT = 5;
    private final static int TRADE_UNSPLIT_TYPE = TradeSplitEnum.SPLIT_NORMAL.getDbType();
    private final static int TRADE_UNMERGE_TYPE = TradeMergeEnum.MERGE_NORMAL.getDbType();
    private final static String SUCCESS = "SUCCESS";

    private final static Set<String> OVERSEA_SOURCE = new HashSet<>();

    static {
        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_LAZADA);
        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_SHOPEE);
        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_SMT);
//        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_SHEIN); 目前是发中转仓
//        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_TEMU);目前是发中转仓
        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_YMX);
        OVERSEA_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_TIKTOK);

    }

    public boolean isOverSeaPlat(User user) {
        if (user == null) {
            return false;//多店铺校验   跨境只能单店铺导入
        }
        return OVERSEA_SOURCE.contains(user.getSource());
    }

    static {
        TRADE_IMPORT_FILED_ALIAS_MAP.put("订单留言", "买家留言");
        TRADE_IMPORT_FILED_ALIAS_MAP.put("订单备注", "卖家备注");
    }

    public static String getAliaName(String key) {
        return TRADE_IMPORT_FILED_ALIAS_MAP.get(key);
    }

    /**
     * 处理别名
     *
     * @param data
     */
    public static void fixAliaColumnName(String[][] data) {
        for (int i = 0; i < data[0].length; i++) {
            String columnName = StringUtils.trimToEmpty(data[0][i]);
            String aliasName = TradeIOService.getAliaName(columnName);
            if (aliasName != null) {
                data[0][i] = aliasName;
            }
        }
    }

//    @Override
    public void importTrades(Staff staff, User user, Integer needCover, Integer isGenerateSaleTrade, String[][] data, Map<String, Integer> titleMapToCol, boolean multiGoodsInSingleRow) {
        needCover = needCover == null ? 0 : needCover;
        fixAliaColumnName(data);
        List<String[][]> trueDatas = getTureTradeData(data, titleMapToCol, user);
        String[][] titleData = trueDatas.get(0);
        String[][] tradeData = trueDatas.get(1);
        List<String> errorMsg = new ArrayList<>();
        TradeImportResult tradeImportResult = assembly(staff, user, needCover, tradeData, errorMsg, titleMapToCol, multiGoodsInSingleRow);
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
        List<Trade> trades = tradeImportResult.getTradeList();
        List<Trade> updateTradeList = tradeImportResult.getUpdateTradeList();
        List<Trade> updateTradeListFiltered = new ArrayList<>();
        for (Trade trade : updateTradeList) {
            if (trade.getSid() != null && trade.getSid() > 0) {
                updateTradeListFiltered.add(trade);
            }
        }

        int tradeNum = tradeImportResult.tradeSuccess;
        //分批次插入交易
        int end, tradeCount = 0, orderCount = 0, batch = tradeNum / 100 + (tradeNum % 100 == 0 ? 0 : 1);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (int i = 0; i < batch; i++) {
            end = (i + 1) * 100; //当前批次的最后一笔trade
            if (end > trades.size()) {
                end = trades.size();
            }
            List<Trade> subNeedMatchWarehouseTrades = new ArrayList<>();
            List<Trade> list = trades.subList(i * 100, end);

            List<Order> insert_orders = new ArrayList<Order>();
            List<Order> update_orders = new ArrayList<Order>();

            //快递模版
            list.forEach(trade -> {
                trade.getOperations().put(OpEnum.TRADE_ADD, "EXCEL导入订单");
                if (tradeImportResult.needRematchWarehouseTrades.contains(trade)) {
                    subNeedMatchWarehouseTrades.add(trade);
                }

            });
            int tradeRow = tradeCount, orderRow = orderCount;
            tradeCount += list.size();

            //在插入前把需要更新的订单移出来
            List<Trade> updateTradeListTemp = new ArrayList<>(list.size());
            List<Trade> updateTradeListCheckedSidNotNull = new ArrayList<>();
            List<Trade> insertTradeListTemp = new ArrayList<>(list.size());
            insertTradeListTemp.addAll(list);

            if (needCover == 1) {
                if (!updateTradeListFiltered.isEmpty()) {
                    updateTradeListTemp.addAll(list);
                    updateTradeListTemp.retainAll(updateTradeListFiltered);
                    insertTradeListTemp.removeAll(updateTradeListTemp);
                    if (!updateTradeListTemp.isEmpty()) {
                        initTrade4Import(updateTradeListTemp, staff, true);
                        //对于 trade 或 order sid 为空的情况，进行过滤。 KMERP-96615
                        updateTradeListCheckedSidNotNull = filterSidNotExist(tradeImportResult, updateTradeListTemp);
                    }
                }
            }
            initializeTradeExtImport(staff, insertTradeListTemp);
            List<Long> sids = initTrade4Import(insertTradeListTemp, staff, false);
            //这里的orders有可能少了赠品 因为赠品匹配在后面 慎用
            insert_orders.addAll(TradeUtils.getOrders4Trade(insertTradeListTemp));
            update_orders.addAll(TradeUtils.getOrders4Trade(updateTradeListCheckedSidNotNull));
            //填充商品标签信息到order
            tradeOrderSyncItemTagFill.fillByItemDubbo(staff, insert_orders);
            //标记是否拣选验货
            OrderUtils.fillIsPick(insert_orders);

            orderCount += insert_orders.size() + update_orders.size();
            try {
                if (!insertTradeListTemp.isEmpty()) {
                    _insertTrades(staff, insertTradeListTemp, sids, tradeConfig, isGenerateSaleTrade);
                    importTradeAutoMerge(staff, user, insertTradeListTemp);
                    reMatchBusiness.reMatch(staff, TradeUtils.toSidList(insertTradeListTemp), null, TradeUtils.toSidList(subNeedMatchWarehouseTrades), EventEnum.EVENT_EXCEL_IMPORT_TRADE, Boolean.FALSE);
                    tradeSysLabelBusiness.matchSystemLabels(staff, Lists.newArrayList(insertTradeListTemp), Lists.newArrayList(SystemTags.TAG_NEED_INVOICE), false, true, true);
                }
                if (needCover == 1 && !updateTradeListCheckedSidNotNull.isEmpty()) {
                    List<TradeTrace> updateTradeTraceList = new ArrayList();
                    for (Trade trade : updateTradeListCheckedSidNotNull) {
                        if(tradeImportResult.updateLog.get(trade) != null) {
                            updateTradeTraceList.add(tradeImportResult.updateLog.get(trade));
                        }
                    }
                    _updateTrade(staff, updateTradeListCheckedSidNotNull, tradeConfig, isGenerateSaleTrade);
                    reMatchBusiness.reMatch(staff, TradeUtils.toSidList(updateTradeListCheckedSidNotNull), null, TradeUtils.toSidList(subNeedMatchWarehouseTrades), EventEnum.EVENT_EXCEL_IMPORT_TRADE, Boolean.FALSE);
                    tradeSysLabelBusiness.matchSystemLabels(staff, Lists.newArrayList(updateTradeListCheckedSidNotNull), Lists.newArrayList(SystemTags.TAG_NEED_INVOICE), false, true, true);
                    tradeTraceService.batchAddTradeTrace(staff, updateTradeTraceList);
                }
                progressData.setCountCurrent(progressData.getCountCurrent() != null ? progressData.getCountCurrent() + list.size() : list.size());
                progressData.setSucNum(progressData.getSucNum() != null ? progressData.getSucNum() + list.size() : list.size());
            } catch (Exception e) {
//                e.printStackTrace();
                for (int faileIndex = 0; faileIndex < list.size(); faileIndex++) {
                    tradeImportResult.removeTrade(list.get(faileIndex).getTid(), "导入失败:" + e.getMessage());
                }
                logger.error(LogHelper.buildLog(staff, String.format("共导入交易%s条, 子订单%s条", tradeRow, orderRow)), e);
                String msg = (orderRow == 0 ? "导入失败 :" : ("前" + orderRow + "行导入成功: ")) + e;
                errorMsg.add(msg);
                progressData.setCountCurrent(progressData.getCountCurrent() != null ? progressData.getCountCurrent() + list.size() : list.size());
                progressData.setErrorNum(progressData.getErrorNum() != null ? progressData.getErrorNum() + list.size() : list.size());
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
            }

            //设置缓存
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }

        String errorUrl = null;
        if (tradeImportResult.errorMsg.size() > 0) {
            errorUrl = exportErrorOrders(staff, titleData, tradeData, tradeImportResult);
        }
        progressData.setProgress(2);
        if (errorUrl != null) {
            progressData.setErrorUrl(errorUrl);
        }
        if (CollectionUtils.isNotEmpty(errorMsg)) {
            progressData.getErrorMsg().addAll(errorMsg);
        }

        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
    }

    private List<Trade> filterSidNotExist(TradeImportResult tradeImportResult, List<Trade> updateTradeListTemp) {
        List<Trade> updateTradeListCheckedSidNotNull = new ArrayList<>();
        for (Trade trade : updateTradeListTemp) {
            if (sidIsNull(trade, tradeImportResult)) {
                continue;
            }
            updateTradeListCheckedSidNotNull.add(trade);
        }
        return updateTradeListCheckedSidNotNull;
    }

    private boolean sidIsNull(Trade trade, TradeImportResult tradeImportResult) {
        if (trade.getSid() == null || trade.getSid() < 0) {
            tradeImportResult.removeTrade(trade.getTid(), String.format("导入失败:  更新订单tid:%s 时，trade.sid是空值参数 ", trade.getTid()));
            return true;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (order.getSid() == null || order.getSid() < 0) {
                tradeImportResult.removeTrade(trade.getTid(), String.format("导入失败:  更新订单tid:%s 时，order.sid是空值参数  %s ", JSON.toJSONString(order)));
                return true;
            }
        }
        return false;
    }


    /**
     * 参考注解：
     * 将原data中的空列(空列只出现在末尾)和"错误信息列"去掉。
     * 例如：
     * data:
     * [
     * ["收货人","手机号码","订单错误信息","详细地址",""],
     * ["成芳","123888419","实例","长沙",""]
     * ]
     * trueDatas：
     * {
     * [
     * ["收货人","手机号码","详细地址"]
     * ],
     * [
     * ["成芳","123888419","长沙"]
     * ]
     * }
     *
     * @param data
     * @return trueDatas
     */
    private List<String[][]> getTureTradeData(String[][] data, Map<String, Integer> titleMapToCol, User user) {//取出错误信息
        List<String[][]> trueDatas = new ArrayList<>();
        String[][] trueTitleData = new String[1][];
        String[][] trueTradeData = new String[data.length - 1][];
        trueDatas.add(trueTitleData);
        trueDatas.add(trueTradeData);
        String[] titleData = data[0];
        int titleIndex = -1;
        int columnLength = titleData.length;
        int emptyColumn = 0;
        for (int i = 0; i < titleData.length; i++) {
            if (titleData[i] != null && titleData[i].indexOf("错误信息") >= 0) {
                titleIndex = i;
            }
            if (titleData[i] == null || titleData[i].trim().length() <= 0) {
                emptyColumn++;
            }
        }

        if (titleIndex >= 0) {
            columnLength = titleData.length - 1 - emptyColumn;
        } else {
            columnLength = titleData.length - emptyColumn;
        }
        trueTitleData[0] = new String[columnLength];
//        System.out.println(columnLength+"@@@"+data[0].length);
        for (int j = 0, k = 0; j < data[0].length - emptyColumn; j++) {
            if (j != titleIndex) {
                trueTitleData[0][k] = data[0][j];
                k++;
            }
        }

        for (int i = 1; i < data.length; i++) {
//            System.out.println(data[i].length+"@@"+emptyColumn+"@@"+columnLength+"@@"+JSONObject.toJSONString(data[i]));
            trueTradeData[i - 1] = new String[columnLength];
            for (int j = 0, k = 0; j < data[0].length - emptyColumn; j++) {
                if (j != titleIndex) {
                    trueTradeData[i - 1][k] = data[i][j];
                    k++;
                }
            }
        }
        return fixTrueDatas(titleMapToCol, user, trueDatas);

    }

    @NotNull
    private List<String[][]> fixTrueDatas(Map<String, Integer> titleMapToCol, User user, List<String[][]> trueDatas) {
        //修复数据，将没有的列填充为空
        String[] fullTitle = user != null ? TRADE_IMPORT_SINGLE_TITLE : TRADE_IMPORT_MULTI_TITLE;
        String[] importFileTitle = trueDatas.get(0)[0];

        Set<String> titleSet = titleMapToCol.keySet();
        Set<String> needAddTitle = new HashSet<>();
        /**
         * importFileTitle  = {"收货人","仓库名称","省","市","区","详细地址","商家编码/规格商家编码","数量","商品付款金额","平台交易号"}
         * newFullTitle     = {"收货人","仓库名称","省","市","区","详细地址","商家编码/规格商家编码","数量","商品付款金额","平台交易号","下单时间","付款时间","主商家编码","规格","系统备注",....}
         */

        for (String colName : fullTitle) {
            if (!titleSet.contains(colName)) {
                needAddTitle.add(colName); //记录缺失的列
            }
        }
        if (needAddTitle.isEmpty()) return trueDatas;// 如果表格没有缺少列则直接返回

        String[] newFullTitle = new String[fullTitle.length];
        System.arraycopy(importFileTitle, 0, newFullTitle, 0, importFileTitle.length);
        Iterator<String> it = needAddTitle.iterator();
        int fullTitleLength = fullTitle.length;
        for (int i = importFileTitle.length; i < fullTitleLength; i++) {
            newFullTitle[i] = it.next();
            titleMapToCol.put(newFullTitle[i], i);
        }
        trueDatas.get(0)[0] = newFullTitle;
        String[][] trueTradeData = trueDatas.get(1);

        for (int i = 0; i < trueTradeData.length; i++) {
            String[] newFullTrade = new String[newFullTitle.length];
            String[] importFileTrade = trueTradeData[i];
            System.arraycopy(importFileTrade, 0, newFullTrade, 0, importFileTrade.length);
            for (int j = importFileTrade.length; j < newFullTrade.length; j++) {
                newFullTrade[j] = "";
            }
            trueTradeData[i] = newFullTrade;
        }
        return trueDatas;
    }

//    @Override
    public void importOutsids(final Staff staff, final Integer outsidImportType, final Integer needCover, final String[][] data) {
        long start = System.currentTimeMillis();
        //查询原订单信息 查询已审核待发货的，有快递模板的所有订单
        Page page = new Page().setPageNo(1).setPageSize(Integer.MAX_VALUE);
        TradeQueryParams params = new TradeQueryParams().setPage(page).setQueryFlag(1).setFields("sid, tid, out_sid, template_type, template_id, warehouse_id, logistics_company_id,merge_type, split_type, tag_ids, source, sub_source")
                .setQueryOrder(false).setIsCancel(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_WAIT_AUDIT)
                .setCheckActive(true).setIsOutstock(0);
//        List<Trade> existsTrades = queryExistTrades(staff, data, params);
        Map<String, String> tidUnMatchMap = new HashMap<>();
        Map<Long, String> sidUnMatchMap = new HashMap<>();
        List<Trade> existsTrades = queryExistTrades(staff, data, params, tidUnMatchMap, sidUnMatchMap);
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "shopee订单不支持该操作!", (trades) -> tradeFilterBusiness.filterShopeeTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "速卖通订单不支持该操作!", (trades) -> tradeFilterBusiness.filterSumaitongTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "lazada订单不支持该操作!", (trades) -> tradeFilterBusiness.filterLazadaTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "抖音厂商代发订单不支持该操作!", (trades) -> tradeFilterBusiness.filterFxgDfTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "得物直发订单不支持该操作!", (trades) -> tradeFilterBusiness.filterPoisonTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "得物直发订单不支持该操作!", (trades) -> tradeFilterBusiness.filterPoisonTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "Joom订单不支持该操作!", (trades) -> tradeFilterBusiness.filterJoomTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "Ozon订单不支持该操作!", (trades) -> tradeFilterBusiness.filterOzonTrades(trades, false));
        //运单号导入自动过滤阿里国际站订单
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "阿里巴巴国际站订单不支持该操作!", (trades) -> tradeFilterBusiness.filterAlibabaIcbuTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "tiktok订单不支持该操作!", (trades) -> tradeFilterBusiness.filterTiktokTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "淘宝厂商代发订单不支持该操作!", (trades) -> tradeFilterBusiness.filterTbDfTrades(trades, false));
        existsTrades = fillTrades(existsTrades, sidUnMatchMap, tidUnMatchMap, "快手代发订单不支持该操作!", (trades) -> tradeFilterBusiness.filterKSDfTrades(trades, false));
        List<Trade> updateTrades = new ArrayList<>();
        Map<String, Object> assemblyOutsdiMap;
        try {
            //装配能成功更新的订单,过滤掉有问题的行数.并且设置错误信息
            assemblyOutsdiMap = assemblyOutsid(staff, data, existsTrades, outsidImportType, needCover, sidUnMatchMap, tidUnMatchMap);
            if (!Objects.isNull(assemblyOutsdiMap)) {
                updateTrades = (List<Trade>) assemblyOutsdiMap.get("updateTrades");
            }
        } catch (Exception e) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT);
            progressData.setProgress(2);
            progressData.setErrorMsg(Arrays.asList("组装运单号报错" + e.getMessage()));
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
            logger.error(LogHelper.buildLogHead(staff).append("组装运单号报错"), e);
            throw new TradeException("组装运单号报错");
        }

        StringBuilder updateSidBuild = new StringBuilder();

        //分批次更新订单
        int end, c = updateTrades.size() / 250 + (updateTrades.size() % 250 == 0 ? 0 : 1);
        for (int i = 0; i < c; i++) {
            end = (i + 1) * 250;
            if (end > updateTrades.size()) {
                end = updateTrades.size();
            }
            List<Trade> batchList = new ArrayList<>(updateTrades.subList(i * 250, end));
            try {
                tradePtService.saveByTrades(staff, batchList);
                tradeUpdateService.updateTrades(staff, batchList);//只插入运单号与模板
                //发货上传 打标签
                if (null != outsidImportType && 2 == outsidImportType) {
                    tradeTagUpdateBusiness.addTags(staff, batchList, OpEnum.TAG_UPDATE, Lists.newArrayList(SystemTags.TAG_IMPORT_DELIVER));
                }
                if (outsidImportType.equals(1)) {//手写快递
                    StringBuilder expressBuild = new StringBuilder();
                    StringBuilder kjBuild = new StringBuilder();
                    for (Trade trade : batchList) {
                        Integer templateType = Optional.ofNullable(trade.getTemplateType()).orElse(0);
                        if (0 == templateType) {
                            expressBuild.append(trade.getSid()).append(",");
                        } else if (2 == templateType) {
                            kjBuild.append(trade.getSid()).append(",");
                        }
                    }
                    if (expressBuild.length() > 0) {
                        String expressSidsStr = expressBuild.deleteCharAt(expressBuild.length() - 1).toString();
                        Long[] sidArray = ArrayUtils.toLongArray(StringUtils.split(expressSidsStr, ","));
                        IPrintRequest printRequest = new EndPrintTimeRequest(sidArray, "手写快递", null);
                        printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
                        printAdapterService.endPrintTime(staff, printRequest);
                        updateSidBuild.append(expressBuild);
                    }
                } else if (outsidImportType.equals(2)) {
                    IPrintRequest printRequest = new EndPrintTimeRequest(TradeUtils.toSids(batchList), "手写快递", null);
                    printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
                    ((EndPrintTimeRequest) printRequest).setExceptionPrint(true);
                    printAdapterService.endPrintTime(staff, printRequest);
                    updateSidBuild.append(StringUtils.join(TradeUtils.toSids(batchList), ","));
                } else if (outsidImportType.equals(3)) {
                    //导入直接上传发货
                    tradeService.consignUpload(staff, TradeUtils.toSids(batchList), SendType.UPLOAD, null, false, 0, null);
                    updateSidBuild.append(StringUtils.join(TradeUtils.toSids(batchList), ","));
                } else {
                    for (Trade trade : batchList) {
                        updateSidBuild.append(trade.getSid()).append(",");
                    }
                }
            } catch (Exception e) {
                logger.error("第" + (i + 1) + "批次导入运单号报错", e);
            }

            // 获取运单号如果是拆单并且是缺货的上传备注到平台
            if (CollectionUtils.isNotEmpty(batchList) && tradeConfigContextBusiness.isOpen(staff, TradeConfigEnum.OPEN_UPLOAD_SELLER_MEMO)) {
                List<Trade> uploadMemos = tradeSearchService.queryBySids(staff, false, TradeUtils.toSids(batchList));
                List<Trade> uploadMemoFilters = uploadMemos.stream().filter(trade -> StringUtils.isNotBlank(trade.getOutSid()) && TradeUtils.isSplit(trade)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(uploadMemoFilters)) {
                    eventCenter.fireEvent(this, new EventInfo("check.splitInsufficient.update").setArgs(new Object[]{staff, uploadMemoFilters}), false);
                }
            }

            if (outsidImportType.equals(1)) {
                recodeOpLog(opLogService, staff, "importOutsidAndWriteExpressEnd", progressService.getProgressKey(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT), "导入运单号并手写面单快递单，系统单号：" + (updateSidBuild.length() == 0 ? "" : updateSidBuild.deleteCharAt(updateSidBuild.length() - 1)), null);
            } else if (outsidImportType.equals(2)) {
                recodeOpLog(opLogService, staff, "importOutsidAndconsignUpload", progressService.getProgressKey(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT), "导入运单号并上传发货，系统单号：" + updateSidBuild, null);
            } else if (outsidImportType.equals(3)) {
                recodeOpLog(opLogService, staff, "importOutsidAndUpload", progressService.getProgressKey(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT), "导入运单号直接上传发货，系统单号：" + updateSidBuild, null);
            } else {
                recodeOpLog(opLogService, staff, "importOutsid", progressService.getProgressKey(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT), "导入运单号，系统单号：" + (updateSidBuild.length() == 0 ? "" : updateSidBuild.deleteCharAt(updateSidBuild.length() - 1)), null);
            }
            eventCenter.fireEvent(this, new EventInfo("trade.import.outsid").setArgs(new Object[]{staff}), batchList);

            // 订阅物流预警
            subscribeLogisticsEvent(staff, batchList);
        }
        logger.debug(LogHelper.buildLogHead(staff).append("导入excel运单号结束，影响的订单有：" + updateSidBuild));

        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT);
        //导入错误信息到excel
        if (!Objects.isNull(assemblyOutsdiMap)) {
            List<String[]> errorList = (List<String[]>) assemblyOutsdiMap.get("errorList");
            String errorUrl = null;
            if (errorList.size() > 0) {
                errorUrl = exportErrorOutsid(staff, errorList);
            }
            if (errorUrl != null) {
                progressData.setErrorUrl(errorUrl);
            }
        }
        progressData.setProgress(2);
        progressData.setCountCurrent(data.length);
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("导入Excel订单成功,共%s行,其中失败%s行，耗时%s ms", data.length, progressData.getErrorMsg().size(), (System.currentTimeMillis() - start))));
        }
    }


    /**
     * 订阅物流预警事件
     *
     * @param staff
     * @param updateTrades
     */
    public void subscribeLogisticsEvent(Staff staff, List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        Long[] sidArray = updateTrades.stream().map(Trade::getSid).toArray(Long[]::new);
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, true, sidArray);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 过滤跨境订单
        trades = logisticsWarningBusiness.filterTrade(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 将分组后的订单，继续按模板维度分组
        Map<String, List<Trade>> templateTradeMap = trades.stream().collect(Collectors.groupingBy(x -> (x.getTemplateId() == null ? "0" : x.getTemplateId()) + "_" + x.getTemplateType()));
        // 迭代分组
        templateTradeMap.forEach((key, templateGroupList) -> {
            // 将模板分组后的订单，如果选择的是按照店铺打印发货地址，需要按照店铺进行分组
            String templateId = key.split("_")[0];
            String templateType = key.split("_")[1];

            IExpressTemplateBase template = null;
            if ("1".equals(templateType)) {
                template = userWlbExpressTemplateService.userQuery(staff, Long.parseLong(templateId), false);
            } else {
                template = userExpressTemplateService.userQuery(staff, Long.parseLong(templateId), false);
            }
            // 简化订单
            List<Trade> tradeList = new ArrayList<>();
            for (Trade trade : templateGroupList) {
                tradeList.add(LogisticsWarningBusiness.buildTrade(trade, template));
            }

            // 订阅快递助手并生成物流预警任务
            eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.subscribe").setArgs(new Object[]{staff, template, tradeList}), null);
        });
    }

    private List<Trade> fillTrades(List<Trade> existsTrades, Map<Long, String> sidUnMatchMap, Map<String, String> tidUnMatchMap, String errMsg, java.util.function.Function<List<Trade>, List<Trade>> function) {
        List<Trade> fillResult = Optional.ofNullable(function.apply(existsTrades)).orElse(new ArrayList<>());
        Map<Long, Trade> sid2Trades = fillResult.stream().collect(Collectors.toMap(Trade::getSid, java.util.function.Function.identity(), (a, b) -> b));
        for (Trade trade : existsTrades) {
            if (!sid2Trades.containsKey(trade.getSid())) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】%s", trade.getSid(), errMsg));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】%s", trade.getTid(), errMsg));
            }
        }
        return fillResult;
    }

    /**
     * 查询 在Excel表格中的订单
     *
     * @param staff
     * @param data
     * @param params
     * @return
     */
    @NotNull
    protected List<Trade> queryExistTrades(Staff staff, String[][] data, TradeQueryParams params, Map<String, String> tidUnMatchMap, Map<Long, String> sidUnMatchMap) {
        ArrayList<Long> sidList = new ArrayList<>();
        ArrayList<String> tidList = new ArrayList<>();
        for (int i = 0; i < data.length; i++) {
            String[] d = data[i];
            String sidStr = d[1];
            if (StringUtils.isNotBlank(sidStr)) {
                try {
                    long sid = Long.parseLong(sidStr);
                    sidList.add(sid);
                } catch (Throwable e) {
                    logger.error(LogHelper.buildLogHead(staff).append("sidStr:").append(sidStr), e);
                }
            }
            String tidStr = d[2];
            if (StringUtils.isNotBlank(tidStr)) {
                tidList.add(tidStr);
            }
        }
        Set<Long> filterSidTable = new HashSet<>();
        List<Trade> existsTrades = new LinkedList<>();
        Map<Long, Integer> userActiveMap = new HashMap<>();
        if (!sidList.isEmpty()) {
            List<List<Long>> sidLists = Lists.partition(sidList, 500);
            for (List<Long> sidQueryTemp : sidLists) {
                params.setSid(sidQueryTemp.toArray(new Long[]{}));
                List<Trade> list = tradeSearchService.search(staff, params).getList();
                params.setSid(null);
                analyseSidUnMatch(staff, sidQueryTemp, list, userActiveMap, sidUnMatchMap, tidUnMatchMap);
                if (list == null) continue;
                for (Trade tradetemp : list) {
                    int startSize = filterSidTable.size();
                    filterSidTable.add(tradetemp.getSid());
                    if (startSize - filterSidTable.size() != 0) {
                        existsTrades.add(tradetemp);
                    }
                }
            }
        }
        if (!tidList.isEmpty()) {
            List<List<String>> tidLists = Lists.partition(tidList, 500);
            for (List<String> tidQueryTemp : tidLists) {
                params.setTid(tidQueryTemp.toArray(new String[]{}));
                List<Trade> list = tradeSearchService.search(staff, params).getList();
                params.setTid(null);
                analyseTidUnMatch(staff, tidQueryTemp, list, userActiveMap, sidUnMatchMap, tidUnMatchMap);
                if (list == null) continue;
                for (Trade tradetemp : list) {
                    int startSize = filterSidTable.size();
                    filterSidTable.add(tradetemp.getSid());
                    if (startSize - filterSidTable.size() != 0) {
                        existsTrades.add(tradetemp);
                    }
                }
            }
        }
        return existsTrades;
    }

    private void analyseSidUnMatch(Staff staff, List<Long> sidQueryTemp, List<Trade> list, Map<Long, Integer> userMap, Map<Long, String> sidUnMatchMap, Map<String, String> tidUnMatchMap) {
        List<Long> originSids = new ArrayList<>(sidQueryTemp);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> matchSids = list.stream().map(Trade::getSid).collect(Collectors.toList());
            originSids.removeAll(matchSids);
        }
        //重新查询未匹配的订单，分析原因
        List<TbTrade> singleTrades = tradeSearchService.queryByKeys(staff, "sid, tid, user_id, is_cancel, sys_status", "sid", originSids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(singleTrades)) {
            originSids.forEach(t -> {
                sidUnMatchMap.put(t, String.format("【系统订单号:%s】ERP系统内不存在该订单", t));
            });
            return;
        }
        List<Long> userIds = singleTrades.stream().map(Trade::getUserId).filter(t -> !userMap.containsKey(t)).collect(Collectors.toList());
        List<User> users = userService.queryByIdList(staff.getCompanyId(), userIds);
        users.forEach(t -> {
            userMap.put(t.getId(), t.getActive());
        });
        List<Long> matchSid = new ArrayList<>();
        for (Trade trade : singleTrades) {
            matchSid.add(trade.getSid());
            if (Integer.valueOf(0).equals(userMap.get(trade.getUserId()))) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】店铺已停用", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】店铺已停用", trade.getTid()));
                continue;
            }
            if (TradeUtils.isCancel(trade)) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已取消", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已取消", trade.getTid()));
                continue;
            }
            if (TradeUtils.isAfterSendGoods(trade)) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已发货", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已发货", trade.getTid()));
                continue;
            }
            sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已退款或其他原因", trade.getSid()));
            tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已退款或其他原因", trade.getTid()));
        }
        originSids.removeAll(matchSid);
        originSids.forEach(t -> {
            sidUnMatchMap.put(t, String.format("【系统订单号:%s】ERP系统内不存在该订单", t));
        });
    }

    private void analyseTidUnMatch(Staff staff, List<String> tidQueryTemp, List<Trade> list, Map<Long, Integer> userMap, Map<Long, String> sidUnMatchMap, Map<String, String> tidUnMatchMap) {
        List<String> originTids = new ArrayList<>(tidQueryTemp);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> matchSids = list.stream().map(Trade::getTid).collect(Collectors.toList());
            originTids.removeAll(matchSids);
        }
        //过滤掉，sid查询时已经标记的异常
        originTids = originTids.stream().filter(t -> !tidUnMatchMap.containsKey(t)).collect(Collectors.toList());
        //重新查询未匹配的订单，分析原因
        List<TbTrade> singleTrades = tradeSearchService.queryByKeys(staff, "sid, tid, user_id, is_cancel, sys_status", "tid", originTids.toArray(new String[0]));
        if (CollectionUtils.isEmpty(singleTrades)) {
            originTids.forEach(t -> {
                tidUnMatchMap.put(t, String.format("【平台订单号:%s】ERP系统内不存在该订单", t));
            });
            return;
        }
        List<Long> userIds = singleTrades.stream().map(Trade::getUserId).filter(t -> !userMap.containsKey(t)).collect(Collectors.toList());
        List<User> users = userService.queryByIdList(staff.getCompanyId(), userIds);
        users.forEach(t -> {
            userMap.put(t.getId(), t.getActive());
        });
        List<String> matchTids = new ArrayList<>();
        for (Trade trade : singleTrades) {
            matchTids.add(trade.getTid());
            if (Integer.valueOf(0).equals(userMap.get(trade.getUserId()))) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】店铺已停用", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】店铺已停用", trade.getTid()));
                continue;
            }
            if (TradeUtils.isCancel(trade)) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已取消", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已取消", trade.getTid()));
                continue;
            }
            if (TradeUtils.isAfterSendGoods(trade)) {
                sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已发货", trade.getSid()));
                tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已发货", trade.getTid()));
                continue;
            }
            sidUnMatchMap.put(trade.getSid(), String.format("【系统订单号:%s】该订单在ERP已退款或其他原因", trade.getSid()));
            tidUnMatchMap.put(trade.getTid(), String.format("【平台订单号:%s】该订单在ERP已退款或其他原因", trade.getTid()));
        }
        originTids.removeAll(matchTids);
        originTids.forEach(t -> {
            tidUnMatchMap.put(t, String.format("【平台订单号:%s】ERP系统内不存在该订单", t));
        });
    }

    private Map<String, Object> assemblyOutsidNew(Staff staff, String[][] data, List<Trade> existsTrades, Integer importType, Integer needCover) {
        Assert.notEmpty(existsTrades, "无满足条件的订单");
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT);
        progressData.setCountAll(data.length);
        progressData.setProgress(1);
        List<String> errorMsgs = progressData.getErrorMsg();
        Map<String, String> outsid2Sid = new LinkedHashMap<String, String>();
        int progressCount = 0;

        Map<String, Trade> sid2ExistsTrade = constructSid2TradeMapping(existsTrades);
        Multimap<String, Trade> tid2ExistsTrade = constructTid2TradeMapping(existsTrades);
        //查询快递模板
        Map<String, UserExpressTemplate> templateMap = getTemplateMap(staff, data);
        //错误信息list,后续导出到excel
        List<String[]> errorList = new LinkedList<>();
        //需要更新的tradeList
        List<Trade> updateTrades = Lists.newArrayList();
        List<Trade> needLabelTrades = new ArrayList<>();
        for (int i = 0; i < data.length; i++) {
            String[] d = data[i];
            String columnNo = d[0];
            String sid = d[1];
            String tid = d[2];
            String templateName = d[3];
            String outsid = d[4];
            //初始化错误excel中的数据
            String[] erroreArray = new String[6];
            erroreArray[0] = columnNo;
            erroreArray[1] = sid;
            erroreArray[2] = tid;
            erroreArray[3] = tid;
            erroreArray[4] = outsid;

            if (StringUtils.isBlank(sid) && StringUtils.isBlank(tid)) {
                continue;
            } else if (StringUtils.isBlank(sid) && StringUtils.isNotBlank(tid)) {
                Collection<Trade> trades = tid2ExistsTrade.get(tid);
                if (trades.size() == 0) {
                    erroreArray[5] = String.format("【平台订单号:%s】" + "单号未匹配", tid);
                    errorList.add(erroreArray);
                    errorMsgs.add(erroreArray[5]);
                    continue;
                }
                if (trades.size() > 1) {
                    erroreArray[5] = String.format("【平台订单号:%s】" + "该订单对应的是拆合单", tid);
                    errorList.add(erroreArray);
                    errorMsgs.add(erroreArray[5]);
                    continue;
                }
                Iterator<Trade> it = trades.iterator();
                Trade trade = it.next();
                if (TradeUtils.isMerge(trade) || TradeUtils.isSplit(trade)) {
                    erroreArray[5] = String.format("【平台订单号:%s】" + "该订单对应的是拆合单", tid);
                    errorList.add(erroreArray);
                    errorMsgs.add(erroreArray[5]);
                    continue;
                }
                sid = trade.getSid() + "";
            }

            if (StringUtils.isBlank(outsid)) {
                erroreArray[5] = String.format("【系统订单号:%s,平台订单号:%s】该订单没有填写对应运单号", sid, tid);
                errorList.add(erroreArray);
                errorMsgs.add(erroreArray[5]);
                continue;
            }
            if (outsid.startsWith("-") || "N/A".equals(outsid)) {
                erroreArray[5] = String.format("【系统订单号:%s】该订单对应运单号格式不对", sid);
                errorList.add(erroreArray);
                errorMsgs.add(erroreArray[5]);
                continue;
            }
            if (outsid2Sid.containsKey(outsid)) {
                erroreArray[5] = String.format("【系统订单号:%s】该订单的运单号重复", sid);
                errorList.add(erroreArray);
                errorMsgs.add(erroreArray[5]);
                continue;
            }
            outsid2Sid.put(outsid, sid);

            //开始模板判断
            //判断sid在系统中是否存在
            Trade existsTrade = sid2ExistsTrade.get(sid);
            if (null == existsTrade) {
                erroreArray[5] = String.format("【系统订单号:%s】单号未匹配", sid);
                errorList.add(erroreArray);
                errorMsgs.add(erroreArray[5]);
                continue;
            }
            UserExpressTemplate userExpressTemplate = templateMap.get(templateName);
            if (null != existsTrade) {
                //已有模版或单号则不导入
                //已有模版或单号则直接覆盖,为什么这里没有处理这个选项,因为会在后面直接更新掉trade中的outsid,templateId.
                if (null == needCover || 0 == needCover) {
                    if (StringUtils.isNotBlank(existsTrade.getOutSid())) {
                        erroreArray[5] = String.format("【系统订单号:%s】运单号已填写", sid);
                        errorList.add(erroreArray);
                        errorMsgs.add(erroreArray[5]);
                        continue;
                    }
                    //已经有模板的情况下,校验快递模板与系统分配快递模板是否相同,相同可以覆盖,不相同则提示错误--沐月
                    if (null != userExpressTemplate && null != existsTrade.getTemplateId() && !existsTrade.getTemplateId().equals(userExpressTemplate.getId())) {
                        erroreArray[5] = String.format("【系统订单号:%s】快递模板已选择且与导入模板不同", sid);
                        errorList.add(erroreArray);
                        errorMsgs.add(erroreArray[5]);
                        continue;
                    }
                }
                //不校验原模板
                /*if (1 == existsTrade.getTemplateType()) {
                    errorMsgs.add(String.format("【系统订单号:%s】菜鸟、云打印、网点等电子模板无法导入运单号", entry.getValue()));
                    it.remove();
                    continue;
                }*/
                if (null != existsTrade.getOutSid() && existsTrade.getOutSid().equalsIgnoreCase(outsid)) {
                    erroreArray[5] = String.format("【系统订单号:%s】运单号重复", sid);
                    errorList.add(erroreArray);
                    errorMsgs.add(erroreArray[5]);
                    continue;
                }
                if ((existsTrade.getTemplateId() == null || existsTrade.getTemplateId() < 0) && StringUtils.isEmpty(templateName)) {
                    erroreArray[5] = String.format("【系统订单号:%s】订单没有快递模板且excel中未填快递模板", sid);
                    errorList.add(erroreArray);
                    errorMsgs.add(erroreArray[5]);
                    continue;
                }
            }
            if (StringUtils.isNotBlank(templateName) && null == userExpressTemplate) {
                erroreArray[5] = String.format("【系统订单号:%s】模板名称'%s'为电子模板或者不存在，未匹配到模板", sid, templateName);
                errorList.add(erroreArray);
                errorMsgs.add(erroreArray[5]);
                continue;
            }
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(Long.valueOf(sid));
            updateTrade.setOutSid(outsid);
            updateTrade.setTemplateType(existsTrade.getTemplateType());
            //发货上传 打标签
            if (null != importType && 2 == importType) {
                updateTrade.setTagIds(existsTrade.getTagIds());
                needLabelTrades.add(updateTrade);
            }
            updateTrades.add(updateTrade);
            existsTrade.setOutSid(outsid);
            if (null != userExpressTemplate) {
                updateTrade.setTemplateType(0);
                updateTrade.setTemplateId(userExpressTemplate.getId());
                existsTrade.setTemplateType(0);
                existsTrade.setTemplateId(userExpressTemplate.getId());
                existsTrade.setTemplateName(templateName);
            }

            progressCount++;
            if (progressCount % 500 == 0) {
                progressData.setCountCurrent(progressCount / 3);
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
            }
        }
        tradeTagUpdateBusiness.addTags(staff, needLabelTrades, OpEnum.TAG_UPDATE, Lists.newArrayList(SystemTags.TAG_IMPORT_DELIVER));
        Map<String, Object> resultmap = new HashMap<>();
        resultmap.put("errorList", errorList);
        resultmap.put("updateTrades", updateTrades);
        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
        return resultmap;
    }

    //之前的的实现
    private String exportErrorOutsid(Staff staff, List<String[]> errorList) {
        //excel表头
        String[][] excelTitle = new String[1][];
        List<String> titleList = new ArrayList<>();
        titleList.add("序号");
        titleList.add("系统订单号");
        titleList.add("平台订单号");
        titleList.add("快递模板");
        titleList.add("运单号");
        titleList.add("错误详情");
        excelTitle[0] = new String[titleList.size()];
        titleList.toArray(excelTitle[0]);
        //excel数据内荣
        String[][] errorArray = new String[errorList.size()][];
        errorList.toArray(errorArray);
        if (errorArray == null || errorArray.length == 0) {
            return null;
        }

        IDownloadCenterCallback callback = new IDownloadCenterCallback() {
            @Override
            public DownloadResult callback(DownloadParam downloadParam) throws Exception {
                DownloadResult result = new DownloadResult();
                result.setFlag(false);
                result.setData(errorArray);

                return result;
            }
        };
        String errorUrl = "";
        FileDownloadParam param = new FileDownloadParam();
        try {
            param.setTimeStampFileName("运单导入失败数据");
            //excel的标题
            param.setExcelTitle("运单导入失败数据");
            param.setTitleArr(excelTitle);
            param.setModule(EnumDownloadCenterModule.TRADE.getCode());
            //这个就会把记录标记为tj操作的,不需要
//            staff.setShadowToken("shadowToken");

            DownloadResult downloadResult = downloadCenterFileService.exportExcel(staff, param, callback);
            if (downloadResult != null) {
                errorUrl = downloadResult.getFileUrl();
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("%s模块导出文件失败", param.getModule())), e);
        }
        return errorUrl;
    }

    private Map<String, Object> assemblyOutsid(Staff staff, String[][] data, List<Trade> existsTrades, Integer importType,
                                               Integer needCover, Map<Long, String> sidUnMatchMap, Map<String, String> tidUnMatchMap) {
        Assert.notEmpty(existsTrades, "无满足条件的订单");
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT);
        progressData.setCountAll(data.length);
        progressData.setProgress(1);
        List<String> errorMsgs = progressData.getErrorMsg();
//        Map<String, String> outsid2Sid = new LinkedHashMap<String, String>();
        int progressCount = 0;

        Map<String, Trade> sid2ExistsTrade = constructSid2TradeMapping(existsTrades);
        Multimap<String, Trade> tid2ExistsTrade = constructTid2TradeMapping(existsTrades);

        ImportOutSidsContext context = null;
        Map<String, UserExpressTemplate> templateMap = null;
        // 是否按快递公司维度处理
        boolean hasVirtualWarehouse = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
        if (hasVirtualWarehouse) {
            context = buildImportOutSidsContext(staff, data);
        } else {
            //查询快递模板
            templateMap = getTemplateMap(staff, data);
        }
        //错误信息list,后续导出到excel
        List<String[]> errorList = new LinkedList<>();
        //需要更新的tradeList
        List<Trade> updateTrades = Lists.newArrayList();

        for (int i = 0; i < data.length; i++) {
            String[] d = data[i];
            String columnNo = d[0];
            String sid = d[1];
            String tid = d[2];
            String templateName = d[3];
            String outsid = d[4];
            //初始化错误excel中的数据
            String[] errorArray = new String[6];
            errorArray[0] = columnNo;
            errorArray[1] = sid;
            errorArray[2] = tid;
            errorArray[3] = templateName;
            errorArray[4] = outsid;

            if (StringUtils.isBlank(sid) && StringUtils.isBlank(tid)) {
                continue;
            } else if (StringUtils.isBlank(sid) && StringUtils.isNotBlank(tid)) {
                Collection<Trade> trades = tid2ExistsTrade.get(tid);
                if (trades.size() == 0) {
                    if (tidUnMatchMap.containsKey(tid)) {
                        errorArray[5] = tidUnMatchMap.get(tid);
                    } else {
                        errorArray[5] = String.format("【平台订单号:%s】" + "单号未匹配", tid);
                    }
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                if (trades.size() > 1) {
                    errorArray[5] = String.format("【平台订单号:%s】" + "该订单对应的是拆合单", tid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                Iterator<Trade> it = trades.iterator();
                Trade trade = it.next();
                if (TradeUtils.isMerge(trade) || TradeUtils.isSplit(trade)) {
                    errorArray[5] = String.format("【平台订单号:%s】" + "该订单对应的是拆合单", tid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                sid = trade.getSid() + "";
            }
            if (StringUtils.isBlank(outsid)) {
                errorArray[5] = String.format("【系统订单号:%s,平台订单号:%s】该订单没有填写对应运单号", sid, tid);
                errorList.add(errorArray);
                errorMsgs.add(errorArray[5]);
                continue;
            }
            outsid = outsid.trim();
            if (outsid.contains(" ")) {
                errorArray[5] = String.format("【系统订单号:%s】该订单对应运单号中有空格", sid);
                errorList.add(errorArray);
                errorMsgs.add(errorArray[5]);
                continue;
            }
            if (outsid.startsWith("-") || "N/A".equals(outsid)) {
                errorArray[5] = String.format("【系统订单号:%s】该订单对应运单号格式不对", sid);
                errorList.add(errorArray);
                errorMsgs.add(errorArray[5]);
                continue;
            }
            if (outsid.length() > 30) {
                errorArray[5] = String.format("【系统订单号:%s】【运单号:%s】该订单对应运单号长度太长,限制30位以内", sid, outsid);
                errorList.add(errorArray);
                errorMsgs.add(errorArray[5]);
                continue;
            }
            //判断sid在系统中是否存在
            Trade existsTrade = sid2ExistsTrade.get(sid);
            if (null == existsTrade) {
                Long format = StringUtils.isBlank(sid) ? null : Long.valueOf(sid.trim());
                if (sidUnMatchMap.containsKey(format)) {
                    errorArray[5] = sidUnMatchMap.get(format);
                } else {
                    errorArray[5] = String.format("【系统订单号:%s】单号未匹配", sid);
                }
                errorList.add(errorArray);
                errorMsgs.add(errorArray[5]);
                continue;
            }
            //已有模版或单号则不导入
            if (null == needCover || 0 == needCover) {
                if (StringUtils.isNotBlank(existsTrade.getOutSid())) {
                    errorArray[5] = String.format("【系统订单号:%s】运单号已填写", sid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                if (null != existsTrade.getOutSid() && existsTrade.getOutSid().equalsIgnoreCase(outsid)) {
                    errorArray[5] = String.format("【系统订单号:%s】运单号重复", sid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
            }

            Long templateId = null;
            Long logisticsCompanyId = null;
            if (hasVirtualWarehouse) {
                UserLogisticsCompanyTemplate userLogisticsCompanyTemplate = context.getUserLogisticsCompanyTemplateMap().get(templateName);
                templateId = userLogisticsCompanyTemplate == null ? null : userLogisticsCompanyTemplate.getTemplateId();
                logisticsCompanyId = userLogisticsCompanyTemplate == null ? null : userLogisticsCompanyTemplate.getLogisticsCompanyId();
                UserLogisticsCompany userLogisticsCompany = context.getUserLogisticsCompanyMap().get(templateName);
                List<Long> warehouseIds = userLogisticsCompany == null ? null : userLogisticsCompany.getWarehouseIdList();
                if (null == needCover || 0 == needCover) {
                    if (null != templateId && null != existsTrade.getTemplateId()
                            && -1 != existsTrade.getTemplateId() && !existsTrade.getTemplateId().equals(templateId)) {
                        errorArray[5] = String.format("【系统订单号:%s】快递公司名称已选择且与导入的快递公司名称不同", sid);
                        errorList.add(errorArray);
                        errorMsgs.add(errorArray[5]);
                        continue;
                    }
                }
                if (StringUtils.isNotBlank(templateName) && null == templateId) {
                    errorArray[5] = String.format("【系统订单号:%s】快递公司名称不存在或匹配到的电子面单不为普通电子面单", sid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                if ((existsTrade.getLogisticsCompanyId() == null || existsTrade.getLogisticsCompanyId() < 0) && StringUtils.isEmpty(templateName)) {
                    errorArray[5] = String.format("【系统订单号:%s】订单没有快递公司名称且excel中未填快递公司名称", sid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                if (CollectionUtils.isEmpty(warehouseIds) || !warehouseIds.contains(existsTrade.getWarehouseId())) {
                    errorArray[5] = String.format("【系统订单号:%s】【excel快递公司名称:%s】【匹配快递公司名称:%s】订单所属仓库没有该快递公司名称。", sid, templateName, userLogisticsCompany == null ? null : userLogisticsCompany.getName());
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
            } else {
                templateId = templateMap.get(templateName) == null ? null : templateMap.get(templateName).getId();
                if (null == needCover || 0 == needCover) {
                    //已经有模板的情况下,校验快递模板与系统分配快递模板是否相同,相同可以覆盖,不相同则提示错误
                    if (null != templateId && null != existsTrade.getTemplateId()
                            && -1 != existsTrade.getTemplateId() && !existsTrade.getTemplateId().equals(templateId)) {
                        errorArray[5] = String.format("【系统订单号:%s】快递模板已选择且与导入模板不同", sid);
                        errorList.add(errorArray);
                        errorMsgs.add(errorArray[5]);
                        continue;
                    }
                }
                if ((existsTrade.getTemplateId() == null || existsTrade.getTemplateId() < 0) && StringUtils.isEmpty(templateName)) {
                    errorArray[5] = String.format("【系统订单号:%s】订单没有快递模板且excel中未填快递模板", sid);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
                if (StringUtils.isNotBlank(templateName) && null == templateId) {
                    errorArray[5] = String.format("【系统订单号:%s】模板名称'%s'为电子模板或者不存在，未匹配到模板", sid, templateName);
                    errorList.add(errorArray);
                    errorMsgs.add(errorArray[5]);
                    continue;
                }
            }

            Trade updateTrade = new TbTrade();
            updateTrade.setSid(Long.valueOf(sid));
            updateTrade.setOutSid(outsid);
            updateTrade.setTemplateType(existsTrade.getTemplateType());
            //发货上传 打标签
            if (null != importType && 2 == importType) {
                updateTrade.setTagIds(existsTrade.getTagIds());
            }
            updateTrades.add(updateTrade);
            existsTrade.setOutSid(outsid);
            if (null != templateId) {
                updateTrade.setTemplateType(0);
                updateTrade.setTemplateId(templateId);
                updateTrade.setLogisticsCompanyId(logisticsCompanyId);
                existsTrade.setTemplateType(0);
                existsTrade.setTemplateId(templateId);
                existsTrade.setTemplateName(templateName);
                existsTrade.setLogisticsCompanyId(logisticsCompanyId);
            }
            progressCount++;
            if (progressCount % 500 == 0) {
                progressData.setCountCurrent(progressCount / 3);
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
            }
        }
        Map<String, Object> resultmap = new HashMap<>();
        resultmap.put("errorList", errorList);
        resultmap.put("updateTrades", updateTrades);
        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, progressData);
        return resultmap;
    }


    private ImportOutSidsContext buildImportOutSidsContext(Staff staff, String[][] data) {
        // 查询快递公司名称
        List<String> logisticsCompanyNames = new ArrayList<>(data.length);
        for (int i = 0; i < data.length; i++) {
            String[] d = data[i];
            logisticsCompanyNames.add(d[3]);
        }
        logisticsCompanyNames = logisticsCompanyNames.stream().distinct().map(String::trim).collect(Collectors.toList());
        List<UserLogisticsCompany> userLogisticsCompanies = userLogisticsCompanyBusiness.queryByNameLike(staff, logisticsCompanyNames, true);
        Map<String, UserLogisticsCompany> userLogisticsCompanyMap = userLogisticsCompanies.stream().collect(Collectors.toMap(UserLogisticsCompany::getName, o -> o, (o1, o2) -> o1));

        // 查询快递公司名称绑定的普通模板
        SearchLogisticsCompanyTemplateParams params = new SearchLogisticsCompanyTemplateParams();
        params.setLogisticsCompanyIds(userLogisticsCompanies.stream().map(UserLogisticsCompany::getId).collect(Collectors.toList()));
        params.setWlbTemplateType(WlbTemplateTypeEnum.COMMON.getValue());
        params.setEnableStatus(1);
        List<UserLogisticsCompanyTemplate> userLogisticsCompanyTemplates = userLogisticsCompanyTemplateBusiness.queryByParams(staff, params);
        Map<Long, UserLogisticsCompanyTemplate> userLogisticsCompanyTemplateMap = userLogisticsCompanyTemplates.stream().collect(Collectors.toMap(UserLogisticsCompanyTemplate::getLogisticsCompanyId, o -> o, (o1, o2) -> o1));


        Map<String, UserLogisticsCompanyTemplate> companyTemplateResultMap = new HashMap<>();
        Map<String, UserLogisticsCompany> logisticsCompanyResultMap = new HashMap<>();

        for (String name : logisticsCompanyNames) {
            // 先按照名称获取
            UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyMap.get(name);
            // 没有同名的快递公司，则模糊获取一个
            if (userLogisticsCompany == null) {
                for (String userLogisticsCompanyName : userLogisticsCompanyMap.keySet()) {
                    if (userLogisticsCompanyName.contains(name)) {
                        userLogisticsCompany = userLogisticsCompanyMap.get(userLogisticsCompanyName);
                        // 优先有电子面单设置的快递公司名称
                        if (userLogisticsCompanyTemplateMap.containsKey(userLogisticsCompany.getId())) {
                            break;
                        }
                    }
                }
            }
            if (userLogisticsCompany != null) {
                logisticsCompanyResultMap.put(name, userLogisticsCompany);
                if (userLogisticsCompanyTemplateMap.containsKey(userLogisticsCompany.getId())) {
                    companyTemplateResultMap.put(name, userLogisticsCompanyTemplateMap.get(userLogisticsCompany.getId()));
                }
            }
        }

        // 封装数据： logisticsCompanyName -> UserLogisticsCompany
        return ImportOutSidsContext.builder().userLogisticsCompanyMap(logisticsCompanyResultMap)
                .userLogisticsCompanyTemplateMap(companyTemplateResultMap).build();
    }

    /***
     * @DESCRIPTION: 老逻辑:map由<outsid,templatname>变为<outsid,templateId>
     * <AUTHOR>
     * @params: [staff, outsid2TemplateName]
     * @return: java.util.Map<java.lang.String, java.lang.Long>
     * @Date: 2021/5/28 10:50 上午
     * @Modified By:
     */
    /***
     * @DESCRIPTION:
     *                      运单号导入只使用与普通快递模板, 跨境模板的运单号是调用第三方生成的, 电子模板可能是通过菜鸟接口获取或者物流公司的接口获取
     * <AUTHOR>
     * @params: [staff, outsid2TemplateName]
     * @return: java.util.Map<java.lang.String, java.lang.Long>
     * @Date: 2021/5/27 7:41 下午
     * @Modified By:
     */
    private Map<String, UserExpressTemplate> getTemplateMap(Staff staff, String[][] data) {
        List<String> templateNames = new ArrayList<>(data.length);
        for (int i = 0; i < data.length; i++) {
            String[] d = data[i];
            templateNames.add(d[3]);
        }
        templateNames = templateNames.stream().distinct().map(String::trim).collect(Collectors.toList());

        Page page = new Page();
        page.setPageSize(10000);
        Map<String, UserExpressTemplate> map = new HashMap<String, UserExpressTemplate>();
        UserExpressTemplates userExpressTemplates = templateService.userList(staff, page);
        if (userExpressTemplates != null && CollectionUtils.isNotEmpty(userExpressTemplates.getList())) {
            Map<String, UserExpressTemplate> userExpressTemplatesMap = userExpressTemplates.getList().stream().collect(Collectors.toMap(UserExpressTemplate::getName, o -> o, (o1, o2) -> o1));

            for (String name : templateNames) {
                UserExpressTemplate userExpressTemplate = userExpressTemplatesMap.get(name);
                if (userExpressTemplate == null) {
                    for (String userExpressTemplateName : userExpressTemplatesMap.keySet()) {
                        if (userExpressTemplateName.contains(name)) {
                            userExpressTemplate = userExpressTemplatesMap.get(userExpressTemplateName);
                        }
                    }
                }
                if (userExpressTemplate != null) {
                    map.put(name, userExpressTemplate);
                }
            }
        }
        return map;
    }


    private Map<String, Trade> constructSid2TradeMapping(List<Trade> existsTrades) {
        return Maps.uniqueIndex(existsTrades, new Function<Trade, String>() {
            @Nullable
            @Override
            public String apply(@Nullable Trade trade) {
                return trade.getSid() + "";
            }
        });
    }

    private Multimap<String, Trade> constructTid2TradeMapping(List<Trade> existsTrades) {
        return Multimaps.index(existsTrades, new Function<Trade, String>() {
            @Nullable
            @Override
            public String apply(@Nullable Trade trade) {
                return trade.getTid();
            }
        });
    }


    class TradeImportResult {


        public int tradeSuccess;

        public int tradeError;

        public int orderSuccess;

        public int orderError;

        public Map<Integer, String/** rowId,错误信息**/> errorMsg = new HashMap<>();

        public Map<Trade, TradeTrace> updateLog = new HashMap<>();

        public Map<String, Map<Long, Integer /**oid,rowId列表 **/> /**tid,对应的order的oid,rowIdMap **/> tidOidRowsMap = new HashMap<>();

        public Map<String, Integer/**tid,tid信息对应的rowId **/> tidRowMap = new HashMap<>();

        public Map<Integer, Integer> oidRowToTidRowMap = new HashMap<>();

        public Map<String, Trade> tidTradeMap = new HashMap<>();

        public List<Trade> needRematchWarehouseTrades = new ArrayList<>();

        public Map<String, Trade> updateTidTradeMap = new HashMap<>();

        public Map<Long, DmsBaseDistributorInfoDto> dmsDistributorInfoMap = new HashMap<>();

        public void addTidOidRow(String tid, Long oid, int row) {
            Integer tradeRowId = tidRowMap.get(tid);
            Map<Long, Integer> oidRowMap = tidOidRowsMap.get(tid);
            if (oidRowMap == null) {
                oidRowMap = new HashMap<>();
                tidOidRowsMap.put(tid, oidRowMap);
            }
            oidRowMap.put(oid, row);
            oidRowToTidRowMap.put(row, tradeRowId);
        }

        public void addErrorMsg(int row, String msg) {
            if (StringUtils.isNotEmpty(errorMsg.get(row))) {
                if (msg != null && msg.length() > 0) {
                    errorMsg.put(row, errorMsg.get(row) + "," + msg);
                }
            } else {
                errorMsg.put(row, msg);
            }
        }

        public void addErrorMsgWithTidOid(String tid, Long oid, String msg) {
            Map<Long, Integer> oidRowsMap = tidOidRowsMap.get(tid);
            if (oidRowsMap != null) {
                for (long orderId : oidRowsMap.keySet()) {
                    if (orderId != oid.longValue()) {
                        addErrorMsg(oidRowsMap.get(orderId), "");
                    } else {
                        addErrorMsg(oidRowsMap.get(orderId), msg);
                    }
                }
            }
        }

        public void addErrorMsgWithTid(String tid, int row, String msg) {
            //这一句重复了，我先给他注释掉
            //addErrorMsg(row, msg);
            Map<Long, Integer> oidRowsMap = tidOidRowsMap.get(tid);
            if (oidRowsMap != null) {
                for (int rowId : oidRowsMap.values()) {
                    if (rowId != row) {
                        addErrorMsg(rowId, "");
                    }
                }
            }
            if (errorMsg.get(row) != null) {
                errorMsg.put(row, errorMsg.get(row) + "," + msg);
            } else {
                errorMsg.put(row, msg);
            }
        }

        public void addTrade(Trade trade) {
            tidTradeMap.put(trade.getTid(), trade);
            tradeSuccess++;
        }


        public void addMatchWarehouseTrade(Trade trade) {
            needRematchWarehouseTrades.add(trade);
        }


        public void addUpdateTrade(Trade trade) {
            updateTidTradeMap.put(trade.getTid(), trade);
        }

        public void removeTrade(String tid, String errorMsg) {
            Trade trade = tidTradeMap.remove(tid);
            if (trade != null) {
                tradeError++;
                tradeSuccess--;
            }
            int tidRowId = tidRowMap.get(tid);
            addErrorMsgWithTid(tid, tidRowId, errorMsg);
        }

        public void removeTrade(String tid, Long oid, String errorMsg) {

            Trade trade = tidTradeMap.remove(tid);
            if (trade != null) {
                tradeError++;
                tradeSuccess--;
            }
            addErrorMsgWithTidOid(tid, oid, errorMsg);
        }

        //获取需要插入的trade的tid
        public List<String> getTids() {
            List<String> tidList = new ArrayList<>();
            Set<String> tidSet = tidTradeMap.keySet();
            for (String tid : tidSet) {
                tidList.add(tid);
            }
            tidList.removeAll(updateTidTradeMap.keySet());
            return tidList;
        }

        public List<Trade> getTradeList() {
            List<Trade> list = new ArrayList<>();
            for (Trade trade : tidTradeMap.values()) {
                list.add(trade);
            }
            return list;
        }

        public List<Trade> getUpdateTradeList() {
            List<Trade> list = new ArrayList<>();
            for (Trade trade : updateTidTradeMap.values()) {
                list.add(trade);
            }
            return list;
        }

        public Map<String, Map<String, List<Order>>> getSysOutIdOrderMap() {
            List<Trade> list = getTradeList();
            Map<String, Map<String, List<Order>>> mapLists = new HashMap<>();


            Map<String, List<Order>> map = new HashMap<>();
            Map<String, List<Order>> skuMap = new HashMap<>();
            List<Order> allOrders = TradeUtils.getOrders4Trade(list);
            for (Order order : allOrders) {
                //规格
                String sysSkuPropertiesName = order.getSysSkuPropertiesName();
                StringBuffer sb = new StringBuffer();
                if (StringUtils.isNotBlank(order.getSysOuterId())) {
                    sb.append(order.getSysOuterId());

                    List<Order> orders = map.get(sb.toString());
                    if (orders == null) {
                        orders = new ArrayList<Order>();
                        map.put(sb.toString(), orders);
                    }
                    orders.add(order);
                } else {
                    //主商家编码
                    String outerId = order.getOuterId();
                    sb.append(outerId + OUTER_IID_SPLIT + sysSkuPropertiesName);
                    if(OUTER_IID_SPLIT.equals(sb.toString())){
                        logger.warn(String.format("订单号：%s商品信息为空，outerId:%s,sysSkuPropertiesName:%s,sysOuterId:%s", order.getTid(), outerId, sysSkuPropertiesName, order.getSysOuterId()));
                    }
                    List<Order> orders = skuMap.get(sb.toString());
                    if (orders == null) {
                        orders = new ArrayList<Order>();
                        skuMap.put(sb.toString(), orders);
                    }
                    orders.add(order);
                }
            }
            mapLists.put("sysOuterId", map);
            mapLists.put("skuOuterId", skuMap);
            return mapLists;
        }


        public int getRowNum(String tid, Long orderId) {
            Map<Long, Integer> oidRowMap = tidOidRowsMap.get(tid);
            if (oidRowMap == null) {
                return -1;
            } else {
                Integer rowNum = oidRowMap.get(orderId);
                if (rowNum == null) {
                    return -1;
                } else {
                    return rowNum;
                }
            }
        }

        public int getRowNum(String tid) {
            return tidRowMap.get(tid);
        }
    }

    private boolean checkUtf8mb4(Staff staff, String str) {
        try {
            boolean has = false;
            final int LAST_BMP = 0xFFFF;
            for (int i = 0; i < str.length(); i++) {
                if (str.codePointAt(i) >= LAST_BMP) {
                    String s = new StringBuilder().appendCodePoint(str.codePointAt(i)).toString();
                    logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s中,特殊字符为%s ", str, s)));
                    has = true;
                    break;
                }
            }
            return has;
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("检查特殊字符出错:%s", str)), e);
            return false;
        }
    }


    /**
     * 参考注释，校验数据是否正确，如，收货人必填，省市区必填，商家编码对应的商品是否存在等。
     * 如果校验通过则会放在 tradeImportResult.tidTradeMap集合中
     *
     * @param staff
     * @param user
     * @param data
     * @param errors
     * @return
     */
    private TradeImportResult assembly(Staff staff, User user, Integer needCover, String[][] data, List<String> errors, Map<String, Integer> titleMapToCol, boolean multiGoodsInSingleRow) {
        List<String> needRematchWarehouseTrades = new ArrayList<>();
        HashMap<Integer, Integer> oldIndexToNewIndex = new HashMap<>();
        String[] oldTitle = user == null ? TRADE_IMPORT_MULTI_TITLE : TRADE_IMPORT_SINGLE_TITLE;
        boolean isMultiUserImport = (user == null);
        for (int i = 0; i < oldTitle.length; i++) {
            oldIndexToNewIndex.put(i, titleMapToCol.get(oldTitle[i]));
        }

        TradeImportResult tradeImportResult = new TradeImportResult();
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
        Map<String, Long> warehouseMap = getWarehouseMap(staff);
        //默认仓库
        Warehouse warehouse = getDefaultWarehouse(staff);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        //是否开启订单自动读取价格的配置
        boolean openImportAutoSetPayment = tradeConfig != null && tradeConfig.openImportAutoSetPayment();
        //是否开启根据平台交易号判断是trade还是order的配置
        boolean openNewTradeImport = tradeConfig != null && tradeConfig.openNewTradeImport();
        Trade trade = null;
        Boolean isUpdata = false;
        String[] tradeData = null;
        boolean isOverSeaPlat = TradeSyncConfigUtils.isOverseaAddressSource(user);

        Map<String, AddressNode> addressNodeMap = erpAddressLibraryBusiness.queryAllUnderLevelAddress(staff, null, 3);

        //由于需要查库判断是否需要覆盖所以添加了批次
        int batch = data.length / 100 + (data.length % 100 == 0 ? 0 : 1);

        for (int j = 0; j < batch; j++) {
            int offset = j * 100;
            int end = offset + 100;
            if (end > data.length) {
                end = data.length;
            }
            // row_index , msg,trade,
            HashMap<Integer, Map.Entry<String, TbTrade>> search_table = new HashMap();
            if (needCover == 1) {
                //生成临时的校验搜索表
                search_table = generateSearchTable(staff, user, data, openNewTradeImport, offset, end, oldIndexToNewIndex, warehouse);
            }
            for (int i = offset; i < end; i++) {
                StringBuilder builder = new StringBuilder();
                Map.Entry<String, TbTrade> statusAndDbTrade = search_table.get(i);
                List<Order> paymentOrderList = null;
                if ((user != null && data[i].length < 18) || (user == null && data[i].length < 21)) {
                    builder.append("部分列未填写,");
                }
                //模版正确，校验开始
                else {
                    StringBuilder emptyTip = new StringBuilder();
                    //单店铺导入
                    if (user != null) {
                        boolean includeSku = (StringUtils.isBlank(getArrayValue(data[i], oldIndexToNewIndex.get(20))) || StringUtils.isBlank(getArrayValue(data[i], oldIndexToNewIndex.get(21))));
                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(10)]) && includeSku) {
                            emptyTip.append("(商家编码)、(主商家编码、规格)、");
                        }
                    } else {
                        boolean includeSku = (StringUtils.isBlank(getArrayValue(data[i], oldIndexToNewIndex.get(21))) || StringUtils.isBlank(getArrayValue(data[i], oldIndexToNewIndex.get(22))));
                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(10)]) && includeSku) {
                            emptyTip.append("(商家编码)、(主商家编码、规格)、");
                        }
                    }
                    boolean hasAddressParse = false;//没有通过详细地址解析
                    String rawAddress = data[i][oldIndexToNewIndex.get(8)];
                    try {
                        hasAddressParse = addressParse(data[i], oldIndexToNewIndex, builder);
                    } catch (Exception e) {
                        logger.error(String.format("解析详细地址异常，地址：%s", rawAddress), e);
                        builder.append("[详细地址(").append(rawAddress).append(")]格式不正确,");
                    }
                    checkAddress(data[i], oldIndexToNewIndex, builder, addressNodeMap, isOverSeaPlat, hasAddressParse, rawAddress);
                    //是trade还是order
                    boolean newTrade = isNewTrade(user, data, openNewTradeImport, i, oldIndexToNewIndex, warehouse, addressNodeMap, !isOverSeaPlat);
                    if (newTrade) {
                        if (trade != null) {//上一个trade完全ok
                            if (isUpdata) {//如果是覆盖导入，则需要缓存数据库的数据到更新时释放库存
                                tradeImportResult.addUpdateTrade(trade);
                                tradeImportResult.updateLog.put(trade, buildTradeTrace(staff, trade));
                            }
                            tradeImportResult.addTrade(trade);
                            trade = null;
                            isUpdata = false;
                        }
                        //收件人
                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(0)])) {
                            emptyTip.append("[收货人]、");
                        }

                        //仓库
                        String warehouseName = data[i][oldIndexToNewIndex.get(4)];
                        if (StringUtils.isBlank(warehouseName)) {
                            needRematchWarehouseTrades.add(data[i][oldIndexToNewIndex.get(17)]);
                        }
                        if (StringUtils.isBlank(warehouseName) && warehouse != null) {
                            warehouseName = data[i][oldIndexToNewIndex.get(4)] = warehouse.getName();
                        }
                        if (StringUtils.isEmpty(warehouseName)) {
                            emptyTip.append("[仓库名称]、");
                        }

                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(17)])) {
                            data[i][oldIndexToNewIndex.get(17)] = String.valueOf(idGenerator.nextId());
                        }
                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(17)])) {
                            emptyTip.append("[平台交易号]、");
                        }

//                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(17)])) {
//                            builder.append("[平台交易号]、");
//                        }
                        if (emptyTip.length() > 0) {
                            emptyTip.deleteCharAt(emptyTip.length() - 1);
                            emptyTip.append("为空, ");
                            builder.append(emptyTip);
                        }
                        Trade addressInfo = null;
//                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(5)]) && StringUtils.isBlank(data[i][oldIndexToNewIndex.get(6)]) && StringUtils.isBlank(data[i][oldIndexToNewIndex.get(7)])) {
//                            if (!StringUtils.isBlank(data[i][oldIndexToNewIndex.get(8)])) {
//                                try {
//                                    addressInfo = converAddress(data[i][oldIndexToNewIndex.get(8)].trim());
//                                } catch (Exception e) {
//                                    builder.append("[详细地址(").append(data[i][oldIndexToNewIndex.get(8)].trim()).append(")]格式不正确,");
//                                }
//                            }
//                        }
//                        if (addressInfo == null) {
//                            checkAddress(data[i], oldIndexToNewIndex, builder, addressNodeMap, isOverSeaPlat);
//                        }
                        if (statusAndDbTrade != null) {
                            //覆盖导入前置处理
                            isUpdata = overPreHandle(isUpdata, statusAndDbTrade, user, builder, data, i, oldIndexToNewIndex);
                            if (statusAndDbTrade.getKey() != SUCCESS) {
                                builder.append(statusAndDbTrade.getKey());
                            }
                        }
                        boolean isCustom = "是".equals(getArrayValue(data[i], oldIndexToNewIndex, 24, isMultiUserImport));
                        boolean isDangkou = "是".equals(getArrayValue(data[i], oldIndexToNewIndex, 25, isMultiUserImport));
                        boolean isCod = "是".equals(getArrayValue(data[i], oldIndexToNewIndex, 29, isMultiUserImport));
                        String sourceName = getArrayValue(data[i], oldIndexToNewIndex, 26, isMultiUserImport);
                        String fxPayType = getArrayValue(data[i], oldIndexToNewIndex, 27, isMultiUserImport);
                        String price = getArrayValue(data[i], oldIndexToNewIndex, 28, isMultiUserImport);

                        String selfBuiltDepositAmountStr = getArrayValue(data[i], oldIndexToNewIndex, 30, isMultiUserImport);
                        String selfBuiltPaymentReceivableStr = getArrayValue(data[i], oldIndexToNewIndex, 31, isMultiUserImport);

                        if ((isCustom && isDangkou) || (isCustom && isCod) || (isDangkou && isCod)) {
                            appendTypeTip(builder, isCustom, isDangkou, isCod);
                        } else if (isCustom && StringUtils.isBlank(getArrayValue(data[i], oldIndexToNewIndex.get(1)))) {
                            builder.append("客户订单[昵称（旺旺号）]不可为空,");
                        } else if (isDangkou && (StringUtils.isBlank(sourceName) || StringUtils.isBlank(fxPayType))) {
                            builder.append("档口订单[分销商名称]、[支付方式]不可为空,");
                        }
                        if(!multiGoodsInSingleRow) {//多行商品的 不这样校验
                            //商品单价如果不为空，判断是否为数字
                            if (StringUtils.isNotEmpty(price)) {
                                if (!Pattern.matches("^\\d*(\\.\\d+)?$", price)) {
                                    builder.append("[商品单价]必须为数字,");
                                }
                            }
                        }
                        if (StringUtils.isNotBlank(selfBuiltDepositAmountStr) && !Pattern.matches("^\\d*(\\.\\d+)?$", selfBuiltDepositAmountStr)) {
                            builder.append("[定金金额]必须为数字,");
                        }
                        if (StringUtils.isNotBlank(selfBuiltPaymentReceivableStr) && !Pattern.matches("^\\d*(\\.\\d+)?$", selfBuiltPaymentReceivableStr)) {
                            builder.append("[代收金额]必须为数字,");
                        }

                        User tempUser = user;
                        if (tempUser == null) {//多店铺导入情况
                            String userId = data[i][oldIndexToNewIndex.get(20)].trim();
                            boolean jumpValidate = false;
                            if (statusAndDbTrade != null && statusAndDbTrade.getValue() != null) {
                                userId = statusAndDbTrade.getValue().getUserId() + "";
                                jumpValidate = true;  //如果拿到了在数据库拿到了userid则跳过校验
                            }
                            if (!jumpValidate && (data[i][oldIndexToNewIndex.get(20)] == null || !data[i][oldIndexToNewIndex.get(20)].trim().matches("[1-9]\\d*"))) {
                                builder.append("[店铺编号]格式有误,");
                            } else {
                                User u = staff.getUserByUserId(Long.parseLong(userId));
                                if (u == null) {
                                    builder.append("[店铺编号(").append(userId).append(")]没有对应店铺或不在权限范围内,");
                                } else if (u.getActive() - CommonConstants.JUDGE_NO == 0) {
                                    builder.append("[店铺编号(").append(userId).append(")]对应店铺已停用,");
                                } else if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(u.getSource())) {
                                    builder.append("[店铺编号(").append(userId).append(")]奇门店铺不支持导入订单,");
                                } else {
                                    tempUser = u;
                                    if ("是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(25))) && !"dangkou".equals(tempUser.getSource())) {
                                        builder.append("非档口类型店铺不可导入档口订单,");
                                    }
                                }
                            }
                        } else {
                            if ("是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(24))) && !"dangkou".equals(tempUser.getSource())) {
                                builder.append("非档口类型店铺不可导入档口订单,");
                            }
                        }
                        if (warehouseName != null && !(warehouseName = warehouseName.trim()).isEmpty()) {
                            Long warehouseId = warehouseMap.get(warehouseName);
                            if (warehouseId == null) {
                                builder.append("[仓库名称(").append(warehouseName).append(")]找不到,");
                            }
                        }
                        if (StringUtils.isBlank(data[i][oldIndexToNewIndex.get(2)]) && StringUtils.isBlank(data[i][oldIndexToNewIndex.get(3)])) {
                            builder.append("[手机号码]与[固定电话]至少需要填一个,");
                        } else if(StringUtils.isNotBlank(data[i][oldIndexToNewIndex.get(2)]) && !TradeSyncConfigUtils.isOverseaAddressSource(tempUser)){
                            //过滤掉跨境的
                            String phone = data[i][oldIndexToNewIndex.get(2)];
                            phone = phone.replace(" ","");
                            data[i][oldIndexToNewIndex.get(2)] = phone;
                            if(!Pattern.matches("^\\d{11}((-\\d{4})|(\\[\\d{4}\\]))?$", phone)){
                                builder.append("[手机号码]格式不正确,");
                            }
                        }

                        if (StringUtils.isNotBlank(data[i][oldIndexToNewIndex.get(13)]) && !org.apache.commons.lang3.math.NumberUtils.isNumber(data[i][oldIndexToNewIndex.get(13)])) {
                            builder.append("[运费(").append(data[i][oldIndexToNewIndex.get(13)]).append(")]格式不正确,");
                        }
                        if (data[i].length > 19) {
                            try {
                                Date date = convertToDate(data[i][oldIndexToNewIndex.get(18)], null);
                                if (date != null && date.getTime() < 0) {
                                    builder.append("[下单时间(").append(data[i][oldIndexToNewIndex.get(18)]).append(")]不正确,");
                                }
                            } catch (Exception e) {
                                builder.append("[下单时间(").append(data[i][oldIndexToNewIndex.get(18)]).append(")]格式不正确,");
                            }
                        }
                        if (data[i].length >= 20) {
                            try {
                                Date date = convertToDate(data[i][oldIndexToNewIndex.get(19)], null);
                                if (date != null && date.getTime() < 0) {
                                    builder.append("[付款时间(").append(data[i][oldIndexToNewIndex.get(19)]).append(")]不正确,");
                                }
                            } catch (Exception e) {
                                builder.append("[付款时间(").append(data[i][oldIndexToNewIndex.get(19)]).append(")]格式不正确,");
                            }
                        }

                        String tid = removeAllBlank(data[i][oldIndexToNewIndex.get(17)]).trim();
                        if (tradeImportResult.tidTradeMap.get(tid) != null) {
                            builder.append("[平台交易号(").append(tid).append(")]重复,");
                        } else if (tid.length() > 100) {
                            builder.append("[平台交易号(").append(tid).append(")]超过100位,");
                        } else if (!Pattern.matches("[A-Za-z0-9_-]+", tid)) {
                            builder.append("[平台交易号(").append(tid).append(")]含有中文或特殊字符,");
                        } else if(StringUtils.isBlank(tid.replace("-","").replace("_", ""))){
                            builder.append("[平台交易号(").append(tid).append(")]不能全部都是下划线或中划线,");
                        } else if (tid.length() <= 0) {
                            builder.append("[平台交易号(").append(tid).append(")]为空,");
                        }
                        tradeData = new String[data[i].length];
                        //订单的昵称不进行特殊字符检查
                        for (int index = 0; index < data[i].length; index++) {
                            if (1 == index) {
                                tradeData[index] = "";
                                continue;
                            }
                            tradeData[index] = data[i][index];
                        }
                        //检查是否有特殊字符
                        String info = JSON.toJSONString(tradeData);
                        if (StringUtils.isNotBlank(info)) {
                            boolean hasUtf8mb4 = checkUtf8mb4(staff, info);
                            if (hasUtf8mb4) {
                                builder.append("该行数据存在生僻字或者表情符等特殊字体,请检查,");
                            }
                        }

                        if (builder.length() <= 0) {//这里认为订单级别的验证已经结束
                            // if (tempUser != null) {
                            trade = createTrade(staff, tempUser, data[i], addressInfo, oldIndexToNewIndex, isMultiUserImport);
                            if (statusAndDbTrade != null && statusAndDbTrade.getKey() == SUCCESS) {
                                trade.setSid(statusAndDbTrade.getValue().getSid());
                                trade.setOrigin(statusAndDbTrade.getValue());
                            }
                            //设置系统备注
                            if (null == user && data[i].length > 23) {
                                trade.setSysMemo(data[i][oldIndexToNewIndex.get(23)]);
                            } else if (null != user && data[i].length > 22) {
                                trade.setSysMemo(data[i][oldIndexToNewIndex.get(22)]);
                            }
                            //设置客户订单
                            if (null == user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(24)))) {
                                trade.setType(TradeConstants.TYPE_TRADE_CUSTOMER);
                            } else if (null != user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(23)))) {
                                trade.setType(TradeConstants.TYPE_TRADE_CUSTOMER);
                            }
                            //设置档口订单
                            if (null == user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(25)))) {
                                trade.setType("dangkou");
                                trade.setSourceName(getArrayValue(data[i], oldIndexToNewIndex.get(26)));
                                trade.setFxPayType(getArrayValue(data[i], oldIndexToNewIndex.get(27)));
                            } else if (null != user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(24)))) {
                                trade.setType("dangkou");
                                trade.setSourceName(getArrayValue(data[i], oldIndexToNewIndex.get(25)));
                                trade.setFxPayType(getArrayValue(data[i], oldIndexToNewIndex.get(26)));
                            }
                            //是否货到付款
                            if (null == user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(29)))) {
                                trade.setType("cod");
                            } else if (null != user && "是".equals(getArrayValue(data[i], oldIndexToNewIndex.get(28)))) {
                                trade.setType("cod");
                            }
                            //设置仓库
                            if (StringUtils.isNotBlank(warehouseName)) {
                                Long warehouseId = warehouseMap.get(warehouseName);
                                trade.setWarehouseId(warehouseId);
                                trade.setWarehouseName(warehouseName);
                            } else {
                                //设置默认仓库
                                if (warehouse != null) {
                                    trade.setWarehouseId(warehouse.getId());
                                    trade.setWarehouseName(warehouse.getName());
                                } else {
                                    builder.append("没有设置默认仓库,");
                                }
                            }
                            tradeImportResult.tidRowMap.put(tid, i);
                        } else {
                            tradeImportResult.tradeError++;
                        }
                    } else {//不是新增trade  记录日志 （工单：QfWavkrZQ58iE）
                        if (i > 0) {
                            logger.debug(LogHelper.buildLog(staff, String.format("第%s行，不是newTrade,上一行收货人及订单号（%s，%s），本行收货人及订单号（%s,%s）,本行其他数据:%s", i,
                                    data[i - 1][oldIndexToNewIndex.get(0)], data[i - 1][oldIndexToNewIndex.get(17)], data[i][oldIndexToNewIndex.get(0)], data[i][oldIndexToNewIndex.get(17)], JSONObject.toJSONString(data[i])
                            )));
                        }
                    }
                    String price = "";
                    if (user != null) {
                        price = getArrayValue(data[i], oldIndexToNewIndex.get(27));
                    } else {
                        price = getArrayValue(data[i], oldIndexToNewIndex.get(28));
                    }
                    //paymentOrderList = convertOrderInfo(staff, data[i][oldIndexToNewIndex.get(10)], data[i][oldIndexToNewIndex.get(11)], getArrayValue(data[i], oldIndexToNewIndex.get(12)));
                    if(multiGoodsInSingleRow) {
                        paymentOrderList = convertMultiOrderInfo(staff, data[i][oldIndexToNewIndex.get(10)],  getArrayValue(data[i], oldIndexToNewIndex.get(12)), price, builder);
                    }
                    if(!multiGoodsInSingleRow) {
                        if (!Pattern.matches("[1-9]\\d*", data[i][oldIndexToNewIndex.get(11)]) && paymentOrderList == null) {
                            builder.append("[数量]必须为正整数,");
                        }
                        if (!Pattern.matches("^\\d*(\\.\\d+)?$", getArrayValue(data[i], oldIndexToNewIndex.get(12))) && paymentOrderList == null) {
                            builder.append("[金额]必须为数字,");
                        }
                        //如果金额为空，那么必须开启了自动读取价格的配置
                        if (StringUtils.isEmpty(getArrayValue(data[i], oldIndexToNewIndex.get(12))) && paymentOrderList == null && !openImportAutoSetPayment) {
                            builder.append("[金额]必须为数字,");
                        }
                    }
                }

                if (trade != null) {
                    if (builder.length() <= 0) {
                        List<Order> orderList = createOrders(staff, trade, data[i], paymentOrderList, user, oldIndexToNewIndex);
                        for (Order order : orderList) {
                            tradeImportResult.addTidOidRow(trade.getTid(), order.getId(), i);
                        }
                    } else {
                        errors.add("第" + (i + 1) + "行 " + builder.deleteCharAt(builder.length() - 1));
                        tradeImportResult.addErrorMsgWithTid(trade.getTid(), i, builder.toString());
                        tradeImportResult.tradeError++;
                        trade = null;
                    }
                } else {
                    if (builder.length() > 0) {
                        errors.add("第" + (i + 1) + "行" + builder.deleteCharAt(builder.length() - 1));
                        tradeImportResult.addErrorMsg(i, builder.toString());
                    } else {
                        tradeImportResult.addErrorMsg(i, "");
                    }
                }

                // 是否重算仓库
                if (trade != null && needRematchWarehouseTrades.contains(trade.getTid())) {
                    tradeImportResult.addMatchWarehouseTrade(trade);
                }
            }

        }


        if (trade != null) {//上一个trade完全ok
            tradeImportResult.addTrade(trade);
            if (isUpdata) {
                tradeImportResult.addUpdateTrade(trade);
                tradeImportResult.updateLog.put(trade, buildTradeTrace(staff, trade));
            }
            trade = null;
        }

//
        //校验是否有tid已存在
        progressData.setCountAll(tradeImportResult.tradeError + tradeImportResult.tradeSuccess);
        progressData.setCountCurrent(tradeImportResult.tradeError);
        progressData.setErrorNum(tradeImportResult.tradeError + 0L);
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        verifyTids(staff, tradeImportResult.getTids(), tradeImportResult, errors);
//        List<Trade> list = tradeImportResult.getTradeList();
        Map<String, Map<String, List<Order>>> mapLists = tradeImportResult.getSysOutIdOrderMap();
        //验证商家编码对应的商品是否存在
        verifySysOuterIds(staff, mapLists, tradeImportResult, errors);
        //验证客户订单的nick是否存在
        verifyBuyerNick(staff, tradeImportResult, errors);
        //验证档口订单的分销商名称是否合法
        verifyDangkou(staff, tradeImportResult, errors);
        //当order的payment为空时，验证是否是客户订单，如果是客户订单，再去根据客户订单、商家编码去查客户销售价；验证是否是档口店铺的分销订单，如果是则根据分销规则去查分销销售价；否则设置商品档案销售价
        verifyOrderPayment(staff, tradeImportResult, errors);
        //若包含运费大于1000，或者商品付款金额大于100000的订单，导入失败
        limitPayment(staff, tradeImportResult, errors);
        tradeCostCalculateBusiness.matchCost(staff, tradeImportResult.getTradeList());
        //无昵称的填充手机号或收件人
        TradeUtils.fillDefaultBuyerNick(tradeImportResult.getTradeList());
        TradeUtils.fillDefaultBuyerNick(tradeImportResult.getUpdateTradeList());
        //处理订单昵称中的emoji表情
        handleBuyerNickEmoji(tradeImportResult);
        //处理奇门店铺订单增加分销属性
        handleQimenFxTrade(user, tradeImportResult);
        return tradeImportResult;
    }

    private void checkAddress(String[] data, Map<Integer, Integer> oldIndexToNewIndex, StringBuilder builder, Map<String, AddressNode> addressNodeMap, boolean isOverSeaPlat, boolean hasAddressParse, String sourceAddressDetail) {
        String province = StringUtils.stripToEmpty(data[oldIndexToNewIndex.get(5)]);
        String city = StringUtils.stripToEmpty(data[oldIndexToNewIndex.get(6)]);
        String district = StringUtils.stripToEmpty(data[oldIndexToNewIndex.get(7)]);
        String detail = data[oldIndexToNewIndex.get(8)];
        StringBuilder thisErr = new StringBuilder();
        boolean isError = false;
        if (StringUtils.isBlank(province)) {
//            thisErr.append("[省份]、");
//            isError = true;
            return;
        }
        if (StringUtils.isBlank(city)) {
//            thisErr.append("[城市]、");
//            isError = true;
            return;
        }

        if (StringUtils.isBlank(detail)) {
            builder.append("[详细地址]为空、");
//            isError = true;
            return;
        }
//        if (isError) {
//            builder.append(thisErr.substring(0, thisErr.length() - 1)).append("为空，");
//            return;
//        }
        if (detail.length() > 256) {
            builder.append("[详细地址]长度不能超过256，");
            return;
        }
        if (isOverSeaPlat) {
            return;
        }

        AddressNode districtNode = null;
        if(StringUtils.isBlank(district)){
            districtNode = getDistrictNodeByDetailAddress(addressNodeMap, province, city, detail, false);
        } else {
            districtNode = getDistrictNode(addressNodeMap, province, city, district, detail, sourceAddressDetail);
        }
        if (districtNode != null) {//能够拿到省市区
            data[oldIndexToNewIndex.get(7)] = districtNode.getName();
            data[oldIndexToNewIndex.get(6)] = districtNode.getParentNode().getName();
            data[oldIndexToNewIndex.get(5)] = districtNode.getParentNode().getParentNode().getName();
            return;
        }
        String errTip = "[该%s(%s)没有(%s)]，";
        AddressNode cityNode = addressNodeMap.get(getCheckAddressKey(province, city));
        if (cityNode != null) {
            if(StringUtils.isBlank(district)){// 区级地址不做校验
                return;
            }
            builder.append(String.format(errTip, "市", cityNode.getName(), district));
            return;
        }
        AddressNode proviceNode = addressNodeMap.get(getCheckAddressKey(province));
        if (proviceNode != null) {
            builder.append(String.format(errTip, "省", proviceNode.getName(), city));
            return;
        }
        builder.append("[没有该省(").append(province).append(")]，");

    }

    /**
     *
     * @param addressNodeMap
     * @param province
     * @param city
     * @param district 如果全称没找到  会通过别名查找
     * @return
     */
    private AddressNode getDistrictNode(Map<String, AddressNode> addressNodeMap, String province, String city, String district, String detail, String sourceAddressDetail){
        AddressNode districtNode = addressNodeMap.get(getCheckAddressKey(province, city)+ district);//先从省市简称+区级全称 查
        if(districtNode != null){
            return districtNode;
        }
        AddressNode cityNode = addressNodeMap.get(getCheckAddressKey(province, city));
        if (cityNode != null) {//通过别名获取区级node
            Map<String, AddressNode> aliasChildNodeMap = cityNode.getAliasChildNodeMap();
            if(aliasChildNodeMap != null && !aliasChildNodeMap.isEmpty()){
                Iterator<String> iterator = aliasChildNodeMap.keySet().iterator();
                while (iterator.hasNext()){
                    String k = iterator.next();
                    if(district.contains(k)){
                        return aliasChildNodeMap.get(k);

                    }
                };
            }
            //快递助手解析到的区县 不正确  如苏州工业园  变成了 工业园，  从原始信息地址去匹配；
            String newAddressDetail = sourceAddressDetail;
            String cityName = null;
            if(sourceAddressDetail.contains(cityNode.getName())){
                cityName = cityNode.getName();
            } else if(sourceAddressDetail.contains(city)){
                cityName = city;
            }
            if(StringUtils.isNotBlank(cityName)){
                newAddressDetail = sourceAddressDetail.substring(sourceAddressDetail.indexOf(cityName) + cityName.length()).trim();
            }
            districtNode = getDistrictNodeByDetailAddress(addressNodeMap, province, city, newAddressDetail, true);
            if(districtNode == null){
                logger.warn(String.format("区县级地址不正确，通过详细地址还是没匹配到区县地址, sourceAddressDetail:%s,city:%s,cityNode.getName:%s,newAddressDetail:%s", sourceAddressDetail,city,cityNode.getName(),newAddressDetail));
            } else {
                logger.debug(String.format("区县级地址不正确，通过详细地址匹配到区县地址, sourceAddressDetail:%s,city:%s,cityNode.getName:%s,newAddressDetail:%s,districtNode.getName:%s", sourceAddressDetail,city,cityNode.getName(),newAddressDetail, districtNode.getName()));
            }
            return districtNode;
        }
        return null;

    }

    /**
     *
     * @param addressNodeMap
     * @param province
     * @param city
     * @param detailAddress 通过城市级的别名  来匹配详细地址以区级关键字打头的
     * @return
     */
    private AddressNode getDistrictNodeByDetailAddress(Map<String, AddressNode> addressNodeMap, String province, String city, String detailAddress, boolean containFlag){
        if(StringUtils.isBlank(detailAddress)){
            return null;
        }
        detailAddress = detailAddress.trim();
        AddressNode cityNode = addressNodeMap.get(getCheckAddressKey(province, city));
        if (cityNode != null) {//通过别名获取区级node
            Map<String, AddressNode> aliasChildNodeMap = cityNode.getAliasChildNodeMap();
            if(aliasChildNodeMap != null && !aliasChildNodeMap.isEmpty()){
                detailAddress = detailAddress.replaceFirst(province, "").replaceFirst(city, "");
                Iterator<String> iterator = aliasChildNodeMap.keySet().iterator();
                while (iterator.hasNext()){
                    String k = iterator.next();
                    if(detailAddress.startsWith(k)){
                        return aliasChildNodeMap.get(k);
                    } else if(containFlag && detailAddress.contains(k)){
                        return aliasChildNodeMap.get(k);
                    }
                };
            }
        }
        return null;

    }

    private String getCheckAddressKey(String... args) {
        StringBuilder sb = new StringBuilder();
        for (String s : args) {
            sb.append(s.length() > 2 ? s.substring(0, 2) : s);
        }
        return sb.toString();
    }


    private void appendTypeTip(StringBuilder builder, boolean isCustom, boolean isDangkou, boolean isCod) {
        StringBuilder tip = new StringBuilder();
        tip.append(isCustom ? "客户订单、" : "");
        tip.append(isDangkou ? "档口订单、" : "");
        tip.append(isCod ? "货到付款订单、" : "");
        builder.append("不可同时为").append(tip.substring(0, tip.length() - 1)).append(",");
    }

    /**
     * 获取默认仓库
     *
     * @param staff
     * @return
     */
    private Warehouse getDefaultWarehouse(Staff staff) {
        Warehouse warehouse = warehouseService.queryWarehouseAllocateDefault(staff.getCompanyId());
        if (warehouse == null) {
            //没有设置默认分配的仓库时，取公司级别的默认仓库
            warehouse = warehouseService.queryDefault(staff.getCompanyId());
        }
        if (warehouse == null) {
            List<Warehouse> warehouseList = warehouseService.queryAll(staff.getCompanyId(), 1);
            if (!warehouseList.isEmpty()) {
                warehouse = warehouseList.get(0);
            }
        }
        return warehouse;
    }

    private TradeTrace buildTradeTrace(Staff staff, Trade trade) {
        if(trade.getOrigin() == null){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        Trade origin = trade.getOrigin();
        String originAddress = toAddressString(origin);
        String currAddress = toAddressString(trade);
        String originItem = toItemString(origin);
        String currItem = toItemString(trade);

        appendChangeLogIfDiff(sb, "仓库信息", origin.getWarehouseName(), trade.getWarehouseName());
        appendChangeLogIfDiff(sb, "收件人", originAddress, currAddress);
        appendChangeLogIfDiff(sb, "买家留言", origin.getBuyerMessage(), trade.getBuyerMessage());
        appendChangeLogIfDiff(sb, "卖家备注", origin.getSellerMemo(), trade.getSellerMemo());
        appendChangeLogIfDiff(sb, "商品信息", originItem, currItem);

        if(sb.length() > 0){
            String diffMsg = "覆盖导入变化信息：" + sb.toString();
            return createTradeTrace(staff.getCompanyId(), trade, staff.getName(), diffMsg);
        }
        return null;
    }

    private void appendChangeLogIfDiff(StringBuilder sb, String title, String text1, String text2){
        if(!Objects.equals(text1, text2)){
            sb.append(String.format("%s【 %s 】变更为【 %s 】；", title, text1, text2));
        }
    }

    String getTradeAndOrderMsg(Trade trade) {
        StringBuilder sb = new StringBuilder();
        if (null == trade) {
            return "";
        }
        sb.append("{")
                .append("仓库信息【").append(trade.getWarehouseName())
                .append("】收件人【").append(toAddressString(trade))
                .append("】买家留言【").append(trade.getBuyerMessage())
                .append("】卖家备注【").append(trade.getSellerMemo())
                .append("】商品信息【").append(toItemString(trade))
                .append("】}");
        return sb.toString();
    }

    private TradeTrace createTradeTrace(Long companyId, Trade trade, String operator, String content) {
        return TradeTraceUtils.createTradeTraceWithTrade(companyId, trade, "覆盖导入", operator, new Date(), content);
    }

    private String toItemString(Trade trade) {
        StringBuilder sb = new StringBuilder();
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        for (int i = 0; i < orders4Trade.size(); i++) {
            if (i != 0) sb.append(",");
            sb.append("(").append(StringUtils.trimToEmpty(orders4Trade.get(i).getSysOuterId()));
            if(StringUtils.isNotBlank(orders4Trade.get(i).getOuterId())) {
                sb.append("{").append(StringUtils.trimToEmpty(orders4Trade.get(i).getOuterId()));
                if (StringUtils.isNotBlank(orders4Trade.get(i).getSkuPropertiesName())) {
                    sb.append(" - ").append(orders4Trade.get(i).getSkuPropertiesName());
                }
                sb.append("}");
            }
            sb.append(" * ")
                .append(orders4Trade.get(i).getNum())
                .append(" : ")
                .append(StringUtils.trimToEmpty(orders4Trade.get(i).getPayment()))
                .append(")");
        }
        return sb.toString();
    }

    private String toAddressString(Trade trade) {
        StringBuilder sb = new StringBuilder();
        sb.append(StringUtils.trimToEmpty(trade.getReceiverState()))
                .append(StringUtils.trimToEmpty(trade.getReceiverCity()))
                .append(StringUtils.trimToEmpty(trade.getReceiverDistrict()));
        if(StringUtils.isNotBlank(trade.getReceiverAddress())) {
            sb.append(" ").append(StringUtils.trimToEmpty(trade.getReceiverAddress()));
        }
        if(StringUtils.isNotBlank(trade.getReceiverZip())) {
            sb.append(" ").append(StringUtils.trimToEmpty(trade.getReceiverZip()));
        }
        if(StringUtils.isNotBlank(trade.getReceiverName())) {
            sb.append(" ").append(StringUtils.trimToEmpty(trade.getReceiverName()));
        }
        if(StringUtils.isNotBlank(trade.getReceiverMobile())) {
            sb.append(StringUtils.trimToEmpty(trade.getReceiverMobile()));
        }
        return sb.toString();
    }

    private Boolean overPreHandle(Boolean isUpdata, Map.Entry<String, TbTrade> statusAndDbTrade, User user, StringBuilder builder, String[][] data, int i, Map<Integer, Integer> oldIndexToNewIndex) {
        if (statusAndDbTrade != null) {
            isUpdata = true;
            if (user != null && !(StringUtils.isBlank(data[i][oldIndexToNewIndex.get(1)]) &&
                    StringUtils.isBlank(data[i][oldIndexToNewIndex.get(23)]) &&
                    StringUtils.isBlank(data[i][oldIndexToNewIndex.get(24)]) &&
                    StringUtils.isBlank(data[i][oldIndexToNewIndex.get(25)]) &&
                    StringUtils.isBlank(data[i][oldIndexToNewIndex.get(26)]))) {
                builder.append("[昵称/是否客户订单/是否档口订单/分销商名称/支付方式，不允许覆盖导入],");
            }
            if (user == null && data[i].length >= 27) {  //编译器指令重排会引起ArrayIndexOutOfBoundsException
                if (!(StringUtils.isBlank(data[i][oldIndexToNewIndex.get(1)]) &&
                        StringUtils.isBlank(data[i][oldIndexToNewIndex.get(20)]) &&
                        StringUtils.isBlank(data[i][oldIndexToNewIndex.get(24)]) &&
                        StringUtils.isBlank(data[i][oldIndexToNewIndex.get(25)]) &&
                        StringUtils.isBlank(data[i][oldIndexToNewIndex.get(26)]) &&
                        StringUtils.isBlank(data[i][oldIndexToNewIndex.get(27)]))) {
                    builder.append("[昵称/店铺/是否客户订单/是否档口订单/分销商名称/支付方式，不允许覆盖导入],");
                }
            }
        } else {
            isUpdata = false;
        }
        return isUpdata;
    }

    private HashMap<Integer, Map.Entry<String, TbTrade>> generateSearchTable(Staff staff, User user, String[][] data, boolean openNewTradeImport, int offset, int end, Map<Integer, Integer> oldIndexToNewIndex, Warehouse warehouse) {
        HashMap<Integer, Map.Entry<String, TbTrade>> search_table;
        TreeMap<Integer, String> rowId_Tid = new TreeMap<>();
        for (int i = offset; i < end; i++) {
            if (isNewTrade(user, data, openNewTradeImport, i, oldIndexToNewIndex, warehouse, null, false)) {
                String tid;
                if ((tid = removeAllBlank(data[i][oldIndexToNewIndex.get(17)])) != "") {
                    rowId_Tid.put(i + 1, tid);
                }
            }
        }

        //从数据库中查到平台单
        List<TbTrade> db_tbTrades = tradeSearchService.queryByTids(staff, true, rowId_Tid.values().toArray(new String[rowId_Tid.values().size()]));

        //如果在交易库中没有查到，需要到报表库中再查一次
        HashSet<String> dataDbExistTid = new HashSet<>();
        Set<String> tids = getTradeDbNotFoundTids(db_tbTrades, rowId_Tid.values());
        if (CollectionUtils.isNotEmpty(tids)) {
//            TradeQueryParams queryParams = new TradeQueryParams();
//            queryParams.setTid(tids);
//            queryParams.setPage(new Page(Page.DEFAULT_PAGE_NUM, 100));
//            List<Trade> list = tradeDataService.queryTrades(staff, queryParams, false);
//            Set<String> needQueryDataDbTidSet = new HashSet<>(tids);
            //查归档订单
            List<String> search3MonthTids = tradeDataService.search3MonthColdDirectQueryByTids(staff, tids, false);
            if (!CollectionUtils.isEmpty(search3MonthTids)) {
                dataDbExistTid.addAll(search3MonthTids);
            }
        }

        ConcurrentHashMap<String, Map.Entry<Integer, TbTrade>> db_trade_status_table = new ConcurrentHashMap<>();

        // row_index , msg,trade,
        search_table = new HashMap();

        for (TbTrade db_tbTrade : db_tbTrades) {
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(db_tbTrade.getSource())) {//不是手工单
                db_trade_status_table.put(db_tbTrade.getTid(), new AbstractMap.SimpleEntry<Integer, TbTrade>(PALTFROM_TRADE, db_tbTrade));
                continue;
            }
            if (!db_tbTrade.getSysStatus().equals(SYS_STATUS_WAIT_AUDIT)) {//不是待审核
                db_trade_status_table.put(db_tbTrade.getTid(), new AbstractMap.SimpleEntry<Integer, TbTrade>(SYS_TRADE_NOT_WAIT_AUDIT, db_tbTrade));
                continue;
            }
            if (db_tbTrade.getSplitType() != TRADE_UNSPLIT_TYPE) {//已经拆分过的
                db_trade_status_table.put(db_tbTrade.getTid(), new AbstractMap.SimpleEntry<Integer, TbTrade>(SYS_TRADE_WAIT_AUDIT_SPLIT, db_tbTrade));
                continue;
            }
            if (db_tbTrade.getMergeType() != TRADE_UNMERGE_TYPE) {//已经合并过的
                db_trade_status_table.put(db_tbTrade.getTid(), new AbstractMap.SimpleEntry<Integer, TbTrade>(SYS_TRADE_WAIT_AUDIT_MERGE, db_tbTrade));
                continue;
            }
            db_trade_status_table.put(db_tbTrade.getTid(), new AbstractMap.SimpleEntry<Integer, TbTrade>(SYS_TRADE_WAIT_AUDIT, db_tbTrade));
        }

        for (Integer rowId : rowId_Tid.keySet()) {
            String tid = rowId_Tid.get(rowId);
            Map.Entry<Integer, TbTrade> point = db_trade_status_table.get(tid);
            if (point == null && dataDbExistTid.contains(tid)) {
                search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>("第" + rowId + "行，已存在该平台单号，导入失败！", null));
                continue;
            }
            if (point != null) {
                switch (point.getKey()) {
                    case PALTFROM_TRADE:
                        search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>("第" + rowId + "行，为平台订单，导入失败！", null));
                        break;
                    case SYS_TRADE_NOT_WAIT_AUDIT:
                        search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>("第" + rowId + "行，为(非待审核的)手工订单，导入失败！", null));
                        break;
                    case SYS_TRADE_WAIT_AUDIT_SPLIT:
                        search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>("第" + rowId + "行，为(拆分过的)手工订单，导入失败！", null));
                        break;
                    case SYS_TRADE_WAIT_AUDIT_MERGE:
                        search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>("第" + rowId + "行，为(合单过的)手工订单，导入失败！", null));
                        break;
                    case SYS_TRADE_WAIT_AUDIT:
                        search_table.put(rowId - 1, new AbstractMap.SimpleEntry<>(SUCCESS, point.getValue()));
                        break;
                }
            }
        }
        return search_table;
    }

    @Nullable
    private Set<String> getTradeDbNotFoundTids(List<TbTrade> db_tbTrades, Collection<String> values) {
        if (CollectionUtils.isEmpty(db_tbTrades)) {
            return Sets.newHashSet(values);
        }
        Set<String> tradeDbTids = db_tbTrades.stream().map(TbTrade::getTid).collect(Collectors.toSet());
        Set<String> excelTids = new HashSet<>(values);
        excelTids.removeAll(tradeDbTids);
        if (CollectionUtils.isEmpty(excelTids)) {
            return null;
        }
        return excelTids;
    }

    private boolean isNewTrade(User user, String[][] data, boolean openNewTradeImport, int i, Map<Integer, Integer> oldIndexToNewIndex, Warehouse warehouse, Map<String, AddressNode> addressNodeMap, boolean needResetAddress) {
        boolean newTrade = false;
        if (!openNewTradeImport) {
            if (i == 0) {
                newTrade = true;
            } else if(StringUtils.isNotBlank(data[i][oldIndexToNewIndex.get(0)])){//收件人不为空 则 新订单
                newTrade = true;
            } else if(StringUtils.isNotBlank(data[i][oldIndexToNewIndex.get(17)])){//订单号不为空  算新订单
                newTrade = true;
            }
        } else {
            if (i == 0) {
                newTrade = true;
                //多店铺导入时除了基本信息还要判断店铺编号是否相同
            } else if (user == null && !ObjectUtils.nullSafeEquals(getArrayValue(data[i], oldIndexToNewIndex.get(20)), getArrayValue(data[i - 1], oldIndexToNewIndex.get(20)))) {
                newTrade = true;
            } else {
//                for (int k = 0; k < 20; k++) {
//                    if (k == 10 || k == 11 || k == 12) {
//                        continue;
//                    }
//                    if (!ObjectUtils.nullSafeEquals(getArrayValue(data[i], oldIndexToNewIndex.get(k)), getArrayValue(data[i - 1], oldIndexToNewIndex.get(k)))) {
//                        newTrade = true;
//                        break;
//                    }
//                }

                // 多店铺  店铺不一致 情况  已经在上面返回了
                boolean isSameTid = isEqual(data, oldIndexToNewIndex, i, 17) //订单号
                        && isEqual(data, oldIndexToNewIndex, i, 4, warehouse != null ? warehouse.getName() : "")  //仓库是否相同
                        && isEqual(data, oldIndexToNewIndex, i, 0)  //收件人名称
                        && isEqualOr(data, oldIndexToNewIndex, i, 2, 3); // 电话号码 或 座机号
//                if (needResetAddress && isSameTid) {//只有需要重算  且前面条件相同才可能再重算   因为可能上一条地址不符合地址库 重算了
//                    StringBuilder builder = new StringBuilder();
//                    checkAddress(data[i], oldIndexToNewIndex, builder, addressNodeMap, false);//
//                }

                if (isSameTid) {
                    isSameTid = isEqualAnd(data, oldIndexToNewIndex, i, 5, 6, 7, 8);//省市区 详细地址
                    newTrade = !isSameTid;
                } else {
                    newTrade = true;
                }
            }
        }
        return newTrade;
    }

    private boolean isEqual(String[][] data, Map<Integer, Integer> map, int row, int column) {
        return ObjectUtils.nullSafeEquals(getArrayValue(data[row], map.get(column)), getArrayValue(data[row - 1], map.get(column)));
    }

    private boolean isEqual(String[][] data, Map<Integer, Integer> map, int row, int column, String defaultName) {
        String v1 = getArrayValue(data[row - 1], map.get(column));//上一行
        String v2 = getArrayValue(data[row], map.get(column));
        if (ObjectUtils.nullSafeEquals(v1, v2)) {
            return true;
        }
        if (StringUtils.isNotBlank(defaultName) && Objects.equals(v1, defaultName) && StringUtils.isBlank(v2)) {
            return true;
        }
        return false;
    }

    private boolean isEqualOr(String[][] data, Map<Integer, Integer> map, int row, int column1, int column2) {
        return isEqual(data, map, row, column1) || isEqual(data, map, row, column2);
    }

    private boolean isEqualAnd(String[][] data, Map<Integer, Integer> map, int row, int column1, int... column2Arr) {
        if (!isEqual(data, map, row, column1)) {
            return false;
        }
        for (int column : column2Arr) {
            if (!isEqual(data, map, row, column)) {
                return false;
            }
        }
        return true;
    }


    private void limitPayment(Staff staff, TradeImportResult tradeImportResult, List<String> errors) {
        //金额超过100000的商品
        List<Order> errorOrders = new ArrayList<>();
        //运费超过1000的订单号
        List<String> errorTids = new ArrayList<>();
        tradeImportResult.getTradeList().forEach(trade -> {
            if (Double.parseDouble(trade.getPostFee()) > 1000) {
                errorTids.add(trade.getTid());
            }
            TradeUtils.getOrders4Trade(trade).forEach(order -> {
                if (Double.parseDouble(order.getPayment()) > 1000000) {
                    errorOrders.add(order);
                }
            });
        });
        if (!errorOrders.isEmpty() || !errorTids.isEmpty()) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
            for (Order order : errorOrders) {
                tradeImportResult.removeTrade(order.getTid(), order.getId(), "商品付款金额大于1000000");
            }
            for (String tid : errorTids) {
                tradeImportResult.removeTrade(tid, "运费大于1000");
            }
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum((long) tradeImportResult.tradeError);
            errors.add("订单运费大于1000或商品付款金额大于1000000");
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }
    }

    private void verifyOrderPayment(Staff staff, TradeImportResult tradeImportResult, List<String> errors) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        //需要客户价格的订单
        Map<String, List<Order>> needCustomerPriceOrderMap = new HashMap<>();
        //需要分销价格的订单
        Map<Long, List<Trade>> needFxPriceTradeMap = new HashMap<>();
        //分销trade当且仅当分销商余额不足时会导入失败
        List<String> errorTids = new ArrayList<>();
        //分销order当分销规则不允许该分销商分销时会导入失败
        List<Order> errorOrders = new ArrayList<>();
        for (Trade trade : tradeImportResult.getTradeList()) {
            //档口订单单独处理。这里不能用convertType做判断，因为订单只是引用了分销价，来源属于某个分销商，但实际并不做分销订单处理，而是档口订单
            if (trade.getSourceId() != null) {
                Long sourceId = trade.getSourceId();
                needFxPriceTradeMap.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(trade);
                continue;
            }
            //没有开启配置的就不走客户订单的逻辑了
            if (tradeConfig == null || !tradeConfig.openImportAutoSetPayment()) {
                continue;
            }
            //其余订单再判断是否填写了价格
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                //如果价格为空
                if (StringUtils.isEmpty(order.getPayment())) {
                    //非客户订单的key值为null
                    String buyerNick = null;
                    if (trade.isCustomerTrade()) {
                        buyerNick = trade.getBuyerNick();
                    }
                    needCustomerPriceOrderMap.computeIfAbsent(buyerNick, k -> new ArrayList<>()).add(order);
                }
            }
        }
        //处理客户订单以及普通订单
        if (!needCustomerPriceOrderMap.isEmpty()) {
            //分用户分批验证
            for (Map.Entry<String, List<Order>> entry : needCustomerPriceOrderMap.entrySet()) {
                List<Order> needPriceOrder = entry.getValue();
                Long customerId = null;
                if (StringUtils.isNotEmpty(entry.getKey())) {
                    CmCustomerQuery cmCustomerQuery = new CmCustomerQuery();
                    cmCustomerQuery.setCmNick(entry.getKey());
                    List<CmCustomerVO> customers = cmCustomerDubboService.getSimpleCustomersByQuery(staff, cmCustomerQuery);
                    customerId = customers.get(0).getId();
                }
                int c = needPriceOrder.size() / 100 + (needPriceOrder.size() % 100 == 0 ? 0 : 1);
                for (int i = 0; i < c; i++) {
                    int end = (i + 1) * 100;
                    if (end > needPriceOrder.size()) {
                        end = needPriceOrder.size();
                    }
                    List<Order> list = new ArrayList<>(needPriceOrder.subList(i * 100, end));
                    QueryByItemIdInfoListResponse queryByItemIdInfoListResponse = queryDmjItemInfo(staff, list);
                    if (!queryByItemIdInfoListResponse.isSuccess()) {
                        logger.error(LogHelper.buildLog(staff, String.format("查询系统商品信息失败，忽略设置客户销售价，errorCode:%s，errorMsg:%s", queryByItemIdInfoListResponse.getErrorCode(), queryByItemIdInfoListResponse.getErrorMsg())));
                        // 下面有个payment初始化 不continue
                    }
                    List<DmjItemDto> dmjItemDtos = queryByItemIdInfoListResponse.getDmjItemList();

                    // 组装一个map来获取，替代原来的双循环
                    Map<String, DmjItemDto> itemSkuIdDtoMap = null;
                    if (CollectionUtils.isNotEmpty(dmjItemDtos)) {
                        itemSkuIdDtoMap = dmjItemDtos.stream().collect(Collectors.toMap(dmjItemDto -> (dmjItemDto.getSysItemId() + "_" + dmjItemDto.getSysSkuId()),
                                java.util.function.Function.identity(), (k1, k2) -> k2));
                    }
                    StringBuilder mapKey = new StringBuilder();
                    for (Order order : list) {
                        Double price = null;
                        if (Objects.nonNull(itemSkuIdDtoMap)) {
                            mapKey.append(order.getItemSysId()).append("_").append(order.getSkuSysId());
                            DmjItemDto dmjItemDto = itemSkuIdDtoMap.get(mapKey.toString());
                            if (Objects.nonNull(dmjItemDto)) {
                                // 商品档案销售价
                                price = dmjItemDto.getSellingPrice();
                                if (CollectionUtils.isNotEmpty(dmjItemDto.getItemCustomerBridgeDtoList())) {
                                    // 价格规则销售价
                                    List<ItemCustomerBridgeDto> itemCustomerBridgeDtoList = dmjItemDto.getItemCustomerBridgeDtoList();
                                    if (customerId != null) {
                                        ItemCustomerBridgeDto dtoByCustomerId = null;
                                        for (ItemCustomerBridgeDto customerBridgeDto : itemCustomerBridgeDtoList) {
                                            if (customerId.equals(customerBridgeDto.getCustomerId())) {
                                                dtoByCustomerId = customerBridgeDto;
                                                break;
                                            }
                                        }
                                        price = Objects.nonNull(dtoByCustomerId) ? dtoByCustomerId.getCmSellingPrice() : itemCustomerBridgeDtoList.get(0).getCmSellingPrice();
                                    }
                                }
                            }
                        }
                        order.setPayment(BigDecimal.valueOf(order.getNum()).multiply(BigDecimal.valueOf(price == null ? 0 : price)).toString());
                        mapKey.setLength(0);
                    }
                }
            }
        }
        //处理分销订单
        if (!needFxPriceTradeMap.isEmpty()) {
            //分用户分批处理
            for (Map.Entry<Long, List<Trade>> entry : needFxPriceTradeMap.entrySet()) {
                List<Trade> needPriceTrade = entry.getValue();
                int c = needPriceTrade.size() / 50 + (needPriceTrade.size() % 50 == 0 ? 0 : 1);
                for (int i = 0; i < c; i++) {
                    int end = (i + 1) * 50;
                    if (end > needPriceTrade.size()) {
                        end = needPriceTrade.size();
                    }
                    List<Trade> list = new ArrayList<>(needPriceTrade.subList(i * 50, end));
                    List<QueryDmsPriceOrderInfo> orderInfoList = buildQueryDmsPriceOrderInfo(list);
                    List<DmsOrderPriceDto> dmsOrderPriceDtos = Optional.ofNullable(dmsTradeService.queryDmsPrice(staff.getCompanyId(), entry.getKey(), orderInfoList)).orElse(new ArrayList<>());
                    Map<Long, DmsOrderPriceDto> oidToDmsMap = dmsOrderPriceDtos.stream().collect(Collectors.toMap(DmsOrderPriceDto::getOid, o -> o));
                    list.forEach(trade -> {
                        boolean flag = true;
                        Double total = 0.0;
                        for (Order order : TradeUtils.getOrders4Trade(trade)) {
                            DmsOrderPriceDto dto = null;
                            if ((dto = oidToDmsMap.get(order.getId())) != null && dto.isAllowSale()) {
                                //价格为空的才设置
                                if (StringUtils.isEmpty(order.getPayment())) {
                                    order.setPayment(BigDecimal.valueOf(order.getNum()).multiply(BigDecimal.valueOf(dto.getPrice() == null ? 0 : dto.getPrice())).toString());
                                }
                                total += Double.parseDouble(order.getPayment());
                            } else {
                                errorOrders.add(order);
                                flag = false;
                            }
                        }
                        //如果order都没问题且支付方式为余额支付，再判断trade余额是否满足条件
                        if (flag && trade.getFxPayType() != null && trade.getFxPayType().contains("余额")) {
                            DmsBaseDistributorInfoDto dmsDistributorInfoDto = tradeImportResult.dmsDistributorInfoMap.get(entry.getKey());
                            if (dmsDistributorInfoDto.getTotalBalance() >= total) {
                                dmsDistributorInfoDto.setTotalBalance(BigDecimal.valueOf(dmsDistributorInfoDto.getTotalBalance()).subtract(BigDecimal.valueOf(total)).doubleValue());
                            } else {
                                errorTids.add(trade.getTid());
                            }
                        }
                    });
                }
            }
        }
        //处理错误trade和order
        if (!errorOrders.isEmpty() || !errorTids.isEmpty()) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
            for (Order order : errorOrders) {
                tradeImportResult.removeTrade(order.getTid(), order.getId(), "根据分销规则该商品不允许分销");
            }
            for (String tid : errorTids) {
                tradeImportResult.removeTrade(tid, "分销商余额不足");
            }
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum((long) tradeImportResult.tradeError);
            errors.add("档口订单导入失败，商品不允许分销或分销商余额不足");
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }

    }

    private QueryByItemIdInfoListResponse queryDmjItemInfo(Staff staff, List<Order> orders) {
        List<ItemIdInfoDto> itemIdInfoDtoList = new ArrayList<>(orders.size());
        DmjItemNeedFilledInfo needFilledInfo = DmjItemNeedFilledInfo.builder().needCustomerSalePrice(true).build();
        orders.forEach(order -> {
            ItemIdInfoDto itemIdInfoDto = new ItemIdInfoDto();
            itemIdInfoDto.setSysItemId(order.getItemSysId());
            itemIdInfoDto.setSysSkuId(order.getSkuSysId());
            itemIdInfoDtoList.add(itemIdInfoDto);
        });
        StaffRequest staffRequest = StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build();
        QueryByItemIdInfoListRequest queryByItemIdInfoListRequest = new QueryByItemIdInfoListRequest();
        queryByItemIdInfoListRequest.setItemIdInfoList(itemIdInfoDtoList);
        queryByItemIdInfoListRequest.setStaffRequest(staffRequest);
        queryByItemIdInfoListRequest.setNeedFilledInfo(needFilledInfo);
        return dmjItemCommonSearchApi.queryByItemIdInfoList(queryByItemIdInfoListRequest);
    }

    private List<QueryDmsPriceOrderInfo> buildQueryDmsPriceOrderInfo(List<Trade> list) {
        List<QueryDmsPriceOrderInfo> query = new ArrayList<>();
        Date now = new Date();
        for (Trade trade : list) {
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                QueryDmsPriceOrderInfo info = new QueryDmsPriceOrderInfo();
                info.setCreateTime(order.getCreated() == null ? now : order.getCreated());
                info.setPayTime(order.getPayTime() == null ? now : order.getCreated());
                info.setOid(order.getId());
                //根据商家编码导入的和根据主商家编码导入的 其最小维度的商家编码被分别放在了两个字段
                info.setOuterId(StringUtils.isNotEmpty(order.getSysOuterId()) ? order.getSysOuterId() : order.getOuterSkuId());
                query.add(info);
            }
        }
        return query;
    }

    private void verifyBuyerNick(Staff staff, TradeImportResult tradeImportResult, List<String> errors) {
        List<Trade> tradeList = tradeImportResult.getTradeList()
                .stream().filter(t -> TradeConstants.TYPE_TRADE_CUSTOMER.equals(t.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        //有问题的tid
        List<String> errorTids = new ArrayList<>();
        //不存在的昵称
        List<String> errorNicks = new ArrayList<>();
        int c = tradeList.size() / 100 + (tradeList.size() % 100 == 0 ? 0 : 1);
        for (int i = 0; i < c; i++) {//分批验证,每批验证100条
            int end = (i + 1) * 100;
            if (end > tradeList.size()) {
                end = tradeList.size();
            }
            List<Trade> list = new ArrayList<>(tradeList.subList(i * 100, end));
            List<String> nicks = list.stream().map(Trade::getBuyerNick).distinct().collect(Collectors.toList());
            CmCustomerQuery cmCustomerQuery = new CmCustomerQuery();
            cmCustomerQuery.setCmNicks(nicks);
            List<CmCustomerVO> customers = Optional.ofNullable(cmCustomerDubboService.getSimpleCustomersByQuery(staff, cmCustomerQuery)).orElse(new ArrayList<>());
            List<String> existNicks = customers.stream().map(CmCustomerVO::getCmNick).collect(Collectors.toList());
            list.forEach(t -> {
                if (!existNicks.contains(t.getBuyerNick())) {
                    errorTids.add(t.getTid());
                    errorNicks.add(t.getBuyerNick());
                }
            });
        }
        if (!errorTids.isEmpty()) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
            for (String tid : errorTids) {
                tradeImportResult.removeTrade(tid, "未找到关联的客户资料");
            }
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum((long) tradeImportResult.tradeError);
            errors.add(String.format("用户昵称%s在系统中不存在", errorNicks));
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }
    }

    private void verifyDangkou(Staff staff, TradeImportResult tradeImportResult, List<String> errors) {
        List<Trade> tradeList = tradeImportResult.getTradeList()
                .stream().filter(t -> StringUtils.isNotEmpty(t.getSourceName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        //有问题的tid
        List<String> errorTids = new ArrayList<>();
        //不存在的分销商名称
        List<String> errorNames = new ArrayList<>();
        int c = tradeList.size() / 100 + (tradeList.size() % 100 == 0 ? 0 : 1);
        for (int i = 0; i < c; i++) {//分批验证,每批验证100条
            int end = (i + 1) * 100;
            if (end > tradeList.size()) {
                end = tradeList.size();
            }
            List<Trade> list = new ArrayList<>(tradeList.subList(i * 100, end));
            List<String> sourceNames = list.stream().map(Trade::getSourceName).distinct().collect(Collectors.toList());
            List<DmsBaseDistributorInfoDto> distributors = Optional.ofNullable(queryDmsDistributorList(staff, sourceNames)).orElse(new ArrayList<>());
            List<String> repeatNames = new ArrayList<>();
            Map<String, DmsBaseDistributorInfoDto> distributorMap = new HashMap<>();
            for (DmsBaseDistributorInfoDto distributor : distributors) {
                if (distributorMap.containsKey(distributor.getDistributorCompanyName())) {
                    repeatNames.add(distributor.getDistributorCompanyName());
                } else {
                    distributorMap.put(distributor.getDistributorCompanyName(), distributor);
                }
            }
            for (String repeatName : repeatNames) {
                distributorMap.remove(repeatName);
            }
            for (Trade trade : list) {
                if (distributorMap.containsKey(trade.getSourceName())) {
                    DmsBaseDistributorInfoDto dmsDistributorInfo = distributorMap.get(trade.getSourceName());
                    trade.setSourceId(dmsDistributorInfo.getDistributorCompanyId());
                    //把存在分销商存一份到tradeImportResult里，方便下一步验证分销商余额使用
                    tradeImportResult.dmsDistributorInfoMap.put(dmsDistributorInfo.getDistributorCompanyId(), dmsDistributorInfo);
                } else {
                    errorTids.add(trade.getTid());
                    errorNames.add(trade.getSourceName());
                }
            }
        }
        if (!errorTids.isEmpty()) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
            for (String tid : errorTids) {
                tradeImportResult.removeTrade(tid, "分销商名称不存在或不唯一");
            }
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum((long) tradeImportResult.tradeError);
            errors.add(String.format("分销商名称%s在系统中不存在或不唯一", errorNames));
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }
    }

    private List<DmsBaseDistributorInfoDto> queryDmsDistributorList(Staff staff, List<String> distributorNames) {
        List<DmsBaseDistributorInfoDto> result = new ArrayList<>();
        DmsQueryDistributorInfoRequest request = new DmsQueryDistributorInfoRequest();
        request.setSupplierCompanyId(staff.getCompanyId());
        request.setState(CooperateStateEnum.supplier_agree);
        request.setQueryKey("distributorName");
        request.setQueryValues(StringUtils.join(distributorNames, ","));
        request.setPage(new Page(1, distributorNames.size()));
        DmsDistributorInfoResponse response = dmsTradeService.queryDistributorInfo(request);
        if (response == null || !response.isSuccess()) {
            logger.error(LogHelper.buildLog(staff, "订单导入查询分销商数据失败,原因:" + (response == null ? "返回响应为空" : response.getErrorMsg())));
            return null;
        }
        if (response.getDmsBaseDistributorInfoDtoList() == null) {
            return null;
        }
        return response.getDmsBaseDistributorInfoDtoList().getList();
    }


    private String getArrayValue(String[] datum, int i) {
        if (datum != null && datum.length > i) {
            return StringUtils.trimToEmpty(datum[i]);
        }
        return null;
    }

    /**
     * 获取店铺编号 不能用该通用方法  单店铺会取不到
     * @param datum
     * @param oldIndexToNewIndex
     * @param i  以多店铺导入为维度
     * @param isMultiUserImport
     * @return
     */
    private String getArrayValue(String[] datum, Map<Integer, Integer> oldIndexToNewIndex, int i, boolean isMultiUserImport) {
        Assert.isTrue(i!=20, "非法调用，获取店铺编号异常");
        if(i>20){
            if(isMultiUserImport){
                return getArrayValue(datum, oldIndexToNewIndex.get(i));
            } else {//单店铺下标需要减1
                return getArrayValue(datum, oldIndexToNewIndex.get(i-1));
            }
        }
        //小于20的情况  单店铺和多店铺是同一下标
        return getArrayValue(datum, oldIndexToNewIndex.get(i));
    }


    private String exportErrorOrders(Staff staff, String[][] titleData, String[][] data, TradeImportResult tradeImportResult) {
        String[][] excelTitle = new String[1][];
        List<String> titleList = new ArrayList<>();
        for (String title : titleData[0]) {
            titleList.add(title);
        }
        titleList.add("错误信息");
        excelTitle[0] = new String[titleList.size()];
        titleList.toArray(excelTitle[0]);

        String[][] errorArray = new String[tradeImportResult.errorMsg.size()][];
//        System.out.println(JSONObject.toJSONString(tradeImportResult.errorMsg));
        int errorIndex = 0;
        for (int i = 0; i < data.length; i++) {
            if (tradeImportResult.errorMsg.get(i) != null) {
                errorArray[errorIndex] = new String[data[i].length + 1];
                for (int columnIndex = 0; columnIndex < errorArray[errorIndex].length - 1; columnIndex++) {
                    errorArray[errorIndex][columnIndex] = data[i][columnIndex];
                }
                errorArray[errorIndex][data[i].length] = tradeImportResult.errorMsg.get(i);
                errorIndex++;
            }
        }

        if (errorArray == null || errorArray.length == 0) {
            return null;
        }

        IDownloadCenterCallback callback = new IDownloadCenterCallback() {
            @Override
            public DownloadResult callback(DownloadParam downloadParam) throws Exception {
                DownloadResult result = new DownloadResult();
                result.setFlag(false);
                result.setData(errorArray);

                return result;
            }
        };
        String errorUrl = "";
        FileDownloadParam param = new FileDownloadParam();
        try {
            param.setTimeStampFileName("普通订单导入失败数据");
            param.setExcelTitle("普通订单导入失败数据");
            param.setTitleArr(excelTitle);
            param.setModule(EnumDownloadCenterModule.TRADE.getCode());
            staff.setShadowToken("shadowToken");

            DownloadResult downloadResult = downloadCenterFileService.exportExcel(staff, param, callback);
            if (downloadResult != null) {
                errorUrl = downloadResult.getFileUrl();
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("%s模块导出文件失败", param.getModule())), e);
        }
        return errorUrl;
    }

    private void verifyTids(Staff staff, List<String> tids, TradeImportResult tradeImportResult, List<String> errors) {
        int c = tids.size() / 100 + (tids.size() % 100 == 0 ? 0 : 1);
        Set<String> existTids = new HashSet<>();
        for (int i = 0; i < c; i++) {//分批验证,每批验证100条
            int end = (i + 1) * 100;
            if (end > tids.size()) {
                end = tids.size();
            }
            List<String> list = tids.subList(i * 100, end);
            Set<String> subTidSet = list.stream().collect(Collectors.toSet());
            List<TbTrade> trades = tbTradeDao.queryByTids(staff, list.toArray(new String[list.size()]));

            Set<String> tradeDbTids = trades.stream().map(TbTrade::getTid).collect(Collectors.toSet());
            List<String> needQueryDataDbTids = new ArrayList<>();
            for (String tid : list) {
                if (tradeDbTids.contains(tid)) {
                    existTids.add(tid);
                } else {
                    needQueryDataDbTids.add(tid);
                }
            }
            TradeQueryParams tradeQueryParams = new TradeQueryParams();
            tradeQueryParams.setTid(needQueryDataDbTids.toArray(new String[]{}));
            if(CollectionUtils.isEmpty(needQueryDataDbTids)) {
                continue;
            }
            Set<String> needQueryDataDbTidSet = new HashSet<>(needQueryDataDbTids);
            //查归档订单
            List<String> search3MonthTids = tradeDataService.search3MonthColdDirectQueryByTids(staff, needQueryDataDbTidSet, false);
            if (!CollectionUtils.isEmpty(search3MonthTids)) {
                for (String tid : search3MonthTids) {//检查3个月前订单是否导入
                    if (subTidSet.contains(tid)) {
                        existTids.add(tid);
                    }
                }
            }

        }
        if (!existTids.isEmpty()) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
            for (String existTid : existTids) {
                tradeImportResult.removeTrade(existTid, "平台交易号在系统中已存在");
            }
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum(tradeImportResult.tradeError + 0L);
            errors.add(String.format("平台交易号%s在系统中已存在", existTids));
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }
    }

    void verifySysOuterIds(Staff staff, Map<String, Map<String, List<Order>>> mapLists, TradeImportResult tradeImportResult, List<String> errorMsg) {
        List<String> result = new ArrayList<String>();
        List<String> errors = new ArrayList<String>();
        List<String> inactiveErrors = new ArrayList<String>();
        Map<String, List<Order>> sysOuterIdMap = mapLists.get("sysOuterId");
        Map<String, List<Order>> skuOuterIdMap = mapLists.get("skuOuterId");

        Map<String, List<Order>> map = new HashMap<>();
        if (sysOuterIdMap != null && sysOuterIdMap.size() > 0) {
            fillItems(staff, sysOuterIdMap, result, errors, inactiveErrors, false);
            map.putAll(sysOuterIdMap);
        }
        if (skuOuterIdMap != null && skuOuterIdMap.size() > 0) {
            fillItems(staff, skuOuterIdMap, result, errors, inactiveErrors, true);
            map.putAll(skuOuterIdMap);
        }

        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT);
        if (!errors.isEmpty()) {
            for (String error : errors) {
                List<Order> orderList = map.get(error);
                if (orderList != null) {
                    for (Order order : orderList) {
//                        int rowNum = tradeImportResult.getRowNum(order.getTid(), order.getId());
//                        errorMsgs.add("第"+(rowNum)+"行"+String.format("商家编码%s对应的商品下存在规格", error));
                        tradeImportResult.removeTrade(order.getTid(), order.getId(), String.format("商家编码%s对应的商品下存在规格", error));
                    }
                }
            }
            errorMsg.add(String.format("商家编码%s对应的商品下存在规格", errors));
        }
        if (!inactiveErrors.isEmpty()) {
            for (String error : inactiveErrors) {
                List<Order> orderList = map.get(error);
                if (orderList != null) {
                    for (Order order : orderList) {
                        tradeImportResult.removeTrade(order.getTid(), order.getId(), String.format("商家编码%s对应的商品已停用", error));
                    }
                }
            }
            errorMsg.add(String.format("商家编码%s对应的商品已停用", inactiveErrors));
        }
        if (!result.isEmpty()) {

            for (String error : result) {
                List<Order> orderList = map.get(error);
                if (orderList != null) {
                    for (Order order : orderList) {
                        tradeImportResult.removeTrade(order.getTid(), order.getId(), String.format("商家编码%s没有对应的商品(规格)", error));
                    }
                }
            }
            errorMsg.add(String.format("商家编码%s没有对应的商品(规格)", result));
        }
        if (!errors.isEmpty() || !inactiveErrors.isEmpty() || !result.isEmpty()) {
            progressData.setCountCurrent(tradeImportResult.tradeError);
            progressData.setErrorNum(tradeImportResult.tradeError + 0L);
            progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, progressData);
        }

    }

    private void fillItems(Staff staff, Map<String, List<Order>> map, List<String> result, List<String> errors, List<String> inactiveErrors, boolean isSkuImport) {

        List<String> sysOuterIds = new ArrayList<String>(map.keySet());
        int c = sysOuterIds.size() / 100 + (sysOuterIds.size() % 100 == 0 ? 0 : 1);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (int i = 0; i < c; i++) {
            int end = (i + 1) * 100;
            if (end > sysOuterIds.size()) {
                end = sysOuterIds.size();
            }
            List<String> subList = sysOuterIds.subList(i * 100, end);
            Map<String, Object> caseSensitiveMap = new HashMap<>();

            if (isSkuImport) {
                caseSensitiveMap = buildOutIdSkuMap(staff, subList);
            } else {
                caseSensitiveMap = itemServiceWrapper.queryItemSkuByOuterId(staff, subList, 12);
            }

            Map<String, Object> tmpMap = new CaseInsensitiveMap(caseSensitiveMap);
            for (String sysOuterId : sysOuterIds.subList(i * 100, end)) {
                //sysOuterId 有可能是商家编码，有可能是主商家编码_skuid
                Object object = tmpMap.get(sysOuterId);
                if (object == null) {
                    result.add(sysOuterId);
                    continue;
                }
                List<Long> sysItemIds = new ArrayList<Long>();
                List<Order> orders = map.get(sysOuterId);
                if (DmjSku.class.isInstance(object)) {
                    DmjSku sku = (DmjSku) object;
                    sysItemIds.add(sku.getSysItemId());
                    if (sku.getActiveStatus().equals(0)) {
                        inactiveErrors.add(sysOuterId);
                        continue;
                    }
                    for (Order order : orders) {
                        order.setSkuSysId(sku.getSysSkuId());
                        order.setItemSysId(sku.getSysItemId());
                        order.setSkuPropertiesName(sku.getPropertiesName());
                        order.setSysSkuPropertiesName(sku.getPropertiesName());
                        order.setSysSkuPropertiesAlias(sku.getPropertiesAlias());
                        order.setOuterSkuId(sku.getOuterId());
                        order.setSysTitle(sku.getTitle());
                        //当商品档案中 商品有维护 销售价的同时，订单导入时也维护了 商品单价。此时，优先取订单导入的商品单价
                        String importPrice = order.getPrice();
                        order.setPrice(StringUtils.isNotEmpty(importPrice) ? importPrice : (sku.getPriceOutput() != null ? String.valueOf(sku.getPriceOutput()) : "0"));
                        order.setSysPicPath(StringUtils.isNotBlank(sku.getSkuPicPath()) ? sku.getSkuPicPath() : sku.getPicPath());
                        order.setTitle(sku.getTitle());
                        order.setShortTitle(sku.getShortTitle());
                        order.setSysSkuRemark(sku.getSkuRemark());
                        order.setNetWeight(MathUtils.scaleUp(sku.getWeight(), 4));
                        order.setCost(sku.getPriceImport());
                        order.setVolume(sku.getVolume() == null ? 0.0 : sku.getVolume());
                        order.setSkuUnit(sku.getUnit());
                        order.setIsVirtual(sku.getIsVirtual());
                        TradeStockUtils.needNotApplyStock(staff, order, tradeConfig);
                        order.setCids(sku.getSellerCids());
                        setSuiteOrders(staff, sku, order, sku.getSuiteSingleList());
                    }
                } else if (DmjItem.class.isInstance(object)) {
                    DmjItem item = (DmjItem) object;
                    sysItemIds.add(item.getSysItemId());
                    if (item.getSkus() != null && !item.getSkus().isEmpty()) {
                        errors.add(sysOuterId);
                        continue;
                    }
                    if (item.getActiveStatus().equals(0)) {
                        inactiveErrors.add(sysOuterId);
                        continue;
                    }
                    for (Order order : orders) {
                        order.setSkuSysId(-1L);
                        order.setItemSysId(item.getSysItemId());
                        order.setSysTitle(item.getTitle());
                        //当商品档案中 商品有维护 销售价的同时，订单导入时也维护了 商品单价。此时，优先取订单导入的商品单价
                        String importPrice = order.getPrice();
                        order.setPrice(StringUtils.isNotEmpty(importPrice) ? importPrice : (item.getPriceOutput() != null ? String.valueOf(item.getPriceOutput()) : "0"));
                        order.setSysPicPath(item.getPicPath());
                        order.setTitle(item.getTitle());
                        order.setShortTitle(item.getShortTitle());
                        order.setSysItemRemark(item.getRemark());
                        order.setNetWeight(MathUtils.scaleUp(item.getWeight(), 4));
                        order.setCost(item.getPriceImport());
                        order.setVolume(item.getVolume() == null ? 0.0 : item.getVolume());
                        order.setUnit(item.getUnit());
                        order.setIsVirtual(item.getIsVirtual());
                        TradeStockUtils.needNotApplyStock(staff, order, tradeConfig);
                        order.setCids(item.getSellerCids());
                        setSuiteOrders(staff, item, order, item.getSuiteSingleList());
                    }
                }
                if (CollectionUtils.isNotEmpty(orders)) {
//                    setOrderSaleFee(staff, orders, sysItemIds);
                    modifyParentBusiness.setOrderSaleFee(staff, orders);
                }
            }
        }
    }

    private Map<String, Object> buildOutIdSkuMap(Staff staff, List<String> sysOuterSkuIdList) {
        List<String> sysOuterSkuIds = Lists.newArrayListWithCapacity(sysOuterSkuIdList.size());
        sysOuterSkuIdList.forEach(s -> {
            String[] outerIdArr = s.split(OUTER_IID_SPLIT);
            if(outerIdArr != null && outerIdArr.length>1){
                sysOuterSkuIds.add(outerIdArr[0]);
            }
        });
        //key 主商家编码  vale 规格商家编码
        Map<String, List<String>> outerIdSkuOuterIdListMap = itemServiceDubbo.getOuterIdSkuOuterIdListMap(staff.getCompanyId(), sysOuterSkuIds);

        HashMap<String, Object> objectObjectHashMap = Maps.newHashMapWithExpectedSize(outerIdSkuOuterIdListMap.size());
        for (Map.Entry<String, List<String>> entry : outerIdSkuOuterIdListMap.entrySet()) {
            //规格ids
            List<String> value = entry.getValue();
            String key = entry.getKey();
            //查询规格 sku商家编码 sku
            Map<String, Object> stringObjectMap = itemServiceWrapper.queryItemSkuByOuterId(staff, value, 2);
            for (Map.Entry<String, Object> entry1 : stringObjectMap.entrySet()) {
                DmjSku dmjSku = (DmjSku) entry1.getValue();
                objectObjectHashMap.put(key + OUTER_IID_SPLIT + dmjSku.getPropertiesName(), dmjSku);
            }
        }
        return objectObjectHashMap;
    }

    public void _insertTrades(final Staff staff, final List<Trade> trades, final List<Long> sids, TradeConfig tradeConfig, Integer isGenerateSaleTrade) throws Exception {
        lockService.locks(TradeLockBusiness.trades2ERPLocks(staff, trades), () -> {
            //匹配完赠品再去申请库存。
            orderStockService.applyTradeStockLocal(staff, trades);
            calculate(staff, trades, tradeConfig);
            //插入交易记录
            tradeUpdateService.insertTrades(staff, trades, TradeUtils.getOrders4Trade(trades));
            //  tradeExceptAdapter.saveExcept(staff,trades, ExceptSignWayEnum.AUTO.getAutoSign());
            return trades;
        });
        // 导入后新增分销属性
        Lists.partition(trades, 100).forEach(trades1 -> {
            try {
                fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(), trades1, 3, 1);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
            }
        });
        //非档口订单生成支付单
        List<Trade> notDangkous = trades.stream().filter(e -> !e.isDangkouTrade()).collect(Collectors.toList());
        tradePayBusiness.tradePayAddForExcel(staff, notDangkous, TradePaySource.SOURCE_CUSTOM);
        tradeTraceBusiness.asyncTrace(staff, trades, OpEnum.TRADE_ADD);
        //档口订单需要新增收银记录（销货单）
        if (isGenerateSaleTrade != null && isGenerateSaleTrade == 1) {
            List<Trade> dangkous = trades.stream().filter(Trade::isDangkouTrade).collect(Collectors.toList());
            saleTradeV2Business.createByTrade(staff, dangkous, 1);
        }
        //同步供应商
        orderSupplierService.sync(staff, TradeUtils.getOrders4Trade(trades), null);
        //同步业务员信息
        trades.forEach(trade -> trade.setSalesmanId((trade.getSalesmanId() == null || trade.getSalesmanId() <= 0) ? staff.getId() : trade.getSalesmanId()));
        tradeSalesmanService.saveTradeSalesman(staff, TradeSalesmanUtils.buildSalesmanList(staff, trades, null));
        //保存发票信息
        addTradeInvoice(staff, trades);
    }

    private void addTradeInvoice(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            List<TradeInvoice> invoices = new ArrayList<>();
            for (Trade trade : trades) {
                TradeInvoice invoice = TradeInvoiceUtils.trade2Invoice(trade);
                if (invoice != null) {
                    invoices.add(invoice);
                }
            }
            tradeInvoiceService.addTradeInvoices(staff, invoices);
        }
    }

    private void _updateTrade(final Staff staff, final List<Trade> trades, TradeConfig tradeConfig, Integer isGenerateSaleTrade) {
        lockService.locks(TradeLockBusiness.trades2ERPLocks(staff, trades), () -> {
            List<Order> allDbOrder = TradeUtils.getOrders4Trade(trades.stream().map(Trade::getOrigin).collect(Collectors.toList()));
            orderStockService.resumeOrderStockLocal(staff, allDbOrder, null);
            tbOrderDAO.deleteByIds(staff, allDbOrder);

            trades.stream().filter(trade -> null != trade.getOrigin() && trade.getOrigin().getIsCancel() == 1).forEach(trade ->
                    OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(trade), false).forEach(order -> order.setIsCancel(1)));

            List<Order> allNewOrder = TradeUtils.getOrders4Trade(trades);
            //原有订单未作废的才去申请库存
            List<Trade> noCancelTrades = trades.stream().filter(r -> filter4Stock(r)).collect(Collectors.toList());
            orderStockService.applyOrderStockLocal(staff, TradeUtils.getOrders4Trade(noCancelTrades));
            tbOrderDAO.batchInsert(staff, allNewOrder);
            calculate(staff, trades, tradeConfig);
            //插入交易记录
            tradeUpdateService.updateTrades(staff, trades, false);
            return trades;
        });
        // 导入后新增分销属性
        Lists.partition(trades, 100).forEach(trades1 -> {
            try {
                fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(), trades1, 3, 1);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
            }
        });
        //档口订单需要生成或修改收银记录
        List<Trade> dkTradeList = trades.stream().filter(e -> e.getOrigin() != null && e.getOrigin().isDangkouTrade()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dkTradeList)) {
            isGenerateSaleTrade = isGenerateSaleTrade == null ? 2 : isGenerateSaleTrade;
            int c = dkTradeList.size() / 100 + (dkTradeList.size() % 100 == 0 ? 0 : 1);
            for (int i = 0; i < c; i++) {//分批处理,每批处理100条
                int end = (i + 1) * 100;
                if (end > dkTradeList.size()) {
                    end = dkTradeList.size();
                }
                List<Trade> list = new ArrayList<>(dkTradeList.subList(i * 100, end));
                List<String> sourceNames = list.stream().map(Trade::getSourceName).distinct().collect(Collectors.toList());
                List<DmsBaseDistributorInfoDto> distributors = Optional.ofNullable(queryDmsDistributorList(staff, sourceNames)).orElse(new ArrayList<>());
                Map<Long, DmsBaseDistributorInfoDto> distributorMap = distributors.stream().collect(Collectors.toMap(DmsBaseDistributorInfoDto::getDistributorCompanyId, o -> o, (v1, v2) -> v1));
                list.forEach(e -> {
                    e.setBuyerNick(e.getOrigin().getBuyerNick());
                    e.setFxPayType(e.getOrigin().getFxPayType());
                    e.setSourceId(e.getOrigin().getSourceId());
                    DmsBaseDistributorInfoDto distributorInfoDto = distributorMap.get(e.getSourceId());
                    e.setSourceName(distributorInfoDto == null ? StringUtils.EMPTY : distributorInfoDto.getDistributorCompanyName());
                    e.setType(e.getOrigin().getType());
                });
            }
            saleTradeV2Business.createByTrade(staff, dkTradeList, isGenerateSaleTrade);
        }
    }

    private boolean filter4Stock(Trade trade) {
        //再次覆盖导入 分销订单，不要锁定库存
        if (trade.getOrigin() != null && TradeUtils.isFxOrMixTrade(trade.getOrigin())) {
            logger.debug("再次覆盖导入 分销订单，不要锁定库存 HasOrigin:" + trade.getOrigin() != null + " tid:" + trade.getTid());
            return false;
        }
        //原有订单未作废的才去申请库存
        return trade.getOrigin() != null && trade.getOrigin().getIsCancel() != 1;
    }


    void calculate(Staff staff, List<Trade> trades, TradeConfig tradeConfig) {
        for (Trade trade : trades) {
            TradeIOUtils.fillTradeExcep(staff, trade);
            trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
            trade.setVolume(TradeUtils.calculateVolume(trade));
            trade.setCost(TradeUtils.calculateCost(trade));
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
            TradeUtils.resetTradeItemNum(trade, tradeConfig);
        }
    }

    private Trade createTrade(Staff staff, User user, String[] row, Trade addressInfo, Map<Integer, Integer> oldIndexToNewIndex, boolean isMultiUserImport) {
        Trade trade = new TbTrade();
        trade.setCompanyId(staff.getCompanyId());
        trade.setSource(CommonConstants.PLAT_FORM_TYPE_SYS);
        trade.setTaobaoId(user.getTaobaoId());
        trade.setUserId(user.getId());
        //modify by baiyun KMERP-36837 start
        trade.setReceiverName(row[oldIndexToNewIndex.get(0)].replaceAll(" ", "").replaceAll("　", ""));
        if (StringUtils.isNotEmpty(row[oldIndexToNewIndex.get(1)])) {
            trade.setBuyerNick(row[oldIndexToNewIndex.get(1)].replaceAll(" ", "").replaceAll("　", ""));
        } else {
            trade.setBuyerNick(row[oldIndexToNewIndex.get(1)]);
        }
        if (StringUtils.isNotEmpty(row[oldIndexToNewIndex.get(2)])) {
            trade.setReceiverMobile(row[oldIndexToNewIndex.get(2)].replaceAll(" ", "").replaceAll("　", ""));
        } else {
            trade.setReceiverMobile(row[oldIndexToNewIndex.get(2)]);
        }
        if (StringUtils.isNotEmpty(row[oldIndexToNewIndex.get(3)])) {
            trade.setReceiverPhone(row[oldIndexToNewIndex.get(3)].replaceAll(" ", "").replaceAll("　", ""));
        } else {
            trade.setReceiverPhone(row[oldIndexToNewIndex.get(3)]);
        }

        if (addressInfo != null) {
            trade.setReceiverState(addressInfo.getReceiverState());
            trade.setReceiverCity(addressInfo.getReceiverCity());
            trade.setReceiverDistrict(addressInfo.getReceiverDistrict());
            trade.setReceiverAddress(addressInfo.getReceiverAddress());
        } else {
            trade.setReceiverState(row[oldIndexToNewIndex.get(5)].replaceAll(" ", "").replaceAll("　", ""));
            trade.setReceiverCity(row[oldIndexToNewIndex.get(6)].replaceAll(" ", "").replaceAll("　", ""));
            trade.setReceiverDistrict(row[oldIndexToNewIndex.get(7)].replaceAll(" ", "").replaceAll("　", ""));
            trade.setReceiverAddress(row[oldIndexToNewIndex.get(8)].replaceAll(" ", "").replaceAll("　", ""));
        }
        //modify by baiyun KMERP-36837 end
        trade.setReceiverZip(row[oldIndexToNewIndex.get(9)]);
        trade.setPostFee(StringUtils.isBlank(row[oldIndexToNewIndex.get(13)]) || row[oldIndexToNewIndex.get(13)].trim().length() == 0 ? "0" : row[oldIndexToNewIndex.get(13)]);
        trade.setInvoiceName(row[oldIndexToNewIndex.get(14)]);
        trade.setBuyerMessage(row[oldIndexToNewIndex.get(15)]);
        trade.setSellerMemo(row[oldIndexToNewIndex.get(16)]);
        trade.setTid(removeAllBlank(row[oldIndexToNewIndex.get(17)]));
        if (row.length > 19) {
            trade.setCreated(convertToDate(row[oldIndexToNewIndex.get(18)], null));
        }
        if (row.length >= 20) {
            trade.setPayTime(convertToDate(row[oldIndexToNewIndex.get(19)], null));
        }
        String selfBuiltDepositAmountStr = "";//定金
        String selfBuiltPaymentReceivableStr = "";//代收金额

        if (!isMultiUserImport) {//单店铺
            selfBuiltDepositAmountStr = getArrayValue(row, oldIndexToNewIndex.get(29));
            selfBuiltPaymentReceivableStr = getArrayValue(row, oldIndexToNewIndex.get(30));
        } else {//多店铺
            selfBuiltDepositAmountStr = getArrayValue(row, oldIndexToNewIndex.get(30));
            selfBuiltPaymentReceivableStr = getArrayValue(row, oldIndexToNewIndex.get(31));
        }

        if(StringUtils.isNotBlank(selfBuiltDepositAmountStr) || StringUtils.isNotBlank(selfBuiltPaymentReceivableStr)){
            TradeExt tradeExt = getOrInitTradeExt(trade);
            if(StringUtils.isNotBlank(selfBuiltDepositAmountStr)){
                TradeExtUtils.setExtraFieldValue(tradeExt, "selfBuiltDepositAmount", String.format("%.3f", Double.parseDouble(selfBuiltDepositAmountStr)));
            }
            if(StringUtils.isNotBlank(selfBuiltPaymentReceivableStr)){
                TradeExtUtils.setExtraFieldValue(tradeExt, "selfBuiltPaymentReceivable", String.format("%.3f", Double.parseDouble(selfBuiltPaymentReceivableStr)));
            }
        }

        TradeUtils.setMobileTail(trade);
        return trade;
    }

    private TradeExt getOrInitTradeExt(Trade trade){
        TradeExt tradeExt = trade.getTradeExt();
        if(tradeExt != null){
            return tradeExt;
        }
        tradeExt = new TradeExt();
        trade.setTradeExt(tradeExt);
        tradeExt.setUserId(trade.getUserId());
        tradeExt.setTid(trade.getTid());
        tradeExt.setCompanyId(trade.getCompanyId());
        return tradeExt;
    }


    /**
     * 得到仓库map
     */
    private Map<String, Long> getWarehouseMap(Staff staff) {
        List<Warehouse> warehouseList = warehouseService.queryAll(staff.getCompanyId(), null);
        Map<String, Long> warehouseMap = new HashMap<String, Long>();
        if (warehouseList != null) {
            for (Warehouse warehouse : warehouseList) {
                warehouseMap.put(warehouse.getName(), warehouse.getId());
            }
        }
        return warehouseMap;
    }

    private List<Order> createOrders(Staff staff, Trade trade, String[] row, List<Order> orderList, User user, Map<Integer, Integer> oldIndexToNewIndex) {
        List<Order> result = new ArrayList<>();
        if (orderList != null && orderList.size() > 0) {
            for (Order order : orderList) {
                result.add(createOrder(staff, trade, row, order, user, oldIndexToNewIndex));
            }
        } else {
            result.add(createOrder(staff, trade, row, null, user, oldIndexToNewIndex));
        }
        List<Order> oriOrders = TradeUtils.getOrders4Trade(trade);
        if (oriOrders == null) {
            oriOrders = new ArrayList<>();
        }
        oriOrders.addAll(result);
        TradeUtils.setOrders(trade, oriOrders);
        return result;
    }

    private Order createOrder(Staff staff, Trade trade, String[] row, Order paymentOrder, User user, Map<Integer, Integer> oldIndexToNewIndex) {
        Order order = new TbOrder();
        order.setCompanyId(staff.getCompanyId());
        String price = "";
        if (user != null) {
            price = getArrayValue(row, oldIndexToNewIndex.get(27));
        } else {
            price = getArrayValue(row, oldIndexToNewIndex.get(28));
        }
        if (paymentOrder != null) {
            order.setSysOuterId(paymentOrder.getSysOuterId());
            order.setNum(paymentOrder.getNum());
            order.setPayment(paymentOrder.getPayment());
            price = paymentOrder.getPrice();//多商品的  用自身
        } else {
            order.setSysOuterId(getArrayValue(row, oldIndexToNewIndex.get(10)));
            order.setNum(Integer.parseInt(row[oldIndexToNewIndex.get(11)]));
            String payment = row[oldIndexToNewIndex.get(12)];
            if (StringUtils.isNotBlank(payment)) {
                order.setPayment(payment);
            } else {
                order.setPayment("0");
            }
        }

        if (StringUtils.isNotBlank(price)) {
            order.setPrice(new BigDecimal(price).setScale(6, RoundingMode.HALF_UP).toString());
        } else {
            order.setPrice(price);
        }
        //单店铺导入主商家编码，规格，商品单价
        if (user != null) {
            order.setOuterId(getArrayValue(row, oldIndexToNewIndex.get(20)));
            order.setSysSkuPropertiesName(getArrayValue(row, oldIndexToNewIndex.get(21)));
        } else {
            order.setOuterId(getArrayValue(row, oldIndexToNewIndex.get(21)).trim());
            order.setSysSkuPropertiesName(getArrayValue(row, oldIndexToNewIndex.get(22)));

        }

        order.setSid(trade.getSid());
        order.setTid(trade.getTid());
        order.setUserId(trade.getUserId());
        order.setSource(CommonConstants.PLAT_FORM_TYPE_SYS);
        order.setWarehouseId(trade.getWarehouseId());
        order.setId(idGenerator.nextId());

        return order;
    }

    /**
     * 插入前的初始化操作
     */
    private List<Long> initTrade4Import(List<Trade> trades, Staff staff, boolean isupdate) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeItemContext tradeItemContext = new TradeItemContext().setTradeConfig(tradeConfig);
        List<Long> sids = new ArrayList<Long>(trades.size() + 1);
        Date now = new Date();
        for (Trade trade : trades) {
            if (!isupdate) {
                trade.setSid(idGenerator.nextId());
            }
            sids.add(trade.getSid());
            trade.setSysStatus(SYS_STATUS_WAIT_AUDIT);
            trade.setMergeType(TradeMergeEnum.MERGE_NORMAL.getDbType());
            trade.setMergeSid(-1L);
            trade.setSplitType(TradeSplitEnum.SPLIT_NORMAL.getDbType());
            trade.setSplitSid(-1L);
            if (trade.getCreated() == null) {
                trade.setCreated(now);
            }
            if (trade.getPayTime() == null) {
                trade.setPayTime(now);
            }
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            BigDecimalWrapper payment = new BigDecimalWrapper();
            BigDecimalWrapper totalFee = new BigDecimalWrapper();
            for (Order order : orders) {
                fillOrderInfo(staff, order, trade);
                String orderTotalFee = "0";
                try {
                    orderTotalFee =MathUtils.toString(MathUtils.multiply(order.getPrice(),order.getNum())) ;
                } catch (RuntimeException e) {
                    LogHelper.buildLog(staff, "子订单总费用计算异常：price:" + order.getPrice() + ",num:" + order.getNum());
                }
                payment.add(order.getPayment());
                totalFee.add(orderTotalFee);
                order.setTotalFee(orderTotalFee);
                order.setDiscountFee(MathUtils.toString(MathUtils.subtract(orderTotalFee,order.getPayment())));
                if (order.getSuits() != null) {
                    for (Order son : order.getSuits()) {
                        fillOrderInfo(staff, son, trade);
                        if (son.isGroup() || son.isProcess()) {
                            son.setStockNum(son.getNum());
                            // son.setStockStatus(Trade.STOCK_STATUS_NORMAL);
                            OrderExceptUtils.setStockStatus(staff, son, Trade.STOCK_STATUS_NORMAL);
                        }
                        son.setCombineId(order.getId());
                    }
                }
                //套件,组合,加工订单需要做金额分摊
                if ((order.isSuit(false) || order.isProcess(false) || order.isGroup(false))
                        && NumberUtils.str2Double(order.getPayment()) > 0
                        && CollectionUtils.isNotEmpty(order.getSuits())) {
                    tradeSuitCalculateBusiness.calculate(staff, order, order.getSuits(), tradeItemContext, false);
                }
            }
            trade.setPayment(payment.add(trade.getPostFee()).getString());
            trade.setTotalFee(totalFee.getString());
            trade.setDiscountFee("0");
            trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
            trade.setCost(TradeUtils.calculateCost(trade));
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
            TradeUtils.resetAddressType(trade);
            TradeUtils.resetTradeItemNum(trade, tradeConfig);
            TradeUserConfig tradeUserConfig = tradeUserConfigService.getByUser(staff, staff.getUserByUserId(trade.getUserId()));
            TradeUtils.setTimeoutActionTime(trade, tradeConfig, tradeUserConfig);
        }
        //订单新增 计算初始化订单实付金额
        payAmountCalculateBusiness.handleInsertTrades(staff, trades);
        tradeSysProductTimeoutActionTimeService.setTradeTimeoutActionTimeFromProduct(staff, trades);
        return sids;
    }

    private void fillOrderInfo(Staff staff, Order order, Trade trade) {
        // order.setId(idGenerator.nextId());
        order.setOid(order.getId());
        order.setSid(trade.getSid());
        order.setUserId(trade.getUserId());
        order.setTaobaoId(trade.getTaobaoId());
        order.setSysStatus(trade.getSysStatus());
        // order.setStockStatus(Trade.STOCK_STATUS_EMPTY);
        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
        order.setStockNum(0);
        order.setSource(trade.getSource());
        order.setCreated(trade.getCreated());
        order.setPayTime(trade.getPayTime());
    }

    private void recodeOpLog(IOpLogService opLogService, Staff staff, String action, String key, String content, String args) {
        OpLog opLog = new OpLog();
        opLog.setCompanyId(staff.getCompanyId());
        opLog.setStaffId(staff.getId());
        opLog.setAccountName(staff.getAccountName());
        opLog.setStaffName(staff.getName());
        opLog.setIp("");
        opLog.setDomain(Domain.PRINT);
        opLog.setAction(action);
        opLog.setKey(key);
        opLog.setContent(content);
        opLog.setArgs(args);
        opLog.setCreated(new Date());

        opLogService.record(staff, opLog);
    }

    private void setSuiteOrders(Staff staff, DmjItem item, Order parent, List<SuiteSingle> suiteSingles) {
        if (suiteSingles == null || suiteSingles.isEmpty()) {
            return;
        }
        parent.setType(ModifyParentBusiness.getType(item));
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (SuiteSingle suiteSingle : suiteSingles) {
            Order son = orderCopier.copy(parent, new TbOrder());
            son.setEnableStatus(1);
            son.setItemSysId(suiteSingle.getSubItemId());
            son.setSkuSysId(suiteSingle.getSubSkuId() == null || suiteSingle.getSubSkuId() <= 0 ? -1L : suiteSingle.getSubSkuId());
            son.setNum(parent.getNum() * suiteSingle.getRatio());
            son.setSysTitle(suiteSingle.getTitle());
            son.setShortTitle(suiteSingle.getShortTitle());
            son.setSysOuterId(suiteSingle.getSkuOuterId() != null ? suiteSingle.getSkuOuterId() : suiteSingle.getOuterId());
            son.setOuterSkuId(suiteSingle.getSkuOuterId());
            son.setSysItemRemark(suiteSingle.getRemark());
            son.setSysSkuRemark(suiteSingle.getSkuRemark());
            son.setSysPicPath(suiteSingle.getPicPath());
            son.setSysSkuPropertiesName(suiteSingle.getPropertiesName());
            son.setSysSkuPropertiesAlias(suiteSingle.getPropertiesAlias());
            son.setPrice(suiteSingle.getPriceOutput() != null ? suiteSingle.getPriceOutput().toString() : "");
            son.setCost(son.getSkuSysId() <= 0 ? suiteSingle.getPriceImport() : suiteSingle.getSkuPriceImport());
            son.setNetWeight(MathUtils.scaleUp(suiteSingle.getWeight(), 4));
            son.setUnit(suiteSingle.getUnit());
            son.setSkuUnit(suiteSingle.getSkuUnit());
            son.setIsVirtual(suiteSingle.getIsVirtual());
            son.setCids(suiteSingle.getSellerCids());
            son.setScalping(parent.getScalping());
            son.setId(idGenerator.nextId());
            TradeStockUtils.needNotApplyStock(staff, son, tradeConfig);
            if (parent.getSuits() == null) {
                parent.setSuits(new ArrayList<Order>(3));
            }
            // sku字段
            if (suiteSingle.getSubSkuId() != null && suiteSingle.getSubSkuId() > 0) {
                son.setSysSkuPropertiesName(suiteSingle.getPropertiesName());
                son.setSysSkuPropertiesAlias(suiteSingle.getPropertiesAlias());
                son.setSysOuterId(suiteSingle.getSkuOuterId());
                son.setSysSkuRemark(suiteSingle.getSkuRemark());
                son.setVolume(suiteSingle.getSkuVolume());
                son.setCost(suiteSingle.getSkuPriceImport());
                son.setNetWeight(MathUtils.scaleUp(suiteSingle.getSkuWeight(), 4));

                if (StringUtils.isNotEmpty(suiteSingle.getSkuPicPath()) && !suiteSingle.getSkuPicPath().equals(StockConstants.PATH_NO_PIC)) {
                    son.setSysPicPath(suiteSingle.getSkuPicPath());
                }
            }
            parent.getSuits().add(son);
        }
        modifyParentBusiness.setOrderSaleFee(staff, parent.getSuits());
    }


    private void setOrderSaleFee(Staff staff, List<Order> orders, List<Long> sysItemIds) {
        List<ItemSalePriceBrige> itemSalePriceBriges = itemServiceDubbo.itemSalePriceBrigelist(staff, sysItemIds, new ArrayList<Long>());
        if (CollectionUtils.isEmpty(itemSalePriceBriges)) {
            return;
        }
        Map<String, ItemSalePriceBrige> brigeMap = new HashMap<String, ItemSalePriceBrige>();
        for (ItemSalePriceBrige brige : itemSalePriceBriges) {
            String key = new StringBuilder().append(brige.getSysItemId()).append(brige.getSysSkuId() != null && brige.getSysSkuId() > 0 ? brige.getSysSkuId() + "" : "")
                    .append(brige.getUserId()).toString();
            brigeMap.put(key, brige);
        }
        for (Order order : orders) {
            String key = new StringBuilder().append(order.getItemSysId()).append(order.getSkuSysId() != null && order.getSkuSysId() > 0 ? order.getSkuSysId() + "" : "")
                    .append(order.getUserId()).toString();
            ItemSalePriceBrige itemSalePriceBrige = brigeMap.get(key);
            if (itemSalePriceBrige != null) {
                order.setSalePrice(String.valueOf(itemSalePriceBrige.getSalePriceFromLadderPrice(order.getNum())));
                order.setSaleFee(new BigDecimalWrapper(order.getSalePrice()).multiply(order.getNum()).getString());
            }
        }
    }

    public static Trade converAddress(String rawAddress) {
        ParseAddress parseAddress = AddressParseUtil.getParseSimpleAddress(1L, rawAddress);
        if (parseAddress == null) {
            return null;
        }
        Trade trade = new TbTrade();
        trade.setReceiverState(parseAddress.getAddress().getProvince());
        trade.setReceiverCity(parseAddress.getAddress().getCity());
        trade.setReceiverDistrict(parseAddress.getAddress().getDistrict());
        trade.setReceiverAddress(parseAddress.getAddress().getDetail());
        return trade;
    }

    public boolean addressParse(String[] data, Map<Integer, Integer> indexMap, StringBuilder builder) {
        String rawAddress = data[indexMap.get(8)];
        if(StringUtils.isBlank(rawAddress)){
            return false;
        }
        rawAddress = rawAddress.trim().replaceAll("\\:|：", " ");//快递助手接口暂时不会把：号当成分隔符
        //手机号或者座机号为空
        boolean phoneEmpty = StringUtils.isBlank(data[indexMap.get(2)]) && StringUtils.isBlank(data[indexMap.get(3)]);
        //收件人名称或者省、市、区为空
        boolean hasEmpty = phoneEmpty ||StringUtils.isBlank(data[indexMap.get(0)]) || StringUtils.isBlank(data[indexMap.get(5)]) || StringUtils.isBlank(data[indexMap.get(6)]) || StringUtils.isBlank(data[indexMap.get(7)]);
        if(!hasEmpty){
            return false;
        }
        ParseAddress parseAddress = AddressParseUtil.getParseAddress(1L, rawAddress);
        if (parseAddress == null) {
            builder.append("地址解析失败、");
            return false;
        }

        replaceValIfNull(parseAddress.getPhone(), data, indexMap, 2);
        replaceValIfNull(parseAddress.getTel(), data, indexMap, 3);
//        Trade trade = new TbTrade();
        ParseAddress.AddressDetail address = parseAddress.getAddress();
        String newAddressDetail = null;
        if(address != null){

            replaceValIfNull(address.getProvince(), data, indexMap, 5);
            replaceValIfNull(address.getCity(), data, indexMap, 6);
            replaceValIfNull(address.getDistrict(), data, indexMap, 7);
//            replaceValIfNull(address.getDetail(), data, indexMap, 8);
            String detailPreText = address.getDistrict();//详细地址前文本
            ParseAddress.Guess guess = parseAddress.getGuess();
            if(guess != null) {
                //某些情况未解析处理 GuessD 为null  其他类似
                if (guess.getGuessD() != null && !guess.getGuessD()){
                    detailPreText = address.getDistrict();
                } else if(guess.getGuessC() != null && !guess.getGuessC()){
                    detailPreText = address.getCity();
                } else if(guess.getGuesssP() != null && !guess.getGuesssP()){
                    detailPreText = address.getProvince();
                }
            }
            if (StringUtils.isNotBlank(detailPreText) && rawAddress.contains(detailPreText)) {//根据区级地址 拿详细地址
                newAddressDetail = rawAddress.substring(rawAddress.indexOf(detailPreText) + detailPreText.length());
                if(StringUtils.isNotBlank(newAddressDetail) ) {
                    newAddressDetail = newAddressDetail.trim();
                    data[indexMap.get(8)] = newAddressDetail;//替换调
                }
            }

        }

        String personName = fixPersonName(parseAddress, data[indexMap.get(0)], rawAddress, newAddressDetail);
        replaceValIfNull(personName, data, indexMap, 0);
        return true;
    }

    /**
     * 产品已跟快递助手提需求  优化 目前 名称可能会包含 详细地址
     * 用下面这方法做兜底
     * @param parseAddress
     * @param sourcePersonName
     * @param rawAddress
     * @return
     */
    public String fixPersonName(ParseAddress parseAddress, String sourcePersonName, String rawAddress, String newAddressDetail){
        String personName = parseAddress.getName();

        if (StringUtils.isBlank(personName) || StringUtils.isNotBlank(sourcePersonName)) {//原始数据有值  就不去判断了
            return personName;
        }
        personName = personName.trim().replaceAll("\\(|\\)|\\[|\\]|（|）|【|】", "");

        ParseAddress.AddressDetail address = parseAddress.getAddress();
        if (StringUtils.isBlank(personName) || address == null) {
            return personName;
        }
        String reg = " |\\:|：|,|，|-|——";
        String[] nameArr = personName.split(reg);

        if (nameArr == null || nameArr.length <= 1) {
            return personName;
        }

        try {

            if (StringUtils.isBlank(address.getDetail())) {
                logger.debug(String.format("订单导入地址解析，得到的详细地址为空，原始地址：%s，", rawAddress));
            }
            //名称包含空格 可能把详细地址也拼进去了
            if (StringUtils.isNotBlank(address.getDetail())) {
                personName = personName.replace(address.getDetail(), "");

            } else if (StringUtils.isNotBlank(newAddressDetail)) {//去掉了省市区的详细地址
                for (String str : nameArr) {
                    if (newAddressDetail.startsWith(str)) {//详细地址包含了这一段
                        personName = personName.replace(str, "");
                        break;
                    }
                }
            }
            logger.debug(String.format("订单导入地址解析，名称替换，原始地址：%s，名称：【%s -> %s】", rawAddress, parseAddress.getName(), personName));
            return personName.replaceAll(reg, "");
        } catch (Exception e){
            logger.error(String.format("订单导入解析名称出错，原始地址信息：%s, personName:%s, parseAddress:%s", rawAddress, personName, JSONObject.toJSONString(parseAddress)), e);
            return personName;
        }
    }

    /**
     *
     * @param val  需要替换的值
     * @param data  行数组
     * @param indexMap 数组下标Map
     * @param index  固定下标
     */
    private void replaceValIfNull(String val, String[] data, Map<Integer, Integer> indexMap, int index){
        if(StringUtils.isNotBlank(val) && StringUtils.isBlank(data[indexMap.get(index)])){
            data[indexMap.get(index)] = val;
        }
    }

    /**
     * 去除字符串中头尾多包含的空格、制表符、换页符等
     *
     * @param source
     * @return
     */
    public static String removeAllBlank(String source) {
        String result = "";
        if (null != source && !"".equals(source)) {
            result = source.replaceAll("^[\\p{Zs}*|\\s*]*", "").replaceAll("[\\p{Zs}*|\\s*]*$", "");
        }
        return result;
    }


    class ErrorData {
        String[] data;
        String errorMsg;

        public String[] getData() {
            return data;
        }

        public void setData(String[] data) {
            this.data = data;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public ErrorData(String[] data, String errorMsg) {
            this.data = data;
            this.errorMsg = errorMsg;
        }
    }


    public static List<Order> convertMultiOrderInfo(Staff staff, String info, String payments, String prices, StringBuilder builder) {//后面用正则
        try {
            if (StringUtils.isEmpty(info)) {
                builder.append("[商家编码/规格商家编码]勾选了多商品模式，不能为空,");
                return null;
            }
//            if (Pattern.matches("[1-9]\\d*", num) && Pattern.matches("[1-9]\\d*", payment)) {
//                return null;
//            }
            String[] orderInfos = info.split(",|，");
            String[] paymentArr = new String[orderInfos.length];
            String[] priceArr = new String[orderInfos.length];
            if(StringUtils.isBlank(payments)){
                for(int i=0;i<paymentArr.length ; i++){
                    paymentArr[i] = "0.00";
                }
            } else {
                paymentArr = payments.split(",|，");
                if (orderInfos.length != paymentArr.length) {
                    builder.append("[商家编码/规格商家编码]和金额无法一一对应（如确认无误，请确认金额有超过1000元，金额列未设置为文本格式）,");
                    return null;
                }

            }
            if(StringUtils.isBlank(prices)){
                for(int i=0;i<priceArr.length ; i++){
                    priceArr[i] = "";
                }
            } else {
                priceArr = prices.split(",|，");
                if (orderInfos.length != priceArr.length) {
                    builder.append("[商家编码/规格商家编码]和商品单价无法一一对应（如确认无误，请确认商品单价有超过1000元，商品单价列未设置为文本格式）,");
                    return null;
                }

            }
            List<Order> orderList = new ArrayList<>();
            if (orderInfos != null && orderInfos.length > 0) {
                for (int i = 0;i < orderInfos.length; i++) {
                    String orderInfo = orderInfos[i];
                    String[] splitInfos = orderInfo.split("\\*");
                    if(splitInfos == null){
                        builder.append("[商家编码/规格商家编码]格式不正确，");
                        return null;
                    }
                    if(splitInfos.length>2){
                        builder.append("[商家编码/规格商家编码]格式不正确，一个商品出现多个*,");
                        return null;
                    }
                    Order order = new TbOrder();
                    order.setSysOuterId(splitInfos[0].trim());
                    if(splitInfos.length == 1){
                        order.setNum(1);
                    } else {
                        try {
                            order.setNum(Integer.parseInt(splitInfos[1].trim()));
                        } catch (Exception e){
                            builder.append("[商家编码/规格商家编码]商家编码和数量填写格式有误:"+orderInfo+",");
                            return null;
                        }
                        if(order.getNum()<=0){
                            builder.append("[商家编码/规格商家编码]数量必须为正整数:"+orderInfo+",");
                            return null;
                        }
                    }
                    String pay = paymentArr[i].trim();
                    try {
                        double payDouble = Double.parseDouble(pay);
                        if(payDouble < 0){
                            builder.append("金额不能为负数,");
                            return null;
                        }
                        order.setPayment(String.format("%.2f", payDouble));
                    } catch (Exception e){
                        builder.append("金额格式不对,");
                        return null;
                    }
                    String price = priceArr[i].trim();
                    if(StringUtils.isNotBlank(price)){
                        try {
                            double priceDouble = Double.parseDouble(price);
                            if(priceDouble < 0){
                                builder.append("商品单价不能为负数,");
                                return null;
                            }
                            order.setPrice(String.format("%.6f", priceDouble));
                        } catch (Exception e){
                            builder.append("商品单价格式不对,");
                            return null;
                        }
                    } else {
                        order.setPrice("");
                    }
                    orderList.add(order);
                }
            }
            return orderList;
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("解析商家编码出错,请规范编码信息！")), e);
            return null;
        }
    }
    public static List<Order> convertOrderInfo(Staff staff, String info, String num, String payment) {//后面用正则
        try {
            if (!info.endsWith(")") && !info.endsWith("）")) {
                return null;
            }
            if (Pattern.matches("[1-9]\\d*", num) && Pattern.matches("[1-9]\\d*", payment)) {
                return null;
            }
            String[] orderInfos = info.split(",|，");
            List<Order> orderList = new ArrayList<>();
            if (orderInfos != null && orderInfos.length > 0) {
                for (String orderInfo : orderInfos) {
                    if (!orderInfo.endsWith(")") && !orderInfo.endsWith("）")) {
                        return null;
                    }
                    String[] splitInfos = orderInfo.split("\\)|）|\\(|（");
                    if (splitInfos != null && splitInfos.length == 4) {
                        Order order = new TbOrder();
                        order.setSysOuterId(splitInfos[0]);
                        order.setNum(Integer.parseInt(splitInfos[1]));
                        Double.parseDouble(splitInfos[3]);
                        order.setPayment(splitInfos[3]);
                        orderList.add(order);
                    } else {
                        return null;
                    }

                }
            }
            return orderList;
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("解析商家编码出错,请规范编码信息！")), e);
            return null;
        }
    }

    public static Date convertToDate(String dateStr, Date defaultDate) {
        try {
            if (dateStr.contains("-")) {
                return DateUtils.str2Date(dateStr, defaultDate, "-");
            }
            return DateUtils.str2Date(dateStr, defaultDate, "/");
        } catch (Exception e) {
            return DateUtils.str2Date(dateStr, defaultDate, "/");
        }
    }

    private void initializeTradeExtImport(Staff staff, List<Trade> trades) {
        List<Trade> pddTrade = new ArrayList<Trade>();
        for (Trade trade : trades) {
            if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSubSource())) {
                pddTrade.add(trade);
            }
            tradeExtBusiness.initTradeExts(staff, pddTrade);
        }
    }

    /**
     * 对昵称中的emoji进行处理
     *
     * @param tradeImportResult
     */
    public void handleBuyerNickEmoji(TradeImportResult tradeImportResult) {
        List<Trade> tradeList = tradeImportResult.getTradeList();
        for (Trade trade : tradeList) {
            trade.setBuyerNick(Strings.replaceUtf8mb4(trade.getBuyerNick()));
        }
    }

    private void importTradeAutoMerge(Staff staff, User user, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades) ||
                !TradeConfigGetUtil.get(staff, TradeConfigEnum.IMPORT_TRADE_AFTER_MATCH_MERGE_RULE).isOpen()
        ) {
            return;
        }
        if (user != null) {
            Map<String, Set<String>> sourceQueryKeysMap = new HashMap<>();
            for (Trade trade : trades) {
                String buyerNick = StringUtils.trimToEmpty(trade.getBuyerNick());
                if (StringUtils.isNotBlank(buyerNick)) {
                    sourceQueryKeysMap.computeIfAbsent(trade.getSource(), k -> new HashSet<>()).add(buyerNick);
                }
            }
            if (sourceQueryKeysMap.isEmpty()) {
                return;
            }
            tradeMergeService.mergeAuto(staff, user, sourceQueryKeysMap);
            return;
        }

        Map<Long, User> userIdMap = staff.getUserIdMap();
        if (userIdMap == null || userIdMap.isEmpty()) {
            logger.warn(LogHelper.buildLog(staff, "多店铺模式导入订单后合单失败, staff.userIdMap为空"));
            return;
        }

        Map<User, Map<String, Set<String>>> userToSourceQueryKeys = new HashMap<>();
        for (Trade trade : trades) {
            User u = userIdMap.get(trade.getUserId());
            if (u == null) {
                continue;
            }

            String buyerNick = StringUtils.trimToEmpty(trade.getBuyerNick());
            if (StringUtils.isNotBlank(buyerNick)) {
                userToSourceQueryKeys.computeIfAbsent(u, k -> new HashMap<>()).
                        computeIfAbsent(trade.getSource(), k -> new HashSet<>()).add(buyerNick);
            }
        }
        for (User u : userToSourceQueryKeys.keySet()) {
            Map<String, Set<String>> sourceQueryKeys = userToSourceQueryKeys.get(u);
            tradeMergeService.mergeAuto(staff, u, sourceQueryKeys);
        }
    }


    /**
     * 对奇门分销店铺导入的订单 增加分销属性
     *
     * @param user
     * @param tradeImportResult
     */
    private void handleQimenFxTrade(User user, TradeImportResult tradeImportResult) {
        // user为空 或者不是奇门店铺 跳过
        if (Objects.isNull(user) || !CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(user.getSource())) {
            return;
        }
        if (StringUtils.isNotBlank(user.getConf())) {
            UserConf conf = JSONObject.parseObject(user.getConf(), UserConf.class);
            if (Objects.nonNull(conf) && conf.getDistributionId() != null) {
                List<Trade> tradeList = tradeImportResult.getTradeList();
                for (Trade trade : tradeList) {
                    trade.setDestId(user.getCompanyId());
                    trade.setBelongType(2);
                    trade.setSourceId(conf.getDistributionId());
                }
            }
        }
    }

}

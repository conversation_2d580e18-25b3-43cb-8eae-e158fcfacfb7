package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.services.trades.ITradeSearchParamsHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-06-25
 */
@Component
public class TradeSearchParamsAgent{

    @Autowired(required = false)
    List<ITradeSearchParamsHandler> handlers;


    public boolean handle(Staff staff, TradeQueryParams params) {
        if (CollectionUtils.isEmpty(handlers)) {
            return true;
        }
        for (ITradeSearchParamsHandler handler : handlers) {
            if (!handler.handle(staff,params)) {
                return false;
            }
        }
        return true;
    }
}

package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderAdditionalConditionEnum;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trades.search.OrderAdditionalConditionEnum.PROP_GIFT;
import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 *             (include[0] OR include[1] OR ...include[n]  )
 *             AND
 *             (
 *               NOT EXISTS ( select 1 from order where o2.company_id = ? and o2.enable_status=1 and o2.belong_sid = o.belong_sid
 *                            AND (exclude[0] OR exclude[1] OR ...exclude[n]
 *                          )
 *              )
 *
 *
 *          ps 这里理论上来说 应该要是 EXISTS ( select 1 from order where o2.company_id = ? and o2.enable_status=1 and o2.belong_sid = o.belong_sid
 *                                    AND (include[0] OR include[1] OR ...include[n]
 *                                 )
 *          也就是说要独立于其他order的条件去过滤的 但是历史逻辑如此 先保持现状
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SORT_ADDTIONAL)
public class AddtionalConditionConverter extends AbsOrderConditionConverter {

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return notEmpty(condition.getAdditionals());
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        //防呆 去重
        Set<OrderAdditionalConditionEnum> include = Arrays.stream(condition.getAdditionals()).filter(x->!x.isExclude()).collect(Collectors.toSet());
        Set<OrderAdditionalConditionEnum> exclude = Arrays.stream(condition.getAdditionals()).filter(x->x.isExclude()).collect(Collectors.toSet());

        filter(include);

        if (CollectionUtils.isEmpty(include) && CollectionUtils.isEmpty(exclude)){
            return false;
        }

        boolean added = false;
        if (CollectionUtils.isNotEmpty(include)) {
            added = true;
            orderQry.append(" AND (");
            hotQry.append(" AND (");
            int counter = 0;
            for (OrderAdditionalConditionEnum con : include) {
                if (counter > 0) {
                    orderQry.append(" OR ");
                    hotQry.append(" OR ");
                }
                orderQry.append(con.getSql());
                if (Objects.equals(con, PROP_GIFT)) {
                    hotQry.append(" o.is_gift = 1");
                }else {
                    hotQry.append(con.getSql());
                }
                counter ++;
            }
            orderQry.append(")");
            hotQry.append(")");
        }

        //注意这里因为带了 o2.belong_sid = o.belong_sid  所以热点表是不支持的
        if (CollectionUtils.isNotEmpty(exclude)) {
            added = true;
            String table = orderQry.getOrderTable();

            orderQry.append(" AND NOT EXISTS ( SELECT 1 from  "+table+"_"+staff.getDbInfo().getOrderDbNo()
                           +"  o2 WHERE o2.company_id = ? and o2.enable_status<> 0 and o2.belong_sid = o.belong_sid AND (").add(staff.getCompanyId());
            int counter = 0;
            for (OrderAdditionalConditionEnum con : exclude) {
                if (counter > 0) {
                    orderQry.append(" OR ");
                }
                orderQry.append(con.getSql());
                counter ++;
            }
            orderQry.append("))");
        }


        return added;
    }

    private void filter(Set<OrderAdditionalConditionEnum> include) {
        //这里合起来就是查全集 相当于条件无效 直接跳过 比如 订单包含赠品 或 不包含赠品 ,其实就是查所有订单
        if (include.contains(OrderAdditionalConditionEnum.PROP_NO_GIFT) && include.contains(OrderAdditionalConditionEnum.PROP_GIFT)) {
            include.clear();
        }
        if (include.contains(OrderAdditionalConditionEnum.TYPE_COMBINE)
                && include.contains(OrderAdditionalConditionEnum.TYPE_NORMAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_PROCESS)
                && include.contains(OrderAdditionalConditionEnum.TYPE_GROUP)
        ) {
            include.clear();
        }
        if (include.contains(OrderAdditionalConditionEnum.TYPE_NORMAL_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_COMBINE_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_GROUP_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_PROCESS_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.PROP_VIRTUAL)
        ) {
            include.clear();
        }


        if (include.contains(OrderAdditionalConditionEnum.TYPE_NORMAL_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_NORMAL)
        ) {
            include.remove(OrderAdditionalConditionEnum.TYPE_NORMAL_IGNORE_VIRTUAL);
        }
        if (include.contains(OrderAdditionalConditionEnum.TYPE_COMBINE_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_COMBINE)
        ) {
            include.remove(OrderAdditionalConditionEnum.TYPE_COMBINE_IGNORE_VIRTUAL);
        }
        if (include.contains(OrderAdditionalConditionEnum.TYPE_GROUP_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_GROUP)
        ) {
            include.remove(OrderAdditionalConditionEnum.TYPE_GROUP_IGNORE_VIRTUAL);
        }
        if (include.contains(OrderAdditionalConditionEnum.TYPE_PROCESS_IGNORE_VIRTUAL)
                && include.contains(OrderAdditionalConditionEnum.TYPE_PROCESS)
        ) {
            include.remove(OrderAdditionalConditionEnum.TYPE_PROCESS_IGNORE_VIRTUAL);
        }
    }


}

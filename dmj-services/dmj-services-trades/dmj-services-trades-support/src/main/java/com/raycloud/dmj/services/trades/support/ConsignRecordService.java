package com.raycloud.dmj.services.trades.support;

import java.util.*;
import java.util.stream.Collectors;

import com.raycloud.dmj.business.logistics.UploadDelayBusiness;
import com.raycloud.dmj.dao.trade.ConsignRecordDao;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.services.trades.IUploadErrorTypeService;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.trades.IConsignRecordService;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * 发货记录服务
 *
 * <AUTHOR>
 */
@Service
public class ConsignRecordService implements IConsignRecordService {

    @Resource
    ConsignRecordDao consignRecordDao;

    @Resource
    UploadDelayBusiness uploadDelayBusiness;

    @Resource
    IUploadErrorTypeService uploadErrorTypeService;
    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;

    @Override
    public List<ConsignRecordStatistics> statistics(Staff staff) {
        return consignRecordDao.count(staff);
    }

    @Override
    public List<ConsignRecord> list(Staff staff, ConsignRecordQueryParams params) {
        fillNullSort(params);
        List<ConsignRecord> consignRecords = consignRecordDao.list(staff, params);
        uploadDelayBusiness.filterErrorDesc(staff, consignRecords);
        uploadErrorTypeService.setErrorTypeDesc(staff, consignRecords);
        fillLogisticsCompanyName(staff, consignRecords);
        return consignRecords;
    }

    /**
     * 填充快递公司
     */
    private void fillLogisticsCompanyName(Staff staff, List<ConsignRecord> consignRecords) {
        if (CollectionUtils.isEmpty(consignRecords)) {
            return;
        }
        List<Long> logisticsCompanyIds = consignRecords.stream().map(ConsignRecord::getLogisticsCompanyId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(logisticsCompanyIds)) {
            return;
        }
        List<UserLogisticsCompany> userLogisticsCompanys = userLogisticsCompanyService.queryByIds(staff, logisticsCompanyIds, 1);
        if (CollectionUtils.isEmpty(userLogisticsCompanys)) {
            return;
        }
        Map<Long, String> userLogisticsCompanyMap = userLogisticsCompanys.stream().collect(Collectors.toMap(UserLogisticsCompany::getId, UserLogisticsCompany::getName));
        for (ConsignRecord consignRecord : consignRecords) {
            String logisticsCompanyName = userLogisticsCompanyMap.get(consignRecord.getLogisticsCompanyId());
            consignRecord.setLogisticsCompanyName(StringUtil.isEmpty(logisticsCompanyName) ? consignRecord.getTemplateName() : logisticsCompanyName);
        }
    }

    @Override
    public Integer countOfList(Staff staff, ConsignRecordQueryParams params) {
        fillNullSort(params);
        return consignRecordDao.countOfList(staff, params);
    }

    @Override
    public void bindConsignRecord(Staff staff, List<Trade> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> sids = new ArrayList<>();
        for (Trade trade : list) {
            if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                sids.addAll(trade.getMessageMemos().stream().map(MessageMemo::getSid).collect(Collectors.toList()));
            } else {
                sids.add(trade.getSid());
            }
        }
        List<ConsignRecord> consignRecords = consignRecordDao.queryBySids(staff, null, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(consignRecords)) {
            return;
        }
        uploadErrorTypeService.setErrorTypeDesc(staff, consignRecords);
        Map<Long, String> sidErrorTypeMap = consignRecords.stream().collect(Collectors.toMap(ConsignRecord::getSid, ConsignRecord::getErrorTypeDesc, (v1, v2) -> v1));
        for (Trade trade : list) {
            if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                //合单设置为所有子单的拼接值
                trade.setUploadErrorTypeDesc(trade.getMessageMemos().stream().map(o -> sidErrorTypeMap.get(o.getSid())).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));
            } else {
                trade.setUploadErrorTypeDesc(sidErrorTypeMap.get(trade.getSid()));
            }
        }
    }

    @Override
    public void cut(Staff staff, Long[] sidArray, boolean cut) {
        if (sidArray == null || sidArray.length == 0) {
            return;
        }
        List<ConsignRecord> consignRecords = consignRecordDao.queryBySids(staff, null, sidArray);
        Assert.notEmpty(consignRecords, "根据sid无法查询到对应记录");
        List<ConsignRecord> record2Update = buildUpdateCutRecord(consignRecords, cut);
        if (CollectionUtils.isEmpty(record2Update)) {
            return;
        }
        consignRecordDao.batchUpdate(staff, record2Update);
    }

    @Override
    public Map<Long, ConsignRecord> getConsignRecordMap(Staff staff,List<Long> sids) {
        Map<Long, ConsignRecord> result = new HashMap<>();
        Long[] sidArray = new Long[sids.size()];
        List<ConsignRecord> consignRecords = consignRecordDao.queryBySids(staff,1,sids.toArray(sidArray));
        if(CollectionUtils.isNotEmpty(consignRecords)){
            for(ConsignRecord consignRecord : consignRecords){
                result.put(consignRecord.getSid(),consignRecord);
            }
        }
        return result;
    }

    private List<ConsignRecord> buildUpdateCutRecord(List<ConsignRecord> consignRecords, boolean cut) {
        List<ConsignRecord> record2Update = new ArrayList<>();
        Integer isCut = cut ? 1 : 0;
        for (ConsignRecord record : consignRecords) {
            if (isCut.equals(record.getIsCut())) {
                continue;
            }
            ConsignRecord newRecord = new ConsignRecord();
            newRecord.setSid(record.getSid());
            newRecord.setCompanyId(record.getCompanyId());
            newRecord.setIsCut(isCut);
            record2Update.add(newRecord);
        }
        return record2Update;
    }

    private void fillNullSort(ConsignRecordQueryParams params) {
        if (params.getSort() == null) {
            params.setSort(new Sort("consigned", "DESC"));
        }
        if (params.getSort().getField() == null || params.getSort().getField().trim().isEmpty()) {
            params.getSort().setField("consigned");
        }
        if (params.getSort().getOrder() == null || params.getSort().getOrder().trim().isEmpty()) {
            params.getSort().setOrder("DESC");
        }
    }
}

package com.raycloud.dmj.services.trades.support;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.trades.TradeTraceQueryParams;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  TradeTraceService 负责转发操作
 *      将写入请求通过ec转发到TradeTraceListener中去执行
 *      将读取请求转发到dubbo服务
 */
@Service
public class TradeTraceService implements ITradeTraceService {

    @Resource(name = "tradeTraceServiceDubbo")
    ITradeTraceService tradeTraceServiceDubbo;

    @Autowired
    TbTradeDao tbTradeDao;

    @Resource
    IEventCenter eventCenter;

    private static final String DELIMITER = ",";

    /**
     * 逗号隔开
     */
    private static final String COMMA_SEPARATED = "\\d+(,\\d+)*";

    @Override
    public void addTradeTrace(Staff staff, TradeTrace tradeTrace) {
        eventCenter.fireEvent(this, new EventInfo("trade.trace.forward").setArgs(new Object[]{staff}), Lists.newArrayList(tradeTrace));
    }

    @Override
    public void batchAddTradeTrace(Staff staff, List<TradeTrace> tradeTraces) {
        eventCenter.fireEvent(this, new EventInfo("trade.trace.forward").setArgs(new Object[]{staff}), tradeTraces);
    }

    @Override
    public List<TradeTrace> getTradeTraceBySids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)){
            return new ArrayList<>();
        }
        return tradeTraceServiceDubbo.getTradeTraceBySids(staff, sids);
    }

    @Override
    public List<TradeTrace> getTradeTraceByCondition(Staff staff, Date start, Date end, String actionLike, List<Long> sids) {
        return tradeTraceServiceDubbo.getTradeTraceByCondition(staff, start, end, actionLike, sids);
    }

    @Override
    public List<TradeTrace> getTradeTraceByCondition(Staff staff, TradeTraceQueryParams tradeTraceQueryParams, Page page) {
        //是否存在ERP的Trade流水短号，系统生成，公司维度唯一
        if (StringUtils.isNotEmpty(tradeTraceQueryParams.getShortIds())) {
            if (StringUtils.isNotEmpty(tradeTraceQueryParams.getShortIds()) && !tradeTraceQueryParams.getShortIds().matches(COMMA_SEPARATED)) {
                throw new IllegalArgumentException("内部单号不合法！");
            }
            Long[] shortId = Strings.getAsLongArray(tradeTraceQueryParams.getShortIds(), DELIMITER, true);
            if (shortId.length > 500) {
                throw new IllegalArgumentException("内部单号最大支持500条！");
            }
            List<TbTrade> tradeList = tbTradeDao.queryByKeys(staff, "sid,merge_sid", "short_id", shortId);
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(tradeList)){
                return Collections.emptyList();
            }
            Set<Long> sidList = tradeList.stream().map(trade -> trade.getSid()).collect(Collectors.toSet());
            Set<Long> mergeSids = tradeList.stream().filter(x->x.getMergeSid() != null && x.getMergeSid() > 0).map(trade -> trade.getMergeSid()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(mergeSids)) {
                sidList.addAll(mergeSids);
            }
            //参数中是否有sid 求交集
            if (StringUtils.isNotEmpty(tradeTraceQueryParams.getSids())) {
                Set<Long> paramsSidList = Arrays.stream(Strings.getAsLongArray(tradeTraceQueryParams.getShortIds(), DELIMITER, true)).collect(Collectors.toSet());
                sidList.retainAll(paramsSidList);
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sidList)) {
                tradeTraceQueryParams.setShortIds(null);
                tradeTraceQueryParams.setSids(StringUtils.join(sidList, DELIMITER));
            }else {
                return Collections.emptyList();
            }
        }

        return tradeTraceServiceDubbo.getTradeTraceByCondition(staff, tradeTraceQueryParams, page);
    }


    @Override
    public List<TradeTrace> getTradeTraceBySid(Staff staff, Long sid) {
        return tradeTraceServiceDubbo.getTradeTraceBySid(staff, sid);
    }

    @Override
    public String[][] tradeTraceExport(Staff staff, TradeTraceQueryParams params) {
        return tradeTraceServiceDubbo.tradeTraceExport(staff, params);
    }


}

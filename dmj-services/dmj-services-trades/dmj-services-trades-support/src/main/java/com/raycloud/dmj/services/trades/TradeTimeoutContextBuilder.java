package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.trades.request.TradeTimeoutContext;
import com.raycloud.dmj.services.feature.FeatureService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TradeTimeoutContextBuilder {

    @Resource
    FeatureService featureService;

    public TradeTimeoutContext build(Staff staff) {
        TradeTimeoutContext ctx = new TradeTimeoutContext();
        ctx.setTimeoutFromSysProduct(featureService.checkHasFeature(staff.getCompanyId(), Feature.EDIT_DELIVERY_TIME));
        return ctx;
    }
}

package com.raycloud.dmj.services.backend;

import com.netflix.hystrix.contrib.javanica.aop.aspectj.HystrixCommandAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * HystrixCommandAop
 * Created by ji<PERSON><PERSON><PERSON> on 16/9/19.
 */
@Configuration
@EnableAspectJAutoProxy
public class HystrixCommandAop {
    @Bean
    public HystrixCommandAspect hystrixAspect() {
        return new HystrixCommandAspect();
    }
}

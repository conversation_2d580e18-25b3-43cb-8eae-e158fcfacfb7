package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.TimeTypeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_SYS_STATUS)
public class SysStatusConditionConverter extends AbsConditionConverter{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query) {
        return true;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        List<String> status = new ArrayList<>();
        DevLogBuilder builder = DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("店铺相关条件处理 ");
        try {
            if (condition.getSysStatus() != null && condition.getSysStatus().length > 0) {
                status.addAll(Arrays.asList(condition.getSysStatus()));
            }else{
                status = TradeStatusUtils.getAllSysStatus();
                builder.append("未指定则查询所有状态");
            }
            status = addByTimeType(condition,status, TimeTypeEnum.CONSIGN_TIME, TradeStatusUtils.AFTER_SEND_GOODS_STATUS, q,builder);
            status = addByTimeType(condition,status, TimeTypeEnum.EXPRESS_PRINT_TIME, TradeStatusUtils.AFTER_PRINT_SYS_STATUS, q,builder);

            if (q.isStopQuery()) {
                return;
            }

            if (condition.getSysConsigned() != null) {
                if (condition.getIsConsigned() == null) {
                    condition.setIsConsigned(true);
                }else if (!isTrue(condition.getIsConsigned())) {
                    q.setStopQuery(true);
                    q.setStopReason("isConsigned为false仅能查到未发货订单 sysConsigned:" + condition.getSysConsigned());
                    return;
                }
            }

            if (isTrue(condition.getIsConsigned())) {
                builder.append("按isConsigned=true查询,自动设置系统状态范围为已发货");
                if (status.size() == 0) {
                    status.addAll(TradeStatusUtils.AFTER_SEND_GOODS_STATUS);
                }else{
                    Collection<String> intersection = CollectionUtils.intersection(TradeStatusUtils.AFTER_SEND_GOODS_STATUS, status);
                    if (intersection.isEmpty()) {
                        q.setStopQuery(true);
                        q.setStopReason("isConsigned为true只能查到已发货状态的订单:" + Arrays.toString(condition.getSysStatus()));
                        return;
                    }else{
                        status =  new ArrayList<>(intersection);
                    }
                }
            }
            condition.setSysStatus(status.toArray(new String[0]));

            if (notEmpty(condition.getSysStatus() )) {
                for (String sysStatus : condition.getSysStatus()) {
                    if (!TradeStatusUtils.isSysStatus(sysStatus)) {
                        q.setStopQuery(true);
                        q.setStopReason("不支持的状态值:" + sysStatus);
                        return;
                    }
                }
                andListCondition(q,"t.sys_status", condition.getSysStatus());
                context.setTradeTable(getTable(condition.getSysStatus()));
            }
        }finally {
            builder.multiPrintDebug(logger);
        }
    }

    private  List<String> addByTimeType(TradeQueryRequest condition,List<String> status, TimeTypeEnum timetype, List<String> afterPrintStatus, Query q,DevLogBuilder builder) {
        if (hasTimeCondition(condition, timetype)) {
            builder.append("按").append(timetype.getName()).append("查询,自动设置系统状态范围:").appendArray(afterPrintStatus);
            Collection<String> intersection = intersection(afterPrintStatus, status);
            if (intersection.isEmpty()) {
                q.setStopQuery(true);
                q.setStopReason("按"+timetype.getName()+"只能查到对应状态的订单:" + Strings.join(",",status));
            }else{
                return new ArrayList<>(intersection);
            }
        }
        return status;
    }

    private  String getTable(String[] statusArr) {
        String table = "trade";

        if (notEmpty(statusArr)) {
            boolean notConsign = true;
            for (String sysStatus : statusArr) {
                notConsign = notConsign && !TradeStatusUtils.isAfterSendGoods(sysStatus);
            }
            if (notConsign) {
                table = "trade_not_consign";
            }
        }
        return table;
    }


}

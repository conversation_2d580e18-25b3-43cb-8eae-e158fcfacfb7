package com.raycloud.dmj.services.trades.support.search.type;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 1688分销小站(分销)
 *
 */
@Component
@Order(1)
public class AlibabaFxTradeQueryType implements  ITradeQueryType{
    @Override
    public TradeTypeEnum getType() {
        return TradeTypeEnum.ALI_1688_FX_ROLE;
    }

    @Override
    public String getSql(Staff staff, Integer type, Query q, Long queryId) {
        return " t.v & 8388608>0 ";
    }
}

package com.raycloud.dmj.services.sensitive.system;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.sensitive.AbstractCustomizeTradeSecurityService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description 订单自定义脱敏通用逻辑
 * @date 2022-01-16
 */
@Service
public class CommonCustomizeTradeSensitiveService extends AbstractCustomizeTradeSecurityService {

    public static final String FULL_BLUR_STR = "****";

    @Override
    public void doSensitiveTrades(Staff staff, List<Trade> trades) {
        trades.forEach(trade -> {
            trade.setReceiverAddress(FULL_BLUR_STR);
            trade.setReceiverName(FULL_BLUR_STR);
            trade.setReceiverMobile(FULL_BLUR_STR);
            trade.setReceiverPhone(FULL_BLUR_STR);
        });
    }

    @Override
    public List<Trade> filterTrades(Staff staff, List<Trade> trades) {
        return trades;
    }
}

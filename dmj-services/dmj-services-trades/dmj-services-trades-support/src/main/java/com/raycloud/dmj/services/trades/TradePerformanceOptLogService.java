package com.raycloud.dmj.services.trades;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.trade.TradePerformanceCalBusiness;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.wave.ItemSkuCheckInfo;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by windy26205 on 19/11/19.
 */
@Service
public class TradePerformanceOptLogService {

    @Resource
    private TradeAuditPerformanceOptLogDao tradeAuditPerformanceOptLogDao;

    @Resource
    private TradeWeightPerformanceOptLogDao tradeWeightPerformanceOptLogDao;


    @Resource
    private TradePackPerformanceOptLogDao tradePackPerformanceOptLogDao;


    @Resource
    private TradeConsignPerformanceOptLogDao tradeConsignPerformanceOptLogDao;

    @Resource
    private TradeFinanceAuditPerformanceOptLogDao tradeFinanceAuditPerformanceOptLogDao;

    @Resource
    private ITradeCheckGoodsService tradeCheckGoodsService;

    @Resource
    TradePerformanceCalBusiness tradePerformanceCalBusiness;

    private final Logger logger = Logger.getLogger(this.getClass());

    public  TradePerformanceOptLog createOptLog(Staff staff,Trade trade,int type) {
        TradePerformanceOptLog tradePerformanceOptLog = new TradePerformanceOptLog();
        converTradeInfo(staff, trade, tradePerformanceOptLog);
        tradePerformanceOptLog.setType(type);
        Map<String,Object> infoJson = new HashMap<>();
        if(type == TradePerformanceOptLog.Audit_Type) {//
            if(!StringUtils.isEmpty(trade.getPayment())){
                infoJson.put("payment",(trade.getPayment()));
            }else {
                infoJson.put("payment","0.00");
            }


        }

        if(type == TradePerformanceOptLog.Weight_Type) {


            if(trade.getWeight() != null){
                infoJson.put("weight",(trade.getWeight()).toString());
            }else {
                infoJson.put("weight","0.00");
            }
          //  calItemInfo(trade, infoJson);

        }

        if(type == TradePerformanceOptLog.Pack_Type) {
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            List<ItemSkuCheckInfo> itemSkuCheckInfos = tradeCheckGoodsService.dealWithItemSkuCheckInfo(orderList);
            tradePerformanceOptLog.setInfoJson(JSONObject.toJSONString(itemSkuCheckInfos));
            return tradePerformanceOptLog;
        }

        if(type == TradePerformanceOptLog.Consign_Type || type == TradePerformanceOptLog.Re_Consign_Type || type == TradePerformanceOptLog.Cancel_Consign_Type) {

//            if(trade.getScalping() != null) {
//                infoJson.put("scalping",trade.getScalping());
//            }else {
//                infoJson.put("scalping",0);
//            }

//            infoJson.put("tradeCount",1);
//            calItemInfo(trade,infoJson);
            infoJson.put("consignType", type);
            if (StringUtils.isNotBlank(trade.getPayment())) {
                infoJson.put("payment", trade.getPayment());
            } else {
                infoJson.put("payment", "0.00");
            }
            if (StringUtils.isNotBlank(trade.getPostFee())) {
                infoJson.put("postFee", trade.getPostFee());
            } else {
                infoJson.put("postFee", "0.00");
            }
            if (Objects.nonNull(trade.getWaveId()) && trade.getWaveId() > 0) {
                infoJson.put("waveId", trade.getWaveId());
            }

            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orderList)) {
                Map<String, String> orderPaymentMap = new HashMap<>();
                for (Order order:orderList) {
                    if(Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                        continue;
                    }
                    if(order.isVirtual() || order.ifNonConsign()) {
                        continue;
                    }
                    if (null != order.getSuits() && order.getSuits().size() > 0 && (order.getType() == null || order.getType() == 2)) {
                        for (Order suit : order.getSuits()) {
                            String orderPayment = StringUtils.isNotBlank(suit.getPayment())?suit.getPayment():"0.00";
                            orderPaymentMap.put(new StringBuilder(String.valueOf(suit.getItemSysId())).append("_").append(suit.getSkuSysId()).append("_").append(suit.getGiftNum()).toString(), orderPayment);
                        }
                        String orderPayment = StringUtils.isNotBlank(order.getPayment())?order.getPayment():"0.00";
                        orderPaymentMap.put(new StringBuilder(String.valueOf(order.getItemSysId())).append("_").append(order.getSkuSysId()).append("_").append(order.getGiftNum()).toString(), orderPayment);
                    } else {
                        String orderPayment = StringUtils.isNotBlank(order.getPayment())?order.getPayment():"0.00";
                        orderPaymentMap.put(new StringBuilder(String.valueOf(order.getItemSysId())).append("_").append(order.getSkuSysId()).append("_").append(order.getGiftNum()).toString(), orderPayment);
                    }
                }
                infoJson.put("orderPayment", orderPaymentMap);
            }
        }
        tradePerformanceCalBusiness.calItemInfo(staff, trade, infoJson);
        tradePerformanceOptLog.setInfoJson(JSONObject.toJSONString(infoJson));
      //  tradePerformanceOptLog.setInfoJson("");
        return tradePerformanceOptLog;
    }







    private  void converTradeInfo(Staff staff,Trade trade,TradePerformanceOptLog tradePerformanceOptLog) {
        tradePerformanceOptLog.setCompanyId(staff.getCompanyId());
        tradePerformanceOptLog.setEnableStatus(1);
        tradePerformanceOptLog.setCreated(new Timestamp(System.currentTimeMillis()));
        tradePerformanceOptLog.setSid(trade.getSid());
        tradePerformanceOptLog.setStaffId(staff.getId());
        tradePerformanceOptLog.setStaffName(staff.getName());
    }

    public void addAuditOptLog(Staff staff ,List<Trade> tradeList,int type) {//后面改为
        if(CollectionUtils.isNotEmpty(tradeList)) {
            try{
                Map<Long,Trade> tradeMap = new HashMap<>();
                List<Trade> trueTradeList = tradePerformanceCalBusiness.getPerformanceTradeInfo(staff, TradeUtils.toSids(tradeList));
                for(Trade trade : tradeList) {
                    tradeMap.put(trade.getSid(),trade);
                }
                for(Trade trade : trueTradeList) {
                    Trade logTrade = tradeMap.get(trade.getSid());
                    if(logTrade != null) {
                        if (StringUtils.isBlank(logTrade.getPayment()) || "NaN".equals(logTrade.getPayment())) {
                            trade.setPayment("0");
                        } else {
                            trade.setPayment(logTrade.getPayment());
                        }
                        trade.setWeight(logTrade.getWeight());
                    }
                }
                if(CollectionUtils.isNotEmpty(trueTradeList)) {
                    List<TradePerformanceOptLog> tradePerformanceOptLogList = new ArrayList<>();
                    for(Trade trade : trueTradeList) {
                        //if(trade.getEnableStatus() != null && trade.getEnableStatus() == 1){
                        tradePerformanceOptLogList.add(createOptLog(staff, trade, type));
                        //}
                    }
                    if(type == TradePerformanceOptLog.Audit_Type) {//
                        tradeAuditPerformanceOptLogDao.batchInsert(staff,tradePerformanceOptLogList);
                    }

                    if(type == TradePerformanceOptLog.Weight_Type) {
                        tradeWeightPerformanceOptLogDao.batchInsert(staff,tradePerformanceOptLogList);
                    }

                    if(type == TradePerformanceOptLog.Pack_Type) {
                            Map<Long, Long> waveIdMap = trueTradeList.stream().filter(tureTrade -> tureTrade.getSid() != null && tureTrade.getWaveId() != null).collect(Collectors.toMap(Trade::getSid, Trade::getWaveId));
                            tradePerformanceOptLogList.forEach(tradePerformanceOptLog -> {
                                tradePerformanceOptLog.setWaveId(waveIdMap.get(tradePerformanceOptLog.getSid()) == null ? 0L : waveIdMap.get(tradePerformanceOptLog.getSid()));
                            });
                            tradePackPerformanceOptLogDao.batchInsert(staff,tradePerformanceOptLogList);
                    }

                    if(type == TradePerformanceOptLog.Consign_Type || type == TradePerformanceOptLog.Re_Consign_Type || type == TradePerformanceOptLog.Cancel_Consign_Type) {
                        tradeConsignPerformanceOptLogDao.batchInsert(staff, tradePerformanceOptLogList);
                    }
                    if (type == TradePerformanceOptLog.Finance_Audit_Type) {
                        tradeFinanceAuditPerformanceOptLogDao.batchInsert(staff, tradePerformanceOptLogList);
                    }
                }
            }catch(Exception e){//不要影响正常业务逻辑
                    logger.warn(LogHelper.buildLog(staff, String.format("绩效流水日志插入失败，type:%s,sids:%s", type,TradeUtils.toSids(tradeList))),e);
            }
        }

    }




}

package com.raycloud.dmj.services.logistics;

import com.google.common.collect.Lists;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.logistics.UploadDelayBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.enums.LogisticsStatusEnum;
import com.raycloud.dmj.domain.platform.trades.LogisticsRequest;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.PlatformLogisticsTrackingService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.trades.IConsignRecordService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于平台的查询物流接口做一个直接上传操作
 *
 *
 * <AUTHOR>
 * @Date 2019-08-15
 * @Time 13:40
 */
@Service
public class TradeLogisticsPlatformQueryConsignServices extends AbstractTradeLogisticsTrackingServices {
    private final String CACHE_KEY = "TradeLogisticsPlatformQueryConsignServices_query_tracking";
    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;
    @Resource
    IExpressCompanyService expressCompanyService;
    @Resource
    UploadDelayBusiness uploadDelayBusiness;
    @Resource
    private PlatformManagement platformManagement;
    @Resource
    private IExpressTemplateDubboService expressTemplateDubboService;
    @Resource
    ICache cache;

    private List<Integer> SYNC_TIME = Arrays.asList(16, 18, 20, 22, 8, 10);

    @Override
    public boolean isAllow(Staff staff) {
        boolean logisticsRealTime = tradeLocalConfigurable.isLogisticsRealTime(staff.getCompanyId());
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        if (!SYNC_TIME.contains(hour) || !logisticsRealTime) {
            return false;
        }
        boolean result = false;
        try {
            boolean add = cache.add(CACHE_KEY, "1", 60 * 60 * 24);
            boolean add1 = cache.add(CACHE_KEY + "_" + staff.getCompanyId(), "1", 60 * 60 * 24);
            boolean add2 = cache.add(CACHE_KEY + "_" + staff.getCompanyId() + "_" + hour, "1", 60 * 30);
            if (add && add1 && add2) {
                result = true;
            } else {
                if (add) {
                    cache.delete(CACHE_KEY);
                }
                if (add1) {
                    cache.delete(CACHE_KEY + "_" + staff.getCompanyId());
                }
            }

        } catch (CacheException e) {
            Logs.error("缓存读取出错：" + e.getMessage());
            return false;
        }
        return result;
    }

    @Override
    public Integer getOrder() {
        return 3;
    }
    @Resource
    private IConsignRecordService consignRecordService;
    @Override
    public void invokeLogisticsInfo(Staff staff) {
        Page page = new Page();
        int pageNo = 1;
        page.setPageSize(500);
        LogisticsTrackingPollPoolQueryParams queryParams = new LogisticsTrackingPollPoolQueryParams();
        queryParams.setPage(page);
        int totalCount = 0;
        int succCount = 0;
        List<UserExpressTemplate> expressTemplates = expressTemplateDubboService.getUserExpressWlbMixIncHidden(staff);
        Map<String, UserExpressTemplate> expressTemplateMap = expressTemplates.stream().collect(Collectors.toMap(t -> t.getId() + "_" + t.getIsWlb(), t -> t, (k1, k2) -> k2));
        Map<Long, ExpressCompany> expressCompanyIdMap = expressCompanyService.getExpressCompanyIdMap();

        for ( ; ; ) {
            ConsignRecordQueryParams params = new ConsignRecordQueryParams();
            page.setPageNo(pageNo);
            params.setPage(page);
            params.setIsError(2);
            List<ConsignRecord> records = consignRecordService.list(staff, params);
            pageNo++;
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            List<ConsignRecord> logisticsRecord = createLogisticsRecord(staff, records, expressCompanyIdMap, expressTemplateMap);
            if (CollectionUtils.isNotEmpty(logisticsRecord)) {
                succCount += logisticsRecord.size();
                uploadDelayBusiness.upload(staff, logisticsRecord.stream().map(ConsignRecord::getSid).toArray(Long[]::new), SendType.DELAY, null);
            }
            totalCount += records.size();
        }
        Logs.debug(LogHelper.buildLogHead(staff).append(" 跟新总记录数:").append(totalCount).append(" 跟新成功记录数:").append(succCount));
    }

    private List<ConsignRecord> createLogisticsRecord(Staff staff, List<ConsignRecord> logisticsInfos, Map<Long, ExpressCompany> expressCompanyIdMap, Map<String, UserExpressTemplate> expressTemplateMap) {
        List<ConsignRecord> updateLogisticsList = Lists.newLinkedList();
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return updateLogisticsList;
        }
        Long start = System.currentTimeMillis();
        for (int i = 0; i < logisticsInfos.size(); i++) {
            ConsignRecord record = logisticsInfos.get(i);
            ExpressCompany expressCompany = getExpressCode(record, staff, expressCompanyIdMap, expressTemplateMap);
            if (expressCompany == null) {
                continue;
            }
            LogisticsTrackingPollPool result  = null;
            try {
                User user = staff.getUserByUserId(record.getUserId());
                try {
                    LogisticsRequest logisticsRequest = new LogisticsRequest();
                    logisticsRequest.setSid(record.getSid());
                    logisticsRequest.setExpressNo(record.getOutSid());
                    logisticsRequest.setExpressCompany(expressCompany);
                     result = platformManagement.getAccess(record.getSource(), PlatformLogisticsTrackingService.class).
                            queryPlatformLogisticInfo(user, logisticsRequest);
                    if(result != null && StringUtils.isNoneBlank(result.getLogisticsStatus()) && LogisticsStatusEnum.getEnum(result.getLogisticsStatus()).getKey() >= LogisticsStatusEnum.ACCEPT.getKey()){
                        updateLogisticsList.add(record);
                    }
                }catch (Exception e){
                    Logs.debug(LogHelper.buildLogHead(staff).append("多平台"+user.getSource()+"查询物流失败！sid:"+record.getSid()+"错误信息:"+e.getMessage()));
                }
            } catch (Exception e) {
                Logs.error("查询实时物流记录失败，msg:" + e.getMessage());
            } finally {
                Logs.debug(String.format("订单【sid:%s,outSid:%s,expressCode:%s,state:%s,key:%s】", record.getSid(), record.getOutSid(), expressCompany.getCode(), result == null ? "" : LogisticsStatusEnum.getEnum(result.getLogisticsStatus()).getValue(), createTplKey(record.getTemplateId(), record.getTemplateType())));
            }
        }
        return updateLogisticsList;
    }


    private ExpressCompany getExpressCode(ConsignRecord record, Staff staff, Map<Long, ExpressCompany> codeMap, Map<String, UserExpressTemplate> expressTemplateMap) {
        String tplKey = createTplKey(record.getTemplateId(), record.getTemplateType());
        UserExpressTemplate tpl = expressTemplateMap.get(tplKey);
        if (tpl == null) {
            Logs.error(LogHelper.buildLogHead(staff).append(record.getSid() + "订单快递模版找不到"));
            return null;
        }
        return codeMap.get(tpl.getExpressId());
    }

    private String createTplKey(Long templateId, Integer templateType){
        return new StringBuilder().append(templateId).append("_").append(templateType).toString();
    }

    @Override
    public void end(Staff staff) {
        try {
            cache.delete(CACHE_KEY);
            cache.delete(CACHE_KEY +"_"+ staff.getCompanyId());
        } catch (CacheException e) {
            Logs.error("缓存删除出错："+ e.getMessage());
        }
    }
}

package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OutSidConditionProps;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;


/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_OUT_SID)
public class OutSidConditionConverter extends AbsConditionConverter {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query) {
        return (request.getOutSids() != null && request.getOutSids().length > 0)
                || request.getHasOutSid() != null;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        Boolean hasOutSid = condition.getHasOutSid();
        if ((hasOutSid != null && !hasOutSid) && ConvertBase.notEmpty(condition.getOutSids())) {
            q.setStopQuery(true);
            q.setStopReason("指定hasOutSid为false,且包含outSid条件");
        }

        if (condition.getHasOutSid() != null) {
            if (ConvertBase.isTrue(condition.getHasOutSid())) {
                ConvertBase.andNotNullQuery(q, "t.out_sid");
            }else {
                ConvertBase.andNullQuery(q, "t.out_sid");
            }
        }

        if (ConvertBase.isEmpty(condition.getOutSids())) {
            return;
        }

        Set<Long> sidsByMultiPacks = new HashSet<>();

        if (condition.getOutSidProps() != null && !Objects.equals(condition.getOutSidProps(), OutSidConditionProps.STRICT)) {
            //快递单号查询时，如果单号是子母单，且是拆分订单，则只查询出对应订单，不查出所有
            boolean exactSearchForSubShipment = Objects.equals(condition.getOutSidProps(), OutSidConditionProps.MAIN_AND_SELF);
            //依据运单号获取一单多包详情
            sidsByMultiPacks = multiPacksPrintTradeLogService.getSidsByMultiPacks(staff, exactSearchForSubShipment, Arrays.asList(condition.getOutSids()));
        }

        if (sidsByMultiPacks.size() > 0) {
            q.append(" AND (");
            ConvertBase.listCondition(q, "t.out_sid", condition.getOutSids());
            q.append(" OR ");
            ConvertBase.listCondition(q, "t.sid", sidsByMultiPacks);
            q.append(" ) ");
        } else {
            ConvertBase.andListCondition(q, "t.out_sid", condition.getOutSids());
        }

    }


}

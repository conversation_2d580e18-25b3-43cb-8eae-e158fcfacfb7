package com.raycloud.dmj.services.trades;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.raycloud.dmj.dao.trade.FxgDfMallDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.FxgDfMall;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 不要被名字迷惑，现在是代发商家店铺，不仅仅是放心购
 *
 * 商家代发店铺的操作封装
 */

@Service
@Slf4j
public class FxgDfMallService implements IFxgDfMallService{

    @Resource
    private FxgDfMallDao fxgDfMallDao;

    @Override
    @Cacheable(value = "defaultCache#600", key = "'company_df_mall_' + #staff.companyId")
    public List<FxgDfMall> queryByCompanyId(Staff staff) {
        return fxgDfMallDao.queryFxgDfMalls(staff, null);
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'company_df_mall_' + #staff.companyId + '_' + #taobaoId")
    public List<FxgDfMall> queryByTaobaoId(Staff staff, Long taobaoId) {
        return fxgDfMallDao.queryFxgDfMallsByTaobaoId(staff, taobaoId);
    }


    /**
     * 批量插入
     * 过滤掉mallMaskName mallMaskId 为空的
     */
    @Override
    @CacheEvict(value = "defaultCache", key = "'company_df_mall_' + #staff.companyId")
    public int batchInsert(Staff staff, List<FxgDfMall> fxgDfMalls) {
        if (CollectionUtils.isEmpty(fxgDfMalls)) {
            return 0;
        }

        //过滤
        fxgDfMalls = fxgDfMalls.stream().filter(fxgDfMall ->
                StringUtils.isNotBlank(fxgDfMall.getMallMaskName()) && Objects.nonNull(fxgDfMall.getMallMaskId()
                )).collect(Collectors.toList());

        return fxgDfMallDao.batchInsert(staff, fxgDfMalls);
    }

    /**
     * 单个插入
     * 校验
     * fxgDfMall 不为null
     * fxgDfMall.mallMaskId 不为null
     * fxgDfMall.mallMaskId 不为blank
     */
    @Override
    @CacheEvict(value = "defaultCache", key = "'company_df_mall_' + #staff.companyId")
    public void insert(Staff staff, FxgDfMall fxgDfMall) {
        if (fxgDfMall == null ||
                fxgDfMall.getMallMaskId() == null ||
                StringUtils.isBlank(fxgDfMall.getMallMaskName())) {
            return;
        }

        fxgDfMallDao.insert(staff, fxgDfMall);
    }

    /**
     * 根据companyId mallMaskId 更新 mallMaskName or enableStatus
     * fxgDfMall公司id可以不用赋值
     */
    @Override
    @CacheEvict(value = "defaultCache", key = "'company_df_mall_' + #staff.companyId")
    public void batchUpdate(Staff staff, List<FxgDfMall> fxgDfMalls) {
        if (CollectionUtils.isEmpty(fxgDfMalls)) {
            return;
        }

        //过滤
        fxgDfMalls = fxgDfMalls.stream().filter(fxgDfMall ->
                Objects.nonNull(fxgDfMall.getMallMaskId()) &&
                        (StringUtils.isNotBlank(fxgDfMall.getMallMaskName()) || fxgDfMall.getEnableStatus() != null)
        ).collect(Collectors.toList());

        fxgDfMallDao.batchUpdate(staff, fxgDfMalls);
    }

    /**
     * 单个更新 mallMaskName or enableStatus
     * fxgDfMall公司id可以不用赋值
     * 根据companyId mallMaskId更新
     */
    @Override
    @CacheEvict(value = "defaultCache", key = "'company_df_mall_' + #staff.companyId")
    public void update(Staff staff, FxgDfMall fxgDfMall) {
        if (fxgDfMall == null ||
                fxgDfMall.getMallMaskId() == null) {
            return;
        }

        if (StringUtils.isBlank(fxgDfMall.getMallMaskName()) && fxgDfMall.getEnableStatus() == null) {
            return;
        }

        fxgDfMallDao.update(staff, fxgDfMall);
    }

    /**
     * 根据 companyId mallMaskId 查询单个 FxgDfMall
     */
    @Override
    public Optional<FxgDfMall> querySingleFxgDfMall(Staff staff, Long taobaoId, Long mallMaskId) {
        if (mallMaskId == null) {
            return Optional.empty();
        }

        FxgDfMall fxgDfMall = new FxgDfMall();
        fxgDfMall.setMallMaskId(mallMaskId);
        fxgDfMall.setTaobaoId(taobaoId);

        return Optional.ofNullable(fxgDfMallDao.querySingleFxgDfMall(staff, fxgDfMall));
    }

    /**
     * 批量查询
     * 根据公司id  mallMaskIds
     * mallMaskIds为空  直接调用 queryByCompanyId
     */
    @Override
    public List<FxgDfMall> queryFxgDfMalls(Staff staff, List<Long> mallMaskIds) {
        if (mallMaskIds == null || mallMaskIds.isEmpty()) {
            return queryByCompanyId(staff);
        }

        return fxgDfMallDao.queryFxgDfMalls(staff, mallMaskIds);
    }

}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.platform.trades.PlatWriteOffVO;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformWriteOffAccess;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-06-06
 */
@Service
public class TaobaoReceiveCodeQuery {


    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    PlatformManagement platformManagement;

    public List<String> queryTidsByReceiveCodes(Staff staff,Map<String, List<Long>> userSourceMap , String ... receiveCodes){
        if (receiveCodes == null  || receiveCodes.length == 0) {
            return new ArrayList<>();
        }
        if (userSourceMap.isEmpty()) {
            return new ArrayList<>();
        }
        DevLogBuilder builder = new DevLogBuilder(staff).append("淘宝/天猫收货码查询").append("receiveCode", receiveCodes);
        List<String> list = Arrays.asList(receiveCodes);

        Map<Long, User> userIdMap = new HashMap<>();
        for (User user : staff.getUsers()) {
            userIdMap.put(user.getId(),user);
        }
        List<String> result = null;
        for (Map.Entry<String, List<Long>> entry : userSourceMap.entrySet()) {
            if (platformManagement.isContainAccess(entry.getKey(), IPlatformWriteOffAccess.class)) {
                IPlatformWriteOffAccess access = platformManagement.getAccess(entry.getKey(), IPlatformWriteOffAccess.class);

                for (Long userId : entry.getValue()) {
                    User user = userIdMap.get(userId);
                    Assert.notNull(user, "不存在的用户id:" + userId);
                    Map<String, PlatWriteOffVO> map = access.queryWriteOffs(user, list);
                    if (result == null) {
                        result = new ArrayList<>();
                    }
                    if (!map.isEmpty()) {
                        for (PlatWriteOffVO value : map.values()) {
                            if (value.getTradeId() != null) {
                                result.add(value.getTradeId());
                                builder.append(String.valueOf(userId),value.getTradeId());
                            }
                        }
                    }
                }
            }
        }

        builder.append("result",result).printDebug(logger);
        return result;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.except.TradeExceptCancelBusiness;
import com.raycloud.dmj.business.except.TradeExceptCancelThreadPoolBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.except.ExcepData;
import com.raycloud.dmj.domain.trade.except.ExceptHandleContext;
import com.raycloud.dmj.domain.trade.except.ExceptHandlerDto;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeCancelExcepOldService;
import com.raycloud.dmj.services.trades.ITradeCancelExceptService;
import com.raycloud.dmj.services.utils.LogHelper;
import net.bytebuddy.implementation.bytecode.Throw;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
public class TradeCancelExceptService implements ITradeCancelExceptService {


    @Resource
    private ITradeCancelExcepOldService  tradeCancelExcepOldService;

    @Resource
    private TradeExceptCancelThreadPoolBusiness tradeExceptCancelThreadPoolBusiness;
    @Resource
    private TradeExceptCancelBusiness tradeExceptCancelBusiness;

    @Resource
    private IProgressService progressService;

    @Override
    public void cancelExceptByEvent(Staff staff, ExceptHandlerDto exceptHandlerDto) {
        String eventName = exceptHandlerDto.getEventName();
        if (StringUtils.isBlank(eventName)) {
            // 事件接收过来的事件名不能为空
            throw new RuntimeException("事件名称不能为空");
        }
        if (!TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            tradeCancelExcepOldService.cancelExceptByEvent(staff, exceptHandlerDto);
            return;
        }

        Logs.ifDebug(LogHelper.buildLog(staff, String.format("接收到事件%s", eventName)));
        long start = System.currentTimeMillis();
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_CANCEL_EXCEP).setProgress(1);
        progressData.setAtomicSucNum(new AtomicLong(0));
        try {
            tradeExceptCancelThreadPoolBusiness.cancelExcept(staff, exceptHandlerDto, progressData);
        } catch (Throwable e) {
            Logs.error(LogHelper.buildLog(staff, "取消异常出错"), e);
        } finally {
            progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_CANCEL_EXCEP, progressData);
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("事件[%s]处理结束,耗时:%sms", eventName, (System.currentTimeMillis() - start))));
        }
    }

    @Override
    public List<Trade> cancelExcept(Staff staff, ExceptHandlerDto exceptHandlerDto) {
        if (!TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            return tradeCancelExcepOldService.cancelExcep(staff, exceptHandlerDto);
        }
        boolean recordTrace = exceptHandlerDto.isRecordTrace();
        Integer isCancelSmallRefund = exceptHandlerDto.getIsCancelSmallRefund();
        List<String> systems = exceptHandlerDto.getSystems();
        List<Long> customs = exceptHandlerDto.getCustoms();
        Long[] sids = exceptHandlerDto.getSids();
        ExceptHandleContext exceptHandleContext = new ExceptHandleContext();
        exceptHandleContext.setRecordTrace(recordTrace);
        exceptHandleContext.setCustomExceptions(customs);
        exceptHandleContext.setSystemExceptions(systems);
        exceptHandleContext.setIsCancelSmallRefund(isCancelSmallRefund);
        exceptHandleContext.setTradeFxExcepSync(exceptHandlerDto.isTradeFxExcepSync());
        return tradeExceptCancelThreadPoolBusiness.doCancelExcep(staff, exceptHandleContext, Arrays.stream(sids).collect(Collectors.toList()));
    }

    @Override
    public List<Trade> cancelPlatformExcept(Staff staff, ExceptHandlerDto exceptHandlerDto) {
        boolean recordTrace = exceptHandlerDto.isRecordTrace();
        Long[] sids = exceptHandlerDto.getSids();
        return tradeCancelExcepOldService.cancelPlatformExcept(staff, recordTrace, sids);
    }

    @Override
    public List<Trade> cancelReturnWmsExcept(Staff staff, ExceptHandlerDto exceptHandlerDto) {
        boolean recordTrace = exceptHandlerDto.isRecordTrace();
        Long[] sids = exceptHandlerDto.getSids();
        return tradeCancelExcepOldService.cancelReturnWmsExcept(staff, recordTrace, sids);
    }

    @Override
    public ExcepData cancelExcept(Staff staff, List<Trade> originTrades, ExceptHandlerDto exceptHandlerDto) {
        if (!TradeExceptWhiteUtils.openCancelExceptCompanyIds(staff)) {
            return tradeCancelExcepOldService.cancelExcep(staff, originTrades, exceptHandlerDto);
        }
        return tradeExceptCancelBusiness.cancelExceptNoLock(staff, originTrades, exceptHandlerDto);
    }
}

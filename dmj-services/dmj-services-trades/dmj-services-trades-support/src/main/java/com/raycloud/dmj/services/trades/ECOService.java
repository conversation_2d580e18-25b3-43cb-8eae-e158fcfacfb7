package com.raycloud.dmj.services.trades;

import com.alibaba.fastjson.JSON;
import com.google.api.client.util.Base64;
import com.google.common.collect.Lists;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.bo.ECOTokenAuthBO;
import com.raycloud.dmj.domain.trades.vo.ECOTokenAuthVO;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.tb.common.TaobaoClientHelper;
import com.raycloud.dmj.tb.common.TbAppInfo;
import com.raycloud.eagle.taobao.TaoBaoWebInvokeFilter;
import com.taobao.api.internal.util.StringUtils;
import com.taobao.api.request.TopOnceTokenGetRequest;
import com.taobao.api.response.TopOnceTokenGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ECOService {

    FxBusiness fxBusiness;

    IStaffService staffService;

    @Autowired
    public ECOService(FxBusiness fxBusiness, IStaffService staffService) {
        this.fxBusiness = fxBusiness;
        this.staffService = staffService;
    }

    /**
     * 获取解密授权信息
     */
    public ECOTokenAuthVO generateAuth(ECOTokenAuthBO tokenAuthBO) {
        ECOTokenAuthVO tokenAuthVO = buildTokenAuthVO(tokenAuthBO);
        if (tokenAuthBO.getIsTopToken()
                && !Objects.equals(tokenAuthBO.getUser().getSource(), CommonConstants.PLAT_FORM_TYPE_SMT)) {
            // 调用淘宝api，获取topToken，用于组装解密sign
            TopOnceTokenGetRequest request = new TopOnceTokenGetRequest();
            request.setSecToken(tokenAuthVO.getSecToken());
            TopOnceTokenGetResponse response = new TaobaoClientHelper(tokenAuthBO.getUser()).request(request);
            tokenAuthVO.setTopToken(response.getToken());
        }
        if(Objects.equals(tokenAuthBO.getUser().getSource(), CommonConstants.PLAT_FORM_TYPE_SMT)) {
            tokenAuthVO.setSign(generateSmtSign(tokenAuthVO, TbAppInfo.SMT_APPSECRET, tokenAuthBO.getIsTopToken()));
        }else{
            tokenAuthVO.setSign(generateSign(tokenAuthVO, TaoBaoWebInvokeFilter.atiAppSecret, tokenAuthBO.getIsTopToken()));
        }
        return tokenAuthVO;
    }

    /**
     * 根据controller校验参数生成返回值 ECOTokenAuthVO
     */
    private ECOTokenAuthVO buildTokenAuthVO(ECOTokenAuthBO tokenAuthBO) {
        ECOTokenAuthVO tokenAuthVO = new ECOTokenAuthVO();
        handlGxTrade(tokenAuthBO, tokenAuthVO);
        tokenAuthVO.setAppKey(TaoBaoWebInvokeFilter.atiAppKey);
        tokenAuthVO.setAppName(TaoBaoWebInvokeFilter.atiAppName);
        tokenAuthVO.setAti(tokenAuthBO.getAti());
        tokenAuthVO.setSessionId(tokenAuthBO.getUser().getSessionId());
        tokenAuthVO.setTaobaoUserId(tokenAuthBO.getUser().getTaobaoId());
        tokenAuthVO.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        tokenAuthVO.setTopAppKey(TaoBaoWebInvokeFilter.apiAppKey);
        tokenAuthVO.setUserId(tokenAuthBO.getUser().getId());
        tokenAuthVO.setUserIp(tokenAuthBO.getUserIp());
        if (tokenAuthBO.getIsTopToken()) {
            tokenAuthVO.setSecToken(tokenAuthBO.getSecToken());
            tokenAuthVO.setMethod("taobao.top.oaid.client.decrypt");
        }
        //速卖通设置相关信息
        if(Objects.equals(tokenAuthBO.getUser().getSource(), CommonConstants.PLAT_FORM_TYPE_SMT)){
            tokenAuthVO.setAppKey(TbAppInfo.SMT_APPKEY);
            tokenAuthVO.setAppName(TbAppInfo.SMT_APPNAME);
            tokenAuthVO.setSellerId(Optional.ofNullable(tokenAuthBO.getUser().getConf())
                    .map(JSON::parseObject)
                    .flatMap(jsonObject -> Optional.ofNullable(jsonObject.getJSONObject("sellerInfo")))
                    .flatMap(jsonObject -> Optional.ofNullable(jsonObject.getString("sellerId")))
                    .orElse(""));
            if(org.apache.commons.lang3.StringUtils.isBlank(tokenAuthVO.getSellerId())){
                tokenAuthVO.setSellerId(Optional.ofNullable(tokenAuthBO.getUser().getConf())
                        .map(JSON::parseObject)
                        .flatMap(jsonObject -> Optional.ofNullable(jsonObject.getJSONObject("sellerInfo")))
                        .flatMap(jsonObject -> Optional.ofNullable(jsonObject.getString("userId")))
                        .orElse(""));
            }
        }
        return tokenAuthVO;
    }

    /**
     * gx订单传入tid，返回真实的tid
     */
    private void handlGxTrade(ECOTokenAuthBO tokenAuthBO, ECOTokenAuthVO tokenAuthVO) {
        if (tokenAuthBO.getIsGx() && !StringUtils.isEmpty(tokenAuthBO.getTid())) {
            Trade trade = new Trade();
            trade.setTid(tokenAuthBO.getTid());
            Staff fxStaff = staffService.queryFullByCompanyId(tokenAuthBO.getUser().getCompanyId());
            List<Trade> fxTrades = fxBusiness.getFxTradesByTids(fxStaff, Lists.newArrayList(trade));
            if (CollectionUtils.isNotEmpty(fxTrades)) {
                tokenAuthVO.setTid(fxTrades.get(0).getTid());
            }
        }
    }


    /**
     * 根据appSecret和参数生成sign
     *
     * @param isGenerateDecryptSign 是：生成获取secToken的sign，否：生成topToken以及解密的sign
     */
    public String generateSign(ECOTokenAuthVO tokenAuthVO, final String appSecret, boolean isGenerateDecryptSign) {
        Map<String, String> params = tokenAuthVO.getParams(isGenerateDecryptSign);
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        StringBuilder text = new StringBuilder();
        text.append(appSecret);
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.areNotEmpty(key, value)) {
                text.append(key).append(value);
            }
        }
        text.append(appSecret);
        Mac sha256_HMAC;
        try {
            sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            return Base64.encodeBase64String(sha256_HMAC.doFinal(text.toString().getBytes(StandardCharsets.UTF_8)));
        } catch (Exception ignore) {
            throw new RuntimeException("生成sign失败");
        }
    }

    /**
     * 速卖通根据appSecret和参数生成sign
     *
     */
    public String generateSmtSign(ECOTokenAuthVO tokenAuthVO, final String appSecret, boolean isGenerateDecryptSign) {
        Map<String, String> params = tokenAuthVO.getSmtParams(isGenerateDecryptSign);
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        StringBuilder text = new StringBuilder();
        text.append(appSecret);
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.areNotEmpty(key, value)) {
                text.append(key).append(value);
            }
        }
        text.append(appSecret);
        Mac sha256_HMAC;
        try {
            sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            return Base64.encodeBase64String(sha256_HMAC.doFinal(text.toString().getBytes(StandardCharsets.UTF_8)));
        } catch (Exception ignore) {
            throw new RuntimeException("生成sign失败");
        }
    }

}

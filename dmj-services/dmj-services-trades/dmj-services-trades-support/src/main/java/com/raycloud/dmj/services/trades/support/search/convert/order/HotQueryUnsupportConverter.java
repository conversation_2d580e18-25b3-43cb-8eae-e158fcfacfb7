package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.services.trades.support.search.convert.ConditionConstants;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.andListCondition;
import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.andSingleCondition;

/**
 * @Description <pre>
 *  仅order表支持的查询条件
 * </pre>
 * <AUTHOR>
 * @Date 2025-06-09
 */
@Component
@Order(ConditionConstants.CONVERTER_ORDER_SUPPORT_ONLY)
public class HotQueryUnsupportConverter extends AbsOrderConditionConverter {

    @Override
    boolean isNeedConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        return true;
    }

    @Override
    boolean doConvert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry) {
        boolean added = false;
        Query sysRef = new Query();
        added = added | andSingleCondition(sysRef,"o.ident_code",condition.getIdentCode(),condition.getQueryType());
        added = added | andListCondition(sysRef,"o.stock_status",condition.getStockStatus(),1);
        added = added | andSingleCondition(sysRef,"o.non_consign",condition.getNonConsign(),1);

        if (added) {
            orderQry.append(" AND ( o.item_sys_id > 0  ");
            orderQry.append(sysRef.getQ().toString()).add(sysRef.getArgs());
            orderQry.append(")");
        }
        return added;
    }


}

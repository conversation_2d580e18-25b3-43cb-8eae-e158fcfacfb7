package com.raycloud.dmj.services.trades.support.utils;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.SimpleTrade;
import com.raycloud.dmj.business.kdzs.KdzsEpidemicAreaBusiness;
import com.raycloud.dmj.domain.trades.kdzs.EpidemicArea;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: jinghe
 * @mail: <EMAIL>
 * @Date: 2022/5/6 10:41 上午
 * @Description:
 */
@Component
public class ForbidAddressUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext = null;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 系统停发地址匹配，省市区匹配
     *
     * @param simpleTrade
     * @return
     */
    public static Boolean matchForbidAddress(SimpleTrade simpleTrade) {
        // 省
        String receiverState = StringUtils.trimToEmpty(simpleTrade.getReceiverState());
        //市
        String receiverCity = StringUtils.trimToEmpty(simpleTrade.getReceiverCity());
        // 区
        String receiverDistrict = StringUtils.trimToEmpty(simpleTrade.getReceiverDistrict());
        if (StringUtils.isBlank(receiverState) || StringUtils.isBlank(receiverCity) || StringUtils.isBlank(receiverDistrict)) {
            return false;
        }
        Map<String, List<EpidemicArea>> rstMap = applicationContext.getBean(KdzsEpidemicAreaBusiness.class).query(null);
        if (MapUtils.isEmpty(rstMap)) {
            Logs.info(String.format("sid =%s ,缓存中没有查到停发地址信息!", simpleTrade.getSid()));
            return false;
        }
        Set<String> keys = rstMap.keySet();
        for(String key:keys){
            String[] arr = key.split("_");
            if(arr.length==1){
                if(receiverState.indexOf(arr[0])!=-1||arr[0].indexOf(receiverState)!=-1){
                   return true;
                }
            }
            if(arr.length==2){
                if((receiverState.indexOf(arr[0])!=-1||arr[0].indexOf(receiverState)!=-1)
                &&(receiverCity.indexOf(arr[1])!=-1||arr[1].indexOf(receiverCity)!=-1)){
                    return true;
                }
            }
            if(arr.length==3){
                if((receiverState.indexOf(arr[0])!=-1||arr[0].indexOf(receiverState)!=-1)
                        &&(receiverCity.indexOf(arr[1])!=-1||arr[1].indexOf(receiverCity)!=-1)
                &&(receiverDistrict.indexOf(arr[2])!=-1||arr[2].indexOf(receiverDistrict)!=-1)){
                    return true;
                }
            }
        }
        return false;
    }
}

package com.raycloud.dmj.services.trades.support.search.convert.order;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.OrderRefCondition;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.trades.support.search.convert.ConvertContext;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
public interface IOrderConditionConverter {

    /**
     *
     * @param staff
     * @param context
     * @param condition
     * @param q
     * @return 是否添加了相关条件
     */
    boolean convert(Staff staff, ConvertContext context, OrderRefCondition condition, Query orderQry, Query hotQry);

}

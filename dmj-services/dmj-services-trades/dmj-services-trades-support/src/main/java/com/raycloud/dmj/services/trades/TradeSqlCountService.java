package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeStaffConfigUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.ConsignUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2016-12-22 19:40
 */
@Service
public class TradeSqlCountService implements ITradeCountService {

    @Resource
    TbTradeDao tbTradeDao;
    @Resource(name = "tradeSqlQueryBuilder")
    TradeQueryBuilder tradeSqlQueryBuilder;
    @Resource ITradeConfigService tradeConfigService;
    @Resource IConsignRecordService consignRecordService;
    @Resource ITradeStaffConfigService tradeStaffConfigService;

    private Logger logger = Logger.getLogger(getClass());

    private void setTradeTable(Staff staff, Query q) {
        q.setTradeTable("trade_not_consign");
    }

    @Override
    public Long countExcep(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsExcep(1).setIsCancel(0).setIsOutstock(0).setIsPresell(0));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countExcep");
    }

    @Override
    public Long countRefund(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsRefund(1).setIsCancel(0));
        return queryCount(staff, q, "countRefund");
    }

    @Override
    public Long countWaitPay(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY).setCheckActive(true));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitPay");
    }

    @Override
    public Long countWaitAudit(Staff staff) {
        return countWaitAudit(staff,0);
    }

    @Override
    public Long countWaitAudit(Staff staff,Integer isExcep) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsExcep(isExcep).setIsCancel(0).setIsOutstock(0).setIsPresell(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT).setCheckActive(true));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitAudit");
    }

    @Override
    public Long countWaitFinanceAudit(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setIsPresell(0).setSysStatus(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT).setCheckActive(true));

        TradeConfigNew auditAutoInsufficientCanceled = TradeConfigGetUtil.get(staff, TradeConfigEnum.AUDIT_AUTO_INSUFFICIENT_CANCELED);
        if(auditAutoInsufficientCanceled !=null && auditAutoInsufficientCanceled.isOpen()){
            q.and().append("(").append(" (t.is_excep=0 OR (t.item_excep=2 AND t.v & 4 = 0 AND t.excep=0 AND t.is_halt = 0 AND t.unattainable = 0 AND t.is_refund = 0 AND ifnull(t.except_ids,'')='')) ");
            q.append(" OR (merge_sid > 0 AND exists (SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t1 ");
            q.append(" WHERE company_id = ? AND t1.merge_sid = t.merge_sid AND merge_sid > 0 and enable_status = 2 ").add(staff.getCompanyId());
            q.and().append(" (t1.is_excep=0 OR (t1.item_excep=2 AND t1.v & 4 = 0 AND t1.excep=0 AND t1.is_halt = 0 AND t1.unattainable = 0 AND t1.is_refund = 0 AND ifnull(t1.except_ids,'')='')) ");
            q.append("))) ");
        }else {
            q.and().append("t.is_excep = 0");
        }
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitFianceAudit");
    }

    @Override
    public Long countFinishAudit(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countFinishedAudit");
    }

    @Override
    public Long countWaitSetTemplate(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true).setWarehouseType(0));
        q.append(" AND template_id = -1");
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitSetTemplate");
    }

    @Override
    public Long countWaitPrint(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true).setWarehouseType(0));
        q.append(" AND (template_id > 0 AND express_print_time = ?)").add(TradeTimeUtils.INIT_DATE);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitPrint");
    }

    @Override
    public Long countFinishPrint(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true).setWarehouseType(0));
        q.append(" AND express_print_time > ?").add(TradeTimeUtils.INIT_DATE);
        q.append(" AND pay_time > ?").add(TradeTimeUtils.INIT_DATE);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countFinishPrint");
    }

    public Long countWaitPackCanUnPrint(Staff staff, TradeConfig tradeConfig, boolean check, Integer allowUnPrint) {
        if (allowUnPrint == null || allowUnPrint == 0) {
            return countWaitPack(staff, tradeConfig, check);
        } else {
            if (check && (tradeConfig.getOpenPackageExamine() != null && tradeConfig.getOpenPackageExamine() == 0)) {
                return 0L;
            }
            TradeQueryParams params = new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setIsHalt(0).setCheckActive(true).setWarehouseType(0);
            params.setAllowUnPrintPack(allowUnPrint);
            Query q = tradeSqlQueryBuilder.buildWaitPackQuery(new Query(), staff, params, tradeConfig);
            setTradeTable(staff, q);
            return queryCount(staff, q, "countWaitPack");
        }
    }

    @Override
    public Long countWaitPack(Staff staff, TradeConfig tradeConfig, boolean check) {
        if (check && (tradeConfig.getOpenPackageExamine() != null && tradeConfig.getOpenPackageExamine() == 0)) {
            return 0L;
        }
        TradeQueryParams params = new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setIsHalt(0).setCheckActive(true).setWarehouseType(0);
        Query q = tradeSqlQueryBuilder.buildWaitPackQuery(new Query(), staff, params, tradeConfig);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitPack");
    }

    @Override
    public Long countPackSplitable(Staff staff) {
        TradeQueryParams params = new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setIsHalt(0).setCheckActive(true).setWarehouseType(0);
        Query q = tradeSqlQueryBuilder.buildPackSplitableQuery(new Query(), staff, params);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countPacksplitable");
    }


    @Override
    public Long countWaitWeigh(Staff staff, TradeConfig tradeConfig, boolean check) {
        if (check && (tradeConfig.getOpenPackageWeigh() != null && tradeConfig.getOpenPackageWeigh() == 0)) {
            return 0L;
        }
        TradeQueryParams params = new TradeQueryParams().setIsExcep(0).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setIsHalt(0).setCheckActive(true).setWarehouseType(0);
        Query q = tradeSqlQueryBuilder.buildWaitWeighQuery(new Query(), staff, params, tradeConfig);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitWeigh");
    }

    @Override
    public Long countWaitConsign(Staff staff, TradeConfig tradeConfig) {
        return countWaitConsign(staff,tradeConfig,0);
    }

    @Override
    public Long countWaitConsign(Staff staff, TradeConfig tradeConfig,Integer isExcep) {
        TradeQueryParams params = new TradeQueryParams().setIsExcep(isExcep).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setIsHalt(0).setCheckActive(true).setWarehouseType(0);
        Query q = tradeSqlQueryBuilder.buildWaitConsignQuery(new Query(), staff, params, tradeConfig);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitConsign");
    }

    @Override
    public Long countWaitConsignExcep(Staff staff, TradeConfig tradeConfig){
        TradeQueryParams params = new TradeQueryParams().setIsExcep(1).setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true).setWarehouseType(0);
        Query q = tradeSqlQueryBuilder.buildWaitConsignQuery(new Query(), staff, params, tradeConfig);
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitConsignExcep");
    }

    @Override
    public Long countFinishConsign(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS));
        return queryCount(staff, q, "countFinishConsign");
    }

    @Override
    public Long countFinished(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED));
        return queryCount(staff, q, "countFinished");
    }

    @Override
    public Long countWaitOut(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsOutstock(1).setIsCancel(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true).setWarehouseType(0));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitOut");
    }

    @Override
    public Long countFinishOut(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsOutstock(1).setIsCancel(0).setSysStatus(Trade.SYS_STATUS_FINISHED).setCheckActive(true).setWarehouseType(0));
        return queryCount(staff, q, "countFinishOut");
    }

    @Override
    public Long countPresell(Staff staff) {
        Query q = buildSqlParams(staff,new TradeQueryParams().setIsCancel(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT).setIsPresell(1));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countPresell");
    }

    @Override
    public Long countWaitDestSendGoods(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countWaitDestSendGoods");
    }

    @Override
    public Long countWaitSelfSendGoods(Staff staff) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT));
        setTradeTable(staff, q);
        // 排除分销
        q.and().append(" not (t.convert_type = 1 AND  t.belong_type IN (1,3)  and t.dest_id>0) ");
        return queryCount(staff, q, "countWaitSelfSendGoods");
    }

    @Override
    public Long countWaitSelfAuditGoods(Staff staff, Integer isExcep) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT).setIsExcep(isExcep));
        setTradeTable(staff, q);
        // 排除代发
        q.and().append(" not (t.convert_type = 1 AND  t.belong_type IN (1,3)  and t.dest_id>0) ");
        return queryCount(staff, q, "countWaitSelfAuditGoods");
    }

    @Override
    public Long countWaitDfAuditGoods(Staff staff, Integer isExcep) {
        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT).setIsExcep(isExcep));
        setTradeTable(staff, q);
        // 指定代发
        q.and().append(" (t.convert_type = 1 AND  t.belong_type IN (1,3)  and t.dest_id>0) ");
        return queryCount(staff, q, "countWaitDfAuditGoods");
    }

    private Query buildSqlParams(Staff staff, TradeQueryParams params) {
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        tradeSqlQueryBuilder.buildSysStatusQuery(q, params, null);//此处待包装待称重待发货不从这里走，所以tradeConfig为null
        tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, params.getWarehouseType(), params.getWarehouseIds());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_cancel", params.getIsCancel());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_refund", params.getIsRefund());
        tradeSqlQueryBuilder.buildNotNullQuery(q, "is_halt", params.getIsHalt());
        return q;
    }

    private long queryCount(Staff staff, Query q, String op, int limit) {
        long start = System.currentTimeMillis();
        try {
            long num = tbTradeDao.queryTradesCount(staff, q, limit);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s from db took: %sms, NumFound: %s, Sql: %s", op, (System.currentTimeMillis() - start), num, q.toString())));
            }
            return num;
        } catch (Exception e) {
            throw new TradeException(String.format("[%s]订单统计出错", op), e);
        }
    }

    private long queryCount(Staff staff, Query q, String op) {
        long start = System.currentTimeMillis();
        try {
            long num = tbTradeDao.queryTradesCount(staff, q, false);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("%s from db took: %sms, NumFound: %s, Sql: %s", op, (System.currentTimeMillis() - start), num, q.toString())));
            }
            return num;
        } catch (Exception e) {
            throw new TradeException(String.format("[%s]订单统计出错", op), e);
        }
    }

    @Override
    public void setTradeNum(Staff staff, long queryId, TradeCount tc) {
        setTradeNum(staff, queryId, tc, 0);
    }

    @Override
    public void setTradeNum(Staff staff, long queryId, TradeCount tc, Integer allowUnPrintPack) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        // 兼容新增的接口，默认就是不查询异常
        Integer isExcep =  tc.getIsExcep()==null?0:tc.getIsExcep();
        if (queryId == QUERY_EXCEP) {
            tc.setExcepNum(countExcep(staff));
        } else if (queryId == QUERY_REFUND) {
            tc.setRefundNum(countRefund(staff));
        } else if (queryId == QUERY_WAIT_PAY) {
            tc.setWaitPayNum(countWaitPay(staff));
        } else if (queryId == QUERY_WAIT_AUDIT) {
            tc.setWaitAuditNum(countWaitAudit(staff,isExcep));
        } else if (queryId == QUERY_WAIT_FINANCE_AUDIT) {
            tc.setWaitFinanceAudit(countWaitFinanceAudit(staff));
        } else if (queryId == QUERY_FINISHE_AUDIT) {
            tc.setFinishAuditNum(countFinishAudit(staff));
        } else if (queryId == QUERY_WAIT_SET_TEMPLATE) {
            tc.setWaitSetTemplateNum(countWaitSetTemplate(staff));
        } else if (queryId == QUERY_WAIT_PRINT) {
            tc.setWaitPrintNum(countWaitPrint(staff));
        } else if (queryId == QUERY_FINISH_PRINT) {
            tc.setFinishPrintNum(countFinishPrint(staff));
        } else if (queryId == QUERY_WAIT_PACK) {
            if (tradeConfig.getOpenPackageExamine() != null && tradeConfig.getOpenPackageExamine() == 1) {
                tc.setWaitPackNum(countWaitPackCanUnPrint(staff, tradeConfig, true, allowUnPrintPack));
            }
            tc.setOpenIdentCode(tradeConfig.getOpenIdentCode());
        } else if (queryId == QUERY_PACK_SPLITABLE) {
            tc.setPackSplitableNum(countPackSplitable(staff));
        }  else if (queryId == QUERY_WAIT_WEIGH) {
            if (tradeConfig.getOpenPackageWeigh() != null && tradeConfig.getOpenPackageWeigh() == 1) {
                tc.setWaitWeighNum(countWaitWeigh(staff, tradeConfig, true));
            }
        } else if (queryId == QUERY_WAIT_CONSIGN) {
            if (tradeConfig.getOpenConsign() != null && tradeConfig.getOpenConsign() == 1) {
                tc.setWaitConsignNum(countWaitConsign(staff, tradeConfig,isExcep));
            }
        } else if (queryId == QUERY_SELLER_SEND_GOODS) {
            tc.setFinishConsignNum(countFinishConsign(staff));
        } else if (queryId == QUERY_FINISHED) {
            tc.setFinishedNum(countFinished(staff));
        } else if (queryId == QUERY_WAIT_OUTSTOCK) {
            tc.setWaitOutNum(countWaitOut(staff));
        } else if (queryId == QUERY_FINISH_OUTSTOCK) {
            tc.setFinishOutNum(countFinishOut(staff));
        } else if (queryId == QUERY_PRESELL) {
            tc.setPresellNum(countPresell(staff));
        } else if (queryId == QUERY_UPLOAD_FAIL) {
            ConsignRecordQueryParams params = new ConsignRecordQueryParams();
            params.setIsError(1);
            params.setUserId(StringUtils.join(CollectionUtils.isEmpty(staff.getUsers())?null:staff.getUsers().stream().map(User::getId).collect(Collectors.toList()), ","));
            ConsignUtils.judgeUserAuthority(params,staff);
            tc.setUploadFailNum(consignRecordService.countOfList(staff, params).longValue());
        } else if(queryId == QUERY_UN_CONSIGNED){
            tc.setUnConsignedNum(countUnConsigned(staff));
        } else if (queryId == QUERY_UPLOADING) {
            ConsignRecordQueryParams params = new ConsignRecordQueryParams();
            params.setIsError(0);
            params.setUserId(StringUtils.join(CollectionUtils.isEmpty(staff.getUsers())?null:staff.getUsers().stream().map(User::getId).collect(Collectors.toList()), ","));
            tc.setUploadingNum(consignRecordService.countOfList(staff, params).longValue());
        }else if (queryId == QUERY_COMMON){
            if (tradeConfig.getOpenPackageWeigh() != null && tradeConfig.getOpenPackageWeigh() == 1) {
                tc.setSellerSendNoWeightNum(countSellerSendNoWeight(staff));
            }
        }else if (queryId == QUERY_WAIT_DEST_SEND_GOODS){
            tc.setWaitDestSendGoodsNum(countWaitDestSendGoods(staff));
        }else if (queryId == QUERY_WAIT_SELF_SEND_GOODS){
            tc.setWaitSelfSendGoodsNum(countWaitSelfSendGoods(staff));
        }else if (queryId == QUERY_WAIT_SELF_AUDIT_GOODS){
            tc.setWaitSelfAuditGoodsNum(countWaitSelfAuditGoods(staff, isExcep));
        }else if (queryId == QUERY_WAIT_DF_AUDIT_GOODS){
            tc.setWaitDfAuditGoodsNum(countWaitDfAuditGoods(staff, isExcep));
        }else if (queryId == QUERY_DELIVER_EXCEPTION){
            tc.setUploadDeliveryExceptNum(countUploadDeliveryExcept(staff));
        } else{
            logger.warn(LogHelper.buildLog(staff, "unknown queryId: " + queryId));
        }
    }

    @Override
    public Long countUnConsigned(Staff staff) {

        Query q = buildSqlParams(staff, new TradeQueryParams().setIsCancel(0).setIsOutstock(0).
                setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT).setCheckActive(true));
        setTradeTable(staff, q);
        return queryCount(staff, q, "countUnConsigned");
    }

    @Override
    public Long countSellerSendNoWeight(Staff staff) {
   //取消验证是否开启称重的判断
//        TradeConfig tradeConfig = tradeConfigService.get(staff);
//        if (tradeConfig.getOpenPackageWeigh() == null || tradeConfig.getOpenPackageWeigh()  == 0){
//            return 0L;
//        }

        // 时间支持自定义，从配置里面查询时间，上限90天
        TradeStaffConfig tradeStaffConfig = tradeStaffConfigService.get(staff);
        Integer days = TradeStaffConfigUtils.getTradeCountDateRange("sellerSendNoWeightNumDateRange", tradeStaffConfig);
        days = Math.min(Optional.ofNullable(days).orElse(3),90);
        Date date = DateUtil.addDate(new Date(),-days);

        TradeQueryParams params = new TradeQueryParams().setIsCancel(0).setIsOutstock(0).setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS).setCheckActive(true).setWarehouseType(0).setIsWeigh(0).setValidateLater(true);
        Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
        q.append(" AND t.sys_status = ?").add(Trade.SYS_STATUS_SELLER_SEND_GOODS);
       // q.append(" AND t.sys_status IN(?, ?)").add(Trade.SYS_STATUS_SELLER_SEND_GOODS).add(Trade.SYS_STATUS_FINISHED);
        q.append(" AND t.weight = ?").add(0.0);
        q.append(" AND t.pay_time >= ?").add(DateUtil.format(date)+" 00:00:00");
        q.setTradeIndex(" force index(idx_cmpy_sstus_user_wid_ptime) ");
        q.setTradeTable("trade");
        return queryCount(staff, q, "countSellerSendNoWeight", 10000);
    }

    @Override
    public Long countUploadDeliveryExcept(Staff staff) {
        boolean hasDeliverExceptionPower = staff != null && staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("100040121");
        if (!hasDeliverExceptionPower) {
            return null;
        }
        try {
            return LogKit.took(() -> {
                TradeQueryParams params = new TradeQueryParams().setIsCancel(0).setIsExcep(null).setCheckActive(true).setIsPresell(0).setIsOutstock(0).setQueryId(QUERY_DELIVER_EXCEPTION);
                Query q = tradeSqlQueryBuilder.buildQuery(new Query(), staff, params);
                tradeSqlQueryBuilder.buildWarehouseQuery(q, staff, null);
                tradeSqlQueryBuilder.buildArrayQuery(q, "sys_status", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_CLOSED);
                q.append(" AND ((((t.item_excep & " + TradeConstants.DELIVER_EXCEPT + " > 0  OR t.v & 4 > 0 ) OR (t.merge_sid > 0 " +
                        "   AND exists ( SELECT 1 FROM trade_" + staff.getDbInfo().getTradeDbNo() + " t1  WHERE company_id = " + staff.getCompanyId() + " AND t1.merge_sid = t.merge_sid AND merge_sid > 0 AND enable_status = 2 " +
                        "   AND ((t1.item_excep & " + TradeConstants.DELIVER_EXCEPT + " > 0  OR t1.v & 4 > 0 ))))))) " +
                        "   AND t.is_cancel = 0");
                tradeSqlQueryBuilder.buildDeliverAndUploadQuery(q, staff, params);
                q.setTradeTable("trade");
                return queryCount(staff, q, "uploadDeliveryExceptNum", 10000);
            }, staff, logger);
        } catch (Exception ex) {
            logger.error("[countUploadDeliveryExcept]订单统计出错", ex);
            return 0L;
        }
    }

}

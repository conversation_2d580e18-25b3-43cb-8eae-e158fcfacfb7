package com.raycloud.dmj.services.trades.support.search.convert;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.TbTradeDecryptBusiness;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.trades.BatchRequestParameter;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeDecryptUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.support.search.TradeSearchSupport;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_RECEIVER)
public class ReceiverConditionConverter extends AbsConditionConverter{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    TbTradeDecryptBusiness tbTradeDecryptBusiness;


    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tbTradeSearchService;

    @Resource
    TradeExtBusiness tradeExtBusiness;

    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    TradeSearchSupport tradeSearchSupport;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        return notEmpty(condition.getReceiverMobiles());
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if (notEmpty(condition.getReceiverMobiles())) {
            Set<String> sources = context.refUsers.stream().map(x -> x.getSource()).collect(Collectors.toSet());
            Set<Long> userIds = context.refUsers.stream().map(x -> x.getId()).collect(Collectors.toSet());
            List<String> tids = processDesensitizationQuery(staff,null, Strings.join(",",condition.getReceiverMobiles()),null,condition.getStartTime(),condition.getEndTime(),userIds.toArray(new Long[0]));

            String[] tradeTypes = null;
            if (notEmpty(condition.getTradeTypes())) {
                tradeTypes = new String[condition.getTradeTypes().length];
                for (int i = 0; i < condition.getTradeTypes().length; i++) {
                    tradeTypes[i] = String.valueOf(condition.getTradeTypes()[i]);
                }
            }
            buildReceiverMobileQuery(staff,q,condition.getReceiverMobiles(),tids.toArray(new String[0]),sources,condition.getUserIds(),tradeTypes);
        }
    }

    /**
     * 收件人信息转tid
     * @param staff
     * @param receiverName
     * @param receiverMobile
     * @param receiverPhone
     * @param startTime
     * @param endTime
     * @param userIds
     * @return
     */
    public List<String> processDesensitizationQuery(Staff staff, String receiverName, String receiverMobiles, String receiverPhone, Date startTime, Date endTime, Long[] userIds) {
        QueryLogBuilder queryLogBuilder = new QueryLogBuilder(staff).append("根据收件人信息查询,")
                .append("name",receiverName).append("mobile",receiverMobiles).append("phone",receiverPhone);
        try {

            // 判断是否有开启oaid的店铺
            List<User> users = supportPageSearchUsers(staff, userIds);
            if (CollectionUtils.isEmpty(users)) {
                return null;
            }
            // 根据收件人姓名查询时，过滤所有淘系店铺，根据收件人手机号查询，过滤出支持手机号后四位查询的店铺
            List<User> userList = filterSearchUsers(staff, userIds,receiverName,receiverMobiles, users,queryLogBuilder);

            if (CollectionUtils.isEmpty(userList)) {
                queryLogBuilder.append("userIds","null");
                return null;
            }
            Date currentDate = new Date();
            BatchRequestParameter parameter = new BatchRequestParameter();
            parameter.setUser(userList);
            parameter.setName(receiverName);
            parameter.setMobile(receiverMobiles);
            parameter.setPhone(receiverPhone);
            parameter.setStartTime(org.apache.commons.lang3.ObjectUtils.defaultIfNull(startTime, DateUtils.addDays(currentDate, -90)));
            parameter.setEndTime(org.apache.commons.lang3.ObjectUtils.defaultIfNull(endTime, currentDate));
            List<String> tids = tbTradeDecryptBusiness.batchQueryByReceiver(staff, parameter);
            queryLogBuilder.append("userIds",userList.stream().map(x->x.getId()).collect(Collectors.toSet())).append("result", JSONArray.toJSONString(tids));
            return tids;
        }finally{
            queryLogBuilder.printDebug(logger);
        }
    }

    private List<User> filterSearchUsers(Staff staff, Long[] userIdArr,String receiverName,String receiverMobile, List<User> userList,QueryLogBuilder queryLogBuilder ) {
        List<User> valid = new ArrayList<>();
        //请求参数里面有店铺
        if (null !=userIdArr && userIdArr .length > 0){
            List<Long> userIds = Arrays.stream(userIdArr).collect(Collectors.toList());
            for (User user : userList){
                if (userIds.contains(user.getId())){
                    valid.add(user);
                }
            }
        }else {
            valid.addAll(userList);
        }
        if (StringUtils.isNotBlank(receiverName)) {
            return new ArrayList<>(valid);
        }
        // 淘系平台 都是返回全脱敏的手机号了 手机号后四位过滤已无意义
        // return supportLastFourPhoneSearch(staff, receiverMobile, valid,queryLogBuilder);
        return valid;
    }

    private List<User> supportLastFourPhoneSearch(Staff staff, String receiverMobiles,  List<User> userList,QueryLogBuilder queryLogBuilder) {
        List<User> supportLast4s = Lists.newArrayListWithCapacity(userList.size());
        List<User> users = new ArrayList<>();
        Set<Long> ids = new HashSet<>();

        userList.forEach(user -> {
            //支持后四位的User
            if (TradeDecryptUtils.supportLastFourPhoneSearch(user)) {
                supportLast4s.add(user);
            } else {
                if (!ids.contains(user.getId())) {
                    users.add(user);
                    ids.add(user.getId());
                }
            }
        });
        //这里的逻辑是 先拿手机号后四位到表里搂一下 如果搂不到 那么就肯定不存在 不需要调用平台接口
        List<User> unSupports = new ArrayList<>(supportLast4s);
        if (CollectionUtils.isNotEmpty(supportLast4s)) {
            if (StringUtils.isNotBlank(receiverMobiles)) {

                String[] split = receiverMobiles.split(",");
                List<String> mobileTails = new ArrayList<>(split.length);
                for (String s : split) {
                    if (StringUtils.isNotBlank(s) && s.length() > 4) {
                        String mobileTail = s.substring(s.length() - 4);
                        mobileTails.add(mobileTail);
                    }
                }

                if (CollectionUtils.isNotEmpty(mobileTails)) {
                    List<Trade> tempTrades = tbTradeSearchService.queryByMobileTail(staff, "sid,user_id", mobileTails.toArray(new String[0]));
                    if (CollectionUtils.isNotEmpty(tempTrades)) {
                        Set<Long> userIds = tempTrades.stream().map(Trade::getUserId).collect(Collectors.toSet());
                        if (CollectionUtils.isNotEmpty(userIds)) {
                            List<User> us = userList.stream().filter(t -> userIds.contains(t.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(us)) {
                                users.addAll(us);
                                unSupports.removeAll(us);
                            }
                        }
                    }
                }

            }
            if (CollectionUtils.isNotEmpty(unSupports)) {
                queryLogBuilder.append("通过后四位预查询trade过滤掉的店铺",unSupports.stream().map(User::getId).collect(Collectors.toSet()));
            }
        }

        return users;
    }


    /**
     * 判断是否有开启oaid的店铺
     *
     * @param staff
     * @param params
     * @return
     */
    private List<User> supportPageSearchUsers(Staff staff, Long[] userIds) {
        List<User> userList = new ArrayList<>();
        Map<Long, User> userMap = tradeSearchSupport.getUserMap(staff);
        if (null != userIds && userIds.length > 0) {
            for (Long id : userIds) {
                User user = userMap.get(id);
                if (TradeDecryptUtils.supportPageSearchUsers4All(user)) {
                    userList.add(user);
                }
            }
        } else {
            for (User user : userMap.values()) {
                if (TradeDecryptUtils.supportPageSearchUsers4All(user)) {
                    userList.add(user);
                }
            }
        }
        return userList;
    }


    public Query buildReceiverMobileQuery(Staff staff, Query q, String[] receiverMobiles,String[] refTids,Set<String> sources,Long[] userIds,String[] tradeTypes) {
        if (q.isStopQuery()) {
            return q;
        }
        //客户订单不去调用平台的转换openUid的接口 防止对平台接口的大量无效调用
        if (contains(tradeTypes, TradeTypeEnum.CUSTOMER_TRADE.getIntCode()) && tradeTypes.length == 1) {
            return q;
        }

        if (receiverMobiles != null && receiverMobiles.length > 0) {
            // 特殊情况： 输入是："," 或者",," 数组是空，字符串不是空
            String[] array = getReceiverMobile(staff, receiverMobiles);
            if(ArrayUtils.isEmpty(array)){
                return q;
            }
            q.setAllowTradeCountIndex(false);
            q.append(" AND (t.sid IN( select sid from ( select temp.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp where temp.company_id= " + staff.getCompanyId() + " and temp.enable_status > 0");
            StringBuilder buf = new StringBuilder();
            int n = 0;
            for (String v : array) {
                if (v == null) {
                    continue;
                }
                if (n++ > 0) {
                    buf.append(", ");
                }
                buf.append("?");
                q.add(v);
            }
            q.append(" AND temp.receiver_mobile IN (").append(buf).append(")");
            try {
                TradeQueryParams params = new TradeQueryParams();
                params.setReceiverMobile(Strings.join(",",receiverMobiles));
                params.getContext().setSources(sources);
                params.setUserIds(userIds);
                String tradeType = null;
                if (contains(tradeTypes, TradeTypeEnum.CASH_ON_DELIVERY_TRADE.getcode())) {
                    tradeType = TradeTypeEnum.CASH_ON_DELIVERY_TRADE.getcode();
                }
                params.setTradeType(tradeType);
                Query queryTradeExt =  tradeExtBusiness.buildReceiverInfo(staff,params);
                if (queryTradeExt.getQ().length() > 0) {
                    q.append(" UNION ALL ").append(queryTradeExt.getQ()).add(queryTradeExt.getArgs());
                }
            } catch (Exception e) {
                new QueryLogBuilder(staff).appendError("构建手机号索引查询语句失败 ",e).printWarn(logger,e);
            }
            if (null != refTids && refTids.length > 0) {
                q.append(" union all select temp1.sid from trade_" + staff.getDbInfo().getTradeDbNo() + " temp1 where temp1.company_id= " + staff.getCompanyId() + " and temp1.enable_status > 0 ");
                Set<String> tids = Sets.newHashSet(refTids);

                String inStr = tids.stream().map(tid -> "?").collect(Collectors.joining(",", "(", ")"));
                String orStr = tids.stream().map(tid -> " OR temp1.tid LIKE CONCAT(?, '-%')").collect(Collectors.joining(" "));

                q.append(" AND ").append("( temp1.tid in ").append(inStr).add(refTids).append(orStr).append(")").add(refTids);
            }
            q.append(") unionTemp");
            q.append("))");
        }
        return q;
    }


    private String[] getReceiverMobile(Staff staff, String[] split) {
        List<String> receiverMobileList = Lists.newArrayListWithCapacity(split.length);

        for (String mobile : split) {
            receiverMobileList.add(mobile);
            receiverMobileList.add(secretBusiness.encodeNum(staff, mobile));
            List<String> values = commonTradeDecryptBusiness.encryptByUser(staff, mobile,3,null,true);
            if (CollectionUtils.isNotEmpty(values)) {
                receiverMobileList.addAll(values);
            }
        }
        return receiverMobileList.toArray(new String[0]);
    }
}

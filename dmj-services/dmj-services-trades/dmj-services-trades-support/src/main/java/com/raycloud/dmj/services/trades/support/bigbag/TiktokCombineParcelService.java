package com.raycloud.dmj.services.trades.support.bigbag;

import com.raycloud.dmj.dao.trade.TradeCombineParcelDAO;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeParcelDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.platform.trades.tiktok.ErrorDetail;
import com.raycloud.dmj.domain.platform.trades.tiktok.GenerateTiktokFmRequest;
import com.raycloud.dmj.domain.platform.trades.tiktok.GenerateTiktokFmResponse;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeGroupParcelRequest;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.platform.trades.tiktok.ITiktokFmService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.enums.TiktokLogisticsProvider;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.IdWorkerFactory;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TiktokCombineParcelService extends CombineParcelAbstractHandler {

    private static final Logger logger = Logger.getLogger(TiktokCombineParcelService.class);

    @Resource
    private TradeCombineParcelDAO tradeCombineParcelDAO;

    @Resource
    private TradeParcelDAO tradeParcelDAO;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    IUserService userService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    ITiktokFmService tiktokFmService;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    IFileUploadService fileUploadService;

    @Override
    public void afterPropertiesSet() throws Exception {
        CombineParcelFactory.register(CommonConstants.PLAT_FORM_TYPE_TIKTOK, this);
    }
    @Override
    @Transactional
    public void addParcelToCombine(Staff staff, List<Trade> tradesList, Long taobaoId) {
        for (Trade trade : tradesList) {
            if (trade.getTradeExt() == null) {
                throw new RuntimeException("存在订单没有附表信息");
            }
            if (trade.getTradeExt().getCombineParcelId() != null && trade.getTradeExt().getCombineParcelId() != 0) {
                throw new RuntimeException("存在已上传的订单");
            }
        }
        Map<Long, User> userIdMap = staff.getUserIdMap();
        //key=平台-系统发货仓库-中转仓库
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : tradesList) {
            TradeExt tradeExt = trade.getTradeExt();
            User user = Optional.ofNullable(userIdMap.get(trade.getUserId())).orElse(userService.queryById(trade.getUserId()));
            String key = user.getNick() + "_" + trade.getWarehouseId() + "_" + tradeExt.getDeliveryType();
            tradeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(trade);
        }

        for (Map.Entry<String, List<Trade>> entry : tradeMap.entrySet()) {
            List<Trade> tempTrades = entry.getValue();
            Map<String, Object> combineParcelCondition = new HashMap<>();
            combineParcelCondition.put("source", tempTrades.get(0).getSource());
            combineParcelCondition.put("transferWarehouseAddress", entry.getKey());
            //根据来源，发货仓库和中转仓库查询大包列表
            List<TradeCombineParcel> allCombineParcel = tradeCombineParcelDAO.queryValidParcel(staff, combineParcelCondition);
            List<TradeCombineParcel> combineParcels = allCombineParcel.stream().filter(e -> StringUtils.isEmpty(e.getPlatformBatchNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(combineParcels)) {
                //如果没有查询到列表，则创建一个新的大包，将同一个key的trade放到一个大包中
                createNewCombineParcel(staff, tempTrades, entry.getKey());
            } else {
                addToOldCombineParcel(staff, tempTrades, combineParcels.get(0));
            }
        }
    }

    private void addToOldCombineParcel(Staff staff, List<Trade> trades, TradeCombineParcel combineParcel) {
        List<TradeParcel> oldTradeParcels = tradeParcelDAO.queryBySidsAndCombineParcelId(staff, trades.stream().map(Trade::getSid).toArray(Long[]::new), combineParcel.getId());
        if (CollectionUtils.isNotEmpty(oldTradeParcels)) {
            StringBuilder builder = new StringBuilder();
            for (TradeParcel oldTradeParcel : oldTradeParcels) {
                builder.append(oldTradeParcel.getSid()).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
            throw new RuntimeException("[" + builder.toString() + "]对应的小包已存在");
        }
        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcel.getId());
        tradeParcelDAO.batchInsert(staff, parcels);
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcel.getId());
            //添加系统日志
            String action = "移入:";
            String content = "tiktok首公里预报移入大包：" + combineParcel.getId();
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        TradeCombineParcel increase = new TradeCombineParcel();
        increase.setId(combineParcel.getId());
        increase.setParcelNum(combineParcel.getParcelNum() + parcels.size());
        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(increase));
        if (TradeCombineParcel.UPLOAD_STATUS_UPLOADED == combineParcel.getUploadStatus()) {
            TradeCombineParcel update = new TradeCombineParcel();
            update.setId(combineParcel.getId());
            update.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED);
            tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(update));
        }
    }

    private void createNewCombineParcel(Staff staff, List<Trade> trades, String key) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseAddress(key);
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
        combineParcel.setTradeKey(key);
        tradeCombineParcelDAO.insert(staff, combineParcel);

        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);

        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            String content = "tiktok首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);
    }


    /**
     * 生成系统日志
     *
     * @param staff
     * @param Sid
     * @param action
     * @param content
     */
    private void tradeTrack(Staff staff, Long Sid, String action, String content) {
        /**
         * 生成 tradeTrace
         */
        List<TradeTrace> tradeTraces = new ArrayList<>();
        TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), Sid, action, staff.getName(), new Date(), content);
        tradeTraces.add(tradeTrace);
        /**
         * 落库
         */
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    private List<TradeParcel> buildParcelsCreate(List<Trade> trades, Long combineParcelId) {
        return trades.stream().map(x -> {
            TradeParcel parcel = new TradeParcel();
            parcel.setCombineParcelId(combineParcelId);
            parcel.setTaobaoId(x.getTaobaoId());
            parcel.setSid(x.getSid());
            parcel.setTid(x.getTid());
            parcel.setOutSid(x.getOutSid());
            parcel.setWeight(x.getWeight());
            parcel.setNetWeight(x.getNetWeight());
            parcel.setVolume(x.getVolume());
            parcel.setUploadStatus(TradeParcel.UPLOAD_STATUS_TO_UPLOAD);
            parcel.setLPOrderCode(x.getTradeExt().getPackageNumber());
            return parcel;
        }).collect(Collectors.toList());
    }

    @Override
    public UploadTradeParcelResult uploadCombineParcel(Staff staff, TradeCombineParcel param, TradeCombineParcel combineParcel) throws Exception {
        User user = userService.queryByTaobaoId(staff.getCompanyId(), combineParcel.getTaobaoId());
        if ((TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD != combineParcel.getStatus() && TradeCombineParcel.UPLOAD_STATUS_PART_UPLOADED != combineParcel.getStatus())) {
            throw new RuntimeException("没有可用的大包");
        }
        combineParcel.setTemplateId(param.getTemplateId());
        combineParcel.setTrackingNo(param.getTrackingNo());
        combineParcel.setGatherType(param.getGatherType());
        //1.查询指定大包里的所有小包
        List<TradeParcel> parcels = tradeParcelDAO.queryByCombineParcelIds(
                staff, new Long[]{combineParcel.getId()}, null);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("当前大包中没有小包");
        }
        //2.查询仓库地址
        Warehouse warehouse = warehouseService.queryById(combineParcel.getConsignWarehouseId());
        UploadTradeParcelResult result = new UploadTradeParcelResult();
        //3.提交上传操作
        TradeCombineParcel combineParcelCommint = bigbagCommint(staff, combineParcel, user, tiktokFmService, result, warehouse, parcels);
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            updateParcelStatus(staff, result.getSuccessList(), TradeParcel.UPLOAD_STATUS_UPLOADED);
        }
        return result;

    }

    /**
     * sumaitong大包上传平台或更新
     *
     * @param staff
     * @param combineParcel
     * @param user
     * @param tiktokFmService
     * @param result
     * @param warehouse
     */
    private TradeCombineParcel bigbagCommint(Staff staff, TradeCombineParcel combineParcel,
                                             User user, ITiktokFmService tiktokFmService, UploadTradeParcelResult result,
                                             Warehouse warehouse, List<TradeParcel> parcels) {
        List<TradeParcel> successList = new ArrayList<>();
        List<TradeParcel> failList = new ArrayList<>(1);
        TradeCombineParcel combineParcelCommint = new TradeCombineParcel();
        combineParcelCommint.setGatherType(combineParcel.getGatherType());
        combineParcelCommint.setId(combineParcel.getId());
        combineParcelCommint.setTaobaoId(combineParcel.getTaobaoId());

        Map<String, TradeParcel> tradeParcelMap = parcels.stream().collect(Collectors.toMap(TradeParcel::getTid, Function.identity(), (v1, v2) -> v1));

        GenerateTiktokFmRequest generateTiktokFmRequest = buildCommintRequest(staff, combineParcel, warehouse, parcels);
        GenerateTiktokFmResponse commitResponse = tiktokFmService.generateFMTrackingNo(user, generateTiktokFmRequest);

        if (commitResponse == null) {
            throw new RuntimeException("组包上传失败，平台未响应返回值");
        }

        if (CollectionUtils.isNotEmpty(commitResponse.getErrors())) {
            for (ErrorDetail error : commitResponse.getErrors()) {
                Map<String, String> detail = error.getDetail();
                TradeParcel tradeParcel = tradeParcelMap.get(detail.get("order_id"));
                tradeParcel.setReason(error.getMessage());
                failList.add(tradeParcel);
                tradeParcelMap.remove(detail.get("order_id"));
            }
        }

        if (StringUtils.isNotBlank(commitResponse.getUrl())) {
            //新增大包成功，更改大包状态，
            combineParcelCommint.setPlatformBatchNo(commitResponse.getFirstMileBundleId());
            combineParcelCommint.setPlatformContentId(String.valueOf(commitResponse.getFirstMileBundleId()));
            combineParcelCommint.setTrackingNo(combineParcel.getTrackingNo());
            combineParcelCommint.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_UPLOADED);
            combineParcelCommint.setStatus(TradeCombineParcel.STATUS_OUTBOUNDED);
            FileResult upload = fileUploadService.upload(commitResponse.getUrl());
            if (upload == null) {
                throw new RuntimeException("上传文件失败");
            }
            combineParcelCommint.setPlatformUrl(upload.getUrl());
            for (Map.Entry<String, TradeParcel> tradeParcelEntry : tradeParcelMap.entrySet()) {
                successList.add(tradeParcelEntry.getValue());
            }
        }


        tradeCombineParcelDAO.batchUpdate(staff, Collections.singletonList(combineParcelCommint));
        result.setSuccessList(successList);
        result.setFailList(failList);
        //打印组包上传日志
        printPlatformBatchNo(successList, failList, staff, combineParcelCommint);
        return combineParcelCommint;
    }

    private GenerateTiktokFmRequest buildCommintRequest(Staff staff, TradeCombineParcel combineParcel, Warehouse warehouse, List<TradeParcel> parcels) {

        GenerateTiktokFmRequest request = new GenerateTiktokFmRequest();
        if (combineParcel.getGatherType().equals(TradeCombineParcel.STATUS_TO_OUTBOUND)) {
            request.setHandoverMethod("PICKUP");
        } else {
            request.setHandoverMethod("DROP_OFF");
        }
        List<String> tidList = parcels.stream().map(TradeParcel::getTid).collect(Collectors.toList());
        request.setOrderIds(tidList);
        // 自寄
        if (Objects.equals(TradeCombineParcel.STATUS_OUTBOUNDED, combineParcel.getGatherType())) {
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQueryWithCache(staff, combineParcel.getTemplateId(), false);
            request.setPhoneTailNumber(getLastFourChars(warehouse.getContactPhone()));
            request.setShippingProviderId(String.valueOf(TiktokLogisticsProvider.getIdByName(userWlbExpressTemplate.getExpressName())));
            request.setTrackingNumber(combineParcel.getTrackingNo());
        }
        return request;
    }

    public static String getLastFourChars(String str) {
        if (str == null || str.length() < 4) {
            return str; // 或者返回空字符串，根据需求决定
        }
        return str.substring(str.length() - 4);
    }
    private void updateParcelStatus(Staff staff, List<TradeParcel> parcels, int uploadStatus) {
        List<TradeParcel> parcelUpdates = parcels.stream().map(x -> {
            TradeParcel parcelUpdate = new TradeParcel();
            parcelUpdate.setId(x.getId());
            parcelUpdate.setUploadStatus(uploadStatus);
            return parcelUpdate;
        }).collect(Collectors.toList());

        tradeParcelDAO.batchUpdate(staff, parcelUpdates);
    }

    /**
     * 组包上传打印日志，记录揽货批次号
     *
     * @param successList        上传成功的订单
     * @param failList           上传失败的订单
     * @param tradeCombineParcel 组包信息
     * <AUTHOR>
     * @date 2022/1/16 下午2:42
     */
    void printPlatformBatchNo(List<TradeParcel> successList, List<TradeParcel> failList, Staff staff, TradeCombineParcel tradeCombineParcel) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<Long> failSid = new ArrayList<>();
        //添加上传失败的订单日志
        for (TradeParcel tradeParcel : failList) {
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), tradeParcel.getSid(), "组包上传", staff.getName(), new Date(), "组包上传：组包上传失败，失败原因：" + tradeParcel.getReason());
            tradeTraces.add(tradeTrace);
            failSid.add(tradeParcel.getSid());
        }
        //添加上传成功的订单日志
        successList.stream().filter(item -> !failSid.contains(item.getSid())).forEach(item -> {
            String content;
            if (TradeCombineParcel.EnumGatherType.CAINIAO_PICKUP.getType().equals(tradeCombineParcel.getGatherType())) {
                //上门揽收
                content = "组包上传：组包上传成功（上门揽收），揽货批次号：" + tradeCombineParcel.getPlatformBatchNo();
            } else {
                //快递寄送
                content = "组包上传：组包上传成功（快递寄送）";
            }
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), item.getSid(), "组包上传", staff.getName(), new Date(), content);
            tradeTraces.add(tradeTrace);
        });
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return;
        }
        try {
            //入库
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "入库组包上传日志时抛出异常"));
        }
    }

    private void updateCombineStatus(Staff staff, List<TradeCombineParcel> filterCombineParcels, int status) {
        List<TradeCombineParcel> combineParcelUpdates = filterCombineParcels.stream().map(x -> {
            TradeCombineParcel combineParcelUpdate = new TradeCombineParcel();
            combineParcelUpdate.setId(x.getId());
            combineParcelUpdate.setStatus(status);
            return combineParcelUpdate;
        }).collect(Collectors.toList());
        tradeCombineParcelDAO.batchUpdate(staff, combineParcelUpdates);
    }

    @Transactional
    @Override
    public void cancelCombineParcel(Staff staff, TradeCombineParcel parcel) {
        //订单状态为已出库的，不能进行取消操作
        List<TradeParcel> parcelList = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{parcel.getId()}, null);
        //判断如果当前大包中未存在小包，则直接置为取消状态
        if (parcel.getParcelNum() == 0) {
            updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
        }
        // 待上传组包，移出组包
        if (CollectionUtils.isNotEmpty(parcelList)) {
            //将小包移出大包
            removeParcel(staff, parcel.getId(), parcelList.stream().map(TradeParcel::getId).toArray(Long[]::new));
        }
        //更改大包状态为已取消
        updateCombineStatus(staff, Collections.singletonList(parcel), TradeCombineParcel.STATUS_CANCELLED);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeParcel(Staff staff, Long combineParcelId, Long[] parcelIds) {
        if (combineParcelId == null || parcelIds == null || parcelIds.length == 0) {
            throw new RuntimeException("缺少参数");
        }
        List<TradeParcel> parcels = tradeParcelDAO.queryByIds(staff, parcelIds);
        if (CollectionUtils.isEmpty(parcels)) {
            throw new RuntimeException("查出的小包为空");
        }
        tradeParcelDAO.deleteByIds(staff, parcels.stream().map(TradeParcel::getId).toArray(Long[]::new));
        tradeParcelDAO.deleteByCombineParcelId(staff, 0L);

        List<TradeParcel> tradeParcels = tradeParcelDAO.queryByCombineParcelIds(staff, new Long[]{combineParcelId}, TradeParcel.UPLOAD_STATUS_TO_UPLOAD);

        TradeCombineParcel decrease = new TradeCombineParcel();
        decrease.setId(combineParcelId);
        decrease.setParcelNum(tradeParcels.size());

        tradeCombineParcelDAO.batchIncrease(staff, Collections.singletonList(decrease));
        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(0L);
            return tradeExt;
        }).collect(Collectors.toList());

        tradeExtDao.batchUpdate(staff, updateExts);
        parcels.stream().forEach(e -> {
            //添加系统日志
            String action = "移出:";
            String content = "tiktok首公里预报移出大包：" + e.getCombineParcelId();
            tradeTrack(staff, e.getSid(), action, content);
        });

    }

    @Override
    CancelUploadTradeParcelResult cancelUploadParcel(Staff staff, Long taobaoId, Long combineParcelId, Long[] parcelIds) throws Exception {
        throw new RuntimeException("tiktok不支持小包取消上传");
    }

    @Override
    public TradeCombineParcelResponse getPlatformBatchNoPrintData(TradeCombineParcel tradeCombineParcel, Map<Long, Warehouse> warehouseMap, Staff staff, String subbagId) {
        TradeCombineParcelResponse response = new TradeCombineParcelResponse();
        response.setPlatformBatchNo(tradeCombineParcel.getPlatformBatchNo());
        response.setPdfUrlList(Collections.singletonList(tradeCombineParcel.getPlatformUrl()));
        return response;
    }


    @Override
    @Transactional
    public void addParcelToCombineAndSplit(Staff staff, List<Trade> tradeList, TradeGroupParcelRequest request) {
        Map<Long, User> userIdMap = staff.getUserIdMap();
        // key 店铺账号（相同卖家）、系统仓库、物流渠道、发货方式相同，且店铺站点可合并的订单分成一组
        Map<String, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : tradeList) {
            User user = Optional.ofNullable(userIdMap.get(trade.getUserId())).orElse(userService.queryById(trade.getUserId()));
            if (Objects.equals(request.getTiktokNick(), user.getNick())) {
                String key = user.getNick() + "_" + trade.getWarehouseId() + "_" + request.getGatherType();
                tradeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(trade);
            }
        }
        if (tradeMap.isEmpty()) {
            throw new RuntimeException("没有能移入组包的数据！");
        }
        for (Map.Entry<String, List<Trade>> trade : tradeMap.entrySet()) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("source", CommonConstants.PLAT_FORM_TYPE_TIKTOK);
            condition.put("transferWarehouseAddress", trade.getKey());
            List<TradeCombineParcel> allCombineParcel = tradeCombineParcelDAO.queryValidParcel(staff, condition);
            if (CollectionUtils.isNotEmpty(allCombineParcel)) {
                addToOldCombineParcel(staff, trade.getValue(), allCombineParcel.get(0));
            } else {
                createNewCombineParcel(staff, trade.getValue(), request, trade.getKey());
            }
        }
    }



    private void createNewCombineParcel(Staff staff, List<Trade> trades, TradeGroupParcelRequest request,String key) {
        TradeCombineParcel combineParcel = new TradeCombineParcel();
        Long combineParcelId = IdWorkerFactory.getIdWorker().nextId();
        combineParcel.setId(combineParcelId);
        if (request != null && request.getTaobaoId() != null) {
            combineParcel.setTaobaoId(request.getTaobaoId());
        } else {
            combineParcel.setTaobaoId(trades.get(0).getTaobaoId());
        }
        combineParcel.setSource(trades.get(0).getSource());
        combineParcel.setConsignWarehouseId(trades.get(0).getWarehouseId());
        combineParcel.setConsignWarehouseName(trades.get(0).getWarehouseName());
        combineParcel.setTransferWarehouseAddress(key);
        combineParcel.setParcelNum(trades.size());
        combineParcel.setWeight(trades.stream().mapToDouble(Trade::getWeight).sum());
        combineParcel.setNetWeight(trades.stream().mapToDouble(Trade::getNetWeight).sum());
        combineParcel.setVolume(trades.stream().mapToDouble(Trade::getVolume).sum());
        combineParcel.setStatus(TradeCombineParcel.STATUS_TO_OUTBOUND);
        combineParcel.setUploadStatus(TradeCombineParcel.UPLOAD_STATUS_TO_UPLOAD);
        combineParcel.setAppointmentType(request.getAppointmentType());
        combineParcel.setTrackingNo(request.getTrackingNo());
        combineParcel.setTemplateId(request.getTemplateId());
        combineParcel.setTemplateName(request.getTemplateName());
        combineParcel.setGatherType(request.getGatherType());
        tradeCombineParcelDAO.insert(staff, combineParcel);

        List<TradeParcel> parcels = buildParcelsCreate(trades, combineParcelId);

        tradeParcelDAO.batchInsert(staff, parcels);

        List<TradeExt> updateExts = parcels.stream().map(x -> {
            TradeExt tradeExt = new TradeExt();
            tradeExt.setSid(x.getSid());
            tradeExt.setCombineParcelId(combineParcelId);
            //添加系统日志
            String action = "移入:";
            String content = "tiktok首公里预报移入大包：" + combineParcelId;
            tradeTrack(staff, x.getSid(), action, content);

            return tradeExt;
        }).collect(Collectors.toList());
        tradeExtDao.batchUpdate(staff, updateExts);

    }
}

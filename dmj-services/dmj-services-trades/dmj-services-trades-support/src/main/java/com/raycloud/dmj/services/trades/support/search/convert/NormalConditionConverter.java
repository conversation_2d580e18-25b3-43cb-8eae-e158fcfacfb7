package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.trades.support.search.TradeSearchSupport;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

import static com.raycloud.dmj.services.trades.support.search.convert.ConvertBase.*;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
@Order(ConditionConstants.CONVERTER_SORT_NORMAL)
public class NormalConditionConverter extends AbsConditionConverter{

    @Resource
    TradeSearchSupport tradeSearchSupport;


    @Override
    protected boolean isNeedConvert(Staff staff, ConvertContext context, TradeQueryRequest request, Query query) {
        return true;
    }

    @Override
    void doConvert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {
        if (isTrue(condition.getContainsMerged())) {
            andListCondition(q,"t.enable_status", Arrays.asList(1, 2));
        }else {
            andSingleCondition(q,"t.enable_status", 1);
        }

        if (notNull(condition.getIsCancel())) {
            andSingleCondition(q,"t.is_cancel", condition.getIsCancel());
        }else {
            if (!isTrue(condition.getContainsCanceled())) {
                andSingleCondition(q,"t.is_cancel", 0);
            }
        }

        andListCondition(q,"t.sid", condition.getSids());

        andListCondition(q,"t.source", condition.getSources());

        andListCondition(q,"t.source_id", condition.getDistributorIds());
        andListCondition(q,"t.taobao_id", condition.getDistributorUserIds());

        if (isTrue(condition.getSysConsigned())) {
            andSingleCondition(q,"t.sys_consigned", condition.getSysConsigned() ? 1:2);
        }

        if (condition.getTimeType() != null) {
            andDateRangeQuery(q,"t."+ condition.getTimeType().getField(), condition.getStartTime(),condition.getEndTime());
            tradeSearchSupport.addUpdTimeRef(staff,q,condition.getTimeType().getField(),condition.getStartTime(),condition.getEndTime(),condition.getSysStatus());
        }

        if (condition.getMaxSid() != null) {
            q.append(" AND ").append("t.sid").append(" < ? ").add(condition.getMaxSid());
        }

        if (condition.getMinSid() != null) {
            q.append(" AND ").append("t.sid").append(" > ? ").add(condition.getMinSid());
        }

        andSingleCondition(q,"t.is_excep", condition.getIsExcep());

        if (condition.getIsPresell() != null) {
            if (condition.getIsPresell() == 1) {
                q.append(" AND ").append("(").append("t.is_presell = ").append(condition.getIsPresell());
                q.append(" OR ").append("t.is_presell = ").append("3").append(")");
            } else if (condition.getIsPresell() == 2) {
                q.append(" AND ").append("t.is_presell = ").append(condition.getIsPresell());
            } else if (condition.getIsPresell() == 0) {
                q.append(" AND ").append("(").append("t.is_presell <> 1");
                q.append(" AND ").append("t.is_presell <> 3").append(")");
            } else if (condition.getIsPresell() == 3) {
                q.append(" AND ").append("(").append("t.is_presell = ").append("1");
                q.append(" OR ").append("t.is_presell = ").append("3").append(")");
            }
        }

        if (null != condition.getContainExpress()) {
            q.append(" AND ").append(condition.getContainExpress() ? "t.template_id > 0" : "t.template_id = -1");
        }

        if (condition.getIsOutstock() != null) {
            if (condition.getIsOutstock() == 0) {
                q.append(" AND ").append("`type` NOT LIKE 'trade_out%'");
            } else if (condition.getIsOutstock() - 1 == 0) {
                q.append(" AND ").append("`type` LIKE 'trade_out%'");
            }
        }

        if (notNull(condition.getExpressStatus())) {
            if (condition.getExpressStatus() == 0) {
                q.append(" AND ").append(" t.express_print_time <= ?").add(TradeConstants.INIT_DATE);
            } else if (condition.getExpressStatus() == 1) {
                q.append(" AND ").append(" t.express_print_time > ?").add(TradeConstants.INIT_DATE);
            }
        }

        andSingleCondition(q,"t.is_halt", condition.getIsHalt());

    }
}

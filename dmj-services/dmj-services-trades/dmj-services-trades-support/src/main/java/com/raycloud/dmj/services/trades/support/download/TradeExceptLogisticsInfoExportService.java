package com.raycloud.dmj.services.trades.support.download;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.LogisticsTrackingPollPoolQueryParams;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.trades.ISysTradeService;

public class TradeExceptLogisticsInfoExportService implements IDownloadCenterCallback {

    private Staff staff;
    private LogisticsTrackingPollPoolQueryParams params;
    private String sids;
    private ISysTradeService sysTradeService;;

    public TradeExceptLogisticsInfoExportService(Staff staff, LogisticsTrackingPollPoolQueryParams params, String sids, ISysTradeService sysTradeService) {
        this.staff = staff;
        this.params = params;
        this.sids = sids;
        this.sysTradeService = sysTradeService;
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult();
        result.setFlag(false);

        String[][] exceptLogisticsExcelContent = sysTradeService.exportExceptLogistics(staff, params, sids);

        result.setData(exceptLogisticsExcelContent);
        return result;
    }


}

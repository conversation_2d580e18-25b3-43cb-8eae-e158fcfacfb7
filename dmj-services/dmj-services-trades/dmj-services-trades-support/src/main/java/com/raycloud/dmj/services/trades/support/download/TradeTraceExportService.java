package com.raycloud.dmj.services.trades.support.download;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TimeSlice;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.trades.TradeTraceQueryParams;
import com.raycloud.dmj.domain.trades.utils.DateSplitUtils;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.download.domain.DownloadParam;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TradeTraceExportService implements IDownloadCenterCallback {

    private final Staff staff;
    private final TradeTraceQueryParams params;
    private final ITradeTraceService tradeTraceService;
    private List<TimeSlice> timeSlices;

    public TradeTraceExportService(Staff staff, TradeTraceQueryParams params, ITradeTraceService tradeTraceService) {
        this.staff = staff;
        this.params = params;
        this.tradeTraceService = tradeTraceService;
        initTimeSlices(params);
    }

    private void initTimeSlices(TradeTraceQueryParams params){
        if (needSplit(params)) {
            this.timeSlices = DateSplitUtils.dateSplit(params.getOperateTimeStart(), params.getOperateTimeEnd(), 24);
        }
    }

    @Override
    public DownloadResult callback(DownloadParam downloadParam) throws Exception {
        DownloadResult result = new DownloadResult();
        result.setFlag(true);
        params.setPage(downloadParam.getPage());
        String[][] tradeTrace = tradeTraceExport(staff, params);
        result.setData(tradeTrace);
        return result;
    }

    private String[][] tradeTraceExport(Staff staff, TradeTraceQueryParams params) {
        List<TradeTrace> tradeTraces = queryTradeTrace(staff, params);
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return null;
        }
        List<String[]> resultList = new ArrayList<>();
        for (TradeTrace trace : tradeTraces) {
            List<String> row = Lists.newArrayListWithCapacity(5);
            row.add(String.valueOf(trace.getSid()));
            row.add(trace.getAction());
            row.add(trace.getContent());
            row.add(DateUtils.format(trace.getOperateTime(), "yyyy-MM-dd HH:mm:ss"));
            row.add(trace.getOperator());
            resultList.add(row.toArray(new String[5]));
        }
        return resultList.toArray(new String[resultList.size()][]);
    }

    private List<TradeTrace> queryTradeTrace(Staff staff, TradeTraceQueryParams params) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        if (CollectionUtils.isEmpty(timeSlices)){
            tradeTraces = tradeTraceService.getTradeTraceByCondition(staff, params, params.getPage());
        }else {
            //记录切片前时间，查询完成会还原回去，用于下一次查询
            Long operateTimeStart = params.getOperateTimeStart();
            Long operateTimeEnd = params.getOperateTimeEnd();
            for (TimeSlice timeSlice : timeSlices) {
                params.setOperateTimeStart(timeSlice.getStart().getTime());
                params.setOperateTimeEnd(timeSlice.getEnd().getTime());
                List<TradeTrace> temp = tradeTraceService.getTradeTraceByCondition(staff, params, params.getPage());
                if (!CollectionUtils.isEmpty(temp)) {
                    tradeTraces.addAll(temp);
                }
            }
            params.setOperateTimeStart(operateTimeStart);
            params.setOperateTimeEnd(operateTimeEnd);
        }
        return tradeTraces;
    }

    /**
     * 是否需要拆分
     *
     * @param params
     * @return
     */
    boolean needSplit(TradeTraceQueryParams params) {
        if (params == null) {
            return false;
        }
        if (params.getOperateTimeStart() == null || params.getOperateTimeEnd() == null) {
            return false;
        }
        return !org.apache.commons.lang3.time.DateUtils.isSameDay(new Date(params.getOperateTimeStart()), new Date(params.getOperateTimeEnd()));
    }
}

package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.params.OrderAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeFillEnum;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import org.springframework.stereotype.Component;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-17
 */
@Component
public class TagUpdQueryer extends TradeAssembleStrategy{



    @Override
    public SenceCodeEnum supportScene() {
        return SenceCodeEnum.ADD_COVER_TAG;
    }


    @Override
    TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long... sids) {
        TradeAssembleParams params = TradeAssembleParams.justTrade()
                .setQueryMerge(true).setMergeStyle(TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB)
                .setQueryOrder(true).setOrderAssembleParams(OrderAssembleParams.justOrder())
                .setFill(true)
                .addIncludeFills(TradeFillEnum.TRADE_TAG_FILL)
                .addIncludeFills(TradeFillEnum.ORDER_EXCEPT_FILL)
                .addIncludeFills(TradeFillEnum.TRADE_EXCEPT_FILL).setFilter(true);
        return params;
    }
}

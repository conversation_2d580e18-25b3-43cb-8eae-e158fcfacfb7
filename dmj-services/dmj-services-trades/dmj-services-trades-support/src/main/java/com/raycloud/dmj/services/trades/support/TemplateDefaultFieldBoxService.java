package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.TemplateDefaultFieldBoxDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TemplateDefaultFieldBox;
import com.raycloud.dmj.domain.trades.TemplateDefaultFieldBoxQuery;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.support.utils.ConfigLogUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * TemplateDefaultFieldBox  Service
 *
 * <AUTHOR>
 * @since 2019-07-25
 */
@Service
public class TemplateDefaultFieldBoxService {

    @Resource
    TemplateDefaultFieldBoxDAO templateDefaultFieldBoxDAO;
    @Resource
    IOpLogService opLogService;

    /**
     * 保存
     */
    public Integer saveTemplateDefaultFieldBox(Staff staff, TemplateDefaultFieldBox templateDefaultFieldBox) {
        if (templateDefaultFieldBox.getId() != null) {
            TemplateDefaultFieldBox oldTemplateDefaultFieldBox = getTemplateDefaultFieldBoxByKey(staff, templateDefaultFieldBox.getId());
            if (oldTemplateDefaultFieldBox != null) {
                updateTemplateDefaultFieldBoxByKey(staff, templateDefaultFieldBox);
                saveChangeLog(staff, oldTemplateDefaultFieldBox, templateDefaultFieldBox);
            } else {
                throw new IllegalArgumentException("根据id没有查到数据!请传入正确的id!!");
            }
        } else {
            addTemplateDefaultFieldBox(staff, templateDefaultFieldBox);
            ConfigLogUtils.addUpdateLog(staff, "新增纯自定义内容:" + templateDefaultFieldBox.getFieldBoxBefore() + templateDefaultFieldBox.getFieldBoxAfter(), "purchase.print.Config" +
                    ".update", opLogService);
        }
        return 1;
    }

    private void saveChangeLog(Staff staff, TemplateDefaultFieldBox oldTemplateDefaultFieldBox, TemplateDefaultFieldBox newTemplateDefaultFieldBox) {
        StringBuilder buf = new StringBuilder();
        ConfigLogUtils.addLogContent(buf, "逻辑删除标志", newTemplateDefaultFieldBox.getEnableStatus(), oldTemplateDefaultFieldBox.getEnableStatus());
        ConfigLogUtils.addLogContent(buf, "前文字", newTemplateDefaultFieldBox.getFieldBoxBefore(), oldTemplateDefaultFieldBox.getFieldBoxBefore());
        ConfigLogUtils.addLogContent(buf, "后文字", newTemplateDefaultFieldBox.getFieldBoxBefore(), oldTemplateDefaultFieldBox.getFieldBoxBefore());
        if (buf.length() > 0) {
            ConfigLogUtils.addUpdateLog(staff, buf.toString(), "purchase.print.Config.update", opLogService);
        }
    }

    /**
     * 插入
     */
    public Integer addTemplateDefaultFieldBox(Staff staff, TemplateDefaultFieldBox templateDefaultFieldBox) {
        return templateDefaultFieldBoxDAO.addTemplateDefaultFieldBox(staff, templateDefaultFieldBox);
    }

    /**
     * 根据company查找
     */
    public List<TemplateDefaultFieldBox> getTemplateDefaultFieldBoxByCompany(Staff staff) {
        return templateDefaultFieldBoxDAO.getTemplateDefaultFieldBoxByCompany(staff);
    }

    /**
     * 根据主键查找
     */
    public TemplateDefaultFieldBox getTemplateDefaultFieldBoxByKey(Staff staff, Long id) {
        return templateDefaultFieldBoxDAO.getTemplateDefaultFieldBoxByKey(staff, id);
    }

    /**
     * 根据主键组进行查找
     */
    public List<TemplateDefaultFieldBox> getTemplateDefaultFieldBoxByKeys(Staff staff, List<Long> idList) {
        return templateDefaultFieldBoxDAO.getTemplateDefaultFieldBoxByKeys(staff, idList);
    }

    /**
     * 根据主键删除
     */
    public void deleteByKey(Staff staff, Long id) {
        templateDefaultFieldBoxDAO.deleteByKey(staff, id);
        ConfigLogUtils.addUpdateLog(staff, "删除自定义文本框:id" + id, "purchase.print.Config.update", opLogService);
    }

    /**
     * 根据主键组进行删除
     */
    public Integer deleteByKeys(Staff staff, List<Long> idList) {
        return templateDefaultFieldBoxDAO.deleteByKeys(staff, idList);
    }

    /**
     * 根据主键更新
     */
    public Integer updateTemplateDefaultFieldBoxByKey(Staff staff, TemplateDefaultFieldBox templateDefaultFieldBox) {
        return templateDefaultFieldBoxDAO.updateTemplateDefaultFieldBoxByKey(staff, templateDefaultFieldBox);
    }

    /**
     * 根据条件查询
     */
    public List<TemplateDefaultFieldBox> getTemplateDefaultFieldBoxList(Staff staff, TemplateDefaultFieldBoxQuery templateDefaultFieldBoxQuery) {
        return templateDefaultFieldBoxDAO.getTemplateDefaultFieldBoxList(staff, templateDefaultFieldBoxQuery);
    }

}

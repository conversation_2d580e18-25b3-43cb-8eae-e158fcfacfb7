package com.raycloud.dmj.services.trades.support.stock;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.stock.OrderKeepInsufficientBusiness;
import com.raycloud.dmj.business.stock.StockOrderFillPropBusiness;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.trade.StockTradeHelpBusiness;
import com.raycloud.dmj.business.trade.StockTradeRouteBusiness;
import com.raycloud.dmj.business.trade.TradeStockData;
import com.raycloud.dmj.business.trade.VirtualWarehouseStockSwapBusiness;
import com.raycloud.dmj.business.warehouse.TradeWarehouseBusiness;
import com.raycloud.dmj.business.wms.TradeWmsBusiness;
import com.raycloud.dmj.domain.TradeStockContent;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.GlobalSwitchConfig;
import com.raycloud.dmj.domain.diamond.GlobalTradeConfig;
import com.raycloud.dmj.domain.item.ItemIdInfo;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.warehouse.TradeWarehouseContext;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.services.trade.common.ITradeBusinessQuery;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.domain.trades.TradeImportResult;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.stock.IStockTradeRouteService;
import com.raycloud.dmj.utils.StockCommonUtils;
import com.raycloud.dmj.utils.wms.DataUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.EXCEPTION_NOT_LOCK_STOCK;

/**
 * Created by CXW on 16/8/4.
 */
@Service
public class OrderStockService implements IOrderStockService {

    private Logger logger = Logger.getLogger(OrderStockService.class);

    @Resource
    private StockTradeRouteBusiness stockTradeRouteBusiness;

    @Resource
    private TradeWarehouseBusiness tradeWarehouseBusiness;

    @Resource
    private StockTradeHelpBusiness stockTradeHelpBusiness;

    @Resource
    private TradeWmsBusiness tradeWmsBusiness;

    @Resource
    private TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    private TradeConfigStockBusiness tradeConfigStockBusiness;

    @Resource
    private StockOrderFillPropBusiness stockOrderFillPropBusiness;

    @Autowired(required = false)
    IStockTradeRouteService stockTradeRouteServiceDubbo;

    @Resource
    private VirtualWarehouseStockSwapBusiness virtualWarehouseStockSwapBusiness;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    ITradeBusinessQuery tradeBusinessQuery;

    //==============================================库存申请=============================================================
    @Transactional
    @Override
    public List<Trade> applyTradeStockLocal(Staff staff, List<Trade> trades) {
        return applyTradeStockLocal(staff, trades, true);
    }

    @Transactional
    @Override
    public List<Trade> applyTradeStockLocal(Staff staff, List<Trade> trades, boolean check) {
        if (trades == null || trades.size() == 0) {
            return trades;
        }
        //分销订单不需要申请库存,默认库存满足
        handleFxTradesStockTrades(staff, trades);
        //过滤需要申请库存的子订单
        List<Order> applyOrders = new ArrayList<>();
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, EXCEPTION_NOT_LOCK_STOCK);
        for (Trade trade : trades) {
            /// 开启快递异常不锁库存单过滤
            if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) || (trade.getIsCancel() != null && trade.getIsCancel() == 1) || TradeUtils.isFxOrMixTrade(trade)
                    || UnattainableUilts.isUnattainableNotApplyStockTrade(staff, trade, tradeConfigNew)
            ) {
                continue;
            }
            OrderStockUtils.fillOrder(trade, applyOrders);
        }

        //申请库存
        applyOrderStockLocal(staff, applyOrders, check);

        //返回申请结果
        StringBuilder sb = new StringBuilder("申请库存结果,");
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Trade trade : trades) {
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
            sb.append("[sid=").append(trade.getSid()).append(",tid=").append(trade.getTid()).append(",stockStatus=").append(trade.getStockStatus()).append("],");
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, sb.toString()));
        }
        return trades;
    }

    @Transactional
    @Override
    public void applyOrderStockLocal(Staff staff, List<Order> orders) {
        if (orders != null && orders.size() > 0) {
            applyOrderStockLocal(staff, orders, true);
        }
    }


    @Transactional
    @Override
    public void applyOrderStockLocal(Staff staff, List<Order> orders, boolean check) {
        //这个方法后面加个入口的枚举，统一过滤记录日志好一些。
        handleFxTradesStockOrders(staff, orders);
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeStockData data = OrderStockUtils.filterApplyStockOrders(staff, orders, tradeLocalConfigurable, tradeConfig, check);
        if (data.requestRecords().size() < 1) {
            return;
        }
        try {
            String stockDubboOpenCompanyIds = Optional.ofNullable(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch()).map(GlobalSwitchConfig::getTrade).map(GlobalTradeConfig::getStockDubboOpenCompanyIds).orElse(null);
            if (stockTradeRouteServiceDubbo != null && StringUtils.isNotBlank(stockDubboOpenCompanyIds) && DataUtils.ids2List(stockDubboOpenCompanyIds).contains(staff.getCompanyId())) {
                TradeStockContent content = new TradeStockContent(orders, check);
                OrderStockUtils.handleApplyStock(staff, orders, stockTradeRouteServiceDubbo.apply(staff, content), OrderStockUtils.OPERATION_APPLY, tradeConfig);
            } else {
                OrderStockUtils.handleApplyStock(staff, orders, stockTradeRouteBusiness.apply(staff, data), OrderStockUtils.OPERATION_APPLY, tradeConfig);
            }
        } catch (Exception e) {
            //这里加表
            String fullStackTrace = ExceptionUtils.getFullStackTrace(e);
            logger.error(LogHelper.buildLog(staff, String.format("订单申请库存报错，tids=%s，e=%s", OrderUtils.toTidList(orders), fullStackTrace)), e);
            throw new TradeException("订单申请库存失败:" + fullStackTrace, e);
        }
    }
    //==============================================库存申请=============================================================


    //==============================================库存归还=============================================================
    @Transactional
    @Override
    public void resumeTradeStockLocal(Staff staff, List<Trade> trades) {
        if (trades == null || trades.size() == 0) {
            return;
        }
        List<Order> resumeOrders = new ArrayList<>();
        for (Trade trade : trades) {
            if (TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isPlatformFxTrade(trade) || TradeUtils.isAlibabaFxRoleTrade(trade)) {
                continue;
            }
            OrderStockUtils.fillOrder(trade, resumeOrders);
        }
        resumeOrderStockLocal(staff, resumeOrders, null);
    }

    @Transactional
    @Override
    public void resumeOrderStockLocal(Staff staff, List<Order> orders, TradeImportResult tir) {
        resumeOrderStockLocalNoWms(staff, orders, tir);
        tradeWmsBusiness.sendResume(staff, orders);
    }


    @Transactional
    @Override
    public void resumeOrderStockLocalNoWms(Staff staff, List<Order> orders, TradeImportResult tir) {
        if (orders == null || orders.size() == 0) {
            return;
        }
        tradeConfigStockBusiness.fillOrderWarehouseIds(staff, orders);
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        resumeOrderStockCommonLocal(staff, orders, tir);
    }

    @Override
    public void resumeOrderStockLocal4CancelConsign(Staff staff, List<Order> orders, TradeImportResult tir) {
        if (orders == null || orders.size() == 0) {
            return;
        }
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        TradeStockData data = new TradeStockData();
        data.addNotApplyRequests(staff, OrderUtils.toFullOrderList(orders, false));
        //取消发货归还库存标记，比较重要。
        data.setCancelConsignFlag(true);
        stockTradeRouteBusiness.resume(staff, data);
        //第三方仓库的事件
        List<Trade> needResumes = handle3rdWarehouse4Resume(staff, orders);
        if (!needResumes.isEmpty()) {
            if (tir == null) {
                Logs.ifDebug(LogHelper.buildLog(staff, "resumeOrderStockLocal4CancelConsign的TradeImportResult为空，不处理"));
            } else {
                tir.getResumes().addAll(needResumes);
            }
        }
        OrderStockUtils.handleResumeStock(staff, orders, tradeConfigService.get(staff));
        tradeWmsBusiness.sendResume(staff, orders);
    }

    private List<Trade> handle3rdWarehouse4Resume(Staff staff, List<Order> orders) {
        Map<Long, Trade> needResumeMap = new HashMap<>();
        Set<Long> sids = new HashSet<>();
        Map<Long, Warehouse> warehouseMap = new HashMap<>();
        for (Order order : orders) {
            if (tradeWarehouseBusiness.isPart3Warehouse(staff, order.getWarehouseId(), warehouseMap)
                    && !sids.contains(order.getSid())
                    && !OrderUtils.isFxOrMixOrder(order)
                    && !TradeStatusUtils.isWaitAudit(order.getOldSysStatus())) {
                sids.add(order.getSid());
                Trade trade = new TbTrade();
                trade.setWarehouseId(order.getWarehouseId());
                trade.setSid(order.getSid());
                trade.setTid(order.getTid());
                trade.setEnableStatus(1);
                needResumeMap.put(order.getSid(), trade);
            }
        }
        return new ArrayList<>(needResumeMap.values());
    }
    //==============================================库存归还=============================================================


    //==============================================库存消费=============================================================
    @Transactional
    @Override
    public void consumeOrders(Staff staff, List<Order> orders) {
        if (orders != null && orders.size() > 0) {
            tradeConfigStockBusiness.fillOrderWarehouseIds(staff, orders);
            //分销商品不需要申请库存,默认库存满足
            handleFxTradesStockOrders(staff, orders);
            TradeStockData data = new TradeStockData();
            data.addConsumeRequests(staff, OrderUtils.toFullOrderList(orders, false));
            stockTradeRouteBusiness.consume(staff, data);
        }
    }
    //==============================================库存消费=============================================================


    //==============================================库存校验=============================================================
    @Transactional
    @Override
    public List<Order> checkOrderStock(Staff staff, List<Order> orders) {
        //分销商品不需要申请库存,默认库存满足
        handleFxTradesStockOrders(staff, orders);
        // tradeConfigStockBusiness.fillOrderScalping(staff, orders);
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeStockData data = OrderStockUtils.filterUpdateStockOrders(staff, orders, tradeLocalConfigurable, false, tradeConfig);
        if (data.requestOrders().size() > 0) {
            OrderStockUtils.handleApplyStock(staff, orders, stockTradeHelpBusiness.checkOrders(staff, data), OrderStockUtils.OPERATION_CHECK, tradeConfig);
        }
        return orders;
    }

    @Transactional
    @Override
    public List<Order> checkWarehouseStock(Staff staff, List<Order> orders, Long warehouseId) {
        stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
        List<Order> stockOrders = assembleStockOrders(staff, orders, tradeConfigService.get(staff));
        if (!stockOrders.isEmpty()) {
            return checkWarehouse(staff, stockOrders, warehouseId);
        }
        return stockOrders;
    }

    public List<Order> checkWarehouse(Staff staff, List<Order> orders, Long warehouseId) {
        Set<String> itemKeys = new HashSet<>();
        List<ItemIdInfo> itemIdInfos = new ArrayList<>();
        for (Order order : orders) {
            String itemKey = TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId());
            if (!itemKeys.contains(itemKey)) {
                itemIdInfos.add(new ItemIdInfo(order.getItemSysId(), NumberUtils.nvlLong(order.getSkuSysId(), 0L)));
                itemKeys.add(itemKey);
            }
        }
        Map<String, FarERPStock> stockMap = new HashMap<>();
        List<FarERPStock> stocks = tradeBusinessQuery.queryFarERPStock(staff, itemIdInfos);
        if (CollectionUtils.isNotEmpty(stocks)) {
            stocks.forEach(stock -> stockMap.put(StockCommonUtils.getStockKey(stock.getWareHouseId(), stock.getSysItemId(), stock.getSysSkuId()), stock));
        }

        for (Order order : orders) {
            FarERPStock stock = stockMap.get(StockCommonUtils.getStockKey(warehouseId, order.getItemSysId(), order.getSkuSysId()));
            if (stock == null) {
                OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EXCEP);
            } else {
                if (staff.openAuditActiveStockRecord()) {
                    OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
                } else {
                    if (stock.getAvailableInStock() < order.getNum()) {
                        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_INSUFFICIENT);
                    } else {
                        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
                    }
                }
                stock.setAvailableInStock(stock.getAvailableInStock() - order.getNum());
            }
        }
        return orders;
    }

    /**
     * 将子订单按商品归类,相同的商品数量累加
     */
    private List<Order> assembleStockOrders(Staff staff, List<Order> orders, TradeConfig tradeConfig) {
        Map<String, Order> map = new HashMap<>(orders.size(), 1);
        for (Order order : orders) {
            if (OrderUtils.isFxOrMixOrder(order)) {
                continue;
            }
            boolean notApplyStock = TradeStockUtils.needNotApplyStock(staff, order, tradeConfig);
            if (order.isSuit(false) && CollectionUtils.isNotEmpty(order.getSuits())) {
                for (Order son : order.getSuits()) {
                    if (!son.isVirtual() && !son.ifNonConsign() && !notApplyStock && !OrderUtils.isFxOrMixOrder(order)) {
                        createAndAddStockOrder(staff, map, son);
                    }
                }
            } else {
                if (!order.isVirtual() && !order.ifNonConsign() && !notApplyStock && !OrderUtils.isFxOrMixOrder(order)) {
                    createAndAddStockOrder(staff, map, order);
                }
            }
        }
        return new ArrayList<>(map.values());
    }

    private void createAndAddStockOrder(Staff staff, Map<String, Order> map, Order order) {
        String key = buildKey(order.getItemSysId(), order.getSkuSysId());
        Order stockOrder = map.get(key);
        if (stockOrder != null) {
            stockOrder.setNum(stockOrder.getNum() + order.getNum());
            return;
        }
        map.put(key, TradeStockUtils.createStockOrder(staff, order));
    }

    private String buildKey(Long sysItemId, Long sysSkuId) {
        return sysItemId + "_" + (sysSkuId == null || sysSkuId < 0 ? 0 : sysSkuId);
    }

    //==============================================库存修改：换商品种类、换商品数量、换仓库等===============
    @Transactional
    @Override
    public List<Order> changeWarehouse(Staff staff, TradeWarehouseContext context, List<Order> orders) {
        return changeStock(staff, context, orders, OrderStockUtils.OPERATION_CHANGE_WAREHOUSE, false, false);
    }

    @Transactional
    @Override
    public List<Order> changeItemKind(Staff staff, List<Order> orders) {
        return changeStock(staff, null, orders, OrderStockUtils.OPERATION_CHANGE_ITEM_KIND, true, true);
    }

    @Transactional
    @Override
    public List<Order> changeItemNum(Staff staff, List<Order> orders) {
        return changeStock(staff, null, orders, OrderStockUtils.OPERATION_CHANGE_ITEM_NUM, false, true);
    }

    private List<Order> changeStock(Staff staff, TradeWarehouseContext context, List<Order> orders, String operation, boolean containVirtual, boolean isStockDubbo) {
        try {
            String stockDubboOpenCompanyIds = Optional.ofNullable(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch()).map(GlobalSwitchConfig::getTrade).map(GlobalTradeConfig::getStockDubboOpenCompanyIds).orElse(null);
            if (isStockDubbo && stockTradeRouteServiceDubbo != null && StringUtils.isNotBlank(stockDubboOpenCompanyIds) && DataUtils.ids2List(stockDubboOpenCompanyIds).contains(staff.getCompanyId())) {
                List<Order> changeStockOrders = stockTradeRouteServiceDubbo.changeStock(staff, orders, operation, containVirtual);
                buildUpdateOrders(changeStockOrders, orders);

            } else {
                OrderKeepInsufficientBusiness.keepInsufficientStockStatus(staff, orders);
                stockOrderFillPropBusiness.fillOrderExceptProp(staff, orders);
                TradeConfig tradeConfig = tradeConfigService.get(staff);
                TradeStockData data = OrderStockUtils.filterUpdateStockOrders(staff, orders, tradeLocalConfigurable, containVirtual, tradeConfig);
                if (context != null && context.isLaterAdjust()) {
                    data.laterAdjust = context.isLaterAdjust();
                }
                if (data.requestOrders().size() > 0) {
                    List<Order> norMalOrders = data.requestOrders().stream().filter(order -> !OrderUtils.isFxOrMixOrder(order)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(norMalOrders)) {
                        //先归还
                        stockTradeRouteBusiness.resume(staff, data);
                        //再申请
                        OrderStockUtils.handleApplyStock(staff, orders, stockTradeRouteBusiness.apply(staff, data), operation, !staff.openAuditActiveStockRecord(), tradeConfig);
                    }
                }
                if (context != null && context.isLaterAdjust() && CollectionUtils.isNotEmpty(data.updateStockKeys)) {
                    context.getUpdateStockKeys().addAll(data.updateStockKeys);
                }
            }
        } catch (Exception e) {
            //失败了。
            String fullStackTrace = ExceptionUtils.getFullStackTrace(e);
            logger.error(LogHelper.buildLog(staff, String.format("订单changeStock报错，tids=%s，e=%s", OrderUtils.toTidList(orders), fullStackTrace)), e);
        }
        return orders;
    }


    /**
     * 调用dubbo服务后，将修改过的值，填到原来的order上，后面会重新计算库存用到
     */
    private void buildUpdateOrders(List<Order> changeStockOrders, List<Order> originOrders) {
        Map<Long, Order> orderMap = OrderUtils.toFullOrderMap(originOrders);
        for (Order changeOrder : changeStockOrders) {
            Order originOrder = orderMap.get(changeOrder.getId());
            if (Objects.isNull(originOrder)) {
                continue;
            }
            originOrder.setStockNum(changeOrder.getStockNum());
            originOrder.setStockStatus(changeOrder.getStockStatus());
            originOrder.setOldStockStatus(changeOrder.getOldStockStatus());
            originOrder.setOldStockStatus(changeOrder.getOldStockStatus());
            originOrder.setDiffStockNum(changeOrder.getDiffStockNum());
        }
    }


    @Transactional
    @Override
    public void modifyOrders(Staff staff, List<Order> applyOrders, List<Order> resumeOrders) {
        //分销商品不需要申请库存,默认库存满足
        handleFxTradesStockOrders(staff, applyOrders);
        //归还库存
        if (resumeOrders != null && resumeOrders.size() > 0) {
            stockTradeRouteBusiness.resume(staff, new TradeStockData().addNotApplyRequests(staff, OrderUtils.toFullOrderList(resumeOrders, false)));
        }
        //申请库存
        if (applyOrders != null && applyOrders.size() > 0) {
            OrderKeepInsufficientBusiness.keepInsufficientStockStatus(staff, applyOrders);
            stockOrderFillPropBusiness.fillOrderExceptProp(staff, applyOrders);
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            TradeStockData data = OrderStockUtils.filterApplyStockOrders(staff, applyOrders, tradeLocalConfigurable, tradeConfig);
            if (data.requestRecords().size() > 0) {
                OrderStockUtils.handleApplyStock(staff, applyOrders, stockTradeRouteBusiness.apply(staff, data), OrderStockUtils.OPERATION_APPLY, !staff.openAuditActiveStockRecord(), tradeConfig);
            }
        }
    }

    @Override
    public void rearUnlockApplyOrderStockLocal(Staff staff, Trade trade, List<Order> orders) {
        List<Order> applyOrderList = orders.stream().filter(order -> !order.isVirtual() && !order.ifNonConsign()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(applyOrderList)) {
            handleFxTradesStockOrders(staff, applyOrderList);
            // tradeConfigStockBusiness.fillOrderScalping(staff, applyOrderList);
            stockOrderFillPropBusiness.fillOrderExceptProp(staff, applyOrderList);
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) || (trade.getIsCancel() != null && trade.getIsCancel() == 1) || TradeUtils.isFxTrade(trade)) {
                return;
            }
            List<Order> stockOrders = new ArrayList<>();
//            OrderStockUtils.fillOrder(trade,stockOrders);
            applyOrderList.stream().forEach(order -> {
                order.setUserId(order.getUserId() == null ? trade.getUserId() : order.getUserId());
                order.setWarehouseId(order.getWarehouseId() == null ? trade.getWarehouseId() : order.getWarehouseId());
                order.setScalping(trade.getScalping());
                stockOrders.add(order);
                List<Order> suits = order.getSuits();
                if (suits != null && suits.size() > 0) {
                    for (Order suit : suits) {
                        suit.setUserId(order.getUserId() == null ? trade.getUserId() : order.getUserId());
                        suit.setWarehouseId(order.getWarehouseId() == null ? trade.getWarehouseId() : order.getWarehouseId());
                        suit.setScalping(trade.getScalping());
                    }
                }
            });
            TradeStockData data = OrderStockUtils.rearUnlockFilterApplyStockOrders(staff, stockOrders, tradeLocalConfigurable, tradeConfig, Boolean.TRUE);
            if (data.requestRecords().size() > 0) {
                OrderStockUtils.handleApplyStock(staff, applyOrderList, stockTradeRouteBusiness.apply(staff, data), OrderStockUtils.OPERATION_APPLY, tradeConfig);
            }
        }
    }

    /**
     * 处理分销类型的商品，默认库存充足（订单同步，修改添加商品的时候）
     */
    private void handleFxTradesStockOrders(Staff staff, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            List<Order> fxOrders = orders.stream().filter(order -> OrderUtils.isFxOrMixOrder(order) || OrderUtils.isPlatformFxOrder(order) || OrderUtils.isAlibabaFxRoleOrder(order)).collect(Collectors.toList());
            fxOrders.forEach(order -> {
                if (OrderUtils.isFxOrMixOrder(order) && Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())) {
                    // KMERP-264022: 分销订单也要标记库存不足异常
                    return;
                }
                order.setStockNum(order.getNum());
                //  order.setStockStatus(Trade.STOCK_STATUS_NORMAL);
                OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            });
        }
    }

    /**
     * 处理分销类型的订单，默认库存充足（订单同步，修改添加商品的时候）
     */
    private void handleFxTradesStockTrades(Staff staff, List<Trade> trades) {
        List<Trade> fxTrades = trades.stream().filter(trade -> TradeUtils.isFxOrMixTrade(trade) || TradeUtils.isPlatformFxTrade(trade) || TradeUtils.isAlibabaFxRoleTrade(trade)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fxTrades)) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(fxTrades);
            handleFxTradesStockOrders(staff, orders4Trade);
        }
    }

    /**
     * orders 对象在后面的逻辑中，没有填充快递异常，空包单等信息，需在此方方法前自己填充，或者给使用resumeOrderStockLocal 方法
     *
     * @param staff
     * @param orders
     * @param tir
     */
    @Transactional
    @Override
    public void resumeOrderStockCommonLocal(Staff staff, List<Order> orders, TradeImportResult tir) {
        TradeStockData data = new TradeStockData();
        data.addNotApplyRequests(staff, OrderUtils.toFullOrderList(orders, false));
        String stockDubboOpenCompanyIds = Optional.ofNullable(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch()).map(GlobalSwitchConfig::getTrade).map(GlobalTradeConfig::getStockDubboOpenCompanyIds).orElse(null);

        if (stockTradeRouteServiceDubbo != null && StringUtils.isNotBlank(stockDubboOpenCompanyIds) && DataUtils.ids2List(stockDubboOpenCompanyIds).contains(staff.getCompanyId())) {
            try {
                TradeStockContent content = new TradeStockContent(orders, true);
                stockTradeRouteServiceDubbo.resume(staff, content);
            } catch (Exception e) {
                //归还失败了。
                String fullStackTrace = ExceptionUtils.getFullStackTrace(e);
                logger.error(LogHelper.buildLog(staff, String.format("订单归还库存报错，tids=%s，e=%s", OrderUtils.toTidList(orders), fullStackTrace)), e);
            }
        } else {
            stockTradeRouteBusiness.resume(staff, data);
        }
        //第三方仓库的事件
        List<Trade> needResumes = handle3rdWarehouse4Resume(staff, orders);
        if (!needResumes.isEmpty()) {
            if (tir == null) {
                Logs.ifDebug(LogHelper.buildLog(staff, "resumeOrderStockLocal的TradeImportResult为空，不处理"));
            } else {
                tir.getResumes().addAll(needResumes);
            }
        }
        OrderStockUtils.handleResumeStock(staff, orders, tradeConfigService.get(staff));
    }

    @Override
    public List<StockOrderRecord> swapVirtualStock(Staff staff, List<Order> sourceOrders, List<Order> targetOrders) {
        return virtualWarehouseStockSwapBusiness.swap(staff, sourceOrders, targetOrders);
    }
}

package com.raycloud.dmj.services.loadtest.utils;

import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.loadtest.domain.JDPTrade;
import com.raycloud.dmj.tb.trade.business.TaobaoSecretBusiness;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.internal.parser.json.ObjectJsonParser;
import com.taobao.api.response.TradeFullinfoGetResponse;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * jdp_tb_trade的domain转换器
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/9/20.
 */
public class JDPTradeUtils {



    static TaobaoSecretBusiness taobaoSecretBusiness = new TaobaoSecretBusiness();


    public static List<TradeFullinfoGetResponse> convertToTrades(List<JDPTrade> jdpTrades, User user) throws ApiException {
        List<String> jsons = new ArrayList<String>(jdpTrades.size());
        for (JDPTrade jdpTrade : jdpTrades) {
            jsons.add(jdpTrade.getJdpResponse());
        }

        return parseResponseByDubbo(jsons, TradeFullinfoGetResponse.class);
    }

    public static <T extends TaobaoResponse> List<T> parseResponseByDubbo(List<String> jsonBody, Class<T> clz) throws ApiException {
        List<T> responseArrayList = new ArrayList();
        Iterator i$ = jsonBody.iterator();

        while(i$.hasNext()) {
            String responseString = (String)i$.next();
            T response = parseResponse(responseString, clz);
            responseArrayList.add(response);
        }

        return responseArrayList;
    }

    public static <T extends TaobaoResponse> T parseResponse(String json, Class<T> clazz) throws ApiException {
        ObjectJsonParser<T> parser = new ObjectJsonParser(clazz);
        T rsp = parser.parse(json, null);
        rsp.setBody(json);
        return rsp;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.raycloud.dmj.business.query.TradeCountBusiness;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeConfigCheckContext;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.ITradeConfigCheck;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.erp.db.router.ibatis.SqlMapClientBaseDao;
import lombok.extern.log4j.Log4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Log4j
public class TradeConfigCheck extends SqlMapClientBaseDao implements ITradeConfigCheck {

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    TradeCountBusiness tradeCountBusiness;

    @Resource
    IUserService userService;

    @Override
    public void verifyOpenUnattainableNotApplyStock(Staff staff, TradeConfigCheckContext ctx) {
        initTradeConfig(staff, ctx);
        Preconditions.checkArgument(!tradeCountBusiness.hasBeforeConsignUnattainable(staff, ctx.getTradeConfig()),
                "订单中还有发货前的快递异常订单，不允许修改配置!");
    }

    @Override
    public void verifyClosePackageExamine(Staff staff, TradeConfigCheckContext ctx) {
        initCurrAllUserIds(staff, ctx);
        initTradeConfig(staff, ctx);
        boolean has = tradeCountBusiness.hasWaitPack(staff, ctx, ctx.getTradeConfig());
        if (has) {
            throw new IllegalArgumentException("当前还有待包装订单未处理，请先处理再关闭包装开关!");
        }
    }

    @Override
    public void verifyClosePackageWeigh(Staff staff, TradeConfigCheckContext ctx) {
        initCurrAllUserIds(staff, ctx);
        initTradeConfig(staff, ctx);
        boolean has = tradeCountBusiness.hasWaitWeigh(staff, ctx, ctx.getTradeConfig());
        if (has) {
            throw new IllegalArgumentException("当前还有待称重订单未处理，请先处理再关闭称重开关!");
        }
    }

    @Override
    public void verifyCloseConsign(Staff staff, TradeConfigCheckContext ctx) {
        initCurrAllUserIds(staff, ctx);
        initTradeConfig(staff, ctx);
        boolean has = tradeCountBusiness.hasWaitConsign(staff, ctx, ctx.getTradeConfig());
        if (has) {
            throw new IllegalArgumentException("当前还有待发货订单未处理，请先处理再关闭发货开关!");
        }
    }

    @Override
    public void verifyCloseCommonPlatformDelayShip(Staff staff, TradeConfigCheckContext ctx) {
        initCurrAllUserIds(staff, ctx);
        List<Long> userIds = ctx.getDelayConsignUserIds().stream().filter(ctx.getCurrAllUserIds()::contains).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return;
        }

        Integer count = countOfDelayTrade(staff, userIds);
        if (count == null || count <= 0) {
            return;
        }

        List<Long> sids = listOfDelayTrade(staff, userIds);
        // 每行显示三个sid 逗号隔开
        int chunkSize = 3;
        String sidMsg = IntStream.range(0, (sids.size() + chunkSize - 1) / chunkSize)
                .mapToObj(i -> sids.subList(i * chunkSize, Math.min((i + 1) * chunkSize, sids.size())))
                .map(chunk -> chunk.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(",")))
                .collect(Collectors.joining("\n"));
        throw new IllegalArgumentException(
                String.format("您还有定时发货未上传的订单: %d 条\n%s%s\n请全部处理完成后才可关闭配置",
                        count, sidMsg, count > 20 ? "...(前20条订单系统单号)" : "")
        );
    }

    void initCurrAllUserIds(Staff staff, TradeConfigCheckContext ctx) {
        if (ctx.getCurrAllUserIds() == null) {
            ctx.setCurrAllUserIds(userService.queryByCompanyId(staff.getCompanyId()).stream().map(User::getId).collect(Collectors.toSet()));
        }
    }

    void initTradeConfig(Staff staff, TradeConfigCheckContext ctx) {
        if (ctx.getTradeConfig() == null) {
            ctx.setTradeConfig(tradeConfigService.get(staff));
        }
    }

    Integer countOfDelayTrade(Staff staff, List<Long> userIds) {
        Query q = new Query();
        q.append("c.company_id = ?").add(staff.getCompanyId());
        q.and().append("c.is_error = ?").add(2);
        q.and().append("c.user_id IN ").append("(" + Joiner.on(",").join(userIds) + ")");

        String sql = String.format("SELECT COUNT(*) FROM consign_record_%s c WHERE %s", staff.getDbInfo().getTradeDbNo(), q.getQ().toString());
        return getJdbcTemplate(staff).queryForObject(sql, q.getArgs().toArray(), Integer.class);
    }

    List<Long> listOfDelayTrade(Staff staff, List<Long> userIds) {
        Query q = new Query();
        q.append("c.company_id = ?").add(staff.getCompanyId());
        q.and().append("c.is_error = ?").add(2);
        q.and().append("c.user_id IN ").append("(" + Joiner.on(",").join(userIds) + ")");

        String sql = String.format("SELECT c.sid FROM consign_record_%s c WHERE %s LIMIT 20", staff.getDbInfo().getTradeDbNo(), q.getQ().toString());
        return getJdbcTemplate(staff).queryForList(sql, q.getArgs().toArray(), Long.class);
    }
}

package com.raycloud.dmj.services.trades.support.search.type;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-19
 */
public interface ITradeQueryType {

     TradeTypeEnum getType();

     String getSql(Staff staff,Integer type, Query q,Long queryId);
}

package com.raycloud.dmj.services.trades.support;

import com.google.common.base.Objects;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.CursorListBase;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryContext;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.Trades;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.trades.TradeSearchService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description <pre>
 *  原有的TradeSearchService因为是放在接口层的 很多公共方法因为包依赖关系无法写入
 *  因此在这里又重新抽了一层
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-11
 */
public abstract class AbsTradeSearchService extends TradeSearchService {

    private final Logger logger = Logger.getLogger(this.getClass());

    protected void reBaseTimer(TradeQueryParams params){
        params.getContext().getLogBuilder().reBaseTimer();
    }

    protected void recordTimer(TradeQueryParams params,String msg){
        params.getContext().getLogBuilder().recordTimer(msg);
    }

    protected void recordTimer(TradeQueryParams params,String msg,Long tooklmt){
        params.getContext().getLogBuilder().recordTimer(msg,tooklmt);
    }

    public static final Map<String,String> FIELD_CH_NAME_MAP = new HashMap<String,String>(){{
        put("pay_time", "订单支付时间");

        put("buyer_nick", "买家昵称");
        put("payment", "订单实付金额");
        put("buyer_message", "买家留言");
        put("seller_memo", "卖家备注");
        put("sys_memo", "系统备注");
        put("sysStatus", "系统状态");

        put("itemTitle", "商品名称");
        put("shortTitle", "商品简称");
        put("itemRemark", "商品备注");



    }};

    protected String getFieldChName(String field){
        if (FIELD_CH_NAME_MAP.containsKey(field)) {
            return FIELD_CH_NAME_MAP.get(field);
        }
        return field;
    }


    protected abstract TradeAssembleBusiness getTradeAssembleBusiness();

    public static Long queryTookThreshold(Long companyId,Integer keySize, Long maxLimit){
        if (DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,companyId, LogBusinessEnum.QUERY.getSign())) {
            return 1L;
        }
        if (Objects.equal(keySize,1)) {
            return 800L;
        }
        return maxLimit == null?3000L:maxLimit;
    }

    @Override
    public CursorListBase<TbTrade> searchTrade(Staff staff, TradeQueryParams params, TradeAssembleParams assemble) {
        boolean pureTrade = params.isPureTrade();
        String fields = params.getFields();
        Integer useHasNext = params.getUseHasNext();

        AbsLogBuilder logBuilder = null;

        try {
            TradeQueryContext context = params.getContext();
            if (context.getLogBuilder() == null || context.getLogBuilder() == AbsLogBuilder.getNvlInstance()) {
                logBuilder = new QueryLogBuilder(staff).append("searchTrade 耗时统计:").startTimer();
                context.setLogBuilder(logBuilder);
            }

            params.setPureTrade(true);
            //这里设置查询字段 有可能导致最终填充数据丢失
            //if (CollectionUtils.isNotEmpty(assemble.getQueryFields())) {
            //    params.setFields(Strings.join(",",assemble.getQueryFields()));
            //}
            params.setUseHasNext(1);

            CursorListBase<TbTrade> result = new CursorListBase<>();
            Trades trades = backTradeSearchUnfuse(staff, params);
            if (CollectionUtils.isEmpty(trades.getList())) {
                result.setHasNext(false);
                result.setList(new ArrayList<>());
                return result;
            }

            result.setHasNext(trades.getHasNext() != null && trades.getHasNext());
            List<Trade> list = getTradeAssembleBusiness().assemblyTrades(staff, assemble, params.getContext().getLogBuilder(), trades.getList());
            result.setList(TradeUtils.toTbTrades(list));

            return result;
        }finally {
            //不改变外面传入的参数值
            params.setPureTrade(pureTrade);
            params.setFields(fields);
            params.setUseHasNext(useHasNext);

            if (logBuilder != null) {
                logBuilder.startWatch().appendTook(queryTookThreshold(staff.getCompanyId(),null,3800L)).multiPrintDebug(logger);
            }
        }

    }


    public abstract List<Trade> queryBySids(Staff staff, boolean showDetail,AbsLogBuilder logBuilder, Long... sids);

    public abstract List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail,AbsLogBuilder logBuilder, Long... sids);

    public abstract List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay,AbsLogBuilder logBuilder, String... tids);

    public abstract List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter,boolean containConsign,AbsLogBuilder logBuilder, Long... sids);


}

package com.raycloud.dmj.services.trades.support;


import com.google.common.collect.Maps;
import com.raycloud.dmj.dao.trade.ExpressCompanyMappingDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.ExpressCompanyMapping;
import com.raycloud.dmj.domain.trades.ExpressCompanyMappingBatchDTO;
import com.raycloud.dmj.domain.trades.bo.ExpressCompanyMappingBO;
import com.raycloud.dmj.services.trades.IExpressCompanyMappingService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统物流和平台关系映射
 *
 * <AUTHOR>
 */
@Service
public class ExpressCompanyMappingServiceImpl implements IExpressCompanyMappingService {
    @Resource
    ExpressCompanyMappingDao expressCompanyMappingDao;

    @Resource
    ExpressCompanyMapingMatchService expressCompanyMapingMatchService;

    @Override
    @Cacheable(value = "defaultCache#3600", key = "'express_company_mapping_' + #expressCompanyMappingBO.source")
    public Map<Long, ExpressCompanyMapping> getExpressMappingMap(ExpressCompanyMappingBO expressCompanyMappingBO) {
        Map<String, Object> params = new HashMap<>();
        params.put("source", expressCompanyMappingBO.getSource());
        params.put("active", true);
        params.put("match_type", expressCompanyMappingBO.getMatchType());
        List<ExpressCompanyMapping> expressCompanyMappingList = expressCompanyMappingDao.getList(params);
        if (CollectionUtils.isEmpty(expressCompanyMappingList)) {
            return Maps.newHashMap();
        }
        return coverToMap(expressCompanyMappingList);
    }

    private Map<Long, ExpressCompanyMapping> coverToMap(List<ExpressCompanyMapping> expressCompanyMappingList) {
        Map<Long, ExpressCompanyMapping> map = new HashMap<>();
        for (ExpressCompanyMapping expressCompanyMapping : expressCompanyMappingList) {
            map.put(expressCompanyMapping.getRelativeExpressCompanyId(), expressCompanyMapping);
        }
        return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int deleteOrActive(Staff staff, Long id, int enableStatus, int active, String source) {
        Map<String, Object> condition = new HashMap<String, Object>(3);
        condition.put("id", id);
        condition.put("enableStatus", enableStatus);
        condition.put("active", active);
        return expressCompanyMappingDao.logicDelete(condition);

    }

    @Override
    public ExpressCompanyMapping addExpressCompanyMapping(ExpressCompanyMappingBO expressCompanyMappingBO) {
        ExpressCompanyMapping expressCompanyMapping = new ExpressCompanyMapping();
        BeanUtils.copyProperties(expressCompanyMappingBO, expressCompanyMapping);
        expressCompanyMappingDao.insert(expressCompanyMapping);
        return expressCompanyMapping;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int addBatch(Staff staff, List<ExpressCompanyMappingBO> list) {
        List<ExpressCompanyMapping> companyMappingList = new ArrayList<>(list.size());
        for (ExpressCompanyMappingBO expressCompanyMappingBO : list) {
            ExpressCompanyMapping expressCompanyMapping = new ExpressCompanyMapping();
            BeanUtils.copyProperties(expressCompanyMappingBO, expressCompanyMapping);
            companyMappingList.add(expressCompanyMapping);
        }
        return expressCompanyMappingDao.batchInsert(staff, companyMappingList);
    }

    @Override
    public int update(Staff staff, ExpressCompanyMappingBO expressCompanyMappingBO) {
        ExpressCompanyMapping expressCompanyMapping = new ExpressCompanyMapping();
        BeanUtils.copyProperties(expressCompanyMappingBO, expressCompanyMapping);
        return expressCompanyMappingDao.update(staff, expressCompanyMapping);
    }

    @Override
    public List<ExpressCompanyMapping> getListByExample(ExpressCompanyMappingBO expressCompanyMappingBO, Page page) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", expressCompanyMappingBO.getId());
        params.put("matchType", expressCompanyMappingBO.getMatchType());
        params.put("source", expressCompanyMappingBO.getSource());
        params.put("relativeExpressCompanyId", expressCompanyMappingBO.getRelativeExpressCompanyId());
        params.put("sysBizCode", expressCompanyMappingBO.getSysBizCode());
        params.put("sysBizName", expressCompanyMappingBO.getSysBizName());
        params.put("active", expressCompanyMappingBO.getActive());
        params.put("page", page);
        return expressCompanyMappingDao.getList(params);
    }

    @Override
    public List<ExpressCompanyMappingBO> createMappingList(ExpressCompanyMappingBatchDTO expressCompanyMappingBatchDTO) {
        if ("map".equals(expressCompanyMappingBatchDTO.getCreateType())) {
            return expressCompanyMapingMatchService.createMappingDataByMap(expressCompanyMappingBatchDTO);
        }
        if ("list".equals(expressCompanyMappingBatchDTO.getCreateType())) {
            return expressCompanyMapingMatchService.createMappingDataByBoList(expressCompanyMappingBatchDTO);
        }
        return null;
    }

    @Override
    public List<ExpressCompanyMapping> getMappingByPlatCodes(String source, List<String> platformBizCodes) {
        if (StringUtils.isBlank(source) || CollectionUtils.isEmpty(platformBizCodes)) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("source", source);
        params.put("platformBizCodes", platformBizCodes);
        return expressCompanyMappingDao.queryMappingByPlatCode(params);
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.ThreadPoolBusiness;
import com.raycloud.dmj.dao.order.OrderProductDao;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.rematch.ReMatchContext;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.stock.Detail4BatchProduct;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.domain.wms.params.WmsQueryProductBatchNoParams;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.dmj.product.domain.OrderStockProductMsg;
import com.raycloud.dmj.product.domain.ProductQueryParams;
import com.raycloud.dmj.product.enums.OrderStockProductStatusEnum;
import com.raycloud.dmj.product.services.IOrderStockProductService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.trades.IOrderProductService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderProductServiceImpl extends ThreadPoolBusiness<Trade> implements IOrderProductService {

    public static final String PROGRESS_ORDER_PRODUCT_SAVE = "trade.order.stock.product.save";

    //取消批次锁定
    public static final String PROGRESS_ORDER_PRODUCT_CANCEL = "trade.order.stock.product.cancel";
    @Resource
    private OrderProductDao orderProductDao;

    @Resource
    private IWmsService wmsService;

    @Autowired(required = false)
    private IOrderStockProductService orderStockProductService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private TbOrderDAO tbOrderDAO;

    @Resource
    private TradeTraceService tradeTraceService;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Override
    public void save(Staff staff, List<OrderProduct> orderProducts) {
        if (CollectionUtils.isEmpty(orderProducts)) {
            return;
        }

        List<OrderProduct> collect = orderProducts.stream().filter(orderProduct -> NumberUtils.isEquals(orderProduct.getSid(), 0L) ||
                NumberUtils.isEquals(orderProduct.getOrderId(), 0L)).collect(Collectors.toList());

        Assert.isTrue(CollectionUtils.isEmpty(collect), "orderId、sid不可以为空");

        List<OrderProduct> oldOrderProjects = orderProductDao.queryBySids(staff, orderProducts.stream().map(OrderProduct::getSid).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(oldOrderProjects)) {
            orderProductDao.save(staff, orderProducts);
            return;
        }

        Map<Long, OrderProduct> orderProductMap = oldOrderProjects.stream().collect(Collectors.toMap(OrderProduct::getOrderId, orderProduct -> orderProduct));

        List<OrderProduct> newOrderProduct = new ArrayList<>(), updateOrderProduct = new ArrayList<>();

        orderProducts.forEach(orderProduct -> {
            OrderProduct oldOrderProject = orderProductMap.get(orderProduct.getOrderId());
            if (ObjectUtils.isEmpty(oldOrderProject)) {
                newOrderProduct.add(orderProduct);
                return;
            }
            Integer newNum = orderProduct.getNum();
            if (newNum == null || newNum <= 0) {
                return;
            }
            Integer oldNum = oldOrderProject.getNum();
            oldOrderProject.setNum(oldNum == null ? newNum : oldNum + newNum);
            updateOrderProduct.add(oldOrderProject);
        });

        if (!CollectionUtils.isEmpty(updateOrderProduct)) {
            orderProductDao.update(staff, updateOrderProduct);
        }

        if (!CollectionUtils.isEmpty(newOrderProduct)) {
            orderProductDao.save(staff, newOrderProduct);
        }
    }

    @Override
    public List<OrderProduct> queryByOrderIds(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }
        return orderProductDao.queryByOrderIds(staff, orderIds);
    }

    @Override
    public void delete(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }

        orderStockProductService.repealConsignment(staff, sids.stream().map(String::valueOf).collect(Collectors.toList()));
    }

    @Override
    public <T extends Trade> void fillOrderProduct(Staff staff, List<T> trades) {

        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        fill(staff, TradeUtils.getOrders4Trade(new ArrayList<>(trades)));
    }

    public <T extends Order> void fill(Staff staff, List<T> orders) {

        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        Map<String, Order> orderMap = new HashMap<>();

        orders.forEach(o -> {
            if (Objects.isNull(o)) {
                return;
            }
            if (!CollectionUtils.isEmpty(o.getSuits())) {
                //子商品order也会有生产日期、批次信息
                o.getSuits().stream().filter(Objects::nonNull).forEach(suit -> orderMap.put(String.valueOf(suit.getId()), suit));
            }

            //套件订单子订单本身无生产日期、批次信息
            if (TbOrder.TypeOfCombineOrder == o.getType() && (null == o.getCombineId() || o.getCombineId() <= 0)) {
                return;
            }
            orderMap.put(String.valueOf(o.getId()), o);
        });

        //获取订单商品批次信息
        Map<String, List<OrderStockProduct>> products = getOriginProduct(staff, new ArrayList<>(orderMap.values()));

        if (CollectionUtils.isEmpty(products)) {
            return;
        }

        products.forEach((k, product) -> {

            Order order = orderMap.get(k);

            if (ObjectUtils.isEmpty(order)) {
                return;
            }

            order.setOrderStockProducts(product);
        });
    }

    public <T extends Order> void fillAll(Staff staff, List<T> orders) {

        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        Map<String, Order> orderMap = new HashMap<>();

        orders.forEach(o -> {
            if (Objects.isNull(o)) {
                return;
            }
            if (!CollectionUtils.isEmpty(o.getSuits())) {
                //子商品order也会有生产日期、批次信息
                o.getSuits().stream().filter(Objects::nonNull).forEach(suit -> orderMap.put(String.valueOf(suit.getId()), suit));
            }

            //套件订单子订单本身无生产日期、批次信息
            if (TbOrder.TypeOfCombineOrder == o.getType() && (null == o.getCombineId() || o.getCombineId() <= 0)) {
                return;
            }
            orderMap.put(String.valueOf(o.getId()), o);
        });

        //获取订单商品批次信息
        Map<String, List<OrderStockProduct>> products = getOriginProductNoConsiderConsign(staff, new ArrayList<>(orderMap.values()));

        if (CollectionUtils.isEmpty(products)) {
            return;
        }

        products.forEach((k, product) -> {

            Order order = orderMap.get(k);

            if (ObjectUtils.isEmpty(order)) {
                return;
            }

            order.setOrderStockProducts(product);
        });
    }

    @Override
    public HashMap<String, Object> queryByItems(Staff staff, ProductQueryParams productQueryParams, Page page) {
        if (ObjectUtils.isEmpty(productQueryParams)) {
            return null;
        }
        Long warehouseId = productQueryParams.getWarehouseId();
        Long sysItemId = productQueryParams.getSysItemId();
        if (ObjectUtils.isEmpty(warehouseId) || ObjectUtils.isEmpty(sysItemId)) {
            return null;
        }

        List<Long> sysItemIds = Collections.singletonList(sysItemId);
        Long sysSkuId = productQueryParams.getSysSkuId();
        List<Long> sysSkuIds = ObjectUtils.isEmpty(sysSkuId) ? null : Collections.singletonList(sysSkuId);
        WmsQueryProductBatchNoParams params = new WmsQueryProductBatchNoParams();
        params.setPage(page);
        params.setWarehouseIds(Collections.singletonList(warehouseId));
        params.setSysItemIds(sysItemIds);
        params.setSysSkuIds(sysSkuIds);
        params.setBatchNo(productQueryParams.getBatchNo());
        params.setProductTime(productQueryParams.getProductionDate());
        params.setFilterExpired(true);
        params.setFilterNoProductBatchNo(true);
        //通过仓储获取到批次信息
        PageList<Detail4BatchProduct> detail4BatchProductPageList = wmsService.queryBatchProductItemStockPageList(staff, params);

        if (CollectionUtils.isEmpty(detail4BatchProductPageList.getList())) {
            return null;
        }

        //获取批次的锁定数
        List<OrderStockProduct> countLockNum = orderStockProductService.getCountLockNum(staff, ProductQueryParams.builder().warehouseId(warehouseId).sysItemId(sysItemId).sysSkuId(sysSkuId).build());

        HashMap<String, Object> data = new HashMap<>();
        data.put("total", detail4BatchProductPageList.getTotal());
        data.put("page", page);
        data.put("list", toOrderStockProduct(detail4BatchProductPageList.getList(), countLockNum));
        return data;
    }

    @Override
    public void saveOrderStockProduct(Staff staff, TradeControllerParams params, OrderStockProduct orderStockProduct, ProgressData progressData, String entrance) {

        DmjItem itemDetail = null;
        if (Objects.nonNull(orderStockProduct.getSysSkuId())) {
            itemDetail = itemServiceDubbo.querySkuWithSysSkuId(staff, orderStockProduct.getSysSkuId());
            Assert.notNull(itemDetail, "所选商品SKU不存在");
        }

        if (Objects.isNull(itemDetail) && Objects.nonNull(orderStockProduct.getSysItemId())) {
            itemDetail = itemServiceDubbo.queryItemWithSysItemId(staff, orderStockProduct.getSysItemId());
            Assert.notNull(itemDetail, "所选商品不存在");
        }

        if (StringUtils.hasText(orderStockProduct.getOrderNumber())) {
            params.setSids(orderStockProduct.getOrderNumber());
        }

        TradeQueryParams queryParams = TradeQueryParams.copyParams(params).setQueryOrder(false)
                .setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_BUYER_PAY)
                .setQueryId(62L)
                .setPage(new Page(1, 200000))
                .setQueryFlag(1).setFields("t.sid, t.warehouse_id, t.convert_type, t.source_id, t.belong_type, t.dest_id");
        Trades search = tradeSearchService.search(staff, queryParams);
        if (ObjectUtils.isEmpty(search) || CollectionUtils.isEmpty(search.getList())) {
            return;
        }
        String outerId = itemDetail.getOuterId();
        run(staff, search.getList(), new AbstractBusiness() {
            @Override
            public void doBusiness(List<Trade> data) {
                data.removeIf(t -> !checkSaveData(Long.valueOf(params.getWarehouseId()), t, progressData));

                Set<Long> sids = data.stream().map(Trade::getSid).collect(Collectors.toSet());
                sids.addAll(data.stream().map(Trade::getMergeSid).filter(Objects::nonNull).collect(Collectors.toSet()));

                List<TbOrder> tbOrders = tbOrderDAO.queryBySidsAndItems(staff, new ArrayList<>(sids), orderStockProduct.getSysItemId(), orderStockProduct.getSysSkuId());
                tbOrders = checkSaveData(tbOrders, data, outerId, progressData, entrance);

                progressData.setErrorNum(progressData.getAtomicErrorNum().get());
                if (CollectionUtils.isEmpty(tbOrders)) {
                    return;
                }
                try {
                    saveOrderStockProduct(staff, tbOrders, orderStockProduct);
                    progressData.setSucNum(progressData.getAtomicSucNum().addAndGet(OrderUtils.toSidList(tbOrders).size()));
                } catch (Exception e){
                    Logs.error(LogHelper.buildLog(staff,"保存批次数据失败"), e);
                    progressData.setErrorNum(progressData.getAtomicErrorNum().addAndGet(data.size()));
                }
            }
        });

    }

    private boolean checkSaveData(Long warehouseId, Trade data, ProgressData progressData) {
        StringBuilder msg = new StringBuilder();
        if (!warehouseId.equals(data.getWarehouseId())) {
            msg.append("，所选订单必须为同一仓库！");
        }
        if (TradeUtils.isGxTrade(data)) {
            msg.append("，供销订单不允许指定批次！");
        }
        if (TradeUtils.isFxTrade(data)) {
            msg.append("，分销订单不允许指定批次！");
        }
        if (msg.length() <= 0) {
            return true;
        }
        progressData.getErrorMsg().add("{\"" + data.getSid() + "\":\"" + msg + "】，处理失败\"}");
        progressData.getAtomicErrorNum().incrementAndGet();
        return false;
    }
    private List<TbOrder> checkSaveData(List<TbOrder> data, List<Trade> trades, String outerId, ProgressData progressData, String entrance) {
        if (CollectionUtils.isEmpty(data)) {
            if ("1".equals(entrance)) {
                trades.stream().map(Trade::getSid).forEach(sid -> progressData.getErrorMsg().add("{\""+sid+"\":\"您在【套件明细】弹框已操作“替换“商品\n" +
                    "替换商品后，请先点击【套件明细】弹框上的“确定”按钮保存替换后的商品，再操作选择批次生产日期!!!\n\n否则所选批次/生产日期不生效!!!\"}"));
            } else {
                trades.stream().map(Trade::getSid).forEach(sid -> progressData.getErrorMsg().add("{\""+sid+"\":\"订单中不包含待审核的【商家编码="+outerId+"】，处理失败\"}"));
            }
            progressData.getAtomicErrorNum().addAndGet(trades.size());
            return null;
        }

        Map<Long, List<TbOrder>> orderMaps = data.stream().collect(Collectors.groupingBy(TbOrder::getSid));

        orderMaps.forEach((sid, order) -> {
            order.removeIf(o -> !checkSaveData(o));
            if (CollectionUtils.isEmpty(order)) {
                if ("1".equals(entrance)) {
                    progressData.getErrorMsg().add("{\""+sid+"\":\"您在【套件明细】弹框已操作“替换“商品\n" +
                            "替换商品后，请先点击【套件明细】弹框上的“确定”按钮保存替换后的商品，再操作选择批次生产日期!!!\n否则所选批次/生产日期不生效!!!\"}");
                } else {
                    progressData.getErrorMsg().add("{\""+sid+"\":\"订单中不包含待审核的,【商家编码="+outerId+"】，处理失败\"}");
                }
                progressData.getAtomicErrorNum().incrementAndGet();
            }
        });
        List<TbOrder> orders = new ArrayList<>();
        orderMaps.values().forEach(orders::addAll);
        return orders;
    }

    private Boolean checkSaveData(TbOrder data) {
        if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(data.getSysStatus()) && !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(data.getSysStatus())) {
            return false;
        }
        if (data.getEnableStatus() == 0) {
            return false;
        }
        if (data.isVirtual()) {
            return false;
        }
        if (data.ifNonConsign()) {
            return false;
        }
        return true;
    }


    @Override
    protected List<List<Trade>> groupData(Staff staff, List<Trade> data) {
        return ListUtils.splitList(data, 500);
    }

    public <T extends Order> void saveOrderStockProduct(Staff staff, List<T> orders, OrderStockProduct orderStockProduct) {
        if (CollectionUtils.isEmpty(orders) || ObjectUtils.isEmpty(orderStockProduct)) {
            return;
        }

        if (StringUtils.hasText(orderStockProduct.getSubOrderNumber())) {
            orders = orders.stream().filter(o -> orderStockProduct.getSubOrderNumber().equals(String.valueOf(o.getId()))).collect(Collectors.toList());
        }

        if (Objects.isNull(orderStockProduct.getBatchNo()) && Objects.isNull(orderStockProduct.getProductionDate()) && Objects.isNull(orderStockProduct.getExpireDate())) {
            cancel(staff, new ArrayList<>(orders), Boolean.TRUE);
            return;
        }

        List<OrderStockProduct> orderStockProducts = orders.stream().map(order -> order2Product(order, orderStockProduct, order.getNum().longValue())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderStockProducts)) {
            return;
        }

        List<String> oldOrderIds = orders.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.toList());
        Map<String, List<OrderStockProduct>> oldOsp = orderStockProductService.getOrderStockProduct(staff,
                ProductQueryParams.builder().subOrderNumbers(oldOrderIds).status(OrderStockProductStatusEnum.STATUS_1).build());

        orderStockProductService.beforehandLock(staff, orderStockProducts);

        addTradeTrace(staff, new ArrayList<>(orders), oldOsp, orderStockProducts.stream().collect(Collectors.groupingBy(OrderStockProduct::getSubOrderNumber)));
    }

    /**
     * 将仓储的批次数据和批次服务的锁定数合并
     */
    private List<OrderStockProduct> toOrderStockProduct(List<Detail4BatchProduct> detail4BatchProducts, List<OrderStockProduct> countLockNum) {
        if (CollectionUtils.isEmpty(detail4BatchProducts)) {
            return null;
        }
        boolean countLockNumIsNull = CollectionUtils.isEmpty(countLockNum);
        Map<String, List<OrderStockProduct>> lockNumMap = countLockNumIsNull ? null : countLockNum.stream().collect(Collectors.groupingBy(this::getLockNumGroupKey));
        return detail4BatchProducts.stream().map(dbp -> {
            if (countLockNumIsNull) {
                return detail4BatchProduct2Product(dbp);
            }
            OrderStockProduct orderStockProduct = detail4BatchProduct2Product(dbp);
            if (ObjectUtils.isEmpty(orderStockProduct)) {
                return null;
            }
            List<OrderStockProduct> productList = lockNumMap.get(getLockNumGroupKey(orderStockProduct));

            if (!CollectionUtils.isEmpty(productList)) {
                orderStockProduct.setLockNum(productList.stream().mapToLong(OrderStockProduct::getLockNum).sum());
            }
            return orderStockProduct;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String getLockNumGroupKey(OrderStockProduct orderStockProduct) {
        return orderStockProduct.getSysItemId() + "_" + orderStockProduct.getSysSkuId() + "_" + orderStockProduct.getBatchNo() + "_" + orderStockProduct.getProductionDate() + "_" + orderStockProduct.getExpireDate();
    }

    /**
     * 修改商品后对应重新计算批次数据
     */
    public void reMatch(Staff staff, ReMatchContext reMatchContext) {
        if (ObjectUtils.isEmpty(staff) || ObjectUtils.isEmpty(reMatchContext)) {
            return;
        }
        List<Order> orderList = trade2Orders(reMatchContext.getOriginTrades());
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        List<Long> originOrderNum = reMatchContext.getOriginTrades().stream().map(Trade::getSid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(reMatchContext.getRemoverSids())) {
            originOrderNum.addAll(reMatchContext.getRemoverSids());
        }
        Map<String, List<OrderStockProduct>> originProduct = getByOrderNum(staff, originOrderNum, OrderStockProductStatusEnum.STATUS_1);

        Map<String, Order> orderMaps = orderList.stream().collect(Collectors.toMap(order -> String.valueOf(order.getId()), o -> o));

        //得到需要删除的批次数据
        List<String> deleteProduct = getDeleteProduct(originProduct, orderMaps, reMatchContext);

        //得到需要新增的批次数据
        List<OrderStockProduct> insertProduct = getInsertProduct(staff, originProduct, reMatchContext.getOriginTrades(), orderMaps, reMatchContext.getEvent());

        if (!CollectionUtils.isEmpty(deleteProduct)) {
            //取消占用
            orderStockProductService.cancelProductLock(staff, deleteProduct, true);
        }

        if (EventEnum.EVENT_TRADE_CHANGE_WAREHOUSE_AFTER.equals(reMatchContext.getEvent())) {
            addTradeTrace(staff, orderList, originProduct, null);
            return;
        }
        if (!CollectionUtils.isEmpty(insertProduct)) {
            //预占用批次信息
            orderStockProductService.beforehandLock(staff, insertProduct);
        }
    }

    @Override
    public void cancelProductLock(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, sids.toArray(new Long[0]));
        List<Long> scalpingSids = trades.stream().filter(trade->checkTrade(staff,trade)).map(Trade::getSid).collect(Collectors.toList());
        List<Long> outerSids = trades.stream().filter(trade->!checkTrade(staff,trade)).map(Trade::getSid).collect(Collectors.toList());
        ListUtils.splitList(scalpingSids, 500).forEach(sidsPage -> {
            List<TbOrder> tbOrders = tbOrderDAO.queryBySidsAndItems(staff, sidsPage, null, null);
            if (!CollectionUtils.isEmpty(tbOrders)) {
                cancel(staff, new ArrayList<>(tbOrders), Boolean.TRUE);
            }
        });
        ListUtils.splitList(outerSids, 500).forEach(sidsPage -> {
            List<TbOrder> tbOrders = tbOrderDAO.queryBySidsAndItems(staff, sidsPage, null, null);
            if (!CollectionUtils.isEmpty(tbOrders)) {
                cancel(staff, new ArrayList<>(tbOrders), Boolean.FALSE);
            }
        });
    }

    private Boolean checkTrade(Staff staff,Trade trade) {
        if (trade.getScalping() == 1) {
            return true;
        }
        if (trade.getEnableStatus() == 0) {
            return true;
        }
        if (TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.UNATTAINABLE)) {
            return true;
        }
        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) || Trade.SYS_STATUS_CANCEL.equals(trade.getSysStatus())) {
            return true;
        }
        return false;
    }

    @Override
    public void cancelProductLock(Staff staff, List<Long> sids, List<Long> orderIds, Boolean cleanAll) {

        ProductQueryParams.ProductQueryParamsBuilder params = ProductQueryParams.builder().status(OrderStockProductStatusEnum.STATUS_1);
        if (!cleanAll && CollectionUtils.isEmpty(sids) && CollectionUtils.isEmpty(orderIds)) {
            return;
        }

        if (!CollectionUtils.isEmpty(sids) && !cleanAll) {
            params.orderNumbers(sids.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(orderIds) && !cleanAll) {
            params.subOrderNumbers(orderIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        Map<String, List<OrderStockProduct>> cleanOsp = orderStockProductService.getOrderStockProduct(staff, params.build());

        if (CollectionUtils.isEmpty(cleanOsp)) {
            return;
        }
        orderStockProductService.cancelProductLock(staff, new ArrayList<>(cleanOsp.keySet()), true);
    }

    private void cancel(Staff staff, List<Order> orders, Boolean filter) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<String> cancelIds;
        if (filter) {
            cancelIds = orders.stream().map(order -> String.valueOf(order.getId())).collect(Collectors.toList());
        } else {
            List<String> sids = OrderUtils.toSidList(orders).stream().map(String::valueOf).collect(Collectors.toList());
            Map<String, List<OrderStockProduct>> allOsp = orderStockProductService.getOrderStockProduct(staff,
                    ProductQueryParams.builder().orderNumbers(sids).status(OrderStockProductStatusEnum.STATUS_1).build());
            cancelIds = orders.stream().filter(order -> checkCancel(order, allOsp)).map(order -> String.valueOf(order.getId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(cancelIds)) {
            return;
        }

        Map<String, List<OrderStockProduct>> cleanOsp = orderStockProductService.getOrderStockProduct(staff,
                ProductQueryParams.builder().subOrderNumbers(cancelIds).status(OrderStockProductStatusEnum.STATUS_1).build());

        if (CollectionUtils.isEmpty(cleanOsp)) {
            return;
        }
        orderStockProductService.cancelProductLock(staff, new ArrayList<>(cleanOsp.keySet()), true);
        addTradeTrace(staff, orders, cleanOsp, null);
    }

    private Boolean checkCancel(Order order, Map<String, List<OrderStockProduct>> allOspMap) {
        if (Objects.isNull(order)) {
            return Boolean.TRUE;
        }
        //作废
        if (order.getIsCancel() == 1) {
            return Boolean.TRUE;
        }
        //删除
        if (order.getEnableStatus() <= 0) {
            return Boolean.TRUE;
        }
        if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
            return Boolean.TRUE;
        }
        if (Trade.SYS_STATUS_CANCEL.equals(order.getSysStatus())) {
            return Boolean.TRUE;
        }
        if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(order.getSysStatus())) {
            return Boolean.TRUE;
        }
        if (Trade.SYS_STATUS_FINISHED.equals(order.getSysStatus())) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(order.getSysConsigned()) && order.getSysConsigned() > 1) {
            return Boolean.TRUE;
        }
        if (CollectionUtils.isEmpty(allOspMap)) {
            return Boolean.FALSE;
        }
        List<OrderStockProduct> productList = allOspMap.get(String.valueOf(order.getId()));
        if (CollectionUtils.isEmpty(productList)) {
            return Boolean.FALSE;
        }
        if (productList.stream().anyMatch(p -> !Objects.equals(p.getSysItemId(), order.getItemSysId())
                || (p.getSysSkuId() > 0 && !Objects.equals(p.getSysSkuId(), order.getSkuSysId())))) {
            return  Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private void addTradeTrace(Staff staff, List<Order> orders, Map<String, List<OrderStockProduct>> oldOsp, Map<String, List<OrderStockProduct>> newOsp) {
        if (CollectionUtils.isEmpty(orders) || (CollectionUtils.isEmpty(oldOsp) && CollectionUtils.isEmpty(newOsp))) {
            return;
        }
        List<TradeTrace> tradeTraces = buildTradeTrace(staff, OrderUtils.toMapBySid(orders), oldOsp, newOsp);
        if (CollectionUtils.isEmpty(tradeTraces)) {
            return;
        }
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    /**
     * 构建操作日志
     * @param staff staff
     * @param orders 此处order按sid分组
     * @param oldOsp 原批次信息
     * @param newOsp 新的批次信息
     * @return 操作日志结果集
     */
    private List<TradeTrace> buildTradeTrace(Staff staff, Map<Long, List<Order>> orders, Map<String, List<OrderStockProduct>> oldOsp, Map<String, List<OrderStockProduct>> newOsp) {
        List<TradeTrace> tradeTraces = new ArrayList<>();
        orders.values().forEach(os -> {
            TradeTrace tradeTrace = buildTradeTrace(staff, os, oldOsp, newOsp);
            if (Objects.nonNull(tradeTrace)) {
                tradeTraces.add(tradeTrace);
            }
        });
        return tradeTraces;
    }

    private TradeTrace buildTradeTrace(Staff staff, List<Order> orders, Map<String, List<OrderStockProduct>> oldOspMap, Map<String, List<OrderStockProduct>> newOspMap) {
        if (CollectionUtils.isEmpty(orders) || (CollectionUtils.isEmpty(oldOspMap) && CollectionUtils.isEmpty(newOspMap))) {
            return null;
        }
        Long taobaoId = orders.get(0).getTaobaoId();
        Long sid = orders.get(0).getSid();
        String tid = orders.get(0).getTid();
        StringBuilder message = new StringBuilder();

        for (Order order : orders){
            List<OrderStockProduct> newOsp = CollectionUtils.isEmpty(newOspMap) ? null : newOspMap.get(String.valueOf(order.getId()));
            List<OrderStockProduct> oldOsp = CollectionUtils.isEmpty(oldOspMap) ? null : oldOspMap.get(String.valueOf(order.getId()));
            if (CollectionUtils.isEmpty(newOsp) && CollectionUtils.isEmpty(oldOsp)) {
                continue;
            }
            message.append("商品:[").append(order.getSysOuterId()).append("]");
            if (CollectionUtils.isEmpty(oldOsp) && !CollectionUtils.isEmpty(newOsp)) {
                builderMessage(message, "指定", newOsp);
            }
            if (CollectionUtils.isEmpty(newOsp) && !CollectionUtils.isEmpty(oldOsp)) {
                builderMessage(message, "清除", oldOsp);
            }
            if (!CollectionUtils.isEmpty(oldOsp) && !CollectionUtils.isEmpty(newOsp)) {
                message.append(",修改批次生产日期,由");
                builderMessage(message, "", oldOsp);
                message.append(",更新为");
                builderMessage(message, "", newOsp);
            }
        }

        return TradeTrace.builder().companyId(staff.getCompanyId()).operateTime(new Date()).sid(sid)
                .operator(staff.getName()).content(message.toString()).action("更新订单批次").taobaoId(taobaoId).tid(tid).build();
    }

    private void builderMessage(StringBuilder message, String op, List<OrderStockProduct> osp) {
        osp.forEach(p -> {
            if (!StringUtils.isEmpty(p.getBatchNo())) {
                message.append(",").append(StringUtils.hasText(op) ? op : "").append("批次:[").append(p.getBatchNo()).append("]");
            }
            if (Objects.nonNull(p.getProductionDate())) {
                message.append(",").append(StringUtils.hasText(op) ? op : "").append("生产日期:[").append(DateUtils.date2Str(p.getProductionDate())).append("]");
            }
            if (Objects.nonNull(p.getExpireDate())) {
                message.append(",").append(StringUtils.hasText(op) ? op : "").append("到期日期:[").append(DateUtils.date2Str(p.getExpireDate())).append("]");
            }
        });
    }

    private List<Order> trade2Orders(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Order> orderList = new ArrayList<>();
        trades.forEach(trade -> {
            if (Objects.isNull(trade)) {
                return;
            }
            List<TbOrder> orders = ((TbTrade) trade).getOrders();
            if (CollectionUtils.isEmpty(orders)) {
                return;
            }
            orders.forEach(order -> {
                order.setWarehouseId(trade.getWarehouseId());
                orderList.add(order);
                if (CollectionUtils.isEmpty(order.getSuits())) {
                    return;
                }
                order.getSuits().forEach(suit -> suit.setWarehouseId(trade.getWarehouseId()));
                orderList.addAll(order.getSuits());
            });
        });

        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>();
        }

        return orderList;
    }


    private Map<String, List<OrderStockProduct>> getOriginProduct(Staff staff, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        List<Long> noConsignment = orders.stream().filter(o -> Objects.nonNull(o) && Objects.nonNull(o.getSysConsigned()) && o.getSysConsigned() == 0).map(Order::getId).collect(Collectors.toList());
        Set<Long> consignment = orders.stream().filter(o -> Objects.nonNull(o) && Objects.nonNull(o.getSysConsigned()) && o.getSysConsigned() == 1).map(Order::getSid).collect(Collectors.toSet());
        Map<String, List<OrderStockProduct>> data = new HashMap<>();
        Map<String, List<OrderStockProduct>> consignmentData = getByOrderNum(staff, new ArrayList<>(consignment), OrderStockProductStatusEnum.STATUS_2);
        if (!CollectionUtils.isEmpty(consignmentData)) {
            data.putAll(consignmentData);
        }
        Map<String, List<OrderStockProduct>> noConsignmentData = getBySubOrderNum(staff, noConsignment, OrderStockProductStatusEnum.STATUS_1);
        if (!CollectionUtils.isEmpty(noConsignmentData)) {
            data.putAll(noConsignmentData);
        }
        return data;
    }

    public Map<String, List<OrderStockProduct>> getOriginProductNoConsiderConsign(Staff staff, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        List<Long> allIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        List<Long> allSids = orders.stream().map(Order::getSid).collect(Collectors.toList());
        Map<String, List<OrderStockProduct>> data = new HashMap<>();
        Map<String, List<OrderStockProduct>> noConsignmentData = getBySubOrderNum(staff, allIds, OrderStockProductStatusEnum.STATUS_1);
        if (!CollectionUtils.isEmpty(noConsignmentData)) {
            data.putAll(noConsignmentData);
        }
        Map<String, List<OrderStockProduct>> consignmentData = getByOrderNum(staff, new ArrayList<>(allSids), OrderStockProductStatusEnum.STATUS_2);
        if (!CollectionUtils.isEmpty(consignmentData)) {
            data.putAll(consignmentData);
        }
        return data;
    }

    private Map<String, List<OrderStockProduct>> getBySubOrderNum(Staff staff, List<Long> subOrderNum, OrderStockProductStatusEnum status) {
        if (CollectionUtils.isEmpty(subOrderNum)) {
            return null;
        }
        if (ObjectUtils.isEmpty(status)) {
            status = OrderStockProductStatusEnum.STATUS_1;
        }
        return orderStockProductService.getOrderStockProduct(staff, ProductQueryParams.builder().subOrderNumbers(subOrderNum.stream().map(String::valueOf).collect(Collectors.toList())).status(status).build());
    }

    private Map<String, List<OrderStockProduct>> getByOrderNum(Staff staff, List<Long> orderNum, OrderStockProductStatusEnum status) {
        if (CollectionUtils.isEmpty(orderNum)) {
            return null;
        }
        if (ObjectUtils.isEmpty(status)) {
            status = OrderStockProductStatusEnum.STATUS_1;
        }
        return orderStockProductService.getOrderStockProduct(staff, ProductQueryParams.builder().orderNumbers(orderNum.stream().map(String::valueOf).collect(Collectors.toList())).status(status).build());
    }

    private Map<String, List<OrderStockProduct>> getOriginProduct(Staff staff, List<Order> orders, OrderStockProductStatusEnum status) {
        if (ObjectUtils.isEmpty(orders)) {
            return null;
        }
        return getBySubOrderNum(staff, orders.stream().map(Order::getId).collect(Collectors.toList()), status);
    }

    private List<String> getDeleteProduct(Map<String, List<OrderStockProduct>> orderStockProduct, Map<String, Order> orderMaps, ReMatchContext reMatchContext) {
        //找到被拆出去的
        EventEnum event = reMatchContext.getEvent();
        Set<String> deleteData = new HashSet<>();
        if (CollectionUtils.isEmpty(orderStockProduct)) {
            return null;
        }
        orderStockProduct.forEach((k, products) -> {
            if (EventEnum.EVENT_TRADE_CHANGE_WAREHOUSE_AFTER.equals(event)) {
                deleteData.add(k);
                return;
            }
            Order order = orderMaps.get(k);
            //如果order为空，说明order已被删除，则对应的批次数据也要删除
            if (ObjectUtils.isEmpty(order)) {
                deleteData.add(k);
                return;
            }

            //仓库不一致取消锁定
            if (products.stream().anyMatch(osp -> !osp.getWarehouseId().equals(order.getWarehouseId()))) {
                deleteData.add(k);
                return;
            }

            if (products.stream().anyMatch(osp -> !osp.getOrderNumber().equals(String.valueOf(order.getSid())))) {
                deleteData.add(k);
                return;
            }

            if (products.stream().anyMatch(osp -> !osp.getSysItemId().equals(order.getItemSysId()) || (order.getSkuSysId() > 0 && !osp.getSysSkuId().equals(order.getSkuSysId())))) {
                deleteData.add(k);
                return;
            }
            long lockNum = products.stream().mapToLong(OrderStockProduct::getLockNum).sum();
            //如果锁定数和order的一致，并且sid一致，则说明没被拆出去
            if (order.getNum() == lockNum && products.stream().allMatch(osp -> osp.getOrderNumber().equals(String.valueOf(order.getSid())))) {
                return;
            }
            //order中数量不等于批次锁定数量，说明order被按数量拆分货修改了商品的数量，则对应的数据要先删除
            if (order.getNum() != lockNum) {
                deleteData.add(k);
            }
        });


        return new ArrayList<>(deleteData);
    }

    /**
     * 修改了商品数量的或者拆单、取消拆单的（沿用操作前的批次信息）
     */
    private List<OrderStockProduct> getInsertProduct(Staff staff, Map<String, List<OrderStockProduct>> orderStockProduct, List<Trade> originTrades, Map<String, Order> orderMaps, EventEnum event) {

        if (CollectionUtils.isEmpty(orderStockProduct)) {
            return null;
        }
        if (EventEnum.EVENT_TRADE_CHANGE_WAREHOUSE_AFTER.equals(event)) {
            return null;
        }
        if (EventEnum.EVENT_CHANGE_ITEM.equals(event)) {
            return isUpdate(orderStockProduct, orderMaps, event);
        }
        if ( EventEnum.EVENT_TRADE_SPLIT_UNDO_AFTER.equals(event)) {
            return isUndoSplit(orderStockProduct, orderMaps);
        }
        if (EventEnum.EVENT_TRADE_SPLIT_AFTER.equals(event)) {
            return isSplitTrade(orderStockProduct, originTrades, orderMaps);
        }
        return null;
    }

    /**
     * 拆单单后重新处理批次
     */
    private List<OrderStockProduct> isSplitTrade(Map<String, List<OrderStockProduct>> orderStockProduct, List<Trade> originTrades, Map<String, Order> orderMaps) {
        if (CollectionUtils.isEmpty(orderStockProduct) || CollectionUtils.isEmpty(orderMaps)) {
            return null;
        }

        Map<Long, List<Trade>> splitTrade = originTrades.stream().filter(t -> !ObjectUtils.isEmpty(t.getSplitSid()) && t.getSplitSid() > 0).collect(Collectors.groupingBy(Trade::getSplitSid));

        Map<Long, Trade> tradeMap = originTrades.stream().collect(Collectors.toMap(Trade::getSid, t ->t));

        List<OrderStockProduct> newOrderStockProduct = new ArrayList<>();

        orderStockProduct.forEach((orderId, osp) -> {
            Order order = orderMaps.get(orderId);
            if (ObjectUtils.isEmpty(order)) {
                return;
            }
            long lockNum = osp.stream().mapToLong(OrderStockProduct::getLockNum).sum();
            //两者数量一致，并且主单id都一样，表明该order没有被拆出
            if (lockNum == order.getNum() && osp.stream().allMatch(p -> p.getOrderNumber().equals(String.valueOf(order.getSid())))) {
                return;
            }
            //给当前order创建锁定记录
            newOrderStockProduct.addAll(order2Product(order, osp));
            //两者数量一致，那就是按sku拆分的，就不再往后执行了
            if (lockNum == order.getNum()) {
                return;
            }
            //两者数量不一致，那是进行了数量拆分,先找到被拆分出去的order
            List<Trade> trades = splitTrade.get(order.getSid());
            if (CollectionUtils.isEmpty(trades)) {
                Trade trade = tradeMap.get(order.getSid());
                trades = Objects.isNull(trade) ? null : splitTrade.get(trade.getSplitSid());
            }

            if (CollectionUtils.isEmpty(trades)) {
                return;
            }
            //被拆出去的orders
            Map<Long, List<Order>> splitOrders = trade2Orders(trades).stream().filter(o -> !o.getSid().equals(order.getSid())).collect(Collectors.groupingBy(o -> ObjectUtils.isEmpty(o.getSkuSysId()) ? o.getItemSysId() : o.getSkuSysId()));
            //根据商品找到被拆出去的order
            List<Order> orders = splitOrders.get(ObjectUtils.isEmpty(order.getSkuSysId()) ? order.getItemSysId() : order.getSkuSysId());
            if (CollectionUtils.isEmpty(orders)) {
                return;
            }
            orders.forEach(o -> {
                List<OrderStockProduct> newOsp = order2Product(o, osp);
                newOrderStockProduct.addAll(newOsp);
                if (o.getSid().equals(order.getSid()) && o.getId().equals(order.getId())) {
                    newOsp.forEach(p -> p.setMessages(buildMessage(orders)));
                }
            });
        });
        return newOrderStockProduct;
    }

    private OrderStockProductMsg buildMessage(List<Order> orders) {
        OrderStockProductMsg orderStockProductMsg = new OrderStockProductMsg();
        orderStockProductMsg.setSplitSubOrderNumbers(orders.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.toList()));
        return orderStockProductMsg;
    }

    /**
     * 修改商品/合单后处理批次
     */
    private List<OrderStockProduct> isUpdate(Map<String, List<OrderStockProduct>> orderStockProduct, Map<String, Order> orderMaps, EventEnum event) {
        if (CollectionUtils.isEmpty(orderStockProduct) || CollectionUtils.isEmpty(orderMaps)) {
            return null;
        }
        List<OrderStockProduct> newOrderStockProduct = new ArrayList<>();

        orderStockProduct.forEach((orderId, osp) -> {
            long oldLockNum = osp.stream().mapToLong(OrderStockProduct::getLockNum).sum();
            Order order = orderMaps.get(orderId);
            if (ObjectUtils.isEmpty(order)) {
                return;
            }
            if (EventEnum.EVENT_CHANGE_ITEM.equals(event) && oldLockNum == order.getNum()) {
                return;
            }
            newOrderStockProduct.addAll(order2Product(order, osp));
        });

        return newOrderStockProduct;
    }

    /**
     * 取消拆单后重新处理批次（适合单批次指定场景）
     */
    private List<OrderStockProduct> isUndoSplit(Map<String, List<OrderStockProduct>> orderStockProduct, Map<String, Order> orderMaps) {
        if (CollectionUtils.isEmpty(orderStockProduct) || CollectionUtils.isEmpty(orderMaps)) {
            return null;
        }
        List<OrderStockProduct> newOrderStockProduct = new ArrayList<>();
        orderMaps.forEach((id ,order) -> {
            List<OrderStockProduct> osp = orderStockProduct.get(id);
            if (CollectionUtils.isEmpty(osp)) {
                return;
            }
            long lockNum = osp.stream().mapToLong(OrderStockProduct::getLockNum).sum();
            //两者数量一致，并且主单id都一样，表明该order没有被拆出
            if (lockNum == order.getNum() && osp.stream().allMatch(p -> p.getOrderNumber().equals(String.valueOf(order.getSid())))) {
                return;
            }
            //仓库不一致不重新添加
            if (osp.stream().anyMatch(p -> !p.getWarehouseId().equals(order.getWarehouseId()))) {
                return;
            }
            newOrderStockProduct.addAll(order2Product(order, osp));
        });

        return newOrderStockProduct;
    }

    /**
     * 将订单转换为批次数据 （此处批次数据状态为交易指定）
     */
    private List<OrderStockProduct> order2Product(Order order, List<OrderStockProduct> products) {

        if (CollectionUtils.isEmpty(products) || ObjectUtils.isEmpty(order)) {
            return new ArrayList<>();
        }
        Integer orderNum = order.getNum();

        //新的锁定数列表 下标对应入参products的下标
        List<Long> lockNums = new ArrayList<>(products.size());
        //新的锁定总数
        AtomicLong newLockNum = new AtomicLong(0L);

        //计算新的批次占用数
        for (OrderStockProduct p : products) {
            Long lockNum = 0L;
            //新的总锁定数大于等于order商品数量的时候，后面的锁定记录就不需要再去匹配了
            if (newLockNum.get() >= orderNum) {
                break;
            }
            //当前锁定记录锁定数+新的总锁定数，小于order商品数量，则直接沿用老的锁定数
            if (p.getLockNum() + newLockNum.get() <= orderNum) {
                lockNum = p.getLockNum();
            }
            //当前锁定记录锁定数 + 新的总锁定数，大于order商品数量，则将没有分配的全给当前锁定记录
            //当最后一个锁定记录数时，新的锁定数 = order商品数量 - 新的总锁定数
            if (p.getLockNum() + newLockNum.get() > orderNum || lockNums.size() == products.size() - 1) {
                lockNum = orderNum - newLockNum.get();
            }
            lockNums.add(lockNum);
            newLockNum.addAndGet(lockNum);
            p.setLockNum(p.getLockNum() - lockNum);
        }
        AtomicInteger index = new AtomicInteger(0);
        if (CollectionUtils.isEmpty(lockNums)) {
            return new ArrayList<>();
        }
        List<OrderStockProduct> newProducts = products.subList(0, lockNums.size()).stream().map(product -> order2Product(order, product, lockNums.get(index.getAndAdd(1)))).filter(p -> p.getLockNum() != null && p.getLockNum() > 0).collect(Collectors.toList());
        products.removeIf(p -> p.getLockNum() <= 0);
        return newProducts;
    }

    /**
     * 创建一个预占用的批次记录
     */
    private OrderStockProduct newStatusAs1(OrderStockProduct source) {
        OrderStockProduct newData = OrderStockProduct.builder().build();
        BeanUtils.copyProperties(source, newData);
        newData.setStatus(OrderStockProductStatusEnum.STATUS_1.getStatus());
        return newData;
    }

    /**
     * order和原批次数据转换为要保存的批次数据
     */
    private OrderStockProduct order2Product(Order order, OrderStockProduct source, Long lockNum) {
        OrderStockProduct product = newStatusAs1(source);
        product.setLockNum(lockNum);
        product.setOrderNumber(String.valueOf(order.getSid()));
        product.setCompanyId(order.getCompanyId());
        product.setSubOrderNumber(String.valueOf(order.getId()));
        product.setSysItemId(order.getItemSysId());
        product.setSysSkuId(order.getSkuSysId());
        product.setWarehouseId(source.getWarehouseId());
        return product;
    }

    private OrderStockProduct detail4BatchProduct2Product(Detail4BatchProduct detail4BatchProduct) {
        OrderStockProduct product = new OrderStockProduct();
        product.setLockNum(0L);
        product.setSysItemId(detail4BatchProduct.getSysItemId());
        product.setSysSkuId(detail4BatchProduct.getSysSkuId());
        product.setWarehouseId(detail4BatchProduct.getWarehouseId());
        product.setInventory((long) (detail4BatchProduct.getBadNum() + detail4BatchProduct.getGoodNum()));
        product.setBatchNo(detail4BatchProduct.getBatchNo());
        product.setProductionDate(detail4BatchProduct.getProductTime());
        product.setExpireDate(detail4BatchProduct.getExpireTime());
        return product;
    }

}

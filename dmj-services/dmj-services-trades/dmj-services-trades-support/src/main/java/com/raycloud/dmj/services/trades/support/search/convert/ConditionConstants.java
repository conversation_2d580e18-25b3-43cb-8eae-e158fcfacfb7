package com.raycloud.dmj.services.trades.support.search.convert;


/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
public class ConditionConstants {

    public static final String TRADE_TABLE_PLACE_HOLDER = "@{tradeTable}";

    public static final String ORDER_TABLE_PLACE_HOLDER = "@{orderTable}";

    public static final String FORCE_INDEX_HOLDER = "@{forceIndex}";


    /**
     *  这里排序遵循以下原则
     *   1 影响后续查询条件转换的优先
     *   2 复杂且耗时较长的放后面,因为前面有可能就已经 isStopQuery 了
     */
    public static final int CONVERTER_SORT_QUERY_ID  = 0;
    public static final int CONVERTER_SORT_PRIVILEGE  = 5;
    public static final int CONVERTER_SORT_SYS_STATUS = 10;
    public static final int CONVERTER_SORT_CURSOR = 20;
    public static final int CONVERTER_SORT_NORMAL  = 30;

    public static final int CONVERTER_SORT_TID  = 40;
    public static final int CONVERTER_SORT_OUT_SID  = 50;
    public static final int CONVERTER_SORT_TRADE_TYPE = 60;
    public static final int CONVERTER_SORT_LAB_EXPT  = 70;
    public static final int CONVERTER_SORT_RECEIVER  = 80;

    public static final int CONVERTER_SORT_ORDER  = 90;




    public static final int CONVERTER_ORDER_SORT_SYS_ID  = 10;
    public static final int CONVERTER_ORDER_SORT_SYS  = 20;
    public static final int CONVERTER_ORDER_SORT_PLAT  = 30;
    public static final int CONVERTER_ORDER_SORT_MUTI = 40;

    public static final int CONVERTER_ORDER_SORT_UNIQUECODES = 50;
    public static final int CONVERTER_ORDER_SORT_ADDTIONAL  = 60;

    public static final int CONVERTER_ORDER_SUPPORT_ONLY  = 70;


}

package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.trades.search.TimeTypeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-03-20
 */
public class ConvertBase {

    /**
     * 1 精确查询
     */
    public static final int QUERY_EQUAL = 1;
    /**
     * 1 左匹配
     */
    public static final int LEFT_MATCH = 2;
    /**
     * 2 右匹配
     */
    public static final int RIGHT_MATCH = 3;
    /**
     * 0 全模糊查询
     */
    public static final int ALL_MATCH = 0;

    public  static   boolean hasTimeCondition(TradeQueryRequest condition, TimeTypeEnum type){
        return Objects.equals(condition.getTimeType(), type) && (condition.getStartTime() != null || condition.getEndTime() != null);
    }

    public  static   <E> boolean notEmpty(E[] values){
        return values != null && values.length > 0;
    }

    public  static   <E> boolean contains(E[] values,E value){
        if (values == null || values.length == 0) {
            return false;
        }
        for (E v : values) {
            if (Objects.equals(v, value)) {
                return true;
            }
        }
        return false;
    }

    public  static   <E> boolean isEmpty(E[] values){
        return !notEmpty(values);
    }

    public  static   boolean isTrue(Boolean values){
        return values != null && values;
    }

    public  static  <E> boolean notNull( E value){
        return !isNull(value) ;
    }

    public  static  <E> boolean isNull( E value){
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StringUtils.isBlank((String)value);
        }
        return false;
    }


    /**
     * <pre>
     *     当filter不为空时,以filter数据过滤target(即取两个集合的交集),否则返回target
     *     target:null OR [], filter:ANY         return:[]
     *     target:[A],        filter:null        return:[A]
     *     target:[A],        filter:[]          return:[]
     *     target:[A,B],      filter:[A]         return:[A]
     * </pre>
     *
     */
    public  static  <E> Collection<E> intersection(final Collection<E> target, final Collection<E> filter) {
        if (CollectionUtils.isEmpty(target)) {
            return new ArrayList<E>();
        }
        if (filter == null) {
            return target;
        }
        if (filter.size() == 0) {
            return new ArrayList<E>();
        }
        Collection<E> intersection = org.apache.commons.collections.CollectionUtils.intersection(target, filter);
        return intersection;
    }

    /**
     * <pre>
     *     当filter不为空时,以filter数据过滤target(即取两个集合的交集),否则返回target
     *     target:null OR [], filter:ANY         return:[]
     *     target:[A],        filter:null        return:[A]
     *     target:[A],        filter:[]          return:[]
     *     target:[A,B],      filter:[A]         return:[A]
     * </pre>
     *
     */
    public  static  <E> Collection<E> intersection(E[] target, E ... filter) {
        if (isEmpty(target)) {
            return new ArrayList<E>();
        }
        if (filter == null) {
            return new ArrayList<>(Arrays.asList(target));
        }
        if (filter.length == 0) {
            return new ArrayList<E>();
        }
        Collection<E> intersection = org.apache.commons.collections.CollectionUtils.intersection(Arrays.asList(target), Arrays.asList(filter));
        return intersection;
    }

    /**
     * <pre>
     *     当filter不为空时,以filter数据过滤target(即取两个集合的交集),否则返回target
     *     target:null OR [], filter:ANY         return:[]
     *     target:[A],        filter:null        return:[A]
     *     target:[A],        filter:[]          return:[]
     *     target:[A,B],      filter:[A]         return:[A]
     * </pre>
     *
     */
    public  static  <E> Collection<E> intersection(final Collection<E> target, E ... filter) {
        if (CollectionUtils.isEmpty(target)) {
            return new ArrayList<E>();
        }
        if (filter == null) {
            return  target;
        }
        if (filter.length == 0) {
            return new ArrayList<E>();
        }
        Collection<E> intersection = org.apache.commons.collections.CollectionUtils.intersection(target, Arrays.asList(filter));
        return intersection;
    }


    public  static <T> boolean andSingleCondition(Query query, String field, T value) {
        return singleCondition(query,"AND",field,value,1);
    }

    public  static <T> boolean andSingleCondition(Query query, String field, T value, Integer queryType) {
        return singleCondition(query,"AND",field,value,queryType);
    }

    public  static <T> boolean orSingleCondition(Query query, String field, T value) {
        return singleCondition(query,"OR",field,value,1);
    }

    public  static <T> boolean orSingleCondition(Query query, String field, T value, Integer queryType) {
        return singleCondition(query,"OR",field,value,queryType);
    }

    public  static <T> boolean singleCondition(Query query, String field, T value) {
        return singleCondition(query,"",field,value,1);
    }

    public  static <T> boolean singleCondition(Query query, String field, T value, Integer queryType) {
        return singleCondition(query,"",field,value,queryType);
    }


    private  static <T> boolean singleCondition(Query query,String prefix,String field,T value,Integer queryType) {
        if (notNull(value)) {
            if (StringUtils.isNotBlank(prefix)) {
                query.append(" ").append(prefix.trim()).append(" ");
            }else {
                query.append(" ");
            }
            query.append(field.trim());
            switch (queryType) {
                case 1:query.append(" = ? ");break;
                case 2:query.append(" LIKE CONCAT( ?, '%')");break;
                case 3:query.append(" LIKE CONCAT( '%',? )");break;
                case 0:query.append(" LIKE CONCAT('%',?, '%')");break;
            }
            query.add(value);
            return true;
        }
        return false;
    }



    public  static boolean andDateRangeQuery(Query q, String field, Date startTime, Date endTime) {
        return andDateRangeQuery(q,field,startTime,endTime,0);
    }

    /**
     * <pre>
     * type
     *             0 不包含边界值 < val <
     *             1 包含左边界  <= val <
     *             2 包含右边界  < val <=
     *             3 包含边界    <= val <=
     * </pre>
     */
    public  static boolean andDateRangeQuery(Query q, String field, Date startTime, Date endTime,int type) {
        if (startTime == null && endTime == null) {
            return false;
        }
        if (startTime != null) {
            q.append(" AND ").append(field).append((type == 1 || type==3)?">=":">").append(" ? ").add(startTime);
        }
        if (endTime != null) {
            q.append(" AND ").append(field).append((type == 2 || type==3)?"<=":"<").append(" ? ").add(endTime);
        }
        return true;
    }




    public  static  <T> boolean andNotNullQuery(Query q, String field){
        q.append(" AND ").append(" (").append(field).append(" IS NOT NULL AND ").append(field).append(" <> '' ").append(") ");
        return true;
    }

    public  static  <T> boolean andNullQuery(Query q, String field){
        q.append(" AND ").append(" (").append(field).append(" IS NULL OR ").append(field).append(" = '' ").append(") ");
        return true;
    }


    public  static  <T> boolean listCondition(Query query, String field, T[] values) {
        if (notEmpty(values)) {
            return listCondition(query,"",field,1,Arrays.asList(values));
        }
        return false;
    }

    public  static <T> boolean listCondition(Query query, String field, Collection<T> values) {
        return listCondition(query,"",field,1,values);
    }

    public  static  <T> boolean listCondition(Query query, String field, T[] values, Integer queryType) {
        if (notEmpty(values)) {
            return listCondition(query,"",field,queryType,Arrays.asList(values));
        }
        return false;
    }

    public  static <T> boolean listCondition(Query query, String field, Collection<T> values, Integer queryType) {
        return listCondition(query,"",field,queryType,values);
    }

    public  static boolean andListCondition(Query query, String field, Object[] values, Integer queryType) {
        if (notEmpty(values)) {
            return listCondition(query,"AND",field,queryType,Arrays.asList(values));
        }
        return false;
    }

    public  static <T> boolean andListCondition(Query query, String field, Object[] values) {
        if (notEmpty(values)) {
            return listCondition(query,"AND",field,1,Arrays.asList(values));
        }
        return false;
    }

    public  static <T> boolean andListCondition(Query query, String field, Collection<T> values, Integer queryType) {
        return listCondition(query,"AND",field,queryType,values);
    }

    public  static <T> boolean andListCondition(Query query, String field, Collection<T> values) {
        return listCondition(query,"AND",field,1,values);
    }



    public  static boolean orListCondition(Query query, String field, Object[] values, Integer queryType) {
        if (notEmpty(values)) {
            return listCondition(query,"OR",field,queryType,Arrays.asList(values));
        }
        return false;
    }

    public  static <T> boolean orListCondition(Query query, String field, Collection<T> values, Integer queryType) {
        return listCondition(query,"OR",field,queryType,values);
    }

    /**
     *
     * @param type 0 全模糊查询 1 精确查询 2 左匹配  3 右匹配
     * @param <T>
     */
    public  static <T> boolean listCondition(Query q, String prefix,String field,int type, Collection<T>  list){
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        List<T> filterd = new ArrayList<>();
        for (T v : list) {
            if (v == null) {
                continue;
            }
            filterd.add(v);
        }

        if (filterd.size() == 0) {
            throw new IllegalArgumentException("["+field+"]集合条件无效:" + list);
        }
        if (filterd.size() == 1) {
            return singleCondition(q,prefix,field,filterd.get(0),type);
        }

        if (StringUtils.isNotBlank(prefix)) {
            q.append(" ").append(prefix.trim()).append(" ");
        }else {
            q.append(" ");
        }

        if (type == 1) {
            StringBuilder buf = new StringBuilder();
            int n = 0;
            for (T v : filterd) {
                if (buf.length() > 0) {
                    buf.append(",");
                }
                buf.append("?");
            }
            q.append(field.trim()).append(" IN (").append(buf).append(")").add(filterd);
        }else {
            StringBuilder buf = new StringBuilder();
            for (T v : filterd) {
                if (buf.length() > 0) {
                    buf.append(" OR ");
                }
                buf.append(field.trim());
                switch (type) {
                    case 2:buf.append(" LIKE CONCAT( ?, '%')");break;
                    case 3:buf.append(" LIKE CONCAT( '%',? )");break;
                    case 0:buf.append(" LIKE CONCAT('%',?, '%')");break;
                }
            }
            q.append("( ").append(buf.toString()).append(")").add(filterd);
        }
        return true;

    }

}

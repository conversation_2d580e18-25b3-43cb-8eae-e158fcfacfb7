package com.raycloud.dmj.services.trades.support;

import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.trade.TradeBlackDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeBlack;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.download.domain.DownloadResult;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.download.service.IDownloadCenterCallback;
import com.raycloud.dmj.download.service.IDownloadCenterFileService;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trades.ITradeBlackService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.support.DefaultEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yangheng on 17/6/27.
 * 黑名单配置
 */
@Service
public class TradeBlackService implements ITradeBlackService {

    private final Logger logger = Logger.getLogger(TradeBlackService.class);

    @Resource
    TradeBlackDao tradeBlackDao;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    DefaultEventCenter eventCenter;

    @Resource
    IDownloadCenterFileService downloadCenterFileService;

    private static final int IMPORT_BATCH_SIZE = 500;

    private static final String[] EXPORT_BLACK_ITEM = {"买家昵称", "备注"};

    /**
     * 查询黑名单配置详情
     */
    @Override
    public List<TradeBlack> query(Staff staff, String buyerNick, Page page) {
        return tradeBlackDao.query(staff, buyerNick, page);
    }

    /**
     * 查询黑名单配置总数
     */
    @Override
    public Long queryCount(Staff staff, String buyerNick) {
        return tradeBlackDao.queryCount(staff, buyerNick);
    }

    /**
     * 新增黑名单配置
     */
    @Override
    public void add(Staff staff, Set<String> buyerNicks, String remark) {
        List<String> list = filterExistsBuyerNicks(staff, buyerNicks);
        if (CollectionUtils.isNotEmpty(list)) {
            tradeBlackDao.batchInsert(staff, initAdd(staff, list, remark));
        }
    }

    /**
     * 导入黑名单
     */
    @Override
    public void importBlack(Staff staff, String[][] data) {
        Set<String> buyerNicks = new HashSet<>();
        Map<String, String> blackMap = new HashMap<>();
        data2BuyerNick(data, buyerNicks, blackMap);
        List<String> list = filterExistsBuyerNicks(staff, buyerNicks);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<TradeBlack> tradeBlacks = initAdd(staff, list, blackMap);
        if (tradeBlacks.size() <= IMPORT_BATCH_SIZE) {
            tradeBlackDao.batchInsert(staff, tradeBlacks);
            return;
        }
        int loop = tradeBlacks.size() / IMPORT_BATCH_SIZE + ((tradeBlacks.size() % IMPORT_BATCH_SIZE == 0) ? 0 : 1);
        for (int i = 0; i < loop; i++) {
            int fromIndex = i * IMPORT_BATCH_SIZE;
            int endIndex = Math.min((fromIndex + IMPORT_BATCH_SIZE), tradeBlacks.size());
            tradeBlackDao.batchInsert(staff, tradeBlacks.subList(fromIndex, endIndex));
        }
    }

    private List<TradeBlack> initAdd(Staff staff, List<String> buyerNicks, Map<String, String> balckMap) {
        List<TradeBlack> tradeBlacks = new ArrayList<>();
        for (String buyerNick : buyerNicks) {
            tradeBlacks.add(buyerNick2TradeBlack(staff, buyerNick, balckMap.get(buyerNick)));
        }
        return tradeBlacks;
    }

    private List<TradeBlack> initAdd(Staff staff, List<String> buyerNicks, String remark) {
        List<TradeBlack> tradeBlacks = new ArrayList<>();
        for (String buyerNick : buyerNicks) {
            buyerNick = StringUtils.strip(buyerNick);
            if (StringUtils.isEmpty(buyerNick)) {
                continue;
            }
            tradeBlacks.add(buyerNick2TradeBlack(staff, buyerNick, remark));
        }
        return tradeBlacks;
    }

    private void data2BuyerNick(String[][] data, Set<String> set, Map<String, String> map) {
        for (String[] buyerNicks : data) {
            String buyerNick = StringUtils.strip(buyerNicks[0]);
            if (StringUtils.isEmpty(buyerNick)) {
                continue;
            }
            set.add(buyerNick);
            if (buyerNicks.length > 1) {
                map.put(buyerNick, StringUtils.trimToEmpty(buyerNicks[1]));
            } else {
                map.put(buyerNick, "");
            }
        }
    }

    /**
     * 删除黑名单配置
     */
    @Override
    public void delete(Staff staff, Long... ids) {
        tradeBlackDao.batchDelete(staff, initDelete(staff, ids));
    }


    private List<TradeBlack> initDelete(Staff staff, Long... ids) {
        List<TradeBlack> tradeBlacks = new ArrayList<>();
        for (Long id : ids) {
            tradeBlacks.add(id2TradeBlack(staff, id));
        }
        return tradeBlacks;
    }

    /**
     * 校验订单是否是黑名单
     */
    @Override
    public void check(Staff staff, List<Trade> trades) {
        try {
            Integer openBlack = tradeConfigService.get(staff).getOpenBlack();
            //没有开启黑名单
            if (NumberUtils.nvlInteger(openBlack, 0) == 0) {
                return;
            }
            BlackCheckData blackCheckData = filterCheck(trades);
            if (blackCheckData.buyerNicks.isEmpty()) {
                return;
            }

            List<TradeBlack> tradeBlacks = tradeBlackDao.query(staff, blackCheckData.buyerNicks, null);
            if (CollectionUtils.isEmpty(tradeBlacks)) {
                return;
            }

            Map<String, List<Trade>> buyer_nick_trades = blackCheckData.buyer_nick_trades;
            for (TradeBlack tradeBlack : tradeBlacks) {
                List<Trade> checkTrades = buyer_nick_trades.get(StringUtils.strip(tradeBlack.getBuyerNick()));
                if (CollectionUtils.isEmpty(checkTrades)) {
                    continue;
                }
                for (Trade checkTrade : checkTrades) {
                    TradeExceptUtils.updateExcept(staff, checkTrade, ExceptEnum.BLACK_NICK, 1L);
                    blackCheckData.blackTrades.add(checkTrade);
                }
            }

            if (!blackCheckData.blackTrades.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("trade.black.mark").setArgs(new Object[]{staff}), blackCheckData.blackTrades);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "校验订单是否是黑名单"), e);
        }

    }

    @Override
    public String[][] exportBlack(Staff staff, List<String> buyerNicks, Page page) {
        if (page == null) {
            page = new Page();
            page.setPageSize(100);
            page.setPageNo(1);
        }
        List<TradeBlack> tradeBlackList = tradeBlackDao.query(staff, buyerNicks, page);
        if (CollectionUtils.isEmpty(tradeBlackList)) {
            return null;
        }
        List<String[]> resultList = new ArrayList<>();
        for (TradeBlack tradeBlack : tradeBlackList) {
            List<String> row = Lists.newArrayListWithCapacity(EXPORT_BLACK_ITEM.length);
            row.add(tradeBlack.getBuyerNick());
            row.add(tradeBlack.getRemark());
            resultList.add(row.toArray(new String[EXPORT_BLACK_ITEM.length]));
        }
        String[][] result = resultList.toArray(new String[resultList.size()][]);

        IDownloadCenterCallback callback = downloadParam -> {
            DownloadResult downloadResult = new DownloadResult();
            downloadResult.setFlag(false);
            downloadResult.setData(result);
            return downloadResult;
        };
        String[][] excelTitle = new String[1][];
        excelTitle[0] = EXPORT_BLACK_ITEM;
        FileDownloadParam param = new FileDownloadParam();
        param.setFileName("黑名单导出" + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls");
        param.setExcelTitle("黑名单导出");
        param.setTitleArr(excelTitle);
        param.setModule(EnumDownloadCenterModule.TRADE.getCode());

        try {
            downloadCenterFileService.exportExcel(staff, param, callback);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("%s模块导出黑名单失败", param.getModule())), e);
        }
        return result;
    }

    private BlackCheckData filterCheck(List<Trade> trades) {
        BlackCheckData blackCheckData = new BlackCheckData();
        for (Trade trade : trades) {
            if (TradeUtils.isAfterSendGoods(trade)) {
                continue;
            }
            blackCheckData.addCheckTrade(trade);
        }
        return blackCheckData;
    }

    /**
     * 新增的时候过滤需要已经存在的黑名单
     */
    private List<String> filterExistsBuyerNicks(Staff staff, Set<String> buyerNicks) {

        List<String> list = new ArrayList<>(buyerNicks);
        List<TradeBlack> tradeBlacks = tradeBlackDao.query(staff, list, null);
        if (CollectionUtils.isEmpty(tradeBlacks)) {
            return list;
        }

        for (TradeBlack tradeBlack : tradeBlacks) {
            list.remove(tradeBlack.getBuyerNick());
        }

        return list;
    }

    private TradeBlack buyerNick2TradeBlack(Staff staff, String buyerNick, String remark) {
        TradeBlack tradeBlack = initTradeBlack(staff);
        tradeBlack.setBuyerNick(buyerNick);
        tradeBlack.setRemark(remark);
        return tradeBlack;
    }

    private TradeBlack id2TradeBlack(Staff staff, Long id) {
        TradeBlack tradeBlack = initTradeBlack(staff);
        tradeBlack.setId(id);
        return tradeBlack;
    }

    private TradeBlack initTradeBlack(Staff staff) {
        TradeBlack tradeBlack = new TradeBlack();
        tradeBlack.setCompanyId(staff.getCompanyId());
        tradeBlack.setDbNo(staff.getDbInfo().getTradeBlackDbNo());
        return tradeBlack;
    }

    private static class BlackCheckData {

        /**
         * 需要检查的黑名单
         */
        List<String> buyerNicks = new ArrayList<>();

        /**
         * 需要校验黑名单的订单
         */
        Map<String, List<Trade>> buyer_nick_trades = new HashMap<>();

        /**
         * 黑名单订单
         */
        List<Trade> blackTrades = new ArrayList<>();

        /**
         * 添加需要检查黑名单的订单
         */
        public void addCheckTrade(Trade trade) {
            String buyerNick = com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(StringUtils.strip(trade.getBuyerNick()));
            if (StringUtils.isEmpty(buyerNick)) {
                return;
            }
            buyer_nick_trades.computeIfAbsent(buyerNick, k -> new ArrayList<>()).add(trade);
            if (!buyerNicks.contains(buyerNick)) {
                buyerNicks.add(buyerNick);
            }
        }

    }
}

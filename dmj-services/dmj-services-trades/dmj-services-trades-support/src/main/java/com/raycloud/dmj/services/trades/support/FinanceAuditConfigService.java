package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.common.SimpleTrade;
import com.raycloud.dmj.business.common.SpelConditionBusiness;
import com.raycloud.dmj.business.operate.PresellItemBusiness;
import com.raycloud.dmj.business.trade.TradePaymentSwitchBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.trade.FinanceAuditConfigDao;
import com.raycloud.dmj.dao.trade.PresellItemDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.enums.TradeBusinessRuleEnum;
import com.raycloud.dmj.domain.trade.enums.TradeRuleOperationTypeEnum;
import com.raycloud.dmj.domain.trade.finance.FinanceAuditConfig;
import com.raycloud.dmj.domain.trade.finance.FinanceConfigData;
import com.raycloud.dmj.domain.trade.finance.FinanceUtils;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trade.rule.convert.FinanceAuditConvertUtils;
import com.raycloud.dmj.domain.trade.utils.PresellItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.DMJItemUtils;
import com.raycloud.dmj.domain.utils.DmjSkuUtils;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.services.trades.IFinanceAuditConfigService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.raycloud.dmj.utils.CommonUtils.replaceVal;

@Service
public class FinanceAuditConfigService implements IFinanceAuditConfigService {

    @Resource
    FinanceAuditConfigDao financeAuditConfigDao;

    @Resource
    PresellItemDao presellItemDao;

    @Resource
    IItemServiceWrapper itemServiceWrapper;

    @Resource
    PresellItemBusiness presellItemBusiness;

    @Resource
    ICache cache;

    @Resource
    SpelConditionBusiness spelConditionBusiness;

    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IdWorkerService idWorkerService;

    @Resource
    TradePayBusiness tradePayBusiness;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeRuleService tradeRuleService;

    private final SpelExpressionParser parser = new SpelExpressionParser();

    @Cacheable(value = "defaultCache#600", key = "'all_finance_audit_config_' + #staff.companyId  +'_' +#queryItem")
    public List<FinanceAuditConfig> getConfigs(Staff staff, int queryItem) {
        List<FinanceAuditConfig> list = financeAuditConfigDao.list(staff);
        fillItems(staff, list, queryItem);
        return list;
    }

    public void fillItems(Staff staff, List<FinanceAuditConfig> rules, int queryItem) {
        if (!rules.isEmpty() && queryItem >= 1) {
            rules.forEach(rule -> rule.setItems(presellItemDao.queryItemsByRuleId(staff, rule.getId(), PresellItemUtils.RULE_TYPE_FINANCE)));
            if (queryItem >= 2) {
                fillItemDetail(staff, rules);
            }
        }
    }


    private void fillItemDetail(Staff staff, List<FinanceAuditConfig> rules) {
        List<Long> deleteIds = new ArrayList<>();
        for (FinanceAuditConfig rule : rules) {
            if (rule.getItems() == null) {
                rule.setItems(presellItemDao.queryItemsByRuleId(staff, rule.getId(), PresellItemUtils.RULE_TYPE_FINANCE));
            }
            if (!rule.getItems().isEmpty()) {
                List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
                for (PresellItem item : rule.getItems()) {
                    sysItemIds.add(item.getSysItemId());
                    if (item.getSysSkuId() > 0) {
                        sysSkuIds.add(item.getSysSkuId());
                    }
                }
                Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(itemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId,title,shortTitle,outerId,remark,type"));
                Map<Long, DmjSku> skuMap = DmjSkuUtils.skuList2Map(itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId,sysSkuId,outerId,propertiesName,propertiesAlias,remark,type"));
                for (PresellItem item : rule.getItems()) {
                    DmjItem dmjItem = itemMap.get(item.getSysItemId());
                    if (dmjItem == null) {
                        deleteIds.add(item.getId());
                        continue;
                    }
                    item.setOuterId(dmjItem.getOuterId());
                    item.setRemark(dmjItem.getRemark());
                    item.setTitle(dmjItem.getTitle());
                    item.setShortTitle(dmjItem.getShortTitle());
                    item.setPicPath(dmjItem.getPicPath());
                    if (item.getSysSkuId() > 0) {
                        DmjSku dmjSku = skuMap.get(item.getSysSkuId());
                        if (dmjSku == null) {
                            deleteIds.add(item.getId());
                            continue;
                        }
                        item.setOuterId(dmjSku.getOuterId());
                        item.setRemark(dmjSku.getRemark());
                        item.setSkuPropertiesName(dmjSku.getPropertiesName());
                        item.setSkuPropertiesAlias(dmjSku.getPropertiesAlias());
                        if (dmjSku.getPicPath() != null && !StockConstants.PATH_NO_PIC.equals(dmjSku.getPicPath())) {
                            item.setPicPath(dmjSku.getPicPath());
                        }
                    }
                }
            }
        }
        //如果存在已删除的商品货sku仍然在配置中,需要将配置删除
        if (!deleteIds.isEmpty()) {
            presellItemDao.deleteByIds(staff, deleteIds, PresellItemUtils.RULE_TYPE_FINANCE, null, null, null);
        }
    }

    @Cacheable(value = "defaultCache#600", key = "'finance_audit_config_' + #staff.companyId + '_' + #id")
    public FinanceAuditConfig get(Staff staff, Long id) {
        FinanceAuditConfig financeAuditConfig = financeAuditConfigDao.get(staff, id);
        Assert.notNull(financeAuditConfig, "财审规则不存在或者已经被删除，刷新后重试");
        List<FinanceAuditConfig> financeAuditConfigs = Collections.singletonList(financeAuditConfig);
        fillItems(staff, financeAuditConfigs, 2);
        return financeAuditConfigs.get(0);
    }

    //财审规则保存初始化与检查
    private void saveInitAndCheck(Staff staff, FinanceAuditConfig financeAuditConfig) {
        Assert.notNull(financeAuditConfig, "请求参数为空");
        checkName(staff, financeAuditConfig);

        // 开关默认值处理
        if (financeAuditConfig.getIsOpen() == null) {
            financeAuditConfig.setIsOpen(1);
        }

        //-----------店铺相关参数校验
        String userIdsStr = StringUtils.join(financeAuditConfig.getUserIds(), ',');
        Assert.isTrue(StringUtils.length(userIdsStr) < 1024, "已超出店铺添加数量，请重新选择");
        financeAuditConfig.setUserIdsStr(userIdsStr);//存入店铺Ids字符串

        //-----------仓库
        String warehouseIdsStr = StringUtils.join(financeAuditConfig.getWarehouseIds(), ',');
        financeAuditConfig.setWarehouseIdsStr(warehouseIdsStr);//存入仓库Ids字符串

        //-----------分销商相关参数校验
        String distributorIdsStr = StringUtils.join(financeAuditConfig.getDistributorIds(), ',');
        Assert.isTrue(StringUtils.length(distributorIdsStr) < 1000, "分销商Id长度超出限制");
        financeAuditConfig.setDistributorIdsStr(distributorIdsStr);//存入分销商Ids字符串

        //校验设置条件是否填写正确,主要是针对输入框与范围值
        if (CollectionUtils.isNotEmpty(financeAuditConfig.getConditions())) {
            spelConditionBusiness.validateCondition(financeAuditConfig.getConditions());
        }
    }

    private void checkName(Staff staff, FinanceAuditConfig financeAuditConfig) {
        String name = StringUtils.trimToEmpty(financeAuditConfig.getName());
        if (StringUtils.isBlank(name)) {
            throw new IllegalArgumentException("规则名称不能为空");
        }
        List<FinanceAuditConfig> financeAuditConfigs = financeAuditConfigDao.list(staff);
        if (CollectionUtils.isNotEmpty(financeAuditConfigs)) {
            for (FinanceAuditConfig auditConfig : financeAuditConfigs) {
                if (StringUtils.equals(name, auditConfig.getName()) && (financeAuditConfig.getId() == null || financeAuditConfig.getId() - auditConfig.getId() != 0)) {
                    throw new IllegalArgumentException("名称[" + name + "]已经存在，请修改名称！");
                }
            }
        }
    }

    public FinanceAuditConfig add(Staff staff, FinanceAuditConfig financeAuditConfig) {
        //保存初始化与检查
        saveInitAndCheck(staff, financeAuditConfig);
        //生成分布式主键Id
        financeAuditConfig.setId(idWorkerService.nextId());
        //商品相关规则
        if (CollectionUtils.isNotEmpty(financeAuditConfig.getItems())) {
            for (PresellItem item : financeAuditConfig.getItems()) {
                item.setRuleType(PresellItemUtils.RULE_TYPE_FINANCE);
                item.setRuleId(financeAuditConfig.getId());
            }
            presellItemBusiness.checkItems(staff, financeAuditConfig.getItems());
            presellItemDao.batchInsert(staff, financeAuditConfig.getItems());
        }
        financeAuditConfigDao.insert(staff, financeAuditConfig);
        clearCache(staff);
        financeAuditConfig.setBusinessType(TradeBusinessRuleEnum.FINANCE_AUDIT_RULE.getBusinessId());
        tradeRuleService.initTradeRule(staff, financeAuditConfig);
        return financeAuditConfig;
    }

    private void clearCache(Staff staff) {
        try {
            cache.delete("all_finance_audit_config_" + staff.getCompanyId() + "_" + 0);
            cache.delete("all_finance_audit_config_" + staff.getCompanyId() + "_" + 1);
            cache.delete("all_finance_audit_config_" + staff.getCompanyId() + "_" + 2);
        } catch (CacheException e) {
            Logs.error("清除缓存失败");
        }
    }


    @CacheEvict(value = "defaultCache", key = "'finance_audit_config_' + #staff.companyId+'_' + #financeAuditConfig.id")
    public void update(Staff staff, FinanceAuditConfig financeAuditConfig) {
        //校验要修改的财审规则是否存在
        FinanceAuditConfig origin = get(staff, financeAuditConfig.getId());
        if (StringUtils.isBlank(financeAuditConfig.getName())) {
            financeAuditConfig.setName(origin.getName());
        }
        //保存初始化与检查
        saveInitAndCheck(staff, financeAuditConfig);
        //修改规则
        financeAuditConfigDao.update(staff, financeAuditConfig);
        //校验用户选取的规则是否正确,选择指定商品相关选项 商品列表不能为空
        List<PresellItem> items = financeAuditConfig.getItems();
        int itemType = financeAuditConfig.getItemType() == null || financeAuditConfig.getItemType() > 2 || financeAuditConfig.getItemType() < 0 ? 0 : financeAuditConfig.getItemType();
        if (itemType > 0 && CollectionUtils.isEmpty(items)) {
            throw new IllegalArgumentException("商品信息错误,请选择商品后重试");
        }
        //修改添加删除商品相关规则
        if (CollectionUtils.isNotEmpty(items)) {
            presellItemDao.deleteByRuleId(staff, financeAuditConfig.getId(), PresellItemUtils.RULE_TYPE_FINANCE);
            for (PresellItem item : items) {
                item.setRuleType(PresellItemUtils.RULE_TYPE_FINANCE);
                item.setRuleId(financeAuditConfig.getId());
            }
            presellItemBusiness.checkItems(staff, items);
            presellItemDao.batchInsert(staff, items);
        }
        clearCache(staff);
        //老的不存在，先保存一份老的数据
        origin.setBusinessType(TradeBusinessRuleEnum.FINANCE_AUDIT_RULE.getBusinessId());
        if (StringUtils.isBlank(origin.getName())) {
            origin.setName(financeAuditConfig.getName());
        }
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, origin);
        if (financeAuditConfig.getEnableStatus() == null) {
            financeAuditConfig.setEnableStatus(origin.getEnableStatus());
        }
        //更新
        tradeRuleService.modify(staff, FinanceAuditConvertUtils.convert(staff, financeAuditConfig, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    @CacheEvict(value = "defaultCache", key = "'finance_audit_config_' + #staff.companyId+'_' + #id")
    @Override
    public void updateOpen(Staff staff, Long id, Integer isOpen) {
        //校验要修改的财审规则是否存在
        FinanceAuditConfig oldConfig = get(staff, id);
        FinanceAuditConfig newConfig = new FinanceAuditConfig();
        newConfig.setId(id);
        newConfig.setIsOpen(isOpen);
        financeAuditConfigDao.update(staff, newConfig);

        oldConfig.setBusinessType(TradeBusinessRuleEnum.FINANCE_AUDIT_RULE.getBusinessId());
        if (StringUtils.isBlank(oldConfig.getName())) {
            oldConfig.setName(NumberUtils.long2Str(id));
        }
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, oldConfig);
        oldConfig.setIsOpen(isOpen);
        tradeRuleService.modify(staff, FinanceAuditConvertUtils.convert(staff, oldConfig, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    @CacheEvict(value = "defaultCache", key = "'finance_audit_config_' + #staff.companyId+'_' + #financeAuditConfig.id")
    public void delete(Staff staff, FinanceAuditConfig financeAuditConfig) {
        FinanceAuditConfig oldConfig = get(staff, financeAuditConfig.getId());
        financeAuditConfigDao.delete(staff, financeAuditConfig.getId());
        oldConfig.setBusinessType(TradeBusinessRuleEnum.FINANCE_AUDIT_RULE.getBusinessId());
        tradeRuleService.remove(staff, oldConfig);
        clearCache(staff);
    }

    /**
     * @return 返回需要财务审核的订单
     */
    @Override
    public List<Trade> tradeCheck(Staff staff, List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return new ArrayList<>();
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (tradeConfig.getOpenFinanceAudit() == null || tradeConfig.getOpenFinanceAudit() <= 0) {
            return new ArrayList<>();
        }
        FinanceConfigData financeConfigData = FinanceUtils.wrapFinanceConfig(staff, getConfigs(staff, 1));
        if ((financeConfigData.getUserFinanceMap() == null || financeConfigData.getUserFinanceMap().size() == 0) && (financeConfigData.getGxFinanceMap() == null || financeConfigData.getGxFinanceMap().size() == 0)) {
            return new ArrayList<>();
        }

        Map<Long, List<Trade>> userId2Trades = new HashMap<>();
        Map<Long, List<Trade>> taobaoId2Trades = new HashMap<>();
        for (Trade trade : tradeList) {
            userId2Trades.computeIfAbsent(trade.getUserId(), k -> new ArrayList<>()).add(trade);
            if (TradeUtils.isGxTrade(trade)) {
                taobaoId2Trades.computeIfAbsent(trade.getTaobaoId(), k -> new ArrayList<>()).add(trade);
            }
        }

        Map<Long, Trade> sidFinanceTradeMap = new HashMap<>();
        matchUser(staff, tradeConfig, userId2Trades, financeConfigData.getUserFinanceMap(), sidFinanceTradeMap);
        matchUser(staff, tradeConfig, taobaoId2Trades, financeConfigData.getGxFinanceMap(), sidFinanceTradeMap);
        return sidFinanceTradeMap.size() > 0 ? new ArrayList<>(sidFinanceTradeMap.values()) : new ArrayList<>();
    }

    private void matchUser(Staff staff, TradeConfig tradeConfig, Map<Long, List<Trade>> userTradeMap, Map<Long, List<FinanceAuditConfig>> userFinanceMap, Map<Long, Trade> sidFinanceTradeMap) {
        if (userFinanceMap == null || userFinanceMap.size() == 0) {
            return;
        }
        for (Map.Entry<Long, List<Trade>> entry : userTradeMap.entrySet()) {
            List<FinanceAuditConfig> userConfigs = FinanceUtils.getFinanceConfig(staff, entry.getKey(), userFinanceMap);
            if (CollectionUtils.isEmpty(userConfigs)) {
                continue;
            }
            Map<Long, Trade> mergeMainTradeMap = new HashMap<>();
            List<Long> sids = Lists.newArrayList();
            for (Trade trade : entry.getValue()) {
                sids.add(trade.getSid());
            }
            Map<Long, Integer> tradePayMaps = tradePayBusiness.tradePaysGet(staff, sids);
            for (Trade trade : entry.getValue()) {
                if (sidFinanceTradeMap.containsKey(trade.getSid())) {
                    continue;
                }
                if (trade.getMergeSid() > 0) {
                    if (trade.getMergeSid() - trade.getSid() == 0) {
                        mergeMainTradeMap.put(trade.getSid(), trade);
                    }
                } else {
                    if (TradePaymentSwitchBusiness.switchPayment(staff, tradeConfig, trade, () -> check(staff, trade, userConfigs, tradePayMaps.get(trade.getSid())))) {
                        sidFinanceTradeMap.put(trade.getSid(), trade);
                    }
                }
            }
            tradeCheckMerge(staff, mergeMainTradeMap, userConfigs, sidFinanceTradeMap, tradePayMaps);
        }
    }

    private void tradeCheckMerge(Staff staff, Map<Long, Trade> mergeMainTradeMap, List<FinanceAuditConfig> userConfigs, Map<Long, Trade> sidFinanceTradeMap, Map<Long, Integer> tradePayMaps) {
        if (mergeMainTradeMap == null || mergeMainTradeMap.size() == 0) {
            return;
        }
        Map<Long, List<Trade>> dbMergeTradeMap = TradeUtils.groupByMergeSid(tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, mergeMainTradeMap.keySet().toArray(new Long[0])));
        for (Map.Entry<Long, Trade> entry : mergeMainTradeMap.entrySet()) {
            long mergeSid = entry.getKey();
            List<Trade> trades = dbMergeTradeMap.get(mergeSid);
            if (trades == null || trades.size() == 0) {
                Logs.ifDebug(LogHelper.buildLog(staff, "财神规则判断合单信息不存在，mergeSid=" + mergeSid));
                continue;
            }

            Trade mainTrade = entry.getValue();
            double payment = 0D;
            double acPayment = 0D;
            List<Order> allOrders = Lists.newArrayList();
            for (Trade trade : trades) {
                payment += NumberUtils.str2Double(trade.getPayment());
                acPayment += NumberUtils.str2Double(trade.getAcPayment());
                allOrders.addAll(TradeUtils.getOrders4Trade(trade));
            }
            double cost = mainTrade.getCost() != null ? mainTrade.getCost() : 0D;
            double packmaCost = mainTrade.getPackmaCost() != null ? mainTrade.getPackmaCost() : 0D;
            double postFee = StringUtils.isNoneBlank(mainTrade.getActualPostFee()) ? Double.parseDouble(mainTrade.getActualPostFee()) : 0D;
            double grossProfit = new BigDecimal(payment - cost - packmaCost - postFee).setScale(2, RoundingMode.HALF_UP).doubleValue();
            double paymentDiff = new BigDecimal(payment - acPayment).setScale(2, RoundingMode.HALF_UP).doubleValue();
            for (Trade trade : trades) {
                trade.setGrossProfit(grossProfit);
                trade.setPaymentDiff(paymentDiff);
                // KMERP-232774: 合单校验规则需要看做一个整体进行校验
                boolean check = replaceVal(trade::setPayment, trade.getPayment(), String.valueOf(payment), () -> {  // 替换payment进行计算
                    return replaceVal(os -> TradeUtils.setOrders(trade, os), TradeUtils.getOrders4Trade(trade), allOrders, () -> {  // 替换orders进行计算
                        return check(staff, trade, userConfigs, tradePayMaps.get(trade.getSid()));
                    });
                });
                if (check) {
                    sidFinanceTradeMap.put(mainTrade.getSid(), mainTrade);
                    break;
                }
            }
        }
    }

    @Override
    public String translateCondition(Staff staff, FinanceAuditConfig config) {
        StringBuilder descStr = new StringBuilder();
        int index = 1;
        //订单类型描述
        String tradeType = getTradeType(config);
        if (StringUtils.isNotBlank(tradeType)) {
            descStr.append(index).append("、");
            descStr.append(tradeType);
            descStr.append(';');
            index++;//条件索引
        }
        //分销商描述
        if (StringUtils.isNotBlank(config.getDistributorIdsStr())) {
            descStr.append(index).append("、");
            descStr.append("包含指定分销商");
            descStr.append(';');
            index++;//条件索引
        }
        //条件规则描述
        descStr.append(spelConditionBusiness.translateCondition(staff, config.getConditions() == null ? new ArrayList<>() : config.getConditions(), index));

        //商品规则描述
        List<PresellItem> items = config.getItems();
        if (CollectionUtils.isNotEmpty(items) && config.getItemType() != null) {
            List<String> itemOuterIds = new ArrayList<>();
            for (int i = 0; i < items.size(); i++) {
                if (i > 100) {
                    continue;
                }
                itemOuterIds.add(items.get(i).getOuterId());
            }
            if (1 == config.getItemType()) {
                descStr.append("指定商品：").append(StringUtils.join(itemOuterIds, ","));
            } else if (2 == config.getItemType()) {
                descStr.append("排除商品：").append(StringUtils.join(itemOuterIds, ","));
            }
        }
        return descStr.toString();
    }

    private String getTradeType(FinanceAuditConfig config) {
        if (config != null) {
            String tradeType = config.getTradeType();
            if (StringUtils.isEmpty(tradeType)) {
                return "全部订单";
            }
            List<String> result = new ArrayList<>();
            String type = "," + tradeType + ",";
            if (type.contains(",platform,")) {
                result.add("平台订单");
            }
            if (type.contains(",sys,")) {
                result.add("手工订单");
            }
            if (type.contains(",changeitem,")) {
                result.add("换货订单");
            }
            if (type.contains(",reissue,")) {
                result.add("补发订单");
            }
            if (type.contains(",dangkou,")) {
                result.add("档口订单");
            }
            if (type.contains(",fenxiao,")) {
                result.add("分销订单");
            }
            if (CollectionUtils.isNotEmpty(result)) {
                return StringUtils.join(result, ",");
            }
        }
        return "";
    }

    private boolean check(Staff staff, Trade trade, List<FinanceAuditConfig> userConfigs, Integer tradePayCount) {
        for (FinanceAuditConfig config : userConfigs) {
            if (check(staff, trade, config, tradePayCount)) {
                return true;
            }
        }
        return false;
    }

    private boolean check(Staff staff, Trade trade, FinanceAuditConfig userConfigs, Integer tradePayCount) {
        return tradeWarehouseMatch(trade, userConfigs)
                && tradeTypeMatch(trade, userConfigs.getTradeType())
                && distributorMatch(trade, userConfigs.getDistributorIdsStr())
                && conditionsMatch(staff, trade, userConfigs.getConditions())
                && itemsMatch(trade, userConfigs.getItems(), userConfigs.getItemType())
                && (NumberUtils.negative2Zero(userConfigs.getSysTradePay()) == 0 || NumberUtils.negative2Zero(tradePayCount) > 0);
    }

    /**
     * 分销商匹配
     */
    private boolean distributorMatch(Trade trade, String distributorIdStr) {
        if (StringUtils.isBlank(distributorIdStr)) {
            return true;
        }
        String[] distributorIdArr = StringUtils.split(distributorIdStr, ',');
        for (String distributorId : distributorIdArr) {
            if (Long.parseLong(distributorId) == trade.getSourceId()) {
                return true;
            }
        }
        return false;
    }


    /**
     * 商品匹配
     *
     * @param itemType 0 或 null 全部商品   1 指定商品   2 排除商品
     */
    private boolean itemsMatch(Trade trade, List<PresellItem> items, Integer itemType) {
        if (itemType == null || itemType == 0) {
            return true;
        }
        Set<String> itemKeys = new HashSet<>();
        items.forEach(item -> itemKeys.add(TradeItemUtils.getItemKey(item.getSysItemId(), item.getSysSkuId())));
        boolean needContainItem = itemType - 1 == 0;
        boolean containItem = false;
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            containItem = itemKeys.contains(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()));
            if (containItem) {
                break;
            }
        }
        return needContainItem == containItem;
    }

    @SuppressWarnings("all")
    private boolean conditionsMatch(Staff staff, Trade trade, List<SpelCondition> conditions) {
        if (CollectionUtils.isNotEmpty(conditions)) {
            String express = SpelConditionBusiness.buildExpression(conditions);
            SimpleTrade st = SimpleTrade.build(trade);
            // KMERP-153742 - bugfix：订单商品支持无需发货
            if (conditions.stream().anyMatch(c -> SpelCondition.FIELD_ITEM_NUM.equals(c.getField()) || SpelCondition.FIELD_ITEM_KIND_NUM.equals(c.getField()))) {
                Integer itemNumExcludeNonConsignConfig = NumberUtils.str2Int(TradeConfigGetUtil.get(staff, TradeConfigEnum.ITEM_NUM_EXCLUDE_NON_CONSIGN).getConfigValue());
                Integer excludeVirtualConfig = NumberUtils.str2Int(TradeConfigGetUtil.get(staff, TradeConfigEnum.ITEM_NUM_EXCLUDE_VIRTUAL).getConfigValue());
                boolean excludeAfterSendGoodsConfig = TradeConfigGetUtil.get(staff, TradeConfigEnum.ITEM_NUM_EXCLUDE_AFTER_SEND_GOODS).isOpen();
                int[] calculated = TradeUtils.calculateItemNum(TradeUtils.getOrders4Trade(trade), excludeAfterSendGoodsConfig, excludeVirtualConfig, itemNumExcludeNonConsignConfig);
                st.setItemKindNum(calculated[0]);
                st.setItemNum(calculated[1]);
            }
            // KMERP-236673: 先这么写，后续不会走这里了
            if (DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV, staff.getCompanyId(), LogBusinessEnum.PAYMENT.getSign())
                    && conditions.stream().anyMatch(c -> (SpelCondition.FIELD_GROSS_PROFIT_RATE.equals(c.getField()) || SpelCondition.FIELD_GROSS_PROFIT.equals(c.getField())))) {
                Logs.debug(LogHelper.buildLog(staff, String.format("finance audit. sid:%s, payment:%s, grossProfit:%s", trade.getSid(), trade.getPayment(), trade.getGrossProfit())));
            }
            try {
                StandardEvaluationContext context = new StandardEvaluationContext();
                context.setRootObject(st);
                return parser.parseExpression(express).getValue(st, Boolean.class);
            } catch (Exception e) {
                Logs.error(LogHelper.buildLog(staff, String.format("解析表达式出错,expr=%s,data=%s", express, JSON.toJSONString(st))), e);
            }
        }
        return true;
    }

    private boolean tradeWarehouseMatch(Trade trade, FinanceAuditConfig userConfig) {
        return CollectionUtils.isEmpty(userConfig.getWarehouseIds()) || userConfig.getWarehouseIds().contains(trade.getWarehouseId());
    }

    private boolean tradeTypeMatch(Trade trade, String tradeTypeStr) {
        if (StringUtils.isBlank(tradeTypeStr) || StringUtils.isBlank(tradeTypeStr.replaceAll(",", ""))) {
            return true;
        }
        String[] tradeTypes = StringUtils.split(tradeTypeStr, ",");
        boolean result;
        for (String type : tradeTypes) {
            String tradeType = trade.getType();
            if ("platform".equals(type)) {
                result = !"sys".equals(trade.getSource());
            } else if ("sys".equals(type)) {
                result = "sys".equals(trade.getSource());
            } else if ("fenxiao".equals(type)) { //分销订单
                result = trade.getBelongType() != null && trade.getBelongType() == 1;
            } else {
                result = StringUtils.equals(tradeType, type);
            }
            if (result) {
                return true;
            }
        }
        return false;
    }
}

package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.dao.trade.OverseasLogisticRuleDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ConfigTraceEnum;
import com.raycloud.dmj.domain.enums.OverseaslogisticRuleConditionFieldEnum;
import com.raycloud.dmj.domain.pt.UserLogisticsChannel;
import com.raycloud.dmj.domain.pt.enums.EnumLogisticsProviderType;
import com.raycloud.dmj.domain.trades.ConfigTrace;
import com.raycloud.dmj.domain.trades.OverseasLogisticRule;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.spel.OverseasLogisticRuleSpelCondition;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.IConfigTraceService;
import com.raycloud.dmj.services.trades.ILogisticsProviderService;
import com.raycloud.dmj.services.trades.IOverseasLogisticRuleService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class OverseasLogisticRuleService implements IOverseasLogisticRuleService {

    private final Logger logger = Logger.getLogger(this.getClass());

    private final String LOG_IDENTIFIER = "改为";

    //tiktok 商家自寄
    private static final String TIKTOK_SEND_BY_SELLER = "SEND_BY_SELLER";

    @Resource
    OverseasLogisticRuleDao dao;
    @Resource
    protected IUserService userService;
    @Resource
    private IConfigTraceService configTraceService;

    @Resource
    private ILogisticsProviderService logisticsProviderService;

    @Resource
    protected IEventCenter eventCenter;

    @Override
    public List<OverseasLogisticRule> selectListByConditions(Staff staff, OverseasLogisticRule rule) {
        List<OverseasLogisticRule> rules = dao.selectListByConditions(staff, rule);

        if (CollectionUtils.isNotEmpty(rules)) {
            for (OverseasLogisticRule r : rules) {
                //规则条件转为list
                r.setConditions(convertConditions2List(r.getConditionsDesc()));
                //规则内容组装
                r.setConditionContent(buildConditionContent(staff, r.getConditions(), ""));
            }
        }
        return rules;
    }

    @Override
    public List<OverseasLogisticRule> selectByLogId(Staff staff, Long logId) {
        if (Objects.isNull(logId)) {
            return new ArrayList<>();
        }
        return dao.selectByLogId(staff, logId);
    }

    @Override
    public OverseasLogisticRule selectDetailById(Staff staff, Long id) {
        OverseasLogisticRule rule = dao.selectDetailById(staff, id);
        rule.setConditions(convertConditions2List(rule.getConditionsDesc()));
        return rule;
    }

    @Override
    public OverseasLogisticRule selectByName(Staff staff, String name) {
        OverseasLogisticRule rule = dao.selectByName(staff, name);
        return rule;
    }

    @Transactional
    @Override
    public void updateActiveStatus(Staff staff, OverseasLogisticRule rule) {
        validate4ActiveStatus(staff, rule);
        recordLog(staff, rule.getId(), "规则启用状态: " + LOG_IDENTIFIER + (Objects.equals(rule.getActiveStatus(), 0) ? "已关闭" : "已开启"));
        dao.updateActiveStatus(staff, rule);
    }

    @Transactional
    @Override
    public void logicalDelete(Staff staff, Long id) {
        recordLog(staff, id, "删除规则");
        dao.logicalDelete(staff, id);
    }

    @Override
    @Transactional
    public void saveOrUpdate(Staff staff, OverseasLogisticRule rule) {
        if (rule.getProviderId() == null) {
            throw new RuntimeException("物流商id不能为空，请刷新页面重新保存");
        }
        validate4Both(staff, rule);
        rule.setConditionsDesc(convertConditions2Str(rule.getConditions()));
        if (rule.getId() == null || rule.getId() <= 0) { //insert
            validate4Insert(staff, rule);
            rule.setActiveStatus(1);
            rule.setEnableStatus(1);
            long insert = dao.insert(staff, rule);
            recordLog(staff, insert, "新建");
        } else { //update
            OverseasLogisticRule ruleExist = dao.selectDetailById(staff, rule.getId());
            dao.updateDetailById(staff, rule);
            rule.setLogId(rule.getId());
            rule.setId(null);
            rule.setActiveStatus(0);
            rule.setEnableStatus(0);
            long insert = dao.insert(staff, rule);
            rule.setId(insert);
            // logModify(staff, rule, ruleExist);
            recordLog(staff, rule.getId(), "修改");
        }
    }

    @Override
    public void matchTemplateId4Trade(User user, List<Trade> tradeList) {
        logger.info(LogHelper.buildLog(user.getStaff(), "开始进行跨境物流规则匹配"));
        //查询可用的物流匹配规则，按优先级排序（优先级数字越小，优先级越高）
        OverseasLogisticRule params = new OverseasLogisticRule();
        params.setActiveStatus(1);
        Optional.ofNullable(dao.selectListByConditions(user.getStaff(), params))
                .ifPresent(rules -> {
                    rules.stream()
                            .sorted(Comparator.comparingInt(OverseasLogisticRule::getPriority))
                            .forEach(rule -> {
                                //规则匹配订单，成功匹配后trade设置对应规则的templateId
                                matchTrades(user.getStaff(), rule, tradeList);
                            });
                });
        compensateOrders(user.getStaff(), tradeList, EnumLogisticsProviderType.getAbroadPlatform());
    }

    private void matchTrades(Staff staff, OverseasLogisticRule rule, List<Trade> tradeList) {
        if (CollectionUtils.isNotEmpty(tradeList)) {
            tradeList.stream().forEach(trade -> matchTradeAndSetTemplateId(staff, rule, trade));
        }
    }

    private void matchTradeAndSetTemplateId(Staff staff, OverseasLogisticRule rule, Trade trade) {
        //跨境平台订单匹配快递模板过滤
        //待审核（或已审核）且已分配仓库未匹配快递模版的的订单需要匹配快递模版
        boolean hasMatch = (TradeStatusUtils.isWaitAudit(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) &&
                (trade.getWarehouseId() != null && trade.getWarehouseId() > 0) &&
                (trade.getTemplateId() == null || trade.getTemplateId() <= 0);
        if (!hasMatch) {
            return;
        }
        AtomicBoolean allConditionMatched = new AtomicBoolean(true);
        Optional.ofNullable(rule)
                .map(OverseasLogisticRule::getConditionsDesc)
                .map(this::convertConditions2List)
                .orElse(new ArrayList<>())
                .forEach(condition -> {
                    OverseaslogisticRuleConditionFieldEnum conditionEnum = OverseaslogisticRuleConditionFieldEnum.getEnum(condition.getField());
                    if (conditionEnum == null) {
                        allConditionMatched.set(false);
                    }
                    if (conditionEnum != null && !conditionEnum.getMatchCondition().apply(trade, condition)) {
                        allConditionMatched.set(false);
                    }
                });
        if (allConditionMatched.get() && !tiktokExpressHandler(staff, trade, rule)) {
            allConditionMatched.set(false);
        }
        //规则下的条件如果全部匹配，则匹配这条规则
        if (allConditionMatched.get()) {
            trade.setTemplateId(rule.getTemplateId());
            trade.setTemplateType(1);
            trade.setTemplateMatchRuleId(rule.getId());
        }
    }


    public void compensateOrders(Staff staff, List<Trade> tradeList, List<String> supportedPlatforms) {
        if (staff == null || tradeList == null || tradeList.isEmpty() || supportedPlatforms == null || supportedPlatforms.isEmpty()) {
            return;
        }

        // 筛选符合条件的订单
        List<Trade> filteredTrades = tradeList.stream()
                .filter(trade -> trade != null
                        && supportedPlatforms.contains(trade.getSource()) // 支持的平台
                        && (trade.getTemplateId() == null || trade.getTemplateId() <= 0)) // 模板 ID 为空或小于等于 0
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredTrades)) {
            return;
        }

        // 按平台分组订单
        Map<String, List<Trade>> platformTradeMap = filteredTrades.stream()
                .collect(Collectors.groupingBy(Trade::getSource));

        // 遍历每个平台的订单
        for (Map.Entry<String, List<Trade>> entry : platformTradeMap.entrySet()) {
            String platform = entry.getKey();
            List<Trade> platformTrades = entry.getValue();

            // 根据平台类型查询物流渠道
            EnumLogisticsProviderType providerType = resolveProviderType(platform);
            if (providerType == null) {
                continue; // 不支持的平台跳过
            }

            List<UserLogisticsChannel> logisticsChannels = logisticsProviderService.queryChannelByProviderType(staff, providerType.getValue());
            if (CollectionUtils.isEmpty(logisticsChannels)) {
                continue;
            }

            // 选择第一个有效的物流渠道
            UserLogisticsChannel channel = logisticsChannels.get(0);
            if (channel != null && channel.getTemplateId() != null) {
                // 更新订单模板信息
                platformTrades.forEach(trade -> {
                    trade.setTemplateId(channel.getTemplateId());
                    trade.setTemplateType(1);
                });
            }
        }
    }

    /**
     * 根据平台标识解析物流渠道提供商类型
     */
    private EnumLogisticsProviderType resolveProviderType(String platform) {
        switch (platform) {
            case CommonConstants.PLAT_FORM_TYPE_TIKTOK:
                return EnumLogisticsProviderType.TIKTOK;
            case CommonConstants.PLAT_FORM_TYPE_JOOM:
                return EnumLogisticsProviderType.JOOM_EXPRESS;
            case CommonConstants.PLAT_FORM_TYPE_OZON:
                return EnumLogisticsProviderType.OZON_EXPRESS;
            default:
                return null; // 不支持的平台返回 null
        }
    }

    private void validate4Insert(Staff staff, OverseasLogisticRule rule) {
        Assert.isTrue(this.selectByName(staff, rule.getName()) == null, "规则名称[" + rule.getName() + "]已存在");
    }

    private void validate4Both(Staff staff, OverseasLogisticRule rule) {
        List<OverseasLogisticRuleSpelCondition> conditions = rule.getConditions();
        Integer priority = rule.getPriority();

        Assert.isTrue(StringUtils.isNotBlank(rule.getName()), "请填写规则名称");
        Assert.isTrue(priority != null, "请填写优先级");
        Assert.isTrue(priority >= 1 && priority <= 100, "优先级取值范围为1-100");
        Assert.isTrue(rule.getTemplateId() != null, "请选择物流渠道");
        Assert.isTrue(CollectionUtils.isNotEmpty(conditions), "请至少选择一个规则条件");
        //优先级不能重复
        Assert.isTrue(!isPriorityRepeat(staff, priority, rule.getId()), "规则优先级: " + priority + ", 不能重复，请重新选择");
        //条件不能有重复的，根据字段field判断
        Assert.isTrue(conditions.size() == conditions.stream().map(SpelCondition::getField).collect(Collectors.toSet()).size(), "同一个规则条件不能有重复");
        //订单金额条件，需要判断最大金额大于最小金额
        for (OverseasLogisticRuleSpelCondition condition : conditions) {
            if (OverseasLogisticRuleSpelCondition.OVERSEAS_FIELD_ORDER_AMOUNT.equals(condition.getField())) {
                if (NumberUtils.isNumber(condition.getMax()) && NumberUtils.isNumber(condition.getMin())) {
                    Assert.isTrue(new BigDecimal(condition.getMax()).compareTo(new BigDecimal(condition.getMin())) == 1, "订单最大金额需大于最小金额");
                } else {
                    new IllegalArgumentException("订单金额请输入数字");
                }
            }
        }
    }

    private void validate4ActiveStatus(Staff staff, OverseasLogisticRule rule) {
        if (Objects.equals(1, rule.getPriority())) {
            //设置成已启用时，校验优先级：优先级不能重复
            Assert.isTrue(!isPriorityRepeat(staff, rule.getId()), "规则优先级: " + rule.getPriority() + ", 不能重复，请修改优先级");
        }
    }

    /**
     * 验证优先级是否重复
     *
     * @param priority
     * @param id
     * @return
     */
    private boolean isPriorityRepeat(Staff staff, Integer priority, Long id) {
        OverseasLogisticRule params = new OverseasLogisticRule();
        params.setActiveStatus(1);
        List<OverseasLogisticRule> rulesExist = this.selectListByConditions(staff, params);
        if (CollectionUtils.isNotEmpty(rulesExist)) {
            return rulesExist.stream()
                    //根据规则id过滤掉自身
                    .filter(rule -> !Objects.equals(rule.getId(), id))
                    .map(OverseasLogisticRule::getPriority)
                    .anyMatch(priority::equals);
        }
        return false;
    }

    /**
     * 验证优先级是否重复
     *
     * @param id
     * @return
     */
    private boolean isPriorityRepeat(Staff staff, Long id) {
        OverseasLogisticRule params = new OverseasLogisticRule();
        List<OverseasLogisticRule> rulesExist = this.selectListByConditions(staff, params);
        if (CollectionUtils.isNotEmpty(rulesExist)) {
            Integer priorityToBeValidate = rulesExist.stream()
                    .filter(rule -> Objects.equals(id, rule.getId()))
                    .map(OverseasLogisticRule::getPriority)
                    .findAny()
                    .orElse(null);
            if (priorityToBeValidate != null) {
                return rulesExist.stream()
                        .filter(rule -> Objects.equals(rule.getActiveStatus(), 1))
                        //根据规则id过滤掉自身
                        .filter(rule -> !Objects.equals(rule.getId(), id))
                        .map(OverseasLogisticRule::getPriority)
                        .anyMatch(priorityToBeValidate::equals);
            }
        }
        return false;
    }

    private List<OverseasLogisticRuleSpelCondition> convertConditions2List(String conditionsJsonStr) {
        if (StringUtils.isNotBlank(conditionsJsonStr)) {
            return JSON.parseArray(conditionsJsonStr, OverseasLogisticRuleSpelCondition.class);
        }
        return new ArrayList<>();
    }

    private String convertConditions2Str(List<OverseasLogisticRuleSpelCondition> conditionList) {
        if (CollectionUtils.isNotEmpty(conditionList)) {
            return JSON.toJSONString(conditionList);
        }
        return null;
    }

    /**
     * 构造规则条件的显示中文内容
     *
     * @param staff
     * @param conditions
     * @param logIdentifier 日志记录标识符
     * @return
     */
    public String buildConditionContent(Staff staff, List<OverseasLogisticRuleSpelCondition> conditions, String logIdentifier) {
        StringBuilder conditionContent = new StringBuilder();
        for (OverseasLogisticRuleSpelCondition condition : conditions) {
            OverseaslogisticRuleConditionFieldEnum fieldEnum = OverseaslogisticRuleConditionFieldEnum.getEnum(condition.getField());
            if (fieldEnum != null) {
                switch (fieldEnum) {
                    case SHOP_SOURCE: {
                        conditionContent.append(fieldEnum.getDisplayName()).append(": ").append(logIdentifier);
                        List<Long> userIdList = Strings.getAsLongList(condition.getValue(), ",", false);
                        List<User> userList = userService.queryByIdList(staff.getCompanyId(), userIdList);
                        List<String> nickList = new ArrayList<>();
                        for (User user:userList){
                            User temp = staff.getUserByUserId(user.getId());
                            if (null!=temp){
                                nickList.add(temp.getNick());
                            }
                        }
                        if (CollectionUtils.isNotEmpty(nickList)){
                            conditionContent.append(StringUtils.join(nickList,","));
                            conditionContent.append(";");
                        }
                        break;
                    }
                    case ITEM:
                        break;
                    case COUNTRY:
                    case LOGISTIC: {
                        conditionContent
                                .append(fieldEnum.getDisplayName())
                                .append(": ")
                                .append(logIdentifier)
                                .append(condition.getValue())
                                .append(";");
                        break;
                    }
                    case ORDER_AMOUNT: {
                        conditionContent
                                .append(fieldEnum.getDisplayName())
                                .append(": ")
                                .append(logIdentifier)
                                .append(condition.getMin())
                                .append("~")
                                .append(condition.getMax())
                                .append(";");
                        break;
                    }
                    default:
                        break;
                }
            }
        }
        return conditionContent.toString();
    }

    private void recordLog(Staff staff, Long key, String content) {
        ConfigTrace configTrace = new ConfigTrace();
        configTrace.setConfigId(key);
        configTrace.setConfigType(ConfigTraceEnum.OVERSEAS_LOGISTIC.getConfigType());
        configTrace.setClueId(ClueIdUtil.getClueId() + "");
        configTrace.setContent(content);
        configTrace.setStaffName(staff.getName());
        configTrace.setCompanyId(staff.getCompanyId());
        configTrace.setCreated(new Date());
        configTraceService.addConfigTrace(staff, configTrace);
    }

    private void logModify(Staff staff, OverseasLogisticRule rule, OverseasLogisticRule ruleExist) {
        String updateLogContent = buildUpdateLogContent(staff, ruleExist, rule);
        //有实质修改才记录修改日志
        if (StringUtils.isNotBlank(updateLogContent)) {
            recordLog(staff, rule.getId(), updateLogContent);
        }
    }

    /**
     * 构建规则修改时的日志内容
     *
     * @param ruleExist     修改之前的规则
     * @param ruleForUpdate 修改后的规则
     * @return
     */
    private String buildUpdateLogContent(Staff staff, OverseasLogisticRule ruleExist, OverseasLogisticRule ruleForUpdate) {
        List<OverseasLogisticRuleSpelCondition> conditionsExist = convertConditions2List(ruleExist.getConditionsDesc());
        List<OverseasLogisticRuleSpelCondition> conditionsForUpdate = convertConditions2List(ruleForUpdate.getConditionsDesc());
        for (OverseasLogisticRuleSpelCondition conditionExist : conditionsExist) {
            if (conditionsForUpdate.contains(conditionExist)) {
                conditionsForUpdate.remove(conditionExist);
            }
        }
        return buildConditionContent(staff, conditionsForUpdate, LOG_IDENTIFIER);
    }

    /**
     * tiktok订单物流匹配特殊规则
     * <AUTHOR>
     * @date 2022/6/14 下午2:01
     * @param staff
     * @param trade
     * @param rule
     * @return boolean
     */
    private boolean tiktokExpressHandler(Staff staff, Trade trade, OverseasLogisticRule rule) {
        if (!Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_TIKTOK)) {
            return true;
        }
        UserLogisticsChannel channel = logisticsProviderService.queryLogisticsChannelByTemplateId(staff, rule.getTemplateId());
        //是否是tiktok线上物流
        boolean isTiktokOnLine = false;
        if (EnumLogisticsProviderType.TIKTOK.getValue().equals(channel.getProviderType())) {
            isTiktokOnLine = true;
        }
        String deliveryOption = Optional.ofNullable(trade.getTradeExt())
                .map(TradeExt::getExtraFields)
                .map(JSON::parseObject)
                .map(jsonObj -> jsonObj.getString("delivery_option"))
                .orElse("");
        if (Objects.equals(deliveryOption, TIKTOK_SEND_BY_SELLER) && isTiktokOnLine) {
            //商家自寄---只能匹配第三方物流
            return false;
        }
        if (!Objects.equals(deliveryOption, TIKTOK_SEND_BY_SELLER) && !isTiktokOnLine) {
            //平台配送---只能匹配tiktok线上物流
            return false;
        }
        return true;
    }

}

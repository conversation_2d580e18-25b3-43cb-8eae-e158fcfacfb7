package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
public interface IConditionConverter {

    void convert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q);

}

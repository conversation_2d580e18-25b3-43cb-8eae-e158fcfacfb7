package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.*;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.audit.*;
import com.raycloud.dmj.business.audit.help.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.fx.handle.TradeGxRefreshFxPriceFromSourceHandle;
import com.raycloud.dmj.business.modify.TradeCalculateTheoryPostFeeBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.payment.support.MonitorLogBuilder;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.dms.domain.finance.DmsCashFlowTypeEnum;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.response.*;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.split.SplitResult;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trades.fx.*;
import com.raycloud.dmj.domain.trades.fx.FxOperateResult;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.*;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.trade.audit.TradeAuditService;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.smart_match.IExpressMatchEngine;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.trade.audit.TradeAuditService;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trade.split.ITradeSplitService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.warehouse.services.ITradeWarehouseService;
import com.raycloud.dmj.waybill.common.context.PtWaybillPathContext;
import com.raycloud.dmj.waybill.common.params.PathParam;
import com.raycloud.dmj.waybill.common.params.PathResult;
import com.raycloud.dmj.waybill.common.params.ResultInfo;
import com.raycloud.ec.api.*;
import com.raycloud.erp.db.router.TransactionTemplateAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.beans.Introspector;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.waybill.common.enums.RecycledPathEnum.*;

/**
 * 分销订单系统业务服务
 */
@Service
public class SysTradeDmsService implements ISysTradeDmsService {

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    ITradePtService tradePtService;
    @Resource
    IStaffService staffService;
    @Resource
    TransactionTemplateAdapter transactionTemplateAdapter;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    WarehouseAllocateBusiness warehouseAllocateBusiness;
    @Resource
    FxBusiness fxBusiness;
    @Resource
    FxCashFlowBusiness fxCashFlowBusiness;
    @Resource
    ICompanyService companyService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    private ILockService lockService;
    @Resource
    AuditFxBusiness auditFxBusiness;
    @Resource
    private CancelUndoBusiness cancelUndoBusiness;
    @Resource
    CancelBusiness cancelBusiness;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    ITradeMergeService tradeMergeService;
    @Resource
    private TbTradeDao tbTradeDao;
    @Resource
    ITradeSplitService tradeSplitService;
    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    private OrderModifyLogBusiness orderModifyLogBusiness;
    @Resource
    private GroupProgressBusiness groupProgressBusiness;

    @Resource
    private ITradeDownloadService tradeDownloadService;
    @Resource
    private ITradeFillService tradeFillService;

    @Resource
    AuditStockBusiness auditStockBusiness;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    IFinanceAuditConfigService financeAuditConfigService;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    ApplicationContext applicationContext;

    SysTradeDmsService beanProxy;

    @Resource
    SimulateExpressMatchBusiness simulateExpressMatchBusiness;

    @Resource
    IExpressMatchEngine expressMatchEngine;

    @Resource
    IExpressTemplateCommonService expressTemplateCommonService;

    @Resource
    private ITradeWarehouseService tradeWarehouseService;

    @Resource
    SimulateImportGxTradeBusiness simulateImportGxTradeBusiness;

    @Resource
    TradeAuditService tradeAuditService;
    @Resource
    TradeGxRefreshFxPriceFromSourceHandle gxItemChangeRefreshFxPriceHandle;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;
    @Resource
    SysTradeDmsBusiness sysTradeDmsBusiness;
    @Resource
    TradeCalculateTheoryPostFeeBusiness tradeCalculateTheoryPostFeeBusiness;

    private final Logger logger = Logger.getLogger(this.getClass());
    private static final SimplePropertyPreFilter RESULT_SIMPLE_PROPERTY_PRE_FILTER = new SimplePropertyPreFilter("sid", "mergeSid", "success", "errorMsg");

    @PostConstruct
    public void startup() {
        beanProxy = (SysTradeDmsService) applicationContext.getBean(Introspector.decapitalize(this.getClass().getSimpleName()));
    }

    /**
     * 分销商付款
     *
     * @param sids
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public List<Trade> distributorPay(Staff staff, Long[] sids) {
        SysTradeDmsBusiness.DmsData dmsData1 = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            SysTradeDmsBusiness.DmsData dmsData = new SysTradeDmsBusiness.DmsData();
            //查询订单
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
            List<Trade> fxExcepTrades = Lists.newArrayListWithCapacity(originTrades.size());
            List<Trade> fxPriceExcepTrades = Lists.newArrayListWithCapacity(originTrades.size());
            List<Trade> errorTrades;
            List<String> logStrList = Lists.newArrayListWithExpectedSize(8);
            for (Trade originTrade : originTrades) {
                Assert.isTrue(!TradeUtils.isUnAuditExcep(staff, originTrade), String.format("分销商反审核异常不能进行次操作[系统订单号=%s]", originTrade.getSid()));
                if (TradeUtils.isUnAllocatedTrade(staff, originTrade)) {
                    logStrList.add(String.format("sid=%s,商品未匹配;", originTrade.getSid()));
                    continue;
                }
                //有分销商付款异常
                if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_WAITPAY)) {
                    fxExcepTrades.add(originTrade);
                } else {
                    logStrList.add(String.format("sid=%s,没有分销商待付款异常;", originTrade.getSid()));
                }
                if (originTrade.getV() != null && (originTrade.getV() | TradeConstants.V_TRADE_FX_PRICE_EXCEPT) - originTrade.getV() == 0) {
                    fxPriceExcepTrades.add(originTrade);
                }
            }
            if (CollectionUtils.isNotEmpty(logStrList)) {
                logger.warn(LogHelper.buildLog(staff, String.format("分销商付款不符合条件：%s", logStrList)));
            }
            if (CollectionUtils.isNotEmpty(fxPriceExcepTrades)) {
                logger.warn(LogHelper.buildLog(staff, String.format("分销价异常,重新匹配分销价：%s", TradeUtils.toSidList(fxPriceExcepTrades))));
                fxBusiness.caculateGxPrice(staff, fxPriceExcepTrades);
            }
            if (CollectionUtils.isNotEmpty(fxExcepTrades)) {
                boolean openQimenFxCashFlow = ConfigHolder.FX_GLOBAL_CONFIG.open("qimen_cash_flow_key", staff.getCompanyId());
                List<Trade> qimenFPTrades = null;
                if (openQimenFxCashFlow) {
                    qimenFPTrades = fxExcepTrades.stream().filter(t -> TradeUtils.isQimenFxSource(t) && (t.getIfFxForcePushTrade() || t.getIfFxWaitPay())).collect(Collectors.toList());
                    Set<Long> qimenFPSids = TradeUtils.toSidSet(qimenFPTrades);
                    fxExcepTrades = fxExcepTrades.stream().filter(t -> !(qimenFPSids.contains(t.getSid()))).collect(Collectors.toList());
                }

                List<Trade> hasCashFlowTrades = fxCashFlowBusiness.filterSuccessPayTrades(staff, fxExcepTrades);
                if (CollectionUtils.isNotEmpty(hasCashFlowTrades)) {
                    MonitorLogBuilder logBuilder = new MonitorLogBuilder(staff, MonitorLogBuilder.MONITOR_FX).append(String.format("分销商付款,还有待付款异常，但流水合计总额大于等于待支付流水金额,自动去掉此异常，sid：%s", TradeUtils.toSidList(hasCashFlowTrades)));
                    logBuilder.printWarn(logger);
                    fxExcepTrades = fxExcepTrades.stream().filter(t -> !hasCashFlowTrades.contains(t)).collect(Collectors.toList());
                }
                //整单支付流水，退款的流水需要保证同一事物内执行
                List<Long> successSids = consumeTrades(staff, fxExcepTrades);
                CashFlowData cashFlowData = lockBalance(staff, qimenFPTrades);
                if (CollectionUtils.isNotEmpty(qimenFPTrades)) {
                    successSids.addAll(cashFlowData.successSids);
                    fxExcepTrades.addAll(qimenFPTrades);
                }
                List<Trade> successTrades = fxExcepTrades.stream().filter(trade -> successSids.contains(trade.getSid())).collect(Collectors.toList());
                errorTrades = fxExcepTrades.stream().filter(trade -> !successSids.contains(trade.getSid())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hasCashFlowTrades)) {
                    tradeUpdateService.updateTrades(staff, createUpdateTrades(staff, hasCashFlowTrades, logger));
                }
                dmsData.errorTrades = errorTrades;
                if (CollectionUtils.isNotEmpty(successTrades)) {
                    dmsData.successTrades = successTrades;
                    //更新数据库
                    List<Trade> updateTrades = createUpdateTrades(staff, successTrades, logger);
                    List<Order> updateOrders = null;
                    if (CollectionUtils.isNotEmpty(fxPriceExcepTrades)) {
                        //分销价异常
                        updateOrders = createUpdateOrders(staff, fxPriceExcepTrades.stream().filter(trade -> successSids.contains(trade.getSid())).collect(Collectors.toList()));
                    }
                    tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
                    Long[] sidArray = TradeUtils.toSids(updateTrades);
                    //添加操作日志
                    if (CollectionUtils.isNotEmpty(updateTrades)) {
                        eventCenter.fireEvent(this, new EventInfo("trade.pay.distributor").setArgs(new Object[]{staff, sidArray}), updateTrades);
                    }
                }
                //日志补充：失败日志 + 具体流水日志
                tradeTraceBusiness.asyncTrace(staff, fxExcepTrades.stream().filter(t -> MapUtils.isNotEmpty(t.getOperations())).collect(Collectors.toList()), OpEnum.TRADE_PAY_DISTRIBUTOR);
            }
            return dmsData;
        });

        // 成功的：取消对应分销订单上的“分销商资金不足”的标签。
        List<Trade> gxSuccessTrades = dmsData1.successTrades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gxSuccessTrades)) {
            Map<Long, List<Trade>> listMap = TradeUtils.groupBySourceId(gxSuccessTrades);
            List<Staff> staffList = staffAssembleBusiness.listDefaultStaff4TradeSync(listMap.keySet().toArray(new Long[0]));
            Map<Long, Staff> companyId2StaffMap = Optional.ofNullable(staffList).map(list -> list.stream().collect(Collectors.toMap(Staff::getCompanyId, a -> a, (a, b) -> a))).orElse(Maps.newHashMap());
            listMap.forEach((sourceId, trades) -> {
                Staff sourceStaff = companyId2StaffMap.get(sourceId);
                if (sourceStaff == null) {
                    logger.warn(LogHelper.buildLog(staff, String.format("[分销业务]companyId=%s对应的staff不存在", sourceId)));
                    return;
                }
                eventCenter.fireEvent(this, new EventInfo("trade.fx.sync.price").setArgs(new Object[]{staff, TradeUtils.toSidList(trades), false, true}), null);

                Long[] tidArray = TradeUtils.toTidList(trades).stream().map(s -> NumberUtils.str2Long(s.trim(), -1L)).toArray(Long[]::new);
                if (ArrayUtils.isNotEmpty(tidArray)) {
                    List<TradeTag> tags = Collections.singletonList(SystemTags.TAG_FX_FUND_NOT_ENOUGH);

                    tradeSysLabelBusiness.handleTagsBySids(sourceStaff, Lists.newArrayList(tidArray), OpEnum.REMOVE_TAG, tags, false);
                }
            });
        }


        // 处理付款失败的对应的分销单
        // 1.记录【分销商付款：执行失败，分销商资金不足】日志
        // 2.新增【分销商资金不足】标签
        List<Trade> gxErrorTrades = dmsData1.errorTrades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gxErrorTrades)) {
            Map<Long, List<Trade>> errorTradesMap = TradeUtils.groupBySourceId(gxErrorTrades);
            List<Staff> staffList = staffAssembleBusiness.listDefaultStaff4TradeSync(errorTradesMap.keySet().toArray(new Long[0]));
            Map<Long, Staff> companyId2StaffMap = Optional.ofNullable(staffList).map(list -> list.stream().collect(Collectors.toMap(Staff::getCompanyId, a -> a, (a, b) -> a))).orElse(Maps.newHashMap());
            errorTradesMap.forEach((sourceId, trades) -> {
                Staff sourceStaff = companyId2StaffMap.get(sourceId);
                if (sourceStaff == null) {
                    logger.warn(LogHelper.buildLog(staff, String.format("[分销业务]companyId=%s对应的staff不存在", sourceId)));
                    return;
                }
                eventCenter.fireEvent(this, new EventInfo("trade.fx.sync.price").setArgs(new Object[]{staff, TradeUtils.toSidList(trades), false, true}), null);
                List<Long> tidLong = TradeUtils.toTidList(trades).stream().map(s -> NumberUtils.str2Long(s.trim(), -1L)).collect(Collectors.toList());
                Long[] tidArray = tidLong.toArray(new Long[0]);
                List<Trade> sourceTrades = tradeSearchService.queryBySids(sourceStaff, false, tidArray);
                eventCenter.fireEvent(this, new EventInfo("trade.pay.distributor.fund.not.enough").setArgs(new Object[]{sourceStaff, TradeUtils.toSids(sourceTrades)}), sourceTrades);
                tradeSysLabelBusiness.handleTagsBySids(sourceStaff, TradeUtils.toSidList(sourceTrades), OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_FX_FUND_NOT_ENOUGH), true);
            });
        }
        distributorPayAfter(staff, dmsData1);
        return dmsData1.successTrades;
    }

    /**
     * 锁定余额,目前仅奇门强推
     *
     * @param staff
     * @param trades
     * @return
     */
    public CashFlowData lockBalance(Staff staff, List<Trade> trades) {
        CashFlowData cashFlowData = new CashFlowData();
        if (CollectionUtils.isNotEmpty(trades)) {
            Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
            List<Trade> qimenTrades = trades.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
            List<DmsOperateBalanceBatchResponse> responses = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(qimenTrades)) {
                Map<String, Object> config = tradeWarehouseService.queryParty3Config(staff);
                if (config == null) {
                    return cashFlowData;
                }
                //开启流水统计
                boolean isOpenFxAmountRecord = (boolean) config.getOrDefault("OpenFXAmountRecord", false);
                if (!isOpenFxAmountRecord) {
                    cashFlowData.successSids.addAll(TradeUtils.toSidList(qimenTrades));
                    return cashFlowData;
                }
                //锁定流水释放节点
                Integer nodeValue = (Integer) config.getOrDefault("DMSOperatorNode", "2");
                responses.addAll(operateLockBalance(staff, qimenTrades, 0, nodeValue, true));
            }
            if (CollectionUtils.isNotEmpty(responses)) {
                for (DmsOperateBalanceBatchResponse response : responses) {
                    if (CollectionUtils.isNotEmpty(response.getDetails())) {
                        for (DmsGenerateCashFlowResponse detail : response.getDetails()) {
                            long sid = 0;
                            if (StringUtils.isNotEmpty(detail.getSysOrderNumber())) {
                                sid = Long.parseLong(detail.getSysOrderNumber());
                            }
                            Trade trade = tradeMap.get(sid);
                            if (null != trade && Integer.valueOf(1).equals(detail.getStatus())) {
                                //操作完成或重复扣款
                                cashFlowData.successSids.add(sid);
                            } else if (null != trade &&
                                    Integer.valueOf(0).equals(detail.getStatus())
                                    && !detail.isBalanceEnough()) {
                                //分销商资金不足
                                cashFlowData.errorMap.put(trade.getSid(), "分销商余额不足，分销商付款失败");
                            }
                        }
                    }
                }
            }
            //兼容历史数据
            Set<Long> successSidsSet = new HashSet<>(cashFlowData.successSids);
            for (Trade trade : trades) {
                if (!successSidsSet.contains(trade.getSid()) && !cashFlowData.errorMap.containsKey(trade.getSid())) {
                    cashFlowData.errorMap.put(trade.getSid(), "分销商余额不足，分销商付款失败");
                }
                if (cashFlowData.errorMap.containsKey(trade.getSid())) {
                    trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, cashFlowData.errorMap.get(trade.getSid()));
                }
                if (cashFlowData.successSids.contains(trade.getSid())) {
                    trade.addV(TradeConstants.V_TRADE_QIMEN_CASH_FLOW);
                    trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, String.format("分销商付款成功，本次计算的流水为%s", CashFlowUtils.calculateAmount(trade)));
                }
            }
        }
        return cashFlowData;
    }

    /**
     * 批量操作锁定余额
     *
     * @param staff
     * @param trades
     * @return
     */
    public List<DmsOperateBalanceBatchResponse> operateLockBalance(Staff staff, List<Trade> trades, Integer type, Integer node, boolean verifyBalance) {
        List<DmsOperateBalanceBatchResponse> responses = new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return responses;
        }
        try {
            if (CollectionUtils.isNotEmpty(trades)) {
                Map<Long, List<Long>> mergeSids = new HashMap<>();
                List<Trade> mergeTrades = trades.stream().filter(trade -> com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(mergeTrades)) {
                    List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, false, TradeUtils.toSids(mergeTrades));
                    if (CollectionUtils.isNotEmpty(tradeList)) {
                        for (Trade trade : tradeList) {
                            mergeSids.computeIfAbsent(trade.getMergeSid(), (t) -> new ArrayList<>()).add(trade.getSid());
                        }
                    }
                }
                List<DmsOperateBalanceRequest> dmsOperateBalanceRequests = CashFlowUtils.buildOperateLockRequest(staff, trades, mergeSids, type, node, verifyBalance);
                Map<String, List<DmsOperateBalanceRequest>> supplierDistributorCompanyIdGroup = dmsOperateBalanceRequests.stream().collect(Collectors.groupingBy(r -> r.getSupplierCompanyId() + "_" + r.getDistributorCompanyId()));
                for (String key : supplierDistributorCompanyIdGroup.keySet()) {
                    String[] supplierDistributorCompanyId = key.split("_");
                    if (supplierDistributorCompanyId.length > 1) {
                        Long supplierCompanyId = Long.parseLong(supplierDistributorCompanyId[0]);
                        Long distributorCompanyId = Long.parseLong(supplierDistributorCompanyId[1]);
                        List<DmsOperateBalanceRequest> requests = supplierDistributorCompanyIdGroup.get(key);
                        DmsOperateBalanceBatchRequest request = new DmsOperateBalanceBatchRequest();
                        request.setSupplierCompanyId(supplierCompanyId);
                        request.setDistributorCompanyId(distributorCompanyId);
                        request.setStaff(staff);
                        request.setRequests(requests);
                        DmsOperateBalanceBatchResponse response = dmsTradeService.operateOrLockDmsBalanceBatch(request);
                        responses.add(response);
                        Logs.info(LogHelper.buildLog(staff, "锁定余额,响应信息:" + JSONObject.toJSONString(response)));
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(LogHelper.buildLog(staff, "批量操作锁定余额失败,sids:" + Arrays.toString(TradeUtils.toSids(trades))));
        }
        return responses;
    }

    /**
     * 奇门订单支付成功和支付失败发送事件[kdzs.fxdf.order.agent.pay] 三方仓会跟快递助手交互
     *
     * @param staff
     * @param dmsData
     */
    private void distributorPayAfter(Staff staff, SysTradeDmsBusiness.DmsData dmsData) {
        List<Trade> qimenSuccessTrades = dmsData.successTrades.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
        List<Trade> qimenErrorTrades = dmsData.errorTrades.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
        List<DmsQimenTradeAgentPayDto> qimenTradeAgentPayDtos = Lists.newArrayListWithExpectedSize(8);
        if (CollectionUtils.isNotEmpty(qimenSuccessTrades)) {
            qimenSuccessTrades.forEach(t -> {
                DmsQimenTradeAgentPayDto qimenTradeAgentPayDto = new DmsQimenTradeAgentPayDto();
                qimenTradeAgentPayDto.setDistributorId(t.getSourceId());
                qimenTradeAgentPayDto.setSupplierId(staff.getCompanyId());
                qimenTradeAgentPayDto.setSysTid(t.getTid());
                qimenTradeAgentPayDto.setPayStatus(DmsQimenTradeAgentPayDto.PAY_SUCCESS);
                qimenTradeAgentPayDto.setPlatform(t.getSubSource());
                qimenTradeAgentPayDtos.add(qimenTradeAgentPayDto);
            });
        }
        if (CollectionUtils.isNotEmpty(qimenErrorTrades)) {
            qimenErrorTrades.forEach(t -> {
                DmsQimenTradeAgentPayDto qimenTradeAgentPayDto = new DmsQimenTradeAgentPayDto();
                qimenTradeAgentPayDto.setDistributorId(t.getSourceId());
                qimenTradeAgentPayDto.setSupplierId(staff.getCompanyId());
                qimenTradeAgentPayDto.setSysTid(t.getTid());
                qimenTradeAgentPayDto.setPayStatus(DmsQimenTradeAgentPayDto.WAIT_PAY);
                qimenTradeAgentPayDto.setPlatform(t.getSubSource());
                qimenTradeAgentPayDto.setFactoryAmount(getWaitPayment(t));
                qimenTradeAgentPayDtos.add(qimenTradeAgentPayDto);
            });
        }
        if (CollectionUtils.isNotEmpty(qimenTradeAgentPayDtos)) {
            eventCenter.fireEvent(this, new EventInfo("kdzs.fxdf.order.agent.pay").setArgs(new Object[]{staff, qimenTradeAgentPayDtos}), null);
        }
    }

    /**
     * 获取待付款金额
     * 未发货的商品 sum(payment)+trade.postFee()
     *
     * @param trade
     * @return
     */
    private String getWaitPayment(Trade trade) {
        double payment = NumberUtils.str2Double(Optional.ofNullable(trade.getPostFee()).orElse("0"));
        payment = payment + TradeUtils.getOrders4Trade(trade).stream().filter(o -> !TradeStatusUtils.isAfterSendGoods(o.getSysStatus())).mapToDouble(o -> NumberUtils.str2Double(Optional.ofNullable(o.getPayment()).orElse("0"))).sum();
        return MathUtils.toString(payment);
    }

    /**
     * 供销订单支付流水处理，包括退款流水
     * 该接口，分销商付款专用，会将已存在的流水，全部删除
     * 如果存在退款，会生成两笔流水：订单支付 + 订单退款
     *
     * @param staff
     * @param payTrades
     * @return
     */
    private List<Long> consumeTrades(Staff staff, List<Trade> payTrades) {
        List<Long> successSids = new ArrayList<>();
        FlowRequest consumeFlowRequest = buildConsumeFlowRequest(staff, payTrades, successSids);
        //设置清理历史流水的参数，保证付款后的流水为唯一一笔
        Map<Long, Map<Long, List<DmsGenerateCashFlowRequest>>> supplierDistributorResumeFlowListMap = consumeFlowRequest.getSupplierDistributorResumeFlowListMap();
        if (MapUtils.isNotEmpty(supplierDistributorResumeFlowListMap)) {
            supplierDistributorResumeFlowListMap.forEach((destId, flowMap) -> {
                flowMap.forEach((sourceId, requests) -> {
                    requests.forEach(request ->
                            request.setResumeCheck(true)
                    );
                });
            });
        }
        FlowResponse refundFlowResponse = dmsTradeService.resumeFlow(staff, consumeFlowRequest);
        Map<Long, Map<Long, Map<Long, DmsGenerateCashFlowResponse>>> refundResumeFlowListMap = refundFlowResponse.getSupplierDistributorResumeFlowListMap();
        if (refundResumeFlowListMap.isEmpty()) {
            return successSids;
        }
        CashFlowData cashFlowData = CashFlowUtils.fillResponseInfo(refundResumeFlowListMap, false);
        if (!cashFlowData.errorMap.isEmpty()) {
            logger.debug(LogHelper.buildLog(staff, String.format("付款失败的订单=%s", cashFlowData.errorMap.keySet())));
        }
        for (Trade trade : payTrades) {
            if (cashFlowData.errorMap.containsKey(trade.getSid())) {
                trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, "分销商余额不足，分销商付款失败");
            }
            if (cashFlowData.successSids.contains(trade.getSid())) {
                trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, String.format("分销商付款成功，本次计算的流水为%s", trade.getPayment()));
            }
        }
        successSids.addAll(cashFlowData.successSids);
        return successSids;
    }

    private FlowRequest buildConsumeFlowRequest(Staff staff, List<Trade> payTrades, List<Long> successSids) {
        List<Trade> gxTrade = new ArrayList<>();
        List<Trade> splitGxTrade = new ArrayList<>();
        boolean dmsTradeGxEditNotRelFx = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen();
        for (Trade trade : payTrades) {
            if (dmsTradeGxEditNotRelFx){
                gxTrade.add(trade);
                continue;
            }
            if (TradeUtils.isSplit(trade) && TradeUtils.isGxOrMixTrade(trade)) {
                if (Objects.equals(trade.getSid(), trade.getSplitSid())) {
                    splitGxTrade.add(trade);
                } else {
                    //开启商品不一致，拆分子单，直接处理为支付成功
                    successSids.add(trade.getSid());
                }
            } else {
                gxTrade.add(trade);
            }
        }
        FlowRequest consumeFlowRequest = CashFlowUtils.buildTradeCashRequestData(staff, gxTrade, DmsCashFlowTypeEnum.orderPay, 0, false, false, true, true);
        FlowRequest consumeItemChangeRequest = gxItemChangeRefreshFxPriceHandle.buildConsumeFlowRequest(staff, splitGxTrade, consumeFlowRequest.getSupplierDistributorResumeFlowListMap());
        if (Objects.nonNull(consumeItemChangeRequest)) {
            consumeFlowRequest.setSupplierDistributorResumeFlowListMap(consumeItemChangeRequest.getSupplierDistributorResumeFlowListMap());
        }
        return consumeFlowRequest;
    }


    /**
     * 新增分销属性
     *
     * @param staff
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public List<Trade> addDistributorAttribute(Staff staff, Long[] sids) {
        return addDistributorAttribute(staff, sids, 0);
    }

    @Override
    public List<Trade> addDistributorAttribute(Staff staff, Long[] sids, Integer addDistributorAttributeType) {
        return addDistributorAttribute(staff, sids, addDistributorAttributeType, new FxOperateResult());
    }

    /**
     * 新增分销属性
     *
     * @param staff
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public List<Trade> addDistributorAttribute(Staff staff, Long[] sids, Integer addDistributorAttributeType, FxOperateResult fxOperateResult) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            addDistributorAttributeBusiness(staff, originTrades, sids, addDistributorAttributeType, fxOperateResult);
            return originTrades;
        });
    }

    private void addDistributorAttributeBusiness(Staff staff, List<Trade> originTrades, Long[] sids, Integer addDistributorAttributeType, FxOperateResult fxOperateResult) {
        List<Long> afterSendTradeSidList = addDisMergeDeal(staff, originTrades);
        if (CollectionUtils.isNotEmpty(afterSendTradeSidList)) {
            originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            List<Trade> deleteList = new ArrayList<>();
            for (Trade trade : originTrades) {
                if (afterSendTradeSidList.contains(trade.getSid())) {
                    deleteList.add(trade);
                }
            }
            originTrades.removeAll(deleteList);
            if (fxOperateResult.isAddDistributorAttributeOperate()) {
                deleteList.forEach(t -> {
                    fxOperateResult.addErrMsg(String.format("合单[sid:%s]已发货，操作失败!", t.getSid()));
                });
            }
        }
        //校验
        List<Trade> validateSuccess = validateTrade(staff, originTrades, 1, null, fxOperateResult);

        List<Trade> copyValidateSuccessvalidateSuccess = new ArrayList<>();
        fxBusiness.copyTrades(validateSuccess, copyValidateSuccessvalidateSuccess);
        List<Long> successSids = Lists.newArrayListWithCapacity(sids.length);
        //匹配供销商品及供销商信息（会设置分销信息及异常信息）
        fxBusiness.matchSupplyIdsWithDmsAttrType(staff.getUser(), validateSuccess, 2, addDistributorAttributeType, fxOperateResult);
        List<Trade> fxTrades = validateSuccess.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        //添加分销属性失败的订单，记录失败原因
        fxOperateResult.parseTempMsg(validateSuccess.stream().filter(t -> !TradeUtils.isFxOrMixTrade(t)).map(Trade::getSid).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(fxTrades)) {
            //匹配供销仓库
            warehouseAllocateBusiness.matchWarehouse(staff, fxTrades);
            //设置库存信息
            sysTradeDmsBusiness.filterFxTrade(staff, validateSuccess, 1);
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                List<Trade> success = validateSuccess.stream().filter(trade -> trade.getConvertType() == 1 && (trade.getBelongType() == 1 || trade.getBelongType() == 3)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(success)) {
                    successSids.addAll(TradeUtils.toSidList(success));
                }
            }
            List<Trade> toUpdate = new ArrayList<>();
            for (Trade success : validateSuccess) {
                FxBusiness.createUpdateTrade(staff, toUpdate, success);
            }
            List<Order> toUpdateOrders = new ArrayList<>();
            for (Order order : TradeUtils.getOrders4Trade(validateSuccess)) {
                FxBusiness.createUpdateOrder(staff, toUpdateOrders, order);
            }
            //更新打印相关字段
            tradePtService.saveByTrades(staff, toUpdate);
            //更新数据库
            tradeUpdateService.updateTrades(staff, toUpdate, toUpdateOrders);
            //新增分销属性回收快递号
            cancelTidSysTidOutSid(staff, copyValidateSuccessvalidateSuccess);
            //添加操作日志
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                eventCenter.fireEvent(this, new EventInfo("trade.add.distributor").setArgs(new Object[]{staff, TradeUtils.toSids(validateSuccess)}), validateSuccess);
                auditFxBusiness.auditAddDistributorAttribute(staff, validateSuccess);
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{staff, TradeUtils.toSidList(validateSuccess)}), null);
            }
        } else {
            // 快麦通新增分销属性，及时订单级别没有成功，order商品需要更新订单商品的分销属性
            if (Objects.equals(1, addDistributorAttributeType)) {
                tradeUpdateService.updateTrades(staff, validateSuccess, TradeUtils.getOrders4Trade(validateSuccess));
            }
        }
        if (CollectionUtils.isNotEmpty(successSids)) {
            List<Trade> needResume = copyValidateSuccessvalidateSuccess.stream().filter(trade -> successSids.contains(trade.getSid())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needResume)) {
                //释放库存
                try {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("needResume:%s", TradeUtils.toSidList(needResume))));
                    }
                    orderStockService.resumeTradeStockLocal(staff, needResume);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "新增分销属性时归还库存报错，订单号：").append(TradeUtils.toSidList(validateSuccess)), e);
                }
            }
        }
    }

    private List<Long> addDisMergeDeal(Staff staff, List<Trade> originTrades) {
        List<Long> afterSendTradeSidList = new ArrayList<>();
        Map<Long, Set<Long>> sidMergeMap = Maps.newHashMap();
        for (Trade trade : originTrades) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && TradeUtils.isAfterSendGoods(trade)) {
                afterSendTradeSidList.add(trade.getSid());
                if (sidMergeMap.containsKey(trade.getMergeSid())) {
                    sidMergeMap.get(trade.getMergeSid()).add(trade.getSid());
                } else {
                    Set<Long> afterSendSidList = new HashSet<>();
                    afterSendSidList.add(trade.getSid());
                    sidMergeMap.put(trade.getMergeSid(), afterSendSidList);
                }
            }
        }
        tradeMergeService.mergeUndoAutoPart(staff, sidMergeMap, TradeMergeEnum.MERGE_UNDO_PART_AUTO);
        return afterSendTradeSidList;
    }

    /**
     * 校验
     *
     * @param staff
     * @param originTrades
     * @param fxOperateResult 初始化后传入，不要传null
     * @return
     */
    private List<Trade> validateTrade(Staff staff, List<Trade> originTrades, int type, Long supplierCompanyId, FxOperateResult fxOperateResult) {
        List<Trade> updateTrades = Lists.newArrayListWithCapacity(originTrades.size());
        Set<Long> errorMergeSids = new HashSet<>();
        boolean throwExcep = false;
        if (type == 1) {
            throwExcep = throwExcep(staff, originTrades);
        }
        boolean addMsg = fxOperateResult.isAddDistributorAttributeOperate();
        Map<Long, String> errMsg = new HashMap<>();
        for (Trade trade : originTrades) {
            if (type == 1) {
                if (throwExcep) {
                    Assert.isTrue(TradeUtils.isNorMalSource(trade) || TradeUtils.isGxTrade(trade) || TradeUtils.isQimenAndNotFx(trade), String.format("%s: 只有普通订单、供销订单、奇门供销订单才能进行此操作!!", trade.getSid()));
                    Assert.isTrue(Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus()), String.format("%s为非待审核订单不能操作!", trade.getSid()));
                    Assert.isTrue(!TradeUtils.isUnAllocatedTrade(staff, trade), String.format("%s为商品未匹配订单不能操作!", trade.getSid()));
                } else {
                    if (!TradeUtils.isNorMalSource(trade) && !TradeUtils.isGxTrade(trade) && !TradeUtils.isQimenAndNotFx(trade)) {
                        addErrMsg(staff, trade, errMsg, errorMergeSids, addMsg, "只有普通订单、供销订单、奇门供销订单才能进行此操作!");
                        continue;
                    }
                    if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
                        addErrMsg(staff, trade, errMsg, errorMergeSids, addMsg, "非待审核订单不能操作!");
                        continue;
                    }
                    if (TradeUtils.isUnAllocatedTrade(staff, trade)) {
                        addErrMsg(staff, trade, errMsg, errorMergeSids, addMsg, "商品未匹配订单不能操作!");
                        continue;
                    }
                }
            } else if (type == 2) {
                Assert.isTrue(TradeUtils.isFxOrMixTrade(trade), String.format("%s不是分销订单不能操作!", trade.getSid()));
                Assert.isTrue(TradeStatusUtils.isWaitSellerSend(trade.getSysStatus()), String.format("%s为非待发货分销订单不能操作!", trade.getSid()));
            } else if (type == 3) {
                Assert.isTrue(TradeUtils.isFxOrMixTrade(trade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()), String.format("%s不是待审核分销订单不能操作!", trade.getSid()));
                if (TradeUtils.isGxAndFxTrade(trade) && trade.getSourceId() != null && trade.getSourceId().equals(supplierCompanyId)) {
                    throw new RuntimeException("不能设置订单的供销商和分销商为同一个公司");
                }
            } else if (type == 4) {
                if (TradeUtils.isFxSource(trade)) {
                    throw new IllegalArgumentException(String.format("%s分销/供销订单不能操作", trade.getSid()));
                }
                Assert.isTrue(Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()), String.format("%s不是待审核不能操作!", trade.getSid()));
                if (staff.getCompanyId().equals(supplierCompanyId)) {
                    throw new RuntimeException("不能设置订单的供销商和分销商为同一个公司");
                }
            } else if (type == 5) {
                Assert.isTrue(TradeUtils.isAlibabaFxRoleTrade(trade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()), String.format("%s不是待审核不能操作!", trade.getSid()));
            }
            //验证店铺
            if (!TradeUtils.isGxOrMixTrade(trade)) {
                User user = staff.getUserIdMap().get(trade.getUserId());
                Assert.notNull(user, String.format("您无权操作此订单[sid:%s]!", trade.getSid()));
            }
            Assert.isTrue(!TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()), String.format("订单%s系统状态为发货后，不能操作!", trade.getSid()));
            updateTrades.add(trade);
        }
        if (type == 1 && CollectionUtils.isNotEmpty(errorMergeSids)) {
            return updateTrades.stream().filter(t -> !errorMergeSids.contains(t.getMergeSid())).collect(Collectors.toList());
        }
        if (MapUtils.isNotEmpty(errMsg)) {
            fxOperateResult.addAllErrMsg(new ArrayList<>(errMsg.values()));
        }
        return updateTrades;
    }


    private void addErrMsg(Staff staff, Trade trade, Map<Long, String> errMsg, Set<Long> errorMergeSids, boolean addErrMsg, String err) {
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            errorMergeSids.add(trade.getMergeSid());
            if (addErrMsg) {
                errMsg.put(trade.getMergeSid(), String.format("合单[sid:%s]: " + err, trade.getSid()));
            }
        } else {
            if (addErrMsg) {
                errMsg.put(trade.getSid(), String.format("订单[sid:%s]: " + err, trade.getSid()));
            }
        }
    }

    private boolean throwExcep(Staff staff, List<Trade> originTrades) {
        Set<Long> sids = new HashSet<>();
        for (Trade trade : originTrades) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                sids.add(trade.getMergeSid());
            } else {
                sids.add(trade.getSid());
            }
        }
        return sids.size() == 1;
    }


    @Override
    public List<Trade> appointAlibabaSupply(DmsAttrData attrData) {

        Staff staff = attrData.getStaff();
        Long[] sids = attrData.getSids();
        Long destId = attrData.getDestId();
        Assert.isTrue(ArrayUtils.isNotEmpty(sids), "sids参数不能为空");

        if (Objects.equals(0L, destId)) {
            return removeAlibabaSupply(attrData);
        }
        //添加操作日志
        Company company = companyService.queryById(destId);
        Assert.isTrue(company != null, String.format("%s,对应的公司不存在", destId));

        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            //校验
            List<Trade> validateSuccess = validateTrade(staff, originTrades, 4, destId, new FxOperateResult());
            List<Trade> copyValidateSuccessTrades = new ArrayList<>();
            fxBusiness.copyTrades(validateSuccess, copyValidateSuccessTrades);
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                SysTradeDmsBusiness.DmsData dmsData = new SysTradeDmsBusiness.DmsData();
                validateSuccess.forEach(trade -> {
                    trade.setSourceId(staff.getCompanyId());
                    trade.setDestId(destId);
                    trade.addV(TradeConstants.V_IF_1688_FX_ROLE);
                    List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                    orders4Trade.forEach(order -> {
                        order.setDestId(destId);
                        order.addV(OrderConstant.V_IF_1688_FX_ROLE);
                        dmsData.updateOrders.add(order);
                    });
                    dmsData.updateTrades.add(trade);
                });
                //设置库存信息
                sysTradeDmsBusiness.filterFxTrade(staff, validateSuccess, 1);
                //匹配仓库
                warehouseAllocateBusiness.matchWarehouse(staff, validateSuccess);

                if (CollectionUtils.isNotEmpty(dmsData.updateTrades)) {
                    List<Trade> toUpdateTrades = new ArrayList<>();
                    for (Trade success : validateSuccess) {
                        FxBusiness.createUpdateTrade(staff, toUpdateTrades, success);
                    }
                    List<Order> toUpdateOrders = new ArrayList<>();
                    for (Order order : TradeUtils.getOrders4Trade(validateSuccess)) {
                        FxBusiness.createUpdateOrder(staff, toUpdateOrders, order);
                    }
                    //更新数据库
                    tradePtService.saveByTrades(staff, toUpdateTrades);
                    tradeUpdateService.updateTrades(staff, toUpdateTrades, toUpdateOrders);
                    //添加操作日志
                    eventCenter.fireEvent(this, new EventInfo("trade.appointAlibabaSupply").setArgs(new Object[]{staff, TradeUtils.toSids(dmsData.updateTrades), company.getName()}), dmsData.updateTrades);
                }

                if (CollectionUtils.isNotEmpty(copyValidateSuccessTrades)) {
                    //释放库存
                    try {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("needResume:%s", TradeUtils.toSidList(copyValidateSuccessTrades))));
                        }
                        orderStockService.resumeTradeStockLocal(staff, copyValidateSuccessTrades);
                    } catch (Exception e) {
                        logger.error(LogHelper.buildErrorLog(staff, e, "指定1688供销商时归还库存报错，订单号：").append(TradeUtils.toSidList(validateSuccess)), e);
                    }

                    List<Trade> needCancelOutSidTrades = copyValidateSuccessTrades.stream().filter(t -> StringUtils.isNotBlank(t.getOutSid())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(needCancelOutSidTrades)) {
                        expressTemplateCommonService.cancelTidSysTidOutSid(staff, needCancelOutSidTrades, com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus.BOUND.getValue());
                    }

                }
            }
            return validateSuccess;
        });
    }


    /**
     * 取消1688分销小站供销商
     * 1. 库存重置
     * 2. 仓库匹配
     * 3.
     *
     * @param  attrData
     * @return
     */
    public List<Trade> removeAlibabaSupply(DmsAttrData attrData) {
        Staff staff = attrData.getStaff();
        Long[] sids = attrData.getSids();

        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            //校验
            List<Trade> validateSuccess = validateTrade(staff, originTrades, 5, 0L, new FxOperateResult());
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                SysTradeDmsBusiness.DmsData dmsData = new SysTradeDmsBusiness.DmsData();
                validateSuccess.forEach(trade -> {

                    trade.setSourceId(0L);
                    trade.setDestId(0L);
                    trade.removeV(TradeConstants.V_IF_1688_FX_ROLE);
                    trade.setWarehouseId(null);
                    trade.setTemplateId(-1L);
                    trade.setLogisticsCompanyId(0L);
                    trade.setTemplateType(0);
                    trade.setOutSid("");
                    TradeExceptUtils.setStockStatus(staff, trade, Trade.STOCK_STATUS_EMPTY);

                    List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                    orders4Trade.forEach(order -> {
                        order.setDestId(0L);
                        order.setSourceId(0L);
                        order.removeV(OrderConstant.V_IF_1688_FX_ROLE);
                        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
                        order.setStockNum(0);

                        dmsData.updateOrders.add(order);
                    });
                    dmsData.updateTrades.add(trade);
                });
                //匹配仓库
                warehouseAllocateBusiness.matchWarehouse(staff, validateSuccess);
                //匹配快递模板
                expressMatchEngine.match(staff, validateSuccess);
                //申请库存
                orderStockService.applyTradeStockLocal(staff, validateSuccess);

                if (CollectionUtils.isNotEmpty(dmsData.updateTrades)) {
                    List<Trade> toUpdateTrades = new ArrayList<>();
                    for (Trade success : validateSuccess) {
                        FxBusiness.createUpdateTrade(staff, toUpdateTrades, success);
                    }
                    List<Order> toUpdateOrders = new ArrayList<>();
                    for (Order order : TradeUtils.getOrders4Trade(validateSuccess)) {
                        FxBusiness.createUpdateOrder(staff, toUpdateOrders, order);
                    }
                    //更新数据库
                    tradePtService.saveByTrades(staff, toUpdateTrades);
                    tradeUpdateService.updateTrades(staff, toUpdateTrades, toUpdateOrders);
                    //添加操作日志
                    eventCenter.fireEvent(this, new EventInfo("trade.removeAlibabaSupply").setArgs(new Object[]{staff, TradeUtils.toSids(dmsData.updateTrades)}), dmsData.updateTrades);
                }
            }
            return validateSuccess;
        });
    }

    /**
     * 强制制定分销商
     *
     * @param staff
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public List<Trade> forceDistributorAttribute(Staff staff, Long[] sids, String supplierCompanyId) {
        Assert.isTrue(ArrayUtils.isNotEmpty(sids), "sids参数不能为空");
        List<Trade> notifyTrades = new ArrayList<>();
        List<Trade> result = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            Long supplierCompanyIdL = Long.valueOf(supplierCompanyId);
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            //校验
            List<Trade> validateSuccess = validateTrade(staff, originTrades, 3, supplierCompanyIdL, new FxOperateResult());
            List<Trade> notifyFxTrade = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                SysTradeDmsBusiness.DmsData dmsData = new SysTradeDmsBusiness.DmsData();
                validateSuccess.forEach(trade -> {
                    if (TradeUtils.isFxOrMixTrade(trade)) {
                        if (!Objects.equals(trade.getDestId(), supplierCompanyIdL) && !Objects.isNull(trade.getDestId()) && trade.getDestId() > 0) {
                            fxBusiness.copyTrades(Collections.singletonList(trade), notifyFxTrade);
                        }
                        trade.setDestId(supplierCompanyIdL);
                        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                        orders4Trade.forEach(order -> {
                            order.setDestId(supplierCompanyIdL);
                            order.setBelongType(1);
                            if (TradeUtils.isGxAndFxTrade(trade)) {
                                order.setBelongType(3);
                            }
                            order.setConvertType(1);
                            dmsData.updateOrders.add(order);
                        });
                        cancelFxAmbiguityExecp(staff, trade);
                        dmsData.updateTrades.add(trade);
                    }
                });
                //设置库存信息
                sysTradeDmsBusiness.filterFxTrade(staff, validateSuccess, 2);
                if (CollectionUtils.isNotEmpty(dmsData.updateTrades)) {
                    //更新数据库
                    tradeUpdateService.updateTrades(staff, dmsData.updateTrades, dmsData.updateOrders);
                    //添加操作日志
                    Company company = companyService.queryById(supplierCompanyIdL);
                    eventCenter.fireEvent(this, new EventInfo("trade.force.distributor").setArgs(new Object[]{staff, TradeUtils.toSids(dmsData.updateTrades), company != null ? company.getName() : ""}), dmsData.updateTrades);
                    //原先有供销单，且更换了供销商，需要把原本的供销单作废掉
                    getNotifyFxTrade(staff, notifyFxTrade, dmsData.updateTrades, notifyTrades);
                }
            }
            return validateSuccess;
        });
        fxBusiness.notifyGxDownload(staff, notifyTrades, 3);
        return result;
    }

    /**
     * 取消分销未付款异常
     * @param originTrade
     */
    private void cancelFxAmbiguityExecp(Staff staff, Trade originTrade) {
        if (TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_AMBIGUITY)) {
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.FX_AMBIGUITY, 0L);
        }
    }

    private void getNotifyFxTrade(Staff staff, List<Trade> notifyFxTrade, List<Trade> updateTrades, List<Trade> result) {
        if (CollectionUtils.isEmpty(notifyFxTrade) || CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        Map<Long, Trade> notifyFxTradeMap = notifyFxTrade.stream().collect(Collectors.toMap(Trade::getSid, t -> t, (k1, k2) -> k1));
        updateTrades.forEach(update -> {
            Trade copy = notifyFxTradeMap.get(update.getSid());
            if (Objects.isNull(copy)) {
                return;
            }
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, copy) && !Objects.equals(copy.getSid(), copy.getMergeSid())) {
                //合单场景下，子单不处理
                return;
            }
            result.add(copy);
        });
    }

    @Override
    public DmsPaymentInfoDto getPaymentInfo(Staff staff, Long[] sids, String supplierCompanyId) {

        Assert.isTrue(ArrayUtils.isNotEmpty(sids), "sids参数不能为空");

        Long supplierCompanyIdL = NumberUtils.str2Long(supplierCompanyId, -1L);
        Assert.isTrue(supplierCompanyIdL != -1L, "供销商不存在");

        List<DmsDistributorInfoDto> distributorInfoDtos = dmsTradeService.queryDmsDistributorInfoList(supplierCompanyIdL, Lists.newArrayList(staff.getCompanyId()));
        Assert.isTrue(CollectionUtils.isNotEmpty(distributorInfoDtos), "供分销关系不存在");

        DmsPaymentInfoDto paymentInfoDto = new DmsPaymentInfoDto();
        paymentInfoDto.setBalance(MathUtils.toString(distributorInfoDtos.get(0).getTotalBalance()));
        paymentInfoDto.setSupplierCompanyId(supplierCompanyId);

        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, false, false, sids);
//            fxBusiness.fillGxTradeByFx(originTrades);
            if (!tradeLocalConfig.isTradeFxAuditNotCalcGxPostFee(staff.getCompanyId())) {
                simulateImportGxTradeBusiness.handle(SimulateImportGxTradeData.builder()
                        .staff(staff)
                        .fxTrades(originTrades)
                        .ifMatchFxPrice(false)
                        .build());
            }
            fxBusiness.fillDmsPrice(staff, originTrades);
            List<Trade> updateTrades = Lists.newArrayListWithCapacity(8);
            // 分销 && 分销的实际运费跟供销的运费不一样
            originTrades.stream().filter( TradeUtils::isFxOrMixTrade).forEach(t -> {
                TbTrade trade = new TbTrade();
                trade.setSid(t.getSid());
                trade.setActualPostFee(t.getActualPostFee());
                updateTrades.add(trade);
            });
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                tradeUpdateService.updateTrades(staff, updateTrades);
            }
            // 分销订单待支付： 未退款和发货前商品的成本+实际运费
            BigDecimal payment = fxBusiness.getBatchFxTotalSaleFee(staff, tradeConfigService.get(staff), originTrades, false, true);

            paymentInfoDto.setPayment(MathUtils.toString(payment));
            paymentInfoDto.setPaymentDiff(MathUtils.subtract(payment, MathUtils.toBigDecimal(distributorInfoDtos.get(0).getTotalBalance())).toPlainString());
            return paymentInfoDto;
        });
    }

    /**
     * 强制推单到供销商
     * <p>
     * 1. 修改分销订单的sysStatus,sourceId,destId,belongType,convertType,excep
     * 1.1. trades校验
     * 1.2. 记录操作日志 -强制推单到供销商(供销商名称)
     * 1.3. 仓库重新匹配
     * 1.4. 符合财审条件的sysStatus=待财审，其他sysStatus=已审核
     * 1.5. 打上强制推送到供销商的标志 trade.v=64
     * <p>
     * 2. 已审核订单生成对应的供销订单
     * 2.1. 打上分销待付款异常
     *
     * @param staff
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public AuditData forcePushTradeToGx(Staff staff, Long[] sids, String supplierCompanyId) {

        Assert.isTrue(ArrayUtils.isNotEmpty(sids), "sids参数不能为空");

        Long supplierCompanyIdL = NumberUtils.str2Long(supplierCompanyId, -1L);
        Assert.isTrue(supplierCompanyIdL != -1L, "供销商不存在");

        List<DmsDistributorInfoDto> distributorInfoDtos = dmsTradeService.queryDmsDistributorInfoList(supplierCompanyIdL, Lists.newArrayList(staff.getCompanyId()));
        Assert.isTrue(CollectionUtils.isNotEmpty(distributorInfoDtos), "供分销关系不存在");
        Integer allowForcedPushOrders = distributorInfoDtos.get(0).getAllowForcedPushOrders();
        Assert.isTrue(allowForcedPushOrders != null && allowForcedPushOrders == 1, "供销商不允许强制推单，请联系供销商");


        AuditData auditData1 = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            // 查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);

            AuditData auditData = new AuditData();
            // 校验
            List<Trade> validateSuccess = validateTrade(staff, originTrades, auditData);
            List<Trade> needResumes = Lists.newArrayListWithCapacity(8);
            if (MapUtils.isNotEmpty(auditData.results)) {
                logger.debug(LogHelper.buildLog(staff, String.format("错误信息：%s", JSON.toJSONString(auditData.results.values(), RESULT_SIMPLE_PROPERTY_PRE_FILTER))));
            }

            if (CollectionUtils.isNotEmpty(validateSuccess)) {

                List<Trade> copyValidateSuccessSuccess = Lists.newArrayListWithExpectedSize(validateSuccess.size());
                copyTrades(validateSuccess, copyValidateSuccessSuccess);
                // 待财审,返回已经是合单的sid
                List<Long> waitFinanceAuditSidList = TradeUtils.toSidList(financeAuditConfigService.tradeCheck(staff, validateSuccess));
                if (CollectionUtils.isNotEmpty(waitFinanceAuditSidList)) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]待财审的订单:%s", waitFinanceAuditSidList)));
                    }
                }

                validateSuccess.forEach(trade -> {
                    if (waitFinanceAuditSidList.contains(trade.getSid())) {
                        trade.setSysStatus(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT);
                    } else {
                        trade.setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT);
                    }
                    trade.setConvertType(1);
                    trade.setDestId(supplierCompanyIdL);
                    trade.addV(64);
                    // isExcept字段  filterFxTrade() 会设置
                    if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_AMBIGUITY)) {
                        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 0L);
                    }
                    if (TradeUtils.isGxOrMixTrade(trade)) {
                        trade.setBelongType(3);
                    } else {
                        trade.setSourceId(trade.getCompanyId());
                        trade.setBelongType(1);
                    }
                    List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                    orders4Trade.forEach(order -> {
                        order.setConvertType(1);
                        order.setDestId(supplierCompanyIdL);
                        if (TradeUtils.isGxOrMixTrade(trade)) {
                            order.setBelongType(3);
                        } else {
                            order.setSourceId(trade.getCompanyId());
                            order.setBelongType(1);
                        }
                    });
                });
                // 释放库存
                try {
                    needResumes = copyValidateSuccessSuccess;
                    if (CollectionUtils.isNotEmpty(needResumes)) {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]needResume:%s", TradeUtils.toSidList(needResumes))));
                        }
                        orderStockService.resumeTradeStockLocal(staff, needResumes);
                    }
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "[分销业务]归还库存报错，订单号：").append(TradeUtils.toSidList(needResumes)), e);
                }
                // 匹配供销仓库
                warehouseAllocateBusiness.matchWarehouse(staff, validateSuccess);
                // 设置库存信息
                sysTradeDmsBusiness.filterFxTrade(staff, validateSuccess, 2);

                List<Trade> updateTrades = Lists.newArrayListWithCapacity(8);
                List<Order> updateOrders = Lists.newArrayListWithCapacity(8);

                validateSuccess.forEach(t -> {
                    FxBusiness.createUpdateTrade(staff, updateTrades, t);
                    fxBusiness.createUpdateOrders(staff, updateOrders, TradeUtils.getOrders4Trade(t));
                });
                updateOrders.forEach(o -> {
                    if (waitFinanceAuditSidList.contains(o.getSid())) {
                        o.setSysStatus(Trade.SYS_STATUS_WAIT_FINANCE_AUDIT);
                    } else {
                        o.setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT);
                    }
                });
                if (CollectionUtils.isNotEmpty(updateTrades)) {
                    // 更新数据库
                    tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]强制推单到供销商更新订单:%s", TradeUtils.toSidList(updateTrades))));
                    }
                    // 添加操作日志
                    Company company = companyService.queryById(supplierCompanyIdL);
                    eventCenter.fireEvent(this, new EventInfo("trade.force.pushTradeToGx").setArgs(new Object[]{staff, TradeUtils.toSids(updateTrades), company != null ? company.getName() : ""}), updateTrades);
                }
                auditData.sourceTrades = validateSuccess.stream().filter(t -> StringUtils.equals(t.getSysStatus(), Trade.SYS_STATUS_FINISHED_AUDIT)).collect(Collectors.toList());
            }
            return auditData;
        });
        forcePushTradeToGxAfter(staff, auditData1);
        return auditData1;
    }


    public void forcePushTradeToGxAfter(Staff staff, AuditData auditData) {

        if (CollectionUtils.isNotEmpty(auditData.sourceTrades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]强制推单到供销商,审核通过的订单:%s", TradeUtils.toSidList(auditData.sourceTrades))));
            }
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_AUDIT).setArgs(new Object[]{staff, TradeUtils.toSids(auditData.sourceTrades)}), null);
        }

    }

    public void copyTrades(List<Trade> srcTrades, List<Trade> destTrades) {
        srcTrades.forEach(trade -> {
            Trade newTrade = new TbTrade();
            org.springframework.beans.BeanUtils.copyProperties(trade, newTrade);
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            List<Order> newOrders = Lists.newArrayListWithCapacity(orders.size());
            for (Order order : orders) {
                Order newOrder = new TbOrder();
                org.springframework.beans.BeanUtils.copyProperties(order, newOrder);
                newOrders.add(newOrder);
            }
            TradeUtils.setOrders(newTrade, newOrders);
            destTrades.add(newTrade);
        });
    }

    /**
     * ⅰ. 异常订单
     * 1. 如果是异常订单，则提示“异常订单不允许推送”，排除“库存不足”，“供应商不明确”，"商品未匹配"的异常。
     * ⅱ. 已经明确了供销商的订单
     * 1. 该情况如果单个点击强制推单至供销商，则提示“供销商明确的订单不支持强制推单至供销商”
     * 注意：ERP和快麦通都按照以下内容进行提示
     *
     * @param staff
     * @param originTrades
     * @param auditData
     * @return
     */
    private List<Trade> validateTrade(Staff staff, List<Trade> originTrades, AuditData auditData) {
        if (CollectionUtils.isEmpty(originTrades) || auditData == null) {
            return Collections.emptyList();
        }

        List<Trade> trades = Lists.newArrayListWithExpectedSize(originTrades.size());
        Map<Long, List<Trade>> mergeTradeMap = new HashMap<>();
        Map<Long, TradeResult> errMergeResult = new HashMap<>();
        originTrades.forEach(t -> {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, t)) {
                mergeTradeMap.computeIfAbsent(t.getMergeSid(), v -> new ArrayList<>()).add(t);
            }
            TradeResult result = new TradeResult();
            result.setSid(t.getSid());
            result.setMergeSid(t.getMergeSid());
            auditData.results.put(com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, t) ? t.getMergeSid() : t.getSid(), result);

            if (TradeUtils.isQimenFxSource(t)) {
                addErrTradeResult(staff, t, result, "奇门订单，无法推送", errMergeResult);
                return;
            }
            if (TradeUtils.isAlibabaFxRoleTrade(t)) {
                addErrTradeResult(staff, t, result, "1688分销小店订单，无法推送", errMergeResult);
                return;
            }
            if (TradeUtils.isPlatformFxTrade(t)) {
                addErrTradeResult(staff, t, result, "平台分销订单，无法推送", errMergeResult);
                return;
            }
            if (TradeUtils.isDangKou(t)) {
                addErrTradeResult(staff, t, result, "档口订单，无法推送", errMergeResult);
                return;
            }
            //合单的子单，如果是已审核的单子，可以推送
            if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(t.getSysStatus()) && !(com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, t) && !Objects.equals(t.getSid(), t.getMergeSid()))) {
                addErrTradeResult(staff, t, result, "非待审核订单，无法推送", errMergeResult);
                return;
            }
            if (TradeUtils.isCancel(t)) {
                addErrTradeResult(staff, t, result, "作废订单，无法推送", errMergeResult);
                return;
            }
            // 如果是异常订单，则提示“异常订单不允许推送”，排除“库存不足”，“供应商不明确”的异常。
            Set<String> exceptions = TradeExceptionUtils.analyze(staff, t);
            exceptions.remove(TradeExceptionUtils.EX_INSUFFICIENT);
            exceptions.remove(TradeExceptionUtils.EX_AMBIGUITY_FX);
            // 商品未匹配也要支持强制推送
            if (CollectionUtils.isNotEmpty(exceptions) && exceptions.contains(TradeExceptionUtils.EX_UNALLOCATED)) {
                t.addV(TradeConstants.V_IF_FX_FORCE_PUSH_UNALLOCATED);
                exceptions.remove(TradeExceptionUtils.EX_UNALLOCATED);
            }
            if (CollectionUtils.isNotEmpty(exceptions) || CollectionUtils.isNotEmpty(TradeExceptUtils.getCustomExceptIds(staff, t))) {
                addErrTradeResult(staff, t, result, "异常订单不允许推送", errMergeResult);
                return;
            }
            if (TradeUtils.isFxOrMixTrade(t) && (t.getDestId() != null && t.getDestId() > 0L)) {
                addErrTradeResult(staff, t, result, "该订单已明确了供销商，无法推送", errMergeResult);
                return;
            }
            List<Order> orders = TradeUtils.getOrders4Trade(t);
            Set<String> errTitle = new HashSet<>();
            for (Order order : orders) {
                if ((null == order.getItemSysId() || order.getItemSysId() <= 0) && StringUtils.isBlank(order.getOuterIid()) && StringUtils.isBlank(order.getOuterSkuId())) {
                    errTitle.add(order.getTitle());
                }
            }
            if (CollectionUtils.isNotEmpty(errTitle)) {
                addErrTradeResult(staff, t, result, String.format("商品(名称:%s)在平台未设置商品编码，不能强制推单，请到平台的商家后台完善对应信息", errTitle), errMergeResult);
                return;
            }
            result.setSuccess(Boolean.TRUE);
            if (!com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, t)) {
                trades.add(t);
            }
        });
        //合单处理
        mergeTradeMap.forEach((k, v) -> {
            if (!errMergeResult.containsKey(k)) {
                trades.addAll(v);
            }
        });
        //合单错误信息设置
        auditData.results.putAll(errMergeResult);
        return trades;
    }

    private void addErrTradeResult(Staff staff, Trade trade, TradeResult result, String errMsg, Map<Long, TradeResult> errMergeResult) {
        result.setErrorMsg(errMsg);
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            errMergeResult.put(trade.getMergeSid(), result);
        }
    }

    /**
     * 取消分销属性
     *
     * @param staff
     * @param sids
     * @return
     * @throws Exception
     */
    @Override
    public List<Trade> cancelDistributorAttribute(Staff staff, Long[] sids, ProgressData progressData) {
        //执行业务方法
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            //实际操作的订单总数（包括隐藏订单）
            progressData.setCountAll(progressData.getCountAll() + originTrades.size() - sids.length);
            List<Trade> validateSuccess = checkCancelDistributorAttribute(staff, originTrades, progressData);
            cancelDistributorAttributeBusiness(staff, validateSuccess, progressData);

            return originTrades;
        });
    }

    private List<Trade> checkCancelDistributorAttribute(Staff staff, List<Trade> originTrades, ProgressData progressData) {
        Map<Long, TradeResult> errorMergeSids = new HashMap<>();
        List<Trade> updateTrades = new ArrayList<>();
        Map<String, Trade> gxMapByFxSid = sysTradeDmsBusiness.getGxMapByFxSid(originTrades);
        List<TradeResult> validateError = new ArrayList<>();
        String logHead = LogHelper.buildLogHead(staff).toString();
        for (Trade trade : originTrades) {
            TradeResult result = new TradeResult();
            User user = staff.getUserIdMap().get(trade.getUserId());
            result.setSid(trade.getSid());
            if (!TradeUtils.isFxOrMixTrade(trade)) {
                result.setErrorMsg("不是分销订单不能操作");
                logger.error(new StringBuilder(logHead).append(String.format("%s不是分销订单不能操作!", trade.getSid())));
            } else if (!(TradeStatusUtils.isWaitSellerSend(trade.getSysStatus()) || TradeStatusUtils.isWaitPay(trade.getSysStatus()))) {
                result.setErrorMsg("非待发货分销订单不能操作");
                logger.error(new StringBuilder(logHead).append(String.format("%s为非待发货分销订单不能操作!", trade.getSid())));
            } else if (!TradeUtils.isGxOrMixTrade(trade) && user == null) {
                result.setErrorMsg("您无权操作此订单");
                logger.error(new StringBuilder(logHead).append(String.format("您无权操作此订单[sid:%s]!", trade.getSid())));
            } else if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
                result.setErrorMsg("系统状态为发货后，不能操作");
                logger.error(new StringBuilder(logHead).append(String.format("订单%s系统状态为发货后，不能操作!", trade.getSid())));
            } else if (!MapUtils.isEmpty(gxMapByFxSid) && AuditFxUtils.checkGxExcept(staff, gxMapByFxSid.get(sysTradeDmsBusiness.getMapKeyByFx(trade)), trade)) {
                result.setErrorMsg("取消分销属性异常，关联供销单发货/上传异常，请联系供销商处理异常后再操作");
                logger.error(new StringBuilder(logHead).append(String.format("订单%s关联供销单发货/上传异常，请联系供销商处理异常后再操作", trade.getSid())));
            }
            if (result.getErrorMsg() != null) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    result.setMergeSid(trade.getMergeSid());
                    errorMergeSids.put(trade.getMergeSid(), result);
                }
                validateError.add(result);
                continue;
            }
            updateTrades.add(trade);
        }
        if (CollectionUtils.isEmpty(validateError)) {
            return updateTrades;
        }
        List<Trade> validateSuccess = new ArrayList<>();
        for (Trade trade : updateTrades) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && errorMergeSids.containsKey(trade.getMergeSid())) {
                logger.error(new StringBuilder(logHead).append(String.format("取消分销属性合单校验失败,sid:%s", trade.getSid())));
                TradeResult SonTradeResult = errorMergeSids.get(trade.getMergeSid());
                TradeResult result = new TradeResult();
                result.setSid(trade.getSid());
                result.setMergeSid(trade.getMergeSid());
                result.setErrorMsg(SonTradeResult.getErrorMsg());
                validateError.add(result);
                continue;
            }
            validateSuccess.add(trade);
        }
        //设置校验失败的错误信息与错误数量
        progressData.setErrorNum(progressData.getErrorNum() + validateError.size())
                .setCountCurrent(progressData.getCountCurrent() + validateError.size())
                .getErrorMsg().addAll(validateError.stream().map(TradeResult::getErrorMsg).collect(Collectors.toList()));
        return validateSuccess;
    }

    private void cancelDistributorAttributeBusiness(Staff staff, List<Trade> validateSuccess, ProgressData progressData) {
        Integer suc = 0;
        Integer err = 0;
        try {
            boolean cancelDirect = ConfigHolder.FX_GLOBAL_CONFIG.open("cancel_distributor_attribute_direct", staff.getCompanyId());
            List<Trade> validateFxDirectTradeList = new ArrayList<>();
            List<Trade> validateFxTradeList = new ArrayList<>();
            List<Trade> validateFxAndGxTradeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(validateSuccess)) {
                for (Trade trade : validateSuccess) {
                    if (TradeUtils.isGxAndFxTrade(trade)) {
                        validateFxAndGxTradeList.add(trade);
                    } else {
                        if (cancelDirect || cancelDistributorAttributeDirect(trade)) {
                            validateFxDirectTradeList.add(trade);
                        } else {
                            validateFxTradeList.add(trade);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(validateFxDirectTradeList)) {
                Long[] sids = TradeUtils.toSids(validateFxDirectTradeList);
                List<Trade> copyTrades = new ArrayList<>();
                fxBusiness.copyTrades(validateFxDirectTradeList, copyTrades);
                sysTradeDmsBusiness.fxTradeCancelDistributorAttribute(staff, validateFxDirectTradeList);
                cancelDistributorAttributeAfterBusiness(staff, sids, copyTrades);
                suc += validateFxDirectTradeList.size();
            }
            if (CollectionUtils.isNotEmpty(validateFxTradeList)) {
                //先指定下载平台订单，排除指定下载失败的平台订单，防止无法取消作废
                Map<Long, Trade> checkPlatTrade = checkPlatTrade(staff, validateFxTradeList);
                Long[] sids = TradeUtils.toSids(validateFxTradeList);
                //作废
                cancelBusiness.cancel(staff, sids, null, 1);
                //取消作废, 传入预先查询的平台订单信息
                cancelUndoBusiness.uncancel(staff, sids, true, 1, checkPlatTrade);
                cancelDistributorAttributeAfterBusiness(staff, sids, validateFxTradeList);
                suc += validateFxTradeList.size();
                //checkPlatTrade流程中被过滤的数量
                err += validateSuccess.size() - validateFxAndGxTradeList.size() - validateFxTradeList.size();
            }
            if (CollectionUtils.isNotEmpty(validateFxAndGxTradeList)) {
                Long[] sids = TradeUtils.toSids(validateFxAndGxTradeList);
                List<Trade> copyValidateSuccessvalidateSuccess = new ArrayList<>();
                fxBusiness.copyTrades(validateFxAndGxTradeList, copyValidateSuccessvalidateSuccess);
                sysTradeDmsBusiness.gxAndFxTradeCancelDistributorAttribute(staff, validateFxAndGxTradeList);
                cancelDistributorAttributeAfterBusiness(staff, sids, copyValidateSuccessvalidateSuccess);
                suc += validateFxAndGxTradeList.size();
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "取消分销属性时报错，订单号：").append(TradeUtils.toSidList(validateSuccess)), e);
            //补偿一些未处理的数据,全部为失败
            err = validateSuccess.size() - suc;
        } finally {
            groupProgressBusiness.updateProgress(staff, progressData.setSucNum(progressData.getSucNum() + suc).setErrorNum(progressData.getErrorNum() + err), validateSuccess.size());
        }
    }

    /**
     * 人人商城、秦丝平台，无法指定下载，取消分销属性时，不走作废 + 取消作废的流程
     * 奇门订单
     * 手工订单
     * @param fxTrade 分销订单
     * @return
     */
    private boolean cancelDistributorAttributeDirect(Trade fxTrade) {
        return CommonConstants.PLAT_FORM_TYPE_RENREN.equals(fxTrade.getSource()) || CommonConstants.PLAT_FORM_TYPE_QINSI.equals(fxTrade.getSource()) || TradeUtils.isQimenFxSource(fxTrade) || CommonConstants.PLAT_FORM_TYPE_SYS.equals(fxTrade.getSource());
    }

    private Map<Long, Trade> checkPlatTrade(Staff staff, List<Trade> originTradeList) {
        Map<Long, Trade> result = new HashMap<>();
        Map<Long, List<Long>> mergeSidMap = new HashMap<>();
        //需要移除的合单订单，其主单sid
        Set<Long> mergeSids = new HashSet<>();
        Iterator<Trade> iterator = originTradeList.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            boolean isMerge = null != trade.getMergeSid() && trade.getMergeSid() > 0;
            if (isMerge) {
                mergeSidMap.computeIfAbsent(trade.getMergeSid(), k -> Lists.newArrayList()).add(trade.getSid());
            }
            if (!(CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(trade.getSource()))) {
                try {
                    //合单订单的过滤，在后面处理
                    if (isMerge && mergeSids.contains(trade.getMergeSid())) {
                        logger.warn(LogHelper.buildLogHead(staff).append(String.format("指定下载时，合单订单[mergeSid=%s]中，有子单下载失败，过滤整个合单", trade.getMergeSid())));
                        continue;
                    }
                    User user = staff.getUserByUserId(trade.getUserId());
                    if (user == null) {
                        logger.warn(LogHelper.buildLogHead(staff).append(String.format("指定下载时，找不到店铺信息[userId=%s]", trade.getUserId())));
                        if (isMerge) {
                            mergeSids.add(trade.getMergeSid());
                        }
                        iterator.remove();
                        continue;
                    }
                    Trade downloadTrade = tradeDownloadService.downloadTrade(user, trade.getTid());
                    if (downloadTrade == null) {
                        logger.warn(LogHelper.buildLogHead(user).append(String.format("指定单下载订单为空[sid=%s,tid=%s," + "userId=%s]", trade.getSid(), trade.getTid(), user.getId())));
                        if (isMerge) {
                            mergeSids.add(trade.getMergeSid());
                        }
                        iterator.remove();
                        continue;
                    }
                    result.put(trade.getSid(), downloadTrade);
                } catch (Exception e) {
                    logger.error(LogHelper.buildLogHead(staff).append(String.format("指定单下载订单报错[sid=%s," + "tid=%s," + "userId=%s]", trade.getSid(), trade.getTid(), trade.getUserId())), e);
                    if (isMerge) {
                        mergeSids.add(trade.getMergeSid());
                    }
                    iterator.remove();
                }
            }
        }
        //需要过滤的合单订单
        List<Long> failSid = new ArrayList<>();
        mergeSids.forEach(mergeSid -> {
            List<Long> sids = mergeSidMap.get(mergeSid);
            if (CollectionUtils.isNotEmpty(sids)) {
                failSid.addAll(sids);
            }
        });
        originTradeList.removeIf(trade -> failSid.contains(trade.getSid()));
        failSid.forEach(result::remove);
        return result;
    }

    private void cancelDistributorAttributeAfterBusiness(Staff staff, Long[] sids, List<Trade> validateSuccess) {
        //添加操作日志
        eventCenter.fireEvent(this, new EventInfo("trade.cancel.distributor").setArgs(new Object[]{staff, sids}), validateSuccess);
        //如果有供销订单就作废掉
        if (CollectionUtils.isNotEmpty(validateSuccess)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("取消分销属性触发供销订单作废:%s", TradeUtils.toSidList(validateSuccess))));
            }
            List<Trade> fxTrades = Lists.newArrayListWithCapacity(validateSuccess.size());
            for (Trade trade : validateSuccess) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    if ((trade.getSid() - trade.getMergeSid() == 0)) {
                        fxTrades.add(trade);
                    }
                } else {
                    fxTrades.add(trade);
                }
            }
            fxBusiness.notifyGxDownload(staff, fxTrades, 2);
        }
    }


    /**
     * 供销订单打回
     *
     * @param staff
     * @param sids
     * @return
     */
    @Override
    public List<Trade> repulseGxTrade(Staff staff, Long[] sids, String excepMsg, Integer isCover) {
        //查询数据
        List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
        if (CollectionUtils.isEmpty(originTrades)) {
            return Collections.emptyList();
        }
        final List<Trade> repulseTrades = filterTrades(staff, originTrades);
        Assert.isTrue(!repulseTrades.isEmpty(), "请至少选择一笔待审核状态的供销订单!");
        List<Trade> locks = lockService.locks(tradeLockBusiness.getERPLocks(staff, TradeUtils.toSids(repulseTrades)), () -> {
            //作废供销订单，产生退款流水
            repulseTrades.forEach(trade -> trade.setCancelFrom(2));
            List<Trade> successTrades = fxBusiness.cancelGxTrade(staff, TradeUtils.toSids(repulseTrades), repulseTrades);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("供销商打回作废=%s", TradeUtils.toSidList(successTrades))));
            }
            return successTrades;
        });
        repulseFxTrade(staff, locks, excepMsg, isCover);
        return originTrades;
    }

    @Override
    public Map<String, Object> updateGxFreight(Staff staff, Long sid, Double freight) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sid);
        Assert.isTrue(trades != null && !trades.isEmpty(), "订单不存在");
        Assert.isTrue(TradeUtils.isGxTrade(trades.get(0)), "不支持非供销订单");
        List<Trade> result = sysTradeDmsBusiness.resumeCashFlow(staff, trades, false, freight, null);
        //记录手动修改运费操作
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(TradeUtils.getOrders4Trade(trades.get(0)), OrderModifyLogTypeEnum.GX_POST_FEE_MODIFY));
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("postfee", result.get(0).getPostFee());
        map.put("payment", result.get(0).getPayment());
        return map;
    }

    @Override
    public Trade reCalculateGxFreight(Staff staff, Long sid) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sid);
        Assert.isTrue(trades != null && !trades.isEmpty(), "订单不存在");
        Assert.isTrue(TradeUtils.isGxTrade(trades.get(0)), "不支持非供销订单");
        tradeCalculateTheoryPostFeeBusiness.recalGxPostFee(staff, trades, null);
        return trades.get(0);
    }

    @Override
    public List<DmsOrderFreightDto> matchGroupFreight(Staff staff, Long sid) {
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, sid);
        Assert.isTrue(trades != null && !trades.isEmpty(), "订单不存在");
        Assert.isTrue(TradeUtils.isFxOrMixTrade(trades.get(0)), "不支持非分销订单");
        return tradeCalculateTheoryPostFeeBusiness.matchGroupFreight(staff, trades);
    }

    @Override
    public List<Trade> mutualConvertPlatformFxTrade(Staff staff, Long[] sids) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            List<Trade> waitAuditTrades = originTrades.stream().filter(trade -> TradeStatusUtils.isWaitAudit(trade.getSysStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(waitAuditTrades)) {
                throw new IllegalArgumentException("不存在待审核普通订单，不存在待供销商发货天猫分销订单");
            }
            boolean allCommonMatch = waitAuditTrades.stream().allMatch(TradeUtils::isCommonSource);
            boolean allPlatformFxMatch = waitAuditTrades.stream().allMatch(TradeUtils::isPlatformFxTrade);
            Assert.isTrue(allCommonMatch || allPlatformFxMatch, "请选择同一类型的订单进行转化，您同时选择了[天猫分销订单]和[普通订单]");
            //转平台分销单
            if (allCommonMatch) {
                convertPlatformFxBusiness(staff, waitAuditTrades);
            } else if (allPlatformFxMatch) {
                convertCommonBusiness(staff, waitAuditTrades);
            }
            return waitAuditTrades;
        });
    }

    private void convertPlatformFxBusiness(Staff staff, List<Trade> tradeList) {
        List<Trade> copyTradeList = new ArrayList<>();
        fxBusiness.copyTrades(tradeList, copyTradeList);
        for (Trade trade : tradeList) {
            trade.setConvertType(TradeConstants.FX_PLAT_CONVERT_TYPE);
            trade.setBelongType(TradeConstants.FX_SYS_BELONG_TYPE);
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            for (Order order : orderList) {
                order.setConvertType(TradeConstants.FX_PLAT_CONVERT_TYPE);
                order.setBelongType(TradeConstants.FX_SYS_BELONG_TYPE);
                if (order.getSuits() != null) {
                    for (Order suit : order.getSuits()) {
                        suit.setConvertType(TradeConstants.FX_PLAT_CONVERT_TYPE);
                        suit.setBelongType(TradeConstants.FX_SYS_BELONG_TYPE);
                    }
                }
            }
        }
        //匹配供销仓库
        warehouseAllocateBusiness.matchWarehouse(staff, tradeList);
        //设置库存信息
        sysTradeDmsBusiness.filterFxTrade(staff, tradeList, 1);
        //更新数据库
        List<Order> orderList = OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(tradeList), false);
        tradeUpdateService.updateTrades(staff, tradeList, orderList);
        //添加操作日志
        eventCenter.fireEvent(this, new EventInfo("trade.add.platformFx").setArgs(new Object[]{staff, TradeUtils.toSids(tradeList)}), tradeList);
        //释放库存
        if (CollectionUtils.isNotEmpty(copyTradeList)) {
            try {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("needResume:%s", TradeUtils.toSidList(copyTradeList))));
                }
                orderStockService.resumeTradeStockLocal(staff, copyTradeList);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "转为平台分销订单时归还库存报错，订单号：").append(TradeUtils.toSidList(copyTradeList)), e);
            }
        }
    }

    public void convertCommonBusiness(Staff staff, List<Trade> tradeList) {
        for (Trade trade : tradeList) {
            trade.setBelongType(TradeConstants.NORMAL_CONVERT_TYPE);
            trade.setConvertType(TradeConstants.NORMAL_CONVERT_TYPE);
            trade.setWarehouseId(null);
            TradeExceptUtils.setStockStatus(staff, trade, Trade.STOCK_STATUS_EMPTY);
            trade.setTemplateId(-1L);
            trade.setTemplateType(0);
            trade.setLogisticsCompanyId(0L);
            trade.setOutSid("");
            for (Order order : TradeUtils.getOrders4Trade(trade)) {
                order.setBelongType(TradeConstants.NORMAL_CONVERT_TYPE);
                order.setConvertType(TradeConstants.NORMAL_CONVERT_TYPE);
                OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
                order.setStockNum(0);
                if (order.getSuits() != null) {
                    for (Order suit : order.getSuits()) {
                        suit.setBelongType(TradeConstants.NORMAL_CONVERT_TYPE);
                        suit.setConvertType(TradeConstants.NORMAL_CONVERT_TYPE);
                        OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
                        suit.setStockNum(0);
                    }
                }
            }
        }
        sysTradeDmsBusiness.doGxAndFxTradeCancelDistributorAttribute(staff, tradeList, 2);
        //添加操作日志
        eventCenter.fireEvent(this, new EventInfo("trade.cancel.platformFx").setArgs(new Object[]{staff, TradeUtils.toSids(tradeList)}), tradeList);
    }

    @Override
    public void splitDestMix(Staff staff, TbTrade frontTrade, String groupCount) {
        List<TbTrade> tbTrades = TradeUtils.toTbTrades(tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, frontTrade.getSid()));
        TbTrade originGxTrade = CollectionUtils.isNotEmpty(tbTrades) ? tbTrades.get(0) : null;
        if (TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()) {
            Assert.isTrue(originGxTrade != null && TradeUtils.isGxTrade(originGxTrade) && TradeStatusUtils.isWaitAudit(originGxTrade.getSysStatus()), "请选择一笔待审核的供销订单进行操作！");
            tradeSplitService.splitMix(staff, frontTrade, groupCount, false);
            return;
        }
        if (originGxTrade != null) {
            checkSplitGxTrade(originGxTrade);
        }
        //通过白名单，控制走供分销的新拆分模式
        if (tradeLocalConfig.isTradeSplitFx2GxCompanyIds(staff.getCompanyId())) {
            splitDestMixFxTradeHandleNew(staff, frontTrade, groupCount);
        } else {
            if (originGxTrade != null) {
                splitDestMixFxTradeHandle(staff, originGxTrade, frontTrade, groupCount);
            }
        }

    }

    @Override
    public List<Trade> matchExpress(User user, List<Trade> trades) {
        return simulateExpressMatchBusiness.matchExpress(user, trades);
    }

    private void checkSplitGxTrade(TbTrade originGxTrade) {
        Assert.isTrue(originGxTrade != null && TradeUtils.isGxTrade(originGxTrade) && TradeStatusUtils.isWaitAudit(originGxTrade.getSysStatus()), "请选择一笔待审核的供销订单进行操作！");
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(originGxTrade.getSourceId());
        Assert.isTrue(dmsDistributorConfigDto != null && dmsDistributorConfigDto.getAllowSupplierMixSplitTrade(), "您的分销商不允许拆分订单，请联系分销商开启相关配置！");
        if (TradeUtils.ifContainV(originGxTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX))  {
            throw new IllegalArgumentException("执行过套转单/商品搭配，不支持拆分。如需要拆分请开启配置「允许供销订单与分销订单的商品信息和订单信息不一致」");
        }
    }

    private void splitDestMixFxTradeHandle(Staff staff, TbTrade originGxTrade, TbTrade frontTrade, String groupCount) {
        Staff fxStaff = staffService.queryFullByCompanyId(originGxTrade.getSourceId());
        fxStaff.setUserIdMap(fxStaff.getUsers().stream().collect(Collectors.toMap(User::getId, user -> user)));
        List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(fxStaff, true, false, true, Long.valueOf(originGxTrade.getTid()));
        if (CollectionUtils.isEmpty(fxTrades)) {
            throw new IllegalArgumentException("对应分销订单不存在");
        }
        TbTrade originFxTrade = null;
        if (fxTrades.size() == 1) {
            originFxTrade = (TbTrade) fxTrades.get(0);
        } else {
            List<Order> fxOrderList = new ArrayList<>();
            for (Trade trade : fxTrades) {
                if (trade.getSid().equals(Long.valueOf(originGxTrade.getTid()))) {
                    originFxTrade = (TbTrade) trade;
                }
                fxOrderList.addAll(TradeUtils.getOrders4Trade(trade));
            }
            if (originFxTrade == null) {
                throw new IllegalArgumentException("对应分销订单不存在");
            }
            TradeUtils.setOrders(originFxTrade, fxOrderList);
        }
        //分销订单反审核
        List<Trade> successAuditUndoTrades = tradeAuditService.unaudit(fxStaff, OpEnum.AUDIT_UNDO_AUTO_FX_GX_MIX_SPLIT, new Long[]{originFxTrade.getSid()}).getSuccessTrades();
        if (CollectionUtils.isEmpty(successAuditUndoTrades)) {
            throw new RuntimeException("分销订单反审核失败");
        }
        SplitResult splitResult = null;
        try {
            splitResult = doSplitFxTrade(fxStaff, originFxTrade, frontTrade, groupCount);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("供销触发分销订单混合拆分失败,供销sid=%s", originGxTrade.getSid())).toString(), e);
        }
        if (splitResult == null || (CollectionUtils.isEmpty(splitResult.getInsertSids()) && CollectionUtils.isEmpty(splitResult.getUpdateSids()))) {
            logger.error(LogHelper.buildLog(staff, "分销订单混合拆分失败,进行异常处理"));
            fxSplitFailHandle(staff, fxStaff, originFxTrade, originGxTrade);
            throw new RuntimeException("分销订单混合拆分失败");
        }
        //拆分分销订单审核
        List<Long> needAuditSidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(splitResult.getUpdateSids())) {
            needAuditSidList.addAll(splitResult.getUpdateSids());
        }
        if (CollectionUtils.isNotEmpty(splitResult.getInsertSids())) {
            needAuditSidList.addAll(splitResult.getInsertSids());
        }
        AuditData auditData = AuditUtils.init(needAuditSidList.toArray(new Long[0]), tradeConfigService.get(staff), staff.openAuditActiveStockRecord(), false, "", 0, 98);
        lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, auditData.originSids), () -> tradeAuditService.audit(fxStaff, auditData));
        auditStockBusiness.auditStockWms(staff, auditData);

        Map<Long, TradeResult> resultMap = auditData.results;
        List<Long> auditFailSidList = new ArrayList<>();
        for (Map.Entry<Long, TradeResult> entry : resultMap.entrySet()) {
            TradeResult tradeResult = entry.getValue();
            if (tradeResult.getErrorMsg() != null) {
                auditFailSidList.add(tradeResult.getSid());
            }
        }
        if (CollectionUtils.isNotEmpty(auditFailSidList)) {
            splitDestFxAuditFailHandle(staff, fxStaff, auditFailSidList, originGxTrade);
        }
    }

    /**
     * 新模块拆分实现，底层是支持批量操作的
     *
     * @param staff         操作人员
     * @param originGxTrade
     * @param frontTrade    前端传的拆分数据，可以满足要求
     * @param groupCount
     */
    private void splitDestMixFxTradeHandleNew(Staff staff, TbTrade frontTrade, String groupCount) {
        tradeSplitService.splitMix(staff, frontTrade, groupCount, true);
    }

    private void splitDestFxAuditFailHandle(Staff staff, Staff fxStaff, List<Long> auditFailSidList, TbTrade originGxTrade) {
        String action = "供销混合拆分";
        String content = "供销混合拆分。";
        List<TradeTrace> tradeTraceList = TradeTraceUtils.createTradeTrace(fxStaff.getCompanyId(), auditFailSidList, action, staff.getName(), new Date(), content);
        tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
        if (!auditFailSidList.contains(Long.valueOf(originGxTrade.getTid()))) {
            return;
        }
        lockService.locks(tradeLockBusiness.getERPLocks(staff, originGxTrade.getSid()), () -> {
            //作废供销订单，产生退款流水
            originGxTrade.setCancelFrom(6);
            List<Trade> successTrades = fxBusiness.cancelGxTrade(staff, new Long[]{originGxTrade.getSid()}, Lists.newArrayList(originGxTrade));
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("分销拆单自动审核失败，打回作废=%s", TradeUtils.toSidList(successTrades))));
            }
            return successTrades;
        });
    }

    private void fxSplitFailHandle(Staff staff, Staff fxStaff, TbTrade originFxTrade, TbTrade originGxTrade) {
        lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, originFxTrade.getSid()), () -> {
            TbTrade fxTrade = tbTradeDao.queryBySid(fxStaff, originFxTrade.getSid());
            tradeFillService.fill(fxStaff, fxTrade);
            if (fxTrade != null) {
                TradeExceptUtils.updateExcept(fxStaff, fxTrade, ExceptEnum.FX_REPULSE, 1L);
                fxTrade.setIsExcep(1);
                StringBuffer sb;
                if (StringUtils.isNotBlank(fxTrade.getExceptMemo())) {
                    sb = new StringBuffer(fxTrade.getExceptMemo()).append(";").append("供销混合拆分");
                } else {
                    sb = new StringBuffer("供销混合拆分");
                }
                fxTrade.setExceptMemo(sb.toString());
                fxTrade.getOperations().put(OpEnum.REPULSE_GX_TRADE, "供销混合拆分失败，供销商打回订单，自动增加供销商打回异常");
                tradeUpdateService.updateTrades(fxStaff, fxTrade);
                tradeTraceBusiness.asyncTrace(fxStaff, Lists.newArrayList(fxTrade), OpEnum.REPULSE_GX_TRADE);
            }
            return fxTrade;
        });
        lockService.locks(tradeLockBusiness.getERPLocks(staff, originGxTrade.getSid()), () -> {
            //作废供销订单，产生退款流水
            originGxTrade.setCancelFrom(6);
            List<Trade> successTrades = fxBusiness.cancelGxTrade(staff, new Long[]{originGxTrade.getSid()}, Lists.newArrayList(originGxTrade));
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("分销自动拆分失败，打回作废=%s", TradeUtils.toSidList(successTrades))));
            }
            return successTrades;
        });
    }

    private SplitResult doSplitFxTrade(Staff fxStaff, TbTrade originFxTrade, TbTrade frontTrade, String groupCount) {
        List<Order> frontOrders = TradeUtils.getOrders4Trade(frontTrade);
        TbTrade tbTrade = new TbTrade();
        tbTrade.setSid(originFxTrade.getSid());
        List<Order> updateOrders = new ArrayList<>();
        for (Order frontOrder : frontOrders) {
            Order order = new TbOrder();
            order.setId(frontOrder.getOid());
            order.setNum(frontOrder.getNum());
            order.setSid(originFxTrade.getSid());
            order.setSplitNum(frontOrder.getSplitNum());
            updateOrders.add(order);
        }
        TradeUtils.setOrders(tbTrade, updateOrders);
        return tradeSplitService.splitMix(fxStaff, tbTrade, groupCount, false);
    }

    private void repulseFxTrade(Staff staff, List<Trade> repulseTrades, String excepMsg, Integer isCover) {
        //将对应的分销订单打回待审核，分销订单异常状态增加“供销商打回”异常进入异常订单tab，订单增加操作日志“供销商打回订单，自动增加供销商打回异常”
        Map<Long, List<Trade>> sourceTradeMap = TradeUtils.groupBySourceId(repulseTrades);
        sourceTradeMap.forEach((sourceId, trades) -> {
            Staff sourceStaff = staffService.queryFullByCompanyId(sourceId);
            transactionTemplateAdapter.execute(sourceStaff, new TransactionCallback<Object>() {
                @Override
                public Object doInTransaction(TransactionStatus status) {
                    List<Long> tidLong = TradeUtils.toTidList(trades).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    Long[] tidArray = tidLong.toArray(new Long[0]);
                    return lockService.locks(tradeLockBusiness.getERPLocks(sourceStaff, tidArray), () -> {
                        sourceStaff.setUserIdMap(sourceStaff.getUsers().stream().collect(Collectors.toMap(User::getId, user -> user)));
                        //对应的分销订单反审核
                        List<Trade> sourceTrades = tradeSearchService.queryBySids(sourceStaff, true, tidArray);
                        List<Trade> relationSourceTrades = sourceTrades.stream().filter(t -> staff.getCompanyId().equals(t.getDestId())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(relationSourceTrades)) {
                            return trades;
                        }
                        //增加供销商打回异常
                        relationSourceTrades.forEach(trade -> {
                            TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.FX_REPULSE,1L);
                            trade.setIsExcep(1);
                            if (StringUtils.isNotBlank(excepMsg)) {
                                StringBuilder sb;
                                if ((isCover != null && isCover == 1)) {
                                    sb = new StringBuilder(excepMsg);
                                } else {
                                    if (StringUtils.isNotBlank(trade.getExceptMemo())) {
                                        sb = new StringBuilder(trade.getExceptMemo()).append(";").append(excepMsg);
                                    } else {
                                        sb = new StringBuilder(excepMsg);
                                    }
                                }
                                trade.setExceptMemo(sb.toString());
                            }
                            trade.getOperations().put(OpEnum.REPULSE_GX_TRADE, "供销商打回订单，自动增加供销商打回异常");
                        });
                        tradeUpdateService.updateTrades(sourceStaff, relationSourceTrades);
                        tradeTraceBusiness.asyncTrace(sourceStaff, relationSourceTrades, OpEnum.REPULSE_GX_TRADE);
                        tradeAuditService.unaudit(sourceStaff, OpEnum.AUDIT_UNDO_AUTO_FX_GX_REPULSE, TradeUtils.toSids(relationSourceTrades));
                        return trades;
                    });
                }
            });
        });


    }

    private List<Trade> filterTrades(Staff staff, List<Trade> originTrades) {
        List<Trade> repulseTrades = Lists.newArrayListWithCapacity(originTrades.size());
        for (Trade originTrade : originTrades) {
            //供销订单并且是待审核
            if (TradeUtils.isGxOrMixTrade(originTrade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(originTrade.getSysStatus())) {
                repulseTrades.add(originTrade);
            }
        }
        return repulseTrades;
    }

    /**
     * 取消分销商未付款
     *
     * @param staff
     * @param trades
     * @param logger
     * @return
     */
    private List<Trade> createUpdateTrades(Staff staff, List<Trade> trades, Logger logger) {
        List<Trade> updates = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade, false, true, false, true);
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_WAITPAY, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.FX_WAITPAY, 0L);
            updateTrade.setIsExcep(TradeExceptUtils.isExcept(staff, trade) ? 1 : 0);
            if ((trade.getV() != null && (trade.getV() | TradeConstants.V_TRADE_FX_PRICE_EXCEPT) - trade.getV() == 0)) {
                updateTrade.setTotalFee(trade.getTotalFee());
                updateTrade.setPayment(trade.getPayment());
                updateTrade.setPayAmount(trade.getPayAmount());
            }
            updates.add(updateTrade);
        }
        return updates;
    }

    /**
     * 更新供销分销价
     *
     * @param staff
     * @param trades
     * @return
     */
    private List<Order> createUpdateOrders(Staff staff, List<Trade> trades) {
        List<Order> updates = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                TbOrder updateOrder = new TbOrder();
                updateOrder.setId(order.getId());
                updateOrder.setTotalFee(order.getTotalFee());
                updateOrder.setPayment(order.getPayment());
                updateOrder.setPayAmount(order.getPayAmount());
                updateOrder.setPrice(order.getPrice());
                updates.add(updateOrder);
            }
        }
        return updates;
    }


    /**
     * 新增分销属性回收快递号
     *
     * @param trades
     */
    private void cancelTidSysTidOutSid(Staff staff, List<Trade> trades) {
        try {
            String[] logKey = new String[]{"sid",
                    "templateId", "outSid", "templateType", "logisticsCompanyId", "oldOutSid"};
            new DevLogBuilder(staff).appendHead("新增分销属性回收快递号")
                    .appendJSon(trades, logKey).printDebug(logger);
            if (CollectionUtils.isEmpty(trades)) {
                return;
            }
            List<Trade> needCancelOutSidTrades = trades.stream()
                    .filter(trade -> StringUtils.isNotBlank(trade.getOutSid()) && !trade.hasOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needCancelOutSidTrades)) {
                WlbStatus wlbStatus = expressTemplateCommonService.cancelTidSysTidOutSid(staff, needCancelOutSidTrades, EnumOutSidStatus.BOUND.getValue());
                fireOutsidStatus(staff, needCancelOutSidTrades, wlbStatus);
                FxLogBuilder.gx(staff).appendHead("新增分销属性回收快递号,")
                        .appendJSon(trades, logKey)
                        .append("WlbStatus=" + JSONObject.toJSONString(wlbStatus))
                        .printDebug(logger);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "新增分销属性回收快递号报错，订单号：").append(TradeUtils.toSidList(trades)), e);
        }
    }

    /**
     * 修改运单回收池状态
     */
    private void fireOutsidStatus(Staff staff, List<Trade> needCancelOutSidTrades, WlbStatus wlbStatus) {
        PathParam pathParam = new PathParam();
        pathParam.setSidAndOutSidMap(needCancelOutSidTrades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getOutSid, (t1, t2) -> t1)));
        List<WlbResult> errorResults = (List<WlbResult>) wlbStatus.getErrorResult();
        List<WlbResult> successResult = (List<WlbResult>) wlbStatus.getSuccessResult();
        List<ResultInfo> successList = CollectionUtils.isEmpty(successResult) ? new ArrayList<>() :
                successResult.stream().map(r -> new ResultInfo(Long.valueOf(r.getSid()), r.getOutSid(), r.getErrorMsg(), true)).collect(Collectors.toList());
        List<ResultInfo> errorList = CollectionUtils.isEmpty(errorResults) ? new ArrayList<>() :
                errorResults.stream().map(r -> new ResultInfo(Long.valueOf(r.getSid()), r.getOutSid(), r.getErrorMsg(), false)).collect(Collectors.toList());
        PtWaybillPathContext ptWaybillPathContext = PtWaybillPathContext.builder()
                .staff(staff)
                .pathParam(pathParam)
                .pathResult(PathResult.builder().successResults(successList).errorResults(errorList).build())
                .waybillPath(DMS_RECYCLED)
                .build();
        eventCenter.fireEvent(this, new EventInfo("waybill.cycle.modify").setArgs(new Object[]{staff, ptWaybillPathContext.getWaybillPath().getName(), ptWaybillPathContext}), null);
    }

}

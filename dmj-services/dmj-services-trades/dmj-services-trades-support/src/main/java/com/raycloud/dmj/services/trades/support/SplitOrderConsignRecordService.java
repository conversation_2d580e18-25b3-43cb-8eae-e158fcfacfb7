package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.dao.trade.SplitOrderConsignRecordDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.SplitOrderConsignRecord;
import com.raycloud.dmj.services.trades.ISplitOrderConsignRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SplitOrderConsignRecordService implements ISplitOrderConsignRecordService {

    @Resource
    SplitOrderConsignRecordDao dao;

    @Override
    public SplitOrderConsignRecord queryByOid(Staff staff, Long oid) {
        return dao.queryByOid(staff, oid);
    }

    @Override
    public List<SplitOrderConsignRecord> queryByTid(Staff staff, String tid) {
        return dao.queryByTid(staff, tid);
    }

    @Override
    public Long insert(Staff staff, SplitOrderConsignRecord record) {
        return dao.insert(staff, record);
    }

    @Override
    public void batchInsert(Staff staff, List<SplitOrderConsignRecord> records) {
        dao.batchInsert(staff, records);
    }
}

package com.raycloud.dmj.services.trades.support.utils;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.lang.BooleanUtils;
import org.apache.http.util.Args;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.raycloud.dmj.services.log.IOpLogService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

/**
 * @author: qingfeng.cxb
 * @create: 2019-05-17 18:42
 */
public class ConfigLogUtils {


    private static final Logger logger = Logger.getLogger(ConfigLogUtils.class);

    /**
     * 拼接日志
     *
     * @param buf
     * @param title
     * @param newValue
     * @param oldValue
     */
    public static void addLogContent(StringBuilder buf, String title, Object newValue, Object oldValue) {
        addLogContent(null, buf, title, newValue, oldValue);
    }

    public static void addLogCNContent(StringBuilder buf, String title, Object newValue, Object oldValue) {
        addLogContent(null, buf, title, convertBooleanValue(newValue), convertBooleanValue(oldValue));
    }

    public static void addLogCNContent(Staff staff, StringBuilder buf, String title, Object newValue, Object oldValue) {
        addLogContent(staff, buf, title, convertBooleanValue(newValue), convertBooleanValue(oldValue));
    }

    /**
     * 拼接日志
     *
     * @param buf
     * @param title
     * @param newValue
     * @param oldValue
     */
    public static void addLogContent(Staff staff, StringBuilder buf, String title, Object newValue, Object oldValue) {
        if (newValue == null || oldValue == null) {
            return;
        }
        if (newValue instanceof Integer) {
            Integer oldV = (Integer) oldValue;
            Integer newV = (Integer) newValue;
            if (oldV - newV != 0) {
                if (buf.length() > 0) {
                    buf.append("; ");
                }
                buf.append(title).append(":").append(oldV).append("->").append(newV);
            }
        } else if (newValue instanceof String) {
            String oldV = oldValue.toString();
            String newV = newValue.toString();
            if (!newV.equals(oldV)) {
                if (buf.length() > 0) {
                    buf.append("; ");
                }
                buf.append(title).append(":").append(oldV).append("->").append(newV);
            }
        } else if (newValue instanceof Boolean) {
            Boolean oldV = (Boolean) oldValue;
            Boolean newV = (Boolean) newValue;
            if (oldV.compareTo(newV) != 0) {
                if (buf.length() > 0) {
                    buf.append("; ");
                }
                buf.append(title).append(":").append(oldV).append("->").append(newV);
            }
        } else if(newValue instanceof List){
            try{
                String oldV = JSONObject.toJSONString(oldValue);
                String newV = JSONObject.toJSONString(newValue);
                if(!newV.equals(oldV)){
                    if (buf.length() > 0) {
                        buf.append("; ");
                    }
                    buf.append(title).append(":").append(oldV).append("->").append(newV);
                }
            }catch (Exception e){
                if(staff != null){
                    logger.error(LogHelper.buildErrorLog(staff, e, "记录日志处理list类型的数据报错！"));
                }else{
                    logger.error("记录日志处理list类型的数据报错！", e);
                }
            }
        }
    }

    public static Object convertBooleanValue(Object value) {
        if (value instanceof Integer) {
            Integer v = (Integer) value;
            return Objects.equals(v, CommonConstants.JUDGE_YES) ? "是" : "否";
        } else if (value instanceof Boolean) {
            Boolean v = (Boolean) value;
            return BooleanUtils.isTrue(v) ? "是" : "否";
        }
        return value;
    }

    /**
     * 组装订单日志
     */
    public static TradeTrace createTradeTrace(User user, Long sid, Date matchTime, String content, Staff staff) {
        TradeTrace trace = new TradeTrace();
        trace.setTaobaoId(user.getTaobaoId());
        trace.setCompanyId(user.getStaff().getCompanyId());
        trace.setSid(sid);
        trace.setAction("审核后自动获取单号");
        String operator = staff == null ? "xit" : staff.getName();
        trace.setOperator(operator);
        trace.setOperateTime(matchTime);
        trace.setContent(content);
        trace.setEnableStatus(1);
        return trace;
    }

    /**
     * 组装系统日志
     *
     * @param staff   公司
     * @param ip      IP
     * @param domain  业务领域
     * @param action  业务方法名称
     * @param content 业务操作内容
     * @param args    业务参数
     */
    public static OpLog buildOpLog(Staff staff, String ip, Domain domain, String action, String content, String args) {
        OpLog opLog = new OpLog();
        opLog.setCompanyId(staff.getCompanyId());
        opLog.setStaffId(staff.getId());
        opLog.setAccountName(staff.getAccountName());
        opLog.setStaffName(staff.getName());
        opLog.setIp(ip);
        opLog.setDomain(domain);//领域，例如订单领域为trade，商品领域为item，库存领域为stock
        opLog.setAction(action);//业务方法名称
        opLog.setContent(content);//业务操作内容
        opLog.setArgs(args); //json化的业务参数
        opLog.setCreated(new Date());
        return opLog;
    }

    /**
     * 保存日志
     */
    public static void addUpdateLog(Staff staff, String content, String action, IOpLogService opLogService) {
        if (StringUtils.isNotEmpty(content)) {
            OpLog log = new OpLog();
            log.setDomain(OpLog.DOMAIN_TRADE);
            log.setAction(action);
            log.setKey(staff.getCompanyId().toString());
            log.setCompanyId(staff.getCompanyId());
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setAccountName(staff.getAccountName());
            log.setContent(content);
            opLogService.record(staff, log);
        }
    }

    /**
     * 拼接日志
     *
     * @param buf
     * @param title
     * @param newValue
     * @param oldValue
     */
    public static void addLogContentByStr(StringBuilder buf, String title, Object newValue, Object oldValue) {
        addLogContent(null, buf, title, newValue == null ? "" : String.valueOf(newValue), oldValue == null ? "" : String.valueOf(oldValue));
    }
}

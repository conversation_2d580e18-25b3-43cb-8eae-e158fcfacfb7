package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.invoice.TradeInvoiceDao;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.invoice.*;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * @Description 订单发票信息
 * <AUTHOR>
 * @Date 2023/9/21 2:03 下午
 */
@Service
public class TradeInvoiceService implements ITradeInvoiceService {
    @Resource
    TradeInvoiceDao tradeInvoiceDao;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    TradeSysLabelBusiness tradeTagUpdateBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Override
    @Transactional
    public void saveTradeInvoices(Staff staff, InvoiceHandleFrom from, List<TradeInvoice> tradeInvoices) {
        if (from == null) {
            throw new TradeException("请传入操作来源！");
        }
        if (CollectionUtils.isEmpty(tradeInvoices)) {
            return;
        }

        Set<Long> sids = new HashSet<>();
        for (TradeInvoice tradeInvoice : tradeInvoices) {
            if (tradeInvoice.getSid() == null) {
                throw new TradeException("请输入系统单号！");
            }
            if (tradeInvoice.getEnableStatus() == null) {
                throw new TradeException("请设置enableStatus！");
            }
            tradeInvoice.setHandleFrom(from.getFrom());
            sids.add(tradeInvoice.getSid());
        }

        // 查询已存在的发票信息
        Map<Long, TradeInvoice> tradeInvoiceMap = TradeInvoiceUtils.tradeInvoice2Map(tradeInvoiceDao.queryBySids(staff, new ArrayList<>(sids)));

        InvoiceData invoiceData = new InvoiceData();
        for (TradeInvoice newInvoice : tradeInvoices) {
            TradeInvoice oldInvoice = tradeInvoiceMap.get(newInvoice.getSid());
            if (oldInvoice != null) {
                if (newInvoice.getEnableStatus() == 0) {//删除
                    invoiceData.deleteList.add(oldInvoice);
                    invoiceData.tradeTraceList.add(tradeTrace(newInvoice.getSid(), from.getMsg() + "删除发票信息"));
                } else if (!TradeInvoiceUtils.equals(newInvoice, oldInvoice)) {
                    invoiceData.deleteList.add(oldInvoice);
                    invoiceData.insertList.add(newInvoice);
                    invoiceData.tradeTraceList.add(tradeTrace(newInvoice.getSid(), from.getMsg() + "修改发票信息"));
                }
            } else {
                invoiceData.insertList.add(newInvoice);
                invoiceData.tradeTraceList.add(tradeTrace(newInvoice.getSid(), from.getMsg() + "新增发票信息"));
            }
            if (InvoiceHandleFrom.FMS != from && newInvoice.getEnableStatus() != 0) {
                if (!StringUtils.isEmpty(newInvoice.getInvoiceName())) {
                    invoiceData.addTagSids.add(newInvoice.getSid());
                } else {
                    invoiceData.removeTagSids.add(newInvoice.getSid());
                }
            }
        }
        saveData(staff, invoiceData);
    }

    @Override
    public void addTradeInvoices(Staff staff, List<TradeInvoice> tradeInvoices) {
        if (CollectionUtils.isNotEmpty(tradeInvoices)) {
            tradeInvoiceDao.batchInsert(staff, tradeInvoices);
        }
    }

    private void saveData(Staff staff, InvoiceData invoiceData) {
        //删除
        tradeInvoiceDao.batchDelete(staff, invoiceData.deleteList);
        //新增
        tradeInvoiceDao.batchInsert(staff, invoiceData.insertList);
        //标签
        if (invoiceData.addTagSids.size() > 0) {
            tradeTagUpdateBusiness.handleTagsBySids(staff, invoiceData.addTagSids, OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_NEED_INVOICE), Boolean.TRUE);
        }
        if (invoiceData.removeTagSids.size() > 0) {
            tradeTagUpdateBusiness.handleTagsBySids(staff, invoiceData.removeTagSids, OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_NEED_INVOICE), Boolean.FALSE);
        }
        List<Trade> updateTrades = new ArrayList<>();
        for (TradeInvoice trade : invoiceData.deleteList) {
            Trade update = new Trade();
            update.setSid(trade.getSid());
            update.addV(TradeConstants.V_IF_SAVE_INVOICES);
            update.setUpdTime(new Date());
            updateTrades.add(update);
        }
        for (TradeInvoice trade : invoiceData.insertList) {
            Trade update = new Trade();
            update.setSid(trade.getSid());
            update.addV(TradeConstants.V_IF_SAVE_INVOICES);
            update.setUpdTime(new Date());
            updateTrades.add(update);
        }
        tradeUpdateService.batchUpdateTrades(staff, updateTrades, null);
        //操作日志
        tradeTraceBusiness.asyncTrace(staff, invoiceData.tradeTraceList, OpEnum.TRADE_INVOICE_EDIT);
    }

    private Trade tradeTrace(Long sid, String operation) {
        Trade traceTrade = new Trade();
        traceTrade.setSid(sid);
        traceTrade.getOperations().put(OpEnum.TRADE_INVOICE_EDIT, operation);
        return traceTrade;
    }

    private static class InvoiceData {
        List<TradeInvoice> insertList = new ArrayList<>();
        List<TradeInvoice> deleteList = new ArrayList<>();
        List<Trade> tradeTraceList = new ArrayList<>();

        List<Long> addTagSids = new ArrayList<>();
        List<Long> removeTagSids = new ArrayList<>();
    }
}

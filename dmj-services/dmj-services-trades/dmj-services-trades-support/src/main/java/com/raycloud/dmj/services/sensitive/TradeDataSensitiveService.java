package com.raycloud.dmj.services.sensitive;

import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.common.PddSecretUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @description 针对订单内某个属性数据的脱敏
 * @date 2022-02-10
 */
@Service
public class TradeDataSensitiveService {

    private final Map<String, Function<String, Boolean>> SENSITIVE_BUYER_NICK_PLATFORM = new HashMap<>();

    {
        SENSITIVE_BUYER_NICK_PLATFORM.put(CommonConstants.PLAT_FORM_TYPE_PDD, PddSecretUtils::isEncryptData);
        SENSITIVE_BUYER_NICK_PLATFORM.put(CommonConstants.PLAT_FORM_TYPE_FXG, data -> CipherTextUtils.ifEncrypt(data) || (data.length() > 40 && data.endsWith("=")));
    }

    /**
     * 对订单列表根据不同平台进行旺旺脱敏处理
     */
    public void sensitiveTradeBuyerNick(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        SENSITIVE_BUYER_NICK_PLATFORM.keySet().forEach(source -> {
            List<Trade> platformTrades = trades.stream().filter(trade -> TradeUtils.platformMatch(staff, trade, source)).collect(Collectors.toList());
            platformTrades.forEach(trade -> {
                if (SENSITIVE_BUYER_NICK_PLATFORM.get(source).apply(trade.getBuyerNick())) {
                    sensitiveTradeBuyerNick(trade);
                }
            });
        });
    }

    /**
     * 对订单的昵称进行脱敏处理
     */
    private void sensitiveTradeBuyerNick(Trade trade) {
        final String sensitiveStr = "****";
        trade.setBuyerNick(sensitiveStr);
    }

}

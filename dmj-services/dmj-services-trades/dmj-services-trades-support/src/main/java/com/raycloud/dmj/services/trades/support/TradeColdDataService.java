package com.raycloud.dmj.services.trades.support;

import com.alibaba.dubbo.rpc.RpcException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.query.TradeAssembleBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.TradeOpenUidTransformBusiness;
import com.raycloud.dmj.data.dubbo.api.TradeColdDataQueryApi;
import com.raycloud.dmj.data.dubbo.common.ThrottlingException;
import com.raycloud.dmj.data.dubbo.request.TradeColdQueryParameter;
import com.raycloud.dmj.data.dubbo.response.PageResponse;
import com.raycloud.dmj.data.dubbo.tools.ParameterConverts;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeSysConsignUtils;
import com.raycloud.dmj.domain.trades.utils.TradeSysDigestUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.filter.ITradeFilterService;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 *
 */
@Service
public class TradeColdDataService {
    private final static Logger logger = Logger.getLogger(TradeColdDataService.class);
    private final static int PAGE_QUERY_SID_NUM = 10000;

    @Resource
    ITradeFilterService filterService;
    @Resource
    ITradeFillService tradeFillService;
    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;
    @Resource
    FxgTradeDecryptBusiness fxgTradeDecryptBusiness;
    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;
    @Resource
    TradeColdDataQueryApi tradeColdDataQueryApi;
    @Resource
    TradeOpenUidTransformBusiness tradeOpenUidTransformBusiness;
    @Resource
    SecretBusiness secretBusiness;

    private static  final int MAX_BATCH_SIZE = 2000;
    public  static  final String EXPORT_THREE_MONTH_AGO  = "exportThreeMonthAgo";


    /**
     *
     * @param staff
     * @param params
     * @param onlyQuery3monthAgo
     * @return
     */
    public Trades search3Month(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo){
        //logger.debug(LogHelper.buildLog(staff, "用冷数据接口查询三个月前订单"));
        Trades trades = new Trades();
        trades.setPage(params.getPage());
        trades.setSort(params.getSort());

        if(!doBeforeSearch(staff,params,onlyQuery3monthAgo)){
            return trades;
        }

        TradeColdQueryParameter tradeColdQueryParameter = ParameterConverts.tradeColdQueryConvert(params);

        PageResponse<TbTrade> pageResponse;
        try {
            // 直接跟sids查询trade性能比tradeColdPage() 方法好很多。
            if(StringUtils.equalsIgnoreCase(EXPORT_THREE_MONTH_AGO,params.getExportSource()) && !CollectionUtils.isEmpty(tradeColdQueryParameter.getSids())){
                // 导出场景使用 (勿添加其他场景)
                pageResponse = new PageResponse<>();
                List<Long> mainSids = tradeColdDataQueryApi.fetchTradeMasterSid(tradeColdQueryParameter.getCompanyId(), tradeColdQueryParameter.getStaffId(), tradeColdQueryParameter.getSids(), null);
                if (CollectionUtils.isEmpty(mainSids)){
                    logger.warn(LogHelper.buildLog(staff, "归档订单导出时，查询主单系统单号为空"));
                    return trades;
                }
                pageResponse.setData(tradeColdDataQueryApi.tradeColdDirectQueryByMasterSids(tradeColdQueryParameter.getCompanyId(),tradeColdQueryParameter.getStaffId(),null, new HashSet<>(mainSids), tradeColdQueryParameter.getSysStatus()));
            }else if (Objects.equals(0,params.getDirectQueryType()) && !CollectionUtils.isEmpty(tradeColdQueryParameter.getSids())){
                // 按SID点查场景
                pageResponse = new PageResponse<>();
                pageResponse.setData(tradeColdDataQueryApi.tradeColdDirectQueryBySids(tradeColdQueryParameter.getCompanyId(),tradeColdQueryParameter.getStaffId(),null,tradeColdQueryParameter.getSids(),tradeColdQueryParameter.getSysStatus()));
            } else if(Objects.equals(1,params.getDirectQueryType()) && !CollectionUtils.isEmpty(tradeColdQueryParameter.getTids())){
                // 按TID点查场景
                pageResponse = new PageResponse<>();
                pageResponse.setData(tradeColdDataQueryApi.tradeColdDirectQueryByTids(tradeColdQueryParameter.getCompanyId(),tradeColdQueryParameter.getStaffId(),null,tradeColdQueryParameter.getTids(),tradeColdQueryParameter.getSysStatus()));
            }else {
                // 普通搜索场景
                pageResponse = tradeColdDataQueryApi.tradeColdPage(tradeColdQueryParameter);
            }
        } catch (IllegalArgumentException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单查询出错,参数错误"), e);
            throw new TradeException("参数错误，"+e.getMessage());
        } catch (ThrottlingException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单查询出错,限流"), e);
            throw new ThrottlingException("冷数据接口限流，"+e.getMessage());
        }  catch (RpcException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单查询出错,RpcException"), e);
            throw new RpcException("冷数据接口RpcException，"+e.getMessage());
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "三个月前订单查询出错"), e);
            throw new TradeException("糟糕，订单查询出错了。。。");
        }

        List<Trade> tradeList;
        if(CollectionUtils.isEmpty(pageResponse.getData())){
           return  trades;
        }
        tradeList = assembleOrders(staff, pageResponse.getData(),params.getQueryOrder());
        trades.setList(tradeList);
        fillSource(tradeList);
        tradeFillService.fill(staff, tradeList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(trades.getList()) && params.getSysMask() == 1) {
            TradeSysDigestUtils.batchSensitive(staff,trades.getList());
        }
        try {
            filterService.filterTrades(staff, tradeList);
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤处理失败"), e);
        }
        fxgTradeDecryptBusiness.batchSensitive(staff, tradeList);
        commonTradeDecryptBusiness.batchSensitive(staff, tradeList);
        TradeSysConsignUtils.handleSysConsigned(tradeList);
        trades.setList(tradeList);
        Page page = params.getPage();
        if (page.getPageNo() > 1) {
            trades.setTotal(tradeList.size() < page.getPageSize() ? (page.getPageNo() - 1) * page.getPageSize() + (long) tradeList.size() : 2000L);
        } else {
            trades.setTotal(tradeList.size() < params.getPage().getPageSize() ? (long) tradeList.size() : 2000L);
        }

        doAfterSearch(staff,params,onlyQuery3monthAgo);

        return trades;
    }

    /**
     * 冷库接口查询sid列表
     * TradeColdQueryParameter.onlyQueryMasterTrade  false:包含单， true：不包含主单
     * @param staff
     * @param params
     * @param onlyQuery3monthAgo
     * @return
     */
    public List<Long> queryTradeSidList(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo){
        //logger.debug(LogHelper.buildLog(staff, "用冷数据接口查询三个月前订单(仅Trade主单系统单号)"));
        TradeDataService.handleQueryParams(params);
        doBeforeSearch(staff,params,onlyQuery3monthAgo);
        Page page = params.getPage();
        if (null == page){
            page = new Page(1, PAGE_QUERY_SID_NUM);
        }
        int pageSize = page.getPageSize();
        int index = pageSize / PAGE_QUERY_SID_NUM;
        int last = pageSize % PAGE_QUERY_SID_NUM;
        List<Long> result = new ArrayList<>();
        params.setPage(page.setPageSize(PAGE_QUERY_SID_NUM));
        TradeColdQueryParameter tradeColdQueryParameter = ParameterConverts.tradeColdQueryConvert(params);
        //归档导出的请求，导出标识设置为true
        if (StringUtils.equalsIgnoreCase(EXPORT_THREE_MONTH_AGO,params.getExportSource())){
            tradeColdQueryParameter.setExport(true);
            tradeColdQueryParameter.setOnlyQueryMasterTrade(false);
            tradeColdQueryParameter.setUniqueId(Math.abs(UUID.randomUUID().getLeastSignificantBits()));
        }
        try {
            PageResponse<Long> pageResponse;
            boolean stop = false;
            for (int i = 1; i <= index; i++){
                tradeColdQueryParameter.setPageNo(i);
                tradeColdQueryParameter.setPageSize(PAGE_QUERY_SID_NUM);
                pageResponse = tradeColdDataQueryApi.tradeColdIndexQuerySid(tradeColdQueryParameter);
                if (pageResponse != null && !CollectionUtils.isEmpty(pageResponse.getData())){
                    result.addAll(pageResponse.getData());
                }else {
                    stop = true;
                    break;
                }
            }
            if (last > 0 && !stop){
                tradeColdQueryParameter.setPageNo(index + 1);
                tradeColdQueryParameter.setPageSize(last);
                pageResponse = tradeColdDataQueryApi.tradeColdIndexQuerySid(tradeColdQueryParameter);
                if (pageResponse != null && !CollectionUtils.isEmpty(pageResponse.getData())){
                    result.addAll(pageResponse.getData());
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "三个月前订单查询(仅主单系统单号)出错"), e);
            throw new TradeException("糟糕，订单查询出错了。。。");
        }
        return result;
    }

    /**
     * trade表冷数据汇总
     *
     * @param params 查询参数
     * @return 由交易定义的TbTrade
     */
    public Long tradeColdTotal(Staff staff,TradeQueryParams params){
        params.setCompanyId(staff.getCompanyId());
        params.setStaffId(staff.getId());
        TradeColdQueryParameter queryParameter = ParameterConverts.tradeColdQueryConvert(params);
        return  tradeColdDataQueryApi.tradeColdTotal(queryParameter);
    }

    /**
     * trade表冷数据-按平台订单ID查询
     * 如果合单则会把子单都查出来
     * 如果未合单则把当前tid的trade查出来。
     *
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param tids         平台订单ID集合
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdQueryIncludeChildByTids(Long companyId, Long staffId, Set<String> queryColumns, Set<String> tids){
        return tradeColdDataQueryApi.tradeColdQueryIncludeChildByTids(companyId,staffId,queryColumns,tids);
    }

    /**
     * trade表冷数据-按系统订单ID查询
     * 如果合单则会把子单都查出来
     * 如果未合单则把当前sid的trade查出来。
     *
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param sids         系统订单ID集合
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdQueryIncludeChildBySids(Long companyId, Long staffId, Set<String> queryColumns, Set<Long> sids){
        return  tradeColdDataQueryApi.tradeColdQueryIncludeChildBySids(companyId,staffId,queryColumns,sids);
    }


    /**
     * trade表冷数据-按平台订单ID查询
     * 仅查询主单
     *
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param tids         平台订单ID集合
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdQueryOnlyMasterTradeByTids(Long companyId, Long staffId, Set<String> queryColumns, Set<String> tids){
        return tradeColdDataQueryApi.tradeColdQueryOnlyMasterTradeByTids(companyId,staffId,queryColumns,tids);
    }

    /**
     * trade表冷数据-按系统订单ID查询
     * 仅查询主单
     *
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param sids         系统订单ID集合
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdQueryOnlyMasterTradeBySids(Long companyId, Long staffId, Set<String> queryColumns, Set<Long> sids){
        return tradeColdDataQueryApi.tradeColdQueryOnlyMasterTradeBySids(companyId,staffId,queryColumns,sids);
    }

    /**
     * trade表冷数据直连查询-按平台订单ID查询
     * 直接通过Lindorm查询数据，按TID查询，
     * 与{@link TradeColdDataQueryApi#tradeColdQueryIncludeChildByTids(Long, Long, Set, Set)}的区别在于tradeColdDirectQueryByTids是按照tid查询，而tradeColdQueryIncludeChildByTids是按照主单+子单查询。
     *
     * @param staff 员工信息
     * @param queryColumns 查询列（数据表列名）
     * @param tids         平台订单ID集合 批量限制2000.
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdDirectQueryByTids(Staff staff, Set<String> queryColumns, Set<String> tids) {
        try {
            return tradeColdDataQueryApi.tradeColdDirectQueryByTids(staff.getCompanyId(), staff.getId(), queryColumns, tids);
        } catch (IllegalArgumentException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,参数错误"), e);
            throw new TradeException("参数错误，"+e.getMessage());
        } catch (ThrottlingException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,限流"), e);
            throw new ThrottlingException("冷数据接口限流，"+e.getMessage());
        }  catch (RpcException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,RpcException"), e);
            throw new RpcException("冷数据接口RpcException，"+e.getMessage());
        } catch (Exception e) {
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错"), e);
            throw new TradeException("糟糕，订单查询出错了。。。");
        }

    }


    /**
     *
     * @param staff
     * @param queryColumns
     * @param tids
     * @param fuzzyMatchingTid  是否模糊匹配   精准TID匹配传false
     * @return
     */
    List<TbTrade> tradeColdDirectQueryByTids(Staff staff, Set<String> queryColumns, Set<String> tids, boolean fuzzyMatchingTid) {
        try {
            return tradeColdDataQueryApi.tradeColdDirectQueryByTids(staff.getCompanyId(), staff.getId(), queryColumns, tids, null, fuzzyMatchingTid);
        } catch (IllegalArgumentException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,参数错误"), e);
            throw new TradeException("参数错误，"+e.getMessage());
        } catch (ThrottlingException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,限流"), e);
            throw new ThrottlingException("冷数据接口限流，"+e.getMessage());
        }  catch (RpcException e){
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错,RpcException"), e);
            throw new RpcException("冷数据接口RpcException，"+e.getMessage());
        } catch (Exception e) {
            logger.warn(LogHelper.buildLog(staff, "三个月前订单是否存在出错"), e);
            throw new TradeException("糟糕，订单查询出错了。。。");
        }

    }

    /**
     * 只查询trade表冷数据SID，不查询Lindorm数据。
     * 由于不用回表查询Lindorm，pageSize上限放开至2000.
     * 由于包含了合单以及其子单，所以最终返回会大于2000
     * @param params 查询参数
     * @return 包含合单的子单的SID
     */
    PageResponse<Long> tradeColdIndexQuerySid(Staff staff,TradeQueryParams params){
        params.setCompanyId(staff.getCompanyId());
        params.setStaffId(staff.getId());
        TradeColdQueryParameter queryParameter = ParameterConverts.tradeColdQueryConvert(params);
        return  tradeColdDataQueryApi.tradeColdIndexQuerySid(queryParameter);
    }

    /**
     * trade表冷数据直连查询-按系统订单ID查询
     * 直接通过Lindorm查询数据，按SID查询，
     * 与{@link TradeColdDataQueryApi#tradeColdQueryIncludeChildByTids(Long, Long, Set, Set)}
     *  的区别在于tradeColdDirectQueryBySids是按照sid查询，而tradeColdQueryIncludeChildByTids是按照主单SID查询。
     * @param companyId 公司ID
     * @param staffId 员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param sids 系统订单ID集合 批量限制500.
     * @return 由交易定义的TbTrade
     */
    List<TbTrade> tradeColdDirectQueryBySids(Long companyId, Long staffId, Set<String> queryColumns, Set<Long> sids){
        return tradeColdDataQueryApi.tradeColdDirectQueryBySids(companyId,staffId,queryColumns,sids);
    }


    /**
     * order表冷数据直连查询-按系统订单ID查询
     * 直接通过Lindorm查询数据，按SID查询，
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param sids         系统订单ID集合 批量限制500.
     * @return 由交易定义的TbOrder
     */
    List<TbOrder> orderColdDirectQueryBySids(Long companyId, Long staffId, Set<String> queryColumns, Set<Long> sids){
        if(CollectionUtils.isEmpty(sids)){
            return Lists.newArrayList();
        }
        if(sids.size()<=MAX_BATCH_SIZE){
            return tradeColdDataQueryApi.orderColdDirectQueryBySids(companyId,staffId,queryColumns,sids);
        }
        List<TbOrder> tbOrders = Lists.newArrayListWithExpectedSize(sids.size());
        Lists.partition(Lists.newArrayList(sids),MAX_BATCH_SIZE).forEach(listPart->{
            tbOrders.addAll(tradeColdDataQueryApi.orderColdDirectQueryBySids(companyId,staffId,queryColumns,Sets.newHashSet(listPart)));
        });
        return tbOrders;
    }


    /**
     * order表冷数据直连查询-按系统订单主单ID查询
     * 直接通过Lindorm查询数据，按SID查询，
     * @param companyId    公司ID
     * @param staffId      员工ID
     * @param queryColumns 查询列（数据表列名）
     * @param belongSids   系统订单主单ID集合 批量限制500.
     * @return 由交易定义的TbOrder
     */
    List<TbOrder> orderColdDirectQueryByBelongSids(Long companyId, Long staffId, Set<String> queryColumns, Set<Long> belongSids){
        return tradeColdDataQueryApi.orderColdDirectQueryByBelongSids(companyId,staffId,queryColumns,belongSids);
    }


    private boolean doBeforeSearch(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        params.setCompanyId(staff.getCompanyId());
        params.setStaffId(staff.getId());

        if(!checkParams(staff,params,onlyQuery3monthAgo)){
            return false;
        }
        processBuyerNickQuery(staff,params);
        return true;
    }


    private void doAfterSearch(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {

    }

    private void fillSource(List<Trade> tradeList) {
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        for (Trade trade : tradeList) {
            if (StringUtils.equals(trade.getSubSource(), CommonConstants.PLAT_FORM_TYPE_SYS)){
                trade.setSource(CommonConstants.PLAT_FORM_TYPE_SYS);
                trade.setSubSource(null);
            }
        }
    }



    private List<Trade> assembleOrders(Staff staff, List<TbTrade> trades,Boolean queryOrder) {
        Set<Long> mergeSids = new HashSet<>();
        Set<Long> sids = new HashSet<>(trades.size() + 2);
        for (Trade trade : trades) {
            sids.add(trade.getSid());
            if (trade.getMergeSid() > 0) {
                mergeSids.add(trade.getMergeSid());
            }
        }
        Map<Long, List<TbTrade>> mergeTradesMap = new HashMap<>(mergeSids.size(), 1);
        if (!mergeSids.isEmpty()) {
            List<TbTrade> mergeTrades = tradeColdDataQueryApi.tradeColdQueryIncludeChildBySids(staff.getCompanyId(),staff.getId(),null,mergeSids);
            for (TbTrade trade : mergeTrades) {
                sids.add(trade.getSid());
                List<TbTrade> tradeList = mergeTradesMap.get(trade.getMergeSid());
                if (tradeList == null) {
                    tradeList = new ArrayList<>();
                    mergeTradesMap.put(trade.getMergeSid(), tradeList);
                }
                tradeList.add(trade);
            }
        }
        List<TbOrder> orders = Lists.newArrayList();
        if(queryOrder == null || queryOrder){
            orders = queryOrders(staff, sids);
        }
        return assembleMergeTrades(staff,trades, mergeTradesMap, orders);
    }

    private List<Trade> assembleMergeTrades(Staff staff,List<TbTrade> trades, Map<Long, List<TbTrade>> mergeTradesMap, List<TbOrder> orders) {
        int oldSize = trades.size();
        for (List<TbTrade> list : mergeTradesMap.values()) {
            trades.addAll(list);
        }
        TradeUtils.assemblyBySid(trades, orders);
        List<Trade> result = new ArrayList<>();
        Set<Long> usedMergeSids = new HashSet<>();
        for (int i = 0; i < oldSize; i++) {
            Trade trade = trades.get(i);
            if (trade.getMergeSid() > 0) {
                if (usedMergeSids.add(trade.getMergeSid())) {
                    List<TbTrade> mergeTrades = mergeTradesMap.get(trade.getMergeSid());
                    Trade main = tradeAssembleBusiness.getMainTrade(staff, mergeTrades, false);
                    if (main != null) {
                        result.add(main);
                    }
                }
            } else {
                result.add(trade);
            }
        }
        return result;
    }

    private List<TbOrder> queryOrders(Staff staff, Set<Long> sids) {
        return  this.orderColdDirectQueryBySids(staff.getCompanyId(),staff.getId(),null,sids);
    }

    /**
     * 只查询三个月以前的订单
     * @param params
     * @param onlyQuery3monthAgo
     */
    private boolean checkParams(Staff staff,TradeQueryParams params, boolean onlyQuery3monthAgo){
        Date endTime = params.getEndTime();
        if (onlyQuery3monthAgo && !params.getContext().isUniqueQuery()) {
            Date startTime = params.getStartTime();
            if (startTime == null || startTime.before(TradeTimeUtils.INIT_DATE)) {
                params.setStartTime(TradeTimeUtils.INIT_DATE);
            }
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, -3);
            if (endTime != null) {
                if (endTime.after(c.getTime())) {
                    endTime = c.getTime();
                }
            } else {
                endTime = c.getTime();
            }
            params.setEndTime(endTime);
        }

        Date startTime = params.getStartTime();
        endTime = params.getEndTime();
        if (startTime!=null && endTime!=null){
            if (startTime.equals(endTime) || startTime.after(endTime)){
                return false;
            }
        }
        return true;
    }

    /**
     * 处理 buyerNick
     *
     * @param staff
     * @param params
     */
    private void processBuyerNickQuery(Staff staff, TradeQueryParams params) {
        String[] buyerNick = params.getBuyerNick();
        if(ArrayUtils.isEmpty(buyerNick)){
            return;
        }

        //客户订单并且使用呢称搜索，昵称已经是传入的明文 不去调用平台的转换openUid的接口 防止对平台接口的大量无效调用
        if (org.apache.commons.lang.StringUtils.equals(org.apache.commons.lang.StringUtils.trimToEmpty(params.getTradeType()),"21")) {
            return;
        }
        List<User> users = staff.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        Long[] userIds = params.getUserIds();
        boolean containsUserId = userIds == null || userIds.length == 0;
        List<String> buyerNickList = Arrays.asList(buyerNick);
        List<String> list = tradeOpenUidTransformBusiness.loginIds2OpenUid(
                users.stream().filter(u -> containsUserId || Arrays.asList(params.getUserIds()).contains(u.getId())).collect(toList()),
                buyerNickList);
        if(CollectionUtils.isEmpty(list)){
            list.addAll(secretBusiness.batchEncodeStrAndAdd(staff,buyerNickList));
            params.setBuyerNick(list.toArray(new String[0]));
        }
        list.addAll(buyerNickList);
        list.addAll(secretBusiness.batchEncodeStrAndAdd(staff,list));
        params.setBuyerNick(list.toArray(new String[0]));
    }
}

package com.raycloud.dmj.services.trades.support.search.convert;

import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;
import com.raycloud.dmj.services.trades.support.search.convert.order.IOrderConditionConverter;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2025-02-21
 */
@Component
public class ConditionConvertService{

    @Autowired
    List<IConditionConverter> conditionConverters;

    private final Logger logger = Logger.getLogger(this.getClass());


    public  void convert(Staff staff, ConvertContext context, TradeQueryRequest condition, Query q) {

        DevLogBuilder timer = DevLogBuilder.forDev(staff, LogBusinessEnum.QUERY.getSign()).append("trade过滤sql构建 耗时统计").setBaseTooklmt(100L).startTimer();

        for (IConditionConverter converter : conditionConverters) {
            converter.convert(staff, context, condition, q);
            if (q.isStopQuery()) {
                return;
            }
            timer.recordTimer(converter.getClass().getSimpleName());
        }
        timer.startWatch().appendTook(timer.curEnabled()?1L:1000L).printDebug(logger);
    }
}

package com.raycloud.dmj.services.trades.support;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.services.cache.CacheContainer;
import com.raycloud.dmj.services.trade.progress.IProgressSpeedService;
import com.raycloud.dmj.services.trade.progress.ProgressCacheBusiness;
import com.raycloud.dmj.services.trade.progress.ProgressWatchBusiness;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * creatd 18/5/7
 *
 * <AUTHOR>
 */
@Service
public class ProgressService implements IProgressService {

    @Resource
    ProgressCacheBusiness progressCacheBusiness;

    @Resource
    CacheContainer cacheContainer;

    @Resource
    IEventCenter eventCenter;

    @Resource
    IProgressSpeedService progressSpeedService;

    /**
     * 默认的缓存时间 10*60 秒
     */
    private static final int DEFAULT_EXPIRE = 20 * 60;

    /**
     * 默认的任务完成后缓存时间 10 秒
     */
    private static final int DEFAULT_EXPIRE_COMPLETE = 10;

    @Override
    public void setProgress(Staff staff, ProgressEnum progressEnum, ProgressData progressData) {
        String progressKey = getProgressKey(staff, progressEnum);
        setProgress(staff, progressKey, progressData);
    }

    @Override
    public void setProgress(Staff staff, String cacheKey, ProgressData progressData) {
        progressData.setCacheKey(cacheKey);
        int timeExpired;
        if (Objects.nonNull(progressData.getProgress()) && 2 == progressData.getProgress()) {
            timeExpired = DEFAULT_EXPIRE_COMPLETE;
            progressData.setEnd(System.currentTimeMillis());
        } else {
            timeExpired = DEFAULT_EXPIRE;
        }
        progressCacheBusiness.set(staff, cacheKey, progressData, timeExpired);
    }

    @Override
    public void setProgress(Staff staff, ProgressEnum progressEnum, Integer countAll) {
        setProgress(staff, progressEnum, countAll, 1);
    }

    @Override
    public void setProgress(Staff staff, ProgressEnum progressEnum, Integer countAll, Integer progress) {
        ProgressData progressData = new ProgressData();
        progressData.setCountAll(countAll);
        progressData.setProgress(progress);
        setProgress(staff, progressEnum, progressData);
    }

    @Override
    public void updateProgress(Staff staff, ProgressEnum progressEnum, ProgressData progressData) {
        setProgress(staff, progressEnum, progressData);
    }

    @Override
    public void updateProgress(Staff staff, String cacheKey, ProgressData progressData) {
        setProgress(staff, cacheKey, progressData);
    }

    @Override
    public void updateProgress(Staff staff, ProgressEnum progressEnum, Integer countCurrent) {
        updateProgress(staff, progressEnum, countCurrent, 1);
    }

    @Override
    public void updateProgress(Staff staff, ProgressEnum progressEnum, Integer countCurrent, Integer progress) {
        ProgressData progressData = getOrCreate(staff, progressEnum);
        progressData.setCountCurrent(countCurrent);
        progressData.setProgress(progress);
        updateProgress(staff, progressEnum, progressData);
    }

    @Override
    public void updateProgressComplete(Staff staff, ProgressEnum progressEnum, ProgressData progressData) {
        if (progressData == null)  return;
        cacheContainer.unregister(getProgressKey(staff, progressEnum));
        progressData.setProgress(2);
        progressData.setCountCurrent(progressData.getCountAll());
        updateProgress(staff, progressEnum, progressData);
        eventCenter.fireEvent(this, new EventInfo(ProgressWatchBusiness.PROGRESS_WATCH_EVENT).setArgs(new Object[]{staff, progressEnum, progressData}), null);
    }

    @Override
    public void updateProgressComplete(Staff staff, String progressKey, ProgressData progressData) {
        if (progressData == null)  return;
        cacheContainer.unregister(progressKey);
        progressData.setProgress(2);
        progressData.setCountCurrent(progressData.getCountAll());
        updateProgress(staff, progressKey, progressData);
    }

    @Override
    public void clearProgress(Staff staff, ProgressEnum progressEnum) {
        cacheContainer.unregister(getProgressKey(staff, progressEnum));
        progressSpeedService.clearProgressSpeed(staff, progressEnum);
        progressCacheBusiness.delete(staff, getProgressKey(staff, progressEnum));
    }

    @Override
    public void clearProgress(Staff staff, String progressKey) {
        cacheContainer.unregister(progressKey);
        progressSpeedService.clearProgressSpeed(staff, progressKey);
        progressCacheBusiness.delete(staff, progressKey);
    }

    @Override
    public void autoClearProgress(Staff staff, String progressKey) {
        ProgressData progressData = progressCacheBusiness.get(staff, progressKey);
        if (progressData != null && progressData.finish(200)) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("自动清理进度条，progressKey=%s, countAll=%s, start=%s", progressKey, progressData.getCountAll(), progressData.getStart())));
            progressCacheBusiness.delete(staff, progressKey);
        }
    }

    @Override
    public ProgressData getOrCreate(Staff staff, ProgressEnum progressEnum) {
        return getOrCreate(staff, getProgressKey(staff, progressEnum));
    }

    @Override
    public ProgressData getOrCreate(Staff staff, String progressKey) {
        ProgressData progressData = progressCacheBusiness.get(staff, progressKey);
        if (progressData == null) {
            progressData = new ProgressData();
            setProgress(staff, progressKey, progressData);
        }
        return progressData;
    }

    @Override
    public ProgressData queryProgress(Staff staff, ProgressEnum progressEnum) {
        return progressCacheBusiness.get(staff, getProgressKey(staff, progressEnum));
    }

    @Override
    public ProgressData queryProgress(Staff staff, String key) {
        return progressCacheBusiness.get(staff, key);
    }

    @Override
    public boolean hasProgress(Staff staff, ProgressEnum progressEnum) {
        ProgressData progressData = progressCacheBusiness.get(staff, getProgressKey(staff, progressEnum));
        return progressData != null && progressData.getProgress() != 2;
    }

    @Override
    public String getProgressKey(Staff staff, ProgressEnum progressEnum) {
        /**
         * 以下业务使用员工维度进度条：
         * 1. 波次打印获取单号
         * 2. 播种拆分
         * 3. 波次直接发货
         * 4. 直接发货
         * 5. 批次有效期保存
         * 模版同步*
         */
        if (Objects.equals(ProgressEnum.PROGRESS_TRADE_WAVE_WAYBILL_GET_REAL_TIME, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_WAVE_SPLIT_TRADE, progressEnum)
                || Objects.equals(ProgressEnum.HOT_SALE_TRADE_WAYBILL_GET, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_WAVE_CONSIGN_DIRECT, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_DIRECT_CONSIGN, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_ORDER_PRODUCT, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD, progressEnum)
                || Objects.equals(ProgressEnum.PROGRESS_WAVE_BATCH_PACK, progressEnum)
                || (Objects.nonNull(progressEnum.getNeedStaffId()) && progressEnum.getNeedStaffId())) {
            return progressEnum.getKey() + "_" + staff.getCompanyId() + "_" + staff.getId();
        }
        return progressEnum.getKey() + "_" + staff.getCompanyId();
    }

    @Override
    public String getProgressKey(Staff staff, ProgressEnum progressEnum, boolean realTime) {
        if (!realTime) {
            return getProgressKey(staff, progressEnum);
        }
        return progressEnum.getKey() + "_" + staff.getCompanyId() + "_" + System.currentTimeMillis();
    }

    @Override
    public boolean addProgress(Staff staff, ProgressEnum progressEnum) {
        //这里默认设置一个总数为1，刚开始的进度为 0%
        cacheContainer.register(getProgressKey(staff, progressEnum));
        return addProgress(staff, progressEnum, new ProgressData().setStart(System.currentTimeMillis()).setCountAll(1).setProgress(1).setClueId(ClueIdUtil.getClueId()));
    }

    @Override
    public boolean addProgress(Staff staff, String progressKey) {
        //这里默认设置一个总数为1，刚开始的进度为 0%
        if (addProgress(staff, progressKey, new ProgressData().setStart(System.currentTimeMillis()).setCountAll(1).setProgress(1).setClueId(ClueIdUtil.getClueId()))) {
            cacheContainer.register(progressKey);
            return true;
        }
        return false;
    }

    @Override
    public boolean addProgress(Staff staff, ProgressEnum progressEnum, ProgressData progressData) {
        if (addProgress0(staff, getProgressKey(staff, progressEnum), progressData)) {
            progressSpeedService.addProgressSpeed(staff, progressEnum);
            return true;
        }
        return false;
    }

    @Override
    public boolean addProgress(Staff staff, String progressKey, ProgressData progressData) {
        if (addProgress0(staff, progressKey, progressData)) {
            progressSpeedService.addProgressSpeed(staff, progressKey);
            return true;
        }
        return false;
    }

    private boolean addProgress0(Staff staff, String progressKey, ProgressData progressData) {
        progressData.setCacheKey(progressKey);
        boolean result = progressCacheBusiness.add(staff, progressKey, progressData, DEFAULT_EXPIRE);
        if (!result) {
            ProgressData existProgressData = progressCacheBusiness.get(staff, progressKey);
            if (existProgressData != null && existProgressData.getProgress() != null && existProgressData.getProgress() == 2) {
                progressCacheBusiness.delete(staff, progressKey);
                result = progressCacheBusiness.add(staff, progressKey, progressData, DEFAULT_EXPIRE);
            }
        }
        return result;
    }

    @Override
    public void touchProgress(Staff staff, ProgressEnum progressEnum, ProgressData progressData, int inc) {
        if (progressData == null) {
            return;
        }
        progressData.setCountAll(progressData.getCountAll() + inc);
        progressData.setCountCurrent(progressData.getCountCurrent() + inc);
        updateProgress(staff, progressEnum, progressData);
    }
}

package com.raycloud.dmj.services.trades.support.search.scene;

import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradeFillEnum;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import org.springframework.stereotype.Component;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-12-17
 */
@Component
public class BatchPrintQueryer extends TradeAssembleStrategy{

    @Override
    public SenceCodeEnum supportScene() {
        return SenceCodeEnum.BATCH_PRINT;
    }

    @Override
    TradeAssembleParams buildTradeAssembleParams(SceneLightQueryContext context, Long... sids) {
        return TradeAssembleParams.justTrade()
                .setQueryMerge(true).setMergeStyle(TradeAssembleParams.MERGE_STYLE_HIDDEN_SUB)
                .setFill(true)
                .addIncludeFills(TradeFillEnum.ORDER_EXCEPT_FILL)
                .addIncludeFills(TradeFillEnum.TRADE_EXCEPT_FILL)
                .addIncludeFills(TradeFillEnum.TRADE_TAG_FILL);
    }
}

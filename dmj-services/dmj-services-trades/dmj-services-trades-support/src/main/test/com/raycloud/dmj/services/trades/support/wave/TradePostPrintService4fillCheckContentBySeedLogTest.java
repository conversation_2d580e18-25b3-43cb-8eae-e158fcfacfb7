package com.raycloud.dmj.services.trades.support.wave;

import com.raycloud.dmj.dao.wave.WaveSeedLogDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.wave.WaveSeedLog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * @see TradePostPrintService#fillCheckContentBySeedLog(com.raycloud.dmj.domain.account.Staff, com.raycloud.dmj.domain.trades.Trade)
 * <AUTHOR> xingquanxiang
 * @Date : 2022/2/28 14:46
 */
@RunWith(MockitoJUnitRunner.class)
public class TradePostPrintService4fillCheckContentBySeedLogTest {
    @InjectMocks
    TradePostPrintService tradePostPrintService = new TradePostPrintService();

    @Mock
    Staff staff;

    @Mock
    WaveSeedLogDao waveSeedLogDao;

    /**
     * 测试播种打印
     */
    @Test
    public void testFillCheckContentBySeedLog() {
        Trade trade = new Trade();
        trade.setSid(1L);
        trade.setWaveId(1L);

        List<WaveSeedLog> waveSeedLogs = new ArrayList<>();
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲"));
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲1"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, "六曲"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, "六曲"));
        doReturn(waveSeedLogs).when(waveSeedLogDao).queryWaveSeedLogWithSortingDetailList(any(), anyList(), any());

        tradePostPrintService.fillCheckContentBySeedLog(staff, trade);

        Assert.assertTrue("质检登记信息有误", "【质检登记】商品：A4*2，质检员：六曲1*1、六曲*1；商品：A5*2，质检员：六曲*2；".equals(trade.getCheckContent()));
    }

    /**
     * 测试播种打印
     */
    @Test
    public void testFillCheckContentBySeedLog1() {
        Trade trade = new Trade();
        trade.setSid(1L);
        trade.setWaveId(1L);

        List<WaveSeedLog> waveSeedLogs = new ArrayList<>();
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲"));
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲1"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, null));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, "六曲"));
        doReturn(waveSeedLogs).when(waveSeedLogDao).queryWaveSeedLogWithSortingDetailList(any(), anyList(), any());

        tradePostPrintService.fillCheckContentBySeedLog(staff, trade);

        Assert.assertTrue("质检登记信息有误", "【质检登记】商品：A4*2，质检员：六曲1*1、六曲*1；商品：A5*2，质检员：六曲*1；".equals(trade.getCheckContent()));
    }


    /**
     * 正常情况
     */
    @Test
    public void testFillCheckContentBySeedLog2() {
        Trade trade = new Trade();
        trade.setSid(1L);
        trade.setWaveId(1L);

        List<WaveSeedLog> waveSeedLogs = new ArrayList<>();
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲"));
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲1"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 2, 1L, 12L, "骑手"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 2, 1L, 12L, "骑手"));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, "六曲"));
        doReturn(waveSeedLogs).when(waveSeedLogDao).queryWaveSeedLogWithSortingDetailList(any(), anyList(), any());

        tradePostPrintService.fillCheckContentBySeedLog(staff, trade);

        Assert.assertTrue("质检登记信息有误", "【质检登记】商品：A4*2，质检员：六曲1*1、六曲*1；商品：A5*5，质检员：骑手*4、六曲*1；".equals(trade.getCheckContent()));
    }

    /**
     * 边界，全部记录质检员为空
     */
    @Test
    public void testFillCheckContentBySeedLog3() {
        Trade trade = new Trade();
        trade.setSid(1L);
        trade.setWaveId(1L);

        List<WaveSeedLog> waveSeedLogs = new ArrayList<>();
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, null));
        waveSeedLogs.add(buildWaveSeedLog("A5", 1, 1L, 12L, null));
        doReturn(waveSeedLogs).when(waveSeedLogDao).queryWaveSeedLogWithSortingDetailList(any(), anyList(), any());

        tradePostPrintService.fillCheckContentBySeedLog(staff, trade);

        Assert.assertTrue("质检登记信息有误", trade.getCheckContent() == null);
    }

    /**
     * 边界，部分记录质检员为空
     */
    @Test
    public void testFillCheckContentBySeedLog4() {
        Trade trade = new Trade();
        trade.setSid(1L);
        trade.setWaveId(1L);

        List<WaveSeedLog> waveSeedLogs = new ArrayList<>();
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, "六曲"));
        waveSeedLogs.add(buildWaveSeedLog("A4", 1, 1L, 11L, null));
        waveSeedLogs.add(buildWaveSeedLog("A5", 2, 1L, 12L, null));
        doReturn(waveSeedLogs).when(waveSeedLogDao).queryWaveSeedLogWithSortingDetailList(any(), anyList(), any());

        tradePostPrintService.fillCheckContentBySeedLog(staff, trade);

        Assert.assertTrue("质检登记信息有误", "【质检登记】商品：A4*2，质检员：六曲*1；".equals(trade.getCheckContent()));
    }



    WaveSeedLog buildWaveSeedLog(String outerId, Integer itemNum, Long sid, Long orderId, String checker) {
        WaveSeedLog waveSeedLog = new WaveSeedLog();
        waveSeedLog.setOuterId(outerId);
        waveSeedLog.setItemNum(itemNum);
        waveSeedLog.setSid(1L);
        waveSeedLog.setOrderId(orderId);
        waveSeedLog.setChecker(checker);
        return waveSeedLog;
    }
}

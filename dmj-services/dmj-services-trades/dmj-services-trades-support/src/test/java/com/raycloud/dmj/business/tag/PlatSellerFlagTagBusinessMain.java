package com.raycloud.dmj.business.tag;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.platform.trades.PlatSellerFlagTag;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.PlatformTradeMemoUpdateBusiness;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.tb.trade.business.TbTradeMemoUpdateBusiness;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class PlatSellerFlagTagBusinessMain {

    static PlatSellerFlagTagBusiness business = new PlatSellerFlagTagBusiness();
    static PlatformManagement platformManagement = Mockito.mock(PlatformManagement.class);
    static TbTradeMemoUpdateBusiness tbTradeMemoUpdateBusiness = new TbTradeMemoUpdateBusiness();
    static Shop shop = Mockito.mock(Shop.class);
    static {
        business.platformManagement = platformManagement;
        business.cache = Mockito.mock(ICache.class);
        shop.setTitle("test");
        Mockito.when(platformManagement.isContainAccess("tb", PlatformTradeMemoUpdateBusiness.class)).thenReturn(true);
        tbTradeMemoUpdateBusiness.cache = Mockito.mock(ICache.class);
        tbTradeMemoUpdateBusiness.shopService = Mockito.mock(IShopService.class);
        Mockito.when(shop.getTitle()).thenReturn(new Random().nextInt(10) + "_user");
        Mockito.when(platformManagement.getAccess("tb", PlatformTradeMemoUpdateBusiness.class)).thenReturn(tbTradeMemoUpdateBusiness);
        Mockito.when(tbTradeMemoUpdateBusiness.shopService.queryByUserId(Mockito.any(), Mockito.any())).thenReturn(shop);

    }

    public static void main(String[] args) throws Exception{
        testQuery();
    }

    public static void testQuery() throws Exception{
        Staff staff = new Staff();
        List<User> users = new ArrayList<>();
        for(int i=0 ;i<50;i++){
            users.add(bulidUser(i));
        }
        staff.setUsers(users);
        Map<Long, List<PlatSellerFlagTag>> longListMap = business.queryPlatSellerFlagTag(staff, null);
        System.out.println(JSONObject.toJSONString(longListMap));
        Thread.sleep(50000);
        System.out.println("睡眠时间到");
        longListMap = business.queryPlatSellerFlagTag(staff, null);
        System.out.println(JSONObject.toJSONString(longListMap));
    }

    public static User bulidUser(long id){
        User user = new User();
        user.setId(id);
        user.setSource("tb");
        user.setSessionId("700021003384d0f399894dbbab23de503b00ade8bc316b5bdd17ef4d99301440e9e8f523984912812");
        return user;
    }
}

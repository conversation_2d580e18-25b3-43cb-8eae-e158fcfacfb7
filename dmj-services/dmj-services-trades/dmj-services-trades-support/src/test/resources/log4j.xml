<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" threshold="debug">

    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d - %c -%-4r [%t] %-5p %x - %m%n"/>
        </layout>
    </appender>
    <!--<appender name="es" class="com.raycloud.log4j.jest.ElasticSearchClientAppender">
         <param name="messageFormatClass" value="com.raycloud.dmj.web.log.Log4jESMessageFormat" />
        <param name="elasticHost" value="http://*************:30001"/>
        <param name="applicationName" value="dev-erp-item" />
        <param name="elasticIndex" value="dev-erp" />
        <param name="forwardInterval" value="1000" />
        <param name="batchSize" value="5" />
        <param name="useClue" value="true" />
    </appender>-->

    <logger name="com.raycloud.dmj.ec" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.ec" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.dmj.services" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.dmj.cache" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.dmj.session" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.dmj.web.controller" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.dmj.web.interceptors" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <!--<logger name="com.raycloud.dmj.web.ErrorHandler" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>-->
    <logger name="com.raycloud.dmj.web.model.wms" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="express" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="sdk.biz.err" additivity="true">
        <level value="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.alibaba.dubbo" additivity="true">
        <level value="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.raycloud.ec.remote.dubbo" additivity="true">
        <level value="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.apache.zookeeper" additivity="true">
        <level value="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="java.sql.PreparedStatement" additivity="true">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <!--<root>-->
        <!--<appender-ref ref="CONSOLE"/>-->
    <!--</root>-->
</log4j:configuration>
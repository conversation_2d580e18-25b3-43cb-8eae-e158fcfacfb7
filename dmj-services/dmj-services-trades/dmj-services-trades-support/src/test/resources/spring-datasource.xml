<?xml version="1.0" encoding="utf-8"?>
<beans default-autowire="byName"
	xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
		http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd">

	<context:annotation-config />
    <context:component-scan base-package="com.raycloud.dmj.dao.trade" >
    </context:component-scan>
    <context:component-scan base-package="com.raycloud.dmj.dao.order" >
    </context:component-scan>
    <!--<context:component-scan base-package="com.raycloud.dmj.dao.pt" >-->
    <!--</context:component-scan>-->
    <!--<context:component-scan base-package="com.raycloud.dmj.dao.gift" >-->
    <!--</context:component-scan>-->

    <!--<context:component-scan base-package="com.raycloud.dmj.dao.wave" >-->
    <!--</context:component-scan>-->

    <tx:annotation-driven transaction-manager="txManager"/>

    <dubbo:application name="junit-erp-trades-1"/>
    <dubbo:registry protocol="zookeeper"  address="zoo4.superboss.cc:30002,zoo5.superboss.cc:30002,zoo6.superboss.cc:30002" />
    <!-- 加密服务 -->
    <dubbo:reference id="secretRequest" interface="com.raycloud.secret_api.api.SecretRequest" />


	<bean id="dataSource"
		class="com.alibaba.druid.pool.RayDruidDataSource"
		init-method="init" destroy-method="close">
		<property name="url" value="${jdbc.master.url}" />
        <property name="username" value="${jdbc.master.username}" />
        <property name="secretRequest" >
            <ref bean="secretRequest" />
        </property>
        <property name="diamondCoord" value="test.local" />
        <property name="driverClassName" value="com.mysql.jdbc.Driver" />  
        <property name="filters" value="stat,config" />
        <property name="maxActive" value="${jdbc.master.maxActive}" />
        <property name="initialSize" value="5" />
        <property name="maxWait" value="${jdbc.master.maxWait}" />
        <property name="minIdle" value="1" />  
        <property name="timeBetweenEvictionRunsMillis" value="3000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
  
        <property name="validationQuery" value="SELECT 'x'" />
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
  
        <property name="poolPreparedStatements" value="false" />
	</bean>
	
	<bean id="jdbcTemplate"
          class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>
	
	<bean id="placeholderConfig"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="location">
			<value>db.properties</value>
		</property>
	</bean>
</beans>
<?xml version="1.0" encoding="utf-8"?>
<beans default-autowire="byName"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
       http://www.springframework.org/schema/util  http://www.springframework.org/schema/util/spring-util-2.5.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd">

    <!--分库信息-->
    <util:set id="shardSet" set-class="java.util.LinkedHashSet">
        <ref local="master"/>
    </util:set>

    <!-- 定义数据分片 -->
    <bean id="master" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master"/>
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 定义总router -->
    <bean id="router" class="com.alibaba.cobarclient.config.SimpleRouterFactoryBean">
        <property name="configLocations">
            <!--路由信息-->
            <list>
                <value>classpath:/dbrule/sharding-rules-on-namespace.xml</value>
            </list>
        </property>
        <property name="shards" ref="shardSet"/>
    </bean>

    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:sqlmap-config.xml"/>
    </bean>

    <!-- 工程里一定要使用此工程模板，不能再使用ibatis原生的api，不然有的情况会不经过cobar的过滤-->
    <bean id="sqlMapClientTemplate" class="com.alibaba.cobarclient.MysdalSqlMapClientTemplate">
        <property name="sqlMapClient" ref="sqlMapClient"/>
        <property name="shards" ref="shardSet"/>
        <property name="router" ref="router"/>
    </bean>

    <!--事务管理-->
    <tx:annotation-driven transaction-manager="txManager" proxy-target-class="true"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="txManager" class="com.alibaba.cobarclient.transaction.BestEffortMultiDataSourceTransactionManager">
        <property name="shards" ref="shardSet"/>
        <!--spring3里要添加下面的参数-->
        <property name="transactionSynchronization" value="2"/>
    </bean>
</beans>

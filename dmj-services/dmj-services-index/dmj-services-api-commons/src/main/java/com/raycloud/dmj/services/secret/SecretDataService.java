package com.raycloud.dmj.services.secret;

import com.google.common.collect.Lists;
import com.raycloud.dmj.services.utils.SecretUtils;
import com.raycloud.secret_api.api.SecretRequest;
import lombok.extern.log4j.Log4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/8 16:36
 */
@Log4j
@Service
public class SecretDataService implements ISecretDataService {

    private static final String UN_CODE_CONTENT_FLAG = new String("unCodeContent");
    private static final int PAGE_SIZE = 100;

    private SecretRequest secretRequest;

    public SecretRequest getSecretRequest() {
        return secretRequest;
    }

    @Autowired
    public void setSecretRequest(SecretRequest secretRequest) {
        this.secretRequest = secretRequest;
    }

    @Override
    public String encodeMD5(String content) {
        return DigestUtils.md5Hex(content);
    }

    @Override
    public String encodeSecret(String content) {
        if (!SecretUtils.needEncode(content)) {
            return content;
        }

        try {
            String encodeContent = secretRequest.encode(content);
            return SecretUtils.addSecretFlag(encodeContent);
        } catch (Exception e) {
            log.error("单个加密错误", e);
        }

        return content;
    }

    @Override
    public String decodeSecret(String content) {
        if (!SecretUtils.isEncodeSecret(content)) {
            return content;
        }

        String encodeContent = SecretUtils.removeSecretFlag(content);
        try {
            return secretRequest.decode(encodeContent);
        } catch (Exception e) {
            log.error("单个解密出错", e);
        }

        return content;
    }

    @Override
    public List<String> batchEncodeSecret(List<String> contentList) {
        if (CollectionUtils.isEmpty(contentList)) {
            return Collections.emptyList();
        }

        // 筛选需要加密的
        int size = contentList.size();
        List<String> encodeSecretList = new ArrayList<>(size);
        List<String> needEncodeContentList = new ArrayList<>(size);
        for (String content : contentList) {
            if (SecretUtils.needEncode(content)) {
                // 占坑
                encodeSecretList.add(UN_CODE_CONTENT_FLAG);
                needEncodeContentList.add(content);
            } else {
                encodeSecretList.add(content);
            }
        }

        // 没有需要加密的
        if (CollectionUtils.isEmpty(needEncodeContentList)) {
            return encodeSecretList;
        }

        try {
            // 加密
            List<String> encodeContentList = partitionCallSecretRequest(needEncodeContentList, true);

            // 填坑
            int encodeContentListIndex = 0;
            for (int i = 0; i < size; i++) {
                if (encodeSecretList.get(i) == UN_CODE_CONTENT_FLAG) {
                    encodeSecretList.set(i, SecretUtils.addSecretFlag(encodeContentList.get(encodeContentListIndex++)));
                }
            }
            return encodeSecretList;
        } catch (Exception e) {
            log.error("批量加密出错", e);
        }

        return contentList;
    }

    @Override
    public List<String> batchDecodeSecret(List<String> contentList) {
        if (CollectionUtils.isEmpty(contentList)) {
            return Collections.emptyList();
        }

        // 筛选需要解密的
        int size = contentList.size();
        List<String> decodeSecretList = new ArrayList<>(size);
        List<String> needDecodeContentList = new ArrayList<>(size);
        for (String content : contentList) {
            if (SecretUtils.isEncodeSecret(content)) {
                // 占坑
                decodeSecretList.add(UN_CODE_CONTENT_FLAG);
                needDecodeContentList.add(SecretUtils.removeSecretFlag(content));
            } else {
                decodeSecretList.add(content);
            }
        }

        // 没有需要解密的
        if (CollectionUtils.isEmpty(needDecodeContentList)) {
            return decodeSecretList;
        }

        try {
            // 解密
            List<String> decodeContentList = partitionCallSecretRequest(needDecodeContentList, false);

            // 填坑
            int decodeContentListIndex = 0;
            for (int i = 0; i < size; i++) {
                if (decodeSecretList.get(i) == UN_CODE_CONTENT_FLAG) {
                    decodeSecretList.set(i, decodeContentList.get(decodeContentListIndex++));
                }
            }
            return decodeSecretList;
        } catch (Exception e) {
            log.error("批量解密出错", e);
        }

        return contentList;
    }

    private List<String> partitionCallSecretRequest(List<String> contentList, boolean encode) throws Exception {
        List<String> resultList = new ArrayList<>(contentList.size());
        if (CollectionUtils.isEmpty(contentList)) {
            return resultList;
        }

        List<List<String>> partitionList = Lists.partition(contentList, PAGE_SIZE);
        for (List<String> subList : partitionList) {
            resultList.addAll(encode ? secretRequest.encode(subList) : secretRequest.decode(subList));
        }

        return resultList;
    }
}

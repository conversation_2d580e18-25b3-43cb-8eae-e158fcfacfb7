package com.raycloud.dmj.services.sentinel;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * user: hj
 * time: 2023/06/30 17:37
 * desc:
 */
@Setter
@Getter
public class SentinelResult implements Serializable {

    private boolean success;

    private Object entry;

    private Object[] reqArgs;

    public static SentinelResult ofSuccess(Object entry, Object[] reqArgs) {
        SentinelResult result = new SentinelResult();
        result.setEntry(entry);
        result.setReqArgs(reqArgs);
        result.setSuccess(true);

        return result;
    }

    public static SentinelResult ofSuccess(Object entry) {

        return ofSuccess(entry, null);
    }

    public static SentinelResult ofSuccess() {
        return ofSuccess(null, null);
    }

    public static SentinelResult ofFail() {
        SentinelResult result = new SentinelResult();
        result.setSuccess(false);

        return result;
    }

    public boolean isLimit() {
        return !this.success;
    }

}

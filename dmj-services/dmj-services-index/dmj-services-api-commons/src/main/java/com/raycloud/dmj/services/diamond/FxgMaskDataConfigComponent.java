package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FxgMaskDataConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

@Deprecated  //这个配置挪到tradeConfig里去了，可以去tj改
public class FxgMaskDataConfigComponent {
    private static final Logger logger = Logger.getLogger(FxgMaskDataConfigComponent.class);

    private static DiamondManager fxgMaskDataDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (fxgMaskDataDiamondManager == null) {
                fxgMaskDataDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_MASK_DATA, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            convertConfig(config);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_MASK_DATA + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_MASK_DATA + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = fxgMaskDataDiamondManager.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_MASK_DATA + ",config=" + config);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_MASK_DATA + ",出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (StringUtils.isBlank(config)) {
            return;
        }
        FxgMaskDataConfig fxgMaskDataConfig = JSONObject.parseObject(config, FxgMaskDataConfig.class);
        String allowMaskDataMatchTagCompanyIds = fxgMaskDataConfig.getAllowMaskDataMatchTagCompanyIds();
        if(StringUtils.isNotEmpty(allowMaskDataMatchTagCompanyIds) && "ALL".equalsIgnoreCase(allowMaskDataMatchTagCompanyIds.trim())){
            fxgMaskDataConfig.setAllAllowMaskDataMatchTag(true);
        } else {
            Set<String> set = new HashSet<>(Arrays.asList(allowMaskDataMatchTagCompanyIds.split(",")));
            fxgMaskDataConfig.setAllowMaskDataMatchTagCompanySet(set);
        }
        ConfigHolder.FXG_MASK_DATA_CONFIG = fxgMaskDataConfig;
    }
}

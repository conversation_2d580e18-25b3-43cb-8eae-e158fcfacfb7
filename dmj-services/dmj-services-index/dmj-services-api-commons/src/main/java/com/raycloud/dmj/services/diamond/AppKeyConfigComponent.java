package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.appkey.AppKeyConfig;
import com.raycloud.dmj.services.context.ProjectContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.Executor;

/**
 * user: hj
 * time: 2021/01/29 11:50
 * desc:
 */
@Component
public class AppKeyConfigComponent {

    private static final Logger logger = Logger.getLogger(AppKeyConfigComponent.class);

    private static DiamondManager diamondManager;
    
    @Resource
    private ProjectContext projectContext;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.APPKEY_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.APPKEY_CONFIG = JSONObject.parseObject(config, AppKeyConfig.class);
                            initConfig();
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.APPKEY_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.APPKEY_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.APPKEY_CONFIG + ",config=" + config);
                if (StringUtils.isNotBlank(config)) {
                    ConfigHolder.APPKEY_CONFIG = JSONObject.parseObject(config, AppKeyConfig.class);
                    initConfig();
                    logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.APPKEY_CONFIG + ",config=" + config);
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.APPKEY_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

    private void initConfig() {
        ConfigHolder.APPKEY_CONFIG.init(projectContext.getProjectProfile());
    }
}

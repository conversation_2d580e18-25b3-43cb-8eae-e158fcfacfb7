package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.PddFzGrayConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * @Author: chenchaochao
 * @Date: 2020/4/23 2:47 下午
 */
@Component
public class PddFzGrayConfigComponent {
    private static final Logger logger = Logger.getLogger(PddFzGrayConfigComponent.class);

    private static DiamondManager pddFzGrayDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (pddFzGrayDiamondManager == null) {
                pddFzGrayDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PDD_FZ_GRAY, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_PDD_FZ_GRAY + ",config=" + config);
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            PddFzGrayConfig pddFzGrayConfig = JSONObject.parseObject(config, PddFzGrayConfig.class);
                            Set<String> sets = new HashSet<>(Arrays.asList(pddFzGrayConfig.getCompanyIds().split(",")));
                            Set<String> fxgZhijianCompanyIds = new HashSet<>(Arrays.asList(pddFzGrayConfig.getFxgZhijianCompanyIds().split(",")));
                            pddFzGrayConfig.setFxgZhijianCompanyIdSet(fxgZhijianCompanyIds);
                            Set<String> decryptBlackCompanyIds = new HashSet<>(Arrays.asList(pddFzGrayConfig.getDecryptBlackCompanyIds().split(",")));
                            pddFzGrayConfig.setDecryptBlackCompanyIdSet(decryptBlackCompanyIds);
                            pddFzGrayConfig.setCompanyIdSet(sets);
                            ConfigHolder.PDD_FZ_GRAY_CONFIG = pddFzGrayConfig;
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_PDD_FZ_GRAY + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = pddFzGrayDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_PDD_FZ_GRAY + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                PddFzGrayConfig pddFzGrayConfig = JSONObject.parseObject(config, PddFzGrayConfig.class);
                Set<String> sets = new HashSet<>(Arrays.asList(pddFzGrayConfig.getCompanyIds().split(",")));
                Set<String> fxgZhijianCompanyIds = new HashSet<>(Arrays.asList(pddFzGrayConfig.getFxgZhijianCompanyIds().split(",")));
                Set<String> decryptBlackCompanyIds = new HashSet<>(Arrays.asList(pddFzGrayConfig.getDecryptBlackCompanyIds().split(",")));
                pddFzGrayConfig.setCompanyIdSet(sets);
                pddFzGrayConfig.setFxgZhijianCompanyIdSet(fxgZhijianCompanyIds);
                pddFzGrayConfig.setDecryptBlackCompanyIdSet(decryptBlackCompanyIds);
                ConfigHolder.PDD_FZ_GRAY_CONFIG = pddFzGrayConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_PDD_FZ_GRAY + ",出错:" + e.getMessage(), e);
        }
    }
}

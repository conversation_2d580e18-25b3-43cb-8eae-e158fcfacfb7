package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.session.DiffTerminalPathConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * user: hj
 * time: 2022/07/07 13:43
 * desc:
 */
@Component
public class DiffTerminalPathConfigComponent {

    private static final Logger logger = Logger.getLogger(DiffTerminalPathConfigComponent.class);

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.Diff_Terminal_Path_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.Diff_Terminal_Path_Config = JSONObject.parseObject(config, DiffTerminalPathConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.Diff_Terminal_Path_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.Diff_Terminal_Path_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.Diff_Terminal_Path_CONFIG + ",config=" + config);
                if (StringUtils.isNotBlank(config)) {
                    ConfigHolder.Diff_Terminal_Path_Config = JSONObject.parseObject(config, DiffTerminalPathConfig.class);
                    logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.Diff_Terminal_Path_CONFIG + ",config=" + config);
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.Diff_Terminal_Path_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }
}

package com.raycloud.dmj.services.utils;

import com.jd.security.tde.Constants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.nio.ByteBuffer;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import com.jd.security.tde.util.Base64;

/**
 * <AUTHOR>
 * @date 2021/1/11 16:55
 */
public class SecretUtils {

    private static final String SECRET_FLAG_PREFIX = "{";
    private static final String SECRET_FLAG_SUFFIX = "}";
    private static final String DESENSITIZATION_FLAG = "*";

    public static boolean isPddEncrypt(String data) {
        char SEP_PHONE = '$';
        char SEP_ID = '#';
        char SEP_NORMAL = '~';
        Pattern BASE64_PATTERN = Pattern.compile("^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$");
        if (null == data || data.length() < 44) {
            return false;
        }
        if (data.charAt(0) != data.charAt(data.length() - 1)) {
            return false;
        }
        char separator = data.charAt(0);
        String[] dataArray = StringUtils.split(data, separator);
        if (dataArray.length < 2
                || !StringUtils.isNumeric(dataArray[dataArray.length - 1])) {
            return false;
        }
        if (separator == SEP_PHONE || separator == SEP_ID) {
            if (dataArray.length != 3) {
                return false;
            }
            if (data.charAt(data.length() - 2) == separator) {
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[1].length() >= 44;
            } else {
                return BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[1].length() >= 44;
            }
        }
        if (separator == SEP_NORMAL) {
            if (data.charAt(data.length() - 2) == separator) {
                if (dataArray.length != 3) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        BASE64_PATTERN.matcher(dataArray[1]).matches() &&
                        dataArray[0].length() >= 44;
            } else {
                if (dataArray.length != 2) {
                    return false;
                }
                return BASE64_PATTERN.matcher(dataArray[0]).matches() &&
                        dataArray[0].length() >= 44;
            }
        }
        return false;
    }

    public static boolean isJdEncrypt(String base64ct) {
        try {
            return isJdEncrypt(Base64.decode(base64ct));
        } catch (Exception var2) {
            return false;
        }
    }

    public static boolean isEncodeSecret(String content) {
        return StringUtils.isNotBlank(content) && content.startsWith(SECRET_FLAG_PREFIX) && content.endsWith(SECRET_FLAG_SUFFIX);
    }

    public static boolean needEncode(String content) {
        return StringUtils.isNotEmpty(content) && !isEncodeSecret(content) && !SecretUtils.isPddEncrypt(content) && !SecretUtils.isJdEncrypt(content);
    }

    public static String addSecretFlag(String content) {
        if (content == null) {
            return null;
        }

        return SECRET_FLAG_PREFIX + content + SECRET_FLAG_SUFFIX;
    }

    public static String removeSecretFlag(String content) {
        if (!isEncodeSecret(content)) {
            return content;
        }

        return content.substring(1, content.length() - 1);
    }

    public static <T> List<T> findEntityInObjects(Object[] objects, Class<T> targetClass) {
        List<T> entityList = new LinkedList<>();

        if (objects == null || objects.length == 0 || targetClass == null) {
            return entityList;
        }

        for (Object arg : objects) {
            entityList.addAll(findEntityInObject(arg, targetClass));
        }

        return entityList;
    }

    public static <T> List<T> findEntityInObject(Object object, Class<T> targetClass) {
        List<T> entityList = new LinkedList<>();

        if (object == null || targetClass == null) {
            return entityList;
        }

        if (targetClass.isAssignableFrom(object.getClass())) {
            entityList.add((T) object);
            return entityList;
        }

        if (object instanceof List) {
            List<Object> list = (List<Object>)object;
            if (CollectionUtils.isNotEmpty(list)) {
                for (Object o : list) {
                    if (targetClass.isAssignableFrom(o.getClass())) {
                        entityList.add((T) o);
                    }
                }
            }
            return entityList;
        }

        if (object instanceof Map) {
            Map map = (Map)object;
            if (MapUtils.isNotEmpty(map)) {
                for (Object o : map.values()) {
                    if (targetClass.isAssignableFrom(o.getClass())) {
                        entityList.add((T) o);
                    }
                }
            }
            return entityList;
        }

        return entityList;
    }

    private static boolean isJdEncrypt(byte[] ct) {
        try {
            byte ctype = ct[0];
            boolean flag = false;
            if (Constants.cipher_type.fromValue(ctype) == Constants.cipher_type.LARGE || Constants.cipher_type.fromValue(ctype) == Constants.cipher_type.REGULAR) {
                flag = true;
            }

            byte[] mkIdx = extractKeyId(ct, flag);
            if (mkIdx != null && mkIdx.length > 0) {
                return true;
            }
        } catch (Exception var4) {
        }

        return false;
    }

    private static byte[] extractKeyId(byte[] ct, boolean isStrong) {
        ByteBuffer b = ByteBuffer.wrap(ct);
        b.get();
        byte[] eid;
        if (isStrong) {
            short eidLen = b.getShort();
            eid = new byte[eidLen];
            if (ct.length - 3 < eidLen) {
                return null;
            }

            b.get(eid);
        } else {
            b.get();
            if (ct.length - 2 < 16) {
                return null;
            }

            eid = new byte[16];
            b.get(eid);
        }

        return eid;
    }

    public static boolean isTbEncrypt(String data) {
        if (StringUtils.contains(data, DESENSITIZATION_FLAG)) {
            return true;
        }
        return false;
    }

}

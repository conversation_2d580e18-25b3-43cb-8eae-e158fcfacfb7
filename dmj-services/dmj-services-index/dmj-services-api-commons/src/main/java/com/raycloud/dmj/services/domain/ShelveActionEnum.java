package com.raycloud.dmj.services.domain;

public enum ShelveActionEnum {
     PC_SHELVE("上架单上架"),
     CAIGOU_FAST_SHELVE("采购快速上架"),
     WORKING_STORAGE_UP_SHELF("暂存区上架"),
     PRODUCT_ORDER_SHELVE("加工单上架"),
     PDA_CAIGOU_SHELVE("PDA直接采购上架"),
     PDA_FAST_SHELVE("PDA快速上架"),
     PDA_PRODUCT_ORDER_SHELVE("PDA加工上架"),
     PDA_PRODUCT_ORDER_MATERIAL_SHELVE("PDA加工上架（扣减原料）"),
     PDA_MULTI_SHELVE("PDA直接混放上架"),
     PDA_CAIGOU_MULTI_SHELVE("PDA采购混放上架"),
     PDA_OTHER_SHELVE("PDA其他上架"),
     PDA_FAST_SHELVE_BATCH("PDA批量采购上架"),
     PDA_BOX_OP_SHELVE_BATCH("PDA批量按箱上架"),
     PDA_BOX_OP_SHELVE("PDA按箱上架"),
     PDA_AFTER_SALE_SHELVE("PDA售后快速上架"),
     PDA_ORDER_UPSHELF("PDA按单上架"),
     CAIGOU_ORDER_SHELVE("PDA采购单上架"),
     PDA_RECEIPT_FAST_RECEIVE("PDA采购快速收货"),
 ;


    /**
     * name
     */
    private String name;


    ShelveActionEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}

package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FlowControlConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 初始化 DiamondConfig.DATE_ID_FLOW_CONTROL
 *
 * <AUTHOR>
 * @Date 2019-10-08
 **/
@Component
public class FlowControlComponent {

    private static final Logger logger = Logger.getLogger(FlowControlComponent.class);

    private static DiamondManager flowControlDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (flowControlDiamondManager == null) {
                flowControlDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATE_ID_FLOW_CONTROL, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isBlank(config)) {
                            return;
                        }
                        try {
                            List<FlowControlConfig> list = JSONObject.parseArray(config, FlowControlConfig.class);
                            if (CollectionUtils.isEmpty(list)) {
                                return;
                            }
                            ConfigHolder.FLOW_CONTROL_CONFIG_MAP = list.stream().collect(
                                    Collectors.toMap(FlowControlConfig::getResource, v -> v, (k1, k2) -> k2));
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_FLOW_CONTROL + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_FLOW_CONTROL + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = flowControlDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_FLOW_CONTROL + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                List<FlowControlConfig> list = JSONObject.parseArray(config, FlowControlConfig.class);
                if (CollectionUtils.isNotEmpty(list)) {
                    ConfigHolder.FLOW_CONTROL_CONFIG_MAP = list.stream().collect(
                            Collectors.toMap(FlowControlConfig::getResource, v -> v, (k1, k2) -> k2));
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_FLOW_CONTROL + ",出错:" + e.getMessage(), e);
        }
    }
}

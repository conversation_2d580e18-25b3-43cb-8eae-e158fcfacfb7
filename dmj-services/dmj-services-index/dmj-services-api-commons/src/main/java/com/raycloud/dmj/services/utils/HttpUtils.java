package com.raycloud.dmj.services.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
/**
 * user: hj
 * time: 2019/05/24 14:11
 * desc:
 */
public class HttpUtils {

    private static Logger logger = Logger.getLogger(HttpUtils.class);

    public static String getGetMethod(String url) {
        HttpResponse httpResponse = null;
        HttpEntity entity = null;
        String body = null;
        try {
            url = url.replaceAll(" ", "%20");
            HttpGet get = new HttpGet(url);
            HttpClient httpClient = HttpClients.createDefault();
            httpResponse = httpClient.execute(get);
            int code = httpResponse.getStatusLine().getStatusCode();
            if (code != 200) {
                get.abort();
                return null;
            }
            entity = httpResponse.getEntity();
            if (entity != null) {
                body = EntityUtils.toString(entity);
            }
        } catch (Exception e) {
            logger.error("http get 请求失败:"+url, e);
        }
        try {
            if (httpResponse != null) {
                EntityUtils.consume(entity); // 会自动释放连接
            }
        } catch (Exception e) {
            logger.error("http get 请求失败:"+url, e);
        }
        return body;
    }

    public static String doPost(String url, Map<String, Object> params) throws IOException {
        final CloseableHttpClient httpClient = getInstance();
        HttpPost httpPost = new HttpPost(url);

        List<BasicNameValuePair> requestParams = new ArrayList<>();
        for(Map.Entry<String, Object> entry : params.entrySet()){
            requestParams.add(new BasicNameValuePair(entry.getKey(), String.valueOf(entry.getValue())));
        }

        httpPost.setEntity(new UrlEncodedFormEntity(requestParams, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        CloseableHttpResponse response = httpClient.execute(httpPost);
        String result;
        try{
            int status = response.getStatusLine().getStatusCode();
            if(status != 200){
                throw new RuntimeException("发送消息失败：" + url
                        + ",请求状态：" + status + ",错误信息:" + EntityUtils.toString(response.getEntity()) + ")");
            }
            result = EntityUtils.toString(response.getEntity());
            EntityUtils.consume(response.getEntity());
        } finally{
            response.close();
            httpPost.releaseConnection();
        }
        return result;
    }


    /**
     * doPost json
     */
    public static String doPost(String url, String params) throws IOException {
        final CloseableHttpClient httpClient = getInstance();
        HttpPost httpPost = new HttpPost(url);

        httpPost.setEntity(new StringEntity(params, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = httpClient.execute(httpPost);
        String result;
        try{
            int status = response.getStatusLine().getStatusCode();
            if(status != 200){
                throw new RuntimeException("发送消息失败：" + url
                        + ",请求状态：" + status + ",错误信息:" + EntityUtils.toString(response.getEntity()) + ")");
            }
            result = EntityUtils.toString(response.getEntity(), "UTF-8");
            EntityUtils.consume(response.getEntity());
        } finally{
            response.close();
            httpPost.releaseConnection();
        }
        return result;
    }

    public static CloseableHttpClient getInstance(){
        return CloseableHttpClientHolderContext.instance;
    }

    private static SSLConnectionSocketFactory sslConnectionSocketFactory = null;

    private static class CloseableHttpClientHolderContext{
        private static CloseableHttpClient instance = HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .build();

    }

    public static String doGet(String url, Map<String, Object> param, Map<String, Object> header) throws Exception {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            // 创建uri
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, String.valueOf(param.get(key)));
                }
            }
            URI uri = builder.build();

            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);

            if (header != null && !header.isEmpty() ) {
                for (String key : header.keySet()) {
                    httpGet.addHeader(key, String.valueOf(header.get(key)));
                }
            }
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
                throw e;
            }
        }
        return resultString;
    }

    /**
     * 重定向请求
     * @param request 原请求request
     * @param response 原响应response
     * @param host 目标地址
     * @param body 请求内容
     * @param ip
     */
    public static void post(HttpServletRequest request, HttpServletResponse response, String host, String body, String ip) {
        InputStream is = null;
        try {
            HttpURLConnection httpUrlConn = null;
            URL url = null;
            if (StringUtils.isNotBlank(request.getQueryString())) {
                url = new URL(request.getRequestURL().toString().replace("erp.superboss.cc", host) + "?" + request.getQueryString());
            } else {
                url = new URL(request.getRequestURL().toString().replace("erp.superboss.cc", host));
            }
            logger.debug(String.format("url:%s, body:%s", JSON.toJSONString(url), body));
            if (ip != null) {
                String strs[] = ip.split("\\.");
                byte[] b = new byte[strs.length];
                for (int i = 0; i < strs.length; i++) {
                    b[i] = (byte) (Integer.parseInt(strs[i], 10));
                }
                Proxy proxy = new Proxy(Proxy.Type.HTTP,
                        new InetSocketAddress(InetAddress.getByAddress(b), 80));//b是绑定的ip,生成proxy代理对象
                httpUrlConn = (HttpURLConnection) url.openConnection(proxy);
            } else {
                httpUrlConn = (HttpURLConnection) url.openConnection();
            }

            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    httpUrlConn.setRequestProperty(headerName, request.getHeader(headerName));
                }
            }
            httpUrlConn.setRequestMethod("POST");
            httpUrlConn.setDoOutput(true);
            httpUrlConn.setDoInput(true);
            httpUrlConn.setConnectTimeout(20000);
            httpUrlConn.setReadTimeout(20000);
            httpUrlConn.setDefaultUseCaches(false);
            httpUrlConn.setUseCaches(false);
            httpUrlConn.setRequestProperty("Charset", "UTF-8");

            httpUrlConn.connect();

            OutputStream os = httpUrlConn.getOutputStream();
            os.write(body.getBytes());
            os.flush();
            try {
                int code = httpUrlConn.getResponseCode();
                logger.debug("响应code:" + code);
            } catch (Exception e) {
                logger.debug("获取响应吗错误：" + e.getMessage(), e);
            }
            int len = 0;

            is = httpUrlConn.getInputStream();
            byte[] bytes = new byte[1024];
            while ((len = is.read(bytes)) > 0) {
                logger.debug(String.format("out:%s", new String(bytes, 0, len)));
                response.getOutputStream().write(bytes, 0, len);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}

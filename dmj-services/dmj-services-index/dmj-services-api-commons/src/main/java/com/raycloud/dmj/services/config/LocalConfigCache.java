package com.raycloud.dmj.services.config;

import com.google.common.cache.Cache;
import com.raycloud.dmj.domain.Configurable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LocalConfigCache {

    private static final String LOCAL_CACHE_PREFIX_KEY = "local_config_cache_";
    private final Cache<String, String> cache = LocalConfigCacheFactory.getInstance();

    @Autowired
    private Configurable config;

    public String getProperty(String key) {
        String companyIdValue = cache.getIfPresent(LOCAL_CACHE_PREFIX_KEY + key);
        if (StringUtils.isNotBlank(companyIdValue)) {
            return companyIdValue;
        }
        String property = config.getProperty(key);
        cache.put(LOCAL_CACHE_PREFIX_KEY + key, property);
        return property;
    }
}

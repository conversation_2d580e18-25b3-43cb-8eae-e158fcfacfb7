package com.raycloud.dmj.services.helper;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.CompanyInfoVO;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.Map;

/**
 * 登录白名单权限帮助类
 * 主要是服务于采退出库的重构代码的验证
 */
@Component
public class LoginWhiteAuthHelper {
    private final Logger logger = Logger.getLogger(this.getClass());

    /**
     * 判断是否符合白名单的规则
     * @param staff 员工信息
     * @param businessCode 业务编码  该值由业务方自定义，值得位置建议放在
     *                     com.raycloud.dmj.domain.basis.params.GroupCompanyInfoVO 中
     * @return 是否符合白名单的规则 true 符合 false 不符合
     */
    public  boolean isWhiteAuth(Staff staff, String businessCode) {
        return true;
    }
}

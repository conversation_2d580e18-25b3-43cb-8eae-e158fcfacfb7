package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.ItemExportThreadConfig;
import com.raycloud.dmj.domain.diamond.PrintRebuildConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * 商品导出配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ItemExportThreadConfigComponent.java
 * @createTime 2024年12月09日 09:51:00
 */
@Slf4j
@Component
public class ItemExportThreadConfigComponent {
    private static DiamondManager ItemExportThreadConfigDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (ItemExportThreadConfigDiamondManager == null) {
                ItemExportThreadConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ITEM_EXPORT_THREAD_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        try {
                            log.info("更新diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ITEM_EXPORT_THREAD_CONFIG, config);
                            convertConfig(config);
                        } catch (Exception e) {
                            log.error("更新diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ITEM_EXPORT_THREAD_CONFIG, e);
                        }
                    }
                });

                String config = ItemExportThreadConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                log.info("拉取diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ITEM_EXPORT_THREAD_CONFIG, config);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ITEM_EXPORT_THREAD_CONFIG, e);
        }
    }

    private static void convertConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return;
        }
        ConfigHolder.ITEM_EXPORT_THREAD_CONFIG = JSONObject.parseObject(config, ItemExportThreadConfig.class);
    }
}

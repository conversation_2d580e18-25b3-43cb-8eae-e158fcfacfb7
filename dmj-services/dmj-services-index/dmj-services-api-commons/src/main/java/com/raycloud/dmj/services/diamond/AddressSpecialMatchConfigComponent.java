package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.AddressSpeciaMatchConfig;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Slf4j
@Component
public class AddressSpecialMatchConfigComponent {
    private static DiamondManager addressSpecialMatchConfigComponent;

    @PostConstruct
    public void init() {
        try {
            if (addressSpecialMatchConfigComponent == null) {
                addressSpecialMatchConfigComponent = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_ADDRESS_SPECIAL_MAP_MATCH_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        convertConfig(config);
                    }
                });

                String config = addressSpecialMatchConfigComponent.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.DATA_ID_ADDRESS_SPECIAL_MAP_MATCH_CONFIG + ", 出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (config == null || config.isEmpty()) {
            log.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.DATA_ID_ADDRESS_SPECIAL_MAP_MATCH_CONFIG + ", config is null or empty");
            return;
        }

        AddressSpeciaMatchConfig addressMapMatchConfig;
        try {
            addressMapMatchConfig = JSONObject.parseObject(config, AddressSpeciaMatchConfig.class);
        } catch (Exception e) {
            log.error("json转实体对象失败, diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.DATA_ID_ADDRESS_SPECIAL_MAP_MATCH_CONFIG + "配置失败, 原因:" + e.getMessage(), e);
            return;
        }

        if (addressMapMatchConfig != null) {
            ConfigHolder.ADDRESS_SPECIAL_MAP_MATCH_CONFIG = addressMapMatchConfig;
            log.info("更新diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.DATA_ID_ADDRESS_SPECIAL_MAP_MATCH_CONFIG + ", config = " + config);
        }
    }

}

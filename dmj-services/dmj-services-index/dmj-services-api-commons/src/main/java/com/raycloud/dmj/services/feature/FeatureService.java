package com.raycloud.dmj.services.feature;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.concurrent.TimeUnit;

@Service
public class FeatureService {

    private static final Logger logger = LoggerFactory.getLogger(FeatureService.class);

    /**
     * 功能开关缓存
     */
    private static final Cache<String, Boolean> FEATURE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    @Autowired(required = false)
    private IIndexDubboService indexDubboService;

    /**
     * 检查企业是否开启了指定功能
     */
    public boolean checkHasFeature(long companyId, Feature feature) {
        Assert.notNull(feature, "功能不能为空");
        return checkHasFeatureByCode(companyId, feature.getCode());
    }

    /**
     * 检查企业是否开启了指定功能
     */
    public boolean checkHasFeatureByCode(long companyId, String code) {
        Assert.notNull(code, "code不能为空");
        String cacheKey = buildCacheKey(companyId, code);
        Boolean hasFeature = FEATURE_CACHE.getIfPresent(cacheKey);
        if (hasFeature == null) {
            try {
                CheckHasFeatureRequest request = new CheckHasFeatureRequest();
                request.setCompanyId(companyId);
                request.setCode(code);
                CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
                if (response.isSuccess() && response.isHasFeature()) {
                    hasFeature = true;
                }else {
                    if(!response.isSuccess()){
                        String errorMsg = response.getErrorMsg();
                        Logs.debug(String.format("白名单功能请求失败 companyId=%s feature=%s errorMsg=%s",companyId,code,errorMsg));
                    }
                }
            } catch (Throwable e) {
                logger.error("检查企业是否开启了指定功能异常", e);
            } finally {
                if (hasFeature == null) {
                    hasFeature = false;
                }
            }

            FEATURE_CACHE.put(cacheKey, hasFeature);
        }

        return hasFeature;
    }

    private static String buildCacheKey(long companyId, String code) {
        return companyId + "_" + code;
    }
}

package com.raycloud.dmj.services.trace;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.domain.ItemTraceMessage;

import java.util.List;
import java.util.Map;

/**
 * 商品日志服务接口
 *
 * <AUTHOR>
 */
public interface IItemTraceService {

    /**
     * 同步记录商品日志消息（调用的比较多，默认认为同步）
     *
     * @param staff   用户
     * @param message 商品日志消息
     */
    void record(Staff staff, ItemTraceMessage message);

    /**
     * 同步批量记录商品日志消息（调用的比较多，默认认为同步）
     *
     * @param staff       用户
     * @param messageList 商品日志消息列表
     */
    void batchRecord(Staff staff, List<ItemTraceMessage> messageList);

    /**
     * 同步记录商品日志消息
     *
     * @param staff   用户
     * @param message 商品日志消息
     */
    void syncRecord(Staff staff, ItemTraceMessage message);

    /**
     * 同步批量记录商品日志消息
     *
     * @param staff       用户
     * @param messageList 商品日志消息列表
     */
    void syncBatchRecord(Staff staff, List<ItemTraceMessage> messageList);

    /**
     * 异步记录商品日志消息
     *
     * @param staff   用户
     * @param message 商品日志消息
     */
    void recordEc(Staff staff, ItemTraceMessage message);

    /**
     * 异步批量记录商品日志消息
     *
     * @param staff       用户
     * @param messageList 商品日志消息列表
     */
    void batchRecordEc(Staff staff, List<ItemTraceMessage> messageList);
}

package com.raycloud.dmj.services.domain;

/**
 * 商品日志关联单据类型的枚举类
 *
 * <AUTHOR>
 */
public enum ItemTraceBillTypeEnum {
    /**
     * 波次
     */
    WAVE(1, "波次"),
    /**
     * 系统订单
     */
    SYS(2, "系统订单"),
    /**
     * 售后工单
     */
    AFTER_SALE(3, "售后工单"),
    /**
     * 加工单
     */
    PROCESS(4, "加工单"),
    /**
     * 上架单
     */
    PUT_ON(5, "上架单"),
    /**
     * 收货单
     */
    ACCEPT(6, "收货单"),
    /**
     * 调拨单
     */
    ALLOT(7, "调拨单"),

    /**
     * 采购退货单
     */
    PURCHASE_RETURN(8, "采购退货单"),

    /**
     * 单据类型为对应波次类型
     */
    WAVE_PDA_ONE_PICK(9, "一单一件"),
    WAVE_PDA_MANY_PICK(10, "一单多件"),
    WAVE_PDA_PURCHASE_RETURN_PICK(11, "采购退货"),
    WAVE_PDA_REPLENISH_PICK(12, "补货"),
    WAVE_PDA_STALL_PICK(13, "批发配货"),
    WAVE_PDA_PRODUCT_ORDER_PICK(14, "加工"),
    WAVE_PDA_FREEDOM_PICK(15, "自由拣"),

    WAVE_PDA_DOWN_SHELF(30, "下架"),

    WAVE_PDA_OTHER_OUT_PICK(16, "其它出库"),

    /**
     * 其它入库单
     */
    OTHER_IN(17, "其它入库单"),
    /**
     * 其它出库单
     */
    OTHER_OUT(18, "其它出库单"),

    WAVE_PDA_STALL_V2_PICK(20, "档口配货"),
    ALLOT_IN(21, "调拨入库单"),
    ALLOT_OUT(22, "调拨出库单"),

    ;

    /**
     * code
     */
    private Integer code;
    /**
     * name
     */
    private String name;

    ItemTraceBillTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.EventNameConstants;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.StockUploadConfig;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.Executor;

/**
 * 库存上传配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21
 * @since JDK1.8
 */
@Component
public class StockUploadConfigComponent {
    private static final Logger logger = Logger.getLogger(StockUploadConfigComponent.class);

    private static DiamondManager diamondManager;

    @Resource
    private IEventCenter eventCenter;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.STOCK_UPLOAD_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.STOCK_UPLOAD_CONFIG = JSONObject.parseObject(config, StockUploadConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.STOCK_UPLOAD_CONFIG + ",config=" + config);
                            eventCenter.fireEvent(this, new EventInfo(EventNameConstants.STOCK_UPLOAD_CONFIG).setArgs(new Object[]{}), null);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.STOCK_UPLOAD_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.STOCK_UPLOAD_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.STOCK_UPLOAD_CONFIG = JSONObject.parseObject(config, StockUploadConfig.class);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.STOCK_UPLOAD_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }
}

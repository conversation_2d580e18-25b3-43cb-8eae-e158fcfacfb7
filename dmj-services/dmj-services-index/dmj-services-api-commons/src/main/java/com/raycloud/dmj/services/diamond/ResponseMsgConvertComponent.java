package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.ResponseMsgConvertConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/15 上午11:41
 * http响应结果错误信息msg转换
 */
@Slf4j
@Component
public class ResponseMsgConvertComponent {

    private static DiamondManager responseMsgConvertDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (responseMsgConvertDiamondManager == null) {
                responseMsgConvertDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        try {
                            log.info("更新diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, config);
                            convertConfig(config);
                        } catch (Exception e) {
                            log.error("更新diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, e);
                        }
                    }
                });

                String config = responseMsgConvertDiamondManager.getAvailableConfigureInfomation(5000L);
                log.info("拉取diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, config);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, e);
        }
    }

    private static void convertConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return;
        }
        ResponseMsgConvertConfig responseMsgConvertConfig = JSONObject.parseObject(config, ResponseMsgConvertConfig.class);
        if (responseMsgConvertConfig != null && CollectionUtils.isNotEmpty(responseMsgConvertConfig.getConvertDataList())) {
            List<ResponseMsgConvertConfig.ConvertData> sorted = responseMsgConvertConfig.getConvertDataList().stream().filter(v -> v.getOrder() != null).sorted(Comparator.comparing(s -> s.getOrder())).collect(Collectors.toList());
            responseMsgConvertConfig.setConvertDataList(sorted);
        }
        ConfigHolder.RESPONSE_MSG_CONVERT_CONFIG = responseMsgConvertConfig;
    }
}

package com.raycloud.dmj.services.domain;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * Created by daixiaoming on 2020/2/18.
 */
public class SyncIntervalLimitDate {

    private static Logger logger = LoggerFactory.getLogger(SyncIntervalLimitDate.class);

    /**
     * 全量数据同步周期，该周期内最多只执行一次全量数据同步，小于等于0表示不限制
     * 单位：小时
     */
    private int fullSyncInterval;

    /**
     * 最近7天数据同步周期，该周期内最多只执行一次近7天数据同步，小于等于0表示不限制
     * 单位：分钟
     */
    private int last7DaySyncInterval;

    /**
     * 最近3天数据同步周期，该周期内最多只执行一次近3天数据同步，小于等于0表示不限制
     * 单位：分钟
     */
    private int last3DaySyncInterval;

    /**
     * 最近1天数据同步周期，该周期内最多只执行一次近1天数据同步，小于等于0表示不限制
     * 单位：分钟
     */
    private int lastDaySyncInterval;

    /**
     * 最少同步数据时间
     * 单位：分钟
     */
    private int minSyncMinutes = 12 * 60;

    public Date getLimitedSyncStartDate(ICache cache, String userId, Date startDate) {
        if (fullSyncInterval > 0) {
            Date limitDate = checkLimitDate(cache, "syncFullData-" + userId, fullSyncInterval * 3600, 7 * 24 * 60, startDate);
            if (limitDate == null) {
                return startDate;
            }
            startDate = limitDate;
        }

        if (last7DaySyncInterval > 0) {
            Date limitDate = checkLimitDate(cache, "syncLast7DayData-" + userId, last7DaySyncInterval * 60, 3 * 24 * 60, startDate);
            if (limitDate == null) {
                return startDate;
            }
            startDate = limitDate;
        }

        if (last3DaySyncInterval > 0) {
            Date limitDate = checkLimitDate(cache, "syncLast3DayData-" + userId, last3DaySyncInterval * 60, 24 * 60, startDate);
            if (limitDate == null) {
                return startDate;
            }
            startDate = limitDate;
        }

        if (lastDaySyncInterval > 0) {
            Date limitDate = checkLimitDate(cache, "syncLastDayData-" + userId, lastDaySyncInterval * 60, minSyncMinutes, startDate);
            if (limitDate == null) {
                return startDate;
            }
            startDate = limitDate;
        }

        return startDate;
    }

    private static Date checkLimitDate(ICache cache, String cacheKey, int expireTime, int limitMinutes, Date startDate) {
        boolean hasKey = false;
        try {
            hasKey = cache.containKey(cacheKey);
        } catch (CacheException e) {
            logger.error("获取同步数据最后时间缓存异常", e);
        }

        if (hasKey) {
            //距离上次同步数据时间不足一个周期，需要限制本次同步的开始时间
            Date limitDate = DateUtils.addMinutes(new Date(), -limitMinutes);
            return limitDate.after(startDate) ? limitDate : startDate;
        } else {
            try {
                cache.add(cacheKey, System.currentTimeMillis(), expireTime);
            } catch (CacheException e) {
                logger.error("设置同步数据最后时间缓存异常", e);
            }
            //本次不限制，直接返回
            return null;
        }
    }

    public int getFullSyncInterval() {
        return fullSyncInterval;
    }

    public SyncIntervalLimitDate setFullSyncInterval(int fullSyncInterval) {
        this.fullSyncInterval = fullSyncInterval;
        return this;
    }

    public int getLast7DaySyncInterval() {
        return last7DaySyncInterval;
    }

    public SyncIntervalLimitDate setLast7DaySyncInterval(int last7DaySyncInterval) {
        this.last7DaySyncInterval = last7DaySyncInterval;
        return this;
    }

    public int getLast3DaySyncInterval() {
        return last3DaySyncInterval;
    }

    public SyncIntervalLimitDate setLast3DaySyncInterval(int last3DaySyncInterval) {
        this.last3DaySyncInterval = last3DaySyncInterval;
        return this;
    }

    public int getLastDaySyncInterval() {
        return lastDaySyncInterval;
    }

    public SyncIntervalLimitDate setLastDaySyncInterval(int lastDaySyncInterval) {
        this.lastDaySyncInterval = lastDaySyncInterval;
        return this;
    }

    public int getMinSyncMinutes() {
        return minSyncMinutes;
    }

    public SyncIntervalLimitDate setMinSyncMinutes(int minSyncMinutes) {
        Assert.isTrue(minSyncMinutes > 0, "最少同步数据时间必须大于0");
        this.minSyncMinutes = minSyncMinutes;
        return this;
    }
}

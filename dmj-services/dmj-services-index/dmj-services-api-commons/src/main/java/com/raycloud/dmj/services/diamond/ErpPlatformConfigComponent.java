package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.ErpPlatformConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/***
 * @DESCRIPTION: 多平台diamond配置
 * <AUTHOR>
 * @params:
 * @return:
 * @Date: 2022/2/10 10:04 上午
 * @Modified By:
 */
@Component
public class ErpPlatformConfigComponent {
    private static final Logger logger = Logger.getLogger(ErpPlatformConfigComponent.class);

    private static DiamondManager erpPlatformConfigDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (erpPlatformConfigDiamondManager == null) {
                erpPlatformConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.ERP_PLATFORM_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.ERP_PLATFORM_CONFIG = JSONObject.parseObject(config, ErpPlatformConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.ERP_PLATFORM_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.ERP_PLATFORM_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = erpPlatformConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.ERP_PLATFORM_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.ERP_PLATFORM_CONFIG = JSONObject.parseObject(config, ErpPlatformConfig.class);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.ERP_PLATFORM_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

    public static Map<String, String> getLogisticsCodeNameMap(){
        return ConfigHolder.ERP_PLATFORM_CONFIG == null ? null : ConfigHolder.ERP_PLATFORM_CONFIG.getLogisticsCodeNameMap();
    }

    public static Set<String> getLogisticsCodeNameMap(String logisticsCode){
        Set<String> values = null;
        if(ConfigHolder.ERP_PLATFORM_CONFIG != null && ConfigHolder.ERP_PLATFORM_CONFIG.getLogisticsCodeValueMap() != null){
            values = ConfigHolder.ERP_PLATFORM_CONFIG.getLogisticsCodeValueMap().get(logisticsCode);
            if(values != null && !values.contains(logisticsCode)){
                values.add(logisticsCode);
            }
        }
        return values;
    }
}

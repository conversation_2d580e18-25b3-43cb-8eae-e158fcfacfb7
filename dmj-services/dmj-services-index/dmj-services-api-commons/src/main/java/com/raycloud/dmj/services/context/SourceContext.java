package com.raycloud.dmj.services.context;

import org.springframework.beans.factory.InitializingBean;


/**
 * 来源获取
 * <AUTHOR>
 *
 */
public class SourceContext implements InitializingBean {

    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public void afterPropertiesSet(){
        SourceHolder.SOURCE = source;
        //这里直接输出,在启动时打印出来
        System.out.println(String.format("source.profile=[%s]", source));
    }
}

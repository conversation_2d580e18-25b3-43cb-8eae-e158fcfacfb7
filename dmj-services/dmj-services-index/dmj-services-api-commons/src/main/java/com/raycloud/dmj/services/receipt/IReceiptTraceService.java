package com.raycloud.dmj.services.receipt;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.domain.ReceiptTrace;

import java.util.List;

/**
 * 单据日志服务
 *
 * <AUTHOR>
 * @date 2020/7/7 11:27
 */
public interface IReceiptTraceService {

    /**
     * 批量保存日志
     *
     * @param staff
     * @param traces
     * @return
     */
    void save(Staff staff, List<ReceiptTrace> traces);

    /**
     * 保存日志
     *
     * @param staff
     * @param trace
     * @return
     */
    void save(Staff staff, ReceiptTrace trace);

    /**
     * 根据单据和类型查询，无分页，代码里兜底最大查1W条数据
     *
     * @param staff
     * @param receiptIds
     * @param receiptType
     * @return
     */
    List<ReceiptTrace> queryByReceipt(Staff staff, List<Long> receiptIds, Integer receiptType);


    /**
     * 根据单据和类型查询，带分页
     * @param staff
     * @param receiptIds
     * @param receiptType
     * @param page
     * @return
     */
    List<ReceiptTrace> queryByReceipt(Staff staff, List<Long> receiptIds, Integer receiptType, Page page);


    List<ReceiptTrace> queryByReceiptNos(Staff staff, List<String> receiptNos, Integer receiptType, Page page);
}

package com.raycloud.dmj.services.helper;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.Map;

public class CommonHelper {

    private static Logger logger = Logger.getLogger(CommonHelper.class);

    public static <T> T getCacheOfCatched(String key, ICache cache) {
        try {
            return cache.get(key);
        } catch (CacheException e) {
            logger.error("缓存异常", e);
        }
        return null;
    }

    public static boolean setCacheOfCatched(final String key, final Object value, final int exp, ICache cache) {
        try {
            cache.set(key, value, exp);
            return true;
        } catch (CacheException e) {
            logger.error("缓存异常", e);
        }
        return false;
    }

    public static boolean setCacheOfCatched(final String key, final Object value, ICache cache) {
        try {
            cache.set(key, value);
            return true;
        } catch (CacheException e) {
            logger.error("缓存异常", e);
        }
        return false;
    }

    public static void deleteCacheOfCatched(String key, ICache cache) {
        try {
            cache.delete(key);
        } catch (CacheException e) {
            logger.error("缓存异常", e);
        }

    }

    public static void initFieldMapping(Class clazz, Map<String, String> fieldMapping) {
        Field[] fields = clazz.getDeclaredFields();
        if (fields == null || fields.length <= 0) {
            return;
        }

        for (Field field : fields) {
            Annotation annotation = field.getAnnotation(JSONField.class);
            if (annotation == null) {
                continue;
            }

            String name = ((JSONField) annotation).name();
            if (StringUtils.isEmpty(name)) {
                continue;
            }

            if (fieldMapping.containsKey(name)) {
                throw new RuntimeException(String.format("%s的JSONField有重复的值:%s", clazz.getSimpleName(), name));
            }

            fieldMapping.put(name, field.getName());
        }
    }
}

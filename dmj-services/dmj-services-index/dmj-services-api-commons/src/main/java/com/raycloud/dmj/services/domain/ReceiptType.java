package com.raycloud.dmj.services.domain;

/**
 * 单据类型
 *
 * <AUTHOR>
 * @date 2020/5/27 19:54
 */
public enum ReceiptType {
    /**
     * 采购单
     */
    PURCHASE_ORDER(1, "采购单"),

    /**
     * 收货单
     */
    WAREHOUSE_ENTRY(2, "收货单"),

    /**
     * 上架单
     */
    SHELVE_ORDER(3, "上架单"),

    /**
     * 采退单
     */
    PURCHASE_RETURN(4, "采退单"),

    /**
     * 其它入库单
     */
    OTHER_IN_ORDER(5, "其它入库单"),

    /**
     * 其它出库单
     */
    OTHER_OUT_ORDER(6, "其它出库单"),

    BOX(11, "箱管理日志"),

    UNSHELVE(12, "下架单"),
    PURCHASE_ALI_ORDER(10, "1688一键下单"),

    CHECK_SHEET(7, "质检单"),

    PREIN_ORDER(13, "预约入库单"),

    STOCK_PRODUCT_ORDER(14, "加工单"),

    LOGISTICS_ORDER(20, "出库单"),

    STOCK_SPACE(15, "库存货位"),

    ALLOCATE_TASK(23, "调拨单"),

    ALLOCATE_OUT(21, "调拨出库"),

    ALLOCATE_IN(22, "调拨入库"),

    TRANSFER(24,"移库"),

    INVENTORY(25,"盘点单"),
    AUTO_ALLOCATE(26,"自动调拨"),

    AI_PACKMA_MODIFIED(27, "包材修正"),
    ;


    private Integer type;

    private String name;

    ReceiptType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static ReceiptType parseType(Integer type) {
        for (ReceiptType value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }

        return null;
    }
}

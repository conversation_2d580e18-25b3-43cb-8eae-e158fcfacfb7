package com.raycloud.dmj.services.config;

import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import org.springframework.beans.factory.InitializingBean;

public class FastjsonConfig implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        // 关闭 fastjson 的 asm，防止 crash
        SerializeConfig.getGlobalInstance().setAsmEnable(false);
        ParserConfig.getGlobalInstance().setAsmEnable(false);
    }
}

package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.MixedCachePubSubConfig;
import com.raycloud.dmj.domain.diamond.ResponseMsgConvertConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/10 下午6:14
 */
@Slf4j
@Component
public class MixedCachePubSubConfigComponent {

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.MIXED_CACHE_PUB_SUB_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        try {
                            log.info("更新diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, config);
                            convertConfig(config);
                        } catch (Exception e) {
                            log.error("更新diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                log.info("拉取diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, config);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.RESPONSE_MSG_CONVERT_CONFIG, e);
        }
    }

    private static void convertConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return;
        }
        MixedCachePubSubConfig mixedCachePubSubConfig = JSONObject.parseObject(config, MixedCachePubSubConfig.class);
        ConfigHolder.MIXED_CACHE_PUB_SUB_CONFIG = mixedCachePubSubConfig;
    }
}

package com.raycloud.dmj.services.context;

import com.raycloud.dmj.domain.Configurable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * user: hj
 * time: 2021/02/23 11:04
 * desc: 用于一些原有静态类需要使用 bean 的场景.
 */
@Component
public class BeanHolder {

    @Resource
    private Configurable config;

    public static Configurable configBean;

    @PostConstruct
    public void init() {
        configBean = config;
    }
}

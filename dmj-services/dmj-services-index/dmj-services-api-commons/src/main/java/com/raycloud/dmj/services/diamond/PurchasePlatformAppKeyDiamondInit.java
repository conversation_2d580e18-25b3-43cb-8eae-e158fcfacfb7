package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;

import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.PurchasePlatformAppKeyDiamondConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 * @Description 采购创建1688一件代发订单外部平台appKey
 * @date 2024/8/12 11:15
 */
@Component
public class PurchasePlatformAppKeyDiamondInit {

    private Logger logger = Logger.getLogger(this.getClass());

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.TRADE_SOURCE_APPKEY, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        convertConfig(config);
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.TRADE_SOURCE_APPKEY + ", 出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (config == null || config.isEmpty()) {
            logger.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.TRADE_SOURCE_APPKEY + ", config is null or empty");
            return;
        }

        PurchasePlatformAppKeyDiamondConfig purchasePlatformAppKeyDiamondConfig;
        try {
            purchasePlatformAppKeyDiamondConfig = JSONObject.parseObject(config, PurchasePlatformAppKeyDiamondConfig.class);
        } catch (Exception e) {
            logger.error("json转实体对象失败, diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.TRADE_SOURCE_APPKEY + "配置失败, 原因:" + e.getMessage(), e);
            return;
        }

        if (purchasePlatformAppKeyDiamondConfig != null && CollectionUtils.isNotEmpty(purchasePlatformAppKeyDiamondConfig.getTradeSourceAppkeyList())) {
            ConfigHolder.PURCHASE_PLATFORM_APP_KEY_DIAMOND_CONFIG = purchasePlatformAppKeyDiamondConfig;
            logger.info("更新diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = " + DiamondConfig.TRADE_SOURCE_APPKEY + ", config = " + config);
        }
    }

}

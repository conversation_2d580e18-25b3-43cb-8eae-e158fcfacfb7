package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.SqlManageDiamondConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Component
public class SqlManageConfigComponent {
    private static final Logger logger = Logger.getLogger(SqlManageConfigComponent.class);

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_SQL_MANAGE, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG = JSONObject.parseObject(config, SqlManageDiamondConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_SQL_MANAGE + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_SQL_MANAGE + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_SQL_MANAGE + ",config=" + config);
                if (StringUtils.isNotBlank(config)) {
                    ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG = JSONObject.parseObject(config, SqlManageDiamondConfig.class);
                    logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_SQL_MANAGE + ",config=" + config);
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_SQL_MANAGE + ",出错:" + e.getMessage(), e);
        }
    }
}

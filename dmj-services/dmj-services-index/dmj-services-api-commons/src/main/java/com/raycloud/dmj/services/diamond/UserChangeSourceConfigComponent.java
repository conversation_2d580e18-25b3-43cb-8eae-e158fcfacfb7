package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.UserChangeSourceConfig;
import com.raycloud.dmj.domain.diamond.domian.UserChangeSourceInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description 针对天猫淘宝店铺改source的配置，目前主要是tm -> tb
 * <AUTHOR>
 * @date 2023/9/19 11:02
 */
@Component
public class UserChangeSourceConfigComponent {

    private static final Logger logger = Logger.getLogger(UserChangeSourceConfigComponent.class);

    private static DiamondManager userChangeSourceDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (userChangeSourceDiamondManager == null) {
                userChangeSourceDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.USER_CHANGE_SOURCE_CONFIG, new ManagerListener() {

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            UserChangeSourceConfig userChangeSourceConfig = JSONObject.parseObject(config, UserChangeSourceConfig.class);
                            buildData(userChangeSourceConfig);
                            ConfigHolder.USER_CHANGE_SOURCE_CONFIG = userChangeSourceConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.USER_CHANGE_SOURCE_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.USER_CHANGE_SOURCE_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = userChangeSourceDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.USER_CHANGE_SOURCE_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                UserChangeSourceConfig userChangeSourceConfig = JSONObject.parseObject(config, UserChangeSourceConfig.class);
                buildData(userChangeSourceConfig);
                ConfigHolder.USER_CHANGE_SOURCE_CONFIG = userChangeSourceConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.USER_CHANGE_SOURCE_CONFIG + ",出错:" + e.getMessage(), e);
        }



    }
    /**
     * 这里把信息组装到cacheMap方便获取。
     */
    private void buildData (UserChangeSourceConfig config) {
        if (config == null) {
            return;
        }
        Map<Long, List<UserChangeSourceInfo>> companyIdInfosMap = config.getCompanyIdInfosMap();
        if (MapUtils.isEmpty(companyIdInfosMap)) {
            return;
        }

        Map<Long, Map<Long, UserChangeSourceInfo>> cacheMap = Maps.newHashMapWithExpectedSize(companyIdInfosMap.size());
        companyIdInfosMap.forEach((companyId, infoList) -> {
            if (CollectionUtils.isNotEmpty(infoList)) {
                cacheMap.put(companyId, infoList.stream().collect(Collectors.toMap(UserChangeSourceInfo::getUserId, Function.identity(), (v1, v2) -> v2)));
            }
        });

        config.setCacheMap(cacheMap);
    }
}

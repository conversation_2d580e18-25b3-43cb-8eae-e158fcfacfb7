package com.raycloud.dmj.services.utils;

import org.apache.commons.beanutils.MethodUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;

/**
 * user: hj
 * time: 2018/11/13 上午11:17
 * desc:
 */
public class CopyPropUtils {

    /**
     * 因为BeanUtils不支持set方法返回值为对象的
     * @param bean
     * @param name
     * @param value
     * @throws NoSuchMethodException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    public static void copyProperty(Object bean, String name, Object value) throws
            NoSuchMethodException, IllegalAccessException, InvocationTargetException {

        MethodUtils.invokeMethod(bean, getMethodName(name), value);
    }

    private static String getMethodName(String fieldName){
        if(StringUtils.isEmpty(fieldName)){
            return fieldName;
        }

        String firstName = fieldName.substring(0, 1).toUpperCase();
        String endName = "";
        if(fieldName.length() > 1){
            endName = fieldName.substring(1);
        }

        return new StringBuilder("set").append(firstName).append(endName).toString();
    }
}

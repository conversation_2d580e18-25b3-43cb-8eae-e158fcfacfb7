package com.raycloud.dmj.services.sentinel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * user: hj
 * time: 2023/07/05 20:09
 * desc:
 */
@Component
public class SentinelVisitor {

    @Autowired(required = false)
    protected ISentinelLimiter sentinelLimiter;

    public SentinelResult entry(String resourceName, Object[] args) {
        if (sentinelLimiter == null) {
            return SentinelResult.ofSuccess();
        }

        return sentinelLimiter.entry(resourceName, args);
    }

    public void exit(SentinelResult result, Exception ex) {
        if (sentinelLimiter == null) {
            return;
        }

        sentinelLimiter.exit(result, ex);
    }
}

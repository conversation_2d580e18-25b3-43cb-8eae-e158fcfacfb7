package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FxgOrderSyncConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;

/**
 * 放心购订单同步配置
 * @Author: zhengbaifu
 * @Date: 2021/7/14
 */
@Component
public class FxgOrderSyncConfigComponent {
    private static final Logger logger = Logger.getLogger(FxgOrderSyncConfigComponent.class);

    private static DiamondManager fxgOrderSyncDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (fxgOrderSyncDiamondManager == null) {
                fxgOrderSyncDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_ORDER_SYNC, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            FxgOrderSyncConfig fxgOrderSyncConfig = JSONObject.parseObject(config, FxgOrderSyncConfig.class);
                            buildData(fxgOrderSyncConfig);
                            ConfigHolder.FXG_ORDER_SYNC_CONFIG = fxgOrderSyncConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_ORDER_SYNC + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_ORDER_SYNC + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = fxgOrderSyncDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_ORDER_SYNC + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                FxgOrderSyncConfig syncConfig = JSONObject.parseObject(config, FxgOrderSyncConfig.class);
                buildData(syncConfig);
                ConfigHolder.FXG_ORDER_SYNC_CONFIG = syncConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FXG_ORDER_SYNC + ",出错:" + e.getMessage(), e);
        }
    }

    /**
     * 组装数据
     * @param fxgOrderSyncConfig
     */
    private void buildData(FxgOrderSyncConfig fxgOrderSyncConfig){
        String useV2AuthCompanyIds = fxgOrderSyncConfig.getUseV2AuthCompanyIds();
        if(StringUtils.isNotEmpty(useV2AuthCompanyIds) && "ALL".equalsIgnoreCase(useV2AuthCompanyIds.trim())){
            fxgOrderSyncConfig.setAllUseV2Auth(true);
        } else {
            Set<String> sets = new HashSet<>(Arrays.asList(useV2AuthCompanyIds.split(",")));
            fxgOrderSyncConfig.setUseV2AuthCompanyIdSet(sets);
        }

        String useNewQueryCompanyIds = fxgOrderSyncConfig.getUseNewQueryCompanyIds();
        if(StringUtils.isNotEmpty(useNewQueryCompanyIds) && "ALL".equalsIgnoreCase(useNewQueryCompanyIds.trim())){
            fxgOrderSyncConfig.setAllUseNewQuery(true);
        } else {
            Set<String> sets = new HashSet<>(Arrays.asList(useNewQueryCompanyIds.split(",")));
            fxgOrderSyncConfig.setUseNewQueryCompanyIdSet(sets);
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String withoutACompanyIds = fxgOrderSyncConfig.getWithoutACompanyIds();
        if(StringUtils.isNotEmpty(withoutACompanyIds) && "ALL".equalsIgnoreCase(withoutACompanyIds.trim())){
            fxgOrderSyncConfig.setAllUseWithoutA(true);
        } else {
            Set<String> sets = new HashSet<>(Arrays.asList(withoutACompanyIds.split(",")));
            fxgOrderSyncConfig.setWithoutACompanyIdSet(sets);
        }
        if(fxgOrderSyncConfig.getUserWithoutADeadline() != null && !fxgOrderSyncConfig.getUserWithoutADeadline().isEmpty()){
            Map<String, String> userWithoutADeadline = fxgOrderSyncConfig.getUserWithoutADeadline();

            Map<String, Long> userWithoutADeadlineMap = new HashMap<>();
            logger.error(String.format("放心购diamond配置白名单店铺去A截止时间，sourceDate:%s。", JSONObject.toJSONString(userWithoutADeadline)));
            for(String key: userWithoutADeadline.keySet()){
                if(StringUtils.isBlank(key)){
                    continue;
                }
                String timeStr = userWithoutADeadline.get(key);
                try {
                    Date time = sf.parse(timeStr.trim());
                    userWithoutADeadlineMap.put(key.trim(), time.getTime()/1000);
                } catch (Exception e){
                    logger.error(String.format("放心购diamond配置白名单店铺去A截止时间转换出错，userId:%s, deadline:%s。", key, timeStr));
                    continue;
                }

            }
            //这里必须设置替换  否则无法关闭某个店铺
            fxgOrderSyncConfig.setUserWithoutADeadlineMap(userWithoutADeadlineMap);
        } else {
            logger.warn("放心购diamond配置白名单店铺去A截止时间为空");
        }

        if(StringUtils.isNotBlank(fxgOrderSyncConfig.getAllWithOutADeadlineTimeStr())){
            try {
                Date time = sf.parse(fxgOrderSyncConfig.getAllWithOutADeadlineTimeStr());
                fxgOrderSyncConfig.setAllWithOutADeadline(time.getTime()/1000);
            } catch (Exception e){
                logger.error(String.format("放心购diamond配置全部店铺去A截止时间转换出错,deadline:%s。", fxgOrderSyncConfig.getAllWithOutADeadlineTimeStr()));
            }
        } else {
            logger.warn("放心购diamond配置全部店铺去A截止时间为空");
        }
    }
}

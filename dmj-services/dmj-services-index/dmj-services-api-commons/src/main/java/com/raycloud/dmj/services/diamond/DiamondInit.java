package com.raycloud.dmj.services.diamond;

import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.dynamicproperty.DubboConsumerFilterConfig;
import com.raycloud.dmj.dynamicproperty.DynamicProperty;
import com.raycloud.dmj.dynamicproperty.DynamicPropertyCenter;
import com.raycloud.dmj.dynamicproperty.ShadowEnvConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

/**
 * DiamondInit
 *
 * <AUTHOR>
 * @since 2024/4/28 11:36 AM
 */
@Slf4j
@Component
public class DiamondInit implements InitializingBean {

    public Set<DiamondConfig> diamondConfigSet = new HashSet<DiamondConfig>() {{
        this.add(buildConfig("kmerp", "dubbo_consumer_config", DubboConsumerFilterConfig.class, dynamicProperty -> DynamicPropertyCenter.setDubboConsumerFilterConfig((DubboConsumerFilterConfig) dynamicProperty)));
        this.add(buildConfig("kmerp", "shadow_env_config", ShadowEnvConfig.class, dynamicProperty -> DynamicPropertyCenter.setShadowEnvConfig((ShadowEnvConfig) dynamicProperty)));

    }};

    @Override
    public void afterPropertiesSet() throws Exception {
        for (DiamondConfig diamondConfig : diamondConfigSet) {
            try {
                processConfigInfo(new DefaultDiamondManager(diamondConfig.getGroup(), diamondConfig.getDataId(), new ManagerListener() {

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        processConfigInfo(configInfo, diamondConfig);
                    }
                }).getAvailableConfigureInfomation(5000L), diamondConfig);
            } catch (Exception e) {
                log.error("handle {} error, errMsg: {}", diamondConfig, e.getMessage(), e);
            }
        }
    }

    private DiamondConfig buildConfig(String group, String dataId, Class<? extends DynamicProperty> clazz, Consumer<DynamicProperty> consumer) {
        return new DiamondConfig(group, dataId, clazz, consumer);
    }

    private void processConfigInfo(String configInfo, DiamondConfig diamondConfig) {
        try {
            if (StringUtils.isEmpty(configInfo)) {
                return;
            }
            DynamicProperty newDynamicProperty = diamondConfig.getClazz().getConstructor(String.class).newInstance(configInfo);
            newDynamicProperty.processConfigInfo(configInfo);
            diamondConfig.getConsumer().accept(newDynamicProperty);
        } catch (Exception e) {
            log.error("{} processConfigInfo{} error, errorMsg: {}", diamondConfig, configInfo, e.getMessage(), e);
        }

    }

    /**
     * DiamondConfig
     *
     * <AUTHOR>
     * @since 2024/4/28 11:47 AM
     */
    @Data
    public static class DiamondConfig {

        private String group;

        private String dataId;

        /**
         * 动态属性的 Class
         */
        private Class<? extends DynamicProperty> clazz;

        private Consumer<DynamicProperty> consumer;

        public DiamondConfig(String group, String dataId, Class<? extends DynamicProperty> clazz, Consumer<DynamicProperty> consumer) {
            this.group = group;
            this.dataId = dataId;
            this.clazz = clazz;
            this.consumer = consumer;
        }
    }
}
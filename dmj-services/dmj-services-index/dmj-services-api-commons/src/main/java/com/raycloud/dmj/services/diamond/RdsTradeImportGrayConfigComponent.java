package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.trade.RdsTradeImportGrayConfig;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * @description rds 订单同步灰度迁移白名单配置
 * <AUTHOR>
 * @date 2023/12/13 11:31
 */
@Component
public class RdsTradeImportGrayConfigComponent {
    private static final Logger logger = Logger.getLogger(RdsTradeImportGrayConfigComponent.class);


    private static DiamondManager diamondManager;

    @PostConstruct
    public void init(){
        try {
            if (diamondManager == null){
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_RDS_TRADE_IMPORT_GRAY_CONFIG, new ManagerListener(){

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            RdsTradeImportGrayConfig rdsTradeImportGrayConfig = JSONObject.parseObject(config, RdsTradeImportGrayConfig.class);
                            buildData(rdsTradeImportGrayConfig);
                            ConfigHolder.RDS_TRADE_IMPORT_GRAY_CONFIG = rdsTradeImportGrayConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_RDS_TRADE_IMPORT_GRAY_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_RDS_TRADE_IMPORT_GRAY_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_RDS_TRADE_IMPORT_GRAY_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                RdsTradeImportGrayConfig rdsTradeImportGrayConfig = JSONObject.parseObject(config, RdsTradeImportGrayConfig.class);
                buildData(rdsTradeImportGrayConfig);
                ConfigHolder.RDS_TRADE_IMPORT_GRAY_CONFIG = rdsTradeImportGrayConfig;
            }
        } catch (Exception e){
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_RDS_TRADE_IMPORT_GRAY_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

    private void buildData(RdsTradeImportGrayConfig config) {
        if (Objects.nonNull(config) && StringUtils.isNotBlank(config.getGraySources())) {
            Set<String> graySourceSet = ArrayUtils.toStringSet(config.getGraySources());
            config.setGraySourceSet(graySourceSet);
        }
    }

}

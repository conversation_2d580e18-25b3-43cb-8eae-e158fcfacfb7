package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FxgOrderSyncConfig;
import com.raycloud.dmj.domain.diamond.PaymentCalculateConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * 放心购订单同步配置
 * @Author: zhengbaifu
 * @Date: 2022/8/18
 */
@Component
public class PaymentCalculateConfigComponent {
    private static final Logger logger = Logger.getLogger(PaymentCalculateConfigComponent.class);

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.PAYMENT_CALCULATE, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            PaymentCalculateConfig configVO = JSONObject.parseObject(config, PaymentCalculateConfig.class);
                            buildData(configVO);
                            ConfigHolder.PAYMENT_CALCULATE_CONFIG = configVO;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.PAYMENT_CALCULATE + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.PAYMENT_CALCULATE + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.PAYMENT_CALCULATE + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                PaymentCalculateConfig syncConfig = JSONObject.parseObject(config, PaymentCalculateConfig.class);
                buildData(syncConfig);
                ConfigHolder.PAYMENT_CALCULATE_CONFIG = syncConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.PAYMENT_CALCULATE + ",出错:" + e.getMessage(), e);
        }
    }

    /**
     * 组装数据
     * @param configVO
     */
    private void buildData(PaymentCalculateConfig configVO){
        String taobaoTotalFeeToPaymentCompanyIds = configVO.getTaobaoTotalFeeToPaymentCompanyIds();
        if (StringUtils.isNotBlank(taobaoTotalFeeToPaymentCompanyIds)) {
            Set<String> sets = new HashSet<>(Arrays.asList(taobaoTotalFeeToPaymentCompanyIds.split(",")));
            configVO.setTaobaoTotalFeeToPaymentCompanyIdSet(sets);
            if (sets.contains("1")) {
                configVO.setTaobaoAllUseTotalFeeToPayment(true);
            }
        }else {
            configVO.setTaobaoTotalFeeToPaymentCompanyIdSet(Collections.emptySet());
            configVO.setTaobaoAllUseTotalFeeToPayment(false);
        }

        String jdDiscountFeeCalculateCompanyIds = configVO.getJdDiscountFeeCalculateCompanyIds();
        if (StringUtils.isNotBlank(jdDiscountFeeCalculateCompanyIds)) {
            Set<String> sets = new HashSet<>(Arrays.asList(jdDiscountFeeCalculateCompanyIds.split(",")));
            configVO.setJdDiscountFeeCalculateCompanyIdSet(sets);
            if (sets.contains("1")) {
                configVO.setJdAllUseDiscountFeeCalculate(true);
            }
        }else {
            configVO.setJdDiscountFeeCalculateCompanyIdSet(Collections.emptySet());
            configVO.setJdAllUseDiscountFeeCalculate(false);
        }

        String useGlobalScaleWhenAllocation = configVO.getUseGlobalScaleWhenAllocation();
        if (StringUtils.isNotBlank(useGlobalScaleWhenAllocation)) {
            configVO.setUseGlobalScaleWhenAllocationSet(Arrays.asList(useGlobalScaleWhenAllocation.split(",")));
        }

        if (StringUtils.isNotBlank(configVO.getUseMaxLength16())) {
            configVO.setUseMaxLength16Set(Arrays.asList(configVO.getUseMaxLength16().split(",")));
        }

    }
}

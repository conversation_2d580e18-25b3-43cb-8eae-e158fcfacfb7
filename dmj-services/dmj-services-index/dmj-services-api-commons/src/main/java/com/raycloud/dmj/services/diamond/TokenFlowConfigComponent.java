package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.tokenflow.TokenFlowConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Component
public class TokenFlowConfigComponent {

    private static final Logger logger = Logger.getLogger(TokenFlowConfigComponent.class);

    private static DiamondManager tokenFlowConfigDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (tokenFlowConfigDiamondManager == null) {
                tokenFlowConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATE_ID_TOKEN_FLOW, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.TOKEN_FLOW_CONFIG = JSONObject.parseObject(config, TokenFlowConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_TOKEN_FLOW + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_TOKEN_FLOW + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = tokenFlowConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_TOKEN_FLOW + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.TOKEN_FLOW_CONFIG = JSONObject.parseObject(config, TokenFlowConfig.class);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_TOKEN_FLOW + ",出错:" + e.getMessage(), e);
        }
    }

}

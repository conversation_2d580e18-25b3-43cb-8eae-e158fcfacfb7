package com.raycloud.dmj.services.domain;


/**
 * 商品日志的操作类型枚举类
 *
 * <AUTHOR>
 */
public enum ItemTraceActionEnum {

    /**
     * 商品相关
     */
    ITEM_UPDATE(10001, "商品信息修改"),
    ITEM_ADD(10002, "商品新增"),
    ITEM_INACTIVE(10003, "商品停用"),
    ITEM_DELETE(10004, "商品删除"),
    ITEM_RELATIONSHIP_DELETE(10005, "商品对应关系变更"),
    ITEM_PRINT(10006, "商品打印"),
    ITEM_STOCK_LABEL_ADD(10007, "新增库存标签"),
    ITEM_STOCK_LABEL_DELETE(10008, "删除库存标签"),
    ITEM_STOCK_LABEL_UPDATE(10009, "商品库存变更"),
    ITEM_UNIQUECODE_SHELF_OFF(10010, "唯一码下架"),
    ITEM_RECYCLE(10011, "商品回收"),
    ITEM_TAG_RELATION_ADD(10012, "商品新增标签"),
    ITEM_TAG_RELATION_UPDATE(10013, "商品变更标签"),
    ITEM_TAG_RELATION_DELETE(10014, "商品删除标签"),
    ITEM_TRANSFORM_NORMAL_TO_SUIT(10015, "普通转套件"),
    ITEM_TRANSFORM_NORMAL_TO_PROCESS(10016, "普通转加工"),
    ITEM_TRANSFORM_NORMAL_TO_GROUP(10017, "普通转组合"),
    ITEM_TRANSFORM_NORMAL_TO_SUIT_SKU(10018, "普通转套件(规格维度)"),
    ITEM_TRANSFORM_SUIT_TO_NORMAL_SKU(10019, "套件转普通(规格维度)"),
    ITEM_TRANSFORM_SUIT_TO_PROCESS_SKU(10020, "套件转加工(规格维度)"),
    ITEM_TRANSFORM_PROCESS_TO_SUIT_SKU(10021, "加工转套件(规格维度)"),
    ITEM_DOWNLOAD_ITEM(10022, "下载平台商品"),
    ITEM_MATCH_ITEM(10023, "匹配系统商品"),
    ITEM_UPDATE_TB_ITEM(10024, "更新平台商品"),
    ITEM_DELETE_TB_ITEM(10025, "删除平台商品"),
    ITEM_AUTO_CREATE_TB_ITEM(10026, "自动创建平台商品"),
    ITEM_IMPORT_TB_ITEM(10027, "导入平台商品"),
    ITEM_NEW_OUTER_ID(10028, "更换款号"),
    ITEM_TRANSFORM_NORMAL_TO_GROUP_SKU(10029, "普通转组合(规格维度)"),

    // 1688
    ITEM_ALI_BIND_PRODUCT(10100, "1688商品映射"),
    ITEM_ALI_RELEASE_PRODUCT(10101, "1688商品取消映射"),
    ITEM_ALI_DELETE_PRODUCT(10102, "1688商品删除"),
    // ITEM_ALI_CREATE_SUPPLIER(10103, "1688映射自动创建供应商"),


    /**
     * 订单相关
     */
    TRADE_ITEM_DELETE(20001, "删除订单商品"),
    TRADE_ITEM_ADD(20002, "增加订单商品"),
    TRADE_ITEM_UPDATE(20003, "修改订单商品信息"),
    TRADE_GIFT_ADD(20004, "增加订单赠品"),
    TRADE_GIFT_DELETE(20005, "删除订单赠品"),
    TRADE_CANCEL_AUDIT(20006, "取消审核订单"),

    /**
     * 波次相关
     */
    WAVE_PICK(30001, "拣选"),
    WAVE_PAPER_PICK(30002, "纸质拣货"),
    WAVE_PICK_CANCEL(30003, "取消拣选"),
    WAVE_PC_SEED(30004, "播种"),
    WAVE_PC_VALIDATE(30005, "验货"),
    WAVE_KICK(30006, "踢出波次"),
    DIRECT_SEND(30007, "直接发货"),
    WORKLOAD_SUPPLEMENT(30008, "工作量补录"),
    POST_PRINT(30040, "后置打印"),
    BLIND_SCAN(30009, "出库"),
    INSPECTION_OUTBOUND(30020, "验货出库"),
    PICKING_RETURN(30030, "拣选归还"),


    /**
     * 售后相关
     */
    AFTER_SALE_ITEM_RETURN(40001, "商品退货"),
    AFTER_SALE_ITEM_REDELIVER(40002, "商品补发"),

    /**
     * 仓储
     */
    WMS_REMOVE_GOODS_ALLOCATION(50001, "释放货位"),
    WMS_CHECK(50002, "盘点"),
    WMS_SHIFTING_PARKING(50003, "移库"),
    // WSS  working-storage section缩写
    WMS_WSS_STOCK_MOVE(50005, "平移暂存区库存"),
    WMS_WSS_STOCK_ADJUST(50006, "调整库存"),
    WMS_ALLOC_GOODS_ALLOCATION(50007, "分配货位"),
    WMS_STOCK_SET(50008, "修改库容"),
    WMS_STOCK_WARNING_SET(50009, "修改库容警戒值"),
    WMS_PROCESS_AND_OUTGOING(50010, "原料加工"),
    WMS_PROCESS_AND_INCOMING(50011, "加工生成"),
    WMS_PUT_ON(50012, "上架"),
    WMS_PUT_OFF(50013, "下架"),
    WMS_REPLENISHMENT(50014, "补货"),
    WMS_POSITION_CHECK(50015, "货位盘点"),
    WMS_MIXED_CHECK(50016, "混放盘点"),
    WMS_TASK_CHECK(50017, "盘点任务"),
    WMS_ITEM_MOVE(50018, "商品移库"),
    WMS_TASK_MOVE(50019, "移库任务"),
    WMS_FAST_MOVE(50020, "快速移库"),
    WMS_PDA_TAKE_OFF(50021, "库存下架"),
    WMS_PDA_ORDER_PICK(50022, "订单拣选"),

    WMS_PDA_ONE_PICK(50023, "一单一件"),
    WMS_PDA_MANY_PICK(50024, "一单多件"),
    WMS_PDA_PURCHASE_RETURN_PICK(50025, "采购退货"),
    WMS_PDA_REPLENISH_PICK(50026, "补货"),
    WMS_PDA_STALL_PICK(50027, "批发配货"),
    WMS_PDA_PRODUCT_ORDER_PICK(50028, "加工"),
    WMS_PDA_FREEDOM_PICK(50029, "自由拣"),
    WAVE_PDA_DOWN_SHELF(50060, "下架"),

    WMS_PDA_REPLENISH_TASK(50030, "补货任务"),

    WMS_PDA_FAST_SHELVE(50031, "快速上架"),
    WMS_PDA_SHELVE_V2(50032, "上架-V2"),
    WMS_PDA_PRODUCT_ORDER_SHELVE(50033, "加工上架"),
    WMS_PDA_MULTI_SHELVE(50034, "混放上架"),
    WMS_PDA_PICK_BACK(50035, "拣选归还"),
    WMS_PDA_AFTER_SALE_SHELVE(50036, "售后快速上架"),
    WMS_PDA_OTHER_SHELVE(50037, "其他上架"),
    WMS_PDA_PURCHASE_SHELVE(50038, "入库上架"),
    WMS_PDA_DEFECTIVE_SHELVE(50039, "次品上架"),
    WMS_PDA_REFUND_SHELVE_GOOD(50040, "销退上架，良品"),
    WMS_PDA_REFUND_SHELVE_DEFECTIVE(50041, "销退上架，次品"),
    WMS_PDA_REPLENISH_ITEM_SHELVE(50042, "补货上架（按商品）"),
    WMS_PDA_REPLENISH_WAVE_SHELVE(50043, "补货上架（按波次）"),
    WMS_PDA_COMMON_SHELVE_GOOD(50044, "普通上架，良品"),
    WMS_PDA_COMMON_SHELVE_DEFECTIVE(50045, "普通上架，次品"),
    WMS_PDA_STALL_V2_PICK(50046, "档口配货"),
    WMS_PDA_RETURN_SHELVE_GOOD(50047, "采退上架，良品"),
    WMS_PDA_REFUND_SHELVE_BATCH(50048, "批量销退上架"),
    WMS_PDA_REFUND_SHELVE(50049, "销退上架"),

    WMS_WSS_STOCK_CLEAR_PURCHASE(50050, "清零入库暂存区库存"),
    WMS_WSS_STOCK_CLEAR_REFUND(50051, "清零销退暂存区库存"),
    WMS_WSS_STOCK_CLEAR_PICK(50052, "清零拣货暂存区库存"),
    WMS_WSS_STOCK_CLEAR_COMMON(50053, "清零通用暂存区库存"),
    WMS_WSS_STOCK_CLEAR_REPLENISH(50054, "清零补货暂存区库存"),
    WMS_WSS_STOCK_CLEAR_DEFECTIVE(50055, "清零次品暂存区库存"),
    WMS_WSS_STOCK_CLEAR_RETURN(50056, "清零采退暂存区库存"),

    WMS_PACKING_GOODS_RECORD(50057, "装袋贴标登记"),

    WMS_PDA_RETURN_SHELVE(50069, "采退上架"),
    WMS_PDA_COMMON_SHELVE(50070, "普通上架"),

    WMS_PDA_PICK_SHELVE(50080, "拣选上架"),
    WMS_ALLOCATE_OUT(50081, "调拨出库"),
    WMS_ALLOCATE_IN(50082, "调拨入库"),
    WMS_PDA_COMMON_DOWN_SHELF(50090, "普通下架"),
    WMS_DELETE_MOVE_TASK(50085, "删除移库任务"),
    WMS_BATCH_MOVE_TASK(50086, "批量移库"),


    /**
     * 采购相关
     */
    PURCHASE_ACCEPT(60001, "采购收货"),
    PURCHASE_NO_BILL_ACCEPT(60002, "无单收货"),
    PURCHASE_RETURN_ACCEPT(60003, "采退收货"),
    PURCHASE_QUALITY_TESTING(60004, "质检"),
    PURCHASE_FAST_ACCEPT(60005, "快速收货"),
    PURCHASE_FASt_PUT_ON(60006, "直接采购上架"),
    PURCHASE_RETURN(60007, "采购退货"),
    PURCHASE_CHELVE(60008, "按单上架"),
    PDA_ALLOCATE_RECEIPT_PLUS(60012, "调拨收货"),
    PURCHASE_RETURN_SCAN(60009, "扫描采退"),

    /**
     * 库存相关
     */
    STOCK_SYNC(70001, "库存同步"),
    STOCK_VIRTUAL_STOCK_UPDATE(70002, "虚拟库存修改"),
    STOCK_PREDICT_PREPARE_GOODS_NUM_UPDATE(70003, "预计备货数修改"),
    STOCK_AUTOMATIC_UPLOAD_SET(70004, "设置自动上传"),
    STOCK_CHECK(70005, "盘点"),
    STOCK_WARNING_SET(70006, "库存警戒设置"),
    STOCK_ALLOT(70007, "库存调拨"),
    STOCK_PROCESS_INCOMING(70008, "库存加工入库"),
    STOCK_PROCESS_OUTGOING(70009, "库存加工出库"),
    STOCK_UPLOAD(70010, "库存上传设置"),
    OTHER_IN(70011, "其它入库"),
    OTHER_OUT(70012, "其它出库"),

    /**
     * 批发开单
     */
    WHOLESALE_PREPARE_GOODS(80001, "批发开单配货"),
    STOCK_RETURN_GOODS(80002, "批发开单退货");


    /**
     * code
     */
    private Integer code;
    /**
     * name
     */
    private String name;


    ItemTraceActionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

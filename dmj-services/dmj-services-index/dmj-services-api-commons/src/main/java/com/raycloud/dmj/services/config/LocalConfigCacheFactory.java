package com.raycloud.dmj.services.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * guava本地缓存工厂类
 * <AUTHOR>
 * @date 2024/9/2 16:04
 */
public class LocalConfigCacheFactory<K, V> {

    private static Cache cache;

    public static <K, V>Cache getInstance() {
        if (Objects.isNull(cache)) {
            synchronized (Cache.class) {
                if (Objects.isNull(cache)) {
                    cache = CacheBuilder.newBuilder()
                            .initialCapacity(30)
                            .concurrencyLevel(10)
                            .expireAfterWrite(1, TimeUnit.MINUTES)
                            .build();
                }
            }
        }
        return cache;
    }

}

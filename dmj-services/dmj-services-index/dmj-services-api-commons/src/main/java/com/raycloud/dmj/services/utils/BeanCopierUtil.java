package com.raycloud.dmj.services.utils;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.beans.BeanCopier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * bean copy 工具类
 *
 * <AUTHOR>
 */
public class BeanCopierUtil {

    /**
     * 创建过的BeanCopier实例放到缓存中，下次可以直接获取，提升性能
     */
    static final Map<String, BeanCopier> BEAN_COPIERS = new HashMap<>();

    public static void copy(Object srcObj, Object destObj) {
        String key = genKey(srcObj.getClass(), destObj.getClass());
        BeanCopier copier = null;
        if (!BEAN_COPIERS.containsKey(key)) {
            copier = BeanCopier.create(srcObj.getClass(), destObj.getClass(), false);
            BEAN_COPIERS.put(key, copier);
        } else {
            copier = BEAN_COPIERS.get(key);
        }
        copier.copy(srcObj, destObj, null);
    }

    /**
     * 自定义copy
     *
     * @param srcObj   srcObj
     * @param destObj  destObj
     * @param consumer consumer
     * @param <T>      T
     * @param <R>      R
     */
    public static <T, R> void copy(T srcObj, R destObj, BiConsumer<T, R> consumer) {
        copy(srcObj, destObj);
        consumer.accept(srcObj, destObj);
    }

    private static String genKey(Class<?> srcClazz, Class<?> destClazz) {
        return srcClazz.getName() + destClazz.getName();
    }

    public static <T> List<T> copyBeanList(List<T> srcList, Class<T> targetClass) {
        List<T> targetBeanList = new ArrayList<>();
        if (CollectionUtils.isEmpty(srcList)) {
            return targetBeanList;
        }
        try {
            for (Object srcObj : srcList) {
                T targetBean = targetClass.newInstance();
                copyBean(srcObj, targetBean);
                targetBeanList.add(targetBean);
            }
        } catch (Exception e) {
            throw new RuntimeException("对象拷贝异常", e);
        }
        return targetBeanList;
    }

    public static <T> T copyBean(T srcObj, Class<T> targetClass) {
        try {
            T targetBean = targetClass.newInstance();
            copyBean(srcObj, targetBean);
            return targetBean;
        } catch (Exception e) {
            throw new RuntimeException("对象拷贝异常", e);
        }
    }

    public static <T,S> List<T> copyPropertiesList(List<S> srcList, Class<T> targetClass) {
        List<T> targetBeanList = new ArrayList<>();
        if (CollectionUtils.isEmpty(srcList)) {
            return targetBeanList;
        }
        try {
            for (Object srcObj : srcList) {
                T targetBean = targetClass.newInstance();
                copyBean(srcObj, targetBean);
                targetBeanList.add(targetBean);
            }
        } catch (Exception e) {
            throw new RuntimeException("对象拷贝异常", e);
        }
        return targetBeanList;
    }

    public static void copyBean(Object srcBean, Object targetBean) {
        if (srcBean == null || targetBean == null) {
            return;
        }
        BeanUtils.copyProperties(srcBean, targetBean);
    }
}

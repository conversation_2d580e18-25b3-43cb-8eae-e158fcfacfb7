package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.trade.TradeSaveSyncRematchConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27 15:05
 */
@Component
public class TradeSaveSyncRematchConfigComponent {
    private static final String GROUP_ID = DiamondConfig.DIAMOND_GROUP_ID;
    private static final String TRADE_SAVE_SYNC_REMATCH_CONFIG = DiamondConfig.TRADE_SAVE_SYNC_REMATCH_CONFIG;
    private static DiamondManager newDiamondManager;
    @PostConstruct
    public void init() {
        try {
            if (newDiamondManager == null) {
                newDiamondManager = new DefaultDiamondManager(GROUP_ID, TRADE_SAVE_SYNC_REMATCH_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }
                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.TRADE_SAVE_SYNC_REMATCH_CONFIG = JSONObject.parseObject(config, TradeSaveSyncRematchConfig.class);
                            Logs.ifDebug("更新TRADE_SAVE_SYNC_REMATCH_CONFIG配置group=" + GROUP_ID + ",dataId=" + TRADE_SAVE_SYNC_REMATCH_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            Logs.error("更新TRADE_SAVE_SYNC_REMATCH_CONFIG配置group=" + GROUP_ID + ",dataId=" + TRADE_SAVE_SYNC_REMATCH_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = newDiamondManager.getAvailableConfigureInfomation(5000L);
                Logs.ifDebug("拉取TRADE_SAVE_SYNC_REMATCH_CONFIG配置group=" + GROUP_ID + ",dataId=" + TRADE_SAVE_SYNC_REMATCH_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.TRADE_SAVE_SYNC_REMATCH_CONFIG = JSONObject.parseObject(config, TradeSaveSyncRematchConfig.class);
            }
        } catch (Exception e) {
            Logs.error("初始化TRADE_SAVE_SYNC_REMATCH_CONFIG配置group=" + GROUP_ID + ",dataId=" + TRADE_SAVE_SYNC_REMATCH_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }
}

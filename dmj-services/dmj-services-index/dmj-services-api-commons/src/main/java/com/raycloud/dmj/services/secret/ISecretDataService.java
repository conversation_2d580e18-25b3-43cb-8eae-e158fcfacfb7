package com.raycloud.dmj.services.secret;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/8 16:23
 */
public interface ISecretDataService {

    /**
     * 将单个明文加密成MD5
     */
    String encodeMD5(String content);

    /**
     * 将单个明文加密成可逆密文
     * 密文不会再次加密
     */
    String encodeSecret(String content);

    /**
     * 解密单个密文
     * 明文不会再次解密
     */
    String decodeSecret(String content);

    /**
     * 批量加密明文，明文和返回的密文下标一一对应
     * 密文不会再次加密
     */
    List<String> batchEncodeSecret(List<String> contentList);

    /**
     * 批量解密密文，密文和返回的明文下标一一对应
     * 明文不会再次解密
     */
    List<String> batchDecodeSecret(List<String> contentList);
}

package com.raycloud.dmj.services.context;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * user: hj
 * time: 2019/02/18 17:40
 * desc:
 */
@Service
public class ProjectContext implements IProjectContext, InitializingBean {

    @Value("${project.profile:}")
    private String projectProfile;

    @Override
    public String getProjectProfile() {

        return projectProfile;
    }

    @Override
    public void afterPropertiesSet(){
        EnvHolder.ENV = projectProfile;
        System.out.println(String.format("project.profile=[%s]", projectProfile));
    }
}

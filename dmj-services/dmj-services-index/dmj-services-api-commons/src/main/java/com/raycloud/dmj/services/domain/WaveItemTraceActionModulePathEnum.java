package com.raycloud.dmj.services.domain;

/**
 * 商品日志的获取操作路径枚举类
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/5/11.
 */
public enum WaveItemTraceActionModulePathEnum {

    /**
     * 波次相关
     */
    //直接拣选
    WAVE_PICK_BY_WAVEMANAGE(30001, "人工拣选商品","/trade/wavemanage/","wave_pick_by_wave_manage"),
    WAVE_PICK_BY_WAVEPRINT(30001, "人工拣选商品","/trade/waveprint/","wave_pick_by_wave_print"),
    WAVE_PICKING_PAPER_BEGIN(30002, "纸质拣选商品","/trade/pickWave/","wave_picking_paper_begin"),
    WAVE_PICK_CANCEL_BY_WAVEMANAGE(30003, "取消拣选任务","/trade/wavemanage/","wave_pick_cancel_by_wave_manage"),
    WAVE_PICK_CANCEL_BY_WAVEPRINT(30003, "取消拣选任务","/trade/waveprint/","wave_pick_cancel_by_wave_print"),
    PRINT_SEED(30004, "商品播种","/trade/sowprint/","print_seed"),
    POST_PRINT_SEED_CANCEL(30004, "重新播种","/trade/sowprint/","post_print_seed_cancel"),
    POST_PRINT_SEED_CHECK_COVER(30004, "复验播种","/trade/sowprint/","post_print_seed_check_cover"),
    WAVE_PC_VALIDATE_BY_PACKAGE(30005, "包装验货成功","/trade/package/","package"),
    WAVE_PC_VALIDATE_BY_POSTPRINT(30005, "商品验货成功","/trade/postprint/","wave_pc_validate_by_postprint"),
    DIRECT_SEND_BY_WAVEMANAGE(30007, "直接发货","/trade/wavemanage/","direct_send_by_wave_manage"),
    DIRECT_SEND_BY_WAVEPRINT(30007, "直接发货","/trade/waveprint/","direct_send_by_wave_print"),
    TRADE_OUT_WAVE(30006, "商品踢出波次","/tradeNew/manage/","trade_out_wave");

    /**
     * code
     */
    private Integer code;
    /**
     * name
     */
    private String name;
    /**
     * 页面路径
     */
    private String path;
    /**
     * 展示路径
     */
    private String operatePath;

    public String getOperatePath() {
        return operatePath;
    }

    public void setOperatePath(String operatePath) {
        this.operatePath = operatePath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    WaveItemTraceActionModulePathEnum(Integer code, String name,String path,String operatePath) {
        this.code = code;
        this.name = name;
        this.path = path;
        this.operatePath = operatePath;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getOperatePathByCodeAndPath(Integer code,String path) {
        for (WaveItemTraceActionModulePathEnum pathEnum:WaveItemTraceActionModulePathEnum.values()) {
            if (pathEnum.code.equals(code) && pathEnum.path.equals(path)){
                return pathEnum.getOperatePath();
            }
        }
        return "";
    }
}

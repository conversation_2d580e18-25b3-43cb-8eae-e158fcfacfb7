package com.raycloud.dmj.services.analyse;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * user: hj
 * time: 2020/08/12 15:50
 * desc: 单个参数传递多个值的解析器, 比如传递多个商家编码
 *
 * 使用场景: 有些用户要查询多个，如果是前端用逗号隔开的形式或者其他的形式，可能会跟原有的值碰撞
 * 故使用json传的方式，让后端进行解析
 * 格式如下：
 * [
 *     "FH808A-1-TX1203-00205",
 *     "FH808A-1-TX1203-00206"
 * ]
 */
public class ParamsMulValueAnalyser {

    private static Logger logger = LoggerFactory.getLogger(ParamsMulValueAnalyser.class);

    public static List<String> analyse(String text) {
        if (StringUtils.isEmpty(text)) {
            return new ArrayList<>();
        }

        try {
            return JSON.parseArray(text, String.class);
        } catch (Exception e) {
            logger.error("解析报错:"+text, e);
        }

        return new ArrayList<>();
    }
}

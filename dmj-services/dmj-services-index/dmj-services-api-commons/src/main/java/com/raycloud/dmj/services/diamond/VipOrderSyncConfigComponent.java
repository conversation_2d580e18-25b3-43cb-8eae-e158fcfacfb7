package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.VipOrderSyncConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * @description 唯品会订单同步配置
 * <AUTHOR>
 * @date 2022 8/23 17:40
 */
@Component
public class VipOrderSyncConfigComponent {

    private static final Logger logger = Logger.getLogger(VipOrderSyncConfigComponent.class);

    private static DiamondManager vipOrderSyncDiamondManager;

    @PostConstruct
    public void init(){
        try {
            if (vipOrderSyncDiamondManager == null){
                vipOrderSyncDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_VIP_ORDER_SYNC, new ManagerListener(){

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            VipOrderSyncConfig vipOrderSyncConfig = JSONObject.parseObject(config, VipOrderSyncConfig.class);
                            buildData(vipOrderSyncConfig);
                            ConfigHolder.VIP_ORDER_SYNC_CONFIG = vipOrderSyncConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_VIP_ORDER_SYNC + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_VIP_ORDER_SYNC + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = vipOrderSyncDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_VIP_ORDER_SYNC + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                VipOrderSyncConfig syncConfig = JSONObject.parseObject(config, VipOrderSyncConfig.class);
                buildData(syncConfig);
                ConfigHolder.VIP_ORDER_SYNC_CONFIG = syncConfig;
            }
        } catch (Exception e){
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_VIP_ORDER_SYNC + ",出错:" + e.getMessage(), e);
        }
    }

    /**
     * 组装数据
     * @param vipOrderSyncConfig
     */
    private void buildData(VipOrderSyncConfig vipOrderSyncConfig) {
        String exceptHandlerCompanyIds = vipOrderSyncConfig.getCooperationNoExceptCompanyIds();
        if (StringUtils.isNotEmpty(exceptHandlerCompanyIds) && "ALL".equalsIgnoreCase(exceptHandlerCompanyIds.trim())) {
            vipOrderSyncConfig.setAllUseCooperationNoExceptHandler(true);
        } else {
            Set<String> sets = new HashSet<>(Arrays.asList(exceptHandlerCompanyIds.split(",")));
            vipOrderSyncConfig.setCooperationNoExceptCompanyIdSet(sets);
        }
    }

}

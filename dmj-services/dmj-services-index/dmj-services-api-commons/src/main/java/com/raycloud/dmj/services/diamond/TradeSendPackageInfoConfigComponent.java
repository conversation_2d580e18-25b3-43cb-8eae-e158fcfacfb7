package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.domian.TradeSendPackageInfoConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-07-02
 */
@Component
public class TradeSendPackageInfoConfigComponent {

    private static final Logger logger = Logger.getLogger(TradeSendPackageInfoConfigComponent.class);

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.TRADE_SEND_PACKAGE_INFO_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            TradeSendPackageInfoConfig bean = JSONObject.parseObject(config, TradeSendPackageInfoConfig.class);
                            bean.initData();
                            ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG = bean;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_SEND_PACKAGE_INFO_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_SEND_PACKAGE_INFO_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_SEND_PACKAGE_INFO_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                TradeSendPackageInfoConfig bean = JSONObject.parseObject(config, TradeSendPackageInfoConfig.class);
                bean.initData();
                ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG = bean;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_SEND_PACKAGE_INFO_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

}

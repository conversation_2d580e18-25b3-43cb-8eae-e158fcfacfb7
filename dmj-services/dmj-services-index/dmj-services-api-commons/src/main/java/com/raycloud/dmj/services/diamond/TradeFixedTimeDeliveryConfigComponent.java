package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.trade.TradeFixedTimeDeliveryConfig;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * @description
 * <AUTHOR>
 * @date 2023/10/25 11:37
 */
@Component
public class TradeFixedTimeDeliveryConfigComponent {

    private static final Logger logger = Logger.getLogger(TradeFixedTimeDeliveryConfigComponent.class);

    private static DiamondManager tradeFixedTimeDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (tradeFixedTimeDiamondManager == null) {
                tradeFixedTimeDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.TRADE_FIXED_TIME_DELIVERY_CONFIG, new ManagerListener() {

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            TradeFixedTimeDeliveryConfig tradeFixedTimeDeliveryConfig = JSONObject.parseObject(config, TradeFixedTimeDeliveryConfig.class);
                            buildData(tradeFixedTimeDeliveryConfig);
                            ConfigHolder.TRADE_FIXED_TIME_DELIVERY_CONFIG = tradeFixedTimeDeliveryConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_FIXED_TIME_DELIVERY_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_FIXED_TIME_DELIVERY_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = tradeFixedTimeDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_FIXED_TIME_DELIVERY_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                TradeFixedTimeDeliveryConfig tradeFixedTimeDeliveryConfig = JSONObject.parseObject(config, TradeFixedTimeDeliveryConfig.class);
                buildData(tradeFixedTimeDeliveryConfig);
                ConfigHolder.TRADE_FIXED_TIME_DELIVERY_CONFIG = tradeFixedTimeDeliveryConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_FIXED_TIME_DELIVERY_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

    private void buildData(TradeFixedTimeDeliveryConfig config) {
        String handleFixedTimeDeliveryCompanyIds = config.getHandleFixedTimeDeliveryCompanyIds();
        if (StringUtils.isNotBlank(handleFixedTimeDeliveryCompanyIds)) {
            config.setHandleFixedTimeDeliveryCompanyIdSet(ArrayUtils.toLongSet(handleFixedTimeDeliveryCompanyIds));
        }
    }
}

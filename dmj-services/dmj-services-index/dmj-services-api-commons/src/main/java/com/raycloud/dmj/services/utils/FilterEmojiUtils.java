package com.raycloud.dmj.services.utils;

import com.raycloud.dmj.domain.account.Staff;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;


/**
 * 过滤emoji表情
 */
public class FilterEmojiUtils {
    public static Logger logger = Logger.getLogger(FilterEmojiUtils.class);

    private static boolean containsEmoji(String source) {
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char hs = source.charAt(i);
            if(isEmojiCharacter(hs)){
                return true;
            }
        }
        return false;
    }

    /**
     *  是否是表情符字符  非表情字符 false， 表情符 true
     * @param codePoint
     * @return
     */
    private static boolean isEmojiCharacter(char codePoint) {
        Boolean isFalse= (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
        return !isFalse;

    }

    /**
     * 过滤emoji 或者 其他非文字类型的字符
     *
     * @param source
     * @return
     */
    private static String filterEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return source;
        }
        StringBuilder buf =  new StringBuilder();
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isEmojiCharacter(codePoint)) {
                buf.append(codePoint);
            }
        }
        if (buf.length()==0) {
            return source;
        } else {
            return buf.toString();
        }
    }



    public static  String emojiToStr(Staff staff, String str){
        if(StringUtils.isBlank(str)){
            return str;
        }
        if(containsEmoji(str)){
            String s = filterEmoji(str);
            String log=String.format("companyId=%s  原字符串 %s 包含表情符，替换表情符号后 %s！",staff.getCompanyId(),str,s);
            logger.info(log);
            return s;
        }
        return str;
    }
}

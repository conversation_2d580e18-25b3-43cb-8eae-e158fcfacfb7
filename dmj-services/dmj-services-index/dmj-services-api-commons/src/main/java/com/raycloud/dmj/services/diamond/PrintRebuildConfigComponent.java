package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.PrintRebuildConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * 打印优化重构配置
 *
 * @Date 2021/11/3
 * <AUTHOR>
 */
@Slf4j
@Component
public class PrintRebuildConfigComponent {

    private static DiamondManager printRebuildConfigDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (printRebuildConfigDiamondManager == null) {
                printRebuildConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PRINT_REBUILD_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        try {
                            log.info("更新diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PRINT_REBUILD_CONFIG, config);
                            convertConfig(config);
                        } catch (Exception e) {
                            log.error("更新diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PRINT_REBUILD_CONFIG, e);
                        }
                    }
                });

                String config = printRebuildConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                log.info("拉取diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PRINT_REBUILD_CONFIG, config);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_PRINT_REBUILD_CONFIG, e);
        }
    }

    private static void convertConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return;
        }
        ConfigHolder.PRINT_REBUILD_CONFIG = JSONObject.parseObject(config, PrintRebuildConfig.class);
    }

}

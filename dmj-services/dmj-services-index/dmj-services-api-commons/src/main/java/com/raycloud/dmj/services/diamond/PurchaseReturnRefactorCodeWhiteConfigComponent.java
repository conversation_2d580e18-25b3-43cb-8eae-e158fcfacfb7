package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.basis.CompanyInfoVO;
import com.raycloud.dmj.domain.basis.params.GroupCompanyInfoVO;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 采退代码重构之后的公司白名单配置信息
 */
@Slf4j
@Component
public class PurchaseReturnRefactorCodeWhiteConfigComponent {

    private static DiamondManager purchaseReturnRefactorCodeWhiteConfigComponent;

    @PostConstruct
    public void init() {
        try {
            if (purchaseReturnRefactorCodeWhiteConfigComponent == null) {
                purchaseReturnRefactorCodeWhiteConfigComponent = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID,
                        DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        convertConfig(config);
                    }
                });

                String config = purchaseReturnRefactorCodeWhiteConfigComponent.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG + ", 出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (config.isEmpty()) {
            log.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG + ", config is null or empty");
            return;
        }

        List<GroupCompanyInfoVO> companyList;
        try {
            companyList = JSONObject.parseArray(config, GroupCompanyInfoVO.class);
        } catch (Exception e) {
            log.error("json转实体对象失败, diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID
                    + ", dataId = " + DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG
                    + "配置失败, 原因:" + e.getMessage(), e);
            return;
        }
        if (CollectionUtils.isEmpty(companyList))
        {
            //输出日志，没有配置白名单规则
            log.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG
                    + ", config is null or empty and json convert model result is empty");
            return;
        }
        //将每个业务对应的公司转换成map，方便后期白名单判断
        companyList.forEach(businessInfo->{
            Map<Long, CompanyInfoVO> companyInfoVOMap = businessInfo.getCompanyList().stream()
                    .collect(Collectors.toMap(CompanyInfoVO::getCompanyId, companyInfoVO -> companyInfoVO));
            ConfigHolder.PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG.put(businessInfo.getBusinessCode(), companyInfoVOMap);
        });
        log.info("更新diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                + DiamondConfig.DATA_ID_PURCHASE_RETURN_REFACTOR_CODE_WHITE_CONFIG + ", config = " + config);
    }

}

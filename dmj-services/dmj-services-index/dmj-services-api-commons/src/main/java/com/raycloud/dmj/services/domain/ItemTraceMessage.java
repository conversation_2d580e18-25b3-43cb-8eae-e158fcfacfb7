package com.raycloud.dmj.services.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商品跟踪记录消息实体
 *
 * <AUTHOR>
 */
public class ItemTraceMessage implements Serializable {

    private static final long serialVersionUID = -8484139049552935656L;

    /**
     * 公司Id
     */
    private Long companyId;

    /**
     * 系统商品ID
     */
    private Long sysItemId;

    /**
     * 系统规格ID
     */
    private Long sysSkuId;
    /**
     * 规格商家编码
     */
    private String outerId;

    /**
     * 主商家编码
     */
    private String itemOuterId;

    /**
     * 关联单据类型
     * {@link ItemTraceBillTypeEnum}
     * 例子: 波次
     * ItemTraceBillTypeEnum.WAVE.getCode();
     */
    private Integer billType;

    /**
     * 关联单据号
     */
    private String billNo;

    /**
     * 操作的商品数量
     */
    private Integer num;

    /**
     * 哪个操作
     * {@link ItemTraceActionEnum}
     * 例子: ItemTraceActionEnum.ITEM_UPDATE.getCode();
     */
    private Integer action;

    /**
     * 备注/操作内容
     */
    private String remark;

    /**
     * 关联货位ID
     */
    private Long sectionId;

    /**
     * 仓位
     */
    private String sectionCode;

    /**
     * 操作人ID
     */
    private Long staffId;

    /**
     * 操作人
     */
    private String staffName;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作路径
     * 获取方式： 请求头中的module_path
     */
    private String operatePath;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private String productTime;


    /**
     * 到期日期
     */
    private String expireDate;

    /**
     * 跟踪ID
     */
    private Long clueId;

    /**
     * 平台商品id
     */
    private String numIid;

    /**
     * 平台规格Id
     */
    private String skuId;

    /**
     * 店铺id
     */
    private Long userId;

    /**
     * 唯一码
     */
    private String uniqueCode;

    /**
     * https://gykj.yuque.com/entavv/xb9xi5/hv8zc8zsrm5qvqmu
     * 这里有多个的话需要平铺开来，一个唯一码塞到一个message里
     */
    private List<String> uniqueCodeList;

    /**
     * 货主名称
     */
    private String shipper;

    /**
     * 货主id
     */
    private String shipperId;

    public ItemTraceMessage(){

    }

    public ItemTraceMessage(Long companyId, String numIid){
        this.companyId = companyId;
        this.numIid = numIid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getItemOuterId() {
        return itemOuterId;
    }

    public void setItemOuterId(String itemOuterId) {
        this.itemOuterId = itemOuterId;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public String getOperatePath() {
        return operatePath;
    }

    public void setOperatePath(String operatePath) {
        this.operatePath = operatePath;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProductTime() {
        return productTime;
    }

    public void setProductTime(String productTime) {
        this.productTime = productTime;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getNumIid() {
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public List<String> getUniqueCodeList() {
        return uniqueCodeList;
    }

    public void setUniqueCodeList(List<String> uniqueCodeList) {
        this.uniqueCodeList = uniqueCodeList;
    }

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    @Override
    public String toString() {
        return "ItemTraceMessage{" +
                "companyId=" + companyId +
                ", sysItemId=" + sysItemId +
                ", sysSkuId=" + sysSkuId +
                ", outerId='" + outerId + '\'' +
                ", itemOuterId='" + itemOuterId + '\'' +
                ", billType=" + billType +
                ", billNo='" + billNo + '\'' +
                ", num=" + num +
                ", action=" + action +
                ", remark='" + remark + '\'' +
                ", sectionId=" + sectionId +
                ", sectionCode='" + sectionCode + '\'' +
                ", staffId=" + staffId +
                ", staffName='" + staffName + '\'' +
                ", operateTime=" + operateTime +
                ", operatePath='" + operatePath + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", clueId=" + clueId +
                "，batchNo=" + batchNo +
                ", productTime=" + productTime +
                ", expireDate=" + expireDate +
                '}';
    }
}

package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FxgPrintConfig;
import com.raycloud.dmj.domain.enums.YesNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 放心购打印配置
 *
 * @Date 2021/7/20
 * <AUTHOR>
 */
@Slf4j
@Component
public class FxgPrintConfigComponent {

    private static DiamondManager fxgPrintConfigDiamondManager;

    private static String SPLITTER = "_";

    private static String SPLITTER_OF_COMPANY = ",";

    private static String SWITCH_ON = DiamondConfig.SWITCH_ON + SPLITTER;

    private static String SWITCH_OFF = DiamondConfig.SWITCH_OFF + SPLITTER;

    @PostConstruct
    public void init() {
        try {
            if (fxgPrintConfigDiamondManager == null) {
                fxgPrintConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_PRINT_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        try {
                            log.info("更新diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_PRINT_CONFIG, config);
                            convertConfig(config);
                        } catch (Exception e) {
                            log.error("更新diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_PRINT_CONFIG, e);
                        }
                    }
                });

                String config = fxgPrintConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                log.info("拉取diamond配置group={} ,dataId={}, config={}", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_PRINT_CONFIG, config);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置group={} ,dataId={} 配置失败", DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FXG_PRINT_CONFIG, e);
        }
    }

    private static void convertConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return;
        }
        FxgPrintConfig fxgPrintConfig = JSONObject.parseObject(config, FxgPrintConfig.class);
        String companyIdStr = StringUtils.defaultString(fxgPrintConfig.getCompanyIdList());

        // 判断配置信息是否以"on_"开始 ？ 开启白名单配置 ： 停用白名单配置
        fxgPrintConfig.setEnableStatus(companyIdStr.startsWith(SWITCH_ON) ?
                YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());

        // 截取开关前缀
        companyIdStr = companyIdStr.replace(SWITCH_ON, "").replace(SWITCH_OFF, "");

        // 解析公司id集合
        if (StringUtils.isNotBlank(companyIdStr)) {
            fxgPrintConfig.setCompanyIdSet(Arrays.asList(companyIdStr.split(SPLITTER_OF_COMPANY)).stream()
                    .map(i -> Long.valueOf(i.trim()))
                    .collect(Collectors.toSet()));
        }
        ConfigHolder.FXG_PRINT_CONFIG = fxgPrintConfig;
    }

}

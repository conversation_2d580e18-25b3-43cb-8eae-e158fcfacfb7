package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.consign.UploadServiceWhitelistConfig;
import com.raycloud.dmj.services.context.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.diamond.consign.UploadServiceWhitelistConfig.ALL;


/**
 * UploadServiceWhitelistConfigComponent
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Component
public class UploadServiceWhitelistConfigComponent {

    private static final String GROUP_ID =  DiamondConfig.DIAMOND_GROUP_ID;
    private static final String DATA_ID =  DiamondConfig.UPLOAD_SERVICE_WHITELIST_CONFIG;

    private static DiamondManager diamondManager;

    @Resource
    private ProjectContext projectContext;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(GROUP_ID, DATA_ID, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.UPLOAD_SERVICE_WHITELIST_CONFIG.setWhitelists(parseWhitelist(config));
                            Logs.ifDebug("更新diamond配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",config=" + config);
                        } catch (Exception e) {
                            Logs.error("更新diamond配置group=" + GROUP_ID + ",dataId=" + DATA_ID + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                Logs.ifDebug("拉取diamond配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.UPLOAD_SERVICE_WHITELIST_CONFIG.setWhitelists(parseWhitelist(config));
            }
        } catch (Exception e) {
            Logs.error("初始化diamond配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",出错:" + e.getMessage(), e);
        }
    }

    private List<UploadServiceWhitelistConfig.Whitelist> parseWhitelist(String config) {
        if (StringUtils.isBlank(config)) {
            return Lists.newArrayList();
        }
        return JSONObject.parseArray(config, UploadServiceWhitelistConfig.Whitelist.class).stream()
                .filter(c -> CollectionUtils.containsAny(c.getEnvs(), Lists.newArrayList(ALL, projectContext.getProjectProfile()))) // 只加载当前环境所需的配置
                .collect(Collectors.toList());
    }
}

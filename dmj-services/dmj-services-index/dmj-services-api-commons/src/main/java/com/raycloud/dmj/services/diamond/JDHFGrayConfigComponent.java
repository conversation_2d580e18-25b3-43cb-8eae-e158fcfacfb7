package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.JdHufuGrayConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * @Author: chenchaochao
 * @Date: 2020/4/23 2:47 下午
 */
@Component
public class JDHFGrayConfigComponent {
    private static final Logger logger = Logger.getLogger(FlowControlComponent.class);

    private static DiamondManager pddFzGrayDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (pddFzGrayDiamondManager == null) {
                pddFzGrayDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_JD_HF_GRAY, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            JdHufuGrayConfig jdHuGrayConfig = JSONObject.parseObject(config, JdHufuGrayConfig.class);
                            Set<String> sets = new HashSet<>(Arrays.asList(jdHuGrayConfig.getCompanyIds().split(",")));
                            jdHuGrayConfig.setCompanyIdSet(sets);
                            ConfigHolder.JD_HU_GRAY_CONFIG = jdHuGrayConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_JD_HF_GRAY + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_JD_HF_GRAY + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = pddFzGrayDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_JD_HF_GRAY + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                JdHufuGrayConfig pddFzGrayConfig = JSONObject.parseObject(config, JdHufuGrayConfig.class);
                Set<String> sets = new HashSet<>(Arrays.asList(pddFzGrayConfig.getCompanyIds().split(",")));
                pddFzGrayConfig.setCompanyIdSet(sets);
                ConfigHolder.JD_HU_GRAY_CONFIG = pddFzGrayConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_JD_HF_GRAY + ",出错:" + e.getMessage(), e);
        }
    }
}

package com.raycloud.dmj.services.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 单据操作日志
 *
 * <AUTHOR>
 * @date 2020/5/27 14:58
 */
public class ReceiptTrace implements Serializable {
    private static final long serialVersionUID = -1519764003046809089L;
    private Long id;

    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    private Integer dbNo;

    /**
     * 单据id
     */
    private Long receiptId;

    /**
     * 业务单据号
     */
    private String receiptNo;

    /**
     * 单据类型
     */
    private Integer receiptType;

    /**
     * 员工编号，如果是为-1表示系统操作的日志
     */
    private Long staffId;

    /**
     * 员工名称
     */
    private String staffName;

    /**
     * 操作
     */
    private String action;

    /**
     * 业务操作内容
     */
    private String content;

    /**
     * 变更内容
     */
    private Map<String, Object> changeData;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 是否成功
     */
    private Integer isSuccess;

    /**
     * 追踪id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long clueId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public Long getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(Long receiptId) {
        this.receiptId = receiptId;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public Integer getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(Integer receiptType) {
        this.receiptType = receiptType;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Integer getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Integer isSuccess) {
        this.isSuccess = isSuccess;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Map<String, Object> getChangeData() {
        return changeData;
    }

    public void setChangeData(Map<String, Object> changeData) {
        this.changeData = changeData;
    }
}

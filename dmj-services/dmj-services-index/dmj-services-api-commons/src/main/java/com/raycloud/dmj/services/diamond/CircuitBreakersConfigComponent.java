package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 初始化 DiamondConfig.DATE_ID_GLOBAL
 *
 * <AUTHOR>
 * @Date 2019-10-08
 **/
@Component
public class CircuitBreakersConfigComponent {

    private static final Logger logger = Logger.getLogger(CircuitBreakersConfigComponent.class);

    private static DiamondManager CircuitBreakersManager;

    @PostConstruct
    public void init() {
        try {
            if (CircuitBreakersManager == null) {
                CircuitBreakersManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATE_ID_EC_CIRCUIT_BREAKERS, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            List<ECCircuitBreakersConfig> list = JSONObject.parseArray(config, ECCircuitBreakersConfig.class);
                            if (CollectionUtils.isEmpty(list)) {
                                return;
                            }
                            ConfigHolder.EC_CIRCUIT_BREAKERS_CONFIG = list.stream().collect(
                                    Collectors.toMap(ECCircuitBreakersConfig::getEventName, v -> v, (k1, k2) -> k2));
                            if (logger.isDebugEnabled()) {
                                logger.info("v=20191023023013更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_EC_CIRCUIT_BREAKERS + ",config=" + config);
                            }
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_EC_CIRCUIT_BREAKERS + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_EC_CIRCUIT_BREAKERS + ",出错:" + e.getMessage(), e);
        }
    }
}

package com.raycloud.dmj.services.diamond;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.FxGlobalConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/7/9 15:34
 */
@Component
public class FxGlobalConfigComponent {

    private static final Logger logger = Logger.getLogger(FxGlobalConfigComponent.class);

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_FX_GLOBAL_CONFIG, new ManagerListener() {

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            List<FxGlobalConfig.SimpleConfig> configs = JSONObject.parseArray(config, FxGlobalConfig.SimpleConfig.class);
                            if (CollectionUtils.isNotEmpty(configs)) {
                                ConfigHolder.FX_GLOBAL_CONFIG.setConfigMap(configs.stream().collect(Collectors.toMap(FxGlobalConfig.SimpleConfig::getKey, Function.identity())));
                            }
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FX_GLOBAL_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FX_GLOBAL_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FX_GLOBAL_CONFIG + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                List<FxGlobalConfig.SimpleConfig> configs = JSONObject.parseArray(config, FxGlobalConfig.SimpleConfig.class);
                if (CollectionUtils.isNotEmpty(configs)) {
                    ConfigHolder.FX_GLOBAL_CONFIG.setConfigMap(configs.stream().collect(Collectors.toMap(FxGlobalConfig.SimpleConfig::getKey, Function.identity())));
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_FX_GLOBAL_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

}

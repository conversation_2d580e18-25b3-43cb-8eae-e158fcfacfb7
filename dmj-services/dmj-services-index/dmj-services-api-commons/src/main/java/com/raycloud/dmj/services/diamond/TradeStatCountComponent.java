package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.TradeStatCountConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @Date 2022/2/17 2:56 下午
 */
@Component
public class TradeStatCountComponent {

    private static final Logger logger = Logger.getLogger(TradeStatCountComponent.class);

    private static DiamondManager tradeStatCountComponent;

    @PostConstruct
    public void init() {
        try {
            if (tradeStatCountComponent == null) {
                tradeStatCountComponent = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_TRADE_STAT_COUNT, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.TRADE_STAT_COUNT_CONFIG = JSONObject.parseObject(config, TradeStatCountConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_TRADE_STAT_COUNT + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_TRADE_STAT_COUNT + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = tradeStatCountComponent.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_TRADE_STAT_COUNT + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.TRADE_STAT_COUNT_CONFIG = JSONObject.parseObject(config, TradeStatCountConfig.class);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_TRADE_STAT_COUNT + ",出错:" + e.getMessage(), e);
        }
    }
}
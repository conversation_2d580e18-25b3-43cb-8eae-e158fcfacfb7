package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.trade.TradeBusinessConfig;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Component
@Log4j
public class TradeBusinessConfigComponent {

    private static final String GROUP_ID = DiamondConfig.DIAMOND_GROUP_ID;
    private static final String DATA_ID = DiamondConfig.DATA_ID_TRADE_BUSINESS_CONFIG;
    private static DiamondManager newDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (newDiamondManager == null) {
                newDiamondManager = new DefaultDiamondManager(GROUP_ID, DATA_ID, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.TRADE_BUSINESS_CONFIG = JSONObject.parseObject(config, TradeBusinessConfig.class);
                            log.debug("更新TRADE_BUSINESS_CONFIG配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",config=" + config);
                        } catch (Exception e) {
                            log.error("更新TRADE_BUSINESS_CONFIG配置group=" + GROUP_ID + ",dataId=" + DATA_ID + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = newDiamondManager.getAvailableConfigureInfomation(5000L);
                log.debug("拉取TRADE_BUSINESS_CONFIG配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                ConfigHolder.TRADE_BUSINESS_CONFIG = JSONObject.parseObject(config, TradeBusinessConfig.class);
            }
        } catch (Exception e) {
            log.error("初始化TRADE_BUSINESS_CONFIG配置group=" + GROUP_ID + ",dataId=" + DATA_ID + ",出错:" + e.getMessage(), e);
        }
    }
}

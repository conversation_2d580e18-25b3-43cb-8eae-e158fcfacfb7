package com.raycloud.dmj.services.trace;

import com.raycloud.dmj.services.domain.ItemTraceMessage;

import java.util.List;

/**
 * 商品日志的dubbo服务接口
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
public interface IItemTraceServiceDubbo {

    /**
     * 批量记录商品日志消息
     *
     * @param params      商品日志的一些额外参数
     * @param messageList 商品日志消息列表
     */
    void batchRecord(Params params, List<ItemTraceMessage> messageList);
}

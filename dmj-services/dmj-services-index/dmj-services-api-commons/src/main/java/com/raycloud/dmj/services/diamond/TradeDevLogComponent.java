package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.TradeDevLogCompanyIds;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * @Description <pre>
 *
 * </pre>
 * <AUTHOR>
 * @Date 2024-08-22
 */
@Component
public class TradeDevLogComponent {


    private final Logger logger = Logger.getLogger(this.getClass());

    private static DiamondManager diamondManager;

    @PostConstruct
    public void init() {
        try {
            if (diamondManager == null) {
                diamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.TRADE_DEV_LOG_COMPANY_IDS, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            TradeDevLogCompanyIds bean = JSONObject.parseObject(config, TradeDevLogCompanyIds.class);
                            bean.initData();
                            ConfigHolder.TRADE_DEV_LOG_COMPANY_IDS = bean;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_DEV_LOG_COMPANY_IDS + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_DEV_LOG_COMPANY_IDS + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = diamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_DEV_LOG_COMPANY_IDS + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                TradeDevLogCompanyIds bean = JSONObject.parseObject(config, TradeDevLogCompanyIds.class);
                bean.initData();
                ConfigHolder.TRADE_DEV_LOG_COMPANY_IDS = bean;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.TRADE_DEV_LOG_COMPANY_IDS + ",出错:" + e.getMessage(), e);
        }
    }

}

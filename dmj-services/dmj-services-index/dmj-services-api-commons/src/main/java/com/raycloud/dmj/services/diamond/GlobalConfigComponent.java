package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.GlobalConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * 初始化 DiamondConfig.DATE_ID_GLOBAL
 *
 * <AUTHOR>
 * @Date 2019-10-08
 **/
@Component
public class GlobalConfigComponent {

    private static final Logger logger = Logger.getLogger(GlobalConfigComponent.class);

    private static DiamondManager globalDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (globalDiamondManager == null) {
                globalDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATE_ID_GLOBAL, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            ConfigHolder.GLOBAL_CONFIG = JSONObject.parseObject(config, GlobalConfig.class);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_GLOBAL + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_GLOBAL + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = globalDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_FLOW_CONTROL + ",config=" + config);
                if (StringUtils.isNotBlank(config)) {
                    ConfigHolder.GLOBAL_CONFIG = JSONObject.parseObject(config, GlobalConfig.class);
                    logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_GLOBAL + ",config=" + config);
                }
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATE_ID_GLOBAL + ",出错:" + e.getMessage(), e);
        }
    }
}

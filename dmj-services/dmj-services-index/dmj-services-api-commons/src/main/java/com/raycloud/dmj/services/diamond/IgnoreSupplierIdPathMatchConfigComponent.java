package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.IgnoreSupplierIdPathMatchConfig;
import com.raycloud.dmj.domain.diamond.PathMatchRegular;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 根据Path确定，哪些业务请求可以忽略掉供应商id的配置信息
 */
@Slf4j
@Component
public class IgnoreSupplierIdPathMatchConfigComponent {

    private static DiamondManager ignoreSupplierIdPathMatchConfigComponent;

    @PostConstruct
    public void init() {
        try {
            if (ignoreSupplierIdPathMatchConfigComponent == null) {
                ignoreSupplierIdPathMatchConfigComponent = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, 
                        DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        convertConfig(config);
                    }
                });

                String config = ignoreSupplierIdPathMatchConfigComponent.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
            }
        } catch (Exception e) {
            log.error("初始化diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG + ", 出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (config.isEmpty()) {
            log.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG + ", config is null or empty");
            return;
        }

        List<IgnoreSupplierIdPathMatchConfig> ignoreSupplierIdPathMatchConfigList;
        try {
            ignoreSupplierIdPathMatchConfigList = JSONObject.parseArray(config, IgnoreSupplierIdPathMatchConfig.class);
        } catch (Exception e) {
            log.error("json转实体对象失败, diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID
                    + ", dataId = " + DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG
                    + "配置失败, 原因:" + e.getMessage(), e);
            return;
        }
        if (CollectionUtils.isEmpty(ignoreSupplierIdPathMatchConfigList))
        {
            //输出日志，没有配置白名单规则
            log.warn("拉取diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                    + DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG
                    + ", config is null or empty and json convert model result is empty");
            return;
        }
        //对ignoreSupplierIdPathMatchConfigList取出其中的pathMatchRegularList，并将其合并为一个大的List，之后在对这个List按照
        // PathMatchRegular中的属性matchType进行分组
        ConfigHolder.IGNORE_SUPPLIER_ID_PATH_MATCH_REGULAR_CONFIG = ignoreSupplierIdPathMatchConfigList
                .stream().flatMap(ignoreSupplierIdPathMatchConfig ->
                        ignoreSupplierIdPathMatchConfig.getPathMatchRegularList().stream())
                .collect(Collectors.groupingBy(PathMatchRegular::getMatchType));
        log.info("更新diamond配置 group = " + DiamondConfig.DIAMOND_GROUP_ID + ", dataId = "
                + DiamondConfig.DATA_ID_IGNORE_SUPPLIER_ID_PATH_CONFIG + ", config = " + config);
    }

}

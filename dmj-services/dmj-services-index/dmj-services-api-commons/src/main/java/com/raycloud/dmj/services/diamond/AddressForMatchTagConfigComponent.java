package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.AddressForMatchTagConfig;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Component
public class AddressForMatchTagConfigComponent {
    private static final Logger logger = Logger.getLogger(AddressForMatchTagConfig.class);

    private static DiamondManager addressForMatchTagConfigDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (addressForMatchTagConfigDiamondManager == null) {
                addressForMatchTagConfigDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_ADDRESS_FOR_MATCH_TAG_CONFIG, new ManagerListener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            convertConfig(config);
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_ADDRESS_FOR_MATCH_TAG_CONFIG + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_ADDRESS_FOR_MATCH_TAG_CONFIG + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = addressForMatchTagConfigDiamondManager.getAvailableConfigureInfomation(5000L);
                convertConfig(config);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_ADDRESS_FOR_MATCH_TAG_CONFIG + ",config=" + config);
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_ADDRESS_FOR_MATCH_TAG_CONFIG + ",出错:" + e.getMessage(), e);
        }
    }

    private void convertConfig(String config) {
        if (StringUtils.isBlank(config)) {
            return;
        }
        AddressForMatchTagConfig addressForMatchTagConfig = JSONObject.parseObject(config, AddressForMatchTagConfig.class);
        if(addressForMatchTagConfig !=null) {
            ConfigHolder.ADDRESS_FOR_MATCH_TAG_CONFIG = addressForMatchTagConfig;
        }
    }
}

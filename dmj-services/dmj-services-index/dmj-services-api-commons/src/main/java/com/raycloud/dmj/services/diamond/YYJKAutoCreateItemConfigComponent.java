package com.raycloud.dmj.services.diamond;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.diamond.manager.DiamondManager;
import com.raycloud.diamond.manager.ManagerListener;
import com.raycloud.diamond.manager.impl.DefaultDiamondManager;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.diamond.YYJKAutoCreateItemConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2024/1/12
 * @description 美团医药健康自动创建商品白名单配置
 */
@Component
public class YYJKAutoCreateItemConfigComponent {

    private static final Logger logger = Logger.getLogger(YYJKAutoCreateItemConfigComponent.class);

    private static DiamondManager yyjkAutoCreatItemDiamondManager;

    @PostConstruct
    public void init() {
        try {
            if (yyjkAutoCreatItemDiamondManager == null) {
                yyjkAutoCreatItemDiamondManager = new DefaultDiamondManager(DiamondConfig.DIAMOND_GROUP_ID, DiamondConfig.DATA_ID_MTYYJK_AUTO_CREATE_ITEM, new ManagerListener() {

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String config) {
                        if (StringUtils.isEmpty(config)) {
                            return;
                        }
                        try {
                            YYJKAutoCreateItemConfig yyjkAutoCreateItemConfig = JSONObject.parseObject(config, YYJKAutoCreateItemConfig.class);
                            buildData(yyjkAutoCreateItemConfig);
                            ConfigHolder.MTYYJK_AUTO_CREATE_ITEM = yyjkAutoCreateItemConfig;
                            logger.info("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_MTYYJK_AUTO_CREATE_ITEM + ",config=" + config);
                        } catch (Exception e) {
                            logger.error("更新diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_MTYYJK_AUTO_CREATE_ITEM + "配置失败,原因:" + e.getMessage(), e);
                        }
                    }
                });

                String config = yyjkAutoCreatItemDiamondManager.getAvailableConfigureInfomation(5000L);
                logger.info("拉取diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_MTYYJK_AUTO_CREATE_ITEM + ",config=" + config);
                if (StringUtils.isBlank(config)) {
                    return;
                }
                YYJKAutoCreateItemConfig syncConfig = JSONObject.parseObject(config, YYJKAutoCreateItemConfig.class);
                buildData(syncConfig);
                ConfigHolder.MTYYJK_AUTO_CREATE_ITEM = syncConfig;
            }
        } catch (Exception e) {
            logger.error("初始化diamond配置group=" + DiamondConfig.DIAMOND_GROUP_ID + ",dataId=" + DiamondConfig.DATA_ID_MTYYJK_AUTO_CREATE_ITEM + ",出错:" + e.getMessage(), e);
        }
    }

    /**
     * 组装数据
     *
     * @param yyjkAutoCreateItemConfig
     */
    private void buildData(YYJKAutoCreateItemConfig yyjkAutoCreateItemConfig) {
        String exceptHandlerCompanyIds = yyjkAutoCreateItemConfig.getAutoCreateItemCompanyIds();
        Set<String> sets = new HashSet<>(Arrays.asList(exceptHandlerCompanyIds.split(",")));
        yyjkAutoCreateItemConfig.setAutoCreateItemCompanyIdSet(sets);
    }

}

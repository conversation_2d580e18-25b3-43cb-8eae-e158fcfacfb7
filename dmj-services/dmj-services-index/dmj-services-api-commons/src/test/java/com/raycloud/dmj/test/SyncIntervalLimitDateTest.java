package com.raycloud.dmj.test;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.cache.simple.SimpleCache;
import com.raycloud.dmj.services.domain.SyncIntervalLimitDate;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.util.Assert;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created by daixiaoming on 2020/2/18.
 */
public class SyncIntervalLimitDateTest {

    @Test
    public void testGetLimitDate() throws CacheException {
        SyncIntervalLimitDate syncIntervalLimitDate = new SyncIntervalLimitDate();
        syncIntervalLimitDate.setFullSyncInterval(2);
        syncIntervalLimitDate.setLast7DaySyncInterval(60);
        syncIntervalLimitDate.setLast3DaySyncInterval(30);
        syncIntervalLimitDate.setLastDaySyncInterval(4);
        syncIntervalLimitDate.setMinSyncMinutes(60);

        ICache cache = new SimpleCache();
        String userId = "1";
        Date now = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        System.out.println("当前时间：" + simpleDateFormat.format(now));
        Date startDate = DateUtils.addDays(now, -30);
        System.out.println("默认同步开始时间：" + simpleDateFormat.format(startDate));
        Date limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第1次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(limitStartDate.equals(startDate), "第1次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第2次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 7 * 24 * 60 * 60 * 1000, "第2次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第3次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 3 * 24 * 60 * 60 * 1000, "第3次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第4次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 24 * 60 * 60 * 1000, "第4次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第5次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 60 * 60 * 1000, "第5次执行同步数据开始时间有误");

        //模拟缓存过期
        cache.delete("syncFullData-1");
        cache.delete("syncLast7DayData-1");
        cache.delete("syncLast3DayData-1");
        cache.delete("syncLastDayData-1");
        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第6次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(limitStartDate.equals(startDate), "第6次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第7次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 7 * 24 * 60 * 60 * 1000, "第7次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第8次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 3 * 24 * 60 * 60 * 1000, "第8次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第9次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 24 * 60 * 60 * 1000, "第9次执行同步数据开始时间有误");

        limitStartDate = syncIntervalLimitDate.getLimitedSyncStartDate(cache, userId, startDate);
        System.out.println("第10次执行同步数据开始时间：" + simpleDateFormat.format(limitStartDate));
        Assert.isTrue(now.getTime() - limitStartDate.getTime() <= 60 * 60 * 1000, "第10次执行同步数据开始时间有误");
    }
}

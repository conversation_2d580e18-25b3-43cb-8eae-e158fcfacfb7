package com.raycloud.dmj.services.cache;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.simple.SimpleCache;
import com.raycloud.cache.simple.SimpleSharedCache;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.util.Assert;

import java.lang.reflect.Field;

/**
 * user: hj
 * time: 2022/12/12 15:38
 * desc:
 */
public class CommonMixedSharedCacheTest {

    private CommonMixedSharedCache mixedCache;
    private String key = "staff_1111";
    private String val = "staff_1111_value";

    @Before
    public void setUp() {
        mixedCache = Mockito.spy(new CommonMixedSharedCache());
        mixedCache.commonMixedCacheConfig = Mockito.mock(CommonMixedCacheConfig.class);
        Mockito.when(mixedCache.commonMixedCacheConfig.isOpen()).thenReturn(true);
        Mockito.when(mixedCache.commonMixedCacheConfig.projectMixedCacheGetOpen()).thenReturn(true);

        mixedCache.sharedCache = new SimpleSharedCache();

        try {
            Field field = mixedCache.sharedCache.getClass().getDeclaredField("simpleCache");
            field.setAccessible(true);
            field.set(mixedCache.sharedCache, new SimpleCache());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test1() {
        mixedCache.set(key, val, 300);

        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        mixedCache.delete(key);
        value = mixedCache.get(key);
        Assert.isNull(value);

        CommonMixedSharedCache.LocalCacheValue localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isNull(localCacheValue);

        mixedCache.set(key, val, 300);

        // 模拟cacheSetTime不一致场景
        mixedCache.localCache.getIfPresent(key).setSetCacheTime(System.currentTimeMillis() - 3600);

        value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isTrue(val.equals(localCacheValue.getOcsCacheValue()));
    }

    @Test
    public void test2() throws CacheException {
        mixedCache.set(key, val, 3600);
        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        // 设置为失效，看看获取的时候会不会重新对本地缓存进行设置
        mixedCache.localCache.invalidate(key);

        value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        CommonMixedSharedCache.LocalCacheValue localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isTrue(localCacheValue.getOcsCacheValue().equals(value));

    }

    @Test
    public void test3() throws InterruptedException, CacheException {
        mixedCache.set(key, val, 5);
//        Thread.sleep(5500);
//        Assert.isNull(mixedCache.localCache.getIfPresent(key));
    }

    @Test
    public void test4() throws CacheException {
        mixedCache.set(key, val, 5);
        mixedCache.delete(key);

        Assert.isNull(mixedCache.localCache.getIfPresent(key));
        Assert.isNull(mixedCache.sharedCache.get(key));
        Assert.isNull(mixedCache.get(key));
    }

    @Test
    public void test5() throws CacheException {
        mixedCache.set(key, val, 300);
        mixedCache.sharedCache.delete(key + mixedCache.CenterCacheKey);

        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));
    }
}

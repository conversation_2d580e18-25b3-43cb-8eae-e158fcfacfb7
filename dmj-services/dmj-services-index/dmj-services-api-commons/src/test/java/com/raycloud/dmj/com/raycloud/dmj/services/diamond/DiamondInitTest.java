package com.raycloud.dmj.com.raycloud.dmj.services.diamond;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.dynamicproperty.DubboConsumerFilterConfig;
import com.raycloud.dmj.dynamicproperty.DynamicPropertyCenter;
import com.raycloud.dmj.services.diamond.DiamondInit;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * DiamondInitTest
 *
 * <AUTHOR>
 * @since 2024/4/28 2:59 PM
 */
public class DiamondInitTest {

    @Test
    public void case1() throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        DiamondInit diamondInit = new DiamondInit();
        invokeMethod(diamondInit, "processConfigInfo", "{\n" +
                "    \"removeUserMatchRules\": [\n" +
                "        {\n" +
                "            \"companyIds\": \"36201\",\n" +
                "            \"dubboInterface\": \"com.raycloud.dmj.services.user.ISubUserService insertSubUser\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"companyIds\": \"1693,1694\",\n" +
                "            \"dubboInterface\": \"com.raycloud.dmj.services.user.ISubUserService insertSubUser\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"companyIds\": \"*\",\n" +
                "            \"dubboInterface\": \"com.raycloud.dmj.services.user.ISubUserService insertSubUser\"\n" +
                "        }\n" +
                "    ]\n" +
                "}", new DiamondInit.DiamondConfig("group", "dataId", DubboConsumerFilterConfig.class, dynamicProperty -> DynamicPropertyCenter.setDubboConsumerFilterConfig((DubboConsumerFilterConfig) dynamicProperty)));
        Assert.assertTrue(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(36201L),"gray3", "com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
        Assert.assertTrue(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(1693L), "gray4","com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
        Assert.assertTrue(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(1694L), "gray4","com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
        Assert.assertTrue(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(1L), "gray5","com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
        Assert.assertTrue(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(2L), "gray5","com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
        Assert.assertFalse(DynamicPropertyCenter.getDubboConsumerFilterConfig().isNeedRemoveUser(new Staff().setCompanyId(36202L),"gray3", "com.raycloud.dmj.services.user.ISubUserService insertSubUser"));
    }

    private Object invokeMethod(Object obj, String methodName, Object... args) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<?>[] classes = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            classes[i] = args[i].getClass();
        }
        Method method = obj.getClass().getDeclaredMethod(methodName, classes);
        method.setAccessible(true);
        return method.invoke(obj, args);
    }


}

package com.raycloud.dmj.services.cache;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.util.LinkedList;

/**
 * user: hj
 * time: 2022/06/21 15:32
 * desc:
 */
public class CacheContainerTest {

    private CacheContainer cacheContainer;

    @Before
    public void setUp() {
        cacheContainer = Mockito.spy(new CacheContainer());

        cacheContainer.callCacheShutdownAction = Mockito.spy(new CallCacheShutdownAction());
        cacheContainer.deleteCacheShutdownAction = Mockito.spy(new CallCacheShutdownAction());
    }

    @Test
    public void test1() {
        LinkedList<String> cacheKeyList = new LinkedList<>();
        cacheKeyList.addLast("aa");
        cacheKeyList.addLast("bb");
        cacheKeyList.addLast("cc");

        cacheKeyList.removeFirst();
        cacheKeyList.removeFirst();

        Assert.isTrue(cacheKeyList.size() == 1);
        Assert.isTrue(cacheKeyList.get(0).equals("cc"));
    }

    @Test
    public void test2() {

        ShutdownCallback callback = new ShutdownCallback() {
            @Override
            public void execute(String cacheKey) {
                cacheContainer.cacheKeyList.remove(cacheKey);
                cacheContainer.cacheKeyMap.remove(cacheKey);
            }
        };

        cacheContainer.register("aa", callback);
        cacheContainer.register("bb", callback);
        cacheContainer.register("cc", callback);

        Assert.isTrue(cacheContainer.cacheKeyList.size() == 3);
        Assert.isTrue(cacheContainer.cacheKeyMap.size() == 3);

        cacheContainer.unregister("bb");

        Assert.isTrue(cacheContainer.cacheKeyList.size() == 2);
        Assert.isTrue(cacheContainer.cacheKeyMap.size() == 2);

        cacheContainer.destroy();

        Assert.isTrue(cacheContainer.cacheKeyList.size() == 0);
        Assert.isTrue(cacheContainer.cacheKeyMap.size() == 0);
    }

    @Test
    public void test3() {
        cacheContainer.FullSize = 3;
        ShutdownCallback callback = new ShutdownCallback() {
            @Override
            public void execute(String cacheKey) {
                cacheContainer.cacheKeyList.remove(cacheKey);
                cacheContainer.cacheKeyMap.remove(cacheKey);
            }
        };

        cacheContainer.register("aa", callback);
        cacheContainer.register("bb", callback);
        cacheContainer.register("cc", callback);
        cacheContainer.register("dd", callback);

        Assert.isTrue(cacheContainer.cacheKeyList.size() == 3);
        Assert.isTrue(cacheContainer.cacheKeyMap.size() == 3);

    }


    @Test
    public void test6() {
        ShutdownCallback callback = new ShutdownCallback() {
            @Override
            public void execute(String cacheKey) {
                cacheContainer.cacheKeyList.remove(cacheKey);
                cacheContainer.cacheKeyMap.remove(cacheKey);
            }
        };

        cacheContainer.register("aa", callback);
        cacheContainer.register("bb", callback);
        cacheContainer.register("cc", callback);
        cacheContainer.register("bb", callback);

        Assert.isTrue(cacheContainer.cacheKeyList.size() == 3);
        Assert.isTrue(cacheContainer.cacheKeyMap.size() == 3);
    }

//    @Test
    public void test4() {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (int i = 0; i < 10000; i++){
            cacheContainer.register("aa"+i);
        }
        stopWatch.stop();

        System.out.println(stopWatch.prettyPrint());
    }

//    @Test
    public void test5() throws InterruptedException {

        for (int t = 0; t < 10; t++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < 10000; i++){
                        cacheContainer.register("aa"+i);
                    }
                }
            }).start();
        }

        Thread.sleep(10000);
    }
}

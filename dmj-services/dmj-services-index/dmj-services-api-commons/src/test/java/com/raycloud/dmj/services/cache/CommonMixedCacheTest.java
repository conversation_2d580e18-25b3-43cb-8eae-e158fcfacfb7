package com.raycloud.dmj.services.cache;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.simple.SimpleCache;
import com.raycloud.dmj.domain.account.Staff;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.util.Assert;

/**
 * user: hj
 * time: 2022/12/12 15:38
 * desc:
 */
public class CommonMixedCacheTest {

    private CommonMixedCache mixedCache;
    private String key = "staff_1111";
    private String val = "staff_1111_value";

    @Before
    public void setUp() {
        mixedCache = Mockito.spy(new CommonMixedCache());
        mixedCache.commonMixedCacheConfig = Mockito.mock(CommonMixedCacheConfig.class);
        mixedCache.cacheValueCopier = new CacheValueCopier();
        Mockito.when(mixedCache.commonMixedCacheConfig.isOpen()).thenReturn(true);
        Mockito.when(mixedCache.commonMixedCacheConfig.projectMixedCacheGetOpen()).thenReturn(true);

        mixedCache.cache = new SimpleCache();
    }

    @Test
    public void test1() {
        mixedCache.set(key, val, 300);

        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        mixedCache.delete(key);
        value = mixedCache.get(key);
        Assert.isNull(value);

        CommonMixedCache.LocalCacheValue localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isNull(localCacheValue);

        mixedCache.set(key, val, 300);

        // 模拟cacheSetTime不一致场景
        mixedCache.localCache.getIfPresent(key).setSetCacheTime(System.currentTimeMillis() - 3600);

        value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isTrue(val.equals(localCacheValue.getOcsCacheValue()));
    }

    @Test
    public void test2() throws CacheException {
        mixedCache.set(key, val, 3600);
        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        // 设置为失效，看看获取的时候会不会重新对本地缓存进行设置
        mixedCache.localCache.invalidate(key);

        value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));

        CommonMixedCache.LocalCacheValue localCacheValue = mixedCache.localCache.getIfPresent(key);
        Assert.isTrue(localCacheValue.getOcsCacheValue().equals(value));

    }

    @Test
    public void test3() throws InterruptedException, CacheException {
        mixedCache.set(key, val, 5);
//        Thread.sleep(5500);
//        Assert.isNull(mixedCache.localCache.getIfPresent(key));
    }

    @Test
    public void test4() throws CacheException {
        mixedCache.set(key, val, 5);
        mixedCache.delete(key);

        Assert.isNull(mixedCache.localCache.getIfPresent(key));
        Assert.isNull(mixedCache.cache.get(key));
        Assert.isNull(mixedCache.get(key));
    }

    @Test
    public void test5() throws CacheException {
        mixedCache.set(key, val, 300);
        mixedCache.cache.delete(key + mixedCache.CenterCacheKey);

        String value = mixedCache.get(key);
        Assert.isTrue(val.equals(value));
    }

    @Test
    public void test6() throws CacheException {
        Staff staff = new Staff();
        staff.setId(111L);
        staff.setName("张三");

        mixedCache.set("person_11", staff, 300);
        Staff value1 = mixedCache.get("person_11");
        Staff value2 = mixedCache.getAndShallowCopy("person_11");

        System.out.println(staff);
        System.out.println(value1);
        System.out.println(value2);

        Assert.isTrue(staff.equals(value1));
        Assert.isTrue(!staff.equals(value2));
    }
}

package com.raycloud.dmj.services.util;

import com.raycloud.dmj.services.utils.SecretUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021/1/11 17:31
 */
public class SecretUtilsTest {

    @Test
    public void testIsPddEncrypt() {
        Assert.assertTrue(SecretUtils.isPddEncrypt("~AgAAAACPnBcFYFlW2wB23IuRGm8eEfNOEFaTNWKbatc=~e91h~0~~"));

        Assert.assertFalse(SecretUtils.isPddEncrypt(null));
        Assert.assertFalse(SecretUtils.isPddEncrypt("r242131231"));
        Assert.assertFalse(SecretUtils.isPddEncrypt("AAQ4LNVgzmEsB+uiKhIXhvme/9TmgoiFz0PE6xLiUYvnG6vOG7KIHSnEYjJ2UxKUOps=\n"));
    }

    @Test
    public void testIsJdEncrypt() {
        Assert.assertTrue(SecretUtils.isJdEncrypt("AAQ4LNVgzmEsB+uiKhIXhvme/9TmgoiFz0PE6xLiUYvnG6vOG7KIHSnEYjJ2UxKUOps=\n"));

        Assert.assertFalse(SecretUtils.isJdEncrypt(null));
        Assert.assertFalse(SecretUtils.isJdEncrypt("r242131231"));
        Assert.assertFalse(SecretUtils.isJdEncrypt("~AgAAAACPnBcFYFlW2wB23IuRGm8eEfNOEFaTNWKbatc=~e91h~0~~"));
    }

    @Test
    public void testIsEncodeSecret() {
        Assert.assertTrue(SecretUtils.isEncodeSecret("{2434vsad}"));

        Assert.assertFalse(SecretUtils.isEncodeSecret(null));
        Assert.assertFalse(SecretUtils.isEncodeSecret(""));
        Assert.assertFalse(SecretUtils.isEncodeSecret(" "));
        Assert.assertFalse(SecretUtils.isEncodeSecret("2434vsad"));
        Assert.assertFalse(SecretUtils.isEncodeSecret("2434vsad}"));
        Assert.assertFalse(SecretUtils.isEncodeSecret("{2434vsad}5"));
    }

    @Test
    public void testNeedEncode() {
        Assert.assertTrue(SecretUtils.needEncode("2434vsad"));
        Assert.assertTrue(SecretUtils.needEncode("{2434vsad"));
        Assert.assertTrue(SecretUtils.needEncode("{2434vs}ad"));
        Assert.assertTrue(SecretUtils.needEncode(" "));

        Assert.assertFalse(SecretUtils.needEncode(null));
        Assert.assertFalse(SecretUtils.needEncode(""));
        Assert.assertFalse(SecretUtils.needEncode("{2434vsad}"));
    }

    @Test
    public void testAddSecretFlag() {
        Assert.assertEquals("{2434vsad}", SecretUtils.addSecretFlag("2434vsad"));
        Assert.assertNull(SecretUtils.addSecretFlag(null));
        Assert.assertEquals("{ }", SecretUtils.addSecretFlag(" "));
    }

    @Test
    public void testRemoveSecretFlag() {
        Assert.assertEquals("2434vsad", SecretUtils.removeSecretFlag("{2434vsad}"));
        Assert.assertEquals(null, SecretUtils.removeSecretFlag(null));
        Assert.assertEquals(" ", SecretUtils.removeSecretFlag(" "));
        Assert.assertEquals("{ ", SecretUtils.removeSecretFlag("{ "));
        Assert.assertEquals(" ", SecretUtils.removeSecretFlag("{ }"));
    }
}

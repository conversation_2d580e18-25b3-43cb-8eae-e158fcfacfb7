package com.raycloud.dmj.services.ec.support;

import org.junit.Assert;
import org.junit.Test;

public class StaffTransformServiceTest {

    @Test
    public void testCheckIdLegality() {
        StaffTransformService staffTransformService = new StaffTransformService();
        Assert.assertFalse(staffTransformService.checkIdLegality(null));
        Assert.assertFalse(staffTransformService.checkIdLegality(-1L));
        Assert.assertFalse(staffTransformService.checkIdLegality(0L));
        Assert.assertTrue(staffTransformService.checkIdLegality(2L));
    }
}

package com.raycloud.dmj.services.secret;

import com.raycloud.dmj.services.utils.SecretUtils;
import com.raycloud.secret_api.api.SecretRequest;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/1/11 10:03
 */
public class SecretDataServiceTest {

    private static final Map<String, String[]> decodeContent_encodeContentAndEncodeSecretMap = new HashMap<>();
    private static final List<String> DECODE_CONTENT_LIST = new ArrayList<>();
    private static final List<String> ENCODE_CONTENT_LIST = new ArrayList<>();
    private static final List<String> ENCODE_SECRET_LIST = new ArrayList<>();
    private static final String DECODE_CONTENT;
    private static final String ENCODE_CONTENT;
    private static final String ENCODE_SECRET;

    private final static SecretDataService secretDataService;

    static {
        SecretRequest secretRequest = Mockito.mock(SecretRequest.class);
        secretDataService = Mockito.spy(new SecretDataService());
        secretDataService.setSecretRequest(secretRequest);

        decodeContent_encodeContentAndEncodeSecretMap.put("SecretDataServiceTest", new String[]{"a469d2354d8c89cf9ef3e23618190f4e1c8b8b010561169a2f7d0c6b0a5a1afe", "{a469d2354d8c89cf9ef3e23618190f4e1c8b8b010561169a2f7d0c6b0a5a1afe}"});
        decodeContent_encodeContentAndEncodeSecretMap.put("justTest", new String[]{"1c9b59d8a7c96910c9bfa6d5d4ae198d", "{1c9b59d8a7c96910c9bfa6d5d4ae198d}"});
        decodeContent_encodeContentAndEncodeSecretMap.put(null, new String[]{null, null});
        decodeContent_encodeContentAndEncodeSecretMap.put("", new String[]{"", ""});
        decodeContent_encodeContentAndEncodeSecretMap.put(" ", new String[]{"c1ce68dcd69adf7bbb879877b4725554", "{c1ce68dcd69adf7bbb879877b4725554}"});
        decodeContent_encodeContentAndEncodeSecretMap.put("hahaha", new String[]{"6e668295369e1910acb298d3b6dac4e2", "{6e668295369e1910acb298d3b6dac4e2}"});
        decodeContent_encodeContentAndEncodeSecretMap.put("{a189e8c2c14f050c8fa5688a5ee20523}", new String[]{"a189e8c2c14f050c8fa5688a5ee20523", "{a189e8c2c14f050c8fa5688a5ee20523}"});

        for (Map.Entry<String, String[]> entry : decodeContent_encodeContentAndEncodeSecretMap.entrySet()) {
            DECODE_CONTENT_LIST.add(entry.getKey());
            ENCODE_CONTENT_LIST.add(entry.getValue()[0]);
            ENCODE_SECRET_LIST.add(entry.getValue()[1]);
        }

        int index = new Random().nextInt(DECODE_CONTENT_LIST.size());
        DECODE_CONTENT = DECODE_CONTENT_LIST.get(index);
        ENCODE_CONTENT = ENCODE_CONTENT_LIST.get(index);
        ENCODE_SECRET = ENCODE_SECRET_LIST.get(index);
    }

    @Test
    public void testEncodeMD5() {
        String result = secretDataService.encodeMD5("SecretDataServiceTest");
        Assert.assertEquals("114fa0ba0aa6c0d8f19896979f011319", result);
    }

    @Test
    public void testEncodeSecret() throws Exception {
        Mockito.when(secretDataService.getSecretRequest().encode(DECODE_CONTENT)).thenReturn(ENCODE_CONTENT);
        String result = secretDataService.encodeSecret(DECODE_CONTENT);

        Assert.assertEquals(ENCODE_SECRET, result);
    }

    @Test
    public void testDecodeSecret() throws Exception {
        Mockito.when(secretDataService.getSecretRequest().decode(ENCODE_CONTENT)).thenReturn(DECODE_CONTENT);
        String result = secretDataService.decodeSecret(ENCODE_SECRET);
        Assert.assertEquals(DECODE_CONTENT, result);
    }

    @Test
    public void testBatchEncodeSecret() throws Exception {
        // 需要加密的明文
        List<String> needEncodeContentList = new ArrayList<>();
        List<String> expectEncodeContentList = new ArrayList<>();
        for (int i = 0; i < DECODE_CONTENT_LIST.size(); i++) {
            String content = DECODE_CONTENT_LIST.get(i);
            if (SecretUtils.needEncode(content)) {
                needEncodeContentList.add(content);
                expectEncodeContentList.add(ENCODE_CONTENT_LIST.get(i));
            }
        }

        Mockito.when(secretDataService.getSecretRequest().encode(needEncodeContentList)).thenReturn(expectEncodeContentList);
        List<String> resultList = secretDataService.batchEncodeSecret(DECODE_CONTENT_LIST);
        Assert.assertEquals(ENCODE_SECRET_LIST, resultList);
    }

    @Test
    public void testBatchDecodeSecret() throws Exception {
        // 需要解密的密文
        List<String> needDecodeContentList = new ArrayList<>();
        List<String> expectDecodeContentList = new ArrayList<>();
        for (int i = 0; i < ENCODE_SECRET_LIST.size(); i++) {
            String content = ENCODE_SECRET_LIST.get(i);
            if (SecretUtils.isEncodeSecret(content)) {
                needDecodeContentList.add(ENCODE_CONTENT_LIST.get(i));
                expectDecodeContentList.add(DECODE_CONTENT_LIST.get(i));
            }
        }

        Mockito.when(secretDataService.getSecretRequest().decode(needDecodeContentList)).thenReturn(expectDecodeContentList);
        List<String> resultList = secretDataService.batchDecodeSecret(ENCODE_SECRET_LIST);
        Assert.assertEquals(DECODE_CONTENT_LIST, resultList);
    }
}

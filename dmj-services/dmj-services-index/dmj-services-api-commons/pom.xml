<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dmj-services-index</artifactId>
        <groupId>com.raycloud.dmj</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dmj-services-api-commons</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.raycloud.server.degradation</groupId>
            <artifactId>server-degradation</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>dmj-services-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud.cache</groupId>
            <artifactId>cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud.ec</groupId>
            <artifactId>ec-api</artifactId>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.raycloud.ec</groupId>-->
            <!--<artifactId>ec-tx</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.raycloud.ec</groupId>
            <artifactId>ec-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud.cache</groupId>
            <artifactId>cache-simple</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>secret-api-interface</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.ibatis</groupId>
            <artifactId>ibatis-sqlmap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-2-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>jd-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>commons-utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-pubsub-cluster</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>

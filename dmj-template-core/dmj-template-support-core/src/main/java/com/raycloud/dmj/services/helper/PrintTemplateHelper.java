package com.raycloud.dmj.services.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.jd.open.api.sdk.request.order.PopOrderPrintDataGetRequest;
import com.jd.open.api.sdk.response.order.PopOrderPrintDataGetResponse;
import com.kuaidizs.express.domain.ht.HtOrderBigMark;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.PickGoodsRouteConfig;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.caigou.*;
import com.raycloud.dmj.domain.enums.CustomPrivilegeType;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.tb.TbItem;
import com.raycloud.dmj.domain.item.tb.TbSku;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.*;
import com.raycloud.dmj.domain.pt.jd.JdBigshotBo;
import com.raycloud.dmj.domain.pt.model.YDWLBConfigParam;
import com.raycloud.dmj.domain.pt.model.print.CloudPrintTaskParam;
import com.raycloud.dmj.domain.pt.model.print.FieldValuesInvoicesRequest;
import com.raycloud.dmj.domain.pt.model.print.PrintItemMergeRule;
import com.raycloud.dmj.domain.pt.model.print.picker.PrintTemplateOrder;
import com.raycloud.dmj.domain.pt.model.print.picker.PrintTemplateTrade;
import com.raycloud.dmj.domain.pt.model.serviceinfo.*;
import com.raycloud.dmj.domain.pt.wlb.FieldValue;
import com.raycloud.dmj.domain.pt.wlb.PrintMerchantCodeDataParam;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.reissueOrRefund.ItemSnapshot;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.label.TradeLabel;
import com.raycloud.dmj.domain.trades.Orderable;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.Address;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.Numeric2ChineseStr;
import com.raycloud.dmj.domain.utils.Provice2ShortUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.wms.PickingType;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.params.AssoGoodsSectionSkuParams;
import com.raycloud.dmj.domain.workorder.WorkOrder;
import com.raycloud.dmj.jd.common.JdClientHelper;
import com.raycloud.dmj.merchantCode.VO.CaigouPrintDataVo;
import com.raycloud.dmj.merchantCode.VO.ReceiptPrintDataVo;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.helper.wlb.WayBillCompanyHelper;
import com.raycloud.dmj.services.helper.wlb.WlbExpressHelper;
import com.raycloud.dmj.services.privilegeUtils.DataPrivilegeFilter;
import com.raycloud.dmj.services.pt.IOutSidRecyclePoolService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.IWarehouseTemplateService;
import com.raycloud.dmj.services.pt.model.StallDeliverModel;
import com.raycloud.dmj.services.pt.model.print.FieldValuesResponse;
import com.raycloud.dmj.services.pt.utils.PrintSkuKeyUtils;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trade.label.ITradeLabelService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeWaveService;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.tve.utils.CommaReplaceUtils;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.constant.SystemTags.*;
import static com.raycloud.dmj.services.helper.PrintMergeHelpler.needSameItemMerge;

/**
 * <AUTHOR> huangjie
 * @Date : 2015/11/13 13:59
 * @Info :
 */
public class PrintTemplateHelper {
    private static final int BATCH_QUERY_ITEM_SIZE = 200;

    private static final Logger logger = Logger.getLogger(PrintTemplateHelper.class);

    private static final String REGEX_SHORT_ADDRESS = "省|市|区|自治|地区";
    // 快递单中订单标记映射
    private static final Map<String, String> TRADE_TAB_MAP = new HashMap<>();
    // 普通快递模板中所有商品详情字段
    private static final Set<String> EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS = new HashSet<>();
    // 电子快递模板中所有商品详情字段
    private static final Set<String> WLB_EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS = new HashSet<>();

    /**
     * 多件波次打印 即为 单件波次不打印
     */
    private static final String MULTI_WAVE_PRINT = "multi_wave";
    /**
     * 所有波次都打印
     */
    private static final String ALL_WAVE_PRINT = "all_wave";

    private static List<String> abroadTradeSource = Arrays.asList(CommonConstants.PLAT_FORM_TYPE_TEMU, CommonConstants.PLAT_FORM_TYPE_SMTQTG, CommonConstants.PLAT_FORM_TYPE_SHEIN);
    /**
     * 各类模板中规格简称字段
     */
    private static final Set<String> SHORT_KEYS = new HashSet<>();

    /**
     * 代收货款匹配规则
     */
    private static final String COD_VALUE_COMPILE = "(?<=\\代收\\[|\\代收\\【).+?(?=\\]|】)";

    /**
     * 采购退货单 采退价 权限控制字段
     */
    private static final List<String> PURCHASE_RETURN_PRIVILEGE_LIST = new ArrayList<>();

    static {
        TRADE_TAB_MAP.put("purchase_out", "采退");
        TRADE_TAB_MAP.put("reissue", "补发");
        TRADE_TAB_MAP.put("changeitem", "换货");
        TRADE_TAB_MAP.put("fenxiao", "分销");
        TRADE_TAB_MAP.put("cod", "货到");
        TRADE_TAB_MAP.put("jd-1", "货到");
        TRADE_TAB_MAP.put("ta", "信保");
        TRADE_TAB_MAP.put("wholesale", "在线");
        TRADE_TAB_MAP.put("ws", "大额批发");
        TRADE_TAB_MAP.put("yp", "普通拿样");
        TRADE_TAB_MAP.put("yf", "一分钱拿样");
        TRADE_TAB_MAP.put("fs", "倒批(限时折扣)");
        TRADE_TAB_MAP.put("cz", "加工定制");
        TRADE_TAB_MAP.put("ag", "协议采购");
        TRADE_TAB_MAP.put("hp", "伙拼");
        TRADE_TAB_MAP.put("supply", "供销");
        TRADE_TAB_MAP.put("factory", "淘工厂");
        TRADE_TAB_MAP.put("quick", "快订");
        TRADE_TAB_MAP.put("xiangpin", "享拼");
        TRADE_TAB_MAP.put("f2f", "当面现付");
        TRADE_TAB_MAP.put("cyfw", "存样服务");
        TRADE_TAB_MAP.put("sp", "代销");
        TRADE_TAB_MAP.put("wg", "微供");
        TRADE_TAB_MAP.put("sd", "经销");
        TRADE_TAB_MAP.put("tmall_i18n", "天猫国际");
        TRADE_TAB_MAP.put("overseas_warehouse", "海外仓");
        TRADE_TAB_MAP.put("lazada", "LAZADA");

        EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS.addAll(Arrays.asList("sys_sku_remark", "item_platform_title", "item_price", "table_section_num", "item_specification_paltform", "item_title", "item_short_title", "item_outer_id", "outer_id_of_item", "item_properties"));
        WLB_EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS.addAll(Arrays.asList("item_num", "goods_properties", "item_platform_title", "goods_specification", "item_price", "goods_short_title", "goods_num", "item_title", "goods_title", "table_section_num", "goods_outer_id", "outer_id_of_item", "sys_sku_remark", "item_sys_remark", "goods_price", "item_specification_paltform", "item_short_title", "item_outer_id", "goods_specification_paltform", "goods_sys_remark", "item_specification", "outer_id_of_goods", "item_properties"));

        SHORT_KEYS.add("item_specification_short_title"); // 快递单
        SHORT_KEYS.add("table_item_specification_short_title"); // 发货单
        SHORT_KEYS.add("table_sku_properties_short_title"); // 拣货单 收货单
        SHORT_KEYS.add("table_item_properties_short_title"); // 装箱清单，拿货单，采购单
        SHORT_KEYS.add("table_sku_short_titlefixed"); // 采购退货单

        // 总金额
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_item_payment");
        // 折后单价
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_item_discount_price");
        // 商品总金额
        PURCHASE_RETURN_PRIVILEGE_LIST.add("caig_return_total_amount");
        // 实付金额（含运费）
        PURCHASE_RETURN_PRIVILEGE_LIST.add("caig_return_payment");
        // 实退退货总金额
        PURCHASE_RETURN_PRIVILEGE_LIST.add("caig_actual_total_amount");
        // 单价
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_price");
        // 总价
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_total_amount");
        // 实退金额
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_actual_total_amount");
        // 折扣率
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_item_discount_rate");
        // 单价
        PURCHASE_RETURN_PRIVILEGE_LIST.add("table_item_price");
    }


    /**
     * 将虚拟物流的订单过滤掉
     */
    public static List<Trade> filterWlbTrade(Staff staff, List<Trade> trades) {
        List<Trade> list = new ArrayList<>(trades.size() + 1);
        for (Trade trade : trades) {
            // 电子面单
            /*if(trade.getTemplateType() != null && trade.getTemplateType() == 1){
                continue;
            }*/
            // 无需物流   TODO  templateId可能为-1
            if (trade.getTemplateId() != null && trade.getTemplateId() == 0) {
                continue;
            }

            list.add(trade);
        }
        return list;
    }

    /**
     * 创建打印记录日志对象
     *
     * @param printTime 打印时间
     * @param printer   打印机
     * @param printType 打印类型
     * @param waveId    波次号
     */
    public static PrintTradeLog createPrintTradeLog(Staff staff, Long[] sids, List<Trade> trades, Date printTime, String printer, Integer printType, Long waveId, Map<Long, CloudPrintTaskParam> cloudPrintTaskMap) {
        PrintTradeLog log = new PrintTradeLog();
        //记录打印记录的时候，打印机名称是限制64个字符超长的时候落库的需要截取
        if (printer != null && printer.length() > 64) {
            printer = printer.substring(0, 64);
        }
        log.setPrinter(printer);
        log.setPrintTime(printTime);
        log.setStaffId(staff.getId());
        log.setStaffName(staff.getName());
        log.setCompanyId(staff.getCompanyId());
        log.setDeliverPrinted(0);
        log.setLeftDeliverPrinted(sids.length);
        Trade t = trades.get(0);
        log.setIsWlb(t.getTemplateType());
        log.setTemplateId(t.getTemplateId());
        log.setLogisticsCompanyId(t.getLogisticsCompanyId());
        log.setType(printType == EnumPrintTradeType.WLB_TEMPLATE.getValue() ? EnumPrintTradeType.EXPRESS_TEMPLATE.getValue() : printType);
        log.setWarehouseId(t.getWarehouseId());
        Set<Long> waveIds = new HashSet<>();
        if (null != waveId) {
            waveIds.add(waveId);
        }
        for (Trade trade : trades) {
            if (null != trade.getWaveId() && trade.getWaveId() > 0) {
                waveIds.add(trade.getWaveId());
            }
        }
        if (waveIds.size() > 0) {
            log.setWaveIds(Strings.join(",", waveIds));
        }
        Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(trades);
        // 创建详情
        for (int i = 1; i <= sids.length; i++) {
            Long sid = sids[i - 1];
            if (!sidTradeMap.containsKey(sid)) {
                continue;
            }
            Trade trade = sidTradeMap.get(sid);
            PrintTradeLogDetail detail = new PrintTradeLogDetail();
            detail.setSeq(i);
            detail.setDeliverPrinted(0);
            detail.setOutSid(trade.getOutSid());
            detail.setSid(sid);
            detail.setTemplateId(trade.getTemplateId());
            detail.setTid(trade.getTid());
            detail.setShortId(trade.getShortId());
            detail.setPrintStatus(PrintStatusEnum.SUCCESS_PRINT.getType());
            if (MapUtils.isNotEmpty(cloudPrintTaskMap) && cloudPrintTaskMap.get(detail.getSid()) != null) {
                CloudPrintTaskParam cloudPrintTaskParam = cloudPrintTaskMap.get(detail.getSid());
                detail.setCloudTaskId(cloudPrintTaskParam.getCloudTaskId());
                detail.setCloudPrinterId(cloudPrintTaskParam.getPrinterId());
                detail.setPrintStatus(cloudPrintTaskParam.getPrintStatus());
                detail.setErrorMessage(cloudPrintTaskParam.getErrorMessage());
            }
            detail.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            log.getDetails().add(detail);
        }
        return log;
    }

    /**
     * 创建打印记录日志对象
     */
    public static List<PrintTradeLogDetail> createPrintTradeLogDetail(Staff staff, Long[] sids, List<Trade> trades) {
        Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(trades);

        List<PrintTradeLogDetail> details = new ArrayList<>();
        // 创建详情
        for (int i = 1; i <= sids.length; i++) {
            Long sid = sids[i - 1];
            if (!sidTradeMap.containsKey(sid)) {
                continue;
            }

            Trade trade = sidTradeMap.get(sid);
            PrintTradeLogDetail detail = new PrintTradeLogDetail();
            detail.setCompanyId(staff.getCompanyId());
            detail.setDeliverPrinted(0);
            detail.setOutSid(trade.getOutSid());
            detail.setSid(sid);
            detail.setTemplateId(trade.getTemplateId());
            detail.setTid(trade.getTid());
            detail.setShortId(trade.getShortId());
            details.add(detail);
        }
        return details;
    }

    /**
     * 由于淘宝系统的bug,这里有个偏移值,判断当前订单是否是单笔
     */
    //public static void __fixOffsetPrice(Staff staff, List<String> needValues, int printType, Trade trade, Map<String, Double> offsetNumMap, ITradeSearchService tradeQueryService) {
    //    List<String> list = new LinkedList<>();
    //
    //    if (printType == 0 && trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_TAO_BAO)) {
    //        TbTrade tbTrade = (TbTrade) trade;
    //        for (TbOrder tbOrder : tbTrade.getOrders()) {
    //            if (tbOrder.getOid() != null) {
    //                // 当oid等于tid的时候这笔订单一定是单笔状况
    //                if (tbOrder.getOid().equals(tbOrder.getTid())) {
    //                    list.add(tbOrder.getTid());
    //                }
    //            }
    //        }
    //        if (list.size() != 0) {
    //            List<TbTrade> tb = tradeQueryService.queryByTids(staff, false, list.toArray(new String[list.size()]));
    //            for (TbTrade __trade : tb) {
    //                if (list.contains(__trade.getTid()) && StringUtils.isNotEmpty(trade.getPostFee())) {
    //                    offsetNumMap.put(__trade.getTid(), Double.parseDouble(trade.getPostFee()));
    //                }
    //            }
    //            if (offsetNumMap.size() != 0)
    //                needValues.add("table_item_oid");
    //        }
    //    }
    //}

    /**
     * 过滤需要打印的子订单
     * <p>
     * 订单本身已发货已完成已关闭,所有子订单都可打印
     * 订单已审核,只能打印已审核的子订单 ⬇️⬇️
     * //主单订单状态为交易成功或者已发货的情况下，剔除交易关闭的子单
     * </p>
     */
    public static boolean __filterValidOrder(Trade tbTrade, boolean filterConsigned) {
        if ("DKPF".equals(tbTrade.getTradeFrom()) || tbTrade.isOutstock() || tbTrade.isCustomerTrade()) {
            //档口批发不过滤
            return false;
        }
        boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(tbTrade.getSysStatus());
        List<Order> filterList = new LinkedList<>();
        List<Order> orders = TradeUtils.getOrders4Trade(tbTrade);
        for (Order order : orders) {
            if (!isAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                logger.debug(String.format("打印时校验订单不合法,订单未发货子订单已发货。订单sid(%s),trade.sysStatus(%s),order.sysStatus(%s)", tbTrade.getSid(), tbTrade.getSysStatus(), order.getSysStatus()));
                continue;
            }
            if (filterConsigned && isAfterSendGoods && order.getSysConsigned() != 1) {
                logger.debug(String.format("打印时校验订单不合法,订单sid(%s),filterConsigned(%s),isAfterSendGoods(%s), order.SysConsigned(%s)", tbTrade.getSid(), filterConsigned, isAfterSendGoods, order.getSysConsigned()));
                continue;
            }
            String sysStatus = tbTrade.getSysStatus();
            if ((StringUtils.equals(Trade.SYS_STATUS_FINISHED, sysStatus) || StringUtils.equals(Trade.SYS_STATUS_SELLER_SEND_GOODS, sysStatus) || StringUtils.equals(Trade.SYS_STATUS_CLOSED, sysStatus))
                    && Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) && filterConsigned) {
                logger.debug(String.format("打印时校验订单不合法,子订单已关闭。订单sid(%s), trade.sysStatus(%s),order.sysStatus(%s)", tbTrade.getSid(), tbTrade.getSysStatus(), order.getSysStatus()));
                continue;
            }
            //子订单没有优惠金额则写成0
            if (StringUtils.isBlank(order.getDiscountFee())) {
                order.setDiscountFee("0.0");
            }
            filterList.add(order);
        }
        TradeUtils.setOrders(tbTrade, filterList);
        return filterList.size() == 0;
    }


    /**
     * 处理系统订单
     */
    public static void __filterSysTrade(Trade trade) {
        if (StringUtils.isEmpty(trade.getBuyerNick())) {
            trade.setBuyerNick(trade.getReceiverName());
        }
    }

    /**
     * 合单 卖家、买家  备注合并
     */
    public static void __repliceWithMemos(List<String> needValues,
                                          Trade tbTrade, Map<String, Object> result) {
        if (needValues.contains("trade_buyer_message")) {
            // TODO 这段逻辑还得重构下，另外ERP里面还有个系统备注，这个字段也要考虑进去 @左韵
            StringBuilder sb = new StringBuilder();
            if (tbTrade.getMessageMemos() != null) {
                for (MessageMemo messageMemo : tbTrade.getMessageMemos()) {
                    if (StringUtils.isNotEmpty(messageMemo.getBuyerMessage()) && !Trade.SYS_STATUS_CLOSED.equals(messageMemo.getSysStatus())) {
                        sb.append(messageMemo.getBuyerMessage()).append(";");
                    }
                }
                result.put("trade_buyer_message", CommaReplaceUtils.commaReplace(sb.toString()));
            } else {
                result.put("trade_buyer_message", CommaReplaceUtils.commaReplace(tbTrade.getBuyerMessage() !=
                        null ? tbTrade.getBuyerMessage() : ""));
            }
        }
        if (needValues.contains("trade_seller_memo")) {
            StringBuilder sb = new StringBuilder();
            if (tbTrade.getMessageMemos() != null) {
                for (MessageMemo messageMemo : tbTrade.getMessageMemos()) {
                    //SysConsigned 为2为其他erp发货，子订单备注不展示
                    if (StringUtils.isNotEmpty(messageMemo.getSellerMemo()) && !Trade.SYS_STATUS_CLOSED.equals(messageMemo.getSysStatus()) && !Objects.equals(messageMemo.getSysConsigned(), 2)) {
                        sb.append(messageMemo.getSellerMemo()).append(";");
                    }
                }
                result.put("trade_seller_memo", CommaReplaceUtils.commaReplace(sb.toString()));
            } else {
                result.put("trade_seller_memo", CommaReplaceUtils.commaReplace(tbTrade.getSellerMemo() != null ? tbTrade.getSellerMemo() : ""));
            }
        }
    }

    /**
     * 格式化发货时间
     */
    public static void __formatWithConsignTime(List<String> needValues,
                                               Trade tbTrade, Map<String, Object> result) {
        DateTime date;
        if (tbTrade.getConsignTime() == null)
            date = DateTime.now();
        else if (new DateTime(tbTrade.getConsignTime()).toString("yyyy-MM-dd").equals("2000-01-01")) {
            date = DateTime.now();
        } else
            date = new DateTime(tbTrade.getConsignTime());

        if (needValues.contains("send_date_day_up") || needValues.contains("send_date_day_down")) {
            result.put("send_date_day_down", date.toString("yyyy-MM-dd"));
            result.put("send_date_day_up", date.toString("yyyy-MM-dd"));
        }
        if (needValues.contains("send_date_up") || needValues.contains("send_date_down") || needValues.contains("send_date")) {
            result.put("send_date_down", date.toString("yyyy-MM-dd HH:mm:ss"));
            result.put("send_date_up", date.toString("yyyy-MM-dd HH:mm:ss"));
            result.put("send_date", date.toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("trade_deliver_time")) {
            result.put("trade_deliver_time", date.toDate().getTime());
        }
    }

    /**
     * 替换商品数量为商品总数量
     */
    public static void __replaceItemNumWithItemNumTotal(Map<String, Object> result) {
        if (result.containsKey("item_num_total") && result.containsKey("item_num")) {
            result.put("item_num", result.get("item_num_total"));
        }
    }

    /**
     * 对字段进行字符串化
     */
    public static void __toString4Result(String str, Map<String, Object> result) {
        if (result.containsKey(str)) {
            result.put(str, result.get(str).toString());
        }
    }

    /**
     * 数据在返回前对订单做一个排序，按照sids顺序出口
     */
    public static List<Map<String, Object>> getResultsBySidSort(Map<Long, Map<String, Object>> results, Long[] sids) {

        List<Map<String, Object>> elements = new ArrayList<>();
        // 空值检查
        if (sids == null) {
            return elements;
        }
        for (Long sid : sids) {
            Map<String, Object> result = results.get(sid);
            if (result == null) {
                continue;
            }
            // sid为重要数据多处需要使用不要删除
            result.put("sid", sid);
            elements.add(result);
        }
        return elements;
    }

    /**
     * 数据在返回前做一个排序， 按照 sids 顺序出口
     */
    public static List<Map<String, Object>> getResultsBySidSort(Map<String, Map<String, Object>> results, String[] sids) {
        List<Map<String, Object>> elements = new ArrayList<>();
        // 空值检查
        if (sids == null) {
            return elements;
        }
        for (String sid : sids) {
            Map<String, Object> result = results.get(sid);
            if (result == null) {
                continue;
            }
            elements.add(result);
        }
        return elements;
    }

    /**
     * 在将要获取字段值之前先检查订单的状态合法性
     */
    public static boolean getFieldValueCheckBefore(Trade singleTrade) {
        if (PrintTemplateHelper.__filterValidOrder(singleTrade, false)) {
            return false;
        }
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(singleTrade.getSource())) {
            PrintTemplateHelper.__filterSysTrade(singleTrade);
        }
        return true;
    }

    public static boolean getFieldValueCheckBefore(Trade singleTrade, boolean filterConsigned) {
        if (PrintTemplateHelper.__filterValidOrder(singleTrade, filterConsigned)) {
            return false;
        }
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(singleTrade.getSource())) {
            PrintTemplateHelper.__filterSysTrade(singleTrade);
        }
        return true;
    }

    /**
     * 在获取完字段值之后进行相关字段的处理
     */
    public static void getFieldValueProcessAfter(Map<String, Object> result) {
        // 条形码问题
        __toString4Result("trade_id", result);
        __toString4Result("trade_id_up", result);
        __toString4Result("trade_id_down", result);
        __toString4Result("trade_barcode", result);
        __toString4Result("trade_barcode_up", result);
        __toString4Result("trade_barcode_down", result);
    }

    /**
     * 是否需要商品种类
     */
    public static void __ifNeedItemCateNum(List<String> needValues, Trade tbTrade, Map<String, Object> result) {
        // 增加一个字段item_cate_num，为商品种类数量
        if (needValues.contains("item_cate_num")) {
            if (result.get("item_cate_num") == null) {
                Orderable<Order> orderable = (Orderable<Order>) tbTrade;
                result.put("item_cate_num", orderable.getOrders().size());
            }
        }
    }

    /**
     * 是否需要订单打印时间
     * 前端自己取当前时间 无需后端给值
     */
    public static void __ifNeedTradePrintTime(List<String> needValues, Map<String, Object> result) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (needValues.contains("print_time")) {
            result.put("print_time", DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("print_time")) {
            result.put("trade_print_time", DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("sh_print_time")) {
            result.put("sh_print_time", DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("caig_print_time")) {
            result.put("caig_print_time", DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("trade_print_time_day")) {
            result.put("trade_print_time_day", DateTime.now().toString("yyyy-MM-dd"));
        }
        if (needValues.contains("trade_print_time_up")) {
            result.put("trade_print_time_up", DateTime.now().toString("yyyy-MM-dd HH:mm"));
            result.put("trade_print_time_down", DateTime.now().toString("yyyy-MM-dd HH:mm"));
        }
        if (needValues.contains("caig_return_print_time")) {
            result.put("caig_return_print_time", DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (needValues.contains("caig_return_out_time") && result.get("caig_return_out_time") instanceof Date) {
            result.put("caig_return_out_time", df.format(result.get("caig_return_out_time")));
        }
        if (needValues.contains("caig_create_time") && result.get("caig_create_time") instanceof Date) {
            result.put("caig_create_time", df.format(result.get("caig_create_time")));
        }
        if (needValues.contains("sh_bill_date") && result.get("sh_bill_date") instanceof Date) {
            result.put("sh_bill_date", new SimpleDateFormat("yyyy-MM-dd").format(result.get("sh_bill_date")));
        }
        if (needValues.contains("delivery_date") && result.get("delivery_date") instanceof Date) {
            result.put("delivery_date", new SimpleDateFormat("yyyy-MM-dd").format(result.get("delivery_date")));
        }
    }

    /**
     * 获取快递大头笔
     */
    public static void __repliceWithExpressShortAddress(List<String> needValues, Map<String, OutSidPool> outSidMap, Trade tbTrade, Map<String, Object> result) {
        String outSid = tbTrade.getOutSid();
        if (needValues.contains("express_short_address")) {
            if (outSidMap != null && outSidMap.containsKey(outSid)) {
                OutSidPool outSidPool = outSidMap.get(outSid);
                String shortAddress = outSidPool.getShortAddress();
                if (StringUtils.isNotEmpty(shortAddress)) {
                    result.put("express_short_address", outSidPool.getShortAddress());
                } else if (result.containsKey("express_short_address")) {
                    result.put("express_short_address", Provice2ShortUtils.convert(result.get("express_short_address").toString()));
                }
            } else if (result.containsKey("express_short_address")) {
                result.put("express_short_address", Provice2ShortUtils.convert(result.get("express_short_address").toString()));
            } else {
                throw new TradeException("调取大头笔信息数据出错");
            }
        }
    }


    /**
     * 替换集包邮地
     */
    public static void __replacePackageCenter(List<String> needValues, Map<String, OutSidPool> outSidMap, Trade trade, Map<String, Object> result) {
        if (outSidMap == null) {
            return;
        }
        OutSidPool outSidPool = outSidMap.get(String.valueOf(trade.getOutSid()));
        if (outSidPool == null) {
            return;
        }
        if (needValues.contains("package_center_name")) {
            result.put("package_center_name", com.raycloud.dmj.tve.utils.StringUtils.nvl(outSidPool.getPackageCenterName()));
        }
        if (needValues.contains("package_center_code")) {
            result.put("package_center_code", com.raycloud.dmj.tve.utils.StringUtils.nvl(outSidPool.getPackageCenterCode()));
        }
        if (needValues.contains("print_config")) {
            result.put("print_config", com.raycloud.dmj.tve.utils.StringUtils.nvl(outSidPool.getPrintConfig()));
        }
        if (needValues.contains("seller_id")) {
            result.put("seller_id", com.raycloud.dmj.tve.utils.StringUtils.nvl(outSidPool.getTaobaoId()));
        }
    }

    /**
     * 对目的地省市区等字段赋值
     */
    public static void __repliceWithShortProvice(Map<String, Object> result) {
        if (result.containsKey("receiver_destination")) {
            result.put("receiver_destination", Provice2ShortUtils.convert(result.get("receiver_destination").toString()));
        }
        if (result.containsKey("receiver_state_city_district_simple_up")) {
            result.put("receiver_state_city_district_simple_up", result.get("receiver_state_city_district_simple_up").toString().replaceAll(REGEX_SHORT_ADDRESS, ""));
        }

        if (result.containsKey("receiver_state_city_district_simple_down")) {
            result.put("receiver_state_city_district_simple_down", result.get("receiver_state_city_district_simple_down").toString().replaceAll(REGEX_SHORT_ADDRESS, ""));
        }
    }

    /**
     * 处理商品属性
     */
    public static void __splitEnterWithItemProperties(List<String> needValues, Map<String, Object> result) {
        if (needValues.contains("item_properties")) {
            result.put("item_properties", result.get("item_properties").toString().replaceAll(";", "<br/>"));
        }
    }

    /**
     * 初始化
     */
    public static void __initFieldSettingsCustom(Map<String, Object> result, UserWlbExpressTemplate userWlbExpressTemplate) {
        List<String> fieldNameList = Collections.singletonList("custom_qrcode");
        Map<String, String> fieldValueMap = WlbExpressHelper.getFieldValueMap(userWlbExpressTemplate, fieldNameList);
        result.putAll(fieldValueMap);
    }

    /**
     * 初始化代付金额和到付金额
     */
    public static void __initServCodAmount(List<String> needValues, Map<String, Object> result, Trade trade, Map<Long, String> sidCodmaop) {
        String columnPayment = sidCodmaop.get(trade.getSid());
        if (needValues.contains("ali_waybill_serv_cod_amount")) {
            result.put("ali_waybill_serv_cod_amount", columnPayment);
        }
        if (needValues.contains("ali_waybill_serv_dest_amount")) {
            result.put("ali_waybill_serv_dest_amount", columnPayment);
        }

    }

    /**
     * 得到代收金额
     */
    public static Map<Long, String> getCodMap(List<Trade> trades, ITradeTagService tradeTagService, IUserService userService, Map<Long, User> idUserMap) {
        Map<Long, String> sidCodMap = new HashMap<>();
        for (Trade trade : trades) {
//            if ("cod".equalsIgnoreCase(trade.getType()) || "jd-1".equalsIgnoreCase(trade.getType())) {
//                String columnPayment = getPaymentForJps(trade, tradeTagService, userService, idUserMap);
//                sidCodMap.put(trade.getSid(), columnPayment);
//            } else {
//                sidCodMap.put(trade.getSid(), "0.00");
//}
            sidCodMap.put(trade.getSid(), "0.00");
        }
        return sidCodMap;
    }

    public static boolean isValidateJps(Trade trade) {
        if (trade == null || trade.getTagIds() == null || "".equals(trade.getTagIds())) {
            return false;
        }
        if (trade.getTagIds().contains(String.valueOf(TAG_JD_JPS.getId()))) {
            return true;
        }
        return false;
    }

    private static String getPaymentForJps(Trade trade, ITradeTagService tradeTagService, IUserService userService, Map<Long, User> idUserMap) {
        double amount = calculateColumnPayment(trade);
        String shouldPay = null;
        if (!isValidateJps(trade)) {
            shouldPay = String.valueOf(amount);
        } else {
            User user = idUserMap.get(trade.getUserId());
            if (user == null) {
                user = userService.queryById(trade.getUserId());
                if (user == null) {
                    return "0.00";
                }
                idUserMap.put(user.getId(), user);
            }
            try {
                logger.debug(LogHelper.buildLogHead(user).append(String.format("京品试订单【%s】获取支付金额", trade.getSid())));
                PopOrderPrintDataGetRequest request = new PopOrderPrintDataGetRequest();
                request.setOrderId(trade.getTid());
                PopOrderPrintDataGetResponse response = new JdClientHelper(user).request(request);
                shouldPay = response.getGetorderprintdataResult().getApiOrderPrintResult().getShouldPay();
            } catch (Exception e) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(user).append("京品试订单获取支付金额失败 ：").append(e.getMessage()));
                }
            }
        }
        return null != shouldPay ? shouldPay : "0.00";
    }

    /**
     * 初始化商家编码+数量
     */
    public static void buildJdOuterIdNum(List<String> needValues, Map<String, Object> result, Trade trade) {
        if (needValues.contains("jd_express_outerid_num")) {
            StringBuilder outerIdNum = new StringBuilder();
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    for (Order suit : order.getSuits()) {
                        outerIdNum.append(suit.getSysOuterId()).append(" 【").append(suit.getNum()).append("】,");
                    }
                } else {
                    outerIdNum.append(order.getSysOuterId()).append(" 【").append(order.getNum()).append("】,");
                }
            }
            if (outerIdNum.length() > 0) {
                outerIdNum.deleteCharAt(outerIdNum.length() - 1);
            }
            result.put("jd_express_outerid_num", outerIdNum.toString());
        }
    }

    /**
     * 处理合单的数据
     */
    public static List<Trade> _removeRepeatTrade(List<Trade> tbTrades) {
        try {
            for (Trade tbTrade : tbTrades) {
                List<Order> orders = TradeUtils.getOrders4Trade(tbTrade);
                Map<Long, MessageMemo> messageMemoMap = new HashMap<>();
                if (TradeUtils.printIsMerge(tbTrade) && CollectionUtils.isNotEmpty(tbTrade.getMessageMemos())) {
                    List<MessageMemo> messageMemos = tbTrade.getMessageMemos();
                    messageMemoMap = messageMemos.stream().collect(Collectors.toMap(MessageMemo::getSid, m -> m, (m1, m2) -> m1));
                    tbTrade.setSellerMemo(messageMemos.stream().map(MessageMemo::getSellerMemo).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";")));
                    tbTrade.setBuyerMessage(messageMemos.stream().map(MessageMemo::getBuyerMessage).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";")));
                    tbTrade.setSysMemo(messageMemos.stream().map(MessageMemo::getSysMemo).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";")));
                }
                orderFillTradeMsg(orders, messageMemoMap, tbTrade);
            }
        } catch (Exception e) {
            Logs.error("处理合单失败：" + e.getMessage(), e);
        }
        //如果为空，或者订单只有一条则直接返回
        if (CollectionUtils.isEmpty(tbTrades) || tbTrades.size() == 1) {
            return tbTrades;
        }
        //下面的逻辑应该没用了
        //Map存储合单数量，以taobao_id + sid方式过滤
        Map<String, Integer> tbTradeMap = new HashMap<>();
        for (Trade tbTrade : tbTrades) {
            if (TradeUtils.printIsMerge(tbTrade)) {
                String key = tbTrade.getTaobaoId() + "_" + tbTrade.getSid();
                int value = tbTradeMap.get(key) == null ? 0 : tbTradeMap.get(key);
                tbTradeMap.put(key, value + 1);
            }
        }
        //获取数量大于1的合单数据
        for (String key : tbTradeMap.keySet()) {
            Integer value = tbTradeMap.get(key);
            String[] keys = key.split("_");
            //说明出现合单重复，从List中移除重复的合单
            if (value > 1) {
                Iterator<Trade> iter = tbTrades.iterator();
                while (iter.hasNext()) {
                    Trade trade = iter.next();
                    if (trade.getTaobaoId().equals(Long.valueOf(keys[0])) && trade.getMergeSid().equals(Long.valueOf(keys[1]))) {
                        iter.remove();
                        value--;
                        //移除剩余一条后，终止循环
                        if (value == 1)
                            break;
                    }
                }
            }
        }
        return tbTrades;
    }

    /**
     * 订单填充部分信息到子订单
     *
     * @param messageMemoMap 合单的子单信息 包含主单的
     */
    private static void orderFillTradeMsg(List<Order> orders, Map<Long, MessageMemo> messageMemoMap, Trade trade) {
        for (Order order : orders) {
            MessageMemo messageMemo = messageMemoMap.get(order.getSid());
            if (messageMemo != null && (StringUtils.isNotBlank(messageMemo.getSellerMemo()) || StringUtils.isNotBlank(messageMemo.getBuyerMessage()))) {
                order.setTradeBuyerMessage(messageMemo.getBuyerMessage());
                order.setTradeSellerMemo(messageMemo.getSellerMemo());
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    for (Order suit : order.getSuits()) {
                        suit.setTradeBuyerMessage(messageMemo.getBuyerMessage());
                        suit.setTradeSellerMemo(messageMemo.getSellerMemo());
                    }
                }
            } else if (StringUtils.isNotBlank(trade.getSellerMemo()) || StringUtils.isNotBlank(trade.getBuyerMessage())) {
                order.setTradeBuyerMessage(trade.getBuyerMessage());
                order.setTradeSellerMemo(trade.getSellerMemo());
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    for (Order suit : order.getSuits()) {
                        suit.setTradeBuyerMessage(trade.getBuyerMessage());
                        suit.setTradeSellerMemo(trade.getSellerMemo());
                    }
                }
            }
        }


    }

    /**
     * 把多个字段的拆分开来进行单个字段的获取，后面再进行组装
     */
    public static List<String> filterSplitNeedValue(List<String> needValues) {
        Set<String> set = new HashSet<>(needValues);
        for (String needValue : needValues) {
            if (needValue.contains("-")) {
                for (String part : needValue.split("-")) {
                    set.add(part);
                }
            }
        }
        return new ArrayList<>(set);
    }

    /**
     * 替换order里面的逗号
     */
    public static void _replaceOrderComma(List<Trade> trades) {
        for (Trade trade : trades) {
            if (!(trade instanceof TbTrade)) {
                continue;
            }

            TbTrade tbTrade = (TbTrade) trade;
            List<TbOrder> tbOrders = tbTrade.getOrders();
            if (tbOrders == null || tbOrders.size() == 0) {
                continue;
            }
            for (TbOrder tbOrder : tbOrders) {
                if (tbOrder.getTitle() != null && tbOrder.getTitle().contains(",")) {
                    tbOrder.setTitle(tbOrder.getTitle().replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getSkuPropertiesName())) {
                    tbOrder.setSkuPropertiesName(com.raycloud.dmj.domain.trades.utils.DmjSkuUtils.formatProprotyName(tbOrder.getSkuPropertiesName(), "&#44;").replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getOuterIid())) {
                    tbOrder.setOuterIid(tbOrder.getOuterIid().replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getOuterSkuId())) {
                    tbOrder.setOuterSkuId(tbOrder.getOuterSkuId().replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getSysOuterId())) {
                    tbOrder.setSysOuterId(tbOrder.getSysOuterId().replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getSysSkuPropertiesName())) {
                    tbOrder.setSysSkuPropertiesName(com.raycloud.dmj.domain.trades.utils.DmjSkuUtils.formatProprotyName(tbOrder.getSysSkuPropertiesName(), "&#44;")
                            .replace(",", "&#44;"));
                }
                if (StringUtils.isNotEmpty(tbOrder.getSysSkuPropertiesAlias())) {
                    tbOrder.setSysSkuPropertiesAlias(com.raycloud.dmj.domain.trades.utils.DmjSkuUtils.formatProprotyName(tbOrder.getSysSkuPropertiesAlias(), "&#44;").replace(",", "&#44;"));
                }
            }
        }
    }

    /**
     * 是否需要打印时间
     */
    public static void __ifNeedTradePrintTime4Wlb(List<String> needValues, Map<String, Object> result) {
        if (needValues.contains("trade_print_time_up")) {
            result.put("trade_print_time_up", DateTime.now().toString("yyyy-MM-dd HH:mm"));
            result.put("trade_print_time_down", DateTime.now().toString("yyyy-MM-dd HH:mm"));
        }
    }

    /**
     * 得到仓库的模版信息
     */
    public static WarehouseTemplate getWarehouseTemplate(Staff staff, Map<String, WarehouseTemplate> warehouseTemplateCache,
                                                         IWarehouseTemplateService warehouseTemplateService, IUserWlbExpressTemplateService userWlbExpressTemplateService, Long warehouseId, Long templateId, Integer templateType) {

        String cacheKey = warehouseId + "_" + templateId + "_" + templateType;
        WarehouseTemplate warehouseTemplate = warehouseTemplateCache.get(cacheKey);
        if (warehouseTemplate == null) {
            warehouseTemplate = warehouseTemplateService.getWarehouseTemplateWithTemplate(staff, warehouseId,
                    templateId, templateType);
            if (warehouseTemplate == null) {
                //为空 先 查下这边的所有模板 去匹配仓库绑定的模板
                List<Long> templateIds = userWlbExpressTemplateService.getGroupTemplateIdsById(staff, templateId);
                if (CollectionUtils.isNotEmpty(templateIds)) {
                    warehouseTemplate = warehouseTemplateService.getOneByTemplateIds(staff, warehouseId, templateIds, templateType);
                    logger.debug(LogHelper.buildLog(staff, "仓库id:" + warehouseId + ",模板id:" + templateId + ",未找到仓库绑定关系,补偿 查询到同类模板id:" + JSONObject.toJSONString(templateIds) + (warehouseTemplate == null ? ",未查询到仓库绑定数据." : ",查询到仓库绑定数据.")));
                } else {
                    logger.debug(LogHelper.buildLog(staff, "仓库id:" + warehouseId + ",模板id:" + templateId + ",未找到仓库绑定关系,补偿未查询到同类模板!"));
                }
            }
            if (warehouseTemplate == null) {
                throw new IllegalArgumentException("找不到模版绑定关系，请确认模版是否被仓库解除绑定.");
            }
            warehouseTemplateCache.put(cacheKey, warehouseTemplate);
        }

        return warehouseTemplate;
    }

    /**
     * 把店铺的地址赋值给网点地址，避免打印的时候地址都找不到
     */
    public static TemplateBranchAddress initTemplateBranchAddress(Shop shop) {
        if (shop == null) {
            return new TemplateBranchAddress();
        }
        TemplateBranchAddress address = new TemplateBranchAddress();
        address.setProvince(com.raycloud.dmj.tve.utils.StringUtils.nvl(shop.getSendState()));
        address.setCity(com.raycloud.dmj.tve.utils.StringUtils.nvl(shop.getSendCity()));
        address.setCountry(com.raycloud.dmj.tve.utils.StringUtils.nvl(shop.getSendDistrict()));
        address.setAddress(com.raycloud.dmj.tve.utils.StringUtils.nvl(shop.getSendAddress()));
        return address;
    }

    /**
     * 得到order的图片，先取系统图片，没有的话取平台图片
     */
    public static String getOrderPicPath(Order order) {
        if (order == null) {
            return "";
        }
        String picPath = order.getSysPicPath();
        if (StringUtils.isNotEmpty(picPath)) {
            return picPath;
        }

        return order.getPicPath();
    }

    public static void __ifNeedOuterIdOfItem(List<String> needValues, Trade singleTrade, Map<Long, String> itemOuterIdMap, Map<String, Object> result) {
        // 增加一个字段outer_id_of_item，为商品商家编码
        if (needValues.contains("outer_id_of_item")) {
            if (singleTrade == null) {
                return;
            }
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }

            StringBuilder sb = new StringBuilder();
            for (Order order : orderList) {
                if (order.getItemSysId() <= 0) {
                    //未匹配商品取平台的值
                    sb.append(CommaReplaceUtils.commaReplace(StringUtils.isNotEmpty(order.getSysOuterId()) ? order.getSysOuterId() : order.getOuterIid()));
                } else if (order.getSkuSysId() <= 0 && StringUtils.isNotBlank(order.getSysOuterId())) {
                    sb.append(CommaReplaceUtils.commaReplace(order.getSysOuterId()));
                } else {
                    sb.append(CommaReplaceUtils.commaReplace(itemOuterIdMap.get(order.getItemSysId())));
                }
                sb.append(",");
            }

            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            result.put("outer_id_of_item", sb.toString());
        }
    }

    /**
     * 添加规格简称字段
     */
    public static void __ifNeedPropertiesAliasOfItem(List<String> needValues, Trade singleTrade, Map<Long, DmjItem> idToDmjItem, Map<Long, DmjSku> idToDmjSku, Map<String, Object> result) {
        if (!needValues.contains("table_item_specification_short_title")) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder sb = new StringBuilder();
        for (Order order : orders) {
            Long itemSysId = order.getItemSysId();
            DmjItem item = idToDmjItem.get(itemSysId);
            if (item != null) {
                Long skuSysId = order.getSkuSysId();
                String shortTitle;
                DmjSku sku;
                if (skuSysId != null && skuSysId > 0 && (sku = idToDmjSku.get(skuSysId)) != null) {
                    //若对应商品是规格商品，则取对应规格商品的规格简称；若其未维护规格简称，则取对应主商品的商品简称
                    String skuShortTitle = StringUtils.isBlank(sku.getShortTitle()) ? item.getShortTitle() : sku.getShortTitle();
                    //若其主商品未维护商品简称，则取规格商品的规格名称
                    shortTitle = StringUtils.isBlank(skuShortTitle) ? sku.getTitle() : skuShortTitle;
                    // 如果规格都没有,则取规格别名
                    shortTitle = StringUtils.isBlank(shortTitle) ? sku.getPropertiesName() : shortTitle;
                } else {
                    //若对应商品是纯商品或不含sku套件，则取对应商品的商品简称；若未维护商品简称，则取商品名称。
                    shortTitle = StringUtils.isBlank(item.getShortTitle()) ? item.getTitle() : item.getShortTitle();
                }
                sb.append(shortTitle).append(",");
            } else {
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        result.put("table_item_specification_short_title", sb.toString());
    }

    /**
     * 添加商品箱规、商品箱数、商品剩余散装数
     * 【商品箱规】---商品档案中的箱规。没有箱规则为空。
     * 【商品箱数】---商品数量除以商品箱规后取整数。
     * 【商品剩余散装数】----商品数量除以商品箱规后的余数。
     */
    public static void ifNeedBoxNum(List<String> needValues, Trade singleTrade, Map<Long, DmjItem> idToDmjItem, Map<Long, DmjSku> idToDmjSku, Map<String, Object> result) {
        if (!needValues.contains("table_boxnum") && !needValues.contains("table_num_of_box") && !needValues.contains("table_remain_bulk_Num") &&
                !needValues.contains("item_num_of_box_total") && !needValues.contains("item_remain_bulk_num_total")) {
            return;
        }
        // 商品箱规
        StringBuilder boxNumStr = new StringBuilder();
        // 商品箱数
        StringBuilder numOfBoxStr = new StringBuilder();
        // 商品剩余散装数
        StringBuilder remainBulkNumStr = new StringBuilder();

        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        int numOfBoxTotal = 0;
        int remainBulkNumTotal = 0;
        for (Order order : orders) {
            DmjItem item = idToDmjItem.get(order.getItemSysId());
            DmjSku sku = idToDmjSku.get(order.getSkuSysId());
            Integer itemBoxNum = item != null && item.getBoxnum() != null ? item.getBoxnum() : null;
            Integer skuBoxNum = sku != null && sku.getBoxnum() != null ? sku.getBoxnum() : null;
            Integer boxNum = skuBoxNum != null ? skuBoxNum : itemBoxNum;
            if (boxNum != null && !Objects.equals(0, boxNum)) {
                int num = order.getNum() == null ? 0 : order.getNum();
                boxNumStr.append(boxNum).append(",");
                int numOfBox = new BigDecimal(num).divide(new BigDecimal(boxNum), RoundingMode.DOWN).intValue();
                numOfBoxStr.append(numOfBox).append(",");
                int remainBulkNum = num % boxNum;
                remainBulkNumStr.append(remainBulkNum).append(",");
                numOfBoxTotal += numOfBox;
                remainBulkNumTotal += remainBulkNum;
            } else {
                boxNumStr.append(",");
                numOfBoxStr.append(",");
                remainBulkNumStr.append(",");
            }
        }
        if (boxNumStr.length() > 0) {
            boxNumStr.deleteCharAt(boxNumStr.length() - 1);
        }
        if (numOfBoxStr.length() > 0) {
            numOfBoxStr.deleteCharAt(numOfBoxStr.length() - 1);
        }
        if (remainBulkNumStr.length() > 0) {
            remainBulkNumStr.deleteCharAt(remainBulkNumStr.length() - 1);
        }
        result.put("table_boxnum", boxNumStr.toString());
        result.put("table_num_of_box", numOfBoxStr.toString());
        result.put("table_remain_bulk_Num", remainBulkNumStr.toString());
        result.put("item_num_of_box_total", numOfBoxTotal);
        result.put("item_remain_bulk_num_total", remainBulkNumTotal);
    }

    /**
     * 添加箱规、箱数、散装数
     * @param needValues
     * @param warehouseEntry
     * @param itemIdMap
     * @param result
     */
//    public static void ifNeedBoxNum(List<String> needValues, WarehouseEntry warehouseEntry, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap, Map<String, Object> result) {
//        if (!needValues.contains("table_boxnum") && !needValues.contains("table_num_of_box") && !needValues.contains("table_bulk_num")) {
//            return;
//        }
//        List<WarehouseEntryDetail> details = warehouseEntry.getDetails();
//        if (CollectionUtils.isEmpty(details)) {
//            return;
//        }
//
//        StringBuilder tableBoxnum = new StringBuilder();
//        StringBuilder tableNumOfBox = new StringBuilder();
//        StringBuilder tableBulkNum = new StringBuilder();
//        for (WarehouseEntryDetail detail : details) {
//            DmjItem item = itemIdMap.get(detail.getItemSysId());
//            DmjSku sku = skuIdMap.get(detail.getSkuSysId());
//            // 商品箱规
//            String itemBoxNum = item != null && item.getBoxnum() != null ? String.valueOf(item.getBoxnum()) : "";
//            // 规格箱规
//            String skuBoxNum = sku != null && sku.getBoxnum() != null ? String.valueOf(sku.getBoxnum()) : "";
//
//            // 箱数
//            String numStr = "";
//            Long numOfBox = Optional.ofNullable(detail.getNumOfBox()).orElse(0L);
//            // 箱规
//            String numPerBoxStr = "";
//            Long numPerBox = Optional.ofNullable(detail.getNumPerBox()).orElse(0L);
//            // 散装数
//            Long bulkNum = Optional.ofNullable(detail.getBulkNum()).orElse(0L);
//            String bulkNumStr = String.valueOf(bulkNum);
//            // 收货数
//            Long quantity = detail.getQuantity();
//
//            if (numPerBox > 0L && numOfBox > 0L) { // 箱规、箱数都有值
//                numStr = String.valueOf(numOfBox);
//                numPerBoxStr = String.valueOf(numPerBox);
//                if (bulkNum == 0L) {
//                    bulkNumStr = String.valueOf(quantity % numPerBox);
//                }
//            } else if (numPerBox > 0L && numOfBox == 0L) {   // 若只有箱规有值，则箱数=收货数量/箱规；箱数向上取整。
//                numPerBoxStr = String.valueOf(numPerBox);
//                numStr = new BigDecimal(quantity).divide(new BigDecimal(numPerBox), RoundingMode.DOWN).toPlainString();
//                if (bulkNum == 0L) {
//                    bulkNumStr = String.valueOf(quantity % numPerBox);
//                }
//            } else if (numOfBox > 0L && numPerBox == 0L) {  // 若只有箱数有值，则箱数取收货单上的，箱规取商品信息上的箱规，若商品上箱规为空，则箱规为空。
//                numStr = String.valueOf(numOfBox);
//                numPerBoxStr = StringUtils.isNotBlank(skuBoxNum) ? skuBoxNum : itemBoxNum;
//                if (bulkNum == 0L && StringUtils.isNotBlank(numPerBoxStr) && !"0".equals(numPerBoxStr)) {
//                    bulkNumStr = String.valueOf(quantity % Long.parseLong(numPerBoxStr));
//                }
//            } else if (numOfBox == 0L && numPerBox == 0L) {  // 收货单上箱规、箱数都没有，则取商品信息上的箱规，箱数=收货数量/箱规；箱数向上取整。
//                numPerBoxStr = StringUtils.isNotBlank(skuBoxNum) ? skuBoxNum : itemBoxNum;
//                if (StringUtils.isNotBlank(numPerBoxStr) && !"0".equals(numPerBoxStr)) {
//                    numStr = new BigDecimal(quantity).divide(new BigDecimal(numPerBoxStr), RoundingMode.DOWN).toPlainString();
//                    if (bulkNum == 0L) {
//                        bulkNumStr = String.valueOf(quantity % Long.parseLong(numPerBoxStr));
//                    }
//                }
//            }
//            tableBoxnum.append(numPerBoxStr).append(",");
//            tableNumOfBox.append(numStr).append(",");
//            tableBulkNum.append(bulkNumStr).append(",");
//        }
//        // 箱规
//        setFieldValue(result, needValues, "table_boxnum", tableBoxnum);
//        // 箱数
//        setFieldValue(result, needValues, "table_num_of_box", tableNumOfBox);
//        // 散装数
//        setFieldValue(result, needValues, "table_bulk_num", tableBulkNum);
//    }

    /**
     * 添加箱规、箱数、散装数
     */
    public static void ifNeedBoxNum(List<String> needValues, WarehouseEntry warehouseEntry, Map<String, Object> result) {
        if (!needValues.contains("table_boxnum") && !needValues.contains("table_num_of_box") && !needValues.contains("table_bulk_num")) {
            return;
        }
        List<WarehouseEntryDetail> details = warehouseEntry.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        StringBuilder tableBoxnum = new StringBuilder();
        StringBuilder tableNumOfBox = new StringBuilder();
        StringBuilder tableBulkNum = new StringBuilder();
        for (WarehouseEntryDetail detail : details) {
            // 箱规
            String numPerBox = Optional.ofNullable(detail.getNumPerBoxStr()).orElse("0");
            // 箱数
            Long numOfBox = Optional.ofNullable(detail.getNumOfBox()).orElse(0L);
            // 散装数
            Long bulkNum = Optional.ofNullable(detail.getBulkNum()).orElse(0L);

            tableBoxnum.append(numPerBox).append(",");
            tableNumOfBox.append(numOfBox).append(",");
            tableBulkNum.append(bulkNum).append(",");
        }
        // 箱规
        setFieldValue(result, needValues, "table_boxnum", tableBoxnum);
        // 箱数
        setFieldValue(result, needValues, "table_num_of_box", tableNumOfBox);
        // 散装数
        setFieldValue(result, needValues, "table_bulk_num", tableBulkNum);
    }

    /**
     * 计算箱规、箱数、散装数
     * https://gykj.yuque.com/entavv/xb9xi5/ximml5a8h9avviyi
     */
    public static void setBoxNum(List<String> needValues, WarehouseEntry warehouseEntry, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap) {
        if (!needValues.contains("table_boxnum") && !needValues.contains("table_num_of_box") && !needValues.contains("table_bulk_num")) {
            return;
        }
        List<WarehouseEntryDetail> details = warehouseEntry.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        for (WarehouseEntryDetail detail : details) {
            DmjItem item = itemIdMap.get(detail.getItemSysId());
            DmjSku sku = skuIdMap.get(detail.getSkuSysId());
            // 商品箱规
            long itemBoxNum = item != null && item.getBoxnum() != null ? Long.valueOf(item.getBoxnum()) : 0L;
            // 规格箱规
            long skuBoxNum = sku != null && sku.getBoxnum() != null ? Long.valueOf(sku.getBoxnum()) : 0L;

            // 箱数
            long numOfBox = Optional.ofNullable(detail.getNumOfBox()).orElse(0L);
            // 箱规
            Long numPerBox = Optional.ofNullable(detail.getNumPerBox()).orElse(0L);
            // 散装数
            long bulkNum = Optional.ofNullable(detail.getBulkNum()).orElse(0L);
            // 收货数
            Long quantity = detail.getQuantity();

            if (numPerBox > 0L && numOfBox > 0L) { // 箱规、箱数都有值
                if (bulkNum == 0L) {
                    detail.setBulkNum(quantity % numPerBox);
                }
            } else if (numPerBox > 0L && numOfBox == 0L) {   // 若只有箱规有值，则箱数=收货数量/箱规；箱数向上取整。
                detail.setNumOfBox(new BigDecimal(quantity).divide(new BigDecimal(numPerBox), RoundingMode.DOWN).longValue());
                if (bulkNum == 0L) {
                    detail.setBulkNum(quantity % numPerBox);
                }
            } else if (numOfBox > 0L && numPerBox == 0L) {  // 若只有箱数有值，则箱数取收货单上的，箱规取商品信息上的箱规，若商品上箱规为空，则箱规为空。
                detail.setNumPerBox(skuBoxNum > 0L ? skuBoxNum : itemBoxNum);
                if (bulkNum == 0L && detail.getNumPerBox() > 0L) {
                    detail.setBulkNum(quantity % detail.getNumPerBox());
                }
            } else if (numOfBox == 0L && numPerBox == 0L) {  // 收货单上箱规、箱数都没有，则取商品信息上的箱规，箱数=收货数量/箱规；箱数向上取整。
                detail.setNumPerBox(skuBoxNum > 0L ? skuBoxNum : itemBoxNum);
                if (detail.getNumPerBox() > 0L) {
                    detail.setNumOfBox(new BigDecimal(quantity).divide(new BigDecimal(detail.getNumPerBox()), RoundingMode.DOWN).longValue());
                    if (bulkNum == 0L) {
                        detail.setBulkNum(quantity % detail.getNumPerBox());
                    }
                }
            }
            detail.setNumPerBoxStr(String.valueOf(detail.getNumPerBox()));
        }
    }

    public static <T extends PurchaseItem> void fillPurchaseItemMap(Staff staff, PrintPageSearchService printPageSearchService, List<T> purchaseItems, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap) {
        if (CollectionUtils.isEmpty(purchaseItems)) {
            return;
        }

        Set<Long> sysSkuIds = Sets.newHashSet();
        Set<Long> sysItemIds = Sets.newHashSet();
        for (PurchaseItem purchaseItem : purchaseItems) {
            if (purchaseItem.getSysSkuId() != null && purchaseItem.getSysSkuId() > 0L) {
                sysSkuIds.add(purchaseItem.getSysSkuId());
            }
            sysItemIds.add(purchaseItem.getSysItemId());
        }
        for (List<Long> subItemIds : Lists.partition(Lists.newArrayList(sysItemIds), BATCH_QUERY_ITEM_SIZE)) {
            List<DmjItem> dmjItems = printPageSearchService.queryItemWithSysItemId(staff, subItemIds);
            itemIdMap.putAll(dmjItems.stream().collect(Collectors.toMap(DmjItem::getSysItemId, java.util.function.Function.identity(), (v1, v2) -> v2)));
        }

        if (!sysSkuIds.isEmpty()) {
            for (List<Long> subSkuIds : Lists.partition(Lists.newArrayList(sysSkuIds), BATCH_QUERY_ITEM_SIZE)) {
                List<DmjSku> dmjSkus = printPageSearchService.querySkuWithSysSkuId(staff, subSkuIds);
                skuIdMap.putAll(dmjSkus.stream().collect(Collectors.toMap(DmjSku::getSysSkuId, java.util.function.Function.identity(), (v1, v2) -> v2)));
            }
        }
    }

    /**
     * 处理折扣率字段，除以100
     *
     * @param processingPrice 是否同时处理折后单价
     */
    public static void dealWithDiscountRateAndPrice(Map<String, Object> result, Boolean processingPrice) {
        //折扣率
        doDividedBy100(result, "table_item_discount_rate", "10000.0");
        if (processingPrice) {
            //折后单价
            doDividedBy100(result, "table_item_discount_price", "0");
        }
    }

    /**
     * 处理Double类型字段（ / 100）
     *
     * @param result       字段集合-集合中含有才处理
     * @param fieldName    字段名
     * @param defaultValue 如果集合中的值为空赋予的默认值
     */
    public static void doDividedBy100(Map<String, Object> result, String fieldName, String defaultValue) {
        if (result.containsKey(fieldName)) {
            String discountPrice = String.valueOf(result.get(fieldName) == null || "".equals(result.get(fieldName)) ? defaultValue : result.get(fieldName));
//            String[] discountList = StringUtils.splitByWholeSeparatorPreserveAllTokens(discountPrice, ",");
            String[] discountList = StringUtils.splitByWholeSeparatorPreserveAllTokens(discountPrice, ",");
            List<String> newDiscount = new ArrayList<>();

            for (String rate : discountList) {
                if (StringUtils.isNotBlank(rate)) {
                    // 使用 BigDecimal 处理折扣率
                    BigDecimal discount = new BigDecimal(rate).divide(new BigDecimal(100), 3, BigDecimal.ROUND_HALF_UP); // 保留10位小数并四舍五入
                    newDiscount.add(discount.toString());
                } else {
                    if ("table_item_discount_price".equals(fieldName)) {
                        newDiscount.add(defaultValue);
                    } else {
                        newDiscount.add("");
                    }
                }
            }
            result.put(fieldName, String.join(",", newDiscount));
        }
    }

    /**
     * 添加规格简称字段 （采购单 PurchaseOrder）
     */
    public static <T extends PurchaseItem> void __ifNeedPropertiesAliasOfItem(List<String> needValues, com.raycloud.dmj.domain.caigou.Orderable<T> receipt, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap, Map<String, Object> result) {
        // 增加一个字段，为规格简称
        String shortStr = null;
        if (receipt == null) {
            return;
        }
        boolean flag = false;
        for (String s : SHORT_KEYS) {
            if (needValues.contains(s)) {
                flag = true;
                shortStr = s;
                break;
            }
        }
        if (flag) {
            List<T> details = receipt.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            StringBuilder sb = new StringBuilder();
            for (T detail : details) {
                Long itemSysId = detail.getSysItemId();
                Long skuSysId = detail.getSysSkuId();
                String shortTitle = handleShortTile(itemSysId, skuSysId, itemIdMap, skuIdMap);
                if (StringUtils.isNotBlank(shortTitle)) {
                    sb.append(shortTitle).append(",");
                } else {
                    sb.append(",");
                }
            }
            String sbTitle = sb.length() > 0 ? sb.deleteCharAt(sb.length() - 1).toString() : "";
            setFieldValue(result, needValues, shortStr, sbTitle);
        }
    }

    /**
     * 添加规格简称字段 （采购退货单 PurchaseReturn）
     */
    public static void __ifNeedPropertiesAliasOfItem(List<String> needValues, PurchaseReturn purchaseReturn, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap, Map<String, Object> result) {
        // 增加一个字段，为规格简称
        String shortStr = null;
        if (purchaseReturn == null) {
            return;
        }
        boolean flag = false;
        for (String s : SHORT_KEYS) {
            if (needValues.contains(s)) {
                flag = true;
                shortStr = s;
                break;
            }
        }
        if (flag) {
            List<PurchaseReturnDetail> details = new ArrayList<>();
            List<PurchaseReturnMerge<PurchaseReturnDetail>> merges = purchaseReturn.getMerges();
            if (CollectionUtils.isNotEmpty(merges)) {
                for (PurchaseReturnMerge<PurchaseReturnDetail> merge : merges) {
                    if (CollectionUtils.isNotEmpty(merge.getDetails())) {
                        details.addAll(merge.getDetails());
                    }
                }
            }
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            StringBuilder sb = new StringBuilder();
            for (PurchaseReturnDetail detail : details) {
                Long itemSysId = detail.getSysItemId();
                Long skuSysId = detail.getSysSkuId();
                String shortTitle = handleShortTile(itemSysId, skuSysId, itemIdMap, skuIdMap);
                if (StringUtils.isNotBlank(shortTitle)) {
                    sb.append(shortTitle).append(",");
                } else {
                    sb.append(",");
                }
            }
            setFieldValue(result, needValues, shortStr, sb.toString());
        }
    }

    /**
     * 处理规格简称
     */
    public static String handleShortTile(Long itemSysId, Long skuSysId, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap) {
        String shortTitle = null;
        DmjItem item = itemIdMap.get(itemSysId);
        if (item != null) {
            DmjSku sku;
            if (skuSysId != null && skuSysId > 0 && (sku = skuIdMap.get(skuSysId)) != null) {
                //若对应商品是规格商品，则取对应规格商品的规格简称；若其未维护规格简称，则取对应主商品的商品简称
                String skuShortTitle = StringUtils.isBlank(sku.getShortTitle()) ? item.getShortTitle() : sku.getShortTitle();
                //若其主商品未维护商品简称，则取规格商品的规格名称
                shortTitle = StringUtils.isBlank(skuShortTitle) ? sku.getTitle() : skuShortTitle;
                // 如果规格都没有,则取规格别名
                shortTitle = StringUtils.isBlank(shortTitle) ? sku.getPropertiesName() : shortTitle;
            } else {
                //若对应商品是纯商品或不含sku套件，则取对应商品的商品简称；若未维护商品简称，则取商品名称。
                shortTitle = StringUtils.isBlank(item.getShortTitle()) ? item.getTitle() : item.getShortTitle();
            }
        }
        return shortTitle;
    }


    /**
     * 处理拿货单字段排序
     */
    public static void handleGetterFieldSort(UserGetterTemplate template, List<PrintTemplateOrder> printOrderList) {
        if (printOrderList == null) return;
        List<FieldValue> fieldValues = template.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        if (orderType == null) {
            return;
        }
        sortPrintTemplateOrder(orderType, printOrderList);
        for (int i = 0; i < printOrderList.size(); i++) {
            PrintTemplateOrder order = printOrderList.get(i);
            order.setId((long) (i + 1));
        }
    }

    /**
     * @description 新字段排序
     * <AUTHOR>
     * @date 2024-04-02 11:08
     */
    private static void sortPrintTemplateOrder(Integer orderType, List<PrintTemplateOrder> printOrderList) {
        // 商品数量降序
        if (SortUserGetterTemplateComparator.SORT_DELIVER_NUM_ITEM_OUTER_ID.equals(orderType)) {
            printOrderList.sort(Comparator.comparing(PrintTemplateOrder::getSortNum, Comparator.nullsFirst(Long::compareTo)).reversed()
                    // 按照规格编码排序
                    .thenComparing(PrintTemplateOrder::getSortSkuOuterId, Comparator.nullsLast(String::compareTo))
                    // 按照主商家编码排序
                    .thenComparing(PrintTemplateOrder::getSortItemOuterId, Comparator.nullsLast(String::compareTo)));
            return;
        }
        // 老排序逻辑
        printOrderList.sort(new SortUserGetterTemplateComparator(orderType));
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.Integer>
     * @description 从fieldValues中拿到suiteType和
     * <AUTHOR>
     * @date 2024-04-08 13:47
     */
    private static Map<String, Integer> getSuiteTypeAndSuitOrderType(List<FieldValue> fieldValues) {
        HashMap<String, Integer> map = new HashMap<>();
        // 填充默认值
        map.put(EnumFieldValueName.SUITE_TYPE.getValue(), 0);
        map.put(EnumFieldValueName.SUIT_SORT_TYPE.getValue(), 0);

        if (CollUtil.isNotEmpty(fieldValues)) {
            for (FieldValue fieldValue : fieldValues) {
                if (EnumFieldValueName.SUITE_TYPE.getValue().equals(fieldValue.getName())) {
                    try {
                        map.put(EnumFieldValueName.SUITE_TYPE.getValue(), Integer.parseInt(fieldValue.getValue()));
                    } catch (Exception e) {
                        logger.error(String.format("获取打印数据转化suiteType失败：%s", fieldValue.getValue()), e);
                    }
                }
                if (EnumFieldValueName.SUIT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                    try {
                        map.put(EnumFieldValueName.SUIT_SORT_TYPE.getValue(), Integer.parseInt(fieldValue.getValue()));
                    } catch (Exception e) {
                        logger.error(String.format("获取打印数据转化suitOrderType失败：%s", fieldValue.getValue()), e);
                    }
                }
            }
        }
        return map;
    }

    /**
     * @return java.lang.Boolean
     * @description 判断是否开启以套件维度排序
     * <AUTHOR>
     * @date 2024-04-08 18:25
     */
    public static Boolean isSuitSort(UserGetterTemplate template) {
        List<FieldValue> fieldValues = template.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return false;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        if (orderType == null) {
            return false;
        }
        Map<String, Integer> map = getSuiteTypeAndSuitOrderType(fieldValues);
        return SortUserGetterTemplateComparator.SORT_DELIVER_NUM_ITEM_OUTER_ID.equals(orderType)
                && map.get(EnumFieldValueName.SUITE_TYPE.getValue()).equals(0)
                && map.get(EnumFieldValueName.SUIT_SORT_TYPE.getValue()).equals(1);
    }

    /**
     * @description 如果开启以套件维度排序，则先组装套件数量，防止删除
     * <AUTHOR>
     * @date 2024-04-11 15:34
     */
    public static void retainSortData(UserGetterTemplate userGetterTemplate, List<Trade> trades) {
        if (isSuitSort(userGetterTemplate)) {
            trades.forEach(trade -> {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                orders.forEach(order -> {
                    if (order.isSuit()) {
                        order.getSuits().forEach(order1 -> order1.setSuitItemCount(order.getNum()));
                    }
                });
            });
        }
    }

    /**
     * 处理拣货单字段排序
     */
    public static void handlePickerFieldsSort(UserPickerTemplate template, PrintTemplateTrade trade) {
        List<PrintTemplateOrder> printOrderList = trade.getOrders();
        if (printOrderList == null)
            return;
        List<FieldValue> fieldValues = template.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        if (orderType == null) {
            return;
        }

        printOrderList.sort(new SortUserPickerTemplateComparator(orderType));
        for (int i = 0; i < printOrderList.size(); i++) {
            PrintTemplateOrder order = printOrderList.get(i);
            order.setId((long) (i + 1));
        }
    }


    /**
     * 处理拣货单字段排序
     */
    public static void handlePickerFieldsSort(UserPickerTemplate userPickerTemplate, List<PrintTemplateOrder> printOrderList) {
        if (printOrderList == null)
            return;

        Integer orderType;
        if (userPickerTemplate.getTemplateType() != null && (userPickerTemplate.getTemplateType() == 1
                || userPickerTemplate.getTemplateType() == 4
                || userPickerTemplate.getTemplateType() == 5)) {
            //货位版 默认按货位排序
            orderType = SortUserPickerTemplateComparator.SORT_SHEL;
        } else {
            List<FieldValue> fieldValues = userPickerTemplate.getFieldValues();
            if (CollectionUtils.isEmpty(fieldValues)) {
                return;
            }
            orderType = getOrderTypeWithFieldValues(fieldValues);
            if (orderType == null) {
                return;
            }
        }
        printOrderList.sort(new SortUserPickerTemplateComparator(orderType));
        for (int i = 0; i < printOrderList.size(); i++) {
            PrintTemplateOrder order = printOrderList.get(i);
            order.setId((long) (i + 1));
        }
    }

    public static void handlePickerVipFieldsSort(UserPickerTemplate template, PrintTemplateTrade trade) {
        List<PrintTemplateOrder> printOrderList = trade.getOrders();
        if (printOrderList == null)
            return;

        //默认按货位排序
        Integer orderType = SortUserPickerTemplateComparator.SORT_SHEL;

        printOrderList.sort(new SortUserPickerTemplateComparator(orderType));
        for (int i = 0; i < printOrderList.size(); i++) {
            PrintTemplateOrder order = printOrderList.get(i);
            order.setId((long) (i + 1));
        }
    }

    /**
     * 处理电子面单字段排序
     */
    public static void handleWlbExpressFieldsSort(WmsConfig wmsConfig, UserWlbExpressTemplate userWlbExpressTemplate,
                                                  Map<String, Object> result, PrintItemMergeRule rule, Long warehouseId,
                                                  PickGoodsRouteConfig pickGoodsRouteConfig, Trade mergeTrade, Map<String, DmjSku> dmjSkuMap,
                                                  Map<String, DmjItem> dmjItemMap) {
        Integer templateType = userWlbExpressTemplate.getWlbTemplateType();
        List<FieldValue> fieldValues;
        if (templateType == null || templateType == 0) {
            fieldValues = userWlbExpressTemplate.getFieldValuesStandard();
        } else {
            fieldValues = userWlbExpressTemplate.getFieldValuesCustom();
        }
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        String printSortPicker = getFieldValueByKey(fieldValues, EnumFieldValueName.PRINT_SORT_PICKER.getValue());
        if (orderType != null) {
            assembleSuitSort(result, fieldValues, orderType, mergeTrade, dmjSkuMap, dmjItemMap, userWlbExpressTemplate);
            SortExpressTemplateHelper.sort(result, orderType, EnumTemplateKind.WLB, wmsConfig, printSortPicker, warehouseId, pickGoodsRouteConfig);
        }
        String sortGiftType = "";
        if (rule != null && rule.getNeedApendGiftKey() && !rule.getHideGift() && MapUtils.isNotEmpty(result) && rule.getGiftSortNum() != null) {
            if (rule.getGiftSortNum().equals("2")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_DESC;
            } else if (rule.getGiftSortNum().equals("1")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_ASC;
            }
        }
        SortExpressTemplateHelper.sortBySingleField(result, "item_gift_msg", sortGiftType);

    }

    /**
     * @param result
     * @param fieldValues
     * @param orderType
     * @param mergeTrade
     * @param dmjSkuMap
     * @param dmjItemMap
     * @param serializable
     * @return void
     * @description
     * <AUTHOR>
     * @date 2024-06-12 14:50
     */
    private static void assembleSuitSort(Map<String, Object> result, List<FieldValue> fieldValues, Integer orderType, Trade mergeTrade, Map<String, DmjSku> dmjSkuMap, Map<String, DmjItem> dmjItemMap, Serializable serializable) {
        if (23 == orderType) {
            List<Order> orders = TradeUtils.getOrders4Trade(mergeTrade);
            if (CollUtil.isEmpty(orders)) {
                return;
            }
            StringBuilder sortItemShortTitle = new StringBuilder();
            // 判断展示套件还是明细
            int selectSuiteType = getSelectSuiteType(serializable);
            // 判断是否以套件维度排序
            Boolean suitSortType = getOrderSuitSortType(fieldValues);
            // 遍历拼接参数
            for (Order order : orders) {
                DmjSku dmjSku = null;
                DmjItem dmjItem = null;
                // 以套件维度排序，并且商品是套件商品，则取套件商品信息
                if (selectSuiteType == 0 && suitSortType && order.isSuit()) {
                    if (order.getSuitSortSkuId() != null)
                        dmjSku = dmjSkuMap.get(order.getSuitSortSkuId().toString());
                    if (order.getSuitSortSysId() != null)
                        dmjItem = dmjItemMap.get(order.getSuitSortSysId().toString());
                } else {
                    if (order.getSkuSysId() != null)
                        dmjSku = dmjSkuMap.get(order.getSkuSysId().toString());
                    if (order.getItemSysId() != null)
                        dmjItem = dmjItemMap.get(order.getItemSysId().toString());
                }
                // 防止报错，都为空代表数据有问题
                if (dmjSku == null && dmjItem == null) {
                    sortItemShortTitle.append(",");
                    continue;
                }
                if (dmjSku != null) {
                    sortItemShortTitle.append(StringUtils.isBlank(dmjSku.getShortTitle()) ? StringUtils.isBlank(dmjItem.getShortTitle()) ? "" : dmjItem.getShortTitle() : dmjSku.getShortTitle()).append(",");
                } else {
                    sortItemShortTitle.append(StringUtils.isBlank(dmjItem.getShortTitle()) ? "" : dmjItem.getShortTitle()).append(",");
                }
            }

            if (sortItemShortTitle.length() > 0) {
                sortItemShortTitle.deleteCharAt(sortItemShortTitle.length() - 1);
            }

            result.put("sort_item_short_title", sortItemShortTitle.toString());
        }

    }


    /**
     * 处理普通面单字段排序（针对商品信息进行排序）
     */
    public static void handleExpressFieldsSort(UserExpressTemplate userExpressTemplate, Map<String, Object> result, PrintItemMergeRule rule) {
        List<FieldValue> fieldValues = userExpressTemplate.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        if (orderType != null) {
            SortExpressTemplateHelper.sort(result, orderType, EnumTemplateKind.EXPRESS);
        }
        String sortGiftType = "";
        if (rule != null && rule.getNeedApendGiftKey() && !rule.getHideGift() && MapUtils.isNotEmpty(result) && rule.getGiftSortNum() != null) {
            if (rule.getGiftSortNum().equals("2")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_DESC;
            } else if (rule.getGiftSortNum().equals("1")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_ASC;
            }
        }
        SortExpressTemplateHelper.sortBySingleField(result, "item_gift_msg", sortGiftType);
    }

    /**
     * 这里是为组合字段添加排序编号，如 一：商品名称 编码 数量，二：...
     *
     * <AUTHOR>
     * @date 下午4:27 2018/12/6
     **/
    public static void addSortOrderNum(Map<String, Object> result) {
        Set<String> keys = result.keySet();
        for (String key : keys) {
            String v;
            if (key.contains("-") && result.get(key) != null && StringUtils.isNotEmpty(v = result.get(key).toString())) {
                String[] v_split = v.split(",");
                for (int i = 0; i < v_split.length; i++) {
                    v_split[i] = Numeric2ChineseStr.toDo(String.valueOf(i + 1)) + ": " + v_split[i];
                }
                result.put(key, StringUtils.join(v_split, ","));
            }
        }
    }

    /**
     * 从fieldValues中拿到orderType
     */
    public static Integer getOrderTypeWithFieldValues(List<FieldValue> fieldValues) {
        Integer orderType = null;
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            for (FieldValue fieldValue : fieldValues) {
                if (!EnumFieldValueName.ORDER_TYPE.getValue().equals(fieldValue.getName())) {
                    continue;
                }
                try {
                    orderType = Integer.parseInt(fieldValue.getValue());
                    break;
                } catch (Exception e) {
                    logger.error("获取打印数据转化orderType失败：" + fieldValue.getValue(), e);
                }
            }
        }
        return orderType;
    }

    /**
     * @param fieldValues
     * @return java.lang.Boolean
     * @description 判断是否以套件维度排序，true：开启，false：关闭
     * <AUTHOR>
     * @date 2024-06-07 17:28
     */
    public static Boolean getOrderSuitSortType(List<FieldValue> fieldValues) {
        boolean orderSuitSortType = false;
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            for (FieldValue fieldValue : fieldValues) {
                if (!EnumFieldValueName.SUIT_SORT_TYPE.getValue().equals(fieldValue.getName())) {
                    continue;
                }
                try {
                    int i = Integer.parseInt(fieldValue.getValue());
                    orderSuitSortType = i == 1;
                    break;
                } catch (Exception e) {
                    logger.error("获取打印数据转化suitSortType失败：" + fieldValue.getValue(), e);
                }
            }
        }
        return orderSuitSortType;
    }

    /**
     * 处理发货单字段排序
     */
    public static void handleDeliverFieldsSort(UserDeliverTemplate template, Map<String, Object> result,
                                               PrintItemMergeRule rule, WmsConfig wmsConfig, List<String> orderedFields, PickGoodsRouteConfig pickGoodsRouteConfig, Trade mergeTrade, Map<Long, DmjSku> dmjSkuMap, Map<Long, DmjItem> dmjItemMap) {
        List<FieldValue> fieldValues = template.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues) && CollectionUtils.isEmpty(orderedFields)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        String printSortPicker = getFieldValueByKey(fieldValues, EnumFieldValueName.PRINT_SORT_PICKER.getValue());
        if (orderType != null || CollectionUtils.isNotEmpty(orderedFields)) {
            // 处理套件维度排序字段
            if (orderType != null && orderType == 23) {
                Map<String, DmjSku> dmjSkuMapStr = dmjSkuMap.entrySet().stream().collect(Collectors.toMap(entry -> String.valueOf(entry.getKey()), Map.Entry::getValue));
                Map<String, DmjItem> dmjItemMapStr = dmjItemMap.entrySet().stream().collect(Collectors.toMap(entry -> String.valueOf(entry.getKey()), Map.Entry::getValue));
                assembleSuitSort(result, fieldValues, orderType, mergeTrade, dmjSkuMapStr, dmjItemMapStr, template);
            }
            if (CollectionUtils.isNotEmpty(orderedFields)) {
                SortExpressTemplateHelper.deliverSort(result, orderType, EnumTemplateKind.DELIVER, wmsConfig, orderedFields);
            } else {
                Long warehouseId = null;
                if (rule != null && rule.getOriginTrade() != null) {
                    warehouseId = rule.getOriginTrade().getWarehouseId();
                }
                SortExpressTemplateHelper.sort(result, orderType, EnumTemplateKind.DELIVER, wmsConfig, printSortPicker, warehouseId, pickGoodsRouteConfig);
            }
        }
        String sortGiftType = "";
        if (rule != null && rule.getNeedApendGiftKey() && !rule.getHideGift() && MapUtils.isNotEmpty(result) && rule.getGiftSortNum() != null) {
            if (rule.getGiftSortNum().equals("2")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_DESC;
            } else if (rule.getGiftSortNum().equals("1")) {
                sortGiftType = SortExpressTemplateHelper.SORT_TYPR_ASC;
            }
        }
        SortExpressTemplateHelper.sortBySingleField(result, "item_gift_msg", sortGiftType);
    }


    /**
     * 处理采退单，收货单字段排序
     */
    public static void handleUserInvoicesFieldsSort(UserInvoicesTemplate userInvoicesTemplate,
                                                    Map<String, Object> result, EnumTemplateKind templateKind) {
        handleUserInvoicesFieldsSort(userInvoicesTemplate, result, templateKind, null, null, null);
    }

    /**
     * 处理采购单，收货单字段排序
     */
    public static void handleUserInvoicesFieldsSort(UserInvoicesTemplate userInvoicesTemplate,
                                                    Map<String, Object> result, EnumTemplateKind templateKind, WmsConfig wmsConfig, Long warehouseId, PickGoodsRouteConfig pickGoodsRouteConfig) {
        List<FieldValue> fieldValues = userInvoicesTemplate.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        Integer orderType = getOrderTypeWithFieldValues(fieldValues);
        String printSortPicker = getFieldValueByKey(fieldValues, EnumFieldValueName.PRINT_SORT_PICKER.getValue());
        if (orderType != null) {
            SortExpressTemplateHelper.sort(result, orderType, templateKind, wmsConfig, printSortPicker, warehouseId, pickGoodsRouteConfig);
        }
    }

    /**
     * @param userInvoicesTemplate
     * @param result
     * @description: 收货单按款打印
     * @author: tanyi
     * @date: 2024-12-23 13:57
     */
    public static Integer assemblyDimension(UserInvoicesTemplate userInvoicesTemplate, Map<String, Object> result) {
        // 打印维度，是否按款打印
        List<FieldValue> fieldValues = userInvoicesTemplate.getFieldValues();
        if (CollectionUtils.isEmpty(fieldValues)) {
            return null;
        }
        String printDimension = getFieldValueByKey(fieldValues, EnumFieldValueName.PRINT_DIMENSION.getValue());
        if (!Objects.equals("1", printDimension)) {
            return null;
        }

//        // 根据系统商品编码区分同款商品
//        String sysItemId = String.valueOf(result.get("sys_item_id"));
//        String[] sysItemIdValueArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(sysItemId, ",");
//        // 相同款商品进行分组
//        Map<String, List<Integer>> groupMap = new LinkedHashMap<>();
//        for (int i = 0; i < sysItemIdValueArr.length; i++) {
//            groupMap.computeIfAbsent(sysItemIdValueArr[i], k -> new ArrayList<>()).add(i);
//        }
//        // 取出相同款排序序号
//        List<Integer> sortIndexList = groupMap.values().stream()
//                .flatMap(List::stream)
//                .collect(Collectors.toList());
//
//        SortExpressTemplateHelper.sortItemFields(result, sortIndexList);


        String sysItemId = String.valueOf(result.get("sys_item_id"));
        String[] sysItemIds = StringUtils.splitByWholeSeparatorPreserveAllTokens(sysItemId, ",");
        List<ReceiptPrintDataVo> receiptPrintDataVos = new ArrayList<>();
        for (int i = 0; i < sysItemIds.length; i++) {
            if (StringUtils.isEmpty(sysItemIds[i])) {
                continue;
            }
            ReceiptPrintDataVo receiptPrintDataVo = new ReceiptPrintDataVo();
            receiptPrintDataVo.setTable_sys_item_id(sysItemIds[i]);
            receiptPrintDataVo.setTable_item_short_title(Objects.nonNull(result.get("table_item_short_title")) ? String.valueOf(result.get("table_item_short_title")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_main_item_pic(Objects.nonNull(result.get("table_main_item_pic")) ? String.valueOf(result.get("table_main_item_pic")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_title(Objects.nonNull(result.get("table_item_title")) ? String.valueOf(result.get("table_item_title")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_remark(Objects.nonNull(result.get("table_item_remark")) ? String.valueOf(result.get("table_item_remark")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_unit(Objects.nonNull(result.get("table_item_unit")) ? String.valueOf(result.get("table_item_unit")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_quantity(Objects.nonNull(result.get("table_item_quantity")) ? Integer.parseInt(String.valueOf(result.get("table_item_quantity")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_item_price(Objects.nonNull(result.get("table_item_price")) ? String.valueOf(result.get("table_item_price")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_item_total_amount(Objects.nonNull(result.get("table_item_total_amount")) ? String.valueOf(result.get("table_item_total_amount")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_item_shelved_num(Objects.nonNull(result.get("table_item_shelved_num")) ? Integer.parseInt(String.valueOf(result.get("table_item_shelved_num")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_item_shelved_amount(Objects.nonNull(result.get("table_item_shelved_amount")) ? String.valueOf(result.get("table_item_shelved_amount")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_ware_good_num(Objects.nonNull(result.get("table_ware_good_num")) ? Integer.parseInt(String.valueOf(result.get("table_ware_good_num")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_ware_bad_num(Objects.nonNull(result.get("table_ware_bad_num")) ? Integer.parseInt(String.valueOf(result.get("table_ware_bad_num")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_ware_good_fee(Objects.nonNull(result.get("table_ware_good_fee")) ? String.valueOf(result.get("table_ware_good_fee")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_ware_bad_fee(Objects.nonNull(result.get("table_ware_bad_fee")) ? String.valueOf(result.get("table_ware_bad_fee")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_item_detail_remark(Objects.nonNull(result.get("table_item_detail_remark")) ? String.valueOf(result.get("table_item_detail_remark")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_discount_rate(Objects.nonNull(result.get("table_item_discount_rate")) ? String.valueOf(result.get("table_item_discount_rate")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_discount_price(Objects.nonNull(result.get("table_item_discount_price")) ? String.valueOf(result.get("table_item_discount_price")).split(",", sysItemIds.length)[i] : "0");
            receiptPrintDataVo.setTable_item_section_code(Objects.nonNull(result.get("table_item_section_code")) ? String.valueOf(result.get("table_item_section_code")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_main_outer_id(Objects.nonNull(result.get("table_main_outer_id")) ? String.valueOf(result.get("table_main_outer_id")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_supplier_outer_id(Objects.nonNull(result.get("table_supplier_outer_id")) ? String.valueOf(result.get("table_supplier_outer_id")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_receipt_code(Objects.nonNull(result.get("table_receipt_code")) ? String.valueOf(result.get("table_receipt_code")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_caigou_code(Objects.nonNull(result.get("table_caigou_code")) ? String.valueOf(result.get("table_caigou_code")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_item_barcode(Objects.nonNull(result.get("table_item_barcode")) ? String.valueOf(result.get("table_item_barcode")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_boxnum(Objects.nonNull(result.get("table_boxnum")) ? String.valueOf(result.get("table_boxnum")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVo.setTable_num_of_box(Objects.nonNull(result.get("table_num_of_box")) ? Integer.parseInt(String.valueOf(result.get("table_num_of_box")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_bulk_num(Objects.nonNull(result.get("table_bulk_num")) ? Integer.parseInt(String.valueOf(result.get("table_bulk_num")).split(",", sysItemIds.length)[i]) : 0);
            receiptPrintDataVo.setTable_item_category_names(Objects.nonNull(result.get("table_item_category_names")) ? String.valueOf(result.get("table_item_category_names")).split(",", sysItemIds.length)[i] : "");
            receiptPrintDataVos.add(receiptPrintDataVo);
        }

        LinkedHashMap<String, List<ReceiptPrintDataVo>> receiptPrintMap = receiptPrintDataVos.stream().collect(Collectors.groupingBy(ReceiptPrintDataVo::getTable_sys_item_id, LinkedHashMap::new, Collectors.toList()));
        List<ReceiptPrintDataVo> receiptPrintData = new ArrayList<>();

        for (Map.Entry<String, List<ReceiptPrintDataVo>> entry : receiptPrintMap.entrySet()) {
            List<ReceiptPrintDataVo> receiptPrintVos = entry.getValue();

            List<ReceiptPrintDataVo> needEncryptVos = receiptPrintVos.stream().filter(d -> "***".equals(d.getTable_item_price())).collect(Collectors.toList());
            boolean needEncrypt = CollectionUtils.isNotEmpty(needEncryptVos);

            ReceiptPrintDataVo receiptPrintDataVo = new ReceiptPrintDataVo();
            receiptPrintDataVo.setTable_item_short_title(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_short_title).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_main_item_pic(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_main_item_pic).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_title(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_title).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_remark(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_remark).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_unit(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_unit).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_quantity(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_item_quantity).sum());
            receiptPrintDataVo.setTable_item_price(needEncrypt ? "***" : String.join("&#44;", receiptPrintVos.stream().map(vo -> String.format("%.3f", Double.parseDouble(vo.getTable_item_price())/100)).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_total_amount(needEncrypt ? "***" : String.format("%.3f", receiptPrintVos.stream().mapToDouble(data -> Double.parseDouble(data.getTable_item_total_amount())/100).sum()));
            receiptPrintDataVo.setTable_item_shelved_num(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_item_shelved_num).sum());
            receiptPrintDataVo.setTable_item_shelved_amount(needEncrypt ? "***" : String.format("%.3f", receiptPrintVos.stream().mapToDouble(data -> Double.parseDouble(data.getTable_item_shelved_amount())/100).sum()));
            receiptPrintDataVo.setTable_ware_good_num(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_ware_good_num).sum());
            receiptPrintDataVo.setTable_ware_bad_num(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_ware_bad_num).sum());
            receiptPrintDataVo.setTable_ware_good_fee(needEncrypt ? "***" : String.format("%.3f", receiptPrintVos.stream().mapToDouble(data -> Double.parseDouble(data.getTable_ware_good_fee())/100).sum()));
            receiptPrintDataVo.setTable_ware_bad_fee(needEncrypt ? "***" : String.format("%.3f", receiptPrintVos.stream().mapToDouble(data -> Double.parseDouble(data.getTable_ware_bad_fee())/100).sum()));
            receiptPrintDataVo.setTable_item_detail_remark(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_detail_remark).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_discount_rate(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_discount_rate).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_discount_price(needEncrypt ? "***" : String.join("&#44;", receiptPrintVos.stream().map(vo -> String.format("%.3f", Double.parseDouble(vo.getTable_item_discount_price()))).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_section_code(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_section_code).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_main_outer_id(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_main_outer_id).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_supplier_outer_id(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_supplier_outer_id).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_receipt_code(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_receipt_code).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_caigou_code(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_caigou_code).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_item_barcode(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_barcode).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_boxnum(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_boxnum).collect(Collectors.toSet())));
            receiptPrintDataVo.setTable_num_of_box(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_num_of_box).sum());
            receiptPrintDataVo.setTable_bulk_num(receiptPrintVos.stream().mapToInt(ReceiptPrintDataVo::getTable_bulk_num).sum());
            receiptPrintDataVo.setTable_item_category_names(String.join("&#44;", receiptPrintVos.stream().map(ReceiptPrintDataVo::getTable_item_category_names).collect(Collectors.toSet())));
            receiptPrintData.add(receiptPrintDataVo);
        }

        Map<String, Object> addResult = new HashMap<>();
        addResult.put("table_item_short_title", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_short_title).collect(Collectors.joining(",")));
        addResult.put("table_main_item_pic", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_main_item_pic).collect(Collectors.joining(",")));
        addResult.put("table_item_title", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_title).collect(Collectors.joining(",")));
        addResult.put("table_item_remark", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_remark).collect(Collectors.joining(",")));
        addResult.put("table_item_unit", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_unit).collect(Collectors.joining(",")));
        addResult.put("table_item_quantity", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_quantity).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_item_price", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_price).collect(Collectors.joining(",")));
        addResult.put("table_item_total_amount", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_total_amount).collect(Collectors.joining(",")));
        addResult.put("table_item_shelved_num", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_shelved_num).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_item_shelved_amount", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_shelved_amount).collect(Collectors.joining(",")));
        addResult.put("table_ware_good_num", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_ware_good_num).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_ware_bad_num", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_ware_bad_num).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_ware_good_fee", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_ware_good_fee).collect(Collectors.joining(",")));
        addResult.put("table_ware_bad_fee", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_ware_bad_fee).collect(Collectors.joining(",")));
        addResult.put("table_item_detail_remark", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_detail_remark).collect(Collectors.joining(",")));
        addResult.put("table_item_discount_rate", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_discount_rate).collect(Collectors.joining(",")));
        addResult.put("table_item_discount_price", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_discount_price).collect(Collectors.joining(",")));
        addResult.put("table_item_section_code", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_section_code).collect(Collectors.joining(",")));
        addResult.put("table_main_outer_id", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_main_outer_id).collect(Collectors.joining(",")));
        addResult.put("table_supplier_outer_id", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_supplier_outer_id).collect(Collectors.joining(",")));
        addResult.put("table_receipt_code", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_receipt_code).collect(Collectors.joining(",")));
        addResult.put("table_caigou_code", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_caigou_code).collect(Collectors.joining(",")));
        addResult.put("table_item_barcode", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_barcode).collect(Collectors.joining(",")));
        addResult.put("table_boxnum", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_boxnum).collect(Collectors.joining(",")));
        addResult.put("table_num_of_box", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_num_of_box).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_bulk_num", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_bulk_num).map(String::valueOf).collect(Collectors.joining(",")));
        addResult.put("table_item_category_names", receiptPrintData.stream().map(ReceiptPrintDataVo::getTable_item_category_names).collect(Collectors.joining(",")));

        result.putAll(addResult);
        result.remove("table_item_outer_id");
        result.remove("table_sys_sku_remark");
        result.remove("table_item_pic");
        result.remove("table_item_properties_name");
        result.remove("table_sku_properties_short_title");
        result.remove("table_sku_properties_alias");
        result.remove("table_sku_unit");
        result.remove("table_sku_barcode");

        return receiptPrintMap.size();
    }

    /**
     * 得到用户选择模版的打印套件名称类型
     * 0：打印套件明细信息
     * 1：打印套件本身信息，不打印明细
     */
    public static int getSelectSuiteType(Serializable serializable) {

        if (serializable == null) {
            return 0;
        }
        List<FieldValue> fieldValues = null;
        if (serializable instanceof UserWlbExpressTemplate) {
            UserWlbExpressTemplate template = (UserWlbExpressTemplate) serializable;
            if (template.getTemplateType() == 0) {
                fieldValues = template.getFieldValuesStandard();
            } else {
                fieldValues = template.getFieldValuesCustom();
            }
        } else if (serializable instanceof UserExpressTemplate) {
            fieldValues = ((UserExpressTemplate) serializable).getFieldValues();
        } else if (serializable instanceof UserDeliverTemplate) {
            fieldValues = ((UserDeliverTemplate) serializable).getFieldValues();
        } else if (serializable instanceof UserGetterTemplate) {
            fieldValues = ((UserGetterTemplate) serializable).getFieldValues();
        } else if (serializable instanceof UserPickerTemplate) {
            fieldValues = ((UserPickerTemplate) serializable).getFieldValues();
        }

        if (CollectionUtils.isEmpty(fieldValues)) {
            return 0;
        }

        String suiteType = "";
        for (FieldValue fieldValue : fieldValues) {
            if (EnumFieldValueName.SUITE_TYPE.getValue().equalsIgnoreCase(fieldValue.getName())) {
                suiteType = fieldValue.getValue();
                break;
            }
        }
        if (StringUtils.isEmpty(suiteType)) {
            return 0;
        }

        int suiteTypeInt = 0;
        try {
            suiteTypeInt = Integer.parseInt(suiteType);
        } catch (Exception e) {
            logger.error("转化suiteType为整型报错.", e);
        }

        return suiteTypeInt;
    }

    /**
     * 验证订单信息是否允许后置打印
     */
    public static void verifyPrintDelayTrades(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("找不到订单信息.");
        }
        for (Trade trade : trades) {
            if (trade.getTemplateId() <= 0) {
                throw new IllegalArgumentException(String.format("系统订单号%s没有设置模版，不能进行后置打印.", trade.getSid()));
            }
            if (StringUtils.isEmpty(trade.getOutSid())) {
                throw new IllegalArgumentException(String.format("系统订单号%s没有填写面单号，不能进行后置打印.", trade.getSid()));
            }
        }
    }

    public static String getFieldValueByKey(IExpressTemplateBase templateBase, String key) {
        if (templateBase instanceof UserExpressTemplate) {
            UserExpressTemplate userExpressTemplate = (UserExpressTemplate) templateBase;
            return getFieldValueByKey(userExpressTemplate.getFieldValues(), key);
        }

        if (templateBase instanceof UserDeliverTemplate) {
            UserDeliverTemplate userDeliverTemplate = (UserDeliverTemplate) templateBase;
            return getFieldValueByKey(userDeliverTemplate.getFieldValues(), key);
        }

        if (templateBase instanceof UserWlbExpressTemplate) {
            UserWlbExpressTemplate userWlbExpressTemplate = (UserWlbExpressTemplate) templateBase;
            if (userWlbExpressTemplate.getTemplateType() == 0) {
                return getFieldValueByKey(userWlbExpressTemplate.getFieldValuesStandard(), key);
            } else if (userWlbExpressTemplate.getTemplateType() == 1 || userWlbExpressTemplate.getTemplateType() == 2) {
                return getFieldValueByKey(userWlbExpressTemplate.getFieldValuesCustom(), key);
            }
        }
        return null;
    }

    public static String getFieldValueByKey(IExpressTemplateBase templateBase, String key, String defaultValue) {
        String fieldValue = getFieldValueByKey(templateBase, key);
        return null != fieldValue ? fieldValue : defaultValue;
    }

    public static String getFieldValueByKey(List<FieldValue> fieldValues, String key) {
        if (CollectionUtils.isEmpty(fieldValues)) {
            return null;
        }
        for (FieldValue fieldValue : fieldValues) {
            if (key.equalsIgnoreCase(fieldValue.getName())) {
                return fieldValue.getValue();
            }
        }
        return null;
    }

    public static String getFieldValueByKey(List<FieldValue> fieldValues, String key, String defaultValue) {
        String fieldValue = getFieldValueByKey(fieldValues, key);
        return null != fieldValue ? fieldValue : defaultValue;
    }

    /**
     * 得到打印时间
     */
    public static String getPrintTimeWithFieldSetting(List<FieldSettings> fieldSettingses, String fieldName) {
        if (CollectionUtils.isEmpty(fieldSettingses)) {
            return "";
        }
        for (FieldSettings fieldSettings : fieldSettingses) {
            if (!fieldName.equalsIgnoreCase(fieldSettings.getField())) {
                continue;
            }
            String content = fieldSettings.getContent();
            if ("day".equals(content)) {
                return DateTime.now().toString("yyyy-MM-dd");
            } else {
                return DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }
        }

        return DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
    }


    /**
     * 得到打印时间
     */
    public static String getPrintTimeWithFieldValue(List<FieldValue> fieldValueList, String fieldName) {
        if (CollectionUtils.isEmpty(fieldValueList)) {
            return "";
        }
        for (FieldValue fieldValue : fieldValueList) {
            if (!fieldName.equalsIgnoreCase(fieldValue.getName())) {
                continue;
            }
            String value = fieldValue.getValue();
            if ("day".equals(value)) {
                return DateTime.now().toString("yyyy-MM-dd");
            } else {
                return DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }
        }

        return DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到自定义内容
     */
    public static String getCustomContent(List<FieldSettings> fieldSettingses, String fieldName) {
        if (CollectionUtils.isEmpty(fieldSettingses)) {
            return "";
        }
        for (FieldSettings fieldSettings : fieldSettingses) {
            if (!fieldName.equalsIgnoreCase(fieldSettings.getField())) {
                continue;
            }
            String content = fieldSettings.getContent();
            return StringUtils.defaultString(content, "");
        }

        return "";
    }

    public static String getServiceInfo(Staff staff, UserWlbExpressTemplate userWlbExpressTemplate, Trade trade,
                                        IWarehouseTemplateService warehouseTemplateService) {
        ServiceInfoChoice serviceInfoChoice = userWlbExpressTemplate.getServiceInfoChoice();
        if (serviceInfoChoice == null) {
            return "";
        }

        ServiceInfoChoiceDirect serviceInfoChoiceDirect = serviceInfoChoice.getServiceInfoChoiceDirect();
        if (serviceInfoChoiceDirect != null) {
            return getServiceInfoDirect(serviceInfoChoiceDirect);
        }

        ServiceInfoChoiceAffiliate serviceInfoChoiceAffiliate = serviceInfoChoice.getServiceInfoChoiceAffiliate();
        if (serviceInfoChoiceAffiliate != null) {
            return getServiceInfoAffiliate(staff, userWlbExpressTemplate, trade, serviceInfoChoiceAffiliate, warehouseTemplateService);
        }

        return "";
    }

    /**
     * 得到直营型的服务类型名称
     */
    private static final Map<String, String> SFServiceInfoMap = new HashMap<>();

    static {
        SFServiceInfoMap.put("TODAY", "顺丰即日");
        SFServiceInfoMap.put("NEXT-MORNING", "顺丰次晨");
        SFServiceInfoMap.put("NEXT-DAY", "顺丰次日");
        SFServiceInfoMap.put("SEVERAL-DAYS", "顺丰隔日");
        SFServiceInfoMap.put("ON-DELIVERY", "现结");
        SFServiceInfoMap.put("MONTHLY", "月结");
        SFServiceInfoMap.put("RECEIVER-PAY", "到付");
        SFServiceInfoMap.put("THIRD-PARTY-PAY", "第三方付");
    }

    private static String getServiceInfoDirect(ServiceInfoChoiceDirect serviceInfoChoiceDirect) {
        if (serviceInfoChoiceDirect == null) {
            return "";
        }
        List<ServiceInfoChoiceDirect.ServiceInfoChoiceEntry> serviceCodes = serviceInfoChoiceDirect.getServiceCodes();
        if (CollectionUtils.isEmpty(serviceCodes)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (ServiceInfoChoiceDirect.ServiceInfoChoiceEntry entry : serviceCodes) {
            if ("SVC-COD".equalsIgnoreCase(entry.getServiceCode())) {
                sb.append("代收货款:{SVC-COD},");
            } else if ("SVC-INSURE".equalsIgnoreCase(entry.getServiceCode())) {
                sb.append("保价:{SVC-INSURE},");
            } else if ("TIMED-DELIVERY".equalsIgnoreCase(entry.getServiceCode())) {
                sb.append(SFServiceInfoMap.get(entry.getSubAttr())).append(",");
            } else if ("PAYMENT-TYPE".equalsIgnoreCase(entry.getServiceCode())) {
                sb.append(SFServiceInfoMap.get(entry.getSubAttr())).append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

    /**
     * 得到加盟型的服务类型名称
     */
    private static String getServiceInfoAffiliate(Staff staff, UserWlbExpressTemplate userWlbExpressTemplate, Trade trade, ServiceInfoChoiceAffiliate serviceInfoChoiceAffiliate,
                                                  IWarehouseTemplateService warehouseTemplateService) {
        if (serviceInfoChoiceAffiliate == null) {
            return "";
        }

        Long warehouseId = trade.getWarehouseId();
        WarehouseTemplate warehouseTemplate = warehouseTemplateService.getWarehouseTemplate(staff, warehouseId, userWlbExpressTemplate.getId(), 1);
        if (warehouseTemplate == null) {
            return "";
        }
        String branchId = warehouseTemplate.getBranchId();
        if (!("," + serviceInfoChoiceAffiliate.getBranchCodes() + ",").contains("," + branchId + ",")) {
            return "";
        }
        String serviceCode = serviceInfoChoiceAffiliate.getServiceCode();
        ServiceInfos serviceInfos = userWlbExpressTemplate.getServiceInfos();
        if (serviceInfos == null) {
            return "";
        }
        List<ServiceInfoGroup> serviceInfoGroups = serviceInfos.getServiceInfoGroups();
        if (CollectionUtils.isEmpty(serviceInfoGroups)) {
            return "";
        }
        for (ServiceInfoGroup serviceInfoGroup : serviceInfoGroups) {
            if (serviceCode.equalsIgnoreCase(serviceInfoGroup.getServiceInfoDto().getServiceCode())) {
                return serviceInfoGroup.getServiceInfoDto().getServiceName();
            }
        }
        return "";
    }

    /**
     * 初始化服务类型
     */
    public static void __initServiceInfo(String serviceInfoPlace, Trade singleTrade, Map<String, Object> result) {
        if (StringUtils.isEmpty(serviceInfoPlace)) {
            return;
        }
        if (!serviceInfoPlace.contains("{")) {
            result.put("service_infos", serviceInfoPlace);
            return;
        }
        if (serviceInfoPlace.contains("{SVC-COD}")) {
            serviceInfoPlace = serviceInfoPlace.replace("{SVC-COD}", String.valueOf(TradeUtils.getPayment(singleTrade)));
        }
        if (serviceInfoPlace.contains("{SVC-INSURE}")) {
            serviceInfoPlace = serviceInfoPlace.replace("{SVC-INSURE}", String.valueOf(TradeUtils.getPayment(singleTrade)));
        }
        result.put("service_infos", serviceInfoPlace);
    }

    /**
     * 对菜鸟打印数据获取后的后续处理
     */
    public static void afterGetFieldValueWithCainiao(List<Map<String, Object>> fieldValues, UserWlbExpressTemplate userWlbExpressTemplate) {
        if (CollectionUtils.isEmpty(fieldValues)) {
            return;
        }

        for (Map<String, Object> map : fieldValues) {
            String ali_waybill_short_address = (null != map.get("ali_waybill_short_address") ? map.get("ali_waybill_short_address").toString() : null);
            String express_short_address = (null != map.get("express_short_address") ? map.get("express_short_address").toString() : null);

            if (StringUtils.isNotEmpty(ali_waybill_short_address)) {
                if (Long.valueOf(101).equals(userWlbExpressTemplate.getExpressId())) {
                    String[] strs = ali_waybill_short_address.split("##");
                    if (strs.length > 1) {
                        map.put("ali_waybill_short_address", strs[1]);
                    } else if (strs.length == 1) {
                        map.put("ali_waybill_short_address", strs[0]);
                    } else {
                        map.put("ali_waybill_short_address", "");
                    }
                } else {
                    map.put("ali_waybill_short_address", ali_waybill_short_address.replace("##", ""));
                }
            }

            if (StringUtils.isNotEmpty(express_short_address)) {
                if (Integer.valueOf(1).equals(userWlbExpressTemplate.getWlbTemplateType()) && Long.valueOf(505).equals(userWlbExpressTemplate.getExpressId())) {
                    String[] strs = express_short_address.split("##");
                    if (strs.length > 0) {
                        map.put("express_short_address", strs[0]);
                        map.put("dest_code", strs[0]);
                        if (strs.length > 1) {
                            map.put("origin_code", strs[1]);
                        } else {
                            map.put("origin_code", "");
                        }
                    } else {
                        map.put("express_short_address", "");
                        map.put("dest_code", "");
                        map.put("origin_code", "");
                    }
                } else if (Integer.valueOf(1).equals(userWlbExpressTemplate.getWlbTemplateType()) && Long.valueOf(5000000004901L).equals(userWlbExpressTemplate.getExpressId())) {
                    String[] strs = express_short_address.split("##");
                    if (strs.length > 0) {
                        map.put("express_short_address", strs[0]);
                        if (strs.length > 1) {
                            map.put("four_segment_code", strs[1]);
                        } else {
                            map.put("four_segment_code", "");
                        }
                    } else {
                        map.put("express_short_address", "");
                        map.put("four_segment_code", "");
                    }
                } else if (Long.valueOf(101).equals(userWlbExpressTemplate.getExpressId()) && !Integer.valueOf(6).equals(userWlbExpressTemplate.getWlbTemplateType())) {
                    String[] strs = express_short_address.split("##");
                    if (strs.length > 1) {
                        map.put("express_short_address", strs[1]);
                    } else if (strs.length == 1) {
                        map.put("express_short_address", strs[0]);
                    } else {
                        map.put("express_short_address", "");
                    }
                } else if (Integer.valueOf(6).equals(userWlbExpressTemplate.getWlbTemplateType())) {
                    String[] strs = express_short_address.split("##");
                    if (strs.length > 0) {
                        if (strs.length > 1) {
                            map.put("express_short_address", strs[0]);
                            String str = strs[1];
                            map.put("three_segment_code", (StringUtils.isNoneBlank(str) && str.contains("null")) ? str.replace("null", "") : str);
                        } else {
                            map.put("three_segment_code", strs[0]);
                            map.put("express_short_address", "");
                        }
                    } else {
                        map.put("express_short_address", "");
                        map.put("three_segment_code", "");
                    }
                } else {
                    map.put("express_short_address", express_short_address.replace("##", ""));
                }
            }
        }
    }

    /**
     * 处理编码模板排序
     */
    public static List<Map<String, Object>> handleMerchantCodeFieldsSort(UserInvoicesTemplate template, Map<String, Map<String, Object>> results,
                                                                         String[] ids, WmsConfig wmsConfig, String printSort, Map<String, Integer> defaultSortMap) {
        WlbExpressHelper.initConvertFieldValues(template);
        long start = System.currentTimeMillis();
        List<Map<String, Object>> result;
        if (MerchantCodePrintSortEnum.NOT_SORT.getValue().equals(printSort)) {
            result = getResultsByDefaultSort(results, defaultSortMap);
        } else if (MerchantCodePrintSortEnum.SUPPLIER_ITEM.getValue().equals(printSort)) {
            //按先供应商再商品编码进行排序
            result = getResultsBySupperNameAndItemSort(results);
        } else if (MerchantCodePrintSortEnum.SUPPLIER_ITEM_OUTER_ID.getValue().equals(printSort)) {
            //先供应商名称再主编码排序
            result = getResultsBySupperNameAndItemOutIdSort(results);
        } else if (MerchantCodePrintSortEnum.GOODSSECTION.getValue().equals(printSort) || MerchantCodePrintSortEnum.GOODSSECTION_TABLE_ITEM_OUTER_ID.getValue().equals(printSort)) {
            //货位相关排序
            result = getResultsBystockRegionCodeSort(results, wmsConfig, printSort);
        } else if (StringUtils.isEmpty(printSort) || MerchantCodePrintSortEnum.ITEM.getValue().equals(printSort)) {
            //仅按商品排序
            result = getResultsBySidSort(results, ids);
        } else {
            result = getResultsByItemMsgSort(results, printSort);
        }
        Logs.ifDebug("sort took:    " + (System.currentTimeMillis() - start));
        return result;

    }

    /**
     * 默认排序 则是根据 前端传入的顺序排序
     */
    public static List<Map<String, Object>> getResultsByDefaultSort(Map<String, Map<String, Object>> results, Map<String, Integer> defaultSortMap) {
        if (MapUtils.isEmpty(results)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> sortResult = Lists.newArrayList();
        for (Map.Entry<String, Map<String, Object>> entry : results.entrySet()) {
            Map<String, Object> value = entry.getValue();
            if (MapUtils.isNotEmpty(value)) {
                sortResult.add(value);
            }
        }
        sortResult.sort((o1, o2) -> {
            String sysItemId1 = String.valueOf(o1.get("sys_item_id") == null ? "" : o1.get("sys_item_id"));
            String sysItemId2 = String.valueOf(o2.get("sys_item_id") == null ? "" : o2.get("sys_item_id"));
            String sysSkuId1 = String.valueOf(o1.get("sys_sku_id") == null ? "" : o1.get("sys_sku_id"));
            String sysSkuId2 = String.valueOf(o2.get("sys_sku_id") == null ? "" : o2.get("sys_sku_id"));
            String k1 = sysItemId1 + "_" + sysSkuId1;
            String k2 = sysItemId2 + "_" + sysSkuId2;
            Integer v1 = defaultSortMap.get(k1);
            Integer v2 = defaultSortMap.get(k2);
            if (v1 == null && v2 == null) {
                //都为空的时候返回0
                return 0;
            }
            if (v1 == null) {
                return -2;
            }
            if (v2 == null) {
                return 2;
            }
            return v1.compareTo(v2);
        });
        return sortResult;
    }

    /**
     * 商家编码模板根据商品信息排序
     */
    private static List<Map<String, Object>> getResultsByItemMsgSort(Map<String, Map<String, Object>> results, String sortType) {
        if (MapUtils.isEmpty(results)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> sortResult = Lists.newArrayList();
        for (Map.Entry<String, Map<String, Object>> entry : results.entrySet()) {
            Map<String, Object> value = entry.getValue();
            if (MapUtils.isNotEmpty(value)) {
                sortResult.add(value);
            }
        }
        sortResult.sort(new SortUseMerchantCodeTemplateComparator(sortType));
        return sortResult;
    }

    /**
     * 按照提供商再商品排序
     *
     * @return List<MapObject>>
     */
    public static List<Map<String, Object>> getResultsBySupperNameAndItemSort(Map<String, Map<String, Object>> results) {
        List<Map<String, Object>> elements = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : results.entrySet()) {
            Map<String, Object> entryValue = entry.getValue();
            elements.add(entryValue);
        }
        elements.sort(new SortUseMerchantCodeTemplateComparator(MerchantCodePrintSortEnum.SUPPLIER_ITEM.getValue()));
        return elements;
    }

    /**
     * 按照提供商再商品排序
     *
     * @return List<MapObject>>
     */
    public static List<Map<String, Object>> getResultsBySupperNameAndItemOutIdSort(Map<String, Map<String, Object>> results) {
        List<Map<String, Object>> elements = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : results.entrySet()) {
            Map<String, Object> entryValue = entry.getValue();
            elements.add(entryValue);
        }
        elements.sort(new SortUseMerchantCodeTemplateComparator(MerchantCodePrintSortEnum.SUPPLIER_ITEM_OUTER_ID.getValue()));
        return elements;
    }

    /**
     * 数据 results 按货位排序
     */
    public static List<Map<String, Object>> getResultsBystockRegionCodeSort(Map<String, Map<String, Object>> results, final WmsConfig wmsConfig, String printSort) {
        //货位编码存储类型
        boolean needReplace = WmsUtils.isSectionCodeStoreDefault(wmsConfig);
        //返回结果集
        List<Map<String, Object>> result = new ArrayList<>();
        //波次货位key
        String key = "wave_picking_goods_section_code";
        //打印数据集合
        List<Map<String, Object>> listValue = new ArrayList<>();
        results.entrySet().iterator().forEachRemaining(mapEntry -> listValue.add(mapEntry.getValue()));
        //排序key
        List<String> sortKey = new ArrayList<>();
        //货位值map数据
        Map<String, List<Map<String, Object>>> sectionCodeMap = new HashMap<>();
        //将打印数据按照货位map汇总
        for (Map<String, Object> map : listValue) {
            //货位值
            String sectionCode = (String) map.get(key);
            sectionCode = sectionCode == null || sectionCode.trim().equals("") ? new String(new byte[12]) : sectionCode;
            //非自定义编码格式需要替换掉 "-"
            sectionCode = needReplace ? sectionCode.replaceAll("-", "") : sectionCode;
            if (!sectionCodeMap.containsKey(sectionCode)) {
                List<Map<String, Object>> mapValue = new ArrayList<>();
                sortKey.add(sectionCode);
                mapValue.add(map);
                sectionCodeMap.put(sectionCode, mapValue);
            } else {
                List<Map<String, Object>> list = sectionCodeMap.get(sectionCode);
                list = list == null ? new ArrayList<>() : list;
                list.add(map);
                sectionCodeMap.put(sectionCode, list);
            }
        }
        //调用波次排序方法
        sortKey.sort((o1, o2) -> WmsUtils.sortGoodsSectionCode(wmsConfig, o1, o2));
        if (MerchantCodePrintSortEnum.GOODSSECTION.getValue().equals(printSort)) {
            //仅拣选货位
            sortKey.forEach(k -> result.addAll(sectionCodeMap.get(k)));
        } else if (MerchantCodePrintSortEnum.GOODSSECTION_TABLE_ITEM_OUTER_ID.getValue().equals(printSort)) {
            //先按拣选货位排序，再按规格商家编码排序
            sortKey.forEach(k -> result.addAll(sectionCodeMap.get(k).stream().sorted(new SortUseMerchantCodeTemplateComparator(MerchantCodePrintSortEnum.TABLE_ITEM_OUTER_ID.getValue())).collect(Collectors.toList())));
        }
        return result;
    }
//
//    /**
//     * 先按货位排序，再按规格商家编码排序
//     *
//     * @param wmsConfig
//     * @return
//     */
//    public static List<Map<String, Object>> getResultsBystockRegionCodeThenOutersidSort(Map<String, Map<String, Object>> results, final WmsConfig wmsConfig) {
//        List<Map<String, Object>> listMap = getResultsBystockRegionCodeSort(results, wmsConfig);
//        Collections.sort(listMap, new SortUseMerchantCodeTemplateComparator(MerchantCodePrintSortEnum.TABLE_ITEM_OUTER_ID.getValue()));
//        return listMap;
//    }

    /**
     * 预处理一下参数 相同商品集合一下
     */
    public static void idsDistinct(PrintMerchantCodeDataParam param) {
        if (StringUtils.isBlank(param.getSysItemIds()) && StringUtils.isBlank(param.getSysSkuIds())) return;
        Long[] itemIds = ArrayUtils.toLongArray(param.getSysItemIds());
        Long[] skuIds = ArrayUtils.toLongArray(param.getSysSkuIds());
        Integer[] nums = StringUtils.isEmpty(param.getPrintNums()) ? new Integer[skuIds.length] : ArrayUtils.toIntegerArray(param.getPrintNums());
        Assert.isTrue(itemIds.length == skuIds.length && nums.length == skuIds.length, "参数异常");
        List<String> keyList = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < itemIds.length; i++) {
            Long itemId = itemIds[i];
            if (itemId == null || itemId <= 0) {
                throw new IllegalArgumentException("参数异常");
            }
            String key = itemId + "_" + skuIds[i];
            if (!keyList.contains(key)) {
                keyList.add(key);
            }
            Integer value = map.get(key);
            Integer num = nums[i];
            if (num != null) {
                if (value == null) {
                    value = 0;
                }
                value = value + num;
            }
            map.put(key, value);
        }
        List<String> itemList = new ArrayList<>();
        List<String> skuList = new ArrayList<>();
        List<String> numList = new ArrayList<>();
        for (String item_sku : keyList) {
            String[] split = item_sku.split("_");
            Integer numResult = map.get(item_sku);
            if (StringUtils.isNotEmpty(param.getPrintNums()) && (null == numResult || numResult == 0)) {
                Logs.error(String.format("商家编码打印数量为null，item_sku-->%s", item_sku));
            } else {
                itemList.add(split[0]);
                skuList.add(split[1]);
                numList.add(map.get(item_sku) == null ? "" : String.valueOf(numResult));
            }
        }
        param.setSysItemIds(StringUtils.join(itemList, ","));
        param.setSysSkuIds(StringUtils.join(skuList, ","));
        param.setPrintNums(StringUtils.join(numList, ","));
        Assert.isTrue(StringUtils.isNotBlank(param.getSysItemIds()) && StringUtils.isNotBlank(param.getSysSkuIds()), "需要打印的数据为空");
    }

    /**
     * 查询订单的波次信息
     */
    public static Map<Long, WaveTrade> querySidWaveTradeMap(Staff staff, List<Trade> trades, PrintPageSearchService printPageSearchService) {
        Map<Long, WaveTrade> waveTradeMap = Maps.newHashMapWithExpectedSize(trades.size());
        Map<Long, List<Long>> waveIdSidsMap = Maps.newHashMap();
        for (Trade trade : trades) {
            if (trade.getWaveId() != null && trade.getWaveId() > 0L) {
                waveIdSidsMap.computeIfAbsent(trade.getWaveId(), v -> new ArrayList<>()).add(trade.getSid());
            }
        }
        if (waveIdSidsMap.isEmpty()) {
            return waveTradeMap;
        }
        for (Map.Entry<Long, List<Long>> entry : waveIdSidsMap.entrySet()) {
            List<WaveTrade> waveTrades = printPageSearchService.queryWaveTradeByWaveIdAndSids(staff, entry.getKey(), entry.getValue());
            if (CollectionUtils.isNotEmpty(waveTrades)) {
                for (WaveTrade waveTrade : waveTrades) {
                    waveTradeMap.put(waveTrade.getSid(), waveTrade);
                }
            }
        }
        return waveTradeMap;
    }

    /**
     * 是否需要波次订单
     */
    public static boolean needWaveTrade(List<String> needValues) {
        return needValues.contains("wave_picking_code") || needValues.contains("wave_position_code") || needValues.contains("wave_position_sort_num");
    }

    /**
     * 处理拣选号和位置号这两个参数 不判断波次类型
     */
    public static void handlePikingAndPositionCode(List<String> needValues, Map<String, Object> result, Trade singleTrade, Map<Long, WaveTrade> waveTradeMap, Wave wave) {
        handlePikingAndPositionCode(needValues, result, singleTrade, waveTradeMap, wave, null);
    }

    /**
     * 处理拣选号和位置号这两个参数 判断波次类型
     */
    public static void handlePikingAndPositionCode(List<String> needValues, Map<String, Object> result,
                                                   Trade singleTrade, Map<Long, WaveTrade> waveTradeMap,
                                                   Wave wave, String wavePositionCode) {
        if (needWaveTrade(needValues)) {
            if (singleTrade == null || MapUtils.isEmpty(waveTradeMap)) {
                return;
            }
            if (DataUtils.checkLongNotEmpty(singleTrade.getWaveId())) {
                WaveTrade waveTrade = waveTradeMap.get(singleTrade.getSid());
                if (waveTrade != null) {
                    result.put("wave_picking_code", waveTrade.getPickingCode());
                    //单件波次且设置为单件波次不打印则返回空
                    result.put("wave_position_code", (wave != null && Integer.valueOf(1).equals(wave.getPickingType()) && MULTI_WAVE_PRINT.equals(wavePositionCode)) ? "" :
                            waveTrade.getPositionCode());
                    result.put("wave_position_sort_num", installWavePositionSortNum(wave, waveTrade));
                }
            } else {
                result.put("wave_picking_code", "");
                result.put("wave_position_code", "");
                result.put("wave_position_sort_num", "");
            }
        }
        if (null != wave) {
            if (needValues.contains("wave_rule_name")) {
                result.put("wave_rule_name", wave.getRuleName() == null ? "" : wave.getRuleName());
            }
            if (needValues.contains("wave_short_id")) {
                result.put("wave_short_id", wave.getShortId() == null ? "" : wave.getShortId().toString());
            }
            if (needValues.contains("wave_rule_group_name")) {
                result.put("wave_rule_group_name", wave.getRuleGroupName() == null ? "" : wave.getRuleGroupName());
            }
            if (needValues.contains("wave_picking_type")) {
                result.put("wave_picking_type", PickingType.parseType(wave.getPickingType() == null ? 0 : wave.getPickingType()).getName());
            }
        }
    }

    public static String installWavePositionSortNum(Wave wave, WaveTrade waveTrade) {
        if (wave != null && !Wave.PositionNoTypeEnum.ITEM.getValue().equals(wave.getPositionNoType())) {
            if (waveTrade.getPositionSidNum() != null && waveTrade.getPositionSidNum() != 1) {
                return waveTrade.getPositionSidIndex() + "/" + waveTrade.getPositionSidNum();
            }
            return "1";
        } else {
            return "";
        }
    }

    /**
     * 处理商品位置号
     */
    public static void handleItemPositionCode(HandleItemPositionCodeParams params) {
        List<String> needValues = params.getNeedValues();
        Trade trade = params.getTrade();
        Map<String, Object> result = params.getResult();
        Staff staff = params.getStaff();
        ITradeWaveService tradeWaveService = params.getTradeWaveService();

        if (!needValues.contains("wave_item_position_code") && !needValues.contains("table_wave_item_position_code")) {
            return;
        }

        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        Wave wave = params.getWave();
        if (null == wave || !Wave.PositionNoTypeEnum.ITEM.getValue().equals(wave.getPositionNoType())) {
            return;
        }

        List<AssoWaveItem> assoWaveItems = tradeWaveService.queryAssoWaveItemsByWaveId(staff, wave.getId());
        if (CollectionUtils.isEmpty(assoWaveItems)) {
            return;
        }
        Map<String, AssoWaveItem> assoWaveItemMap = new HashMap<>(assoWaveItems.size(), 1);
        for (AssoWaveItem assoWaveItem : assoWaveItems) {
            assoWaveItemMap.put(getItemKey(assoWaveItem.getItemSysId(), assoWaveItem.getSkuSysId()), assoWaveItem);
        }

        List<String> itemPositionCodes = new ArrayList<>();
        for (Order order : orderList) {
            AssoWaveItem assoWaveItem = assoWaveItemMap.get(getItemKey(order.getItemSysId(), order.getSkuSysId()));
            itemPositionCodes.add(null == assoWaveItem ? "" : WaveUtils.generatePositionCode(staff, assoWaveItem.getPositionNo()));
        }
        if (needValues.contains("wave_item_position_code")) {
            result.put("wave_item_position_code", StringUtils.join(itemPositionCodes, ","));
        }
        if (needValues.contains("table_wave_item_position_code")) {
            result.put("table_wave_item_position_code", StringUtils.join(itemPositionCodes, ","));
        }
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String key = entry.getKey();
            if (StringUtils.isNotBlank(key) && key.contains("-") && (key.contains("table_wave_item_position_code") || key.contains("wave_item_position_code"))) {
                String value = (String) entry.getValue();
                String[] split = StringUtils.split(value, ",");
                if (split == null) {
                    return;
                }
                List<String> newValue = new ArrayList<>();
                for (int i = 0; i < split.length; i++) {
                    newValue.add(split[i] + " " + itemPositionCodes.get(i));
                }
                result.put(key, StringUtils.join(newValue, ","));
            }
        }
    }


    /**
     * 初始化入库单中的Item相关信息
     */
    public static void initSysSkuIdsAndSysItemIdsOfItem(Staff staff, WarehouseEntry warehouseEntry, Map<Long, DmjItem> itemIdMap, Map<Long, DmjSku> skuIdMap, List<String> needValues, Map<String, Object> result) {
        List<WarehouseEntryDetail> details = warehouseEntry.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        StringBuilder pic = new StringBuilder();
        StringBuilder mainPic = new StringBuilder();
        StringBuilder remarkSB = new StringBuilder();
        StringBuilder detailRemark = new StringBuilder();
        StringBuilder mainOuterId = new StringBuilder();
        StringBuilder tableItemBarcode = new StringBuilder();
        StringBuilder tableSkuBarcode = new StringBuilder();
        for (WarehouseEntryDetail detail : details) {
            Long skuSysId = detail.getSkuSysId();
            Long itemSysId = detail.getItemSysId();
            DmjItem item = itemIdMap.get(itemSysId);
            if (item == null) {
                throw new RuntimeException("系统商品不存在或已删除");
            }
            String picPath = item.getPicPath();
            String skuBarcode = item.getBarcode();
            if (skuSysId > 0) {
                DmjSku sku = skuIdMap.get(skuSysId);
                String skuPicStr = sku == null ? "" : sku.getSkuPicPath();
                if (StringUtils.isNotEmpty(skuPicStr) && !StockConstants.PATH_NO_PIC.equals(skuPicStr)) {
                    picPath = skuPicStr;
                }
                skuBarcode = (sku == null || StringUtils.isBlank(sku.getBarcode())) ? item.getBarcode() : sku.getBarcode();
            }
            tableItemBarcode.append(StringUtils.trimToEmpty(item.getBarcode())).append(",");
            tableSkuBarcode.append(StringUtils.defaultString(skuBarcode)).append(",");
            mainOuterId.append(CommaReplaceUtils.commaReplace(StringUtils.trimToEmpty(item.getOuterId()))).append(",");
            pic.append(picPath).append(",");
            mainPic.append(detail.getItemPicPath()).append(",");
            remarkSB.append(CommaReplaceUtils.commaReplace(StringUtils.trimToEmpty(item.getRemark()))).append(",");
            detailRemark.append(CommaReplaceUtils.commaReplace(StringUtils.trimToEmpty(detail.getRemark()))).append(",");
        }

        //设置规格图片
        setFieldValue(result, needValues, "table_item_pic", pic);

        //设置主商品图片url
        setFieldValue(result, needValues, "table_main_item_pic", mainPic);

        //设置商品备注
        setFieldValue(result, needValues, "table_item_remark", remarkSB);

        //设置主商家编码
        setFieldValue(result, needValues, "table_main_outer_id", mainOuterId);

        //商品条形码
        setFieldValue(result, needValues, "table_item_barcode", tableItemBarcode);

        //规格条形码
        setFieldValue(result, needValues, "table_sku_barcode", tableSkuBarcode);

        //商品明细备注
        setFieldValue(result, needValues, "table_item_detail_remark", detailRemark);
    }

    /**
     * @param result       返回结果
     * @param needValues   需要字段集合
     * @param fieldName    字段名
     * @param filedValueSB 字段名对应的值
     * @description: 设置需要字段的值
     * <AUTHOR>
     * @date 2018/8/27 下午7:27
     */
    private static void setFieldValue(Map<String, Object> result, List<String> needValues, String fieldName, StringBuilder filedValueSB) {

        if (null == result) {
            return;
        }

        if (CollectionUtils.isEmpty(needValues)) {
            return;
        }

        if (StringUtils.isEmpty(fieldName)) {
            return;
        }

        if (needValues.contains(fieldName)) {
            if (filedValueSB.length() > 0) {
                filedValueSB.deleteCharAt(filedValueSB.length() - 1);
            }
            result.put(fieldName, filedValueSB);
        }
    }

    /**
     * 填充指定数据到map
     */
    public static void setFieldValue(Map<String, Object> result, List<String> needValues, String fieldName,
                                     String fieldValue) {
        if (null == result || CollectionUtils.isEmpty(needValues) || StringUtils.isEmpty(fieldName)) {
            return;
        }
        if (needValues.contains(fieldName)) {
            result.put(fieldName, fieldValue);
        }
    }

    /**
     * 其他单据调用打印模板去打印数据的时候可以通过该方法去匹配所需要的后续处理方式
     */
    public static void handleMsgToOtherBills(Object source, Integer billType, String sids, IEventCenter eventCenter, String tempalteType, Staff staff) {
        Assert.notNull(billType, "请传入单据类型参数");
        if (billType == 1) {
            eventCenter.fireEvent(source, new EventInfo("trade.purchase.out." + tempalteType + ".print").setArgs(new Object[]{sids, staff}), null);
        }
    }

    /**
     * @param printData     返回的打印数据
     * @param printCountNum 打印总数
     * @description: 处理打印序号总数
     * <AUTHOR>
     * @date 2018/9/14 上午9:30
     */
    public static void handlePrintTotalNum(JSONObject printData, Integer printCountNum) {
        if (null == printData) {
            return;
        }
        printData.put("printCountNum", printCountNum);
    }

    public static Map<Long, String> initSidToSuitesDetailMap(List<Trade> trades, List<String> needValues) {
        Map<Long, String> result = Maps.newHashMap();
        if (needValues.contains("suites_detail")) {
            for (Trade singleTrade : trades) {
                String sysStatus = singleTrade.getSysStatus();
                List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
                if (CollectionUtils.isEmpty(orderList))
                    continue;
                StringBuilder detailStr = new StringBuilder();
                //订单本身已发货已完成已关闭
                boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(sysStatus);
                for (Order order : orderList) {
                    //过滤需要打印的子订单 (1、订单本身已发货已完成已关闭,所有子订单都可打印 2、订单已审核,只能打印已审核的子订单)
                    if (!isAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                        continue;
                    }
                    if ((StringUtils.equals(Trade.SYS_STATUS_FINISHED, sysStatus) || StringUtils.equals(Trade.SYS_STATUS_SELLER_SEND_GOODS, sysStatus))
                            && Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                        continue;
                    }
                    List<Order> suits = order.getSuits();
                    if (CollectionUtils.isNotEmpty(suits)) {
                        detailStr.append(order.getSysTitle()).append("(");
                        for (Order suit : suits) {
                            String sysTitle = suit.getSysTitle();
                            detailStr.append(null == sysTitle ? "" : sysTitle).append("&#44;");
                        }
                        detailStr.delete(detailStr.length() - 5, detailStr.length()).append("),");
                    }
                }
                if (detailStr.length() > 0) {
                    detailStr.delete(detailStr.length() - 1, detailStr.length());
                }
                result.put(singleTrade.getSid(), detailStr.toString());
            }
        }
        return result;
    }

    /**
     * 获得 套件名称明细明细
     */
    public static String getSuitesDetail(Trade trade) {
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orderList)) {
            return "";
        }
        StringBuilder detailStr = new StringBuilder();
        for (Order order : orderList) {
            List<Order> suits = order.getSuits();
            if (CollectionUtils.isNotEmpty(suits)) {
                detailStr.append(order.getSysTitle()).append("(");
                for (Order suit : suits) {
                    String sysTitle = suit.getSysTitle();
                    detailStr.append(null == sysTitle ? "" : sysTitle).append("&#44;");
                }
                detailStr.delete(detailStr.length() - 5, detailStr.length()).append("),");
            }
        }
        if (detailStr.length() > 0) {
            detailStr.delete(detailStr.length() - 1, detailStr.length());
        }
        return detailStr.toString();
    }

    /**
     * 将菜鸟的返回值转化为云打印的返回值
     */
    public static void initCloudFieldValues(List<Map<String, Object>> fieldValues) {
        for (Map<String, Object> map : fieldValues) {
            String shortAddress = (String) map.get("express_short_address");
            if (null != shortAddress) {
                String[] strs = shortAddress.split("##");
                if (strs.length > 0) {
                    map.put("routingInfo_sortation_name", strs[0]);
                    if (strs.length > 1) {
                        map.put("routingInfo_routeCode", strs[1]);
                    }
                }
            }
            String type = (String) map.get("trade_type");
            if ("cod".equals(type)) {
                map.put("shippingOption_title", "货到付款");
                String payment = (String) map.get("trade_payment");
                if (StringUtils.isNotBlank(payment)) {
                    map.put("shippingOption_services_SVCCOD_value", payment);
                    map.put("shippingOption_code", "1");
                } else {
                    map.put("shippingOption_services_SVCCOD_value", "");
                }
            } else {
                map.put("shippingOption_title", "标准快递");
            }
        }
    }

    /**
     * 获取子订单map
     * key是sid_order的id
     */
    public static Map<String, Order> getOldOrderMap(List<Trade> trades) {
        Map<String, Order> result = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(trades)) {
            for (Trade trade : trades) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    result.put(order.getSid() + "_" + order.getId(), order);
                }
            }
        }
        return result;
    }

    /**
     * 处理打印数据返回结果
     */
    public static FieldValuesResponse handleFieldValuesResponse(Map<String, Order> map, List<Map<String, Object>> resultsBySidSort, Long[] sids) {
        FieldValuesResponse response = new FieldValuesResponse();
        response.setResult(resultsBySidSort);
        setFieldValuesResponse(response, map, sids);
        return response;
    }

    /**
     * 处理打印数据返回结果
     */
    public static void setFieldValuesResponse(FieldValuesResponse response, Map<String, Order> sidIdToOrder, Long[] sids) {
        StringBuilder sb = new StringBuilder();
        if (MapUtils.isNotEmpty(sidIdToOrder)) {
            sb.append("本次打印【");
            for (Long sid : sids) {
                sb.append(sid).append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append("】将过滤部分发货、订单关闭、商品无需发货以及用户要求过滤的商品共【").append(sidIdToOrder.size()).append("】种&#44;详情如下：");
            for (Map.Entry<String, Order> entry : sidIdToOrder.entrySet()) {
                Order order = entry.getValue();
                sb.append(order.getSid()).append("中的商品").append(order.getSysOuterId()).append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        if (StringUtils.isNotEmpty(sb.toString())) {
            response.setWarnMsg(sb.toString());
        }
        if (!sidIdToOrder.isEmpty()) {
            Collection<Order> values = sidIdToOrder.values();
            Set<Long> filterSids = values.stream().map(Order::getSid).collect(Collectors.toSet());
            response.setFilterItemSids(filterSids);
        }
    }

    /**
     * 把排序要用的字段添加到needValues里面
     */
    public static void needValuesAddSortField(List<String> needValues, EnumTemplateKind templateKind) {
        Map<Integer, String> sortMap = SortExpressTemplateHelper.getSortMap(templateKind);
        if (MapUtils.isNotEmpty(sortMap)) {
            sortMap.values().stream()
                    .filter(StringUtils::isNotBlank)
                    .flatMap(value -> Arrays.stream(value.split(",")))
                    .forEach(needValues::add);
        }
    }

    public static boolean orderReplacePlatformField(Order order, boolean openPlatformItemConf) {
        return order != null && ((order.getSysItemChanged() != null && order.getSysItemChanged() == 1 && openPlatformItemConf));
    }

    /**
     * 填充平台商品名称 快递单
     */
    public static void ifNeedItemPlatformTitle(String itemPlatformTitleKey, List<String> needValues, Trade singleTrade,
                                               Map<String, Object> result, Map<String, TbItem> tbItemMap, boolean openPlatformItemConf) {
        if (needValues.contains(itemPlatformTitleKey)) {
            if (singleTrade == null) {
                return;
            }
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            List<String> titles = new ArrayList<>();
            for (Order order : orderList) {
                TbItem tbItem = null;
                if (MapUtils.isNotEmpty(tbItemMap)) {
                    tbItem = tbItemMap.get(order.getNumIid());
                }
                // openPlatformItemConf：如果用户修改了商品信息，就取系统商品的名称
                if (orderReplacePlatformField(order, openPlatformItemConf)) {
                    titles.add(CommaReplaceUtils.commaReplace(order.getSysTitle()));
                } else {
                    if (StringUtils.isNotBlank(order.getTitle())) {
                        titles.add(CommaReplaceUtils.commaReplace(order.getTitle()));
                        continue;
                    }
                    if (tbItem != null && StringUtils.isNotBlank(tbItem.getTitle())) {
                        titles.add(CommaReplaceUtils.commaReplace(tbItem.getTitle()));
                        continue;
                    }
                    titles.add(CommaReplaceUtils.commaReplace(order.getSysTitle()));
                }
            }
            result.put(itemPlatformTitleKey, StringUtils.join(titles, ","));
        }
    }

    public static void ifNeedItemPlatformTitle4(boolean needPlatformItemMsg, Trade singleTrade, Map<String, Object> result, Map<String, TbItem> tbItemMap) {
        if (needPlatformItemMsg) {
            if (singleTrade == null) {
                return;
            }
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }

            List<String> itemPlatOuterId = new ArrayList<>();
            for (Order order : orderList) {
                TbItem tbItem = tbItemMap.get(order.getNumIid());
                if (StringUtils.isNotBlank(order.getOuterIid())) {
                    itemPlatOuterId.add(CommaReplaceUtils.commaReplace(order.getOuterIid()));
                } else {
                    itemPlatOuterId.add(tbItem == null ? "" : CommaReplaceUtils.commaReplace(tbItem.getOuterId()));
                }
            }
            if (CollectionUtils.isNotEmpty(itemPlatOuterId)) {
                result.put("item_platform_outer_id", StringUtils.join(itemPlatOuterId, ","));
            }
        }
    }

    /**
     * 填充平台商品名称 发货单
     */
    public static void ifNeedItemPlatformTitle4Deliver(boolean needPlatformItemMsg, List<String> needValues, Trade singleTrade,
                                                       Map<String, Object> result, Map<String, TbItem> tbItemMap, boolean isConfigPrintSys) {
        if (needPlatformItemMsg) {
            if (singleTrade == null) {
                return;
            }
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            List<String> titles = new ArrayList<>();
            List<String> skuName = new ArrayList<>();
            List<String> platOuterId = new ArrayList<>();
            List<String> itemPlatOuterId = new ArrayList<>();
            for (Order order : orderList) {
                TbItem tbItem = tbItemMap.get(order.getNumIid());
                TbSku tbSku = (TbSku) tbItemMap.get(order.getNumIid() + "_" + order.getSkuId());
                //平台规格名称，取订单中sku规格名称，再取sku中规格名称，最后取订单系统规格名称
                if (StringUtils.isNotBlank(order.getSkuPropertiesName())) {
                    skuName.add(CommaReplaceUtils.commaReplace(order.getSkuPropertiesName()));
                } else if (tbSku != null && StringUtils.isNotEmpty(tbSku.getPropertiesName())) {
                    skuName.add(CommaReplaceUtils.commaReplace(tbSku.getPropertiesName()));
                } else {
                    skuName.add(CommaReplaceUtils.commaReplace(order.getSysSkuPropertiesName()));
                }
                // 平台编码,取订单中的skuOuterId,再取对应表中的平台商家编码,规格没有取主商品的
                if (StringUtils.isNotBlank(order.getOuterSkuId())) {
                    platOuterId.add(CommaReplaceUtils.commaReplace(order.getOuterSkuId()));
                } else if (tbSku != null && StringUtils.isNotEmpty(tbSku.getOuterId())) {
                    platOuterId.add(CommaReplaceUtils.commaReplace(tbSku.getOuterId()));
                } else {
                    platOuterId.add(tbItem == null ? "" : CommaReplaceUtils.commaReplace(tbItem.getOuterId()));
                }
                if (orderReplacePlatformField(order, isConfigPrintSys) || tbItem == null || StringUtils.isBlank(tbItem.getTitle())) {
                    titles.add(CommaReplaceUtils.commaReplace(order.getSysTitle()));
                } else {
                    titles.add(CommaReplaceUtils.commaReplace(tbItem.getTitle()));
                }
                if (StringUtils.isNotBlank(order.getOuterIid())) {
                    itemPlatOuterId.add(CommaReplaceUtils.commaReplace(order.getOuterIid()));
                } else {
                    itemPlatOuterId.add(tbItem == null ? "" : CommaReplaceUtils.commaReplace(tbItem.getOuterId()));
                }
            }
            result.put("table_item_platform_title", StringUtils.join(titles, ","));
            if (CollectionUtils.isNotEmpty(skuName)) {
                result.put("table_specification_paltform", StringUtils.join(skuName, ","));
            }
            if (CollectionUtils.isNotEmpty(platOuterId)) {
                result.put("table_platform_outer_id", StringUtils.join(platOuterId, ","));
            }
            if (CollectionUtils.isNotEmpty(itemPlatOuterId)) {
                result.put("table_item_platform_outer_id", StringUtils.join(itemPlatOuterId, ","));
            }
        }
    }

    public static void setWaybillType(List<String> needValues, Trade singleTrade, Map<String, Object> result) {
        if (needValues.contains("trade_print_waybill_type")) {
            result.put("trade_print_waybill_type", TradeUtils.haveAllChildSplitTag(singleTrade) ? "子单" : "主单");
        }
    }

    /**
     * 初始化订单类型相关字段
     */
    public static void __handleTradeTabs(List<String> needValues, Trade singleTrade, Map<String, Object> result) {
        if (!needValues.contains("trade_tab")) {
            return;
        }
        StringBuilder trade_tab = new StringBuilder();
        String tab = TRADE_TAB_MAP.get(singleTrade.getType());
        if (StringUtils.isNotBlank(tab)) {
            trade_tab.append(tab).append(" ");
        }
        if ("sys".equals(singleTrade.getSource()) && !"putchase_out".equals(singleTrade.getType())) {
            trade_tab.append("手工").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsPresell())) {
            trade_tab.append("预售").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsUrgent())) {
            trade_tab.append("急").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getScalping())) {
            trade_tab.append("空包单").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsJz())) {
            trade_tab.append("家装").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsSmart())) {
            trade_tab.append("智选").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsStore())) {
            trade_tab.append("门店").append(" ");
        }
        if (trade_tab.length() > 1) {
            trade_tab.deleteCharAt(trade_tab.length() - 1);
        }
        result.put("trade_tab", trade_tab);
    }

    public static Map<String, TbItem> getTbItemMap(Staff staff, boolean needItemPlatformTitle, List<Trade> trades, PrintPageSearchService printPageSearchService) {
        Map<String, TbItem> tbItemMap = new HashMap<>();
        if (needItemPlatformTitle) {
            List<Order> orderList = TradeUtils.getOrders4Trade(trades);
            if (CollectionUtils.isNotEmpty(orderList)) {
                Set<String> numIids = new HashSet<>(orderList.size());
                List<String> skuIdList = new ArrayList<>();
                for (Order order : orderList) {
                    numIids.add(order.getNumIid());
                    skuIdList.add(order.getSkuId());
                }
                List<String> numIidList = new ArrayList<>(numIids.size());
                numIidList.addAll(numIids);
                List<TbItem> tbItems = printPageSearchService.queryTbItemList(staff, numIidList, skuIdList);
                for (TbItem tbItem : tbItems) {
                    //修复商品名称和规格名称对应不正确的BUG,当Item下有Sku的时候key为NumIid+"_"+skuId
                    if (CollectionUtils.isNotEmpty(tbItem.getSkuList())) {
                        for (TbSku tbSku : tbItem.getSkuList()) {
                            tbItemMap.put(tbItem.getNumIid() + "_" + tbSku.getSkuId(), tbSku);
                        }
                    }
                    tbItemMap.put(tbItem.getNumIid(), tbItem);
                }
            }
        }
        return tbItemMap;
    }

    /**
     * 处理京东一体化面单所有的与面单相关的信息
     * 主要是使用京配接口进行查询相关信息
     * packageCenterCode ===》始发地代码##目的地代码##路区代码
     * packageCenterName ===》始发分拣中心名称##目的分拣中心名称##集包地名称
     */
    public static void _handleJdWaybillMsg(List<String> needValues,
                                           Trade singleTrade,
                                           Map<String, Object> result,
                                           Map<String, OutSidPool> outSidPoolMap,
                                           User user,
                                           UserWlbExpressTemplate userWlbExpressTemplate) {
        if (MapUtils.isEmpty(outSidPoolMap) || !outSidPoolMap.containsKey(String.valueOf(singleTrade.getOutSid()))) {
            return;
        }
        OutSidPool outSidPool = outSidPoolMap.get(String.valueOf(singleTrade.getOutSid()));
        String packageCenterCode = outSidPool.getPackageCenterCode();
        String packageCenterName = outSidPool.getPackageCenterName();

        if (StringUtils.isBlank(packageCenterCode) || StringUtils.isBlank(packageCenterCode.replace("##", ""))
                || packageCenterCode.split("##").length != 3
                || StringUtils.isBlank(packageCenterName) || StringUtils.isBlank(packageCenterName.replace("##", ""))
                || packageCenterName.split("##").length != 3) {
            return;
        }
        String[] codes = packageCenterCode.split("##");
        String[] names = packageCenterName.split("##");
        //始发地代码
        jdNeedValuesSetValue(needValues, codes[0], result, "origin_code");
        //目的地代码
        jdNeedValuesSetValue(needValues, codes[1], result, "dest_code");
        //路区
        jdNeedValuesSetValue(needValues, codes[2], result, "jd_rode");
        //始发分拣中心名称
        jdNeedValuesSetValue(needValues, names[0], result, "origin_name");
        //目的分拣中心名称
        jdNeedValuesSetValue(needValues, names[1], result, "dest_name");
        //配送商家编码
        jdNeedValuesSetValue(needValues, user.getLogisticsOuterId(), result, "custom_code");
        //集包地名称
        jdNeedValuesSetValue(needValues, names[2], result, "package_center_name");
        //派送营业部
        jdNeedValuesSetValue(needValues, names[2], result, "site_name");
        //始发滑道+笼车
        jdNeedValuesSetValue(needValues, codes[0], result, "original_tabletrolley_code");
        //目的滑道+笼车
        jdNeedValuesSetValue(needValues, codes[1], result, "destination_tabletrolley_code");
        //京东的单号需要拼接-1-1-
        String newOutSid = null != singleTrade.getOutSid() ? singleTrade.getOutSid() + "-1-1-" : "";
        //运单号
        jdNeedValuesSetValue(needValues, singleTrade.getOutSid(), result, "waybill_num");
        //运单号条码（上）
        jdNeedValuesSetValue(needValues, newOutSid, result, "jd_express_top_barcode");
        //运单号条码（下）
        jdNeedValuesSetValue(needValues, singleTrade.getOutSid(), result, "waybill_num_barcode");
        //温层
        if (CollectionUtils.isNotEmpty(userWlbExpressTemplate.getFieldValuesCustom())) {
            List<FieldValue> filterList = userWlbExpressTemplate.getFieldValuesCustom().stream().filter(x -> EnumFieldValueName.GOODS_TYPE.getValue().equals(x.getName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                filterList = userWlbExpressTemplate.getFieldValuesCustom().stream().filter(x -> EnumFieldValueName.JD_GOODS_TYPE.getValue().equals(x.getName())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(filterList) && filterList.size() == 1) {
                jdNeedValuesSetValue(needValues, EnumJdGoodsType.getNameByValue(filterList.get(0).getValue()), result, EnumFieldValueName.GOODS_TYPE.getValue());
                result.put("goods_type_enum", filterList.get(0).getValue());
            }
        }

        String shippingOptionService = outSidPool.getShippingOptionService();
        if (StringUtils.isBlank(shippingOptionService)) {
            return;
        }

        JSONObject resultInfoDTO = JSON.parseObject(shippingOptionService);

        if (resultInfoDTO == null) {
            return;
        }

        //产品类型
        jdNeedValuesSetValue(
                needValues,
                EnumJdPromiseTimeType.getNameByValueAndMode(resultInfoDTO.getInteger("promiseTimeType"), resultInfoDTO.getInteger("expressOperationMode")),
                result,
                "promise_time_type");

        result.put("promise_time_type_enum", resultInfoDTO.getInteger("promiseTimeType"));

        JSONObject preSortResult = resultInfoDTO.getJSONObject("preSortResult");

        if (preSortResult == null) {
            return;
        }

        //76*130的集包地需要用collectionAddress
        if (EnumExpressSysTemplateId.JD_76_130.getSysTemplateId().equals(userWlbExpressTemplate.getSysTemplateId())) {
            jdNeedValuesSetValue(needValues, preSortResult.getString("collectionAddress"), result, "package_center_name");
        }

        //T水印
        jdNeedValuesSetValue(needValues, preSortResult.getString("coverCode"), result, "t_watermark");

        //目的分拣水印
        jdNeedValuesSetValue(needValues, preSortResult.getString("distributeCode"), result, "target_sort_watermark");
    }

    private static void jdNeedValuesSetValue(List<String> needValues, String value, Map<String, Object> result, String fieldName) {
        if (needValues.contains(fieldName)) {
            result.put(fieldName, value == null ? "" : value);
        }
    }


    public static int getItemOverlengthThreshold(Staff staff, IExpressTemplateBase template) {
        String itemOverlengthThreshold = PrintTemplateHelper.getFieldValueByKey(template, EnumFieldValueName.ITEM_OVERLENGTH_THRESHOLD.getValue(), "0");
        int itemOverlengthThresholdNum = 0;
        try {
            itemOverlengthThresholdNum = Integer.parseInt(itemOverlengthThreshold);
        } catch (NumberFormatException e) {
            logger.debug(LogHelper.buildLog(staff, String.format("item_overlength_threshold配置不为数字，templateId=%s", template.getId())));
        }
        return itemOverlengthThresholdNum;
    }

    /**
     * 商品列表长度超过阈值时移除商品明细字段
     */
    public static void removeItemDetailWhenOverlength(Staff staff, IExpressTemplateBase template, Map<String, Object> result, int itemOverlengthThresholdNum, Trade singleTrade) {
        String orderNumKey = "order_num";
        String fieldValueWithCustomSkuExceed = PrintTemplateHelper.getFieldValueByKey(template, EnumFieldValueName.CUSTOM_SKU_EXCEED.getValue(), "0");
        try {
            if ("0".equals(fieldValueWithCustomSkuExceed) || "2".equals(fieldValueWithCustomSkuExceed) || "3".equals(fieldValueWithCustomSkuExceed) || itemOverlengthThresholdNum <= 0) {
                return;
            }
            int orderNum = (Integer) result.get(orderNumKey);
            if (orderNum > itemOverlengthThresholdNum) {
                if (template instanceof UserExpressTemplate) {
                    removeItemDetail(result, EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS);
                } else if (template instanceof UserWlbExpressTemplate) {
                    removeItemDetail(result, WLB_EXPRESS_TEMPLATE_ITEM_DETAIL_KEYS);
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("商品列表长度超过阈值时移除商品明细字段出错，sid=%s", singleTrade.getSid())), e);
        } finally {
            result.remove(orderNumKey);
        }
    }

    /**
     * 移除商品明细字段
     *
     * @param templateItemDetailKeys 模板中的商品明细字段，不同模板可能并不相同
     */
    private static void removeItemDetail(Map<String, Object> result, Set<String> templateItemDetailKeys) {
        Iterator<String> iterator = result.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Set<String> splitKeys = new HashSet<>(Arrays.asList(key.split("-")));
            for (String splitKey : splitKeys) {
                String compale = splitKey;
                //TODO  直接去除字段的后缀   以后添加商品相关字段的时候注意 最好是定义一套后端规范  给每一个字段属性
                if (splitKey.endsWith("_up")) {
                    compale = splitKey.replace("_up", "");
                }
                if (splitKey.endsWith("_down")) {
                    compale = splitKey.replace("_down", "");
                }
                if (compale.contains("item_total_num") || compale.contains("item_cate_num")) {
                    break;
                }
                if (templateItemDetailKeys.contains(compale) || compale.contains("item") || compale.contains("sku")) {
                    iterator.remove();
                    break;
                }
            }
        }
    }


    /**
     * 将面单池对象中对应的打印服务拉取出来
     */
    public static void __getPrintData(List<String> needValues, Map<String, OutSidPool> outSidMap, Trade singleTrade, Map<String, Object> result,
                                      UserWlbExpressTemplate userWlbExpressTemplate,Integer isMultiExpress) {
        OutSidPool outSidPool = outSidMap.get(singleTrade.getOutSid());
        if (outSidPool == null && !CommonConstants.VALUE_YES.equals(isMultiExpress) &&
                (WlbTemplateTypeEnum.CLOUD.getValue().equals(userWlbExpressTemplate.getWlbTemplateType()) || WlbTemplateTypeEnum.DIVIDE.getValue().equals(userWlbExpressTemplate.getWlbTemplateType()))
                && StringUtils.isNotBlank(singleTrade.getOutSid())) {
            throw new IllegalArgumentException(String.format("订单【%s】快递单号【%s】取号超过15-30天未产生物流轨迹,单号已回收,不能预览打印!请重新取号在进行打印", singleTrade.getSid(), singleTrade.getOutSid()));
        }
        if (outSidPool != null) {
            String js = outSidPool.getShippingOptionService();
            if (StringUtils.isNotEmpty(js)) {
                // 众邮快递
                if (Long.valueOf(881232L).equals(userWlbExpressTemplate.getExpressId())) {
                    if (WlbTemplateTypeEnum.BRANCH.getValue().equals(userWlbExpressTemplate.getWlbTemplateType())) {
                        result.putAll(JSONObject.parseObject(js, Map.class));
                        result.put("to_branch_name", result.get("endBranchName"));
                        //始发分拣中心编号
                        String startCrossCode = handleFiled("startCrossCode", result);
                        String startTabletrolleyCode = handleFiled("startTabletrolleyCode", result);
                        result.put("startSortCenterCode", startCrossCode + "-" + startTabletrolleyCode);
                        //目的分拣中心编号
                        String endCrossCode = handleFiled("endCrossCode", result);
                        String endTabletrolleyCode = handleFiled("endTabletrolleyCode", result);
                        result.put("endSortCenterCode", endCrossCode + "-" + endTabletrolleyCode);
                    } else {
                        JdBigshotBo jdBigshotBo = JSONObject.parseObject(js, JdBigshotBo.class);
                        result.put("to_branch_name", jdBigshotBo.getToBranchName());
                        result.put("to_three_code", jdBigshotBo.getToCrossCode() + "-" + jdBigshotBo.getToTabletrolleyCode());
                        result.put("from_branch_name", jdBigshotBo.getFromBranchName());
                        result.put("from_three_code", jdBigshotBo.getFromCrossCode() + "-" + jdBigshotBo.getFromTabletrolleyCode());
                        result.put("branch_name", jdBigshotBo.getBranchName());
                        result.put("road", jdBigshotBo.getRoad());

                    }
                    //众邮单号处理
                    Object waybill_num = result.get("waybill_num");
                    Object waybillCode = result.get("waybillCode");
                    String outsid = String.valueOf(waybill_num == null || "".equals(waybill_num) ? waybillCode : waybill_num);
                    if (StringUtils.isBlank(outsid)) {
                        outsid = (String) result.get("waybill_num_barcode");
                    }
                    if (StringUtils.isNotBlank(outsid)) {
                        outsid = outsid + "-1-1-";
                        result.put("waybillCode", outsid);
                        result.put("waybill_num", outsid);
                        result.put("waybill_num_barcode", outsid);
                    }
                }
                result.put("template_print_data", cloudTemplateUrlReplace(userWlbExpressTemplate, js));
                List<String> packageids = new ArrayList<>();
                packageids.add(outSidPool.getTradeOrder());
                //设置包裹号（团好货需要包裹id）
                result.put("package_ids", packageids);
                //顺丰需要打印二维码 就在这个里面取
                if (needValues.contains("sf_ls_qrcode")) {
                    String sfTwoDimesionCode = getSfTwoDimesionCode(js);
                    if (StringUtils.isNotBlank(sfTwoDimesionCode)) {
                        logger.debug("获取到的顺丰揽件二维码数据为:" + sfTwoDimesionCode);
                        result.put("sf_ls_qrcode", sfTwoDimesionCode);
                    }
                }
                //百世快递要打印末端分拣码
                if (needValues.contains("pdf_info")) {
                    HtOrderBigMark htOrderBigMark = JSON.parseObject(js, HtOrderBigMark.class);
                    result.put("pdf_info", StringUtils.isNotBlank(htOrderBigMark.getSortingCode()) ? htOrderBigMark.getSortingCode() : "");
                }
            }
        }
    }

    private static String handleFiled(String key, Map<String, Object> result) {
        if (MapUtils.isEmpty(result) || !result.containsKey(key)) {
            return "";
        }
        Object obj = result.get(key);
        if (obj == null) {
            return "";
        }
        return (String) obj;
    }

    /**
     * 替换掉打印数据中的模板url
     * 防止出现更换模板之后使用老的模板进行打印
     */
    private static String cloudTemplateUrlReplace(UserWlbExpressTemplate userWlbExpressTemplate, String js) {
        if (!StringUtils.contains(js, "templateURL")) {
            return js;
        }
        if (WlbTemplateTypeEnum.CLOUD.getValue().equals(userWlbExpressTemplate.getWlbTemplateType()) || WlbTemplateTypeEnum.DIVIDE.getValue().equals(userWlbExpressTemplate.getWlbTemplateType())) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(js);
                if (jsonObject.containsKey("templateURL")) {
                    if (!StringUtils.equals(jsonObject.getString("templateURL"), userWlbExpressTemplate.getCloudTemplateUrl())) {
                        jsonObject.put("templateURL", userWlbExpressTemplate.getCloudTemplateUrl());
                        return jsonObject.toJSONString();
                    }
                }
            } catch (Exception e) {
                Logs.error("替换json数据中templateUrl失败，失败原因：" + e.getMessage() + "json：" + js, e);
            }
        }
        return js;
    }

    /**
     * 获取 sf 二维码
     */
    private static String getSfTwoDimesionCode(String shippingOptionService) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(shippingOptionService);
            String detailStr = jsonObject.getString("detail");
            if (StringUtils.isBlank(detailStr)) {
                return "";
            }
            JSONObject detail = JSONObject.parseObject(detailStr);
            return StringUtils.isBlank(detail.getString("twoDimensionCode")) ? "" : detail.getString("twoDimensionCode");
        } catch (Exception e) {
            logger.debug("解析 打印数据失败 ,打印数据:" + shippingOptionService + " message:" + e.getMessage());
            return "";
        }
    }

    /**
     * 计算顺丰保价服务值
     */
    public static String getSfInsureAmountValue(Staff staff, UserWlbExpressTemplate template, Trade singleTrade, ITradeSearchService tradeQueryService) {
        String fieldValueByKey = PrintTemplateHelper.compatibleTemplateFileds(template, EnumServiceValueName.SF_INSURE_AMOUNT.getFieldName(), EnumServiceValueName.INSURE_AMOUNT.getFieldName());
        if ("2".equals(fieldValueByKey)) {
            return TradeUtils.getPayment(singleTrade);
        } else if ("3".equals(fieldValueByKey)) {
            return PrintTemplateHelper.compatibleTemplateFileds(template, EnumServiceValueName.SF_INSURE_AMOUNT.getFieldValue(), EnumServiceValueName.INSURE_AMOUNT.getFieldValue());
        }
        return "";
    }

    /**
     * 计算放心购保价服务值
     */
    public static String getInsureAmountValue(UserWlbExpressTemplate template, Trade singleTrade) {
        String fieldValueByKey = compatibleTemplateFileds(template, EnumServiceValueName.INSURE_AMOUNT.getFieldName(), EnumServiceValueName.INSURE_AMOUNT.getFieldName());
        if ("2".equals(fieldValueByKey)) {
            return String.format("%.2f", Double.valueOf(TradeUtils.getPayment(singleTrade)));
        } else if ("3".equals(fieldValueByKey)) {
            return compatibleTemplateFileds(template, EnumServiceValueName.INSURE_AMOUNT.getFieldValue(), EnumServiceValueName.INSURE_AMOUNT.getFieldValue());
        }
        return "";
    }

    /**
     * 计算放心购保价服务值
     */
    public static String getInsureAmountValue(Staff staff, UserWlbExpressTemplate template, Trade singleTrade, ITradeSearchService tradeQueryService) {
        String fieldValueByKey = PrintTemplateHelper.compatibleTemplateFileds(template, EnumServiceValueName.INSURE_AMOUNT.getFieldName(), EnumServiceValueName.INSURE_AMOUNT.getFieldName());
        if ("2".equals(fieldValueByKey)) {
            return TradeUtils.getPayment(singleTrade);
        } else if ("3".equals(fieldValueByKey)) {
            return PrintTemplateHelper.compatibleTemplateFileds(template, EnumServiceValueName.INSURE_AMOUNT.getFieldValue(), EnumServiceValueName.INSURE_AMOUNT.getFieldValue());
        }
        return "";
    }

//    /**
//     * 获取所有的网点服务
//     * @param template
//     * @return
//     */
//    public static Map<String, String> getBranchAccountServiceInfo(UserWlbExpressTemplate template) {
//        String serviceInfo = PrintTemplateHelper.getFieldValueByKey(template, EnumFieldValueName.BRANCH_ACCOUNT_SERVICE_INFO.getValue());
//        Map<String, String> map= Maps.newHashMap();
//        try {
//           map = JSON.parseObject(serviceInfo, Map.class);
//        } catch (Exception e) {
//            logger.error("转化网点服务json出错:" + serviceInfo, e);
//        }
//        return map ;
//    }
//
//    /**
//     * 获取指定服务值
//     * @param template
//     * @param name
//     * @return
//     */
//    public static String getBranchAccountServiceInfoByName(UserWlbExpressTemplate template, String name) {
//        Map<String, String> info = getBranchAccountServiceInfo(template);
//        String s = info.get(name);
//        if (null == s ){
//            return "";
//        }
//        return s;
//    }

    /**
     * 校验模板是否为一体化面单
     */
    public static Boolean checkIsJdIntegratedTemplate(UserWlbExpressTemplate template) {
        return template != null
                && WlbTemplateTypeEnum.JD.getValue().equals(template.getWlbTemplateType())
                && (CloudTemplatePaperTypeEnum.JD_INTEGRATED.getValue().equals(template.getCloudTemplatePaperType()) || CloudTemplatePaperTypeEnum.EXPRESS_COUPON.getValue().equals(template.getCloudTemplatePaperType()));
    }


    /**
     * 处理货位数量在组合字段时问题
     */
    public static void __replaceSectionCodeNumWithCombinFiled(List<String> needValues, Map<String, Object> result, String split) {
        if (!needValues.contains("table_section_num")) {
            return;
        }
        Object object = result.get("table_section_num");
        if (object == null) {
            return;
        }
        split = ",";
        String str = (String) object;
        if (StringUtils.isNotBlank(str) && StringUtils.isNotBlank(str.replace(",", ""))) {
            String[] keyValues = StringUtils.splitByWholeSeparatorPreserveAllTokens(str, ",");
            for (String needValue : needValues) {
                if (needValue.contains("-") && needValue.contains("table_section_num")) {
                    Object obj = result.get(needValue);
                    if (obj != null) {
                        String str2 = (String) obj;
                        if (StringUtils.isNotBlank(str2) && StringUtils.isNotBlank(str.replace(split, ""))) {
                            String[] oldValues = StringUtils.splitByWholeSeparatorPreserveAllTokens(str2, ",");
                            if (oldValues.length != keyValues.length) {
                                continue;
                            }
                            List<String> resultString = new ArrayList<>();
                            for (int i = 0; i < oldValues.length; i++) {
                                resultString.add(oldValues[i] + " " + keyValues[i]);
                            }
                            result.put(needValue, StringUtils.join(resultString, split));
                        }
                    }
                }
            }
        }
    }


    public static void __fillGiftMsg(Trade trade, Map<String, Object> result, PrintItemMergeRule rule) {
        if (rule.getNeedApendGiftKey() && !rule.getHideGift() && MapUtils.isNotEmpty(result)) {
            __fillGiftMsg(trade, result, "");
        }
    }

    /**
     * 填充打印数据中的赠品相关信息， 并appendFields不为空的情况下将赠品信息追加到这字段值中间去
     */
    public static void __fillGiftMsg(Trade trade, Map<String, Object> result, String appendFields) {
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        List<String> giftMsg = new ArrayList<>();
        for (Order order : orderList) {
            giftMsg.add((order.getGiftNum() != null && order.getGiftNum() > 0) ? "【赠品】" : "");
        }
        result.put("item_gift_msg", StringUtils.join(giftMsg, ","));
        if (StringUtils.isNotEmpty(appendFields)) {
            String[] fieldValues = StringUtils.splitByWholeSeparatorPreserveAllTokens(result.get(appendFields) == null ? "" : String.valueOf(result.get(appendFields)), ",");
            if (fieldValues != null && giftMsg.size() == fieldValues.length) {
                List<String> newValues = new ArrayList<>();
                for (int i = 0; i < fieldValues.length; i++) {
                    newValues.add(giftMsg.get(i) + "  " + fieldValues[i]);
                }
                result.put(appendFields, StringUtils.join(newValues, ","));
            }
        }
    }


    public static void _handleKuaYueWaybillMsg(List<String> needValues, Trade singleTrade, Map<String, Object> result, Map<String, OutSidPool> outSidMap, User user, UserWlbExpressTemplate userWlbExpressTemplate) {
        Integer wlbTemplateType = userWlbExpressTemplate.getWlbTemplateType();
        Long expressId = userWlbExpressTemplate.getExpressId();
        if (WlbTemplateTypeEnum.BRANCH.getValue().equals(wlbTemplateType) && 5300000110733L == expressId) {
            OutSidPool outSidPool = outSidMap.get(String.valueOf(singleTrade.getOutSid()));
            if (outSidPool == null) {
                return;
            }
            String packageCenterCode = outSidPool.getPackageCenterCode();
            String packageCenterName = outSidPool.getPackageCenterName();
            String shortAddress = outSidPool.getShortAddress();
            if (needValues.contains("dest_code")) {
                result.put("dest_code", com.raycloud.dmj.tve.utils.StringUtils.nvl(packageCenterCode));
            }
            if (needValues.contains("package_center_name")) {
                result.put("package_center_name", com.raycloud.dmj.tve.utils.StringUtils.nvl(packageCenterName));
            }
            if (needValues.contains("express_short_address")) {
                result.put("express_short_address", com.raycloud.dmj.tve.utils.StringUtils.nvl(shortAddress));
            }
        }
    }

    /**
     * 处理优惠金额
     */
    public static void setDiscountFee(Trade singleTrade, Map<String, Object> result) {
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        BigDecimal totalDiscountFee = BigDecimal.ZERO;
        for (Order order : orderList) {
            totalDiscountFee = totalDiscountFee.add(new BigDecimal(order.getDiscountFee() == null ? "0" : order.getDiscountFee())).setScale(2, RoundingMode.HALF_UP);
        }
        result.put("trade_discount_fee", totalDiscountFee);
    }

    /**
     * 添加商品的第一第二属性 并且做统计
     */
    public static void addItemProperties(Staff staff, List<String> needValues, Trade singleTrade, Map<String, Object> result,
                                         PrintPageSearchService printPageSearchService, UserDeliverTemplate userDeliverTemplate) {
        if (!Long.valueOf(9L).equals(userDeliverTemplate.getSysTemplateId()) || singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<Long> itemSysIds = new ArrayList<>();
        List<Long> skuSysIds = new ArrayList<>();
        for (Order order : orderList) {
            Long skuSysId = order.getSkuSysId();
            if (skuSysId > 0 && !skuSysIds.contains(skuSysId)) {
                skuSysIds.add(skuSysId);
                continue;
            }
            Long itemSysId = order.getItemSysId();
            if (itemSysId > 0 && !itemSysIds.contains(itemSysId)) {
                itemSysIds.add(itemSysId);
            }
        }
        List<DmjSku> dmjSkus = printPageSearchService.queryDmjItems4Print(staff, itemSysIds, skuSysIds);
        if (CollectionUtils.isEmpty(dmjSkus)) {
            return;
        }
        if (orderList.size() == 1 && dmjSkus.size() == 1) {
            DmjSku dmjSku = dmjSkus.get(0);
            if (isFixedMeasures(userDeliverTemplate, dmjSku.getPropOther())) {
                throw new TradeException("商品规格不符合出货单固定尺寸配置！");
            }
            if (dmjSku.getSysSkuId() > 0 && StringUtils.isNotEmpty(dmjSku.getPropOther())) {
                result.put("table_prop_other_name", dmjSku.getPropOther());
                result.put("table_prop_other_num", result.get("table_item_num"));
            }
            result.put("table_prop_color_name", dmjSku.getPropColor());
            result.put("table_item_discount", orderList.get(0).getDiscountFee());
            result.put("table_item_discount_price", new BigDecimal(orderList.get(0).getPayment() == null ? "0" : orderList.get(0).getPayment()).divide(new BigDecimal(orderList.get(0).getNum()), 2, RoundingMode.HALF_UP).toString());
            result.put("trade_discount_fee", orderList.get(0).getDiscountFee());
            return;
        }
        Map<String, DmjSku> skuMap = new HashMap<>();
        for (DmjSku sku : dmjSkus) {
            skuMap.put(PrintSkuKeyUtils.getSkuKey(sku.getSysSkuId(), sku.getSysItemId()), sku);
        }
        List<StallDeliverModel> modelList = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            Order order = orderList.get(i);
            DmjSku dmjSku = skuMap.get(PrintSkuKeyUtils.getSkuKey(order.getSkuSysId(), order.getItemSysId()));
            if (dmjSku == null) continue;
            if (isFixedMeasures(userDeliverTemplate, dmjSku.getPropOther())) continue;
            StallDeliverModel model = new StallDeliverModel(dmjSku.getPropColor(), dmjSku.getPropOther(), dmjSku.getSysItemId(), i);
            model.setDiscountFee(order.getDiscountFee());
            model.setTotalFee(order.getTotalFee());
            model.setPayment(order.getPayment());
            model.setNum(order.getNum());
            model.setPrice(order.getPrice());
            model.setGoodsSectionNumStr(order.getGoodsSectionNumStr());
            model.setDiscountPrice(new BigDecimal(order.getPayment() == null ? "0" : order.getPayment()).divide(new BigDecimal(order.getNum()), 2, RoundingMode.HALF_UP).toString());
            //modelMap.put(order.getId(), model);
            modelList.add(model);
        }
        if (modelList.isEmpty()) {
            throw new TradeException("商品规格不符合出货单固定尺寸配置！");
        }
        Map<String, StallDeliverModel> mergeMap = new HashMap<>();
        for (StallDeliverModel model : modelList) {
            String key = model.getItemId() + "_" + nvl(model.getColor());
            StallDeliverModel mapModel = mergeMap.get(key);
            if (mapModel != null) {
                model.setDiscountFee(String.valueOf(add(model.getDiscountFee(), mapModel.getDiscountFee()).toString()));
                model.setTotalFee(String.valueOf(add(model.getTotalFee(), mapModel.getTotalFee()).toString()));
                model.setPayment(String.valueOf(add(model.getPayment(), mapModel.getPayment()).toString()));
                Map<String, Integer> otherPropNum = model.getOtherPropNum();
                otherPropNum.putAll(mapModel.getOtherPropNum());
                String other = StringUtils.isEmpty(model.getOther()) ? "无" : model.getOther();
                Integer otherNum = otherPropNum.get(other);
                if (otherNum == null) {
                    otherNum = 0;
                }
                otherNum += model.getNum();
                otherPropNum.put(other, otherNum);
                model.setNum(model.getNum() + mapModel.getNum());
                if (model.getGoodsSectionNumStr() != null) {
                    model.setGoodsSectionNumStr(mapModel.getGoodsSectionNumStr() + "/" + model.getGoodsSectionNumStr());
                } else {
                    model.setGoodsSectionNumStr(mapModel.getGoodsSectionNumStr());
                }
            } else {
                String other = StringUtils.isEmpty(model.getOther()) ? "无" : model.getOther();
                model.getOtherPropNum().put(other, model.getNum());
            }
            mergeMap.put(key, model);
        }
        List<StallDeliverModel> mergeList = new ArrayList<>(mergeMap.values());
        mergeList.sort((o1, o2) -> o1.getSort() > o2.getSort() ? 1 : -1);
        List<Integer> sortList = new ArrayList<>();
        for (StallDeliverModel model : mergeList) {
            sortList.add(model.getSort());
        }
        int itemCount = 0;
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String fieldName = entry.getKey();
            if ("trade_tag_name".equals(fieldName)) {
                continue;
            }
            String value = String.valueOf(entry.getValue());
            if (StringUtils.isBlank(value) || StringUtils.isBlank(value.replaceAll(",", ""))) {
                continue;
            }
            List<String> list = Arrays.asList(StringUtils.splitByWholeSeparatorPreserveAllTokens(value, ","));
            if (list.size() <= 1) {
                continue;
            }
            if (itemCount == 0) {
                itemCount = list.size();
            }
            StringBuilder fielValue = new StringBuilder();
            for (Integer integer : sortList) {
                if (list.size() <= integer) {
                    fielValue.append(",");
                    continue;
                }
                fielValue.append(nvl(list.get(integer))).append(",");
            }
            if (fielValue.length() > 0) {
                fielValue.deleteCharAt(fielValue.length() - 1);
            }
            result.put(fieldName, fielValue);
        }
        Set<String> allOtherName = new HashSet<>();
        List<String> colorName = new ArrayList<>();
        for (StallDeliverModel model : mergeList) {
            Map<String, Integer> otherPropNum = model.getOtherPropNum();
            allOtherName.addAll(otherPropNum.keySet());
            colorName.add(model.getColor());
        }
        StringBuilder numStr = new StringBuilder();
        StringBuilder table_item_discount = new StringBuilder();
        StringBuilder table_item_num = new StringBuilder();
        StringBuilder table_item_payment = new StringBuilder();
        StringBuilder table_item_totalFee = new StringBuilder();
        StringBuilder table_item_discount_price = new StringBuilder();
        List<String> otherPropNameList = new ArrayList<>(allOtherName);
        otherPropNameList.sort(new Comparator<String>() {
            final Map<String, Integer> map = new HashMap<>();

            private void loadMap() {
                List<String> sortField = Arrays.asList("", "均码", "F", "S", "M", "L", "XL", "XXL", "2XL", "XXXL", "3XL", "XXXXL", "4XL");
                for (int i = 0; i < sortField.size(); i++) {
                    map.put(sortField.get(i), i);
                }
            }

            @Override
            public int compare(String o1, String o2) {
                loadMap();
                if (o1 != null)
                    o1 = o1.toUpperCase();
                if (o2 != null)
                    o2 = o2.toUpperCase();
                int nullValue = 806461562;
                int x = map.get(o1) == null ? nullValue : map.get(o1);
                int y = map.get(o2) == null ? nullValue : map.get(o2);
                if (x == nullValue && y == nullValue) {
                    return Collator.getInstance(Locale.CHINA).compare(o1, o2);
                }
                return Integer.compare(x, y);
            }
        });
        for (StallDeliverModel model : mergeList) {
            table_item_discount.append(model.getDiscountFee()).append(",");
            table_item_num.append(model.getNum()).append(",");
            table_item_payment.append(nvl(model.getPayment())).append(",");
            table_item_totalFee.append(nvl(model.getTotalFee())).append(",");
            table_item_discount_price.append(nvl(model.getDiscountPrice())).append(",");
            Map<String, Integer> otherPropNum = model.getOtherPropNum();
            for (String other : otherPropNameList) {
                String key = "".equals(other) ? "无" : other;
                Integer numSingle = otherPropNum.get(key);
                numStr.append(numSingle == null ? "" : numSingle).append(";");
            }
            if (numStr.length() > 0)
                numStr.deleteCharAt(numStr.length() - 1).append(",");
        }
        result.put("table_prop_other_name", StringUtils.join(otherPropNameList, ","));
        result.put("table_prop_color_name", StringUtils.join(colorName, ","));
        result.put("table_prop_other_num", numStr.length() > 0 ? numStr.deleteCharAt(numStr.length() - 1).toString() : "");
        result.put("table_item_discount", table_item_discount.length() > 0 ? table_item_discount.deleteCharAt(table_item_discount.length() - 1).toString() : "");
        result.put("table_item_num", table_item_num.length() > 0 ? table_item_num.deleteCharAt(table_item_num.length() - 1).toString() : "");
        result.put("table_item_payment", table_item_payment.length() > 0 ? table_item_payment.deleteCharAt(table_item_payment.length() - 1).toString() : "");
        result.put("table_item_totalFee", table_item_totalFee.length() > 0 ? table_item_totalFee.deleteCharAt(table_item_totalFee.length() - 1).toString() : "");
        result.put("table_item_discount_price", table_item_discount_price.length() > 0 ? table_item_discount_price.deleteCharAt(table_item_discount_price.length() - 1).toString() : "");
    }

    //判断商品规格是否符合出货单固定尺寸配置，不符合不打印
    static Boolean isFixedMeasures(UserDeliverTemplate userDeliverTemplate, String propOther) {
        if (Long.valueOf(9L).equals(userDeliverTemplate.getSysTemplateId())) {
            List<String> sortField = Arrays.asList("均码", "F", "S", "M", "L", "XL", "XXL");
            List<FieldValue> fieldValueList = userDeliverTemplate.getFieldValues();
            if (CollectionUtils.isNotEmpty(fieldValueList)) {
                for (FieldValue f : fieldValueList) {
                    //判断出货单模版是否开启了固定尺码配置
                    if ("measures".equalsIgnoreCase(f.getName())) {
                        if ("fixed".equalsIgnoreCase(f.getValue())) {
                            return !sortField.contains(propOther.replaceAll(";", "").toUpperCase());
                        }
                    }
                }
            }
        }
        return false;
    }

    public static String nvl(String str) {
        return StringUtils.isEmpty(str) ? "" : str;
    }

    private static BigDecimal add(String str1, String str2) {
        return new BigDecimal(com.raycloud.dmj.tve.utils.StringUtils.isEmpty(str1) ? "0" : str1).add(new BigDecimal(com.raycloud.dmj.tve.utils.StringUtils.isEmpty(str2) ? "0" : str2));
    }

    /**
     * @param o         当前对象
     * @param fieldName 属性名
     * @return String
     * @description: 根据属性名获取当前对象的对应属性值
     * <AUTHOR>
     * @date 2018/8/28 上午9:06
     */
    private static String getResult(Object o, Object fieldName) {
        try {
            Class<?> aClass = o.getClass();
            Field declaredField = aClass.getDeclaredField(fieldName.toString());
            declaredField.setAccessible(true);
            PropertyDescriptor pd = new PropertyDescriptor(declaredField.getName(), aClass);
            Method readMethod = pd.getReadMethod();

            Object result = readMethod.invoke(o);
            return null == result ? "" : String.valueOf(result);
        } catch (NoSuchFieldException | IntrospectionException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * @param sortField 比较字段
     * @param o1        比较对象1
     * @param o2        比较对象2
     * @return Integer
     * @description: 辅助单个比较
     * <AUTHOR>
     * @date 2018/8/28 上午9:05
     */
    private static Integer compareOneHelper(String sortField, Object o1, Object o2) {

        if (StringUtils.isEmpty(sortField)) {
            return 0;
        }

        String sortData1 = getResult(o1, sortField);
        String sortData2 = getResult(o2, sortField);

        return Collator.getInstance(Locale.CHINA).compare(sortData1, sortData2);
    }

    /**
     * @param sortTypeStr 比较字段串(,隔开形式)
     * @param o1          比较对象1
     * @param o2          比较对象2
     * @return Integer
     * @description: 辅助比较
     * <AUTHOR>
     * @date 2018/8/28 上午9:05
     */
    public static Integer compareHelper(String sortTypeStr, Object o1, Object o2) {
        if (StringUtils.isEmpty(sortTypeStr)) {
            return 0;
        }

        //根据单个条件查询
        if (!sortTypeStr.contains(PtConfigConst.ENG_COMMA)) {
            return compareOneHelper(sortTypeStr, o1, o2);
        }

        //根据多个条件查询
        String[] sortFields = sortTypeStr.split(PtConfigConst.ENG_COMMA);
        for (String sortField : sortFields) {
            if (StringUtils.isEmpty(sortField)) {
                continue;
            }
            int result = compareOneHelper(sortField, o1, o2);
            if (result != 0) {
                return result > 0 ? 1 : -1;
            }
        }

        //未知字段
        return 0;
    }

    /**
     * @param results    返回结果
     * @param needValues 需要返回的字段
     * @param field      字段
     * @param fieldValue 字段值
     * @param fieldAlias 返回字段别名
     * @param flag       是否使用字段别名
     * @description: 处理返回字段值
     * <AUTHOR>
     * @date 2018/9/4 上午11:02
     */
    public static void handleFieldValue(Map<String, Object> results, List<String> needValues,
                                        String field, Object fieldValue, String fieldAlias, boolean flag) {

        if (null == results) {
            return;
        }

        if (CollectionUtils.isEmpty(needValues)) {
            return;
        }

        if (StringUtils.isEmpty(field)) {
            return;
        }

        if (needValues.contains(field)) {
            if (flag && StringUtils.isNotEmpty(fieldAlias)) {
                results.put(fieldAlias, fieldValue);
            } else {
                results.put(field, fieldValue);
            }
        }
    }

    private static final String PICKER_CODE = "table_section_num";
    private static final String PICKER_NUM = "table_section_picker_num";
    private static final String PICKER_CODE_PICKER_NUM = "table_section_and_picker_num";
    /**
     * 根据货位编码排序
     */
    private static final Integer LOCATION_CODE_SORT = 16;

    /**
     * 商品维度
     */
    private static final Integer PRODUCT_SHOW_0 = 0;
    /**
     * 货位维度
     */
    private static final Integer PRODUCT_SHOW_1 = 1;

    public static void prettifyTableSectionNum(UserWlbExpressTemplate userWlbExpressTemplate,
                                               Map<String, Object> result, WmsConfig wmsConfig) {
        String orderTypeStr = getFieldValueByKey(userWlbExpressTemplate, EnumFieldValueName.ORDER_TYPE.getValue());
        String productShowStr = getFieldValueByKey(userWlbExpressTemplate, EnumFieldValueName.PRODUCT_SHOW.getValue());
        Integer productShow = StringUtils.isEmpty(productShowStr) ? PRODUCT_SHOW_0 : Integer.parseInt(productShowStr);
        Integer orderType = StringUtils.isEmpty(orderTypeStr) ? PRODUCT_SHOW_0 : Integer.parseInt(orderTypeStr);
        Set<String> stringSet = result.keySet();
        if (PRODUCT_SHOW_1.equals(productShow)) {
            Map<String, Object> currentResult = new HashMap<>();
            for (String key : stringSet) {
                if (key.contains(PICKER_CODE)) {
                    String[] key_split = key.split("-");
                    List<Map<String, String>> combinationList = getMapList(key_split, result);
                    List<Map<String, String>> combinationNewList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(combinationList)) {
                        for (Map<String, String> map : combinationList) {
                            String pickerCodeV = map.get(PICKER_CODE);
                            if ("无".equals(pickerCodeV)) {
                                Map<String, String> entity = new HashMap<>();
                                entity.putAll(map);
                                entity.put(PICKER_CODE, "");
                                entity.put(PICKER_NUM, "");
                                combinationNewList.add(entity);
                            } else {
                                String[] pickerCodeV_split = pickerCodeV.split(";");
                                if (pickerCodeV_split.length > 0) {
                                    for (String s : pickerCodeV_split) {
                                        if (null != s && !"".equals(s)) {
                                            Map<String, String> entity = new HashMap<>();
                                            entity.putAll(map);
                                            String[] codeV_split = s.split("\\*");
                                            entity.put(PICKER_CODE, codeV_split[0]);
                                            entity.put(PICKER_NUM, codeV_split[1]);
                                            combinationNewList.add(entity);
                                        }
                                    }
                                }
                            }
                        }
                    }
//                    if (LOCATION_CODE_SORT.equals(orderType) && wmsConfig != null) {
//                        listSort(combinationNewList, wmsConfig);
//                    }
                    connectJoinResult(key, combinationNewList, currentResult);
                    connectFiledValueResult(key, combinationNewList, currentResult);

                }
            }

            result.putAll(currentResult);

        } else {
            Map<String, Object> currentResult = new HashMap<>();
            for (String key : stringSet) {
                if (key.contains(PICKER_CODE)) {
                    String[] key_split = key.split("-");
                    List<Map<String, String>> combinationList = getMapList(key_split, result);
                    List<Map<String, String>> combinationNewList = new ArrayList<>();
                    for (Map<String, String> map : combinationList) {
                        String pickerCodeV = map.get(PICKER_CODE);
                        if ("无".equals(pickerCodeV)) {
                            map.put(PICKER_CODE, "");
                            map.put(PICKER_NUM, "");
                        } else {
                            String[] pickerCodeV_split = pickerCodeV.split(";");
                            if (pickerCodeV_split.length > 0) {
                                StringBuilder code = new StringBuilder();
                                StringBuilder num = new StringBuilder();
                                for (String aPickerCodeV_split : pickerCodeV_split) {
                                    if (null != aPickerCodeV_split && !"".equals(aPickerCodeV_split)) {
                                        String[] codeV_split = aPickerCodeV_split.split("\\*");
                                        if (codeV_split.length != 2) {
                                            Logs.debug(JSONObject.toJSON(aPickerCodeV_split) + "--------------------" + JSONObject.toJSONString(codeV_split));
                                        }
                                        if (code.length() > 0) {
                                            code.append(";").append(codeV_split[0]);
                                        } else {
                                            code.append(codeV_split[0]);
                                        }

                                        if (num.length() > 0) {
                                            num.append(";").append(codeV_split[1]);
                                        } else {
                                            num.append(codeV_split[1]);
                                        }
                                    }
                                }

                                map.put(PICKER_CODE, code.toString());
                                map.put(PICKER_NUM, num.toString());

                            }
                            map.put(PICKER_CODE_PICKER_NUM, pickerCodeV);
                        }

                        combinationNewList.add(map);
                    }
//                    if (LOCATION_CODE_SORT.equals(orderType)) {
//                        listSort(combinationNewList, wmsConfig);
//                    }
                    connectJoinResult(key, combinationNewList, currentResult);
                    connectFiledValueResult(key, combinationNewList, currentResult);
                }
            }
            result.putAll(currentResult);
        }

    }

    private static List<Map<String, String>> getMapList(String[] key_split, Map<String, Object> result) {
        List<Map<String, String>> combinationList = new ArrayList<>();
        Object valueLengthO = result.get(PICKER_CODE);
        if (null == valueLengthO || StringUtils.isBlank((String) valueLengthO)) {
            return Collections.emptyList();
        }
        int valueLength = (((String) valueLengthO).split(",")).length;
        for (int i = 0; i < valueLength; i++) {
            Map<String, String> entity = new HashMap<>();
            for (String s : key_split) {
                String strV = String.valueOf(result.get(s));

//                //防止以","结尾但是split分割不出
//                if (strV.endsWith(",")) {
//                    strV = strV + " ";
//                }
                String[] v = strV.split(",", -1);
                if (v.length == 0) {
                    entity.put(s, " ");
                } else if (v.length == 1) {
                    entity.put(s, v[0]);
                } else {
                    try {
                        entity.put(s, v[i]);
                    } catch (ArrayIndexOutOfBoundsException e) {
                        entity.put(s, " ");
                        logger.warn("[" + s + "]该key的值得数量与其他key的值不一致，没有的值以[' ']表示");
                    }
                }

            }
            combinationList.add(entity);
        }

        return combinationList;
    }

    private static void connectJoinResult(String key, List<Map<String, String>> list, Map<String, Object> result) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String[] key_split = key.split("-");
        StrBuilder sb = new StrBuilder();
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            for (int j = 0; j < key_split.length; j++) {
                String value = map.get(key_split[j]);
                sb.append(value);
                // 最后一个值不加空格，换行符后面不加空格
                if (j < key_split.length - 1 && !Objects.equals(value, "\n")) {
                    sb.append(" ");
                }
            }
            // 每条数据项直接用,隔开，最后一个不加,
            if (i != list.size() - 1) {
                sb.append(",");
            }
        }
        result.put(key, sb.toString());
    }

    private static void connectFiledValueResult(String key, List<Map<String, String>> list, Map<String, Object> result) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(key)) {
            return;
        }

        StrBuilder pickerNUmStr = new StrBuilder();
        for (Map<String, String> map : list) {
            if (pickerNUmStr.length() == 0) {
                pickerNUmStr.append(map.get(PICKER_NUM));
            } else {
                pickerNUmStr.append(",").append(map.get(PICKER_NUM));
            }
        }
        result.put(PICKER_NUM, pickerNUmStr.toString());

        String[] key_split = key.split("-");
        for (String s : key_split) {
            StrBuilder filedValue = new StrBuilder();
            for (Map<String, String> map : list) {
                filedValue.append(map.get(s)).append(",");

            }

            if (filedValue.length() > 0) {
                filedValue = filedValue.deleteCharAt(filedValue.length() - 1);
                result.put(s, filedValue.toString());
            }
        }

    }

    private static String getSmallCode(Map<String, String> map) {
        String codeNum = map.get(PICKER_CODE_PICKER_NUM);
        String code = map.get(PICKER_CODE);
        if (StringUtils.isNotEmpty(codeNum)) {
            String[] v_split = codeNum.trim().split(";");
            if (v_split.length < 2) {
                return code;
            }

            List<Map<String, String>> pickerCodeAndNumList = new ArrayList<>();
            for (String v : v_split) {
                Map<String, String> itemMap = new HashMap<>();
                String[] vv = v.split("\\*");
                itemMap.put(PICKER_CODE, vv[0]);
                itemMap.put(PICKER_NUM, vv[1]);
                pickerCodeAndNumList.add(itemMap);
            }
            pickerCodeAndNumList.sort(Comparator.comparing(o -> o.get(PICKER_CODE)));

            if (CollectionUtils.isNotEmpty(pickerCodeAndNumList) && pickerCodeAndNumList.size() > 1) {
                StringBuilder pickerCodeStr = new StringBuilder();
                StringBuilder pickerNumStr = new StringBuilder();
                for (Map<String, String> stringMap : pickerCodeAndNumList) {
                    if (pickerCodeStr.length() == 0 && pickerNumStr.length() == 0) {
                        pickerCodeStr.append(stringMap.get(PICKER_CODE));
                        pickerNumStr.append(stringMap.get(PICKER_NUM));
                    } else {
                        pickerCodeStr.append(";").append(stringMap.get(PICKER_CODE));
                        pickerNumStr.append(";").append(stringMap.get(PICKER_NUM));
                    }
                }
                map.put(PICKER_CODE, pickerCodeStr.toString());
                map.put(PICKER_NUM, pickerNumStr.toString());
            }
            return pickerCodeAndNumList.get(0).get(PICKER_CODE);
        } else {
            return code;
        }
    }

    public static Map<Long, Map<String, AssoWaveItem>> getAssoWaveItemMap(Staff staff, List<Long> waveIds, ITradeWaveService tradeWaveService) {
        List<AssoWaveItem> assoWaveItems = tradeWaveService.queryAssoWaveItemsByWaveIds(staff, new ArrayList<>(waveIds));
        Multimap<Long, AssoWaveItem> assoWaveItemMultimap = ArrayListMultimap.create();
        Map<Long, Map<String, AssoWaveItem>> assoWaveItemMap = new HashMap<>();
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(assoWaveItems)) {
            for (AssoWaveItem assoWaveItem : assoWaveItems) {
                assoWaveItemMultimap.put(assoWaveItem.getWaveId(), assoWaveItem);
            }
            Map<Long, Collection<AssoWaveItem>> map0 = assoWaveItemMultimap.asMap();
            for (Map.Entry<Long, Collection<AssoWaveItem>> entry : map0.entrySet()) {
                Long waveId = entry.getKey();
                Map<String, AssoWaveItem> assoWaveItemSubMap = new HashMap<>();
                for (AssoWaveItem assoWaveItem : entry.getValue()) {
                    assoWaveItemSubMap.put(getItemKey(assoWaveItem.getItemSysId(), assoWaveItem.getSkuSysId()), assoWaveItem);
                }
                assoWaveItemMap.put(waveId, assoWaveItemSubMap);
            }
        }
        return assoWaveItemMap;
    }

    public static AssoWaveItem getAssoWaveItem(Map<Long, Map<String, AssoWaveItem>> assoWaveItemMap, Long waveId, Long sysItemId, Long sysSkuId) {
        Map<String, AssoWaveItem> assoWaveItemSubMap = assoWaveItemMap.get(waveId);
        if (null == assoWaveItemSubMap) {
            return null;
        }
        return assoWaveItemSubMap.get(getItemKey(sysItemId, sysSkuId));
    }

    private static String getItemKey(Long sysItemId, Long sysSkuId) {
        return sysItemId + "_" + NumberUtils.negative2Zero(sysSkuId);
    }

    /**
     * 替换阿里加密面单中发件人信息
     */
    public static void __repliceWithAliSecrecyAddress(Trade singleTrade, Map<String, Object> result, Map<Long, Address> secrecyAddressMap) {
        if (secrecyAddressMap == null || secrecyAddressMap.get(singleTrade.getSid()) == null) {
            return;
        }
        Address address = secrecyAddressMap.get(singleTrade.getSid());
        String name = address.getContactName();
        String phone = address.getPhone();
        if (StringUtils.isNotBlank(name)) {
            result.put("send_name", name);
            result.put("send_name_up", name);
            result.put("send_name_down", name);
        }
        if (StringUtils.isNotBlank(phone)) {
            result.put("send_mobile", phone);
            result.put("send_mobile_up", phone);
            result.put("send_mobile_down", phone);
            result.put("send_phone", phone);
            result.put("send_phone_up", phone);
            result.put("send_phone_down", phone);
        }
    }

    public static void __ifNeedItemMsg(Trade singleTrade, Map<Long, DmjItem> idToDmjItem, Map<Long, DmjSku> idToDmjSku, Map<String, Object> result) {
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder itemOuterIds = new StringBuilder();
        StringBuilder itemBarcode = new StringBuilder();
        StringBuilder skuOuterIds = new StringBuilder();
        StringBuilder brandSb = new StringBuilder();//商品品牌
        StringBuilder standardSb = new StringBuilder();//执行标准
        StringBuilder skuBarcode = new StringBuilder();
        for (Order order : orders) {
            Long itemSysId = order.getItemSysId();
            DmjItem item = idToDmjItem.get(itemSysId);
            if (item != null) {
                String skuOuterId;
                String brand;
                String standard;
                DmjSku sku = order.getSkuSysId() != null && order.getSkuSysId() > 0 ? idToDmjSku.get(order.getSkuSysId()) : null;
                if (sku != null) {
                    skuOuterId = sku.getSkuOuterId();
                    brand = sku.getBrand();
                    standard = StringUtils.isBlank(sku.getSkuStandard()) ? item.getStandard() : sku.getSkuStandard();
                    skuBarcode.append(StringUtils.isEmpty(sku.getBarcode()) ? item.getBarcode() : sku.getBarcode()).append(",");
                } else {
                    //若对应商品是纯商品或不含sku套件，则取对应商品的商品简称；若未维护商品简称，则取商品名称。
                    skuOuterId = "";
                    brand = item.getBrand();
                    standard = item.getStandard();
                    skuBarcode.append(item.getBarcode()).append(",");
                }
                itemBarcode.append(item.getBarcode()).append(",");
                itemOuterIds.append(item.getOuterId()).append(",");
                skuOuterIds.append(skuOuterId).append(",");
                brandSb.append(brand).append(",");
                standardSb.append(standard).append(",");
            } else {
                itemOuterIds.append(",");
                skuOuterIds.append(",");
                brandSb.append(",");
                standardSb.append(",");
                itemBarcode.append(",");
                skuBarcode.append(",");
            }
        }
        if (itemOuterIds.length() > 0) {
            itemOuterIds.deleteCharAt(itemOuterIds.length() - 1);
        }
        if (skuOuterIds.length() > 0) {
            skuOuterIds.deleteCharAt(skuOuterIds.length() - 1);
        }
        if (brandSb.length() > 0) {
            brandSb.deleteCharAt(brandSb.length() - 1);
        }
        if (standardSb.length() > 0) {
            standardSb.deleteCharAt(standardSb.length() - 1);
        }
        if (itemBarcode.length() > 0) {
            itemBarcode.deleteCharAt(itemBarcode.length() - 1);
        }
        if (skuBarcode.length() > 0) {
            skuBarcode.deleteCharAt(skuBarcode.length() - 1);
        }
        result.put("table_outer_id_of_item_barCode", itemOuterIds.toString());
        result.put("table_outer_id_of_item_QRCode", itemOuterIds.toString());
        result.put("table_item_outer_id_barCode", skuOuterIds.toString());
        result.put("table_item_outer_id_QRCode", skuOuterIds.toString());
        result.put("table_item_brand", brandSb.toString());
        result.put("table_item_executive_standard", standardSb.toString());

        result.put("table_item_barcode", itemBarcode.toString());
        result.put("table_sku_barcode", skuBarcode.toString());
    }

    public static void buildOrderCustomMessage(List<String> needValues, Map<String, Object> result, Trade singleTrade) {
        if (needValues.contains("item_custom_message")) {
            boolean needJoin = false;
            List<String> values = new ArrayList<>();
            for (Order order : TradeUtils.getOrders4Trade(singleTrade)) {
                OrderExt orderExt = order.getOrderExt();
                //定制化信息是否存在
                boolean haveCustomization = false;
                if (orderExt != null) {
                    needJoin = true;
                    String customization = orderExt.getCustomization();
                    if (StringUtils.isNotEmpty(customization)) {
                        haveCustomization = true;
                        try {
                            JSONObject jsonObject = JSONObject.parseObject(customization);
                            JSONArray textObj = jsonObject.getJSONArray("text");
                            if (CollectionUtils.isEmpty(textObj)) {
                                continue;
                            }
                            List<String> results = new ArrayList<>();
                            for (String s : "designNo,mainTextValue,subTextValue".split(",")) {
                                for (Object text : textObj) {
                                    JSONObject jo = (JSONObject) text;
                                    if (s.equals(jo.getString("key"))) {
                                        String content = jo.getString("content");
                                        if (org.apache.commons.lang.StringUtils.isNotEmpty(content)) {
                                            results.add(content);
                                        }
                                    }
                                }
                            }
                            if (!org.springframework.util.CollectionUtils.isEmpty(results)) {
                                values.add(String.join("；", results).replace(",", "%2C"));
                            }
                        } catch (Exception e) {
                            Logs.error("处理自定义字段出错" + e.getMessage(), e);
                        }
                    }
                }
                if (!haveCustomization) {
                    //如果定制信息为空也要添加到集合中，不然组合字段判断长度不一样会不显示
                    values.add(" ");
                }
            }
            if (needJoin) {
                result.put("item_custom_message", String.join(",", values));
            }
        }
    }


    public static void buildItemMaterial(List<String> needValues, Trade singleTrade, Map<String, Object> result, Map<String, DmjSku> skuMap, Map<String, DmjItem> itemMap) {

        if (needValues.contains("item_order_component") || needValues.contains("item_order_material")) {
            if (singleTrade == null) {
                return;
            }
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            StringJoiner component = new StringJoiner(",");
            StringJoiner material = new StringJoiner(",");
            for (Order order : orderList) {
                DmjSku dmjSku = skuMap.get(String.valueOf(order.getSkuSysId()));
                if (dmjSku != null) {
                    component.add(StringUtils.isNotEmpty(dmjSku.getSkuComponent()) ? dmjSku.getSkuComponent() : "");
                    material.add(StringUtils.isNotEmpty(dmjSku.getMaterialQuality()) ? dmjSku.getMaterialQuality() : "");
                } else {
                    DmjItem dmjItem = itemMap.get(String.valueOf(order.getItemSysId()));
                    if (dmjItem != null) {
                        component.add(StringUtils.isNotEmpty(dmjItem.getComponent()) ? dmjItem.getComponent() : "");
                        material.add(StringUtils.isNotEmpty(dmjItem.getMaterialQuality()) ? dmjItem.getMaterialQuality() : "");
                    }
                }
            }
            result.put("item_order_component", component.toString());
            result.put("item_order_material", material.toString());
        }

    }

    public static PrintTradeLog createPrintTradeLogInit(Staff staff, Long[] sids, List<Trade> trades, String printName, String waveIds, Integer type) {
        PrintTradeLog log = new PrintTradeLog();
        //记录打印记录的时候，打印机名称是限制64个字符超长的时候落库的需要截取
        if (printName != null && printName.length() > 64) {
            printName = printName.substring(0, 64);
        }
        if (waveIds != null && waveIds.length() > 240) {
            waveIds = waveIds.substring(0, 240);
        }
        log.setPrinter(printName);
        log.setPrintTime(new Date());
        log.setStaffId(staff.getId());
        log.setStaffName(staff.getName());
        log.setCompanyId(staff.getCompanyId());
        log.setDeliverPrinted(0);
        log.setLeftDeliverPrinted(sids.length);
        Trade t = trades.get(0);
        log.setIsWlb(t.getTemplateType());
        log.setTemplateId(t.getTemplateId());
        log.setType(Objects.nonNull(type) ? type : EnumPrintTradeType.EXPRESS_TEMPLATE.getValue());
        log.setWarehouseId(t.getWarehouseId());
        log.setWaveIds(waveIds);
        log.setLogisticsCompanyId(t.getLogisticsCompanyId());
        Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(trades);
        // 创建详情
        for (int i = 1; i <= sids.length; i++) {
            Long sid = sids[i - 1];
            if (!sidTradeMap.containsKey(sid)) {
                continue;
            }
            Trade trade = sidTradeMap.get(sid);
            PrintTradeLogDetail detail = new PrintTradeLogDetail();
            detail.setSeq(i);
            detail.setDeliverPrinted(0);
            detail.setOutSid(trade.getOutSid());
            detail.setSid(sid);
            detail.setTemplateId(trade.getTemplateId());
            detail.setTid(trade.getTid());
            detail.setShortId(trade.getShortId());
            detail.setPrintStatus(PrintStatusEnum.NOT_PRINTED.getType());
            detail.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            log.getDetails().add(detail);
        }
        return log;
    }

    /**
     * 获取商品货位编码
     */
    public static Map<String, AssoGoodsSectionSku> getItemGoodsSection(Staff staff, List<Trade> trades, IWmsService wmsService) {
        AssoGoodsSectionSkuParams skuParams = new AssoGoodsSectionSkuParams();
        skuParams.setWarehouseIds(trades.stream().map(Trade::getWarehouseId).distinct().collect(Collectors.toList()));
        List<Order> allOrders = TradeUtils.getOrders4Trade(trades);

        List<Long> itemSysIds = allOrders.stream().map(Order::getItemSysId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> itemSkuIds = allOrders.stream().map(Order::getSkuSysId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> updatedItemSkuIds = itemSkuIds.stream()
                .map(id -> id == -1L ? 0L : id)
                .collect(Collectors.toList());
        skuParams.setSysSkuIds(updatedItemSkuIds);
        skuParams.setSysItemIds(itemSysIds);
        /*
         * 查询商品货位
         */
        Map<String, AssoGoodsSectionSku> sectionSkuMap = new HashMap<>();
        if (CollectionUtils.isEmpty(itemSkuIds) && CollectionUtils.isEmpty(updatedItemSkuIds)) {
            return sectionSkuMap;
        }
        List<AssoGoodsSectionSku> skuList = wmsService.queryAssoGoodsSectionSkuList(staff, skuParams);
        if (!CollectionUtils.isEmpty(skuList)) {
            for (AssoGoodsSectionSku sku : skuList) {
                String key = sku.getSysItemId() + "_" + sku.getSysSkuId() + "_" + sku.getWarehouseId();
                sectionSkuMap.putIfAbsent(key, sku);
                if (sku.getSysSkuId() == 0L) {
                    key = sku.getSysItemId() + "_" + "-1" + "_" + sku.getWarehouseId();
                    sectionSkuMap.putIfAbsent(key, sku);
                }
            }
        }
        return sectionSkuMap;
    }

    /**
     * @param singleTrade
     * @param uniqueCodeMap
     * @param result
     * @return void
     * @description 组装拼接唯一码
     * <AUTHOR>
     * @date 2024-05-29 17:10
     */
    public static void ifNeedUniqueCode(Trade singleTrade, Map<String, List<WaveUniqueCode>> uniqueCodeMap, Map<String, Object> result) {
        if (CollUtil.isEmpty(uniqueCodeMap)) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder uniqueCode = new StringBuilder();
        for (Order order : orders) {
            String orderUniqueCode = null;
            List<WaveUniqueCode> uniqueCodes = uniqueCodeMap.get(order.getSid() + "_" + order.getId());
            if (CollUtil.isNotEmpty(uniqueCodes)) {
                orderUniqueCode = uniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.joining("&#44"));
            }
            uniqueCode.append(orderUniqueCode).append(",");
        }
        if (uniqueCode.length() > 0) {
            uniqueCode.deleteCharAt(uniqueCode.length() - 1);
        }
        result.put("table_unique_code", uniqueCode.toString());
    }

    public static void ifPlatformSkuId(List<String> needValues, Trade mergeTrade, Map<String, Object> result) {
        if (needValues.contains("table_platform_item_id") || needValues.contains("table_platform_sku_id")) {
            StringJoiner itemId = new StringJoiner(",");
            StringJoiner skuId = new StringJoiner(",");
            List<Order> orders = TradeUtils.getOrders4Trade(mergeTrade);
            for (Order order : orders) {
                itemId.add(order.getNumIid() == null ? "" : order.getNumIid());
                skuId.add(order.getSkuId() == null ? "" : order.getSkuId());
            }
            result.put("table_platform_item_id", itemId.toString());
            result.put("table_platform_sku_id", skuId.toString());
        }
    }

    /**
     * @param needValues
     * @param mergeTrade
     * @param result
     * @return void
     * @description 库存占用数/商品总数
     * <AUTHOR>
     * @date 2024-06-20 15:16
     */
    public static void ifNeedTableStockNum(List<String> needValues, Trade mergeTrade, Map<String, Object> result) {
        if (!needValues.contains("table_stock_num_and_num")) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(mergeTrade);
        StringBuilder stockNumAndNum = new StringBuilder();
        for (Order order : orders) {
            Integer stockNum = order.getStockNum() != null ? order.getStockNum() : 0;
            Integer num = order.getNum() != null ? order.getNum() : 0;
            stockNumAndNum.append(String.format("%s/%s", stockNum, num)).append(",");
        }
        if (stockNumAndNum.length() > 0) {
            stockNumAndNum.deleteCharAt(stockNumAndNum.length() - 1);
        }
        result.put("table_stock_num_and_num", stockNumAndNum.toString());
    }

    public static class HandleItemPositionCodeParams {
        private Staff staff;

        private List<String> needValues;

        private Map<String, Object> result;

        private Trade trade;

        private ITradeWaveService tradeWaveService;

        private Wave wave;

        public static class Builder {
            private Staff staff;

            private List<String> needValues;

            private Map<String, Object> result;

            private Trade trade;

            private ITradeWaveService tradeWaveService;

            private Wave wave;

            public HandleItemPositionCodeParams build() {
                HandleItemPositionCodeParams params = new HandleItemPositionCodeParams();
                params.staff = staff;
                params.needValues = needValues;
                params.result = result;
                params.trade = trade;
                params.tradeWaveService = tradeWaveService;
                params.wave = wave;
                return params;
            }

            public Builder() {
            }

            public Builder staff(Staff staff) {
                this.staff = staff;
                return this;
            }

            public Builder needValues(List<String> needValues) {
                this.needValues = needValues;
                return this;
            }

            public Builder result(Map<String, Object> result) {
                this.result = result;
                return this;
            }

            public Builder trade(Trade trade) {
                this.trade = trade;
                return this;
            }

            public Builder tradeWaveService(ITradeWaveService tradeWaveService) {
                this.tradeWaveService = tradeWaveService;
                return this;
            }

            public Builder wave(Wave wave) {
                this.wave = wave;
                return this;
            }
        }

        public Staff getStaff() {
            return staff;
        }

        public List<String> getNeedValues() {
            return needValues;
        }

        public Map<String, Object> getResult() {
            return result;
        }

        public Trade getTrade() {
            return trade;
        }

        public ITradeWaveService getTradeWaveService() {
            return tradeWaveService;
        }

        public Wave getWave() {
            return wave;
        }
    }


    public static void initYDValue(UserWlbExpressTemplate userWlbExpressTemplate, Map<String, Object> result) {
        if (userWlbExpressTemplate.getYdWlbConfig() == null) {
            return;
        }

        result.put("userInfo", userWlbExpressTemplate.getYdWlbConfig().getUserInfo());
        List<YDWLBConfigParam.Area> areas = userWlbExpressTemplate.getYdWlbConfig().getAreas();
        Map<String, Map<String, Object>> returnMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(areas)) {
            for (YDWLBConfigParam.Area area : areas) {
                Map<String, Object> areaMap = new HashMap<>();

                if (area.isTrade_seller_memo()) {
                    areaMap.put("trade_seller_memo", result.get("trade_seller_memo"));
                }
                if (area.isBuyer_nick()) {
                    areaMap.put("buyer_nick", result.get("buyer_nick"));
                }
                if (area.isTrade_buyer_message()) {
                    areaMap.put("trade_buyer_message", result.get("trade_buyer_message"));
                }

                if (area.isTrade_buyer_message()) {
                    areaMap.put("custom_content", area.getCustom_content());
                }

                returnMap.put(area.getArea_name(), areaMap);

            }
        }

        result.put("areas", returnMap);
    }

    /**
     * 获取商品所在的订单*
     * <p>
     * skuId+itemId:sid
     */
    public static Map<String, List<Long>> getTradeItemMap(FieldValuesInvoicesRequest request, List<Trade> tradeList, Integer suitType, Map<Long, List<Order>> orderMap, Map<Long, Integer> orderStockMap) {
        PrintMerchantCodeDataParam param = (PrintMerchantCodeDataParam) request.getParam();
        Map<String, List<Long>> tItemSumMap = new HashMap<>();
        List<Order> orders = new ArrayList<>();
        for (List<Order> orderList : orderMap.values()) {
            for (Order order : orderList) {
                orders.addAll((!new Integer(1).equals(suitType) && order.isSuit() && order.getSuits() != null) ? order.getSuits() : Collections.singletonList(order));
            }
        }
        orders = orders.stream().sorted(Comparator.comparing(Order::getPayTime)).collect(Collectors.toList());
        for (Order o : orders) {
            Integer num = o.getNum() == null ? 0 : o.getNum();
            if ("caigou".equals(request.getSource())) {
                //采购模块统计库存不足的订单
                o.setDiffStockNum(orderStockMap.get(o.getId()));
                if (o.getDiffStockNum() == null || o.getDiffStockNum() < 1) continue;
                num = o.getDiffStockNum();
            }
            String key = (o.getSkuSysId() > 0L ? o.getSkuSysId() : 0L) + "_" + o.getItemSysId();
            for (int i = 0; i < num; i++) {
                if (tItemSumMap.containsKey(key)) {
                    tItemSumMap.get(key).add(o.getMergeSid() != null ? o.getMergeSid() : o.getSid());
                } else {
                    tItemSumMap.put(key, Lists.newArrayList(o.getMergeSid() != null ? o.getMergeSid() : o.getSid()));
                }
            }
        }
        return tItemSumMap;
    }

    /**
     * 获取商品所在的售后工单
     */
    public static Map<String, List<Long>> getItemAfterSaleMap(Map<Long, WorkOrder> afterSaleMap) {
        Map<String, List<Long>> itemAfterSaleMap = new HashMap<>();
        for (Map.Entry<Long, WorkOrder> workOrderMap : afterSaleMap.entrySet()) {
            WorkOrder workOrder = workOrderMap.getValue();
            if (workOrder.getItemSnapshots() == null || workOrder.getItemSnapshots().size() == 0)
                continue;
            for (ItemSnapshot itemSnapshot : workOrder.getItemSnapshots()) {
                Integer goodNum = itemSnapshot.getGoodItemCount();
                Integer badNum = itemSnapshot.getBadItemCount();
                int sum = (goodNum == null ? 0 : goodNum) + (badNum == null ? 0 : badNum);
                String key = itemSnapshot.getSysItemId() + "_" + (itemSnapshot.getSysSkuId() > 0L ? itemSnapshot.getSysSkuId() : 0L);
                for (int i = 0; i < sum; i++) {
                    if (itemAfterSaleMap.containsKey(key)) {
                        itemAfterSaleMap.get(key).add(workOrder.getId());
                    } else {
                        itemAfterSaleMap.put(key, Lists.newArrayList(workOrder.getId()));
                    }
                }
            }
        }
        return itemAfterSaleMap;
    }

    /**
     * 供应商数据脱敏
     */
    public static void supplierPermissionProcess(Staff staff, List<Supplier> suppliers, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(suppliers)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (null == supplierIds) {
            supplierIds = new ArrayList<>();
        }
        for (Supplier supplier : suppliers) {
            if (!supplierIds.contains(supplier.getId())) {
                supplier.setName("***");
                supplier.setCity("***");
                supplier.setDistrict("***");
                supplier.setProvince("***");
                supplier.setAddress("***");
                supplier.setMobile("***");
                supplier.setPhone("***");
                supplier.setCode("***");
            }
        }
    }

    /**
     * 采购单数据脱敏
     */
    public static void dataPermissionProcess(Staff staff, List<PurchaseOrder> purchaseOrders, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (null == supplierIds) {
            supplierIds = new ArrayList<>();
        }
        for (PurchaseOrder purchaseOrder : purchaseOrders) {
            if (null != purchaseOrder.getSupplierId() && purchaseOrder.getSupplierId() > 0 && !supplierIds.contains(purchaseOrder.getSupplierId())) {
//                purchaseOrder.setSupplierName("***");
                purchaseOrder.setSupplierCode("***");
            }
            List<PurchaseOrderDetail> details = purchaseOrder.getDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                for (PurchaseOrderDetail detail : details) {
                    if (null != detail.getSupplierId() && detail.getSupplierId() > 0 && !supplierIds.contains(detail.getSupplierId())) {
                        detail.setSupplierItemOuterId("***");
//                        detail.setSupplierName("***");
                        detail.setSupplierCode("***");
                    }
                }
            }
        }
    }

    /**
     * 商品档案数据脱敏
     */
    public static void goodsPermissionProcess(Staff staff, List<DmjSku> dmjSkus, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(dmjSkus)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (CollectionUtils.isEmpty(supplierIds)) {
            supplierIds = new ArrayList<>();
        }
        for (DmjSku dmjSku : dmjSkus) {
            List<ItemSupplierBridge> itemSupplierBridgeList = dmjSku.getItemSupplierBridgeList();
            if (CollectionUtils.isNotEmpty(itemSupplierBridgeList)) {
                for (ItemSupplierBridge bridge : itemSupplierBridgeList) {
                    if (null != bridge.getSupplierId() && bridge.getSupplierId() > 0 && !supplierIds.contains(bridge.getSupplierId())) {
                        bridge.setSupplierName("***");
                        bridge.setSupplierCode("***");
                        bridge.setSupplierItemOuterId("***");
                    }
                }

            }
        }
    }

    /**
     * 商品档案数据脱敏
     */
    public static void itemBridgePermissionProcess(Staff staff, List<ItemSupplierBridge> itemSupplierBridges, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(itemSupplierBridges)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (CollectionUtils.isEmpty(supplierIds)) {
            supplierIds = new ArrayList<>();
        }
        for (ItemSupplierBridge bridge : itemSupplierBridges) {
            if (null != bridge.getSupplierId() && bridge.getSupplierId() > 0 && !supplierIds.contains(bridge.getSupplierId())) {
                bridge.setSupplierName("***");
                bridge.setSupplierCode("***");
                bridge.setSupplierItemOuterId("***");
            }
        }
    }

    /**
     * 收货单数据脱敏
     */
    public static void acceptPermissionProcess(Staff staff, List<WarehouseEntry> warehouseEntries, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(warehouseEntries)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (CollectionUtils.isEmpty(supplierIds)) {
            supplierIds = new ArrayList<>();
        }
        for (WarehouseEntry warehouseEntry : warehouseEntries) {
            if (null != warehouseEntry.getSupplierId() && warehouseEntry.getSupplierId() > 0 && !supplierIds.contains(warehouseEntry.getSupplierId())) {
                warehouseEntry.setSupplierName("***");
            }
            List<WarehouseEntryDetail> details = warehouseEntry.getDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                for (WarehouseEntryDetail detail : details) {
                    if (null != detail.getSupplierId() && detail.getSupplierId() > 0 && !supplierIds.contains(detail.getSupplierId())) {
                        detail.setSupplierName("***");
                    }
                }
            }
        }
    }

    /**
     * 采购退货单数据脱敏
     */
    public static void returnPermissionProcess(Staff staff, List<PurchaseReturn> purchaseReturns, IIndexDubboService indexDubboService) {
        if (CollectionUtils.isEmpty(purchaseReturns)) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        if (CollectionUtils.isEmpty(supplierIds)) {
            supplierIds = new ArrayList<>();
        }
        for (PurchaseReturn purchaseReturn : purchaseReturns) {
            if (null != purchaseReturn.getSupplierId() && purchaseReturn.getSupplierId() > 0 && !supplierIds.contains(purchaseReturn.getSupplierId())) {
                purchaseReturn.setSupplierName("***");
                purchaseReturn.setProvince("***");
                purchaseReturn.setCity("***");
                purchaseReturn.setDistrict("***");
                purchaseReturn.setAddress("***");
                purchaseReturn.setReceiver("***");
                purchaseReturn.setPhone("***");
            }
            List<PurchaseReturnDetail> details = purchaseReturn.getDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                for (PurchaseReturnDetail detail : details) {
                    if (null != detail.getSupplierId() && detail.getSupplierId() > 0 && !supplierIds.contains(detail.getSupplierId())) {
                        detail.setSupplierItemOuterId("***");
                        detail.setSupplierName("***");
                        detail.setSupplierCode("***");
                    }
                }
            }
            List<PurchaseReturnMerge<PurchaseReturnDetail>> merges = purchaseReturn.getMerges();
            if (CollectionUtils.isNotEmpty(merges)) {
                for (PurchaseReturnMerge<PurchaseReturnDetail> merge : merges) {
                    if (CollectionUtils.isNotEmpty(merge.getDetails())) {
                        for (PurchaseReturnDetail detail : merge.getDetails()) {
                            if (null != detail.getSupplierId() && detail.getSupplierId() > 0 && !supplierIds.contains(detail.getSupplierId())) {
                                detail.setSupplierItemOuterId("***");
                                detail.setSupplierName("***");
                                detail.setSupplierCode("***");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 解析订单系统备注中的代收货款
     */
    public static BigDecimal getCodValueBySysMemo(Trade trade, boolean needTradePayment) {
        String sysMemo = trade.getSysMemo();
        if (StringUtils.isBlank(sysMemo)) {
            if (needTradePayment) {
                return BigDecimal.valueOf(calculateColumnPayment(trade));
            }
            throw new IllegalArgumentException("快递模板开启配置：【取订单系统备注中手工填写的代收金额】，但手工填写的代收金额格式错误，请检查");
        }
        Pattern codValueCompile = Pattern.compile(COD_VALUE_COMPILE);
        Matcher matcher = codValueCompile.matcher(sysMemo);
        if (matcher.find()) {
            String codValue = matcher.group().replaceAll(" ", "");
            if (StringUtils.isNotBlank(codValue)) {
                try {
                    return new BigDecimal(codValue);
                } catch (Exception e) {
                    logger.debug("解析订单系统备注中的代收货款出错，代收货款转换为金额类型错误！codValue：" + codValue);
                }
            }
        }
        if (needTradePayment) {
            return BigDecimal.valueOf(calculateColumnPayment(trade));
        }
        throw new IllegalArgumentException("快递模板开启配置：【取订单系统备注中手工填写的代收金额】，但手工填写的代收金额格式错误，请检查！");
    }

    /**
     * 计算订单实付金额 合单
     */
    public static double calculateColumnPayment(Trade trade) {
        double columnPayment = 0.0D;//所有子定单的实付金额合和存在子订单的订单的邮费
        //供销订单取ext扩展表字段金额
        if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_NEWFX)) {
            TradeExt tradeExt = Optional.ofNullable(trade.getTradeExt()).orElse(new TradeExt());
            String payAmount = String.valueOf(tradeExt.get("payAmount"));
            if (StringUtils.isNotBlank(payAmount) && !Objects.equals(payAmount, "null")) {
                return NumberUtils.str2Double(payAmount);
            }
        }

        if ("jd-1".equals(trade.getType())) {
            columnPayment = NumberUtils.str2Double(trade.getPayment());
        } else {
            columnPayment = NumberUtils.str2Double(TradeUtils.getPayment(trade));
        }
        return columnPayment;
    }

    /**
     * 处理代收货款
     *
     * <AUTHOR>
     * @date 2021/5/24
     */
    public static void handleCod(Map<String, Object> result, UserWlbExpressTemplate userWlbExpressTemplate, Trade singleTrade) {
        String field = EnumServiceValueName.NEED_COD.getFieldName();
        // 德邦快递开关值处理
        if (Objects.equals(userWlbExpressTemplate.getWlbTemplateType(), WlbTemplateTypeEnum.BRANCH.getValue())
                && Objects.equals(userWlbExpressTemplate.getWlbType(), EnumWlbType.DB.getValue())) {
            field = EnumServiceValueName.DB_COD.getFieldName();
        }
        // 是否开启代收货款：代收货款开关标识 0.关，1.开
        String needCod = compatibleTemplateFileds(userWlbExpressTemplate, field, EnumServiceValueName.NEED_COD.getFieldName());
        // 开启返回："代收货款"， 否则返回空字符
        result.put(EnumServiceValueName.NEED_COD.getFieldName(), Objects.equals(needCod, "1") ? "代收货款" : "");
        // 如果开启，则计算代收货款金额
        if (Objects.equals(needCod, "1")) {
            result.put(EnumServiceValueName.NEED_COD.getFieldValue(), String.valueOf(calculateColumnPayment(singleTrade)));
        }
    }

    /**
     * 处理保价字段
     *
     * <AUTHOR>
     * @date 2021/5/24
     */
    public static void handleInsureAmount(Map<String, Object> result, UserWlbExpressTemplate userWlbExpressTemplate,
                                          Trade singleTrade, Staff staff, ITradeSearchService tradeQueryService) {
        // 处理极兔保价字段
        String insureAmount = compatibleTemplateFileds(userWlbExpressTemplate, EnumServiceValueName.INSURE_AMOUNT.getFieldName(), EnumServiceValueName.INSURE_AMOUNT.getFieldName());
        // 开启保价则返回："保"，否则返回空字符
        result.put("need_insure", StringUtils.isBlank(insureAmount) || Objects.equals(insureAmount, "1") ? "" : "保");
        // 保价金额
        if (!Objects.equals(result.get("insure_amount"), "1")) {
            result.put("insure_amount_value", getInsureAmountValue(staff, userWlbExpressTemplate, singleTrade, tradeQueryService));
        }
    }

    /**
     * 设置订单商品备注
     */
    public static void setOrderRemark(List<String> needValues, Trade trade, Map<String, Object> result, String nullGetSellerBuyer) {
        StringBuilder orderRemarks = new StringBuilder();
        if (needValues.contains("item_order_remark")) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (int i = 0; i < orders.size(); i++) {
                Order order = orders.get(i);
                String orderRemark = null;
                if (order != null && order.getOrderExt() != null) {
                    orderRemark = orders.get(i).getOrderExt().getOrderRemark();
                }

                if (StringUtils.isEmpty(nullGetSellerBuyer) || "1".equals(nullGetSellerBuyer)) {
                    orderRemark = order != null
                            ? StrUtil.isEmpty(orderRemark) // 判断订单商品备注
                            ? StrUtil.isEmpty(order.getTradeSellerMemo()) // 判断卖家备注
                            ? StrUtil.isEmpty(order.getTradeBuyerMessage()) // 判断买家留言
                            ? " " : order.getTradeBuyerMessage().replace(",", "%2C")
                            : order.getTradeSellerMemo().replace(",", "%2C")
                            : orderRemark.replace(",", "%2C")
                            : " ";
                } else {
                    orderRemark = orderRemark == null ? " " : orderRemark.replace(",", "%2C");
                }
                orderRemarks.append(orderRemark);
                if (i != orders.size() - 1) {
                    orderRemarks.append(",");
                }
            }
        }
        result.put("item_order_remark", orderRemarks.toString());
    }


    /**
     * 设置订单缺货相关参数
     * 缺货标记为1，无缺货未0
     *
     * @param outerKey 规格编码key
     */
    public static void setShortage(Map<String, Object> waveSortingDetailMap, Trade singleTrade, Map<String, Object> result, String outerKey) {
        Object sort_item_outer_id = result.get(outerKey);
        String[] split = sort_item_outer_id.toString().split(",");
        StringBuilder sb = new StringBuilder();
        StringBuilder itemSb = new StringBuilder();
        Integer orderShortageTotal = 0;
        for (int i = 0; i < split.length; i++) {
            WaveSortingDetail waveSortingDetail = (WaveSortingDetail) waveSortingDetailMap.get(singleTrade.getSid() + split[i]);
            if (null != waveSortingDetail) {
                if (waveSortingDetail.getShortageNum() > 0) {
                    itemSb.append("★");
                } else {
                    itemSb.append(" ");
                }
                orderShortageTotal += waveSortingDetail.getShortageNum();
                sb.append(waveSortingDetail.getShortageNum());
            } else {
                itemSb.append(" ");
            }
            if (i != split.length - 1) {
                sb.append(",");
                itemSb.append(",");
            }
        }
        result.put("order_shortage_total", orderShortageTotal);
        result.put("table_item_shortage_num", sb.toString());
        result.put("table_item_shortage_mark", itemSb.toString());
        result.put("item_shortage_num", sb.toString());
        result.put("item_shortage_mark", itemSb.toString());
        if (orderShortageTotal != 0) {
            result.put("order_shortage_mark", "★");
        }
    }

    /**
     * 设置发货单订单缺货相关参数
     * 缺货标记为1，无缺货未0
     */
    public static void setDeliverShortage(List<WaveSortingDetail> waveSortingDetailMap, Trade singleTrade, Map<String, Object> result, UserDeliverTemplate userDeliverTemplate) {
        boolean needSameItemMerge = needSameItemMerge(userDeliverTemplate);
        // 是否需要合并商品，合并商品和非合并商品 计算 缺货纬度不一致
        if (needSameItemMerge) {
            itemMergeShortage(waveSortingDetailMap, singleTrade, result);
        } else {
            notItemMergeShortage(waveSortingDetailMap, singleTrade, result);
        }
    }

    private static void itemMergeShortage(List<WaveSortingDetail> waveSortingDetailList, Trade singleTrade, Map<String, Object> result) {
        Object sort_item_outer_id = result.get("sort_sku_outer_id");
        Map<String, List<WaveSortingDetail>> waveSortingDetailMap = waveSortingDetailList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(wsd -> wsd.getSid().toString() + wsd.getOuterId()));

        String[] split = sort_item_outer_id.toString().split(",");
        StringJoiner sb = new StringJoiner(",");
        StringJoiner itemSb = new StringJoiner(",");
        int orderShortageTotal = 0;
        for (String s : split) {
            List<WaveSortingDetail> waveSortingDetails = Optional.ofNullable(waveSortingDetailMap.get(singleTrade.getSid() + s)).orElse(new ArrayList<>(1));
            Integer shortageNum = waveSortingDetails.stream().mapToInt(WaveSortingDetail::getShortageNum).sum();
            if (shortageNum > 0) {
                itemSb.add("★");
                sb.add(String.valueOf(shortageNum));
            } else {
                sb.add("");
                itemSb.add(" ");
            }
            orderShortageTotal += shortageNum;
        }
        result.put("order_shortage_total", orderShortageTotal);
        result.put("table_item_shortage_num", sb.toString());
        result.put("table_item_shortage_mark", itemSb.toString());
        result.put("item_shortage_num", sb.toString());
        result.put("item_shortage_mark", itemSb.toString());
        if (orderShortageTotal != 0) {
            result.put("order_shortage_mark", "★");
        }
    }

    private static void notItemMergeShortage(List<WaveSortingDetail> waveSortingDetailList, Trade singleTrade, Map<String, Object> result) {
        Map<Long, List<WaveSortingDetail>> waveSortingDetailMap = waveSortingDetailList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(WaveSortingDetail::getOrderId));

        StringJoiner sb = new StringJoiner(",");
        StringJoiner itemSb = new StringJoiner(",");
        int orderShortageTotal = 0;
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        for (Order order : orderList) {
            List<WaveSortingDetail> waveSortingDetails = Optional.ofNullable(waveSortingDetailMap.get(order.getId())).orElse(new ArrayList<>(1));
            Integer shortageNum = waveSortingDetails.stream().mapToInt(WaveSortingDetail::getShortageNum).sum();
            if (shortageNum > 0) {
                itemSb.add("★");
                sb.add(String.valueOf(shortageNum));
            } else {
                sb.add("");
                itemSb.add(" ");
            }
            orderShortageTotal += shortageNum;
        }
        result.put("order_shortage_total", orderShortageTotal);
        result.put("table_item_shortage_num", sb.toString());
        result.put("table_item_shortage_mark", itemSb.toString());
        result.put("item_shortage_num", sb.toString());
        result.put("item_shortage_mark", itemSb.toString());
        if (orderShortageTotal != 0) {
            result.put("order_shortage_mark", "★");
        }
    }

    /**
     * 菜鸟虚拟号改造。
     * <p>
     * 满足以下条件时，在详细地址后拼接 "[配送拨打15911110000转1234]" ：淘系订单 && 网点模版 && 虚拟手机号
     * </p>
     *
     * <AUTHOR>
     * @date 2021/12/3
     */
    public static void handleTbVirtualPhone(Map<String, Object> result, Trade singleTrade,
                                            UserWlbExpressTemplate userWlbExpressTemplate, IUserService userService, Map<Long, User> userMap, Staff staff) {
        // 模板类型
        Integer wlbTemplateType = userWlbExpressTemplate.getWlbTemplateType();

        // 非网点模版 || 非淘系订单  不处理
        if (!Objects.equals(wlbTemplateType, WlbTemplateTypeEnum.BRANCH.getValue())
                || !WayBillCompanyHelper.isCipherTrade(staff, singleTrade, userService, userMap)) {
            return;
        }

        String format = "{0}[配送拨打{1}]";
        String receiverAddress = (String) result.getOrDefault("receiver_address", "");
        if (!StringUtils.contains(receiverAddress, "配送拨打")) {
            // 若手机号大于11位，在详细地址后拼接 "[配送请拨打15911110000转1234]"
            if (StringUtils.defaultString(singleTrade.getReceiverMobile()).length() > 11) {
                result.put("receiver_address", MessageFormat.format(format, receiverAddress, singleTrade.getReceiverMobile()));
            } else if (StringUtils.defaultString(singleTrade.getReceiverPhone()).length() > 11) {
                result.put("receiver_address", MessageFormat.format(format, receiverAddress, singleTrade.getReceiverPhone()));
            }
        }
    }


    /**
     * 从fieldValues中拿到PrintDimension
     */
    public static Integer getPrintDimensionWithFieldValues(List<FieldValue> fieldValues) {
        Integer PrintDimension = null;
        for (FieldValue fieldValue : fieldValues) {
            if (!EnumFieldValueName.PRINT_DIMENSION.getValue().equals(fieldValue.getName())) {
                continue;
            }
            try {
                PrintDimension = Integer.parseInt(fieldValue.getValue());
                break;
            } catch (Exception e) {
                logger.error("获取打印数据转化PrintDimension失败：" + fieldValue.getValue(), e);
            }
        }
        return PrintDimension;
    }

    /**
     * 获取电子面单模板service设置
     */
    public static String getServiceByKey(UserWlbExpressTemplate template, String key, String defaultValue) {
        if (template == null || (StringUtils.isBlank(template.getServiceValueStr()) && MapUtils.isEmpty(template.getServiceValue()))) {
            return defaultValue;
        }
        Map<String, String> serviceValue = template.getServiceValue();
        if (MapUtils.isNotEmpty(serviceValue)) {
            return StringUtils.isNotBlank(serviceValue.get(key)) && !"null".equals(serviceValue.get(key)) ? serviceValue.get(key) : defaultValue;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(template.getServiceValueStr());
            return StringUtils.isNotBlank(jsonObject.getString(key)) && !"null".equals(serviceValue.get(key)) ? jsonObject.getString(key) : defaultValue;
        } catch (Exception e) {
            logger.debug("读取模serviceValues失败!msg:" + e.getMessage());
        }
        return defaultValue;
    }

    /**
     * 排序完后处理有商品序号的字段
     */
    public static void setItemIndexFields(Map<String, Object> result) {
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String fieldName = entry.getKey();
            if (fieldName.contains("item_index")) {
                String strValue = (String) result.get(fieldName);
                String[] fieldsValue = strValue.split(",");
                StringBuilder newValue = new StringBuilder();
                for (int i = 0; i < fieldsValue.length; i++) {
                    String replace = fieldsValue[i].replace("*$itemIndex#*", "(" + (i + 1) + ")");
                    newValue.append(replace).append(",");
                }
                if (newValue.length() > 0) {
                    newValue.deleteCharAt(newValue.length() - 1);
                }
                result.put(fieldName, newValue.toString());
            }
        }
    }

    public static Integer getSpecificSortType(List<FieldValue> fieldValues) {
        int specificSortType = 0;
        if (CollectionUtils.isNotEmpty(fieldValues)) {
            List<FieldValue> specifics = fieldValues.stream().filter(temp -> EnumFieldValueName.SPECIFIC_SORT_TYPE.getValue().equals(temp.getName())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(specifics)) {
                specificSortType = Integer.parseInt(specifics.get(0).getValue());
            }
        }
        return specificSortType;
    }


    public static String formatTime(Date date, String timeType) {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        if ("day".equals(timeType)) {
            pattern = "yyyy-MM-dd";
        } else if ("month".equals(timeType)) {
            pattern = "yyyy-MM";
        }
        return DateUtil.format(date, pattern);
    }

    public static String formatTimeReverse(Date date, String timeType) {
        String pattern = "MMddHHmmyy";
        if ("day".equals(timeType)) {
            pattern = "MMddyy";
        } else if ("month".equals(timeType)) {
            pattern = "MMyy";
        }
        return DateUtil.format(date, pattern);
    }

    /**
     * 更新面单回收池状态
     * TODO 可能会删除
     */
    public static void updateOutSidPoolService(Staff staff, List<String> outSids, IOutSidRecyclePoolService outSidRecyclePoolService) {
        if (CollectionUtils.isEmpty(outSids)) {
            return;
        }
        List<OutSidRecyclePool> outSidRecyclePools = outSidRecyclePoolService.queryByOutSids(staff, outSids);
        if (CollectionUtils.isEmpty(outSidRecyclePools)) {
            return;
        }
        //更新面单池数据
        outSidRecyclePoolService.batchUpdate(staff,
                outSidRecyclePools.stream()
                        .map(outPool -> {
                            OutSidRecyclePool upOutSidRecyclePool = new OutSidRecyclePool();
                            upOutSidRecyclePool.setId(outPool.getId());
                            upOutSidRecyclePool.setStatus(OutSidRecyclePool.STATUS_CONFIRM_PRINT);
                            return upOutSidRecyclePool;
                        }).collect(Collectors.toList()));
    }

    /**
     * 设置btas订单码
     */
    public static void buildTradeBtasCode(List<String> needValues, Trade trade, Map<String, Object> resolve) {
        if (needValues.contains("trade_btas_code")) {
            StringBuilder btasCodeSb = new StringBuilder();
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                List<String> btasOrderCode = OrderUtils.getBtasOrderCode(order);
                StringBuilder sb = new StringBuilder();
                for (String btasCode : btasOrderCode) {
                    sb.append(btasCode).append(" ");
                }
                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                btasCodeSb.append(sb.length() > 0 ? sb.toString() : "").append(",");
            }
            if (btasCodeSb.length() > 0) {
                btasCodeSb.deleteCharAt(btasCodeSb.length() - 1);
            }
            resolve.put("trade_btas_code", btasCodeSb.toString());
        }
    }

    public static void buildChildItemDetails(UserDeliverTemplate userDeliverTemplate, List<String> needValues, Trade trade, Map<Long, DmjItem> idToDmjItem, Map<String, List<GoodsSectionOrderRecord>> keyGoodsSectionOrderRecordMap, Map<String, Object> result) {
        if (PrintTemplateHelper.getSelectSuiteType(userDeliverTemplate) == 0 || !(needValues.contains("table_child_outer_id_of_item") || needValues.contains("table_child_item_outer_id") ||
                needValues.contains("table_child_item_num") || needValues.contains("table_child_section_code"))) {
            return;
        }
        StringBuilder childItemNumSb = new StringBuilder();// 套件的子件数量
        StringBuilder childItemOuterIdSb = new StringBuilder();// 套件的子件规格编码
        StringBuilder childOuterIdOfItemSb = new StringBuilder();// 套件的子件主商家编码
        StringBuilder childSectionCodeSb = new StringBuilder();// 套件的子件拣选货位
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (order.isSuit() && CollectionUtils.isNotEmpty(order.getSuits())) {
                List<Order> suits = order.getSuits();
                for (Order suit : suits) {
                    Long skuSysId = suit.getSkuSysId() == null || suit.getSkuSysId() < 1 ? -1L : suit.getSkuSysId();
                    String key = suit.getItemSysId() + "_" + skuSysId + "_" + suit.getId();
                    String childOuterIdOfItem = idToDmjItem.get(suit.getItemSysId()) == null ? "" : idToDmjItem.get(suit.getItemSysId()).getOuterId();
                    // 拣选货位
                    List<GoodsSectionOrderRecord> goodsSectionOrderRecords = CollectionUtils.isNotEmpty(keyGoodsSectionOrderRecordMap.get(key)) ? keyGoodsSectionOrderRecordMap.get(key) : new ArrayList<>();
                    goodsSectionOrderRecords.sort((o1, o2) -> Collator.getInstance(Locale.CHINA).compare(o1.getGoodsSectionCode(), o2.getGoodsSectionCode()));
                    for (GoodsSectionOrderRecord goodsSectionOrderRecord : goodsSectionOrderRecords) {
                        childSectionCodeSb.append(goodsSectionOrderRecord.getGoodsSectionDisplay()).append("<br/>");
                    }
                    childItemNumSb.append(suit.getSysOuterId()).append("*").append(suit.getNum() / order.getNum()).append("<br/>");//单品比例
                    childItemOuterIdSb.append(suit.getSysOuterId()).append("<br/>");
                    childOuterIdOfItemSb.append(childOuterIdOfItem).append("<br/>");
                }
                childSectionCodeSb.append(",");
                childItemNumSb.append(",");
                childItemOuterIdSb.append(",");
                childOuterIdOfItemSb.append(",");
            } else {
                childSectionCodeSb.append(",");
                childItemNumSb.append(",");
                childItemOuterIdSb.append(",");
                childOuterIdOfItemSb.append(",");
            }
        }
        if (childItemNumSb.length() > 0) {
            childItemNumSb.deleteCharAt(childItemNumSb.length() - 1);
        }
        if (childItemOuterIdSb.length() > 0) {
            childItemOuterIdSb.deleteCharAt(childItemOuterIdSb.length() - 1);
        }
        if (childOuterIdOfItemSb.length() > 0) {
            childOuterIdOfItemSb.deleteCharAt(childOuterIdOfItemSb.length() - 1);
        }
        if (childSectionCodeSb.length() > 0) {
            childSectionCodeSb.deleteCharAt(childSectionCodeSb.length() - 1);
        }
        result.put("table_child_outer_id_of_item", childOuterIdOfItemSb);
        result.put("table_child_item_outer_id", childItemOuterIdSb);
        result.put("table_child_item_num", childItemNumSb);
        result.put("table_child_section_code", childSectionCodeSb);
    }

    /**
     * 获取同款商品总数量
     */
    public static void __ifNeedItemTotal(List<String> needValues, Trade singleTrade, Map<String, Object> result) {
        if (!needValues.contains("table_item_total")) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        Map<Long, List<Order>> ordersMap = orders.stream().filter(t -> t.getItemSysId() != null).collect(Collectors.groupingBy(Order::getItemSysId));
        StringBuilder itemTotalSb = new StringBuilder();
        for (Order order : orders) {
            Integer totalNum = 0;
            List<Order> orders1 = ordersMap.get(order.getItemSysId());
            if (CollectionUtils.isNotEmpty(orders1)) {
                totalNum += orders1.stream().mapToInt(t -> t.getNum() == null ? 0 : t.getNum()).sum();
            }
            itemTotalSb.append(totalNum).append(",");
        }
        if (itemTotalSb.length() > 0) {
            itemTotalSb.deleteCharAt(itemTotalSb.length() - 1);
        }
        result.put("table_item_total", String.valueOf(itemTotalSb));
    }

    /**
     * 电子面单又改名字又改存放地址
     * 将电子面单中的增值服务的一些以前存filedValues的字段,放到serviceValues中,
     * 注：1、老模板的数据还是会在fieldValues中的或者存在放到serviceValues中,需要兼容
     * 2、此时的老数据和新数据存的key不一样
     */
    public static String compatibleTemplateFileds(UserWlbExpressTemplate template, String oldKey, String newKey) {
        // 在serviceValue中找key
        Map<String, String> serviceValue = Optional.ofNullable(template.getServiceValue()).orElse(new HashMap<>());
        // 获取存放在serviceValues中的新的key的值(或许没改过key,就传一样的)
        String newValue = serviceValue.get(newKey);
        if (newValue == null) {
            // 兼容老模板存在的数据,在serviceValues中未找到字段,直接返回在fieldValuesCustom找到的
            return getFieldValueByKey(template, oldKey);
        }
        return newValue;
    }


    public static String getTemplateServiceValue(UserWlbExpressTemplate template, String key, String defaultValue) {
        // 在serviceValue中找key
        Map<String, String> serviceValueMap = Optional.ofNullable(template.getServiceValue()).orElse(new HashMap<>());
        String serviceValue = serviceValueMap.get(key);
        return StringUtils.isNotEmpty(serviceValue) ? serviceValue : defaultValue;
    }


    /**
     * 计算顺丰保价服务值
     */
    public static String getSfInsureAmountValue(UserWlbExpressTemplate template, Trade singleTrade) {
        String fieldValueByKey = compatibleTemplateFileds(template, EnumServiceValueName.SF_INSURE_AMOUNT.getFieldName(), EnumServiceValueName.INSURE_AMOUNT.getFieldName());
        if ("2".equals(fieldValueByKey)) {
            return TradeUtils.getPayment(singleTrade);
        } else if ("3".equals(fieldValueByKey)) {
            return compatibleTemplateFileds(template, EnumServiceValueName.SF_INSURE_AMOUNT.getFieldValue(), EnumServiceValueName.INSURE_AMOUNT.getFieldValue());
        }
        return "";
    }

    /**
     * 计算代收货款
     */
    public static String getCodValue(UserWlbExpressTemplate template, Trade singleTrade) {
        String fieldValueByKey = compatibleTemplateFileds(template, EnumServiceValueName.NEED_COD.getFieldName(), EnumServiceValueName.NEED_COD.getFieldName());
        if ("1".equals(fieldValueByKey)) {
            String codValue = compatibleTemplateFileds(template, EnumServiceValueName.NEED_COD.getFieldValue(), EnumServiceValueName.NEED_COD.getFieldValue());
            return StringUtils.isNotBlank(codValue) && new BigDecimal(codValue).intValue() > 0 ? codValue : String.valueOf(calculateColumnPayment(singleTrade));
        }
        return "";
    }

    /**
     * 获取订单重量信息
     *
     * @param isConvertWeight 是否进行转换为g，京东平台不需要转换
     */
    public static Long getTradeWeight(UserWlbExpressTemplate template, Trade singleTrade, boolean isConvertWeight) {
        long tradeWeight = 0L;
        assert template != null : "模板不能为空";
        String weightSelectType = PrintTemplateHelper.getFieldValueByKey(template, EnumFieldValueName.TOTAL_WEIGHT_SELECT.getValue());
        try {
            assert singleTrade != null : "订单不能为空";
            if (EnumPrintAllState.TRADE_WEIGHT_1.state.equals(weightSelectType)) {
                BigDecimal bigDecimal = new BigDecimal("0.00");
                bigDecimal = bigDecimal.add(new BigDecimal(singleTrade.getNetWeight().toString()));
                if (isConvertWeight) {
                    bigDecimal = bigDecimal.multiply(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                }
                tradeWeight = bigDecimal.longValue();
            }
            if (EnumPrintAllState.TRADE_WEIGHT_2.state.equals(weightSelectType)) {
                String totalWeight = PrintTemplateHelper.getFieldValueByKey(template, EnumFieldValueName.TOTAL_WEIGHT.getValue());
                assert totalWeight != null && !totalWeight.isEmpty() : "用户设置的重量为空，默认返回0";
                BigDecimal weight = new BigDecimal(totalWeight);
                if (isConvertWeight) {
                    weight = weight.multiply(new BigDecimal("1000")).setScale(0, RoundingMode.DOWN);
                }
                tradeWeight = weight.longValue();
            }
        } catch (AssertionError | Exception e) {
            logger.warn("获取订单重量异常" + e);
            return tradeWeight;
        }
        return tradeWeight;
    }

    /**
     * 获取订单体积信息
     */
    public static Long getTradeVolume(UserWlbExpressTemplate template, Trade singleTrade) {
        try {
            assert template != null : "模板不能为空";
            assert singleTrade != null : "订单不能为空";

            String weightSelectType = PrintTemplateHelper.getFieldValueByKey(template, EnumNeedRemoveFileds.TOTAL_VOLUME_SELECT.getValue());
            if (EnumPrintAllState.TRADE_VOLUME_0.state.equals(weightSelectType)) {
                return 0L;
            }
            if (EnumPrintAllState.TRADE_VOLUME_1.state.equals(weightSelectType)) {
                Double tradeVolume = Optional.ofNullable(singleTrade.getVolume()).orElse(0D);
                return tradeVolume.longValue();
            }
        } catch (AssertionError | NumberFormatException e) {
            logger.error("获取订单体积异常" + e);
            return 0L;
        }
        return 0L;
    }
    public static Double getTradeVolumeToDouble(UserWlbExpressTemplate template, Trade singleTrade) {
        try {
            assert template != null : "模板不能为空";
            assert singleTrade != null : "订单不能为空";

            String weightSelectType = PrintTemplateHelper.getFieldValueByKey(template, EnumNeedRemoveFileds.TOTAL_VOLUME_SELECT.getValue());
            if (EnumPrintAllState.TRADE_VOLUME_0.state.equals(weightSelectType)) {
                return 0D;
            }
            if (EnumPrintAllState.TRADE_VOLUME_1.state.equals(weightSelectType)) {
                return Optional.ofNullable(singleTrade.getVolume()).orElse(0D);
            }
        } catch (AssertionError | NumberFormatException e) {
            logger.error("获取订单体积异常" + e);
            return 0D;
        }
        return 0D;
    }

    /**
     * 尺码排序
     */
    public static void otherSort(List<String> otherPropNameList) {
        otherPropNameList.sort(new Comparator<String>() {
            final Map<String, Integer> map = new LinkedHashMap<>();

            private void loadMap() {
                List<String> sortField = Arrays.asList("", "均码", "F", "XS", "S", "M", "L", "XL", "XXL", "2XL", "XXXL",
                        "3XL", "XXXXL", "4XL", "XXXXXL", "5XL", "XXXXXXL", "6XL", "XXXXXXXL", "7XL", "XXXXXXXXL", "8XL", "XXXXXXXXXL", "9XL");
                for (int i = 0; i < sortField.size(); i++) {
                    map.put(sortField.get(i), i);
                }
            }

            @Override
            public int compare(String o1, String o2) {
                loadMap();
                if (o1 != null) {
                    o1 = o1.toUpperCase();
                }
                if (o2 != null) {
                    o2 = o2.toUpperCase();
                }
                int nullValue = 806461562;
                int x = map.get(o1) == null ? nullValue : map.get(o1);
                int y = map.get(o2) == null ? nullValue : map.get(o2);
                if (x == nullValue && y == nullValue) {
                    return Collator.getInstance(Locale.CHINA).compare(o1, o2);
                }
                return Integer.compare(x, y);
            }
        });
    }

    /**
     * 过滤无需发货子订单 默认过滤
     *
     * @param unNeedDeliverPrint 是否打印 1打印 其他不打印
     */
    public static void filterNonConsignOrder(Trade trade, String unNeedDeliverPrint) {
        if ("1".equals(unNeedDeliverPrint)) {
            //需要打印直接返回
            return;
        }
        filterNonConsignOrder(trade);
    }

    public static void filterNonConsignOrder(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Order> filterList = orders.stream().filter(Order::ifNonConsign).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterList)) {
            orders.removeAll(filterList);
            TradeUtils.setOrders(trade, orders);
        }
    }

    /**
     * 过滤无需发货子订单 默认过滤
     *
     * @param unNeedDeliverPrint 是否打印 1打印 其他不打印
     */
    public static void filterNonConsignOrders(List<Trade> trades, String unNeedDeliverPrint) {
        if ("1".equals(unNeedDeliverPrint)) {
            //需要打印直接返回
            return;
        }
        for (Trade trade : trades) {
            filterNonConsignOrder(trade);
        }
    }

    /**
     * 发货单读取fieldValue配置 根据key
     *
     * @param key 配置key
     */
    public static String getFieldValueByKey(UserDeliverTemplate template, String key) {
        List<FieldValue> fieldValues = template.getFieldValues();
        return getFieldValueByKey(fieldValues, key);
    }

    /**
     * 发货单读取fieldValue配置 根据key
     *
     * @param key 配置key
     */
    public static String getFieldValueByKey(UserWlbExpressTemplate template, String key) {
        List<FieldValue> fieldValues = template.getFieldValuesCustom();
        if (CollectionUtils.isEmpty(fieldValues)) {
            WlbExpressHelper.initConvertFieldValuesStr(template);
            fieldValues = template.getFieldValuesCustom();
        }
        return getFieldValueByKey(fieldValues, key);
    }


    /**
     * 发货单获取商品重量\商品体积
     */
    public static void __ifNeedWeightOrVolume(List<String> needValues, Trade singleTrade, Map<String, Object> result) {
        if (!needValues.contains("table_item_weight") && !needValues.contains("table_item_volume")) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder weightSb = new StringBuilder();
        StringBuilder volumeSb = new StringBuilder();
        for (Order order : orders) {
            Integer num = order.getNum();
            Double weight = order.getNetWeight() != null ? order.getNetWeight() * num : 0;
            Double volume = order.getVolume() != null ? order.getVolume() * num : 0;
            weightSb.append(String.format("%.3f", weight)).append(",");
            volumeSb.append(String.format("%.1f", volume)).append(",");
        }
        if (weightSb.length() > 0) {
            weightSb.deleteCharAt(weightSb.length() - 1);
        }
        if (volumeSb.length() > 0) {
            volumeSb.deleteCharAt(volumeSb.length() - 1);
        }
        result.put("table_item_weight", weightSb.toString());
        result.put("table_item_volume", volumeSb.toString());
    }

    public static Map<String, TbItem> getTbItemMapNew(Staff staff, boolean needItemPlatformTitle, List<Trade> trades, PrintPageSearchService printPageSearchService) {
        Map<String, TbItem> tbItemMap = new HashMap<>();
        if (needItemPlatformTitle) {
            List<Order> orderList = TradeUtils.getOrders4Trade(trades);
            if (CollectionUtils.isNotEmpty(orderList)) {
                Set<String> numIids = new HashSet<>(orderList.size());
                List<String> skuIdList = new ArrayList<>();
                for (Order order : orderList) {
                    numIids.add(order.getNumIid());
                    skuIdList.add(order.getSkuId());
                }
                List<String> numIidList = new ArrayList<>(numIids.size());
                numIidList.addAll(numIids);
                List<TbItem> tbItems = printPageSearchService.queryTbItemListNew(staff, numIidList, skuIdList);
                for (TbItem tbItem : tbItems) {
                    //修复商品名称和规格名称对应不正确的BUG,当Item下有Sku的时候key为NumIid+"_"+skuId
                    if (CollectionUtils.isNotEmpty(tbItem.getSkuList())) {
                        for (TbSku tbSku : tbItem.getSkuList()) {
                            tbItemMap.put(tbItem.getNumIid() + "_" + tbSku.getSkuId(), tbSku);
                        }
                    }
                    tbItemMap.put(tbItem.getNumIid(), tbItem);
                }
            }
        }
        return tbItemMap;
    }

    /**
     * 获取订单标签
     */
    public static void queryTradeTags(Staff staff, List<Trade> trades, ITradeLabelService tradeLabelService, ITradeTagService tradeTagService) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 获取trade中所有tagId,订单标签分离,通过sid获取标签ID
        List<Long> sids = trades.stream().map(Trade::getSid).distinct().collect(Collectors.toList());
        List<TradeLabel> tradeLabels = tradeLabelService.query(staff, sids);
        if (CollectionUtils.isEmpty(tradeLabels)) {
            return;
        }
        Long[] tagIdArray = tradeLabels.stream().map(TradeLabel::getLabelId).distinct().toArray(Long[]::new);
        // 一个订单会有多个标签
        Map<Long, List<Long>> labelIdMap = new HashMap<>();
        tradeLabels.forEach(t -> labelIdMap.computeIfAbsent(t.getSid(), v -> new ArrayList<>()).add(t.getLabelId()));
        trades.forEach(t -> {
            List<Long> labelIds = labelIdMap.get(t.getSid());
            if (CollectionUtils.isNotEmpty(labelIds)) {
                // 写入订单方便后续使用
                t.setTagIds(Strings.join(",", labelIds));
            }
        });
        // 获取订单标签Map
        List<TradeTag> allTradeTags = tradeTagService.queryByIds(staff, tagIdArray);
        if (CollectionUtils.isNotEmpty(allTradeTags)) {
            Map<Long, TradeTag> tradeTagMap = allTradeTags.stream().collect(Collectors.toMap(TradeTag::getId, Function.identity(), (t1, t2) -> t1));
            for (Trade trade : trades) {
                String tagIdStr = trade.getTagIds();
                if (StringUtils.isBlank(tagIdStr)) {
                    continue;
                }
                List<Long> tagIds = Arrays.stream(tagIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
                List<TradeTag> tradeTags = new ArrayList<>();
                List<String> tagNames = new ArrayList<>();
                for (Long tagId : tagIds) {
                    TradeTag tradeTag = tradeTagMap.get(tagId);
                    if (tradeTag != null) {
                        tradeTags.add(tradeTag);
                        tagNames.add(tradeTag.getTagName());
                    }
                }
                trade.setTags(tradeTags);
                trade.setTagName(tagNames);
            }
        }
    }

    /**
     * @param tradeList
     * @param multiPrintConfig
     * @return java.util.List<com.raycloud.dmj.domain.trades.Trade>
     * <AUTHOR>
     * @Description 跨境后置打印根据配置过滤订单是否打印
     * @Date 14:10 2024/4/19
     **/
    public static List<Trade> filterAfterAbroad(List<Trade> tradeList, MultiPrintConfig multiPrintConfig) {
        //是否开启了跨境全托管配置 并且未开启打印快递单配置 不进行取号和打印
        Object kjPlatformPrintSet = Optional.ofNullable(multiPrintConfig.get("kjPlatformPrintSet")).orElse(0);
        Object abroadAfterExpressPrint = Optional.ofNullable(multiPrintConfig.get("abroadAfterExpressPrint")).orElse(0);
        if (Objects.equals(kjPlatformPrintSet, 1) && Objects.equals(abroadAfterExpressPrint, 0)) {
            //过滤跨境平台来源订单
            return tradeList.stream().filter(e -> !abroadTradeSource.contains(e.getSource())).collect(Collectors.toList());
        }
        return tradeList;
    }

    /**
     * @param trade
     * @param multiPrintConfig
     * @return java.util.List<com.raycloud.dmj.domain.trades.Trade>
     * <AUTHOR>
     * @Description 跨境后置打印根据配置过滤订单是否打印
     * @Date 14:10 2024/4/19
     **/
    public static boolean filterAfterAbroad(Trade trade, MultiPrintConfig multiPrintConfig) {
        //是否开启了跨境全托管配置 并且未开启打印快递单配置 不进行取号和打印
        Object kjPlatformPrintSet = Optional.ofNullable(multiPrintConfig.get("kjPlatformPrintSet")).orElse(0);
        Object abroadAfterExpressPrint = Optional.ofNullable(multiPrintConfig.get("abroadAfterExpressPrint")).orElse(0);
        if (Objects.equals(kjPlatformPrintSet, 1) && Objects.equals(abroadAfterExpressPrint, 0)) {
            //过滤跨境平台来源订单
            return abroadTradeSource.contains(trade.getSource());
        }
        return false;
    }

    /**
     * 判断是否为跨境全托管订单
     *
     * @param trade
     * @return
     */
    public static boolean filterAfterAbroad(Trade trade) {
        return abroadTradeSource.contains(trade.getSource());
    }

    /**
     * 初始化订单类型相关字段
     */
    public static String __handleTradeTabs(Trade singleTrade) {
        StringBuilder trade_tab = new StringBuilder();
        String tab = TRADE_TAB_MAP.get(singleTrade.getType());
        if (StringUtils.isNotBlank(tab)) {
            trade_tab.append(tab).append(" ");
        }
        if ("sys".equals(singleTrade.getSource()) && !"putchase_out".equals(singleTrade.getType())) {
            trade_tab.append("手工").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsPresell())) {
            trade_tab.append("预售").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsUrgent())) {
            trade_tab.append("急").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getScalping())) {
            trade_tab.append("空包单").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsJz())) {
            trade_tab.append("家装").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsSmart())) {
            trade_tab.append("智选").append(" ");
        }
        if (YesNoEnum.YES.getValue().equals(singleTrade.getIsStore())) {
            trade_tab.append("门店").append(" ");
        }
        if (trade_tab.length() > 1) {
            trade_tab.deleteCharAt(trade_tab.length() - 1);
        }
        return trade_tab.toString();
    }


    /**
     * @param staff
     * @param result
     * @description: 采购退货单敏感数据增加权限控制
     * @author: tanyi
     * @date: 2025-03-11 15:00
     */
    public static void caigouReturnPowerControl(Staff staff, Map<String, Object> result) {
        if (staff == null || result == null || result.isEmpty()) {
            return;
        }
        // 采退价权限
        Boolean purchaseReturnPrivilege = DataPrivilegeFilter.hasDataPrivilegeRight(staff, PurchaseDataPrivilege.PURCHASE_RETURN_PRIVILEGE.getValue());
        // 销售价权限
        Boolean purchaseReturnSaleAmount = DataPrivilegeFilter.hasDataPrivilegeRight(staff, PurchaseDataPrivilege.PURCHASE_RETURN_SALE_AMOUNT.getValue());

        if (purchaseReturnPrivilege && purchaseReturnSaleAmount) {
            return;
        }
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String fieldName = entry.getKey();
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }
            // 没有查看采退价权限进行数据脱敏
            if (!purchaseReturnPrivilege && PURCHASE_RETURN_PRIVILEGE_LIST.contains(fieldName)) {
                result.put(fieldName, caigouReturnEncryption(entry.getValue()));
            }
            // 没有查看销售价权限进行数据脱敏
            if (!purchaseReturnSaleAmount && "table_item_selling_price".equals(fieldName)) {
                result.put(fieldName, caigouReturnEncryption(entry.getValue()));
            }

        }
    }

    /**
     * @param value
     * @return java.lang.String
     * @description: 将需要数据转换成***
     * @author: tanyi
     * @date: 2025-03-11 15:51
     */
    public static String caigouReturnEncryption(Object value) {
        if (value == null) {
            return "***";
        }
        String fieldValue = String.valueOf(value);
        try {
            if (!fieldValue.contains(",")) {
                return "***";
            }
            // 通过 Collections.nCopies 生成 "***" 组成的字符串，提高效率
            int count = StringUtils.countMatches(fieldValue, ",") + 1;
            return String.join(",", Collections.nCopies(count, "***"));
        } catch (Exception e) {
            logger.debug("采购退货单转换敏感数据错误!", e);
        }
        return fieldValue;
    }

}
package com.raycloud.dmj.services.helper;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.openapi.util.DateUtil;
import com.kuaidizs.twcn.common.util.Md5Util;
import com.raycloud.dmj.dms.domain.dto.DmsSupplierForDisConfigDto;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.caigou.PurchaseOrder;
import com.raycloud.dmj.domain.caigou.PurchaseReturn;
import com.raycloud.dmj.domain.caigou.WarehouseEntry;
import com.raycloud.dmj.domain.deliver.DeliverVo;
import com.raycloud.dmj.domain.deliver.DeliversVo;
import com.raycloud.dmj.domain.diamond.domian.TradeSourcePlatformCodeRelation;
import com.raycloud.dmj.domain.enums.ConvertQiMenSourceEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.EnumFieldValueName;
import com.raycloud.dmj.domain.pt.model.print.picker.PrintTemplateOrder;
import com.raycloud.dmj.domain.pt.model.print.picker.PrintTemplateTrade;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.Address;
import com.raycloud.dmj.domain.user.QiMenShop;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.diamond.TradeSourcePlatformCodeDiamondUtils;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.wms.LogisticsOrderDetail;
import com.raycloud.dmj.services.pt.model.ItemSupplierBridgeResult;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.tve.utils.CommaReplaceUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> huangjie
 * @Date : 2015/11/17 10:36
 * @Info :
 */
public class TVETemplateHelper {

    private static final Logger logger = Logger.getLogger(TVETemplateHelper.class);

    public static void __ifNeedSwapMobilePhone(List<String> needValues) {
        //如果需要移动电话号码,当移动电话为空座机替换
        if (needValues.contains("receiver_mobile")) {
            needValues.add("receiver_phone");
        }
        if (needValues.contains("receiver_mobile_up")) {
            needValues.add("receiver_phone_up");
        }
        if (needValues.contains("receiver_mobile_down")) {
            needValues.add("receiver_phone_down");
        }
    }

    /**
     * 商家编码替换: 如果存在规格吗优先使用规格, 否则使用商家编码
     *
     * @param needValues
     */
    public static void __ifNeedSwapItemOuterId(List<String> needValues) {
        if ((needValues.contains("item_outer_id_up")
                || needValues.contains("item_outer_id_down"))
                && !needValues.contains("item_outer_id")) {
            needValues.add("item_outer_id");
            needValues.add("item_outer_idx");
        }
    }

    /**
     * 因为收货人目的地(市或县)是需要市和县的
     *
     * @param needValues
     */
    public static void __ifNeedSwapReceiverDestination(List<String> needValues) {

        if (needValues.contains("receiver_destination_accurate")) {
            needValues.add("receiver_city");
            needValues.add("receiver_district");
        }
        if (needValues.contains("receiver_destination_accurate_up")) {
            needValues.add("receiver_city_up");
            needValues.add("receiver_district_up");
        }
        if (needValues.contains("receiver_destination_accurate_down")) {
            needValues.add("receiver_city_down");
            needValues.add("receiver_district_down");
        }
    }

    /**
     * 如果有商家编码则加上规格备注字段
     *
     * @param needValues
     */
    public static void __ifNeedItemOuterIdPlusSysSkuRemark(List<String> needValues) {
        if (needValues.contains("item_outer_id")) {
            needValues.add("sys_sku_remark");
        } else if (needValues.contains("table_item_outer_id")) {
            needValues.add("table_sys_sku_remark");
        }
    }

    /**
     * 当含有宝贝属性字段时，同时记录系统SKU规格属性别名
     *
     * @param needValues
     */
    public static void _ifNeedSwapSysSkuPropertiesAlias(List<String> needValues) {
        if (needValues.contains("item_properties")) {
            needValues.add("item_properties_alias");
        }

        if (needValues.contains("table_item_properties")) {
            needValues.add("table_item_properties_alias");
        }
    }

    public static Set<String> __addNewNeedValue(Set<String> set, Set<String> newNeedSet) {
        Set<String> newSet = new HashSet<String>();
        for (String needValue : set) {
            if (needValue.contains("-")) {
                newNeedSet.add(needValue);
                String[] newStr = needValue.split("-");
                for (String s : newStr) {
                    newSet.add(s);
                }
            }
        }

        //如果需要商家编码 ,要拉取订单out_iid,做填充补充
        if (newSet.contains("table_item_outer_id"))
            newSet.add("table_item_outer_idx");
        if (newSet.contains("item_outer_id"))
            newSet.add("item_outer_idx");


        if (set.contains("item_short_title") || set.contains("item_title")) {
            newSet.add("item_short_title");
            newSet.add("item_title");
        }
        if (set.contains("item_num")) {
            newSet.add("item_num_total");
        }
        if (set.contains("item_num") || set.contains("item_num_up") || set.contains("item_num_down")
                || set.contains("table_section_picker_num") || set.contains("table_section_num") || set.contains("table_item_storehouse")) {
            newSet.add("item_unit");
        }
        return newSet;

    }

    public static void __replaceWithPhone(Map<String, Object> resolve) {
        //电子面单、普通面单、发货单的手机号码如果没有的话使用电话号码来赋值
        if (resolve.containsKey("receiver_mobile") && resolve.containsKey("receiver_phone")) {
            if (StringUtils.isEmpty(resolve.get("receiver_mobile").toString()))
                resolve.put("receiver_mobile", resolve.get("receiver_phone").toString());
        }
        if (resolve.containsKey("receiver_mobile_down") && resolve.containsKey("receiver_phone_down")) {
            if (StringUtils.isEmpty(resolve.get("receiver_mobile_down").toString()))
                resolve.put("receiver_mobile_down", resolve.get("receiver_phone_down").toString());
        }
        if (resolve.containsKey("receiver_mobile_up") && resolve.containsKey("receiver_phone_up")) {
            if (StringUtils.isEmpty(resolve.get("receiver_mobile_up").toString()))
                resolve.put("receiver_mobile_up", resolve.get("receiver_phone_up").toString());
        }
    }

    /**
     * 初始化包裹重量，从订单中取
     *
     * @param resolve
     * @param trade
     */
    public static void initPackageWeightWithTrade(Map<String, Object> resolve, Trade trade) {
        if (!resolve.containsKey("package_weight")) {
            return;
        }

        String netWeight = TradeUtils.calculateTradeNetWeight(trade) + "";
        resolve.put("package_weight", netWeight);
    }

    public static void __replaceWithItemOuterId(Map<String, Object> resolve) {
        if (resolve.containsKey("item_outer_id") && resolve.containsKey("item_outer_idx")) {
            String[] result1 = split(resolve.get("item_outer_id").toString(), ',', true);
            String[] result2 = split(resolve.get("item_outer_idx").toString(), ',', true);
//            logger.debug("商家编码替换" + JSON.toJSONString(result1) + JSON.toJSONString(result2));
            if (result1.length == 0 && result2.length == 1) {
                resolve.put("item_outer_id", StringUtils.join(result2, ","));
            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if ((StringUtils.isEmpty(result1[i]) || result1[i].equals("null")) && i < result2.length && result2.length > 0) {
                        result1[i] = result2[i];
                    }
                }
                resolve.put("item_outer_id", StringUtils.join(result1, ","));
            }
        }
    }

    /**
     * 初始化商品或规则备注
     *
     * @param singleTrade
     * @param resolve
     */
    public static void __initTableSysSkuRemarkAndItemRemack(Trade singleTrade, Map<String, Object> resolve) {

        boolean hasSysSkuRemark = resolve.containsKey("table_sys_sku_remark");
        boolean hasItemRemark = resolve.containsKey("table_item_remark");
        if (!(hasSysSkuRemark || hasItemRemark)) {
            return;
        }
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        StringBuilder sbSysSkuRemark = new StringBuilder();
        StringBuilder sbItemRemark = new StringBuilder();
        for (Order order : orderList) {
            String sysItemRemark = StringUtils.defaultIfEmpty(order.getSysItemRemark(), "");
            String sysSkuRemark = StringUtils.isEmpty(order.getSysSkuRemark()) ? sysItemRemark : order.getSysSkuRemark();
            if (hasSysSkuRemark) {
                sbSysSkuRemark.append(CommaReplaceUtils.commaReplace(sysSkuRemark)).append(",");
            }
            if (hasItemRemark) {
                sbItemRemark.append(CommaReplaceUtils.commaReplace(sysItemRemark)).append(",");
            }
        }

        if (sbSysSkuRemark.length() > 0) {
            sbSysSkuRemark.deleteCharAt(sbSysSkuRemark.length() - 1);
        }
        if (sbItemRemark.length() > 0) {
            sbItemRemark.deleteCharAt(sbItemRemark.length() - 1);
        }
        if (hasSysSkuRemark) {
            resolve.put("table_sys_sku_remark", sbSysSkuRemark.toString());
        }
        if (hasItemRemark) {
            resolve.put("table_item_remark", sbItemRemark.toString());
        }
    }

    public static void __initTableSysSkuRemarkAndItemRemack(PrintTemplateTrade signalTrade, Map<String, Object> resolve) {

        boolean hasSysSkuRemark = resolve.containsKey("table_sys_sku_remark");
        boolean hasItemRemark = resolve.containsKey("table_item_remark");
        if (!(hasSysSkuRemark || hasItemRemark)) {
            return;
        }
        if (signalTrade == null) {
            return;
        }
        List<PrintTemplateOrder> orderList = signalTrade.getOrders();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        StringBuilder sbSysSkuRemark = new StringBuilder();
        StringBuilder sbItemRemark = new StringBuilder();
        for (PrintTemplateOrder order : orderList) {
            if (hasSysSkuRemark) {
                sbSysSkuRemark.append(StringUtils.defaultIfEmpty(CommaReplaceUtils.commaReplace(order.getSysSkuRemark()), "")).append(",");
            }
            if (hasItemRemark) {
                sbItemRemark.append(StringUtils.defaultIfEmpty(CommaReplaceUtils.commaReplace(order.getSysItemRemark()), "")).append(",");
            }
        }

        if (sbSysSkuRemark.length() > 0) {
            sbSysSkuRemark.deleteCharAt(sbSysSkuRemark.length() - 1);
        }
        if (sbItemRemark.length() > 0) {
            sbItemRemark.deleteCharAt(sbItemRemark.length() - 1);
        }
        if (hasSysSkuRemark) {
            resolve.put("table_sys_sku_remark", sbSysSkuRemark.toString());
        }
        if (hasItemRemark) {
            resolve.put("table_item_remark", sbItemRemark.toString());
        }
    }

    /**
     * 初始化商家编码和规格备注
     *
     * @param singleTrade
     * @param resolve
     */
    public static void __initItemOuterIdAndSysSkuRemark(Trade singleTrade, Map<String, Object> resolve, String resolveKey) {
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            sb.append(getSysOuterIdAndRemark(order)).append(",");
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put(resolveKey, sb.toString());
    }

    public static void __initItemUnit(PrintTemplateTrade signalTrade, Map<String, Object> resolve, String resolveKey) {
        if (!resolve.containsKey(resolveKey)) {
            return;
        }
        if (signalTrade == null) {
            return;
        }
        List<PrintTemplateOrder> orderList = signalTrade.getOrders();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (PrintTemplateOrder order : orderList) {
            //纯商品的话商品单位取sku单位
            if (order.getSkuSysId() != null && order.getSkuSysId() == -1) {
                if (StringUtils.isEmpty(order.getItemUnit())) {
                    sb.append(StringUtils.defaultIfEmpty(order.getSkuUnit(), "")).append(",");
                } else {
                    sb.append(StringUtils.defaultIfEmpty(order.getItemUnit(), "")).append(",");
                }
            } else {
                sb.append(StringUtils.defaultIfEmpty(order.getItemUnit(), "")).append(",");
            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put(resolveKey, sb.toString());
    }

    public static void __initItemUnit(Trade singleTrade, Map<String, Object> resolve, String resolveKey) {
        if (!resolve.containsKey(resolveKey)) {
            return;
        }
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            //纯商品的话商品单位取sku单位
            if (order.getSkuSysId() != null && order.getSkuSysId() == -1) {
                if (StringUtils.isEmpty(order.getUnit())) {
                    sb.append(StringUtils.defaultIfEmpty(order.getSkuUnit(), "")).append(",");
                } else {
                    sb.append(StringUtils.defaultIfEmpty(order.getUnit(), "")).append(",");
                }
            } else {
                sb.append(StringUtils.defaultIfEmpty(order.getUnit(), "")).append(",");
            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put(resolveKey, sb.toString());
    }

    public static String getSysOuterIdAndRemark(Order order) {
        StringBuilder sb = new StringBuilder();
        String sysOuterId = StringUtils.defaultIfEmpty(order.getSysOuterId(), "");
        sb.append(CommaReplaceUtils.commaReplace(sysOuterId));

        String sysRemark = OrderUtils.getSysRemark(order);
        if (StringUtils.isNotEmpty(sysRemark)) {
            sb.append("(").append(CommaReplaceUtils.commaReplace(sysRemark)).append(")");
        }

        return sb.toString();
    }

    public static String getSysOuterIdAndRemark(PrintTemplateOrder order) {
        StringBuilder sb = new StringBuilder();
        String sysOuterId = StringUtils.defaultIfEmpty(order.getOuterSkuId(), "");
        sb.append(sysOuterId);

//        String sysRemark = order.getSkuSysId().longValue() <= 0 ? order.getSysItemRemark() : order.getSysSkuRemark();
//        if(StringUtils.isNotEmpty(sysRemark)){
//            sb.append("(").append(sysRemark).append(")");
//        }

        return sb.toString();
    }

    public static void __initItemOuterIdAndSysSkuRemark(Trade singleTrade, Map<String, Object> resolve) {
        __initItemOuterIdAndSysSkuRemark(singleTrade, resolve, "item_outer_id");
    }


    /**
     * 当含有宝贝属性别名时，替换掉宝贝属性
     *
     * @param resolve
     */
    public static void _replaceWithSysSkuPropertiesName(Map<String, Object> resolve) {
        if (resolve.containsKey("item_properties")) {
            String[] result1 = split(resolve.get("item_properties").toString(), ',', true);
            String[] result2 = split(resolve.get("item_properties_alias").toString(), ',', true);
            if (result2.length == 0 && result1.length == 1) {
                resolve.put("item_properties", StringUtils.join(result1, ","));
            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if (StringUtils.isEmpty(result2[i]) && i < result2.length && result2.length > 0) {
                        result2[i] = result1[i];
                    }
                }
                resolve.put("item_properties", StringUtils.join(result2, ","));
            }
        }

        if (resolve.containsKey("table_item_properties")) {
            String[] result1 = split(resolve.get("table_item_properties").toString(), ',', true);
            String[] result2 = split(resolve.get("table_item_properties_alias").toString(), ',', true);
//            if (logger.isDebugEnabled()) {
//                logger.debug("table_item_properties=" + StringUtils.join(result1, ",") + "|| table_item_properties_alias=" + StringUtils.join(result2, ","));
//            }
            if (result2.length == 0 && result1.length == 1) {
                resolve.put("table_item_properties", StringUtils.join(result1, ","));
            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if (StringUtils.isEmpty(result2[i]) && i < result2.length && result2.length > 0) {
                        result2[i] = result1[i];
                    }
                }
                resolve.put("table_item_properties", StringUtils.join(result2, ","));
            }
        }
    }

    /**
     * @param value
     * @param splitChar
     * @param ignoreEmpty
     * @param return1LengthArrWhenEmpty value为空字符串时，是否返回一个长度为1的空字符串数组
     * @return
     */
    public static String[] split(String value, char splitChar, boolean ignoreEmpty, boolean return1LengthArrWhenEmpty) {
        if (null == value) {
            return new String[0];
        }
        if ("".equals(value)) {
            if (return1LengthArrWhenEmpty) {
                return new String[]{""};
            } else {
                return new String[0];
            }
        }

        if (!ignoreEmpty)
            return value.split(String.valueOf(splitChar));

        List<String> matchList = new ArrayList<String>();
        int index = 0;
        while (index <= value.length()) {
            int nextIndex = value.indexOf(splitChar, index);
            if (nextIndex == -1 && index == 0)
                return new String[]{value};
            if (nextIndex == -1) {
                if (index <= value.length()) {
                    matchList.add(value.substring(index));
                }
                break;
            }
            matchList.add(value.substring(index, nextIndex));
            index = nextIndex + 1;
        }

        return matchList.toArray(new String[matchList.size()]);
    }

    public static String[] split(String value, char splitChar, boolean ignoreEmpty) {
        return split(value, splitChar, ignoreEmpty, false);
    }

    public static void __replaceWithCombinFiled(Set<String> forNeedSet, Set<String> newSet, Map<String, Object> resolve, String splitStr, boolean needOrderNum, boolean needSubStr, ReplaceWithCombineFieldParams params) {
        if (StringUtils.isNotBlank(splitStr)) {
            splitStr = ",";
        }
        //这里对一些有 - 连接符的 字符做特殊处理
        //组合选项  如:    num-shortTitle 字段
        if (newSet.size() != 0) {
            for (String str : forNeedSet) {
                String[] newStr = str.split("-");
                Map<String, String[]> data = new HashMap<String, String[]>();
                Integer length = null;
                for (String key : newStr) {
                    if (resolve.get(key) != null && !resolve.get(key).toString().equals("")) {
                        String[] __str = split(resolve.get(key).toString(), ',', true);
                        if (length == null && __str.length != 0) {
                            length = __str.length;
                            data.put(key, __str);
                        } else if (length != null && length == __str.length) {
                            data.put(key, __str);
                        }
                    }
                    if (key.equals("item_num") || key.equals("item_num_up") || key.equals("item_num_down")) {
                        data.put("item_unit", split(resolve.get("item_unit").toString(), ',', true, true));
                    }
                    if (key.equals("item_line_break") && length != null) {
                        data.put("item_line_break", Arrays.stream(new String[length]).map(s -> "\n").toArray(String[]::new));
                        // 将换行符存入 resolve 中，不被 PrintTemplateHelper.prettifyTableSectionNum 影响
                        resolve.put("item_line_break", "\n");
                    }
                }

                if (data.size() != 0 && length != null) {
                    List<String> newValue = new LinkedList<String>();
                    Integer point = 0;
                    while (point < length) {
                        List<String> value = new LinkedList<String>();
                        Integer tr = point + 1;
                        if (needOrderNum) {
                            value.add(Numeric2ChineseStr.toDo(tr.toString()) + ":");
                        }
                        for (String key : newStr) {
                            if (data.containsKey(key)) {
                                String subStr;
//                                if (needSubStr
//                                        && (key.contains("properties") || key.contains("specification"))
//                                        && data.get(key)[point].indexOf(":") > 0 && data.get(key)[point].indexOf(":") + 1 <= data
//                                        .get(key)[point].length()) {
//                                    subStr = __formatItemProperites(data.get(key)[point]);
//                                } else {
//                                    subStr = data.get(key)[point];
//                                }
                                subStr = data.get(key)[point];
                                if (key.equals("item_num")
                                        || key.equals("item_num_up")
                                        || key.equals("item_num_down")
                                ) {
                                    String printItemUnit = null != params ? params.getPrintItemUnit() : "";
                                    String itemNumSelect = null != params ? params.getItemNumSelect() : "";
                                    String start = "【";
                                    String end = "】";
                                    if (Objects.equals("2", itemNumSelect)) {
                                        start = "*";
                                        end = "";
                                    }
                                    if (StringUtils.isBlank(printItemUnit)) {
                                        value.add(start + subStr + "件" + end);
                                    } else {
                                        value.add(start + subStr + data.get("item_unit")[point] + end);
                                    }
                                } else {
                                    if (StringUtils.isNotEmpty(subStr) && !"null".equals(subStr)) {
                                        value.add(Objects.equals("\n", subStr) ? "\n" : subStr.trim());
                                    }
                                }
                            }
                        }
                        newValue.add(getSplitValue(value));
                        point++;
                    }
                    resolve.put(str, StringUtils.join(newValue, splitStr));
                }
            }

        }
    }

    public static String __formatItemProperites(String str) {
        String subStr = "";
        String[] subStrArr = StringUtils.split(str, ";");
        if (subStrArr != null) {
            for (String s : subStrArr) {
                if (StringUtils.isNotEmpty(s) && str.indexOf(":") + 1 < str.length())
                    subStr += s.substring(s.indexOf(":") + 1);
                else subStr += s;
            }
        } else {
            subStr = str;
        }
        return subStr;
    }

    public static void __replaceWithCustomFiled(Map<String, Object> resolve, Object[] objects) {
        /**
         * Auth:hj,填充自定义内容
         */
        UserExpressTemplate userExpressTemplate = null;
        for (Object object : objects) {
            if (object instanceof UserExpressTemplate) {
                userExpressTemplate = (UserExpressTemplate) object;
                break;
            }
        }
        if (userExpressTemplate != null) {
            //开始填充自定义内容1~5
            List<FieldSettings> fieldSettingses = userExpressTemplate.getFieldSettings();
            if (fieldSettingses != null) {
                for (FieldSettings fieldSettings : fieldSettingses) {
                    String field = fieldSettings.getField();
                    if ("custom_content1".equals(field)
                            || "custom_content2".equals(field)
                            || "custom_content3".equals(field)
                            || "custom_content4".equals(field)
                            || "custom_content5".equals(field)
                            || "custom_content6".equals(field)
                            || "custom_content7".equals(field)
                            || "custom_content8".equals(field)
                            || "custom_content9".equals(field)
                            || "custom_content10".equals(field)
                    ) {
                        String content = fieldSettings.getContent();
                        resolve.put(field, com.raycloud.dmj.tve.utils.StringUtils.nvl(content));
                    }
                }
            }
        }
    }

    public static void __needSwapFields(List<String> needValues) {
        if (needValues.contains("table_item_outer_id"))
            needValues.add("table_item_outer_idx");

        //子订单优惠金额=单价*数量-实付
        if (needValues.contains("table_item_discount")) {
            needValues.add("table_item_num");
            needValues.add("table_item_price");
            needValues.add("table_item_payment");
        }
    }

    /**
     * 折扣金额计算
     *
     * @param offsetNumMap
     * @param needValues
     * @param resolve
     */
    public static void __replaceWithSubDiscount(Map<String, Double> offsetNumMap, List<String> needValues, Map<String, Object> resolve) {
        DecimalFormat currency = new DecimalFormat("0.00");

        //子订单优惠金额=单价*数量-实付   PS: 如果是单笔子订单,要把邮费加进来!!!  QAQ
        if (needValues.contains("table_item_discount")) {
            if (resolve.containsKey("table_item_num") && resolve.containsKey("table_item_price") && resolve.containsKey(
                    "table_item_payment")) {
                String[] result1 = split(resolve.get("table_item_num").toString(), ',', true);
                String[] result2 = split(resolve.get("table_item_price").toString(), ',', true);
                String[] result3 = split(resolve.get("table_item_payment").toString(), ',', true);
                String[] result4 = null;
                int result4Len = 0;

                if (resolve.containsKey("table_item_oid")) {
                    result4 = split(resolve.get("table_item_oid").toString(), ',', true);
                    if (result4 != null) {
                        result4Len = result4.length;
                    }
                }
                if (result1.length == result2.length && result2.length == result3.length) {
                    for (int i = 0; i < result1.length; i++) {
                        BigDecimal count = new BigDecimal(result1[i]);
                        BigDecimal price = new BigDecimal(result2[i]);
                        BigDecimal payment = new BigDecimal(result3[i]);
                        BigDecimal result = price.multiply(count).subtract(payment);
                        //如果是订单子订单是单笔:
                        //  1.邮费要是要加回优惠总额
                        //  2.邮费要从实付金额进去
                        if (result4 != null && (result4Len - 1) >= i && !StringUtils.isEmpty(result4[i]) && offsetNumMap.containsKey(result4[i])) {
                            result = result.add(new BigDecimal(offsetNumMap.get(result4[i])));
                            //实付总额把邮费减去
                            result3[i] = currency.format(new BigDecimal(result3[i]).subtract(
                                    new BigDecimal(offsetNumMap.get(result4[i]))));
                        }
                        result1[i] = currency.format(result);
                    }
                    resolve.put("table_item_discount", StringUtils.join(result1, ","));
                    resolve.put("table_item_payment", StringUtils.join(result3, ","));
                }
            }
        }
    }

    public static void __replaceWithOuterIds(Map<String, Object> resolve) {
        //商家编码替换规则,  优先使用 sku商家编码,没有则用 商品商家编码替换
        if (resolve.containsKey("table_item_outer_id") && resolve.containsKey("table_item_outer_idx")) {
            String[] result1 = split(resolve.get("table_item_outer_id").toString(), ',', true);
            String[] result2 = split(resolve.get("table_item_outer_idx").toString(), ',', true);
            if (result1.length == 0 && result2.length == 1) {
                resolve.put("table_item_outer_id", StringUtils.join(result2, ","));
            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    try {
                        if (StringUtils.isEmpty(result1[i]) || result1[i].equals("null")) {
                            result1[i] = result2[i];
                        }
                    } catch (ArrayIndexOutOfBoundsException e) {
                        logger.error("数组越界[" + JSON.toJSONString(result1) + "][" + JSON.toJSONString(result2) + "]", e);
                    }
                }
                resolve.put("table_item_outer_id", StringUtils.join(result1, ","));
            }
        }
    }

    public static void __initId(Map<String, Object> resolve, Object[] objects) {
        //前端 序号
        int count = 0;
        for (Object object : objects) {
            if (object instanceof TbTrade) {
                count = ((TbTrade) object).getOrders().size();
            } else if (object instanceof PrintTemplateTrade) {
                count = ((PrintTemplateTrade) object).getOrders().size();
            } else if (object instanceof PurchaseOrder) {
                if (((PurchaseOrder) object).getDetails() != null) {
                    count = ((PurchaseOrder) object).getDetails().size();
                }
            } else if (object instanceof PurchaseReturn) {
                if (((PurchaseReturn) object).getDetails() != null) {
                    count = ((PurchaseReturn) object).getDetails().size();
                }
            } else if (object instanceof WarehouseEntry) {
                if (((WarehouseEntry) object).getDetails() != null) {
                    count = ((WarehouseEntry) object).getDetails().size();
                }
            }
        }

        addTableItemId(resolve, count);
    }

    /**
     * @param resolve
     * @param count
     * @description: 组装序号
     * @author: tanyi
     * @date: 2024-12-24 11:17
     */
    public static void addTableItemId(Map<String, Object> resolve, Integer count) {
        StringBuilder counts = new StringBuilder();
        for (int i = 0; i < count; i++) {
            counts.append(i + 1);
            if (i != count - 1) {
                counts.append(",");
            }
        }
        resolve.put("table_item_id", counts);
    }

    public static void setItemShortTitle4Item(Map<String, Object> resolve) {

    }

    public static void __replaceWithShortItem(Map<String, Object> resolve) {
        if (resolve.containsKey("item_title") && resolve.containsKey("item_short_title")) {
            String[] result1 = split(resolve.get("item_title").toString(), ',', true);
            String[] result2 = split(resolve.get("item_short_title").toString(), ',', true);
            if (result2.length == 0 && result1.length == 1) {
                resolve.put("item_short_title", StringUtils.join(result1, ","));
                resolve.put("item_short_title_up", StringUtils.join(result1, ","));
                resolve.put("item_short_title_down", StringUtils.join(result1, ","));

            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if (StringUtils.isBlank(result2[i]) && i < result2.length && result2.length > 0) {
                        result2[i] = result1[i];
                    }
                }
                resolve.put("item_short_title", StringUtils.join(result2, ","));
                resolve.put("item_short_title_up", StringUtils.join(result2, ","));
                resolve.put("item_short_title_down", StringUtils.join(result2, ","));
            }

        } else if (resolve.containsKey("table_item_short_title") && resolve.containsKey("table_item_title")) {
            String[] result1 = split(resolve.get("table_item_title").toString(), ',', true);
            String[] result2 = split(resolve.get("table_item_short_title").toString(), ',', true);
            if (result2.length == 0 && result1.length == 1) {
                resolve.put("table_item_short_title", StringUtils.join(result1, ","));

            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if (StringUtils.isBlank(result2[i]) && i < result2.length && result2.length > 0) {
                        result2[i] = result1[i];
                    }
                }
                resolve.put("table_item_short_title", StringUtils.join(result2, ","));
            }

        }
    }

    public static void __replaceWithReceiverDestinationAccurate(List<String> needValues, Map<String, Object> resolve) {
        if (needValues.contains("receiver_destination_accurate")
                && resolve.containsKey("receiver_city")
                && resolve.containsKey("receiver_district")) {

            if (resolve.get("receiver_district").toString().contains("县")) {
                resolve.put("receiver_destination_accurate", resolve.get("receiver_district"));
            } else {
                resolve.put("receiver_destination_accurate", resolve.get("receiver_city"));
            }

        }
        if (needValues.contains("receiver_destination_accurate_up")
                && resolve.containsKey("receiver_city_up")
                && resolve.containsKey("receiver_district_up")) {

            if (resolve.get("receiver_district_up").toString().contains("县")) {
                resolve.put("receiver_destination_accurate_up", resolve.get("receiver_district_up"));
            } else {
                resolve.put("receiver_destination_accurate_up", resolve.get("receiver_city_up"));
            }

        }
        if (needValues.contains("receiver_destination_accurate_down")
                && resolve.containsKey("receiver_city_down")
                && resolve.containsKey("receiver_district_down")) {

            if (resolve.get("receiver_district_up").toString().contains("县")) {
                resolve.put("receiver_destination_accurate_down", resolve.get("receiver_district_down"));
            } else {
                resolve.put("receiver_destination_accurate_down", resolve.get("receiver_city_down"));
            }

        }
    }

    public static void __replaceWithSysSkuPropertiesName(List<String> needValues, Map<String, Object> resolve, Trade singleTrade, Map<String, DmjSku> dmjSkuMap) {
        if (!needValues.contains("item_properties")) {
            return;
        }

        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            resolve.put("item_properties", "");
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            if (StringUtils.isNotBlank(order.getSysSkuPropertiesAlias())) {
                sb.append(CommaReplaceUtils.commaReplace(order.getSysSkuPropertiesAlias())).append(",");
            } else if (StringUtils.isNotBlank(order.getSysSkuPropertiesName())) {
                sb.append(CommaReplaceUtils.commaReplace(order.getSysSkuPropertiesName())).append(",");
            } else {
                String key = order.getSkuSysId() == null || order.getSkuSysId() < 0 ? "0" : order.getSkuSysId().toString();
                DmjSku sku = dmjSkuMap.get(key);
                if (sku != null) {
                    String propertiesAlias = StringUtils.isNotBlank(sku.getPropertiesAlias()) ? sku.getPropertiesAlias() : sku.getPropertiesName();
                    sb.append(StringUtils.isNotBlank(propertiesAlias) ? propertiesAlias : "").append(",");
                } else {
                    sb.append(",");
                }
            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        resolve.put("item_properties", sb.toString());
    }

    public static void parseFxConfig(Map<String, Object> resolve, DmsSupplierForDisConfigDto dmsSupplierForDisConfigDto) {
        // 分销配置信息
        String fxConfig = dmsSupplierForDisConfigDto.getConfig();

        // 分销发货人姓名
        if (FxConfigHelper.needName(fxConfig)) {
            resolve.put("send_name", dmsSupplierForDisConfigDto.getContactName());
            resolve.put("send_name_up", dmsSupplierForDisConfigDto.getContactName());
            resolve.put("send_name_down", dmsSupplierForDisConfigDto.getContactName());
        }
        // 分销发货人联系方式
        if (FxConfigHelper.needContactInfo(fxConfig)) {
            resolve.put("send_mobile", dmsSupplierForDisConfigDto.getContactPhone());
            resolve.put("send_mobile_up", dmsSupplierForDisConfigDto.getContactPhone());
            resolve.put("send_mobile_down", dmsSupplierForDisConfigDto.getContactPhone());
            resolve.put("send_phone", dmsSupplierForDisConfigDto.getContactTelephone());
            resolve.put("send_phone_up", dmsSupplierForDisConfigDto.getContactTelephone());
            resolve.put("send_phone_down", dmsSupplierForDisConfigDto.getContactTelephone());
        }
        // 分销发货人地址
        if (FxConfigHelper.needAddress(fxConfig)) {
            resolve.put("send_state", dmsSupplierForDisConfigDto.getContactProvince());
            resolve.put("send_state_up", dmsSupplierForDisConfigDto.getContactProvince());
            resolve.put("send_state_down", dmsSupplierForDisConfigDto.getContactProvince());
            resolve.put("send_city", dmsSupplierForDisConfigDto.getContactCity());
            resolve.put("send_city_up", dmsSupplierForDisConfigDto.getContactCity());
            resolve.put("send_city_down", dmsSupplierForDisConfigDto.getContactCity());
            resolve.put("send_district", dmsSupplierForDisConfigDto.getContactDistrict());
            resolve.put("send_district_up", dmsSupplierForDisConfigDto.getContactDistrict());
            resolve.put("send_district_down", dmsSupplierForDisConfigDto.getContactDistrict());
            resolve.put("send_address", dmsSupplierForDisConfigDto.getContactDetailAddress());
            resolve.put("send_address_up", dmsSupplierForDisConfigDto.getContactDetailAddress());
            resolve.put("send_address_down", dmsSupplierForDisConfigDto.getContactDetailAddress());
        }
    }

    /**
     * 组装分销商打印地址
     */
    public static void buildDisAddressMsg(Map<String, Object> resolve, DmsSupplierForDisConfigDto dmsSupplierForDisConfigDto) {
        Address address = new Address();
        address.setCity(dmsSupplierForDisConfigDto.getContactCity());//市
        address.setContactName(dmsSupplierForDisConfigDto.getContactName());//发件人
        address.setMobilePhone(dmsSupplierForDisConfigDto.getContactPhone());//发件人手机
        address.setPhone(dmsSupplierForDisConfigDto.getContactTelephone());//发件人电话
        address.setProvince(dmsSupplierForDisConfigDto.getContactProvince());//省
        address.setCountry(dmsSupplierForDisConfigDto.getContactDistrict());//区
        address.setAddr(dmsSupplierForDisConfigDto.getContactDetailAddress());//详细地址
        buileResultAddress(resolve, address);
    }

    public static void buileResultAddress(Map<String, Object> resolve, Address address) {
        resolve.put("originating", getValue(address.getCity()));
        resolve.put("send_name", getValue(address.getContactName()));
        resolve.put("send_name_up", getValue(address.getContactName()));
        resolve.put("send_name_down", getValue(address.getContactName()));
        resolve.put("send_mobile", getValue(address.getMobilePhone()));
        resolve.put("send_mobile_up", getValue(address.getMobilePhone()));
        resolve.put("send_mobile_down", getValue(address.getMobilePhone()));
        resolve.put("send_phone", getValue(address.getPhone()));
        resolve.put("send_phone_up", getValue(address.getPhone()));
        resolve.put("send_phone_down", getValue(address.getPhone()));
        resolve.put("send_state", getValue(address.getProvince()));
        resolve.put("send_state_up", getValue(address.getProvince()));
        resolve.put("send_state_down", getValue(address.getProvince()));
        resolve.put("send_city", getValue(address.getCity()));
        resolve.put("send_city_up", getValue(address.getCity()));
        resolve.put("send_city_down", getValue(address.getCity()));
        resolve.put("send_district", getValue(address.getCountry()));
        resolve.put("send_district_up", getValue(address.getCountry()));
        resolve.put("send_district_down", getValue(address.getCountry()));
        resolve.put("send_address", getValue(address.getAddr()));
        resolve.put("send_address_up", getValue(address.getAddr()));
        resolve.put("send_address_down", getValue(address.getAddr()));
        StringBuffer send_state_city_district_1 = new StringBuffer()
                .append(getValue(address.getProvince()))
                .append(getValue(address.getCity()))
                .append(getValue(address.getCountry()));
        resolve.put("send_state_city_district", send_state_city_district_1);
        resolve.put("send_state_city_district_up", send_state_city_district_1);
        resolve.put("send_state_city_district_down", send_state_city_district_1);
        StringBuffer send_detail_address_1 = new StringBuffer()
                .append(getValue(address.getProvince()))
                .append(getValue(address.getCity()))
                .append(getValue(address.getCountry()))
                .append(getValue(address.getAddr()));
        resolve.put("send_detail_address", send_detail_address_1);
        resolve.put("send_detail_address_up", send_detail_address_1);
        resolve.put("send_detail_address_down", send_detail_address_1);
        StringBuffer send_city_district_1 = new StringBuffer()
                .append(null != address.getCity() ? address.getCity() : "")
                .append(null != address.getCountry() ? address.getCountry() : "");
        resolve.put("send_city_district", send_city_district_1);
        resolve.put("send_city_district_up", send_city_district_1);
        resolve.put("send_city_district_down", send_city_district_1);
    }

    /**
     * 判断是否隐藏寄件人电话或地址
     *
     * @param resolve
     * @param userWlbExpressTemplate
     */
    public static void hideSendPhoneOrAddress(Map<String, Object> resolve, UserWlbExpressTemplate userWlbExpressTemplate) {
        boolean hideSendAddress = "1".equals(PrintTemplateHelper.getFieldValueByKey(userWlbExpressTemplate, EnumFieldValueName.HIDE_SENDER_ADDRESS.getValue()));
        boolean hideSendPhone = "1".equals(PrintTemplateHelper.getFieldValueByKey(userWlbExpressTemplate, EnumFieldValueName.HIDE_SENDER_PHONE.getValue()));
        //清空寄件人电话信息
        if (hideSendPhone) {
            resolve.put("send_mobile", "***");
            resolve.put("send_mobile_up", "***");
            resolve.put("send_mobile_down", "***");
            resolve.put("send_phone", "***");
            resolve.put("send_phone_up", "***");
            resolve.put("send_phone_down", "***");
        }
        //清空寄件人地址信息
        if (hideSendAddress) {
            resolve.put("send_state", "***");
            resolve.put("send_state_up", "***");
            resolve.put("send_state_down", "***");
            resolve.put("send_city", "***");
            resolve.put("send_city_up", "***");
            resolve.put("send_city_down", "***");
            resolve.put("send_district", "***");
            resolve.put("send_district_up", "***");
            resolve.put("send_district_down", "***");
            resolve.put("send_address", "***");
            resolve.put("send_address_up", "***");
            resolve.put("send_address_down", "***");
            resolve.put("send_state_city_district", "***");
            resolve.put("send_state_city_district_up", "***");
            resolve.put("send_state_city_district_down", "***");
            resolve.put("send_detail_address", "***");
            resolve.put("send_detail_address_up", "***");
            resolve.put("send_detail_address_down", "***");
            resolve.put("send_city_district", "***");
            resolve.put("send_city_district_up", "***");
            resolve.put("send_city_district_down", "***");
        }
    }

    public static void __replaceWlbWithSendInfo(Staff staff, Map<String, Object> resolve, Warehouse warehouse, Shop shop,
                                                String printAddressSelect, UserWlbExpressTemplate userWlbExpressTemplate, Trade trade, IShopService shopService) {

        if (!"2".equals(printAddressSelect) && !"3".equals(printAddressSelect)) {
            if (StringUtils.isNotEmpty(warehouse.getState()) || StringUtils.isNotEmpty(warehouse.getAddress())) {
                return;
            }
        }
        //使用自定义地址
        if ("3".equals(printAddressSelect)) {
            String customAddress = PrintTemplateHelper.getFieldValueByKey(userWlbExpressTemplate, EnumFieldValueName.PRINT_CUSTOM_SEND_ADDRESS.getValue());
            if (StringUtils.isEmpty(customAddress)) {
                return;
            }
            Address address = JSONObject.parseObject(customAddress, Address.class);
            if (null == address) {
                return;
            }
            buileResultAddress(resolve, address);
            return;
        }
        if (shop == null || (StringUtils.isEmpty(shop.getSendAddress()) && StringUtils.isEmpty(shop.getSendState()))) {
            return;
        }
        String sendCity = shop.getSendCity();
        String sendContact = shop.getSendContact();
        String sendMobile = shop.getSendMobile();
        String sendPhone = shop.getSendPhone();
        String sendState = shop.getSendState();
        String sendDistrict = shop.getSendDistrict();
        String sendAddress = shop.getSendAddress();

        if (CommonConstants.QIMEN_DISTRIBUTOR.equals(trade.getSource()) && ConvertQiMenSourceEnum.isQiMenAll(trade.getSubSource())) {
            String sellerNick = trade.getSellerNick();
            if (StringUtils.isNotEmpty(sellerNick)) {
                // 奇门新逻辑，调用店铺那边提供的方法获取id
                QiMenShop qiMenShop = shopService.getQiMenShop(staff, trade.getUserId(), sellerNick, trade.getSubSource());
                if (Objects.nonNull(qiMenShop) && qiMenShop.getAddressFlag().equals(1) && CollectionUtils.isNotEmpty(qiMenShop.getSendAddressList())) {
                    List<Address> sendAddressList = qiMenShop.getSendAddressList().stream()
                            .filter(send -> send.getType().equals(1))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(sendAddressList)) {
                        Address address = sendAddressList.stream()
                                .filter(send -> send.getSendDef().equals(1))
                                .findFirst()
                                .orElse(null);
                        if (Objects.isNull(address)) {
                            address = qiMenShop.getSendAddressList().get(0);
                        }
                        sendCity = address.getCity();
                        sendContact = address.getContactName();
                        sendMobile = address.getMobilePhone();
                        sendPhone = address.getPhone();
                        sendState = address.getProvince();
                        sendDistrict = address.getCountry();
                        sendAddress = address.getAddr();
                    }
                }
            }
        }

        resolve.put("originating", getValue(sendCity));
        resolve.put("send_name", getValue(sendContact));
        resolve.put("send_name_up", getValue(sendContact));
        resolve.put("send_name_down", getValue(sendContact));
        resolve.put("send_mobile", getValue(sendMobile));
        resolve.put("send_mobile_up", getValue(sendMobile));
        resolve.put("send_mobile_down", getValue(sendMobile));
        resolve.put("send_phone", getValue(sendPhone));
        resolve.put("send_phone_up", getValue(sendPhone));
        resolve.put("send_phone_down", getValue(sendPhone));
        resolve.put("send_state", getValue(sendState));
        resolve.put("send_state_up", getValue(sendState));
        resolve.put("send_state_down", getValue(sendState));
        resolve.put("send_city", getValue(sendCity));
        resolve.put("send_city_up", getValue(sendCity));
        resolve.put("send_city_down", getValue(sendCity));
        resolve.put("send_district", getValue(sendDistrict));
        resolve.put("send_district_up", getValue(sendDistrict));
        resolve.put("send_district_down", getValue(sendDistrict));
        resolve.put("send_address", getValue(sendAddress));
        resolve.put("send_address_up", getValue(sendAddress));
        resolve.put("send_address_down", getValue(sendAddress));
        StringBuffer send_state_city_district = new StringBuffer()
                .append(getValue(sendState))
                .append(getValue(sendCity))
                .append(getValue(sendDistrict));
        resolve.put("send_state_city_district", send_state_city_district);
        resolve.put("send_state_city_district_up", send_state_city_district);
        resolve.put("send_state_city_district_down", send_state_city_district);
        StringBuffer send_detail_address = new StringBuffer()
                .append(getValue(sendState))
                .append(getValue(sendCity))
                .append(getValue(sendDistrict))
                .append(getValue(sendAddress));
        resolve.put("send_detail_address", send_detail_address);
        resolve.put("send_detail_address_up", send_detail_address);
        resolve.put("send_detail_address_down", send_detail_address);
        StringBuffer send_city_district = new StringBuffer()
                .append(null != sendCity ? sendCity : "")
                .append(null != sendDistrict ? sendDistrict : "");
        resolve.put("send_city_district", send_city_district);
        resolve.put("send_city_district_up", send_city_district);
        resolve.put("send_city_district_down", send_city_district);

    }

    public static void __replaceQimenShopTitle(Staff staff, Map<String, Object> resolve, Shop shop, Trade trade) {
        if (!CommonConstants.QIMEN_DISTRIBUTOR.equals(trade.getSource()) || !ConvertQiMenSourceEnum.isQiMenAll(trade.getSubSource())) {
            return;
        }
        if (resolve.containsKey("shop_title")) {
            TradeSourcePlatformCodeRelation relation = TradeSourcePlatformCodeDiamondUtils.getPlatformBySource(trade.getSubSource(), "qimen");
            resolve.put("shop_title", relation.getName() + "-" + shop.getTitle() + "-" + trade.getSellerNick());
        }
    }

    private static String getValue(String str) {
        if (str == null) {
            return "";
        } else {
            return str;
        }
    }

    public static void __replaceExpressWithSendInfo(Map<String, Object> resolve, Warehouse warehouse, Shop shop,
                                                    String printAddressSelect, UserExpressTemplate userExpressTemplate) {

        if (!"2".equals(printAddressSelect) && !"3".equals(printAddressSelect)) {
            if (StringUtils.isNotEmpty(warehouse.getState()) || StringUtils.isNotEmpty(warehouse.getAddress())) {
                return;
            }
        }

        //使用自定义地址
        if ("3".equals(printAddressSelect)) {
            String customAddress = PrintTemplateHelper.getFieldValueByKey(userExpressTemplate, EnumFieldValueName.PRINT_CUSTOM_SEND_ADDRESS.getValue());
            if (StringUtils.isEmpty(customAddress)) {
                return;
            }
            Address address = JSONObject.parseObject(customAddress, Address.class);
            if (null == address) {
                return;
            }
            resolve.put("send_name", getValue(address.getContactName()));
            resolve.put("send_mobile", getValue(address.getMobilePhone()));
            resolve.put("send_phone", getValue(address.getPhone()));
            resolve.put("send_state", getValue(address.getProvince()));
            resolve.put("send_city", getValue(address.getCity()));
            resolve.put("send_district", getValue(address.getCountry()));
            resolve.put("send_address", getValue(address.getAddr()));
            resolve.put("send_state_city_district", new StringBuffer()
                    .append(getValue(address.getProvince()))
                    .append(getValue(address.getCity()))
                    .append(getValue(address.getCountry()))
            );
            resolve.put("send_city_district", new StringBuffer()
                    .append(getValue(address.getCity()))
                    .append(getValue(address.getCountry()))
            );
            resolve.put("send_detail_address", new StringBuffer()
                    .append(getValue(address.getProvince()))
                    .append(getValue(address.getCity()))
                    .append(getValue(address.getCountry()))
                    .append(getValue(address.getAddr()))
            );
            return;
        }
        if (shop == null || (StringUtils.isEmpty(shop.getSendAddress()) && StringUtils.isEmpty(shop.getSendState()))) {
            return;
        }
        resolve.put("send_name", getValue(shop.getSendContact()));
        resolve.put("send_mobile", getValue(shop.getSendMobile()));
        resolve.put("send_phone", getValue(shop.getSendPhone()));
        resolve.put("send_state", getValue(shop.getSendState()));
        resolve.put("send_city", getValue(shop.getSendCity()));
        resolve.put("send_district", getValue(shop.getSendDistrict()));
        resolve.put("send_address", getValue(shop.getSendAddress()));
        resolve.put("send_state_city_district", new StringBuffer()
                .append(getValue(shop.getSendState()))
                .append(getValue(shop.getSendCity()))
                .append(getValue(shop.getSendDistrict()))
        );
        resolve.put("send_city_district", new StringBuffer()
                .append(getValue(shop.getSendCity()))
                .append(getValue(shop.getSendDistrict()))
        );
        resolve.put("send_detail_address", new StringBuffer()
                .append(getValue(shop.getSendState()))
                .append(getValue(shop.getSendCity()))
                .append(getValue(shop.getSendDistrict()))
                .append(getValue(shop.getSendAddress()))
        );
    }

    public static void __initSysRemark(Trade singleTrade, Map<String, Object> resolve) {
        __initSysRemark(singleTrade, resolve, "sys_sku_remark");
    }

    private static void __initSysRemark(Trade singleTrade, Map<String, Object> resolve, String resolveKey) {
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        StringBuilder itemRemarkSB = new StringBuilder();
        for (Order order : orderList) {
            String sysRemark = OrderUtils.getSysRemark(order);
            sb.append(null != sysRemark ? CommaReplaceUtils.commaReplace(sysRemark) : "").append(",");

            //处理商品备注数据
            itemRemarkSB.append(CommaReplaceUtils.commaReplace(StringUtils.trimToEmpty(order.getSysItemRemark())));
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put(resolveKey, sb.toString());

        //设置商品备注数据
        if (itemRemarkSB.length() > 0) {
            itemRemarkSB.deleteCharAt(itemRemarkSB.length() - 1);
        }
        resolve.put("item_sys_remark", itemRemarkSB.toString());
    }

    public static void initItemSysRemark(Trade singleTrade, Map<String, Object> resolve) {
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            String sysItemRemark = order.getSysItemRemark();
            sb.append(null != sysItemRemark ? CommaReplaceUtils.commaReplace(sysItemRemark) : "").append(",");
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put("item_sys_remark", sb.toString());
    }

    /**
     * 初始化发货单、拣货单、拿货单的宝贝图片取值
     *
     * @param resolve
     * @param singleTrade
     * @param templateBase
     */
    public static void __initItemPicSelect(Map<String, Object> resolve, Trade singleTrade, UserTemplateBase templateBase) {
        if (!resolve.containsKey("table_item_pic")) {
            return;
        }

        String itemPicSelect = null;
        if (templateBase instanceof UserDeliverTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserDeliverTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserGetterTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserGetterTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserPickerTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserPickerTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        }

        if (StringUtils.isEmpty(itemPicSelect)) {
            return;
        }

        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            if (PtConfigConst.PIC_PATH_SOURCE.equals(itemPicSelect)) {
                sb.append(StringUtils.defaultString(order.getPicPath(), "")).append(",");
            } else {
                sb.append(StringUtils.defaultString(order.getSysPicPath(), "")).append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        resolve.put("table_item_pic", sb.toString());
    }

    /**
     * @param resolve
     * @param singleTrade
     * @param itemMainPicMap
     * @return
     * @throws
     * @description: 处理发货单主图数据
     * <AUTHOR>
     * @date 2018/8/1 下午2:46
     */
    public static void __initItemMainPicPath(Map<String, Object> resolve, Trade singleTrade, Map<Long, String> itemMainPicMap) {

        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            resolve.put("table_main_item_pic", "");
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            Long itemSysId = order.getItemSysId();
            if (!itemMainPicMap.isEmpty() && itemMainPicMap.containsKey(itemSysId)) {
                String mainItemPicPath = itemMainPicMap.get(itemSysId);
                sb.append(mainItemPicPath).append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        resolve.put("table_main_item_pic", sb.toString());
    }

    /**
     * 填入发货单二维码且放回发货单保存对象
     */
    public static void _initDeliverCode(Staff staff, Map<String, Object> resolve, Trade singleTrade,
                                        List<String> needValues) {
        if (needValues.contains("deliver_code")) {
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            DeliversVo deliversVo = new DeliversVo();
            List<DeliverVo> deliverVos = new ArrayList<>();
            deliversVo.setDeliverVoList(deliverVos);
            int total = 0;//商品总数
            for (Order order : orderList) {
                DeliverVo deliverVo = new DeliverVo();
                deliverVos.add(deliverVo);
                deliverVo.setSkuPicUrl(order.getSysPicPath());
                deliverVo.setItemTitle(StringUtils.isNotBlank(order.getTitle()) ? order.getTitle() : order.getSysTitle());
                deliverVo.setSkuTitle(order.getSysSkuPropertiesName());
                deliverVo.setNum(order.getNum());
                deliverVo.setPayment(order.getPayment());
                total += order.getNum();
            }
            deliversVo.setTotal(total);
            String sign = Md5Util.md5(singleTrade.getSid() + "cjzg_erp");
            sign = sign.substring(0, 18);
            deliversVo.setSid(sign);
            String codePath = "http://wxerp.superboss.cc/shippingList/index.html?companyId=" + staff.getCompanyId() + "&sid=" + sign;
            resolve.put("deliver_code", codePath);
            resolve.put("deliversVo", deliversVo);
        }
    }

    public static void __initItemPicSelect(Map<String, Object> resolve, PrintTemplateTrade signalTrade, UserTemplateBase templateBase) {
        if (!resolve.containsKey("table_item_pic")) {
            return;
        }

        String itemPicSelect = null;
        if (templateBase instanceof UserDeliverTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserDeliverTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserGetterTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserGetterTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserPickerTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserPickerTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        }

        if (StringUtils.isEmpty(itemPicSelect)) {
            return;
        }

        List<PrintTemplateOrder> orderList = signalTrade.getOrders();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (PrintTemplateOrder order : orderList) {
            if ("2".equals(itemPicSelect)) {
                sb.append(StringUtils.defaultString(order.getPicPath(), "")).append(",");
            } else {
                sb.append(StringUtils.defaultString(order.getSysPicPath(), "")).append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        resolve.put("table_item_pic", sb.toString());
    }

    /**
     * 得到用户选择的宝贝图片
     *
     * @param printOrder
     * @param templateBase
     * @return
     */
    public static String getSelectItemPicPath(PrintTemplateOrder printOrder, UserTemplateBase templateBase) {

        String itemPicSelect = null;
        if (templateBase instanceof UserPickerTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserPickerTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserGetterTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserGetterTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        }

        if (StringUtils.isEmpty(itemPicSelect)) {
            return StringUtils.defaultString(printOrder.getPicPath(), "");
        }

        if (StringUtils.equals(PtConfigConst.PIC_PATH_SOURCE, itemPicSelect)) {
            return StringUtils.defaultString(printOrder.getPicPath(), "");
        } else {
            return StringUtils.defaultString(printOrder.getSysPicPath(), "");
        }
    }

    /**
     * @param itemPicPath
     * @param mainPicPath
     * @return
     * @throws
     * @description: 处理规格图片数据
     * <AUTHOR>
     * @date 2018/8/8 下午2:21
     */
    public static String handleSkuPicPath(String itemPicPath, String mainPicPath) {
        if (StringUtils.isEmpty(itemPicPath) || StringUtils.equals(itemPicPath, StockConstants.PATH_NO_PIC)) {
            return mainPicPath;
        }
        return itemPicPath;
    }

    /**
     * @param printOrder
     * @param itemMap
     * @return
     * @throws
     * @description: 获取主商品图片数据
     * <AUTHOR>
     * @date 2018/8/2 上午11:41
     */
    public static String getMainPicPath(PrintTemplateOrder printOrder, Map<Long, DmjItem> itemMap) {
        if (null == printOrder) {
            return "";
        }

        if (itemMap.isEmpty()) {
            return "";
        }

        if (!itemMap.containsKey(printOrder.getItemSysId())) {
            return "";
        }

        return itemMap.get(printOrder.getItemSysId()).getPicPath();
    }

    /**
     * 得到用户选择的宝贝图片
     *
     * @param order
     * @param templateBase
     * @return
     */
    public static String getSelectItemPicPath(Order order, UserTemplateBase templateBase) {

        String itemPicSelect = "";
        if (templateBase instanceof UserPickerTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserPickerTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        } else if (templateBase instanceof UserGetterTemplate) {
            itemPicSelect = PrintTemplateHelper.getFieldValueByKey(((UserGetterTemplate) templateBase).getFieldValues(),
                    EnumFieldValueName.ITEM_PIC_SELECT.getValue());
        }

        if (StringUtils.isEmpty(itemPicSelect)) {
            return StringUtils.defaultString(order.getPicPath(), "");
        }

        if (PtConfigConst.PIC_PATH_SOURCE.equals(itemPicSelect)) {
            return StringUtils.defaultString(order.getPicPath(), "");
        } else {
            return StringUtils.defaultString(order.getSysPicPath(), "");
        }
    }

    public static Map<String, String> getCustomContent(UserInvoicesTemplate userInvoicesTemplate) {
        if (userInvoicesTemplate == null) {
            return Collections.emptyMap();
        }
        List<FieldSettings> fieldSettingsList = userInvoicesTemplate.getFieldSettings();
        if (CollectionUtils.isEmpty(fieldSettingsList)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<String, String>();
        for (FieldSettings fieldSettings : fieldSettingsList) {
            String fieldName = fieldSettings.getField();
            if (fieldName != null && fieldName.contains("custom_content")) {
                map.put(fieldName, fieldSettings.getContent());
            }
        }
        return map;
    }

    public static String formMatProductionDate(Object productionDate) {
        if (productionDate == null) {
            return "";
        }
        if (productionDate instanceof String) {
            if (StringUtils.isEmpty((String) productionDate)) {
                return "";
            }
            if (logger.isDebugEnabled()) {
                logger.debug("productionDate stringValue:" + productionDate + "#");
            }
            return "";
        }
        try {
            Date date = (Date) productionDate;
            if (date == null) {
                return "";
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(date);
        } catch (Exception e) {
            logger.error("formMatProductionDate出错：" + productionDate, e);
            return "";
        }

    }

    /**
     * 判断是否需要进行相同商品合并
     * true 为合并
     * false 不合并
     *
     * @param template
     * @return
     */
    public static boolean needSameItemMerge(Object template) {
        if (template instanceof UserWlbExpressTemplate || template instanceof UserExpressTemplate) {
            return "2".equals(PrintTemplateHelper.getFieldValueByKey((IExpressTemplateBase) template, EnumFieldValueName.PORD_MERGE.getValue()));
        }
        if (template instanceof UserDeliverTemplate) {
            String tableMerge = PrintTemplateHelper.getFieldValueByKey(((UserDeliverTemplate) template).getFieldValues(), "table_merge");
            if (StringUtils.isEmpty(tableMerge)) {
                tableMerge = PrintTemplateHelper.getFieldValueByKey(((UserDeliverTemplate) template).getFieldValues(), "Fhdishb");
            }
            return "1".equals(tableMerge);
        } else {
            return false;
        }
    }

    public static Trade getMergeTradeByCustomRule(Trade singleTrade, String customKey) {
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        Map<String, Order> orderMap = new HashMap<String, Order>();
        for (Order order : orderList) {
            String key = order.getItemSysId() + "_" + order.getSkuSysId() + customKey;
            Order mapOrder = orderMap.get(key);
            if (orderMap.containsKey(key)) {
                mapOrder.setNum(mapOrder.getNum() + order.getNum());
                if (mapOrder.getOldNum() != null && order.getOldNum() != null) {
                    mapOrder.setOldNum(mapOrder.getOldNum() + order.getOldNum());
                }
                mapOrder.setTotalFee(String.valueOf(add(order.getTotalFee(), mapOrder.getTotalFee()).toString()));
                mapOrder.setPayment(String.valueOf(add(order.getPayment(), mapOrder.getPayment()).toString()));
                mapOrder.setDiscountFee(String.valueOf(add(order.getDiscountFee(), mapOrder.getDiscountFee()).toString()));
                orderMap.put(key, mapOrder);
            } else {
                orderMap.put(key, order);
            }
        }
        List<Order> mergeTradeOrderList = new ArrayList<Order>(orderMap.values());
        Trade sameItemMergeTrade = new Trade();
        BeanUtils.copyProperties(singleTrade, sameItemMergeTrade);
        TradeUtils.setOrders(sameItemMergeTrade, mergeTradeOrderList);
        return sameItemMergeTrade;
    }

    /**
     * 获取商品合并后的trade
     *
     * @param singleTrade
     * @return
     */
    public static Trade getSameItemMergeTrade(Trade singleTrade) {
        return getMergeTradeByCustomRule(singleTrade, null);
    }

    private static BigDecimal add(String str1, String str2) {
        return new BigDecimal(com.raycloud.dmj.tve.utils.StringUtils.isEmpty(str1) ? "0" : str1).add(new BigDecimal(com.raycloud.dmj.tve.utils.StringUtils.isEmpty(str2) ? "0" : str2));
    }

    public static void __ifNeedTbSkuOuterId(List<String> needValues, String needTbSkuOuterId) {
        if ("2".equals(needTbSkuOuterId)) {
            needValues.add("sku_outer_id");
            needValues.add("sku_outer_idx");
        }
    }

    public static void __replaceExpressWithSkuOuterId(Map<String, Object> resolve, String needTbSkuOuterId, Trade singleTrade, boolean print_platform_item_conf, List<String> needValues) {
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        //平台商家编码
        if ("2".equals(needTbSkuOuterId)) {
            if (resolve.containsKey("sku_outer_id") && resolve.containsKey("sku_outer_idx") && resolve.containsKey("item_outer_id")) {
                String[] result1 = split(resolve.get("sku_outer_id").toString(), ',', true);
                String[] result2 = split(resolve.get("sku_outer_idx").toString(), ',', true);
                String[] result3 = split(resolve.get("item_outer_id").toString(), ',', true);
                if (result2.length == 0 && result1.length == 1) {
                    resolve.put("sku_outer_idx", StringUtils.join(result1, ","));
                } else if (result1.length == result2.length && result2.length != 0) {
                    for (int i = 0; i < orderList.size(); i++) {
                        if (StringUtils.isEmpty(result2[i])) {
                            result2[i] = result1[i];
                        }
                        Order order = orderList.get(i);
                        if (PrintTemplateHelper.orderReplacePlatformField(orderList.get(i), print_platform_item_conf)) {
                            result2[i] = order.getSysOuterId();
                        }
                    }
                    resolve.put("sku_outer_idx", StringUtils.join(result2, ","));//将原有数据保留，方便以后查询问题
                }
                resolve.put("item_outer_id", resolve.get("sku_outer_idx"));
            }
        }

        List<String> results = new ArrayList<>();
        List<String> results1 = new ArrayList<>();
        for (Order order : orderList) {
            if (PrintTemplateHelper.orderReplacePlatformField(order, print_platform_item_conf)) {
                results.add(CommaReplaceUtils.commaReplace(order.getSysSkuPropertiesName()));
                results1.add(CommaReplaceUtils.commaReplace(order.getSysOuterId()));
            } else {
                results1.add(CommaReplaceUtils.commaReplace(StringUtils.defaultIfBlank(StringUtils.defaultIfBlank(order.getOuterSkuId(), order.getOuterIid()), order.getSysOuterId())));
                results.add(CommaReplaceUtils.commaReplace(StringUtils.defaultIfBlank(order.getSkuPropertiesName(), order.getSysSkuPropertiesName())));
            }
        }
        if (needValues.contains("item_specification_paltform"))
            resolve.put("item_specification_paltform", StringUtils.join(results, ","));
        if ("2".equalsIgnoreCase(needTbSkuOuterId))
            resolve.put("item_outer_id", StringUtils.join(results1, ","));
    }

    /**
     * 建立一个替换的依赖关系 经常在__replacePropertiesWithNeedValue方法前使用
     *
     * @param needValues
     * @param source
     * @param target
     */
    public static void makeNeedValuesDep(List<String> needValues, String source, String target) {
        if (needValues.contains(target) && !needValues.contains(source)) {
            needValues.add(source);
        }
    }

    /**
     * @param needValues
     * @param source
     * @param target
     */
    public static void makeNeedValuesDep(List<String> needValues, String source, boolean target) {
        if (target && !needValues.contains(source)) {
            needValues.add(source);
        }
    }

    /**
     * 建立一个替换的依赖关系 经常在__replacePropertiesWithNeedValue方法前使用
     *
     * @param needValues
     * @param source
     * @param targets
     */
    public static void makeNeedValuesDep(List<String> needValues, String source, String... targets) {
        if (targets == null) {
            return;
        }
        for (String target : targets) {
            makeNeedValuesDep(needValues, source, target);
        }
    }

    /**
     * 属性替代 如果属性target没有那么就使用source属性代替
     * 使用前最好是先建立一个依赖关系，防止数据为空
     *
     * @param resolve
     * @param source  替身
     * @param target  需要被替换的属性
     */
    public static void __replacePropertiesWithNeedValue(Map<String, Object> resolve, String source, String target) {
        if (resolve.containsKey(source) && resolve.containsKey(target)) {
            String[] result1 = split(resolve.get(source).toString(), ',', true);
            String[] result2 = split(resolve.get(target).toString(), ',', true);
            if (result2.length == 0 && result1.length == 1) {
                resolve.put(target, StringUtils.join(result1, ","));
            } else if (result1.length == result2.length && result2.length != 0) {
                for (int i = 0; i < result1.length; i++) {
                    if (StringUtils.isBlank(result2[i])) {
                        result2[i] = result1[i];
                    }
                }
                resolve.put(target, StringUtils.join(result2, ","));
            }

        }
    }

    public static void __replaceWithNum(List<String> needValues, Map<String, Object> resolve, String itemNumSelect) {
        if (needValues.contains("item_num") || needValues.contains("item_num_up") || needValues.contains("item_num_down")) {
            String nums = (String) resolve.get("item_num");
            if (StringUtils.isBlank(nums)) {
                nums = (String) resolve.get("item_num_up");
            }
            if (StringUtils.isBlank(nums)) {
                nums = (String) resolve.get("item_num_down");
            }
            StringBuilder sb = new StringBuilder();
            String[] array = ArrayUtils.toStringArray(nums);
            String[] itemUnitArr = split(resolve.get("item_unit").toString(), ',', true, true);
            String start = "【";
            String end = "】";
            if (Objects.equals("2", itemNumSelect)) {
                start = "*";
                end = "";
            }
            if (StringUtils.isNotBlank(nums)) {
                for (int i = 0; i < array.length; i++) {
                    sb.append(start).append(array[i]).append(itemUnitArr[i]).append(end).append(",");
                }
                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
            }
            resolve.put("item_num", sb.toString());
            resolve.put("item_num_up", sb.toString());
            resolve.put("item_num_down", sb.toString());
        }

    }

    /**
     * 判断是否为jitx订单
     *
     * @param t
     * @return
     */
    public static boolean isJITXTrade(Trade t) {
        return CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(t.getSubSource()) || (TradeUtils.isPoisonBrandDeliverTrade(t) && !TradeUtils.isPoisonSpecifyLogistics(t));
    }

    /**
     * 规格简称
     */
    public static void __replaceWithItemSkuShortTitle(Trade singleTrade, Map<String, DmjSku> dmjSkuMap, Map<String, DmjItem> itemMap, Map<String, Object> resolve, boolean shortTitle4Item) {
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringJoiner stringJoiner = new StringJoiner(",");
        StringJoiner itemShortTitle = new StringJoiner(",");//商品简称
        for (Order order : orders) {
            String itemId = order.getItemSysId() == null ? "" : order.getItemSysId().toString();
            String skuId = order.getSkuSysId() == null ? "" : order.getSkuSysId().toString();
            DmjSku sku = dmjSkuMap.get(skuId);
            DmjItem item = itemMap.get(itemId);
            if (sku != null) {
                //规格简称
                String shortTitle = sku.getShortTitle();
                //商品简称
                if (item != null) {
                    shortTitle = StringUtils.isBlank(shortTitle) ? item.getShortTitle() : shortTitle;
                }
                //规格名称
                shortTitle = StringUtils.isBlank(shortTitle) ? sku.getTitle() : shortTitle;
                //规格别名
                shortTitle = StringUtils.isBlank(shortTitle) ? sku.getPropertiesName() : shortTitle;
                // 将名称中的 "," 替换成前端可识别的逗号
                stringJoiner.add(StringUtils.defaultString(shortTitle).replaceAll(",", "&#44;"));
            } else {
                stringJoiner.add(item == null ? "" : Optional.ofNullable(item.getShortTitle()).orElse(""));
            }
            itemShortTitle.add(item == null || StringUtils.isBlank(item.getShortTitle()) ? order.getShortTitle() : item.getShortTitle());
        }
        resolve.put("item_specification_short_title", stringJoiner.toString());
        if (shortTitle4Item) {
            resolve.put("item_short_title", itemShortTitle.toString());
        }
    }

    public static void __handWaveId(List<String> needValues, Map<String, Object> resolve) {
        if (needValues.contains("trade_wave_id")) {
            String trade_wave_id = String.valueOf(resolve.get("trade_wave_id"));
            if (trade_wave_id != null && "0".equals(trade_wave_id)) {
                resolve.put("trade_wave_id", "");
            }
        }
    }

    /**
     * 初始化商品单位
     *
     * @param singleTrade
     * @param resolve
     * @param resolveKey
     * @param printItemUnit
     */
    public static void initItemUnit(Trade singleTrade, Map<String, Object> resolve, String resolveKey, String printItemUnit) {
        if (!resolve.containsKey(resolveKey)) {
            return;
        }
        if (singleTrade == null) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            // 商品打印单位（1：不显示单位；2：件（默认值）；3：主商品单位；4：sku单位）
            if ("1".equals(printItemUnit)) {
                sb.append(",");
            } else if ("3".equals(printItemUnit)) {
                sb.append(StringUtils.defaultIfEmpty(order.getUnit(), "")).append(",");
            } else if ("4".equals(printItemUnit)) {
                if (order.getSkuSysId() != null && order.getSkuSysId() == -1) {
                    sb.append(StringUtils.defaultIfEmpty(order.getUnit(), "")).append(",");
                } else {
                    String skuUnit = order.getSkuUnit();
                    if (StringUtils.isBlank(skuUnit)) {
                        sb.append(StringUtils.defaultIfEmpty(order.getUnit(), "")).append(",");
                    } else {
                        sb.append(skuUnit).append(",");
                    }
                }
            } else {
                sb.append("件,");
            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put(resolveKey, sb.toString());
    }

    /**
     * 当采购单为模板类型为交叉模式，去除不需要的字段
     *
     * @param resolve
     */
    public static void __crossStructureRendering(Map<String, Object> resolve) {
        resolve.remove("sort_item_outer_id");
        resolve.remove("sort_sku_outer_id");
        resolve.remove("table_item_outer_id");
        resolve.remove("table_item_outer_id_js_bar_code");
        resolve.remove("table_pt_2barcode");
        resolve.put("outer_id_of_item", resolve.get("outer_id_item"));
        String[] title = String.valueOf(resolve.get("table_item_title")).split(",");
        StringBuilder counts = new StringBuilder();
        for (int i = 0; i < title.length; i++) {
            counts.append(i + 1);
            if (i != title.length - 1) {
                counts.append(",");
            }
        }
        resolve.put("table_item_id", counts);
        // 商品主图为空问题修复
//        resolve.put("table_item_pic", resolve.get("table_item_pic_url"));
//        resolve.put("table_main_item_pic", resolve.get("table_item_pic_url"));
        resolve.remove("table_item_pic_url");
        resolve.remove("table_sku_unit");
        resolve.remove("table_supplier_outer_id");
        resolve.remove("table_sys_sku_remark");
        resolve.remove("table_detailed_remark");
        resolve.remove("sort_sku_remark");
        resolve.remove("table_item_supplier_code");
        resolve.remove("table_item_supplier_name");
        resolve.remove("table_item_unit");
        resolve.remove("table_item_properties");
        resolve.remove("table_item_remark");

    }

    public static void getItemSuitOuterId(List<String> needValues, Trade singleTrade, Map<String, Object> resolve, UserWlbExpressTemplate template) {
        if (!needValues.contains("item_suit_outer_id")) {
            return;
        }
        //相同商品不合并并且商品打印  0为明细
        if (!needSameItemMerge(template) && PrintTemplateHelper.getSelectSuiteType(template) == 0) {
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            StringJoiner itemSuitOuterId = new StringJoiner(",");
            for (Order order : orderList) {
                itemSuitOuterId.add(StringUtils.isEmpty(order.getItemSuitOuterId()) ? "" : order.getItemSuitOuterId());
            }
            resolve.put("item_suit_outer_id", itemSuitOuterId.toString());
        }
    }

    public static void getItemSuitRemark(List<String> needValues, Trade singleTrade, Map<String, Object> resolve, UserWlbExpressTemplate template, Map<String, DmjItem> itemMap, Map<String, DmjSku> skuMap) {
        if (!needValues.contains("item_suit_remark")) {
            return;
        }
        // 相同商品不合并并且打印套件明细
        if (!needSameItemMerge(template) && PrintTemplateHelper.getSelectSuiteType(template) == 0) {
            List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            StringJoiner itemSuitRemark = new StringJoiner(",");
            for (Order order : orderList) {
                String remark = "";
                if (order.isSuit(true)) {
                    remark = order.getItemSuitRemark();
                }
                itemSuitRemark.add(StringUtils.isEmpty(remark) ? "" : remark);
            }
            resolve.put("item_suit_remark", itemSuitRemark.toString());
        }
    }

    public static void getItemSupplierCode(List<String> needValues, Trade singleTrade, Map<String, Object> resolve, ItemSupplierBridgeResult itemSupplierBridgeResult) {
        if (!(needValues.contains("item_supplier_code") || needValues.contains("item_supplier_name")) || itemSupplierBridgeResult == null) {
            return;
        }
        Map<Long, List<ItemSupplierBridge>> itemSupplierBridgesMap = itemSupplierBridgeResult.getPureItemSupplierBridgesMap();
        Map<Long, List<ItemSupplierBridge>> skuSupplierBridgesMap = itemSupplierBridgeResult.getSkuSupplierBridgesMap();
        Map<String, Supplier> supplierMap = itemSupplierBridgeResult.getSupplierMap();
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        StringJoiner itemSupplierCode = new StringJoiner(",");
        StringJoiner itemSupplierName = new StringJoiner(",");
        for (Order order : orderList) {
            String key = order.getItemSysId() + "_" + (order.getSkuSysId() == null || order.getSkuSysId() < 1 ? 0 : order.getSkuSysId());
            Supplier supplier = supplierMap.get(key);
            itemSupplierName.add(supplier == null || StringUtils.isBlank(supplier.getName()) ? "" : supplier.getName());
            List<ItemSupplierBridge> itemSupplierBridges = itemSupplierBridgesMap.get(order.getItemSysId());
            List<ItemSupplierBridge> skuSupplierBridges = skuSupplierBridgesMap.get(order.getSkuSysId());
            if (CollectionUtils.isNotEmpty(skuSupplierBridges)) {
                getSupplierCode(itemSupplierCode, skuSupplierBridges);
            } else {
                if (CollectionUtils.isNotEmpty(itemSupplierBridges)) {
                    getSupplierCode(itemSupplierCode, itemSupplierBridges);
                } else {
                    itemSupplierCode.add("");
                }
            }
        }
        resolve.put("item_supplier_code", itemSupplierCode.toString());
        resolve.put("item_supplier_name", itemSupplierName.toString());
    }

    private static void getSupplierCode(StringJoiner itemSupplierCode, List<ItemSupplierBridge> itemSupplierBridges) {
        List<ItemSupplierBridge> mainItemSupplier = itemSupplierBridges.stream().filter(e -> Objects.equals(e.getMainState(), 1)).collect(Collectors.toList());
        ItemSupplierBridge itemSupplierBridge = CollectionUtils.isNotEmpty(mainItemSupplier) ? mainItemSupplier.get(0) : itemSupplierBridges.get(0);
        itemSupplierCode.add(StringUtils.isEmpty(itemSupplierBridge.getSupplierItemOuterId()) ? "" : itemSupplierBridge.getSupplierItemOuterId());
    }

    public static void initTradeType(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (needValues.contains("trade_qtg_type") && (Objects.equals(CommonConstants.PLAT_FORM_TYPE_TEMU, singleTrade.getSource()) || Objects.equals(CommonConstants.PLAT_FORM_TYPE_SHEIN, singleTrade.getSource()))) {
            if (singleTrade.getType().contains("1")) {
                resolve.put("trade_qtg_type", "紧急");
            } else {
                resolve.put("trade_qtg_type", "普通");
            }
        }
    }

    /**
     * @param resolve
     * @return void
     * @description 散装数转换(不解释计算)
     * <AUTHOR>
     * @date 2024-04-23 16:39
     */
    public static void bulkNumConversion(Map<String, Object> resolve) {
        // table_num_per_box = 箱规   table_bulk_num = 散装数     table_num_of_box = 箱数
        String[] tableNumPerBox = split(resolve.get("table_num_per_box") == null ? "" : resolve.get("table_num_per_box").toString(), ',', true);
        if (tableNumPerBox == null || tableNumPerBox.length == 0) return;
        String numPer = tableNumPerBox[0];
        if (StrUtil.isEmpty(numPer)) return;
        int numPerBox = Integer.parseInt(numPer);
        String[] tableBulkNum = split(resolve.get("table_bulk_num") == null ? "" : resolve.get("table_bulk_num").toString(), ',', true);
        String[] tableNumOfBox = split(resolve.get("table_num_of_box") == null ? "" : resolve.get("table_num_of_box").toString(), ',', true);
        StringBuilder tableBulkNumBuilder = new StringBuilder();
        StringBuilder tableNumOfBoxBuilder = new StringBuilder();
        for (int i = 0; i < tableNumPerBox.length; i++) {
            int bulkNum = 0;
            int numOfBox = 0;
            if (tableBulkNum != null && tableBulkNum.length == tableNumPerBox.length && StrUtil.isNotEmpty(tableBulkNum[i])) {
                bulkNum = Integer.parseInt(tableBulkNum[i]);
            }
            if (tableNumOfBox != null && tableNumOfBox.length == tableNumPerBox.length && StrUtil.isNotEmpty(tableNumOfBox[i])) {
                numOfBox = Integer.parseInt(tableNumOfBox[i]);
            }
            if (numPerBox != 0 && bulkNum != 0 && bulkNum >= numPerBox) {
                numOfBox += bulkNum / numPerBox;
                bulkNum = bulkNum % numPerBox;
            }
            tableBulkNumBuilder.append(bulkNum).append(",");
            tableNumOfBoxBuilder.append(numOfBox).append(",");
        }
        resolve.put("table_bulk_num", tableBulkNumBuilder.length() > 0 ? tableBulkNumBuilder.deleteCharAt(tableBulkNumBuilder.length() - 1).toString() : "");
        resolve.put("table_num_of_box", tableNumOfBoxBuilder.length() > 0 ? tableNumOfBoxBuilder.deleteCharAt(tableNumOfBoxBuilder.length() - 1).toString() : "");
    }

    /**
     * 商品条形码
     * 取值内容：订单中商品的商品条形码barcode
     * ● 如果订单中的商品为纯商品，则取该商品的【条形码】；
     * ● 如果订单中的商品为规格商品，则取该商品的【规格条形码】;
     * ● 如果【商品套件打印】中选择'明细'，则取套件明细中商品的【条形码】或【规格条形码】；
     * ● 如果【商品套件打印】中选择'套件'，则值为空；
     *
     * @param singleTrade            交易订单
     * @param needValues             需要打印的字段
     * @param resolve                需要打印字段对应的键值
     * @param userWlbExpressTemplate 快递面单模版
     * @param itemMap                商品信息
     * @param skuMap                 商品如果有多个，对应的规格信息
     */
    public static void getItemBarcode(Trade singleTrade, List<String> needValues, Map<String, Object> resolve,
                                      UserWlbExpressTemplate userWlbExpressTemplate, Map<String, DmjItem> itemMap,
                                      Map<String, DmjSku> skuMap) {
        // 先做基本的 null 检查，避免空指针异常
        if (userWlbExpressTemplate == null || needValues == null || resolve == null || itemMap == null || skuMap == null) {
            return; // 如果关键参数为空，直接返回，防止 NPE
        }

        // 商品套件打印为 '明细' 且勾选了商品条形码
        if (PrintTemplateHelper.getSelectSuiteType(userWlbExpressTemplate) == 0 && needValues.contains("item_barcode")) {
            List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);

            if (orders == null || orders.isEmpty()) {
                return; // 如果订单列表为空，直接返回
            }

            StringJoiner barcode = new StringJoiner(",");
            for (Order order : orders) {
                String itemId = order.getItemSysId() == null ? "" : order.getItemSysId().toString();
                String skuId = order.getSkuSysId() == null ? "" : order.getSkuSysId().toString();
                // 先确保 Map 取出的值不为空
                DmjSku sku = skuMap.get(skuId);
                DmjItem item = itemMap.get(itemId);

                if (sku != null && sku.getBarcode() != null) {
                    // 规格条形码
                    barcode.add(sku.getBarcode());
                } else if (item != null && item.getBarcode() != null) {
                    // 商品条形码
                    barcode.add(item.getBarcode());
                }
            }
            resolve.put("item_barcode", barcode.toString());
        }
    }

    /**
     * 获取订单发货承诺时间
     * @param resolve       返回的组装的打印数据
     * @param singleTrade   订单
     * @param needValues    模版需要打印的值
     */
    public static void buildPromiseTime(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("trade_timeout_action_time")) {
            return;
        }
        Date promiseTime = singleTrade.getTimeoutActionTime();
        resolve.put("trade_timeout_action_time", promiseTime != null ? promiseTime.getTime() : "");
    }

    public static void buildSourceNameOrAliasName(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (needValues.contains("trade_source_name")) {
            resolve.put("trade_source_name", StringUtils.isBlank(singleTrade.getSourceName()) ? "" : singleTrade.getSourceName());
        }
        if (needValues.contains("trade_source_alias_name")) {
            resolve.put("trade_source_alias_name", StringUtils.isBlank(singleTrade.getSourceAliasName()) ? "" : singleTrade.getSourceAliasName());
        }
    }

    /**
     * 订单识别码
     * @param resolve       返回的组装的打印数据
     * @param singleTrade   订单
     * @param needValues    模版需要打印的值
     */
    public static void buildItemIdentCode(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("item_ident_code")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        StringJoiner identCode = new StringJoiner(",");
        for (Order order : orderList) {
            identCode.add(order.getIdentCode());
        }
        resolve.put("item_ident_code", identCode.toString());
    }

    public static class ReplaceWithCombineFieldParams {
        // 打印单位
        private String printItemUnit;
        // 商品数量连接符
        private String itemNumSelect;

        public String getPrintItemUnit() {
            return printItemUnit;
        }

        public ReplaceWithCombineFieldParams setPrintItemUnit(String printItemUnit) {
            this.printItemUnit = printItemUnit;
            return this;
        }

        public String getItemNumSelect() {
            return itemNumSelect;
        }

        public void setItemNumSelect(String itemNumSelect) {
            this.itemNumSelect = itemNumSelect;
        }
    }

    /**
     * 当商品未匹配的订单时获取打印数据商品详细时，如果系统值为空则获取平台的值
     *
     * <AUTHOR>
     * @date 下午6:49 2018/12/10
     **/
    public static void replaceValueIfSysIsNull(List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                replaceValueIfSysIsNull(trade);
            }
        }
    }

    public static void replaceValueIfSysIsNull(Trade trade) {
        if (trade == null) {
            return;
        }
        TbTrade tbTrade = (TbTrade) trade;
        List<TbOrder> orders = tbTrade.getOrders();
        if (CollectionUtils.isNotEmpty(orders)) {
            for (TbOrder tbOrder : orders) {
                // 商品未匹配的订单
                if (tbOrder.getItemSysId() == null || tbOrder.getItemSysId() <= 0) {
                    if (StringUtils.isEmpty(tbOrder.getSysTitle())) {
                        tbOrder.setSysTitle(tbOrder.getTitle());
                    }
                    if (StringUtils.isEmpty(tbOrder.getSysPicPath())) {
                        tbOrder.setSysPicPath(tbOrder.getPicPath());
                    }
                    if (StringUtils.isEmpty(tbOrder.getSysOuterId())) {
                        tbOrder.setSysOuterId(StringUtils.isNotEmpty(tbOrder.getOuterSkuId()) ? tbOrder.getOuterSkuId() : tbOrder.getOuterId());
                    }
                    if (StringUtils.isEmpty(tbOrder.getSysSkuPropertiesName())) {
                        tbOrder.setSysSkuPropertiesName(tbOrder.getSkuPropertiesName());
                    }
                }
            }
        }
    }

    /**
     * 去详细地址里的重复省市区
     */
    public static void receiverAddressRemoveOther(Trade trade) {
        //跨境不走后面的逻辑
        if (isAbroad(trade)) {
            return;
        }
        if (trade != null && StringUtils.isNotBlank(trade.getReceiverAddress())) {
            String receiverAddress = trade.getReceiverAddress().replaceAll("\\[", "").replaceAll("]", "").replaceAll("【", "").replaceAll("】", "").trim();
            String receiverState = trade.getReceiverState() == null ? "" : trade.getReceiverState().trim();
            String receiverCity = trade.getReceiverCity() == null ? "" : trade.getReceiverCity().trim();
            String receiverDistrict = trade.getReceiverDistrict() == null ? "" : trade.getReceiverDistrict().trim();
            String receiverStreet = trade.getReceiverStreet() == null ? "" : trade.getReceiverStreet().trim();
            if (receiverState.contains("*") || receiverCity.contains("*") || receiverDistrict.contains("*") || receiverStreet.contains("*")) {
                return;
            }
            for (int i = 0; i < 10; i++) {
                if (StringUtils.isNotBlank(receiverState) && receiverAddress.startsWith(receiverState)) {
                    receiverAddress = receiverAddress.replaceFirst(receiverState, "");
                    continue;
                }
                if (StringUtils.isNotBlank(receiverCity) && receiverAddress.startsWith(receiverCity)) {
                    receiverAddress = receiverAddress.replaceFirst(receiverCity, "");
                    continue;
                }
                if (StringUtils.isNotBlank(receiverDistrict) && receiverAddress.startsWith(receiverDistrict)) {
                    receiverAddress = receiverAddress.replaceFirst(receiverDistrict, "");
                    continue;
                }
                if (StringUtils.isNotBlank(receiverStreet) && receiverAddress.startsWith(receiverStreet)) {
                    receiverAddress = receiverAddress.replaceFirst(receiverStreet, "");
                    continue;
                }
                break;
            }
            trade.setReceiverAddress(receiverAddress);
        }
    }

    /**
     * 发货单折扣率和折后单价字段
     *
     * @param needValues
     * @param resolve
     * @param singleTrade
     */
    public static void initTableItemDiscountPrice(List<String> needValues, Map<String, Object> resolve, Trade singleTrade) {
        List<Order> orders = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder table_item_discount_price = new StringBuilder();
        StringBuilder table_item_discount_rate = new StringBuilder();
        for (Order order : orders) {
            double discountedPrice = order.getDiscountedPrice();
            table_item_discount_price.append(discountedPrice).append(",");
            Double price = StringUtils.isNotBlank(order.getPrice()) ? Double.parseDouble(order.getPrice()) : 0D;
            table_item_discount_rate.append(price > 0D ? new BigDecimal((discountedPrice / price) * 100D).setScale(2, BigDecimal.ROUND_HALF_UP).toString() : " ").append(",");
        }
        if (needValues.contains("table_item_discount_price")) {
            resolve.put("table_item_discount_price", table_item_discount_price.length() > 0 ? table_item_discount_price.deleteCharAt(table_item_discount_price.length() - 1).toString() : " ");
        }
        if (needValues.contains("table_item_discount_rate")) {
            resolve.put("table_item_discount_rate", table_item_discount_rate.length() > 0 ? table_item_discount_rate.deleteCharAt(table_item_discount_rate.length() - 1).toString() : " ");
        }
    }

    /**
     * 生成套件的单品总数
     */
    public static void buildSuitItemCount(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("suit_item_count")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            if (order.getSuitItemCount() != 0) {
                sb.append("单品总数:").append(order.getSuitItemCount()).append(",");
            } else {
                sb.append(",");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put("suit_item_count", sb.toString());
    }

    /**
     * 单品总数合计
     *
     * @param resolve
     * @param singleTrade
     * @param needValues
     */
    public static void buildItemCountCombined(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("suit_count_combined")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder sb = new StringBuilder();
        Integer itemCountCombined = 0;
        for (Order order : orderList) {
            itemCountCombined += order.getSuitItemCount();
        }
        resolve.put("suit_count_combined", itemCountCombined == 0 ? "" : itemCountCombined.toString());
    }

    /**
     * 生成商品序号
     */
    public static void buildItemIndex(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("item_index")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        StringBuilder sb = new StringBuilder();
        for (Order order : orderList) {
            // 给商品序号字段占个位置
            sb.append("*$itemIndex#*").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        resolve.put("item_index", sb.toString());
    }

    /**
     * 生成批次\生产日期
     */
    public static void buildBatchNoOrProductTime(Map<String, Object> resolve, Trade singleTrade, List<String> needValues, Map<String, List<GoodsSectionOrderRecord>> goodsSectionOrderRecordMap) {
        if (singleTrade == null || goodsSectionOrderRecordMap == null || goodsSectionOrderRecordMap.size() <= 0 || !(needValues.contains("item_batch_no") || needValues.contains("item_product_date"))) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        List<String> batchNoList = new ArrayList<>();
        List<String> productDateList = new ArrayList<>();
        for (Order order : orderList) {
            String key = order.getItemSysId() + "_" + order.getSkuSysId() + "_" + order.getId();
            List<GoodsSectionOrderRecord> goodsSectionOrderRecords = goodsSectionOrderRecordMap.get(key);
            if (CollectionUtils.isEmpty(goodsSectionOrderRecords)) {
                batchNoList.add("");
                productDateList.add("");
                continue;
            }
            List<String> batchNoItemList = new ArrayList<>();
            List<String> productDateItemList = new ArrayList<>();
            for (GoodsSectionOrderRecord record : goodsSectionOrderRecords) {
                batchNoItemList.add(record.getBatchNo());
                String productTime = record.getProductTime() != null ? new SimpleDateFormat("yyyy-MM-dd").format(record.getProductTime()) : "";
                productDateItemList.add(productTime);
            }
            batchNoList.add(StringUtils.join(batchNoItemList, ";"));
            productDateList.add(StringUtils.join(productDateItemList, ";"));
        }
        resolve.put("item_batch_no", StringUtils.join(batchNoList, ","));
        resolve.put("item_product_date", StringUtils.join(productDateList, ","));
    }

    /**
     * 生产日期
     */
    public static void buildPlatformItemProductDate(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("platform_item_product_date")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        List<String> platformProductDateList = new ArrayList<>();
        for (Order order : orderList) {
            if (needValues.contains("platform_item_product_date") && order.getOrderExt() != null && StringUtils.isNotBlank(order.getOrderExt().getCustomization())) {
                JSONObject customization = JSONObject.parseObject(order.getOrderExt().getCustomization());
                platformProductDateList.add(customization.getString("productDate"));
            } else {
                platformProductDateList.add("");
            }
        }
        resolve.put("platform_item_product_date", StringUtils.join(platformProductDateList, ","));
    }

    /**
     * 达人名称
     */
    public static void buildAuthorName(Map<String, Object> resolve, Trade singleTrade, List<String> needValues) {
        if (singleTrade == null || !needValues.contains("author_name")) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        List<String> authorNameList = new ArrayList<>();
        for (Order order : orderList) {
            if (order.getOrderExt() != null && StringUtils.isNotBlank(order.getOrderExt().getAuthorName())) {
                authorNameList.add(order.getOrderExt().getAuthorName());
            } else {
                authorNameList.add("");
            }
        }
        resolve.put("author_name", StringUtils.join(authorNameList, ","));
    }

    /**
     * 商品自定义属性
     */
    public static void buildItemDefined(Map<String, Object> resolve, Trade singleTrade, List<String> needValues, Map<String, DmjItem> itemMap) {
        if (singleTrade == null || itemMap == null || itemMap.size() <= 0 || !(needValues.contains("item_defined_1") || needValues.contains("item_defined_2") || needValues.contains("item_defined_3") || needValues.contains("item_defined_4"))) {
            return;
        }
        Map<String, StringBuilder> definedMap = new HashMap<>();
        definedMap.put("item_defined_1", new StringBuilder());
        definedMap.put("item_defined_2", new StringBuilder());
        definedMap.put("item_defined_3", new StringBuilder());
        definedMap.put("item_defined_4", new StringBuilder());

        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        for (Order order : orderList) {
            String itemId = order.getItemSysId() == null ? "" : order.getItemSysId().toString();
            DmjItem item = itemMap.get(itemId);
            if (item == null || StringUtils.isBlank(item.getDefinedJson())) {
                for (String key : definedMap.keySet()) {
                    definedMap.get(key).append(",");
                }
                continue;
            }

            List<DmjItem.ItemDefined> definedList = DMJItemUtils.dealDefinedJson(null, item.getDefinedJson());
            for (int i = 0; i < definedMap.size(); i++) {
                String value = "";
                if (i < definedList.size()) {
                    value = definedList.get(i).getDefinedValue();
                }
                definedMap.get("item_defined_" + (i + 1)).append(value).append(",");
            }
        }

        // 去除最后一个逗号
        for (String key : definedMap.keySet()) {
            if (definedMap.get(key).length() > 0) {
                definedMap.get(key).deleteCharAt(definedMap.get(key).length() - 1);
            }
        }
        resolve.putAll(definedMap);
    }

    /**
     * 替换淘宝买家昵称
     *
     * @param shop
     * @param tbTrade
     */
    public static void replaceTaobaoBuyerNick(Shop shop, Trade tbTrade) {
        if (tbTrade == null) {
            return;
        }
        if (isTbTrade(tbTrade.getSource())
                || (CommonConstants.PLAT_FORM_TYPE_NEW_FX.equals(tbTrade.getSource()) && isTbTrade(tbTrade.getSubSource()))//供销订单
                || ("sys".equals(tbTrade.getSource()) && isTbTrade(shop.getSource()))) {
            if (StringUtils.isNotBlank(tbTrade.getOpenUid()) && tbTrade.getBuyerNick() != null && tbTrade.getBuyerNick().length() >= 24) {
                tbTrade.setBuyerNick(tbTrade.getOpenUid());
            }
        }
        if (CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(tbTrade.getSource()) || ("sys".equals(tbTrade.getSource()) && CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(shop.getSource()))) {
            if (tbTrade.getBuyerNick() != null && tbTrade.getBuyerNick().length() >= 24) {
                tbTrade.setBuyerNick(tbTrade.getReceiverMobile());
            }
        }
    }

    private static boolean isTbTrade(String source) {
        if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_1688.equals(source)) {
            return true;
        }
        return false;
    }

    /**
     * 设置商品货物编码
     *
     * @param singleTrade
     * @param result
     * @param itemSectionSkuMap
     */
    public static void handleItemSectionNum(Trade singleTrade, Map<String, Object> result, Map<String, AssoGoodsSectionSku> itemSectionSkuMap) {
        List<Order> orderList = TradeUtils.getOrders4Trade(singleTrade);
        if (orderList == null || orderList.size() == 0) {
            return;
        }
        StringJoiner sectionSku = new StringJoiner(",");
        for (Order order : orderList) {
            String key = order.getItemSysId() + "_" + order.getSkuSysId() + "_" + singleTrade.getWarehouseId();
            AssoGoodsSectionSku assoGoodsSectionSku = Optional.ofNullable(itemSectionSkuMap).orElse(new HashMap<>()).get(key);
            sectionSku.add(assoGoodsSectionSku == null ? "" : assoGoodsSectionSku.getGoodsSectionCode());
        }
        result.put("item_section_num", sectionSku.toString());
    }

    /**
     * 设置包材信息
     *
     * @param singleTrade
     * @param result
     * @param packmaList
     */
    public static void handlePackma(List<String> needValues, Trade singleTrade, Map<String, Object> result, List<LogisticsOrderDetail> packmaList, Map<String, DmjSku> skuMap, Map<String, DmjItem> itemMap) {
        if (CollectionUtils.isEmpty(packmaList)) {
            return;
        }

        StringJoiner packmaOuterId = new StringJoiner(",");
        StringJoiner packmaNum = new StringJoiner(",");
        StringJoiner packmaName = new StringJoiner(",");

        for (LogisticsOrderDetail detail : packmaList) {
            packmaOuterId.add(StringUtils.isNotBlank(detail.getSkuOuterId()) ? detail.getSkuOuterId() : detail.getItemOuterId());
            packmaNum.add(detail.getNum() == null ? "0" : detail.getNum().toString());
            if (needValues.contains("item_packma_name")) {
                String itemId = detail.getSysItemId() == null ? "" : detail.getSysItemId().toString();
                String skuId = detail.getSysSkuId() == null ? "" : detail.getSysSkuId().toString();
                DmjSku sku = skuMap.get(skuId);
                DmjItem item = itemMap.get(itemId);
                if (sku != null && StringUtils.isNotBlank(sku.getTitle())) {
                    packmaName.add(sku.getTitle());
                } else {
                    packmaName.add(item == null ? "" : Optional.ofNullable(item.getTitle()).orElse(""));
                }
            }
        }

        if (needValues.contains("item_packma_outer_id")) {
            result.put("item_packma_outer_id", packmaOuterId.toString());
        }
        if (needValues.contains("item_packma_num")) {
            result.put("item_packma_num", packmaNum.toString());
        }
        if (needValues.contains("item_packma_name")) {
            result.put("item_packma_name", packmaName.toString());
        }
    }

    private static String generateKey(Long itemId, Long skuId) {
        return (itemId != null ? itemId : "") + "_" + (skuId == null || skuId < 0 ? 0 : skuId);
    }

    public static Boolean isAbroad(Trade trade) {
        ArrayList<String> abroadSource = new ArrayList<>();
        abroadSource.add(CommonConstants.PLAT_FORM_TYPE_SHOPEE);
        abroadSource.add(CommonConstants.PLAT_FORM_TYPE_SUMAITONG);
        abroadSource.add(CommonConstants.PLAT_FORM_TYPE_TIKTOK);
        abroadSource.add(CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU);
        abroadSource.add(CommonConstants.PLAT_FORM_TYPE_LAZADA);
        return abroadSource.contains(trade.getSource());
    }

    /**
     * 拼接值，如果包含换行符，后面的值不再拼接空格
     *
     * @param value 待拼接值
     * @return
     */
    private static String getSplitValue(List<String> value) {
        StringBuilder splitValue = new StringBuilder();
        for (String s : value) {
            if (Objects.equals("\n", s)) {
                splitValue.append(s);
            } else {
                splitValue.append(s).append(" ");
            }
        }
        return splitValue.toString();
    }

}

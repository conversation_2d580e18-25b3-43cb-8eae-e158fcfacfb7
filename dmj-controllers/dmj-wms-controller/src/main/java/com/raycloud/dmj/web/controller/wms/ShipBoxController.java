package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.utils.DataUtils;
import com.raycloud.dmj.domain.wms.box.*;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.wms.IShipBoxService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: tianfeng
 * @Date: 2020-05-15 15:28
 */

@Controller
@RequestMapping("/wms/shipBox")
public class ShipBoxController extends WmsInitController {

    @Resource
    private IShipBoxService IShipBoxService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;
    private static final Integer wmsMaxGenerateBoxCodesNum = 1000;

    /**
     * 校验单号
     * @param mixBillNo
     * @param billType 1系统单号 2内部单号 3快递单号
     * @param orderType 0 按订单， 1 按波次
     * @param waveIdStr 波次号，英文逗号分隔
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/verify", method = RequestMethod.GET)
    @ResponseBody
    public Object verifyBillNo(String mixBillNo, Integer billType, Integer orderType, String waveIdStr,Integer openPackageAutoExamine,Integer openAfterConsignBox,Integer jitBoxValidate,Integer noJitBoxValidate,String pickingCodeStr,String api_name) throws SessionException {

        Staff staff = getStaff();
        VerifyResult result = null;
        if (orderType == null || orderType == 0) {
            Assert.notNull(mixBillNo, "单据号不能为空");
            Assert.notNull(billType, "单据类型不能为空");
            result = IShipBoxService.verifyBillNo(staff, mixBillNo, billType, openPackageAutoExamine,openAfterConsignBox);
        } else {
            if (StringUtils.isNotBlank(pickingCodeStr)) {
                List<String> pickingCodes = DataUtils.str2List(pickingCodeStr);

                List<Long> waveIds = tradeServiceDubbo.queryWaveIdListByPickingCodes(staff, pickingCodes);
                String newWaveStr = DataUtils.listToStringByLong(waveIds);
                if (StringUtils.isNotBlank(waveIdStr)) {
                    waveIdStr = newWaveStr + "," + waveIdStr;
                } else {
                    waveIdStr = newWaveStr;
                }
            }
            Assert.notNull(waveIdStr, "波次号不能为空");
            result = IShipBoxService.verifyWaveCode(staff, waveIdStr, jitBoxValidate, noJitBoxValidate);
            result.setWaveIdStr(waveIdStr);
            result.setPickingCodeStr(pickingCodeStr);
        }
        return result;
    }

    /**
     * 根据商家编码查询商品
     * @param boundOrder 绑定订单标识 0 未绑定，1 绑定
     * @param sid
     * @param outerId
     * @param orderType 0 按订单， 1 按波次
     * @param waveIdStr 波次号，英文逗号分隔
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/itemInfo", method = RequestMethod.GET)
    @ResponseBody
    public Object boxItemInfo(Integer boundOrder, Long sid, String outerId, Integer orderType, String waveIdStr, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(boundOrder, "绑定订单标识不能为空");

        BoxItem boxItem = null;
        if (orderType == null || orderType == 0) {
            if (boundOrder == 1) {
                Assert.notNull(sid, "系统单号不能为空");
            }
            Assert.notNull(outerId, "商家编码不能为空");
            boxItem = IShipBoxService.queryBoxItem(staff, boundOrder, sid, outerId);
        } else {
            Assert.notNull(waveIdStr, "波次号不能为空");
            Assert.notNull(outerId, "商家编码不能为空");
            boxItem = IShipBoxService.queryWaveBoxItem(staff, waveIdStr, outerId);
        }

        return boxItem;
    }

    /**
     * 点击订单/波次 商品总数，显示订单/波次明细 详情
     * @param sid
     * @param state 状态 0 未装箱 1已装箱 2部分装箱
     * @param orderType 0 按订单， 1 按波次
     * @param waveIdStr 波次号，英文逗号分隔
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/orderItemInfo", method = RequestMethod.GET)
    @ResponseBody
    public Object orderItemInfo(Long sid, Integer state, Integer orderType, String waveIdStr,Boolean querySuit, String api_name) throws SessionException {

        Staff staff = getStaff();
        OrderBox orderBox = null;
        if (orderType == null || orderType == 0) {
            Assert.notNull(sid, "系统单号不能为空");
            orderBox = IShipBoxService.queryOrderItem(staff, sid, state);
        } else {
            Assert.notNull(waveIdStr, "波次号不能为空");
            orderBox = IShipBoxService.queryItemOfWave(staff, waveIdStr,querySuit);
            if(null!=state){
               filterState(orderBox,state);
            }
        }
        return orderBox;
    }

    /**
     * 发货装箱
     * @param params
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public Object confirm(@RequestBody ShipBoxParams params, String api_name) throws SessionException {

        Staff staff = getStaff();
        Assert.notNull(params, "请求参数不能为空");
        Assert.notNull(params.getBoundOrder(), "绑定订单标识不能为空");

        String clientIP = IpUtils.getClientIP(request);
        if (params.getOrderType() == null || params.getOrderType() == 0) {
            if (params.getBoundOrder() == 1) {
                Assert.notNull(params.getSid(), "系统单号不能为空");
            }
            Assert.notNull(params.getBoxNum(), "箱数不能为空");
            Integer maxNum = Optional.ofNullable(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getWmsMaxGenerateBoxCodesNum()).orElse(wmsMaxGenerateBoxCodesNum);
            Assert.isTrue(params.getBoxNum() <= maxNum, "超过最大生成数量" + maxNum);
            checkDetails(params);

            List<String> boxCodeList = IShipBoxService.save(staff, params, clientIP);
            return boxCodeList;
        } else {
            Assert.notNull(params.getWaveIdStr(), "波次号不能为空");
            Assert.notNull(params.getConfirmBox(), "确认装箱标识不能为空");

            if (params.getConfirmBox() != 2) {
                checkDetails(params);
            }

            WaveBox waveBox = IShipBoxService.saveWaveBox(staff, params, clientIP);
            Assert.isTrue(waveBox.isSuccess(), waveBox.getErrMsg());
            return waveBox;
        }
    }

    private void checkDetails(ShipBoxParams params) {
        Assert.notEmpty(params.getDetailList(), "装箱明细不能为空");
        params.getDetailList().forEach(detail -> {
            Assert.notNull(detail.getNum(), "装箱数量不能为空");
            Assert.notNull(detail.getSysItemId(), "sysItemId不能为空");
            Assert.notNull(detail.getSysSkuId(), "sysSkuId不能为空");
        });
    }


    /**
     * 校验箱码
     * @param boxCode
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/verifyBoxCode", method = RequestMethod.GET)
    @ResponseBody
    public Object verifyBoxCode(String boxCode, String api_name) throws SessionException {
        Staff staff = getStaff();
        return IShipBoxService.verifyBoxCode(staff, boxCode);
    }

    @RequestMapping(value = "/update/have/out", method = RequestMethod.GET)
    @ResponseBody
    public Object updateHaveOut(String boxCode, String api_name) throws SessionException {
        Staff staff = getStaff();
        return IShipBoxService.updateHaveOut(staff, boxCode);
    }


    /**
     * 箱明细导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ResponseBody
    public Object export(@RequestBody ShipBoxParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");
        String[] EXCEL_HEADER;
        List<String[]> arrList = new ArrayList<String[]>();
        FileDownloadParam param = new FileDownloadParam();
        String[][] excelHeader;
        String fileName;
        String excelTitle;
        if(BooleanUtils.isTrue(params.getQueryBoxing())){
            EXCEL_HEADER = new String[]{"装箱顺序","箱码","箱状态","商品编码","商品名称","规格名称","装箱数量","打印状态","打印次数","打印时间"};
            arrList.add(EXCEL_HEADER);
            excelHeader = arrList.toArray(new String[arrList.size()][]);
            fileName = new String("已装箱明细记录".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";
            excelTitle = "已装箱明细记录数据";
        }else {
            if (StringUtils.isEmpty(params.getWaveIdStr())) {
                EXCEL_HEADER = new String[]{"序号","商家编码","商品名称/规格别名","总出库数","未装箱数","待装箱数","货位","装箱状态"};
            } else {
                EXCEL_HEADER = new String[]{"序号", "波次号", "商家编码","商品名称/规格别名","总出库数","未装箱数","待装箱数","货位","装箱状态"};
            }
            arrList.add(EXCEL_HEADER);
            excelHeader = arrList.toArray(new String[arrList.size()][]);
            fileName = new String("未装箱明细记录".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";
            excelTitle = "未装箱明细记录数据";
        }
        param.setFileName(fileName);
        param.setExcelTitle(excelTitle);
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo("wms.shipBox.unBoxed.boxed.download.excel").setArgs(new Object[]{staff, param, params}), false);
        return successResponse();
    }

    /**
     * 箱明细
     */
    @RequestMapping(value = "/queryUnBoxedOrBoxed", method = RequestMethod.POST)
    @ResponseBody
    public Object queryUnBoxedOrBoxed(@RequestBody ShipBoxParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        return IShipBoxService.queryBoxDetails(staff,params);
    }

    /**
     * 重装
     */
    @RequestMapping(value = "/dismantleBox", method = RequestMethod.POST)
    @ResponseBody
    public Object dismantleBox(@RequestBody ShipBoxParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        return IShipBoxService.dismantleBox(staff,params);
    }

    /**
     * 校验箱码
     * @param boxCode
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/virtualBox", method = RequestMethod.GET)
    @ResponseBody
    public Object virtualBox(String boxCode, String api_name) throws SessionException {
        Staff staff = getStaff();
        return IShipBoxService.virtualBox(staff, boxCode);
    }


    /**
     * 查询前2条数据
     */
    @RequestMapping(value = "/queryShipBoxes", method = RequestMethod.POST)
    @ResponseBody
    public Object queryShipBoxes(@RequestBody ShipBoxParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        return IShipBoxService.queryShipBoxes(staff,params);
    }

    //按照是否装箱来过滤
    private void filterState( OrderBox orderBox,Integer state){
        if(CollectionUtils.isEmpty(orderBox.getItems())){
            return;
        }
        List<OrderBoxItem> itemList = orderBox.getItems().stream().filter(boxItem -> boxItem.getState().equals(state)).collect(Collectors.toList());
        orderBox.setItems(itemList);

    }


}

package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.wms.ILogisticsTradeService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.model.wms.LogisticsTradeParams;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName LogisticsTradeController.java
 * @Description 销售出库单
 * <AUTHOR>
 * @createTime 2022年12月15日 17:17:00
 */
@RestController
@RequestMapping("/wms/logistics/trade")
public class LogisticsTradeController extends Sessionable {

    @Resource
    private ILogisticsTradeService logisticsTradeService;


    /**
     * 查询销售出库单
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public Object queryTrades(LogisticsTradeParams params, String api_name) throws SessionException {
        Staff staff = getStaff();
        return logisticsTradeService.queryTrades(staff, params);
    }

    /**
     * 查询销售出库单明细（本体）
     */
    @RequestMapping(value = "/detail/query", method = RequestMethod.POST)
    public Object queryOrders(Page page, Long mainId, String api_name) throws SessionException {
        Assert.notNull(mainId, "请选择单据");
        Staff staff = getStaff();
        return logisticsTradeService.queryOrders(staff, page, mainId);
    }

    /**
     * 查询销售出库单（套件明细）
     */
    @RequestMapping(value = "/detail/single/query", method = RequestMethod.POST)
    public Object queryOrderSingles(Page page, Long mainId, Long combineId, String api_name) throws SessionException {
        Assert.notNull(mainId, "请选择单据");
        Assert.notNull(combineId, "请选择单据");
        Staff staff = getStaff();
        return logisticsTradeService.queryOrderSingles(staff, mainId, combineId);
    }


}

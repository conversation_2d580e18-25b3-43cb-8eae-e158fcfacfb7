package com.raycloud.dmj.web.controller.wms.product;

import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.process.suggest.params.ProcessPlanGenerateOrderParam;
import com.raycloud.dmj.domain.process.suggest.params.ProcessSuggestGenerateParam;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.product.params.ProcessPlanBomExportParams;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.product.plan.ProcessPlan;
import com.raycloud.dmj.services.wms.IProgressService;
import com.raycloud.dmj.services.wms.product.plan.IProcessPlanMaterialService;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.product.plan.IProcessPlanOrderService;
import com.raycloud.dmj.services.wms.product.plan.IProcessPlanProductService;
import com.raycloud.dmj.services.wms.product.plan.IProcessPlanService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.ProcessPlanParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 加工计划
 **/
@Controller
@RequestMapping("/wms/product/plan")
public class ProcessPlanController extends WmsInitController {
    @Resource
    private IDownloadCenterService downloadCenterService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IProcessPlanService processPlanService;

    @Resource
    private IProcessPlanProductService processPlanProductService;

    @Resource
    private IProcessPlanOrderService processPlanOrderService;

    @Resource
    private IProcessPlanMaterialService processPlanMaterialService;

    @Resource
    private IProgressService progressService;

    /**
     * 查询
     */
    @RequestMapping(value = "/calculateData")
    @ResponseBody
    public Object calculateData(Long materialWarehouseId, Long productWarehouseId,Long ruleId) throws Exception {
        Assert.notNull(materialWarehouseId, "原料仓库不能为空!");
        Assert.notNull(productWarehouseId, "成品仓库不能为空!");
        Assert.notNull(ruleId, "规则不能为空!");
        Staff staff = getLightStaff();

        ProgressData progressData = new ProgressData();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_CALCULATE), "正在进行加工计划计算，请稍候再试！");
        progressService.setProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_CALCULATE, progressData);
        progressData.setCacheKey(ProgressEnum.PROGRESS_PROCESS_PLAN_CALCULATE.getKey());
        ProcessSuggestGenerateParam param = new ProcessSuggestGenerateParam();
        param.setMaterialWarehouseId(materialWarehouseId);
        param.setProductWarehouseId(productWarehouseId);
        param.setRuleId(ruleId);
        param.setSource(ProcessSuggestGenerateParam.SOURCE_PLAN);
//        processPlanService.generatePlan(staff, param);

        progressData.setProgress(1);
        progressData.setCountAll(100);
        progressService.setProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_CALCULATE, progressData);
        eventCenter.fireEvent(this, new EventInfo("wms.process.plan.calculate").setArgs(new Object[]{staff, param}), false);

        return successResponse();
    }

    /**
     * 查询
     */
    @RequestMapping(value = "/begin")
    @ResponseBody
    public Object begin(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        Staff staff = getLightStaff();
        processPlanService.beginPlan(staff, planId);
        return successResponse();
    }

    @RequestMapping(value = "/updatePlanProduct")
    @ResponseBody
    public Object updatePlanProduct(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Assert.notEmpty(params.getProductDetails(), "修改成品数据不能为空!");
        Staff staff = getLightStaff();
        processPlanProductService.updatePlanProduct(staff, params);
        return successResponse();
    }

    @RequestMapping(value = "/replenishPlanOrder")
    @ResponseBody
    public Object replenishPlanOrder(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Assert.notEmpty(params.getOrderDetails(), "补录数据不能为空!");
        Staff staff = getLightStaff();
        processPlanOrderService.replenishPlanOrder(staff,params);
        return successResponse();
    }

    @RequestMapping(value = "/getProcessPlanInfo")
    @ResponseBody
    public Object getProcessPlanInfo(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        return processPlanService.queryById(getLightStaff(), planId);
    }

    @RequestMapping(value = "/replenish")
    @ResponseBody
    public Object replenish(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        Staff staff = getLightStaff();
        processPlanService.replenishPlan(staff, planId);
        return successResponse();
    }

    @RequestMapping(value = "/complete")
    @ResponseBody
    public Object complete(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        Staff staff = getLightStaff();
        processPlanService.completePlan(staff, planId);
        return successResponse();
    }

    @RequestMapping(value = "/getCompletePlanPage")
    @ResponseBody
    public Object getCompletePlanPage(ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "计划id不能为空");
        Staff staff = getLightStaff();
        return processPlanService.getCompletePlanPage(staff, params);
    }

    @RequestMapping(value = "/checkMaterItemTag")
    @ResponseBody
    public Object checkMaterItemTag(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        Staff staff = getLightStaff();
        processPlanService.checkMaterItemTag(staff, planId);
        return successResponse();
    }

    /**
     * 刷新原料明细，商品标签信息
     * @param idStr
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/refreshMaterialItemTag")
    @ResponseBody
    public Object refreshMaterialItemTag(Long planId, String api_name) throws Exception {
        Assert.notNull(planId, "计划id不能为空");
        Staff staff = getLightStaff();
        processPlanMaterialService.refreshMaterialItemTag(staff,planId);
        return successResponse();
    }

    /**
     * 生成加工单
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/generateOrder")
    @ResponseBody
    public Object generateOrder(ProcessPlanGenerateOrderParam param) throws Exception {
        Assert.notNull(param.getPlanId(), "计划id不能为空");
        Staff staff = getLightStaff();

        ProgressData progressData = new ProgressData();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_GENERATE_ORDER), "正在进行加工计划生成加工单，请稍候再试！");
        progressService.setProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_GENERATE_ORDER, progressData);
        progressData.setCacheKey(ProgressEnum.PROGRESS_PROCESS_PLAN_GENERATE_ORDER.getKey());
        progressData.setProgress(1);
        progressData.setCountAll(100);
        progressService.setProgress(staff, ProgressEnum.PROGRESS_PROCESS_PLAN_GENERATE_ORDER, progressData);
        eventCenter.fireEvent(this, new EventInfo("wms.process.plan.generateOrder").setArgs(new Object[]{staff, param}), false);
        return successResponse();
    }

    @RequestMapping(value = "/getCurPlan")
    @ResponseBody
    public Object getCurPlan(ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Staff staff = getLightStaff();
        return processPlanService.getCurPlan(staff);
    }

    @RequestMapping(value = "/getPlanProductPage")
    @ResponseBody
    public Object getPlanProductPage(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Staff staff = getLightStaff();
        return processPlanProductService.getPlanProductPage(staff, params);
    }

    @RequestMapping(value = "/getPlanOrderPage")
    @ResponseBody
    public Object getPlanOrderPage(ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Staff staff = getLightStaff();
        return processPlanOrderService.getPlanOrderPage(staff, params);
    }

    @RequestMapping(value = "/getPlanMaterialPage")
    @ResponseBody
    public Object getPlanMaterialPage(ProcessPlanParams params, String api_name) throws Exception {
        Assert.notNull(params, "参数不能为空");
        Staff staff = getLightStaff();
        return processPlanMaterialService.queryProcessPlanMaterials(staff, params);
    }

    /**
     * 导出加工单据
     */
    @RequestMapping(value = "/exportProcessPlanMaterial", method = RequestMethod.POST)
    @ResponseBody
    public Object exportBom(@RequestBody ProcessPlanBomExportParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        Assert.isNull(downloadCenterService.queryNoExportFinish(staff, condition), "模块[" + EnumDownloadCenterModule.WMS.getValue() + "]已经有在导出中的任务，请稍后再导出!");
        eventCenter.fireEvent(this, new EventInfo("wms.process.plan.export.bom").setArgs(new Object[]{staff, params}), null);
        return successResponse();
    }

    /**
     * 可拣选加工成品导出
     *
     * @param params   ProcessPlanParams#planId 加工计划 Id
     * @param api_name
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/exportCanPickNum", method = RequestMethod.POST)
    public Object canPickExcelExport(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        condition.setStaffId(staff.getId());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        Assert.notNull(params.getPlanId(), "加工计划不能为空！");
        String[] EXCEL_HEADER = new String[]{"序号", "主商家编码", "商家编码", "规格名称", "拣选货位", "拣选数量", "唯一码", "补录加工数"};
        List<String[]> arrList = new ArrayList<>();
        arrList.add(getPickExcelSecRow(staff, params.getPlanId()));
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("加工计划可拣选单据".getBytes(), "utf-8") + org.apache.commons.lang3.time.DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("加工计划可拣选成品数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("wms.product.plan.pick.export").setArgs(new Object[]{staff, param, params}), false);
        return successResponse();
    }

    private String[] getPickExcelSecRow(Staff staff, Long planId) {
        ProcessPlan processPlan = processPlanService.queryById(staff, planId);
        if (processPlan == null) {
            throw new IllegalArgumentException("根据加工计划Id未查询到信息！");
        }
        return new String[]{"加工计划编号" + processPlan.getCode() + " 订单过滤规则：" + " 加工类型：" + processPlan.getTypeName() + " 成品仓：" + processPlan.getProductWarehouseName() + " 原料仓：" + processPlan.getMaterialWarehouseName() + " 加工状态：" + processPlan.getStatusName()};
    }

    /**
     * 导出原料
     *
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/exportProcessPlanMaterialSummary", method = {RequestMethod.POST, RequestMethod.GET})
    public Object exportProcessPlanMaterial(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Staff staff = getLightStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        eventCenter.fireEvent(this, new EventInfo("export.process.plan.materialOrOrder").setArgs(new Object[]{staff, params, CommonConstants.VALUE_NO}), false);

        return successResponse();
    }

    /**
     * 导出加工订单
     *
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/exportProcessPlanOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public Object exportProcessPlanOrder(@RequestBody ProcessPlanParams params, String api_name) throws Exception {
        Staff staff = getLightStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        eventCenter.fireEvent(this, new EventInfo("export.process.plan.materialOrOrder").setArgs(new Object[]{staff, params, CommonConstants.VALUE_YES}), false);

        return successResponse();
    }
}

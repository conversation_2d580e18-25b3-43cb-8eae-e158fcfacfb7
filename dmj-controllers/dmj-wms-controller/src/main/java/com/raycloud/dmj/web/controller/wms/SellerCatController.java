package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.ShopSellerCat;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.items.IShopSellerCatService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.items.ItemModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 店铺类目控制器
 *
 * <AUTHOR>
 */

@Controller  ///wms
@RequestMapping("/wms/item/cat")
public class SellerCatController extends WmsInitController {

  @Resource
  private IShopSellerCatService shopSellerCatService;

  @Resource
  private IShopService shopService;

  @RequestMapping(value = "/list", method = RequestMethod.GET)
  @ResponseBody
  public Object single(
      Long userId,
      String api_name) throws SessionException {
    Staff staff = getStaff();
    List<ShopSellerCat> result = Lists.newLinkedList();

    if (userId != null && userId != -1) {
      Map<Long, User> users = staff.getUserIdMap();
      if (users.containsKey(userId)) {
        User user = users.get(userId);
        if (user.getActive() == 1) {
          List<ShopSellerCat> list = shopSellerCatService.queryAll(user, false);
          result.addAll(list);
        }
      } else {
        throw new IllegalArgumentException("当前店铺无法找到");
      }
      List<Shop> shops = null;
      if (CollectionUtils.isNotEmpty(result)) {
        shops = shopService.queryByCompanyId(staff);
      }
      return ItemModel.renderToView(result, shops);
    } else if (userId != null && userId == -1) {
      return single(api_name);
    } else {
      for (User user : staff.getUserIdMap().values()) {
        List<ShopSellerCat> list = shopSellerCatService.queryAll(user, false);
        result.addAll(list);
      }
      List<Shop> shops = null;
      if (CollectionUtils.isNotEmpty(result)) {
        shops = shopService.queryByCompanyId(staff);
      }
      return ItemModel.renderToView(result, shops);
    }
  }



  @RequestMapping(value = "/sys/list", method = RequestMethod.GET)
  @ResponseBody
  public Object single(String api_name) throws SessionException {
    Staff staff = getStaff();
    List<ShopSellerCat> result = Lists.newLinkedList();
    List<ShopSellerCat> list = shopSellerCatService.queryAllSys(staff);
    result.addAll(list);
    List<Shop> shops = null;
    if (CollectionUtils.isNotEmpty(result)) {
      shops = shopService.queryByCompanyId(staff);
    }
    return ItemModel.renderToView(result, shops);
  }



}

package com.raycloud.dmj.web;

import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.web.exception.ExceptionParams;
import com.raycloud.dmj.web.exception.ResultBean;
import com.raycloud.dmj.web.exception.annotation.ErpExceptionHandler;
import com.raycloud.dmj.web.exception.chain.ExceptionHandlerChain;
import com.raycloud.dmj.web.exception.handler.ExceptionHandler;
import org.springframework.stereotype.Component;

/**
 * 自定义异常处理
 *
 * <AUTHOR>
 * @date 2018/8/29 14:05
 */
@Component
@ErpExceptionHandler(order = "100")
public class CustomExceptionHandler implements ExceptionHandler {
    @Override
    public ResultBean handlerException(Throwable e, ExceptionParams params, ExceptionHandlerChain chain) {
        if (e instanceof CustomException) {
            CustomException ce = (CustomException) e;
            return ResultBean.ofError(ce.getResultCode(), params.getApi_name(), params.getClueId(), String.valueOf(ce.getSubCode()), ce.getMessage()).setData((ce.getData()));
        }
        return chain.doHandler(e, params);
    }
}

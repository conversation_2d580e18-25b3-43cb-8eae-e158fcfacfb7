package com.raycloud.dmj.web;

import com.raycloud.dmj.web.exception.ExceptionParams;
import com.raycloud.dmj.web.exception.ResultBean;
import com.raycloud.dmj.web.exception.annotation.ErpExceptionHandler;
import com.raycloud.dmj.web.exception.chain.ExceptionHandlerChain;
import com.raycloud.dmj.web.exception.handler.ExceptionHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

/**
 * 运行包装异常处理
 * <AUTHOR>
 * @date 2018/9/19 13:30
 */
@Component
@ErpExceptionHandler(order = "200")
public class RuntimeWrapperExceptionHandler implements ExceptionHandler {
    @Override
    public ResultBean handlerException(Throwable e, ExceptionParams params, ExceptionHandlerChain chain) {
        if (e instanceof RuntimeException) {
            if (e.getCause() == null) {
                return ResultBean.ofError(params, e.getMessage());
            } else {
                Throwable rootCause = ExceptionUtils.getRootCause(e);
                return ResultBean.ofError(params, rootCause.getMessage());
            }
        }
        return chain.doHandler(e, params);
    }
}

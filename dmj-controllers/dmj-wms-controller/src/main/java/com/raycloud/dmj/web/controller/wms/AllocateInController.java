package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.AllocateInReceipt;
import com.raycloud.dmj.domain.wms.AllocateInReceiptDetail;
import com.raycloud.dmj.domain.wms.AllocateOutReceiptDetail;
import com.raycloud.dmj.domain.wms.AllocateTask;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.AllocateTaskType;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IAllocateInShelveService;
import com.raycloud.dmj.utils.WmsAllocateUtils;
import com.raycloud.dmj.utils.WmsOpLogHelper;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.idempotent.IdempotentCache;
import com.raycloud.dmj.web.model.wms.AllocateQueryParams;
import com.raycloud.dmj.web.utils.Md5Encrypt;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2018/8/17 10:12
 */
@Controller
@RequestMapping("/wms/allocate/in")
public class AllocateInController extends WmsInitController {
    
    private static final Logger LOGGER = Logger.getLogger(AllocateInController.class);

    @Resource
    private IAllocateInShelveService allocateInService;
    
    @Resource
    private IdempotentCache dempotentCache;
    
    @Resource
    private IEventCenter eventCenter;
    
    @Resource
    protected WmsCacheBusiness wmsCacheBusiness;


    /**
     * 调拨入库单列表
     * @param params
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(AllocateQueryParams params, Page page, String api_name) throws Exception {
        return allocateInService.queryInPageList(getLightStaff(), params, page);
    }

    /**
     * 根据调拨入库id查询调出单据
     * @param id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/detail")
    @ResponseBody
    public Object queryInReceiptsByTaskId(Long id, String api_name) throws Exception {
        Assert.notNull(id, "请选择调拨入库单！");
        return allocateInService.queryInById(getLightStaff(), id);
    }

    /**
     * 根据调拨任务id查询调出单据
     * @param taskId
     * @param withDetail
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/task/details")
    @ResponseBody
    public Object queryInReceiptsByTaskId(Long taskId, Boolean withDetail, String api_name) throws Exception {
        Assert.notNull(taskId, "请选择调拨单！");
        return allocateInService.queryInByTaskId(getLightStaff(), taskId, withDetail);
    }

    /**
     * 修改调拨入库单
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#receipt.code", content = "'修改调拨入库单' + #receipt.code + '备注为：' + #receipt.remark")
    public Object update(AllocateInReceipt receipt, String api_name) throws Exception {
        Assert.notNull(receipt.getId(), "请选择调拨入库单！");
        Assert.notNull(receipt.getCode(), "请选择调拨入库单！");
        Assert.notNull(receipt.getRemark(), "请输入备注！");

        allocateInService.updateInReceipt(getLightStaff(), receipt);
        return successResponse();
    }

    /**
     * 调拨入库单收货
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/receive", method = RequestMethod.POST)
    @ResponseBody
    public Object receive(@RequestBody AllocateInReceipt receipt, String api_name) throws Exception {
        Assert.notNull(receipt.getId(), "请选择调拨入库单！");
        Assert.notNull(receipt.getCode(), "请选择调拨入库单！");
        Assert.notEmpty(receipt.getDetails(), "收获明细不能为空！");
        AllocateInReceipt receive = null;
        Staff staff = getLightStaff();
        int expireTime = 10 * 60;
        String cacheKey = "allocate_in" + "_" + staff.getCompanyId() + "_" + receipt.getId() + "_" + receipt.getPageId();
        LOGGER.debug(LogHelper.buildLog(staff, String.format("调拨入库,调入单code %s,cacheKey：%s", receipt.getCode(), cacheKey)));
        if (receipt.getPageId() != null) {
            dempotentCache.checkTokenPreEnd(cacheKey,null, expireTime);
            try {
                receive = allocateInService.receive(getLightStaff(), receipt);
                dempotentCache.tryPreEnd(cacheKey,null, expireTime);
            } catch (Exception e) {
                dempotentCache.catchPreEnd(cacheKey,null, expireTime);
                throw e;
            }
        } else {
            receive = allocateInService.receive(getLightStaff(), receipt);
        }
        return receive;
    }
    


    /**
     * 获取调拨收货单合并明细
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/merge/items")
    @ResponseBody
    public Object mergeItems(String ids, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨入库单！");
        Staff staff = getLightStaff();
        final List<Long> idList = ArrayUtils.toLongList(ids);
        List<AllocateInReceipt> receipts = allocateInService.queryInByIds(staff, idList, true);
        Long warehouseId = null;
        Collections.sort(receipts, new Comparator<AllocateInReceipt>() {
            @Override
            public int compare(AllocateInReceipt pre, AllocateInReceipt next) {
                return idList.indexOf(pre.getId()) - idList.indexOf(next.getId());
            }
        });
        List<String> codes = Lists.newArrayListWithCapacity(receipts.size());
        List<AllocateInReceiptDetail> details = Lists.newArrayList();

        for (AllocateInReceipt receipt : receipts) {
            if (warehouseId == null) {
                warehouseId = receipt.getInWarehouseId();
            }
            Assert.isTrue(warehouseId.equals(receipt.getInWarehouseId()), "亲选择相同仓库进行操作！");
            details.addAll(receipt.getDetails());
            codes.add(receipt.getCode());
        }

        long totalNum = 0L;
        Map<String, AllocateInReceiptDetail> itemKeyDetailMap = Maps.newHashMapWithExpectedSize(details.size());
        for (AllocateInReceiptDetail detail : details) {
            String key = WmsAllocateUtils.buildUniqueKey(detail);
            AllocateInReceiptDetail receiptDetail = itemKeyDetailMap.get(key);
            totalNum += detail.getInNum();
            if (receiptDetail == null) {
                itemKeyDetailMap.put(key, detail);
            } else {
                receiptDetail.setGoodNum(receiptDetail.getGoodNum() + detail.getGoodNum());
                receiptDetail.setBadNum(receiptDetail.getBadNum() + detail.getBadNum());
                receiptDetail.setInNum(receiptDetail.getInNum() + detail.getInNum());
            }
        }

        List<AllocateInReceiptDetail> receiptDetails = Lists.newArrayList(itemKeyDetailMap.values());
        Collections.sort(receiptDetails, new Comparator<AllocateInReceiptDetail>() {
            @Override
            public int compare(AllocateInReceiptDetail pre, AllocateInReceiptDetail next) {
                return pre.getOuterId().compareTo(next.getOuterId());
            }
        });
        AllocateInReceipt receipt = receipts.get(0);
        receipt.setIds(idList);
        receipt.setCodes(codes);
        receipt.setDetails(receiptDetails);
        receipt.setInNum(totalNum);
        return receipt;
    }


    /**
     * 批量收货
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batch/receive", method = RequestMethod.POST)
    @ResponseBody
    public Object batchReceive(@RequestBody AllocateInReceipt receipt, String api_name) throws Exception {
        Assert.notEmpty(receipt.getIds(), "请选择收货单！");
        Assert.notEmpty(receipt.getCodes(), "请选择收货单！");
        Assert.isTrue(receipt.getIds().size() == receipt.getCodes().size(), "参数个数不一致！");
        List<BatchExecuteResult<AllocateInReceipt>> results = null;
        Staff staff = getLightStaff();
        int expireTime = 10 * 60;
        if (receipt.getPageId() != null) {
            String cacheKey = "allocate_in" + "_" + staff.getCompanyId() + "_" + Md5Encrypt.md5(StringUtils.join(receipt.getIds(), "_")) + "_" + receipt.getPageId();
            LOGGER.debug(LogHelper.buildLog(staff, String.format("调拨入库,调入单ids %s,cacheKey：%s", receipt.getIds(), cacheKey)));
            dempotentCache.checkTokenPreEnd(cacheKey,null, expireTime);
            try {
                results = allocateInService.batchReceive(getLightStaff(), receipt.getIds(), receipt);
                handleBatchResults(results);
                dempotentCache.tryPreEnd(cacheKey,null, expireTime);
            } catch (Exception e) {
                dempotentCache.catchPreEnd(cacheKey,null, expireTime);
                throw e;
            }
        } else {
            results = allocateInService.batchReceive(getLightStaff(), receipt.getIds(), receipt);
            handleBatchResults(results);
        }
        return results;
    }
    
}

package com.raycloud.dmj.web.controller.wms;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.trade.WmsTradeRepairBusiness;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.constants.WmsOpLogConstants;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.domain.ItemTraceMessage;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.ec.WmsStallBindListener;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IDepotService;
import com.raycloud.dmj.services.wms.IGoodsSectionService;
import com.raycloud.dmj.services.wms.IGoodsSectionSkuService;
import com.raycloud.dmj.services.wms.IWmsStockOpService;
import com.raycloud.dmj.services.wms.allocategoods.IAllocateGoodsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsOpLogUtils;
import com.raycloud.dmj.utils.wms.WmsBatchProductUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.*;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by guzy on 15/12/21.
 */
@Controller
@RequestMapping("/wms/goodsSectionInventory/")
@LogTag(value = "wms")
public class GoodsSectionInventoryController extends WmsInitController {

    @Resource
    private IGoodsSectionSkuService goodsSectionSkuService;

    @Resource
    private IWmsStockOpService wmsStockOpService;

    @Resource
    private IGoodsSectionService goodsSectionService;

    @Autowired
    private IDepotService depotService;

    @Resource
    private WmsHelpBusiness wmsHelpBusiness;

    @Resource
    IAllocateGoodsService allocateGoodsService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IItemTraceService itemTraceService;

    @Resource
    private WmsItemBusiness wmsItemBusiness;

    @Resource
    private WmsTradeRepairBusiness wmsTradeRepairBusiness;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private Configurable config;


    /**
     * 根据货位id与商品系统id查询锁定记录列表
     *
     * @param goodsSectionId 货位id
     * @param sysSkuId       商品系统id
     * @param warehouseId    仓库编号
     * @param isGoodsSection 是否是货位锁定明细，默认为true
     */
    @ResponseBody
    @RequestMapping(value = "lockList", method = RequestMethod.GET)
    public Object getLockList(Long warehouseId, Long goodsSectionId, Long sysSkuId, Long sysItemId, String batchNo, Date productTime,Boolean isGoodsSection, Page page) throws SessionException {
        isGoodsSection = isGoodsSection == null ? true : isGoodsSection;
        Staff staff = getLightStaff();
        List<GoodsSectionSkuLockVo> dataList = goodsSectionSkuService.selectByGoodsSectionIdAndSkuId(staff, warehouseId, goodsSectionId, sysSkuId, sysItemId, batchNo, productTime,isGoodsSection, page);
        Integer count = goodsSectionSkuService.selectByGoodsSectionIdAndSkuIdCount(staff, warehouseId, goodsSectionId, sysSkuId, sysItemId, batchNo, productTime, isGoodsSection, page);

        Integer depotCount = 0;
        count = count == null ? 0 : count;
        boolean openDepot = staff.getConf().getOpenDepot();

        //只有开启返库且仓库库存才显示返库锁定，目前仓库库存页面隐藏
        if (openDepot && !isGoodsSection && warehouseId != null) {
            depotCount = depotService.selectWareDepotLockCount(staff, sysItemId, sysSkuId, warehouseId);
            depotCount = depotCount == null ? 0 : depotCount;

            if (dataList.size() < page.getPageSize() && depotCount > 0) {
                Page depotPage = getSecondPage(page, count, depotCount);
                List<DepotOrderDetail> depotList = depotService.selectWareDepotLockList(staff, sysItemId, sysSkuId, warehouseId, depotPage);
                for (DepotOrderDetail depot : depotList) {
                    GoodsSectionSkuLockVo vo = new GoodsSectionSkuLockVo();
                    vo.setSourceKey(String.valueOf(depot.getSid()));
                    vo.setNum(depot.getDepotNum().intValue());
                    vo.setLockType("返库锁定");
                    vo.setWarehouseId(depot.getWarehouseId());
                    dataList.add(vo);
                }
            }
        }

        wrapperWithWarehouseName(dataList);
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("dataList", dataList);
        data.put("totalCount", count);

        //计算剩余数量
        if (page != null) {
            int remainCount = count + depotCount - page.getPageNo() * page.getPageSize();
            data.put("remainCount", remainCount < 0 ? 0 : remainCount);
        }
        if (openOrClose() && WmsUtils.isOldWms(staff) && !WmsUtils.isOpenDepot(staff) && CollectionUtils.isNotEmpty(dataList)) {
            List<GoodsSectionSkuLockVo> goodsSectionSkuLockVos = dataList.stream().filter(allocateWaveVo -> allocateWaveVo.getCreated() != null && !allocateWaveVo.getCreated().after(DateUtil.addDate(new Date(), -10))).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(goodsSectionSkuLockVos)){
                return data;
            }
            wmsTradeRepairBusiness.repairResume(staff, true, queryParams());
        }
        if (WmsUtils.isOldWms(staff) && !WmsUtils.isOpenDepot(staff)) {
            wmsTradeRepairBusiness.repairOneStripLockNum(staff, warehouseId,goodsSectionId, sysItemId, sysSkuId, batchNo, productTime);
        }
        return data;
    }

    private Page getSecondPage(Page firstPage, long firstCount, long secondCount) {
        long offset = firstPage.getPageNo() * firstPage.getPageSize();
        if (firstCount < offset) {
            long total = firstCount + secondCount;
            Page secondPage = new Page().setPageSize(firstPage.getPageSize());
            long diff = offset - firstCount;
            if (diff < firstPage.getPageSize()) {
                secondPage.setPageSize((int) (Math.min(offset, total) - firstCount));
                secondPage.setStartRow(0);
            } else {
                secondPage.setPageSize(total > offset ? firstPage.getPageSize() : (int) (total - firstPage.getStartRow()));
                secondPage.setStartRow((int) (firstPage.getStartRow() - firstCount));
            }
            return secondPage;
        }
        return null;
    }

    private void wrapperWithWarehouseName(List<GoodsSectionSkuLockVo> vos) {
        if (vos != null && vos.size() > 0) {
            List<Long> warehouseIds = Lists.newArrayListWithCapacity(vos.size());
            for (GoodsSectionSkuLockVo vo : vos) {
                if (!warehouseIds.contains(vo.getWarehouseId())) {
                    warehouseIds.add(vo.getWarehouseId());
                }
            }
            List<Warehouse> warehouses = wmsHelpBusiness.queryWarehouseByWarehouseIds(warehouseIds);
            for (GoodsSectionSkuLockVo vo : vos) {
                for (Warehouse warehouse : warehouses) {
                    if (vo.getWarehouseId() != null && vo.getWarehouseId().longValue() == warehouse.getId()) {
                        vo.setWarehouseName(warehouse.getName());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 释放货位
     *
     * @param ids 货位ids
     */
    @RequestMapping(value = "releaseBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSectionBatch(@RequestParam("ids") String ids, String codes, String api_name) throws SessionException {
        Assert.hasText(ids, "请选择货位！");
        Staff staff = getStaff();
        Long[] idArr = ArrayUtils.toLongArray(ids);
        Assert.isTrue(idArr.length > 0, "请选择货位！");
        goodsSectionService.releaseGoods(staff, Arrays.asList(idArr));
        writeOpLog(staff, "releaseGoodsSectionBatch", String.format("%s，货位：%s", idArr.length == 1 ? "释放货位" : "批量释放", codes), String.format("id列表:%s", ids));
        return successResponse();
    }

    /**
     * 释放货位
     *
     * @param id 货位id
     */
    @RequestMapping(value = "release", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSection(@RequestParam("id") Long id, String api_name) throws SessionException {
        Staff staff = getStaff();
        GoodsSectionVo goodsSectionVo = goodsSectionService.releaseGoodsSectionInventory(staff, id);
        writeOpLog(staff, "releaseGoodsSection", String.format("释放货位，货位:%s", goodsSectionVo.getCode()), "货位id:" + id + ",sysItemId:" + goodsSectionVo.getSku().getSysItemId() + ",sysSkuId:" + goodsSectionVo.getSku().getSysSkuId());
        return goodsSectionVo;
    }

    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "list", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @AccessShield(value = "1002")
    public Object list(@RequestBody GoodsSectionInventorySearchParams params, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        params.setShowTask(true);
        params.setShowAllocateNum(Boolean.TRUE);
        // 批次有效期都是模糊查询
        params.setBatchQueryType(0);
        params.setProductQueryType(0);
//        params.setSplit(false);
        InventoryResult<GoodsSectionVo> list = goodsSectionSkuService.list(staff, params, params.getPage());
        formatGoodsSectionCode(staff, list.getList());
        buildHighlights(list.getList(), params.getCode(), params.getQueryType());
        list.setPage(params.getPage());
        return list;
    }

    /**
     * 查询获取商品已配未拣配货记录
     * 查询条件：
     * 1. 波次类型(picking_type)：一单一件(1)，一单多件(2)，采退(4)，补货(5)，档口(6)，加工(7)，订单拣选(0)
     * 2. 波次状态(waveStatus)：未拣选(0)，拣选中(1)
     * 3. 波次号（waveId）
     *
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/queryGoodsSectionAllocateNum")
    @ResponseBody
    public Object queryGoodsSectionAllocateNum(@RequestBody AllocateWaveVo params, String api_name) throws SessionException {
        Assert.notNull(params.getWarehouseId(), "仓库Id不能为空！");
        Assert.notNull(params.getSysItemId(), "主商家Id不能为空！");
        Assert.notNull(params.getGoodsSectionId(), "货位Id不能为空！");
        if (params.getPage() == null) {
            params.setPage(new Page(Page.DEFAULT_PAGE_NUM, Page.DEFAULT_PAGE_SIZE));
        }
        PageList<AllocateWaveVo> pageList = allocateGoodsService.queryAllocateNumPage(getLightStaff(), params);
        if (pageList != null && CollectionUtils.isNotEmpty(pageList.getList())) {
            List<AllocateWaveVo> allocateWaveVos = pageList.getList();
            for (AllocateWaveVo waveVo : allocateWaveVos) {
                // 单据号，sid，销货单号，采退单号，加工单号
                if (StringUtils.isEmpty(waveVo.getBussiCode()) && !PickingType.REPLENISH.getValue().equals(waveVo.getPickingType())) {
                    waveVo.setBussiCode(waveVo.getSid() == null ? "" : String.valueOf(waveVo.getSid()));
                }
                if(StringUtils.isNotEmpty(waveVo.getBussiCode()) && waveVo.getBussiCode().contains("PDD")) {
                    waveVo.setPickingType(PickingType.PICK_INVENTORY.getValue());
                }
                // 订单拣选特殊处理
                if (waveVo.getPickingType() == null) {
                    waveVo.setPickingType(PickingType.getTypeByAllocateType(waveVo.getAllocateType()));
                    waveVo.setWaveId(null);
                    waveVo.setWaveStatus(null);
                }
            }
            Staff staff = getLightStaff();
            if (!black(staff)&&openOrClose() && WmsUtils.isNewWms(staff) && CollectionUtils.isNotEmpty(allocateWaveVos)) {
                List<AllocateWaveVo> allocateWaveVoStream = allocateWaveVos.stream().filter(allocateWaveVo -> allocateWaveVo.getCreated() != null && !allocateWaveVo.getCreated().after(DateUtil.addDate(new Date(), -10))).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(allocateWaveVoStream)){
                    return pageList;
                }
                wmsTradeRepairBusiness.repairAllocateResume(staff, true, queryParams());
            }
        }
        return pageList;
    }

    private Map<String, Object> queryParams(){
        Map<String, Object> param = Maps.newHashMap();
        param.put("needCommon", true);
        param.put("beginTime", 10);
        return param;
    }



    private Boolean openOrClose() {
        String openFlag = config.getProperty("OPEN_ORDER_STOCK_OR_ALLOCATE_CONFIG");
        return StringUtils.isNoneBlank(openFlag) && Boolean.parseBoolean(openFlag);
    }

    private Boolean black(Staff staff) {
        String companyIds = config.getProperty("trade_allocate_num_repair_black_company_ids");
        if (StringUtils.isBlank(companyIds)) {
            return false;
        }
        List<String> ids = ArrayUtils.toStringList(companyIds);
        if (CollectionUtils.isNotEmpty(ids) && ids.contains(staff.getCompanyId().toString())) {
            return true;
        }
        return false;
    }

    /**
     * 查询货位商品数据,用于弹出框 不限定商品
     */
    @RequestMapping("/listSimple")
    @ResponseBody
    public Object listSimple(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (StringUtils.isNotEmpty(params.getGoodsSectionCode())) {
            params.setQueryType("code");
            params.setCode(params.getGoodsSectionCode());
        }
        //上架填入货位,只需要已启用的货位
        params.setActiveStatus(1);
        InventoryResult<GoodsSectionVo> result = goodsSectionSkuService.list(staff, params, page);
        formatGoodsSectionCode(staff, result.getList());
        result.setPage(page);
        return result;
    }

    /**
     * 查询货位商品数据,用于弹出框 限定商品
     */
    @RequestMapping(value = "listForOp", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object listForOp(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if(StringUtils.isNoneBlank(params.getRegionTypes())){
            params.setStockRegionTypes(ArrayUtils.toIntegerListPosition(params.getRegionTypes()));
        }
        PageList<GoodsSectionVo> pageList = goodsSectionSkuService.listForOp(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        return pageList;
    }

    /**
     * 根据货位编码查询是否存在货位
     * */
    @RequestMapping(value = "checkSectionCode", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object checkSectionCode(GoodsSectionInventorySearchParams params,String api_name) throws SessionException{
        Staff staff = getLightStaff();
        GoodsSectionVo goodsSectionVo = goodsSectionSkuService.checkSectionCode(staff, params);
        if (null != goodsSectionVo) {
            logger.debug(LogHelper.buildLog(staff, String.format("根据货位编码查询是否存在，货位Id：%s", goodsSectionVo.getId())));
        }
        return goodsSectionVo;
    }


    /**
     * 仓库库存
     *
     * @param params 搜索参数
     * @param page   分页参数
     */
    @ResponseBody
    @RequestMapping(value = "list/byWarehouse", method = {RequestMethod.GET, RequestMethod.POST})
    public Object listByWarehouse(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getStaff();
        PageListBase<GoodsSectionSkuVo> pageList = goodsSectionSkuService.listByWarehouse(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        return pageList;
    }

    /**
     * 商品货位
     *
     * @param params 搜索参数
     * @param page   分页参数
     */
    @AccessShield(value = "1119", extendsType = false)
    @ResponseBody
    @RequestMapping(value = "list/byGoods", method = {RequestMethod.GET, RequestMethod.POST})
    public Object listByGoods(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getStaff();
        PageListBase<GoodsSectionSkuVo> pageList = goodsSectionSkuService.listByGoods(getStaff(), params, page);
        WmsConfig config = wmsHelpBusiness.getWmsConfig(staff);
        if (BooleanUtils.isTrue(config.getOpenGsCodeDisplay())) {
            for (GoodsSectionSkuVo display : pageList.getList()) {
                if (StringUtils.isNotBlank(display.getGoodsSectionCode())) {
                    String[] codes = display.getGoodsSectionCode().split(",");
                    StringBuffer sb = new StringBuffer();
                    for (String code : codes) {
                        sb.append(",").append(WmsUtils.encodeGsCode(config, code));
                    }
                    display.setGoodsSectionCode(sb.substring(1));
                }
            }
        }
        pageList.setPage(page);
        return pageList;
    }

    /**
     * 往库位增加商品
     *
     * @param skuCheckVo 盘点vo
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#skuCheckVo.goodsSectionId + '_' + #skuCheckVo.sysItemId + '_' + #skuCheckVo.sysSkuId ", content = "'初始盘点，货位:'+#skuCheckVo.goodsSectionCode ", enableArgs = "true")
    public Object add(SkuCheckVo skuCheckVo, String api_name) throws Exception {
        Assert.notNull(skuCheckVo.getNum(), "盘点数量不能为空！");
        Assert.isTrue(skuCheckVo.getNum() >= 0L, "盘点数量不能小于0！");

        Staff staff = getStaff();
        skuCheckVo.setOpType("add");
        // 记录商品初始盘点
        recordCheckLog(skuCheckVo, staff);
        return executeCheck(staff, skuCheckVo);
    }

    /**
     * 记录商品盘点日志
     * @param skuCheckVo
     * @param staff
     */
    private void recordCheckLog(SkuCheckVo skuCheckVo, Staff staff) {
        ItemTraceMessage message = new ItemTraceMessage();
        message.setSectionCode(skuCheckVo.getGoodsSectionCode());
        message.setNum(skuCheckVo.getNum());
        message.setRemark(String.format("盘点后数量：%d", skuCheckVo.getNum()));
        WmsOpLogUtils.assembleCommon(staff, message, WmsOpLogConstants.CHECK_PATH, ItemTraceActionEnum.WMS_CHECK);
        message.setSysItemId(skuCheckVo.getSysItemId());
        message.setSysSkuId(skuCheckVo.getSysSkuId());
        message.setProductTime(WmsBatchProductUtils.productTimeShow(skuCheckVo.getProductTime()));
        message.setBatchNo(skuCheckVo.getBatchNo());
        message.setOuterId(skuCheckVo.getOuterId());
        WmsOpLogUtils.checkAndSetOuterId(staff, message, wmsItemBusiness);
        itemTraceService.recordEc(staff, message);
    }

    /**
     * 往库位增加商品
     *
     * @param skuCheckVo 盘点vo
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#skuCheckVo.goodsSectionId + '_' + #skuCheckVo.sysItemId + '_' + #skuCheckVo.sysSkuId ", content = "'盘点更新数量，货位:'+#skuCheckVo.goodsSectionCode ", enableArgs = "true")
    public Object update(SkuCheckVo skuCheckVo, String api_name) throws Exception {
        Assert.notNull(skuCheckVo.getNum(), "在架数不能为空！");
        Assert.isTrue(skuCheckVo.getNum() >= 0L, "货位在架数不能小于0！");

        skuCheckVo.setOpType("update");
        skuCheckVo.setBusiType(GoodsSectionInventoryBusiTypeEnum.CHECK);
        Staff staff = getLightStaff();
        Assert.isTrue(!BooleanUtils.isTrue(staff.getConf().getOpenRecheck()), "开启复盘之后不能直接进行盘点！");
        // 记录盘点日志
        recordCheckLog(skuCheckVo, staff);
        return executeCheck(staff, skuCheckVo);
    }

    private GoodsSectionVo executeCheck(Staff staff, SkuCheckVo skuCheckVo) {
        wmsStockOpService.saveCheckTask(staff, skuCheckVo);
        GoodsSectionInventorySearchParams params = new GoodsSectionInventorySearchParams();
        params.setGoodsSectionId(skuCheckVo.getGoodsSectionId());
        params.setSysItemId(skuCheckVo.getSysItemId());
        params.setSysSkuId(skuCheckVo.getSysSkuId());
        params.setBatchNo(skuCheckVo.getBatchNo());
        params.setProductTime(skuCheckVo.getProductTime());
        params.setWithSku(true);
        params.setValidAsso(false);
        params.setCheckActiveStatus(false);
        return goodsSectionSkuService.queryGoodsSection(staff, params);
    }

    /**
     * 查询商品批次
     *
     * @param condition
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/item/batchNos")
    @ResponseBody
    public Object queryGoodsBatchNos(AssoGoodsSectionSku condition, String api_name) throws Exception {
        Assert.notNull(condition.getSysItemId(), "请选择商品！");
        if (condition.getSysSkuId() == null || condition.getSysSkuId() < 0L) {
            condition.setSysSkuId(0L);
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("warehouseId", condition.getWarehouseId());
        params.put("sysItemId", condition.getSysItemId());
        params.put("sysSkuId", condition.getSysSkuId());
        params.put("qualityType", condition.getQualityType());
        return goodsSectionSkuService.queryBatchNos(getLightStaff(), params);
    }

    /**
     * 批量盘点
     *
     * @param assoIds 货位商品关联id
     */
    @ResponseBody
    @RequestMapping(value = "updateBatch", method = RequestMethod.POST)
    public Object updateBatch(String assoIds, String goodsSectionCodes, Long totalNum, String api_name) throws SessionException, CustomException {
        Assert.hasText(assoIds, "请选择货位关联记录！");
        Assert.hasText(goodsSectionCodes, "请选择货位关联记录！");
        Assert.notNull(totalNum, "请输入盘点数量！");
        Assert.isTrue(totalNum >= 0L, "盘点数不能小于0！");

        Staff staff = getStaff();
        Assert.isTrue(!BooleanUtils.isTrue(staff.getConf().getOpenRecheck()), "开启复盘之后不能直接进行盘点!");
        wmsStockOpService.updateBatch(staff, ArrayUtils.toLongList(assoIds), ArrayUtils.toStringList(goodsSectionCodes), totalNum);
        return true;
    }

    /**
     * 清零货位库存
     *
     * @param assoIds 货位商品关联id
     */
    @ResponseBody
    @RequestMapping(value = "updateBatchZero", method = RequestMethod.POST)
    public Object updateBatchZero(String assoIds, String goodsSectionCodes, String api_name) throws SessionException, CustomException {
        Assert.hasText(assoIds, "请选择货位关联记录！");
        Assert.hasText(goodsSectionCodes, "请选择货位关联记录！");

        Staff staff = getStaff();
        Assert.isTrue(!BooleanUtils.isTrue(staff.getConf().getOpenRecheck()), "开启复盘之后不能直接进行盘点!");
        wmsStockOpService.updateBatchZero(staff, ArrayUtils.toLongList(assoIds), ArrayUtils.toStringList(goodsSectionCodes));
        return true;
    }

    @AccessShield(value = "1119", extendsType = false)
    @ResponseBody
    @RequestMapping(value = "bindGs", method = RequestMethod.POST)
    public Object bindGs(String itemIds, Long goodsSectionId, String api_name) throws SessionException {
        Assert.notNull(goodsSectionId, "请选择分配货位！");

        Staff staff = getStaff();
        wmsStockOpService.bindGs(staff, itemIds, goodsSectionId);
        return successResponse();
    }

    /**
     * 绑定所有的Z货位
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/bindGs/z", method = RequestMethod.POST)
    @ResponseBody
    public Object bindGsZ(String goodsSectionIds) throws Exception {
        Staff staff = getStaff();
        if (!WmsUtils.isOpenWms(staff)) {
            throw new IllegalArgumentException("仅支持暂存区用户!");
        }
        if (staff.getConf().getStallType() == 0) {
            throw new IllegalArgumentException("仅支持虚拟Z货位!");
        }

        String importCacheKey = WmsStallBindListener.IMPORT_CACHE + staff.getCompanyId();
        String bindCacheKey = WmsStallBindListener.BIND_CACHE + staff.getCompanyId();

        //check result
        ImportProcess process = cache.get(bindCacheKey);
        if (process != null) {
            if (BooleanUtils.isTrue(process.isComplete())) {
                cache.delete(bindCacheKey);
                cache.delete(importCacheKey);
                if (CollectionUtils.isNotEmpty(process.getErrors())) {
                    throw new IllegalArgumentException("关联货位发生错误，原因:" + process.getErrors());
                }
            } else {
                throw new IllegalArgumentException("上次操作正在关联中(" + process.getCurrNum() + "/" + process.getTotalNum() + ")，请结束后再操作！");
            }
        }

        //check import
        Boolean importFlag = cache.get(importCacheKey);
        if (BooleanUtils.isTrue(importFlag)) {
            throw new IllegalArgumentException("正在进行Z货位绑定，请稍后重试!");
        } else {
            cache.set(importCacheKey, Boolean.TRUE, WmsStallBindListener.CACHE_TIME);
        }

        if (StringUtils.isNotBlank(goodsSectionIds)) {
            List<Long> sectionIds = Arrays.stream(goodsSectionIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
            eventCenter.fireEvent(this, new EventInfo("wms.stall.bind.z").setArgs(new Object[]{staff, WmsStallBindListener.BIND_TYPE_SPECIFY, sectionIds}), null);
        } else {
            eventCenter.fireEvent(this, new EventInfo("wms.stall.bind.z").setArgs(new Object[]{staff, WmsStallBindListener.BIND_TYPE_ALL}), null);
        }
        return successResponse();
    }

}

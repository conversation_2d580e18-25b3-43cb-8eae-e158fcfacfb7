package com.raycloud.dmj.web.controller.wms.product;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.DataUtils;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.product.StockProductOrder;
import com.raycloud.dmj.domain.wms.product.params.StockProductGoodParams;
import com.raycloud.dmj.domain.wms.product.vo.StockProductGoodVo;
import com.raycloud.dmj.domain.wms.product.vo.StockProductMaterialVo;
import com.raycloud.dmj.domain.wms.product.vo.StockProductRatio;
import com.raycloud.dmj.services.wms.product.IStockProductGoodService;
import com.raycloud.dmj.services.wms.product.IStockProductOrderService;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.storage.utils.ExcelConverter;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仓内成品单
 *
 * <AUTHOR>
 * @Date 2019-09-03
 **/
@Controller
@RequestMapping("/wms/product/good")
public class StockProductGoodController extends WmsInitController {

    @Resource
    private IStockProductGoodService stockProductGoodService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private WmsCacheBusiness wmsCacheBusiness;
    @Resource
    private IStockProductOrderService stockProductOrderService;
    @Resource
    private IFileUploadService fileUploadService;

    private final static String IMPORT_MATERIAL_EXCEL = "1";
    private final static String IMPORT_GOODS_EXCEL = "2";
    /**
     * 查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public Object query(@RequestBody StockProductGoodParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null!");
        Staff staff = getLightStaff();
        return stockProductGoodService.queryStockProductGood(staff, params);
    }

    /**
     * 新增
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public Object add(@RequestBody List<StockProductGoodVo> list, String api_name) throws Exception {
        Assert.notNull(list, "list not null");
        Staff staff = getLightStaff();
        stockProductGoodService.addStockProductGood(staff, list);
        return successResponse();
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public Object edit(@RequestBody List<StockProductGoodVo> list, String api_name) throws Exception {
        Assert.notNull(list, "list not null!");
        Staff staff = getLightStaff();
        stockProductGoodService.editStockProductGoodByIds(staff, list);
        return successResponse();
    }

    /**
     * 编辑改码的bom  ---全量
     */
    @RequestMapping(value = "/edit/bom/barter", method = RequestMethod.POST)
    @ResponseBody
    public Object bomBarter(@RequestBody List<StockProductMaterialVo> stockProductMaterialVos, String api_name) throws Exception {
        Staff staff = getLightStaff();
        stockProductGoodService.updateBomByTypeBarter(staff, stockProductMaterialVos);
        return successResponse();
    }

    /**
     * 编辑改码的bom  ---全量
     */
    @RequestMapping(value = "/edit/bom/ratio", method = RequestMethod.POST)
    @ResponseBody
    public Object editBomRatio(@RequestBody List<StockProductMaterialVo> stockProductMaterialVos, String api_name) throws Exception {
        Staff staff = getLightStaff();
        stockProductGoodService.editBomRatio(staff, stockProductMaterialVos);
        return successResponse();
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object delete(String idStr, String api_name) throws Exception {
        Assert.notNull(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        stockProductGoodService.deleteByIds(staff, ArrayUtils.toLongList(idStr));
        return successResponse();
    }

    /**
     * 导入成品/原料
     */
    @RequestMapping(value = "/excel/import", method = RequestMethod.POST)
    @ResponseBody
    public Object importStockProductGood(MultipartFile file, Long productOrderId, @RequestParam(defaultValue = IMPORT_GOODS_EXCEL) String importType , Integer addRepeatType , String api_name) throws Exception {
        if (file == null) {
            throw new IllegalArgumentException("请选择要导入成品或者原料的excel文件!");
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("上传成品或者原料的excel文件大小不能超过5MB!");
        }
        String filename = file.getOriginalFilename();
        if (!(filename.endsWith(".xls") || filename.endsWith(".xlsx") || filename.endsWith(".csv"))) {
            throw new IllegalArgumentException("上传文件文件格式只支持EXL表格与CSV格式!");
        }

        if (productOrderId == null) {
            throw new IllegalArgumentException("加工单id不能为空!");
        }
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, productOrderId);
        if (order == null) {
            throw new IllegalArgumentException("导入成品或者原料excel时，未找到加工单!");
        }

        String importKey = null;
        if (IMPORT_MATERIAL_EXCEL.equals(importType)){
            Assert.notNull(addRepeatType,"原料excel导入的重复类型不为空和null");
            importKey = wmsCacheBusiness.getKey(staff,WmsCacheEnum.CACHE_STOCK_MATERIAL_IMPORT);
        }else {
            importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_STOCK_PRODUCT_GOOD_IMPORT);
        }
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null) {
            throw new IllegalArgumentException("上一次成品或者原料excel导入还未执行完，请稍后重试!");
        }
        String[][] data;
        try {
            data = ExcelConverter.excel2DataArr(file.getInputStream(), 1);
        } catch (Exception e) {
            throw new IllegalArgumentException("成品或者原料上传的文件格式不正确!");
        }
        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("成品或者原料excel数据不能为空！");
        }
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        wmsCacheBusiness.set(importKey, WmsProcessUtils.buildBaseProcess(importKey, 1), 10 * 60);
        if (IMPORT_MATERIAL_EXCEL.equals(importType)) {
            /**
             * 添加原料重复时，给出对应的错误类型
             * 默认 null 或者 0
             * 0  商家编码重复  ADD_REPEAT_TYPE_ERROR
             * 1  直接跳过  ADD_REPEAT_TYPE_SKIP
             * 2  数量进行替换  ADD_REPEAT_TYPE_REPLACE
             * 3  数量取和累加  ADD_REPEAT_TYPE_SUM
             */
            order.setAddRepeatType(addRepeatType);
            eventCenter.fireEvent(this, new EventInfo("wms.material.excel").setArgs(new Object[]{staff, order, data}), fileResult.getUrl());
        } else {
            eventCenter.fireEvent(this, new EventInfo("wms.product.good.excel.import").setArgs(new Object[]{staff, order, data}), fileResult.getUrl());
        }
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importKey);
        return result;
    }

    /**
     * 成品、原料excel导入进度
     */
    @RequestMapping(value = "/excel/import/status")
    @ResponseBody
    public Object getImportStatus(@RequestParam(defaultValue = IMPORT_GOODS_EXCEL) String importType , String api_name) throws Exception {
        Staff staff = getLightStaff();
        String importKey = null ;
        String importTypeMsg = null;
        if (IMPORT_MATERIAL_EXCEL.equals(importType)){
            importTypeMsg = "原料";
            importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_STOCK_MATERIAL_IMPORT);
        }else {
            importTypeMsg = "成品";
            importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_STOCK_PRODUCT_GOOD_IMPORT);
        }
        ImportProcess process = wmsCacheBusiness.get(importKey);
        Assert.notNull(process, "请先进行" + importKey + "excel导入！");
        if (process.isComplete()) {
            wmsCacheBusiness.delete(importKey);
            int errorNum = process.getErrorNum();
            String content = String.format(importTypeMsg + "excel导入，结果：%s，总条数：%s，失败：%s", (errorNum > 0 ? "失败" : "成功"), process.getTotalNum(), errorNum);
            if (IMPORT_MATERIAL_EXCEL.equals(importType)){
                writeOpLog(staff, Domain.WMS, "StockProductMaterialExcelImport", content, importKey + "_" + staff.getAccountId());
            } else {
                writeOpLog(staff, Domain.WMS, "StockProductGoodExcelImport", content, importKey + "_" + staff.getAccountId());
            }
        }
        return process;
    }

}

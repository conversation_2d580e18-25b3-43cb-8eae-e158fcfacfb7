package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.wms.BoxInOutRecord;
import com.raycloud.dmj.domain.wms.BoxInOutRecordQueryParams;
import com.raycloud.dmj.domain.wms.GoodsSectionDisplay;
import com.raycloud.dmj.domain.wms.WmsPageList;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.IBoxInOutRecordService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInOutRecordVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022-08-09 19:26
 * @description：
 * @modified By：
 * @version: $
 */
@RequestMapping("/wms/box/record")
@Controller
public class BoxInOutRecordController extends WmsInitController {


    @Autowired
    private IBoxInOutRecordService boxInOutRecordService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;

    /**
     * 箱进出记录查询
     * @Title: query
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public Object query(@RequestBody BoxInOutRecordQueryParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        WmsPageList<BoxInOutRecord> pageList = boxInOutRecordService.queryInOutRecordPage(staff, params);
        List<BoxInOutRecord> list = pageList.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            BoxInOutRecord lastOne = list.get(list.size() - 1);
            pageList.setLastOneId(lastOne.getId());
        }
        return pageList;
    }


    /**
     * 箱进出记录导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ResponseBody
    public Object export(@RequestBody BoxInOutRecordQueryParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        String fileName = new String("箱进出记录数据导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";
        FileDownloadParam downParam = new FileDownloadParam();
        downParam.setFileName(fileName);
        downParam.setExcelTitle("箱进出记录数据");
        downParam.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo("wms.box.record.export").setArgs(new Object[]{staff, downParam, params}), null);
        return successResponse();
    }

    

}

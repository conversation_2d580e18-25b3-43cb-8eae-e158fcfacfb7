package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionInventoryOpLog;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.SkuAllocateTask;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryOpTypeEnum;
import com.raycloud.dmj.domain.wms.enums.ReleaseOperateTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.domain.wms.result.BatchResult;
import com.raycloud.dmj.domain.wms.result.ErrorItem;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.*;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.utils.ExcelConverter;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.interceptors.TbTask;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import com.raycloud.dmj.web.model.wms.SkuAllocateTaskVo;
import com.raycloud.dmj.web.model.wms.TransferVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 移库控制器
 * Created by guzy on 16/2/17.
 */
@Controller
@LogTag(value="wms")
@RequestMapping("/wms/skuTransfer/")
@AccessShield(value = "1006")
public class SkuTransferController extends WmsInitController {

    @Resource
    private ISkuTransferService skuTransferService;

    @Resource
    private IGoodsSectionSkuService goodsSectionSkuService;

    @Resource
    private IWmsStockOpService wmsStockOpService;

    @Resource
    private IWmsService wmsService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private WmsSkuTransferExcelService wmsSkuTransferExcelService;

    /**
     * 待移库列表
     */
    @ResponseBody
    @RequestMapping(value = "wait/list",method = {RequestMethod.GET,RequestMethod.POST})
    public Object waitList(GoodsSectionInventorySearchParams params,Page page,String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<SkuAllocateTaskVo> pageList = skuTransferService.selectWaitAllocateTasks(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        pageList.setPage(page);
        return pageList;
    }

    /**
     * 移库完成列表
     */
    @ResponseBody
    @RequestMapping(value="finish/list",method = {RequestMethod.GET,RequestMethod.POST})
    public Object finishedList(GoodsSectionInventorySearchParams params,Page page,String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<SkuAllocateTaskVo> pageList = skuTransferService.selectFinishedAllocateTasks(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        pageList.setPage(page);
        return pageList;
    }

    /**
     * 根据任务id查询入库货位列表
     * @param taskId 移库任务id
     */
    @ResponseBody
    @RequestMapping(value = "to_goods_sections",method = RequestMethod.GET)
    public Object selectToRecords(Long taskId,String api_name) throws SessionException {
        return skuTransferService.selectToRecordsByTaskId(getStaff(), taskId);
    }

    /**
     * 根据移库任务id查询详情日志列表
     * @param transferId 移库任务id
     */
    @ResponseBody
    @RequestMapping(value = "logs",method = {RequestMethod.GET})
    public Object getOpLogs(Long transferId,String api_name) throws SessionException {
        List<GoodsSectionInventoryOpLog> logs= goodsSectionSkuService.getOpLogs(getStaff(), transferId, GoodsSectionInventoryBusiTypeEnum.TRANSFER);
        for(GoodsSectionInventoryOpLog opLog:logs){
            if(opLog.getOpCode().equals(GoodsSectionInventoryOpTypeEnum.EXECUTE.code)){
                opLog.setOpName("移库完成");
            }
        }
        return logs;
    }

    /**
     * 新建移库任务
     */
    @RequestMapping(value = "add",method = RequestMethod.POST)
    @ResponseBody
    public Object add(TransferVo transferVo,String api_name) throws Exception {
        Staff staff=getStaff();
        if(BooleanUtils.isTrue(transferVo.getExecuted())){
            writeOpLog(staff,"executeSkuTransfer",String.format("执行移库任务,出库货位:%s,入库货位:%s",transferVo.getFromGoodsSectionCode(),transferVo.getToGoodsSectionCodes()), JSONObject.toJSONString(transferVo));
        }else{
            writeOpLog(staff,"addSkuTransfer",String.format("新建移库任务,货位:%s",transferVo.getFromGoodsSectionCode()), JSONObject.toJSONString(transferVo));
        }
        //特殊场景处理释放货位
        transferVo.setOperateTypeEnum(ReleaseOperateTypeEnum.BATCH_TRANSFER_OPERATE);
        return wmsStockOpService.addTransfer(staff, transferVo);
    }

    @RequestMapping(value = "batch/add",method = RequestMethod.POST)
    @ResponseBody
    @TbTask(taskId = "KMERP-96379")
    public Object add(@RequestBody List<TransferVo> voList,String api_name) throws Exception {
        Staff staff=getStaff();
        //特殊场景处理释放货位
        voList.forEach(t -> t.setOperateTypeEnum(ReleaseOperateTypeEnum.BATCH_TRANSFER_OPERATE));
        BatchResult<SkuAllocateTask> result = wmsStockOpService.addTransferBatch(staff, voList);
        return batchWriteOpLog(staff,voList,result);
    }

    private Object batchWriteOpLog(Staff staff, List<TransferVo> voList, BatchResult<SkuAllocateTask> result) {
        if (result.getSuccess().isEmpty()) {
            return result;
        }
        voList.forEach(vo -> {
            if (BooleanUtils.isTrue(vo.getExecuted())) {
                writeOpLog(staff, "executeSkuTransfer", String.format("执行移库任务,出库货位:%s,入库货位:%s", vo.getFromGoodsSectionCode(), vo.getToGoodsSectionCodes()), null);
            } else {
                writeOpLog(staff, "addSkuTransfer", String.format("新建移库任务,货位:%s", vo.getFromGoodsSectionCode()), null);
            }
        });
        return result;
    }

    /**
     * 批量新建完成良次品货位信息
     * 注：仅支持自定义货位方式；只能移动到加C的次品或者不加C的良品。
     * 产品：这种要基于实施先按照我们的解决方案来的
     */
    @RequestMapping(value = "add/defective", method = RequestMethod.POST)
    @ResponseBody
    public Object addDefective(@RequestBody List<TransferVo> list, String api_name) throws Exception {
        Assert.notEmpty(list, "移库任务不能为空!");
        Staff staff = getStaff();
        if (WmsUtils.isSectionCodeStoreDefault(staff)) {
            throw new IllegalArgumentException("标准货位暂不支持快速货位移库!");
        }

        //获取对应加/去掉C的货位method
        Function<TransferVo, String> reverseToCode = transferVo -> BooleanUtils.isTrue(transferVo.getQualityType())
                ? "C" + transferVo.getFromGoodsSectionCode() : transferVo.getFromGoodsSectionCode().substring(1);

        Map<String, GoodsSectionVo> toGoodsSectionMap = wmsService.queryGoodsSectionsByCodes(staff, list.stream().map(reverseToCode).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(GoodsSectionVo::getCode, v -> v, (k1, k2) -> k1));

        List<ErrorItem> errors = new ArrayList<>();
        List<TransferVo> transferVos = list.stream().filter(transferVo -> {
            String toGoodsSectionCode = reverseToCode.apply(transferVo);
            GoodsSectionVo toGoodsSection = toGoodsSectionMap.get(toGoodsSectionCode);
            if (toGoodsSection == null) {
                errors.add(new ErrorItem(transferVo.getFromGoodsSectionCode(), "未找到对应的次品货位[" + toGoodsSectionCode + "]!"));
                return false;
            }
            transferVo.setToGoodsSectionIds(toGoodsSection.getId().toString());
            transferVo.setToGoodsSectionCodes(toGoodsSection.getCode());
            return true;
        }).collect(Collectors.toList());
        if (transferVos.isEmpty()) {
            BatchResult<SkuAllocateTask> result = new BatchResult<>();
            result.init();
            result.getErrors().addAll(errors);
            return result;
        }

        BatchResult<SkuAllocateTask> result = wmsStockOpService.addTransferBatch(staff, transferVos);
        if (!errors.isEmpty()) {
            result.getErrors().addAll(errors);
        }
        return batchWriteOpLog(staff,transferVos,result);
    }

    /**
     * 批量新增移库任务数据
     * @param assoIds 货位商品关联id
     * @param goodsSectionCodes 货位编码
     */
    @ResponseBody
    @RequestMapping(value = "addBatch",method = RequestMethod.POST)
    @LogTag(key = "#goodsSectionCodes",content = "'批量新增移库任务,货位:'+#goodsSectionCodes")
    public Object addBatch(String assoIds,String goodsSectionCodes,String api_name) throws SessionException, CustomException {
        skuTransferService.addBatch(getStaff(), ArrayUtils.toLongList(assoIds), ArrayUtils.toStringList(goodsSectionCodes));
        return true;
    }


    /**
     * 更新移库任务
     * @param transferVo 移库任务参数vo
     */
    @RequestMapping(value = "update",method = RequestMethod.POST)
    @LogTag(key="#transferVo.fromGoodsSectionId + '_' + #transferVo.sysItemId + '_' + #transferVo.sysSkuId ",content = "'修改移库任务，货位:'+#transferVo.fromGoodsSectionCode ",enableArgs = "true")
    @ResponseBody
    public Object update(TransferVo transferVo,String api_name) throws Exception {
        transferVo.setExecuted(false);
        return wmsStockOpService.updateTransfer(getStaff(), transferVo);
    }

    /**
     * 执行移库任务
     * @param transferVo 移库任务参数vo
     */
    @RequestMapping(value = "execute",method = RequestMethod.POST)
    @LogTag(key="#transferVo.fromGoodsSectionId + '_' + #transferVo.sysItemId + '_' + #transferVo.sysSkuId ",content = "'执行移库任务，移出货位:'+#transferVo.fromGoodsSectionCode +',移入货位:'+#transferVo.toGoodsSectionCodes ",enableArgs = "true")
    @ResponseBody
    public Object execute(TransferVo transferVo,String api_name) throws Exception {
        transferVo.setExecuted(true);
        return wmsStockOpService.updateTransfer(getStaff(), transferVo);
    }

    /**
     * 批量执行移库任务
     * @param ids 移库任务参数vo
     */
    @RequestMapping(value = "batchExecute",method = RequestMethod.POST)
    @LogTag(key="#ids ",content = "'执行批量移库任务' ",enableArgs = "true")
    @ResponseBody
    public Object batchExecute(String ids,String api_name) throws Exception {
        Assert.notNull(ids, "ids不能为空！");
        return skuTransferService.batchExecuteTransfer(getStaff(), Arrays.asList(ArrayUtils.toLongArray(ids)));
    }

    /**
     * 根据id删除货位移库任务
     * @param id 移库任务id
     */
    @RequestMapping(value = "delete",method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#id+''",content = "'删除移库任务,货位:'+#goodsSectionCode ")
    public Object delete(Long id,String goodsSectionCode,String api_name) throws SessionException {
        return skuTransferService.delete(getStaff(), id);
    }

    /**
     * 根据ids批量删除货位移库任务
     */
    @RequestMapping(value = "deleteBatch",method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#ids+''",content = "'批量删除移库任务,货位:'+#goodsSectionCodes ")
    public Object deleteBatch(String ids,String goodsSectionCodes,String api_name) throws SessionException {
        Assert.hasText(ids, "序号不能为空！");
        return skuTransferService.deleteBatch(getStaff(), ids);
    }

    /**
     * 路径排序
     */
    @RequestMapping(value = "sort/path",method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "'路径排序'",content = "'路径排序'")
    public Object sortPath(String api_name) throws SessionException {
        return skuTransferService.sortPath(getStaff());
    }


    /**
     * 安排移库人员
     */
    @RequestMapping(value = "assign/mover",method = RequestMethod.POST)
    @ResponseBody
    public Object assignMover(String taskIds, String operatorIds, String operatorNames,String api_name) throws SessionException {
        skuTransferService.assignMover(getStaff(), taskIds, operatorIds, operatorNames);
        return successResponse();
    }

    /**
     * 移库任务导出
     * @Title: export
     * @param params
     * @param isFinish
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "export", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object export(GoodsSectionInventorySearchParams params, Boolean isFinish, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");
        eventCenter.fireEvent(this, new EventInfo("wms.transfer.download.excel").setArgs(new Object[]{staff, params, isFinish}), false);
        return successResponse();
    }

    /**
     * 批量填充移入货位
     */
    @RequestMapping(value = "update/toSection",method = RequestMethod.POST)
    @ResponseBody
    public Object updateToSection(String taskIds, Long toGoodsSectionId, String toGoodsSectionCode,String api_name) throws SessionException {
        skuTransferService.updateToSection(getStaff(), taskIds, toGoodsSectionId, toGoodsSectionCode);
        return successResponse();
    }

    /**
     * 移库任务导入
     * @param file
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "import/upload", method = RequestMethod.POST)
    @ResponseBody
    public Object importUploadTransferExcelData(MultipartFile file) throws Exception {
        Assert.notNull(file, "请选择上传的文件");
        Staff staff = getStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_SKU_TRANSFER_IMPORT);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        Map<String, Object> result = Maps.newHashMap();
        //文件后缀名
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        //后缀判断
        if (!fileSuffix.equals(".xlsx") && !fileSuffix.equals(".xls") && !fileSuffix.equals(".csv")) {
            result.put("errorMessage", "只能导入.xls或.xlsx或.csv为后缀的文件");
            return result;
        }
        if (file.getSize() > 3 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于3M");
            return result;
        }
        //取出excel
        String[][] dataArr;
        try {
            dataArr = ExcelConverter.excel2DataArr(file.getInputStream(), 0);
        } catch (Exception e) {
            result.put("errorMessage", "上传文件内容不符合要求，请下载后重新尝试");
            return result;
        }
        if (dataArr == null || dataArr.length < 1) {
            result.put("errorMessage", "导入数据不能为空，请填入数据后重新上传");
            return result;
        }
        if (!checkTransferTemplate(dataArr[0])) {
            result.put("errorMessage", "导入的模板有误");
            return result;
        }
        if (dataArr.length > 2000) {
            result.put("errorMessage", "每次最多只能导入2000行数据！");
            return result;
        }

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行货位批量添加，请稍候！");
        }

        ImportProcess process = new ImportProcess();
        process.setTotalNum(dataArr.length - 1);
        process.setCurrNum(0);
        process.setComplete(false);
        cache.set(cacheKey, process);
        List<TransferVo> transferVos = wmsSkuTransferExcelService.importSkuTransfer(staff,cacheKey, dataArr);
        result.put("transferVos", transferVos);
        result.put("cacheKey", cacheKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 查看导入进度
     * @throws CacheException
     */
    @RequestMapping(value = "/import/status", method = RequestMethod.GET)
    @ResponseBody
    public Object getImportStatus(String cacheKey, String api_name) throws CacheException {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            process.setErrorNum(process.getTotalNum() - process.getRightNum());
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }

    /**
     * 校验模板
     *
     * @param menu
     * @return
     */
    private boolean checkTransferTemplate( String[] menu) {
        return "<必填>商品编码".equals(menu[0])
                && "商品名称".equals(menu[1])
                && "规格名称".equals(menu[2])
                && "是否换质量移库".equals(menu[3])
                && "生产日期".equals(menu[4])
                && "批次".equals(menu[5])
                && "<必填>移出货位".equals(menu[6])
                && "移库数量".equals(menu[7])
                && "移入货位".equals(menu[8]);
    }


    /**
     * assoNumType = 0 在架数
     * assoNumType = 1 可配数
     * 填充在架数 or 可配数
     */
    @RequestMapping(value = "/fill/asso/num", method = RequestMethod.POST)
    @ResponseBody
    public Object fillTransferAssoNum(String ids, Integer assoNumType) throws Exception {
        Assert.hasText(ids, "序号不能为空！");
        Staff staff = getStaff();
        skuTransferService.fillTransferAssoNum(staff, ids, assoNumType);
        return successResponse();
    }

}

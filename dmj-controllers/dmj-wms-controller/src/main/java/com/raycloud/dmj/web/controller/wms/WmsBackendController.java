package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.business.trade.WmsStallRecordAdjustBusiness;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.trade.WmsTradeRepairBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.ec.trade.WmsTradeAuditListener;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IBackendService;
import com.raycloud.dmj.services.wms.IGoodsSectionService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.allocategoods.IAllocateGoodsService;
import com.raycloud.dmj.services.wms.support.AllCompanyDealer;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.domain.wms.params.*;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * creatd 18/5/25
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/wms/backend")
public class WmsBackendController extends WmsInitController {

    @Resource
    private ICompanyService companyService;

    @Resource
    private AllCompanyDealer allCompanyDealer;

    @Resource
    private IBackendService backendService;

    @Resource
    private WmsTradeRepairBusiness wmsTradeRepairBusiness;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IGoodsSectionService goodsSectionService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IAllocateGoodsService allocateGoodsService;

    @Resource
    private WmsStallRecordAdjustBusiness wmsStallRecordAdjustBusiness;

    @Resource
    private IWmsService wmsService;

    @Resource
    private WmsTradeAuditListener wmsTradeAuditListener;

    private final static Logger logger = Logger.getLogger(WmsBackendController.class);

    /**
     * 查询所有开启了仓储的公司id
     */
    @RequestMapping(value = "/query/all/open", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairGoodsSectionOrderRecordApply() throws SessionException {
        Page page = new Page().setPageNo(1).setPageSize(500);
        List<Long> result = new ArrayList<Long>();
        List<Company> list;
        while ((list = companyService.list(page)).size() > 0) {
            try {
                List<Long> companyIds = new ArrayList<Long>();
                for (Company company : list) {
                    companyIds.add(company.getId());
                }
                List<CompanyProfile> profiles = companyService.getCompanyProfiles(companyIds.toArray(new Long[0]));
                for (CompanyProfile profile : profiles) {
                    if (profile.getConf().isOpenWms()) {
                        result.add(profile.getCompanyId());
                    }
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(getStaff(), e, "查询所有开启了仓储的公司出错"), e);
            } finally {
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        return result;
    }

    /**
     * 修复锁定记录的申请数
     */
    @RequestMapping(value = "/repair/record/apply", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairGoodsSectionOrderRecordApply(String companyIdStr) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIds参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                if (WmsUtils.isOldWms(staff)) {
                    backendService.repairGoodsSectionOrderRecordApply(staff);
                }
            }
        }
        return successResponse();
    }

    /**
     * 修复锁定记录的申请数
     */
    @RequestMapping(value = "/repair/record/getnum", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairGoodsSectionOrderRecordGetNum(String companyIdStr) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIds参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                if (WmsUtils.isOldWms(staff)) {
                    backendService.repairGoodsSectionOrderRecordGetNum(staff);
                }
            }
        }
        return successResponse();
    }


    /**
     * 修复货位锁定数
     */
    @RequestMapping(value = "/repair/agss/Lock", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairAssoGoodsSectionSkuLock(String companyIdStr) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIds参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                wmsTradeRepairBusiness.repairLockNum(staff, true);
            }
        }
        return successResponse();
    }

    /**
     * 申请货位库存,order已审核,未发货，没有货位锁定记录
     */
    @ResponseBody
    @RequestMapping(value = "/repair/trade/audit", method = {RequestMethod.POST, RequestMethod.GET})
    public Object repairTradeAudit(String companyIdStr) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIdStr参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                if (WmsUtils.isOldWms(staff) && !WmsUtils.isOpenDepot(staff)) {
                    wmsTradeRepairBusiness.repairApply(staff, true);
                }
            }
        }
        return successResponse();
    }

    /**
     * 归还货位库存,order非已审核,没有从系统发货，有货位锁定记录
     */
    @ResponseBody
    @RequestMapping(value = "/repair/trade/unaudit", method = {RequestMethod.POST, RequestMethod.GET})
    public Object repairTradeUnAudit(String companyIdStr) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIdStr参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                if (WmsUtils.isOldWms(staff) && !WmsUtils.isOpenDepot(staff)) {
                    wmsTradeRepairBusiness.repairResume(staff, true,null);
                } else if(WmsUtils.isNewWms(staff) && !WmsUtils.isOpenDepot(staff)){
                    wmsTradeRepairBusiness.repairAllocateResume(staff, true,null);
                }
            }
        }
        return successResponse();
    }

    /**
     * 消费货位库存,order从系统发货,有货位锁定记录
     */
    @ResponseBody
    @RequestMapping(value = "/repair/trade/consign", method = {RequestMethod.POST, RequestMethod.GET})
    public Object repairTradeConsign(String companyIdStr, Boolean checkConsistent) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr), "请输入companyIdStr参数");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), companyIds);
        if (staffs != null && staffs.size() > 0) {
            for (Staff staff : staffs) {
                if (WmsUtils.isOldWms(staff) && !WmsUtils.isOpenDepot(staff)) {
                    wmsTradeRepairBusiness.repairConsume(staff, true, BooleanUtils.isNotFalse(checkConsistent));
                } else if(WmsUtils.isNewWms(staff)){
                    wmsTradeRepairBusiness.repairAllocateConsume(staff, true, BooleanUtils.isNotFalse(checkConsistent));
                }
            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/repair/trade/all", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairWmsTrade(String companyIdStr) throws Exception {
        Assert.hasText(companyIdStr, "请输入companyIdStr参数");
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), ArrayUtils.toLongArray(companyIdStr));
        if (CollectionUtils.isNotEmpty(staffs)) {
            for (Staff staff : staffs) {
                wmsTradeRepairBusiness.repairAll(staff, true);
            }
        }
        return successResponse();
    }

    /**
     * 修复oms已发货wms无锁定无发货
     *
     * @param companyId
     * @param orderIds
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repair/consign/nogoods/records", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object repairConsignNoGoodsRecords(Long companyId, String orderIds) throws Exception {
        Assert.notNull(companyId, "请输入companyId参数");
        Assert.hasText(orderIds, "请输入orderIds参数");
        List<Staff> staffs = allCompanyDealer.getStaffs(getStaff(), new Long[]{companyId});
        if (CollectionUtils.isNotEmpty(staffs)) {
            for (Staff staff : staffs) {
                wmsTradeRepairBusiness.repairOmsConsumeBeforeLock(staff, ArrayUtils.toLongArray(orderIds));
            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/export/excel", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object exportExcel(String exportExcel,Integer maxCount) throws Exception {
//        Assert.hasText(exportExcel, "请输入exportExcel参数");
//        exportExcel = exportExcel.toLowerCase().trim();
//        Assert.isTrue(exportExcel.startsWith("select"), "不是查询语句，无法处理");
//        eventCenter.fireEvent(this, new EventInfo("wms.backend.export.download.excel").setArgs(new Object[]{getStaff(), exportExcel, maxCount}), false);
//        return successResponse();
        throw new IllegalArgumentException("暂不支持！");
    }

    @RequestMapping(value = "/delete/allocate/records", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteAllocateRecords(String waveIds, String orderIds, Integer allocateType) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(waveIds) || StringUtils.isNotEmpty(orderIds), "waveIds/orderIds不能同时为空！");

        allocateGoodsService.deleteByMultiIds(getLightStaff(), ArrayUtils.toLongList(waveIds), ArrayUtils.toLongList(orderIds), allocateType);
        return successResponse();
    }

    @RequestMapping("/adjust/stall/records")
    @ResponseBody
    public Object adjustStallRecords(String orderIds) throws Exception {
        Assert.hasText(orderIds, "orderIds not null");
        wmsStallRecordAdjustBusiness.adjustStallRecords(getLightStaff(), ArrayUtils.toLongList(orderIds));
        return successResponse();
    }

    /**
     *修改货位拣选属性
     */
    @RequestMapping("/modify/GsIgnorePick")
    @ResponseBody
    public Object modifyGsIgnorePick (String gsIds,Integer ignorePick) throws SessionException {
        Assert.hasText(gsIds, "GsIds not null");
        Assert.isTrue(ignorePick!=null ," ignorePick is not null");
        Assert.isTrue(ignorePick==0||ignorePick==1 ,"ignorePick not in 0 or 1");
        backendService.modifyGsIgnorePick(getLightStaff(),ArrayUtils.toLongList(gsIds),ignorePick);
        return successResponse();
    }

    @RequestMapping("/callback/afterOpenWmsStorageSection")
    @ResponseBody
    public Object callbackAfterOpenWmsStorageSection(Boolean openStorageSection) throws SessionException {
        Assert.notNull(openStorageSection, "openStorageSection is not null");
        wmsService.afterOpenWmsStorageSection(getLightStaff(), openStorageSection);
        return successResponse();
    }

    /**
     *作废调拨中的调拨单以及关联的出库单
     */
    @RequestMapping("/cancel/allocateTask")
    @ResponseBody
    public Object cancelAllocateTask(String allocateIds) throws SessionException {
        Assert.hasText(allocateIds, "allocateIds not null");
        backendService.cancelAllocateTask(getLightStaff(),ArrayUtils.toLongList(allocateIds));
        return successResponse();
    }

    @RequestMapping("/close/purchaseReturnStorage")
    @ResponseBody
    public Object closePurchaseReturnStorage() throws SessionException {
        return wmsService.checkClosePurchaseReturnStorage(getLightStaff());
    }

    @RequestMapping("/test/queryBatchProductItemStockPageList")
    @ResponseBody
    public Object queryBatchProductItemStockPageList(String warehouseIds,String sysItemIds,String sysSkuIds) throws Exception {
        WmsQueryProductBatchNoParams params = new WmsQueryProductBatchNoParams();
        params.setWarehouseIds(Strings.getAsLongList(warehouseIds,",",true));
        params.setSysItemIds(Strings.getAsLongList(sysItemIds,",",true));
        params.setSysSkuIds(Strings.getAsLongList(sysSkuIds,",",true));
        return wmsService.queryBatchProductItemStockPageList(getLightStaff(), params);
    }
}

package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionInventoryOpLog;
import com.raycloud.dmj.domain.wms.SkuCheckRecord;
import com.raycloud.dmj.domain.wms.constants.WmsOpLogConstants;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryOpTypeEnum;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.domain.ItemTraceMessage;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.wms.IGoodsSectionSkuService;
import com.raycloud.dmj.services.wms.IInventoryCheckService;
import com.raycloud.dmj.services.wms.WmsStockOpService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsOpLogUtils;
import com.raycloud.dmj.utils.wms.WmsBatchProductUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import com.raycloud.dmj.web.model.wms.SkuCheckRecordVo;
import com.raycloud.dmj.web.model.wms.SkuCheckVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * 库存盘点控制器
 * Created by guzy on 16/1/28.
 */
@Controller
@LogTag(value="wms")
@RequestMapping("/wms/inventoryCheck/")
@AccessShield(value = "1004")
public class InventoryCheckController extends WmsInitController {

    @Resource
    private IInventoryCheckService inventoryCheckService;

    @Resource
    private WmsStockOpService wmsStockOpService;

    @Resource
    private IGoodsSectionSkuService goodsSectionSkuService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IItemTraceService itemTraceService;

    @Resource
    private WmsItemBusiness wmsItemBusiness;


    /**
     * 待盘点列表
     */
    @ResponseBody
    @RequestMapping(value = "wait/list",method = {RequestMethod.GET,RequestMethod.POST})
    public Object waitList(GoodsSectionInventorySearchParams params,Page page,String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<GoodsSectionVo> pageList = goodsSectionSkuService.waitCheckInventories(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        return pageList;
    }

    /**
     * 盘点完成列表
     */
    @ResponseBody
    @RequestMapping(value="finish/list",method = {RequestMethod.GET,RequestMethod.POST})
    public Object finishedList(GoodsSectionInventorySearchParams params,Page page,String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<SkuCheckRecordVo> pageList = inventoryCheckService.finishedCheckInventories(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        pageList.setPage(page);
        return pageList;
    }

    @RequestMapping(value = "/export", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object export(@RequestBody GoodsSectionInventorySearchParams params, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");

        String[] EXCEL_HEADER;
        if(params.getInventoryExportExcelType() == 1) {
            EXCEL_HEADER = new String[]{"序号", "商品名称", "规格", "商家编码", "批次","生产日期", "质量", "仓库", "货位编码", "在架数", "盘点数量", "损益数量", "创建人", "创建时间","备注说明"};
        } else {
            EXCEL_HEADER = new String[]{"序号", "商品名称", "规格", "商家编码", "批次","生产日期", "质量", "仓库", "货位编码", "在架数", "盘点数量", "损益数量", "盘点人员", "完成时间","备注说明"};
        }

        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("盘点任务导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("盘点任务数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("wms.inventory.download.excel").setArgs(new Object[]{staff, param, params}), false);

        return successResponse();
    }



    /**
     * 根据盘点任务id查询详情日志列表
     */
    @ResponseBody
    @RequestMapping(value = "logs",method = {RequestMethod.GET})
    public Object getOpLogs(Long checkId,String api_name) throws SessionException {
        List<GoodsSectionInventoryOpLog> opLogs=goodsSectionSkuService.getOpLogs(getStaff(), checkId, GoodsSectionInventoryBusiTypeEnum.CHECK);
        for(GoodsSectionInventoryOpLog opLog:opLogs){
            if(opLog.getOpCode().equals(GoodsSectionInventoryOpTypeEnum.EXECUTE.code)){
                opLog.setOpName("盘点完成");
            } else if (opLog.getOpCode().equals(GoodsSectionInventoryOpTypeEnum.RECHECK.code)) {
                opLog.setOpName("执行盘点");
            }
        }
        return opLogs;
    }

    /**
     * 新增盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "add",method = RequestMethod.POST)
    @LogTag(key="#skuCheckVo.goodsSectionId + '_' + #skuCheckVo.sysItemId + '_' + #skuCheckVo.sysSkuId ",content = "('execute'==#skuCheckVo.opType?'执行盘点任务，货位:':'新建盘点任务，货位:')+#skuCheckVo.goodsSectionCode ",enableArgs = "true",action = "addInventoryCheck")
    public Object add(SkuCheckVo skuCheckVo,String api_name) throws Exception {
        Assert.notNull(skuCheckVo.getNum(), "盘点数量不能为空！");
        Assert.isTrue(skuCheckVo.getNum() >= 0L, "盘点数量不能小于0！");
        skuCheckVo.setForce(false);
        skuCheckVo.setBusiType(GoodsSectionInventoryBusiTypeEnum.CHECK);
        inventoryCheckService.addCheckTask(getStaff(), skuCheckVo);
        Staff staff = getLightStaff();
        // 记录商品操作日志
        recordItemLog(staff, skuCheckVo);
        return skuCheckVo;
    }

    private void recordItemLog(Staff staff, SkuCheckVo skuCheckVo) {
        String opType = skuCheckVo.getOpType();
        // 只记录盘点完成
        if ("execute".equals(opType)) {
            ItemTraceMessage message = new ItemTraceMessage();
            message.setSectionCode(skuCheckVo.getGoodsSectionCode());
            message.setNum(skuCheckVo.getNum());
            message.setRemark(String.format("盘点前数量：%d，盘点后数量：%d", skuCheckVo.getNum() - skuCheckVo.getChangeNum(), skuCheckVo.getNum()));
            WmsOpLogUtils.assembleCommon(staff, message, WmsOpLogConstants.CHECK_PATH, ItemTraceActionEnum.WMS_CHECK);
            message.setSysItemId(skuCheckVo.getSysItemId());
            message.setSysSkuId(skuCheckVo.getSysSkuId());
            message.setOuterId(skuCheckVo.getOuterId());
            message.setBatchNo(skuCheckVo.getBatchNo());
            message.setProductTime(WmsBatchProductUtils.productTimeShow(skuCheckVo.getProductTime()));
            WmsOpLogUtils.checkAndSetOuterId(staff, message, wmsItemBusiness);
            itemTraceService.recordEc(staff, message);
        }
    }

    /**
     * 修改盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "update",method = {RequestMethod.POST})
    @LogTag(key="#skuCheckRecord.goodsSectionCode ",content = "'修改盘点任务，货位:'+#skuCheckRecord.goodsSectionCode ",enableArgs = "true",action = "updateInventoryCheck")
    public Object update(SkuCheckRecord skuCheckRecord,String api_name) throws SessionException {
        return inventoryCheckService.updateCheckTask(getStaff(), skuCheckRecord);
    }

    /**
     * 修改盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "batchUpdateMemo",method = {RequestMethod.POST})
    @LogTag(key = "#ids", content = "'批量修改盘点任务备注，备注：'+#memo")
    public Object batchUpdateMemo(String memo,String ids,String api_name) throws SessionException {
        return inventoryCheckService.batchUpdateMemo(getStaff(),ids,memo);
    }

    /**
     * 删除盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "delete",method = RequestMethod.POST)
    @LogTag(key = "#id+''",content = "'删除盘点任务,货位:'+#goodsSectionCode ")
    public Object delete(Long id,String goodsSectionCode,String api_name) throws SessionException {
        return inventoryCheckService.delete(getStaff(), id);
    }

    /**
     * 批量删除盘点任务
     */
    @RequestMapping(value = "deleteBatch", method = RequestMethod.POST)
    @LogTag(key = "#ids", content = "'批量删除盘点任务，货位：'+#goodsSectionCodes")
    public @ResponseBody Object deleteBatch(String ids, String goodsSectionCodes, String api_name) throws SessionException {
        Assert.hasText(ids, "序号不能为空！");
        return inventoryCheckService.deleteBatch(getStaff(), ids);
    }

    /**
     * 批量新增盘点任务数据
     */
    @ResponseBody
    @RequestMapping(value = "addBatch",method = RequestMethod.POST)
    @LogTag(key = "#goodsSectionCodes",content = "'批量新增盘点任务,货位:'+#goodsSectionCodes")
    public Object addBatch(String assoIds,String goodsSectionCodes, String batchNos, String productTimes,String api_name) throws SessionException, CustomException {
        inventoryCheckService.addBatch(getStaff(), ArrayUtils.toLongList(assoIds), ArrayUtils.toStringList(goodsSectionCodes),false,null);
        return true;
    }

    /**
     * 执行盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "execute",method = RequestMethod.POST)
    @LogTag(key="#skuCheckRecord.goodsSectionCode ",content = "'执行盘点任务，货位:'+#skuCheckRecord.goodsSectionCode ",enableArgs = "true")
    public Object execute(SkuCheckRecord skuCheckRecord,String api_name) throws Exception {
        wmsStockOpService.executeCheckTask(getStaff(), skuCheckRecord);
        return true;
    }

    /**
     * 批量执行盘点任务
     */
    @ResponseBody
    @RequestMapping(value = {"batchExecute", "/batch/finish"}, method = RequestMethod.POST)
    @LogTag(key="#ids ",content = "'执行批量盘点任务' ",enableArgs = "true")
    public Object batchFinish(String ids, String api_name) throws Throwable {
        Assert.isTrue(StringUtils.isNotEmpty(ids), "ids不能为空！");
        wmsStockOpService.batchFinish(getLightStaff(), Arrays.asList(ArrayUtils.toLongArray(ids)));
        return true;
    }

    /**
     * 批量执行盘点任务
     */
    @ResponseBody
    @RequestMapping(value = {"batchAudit", "/batch/audit"},method = RequestMethod.POST)
    @LogTag(key="#ids ",content = "'执行批量审核任务' ",enableArgs = "true")
    public Object batchAudit(String ids, String api_name) throws Throwable {
        Assert.isTrue(StringUtils.isNotEmpty(ids), "ids不能为空！");
        wmsStockOpService.batchAudit(getLightStaff(), Arrays.asList(ArrayUtils.toLongArray(ids)));
        return true;
    }
}

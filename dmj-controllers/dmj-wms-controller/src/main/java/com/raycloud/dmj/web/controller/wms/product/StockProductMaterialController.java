package com.raycloud.dmj.web.controller.wms.product;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.product.model.StockProductPage;
import com.raycloud.dmj.domain.wms.product.params.StockProductMaterialParams;
import com.raycloud.dmj.domain.wms.product.vo.StockProductMaterialVo;
import com.raycloud.dmj.services.wms.product.IStockProductMaterialService;
import com.raycloud.dmj.services.wms.product.IStockProductOrderService;
import com.raycloud.dmj.web.controllers.Sessionable;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓内原料单
 *
 * <AUTHOR>
 * @Date 2019-09-03
 **/
@Controller
@RequestMapping("/wms/product/material")
public class StockProductMaterialController extends Sessionable {

    @Resource
    private IStockProductMaterialService stockProductMaterialService;
    @Resource
    private IStockProductOrderService stockProductOrderService;

    /**
     * 查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public Object query(@RequestBody StockProductMaterialParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null!");
        Staff staff = getLightStaff();
        return stockProductMaterialService.queryStockProductMaterial(staff, params);
    }

    /**
     * 新增
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public Object add(@RequestBody List<StockProductMaterialVo> list, String api_name) throws Exception {
        Assert.notNull(list, "list not null");
        Staff staff = getLightStaff();
        stockProductMaterialService.batchAddStockProductMaterial(staff, list);
        return successResponse();
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public Object edit(@RequestBody List<StockProductMaterialVo> list, String api_name) throws Exception {
        Assert.notNull(list, "list not null!");
        Staff staff = getLightStaff();
        stockProductMaterialService.editStockProductMaterialByIds(staff, list);
        return successResponse();
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object delete(String idStr, String api_name) throws Exception {
        Assert.notNull(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        stockProductMaterialService.deleteByIds(staff, ArrayUtils.toLongList(idStr));
        return successResponse();
    }

    /**
     * 改码类型的原料展示
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/barter/preview", method = RequestMethod.POST)
    @ResponseBody
    public Object previewBarterMaterials(@RequestBody StockProductMaterialParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null!");
        Assert.notNull(params.getProductOrderId(), "productOrderId not null!");
        Staff staff = getLightStaff();
        List<StockProductMaterialVo> list = stockProductOrderService.previewBarterMaterials(staff, params.getProductOrderId());
        if (CollectionUtils.isEmpty(list)) {
            return StockProductPage.buildEmptyResult();
        }
        List<List<StockProductMaterialVo>> partitions = Lists.partition(list, params.getPageSize());
        StockProductPage<StockProductMaterialVo> page = new StockProductPage<>();
        page.setList(partitions.get(Math.min(params.getPageNo() - 1, 0)));
        page.setTotal((long) page.getList().size());
        page.setPage(new Page(params.getPageNo(), params.getPageSize()));
        return page;
    }
}

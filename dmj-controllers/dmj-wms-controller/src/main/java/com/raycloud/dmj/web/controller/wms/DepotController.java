package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.DepotOrderDetail;
import com.raycloud.dmj.domain.wms.DepotQueryParam;
import com.raycloud.dmj.domain.wms.DepotRecord;
import com.raycloud.dmj.domain.wms.DepotTask;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.wms.IDepotBatchService;
import com.raycloud.dmj.services.wms.IDepotService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 返库控制器
 * Created by shaoxianchang on 2016/11/23.
 */
@Controller
@RequestMapping("/wms/depot")
@LogTag(value = "wms")
@AccessShield(value = "1113")
public class DepotController extends WmsInitController {

    @Autowired
    private IDepotService depotService;

    @Autowired
    private IDepotBatchService depotBatchService;

    @Resource
    private IItemServiceDubbo itemsService;

    @Resource
    private WmsHelpBusiness wmsHelpBusiness;

    /**
     * 获取待返库任务列表
     */
    @RequestMapping("/tasks")
    @ResponseBody
    public Object getWaitDepotTasks(DepotQueryParam param, Page page, String api_name) throws SessionException {
        param.setDepotStatus(DepotQueryParam.DEPOT_STATUS_WAIT);
        Staff staff = getLightStaff();
        PageList<DepotTask> pageList = depotService.getTaskPageList(staff, param, page);
        wrapperWithDmjItemsAndWareName(staff, pageList.getList());
        return pageList;
    }

    private void wrapperWithDmjItemsAndWareName(Staff staff, List<? extends DepotTask> tasks) {
        if (CollectionUtils.isNotEmpty(tasks)) {
            List<Long> sysItemIds = Lists.newArrayList();
            List<Long> sysSkuIds = Lists.newArrayList();
            for (DepotTask task : tasks) {
                if (!sysItemIds.contains(task.getSysItemId())) {
                    sysItemIds.add(task.getSysItemId());
                }
                if (!sysSkuIds.contains(task.getSysSkuId())) {
                    sysSkuIds.add(task.getSysSkuId());
                }
            }

            if (sysItemIds.size() > 0 || sysSkuIds.size() > 0) {
                List<DmjItem> dmjItems = itemsService.queryItemDetail(staff, sysItemIds, sysSkuIds, false);
                for (DepotTask task : tasks) {
                    for (DmjItem item : dmjItems) {
                        if (task.getSysItemId().longValue() == item.getSysItemId()) {
                            task.setTitle(item.getTitle());
                            task.setShortTitle(item.getShortTitle());
                            task.setPicUrl(item.getPicPath());
                            task.setRemark(item.getRemark());
                            task.setOuterId(item.getOuterId());
                            task.setHasBatch(item.getHasBatch());
                            if (task.getSysSkuId() > 0L) {
                                List<DmjSku> skus = item.getSkus();
                                for (DmjSku sku : skus) {
                                    if (task.getSysSkuId().longValue() == sku.getSysSkuId()) {
                                        task.setTitle(StringUtils.isNotEmpty(sku.getTitle()) ? sku.getTitle() : item.getTitle());
                                        task.setPicUrl((StringUtils.isEmpty(sku.getPicPath()) || StockConstants.PATH_NO_PIC.equals(sku.getPicPath())) ? item.getPicPath() : sku.getPicPath());
                                        task.setOuterId(StringUtils.isNotEmpty(sku.getOuterId()) ? sku.getOuterId() : item.getOuterId());
                                        task.setRemark(sku.getRemark());
                                        task.setPropertiesAlias(sku.getPropertiesAlias());
                                        task.setPropertiesName(sku.getPropertiesName());
                                        task.setPropColor(sku.getPropColor());
                                        task.setPropOther(sku.getPropOther());
                                        task.setHasBatch(sku.getHasBatch());
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }

            wrapperWithWarehouseName(tasks);
        }
    }

    private void wrapperWithWarehouseName(List<? extends DepotTask> tasks) {
        List<Long> warehouseIds = Lists.newArrayList();
        for (DepotTask task : tasks) {
            if (!warehouseIds.contains(task.getWarehouseId())) {
                warehouseIds.add(task.getWarehouseId());
            }
        }

        List<Warehouse> warehouses = wmsHelpBusiness.queryWarehouseByWarehouseIds(warehouseIds);
        for (DepotTask task : tasks) {
            for (Warehouse warehouse : warehouses) {
                if (task.getWarehouseId().longValue() == warehouse.getId()) {
                    task.setWarehouseName(warehouse.getName());
                }
            }
        }
    }

    /**
     * 获取返库记录列表
     */
    @RequestMapping("/task")
    @ResponseBody
    public Object getDepotTask(Long taskId, String api_name) throws SessionException {
        Assert.notNull(taskId, "请选择一个返库任务！");

        return depotService.getTaskById(getLightStaff(), taskId, true);
    }

    /**
     * 保存返库记录
     */
    @RequestMapping(value = "/task/save", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#task.id", content = "'保存返库操作'")
    public Object saveDepotTaskRecords(@RequestBody DepotTask task, String api_name) throws SessionException {
        Assert.isTrue(task != null && task.getId() != null, "请选择一个返库任务");

        return depotService.saveTaskRecords(getLightStaff(), task);
    }

    /**
     * 执行返库操作
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#task.id", content = "'执行返库操作'")
    public Object execute(@RequestBody DepotTask task, String api_name) throws SessionException {
        Assert.isTrue(task != null && task.getId() != null, "请选择一个返库任务");
        Staff staff = getLightStaff();
        depotService.depot(staff, Collections.singletonList(task));
        //修改库存返库数和可用数
        return successResponse();
    }

    /**
     * 批量执行返库操作
     */
    @RequestMapping(value = "/execute/batch", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#depotTaskIdStr", content = "'批量执行返库操作'")
    public Object executeBatch(Long goodsSectionId, String goodsSectionCode, String depotTaskIdStr, String api_name) throws SessionException {
        Assert.notNull(goodsSectionId, "请选择货位!");
        List<Long> depotTaskIds = ArrayUtils.toLongList(depotTaskIdStr);
        Assert.notNull(depotTaskIds, "请选择返库任务!");
        Assert.isTrue(depotTaskIds.size() > 0, "请选择返库任务!");
        Staff staff = getLightStaff();
        depotBatchService.depotBatch(staff, goodsSectionId, goodsSectionCode, depotTaskIds);
        //修改库存返库数和可用数
        return successResponse();
    }

    /**
     * 获取返库记录列表
     */
    @RequestMapping("/records")
    @ResponseBody
    public Object getDepotRecords(DepotQueryParam param, Page page, String api_name) throws SessionException {
        param.setFinished(true);
        Staff staff = getLightStaff();
        PageList<DepotRecord> pageList = depotService.getRecordPageList(staff, param, page);
        formatGoodsSectionCode(staff, pageList.getList());
        wrapperWithDmjItemsAndWareName(staff, pageList.getList());
        return pageList;
    }

    /**
     * 获取返库锁定明细
     *
     * @param param
     * @param page
     * @param api_name
     * @return
     */
    @RequestMapping("/order/details")
    @ResponseBody
    public Object getOrderDetails(DepotQueryParam param, Page page, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<DepotOrderDetail> pageList = depotService.getOrderDetailPageList(staff, param, page);
        wrapperWithWarehouseName(pageList.getList());
        return pageList;
    }


    /**
     * 按批次号维度查询返库任务明细
     *
     * @param param
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/batchno/details")
    @ResponseBody
    public Object getItemBatchNoDetails(DepotQueryParam param, Integer pageNo, Integer pageSize, String api_name) throws SessionException {
        Assert.notNull(param.getSysItemId(), "请选择商品！");

        if (pageNo != null) {
            param.setPage(new Page().setPageNo(pageNo).setPageSize(pageSize));
        }

        Map<String, Object> result = Maps.newHashMap();
        long totalNum = 0L;
        List<DepotOrderDetail> list = depotService.getItemBatchNoDetails(getLightStaff(), param);
        if (CollectionUtils.isNotEmpty(list)) {
            for (DepotOrderDetail orderDetail : list) {
                totalNum += orderDetail.getDepotNum();
            }
        }
        result.put("totalNum", totalNum);
        result.put("list", list);
        return result;
    }
}

package com.raycloud.dmj.web.controller.wms;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.InventoryDetail;
import com.raycloud.dmj.domain.wms.InventorySheet;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.log.OpLogs;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IInventoryV2Service;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsOpLogHelper;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.InventoryDetailParams;
import com.raycloud.dmj.web.model.wms.InventorySheetParams;
import com.raycloud.dmj.web.model.wms.InventoryTaskParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 盘点任务v2
 */
@Controller
@LogTag(value="wms")
@RequestMapping("/wms/inventory/v2")
public class InventoryV2Controller extends WmsInitController {

    private final static Logger logger = Logger.getLogger(GoodsSectionController.class);

    @Resource
    private IInventoryV2Service inventoryV2Service;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IEventCenter eventCenter;


    @RequestMapping("/oplog/list")
    @ResponseBody
    public Object opLogList(String code, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        Assert.hasText(code, "请选择单据！");
        OpLogs logs = opLogService.list(staff, null, code, null, "wms", null, null, new Page().setPageNo(1).setPageSize(1000), null, null);
        return handlerRecord(logs.getList());
    }

    /**
     * 新增盘点单
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/add",method = RequestMethod.POST)
    public Object sheetAdd(@RequestBody InventorySheet inventorySheet, String api_name) throws Exception {
        Assert.notNull(inventorySheet.getWarehouseId(),"请选择仓库");
        Assert.notNull(inventorySheet.getPdaShowStock(),"请选择pda是否显示系统库存");
        Assert.notNull(inventorySheet.getNotInventoryToZero(),"请选择未盘商品是否盘为0");
        Assert.notNull(inventorySheet.getStatus(),"请传递盘点单状态");
        Assert.isTrue(inventorySheet.getStatus().equals(InventorySheet.DRAFT),"状态不是草稿");
        Staff staff = getLightStaff();
        inventoryV2Service.sheetInsert(staff,inventorySheet);
        WmsOpLogHelper.recordOpLog(staff, opLogService, "addInventorySheet", inventorySheet.getCode(), String.format("新增盘点单:%s", inventorySheet.getCode()));
        return inventorySheet;
    }


    /**
     * 分页查询盘点单列表
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/list",method = RequestMethod.POST)
    public Object sheetList(@RequestBody InventorySheetParams inventorySheetParams, String api_name) throws Exception {
        inventorySheetParams.setQueryType(2);
        return inventoryV2Service.list4InventorySheet(getLightStaff(),inventorySheetParams);
    }

    /**
     * 草稿修改+备注修改
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/update",method = RequestMethod.POST)
    public Object sheetUpdate(@RequestBody InventorySheet inventorySheet, String api_name) throws Exception {
        Assert.notNull(inventorySheet.getWarehouseId(),"请选择仓库");
        Assert.notNull(inventorySheet.getPdaShowStock(),"请选择pda是否显示系统库存");
        Assert.notNull(inventorySheet.getNotInventoryToZero(),"请选择未盘商品是否盘为0");
        Staff staff = getLightStaff();
        String content=null;
        //在这一步判断是草稿修改业务，还是备注修改业务
        if(BooleanUtils.isTrue(inventorySheet.getUpdateRemark())){
            List<InventorySheet> inventorySheets = inventoryV2Service.inventorySheetByIds(staff, Lists.newArrayList(inventorySheet.getId()));
            Assert.notEmpty(inventorySheets,"根据盘点单ID查询有误！");
            content=String.format("修改盘点单备注\n修改前[%s]，修改后[%s]",inventorySheets.get(0).getRemark(),inventorySheet.getRemark());
        }else{
            Assert.isTrue(inventorySheet.getStatus().equals(InventorySheet.DRAFT),"非草稿状态不允许修改");
            content=String.format("修改盘点单%s", inventorySheet.getCode());
        }
        InventorySheet sheet = inventoryV2Service.sheetUpdate(staff, inventorySheet);

        WmsOpLogHelper.recordOpLog(staff, opLogService, "updateInventorySheet", inventorySheet.getCode(), content);
        return sheet;
    }

    /**
     * 批量作废 批量
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/delete",method = RequestMethod.POST)
    public Object sheetDelete(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
        Map<String, String> result = inventoryV2Service.sheetDelete(getLightStaff(), ids);
        return MapUtils.isNotEmpty(result) ? result : successResponse();
    }

    /**
     * 提交 单条+批量 生成盘点任务
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/subbmit",method = RequestMethod.POST)
    public Object sheetSubbmit(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_SUBBMIT_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行提交盘点单生成盘点任务, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        Map<String, String> result = new HashMap<>();
        try{
            result = inventoryV2Service.sheetSubbmit(staff, ids);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "提交盘点单生成盘点任务失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return MapUtils.isNotEmpty(result) ? result : successResponse();
    }

    /**
     * 要拆分的情况下:splitInventoryDetail==1:校验是否有未设置盘点员的盘点单
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/check/split",method = RequestMethod.POST)
    public Object checkSplitSheets(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
        return inventoryV2Service.checkSplitSheets(getLightStaff(), ids);
    }

    /**
     * 复盘
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/subbmit/again",method = RequestMethod.POST)
    public Object sheetSubbmitAgain(@RequestBody List<InventoryDetail> inventoryDetailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(inventoryDetailList),"请传递盘点详情数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_AGAIN_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在生成复盘任务, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        Map<String, String> result = new HashMap<>();
        try{
            result = inventoryV2Service.sheetSubbmitAgain(staff, inventoryDetailList);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "生成复盘任务失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return MapUtils.isNotEmpty(result) ? result : successResponse();
    }

    /**
     * 审核 单条+批量
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/auddit",method = RequestMethod.POST)
    public Object sheetAudit(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
//        result = inventoryV2Service.sheetAuddit(staff, ids);
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_AUDDIT_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null ) {
            throw new IllegalArgumentException("正在进行盘点单审核, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, ids.size()), 10 * 60);
        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.auddit").setArgs(new Object[]{staff, ids, cacheKey}), false);
        Map<String,Object> result = Maps.newHashMap();
        result.put("cacheKey", cacheKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 返回审核/一键添加状态
     *
     * @return
     */
    @RequestMapping(value = {"/sheet/auddit/status","/detail/click/add/status"})
    @ResponseBody
    public Object queryStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process == null){
            process = new ImportProcess();
        }
        if (process.isComplete()) {
            wmsCacheBusiness.delete(cacheKey);
        }
        return process;
    }

    /**
     * 详情部分审核
     */
    @ResponseBody
    @RequestMapping(value = "/detail/auddit",method = RequestMethod.POST)
    public Object detailAudit(@RequestBody InventoryDetailParams detailParams, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailParams.getGoodsSectionCodes()),"请传递货位数据");
        Assert.isTrue(CollectionUtils.isNotEmpty(detailParams.getSheetCodes()),"请传递盘点单单号");
        inventoryV2Service.detailAudit(getLightStaff(), detailParams);
        return successResponse();
    }

    /**
     * 即时盘点单,完成
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/finish",method = RequestMethod.POST)
    public Object sheetFinish(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
        inventoryV2Service.sheetFinish(getLightStaff(), ids);
        return successResponse();
    }

    @ResponseBody
    @RequestMapping(value = "/process/key/delete",method = RequestMethod.POST)
    public Object deleteKey(String key, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(key),"请传递盘点单id数据");
        wmsCacheBusiness.delete(key);
        return successResponse();
    }

//===============================================盘点任务===========================================

    /**
     * 任务维度列表
     */
    @ResponseBody
    @RequestMapping(value = "/task/list",method = RequestMethod.POST)
    public Object taskList(@RequestBody InventoryTaskParams inventoryTaskParams, String api_name) throws Exception {
        inventoryTaskParams.setQueryType(2);
        return inventoryV2Service.list4InventoryTask(getLightStaff(),inventoryTaskParams);
    }

    /**
     * 盘点任务详情
     */
    @ResponseBody
    @RequestMapping(value = "/task/detail",method = RequestMethod.POST)
    public Object taskDetail(@RequestBody InventoryTaskParams inventoryTaskParams, String api_name) throws Exception {
        Assert.notNull(inventoryTaskParams.getCode(),"请传递盘点任务号");
        Assert.notNull(inventoryTaskParams.getSheetCodes(),"请传递盘点单号");
        return inventoryV2Service.taskDetail(getLightStaff(),inventoryTaskParams);
    }

    /**
     * 盘点次数详情
     *
     */
    @ResponseBody
    @RequestMapping(value = "/detail/info",method = RequestMethod.POST)
    public Object taskDetail(@RequestBody InventoryDetailParams inventoryDetailParams, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(inventoryDetailParams.getSheetCodes()),"请传递盘点单号");
        Assert.isTrue(StringUtils.isNotEmpty(inventoryDetailParams.getGoodsSectionCode()),"请传递货位编码");
        return inventoryV2Service.detailInfo(getLightStaff(),inventoryDetailParams);
    }

    /**
     * 任务作废
     */
    @ResponseBody
    @RequestMapping(value = "/task/delete",method = RequestMethod.POST)
    public Object taskDelete(@RequestBody List<Long> ids, String api_name) throws Exception {
        inventoryV2Service.taskDelete(getLightStaff(), ids);
        return successResponse();
    }

//===============================================盘点详情===========================================

    /**
     * 正常添加 查询仓内库存接口/wms/section/query/stock根据结果上传goodsSectionCode
     */
    @ResponseBody
    @RequestMapping(value = "/detail/add",method = RequestMethod.POST)
    public Object detailAdd(@RequestBody List<InventoryDetail> list, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.isTrue(CollectionUtils.isNotEmpty(list),"请传递要添加的货位/暂存区数据");
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_ADD_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行添加详情任务, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));

        try{
            inventoryV2Service.detailInsert(staff, list);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "盘点单添加数据失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return successResponse();
    }

    /**
     * 一键添加 默认加载当前盘点仓库的全部未占用货位和暂存区数据；
     */
    @ResponseBody
    @RequestMapping(value = "/detail/click/add",method = RequestMethod.POST)
    public Object detailClickAdd(@RequestBody InventoryDetail inventoryDetail, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(inventoryDetail.getSheetCode()),"请传递盘点单号");
        String cacheKey = wmsCacheBusiness.getKey(getStaff(), WmsCacheEnum.INVENTORY_V2_CLICK_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null ) {
            throw new IllegalArgumentException("正在进行盘点单一键添加, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.click.add").setArgs(new Object[]{getLightStaff(), inventoryDetail.getSheetCode(), inventoryDetail.getShowZero(), cacheKey}), false);
        Map<String,Object> result = Maps.newHashMap();
        result.put("cacheKey", cacheKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 列表
     */
    @ResponseBody
    @RequestMapping(value = "/detail/list",method = RequestMethod.POST)
    public Object detailList(@RequestBody InventoryDetailParams inventoryDetailParams, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(inventoryDetailParams.getSheetCodes()),"请传递盘点单号");
        return inventoryV2Service.list4InventoryDetail(getLightStaff(),inventoryDetailParams);
    }

    /**
     * 实时计算详情
     */
    @ResponseBody
    @RequestMapping(value = "/detail/caculate",method = RequestMethod.POST)
    public Object detaiCaculate(@RequestBody InventorySheetParams inventorySheetParams, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(inventorySheetParams.getCode()),"请传递盘点单号");

        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_CACULATE_SHEET);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行盘点单实时计算, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));

        try{
            inventoryV2Service.detaiCaculate(staff,inventorySheetParams);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "盘点单实时更新详情失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return successResponse();
    }

    /**
     * 批量删除
     */
    @ResponseBody
    @RequestMapping(value = "/detail/batch/delete",method = RequestMethod.POST)
    public Object detailBatchDelete(@RequestBody InventoryDetailParams inventoryDetailParams, String api_name) throws Exception {
        Assert.notNull(inventoryDetailParams.getGoodsSectionCodes(),"请选择要删除的货位数据");
        Assert.isTrue(CollectionUtils.isNotEmpty(inventoryDetailParams.getSheetCodes()),"请传递盘点单号");
        inventoryV2Service.detailBatchDelete(getLightStaff(), inventoryDetailParams);
        return successResponse();
    }

    /**
     * 分配盘点员
     */
    @ResponseBody
    @RequestMapping(value = "/detail/allocate/inventory",method = RequestMethod.POST)
    public Object detailAllocateInventory(@RequestBody List<InventoryDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"请传递要分配盘点员的详情数据");
        List<InventoryDetail> inventoryDetails = inventoryV2Service.detailAllocateInventory(getLightStaff(), detailList);
        return CollectionUtils.isNotEmpty(inventoryDetails) ? inventoryDetails : successResponse();
    }

    /**
     * 调整盘点员
     */
    @ResponseBody
    @RequestMapping(value = "/detail/adjust/inventory",method = RequestMethod.POST)
    public Object detailAdjustInventory(@RequestBody List<InventoryDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"请传递要分配盘点员的详情数据");
        Map<String,String> result = inventoryV2Service.detailAdjustInventory(getLightStaff(), detailList);
        return result.size() > 0 ? result : successResponse();
    }

    /**
     * 后端自动分配盘点员,不传递货位
     */
    @ResponseBody
    @RequestMapping(value = "/detail/auto/allocate/inventory",method = RequestMethod.POST)
    public Object detailAutoAllocateInventory(@RequestBody InventoryDetailParams detailParams, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.isTrue(CollectionUtils.isNotEmpty(detailParams.getDetailList()),"请传递盘点员信息");
        Assert.isTrue(CollectionUtils.isNotEmpty(detailParams.getSheetCodes()),"请传递盘点单号");
        Assert.isTrue("goodsSectionCode".equals(detailParams.getGroupBy()),"传递的参数groupBy有误");

        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_AUTO_ALLOCATE_INVENTORY);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行分配盘点员, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        List<InventoryDetail> inventoryDetails = new ArrayList<>();
        try{
            inventoryDetails = inventoryV2Service.detailAutoAllocateInventory(getLightStaff(), detailParams);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "自动分配盘点员失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return CollectionUtils.isNotEmpty(inventoryDetails) ? inventoryDetails : successResponse();
    }

    /**
     * excel导入
     */
    @ResponseBody
    @RequestMapping(value = "/excel/import",method = RequestMethod.POST)
    public Object detailExcelAdd(MultipartFile file, Integer importType, Long id, String api_name) throws Exception {
        Assert.notNull(id,"请传递盘点单id");
        Assert.notNull(importType,"请传递导入类型");
        InventorySheetParams params = new InventorySheetParams();
        Staff staff = getStaff();
        List<Long> ids=new ArrayList();
        ids.add(id);
        List<InventorySheet> sheetIdsList= inventoryV2Service.inventorySheetByIds(staff, ids);
        if(CollectionUtils.isNotEmpty(sheetIdsList)) {
            InventorySheet inventorySheet = sheetIdsList.get(0);
            Assert.isTrue(inventorySheet.getStatus().equals(InventorySheet.DRAFT),"盘点单不是草稿状态,不允许导入数据");
        }

        Map<String, Object> result = new HashMap<String, Object>();
        //文件校验
        if (file == null) {
            result.put("errorMessage", "请选择要导入的excel文件");
            return result;
        }
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix) && !".csv".equals(fileSuffix)) {
            result.put("errorMessage", "文件格式只支持EXL表格与CSV格式");
            return result;
        }
        if (file.getSize() > 2 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于2M");
            return result;
        }

        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_IMPORT_EXCEL);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("盘点单不能同时导入，请稍等！");
        }

        String[][] data ;
        try {
            data = convertExcelData(file,1);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("errorMessage", "上传文件内容转化数据异常，检查内容是否为空, 请下载后重新尝试");
            return result;
        }

        process = new ImportProcess();
        process.setTotalNum(data.length);
        process.setComplete(false);
        wmsCacheBusiness.set(importKey, process, 30 * 60);

        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.import").setArgs(new Object[]{staff, importType, id}), data);

        result.put("importKey", importKey);
        result.put("isSuccess", true);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/import/status",method = RequestMethod.GET)
    public Object queryImportStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            process.setRightNum(process.getTotalNum() - process.getErrorNum());
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }
    
    @ResponseBody
    @RequestMapping(value = "/createSheet/excel/import",method = RequestMethod.POST)
    public Object createSheetExcelImport(MultipartFile file, Long warehouseId, String  remark, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(warehouseId, "请传入盘点仓库!");
        Map<String, Object> result = new HashMap<String, Object>();
        //文件校验
        if (file == null) {
            result.put("errorMessage", "请选择要导入的excel文件");
            return result;
        }
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix) && !".csv".equals(fileSuffix)) {
            result.put("errorMessage", "文件格式只支持EXL表格与CSV格式");
            return result;
        }
        if (file.getSize() > 5 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于5M");
            return result;
        }
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.INVENTORY_V2_CREATE_SHEET_IMPORT_EXCEL);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("已有Excel导入在创建盘点单，请稍等！");
        }

        String[][] data ;
        Integer ignoreRows = 2;
        try {
            //PS:这里不过滤数据,为后面失败导出保留数据
            data = convertExcelData(file, 0);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("errorMessage", "上传文件内容转化数据异常，检查内容是否为空, 请下载后重新尝试");
            return result;
        }

        process = new ImportProcess();
        process.setTotalNum(data.length - ignoreRows);
        process.setComplete(false);
        wmsCacheBusiness.set(importKey, process, 30 * 60);

        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.createSheet.import").setArgs(new Object[]{staff, warehouseId, remark, ignoreRows}), data);

        result.put("importKey", importKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 导出
     */
    @ResponseBody
    @RequestMapping(value = "/detail/excel/export",method = RequestMethod.POST)
    public Object detailExcelExport(@RequestBody InventorySheetParams inventorySheetParams, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        List<Long> ids = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(inventorySheetParams.getIds())) {
            ids.addAll(inventorySheetParams.getIds());
        }else {
            PageList<InventorySheet> sheetPageList = inventoryV2Service.list4InventorySheet(staff, inventorySheetParams);
            if(null != sheetPageList) {
                List<InventorySheet> list = sheetPageList.getList();
                if(CollectionUtils.isNotEmpty(list)) {
                    ids.addAll(list.stream().map(InventorySheet::getId).collect(Collectors.toList()));
                }
            }
        }
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"根据条件未找到盘点单数据");
        String[] EXCEL_HEADER = new String[]{"序号","盘点单号","盘点单类型","盘点仓库","盘点状态","未盘商品","PDA系统库存","备注","制单时间","制单人","提交时间","提交人","审核时间","审核人",
                "商品名称","商家编码","规格名称","箱码","批次","生产日期","盘点前数量","盘点后数量","盘点差异数","盘盈","盘亏","差异状态","库存类型","货位编码","库区","货位质量","盘点员","盘点次数","最近盘点时间","成本价","差异金额","盘点结果"};
        List<String[]> arrList = new ArrayList<>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("盘点单明细数据".getBytes(), "utf-8") + org.apache.commons.lang3.time.DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("盘点单明细数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.download.excel").setArgs(new Object[]{staff, param, ids}), false);

        return successResponse();
    }

    /**
     * 盘点录入
     */
    @ResponseBody
    @RequestMapping(value = "/detail/inventory/entry/operate",method = RequestMethod.POST)
    public Object inventoryDetailEntry(@RequestBody List<InventoryDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"请传递要录入的盘点单详情！");
        Staff staff = getLightStaff();
        inventoryV2Service.inventoryEntry(staff,detailList);
        return successResponse();
    }

    /**
     * PC盘点录入查询
     */
    @ResponseBody
    @RequestMapping(value = "/detail/inventory/entry/query", method = RequestMethod.POST)
    public Object inventoryDetailEntryQuery(@RequestBody InventoryDetailParams detailParams, String api_name) throws Exception {
        Assert.notNull(detailParams, "请传递查询的条件！");
        Staff staff = getLightStaff();
        return inventoryV2Service.inventoryEntryQuery(staff, detailParams);
    }

    /**
     * PC盘点录入导出
     */
    @ResponseBody
    @RequestMapping(value = "/detail/inventory/entry/export", method = RequestMethod.POST)
    public Object inventoryDetailEntryQuery(String ids, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(ids), "请传递需要导出的盘点信息！");
        List<Long> list = DataUtils.ids2List(ids);
        Staff staff = getLightStaff();
        eventCenter.fireEvent(this, new EventInfo("wms.inventory.v2.entry.export").setArgs(new Object[]{staff, list}), false);
        return successResponse();
    }




    @ResponseBody
    @RequestMapping(value = "/test",method = RequestMethod.POST)
    public Object test() throws Exception {
        return inventoryV2Service.test(getLightStaff());
    }

}

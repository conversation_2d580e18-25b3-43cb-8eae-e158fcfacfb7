package com.raycloud.dmj.web;

import com.raycloud.dmj.domain.enums.ModuleCodeEnum;
import com.raycloud.dmj.domain.wms.router.RouterResponse;
import com.raycloud.dmj.web.exception.ErpResultWarpper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 有关数据的包装拦截器
 *
 * <AUTHOR>
 */
@Component
@Aspect
public class ResponseDataWrapperAspect extends ErpResultWarpper {


    @Pointcut("@annotation(org.springframework.web.bind.annotation.RequestMapping)")
    public void aspect() {
    }


    @Around("aspect()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        long start=System.currentTimeMillis();
        String apiName=getApiName(joinPoint.getArgs());
        try {
            result = joinPoint.proceed(joinPoint.getArgs());
            // 一般响应到页面时，返回的内容一般都是字符串类型
            if (result instanceof String) {
                return result;
            }

            if (result instanceof ResponseDataWrapper || result instanceof RouterResponse){
                return result;
            }
            return getSucResult(apiName, result).setqTime(System.currentTimeMillis()-start);
        } catch (Throwable e) {
            return getErrorResult(e, apiName, ModuleCodeEnum.ERP_WMS).setqTime(System.currentTimeMillis()-start);
        }
    }


}

package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.wms.ILogisticsPackageService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.model.wms.LogisticsPackageParams;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName LogisticsPackageController.java
 * @Description 包裹
 * @createTime 2022年12月30日 16:04:00
 */
@RestController
@RequestMapping("/wms/logistics/package")
public class LogisticsPackageController extends Sessionable {

    @Resource
    private ILogisticsPackageService logisticsPackageService;


    /**
     * 查询包裹单
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public Object queryPackages(LogisticsPackageParams params, String api_name) throws SessionException {
        Staff staff = getStaff();
        return logisticsPackageService.queryPackages(staff, params);
    }

    /**
     * 查询包裹单明细（本身）
     */
    @RequestMapping(value = "/detail/query", method = RequestMethod.POST)
    public Object queryPackageDetails(Page page, Long mainId, String api_name) throws SessionException {
        Assert.notNull(mainId, "请选择单据");
        Staff staff = getStaff();
        return logisticsPackageService.queryPackageDetails(staff, page, mainId);
    }

    /**
     * 查询包裹单明细（套件明细）
     */
    @RequestMapping(value = "/detail/single/query", method = RequestMethod.POST)
    public Object queryPackageDetailSingles(Page page, Long mainId, Long combineId, String api_name) throws SessionException {
        Assert.notNull(mainId, "请选择单据");
        Assert.notNull(combineId, "请选择单据");
        Staff staff = getStaff();
        return logisticsPackageService.queryPackageDetailSingles(staff, mainId, combineId);
    }

}

package com.raycloud.dmj.web.controller;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.wms.GoodsSectionDisplay;
import com.raycloud.dmj.domain.wms.HandlerRecord;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.domain.wms.result.WmsBatchExecuteException;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.storage.utils.ExcelConverter;
import com.raycloud.dmj.utils.WmsHighlightUtils;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.utils.IpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Created by ZuoYun on 6/1/15. Time: 9:24 AM Information:
 */

public class WmsInitController extends Sessionable {

    @Resource
    protected IOpLogService opLogService;

    @Resource
    protected WmsHelpBusiness wmsHelpBusiness;

    @Resource
    private IDownloadCenterService downloadCenterService;

    public final static long MAX_FILE_SIZE=5* 1024*1024;

    protected void writeOpLog(Staff staff, Domain domain, String action, String content, String args) {
        writeOpLog(staff, domain, action, null, content, args, 0);
    }

    /**
     * 写opLog
     */
    protected void writeOpLog(Staff staff, Domain domain, String action, String key, String content, String args, Integer isError) {
        //记录操作日志
        OpLog log = new OpLog();
        log.setDomain(domain.getValue());
        log.setAction(action);
        log.setKey(key);
        log.setContent(content);
        log.setArgs(args);
        log.setIp(IpUtils.getClientIP(request));
        log.setIsError(isError);
        opLogService.record(staff, log);
    }

    protected void writeOpLog(Staff staff, String action, String content, String args) {
        writeOpLog(staff, Domain.WMS, action, content, args);
    }

    public void formatGoodsSectionCode(Staff staff, List<? extends GoodsSectionDisplay> list) {
        wmsHelpBusiness.formatGoodsSectionCode(staff, list, true);
    }

    public void buildHighlights(List<?> data, String searchKey, String queryType) {
        if (StringUtils.isEmpty(searchKey) || StringUtils.isEmpty(queryType)) {
            return;
        }
        WmsHighlightUtils.buildHighlights(data, searchKey, queryType);
    }

    protected void checkBeforeImport(MultipartFile file, Integer type) {
        if (file == null) {
            throw new IllegalArgumentException("请选择要导入的excel文件");
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("上传的excel文件大小不能超过5MB");
        }

        String filename = file.getOriginalFilename();
        if (! (filename.endsWith(".xls") || filename.endsWith(".xlsx") || filename.endsWith(".csv"))) {
            throw new IllegalArgumentException("文件格式只支持EXL表格与CSV格式");
        }
        if (type == null) {
            throw new IllegalArgumentException("请选择一个导入方式！");
        }
    }

    protected String[][] convertExcelData(MultipartFile file) {
        return convertExcelData(file, 2);
    }

    protected String[][] convertExcelData(MultipartFile file, Integer ignoreRows) {
        String[][] data;
        try {
            data = ExcelConverter.excel2DataArr(file.getInputStream(), ignoreRows);
        } catch (Exception e) {
            throw new IllegalArgumentException("上传的文件格式不正确");
        }

        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("Excel数据不能为空！");
        }
        return data;
    }

    /**
     * 处理批量异常
     * @param results
     * @param <T>
     */
    protected <T> void handleBatchResults(List<BatchExecuteResult<T>> results) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        List<BatchExecuteResult> success = Lists.newArrayListWithCapacity(results.size());
        List<BatchExecuteResult> errors = Lists.newArrayList();
        for (BatchExecuteResult<T> result : results) {
            if (result.isSuccess()) {
                success.add(result);
            } else {
            	result.setExtraResult(null);
                errors.add(result);
            }
        }
        if (!errors.isEmpty()) {
            //throw new BatchExecuteException(success.size() > 0 ? "部分操作失败！" : "操作失败！", success, errors);
            throw new WmsBatchExecuteException(success.size() > 0 ? "部分操作失败！" : "操作失败！", errors);
        }
    }

    /**
     * 处理系统日志显示
     * @param list
     * @return
     */
    public static List<HandlerRecord> handlerRecord(List<OpLog> list) {
        if (CollectionUtils.isEmpty(list))
            return Collections.emptyList();

        List<HandlerRecord> records = Lists.newArrayListWithCapacity(list.size());
        for (OpLog opLog : list) {
            if (opLog.getIsError() == 1) {
                continue;
            }
            HandlerRecord record = new HandlerRecord();
            record.setId(opLog.getId());
            record.setDomain(opLog.getDomain());
            record.setIp(opLog.getIp());
            record.setAction(opLog.getAction());
            record.setStaffName(opLog.getStaffName());
            record.setCreated(opLog.getCreated());
            if(StringUtils.isNoneBlank(opLog.getContent())) {
                String[] contents = opLog.getContent().split(":");
                if (contents.length >= 1) {
                    record.setContent(contents[0]);
                    if (contents.length >= 3) {
                        record.setWarehouseName(contents[2]);
                    }
                }
            }
            records.add(record);
        }
        return records;
    }

    /**
     * 校验当前该模块是否有导出任务
     * @param staff
     * @param module
     */
    protected void checkModuleHasExport(Staff staff, EnumDownloadCenterModule module) {
        DownloadCenter condition = new DownloadCenter();
        condition.setModule(module.getCode());
        condition.setStaffId(staff.getId());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出!");
        }
    }
}

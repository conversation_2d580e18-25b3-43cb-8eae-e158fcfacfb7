package com.raycloud.dmj.web;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.controller.StaffSessionContext;
import com.raycloud.dmj.web.interceptors.RequestBodyParamsUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;

@ControllerAdvice
public class WmsRequestBodyAdviceAdapter extends RequestBodyAdviceAdapter {

    @Override
     public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
         return true;
     }

     @Override
     public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
                                 Class<? extends HttpMessageConverter<?>> converterType) {
         Staff staff = StaffSessionContext.get();
         RequestBodyParamsUtils.setParams(staff,body);
         return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
     }
}

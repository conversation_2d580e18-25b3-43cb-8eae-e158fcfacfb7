package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.StockWmsBridgeBusiness;
import com.raycloud.dmj.business.wms.WmsBatchOutBusiness;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.StockWmsBridgeBusiness;
import com.raycloud.dmj.business.wms.WmsBatchOutBusiness;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.item.ItemIdInfo;
import com.raycloud.dmj.domain.item.ItemKey;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemStockSimple;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.stock.Stock;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.stock.StockQueryParams;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.AllocateTask;
import com.raycloud.dmj.domain.wms.AllocateTaskDetail;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.WmsItem;
import com.raycloud.dmj.domain.wms.enums.AllocateExportType;
import com.raycloud.dmj.domain.wms.enums.AllocateOpType;
import com.raycloud.dmj.domain.wms.enums.AllocateTaskType;
import com.raycloud.dmj.domain.wms.enums.ValidateAllocateStockTypeEnum;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.log.OpLogs;
import com.raycloud.dmj.services.stock.IStockServiceDubbo;
import com.raycloud.dmj.services.wms.IAllocateTaskService;
import com.raycloud.dmj.services.wms.IWmsConfigService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms2.IWmsStockService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsAllocateUtils;
import com.raycloud.dmj.utils.WmsOpLogHelper;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.ResponseDataWrapper;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.exception.ResultBean;
import com.raycloud.dmj.web.model.wms.*;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.dmj.web.utils.Md5Encrypt;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/8/14 15:54
 */
@Controller
@RequestMapping("/wms/allocate")
@LogTag(value = "wms")
public class AllocateTaskController extends WmsInitController {

    @Resource
    private IAllocateTaskService allocateTaskService;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private StockWmsBridgeBusiness stockWmsBridgeBusiness;

    @Resource
    private WmsHelpBusiness wmsHelpBusiness;

    @Resource
    private WmsBatchOutBusiness wmsBatchOutBusiness;

    @Resource
    private WmsItemBusiness wmsItemBusiness;
    
    @Resource
    private IWmsStockService wmsStockService;
    
    @Resource
    private IItemServiceDubbo itemServiceDubbo;
    
    @Resource
    private WmsSectionController wmsSectionController;
    
    @Resource
    private IWmsService  wmsService;

    @Resource
    private IStockServiceDubbo stockServiceDubbo;

    @Resource
    protected WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IWmsConfigService wmsConfigService;


    /**
     * 查询调拨任务
     * @param params
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(AllocateQueryParams params, Page page, String api_name) throws Exception {
        PageList<AllocateTask> pageList = allocateTaskService.queryPageList(getLightStaff(), params, page);
        if (CollectionUtils.isNotEmpty(pageList.getList())) {
            for (AllocateTask task : pageList.getList()) {
            	//历史数据处理
            	if (Long.valueOf(0).equals(task.getActualOutNum()) && 
            			(AllocateTaskType.FINISHED.getStatus().equals(task.getStatus()) 
            					|| AllocateTaskType.ALLOCATE_OUT.getStatus().equals(task.getStatus())
            					|| AllocateTaskType.ALLOCATE_IN.getStatus().equals(task.getStatus()) 
            					|| AllocateTaskType.SHELVED.getStatus().equals(task.getStatus()))) {
            		task.setActualOutNum(task.getOutNum());
            		task.setActualOutTotalAmount(task.getOutTotalAmount());
				}
                task.setDiffNum(task.getActualOutNum() - task.getInNum());
                task.setDiffAmount(BigDecimal.valueOf(task.getActualOutTotalAmount())
                		.subtract(BigDecimal.valueOf(task.getInTotalAmount())).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        return pageList;
    }

    /**
     * 查看操作记录
     *
     * @param code
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/oplog/list")
    @ResponseBody
    public Object opLogList(String code, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        Assert.hasText(code, "请选择单据！");

        OpLogs logs = opLogService.list(staff, null, code, null, "wms", null, null, new Page().setPageNo(1).setPageSize(100), null, null);
        return handlerRecord(logs.getList());
    }

    /**
     * 查询任务明细
     * @param id
     * @param api_name
     * @param withStock 查库存
     * @return
     * @throws Exception
     */
    @RequestMapping("/detail")
    @ResponseBody
    public Object detail(Long id, Boolean withStock, String api_name) throws Exception {
        Assert.notNull(id, "任务id不能为空！");
        Staff staff = getLightStaff();
        AllocateTask task = allocateTaskService.queryById(staff, id, true, true);
        if (BooleanUtils.isTrue(withStock)) {
            fillWithStockAndBatchNo(staff, task.getOutWarehouseId(), task.getDetails(), task);
        }
        return task;
    }

    private void fillWithStockAndBatchNo(Staff staff, Long warehouseId, List<AllocateTaskDetail> details, AllocateTask task) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Long outGoodSectionId = details.get(0).getOutGoodSectionId();
        if (outGoodSectionId != null) {
        	List<AssoGoodsSectionSku> assos = new ArrayList<AssoGoodsSectionSku>();
            for (AllocateTaskDetail detail : details) {
                AssoGoodsSectionSku assoGoodsSectionSku = new AssoGoodsSectionSku();
                assoGoodsSectionSku.setGoodsSectionId(detail.getOutGoodSectionId());
                assoGoodsSectionSku.setSysItemId(detail.getSysItemId());
                assoGoodsSectionSku.setSysSkuId(detail.getSysSkuId());
                assoGoodsSectionSku.setProductTime(detail.getProductTime());
                assoGoodsSectionSku.setBatchNo(detail.getBatchNo());
                assos.add(assoGoodsSectionSku);
            }
            List<GoodsSectionSkuVo> goodsSectionSkuVos = wmsService.queryByAssos(staff, assos);
            Map<String, GoodsSectionSkuVo> goodsSectionSkuVoMap = new HashMap<String, GoodsSectionSkuVo>();
            for (GoodsSectionSkuVo goodsSectionSkuVo : goodsSectionSkuVos) {
                String key = goodsSectionSkuVo.getGoodsSectionId() + "_" + goodsSectionSkuVo.getSysItemBatchKey() ;
                goodsSectionSkuVoMap.put(key, goodsSectionSkuVo);
            }
            for (AllocateTaskDetail detail : details) {
            	String key = detail.getOutGoodSectionId() + "_" + detail.getSysItemBatchProductKey();
            	GoodsSectionSkuVo goodsSectionSkuVo = goodsSectionSkuVoMap.get(key);
            	detail.setAvailableInStock((goodsSectionSkuVo == null || goodsSectionSkuVo.getTotalNum() == null) ? 0L : goodsSectionSkuVo.getTotalNum().longValue());
            }
		} else {
	        List<ItemKey> itemIdInfoList = Lists.newLinkedList();
	        for (AllocateTaskDetail detail : details) {
	            itemIdInfoList.add(new ItemKey(detail.getSysItemId(), detail.getSysSkuId(), Integer.valueOf(1).equals(detail.getSuitStatus())));
	        }
	        StockQueryParams params = new StockQueryParams();
	        WmsConfig config = wmsConfigService.get(staff);
	        if (ValidateAllocateStockTypeEnum.PICK_ALLOCATE.getType().equals(config.getValidateAllocateStockSection())
                    ||ValidateAllocateStockTypeEnum.SCRATCH_SPACE.getType().equals(config.getValidateAllocateStockSection())) {
	            params.setQueryWorkingStock(true);
	        } else if (ValidateAllocateStockTypeEnum.CONFIGURABLE.getType().equals(config.getValidateAllocateStockSection())
                    && WmsUtils.isNewWms(staff)) {
	            params.setQueryAllocateStock(true);
	        }
	        List<FarERPStock> farERPStocks = wmsHelpBusiness.queryItemStocks(staff, itemIdInfoList, null, params);
	        Map<String, List<FarERPStock>> farERPStockMap = Optional.ofNullable(farERPStocks).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(FarERPStock :: getItemKey));
            Map<String, Long> useValidateNumMap = allocateTaskService.getWarehouseStock(staff, task, true).stream().collect(Collectors.toMap(a -> a.getSysItemKey(), WmsChangeAffect::getUseValidateNum));

            for (AllocateTaskDetail detail : details) {
	            List<FarERPStock> stocks = farERPStockMap.get(detail.getSysItemKey());
                Long useValidateNum = useValidateNumMap.get(detail.getSysItemKey());
                if (stocks != null) {
                    Pair<ItemStockSimple, List<ItemStockSimple>> pair = wmsBatchOutBusiness.convertToItemStock(stocks, warehouseId, useValidateNum);
                    detail.setStocks(pair.getRight());
                    detail.setStock(pair.getLeft());
	            } else {
	                detail.setStocks(Lists.newArrayList(wmsBatchOutBusiness.buildDefaultStockSimple()));
	            }
	        }
		}
    }

    /**
     * 保存任务
     * @param task
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public Object save(@RequestBody AllocateTask task, String api_name) throws Exception {
        Staff staff = getLightStaff();
        boolean isAdd = task.getId() == null;
        AllocateTask result = allocateTaskService.save(staff, task);
        WmsOpLogHelper.recordOpLog(staff, opLogService, "saveAllocateTask", task.getCode(), String.format("%s调拨单:%s:%s", isAdd ? "创建" : "修改", task.getCode(), task.getOutWarehouseName()));
        return result;
    }

    /**
     * 查询可调拨数
     * @param affects
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query/allocateNum", method = RequestMethod.POST)
    @ResponseBody
    public Object queryAllocateNum(@RequestBody AllocateTask task, String api_name) throws Exception {
        Staff staff = getLightStaff();
        List<WmsChangeAffect> affects = allocateTaskService.getWarehouseStock(staff, task, true);
        if (CollectionUtils.isEmpty(affects)) {
            return new HashMap<>();
        }
        return affects.stream().collect(Collectors.toMap(a -> getAssoKey(a), WmsChangeAffect::getUseValidateNum));
    }

    private String getAssoKey(WmsChangeAffect affect) {
        return affect.getSysItemKey() + "_" + (StringUtils.isEmpty(affect.getOutGoodSectionCode()) ? 0 : affect.getOutGoodSectionCode());
    }

    /**
     * 调拨单据更新(仅单据)
     * @param task
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/task/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateTask(@RequestBody AllocateTask task, String api_name) throws Exception {
        Staff staff = getLightStaff();
        return allocateTaskService.updateTask(staff, task);
    }

    /**
     * 审核任务
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    public Object audit(String ids, String codes, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨单！");
        Assert.hasText(codes, "请选择调拨单！");

        Staff staff = getLightStaff();
        List<BatchExecuteResult<AllocateTask>> results = allocateTaskService.audit(staff, ArrayUtils.toLongList(ids), false);
        handleBatchResults(results);
        return results;
    }

    /**
     * 财务审核任务
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/finance", method = RequestMethod.POST)
    @ResponseBody
    public Object finance(String ids, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨单！");

        Staff staff = getLightStaff();
        allocateTaskService.finance(staff, ArrayUtils.toLongList(ids));
        return successResponse();
    }

    /**
     * 财务审核任务
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/financeVerify", method = RequestMethod.POST)
    @ResponseBody
    public Object financeVerify(String ids, String api_name,Boolean financeVerify) throws Exception {
        Assert.hasText(ids, "请选择调拨单！");

        Staff staff = getLightStaff();
        allocateTaskService.financeVerify(staff, ArrayUtils.toLongList(ids),financeVerify);
        return successResponse();
    }

    /**
     * 作废任务
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    @ResponseBody
    public Object cancel(String ids, String codes, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨单！");
        Assert.hasText(codes, "请选择调拨单！");

        Staff staff = getLightStaff();
        List<BatchExecuteResult<AllocateTask>> results = allocateTaskService.cancel(staff, ArrayUtils.toLongList(ids));
        handleBatchResults(results);
        return successResponse();
    }


    /**
     * 完成任务
     * @param task
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/complete", method = RequestMethod.POST)
    @ResponseBody
    public Object complete(AllocateTask task, String api_name) throws Exception {
        Assert.notNull(task.getId(), "请选择调拨单！");
        Assert.hasText(task.getCode(), "请选择调拨单！");
        Assert.hasText(task.getFinishReason(), "手动完成原因不能为空！");

        Staff staff = getLightStaff();
        AllocateTask result = allocateTaskService.complete(staff, task);
        WmsAllocateUtils.recordAllocateOpLogs(staff, opLogService, AllocateOpType.COMPLETE_TASK, IpUtils.getClientIP(request), result);
        return successResponse();
    }

    /**
     * 调拨出库单导出
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/export")
    @ResponseBody
    public Object export(@RequestBody AllocateQueryParams params, String api_name) throws Exception {
        Assert.hasText(params.getExportType(), "请选择导出类型");
        Staff staff = getLightStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.STOCK.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        AllocateExportType exportType = AllocateExportType.parseExportType(params.getExportType());
        String fileName = new String((exportType.getName() + "明细导出").getBytes(), "utf-8");
        fileName += DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam downloadParam = new FileDownloadParam();
        downloadParam.setFileName(fileName);
        downloadParam.setExcelTitle(exportType.getName() + "明细导出");
        downloadParam.setModule(EnumDownloadCenterModule.STOCK.getCode());
        eventCenter.fireEvent(this, new EventInfo("wms.allocate.export").setArgs(new Object[]{staff, downloadParam, params}), false);

        return successResponse();
    }
    
    
    /**
     * 查询货位商品数据
     */
    @PostMapping(value = "/item/section/query")
    @ResponseBody
    public Object queryStock(@RequestBody WmsStockQueryParams params, String api_name) throws Exception {
    	Staff staff = getLightStaff();
    	List<ItemNumVo> itemNumList = params.getItemNumList();
    	List<Long> sysItemIds = Lists.newArrayList();
    	List<Long> sysSkuIds = Lists.newArrayList();
    	Map<String,String> outerIdMap = new LinkedHashMap<>();
    	Map<String,Long> numMap = new HashMap<>();
    	ItemNumVo itemNumVo = null;
    	for (int i = 0; i < itemNumList.size(); i++) {
    		itemNumVo = itemNumList.get(i);
    		sysItemIds.add(itemNumVo.getSysItemId());
    		sysSkuIds.add(itemNumVo.getSysSkuId());
    		outerIdMap.put(itemNumVo.getSysItemId() + "_" + itemNumVo.getSysSkuId(), (i+1)+ "_" + itemNumVo.getOuterId());
    		numMap.put(itemNumVo.getSysItemId() + "_" + itemNumVo.getSysSkuId(), itemNumVo.getNum());
		}
    	params.setSysItemIds(sysItemIds);
    	params.setSysSkuIds(sysSkuIds);
    	Object result = wmsSectionController.queryStock(params, api_name);
    	Map<String, Object> map = new HashMap<>();
    	if (result instanceof ResponseDataWrapper){
    		ResponseDataWrapper responseDataWrapper = (ResponseDataWrapper)result;
    		map = (Map<String, Object>)responseDataWrapper.getData();
        }
    	if (result instanceof ResultBean){
    		ResultBean resultBean = (ResultBean)result;
    		map = (Map<String, Object>)resultBean.getData();
        }
    	Object listObj = map.get("list");
    	if (listObj == null) {
			return result;
		}
    	List<WmsStockVo> stocks = (List<WmsStockVo>)listObj;
        fillSkuInfo(staff, stocks);
        for (WmsStockVo wmsStockVo : stocks) {
        	String  key = wmsStockVo.getSysItemId() + "_" + wmsStockVo.getSysSkuId();
        	outerIdMap.remove(key);
        	Long num = numMap.get(key);
        	if (num == null || num <= 0) {
				continue;
			}
        	if (wmsStockVo.getTotalNum() > num) {
        		wmsStockVo.setNeedNum(num);
        		num -= num;
			} else {
				wmsStockVo.setNeedNum(wmsStockVo.getTotalNum().longValue());
				num -= wmsStockVo.getTotalNum().longValue();
			}
        	numMap.put(key, num);
		}
        map.put("noSectionItems", outerIdMap.values());
        return result;
    }


    private void fillSkuInfo(Staff staff, List<WmsStockVo> stocks) {
		List<Long> sysItemIds = Lists.newArrayList();
        List<Long> sysSkuIds = Lists.newArrayList();
        for (WmsStockVo wmsStockVo : stocks) {
        	sysItemIds.add(wmsStockVo.getSysItemId());
        	sysSkuIds.add(wmsStockVo.getSysSkuId());
		}

        Map<String, WmsItem> itemKeyMap = wmsItemBusiness.queryItemDetailMap(staff, sysItemIds, sysSkuIds);
        for (WmsStockVo stock : stocks) {
            WmsItem wmsItem = itemKeyMap.get(WmsKeyUtils.buildItemKey(stock));
            if (wmsItem != null) {
                stock.setPrice(wmsItem.getPurchasePrice());
                stock.setUnit(wmsItem.getUnit());
                stock.setPicPath(wmsItem.getPicPath());
            }
        }
	}



    /**
     * 一键出库
     * @Title: saveOutDirect
     * @param id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/operate/direct", method = RequestMethod.POST)
    @ResponseBody
    public Object saveOutDirect(Long id, Integer  operateDirectType, String api_name) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        Assert.notNull(id, "请选择调拨单!");
        Staff staff = getLightStaff();
        String cacheKey = Md5Encrypt.md5("allocate_direct_" + staff.getCompanyId() + "_" + id + "_" + operateDirectType);
        Assert.isTrue(wmsCacheBusiness.get(cacheKey) == null, String.format("正在进行%s，请稍等！", Integer.valueOf(0).equals(operateDirectType) ? "调拨一键出库" : "调拨一键收货上架"));
        ImportProcess process = new ImportProcess();
        process.setTotalNum(100);
        process.setComplete(false);
        wmsCacheBusiness.set(cacheKey, process, 10 * 60);
        if (Integer.valueOf(0).equals(operateDirectType)) {
            eventCenter.fireEvent(this, new EventInfo("wms.allocate.save.out.direct").setArgs(new Object[]{staff, cacheKey, id}), null);
        } else if (Integer.valueOf(1).equals(operateDirectType)) {
            eventCenter.fireEvent(this, new EventInfo("wms.allocate.receive.shelve.direct").setArgs(new Object[]{staff, cacheKey, id}), null);
        }
        result.put("cacheKey", cacheKey);
        result.put("isSuccess", true);
        return result;
    }


    /**
     * 查看一键出库进度
     * @throws CacheException
     */
    @RequestMapping(value = "/operate/direct/status", method = RequestMethod.GET)
    @ResponseBody
    public Object getImportStatus(String cacheKey, String api_name) throws CacheException {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        } else {
            //虚拟进度
            if (process.getCurrNum() < 50) {
                process.setCurrNum(process.getCurrNum() + 10);
                wmsCacheBusiness.set(cacheKey, process, 10 * 60);
            }
        }
        return process;
    }




}

package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.ERPLock;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.stock.StockInOutRecordType;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.WorkingStorageSection.TypeEnum;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.vo.GoodsInOutRecordDetailVo;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.constants.ItemTraceConstants;
import com.raycloud.dmj.services.constants.ItemTraceConstants;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.ec.WmsSectionListener;
import com.raycloud.dmj.services.trades.IColumnConfService;
import com.raycloud.dmj.services.wms.IGoodsSectionInOutRecordService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IProgressService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.storagesection.IWorkingStorageSectionManageService;
import com.raycloud.dmj.services.wms2.IWmsStockService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.ERPLockUtils;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.WmsOpLogUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.*;
import com.raycloud.dmj.web.source.OperateSourceContext;
import com.raycloud.dmj.web.source.OperateSourceHolder;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 18/12/20
 */
@RequestMapping("/wms/section")
@Controller
public class WmsSectionController extends WmsInitController {

    @Resource
    private IWmsStockService wmsStockService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;
    
    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Resource
    private IWorkingStorageSectionManageService workingStorageSectionManageService;

    @Resource
    private IColumnConfService columnConfService;

    @Resource
    private IWmsService wmsService;

    @Resource
    private IGoodsSectionInOutRecordService goodsSectionInOutRecordService;

    @Autowired
    private IProgressService progressService;

    @Resource
    private WmsSectionListener wmsSectionListener;

    @Resource
    private IFileUploadService fileUploadService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private WmsItemBusiness wmsItemBusiness;
    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "/query/stock", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object queryStock(@RequestBody WmsStockQueryParams params, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if(!WmsUtils.isSectionCodeStoreDefault(staff) && StringUtils.isNotEmpty(params.getGoodsSectionCode()) && params.getGoodsSectionCode().endsWith("-")) {
            params.setCustomSectionCode(true);
        }
        validateParams(staff, params);
        Page page= params.getPage();
        Map<String, Object> map = new HashMap<String, Object>();
        //仓内库存默认排序更改,不改sql防止影响其他业务
        if (StringUtils.isEmpty(params.getOrderType())) {
            params.setOrderType("goods_section_id");
            params.setOrder("DESC");
        }
        if (StringUtils.isNotEmpty(params.getGroupBy())) {
            PageList<WmsStockVo> pageList = wmsStockService.query4Inventory(staff, params, page);;
            formatGoodsSectionCode(staff, pageList.getList());
            map.put("total", pageList.getTotal());
            map.put("list", pageList.getList());
            map.put("allTotalNum", getAllTotalNum(pageList.getList()));
        } else {
            Long count = wmsStockService.queryCount(staff, params);
            List<WmsStockVo> stocks = count > 0 ? wmsStockService.query(staff, params, page) : new ArrayList<>();
            formatGoodsSectionCode(staff, stocks);
            map.put("total", count);
            map.put("list", stocks);
            map.put("allTotalNum", getAllTotalNum(stocks));
        }
        map.put("page", page);
        return map;
    }

    @RequestMapping(value = "/query/stock/change/detail", method = {RequestMethod.GET})
    @ResponseBody
    public Object queryStockChangeDetail(String outerId, String skuOuterId, String goodsSectionCode, Integer containerType, Integer pageNo, Integer pageSize, String operateBeginStr, String operateEndStr, Long warehouseId, Integer type, String busiType, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.isTrue(StringUtils.isNotEmpty(goodsSectionCode) && null != containerType,"请传递货位和容器类型");
        Assert.isTrue(StringUtils.isNotEmpty(outerId) || StringUtils.isNotEmpty(skuOuterId),"请传递商家编码");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date begin = null;
        Date end = null;
        if(StringUtils.isNotEmpty(operateBeginStr)) {
            begin = dateFormat.parse(operateBeginStr + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(operateEndStr)) {
            end = dateFormat.parse(operateEndStr + " 23:59:59");
        }

        PageList<GoodsInOutRecordDetailVo> result = new PageList();
        //货位
        if(containerType == 0) {
            GoodsSectionInOutRecordQueryParams gsParams = new GoodsSectionInOutRecordQueryParams();
            gsParams.setGoodsSectionCode(WmsUtils.decodeGsCodeSearch(staff, goodsSectionCode));
            if(StringUtils.isNotEmpty(outerId)) {
                gsParams.setOuterId(outerId);
                gsParams.setExactOuterId(true);
            }
            if(StringUtils.isNotEmpty(skuOuterId)) {
                gsParams.setSkuOuterId(skuOuterId);
                gsParams.setExactSkuOuterId(true);
            }
            gsParams.setOperateBegin(begin);
            gsParams.setOperateEnd(end);
            gsParams.setPage(new Page(pageNo,pageSize));
            gsParams.setWarehouseId(warehouseId);
            gsParams.setType(type);
            WmsPageList<GoodsSectionInOutRecordVo> pageList = goodsSectionInOutRecordService.queryInOutRecordPage(staff, gsParams,true);
            wmsHelpBusiness.formatGoodsSectionCode(staff, pageList.getList(), true);
            result.setPage(pageList.getPage());
            result.setSort(pageList.getSort());
            result.setTotal(pageList.getTotal());
            result.setList(getGoodsInOutRecordDetailVo(pageList.getList(),null));
        //暂存区
        } else {
            QueryInOutRecordParams params = new QueryInOutRecordParams();
            if(StringUtils.isNotEmpty(outerId)) {
                params.setOuterId(outerId);
                params.setExactOuterId(true);
            }
            if(StringUtils.isNotEmpty(skuOuterId)) {
                params.setSkuOuterId(skuOuterId);
                params.setExactSkuOuterId(true);
            }
            params.setOperateBegin(begin);
            params.setOperateEnd(end);
            params.setPage(new Page(pageNo,pageSize));
            params.setWarehouseId(warehouseId);
            params.setSectionTypeList(Lists.newArrayList(goodsSectionCode));
            if (StringUtils.isNotBlank(busiType)) {
                params.setBusiTypeList(Lists.newArrayList(busiType));
            }
            WmsPageList<WorkingStorageSectionInOutRecord> pageList = workingStorageSectionManageService.queryInOutRecordPage(staff, params,true);
            result.setPage(pageList.getPage());
            result.setSort(pageList.getSort());
            result.setTotal(pageList.getTotal());
            result.setList(getGoodsInOutRecordDetailVo(null,pageList.getList()));
        }
        return result;
    }

    private List<GoodsInOutRecordDetailVo> getGoodsInOutRecordDetailVo(List<GoodsSectionInOutRecordVo> gsList, List<WorkingStorageSectionInOutRecord> wssList) {
        List<GoodsInOutRecordDetailVo> voList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(gsList)) {
            for(GoodsSectionInOutRecordVo vo : gsList) {
                GoodsInOutRecordDetailVo detailVo = new GoodsInOutRecordDetailVo();
                detailVo.setBusiName(vo.getTypeName());
                detailVo.setOperateTime(vo.getOperateTime());
                detailVo.setOrderNumber(vo.getOrderNumber());
                detailVo.setStockChange(vo.getStockChange());
                detailVo.setQualityType(vo.getQualityType());
                detailVo.setGoodsSectionCode(vo.getGoodsSectionCode());
                detailVo.setStaffName(vo.getStaffName());
                detailVo.setWarehouseName(vo.getWarehouseName());
                detailVo.setContent(vo.getContent());
                voList.add(detailVo);
            }
        }
        if(CollectionUtils.isNotEmpty(wssList)) {
            for(WorkingStorageSectionInOutRecord vo : wssList) {
                GoodsInOutRecordDetailVo detailVo = new GoodsInOutRecordDetailVo();
                detailVo.setBusiName(vo.getBusiName());
                detailVo.setOperateTime(vo.getCreated());
                detailVo.setOrderNumber(vo.getWorkOrderId());
                detailVo.setStockChange(Long.parseLong(String.valueOf(vo.getIncNum())));
                detailVo.setQualityType(vo.getQualityType());
                detailVo.setGoodsSectionCode(vo.getSectionName());
                detailVo.setStaffName(vo.getOperator());
                detailVo.setWarehouseName(vo.getWarehouseName());
                String content = StringUtils.defaultIfBlank(vo.getContent(), "");
                content += appendWaveId(vo);
                detailVo.setContent(content);
                voList.add(detailVo);
            }
        }
        return voList;
    }
    private String appendWaveId(WorkingStorageSectionInOutRecord vo){
        if (Objects.equals(-4L,vo.getWaveId()) || Objects.equals(-5L,vo.getWaveId()) || Objects.equals(-6L,vo.getWaveId())) {
            return "  ," + vo.getWaveId() + "盲扫；";
        }
        return "";
    }

    /**
     * 暂存区库存调整,单个调整,批量调整
     * singleChange:true/false 单个累加/批量修改
     */
    @RequestMapping(value = "/stock/change", method = {RequestMethod.POST})
    @ResponseBody
    public Object stockChange(String api_name, @RequestBody WmsStockChangeParams[] list, Boolean singleChange)throws SessionException  {
        Staff staff = getLightStaff();
        Assert.notEmpty(list, "请选择要修改的数据");
        wmsStockService.stockChange(staff, Lists.newArrayList(list));
        return successResponse();
    }

    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "/export", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object export(@RequestBody WmsStockQueryParams params, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");

        validateParams(staff, params);

        String fileName = new String("仓内库存导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("仓内库存数据");
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("wms.section.stock.download.excel").setArgs(new Object[]{staff, param, params}), false);

        return successResponse();
    }

    /**
     * @param sectionType {@linkplain WorkingStorageSection.TypeEnum}
     */
    @RequestMapping(value = "/clear/storage", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object clearStorage(Long warehouseId, String sectionType, String idStr, Integer totalNumType) throws SessionException {
        Assert.notNull(warehouseId, "请选择仓库！");
        Assert.notNull(sectionType, "请选择暂存区类型！");
        Assert.notNull(idStr, "请选择需要清零的记录！");
        Staff staff = getLightStaff();
        WmsStockQueryParams params = new WmsStockQueryParams();
        params.setIds(ArrayUtils.toLongList(idStr));
        params.setWarehouseId(warehouseId);
        if ("WSS_ALL".equals(sectionType)) {
            TypeEnum[] values = WorkingStorageSection.TypeEnum.values();
            for (TypeEnum typeEnum : values) {
                params.addTypeEnums(typeEnum);
            }
        } else {
            params.setTypeEnum(WorkingStorageSection.TypeEnum.valueOf(sectionType.toUpperCase()));
        }
        if (Integer.valueOf(0).equals(params.getTotalNumType())) {
            params.setTotalNumMax(0L);
        }
        List<WmsStockVo> wmsStockList = wmsStockService.query(staff, params, null);
        if (CollectionUtils.isNotEmpty(wmsStockList)) {
            wmsStockService.releaseItemSku(staff,wmsStockList,warehouseId,sectionType);
            if(CollectionUtils.isEmpty(wmsStockList)){
                return successResponse();
            }
            Map<String, List<WmsStockVo>> sectionTypeMap = wmsStockList.stream().collect(Collectors.groupingBy(WmsStockVo :: getSectionType));
            for (Entry<String, List<WmsStockVo>>  entry : sectionTypeMap.entrySet()) {
                wmsStockService.clearWorkingStorageSection(staff, warehouseId, WorkingStorageSection.TypeEnum.valueOf(entry.getKey().toUpperCase()), entry.getValue());
            }
        }
        return successResponse();
    }

    /**
     * 根据查询条件清空暂存区
     * @param params 查询条件
     * @param api_name
     * @param sectionType 清空暂存区类型
     * @return
     */
    @RequestMapping(value = "/clear/storage/condition", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object clearStorageBySearchCondition(@RequestBody WmsStockQueryParams params, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        validateParams(staff, params);
        JSONObject result = new JSONObject();
        Long delNum = 0L;
        logger.debug("1.清空暂存区，StockType = " + params.getStockType());
        ProgressData progressData = new ProgressData();
        progressData.setCacheKey(ProgressEnum.PROGRESS_SECTION_CLEAR_STORAGE_CONDITION.getKey());
        ProgressData progressDataExists = progressService.queryProgress(staff, ProgressEnum.PROGRESS_SECTION_CLEAR_STORAGE_CONDITION);
        if(progressDataExists !=null&&!Objects.equals(progressDataExists.getProgress(),2)){
            throw new IllegalArgumentException("正在进行清空暂存区，请稍候再试！");
        }
        Integer stockType = params.getStockType();
        if (params.getStockType() == 0 || wmsSectionListener.validateDeleteParams(params, params.getSectionType())) {
            if ("WSS_ALL".equals(params.getSectionType())) {
                params.setStockType(null);
            } else {
                params.setTypeEnum(WorkingStorageSection.TypeEnum.valueOf(params.getSectionType().toUpperCase()));
            }
            if (Integer.valueOf(0).equals(params.getTotalNumType()) && (params.getTotalNumMax() == null || params.getTotalNumMax() > 0)) {
                params.setTotalNumMax(0L);
            }
        }
        Long totalNum = wmsStockService.queryCount(staff, params);
        params.setStockType(stockType);
        progressData.setProgress(1);
        progressData.setCountAll(Optional.ofNullable(totalNum).orElse(0L).intValue());
        progressService.setProgress(staff, ProgressEnum.PROGRESS_SECTION_CLEAR_STORAGE_CONDITION, progressData);
        eventCenter.fireEvent(this, new EventInfo("wms.section.clear.storage.condition").setArgs(new Object[]{staff, params,delNum,totalNum}), false);
        result.put("status", "success");
        result.put("delNum", delNum);
        return result;
    }

    /**
     * 平移暂存区库存
     */
    @RequestMapping(value = "/move", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object move(Long warehouseId, String fromSectionType, String toSectionType, String idStr, String transferNumStr, String api_name) throws SessionException {
        Assert.notNull(warehouseId, "请选择仓库！");
        Assert.notNull(fromSectionType, "请选择要移出的暂存区！");
        Assert.notNull(toSectionType, "请选择要移入的暂存区！");
        Assert.notNull(idStr, "请选择需要移动的商品信息的记录！");
        Assert.isTrue(!fromSectionType.equalsIgnoreCase(toSectionType), "移入和移出的暂存区类型不能一样！");
        List<Integer> transferNumList = ArrayUtils.toIntegerListPosition(transferNumStr);
        wmsStockService.moveWorkingStorageSection(getLightStaff(), warehouseId, WorkingStorageSection.TypeEnum.valueOf(fromSectionType.toUpperCase()), WorkingStorageSection.TypeEnum.valueOf(toSectionType.toUpperCase()), ArrayUtils.toLongList(idStr), transferNumList, null);
        return successResponse();
    }

    /**
     * 一键平移暂存区库存
     */
    @RequestMapping(value = "/move/all", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object moveAll(@RequestBody WmsStockQueryParams params) throws SessionException {
        Assert.notNull(params.getToSectionType(),"移动到货位类型不能为空");
        JSONObject jsonObject = new JSONObject();
        Staff staff = getLightStaff();
        List<WorkingStorageSection.TypeEnum> typeEnumList = getTypeEnum(params, staff);
        ProgressData progressData = new ProgressData();
        progressData.setCacheKey(ProgressEnum.PROGRESS_BATCH_MOVE_STOCK.getKey());
        ProgressData progressDataExists = progressService.queryProgress(staff, ProgressEnum.PROGRESS_BATCH_MOVE_STOCK);
        if(progressDataExists !=null&&!Objects.equals(progressDataExists.getProgress(),2)){
            throw new IllegalArgumentException("正在平移库存，请稍候再试！");
        }
        RequestAttributes requestAttributes = WmsOpLogUtils.getRequestAttributesSafely();
        if (requestAttributes != null) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            params.setModulePath(request.getHeader(ItemTraceConstants.MODULE_PATH));
        }
        progressData.setProgress(1);
        progressData.setCountAll(100);
        progressService.setProgress(staff, ProgressEnum.PROGRESS_BATCH_MOVE_STOCK, progressData);
        OperateSourceContext.initItemTraceInfo(ItemTraceActionEnum.WMS_WSS_STOCK_MOVE,String.format("平移前：%s，平移后：%s", params.getStockTypeName(), WorkingStorageSection.TypeEnum.valueOf(params.getToSectionType().toUpperCase()).getName()));
        OperateSourceHolder operateSource = OperateSourceContext.getOperateSourceNew();
        eventCenter.fireEvent(this, new EventInfo("wms.section.move.all").setArgs(new Object[]{staff, params,typeEnumList.get(0).getCode()}), operateSource);
        jsonObject.put("status", "success");
        return jsonObject;
    }

    private List<TypeEnum> getTypeEnum(WmsStockQueryParams params, Staff staff) {
        validateParams(staff, params);
        List<TypeEnum> typeEnumList = params.getTypeEnums();
        Assert.isTrue(typeEnumList.size()==1, "该功能不支持暂存区类型以外的类型！");
        Assert.isTrue(!typeEnumList.get(0).getCode().equalsIgnoreCase(params.getToSectionType()), "移入和移出的暂存区类型不能一样！");
        Assert.notNull(params.getToSectionType(), "请选择要移入的暂存区！");
        return typeEnumList;
    }

    /**
     * 明细信息
     */
    @RequestMapping(value = "/stock/detail", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object stockDetail(Long id, String api_name) throws SessionException {
        //暂存区数量明细
        Map<String, Object> map = new HashMap<String, Object>();
        List<WorkingStorageSectionGoodsRelatedWorkOrder> list = workingStorageSectionManageService.queryRelatedWorkOrderList(getLightStaff(), id);
//        List<WmsStockDetailVo> details = ;
        map.put("list", list);
        return map;
    }

    private void validateParams(Staff staff, WmsStockQueryParams params) {
        Assert.notNull(params, "请输入查询参数！");
        Assert.notNull(params.getWarehouseId(), "请输入仓库！");
        Integer stockType = params.getStockType();
        Assert.notNull(stockType, "请输入库存类型！");
        //库存类型：0：全部、1：仓库库存、2：货位库存、3：拣货暂存区、4：通用暂存区、5：入库暂存区、6：销退暂存区、7：补货暂存区、8:次品暂存区
        if (stockType == 1) {//库库存=货位库存(良次品之和)+拣选暂存区+通用暂存区+补货暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PICK);//拣选暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.COMMON);//通用暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REPLENISH);//补货暂存区
            params.setStockTypeName("仓库库存");
        } else if (stockType == 3) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PICK);//拣选暂存区
            params.setStockTypeName("拣选暂存区");
        } else if (stockType == 4) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.COMMON);//通用暂存区
            params.setStockTypeName("通用暂存区");
        } else if (stockType == 5) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PURCHASE);//入库暂存区(采购入库暂存区)
            params.setStockTypeName("入库暂存区");
        } else if (stockType == 6) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REFUND);//销退暂存区(销退入库暂存区)
            params.setStockTypeName("销退暂存区");
        } else if (stockType == 7) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REPLENISH);//补货暂存区
            params.setStockTypeName("补货暂存区");
        } else if(stockType == 8) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.DEFECTIVE);//次品暂存区
            params.setStockTypeName("次品暂存区");
        } else if(stockType == 10) {
            params.setTypeEnums(Lists.newArrayList(WorkingStorageSection.TypeEnum.values()));
            params.setStockTypeName("全部");
        } else if(stockType == 0) {
            params.setTypeEnums(Lists.newArrayList(WorkingStorageSection.TypeEnum.values()));
            params.setStockTypeName("全部");
        } else if(stockType == 11) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.RETURN);//采退暂存区
            params.setStockTypeName("采退暂存区");
        }

        //货位编码
        String goodsSectionCode = params.getGoodsSectionCode();
        if (StringUtils.isNotEmpty(goodsSectionCode)) {
            WorkingStorageSection.TypeEnum typeEnum = goodsSectionCode2SectionType(goodsSectionCode);
            if (typeEnum == null) {
                params.setGoodsSectionCode(WmsUtils.decodeGsCodeSearch(staff, goodsSectionCode));
            } else {
                params.setGoodsSectionCode(null);
                params.setTypeEnum(typeEnum);
            }
        }
        String idStr = params.getIdStr();
        if (StringUtils.isNotEmpty(idStr)) {
            params.setIds(ArrayUtils.toLongList(idStr));
        }
        if (StringUtils.isNotEmpty(params.getMultiCode()) || ("barcode".equals(params.getQueryKey()) && StringUtils.isNotEmpty(params.getQueryText()))) {
            String multiCode = StringUtils.isNotEmpty(params.getMultiCode()) ? params.getMultiCode() : params.getQueryText();
            List<Pair<Long, Long>> itemIds = wmsService.queryItemIdsByMultiCodes(staff, Lists.newArrayList(multiCode));
            if (CollectionUtils.isNotEmpty(itemIds)) {
                params.setSysItemIds(itemIds.stream().map(Pair :: getLeft).collect(Collectors.toList()));
                params.setSysSkuIds(itemIds.stream().map(Pair :: getRight).collect(Collectors.toList()));
            } else {
                params.setSysItemIds(Lists.newArrayList(-1L));
            }
        }

        //根据排数封装参数
        if(StringUtils.isNotEmpty(params.getBeginRow()) && StringUtils.isNotEmpty(params.getEndRow())) {
            String beginRow = params.getBeginRow();
            String endRow = params.getEndRow();
            //参数类型不对
            Assert.isTrue(!((Character.isDigit(beginRow.charAt(0)) && !Character.isDigit(endRow.charAt(0))) || (!Character.isDigit(beginRow.charAt(0)) && Character.isDigit(endRow.charAt(0)))),"排数参数类型请保持一致,不能同时包含字母和数字");
            //数字,可以通过goods_section表中shelf_position联查
            if(Character.isDigit(beginRow.charAt(0))) {
                logger.debug(LogHelper.buildLog(staff, String.format("查询详情排数数字:%s", params.getBeginRow()+"_"+params.getEndRow())));
                //字母,只能范围查询,查出货位,货位带进去
            }else {
                int begin = beginRow.charAt(0);
                int end = endRow.charAt(0);
                List<String> shelfPositions = new ArrayList<>();
                for(int i = begin;i <= end;i++) {
                    shelfPositions.add(String.valueOf((char)i));
                }
                params.setShelfPositions(shelfPositions);
            }
        }else {
            params.setBeginRow(null);
            params.setEndRow(null);
        }

    }

    private WorkingStorageSection.TypeEnum goodsSectionCode2SectionType(String goodsSectionCode) {
        for (WorkingStorageSection.TypeEnum typeEnum : WorkingStorageSection.TypeEnum.values()) {
            if (typeEnum.getName().equalsIgnoreCase(goodsSectionCode)) {
                return typeEnum;
            }
        }
        return null;
    }

    private int getAllTotalNum(List<WmsStockVo> stocks) {
        int allTotalNum = 0;
        if (stocks != null && stocks.size() > 0) {
            for (WmsStockVo stock : stocks) {
                allTotalNum += stock.getTotalNum();
            }
        }
        return allTotalNum;
    }

    /**
     * 获取推荐货位
     * @param goodsSectionSkuVos
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/recommend/auto", method = RequestMethod.POST)
    @ResponseBody
    public Object autoRecommend(@RequestBody List<GoodsSectionSkuVo> goodsSectionSkuVos, String api_name) throws SessionException {
        Assert.notEmpty(goodsSectionSkuVos, "商品明细不能为空！");
        return workingStorageSectionManageService.autoRecommend(getLightStaff(), goodsSectionSkuVos);
    }

    /**
     * 暂存区批量上架
     * @param goodsSectionSkuParams
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/batch/upshelf", method = RequestMethod.POST)
    @ResponseBody
    public Object workingStorageUpBatchUpshelf(@RequestBody GoodsSectionSkuParams goodsSectionSkuParams, String api_name) throws SessionException {
        Assert.notNull(goodsSectionSkuParams, "暂存区上架参数不能为空！");
        Assert.notEmpty(goodsSectionSkuParams.getGoodsSectionSkuVos(), "商品明细不能为空！");
        Assert.notNull(goodsSectionSkuParams.getSectionType(), "暂存区类型不能为空！");
        workingStorageSectionManageService.workingStorageUpBatchUpshelf(getLightStaff(), goodsSectionSkuParams);
        return successResponse();
    }

    /**
     * excel导入所需平移商品和平移库存
     * 导入的商品可以是不同暂存区，但是移入暂存区一致
     * @param warehouseId   仓库Id
     * @param toSectionType  移入暂存区类型
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/excel/move", method = RequestMethod.POST)
    @ResponseBody
    public Object excelMove(MultipartFile file, Long warehouseId, String toSectionType, String api_name) throws Exception {
        Map<String, Object> resultMap = new HashMap<>(2);
        Staff staff = getLightStaff();
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.WMS_SECTION_GOODS_MOVE_CACHE);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("导入商品暂存区平移不能同时导入，请稍等！");
        }
        Assert.notNull(file, "请导入excel文件！");
        Assert.notNull(warehouseId, "请选择仓库！");
        Assert.notNull(toSectionType, "请选择要移入的暂存区！");
        String[][] dataArr = convertExcelData(file, 2);
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix)) {
            resultMap.put("errorMessage", "文件格式只支持EXL表格");
            return resultMap;
        }
        if (file.getSize() > 10 * 512 * 1024) {
            resultMap.put("errorMessage", "传入的文件不能大于5M");
            return resultMap;
        }
        //采用传入文件URL的方式（减轻传输文件内容数据过大导致事件处理过慢）
        FileResult fileResult = fileUploadService.uploadToTemp(filename, dataArr);
        eventCenter.fireEvent(this, new EventInfo("wms.goods.section.move.import").setArgs(new Object[]{staff, warehouseId, toSectionType, fileResult.getUrl()}),false);

        process = new ImportProcess();
        process.setTotalNum(dataArr.length);
        process.setComplete(false);
        cache.set(importKey, process,10 * 60);
        resultMap.put("cacheKey", importKey);
        resultMap.put("isSuccess", true);
        return resultMap;
    }

    /**
     * 查询excel导入商品平移暂存区状态
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/excel/import/status", method = RequestMethod.POST)
    @ResponseBody
    public Object moveStatus(String cacheKey, String api_name){
            ImportProcess process = wmsCacheBusiness.get(cacheKey);
            Assert.notNull(process, "请先导入暂存区商品平移excel！");
            if (process.isComplete()) {
                wmsCacheBusiness.delete(process.getCacheKey());
            }
            return process;
        }
}

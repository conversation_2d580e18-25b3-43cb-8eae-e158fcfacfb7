package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.ReplenishTask;
import com.raycloud.dmj.domain.wms.ReplenishTaskDetail;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.WmsItem;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.params.ReplenishParams;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.wms.IReplenishTaskService;
import com.raycloud.dmj.services.wms.IWmsConfigService;
import com.raycloud.dmj.services.wms.allocategoods.IAllocateGoodsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 补货任务控制器
 * <AUTHOR>
 * @date 2018/5/23 19:06
 */
@RequestMapping("/wms/replenish")
@Controller
@LogTag(value = "wms")
public class ReplenishTaskController extends WmsInitController {
    @Resource
    private IReplenishTaskService replenishTaskService;

    @Resource
    private WmsItemBusiness wmsItemBusiness;

    @Resource
    private IAllocateGoodsService allocateGoodsService;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private IWmsConfigService wmsConfigService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    /**
     * 补货任务列表
     * @param params
     * @param page
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        return replenishTaskService.queryTaskPageList(getLightStaff(), params, page);
    }


    /**
     * 查出补货商品列表
     *
     * @param params
     * @param page
     * @return
     * @throws SessionException
     */
    @RequestMapping("/list/items")
    @ResponseBody
    public Object listItems(ReplenishParams params, Page page, String api_name) throws SessionException {
        Assert.notNull(params, "参数不能为空！");
        Assert.notNull(params.getWarehouseId(), "请选择仓库！");
        Assert.notNull(params.getPlan(), "请选择方案！");
        Staff staff = getLightStaff();

        return replenishTaskService.getReplenishTaskDetailPageList(params, page, staff);
    }



    private void fillItemInfo(Staff staff, List<ReplenishTaskDetail> details, Long warehouseId) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        Map<String, WmsItem> keyWmsItemMap = wmsItemBusiness.queryItemDetailMap(staff, details);
        for (ReplenishTaskDetail detail : details) {
            WmsItem wmsItem = keyWmsItemMap.get(detail.getSysItemId() + "_" + detail.getSysSkuId());
            if (wmsItem != null) {
                detail.setTitle(wmsItem.getTitle());
                detail.setShortTitle(wmsItem.getShortTitle());
                detail.setOuterId(wmsItem.getOuterId());
                detail.setItemOuterId(wmsItem.getItemOuterId());
                detail.setPicPath(wmsItem.getPicPath());
                detail.setRemark(wmsItem.getRemark());
                detail.setPropertiesAlias(wmsItem.getPropertiesAlias());
                detail.setPropertiesName(wmsItem.getPropertiesName());
            }
        }
    }

    /**
     * 保存、修改补货任务
     * @param task
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public Object save(@RequestBody ReplenishTask task, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (WmsUtils.isNewWms(staff)) {
            List<Wave> waves = replenishTaskService.saveReplenishWavesByStockRegion(staff, task);
            Assert.isTrue(CollectionUtils.isNotEmpty(waves), "生成补货波次失败，商品可配数不足！");
            return waves;
        }

        boolean isAdd = task.getId() == null;
        //判断备货区可补数
        if (BooleanUtils.isNotTrue(task.getForce())) {
            List<ReplenishTaskDetail> details = replenishTaskService.checkUnAvailDetails(staff, task);
            if (CollectionUtils.isNotEmpty(details)) {
                Map<String, Object> result = Maps.newHashMap();
                result.put("errorCode", "un_available");
                result.put("list", details);
                return result;
            }
        }
        ReplenishTask result = replenishTaskService.save(staff, task);
        writeOpLog(staff, Domain.WMS, "saveReplenishTask", String.valueOf(result.getId()), String.format("%s补货任务，库区：%s，共%s个商品明细", isAdd ? "新增" : "修改", task.getStockRegionZoneName(), task.getDetails().size()), null, 0);
        return result;
    }

    /**
     * 自动补货任务调试接口j
     * @param taskType
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/saveReplenishWaves/{taskType}", method = RequestMethod.POST)
    @ResponseBody
    public Object saveReplenishWaves(@PathVariable String taskType, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        List<Wave> waves = replenishTaskService.saveReplenishWaves(staff, taskType);
        logger.debug("自动补货调试接口 waves: " + JSON.toJSON(waves));
        return waves;
    }


    /**
     * 取消补货任务
     * @param task
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#task.id", content = "'取消补货任务，编号：' + #task.code")
    public Object cancel(ReplenishTask task, String api_name) throws SessionException {
        Assert.notNull(task.getId(), "请选择补货任务！");
        Assert.notNull(task.getCode(), "请选择补货任务！");

        replenishTaskService.cancel(getLightStaff(), task);
        return successResponse();
    }

    /**
     * 批量完成补货任务
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/finish", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#taskIds", content = "'完成补货任务，补货ids：' + #taskIds")
    public Object finish(String taskIds, String api_name) throws SessionException {
        Assert.notNull(taskIds, "请选择补货任务！");
        replenishTaskService.finish(getLightStaff(), taskIds);
        return successResponse();
    }


    /**
     * 根据id获取补货任务明细
     * @param id
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/detail")
    @ResponseBody
    public Object queryById(Long id, Boolean edit, String api_name) throws SessionException {
        Assert.notNull(id, "请选择补货任务！");

        Staff staff = getLightStaff();
        ReplenishTask task = replenishTaskService.queryTaskFullInfo(staff, id, edit);
        if (CollectionUtils.isNotEmpty(task.getDetails())) {
            fillItemInfo(staff, task.getDetails(),null);
        }
        return task;
    }


    /**
     * 开始补货拣选
     * @param task
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/begin/picking", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#task.id", content = "'开始补货拣选，编号：' + #task.code")
    public Object beginPicking(@RequestBody ReplenishTask task, String api_name) throws SessionException {
        Assert.notNull(task.getId(), "请选择补货任务！");

        return replenishTaskService.beginReplenishPicking(getLightStaff(), task);
    }


    /**
     * 取消补货拣选
     * @param task
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/update/status", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#task.id", content = "'取消补货拣选，编号：' + #task.code")
    public Object cancelPicking(ReplenishTask task, String api_name) throws SessionException {
        Assert.notNull(task.getId(), "请选择补货任务！");

        replenishTaskService.updateReplenishStatus(getLightStaff(), task);
        return successResponse();
    }

    /**
     * 补货任务商品导出
     *
     * @param replenishParams
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/items/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportItems(ReplenishParams replenishParams) throws SessionException {
        Staff staff = getLightStaff();
        // 导出前校验
        checkModuleHasExport(staff, EnumDownloadCenterModule.WMS);
        // 发送事件
        eventCenter.fireEvent(this, new EventInfo("wms.replenish.items.export").setArgs(new Object[]{staff, replenishParams}), false);
        return successResponse();
    }

    /**
     * 补货任务商品导出
     *
     * @param replenishParams
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/items/gs/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportItemGs(ReplenishParams replenishParams) throws SessionException {
        Staff staff = getLightStaff();
        // 导出前校验
        checkModuleHasExport(staff, EnumDownloadCenterModule.WMS);
        replenishParams.setGsExport(true);
        // 发送事件
        eventCenter.fireEvent(this, new EventInfo("wms.replenish.items.export").setArgs(new Object[]{staff, replenishParams, true}), false);
        return successResponse();
    }

    /**
     * 补货任务商品导入
     *
     * @param file
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = {"/items/import"}, method = RequestMethod.POST)
    @ResponseBody
    public Object importExcel(MultipartFile file, Long warehouseId) throws SessionException {
        // 导入前校验
        checkBeforeImport(file, -1);
        // 查询上一次进度
        Staff staff = getLightStaff();
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.REPLENISH_TASK_ITEMS_IMPORT);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null) {
            throw new IllegalArgumentException("上一次补货任务商品导入还未执行完，请稍后重试!");
        }
        // 转成数组
        String[][] data = convertExcelData(file, 1);
        if (data.length > 2000) {
            throw new IllegalArgumentException("每次最多只能导入2000条数据!");
        }
        // 发送事件
        eventCenter.fireEvent(this, new EventInfo("wms.replenish.items.import").setArgs(new Object[]{staff, warehouseId}), data);
        Map<String, Object> result = Maps.newHashMap();
        result.put("cacheKey", importKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 补货任务商品导入进度
     *
     * @param cacheKey
     * @return
     * @throws CacheException
     */
    @RequestMapping(value = {"/items/import/status"})
    @ResponseBody
    public Object queryImportStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }
}

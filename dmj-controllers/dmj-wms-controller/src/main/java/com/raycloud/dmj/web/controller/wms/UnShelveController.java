package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.model.WaveQueryFilterParam;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.UnShelveOrder;
import com.raycloud.dmj.domain.wms.UnShelveOrderDetail;
import com.raycloud.dmj.domain.wms.WaveCreateResult;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.IUnShelveService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: lit
 * @Date: 2021/4/14 3:06 下午
 */
@Controller
@RequestMapping("/wms/unShelve/")
@LogTag(value = "wms")
public class UnShelveController extends WmsInitController {


    @Resource
    private IUnShelveService unShelveService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IFileUploadService fileUploadService;

    @Resource
    private IDownloadCenterService downloadCenterService;


    /**
     * 下架单保存
     *
     * @param unShelveOrder 下架单信息
     * @return 单据ID
     * @throws SessionException
     */
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public Object save(UnShelveOrder unShelveOrder) throws SessionException {
        Staff staff = getStaff();
        return unShelveService.save(staff, unShelveOrder);
    }

    /**
     * 下架单明细，批量保存
     *
     * @param unShelveOrderDetails 下架单明细列表
     * @return true/false 成功/失败
     * @throws SessionException
     */
    @RequestMapping(value = "batchSaveDetail", method = RequestMethod.POST)
    @ResponseBody
    public Object batchSaveDetail(@RequestBody List<UnShelveOrderDetail> unShelveOrderDetails) throws SessionException {
        Staff staff = getStaff();
        unShelveService.batchSaveDetail(unShelveOrderDetails, staff,null);
        return true;
    }

    /**
     * 批量覆盖申请数量
     * @param unShelveOrder
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "batchUpdateApplyNum", method = RequestMethod.POST)
    @ResponseBody
    public Object batchUpdateApplyNum(@RequestBody UnShelveOrder unShelveOrder) throws SessionException {
        Staff staff = getStaff();
        unShelveService.batchUpdateApplyNum(staff,unShelveOrder);
        return true;
    }

    /**
     * 批量打回草稿
     *
     * @param idsStr   idsStr
     * @param codesStr codeStr
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/unaudit/batch", method = RequestMethod.POST)
    @ResponseBody
    @AccessShield(value = "103306",extendsType = false)
    @LogTag(key = "#idsStr", content = "'批量打回草稿：' + #codesStr")
    public Object batchUnAudit(String idsStr, String api_name) throws SessionException {
        Assert.hasText(idsStr, "请先选择需要操作的单据！");
        List<Long> ids = Arrays.stream(idsStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        Staff staff = getLightStaff();
        return unShelveService.batchUnAudit(staff, ids);
    }

    /**
     * 下架单批量审核
     *
     * @param ids
     * @return true/false 成功/失败
     * @throws SessionException
     */
    @RequestMapping(value = "batchAudit", method = RequestMethod.POST)
    @ResponseBody
    public Object batchAudit(String ids,Boolean force) throws SessionException {
        Staff staff = getStaff();
        return unShelveService.batchAudit(staff, ArrayUtils.toLongList(ids),force);
    }

    /**
     * 下架单批量作废
     *
     * @param ids
     * @return true/false 成功/失败
     * @throws SessionException
     */
    @RequestMapping(value = "batchCancel", method = RequestMethod.POST)
    @ResponseBody
    public Object batchCancel(@RequestBody List<Long> ids) throws SessionException {
        Staff staff = getStaff();
        return unShelveService.batchCancel(staff, ids);
    }

    /**
     * 下架单明细批量删除
     *
     * @param ids 明细ID列表
     * @return true/false 成功/失败
     * @throws SessionException
     */
    @RequestMapping(value = "item/delete", method = RequestMethod.GET)
    @ResponseBody
    public Object deleteDetail(@RequestParam(value = "unShelveId") Long unShelveId, @RequestParam(value = "ids") String ids) throws SessionException {
        Staff staff = getStaff();
        List<Long> detailIds = Arrays.stream(ids.split(","))
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        unShelveService.deleteDetail(staff, unShelveId, detailIds);
        unShelveService.batchUpdateNum(staff, Collections.singletonList(unShelveId));
        return true;
    }

    /**
     * 下架单列表
     *
     * @param unShelveOrder 查询条件
     * @param page          分页数据
     * @return 下架单分页内容
     * @throws SessionException
     */
    @RequestMapping(value = "page", method = RequestMethod.GET)
    @ResponseBody
    public Object page(UnShelveOrder unShelveOrder, Page page) throws SessionException {
        Staff staff = getStaff();
        return unShelveService.page(staff, unShelveOrder, page, false);
    }

    /**
     * 下架单单对象明细
     *
     * @param id 下架单ID
     * @return 下架单，带明细
     * @throws SessionException
     */
    @RequestMapping(value = "info", method = RequestMethod.GET)
    @ResponseBody
    public Object info(@RequestParam(value = "id") Long id) throws SessionException {
        Staff staff = getStaff();
        return unShelveService.info(staff, id);
    }

    /**
     * 根据下架单ID，查询明细列表
     *
     * @param unShelveId 下架单ID
     * @return 下架单明细列表
     * @throws SessionException
     */
    @RequestMapping(value = "listDetail", method = RequestMethod.GET)
    @ResponseBody
    public Object listDetail(Long unShelveId, String outerId,Long waveId,String skuOuterId,String goodsSectionCodes,String title,Boolean accurate, Page page) throws SessionException {
        Staff staff = getStaff();
        Map<String, Object> param = new HashMap<>();
        param.put("outerId", outerId);
        param.put("waveId", waveId);
        param.put("skuOuterId", skuOuterId);
        param.put("goodsSectionCodes", ArrayUtils.toStringList(goodsSectionCodes));
        param.put("title", title);
        param.put("accurate", accurate);
        return unShelveService.listDetail(staff, unShelveId, param, page);
    }

    /**
     * 批量生成波次
     *
     * @param ids         需要生成波次的下架单
     * @param allAllocate 是否需要全部配货
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/create/wave", method = RequestMethod.POST)
    @ResponseBody
    public Object createWaves(String ids, Boolean allAllocate,Integer downShelfType) throws SessionException {
        List<WaveCreateResult> results = unShelveService.createWaves(getLightStaff(), ArrayUtils.toLongUniqList(ids), allAllocate, downShelfType);
        return results.stream().collect(Collectors.groupingBy(WaveCreateResult::getBusCode));
    }

    /**
     * 货位excel导入
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    public Object importGoodsSections(MultipartFile file, String api_name, Long unShelveId, Integer insertType, Boolean isGoodProducts,@RequestParam(value = "downShelfType",defaultValue = "1") Integer downShelfType) throws Exception {
        checkBeforeImport(file, -1);

        String[][] data = convertExcelData(file);
        Staff staff = getStaff();

        String importToken = wmsCacheBusiness.getKey(staff, WmsCacheEnum.UNSHELVE_ORDER_IMPORT_EXCEL);
        ImportProcess preProcess = wmsCacheBusiness.get(importToken);

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("下架单正在导入，请稍候！");
        }

        wmsCacheBusiness.set(importToken, WmsProcessUtils.buildBaseProcess(importToken, data.length));
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.unShelve.order.import").setArgs(new Object[]{staff, unShelveId, insertType, isGoodProducts,downShelfType}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("cacheKey", importToken);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * @param importToken token
     * @param api_name
     * @return 导入进度
     * @throws Exception
     */
    @RequestMapping(value = "/import/status")
    @ResponseBody
    public Object getImportStatus(String cacheKey, String api_name) throws Exception {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        Assert.notNull(process, "请先导入下架单！");
        if (process.isComplete()) {
            wmsCacheBusiness.delete(process.getCacheKey());
        }
        return process;
    }

    @ResponseBody
    @RequestMapping(value = "/exportUnshelve", method = {RequestMethod.POST})
    public Object export(@RequestBody UnShelveOrder unShelveOrder, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        condition.setStaffId(staff.getId());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出!");
        }
        eventCenter.fireEvent(this, new EventInfo("unShelve.download.excel").setArgs(new Object[]{staff, unShelveOrder}), false);
        Map<String, Object> map = Maps.newHashMap();
        map.put("result", "success");
        return map;
    }
}

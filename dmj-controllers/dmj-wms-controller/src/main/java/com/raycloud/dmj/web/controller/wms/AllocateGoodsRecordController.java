package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.wms.QueryAllocateGoodsRecordParams;
import com.raycloud.dmj.services.wms.IAllocateGoodsRecordService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@Controller
@RequestMapping("/wms/allocate/goods")
public class AllocateGoodsRecordController extends WmsInitController {

    @Resource
    private IAllocateGoodsRecordService allocateGoodsRecordService;

    /**
     * 配货记录查询
     * @param params
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(@RequestBody QueryAllocateGoodsRecordParams params, String api_name) throws Exception {
        checkAndSetParams(params);
        return allocateGoodsRecordService.queryInPageList(getLightStaff(), params);
    }

    private void checkAndSetParams(QueryAllocateGoodsRecordParams params) throws Exception {
        Assert.isTrue(params.getWarehouseId() != null, "仓库Id不能为空！");
        if (StringUtils.isNotBlank(params.getStartDate())) {
            params.setStartTime(DateUtil.getDate(params.getStartDate(),"yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isNotBlank(params.getEndDate())) {
            params.setEndTime(DateUtil.getDate(params.getEndDate(),"yyyy-MM-dd HH:mm:ss"));
        }
        if (CollectionUtils.isEmpty(params.getEnableStatusQuery())) {
            params.setEnableStatusQuery(Lists.newArrayList(0, 1));
        }
        params.setNeedOuterId("1");
        params.setContainQueryType("1");
    }


}

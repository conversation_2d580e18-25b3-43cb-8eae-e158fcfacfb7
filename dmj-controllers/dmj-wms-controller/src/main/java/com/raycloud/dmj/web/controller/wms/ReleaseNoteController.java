package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.utils.BackFileUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * ReleaseNote查看控制器
 */
@RequestMapping("/wms")
@Controller
public class ReleaseNoteController extends Sessionable {


    @RequestMapping("/pom")
    public void readPom() {
        BackFileUtils.readFile("com.raycloud.dmj", "dmj-wms-web", BackFileUtils.POM_FILE, request, response);
    }

    @RequestMapping("/ReleaseNote")
    public void readReleaseNote() {
        BackFileUtils.readFile(null, null, BackFileUtils.MD_FILE, request, response);
    }
}

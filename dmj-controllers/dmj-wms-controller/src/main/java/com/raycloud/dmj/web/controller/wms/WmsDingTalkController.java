package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.services.router.MessageRouterHandler;
import com.raycloud.dmj.domain.wms.router.RouterRequest;
import com.raycloud.dmj.domain.wms.router.RouterResponse;
import com.raycloud.dmj.web.controllers.Sessionable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021-03-15 21:04
 */
@Controller
@RequestMapping("/wms/dingtalk")
public class WmsDingTalkController extends Sessionable {

    @Resource
    private MessageRouterHandler messageRouterHandler;

    @RequestMapping(value = "/forward", method = RequestMethod.POST)
    @ResponseBody
    public RouterResponse forward(@RequestBody RouterRequest message) {
        logger.debug(String.format("url:%s,message:%s", getRequest().getRequestURL(), JSON.toJSONString(message)));

        RouterResponse response = messageRouterHandler.dispatcher(message);
        logger.debug(String.format("response:%s", JSON.toJSONString(response)));
        return response;
    }
}

package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.StockWmsBridgeBusiness;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.ColumnConf;
import com.raycloud.dmj.domain.trades.ColumnConfListWrapper;
import com.raycloud.dmj.domain.wms.GoodsSectionInventoryImport;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.trades.IColumnConfService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsIOService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionImportCache;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventoryImportVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.ImportDataResult;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * creatd 18/6/12
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/wms/goodsSectionInventory/")
@LogTag(value = "wms")
@AccessShield(value = "1002")
public class GoodsSectionExcelController extends WmsInitController {
    private Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IWmsIOService wmsIOService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private StockWmsBridgeBusiness stockWmsBridgeBusiness;

    @Resource
    private IColumnConfService columnConfService;

    @Resource
    private IFileUploadService fileUploadService;

    private static final int  CACHE_EXPIRE = 30 * 60;


    /**
     * 导出货位库存数据
     */
    @RequestMapping(value = "export", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object export(@RequestBody GoodsSectionInventorySearchParams params, String api_name) throws Exception {
    	if (CollectionUtils.isNotEmpty(params.getSortList())) {
			for (Sort sort : params.getSortList()) {
				Assert.notNull(sort.getField(), "排序值不能为空");
				Assert.notNull(sort.getOrder(), "排序类型不能为空");
			}
		}
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");

        //根据列配置
        String[] EXCEL_HEADER;
        ColumnConfListWrapper columnConfigList = columnConfService.getColumnConfigList(staff, 90);
        Assert.notNull(columnConfigList, "货位库存导出没有找到列配置数据,pageId=90");
        List<ColumnConf> columnConfList = columnConfigList.getColumnConfList();
        columnConfList.removeIf(columnConf -> "brand".equals(columnConf.getField()));
        Iterator<ColumnConf> iterator = columnConfList.iterator();
        while (iterator.hasNext()) {
            ColumnConf conf = iterator.next();
            if (stockWmsBridgeBusiness.isNewWms(staff) && (conf.getTitle().contains("可用数") || conf.getTitle().contains("锁定数"))) {
                iterator.remove();
                continue;
            }
            if (WmsUtils.isOldWms(staff) && (conf.getTitle().contains("已配数"))) {
                iterator.remove();
                continue;
            }
            if (conf.getTitle().equals("图片")) {
                iterator.remove();
            }
        }
        String fileName = new String("货位库存数据".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";
        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("货位库存数据");
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("goodsSection.inventory.download.excel").setArgs(new Object[]{staff, param, params,columnConfList}), false);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("result", "success");
        return map;
    }

    /**
     * 导出商品货位类型数据
     */
    @RequestMapping(value = "type/export", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object goodsExport(@RequestBody GoodsSectionInventorySearchParams params, String api_name) throws Exception {
        if (CollectionUtils.isNotEmpty(params.getSortList())) {
            for (Sort sort : params.getSortList()) {
                Assert.notNull(sort.getField(), "排序值不能为空");
                Assert.notNull(sort.getOrder(), "排序类型不能为空");
            }
        }
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");

        String fileName = new String("商品货位数据".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("商品货位数据");
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("goodsSection.type.download.excel").setArgs(new Object[]{staff, param, params}), false);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("result", "success");
        return map;
    }

    /**
     * 导入货位库存数据检查
     */
    @RequestMapping(value = "excel/check", method = RequestMethod.POST)
    @ResponseBody
    public Object importInventoryCheck(MultipartFile file, Integer type, String api_name) throws SessionException, IOException {
        return importExcel(getStaff(), file, type, false);
    }

    /**
     * 增量导入货位库存数据
     */
    @RequestMapping(value = "excel/incImport", method = RequestMethod.POST)
    @ResponseBody
    public Object importInventoryInc(MultipartFile file, Integer type, String api_name) throws SessionException, IOException {
        return importExcel(getStaff(), file, type, true);
    }

    private Map<String, Object> importExcel(Staff staff, MultipartFile file, Integer type, boolean inc) throws IOException {
        checkBeforeImport(file, type);

        Boolean openRecheck = staff.getConf().getOpenRecheck();
        Boolean isDefaultStaff = staff.isDefaultStaff();
        if (openRecheck != null && openRecheck) {
            Assert.isTrue(isDefaultStaff, "只有管理员才能进行该操作！");
        }
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_PRE);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("货位库存不能同时导入，请稍等！");
        }

        String[][] data = convertExcelData(file);
        process = new ImportProcess();
        process.setTotalNum(data.length);
        process.setComplete(false);
        wmsCacheBusiness.set(importKey, process, CACHE_EXPIRE);
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.gs.stock.import").setArgs(new Object[]{staff, type, importKey, inc}), fileResult.getUrl());

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("importToken", importKey);
        return result;
    }

    /**
     * 查看导入进度
     */
    @RequestMapping(value = "excel/status", method = RequestMethod.GET)
    @ResponseBody
    public Object getImportStatus(String importToken, String api_name) throws SessionException {
        ImportProcess process = wmsCacheBusiness.get(importToken);
        Assert.notNull(process, "当前excel导入信息已过期或者已被覆盖，请重新进行导入操作！");
        return process;
    }

    /**
     * 从excel中批量新增盘点任务
     */
    @RequestMapping(value = "excel/addTasks", method = RequestMethod.POST)
    @ResponseBody
    public Object batchAddFromExcel(String dataKey, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.hasText(dataKey, "请传入导入数据key!");
        GoodsSectionInventoryImportVo result = wmsIOService.saveBatchCheckTasks(staff, dataKey);
        writeOpLog(staff, "excelAddTasks", String.format("通过excel批量生成盘点任务,原库存:%d,导入库存:%d,生成盘点任务数:%d", result.getPreNum(), result.getTargetNum(), result.getCount()), null);
        return result;
    }

    /**
     * 从excel中批量覆盖库存
     */
    @RequestMapping(value = "excel/import", method = RequestMethod.POST)
    @ResponseBody
    public Object batchImportFromExcel(String dataKey, String api_name) throws SessionException {
        Staff staff = getStaff();
        Boolean openRecheck = staff.getConf().getOpenRecheck();
        Boolean isDefaultStaff = staff.isDefaultStaff();
        if (openRecheck != null && openRecheck) {
            Assert.isTrue(isDefaultStaff, "只有管理员才能进行该操作！");
        }
        Assert.hasText(dataKey, "请传入导入数据key!");
        GoodsSectionImportCache importCache = wmsCacheBusiness.get(dataKey);
        if (importCache != null && CollectionUtils.isNotEmpty(importCache.getErrorList())) {
            StringBuilder stringBuilder = new StringBuilder("");
            importCache.getErrorList().forEach(s -> {
                stringBuilder.append(s.getMessage()).append(";");
            });
            if (Objects.equals(importCache.getErrorList().size(), importCache.getImportRows())) {
                throw new IllegalArgumentException(stringBuilder.toString());
            } else {
                logger.debug(LogHelper.buildLog(staff, stringBuilder.toString()));
            }
        }
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_DATA_COVER);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在导入货位库存，请稍候！");
        }

        ImportProcess importProcess = WmsProcessUtils.buildBaseProcess(cacheKey, 100);
        importProcess.setDataKey(dataKey);
        wmsCacheBusiness.set(cacheKey, importProcess, CACHE_EXPIRE);

        eventCenter.fireEvent(this, new EventInfo("wms.gs.stock.cover.import").setArgs(new Object[]{staff, importProcess}), null);
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", cacheKey);
        return result;
    }

    /**
     * 查询不一致的excel库存数据
     */
    @RequestMapping(value = "excel/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryExcelPageList(String importToken, Page page, String api_name) throws SessionException {
        Staff staff = getStaff();
        ImportDataResult<GoodsSectionInventoryImport> result = wmsIOService.queryExcelPageList(staff, page);
        result.setOpenRecheck(staff.getConf().getOpenRecheck());
        return result;
    }

    /**
     * 清除当前 excel导入缓存信息
     */
    @RequestMapping(value = "excel/clearCur", method = RequestMethod.POST)
    @ResponseBody
    public Object clearExcelCacheCur(String importToken, String api_name) {
        try {
            return wmsIOService.clearDataCache(getStaff(), null);
        } catch (Exception e) {
            return false;
        }
    }

}

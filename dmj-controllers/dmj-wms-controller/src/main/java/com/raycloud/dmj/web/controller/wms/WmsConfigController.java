package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.CustomSortRule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.ReserveAreaPickGoods;
import com.raycloud.dmj.domain.wms.WarehousePositionConfig;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.enums.ValidateAllocateStockTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.services.wms.IReserveAreaPickGoodsService;
import com.raycloud.dmj.services.wms.IWarehousePositionConfigService;
import com.raycloud.dmj.services.wms.IWmsConfigService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.ReserveAreaPickGoodsParams;
import com.raycloud.dmj.web.model.wms.WarehousePositionConfigParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 仓库配置控制器
 * Created by shaoxianchang on 2016/11/9.
 */
@Controller
@RequestMapping("/wms/config")
@LogTag(value = "wms")
public class WmsConfigController extends WmsInitController {

    /**
     * 备货区可拣商品excel导入文件最大限制
     */
    public final static long RESERVE_AREA_PICK_GOODS_MAX_FILE_SIZE = 2 * 1024 * 1024;

    @Resource
    private IWmsConfigService wmsConfigService;

    @Resource
    private IReserveAreaPickGoodsService reserveAreaPickGoodsService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IFileUploadService fileUploadService;

    /**
     * 更改复盘配置
     * @param config 仓储配置
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateConfig(@RequestBody WmsConfig config, String api_name) throws SessionException {
        Assert.notNull(config, "配置信息不能为空！");

        Staff staff = getStaff();
        if (!staff.getConf().isOpenWms()) {
            throw new IllegalArgumentException("请先开启仓储才能进行该操作！");
        }
        wmsConfigService.save(staff, config);

        String content = "保存仓储配置信息，复盘配置：" + config.getOpenRecheck() + "，货位编码显示配置："
                + config.getOpenGsCodeDisplay() + "，备货区配置：" + config.getOpenBackRegion()
                + "，备货区参与拣选：" + config.getPickRegionType();
        writeOpLog(staff, Domain.WMS, "wmsConfig", content, JSON.toJSONString(config));
        return successResponse();
    }

    @RequestMapping(value = "/system/save", method = RequestMethod.POST)
    @ResponseBody
    @AccessShield("990")
    public Object systemConfigSave(@RequestBody WmsConfig config, String api_name) throws SessionException {
        Assert.notNull(config, "配置信息不能为空！");
        Staff staff = getStaff();
        if (!staff.getConf().isOpenWms()) {
            throw new IllegalArgumentException("请先开启仓储才能进行该操作！");
        }
        if(ValidateAllocateStockTypeEnum.SCRATCH_SPACE.getType().equals(config.getValidateAllocateStockSection())){
            Assert.isTrue(WmsUtils.isNewWms(staff), "请先开启暂存区！");
        }
        wmsConfigService.save(staff, config);

        String content = "保存仓储配置信息，复盘配置：" + config.getOpenRecheck() + "，货位编码显示配置："
                + config.getOpenGsCodeDisplay() + "，备货区配置：" + config.getOpenBackRegion()
                + "，备货区参与拣选：" + config.getPickRegionType();
        writeOpLog(staff, Domain.WMS, "wmsConfig", content, JSON.toJSONString(config));
        return successResponse();
    }

    /**
     * 查询仓储配置
     * @return 配置信息
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    @ResponseBody
    public Object getConfig(String api_name) throws SessionException {
        Staff staff = getLightStaff();
        return wmsConfigService.get(staff);
    }

    /**
     * 获取拣选路径
     *
     * @return
     */
    @RequestMapping(value = "/pickGoodsRouteConfig", method = RequestMethod.GET)
    @ResponseBody
    public Object getPickGoodsRouteConfig(String api_name) throws SessionException {
        return wmsConfigService.getPickGoodsRouteConfig(getStaff());
    }

    @RequestMapping(value = "/rules", method = RequestMethod.POST)
    @ResponseBody
    public Object setPickGoodsRouteConfig(@RequestBody List<CustomSortRule> ruleList, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        wmsConfigService.setPickGoodsRouteConfig(staff, ruleList);
        return Status.buildSuccessStatus();
    }


    /**
     * 保存拣选路径
     *
     * @return
     */
    @RequestMapping(value = "/pickGoodsRouteConfig/save", method = RequestMethod.POST)
    @ResponseBody
    public Object setRules(@RequestBody CustomSortRules customSortRules, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        List<CustomSortRule> rules = customSortRules.getRules();
        wmsConfigService.updatePickGoodsRouteConfig(staff, rules);
        return Status.buildSuccessStatus();
    }

    private static class CustomSortRules {

        private List<CustomSortRule> rules;

        public List<CustomSortRule> getRules() {
            return rules;
        }

        public void setRules(List<CustomSortRule> rules) {
            this.rules = rules;
        }
    }

    /**
     * 保存配货库区及配货顺序
     *
     * @return
     */
    @RequestMapping(value = "/allocate/sort/save", method = RequestMethod.POST)
    @ResponseBody
    public Object saveAllocateRule(@RequestBody WmsConfig.AllocateOrderAndRegionConfig config, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        Assert.notNull(config, "配置参数不能为空! ");
        wmsConfigService.saveAllocateRule(staff, config);
        return Status.buildSuccessStatus();
    }

    /**
     * 批量保存备货区可拣商品
     * @param params
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/reserve/goods/save", method = RequestMethod.POST)
    @ResponseBody
    public Object batchSaveReserveAreaPickGoods(@RequestBody ReserveAreaPickGoodsParams params, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (params == null || params.getReserveAreaPickGoods() == null
                || params.getReserveAreaPickGoods().size() <= 0) {
            throw new IllegalArgumentException("备货区可拣货商品不能为空！");
        }
        List<ReserveAreaPickGoods> goods = params.getReserveAreaPickGoods();
        for (ReserveAreaPickGoods good : goods) {
            good.setSysSkuId(DataUtils.getZeroIfDefault(good.getSysSkuId()));
        }
        List<BatchExecuteResult<ReserveAreaPickGoods>> result = reserveAreaPickGoodsService.saves(staff, goods, 0);
        return result;
    }

    /**
     * 分页查询备货区可拣商品
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/reserve/goods/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryReserveAreaPickGoodsByPage(ReserveAreaPickGoodsParams params, String api_name) throws SessionException {
        return reserveAreaPickGoodsService.queryPageList(getLightStaff(), params);
    }

    /**
     * 根据ID批量删除备货区可拣商品
     * @param ids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/reserve/goods/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object batchDeleteReserveAreaPickGoods(String ids, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择要删除的商品！");
        }
        List<Long> delIds = Lists.newArrayList();
        for (String id : ids.split(",")) {
            delIds.add(Long.parseLong(id));
        }
        reserveAreaPickGoodsService.batchDelete(staff, delIds);
        return successResponse();
    }

    /**
     * 备货区可拣商品excel导入进度
     * @param importToken 缓存key
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reserve/goods/import/status")
    @ResponseBody
    public Object getImportStatus(String importToken, String api_name) throws Exception {
        ImportProcess process = wmsCacheBusiness.get(importToken);
        Assert.notNull(process, "请先导入指定商品！");
        if (process.isComplete()) {
            wmsCacheBusiness.delete(process.getCacheKey());
            int errorNum = process.getErrorNum();
            writeOpLog(getLightStaff(), Domain.WMS, "reserveAreaPickGoodsExcelImportResult", importToken, String.format("指定商品导入，结果：%s，总条数：%s，失败：%s", (errorNum > 0 ? "失败" : "成功"), process.getTotalNum(), errorNum), null, errorNum > 0 ? 1 : 0);
        }
        return process;
    }

    /**
     * 备货区可拣商品excel导入
     * @param file
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reserve/goods/import", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#file.originalFilename", content = "'备货区可拣商品:' + #file.originalFilename")
    public Object importGoodsSections(MultipartFile file, String api_name) throws Exception {
        // 1.文件校验，不超过2M
        checkBeforeImport(file, -1);
        if (file.getSize() > RESERVE_AREA_PICK_GOODS_MAX_FILE_SIZE) {
            throw new IllegalArgumentException("上传的excel文件大小不能超过2MB");
        }
        // 2.excel格式转化
        String[][] data = convertExcelData(file);
        Staff staff = getStaff();
        // 3.设置导入缓存
        String importToken = wmsCacheBusiness.getKey(staff, WmsCacheEnum.RESERVE_AREA_PICK_GOODS_IMPORT);
        ImportProcess preProcess = wmsCacheBusiness.get(importToken);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在导入备货区可拣商品，请稍候！");
        }
        wmsCacheBusiness.set(importToken, WmsProcessUtils.buildBaseProcess(importToken, data.length));
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        // 4.触发导入事件
        eventCenter.fireEvent(this, new EventInfo("wms.reserve.area.goods.import").setArgs(new Object[]{staff}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importToken);
        return result;
    }
}

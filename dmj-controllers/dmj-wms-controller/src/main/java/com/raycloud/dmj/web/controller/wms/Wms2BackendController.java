package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.WorkingStorageSectionBusiness;
import com.raycloud.dmj.dao.stock.StockInOutRecordDAO;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.params.StockInOutRecordQueryParams;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.stock.StockInOutRecord;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.WmsChangeAffect;
import com.raycloud.dmj.domain.wms.WorkingStorageSection;
import com.raycloud.dmj.domain.wms.WorkingStorageSectionGoods;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IBackendService;
import com.raycloud.dmj.services.wms.storagesection.IWorkingStorageSectionManageService;
import com.raycloud.dmj.services.wms.storagesection.IWorkingStorageSectionService;
import com.raycloud.dmj.services.wms.support.AllCompanyDealer;
import com.raycloud.dmj.services.wms2.IWmsStockService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.utils.date.DateUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 18/12/19
 */

@Controller
@RequestMapping("/wms/backend")
public class Wms2BackendController extends WmsInitController {
    @Resource
    private IWmsStockService wmsStockService;
    @Resource
    private WorkingStorageSectionBusiness workingStorageSectionBusiness;
    @Resource
    IWorkingStorageSectionManageService workingStorageSectionManageService;
    @Resource
    IWorkingStorageSectionService workingStorageSectionService;
    @Resource
    private AllCompanyDealer allCompanyDealer;
    @Resource
    private IBackendService backendService;
    @Resource
    private StockInOutRecordDAO stockInOutRecordDAO;
    @Resource
    private WmsItemBusiness wmsItemBusiness;

    @RequestMapping(value = "/wms2/openStorageSection", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object openStorageSection(Integer openStorageSection) throws SessionException {
        Assert.notNull(openStorageSection, "请输入参数openStorageSection");
        workingStorageSectionBusiness.updateOpenStorageSection(getLightStaff(), openStorageSection);
        return successResponse();
    }

    @RequestMapping(value = "/wms2/check", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object check(@RequestBody WorkingStorageSection section) throws SessionException {
        section.setBusiType(StockChangeBusiType.STOCK_ADJUST);
        wmsStockService.check(getStaff(), section);
        return successResponse();
    }

    @RequestMapping(value = "/wms2/workingStorageSectionGoods/create", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object createWorkingStorageSectionGoods(Long warehouseId, String sysItemIds, String sysSkuIds, String nums) throws SessionException {
        List<Long> sysItemIdList = ArrayUtils.toLongList(sysItemIds);
        List<Long> sysSkuIdList = ArrayUtils.toLongList(sysSkuIds);
        List<Long> numList = ArrayUtils.toLongList(nums);
        int size = sysItemIdList.size();
        Assert.isTrue(size == sysSkuIdList.size() && size == numList.size(), "参数长度不一致");
        Staff staff = getLightStaff();
        WorkingStorageSection workingStorageSection = workingStorageSectionManageService.create(staff, WorkingStorageSection.TypeEnum.PICK, warehouseId);
        workingStorageSection.setBusiType(StockChangeBusiType.PICK_GOODS);
        List<WorkingStorageSectionGoods> goodsList = new ArrayList<WorkingStorageSectionGoods>(size);
        for (int i = 0; i < size; i++) {
            WorkingStorageSectionGoods goods = new WorkingStorageSectionGoods();
            goods.setSysItemId(sysItemIdList.get(i));
            goods.setSysSkuId(sysSkuIdList.get(i));
            goods.setNum(numList.get(i).intValue());
            goods.setQualityType(true);
            goodsList.add(goods);
        }
        workingStorageSection.setGoodsList(goodsList);
        workingStorageSectionService.in(staff, workingStorageSection);
        return workingStorageSection;
    }

    @RequestMapping(value = "/wms2/outGoods")
    @ResponseBody
    public Object outWorkingStorageGoods(@RequestBody WorkingStorageSection section) throws Exception {
        Assert.notNull(section.getWarehouseId(), "请选择仓库");
        Assert.notNull(section.getType(), "请选择暂存区");
        Assert.notEmpty(section.getGoodsList(), "请选择商品！");

        wmsStockService.outWorkingStorageSection(getLightStaff(), section);
        return successResponse();
    }
    
    @RequestMapping(value = "/check/order/stock/notConsistent", method = RequestMethod.POST)
    @ResponseBody
    public Object repairGoodsStock(String companyIdStr, Long warehouseId, String outerId, Boolean qualityType, String startTime, String endTime) throws Exception {
        Assert.hasText(companyIdStr, "companyIdStr not null!");
        Assert.notNull(warehouseId, "warehouseId  not null");
        Assert.hasText(outerId, "outerId not null!");
        return allCompanyDealer.dealWithCompanyIds(ArrayUtils.toLongList(companyIdStr), staff -> {
            return backendService.checkOrderStockNotConsistent(staff, warehouseId, outerId, qualityType, startTime, endTime);
        });
    }
    
    
    
}

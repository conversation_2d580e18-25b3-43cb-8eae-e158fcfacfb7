package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.wms.AssoOrderBoxDao;
import com.raycloud.dmj.dao.wms.BoxDao;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PrintBoxQueryParams;
import com.raycloud.dmj.domain.WmsConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.Box;
import com.raycloud.dmj.domain.wms.BoxQueryParams;
import com.raycloud.dmj.domain.wms.BoxQueryRequest;
import com.raycloud.dmj.domain.wms.BoxSaveParams;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.services.domain.ReceiptTrace;
import com.raycloud.dmj.services.domain.ReceiptType;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.services.log.OpLogs;
import com.raycloud.dmj.services.wms.IBoxLabelService;
import com.raycloud.dmj.services.wms.IWmsLogTraceService;
import com.raycloud.dmj.services.wms2.IBoxStockService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsLogHelper;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.BoxComposeOpVo;
import com.raycloud.dmj.web.model.wms.WmsStockQueryParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 箱库存控制器
 *
 * <AUTHOR>
 * @date 2020/3/18 18:34
 */
@RequestMapping("/wms/box")
@Controller
public class BoxStockController extends WmsInitController {

    @Resource
    private IBoxStockService boxStockService;

    @Resource
    private IWmsLogTraceService boxLogTraceService;

    @Resource
    private BoxDao boxDao;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IFileUploadService fileUploadService;

    @Resource
    private AssoOrderBoxDao assoOrderBoxDao;

    @Resource
    private IBoxLabelService boxService;

    /**
     * 生成箱码
     * @param num
     * @return
     */
    @RequestMapping(value = "/generate/code")
    @ResponseBody
    public Object generateBoxCodes(Integer num, Long warehouseId) throws Exception {
        Assert.notNull(num, "num not null!");
        return boxStockService.generateBoxCodes(getLightStaff(), num, warehouseId);
    }

    /**
     * 保存箱信息
     *
     * @param boxes
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/save")
    @ResponseBody
    public Object save(@RequestBody Box[] boxes) throws Exception {
        Assert.notEmpty(boxes, "boxes not empty!");

        return boxStockService.save(getLightStaff(), Lists.newArrayList(boxes),null,null);
    }

    /**
     * 批量保存箱信息
     *
     * @param params
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batch/save")
    @ResponseBody
    public Object batchSave(@RequestBody BoxSaveParams params) throws Exception {
        Assert.notEmpty(params.getGoodsList(), "商品明细不能为空！!");

        return boxStockService.batchSave(getLightStaff(), params);
    }

    /**
     * 查询箱库存信息
     */
    @RequestMapping(value = "/stock/list", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object queryStockPageList(@RequestBody WmsStockQueryParams params) throws SessionException {
        return boxStockService.queryStockPageList(getLightStaff(), params);
    }


    /**
     * 查询箱库存
     *
     * @param isBoxed
     * @param boxCodes
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query/stock")
    @ResponseBody
    public Object query(Boolean isBoxed, String boxCodes) throws Exception {
        Assert.hasText(boxCodes, "boxCodes not empty!");

        return boxStockService.query(getLightStaff(), isBoxed, ArrayUtils.toStringArray(boxCodes));
    }

    /**
     * 查询箱信息，用于打印
     * @param params
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query/stock/print", method = RequestMethod.GET)
    @ResponseBody
    public Object query(@RequestBody PrintBoxQueryParams params) throws Exception {
        Assert.notEmpty(params.getBoxCodes(), "boxCodes not empty!");
        return boxStockService.query(getLightStaff(), params);
    }

    /**
     * 箱管理分页查询
     * @param request
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/pageLit", method = RequestMethod.POST)
    @ResponseBody
    public Object listPage(@RequestBody BoxQueryRequest request, String api_name) throws SessionException {

        Staff staff = getStaff();
        return boxStockService.pageList(staff, request, request.getPage());
    }

    /**
     * 查询箱明细
     * @param id
     * @param outerId
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/detail/list", method = RequestMethod.GET)
    @ResponseBody
    public Object listDetails(Long id, String outerId, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(id, "箱id不能为空!");
        Box box = boxStockService.detailList(staff, id, outerId);
        return box.getGoodsList();
    }

    @RequestMapping("/oplog/list")
    @ResponseBody
    public Object opLogList(String code, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        Assert.hasText(code, "请选择箱码！");
        OpLogs logs = opLogService.list(staff, null, code, null, "wms", null, null, new Page().setPageNo(1).setPageSize(100), null, null);
        return handlerRecord(logs.getList());
    }

    /**
     * 更新箱子备注
     * @param boxId
     * @param remark
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/update/remark", method = RequestMethod.POST)
    @ResponseBody
    public Object updateRemark(Long boxId, String remark, String api_name) throws SessionException {
        Assert.notNull(boxId, "箱id不能为空！");
        Assert.notNull(remark, "备注不能为空！");
        Staff staff = getLightStaff();
        boxStockService.updateRemark(staff, boxId, remark);
        return successResponse();
    }

    /**
     * 查询箱库存(后台测试用，前端目前不会调)
     *
     * @param params
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query/stock/params")
    @ResponseBody
    public Object query(@RequestBody BoxQueryParams params) throws Exception {

        return boxStockService.queryBoxInfo(getLightStaff(), params);
    }

    /**
     * 箱库存盘点
     *
     * @param box
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/inventory")
    @ResponseBody
    public Object inventory(@RequestBody Box box) throws Exception {
        box.setSheetLogRemark("PC修改装箱数量，创建盘点单");
        box.setSheetRemark("修改装箱数量");
        box.setSheetSource("PC修改装箱数量");
        return boxStockService.inventory(getLightStaff(), box);
    }

    /**
     * 收货装箱
     *
     * @param box
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/receive/pack")
    @ResponseBody
    public Object receiveAndPack(@RequestBody Box box) throws Exception {

        return boxStockService.receiveAndPack(getLightStaff(), box);
    }

    /**
     * 按箱收货
     *
     * @param box
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/receive")
    @ResponseBody
    public Object receive(@RequestBody Box box) throws Exception {
        Assert.hasText(box.getBoxCode(), "箱码不能为空！");
        Staff staff = getLightStaff();
        Box origin = boxStockService.validateBox(staff, box.getBoxCode(), WmsConstants.BOX_OP_RECEIVE);
        origin.setBoxed(false);
        return boxStockService.receiveAndPack(staff, origin);
    }

    /**
     * 校验箱
     *
     * @param boxCode
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/validate")
    @ResponseBody
    public Object validateBox(String boxCode, Integer checkType, String api_name) throws Exception {
        Assert.hasText(boxCode, "请输入箱码");
        Assert.notNull(checkType, "请选择校验类型");

        return boxStockService.validateBox(getLightStaff(), boxCode, checkType);
    }

    /**
     * 按箱上架
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/shelve")
    @ResponseBody
    public Object shelve(@RequestBody BoxComposeOpVo vo) throws Exception {
        Assert.notNull(vo.getBox(), "请选择箱！");
        Assert.notNull(vo.getGoodsSection(), "请选择货位！");
        Box box = vo.getBox();
        box.setUnpack(true);
        return boxStockService.shelve(getLightStaff(), box, vo.getGoodsSection());
    }

    /**
     * 移箱
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/transfer")
    @ResponseBody
    public Object transfer(@RequestBody BoxComposeOpVo vo) throws Exception {
        Assert.notNull(vo.getBox(), "请选择箱！");
        Assert.notNull(vo.getGoodsSection(), "请选择货位！");

        return boxStockService.transfer(getLightStaff(), vo.getBox(), vo.getGoodsSection());
    }

    /**
     * 初始化装箱
     *
     * @param box
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/init/pack")
    @ResponseBody
    public Object initAndPack(@RequestBody Box box) throws Exception {

        return boxStockService.initAndPack(getLightStaff(), box);
    }

    /**
     * 初始化箱登记
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/init/register")
    @ResponseBody
    public Object initAndRegister(@RequestBody BoxComposeOpVo vo) throws Exception {
        Assert.notNull(vo.getBox(), "请选择箱！");
        Assert.notNull(vo.getGoodsSection(), "请选择货位！");

        return boxStockService.initAndRegister(getLightStaff(), vo.getBox(), vo.getGoodsSection());
    }

    /**
     * 获取单据操作日志
     *
     * @param receiptId
     * @param receiptType
     * @return
     * @throws Exception
     */
    @RequestMapping("/trace")
    @ResponseBody
    public Object queryReceiptTraces(Long receiptId, Integer receiptType) throws Exception {
        Assert.notNull(receiptId, "请选择单据！");
        Assert.notNull(receiptType, "请选择单据类型！");

        Staff staff = getLightStaff();
        List<ReceiptTrace> traces = boxLogTraceService.queryByReceipt(staff, Collections.singletonList(receiptId), receiptType);

        if (ReceiptType.BOX.getType().equals(receiptType)) {
            Box box = boxDao.queryById(staff, receiptId);
            if (box != null) {
                OpLogs opLogs = opLogService.list(staff, null, box.getBoxCode(), null, Domain.WMS.getValue(), null, null, new Page().setPageNo(1).setPageSize(100), null, null);
                traces.addAll(WmsLogHelper.convertOpLogsToTraces(opLogs.getList(), ContainerTypeEnum.BOX));
            }
        }
        //排序
        if (CollectionUtils.isNotEmpty(traces)) {
            traces.sort(Comparator.comparing(ReceiptTrace::getCreated).reversed()
                    .thenComparing(Comparator.nullsLast(Comparator.comparing(ReceiptTrace::getId).reversed())));
        }
        return traces;
    }

    /**
     * box导出
     *
     * @param params
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/excel/export", method = {RequestMethod.POST})
    @ResponseBody
    public Object export(@RequestBody BoxQueryRequest params, String api_name) throws Exception {
        return boxStockService.export(getLightStaff(),params);
    }

    /**
     * excel导入
     */
    @ResponseBody
    @RequestMapping(value = "/excel/import",method = RequestMethod.POST)
    public Object detailExcelAdd(MultipartFile file, String api_name) throws Exception {
        Staff staff = getStaff();
        Map<String, Object> result = new HashMap<String, Object>();
        //文件校验
        if (file == null) {
            result.put("errorMessage", "请选择要导入的excel文件");
            return result;
        }
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix) && !".csv".equals(fileSuffix)) {
            result.put("errorMessage", "文件格式只支持EXL表格与CSV格式");
            return result;
        }
        if (file.getSize() > 10 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于10M");
            return result;
        }
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_BOX_CREATE);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("创建箱导入不能同时导入，请稍等！");
        }
        Integer ignoreRows = 2;
        String[][] data ;
        try {
            data = convertExcelData(file,0);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("errorMessage", "上传文件内容转化数据异常，检查内容是否为空, 请下载后重新尝试");
            return result;
        }
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        process = new ImportProcess();
        process.setTotalNum(data.length - ignoreRows);
        process.setComplete(false);
        wmsCacheBusiness.set(importKey, process, 30 * 60);
        eventCenter.fireEvent(this, new EventInfo("wms.box.order.import").setArgs(new Object[]{staff,fileResult.getUrl(),ignoreRows}), false);
        result.put("importKey", importKey);
        result.put("isSuccess", true);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/import/status",method = RequestMethod.GET)
    public Object queryImportStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            process.setRightNum(process.getTotalNum() - process.getErrorNum());
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }
    
    
    /**
     * 批量拆箱
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batch/unPack")
    @ResponseBody
    public Object batchUnPack(String boxCodeStr) throws Exception {
        Assert.hasText(boxCodeStr, "请选择箱码!");
        Staff staff = getStaff();
        return boxStockService.batchDownBoxInSameArea(staff, ArrayUtils.toStringList(boxCodeStr), "PC");
    }

    /**
     * 单据和箱查询
     */
    @RequestMapping("/asso/order/query")
    @ResponseBody
    public Object AssoOrderQuery(AssoOrderBoxParams params, String api_name) throws Exception {
        Staff staff = getLightStaff();
        return assoOrderBoxDao.queryByCondition(staff,params);
    }

    /**
     * 删除虚拟箱
     */
    @RequestMapping(value = "/batch/delete/vitualBox")
    @ResponseBody
    public Object batchDeleteVitualBox(String ids, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(ids, "箱id不能为空!");
        return boxStockService.batchDeleteVitualBox(staff, ArrayUtils.toLongList(ids));
    }


    /**
     * 查找箱标签
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query/labels")
    @ResponseBody
    public Object queryLabels() throws Exception {
        Staff staff = getStaff();
        return boxService.queryBoxLabelInfo(staff);
    }

    /**
     * 修改箱信息（目前开放修改箱标签信息）
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/update/labelInfo", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBoxInfo(@RequestBody Box update) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(update, "修改参数不能为空！");
        Assert.notNull(update.getEditType(), "编辑标签的类型不能为空！");
        boxService.updateBoxInfo(staff, update);
        return successResponse();
    }

    /**
     * 修改箱信息（目前开放修改箱标签信息）
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/update/batch/labelInfo", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBoxInfoBatch(String boxIds, String labelIds, Integer type) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(StringUtils.isNotBlank(boxIds), "批量修改箱id为空！");
        Assert.isTrue(type != null, "批量标签修改类型为空！");
        boxService.updateBoxInfoBatch(staff, boxIds, labelIds, type);
        return successResponse();
    }

    /**
     * 自由装箱
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/free/pack")
    @ResponseBody
    public Object freePack(@RequestBody BoxSaveParams params) throws Exception {
        Assert.notNull(params, "请选择箱！");
        Staff staff = getLightStaff();

        return boxStockService.batchFreeSave(staff, params);
    }

    /**
     * 自由装箱
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/free/pack/check")
    @ResponseBody
    public Object checkfreePack(@RequestBody Box params) throws Exception {
        Assert.notNull(params, "请选择箱！");
        Staff staff = getLightStaff();
        boxStockService.bulkAvaliableNum(staff, params);
        return params;
    }

}

package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.StockRegion;
import com.raycloud.dmj.services.wms.IStockRegionZoneService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.StockRegionZoneSearchParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.xml.stream.events.Comment;

/**
 * 库存警戒controller
 * creatd 18/5/23
 *
 * <AUTHOR>
 */
@RequestMapping("/wms/zone")
@Controller
@LogTag(value = "wms")
public class StockRegionZoneController extends WmsInitController {

    @Resource
    private IStockRegionZoneService stockRegionZoneService;

    /**
     * 货位分区查询
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Object queryGoodsSectionPageList(StockRegionZoneSearchParams params, Page page, String api_name) throws SessionException {
        return stockRegionZoneService.queryStockRegion(getLightStaff(), params);
    }

    /**
     * 删除货位分区
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteStockRegion(Long id, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        StockRegion stockRegion = stockRegionZoneService.deleteStockRegion(staff, id);
        writeOpLog(staff, "deleteStockRegion", String.format("删除货位，名称:%s", stockRegion.getName()), String.format("库区id:%d", id));
        return true;
    }

    /**
     * 修改货位分区
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @LogTag(key = "#id + ''", content = "'修改分区,名称:'+#name")//,id为'+#id+'
    @ResponseBody
    public Object modifyStockRegion(@RequestParam("id") Long id, @RequestParam("name") String name, Boolean isMulti, Integer bindItemSingle, String api_name) throws SessionException {
        StockRegion stockRegion = new StockRegion();
        stockRegion.setId(id);
        stockRegion.setName(name);
        stockRegion.setIsMulti(isMulti);
        stockRegion.setBindItemSingle(bindItemSingle);
        stockRegionZoneService.updateStockRegion(stockRegion, getLightStaff());
        return true;
    }

    /**
     * 新增货位分区
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @LogTag(key = "#warehouseId + ''", content = "'新增分区,名称:'+#name")
    @ResponseBody
    public Object addStockRegion(Long warehouseId, String name, String code,Integer stockRegionType, Integer bindItemSingle, String api_name) throws SessionException {
        if(stockRegionType == null){
            stockRegionType = 1;
        }

        StockRegion stockRegion = new StockRegion();
        if(StringUtils.isNoneBlank(code)) {
            stockRegion.setCode(code);
        }
        stockRegion.setName(name);
        stockRegion.setWarehouseId(warehouseId);
        stockRegion.setStockRegionType(stockRegionType);
        stockRegion.setProductTimeMulti(CommonConstants.JUDGE_NO);
        stockRegion.setBatchMulti(CommonConstants.JUDGE_NO);
        stockRegion.setBindItemSingle(bindItemSingle);
        stockRegionZoneService.addStockRegion(stockRegion, getLightStaff(), false);
        return stockRegion.getId();
    }

    /**
     * 同步货位分区
     */
    @RequestMapping(value = "sync", method = RequestMethod.POST)
    @LogTag(key = "#warehouseId + ''", content = "'同步货位分区,名称:'+#name")
    @ResponseBody
    public Object addStockRegion(Long warehouseId,Long stockRegionZoneId,Long syncWarehouseId,Long syncStockRegionZoneId,String stockRegionZoneIds, String api_name) throws SessionException {
        StockRegion current=new StockRegion();
        current.setWarehouseId(warehouseId);
        current.setId(stockRegionZoneId);
        StockRegion sync = new StockRegion();
        sync.setWarehouseId(syncWarehouseId);
        sync.setId(syncStockRegionZoneId);
        sync.setStockRegionIds(stockRegionZoneIds);
        stockRegionZoneService.sync(getLightStaff(), current,sync);
        return successResponse();
    }

}

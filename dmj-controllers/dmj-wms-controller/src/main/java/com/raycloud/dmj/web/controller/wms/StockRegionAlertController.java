package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.IStockRegionAlertService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.StockRegionAlertVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 库存警戒controller
 * creatd 18/5/23
 *
 * <AUTHOR>
 */
@RequestMapping("/wms/alert")
@Controller
@LogTag(value = "wms")
public class StockRegionAlertController extends WmsInitController {

    @Resource
    private IStockRegionAlertService stockRegionAlertService;
    @Resource
    private WmsCacheBusiness wmsCacheBusiness;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IDownloadCenterService downloadCenterService;
    @Resource
    private IFileUploadService fileUploadService;

    /**
     * 库区警戒查询
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Object queryGoodsSectionPageList(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        return stockRegionAlertService.query(getLightStaff(), params, page);
    }

    /**
     * 库区警戒查询
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Object saveGoodsSectionPageList(@RequestBody StockRegionAlertVo stockRegionAlertVo, String api_name) throws SessionException {
        stockRegionAlertService.save(getLightStaff(), stockRegionAlertVo.getStockRegionAlerts());
        return successResponse();
    }

    /**
     * 导入库区警戒
     */
    @RequestMapping(value = "/excel/import", method = RequestMethod.POST)
    @ResponseBody
    public Object importStockRegionAlert(MultipartFile file, String api_name) throws SessionException, IOException {
        checkBeforeImport(file, -1);
        Staff staff = getLightStaff();
        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_STOCK_REGION_ALERT_DATA_IMPORT);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null) {
            throw new IllegalArgumentException("上一次库区警戒导入还未执行完，请稍后重试!");
        }
        // 预设置缓存，解决事件处理延长，查询缓存为空的问题
        String[][] data = convertExcelData(file, 1);
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        ImportProcess processAdvance = WmsProcessUtils.buildBaseProcess(importKey, 0);
        wmsCacheBusiness.set(processAdvance.getCacheKey(), processAdvance);
        eventCenter.fireEvent(this, new EventInfo("wms.stock.region.alert.import").setArgs(new Object[]{staff, data}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importKey);
        return result;
    }

    /**
     * 货位excel导入进度
     */
    @RequestMapping(value = "/excel/import/status")
    @ResponseBody
    public Object getImportStatus(String importToken, String api_name) throws Exception {
        ImportProcess process = wmsCacheBusiness.get(importToken);
        Assert.notNull(process, "请先进行库存预警导入！");
        if (process.isComplete()) {
            wmsCacheBusiness.delete(process.getCacheKey());
            int errorNum = process.getErrorNum();
            writeOpLog(getLightStaff(), Domain.WMS, "stockRegionAlertExcelImportResult", importToken, String.format("库区警戒导入，结果：%s，总条数：%s，失败：%s", (errorNum > 0 ? "失败" : "成功"), process.getTotalNum(), errorNum), null, errorNum > 0 ? 1 : 0);
        }
        return process;
    }

    /**
     * 导出库区警戒
     */
    @RequestMapping(value = "/excel/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportStockRegionAlert(GoodsSectionInventorySearchParams params, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        Assert.isNull(downloadCenterService.queryNoExportFinish(staff, condition), "模块[" + EnumDownloadCenterModule.WMS.getValue() + "]已经有在导出中的任务，请稍后再导出");

        eventCenter.fireEvent(this, new EventInfo("wms.stock.region.alert.export").setArgs(new Object[]{staff, params}), false);
        Map<String, Object> map = new HashMap<>();
        map.put("result", "success");
        return map;
    }
}

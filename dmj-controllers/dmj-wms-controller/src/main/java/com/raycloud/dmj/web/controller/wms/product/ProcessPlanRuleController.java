package com.raycloud.dmj.web.controller.wms.product;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.process.suggest.ProcessPlanRule;
import com.raycloud.dmj.services.wms.product.plan.*;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 加工计划
 **/
@Controller
@RequestMapping("/wms/product/plan/rule")
public class ProcessPlanRuleController extends WmsInitController {

    @Resource
    private IProcessPlanRuleService processPlanRuleService;

    /**
     * 查询
     */
    @RequestMapping(value = "/save")
    @ResponseBody
    public Object savePlanRule(@RequestBody ProcessPlanRule planRule, String api_name) throws Exception {
        Staff staff = getLightStaff();
        return processPlanRuleService.savePlanRule(staff, planRule);
    }

    @RequestMapping(value = "/update")
    @ResponseBody
    public Object updatePlanRule(@RequestBody ProcessPlanRule planRule, String api_name) throws Exception {
        Assert.notNull(planRule.getId(), "加工规则ID不能为空");
        Staff staff = getLightStaff();
        return processPlanRuleService.updatePlanRule(staff, planRule);
    }

    @RequestMapping(value = "/list")
    @ResponseBody
    public Object listPlanRule(ProcessPlanRule planRule, String api_name) throws Exception {
        Staff staff = getLightStaff();
        return processPlanRuleService.listPlanRule(staff, planRule);
    }

    @RequestMapping(value = "/delete")
    @ResponseBody
    public Object deletePlanRule(Long id, String api_name) throws Exception {
        Assert.notNull(id, "加工规则ID不能为空");
        Staff staff = getLightStaff();
        processPlanRuleService.deletePlanRule(staff, id);
        return successResponse();
    }

}

package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.FastInOutStock;
import com.raycloud.dmj.domain.wms.params.FastInOutQueryParams;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.wms.IFastInOutStockService;
import com.raycloud.dmj.services.wms.IProgressService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 快销库存控制器
 * @Date: 2022-01-11 13:32
 * @Author: qingfeng
 */
@RequestMapping("/wms/fast/in/out")
@Controller
@LogTag(value = "wms")
public class FastInOutStockController extends WmsInitController {

    @Resource
    private IFastInOutStockService fastInOutStockService;
    @Autowired
    private IProgressService progressService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IWarehouseService warehouseService;

    /**
     * 快销库存查询页面
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Object query(FastInOutQueryParams params, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.notNull(params.getWarehouseIds(), "仓库不能为空！");
        List<Warehouse> warehouses = warehouseService.queryWarehouseByPower(staff, null);
        if (!staff.isDefaultStaff()) {
            Assert.notEmpty(warehouses, "当前用户无仓库权限！");
        }

        // 仓库权限控制，只会有一个仓库id
        if (!staff.isDefaultStaff()) {
            Set<Long> warehouseIds = warehouses.stream().map(Warehouse::getId).collect(Collectors.toSet());
            if (!warehouseIds.contains(ArrayUtils.toLongList(params.getWarehouseIds()).get(0))) {
                throw new IllegalArgumentException("当前用户无该仓库权限！");
            }
        }
        return fastInOutStockService.queryListPage(staff, params);
    }

    /**
     * 一键清零
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clear", method = RequestMethod.POST)
    @ResponseBody
    public Object clearOccupyNumByCondition(FastInOutQueryParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_FAST_IN_OUT_STOCK_CLEAR), "上次清零还未结束，请稍等！");

        // 如果没勾选，仓库不能为空
        if (StringUtils.isEmpty(params.getIds())) {
            Assert.notNull(params.getWarehouseIds(), "仓库不能为空！");
        }
        eventCenter.fireEvent(this, new EventInfo("fast.in.out.clear").setArgs(new Object[]{staff, params}), null);
        Map<String, Object> result = new HashMap<>();
        result.put("progressType", ProgressEnum.PROGRESS_FAST_IN_OUT_STOCK_CLEAR.getType());
        return result;
    }

    /**
     * 统计快销页面数量
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/count", method = RequestMethod.GET)
    @ResponseBody
    public Object count(FastInOutQueryParams params, String api_name) throws Exception {
        params.setNeedWssStock(false);
        params.setNeedSingleInsufficientNum(false);
        return fastInOutStockService.count(getLightStaff(), params);
    }

    /**
     * 一键重算快销占用数
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/re/calculate", method = RequestMethod.POST)
    @ResponseBody
    public Object reCalculateOccupyNumByCondition(FastInOutQueryParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_FAST_IN_OUT_STOCK_RE_CALCULATE), "上次重算还未结束，请稍等！");

        // 如果没勾选，仓库不能为空
        if (StringUtils.isEmpty(params.getIds())) {
            Assert.notNull(params.getWarehouseIds(), "仓库不能为空！");
        }
        eventCenter.fireEvent(this, new EventInfo("fast.in.out.re.calculate").setArgs(new Object[]{staff, params}), null);
        Map<String, Object> result = Maps.newHashMap();
        result.put("progressType", ProgressEnum.PROGRESS_FAST_IN_OUT_STOCK_RE_CALCULATE.getType());
        return result;
    }

    /**
     * 单个重算
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/re/calculate/single", method = RequestMethod.POST)
    @ResponseBody
    public Object reCalculateSingle(FastInOutQueryParams params, String api_name) throws Exception {
        return fastInOutStockService.reCalculateOccupyNumByCondition(getStaff(), params, null);
    }

    /**
     * 单个快销库存调整
     * @param stock
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/adjust/single", method = RequestMethod.POST)
    @ResponseBody
    public Object adjustSingle(FastInOutStock stock, String api_name) throws Exception {
        fastInOutStockService.adjustSingle(getStaff(), stock);
        return successResponse();
    }
}

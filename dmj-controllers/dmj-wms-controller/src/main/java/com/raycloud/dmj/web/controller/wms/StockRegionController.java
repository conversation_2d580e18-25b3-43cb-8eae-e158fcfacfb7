package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.StockRegion;
import com.raycloud.dmj.services.wms.IStockRegionService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 库区控制器
 * Created by guzy on 15/12/17.
 */
@Controller
@RequestMapping("/wms/stockRegion/")
@LogTag(value = "wms")
public class StockRegionController extends WmsInitController {

    @Resource
    private IStockRegionService stockRegionService;

    /**
     * 根据id查询库区
     */
    @RequestMapping(value = "load", method = RequestMethod.GET)
    @ResponseBody
    public Object findStockRegionById(Long id, String api_name) throws SessionException {
        return stockRegionService.findById(id, getStaff());
    }

    /**
     * 删除库区
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteStockRegion(Long id, String api_name) throws SessionException {
        Staff staff = getStaff();
        StockRegion stockRegion = stockRegionService.deleteStockRegion(staff, id);
        writeOpLog(staff, "deleteStockRegion", String.format("删除货位，名称:%s", stockRegion.getName()), String.format("库区id:%d", id));
        return true;
    }

    /**
     * 修改库区
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @LogTag(key = "#id + ''", content = "'修改库区,名称:'+#name")//,id为'+#id+'
    @ResponseBody
    public Object modifyStockRegion(@RequestParam("id") Long id, @RequestParam("name") String name, Integer bindItemSingle, String api_name, Integer batchMulti, Integer productTimeMulti) throws SessionException {
        StockRegion stockRegion = new StockRegion();
        stockRegion.setId(id);
        stockRegion.setName(name);
        stockRegion.setBindItemSingle(bindItemSingle);
        stockRegion.setBatchMulti(batchMulti);
        stockRegion.setProductTimeMulti(productTimeMulti);
        stockRegionService.updateStockRegion(stockRegion, getStaff());
        return true;
    }

    /**
     * 新增库区
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @LogTag(key = "#warehouseId + ''", content = "'新增库区,名称:'+#name")
    @ResponseBody
    public Object addStockRegion(Long warehouseId, String name, String code, Boolean isMulti,Integer stockRegionType, Integer batchMulti, Integer productTimeMulti,String api_name) throws SessionException {
        if(stockRegionType == null){
            stockRegionType = 1;
        }

        StockRegion stockRegion = new StockRegion();
        stockRegion.setCode(code);
        stockRegion.setName(name);
        if (isMulti == null) {
            isMulti = false;
        }
        stockRegion.setIsMulti(isMulti);
        stockRegion.setWarehouseId(warehouseId);
        stockRegion.setStockRegionType(stockRegionType);
        stockRegion.setBatchMulti(batchMulti == null ? 0 : batchMulti);
        stockRegion.setProductTimeMulti(productTimeMulti == null ? 0 : productTimeMulti);
        stockRegionService.addStockRegion(stockRegion, getStaff(), false);
        return stockRegion.getId();
    }

    /**
     * 根据仓库id查询库区列表
     */
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Object getStockRegionsByWarehouseId(Long warehouseId, String api_name) throws SessionException {
        return stockRegionService.getStockRegionsByWarehouseId(getStaff(), warehouseId);
    }

    /**
     * 查询所有的库区
     */
    @RequestMapping(value = "getStockRegions", method = RequestMethod.GET)
    @ResponseBody
    public Object getStockRegions(Integer warehouseType, Integer stockRegionType, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (!staff.getConf().isOpenWms()) {
            return Collections.emptyList();
        }
        return stockRegionService.getWarehouseVoWithStockRegions(staff, warehouseType, stockRegionType);
    }

    /**
     * 根据变更id分页查询记录
     * @param changeId
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/wave/change/logs")
    @ResponseBody
    public Object queryWaveGoodsChangeLogs(Long changeId, Page page, String api_name) throws Exception {
        Assert.notNull(changeId, "变更记录id不能为空！");

        return stockRegionService.queryWaveGoodsChangeLogs(getLightStaff(), changeId, page);
    }

    /**
     * 根据仓库Id和库区类型查询库区
     * @param warehouseId
     * @param stockRegionType
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/getByStockRegionType", method = RequestMethod.GET)
    @ResponseBody
    public Object getStockRegions(Long warehouseId, Integer stockRegionType, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (!staff.getConf().isOpenWms()) {
            return Collections.emptyList();
        }
        return stockRegionService.getStockRegionsByWarehouseId(staff, warehouseId, stockRegionType);
    }

    /**
     * 根据仓库Id和库区类型查询库区和货位
     * @param warehouseId
     * @param stockRegionType
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/getByGoodsStockRegionType", method = RequestMethod.GET)
    @ResponseBody
    public Object getGoodsStockRegions(Long warehouseId, Integer stockRegionType, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (!staff.getConf().isOpenWms()) {
            return Collections.emptyList();
        }
        return stockRegionService.getStockRegionsByWarehouseIdAndGoodsSection(staff, warehouseId, stockRegionType);
    }
}

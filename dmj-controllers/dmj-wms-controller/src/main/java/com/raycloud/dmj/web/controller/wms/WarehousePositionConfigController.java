package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.PickRegionExpress;
import com.raycloud.dmj.domain.wms.StockRegion;
import com.raycloud.dmj.domain.wms.WarehousePositionConfig;
import com.raycloud.dmj.services.wms.IPickRegionExpressService;
import com.raycloud.dmj.services.wms.IStockRegionService;
import com.raycloud.dmj.services.wms.IWarehousePositionConfigService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.PickRegionExpressParams;
import com.raycloud.dmj.web.model.wms.WarehousePositionConfigParams;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: qingfeng
 * @Description: 唯一码分拣控制器
 * @Date: 2021-01-27 14:33
 */
@Controller
@RequestMapping("/wms/pick")
@LogTag(value = "wms")
public class WarehousePositionConfigController extends WmsInitController {

    @Resource
    private IWarehousePositionConfigService warehousePositionConfigService;
    @Resource
    private IStockRegionService stockRegionService;
    @Resource
    private IPickRegionExpressService pickRegionExpressService;
    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    private static String getProgressKey(Staff staff, ProgressEnum progressEnum) {
        return progressEnum.getKey() + "_" + staff.getCompanyId();
    }

    /**
     * 查询分拣库区
     * @param warehouseType
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/regions/list", method = RequestMethod.GET)
    @ResponseBody
    public Object getPickRegions(Integer warehouseType, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (!staff.getConf().isOpenWms()) {
            return Collections.emptyList();
        }
        return stockRegionService.getWarehouseVoWithStockRegions(staff, warehouseType, 4);
    }

    /**
     * 新增分拣库区
     * @param warehouseId
     * @param name
     * @param code
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/regions/add", method = RequestMethod.POST)
    @ResponseBody
    public Object addPickRegion(Long warehouseId, String name, String code, String api_name) throws SessionException {
        Assert.notNull(warehouseId, "仓库Id不能为空！");
        Assert.notNull(name, "库区名称不能为空！");
        Assert.notNull(code, "库区编码不能为空！");

        List<StockRegion> exist = stockRegionService.query(getStaff(), Lists.newArrayList(warehouseId), 4, Lists.newArrayList(code));
        Assert.isTrue(CollectionUtils.isEmpty(exist), "该仓库下存在相同的库区编号！");
        StockRegion stockRegion = new StockRegion();
        stockRegion.setCode(code);
        stockRegion.setName(name);
        stockRegion.setIsMulti(false);
        stockRegion.setBatchMulti(CommonConstants.JUDGE_YES);
        stockRegion.setProductTimeMulti(CommonConstants.JUDGE_YES);
        stockRegion.setWarehouseId(warehouseId);
        stockRegion.setStockRegionType(4);
        stockRegionService.addStockRegion(stockRegion, getStaff(), false);
        writeOpLog(getStaff(), "addStockRegion", String.format("新增分拣库区，名称:%s", stockRegion.getName()), String.format("库区id:%d", 0));
        return stockRegion.getId();
    }

    /**
     * 删除分拣库区
     * @param id
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/regions/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deletePickRegion(Long id, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(id, "库区Id不能为空！");

        WarehousePositionConfigParams params = new WarehousePositionConfigParams();
        params.setStockRegionIds(Lists.newArrayList(id));
        List<WarehousePositionConfig> positionConfigs = warehousePositionConfigService.queryList(staff, params);
        Assert.isTrue(CollectionUtils.isEmpty(positionConfigs), "该分拣库区下有未删除的分拣货位！");
        StockRegion stockRegion = stockRegionService.deleteStockRegion(staff, id);
        if (stockRegion != null) {
            // 删除绑定的快递模版
            PickRegionExpressParams expressParams = new PickRegionExpressParams();
            expressParams.setWarehouseIds(Lists.newArrayList(stockRegion.getWarehouseId()));
            expressParams.setStockRegionId(stockRegion.getId());
            List<PickRegionExpress> expresses = pickRegionExpressService.queryList(staff, expressParams);
            if (!CollectionUtils.isEmpty(expresses)) {
                pickRegionExpressService.deleteByIds(staff, expresses.stream().map(PickRegionExpress::getId).collect(Collectors.toList()));
            }
        }
        writeOpLog(staff, "deleteStockRegion", String.format("删除分拣库区，名称:%s", stockRegion.getName()), String.format("库区id:%d", id));
        return true;
    }

    /**
     * 修改分拣库区
     * @param id
     * @param name
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/regions/update", method = RequestMethod.POST)
    @ResponseBody
    public Object modifyPickRegion(Long id, String name, String api_name) throws SessionException {
        Assert.notNull(id, "库区Id不能为空！");
        Assert.hasText(name, "库区名称不能为空！");
        StockRegion stockRegion = new StockRegion();
        stockRegion.setId(id);
        stockRegion.setName(name);
        stockRegionService.updateStockRegion(stockRegion, getStaff());
        return true;
    }

    /**
     * 查询分区快递
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/express/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryRegionExpress(PickRegionExpressParams params, String api_name) throws SessionException {
        return pickRegionExpressService.queryPageList(getLightStaff(), params);
    }

    /**
     * 增加分区快递
     * @param ids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/express/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object batchDeleteExpress(String ids, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择要删除的分拣库区快递！");
        }
        List<Long> delIds = Lists.newArrayList();
        for (String id : ids.split(",")) {
            delIds.add(Long.parseLong(id));
        }
        pickRegionExpressService.deleteByIds(staff, delIds);
        return successResponse();
    }

    /**
     * 修改分区快递
     * @param expresses
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/express/save", method = RequestMethod.POST)
    @ResponseBody
    public Object batchSaveWarehousePositionConfigs(@RequestBody PickRegionExpress[] expresses, String api_name) throws SessionException {
        pickRegionExpressService.save(getLightStaff(), Lists.newArrayList(expresses));
        return successResponse();
    }

    /**
     * 增加分区快递
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/express/add", method = RequestMethod.POST)
    @ResponseBody
    public Object batchSaveWarehousePositionConfigs(PickRegionExpressParams params, String api_name) throws SessionException {
        pickRegionExpressService.add(getLightStaff(), params);
        return successResponse();
    }

    /**
     * 生成仓库位置号
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/position/generate", method = RequestMethod.POST)
    @ResponseBody
    public Object generateWarehousePositionConfigs(WarehousePositionConfigParams params, String api_name) throws SessionException {
        Assert.notNull(params, "参数不能为空！");
        warehousePositionConfigService.generate(getLightStaff(), params);
        return successResponse();
    }

    /**
     * 分页查询仓库位置号
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/position/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryWarehousePositionConfigsByPage(WarehousePositionConfigParams params, String api_name) throws SessionException {
        return warehousePositionConfigService.queryPageList(getLightStaff(), params);
    }

    /**
     * 查询分拣货位分组
     */
    @RequestMapping(value = "/position/group/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryPositionGroupList(WarehousePositionConfigParams params, String api_name) throws SessionException {
        return warehousePositionConfigService.queryPositionGroupList(getLightStaff(), params);
    }

    /**
     * 查询分拣库区绑定的模版、店铺
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/stock/region/userId/Template", method = RequestMethod.GET)
    @ResponseBody
    public Object queryStockRegionUserIdAndTemplate(WarehousePositionConfigParams params, String api_name) throws SessionException {
        return warehousePositionConfigService.queryStockRegionUserIdAndTemplate(getLightStaff(), params == null
                ? new ArrayList<>() : params.getWarehouseIdList());
    }

    /**
     * 删除仓库位置号
     * @param ids
     * @param api_name
     * @param force 是否强制删除
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/position/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object batchDeleteWarehousePositionConfigs(String ids, boolean force, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择要删除的位置号！");
        }
        List<Long> delIds = Lists.newArrayList();
        for (String id : ids.split(",")) {
            delIds.add(Long.parseLong(id));
        }
        ProgressData process = wmsCacheBusiness.get(getProgressKey(staff, ProgressEnum.PROGRESS_GENERATE_ORDER_UNIQUE_CODE));
        if (process != null && process.getProgress() != 2) {
            throw new IllegalArgumentException("订单唯一码正在生成，请稍后重试!");
        }
        warehousePositionConfigService.deleteByIds(staff, delIds, force);
        return successResponse();
    }

    /**
     * 保存商品位置号
     * @param configs
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/position/save", method = RequestMethod.POST)
    @ResponseBody
    public Object batchSaveWarehousePositionConfigs(@RequestBody WarehousePositionConfig[] configs, String api_name) throws SessionException {
        Staff staff = getStaff();
        ProgressData progressData = wmsCacheBusiness.get(getProgressKey(staff, ProgressEnum.PROGRESS_GENERATE_ORDER_UNIQUE_CODE));
        if (progressData != null && progressData.getProgress() != 2) {
            throw new IllegalArgumentException("订单唯一码正在生成，请稍后重试!");
        }
        warehousePositionConfigService.save(staff, Lists.newArrayList(configs));
        return successResponse();
    }
}

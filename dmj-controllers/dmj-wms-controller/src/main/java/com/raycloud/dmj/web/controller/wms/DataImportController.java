package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.storage.utils.ExcelConverter;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;

/**
 * Created by fenglingfang on 2019/3/26.
 */
@Controller
@RequestMapping("/wms")
public class DataImportController extends WmsInitController {
    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IFileUploadService fileUploadService;

    /**
     * 采购单excel导入
     *
     * @param file
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = {"/data/import"}, method = RequestMethod.POST)
    @ResponseBody
    public Object importExcel(Boolean withSuit, Long  warehouseId, MultipartFile file, String api_name) throws SessionException, IOException, CacheException {
        Map<String, Object> result = Maps.newHashMap();
        result.put("isSuccess", false);
        if (file == null) {
            result.put("errorMessage", "请选择要导入的excel文件");
            return result;
        }
        //文件后缀名
        String filename = file.getOriginalFilename();
        logger.debug("采购单上传：" + filename);
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        //后缀判断
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix) && !".csv".equals(fileSuffix)) {
            result.put("errorMessage", "文件格式只支持EXL表格与CSV格式");
            return result;
        }

        if (file.getSize() > 2 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于2M");
            return result;
        }
        // 忽略的行数 表头为一行
        Integer ignoreRows = 3;
        //取出excel
        String[][] dataArr;
        try {
            dataArr = ExcelConverter.excel2DataArr(file.getInputStream(), 0);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            result.put("errorMessage", "上传文件内容不符合要求，请下载后重新尝试");
            return result;
        }

        if (dataArr == null || dataArr.length <= ignoreRows) {
            result.put("errorMessage", "导入数据不能为空，请填入数据后重新上传");
            return result;
        } else if (dataArr.length - ignoreRows > 2000) {
            result.put("errorMessage", "每次最多只能导入2000行数据！");
            return result;
        }
        String cacheKey = UUID.randomUUID().toString();
        Staff staff = super.getStaff();

        ImportProcess process = new ImportProcess();
        process.setTotalNum(dataArr.length - ignoreRows);
        process.setCurrNum(0);
        process.setComplete(false);
        cache.set(cacheKey, process);
        FileResult fileResult = fileUploadService.upload(filename, dataArr);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.data.import").setArgs(new Object[]{staff, fileResult.getUrl(), cacheKey, withSuit, warehouseId, ignoreRows}), false);
        result.put("cacheKey", cacheKey);
        result.put("isSuccess", true);

        //记录操作日志
        String content = String.format("Excel批量导入调拨单明细%s, 操作：%s】", filename, staff.getAccountName());
        logger.info(LogHelper.buildLog(staff, content + "参数：" + String.format("staffId:%s", staff.getId())));
        return result;
    }

    /**
     * 返回导入状态
     *
     * @param cacheKey
     * @return
     * @throws CacheException
     */
    @RequestMapping(value = {"/data/import/status"})
    @ResponseBody
    public Object queryImportStatus(String cacheKey) throws CacheException {
        ImportProcess process = cache.get(cacheKey);
        if (process != null && process.isComplete()) {
            process.setErrorNum(process.getTotalNum() - process.getRightNum());
            cache.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }

        return process;
    }

}

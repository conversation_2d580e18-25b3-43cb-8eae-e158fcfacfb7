package com.raycloud.dmj.web.controller.wms;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import javax.annotation.Resource;

import com.raycloud.dmj.domain.stock.StockInOutRecordType;
import com.raycloud.dmj.domain.wms.WmsPageList;
import com.raycloud.dmj.domain.wms.WorkingStorageSectionInOutRecord;
import com.raycloud.dmj.web.interceptors.TbTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionInOutRecordQueryParams;
import com.raycloud.dmj.domain.wms.QueryInOutRecordParams;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.IGoodsSectionInOutRecordService;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInOutRecordVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;


/**
 * 货位操作记录控制器
 * Created by guzy on 15/12/16.
 */
@RequestMapping("/wms/goodsSection/record/")
@Controller
@LogTag(value = "wms")
public class GoodsSectionInOutRecordController extends WmsInitController  {
	
	@Resource
    private IGoodsSectionInOutRecordService goodsSectionInOutRecordService;

    @Resource
    private IDownloadCenterService downloadCenterService;
    
    @Resource
    private IEventCenter eventCenter;
	
	
	
	 /**
     * 货位进出记录查询
     * @Title: query 
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @TbTask(taskId = "KMERP-90710")
    public Object query(@RequestBody GoodsSectionInOutRecordQueryParams params, String api_name) throws Exception {
    	Staff staff = getStaff();
        validateParams(staff, params);
        WmsPageList<GoodsSectionInOutRecordVo> pageList = goodsSectionInOutRecordService.queryInOutRecordPage(staff, params);
        List<GoodsSectionInOutRecordVo> list = pageList.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            GoodsSectionInOutRecordVo lastOne = list.get(list.size() - 1);
            pageList.setLastOneId(lastOne.getId());
        }
        formatGoodsSectionCode(staff, pageList.getList());
        return pageList;
    }

    /**
     * 货位进出记录导出
     */
    @RequestMapping(value = "export", method = RequestMethod.POST)
    @ResponseBody
    public Object export(@RequestBody GoodsSectionInOutRecordQueryParams params, String api_name) throws Exception {
    	Staff staff = getStaff();
        validateParams(staff, params);
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");


        String[] EXCEL_HEADER = new String[]{"序号", "货位", "商品名称", "规格名称" , "商家编码", "箱码", "生产日期", "批次", "质量类型", "操作", "出入库类型", "数量", "仓库", "单据编码", "平台订单号","操作人", "操作时间", "操作详情"};
        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("货位进出明细记录".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("货位进出明细记录数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        Long maxId = goodsSectionInOutRecordService.exportMaxId(staff, params);
        if(null != maxId && maxId > 0) {
            params.setExportMaxId(maxId+1);
        }
        eventCenter.fireEvent(this, new EventInfo("wms.goods.section.record.download.excel").setArgs(new Object[]{staff, param, params}), false);

        return successResponse();
    }
    
    
    private void validateParams(Staff staff,GoodsSectionInOutRecordQueryParams params) throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotEmpty(params.getOperateBeginStr())) {
            params.setOperateBegin(dateFormat.parse(params.getOperateBeginStr() + " 00:00:00"));
        }
        if (StringUtils.isNotBlank(params.getOperateEndStr())) {
            params.setOperateEnd(dateFormat.parse(params.getOperateEndStr() + " 23:59:59"));
        }
        if (StringUtils.isNotEmpty(params.getGoodsSectionCode())) {
        	params.setGoodsSectionCode(WmsUtils.decodeGsCodeSearch(staff, params.getGoodsSectionCode()));
		}
        if (params != null && CollectionUtils.isNotEmpty(params.getTypes())) {
            List<Integer> types = params.getTypes();
            if (types.contains(StockInOutRecordType.PROCESS_ORDER.getIndex())) {
                types.add(StockInOutRecordType.STOCK_PRODUCT.getIndex());
            }
        }
    }

}

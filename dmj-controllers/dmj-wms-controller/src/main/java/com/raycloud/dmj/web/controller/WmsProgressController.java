package com.raycloud.dmj.web.controller;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.services.wms.IProgressService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 进度条控制器
 * <AUTHOR>
 * @date 2019/1/2 18:47
 */
@Controller
@RequestMapping("/wms")
public class WmsProgressController extends Sessionable {
    @Autowired
    private IProgressService progressService;

    /**
     * 进度查询
     */
    @RequestMapping(value = "/progress/query", method = RequestMethod.POST)
    @ResponseBody
    public Object progressQuery(Integer progressType, String api_name) throws SessionException {
        Assert.notNull(progressType,"请输入进度条类型！");
        ProgressEnum progressEnum = ProgressEnum.getProgressEnumByType(progressType);
        Assert.notNull(progressEnum,String.format("找不到对应的进度条类型[progressType=%s]",progressType));
        Staff staff = getStaff();
        ProgressData progressDataResoult=new ProgressData();
        ProgressData progressData = progressService.queryProgress(staff,progressEnum);
        if(progressData == null){
            return progressDataResoult;
        }
        BeanUtils.copyProperties(progressData,progressDataResoult);
        if (progressData.getProgress() == 2) {
            progressService.clearProgress(staff, progressEnum);
        }
        return progressDataResoult;
    }
}

package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.StockWmsBridgeBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.ItemQueryParams;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.AllocateOpType;
import com.raycloud.dmj.domain.wms.product.model.PurchaseReturnWave;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IAllocateDubboService;
import com.raycloud.dmj.services.wms.IAllocateInShelveService;
import com.raycloud.dmj.services.wms.IAllocateOutService;
import com.raycloud.dmj.services.wms.IAllocateTaskService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsAllocateUtils;
import com.raycloud.dmj.utils.WmsOpLogHelper;
import com.raycloud.dmj.web.ResponseDataWrapper;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.exception.ResultBean;
import com.raycloud.dmj.web.idempotent.IdempotentCache;
import com.raycloud.dmj.web.model.wms.AllocateQueryParams;
import com.raycloud.dmj.web.utils.Md5Encrypt;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调拨出库单
 * <AUTHOR>
 * @date 2018/8/16 15:05
 */
@Controller
@RequestMapping("/wms/allocate/out")
@LogTag(value = "wms")
public class AllocateOutController extends WmsInitController {
    
    private static final Logger LOGGER = Logger.getLogger(AllocateOutController.class);

    @Resource
    private IAllocateOutService allocateOutService;
    
    @Resource
    private IAllocateTaskService allocateTaskService;
    
    @Resource
    private ItemsController itemsController;
    
    @Resource
    private IdempotentCache dempotentCache;

    @Resource
    private IAllocateDubboService allocateDubboService;


    /**
     * 调拨出库单列表
     * @param params
     * @param page
     * @param api_name
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(AllocateQueryParams params, Page page, String api_name) throws Exception {
        return allocateOutService.queryPageList(getLightStaff(), params, page);
    }

    /**
     * 调拨出库单明细
     * @param id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/detail")
    @ResponseBody
    public Object detail(Long id, String api_name) throws Exception {
        Assert.notNull(id, "请选择调拨出库单！");
        return allocateOutService.queryById(getLightStaff(), id);
    }

    /**
     * 根据调拨任务id查询调出单据
     * @param taskId
     * @param withDetail
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/task/details")
    @ResponseBody
    public Object queryOutReceiptsByTaskId(Long taskId, Boolean withDetail, String api_name) throws Exception {
        Assert.notNull(taskId, "请选择调拨单！");
        return allocateOutService.queryByTaskId(getLightStaff(), taskId, withDetail);
    }

    /**
     * 修改调拨出库单
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#receipt.code", content = "'修改调拨出库单:' + #receipt.code")
    public Object update(AllocateOutReceipt receipt, String api_name) throws Exception {
        Assert.notNull(receipt.getId(), "请选择调拨单据！");

        allocateOutService.update(getLightStaff(), receipt);
        return successResponse();
    }

    /**
     * 审核调拨出库单
     * @param ids
     * @param codes
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#ids", content = "'审核调拨出库单，单据号：' + #codes")
    public Object audit(String ids, String codes, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨单！");
        Assert.hasText(codes, "请选择调拨单！");

        List<BatchExecuteResult<AllocateOutReceipt>> results = allocateOutService.audit(getLightStaff(), ArrayUtils.toLongList(ids));
        handleBatchResults(results);
        return results;
    }

    /**
     * 批量作废出库单
     * @param ids
     * @param codes
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batch/cancel", method = RequestMethod.POST)
    @ResponseBody
    public Object batchCancel(String ids, String codes, String api_name) throws Exception {
        Assert.hasText(ids, "请选择调拨出库单！");
        Assert.hasText(codes, "请选择调拨出库单！");

        return allocateOutService.batchCancel(getLightStaff(), ArrayUtils.toLongList(ids));
    }
    
    /**
     * 查询商品列表
     */
    @GetMapping(value = "/item/list")
    @ResponseBody
    public Object getItemList(Page page, ItemQueryParams params, Boolean withBatch, Long outId) throws SessionException {
    	Staff staff = getLightStaff();
    	AllocateOutReceipt allocateOutReceipt = allocateOutService.queryById(staff, outId);
    	Assert.notNull(allocateOutReceipt, "请选择单据!");
    	AllocateTask allocateTask = allocateTaskService.queryById(staff, allocateOutReceipt.getTaskId(), true);
    	Assert.notNull(allocateTask, "请选择单据!");
    	List<AllocateTaskDetail> details = allocateTask.getDetails();
    	List<Long> sysItemIds = new  ArrayList<>();
    	List<Long> sysSkuIds = new  ArrayList<>();
    	Map<String,List<String>>  batchNoMap = new HashMap<>();
    	for (AllocateTaskDetail allocateTaskDetail : details) {
    		sysItemIds.add(allocateTaskDetail.getSysItemId());
    		sysItemIds.add(allocateTaskDetail.getSysSkuId());
    		if (StringUtils.isEmpty(allocateTaskDetail.getBatchNo())) {
				continue;
			}
    		List<String> batchNoList = batchNoMap.get(allocateTaskDetail.getSysItemId() + "_" + allocateTaskDetail.getSysSkuId());
    		if (batchNoList == null) {
    			batchNoList = Lists.newArrayList();
    			batchNoList.add(allocateTaskDetail.getBatchNo());
    			batchNoMap.put(allocateTaskDetail.getSysItemId() + "_" + allocateTaskDetail.getSysSkuId(), batchNoList);
			} else {
				batchNoList.add(allocateTaskDetail.getBatchNo());
			}
    	}
    	params.setSysItemIds(sysItemIds);
    	params.setSysSkuIds(sysSkuIds);
    	Object result = itemsController.getItemList(page, params, withBatch);
    	PageList<WmsItem>  pageList = null;
    	if (result instanceof ResponseDataWrapper){
    		ResponseDataWrapper responseDataWrapper = (ResponseDataWrapper)result;
    		pageList = (PageList<WmsItem>)responseDataWrapper.getData();
        }
    	if (result instanceof ResultBean){
    		ResultBean resultBean = (ResultBean)result;
    		pageList = (PageList<WmsItem>)resultBean.getData();
        }
    	List<WmsItem> itemList = pageList.getList();
    	if (CollectionUtils.isEmpty(itemList)) {
			return result;
		}
    	for (WmsItem wmsItem : itemList) {
    		List<WmsItemBatch> batchNos = wmsItem.getBatchNos();
    		if (CollectionUtils.isEmpty(batchNos)) {
				continue;
			}
    		List<String> batchNoList = batchNoMap.get(wmsItem.getSysItemId() + "_" + wmsItem.getSysSkuId());
    		if (CollectionUtils.isEmpty(batchNoList)) {
    			wmsItem.setBatchNos(null);
    			continue;
			}
    		Iterator<WmsItemBatch> iterator = batchNos.iterator();
    		while (iterator.hasNext()) {
    			WmsItemBatch batchItem = iterator.next();
    			if (!batchNoList.contains(batchItem.getBatchNo())) {
    				iterator.remove();
				}
    		}
		}
    	return result;
    }

    
    /**
     * 出库
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/save/out", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#receipt.code", content = "'修改调拨出库单:' + #receipt.code")
    public Object out(@RequestBody AllocateOutReceipt receipt, String api_name) throws Exception {
        Assert.notNull(receipt.getId(), "请选择调拨单据！");
        int expireTime = 10 * 60;
        String cacheKey = null;
        Staff staff = getLightStaff();
        AllocateOutReceipt allocateOutReceipt = allocateOutService.queryById(staff, receipt.getId());
        Assert.notNull(allocateOutReceipt,"无法查到该调拨单据");
        allocateOutReceipt.setContinueCreate(receipt.getContinueCreate());
        boolean isWave = checkLongNotEmpty(allocateOutReceipt.getWaveId());
        if (isWave && CollectionUtils.isEmpty(receipt.getDetails())) {
            receipt = allocateOutReceipt;
            cacheKey = "allocate_out" + "_" + staff.getCompanyId() + "_" + receipt.getId() + "_" + receipt.getWaveId() + "_" + receipt.getPageId();
        } else {
            StringBuilder builder = new StringBuilder();
            Assert.isTrue(CollectionUtils.isNotEmpty(receipt.getDetails()), "详情不能为空");
            for (AllocateOutReceiptDetail detail : receipt.getDetails()) {
                builder.append("_" + detail.getOuterId() + "_" + detail.getActualOutNum());
            }
            cacheKey = "allocate_out" + "_" + staff.getCompanyId() + "_" + receipt.getId() + Md5Encrypt.md5(builder.toString()) + "_" + receipt.getPageId();
        }
        BatchExecuteResult<AllocateOutReceipt> allocate = BatchExecuteResult.buildSuccessResult(1,receipt.getCode(), null);
        LOGGER.debug(LogHelper.buildLog(staff, String.format("调拨出库,调出单code %s,cacheKey：%s", receipt.getCode(), cacheKey)));
        dempotentCache.checkTokenPreEnd(cacheKey,null, expireTime);
        try {
            allocate = allocateOutService.saveAndOutReceipt(staff, receipt);
            if (allocate != null &&  !allocate.isSuccess()){
                dempotentCache.catchPreEnd(cacheKey,null, expireTime);
            }else {
                dempotentCache.tryPreEnd(cacheKey,null, expireTime);
            }
        } catch (Exception e) {
            dempotentCache.catchPreEnd(cacheKey,null, expireTime);
            throw e;
        }
        return allocate;
    }
    
    public static Boolean checkLongNotEmpty(Long value) {
        return value != null && !value.equals(0l) && !value.equals(-1l);
    }
    
    
    /**
     * 调拨收货单生成波次
     * @Title: createWaves
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/create/wave", method = RequestMethod.POST)
    @ResponseBody
    public Object createWaves(@RequestBody AllocateOutReceipt receipt, String api_name) throws SessionException {
        Assert.notNull(receipt, "请选择调拨出库单！");
        String ids = receipt.getIds();
        Boolean allAllocate=receipt.getAllAllocate();
        Assert.hasText(ids, "请选择调拨出库单！");
        Staff lightStaff = getLightStaff();
        PurchaseReturnWave waves = allocateOutService.createWaves(lightStaff, ArrayUtils.toLongList(ids), allAllocate, null);
        List<AllocateOutReceipt> canAllocateOutReceipts = waves.getCanAllocateOutReceipts();
        if(CollectionUtils.isNotEmpty(canAllocateOutReceipts)){
            this.recordAllocateLog(lightStaff,receipt);
        }
        return waves;
        /*if (CollectionUtils.isEmpty(waves)) {
            return allocateOutService.queryByIds(getLightStaff(), ArrayUtils.toLongList(ids), false);
        }
        // 已生成波次的采退单
        List<AllocateOutReceipt> waveReceipts = allocateOutService.getByWaveIds(getStaff(), waves.stream().map(Wave::getId).collect(Collectors.toList()));
        List<Long> waveReceiptIds = waveReceipts.stream().map(AllocateOutReceipt::getId).collect(Collectors.toList());
        // 未生成波次的采退单
        List<Long> unGenReceiptIds = ArrayUtils.toLongList(ids).stream().filter(id -> !waveReceiptIds.contains(id)).collect(Collectors.toList());
        return allocateOutService.queryByIds(getLightStaff(), unGenReceiptIds, false);*/
    }

    private void recordAllocateLog(Staff lightStaff,AllocateOutReceipt receipt) {
        List<AllocateOutReceipt> allocateOutReceipts = allocateDubboService.queryOutReceiptByIds(lightStaff, ArrayUtils.toLongList(receipt.getIds()), Boolean.FALSE);
        if(CollectionUtils.isEmpty(allocateOutReceipts)){
            return;
        }
        allocateOutReceipts.stream().forEach(allocateOutReceipt -> {
            WmsOpLogHelper.recordOpLog(lightStaff, opLogService,
                            AllocateOpType.CREATE_WAVE.getAction(),
                            allocateOutReceipt.getTaskCode(),
                    AllocateOpType.CREATE_WAVE.getName()+"，波次号 "+allocateOutReceipt.getTaskCode()+"::"+allocateOutReceipt.getOutWarehouseName());
        });
    }


}

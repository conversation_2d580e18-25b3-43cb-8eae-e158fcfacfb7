package com.raycloud.dmj.web.controller.wms.product;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.wms.product.StockProductFactory;
import com.raycloud.dmj.domain.wms.product.params.StockProductFactoryParams;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.product.IStockProductFactoryService;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加工工厂管理
 **/
@Controller
@RequestMapping("/wms/product/factory")
public class StockProductFactoryController extends WmsInitController {

    @Resource
    private IStockProductFactoryService stockProductFactoryService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private IDownloadCenterService downloadCenterService;

    /**
     * 新增
     */
    @RequestMapping(value = "/add")
    @ResponseBody
    public Object addStockProductFactory(@RequestBody StockProductFactory factory, String api_name) throws Exception {
        Assert.notNull(factory, "params not null!");
        Staff staff = getLightStaff();
        stockProductFactoryService.addStockProductFactory(staff, factory);
        return successResponse();
    }

    /**
     * 编辑修改
     */
    @RequestMapping(value = "/edit")
    @ResponseBody
    public Object editStockProductFactory(@RequestBody StockProductFactory factory, String api_name) throws Exception {
        Assert.notNull(factory, "params not null!");
        Assert.notNull(factory.getId(), "修改工厂id不能为空");
        Staff staff = getLightStaff();
        stockProductFactoryService.editStockProductFactory(staff, factory);
        return successResponse();
    }

    /**
     * 查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public Object query(@RequestBody StockProductFactoryParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null!");
        Staff staff = getLightStaff();
        return stockProductFactoryService.queryStockProductFactoryList(staff, params);
    }

    /**
     * 删除加工工厂(逻辑删除)
     */
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    @ResponseBody
    public Object deleteStockProductFactory(String idStr, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(idStr), "需要删除的工厂信息不能为空！");
        List<Long> ids = DataUtils.ids2List(idStr);
        Staff staff = getLightStaff();
        stockProductFactoryService.deleteStockProductFactory(staff, ids);
        return successResponse();
    }

    /**
     * 导出
     */
    @RequestMapping(value = "/excel/export" ,method = RequestMethod.POST)
    @ResponseBody
    public Object export(String ids, String api_name) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(ids), "需要导出的工厂信息不能为空！");
        Staff staff = getLightStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        Assert.isNull(downloadCenterService.queryNoExportFinish(staff, condition), "模块【" + EnumDownloadCenterModule.WMS.getValue() + "】已经有在导出中的任务，请稍后再导出!");
        eventCenter.fireEvent(this, new EventInfo("wms.product.factory.excel.export").setArgs(new Object[]{staff, ids}), false);
        Map<String, Object> map = new HashMap<>();
        map.put("result", "success");
        return map;
    }


}

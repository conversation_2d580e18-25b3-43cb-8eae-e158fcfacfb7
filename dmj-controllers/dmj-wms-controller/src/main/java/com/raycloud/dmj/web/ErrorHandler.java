package com.raycloud.dmj.web;

import com.raycloud.dmj.data.DataAccessException;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.tb.common.W2InvalidateException;
import org.apache.log4j.Logger;
import org.springframework.web.bind.annotation.ControllerAdvice;

/**
 * 异常处理拦截器，使用了{@link ControllerAdvice}注解
 *
 * <AUTHOR>
 */
//@ControllerAdvice
public class ErrorHandler {

    private Logger logger = Logger.getLogger(this.getClass());

    /*public Object errorResponse(WebRequest request, Exception e) {
        String apiName = request.getParameter("api_name");
        return errorResponse(apiName, e);
    }*/

    public Object errorResponse(User user, String apiName, Exception e) {
        logger.error(LogHelper.buildErrorLog(user, e, ""), e);
        if (e instanceof DataAccessException) {
            return ResponseDataWrapperBuilder.buildWithApiName(apiName, ErrorInfo.buildSystemError(e.getMessage()));
        } else if (e instanceof SessionException) {
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setError(ErrorInfo.ERROR_LOGIN);
            errorInfo.setMessage(e.getMessage());
            return ResponseDataWrapperBuilder.buildWithApiName(apiName, errorInfo);
        } else if (e instanceof CustomException) {
            CustomException ce = (CustomException) e;
            return ResponseDataWrapperBuilder.build(apiName, ce.getResultCode(), e.getMessage(), ce.getData()).setSubCode(ce.getSubCode());
        } else if (e instanceof W2InvalidateException) {
            return ResponseDataWrapperBuilder
                    .buildWithApiName(apiName, new ErrorInfo(ErrorInfo.ERROR_W2_INVALID, "淘宝短授权失效！"));
        }
        return ResponseDataWrapperBuilder.buildWithApiName(apiName, ErrorInfo.buildSystemError(e.getMessage()));
    }
}

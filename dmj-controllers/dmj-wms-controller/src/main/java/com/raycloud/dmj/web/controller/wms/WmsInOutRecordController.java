package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.stock.message.InOutRecord;
import com.raycloud.dmj.domain.stock.message.InOutRecordParams;
import com.raycloud.dmj.domain.trades.ColumnConf;
import com.raycloud.dmj.domain.trades.ColumnConfListWrapper;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.stock.IInOutMessageService;
import com.raycloud.dmj.services.trades.IColumnConfService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 */
@RequestMapping("/wms/in/out")
@Controller
public class WmsInOutRecordController extends WmsInitController {
    @Resource
    private IInOutMessageService inOutMessageService;
    @Resource
    private WmsItemBusiness wmsItemBusiness;
    @Resource
    private IDownloadCenterService downloadCenterService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IColumnConfService columnConfService;
    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "/query", method = {RequestMethod.POST})
    @ResponseBody
    public Object queryStock(@RequestBody InOutRecordParams params, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        ColumnConfListWrapper columnConfigList = columnConfService.getColumnConfigList(staff, 272L);
        Assert.notNull(columnConfigList, "erp出入库记录没有找到列配置数据,pageId=272");
        List<ColumnConf> columnConfList = columnConfigList.getColumnConfList();
        params.setColumnConfList(columnConfList);
        //checkAndSetQueryItem(staff,params,columnConfList);

        WmsPageList<InOutRecord> inOutRecordPageList = inOutMessageService.queryInOutRecordPage(staff, params);
        List<InOutRecord> list = inOutRecordPageList.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            InOutRecord lastOne = list.get(list.size() - 1);
            inOutRecordPageList.setLastOneId(lastOne.getId());
        }

        return inOutRecordPageList;
    }

    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ResponseBody
    public Object export(@RequestBody InOutRecordParams params, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");
        ColumnConfListWrapper columnConfigList = columnConfService.getColumnConfigList(staff, 272L);
        Assert.notNull(columnConfigList, "erp出入库记录导出没有找到列配置数据,pageId=272");
        List<ColumnConf> columnConfList = columnConfigList.getColumnConfList();
        params.setColumnConfList(columnConfList);

        inOutMessageService.queryItemIdByOuterId(staff,params);
        String[] EXCEL_HEADER;
        List<String> exportList = new ArrayList<>();
        for(ColumnConf conf : columnConfList) {
            if(conf.getVisible().equals(1)) {
                exportList.add(conf.getTitle());
            }
        }
        EXCEL_HEADER = exportList.toArray(new String[0]);
        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("erp出入库记录导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("erp出入库记录数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        params.setNeedSort(1);
        params.setSort(null);
        Long exportMaxId = inOutMessageService.exportMaxId(staff, params);
        if(null != exportMaxId && exportMaxId > 0) {
            params.setExportMaxId(exportMaxId+1);
        }
        eventCenter.fireEvent(this, new EventInfo("wms.in.out.record.download.excel").setArgs(new Object[]{staff, param, params}), false);
        return successResponse();
    }

    private void checkAndSetQueryItem(Staff staff, InOutRecordParams params ,List<ColumnConf> columnConfList) {
        boolean queryItem = false;
        if(StringUtils.isNotEmpty(params.getQueryText())) {
            if(params.getQueryKey().equals("title") || params.getQueryKey().equals("shortTitle") || params.getQueryKey().equals("itemRemark")
                    || params.getQueryKey().equals("skuRemark") || params.getQueryKey().equals("propertiesName") || params.getQueryKey().equals("propertiesAlias")) {
                queryItem = true;
                //根据outerId精确查询商品, 放入sysItemId/sysSkuId
            }
        }
        if (StringUtils.isNotEmpty(params.getOuterId()) || StringUtils.isNotEmpty(params.getSkuOuterId())){
            if (StringUtils.isNotEmpty(params.getOuterId()) && BooleanUtils.isNotTrue(params.getExactOuterId())){
                queryItem = true;
            }
            if (StringUtils.isNotEmpty(params.getSkuOuterId()) && BooleanUtils.isNotTrue(params.getExactSkuOuterId())){
                queryItem = true;
            }
            if ((BooleanUtils.isTrue(params.getExactOuterId()) && StringUtils.isNotEmpty(params.getOuterId()))
                    || (BooleanUtils.isTrue(params.getExactSkuOuterId()) && StringUtils.isNotEmpty(params.getSkuOuterId()))){
                WmsItem wmsItem = wmsItemBusiness.queryWmsItemByOuterIds(staff,params.getOuterId(),params.getExactOuterId(),params.getSkuOuterId(),params.getExactSkuOuterId());
                Optional.ofNullable(wmsItem.getSysItemId()).ifPresent(itemId ->params.setSysItemIds(Lists.newArrayList(itemId)));
                Optional.ofNullable(wmsItem.getSysSkuId()).ifPresent(skuId ->params.setSysSkuIds(Lists.newArrayList(skuId)));
            }
        }
        for(ColumnConf conf : columnConfList) {
            if (Objects.equals(conf.getField(), "itemTitle") || Objects.equals(conf.getField(), "shortTitle")
                    || Objects.equals(conf.getField(), "skuTitle") || Objects.equals(conf.getField(), "outerId")) {
                queryItem = true;
                break;
            }
        }
        params.setQueryItem(queryItem);
    }

}

package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.StockWmsBridgeBusiness;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.dao.wms.AssoGoodsSectionSkuDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.WmsConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.ItemQueryParams;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.WaveSortingDetail;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IReplenishTaskService;
import com.raycloud.dmj.services.wms.IStockRegionAlertService;
import com.raycloud.dmj.services.wms.IWmsConfigService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.allocategoods.IAllocateGoodsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**`
 * 商品控制器
 * Created by guzy on 15/12/23.
 */
@Controller
@RequestMapping("/wms/item")
public class ItemsController extends WmsInitController {

    @Resource
    private WmsItemBusiness wmsItemBusiness;

    @Resource
    private AssoGoodsSectionSkuDao assoGoodsSectionSkuDao;

    @Resource
    private WmsHelpBusiness wmsHelpBusiness;

    @Resource
    private StockWmsBridgeBusiness stockWmsBridgeBusiness;

    @Resource
    private IAllocateGoodsService allocateGoodsService;

    @Resource
    private IReplenishTaskService replenishTaskService;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private IStockRegionAlertService stockRegionAlertService;
    
    @Resource
    private IWmsConfigService wmsConfigService;

    @Resource
    private IWmsService wmsService;

    /**
     * 查询商品列表
     */
    @RequestMapping(value = "list/all", method = RequestMethod.GET)
    @ResponseBody
    public Object getItemList(Page page, ItemQueryParams params, Boolean withBatch) throws SessionException {
        Staff staff = getStaff();
        params.setPage(page);
        if (page.getPageNo() == null) {
            page.setPageNo(1);
        }

        PageList<WmsItem> pageList;
        if (StringUtils.isNotEmpty(params.getOuterId())) {
            pageList = queryByOuterId(staff, params);
        } else {
            params.setActiveStatus(1);
            params.setHasAvailableInStock(1);
            params.setIsVirtual(0);
            pageList = wmsItemBusiness.queryWmsItemPageList(staff, params);
        }

        if (pageList.getList() != null && BooleanUtils.isTrue(withBatch)) {
            fillWithBatchInfo(staff, getItemSkus(pageList.getList()));
        }
        return pageList;
    }

    /**
     * 根据货位信息查询商品批次
     *
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/stock/itemBatch/info", method = RequestMethod.POST)
    @ResponseBody
    public Object queryStockItemBatchs(@RequestBody WmsStockQueryParams params) throws SessionException {
        Staff staff = getLightStaff();
        fillTypeEnum(params);
        return wmsService.queryStock(staff,params,params.getPage());
    }

    private void fillTypeEnum(WmsStockQueryParams params) {
        Integer stockType = params.getStockType();
        if (stockType == null){
            return;
        }
        //库存类型：0：全部、1：仓库库存、2：货位库存、3：拣货暂存区、4：通用暂存区、5：入库暂存区、6：销退暂存区、7：补货暂存区、8:次品暂存区
        if (stockType == 1) {//库库存=货位库存(良次品之和)+拣选暂存区+通用暂存区+补货暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PICK);//拣选暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.COMMON);//通用暂存区
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REPLENISH);//补货暂存区
        } else if (stockType == 3) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PICK);//拣选暂存区
        } else if (stockType == 4) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.COMMON);//通用暂存区
        } else if (stockType == 5) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.PURCHASE);//入库暂存区(采购入库暂存区)
        } else if (stockType == 6) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REFUND);//销退暂存区(销退入库暂存区)
        } else if (stockType == 7) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.REPLENISH);//补货暂存区
        } else if(stockType == 8) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.DEFECTIVE);//次品暂存区
        } else if(stockType == 10) {
            params.setTypeEnums(Lists.newArrayList(WorkingStorageSection.TypeEnum.values()));
        } else if(stockType == 0) {
            params.setTypeEnums(Lists.newArrayList(WorkingStorageSection.TypeEnum.values()));
        } else if(stockType == 11) {
            params.addTypeEnums(WorkingStorageSection.TypeEnum.RETURN);//采退暂存区
        }
    }

    /**
     * 根据货位信息查询商品信息
     *
     * @param params
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/itemBatch/info", method = RequestMethod.POST)
    @ResponseBody
    public Object queryItemBatchs(@RequestBody ItemBatchParams params) throws SessionException {
        Staff staff = getLightStaff();
        return wmsService.queryItemBatchs(staff,params);
    }

    private PageList<WmsItem> queryByOuterId(Staff staff, ItemQueryParams params) {
        WmsItem item = wmsItemBusiness.queryWmsItemByOuterId(staff, params.getOuterId());
        Assert.isTrue("0".equals(item.getType()), "该商品是套件！");
        Assert.isTrue(item.getIsVirtual() == CommonConstants.JUDGE_NO, "该商品是虚拟商品！");
        Assert.isTrue(item.getActiveStatus() == CommonConstants.JUDGE_YES, "该商品已停用！");
        Assert.isTrue(item.getSysSkuId() > 0L || item.getIsSkuItem() == CommonConstants.JUDGE_NO, "该商品含有规格！");
        List<Long> sysItemIds = params.getSysItemIds();
        List<Long> sysSkuIds = params.getSysSkuIds();
        if (CollectionUtils.isNotEmpty(sysItemIds) && CollectionUtils.isNotEmpty(sysSkuIds)) {
        	Assert.isTrue(sysItemIds.contains(item.getSysItemId()) && sysSkuIds.contains(item.getSysSkuId()), "该商品不在选择范围内!");
		}
        PageList<WmsItem> pageList = new PageList<WmsItem>();
        List<WmsItem> wmsItems = Lists.newArrayList(CollectionUtils.isNotEmpty(item.getSkus()) ? item.getSkus().get(0) : item);
        Map<String, FarERPStock> itemKeyStockMap = stockWmsBridgeBusiness.queryItemStocks(staff, params.getWarehouseId(), Lists.newArrayList(item.getSysItemId()), Lists.newArrayList(item.getSysSkuId()));
        if (itemKeyStockMap != null && !itemKeyStockMap.isEmpty()) {
            FarERPStock stock = itemKeyStockMap.get(item.getSysItemId() + "_" + item.getSysSkuId());
            if (stock != null) {
                item.setAvailableInStock(stock.getAvailableStock());
            }
        }

        pageList.setList(wmsItems);
        pageList.setTotal(1L);
        return pageList;
    }

    private List<WmsItem> getItemSkus(List<WmsItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return items;
        }

        List<WmsItem> all = Lists.newArrayListWithCapacity(items.size());
        for (WmsItem item : items) {
            List<WmsItem> skus = item.getSkus();
            if (CollectionUtils.isNotEmpty(skus)) {
                all.addAll(skus);
            } else {
                all.add(item);
            }
        }
        return all;
    }

    /**
     * 填充批次信息
     */
    private void fillWithBatchInfo(Staff staff, List<WmsItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        if (!staff.getConf().isOpenWms()) {
            for (WmsItem item : items) {
                item.setHasBatch(0);
            }
            return;
        }

        List<Long> sysItemIds = Lists.newArrayList();
        List<Long> sysSkuIds = Lists.newArrayList();
        for (WmsItem item : items) {
            if (item.getHasBatch() != null && item.getHasBatch() == CommonConstants.JUDGE_YES) {
                sysItemIds.add(item.getSysItemId());
                sysSkuIds.add(item.getSysSkuId());
            }
        }

        if (!sysItemIds.isEmpty()) {
            Map<String, List<WmsItemBatch>> itemKeyBatchNosMap = wmsHelpBusiness.queryItemBatchNosMap(staff, sysItemIds, sysSkuIds);
            for (WmsItem item : items) {
                item.setBatchNos(itemKeyBatchNosMap.get(item.getSysItemId() + "_" + item.getSysSkuId()));
            }
        }
    }


    /**
     * 仓储商品查询列表
     * withBackAvailNum:true
     * params:货位库存查询参数
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(GoodsSectionInventorySearchParams params, Boolean withBackAvailNum, Page page, String api_name) throws SessionException {
        Assert.notNull(params.getWarehouseId(), "请选择仓库！");

        Staff staff = getLightStaff();
        //对应仓库/公司的货位sku
        Long total = assoGoodsSectionSkuDao.queryAssoItemsCountWithNoBindingGoodsAllocation(staff, params);
        //返回货位商品voList
        PageList<GoodsSectionSkuVo> pageList = new PageList<GoodsSectionSkuVo>();
        pageList.setTotal(total);
        pageList.setPage(page);
        if (total > 0) {
            pageList.setList(assoGoodsSectionSkuDao.queryAssoItemWithNoBindingGoodsAllocationPage(staff, params, page));
            if (BooleanUtils.isTrue(withBackAvailNum)) {
                replenishTaskService.fillManualReplenishData(staff, params.getWarehouseId(), pageList.getList());
            }
        }
        return pageList;
    }


}

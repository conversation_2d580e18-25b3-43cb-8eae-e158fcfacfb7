package com.raycloud.dmj.web.controller.wms;


import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.Trades;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.wms.CheckDetail;
import com.raycloud.dmj.domain.wms.CheckSheet;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.log.OpLogs;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.ICheckService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.WmsOpLogHelper;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.CheckDetailParams;
import com.raycloud.dmj.web.model.wms.CheckSheetParams;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

/**
 * 质检单
 */
@Controller
@LogTag(value="wms")
@RequestMapping("/wms/check")
public class CheckController extends WmsInitController {

    private final static Logger logger = Logger.getLogger(CheckController.class);

    @Resource
    private IOpLogService opLogService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private ICheckService checkService;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;

    @RequestMapping("/oplog/list")
    @ResponseBody
    public Object opLogList(String code, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        Assert.hasText(code, "请选择单据！");
        OpLogs logs = opLogService.list(staff, null, code, null, "wms", null, null, new Page().setPageNo(1).setPageSize(1000), null, null);
        return handlerRecord(logs.getList());
    }

    /**
     * 新增质检单
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/add",method = RequestMethod.POST)
    public Object sheetAdd(@RequestBody CheckSheet checkSheet, String api_name) throws Exception {
        Assert.notNull(checkSheet.getWarehouseId(),"请选择仓库");
        Assert.notNull(checkSheet.getCheckLocation(),"请选择质检位置");
        Assert.notNull(checkSheet.getStatus(),"请传递质检单状态");
        Assert.isTrue(checkSheet.getStatus().equals(CheckSheet.DRAFT),"状态不是草稿");
        Staff staff = getLightStaff();
        checkService.sheetInsert(staff,checkSheet);
        WmsOpLogHelper.recordOpLog(staff, request, opLogService, "addCheckSheet", checkSheet.getCode(), String.format("新增质检单:%s", checkSheet.getCode()));
        return checkSheet;
    }

    /**
     * 批量作废 批量
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/delete",method = RequestMethod.POST)
    public Object sheetDelete(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请传递盘点单id数据");
        Map<String, String> result = checkService.sheetDelete(getLightStaff(), request, ids);
        return MapUtils.isNotEmpty(result) ? result : successResponse();
    }

    /**
     * 草稿修改
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/update",method = RequestMethod.POST)
    public Object sheetUpdate(@RequestBody CheckSheet checkSheet, String api_name) throws Exception {
        Assert.notNull(checkSheet.getWarehouseId(),"请选择仓库");
        Assert.isTrue(checkSheet.getStatus().equals(CheckSheet.DRAFT),"非草稿状态不允许修改");
        Staff staff = getLightStaff();
        CheckSheet sheet = checkService.sheetUpdate(staff, request, checkSheet);
        WmsOpLogHelper.recordOpLog(staff, request, opLogService, "updateCheckSheet", checkSheet.getCode(), String.format("修改质检单:%s", checkSheet.getCode()));
        return sheet;
    }

    /**
     * 分页查询质检单列表
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/list",method = RequestMethod.POST)
    public Object sheetList(@RequestBody CheckSheetParams checkSheetParams, String api_name) throws Exception {
        checkSheetParams.setQueryType(2);
        return checkService.list4CheckSheet(getLightStaff(),checkSheetParams);
    }

    /**
     * 审核质检单
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/audit",method = RequestMethod.POST)
    public Object sheetAudit(@RequestBody List<Long> ids, String api_name) throws Exception {

        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_AUDIT);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行审核, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        Assert.notNull(ids,"请选择质检单");
        try{
            return checkService.sheetAudit(staff, request, ids);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, ("质检单审核失败")), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
    }

    /**
     * 完成前校验
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/checkDetailFinish",method = RequestMethod.POST)
    public Object checkDetailFinish(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请选择质检单");
        return checkService.checkDetailFinish(getLightStaff(), ids);
    }

    /**
     * 完成前校验
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/checkDetailStock",method = RequestMethod.POST)
    public Object checkDetailStock(@RequestBody List<Long> ids, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(ids),"请选择质检单");
        return checkService.checkDetailStock(getLightStaff(), ids, new HashMap<>());
    }

    /**
     * 完成质检单
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/finish",method = RequestMethod.POST)
    public Object sheetFinish(@RequestBody CheckSheet checkSheet,String api_name) throws Exception {
        Assert.isTrue(checkSheet != null,"请选择质检单或者扫描快递单号");
        List<Long> ids =checkSheet.getIds();
        String outSid =checkSheet.getOutSid();
        Assert.isTrue(CollectionUtils.isNotEmpty(ids) || StringUtils.isNotEmpty(outSid) || CollectionUtils.isNotEmpty(checkSheet.getDetailList()),"请选择质检单");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_FINISH);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行完成, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));

        try{// 如果是发货质检 1 新增完成质检单 2 发货 3 库存变更
            if (StringUtils.isNotEmpty(outSid)) {
                return checkService.consignCheck(staff,request,checkSheet);
            }
            if (checkSheet.getId() != null || CollectionUtils.isNotEmpty(checkSheet.getIds())){
                return checkService.sheetFinish(getLightStaff(), request, checkSheet);
            }
            return checkService.addCheckAndFinishByItem(staff, request, checkSheet);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "之间完成操作失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }

    }



    @ResponseBody
    @RequestMapping(value = "/import/status",method = RequestMethod.GET)
    public Object queryImportStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            process.setErrorNum(process.getTotalNum() - process.getRightNum());
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }

    /**
     * 导入
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/import",method = RequestMethod.POST)
    public Object sheetImport(MultipartFile file, Long id, String api_name) throws Exception {
        Assert.notNull(id,"请传质检单id");
        CheckSheetParams params = new CheckSheetParams();
        params.setIds(Lists.newArrayList(id));
        Staff staff = getStaff();
        PageList<CheckSheet> sheetPageList = checkService.list4CheckSheet(staff, params);
        List<CheckSheet> list = sheetPageList.getList();
        if(CollectionUtils.isNotEmpty(list)) {
            CheckSheet checkSheet = list.get(0);
            Assert.isTrue(checkSheet.getStatus().equals(CheckSheet.DRAFT),"质检单不是草稿状态,不允许导入数据");
        }

        Map<String, Object> result = new HashMap<String, Object>();
        //文件校验
        if (file == null) {
            result.put("errorMessage", "请选择要导入的excel文件");
            return result;
        }
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(fileSuffix) && !".xls".equals(fileSuffix) && !".csv".equals(fileSuffix)) {
            result.put("errorMessage", "文件格式只支持EXL表格与CSV格式");
            return result;
        }
        if (file.getSize() > 2 * 1024 * 1024) {
            result.put("errorMessage", "传入的文件不能大于2M");
            return result;
        }

        String[][] data ;
        try {
            data = convertExcelData(file,1);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("errorMessage", "上传文件内容转化数据异常，检查内容是否为空, 请下载后重新尝试");
            return result;
        }

        String importKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_IMPORT);
        ImportProcess process = wmsCacheBusiness.get(importKey);
        if (process != null && !process.isComplete()) {
            throw new IllegalArgumentException("质检单不能同时导入，请稍等！");
        }

        process = new ImportProcess();
        process.setTotalNum(data.length);
        process.setComplete(false);
        wmsCacheBusiness.set(importKey, process, 30 * 60);

        eventCenter.fireEvent(this, new EventInfo("wms.check.sheet.import").setArgs(new Object[]{staff, id}), data);

        result.put("importKey", importKey);
        result.put("isSuccess", true);
        return result;
    }

    /**
     * 导出
     */
    @ResponseBody
    @RequestMapping(value = "/sheet/export",method = RequestMethod.POST)
    public Object sheetExport(@RequestBody CheckSheetParams checkSheetParams, String api_name) throws Exception {
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        String[] EXCEL_HEADER = new String[]{"序号","质检单号","质检仓库","质检状态","质检位置","计划质检总数","待上架数","已上架数","制单时间","制单人","审核时间","审核人",
                "开始质检时间","完成质检时间","备注","订单号","商家编码","商品名称","规格名称","计划质检数","超出质检数","剩余质检数","良品质检数","次品质检数","开始质检时间","完成质检时间",
                "质检进度","质检员","待上架数","已上架数"};
        List<String[]> arrList = new ArrayList<>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("质检单明细数据".getBytes(), "utf-8") + org.apache.commons.lang3.time.DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("质检单明细数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());

        eventCenter.fireEvent(this, new EventInfo("wms.check.sheet.export").setArgs(new Object[]{staff, param, checkSheetParams}), false);

        return successResponse();
    }

    /**
     * ================================================================================================================================================================
     * =========================================================================详情===================================================================================
     * ================================================================================================================================================================
     */

    /**
     * 详情新增
     */
    @ResponseBody
    @RequestMapping(value = "/detail/add",method = RequestMethod.POST)
    public Object detailAdd(@RequestBody List<CheckDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"未传递商品详情数据");
        checkService.detailAdd(getLightStaff(), detailList);
        return successResponse();
    }


    /**
     * 详情删除
     */
    @ResponseBody
    @RequestMapping(value = "/detail/delete",method = RequestMethod.POST)
    public Object detailDelete(@RequestBody CheckDetailParams checkDetailParams, String api_name) throws Exception {
        Assert.notNull(checkDetailParams.getSheetCodes(),"未传递质检单单号");
        Assert.isTrue(CollectionUtils.isNotEmpty(checkDetailParams.getIds()),"未传递商品详情id");
        checkService.detailDelete(getLightStaff(), checkDetailParams);
        return successResponse();
    }

    /**
     * 详情查询
     */
    @ResponseBody
    @RequestMapping(value = "/detail/list",method = RequestMethod.POST)
    public Object detailList(@RequestBody CheckDetailParams checkDetailParams, String api_name) throws Exception {
        if(CollectionUtils.isEmpty(checkDetailParams.getSheetCodes())) {
            logger.debug(LogHelper.buildLog(getLightStaff(), String.format("质检单查询,没有传递质检单单号")));
            return null;
        }
        return checkService.detailList(getLightStaff(), checkDetailParams);
    }

    /**
     * 质检单调整
     */
    @ResponseBody
    @RequestMapping(value = "/detail/adjust",method = RequestMethod.POST)
    public Object sheetAdjust(@RequestBody List<CheckDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"未传递详情数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_DETAIL_BATCH_ADJUST);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行批量质检调整, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        try{
            checkService.sheetDetailAdjust(getLightStaff(), detailList);
            return successResponse();
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "批量质检调整失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
    }

    /**
     * 1:详情修改
     */
    @ResponseBody
    @RequestMapping(value = "/detail/update",method = RequestMethod.POST)
    public Object detailUpdate(@RequestBody CheckDetail checkDetail, String api_name) throws Exception {
        Assert.notNull(checkDetail,"请传入修改详情数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_DETAIL_UPDATE);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行修改详情, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        try{
            checkService.detailUpdate(staff, checkDetail);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "修改详情失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return successResponse();
    }

    /**
     * 2:扫描
     */
    @ResponseBody
    @RequestMapping(value = "/detail/scan",method = RequestMethod.POST)
    public Object detailScan(@RequestBody CheckDetail checkDetail, String api_name) throws Exception {
        Assert.isTrue(null != checkDetail.getOuterId(),"未传递详情outerId数据");
        Assert.isTrue(null != checkDetail.getSheetCode(),"未传递详情质检单数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_DETAIL_SCAN);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行扫描质检, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        try{
            return checkService.detailScan(staff, checkDetail);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "扫描质检失败"), e);
        throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
    }

    /**
     * 3:批量质检
     */
    @ResponseBody
    @RequestMapping(value = "/detail/batchUpdate",method = RequestMethod.POST)
    public Object detailBatchUpdate(@RequestBody List<CheckDetail> detailList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(detailList),"未传递详情数据");
        Staff staff = getLightStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CHECK_SHEET_DETAIL_BATCH_UPDATE);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行批量质检, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        try{
            return checkService.detailBatchUpdate(staff, detailList);
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "批量质检失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
    }


}

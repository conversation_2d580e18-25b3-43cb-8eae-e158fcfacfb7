package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.builder.FarERPStockBuilder;
import com.raycloud.dmj.business.common.StockHelpBusiness;
import com.raycloud.dmj.business.common.StockKey;
import com.raycloud.dmj.business.item.StockProductOrderBusiness;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.GoodsSectionAutoReleaseBusiness;
import com.raycloud.dmj.business.wms.RecommendGoodsSectionBusiness;
import com.raycloud.dmj.dao.stock.StockDAO;
import com.raycloud.dmj.dao.wms.AssoGoodsSectionSkuDao;
import com.raycloud.dmj.dao.wms.GoodsSectionDao;
import com.raycloud.dmj.dao.wms.StockRegionDao;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.params.WorkingStorageGoodsQueryParams;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.ec.trade.Wms2TradeCancelConsignListener;
import com.raycloud.dmj.services.router.DubboEnvManagement;
import com.raycloud.dmj.services.router.OpenWmsHandler;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.*;
import com.raycloud.dmj.services.wms.support.AllCompanyDealer;
import com.raycloud.dmj.services.wms.support.CompanyDealOp;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventoryImportVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.SkuCheckVo;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by guzy on 16/3/8.
 */
@Controller
@RequestMapping("/wms/backend")
@LogTag(value = "wms")
public class Wms1BackendController extends WmsInitController {

    private Logger logger = Logger.getLogger(Wms1BackendController.class);

    @Resource
    private IBackendService backendService;

    @Resource
    private AllCompanyDealer allCompanyDealer;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private AssoGoodsSectionSkuDao assoGoodsSectionSkuDao;

    @Resource
    private IWmsService wmsService;

    @Resource
    private StockDAO stockDAO;

    @Resource
    private GoodsSectionDao goodsSectionDao;

    @Resource
    private WmsItemBusiness wmsItemBusiness;

    @Resource
    private IDepotService depotService;

    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private RecommendGoodsSectionBusiness recommendGoodsSectionBusiness;

    @Resource
    private IWmsConfigService wmsConfigService;

    @Resource
    private Wms2TradeCancelConsignListener wms2TradeCancelConsignListener;
    @Resource
    private IStockRegionZoneService stockRegionZoneService;
    @Resource
    private StockRegionDao stockRegionDao;

    @Resource
    private GoodsSectionAutoReleaseBusiness goodsSectionAutoReleaseBusiness;

    @Resource
    private StockProductOrderBusiness stockProductOrderBusiness;

    @Resource
    private OpenWmsHandler openWmsHandler;

    @Resource
    private DubboEnvManagement dubboEnvManagement;

    @Resource
    private IWarehouseService warehouseService;

    /**
     * 根据公司id清除数据
     */
    @RequestMapping(value = "clearData", method = RequestMethod.POST)
    @ResponseBody
    public Object clearDataByCompanyId() throws SessionException {
        checkIp();
        backendService.clearDataByCompanyId(getStaff());
        return successResponse();
    }

    @RequestMapping(value = "/clearDeleteGoodsSections", method = RequestMethod.POST)
    @ResponseBody
    public Object clearDeleteGoodsSections(Integer dbNo) throws SessionException {
        checkIp();
        Assert.notNull(dbNo, "请选择数据库！");
        backendService.clearDeleteGoodsSections(dbNo);
        return successResponse();
    }

    @RequestMapping(value = "/update/order/records")
    @ResponseBody
    public Object updateGoodsSectionOrderRecords(String ids, Integer lockStatus, Integer stockStatus, Boolean enableStatus, Integer applyNum, Integer getNum, Long goodsSectionId, String goodsSectionCode) throws Exception {
        Assert.hasText(ids, "传入id");
        if (lockStatus == null && stockStatus == null && enableStatus == null) {
            throw new IllegalArgumentException("请选择修改内容");
        }

        Long[] idArr = ArrayUtils.toLongArray(ids, ",");
        List<GoodsSectionOrderRecord> records = Lists.newArrayListWithCapacity(idArr.length);
        for (Long id : idArr) {
            GoodsSectionOrderRecord record = new GoodsSectionOrderRecord();
            record.setId(id);
            record.setLockStatus(lockStatus);
            record.setStockStatus(stockStatus);
            record.setEnableStatus(enableStatus);
            record.setApplyNum(applyNum);
            record.setGetNum(getNum);
            record.setGoodsSectionId(goodsSectionId);
            record.setGoodsSectionCode(goodsSectionCode);
            records.add(record);
        }
        return backendService.updateStockOrderRecords(getLightStaff(), records);
    }


    @RequestMapping("/goods/section/info")
    @ResponseBody
    public Object queryGoodsSection(GoodsSectionInventorySearchParams params) throws Exception {

        return wmsService.queryGoodsSection(getLightStaff(), params);
    }


    @RequestMapping("/pda/wait/checks")
    @ResponseBody
    public Object selectWaitChecks4Pda(GoodsSectionInventorySearchParams params, Page page) throws Exception {
        return wmsService.selectWaitChecks4Pda(params, getLightStaff(), page);
    }


    @RequestMapping("/update/asso")
    @ResponseBody
    public Object updateAssos(@RequestBody AssoGoodsSectionSku asso) throws Exception {
        Assert.isTrue(asso != null && asso.getId() != null, "asso参数不能为空！");

        if (asso.getTotalNum() == null && asso.getLockNum() == null) {
            throw new IllegalArgumentException("修改数据不能为空");
        }

        return assoGoodsSectionSkuDao.updateTargetTotalAndLockNumBatch(getLightStaff(), Lists.newArrayList(asso));
    }


    /**
     * 根据商品的库存总数修改对应的货位库存总数
     */
    @RequestMapping(value = "/fix/assoTotalByStock", method = RequestMethod.POST)
    @ResponseBody
    public Object fixAssoTotalNumWithStock(String itemKeys, Long warehouseId, Boolean fix) throws Exception {
        Assert.hasText(itemKeys, "请传入商品！");
        Assert.notNull(warehouseId, "请选择仓库！");

        String[] itemKeyArr = itemKeys.split(",");
        List<Long> sysItemIds = Lists.newArrayList();
        List<Long> sysSkuIds = Lists.newArrayList();
        for (String itemKey : itemKeyArr) {
            String[] itemSkuIds = itemKey.split("_");
            sysItemIds.add(Long.parseLong(itemSkuIds[0]));
            sysSkuIds.add(Long.parseLong(itemSkuIds[1]));
        }

        if (sysItemIds.isEmpty() && sysSkuIds.isEmpty()) {
            throw new IllegalArgumentException("商品不存在！");
        }
        Staff staff = getLightStaff();

        List<FarERPStock> stocks = FarERPStockBuilder.build4Stocks(stockDAO.queryByThreeIdsList(staff, StockHelpBusiness.getAllWarehouseIds(staff, warehouseId), sysSkuIds, sysItemIds));
        if (CollectionUtils.isEmpty(stocks)) {
            throw new IllegalArgumentException("商品库存不存在！");
        }

        Map<String, FarERPStock> itemTotalStockMap = Maps.newHashMapWithExpectedSize(stocks.size());
        for (FarERPStock stock : stocks) {
            stock.setAvailableStockSum(stock.getAvailableStock() + stock.getLockStock() + stock.getDefectiveStock() + stock.getDepotStock());
            itemTotalStockMap.put(stock.getSysItemId() + "_" + stock.getSysSkuId(), stock);
        }

        AssoGoodsSectionSku condition = new AssoGoodsSectionSku();
        condition.setWarehouseId(warehouseId);
        List<AssoGoodsSectionSku> assos = assoGoodsSectionSkuDao.queryByItemSkuIds(staff, sysItemIds, sysSkuIds, null, null, condition);
        if (CollectionUtils.isEmpty(assos)) {
            throw new IllegalArgumentException("货位库存不存在！");
        }

        List<AssoGoodsSectionSku> modifyList = Lists.newArrayList();
        List<String> errors = Lists.newArrayList();
        List<String> success = Lists.newArrayListWithCapacity(assos.size());
        List<Long> ignoreAssoIds = Lists.newArrayList();
        Map<String, List<AssoGoodsSectionSku>> itemAssoMap = Maps.newHashMap();
        for (AssoGoodsSectionSku asso : assos) {
            String itemKey = asso.getSysItemId() + "_" + asso.getSysSkuId();
            List<AssoGoodsSectionSku> assoList = itemAssoMap.get(itemKey);
            if (assoList == null) {
                assoList = Lists.newArrayList();
            }
            assoList.add(asso);
            itemAssoMap.put(itemKey, assoList);
        }

        List<AssoGoodsSectionSku> filterAssos = Lists.newArrayListWithCapacity(assos.size());
        for (Map.Entry<String, List<AssoGoodsSectionSku>> entry : itemAssoMap.entrySet()) {
            List<AssoGoodsSectionSku> assoList = entry.getValue();
            if (assoList.size() == 1) {
                filterAssos.addAll(entry.getValue());
            } else if (assoList.size() == 2) {
                AssoGoodsSectionSku firstAsso = assoList.get(0);
                AssoGoodsSectionSku secondAsso = assoList.get(1);
                if (!firstAsso.getQualityType().equals(secondAsso.getQualityType())) {
                    filterAssos.addAll(entry.getValue());
                } else {
                    ignoreAssoIds.add(firstAsso.getId());
                    ignoreAssoIds.add(secondAsso.getId());
                }
            } else {
                for (AssoGoodsSectionSku asso : assoList) {
                    ignoreAssoIds.add(asso.getId());
                }
            }
        }

        for (AssoGoodsSectionSku asso : filterAssos) {
            String itemKey = asso.getSysItemId() + "_" + asso.getSysSkuId();
            FarERPStock stock = itemTotalStockMap.get(itemKey);
            if (stock == null) {
                errors.add(String.format("商品[%s_%s]库存记录不存在！", asso.getSysItemId(), asso.getSysSkuId()));
            } else {
                int changeTotalNum = 0;
                if (asso.getQualityType()) {
                    changeTotalNum = (int) (stock.getAvailableStockSum() - stock.getDefectiveStock());
                } else {
                    changeTotalNum = stock.getDefectiveStock().intValue();
                }
                success.add(String.format("修改货位库存id=%s, 货位质量=%s, totalNum=%s -> %s", asso.getId(), asso.getQualityType(), asso.getTotalNum(), changeTotalNum));
                asso.setTotalNum(changeTotalNum);
                modifyList.add(asso);
            }
        }

        if (BooleanUtils.isTrue(fix) && modifyList.size() > 0) {
            assoGoodsSectionSkuDao.updateTargetTotalAndLockNumBatch(staff, modifyList);
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("success", success);
        result.put("errors", errors);
        result.put("ignoreIds", ignoreAssoIds);
        result.put("fix", fix);
        return result;
    }

    /**
     * 发送仓储模块验证事件
     */
    @RequestMapping(value = "/module_validate")
    @ResponseBody
    public Object sendModuleValidate(Boolean openWms) throws SessionException {
        checkIp();
        Assert.notNull(openWms, "openWms不能为空！");

        eventCenter.fireEvent(this, new EventInfo("module.open.validate").setArgs(new Object[]{getStaff(), Domain.WMS.getValue(), openWms ? "enable" : "disable"}), null);

        return "发送模块验证事件，状态：" + (openWms ? "enable" : "disable");
    }

    /**
     * 发送仓储模块开启/关闭事件
     */
    @RequestMapping(value = "/module_open")
    @ResponseBody
    public Object sendModuleOpen(Boolean openWms) throws SessionException {
        checkIp();
        Assert.notNull(openWms, "openWms不能为空！");

        eventCenter.fireEvent(this, new EventInfo("module.open").setArgs(new Object[]{getStaff(), Domain.WMS.getValue(), openWms ? "enable" : "disable"}), null);

        return "发送模块开启/关闭事件，状态：" + (openWms ? "enable" : "disable");
    }


    /**
     * 开启仓储
     * @param companyId
     */
    @RequestMapping(value = "/open/wms", method = RequestMethod.POST)
    @ResponseBody
    public void openWms(Long companyId) {
        Assert.notNull(companyId, "companyId not null!");

        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.notNull(staff, "该公司不存在！");
        openWmsHandler.openWms(staff);
    }


    @RequestMapping(value = "/test/dubbo/env")
    @ResponseBody
    public Object testDubboEnv(Long companyId, String clazz, String method) throws Exception {

        Object bean = dubboEnvManagement.getBean(staffService.queryDefaultStaffByCompanyId(companyId), Class.forName(clazz));
        Assert.notNull(bean, "not found");

        return MethodUtils.getMatchingAccessibleMethod(bean.getClass(), method);
    }

    /**
     * 查询没有关联的待移库任务
     */
    @RequestMapping(value = "/queryNotAssoTask")
    @ResponseBody
    public Object queryNotAssoTask() throws SessionException {

        return backendService.queryNotAssoTransferTasks(getStaff());
    }

    /**
     * 根据id删除移库、调拨任务
     */
    @RequestMapping(value = "/deleteAllocateTaskByIds")
    @ResponseBody
    public Object deleteAllocateTaskByIds(String ids) throws SessionException {
        checkIp();
        if (StringUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("ids不能为空！");
        }

        String[] idArr = ids.split(",");
        List<Long> idList = new ArrayList<Long>(idArr.length);
        for (String id : idArr) {
            idList.add(Long.parseLong(id));
        }
        return backendService.deleteAllocateTaskByIds(getStaff(), idList);
    }


    @RequestMapping("updateGsQtTypes")
    @ResponseBody
    public Object updateGsQtTypes() throws SessionException {
        checkIp();
        backendService.updateGsQtTypes(getStaff());
        return successResponse();
    }


    /*******************  以下的后台修复方法，不区分staff ***********************/

    @RequestMapping("updateAllGsQtTypes")
    @ResponseBody
    public Object updateAllGsQtTypes() {
        checkIp();
        allCompanyDealer.dealWithAllCompanys(new CompanyDealOp() {
            @Override
            public Object deal(Staff staff) {
                backendService.updateGsQtTypes(staff);
                return 1;
            }
        });
        return successResponse();
    }

    @RequestMapping(value = "correctSkuCheckRecords", method = RequestMethod.POST)
    @LogTag(content = "修正盘点记录数据")
    @ResponseBody
    public Object correctSkuCheckRecords(Integer dbNo) {
        checkIp();
        checkDbNo(dbNo);
        backendService.correctSkuCheckRecords(dbNo);
        return successResponse();
    }

    @RequestMapping(value = "correctAssos", method = RequestMethod.POST)
    @LogTag(content = "修正货位商品关联数据")
    @ResponseBody
    public Object correctAssos(Integer dbNo) {
        checkIp();
        backendService.correctAssos(dbNo);
        return successResponse();
    }

    @RequestMapping(value = "/close/depot/tasks", method = RequestMethod.POST)
    @ResponseBody
    public Object closeAllDepotTasks(Long companyId, Boolean closeDepot) throws Exception {
        Assert.notNull(companyId, "companyId not null!");

        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        backendService.closeAllDepotTasks(staff);
        if (BooleanUtils.isTrue(closeDepot)) {
            WmsConfig wmsConfig = wmsConfigService.get(staff);
            wmsConfig.setOpenDepot(false);
            wmsConfigService.save(staff, wmsConfig);
        }
        return successResponse();
    }


    @RequestMapping(value = "/reset/assos", method = RequestMethod.POST)
    @ResponseBody
    public Object resetAssos(@RequestParam(value = "resetDepot", defaultValue = "true") Boolean resetDepot) throws Exception {
        checkIp();
        String path = request.getRequestURL().toString();
        if (path.startsWith(CommonConstants.httpProtocol+"://erp.superboss.cc")) {
            return "无权访问";
        }

        Map<String, Object> result = Maps.newHashMap();
        Staff staff = getLightStaff();
        int count = assoGoodsSectionSkuDao.resetAllNum(staff);
        logger.debug(LogHelper.buildLog(staff, "重置所有的货位数量，共" + count));
        result.put("count", count);

        if (BooleanUtils.isTrue(resetDepot)) {
            //清除返库任务
            depotService.resetDepotTasks(staff);
        }
        return result;
    }

    /**
     * 修改goodsSectionOrderRecord表中的enable_status
     */
    @RequestMapping(value = "correctGoodsSectionOrderRecordStatus", method = RequestMethod.POST)
    @LogTag(content = "修正货位订单锁定记录的锁定状态")
    @ResponseBody
    public Object correctGoodsSectionOrderRecordStatus(Integer dbNo) {
        checkIp();
        checkDbNo(dbNo);
        backendService.correctGoodsSectionOrderRecordStatus(dbNo);
        return successResponse();
    }

    @RequestMapping(value = "/fire/event", method = RequestMethod.POST)
    @ResponseBody
    public Object fireGoodsSectionRetry(String eventName) throws Exception {
        Assert.hasText(eventName, "请输入事件！");

        if ("goods_section.excel_delete".equals(eventName)) {
            eventCenter.fireEvent(this, new EventInfo(eventName), null);
        } else if ("replenish.auto.create".equals(eventName)) {
            eventCenter.fireEvent(this, new EventInfo(eventName), null);
        } else {
            throw new IllegalArgumentException("目前还不支持该事件！");
        }

        return successResponse();
    }


    @RequestMapping(value = "/release/gs", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSection(Long goodsSectionId, Long assoId) throws Exception {
        Assert.notNull(goodsSectionId, "货位id不能为空！");

        Staff staff = getLightStaff();
        int count = 0;
        if (assoId != null) {
            count = assoGoodsSectionSkuDao.deleteByIds(staff, Lists.newArrayList(assoId));
            logger.debug(LogHelper.buildLog(staff, String.format("清除货位商品关联记录，id：%s，结果:%s", assoId, count)));
        }
        GoodsSection goodsSection = new GoodsSection();
        goodsSection.setId(goodsSectionId);
        goodsSection.setIsUsed(false);
        goodsSection.setQualityType(0);
        count = goodsSectionDao.update(staff, goodsSection);
        logger.debug(LogHelper.buildLog(staff, String.format("修改货位记录，id：%s，结果:%s", goodsSectionId, count)));
        return successResponse();
    }

    @RequestMapping("/assos/list")
    @ResponseBody
    public Object queryAssoGoodsSectionSkuPageList(AssoGoodsSectionSku condition, Page page) throws Exception {
        return wmsService.queryAssoGoodsSectionSkuPageList(getLightStaff(), condition, page);
    }


    @RequestMapping("/gs/sku")
    @ResponseBody
    public Object queryGoodsSectionWithSkus(String gsCode) throws Exception {
        return wmsService.queryGoodsSectionWithSkus(getLightStaff(), gsCode, null);
    }

    @RequestMapping(value = "/listForPda")
    @ResponseBody
    public Object listForPda(GoodsSectionInventorySearchParams params, Page page) throws Exception {
        return wmsService.listForPda(params, page, getLightStaff());
    }

    @RequestMapping(value = "/batch/out")
    @ResponseBody
    public Object batchOut(@RequestBody GoodsSectionInventoryImportVo data) throws SessionException {
        if (CollectionUtils.isEmpty(data.getToUpdates())) {
            throw new IllegalArgumentException("出库明细不能为空！");
        }
        return wmsService.batchOut(getLightStaff(), data.getToUpdates(), GoodsSectionInventoryBusiTypeEnum.PURCHASE_OUT);
    }

    @RequestMapping(value = "/goods/section/codes")
    @ResponseBody
    public Object queryGoodsSectionsByCodes(String codes) throws SessionException {
        Assert.hasText(codes, "请选择货位！");
        return wmsService.queryGoodsSectionsByCodes(getLightStaff(), Lists.newArrayList(codes.split(",")));
    }


    @RequestMapping(value = "/batch/check", method = RequestMethod.POST)
    @ResponseBody
    public Object batchCheck(@RequestBody SkuCheckVo[] checkVos) throws Exception {
        Assert.notEmpty(checkVos, "混放盘点信息不能为空！");
        return wmsService.batchCheck(Lists.newArrayList(checkVos), getLightStaff(),false);
    }


    @RequestMapping("/item")
    @ResponseBody
    public Object queryItemForWMs(Long sysItemId, Long sysSkuId) throws SessionException {
        Assert.notNull(sysItemId, "请选择商品！");
        return wmsItemBusiness.getDmjItemAndSku(getLightStaff(), sysItemId, sysSkuId);
    }


    @RequestMapping("/stock")
    @ResponseBody
    public Object queryStock(Long sysItemId, Long sysSkuId, Long warehouseId) throws SessionException {
        Assert.notNull(sysItemId, "请选择商品！");
        Assert.notNull(warehouseId, "请选择仓库！");
        Staff staff = getLightStaff();
        return stockDAO.queryByThreeIdsList(staff, StockHelpBusiness.getAllWarehouseIds(staff, warehouseId), sysSkuId != null ? Lists.newArrayList(sysSkuId) : null, Lists.newArrayList(sysItemId));
    }


    @RequestMapping("/wms/validate")
    @ResponseBody
    public Object wmsOpenValidate(Boolean openWms) throws SessionException {
        Assert.notNull(openWms, "openWms not null");
        Staff staff = getLightStaff();
        return wmsService.wmsOpenValidate(staff, openWms) && tradeServiceDubbo.moduleOpenValidate(staff, openWms ? 1 : 0);
    }

    @RequestMapping("/need/replenish/count")
    @ResponseBody
    public Object queryNeedReplenishItemKindCount(Long warehouseId) throws Exception {
        return wmsService.queryNeedReplenishItemKindCount(getLightStaff(), warehouseId);
    }

    /**
     * 推荐货位
     */
    @RequestMapping("/recommend/goods/section")
    @ResponseBody
    public Object recommendGoodsSection(@RequestBody AssoGoodsSectionSku[] assos) throws Exception {
        Assert.notEmpty(assos, "请选择商品！");
        Long warehouseId = assos[0].getWarehouseId();
        Assert.notNull(warehouseId, "请选择仓库！");

        return recommendGoodsSectionBusiness.recommendGoodsSections(getLightStaff(), warehouseId, Lists.newArrayList(assos));
    }

    @RequestMapping(value = "/customCode/update", method = RequestMethod.POST)
    @ResponseBody
    public Object save(Integer sectionCodeType) throws Exception {
        Assert.notNull(sectionCodeType, "sectionCodeType not null!");
        Staff staff = getStaff();
        if (!staff.getConf().isOpenWms()) {
            throw new IllegalArgumentException("未开启仓储配置，无法设置自定义货位编码!");
        }
        wmsService.updateSectionCodeType(staff,sectionCodeType);
        return successResponse();
    }

    @RequestMapping(value = "/wmsStall/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateStall(Integer stallType) throws Exception {
        Assert.notNull(stallType, "stallType not null!");
        Staff staff = getStaff();
        if (!staff.getConf().isOpenWms()) {
            throw new IllegalArgumentException("未开启仓储配置，无法开启档口配置!");
        }
        wmsService.updateStallType(staff, stallType);
        return successResponse();
    }

    private void checkIp() {
        Assert.isTrue(IpUtils.isSelfVisitor(request), "没有此权限！");
    }

    private void checkDbNo(Integer dbNo) {
        Assert.notNull(dbNo, "请选择数据库！");
    }


    @RequestMapping(value = "/repair/goods/stock", method = RequestMethod.POST)
    @ResponseBody
    public Object repairGoodsStock(String companyIdStr, Boolean incAndDec, String itemKeys) throws Exception {
        Assert.hasText(companyIdStr, "companyIdStr not null!");
        for (Staff staff : allCompanyDealer.getStaffs(getLightStaff(), ArrayUtils.toLongArray(companyIdStr))) {
            List<Long> itemIds = Lists.newArrayList();
            List<Long> skuIds = Lists.newArrayList();
            if (StringUtils.isNotEmpty(itemKeys)) {
                for (String itemId : itemKeys.split(",")) {
                    String[] itemIdArr = itemId.split("_");
                    itemIds.add(Long.parseLong(itemIdArr[0]));
                    skuIds.add(Long.parseLong(itemIdArr[1]));
                }
            }

            backendService.repairGoodsStock(staff, BooleanUtils.isTrue(incAndDec), itemIds, skuIds);
        }
        return successResponse();
    }

    @RequestMapping(value = "/batch/check/goods/stock", method = RequestMethod.POST)
    @ResponseBody
    public Object batchCheckGoodsStock(Long companyId, AssoGoodsSectionSku condition, Long num) throws Exception {
        Assert.notNull(companyId, "companyId not null!");

        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        backendService.batchCheckGoodsStock(staff, condition, num);
        return successResponse();
    }

    @RequestMapping(value = "/cancel/trade/consign")
    @ResponseBody
    public Object cancelTradeConsign(String orderIds) throws Exception {
        Assert.hasText(orderIds, "orderIds not null!");

        wms2TradeCancelConsignListener.handle(getLightStaff(), ArrayUtils.toLongArray(orderIds));
        return successResponse();
    }

    /**
     * 新版本拣货路径支持货位维度,旧数据修复
     */
    @RequestMapping(value = "/pick/route/repair", method = RequestMethod.POST)
    @ResponseBody
    public Object pickRouteRepair(Long companyId) throws SessionException {
        Assert.notNull(companyId, "companyId not null!");
        checkIp();
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        backendService.pickRouteRepair(staff);
        return successResponse();
    }

    @RequestMapping("/working/section/goods")
    @ResponseBody
    public Object queryWorkingSectionGoodsSimple(@RequestBody WorkingStorageGoodsQueryParams params, Page page) throws Exception {
        return wmsService.queryWorkingSectionGoodsSimple(getLightStaff(), params, page);
    }

    /**
     * 修复进出仓数据
     * @param companyIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repair/inOutWarehouse", method = RequestMethod.POST)
    @ResponseBody
    public Object repairInOutWarehouse(String companyIds, String startTime, String endTime) throws Exception {
        Assert.hasText(companyIds, "companyIds不能为空！");
        Assert.notNull(startTime, "开始时间不能为空！");
        Assert.notNull(endTime, "结束时间不能为空！");

        for (Staff staff : allCompanyDealer.getStaffs(getLightStaff(), ArrayUtils.toLongArray(companyIds))) {
            backendService.repairInOutWarehouseStock(staff, startTime, endTime);
            backendService.repairInOutWarehouseWss(staff, startTime, endTime);
        }
        return successResponse();
    }

    @RequestMapping(value = "/openIncludedInTotalStockWss", method = RequestMethod.POST)
    @ResponseBody
    public Object openIncludedInTotalStockWss(String includedInTotalStockWss, String api_name) throws SessionException, InterruptedException {
        Staff staff = getStaff();
        if (includedInTotalStockWss != null) {
            List<String> allTypes = Lists.newArrayList("PURCHASE", "REFUND", "DEFECTIVE");
            List<String> types = Lists.newArrayList(includedInTotalStockWss.split(","));
            CompanyProfile companyProfile = companyService.getCompanyProfile(staff.getCompanyId());
            List<String> needUpdates = Lists.newArrayList();
            if (!(companyProfile == null || companyProfile.getConf() == null || companyProfile.getConf().getConfAttrInfo() == null)) {
                Object ob = companyProfile.getConf().getConfAttrInfo().get("includedInTotalStockWss");
                if (ob != null && StringUtils.isNotEmpty(ob.toString())) {
                    List<String> existTypes = Lists.newArrayList(ob.toString().split(","));
                    allTypes.forEach(a -> {
                        if (!((types.contains(a) && existTypes.contains(a))
                                || (!types.contains(a) && !existTypes.contains(a)))) {
                            needUpdates.add(a);
                        }
                    });
                } else {
                    if (StringUtils.isNotEmpty(includedInTotalStockWss)) needUpdates.addAll(types);
                }
            }
            if (CollectionUtils.isNotEmpty(needUpdates)) {
                wmsService.checkOpenIncludedInTotalStockWss(staff, needUpdates);
                companyService.openIncludedInTotalStockWss(staff, includedInTotalStockWss);
            }
        }
        return successResponse();
    }


    /**
     * 修复商品删除仓内库存还在的记录
     * @param companyIdStr
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repair/item/delete/stock/exist", method = RequestMethod.POST)
    @ResponseBody
    public Object repairItemDeleteGoodsStockExist(String companyIdStr) throws Exception {
        Assert.hasText(companyIdStr, "请选择公司！");
        for (Staff staff : allCompanyDealer.getStaffs(getLightStaff(), ArrayUtils.toLongArray(companyIdStr))) {
            wmsItemDeleteAssoSkuExsitAlertHandler.checkAndClean(staff,true);
        }
        return successResponse();
    }


    /**
     * 修复仓库删除仓内库存还在的记录
     * @param warehouseIdStr
     * @param forceDelete   true 有库存也直接删除   false 有库存先释放库存,再删除
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repair/warehouse/delete/stock/exist", method = RequestMethod.POST)
    @ResponseBody
    public Object repairWarehouseDeleteGoodsStockExist(Long companyId, String warehouseIdStr, Boolean forceDelete) throws Exception {
        Assert.notNull(companyId, "请选择公司！");
        Assert.hasText(warehouseIdStr, "请选择仓库！");
        return allCompanyDealer.dealWithCompanyId(companyId, staff ->  {
            backendService.repairWarehouseDeleteGoodsStockExist(staff, warehouseIdStr, forceDelete);
            return null;
        });
    }

    @RequestMapping(value = "/release/goods/sku", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSectionSku(String assoIds) throws Exception {
        Assert.hasText(assoIds, "请选择货位商品！");
        Staff staff = getLightStaff();
        for (List<Long> subIds : Lists.partition(ArrayUtils.toLongList(assoIds), 200)) {
            logger.debug(LogHelper.buildLog(staff, "释放货位商品，assoIds=" + subIds));
            goodsSectionAutoReleaseBusiness.autoReleaseByIds(staff, subIds);
        }
        return successResponse();
    }

    /**
     * 库区警戒初始化
     * @param companyIdStr
     * @param isAll
     * @param envStrs
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repair/stock/region/zone/init", method = RequestMethod.POST)
    @ResponseBody
    public Object repairColumnConfigStaff(String companyIdStr, Long warehouseId, Boolean isAll, String envStrs) throws Exception {
        Assert.hasText(envStrs, "请选择环境！");
        List<Warehouse> warehouses = new ArrayList<>();
        if (warehouseId != null && warehouseId > 0L) {
            warehouses.add(warehouseService.queryById(warehouseId));
        }
        if (StringUtils.isNotEmpty(companyIdStr)) {
            allCompanyDealer.dealWithCompanyIds(ArrayUtils.toLongList(companyIdStr), staff ->  {
                Long count = stockRegionDao.checkCount(staff, null, 1,warehouseId);
                if (count == null || count <= 0L) {
                    stockRegionZoneService.init(staff, warehouses);
                    logger.debug(LogHelper.buildLog(staff, staff.getCompanyName() + "，库区警戒初始化完成"));
                }
                return "";
            });
        } else {
            allCompanyDealer.dealWithAllCompanys(staff->{
                Long count = stockRegionDao.checkCount(staff, null, 1,warehouseId);
                if (count != null && count > 0L) {
                    return "";
                }
                if (BooleanUtils.isTrue(isAll)) {
                    stockRegionZoneService.init(staff, warehouses);
                } else if (StringUtils.isNotEmpty(envStrs)) {
                    String companyEnvir = companyService.getCompanyEnvir(staff.getCompanyId());
                    List<String> list = ArrayUtils.toStringList(envStrs);
                    if (list != null && list.contains(companyEnvir)) {
                        stockRegionZoneService.init(staff, warehouses);
                        logger.debug(LogHelper.buildLog(staff, staff.getCompanyName() + "，在" + companyEnvir + "环境下，库区警戒初始化完成"));
                    }
                }
                return "";
            });
        }
        return successResponse();
    }

    @RequestMapping(value = "/pick/back")
    @ResponseBody
    public Object unallocateGoods(String sids) throws Exception {
        Assert.hasText(sids, "订单号不能为空！");
        wmsService.unallocateGoods(getStaff(), ArrayUtils.toLongArray(sids));
        return successResponse();
    }

    @RequestMapping(value = "/repair/stock/product/onway", method = RequestMethod.POST)
    @ResponseBody
    public Object repairStockProductOnway(Long warehouseId, String itemKeys) throws Exception {
        Assert.notNull(warehouseId, "请选择仓库");
        Assert.hasText(itemKeys, "请选择货位商品！");
        Staff staff = getLightStaff();


        Set<StockKey> onWayInfos = Sets.newHashSet();
        for (String itemKey : itemKeys.split(",")) {
            String[] itemSku = itemKey.split("_");
            onWayInfos.add(new StockKey(warehouseId, Long.parseLong(itemSku[0]), Long.parseLong(itemSku[1])));
        }
        stockProductOrderBusiness.updateAllocateOnWayQuantity(getLightStaff(), warehouseId, onWayInfos);
        return successResponse();
    }

    @RequestMapping(value = "/query/onway/stock", method = RequestMethod.POST)
    @ResponseBody
    public Object queryOnWayQuantityList(Long warehouseId, String itemKeys, Integer type, Page page) throws Exception {
        Assert.notNull(warehouseId, "请选择仓库");
        Assert.hasText(itemKeys, "请选择货位商品！");
        Staff staff = getLightStaff();

        List<Long> itemIds = Lists.newArrayList();
        List<Long> skuIds = Lists.newArrayList();
        for (String itemKey : itemKeys.split(",")) {
            String[] itemIdArr = itemKey.split("_");
            itemIds.add(Long.parseLong(itemIdArr[0]));
            skuIds.add(Long.parseLong(itemIdArr[1]));
        }

        return backendService.queryOnWayQuantityList(getLightStaff(), warehouseId, itemIds, skuIds, type, page);
    }

}

package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.GoodsSectionInventoryOpLog;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryBusiTypeEnum;
import com.raycloud.dmj.domain.wms.enums.GoodsSectionInventoryOpTypeEnum;
import com.raycloud.dmj.services.wms.IGoodsSectionSkuService;
import com.raycloud.dmj.services.wms.ISkuAllocateService;
import com.raycloud.dmj.services.wms.ISkuTransferService;
import com.raycloud.dmj.services.wms.IWmsStockOpService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.AllocateVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.SkuAllocateTaskVo;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * 调拨任务控制器
 * Created by guzy on 16/3/1.
 */
@Controller
@LogTag(value = "wms")
@RequestMapping("/wms/skuAllocate/")
@AccessShield(value = "1007")
public class SkuAllocateController extends WmsInitController {

    @Resource
    private ISkuAllocateService skuAllocateService;

    @Resource
    private ISkuTransferService skuTransferService;

    @Resource
    private IWmsStockOpService wmsStockOpService;

    @Resource
    private IGoodsSectionSkuService goodsSectionSkuService;

    /**
     * 等待调拨列表
     */
    @ResponseBody
    @RequestMapping(value = "wait/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Object selectWaitList(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<SkuAllocateTaskVo> pageList = skuAllocateService.selectWaitList(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        return pageList;
    }

    /**
     * 调拨完成列表
     */
    @ResponseBody
    @RequestMapping(value = "finish/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Object selectFinishList(GoodsSectionInventorySearchParams params, Page page, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageList<SkuAllocateTaskVo> pageList = skuAllocateService.selectFinishList(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        buildHighlights(pageList.getList(), params.getCode(), params.getQueryType());
        return pageList;
    }

    /**
     * 新增调拨任务
     */
    @ResponseBody
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public Object add(AllocateVo allocateVo, String api_name) throws Exception {
        Staff staff = getStaff();
        if (BooleanUtils.isTrue(allocateVo.getExecuted())) {
            writeOpLog(staff, "executeSkuAllocate", String.format("执行调拨任务,商品标题:%s,商家编码:%s", allocateVo.getTitle(), allocateVo.getOuterId()), JSONObject.toJSONString(allocateVo));
        } else {
            writeOpLog(staff, "addSkuAllocate", String.format("新建调拨任务,商品标题:%s,商家编码:%s", allocateVo.getTitle(), allocateVo.getOuterId()), JSONObject.toJSONString(allocateVo));
        }
        return wmsStockOpService.saveAllocateTask(staff, allocateVo, false);
    }

    /**
     * 修改调拨任务
     */
    @ResponseBody
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @LogTag(key = "#allocateVo.outerId", content = "'修改调拨任务,商品标题:'+#allocateVo.title+',商家编码:'+#allocateVo.outerId")
    public Object update(AllocateVo allocateVo, String api_name) throws Exception {
        return wmsStockOpService.saveAllocateTask(getLightStaff(), allocateVo, true);
    }

    /**
     * 执行调拨任务
     */
    @ResponseBody
    @RequestMapping(value = "execute", method = RequestMethod.POST)
    @LogTag(key = "#allocateVo.outerId", content = "'执行调拨任务,商品标题:'+#allocateVo.title+',商家编码:'+#allocateVo.outerId")
    public Object execute(AllocateVo allocateVo, String api_name) throws Exception {
        allocateVo.setExecuted(true);
        return wmsStockOpService.saveAllocateTask(getLightStaff(), allocateVo, true);
    }

    /**
     * 根据id删除货位调拨任务
     *
     * @param id 移库任务id
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#id+''", content = "'删除调拨任务,商品标题:'+#title+',商家编码:'+#outerId ")
    public Object delete(Long id, String title, String outerId, String api_name) throws SessionException {
        return skuTransferService.delete(getStaff(), id);
    }

    /**
     * 根据ids批量删除货位调拨任务
     *
     * @param ids 批量移库任务ids序号，以逗号分隔
     */
    @RequestMapping(value = "deleteBatch", method = RequestMethod.POST)
    @LogTag(key = "#ids", content = "'批量删除调拨任务,商品标题:'+#titles+',商家编码:'+#outerIds ")
    public @ResponseBody
    Object deleteBatch(String ids, String titles, String outerIds, String api_name) throws SessionException {
        Assert.hasText(ids, "序号不能为空！");
        return skuTransferService.deleteBatch(getStaff(), ids);
    }

    /**
     * 根据id查询调拨任务详情
     */
    @ResponseBody
    @RequestMapping(value = "detail", method = RequestMethod.GET)
    public Object findById(Long id, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        SkuAllocateTaskVo vo = skuAllocateService.findById(getStaff(), id);
        if (vo != null) {
            formatGoodsSectionCode(staff, vo.getFromRecords());
            formatGoodsSectionCode(staff, vo.getToRecords());
        }
        return vo;
    }

    /**
     * 根据调拨任务id查询详情日志列表
     *
     * @param allocateId 调拨任务id
     */
    @ResponseBody
    @RequestMapping(value = "logs", method = {RequestMethod.GET})
    public Object getOpLogs(Long allocateId, String api_name) throws SessionException {
        List<GoodsSectionInventoryOpLog> logs = goodsSectionSkuService.getOpLogs(getStaff(), allocateId, GoodsSectionInventoryBusiTypeEnum.ALLOCATE);
        for (GoodsSectionInventoryOpLog opLog : logs) {
            if (opLog.getOpCode().equals(GoodsSectionInventoryOpTypeEnum.EXECUTE.code)) {
                opLog.setOpName("调拨完成");
            }
        }
        return logs;
    }

}

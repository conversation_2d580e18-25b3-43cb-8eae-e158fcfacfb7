package com.raycloud.dmj.web.controller.wms.product;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.item.StockProductOrderBusiness;
import com.raycloud.dmj.business.item.WmsItemBusiness;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.product.StockProductGoodUniqueCode;
import com.raycloud.dmj.domain.wms.product.StockProductOrder;
import com.raycloud.dmj.domain.wms.product.model.ErrorResult;
import com.raycloud.dmj.domain.wms.product.model.ProductOrderWave;
import com.raycloud.dmj.domain.wms.product.model.StockBatchResult;
import com.raycloud.dmj.domain.wms.product.params.StockProductBarterParams;
import com.raycloud.dmj.domain.wms.product.params.StockProductCheckParams;
import com.raycloud.dmj.domain.wms.product.params.StockProductGoodParams;
import com.raycloud.dmj.domain.wms.product.params.StockProductGoodUniqueCodeParams;
import com.raycloud.dmj.domain.wms.product.params.StockProductOrderParams;
import com.raycloud.dmj.domain.wms.product.vo.StockProductMaterialVo;
import com.raycloud.dmj.domain.wms.product.vo.StockProductOrderVo;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.log.OpLogs;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.product.IStockProductMaterialService;
import com.raycloud.dmj.services.wms.product.IStockProductGoodService;
import com.raycloud.dmj.services.wms.product.IStockProductOrderService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓内加工单
 *
 * <AUTHOR>
 * @Date 2019-09-03
 **/
@Controller
@RequestMapping("/wms/product/order")
public class StockProductOrderController extends WmsInitController {

    @Resource
    private IStockProductOrderService stockProductOrderService;
    @Resource
    private IOpLogService opLogService;
    @Resource
    private IDownloadCenterService downloadCenterService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private StockProductOrderBusiness stockProductOrderBusiness;
    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IStockProductMaterialService stockProductMaterialService;
    @Resource
    private IStockProductGoodService stockProductGoodService;
    @Resource
    private IFileUploadService fileUploadService;


    @Resource
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private WmsItemBusiness wmsItemBusiness;


    /**
     * 加工单日志查询
     *
     * @param code
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/log", method = RequestMethod.GET)
    @ResponseBody
    public Object opLogList(String code, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (StringUtils.isEmpty(code)) {
            throw new IllegalArgumentException("传入需要查询的加工单单据编号！");
        }
        OpLogs logs = opLogService.list(staff, null, code, null, Domain.WMS.getValue(), null, null, new Page().setPageNo(1).setPageSize(100), null, null);
        if (logs == null || CollectionUtils.isEmpty(logs.getList())) {
            return Lists.newArrayList();
        }
        List<OpLog> opLogs = logs.getList().stream().filter(log -> log.getIsError() != 1).collect(Collectors.toList());
        opLogs.forEach(opLog -> opLog.setContent(StringUtils.defaultString(opLog.getContent(), "").split(":")[0]));
        return opLogs;
    }

    /**
     * 查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public Object query(@RequestBody StockProductOrderParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null!");
        Staff staff = getLightStaff();
        return stockProductOrderService.queryStockProductOrder(staff, params);
    }

    /**
     * 新增
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public Object add(@RequestBody StockProductOrderParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null");
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.addStockProductOrder(staff, params);
        params.setPageNo(Page.DEFAULT_PAGE_NUM);
        params.setPageSize(Integer.MAX_VALUE);
        writeOpLog(staff, Domain.WMS, "productOrderAdd", order.getCode(), "创建加工单:" + order.getCode(), null, 0);
        return order;
    }

    @RequestMapping(value = "/add/reverse/order", method = RequestMethod.POST)
    @ResponseBody
    public Object addReverseOrder(@RequestBody StockProductOrderParams params, String api_name) throws Exception {
        Assert.notNull(params, "params not null");
        Assert.notNull(params.getId(), "未传递要生成反向加工的单据id");
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.addReverseStockProductOrder(staff, params);
        writeOpLog(staff, Domain.WMS, "productOrderAdd", order.getCode(), "创建反向加工单:" + order.getCode(), null, 0);
        return order;
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public Object edit(@RequestBody StockProductOrderVo orderVo, String api_name) throws Exception {
        Assert.notNull(orderVo, "order not null!");
        Staff staff = getLightStaff();
        stockProductOrderService.editStockProductOrder(staff, orderVo);
        writeOpLog(staff, Domain.WMS, "productOrderEdit", orderVo.getCode(), "编辑加工单:" + orderVo.getCode(), null, 0);
        return successResponse();
    }

    /**
     * 删除未设置bom表单的商品
     * g
     *
     * @param id       id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/bom/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object bomDelete(Long id, String api_name) throws Exception {
        Assert.notNull(id, "id not null!");
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, id);
        Assert.notNull(order, "根据id[" + id + "]未找到加工单!");
        stockProductOrderService.removeProductOrderNoBomGood(staff, id);
        writeOpLog(staff, Domain.WMS, "productOrderBomDelete", order.getCode(), "删除未设置Bom商品:" + order.getCode(), null, 0);
        return successResponse();
    }

    /**
     * 根据bom添加原材料
     *
     * @param id               id
     * @param productGoodIdStr 加工单成品id集合
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/bom/add", method = RequestMethod.POST)
    @ResponseBody
    public Object bomAdd(Long id, String productGoodIdStr, Boolean isCover, String api_name) throws Exception {
        Assert.notNull(id, "id not null!");
        List<Long> productGoodIds = new ArrayList<>();
        if (StringUtils.isNotBlank(productGoodIdStr)) {
            productGoodIds = Arrays.stream(productGoodIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, id);
        Assert.notNull(order, "添加bom时根据id[" + id + "]未找到加工单!");
        Assert.isTrue(order.getStatus().equals(StockProductOrder.STATUS_WAIT_VERIFY),"加工单状态为非审核，无法根据Bom生成原料明细");
        stockProductOrderService.addProductMaterialByBomGoodParam(staff, id, productGoodIds, isCover);
        writeOpLog(staff, Domain.WMS, "productOrderBomAdd", order.getCode(), "根据Bom生成原料明细:" + order.getCode(), null, 0);
        return successResponse();
    }

    /**
     * 根据bom重新生成原料
     *
     * @param id               id
     * @param productGoodIdStr 加工单成品id集合
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/normal/bom/add", method = RequestMethod.POST)
    @ResponseBody
    public Object normalBomAdd(Long id, String productGoodIdStr, String api_name) throws Exception {
        Assert.notNull(id, "id not null!");
        List<Long> productGoodIds = new ArrayList<>();
        if (StringUtils.isNotBlank(productGoodIdStr)) {
            productGoodIds = Arrays.stream(productGoodIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, id);
        Assert.notNull(order, "添加bom时根据id[" + id + "]未找到加工单!");
        stockProductOrderService.addProductMaterialByNormalBomGood(staff, id, productGoodIds);
        writeOpLog(staff, Domain.WMS, "productOrderBomAdd", order.getCode(), "根据Bom生成原料明细:" + order.getCode(), null, 0);
        return successResponse();
    }

    /**
     * 批量生成加工波次
     *
     * @param orderIds    加工单
     * @param allAllocate 是否要求加工单全部商品配货
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/create/wave", method = RequestMethod.POST)
    @ResponseBody
    public Object createWaves(String orderIds, Boolean allAllocate, String api_name) throws Exception {
        Assert.hasText(orderIds, "请选择加工单！");
        Staff staff = getStaff();
        ProductOrderWave productOrderWave = stockProductOrderService.createWaves(staff, ArrayUtils.toLongList(orderIds), allAllocate);
        Optional.ofNullable(productOrderWave.getCanGenerateWaves()).orElse(Lists.newArrayList()).forEach(vo -> {
            String context;
            if (StockProductOrderVo.TYPE_NORMAL_PROCESS.equals(vo.getType())) {
                context = String.format("%s, 波次号：%s, 加工单号：%s", "按成品分配位置号的波次创建成功", vo.getWaveId(), vo.getCode());
            } else {
                context = String.format("%s, 波次号：%s, 加工单号：%s", (allAllocate ? "生成波次" : "部分配货生成波次"), vo.getWaveId(), vo.getCode());
            }
            writeOpLog(staff, Domain.WMS, "createWave", vo.getCode(), context, null, 0);
        });
        return productOrderWave;
    }

    /**
     * 批量分配加工人员
     *
     * @param idStr       ids
     * @param productId   productId
     * @param productName productName
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/assign/product", method = RequestMethod.POST)
    @ResponseBody
    public Object assignProduct(String idStr, Long productId, String productName, String api_name) throws Exception {
        Assert.hasText(idStr, "idStr not null!");
        Assert.notNull(productId, "productId not null!");
        Assert.hasText(productName, "productName not null!");
        Staff staff = getLightStaff();
        List<Long> ids = Arrays.stream(idStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        StockBatchResult result = stockProductOrderService.assignProductOrderProductIds(staff, ids, productId, productName);
        if (!result.getSuccess().isEmpty()) {
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderAssign", String.valueOf(code), "分配加工人员:" + code, null, 0));
        }
        return result;
    }

    /**
     * 批量审核加工单
     *
     * @param idStr    ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/audit/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object batchAudit(String idStr, String api_name) throws Exception {
        Assert.hasText(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        StockBatchResult result = stockProductOrderService.batchAuditStockProductOrder(staff, ArrayUtils.toLongList(idStr));
        if (!result.getSuccess().isEmpty()) {
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderAudit", String.valueOf(code), "审核加工单:" + code, null, 0));
        }
        return result;
    }

    @RequestMapping(value = "/split", method = RequestMethod.POST)
    @ResponseBody
    public Object split(@RequestBody StockProductOrderParams params, String api_name) throws Exception {
        Staff staff = getStaff();
        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.STOCK_PRODUCT_ORDER_SPLIT);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);
        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行加工单拆分, 请稍后");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, params.getCodes().size()), 10*60);
        try {
            StockBatchResult result = stockProductOrderService.batchSplitProductOrder(staff, params.getCodes());
            if (!result.getSuccess().isEmpty()) {
                result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderSplit", String.valueOf(code), "拆分加工单:" + code, null, 0));
            }
            return result;
        }catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "加工单拆分失败"), e);
            throw e;
        }finally {
            wmsCacheBusiness.delete(cacheKey);
        }
    }

    /**
     * 批量反审核加工单
     *
     * @param idStr    idStr
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/unaudit/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object batchUnAudit(String idStr, String api_name) throws Exception {
        Assert.hasText(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        StockBatchResult result = stockProductOrderService.batchUnAuditStockProductOrder(staff, ArrayUtils.toLongList(idStr));
        if (!result.getSuccess().isEmpty()) {
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderUnAudit", String.valueOf(code), "反审核加工单:" + code, null, 0));
        }
        return result;
    }

    /**
     * 批量关闭加工单
     *
     * @param idStr    idStr
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/closed/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object batchClosed(String idStr, String api_name) throws Exception {
        Assert.hasText(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        StockBatchResult result = stockProductOrderService.batchClosedStockProductOrder(staff, ArrayUtils.toLongList(idStr));
        if (!result.getSuccess().isEmpty()) {
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderClosed", String.valueOf(code), "关闭加工单:" + code, null, 0));
        }
        return result;
    }

    @RequestMapping(value = "/update/stock/region", method = RequestMethod.POST)
    @ResponseBody
    public Object updateStockRegion(@RequestBody StockProductOrderParams orderParams, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(orderParams.getIds()), "没有要修改的单据ids");
        Assert.isTrue(null != orderParams.getStockRegionType(), "没有传递库区类型");
        Assert.isTrue(orderParams.getStockRegionType() == 1 || orderParams.getStockRegionType() == 2 || orderParams.getStockRegionType() == 3, "传递的库区类型有误,不是拣货/备货/次品");
        Staff staff = getLightStaff();
        StockBatchResult result = stockProductOrderService.updateStockRegion(staff,orderParams);
        if (!result.getSuccess().isEmpty()) {
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderUpdateStockRegionType", String.valueOf(code), "设置扣减区加工单:" + code, null, 0));
        }
        return result;
    }


    /**
     * 批量完成
     */
    @RequestMapping(value = "/finished/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object batchFinished(String idStr, boolean autoShelve, String api_name) throws Exception {
        Assert.hasText(idStr, "idStr not null!");
        Staff staff = getLightStaff();
        List<Long> ids = ArrayUtils.toLongList(idStr);

        Map<Long, StockProductOrder> orderMap = stockProductOrderService.getStockProductOrderByIds(staff, ids).stream().collect(
                Collectors.toMap(StockProductOrder::getId, v -> v, (k1, k2) -> k1));
        // 更新分摊成本
        stockProductGoodService.updateStockProductGoodProductAmount(staff, ids,null, true);
        StockBatchResult result = new StockBatchResult();
        for (Long id : ids) {
            StockProductOrder order = orderMap.get(id);
            try {
                stockProductOrderService.finishedStockProductOrderAutoShelve(staff, id, autoShelve);
                result.getSuccess().add(order.getCode());
            } catch (Exception e) {
                result.getErrors().add(new ErrorResult(order.getCode(), e.getMessage()));
            }
        }
        eventCenter.fireEvent(this, new EventInfo("wms.product.update.on.way").setArgs(new Object[]{staff, ids}), null);
        if (!result.getSuccess().isEmpty()) {
            // 更新分摊成本
//            stockProductGoodService.updateStockProductGoodProductAmount(staff, ids,null, true);
            result.getSuccess().forEach(code -> writeOpLog(staff, Domain.WMS, "productOrderFinished", String.valueOf(code), "批量完成加工单:" + code, null, 0));
        }
        return result;
    }

    /**
     * 完成加工单
     *
     * @param id       id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/finished", method = RequestMethod.POST)
    @ResponseBody
    public Object finished(Long id, boolean autoShelve, String api_name) throws Exception {
        Assert.notNull(id, "id not null!");
        Staff staff = getLightStaff();
        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, id);
        Assert.notNull(order, "完成加工单时根据id[" + id + "]未找到加工单!");
        stockProductGoodService.updateStockProductGoodProductAmount(staff, Lists.newArrayList(id),null, true);
        stockProductOrderService.finishedStockProductOrderAutoShelve(staff, id, autoShelve);
        eventCenter.fireEvent(this, new EventInfo("wms.product.update.on.way").setArgs(new Object[]{staff, Lists.newArrayList(id)}), null);
//        stockProductGoodService.updateStockProductGoodProductAmount(staff, Lists.newArrayList(id),null, true);
        writeOpLog(staff, Domain.WMS, "productOrderFinished", order.getCode(), "完成加工单:" + order.getCode(), null, 0);
        return successResponse();
    }

    /**
     * 导出仓内加工单
     */
    @RequestMapping(value = "/excel/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportProductGood(@RequestBody StockProductOrderParams params, String api_name) throws Exception {
        Assert.notNull(params, "stockProductOrderParams not null");
        Assert.notNull(params.getExportType(), "export type not null");
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        Assert.isNull(downloadCenterService.queryNoExportFinish(staff, condition), "模块[" + EnumDownloadCenterModule.WMS.getValue() + "]已经有在导出中的任务，请稍后再导出!");

        eventCenter.fireEvent(this, new EventInfo("wms.product.order.excel.export").setArgs(new Object[]{staff, params}), false);
        Map<String, Object> map = new HashMap<>();
        map.put("result", "success");
        return map;
    }

    /**
     * 退款拦截 根据加工单号进行查询
     */
    @RequestMapping(value = "/refund/intercept", method = RequestMethod.POST)
    @ResponseBody
    public Object refundInterceptByProductGood(@RequestBody StockProductGoodUniqueCodeParams params) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue((StringUtils.isNotEmpty(params.getUniqueCode()) || StringUtils.isNotEmpty(params.getProductOrderCode())), "订单唯一码 和 加工单号不能同时为空");
        return stockProductOrderService.refundInterceptByProductGood(staff, params);
    }


    /**
     * 导出仓内加工单拆分错误excel
     */
    @RequestMapping(value = "/excel/error/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportProductGoodErrorMessage(@RequestBody List<ErrorResult> errorList, String api_name) throws Exception {
        Assert.isTrue(CollectionUtils.isNotEmpty(errorList),"没有传递错误信息");
        Staff staff = getStaff();
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        Assert.isNull(downloadCenterService.queryNoExportFinish(staff, condition), "模块[" + EnumDownloadCenterModule.WMS.getValue() + "]已经有在导出中的任务，请稍后再导出!");

        eventCenter.fireEvent(this, new EventInfo("wms.stock.product.order.error.excel").setArgs(new Object[]{staff, errorList}), false);
        return successResponse();
    }
//    /**
//     * 根据bom添加原材料
//     *
//     * @param id               id
//     * @param productGoodIdStr 加工单成品id集合
//     * @param api_name
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/bom/addAndReplace", method = RequestMethod.POST)
//    @ResponseBody
//    public Object bomAddAndReplace(Long id, String productGoodIdStr, String api_name) throws Exception {
//        Assert.notNull(id, "id not null!");
//        List<Long> productGoodIds = new ArrayList<>();
//        if (StringUtils.isNotBlank(productGoodIdStr)) {
//            productGoodIds = Arrays.stream(productGoodIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
//        }
//        Staff staff = getLightStaff();
//        StockProductOrder order = stockProductOrderService.getStockProductOrderById(staff, id);
//        Assert.notNull(order, "添加bom时根据id[" + id + "]未找到加工单!");
//        stockProductOrderService.addProductMaterialByBomGood(staff, id, productGoodIds);
//        stockProductMaterialService.autoReplaceMaterial(staff,id);
//        writeOpLog(staff, Domain.WMS, "productOrderBomAdd", order.getCode(), "根据Bom生成原料明细并替换:" + order.getCode(), null, 0);
//        return successResponse();
//    }


    @RequestMapping(value = "/getStockProductWaveInfo", method = RequestMethod.GET)
    @ResponseBody
    public Object getStockProductWaveSorting(Long waveId) throws Exception {
        Staff staff = getLightStaff();
        return stockProductOrderService.getStockProductWaveInfo(staff, waveId);
    }

    @RequestMapping(value = "/createByDaily", method = RequestMethod.POST)
    @ResponseBody
    public Object createStockProductOrderByDaily(@RequestBody StockProductOrderVo orderVo, String api_name) throws Exception {
        Assert.notNull(orderVo, "order not null!");
        Staff staff = getLightStaff();
        StockProductOrder stockProductOrder = stockProductOrderService.createStockProductOrderByDaily(staff, orderVo);
        writeOpLog(staff, Domain.WMS, "productOrderEdit", orderVo.getCode(), "印花更新加工单:" + stockProductOrder.getCode(), null, 0);
        return successResponse();
    }

    @RequestMapping(value = "/updateByDaily", method = RequestMethod.POST)
    @ResponseBody
    public Object updateStockProductOrderByDaily(@RequestBody StockProductOrderVo orderVo, String api_name) throws Exception {
        Assert.notNull(orderVo, "order not null!");
        Staff staff = getLightStaff();
        stockProductOrderService.updateStockProductOrderByDaily(staff, orderVo);
        writeOpLog(staff, Domain.WMS, "productOrderEdit", orderVo.getCode(), "标准加工登记差异数:" + orderVo.getCode(), null, 0);
        return successResponse();
    }

    @RequestMapping(value = "/listProductUniqueCode", method = RequestMethod.POST)
    @ResponseBody
    public Object listProductUniqueCode(@RequestBody StockProductGoodUniqueCodeParams uniqueCodeParams, String api_name) throws Exception {
        Assert.notNull(uniqueCodeParams.getProductOrderId(), "加工单ID不能为空！");
        Staff staff = getLightStaff();
        return stockProductOrderService.listProductUniqueCode(staff, uniqueCodeParams);
    }

    @RequestMapping(value = "/updateProductUniqueCode", method = RequestMethod.POST)
    @ResponseBody
    public Object updateProductUniqueCode(@RequestBody StockProductGoodUniqueCode uniqueCode, String api_name) throws Exception {
        Assert.notNull(uniqueCode.getId(), "ID不能为空！");
        Staff staff = getLightStaff();
        stockProductOrderService.updateProductUniqueCode(staff, uniqueCode);
        return successResponse();
    }

    /**
     *
     * @param checkParams
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkAndQueryGoodItem", method = RequestMethod.GET)
    @ResponseBody
    public Object checkAndQueryGoodItem(StockProductCheckParams checkParams, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.notNull(checkParams, "checkParams is null ！");
        Assert.notNull(checkParams.getType(), "请传入唯一码类型");
        if (StockProductOrder.TYPE_NORMAL_PROCESS.equals(checkParams.getProductType())) {
            Assert.notNull(checkParams.getStockProductOrderCode(), "请先输入正确的加工单号！");
            Assert.notNull(checkParams.getCheckWay(), "请选择登记方式！");
        }
        return stockProductOrderService.checkAndQueryGoodItem(staff, checkParams);
    }

    /**
     * 计算分摊加工费
     *
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/calculate", method = RequestMethod.GET)
    @ResponseBody
    public Object calculate(Long stockProductOrderId, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        if (stockProductOrderId == null) {
            throw new IllegalArgumentException("加工单id是空！");
        }
        return stockProductOrderService.calculateProductFee(staff, stockProductOrderId,null);
    }

    /**
     * 创建加工单并直接完成
     */
    @RequestMapping(value = "/createStockProductOrderWithStatus", method = RequestMethod.POST)
    @ResponseBody
    public Object createStockProductOrderWithStatus(@RequestBody StockProductOrderVo stockProductOrderVo, String api_name) throws Exception {
        Staff staff = getStaff();
        stockProductOrderService.createStockProductOrderWithStatus(staff, stockProductOrderVo);
        return successResponse();
    }


    /**
     * 仓内加工导入加工单
     * 注释：目前只支持加工和反加工 类型加工单
     */
    @RequestMapping(value = "/excel/import", method = RequestMethod.POST)
    @ResponseBody
    public Object exportProductGoodImport(MultipartFile file, String api_name) throws Exception {
        checkBeforeImport(file);
        String[][] data = convertExcelData(file,0);
        Staff staff = getStaff();

        String importToken = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_PRODUCT_ORDER);
        ImportProcess preProcess = wmsCacheBusiness.get(importToken);

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在货位导入，请稍候！");
        }

        wmsCacheBusiness.set(importToken, WmsProcessUtils.buildBaseProcess(importToken, (data.length - 2)));
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.stock.product.import").setArgs(new Object[]{staff}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importToken);
        return result;
    }


    /**
     * 改码打标查询
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/barter/print/query", method = RequestMethod.POST)
    @ResponseBody
    public Object barterPrintQuery(@RequestBody StockProductBarterParams params, String api_name) throws Exception {
        Staff staff = getLightStaff();
        return stockProductOrderService.barterPrintQuery(staff, params);
    }


    /**
     * 改码打标更新---增量
     * @param stockProductMaterialVos
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/barter/print/save", method = RequestMethod.POST)
    @ResponseBody
    public Object barterPrintSave(@RequestBody List<StockProductMaterialVo> stockProductMaterialVos, String api_name) throws Exception {
        Staff staff = getLightStaff();
        stockProductGoodService.updatePrintBomRatio(staff, stockProductMaterialVos);
        return successResponse();
    }

    /**
     * 导入进度条查询
     *
     * @param cacheKey
     * @return
     */
    @RequestMapping(value = "/excel/import/status", method = RequestMethod.GET)
    @ResponseBody
    public Object exportImportStatus(String cacheKey) {
        ImportProcess process = wmsCacheBusiness.get(cacheKey);
        if (process != null && process.isComplete()) {
            wmsCacheBusiness.delete(cacheKey);
        } else if (process == null) {
            process = new ImportProcess();
        }
        return process;
    }

    protected void checkBeforeImport(MultipartFile file) {
        if (file == null) {
            throw new IllegalArgumentException("请选择要导入的excel文件");
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("上传的excel文件大小不能超过5MB");
        }

        String filename = file.getOriginalFilename();
        if (! (filename.endsWith(".xls") || filename.endsWith(".xlsx") || filename.endsWith(".csv"))) {
            throw new IllegalArgumentException("文件格式只支持EXL表格与CSV格式");
        }
    }
}

package com.raycloud.dmj.web.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * Created by guzy on 17/9/25.
 */
public class IbatisTagListener implements ServletContextListener {
    public IbatisTagListener() {
    }

    public void contextInitialized(ServletContextEvent servletContextEvent) {
        try {
            Class.forName("com.raycloud.ibatis.sqltag.EncodeTagHandler");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    public void contextDestroyed(ServletContextEvent servletContextEvent) {
    }
}

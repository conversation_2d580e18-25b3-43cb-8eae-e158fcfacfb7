package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.wms.params.GoodsSectionOrderRecordParams;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.IWmsOrderService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.GoodsSectionOrderRecordVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by guzy on 17/2/27.
 */
@Controller
@RequestMapping("/wms/goodsSectionLock")
@LogTag(value = "wms")
@AccessShield(value = "1009")
public class GoodsSectionLockController extends WmsInitController {

    @Resource
    private IWmsOrderService wmsOrderService;

    @Resource
    private IDownloadCenterService downloadCenterService;
    @Resource
    private IEventCenter eventCenter;



    /**
     * 锁定记录列表
     * @param goodsSectionOrderRecord
     * @param operateTimeBegin
     * @param operateTimeEnd
     * @param queryType 关键字匹配类型
     * @param text 关键字
     * @param page
     * @param api_name
     * @return
     * @throws ParseException
     * @throws SessionException
     */
    @ResponseBody
    @RequestMapping("/list")
    public Object orderLockList(GoodsSectionOrderRecordParams params, Page page, String api_name) throws ParseException, SessionException {
        if(page==null){
            page=new Page().setPageNo(1).setPageSize(20);
        }
        Staff staff = getLightStaff();
        return queryList(staff, params, page);
    }

    /**
     * 锁定记录列表导出
     * @param goodsSectionOrderRecord
     * @param operateTimeBegin
     * @param operateTimeEnd
     * @param queryType 关键字匹配类型
     * @param text 关键字
     * @param api_name
     * @return
     * @throws ParseException
     * @throws SessionException
     */
    @ResponseBody
    @RequestMapping("/export")
    public Object exportList(GoodsSectionOrderRecordParams params, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff,condition);
        if(downloadCenterOld!=null){
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        String[] EXCEL_HEADER = new String[]{"序号","订单号","商家编码","商品名称/规格别名","商品质量","仓库","锁定货位","申请数量","分配数量","锁定时间","生产日期","批次","订单状态","波次号","锁定记录变更时间","记录状态"};
        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);

        //导出Excel
        String fileName = new String("货位锁定记录".getBytes(),"utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss")+ ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("货位锁定记录");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo("goodsSection.lock.download.excel").setArgs(new Object[]{staff,param,params}), false);

        Map<String,Object> map = new HashMap<String,Object>();
        map.put("result","success");
        return map;
    }

    private PageList<GoodsSectionOrderRecordVo> queryList(Staff staff,GoodsSectionOrderRecordParams params, Page page) throws ParseException {
        GoodsSectionOrderRecord goodsSectionOrderRecord = new GoodsSectionOrderRecord();
        if(StringUtils.isNotBlank(params.getText())){
            if("orderId".equals(params.getQueryType())){
                goodsSectionOrderRecord.setOrderId(Long.parseLong(params.getText().trim()));
            }else{
                String text="%"+params.getText().trim()+"%";
                if(StringUtils.isBlank(params.getQueryType())){
                    throw new IllegalArgumentException("关键字匹配类型不能为空！");
                }
                if("itemName".equals(params.getQueryType())){
                    goodsSectionOrderRecord.setTitle(text);
                }else if("outerId".equals(params.getQueryType())){
                    goodsSectionOrderRecord.setOuterId(text);
                }else if("goodsSectionCode".equals(params.getQueryType())){
                    goodsSectionOrderRecord.setGoodsSectionCode(WmsUtils.decodeGsCodeSearch(staff, text));
                }else if("sid".equals(params.getQueryType())){
                    goodsSectionOrderRecord.setSid(text);
                }else if("shortTitle".equals(params.getQueryType())){
                    goodsSectionOrderRecord.setShortTitle(text);
                }
            }
        }
        goodsSectionOrderRecord.setWarehouseId(params.getWarehouseId());
        params.setOrderRecord(goodsSectionOrderRecord);
        PageList<GoodsSectionOrderRecordVo> pageList = wmsOrderService.queryOrderRecords(staff, params, page);
        formatGoodsSectionCode(staff, pageList.getList());
        if (!CollectionUtils.isEmpty(pageList.getList())){
            for (GoodsSectionOrderRecordVo vo : pageList.getList()) {
                if (vo.getWaveId() != null && vo.getWaveId() == 0L){
                    vo.setWaveId(null);
                }
            }
        }
        return pageList;
    }


    /**
     * 校验子订单的货位锁定记录
     *
     * @param sids 系统订单号
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping("/check/goods/order/records")
    @ResponseBody
    public Object checkGoodsOrderRecords(String sids, String api_name) throws SessionException {
        Assert.hasText(sids, "请选择订单！");

        Staff staff = getLightStaff();
        if (staff.getConf().isOpenWms()) {
            wmsOrderService.checkGoodsOrderRecords(staff, ArrayUtils.toLongList(sids));
        }

        return successResponse();
    }

}

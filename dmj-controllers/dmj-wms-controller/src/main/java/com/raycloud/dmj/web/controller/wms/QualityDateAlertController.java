package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.business.wms.QualityDateAlertBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.QualityDateAlert;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.model.wms.QualityDateAlertParams;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 保质期提醒控制器
 * Created by yangheng on 17/12/28.
 */
@RequestMapping("/wms/quality")
@Controller
@LogTag(value = "wms")
public class QualityDateAlertController extends WmsInitController {

    @Resource
    QualityDateAlertBusiness qualityDateAlertBusiness;

    @Resource
    IDownloadCenterService downloadCenterService;

    @Resource
    IEventCenter eventCenter;

    /**
     * @param qualityDateAlert
     * @param page
     * @param api_name
     * @return
     * @throws SessionException
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Object queryGoodsSectionPageList(QualityDateAlert qualityDateAlert, QualityDateAlertParams params, Page page, String api_name) throws SessionException {
        return qualityDateAlertBusiness.query(getLightStaff(), qualityDateAlert, params, page);
    }

    /**
     * 保质期提醒导出
     *
     * @param qualityDateAlert
     * @param api_name
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/export")
    public Object exportList(QualityDateAlert qualityDateAlert, QualityDateAlertParams queryParams,String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        String[] EXCEL_HEADER = new String[]{"序号", "商家编码", "商品名称", "规格属性", "库存", "效期状态", "生产日期","批次","货位", "保质期（天）","临保时长（天）", "过期日期","临保日期", "剩余过期天数", "商品临保期是否允许销售"};
        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);

        //导出Excel
        String fileName = new String("保质期提醒".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("保质期提醒");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo("quality.date.alert.download.excel").setArgs(new Object[]{staff, param, qualityDateAlert, queryParams}), false);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("result", "success");
        return map;
    }
}

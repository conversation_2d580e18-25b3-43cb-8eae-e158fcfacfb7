package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.stock.message.InOutRecord;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.QueryInOutRecordParams;
import com.raycloud.dmj.domain.wms.WmsPageList;
import com.raycloud.dmj.domain.wms.WorkingStorageSectionInOutRecord;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.wms.storagesection.IWorkingStorageSectionManageService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 18/12/21
 */
@RequestMapping("/wms/section/record")
@Controller
public class WmsSectionInOutRecordController extends WmsInitController {

    @Resource
    private IWorkingStorageSectionManageService workingStorageSectionManageService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IEventCenter eventCenter;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    public Object query(@RequestBody QueryInOutRecordParams params, String api_name) throws Exception {
        validateParams(params);
        WmsPageList<WorkingStorageSectionInOutRecord> workingStorageSectionInOutRecordPageList = workingStorageSectionManageService.queryInOutRecordPage(getStaff(), params);
        List<WorkingStorageSectionInOutRecord> list = workingStorageSectionInOutRecordPageList.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            WorkingStorageSectionInOutRecord lastOne = list.get(list.size() - 1);
            workingStorageSectionInOutRecordPageList.setLastOneId(lastOne.getId());
        }
        return workingStorageSectionInOutRecordPageList;
    }

    /**
     * 查询货位商品数据
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ResponseBody
    public Object export(@RequestBody QueryInOutRecordParams params, String api_name) throws Exception {
        validateParams(params);
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        Assert.isNull(downloadCenterOld, "该模块已经有在导出中的任务，请稍后再导出");


        String[] EXCEL_HEADER = new String[]{"序号", "暂存区类型", "商品名称", "规格名称", "商家编码", "箱码", "生产日期","批次", "数量", "仓库", "操作人", "操作时间", "操作类型", "出入库类型","单据编号", "平台订单号", "操作详情"};
        List<String[]> arrList = new ArrayList<String[]>();
        arrList.add(EXCEL_HEADER);
        String[][] excelHeader = arrList.toArray(new String[arrList.size()][]);
        String fileName = new String("暂存区进出记录导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("暂存区进出记录数据");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        params.setNeedSort(1);
        Long exportMaxId = workingStorageSectionManageService.exportMaxId(staff, params);
        if(null != exportMaxId && exportMaxId > 0) {
            params.setExportMaxId(exportMaxId+1);
        }
        eventCenter.fireEvent(this, new EventInfo("wms.section.record.download.excel").setArgs(new Object[]{staff, param, params}), false);

        return successResponse();
    }

    private void validateParams(QueryInOutRecordParams params) throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotEmpty(params.getOperateBeginStr())) {
            params.setOperateBegin(dateFormat.parse(params.getOperateBeginStr() + " 00:00:00"));
        }
        if (StringUtils.isNotBlank(params.getOperateEndStr())) {
            params.setOperateEnd(dateFormat.parse(params.getOperateEndStr() + " 23:59:59"));
        }

        String sectionTypeStr = params.getSectionTypeStr();
        if (StringUtils.isNotEmpty(sectionTypeStr)) {
            params.setSectionTypeList(ArrayUtils.toStringList(sectionTypeStr));
        }
    }
}

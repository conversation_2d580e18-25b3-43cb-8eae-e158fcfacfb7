package com.raycloud.dmj.web.controller.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wms.RecommendGoodsSectionBusiness;
import com.raycloud.dmj.business.wms.WmsHelpBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.AllocateInReceipt;
import com.raycloud.dmj.domain.wms.AllocateInReceiptDetail;
import com.raycloud.dmj.domain.wms.AllocateShelveReceipt;
import com.raycloud.dmj.domain.wms.AllocateShelveReceiptBatch;
import com.raycloud.dmj.domain.wms.AllocateShelveReceiptDetail;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.result.BatchExecuteResult;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IAllocateInShelveService;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.idempotent.IdempotentCache;
import com.raycloud.dmj.web.model.wms.AllocateQueryParams;
import com.raycloud.dmj.web.utils.Md5Encrypt;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调拨上架
 * <AUTHOR>
 * @date 2018/8/21 13:45
 */
@RequestMapping("/wms/allocate/shelve")
@Controller
public class AllocateShelveController extends WmsInitController {
    
    private static final Logger LOGGER = Logger.getLogger(AllocateShelveController.class);

    @Resource
    private IAllocateInShelveService allocateInService;

    @Resource
    private RecommendGoodsSectionBusiness recommendGoodsSectionBusiness;

    @Resource
    private WmsHelpBusiness wmsHelpBusiness;
    
    @Resource
    private IdempotentCache dempotentCache;

    /**
     * 调拨上架单列表
     * @param params
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(AllocateQueryParams params, Page page, String api_name) throws Exception {
        Assert.hasText(params.getStatus(), "请选择状态！");
        return allocateInService.queryShelvePageList(getLightStaff(), params, page);
    }

    /**
     * 根据调拨上架id查询上架单据
     * @param id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/detail")
    @ResponseBody
    public Object queryInReceiptsByTaskId(Long id, String api_name) throws Exception {
        Assert.notNull(id, "请选择调拨上架单！");
        return allocateInService.queryShelveById(getLightStaff(), id);
    }

    /**
     * 根据上架单id查询关联的收货单信息
     * @param id
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/in/receipts")
    @ResponseBody
    public Object queryInReceiptsByShelveId(Long id, String api_name) throws Exception {
        Assert.notNull(id, "请选择调拨上架单！");

        return allocateInService.queryInReceiptsByShelveId(getLightStaff(), id);
    }

    /**
     * 根据调拨任务id查询调拨上架单据
     * @param taskId
     * @param withDetail
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/task/details")
    @ResponseBody
    public Object queryInReceiptsByTaskId(Long taskId, Boolean withDetail, String api_name) throws Exception {
        Assert.notNull(taskId, "请选择调拨单！");
        return allocateInService.queryShelveByTaskId(getLightStaff(), taskId, withDetail);
    }

    /**
     * 调拨上架单收货
     * @param receipt
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    @ResponseBody
    public Object receive(@RequestBody AllocateShelveReceipt receipt, String api_name) throws Exception {
        Assert.notNull(receipt.getId(), "请选择调拨入库单！");
        Assert.notNull(receipt.getCode(), "请选择调拨入库单！");
        Assert.notEmpty(receipt.getDetails(), "收获明细不能为空！");
        AllocateShelveReceipt shelve = null;
        Staff staff = getLightStaff();
        int expireTime = 10 * 60;
        String cacheKey = "allocate_shelve" + "_" + staff.getCompanyId() + "_" + receipt.getId() + "_" + receipt.getPageId();
        LOGGER.debug(LogHelper.buildLog(staff, String.format("调拨上架,上架单code %s,cacheKey：%s", receipt.getCode(), cacheKey)));
        if (receipt.getPageId() != null) {
            dempotentCache.checkTokenPreEnd(cacheKey,null, expireTime);
            try {
                shelve = allocateInService.shelve(getLightStaff(), receipt);
                dempotentCache.tryPreEnd(cacheKey,null, expireTime);
            } catch (Exception e) {
                dempotentCache.catchPreEnd(cacheKey,null, expireTime);
                throw e;
            }
        } else {
            shelve = allocateInService.shelve(getLightStaff(), receipt);
        }
        return shelve;
    }

    /**
     * 批量获取上架单和明细
     * @param ids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping("/details")
    @ResponseBody
    public Object queryDetails(String ids, String api_name) throws Exception {
        Assert.hasText(ids, "请选择上架单！");

        final List<Long> idList = ArrayUtils.toLongList(ids);
        List<AllocateShelveReceipt> receipts = allocateInService.queryShelveByIds(getLightStaff(), idList, true);
        Assert.isTrue(idList.size() == receipts.size(), "数据个数不一致！");
        Collections.sort(receipts, new Comparator<AllocateShelveReceipt>() {
            @Override
            public int compare(AllocateShelveReceipt pre, AllocateShelveReceipt next) {
                return idList.indexOf(pre.getId()) - idList.indexOf(next.getId());
            }
        });
        return receipts;
    }


    /**
     * 批量上架
     * @param receipts
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batch/execute", method = RequestMethod.POST)
    @ResponseBody
    public Object batchShelve(@RequestBody AllocateShelveReceiptBatch receiptBatch, String api_name) throws Exception {
        Assert.notEmpty(receiptBatch.getReceipts(), "请选择上架单！");
        
        List<BatchExecuteResult<AllocateShelveReceipt>> results = null;
        Staff staff = getLightStaff();
        int expireTime = 10 * 60;
        String codeStr = StringUtils.join(receiptBatch.getReceipts().stream().map(AllocateShelveReceipt :: getCode).collect(Collectors.toList()), "_");
        String cacheKey = "allocate_shelve" + "_" + staff.getCompanyId() + "_" + Md5Encrypt.md5(codeStr) + "_" + receiptBatch.getPageId();
        LOGGER.debug(LogHelper.buildLog(staff, String.format("调拨上架,上架单code %s,cacheKey：%s", codeStr, cacheKey)));
        if (receiptBatch.getPageId() != null) {
            dempotentCache.checkTokenPreEnd(cacheKey,null, expireTime);
            try {
                results = allocateInService.batchShelve(getLightStaff(), receiptBatch.getReceipts());
                handleBatchResults(results);
                dempotentCache.tryPreEnd(cacheKey,null, expireTime);
            } catch (Exception e) {
                dempotentCache.catchPreEnd(cacheKey,null, expireTime);
                throw e;
            }
        } else {
            results = allocateInService.batchShelve(getLightStaff(), receiptBatch.getReceipts());
            handleBatchResults(results);
        }
        
        return receiptBatch.getReceipts();
    }


    /**
     * 推荐货位
     * @param receipts
     * @return
     */
    @RequestMapping("/recommend")
    @ResponseBody
    public Object recommendGoodsSection(@RequestBody AllocateShelveReceipt[] receipts) throws Exception {
        Assert.notEmpty(receipts, "请选择调拨上架单！");

        List<AssoGoodsSectionSku> recommends = Lists.newArrayListWithCapacity(receipts.length);
        List<AllocateShelveReceiptDetail> details = Lists.newArrayList();
        Long warehouseId = null;
        for (AllocateShelveReceipt receipt : receipts) {
            Assert.notNull(receipt.getInWarehouseId(), "仓库不能为空！");
            if (warehouseId == null) {
                warehouseId = receipt.getInWarehouseId();
            }
            Assert.notEmpty(receipt.getDetails(), "调拨上架单明细不能为空！");
            Assert.isTrue(warehouseId.equals(receipt.getInWarehouseId()), "请选择相同仓库的调拨上架单！");
            for (AllocateShelveReceiptDetail detail : receipt.getDetails()) {
                AssoGoodsSectionSku recommend = new AssoGoodsSectionSku();
                recommend.setSysItemId(detail.getSysItemId());
                recommend.setSysSkuId(detail.getSysSkuId());
                recommend.setQualityType(detail.getQualityType());
                recommend.setWarehouseId(receipt.getInWarehouseId());
                recommend.setBatchNo(detail.getBatchNo());
                recommend.setProductTime(detail.getProductTime());
                recommends.add(recommend);
            }
            details.addAll(receipt.getDetails());
        }

        Staff staff = getLightStaff();
        recommends = recommendGoodsSectionBusiness.recommendGoodsSections(staff, warehouseId, recommends);
        if (CollectionUtils.isNotEmpty(recommends)) {
            wmsHelpBusiness.formatAssoGoodsSectionSku(staff, recommends);
        }
        Assert.isTrue(recommends.size() == details.size(), "推荐结果不满足，请检查货位推荐配置");
        int index = 0;
        for (AllocateShelveReceiptDetail detail : details) {
            AssoGoodsSectionSku matched = recommends.get(index++);
            detail.setGoodsSectionId(matched.getGoodsSectionId());
            detail.setGoodsSectionCode(matched.getGoodsSectionCode());
        }
        return receipts;
    }
}

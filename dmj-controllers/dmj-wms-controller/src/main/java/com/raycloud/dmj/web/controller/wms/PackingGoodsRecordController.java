package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessCondition;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.annotation.AccessShields;
import com.raycloud.dmj.domain.wms.params.PackingGoodsRecordParams;
import com.raycloud.dmj.domain.wms.vo.PackingGoodsRecordVo;
import com.raycloud.dmj.services.wms.IPackingGoodsRecordService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 装袋记录表
 *
 * <AUTHOR>
 * @since 2022-06-22 17:49:12
 */
@RestController
@RequestMapping("/wms/packingGoodsRecord")
public class PackingGoodsRecordController extends WmsInitController {
    /**
     * 服务对象
     */
    @Resource
    private IPackingGoodsRecordService packingGoodsRecordService;

    /**
     * 查询分区快递
     *
     * @param params
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryPackingGoodsRecord(PackingGoodsRecordParams params, String api_name) throws SessionException {
        return packingGoodsRecordService.queryPageList(getLightStaff(), params);
    }

    /**
     * @param checkParams
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkPackingGoodItem", method = RequestMethod.GET)
    @ResponseBody
    @AccessShields(shield = {
            @AccessShield(value = "103901", condition = @AccessCondition(field = "type", expected = {"2"})),
            @AccessShield(value = "103902", condition = @AccessCondition(field = "type", expected = {"1"})),
            @AccessShield(value = "103903", condition = @AccessCondition(field = "type", expected = {"0"})),
    })
    public Object checkPackingGoodItem(PackingGoodsRecordParams checkParams, String api_name) throws Exception {
        Staff staff = getLightStaff();
        Assert.notNull(checkParams, "checkParams is null ！");
        Assert.notNull(checkParams.getType(), "请传入唯一码类型");
        return packingGoodsRecordService.checkPackingGoodItem(staff, checkParams);
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public Object createPackingGoodsRecord(@RequestBody PackingGoodsRecordVo vo, String api_name) throws Exception {
        Assert.notEmpty(vo.getPackingGoodsRecords(), "record not null!");
        Staff staff = getLightStaff();
        packingGoodsRecordService.createPackingGoodsRecords(staff, vo);
        return successResponse();
    }
}
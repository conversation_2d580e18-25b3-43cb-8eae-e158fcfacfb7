package com.raycloud.dmj.web.controller.wms;

import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.wms.params.FastInOutLogQueryParams;
import com.raycloud.dmj.services.wms.IFastInOutStockLogService;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 快销库存日志控制器
 * @author: chunri
 * @create: 2022-01-11 17:27
 **/
@RequestMapping("/wms/fast/in/out/log")
@RestController
@LogTag(value = "wms")
public class FastInOutStockLogController extends WmsInitController {

    @InitBinder
    public void initBinder(ServletRequestDataBinder bin){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CustomDateEditor cust = new CustomDateEditor(sdf,true);
        bin.registerCustomEditor(Date.class,cust);
    }

    @Resource
    private IFastInOutStockLogService fastInOutStockLogService;

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public Object query(FastInOutLogQueryParams params, String api_name) throws Exception {
        return fastInOutStockLogService.query(params, getLightStaff());
    }
}

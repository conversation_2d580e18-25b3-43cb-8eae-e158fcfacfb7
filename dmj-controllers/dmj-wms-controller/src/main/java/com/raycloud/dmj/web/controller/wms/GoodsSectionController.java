package com.raycloud.dmj.web.controller.wms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.wms.WmsCacheBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.domain.wms.GoodsSectionInOutRecord;
import com.raycloud.dmj.domain.wms.ImportProcess;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.enums.WmsCacheEnum;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IGoodsSectionInOutRecordService;
import com.raycloud.dmj.services.wms.IGoodsSectionService;
import com.raycloud.dmj.services.wms.WmsConfigService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.ExportUtils;
import com.raycloud.dmj.utils.wms.WmsProcessUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.controller.WmsInitController;
import com.raycloud.dmj.web.interceptors.TbTask;
import com.raycloud.dmj.web.model.wms.GoodsSectionInOutRecordVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionSearchParams;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionsBatchAddVo;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import java.util.*;

/**
 * 货位操作控制器
 * Created by guzy on 15/12/16.
 */
@RequestMapping("/wms/goodsSection/")
@Controller
@LogTag(value = "wms")
@AccessShield(value = "1001")
public class GoodsSectionController extends WmsInitController {

    @Resource
    private IGoodsSectionService goodsSectionService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private WmsCacheBusiness wmsCacheBusiness;

    @Resource
    private IGoodsSectionInOutRecordService goodsSectionInOutRecordService;

    @Resource
    private IDownloadCenterService downloadCenterService;

    @Resource
    private IFileUploadService fileUploadService;

    private final static Logger logger = Logger.getLogger(GoodsSectionController.class);

    private final static String GOODS_SECTION_EXPORT = "goods.section.export";

    /**
     * 新增货位
     *
     * @param goodsSection 货位实体
     * @return 新增的货位id
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @ResponseBody
    public Object addGoodsSection(GoodsSection goodsSection, String api_name) throws SessionException {
        Staff staff = getStaff();
        goodsSectionService.addGoodsSection(staff, goodsSection);
        if (DataUtils.checkLongNotEmpty(goodsSection.getId())) {
            writeOpLog(staff, "addGoodsSection", String.format("新建，货位：%s", goodsSection.getCode()), JSONObject.toJSONString(goodsSection));
        }
        return goodsSection.getId();
    }

    /**
     * 批量新增货位
     *
     * @param batchAddVo 货位新增vo
     */
    @RequestMapping(value = "addBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object addGoodsSectionsBatch(GoodsSectionsBatchAddVo batchAddVo, String api_name) throws Exception {
        Staff staff = getStaff();

        String cacheKey = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_GOODS_SECTION_BATCH_ADD);
        ImportProcess preProcess = wmsCacheBusiness.get(cacheKey);

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在进行货位批量添加，请稍候！");
        }
        wmsCacheBusiness.set(cacheKey, WmsProcessUtils.buildBaseProcess(cacheKey, 1));
        try {
            goodsSectionService.addGoodsSectionsBatch(staff, batchAddVo);
            writeOpLog(staff, "addGoodsSectionBatch", String.format("批量添加，货位：%s~%s", batchAddVo.getFirstCode(), batchAddVo.getLastCode()), JSONObject.toJSONString(batchAddVo));
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "批量添加货位失败！"), e);
            throw e;
        } finally {
            wmsCacheBusiness.delete(cacheKey);
        }
        return true;
    }

    /**
     * 释放货位
     *
     * @param id 货位id
     */
    @RequestMapping(value = "release", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSection(@RequestParam("id") Long id, @RequestParam("code") String code, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(id,"请选择货位");
        int rows = goodsSectionService.releaseGoodsSection(staff, id);
        writeOpLog(staff, "releaseGoodsSection", String.format("释放货位，货位:%s", code), "货位id:" + id);
        return rows;
    }

    /**
     * 批量释放货位
     *
     * @param ids 货位ids
     */
    @RequestMapping(value = "releaseBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object releaseGoodsSectionBatch(String ids, String codes, String api_name) throws SessionException, CustomException {
        Assert.hasText(codes, "请选择货位！");
        String[] codeArr = codes.split(",");
        Assert.isTrue(codeArr.length > 0, "请选择货位！");
        Staff staff = getStaff();
        Long[] idArr = ArrayUtils.toLongArray(ids);
        Assert.isTrue(idArr.length > 0, "请选择货位！");
        int rows = goodsSectionService.releaseGoodsSection(staff, Arrays.asList(idArr));

        Arrays.sort(codeArr, new Comparator<String>() {
            @Override
            public int compare(String s, String t) {
                return s.compareTo(t);
            }
        });
        String first = codeArr[0];
        String last = codeArr[codeArr.length - 1];
        if (codeArr.length == 1) {
            writeOpLog(staff, "releaseGoodsSectionBatch", String.format("释放货位，货位：%s", first), String.format("id列表:%s", ids));
        } else if (codeArr.length <= 20) {
            writeOpLog(staff, "releaseGoodsSectionBatch", String.format("批量释放，货位：%s", codes), String.format("id列表:%s", ids));
        } else {
            writeOpLog(staff, "releaseGoodsSectionBatch", String.format("批量释放，货位：%s~%s", first, last), String.format("id列表:%s", ids));
        }
        return rows;
    }

    /**
     * 删除货位
     *
     * @param id 货位id
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteGoodsSection(@RequestParam("id") Long id, String api_name) throws SessionException {
        Staff staff = getStaff();
        GoodsSection goodsSection = goodsSectionService.deleteGoodsSection(staff, id);
        writeOpLog(staff, "deleteGoodsSection", String.format("删除货位，货位:%s", goodsSection.getCode()), String.format("货位id:%d", id));
        return true;
    }

    /**
     * 批量修改货位库区类型
     * @param gsIds
     * @param warehouseId
     * @param stockRegionType
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "modify/goodsSection/type", method = RequestMethod.POST)
    @ResponseBody
    public Object modifyGsStockRegionType(String gsIds, Integer stockRegionType, Integer ignorePick ,Long warehouseId,Long stockRegionZoneId,
                                          Integer canAllocate, Boolean isMulti, Boolean sameProductMulti, Integer maxMultiProduct, Integer capacity, Integer batchMulti, Integer productTimeMulti, String api_name) throws SessionException {
        Assert.hasText(gsIds, "请选择货位！");
        Assert.notNull(warehouseId, "请选择仓库！");
        Staff staff = getStaff();
        return goodsSectionService.batchModifyGsStockRegionType(staff, warehouseId, DataUtils.ids2List(gsIds), stockRegionType,stockRegionZoneId,ignorePick,isMulti,sameProductMulti,maxMultiProduct, canAllocate,capacity, batchMulti, productTimeMulti);
    }

    /**
     * 批量修改货位启禁状态
     * @param gsIds
     * @param activeStatus
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "activeStatus/change", method = RequestMethod.POST)
    @ResponseBody
    public Object modifyGsActiveStatus(String gsIds, Long warehouseId, Integer activeStatus, String api_name) throws SessionException {
        Assert.hasText(gsIds, "请选择货位！");
        Assert.notNull(warehouseId, "请选择仓库！");
        Assert.notNull(activeStatus, "请选择启禁状态！");
        Staff staff = getLightStaff();

        return goodsSectionService.batchModifyGsActiveStatus(staff, warehouseId, DataUtils.ids2List(gsIds), activeStatus);
    }

    /**
     * 批量删除货位
     *
     * @param ids 货位ids
     */
    @RequestMapping(value = "deleteBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteGoodsSectionBatch(String ids, String codes, String api_name) throws SessionException, CustomException {
        if (StringUtils.isBlank(codes)) {
            return false;
        }
        if (StringUtils.isBlank(ids)) {
            return false;
        }
        String[] codeArr = codes.split(",");
        if (codeArr.length == 0) {
            return false;
        }
        Staff staff = getStaff();
        Arrays.sort(codeArr, new Comparator<String>() {
            @Override
            public int compare(String s, String t) {
                return s.compareTo(t);
            }
        });
        String first = codeArr[0];
        String last = codeArr[codeArr.length - 1];

        goodsSectionService.deleteGoodsSectionBatch(staff, ArrayUtils.toLongUniqList(ids));
        writeOpLog(staff, "releaseGoodsSectionBatch", String.format("批量删除，货位：%s~%s", first, last), String.format("id列表:%s", ids));
        return true;
    }

    /**
     * 分业查询货位列表
     *
     * @param warehouseId   仓库id
     * @param stockRegionId 库区id
     * @param stockRegionType 库区类型
     * @param code          货位编码
     * @param page          分页参数
     */
    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @TbTask(taskId = "KMERP-92392")
    public Object queryGoodsSectionPageList(Long warehouseId, Long stockRegionId, String code, String isUsed, Integer batchMulti, Integer productTimeMulti,
                                            Integer ignorePick, Integer stockRegionType, String op, Page page, String stockRegionZoneIds, Integer activeStatus, Integer canAllocate, String api_name) throws SessionException {
        Staff staff = getLightStaff();
        PageListBase<GoodsSectionVo> pageList = goodsSectionService.queryGoodsSectionPageList(staff, warehouseId, stockRegionId, stockRegionType, code, batchMulti, productTimeMulti,isUsed, op, ignorePick, page,stockRegionZoneIds, activeStatus, canAllocate);
        formatGoodsSectionCode(staff, pageList.getList());
        pageList.setPage(page);
        return pageList;
    }

    /**
     * 货位出入库记录
     */
    @RequestMapping(value = "inOutRecord/list", method = RequestMethod.GET)
    @ResponseBody
    public Object list(GoodsSectionInOutRecord record, Page page, String timeOrderedStr, String api_name) throws Exception {
        Staff staff = getStaff();
        PageListBase<GoodsSectionInOutRecordVo> pageList = goodsSectionInOutRecordService.queryList(staff, record, timeOrderedStr, page);
        formatGoodsSectionCode(staff, pageList.getList());
        return pageList;
    }

    /**
     * 导出货位出入库明细Excel
     *
     * @throws Exception
     */
    @RequestMapping(value = "inOutRecord/export", method = RequestMethod.GET)
    @ResponseBody
    public Object exportStockInOutRecordExcelData(GoodsSectionInOutRecord record, Page page, String timeOrderedStr, String api_name) throws Exception {
        Staff staff = getStaff();

        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.WMS.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }

        String[][] excelHeader = goodsSectionInOutRecordService.getRecordExcelTitle();
        //导出Excel
        String fileName = new String("货位出入明细记录".getBytes(), "utf-8");
        fileName += org.apache.commons.lang.time.DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("出入库记录");
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo("goods.section.inout.download.excel").setArgs(new Object[]{staff, param, record, timeOrderedStr}), false);

        Map<String, Object> map = Maps.newHashMap();
        map.put("result", "success");
        return map;
    }


    /**
     * 货位excel导入
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#file.originalFilename", content = "'货位导入:' + #file.originalFilename")
    public Object importGoodsSections(MultipartFile file, String api_name) throws Exception {
        checkBeforeImport(file, -1);

        String[][] data = convertExcelData(file);
        Staff staff = getStaff();

        String importToken = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_GOODS_SECTION_CODE_IMPORT);
        ImportProcess preProcess = wmsCacheBusiness.get(importToken);

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在货位导入，请稍候！");
        }

        wmsCacheBusiness.set(importToken, WmsProcessUtils.buildBaseProcess(importToken, data.length));
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.goods.section.import").setArgs(new Object[]{staff}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importToken);
        return result;
    }

    /**
     * 货位excel导入进度
     */
    @RequestMapping(value = "/import/status")
    @ResponseBody
    public Object getImportStatus(String importToken, String api_name) throws Exception {
        ImportProcess process = wmsCacheBusiness.get(importToken);
        Assert.notNull(process, "请先导入货位编码！");
        if (process.isComplete()) {
            wmsCacheBusiness.delete(process.getCacheKey());
            int errorNum = process.getErrorNum();
            writeOpLog(getLightStaff(), Domain.WMS, "goodsSectionExcelImportResult", importToken, String.format("货位导入，结果：%s，总条数：%s，失败：%s", (errorNum > 0 ? "失败" : "成功"), process.getTotalNum(), errorNum), null, errorNum > 0 ? 1 : 0);
        }
        return process;
    }


    /**
     * 货位启禁用状态导入
     */
    @RequestMapping(value = "/activeStatus/import", method = RequestMethod.POST)
    @ResponseBody
    @LogTag(key = "#file.originalFilename", content = "'货位启禁用状态导入:' + #file.originalFilename")
    public Object importActiveStatus(MultipartFile file, String api_name) throws Exception {
        checkBeforeImport(file, -1);

        String[][] data = convertExcelData(file);
        Staff staff = getStaff();

        String importToken = wmsCacheBusiness.getKey(staff, WmsCacheEnum.CACHE_EXCEL_IMPORT_GOODS_SECTION_ACTIVE_IMPORT);
        ImportProcess preProcess = wmsCacheBusiness.get(importToken);

        if (preProcess != null && !preProcess.isComplete()) {
            throw new IllegalArgumentException("正在货位启禁用导入，请稍候！");
        }

        wmsCacheBusiness.set(importToken, WmsProcessUtils.buildBaseProcess(importToken, data.length));
        String filename = file.getOriginalFilename();
        FileResult fileResult = fileUploadService.upload(filename, data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("wms.goods.section.active.import").setArgs(new Object[]{staff}), fileResult.getUrl());
        Map<String, Object> result = new HashMap<>();
        result.put("importToken", importToken);
        return result;
    }

    /**
     * 货位excel导出
     */
    @RequestMapping("/export")
    @ResponseBody
    public Object export (GoodsSectionSearchParams searchParams, String api_name) throws Exception{
        Staff staff = getStaff();
        Assert.isTrue(WmsUtils.isOpenGoodsSectionExport(staff),"没有开启库区货位");

        FileDownloadParam downloadParam = new FileDownloadParam();
        downloadParam.setTitleArr(new String[][]{ExportUtils.GOODS_SECTION_EXCEL_HEADER});
        downloadParam.setModule(EnumDownloadCenterModule.WMS.getCode());
        eventCenter.fireEvent(this, new EventInfo(GOODS_SECTION_EXPORT).setArgs(new Object[]{staff, downloadParam, searchParams}), false);
        Map<String, Object> map = Maps.newHashMap();
        map.put("result", "success");
        return map;
    }
}

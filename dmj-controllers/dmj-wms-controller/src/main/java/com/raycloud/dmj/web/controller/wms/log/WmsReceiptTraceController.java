package com.raycloud.dmj.web.controller.wms.log;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.services.domain.ReceiptTrace;
import com.raycloud.dmj.services.wms.IWmsReceiptTraceService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controller.WmsInitController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lit
 * @Date: 2021/5/10 6:14 下午
 */

@Controller
@RequestMapping("/wms/receiptTrace/")
public class WmsReceiptTraceController extends WmsInitController {

    @Autowired
    private IWmsReceiptTraceService wmsReceiptTraceService;

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Object queryByReceipt(@RequestParam(value = "receipts") String receipts, @RequestParam(value = "receiptType") Integer receiptType) throws SessionException {
        Staff staff = getStaff();
        List<Long> receiptIds = Arrays.stream(receipts.split(","))
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        return wmsReceiptTraceService.queryByReceipt(staff, receiptIds, receiptType);
    }
}
